package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.PathKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.template.Engine;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.model.StaffRowMapper;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;

public class StaffService extends AppBaseService {
	
	public static List<String> normalUserIds = new ArrayList<String>();
	public static List<String> normalNameIds = new ArrayList<String>();
	
	public static Map<String,StaffModel> staffModels = new ConcurrentHashMap<String, StaffModel>();
	public static Map<String,String> userNames = new HashMap<String, String>();
	public static Map<String,String> nameIds = new HashMap<String, String>();
	public static Map<String,String> userStaffNos = new HashMap<String, String>();
	public static Map<String,String> deptLeader = new HashMap<String, String>();
	public static Map<String,String> deptSecondLeader = new HashMap<String, String>();
	public static Map<String,String> userDeptNames = new HashMap<String, String>();
	public static Map<String,String> userEmail = new HashMap<String, String>();
	
	private static class Holder{
		private static StaffService service=new StaffService();
	}
	public static StaffService getService(){
		return Holder.service;
	}
	
	public void initData() {
		try {
			List<JSONObject> list = this.getMainQuery().queryForList("select t1.USER_ID,t1.USERNAME,t2.STAFF_NO,t1.DEPTS,t1.EMAIL,t1.STATE from easi_user t1 INNER JOIN yq_staff_info t2 on t1.USER_ID = t2.staff_user_id",new Object[]{},new JSONMapperImpl());
			for(JSONObject row :list) {
				userNames.put(row.getString("USER_ID"), row.getString("USERNAME"));
				userStaffNos.put(row.getString("USER_ID"), row.getString("STAFF_NO"));
				nameIds.put(row.getString("USERNAME"), row.getString("USER_ID"));
				userDeptNames.put(row.getString("USER_ID"), row.getString("DEPTS"));
				userEmail.put(row.getString("USER_ID"), row.getString("EMAIL"));
				
				int state = row.getIntValue("STATE");
				if(state==0) {
					normalUserIds.add(row.getString("USER_ID"));
					normalNameIds.add(row.getString("USERNAME"));
				}
				
			}
			List<JSONObject> depts = this.getMainQuery().queryForList("select t1.DEPT_ID,t1.LEADER,t1.SECOND_LEADER from easi_dept t1",new Object[]{},new JSONMapperImpl());
			for(JSONObject row :depts) {
				deptLeader.put(row.getString("DEPT_ID"), row.getString("LEADER"));
				deptSecondLeader.put(row.getString("DEPT_ID"), row.getString("SECOND_LEADER"));
			}
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	
	public StaffService() {
		initData();
	}
	
	public StaffModel getStaffInfo(String userId) {
		return getStaffInfo(userId,false);
	}
	
	public StaffModel getStaffInfo(String userId,boolean isReload) {
		try {
			if(StringUtils.isBlank(userId)||"null".equals(userId)) {
				this.getLogger().error("getStaffInfo>userId>is Empty.");
				return new StaffModel();
			}
			if(userId.length()<5) {
				userId = getUserId(userId);
			}
			if(userId.indexOf(",")>-1) {
				userId = userId.split(",")[0];
			}
			if(isReload) {
				staffModels.remove("userInfo_"+userId);
			}
			StaffModel _staffModel = staffModels.get("userInfo_"+userId);
			if(_staffModel!=null) {
				return _staffModel;
			}
			StaffModel staffModel = this.getMainQuery().queryForRow("select t1.STATE,t1.ROOT_ID,t1.USER_ID,t1.DEPTS,t1.USERNAME,t1.NIKE_NAME,t1.OPEN_ID,t1.SEX,t1.EMAIL,t1.MOBILE,t1.PIC_URL,t2.SUPERIOR,t2.P_SUPERIOR,t2.STAFF_NO,t2.POINTS,t2.`LEVEL`,t2.JOB_TITLE,t3.DEPT_ID,t2.JOIN_DATE,t2.OFTEN_WORK_CITY from easi_user t1 LEFT JOIN yq_staff_info t2 on t1.USER_ID = t2.staff_user_id LEFT JOIN easi_dept_user t3 on t3.USER_ID = t1.USER_ID where t1.USER_ID = ?",new Object[]{userId},new StaffRowMapper());
			if(staffModel==null) {
				return new StaffModel();
			}
			staffModel.setDeptLeader(getLeader(staffModel.getDeptId()));
			staffModel.setDeptLeaderName(StaffService.getService().getUserName(staffModel.getDeptLeader()));
			staffModels.put("userInfo_"+userId,staffModel);
			return staffModel;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new StaffModel();
		}
	}
	
	public String getDeptSecondLeader(String deptId) {
		String leader = deptSecondLeader.get(deptId);
		if(leader==null) {
			try {
				leader =  this.getMainQuery().queryForString("select t1.SECOND_LEADER from easi_dept t1 where t1.dept_id = ?",new Object[]{deptId});
				deptSecondLeader.put(deptId, leader);
			} catch (SQLException e) {
				this.getLogger().error(null,e);
			}
		}
		return leader;
	}
	
	public String getLeader(String deptId) {
		String leader = deptLeader.get(deptId);
		if(leader==null) {
			try {
				leader =  this.getMainQuery().queryForString("select t1.LEADER from easi_dept t1 where t1.dept_id = ?",new Object[]{deptId});
				deptLeader.put(deptId, leader);
			} catch (SQLException e) {
				this.getLogger().error(null,e);
			}
		}
		return leader;
	}
	
	public String getUserDeptName(String userId) {
		String deptName = userDeptNames.get(userId);
		if(deptName==null) {
			try {
				deptName =  this.getMainQuery().queryForString("select t1.depts from easi_user t1 where t1.user_id = ?",new Object[]{userId});
				userDeptNames.put(userId, deptName);
			} catch (SQLException e) {
				this.getLogger().error(null,e);
			}
		}
		return deptName;
	}
	
	public String getUserEmail(String userId) {
		String email = userEmail.get(userId);
		if(email==null) {
			try {
				email =  this.getMainQuery().queryForString("select t1.email from easi_user t1 where t1.user_id = ?",new Object[]{userId});
				if(StringUtils.notBlank(email)) {
					email = StringUtils.trim(email);
				}
				userEmail.put(userId, email);
			} catch (SQLException e) {
				this.getLogger().error(null,e);
			}
		}
		return email;
	}
	
	public String autoGetUserId(String str) {
		if(StringUtils.notBlank(str)) {
			if(str.length()>10) {
				return str;
			}
			return getUserId(str);
		}
		return "";
	}
	
	public String autoGetUserName(String str) {
		if(StringUtils.notBlank(str)) {
			if(str.length()>10) {
				return getUserName(str);
			}
			return str;
		}
		return "";
	}
	
	public String getUserId(String userName) {
		String[] userNameArray = userName.split(",");
		StringBuffer strs = new StringBuffer();
		if(userName.indexOf(",")>-1) {
			for(String str:userNameArray) {
				strs.append(queryUserId(str)).append(",");
			}
		}else {
			return queryUserId(userName);
		}
		String result = strs.toString();
		result = result.substring(0,result.length()-1);
		return result;

	}
	
	public String getStaffNo(String userIds) {
		StringBuffer sb = new StringBuffer();
		if(StringUtils.notBlank(userIds)) {
			String[] userIdArray = userIds.split(",");
			for(String userId:userIdArray) {
				String staffNo = userId;
				if(userId.length()>10) {
					staffNo = userStaffNos.get(userId);
					if(staffNo==null) {
						try {
							staffNo =  this.getMainQuery().queryForString("select staff_no from yq_staff_info where staff_user_id = ?", userId);
							userStaffNos.put(userId,staffNo);
						} catch (SQLException e) {
							getLogger().error(null,e);
						}
					}
				}
				sb.append(staffNo+",");
			}
			return sb.deleteCharAt(sb.length() - 1).toString();
		}
		return "";
		
	}
	
	public String getUserMgrDeptIds(String userId) {
		StringBuffer sb = new StringBuffer("");
		try {
			List<JSONObject>  list = this.getMainQuery().queryForList("select * from easi_user_dept where USER_ID = ?", new String[] {userId},new JSONMapperImpl());
			if(list!=null) {
				for(JSONObject row:list) {
					sb.append(row.getString("DEPT_ID"));
					sb.append(",");
				}
				if (sb.length() > 0) {
		           sb.deleteCharAt(sb.length() - 1);
		        }
			}
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
		return sb.toString();
	}
	
	public String getUserName(String userId) {
		if(StringUtils.isBlank(userId)) {
			this.getLogger().error("getUserName is empty");
			return "null";
		}
		String[] userIdArray = userId.split(",");
		StringBuffer strs = new StringBuffer();
		if(userId.indexOf(",")>-1) {
			for(String str:userIdArray) {
				if(StringUtils.notBlank(str)) {
					strs.append(queryUserName(str)).append(",");
				}
			}
		}else {
			return queryUserName(userId);
		}
		String result = strs.toString();
		result = result.substring(0,result.length()-1);
		return result;
	}
	
	public String queryUserId(String userName) {
		try {
			String userId = nameIds.get(userName);
			if(userId!=null) {
				return userId;
			}
			userId  = this.getMainQuery().queryForString("select user_id from easi_user where username = ?", userName);
			userNames.put(userId, userName);
			nameIds.put(userName, userId);
			return userId;
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
		return null;
	}
	
	public String getNormalUserId(String userIds) {
		if(StringUtils.notBlank(userIds)) {
			List<String> list = new ArrayList<String>();
			String[] array = userIds.split(",");
			for(String userId:array) {
				if(StringUtils.notBlank(userId)&&normalUserIds.contains(userId)) {
					list.add(userId);
				}
			}
			return String.join(",", list);
		}
		return userIds;
	}
	
	public String getNormalUserName(String userNames) {
		if(StringUtils.notBlank(userNames)) {
			List<String> list = new ArrayList<String>();
			String[] array = userNames.split(",");
			for(String userName:array) {
				if(StringUtils.notBlank(userName)&&normalNameIds.contains(userName)) {
					list.add(userName);
				}
			}
			return String.join(",", list);
		}
		return userNames;
	}
	
	public String queryUserName(String userId) {
		try {
			String userName = userNames.get(userId);
			if(userName!=null) {
				return userName;
			}
			userName  = this.getMainQuery().queryForString("select username from easi_user where user_id = ?", userId);
			userNames.put(userId, userName);
			nameIds.put(userName, userId);
			return userName;
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
		return "";
	}
	
	public void sendJoinYearEmail() {
		try {
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT LEFT(t1.join_date,4) year,t2.USER_ID,t2.EMAIL,t1.join_date,t2.USERNAME,t2.OPEN_ID from yq_staff_info t1,easi_user t2 where t1.staff_user_id = t2.USER_ID and t1.account_state=0  and RIGHT(t1.join_date,5) = ?", new Object[] {EasyDate.getCurrentDateString("MM-dd")}, new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject obj:list) {
					int year = obj.getIntValue("YEAR");
					int nowYear = EasyCalendar.newInstance().getYear();
					String x = String.valueOf(nowYear - year);
					String userId = obj.getString("USER_ID");
					String username = obj.getString("USERNAME");
					
					Map<String,String> map=new HashMap<String, String>();
					MessageModel model = new MessageModel();
					model.setSender("7b4dc6a807214e58afa51cca9dc83e76");//小翠id
					model.setTplName("joinYearNotice.html");
					model.setReceiver(userId);
					model.setTitle("今天是你入职"+x+"周年纪念日");
					map.put("title", model.getTitle());
					map.put("username",username);
					map.put("year",x);
					map.put("date",EasyDate.getCurrentDateString("yyyy年MM月dd日"));
					
					model.setTitle(model.getTitle());
					String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(map);
					model.setDesc(bodyHtml);
					
					WeChatWebhookSender.sendMarkdownMessage(WebhookKey.HR, model.getTitle(), username);
					
					EmailService.getService().sendEmail(model);
					
					this.getLogger().info(username+","+model.getTitle());
				}
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
	}
	public void sendBirthdayEmail() {
		try {
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT t2.USER_ID,t2.EMAIL,t2.BORN,t2.USERNAME,t2.OPEN_ID from easi_user t2 where  t2.state = 0  and RIGHT(t2.BORN,5) = ?", new Object[] {EasyDate.getCurrentDateString("MM-dd")}, new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject obj:list) {
					String userId = obj.getString("USER_ID");
					String username = obj.getString("USERNAME");
					
					Map<String,String> map=new HashMap<String, String>();
					MessageModel model = new MessageModel();
					model.setSender("7b4dc6a807214e58afa51cca9dc83e76");//小翠id
					model.setTplName("birthdayNotice.html");
					model.setReceiver(userId);
					model.setTitle("生日快乐");
					map.put("title", model.getTitle());
					map.put("username",username);
					map.put("date",EasyDate.getCurrentDateString("yyyy年MM月dd日"));
					
					model.setTitle(model.getTitle());
					String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(map);
					model.setDesc(bodyHtml);
					
					WeChatWebhookSender.sendMarkdownMessage(WebhookKey.HR, model.getTitle(), username);
					
					EmailService.getService().sendEmail(model);
					
					this.getLogger().info(username+","+model.getTitle());
				}
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
	}
	
	
	public void clearCache() {
		userNames.clear();
		nameIds.clear();
		userStaffNos.clear();
		deptLeader.clear();
		userDeptNames.clear();
		deptSecondLeader.clear();
		userEmail.clear();
		staffModels.clear();
		initData();
		
	}
}
