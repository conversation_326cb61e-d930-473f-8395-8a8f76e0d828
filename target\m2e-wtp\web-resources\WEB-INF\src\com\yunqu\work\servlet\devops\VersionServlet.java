package com.yunqu.work.servlet.devops;

import java.io.File;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.VersionModel;
import com.yunqu.work.service.CommonService;
import com.yunqu.work.service.EmailService;
/**
 * 版本管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/version/*")
public class VersionServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		JSONObject params = getJSONObject();
		int state = params.getIntValue("state");
		VersionModel model=getModel(VersionModel.class, "version");
		model.addCreateTime();
		model.set("dept_id", getDeptId());
		model.set("create_name",getUserName());
		model.setCreator(getUserPrincipal().getUserId());
		try {
//			String appId=model.getString("APP_ID");
//			AppModel appModel=new AppModel();
//			appModel.setPrimaryValues(appId);
//			appModel.set("last_version_name",model.getString("VERSION_NAME"));
//			appModel.set("last_version_time", EasyDate.getCurrentDateString());
//			appModel.set("last_publisher", getUserPrincipal().getUserId());
//			appModel.update();
			if(state==1) {
				model.set("version_state", -1);
			}else {
				model.set("version_state", 0);
			}
			model.save();
			
			this.getQuery().executeUpdate("update yq_ops_version t1 set t1.file_desc = (select GROUP_CONCAT(t2.file_name) from yq_files t2 where t2.fk_id = ? and t1.version_id = t2.fk_id)  where t1.version_id = ?",model.getPrimaryValue(),model.getPrimaryValue());
	
			int versionType = model.getIntValue("VERSION_TYPE");
			if(versionType==2) {
				this.getQuery().executeUpdate("update yq_ops_service set service_version = ? where service_id = ?",model.getString("VERSION_NAME"),model.getString("PROJECT_ID"));
			}
			if(state==2) {
				this.sendMsg(model);
			}
			
			JSONObject module = getJSONObject("module");
			Set<String> moduleIds= module.keySet();
			for(String moduleId:moduleIds) {
				String versionName = module.getString(moduleId);
				this.getQuery().executeUpdate("update yq_project_module set last_version = ?,update_by = ?,update_by_name = ?,update_time = ? where module_id = ?", versionName,getUserId(),getUserName(),EasyDate.getCurrentDateString(),moduleId);
			
				
				EasyRecord record = new EasyRecord("yq_project_module_version");
				record.put("version_id", model.getPrimaryValue());
				record.put("module_id", moduleId);
				record.put("module_name", params.getString("moduleName_"+moduleId));
				record.put("prev_version", params.getString("prevVersion_"+moduleId));
				record.put("version_desc", params.getString("versionDesc_"+moduleId));
				record.put("now_version", versionName);
				record.put("add_time", EasyDate.getCurrentDateString());
				record.put("add_by", getUserId());
				record.put("add_by_name", getUserName());
				this.getQuery().save(record);
			}
			if(moduleIds.size()>0) {
				this.getQuery().executeUpdate("update yq_ops_version set module_count = ? where version_id = ?", moduleIds.size(),model.getPrimaryValue());
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void sendMsg(VersionModel model) throws SQLException {
		String mailtos = model.getString("RECIPIENT_ID");
		MessageModel messageModel=new MessageModel();
		messageModel.setData(this.getQuery().queryForRow("select * from yq_ops_version where version_id = ?", new Object[] {model.getVersionId()},new JSONMapperImpl()));
		messageModel.setReceiver(model.getString("RECEIVER_ID"));
		if(StringUtils.notBlank(mailtos)){
			mailtos = mailtos +","+getUserId();
			messageModel.setCc(mailtos.split(","));
		}
		messageModel.setFkId(model.getPrimaryValue().toString().toString());
		messageModel.setSender(getUserId());
		messageModel.setSendName(getUserName());
		messageModel.setTplName("newVersion.html");
		messageModel.setTitle(model.getString("PROJECT_NAME")+"-"+model.getString("VERSION_TITLE"));
		messageModel.setFileList(CommonService.getService().getFileList(model.getVersionId()));
		EmailService.getService().sendTaskEmail(messageModel);
	}
	
	public EasyResult actionForUpdateFile(){
		String id = getJsonPara("id");
		try {
			this.getQuery().executeUpdate("update yq_ops_version t1 set t1.file_desc = (select GROUP_CONCAT(t2.file_name) from yq_files t2 where t2.fk_id = ? and t1.version_id = t2.fk_id) where t1.version_id = ?",id,id);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForUpdate(){
		JSONObject params = getJSONObject();
		VersionModel model=getModel(VersionModel.class, "version");
		try {
			model.set("update_time",EasyDate.getCurrentDateString());
			
			int versionState = model.getIntValue("VERSION_STATE");
			int state = params.getIntValue("state");
			if(state==2&&versionState==-1) {
				this.sendMsg(model);
				model.set("VERSION_STATE",0);
			}
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForModules(){
		String moduleIds = getJsonPara("moduleIds");
		EasySQL sql = new EasySQL("select * from yq_project_module where 1=1");
		sql.appendIn(moduleIds.split(","), "and module_id");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			return EasyResult.ok(list);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelete(){
		VersionModel model=getModel(VersionModel.class, "version");
		try {
			model.delete();
			
			//删除附件
			
			String fkId = model.getPrimaryValue().toString();
			try {
				List<JSONObject> list = getQuery().queryForList("select * from yq_files where fk_id = ?", new Object[]{fkId}, new JSONMapperImpl());
				for(JSONObject jsonObject:list) {
					String id=jsonObject.getString("FILE_ID");
					String diskPath=jsonObject.getString("DISK_PATH");
					String basePath = getBaseDir();
					File file =  new File(basePath+diskPath);
					boolean fileNotExist = false;
					if(file==null||!file.exists()){
						fileNotExist = true;
						this.error("文件不存在",null);
					}
					boolean bl = file.delete();
					if(bl||fileNotExist) {
						if(StringUtils.isNotBlank(id)) {
							this.getQuery().executeUpdate("delete from yq_file_download_log where file_id =  ?",id);
							this.getQuery().executeUpdate("delete from yq_look_log where fk_id =  ?",id);
							this.getQuery().executeUpdate("delete from yq_files where file_id =  ?",id);
						}
					}
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}





