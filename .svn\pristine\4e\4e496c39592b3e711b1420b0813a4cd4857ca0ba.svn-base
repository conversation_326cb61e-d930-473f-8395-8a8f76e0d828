package com.yunqu.work.utils.exmail;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Properties;

import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.Constants;
import com.yunqu.work.model.FileModel;
import com.yunqu.work.service.CommonService;
import com.yunqu.work.utils.LogUtils;

public class EmailUtils {
	
	
	private Properties props;// 系统属性
	private Session mailSession;// 邮件会话对象
	private MimeMessage mimeMsg; // MIME邮件对象
	
	class Auth extends Authenticator{
		private String username = "";
		private String password = "";
	 
		public Auth(String username, String password) {
			this.username = username;
			this.password = password;
		}
		public PasswordAuthentication getPasswordAuthentication() {
			return new PasswordAuthentication(username, password);

	}
  }
 
	public EmailUtils(String smtpHost, String Port, String MailUsername, String MailPassword) {
		Auth au = new Auth(MailUsername, MailPassword);
		// 设置系统属性
		props = java.lang.System.getProperties(); // 获得系统属性对象
		props.put("mail.smtp.host", smtpHost); // 设置SMTP主机
		props.put("mail.smtp.port", Port); // 设置服务端口号
		props.put("mail.smtp.auth", "true");// 同时通过验证
		// 获得邮件会话对象 
		mailSession = Session.getInstance(props, au);
		mailSession.setDebug(true);
	}
  
	private InternetAddress[]  getAddress(String mailTo) throws AddressException, UnsupportedEncodingException {
		if(mailTo.indexOf("_")>-1) {
			String[] array = mailTo.split(",");
			InternetAddress[] addressList = new InternetAddress[array.length];
			for(int i =0;i<array.length;i++) {
				String str = array[i];
				String[] arr = str.split("_");
				addressList[i] = new InternetAddress(arr[0], arr[1]);
			}
			return addressList;
		}else {
			return InternetAddress.parse(mailTo);
		}
	}
	
	public boolean sendingMimeMail(String[] mailFrom, String MailTo,String mailCopyTo, String mailBCopyTo, String mailSubject,String mailBody,List<FileModel> fileList) {
		try { 
			// 创建MIME邮件对象
			mimeMsg = new MimeMessage(mailSession);
			// 设置发信人
			
			mimeMsg.setFrom(new InternetAddress(mailFrom[0],mailFrom[1],"UTF-8"));
			// 设置收信人
			if (MailTo != null) {
				mimeMsg.setRecipients(Message.RecipientType.TO, getAddress(MailTo));
			} 
			// 设置抄送人
			if (StringUtils.notBlank(mailCopyTo)) {
				mimeMsg.setRecipients(javax.mail.Message.RecipientType.CC,getAddress(mailCopyTo));
			}
			// 设置暗送人
			if (StringUtils.notBlank(mailBCopyTo)) {
				mimeMsg.setRecipients(javax.mail.Message.RecipientType.BCC,InternetAddress.parse(mailBCopyTo));
			}
			// 设置邮件主题 
			mimeMsg.setSubject(mailSubject, "gb2312");
			// 设置邮件内容，将邮件body部分转化为HTML格式
			mimeMsg.setContent(mailBody, "text/html;charset=gb2312");
			//mimeMsg.setDataHandler(new javax.activation.DataHandler(new StringDataSource(MailBody, "text/html")));
			
			if(fileList!=null&&fileList.size()>0) {
				MimeBodyPart  contentPart = new MimeBodyPart();
				contentPart.setContent(mailBody, "text/html;charset=gb2312");
				
				Multipart multipartList = new MimeMultipart();
				multipartList.addBodyPart(contentPart);
				
				for(FileModel model:fileList) {
					long fileLength = model.getFileLenth();
					if(fileLength<=(1024*1024*10)) {
						MimeBodyPart filePart = new MimeBodyPart();
						String diskPath = model.getDiskPath();
						File file = CommonService.getService().getFileByPath(diskPath);
						if(file!=null) {
							filePart.attachFile(file);
							filePart.setFileName(MimeUtility.encodeText(model.getFileName()));
							multipartList.addBodyPart(filePart);
						}
					}
				}
				mimeMsg.setContent(multipartList);
			}
	         
			// 发送邮件
			Transport.send(mimeMsg);
			return true;
		} catch (Exception e) {
			LogUtils.getLogger().error("mailFrom:"+mailFrom[0]+",MailTo:"+MailTo+",mailCopyTo:"+mailCopyTo+",mailSubject:"+mailSubject);
			LogUtils.getLogger().error(e.getMessage(), e);
			return false;
		}
	}
	
	/**
	 * 
	 * @param senderUserName 发送人姓名
	 * @param mailto 收件人
	 * @param cc 抄送人
	 * @param title 主题
	 * @param desc 内容详情
	 * @return
	 */
	public static boolean sendMsgMail(String senderUserName,String mailto,String cc,String title,String desc){
		return sendMsgMail(senderUserName, mailto, cc, title, desc, null);
	}
	
	public static boolean sendMsgMail(String senderUserName,String mailto,String cc,String title,String desc,List<FileModel> fileList){
		String smtpHost ="smtp.exmail.qq.com";
		String Port="25";
		String MailUsername = "<EMAIL>";
		String pwd = AppContext.getContext(Constants.APP_NAME).getProperty("emailPwd", "");
		if(StringUtils.isBlank(pwd)) {
			return false;
		}
		String MailFrom = "<EMAIL>";
		if(mailto == null){
			System.out.println("Servlet parameter Wrongs");
			return false;
		}
		EmailUtils send=new EmailUtils(smtpHost,Port,MailUsername,pwd);
		if(send.sendingMimeMail(new String[]{MailFrom,senderUserName}, mailto, cc, "", title, desc,fileList)){
			return true;
		}else{
			return false;
		}
	}
	public static void main(String[] args) {
		sendMsgMail("陈小丰","<EMAIL>","<EMAIL>","title","<b>已审核<b>");
	}
}
