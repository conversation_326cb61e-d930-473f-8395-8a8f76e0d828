package com.yunqu.work.servlet.platform;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import com.yunqu.work.base.AppBaseServlet;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;

@WebServlet("/servlet/business-platform/*")
public class BusinessPlatformServlet extends AppBaseServlet {

    private static final long serialVersionUID = 5802513884640243775L;

    public EasyResult actionForAddPlatform(){
        JSONObject jsonObject = this.getJSONObject("platform");
        String platformId = RandomKit.uuid();
        Integer idxOrder = jsonObject.getInteger("IDX_ORDER");
        try {
            EasyRecord record = new EasyRecord("YQ_BUSINESS_PLATFORM","PLATFORM_ID");
            record.setColumns(jsonObject);
            record.set("PLATFORM_ID", platformId);
            record.set("CREATE_TIME", EasyDate.getCurrentDateString());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            record.set("IDX_ORDER",idxOrder);
            this.getQuery().save(record);
            return EasyResult.ok(null,"添加平台成功！");
        } catch (SQLException e) {
            this.error("添加平台失败，原因："+e.getMessage(),e);
            return EasyResult.fail("添加平台失败，原因："+e.getMessage());
        }
    }

    public EasyResult actionForDeletePlatform(){
        JSONObject jsonObject = this.getJSONObject();
        String platformId = jsonObject.getString("platformId");
        String platformTypeId = jsonObject.getString("platformTypeId");
        String sql = "DELETE FROM YQ_BUSINESS_PLATFORM WHERE PLATFORM_ID = ? and PLATFORM_TYPE_ID = ?";
        try {
            this.getQuery().execute(sql, new Object[]{platformId,platformTypeId});
            return EasyResult.ok(null, "删除成功！");
        } catch (Exception ex) {
            return EasyResult.fail("删除失败！");
        }
    }

    public EasyResult actionForModPlatform(){
        JSONObject jsonObject = this.getJSONObject("platform");
        String platformId = jsonObject.getString("PLATFORM_ID");
        EasyRecord record = new EasyRecord("YQ_BUSINESS_PLATFORM","PLATFORM_ID").setPrimaryValues(platformId);
        try {
            record.setColumns(jsonObject);
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getDb().update(record);
            return EasyResult.ok(null,"修改平台成功！");
        } catch (SQLException ex) {
            this.error("修改平台失败，原因："+ex.getMessage(),ex);
            return EasyResult.fail("修改平台失败，原因："+ex.getMessage());
        }
    }

    public EasyResult actionForAddPlatformType(){
        JSONObject jsonObject = this.getJSONObject("platformType");
        Integer idxOrder = jsonObject.getIntValue("IDX_ORDER");
        String platformTypeId = RandomKit.uuid();
        try {
            EasyRecord record = new EasyRecord("YQ_BUSINESS_PLATFORM_TYPE","PLATFORM_TYPE_ID");
            record.setColumns(jsonObject);
            record.setPrimaryValues(platformTypeId);
            record.set("IDX_ORDER",idxOrder);
            record.set("CREATE_TIME", EasyDate.getCurrentDateString());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getQuery().save(record);
            return EasyResult.ok(null,"添加平台类型成功！");
        } catch (SQLException e) {
            this.error("添加平台类型失败，原因："+e.getMessage(),e);
            return EasyResult.fail("添加平台类型失败，原因："+e.getMessage());
        }
    }

    public EasyResult actionForModPlatformType() {
        JSONObject jsonObject = this.getJSONObject("platformType");
        String platformTypeId = jsonObject.getString("PLATFORM_TYPE_ID");
        EasyRecord record = new EasyRecord("YQ_BUSINESS_PLATFORM_TYPE","PLATFORM_TYPE_ID").setPrimaryValues(platformTypeId);
        try {
            record.setColumns(jsonObject);
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getDb().update(record);
            return EasyResult.ok(null,"修改平台类型成功！");
        } catch (SQLException ex) {
            return EasyResult.fail("修改平台类型失败，原因："+ex.getMessage());
        }
    }

    public EasyResult actionForDeletePlatformType(){
        JSONObject jsonObject = this.getJSONObject();
        String platformTypeId = jsonObject.getString("platformTypeId");
        try {
            String sql = "DELETE FROM YQ_BUSINESS_PLATFORM WHERE PLATFORM_TYPE_ID=?";
            this.getQuery().executeUpdate(sql, platformTypeId);
            sql = "DELETE FROM YQ_BUSINESS_PLATFORM_TYPE WHERE PLATFORM_TYPE_ID = ?";
            this.getQuery().execute(sql, new Object[]{platformTypeId});
            return EasyResult.ok(null, "删除成功！");
        } catch (Exception ex) {
            this.error("删除平台类型失败，原因："+ex.getMessage(), ex);
            return EasyResult.fail("删除平台类型失败，原因："+ex.getMessage());
        }
    }
}