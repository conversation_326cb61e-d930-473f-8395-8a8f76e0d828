package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;
import java.util.Random;

import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.FlowFormModel;
import com.yunqu.work.model.FlowFormRowMapper;
import com.yunqu.work.model.MessageModel;

public class FlowService extends AppBaseService{

	private static class Holder{
		private static FlowService service=new FlowService();
	}
	public static FlowService getService(){
		return Holder.service;
	}
	
	public FlowFormModel getFlowFormModel(String formId) {
		try {
			return this.getQuery().queryForRow("select * from yq_flow_form where form_id = ?",new Object[] {formId}, new FlowFormRowMapper());
		} catch (SQLException e) {
			this.getLogger().error(null,e);
			return new FlowFormModel();
		}
	}
	
	public void updateApplyInfo(String businessId,String currentResultId,String nextResultId,String nowNodeId,String nextNodeId,Integer applyState) throws SQLException{
		 updateApplyInfo(null , businessId, currentResultId, nextResultId, nowNodeId, nextNodeId, applyState);
	}
	
	public void updateApplyInfo(EasyQuery query,String businessId,String currentResultId,String nextResultId,String nowNodeId,String nextNodeId) throws SQLException{
		updateApplyInfo(query, businessId, currentResultId, nextResultId, nowNodeId, nextNodeId,20);
	}
	
	public void updateApplyInfo(EasyQuery query,String businessId,String currentResultId,String nextResultId,String nowNodeId,String nextNodeId,Integer applyState) throws SQLException{
		EasySQL sql = new EasySQL("update yq_flow_apply set ");
		sql.append(nowNodeId,"current_node_id = ?,");
		sql.append(nextNodeId,"next_node_id = ?,");
		sql.append(currentResultId,"current_result_id = ?,");
		sql.append(nextResultId,"next_result_id = ?,");
		sql.append(applyState,"apply_state = ?,");
		sql.append(EasyDate.getCurrentDateString(),"last_result_time = ?");
		sql.append(businessId,"where apply_id = ?");
		if(query==null) {
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		}else {
			query.executeUpdate(sql.getSQL(),sql.getParams());
		}
	}
	/**
	 * 流程退回
	 * @param businessId
	 * @param nextNodeId
	 * @param nextResultId
	 */
	public void updateApplyReturn(String businessId) {
		this.updateApplyReturn(null,businessId);
	}
	
	public void updateApplyReturn(EasyQuery query,String businessId) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(EasyDate.getCurrentDateString(),"last_result_time = ?");
			sql.append(21,",apply_state = ?");
			sql.append(businessId,"where apply_id = ?");
			if(query==null) {
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			}else {
				query.executeUpdate(sql.getSQL(),sql.getParams());
			}
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	
	public void updateApplyBegin(String businessId,String nextNodeId,String nowResultId,String nextResultId) throws SQLException{
		this.updateApplyBegin(null,businessId, nextNodeId, nowResultId, nextResultId);
	}
	
	public void updateApplyBegin(EasyQuery query,String businessId,String nextNodeId,String nowResultId,String nextResultId) throws SQLException{
		EasySQL sql = new EasySQL("update yq_flow_apply set ");
		sql.append(0,"current_node_id = ?");
		sql.append(nowResultId,",current_result_id = ?");
		sql.append(10,",apply_state = ?");
		sql.append(nextResultId,",next_result_id = ?");
		sql.append(nextNodeId,",next_node_id = ?");
		sql.append(businessId,"where apply_id = ?");
		if(query==null) {
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		}else {
			query.executeUpdate(sql.getSQL(),sql.getParams());
		}
	}
	
	public void updateApplyEnd(String businessId,String nowNodeId,String nextResultId) throws SQLException{
		 this.updateApplyEnd(null,businessId,nowNodeId,nextResultId);
	}
	
	public void updateApplyEnd(EasyQuery query,String businessId,String nowNodeId,String nextResultId) throws SQLException{
		EasySQL sql = new EasySQL("update yq_flow_apply set ");
		sql.append(nowNodeId,"current_node_id = ?,");
		sql.append(EasyDate.getCurrentDateString(),"apply_end_time = ?,");
		sql.append(nextResultId,"current_result_id = ?,");
		sql.append(30,"apply_state = ?");// 10 待审批 20审批中 21 拒绝 30审批完成
		sql.append(businessId,"where apply_id = ?");
		if(query!=null) {
			query.executeUpdate(sql.getSQL(),sql.getParams());
		}else {
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		}
	}
	
	public void updateApplyInfo(String businessId,String nextNodeId,int applyState) throws SQLException{
		EasySQL sql = new EasySQL("update yq_flow_apply set ");
		sql.append(nextNodeId,"current_node_id = ?,");
		sql.append(applyState,"apply_state = ?,");
		sql.append(businessId,"where apply_id = ?");
		this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
	}
	
	public  String getID(){
		return getID(12);
	}
	
	public void initFlowField(String flowCode) {
		for(int i=1;i<=20;i++) {
			EasyRecord record = new EasyRecord("yq_flow_field","field_id");
			record.set("flow_code", flowCode);
			record.set("table_field", "data"+i);
			record.set("table_flag", 0);
			record.set("header","字段"+i);
			record.set("title","字段"+i);
			record.set("idx_order",i);
			record.set("update_by","admin");
			record.set("data_type","text");
			record.set("update_time",EasyDate.getCurrentDateString());
			record.set("node_config","{}");
			record.set("ext_config","{}");
			try {
				this.getQuery().save(record);
			} catch (SQLException e) {
				this.getLogger().error(e);
			}
		}
	}
	public  String getID(int n ){
        String val = "";
        Random random = new Random();
        for ( int i = 0; i < n; i++ )
        {
            String str = random.nextInt( 2 ) % 2 == 0 ? "num" : "char";
            if ( "char".equalsIgnoreCase( str ) )
            { // 产生字母
                int nextInt = random.nextInt( 2 ) % 2 == 0 ? 65 : 97;
                // System.out.println(nextInt + "!!!!"); 1,0,1,1,1,0,0
                val += (char) ( nextInt + random.nextInt( 26 ) );
            }
            else if ( "num".equalsIgnoreCase( str ) )
            { // 产生数字
                val += String.valueOf( random.nextInt( 10 ) );
            }
        }
        return val;
    }
	
	public void overtimeFlowNotice() {
		EasySQL sql = new EasySQL("select count(1) count,t2.check_user_id from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("where 1=1");
		sql.append(EasyDate.getCurrentDateString(),"and t2.over_time < ? and t2.over_time <> ''");
		sql.appendIn(new int[]{10,20},"and t1.apply_state");
		sql.append(0,"and t2.check_result = ?");
		sql.append("GROUP BY t2.check_user_id");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			for(JSONObject row:list) {
				int count = row.getIntValue("COUNT");
				String checkUserId = row.getString("CHECK_USER_ID");
				if(StringUtils.notBlank(checkUserId)&&checkUserId.indexOf(",")==-1&&count>0) {
					MessageModel msgModel = new MessageModel();
					msgModel.setReceiver(checkUserId);
					msgModel.setTitle("流程超时没办理");
					msgModel.setWxData("--","--","--","--");
					msgModel.setData4("您有"+count+"个流程超时待办,请尽快处理");
					WxMsgService.getService().sendFlowTodoCheck(msgModel);
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
		}
	}
	public void todoFlowNotice() {
		EasySQL sql = new EasySQL("select count(1) count,t2.check_user_id from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("where 1=1");
		sql.appendIn(new int[]{10,20},"and t1.apply_state");
		sql.append(0,"and t2.check_result = ?");
		sql.append("GROUP BY t2.check_user_id");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			for(JSONObject row:list) {
				int count = row.getIntValue("COUNT");
				String checkUserId = row.getString("CHECK_USER_ID");
				if(StringUtils.notBlank(checkUserId)&&checkUserId.indexOf(",")==-1&&count>0) {
					MessageModel msgModel = new MessageModel();
					msgModel.setReceiver(checkUserId);
					msgModel.setTitle("流程待办提醒");
					msgModel.setUrl("/yq-work/flow/todo");
					msgModel.setWxData("--","--","--","--");
					msgModel.setData4("您有"+count+"个流程待办,请尽快处理");
					WxMsgService.getService().sendFlowTodoCheck(msgModel);
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
		}
	}
	
}
