package com.yunqu.work.utils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.tagext.TagSupport;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.taglib.log.TaglibLogger;

/**
 * 用户控制页面的代码块的显示权限
 *
 */
public class YqResTag extends TagSupport{

	private static final long serialVersionUID = 1709247206558483719L;
	
	/**
	 * 资源ID
	 */
	private String resId;
	private Boolean hasPermission=true;
	
	public String getResId() {
		return resId;
	}

	public void setResId(String resId) {
		this.resId = resId;
	}
	

	public Boolean getHasPermission() {
		return hasPermission;
	}

	public void setHasPermission(Boolean hasPermission) {
		this.hasPermission = hasPermission;
	}

	/**
	 * @return EVAL_BODY_INCLUDE or EVAL_BODY_BUFFERED or SKIP_BODY
	 */
	@Override
	public int doStartTag() throws JspException {
		HttpServletRequest request = (HttpServletRequest)this.pageContext.getRequest();
		UserPrincipal up = (UserPrincipal)request.getUserPrincipal();
		//当前用户没有登录
		if(up == null) {
			if(ServerContext.isDebug()){
				TaglibLogger.getLogger().warn("EasyResTag->登录用户为空，资源不给予显示！");
			}
			return SKIP_BODY;
		}
		if(hasPermission&&up.isSuperUser()){
			return EVAL_PAGE;
		}
		//没有设定当前的资源标签
		if(StringUtils.isBlank(this.getResId())) {
			if(ServerContext.isDebug()){
				TaglibLogger.getLogger().warn ("EasyResTag->ResId为空，资源不给予显示！");
			}
			return SKIP_BODY;
		}
		if(hasPermission){
			String resIds = this.getResId();
			String[] array  = resIds.split(",");
			for(String resId:array){
				if(up.isResource(resId)){
					return EVAL_PAGE;
				}
			}
		}
		if(!hasPermission){
			String resIds = this.getResId();
			String[] array  = resIds.split(",");
			for(String resId:array){
				if(!up.isResource(resId)){
					return EVAL_PAGE;
				}
			}
		}
		return SKIP_BODY;
	}

	/**
	 * @return EVAL_PAGE or SKIP_PAGE
	 */
	@Override
	public int doEndTag() throws JspException {
		return EVAL_PAGE;
	}
	

}
