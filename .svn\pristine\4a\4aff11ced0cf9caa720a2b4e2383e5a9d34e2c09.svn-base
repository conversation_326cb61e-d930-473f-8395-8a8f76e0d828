<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>文档</title>
	<style>
		#editForm .d span{margin: 0px 2px;color: #858585;}
		#DOC_DESC{margin: 0 auto;padding: 10px;}
		.layer-foot span{position: absolute;top: -10px;}
		.layer-foot i,span{cursor: pointer;text-decoration: none;}
		#editForm .userName{
		    font-size: 1em;
		    color: rgba(0,0,0,.87);
		    font-weight: 500;
		}
		#editForm #DOC_DESC{line-height: normal;}
		#DOC_DESC td,#DOC_DESC th{
			border-style: solid;
		}
		.avatar{
			float: left;
			height: 3em;
			width: 3em;
			display: block;
		    margin: .2em 0 0;
		}
    	.avatar img{
		    display: inline-block;
		    height: 100%;
		    width: 100%;
		    border-radius: 100%;
		    overflow: hidden;
		    font-size: inherit;
		    vertical-align: middle;
		    -webkit-box-shadow: 0 0 1px rgba(0,0,0,.3);
		    box-shadow: 0 0 1px rgba(0,0,0,.3);
		}
		.b_content{
			margin: 10px 15px;
		}
		.d_content{
			margin-left: 4em;
			padding: 6px 0px;
		}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 4px;
		}
		#editForm #DOC_DESC img{max-width: 100%;height: auto;}
		<c:if test="${param.isDiv==0}">
			.layer-foot{display: none;}
			body{background-color: #fff;background-image:linear-gradient(120deg,#fdfbfb 0%, #ebedee 100%)}
			.container-fluid{
				padding: 5px 15px;
			}
			@media screen and (max-width: 768px){
				.container-fluid{
					padding: 5px;
				}
			}
		</c:if>
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<div class="ibox-content">
	     	<form id="editForm" class="ibox-index" data-mars="DocDao.detail" autocomplete="off">
		   		  	    <input type="hidden" id="docId" value="${param.docId}" name="docId"/>
		   		  	    <input type="hidden" name="fkId" value="${param.docId}"/>
	                  	<div style="font-size: 20px;font-weight: bold;text-align: left;line-height: 40px;" id="TITLE"></div>
	                  	<div class="d" style="text-align: left;line-height: 30px;">
	                  		<span id="CREATE_NAME"></span> 发布于<span id="UPDATE_TIME"></span> 浏览量 <span id="VIEW_COUNT"></span>
	                  	</div>
	                  	<hr>
	                    <div id="DOC_DESC" style="overflow: auto;"></div>
	              		<p data-template="template-files" class="mt-15" data-mars="FileDao.fileList"></p>
	              	   <br>
					   <div class="layui-hide-xs" style="width: 100%;margin: 10px auto;display: inline-block;">
					  	  <fieldset class="content-title history">
	 							<legend>我来评论</legend>
						  </fieldset>
	  					  <textarea placeholder="在此输入评论内容" name="content" style="height: 70px;" id="comment" class="mt-5 form-control input-sm"></textarea>
					  </div>
					  <div class="clearfix mt-5 layui-hide-xs">
	                   <div class="btn-group pull-left">
							<a type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown">
								动态 <span class="caret"></span>
							</a>
							<ul class="dropdown-menu" role="menu">
								<li><a href="javascript:void(0)" onclick="loadCommentHistory();">评论动态</a></li>
								<li><a href="javascript:void(0)" onclick="lookLog();">浏览动态</a></li>
								<li><a href="javascript:void(0)" onclick="downloadLog();">下载记录</a></li>
							</ul>
						</div>
	                   	<button type="button" style="box-shadow: 0 4px 8px 0 rgba(31,93,234,.35);" class="btn btn-primary btn-sm  pull-right" onclick="addComment()"> <i class="glyphicon glyphicon-send"></i> 发布评论 </button>
					  </div>
	  				     <div id="lookLog">
	 					  	<table id="viewTable"></table>
	 				    </div>
	 				    <div id="opLog">
	 					  	<table id="downloadLogTable"></table>
	 					</div>
	 					<div id="history"></div>
				 	  <div class="layer-foot text-l">
				 		  <i class="glyphicon glyphicon-fullscreen ml-30" onclick="layermax()"></i>
				 		  <a href="/yq-work/doc/${param.docId}" target="_blank"><i class="fa fa-share ml-30"></i></a>
				 		  <i class="glyphicon glyphicon-thumbs-up ml-50"  onclick="useful($(this))"><span id="USEFUL_COUNT" ></span></i>
				 		  <i class="glyphicon glyphicon-thumbs-down ml-50" onclick="noUse($(this))"><span id="NOUSE_COUNT"></span></i>
				 		  <i class="glyphicon glyphicon-star-empty ml-50" onclick="favorite($(this))"><span id="FAVORITE_COUNT"></span></i>
					      <button type="button" title="关闭" class="btn btn-default btn-sm mr-5 pull-right" style="margin-top: 5px;" onclick="layer.closeAll();"> 关闭 </button>
				</div>
	  		</form>
		</div>
  		<script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div">{{call:FILE_TYPE fn='getFileIcon'}}<input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> </a> <a style="color:#17a6f0!important;" href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击下载"  target="_blank">下载</a></div>
			{{/for}}
		</script>
		<script id="template-comments" type="text/x-jsrender">
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{:CREATE_NAME}}</span> <span class="createTime">{{:CREATE_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{call:CONTENT fn='getContent'}}</div>{{if #parent.parent.data.currentUserId == CREATOR}}<a style class="btn btn-xs btn-link hover ib" href="javascript:void(0)" onclick="delComment('{{:COMMENT_ID}}',$(this))">删除</a>	{{/if}}
						</div>						
					</div>
				</div>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
	
		Edit.docId='${param.docId}';
		
		$(function(){
			$("#editForm").render({success:function(result){
				$('title').text($('#TITLE').text());
				layer.photos({photos:'#DOC_DESC',anim: 0,shade:0,shadeClose:true,closeBtn:true}); 
				loadCommentHistory();
			}});
		});
		function layermax(){
			$("#editForm").parents(".layui-layer-content").next().find(".layui-layer-max").click();
		}
		function loadCommentHistory(){
			$("#history").show();
			$("#lookLog").hide();
			$("#opLog").hide();
			ajax.remoteCall("${ctxPath}/webcall?action=CommonDao.commentsList",{fkId:Edit.docId},function(result) { 
				if(result.data==null||result.data.length<=0){
					$("#editForm").find('#history').hide();
				}else{
					result['currentUserId']=getCurrentUserId();
					var html=renderTpl("template-comments",result);
					$("#history").html(html);
				}
			});
		}
		function lookLog(){
			$("#lookLog").show();
			$("#history").hide();
			$("#opLog").hide();
			loadLookLog({tableId:'viewTable',formId:'editForm',fkId:Edit.docId});
		}
		function downloadLog(){
			$("#opLog").show();
			$("#lookLog").hide();
			$("#history").hide();
			loadDownloadLog({tableId:'downloadLogTable',formId:'editForm',fkId:Edit.docId});
		}
		var addComment = function() {
			var data = {content:$("#comment").val(),fkId:Edit.docId};
			ajax.remoteCall("${ctxPath}/servlet/comment?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$("#comment").val("");
						loadCommentHistory();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function delComment(id,obj){
			ajax.remoteCall("${ctxPath}/servlet/comment?action=del",{commentId:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(obj).parents(".b_content").remove();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		var favorite=function(obj) {
			ajax.remoteCall("${ctxPath}/servlet/doc?action=addFavorite",{fkId:Edit.docId},function(result) { 
				if(result.state == 1){
					obj=$(obj).find("span");
					$(obj).text(parseInt($(obj).text())+1);
				}else{
					layer.msg(result.msg,{icon: 7,time:1200});
				}
			},{loading:false});
		}
		var useful=function(obj) {
			ajax.remoteCall("${ctxPath}/servlet/doc?action=useful",{docId:Edit.docId},function(result) { 
				if(result.state == 1){
					obj=$(obj).find("span");
					$(obj).text(parseInt($(obj).text())+1);
				}else{
					layer.msg(result.msg,{icon: 7,time:1200});
				}
			},{loading:false});
		}
		var noUse=function(obj) {
			ajax.remoteCall("${ctxPath}/servlet/doc?action=noUse",{docId:Edit.docId},function(result) { 
				if(result.state == 1){
					obj=$(obj).find("span");
					$(obj).text(parseInt($(obj).text())+1);
				}else{
					layer.msg(result.msg,{icon: 7,time:1200});
				}
			},{loading:false});
		}
</script>
</EasyTag:override>
<c:choose>
	<c:when test="${param.isDiv==0}">
		<%@ include file="/pages/common/layout_form.jsp" %>
	</c:when>
	<c:otherwise>
		<%@ include file="/pages/common/layout_div.jsp" %>
	</c:otherwise>
</c:choose>