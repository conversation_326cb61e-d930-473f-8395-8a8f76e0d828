package com.yunqu.work.model;

import java.util.List;

import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseModel;
import com.yunqu.work.service.StaffService;

public class MessageModel extends AppBaseModel {
   private static final long serialVersionUID = 1L;
	
   private String msgId;
   private int msgType;
   private String receiver;
   private String[] receivers;
   private String receiveName;
   private String contents;
   private int msgState;
   private String createTime;
   private String fkId;
   private String readTime;
   private String url;
   private int senderType;
   private String sender;
   private String emailSender;
   private String sendName;
   private String typeName;
   private String openId;
   
   
   private String msgLabel;
   private String jsonParams;
   
   private String title;
   private String[] cc;
   private String desc;
   private String tplName;
   private JSONObject data;
   private boolean all;
   
   
   private String data1;
   private String data2;
   private String data3;
   private String data4;
   
   private List<FileModel> fileList;
   
    public MessageModel(){
	   setTableInfo("YQ_MESSAGE", "MSG_ID");
	   this.setMsgState(1);
	   this.setMsgType(2001);
	   this.setSenderType(0);
   }
    
    public MessageModel setWxData(String data1,String data2,String data3,String data4){
    	this.setData1(data1);
    	this.setData2(data2);
    	this.setData3(data3);
    	this.setData4(data4);
    	return this;
    }
    

	public String getMsgId() {
		return msgId;
	}
	
	public void setMsgId(String msgId) {
		this.msgId = msgId;
		set("MSG_ID", msgId);
	}
	
	public int getMsgType() {
		return msgType;
	}
	/**
	 * 如: 2000:系统消息 ,2001:消息通知, 2002：私信
	 * @param msgType
	 */
	public MessageModel setMsgType(int msgType) {
		this.msgType = msgType;
		set("MSG_TYPE", msgType);
		return this;
	}
	
	public String getReceiver() {
		return receiver;
	}
	
	public MessageModel setReceiver(String receiver) {
		this.receiver = receiver;
		if(StringUtils.notBlank(receiver)&&receiver.indexOf(",")>-1) {
			this.setReceivers(receiver.split(","));
		}
		set("RECEIVER", receiver);
		if(StringUtils.notBlank(receiver)) {
			this.setReceiveName(StaffService.getService().getUserName(receiver));
		}
		return this;
	}
	
	public String[] getReceivers() {
		return receivers;
	}

	public void setReceivers(String[] receivers) {
		this.receivers = removeEmptyElements(receivers);;
	}

	public String getReceiveName() {
		return receiveName;
	}

	public void setReceiveName(String receiveName) {
		this.receiveName = receiveName;
		this.set("RECEIVE_NAME", receiveName);
	}

	
	public String getMsgLabel() {
		return msgLabel;
	}

	public MessageModel setMsgLabel(String msgLabel) {
		this.msgLabel = msgLabel;
		this.set("MSG_LABEL", msgLabel);
		return this;
	}

	public String getJsonParams() {
		return jsonParams;
	}

	public void setJsonParams(String jsonParams) {
		this.jsonParams = jsonParams;
		this.set("JSON_PARAMS", jsonParams);
	}

	public String getContents() {
		return contents;
	}
	
	public void setContents(String contents) {
		this.contents = contents;
	    this.setTitle(contents);
		set("CONTENTS", contents);
	}
	
	public int getMsgState() {
		return msgState;
	}
	/**
	 * 发送状态： 0 未发送 1 已发送 2 未读 3 已读
	 * @param msgState
	 */
	public void setMsgState(int msgState) {
		this.msgState = msgState;
		set("MSG_STATE", msgState);
	}
	
	public String getCreateTime() {
		return createTime;
	}
	
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
		set("CREATE_TIME", createTime);
	}
	
	public String getFkId() {
		return fkId;
	}
	
	public void setFkId(String fkId) {
		this.fkId = fkId;
		set("FK_ID", fkId);
	}
	
	public String getReadTime() {
		return readTime;
	}
	
	public void setReadTime(String readTime) {
		this.readTime = readTime;
		set("READ_TIME", readTime);
	}
	
	public String getUrl() {
		return url;
	}
	
	public void setUrl(String url) {
		this.url = url;
		set("URL", url);
	}
	
	public int getSenderType() {
		return senderType;
	}
	
	/**
	 * 0 私信  1 微信 2 邮件  3 短信
	 * @param senderType
	 */
	public void setSenderType(int senderType) {
		this.senderType = senderType;
		set("SENDER_TYPE", senderType);
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
		this.set("SENDER", sender);
	}
	
	public String getSendName() {
		return sendName;
	}

	public void setSendName(String sendName) {
		this.sendName = sendName;
		this.set("SEND_NAME", sendName);
	}
	
	
	public String getEmailSender() {
		return emailSender;
	}

	public void setEmailSender(String emailSender) {
		this.emailSender = emailSender;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
		this.set("TYPE_NAME", typeName);
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
		set("CONTENTS", title);
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public JSONObject getData() {
		return data;
	}

	public void setData(JSONObject data) {
		this.data = data;
	}

	public String getTplName() {
		return tplName;
	}

	public void setTplName(String tplName) {
		this.tplName = tplName;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String[] getCc() {
		return cc;
	}

	public void setCc(String[] cc) {
		this.cc = removeEmptyElements(cc);
	}

	public String getData1() {
		return data1;
	}

	public void setData1(String data1) {
		this.data1 = data1;
	}

	public String getData2() {
		return data2;
	}

	public void setData2(String data2) {
		this.data2 = data2;
	}

	public String getData3() {
		return data3;
	}

	public void setData3(String data3) {
		this.data3 = data3;
	}

	public String getData4() {
		return data4;
	}

	public void setData4(String data4) {
		this.data4 = data4;
	}

	public List<FileModel> getFileList() {
		return fileList;
	}

	public void setFileList(List<FileModel> fileList) {
		this.fileList = fileList;
	}
	
    public boolean isAll() {
		return all;
	}

	public void setAll(boolean all) {
		this.all = all;
	}

	public String removeEmptyElements(String input) {
    	StringBuilder result = new StringBuilder();
    	if(StringUtils.notBlank(input)) {
    		String[] elements = input.split(",");
    		for (String element : elements) {
    			if (!element.isEmpty()) {
    				if (result.length() > 0) {
    					result.append(",");
    				}
    				result.append(element);
    			}
    		}
    	}
        return result.toString();
    }

    public static String[] removeEmptyElements(String[] array) {
    	  StringBuilder result = new StringBuilder();
    	  if(array!=null) {
    		  for (String element : array) {
    			  if (element != null && !element.isEmpty()) {
    				  if (result.length() > 0) {
    					  result.append(",");
    				  }
    				  result.append(element);
    			  }
    		  }
    	  }
          return result.toString().split(",");
    }
}
