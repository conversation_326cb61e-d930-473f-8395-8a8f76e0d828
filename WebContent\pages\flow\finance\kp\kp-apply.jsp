<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
	    .layui-table-tool {border: 1px solid #a9b7c1!important;}
		.layui-icon{font-size: 12px;}
		.ibox-content{min-height: calc(100vh - 70px)}
		.text-l{text-align: left!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					 		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td style="width: 42%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的开票申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">合同名称</td>
					  			<td>
					  				<input type="hidden" name="apply.data2"/>
					  				<input type="text" data-rules="required" readonly="readonly" data-ref-erp-no="_erpNo" data-ref-cust-id="_custId"  data-ref-cust-no="_custNo"  data-ref-cust="_custName" onclick="singleContract(this);" class="form-control input-sm" name="apply.data1"/>
					  				<button class="pull-right btn btn-xs btn-default edit-remove" type="button" onclick="contractDetail(this)">详情</button>
					  			</td>
					  			<td class="required">ERP合同号</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" id="_erpNo" class="form-control input-sm" name="apply.data12"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>客户名称</td>
					  			<td>
					  				<input type="hidden" name="apply.data13" id="_custId"/>
					  				<input type="text" readonly="readonly" id="_custName" class="form-control input-sm" name="apply.data14"/>
					  				<button class="pull-right btn btn-xs btn-default edit-remove" type="button" onclick="custDetail(this)">详情</button>
					  			</td>
					  			<td>客户号</td>
					  			<td>
					  				<input type="text" id="_custNo" class="form-control input-sm" name="apply.data15"/>
					  			</td>
					  		</tr>
				  			<tr>
					  			<td>开票资料</td>
					  			<td colspan="3">
					  				 1、是否首次开增值税发票：
					  				 <label class="radio radio-inline radio-success">
			                        	<input type="radio" value="是" name="apply.data5"> <span>是</span>
			                         </label>
			                         <label class="radio radio-inline radio-success">
			                        	<input type="radio" value="否"  checked="checked" name="apply.data5"> <span>否</span>
				                    </label>
			                         <br>
					  				 2、是否有开票资料变更：&nbsp;&nbsp;&nbsp;
					  				 <label class="radio radio-inline radio-success">
			                        	<input type="radio" value="是"  name="apply.data6"> <span>是</span>
			                         </label>
			                         <label class="radio radio-inline radio-success">
			                        	<input type="radio" value="否" checked="checked" name="apply.data6"> <span>否</span>
				                    </label>
			                        <br>
				                    <div style="display: none;">
				                         3、是否需要付款通知书：&nbsp;&nbsp;&nbsp;
				                          <label class="radio radio-inline radio-success">
				                        	<input type="radio" value="是"  name="apply.data7"> <span>是</span>
				                         </label>
				                         <label class="radio radio-inline radio-success">
				                        	<input type="radio" value="否" checked="checked" name="apply.data7"> <span>否</span>
					                    </label>
					                    <a class="ml-5 unedit-remove" target="_blank" href="/template.docx">下载模板</a>
					                    <a class="upload-btn" href="javascript:;">上传</a>
					                    <br>
				                    </div>
									<textarea style="height: 50px;" class="form-control input-sm" placeholder="如首次开票或者客户资料变更，请此处填写开票资料（单位名称、地址、电话、税号开户银行及帐号）" name="apply.data8"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>开票金额</td>
					  			<td>
					  				<input type="number" data-rules="required" readonly="readonly" placeholder="自动计算无需填写" class="form-control input-sm" name="apply.data9"/>
					  			</td>
					  			<td>大写</td>
					  			<td>
					  				<input type="text" readonly="readonly" placeholder="自动计算无需填写" class="form-control input-sm" name="apply.data10"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" rowspan="2">开票内容</td>
					  			<td>
				  				     <label class="radio radio-inline radio-success">
			                        	<input type="radio" value="按合同开" checked="checked" name="apply.data3"> <span>按合同开</span>
			                         </label>
			                         <label class="radio radio-inline radio-success">
			                        	<input type="radio" value="客户提出特别开票要求" name="apply.data3"> <span>客户提出特别开票要求</span>
				                    </label>
					  			</td>
					  			<td>开票要求</td>
					  			<td>
					  				<input type="text" style="font-weight: 400;color: #333;" placeholder="如有特别开票要求,请在此处填写"  class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td colspan="3">
					  			  <div class="layui-col-md12 edit-remove mt-10">
								 	 <table class="layui-table" style="margin-top: 0px;">
									   <thead>
									    <tr>
									      <th style="width: 100px;">序号</th>
									      <th>申请开票阶段</th>
									      <th>发票类型</th>
									      <th>开票金额</th>
									      <th>备注</th>
									    </tr> 
									  </thead>
									  <tbody data-mars="FlowCommon.items" class="edit-remove" data-template="detail-items"></tbody>
									 </table>
								    </div>
					  				<div class="layui-col-md12 unedit-remove mt-10">
								     <div class="layui-table-tool">
										<div class="pull-left">
											<i class="layui-icon layui-icon-time"></i> 明细
										</div>
										<div class="pull-right">
											<button class="btn btn-sm btn-default" onclick="addTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增行</button>
											<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
											<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
											<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
										</div>
								    </div>
								  	<div class="table-responsive" style="min-height: 100px;">
											 <table class="layui-table approve-table" style="margin-top: 0px;">
											   <thead>
											    <tr>
											      <th style="width: 60px;"></th>
											      <th>申请开票阶段</th>
											      <th>发票类型</th>
											      <th>开票金额</th>
											      <th>备注</th>
											    </tr> 
											  </thead>
											  <tbody data-mars="FlowCommon.items" data-template="flow-items"></tbody>
											  <tbody>
											  	<c:forEach var="item" begin="1000" end="1020">
												    <tr id="item_${item}" data-hide-tr="1" style="display:none;">
												      <td>
												      	<input type="hidden" name="order_index_${item}"/>
												      	<input type="hidden" value="${item}" name="itemIndex"/>
												        <label class="checkbox checkbox-inline checkbox-success">
													      	 <input type="checkbox" name="ids" value="${item}"><span></span>
												        </label>
												      </td>
												      <td>
												      	    <select name="data1_${item}" data-rules="required" class="form-control input-sm">
												      	    	<option value="">--请选择--</option>
												      	    	<option value="合同签订(预付款)">合同签订(预付款)</option>
																<option value="验齐货(货到款)">验齐货(货到款)</option>
																<option value="初验(初验款)">初验(初验款)</option>
																<option value="终验(终验款)">终验(终验款)</option>
																<option value="保质期(余款)">保质期(余款)</option>
																<option value="质保款(质保金)">质保款(质保金)</option>
																<option value="阶段款">阶段款</option>
												      	    </select>
												      </td>
												      <td>
												     	  <select name="data2_${item}" data-rules="required" class="form-control input-sm">
													     	<option value="增值税专用发票6%(可抵扣)">增值税专用发票6%(可抵扣)</option>
													     	<option value="增值税专用发票13%(可抵扣)">增值税专用发票13%(可抵扣)</option>
													     	<option value="增值税电子普通发票6%">增值税电子普通发票6%</option>
													     	<option value="增值税电子普通发票13%">增值税电子普通发票13%</option>
													     	<option value="增值税电子普通发票(免税)">增值税电子普通发票(免税)</option>
												     	  </select>
												      </td>
												      <td>
												      		<input type="number" class="form-control input-sm"  data-rules="required" name="data3_${item}" onchange="onlyNumber(this);calcBxMoney('${item}')"/>
												      </td>
												      <td>
												      		<textarea style="height: 40px;resize: none;" maxlength="255" class="form-control input-sm" name="data4_${item}"></textarea>
												      </td>
												    </tr>
											  	</c:forEach>
											  </tbody>	
											</table>
										</div>
								   </div>
					  			</td>
					  		</tr>
				  			<tr style="display: none;">
					  			<td class="required">邮寄地址</td>
					  			<td colspan="3">
									<input value="无" placeholder="如需邮寄，请务必准确填写邮寄地址信息，省市区 +收件人号码+姓名；若无需寄送填写无" class="form-control input-sm" name="apply.applyRemark">
					  			</td>
					  		</tr>
				  			<tr class="edit-remove">
					  			<td>开票时间</td>
					  			<td>
									<input class="form-control input-sm" data-laydate="{type:'date'}" name="apply.data17">
					  			</td>
					  			<td>发票号</td>
					  			<td>
									<input class="form-control input-sm" maxlength="100" type="text" name="apply.data18">
					  			</td>
					  		</tr>
				  			<tr class="edit-remove">
					  			<td>备注</td>
					  			<td colspan="3">
									<textarea style="height: 50px;" placeholder="快递单号等" class="form-control input-sm" name="apply.data11"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	   <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
			
	  <script id="flow-items" type="text/x-jsrender">
  		 {{for data}}
			<tr id="item_{{:ITEM_ID}}">
			  <td>
				<input type="hidden" name="order_index_{{:ITEM_ID}}" value="{{:ITEM_INDEX}}"/>
				<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
				<label class="checkbox checkbox-inline checkbox-success">
					 <input type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
				</label>
			  </td>
			  <td>
					<select name="data1_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" data-value="{{:DATA1}}">
						<option value="">--请选择--</option>
						<option value="合同签订(预付款)">合同签订(预付款)</option>
						<option value="验齐货(货到款)">验齐货(货到款)</option>
						<option value="初验(初验款)">初验(初验款)</option>
						<option value="终验(终验款)">终验(终验款)</option>
						<option value="保质期(余款)">保质期(余款)</option>
						<option value="质保款(质保金)">质保款(质保金)</option>
						<option value="阶段款">阶段款</option>
					</select>
			  </td>
			  <td>
				  <select name="data2_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" data-value="{{:DATA2}}">
						<option value="增值税专用发票6%(可抵扣)">增值税专用发票6%(可抵扣)</option>
						<option value="增值税专用发票13%(可抵扣)">增值税专用发票13%(可抵扣)</option>
						<option value="增值税电子普通发票6%">增值税电子普通发票6%</option>
						<option value="增值税电子普通发票13%">增值税电子普通发票13%</option>
						<option value="增值税电子普通发票(免税)">增值税电子普通发票(免税)</option>
				  </select>
			  </td>
			  <td>
					<input type="number" class="form-control input-sm" data-rules="required" name="data3_{{:ITEM_ID}}" value="{{:DATA3}}" onchange="onlyNumber(this);calcBxMoney('{{:ITEM_ID}}')"/>
			  </td>
			  <td>
					<textarea style="height: 40px;resize: none;" maxlength="255" class="form-control input-sm" name="data4_{{:ITEM_ID}}">{{:DATA4}}</textarea>
			  </td>
			</tr>
 	     {{/for}}
   	  </script>
	  <script id="detail-items" type="text/x-jsrender">
  		 {{for data}}
			<tr>
			  <td style="text-align: left;">{{:ORDER_INDEX}}</td>
			  <td style="text-align: left;">{{:DATA1}}</td>
			  <td style="text-align: left;">{{:DATA2}}</td>
			  <td style="text-align: left;">{{:DATA3}}</td>
			  <td style="text-align: left;">{{:DATA4}}</td>
			</tr>
 	     {{/for}}
   	  </script>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		var Flow = {};

		$(function(){
			FlowCore.initPage({title:'合同{{:data12}}_({{:data9}}元)的开票申请',success:function(result){
				if(FlowCore.businessId){
					var businessInfo = result['FlowDao.businessInfo'];
					var data = businessInfo['data'];
					
					$("[data-value]").each(function(){
						$(this).val($(this).data('value'));
					});
				}else{
					addTr();
				}
			}});
			
		});
		
		Flow.ajaxSubmitForm = function(state){
			$("[data-hide-tr]").remove();
			var index  = 1;
			$("[name^='order_index_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			
			var firstKp = $("[name='apply.data5']:checked").val();
			var v1 = $("[name='apply.data8']").val();
			var v2 = $('.file-div').length;
			if(firstKp=='是'&&v1==''&&v2==0){
				layer.msg('首次开票请填写或上传开票资料',{icon:2});
				return;
			}
			var updateCustInfo = $("[name='apply.data6']:checked").val();
			if(updateCustInfo=='是'&&v1==''&&v2==0){
				layer.msg('客户资料变更请填写或上传最新开票信息',{icon:2});
				return;
			}
			var payNotice = $("[name='apply.data7']:checked").val();
			if(payNotice=='是'&&v2==0){
				layer.msg('请上传付款通知书',{icon:2});
				return;
			}
			var kpRequire = $("[name='apply.data3']:checked").val();
			var v3 = $("[name='apply.data4']").val();
			if(kpRequire=='1'&&v3==''){
				layer.msg('请填写开票特别要求',{icon:2});
				return;
			}
			FlowCore.ajaxSubmitForm(state);
		}
		
		
		Flow.insertData = function() {
		    FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		
		function calcBxMoney(){
			var sum  = 0 ;
			$("[name^='data3_']").each(function(){
				var t = $(this);
				sum = numAdd(sum,t.val());	
			  }
		     );
			$("[name='apply.data9']").val(sum);
			
			var dxVal = digitUppercase(sum);
			$("[name='apply.data10']").val(dxVal);
		}
		
	   function onlyNumber(obj){
		  obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"和"."以外的字符
		  obj.value = obj.value.replace(/^\./g,"");//验证第一个字符是数字而不是字符
		  obj.value = obj.value.replace(/\.{2,}/g,".");//只保留第一个.清除多余的
		  obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
		  obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
	   }
		
	  function addTr(){
			var obj  = $("[data-hide-tr]").first();
			if(obj.length==0){
				layer.msg('不能再新增啦',{icon:7,shade: 0.1});
				return;
			}
			obj.removeAttr("data-hide-tr");
			obj.show();
		}

		function delTr(){
			layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
				layer.close(index);
				$("input[name='ids']:checked").each(function(){
					var id = this.value;
					if(id.length>20){
						ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=delItems",{itemId:id},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg);
								$('#item_'+id).remove();
								calcBxMoney();
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});					
					}else{
						$('#item_'+id).remove();
						calcBxMoney();
					}
					
				});
			});
		}
		
		function upTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var prevTR  = $('#item_'+id).prev();
				if (prevTR.length > 0) {
					prevTR.insertAfter($('#item_'+id));
				}
			}
		}
		function downTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var nextTR  = $('#item_'+id).next();
				if (nextTR.length > 0) {
					nextTR.insertBefore($('#item_'+id));
				}
			}
		}
		
		function selctCallBack(){
			
		}
		
		function contractDetail(el){
			var contractId = $(el).prev().prev().prev().val();
			popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/project/contract',data:{contractId:contractId,isDiv:0}});
		}
		
		function custDetail(el){
			var custId = $(el).prev().prev().prev().val();
			popup.openTab({id:'custDetail',title:'客户详情',type:1,url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId,isDiv:0}});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>