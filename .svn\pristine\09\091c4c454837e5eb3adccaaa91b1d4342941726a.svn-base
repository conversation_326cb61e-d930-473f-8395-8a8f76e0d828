<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>已办流程</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input type="hidden" name="flowCode" value="${flowCode}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          			 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程名称</span>
						 	<input class="form-control input-sm" name="flowName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">待办人</span>
						 	<input class="form-control input-sm" name="checkName">
						 </div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">状态</span>	
							<select name="applyState" onchange="queryData();" class="form-control input-sm">
							      <option value="">请选择 </option>
                    			  <option data-class="label label-default" value="0">草稿</option>
                    			  <option data-class="label label-warning" value="1">作废</option>
							      <option data-class="label label-warning" value="10">待审批</option>
                    			  <option data-class="label label-info" value="20">审批中</option>
                    			  <option data-class="label label-danger" value="21">审批退回</option>
                    			  <option data-class="label label-success" value="30">已完成</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>
		  </script>
		  
	  	 <script type="text/html" id="btnBar">
			 <button class="btn btn-sm btn-default" lay-event="refreshData"><i class="fa fa-refresh"></i> 刷新</button>
			 <button class="btn btn-sm btn-default ml-10" lay-event="recover"><i class="fa fa-bitbucket" aria-hidden="true"></i> 收回</button>
			 <button class="btn btn-sm btn-default ml-10" lay-event="urge"><i class="fa fa-comment-o"></i> 催办</button>
 		</script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'FlowDao.myFlowDone',
					id:'flowTable',
					page:true,
					limit:50,
					rowEvent:'rowEvent',
					toolbar:'#btnBar',
					rowDoubleEvent:'flowDetail',
					height:'full-90',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'radio',
						title: '序号',
						align:'left'
					 },{
						field:'',
						title:'操作',
						width:70,
						align:'center',
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					},{
						 field:'APPLY_NO',
						 title:'编号',
						 width:220,
						 templet:function(row){
							 return row['APPLY_NO']+"_"+row['FLOW_NAME'];
						 }
					 },{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:200
					 },{
						 field:'APPLY_STATE',
						 title:'当前状态',
						 width:80,
						 templet:function(row){
							var val = row['APPLY_STATE'];
							return getText(val,'applyState');
						}
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:80
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:70
					 },{
					    field: 'APPLY_TIME',
						title: '发起时间',
						align:'center',
					    width:140
					},{
						 field:'NODE_NAME',
						 title:'已办节点',
						 width:80
					 },{
						 field:'CHECK_TIME',
						 title:'办理时间',
						 width:120
					 },{
						 field:'CURRENT_NODE',
						 title:'当前节点',
						 width:120
					 },{
						 field:'CHECK_RESULT',
						 title:'流程状态',
						 width:80,
						 hide:true,
						 templet:function(row){
							 return row['CHECK_RESULT']=='1'?'通过':'退回';
						 }
					 }
				]],done:function(){
					
			  	}
			});
		 }
			
		function flowDetail(data){
			var flowCode = data['FLOW_CODE'];
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,resultId:data['CURRENT_RESULT_ID']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
		
	   function refreshData(){
		   location.reload();
	   }
		   
		function recover(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		   
		    var applyState = data['APPLY_STATE'];
			if(applyState=='10'||applyState=='20'){
			   if(data['CURRENT_RESULT_ID']!= data['RESULT_ID']){
				    layer.msg('下节点已经办理,不可收回',{icon:2});
					return;				   
			   }
			    var title = data['APPLY_TITLE'];
			    var businessId = data['APPLY_ID'];
			    var resultId = data['RESULT_ID'];
			    var nextResultId = data['NEXT_RESULT_ID'];
			    layer.confirm('下一步骤尚未接收办理，确认要收回至本步骤重新办理么？',{icon:3,offset:'20px'},function(){
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=recover",{resultId:resultId,businessId:businessId,nextResultId:nextResultId},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,function(){
								refreshData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
			    });
			}else{
				layer.msg('只有审批中的流程才能收回。');
				return;
			}
			
		}
		
		 function urge(dataList){
			   if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    var data = dataList[0];
			    var applyState = data['APPLY_STATE'];
				if(applyState=='10'||applyState=='20'){
				    var title = data['APPLY_TITLE'];
				    var businessId = data['APPLY_ID'];
				    var resultId = data['NEXT_RESULT_ID'];
				    var staffInfo = getStaffInfo();
				    var content = '流程：'+title+'，已达你处，请尽快办理，谢谢！--'+staffInfo.userName;
				    layer.prompt({formType: 2,value: content,offset:'20px',title: '请输入催办信息',area: ['400px', '150px']}, function(msgContent, index, elem){
			    	 	layer.close(index);
			    	  	ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=addUrge",{resultId:resultId,businessId:businessId,msgContent:msgContent},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg)
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});	    	  	
			    	});
				}else{
					layer.msg('只有审批中的流程才能催办。');
					return;
				}
			    
		   }
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>