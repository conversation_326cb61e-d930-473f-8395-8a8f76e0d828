<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
	<style>
		.select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
   			 background-color: #fff;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editForwardForm" autocomplete="off">
	     	   <input type="hidden" id="taskId" value="${param.taskId}" name="taskId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td>
		                    	<select  multiple="multiple" data-mars="CommonDao.userDict" name="mailtos" class="form-control input-sm">
		                    	</select>
		                    </td>
		                </tr>
		                 <tr>
		                    <td>
		                    		<textarea style="height: 110px;" placeholder="抄送说明" maxlength="500" name="ccReason" class="form-control input-sm"></textarea>
		                    </td>
		                </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="TaskForward.ajaxSubmitForm()"> 确 定  </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
       
	    var TaskForward={};
		TaskForward.ajaxSubmitForm = function(flag) {
			var data = form.getJSONObject("#editForwardForm");
			ajax.remoteCall("${ctxPath}/servlet/task?action=addCC",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						initFormData();
						popup.layerClose("#editForwardForm");
					});
				}else{
					layer.alert('请选择抄送人',{icon: 5});
				}
			});
		}
		$(function(){
			 requreLib.setplugs('select2',function(){
				$("#editForwardForm").render({success:function(){
					$("#editForwardForm select").select2({theme: "bootstrap",placeholder:'请选择'});
				}});
			 });
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>