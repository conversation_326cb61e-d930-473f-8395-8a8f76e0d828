<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>项目仪表盘</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }


        .layui-this {
            font-weight: bold;
        }


        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .top-3 {
            font-size: 24px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .divInline * {
            display: inline;
        }

        .divInline {
            height: 24px;
        }


        .pj-stat .layui-card {
            padding: 10px;
            border-radius: 5px;
        }

        .pj-stat .layui-card-hover:hover {
            transform: scale(1.02);
            transition: transform .3s ease;
            box-shadow: 0 4px 11px #0003;
            cursor: pointer;
        }


        .layui-icon-tips {
            margin-left: 3px;
            font-size: 14px;
        }

        #fixedDiv {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 999;
            background: #fff;
            padding: 0px 10px;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        #searchForm {
            padding-top: 60px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .layui-tab-content .layui-card {
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .fullscreen {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: white !important;
            z-index: 9999 !important;
            padding: 20px;
            box-sizing: border-box;
        }

        .fullscreen .layui-card-body {
            height: calc(100% - 55px) !important;
            width: 100%;
        }

        .layui-card-body-2{
            padding:0;
            width: 100%;
            height: calc(100% - 50px) ;
        }

        .fullscreen .layui-card-body-2 {
            height: calc(100% - 60px) !important;
            overflow-y: scroll; !important;
            padding:10px 15px;
            width: 100%;
        }

        /* 添加过渡动画效果 */
        .table-card-410{
            transition: all 0.2s ease;
            height: 410px;
        }

        .table-card-500{
            transition: all 0.2s ease;
            height: 500px;
        }

        .chart-card-430{
            transition: all 0.1s ease;
            height: 430px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm" autocomplete="off">
        <div style="display: none">
            <select name="projectState" class="form-control input-sm">
                <option value=""></option>
                <option value="11" data-class="text-info">进度正常</option>
                <option value="12" data-class="text-warning">存在风险</option>
                <option value="13" data-class="text-danger">进度失控</option>
                <option value="20" data-class="text-danger">挂起中</option>
                <option value="30" data-class="text-success">已完成</option>
            </select>
        </div>
        <div class="layui-row" id="fixedDiv">
            <div class="form-group" style="flex-grow: 1;display: flex;margin-top: 10px;">
                <input type="hidden" name="timeType" id="timeType" value="begin_date">
                <div class="btn-group-sm btn-group time-type" style="width: 310px;margin-left: 20px">
                    <button class="btn btn-info btn-xs" type="button" value="begin_date">项目开始时间</button>
                    <button class="btn btn-default btn-xs" type="button" value="create_time">创建时间</button>
                    <button class="btn btn-default btn-xs" type="button" value="cy_date">计划初验</button>
                    <button class="btn btn-default btn-xs" type="button" value="zy_date">计划终验</button>
                </div>
                <div class="input-group input-group-sm ml-10" style="width: 280px">
                    <span class="input-group-addon">时间</span>
                    <input data-mars="CommonDao.yearBegin" name="beginDate" id="beginDate"
                           class="form-control input-sm" onchange="timeChange()"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="12">
                    <span class="input-group-addon">-</span>
                    <input data-mars="CommonDao.yearDateEnd" name="endDate" id="endDate"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="12" onchange="timeChange()"
                           class="form-control input-sm">
                </div>
                <div class="btn-group-sm btn-group select-begin-time" style="width: 400px;margin-left: 20px">
                    <button class="btn btn-default btn-xs" type="button" value="threeMonth">3月内</button>
                    <button class="btn btn-default btn-xs" type="button" value="halfYear">半年内</button>
                    <button class="btn btn-default btn-xs" type="button" value="oneYear">1年内</button>
                    <button class="btn btn-default btn-xs" type="button" value="twoYear">2年内</button>
                    <button class="btn btn-info btn-xs" type="button" value="nowYear">今年</button>
                    <button class="btn btn-default btn-xs" type="button" value="lastYear">去年</button>
                    <button class="btn btn-default btn-xs" type="button" value="nextYear">明年</button>
                    <button class="btn btn-default btn-xs" type="button" value="all">全部</button>
                </div>
                <i class="layui-icon layui-icon-tips" lay-tips="除特殊说明外，本页面下所有统计和列表均根据此置顶区的时间条件对项目进行了筛选。"></i>
            </div>
        </div>

        <div class="layui-row layui-col-space10 pj-stat" data-mars="ProjectStatisticDao.projectDashboardStat">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="divInline">
                            <div class="top-1" id="newProjectCount">0</div>
                            <div class="top-3">(<span id="newProjectCount2">0</span>)</div>
                        </div>
                        <div class="top-2">今年开工项目
                            <i class="layui-icon layui-icon-tips" lay-tips="括号内只统计技术开发合同和技术服务合同的项目。"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="projectCount">0</div>
                        <div class="top-2">当前进行中项目</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="taskCount">0</div>
                        <div class="top-2">当前进行中任务</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-3" id="riskCount">0</div>
                        <div class="top-2">当前待处理风险</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-3" id="projectCount2">0</div>
                        <div class="top-2">时段内项目总数</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10 layui-tab" lay-filter="filter1">
            <ul class="layui-tab-title" style="background-color: #fff;">
                <li class="layui-this" lay-id="tab1">项目总览</li>
                <li lay-id="tab3">部门</li>
                <li lay-id="tab4">个人</li>
                <li lay-id="tab2">进度/风险</li>
            </ul>
            <div class="layui-tab-content" style="margin: 0;padding-top:10px;background-color: #fff;">
                <div class="layui-tab-item layui-show">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md8">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    项目趋势(月)
                                </div>
                                <div style="height: 360px">
                                    <div id="timeChart">
                                        <div id="timeChart1" style="height: 340px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-card">
                                <div class="layui-card-header">项目状态统计</div>
                                <div>
                                    <div id="stateChart1" style="height: 360px;width: 100%;" data-anim="fade"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6">
                            <div class="layui-card layui-col-md12">
                                <div class="layui-card-header">项目阶段统计</div>
                                <div>
                                    <div class="layui-col-md12">
                                        <div id="stageChart" style="height: 380px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    <input type="hidden" id="fieldName" name="fieldName" value="PROJECT_TYPE"/>
                                    <select class="select-ratio-field" style="width: 150px;float: left;color:#000000;height:42px;font-size:14px;box-shadow:none;border: none;">
                                        <option value="PROJECT_TYPE" selected> 项目类型占比</option>
                                        <option value="CONTRACT_TYPE">合同类型占比</option>
                                        <option value="PROJECT_DEPT_NAME">工程大区占比</option>
                                        <option value="PRODUCT_LINE">产品线占比</option>
                                    </select>
                                    <i class="layui-icon layui-icon-tips" lay-tips="项目的统计范围跟随置顶栏的时间条件。"></i>
                                </div>
                                <div style="height: 380px">
                                    <div>
                                        <div id="ratioChart1" style="height: 380px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md5">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    <select class="selectPersonCount" style="width: 150px;float: left;color:#000000;height:42px;font-size:14px;box-shadow:none;border: none;">
                                        <option value="personCountStat" selected> 项目规模分布</option>
                                        <option value="workPersonCountStat"> 实际参与分布</option>
                                    </select>
                                    <i class="layui-icon layui-icon-tips" lay-tips="项目规模（参与人数）指项目中[成员]的数量，实际参与(周报人数)指周报中填写过该项目的员工的总数。"></i>
                                </div>
                                <div style="height: 380px">
                                    <div id="personCountStat">
                                        <div id="personChart1" style="height: 360px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div id="workPersonCountStat" style="display: none">
                                        <div id="personChart2" style="height: 360px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md7 chart-card-430" id="personChart3Card">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    项目规模TOP15
                                    <i class="layui-icon layui-icon-tips" lay-tips="根据项目参与人数和周报填写人数进行统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('personChart3Card','personCountRankTableDiv','personChart3')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="personChart3" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="personCountRankTableDiv">
                                        <table id="personCountRankTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12 chart-card-430" id="workHourTypeCard">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    开发工程工时TOP25
                                    <i class="layui-icon layui-icon-tips" lay-tips="根据顶部的时间框筛选周报时间后统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('workHourTypeCard','workHourTypeTableDiv','workHourTypeChart')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="workHourTypeChart" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="workHourTypeTableDiv">
                                        <table id="workHourTypeTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12 chart-card-430" id="delayTable1Card">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    延期任务数量TOP20
                                    <i class="layui-icon layui-icon-tips" lay-tips="根据顶部的时间框筛选任务创建时间后统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('delayTable1Card','delayTable1Div','delayChart1')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="delayChart1" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="delayTable1Div">
                                        <div class="layui-col-md6">
                                            <table id="delayTable1"></table>
                                        </div>
                                        <div class="layui-col-md6">
                                            <div style="padding:0 15px"><span style="font-weight: bold" id="projectNameDiv" ></span></div>
                                            <div id="delayAiAnswer" class="layui-card-body markdown-body">


                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12 chart-card-430" id="delayTable2Card">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    延期任务时长TOP20
                                    <i class="layui-icon layui-icon-tips" lay-tips="根据顶部的时间框筛选任务创建时间后统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('delayTable2Card','delayTable2Div','delayChart2')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="delayChart2" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="delayTable2Div">
                                        <div class="layui-col-md6">
                                            <table id="delayTable2"></table>
                                        </div>
                                        <div class="layui-col-md6">
                                            <div style="padding:0 15px"><span style="font-weight: bold" id="projectNameDiv2" ></span></div>
                                            <div id="delayAiAnswer2" class="layui-card-body markdown-body">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12 table-card-500" id="projectListTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    总项目统计表 <i class="layui-icon layui-icon-tips" lay-tips="列表搜索结果由置顶的时间条件得出。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('projectListTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body">
                                    <table id="projectListTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6 chart-card-430" id="deptTable1Card">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    周报参与项目TOP20
                                    <i class="layui-icon layui-icon-tips" lay-tips="通过周报中的项目数进行统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('deptTable1Card','deptTable1Div','deptChart1')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="deptChart1" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="deptTable1Div">
                                        <table id="deptTable1"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6 chart-card-430" id="deptTable2Card">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    任务参与项目TOP20
                                    <i class="layui-icon layui-icon-tips" lay-tips="通过被指派的任务进行统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('deptTable2Card','deptTable2Div','deptChart2')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="deptChart2" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="deptTable2Div">
                                        <table id="deptTable2"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6 chart-card-430" id="deptFinishTaskCard">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    部门完成任务TOP20
                                    <i class="layui-icon layui-icon-tips" lay-tips="根据顶部的时间框筛选任务创建时间后统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('deptFinishTaskCard','deptFinishTaskTableDiv','deptFinishTaskChart')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="deptFinishTaskChart" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="deptFinishTaskTableDiv">
                                        <table id="deptFinishTaskTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6 chart-card-430" id="deptUndoTaskCard">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    部门待完成任务TOP20
                                    <i class="layui-icon layui-icon-tips" lay-tips="统计全部待完成任务，不根据置顶的时间条件进行筛选"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('deptUndoTaskCard','deptUndoTaskTableDiv','deptUndoTaskChart')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="deptUndoTaskChart" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="deptUndoTaskTableDiv">
                                        <table id="deptUndoTaskTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6 chart-card-430" id="deptDelayTable1Card">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    延期任务数量TOP20
                                    <i class="layui-icon layui-icon-tips" lay-tips="根据顶部的时间框筛选任务创建时间后统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('deptDelayTable1Card','deptDelayTable1Div','deptDelayChart1')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="deptDelayChart1" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="deptDelayTable1Div">
                                        <table id="deptDelayTable1"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6 chart-card-430" id="deptDelayTable2Card">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    任务延期时间TOP20
                                    <i class="layui-icon layui-icon-tips" lay-tips="根据顶部的时间框筛选任务创建时间后统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('deptDelayTable2Card','deptDelayTable2Div','deptDelayChart2')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="deptDelayChart2" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="deptDelayTable2Div">
                                        <table id="deptDelayTable2"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12 chart-card-430" id="userTaskCard">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    完成任务数量TOP30
                                    <i class="layui-icon layui-icon-tips" lay-tips="根据顶部的时间框筛选任务创建时间后统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('userTaskCard','userTaskTableDiv','userTaskChart')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="userTaskChart" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="userTaskTableDiv">
                                        <table id="userTaskTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12 chart-card-430" id="userDelayCard">
                            <div class="layui-card" style="height:100%;width: 100%;">
                                <div class="layui-card-header">
                                    延期任务数量TOP30
                                    <i class="layui-icon layui-icon-tips" lay-tips="根据顶部的时间框筛选任务创建时间后统计。"></i>
                                    <div style="float:right;" onclick="toggleFullScreenWithChart('userDelayCard','userDelayTableDiv','userDelayChart')">
                                        <i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i>
                                    </div>
                                </div>
                                <div class="layui-card-body-2">
                                    <div style="height: 370px">
                                        <div id="userDelayChart" style="height: 370px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div style="display: none;height: 450px;padding-top: 0" id="userDelayTableDiv">
                                        <table id="userDelayTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4 table-card-410" id="sxTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">本月上线<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件，仅由上线日期得出。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('sxTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i> </div>
                                </div>
                                <div  class="layui-card-body" style="width: 100%">
                                    <table id="sxTable"></table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 table-card-410" id="cyTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">本月计划初验<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件，仅由计划初验日期得出，包含全部的项目阶段。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('cyTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body">
                                    <table id="cyTable"></table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 table-card-410" id="zyTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">本月计划终验<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件，仅由计划终验日期得出，包含全部的项目阶段。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('zyTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body">
                                    <table id="zyTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4 table-card-410" id="allCyTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">全部待初验<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件，显示全部项目阶段为待初验的项目。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('allCyTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body">
                                    <table id="allCyTable"></table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 table-card-410" id="allZyTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">全部待终验<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件，显示全部项目阶段为待终验的项目。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('allZyTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body">
                                    <table id="allZyTable"></table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4 table-card-410" id="allOnceTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">全部待一次性验收<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件，显示全部项目阶段为待一次性验收的项目。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('allOnceTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body">
                                    <table id="allOnceTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12 table-card-410" id="undoRiskTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    待处理风险
                                    <i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件，显示所有待处理/所有状态的风险。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('undoRiskTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body">
                                    <table id="undoRiskTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12 table-card-410" id="finishRiskTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    已完成风险
                                    <i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件，显示所有待处理/所有状态的风险。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('finishRiskTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body">
                                    <table id="finishRiskTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12 table-card-500" id="projectProgressListTableCard">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    总项目进度列表 <i class="layui-icon layui-icon-tips" lay-tips="列表搜索结果由置顶的时间条件得出。"></i>
                                    <div style="float:right;" onclick="toggleFullScreen('projectProgressListTableCard')"><i class="layui-icon layui-icon-screen-full" style="font-size: 20px; color: #c9c9c9;"></i></div>
                                </div>
                                <div class="layui-card-body">
                                    <table id="projectProgressListTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/console-echarts.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

    <script>
        var loadMap = {};
        var TabId = 'tab1';
        var tabLoadMap = {};

        $(function () {

            $("#searchForm").render({
                success: function () {
                    timeChange();
                }
            });

            layui.element.on('tab(filter1)', function (data) {
                TabId = data.id;
                if(!tabLoadMap[TabId]){
                    loadAllChartAndTable();
                }
            });


            $('.select-begin-time button').click(function () {
                getDate($(this)[0], 'beginDate', 'endDate');
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                timeChange();
            });

            $('.select-ratio-field').on('change', function () {
                var value = $(this).val();
                $('#fieldName').val(value);
                loadRatioChart();
            });

            $('.selectPersonCount').on('change', function () {
                var value = $(this).val();
                $('#' + value).show();
                $('#' + value).siblings().hide();
                if (value === "workPersonCountStat") {
                    echarts.getInstanceByDom(document.getElementById("personChart2")).resize();
                }
            });

            $('.time-type button').click(function () {
                $('[name="timeType"]').val($(this)[0].value);
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                timeChange();
            });
        });


        function timeChange() {
            tabLoadMap = {};
            loadAllChartAndTable();
        }

        function loadAllChartAndTable(){
            if(TabId === 'tab1'){
                loadTimeChart();
                loadRatioChart();
                loadStageChart();
                loadStateChart();
                loadPersonChart();
                loadProjectListTable();
                loadDelayTable1();
                loadDelayTable2();
                loadPersonTable();
                setTimeout(function () {
                    loadWorkPersonChart();
                    loadWorkHourTypeTable();
                }, 300)
            }else if(TabId === 'tab2'){
                loadSxTable();
                loadCyTable();
                loadZyTable();
                loadRiskTable(0,'undoRiskTable');
                loadRiskTable(1,'finishRiskTable');
                loadAllStageTable("待初验", "allCyTable","CY_DATE","计划初验");
                loadAllStageTable("待终验", "allZyTable","ZY_DATE","计划终验");
                loadAllStageTable("待一次性验收", "allOnceTable","ZY_DATE","计划终验");
                setTimeout(function () {
                    loadProjectProgressListTable();
                }, 300)
            }else if(TabId === 'tab3'){
                loadDeptProjectTableAndChart('ProjectStatisticDao.weeklyProjectNumsRankByDept','deptTable1','deptChart1');
                loadDeptProjectTableAndChart('ProjectStatisticDao.taskProjectNumsRankByDept','deptTable2','deptChart2');
                loadDeptFinishTaskTable();
                loadDeptUndoTaskTable();
                loadDeptDelayChart1();
                loadDeptDelayChart2();
            }else if(TabId === 'tab4'){
                loadUserTaskChart();
                loadUserDelayChart();
            }
            tabLoadMap[TabId]=true;
        }

        function loadTimeChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectCountByTime", {"beginDate": beginDate, "endDate": endDate}, function (result) {
                var data1 = result["begin_date"];
                var data2 = result["end_date"];

                var timeTypes = ['开始项目', '结束项目'];
                var months = Object.keys(data1);
                var amount1 = months.map(month => data1[month] || "0");
                var amount2 = months.map(month => data2[month] || "0");

                loadTwoLineChart(months, amount1, amount2, timeTypes,'timeChart1');

            })
        }

        function loadRatioChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            var fieldName = $('#fieldName').val();

            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectFieldRatio", {
                "beginDate": beginDate,
                "endDate": endDate,
                "timeType": timeType,
                "fieldName": fieldName
            }, function (result) {
                var data2 = result.data;
                var projectTypeMapping = {"10": "合同项目", "11": "提前执行", "20": "自研项目", "30": "维保项目"};

                const echartsData = data2.map(item => {
                    if (fieldName == "PROJECT_TYPE") {
                        return {
                            name: projectTypeMapping[item.FIELD_NAME],
                            value: item.NUMS
                        };
                    } else {
                        return {
                            name: item.FIELD_NAME,
                            value: item.NUMS
                        };
                    }

                });
                loadPieChart(echartsData, "ratioChart1");
            })
        }

        var delayChart1ProjectIds = [];
        function loadDelayTable1() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.delayTaskRankByProject',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#delayTable1Div-50',
                    autoSort: false,
                    id: 'delayTable1',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 100,
                        }, {
                            field: 'DELAY_TASK',
                            title: '延期任务数',
                            width: 100,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'SUM_DELAY',
                            title: '总延期次数',
                            width: 100,
                            sort: true,
                            align: 'center',
                        }, {
                            field: '',
                            title: '操作',
                            width: 80,
                            sort: true,
                            align: 'center',
                            templet: function (d) {
                                return '<button type="button" class="btn btn-xs btn-default" onclick="analyzeProjectDelay1(\'' + d.PROJECT_ID + '\', \'' + d.PROJECT_NAME + '\')">AI总结</button>';
                            }
                        }
                    ]],
                    done: function (data) {
                        if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                            return;
                        }
                        var data2 = data.data;
                        data2 = data2.slice(0, 20);
                        var projectName = data2.map(item => subContractName(item.PROJECT_NAME));
                        var nums1 = data2.map(item => item.DELAY_TASK);
                        var nums2 = data2.map(item => item.SUM_DELAY);
                        var legend = ['延迟任务数量', '总延迟次数']
                        delayChart1ProjectIds = data2.map(item => item.PROJECT_ID);
                        var delayChart1 = loadBarAndLineChart(projectName, nums1, nums2, "delayChart1", legend,'defaultStyle')
                        if (!loadMap["delayChart1"]) {
                            delayChart1.on('click', function (params) {
                                var index = params.dataIndex;
                                var projectId = delayChart1ProjectIds[index];
                                projectDetail(projectId)
                            });
                        }
                        loadMap["delayChart1"] = true;
                    }
                }
            )
        }

        var delayChart2ProjectIds =[];
        function loadDelayTable2() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.delayTimeRankByProject',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#delayTable2Div-50',
                    autoSort: false,
                    id: 'delayTable2',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 100,
                        }, {
                            field: 'MAX_DELAY_DAYS',
                            title: '最大延期/天',
                            minWidth: 100,
                            maxWidth:110,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'AVERAGE_DELAY_DAYS',
                            title: '平均延期/天',
                            minWidth: 100,
                            maxWidth:110,
                            sort: true,
                            align: 'center',
                        }, {
                            field: '',
                            title: '操作',
                            width: 80,
                            sort: true,
                            align: 'center',
                            templet: function (d) {
                                return '<button type="button" class="btn btn-xs btn-default" onclick="analyzeProjectDelay2(\'' + d.PROJECT_ID + '\', \'' + d.PROJECT_NAME + '\')">AI总结</button>';
                            }
                        }
                    ]],
                    done: function (data) {
                        if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                            return;
                        }
                        var data2 = data.data;
                        data2 = data2.slice(0, 20);
                        var projectName = data2.map(item => subContractName(item.PROJECT_NAME));
                        var nums1 = data2.map(item => item.MAX_DELAY_DAYS);
                        var nums2 = data2.map(item => item.AVERAGE_DELAY_DAYS);
                        var legend = ['任务最大延期/天', '任务平均延期/天']
                        delayChart2ProjectIds = data2.map(item => item.PROJECT_ID);
                        var delayChart2 = loadBarAndLineChart(projectName, nums1, nums2, "delayChart2", legend,'defaultStyle')
                        if (!loadMap["delayChart2"]) {
                            delayChart2.on('click', function (params) {
                                var index = params.dataIndex;
                                var projectId = delayChart2ProjectIds[index];
                                projectDetail(projectId)
                            });
                        }
                        loadMap["delayChart2"] = true;
                    }
                },
            )
        }

        function analyzeProjectDelay1(projectId,projectName){
            analyzeProjectDelay(projectId,projectName,"projectNameDiv","delayAiAnswer");
        }

        function analyzeProjectDelay2(projectId,projectName){
            analyzeProjectDelay(projectId,projectName,"projectNameDiv2","delayAiAnswer2");
        }

        function analyzeProjectDelay(projectId, projectName,projectNameDiv,delayAiAnswerDiv){
            $('#'+projectNameDiv).text(projectName)
            $('#'+delayAiAnswerDiv).html('..分析中...')
            // 创建 EventSource
            var source = new EventSource('${ctxPath}/llm/analyzeProjectDelayStream/' + projectId);
            var content = '';

            // 监听消息
            source.onmessage = function(event) {
                content += event.data;
                $('#'+delayAiAnswerDiv).html(content);
            };

            // 监听错误
            source.onerror = function(event) {
                source.close();
                // 转换markdown为HTML
                $.get("${ctxPath}/llm/markdownToHtml", {id: projectId}, function(result) {
                    if(result) {
                        $('#'+delayAiAnswerDiv).html(result);
                    }
                });
            };
        }

        function loadDeptDelayChart1() {
            $("#searchForm").initTable({
                mars: 'ProjectStatisticDao.delayTaskRankByDept',
                cellMinWidth: 50,
                limit: 20,
                height: '#deptDelayTable1Div-50',
                autoSort: false,
                id: 'deptDelayTable1',
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align: 'left'
                    }, {
                        field: 'DEPT_NAME',
                        title: '部门',
                        minWidth: 100,
                        align: 'center',
                    }, {
                        field: 'DELAY_TASK',
                        title: '延期任务数',
                        minWidth: 100,
                        maxWidth:200,
                        sort: true,
                        align: 'center',
                    }, {
                        field: 'SUM_DELAY',
                        title: '总延期次数',
                        minWidth: 100,
                        maxWidth:200,
                        sort: true,
                        align: 'center',
                    }
                ]],
                done: function(data) {
                    if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                        return;
                    }
                    var data2 = data.data;
                    var deptName = data2.map(item => item.DEPT_NAME);
                    var nums1 = data2.map(item => item.DELAY_TASK);
                    var nums2 = data2.map(item => item.SUM_DELAY);
                    var legend = ['延迟任务数量', '总延迟次数'];
                    loadBarAndLineChart(deptName, nums1, nums2, "deptDelayChart1", legend,'defaultStyle');
                }
            });
        }

        function loadDeptDelayChart2() {
            $("#searchForm").initTable({
                mars: 'ProjectStatisticDao.delayTimeRankByDept',
                cellMinWidth: 50,
                limit: 20,
                height: '#deptDelayTable2Div-50',
                autoSort: false,
                id: 'deptDelayTable2',
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align: 'left'
                    }, {
                        field: 'DEPT_NAME',
                        title: '部门',
                        minWidth: 100,
                        align: 'center',
                    }, {
                        field: 'MAX_DELAY_DAYS',
                        title: '最大延期/天',
                        minWidth: 100,
                        maxWidth:200,
                        sort: true,
                        align: 'center',
                    }, {
                        field: 'AVERAGE_DELAY_DAYS',
                        title: '平均延期/天',
                        minWidth: 100,
                        maxWidth:200,
                        sort: true,
                        align: 'center',
                    }
                ]],
                done: function(data) {
                    if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                        return;
                    }
                    var data2 = data.data;
                    var deptName = data2.map(item => item.DEPT_NAME);
                    var nums1 = data2.map(item => item.MAX_DELAY_DAYS);
                    var nums2 = data2.map(item => item.AVERAGE_DELAY_DAYS);
                    var legend = ['任务最大延期/天', '任务平均延期/天'];
                    loadBarAndLine2yAxisChart(deptName, nums1, nums2, "deptDelayChart2", legend);
                }
            });
        }

        function loadUserTaskChart(){
            $("#searchForm").initTable({
                mars: 'ProjectStatisticDao.taskRankByUser',
                cellMinWidth: 50,
                limit: 30,
                height: '#userTaskTableDiv-50',
                autoSort: false,
                id: 'userTaskTable',
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align: 'left'
                    }, {
                        field: 'ASSIGN_USER_NAME',
                        title: '人员',
                        minWidth: 100,
                        align: 'center',
                    }, {
                        field: 'ASSIGN_DEPT_NAME',
                        title: '部门',
                        minWidth: 100,
                        maxWidth: 200,
                        sort: true,
                        align: 'center',
                    },{
                        field: 'TASK_NUMS',
                        title: '完成任务数',
                        minWidth: 100,
                        maxWidth: 200,
                        sort: true,
                        align: 'center',
                    }, {
                        field: 'PROJECT_COUNTS',
                        title: '项目数',
                        minWidth: 100,
                        maxWidth: 200,
                        sort: true,
                        align: 'center',
                    }
                ]],
                done: function(data) {
                    if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 30) {
                        return;
                    }
                    var data2 = data.data;
                    var userName = data2.map(item => item.ASSIGN_USER_NAME);
                    var nums1 = data2.map(item => item.TASK_NUMS);
                    var nums2 = data2.map(item => item.PROJECT_COUNTS);
                    var legend = ['完成任务数','项目数']
                    loadBarAndLineChart(userName,nums1,nums2, "userTaskChart", legend,'blueStyle')
                }
            });
        }

        function loadUserDelayChart() {
            $("#searchForm").initTable({
                mars: 'ProjectStatisticDao.delayTaskRankByUser',
                cellMinWidth: 50,
                limit: 30,
                height: '#userDelayTableDiv-50',
                autoSort: false,
                id: 'userDelayTable',
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align: 'left'
                    }, {
                        field: 'ASSIGN_USER_NAME',
                        title: '人员',
                        minWidth: 100,
                        align: 'center',
                    },{
                        field: 'ASSIGN_DEPT_NAME',
                        title: '部门',
                        minWidth: 100,
                        maxWidth: 200,
                        sort: true,
                        align: 'center',
                    }, {
                        field: 'DELAY_TASK',
                        title: '延期任务数',
                        minWidth: 100,
                        maxWidth: 200,
                        sort: true,
                        align: 'center',
                    }, {
                        field: 'SUM_DELAY',
                        title: '总延期次数',
                        minWidth: 100,
                        maxWidth: 200,
                        sort: true,
                        align: 'center',
                    }
                ]],
                done: function(data) {
                    if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 30) {
                        return;
                    }
                    var data2 = data.data;
                    var userName = data2.map(item => item.ASSIGN_USER_NAME);
                    var nums1 = data2.map(item => item.DELAY_TASK);
                    var nums2 = data2.map(item => item.SUM_DELAY);
                    var legend = ['延迟任务数量', '总延迟次数']
                    loadBarAndLineChart(userName, nums1, nums2, "userDelayChart", legend,'defaultStyle')
                }
            });
        }


        function loadSxTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectList1',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#sxTableCard-80',
                    autoSort: false,
                    data: {"timeType2": "sx_date"},
                    id: 'sxTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed:'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 140,
                            fixed:'left'
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            minWidth: 110,
                            maxWidth:150
                        }, {
                            field: 'SX_DATE',
                            title: '上线时间',
                            minWidth: 90,
                            sort: true,
                            align: 'center',
                            maxWidth:150
                        },
                    ]],
                    done: function (data) {
                        table.on('sort(sxTable)', function (obj) {
                            $("#searchForm").queryData({
                                id: 'sxTable', jumpOne: true, initSort: obj,
                                data: {sortName: obj.field, sortType: obj.type, "timeType2": "sx_date"}
                            })
                        })
                    }
                },
            )
        }

        function loadCyTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectList1',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#cyTableCard-80',
                    autoSort: false,
                    data: {"timeType2": "cy_date"},
                    id: 'cyTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed:'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 140,
                            fixed:'left'
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            minWidth: 110,
                            maxWidth:200
                        }, {
                            field: 'CY_DATE',
                            title: '计划初验',
                            minWidth: 90,
                            maxWidth:150,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'CY_REAL_DATE',
                            title: '实际初验',
                            minWidth: 80,
                            align: 'center',
                            maxWidth:150
                        }
                    ]],
                    done: function (data) {
                        table.on('sort(cyTable)', function (obj) {
                            $("#searchForm").queryData({
                                id: 'cyTable', jumpOne: true, initSort: obj,
                                data: {sortName: obj.field, sortType: obj.type, "timeType2": "cy_date"}
                            })
                        })
                    }
                },
            )
        }

        function loadZyTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectList1',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#zyTableCard-80',
                    autoSort: false,
                    data: {"timeType2": "zy_date"},
                    id: 'zyTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed:'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 140,
                            fixed:'left'
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            minWidth: 110,
                            maxWidth:200,
                        }, {
                            field: 'ZY_DATE',
                            title: '计划终验',
                            minWidth: 90,
                            maxWidth:200,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'ZY_REAL_DATE',
                            title: '实际终验',
                            minWidth: 80,
                            maxWidth:150,
                            align: 'center',
                        },
                    ]],
                    done: function (data) {
                        table.on('sort(zyTable)', function (obj) {
                            $("#searchForm").queryData({
                                id: 'zyTable', jumpOne: true, initSort: obj,
                                data: {sortName: obj.field, sortType: obj.type, "timeType2": "zy_date"}
                            })
                        })
                    }
                },
            )
        }

        function loadAllStageTable(stage, tableId,field1,title1) {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectListByCondition',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#' + tableId + 'Card-80',
                    autoSort: false,
                    data: {"projectStage": stage},
                    id: tableId,
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed:'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 140,
                            fixed:'left'
                        }, {
                            field: field1,
                            title: title1,
                            minWidth: 90,
                            maxWidth:150,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'LAST_TASK_TIME',
                            title: '最后任务',
                            minWidth: 90,
                            maxWidth:150,
                            sort: true,
                            align: 'center',
                            templet: function (d) {
                                if(d.LAST_TASK_TIME && d.LAST_TASK_TIME !== ''){
                                    return d.LAST_TASK_TIME.substring(0,10);
                                }
                                return  '';
                            }
                        }
                    ]],
                    done: function (data) {
                        table.on('sort('+tableId+')', function (obj) {
                            $("#searchForm").queryData({
                                id: tableId, jumpOne: true, initSort: obj,
                                data: {sortName: obj.field, sortType: obj.type, "projectStage": stage}
                            })
                        })
                    }
                },
            )
        }

        function loadRiskTable(state,tableId) {
            $("#searchForm").initTable({
                mars: 'ProjectRiskDao.riskList',
                id: tableId,
                height: '#'+tableId+'Card-80',
                limit: 30,
                data: {riskState: state},
                cols: [[
                    {title: '序号', type: 'numbers'},
                    {
                        title: '操作',
                        align: 'center',
                        width: 120,
                        templet: function (row) {
                            return '<a class="btn btn-xs btn-link" href="javascript:;" lay-event="riskDetail">详情</a><a class="btn btn-xs btn-link ml-5" href="javascript:;" lay-event="riskFlow">流程</a>';
                        }
                    }, {
                        field: 'OWNER_NAME',
                        title: '负责人',
                        width: 70,
                        templet: function (row) {
                            return getUserName(row.SOL_OWNER);
                        }
                    }, {
                        field: 'CREATOR',
                        title: '发起人',
                        width: 70,
                        align: 'center',
                        templet: function (row) {
                            return getUserName(row.CREATOR);
                        }
                    },
                    {
                        field: 'RISK_BELONG',
                        title: '风险分类',
                        align: 'center',
                        width: 90
                    }, {
                        field: 'RISK_DESC',
                        title: '风险说明',
                        align: 'left',
                        minWidth: 200,
                        templet: function (row) {
                            return cutText(row.RISK_DESC, 120);
                        }
                    }, {
                        field: 'SOLUTION',
                        title: '风险应对措施',
                        align: 'left',
                        minWidth: 150,
                        templet: function (row) {
                            return cutText(row.SOLUTION, 120);
                        }
                    }, {
                        field: 'CREATE_TIME',
                        title: '录入时间',
                        align: 'center',
                        width: 140,
                    }, {
                        field: 'RISK_LEVEL',
                        title: '风险等级',
                        align: 'center',
                        width: 80,
                        templet: function (row) {
                            return riskLevelLabel(row.RISK_LEVEL);
                        }
                    }, {
                        field: 'RISK_STATE',
                        title: '风险状态',
                        align: 'center',
                        width: 80,
                        templet: function (row) {
                            return riskStateLabel(row.RISK_STATE);
                        }
                    }
                ]], done: function (result) {

                }
            });
        }

        function riskFlow(data) {
            var data = {businessId: data['RISK_ID']};
            popup.openTab({id: 'flowDetail', title: '流程详情', url: '${ctxPath}/web/flow', data: data});
        }

        function riskDetail(data) {
            var isAdmin = 0;
            if (top.isAdmin == '1' || isSuperUser) {
                isAdmin = 1;
            }
            var riskId = data['RISK_ID'];
            var riskState = data['RISK_STATE'];
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                shadeClose: false,
                title: '风险详情',
                offset: 'r',
                area: ['700px', '100%'],
                url: '${ctxPath}/pages/project/risk/risk-detail.jsp',
                title: '风险详情',
                data: {riskId: riskId, riskState: riskState, isAdmin: isAdmin}
            });
        }

        function riskStateLabel(state) {
            var json = {0: '<label class="label label-danger label-outline">进行中</label>', 1: '<label class="label label-success">已完成</label>'};
            var result = json[state] || '';
            if (result) {
                return result;
            } else {
                return '';
            }
        }

        function riskLevelLabel(state) {
            var json = {0: '<label class="label  label-outline label-warning">低</label>', 1: '<label class="label  label-warning">中</label>', 2: '<label class="label label-danger">高</label>'};
            var result = json[state] || '';
            if (result) {
                return result;
            } else {
                return '';
            }
        }

        function loadProjectListTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectStatList',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#projectListTableCard-80',
                    autoSort: false,
                    toolbar:true,
                    id: 'projectListTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 100,
                        },{
                            field: 'PROJECT_PO_NAME',
                            title: '项目经理',
                            width:80
                        },{
                            field: 'PO_NAME',
                            title: '开发负责人',
                            width:100
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            width: 110
                        },{
                            field: 'PERSON_COUNT',
                            title: '参与人数',
                            width: 110,
                            sort: true,
                        },{
                            field: 'WORK_PEOPLE_COUNT',
                            title: '周报人数',
                            width: 110,
                            sort: true,
                        },{
                            field: 'WORKHOUR_DAY',
                            title: '投入人力/天',
                            width: 110,
                            sort: true,
                        },{
                            field: '',
                            title: '人均投入/天',
                            width: 110,
                            templet: function (d) {
                                if(d.WORK_PEOPLE_COUNT == ""|| d.WORK_PEOPLE_COUNT == "0" || d.WORK_PEOPLE_COUNT === 0)return '';
                                return (Number(d.WORKHOUR_DAY)/Number(d.WORK_PEOPLE_COUNT)).toFixed(1);
                            }
                        }, {
                            field: 'CY_DATE',
                            title: '计划初验',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'ZY_DATE',
                            title: '计划终验',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'CY_REAL_DATE',
                            title: '实际初验',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'ZY_REAL_DATE',
                            title: '实际终验',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'BEGIN_DATE',
                            title: '开始时间',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'LAST_TASK_TIME',
                            title: '最近任务',
                            width: 140,
                            align: 'left',
                            sort: true,
                        },
                    ]],
                    done: function (data) {
                    }
                },
            )
        }

        function loadProjectProgressListTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectProgressList',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#projectProgressListTableCard-80',
                    autoSort: false,
                    toolbar: true,
                    id: 'projectProgressListTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed:'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 200,
                            fixed:'left'
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            width: 110
                        }, {
                            field: 'PROJECT_STATE',
                            title: '项目状态',
                            align: 'center',
                            width: 80,
                            templet: function (row) {
                                return getText(row.PROJECT_STATE, 'projectState');
                            }
                        },{
                            field: 'DELAY_TASK',
                            title: '延期任务数',
                            width: 100,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'SUM_DELAY',
                            title: '总延期次数',
                            width: 100,
                            sort: true,
                            align: 'center',
                        },{
                            field: 'MAX_DELAY_DAYS',
                            title: '任务最大延期/天',
                            width: 150,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'AVERAGE_DELAY_DAYS',
                            title: '任务平均延期/天',
                            width: 150,
                            sort: true,
                            align: 'center',
                        },{
                            field: 'CY_DATE',
                            title: '计划初验',
                            width: 90,
                            sort: true,
                        }, {
                            field: 'ZY_DATE',
                            title: '计划终验',
                            width: 90,
                            sort: true,
                        }, {
                            field: 'CY_REAL_DATE',
                            title: '实际初验',
                            width: 90,
                            sort: true,
                        }, {
                            field: 'ZY_REAL_DATE',
                            title: '实际终验',
                            width: 90,
                            sort: true,
                        }, {
                            field: 'BEGIN_DATE',
                            title: '开始时间',
                            width: 90,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'LAST_TASK_TIME',
                            title: '最近任务',
                            width: 140,
                            align: 'left',
                            sort: true,
                        },
                    ]],
                    done: function (data) {
                    }
                },
            )
        }


        function loadStageChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectStage", {"beginDate": beginDate, "endDate": endDate, "timeType": timeType}, function (result) {
                var data2 = result.data;
                var stageName = data2.map(item => item.PROJECT_STAGE);
                var amounts = data2.map(item => item.NUMS);
                loadHorizontalBarChart(stageName, amounts, "stageChart")
            })
        }

        function loadStateChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectState", {"beginDate": beginDate, "endDate": endDate, "timeType": timeType}, function (result) {
                var data2 = result.data;

                const PROJECT_STATE_MAP = {
                    '11': '进度正常',
                    '12': '存在风险',
                    '13': '进度失控',
                    '20': '挂起中',
                    '30': '已完成'
                };
                const echartsData = data2.map(item => {
                    return {
                        name: PROJECT_STATE_MAP[item.PROJECT_STATE] || item.PROJECT_STATE,
                        value: item.NUMS
                    };
                });
                loadPieChart(echartsData, "stateChart1");
            })
        }

        function loadPersonChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectPersonCountStat", {"beginDate": beginDate, "endDate": endDate, "timeType": timeType}, function (result) {
                var data2 = result.data;
                var personCountCategory = Object.keys(data2);
                var amounts = personCountCategory.map(key => data2[key]);
                loadHorizontalBarChart(personCountCategory, amounts, "personChart1")
            })
        }

        function loadWorkPersonChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectWorkPeopleCountStat", {"beginDate": beginDate, "endDate": endDate, "timeType": timeType}, function (result) {
                var data2 = result.data;
                var personCountCategory = Object.keys(data2);
                var amounts = personCountCategory.map(key => data2[key]);
                loadHorizontalBarChart(personCountCategory, amounts, "personChart2")
            })
        }

        var personChart3ProjectIds = [];
        function loadPersonTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectPersonCountList',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#personCountRankTableDiv-50',
                    autoSort: false,
                    id: 'personCountRankTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 100,
                        }, {
                            field: 'PERSON_COUNT',
                            title: '参与人数',
                            minWidth: 100,
                            maxWidth: 200,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'WORK_PEOPLE_COUNT',
                            title: '周报人数',
                            minWidth: 100,
                            maxWidth: 200,
                            sort: true,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {
                        if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                            return;
                        }
                        var data2 = data.data.slice(0, 15);
                        personChart3ProjectIds = data2.map(item => item.PROJECT_ID);
                        var projectName = data2.map(item => subContractName(item.PROJECT_NAME));
                        var nums1 = data2.map(item => item.PERSON_COUNT);
                        var nums2 = data2.map(item => item.WORK_PEOPLE_COUNT);
                        var legend = ['参与人数','周报人数']
                        var personChart3 =  loadTwoBarChart(projectName,nums1,nums2, "personChart3",legend)
                        if (!loadMap["personChart3"]) {
                            personChart3.on('click', function (params) {
                                var index = params.dataIndex;
                                var projectId = personChart3ProjectIds[index];
                                projectDetail(projectId)
                            });
                        }
                        loadMap["personChart3"] = true;
                    }
                },
            )
        }

        var workHourTypeChartProjectIds = [];
        function loadWorkHourTypeTable() {
            $("#searchForm").initTable({
                mars: 'ProjectStatisticDao.workHourTypeRank',
                cellMinWidth: 50,
                limit: 25,
                height: '#workHourTypeTableDiv-50',
                autoSort: false,
                id: 'workHourTypeTable',
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align: 'left'
                    }, {
                        field: 'PROJECT_NAME',
                        title: '项目',
                        event: 'projectDetailByRow',
                        style: 'cursor:pointer;text-decoration: underline;',
                        minWidth: 100,
                    }, {
                        field: 'ENG_HOURS',
                        title: '工程工时/天',
                        width: 110,
                        sort: true,
                        align: 'center',
                        templet: function(d) {
                            return Math.floor(d.ENG_HOURS);
                        }
                    }, {
                        field: 'DEV_HOURS',
                        title: '开发工时/天',
                        width: 110,
                        sort: true,
                        align: 'center',
                        templet: function(d) {
                            return Math.floor(d.DEV_HOURS);
                        }
                    }, {
                        field: 'TOTAL_TECH_HOURS',
                        title: '总技术工时/天',
                        width: 120,
                        sort: true,
                        align: 'center',
                        templet: function(d) {
                            return Math.floor(d.TOTAL_TECH_HOURS);
                        }
                    }, {
                        field: 'TOTAL_HOURS',
                        title: '全部总工时/天',
                        width: 120,
                        sort: true,
                        align: 'center',
                        templet: function(d) {
                            return Math.floor(d.TOTAL_HOURS);
                        }
                    }
                ]],
                done: function(data) {
                    if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 25) {
                        return;
                    }
                    var data2 = data.data;
                    var projectName = data2.map(item => subContractName(item.PROJECT_NAME));
                    workHourTypeChartProjectIds = data2.map(item => item.PROJECT_ID);
                    var engHour = data2.map(item => Math.floor(item.ENG_HOURS));
                    var devHour = data2.map(item => Math.floor(item.DEV_HOURS));
                    var totalTechHour = data2.map(item => Math.floor(item.TOTAL_TECH_HOURS));
                    var totalHour = data2.map(item => Math.floor(item.TOTAL_HOURS));
                    var legend = ['工程工时/天', '开发工时/天', '总技术工时/天', '全部总工时']
                    var options2 = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                        },
                        legend: {
                            data: legend
                        },
                        grid: {
                            left: '3%',
                            right: '2%',
                            bottom: '2%',
                            containLabel: true
                        },
                        barGap: '0%',
                        barCategoryGap: '50%',
                        xAxis: {
                            type: 'category',
                            data: projectName,
                            axisLabel: {
                                rotate: 45,
                                interval: 0
                            }
                        },
                        yAxis: [
                            {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}'
                                }
                            },
                        ],
                        series: [
                            {
                                name: legend[0],
                                type: 'bar',
                                data: engHour,
                                stack: '总量',
                                label: {
                                    show: true,
                                    position: 'inside',
                                    formatter: function (params) {
                                        return params.value > 30 ? params.value : '';
                                    },
                                    textBorderColor: '#5b8ff9',
                                },
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#7ca3fa'},
                                        {offset: 1, color: '#5b8ff9'}
                                    ])
                                }
                            },
                            {
                                name: legend[1],
                                type: 'bar',
                                data: devHour,
                                stack: '总量',
                                label: {
                                    show: true,
                                    position: 'inside',
                                    formatter: function (params) {
                                        return params.value > 30 ? params.value : '';
                                    },
                                    textBorderColor: '#67C23A',
                                },
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#91cc75'},  // 浅绿色
                                        {offset: 1, color: '#67C23A'}   // 深绿色
                                    ])
                                }
                            },
                            {
                                name: legend[2],
                                type: 'line',
                                data: totalTechHour,
                                smooth: false,
                                label: {
                                    show: true,
                                    position: 'inside',
                                    fontWeight: 'bold',
                                    distance: 0
                                },
                                symbolSize: 8,
                                lineStyle: {
                                    width: 3,
                                    color: '#FF9F7F'  // 橙色
                                },
                                itemStyle: {
                                    color: '#FF9F7F'
                                }
                            },
                            {
                                name: legend[3],
                                type: 'line',
                                data: totalHour,
                                smooth: false,
                                label: {
                                    show: true,
                                    position: 'top',
                                    distance: 2,
                                    formatter: function (params) {
                                        return params.value > totalTechHour[params.dataIndex] ? params.value : '';
                                    }
                                },
                                lineStyle: {
                                    width: 2,
                                    color: '#FFB74D'  // 金色
                                },
                                itemStyle: {
                                    color: '#FFB74D'
                                }
                            }
                        ]
                    };

                    var workHourChart = echarts.init(document.getElementById('workHourTypeChart'));
                    workHourChart.setOption(options2);
                    if (!loadMap["workHourTypeChart"]) {
                        workHourChart.on('click', function (params) {
                            if (params.componentType === 'series') {
                                var index = params.dataIndex;
                                var projectId = workHourTypeChartProjectIds[index];
                                projectDetail(projectId);
                            }
                        });
                    }
                    loadMap["workHourTypeChart"] = true;
                }
            });
        }

        function loadDeptFinishTaskTable() {
            $("#searchForm").initTable({
                mars: 'ProjectStatisticDao.finishTaskRankByDept',
                cellMinWidth: 50,
                limit: 20,
                height: '#deptFinishTaskTableDiv-50',
                autoSort: false,
                id: 'deptFinishTaskTable',
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align: 'left'
                    }, {
                        field: 'DEPT_NAME',
                        title: '部门',
                        minWidth: 100,
                        align: 'center',
                    }, {
                        field: 'TASK_NUMS',
                        title: '完成任务数量',
                        minWidth: 100,
                        maxWidth:200,
                        sort: true,
                        align: 'center',
                    }, {
                        field: 'AVG_TASK_NUMS',
                        title: '人均任务数量',
                        minWidth: 100,
                        maxWidth:200,
                        sort: true,
                        align: 'center',
                        templet: function(d) {
                            return Math.floor(Number(d.AVG_TASK_NUMS));
                        }
                    }
                ]],
                done: function(data) {
                    if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                        return;
                    }
                    var data2 = data.data;
                    var deptName = data2.map(item => item.DEPT_NAME);
                    var taskCounts = data2.map(item => item.TASK_NUMS);
                    var avgTaskNums = data2.map(item => Math.floor(Number(item.AVG_TASK_NUMS)));
                    var legend = ['完成任务数量', '人均任务数量'];
                    loadBarAndLineChart(deptName, taskCounts, avgTaskNums, "deptFinishTaskChart", legend,'blueStyle');
                }
            });
        }

        function loadDeptUndoTaskTable() {
            $("#searchForm").initTable({
                mars: 'ProjectStatisticDao.undoTaskRankByDept',
                cellMinWidth: 50,
                limit: 20,
                height: '#deptUndoTaskTableDiv-50',
                autoSort: false,
                id: 'deptUndoTaskTable',
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align: 'left'
                    }, {
                        field: 'DEPT_NAME',
                        title: '部门',
                        minWidth: 100,
                        align: 'center',
                    }, {
                        field: 'TASK_NUMS',
                        title: '待完成任务数量',
                        minWidth: 100,
                        maxWidth:200,
                        sort: true,
                        align: 'center',
                    }, {
                        field: 'AVG_TASK_NUMS',
                        title: '人均待完成',
                        minWidth: 100,
                        maxWidth:200,
                        sort: true,
                        align: 'center',
                        templet: function(d) {
                            return Math.floor(Number(d.AVG_TASK_NUMS));
                        }
                    }
                ]],
                done: function(data) {
                    if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                        return;
                    }
                    var data2 = data.data;
                    var deptName = data2.map(item => item.DEPT_NAME);
                    var taskCounts = data2.map(item => item.TASK_NUMS);
                    var avgTaskNums = data2.map(item => Math.floor(Number(item.AVG_TASK_NUMS)));
                    var legend = ['待完成任务数量', '人均待完成'];
                    loadBarAndLineChart(deptName, taskCounts, avgTaskNums, "deptUndoTaskChart", legend,'yellowStyle');
                }
            });
        }

        function loadDeptProjectTableAndChart(marsMethod,tableId,chartId) {
            $("#searchForm").initTable({
                    mars: marsMethod,
                    cellMinWidth: 50,
                    limit: 20,
                    height: '#'+tableId+'Div-50',
                    autoSort: false,
                    id: tableId,
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'DEPT_NAME',
                            title: '部门',
                            minWidth: 100,
                            align: 'center',
                        }, {
                            field: 'PROJECT_COUNTS',
                            title: '项目总数',
                            minWidth: 100,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'PERSON_COUNT',
                            title: '参与人数',
                            minWidth: 100,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'AVG_PROJECT_COUNTS',
                            title: '人均参与',
                            minWidth: 100,
                            sort: true,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {
                        if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                            return;
                        }
                        var data2 = data.data;
                        data2 = data2.slice(0, 20);
                        var deptName = data2.map(item => item.DEPT_NAME);
                        var nums = data2.map(item => item.PROJECT_COUNTS);
                        var averageNums = data2.map(item => item.AVG_PROJECT_COUNTS);
                        var legend = ['部门参与项目总数','部门人均参与']
                        loadBarAndLineChart(deptName,nums,averageNums, chartId, legend,'blueStyle')
                    }
                },
            )
        }

        function projectDetail(projectId){
            if(projectId){
                projectBoard(projectId);
            }
        }

        var getDate = function (obj, startDate, endDate) {
            var val = obj.value;
            var bdate = new Date();
            var edate = new Date();
            var monthAdjustments = {
                'oneMonth': -1,
                'threeMonth': -3,
                'halfYear': -6,
                'oneYear': -12,
                'twoYear': -24
            };
            if (val in monthAdjustments) {
                bdate.setMonth(bdate.getMonth() + monthAdjustments[val]);
            } else if (val === 'nowYear') {
                bdate.setMonth(0, 1);
                edate.setMonth(11, 31);
            } else if (val === 'lastYear') {
                bdate.setFullYear(bdate.getFullYear() - 1, 0, 1);
                edate.setFullYear(edate.getFullYear() - 1, 11, 31);
            } else if (val === 'nextYear') {
                bdate.setFullYear(bdate.getFullYear() + 1, 0, 1);
                edate.setFullYear(edate.getFullYear() + 1, 11, 31);
            }
            var bdateVal = val && val !== 'all' ? DateUtils.dateFormat(bdate, 'yyyy-MM-dd') : '';
            var edateVal = val && val !== 'all' ? DateUtils.dateFormat(edate, 'yyyy-MM-dd') : '';
            $('[name="' + startDate + '"]').val(bdateVal);
            $('[name="' + endDate + '"]').val(edateVal);
        }

        function toggleFullScreen(divId) {
            var div = document.getElementById(divId);
            if (!div.classList.contains('fullscreen')) {
                // 放大
                div.classList.add('fullscreen');
                var icon = div.querySelector('.layui-icon-screen-full');
                icon.classList.remove('layui-icon-screen-full');
                icon.classList.add('layui-icon-screen-restore');
            } else {
                // 还原
                div.classList.remove('fullscreen');
                var icon = div.querySelector('.layui-icon-screen-restore');
                icon.classList.remove('layui-icon-screen-restore');
                icon.classList.add('layui-icon-screen-full');
            }
        }

        function toggleFullScreenWithChart(cardId,tableDivId,chartId) {
            var div = document.getElementById(cardId);
            var chart = echarts.getInstanceByDom(document.getElementById(chartId));
            if (!div.classList.contains('fullscreen')) {
                // 放大
                div.classList.add('fullscreen');
                var icon = div.querySelector('.layui-icon-screen-full');
                icon.classList.remove('layui-icon-screen-full');
                icon.classList.add('layui-icon-screen-restore');
                chart.setOption({
                    grid: {
                        left: '2%',
                        right: '2%',
                        bottom: '0%',
                        containLabel: true
                    }
                });
                $('#' + tableDivId).show();
            } else {
                // 还原
                $('#' + tableDivId).hide();
                div.classList.remove('fullscreen');
                var icon = div.querySelector('.layui-icon-screen-restore');
                icon.classList.remove('layui-icon-screen-restore');
                icon.classList.add('layui-icon-screen-full');
                chart.setOption({
                    grid: {
                        left: '6%',
                        right: '4%',
                        bottom: '1%',
                        containLabel: true
                    },
                });
            }
            //chart-card动画效果需要时间
            setTimeout(function() {
                if (chart) {
                    chart.resize();
                }
            }, 300);
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
