<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editTaskReturnForm" autocomplete="off">
	     	   <input type="hidden" id="taskId" value="${param.taskId}" name="taskId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
		                <tr>
		                    <td>
		                    	<textarea placeholder="请填写退回原因" style="height: 130px;"  data-rules="required"  maxlength="500" name="reason" class="form-control input-sm"></textarea>
		                    </td>
		                </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="TaskForward.ajaxSubmitForm()"> 确 定  </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
       
	    var TaskForward={};
		TaskForward.ajaxSubmitForm = function(flag) {
			if(form.validate("#editTaskReturnForm")){
				var data = form.getJSONObject("#editTaskReturnForm");
				ajax.remoteCall("${ctxPath}/servlet/task?action=returnTask",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							initFormData();
							popup.layerClose("#editTaskReturnForm");
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>