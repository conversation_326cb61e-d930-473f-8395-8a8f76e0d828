<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>流程发起</title>
	<style>
		.layui-elem-field legend{font-size: 16px;}
		.btn{text-align: left;}
		.layui-icon{font-size: 12px;}
		.layui-field-title{margin-bottom: 30px;}
		
		.layui-card-header{
			display: flex;
			position: relative;
   			padding: .5rem 1rem;
			flex-wrap: wrap;
			align-items: center;
			justify-content: space-between;
		    height: 50px;
		    border-bottom: 1px solid #efefef;
		    background-color: #fafbfc;
		    flex-wrap: nowrap;
		    border-radius: 0;
		}
		.topFlow button{border-left: 2px solid #5fb878!important;}
		
		.layui-card-header input{
			height: 36px!important;
		}
		
		.layui-col-md3 button{
			border: 1px solid #ddd;
			border-left: 2px solid #00abfc;
			background: #fafafa;
			max-width: 200px;
		}
		.layui-col-md3 .root-zr{
			border-left: 2px solid #a9ca27;
			border-color: #a9ca27;
		}
		.legend-zr{
			color: #a9ca27;
		}
		
		.layui-col-md3 .root-yq{
			color: #1e1ced;
			border-left: 2px solid #1e1ced;
		}
		.legend-yq{
			color: #1e1ced;
		}
		
		.layui-col-md3 button:hover {
		    background: #00abfc;
		    color:#fff;
		}
		.layui-col-md3:hover .fa{
		    display: inline-block!important;
		}
		.layui-col-md3 .fa{
			padding-left: 3px;
			margin-top: 6px;
			display: none;
			cursor: pointer;
		    position: absolute;
		}
		@media screen and (max-width: 768px){
			.apply-row{
				padding:0px 10px;
			}
			.layui-card-body{
				padding: 0px;
			}
			.layui-col-space15>*{
				padding: 5px 0px;
			}
			.btn-group-sm>.btn, .btn-sm{
			    padding: 5px 5px;
			}
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
<form id="flowForm" onsubmit="return false;">
	  	<div class="layui-card">
		  <div class="layui-card-header layui-hide-xs">
		     <div class="pull-left">发起流程</div>
		  	 <div class="input-group input-group-sm pull-right" style="width: 200px;">
			 	<input onkeypress="initData();" class="form-control" placeholder="搜索：流程名称" name="flowName">
		 		<span class="input-group-addon" onclick="initData();"><i class="glyphicon glyphicon-search"></i></span>
			 </div>
		  </div>
		  <div class="layui-card-body"  style="min-height: calc(100vh - 50px);">
	      	<div class="layui-row apply-row" style="padding:10px 30px;">
		  		<fieldset class="layui-elem-field layui-field-title">
				  <legend style="font-weight: 700;">智能推荐</legend>
				  <div class="layui-field-box">
				  	  <div class="layui-row layui-col-space15 recommendFlow" data-mars="FlowConfigDao.recommendFlow" data-template="recommend-list"></div>
				  </div>
			    </fieldset>
		  		<fieldset class="layui-elem-field layui-field-title">
				  <legend style="font-weight: 700;">常用置顶</legend>
				  <div class="layui-field-box">
				  	  <div class="layui-row layui-col-space15 topFlow" data-mars="FlowConfigDao.myFlowFavorite" data-template="top-list"></div>
				  </div>
			  </fieldset>
	      	  <div data-mars="FlowConfigDao.myFlowCategoryList" data-template="template-list"></div>
	      	</div>
		  </div>
		</div>
</form>
		
<script id="template-list" type="text/x-jsrender">
			{{for data}}
				{{if P_FLOW_CODE =='0'}}
					<fieldset class="layui-elem-field layui-field-title">
						  <legend class="legend-{{:ROOT_ID}}">{{:FLOW_NAME}}</legend>
						  <div class="layui-field-box">
						  		<div class="layui-row layui-col-space15">
									{{for #parent.parent.data}}
									 {{if #parent.parent.data.FLOW_CODE==P_FLOW_CODE}}
						  			   <div class="layui-col-md3 layui-col-sm6 layui-col-xs6 layui-col-lg2" data-code="{{:FLOW_CODE}}" data-name="{{:FLOW_NAME}}">
										 <button class="btn btn-sm root-{{:ROOT_ID}}" onclick="applyFlow('{{:FLOW_CODE}}');" style="width: 85%;" type="button"> 
											<i class="layui-icon layui-icon-addition"></i>{{:FLOW_NAME}}
										</button>	
											<i class="fa fa-hand-o-up" data-top="{{:FLOW_CODE}}" onclick="flowTop('{{:FLOW_CODE}}')"></i>
						  			   </div>
									 {{/if}}
  									{{/for}}
						  		</div>
						  </div>
					</fieldset>
				{{/if}}
			{{/for}}
</script>
<script id="top-list" type="text/x-jsrender">
	{{for data}}
		<div class="layui-col-md3 layui-col-sm6 layui-col-xs6 layui-col-lg2" data-code="{{:FLOW_CODE}}" data-name="{{:FLOW_NAME}}">
			<button class="btn btn-sm" onclick="applyFlow('{{:FLOW_CODE}}');" style="width: 85%;" type="button"> 
				<i class="layui-icon layui-icon-addition"></i>{{:FLOW_NAME}}
			</button>	
			<i class="fa fa-close" data-top="{{:FLOW_CODE}}" onclick="delTop('{{:ID}}')"></i>
		</div>
	{{/for}}
</script>
<script id="recommend-list" type="text/x-jsrender">
	{{for data}}
		<div class="layui-col-md3 layui-col-sm6 layui-col-xs6 layui-col-lg2" data-code="{{:FLOW_CODE}}" data-name="{{:FLOW_NAME}}">
			<button class="btn btn-sm" onclick="applyFlow('{{:FLOW_CODE}}');" style="width: 85%;" type="button"> 
				<i class="layui-icon layui-icon-addition"></i>{{:FLOW_NAME}}
			</button>	
		</div>
	{{/for}}
</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   	 $(function(){
   		initData();
   	 });
   	 
   	 function initData(){
   		 $('#flowForm').render({success:function(){
   			 $('.layui-elem-field').each(function(){
   				var len =  $(this).find('.layui-col-md3').length;
   				if(len==0){
   					$(this).remove();
   				}
   			 });
   		 }});
   	 }
		
   	 function applyFlow(id){
   		ajax.remoteCall("${ctxPath}/web/flow/getFlow",{flowCode:id},function(result) { 
			if(result.state == 1){
				var data = result.data;
				var title = data.flowName;
				//popup.closeTab({callback:function(){
					if(self==top){
						location.href = "/yq-work/web/flow?handle=add&flowCode="+id;				
					}else{
						popup.openTab({id:id,url:'/yq-work/web/flow?flowCode='+id,title:title,data:{handle:'add'}});
					}
				//}});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
   	 }
   	 
   	String.prototype.replaceAll = function(s1, s2) {
   		return this.replace(new RegExp(s1, "gm"), s2);
   	}

   	 
   	 function delTop(id){
   		ajax.remoteCall("${ctxPath}/web/flow/config/delFavorite",{id:id},function(result) { 
			if(result.state == 1){
		   		layer.msg('移除成功',{icon:1,time:800},function(){
		   			initData();
		   		});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
   	 }
   	 
   	 function flowTop(flowCode){
   		ajax.remoteCall("${ctxPath}/web/flow/config/addFavorite",{flowCode:flowCode},function(result) { 
			if(result.state == 1){
		   		layer.msg('置顶成功',{icon:1,time:800},function(){
		   			initData();
		   		});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
   	 }
   	 
   	 
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>