<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购详情</title>
	<style>
		.form-horizontal{width: 100%;}
		.layui-table-cell a{font-size: 12px;color: #3E84E9;cursor: pointer;}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 14px;}
		.layui-timeline-item:hover a{opacity:1;}
		.layui-tab-card>.layui-tab-title{background-color: #fff;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		.layui-timeline-content p{font-size: 13px;color: #666;}
		.layui-timeline-title{margin-bottom: 2px;}
		.layui-timeline-item{margin-bottom: 2px;}
		.layui-text h3{font-size: 16px;}
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.gray-bg{background-color: #e8edf7;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="SupplierDetailForm">
			    <input type="hidden" value="${param.supplierId}" id="supplierId" name="supplierId"/>
 	  	        <input type="hidden" value="${param.supplierId}" name="pFkId"/>
 	  	        <input type="hidden" value="${param.custId}" name="custId"/>
				<div style="padding: 5px" class="ibox-content" data-mars="SupplierDao.record" data-mars-prefix="supplier.">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md8">
					     <i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 18px;" name='supplier.NAME'></span>
					  </div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm">
									<button class="btn btn-default btn-xs" onclick="editSupplier();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding:10px 15px;">
					  <div class="layui-col-md2">
					  	<span class="h-title">编号</span>
					  	<span class="h-value" name="supplier.SUPPLIER_NO"></span>
					  </div>
					  <div class="layui-col-md4">
					  	<span class="h-title">地址</span>
					  	<span class="h-value" name="supplier.ADDRESS"></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">更新时间</span>
					  	<span class="h-value" name="supplier.UPDATE_TIME"></span>
					  </div>
					</div>
				</div>
				<div class="layui-row">
					<div class="layui-col-md12">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">基本信息</li>
						    <li>供货产品(<span id="productCount">0</span>)</li>
						    <li>采购记录(<span id="orderCount">0</span>)</li>
						    <li>付款(<span id="paymentCount">0</span>)</li>
						    <li>附件(<span id="fileCount">0</span>)</li>
						  </ul>
						  <div class="layui-tab-content" style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
						    <div class="layui-tab-item layui-show">
						    	<jsp:include page="include/base-info.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/product.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/order.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/payment.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/file-list.jsp"/>
						    </div>
						  </div>
					</div>
				  </div>
				</div>
		</form>
		<form  id="orderDetailFileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="orderDetailLocalfile" onchange="orderUploadFile()"/>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   
		jQuery.namespace("OrderDetail");
	    
		OrderDetail.supplierId='${param.supplierId}';
		
		var supplierId = '${param.supplierId}';
		
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
				var form = layui.form;
				form.render();
			});
			$("#SupplierDetailForm").render({success:function(result){
				var newData = '${param.newData}';
				if(newData=='1'){
					$('.layui-tab-title li').last().click();
					layer.confirm('请上传供应商相关资质文件',{offset:'50px',icon:3},function(index){
						layer.close(index);
						$('.uploadFileBtn').click();
					});
				}
			}});  
			
		});
		
		
		function editSupplier(){
			popup.layerShow({id:'editSupplier',scrollbar:false,title:'修改',area:['600px','500px'],url:'${ctxPath}/pages/erp/supplier/supplier-edit.jsp',data:{supplierId:supplierId}});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>