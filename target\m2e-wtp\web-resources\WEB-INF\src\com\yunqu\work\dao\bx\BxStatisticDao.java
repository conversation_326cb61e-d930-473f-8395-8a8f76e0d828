package com.yunqu.work.dao.bx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.base.FlowConstants;
import java.text.ParseException;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;

@WebObject(name = "BxStatisticDao")
public class BxStatisticDao extends AppDaoContext {

    @WebControl(name = "bxConsoleStat", type = Types.RECORD)
    public JSONObject bxConsoleStat() {
        String timeType = param.getString("timeType");

        EasySQL sql = new EasySQL("select ");
        int curYear = EasyCalendar.newInstance().getYear();
        int lastYear = curYear - 1;
        String todayDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
        String lastYearToday = DateUtils.getLastYearTodayDate();
        String yearBeforeLastYearToday = yearBeforeLastYearTodayDate();

        sql.appendLike(curYear, "ROUND(SUM(CASE WHEN "+timeType+" like ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.appendLike(lastYear, "ROUND(SUM(CASE WHEN "+timeType+" like ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR,");
        sql.appendLike(DateUtils.getFullMonthStr(), "ROUND(SUM(CASE WHEN "+timeType+" like ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_MONTH,");
        sql.appendLike(DateUtils.getLastMonthStr(), "ROUND(SUM(CASE WHEN "+timeType+" like ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_MONTH,");
        sql.appendLike(getLastYearLastMonthStr(), "ROUND(SUM(CASE WHEN "+timeType+" like ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_THIS_MONTH,");
        sql.appendLike(getLastYearThisMonthStr(), "ROUND(SUM(CASE WHEN "+timeType+" like ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_LAST_MONTH,");

        sql.append(todayDate, "ROUND(SUM(CASE WHEN "+timeType+" <= ?");
        sql.append(lastYearToday, "AND "+timeType+" > ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_IN_A_YEAR,");
        sql.append(lastYearToday, "ROUND(SUM(CASE WHEN "+timeType+" <= ?");
        sql.append(yearBeforeLastYearToday, "AND "+timeType+" > ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_BEFORE_YEAR,");

        int curQuarter = getQuarterFromMonth(EasyCalendar.newInstance().getMonth());
        int lastQuarter = curQuarter == 1 ? 4 : curQuarter - 1;

        sql.append(DateUtils.getQuarterStart(), "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(DateUtils.getQuarterEnd(), "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_QUARTER,");
        if (lastQuarter == 4) {
            sql.append(DateUtils.getQuarterStart(lastYear, lastQuarter), "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
            sql.append(DateUtils.getQuarterEnd(lastYear, lastQuarter), "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_QUARTER,");
        } else {
            sql.append(DateUtils.getQuarterStart(curYear, lastQuarter), "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
            sql.append(DateUtils.getQuarterEnd(curYear, lastQuarter), "AND "+timeType+" <=  ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_QUARTER,");
        }

        sql.append(DateUtils.getQuarterStart(lastYear, curQuarter), "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(DateUtils.getQuarterEnd(lastYear, curQuarter), "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_THIS_QUARTER,");

        if (lastQuarter == 4) {
            sql.append(DateUtils.getQuarterStart(lastYear - 1, lastQuarter), "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
            sql.append(DateUtils.getQuarterEnd(lastYear - 1, lastQuarter), "AND "+timeType+" <=  ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_LAST_QUARTER,");
        } else {
            sql.append(DateUtils.getQuarterStart(lastYear, lastQuarter), "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
            sql.append(DateUtils.getQuarterEnd(lastYear, lastQuarter), "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_LAST_QUARTER,");
        }

        sql.appendLike(EasyCalendar.newInstance().getYear(), " COUNT(DISTINCT CASE WHEN "+timeType+" LIKE ? THEN t1.business_id END) AS COUNT_THIS_YEAR,");
        sql.appendLike(EasyCalendar.newInstance().getYear() - 1, "COUNT(DISTINCT CASE WHEN "+timeType+" LIKE ? THEN t1.business_id END) AS COUNT_LAST_YEAR,");

        sql.append(todayDate, "COUNT(DISTINCT CASE WHEN "+timeType+" <= ?   ");
        sql.append(lastYearToday, "AND "+timeType+" > ? THEN t1.business_id END) AS COUNT_IN_A_YEAR,");
        sql.append(lastYearToday, "COUNT(DISTINCT CASE WHEN "+timeType+" <= ?   ");
        sql.append(yearBeforeLastYearToday, "AND "+timeType+" > ? THEN t1.business_id END) AS COUNT_BEFORE_YEAR");

        sql.append("from yq_flow_bx_item t1");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
            sql.append("and t3.recorded = 1");
        }else {
            sql.append("INNER JOIN yq_flow_apply t2 on  t1.business_id = t2.apply_id where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        }
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "bxConsoleStat2", type = Types.RECORD)
    public JSONObject bxConsoleStat2() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        String beginDate = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01-01":beginMonth + "-01";
        String endDate = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12-31":endMonth + "-31";

        EasySQL sql = new EasySQL("select ");
        sql.append(beginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(endDate, "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS AMOUNT_IN_PERIOD,");
        sql.append(beginDate, "COUNT(DISTINCT CASE WHEN "+timeType+" >= ?");
        sql.append(endDate, "AND "+timeType+" <= ? THEN t1.business_id END) AS COUNT_IN_PERIOD,");
        sql.append(beginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(endDate, "AND "+timeType+" <= ? AND fee_type like '差旅费%' THEN R_AMOUNT ELSE 0 END), 2) AS TRAVEL_FEE_IN_PERIOD,");
        sql.append(beginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(endDate, "AND "+timeType+" <= ? AND fee_type = '业务招待费' THEN R_AMOUNT ELSE 0 END), 2) AS BUSINESS_FEE_IN_PERIOD");

        sql.append("from yq_flow_bx_item t1");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
            sql.append("and t3.recorded = 1");
        }else {
            sql.append("INNER JOIN yq_flow_apply t2 on  t1.business_id = t2.apply_id where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        }
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "bxDeptTypeAmountMonthly", type = Types.LIST)
    public JSONObject bxDeptTypeAmountMonthly() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        beginMonth = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01":beginMonth;
        endMonth = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12":endMonth;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar beginCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();
        try {
            beginCal.setTime(sdf.parse(beginMonth));
            endCal.setTime(sdf.parse(endMonth));
        } catch (ParseException e) {
            return EasyResult.fail("日期格式错误");
        }
        Calendar calendar =(Calendar) beginCal.clone();

        int monthPeriod = (endCal.get(Calendar.YEAR) - beginCal.get(Calendar.YEAR)) * 12 + endCal.get(Calendar.MONTH) - beginCal.get(Calendar.MONTH) + 1;

        String[] deptType = {"工程研发部门", "销售部门", "管理部门", "sum"};
        JSONObject result = new JSONObject();
        for (String dept : deptType) {
            EasySQL sql = new EasySQL("select ");
            for (int j = 1; j <= monthPeriod; j++) {
                String month = sdf.format(calendar.getTime());
                String monthName = month.replace("-", "_");
                sql.appendLike(month, "ROUND(SUM(CASE WHEN "+timeType+" like ? THEN R_AMOUNT ELSE 0 END),2)");
                if (j == monthPeriod) {
                    sql.append("AS " + monthName);
                } else {
                    sql.append("AS " + monthName + ",");
                }
                calendar.add(Calendar.MONTH, 1);
            }
            sql.append("from yq_flow_bx_item t1");
            if("recorded_date".equals(timeType)){
                sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
                sql.append("and t3.recorded = 1");
            }else {
                sql.append("INNER JOIN yq_flow_apply t2 on  t1.business_id = t2.apply_id where 1=1");
                sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
            }
            if (!dept.equals("sum")) {
                sql.append(dept, "and t1.dept_type = ?");
            }
            JSONObject resultTemp = queryForRecord(sql.getSQL(), sql.getParams());
            JSONObject data = resultTemp.getJSONObject("data");
            result.put(dept, data);
            calendar.setTimeInMillis(beginCal.getTimeInMillis());
        }
        return result;
    }

    /**
     * 搜索可自选统计的维度和报销日期
     */
    @WebControl(name = "searchAmountRank", type = Types.LIST)
    public JSONObject searchAmountRank() {
        String timeType = param.getString("timeType2");
        String field = param.getString("searchField");
        String startDate = param.getString("startDate2");
        String endDate = param.getString("endDate2");
        if (StringUtils.isAnyBlank(field,startDate,endDate)) {
            return EasyResult.fail("请填写自定义统计的必填项：数据时间、统计指标！");
        }

        EasySQL sql = getEasySQL("SELECT");
        if (field.equalsIgnoreCase("APPLY_NAME")) {
            sql.append(field + ",");
        } else if (field.equalsIgnoreCase("FEE_TYPE")) {
            sql.append("CASE WHEN FEE_TYPE like '差旅费%' THEN '差旅费' ELSE FEE_TYPE END as FEE_TYPE,");
        } else if (field.equalsIgnoreCase("TRAVEL_FEE")) {
            sql.append("FEE_TYPE as TRAVEL_FEE,");
        } else {
            sql.append(" t1." + field + ",");
        }
        sql.append(startDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(endDate, "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
            sql.append("and t3.recorded = 1");
        }else {
            sql.append("where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        }
        sql.append(param.getString("bxBy"),"and t2.apply_by = ?");
        sql.append(param.getString("contractId"), "and t1.contract_id = ?");
        sql.appendLike(param.getString("deptName"),"and t1.dept_name like ?");
        sql.appendLike(param.getString("feeType"),"and t1.fee_type like ?");

        if (field.equalsIgnoreCase("APPLY_NAME")) {
            sql.append("GROUP BY " + field);
        } else if (field.equalsIgnoreCase("FEE_TYPE")) {
            sql.append("GROUP BY CASE WHEN FEE_TYPE LIKE '差旅费%' THEN '差旅费' ELSE FEE_TYPE END");
        } else if (field.equalsIgnoreCase("TRAVEL_FEE")) {
            sql.append("and FEE_TYPE LIKE '差旅费%' ");
            sql.append("GROUP BY FEE_TYPE");
        } else {
            sql.append("GROUP BY t1." + field);
        }
        setOrderBy(sql, " order by TOTAL_AMOUNT desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "deptNameAmountRank", type = Types.LIST)
    public JSONObject deptNameAmountRank() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        String beginDate1 = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01-01":beginMonth + "-01";
        String endDate1 = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12-31":endMonth + "-31";

        String lastYearBeginDate = getLastYearDate(beginDate1);
        String lastYearEndDate = getLastYearDate(endDate1);

        EasySQL sql = getEasySQL("SELECT t1.DEPT_NAME,");
        sql.append(beginDate1," COUNT(DISTINCT CASE WHEN "+timeType+" >= ? ");
        sql.append(endDate1, "and "+timeType+" <= ? THEN t2.apply_by END) AS PEOPLE_COUNT,");
        sql.append(beginDate1, "ROUND(SUM(CASE WHEN "+timeType+" >= ? ");
        sql.append(endDate1, "and "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(lastYearBeginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ? ");
        sql.append(lastYearEndDate, "and "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR,");

        sql.append(beginDate1," ROUND( CASE WHEN COUNT(DISTINCT CASE WHEN "+timeType+" >= ? ");
        sql.append(endDate1, "and "+timeType+" <= ? THEN t2.apply_by END) > 0");
        sql.append(beginDate1,"THEN SUM(CASE WHEN "+timeType+" >= ? ");
        sql.append(endDate1, "and "+timeType+" <= ? THEN r_amount ELSE 0 END) / ");
        sql.append(beginDate1,"COUNT(DISTINCT CASE WHEN "+timeType+" >= ? ");
        sql.append(endDate1, "and "+timeType+" <= ? THEN t2.apply_by END) ELSE 0 END ,2 ) AS AVERAGE_THIS_YEAR");

        sql.append("from yq_flow_bx_item t1 INNER JOIN yq_flow_apply t2 on  t1.business_id = t2.apply_id");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
            sql.append("and t3.recorded = 1");
        }else {
            sql.append("where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        }
        sql.append("GROUP BY t1.DEPT_NAME");
        setOrderBy(sql, "order by TOTAL_AMOUNT_THIS_YEAR DESC,TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "deptNameAverageRank", type = Types.LIST)
    public JSONObject deptNameAverageRank() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        String beginDate = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01-01":beginMonth + "-01";
        String endDate = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12-31":endMonth + "-31";

        EasySQL sql = getEasySQL("SELECT t1.DEPT_NAME,");
        sql.append(beginDate," ROUND( CASE WHEN COUNT(DISTINCT CASE WHEN "+timeType+" >= ? ");
        sql.append(endDate, "and "+timeType+" <= ? THEN t2.apply_by END) > 0");
        sql.append(beginDate,"THEN SUM(CASE WHEN "+timeType+" >= ? ");
        sql.append(endDate, "and "+timeType+" <= ? THEN r_amount ELSE 0 END) / ");
        sql.append(beginDate,"COUNT(DISTINCT CASE WHEN "+timeType+" >= ? ");
        sql.append(endDate, "and "+timeType+" <= ? THEN t2.apply_by END) ELSE 0 END ,2 ) AS PERSON_AVERAGE");
        sql.append("from yq_flow_bx_item t1 INNER JOIN yq_flow_apply t2 on  t1.business_id = t2.apply_id");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
            sql.append("and t3.recorded = 1");
        }else {
            sql.append("where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        }
        sql.append("GROUP BY t1.DEPT_NAME");
        sql.append(" order by PERSON_AVERAGE desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }



    @WebControl(name = "staffAmountRank", type = Types.LIST)
    public JSONObject staffAmountRank() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        String beginDate = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01-01":beginMonth+"-01";
        String endDate = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12-31":endMonth+"-31";
        String lastYearBeginDate = getLastYearDate(beginDate);
        String lastYearEndDate = getLastYearDate(endDate);

        EasySQL sql = getEasySQL("SELECT APPLY_NAME,APPLY_BY,");
        sql.append(beginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(endDate, "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(lastYearBeginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(lastYearEndDate, "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id");
            sql.append("where 1=1 and t3.recorded = 1");
        }else {
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "where 1=1 and t2.apply_state = ?");
        }

        sql.append("GROUP BY apply_name,APPLY_BY");
        sql.append("HAVING TOTAL_AMOUNT_THIS_YEAR > 0 OR TOTAL_AMOUNT_LAST_YEAR > 0");
        setOrderBy(sql, "order by TOTAL_AMOUNT_THIS_YEAR DESC,TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    //单独排行页
    @WebControl(name = "staffAmountList", type = Types.LIST)
    public JSONObject staffAmountList() {
        String timeType = param.getString("timeType");
        String beginDate = StringUtils.isBlank(param.getString("beginDate"))?EasyCalendar.newInstance().getYear()+"-01-01":param.getString("beginDate");
        String endDate = StringUtils.isBlank(param.getString("endDate"))?EasyCalendar.newInstance().getYear()+"-12-31":param.getString("endDate");
        String lastYearBeginDate = getLastYearDate(beginDate);
        String lastYearEndDate = getLastYearDate(endDate);

        EasySQL sql = getEasySQL("SELECT APPLY_NAME,APPLY_BY,t4.DEPTS,");
        sql.append(beginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(endDate, "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(lastYearBeginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(lastYearEndDate, "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id");
        sql.append("INNER JOIN " + Constants.DS_MAIN_NAME + ".easi_user t4 on t4.USER_ID = t2.APPLY_BY");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id");
            sql.append("where 1=1 and t3.recorded = 1");
        }else {
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "where 1=1 and t2.apply_state = ?");
        }
        sql.appendLike(param.getString("staffDeptName"),"and t4.depts like ?");
        sql.appendLike(param.getString("contractName"),"and t1.contract_name like ?");
        sql.appendLike(param.getString("feeType"),"and t1.fee_type like ?");
        sql.append(param.getString("bxBy"),"and t2.apply_by = ?");

        sql.append("GROUP BY apply_name,APPLY_BY");
        sql.append("HAVING TOTAL_AMOUNT_THIS_YEAR > 0 OR TOTAL_AMOUNT_LAST_YEAR > 0");
        String compareType = param.getString("compareType"); // 新增：同比升降筛选
        if ("up".equals(compareType)) {
            sql.append("AND TOTAL_AMOUNT_THIS_YEAR > TOTAL_AMOUNT_LAST_YEAR");
        } else if ("down".equals(compareType)) {
            sql.append("AND TOTAL_AMOUNT_THIS_YEAR < TOTAL_AMOUNT_LAST_YEAR");
        }

        setOrderBy(sql, "order by TOTAL_AMOUNT_THIS_YEAR desc,TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "feeTypeAmountRank", type = Types.LIST)
    public JSONObject feeTypeAmountRank() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        String beginDate = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01-01":beginMonth+"-01";
        String endDate = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12-31":endMonth+"-31";
        String lastYearBeginDate = getLastYearDate(beginDate);
        String lastYearEndDate = getLastYearDate(endDate);

        EasySQL sql = getEasySQL("SELECT CASE WHEN FEE_TYPE like '差旅费%' THEN '差旅费' ELSE FEE_TYPE END as FEE_TYPE,");
        sql.append(beginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(endDate, "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(lastYearBeginDate, "ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(lastYearEndDate, "AND "+timeType+" <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append("FROM yq_flow_bx_item t1");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
            sql.append("and t3.recorded = 1");
        }else {
            sql.append("INNER JOIN yq_flow_apply t2 on  t1.business_id = t2.apply_id where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        }
        sql.append("GROUP BY CASE WHEN FEE_TYPE LIKE '差旅费%' THEN '差旅费' ELSE FEE_TYPE END");
        setOrderBy(sql, "order by TOTAL_AMOUNT_THIS_YEAR DESC,TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "contractNameAmountRank", type = Types.LIST)
    public JSONObject contractNameAmountRank() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        String beginDate = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01-01":beginMonth + "-01";
        String endDate = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12-31":endMonth + "-31";
        String lastYearBeginDate = getLastYearDate(beginDate);
        String lastYearEndDate = getLastYearDate(endDate);

        EasySQL sql = getEasySQL("SELECT CONTRACT_SIMPILE_NAME,t2.AMOUNT,t1.CONTRACT_ID,ROUND(SUM(r_amount)/t2.AMOUNT * 100 ,2) as ratio,");
        sql.append(beginDate,"ROUND(SUM(CASE WHEN "+timeType+" >= ?  ");
        sql.append(endDate,"and "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(lastYearBeginDate,"ROUND(SUM(CASE WHEN "+timeType+" >= ?  ");
        sql.append(lastYearEndDate,"and "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR,");
        sql.append("ROUND(SUM(r_amount), 2) AS SUM_BX");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_project_contract t2 on t1.contract_id = t2.CONTRACT_ID ");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
            sql.append("and t3.recorded = 1");
        }else {
            sql.append("INNER JOIN yq_flow_apply t3 on t1.business_id = t3.apply_id where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t3.apply_state = ?");
        }
        sql.append("and t1.CONTRACT_ID!='9999999999999' and t1.CONTRACT_ID != '0' ");
        sql.append("GROUP BY CONTRACT_SIMPILE_NAME,t1.CONTRACT_ID,t2.AMOUNT");
        setOrderBy(sql, "order by TOTAL_AMOUNT_THIS_YEAR DESC,TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "feeTypeRankByContract", type = Types.LIST)
    public JSONObject feeTypeRankByContract() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        String beginDate = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01-01":beginMonth + "-01";
        String endDate = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12-31":endMonth + "-31";
        EasySQL sql = getEasySQL("SELECT FEE_TYPE,t2.AMOUNT,");
        sql.append(beginDate,"ROUND(SUM(CASE WHEN "+timeType+" >= ? ");
        sql.append(endDate,"and "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS YEAR_AMOUNT,");
        sql.append("ROUND(SUM( r_amount), 2) AS TOTAL_AMOUNT");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_project_contract t2 on t1.contract_id = t2.CONTRACT_ID");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
            sql.append("and t3.recorded = 1");
        }else {
            sql.append("INNER JOIN yq_flow_apply t3 on t1.business_id = t3.apply_id where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t3.apply_state = ?");
        }
        sql.append(param.getString("contractId"), "and t1.CONTRACT_ID = ?");
        sql.append("GROUP BY FEE_TYPE ");
        setOrderBy(sql, "order by YEAR_AMOUNT DESC,TOTAL_AMOUNT desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "staffRankByContract", type = Types.LIST)
    public JSONObject staffRankByContract() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        String startDate = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01-01":beginMonth + "-01";
        String endDate = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12-31":endMonth + "-31";
        EasySQL sql = getEasySQL("SELECT APPLY_NAME,APPLY_BY,");
        sql.append("ROUND(SUM( r_amount), 2) AS TOTAL_AMOUNT,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN "+timeType+" >= ?");
        sql.append(endDate,"AND "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS TIME_AMOUNT");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id");
            sql.append("where 1=1 and t3.recorded = 1");
        }else {
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "where 1=1 and t2.apply_state = ?");
        }
        sql.append("and FEE_TYPE LIKE '差旅费%'");
        sql.append(param.getString("contractId"), "and t1.CONTRACT_ID = ?");
        sql.append("GROUP BY APPLY_NAME ,APPLY_BY");
        setOrderBy(sql, "order by TIME_AMOUNT DESC,TOTAL_AMOUNT desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "travelFeeRankByContract", type = Types.LIST)
    public JSONObject travelFeeRankByContract() {
        String timeType = param.getString("timeType");
        String beginMonth = param.getString("beginMonth");
        String endMonth = param.getString("endMonth");
        String startDate = StringUtils.isBlank(beginMonth)?EasyCalendar.newInstance().getYear()+"-01-01":beginMonth + "-01";
        String endDate = StringUtils.isBlank(endMonth)?EasyCalendar.newInstance().getYear()+"-12-31":endMonth + "-31";

        EasySQL sql = getEasySQL("SELECT CONTRACT_SIMPILE_NAME,t2.AMOUNT,t1.CONTRACT_ID,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-机票费' AND "+timeType+" >= ?");
        sql.append(endDate,"AND "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS FEE1,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-车船费' AND "+timeType+" >= ?");
        sql.append(endDate,"AND "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS FEE2,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-市内交通费' AND "+timeType+" >= ?");
        sql.append(endDate,"AND "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS FEE3,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-住宿费' AND "+timeType+" >= ?");
        sql.append(endDate,"AND "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS FEE4,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-出差补助' AND "+timeType+" >= ?");
        sql.append(endDate,"AND "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS FEE5,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type like '差旅费%' AND "+timeType+" >= ?");
        sql.append(endDate,"AND "+timeType+" <= ? THEN r_amount ELSE 0 END), 2) AS SUM_FEE,");
        sql.append("ROUND(SUM(CASE WHEN fee_type like '差旅费%' THEN r_amount ELSE 0 END), 2) AS SUM_FEE_ALL_TIME,");
        sql.append("ROUND(SUM(CASE WHEN fee_type like '差旅费%' THEN r_amount ELSE 0 END)/t2.AMOUNT * 100 ,2) as RATIO");

        sql.append("FROM yq_flow_bx_item t1 inner join yq_project_contract t2 on t1.contract_id = t2.CONTRACT_ID ");
        if("recorded_date".equals(timeType)){
            sql.append("INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
            sql.append("and t3.recorded = 1");
        }else {
            sql.append("INNER JOIN yq_flow_apply t3 on t1.business_id = t3.apply_id where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t3.apply_state = ?");
        }
        sql.append("and t1.CONTRACT_ID!='9999999999999' and t1.CONTRACT_ID != '0' ");
        sql.append("GROUP BY CONTRACT_SIMPILE_NAME,t1.CONTRACT_ID,t2.AMOUNT");
        setOrderBy(sql, " order by SUM_FEE DESC,SUM_FEE_ALL_TIME desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    private String getLastYearDate(String dateString) {
        int year = Integer.parseInt(dateString.substring(0, 4));
        int lastYear = year - 1;
        return lastYear + dateString.substring(4);
    }

    private String getLastYearLastMonthStr() {
        StringBuffer buf = new StringBuffer("");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.add(Calendar.YEAR, -1);
        buf.append(calendar.get(Calendar.YEAR));
        buf.append("-");
        buf.append(calendar.get(Calendar.MONTH) + 1 > 9 ? String.valueOf(calendar.get(Calendar.MONTH) + 1) : "0" + (calendar.get(Calendar.MONTH) + 1));
        return buf.toString();
    }

    private String getLastYearThisMonthStr() {
        StringBuffer buf = new StringBuffer("");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        buf.append(calendar.get(Calendar.YEAR));
        buf.append("-");
        buf.append(calendar.get(Calendar.MONTH) + 1 > 9 ? String.valueOf(calendar.get(Calendar.MONTH) + 1) : "0" + (calendar.get(Calendar.MONTH) + 1));
        return buf.toString();
    }

    private int getQuarterFromMonth(int month) {
        return (month - 1) / 3 + 1;
    }

    private String yearBeforeLastYearTodayDate(){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -2);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }

    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by ").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }

}
