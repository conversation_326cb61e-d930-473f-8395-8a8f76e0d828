package com.yunqu.work.base;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultRecord;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.BxService;
import com.yunqu.work.service.CommonService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.FlowUtils;
import com.yunqu.work.utils.WebSocketUtils;

import eu.bitwalker.useragentutils.DeviceType;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
public abstract class BaseFlowServlet extends AppBaseServlet{ 
	private static final long serialVersionUID = 1L;

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME+"-flow";
	}
	
	protected synchronized String getBxLoanNo(String flowCode) {
		if(FlowUtils.isZrFlow(flowCode)) {
			return BxService.getService().getZrBxNo();
		}else {
			return BxService.getService().getYqBxNo();
		}
	}
	
	protected MessageModel getMessageModel(String businessId,String receiver,String msgLabel,String desc) {
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		return getMessageModel(apply, receiver, msgLabel, desc);
	}
	
	protected MessageModel getMessageModel(FlowApplyModel apply,String receiver,String msgLabel,String desc) {
		MessageModel msgModel = new MessageModel();
		msgModel.setFkId(apply.getApplyId());
		msgModel.setTitle(apply.getApplyTitle());
		msgModel.setTypeName(apply.getFlowCode());
		msgModel.setSender(getUserId());
		msgModel.setSendName(getUserName());
		if(desc==null) {
			msgModel.setDesc(apply.getApplyRemark());
		}else {
			msgModel.setDesc(desc);
		}
		msgModel.setWxData(apply.getFlowName(),apply.getApplyName(),apply.getApplyTime(),"待审批");
		if(msgLabel==null) {
			msgModel.setMsgLabel("待审批");
		}else {
			msgModel.setMsgLabel(msgLabel);
			msgModel.setData4(msgLabel);
		}
		msgModel.setData4("操作人："+getUserName()+"\n"+msgModel.getData4());
		if(receiver==null) {
			msgModel.setReceiver(apply.getApplyBy());
		}else {
			msgModel.setReceiver(receiver);
		}
		msgModel.setData((JSONObject)JSONObject.toJSON(apply));
		return msgModel;
	}
	
	protected void sendAllMsg(MessageModel msgModel) {
		sendAllMsg(msgModel,true);
	}
	
	protected void sendAllMsg(MessageModel msgModel,boolean sendEmail) {
		if(msgModel==null) {
			return;
		}
		WebSocketUtils.sendMessage(new MsgModel(msgModel.getReceiver(),"流程审批", msgModel.getTitle()));
		WxMsgService.getService().sendFlowTodoCheck(msgModel);
		MessageService.getService().sendMsg(msgModel);
		if(sendEmail) {
			msgModel.setFileList(CommonService.getService().getFileList(msgModel.getFkId()));
			EmailService.getService().sendFlowNoticeEmail(msgModel);
		}
	}
	
	
	protected MessageModel sendToApplyMsgModel(String businessId,int checkResult) {
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		FlowModel flow = ApproveService.getService().getFlow(apply.getFlowCode());
		MessageModel msgModel = new MessageModel();
		msgModel.setTypeName(apply.getFlowCode());
		msgModel.setSender(getUserId());
		msgModel.setReceiver(apply.getApplyBy());
		msgModel.setSendName(getUserName());
		msgModel.setFkId(businessId);
		msgModel.setTitle(apply.getApplyTitle());
		String resultDesc = checkResult==1?"审批通过。":"审批退回";
		msgModel.setMsgLabel(getUserName()+"："+resultDesc);
		msgModel.setData1("您申请的"+flow.getFlowName()+",单据编号"+apply.getApplyNo()+"已被"+getUserName()+resultDesc);
		msgModel.setData2(getUserName()+":"+resultDesc);
		msgModel.setData3(EasyDate.getCurrentDateString());
		msgModel.setDesc("更多信息,请登录云趣工作台查看,感谢您的使用。");
		
		WxMsgService.getService().sendFlowCheckOk(msgModel);
		
		msgModel.setDesc(msgModel.getData1());
//		EmailService.getService().sendFlowNoticeEmail(msgModel);
		
		WebSocketUtils.sendMessage(new MsgModel(msgModel.getReceiver(),"流程审批", msgModel.getData1()));
		
		return msgModel;
		
	}
	
	protected void updateFlowCCNum(String businessId) {
		try {
			this.getQuery().executeUpdate("update yq_flow_apply t1 set t1.cc_count = (select count(1) from yq_flow_cc t2 where t2.business_id = t1.apply_id) where t1.apply_id = ? ", businessId);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	protected void sendFeedback(String businessId,String toUserId,String title,String replyContent) {
		if(replyContent==null) {
			replyContent = "";
		}
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		MessageModel model = new MessageModel();
		model.setSender(getUserId());
		model.setTitle(apply.getApplyTitle());
		model.setData((JSONObject)JSONObject.toJSON(apply));
		model.setFkId(businessId);
		if(toUserId==null) {
			model.setReceiver(apply.getApplyBy());
		}else {
			model.setReceiver(toUserId);
		}
		model.setData1(EasyDate.getCurrentDateStr());
		model.setData2(title+"|"+apply.getApplyTitle());
		if(StringUtils.notBlank(replyContent)) {
			model.setData3(replyContent);
		}else {
			model.setData3("--");
		}
		model.setDesc("更多信息，请登录云趣工作台查看详情，或联系处理人 "+getUserName());
		WxMsgService.getService().sendFlowFeedback(model);
		WebSocketUtils.sendMessage(new MsgModel(model.getReceiver(),"流程审批", model.getTitle()));
		if(StringUtils.notBlank(replyContent)) {
			replyContent = replyContent.replaceAll("\n", "<br>");
		}
		model.setDesc(replyContent+"<br>更多信息，请登录云趣工作台查看详情，或联系处理人 "+getUserName());
		model.setMsgLabel(title);
		model.setTitle("【流程审批】"+apply.getApplyTitle());
		EmailService.getService().sendFlowNoticeEmail(model);
	}
	
	protected void sendFeedback(String businessId,String toUserId,String title) {
		sendFeedback(businessId,toUserId,title,null);
	}
	protected void sendFeedback(String businessId,String msgContent) {
		sendFeedback(businessId,null,msgContent,null);
	}
	
	protected void updateApplyConf(EasyQuery query,String businessId,JSONObject params) throws SQLException {
		
	}
	
	protected EasyResult getApproveNextNodeId(String nextNodeId,FlowApplyModel apply,JSONObject params) throws SQLException {
		String _nextNodeId = params.getString("nextNodeId");
		String _nextNodeCode = params.getString("nextNodeCode");
		
		if(StringUtils.isNotBlank(_nextNodeCode)) {
			if("0".equals(_nextNodeCode)) {
				nextNodeId = "0";
			}else {
				ApproveNodeModel node = ApproveService.getService().getNodeInfoByNodeCode( _nextNodeCode, apply,params);
				if(node==null) {
					return EasyResult.fail(_nextNodeCode+"不存在,请联系管理员.");
				}
				nextNodeId = node.getNodeId();
			}
		}else if(StringUtils.isNotBlank(_nextNodeId)) {
			nextNodeId = _nextNodeId;
		}
		return EasyResult.ok(nextNodeId);
	}
	
	
	public abstract EasyResult actionForSubmitApply();
	
	public abstract EasyResult actionForUpdateApply();
	
	protected boolean setFlowApplyData(String businessId,EasyRecord applyRecord,JSONObject params) {
		return true;
	}
	
	protected boolean setFlowBusiData(String businessId,EasyRecord busiRecord,JSONObject params) {
		return true;
	}
	
	protected EasyResult submitApply() {
		JSONObject params = getJSONObject();
		this.info("submitApply:"+params.toJSONString(), null);
		
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String businessId = params.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			businessId =  FlowService.getService().getID();
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		
		String doAction = params.getString("doAction");
		EasyRecord applyRecord = getApplyRecord(flowCode,"submit".equals(doAction)?10:0);
		
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.set("apply_id",businessId);
		applyRecord.setColumns(applyInfo);
		JSONObject extInfo  = JsonKit.getJSONObject(params, "extend");
		applyRecord.set("data_json",extInfo.toJSONString());
		
		if("submit".equals(doAction)) {
			this.setApplyNo(flow,applyRecord);
		}
		
		boolean bl = true;
		EasyRecord record = null;
		JSONObject business = JsonKit.getJSONObject(params, "business");
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
		}
		try {
			if(!this.setFlowApplyData(businessId,applyRecord,params)||!this.setFlowBusiData(businessId,record,params)) {
				return EasyResult.fail();
			}
			
			if(record!=null) {
				bl = this.getQuery().save(record);
			}
			if(bl) {
				this.getQuery().save(applyRecord);
			}
			this.saveFile(businessId,params);
			this.saveItems(businessId, params);
			
			if("submit".equals(doAction)) {
				String result = this.startFlow(businessId,applyState,params);
				if(result==null) {
					this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
					return EasyResult.fail("流程节点未配置,请联系管理员");
				}
				return EasyResult.ok(businessId,"提交到审批节点:"+result);
			}else {
				addReviseLog(businessId,"保存草稿");
			}
			return EasyResult.ok(businessId);
		} catch (Exception e) {
			this.error(null, e);
			this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
			return EasyResult.fail("流程异常,请联系管理员"+e.getMessage());
		}
	}
	
	protected EasyResult updateApply() {
		JSONObject params = getJSONObject();
		this.info("updateApply:"+params.toJSONString(), null);
		
		String businessId = params.getString("businessId");
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String doAction = params.getString("doAction");
		
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		
		boolean bl = true;
		EasyRecord record = null;
		JSONObject business = JsonKit.getJSONObject(params, "business");
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
		}
		
		EasyRecord applyRecord = null;
		if("submit".equals(doAction)) {
			applyRecord =  getApplyRecord(businessId,flowCode,applyState);
		}else {
			applyRecord = getApplyRecord();
		}
		applyRecord.setPrimaryValues(businessId);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.setColumns(applyInfo);
		applyRecord.set("apply_id",businessId);
		JSONObject extInfo  = JsonKit.getJSONObject(params, "extend");
		applyRecord.set("data_json",extInfo.toJSONString());
		
		if("submit".equals(doAction)&&applyState!=FlowConstants.FLOW_STAT_CHECK_RETURN) {
			this.setApplyNo(flow,applyRecord);
		}
		
		try {
			if(!this.setFlowApplyData(businessId,applyRecord,params)||!this.setFlowBusiData(businessId,record,params)) {
				return EasyResult.fail();
			}
			
			if(record!=null) {
				bl = this.getQuery().update(record);
			}
			if(bl) {
				this.getQuery().update(applyRecord);
			}
			this.saveItems(businessId, params);
			
			if("submit".equals(doAction)) {
				String result = this.startFlow(businessId,applyState,params);
				if(result==null) {
					return EasyResult.fail("没有配置审批节点");
				}
			}else{
				addReviseLog(businessId,"保存草稿");
			}
			this.addReviseLog(businessId, "修改流程");
		} catch (Exception e) {
			this.error(null, e);
			return EasyResult.fail("保存异常,请联系管理员"+e.getMessage());
		}
		return EasyResult.ok();
	}
	
	protected String startFlow(String businessId,int applyState,JSONObject params) throws SQLException {
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		FlowModel flow = apply.getFlow();
		String _nextNodeId = params.getString("nextNodeId");
		String _nextNodeCode = params.getString("nextNodeCode");
		
		ApproveNodeModel nodeInfo = null;
		if(StringUtils.notBlank(_nextNodeCode)) {
			nodeInfo = ApproveService.getService().getNodeInfoByNodeCode(_nextNodeCode, apply,params);
		}else if(StringUtils.notBlank(_nextNodeId)) {
			nodeInfo = ApproveService.getService().getNodeInfoByNodeId(_nextNodeId,apply,params);
		}else {
			nodeInfo = ApproveService.getService().getStartNode(apply,params);
		}
		if(nodeInfo==null) {
		    return null;
		}
		if(StringUtils.isBlank(nodeInfo.getCheckBy())) {
			return null;
		}

		String todoCheckUserId = nodeInfo.getCheckBy();
		if(todoCheckUserId.indexOf(apply.getApplyBy())>-1) {
			ApproveNodeModel  _nodeInfo = ApproveService.getService().getNodeInfoByNodeId(nodeInfo.getNextNodeId(),apply,params);
			if(!"0".equals(nodeInfo.getNextNodeId())&&_nodeInfo!=null&&_nodeInfo.getCheckType()!=FlowConstants.FLOW_APPROVER_SELECT_USER) {
				nodeInfo = _nodeInfo;
			}
		}
		
		String nextResultId = RandomKit.uniqueStr();
		
		EasyRecord resultRecord = getApproveResultRecord(businessId);
		if(applyState==21) {
			resultRecord.set("node_name",flow.getStartNodeName()+"修改流程");
		}else {
			resultRecord.set("node_name",flow.getStartNodeName()+"发起流程");
		}
		
		List<String> ccIds = this.addCCLog(this.getQuery(),params,businessId,resultRecord.getString("RESULT_ID"));
		int ccCount = ccIds.size();
		resultRecord.set("cc_count", ccCount);
		
		String checkDesc = params.getString("checkDesc");
		if(StringUtils.notBlank(checkDesc)) {
			resultRecord.set("check_desc",checkDesc);
		}
		this.getQuery().save(resultRecord);
		String nowResultId = resultRecord.getPrimaryValue().toString();
		
		int signFlag = nodeInfo.getSignFlag();
		if(signFlag==1) {
			String checkBys = nodeInfo.getCheckBy();
			String[] checkIds = checkBys.split(",");
			int i= 0 ;
			for(String checkBy:checkIds) {
				ApproveResultRecord nextResult = getApproveResultRecord(nodeInfo, businessId, nextResultId,nowResultId);
				nextResult.setCheckUserId(checkBy);
				nextResult.setPreCheckUserId(checkBy);
				nextResult.setCheckName(StaffService.getService().getUserName(checkBy));
				if(i>0) {
					nextResult.setResultId(RandomKit.uniqueStr());
				}
				this.saveApproveNode(nextResult,apply.getFlowCode());
				i++;
			}
		}else {
			this.saveApproveResultRecord(nodeInfo, apply, nextResultId, nowResultId);
		}
		
		FlowService.getService().updateApplyBegin(businessId, nodeInfo.getNodeId(),nowResultId,nextResultId);
		
		//微信推送给审批人
		MessageModel msgModel  = getMessageModel(apply, nodeInfo.getCheckBy(), null, null);
		this.sendAllMsg(msgModel);
		
		if(ccCount>0) {
			this.sendFeedback(apply.getApplyId(),ccIds.stream().collect(Collectors.joining(",")),"抄送");
			this.updateFlowCCNum(apply.getApplyId());
		}
		
		return nodeInfo.getNodeName();
	}
	
	
	protected void saveFile(String businessId,JSONObject params) {
		Object obj = params.get("fileIds");
		if(obj!=null) {
			try {
				if(obj instanceof String) {
					this.getQuery().executeUpdate("update yq_files set fk_id = ? where file_id = ?",businessId,obj.toString());
				}else {
					JSONArray array = params.getJSONArray("fileIds");
					for(int i=0;i<array.size();i++) {
						this.getQuery().executeUpdate("update yq_files set fk_id = ? where file_id = ?",businessId,array.getString(i));
					}
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
	}
	
	protected void saveItems(String businessId,JSONObject params) throws SQLException {
		Object itemIndexObj = params.get("itemIndex");
		if(itemIndexObj!=null) {
			JSONArray itemIndexs = null;
			if(itemIndexObj instanceof String) {
				itemIndexs = new JSONArray();
				itemIndexs.add(itemIndexObj.toString());
			}else {
				itemIndexs = params.getJSONArray("itemIndex");
			}
			for(int i= 0;i<itemIndexs.size();i++) {
				EasyRecord itemModel = new EasyRecord("YQ_FLOW_APPLY_ITEM","ITEM_ID");
				String itemId = itemIndexs.getString(i);
				itemModel.setColumns(getItem(params, itemId));
				itemModel.setPrimaryValues(itemId);
				itemModel.set("BUSINESS_ID", businessId);
				if(itemId.length()>20) {
					itemModel.set("ITEM_ID", itemId);
					this.getQuery().update(itemModel);
				}else {
					itemModel.setPrimaryValues(RandomKit.uniqueStr());
					this.getQuery().save(itemModel);
				}
			}
		}
	}
	
	private JSONObject getItem(JSONObject jsonObject,String itemId){
		if(StringUtils.isBlank(itemId)){
			return jsonObject;
		}else{
			JSONObject newJsonObject=new JSONObject();
			for(String key :jsonObject.keySet()){
				if(key.endsWith("_"+itemId)){
					newJsonObject.put(key.replace("_"+itemId, "").trim(),jsonObject.get(key));
				}
			}
			return newJsonObject;
		}
	}
	
	
	protected synchronized void setApplyNo(FlowModel flow,EasyRecord applyRecord) {
		String applyNo = applyRecord.getString("APPLY_NO");
		if(StringUtils.isBlank(applyNo)) {
			try {
				String applyNoRule = flow.getApplyNoRule();
				if(StringUtils.notBlank(applyNoRule)) {
					List<String> nos = new ArrayList<String>();
					String seq = null;
					String[] applyNoRuleArray = applyNoRule.split("\\$");
					for(String rule:applyNoRuleArray) {
						if("YEAR".equals(rule)) {
							nos.add(String.valueOf(EasyCalendar.newInstance().getYear()));
						}else if("MONTH".equals(rule)) {
							nos.add(String.valueOf(EasyCalendar.newInstance().getMonth()));
						}else if("YEAR_MONTH".equals(rule)) {
							nos.add(String.valueOf(EasyCalendar.newInstance().getFullMonth()));
						}else if("DATE".equals(rule)){
							nos.add(String.valueOf(EasyCalendar.newInstance().getDateInt()));
						}else if(rule.startsWith("RUN_SEQ")) {
							seq = rule.substring(7);
						}else if(rule.startsWith("START_")) {
							nos.add(rule.substring(6));
						}
					}
					String prefix = String.join("", nos);
					String flowCode = flow.getFlowCode();
					String no = this.getQuery().queryForString("select max(apply_no) from yq_flow_apply where flow_code = ? and apply_no like '"+prefix+"%'",flowCode);
					if(StringUtils.isBlank(no)) {
						applyRecord.set("APPLY_NO", prefix + seq);
					}else {
						String str  = no.substring(prefix.length());
						applyRecord.set("APPLY_NO", prefix + calcNo(str));
					}
				}else {
					String prefix = EasyCalendar.newInstance().getFullMonth();
					String no = this.getQuery().queryForString("select max(apply_no) from yq_flow_apply where apply_no like '"+prefix+"%'");
					String num = prefix+"0001";
					if(StringUtils.isNotBlank(no)) {
						num = String.valueOf(Long.valueOf(no)+1);
					}
					applyRecord.set("APPLY_NO", num);
				}
			} catch (Exception e) {
				this.error(null, e);
				applyRecord.set("APPLY_NO", RandomKit.orderId());
			}
		}
		
	}
	
	private String calcNo(String str) {
		int len = str.length();
		StringBuffer beginStr = new StringBuffer();
		String _str = String.valueOf(Integer.valueOf(str)+1);
		int len2 = _str.length();
		for(int i=0;i<(len-len2);i++) {
			beginStr.append("0");
		}
		return beginStr.toString()+_str;
	}
	
	
	protected void delApplyData(String id) {
		if(StringUtils.isBlank(id)) {
			return;
		}
		try {
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id  = ?", id);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	protected void delApplyDatas(String id,String... params) {
		if(StringUtils.isBlank(id)) {
			return;
		}
		try {
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_files where fk_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_flow_cc where business_id  = ?", id);
			if(params!=null) {
				for(String obj:params) {
					if(StringUtils.isBlank(obj)) {
						continue;
					}
					if(!"yq_flow_apply".equalsIgnoreCase(obj)) {
						this.getQuery().executeUpdate("delete from "+obj+" where business_id  = ?", id);
					}
				}
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	protected EasyRecord getApproveResultRecord(String businessId) {
		EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
		resultRecord.set("result_id", RandomKit.uniqueStr());
		resultRecord.set("business_id", businessId);
		resultRecord.set("check_name", getUserName());
		resultRecord.set("dept_name", getDeptName());
		resultRecord.set("pre_check_user_id", getUserId());
		resultRecord.set("check_user_id", getUserId());
		resultRecord.set("check_result", 1);
		resultRecord.set("node_id",0);
		resultRecord.set("ip_address",WebKit.getIP(getRequest()));
		resultRecord.set("check_desc","");
		resultRecord.set("check_time",EasyDate.getCurrentDateString());
		resultRecord.set("get_time",EasyDate.getCurrentDateString());
		resultRecord.set("get_date", EasyCalendar.newInstance().getDateInt());
		this.setDevice(resultRecord);
		return resultRecord;
	}
	
	public void setDevice(EasyRecord record) {
		UserAgent ua = UserAgent.parseUserAgentString(getRequest().getHeader("User-Agent"));
		if(ua!=null) {
			OperatingSystem os = ua.getOperatingSystem();
			if(os!=null) {
				DeviceType deviceType = os.getDeviceType();
				if(deviceType!=null) {
					record.set("device",deviceType.toString());
				}
			}
		}
	}
	
	/**
	 * 修改
	 * @param resultId
	 * @param checkResult
	 * @param checkDesc
	 * @return
	 */
	protected EasyRecord getApproveResultRecord(String resultId,int checkResult,String checkDesc) {
		EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
		record.set("result_id", resultId);
		record.set("check_name", getUserName());
		record.set("check_user_id", getUserId());
		record.set("dept_name", getDeptName());
		record.set("check_result",checkResult);
		record.set("check_desc", checkDesc);
		record.set("check_time", EasyDate.getCurrentDateString());
		record.set("ip_address",WebKit.getIP(getRequest()));
		this.setDevice(record);
		return record;
	}
	
	protected ApproveResultRecord getApproveResultRecord(ApproveNodeModel nextNodeInfo,FlowApplyModel apply,String resultId,String prevResultId) {
		return getApproveResultRecord(nextNodeInfo,apply.getApplyId(),resultId,prevResultId);
	}
	
	/**
	 * 新增下节点
	 * @param model
	 * @return
	 */
	protected ApproveResultRecord getApproveResultRecord(ApproveNodeModel nextNodeInfo,String businessId,String resultId,String prevResultId) {
		ApproveResultRecord record = new ApproveResultRecord();
		record.set("result_id", resultId);
		record.set("prev_result_id", prevResultId);
		record.set("business_id", businessId);
		record.setCheckName(nextNodeInfo.getCheckName());
		record.setCheckUserId(nextNodeInfo.getCheckBy());
		record.setPreCheckUserId(nextNodeInfo.getCheckBy());
		record.setCheckResult(0);
		record.set("node_id",nextNodeInfo.getNodeId());
		record.set("node_name",nextNodeInfo.getNodeName());
		record.set("check_desc","");
		record.set("get_time",DateUtils.nowAddOneSecond());
		record.set("get_date", EasyCalendar.newInstance().getDateInt());
		if(nextNodeInfo.getHourLimit()>0) {
			record.setOverTime(DateUtils.hourAfter(nextNodeInfo.getHourLimit()));
		}
		this.setDevice(record);
		return record;
	}
	
	protected ApproveResultRecord saveApproveResultRecord(ApproveNodeModel nextNodeInfo,FlowApplyModel applyModel,EasyQuery query,String resultId,String prevResultId) throws SQLException {
		ApproveResultRecord record = getApproveResultRecord(nextNodeInfo,applyModel,resultId,prevResultId);
		boolean hasTrust = this.saveApproveNode(record,applyModel.getFlowCode(),query);
		if(hasTrust) {
			nextNodeInfo.setCheckBy(record.getCheckUserId());
		}
		return record;
	}
	
	protected ApproveResultRecord saveApproveResultRecord(ApproveNodeModel nextNodeInfo,FlowApplyModel applyModel,String resultId,String prevResultId) throws SQLException {
		return saveApproveResultRecord(nextNodeInfo,applyModel,null,resultId,prevResultId);
	}
	
	protected EasyRecord getApplyRecord(String flowCode) {
		return getApplyRecord(null,flowCode);
	}
	protected EasyRecord getApplyRecord(String flowCode,int applyState) {
		EasyRecord record = getApplyRecord(null,flowCode);
		record.set("apply_state", applyState);
		return record;
	}
	
	protected EasyRecord getApplyRecord(String applyId,String flowCode,int applyState) {
		EasyRecord record =  getApplyRecord(applyId,flowCode);
		if(applyState==FlowConstants.FLOW_STAT_CHECK_RETURN) {
			record.remove("APPLY_TIME");
			record.remove("APPLY_DATE");
		}
		return record;
	}
	
	protected EasyRecord getApplyRecord() {
		EasyRecord model = new EasyRecord("yq_flow_apply","apply_id"); 
		model.set("update_by",getUserId());
		model.set("update_time",EasyDate.getCurrentDateString());
		return model;
	}
	
	protected EasyRecord getApplyRecord(String id,String flowCode) {
		EasyRecord model = new EasyRecord("yq_flow_apply","apply_id"); 
		model.set("apply_time", EasyDate.getCurrentDateString());
		model.set("apply_date", EasyCalendar.newInstance().getDateInt());
		model.set("apply_by", getUserId());
		model.set("apply_staff_no", getStaffInfo().getStaffNo());
		model.set("apply_name", getUserName());
		model.set("dept_id", getDeptId());
		model.set("dept_name", getDeptName());
		model.set("flow_code", flowCode);
		if(id!=null) {
			model.setPrimaryValues(id);
		}
		return model;
	}

	
	protected EasyResult addReviseLog(String businessId,String relationId,String reviseContent) {
		EasyRecord record = new EasyRecord("yq_flow_revise_record","revise_id");
		try {
			record.setPrimaryValues(RandomKit.uniqueStr());
			record.set("business_id",businessId);
			record.set("relation_id",relationId);
			record.set("revise_content",reviseContent);
			record.set("add_time", EasyDate.getCurrentDateString());
			record.set("add_user_id", getUserId());
			record.set("add_user_name", getUserName());
			this.getQuery().save(record);
			this.getQuery().executeUpdate("update yq_flow_apply set revise_count = revise_count + 1 where apply_id = ?",businessId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	/**
	   * 先判断是否有委托
	  * 如果有委托，替换委托人，并且生成委托记录
	 * @param record
	 * @param query
	 * @throws SQLException
	 */
	protected boolean saveApproveNode(ApproveResultRecord record,String folowCode,EasyQuery query) throws SQLException {
		boolean hasTrust = false;
		if(query==null) {
			query = this.getQuery();
		}
		String checkUserId = record.getString("CHECK_USER_ID");
		if(StringUtils.notBlank(checkUserId)&&checkUserId.indexOf(",")==-1) {
			JSONObject row = this.getQuery().queryForRow("select * from yq_flow_trust_conf where assignor = ? and state = 0 and (flow_code = ? or flow_code = '0')", new Object[] {checkUserId,folowCode},new JSONMapperImpl());
			if(row!=null&&!row.isEmpty()) {
				String trustId = RandomKit.uniqueStr();
				String assignorName = row.getString("ASSIGNOR_NAME");
				String entrusted = row.getString("ENTRUSTED");
				record.set("trust_id",trustId);
				record.setApproveType(FlowConstants.APPROVE_TYPE_TRUST);
				record.setCheckUserId(entrusted);
				record.setCheckName(StaffService.getService().getUserName(entrusted));
				record.set("check_desc",assignorName+"发起的流程委托");
				
				EasyRecord trustRecord = new EasyRecord("yq_flow_trust","trust_id");
				trustRecord.setPrimaryValues(trustId);
				trustRecord.set("business_id", record.getString("BUSINESS_ID"));
				trustRecord.set("result_id", record.getString("RESULT_ID"));
				trustRecord.set("create_by",row.getString("ASSIGNOR"));
				trustRecord.set("create_by_name",assignorName);
				trustRecord.set("trust_user_id",entrusted);
				trustRecord.set("trust_user_name",row.getString("ENTRUSTED_NAME"));
				trustRecord.set("trust_reason",row.getString("REMARK"));
				trustRecord.set("trust_time",EasyDate.getCurrentDateString());
				
				query.save(trustRecord);
				hasTrust = true;
			}
		}
		query.save(record);
		return hasTrust;
	}
	
	protected boolean saveApproveNode(ApproveResultRecord record,String folowCode) throws SQLException {
		return saveApproveNode(record,folowCode,null);
	}
	
	protected EasyResult addReviseLog(String businessId,String reviseContent) {
		return addReviseLog(businessId,null,reviseContent);
	}
	
	protected List<String> addCCLog(EasyQuery query ,JSONObject params,String businessId,String resultId) throws SQLException {
		List<String> ids = new ArrayList<String>();
		String ccIdsStr = params.getString("ccIds");
		String ccNamesStr = params.getString("ccNames");
		if(StringUtils.isBlank(ccIdsStr)) {
			if(StringUtils.notBlank(ccNamesStr)) {
				ccIdsStr = StaffService.getService().getUserId(ccNamesStr);
			}
		}
		if(StringUtils.notBlank(ccIdsStr)&&StringUtils.notBlank(ccNamesStr)) {
			if(ccIdsStr.split(",").length!=ccNamesStr.split(",").length) {
				ccIdsStr = StaffService.getService().getUserId(ccNamesStr);
			}
		}
		if(StringUtils.notBlank(ccIdsStr)) {
			String[] ccIds = ccIdsStr.split(",");
			Set<String> uniqueCcIds = new HashSet<String>(Arrays.asList(ccIds));
			for(String ccId:uniqueCcIds) {
				if(StringUtils.notBlank(ccId)&&!"undefined".equals(ccId)) {
					String userId = StaffService.getService().autoGetUserId(ccId);
					if(!this.getQuery().queryForExist("select count(1) from yq_flow_cc where business_id = ? and cc_by = ?", businessId,userId)) {
						EasyRecord record = new EasyRecord("yq_flow_cc","cc_id");
						record.set("cc_id", RandomKit.uniqueStr());
						record.set("cc_by", userId);
						record.set("cc_by_name", StaffService.getService().autoGetUserName(userId));
						record.set("business_id", businessId);
						record.set("create_by", getUserId());
						record.set("create_name", getUserName());
						record.set("result_id", resultId);
						record.set("cc_time", EasyDate.getCurrentDateString());
						if(!ids.contains(userId)) {
							query.save(record);
							ids.add(userId);
						}
					}
				}
			}
		}
		return ids;
	}
}
