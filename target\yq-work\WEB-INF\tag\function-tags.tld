<?xml version="1.0" encoding="UTF-8" ?>
<taglib xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3g.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee web-jsptaglibrary_2_0.xsd"
	version="2.0">
	<tlib-version>1.2</tlib-version>
	<jsp-version>1.2</jsp-version>
	<short-name>jsp-tags</short-name>
	<uri>http://www.yunqu-info.com/yq/work/tags/function-tags.tld</uri>
	<description>jsp-tags</description>
	
	<tag>
		<name>res</name>
		<tag-class>com.yunqu.work.utils.YqResTag</tag-class>
		<body-content>JSP</body-content>
		<attribute>
			<name>resId</name>
			<required>true</required> 
			<rtexprvalue>true</rtexprvalue> 
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>是否有资源权限  默认true </description>
			<name>hasPermission</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	<tag>
		<name>user</name>
		<tag-class>com.yunqu.work.utils.YqUserTag</tag-class>
		<body-content>JSP</body-content>
		<attribute>
			<name>userId</name>
			<required>true</required> 
			<rtexprvalue>true</rtexprvalue> 
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<description>是否有资源权限  默认true </description>
			<name>hasPermission</name>
			<required>false</required>
			<rtexprvalue>false</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	<tag>
		<name>hasRole</name>
		<tag-class>com.yunqu.work.utils.YqRoleTag</tag-class>
		<body-content>JSP</body-content>
		<attribute>
			<name>roleId</name>
			<required>true</required> 
			<rtexprvalue>true</rtexprvalue> 
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
    <function>
        <description>获取配置参数</description>
        <name>getValue</name>
        <function-class>com.yunqu.work.utils.Functions</function-class>
        <function-signature>java.lang.String getValue(java.lang.String)</function-signature>
    </function>
</taglib>