package com.yunqu.work.dao.contract;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.FlowConstants;

@WebObject(name = "ReviewDao")
public class ReviewDao extends AppDaoContext {

	@WebControl(name="getReviewNo",type=Types.TEXT)
	public JSONObject getReviewNo(){
		String dateId = EasyCalendar.newInstance().getFullMonth();
		try {
			String val = this.getQuery().queryForString("select max(apply_no) from yq_flow_apply where apply_no like 'P-"+dateId+"%' and flow_code = ?","contract_review");
			if(StringUtils.isBlank(val)) {
				return getJsonResult("P-"+dateId+"1001");
			}else {
				val = val.replace("P-", "");
				return getJsonResult("P-"+String.valueOf(Long.valueOf(val)+1));
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	@WebControl(name = "selectData",type = Types.TEMPLATE)
	public JSONObject selectData() {
		EasySQL sql = new EasySQL("select t1.*,t3.* from yq_flow_apply t1 INNER JOIN yq_contract_review t3 on t3.business_id = t1.apply_id");
		sql.appendLike(param.getString("contractName"),"and t3.contract_name like ?");
		sql.append("order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "reviewInfo",type = Types.TEMPLATE)
	public JSONObject reviewInfo() {
		EasySQL sql = new EasySQL("select t1.*,t3.* from yq_flow_apply t1 INNER JOIN yq_contract_review t3 on t3.business_id = t1.apply_id");
		String reviewId = param.getString("reviewId");
		if(StringUtils.isBlank(reviewId)) {
			return getJsonResult(new JSONObject());
		}
		sql.append(reviewId,"and t3.business_id = ?");
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name = "list",type = Types.TEMPLATE)
	public JSONObject list() {
		EasySQL sql = new EasySQL("select t1.*,t3.*,t2.node_name,t2.check_result,t2.check_name,t2.check_user_id,t4.flow_name,t2.read_time,t2.result_id,t2.data4 review_node_name,t2.check_desc,t2.check_time from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on");
		int approveAll = param.getIntValue("approveAll");
		if(approveAll==0) {
			sql.append("t2.result_id = t1.next_result_id");
		}else {
			sql.append("t2.business_id = t1.apply_id");
		}
		sql.append("INNER JOIN yq_contract_review t3 on t3.business_id = t1.apply_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		if(approveAll!=0) {
			sql.append(1,"and t2.data1 = ?");
		}
		sql.appendLike(param.getString("contractName"),"and t3.contract_name like ?");
		
		String applyState = param.getString("applyState");
		if(StringUtils.isBlank(applyState)) {
			sql.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		}else {
			sql.append(applyState,"and t1.apply_state = ?");
		}
		
		sql.append(param.getString("signEnt"),"and t3.sign_ent = ?");
		sql.append(param.getString("checkResult"),"and t2.check_result = ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		sql.appendLike(param.getString("checkNodeName"),"and t2.data4 like ?");
//		sql.append(getUserId(),"and t1.apply_by = ?");
		sql.append("order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "purchaseList",type = Types.TEMPLATE)
	public JSONObject purchaseList() {
		EasySQL sql = new EasySQL("select t1.apply_id,t1.apply_no,t2.*,t3.contract_name from yq_flow_apply t1 INNER JOIN yq_contract_purchase t2 on t2.business_id = t1.apply_id");
		sql.append("INNER JOIN yq_contract_review t3 on t3.business_id = t1.apply_id");
		sql.append("where 1=1");
		sql.appendLike(param.getString("contractName"),"and t3.contract_name like ?");
		sql.append(param.getString("signEnt"),"and t3.sign_ent = ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		sql.append("order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeInfo",type = Types.RECORD)
	public JSONObject nodeInfo() {
		String nodeId = param.getString("conf.NODE_ID");
		if(StringUtils.isBlank(nodeId)) {
			return getJsonResult(new JSONObject());
		}
		return queryForRecord("select * from yq_contract_review_node_conf where node_id = ?",nodeId);
	}
	
	@WebControl(name = "custReview",type = Types.LIST)
	public JSONObject custReview() {
		EasySQL sql = getEasySQL("select t1.* from yq_contract_review t2,yq_flow_apply t1 where t2.business_id = t1.apply_id");
		sql.append(param.getString("custId"),"and t2.cust_id = ?",false);	
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name = "nodeConfList",type = Types.TEMPLATE)
	public JSONObject nodeConfList() {
		EasySQL sql = getEasySQL("select * from yq_contract_review_node_conf where node_type = 1 and node_state = 0 order by order_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeConfTree",type = Types.TEMPLATE)
	public JSONObject nodeConfTree() {
		EasySQL sql = getEasySQL("select * from yq_contract_review_node_conf where node_type = 1");
		String nodeState = param.getString("nodeState");
		sql.append(nodeState,"and node_state = ?");
		sql.append("order by order_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}

}



