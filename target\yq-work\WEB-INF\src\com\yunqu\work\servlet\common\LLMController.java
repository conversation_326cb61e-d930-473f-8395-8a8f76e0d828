package com.yunqu.work.servlet.common;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.HashSet;
import java.util.Set;

import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.string.StringUtils;

import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.jfinal.kit.SseEmitter;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.ai.Bigmodel;
import com.yunqu.work.ai.SiliconCloudAI;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;

@Before(AuthInterceptor.class)
@Path(value = "/llm")
public class LLMController extends BaseController {

	public static Map<String,List<String>> cacheData = new ConcurrentHashMap<String,List<String>>();
	
	public void taskAnswerStreamAI() {
		SseEmitter sseEmitter = new SseEmitter(getResponse());
		String taskId = getPara();
		String content = Db.queryStr("select task_desc from yq_task where task_id = ?",taskId);
		if(StringUtils.notBlank(content)) {
			List<String> result = SiliconCloudAI.requestSSE("deepseek-chat","请解答的这个任务", content);
			if(result!=null) {
				cacheData.put(taskId, result);
				for(String msg:result) {
					sseEmitter.sendMessage(msg);
					try {
						Thread.sleep(20);
					} catch (InterruptedException e) {
						this.error(e.getMessage(), e);
					}
				}
			}
		}
		sseEmitter.complete();
		renderNull();
	}


	public void analyzeProjectDelayStream() {
		SseEmitter sseEmitter = new SseEmitter(getResponse());
		String projectId = getPara();

		//延期次数最多的
		String delayCountSql = "SELECT DISTINCT t1.task_id, t1.task_desc, t1.assign_user_name, t1.delay_count FROM yq_task t1 WHERE t1.project_id = ? AND t1.delay_count > 0 ORDER BY t1.delay_count DESC LIMIT 5";
		//延期天数最长
		String delayDaysSql = "SELECT t1.task_id, t1.task_name, t1.assign_user_name, SUM(DATEDIFF(STR_TO_DATE(t2.new_plan_time, '%Y-%m-%d %H:%i'), STR_TO_DATE(t2.old_plan_time, '%Y-%m-%d %H:%i'))) as total_delay_days FROM yq_task t1 INNER JOIN yq_task_delay t2 ON t1.task_id = t2.task_id WHERE t1.project_id = ? GROUP BY t1.task_id, t1.task_desc, t1.assign_user_name ORDER BY total_delay_days DESC LIMIT 3";

		String delayDetailSql = "SELECT reason, DATEDIFF(STR_TO_DATE(new_plan_time, '%Y-%m-%d %H:%i'), STR_TO_DATE(old_plan_time, '%Y-%m-%d %H:%i')) as delay_days, create_time FROM yq_task_delay WHERE task_id = ? ORDER BY delay_days DESC LIMIT 3";

			List<Record> delayCountTasks = Db.find(delayCountSql, projectId);
			List<Record> delayDaysTasks = Db.find(delayDaysSql, projectId);

			//删除重复的 taskId
			Set<String> taskIdSet = new HashSet<>();
			for (Record task : delayDaysTasks) {
				taskIdSet.add(task.getStr("task_id"));
			}
			delayCountTasks.removeIf(task -> taskIdSet.contains(task.getStr("task_id")));

			if (delayCountTasks.isEmpty() && delayDaysTasks.isEmpty()) {
				sseEmitter.complete();
				renderNull();
				return;
			}

			StringBuilder content = new StringBuilder();
			content.append("这个项目的延期情况分析如下：\n");
			if(!delayCountTasks.isEmpty()) {
				content.append("## 延期次数最多的任务\n");
				for(Record task : delayCountTasks) {
					content.append("### ").append(task.getStr("task_name")).append("\n");
					content.append("- 负责人: ").append(task.getStr("assign_user_name")).append("\n");
					content.append("- 延期次数: ").append(task.getInt("delay_count")).append("\n");

					List<Record> delayDetails = Db.find(delayDetailSql, task.getStr("task_id"));
					if(delayDetails != null && !delayDetails.isEmpty()) {
						content.append("- 前3条最长延期记录:\n");
						for(Record delay : delayDetails) {
							content.append("  * 延期").append(delay.getInt("delay_days"))
									.append("天: ").append(delay.getStr("reason")).append("\n");
						}
					}
					content.append("\n");
				}
			}
			if(!delayDaysTasks.isEmpty()) {
				content.append("## 延期天数最长的任务\n");
				for(Record task : delayDaysTasks) {
					content.append("### ").append(task.getStr("task_name")).append("\n");
					content.append("- 负责人: ").append(task.getStr("assign_user_name")).append("\n");
					content.append("- 总延期天数: ").append(task.getBigDecimal("total_delay_days")).append("\n");
					List<Record> delayDetails = Db.find(delayDetailSql, task.getStr("task_id"));
					if(delayDetails != null && !delayDetails.isEmpty()) {
						content.append("- 前3条最长延期记录:\n");
						for(Record delay : delayDetails) {
							content.append("  * 延期").append(delay.getInt("delay_days"))
									.append("天:").append(delay.getStr("reason")).append("\n");
						}
					}
					content.append("\n");
				}
			}

			String prompt = "请分析这些任务的延期情况，用简洁的语言总结出 最重要的3-4条延期原因以及该原因导致的延期天数。";
			List<String> result = SiliconCloudAI.requestSSE("deepseek-chat", prompt, content.toString());
			if(result != null) {
				cacheData.put(projectId, result);
				for(String msg : result) {
					sseEmitter.sendMessage(msg);
					try {
						Thread.sleep(20);
					} catch (InterruptedException e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			sseEmitter.complete();
			renderNull();
	}
	
	public void taskAnswerAI() {
		String taskId = getJsonPara("taskId");
		String content = Db.queryStr("select task_desc from yq_task where task_id = ?",taskId);
		if(StringUtils.notBlank(content)) {
			String result = Bigmodel.request("请回答这个任务,如果是开发相关请使用Java或mysql,如果是其他问题正常回答就行。", content);
			if(StringUtils.notBlank(result)) {
				renderJson(EasyResult.ok(result));
				return;
			}
		}
		renderJson(EasyResult.fail());
	}
	
	public void weeklyAnswerAI() {
		String weeklyId = getJsonPara("weeklyId");
		String content = Db.queryStr("select item_1 from yq_weekly where weekly_id = ?",weeklyId);
		if(StringUtils.notBlank(content)) {
			String html = Bigmodel.request("请总结周报的风险或进度问题", content);
			if(StringUtils.notBlank(html)) {
				renderJson(EasyResult.ok(html));
				return;
			}
		}
		renderJson(EasyResult.fail());
	}
	
	public void markdownToHtml() {
		String id = getPara("id");
		List<String> list = cacheData.get(id);
		if(list==null) {
			renderText("");
			return;
		}
		StringBuffer markdown = new StringBuffer();
		for(String str:list) {
			markdown.append(str);
		}
		cacheData.remove(id);
		Parser parser = Parser.builder().build();
        HtmlRenderer renderer = HtmlRenderer.builder().build();
        Node document = parser.parse(markdown.toString());
        String result =  renderer.render(document);
        renderText(result);
	}
}
