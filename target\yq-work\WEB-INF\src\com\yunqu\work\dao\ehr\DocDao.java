package com.yunqu.work.dao.ehr;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.DocModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="DocDao")
public class DocDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		DocModel model=new DocModel();
		model.setColumns(getParam("doc"));
		return queryForRecord(model);
	}
	@WebControl(name="detail",type=Types.RECORD)
	public JSONObject detail(){
		DocModel model=new DocModel();
		model.setPrimaryValues(param.getString("docId"));
		if(!isSuperUser()) {
			if(StringUtils.notBlank(model.getDocId())){
				LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getDocId(),"doc",request);
			}
			try {
				this.getQuery().executeUpdate("update yq_doc set view_count = view_count +1 where doc_id = ?",model.getDocId());
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		return queryForRecord(model);
	}
	@WebControl(name="favoriteList",type=Types.LIST)
	public JSONObject favoriteList(){
		EasySQL sql=getEasySQL("select t1.*,t2.favorite_time,t2.favorite_id from YQ_DOC t1 inner join yq_favorite t2 on t2.fk_id = t1.doc_id where 1=1");
		sql.append(getUserPrincipal().getUserId(),"and t2.favorite_by = ?");
		sql.appendLike(param.getString("title"),"and t1.title like ?");
		sql.append(param.getString("folderId"),"and t1.folder_id = ?");
		sql.append("order by t2.favorite_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select t1.* from YQ_DOC t1,yq_folder t2 where t1.folder_id = t2.folder_id");
		sql.append(0,"and t2.folder_auth = ?");
		sql.append("and t1.source = '0'");
		sql.appendLike(param.getString("title"),"and t1.title like ?");
		sql.append(param.getString("folderId"),"and t1.folder_id = ?");
		sql.append(1,"and t1.doc_auth = ?");
		sql.append("order by t1.order_index asc,t1.update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="myAiEditorData",type=Types.LIST)
	public JSONObject myAiEditorData(){
		EasySQL sql = new EasySQL();
		sql.append("select t1.* from yq_ai_editor t1");
		sql.append("where 1=1");
		sql.appendLike(param.getString("title"),"and t1.title like ?");
		sql.append(getUserId(),"and t1.create_user_id = ?");
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="myLookAiEditorData",type=Types.LIST)
	public JSONObject myLookAiEditorData(){
		EasySQL sql = new EasySQL();
		sql.append("select t1.*,t2.look_time from yq_ai_editor t1,yq_look_log t2");
		sql.append("where 1=1 and t1.doc_id = t2.fk_id");
		sql.appendLike(param.getString("title"),"and t1.title like ?");
		sql.append(getUserId(),"and t2.look_by = ?");
		sql.append("order by t2.look_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="xtAiEditorData",type=Types.LIST)
	public JSONObject xtAiEditorData(){
		EasySQL sql = new EasySQL();
		sql.append("select t1.*,t2.create_time edit_time from yq_ai_editor t1 inner join ");
		sql.append("(select tt.* from yq_ai_editor_his tt where 1=1");
		sql.append(getUserId(),"and tt.create_user_id = ?");
		sql.append("group by tt.doc_id) t2");
		sql.append("on t1.doc_id = t2.doc_id");
		sql.append("where 1=1");
		sql.append("and t1.create_user_id !=t2.create_user_id");
		sql.appendLike(param.getString("title"),"and t1.title like ?");
		sql.append("order by t2.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectList",type=Types.LIST)
	public JSONObject projectList(){
		EasySQL sql=getEasySQL("select * from YQ_DOC where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append(param.getString("folderId"),"and folder_id = ?");
		sql.append(param.getString("fkId"),"and fk_id = ?");
		sql.append(1,"and doc_auth = ?");
		sql.append("order by order_index asc,update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}


}
