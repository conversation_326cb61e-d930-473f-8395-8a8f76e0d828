<?xml version="1.0" encoding="UTF-8"?><project-modules id="moduleCoreId" project-version="1.5.0">
            
    
    <wb-module deploy-name="yq-work">
                        
        
        <wb-resource deploy-path="/" source-path="/WebContent"/>
                        
        
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src"/>
                        
        
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/resource"/>
                        
        
        <wb-resource deploy-path="/" source-path="/target/m2e-wtp/web-resources"/>
                        
        
        <wb-resource deploy-path="/" source-path="/src/main/webapp" tag="defaultRootSource"/>
                        
        
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/target/generated-test-sources/test-annotations"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/target/generated-sources/annotations"/>
                        
        
        <property name="java-output-path" value="/yq-work/build/classes"/>
                        
        
        <property name="context-root" value="yq-work"/>
                
        <property name="component.exclusion.patterns"/>
                    
    
    </wb-module>
        

</project-modules>
