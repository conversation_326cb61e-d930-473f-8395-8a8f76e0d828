<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>
		
		.left{
			width: 355px;float: left;margin: -15px;padding: 15px;background-color: #fff;height: 100%;height: 100%;
			margin-left: 0px;
		}
		.right{
			float: left;width:calc(100% - 370px);
			margin-left: 30px;
			height: 100%;
		}
		.laytable-cell-1-0-2{font-size: 15px!important;color: #d71a1a;}
		.laytable-cell-1-0-3{font-size: 15px!important;color: #5fb878;}
		.weekActive{font-weight: 700;}
		p{line-height: 30px;height: 30px;cursor: pointer;color: #1E9FFF;font-size: 13px;}
		p:hover{text-decoration: underline;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox-content left">
				<div style="line-height:40px;height: 40px;border-bottom: 1px solid #eee;">
					请选择筛选条件
				</div>
				<div class="input-group input-group-sm mt-10" style="width: 100%;">
					 <span class="input-group-addon">部门</span>	
					 <select name="deptId" data-mars="EhrDao.allDept" data-mars-reload="false" onchange="list.query();" class="form-control input-sm">
                    		<option value="">请选择</option>
                   	</select>
			     </div>
				 <div style="padding: 8px;" data-mars="WeeklyDao.getRecentWeek15" data-template="getRecentWeek"></div>
			</div>
			<script type="text/x-jsrender" id="getRecentWeek">
			     <p data-year="" data-week-no=""><i class="layui-icon layui-icon-right"></i> 全部</p>
			    {{for data}}
				{{if thisWeekNo==WEEK_NO}}<input type="hidden" name="weeklyNo" id="weeklyNo" value="{{:thisWeekNo}}"/><input type="hidden" name="year" id="year" value="{{:YEAR}}"/>{{/if}}
					<p data-week-no="{{:WEEK_NO}}" data-year="{{:YEAR}}" class="mr-20 {{if thisWeekNo==WEEK_NO}}weekActive{{/if}}"><i class="layui-icon layui-icon-right"></i> {{:TITLE}}</p>
			    {{/for}}
			</script>
			<div class="right">
				<div class="ibox-content clearfix">
			    	<table class="layui-hide" id="list"></table>
				</div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'WeeklyDao.weeklyStat',
					id:'list',
					limit:30,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'DEPT_NAME',
						title: '部门',
						align:'left'
					},{
					    field: '',
						title: '工作周期',
						align:'center',
						templet:function(row){
							return row['YEAR']+"年第"+row['WEEKLY_NO']+"周";
						}
					},{
					    field: 'USER_COUNT',
						title: '部门人数',
						align:'center'
					},{
					    field: 'WEEKLY_COUNT',
						title: '已填报数',
						align:'center',
						templet:function(row){
							var v1=row['USER_COUNT'];
							var v2=row['WEEKLY_COUNT'];
							var v3 = v2/v1;
							if(v3 < 0.7){
								return '<span class="layui-badge-dot"></span> '+v2;
							}else{
								return v2;
							}
						}
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'list',jumpOne:true});
			}
		}
		$(function(){
			var height=$(window).height()-10;
			$(".left").css("height",height);
			$("#searchForm").render({success:function(){
				list.init();
				$("p").click(function(){
					var t=$(this);
					t.siblings().removeClass("weekActive");
					t.addClass("weekActive");
					$("#year").val(t.data("year"));
					$("#weeklyNo").val(t.data("weekNo"));
					list.query();
				});
			}});
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>