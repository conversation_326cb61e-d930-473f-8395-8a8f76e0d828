package com.yunqu.work.servlet.common;

import java.sql.SQLException;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Clear;
import com.jfinal.core.Path;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.utils.AesUtils;

@Clear
@Path(value = "/request/mavenlog")
public class MavenLogController extends BaseController{
	
	public void addLog() {
		String auth = getRequest().getHeader("auth");
		if(StringUtils.isBlank(auth)) {
			renderJson(EasyResult.fail("auth is empty"));
			return;
		}
		String _auth  = AesUtils.decrypt(auth);
		if(StringUtils.isBlank(_auth)) {
			renderJson(EasyResult.fail("decrypt error"));
			return;
		}
		Long ts = Long.valueOf(_auth);
		long date = 1000*60*30;
		if(System.currentTimeMillis() - ts > date) {
			renderJson(EasyResult.fail("time out"));
			return;
		}
		
		JSONObject data = getJSONObject();
		System.out.println(data.toJSONString());
		EasyRecord record = new EasyRecord("YQ_MAVEN_LOG","LOG_ID");
		record.set("LOG_ID", RandomKit.uniqueStr());
		record.setColumns(data);
		try {
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderJson(EasyResult.ok(data));
	}
	
}
