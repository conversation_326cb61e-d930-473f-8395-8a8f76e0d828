package com.yunqu.work.dao.bx;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;

@WebObject(name = "BxDao")
public class BxDao extends AppDaoContext{
	
	@WebControl(name = "bxTitle",type = Types.TEXT)
	public JSONObject bxTitle() {
		return getText(getStaffInfo().getUserName()+getStaffInfo().getStaffNo()+"费用报销单");
	}
	
	@WebControl(name = "yqBxNo",type = Types.TEXT)
	public JSONObject yqBxNo() {
		try {
			String prefix = "14EXP"+EasyCalendar.newInstance().getYear();
			String no = this.getQuery().queryForString("select max(apply_no) from yq_flow_apply where apply_no like '"+prefix+"%'");
			String num = prefix+"000001";
			if(StringUtils.isNotBlank(no)) {
				String str = String.valueOf(Integer.valueOf(no.substring(5))+1);
				num = "14EXP"+  str;
			}
			return getText(num);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult("");
	}
	@WebControl(name = "zrBxNo",type = Types.TEXT)
	public JSONObject zrBxNo() {
		try {
			String prefix = "13EXP"+EasyCalendar.newInstance().getYear();
			String no = this.getQuery().queryForString("select max(apply_no) from yq_flow_apply where apply_no like '"+prefix+"%'");
			String num = prefix+"000001";
			if(StringUtils.isNotBlank(no)) {
				num = "13EXP" + String.valueOf(Integer.valueOf(no.substring(5))+1);
			}
			return getText(num);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult("");
	}

	@WebControl(name="selectDept",type=Types.PAGE)
	public JSONObject selectDept(){
		EasySQL sql = new EasySQL();
		sql.append("select t1.DEPT_ID,t2.FINANCE_DEPT_CODE,t1.DEPT_NAME,t2.TEMP_NAME from "+Constants.DS_MAIN_NAME+".easi_dept t1,yq_flow_bx_dept t2 where t1.dept_id = t2.dept_id");
		sql.append("and t2.dept_state = 0");
		sql.appendLike(param.getString("deptName"),"and t1.DEPT_NAME like ?");
		sql.append("ORDER BY t1.DEPT_CODE desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="selectFeeType",type=Types.PAGE)
	public JSONObject selectFeeType(){
		EasySQL sql = new EasySQL("select DISTINCT(fee_name) fee_name,fee_code from yq_flow_bx_subject where 1=1");
		sql.appendLike(param.getString("feeName"),"and fee_name like ?");
		sql.appendLike(param.getString("feeCode"),"and fee_code like ?");
		sql.append("ORDER BY fee_code");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name = "bxItemInfo",type = Types.RECORD)
	public JSONObject bxItemInfo() {
		String itemId = param.getString("itemId");
		if(StringUtils.isBlank(itemId)) {
			return getJsonResult(null);
		}
		EasySQL sql = getEasySQL("select * from yq_flow_bx_item where 1=1");
		sql.append(itemId,"and item_id = ?");
		return queryForRecord(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "bxItems",type = Types.TEMPLATE)
	public JSONObject bxItems() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from yq_flow_bx_item where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by item_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "ztBxItems",type = Types.TEMPLATE)
	public JSONObject ztBxItems() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from zr_flow_bx_item where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by item_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "allDept",type = Types.TEMPLATE)
	public JSONObject allDept() {
		return queryForList("SELECT t1.DEPT_PATH_NAME d_name,t1.ROOT_ID,t1.DEPT_ID d_id,t1.dept_code d_code,t2.* FROM "+Constants.DS_MAIN_NAME+".easi_dept t1 LEFT JOIN yq_flow_bx_dept t2 ON t1.DEPT_ID = t2.dept_id where t2.dept_state = 0 and length(t1.dept_code) = 6 order by t1.dept_name",null);
	}
	
	@WebControl(name = "myBudget",type = Types.RECORD)
	public JSONObject myBudget() {
		EasySQL sql = getEasySQL("select * from");
		return queryForRecord(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "budgetList",type = Types.PAGE)
	public JSONObject budgetList() {
		EasySQL sql = getEasySQL("select * from yq_flow_bx_budget where 1=1");
		sql.append(param.getString("budgetType"),"and budget_type = ?","0");
		sql.append(param.getString("userName"),"and user_name = ?");
		sql.append("order by user_id,create_time");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name = "contactUnit",type = Types.PAGE)
	public JSONObject contactUnit() {
		EasySQL sql = getEasySQL("select * from yq_finance_contact_unit where 1=1");
		sql.appendLike(param.getString("unitName"),"and unit_name like ?");
		sql.appendLike(param.getString("unitNo"),"and unit_no like ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "contactUnitInfo",type = Types.RECORD)
	public JSONObject contactUnitInfo() {
		EasySQL sql = getEasySQL("select * from yq_finance_contact_unit where 1=1");
		sql.append(param.getString("id"),"and id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "flowList",type = Types.TEMPLATE)
	public JSONObject flowList() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t2.check_time,t4.bx_money");
		sql.append("from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("INNER JOIN yq_flow_bx_base t4 on t1.apply_id = t4.business_id");
		sql.append("where 1=1");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		sql.append(param.getString("applyState"),"and t1.apply_state = ?");
		sql.append(param.getString("checkName"),"and t1.check_name = ?");
		sql.append(param.getString("applyName"),"and t1.apply_name = ?");
		sql.append(param.getString("payState"),"and t4.pay_state = ?");
		sql.append(param.getString("checkResult"),"and t2.check_result = ?");
		sql.append(param.getString("checkUserId"),"and t2.check_user_id = ?");
		sql.append("order by t1.apply_time desc,t1.apply_id,t2.get_time");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
}
