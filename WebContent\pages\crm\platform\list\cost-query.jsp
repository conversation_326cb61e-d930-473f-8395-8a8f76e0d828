<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>成本明细</title>
	<style>
		.ibox-content .layui-table-cell {
		    font-size: 13px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 180px">
							 <span class="input-group-addon">平台类型</span>
							 <SELECT name="platformTypeId" class="form-control input-sm" data-mars="BusinessPlatformDao.getPlatformTypeDict" onchange="CostList.loadPlatforms(this.value)">
								 <option value="">全部</option>
							 </SELECT>
						 </div>
						 <div class="input-group input-group-sm" style="width: 200px">
							 <span class="input-group-addon">业务平台</span>
							 <SELECT name="platformId" class="form-control input-sm" data-mars="BusinessPlatformDao.getPlatformDict" >
								 <option value="">全部</option>
							 </SELECT>
						 </div>
						 <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">成本来源</span>
							 <select name="costType" class="form-control input-sm">
								 <option value="">全部</option>
								 <option value="1">采购</option>
								 <option value="2">报销</option>
								 <option value="0">其他</option>
							 </select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">成本归属</span>
							 <select name="costOwner" class="form-control input-sm">
								 <option value="">全部</option>
								 <option value="0">平台成本</option>
								 <option value="1">客户成本</option>
								 <option value="2">其他</option>
							 </select>
						 </div>
	          		     <div class="input-group input-group-sm" style="width: 200px">
							 <span class="input-group-addon">成本名称</span>	
							 <input type="text" name="costName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">合同名称</span>
							 <input type="text" name="contractName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">客户名称</span>	
							 <input type="text" name="custName" class="form-control input-sm">
					     </div>
	          	     </div>
	          	     <div class="form-group input-group-sm">
						 <div class="input-group input-group-sm">
							 <span class="input-group-addon">成本日期</span>
							 <input type="text" name="costBeginDate" class="form-control input-sm" style="width: 90px;" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
							 <input type="text" name="costEndDate" class="form-control input-sm" style="width: 90px;"onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
						 </div>
						 <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">供应商</span>
							 <input type="text" name="supplierName" class="form-control input-sm">
						 </div>
					     <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-info" data-event="enter" onclick="CostList.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="$('#searchForm')[0].reset();CostList.query();">重置</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="costListTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		var CostList = {
			init:function(){
				$("#searchForm").initTable({
					mars:'CustOperateDao.costPageList',
					id:'costListTable',
					height:'full-110',
					limit:30,
					toolbar:true,
					defaultToolbar:['filter', 'print', 'exports'],
					autoSort:false,
					cols: [[
					  {
						title:'序号',
						type:'numbers',
						align:'left'
					  },{
						field: 'PLATFORM_TYPE_NAME',
						title: '平台类型',
						width:120
					  },{
						field: 'PLATFORM_NAME',
						title: '平台名称',
						width:140,
						templet:'<div><a href="javascript:;" onclick="platformDetail(\'{{d.PLATFORM_ID}}\')">{{d.PLATFORM_NAME}}</a></div>'
					  },{
						field: 'CONTRACT_NAME',
						title: '合同名称',
						minWidth:180,
						templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_NAME}}</a></div>'
					  },{
						field: 'CUST_NAME',
						title: '客户名称',
						width:150,
						templet:'<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
					  },{
						field: 'COST_TYPE',
						title: '成本类型',
						width:80,
						align:'center'
					  },{
						field: 'COST_NAME',
						title: '成本名称',
						width:150
					  },{
						field: 'COST_OWNER',
						title: '成本归属',
						width:80,
						align:'center',
						templet: function(row){
							var types = ['平台成本','客户成本','其他'];
							return types[row.COST_OWNER] || '其他';
						}
					  },{
						field: 'RELATE_TYPE',
						title: '成本来源',
						width:80,
						align:'center',
						templet: function(row){
							var types = ['其他','采购订单','个人报销'];
							return types[row.RELATE_TYPE] || '其他';
						}
					  },{
							field: 'SETTLEMENT_ID',
							title: '结算状态',
							width:80,
							align:'center',
							templet: function(d){
								if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
									return '<span style="color:#ff5722;">已结算</span>';
								} else {
									return '<span style="color:#009688;">未结算</span>';
								}
							}
						},{
						field: 'UNIT_PRICE',
						title: '单价',
						width:80,
						align:'center'
					  },{
						field: 'NUMBER',
						title: '数量',
						width:60,
						align:'center'
					  },{
						field: 'TOTAL_AMOUNT',
						title: '总金额',
						width:80,
						align:'center'
					  },{
						field: 'TAX_RATE',
						title: '税率%',
						width:80,
						align:'center'
					  },{
						field: 'NO_TAX_AMOUNT',
						title: '不含税金额',
						width:100,
						align:'center'
					  },{
						field: 'ORDER_NO',
						title: '采购订单号',
						width:110,
						templet:'<div><a href="javascript:;" onclick="purchaseDetail(\'{{d.ORDER_ID}}\',\'{{d.CUST_ID}}\')">{{d.ORDER_NO}}</a></div>'
					  },{
						field: 'ORDER_TIME',
						title: '采购下单时间',
						width:110,
						align:'center'
					  },{
						field: 'PAY_TIME',
						title: '付款时间',
						width:110,
						align:'center'
					  },{
						field: 'SUPPLIER_NAME',
						title: '供应商',
						width:120,
						templet:'<div><a href="javascript:;" onclick="supplierDetail(\'{{d.SUPPLIER_ID}}\')">{{d.SUPPLIER_NAME}}</a></div>'
					  },{
						field: 'REMARK',
						title: '备注',
						width:150
					  }
				  ]]}
				);
			},
			loadPlatforms: function(platformTypeId){
				$("[name='platformId']").render({data:{platformTypeId:platformTypeId}});
			},
			query:function(){
				$("#searchForm").queryData({id:'costListTable',jumpOne:true});
			}
		}
		
		$(function(){
			$("#searchForm").render({success:function(){
				CostList.init();
			}});
		});

		function platformDetail(platformId){
			popup.openTab({id: 'platformDetail', title: '平台详情', url: '${ctxPath}/pages/crm/platform/business-platform-detail.jsp', data: {platformId:platformId}});

		}

		function contractDetail(contractId){
			popup.openTab({
				id:'contractDetail',
				title:'合同详情',
				url:'${ctxPath}/project/contract',
				data:{contractId:contractId,isDiv:0}
			});
		}

		function custDetail(custId){
			popup.openTab({
				id:'custDetail',
				title:'客户详情',
				url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',
				data:{custId:custId,isDiv:0}
			});
		}

		function supplierDetail(supplierId){
			popup.openTab({
				id:'supplierDetail',
				url:"${ctxPath}/pages/erp/supplier/supplier-detail.jsp",
				title:'供应商详情',
				data:{supplierId:supplierId}
			});
		}

		function purchaseDetail(orderId,custId){
			popup.openTab({
				id:'orderDetail',
				title:'采购详情',
				url:"${ctxPath}/pages/erp/order/order-detail.jsp",
				data:{orderId:orderId,custId:custId}
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %> 