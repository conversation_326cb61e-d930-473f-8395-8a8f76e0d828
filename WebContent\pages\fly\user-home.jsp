<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>云趣乐享平台 - ${userInfo.userName}</title>
</EasyTag:override>
<EasyTag:override name="content">
	 <div class="fly-home fly-panel" style="background-image: url('');">
	  <img src="${userInfo.picUrl}" onerror="this.src='/yq-work/static/images/user-avatar-large.png'">
	  <i class="iconfont icon-renzheng"></i>
	  <h1>
	    ${userInfo.userName}&nbsp;
	    <c:choose>
	    	<c:when test=" ${userInfo.sex=='2'}"><i class="iconfont icon-nv"></i> </c:when>
	    	<c:otherwise><i class="iconfont icon-nan"></i></c:otherwise>
	    </c:choose>&nbsp;
	    <i class="layui-badge fly-badge-vip">VIP${userInfo.level}</i>
	    <!--
	    <span style="color:#c00;">（管理员）</span>
	    <span style="color:#5FB878;">（社区之光）</span>
	    <span>（该号已被封）</span>
	    -->
	  </h1>
	
	  <p style="padding: 10px 0; color: #5FB878;">认证信息：${userInfo.post}</p>
	
	  <p class="fly-home-info">
	    <i class="iconfont icon-kiss" title="积分"></i><span style="color: #FF7200;">${userInfo.points} 积分</span>
	    <i class="iconfont icon-shijian"></i><span>${userInfo.joinDate} 加入</span>
	    <i class="iconfont icon-chengshi"></i><span>来自${userInfo.workCity}</span>
	  </p>
	
	  <p class="fly-home-sign">（人生仿若一场修行）</p>
	
	  <div class="fly-sns" data-user="">
	    <a href="javascript:;" class="layui-btn layui-btn-primary fly-imActive" data-type="addFriend">加为好友</a>
	    <a href="javascript:;" class="layui-btn layui-btn-normal fly-imActive" data-type="chat">发起会话</a>
	  </div>
	
	</div>
	
	<div class="layui-container">
	  <div class="layui-row layui-col-space15">
	    <div class="layui-col-md6 fly-home-jie">
	      <div class="fly-panel">
	        <h3 class="fly-panel-title">${userInfo.userName} 最近的提问</h3>
	        <ul class="jie-row">
	          <c:forEach items="${userArticleList}" var="item">
		          <li>
		            <c:if test="${item.wonderful_flag=='1'}">
	              		<span class="fly-jing">精</span>
	            	</c:if>
		            <a href="/yq-work/fly/detail/${item.remind_id}" class="jie-title"> ${item.title }</a>
		            <i>${item.publish_time}</i>
		            <em class="layui-hide-xs">${item.view_count}阅/${item.comment_count}答</em>
		          </li>
	          </c:forEach>
	          <c:if test="${empty userArticleList}">
	         	 <div class="fly-none" style="min-height: 50px; padding:30px 0; height:auto;"><i style="font-size:14px;">没有发表任何求解</i></div>
	          </c:if>
	        </ul>
	      </div>
	    </div>
	    
	    <div class="layui-col-md6 fly-home-da">
	      <div class="fly-panel">
	        <h3 class="fly-panel-title">${userInfo.userName} 最近的回答</h3>
	        <ul class="home-jieda">
	        	<c:forEach items="${userCommentList}" var="item">
		         <li>
		          <p>
		            <span>${item.comment_time}</span>
		         	 在<a href="/yq-work/fly/detail/${item.remind_id}" target="_blank">${item.title}</a>中回答：
		          </p>
		          <div class="home-dacontent">
		            ${item.content}
		          </div>
		         </li>
	        	</c:forEach>
	        	  <c:if test="${empty userCommentList}">
		         	 <div class="fly-none" style="min-height: 50px; padding:30px 0; height:auto;"><span>没有回答任何问题</span></div>
	        	  </c:if>
	        </ul>
	      </div>
	    </div>
	  </div>
	</div>

</EasyTag:override>
 
<EasyTag:override name="script">
	<script type="text/javascript">
	 	layui.cache.page = 'user';
	 	layui.cache.user = {username: '${staffInfo.userName}' , uid: '${staffInfo.userId}', avatar: '${staffInfo.picUrl}', experience: '${staffInfo.points}'};
		layui.config({version: "3",base: '/yq-work/pages/fly/static/mods/'}).extend({fly: 'index'}).use(['fly','face'],function(){
		   var fly = layui.fly;
	       $('.home-dacontent').each(function(){
	         var othis = $(this), html = othis.html();
	         othis.html(fly.content(html));
	      });
		});
   </script>
</EasyTag:override>
<%@ include file="fly-layout.jsp" %>
