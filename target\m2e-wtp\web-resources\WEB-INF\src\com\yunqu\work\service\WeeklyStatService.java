package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.utils.WeekUtils;

public class WeeklyStatService extends AppBaseService implements Job{

	private static class Holder{
		private static WeeklyStatService service=new WeeklyStatService();
	}
	public static WeeklyStatService getService(){
		return Holder.service;
	}
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		this.run();
	}
	public  void run(){
		getLogger().info("执行周报统计 ....");
			List<String> depts=getDepts();
			for(String deptId:depts){
				List<String> userIds=getUserIds(deptId);
				if(userIds.size()==0)continue;
				JSONArray array = WeekUtils.getRecentWeek(WeekUtils.getYear(),15," 周报");
				for(int i=0;i<array.size();i++){
					JSONObject object = array.getJSONObject(i); 
					int weeklyCount = getWeeklyCount(userIds,object.getString("YEAR"),object.getString("WEEK_NO"));
					EasyRecord record=new EasyRecord("yq_weekly_stat","dept_id","year","weekly_no");
					record.set("year", object.getString("YEAR"));
					record.set("weekly_no", object.getString("WEEK_NO"));
					record.set("dept_id", deptId);
					record.set("weekly_count", weeklyCount);
					record.set("user_count", userIds.size());
					try {
						boolean bl = this.getQuery().update(record);
						if(!bl){
						    this.getQuery().save(record);
						}
					} catch (Exception e) {
						this.getLogger().error(null,e);
					}
				}
			}
	}

	private int getWeeklyCount(List<String> userIds,String year,String weeklyNo) {
		try {
			EasySQL sql=new EasySQL("select count(1) from yq_weekly where 1=1 ");
			sql.append(year, "and year = ?");
			sql.append(weeklyNo, "and week_no = ?");
			String[] strings = new String[userIds.size()];
			userIds.toArray(strings);
			sql.appendIn(strings, "and CREATOR");
			
			int count=this.getQuery().queryForInt(sql.getSQL(),sql.getParams());
			return count;
		} catch (SQLException e) {
			this.getLogger().error(e);
			return 0;
		}
	}
	private List<String> getUserIds(String deptId) {
		List<String> userIds=new ArrayList<String>();
		try {
			List<EasyRow> list=this.getMainQuery().queryForList("select t1.USER_ID from easi_dept_user t1 INNER JOIN easi_user t2 on t1.user_id = t2.user_id where t2.STATE=0 and t1.DEPT_ID= ?",deptId);
			if(list!=null){
				for(EasyRow row:list){
					userIds.add(row.getColumnValue("USER_ID"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return userIds;
	}
	private List<String> getDepts() {
		List<String> depts=new ArrayList<String>();
		try {
			List<EasyRow> list=this.getMainQuery().queryForList("select DEPT_ID from EASI_DEPT");
			if(list!=null){
				for(EasyRow row:list){
					depts.add(row.getColumnValue("DEPT_ID"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return depts;
	}
}
