<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>报销人员设置</title>
	<style>
		.select2-container .select2-selection--single{z-index: 9999!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form id = "searchForm"  name = "searchForm" onsubmit="return false;" class="form-inline" autocomplete="off">
			<div class="ibox">
				<div class="ibox-content">
				    <table class="table table-auto table-bordered table-hover table-condensed" id="tableHead">
                           <thead>
                        	  <tr>
                        	      <th>序号</th>
                        	      <th>ID</th>
                        	      <th>部门</th>
                        	      <th>部门主管</th>
						          <th>费用会计</th>
						          <th>中心副总</th>
						          <th>总经理</th>
						          <th>财务总监</th>
						          <th>会计主管</th>
						          <th>模板</th>
						          <th>部门编码(财务)</th>
						          <th>操作</th>
   						      </tr>
                           </thead>
                           <tbody id="dataList"  data-mars="BxDao.allDept" data-mars-reload="false" data-template="list-template">
                           </tbody>
                    </table>
                    <script id="list-template" type="text/x-jsrender">
								   {{for  data}}
										<tr>
											<td>{{:#index+1}}</td>
											<td>{{:D_CODE}}</td>  
											<td>{{:D_NAME}}</td>
											<td>
												<select class="form-control input-sm" data-value="{{:DEPT_MGR_ID}}" data-mars="CommonDao.userDict" name="DEPT_MGR_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:ACCT_ID}}" data-mars="CommonDao.userDict" name="ACCT_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:CENTER_ID}}" data-mars="CommonDao.userDict" name="CENTER_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:CEO_ID}}" data-mars="CommonDao.userDict" name="CEO_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:CFO_ID}}" data-mars="CommonDao.userDict" name="CFO_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:ACCT_MGR_ID}}" data-mars="CommonDao.userDict" name="ACCT_MGR_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<input class="form-control input-sm" value="{{:TEMP_NAME}}" name="TEMP_NAME_{{:D_ID}}" style="width:80px;"/>
											</td>
											<td>
												<input class="form-control input-sm" value="{{:FINANCE_DEPT_CODE}}" name="FINANCE_DEPT_CODE_{{:D_ID}}" style="width:80px;"/>
											</td>
											<td>
                                                <a href="javascript:void(0)" onclick="Config.saveData('{{:D_ID}}','{{:D_NAME}}','{{:D_CODE}}')" > 提交</a>
                                            </td>                                       
									    </tr>
								    {{/for}}					         
				    </script>
				</div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		$(function(){
			$("#searchForm").render({success:function(){
				$("#searchForm").render({success:function(){
					 requreLib.setplugs('select2',function(){
						$("#searchForm select").select2({theme: "bootstrap",placeholder:''});
						$(".select2-container").css("width","100%");
				 	 });
				}});
			}});
		});
		
		jQuery.namespace("Config");
		
		Config.saveData = function(deptId,deptName,deptCode){
			var data = form.getJSONObject("#searchForm");
			data['deptId'] = deptId;
			data['deptName'] = deptName;
			data['deptCode'] = deptCode;
			ajax.remoteCall("/yq-work/servlet/bx?action=savePeople",data,function(result) {
				if(result.state == 1){
				    alert(result.msg);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>