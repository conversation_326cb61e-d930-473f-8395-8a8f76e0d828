<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<c:set var="userData" value="<%=request.getUserPrincipal()%>" />
<EasyTag:override name="head">
	<title>定时通知</title>
	<style>
		#affairForm img{max-width: 98%!important;height: auto;}
		#affairForm .w-e-text{max-width: 100%!important;}
		#affairForm table,tr,td,p{max-width: 100%!important;}
		.layui-table-tips{z-index: 999999999999999999!important;};
		.btn-group-sm > .btn, .btn-sm{padding: 5px!important;}
		.multiselect-container, .select2-container{z-index: 9999999;}
	</style>
	<link href="/easitline-static/lib/select2/css/select2.min.css" rel="stylesheet">
	<link href="/easitline-static/lib/select2/css/select2-bootstrap.min.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
	     	<form class="ibox-content" id="affairForm" style="margin-bottom: 100px;min-height:calc(100vh - 10px)" data-mars="AffairDao.record" autocomplete="off" data-mars-prefix="affair.">
	     		   <input type="hidden" id="randomId" data-mars="AffairDao.randomId"/>
	     		   <input type="hidden" id="affairState" value="10" name="affair.AFFAIR_STATE"/>
	     		   <input type="hidden" id="affairId" value="${param.affairId}" name="affair.AFFAIR_ID"/>
	     		   <input type="hidden" value="${param.affairId}" name="fkId"/>
					<table class="table table-edit table-vzebra">
				        <tbody>
				            <tr>
			                    <td class="required">主题</td>
			                    <td colspan="3">
			                    	<input data-rules="required" maxlength="200" type="text" name="affair.AFFAIR_NAME" class="form-control input-sm">
			                    </td>
				            </tr>
				             <tr>
			                    <td class="required">收件人</td>
			                    <td colspan="3">
				                       <input name="affair.RECEIVER_USER_ID" type="hidden">
				                       <input name="affair.RECEIVER_USER_NAME" onclick="multiUser(this)" type="hidden" class="form-control input-sm"/>
				                       <select id="agentIds" multiple="multiple" class="form-control input-sm"></select>
			                    </td>
		                    </tr>
				             <tr class="hidden">
			                    <td>收件部门</td>
			                    <td colspan="3">
			                    	<input name="affair.RECEIVER_DEPT_ID" type="hidden">
			                    	<input name="affair.RECEIVER_DEPT_NAME" onclick="multiDept(this)" readonly="readonly" class="form-control input-sm">
			                    </td>
		                    </tr>
		                    <tr>
			                    <td>抄送人</td>
			                    <td>
		                    		<input name="affair.SHARE_IDS" type="hidden">
		                    		<input name="affair.SHARE_NAMES" type="hidden">
		                    		<select id="shareNames" multiple="multiple" class="form-control input-sm"></select>
			                    </td>
			                </tr>
		                    <c:choose>
		                    	<c:when test="${param.sendType=='0'}">
						             <tr>
					                    <td>通知时间</td>
					                    <td colspan="3"><input value="0" type="hidden" name="affair.SEND_TYPE">立即发送</td>
				                    </tr>
		                    	</c:when>
		                    	<c:otherwise>
						             <tr>
					                    <td>通知时间</td>
					                    <td colspan="3">
					                    	<select class="form-control input-sm" onchange="selectSendType(this.value)" name="affair.SEND_TYPE" style="display: inline-block;width: 100px;">
					                    		<option value="10">指定时间</option>
					                    		<option value="20">每月时间</option>
					                    	</select>
					                    	<span class="ml-10 t1">
						                    	指定时间：<input name="affair.EXCUTE_TIME" readonly="readonly" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-{%d+1} 10:00:00',minTime:'8:00',maxTime:'22:00',minDate:'%y-%M-{%d+1} 8:00',doubleCalendar:true,alwaysUseStartDate:true})" style="display: inline-block;width: 160px;" class="form-control input-sm Wdate">
					                    	</span>
					                    	<span class="ml-10 t2" style="display: none;">
					                    		<input value="10:00" type="hidden" name="affair.DAY_HOURS">
						                    	每月指定日期：<input name="affair.MONTH_DAYS" readonly="readonly" data-laydate="{type:'date',format:'d'}" style="display: inline-block;width: 60px;" class="form-control input-sm Wdate">
					                    		<small>早上10:00通知</small>
					                    	</span>
					                    </td>
				                    </tr>
		                    	</c:otherwise>
		                   </c:choose>
		                   <tr>
				                <td style="width: 80px">附件</td>
				               	<td colspan="3">
				               		<div data-template="template-files" data-mars="FileDao.fileList"></div>
				               		<div id="fileList"></div>
									<button class="btn btn-sm btn-link mt-5" type="button" onclick="$('#localfile').click()">+添加附件</button>
				               	</td>
				            </tr>
				            <tr>
				            	<td>描述</td>
				               	<td colspan="3">
				               	     <div id="editor"></div>
		                             <textarea id="wordText" data-text="false" style="height: 500px;width:400px;display: none;" class="form-control input-sm" name="affair.AFFAIR_DESC"></textarea>
				               	</td>
				            </tr>
				            <tr>
				            	<td></td>
				            	<td colspan="3">
				 					<button type="button" style="width: 100px;text-align: center;" class="btn btn-info btn-sm" onclick="Affair.ajaxSubmitForm('10')"><i class="layui-icon layui-icon-release"></i> 发 送  </button>
				            		<c:if test="${empty param.affairId||param.affairState=='0'}">
					 					<button type="button" style="width: 100px;text-align: center;margin-left: 10px;" class="btn btn-default btn-sm" onclick="Affair.ajaxSubmitForm('0')"><i class="layui-icon layui-icon-release"></i> 保存草稿  </button>
				            		</c:if>
				            	</td>
				            </tr>
				        </tbody>
	 				</table>
	  		</form>
  		 <script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Affair.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/pinyin.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/lib/select2/select2.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js?v=0314"></script>
	<script type="text/javascript">
		
		jQuery.namespace("Affair");
	    
		Affair.affairId='${param.affairId}';
		var affairId='${param.affairId}';
		
		var _height = $(window).height()-350;
		if(_height<300){
			_height = 300;
		}
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.config.pasteFilterStyle = true;
		editor.config.pasteIgnoreImg = false;
		editor.config.showLinkImg = false;
		editor.config.height = _height;
		editor.config.uploadImgShowBase64 = true
		editor.config.pasteTextHandle = function (content) {
		      if (content == '' && !content) return ''
		      var str = content
		      if(str.indexOf('MsoNormal')>-1){
		    	  layer.msg("复制word文档内容可能格式不正确,请对格式进行调整。",{icon:7});
		      }
		      if(str.indexOf('file://')>-1){
		    	  layer.msg("复制word文档附加的图片路径不存在,请单独复制图片。",{icon:7});
		      }
		      return str;
		} 
		//weUpload(editor,{uploadImgMaxLength:3});
		editor.config.onchange = function (html) {
		     $("#wordText").val(html)
		};
		editor.create();
		
		$(function(){
			$("#affairForm").render({success:function(result){
				 var c = $("#wordText").val();
				 editor.txt.html(c);
				 if(Affair.affairId){
					 var obj = result['AffairDao.record'].data;
					 var receiverUserId = obj['RECEIVER_USER_ID'];
					 var receiverUserName = obj['RECEIVER_USER_NAME'];
					 
					 var arr1 = receiverUserId.split(',');
					 var arr2 = receiverUserName.split(',');
					 for(var index in arr1){
						 var newOption = new Option(arr2[index], arr1[index], true, true);
						 $('#agentIds').append(newOption);
					 }
					 
					 var shareIds = obj['SHARE_IDS'];
					 var shareNames = obj['SHARE_NAMES'];
					 if(shareIds){
						 var arr3 = shareIds.split(',');
						 var arr4 = shareNames.split(',');
						 for(var index in arr3){
							 var newOption = new Option(arr4[index], arr3[index], true, true);
							 $('#shareNames').append(newOption);
						 }
					 }
					 
					 $('#agentIds,#shareNames').trigger('change');
					 
					 selectSendType(obj.SEND_TYPE);
				 }
				 
				 
				 renderDate('#affairForm');
				 
				 $("#affairForm #agentIds,#affairForm #shareNames").select2({
					 theme: "bootstrap",
					 placeholder:'请选择',
					 allowClear: true,
					 ajax: {
						type:'post',
					    url: '/yq-work/webcall?action=CommonDao.selectUserInfo',
					    dataType: 'json',
					    language: "zh-CN",
					    dropdownParent:$("#affairForm"),
					    tags: true,
					    allowClear: true,
					    data:function(params){
					    	return {info:params.term, page: params.page};
					    },
					    processResults: function (result, params) {
				             params.page = params.page || 1;
				             var itemList = [];
				             var list = result.data
				             for(var index in list){
				                 itemList.push({id: list[index]['USER_ID'], text: list[index]['USERNAME']})
				             }
				             return {results: itemList,pagination: {more: (params.page * 30) < result.total}};
				         },
				         cache: true,
				         success:function(){
				        	 
				         }
					  }
				 });
				 
				 var userId = getUrlParam('userId');
				 if(userId){
					 var el = $('#agentIds');
					 el.hide();
					 el.next().hide();
					 el.prev().attr('type','text').attr('readonly','readonly');
					 el.prev().val('${param.userName}');
					 el.prev().prev().val(userId);
				 }
				 
			}});  
		});
		
		Affair.uploadFile = function(callback){
			var fkId='';
			if(Affair.affairId){
				fkId=affairId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'callback',fileMaxSize:(1024*20),fkId:fkId,source:'affair'});
		}
		var callback = function(data){
			var el = $("[name='affair.AFFAIR_NAME']");
			var wordText = $("#wordText");
			if(el.val()==''){
				el.val(data.name);
			}
			if(wordText.val()==''){
				wordText.val('请查收附件.');
				editor.txt.html('请查收附件.');
			}
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="${ctxPath}/fileview?action=show&path='+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
		}
		
		Affair.ajaxSubmitForm = function(affairState){
			var _affairState = $('#affairState').val();
			if(_affairState==''||_affairState<=10){
				$('#affairState').val(affairState);
			}
			if(form.validate("#affairForm")){
				var agentIds = $("#agentIds").val()||[];
				$('#agentIds').prev().prev().val(agentIds.join());
				$('#agentIds').prev().val(getTexts('agentIds'));
				
				var shareNames = $("#shareNames").val()||[];
				$('#shareNames').prev().prev().val(shareNames.join());
				$('#shareNames').prev().val(getTexts('shareNames'));
				
				
				if(Affair.affairId){
					Affair.updateData(); 
				}else{
					Affair.insertData(); 
				}
				
				function getTexts(id){
					var array = [];
					var list = $('#'+id).select2('data');
					for(var index in list){
						array.push(list[index]['text']);
					}
					return array.join();
				}
			};
		}
		Affair.insertData = function(flag) {
			$("#affairId").val($("#randomId").val());
			var data = form.getJSONObject("#affairForm");
			ajax.remoteCall("${ctxPath}/servlet/affair?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						location.href = '/yq-work/affair/send';
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Affair.updateData = function(flag) {
			var data = form.getJSONObject("#affairForm");
			ajax.remoteCall("${ctxPath}/servlet/affair?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						location.href = '/yq-work/affair/send';
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function selectSendType(val){
			if(val=='10'){
				$('.t1').show();
				$('.t2').hide();
			}else{
				$('.t2').show();
				$('.t1').hide();
			}
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>