<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editTaskDelayForm" autocomplete="off">
	     	   <input type="hidden" id="taskId" value="${param.taskId}" name="taskId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
			            	<td style="vertical-align: top;width: 80px;">延期至：</td>
		                    <td>
		                    	<input type="hidden" name="bdate" value="${param.deadlineAt}"/>
		                    	<input type="text" value="${param.deadlineAt}"  data-rules="required" placeholder="截至时间" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 18:00:00',doubleCalendar:true,alwaysUseStartDate:true,minTime:'08:00:00',maxTime:'22:00:00'})" name="date" class="form-control input-sm Wdate">
		                    </td>
		                </tr>
		                <tr>
		                    <td>延期理由</td>
		                    <td>
		                    	<textarea style="height: 110px;"  data-rules="required"  maxlength="500" name="reason" placeholder="请填写合理延期原因,不要随意输入" class="form-control input-sm"></textarea>
		                    	<small>请详细填写合理的延期理由。若申请延期会影响项目进度，务必先与任务发起人、部门主管沟通汇报，在获得双方同意后，再提交延期申请 。</small>
		                    </td>
		                </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="TaskForward.ajaxSubmitForm()"> 确 定  </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
       
	    var TaskForward={};
		TaskForward.ajaxSubmitForm = function(flag) {
			if(form.validate("#editTaskDelayForm")){
				var data = form.getJSONObject("#editTaskDelayForm");
				if(data['reason'].length<4){
					layer.msg('不要随意填写延期理由',{icon:7,offset:'20px',time:1200});
					return;
				}
				ajax.remoteCall("${ctxPath}/servlet/task?action=delayTask",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							initFormData();
							popup.layerClose("#editTaskDelayForm");
						});
					}else{
						layer.alert('请选择抄送人',{icon: 5});
					}
				});
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>