//console-echarts.js : 数据看板、仪表盘的echarts函数

//饼图,withWan时tooltip和label带单位万
function loadPieChart(echartsData, chartName, withWan = false,color2Flag = false) {
    var color1 = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"];
    var color2 = ["#63b2ee", "#76da91", "#f8cb7f", "#f89588", "#7cd6cf", "#9192ab", "#7898e1", "#efa666", "#eddd86", "#9987ce","#91cc75"];

    var options1 = {
        color: color2Flag ? color2 : color1,
        tooltip: {
            trigger: "item",
            formatter: withWan ? "{b}: {c}万 ({d}%)" : "{b}: {c}<br> ({d}%)"
        },
        grid: {
            left: '35%',
            right: '35%',
            top: '1%',
            bottom: '0%',
            containLabel: false
        },
        series: [
            {
                name: "",
                type: "pie",
                radius: ['20%', '50%'],
                data: echartsData.length > 0 ? echartsData : [{
                    name: '暂无数据',
                    value: 0
                }],
                label: {
                    show: true,
                    position: 'top',
                    formatter: withWan ? '{b}: {c} 万({d}%)' : '{b}: {c} \n({d}%)',
                    lineHeight: 12,
                },
                labelLine: {
                    show: true,
                    smooth: true,
                    length: 1,
                }
            },
        ]
    };
    var myCharts = echarts.init(document.getElementById(chartName));
    myCharts.setOption(options1);
    return myCharts;
}

//玫瑰型饼图，左右分开40%的空间
function loadRosePieChart(echartsData, tableName) {
    var options1 = {
        color: ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"],
        tooltip: {
            trigger: "item",
            formatter: "{b}: {c}<br> ({d}%)"
        },
        grid: {
            left: '40%',
            right: '40%',
            bottom: '0%',
            containLabel: false
        },
        series: [
            {
                name: "",
                type: "pie",
                radius: ['20%', '55%'],
                center: ['55%', '50%'],
                roseType: "radius",
                data: echartsData.length > 0 ? echartsData : [{
                    name: '暂无数据',
                    value: 0
                }],
                minAngle: 17,
                label: {
                    show: true,
                    position: 'outside',
                    lineHeight: 14,
                    rich: {
                        bValue: {
                            fontSize: 14,
                        },
                        cValue: {
                            fontSize: 14,
                        },
                        dValue: {
                            fontSize: 14,
                        }
                    },
                    formatter: function (params) {
                        if (params.dataIndex >= 7) {
                            return '{bValue|' + params.data.name + '}:{cValue|' + params.data.value + '}({dValue|' + params.percent + '%})';
                        }
                        return '{bValue|' + params.data.name + '}:{cValue|' + params.data.value + '}\n({dValue|' + params.percent + '%})';
                    },
                },
                labelLine: {
                    normal: {
                        length: 7,
                        length2: 2
                    }
                },
            },

        ]
    };
    var myCharts = echarts.init(document.getElementById(tableName));
    myCharts.setOption(options1);
}

//报销看板的玫瑰图
function loadRosePieChart2(echartsData,tableName){
    var options1 = {
        tooltip: {
            trigger: "item",
            formatter: "{b}: {c}万 ({d}%)"
        },
        series: [
            {
                name: "花销数据",
                type: "pie",
                radius: ['20%', '55%'],
                center: ['52%', '50%'],
                roseType: "radius",
                data: echartsData.length > 0 ? echartsData : [{
                    name: '暂无数据',
                    value: 0
                }],
                minAngle: 2,
                itemStyle: {
                    color: function (params) {
                        var colorList = ["#63b2ee", "#76da91", "#f8cb7f", "#f89588", "#7cd6cf", "#9192ab", "#7898e1", "#efa666", "#eddd86", "#9987ce","#91cc75"];
                        return colorList[params.dataIndex % colorList.length];
                    }
                },
                label: {
                    show: true,
                    position: 'top',
                    formatter: '{b}: {c} 万({d}%)'
                },
                labelLine: {
                    show: true
                }
            },

        ]
    };
    var myCharts = echarts.init(document.getElementById(tableName), myEchartsTheme);
    myCharts.setOption(options1);

    return myCharts;
}

//柱形图, 传参withWan为true时加上'万'为单位和后缀
function loadBarChart(fieldName, amounts, tableName, legend, withWan = false) {
    var options = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                var result = params[0].name + '<br/>';
                params.forEach(function (item) {
                    result += item.marker + item.seriesName + ': ' + (item.value + (withWan ? '万' : '')) + '<br/>';
                });
                return result;
            }
        },
        legend: {
            data: legend
        },
        grid: {
            left: '2%',
            right: '2%',
            bottom: '1%',
            containLabel: true
        },
        barGap: '0%',
        barWidth: '50%',
        xAxis: {
            type: 'category',
            data: fieldName,
            axisLabel: {
                rotate: 45,
                interval: 0
            }
        },
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    formatter: withWan?'{value}万':'{value}'
                }
            }
        ],
        series: [
            {
                name: legend,
                type: 'bar',
                data: amounts,
                label: {
                    show: true,
                    position: 'top',
                    color:'#fff',
                    textBorderColor:'#5b8ff9',
                    textBorderWidth: 2,
                    formatter: function (params) {
                        return withWan?params.value + '万':params.value;
                    }
                },
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {offset: 0, color: '#7ca3fa'},
                        {offset: 1, color: '#5b8ff9'}
                    ])
                }
            }
        ],
    };

    var chart = echarts.init(document.getElementById(tableName));
    chart.setOption(options);

    return chart;
}

// 柱形+折线，使用同一条y轴
function loadBarAndLineChart(fieldName, amounts1, amounts2, tableName, legend, colorStyle) {
    const defaultStyle = {
        bar1: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: '#ff7c6e'},
                {offset: 1, color: '#FF9F7F'}
            ])
        },
        textColor: '#e86b56',
    };

    // 蓝绿系配色方案
    const blueStyle = {
        bar1: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: '#36A2EB'},
                {offset: 1, color: '#73C0DE'}
            ])
        },
        textColor: '#36A2EB',
    };

    // 黄色系配色方案
    const yellowStyle = {
        bar1: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {offset: 0, color: '#FFD700'},  // 金黄色
                {offset: 1, color: '#FFC300'}   // 橙色
            ])
        },
        textColor:'#FFA500',
    };

    var style = defaultStyle;

    if(colorStyle === 'blueStyle'){
        style = blueStyle;
    }else if(colorStyle === 'yellowStyle') {
        style = yellowStyle;
    }

    var options = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
        },
        legend: {
            data: legend
        },
        grid: {
            left: '6%',
            right: '4%',
            bottom: '1%',
            containLabel: true
        },
        barGap: '0%',
        barWidth: '50%',
        xAxis: {
            type: 'category',
            data: fieldName,
            axisLabel: {
                rotate: 45,
                interval: 0
            }
        },
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}'
                }
            }
        ],
        series: [
            {
                name: legend[0],
                type: 'bar',
                data: amounts1,
                label: {
                    show: true,
                    position: 'top',
                    distance: 0,
                    color: '#fff',
                    textBorderColor: style.textColor,
                    textBorderWidth: 2,
                },
                barWidth: '40%',
                itemStyle: {
                    borderRadius: [4, 4, 0, 0],
                    color: style.bar1.color
                },
            },
            {
                name: legend[1],
                type: 'line',
                data: amounts2,
                smooth: false,
                color: '#000',
                label: {
                    color: '#fff',
                    textBorderColor: '#000',
                    textBorderWidth: 2,
                    show: true,
                    position: 'inside',
                    formatter: function (params) {
                        if (Number(amounts1[params.dataIndex]) === Number(amounts2[params.dataIndex])) {
                            return '';
                        }
                        return amounts2[params.dataIndex];
                    },
                },
            }
        ],
    };

    var chart = echarts.init(document.getElementById(tableName));
    chart.setOption(options);

    return chart;
}

// 柱形+折线，使用两个y轴;折线图只显示top3的值
function loadBarAndLine2yAxisChart(fieldName, amounts1, amounts2, tableName, legend) {
    var sortedAmounts2 = [...amounts2].sort((a, b) => b - a);
    var thirdNum = Number(sortedAmounts2[2]);
    var options = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
        },
        legend: {
            data: legend
        },
        grid: {
            left: '6%',
            right: '4%',
            bottom: '1%',
            containLabel: true
        },
        barGap: '0%',
        barWidth: '50%',
        xAxis: {
            type: 'category',
            data: fieldName,
            axisLabel: {
                rotate: 45,
                interval: 0
            }
        },
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}'
                }
            } ,{
                type: 'value',
                axisLabel: {
                    formatter: '{value}'
                },
                splitLine:{
                    show:false
                },
                max: function(value) {
                    return Math.ceil(value.max * 1.5);
                }
            }
        ],
        series: [
            {
                name: legend[0],
                type: 'bar',
                data: amounts1,
                label: {
                    show: true,
                    position: 'top',
                    distance:0,
                    color: '#fff',
                    textBorderColor: '#e86b56',
                    textBorderWidth: 2,
                },
                itemStyle: {
                    borderRadius: [4, 4, 0, 0],
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {offset: 0, color: '#ff7c6e'},
                        {offset: 1, color: '#FF9F7F'}
                    ])
                },
            },
            {
                name: legend[1],
                type: 'line',
                data: amounts2,
                smooth: false,
                yAxisIndex: 1,
                itemStyle: {
                    color:  "#000"
                },
                label: {
                    show: true,
                    position: 'top',
                    color:  "#FFF",
                    textBorderColor:'#000',
                    textBorderWidth: 1,
                    formatter: function (params) {
                        return amounts2[params.dataIndex] >= thirdNum ? amounts2[params.dataIndex] : '';
                    },
                },
            }

        ],
    };

    var chart = echarts.init(document.getElementById(tableName));
    chart.setOption(options);

    return chart;
}

//双柱形图，单y轴，红+橙配色
function loadTwoBarChart(fieldName, amounts1, amounts2, tableName, legend) {
    var options = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
        },
        legend: {
            data: legend
        },
        grid: {
            left: '7%',
            right: '4%',
            bottom: '1%',
            containLabel: true
        },
        barGap: '0%',
        barWidth: '30%',
        xAxis: {
            type: 'category',
            data: fieldName,
            axisLabel: {
                rotate: 45,
                interval: 0
            }
        },
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}'
                }
            },
        ],
        series: [
            {
                name: legend[0],
                type: 'bar',
                data: amounts1,
                label: {
                    show: true,
                    position: 'top',
                    distance:0,
                    offset: [-3, 0]
                },
                itemStyle: {
                    color: '#FF6F61',
                    opacity: 0.85
                }
            },
            {
                name: legend[1],
                type: 'bar',
                data: amounts2,
                label: {
                    show: true,
                    position: 'top',
                    distance:0,
                    offset: [2, 0]
                },
                itemStyle: {
                    color: '#FFB74D',
                    opacity: 0.85
                }
            },
        ]
    };

    var chart = echarts.init(document.getElementById(tableName));
    chart.setOption(options);

    return chart;
}


//双柱形图+单y轴，传入今年、去年的数据，自带单位'万' ; 配色蓝+绿，不计算增长率;只显示thisYearAmounts的数字
function loadTwoBarChart2(fieldName, thisYearAmounts, lastYearAmounts, tableName, legend){
    let existingChart = echarts.getInstanceByDom(document.getElementById(tableName));
    if (existingChart) {
        existingChart.dispose();
    }
    if (legend == null) {
        legend = ['时段内', '上一年同期']
    }

    var options = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                var result = params[0].name + '<br/>';
                params.forEach(function (item) {
                    if (item.seriesType === 'bar') {
                        result += item.marker + item.seriesName + ': ' + (item.value + '万') + '<br/>';
                    }
                });
                return result;
            }
        },
        legend: {
            data: legend
        },
        grid: {
            left: '2%',
            right: '0%',
            bottom: '2%',
            containLabel: true
        },
        barGap: '0%',
        barCategoryGap: '50%',
        xAxis: {
            type: 'category',
            data: fieldName,
            axisLabel: {
                rotate: 45,
                interval: 0
            }
        },
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}万'
                }
            },
        ],
        series: [
            {
                name: legend[0],
                type: 'bar',
                data: thisYearAmounts,
                barWidth: '40%',
                label: {
                    show: true,
                    position: 'top',
                    offset: [-3, 0],
                    formatter: function (params) {
                        return params.value + '万';
                    },
                    textStyle: {
                        fontWeight: 'bold',
                        fontSize: 13,
                        color: '#ffffff',  // 内部白色
                        textBorderColor: '#4c86fc',  // 外描边蓝色
                        textBorderWidth: 2  // 描边宽度
                    }
                },
                itemStyle: {
                    color: '#5B8FF9',
                    opacity: 0.85
                },
                z: 3
            },
            {
                name: legend[1],
                type: 'bar',
                data: lastYearAmounts,
                barWidth: '40%',
                itemStyle: {
                    color: '#61DDAA',
                    opacity: 0.85
                },
                z:1
            },
        ]
    };

    var chart = echarts.init(document.getElementById(tableName));
    chart.setOption(options);

    return chart;
}


//双柱形图+折线，双y轴，红+橙+黑配色
function loadTwoBarAndLineChart(fieldName, amounts1, amounts2, amounts3, tableName, legend) {
    var options = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
        },
        legend: {
            data: legend
        },
        grid: {
            left: '7%',
            right: '4%',
            bottom: '1%',
            containLabel: true
        },
        barGap: '0%',
        barWidth: '30%',
        xAxis: {
            type: 'category',
            data: fieldName,
            axisLabel: {
                rotate: 45,
                interval: 0
            }
        },
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}'
                }
            },
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}'
                },
                splitLine:{
                    show:false
                }
            },
        ],
        series: [
            {
                name: legend[0],
                type: 'bar',
                data: amounts1,
                label: {
                    show: true,
                    position: 'inside',
                    offset: [-3, 0]
                },
                itemStyle: {
                    color: '#FF6F61',
                    opacity: 0.85
                }
            },
            {
                name: legend[1],
                type: 'bar',
                data: amounts2,
                label: {
                    show: true,
                    position: 'inside',
                    offset: [2, 0]
                },
                itemStyle: {
                    color: '#FFB74D',
                    opacity: 0.85
                }
            },
            {
                name: legend[2],
                type: 'line',
                data: amounts3,
                smooth: false,
                yAxisIndex: 1,
                symbol: 'circle',
                symbolSize: 3,
                lineStyle: {
                    width: 2,
                    color: '#333'
                },
                itemStyle: {
                    color: '#fff',  // 中间空心为白色
                    borderWidth: 2,
                    borderColor: '#000'  // 描边颜色为黑色
                },
                label: {
                    show: true,
                    position: 'top',
                    color: '#333',
                }
            }
        ]
    };

    var chart = echarts.init(document.getElementById(tableName));
    chart.setOption(options);
    return chart;
}

//双柱形图+折线(增长率%)+双y轴，传入今年、去年的数据，自带单位'万'和'%' ; 当ratio不为空、withRatio= true时，用自带的ratio而不是默认计算增长率
function loadTwoBarWithRatioLineChart(fieldName, thisYearAmounts, lastYearAmounts, tableName, legend,ratio = null,withRatio = false){
    if (legend == null) {
        legend = ['时段内', '上一年同期', '增长率']
    }
    if(!withRatio || ratio == null){
        //无传入比例，则计算增长率
        ratio = thisYearAmounts.map((thisYear, index) => {
            var lastYear = lastYearAmounts[index];
            if (lastYear === 0 || lastYear == "0.00") {
                return 0;
            }
            return ((thisYear - lastYear) / lastYear * 100).toFixed(2);
        });
    }
    var sortedRatios = [...ratio].sort((a, b) => b - a);

    var ratio3 = parseFloat(sortedRatios[3]);
    ratio3 = ratio3 > 100 ? ratio3 : 100; // 提高阈值到100%
    ratio3 = (ratio3 > 30 && ratio3 <= 100) ? ratio3 : 30;
    ratio3 = ratio3 < 0? 0 : ratio3;

    var options3 = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                var result = params[0].name + '<br/>';
                params.forEach(function (item) {
                    if (item.seriesType === 'bar') {
                        result += item.marker + item.seriesName + ': ' + (item.value + '万') + '<br/>';
                    } else if (item.seriesType === 'line') {
                        result += item.marker + item.seriesName + ': ' + item.value + '%<br/>';
                    }
                });
                return result;
            }
        },
        legend: {
            data: legend
        },
        grid: {
            left: '4%',
            right: '2%',
            bottom: '3%',
            containLabel: true
        },
        barGap: '0%',
        barCategoryGap: '50%',
        xAxis: {
            type: 'category',
            data: fieldName,
            axisLabel: {
                rotate: 45,
                interval: 0
            }
        },
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}万'
                }
            },
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}%'
                },
                splitLine:{
                    show:false
                }
            }
        ],
        series: [
            {
                name: legend[0],
                type: 'bar',
                data: thisYearAmounts,
                barWidth: '40%',
                label: {
                    show: true,
                    position: 'top',
                    offset: [-3, 0],
                    formatter: function (params) {
                        return params.value.substring(0,params.value.length - 1) + '万';
                    },
                    textStyle: {
                        fontWeight: 'bold',
                        fontSize: 13,
                        color: '#ffffff',  // 内部白色
                        textBorderColor: '#4c86fc',  // 外描边蓝色
                        textBorderWidth: 2  // 描边宽度
                    }
                },
                itemStyle: {
                    color: '#5B8FF9',
                    opacity: 0.85
                },
                z: 3
            },
            {
                name: legend[1],
                type: 'bar',
                data: lastYearAmounts,
                barWidth: '40%',
                itemStyle: {
                    color: '#61DDAA',
                    opacity: 0.85
                },
                z:1
            },
            {
                name: legend[2],
                type: 'line',
                yAxisIndex: 1,
                data: ratio.map(Number),
                smooth: false,
                connectNulls: false, // 不连接空值点
                itemStyle: {
                    normal: {
                        color: function(params) {
                            return  params.value >= ratio3 ? '#f8acb7' : '#fff';
                        },
                        borderColor: '#000',
                        borderWidth: 2
                    }
                },
                symbol: 'circle',
                symbolSize: 8,
                lineStyle: {
                    width: 2,
                    type: 'solid'
                },
                label: {
                    show: true,
                    position: 'top',
                    lineHeight:18,
                    backgroundColor: 'rgba(255,255,255,0.8)',
                    borderRadius: 3,
                    padding: [0, 1],
                    formatter: function(params) {
                        return params.value > ratio3 ? params.value + '%' : '';
                    },
                    textStyle: {
                        color: '#9b4f5d',
                        fontWeight: 'bold'
                    },
                },
                z:2
            }
        ]
    };

    var chart = echarts.init(document.getElementById(tableName));
    chart.setOption(options3);

    return chart;
}

//横向柱形图，用来表示分布
function loadHorizontalBarChart(fieldName, nums, chartName,title1) {
    var totalAmount = nums.reduce((sum, value) => sum + Number(value), 0);

    var option = {
        title: {
            text:  title1
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params) {
                if (totalAmount === 0) {
                    return params[0].name + ':' + params[0].value;
                }
                var percentage = ((Number(params[0].value) / totalAmount) * 100).toFixed(2);
                return params[0].name + ': ' + params[0].value + '\n占比: ' + percentage + '%';
            },
        },
        grid: {
            left: '3%',
            right: '17%',
            bottom: '3%',
            top: title1 ? '10%' : '0%',
            containLabel: true
        },
        barGap: '0%',
        barWidth: '80%',
        xAxis: {
            type: 'value',
        },
        yAxis: {
            type: 'category',
            data: fieldName,
            axisLabel: {
                show: true,
                interval: 0,
            }
        },
        series: [
            {
                name: '',
                type: 'bar',
                data: nums,
                itemStyle: {
                    color: function (params) {
                        var colorList = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"];
                        return colorList[params.dataIndex % colorList.length];
                    }
                },
                label: {
                    show: true,
                    position: 'right',
                    lineHeight: 12,
                    textStyle: {
                        fontWeight: 'bold',
                    },
                    formatter: function (params) {
                        if (totalAmount === 0) {
                            return params.name + ':' + params.value;
                        }
                        var percentage = ((Number(params.value) / totalAmount) * 100).toFixed(2);
                        return params.name + ': ' + params.value + '\n占比: ' + percentage + '%';
                    }
                },
            },
        ]
    };
    var myChart = echarts.init(document.getElementById(chartName));
    myChart.setOption(option)
}

function loadTwoLineChart(fieldName,amount1,amount2,legend,chartName){
    var options = {
        color: ['#36A2EB', '#FF6384'],  // 使用蓝色和玫红色
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
        },
        legend: {
            data: legend
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: fieldName,
            axisLabel: {
                rotate: 45,
                interval: 0
            }
        },
        yAxis: [
            {
                type: 'value',
                axisLabel: {
                    formatter: '{value}'
                }
            },
        ],
        series: [
            {
                name: legend[0],
                type: 'line',
                yAxisIndex: 0,
                data: amount1,
                smooth: false,
                lineStyle: {
                    width: 4  // 加粗线条
                },
                symbolSize: 8,  // 增大数据点的大小
                label: {
                    show: true,
                    position: 'top',
                    formatter: function (params) {
                        return amount1[params.dataIndex] > 0 ? amount1[params.dataIndex] : '';
                    },
                    textStyle: {
                        fontSize: 13,
                        fontWeight: 'bold',
                    },
                },
                itemStyle: {
                    borderWidth: 2  // 数据点边框加粗
                }
            },
            {
                name: legend[1],
                type: 'line',
                yAxisIndex: 0,
                data: amount2,
                smooth: false,
                lineStyle: {
                    width: 4  // 加粗线条
                },
                symbolSize: 8,  // 增大数据点的大小
                label: {
                    show: true,
                    position: 'top',
                    formatter: function (params) {
                        return amount2[params.dataIndex] > 0 ? amount2[params.dataIndex] : '';
                    },
                    textStyle: {
                        fontSize: 13,
                        fontWeight: 'bold',
                    },
                },
                itemStyle: {
                    borderWidth: 2  // 数据点边框加粗
                }
            },
        ]
    };

    var chart = echarts.init(document.getElementById(chartName));
    chart.setOption(options);
}

//金字塔图
function loadPyramidChart(data,title,chartName){
    var option = {
        title: {
            text:  title
        },
        color: ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"],
        tooltip: {
            trigger: 'item',
            formatter: "{b}: {c} <br>占比: {d}%"
        },
        series: [
            {
                name: '',
                type: 'funnel',
                left: '0%',
                width: '80%',
                minSize: '0%',
                maxSize: '100%',
                sort: 'ascending',
                funnelAlign: 'center',
                gap: 2,
                label: {
                    show: true,
                    position: 'right',
                    textStyle: {
                        fontWeight: 'bold',
                    },
                    formatter: '{b}: {c} ({d}%)'
                },
                labelLine: {
                    length: 10,
                    lineStyle: {
                        width: 1,
                        type: 'solid'
                    }
                },
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 1
                },
                data: data
            }
        ]
    };

    var chart = echarts.init(document.getElementById(chartName));
    chart.setOption(option)
    return chart;
}
