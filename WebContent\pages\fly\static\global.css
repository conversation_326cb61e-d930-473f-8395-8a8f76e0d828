/**
 @Name: Fly社区
 @Author: 贤心
 */
/* 全局 */
html,body{overflow-x: hidden;}
html body{margin-top: 61px;}
html{background-color: #F2F2F2;}
i{font-style: normal;}
/* 图标 */
@font-face {font-family: "layui-icon-fly";
  src: url('iconfont.eot?t=1512007250695'); /* IE9*/
  src: url('iconfont.eot?t=1512007250695#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('data:application/x-font-woff;charset=utf-8;base64,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') format('woff'),
  url('iconfont.ttf?t=1512007250695') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('iconfont.svg?t=1512007250695#iconfont') format('svg'); /* iOS 4.1- */
}
.iconfont {
  font-family:"layui-icon-fly" !important;
  font-size:16px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}
.icon-zan:before{content:"\e612"}
.icon-jiazaizhong:before{content:"\e60e"}
.icon-sousuo:before{content:"\e621"}
.icon-quanpingpad:before{content:"\e61d"}
.icon-shezhi:before{content:"\e607"}
.icon-renzhengv:before{content:"\e62b"}
.icon-shijian:before{content:"\e60a"}
.icon-guanbi:before{content:"\e614"}
.icon-tianjia:before{content:"\e616"}
.icon-tuichu:before{content:"\e601"}
.icon-shui:before{content:"\e602"}
.icon-qq:before{content:"\e618"}
.icon-weibo:before{content:"\e617"}
.icon-tupian:before{content:"\e608"}
.icon-logo:before{content:"\e603"}
.icon-daima:before{content:"\e609"}
.icon-biaoqing:before{content:"\e60f"}
.icon-nan:before{content:"\e619"}
.icon-nv:before{content:"\e61a"}
.icon-quitquanping:before{content:"\e61e"}
.icon-zuichun:before{content:"\e61c"}
.icon-charushuipingxian:before{content:"\e622"}
.icon-yulan:before{content:"\e60d"}
.icon-liulanyanjing:before{content:"\e60b"}
.icon-touxiang:before{content:"\e604"}
.icon-caina:before{content:"\e613"}
.icon-room:before{content:"\e615"}
.icon-svgmoban53:before{content:"\e610"}
.icon-shichang:before{content:"\e600"}
.icon-shouye:before{content:"\e605"}
.icon-tishilian:before{content:"\e629"}
.icon-fabu:before{content:"\e606"}
.icon-pinglun:before{content:"\e60c"}
.icon-zan1:before{content:"\e611"}
.icon-chengshi:before{content:"\e61b"}
.icon-lianjie:before{content:"\e620"}
.icon-yulan1:before{content:"\e785"}
.icon-renshu:before{content:"\e61f"}
.icon-huizongzuoyoutuodong:before{content:"\e623"}
.icon-404:before{content:"\e627"}
.icon-iconmingxinganli:before{content:"\e652"}
.icon-wenda:before{content:"\e626"}
.icon-top:before{content:"\e624"}
.icon-ui:before{content:"\e625"}
.icon-fengexian:before{content:"\e63a"}
.icon-jiacu:before{content:"\e62f"}
.icon-kiss:before{content:"\e6cd"}
.icon-biaoqing1:before{content:"\e63b"}
.icon-emwdaima:before{content:"\e62a"}
.icon-jifen:before{content:"\e632"}
.icon-aqrenzheng:before{content:"\e62d"}
.icon-pinglun1:before{content:"\e631"}
.icon-yxj-expression:before{content:"\e628"}
.icon-tongzhi:before{content:"\e647"}
.icon-pinglun2:before{content:"\e62c"}
.icon-jiaoliu:before{content:"\e6b7"}
.icon-renzheng:before{content:"\e62e"}
/* 辅助 */
a:hover{color: #009688; transition: all .3s;}
pre{padding: 10px 15px; margin: 10px 0; font-size: 12px; border-left: 6px solid #009688;  background-color: #f8f8f8; font-family: Courier New; overflow: auto;}
.layui-container{padding: 0;}
.fly-main{width: 1079px; min-height: 600px; margin: 0 auto 15px;}
.layui-badge{height: 20px; line-height: 20px; border-radius: 2px;}
.fly-link{color: #01AAED;}
.fly-link:hover{color: #5FB878;}
.fly-grey{color: #999;}
.fly-msg, .fly-error{padding: 10px 15px; line-height: 24px;}
.fly-msg{background-color:#F8F8F8; color:#666;}
.fly-msg a{color:#4F99CF}
.fly-editbox{position: relative;}
.fly-marginTop{margin-top: 15px;}
.fly-mid{display: inline-block; height: 10px; width: 1px; margin: 0 10px; vertical-align: middle; background-color: #e2e2e2;}
.fly-right{position: absolute; right: 15px; top: 0;}
/* 过度 */
.fly-loading{position: absolute; top: 50%; left: 50%; margin: -12px 0 0 -15px; font-size: 30px; color: #c2c2c2;}
/* 头像 */
.fly-avatar{position: absolute; left: 15px; top: 15px;}
.fly-avatar img{display: block; width: 45px; height: 45px; margin: 0; border-radius: 2px;}
/* 徽章 */
.fly-badge-vip{height: 16px; line-height: 16px; padding: 0 3px; background-color: #FF5722; color: #fff; border-radius: 2px;}
.fly-badge-accept{height: 18px; line-height: 18px; padding: 0 5px !important; background-color: #5FB878; border-radius: 2px;}
/* 赞助商 */
.fly-zanzhu{display: block; position: relative; height: 60px; line-height: 60px; margin-top: 10px; padding: 0 20px; text-align: center; font-size: 16px; font-weight: 300; background-color: #009688; color: #fff;}
.fly-zanzhu:first-child{margin-top: 0;}
.fly-zanzhu:hover{opacity: 0.9; color: #fff;}
/* 图标 */
.icon-touxiang{display: inline-block; font-size: 34px;}
.icon-qq, .icon-weibo{font-size: 30px;}
.icon-renzheng{position: relative; color: #FFB800;}
.icon-kiss{font-size: 18px;}
.icon-pinglun1{position: relative; top: 2px;}
/* 头部 */
.fly-header{position: fixed; left: 0; top: 0; z-index: 10000; width: 100%; height: 60px; border-bottom: 1px solid #404553; border-right: 1px solid #404553; border-radius: 0;}
.fly-logo{position: absolute; left: 15px; top: 11px;}
.fly-nav{margin-left: 200px;}
.fly-nav a i{position: absolute; left: 25px; top: 0; padding-right: 10px; font-size: 26px;}
.fly-nav a .icon-shouye, .nav a .icon-shezhi{top: 2px;}
.fly-nav-user{position: absolute; top: 0; right: 0;}
.fly-nav-user .iconfont{position: relative;}
.fly-nav-avatar img{width: 36px; height: 36px; margin-left: 10px; border-radius: 100%;}
.fly-nav-avatar  .icon-renzheng{font-size: 16px; top: 1px;}
.fly-nav-avatar .fly-badge-vip{position: relative; margin-left: 10px;}
.fly-nav-user .layui-nav-child a i{position: relative; top: 2px; margin-right: 10px; font-size: 26px;}
.fly-nav-msg{position:absolute; top: 50%; left: -25px; height: 20px; line-height: 20px; margin-top: -10px; padding:0 6px; background-color: #FF7200; color: #fff; border-radius: 2px;}
.fly-nav-msg:hover{color:#fff;}
.fly-header .layui-nav{padding: 0; background: none;}
.fly-header .fly-nav a{padding: 0 25px 0 60px;}
.fly-header .fly-nav-user li a{padding: 0 10px;}
.fly-header .fly-nav-user li .fly-nav-avatar{padding-right: 0;}
.fly-header .fly-nav-user a.iconfont{color: #A9B7B7;}
.fly-header>.layui-nav-item a{color: rgba(255,255,255,0.5);}
.fly-header .layui-this a{color: #fff;}
.fly-header .layui-nav .layui-this:after,
.fly-header .layui-nav .layui-nav-bar,
.fly-header .fly-nav-user .layui-nav-more{display: none !important;}
.fly-header .fly-nav-user .layui-nav-child{left: auto; right: 0; width: 120px; min-width: 0;}
/* 底部 */
.fly-footer {margin: 50px 0 0; padding: 20px 0 30px; line-height: 30px; text-align: center; color: #737573; border-top: 1px solid #e2e2e2;}
.fly-footer a{padding:0 6px; font-weight: 300; color: #333;}
.fly-footer a:hover{color: #777;}
.fly-union{margin-top: 10px; color: #999;}
.fly-union>*{display: inline-block; vertical-align: middle;}
.fly-union a[upyun] img{width: 80px;}
.fly-union span{position: relative; top: 3px;}
.fly-union span a{padding: 0; display: inline; color: #999;}
.fly-union span a:hover{text-decoration: underline;}
/* 面板 */
.fly-panel{margin-bottom: 15px; border-radius: 2px; background-color: #fff; box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);}
.fly-panel[pad20]{padding: 20px;}
.fly-panel-title{position: relative; height: 50px; line-height: 50px; padding: 0 15px; border-bottom: 1px dotted #E9E9E9; color: #333; border-radius: 2px 2px 0 0; font-size: 14px;}
.fly-panel-main{padding: 10px 15px;}
/* 专栏 */
.fly-column{height: 50px; line-height: 50px;}
.fly-column ul li{position: relative; display: inline-block; height: 50px;}
.fly-column ul li a{padding: 0 20px;}
.fly-column ul li.layui-this:after{ position: absolute; bottom: 13px; left: 8px; z-index: 0; width: 50px; height: 22px; border: 1px solid #5FB878; border-radius: 2px;}
.fly-column ul li.layui-this a{color: #5FB878;}
.fly-column ul li .fly-mid{margin: 0 20px;}
.fly-column-right{position: absolute; right: 0; top: 0;}
.fly-column-right .layui-btn{vertical-align: initial;}
.fly-column .layui-badge-dot{position: absolute; top: 50%; left: 50%; margin: -4px 0 0 20px;}
/* 搜索 */
.fly-search{display: inline-block; vertical-align: top; width: 50px; height: 50px; margin-right: 10px; text-align: center; cursor: pointer; font-size: 20px;}
.fly-search .layui-icon{font-size: 20px;}
.fly-search:hover{color: #5FB878;}
.fly-layer-search input{height: 75px; line-height: 75px; width: 500px; padding: 0 15px; font-size: 20px; border: none 0; background: none;}
/* 筛选 */
.fly-filter a{padding: 0 8px; color: #666;}
.fly-filter a.layui-this{color: #5FB878;}
.fly-filter .fly-mid{margin: 0 8px;}
.fly-filter-right{position: absolute; right: 10px; top: 0;}
/* Tab */
.fly-tab{position: relative; padding-top: 3px;}
.fly-tab .layui-tab{margin: 0;}
.fly-tab .layui-tab-title{border-bottom: 1px dotted #e2e2e2;}
.fly-tab-border{position:relative; margin-bottom: 15px;}
.fly-tab-border span,
.fly-tab-border span a{display:inline-block; *display:inline; *zoom:1; vertical-align:top;}
.fly-tab-border span{border: 1px solid #ddd; border-right: none; font-size:0;}
.fly-tab-border span a{position: relative; height: 36px; line-height: 36px; padding: 0 20px; border-right: 1px solid #ddd; font-size: 14px; background-color: #fff;}
.fly-tab-border .tab-this{color: #000;}
.fly-tab-border .tab-this:after{content: ''; position: absolute; bottom: -1px; left: -1px; width: 100%; height: 1px; padding: 0 1px; background-color: #009688;}
/* 分页 */
.laypage-main,
.laypage-main *{display:inline-block; *display:inline; *zoom:1; vertical-align:top;}
.laypage-main{margin: 20px 0; border: 1px solid #009E94; border-right: none; border-bottom: none; font-size: 0;}
.laypage-main *{padding: 0 20px; line-height: 36px; border-right: 1px solid #009E94; border-bottom: 1px solid #009E94; font-size: 14px;}
.laypage-main .laypage-curr{background-color:#009E94; color:#fff;}
/* 简易编辑器 */
.fly-edit{position:relative; display: block; top: 1px; left:0; padding:0 10px; border: 1px solid #e6e6e6; border-radius: 2px 2px 0 0; background-color: #FBFBFB;}
.fly-edit span{cursor:pointer; padding:0 10px; line-height: 38px; color:#009E94;}
.fly-edit span i{position: relative; padding-right: 6px; font-size: 18px;}
.fly-edit span:hover{color: #5DB276;}
/* 列表 */
.fly-list li{position: relative; height: 45px; line-height: 22px; padding: 15px 15px 15px 75px; border-bottom: 1px dotted #e2e2e2;}
.fly-list li:last-child{border-bottom: none;}
.fly-list li h2,
.fly-list li h2 a,
.fly-list-info{white-space: nowrap; overflow: hidden; text-overflow: ellipsis;}
.fly-list li h2{height: 26px; font-size: 0;}
.fly-list li h2 a{display: inline-block; max-width: 80%; padding-right: 10px; font-size: 16px;}
.fly-list li h2 .layui-badge{top: -2px; height: 16px; line-height: 16px; padding: 0 5px; margin-right: 10px; font-size: 12px; border: 1px solid #5FB878; background: none; color: #5FB878;}
.fly-list-info{position: relative; font-size: 13px; color: #999;}
.fly-list-info>*{padding-right: 15px;}
.fly-list-info a[link]{color: #999;}
.fly-list-info a[link]:hover{color: #5FB878;}
.fly-list-info .icon-renzheng{position: relative; top: 1px; margin-right: 3px;}
.fly-list-info .fly-badge-vip{position: relative; margin-left: 2px;}
.fly-list-kiss{color: #FF5722;}
.fly-list-nums{position: absolute; right: 0; top: 0; padding-right: 0!important;}
.fly-list-nums i{position: relative; padding: 0 3px 0 15px;}
.fly-list-badge{position: absolute; right: 15px; top: 15px; font-size: 0;}
.fly-list-badge .layui-badge{margin-left: 5px; border-radius: 2px; font-size: 12px;}
/* 单行列表 */
.fly-list-one .fly-panel-title{margin-bottom: 5px;}
.fly-list-one dd{margin: 0 15px; line-height: 26px; white-space: nowrap; overflow: hidden; list-style: decimal-leading-zero inside; *list-style-type: decimal inside; color: #009E94;}
.fly-list-one dd a,
.fly-list-one dd span{display: inline-block; *display: inline; *zoom: 1; vertical-align: top; font-style: normal}
.fly-list-one dd a{max-width: 85%; margin-right: 5px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-size: 14px;}
.fly-list-one dd span{font-size: 12px; color: #ccc;}
.fly-list-one dd:last-child{padding-bottom: 5px;}
body .layui-edit-face{ border:none; background:none;}
body .layui-edit-face  .layui-layer-content{padding:0; background-color:#fff; color:#666; box-shadow:none}
.layui-edit-face .layui-layer-TipsG{display:none;}
.layui-edit-face ul{position:relative; width:372px; padding:10px; border:1px solid #D9D9D9; background-color:#fff; box-shadow: 0 0 20px rgba(0,0,0,.2);}
.layui-edit-face ul li{cursor: pointer; float: left; border: 1px solid #e8e8e8; height: 22px; width: 26px; overflow: hidden; margin: -1px 0 0 -1px; padding: 4px 2px; text-align: center;}
.layui-edit-face ul li:hover{position: relative; z-index: 2; border: 1px solid #eb7350; background: #fff9ec;}
/* 签到 */
.fly-signin cite{padding: 0 5px; color: #FF5722; font-style: normal;}
.fly-signin .layui-badge-dot{top: -7px; margin-left: 0px;}
.fly-signin-list{padding: 0; line-height: 30px;}
.fly-signin-list .layui-tab-item{padding: 10px; height: 320px; overflow-x: hidden; overflow-y: auto;}
.fly-signin-list li{margin-top: 5px; padding-bottom: 5px; border-bottom: 1px dotted #e2e2e2; white-space: nowrap;}
.fly-signin-list li:first-child{margin-top: 0;}
.fly-signin-list li:last-child{border: none 0;}
.fly-signin-list img{width: 30px; height: 30px; margin-right: 10px; border-radius: 2px;}
.fly-signin-list span{padding-left: 10px;}
.fly-signin-list span i{color: #FF5722;}
.fly-signin-list .fly-none{padding-top: 20px; min-height: 0;}
.fly-signin-days{position: absolute; right: 15px; padding-left: 10px; color: #999;}
.fly-signin-main{position: relative; height: 38px; padding: 24px 15px; text-align: center;}
.fly-signin-main span{padding-left: 10px;}
/* 榜单 */
.fly-rank{padding-bottom: 10px;}
.fly-rank dl{position: relative; overflow: hidden; margin-left: 20px; text-align: center; font-size: 0;}
.fly-rank dd{position: relative; width: 65px; height: 85px; margin: 10px 25px 5px 0; display:inline-block; *display:inline; *zoom:1; vertical-align:top; font-size:12px;}
.fly-rank dd a img{width: 65px; height: 65px; border-radius: 2px;}
.fly-rank dd a cite{ position:absolute; bottom: 20px; left: 0; width: 100%; height:20px; line-height:20px; text-align:center; background-color:rgba(0,0,0,.2); color:#fff; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.fly-rank dd a:hover cite{display: block;}
.fly-rank dd a i{position:absolute; bottom: 0; left: 0; width: 100%; text-align: center; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; font-style: normal;}
/* 静态列表 */
.fly-list-static li{line-height: 26px; list-style-position: inside; list-style-type: disc; white-space: nowrap; text-overflow: ellipsis; overflow: hidden;}
.fly-list-static li a{color: #01AAED;}
.fly-list-static li a:hover{opacity: 0.8;}
/* 单行列表 */
.jie-row li{position: relative; margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px dotted #E9E9E9; font-size: 0;}
.jie-row li *{position: relative; display:inline-block; *display:inline; *zoom:1; vertical-align: top; line-height: 20px; font-size:12px;}
.jie-row li span{padding: 0 6px; margin-right: 10px; background-color: #DADADA; color:#fff; font-size:12px;}
.jie-row li .fly-stick{background-color:#393D49;}
.jie-row li .fly-jing{background-color:#CC0000;}
.jie-row li .jie-status{margin:0 10px 0 0;}
.jie-row li .jie-status-ok{background-color:#8FCDA0;}
.jie-row li a{ padding-right:15px; font-size:14px;}
.jie-row li cite{padding-right:15px;}
.jie-row li i, .jie-row li em, .jie-row li cite{font-size:12px; color:#999; font-style: normal;}
.jie-row li .mine-edit{margin-left:15px; padding:0 6px; background-color: #8FCDA0; color:#fff; font-size:12px;}
.jie-row li em{position:absolute; right:0; top:0;}
.jie-row li .jie-user{}
.jie-row li .jie-title{max-width: 70%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.jie-row li .jie-user img{position:relative; top: 16px; width: 35px; height: 35px;}
/* Detail页 */
.detail-box{padding: 20px;}
.detail h1{font-size: 24px; line-height: 36px;}
.fly-detail-info{position: relative; margin: 10px 0 15px;}
.fly-detail-info .layui-btn{height: 20px; line-height: 20px; vertical-align: top; border-radius: 0;}
.fly-detail-info .layui-btn+.layui-btn{margin-left: 0;}
.fly-admin-box{position: relative; display: inline-block; vertical-align: top; margin-left: 20px;}
.fly-detail-info .fly-list-nums{top: -3px; font-size: 16px;}
.fly-detail-info .fly-list-nums i{padding: 0 3px 0 15px; font-size: 22px; color: #999;}
.detail-about{position: relative; line-height: 20px; padding: 15px 15px 15px 75px; font-size: 13px; background-color: #f8f8f8; color: #999;}
.detail-about .jie-status, .detail-about .jie-status-ok{color:#fff;}
.detail-about .fly-jing{padding:0 6px; background-color:#c00; color:#fff;}
.detail-about .detail-hits{position: relative; top: 5px; line-height: 20px;}
.fly-detail-user{white-space: nowrap; overflow: hidden;}
.fly-detail-user a{padding-right: 10px; font-size: 14px;}
.fly-detail-user .icon-renzheng{top: 1px;}
.detail-hits span{height: 20px; line-height: 20px;}
.detail-hits .layui-btn{border-radius: 0;}
.detail-hits .layui-btn+.layui-btn{margin-left: 5px;}
.detail-hits .jie-admin{margin-right: 1px;}
.detail-body{margin: 20px 0 0; min-height: 306px; line-height: 26px; font-size: 16px; color: #333; word-wrap: break-word;}
.detail-body p{margin-bottom:15px;}
.detail-body a{color:#4f99cf;}
.detail-body img{max-width: 100%; cursor: crosshair;}
.detail-body table{margin: 10px 0 15px;}
.detail-body table thead{background-color:#f2f2f2;}
.detail-body table th, 
.detail-body table td{padding: 10px 20px; line-height: 22px; border: 1px solid #DFDFDF; font-size: 14px; font-weight: 400;}
.detail .page-title{ border: none; background-color: #f2f2f2;}
/* 发帖 */
.layui-form-item.layui-col-space15{margin-bottom: 7.5px;}
/* 求解管理 */
.jie-admin{cursor: pointer;}
.detail-hits .jie-admin{color: #fff; padding: 0 10px; }
.detail-hits .jie-admin a{color: #fff;}
.jieda-admin{position:absolute; right: 0; top: 4px;}
/* 回答 */
.jieda{margin-bottom: 30px;}
.jieda li{position: relative; padding: 20px 0 10px; border-bottom: 1px dotted #DFDFDF;}
.jieda li:last-child{border-bottom: none;}
.jieda .fly-none{height: 50px; min-height: 0;}
.jieda .icon-caina{position:absolute; right:10px; top:15px; font-size:60px; color: #58A571;}
.detail-about-reply{padding: 0 0 0 55px; background: none;}
.detail-about-reply .detail-hits{left: 0; bottom: 0;}
.detail-about-reply .fly-avatar{left: 0; top: 0;}
.jieda-body{margin: 25px 0 20px; min-height: 0; line-height: 24px; font-size:14px;}
.jieda-body p{margin-bottom: 10px;}
.jieda-body a{color:#4f99cf}
.jieda-reply{position:relative;}
.jieda-reply span{padding-right:20px; color:#999; cursor:pointer;}
.jieda-reply span:hover{color:#666;}
.jieda-reply span i{margin-right:5px; font-size:16px;}
.jieda-reply span em{font-style: normal;}
.jieda-reply span .icon-zan{font-size: 22px;}
.jieda-reply .zanok,
.jieda-reply .jieda-zan:hover{color:#c00}
.jieda-reply span .icon-svgmoban53{position: relative; top: 1px;}
/* 用户中心 */
body .fly-user-main{position: relative; min-height: 600px;}
.fly-user-main>.layui-nav{position: absolute; left: 0; top: 0; z-index: 1000; height: 100%; padding: 10px 0;}
.fly-user-main>.layui-nav .layui-icon{position: relative; top: 2px; font-size: 20px; margin-right: 10px;}
.fly-user-main>.fly-panel{min-height: 575px; margin: 0 0 10px 215px;}
.fly-user-main .fly-none{min-height: 0;}
.fly-panel-user[pad20]{padding-top: 5px;}
.fly-form-app{margin-top:30px;}
.fly-form-app .iconfont{font-size:26px; padding: 0 5px;}
.fly-form-app .icon-qq{color:#7CA9C9}
.fly-form-app .icon-weibo{color:#E6162D}
.user-tab{margin:20px 0;}
.user-about{position:relative; padding:0 0 0px 20px; border-left:1px solid #DFDFDF; text-align:center;}
.user-about .user-avatar{width:100px; height:100px; border-radius:100%;}
.user-about p{line-height:30px;}
.user-about p span{padding:0 5px; color:#999;}
/* 个人主页 */
.fly-home{position: relative; padding: 30px 0 30px; text-align: center;}
.fly-home img{width:120px; height:120px; border-radius:100%;}
.fly-home h1{font-size:26px; line-height:30px; margin-top:10px;}
.fly-home h1 span{font-size:14px; color:#999;}
.fly-home h1 .icon-nan{color:#4EBBF9}
.fly-home h1 .icon-nv{color:#F581B1}
.fly-home-sign{padding: 0 10px; color: #999; margin-top: 10px;}
.fly-home .icon-renzheng{display: inline-block; width: 20px; height: 20px; line-height: 20px; top: 45px; left: -15px; background-color: #FFB800; color: #fff; border-radius: 50%; font-size: 12px;}
.fly-home-info i{padding-right: 5px; padding-left: 10px; color: #666;}
.fly-home-info span{color: #999;}
.fly-sns{margin-top: 10px;}
.fly-home-jie .jie-row,
.fly-home-da .home-jieda{min-height: 500px; padding: 5px 20px;}
/*.home-jieda li{margin-bottom:20px; padding-bottom:10px; line-height:24px; border-bottom: 1px dotted #DFDFDF;}*/
.home-jieda li{ margin-bottom:20px; line-height:24px;}
.home-dacontent{margin-top:10px; padding:10px 15px; background-color:#F2F2F5; border-radius:5px; word-wrap: break-word;;}
.home-dacontent pre{ background-color:#F2F2F5;}
.home-dacontent img{max-width:100%;}
.home-jieda li a{padding:0 5px; color:#4F99CF;}
.home-jieda li p{color:#999;}
.home-jieda li p span{padding-right:5px;}
/* 我的消息 */
#LAY-minemsg{min-height:420px;}
.mine-msg li{position:relative; margin-bottom: 15px; padding: 10px 0 5px; line-height:24px; border-bottom:1px dotted #E9E9E9}
.mine-msg li cite{padding: 0 5px; color: #4F99CF;}
.mine-msg li i{color:#4F99CF; padding-right:5px;}
.mine-msg li>p{position: relative; margin-top: 5px; line-height: 26px; text-align: right;}
.mine-msg li>p span{position: absolute; left: 0; top: 0; color:#999;}
.mine-msg li .fly-delete{position: relative; top: -3px;}
.mine-msg li .layui-elem-quote p[download]{padding: 10px 0 5px;}
/* 设置 */
.avatar-add{position:relative; width:373px; height:373px; background-color:#F2F2F5;}
.avatar-add .upload-img{position:absolute; left:50%; top:35px; margin:0 0 0 -56px;}
.avatar-add img{position:absolute; left:50%; top:50%; width:168px; height:168px; margin:-50px 0 0 -84px; border-radius:100%;}
.avatar-add .loading{display:none; position:absolute; width:100%; height:100%; left:0; top:0; padding: 0; background-color:#000; opacity:0.5; filter: Alpha(opacity=50);}
.avatar-add p{position:absolute; top:70px; width:100%; margin-top: 10px;; font-size:12px; text-align:center; color:#999;}
.app-bind li{margin-bottom:10px; line-height:30px; color:#999;}
.app-bind li .iconfont{position: relative; top: 3px; margin-right: 5px; font-size:28px; }
.app-bind .app-havebind{color:#333;}
.app-bind .app-havebind .icon-qq{color:#7CA9C9}
.app-bind .app-havebind .icon-weibo{color:#E6162D}
/* 案例 */
.fly-case-header{position: relative; height: 260px; text-align: center; background: #393D49;}
.fly-case-year{position: absolute; top: 30px; width: 100%; line-height: 50px; font-size: 50px; text-align: center; color: #fff; font-weight: 300;}
.fly-case-banner{position: absolute; left: 50%; top: 100px; width: 670px; margin-left: -335px;}
.fly-case-btn{position: absolute; bottom: 30px; left: 0; width: 100%; text-align: center;}
.fly-case-btn a{color: #fff;}
.fly-case-btn .layui-btn-primary{background: none; color: #fff;}
.fly-case-btn .layui-btn-primary:hover{border-color: #5FB878;}
.fly-case-tab{margin-top: 20px; text-align: center;}
.fly-case-tab span,
.fly-case-tab span a{border-color: #009688;}
.fly-case-tab .tab-this{background-color: #009688; color: #fff;}
.fly-case-list{margin-top: 15px; font-size: 0;}
.fly-case-list li, 
.layer-ext-ul li{display: inline-block; vertical-align: middle; *display: inline; *zoom:1; font-size: 14px; background-color: #fff;}
.fly-case-list{width: 110%;}
.fly-case-list li{width: 239px; margin: 0 15px 15px 0; padding: 10px;}
.fly-case-list li:hover{box-shadow: 1px 1px 5px rgba(0,0,0,.1);}
.fly-case-img{position: relative; display: block;}
.fly-case-img img{width: 239px; height: 150px;}
.fly-case-img .layui-btn{display: none; position: absolute; bottom: 20px; left: 50%; margin-left: -29px;}
.fly-case-img:hover .layui-btn{display: inline-block;}
.fly-case-list li h2{padding: 10px 0 5px; line-height: 22px; font-size: 18px; white-space: nowrap; overflow: hidden; text-align: center;}
.fly-case-desc{height: 60px; line-height: 20px; font-size: 12px; color: #666; overflow: hidden;}
.fly-case-info{position: relative; margin: 10px 0 0; padding: 10px 65px 0 45px; border-top: 1px dotted #eee;}
.fly-case-info p{height:24px; line-height: 24px;}
.fly-case-user{position: absolute; left: 0; top: 15px; width: 35px; height: 35px;}
.fly-case-user img{width: 35px; height: 35px; border-radius: 100%;}
.fly-case-info .layui-btn{position: absolute; right: 0; top: 15px;  padding: 0 15px;}
.layer-ext-ul{margin: 10px; max-height: 500px;}
.layer-ext-ul img{width: 50px; height: 50px; border-radius: 100%;}
.layer-ext-ul li{margin: 8px;}
.layer-ext-case .layui-layer-title{border: none; background-color: #009688; color: #fff;}
/* 广告 */
.fly-ad{position: relative; background-color: #f2f2f2; overflow:hidden;}
.fly-ad:before{content: '广告位'; position: absolute; z-index: 0; top: 50%; left: 50%; left: 50%; margin: -10px 0 0 -25px; color: #aaa; font-size: 18px; font-weight: 300;}
.fly-ad div{position: relative; z-index: 1;}
/* 友链 */
.fly-link dd{display: inline-block; vertical-align: top;}
.fly-link a{line-height: 24px; padding-right: 15px;}
/* 404或提示 */
.fly-none{min-height: 600px; text-align: center; padding-top:50px; color: #999;}
.fly-none .iconfont{line-height: 300px; font-size: 300px; color: #393D49;}
.fly-none .icon-tishilian{display: inline-block; margin: 30px 0 20px;}
.fly-none p{margin-top: 50px; padding: 0 15px; font-size: 20px; color: #999; font-weight: 300;}
.fly-list-one .fly-none{min-height: 70px;}
@media screen and (max-width: 768px) {
	.fly-main{width: 100%;}
  /* 顶边距 */
  .fly-marginTop{margin-top: 0;}
  /* 头部 */
  .fly-header .fly-nav-user li .fly-nav-avatar{padding-right: 15px;}
  .fly-header .fly-nav-user{margin-right: 5px;}
  /* 专栏 */
  .fly-column{height: auto;}
  .fly-column ul{padding: 10px; font-size: 0;}
  .fly-column ul li{float: left; width: 33.33%; height: 36px; line-height: 36px; font-size: 14px; vertical-align: middle; text-align: center; box-sizing: border-box;}
  .fly-column-right{right: 10px;}
  .fly-column ul li.layui-this:after{display: none;}
  /* 页脚 */
  .fly-footer{margin-top: 0; border-top: none;}
  /* 分页 */
  .laypage-main a, .laypage-main span{display: none;}
  .laypage-main .laypage-prev,
  .laypage-main .laypage-curr,
  .laypage-main .laypage-next{display: inline-block;}
  /* 列表 */
  .fly-list li h2 a{max-width: 72%;}
  /* Detail 页 */
	.fly-admin-box{display: block; margin: 0; margin-top: 10px;}
	.fly-detail-info .fly-list-nums{top: -2px;}
	.fly-edit span{padding: 0 6px;}
  /* 案例 */
  .fly-case-list,
  .fly-case-list li{width: 100%;  -webkit-box-sizing: border-box !important; -moz-box-sizing: border-box !important; box-sizing: border-box !important;}
  .fly-case-img{text-align: center;}
  .fly-case-img img{max-width: 100%;}
  .fly-case-banner{width: 300px; margin-left: -150px;}
  body .fly-user-main{width: auto;}
  .fly-user-main>.layui-nav{left: -300px; transition: all .3s; -webkit-transition: all .3s;}
  .fly-user-main>.fly-panel-user{width: auto; margin-left: 0; transition: all .3s; -webkit-transition: all .3s;}
  .site-tree-mobile{display: block!important; position: fixed; z-index: 100000; bottom: 20px; left: 10px; width: 50px; height: 50px; line-height: 50px; border-radius: 2px; text-align: center; background-color: rgba(0,0,0,.7); color: #fff;}
  .site-mobile .site-tree-mobile{display: none !important;}
  .site-mobile .fly-user-main>.layui-nav{left: 0;}
  .site-mobile .site-mobile-shade{content: ''; position: fixed; top: 0; bottom: 0; left: 0; right: 0; background-color: rgba(0,0,0,.9); z-index: 999;}
}
