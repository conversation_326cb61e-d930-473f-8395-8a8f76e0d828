<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>客户管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="CustContactsMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>联系人管理</h5>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">姓名</span>
						 	<input class="form-control input-sm" name="name">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">手机号</span>
						 	<input class="form-control input-sm" name="mobile">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="CustContactsMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="CustContactsMgr.add()">+ 新建联系人</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="CustContactsMgrTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var CustContactsMgr={
			init:function(){
				$("#CustContactsMgrForm").initTable({
					mars:'ContactsDao.page',
					id:'CustContactsMgrTable',
					autoSort:false,
					height:'full-120',
					limit:15,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left',
						fixed:'left'
					 },{
					    field: 'NAME',
						title: '联系人',
						align:'left',
						width:80,
						event:'CustContactsMgr.edit',
						style:'color:#1E9FFF;cursor:pointer',	
						fixed:'left'
					},{
						field:'CUST_NAME',
						title:'所属客户',
						templet:'<div><a href="javascript:;" onclick="CustContactsMgr.custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
					},{
					    field: 'MOBILE',
						title: '手机',//https://h5.bqu.cn/tel_qrcode/?tel=15821877034
						align:'center',
						width:110
					},{
						field:'SEX',
						title:'性别',
						align:'center',
						width:70
					},{
						field:'ROLE',
						title:'角色',
						align:'left',
						width:100
					},{
					    field: 'EMAIL',
						title: '邮箱',
						align:'left',
						width:140
					},{
					    field: 'POST',
						title: '职务',
						align:'left',
						width:100
					},{
					    field: 'LAST_FOLLOW_TIME',
						title: '最后跟进时间',
						align:'center',
						width:110
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						width:90,
						hide:true,
						align:'center',
						templet:function(row){
							var time= row['UPDATE_TIME'];
							return cutText(time,12,'');
						}
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'CREATE_USER_ID',
						title: '创建人',
						align:'center',
						width:70,
						templet:function(row){
							return getUserName(row.CREATE_USER_ID);
						}
					},{
						field:'',
						title:'操作',
						width:100,
						templet:'<div><a href="javasript:;" onclick="contactsQuery(\'{{d.CONTACTS_ID}}\')">联系记录</a></div>'
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#CustContactsMgrForm").queryData({id:'CustContactsMgrTable',jumpOne:true});
			},
			edit:function(data){
				var  contactsId = data['CONTACTS_ID'];
				var custName = data['CUST_NAME'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/crm/contacts/contacts-edit.jsp',title:'编辑联系人',data:{isEdit:1,contactsId:contactsId,custName:custName}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/crm/contacts/contacts-edit.jsp',title:'新增联系人',closeBtn:0,data:{isEdit:1}});
			},
			custDetail:function(custId){
				var width = $(window).width();
				var w = '80%';
				if(width>1500){
					w = '800px';
				}else if(width<1000){
					w = '100%';
				}else{
					
				}
				popup.openTab({id:'custDetail',title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:[w,'100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId}});
			}
		}
		function reloadContactsList(){
			CustContactsMgr.query();
		}
		
		function contactsQuery(id){
			popup.openTab({id:'custContactQuery',title:'联系记录',url:'${ctxPath}/pages/crm/contacts/contact-query.jsp',data:{contactsId:id}});	
		}
		
		$(function(){
			$("#CustContactsMgrForm").render({success:function(){
				CustContactsMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>