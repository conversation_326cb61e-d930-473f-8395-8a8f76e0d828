layui.use(["element", "carousel"], function () {
   var e = layui.$, t = (layui.admin, layui.carousel), a = layui.element, i = layui.device();
   e(".layadmin-carousel").each(function () {
       var a = e(this);
       t.render({
           elem: this,
           width: "100%",
           arrow: "none",
           interval: a.data("interval")||5000,
           autoplay: a.data("autoplay") === !0,
           trigger: i.ios || i.android ? "click" : "hover",
           anim: a.data("anim")
       })
   });
   a.render("progress");
});
$(function(){
	localStorage.setItem("devLead",devLead);
	localStorage.setItem("deptId",deptId);
	localStorage.setItem("yq_userId",userId);
	localStorage.setItem("roles",roles);
	pwd=pwd.toUpperCase();
	if('E10ADC3949BA59ABBE56E057F20F883E'==pwd||'E91AD915BA65B8DE9FD53CAFEB9FDD95'==pwd){
		layer.alert('密码较弱,请立即修改密码!',{icon:7,time:30000,offset:'auto',shade:0,shadeClose:true},function(index){
			layer.close(index);
			popup.openTab({url:'/yq-admin/servlet/user?action=userModPwd',title:'修改密码',id:'pwd'});
		});
	}
    $(".render-1").render();
    $(".render-2").render();
    $(".render-3").render();
	if(locationCompany()){
		 initBookFoodData();
	}else{
		 $(".dc").remove();
	}
	openWx();
	
	var pushMsgFlag=sessionStorage.getItem("pushMsgFlag");
	if(pushMsgFlag!='ok'){
		pushMsg();
	}
	
});

function pushMsg(){
	ajax.remoteCall(ctxPath+"/servlet/workbench?action=pushMsg",{},function(result) { 
		sessionStorage.setItem("pushMsgFlag",'ok');
	},{loading:false,error:function(e){
		
	}});
}

function openWx(){
	if(''==openId){
		bindWx();
	}
}
function bindWx(){
	$('#codeContainer').html("");
	$('#codeContainer').qrcode({width:180,height:180,text:"http://work.yunqu-info.cn/yq-work/api/getAuthorize?userId="+userId});
	layer.open({type:1,title:'绑定微信服务号',offset:'30px',area:['300px','300px'],shadeClose:false,closeBtn:0,btn:['我已扫码','稍后再试'],content:$("#qcode")});
}
function bindOA(type){
	popup.layerShow({type:1,anim:0,scrollbar:false,offset:'30px',area:['300px','250px'],url:ctxPath+'/pages/oa/oa-acount.jsp',data:{type:type},title:'OA账号绑定'});
}
function openMyAllWf(){
	popup.openTab({url:ctxPath+'/pages/oa/my-all-wf.jsp',title:'我发起的流程'});
}
function openMydoWf(){
	popup.openTab({url:ctxPath+'/pages/oa/my-do-wf.jsp',title:'待我审批的流程'});
}
function fullShow(){
	var windowWidth = $(window).width();
    if(windowWidth < 640){
       return true;
    }else{
    	return false;
    }
}
function zendao(){
	if(locationCompany()){
		window.open(ctxPath+'/sso.jsp');
	}else{
		window.open('http://work.yunqu-info.cn/yq-work/sso.jsp');
	}
}
function zendaoNum(data){
	var num=data.length;
	if(num > 0){
		$("#zendaoNum").append('<span class="layui-badge-dot layui-bg-blue"></span>');
	}
	return '';
}
function addressList(){
	popup.openTab({id:'addressList',type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:ctxPath+'/pages/ehr/address-list.jsp',title:'通讯录',data:{}});
}
function kanban(){
	popup.openTab({id:'kanban',url:ctxPath+'/pages/kanban/kanban-index.jsp',title:'我的看板'});
}
function moreNote(){
	popup.openTab({id:'note',url:ctxPath+'/pages/note/note-list.jsp',title:'我的笔记'});
}
function moreDoc(){
	popup.openTab({url:ctxPath+'/pages/doc/doc-list.jsp',title:'文档'});
}
function moreNav(){
	popup.openTab({url:ctxPath+'/pages/nav/user-nav-mgr.jsp',title:'我的网址'});
}
function moreTw(){
	popup.openTab({url:ctxPath+'/pages/remind/remind-mgr.jsp',data:{type:0},title:'我的动弹'});
}
function moreTodo(){
	popup.openTab({type:1,full:fullShow(),maxmin:true,area:['900px','650px'],url:ctxPath+'/pages/todo/my-todo-list.jsp',title:'我的日程'});
}
function moreTask(){
	popup.openTab({id:'task',url:ctxPath+'/pages/task/task-index.jsp?status=1',title:'我负责的任务'});
}
function reloadNav(){
	$(".nav-card").render();;
}
function reloadTaskList(){
	$(".taskList").render();;
}
function weekly(){
	popup.openTab({title:'我的周报',url:ctxPath+'/pages/weekly/weekly-index.jsp'})
}
function remindDetail(remindId,remindType){
	popup.openTab({id:"remindDetail",url:ctxPath+'/pages/remind/remind-detail.jsp',title:'详情',data:{remindId:remindId,remindType:remindType}});
}
function docDetail(docId,folderId){
	popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:ctxPath+'/pages/doc/doc-detail.jsp',title:'详情',data:{op:'detail',docId:docId,folderId:folderId}});
}
function openNotices(){
	popup.openTab({id:'reminds',url:ctxPath+'/pages/remind/remind-list.jsp?type=1',title:'通知公告'});
}
function taskDetail(taskId,taskState){
	popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:ctxPath+'/pages/task/task-detail.jsp',title:'处理',data:{taskId:taskId,taskState:taskState,status:1}});
}
function zentao(id){
	window.open('http://172.16.71.247/zentao/bug-view-'+id+'.html');
}
function addTwitter(){
	var width=$(window).width();
	var fullFlag=false;
	if(width<700){
		fullFlag=true;
	}
	popup.layerShow({type:1,full:fullFlag,closeBtn:0,anim:0,scrollbar:false,offset:'20px',area:['450px','440px'],url:ctxPath+'/pages/remind/twitter.jsp',title:false});
}
function reloadTwitter(){
	$(".tw-card").render();
}
function addUserNav(){
	popup.layerShow({type:1,anim:0,scrollbar:false,offset:'20px',area:['450px','400px'],url:ctxPath+'/pages/nav/user-nav-edit.jsp',title:'网址导航'});
}
function userNavDetail(id){
	popup.layerShow({type:1,anim:0,scrollbar:false,offset:'20px',area:['400px','350px'],url:ctxPath+'/pages/nav/user-nav-detail.jsp',data:{navId:id},title:'访问链接'});
}
function projectDetail(title,id){
	title = cutText(title,10,'');
	popup.openTab({url:ctxPath+'/pages/project/project-detail.jsp',title:title,data:{projectId:id}});
}
function myProject(){
	popup.openTab({id:'myProject',url:ctxPath+'/pages/project/my-project-list.jsp?status=2',title:'我参与的项目',data:{}});
}
function getTwContent(val){
	//val = val.replaceAll("img src","img data-src")
	return val;
}

function getDateDes(dateStr){
	return getDateDiff(getDateTimeStamp(dateStr));
}
function getDateTimeStamp(dateStr){
	 return Date.parse(dateStr.replace(/-/gi,"/"));
}
function getDateDiff(dateTimeStamp){
	var minute = 1000 * 60;
	var hour = minute * 60;
	var day = hour * 24;
	var halfamonth = day * 15;
	var month = day * 30;
	var now = new Date().getTime();
	var diffValue = now - dateTimeStamp;
	if(diffValue < 0){return;}
	var monthC =diffValue/month;
	var weekC =diffValue/(7*day);
	var dayC =diffValue/day;
	var hourC =diffValue/hour;
	var minC =diffValue/minute;
	if(monthC>=1){
		result="" + parseInt(monthC) + "月前";
	}
	else if(weekC>=1){
		result="" + parseInt(weekC) + "周前";
	}
	else if(dayC>=1){
		result=""+ parseInt(dayC) +"天前";
	}
	else if(hourC>=1){
		result=""+ parseInt(hourC) +"小时前";
	}
	else if(minC>=1){
		result=""+ parseInt(minC) +"分钟前";
	}else
	result="刚刚";
	return result;
}