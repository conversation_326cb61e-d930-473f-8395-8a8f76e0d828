<%@ page language="java" contentType="text/html;charset=UTF-8"%>
 <table class="table table-edit table-vzebra">
  		<tr>
  			<td style="width: 10%;">评审编号</td>
  			<td style="width: 25%;"><input type="text" data-rules="required" class="form-control" name="review.REVIEW_NO" data-mars="ReviewDao.getReviewNo"/></td>
  			<td style="width: 10%;">经办人</td>
  			<td style="width: 23%;"><input type="text" readonly="readonly" class="form-control" data-mars="CommonDao.userName"/></td>
  			<td style="width: 10%;">提交时间</td>
  			<td style="width: 22%;"><input type="text" readonly="readonly" class="form-control" data-mars="CommonDao.date02"/></td>
  		</tr>
  		<tr>
  			<td class="required">签约单位</td>
  			<td>
  				<select data-rules="required" class="form-control text-val" name="review.SIGN_ENT">
  					<option value="yunqu">云趣</option>
  					<option value="zhongrong">中融</option>
  					<option value="pci">佳都</option>
  				</select>
  			</td>
  			<td class="required">合同名称</td>
  			<td>
  				<input type="text" data-rules="required" class="form-control" name="review.CONTRACT_NAME"/>
  			</td>
  			<td class="required">产品线</td>
  			<td>
  				<select class="form-control text-val" data-rules="required" name="review.PROD_LINE" data-mars="YqAdmin.DictDao.select('Y001')">
  					<option value="">请选择</option>
  				</select>
  			</td>
  		</tr>
  		<tr>
  			<td class="required">售前经理</td>
  			<td>
  				<select data-rules="required" data-mars="CommonDao.userDict" name="review.PRE_SALES_BY"  class="form-control text-val">
                	 	<option value="">请选择</option>
                </select>
  			</td>
  			<td class="required">销售经理</td>
  			<td>
  				<select data-rules="required" data-mars="CommonDao.userDict" onchange="getPreMgr(this)" name="review.SALES_BY"  class="form-control text-val">
                	 <option value="">请选择</option>
                </select>
  			</td>
  			<td class="required">上级主管</td>
  			<td>
  				<input type="hidden" class="form-control" name="review.UP_SALES_NAME"/>
  				<select data-rules="required" data-mars="CommonDao.userDict" name="review.UP_SALES_BY" onchange="getPreMgrName(this)" class="form-control text-val">
                	 <option value="">请选择</option>
                </select>
  			</td>
  		</tr>
  		<tr>
  			<td>合同金额</td>
  			<td><input type="text" class="form-control" name="review.AMOUNT"/></td>
  			<td>付款方式</td>
  			<td><input type="text" class="form-control" name="review.PAY_TYPE"/></td>
  			<td>维保时间</td>
  			<td><input type="text" placeholder="维保合同必填" class="form-control" name="review.WB_TIME"/></td>
  		</tr>
  		<tr>
  			<td class="required">签约部门</td>
  			<td>
  				<select name="review.SIGN_DEPT_ID" data-rules="required" data-mars="CommonDao.deptDict" class="form-control text-val">
					<option value=""></option>						    					
  				</select>
  			</td>
  			<td class="required">所属客户</td>
  			<td>
  				<input type="hidden" class="form-control" name="review.CUST_ID"/>
  				<input type="text" data-rules="required" class="form-control" name="review.CUST_NAME" id="custId" readonly="readonly"/>
  			</td>
  			<td>所属商机</td>
  			<td>
  				<input type="hidden" class="form-control" name="review.BUSINESS_NAME"/>
  				<select data-mars="CustDao.custBusniessDict" name="review.BUSINESS_ID" class="form-control text-val">
               		<option value="">请选择-</option>
                </select>
  			</td>
  		</tr>
  		<tr>
  			<td>可阅用户</td>
  			<td colspan="5">
  				<select multiple="multiple" data-mars="CommonDao.userDict" name="readUserIds"  class="form-control text-val">
                	 <option value="">请选择</option>
                </select>
  			</td>
  		</tr>
  		<tr>
  			<td>备注</td>
  			<td colspan="5">
  				<textarea class="form-control" style="height: 100px;" name="review.REMARK"></textarea>
  			</td>
  		</tr>
  		<tr>
  			<td style="vertical-align: top;">合同方案附件</td>
  			<td colspan="5">
				<a onclick="$('#localfile').click();" href="javascript:;">+上传</a><br>
            	<div id="fileList" style="display: inline-block;" data-template="template-files" data-mars="FileDao.fileList"></div>
  			</td>
  		</tr>
  	</table>
  	
  	
<script id="template-files" type="text/x-jsrender">
	{{if data.length==0}}

	{{/if}}
	{{for data}}
		{{:#index+1}}.{{:CREATE_NAME}}于{{:CREATE_TIME}}上传<a style="color:#20a0ff;" href="/yq-work/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank">{{:FILE_NAME}}</a>
		<br>
	{{/for}}
</script>
<script>

	var uploadFile = function(){
		var randomId = $("#randomId").val();
		var fkId = Review.reviewId||randomId;
		easyUploadFile({callback:'callback',fkId:fkId,source:'contract',formId:'fileForm',fileId:'localfile'});
	}

	var callback = function(data){
		var randomId = $("#randomId").val();
		var fkId = Review.reviewId||randomId;
		$("#fileList").render({data:{fkId:fkId}});
	}
	
	function getPreMgr(el){
		var id  = el.value;
		ajax.daoCall({loading:false,controls:['OrgDao.getUpUser'],params:{userId:id}},function(rs){
			var result = rs['OrgDao.getUpUser'].data;
			$("[name='review.UP_SALES_BY']").val(result['USER_ID']).trigger("change");
			$("[name='review.UP_SALES_NAME']").val(result['USERNAME']);
		});
	}
	
	function getPreMgrName(el){
		var name = $(el).find("option:selected").text()
		$("[name='review.UP_SALES_NAME']").val(name);
	}
	
</script>


