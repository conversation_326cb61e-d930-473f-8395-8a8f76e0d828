package com.yunqu.work.service;

import java.sql.SQLException;

import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.LookLogModel;

public class LookLogService extends AppBaseService{
	private static class Holder{
		private static LookLogService service=new LookLogService();
	}
	public static LookLogService getService(){
		return Holder.service;
	}
	
	public void addLog(String lookBy,String fkId){
		addLog(lookBy,fkId,null,null,null);
	}
	
	public void addLog(String lookBy,String fkId,String type){
		addLog(lookBy,fkId,type,null,null);
	}
	public void addLog(String lookBy,String fkId,String type,String ip){
		addLog(lookBy,fkId,type,ip,null);
	}
	
	public void addLog(String lookBy,String fkId,String type,String ip,String url){
		LookLogModel model=new LookLogModel();
		model.setFkId(fkId);
		model.setLookBy(lookBy);
		model.setPrimaryValues(RandomKit.uuid());
		model.set("look_time", EasyDate.getCurrentDateString());
		if(type!=null) {
			model.set("type", type);
		}
		if(ip!=null) {
			model.set("ip", ip);
		}
		if(url!=null) {
			model.set("url", url);
		}
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
	}

}
