package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;

public class NextCloudService extends AppBaseService{

	private static class Holder{
		private static NextCloudService service=new NextCloudService();
	}
	public static NextCloudService getService(){
		return Holder.service;
	}
	
	public void run() {
		syn251GroupData();
		syn251UserData();
		
		
		syn252GroupData();
		syn252UserData();
	}
	public void syn251GroupData() {
		try {
			EasyQuery query251 = EasyQuery.getQuery("nextcloud-251");
			List<JSONObject>  groupList  = query251.queryForList("select * from oc_groups",new Object[] {},new JSONMapperImpl());
		    String[] groupNames = new String[groupList.size()];
		    int i = 0;
			for(JSONObject object:groupList) {
				groupNames[i] = object.getString("GID");
				i++;
		    }

			EasySQL sql = new EasySQL("select * from easi_dept where length(DEPT_CODE) = 6");
			sql.appendIn(groupNames,"and dept_name not");
		    List<JSONObject>  deptList  = this.getMainQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
		    if(deptList!=null&&deptList.size()>0) {
		    	for(JSONObject object : deptList) {
		    		EasyRecord record = new EasyRecord("oc_groups","gid");
		    		record.set("gid",object.getString("DEPT_NAME"));
		    		record.set("displayname",object.getString("DEPT_NAME"));
		    		query251.save(record);
		    	}
		    }
			
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	public void syn251UserData() {
		try {
			EasyQuery query251 = EasyQuery.getQuery("nextcloud-251");
			List<JSONObject>  userList  = query251.queryForList("select * from oc_users",new Object[] {},new JSONMapperImpl());
			String[] userAccts = new String[userList.size()];
			int i = 0;
			for(JSONObject object:userList) {
				userAccts[i] = object.getString("UID");
				i++;
			}
			
			EasySQL sql = new EasySQL("select t2.staff_no,t1.USERNAME,t1.EMAIL from easi_user t1,yq_staff_info t2 where t1.USER_ID = t2.staff_user_id and t1.STATE = 0");
			sql.appendIn(userAccts,"and t2.staff_no not");
			
			List<JSONObject>  staffList  = this.getMainQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(staffList!=null&&staffList.size()>0) {
				for(JSONObject object : staffList) {
					EasyRecord record = new EasyRecord("oc_users","uid");
					record.set("uid",object.getString("STAFF_NO"));
					record.set("uid_lower",object.getString("STAFF_NO"));
					record.set("password","3|$argon2id$v=19$m=65536,t=4,p=1$cVlZNzhtdHAzL2RicldPeQ$985t4o5+Rq3UkhWUXGhGW7n+H2F4FdaDEiZCALtN74w");
					record.set("displayname",object.getString("USERNAME"));
					query251.save(record);
				}
			}
			
			query251.executeUpdate("delete from oc_group_user where uid <> ? ","admin");
			EasySQL sql2 = new EasySQL();
			sql2.append("select t1.email,t1.depts,t2.staff_no from easi_user t1,yq_staff_info t2 where t1.state = 0 and t1.user_id = t2.staff_user_id");
			List<JSONObject>  userDepts  = this.getMainQuery().queryForList(sql2.getSQL(),sql2.getParams(),new JSONMapperImpl());
			for(JSONObject object : userDepts) {
				EasyRecord record = new EasyRecord("oc_group_user","gid");
				record.set("gid",object.getString("DEPTS"));
				record.set("uid",object.getString("STAFF_NO"));
				query251.save(record);
				
				EasyRecord record2 = new EasyRecord("oc_preferences","userid","configkey");
				record2.set("userid",object.getString("STAFF_NO"));
				record2.set("appid","settings");
				record2.set("configkey","email");
				record2.set("configvalue",object.getString("EMAIL"));
				boolean bl = query251.update(record2);
				if(!bl) {
					query251.save(record2);
				}
				
			}
			
			EasySQL sql3 = new EasySQL();
			sql3.append("select  t1.email,t1.depts,t2.staff_no from easi_user t1,yq_staff_info t2 where t1.state <> 0 and t1.user_id = t2.staff_user_id");
			List<JSONObject>  disabedUser  = this.getMainQuery().queryForList(sql3.getSQL(),sql3.getParams(),new JSONMapperImpl());
			for(JSONObject object : disabedUser) {
				EasyRecord record = new EasyRecord("oc_preferences","userid","appid","configkey");
				record.set("userid",object.getString("STAFF_NO"));
				record.set("appid","core");
				record.set("configkey","enabled");
				record.set("configvalue","false");
				boolean bl = query251.update(record);
				if(!bl) {
					query251.save(record);
				}
			}
			
			
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	public void syn252UserData() {
		try {
			EasyQuery query252 = EasyQuery.getQuery("nextcloud-252");
			List<JSONObject>  userList  = query252.queryForList("select * from oc_users",new Object[] {},new JSONMapperImpl());
			String[] userAccts = new String[userList.size()];
			int i = 0;
			for(JSONObject object:userList) {
				userAccts[i] = object.getString("UID");
				i++;
			}
			
			EasySQL sql = new EasySQL("select t2.staff_no,t1.USERNAME,t1.EMAIL,t1.depts from easi_user t1,yq_staff_info t2 where t1.USER_ID = t2.staff_user_id and t1.STATE = 0");
			sql.appendIn(userAccts,"and t2.staff_no not");
			
			List<JSONObject>  staffList  = this.getMainQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(staffList!=null&&staffList.size()>0) {
				for(JSONObject object : staffList) {
					EasyRecord record = new EasyRecord("oc_users","uid");
					record.set("uid",object.getString("STAFF_NO"));
					record.set("uid_lower",object.getString("STAFF_NO"));
					record.set("password","3|$argon2id$v=19$m=65536,t=4,p=1$cVlZNzhtdHAzL2RicldPeQ$985t4o5+Rq3UkhWUXGhGW7n+H2F4FdaDEiZCALtN74w");
					record.set("displayname",object.getString("USERNAME"));
					query252.save(record);
				}
			}
			
			query252.executeUpdate("delete from oc_group_user where uid <> ? ","admin");
			EasySQL sql2 = new EasySQL();
			sql2.append("select  t1.email,t1.depts,t2.staff_no from easi_user t1,yq_staff_info t2 where t1.state = 0 and t1.user_id = t2.staff_user_id");
			List<JSONObject>  userDepts  = this.getMainQuery().queryForList(sql2.getSQL(),sql2.getParams(),new JSONMapperImpl());
			for(JSONObject object : userDepts) {
				EasyRecord record = new EasyRecord("oc_group_user","gid");
				record.set("gid",object.getString("DEPTS"));
				record.set("uid",object.getString("STAFF_NO"));
				query252.save(record);
				
				EasyRecord record2 = new EasyRecord("oc_preferences","userid","configkey");
				record2.set("userid",object.getString("STAFF_NO"));
				record2.set("appid","settings");
				record2.set("configkey","email");
				record2.set("configvalue",object.getString("EMAIL"));
				boolean bl = query252.update(record2);
				if(!bl) {
					query252.save(record2);
				}
			}
			
			EasySQL sql3 = new EasySQL();
			sql3.append("select  t1.email,t1.depts,t2.staff_no from easi_user t1,yq_staff_info t2 where t1.state <> 0 and t1.user_id = t2.staff_user_id");
			List<JSONObject>  disabedUser  = this.getMainQuery().queryForList(sql3.getSQL(),sql3.getParams(),new JSONMapperImpl());
			for(JSONObject object : disabedUser) {
				EasyRecord record = new EasyRecord("oc_preferences","userid","appid","configkey");
				record.set("userid",object.getString("STAFF_NO"));
				record.set("appid","core");
				record.set("configkey","enabled");
				record.set("configvalue","false");
				boolean bl = query252.update(record);
				if(!bl) {
					query252.save(record);
				}
			}
			
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	
	public void syn252GroupData() {
		try {
			EasyQuery query252 = EasyQuery.getQuery("nextcloud-252");
			
			List<JSONObject>  groupList  = query252.queryForList("select * from oc_groups",new Object[] {},new JSONMapperImpl());
			String[] groupNames = new String[groupList.size()];
			int i = 0;
			for(JSONObject object:groupList) {
				groupNames[i] = object.getString("GID");
				i++;
			}
			
			EasySQL sql = new EasySQL("select * from easi_dept where length(DEPT_CODE) = 6");
			sql.appendIn(groupNames,"and dept_name not");
			List<JSONObject>  deptList  = this.getMainQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(deptList!=null&&deptList.size()>0) {
				for(JSONObject object : deptList) {
					EasyRecord record = new EasyRecord("oc_groups","gid");
					record.set("gid",object.getString("DEPT_NAME"));
					record.set("displayname",object.getString("DEPT_NAME"));
					query252.save(record);
				}
			}
			
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	
	
}
