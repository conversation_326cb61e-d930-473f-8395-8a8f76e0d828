<!-- Generated by SpecStory -->

# 防止actionForWxDk并发请求问题 (2025-04-03 10:33:33)

_**User**_

怎么防止actionForWxDk存在一个用户同时请求打卡并发问题

---

_**Assistant**_

```thinking
让我思考一下如何解决actionForWxDk的并发问题...

首先需要理解当前的问题:
1. actionForWxDk是处理微信打卡的方法,一个用户可能会在短时间内多次请求打卡
2. 目前的代码没有任何并发控制,可能导致:
   - 数据库记录重复插入
   - 打卡时间记录混乱
   - 消息重复发送
   - 数据不一致

解决思路:
1. 需要对同一个用户的打卡请求进行同步控制
2. 可以使用synchronized或分布式锁来实现
3. 由于是web应用,分布式锁更合适,可以防止多节点部署时的并发问题
4. 锁的粒度应该是用户级别,不同用户之间不需要互斥
5. 锁的超时时间不能太长,避免死锁

具体实现方案:
1. 使用Redis分布式锁
2. key使用 "kq_lock_{userId}" 格式
3. 设置合理的锁超时时间,比如5秒
4. 在方法开始获取锁,结束释放锁
5. 使用try-finally确保锁一定会释放

让我看看项目中是否有Redis相关的依赖和工具类...

我注意到这是一个JFinal项目,可以使用JFinal的Cache插件实现分布式锁。

让我编写具体的代码修改方案...
```

我建议对`actionForWxDk`方法进行如下修改,添加分布式锁来防止并发问题:

---

```diff
  	
  	public EasyResult actionForWxDk() {
+ 		String userId = getUserId();
+ 		String lockKey = "kq_lock_" + userId;
+ 		
+ 		// 尝试获取分布式锁
+ 		if(!Globals.getCache().tryLock(lockKey, 5)) { // 5秒超时
+ 			return EasyResult.fail("您的打卡请求正在处理中,请稍后再试");
+ 		}
+ 		
+ 		try {
  			JSONObject params = getJSONObject();
  			String dkTime = EasyDate.getCurrentDateString();
- 		try {
  			int signTimeType = params.getIntValue("signTimeType");
  			EasyRecord record = new EasyRecord("YQ_KQ_OBJ","DK_DATE","USER_ID");
  				WxMsgService.getService().sendKqDkNotice(model);
  			}
+ 			return EasyResult.ok(dkTime);
  		} catch (SQLException e) {
  			this.getLogger().error(null,e);
  			return EasyResult.fail(e.getMessage());
+ 		} finally {
+ 			// 释放锁
+ 			Globals.getCache().remove(lockKey);
  		}
- 		return EasyResult.ok(dkTime);
  	}
  
```

---

```thinking
看来Globals类中没有getCache()方法。让我检查一下项目中的缓存实现...

分析一下其他方案:

1. 使用数据库锁:
- 可以在yq_kq_obj表中添加一个lock_time字段
- 打卡前检查lock_time是否在5秒内
- 但这种方式性能较差,且可能有死锁风险

2. 使用Java synchronized:
- 可以使用ConcurrentHashMap存储用户锁
- 但这种方式只能在单机环境下工作
- 如果是分布式部署会失效

3. 使用乐观锁:
- 在yq_kq_obj表中添加version字段
- 更新时检查version
- 这种方式比较轻量,但可能会导致用户重试

4. 使用Redis:
- 需要先确认项目是否有Redis依赖
- 如果没有,添加新依赖可能影响较大

5. 使用数据库唯一索引:
- 在yq_kq_obj表中对(user_id, dk_date, sign_type)添加唯一索引
- 这是最简单可靠的方案
- 可以防止重复打卡记录

考虑到:
1. 不确定项目是否使用Redis
2. 不想引入新的依赖
3. 需要一个简单可靠的方案

我决定使用数据库唯一索引的方案,这是最简单且可靠的解决方案。
```

我建议通过以下步骤解决并发问题:

1. 首先添加唯一索引来从数据库层面防止重复打卡:

---

```bash
echo "ALTER TABLE yq_kq_obj ADD UNIQUE INDEX uk_user_date (user_id, dk_date);" > add_unique_index.sql
```

```
PS D:\develop\workbench202111\yq-work> echo "ALTER TABLE yq_kq_obj ADD UNIQUE IN
DEX uk_user_date (user_id, dk_date);" > add_unique_index.sql
PS D:\develop\workbench202111\yq-work>
```

---

2. 然后修改代码添加重复检查:

---

```diff
  	
  	public EasyResult actionForWxDk() {
- 		String userId = getUserId();
- 		String lockKey = "kq_lock_" + userId;
- 		
- 		// 尝试获取分布式锁
- 		if(!Globals.getCache().tryLock(lockKey, 5)) { // 5秒超时
- 			return EasyResult.fail("您的打卡请求正在处理中,请稍后再试");
- 		}
- 		
- 		try {
  		JSONObject params = getJSONObject();
  		String dkTime = EasyDate.getCurrentDateString();
+ 		try {
  			int signTimeType = params.getIntValue("signTimeType");
+ 			int dateId = EasyCalendar.newInstance().getDateInt();
+ 			
+ 			// 检查是否已打卡
+ 			if(signTimeType == 0) {
+ 				JSONObject kqRow = this.getQuery().queryForRow(
+ 					"select SIGN_IN from YQ_KQ_OBJ where DK_DATE = ? and USER_ID = ?",
+ 					new Object[] {dateId, getUserId()},
+ 					new JSONMapperImpl()
+ 				);
+ 				if(kqRow != null && StringUtils.notBlank(kqRow.getString("SIGN_IN"))) {
+ 					String signIn = kqRow.getString("SIGN_IN");
+ 					String time2 = "09:00";
+ 					LocalTime lt1 = LocalTime.parse(signIn);
+ 					LocalTime lt2 = LocalTime.parse(time2);
+ 					if (!lt1.isBefore(lt2)) {
+ 						return EasyResult.fail("已有今日上班卡,请勿重复打卡");
+ 					}
+ 				}
+ 			}
+ 			
  			EasyRecord record = new EasyRecord("YQ_KQ_OBJ","DK_DATE","USER_ID");
  			record.set("IP", WebKit.getIP(getRequest()));
  			record.set("device",getRequest().getHeader("User-Agent"));
  			record.set("CREATE_TIME", dkTime);
  			record.set("USER_ID", getUserId());
- 			record.set("DK_DATE", EasyCalendar.newInstance().getDateInt());
- 			record.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
+ 			record.set("DK_DATE", dateId);
+ 			record.set("DATE_ID", dateId);
  			record.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
  			record.set("DK_CITY", params.getString("city"));
  				record.set("DK_RANK", "加班");
  			}
- 			boolean existData  = this.getQuery().update(record);
+ 			
+ 			try {
+ 				boolean existData = this.getQuery().update(record);
  				if(!existData) {
  					record.set("KQ_ID",RandomKit.uuid());
  					record.set("DEPT", getStaffInfo().getDeptName());
  					this.getQuery().save(record);
+ 				}
+ 			} catch(SQLException e) {
+ 				// 如果是唯一键冲突,说明并发打卡,返回友好提示
+ 				if(e.getMessage().contains("Duplicate entry")) {
+ 					return EasyResult.fail("您的打卡请求正在处理中,请稍后再试");
+ 				}
+ 				throw e;
  			}
  			
  			String address = params.getString("address");
  			if(signTimeType==0) {
- 				JSONObject kqRow = this.getQuery().queryForRow("select * from YQ_KQ_OBJ where DK_DATE = ? and USER_ID = ?",new Object[] {EasyCalendar.newInstance().getDateInt(),getUserId()},new JSONMapperImpl());
- 				if(kqRow!=null&&!kqRow.isEmpty()) {
- 					String signIn = kqRow.getString("SIGN_IN");
- 					if(StringUtils.notBlank(signIn)) {
- 						String time2 = "09:00";
- 						LocalTime lt1 = LocalTime.parse(signIn);
- 						LocalTime lt2 = LocalTime.parse(time2);
- 						if (!lt1.isBefore(lt2)) {
- 					        return EasyResult.fail("已有今日上班卡，请勿重复");
- 					    }
- 					}
- 				}
  				record.set("ADDRESS_IN", address);
  				record.set("SIGN_IN", EasyDate.getCurrentDateString("HH:mm"));
  				WxMsgService.getService().sendKqDkNotice(model);
  			}
- 			return EasyResult.ok(dkTime);
  		} catch (SQLException e) {
  			this.getLogger().error(null,e);
  			return EasyResult.fail(e.getMessage());
- 		} finally {
- 			// 释放锁
- 			Globals.getCache().remove(lockKey);
  		}
+ 		return EasyResult.ok(dkTime);
  	}
  
```

---

```thinking
代码修改已经完成,让我总结一下解决方案:

1. 添加了唯一索引uk_user_date(user_id, dk_date)来从数据库层面防止重复打卡

2. 代码层面的改进:
- 提前检查是否已经打过上班卡
- 使用try-catch捕获唯一索引冲突异常
- 优化了代码结构,减少重复查询
- 统一使用dateId变量,避免多次调用getDateInt()

3. 并发控制策略:
- 主要依靠数据库唯一索引实现
- 在发生并发时返回友好提示
- 无需额外的分布式锁机制

4. 优点:
- 简单可靠,不依赖额外组件
- 性能开销小
- 不会产生死锁
- 容易维护

5. 注意事项:
- 需要DBA执行添加索引的SQL
- 索引可能会略微影响写入性能
- 需要监控唯一索引冲突的情况

这个方案应该能很好地解决并发打卡的问题。
```

以上就是解决方案,主要通过以下方式防止并发问题:

1. 执行`add_unique_index.sql`添加唯一索引,从数据库层面防止重复打卡
2. 代码中增加了重复检查和异常处理
3. 优化了代码结构,提高了性能

这个方案的优点是:
- 简单可靠,不依赖额外组件
- 性能开销小
- 不会产生死锁
- 容易维护

您需要请DBA执行添加索引的SQL文件。如果您有任何疑问,我很乐意为您解答。

---



---

