package com.yunqu.work.servlet.flow;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.StaffService;

@WebServlet("/servlet/flow/approve/*")
public class ApproveServlet extends BaseFlowServlet {
	private static final long serialVersionUID = 1L;

	public EasyResult actionForDoApprove() {
		EasyQuery query = this.getQuery();
		String msg = "操作成功";
		try {
			query.begin();
			JSONObject params = getJSONObject();
			String flowCode = params.getString("flowCode");
			String businessId = params.getString("businessId");
			String resultId = params.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("审批人不存在");
			}
			
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			
			int ccCount = 0;
			String ccIdsStr = params.getString("ccIds");
			if(StringUtils.notBlank(ccIdsStr)) {
				String[] ccIds = ccIdsStr.split(",");
				ccCount = ccIds.length;
				for(String ccId:ccIds) {
					EasyRecord record = new EasyRecord("yq_flow_cc","cc_id");
					record.set("cc_id", RandomKit.uniqueStr());
					record.set("cc_by", ccId);
					record.set("cc_by_name", StaffService.getService().getUserName(ccId));
					record.set("business_id", businessId);
					record.set("result_id", resultId);
					record.set("cc_time", EasyDate.getCurrentDateString());
					query.save(record);
				}
			}
			
			//获取下一级节点
			ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
			String nodeId = approveNode.getNodeId();
			String nextNodeId = approveNode.getNextNodeId();
			String _nextNodeId = params.getString("nextNodeId");
			String _nextNodeCode = params.getString("nextNodeCode");
			
			if(StringUtils.isNotBlank(_nextNodeCode)) {
				if("0".equals(_nextNodeCode)) {
					nextNodeId = "0";
				}else {
					ApproveNodeModel node = ApproveService.getService().getNodeInfo(flowCode, _nextNodeCode, getUserId());
					if(node==null) {
						return EasyResult.fail(_nextNodeCode+"不存在,请联系管理员.");
					}
					nextNodeId = node.getNodeId();
				}
			}else if(StringUtils.isNotBlank(_nextNodeId)) {
				nextNodeId = _nextNodeId;
			}
			
			FlowModel flow = ApproveService.getService().getFlow(flowCode);
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);

			int checkResult = params.getIntValue("checkResult");//1 通过 2拒绝
			String checkDesc = params.getString("checkDesc");
			EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("business_id", businessId);
			record.set("check_user_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc", checkDesc);
			record.set("check_time", EasyDate.getCurrentDateString());
			record.set("cc_count", ccCount);
			if("0".equals(nextNodeId)||checkResult==2) {
				query.update(record);
			}
			
			MessageModel msgModel = new MessageModel();
			msgModel.setTypeName(flow.getFlowCode());
			msgModel.setSender(getUserId());
			msgModel.setSendName(getUserName());
			msgModel.setFkId(businessId);
			msgModel.setMsgLabel("待审批");
			msgModel.setTitle(apply.getApplyTitle());
			msgModel.setData1(flow.getFlowName());
			msgModel.setData2(apply.getApplyName());
			msgModel.setData3(apply.getApplyTime());
			msgModel.setData4("待审批");
			msgModel.setDesc(apply.getApplyRemark());
			
			if(checkResult==2) {
				FlowService.getService().updateApplyReturn(businessId);
				msgModel.setData4("审批退回");
				msgModel.setMsgLabel("审批退回");
				msgModel.setDesc("审批退回："+checkDesc);
				msgModel.setReceiver(apply.getApplyBy());
				this.sendAllMsg(msgModel);
				return EasyResult.ok();
			}
			
			String nextResultId  = "0";
			if("0".equals(nextNodeId)) {//完成了
				msg = "审批完成,流程结束";
				msgModel.setMsgLabel("审批完成");
				msgModel.setReceiver(apply.getApplyBy());
				FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
			}else {
				msgModel.setMsgLabel("请尽快审批");
				
				ApproveNodeModel nextNodeInfo = ApproveService.getService().getNodeInfo(nextNodeId,apply.getApplyBy(),params);
				if(nextNodeInfo==null) {
					return EasyResult.fail("获取不到下一级审批,请联系管理员");
				}
				
				query.update(record);
				
				String todoCheckUserId = nextNodeInfo.getCheckBy();
				if(todoCheckUserId.indexOf(apply.getApplyBy())>-1) {
					 nextNodeId = nextNodeInfo.getNextNodeId();
					 if("0".equals(nextNodeId)) {
						 FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
						 return EasyResult.ok();
					 }else {
						 nextNodeInfo = ApproveService.getService().getNodeInfo(nextNodeId,getUserId(),params);
					 }
				}
				msgModel.setMsgLabel(nextNodeInfo.getNodeName());
				msgModel.setReceiver(nextNodeInfo.getCheckBy());
				

				EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
				nextResultId = RandomKit.uniqueStr();
				resultRecord.set("result_id", nextResultId);
				resultRecord.set("business_id", businessId);
				resultRecord.set("check_name", nextNodeInfo.getCheckName());
				resultRecord.set("check_user_id", nextNodeInfo.getCheckBy());
				resultRecord.set("check_result", 0);
				resultRecord.set("node_id",nextNodeId);
				resultRecord.set("node_name",nextNodeInfo.getNodeName());
				resultRecord.set("check_desc","");
				resultRecord.set("ip_address",WebKit.getIP(getRequest()));
				resultRecord.set("get_time",EasyDate.getCurrentDateString());
				query.save(resultRecord);
				msg = "提交成功,审批下一个节点:"+nextNodeInfo.getNodeName();
				
				FlowService.getService().updateApplyInfo(businessId, resultId,nextResultId,nodeId,nextNodeId, 20);
			}
			this.sendAllMsg(msgModel);
			
			query.commit();
		} catch (SQLException e) {
			this.error(null, e);
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(null, ex);
			}
			return EasyResult.fail(e.getMessage());
			
		}
		return EasyResult.ok(null,msg);
	}
	
	public EasyResult actionForUpdateApply() {
		return updateApply();
	}
	

	
	/**
	 *提交流程
	 */
	public EasyResult actionForSubmitApply() {
		return submitApply();
	}
	
	public EasyResult actionForSubmitTrust() {
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			JSONObject params = getJSONObject();
			String resultId = params.getString("resultId");
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			String trustId = RandomKit.uniqueStr();
			EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
			record.setPrimaryValues(resultId);
			record.set("trust_id",trustId);
			record.set("get_time",EasyDate.getCurrentDateString());
			record.set("read_time","");
			record.set("check_desc",getUserName()+"发起的流程委托");
			record.set("check_user_id",params.getString("trustUserId"));
			record.set("check_name",params.getString("trustUserName"));
			
			EasyRecord trustRecord = new EasyRecord("yq_flow_trust","trust_id");
			trustRecord.setPrimaryValues(trustId);
			trustRecord.set("result_id", resultId);
			trustRecord.set("trust_by",getUserId());
			trustRecord.set("trust_by_name",getUserName());
			trustRecord.set("trust_user_id",params.getString("trustUserId"));
			trustRecord.set("trust_user_name",params.getString("trustUserName"));
			trustRecord.set("trust_reason",params.getString("trustReason"));
			trustRecord.set("trust_time",EasyDate.getCurrentDateString());
			
			query.update(record);
			query.save(trustRecord);
			query.commit();
		} catch (SQLException e) {
			this.error(null, e);
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(null, ex);
			}
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	
	public EasyResult actionForAddReadTime() {
		JSONObject params = getJSONObject();
		String resultId = params.getString("resultId");
		try {
			this.getQuery().executeUpdate("update yq_flow_approve_result set read_time = ? where result_id = ?", EasyDate.getCurrentDateString(),resultId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForGetNodeInfoByResultId() {
		JSONObject params = getJSONObject();
	
		String resultId = params.getString("resultId");
		
		JSONObject result = new JSONObject();
		
		ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
		result.put("nowNode", approveNode);
		
		ApproveResultModel approveResult = ApproveService.getService().getResult(resultId);
		result.put("nowResult", approveResult);
		
		
		if("0".equals(approveNode.getNextNodeId())) {
			result.put("nextNode", new ApproveNodeModel());
		}else {
			ApproveNodeModel nextNodeInfo = ApproveService.getService().getNode(approveNode.getNextNodeId());
			result.put("nextNode", nextNodeInfo);
		}
		return EasyResult.ok(result);
	}
}
