<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
	<style>
		.select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
   			 background-color: #fff;
		}
		#editForm .layui-table td {
		    padding: 2px 8px;
		}
		#editForm .select2-container--bootstrap .select2-selection--single {
		    height: 30px;
		    line-height: 1.62857143;
		    padding: 2px 8px 2px 8px;
		    font-size: 13px;
		}
		#editForm img{max-width: 100%;height: auto;}
		.layui-table-tips{z-index: 999999999999999999!important;};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editForm" style="margin-bottom: 100px;">
	     				<input type="hidden" name="projectId" value="${param.projectId}"/>
	     				<input type="hidden" name="projectName" value="${param.projectName}">
	     				<input type="hidden" name="task.TASK_TYPE" value="2">
	     				<input type="hidden" name="task.PROJECT_ID" value="${param.projectId}"/>
						<table class="layui-table" lay-skin="nob">
							  <thead>
							    <tr>
							      <th>指派给</th>
							      <th>模块</th>
							      <th>标题</th>
							      <th>开始时间</th>
							      <th>结束时间</th>
							      <th>类型</th>
							      <th>等级</th>
							      <th>任务内容	<a class="btn btn-xs btn-link" onclick="addTaskTr();" href="javascript:;" style="position: absolute;right: 15px;">+添加</a></th>
							    </tr> 
							  </thead>
							  <tbody>
							  	<c:forEach var="item" begin="1" end="50">
								    <tr id="${item}" <c:if test="${item>3}">data-hide-tr="1" style="display:none"</c:if>>
								      <td style="width: 120px;">		
						      			  <select onchange="selectUser('${item }',$(this))" data-rules="required" data-mars="ProjectDao.projectTeamsDict" name="assignUserId_${item}" class="form-control input-sm users">
				                    		<option value="">请选择</option>
				                    	  </select>
				                       </td>
				                       <td style="width: 130px;">
				                       	  <input type="hidden" name="module_name_${item}"/>
						      			  <select onchange="selectModule('${item }',$(this))" data-mars="ProjectDao.modules" name="moduleId_${item}" class="form-control input-sm modules">
				                    		<option value="">/</option>
				                    	  </select>
				                       </td>
				                       <td>
				                       		<input type="text" data-rules="required" name="title_${item}" class="form-control input-sm"/>
				                       </td>
				                       <td style="width: 140px;">
				                       		<input type="text" data-rules="required" data-mars="CommonDao.date02" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" name="start_time_${item}"  class="form-control input-sm">
				                       </td>
				                       <td style="width: 140px;">
				                       		<input type="text" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 18:00:00',doubleCalendar:true,alwaysUseStartDate:true})" name="end_time_${item}"  class="form-control input-sm">
				                       </td>
				                       <td style="width: 100px;">
				                       	    <select name="typeId_${item}" data-rules="required" data-mars="TaskDao.taskType" class="form-control input-sm"></select>
				                       </td>
				                       <td style="width: 100px;">
				                       	    <select name="level_${item}" data-rules="required" class="form-control input-sm">
				                       	    	<option value="1">较低</option>
				                       	    	<option value="2">普通</option>
				                       	    	<option value="3">紧急</option>
				                       	    	<option value="4">非常紧急</option>
				                       	    </select>
				                       </td>
								       <td style="width: 20%;">
								      	<input type="hidden" name="ids" value="${item}"/>
								      	<textarea style="height: 40px;" name="desc_${item}" class="form-control input-sm"></textarea>
								      	<a class="btn btn-xs btn-link" href="javascript:;" onclick="$('#${item}').remove();" style="position: absolute;top: 15px;right: 15px;">删除</a>
								      </td>
								    </tr>
							  	</c:forEach>
							  </tbody>
							</table>
							<table class="table table-edit table-vzebra">
						        <tbody>
						            <tr>
					                    <td>抄送人</td>
					                    <td>
					                    	<select  multiple="multiple" data-mars="CommonDao.userDict" id="mailtos" name="mailtos" class="form-control input-sm">
					                    	</select>
					                    </td>
					                </tr>
						            <tr>
						              	<td>
						            		提醒方式
						            	</td>
						            	<td>
						            		<label class="checkbox checkbox-default checkbox-inline">
				                    			<input type="checkbox" value="1" disabled="disabled" name="task.NOTICE_TYPES"/> <span>即时通讯</span>
					                    	</label>
						            		<label class="checkbox checkbox-default checkbox-inline">
				                    			<input type="checkbox" checked="checked" value="2" onclick="$(this).prop('checked', true);" name="task.NOTICE_TYPES"/> <span>系统私信</span>
					                    	</label>
					                    	<label class="checkbox checkbox-default checkbox-inline">
						                    	<input type="checkbox" value="3" checked="checked"  name="task.NOTICE_TYPES"/> <span>邮件提醒</span>
					                    	</label>
					                    	<label class="checkbox checkbox-default checkbox-inline">
						                    	<input type="checkbox" value="4" name="task.NOTICE_TYPES" checked="checked"/> <span>微信推送</span>
					                    	</label>
						            	</td>
						            </tr>
						        </tbody>
		  					  </table>
							 <div class="layer-foot text-c" style="z-index: 99999999999;box-shadow: 0 -10px 20px 0 rgba(45,67,118,.1);position: fixed;">
							    	  <button type="button" class="btn btn-primary btn-sm" onclick="Task.ajaxSubmitForm()"> 保 存  </button>
								      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.closeTab();"> 关闭 </button>
							</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		
		jQuery.namespace("Task");
		
		$(function(){
		    $('[data-toggle="tooltip"]').tooltip();
		    $("#editForm").render({success:function(){
				 requreLib.setplugs('select2',function(){
					$("#editForm .users,.modules,#editForm #mailtos").select2({theme: "bootstrap",placeholder:'请选择'});
				 });
		    }});
		});
		function selectUser(id,el){
			try{
				var nextEl=$("#"+id).next().find("select:eq(0)");
				if(nextEl.length>0&&nextEl.val()==''){
					nextEl.val(el.val());
					nextEl.select2({theme: "bootstrap",placeholder:'请选择'});
				}
			}catch(e){
				console.error(e);
			}
		}
		function selectModule(id,el){
			try{
				el.prev().val(el.find('option:selected').text());
				var nextEl=$("#"+id).next().find("select:eq(1)");
				if(nextEl.length>0&&nextEl.val()==''){
					nextEl.val(el.val());
					nextEl.select2({theme: "bootstrap",placeholder:'请选择'});
				}
			}catch(e){
				console.error(e);
			}
		}
		function fillDate(val){
			var v = addDate(val);
			if(v.indexOf("NaN")>-1){
				layer.msg("时间格式不正确,请检查浏览器兼容性或者换chome");
			}else{
				$("#endDate").val(v);
			}
		}
		function addDate(days) {
            if (days == undefined || days == '') {
                days = 1;
            }
            var d=$("#beginDate").val();
            if(d){
	            var date = new Date(d.replace(/-/g,'/'));
	            date.setDate(date.getDate() + days);
	            var month = date.getMonth() + 1;
	            var day = date.getDate();
	            var h = date.getHours();
	            var m = date.getMinutes();
	          //  return date.getFullYear() + '-' + getFormatDate(month) + '-' + getFormatDate(day)+" "+ getFormatDate(h)+":"+ getFormatDate(m);
	            return date.getFullYear() + '-' + getFormatDate(month) + '-' + getFormatDate(day)+" 18:00";
            }else{
            	return "";
            }
        }
		function getFormatDate(arg) {
            if (arg=='0'||arg) {
	            var re = arg + '';
	            if (re.length < 2) {
	                re = '0' + re;
	            }
	            return re;
            }else{
                return '';
            }
        }

		Task.ajaxSubmitForm = function(){
			$("[data-hide-tr]").remove();
			if(form.validate("#editForm")){
				Task.insertData(); 
			};
		}
		Task.insertData = function(flag) {
			$("[data-hide-tr]").remove();
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/task?action=batchAdd",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.closeTab();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function addTaskTr(){
			var obj  = $("[data-hide-tr]").first();
			obj.removeAttr("data-hide-tr");
			obj.show();
			
			obj.find("select").val(obj.prev().find("select:eq(0)").val());
			obj.find("select").select2({theme: "bootstrap",placeholder:'请选择'});
			
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>