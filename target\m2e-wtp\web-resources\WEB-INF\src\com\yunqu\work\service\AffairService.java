package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.HashSet;
import java.util.Set;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.AffairModel;

public class AffairService extends AppBaseService{
	
	private static class Holder{
		private static AffairService service = new AffairService();
	}
	
	public static AffairService getService(){
		return Holder.service;
	}
	
	public void updateAffairObj(AffairModel model,boolean isAdd) throws SQLException {
		String receiverUserId = model.getString("RECEIVER_USER_ID");
		String receiverDeptId = model.getString("RECEIVER_DEPT_ID");
		
		Set<String> userIds = new HashSet<String>();
		
		if(StringUtils.notBlank(receiverDeptId)) {
			EasySQL sql = new EasySQL("select group_concat(t1.user_id) from easi_dept_user t1,easi_user t2 where t1.user_id = t2.user_id and t2.state = 0 ");
			sql.appendIn(receiverDeptId.split(","), "and t1.dept_id");
			receiverDeptId = this.getMainQuery().queryForString(sql.getSQL(),sql.getParams());
			String[] receiverUserIds = receiverDeptId.split(",");
			for(String userId:receiverUserIds) {
				userIds.add(userId);
			}
		}
		
		String[] receiverUserIds = receiverUserId.split(",");
		for(String userId:receiverUserIds) {
			if(StringUtils.notBlank(userId)) {
				userIds.add(userId);
			}
		}
		
		if(!isAdd) {
			EasySQL sql = new EasySQL("select group_concat(user_id) from yq_affair_obj where 1=1");
			sql.append(model.getAffairId(), "and affair_id = ?");
			String result = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			String[] array = result.split(",");
			for(String userId:array) {
				if(!userIds.contains(userId)) {
					this.getQuery().executeUpdate("delete from yq_affair_obj where affair_id = ? and user_id = ?",model.getAffairId(),userId);
				}else {
					userIds.remove(userId);
				}
			}
		}
		
		for(String userId:userIds) {
			EasyRecord record = new EasyRecord("yq_affair_obj","obj_id");
			record.setPrimaryValues(RandomKit.uniqueStr());
			record.set("affair_id",model.getPrimaryValue());
			record.set("user_id",userId);
			record.set("user_name",StaffService.getService().getUserName(userId));
			record.set("state",0);
			record.set("add_time",EasyDate.getCurrentDateString());
			this.getQuery().save(record);
		}
		
	}
	
	public EasyResult addDeptWeeklyAffair(String affairName,String affairDesc,String creator) {
		String deptId = StaffService.getService().getStaffInfo(creator).getDeptId();
		String deptSecondLeader = StaffService.getService().getDeptSecondLeader(deptId);
		return addAffair(affairName, affairDesc, creator,creator,deptSecondLeader);
	}
	
	public EasyResult addAffair(String affairName,String affairDesc,String creator) {
		return addAffair(affairName, affairDesc, creator,creator,null);
	}
	
	public EasyResult addAffair(String affairName,String affairDesc,String creator,String receiver,String ccIds) {
		if(StringUtils.isBlank(creator)) {
			return EasyResult.fail();
		}
		if(StringUtils.isBlank(receiver)) {
			receiver = creator;
		}
		AffairModel model = new AffairModel();
		model.set("AFFAIR_STATE",10);
		model.set("SEND_TYPE",0);
		model.set("AFFAIR_ID",FlowService.getService().getID());
		model.set("AFFAIR_NAME",affairName);
		model.set("AFFAIR_DESC",affairDesc);
		model.set("RECEIVER_USER_ID",StaffService.getService().getNormalUserId(receiver));
		model.set("RECEIVER_USER_NAME",StaffService.getService().getNormalUserName(StaffService.getService().getUserName(receiver)));
//		model.set("RECEIVER_DEPT_ID",deptId);
//		model.set("RECEIVER_DEPT_NAME",StaffService.getService().getStaffInfo(creator).getDeptName());
		
		if(StringUtils.notBlank(ccIds)) {
			model.set("SHARE_IDS",StaffService.getService().getNormalUserId(ccIds));
			model.set("SHARE_NAMES",StaffService.getService().getNormalUserName(StaffService.getService().getUserName(ccIds)));
		}
		
		model.addCreateTime();
		model.setCreator(creator);
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", creator);
		model.set("CREATE_NAME", StaffService.getService().getUserName(creator));
		try {
			int sendType = model.getIntValue("SEND_TYPE");
			this.getQuery().save(model);
			this.updateAffairObj(model,true);
			this.getQuery().executeUpdate("update yq_affair t1 set t1.receiver_count =  (select count(1) from yq_affair_obj t2 where t2.affair_id = t1.affair_id) where t1.affair_id= ?", model.getAffairId());
			int affairState = model.getIntValue("AFFAIR_STATE");
			if(sendType==0&&affairState==10) {
				TaskNoticeService.getService().affairNotice(model.getAffairId());
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}

}
