package com.yunqu.work.model;

import com.yunqu.work.base.AppBaseModel;

public class OpLogModel extends AppBaseModel {
   private static final long serialVersionUID = 1L;
	
   private String logId;
	
   private String opBy;
   
   private String fkId;
   
   private String opTime;
   
   private String url;
   
   private String content;
   
   private String param;
   
	
   public OpLogModel(){
	   setTableInfo("YQ_OPLOG", "LOG_ID");
   }

	public String getLogId() {
		return logId;
	}
	
	
	public void setLogId(String logId) {
		this.logId = logId;
		this.set("log_id", logId);
	}
	
	
	public String getOpBy() {
		return opBy;
	}
	
	
	public void setOpBy(String opBy) {
		this.opBy = opBy;
		this.set("op_by", opBy);
	}
	
	
	public String getFkId() {
		return fkId;
	}
	
	
	public void setFkId(String fkId) {
		this.fkId = fkId;
		this.set("fk_id", fkId);
	}
	
	
	public String getOpTime() {
		return opTime;
	}
	
	
	public void setOpTime(String opTime) {
		this.opTime = opTime;
		this.set("op_time", opTime);
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
		this.set("op_url", url);
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
		this.set("content", content);
	}
	public String getParam() {
		return param;
	}
	public void setParam(String param) {
		this.param = param;
		this.set("op_param", param);
	}
	
	   
   

}
