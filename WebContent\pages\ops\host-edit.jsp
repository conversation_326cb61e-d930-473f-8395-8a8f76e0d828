<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>机房</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="HostDao.record" autocomplete="off" data-mars-prefix="host.">
     		   <input type="hidden" value="${param.hostId}" name="host.HOST_ID"/>
     		   <input type="hidden" value="${param.projectId}" name="projectId"/>
     		   <input type="hidden" value="${param.projectId}" name="host.PROJECT_ID"/>
						<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td style="width: 80px" class="required">机房</td>
				                    <td>
				                    	<select data-value="${param.serverRoomId}" data-mars="ServerRoomDao.dict" data-rules="required" name="host.SERVER_ROOM_ID" class="form-control input-sm">
				                    		<option value="">请选择</option>
				                    	</select>
				                    </td>
					            </tr>
					            <tr>
				                    <td style="width: 80px" class="required">主机名称</td>
				                    <td><input data-rules="required"  type="text" name="host.HOST_NAME" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td style="width: 80px" class="required">服务器型号</td>
				                    <td><input value="0" data-rules="required"  type="text" name="host.SERVER_MODEL" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td style="width: 80px" class="required">主机类型</td>
				                    <td>
				                    	<label class="radio radio-success radio-inline">
					                  	    <input type="radio" checked="checked" value="1" name="host.HOST_TYPE"/> <span>实体主机</span>
				                    	</label>
				                    	<label class="radio radio-success radio-inline">
					                    	<input type="radio" value="2" checked="checked" name="host.HOST_TYPE"/> <span>虚拟主机</span>
				                    	</label>
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required">内网IP</td>
				                    <td>
				                    	<input data-rules="required"  type="text" name="host.PRIVATE_IP" class="form-control input-sm">
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required">公网IP</td>
				                    <td>
				                   		 <input value="0" data-rules="required"  type="text" name="host.PUBLIC_IP" class="form-control input-sm">
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required">内存</td>
				                    <td>
				                    	<div class="input-group input-group-sm">
					                        <input value="0" data-rules="required"  type="text" name="host.MEMORY" class="form-control input-sm">
											 <span class="input-group-addon">GB</span>	
								    	 </div>
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required">磁盘</td>
				                    <td>
				                    	 <div class="input-group input-group-sm">
						                   	 <input value="0" data-rules="required" value="" type="text" name="host.DISK_SIZE" class="form-control input-sm">
											 <span class="input-group-addon">GB</span>	
								    	 </div>
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required">操作系统</td>
				                    <td>
				                    	<select data-rules="required" name="host.OS_NAME" class="form-control input-sm">
				                    		<option value="Linux">Linux</option>
				                    		<option value="Microsoft Windows">Microsoft Windows</option>
				                    		<option value="Solaris">Solaris</option>
				                    		<option value="Novell NetWare">Novell NetWare</option>
				                    		<option value="VMware ESX">VMware ESX</option>
				                    		<option value="Other">Other</option>
				                    	</select>
					            </tr>
					            <tr>
				                    <td>备注</td>
				                    <td>
						                   	 <textarea name="host.REMARK" style="height: 80px" class="form-control input-sm"></textarea>
				                    </td>
					            </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c">
						 		  <c:if test="${!empty param.hostId}">
								    	  <button type="button" class="btn btn-info btn-sm"  onclick="Edit.del()"> 删除 </button>
						 		  </c:if>
						    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("Edit");
		Edit.hostId='${param.hostId}';
		
		$(function(){
			$("#editForm").render({success:function(){
				requreLib.setplugs('select2',function(){
				   $("#editForm select[name='host.SERVER_ROOM_ID']").select2({theme: "bootstrap",placeholder:'请选择'});
				});
			}});  
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.hostId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/host?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/host?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		};
		Edit.del=function(data){
			layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/host?action=delete",{'host.HOST_ID':Edit.hostId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							list.query();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>