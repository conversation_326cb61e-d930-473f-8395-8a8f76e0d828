<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
	<style>
		#selectUserForm{padding:5px 10px;}
		.nowUser .label{
			display: inline-block;
		    margin-bottom: 5px;
		    min-width: 78px;
		    margin-right: 5px;
		    height: 26px;
		    line-height: 20px;
		}
		.layui-tree-entry{height: 30px;}
		.nowUser .layui-icon-close{cursor: pointer;font-size: 12px;float: right;}
		.nowUser .layui-icon-close:hover{color: red;}
		#ztree{height: 305px;overflow-y: scroll;overflow-x: hidden;margin: 5px -5px;}
		#selectUserForm .groupList{height: 325px;overflow-y: scroll;overflow-x: hidden;}
		#selectUserForm .layui-tab-item{padding: 0px;}
		#selectUserForm .layui-tab-content{padding: 0px;}
		#selectUserForm .groupList p{height: 36px;line-height: 36px;padding-left: 5px;margin-left: 10px;}
		#selectUserForm .groupList p:hover{background-color: #eee;cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectUserForm">
        		<input name="busiType" type="hidden" id="busiType"/>
       			<input name="groupId" id="groupId" type="hidden"/>
       			<input name="deptId" id="deptId" type="hidden"/>
				<input name="deptCode" id="deptCode" type="hidden"/>
            	<div class="ibox">
            		<div class="layui-row list-user" style="margin-top: -18px;">
					    <div class="layui-col-md12"><hr class="mt-5 mb-5"></div>
					    <div class="layui-col-md12" style="height: 32px;line-height: 32px;">
					    	<div class="pull-left f-12">已选择 <span id="selectCount">0</span> 项</div> 
					    	<div class="pull-right layui-hide-xs">
					    		<button class="btn btn-xs btn-default mr-5" type="button" onclick="SelectUser.inputUserName()">批量查找</button>
					    		<button class="btn btn-xs btn-default" type="button" onclick="SelectUser.createGroup()">+创建分组</button>
					    	</div>
					    </div>
					</div>
					<div class="layui-row list-user">
					    <div class="nowUser layui-col-md12"><span class="msg f-12 layui-hide-xs">请选择</span></div>
					</div>
	            	<div class="layui-row layui-col-space5">
					  <div class="layui-col-md4">
					  	<div style="margin-top: 0px;">
					  		 <div class="input-group input-group-sm" style="width: 100%;">
								 <input type="search" name="userName" placeholder="支持拼音简写,回车键搜索" class="form-control" style="background-color: #f2f2f2;">
						     </div>
			    			 <button type="button" class="btn btn-sm btn-default hidden ml-5"  data-event="enter"  onclick="SelectUser.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					  	</div>
					    <div class="layui-tab layui-tab-brief layui-hide-xs" lay-filter="typeFilter" style="margin: 0px;margin-bottom: -8px;">
          		 	 		<ul class="layui-tab-title"><li data-val="dept" class="layui-this">部门</li><li data-val="group">分组</li><li data-val="often">常用</li></ul>
          		 	 		<div class="layui-tab-content">
          		 	 			 <div class="layui-tab-item layui-show"><div id="ztree"></div></div>
								 <div class="layui-tab-item groupList" data-mars="FlowConfigDao.groupList" data-template="select-usergroup-template"></div>
								 <div class="layui-tab-item">
								 	<p style="padding: 10px 5px;">点击右侧选择</p>
								 </div>
          		 	 		</div>
          		 	 	</div>
	            	  </div>
					  <div class="layui-col-md8" style="margin-top: -10px;">
	              			<table id="userList"></table>
					  </div>
					</div>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary" type="button" onclick="SelectUser.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
		   		<button class="btn btn-sm btn-link ml-20"  type="button" onclick="SelectUser.clearData();">清除</button>
			</div>
        </form>
        <script id="select-usergroup-template" type="text/x-jsrender">
			      {{for data}}
			          <p onclick="SelectUser.selectGroup('{{:GROUP_ID}}')"><i class="fa fa-address-card" aria-hidden="true"></i> {{:GROUP_NAME}}</p>
			      {{/for}}
				  {{if data.length==0}} <p>无数据</p> {{/if}}
		</script>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
			
			var SelectUser={
					sid:'${param.sid}',
					type:'radio',
					query : function(){
						$("#selectUserForm").queryData();
					},
					initData:function(){
						var el = $("[data-sid='"+SelectUser.sid+"']");
						var names = el.val();
						var ids = '';
						if(el.prev().length==0){
							ids = names;
						}else{
							ids = el.prev().val();
						}
						if(names){
							var array1 = names.split(',');
							var array2 = ids.split(',');
							for(var index in array1){
								SelectUser.addRow(array2[index],array1[index]);
							}
						}
						
					},
					initTable : function(){
						var type = '${param.type}';
						SelectUser.type = type;
						if(type=='checkbox'){
							SelectUser.initData();
						}else{
							$('#selectUserForm .layui-col-md10').css('width','100%');
							$('.nowUser,.list-user').remove();
						}
						var height = '400px';
						var device = layui.device();
					    if(device.mobile){
					    	height = 'full-220';
					    }
						$("#selectUserForm").initTable({
							mars:'EhrDao.userList',
							id:'userList',
							page:true,
							limit:15,
							height:height,
							limits:[15,20,50,100,200,300,500],
							rowEvent:'rowEvent',
							rowDoubleEvent:'SelectUser.ok',
							cols: [[
					        {type:type},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'USERNAME',
								title: '姓名',
								width:70
							},{
							    field: 'STAFF_NO',
								title: '工号',
								width:70
							},{
							    field: 'DEPTS',
								title: '部门',
								width:120
							},{
							    field: 'JOB_TITLE',
								title: '职位',
								width:130
							}
							]],done:function(){
								table.on('checkbox(userList)', function(obj){
									if(obj.type=='all'){
										if(obj.checked){
											var checkStatus = table.checkStatus('userList');
											if(checkStatus.data.length>0){
												var data = checkStatus.data;
												for(var index in data){
													SelectUser.addRow(data[index]['USER_ID'],data[index]['USERNAME']);
												}
											}
										}else{
											$('.nowUser').empty();
										}
									}else{
									   if(obj.checked){
										   SelectUser.addRow(obj.data.USER_ID,obj.data.USERNAME);
									   }else{
										   $(".nowUser [data-id='"+obj.data.USER_ID+"']").remove();
									   }
										
									}
								});  
							}
					       }
						);
					},
					addRow:function(userId,username){
						$(".nowUser .msg").remove();
						if(userId==''||userId==undefined){
							userId = username;
						}
						$(".nowUser [data-id='"+userId+"']").remove();
						$('.nowUser').append('<div data-id="'+userId+'" data-name="'+username+'" class="label label-info label-outline">'+username+'<i class="layui-icon layui-icon-close" onclick="$(this).parent().remove();"></i></div>');
						$('#selectCount').text($('.nowUser div').length);
					},
					ok : function(selectRow,obj){
						var el = $("[data-sid='"+SelectUser.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('userList');
							if(SelectUser.type=='checkbox'){
								checkStatus.data = []; 
								$(".nowUser [data-id]").each(function(){
									var t = $(this);
									checkStatus.data.push({USERNAME:t.data('name'),USER_ID:t.data('id')});
								});
								if(checkStatus.data.length>0){
									var names = [];
									var ids = [];
									var data = checkStatus.data;
									for(var index in data){
										names.push(data[index]['USERNAME']);
										ids.push(data[index]['USER_ID']);
									}
									el.val(names.join(','));
									if(el.prev().length>0){
										el.prev().val(ids.join(','));
									}
									
									popup.layerClose("selectUserForm");
								}else{
									el.val('');
									if(el.prev().length>0){
										el.prev().val('');
									}
									popup.layerClose("selectUserForm");
								}
							}else{
								if(checkStatus.data.length>0){
									doSelect(checkStatus.data[0]);
								}else{
									el.val('');
									if(el.prev().length>0){
										el.prev().val('');
									}
								}
								popup.layerClose("selectUserForm");
							}
							
						}else{
							if(SelectUser.type=='checkbox'){
								SelectUser.addRow(selectRow['USER_ID'],selectRow['USERNAME']);
							}else{
								doSelect(selectRow);
								popup.layerClose("selectUserForm");
							}
						}
						
						function doSelect(selectRow){
							el.val(selectRow['USERNAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['USER_ID']);
							}
							
							var refDept = el.data('refDept');
							var refNo = el.data('refNo');
							var refJob = el.data('refJob');
							var refJoindate = el.data('refJoindate');
							if(refDept){
								var _refDept = $($g(refDept));
								_refDept.val(selectRow['DEPTS']);
							}
							if(refNo){
								var _refNo = $($g(refNo));
								_refNo.val(selectRow['STAFF_NO']);
							}
							if(refJob){
								var _refJob = $($g(refJob));
								_refJob.val(selectRow['JOB_TITLE']);
							}
							if(refJoindate){
								var _refJoindate = $($g(refJoindate));
								_refJoindate.val(selectRow['JOIN_DATE']);
							}
						}
						
						if(SelectUser.type=='checkbox'){
						     //获取checkbox  
			                var checked = $($($(obj.tr[0].firstChild)[0].firstElementChild)[0].lastChild);
			                //判断是否被选中
			                if(checked[0].className == "layui-unselect layui-form-checkbox layui-form-checked") {
			                	//设置为未选中的样式
			                    checked.attr("class","layui-unselect layui-form-checkbox");
			                } else {
			                	//设置为选中的样式
			                    checked.attr("class","layui-unselect layui-form-checkbox layui-form-checked");
			                }
						}
		                
					},
					clearData:function(){
						var el = $("[data-sid='"+SelectUser.sid+"']");
						el.val('');
						if(el.prev().length>0){
							el.prev().val('');
						}
						popup.layerClose("selectUserForm");
					},
					createGroup:function(){
						layer.prompt({title:'请输入组名称',offset:'20px'},function(val,index){
							layer.close(index);
							var ids = []; 
							$(".nowUser [data-id]").each(function(){
								var t = $(this);
								ids.push(t.data('id'));
							});
							var data = {groupName:val,userIds:ids.join()};
							ajax.remoteCall("/yq-work/web/flow/config/addGroup",data,function(result) { 
								if(result.state == 1){
									layer.msg(result.msg,{icon:1,time:800},function(){
										layer.closeAll();
									});
								}else{
									layer.alert(result.msg,{icon: 5});
								}
						   });
							
						});
					},
					inputUserName:function(){
						layer.prompt({title:'请输入、每行一个姓名',offset:'20px',formType:2,area:['400px','300px']},function(val,index){
							layer.close(index);
							var list = val.replaceAll('\n',',');
							layer.msg("查找"+list.split(',').length+"人,如需要大量一次性选择，请分页设置最大",{time:1200,icon:7,offset:'50px'},function(){
								$("#selectUserForm [name='userName']").val(list);
								SelectUser.query();
							});
						});
					},
					initTree:function(){
						ajax.remoteCall("${ctxPath}/servlet/ehr?action=getFrameworkTree",{},function(result) { 
							var data=result['data']['children'];
							var str = JSON.stringify(data);
							str = str.replaceAll('name','title');
							var newData=eval('('+str+')');
							layui.use('tree', function(){
								 var tree = layui.tree;
								 tree.render({
								      elem: '#ztree',
								      data:newData,
								      showLine:false,
								      accordion:true,
								      click: function(obj){
								    	 $('#groupId').val('');
								    	 $("#deptId").val(obj.data.deptId);
								    	 $("#deptCode").val(obj.data.deptCode);
								    	 SelectUser.query();
								      }
								   }
								 );
							 });
							$(".layui-tree-main:eq(0)").click();
						});
					},
					selectGroup:function(groupId){
						$('#groupId').val(groupId);
						SelectUser.query();
					}
			};
			
			$(function(){
				$('#selectUserForm').render({success:function(){
					SelectUser.initTree();
					SelectUser.initTable();
					layui.element.on('tab(typeFilter)', function(){
						var val =  this.getAttribute('data-val');
						if(val=='often'){
							$('#selectUserForm #busiType').val('${param.busiType}');
						}else{
							$('#selectUserForm #busiType').val('');
						}
						SelectUser.query();
				    });
				}});
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>