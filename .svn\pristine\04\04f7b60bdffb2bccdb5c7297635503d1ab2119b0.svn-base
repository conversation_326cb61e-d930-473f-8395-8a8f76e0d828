<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<select style="display: none" id="shouSumSelect" name="skAmount" data-mars="ContractReceiptDao.shouSumSelect"></select>
<select style="display: none" id="kpSumSelect" name="kpAmount" data-mars="InvoiceDao.kpSumSelect"></select>
<SELECT style="display: none" name="paymentType" data-mars="ContractPaymentDao.getPaymentType"></SELECT>
<SELECT style="display: none" name="contractStage" data-mars="ContractStageDao.selectStage(${param.contractId})"></SELECT>
<SELECT style="display: none" name="paymentName" data-mars="ContractPaymentDao.selectPayment(${param.contractId})"></SELECT>
<SELECT style="display: none" name="incomeStageName" data-mars="IncomeStageDao.selectAllIncomeStage(${param.contractId})"></SELECT>

<table id="stageTable"></table>

<script  type="text/html" id="stageToolbar">
	<button class="btn btn-info btn-sm" onclick="contractStageList.add()" type="button">新增</button>
	<button class="btn btn-default btn-sm ml-15" onclick="reloadStageList()" type="button">刷新</button>
	<button class="btn btn-default btn-sm ml-15" onclick="contractStageList.edit()" type="button">修改</button>
	<button class="btn btn-default btn-sm ml-15" onclick="contractStageList.del()" type="button">删除</button>
</script>

<script type="text/javascript">

	var contractStageList = {
		init:function(){
			$("#ContractDetailForm").initTable({
				mars:'ContractStageDao.stageList',
				id:'stageTable',
				page:false,
				toolbar:'#stageToolbar',
				cols: [[
					{
						type: 'checkbox',
						align:'left'
					},{
						field: 'STAGE_NAME',
						title: '阶段名称',
						align:'left',
						minWidth:80
					},{
						field:'IS_RECEIVE',
						title:'是否应收',
						align:'left',
						minWidth:70
					},{
						field:'RCV_DATE',
						title:'应收日期',
						width:80
					},{
						field:'PLAN_COMP_DATE',
						title:'计划完成日期',
						width:80
					},{
						field:'ACT_COMP_DATE',
						title:'实际完成日期',
						sort:true,
						minWidth:80
					},{
						field:'RATIO',
						title:'比例(%)',
						width:50,
					},{
						field:'PLAN_RCV_AMT',
						title:'计划收款金额',
						sort:true,
						minWidth:100
					},{
						field:'KAIPIAO_AMT',
						title:'已开票金额(含税)',
						minWidth:100,
						//在开票信息里面统计
						templet:function(d){
							return getNumByStageId(d.STAGE_ID,"kpSumSelect");
						}
					},{
						title:'已收款金额(含税)',
						minWidth:100,
						//收款信息里面统计
						templet:function(d){
							return getNumByStageId(d.STAGE_ID,"shouSumSelect");
						}
					},{
						field:'YK_WB_COND',
						title:'余款维保条件',
						minWidth:100
					},{
						field:'YK_COND_DAYS',
						title:'余款条件天数',
						width:90
					},{
						field:'BAD_DEBT_AMT',
						title:'坏账金额',
						width:80
					},{
						field: 'UPDATE_TIME',
						title: '更新时间',
						width:120,
						sort:true,
						align:'center'
					}
				]],
				done:function(res){
				if(res.total!=undefined){
					$("#contractStageCount").text("("+res.total+")");
				}
			}
			});
		},
		add:function(){
			popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增阶段信息',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/stage-edit.jsp',title:'新增阶段信息',data:{amount:contractInfo.AMOUNT,contractId:contractId,isNew:'1'}});
		},
		del:function(){
			var checkStatus = table.checkStatus('stageTable');
			if(checkStatus.data.length>0){
				layer.confirm("确认要删除吗?",{icon:3,offset:'120px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/contractStage?action=BatchDel",checkStatus.data,function(result) {
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								layer.closeAll();
								reloadStageList();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			}else{
				layer.msg("请选择!");
			}
		},
		edit:function(){
			var checkStatus = table.checkStatus('stageTable');

			if(checkStatus.data.length > 1){
				layer.msg('一次只能更新一个阶段',{icon : 7, time : 1000});
			}
			else if(checkStatus.data.length == 1){
				var stageId = checkStatus.data[0]['STAGE_ID'];
				popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新阶段信息',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/stage-edit.jsp',data:{amount:contractInfo.AMOUNT,contractId:contractId,stageId:stageId,isNew:'0'}});
			}else{
				layer.msg("请选择!");
			}
		}
	}

	function getNumByStageId(id,selectId){
		var selectElement = $("#"+selectId);
		var optionsData = selectElement.find('option').map(function() {
			return {
				value: $(this).val(),
				text: $(this).text()
			};
		}).get();

		var Dict ={};
		if(optionsData.length == 0)
			return "0.00";

		optionsData.forEach(function(item) {
			Dict[item.value] = item.text;
		});

		if (id == "" || id == undefined){
			return "0.00";
		}
		if (Dict[id] == undefined){
			return "0.00";
		}
		var text = Dict && Dict[id];
		return text;
	}

	function reloadStageList(){
		$("#ContractDetailForm").queryData({id:'stageTable',page:false});
		$("#kpSumSelect").render();
		$("#shouSumSelect").render();
	}
</script>
