<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>合同评审</title>
	<style>
		.layui-table td, .layui-table th{font-size: 13px;}
		td span{font-size: 13px;}
		.ew-tree-table{margin-top: 0px!important;border: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form name="searchForm" autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
		<div class="ibox">
				<div class="ibox-content clearfix" >
					<div class="form-group">
             		      <div class="input-group input-group-sm">
							<span class="input-group-addon input-group-addon-sm">查询条件</span>	
							<input type="text" name="condition" id="condition" class="form-control input-sm" style="width:142px">
						 </div>
						 <div class="input-group input-group-sm ml-10">
								<button type="button" class="btn btn-sm btn-default" onclick="list.queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
					</div>
						 <div class="pull-right">
						 	<button type="button" class="layui-btn layui-btn-primary layui-btn-sm mr-10 refresh">刷新</button>
							<button type="button" class="layui-btn layui-btn-primary layui-btn-sm mr-10 open-all">全部展开</button>
							<button type="button" class="layui-btn layui-btn-primary layui-btn-sm mr-10 close-all">全部关闭</button>
						 </div>
				</div>
				<div class="ibox-content">
				    <table class="layui-table" id="tree-table"></table>
				</div>
			</div>
		</form>

</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
 		     var list= {};
 		     layui.config({
				base: '${ctxPath}/static/module/treeTable/',
			 })
 		     function loadTreeTable(data){
 		    	data.push({NODE_ID:'0',P_NODE_ID:'',NODE_NAME:'审批部门'});
				layui.use(['treeTable','form'],function(){
						var form = layui.form;
						var treeTable = layui.treeTable;
					    var re = treeTable.render({
							elem: '#tree-table',
							data: data,
							tree:{
								iconIndex: 1,
				                isPidData: true,
				                idName: 'NODE_ID',
				                pidName: 'P_NODE_ID',
				                openName:'NODE_NAME'
							},
							height: 'full-40',
							done: function(e){
								form.render();
							},
							cols: [
							    {type:'numbers'},
								{
									field: 'NODE_NAME',
									title: '名称'
								},
								{
									field: 'FIRST_CHECK_NAMES',
									title: '初审人'
								},
								{
									field: 'LAST_CHECK_NAMES',
									title: '终审人'
								},
								{
									field: 'NODE_STATE',
									title: '状态',
									templet:function(row){
										if(row['NODE_STATE']=='1'){
											return '<span class="label label-warning">暂停</span>';
										}else{
											return '';
										}
									}
								},
								{
									field:'',
									title:'操作',
									templet:'<div><a href="javascript:;" onclick="list.updateNode(\'{{d.NODE_ID}}\',\'{{d.P_NODE_ID}}\')">编辑</a><a href="javascript:;" class="hidden ml-5" onclick="list.delNode(\'{{d.NODE_ID}}\',\'{{d.P_NODE_ID}}\')">删除</a><a class="ml-5" href="javascript:;" onclick="list.addNode(\'{{d.NODE_ID}}\',\'{{d.P_NODE_ID}}\')">新增子集</a></div>'
								}
							]
					});
					$('.open-all').click(function(){
						re.expandAll();
					});
					$('.close-all').click(function(){
						re.foldAll();
					})
					$('.refresh').click(function(){
						location.reload();
					})
					    
				});
				
 		    	 
 		     }
 		    
 		function loadTree(){
 			list.load();
 		}  
 		
 		list.load = function(){
 			ajax.remoteCall("${ctxPath}/webcall?action=ReviewDao.nodeConfTree",form.getJSONObject("#searchForm"),function(result) { 
				if(result.state == 1){
					loadTreeTable(result['data']);
				}else{
						
				}
			});
 		}
 		
 		list.updateNode = function(nodeId,pNodeId){
 			if(nodeId=='0'){
 				return;
 			}
 			popup.layerShow({id:'editNode',area:['500px','350px'],url:'${ctxPath}/pages/crm/contract/review/node-edit.jsp',title:'编辑',data:{pNodeId:pNodeId,nodeId:nodeId}});
 		}
 		
 		list.delNode = function(nodeId,pNodeId){
 			if(nodeId=='0'){
 				return;
 			}
 			layer.confirm("确定删除?",{offset:'20px'},function(index){
			  	layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/contract/review?action=delNode",{nodeId:nodeId},function(result) { 
					if(result.state==1){
						layer.msg(result.msg,{time:800},function(){
							location.reload();
						});
					}else{
						layer.alert(result.msg);
					}
				 }
			  );
		  });
 		}
 		
 		list.addNode = function(nodeId,pNodeId){
 			popup.layerShow({id:'editNode',area:['500px','350px'],url:'${ctxPath}/pages/crm/contract/review/node-edit.jsp',title:'新增',data:{pNodeId:nodeId,nodeId:''}});
 		}
 		
		list.queryData = function(){
			list.load();
	    }
		$(function(){
			list.load();
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>