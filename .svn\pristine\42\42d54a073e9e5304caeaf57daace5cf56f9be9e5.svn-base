package com.yunqu.work.servlet.common;

import java.sql.SQLException;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.utils.ZendaoApi;


@Before(AuthInterceptor.class)
@Path(value = "/project")
public class ProjectController extends BaseController {

	public void index() {
		String projectId = getPara();
		if(StringUtils.isBlank(projectId)) {
			projectId = getPara("projectId");
		}
		Record record = Db.findFirst("select * from yq_project where project_id = ?", projectId);
		if(record==null) {
			renderHtml("url不正确");
			return;
		}
		Record team = Db.findFirst("select * from YQ_PROJECT_TEAM where USER_ID = ? and PROJECT_ID = ?",getUserId(),projectId);
		int projectPublic = record.getInt("project_public");
		if(projectPublic==1) {
			if(team==null) {
				renderHtml("您无权访问、请联系项目管理员");
				return;
			}
		}
		int isAdmin = 0;
		if(team!=null) {
			isAdmin = team.getInt("ADMIN_FLAG");
			try {
				this.getQuery().executeUpdate("update yq_project_team set ACCESS_TIME = ? where USER_ID = ? and PROJECT_ID = ?", EasyDate.getCurrentDateString(),getUserId(),projectId);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		if(hasRole("PROJECT_MANAGER")) {
			isAdmin = 1;
		}
		setAttr("isAdmin", isAdmin);
		setAttr("projectInfo", record);
		setAttr("projectId", projectId);
		render("/pages/project/portal/index.jsp?isDiv=0&projectId="+projectId);
	}
	
	public void bug() {
		String projectId = getPara();
		String id = Db.queryStr("select id from zentao.zt_project where code = ?",projectId);
		if(StringUtils.isBlank(id)) {
			try {
			    JSONObject row = this.getQuery().queryForRow("select * from yq_project where project_id= ?", new Object[] {projectId}, new JSONMapperImpl());
			    JSONObject result = ZendaoApi.addProject(row.getString("PROJECT_NAME"),id);
			    if(result==null||result.isEmpty()) {
			    	return;
			    }
			    id = result.getString("id");
			    EasyRecord record = new EasyRecord("zentao.zt_project","id");
			    record.set("id",id);
			    record.set("code", projectId);
			    record.set("type", "project");
			    record.set("model", "kanban");
			    record.set("acl", "open");
			    record.set("auth", "extend");
			    record.set("status","doing");
			    record.set("firstEnd","2059-12-31");
				record.set("begin", EasyDate.getCurrentDateString("yyyy-MM-dd"));
				record.set("realBegan", EasyDate.getCurrentDateString("yyyy-MM-dd"));
			    this.getQuery().update(record);
			    
			    this.getQuery().executeUpdate("update yq_project set zentao_id = ? where project_id = ?", id,projectId);
			    
			} catch (SQLException e) {
				this.getLogger().error(e.getMessage());
				renderHtml(e.getMessage());
				return;
			}
		}
		redirect("http://*************:6688/index.php?m=bug&f=browse&productID="+id);
	}
	
	public void zentao() {
		long timestamp = System.currentTimeMillis() / 1000;		 
		String token = MD5Util.getHexMD5("OAe36fae6627a6d97a77ce3c9b1b5868f2"+timestamp);
		token = token.toLowerCase();
		String account = getUserPrincipal().getLoginAcct();
		redirect("http://*************:6688/api.php?m=user&f=apilogin&id=2&code=OA&time="+timestamp+"&token="+token+"&account="+account);
	}
	
	
	public void projectConsole() {
		keepPara();
		String projectId = getPara();
		if(StringUtils.isBlank(projectId)) {
			renderHtml("页面链接错误");
			return;
		}
		try {
			JSONObject result = this.getQuery().queryForRow("select * from yq_project where project_id  = ?",new Object[] {projectId}, new JSONMapperImpl());
			if(result==null) {
				renderHtml("页面链接错误");
				return;
			}
			setAttr("projectInfo", result);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		render("/pages/project/project-console.jsp");
	}
	
	public void projectApprove() {
		String projectId = getPara();
		String contractId = Db.queryStr("select contract_id from yq_project where project_id = ?", projectId);
		String reviewId = Db.queryStr("select REVIEW_ID from YQ_PROJECT_CONTRACT where CONTRACT_ID = ?", contractId);
		if(StringUtils.isBlank(reviewId)) {
			renderHtml("没找到对应的评审流程");
			return;
		}
		String applyId = Db.queryStr("select apply_id from yq_flow_apply where apply_no = ?", reviewId);
		redirect("/web/flow/"+applyId);
	}
	
}
