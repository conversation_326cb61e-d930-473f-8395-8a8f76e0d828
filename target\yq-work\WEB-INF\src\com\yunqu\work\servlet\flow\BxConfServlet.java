package com.yunqu.work.servlet.flow;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.service.BxService;

@WebServlet("/servlet/bx/conf")
@MultipartConfig
public class BxConfServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForSavePeople() {
		JSONObject params = getJSONObject();
		String deptId = params.getString("deptId");
		EasyRecord record = new EasyRecord("yq_flow_bx_dept","dept_id");
		try {
			record.set("DEPT_ID",deptId);
			record.set("DEPT_STATE",0);
			record.set("COMPANY",params.getString("rootId"));
			record.set("DEPT_NAME",params.getString("deptName"));
			record.set("DEPT_CODE",params.getString("deptCode"));
			
			record.set("DEPT_MGR_ID", params.getString("DEPT_MGR_ID_"+deptId));
			record.set("ACCT_ID", params.getString("ACCT_ID_"+deptId));
			record.set("CENTER_ID", params.getString("CENTER_ID_"+deptId));
			record.set("CEO_ID", params.getString("CEO_ID_"+deptId));
			record.set("CFO_ID", params.getString("CFO_ID_"+deptId));
			record.set("ACCT_MGR_ID", params.getString("ACCT_MGR_ID_"+deptId));
			
			record.set("DEPT_MGR_NAME", params.getString("DEPT_MGR_NAME_"+deptId));
			record.set("ACCT_NAME", params.getString("ACCT_NAME_"+deptId));
			record.set("CENTER_NAME", params.getString("CENTER_NAME_"+deptId));
			record.set("CEO_NAME", params.getString("CEO_NAME_"+deptId));
			record.set("CFO_NAME", params.getString("CFO_NAME_"+deptId));
			record.set("ACCT_MGR_NAME", params.getString("ACCT_MGR_NAME_"+deptId));
			
			record.set("TEMP_NAME", params.getString("TEMP_NAME_"+deptId));
			record.set("FINANCE_DEPT_CODE", params.getString("FINANCE_DEPT_CODE_"+deptId));
			boolean bl = this.getQuery().update(record);
			if(!bl) {
				this.getQuery().save(record);
			}
			List<ApproveNodeModel> approvers = BxService.getService().getApprovers(deptId);
			JSONObject arpproverJSON = BxService.getService().toArpproverJSON(approvers);
			this.getQuery().executeUpdate("update yq_flow_bx_dept set approve_flow = ? where dept_id = ?", JSONObject.toJSONString(arpproverJSON),deptId);
	
			this.getQuery().executeUpdate("update yq_flow_bx_dept set dept_state = 1 where DEPT_ID not in(select DEPT_ID from "+Constants.DS_MAIN_NAME+".easi_dept)");
		
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForQueryUserBudget() {
		EasySQL sql = new EasySQL();
		sql.append("select * from yq_flow_bx_budget where 1=1");
		sql.append(getUserId(),"and user_id = ?");
		sql.appendIn(new String[] {String.valueOf(EasyCalendar.newInstance().getYear()),String.valueOf(EasyCalendar.newInstance().getYear()-1)},"and year_id");
		try {
		  List<JSONObject>	list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(), new JSONMapperImpl());
		  return EasyResult.ok(list);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForSaveBudget() {
		JSONObject params = getJSONObject();
		String budgetId = params.getString("budgetId");
		EasyRecord record = new EasyRecord("yq_flow_bx_budget","budget_id");
		try {
			record.set("YEAR_ID", params.getString("YEAR_ID_"+budgetId));
			record.set("USER_NAME", params.getString("USER_NAME_"+budgetId));
			record.set("USER_ID", params.getString("USER_ID_"+budgetId));
			record.set("DEPT_ID", params.getString("DEPT_ID_"+budgetId));
			record.set("DEPT_NAME", params.getString("DEPT_NAME_"+budgetId));
			record.set("FEE_TYPE", params.getString("FEE_TYPE_"+budgetId));
			for(int i=1;i<=12;i++) {
				record.set("MONTH_"+i, params.getFloatValue("MONTH_"+i+"_"+budgetId));
			}
			if(budgetId.length()>10) {
				record.set("budget_id",budgetId);
				record.set("update_time",EasyDate.getCurrentDateString());
				this.getQuery().update(record);
			}else {
				record.set("BUDGET_TYPE", params.getString("budgetType"));
				String id = RandomKit.uniqueStr();
				record.set("create_time",EasyDate.getCurrentDateString());
				record.set("create_name",getUserName());
				record.set("budget_id",id);
				this.getQuery().save(record);
				
//				this.getQuery().executeUpdate("update yq_flow_bx_budget t1,"+Constants.DS_MAIN_NAME+".easi_user t2 set t1.user_name = t2.username,t1.dept_name = t2.depts where t1.user_id = t2.user_id and t1.budget_id = ?", id);
//				this.getQuery().executeUpdate("update yq_flow_bx_budget t1,"+Constants.DS_MAIN_NAME+".easi_dept_user t2 set t1.dept_id = t2.dept_id where t1.user_id = t2.user_id and t1.budget_id = ?", id);
				
			}
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelBudget() {
		JSONObject params = getJSONObject();
		String budgetId = params.getString("budgetId");
		try {
				this.getQuery().executeUpdate("delete from yq_flow_bx_budget where budget_id = ?", budgetId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddBxDept() {
		JSONObject params = getJSONObject();
		String deptId = params.getString("deptId");
		try {
			EasyRecord record = new EasyRecord("yq_flow_bx_dept","dept_id");
			record.set("DEPT_ID",deptId);
			record.set("DEPT_STATE",0);
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelPeople() {
		try {
			this.getQuery().executeUpdate("update yq_flow_bx_dept set dept_state = 1 where dept_id = ?", getJsonPara("deptId"));
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
		
	}
	public EasyResult actionForGetUserBudget() {
		EasySQL sql = new EasySQL();
		sql.append("select * from yq_flow_bx_budget where 1=1");
		sql.append(getUserId(),"and user_id = ?");
		sql.append("order by year_id desc");
		sql.append("limit 10");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			return EasyResult.ok(list);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok(new JSONArray());
		
	}
	public EasyResult actionForCalcFee() {
		
		EasySQL sql = new EasySQL();
		sql.append("select * from yq_flow_bx_budget where 1=1");
		sql.append(getUserId(),"and user_id = ?");
		sql.appendIn(new String[] {String.valueOf(EasyCalendar.newInstance().getYear()),String.valueOf(EasyCalendar.newInstance().getYear()-1)},"and year_id");
		JSONObject json = new JSONObject();
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			for(JSONObject row:list) {
				String deptId = row.getString("DEPT_ID");
				for(int i=1;i<=12;i++) {
					String money = row.getString("MONTH_"+i);
					String monthId = i<10?("0"+i):(i+"");
					json.put(deptId+"_"+row.getString("YEAR_ID")+"-"+monthId+"_"+row.getString("FEE_TYPE"),money);
				}
			}
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
		}
		
		StringBuffer msg = new StringBuffer();
		
		JSONObject data = getJSONObject();
		String bxId = data.getString("bxId");
		JSONObject params = data.getJSONObject("data");
		
		Set<String> deptIds = params.keySet();
		for(String deptId:deptIds) {
			JSONObject monthParams = params.getJSONObject(deptId);
			Set<String> monthIds = monthParams.keySet();
			for(String monthId:monthIds) {
				JSONObject row = monthParams.getJSONObject(monthId);
				Set<String> feeInfo  = row.keySet();
				for(String feeType:feeInfo) {
					float editMoney = row.getFloatValue(feeType);
					try {
						EasySQL sql2 = new EasySQL("select sum(amount) from yq_flow_bx_item t1,yq_flow_apply t2 where t1.business_id = t2.apply_id");
						sql2.append(getUserId(),"and t2.apply_by = ?");
						sql2.append(deptId,"and t1.dept_id = ?");
						sql2.append(FlowConstants.FLOW_STAT_CANNEL,"and t2.apply_state <> ?");
						sql2.append(monthId,"and t1.budget_month = ?");
						sql2.appendRLike(feeType,"and t1.fee_in_type like ?");
						sql2.append(bxId,"and business_id <> ?");
						String _hasBxMoney = this.getQuery().queryForString(sql2.getSQL(), sql2.getParams());
						
						float hasBxMoney = StringUtils.isBlank(_hasBxMoney)?0:Float.valueOf(_hasBxMoney);
						
						//加起来金额 
						float lastMoney = editMoney + hasBxMoney;
						
						//预算金额
						String key  = deptId+"_"+monthId+"_"+feeType;
						if(!json.containsKey(key)) {
							continue;
						}
						float ysMoney = json.getFloatValue(key);
						if(lastMoney>ysMoney) {
//							msg.append(deptId);
							msg.append(monthId+feeType+",预算金额"+ysMoney);
							msg.append(",已申请"+hasBxMoney+",本次申请"+editMoney);
							msg.append(",超过预算"+(lastMoney-ysMoney));
							msg.append("<br>");
						}
					} catch (SQLException e) {
						this.error(null, e);
					}
				}
			}
			
		}
		
		
		if(msg.toString().length()>0) {
			return EasyResult.fail(msg.toString());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForGetSubject() {
		JSONObject params = getJSONObject();
		try {
			EasySQL sql = new EasySQL("select * from yq_flow_bx_subject where 1=1");
			sql.append(params.getString("feeCode"),"and fee_code = ?");
			sql.append(params.getString("deptType"),"and dept_type = ?");
			sql.appendIn(new String[] {"A",params.getString("state")},"and project_state");
			JSONObject row = this.getQuery().queryForRow(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(row==null) {
				return EasyResult.fail("无关联会计科目,请联系财务.");
			}
			return EasyResult.ok(row);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	public EasyResult actionForAllSubject() {
		try {
			EasySQL sql = new EasySQL("select * from yq_flow_bx_subject where 1=1");
			List<JSONObject> list = this.getQuery(3).queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			return EasyResult.ok(list);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForJudgeInvoice() {
		JSONObject params = getJSONObject();
		String invoiceNos = params.getString("invoiceNo");
		String itemId = params.getString("itemId");
		String flag = null;
		if(StringUtils.notBlank(invoiceNos)) {
			String[] invoiceNoArray = invoiceNos.split(",");
			for(String invoiceNo:invoiceNoArray) {
				EasySQL sql = new EasySQL("select count(1) from yq_flow_bx_item t1,yq_flow_apply t2  where 1=1 and t1.business_id = t2.apply_id");
				sql.append(FlowConstants.FLOW_STAT_CANNEL,"and t2.apply_state <> ?");
				sql.append(itemId,"and t1.item_id <> ?");
				sql.append(invoiceNo,  "and FIND_IN_SET(?,t1.invoice_no)");
				try {
					boolean exist = this.getQuery().queryForExist(sql.getSQL(), sql.getParams());
					if(exist) {
						flag = invoiceNo;
						break;
					}
				} catch (SQLException e) {
					this.error(null, e);
				}
			}
		}
		if(flag!=null) {
			return EasyResult.fail(flag);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForContactUnit() {
		JSONObject params = getJSONObject();
		String action = params.getString("action");
		JSONObject conf = JsonKit.getJSONObject(params, "conf");
		EasyRecord record = new EasyRecord("yq_finance_contact_unit","id");
		record.setColumns(conf);
		try {
			if("add".equals(action)) {
				String unitNo = this.getQuery().queryForString("select unit_no from yq_finance_contact_unit where unit_name = ?", record.getString("UNIT_NAME"));
				if(StringUtils.notBlank(unitNo)) {
					record.set("unit_no",unitNo);
				}else {
					String no = "1"+this.getQuery().queryForString("select max(SUBSTR(unit_no,3)) from yq_finance_contact_unit");
					int n = Integer.valueOf(no)+1;
					record.set("unit_no","WL"+String.valueOf(n).substring(1));
				}
				record.remove("ID");
				record.set("create_time",EasyDate.getCurrentDateString());
				record.set("creator",getUserId());
				record.set("create_name",getUserName());
				this.getQuery().save(record);
			}else if("update".equals(action)){
				this.getQuery().update(record);
			}else if("delete".equals(action)) {
				this.getQuery().deleteById(record);
			}
		  } catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}

	public EasyResult actionForUploadBxBudgetFile()  throws SQLException, IOException, ServletException {
		EasyResult result = new EasyResult();
		try {
			String budgetType = getPara("budgetType");
			Part part=this.getFile("bxData");
			Workbook workbook = WorkbookFactory.create(part.getInputStream());
		    List<List<String>> list = new ArrayList<>();
		    int num = workbook.getNumberOfSheets();
		    int lastCellNum=0;
		    for(int index =0 ;index < num ;index++){
		    	Sheet sheet = workbook.getSheetAt(index);
		    	int maxLine =sheet.getLastRowNum();
		    	for (int ii = 1; ii <= maxLine; ii++) {
		    		List<String> rows = new ArrayList<>();
		    		Row row = sheet.getRow(ii);
		    		lastCellNum=row.getLastCellNum();
		    		if(ii==1){
		    			if(lastCellNum!=7){
		    				return EasyResult.fail("标准模板字段7个，您上传的模板字段"+lastCellNum+"个，不匹配!");
		    			}
		    		}
		    		if(row!=null){
		    			for(int j=0;j<lastCellNum;j++){
		    				Cell cell=row.getCell(j);
		    				if(cell!=null){
		    					String val = Utils.getCellValue(cell);
		    					if(StringUtils.isBlank(val)){
		    						rows.add("");
		    					}else{
		    						rows.add(val);
		    					}
		    				}else{
		    					rows.add("");
		    				}
		    			}
		    			list.add(rows);
		    		}
		    	}
		    }
		    
		    String updateTime = EasyDate.getCurrentDateString();
			 for(int j=0;j<list.size();j++){
				List<String> strs=list.get(j);
				if(strs.size()>0){
					String userName = strs.get(0);
					String staffNo = strs.get(1);
					String deptName = strs.get(2);
					String yearId = strs.get(3);
					String monthId = strs.get(4);
					String feeType = strs.get(5);
					String money = strs.get(6);
					
					EasyRecord record = new EasyRecord("yq_flow_bx_budget","budget_type","year_id","dept_name","user_name","fee_type");
					record.set("budget_type", budgetType);
					record.set("user_name", userName);
					record.set("staff_no", staffNo);
					record.set("dept_name", deptName);
					record.set("year_id", yearId);
					record.set("update_time", updateTime);
					record.set("fee_type", feeType);
					record.set("month_"+monthId, money);
					boolean bl = this.getQuery().update(record);
					if(!bl) {
						record.set("create_name", getUserName());
						record.set("create_time", updateTime);
						record.set("budget_id",RandomKit.uuid());
						this.getQuery().save(record);
					}
			   }
		    }
			 
			this.getQuery().executeUpdate("update yq_flow_bx_budget t1,"+Constants.DS_MAIN_NAME+".yq_staff_info t2 set t1.user_id = t2.staff_user_id where t1.staff_no = t2.staff_no and t1.update_time = ?", updateTime); 
			this.getQuery().executeUpdate("update yq_flow_bx_budget t1,"+Constants.DS_MAIN_NAME+".easi_dept t2 set t1.dept_id = t2.dept_id where t1.dept_name = t2.dept_name and t1.update_time = ?", updateTime); 
			 
			result.put("state", 1);
			result.put("msg", "上传成功!");
			part.delete();
			return result;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.addFail(e.getMessage());
			return result;
		}finally {
			
		}
	}
	
	public void actionForExportBxBudget() {
		String _data = getPara("data");
		JSONObject param = JSONObject.parseObject(_data);
		List<String> headers=new ArrayList<String>();
		try {
			EasySQL sql = new EasySQL();
			sql.append("select * from yq_flow_bx_budget where 1=1");
			sql.append(param.getString("budgetType"),"and budget_type = ?");
			sql.append(param.getString("yearId"),"and year_id = ?");
			sql.appendLike(param.getString("userName"),"and user_name like ?");
			sql.appendLike(param.getString("deptName"),"and dept_name like ?");
			File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
			/**创建头部*/
			headers.add("姓名");
			headers.add("工号");
			headers.add("部门");
			headers.add("年份");
			headers.add("月份");
			headers.add("费用类型");
			headers.add("金额");
			
			List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
			for(String header:headers){
				ExcelHeaderStyle style=new ExcelHeaderStyle();
				style.setData(header);
				style.setWidth(4500);
				style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
				styles.add(style);
			}
			/**数据***/
			List<List<String>> excelData=new ArrayList<List<String>>();
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> data = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			
			if(data!=null && data.size()>0){
				for (int i = 0; i < data.size(); i++) {
					JSONObject map = data.get(i);
					for(int y=1;y<=12;y++) {
						List<String> list=new ArrayList<String>();
						list.add(map.getString("USER_NAME"));
						list.add(map.getString("STAFF_NO"));
						list.add(map.getString("DEPT_NAME"));
						list.add(map.getString("YEAR_ID"));
						list.add(y+"");
						list.add(map.getString("FEE_TYPE"));
						list.add(map.getString("MONTH_"+y));
						excelData.add(list);
					}
				}
			}
			
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			String fileName="费用预算.xlsx";
			renderFile(file,fileName,true);
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
	}
	
	
	
}
