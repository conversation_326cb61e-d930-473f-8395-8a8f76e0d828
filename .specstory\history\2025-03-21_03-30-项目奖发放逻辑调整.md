<!-- Generated by SpecStory -->

# 项目奖发放逻辑调整 (2025-03-21 11:30:24)

_**User**_

实际初验日期大于考核日期，应该默认不发项目奖，点选否

---

_**Assistant**_

```thinking
让我理解一下需求和现有代码:

1. 需求分析:
- 当实际初验日期大于考核日期时,应该默认选择"不发项目奖"
- 需要在页面加载时进行判断和设置
- 涉及到两个日期字段:
  * 实际初验日期: project.CY_REAL_DATE
  * 考核日期: project.KH_DATE

2. 代码现状:
- 已有项目奖选择的radio按钮
- 已有updateAwardReasonTitle()函数处理显示/隐藏原因输入框
- 页面加载时会调用render()获取数据

3. 实现思路:
- 需要在页面加载完成后,比较两个日期
- 如果实际初验日期 > 考核日期,则:
  * 设置radio "否" 为选中
  * 显示原因输入框
  * 更新原因标题为"不发放项目奖原因"

4. 修改方案:
- 在render回调函数中添加日期比较和设置逻辑
- 使用JavaScript Date对象进行日期比较
- 调用已有的updateAwardReasonTitle()函数更新UI

我需要编辑config.jsp文件来实现这个功能。
```

让我来修改代码以实现这个功能:

---

Tool call timed out after 200000ms

---

让我重试一下:

---



---

我注意到工具调用可能有问题。让我尝试一个更简洁的修改:

---

```diff
  				oldData = result['ProjectDao.record'].data;
  				loadLog();
- 				
- 				// 比较实际初验日期和考核日期
  				var cyRealDate = result['ProjectDao.record'].data.CY_REAL_DATE;
  				var khDate = result['ProjectDao.record'].data.KH_DATE;
- 				
- 				if(cyRealDate && khDate) {
- 					var cyRealDateTime = new Date(cyRealDate);
- 					var khDateTime = new Date(khDate);
- 					
- 					if(cyRealDateTime > khDateTime) {
- 						// 设置不发项目奖
+ 				if(cyRealDate && khDate && new Date(cyRealDate) > new Date(khDate)) {
  					$("input[name='project.IS_PROJECT_AWARD'][value='0']").prop("checked", true);
  					updateAwardReasonTitle(1);
  				}
- 				}
- 				
- 				updateAwardReasonTitle(0); // 初始化标题
+ 				updateAwardReasonTitle(0);
  			}});  
  		});
```

---

很好,我已经成功修改了代码。让我解释一下这个修改的功能:

1. 在页面加载完成后,会执行以下逻辑:
   - 获取实际初验日期(CY_REAL_DATE)和考核日期(KH_DATE)
   - 如果两个日期都存在,则进行比较
   - 如果实际初验日期大于考核日期:
     * 自动选中"否"选项(不发项目奖)
     * 调用updateAwardReasonTitle(1)显示原因输入框
   - 最后调用updateAwardReasonTitle(0)进行初始化

2. 代码优化:
   - 使用了更简洁的条件判断
   - 直接使用Date对象进行日期比较
   - 保留了原有的UI更新逻辑

这样,当实际初验日期超过考核日期时,系统会自动:
1. 选择"不发项目奖"选项
2. 显示原因输入框
3. 将标题更新为"不发放项目奖原因"

用户仍然可以手动更改选择,但默认会设置为不发放项目奖。这样的实现符合业务需求,并保持了良好的用户体验。

---

_**User**_

是输入实际初验日期的时候 onchange事件做判断

---

_**Assistant**_

```thinking
让我理解一下需求和实现方案:

1. 需求分析:
- 在输入实际初验日期时,需要立即判断与考核日期的关系
- 如果实际初验日期大于考核日期,就自动设置为不发项目奖
- 需要监听实际初验日期输入框的onchange事件

2. 代码现状:
- 实际初验日期输入框:
  * name="project.CY_REAL_DATE"
  * 使用WdatePicker日期选择器
  * 目前没有onchange事件处理
- 已有updateAwardReasonTitle()函数处理项目奖UI更新

3. 实现思路:
- 添加onchange事件处理函数
- 在事件处理中比较日期
- 根据比较结果设置项目奖选项
- 调用已有的updateAwardReasonTitle()更新UI

4. 需要注意:
- 日期格式的处理
- 空值的处理
- 确保UI状态的正确更新

我需要修改config.jsp文件来实现这个功能。
```

让我来修改代码:

---

```diff
  					                    <td>实际初验日期</td>
  					                    <td>
- 						                    <input type="text" readonly="readonly" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" placeholder="盖章确认的初验日期"  name="project.CY_REAL_DATE"  class="form-control input-sm Wdate">
+ 						                    <input type="text" readonly="readonly" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){checkCyRealDate(this);}})" placeholder="盖章确认的初验日期"  name="project.CY_REAL_DATE"  class="form-control input-sm Wdate">
  					                    </td>
  					                    <td>实际终验日期</td>
  			}
  		}
+ 		
+ 		// 检查实际初验日期与考核日期的关系
+ 		function checkCyRealDate(el) {
+ 			var cyRealDate = $(el).val();
+ 			var khDate = $("[name='project.KH_DATE']").val();
+ 			
+ 			if(cyRealDate && khDate) {
+ 				var cyRealDateTime = new Date(cyRealDate);
+ 				var khDateTime = new Date(khDate);
+ 				
+ 				if(cyRealDateTime > khDateTime) {
+ 					// 设置不发项目奖
+ 					$("input[name='project.IS_PROJECT_AWARD'][value='0']").prop("checked", true);
+ 					updateAwardReasonTitle(1);
+ 				}
+ 			}
+ 		}
  </script>
  </EasyTag:override>
```

---

我已经成功修改了代码。让我解释一下实现的功能:

1. 修改了实际初验日期输入框的配置:
```javascript
onClick="WdatePicker({
    dateFmt:'yyyy-MM-dd',
    onpicked:function(){checkCyRealDate(this);}
})"
```
- 添加了onpicked回调函数
- 当用户选择日期后会触发checkCyRealDate函数

2. 添加了新的checkCyRealDate函数:
```javascript
function checkCyRealDate(el) {
    var cyRealDate = $(el).val();
    var khDate = $("[name='project.KH_DATE']").val();
    
    if(cyRealDate && khDate) {
        var cyRealDateTime = new Date(cyRealDate);
        var khDateTime = new Date(khDate);
        
        if(cyRealDateTime > khDateTime) {
            // 设置不发项目奖
            $("input[name='project.IS_PROJECT_AWARD'][value='0']").prop("checked", true);
            updateAwardReasonTitle(1);
        }
    }
}
```

功能说明:
1. 当用户选择实际初验日期时,会立即触发检查
2. 获取实际初验日期和考核日期
3. 如果两个日期都存在,则进行比较
4. 如果实际初验日期大于考核日期:
   - 自动选中"否"选项(不发项目奖)
   - 调用updateAwardReasonTitle(1)显示原因输入框
   - 更新标题为"不发放项目奖原因"

这样实现了实时检查和自动设置的功能,提供了更好的用户体验。用户在选择日期后可以立即看到效果,而不需要等到保存时才发现问题。

---

_**User**_

修改日志logInfo，需要补充记录IS_PROJECT_AWARD，PROJECT_AWARD_REASON

---

_**Assistant**_

```thinking
让我分析一下需求和修改方案：

1. 当前logInfo记录情况：
- 在Project.updateData()函数中记录字段变化
- 使用fieldNameMap定义字段中文名映射
- 目前没有记录项目奖相关字段的变化

2. 需要添加的字段：
- IS_PROJECT_AWARD: 是否发项目奖
- PROJECT_AWARD_REASON: 项目奖原因

3. 修改思路：
- 在fieldNameMap中添加这两个字段的中文映射
- 对于IS_PROJECT_AWARD需要特殊处理,将1/0转换为"是"/"否"
- PROJECT_AWARD_REASON作为普通文本字段记录变化

4. 具体实现：
- 添加字段映射
- 添加值映射处理
- 确保这两个字段的变化被记录到logInfo中
```

让我来修改代码：

---

Cancelled

---



---



---

