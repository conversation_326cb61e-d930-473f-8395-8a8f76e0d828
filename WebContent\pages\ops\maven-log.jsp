<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>主机管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">appId</span>	
							 <input type="text" name="appId" class="form-control input-sm" style="width: 90px;">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">computer</span>	
							 <input type="text" name="computer" class="form-control input-sm" style="width: 90px;">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">pushFlag</span>	
							 <input type="text" name="pushFlag" class="form-control input-sm" style="width: 90px;">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">consoleUrl</span>	
							 <input type="text" name="consoleUrl" class="form-control input-sm" style="width: 90px;">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		
	    $(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
	    
		var list = {
			init:function(){
				$("#searchForm").initTable({
					mars:'ProjectDataDao.mavenLog',
					cellMinWidth:120,
					limit:30,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },
				    { field: 'LOG_ID', title: 'LOG_ID', align: 'left' },
				    { field: 'APP_ID', title: 'APP_ID', align: 'left' },
				    { field: 'APP_NAME', title: 'APP_NAME', align: 'left' },
				    { field: 'PROJECT_NAME', title: 'PROJECT_NAME', align: 'left' },
				    { field: 'JAVA_HOME', title: 'JAVA_HOME', align: 'left' },
				    { field: 'JAVA_VERSION', title: 'JAVA_VERSION', align: 'left' },
				    { field: 'WAR_PATH', title: 'WAR_PATH', align: 'left' },
				    { field: 'APP_VERSION', title: 'APP_VERSION', align: 'left' },
				    { field: 'POM_VERSION', title: 'POM_VERSION', align: 'left' },
				    { field: 'PACKAGING', title: 'PACKAGING', align: 'left' },
				    { field: 'BUILD_DATE', title: 'BUILD_DATE', align: 'left',width:140 },
				    { field: 'WAR_SIZE', title: 'WAR_SIZE', align: 'left' },
				    { field: 'PUSH_FLAG', title: 'PUSH_FLAG', align: 'left' },
				    { field: 'CLEAN_LIB_DIRECTORY', title: 'CLEAN_LIB_DIRECTORY', align: 'left' },
				    { field: 'PARENT_INFO', title: 'PARENT_INFO', align: 'left' },
				    { field: 'PROJECT_INFO', title: 'PROJECT_INFO', align: 'left' },
				    { field: 'FINAL_NAME', title: 'FINAL_NAME', align: 'left' },
				    { field: 'COMPUTER', title: 'COMPUTER', align: 'left' },
				    { field: 'JAR_PATTERNS', title: 'JAR_PATTERNS', align: 'left' },
				    { field: 'CONSOLE_URL', title: 'CONSOLE_URL', align: 'left' }
				 ]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>