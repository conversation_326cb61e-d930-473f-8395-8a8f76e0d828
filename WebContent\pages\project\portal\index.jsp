<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<html>
<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/x-icon" href="${ctxPath}/static/images/project.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${projectName}</title>
	<link rel="stylesheet" href="/yq-work/pages/project/portal/libs/element-ui/element-ui.css">
    <link rel="stylesheet" href="/yq-work/pages/project/portal/css/main.css">
</head>

<body onload="init()">
	<div id="portalBox" v-cloak>
	    <div id="yc-common-sidebar-wrapper">
	        <div data-stick="true" class="yc-common-sidebar-wrap type-lite ">
	            <div class="yc-common-sidebar type-lite level-1" data-mini="false" data-stick="true">
	                <div class="yc-common-sidebar-main" style="margin-top: 0px;">
	                    <div class="yc-common-sidebar-main-scroll">
	                    	<!-- 菜单 -->
	                    	<template v-for="item,index in menu">
		                    	<a v-if="!item.admin || (item.admin&&isAdmin=='1')" @click.stop="openPage(item)" :data-href="item.url || 'javascript:;'" class="nav-item-a">
		                            <div  class="yc-common-sidebar-main-item" :data-active="currentPage == item.url">
		                                <div class="yc-common-sidebar-main-item-icon "><i :class="item.icon || 'el-icon-menu'" class="project-icon project-medium"></i></div>
		                                <div class="yc-common-sidebar-main-item-name" >{{item.title}}</div>
		                            </div>
		                        </a>
	                    	</template>
	                    </div>
	                </div>
					<c:if test="${isAdmin=='1'}">
	                  <div class="yc-common-sidebar-extra">
	                	<a href="javascript:;" @click.stop="openPage({title:'设置',url:'/yq-work/pages/project/module/config.jsp?projectId=${projectId}'})" class="yc-common-sidebar-extra-item active yc-common-sidebar-extra-item-is-last" data-active="true">
	                        <div class="yc-common-sidebar-extra-item-icon">
	                        	<button id="setting-for-lead" type="button" class="next-btn next-small next-btn-normal next-btn-text isOnlyIcon isOnlyIcon  is-yunxiao" style="margin-top: 10px;border: 0;font-size: 20px;"><i class="project-icon project-medium el-icon-setting"></i></button>
                        	</div>
	                    </a>
	                    <div class="fold-icon"></div>
	                  </div>
					</c:if>
	            </div>
	        </div>
	        <div class="next-css-var-ref-element" style="width: 0px; height: 0px; display: none;"></div>
	    </div>

	    <div id="yc-navigation-container">
	        <header class="yc-navigator-container yc-navigation-navbar yc-navigation-fixed-top yc-navigation-appMode-dock" style="color: rgb(128, 128, 128);">
	            <div class="yc-navigator-nav-inner row-flex" style="background-color: rgb(255, 255, 255);">
	                <div class="system-bar" >
	                    <div class="system-bar-left" >
	                        <div id="yc-navigation-dock">
	                            <div>
	                                <div>
	                                	<span class="next-badge">
	                                        <div class="yc-navigation-icon-wraper" id="yc-navigation-icon-wraper"><i class="yc-navigation-project-icon yc-navigation-project-medium el-icon-s-grid"></i>
	                                        </div>
	                                    </span>
	                                </div>
	                                <div class="yc-navigation-overlay-wrapper yc-navigation-overlay">
	                                    <div class="left-shell yc-navigation-overlay-inner left-shell-not-visible" style="transform: translate(-100%);">
	                                        <div id="Dock_CONTAINER"></div>
	                                    </div>
	                                </div>
	                            </div>
	                        </div>
	                        <div class="yc-navigation-contextSwitcher" id="yc-navigation-contextSwitcher-container">
	                            <div class="yc-navigation-logo-wrap">
	                                <div class="yc-navigation-breadcrumb">
	                                	   <img class="yc-navigation-breadcrumb-home-logo" src="/yq-work/pages/project/portal/images/logo.png" >
	                                	   <a class="yc-navigation-breadcrumb-home yc-navigation-title" href="#">${projectName}</a>
	                                		<template v-if="currentPageTitle">
		                                		<i class="yc-navigation-project-icon el-icon-arrow-right"></i>
			                                    <div id="yc-navigation-contextSwitcher-first">
			                                        <div class="dropdown-trigger">
			                                            <div class="yc-navigation-contextSwitcher-element" style="color: rgb(128, 128, 128); max-width: 206px;">
			                                                <div class="yc-navigation-contextSwitcher-element-container" style="max-width: 198px;">
			                                                    <div class="yc-navigation-contextSwitcher-element-title" style="max-width: 178px;">
			                                                    	<div class="project-title">{{currentPageTitle}}</div>
			                                                    </div>
			                                                    <i class="yc-navigation-project-icon yc-navigation-project-medium "></i>
			                                                </div>
			                                            </div>
			                                        </div>
			                                    </div>
	                                		</template>
		                                    <i class="yc-navigation-project-icon yc-navigation-project-medium yc-navigation-breadcrumb-onelevel-icon"></i>
		                                    <div id="yc-navigation-contextSwitcher-second"></div>
		                                    <i class="yc-navigation-project-icon yc-navigation-project-medium yc-navigation-breadcrumb-twolevel-icon"></i>
		                                    <div id="yc-navigation-contextSwitcher-third"></div>
	                                </div>
	                            </div>
	                        </div>
	                        <div id="yc-navigation-customAction">
	                            <div>
	                                <div>
	                                	<i class="project-icon project-icon-star-fill project-medium"></i>
	                                </div>
	                            </div>
	                        </div>
	                    </div>
	                    <div class="system-bar-middle"></div>
	                    <div class="system-bar-right">
	                    	<el-dropdown  trigger="click">
							   <div id="yc-navigation-createEntrance-container" class="active-custom-entrance">
		                        	<el-tooltip class="item" effect="dark" content="创建" placement="left">
								      <div class="yc-navigation-create-entrance-container">
			                                <div>
			                                    <div data-active="false" class="yc-navigation-create-entrance">
			                                        <div class="yc-navigation-create-svg" style="background: var(--color-brand1-6, #1b9aee);"></div>
			                                    </div>
			                                </div>
			                            </div>
								    </el-tooltip>
		                      </div>
							  <el-dropdown-menu slot="dropdown">
							    <el-dropdown-item @click.stop="openPage(item)" v-for="item,index in addList">{{item.title}}</el-dropdown-item>
							  </el-dropdown-menu>
							</el-dropdown>
							<template>
  							   <div>
								<div class="project-star" v-if="!favorite" @click="favoriteAction()"><i class="el-icon-star-off"></i> 收藏</div>
	                    		<div class="project-star" v-else @click="favoriteAction()"><i class="el-icon-star-on"></i>取消收藏</div>
	                   		   </div>
							</template>
	                    </div>
	                </div>
	            </div>
	        </header>
	    </div>
	    <div id="container">
	        <main class="project-layout-layout no-padding full-page">
	        	<iframe :src="currentPage" frameborder="0" style="width:100%;height: 100%;"></iframe>
	        </main>
	    </div>
	</div>

    <script src="/easitline-static/js/jquery.min.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
    <script src="/yq-work/pages/project/portal/libs/vue.min.js"></script>
    <script src="/yq-work/pages/project/portal/libs/element-ui/element-ui.js"></script>
    <script>
    	function init(){
			window.pageVue = new Vue({
				el:'#portalBox',
				data:function(){
					return{
						currentPage:'${ctxPath}/project/projectConsole?projectId=${projectId}',
						currentPageTitle:'首页',
						indexPage:'',
						favorite:false,
						isAdmin:'${isAdmin}',
						menu:[
							{title:'概述',icon:'el-icon-s-marketing',url:'${ctxPath}/project/projectConsole?projectId=${projectId}'},
							{title:'任务',icon:'el-icon-s-opportunity',url:'${ctxPath}/pages/task/task-query.jsp?projectId=${projectId}&projectName=${projectName}'},
							{title:'文档',icon:'el-icon-folder',url:'/yq-work/pages/project/module/docs.jsp?projectId=${projectId}&projectName=${projectName}'},
							{title:'禅道',icon:'el-icon-s-release',url:'/yq-work/project/goZt?url=/yq-work/project/bug/${projectId}'},
							{title:'计划',icon:'el-icon-s-flag',url:'/yq-work/pages/project/plan/plan-list.jsp?projectId=${projectId}&projectName=${projectName}'},
							{title:'反馈',icon:'el-icon-bell',url:'/yq-work/pages/project/module/action.jsp?projectId=${projectId}&projectName=${projectName}'},
							{title:'风险',icon:'el-icon-warning',url:'/yq-work/pages/project/risk/risk-list.jsp?projectId=${projectId}&projectName=${projectName}'},
							{title:'周报',icon:'el-icon-position',url:'/yq-work/pages/project/module/weekly.jsp?projectId=${projectId}'},
							{title:'延期',icon:'el-icon-s-opportunity',url:'/yq-work/pages/task/task-delay-query.jsp?projectId=${projectId}'},
							{title:'需求',icon:'el-icon-receiving',url:'/yq-work/pages/flow/my/flow-query.jsp?flowCode=ts_rd&q=DATA1&v=${projectId}'},
							{title:'工时',icon:'el-icon-alarm-clock',url:'/yq-work/pages/project/workhour/project-workhour.jsp?projectId=${projectId}'},
							{title:'版本',icon:'el-icon-paperclip',url:'${ctxPath}/pages/ops/version-list.jsp?projectId=${projectId}'},
							{title:'评审',admin:true,icon:'el-icon-s-check',url:'/yq-work/project/projectApprove/${projectId}'},
							{title:'模块',icon:'el-icon-files',url:'/yq-work/pages/project/module/modules.jsp?projectId=${projectId}'},
							{title:'统计',icon:'el-icon-data-line',url:'/yq-work/pages/task/dept/person-project.jsp?projectId=${projectId}&isAll=1'},
							{title:'成员',icon:'el-icon-user',url:'/yq-work/pages/project/module/team.jsp?projectId=${projectId}'},
							{title:'看板',icon:'el-icon-data-analysis',url:'/yq-work/pages/kanban/kanban-index.jsp?projectId=${projectId}'},
						],
						addList:[
							{title:'新建项目',url:'#'},
							{title:'新建需求',url:'#'},
							{title:'新建任务',url:'#'},
							{title:'新建缺陷',url:'#'},
						]
					}
				},
				mounted:function(){
					var isAdmin = '${isAdmin}';
					var isProjectMgr = '${isProjectMgr}';
					top.isAdmin = isAdmin;
					top.isProjectMgr = isProjectMgr;
					this.loadStart();
					var hashValue = window.location.hash;
					if(hashValue&&hashValue!='设置'){
						hashValue = hashValue.substring(1);
						hashValue = decodeURIComponent(hashValue);
						
						var data = this.menu;
						var targetItem = null;
						for (var i = 0; i < data.length; i++) {
						  if (data[i].title === hashValue) {
						    targetItem = data[i];
						    break;
						  }
						}
						if(targetItem){
							this.openPage(targetItem);
						}
					}
				},
				methods:{
					openIndex:function(){//打开首页
						this.currentPage = indexPage;
						this.currentPageTitle = ''
					},
					openPage:function(item){//打开页面
						if(item.target=='blank'){
							window.open(item.url);
							return;
						}
						this.currentPage = item.url;
						this.currentPageTitle = item.title;
						window.location.hash = '#'+item.title;
					},
					loadStart:function(){
						var self = this;
						$.get('/yq-work/servlet/project/conf?action=favoriteState&id=${projectId}',function(result){
							self.favorite = result.data>0;
						});
					},
					favoriteAction:function(){
						var self = this;
						$.get('/yq-work/servlet/project/conf?action=updateFavoriteState',{projectId:'${projectId}',favoriteFlag:self.favorite?1:0},function(result){
							self.favorite = !self.favorite;
						});
					}
				}

			});
		}
    </script>
</body>

</html>