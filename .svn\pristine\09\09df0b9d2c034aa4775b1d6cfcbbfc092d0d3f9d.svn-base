package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ApproveNodeModel;

public class BxService extends AppBaseService {
	
	private static class Holder{
		private static BxService service=new BxService();
	}
	public static BxService getService(){
		return Holder.service;
	}
	
	public String getYqBxNo() {
		try {
			String prefix = "14EXP"+EasyCalendar.newInstance().getYear();
			String no = this.getQuery().queryForString("select max(apply_no) from yq_flow_apply where apply_no like '"+prefix+"%'");
			String num = prefix+"000001";
			if(StringUtils.isNotBlank(no)) {
				String str = String.valueOf(Integer.valueOf(no.substring(5))+1);
				num = "14EXP"+  str;
			}
			return num;
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
		return "0";
	}
	
	public String getZrBxNo() {
		try {
			String prefix = "13EXP"+EasyCalendar.newInstance().getYear();
			String no = this.getQuery().queryForString("select max(apply_no) from yq_flow_apply where apply_no like '"+prefix+"%'");
			String num = prefix+"000001";
			if(StringUtils.isNotBlank(no)) {
				num = "13EXP" + String.valueOf(Integer.valueOf(no.substring(5))+1);
			}
			return num;
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
		return "0";
	}
	
	public JSONObject userFeeDudget(String userId,String feeType,String yearId) {
		EasySQL sql = new EasySQL("select * from yq_flow_bx_budget where 1=1");
		sql.append(userId,"and user_id = ?");
		sql.append(feeType,"and fee_type = ?");
		sql.append(yearId,"and year_id = ?");
		try {
			JSONObject row =  this.getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			if(row==null) {
				return new JSONObject();
			}else {
				return row;
			}
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
		return new JSONObject();
	}
	
	
	public String getFeeByMonth(String userId,String feeType,String monthId) {
		JSONObject row = userFeeDudget(userId,feeType,monthId.substring(0,4));
	    int m = Integer.valueOf(monthId.substring(5));
	    return row.getString("MONTH_"+m);
	}
	
	public void queryNowBxMoney(String feeType) {
		EasySQL sql = new EasySQL("select sum(amount) from yq_flow_bx_item where 1=1");
		sql.append("and user_id = ?");
		sql.appendRLike(feeType,"and fee_type like ?");
		sql.append("2022-02","and bx_date = ?");
	}
	
	
	public ApproveNodeModel getNodeByResultId(String resultId) {
		ApproveNodeModel model = new ApproveNodeModel();
		try {
			JSONObject row = this.getQuery().queryForRow("select t1.node_id,t2.dept_id from yq_flow_approve_result t1,yq_flow_apply t2 where t1.business_id = t2.apply_id and t1.result_id =  ?", new Object[] {resultId},new JSONMapperImpl());
			String nodeId  = row.getString("NODE_ID");
			String deptId  = row.getString("DEPT_ID");
			
			JSONObject bxApprover = this.getBxApprover(deptId);
			String approveFlowStr = bxApprover.getString("approveFlow");
			JSONObject approveFlow = JSONObject.parseObject(approveFlowStr);
			JSONObject nodeInfo = approveFlow.getJSONObject(nodeId);
			
			model.setNodeId(nodeId);
			model.setNextNodeId(nodeInfo.getString("nextNodeId"));
			model.setPrevNodeId(nodeInfo.getString("prevNodeId"));
			model.setCheckName(nodeInfo.getString("checkName"));
			model.setCheckBy(nodeInfo.getString("checkBy"));
			model.setNodeName(nodeInfo.getString("nodeName"));
		} catch (SQLException e) {
			this.getLogger().error(null, e);
		}
		return model;
	}
	
	public ApproveNodeModel getNodeInfo(String nodeId,String deptId,JSONObject params) {
		ApproveNodeModel model = new ApproveNodeModel();
		JSONObject bxApprover = this.getBxApprover(deptId);
		String approveFlowStr = bxApprover.getString("approveFlow");
		JSONObject approveFlow = JSONObject.parseObject(approveFlowStr);
		JSONObject nodeInfo = approveFlow.getJSONObject(nodeId);
		
		model.setNodeId(nodeId);
		model.setNextNodeId(nodeInfo.getString("nextNodeId"));
		model.setPrevNodeId(nodeInfo.getString("prevNodeId"));
		model.setCheckName(nodeInfo.getString("checkName"));
		model.setCheckBy(nodeInfo.getString("checkBy"));
		model.setNodeName(nodeInfo.getString("nodeName"));
		return model;
	}
	
	public String getUserName(String userId){
		try {
			EasySQL sql = new EasySQL();
			sql.append("select GROUP_CONCAT(USERNAME) from "+Constants.DS_MAIN_NAME+".EASI_USER WHERE 1=1");
			sql.appendIn(userId.split(","), "and USER_ID");
			return this.getQuery().queryForString(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null,e);
			return "";
		}
	}
	
	public List<ApproveNodeModel> getApproversByUser(String userId,String deptId) {
		List<ApproveNodeModel> approvers = getApprovers(getBxApprover(deptId));
		List<ApproveNodeModel> list = new ArrayList<ApproveNodeModel>();
		for(ApproveNodeModel model:approvers) {
			String checkBy = model.getCheckBy();
			if(!userId.equals(checkBy)) {
				list.add(model);
			}
		}
		return list;
	}
	
	
	public JSONObject getBxApprover(String deptId) {
		String sql  = "select * from yq_flow_bx_dept where dept_id = ?";
		try {
			 JSONObject row = this.getQuery(3).queryForRow(sql, new Object[] {deptId}, new JSONMapperImpl());
			 if(row!=null) {
				 return row;
			 }else {
				 return new JSONObject();
			 }
		} catch (SQLException e) {
			getLogger().error(null, e);
			return new JSONObject();
		}
	}
	
	public String getApproverUserIds(String deptId) {
		return getApproverUser(deptId)[0];
	}
	
	public String[] getApproverUser(String deptId) {
		return getApproverUser(getBxApprover(deptId));
	}
	
	public String[] getApproverUser(JSONObject object) {
		String[] strs = null;
		String deptMgrId = object.getString("deptMgrId");//部门主管
		String acctId = object.getString("acctId");//会计审批
		String centerId = object.getString("centerId");//中心副总
		String ceoId = object.getString("ceoId");//中心副总
		String cfoId = object.getString("cfoId");//财务总监
		String acctMgrId = object.getString("acctMgrId");//会计主管
		StringBuffer userIds = new StringBuffer();
		if(StringUtils.isNotBlank(deptMgrId)) {
			userIds.append(deptMgrId+",");
		}
		if(StringUtils.isNotBlank(acctId)) {
			userIds.append(acctId+",");
		}
		if(StringUtils.isNotBlank(centerId)) {
			userIds.append(centerId+",");
		}
		if(StringUtils.isNotBlank(ceoId)) {
			userIds.append(ceoId+",");
		}
		if(StringUtils.isNotBlank(cfoId)) {
			userIds.append(cfoId+",");
		}
		if(StringUtils.isNotBlank(acctMgrId)) {
			userIds.append(acctMgrId+",");
		}
		if(StringUtils.notBlank(userIds.toString())) {
			String id  = userIds.substring(0,userIds.length() -1);
			strs = new String[]{id,getUserName(id)};
		}
		return strs;
	}
	
	
	public List<ApproveNodeModel> getApprovers(String deptId) {
		return getApprovers(getBxApprover(deptId));
	}
	
	public JSONObject toArpproverJSON(List<ApproveNodeModel> list) {
		JSONObject json = new JSONObject(true);
		for(ApproveNodeModel model:list) {
			json.put(model.getNodeId(),model);
		}
		return json;
	}
	
	private List<ApproveNodeModel> getApprovers(JSONObject object) {
		String deptMgrId = object.getString("deptMgrId");//部门主管
		String acctId = object.getString("acctId");//会计审批
		String centerId = object.getString("centerId");//中心副总
		String ceoId = object.getString("ceoId");//中心副总
		String cfoId = object.getString("cfoId");//财务总监
		String acctMgrId = object.getString("acctMgrId");//会计主管
		
		List<ApproveNodeModel> list = new ArrayList<ApproveNodeModel>();
		if(StringUtils.isNotBlank(deptMgrId)) {
			list.add(new ApproveNodeModel("deptMgr","部门主管",deptMgrId,getUserName(deptMgrId)));
		}
		if(StringUtils.isNotBlank(acctId)) {
			list.add(new ApproveNodeModel("acct","费用会计",acctId,getUserName(acctId)));
		}
		if(StringUtils.isNotBlank(centerId)) {
			list.add(new ApproveNodeModel("center","中心副总",centerId,getUserName(centerId)));
		}
		if(StringUtils.isNotBlank(ceoId)) {
			list.add(new ApproveNodeModel("ceo","总经理",ceoId,getUserName(ceoId)));
		}
		if(StringUtils.isNotBlank(cfoId)) {
			list.add(new ApproveNodeModel("cfo","财务总监",cfoId,getUserName(cfoId)));
		}
		if(StringUtils.isNotBlank(acctMgrId)) {
			list.add(new ApproveNodeModel("acctMgr","会计主管",acctMgrId,getUserName(acctMgrId)));
		}
		if(list.size()<2) {
			return list;
		}
		for(int i=0,len=list.size();i<len;i++) {
			if(i==0) {
				ApproveNodeModel nextModel = list.get(i+1);
				ApproveNodeModel model = list.get(i);
				model.setPrevNodeId("0");
				model.setNextNodeId(nextModel.getNodeId());
			}else if(i==(len-1)) {
				ApproveNodeModel prevModel = list.get(i-1);
				ApproveNodeModel model = list.get(i);
				model.setPrevNodeId(prevModel.getNodeId());
				model.setNextNodeId("0");
			}else {
				ApproveNodeModel prevModel = list.get(i-1);
				ApproveNodeModel nextModel = list.get(i+1);
				ApproveNodeModel model = list.get(i);
				model.setPrevNodeId(prevModel.getNodeId());
				model.setNextNodeId(nextModel.getNodeId());
			}
		}
		return list;
	}
	
}
