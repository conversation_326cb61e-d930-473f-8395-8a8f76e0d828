<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
 <div class="pull-left mb-5">
   	 <button type="button" class="btn btn-sm btn-info" onclick="$('#custLocalfile').click()">+ 上传附件</button>
   	 <button type="button" class="btn btn-sm ml-10 btn-default" onclick="openDownloadLog()">下载日志</button>
 </div>
  			
  <table id="fileList" class="layui-table"></table>
  
  <script>
	
	function loadFileList(){
		$("#custDetailForm").initTable({
			mars:'FileDao.fileListDetail',
			id:'fileList',
			page:false,
			data:{source:'cust'},
			cols: [[
	        {
	        	type: 'numbers',
	        	title: '序号',
	        	align:'left'
	        },{
	        	field: 'FILE_NAME',
	        	title: '文件名',
	        	minWidth:400,
				templet:'<div><a target="_blank" href="'+ctxPath+'/fileview/{{d.FILE_ID}}?view=online">{{d.FILE_NAME}}</a></div>'
	        },{
	        	field: 'FILE_SIZE',
	        	title: '文件大小 ',
	        	width:80
	        },{
	        	field: 'CREATE_NAME',
	        	title: '上传人',
	        	width:80
	        },{
	        	field: 'CREATE_TIME',
	        	title: '上传时间',
	        	width:140
	        },{
	        	field:'',
	        	title:'操作',
	        	width:80,
	        	templet:'<div><a href="'+ctxPath+'/fileview/{{d.FILE_ID}}?filename={{d.FILE_NAME}}" title="点击查看" target="_blank">下载</a> <a href="javascript:;" class="file-del" onclick="delFile(\'{{d.FILE_ID}}\')">删除</a></div>'
	        }
	        ]],done:function(res,curr,count){
				$("#fileCount").text("("+res.total+")");
	        	
        }});
	}
  </script>
