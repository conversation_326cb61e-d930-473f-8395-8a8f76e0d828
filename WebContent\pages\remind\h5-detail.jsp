<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>${record.title}</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
    <link rel="stylesheet" href="/yq-work/static/css/weui.css"/>
    <link rel="stylesheet" href="/yq-work/static/css/weuix.css"/>
    <script src="/yq-work/static/js/zepto.min.js"></script>
    <script>
        $(function(){
	       $(".weui-c-like .icon").click(function(){
	           if($(this).hasClass('on')) {
	               $(this).removeClass('on')
	           }else{
	               $(this).addClass('on') 
	           }
	       })
        });
    </script>
</head>

<body ontouchstart>
	<div class="weui-content">
	    <div class="weui-c-inner">
		 <div class="weui-c-content">
		      <h2 class="weui-c-title">${record.title}</h2>
		     <div class="weui-c-meta">
		         <span class="weui-c-nickname"><a href="javascript:;">${record.publish_by}</a></span>
		         <em class="weui-c-nickname">${record.publish_time}</em>
		     </div>
		     <div class="weui-c-article">
		      		${record.content}
		      		
		      		<br>
		      		<div id="fileList"></div>
		     </div>
		 </div>
		  <div class="weui-c-tools">
		      <!-- <a href="javascript:;">阅读原文</a> -->
		      <div class="weui-c-readnum">阅读<span id="readnum"> ${record.view_count}</span></div>
		      <div class="weui-c-like">
		         <i class="icon"></i>
		          <span id="likenum">${record.comment_count}</span>
		      </div>
		  </div>
	    </div>
</div>

		
 <script>
		var _hmt = _hmt || [];
		(function() {
		  var hm = document.createElement("script");
		  hm.src = "https://hm.baidu.com/hm.js?3074806290f6205dfc5b78dcaf6349d9";
		  var s = document.getElementsByTagName("script")[0]; 
		  s.parentNode.insertBefore(hm, s);
		})();
		
		try{
			document.addEventListener('WeixinJSBridgeReady', function onBridgeReady() {
			    WeixinJSBridge.call('hideOptionMenu');
			});
		}catch(e){
			console.log(e);
		}
		
		$.ajax({
			url:'/yq-work/webcall?action=FileDao.fileList',
			type:'post',
			dataType:'json',
			data:{fkId:'${record.remind_id}'},
			success:function(rs){
				var data = rs.data;
				var html  = [];
				if(data){
					for(var index in data){
						var row = data[index];
						var fileId = row['FILE_ID'];
						var fileName = row['FILE_NAME'];
						html.push('附件：<a href="/yq-work/fileview/'+fileId+'?filename='+fileName+'&view=online&watermark=1" title="点击查看" target="_blank">'+fileName+'</a>');
					}
					$('#fileList').html(html.join(''));
				}
			}
		});
		
  </script>
</body>
</html>