<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>流程配置</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>流程设置</h5>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">分类</span>	
							<select name="pFlowCode" onchange="queryData();" data-mars="FlowConfigDao.categoryDict" class="form-control input-sm">
							      <option value="">-- </option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">状态</span>	
							<select name="flowState" onchange="queryData();" class="form-control input-sm">
							      <option value="">-- </option>
							      <option value="0" data-class="label label-info">启用</option>
							      <option value="1" data-class="label label-warning">暂停 </option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm ml-15">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="addFlow()"> 新增</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
		</form>
		 <script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default" lay-event="flowList.copyData">复制</button>
			 	<button class="btn btn-sm btn-default ml-10" lay-event="editFlow">编辑</button>
  				<button class="btn btn-sm btn-default ml-10" lay-event="nodeFlow">节点设置</button>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
	$(function(){
		$("#dataMgrForm").render({success:function(){
			initList();
		}});
	});
	
	function initList(){
		$("#dataMgrForm").initTable({
			mars:'FlowConfigDao.categoryList',
			id:'flowTable',
			page:false,
			edit:'editData',
			height:'full-130',
			cellMinWidth:100,
			toolbar:'#btnBar',
			rowEvent:'rowEvent',
			cols: [[
			 {type:'numbers'},
			 {type:'radio'},
			 {
				 field:'FLOW_NAME',
				 title:'流程名称'
			 },{
				 field:'FLOW_CODE',
				 title:'流程代码'
			 },{
				 field:'FLOW_INDEX',
				 title:'排序',
				 edit:'text'
			 },{
				 field:'P_FLOW_CODE',
				 title:'类别',
				 templet:function(row){
					 var pFlowCode  = row['P_FLOW_CODE'];
					 if(pFlowCode=='0'){
						 return '';
					 }else{
						 return getText(pFlowCode,'pFlowCode');
					 }
				 }
			 },{
				 field:'FLOW_DESC',
				 title:'流程描述',
				 minWidth:160
			 },{
				 field:'NODE_COUNT',
				 title:'节点数量',
				 templet:function(row){
					 var pFlowCode  = row['P_FLOW_CODE'];
					 if(pFlowCode=='0'){
						 return '';
					 }else{
						 return row['NODE_COUNT'];
					 }
				 }
			 },{
				 field:'UPDATE_TIME',
				 title:'修改时间',
				 minWidth:160
			 },{
				field:'FLOW_STATE',
				title:'状态',
				templet:function(row){
					var state = row['FLOW_STATE'];
					return getText(state,'flowState');
				}
			}
		]],done:function(){
			$("tbody [data-field='P_FLOW_CODE'][data-content='0']").parent().css('background-color','#ddd');
	  	}
	 });
   }
	
	function editData(obj){
		var data=obj.data;
		ajax.remoteCall("${ctxPath}/web/flow/config/updateRow",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 7});
			}else{
				layer.alert(result.msg,{icon: 7});
			}
		});
	}
			
	function editFlow(dataList){
	    if(dataList.length == 0){
			layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
			return;
		}
	    var data = dataList[0];
		var flowCode = data['FLOW_CODE'];
		popup.layerShow({id:'editNode',area:['650px','500px'],full:true,offset:'20px',url:'${ctxPath}/pages/flow/config/category-edit.jsp',title:'修改',data:{flowCode:flowCode}});
	}
	
	function nodeFlow(dataList){
		if(dataList.length == 0){
			layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
			return;
		}
	    var data = dataList[0];
		var pFlowCode = data['P_FLOW_CODE'];
		if(pFlowCode=='0'){
			return;
		}
		var flowCode = data['FLOW_CODE'];
		var flowName = data['FLOW_NAME'];
		popup.openTab({id:'nodeFlow',url:'${ctxPath}/pages/flow/config/check-node.jsp',full:true,title:flowName+'节点设置',data:{flowCode:flowCode}});
	}
	
	function addFlow(){
		popup.layerShow({id:'editNode',area:['650px','500px'],offset:'20px',full:true,url:'${ctxPath}/pages/flow/config/category-edit.jsp',title:'新增',data:{}});
	}
	
	function queryData(){
		$("#dataMgrForm").queryData({id:'flowTable',page:false});
	}
			
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>

