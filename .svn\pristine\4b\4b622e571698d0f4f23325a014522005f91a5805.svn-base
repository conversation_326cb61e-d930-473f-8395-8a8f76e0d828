package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;

public class ZendaoService extends AppBaseService implements Job{

	private static class Holder{
		private static ZendaoService service=new ZendaoService();
	}
	public static ZendaoService getService(){
		return Holder.service;
	}
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		this.run();
	}
	public  void run(){
		getLogger().info("执行同步用户任务....");
		try {
			String pwd="e91ad915ba65b8de9fd53cafeb9fdd95";//yq123456
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT USERNAME,NIKE_NAME,SEX,MOBILE,EMAIL,BORN,USER_PWD,DATA3,USER_ACCT,DEPTS,ACCT_STATE from easi_user t1 INNER JOIN easi_user_login t2 on t1.USER_ID=t2.USER_ID ",null,new JSONMapperImpl());
			getLogger().info("list>>"+list.size());
			if(list!=null&&list.size()>0){
				Map<String,String> depts=getDepts();
				getLogger().info("depts>>"+depts);
				for(JSONObject jsonObject:list){
				 int acctState=jsonObject.getIntValue("ACCT_STATE");
				 String id= this.getZentaoQuery().queryForString("select id from zt_user where account = ?",jsonObject.getString("USER_ACCT"));
				 if(StringUtils.isBlank(id)&&acctState==0){
					// pwd=jsonObject.getString("USER_PWD").toLowerCase();
					 EasyRecord record=new EasyRecord("zt_user");
					 record.set("realname",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),""));
					 record.set("nickname",StringUtils.defaultIfEmpty(jsonObject.getString("NIKE_NAME"),""));
					 record.set("phone", jsonObject.getString("MOBILE")+"");
					 record.set("mobile", jsonObject.getString("MOBILE")+"");
					 record.set("email", jsonObject.getString("EMAIL")+"");
					 record.set("role", "dev");
					 record.set("account", jsonObject.getString("USER_ACCT"));
					 record.set("password", pwd);
					 String deptId=depts.get(jsonObject.getString("DEPTS"));
					 record.set("dept", deptId==null?"0":deptId);
					 this.getZentaoQuery().save(record);
					 
					 EasyRecord usergroup=new EasyRecord("zt_usergroup","account","group");
					 usergroup.put("account",jsonObject.getString("USER_ACCT"));
					 usergroup.put("`group`",2);
					 try {
						this.getZentaoQuery().save(usergroup);
					} catch (Exception e) {
						e.printStackTrace();
					}
					 
				 }else{
					// pwd=jsonObject.getString("USER_PWD").toLowerCase();
					 EasyRecord record=new EasyRecord("zt_user","id");
					 record.setPrimaryValues(id);
					 record.set("realname",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),""));
					 record.set("nickname",StringUtils.defaultIfEmpty(jsonObject.getString("NIKE_NAME"),""));
					 record.set("phone", jsonObject.getString("MOBILE")+"");
					 record.set("mobile", jsonObject.getString("MOBILE")+"");
					 record.set("email", jsonObject.getString("EMAIL")+"");
					 record.set("account", jsonObject.getString("USER_ACCT"));
					 record.set("password",pwd);
					 if(acctState!=0){
						 record.set("deleted", 1);
					 }else{
						 record.set("deleted", 0);
					 }
					// String deptId=depts.get(jsonObject.getString("DEPTS"));
					 //record.set("dept", deptId==null?"0":deptId);
					 try {
						this.getZentaoQuery().update(record);
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				 }
				}
			}
		} catch (SQLException e2) {
			e2.printStackTrace();
			this.getLogger().error(e2);
		}
	}
	private Map<String,String> getDepts() {
		Map<String,String> depts=new HashMap<String,String>();
		try {
			List<EasyRow> list=this.getZentaoQuery().queryForList("select * from zt_dept");
			if(list!=null){
				for(EasyRow row:list){
					depts.put(row.getColumnValue("NAME"),row.getColumnValue("ID"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return depts;
	}
}
