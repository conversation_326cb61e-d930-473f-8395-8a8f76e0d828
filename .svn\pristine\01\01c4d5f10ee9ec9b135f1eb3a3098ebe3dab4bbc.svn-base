<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div id="fileList">
	<div class="mb-15 btn-group pull-right">
	  	<button class="btn btn-sm btn-default" type="button" onclick="folderHome()"><i class="fa fa-home" aria-hidden="true"></i> 返回首页</button>
	  	<button class="btn btn-sm btn-default ml-10" type="button" onclick="addFolder()"><i class="fa fa-folder" aria-hidden="true"></i> 创建文件夹</button>
	  	<button class="btn btn-sm btn-default ml-10" type="button" onclick="addLink()"> <i class="fa fa-link" aria-hidden="true"></i> 引用链接 </button>
	  	<button class="btn btn-sm btn-default ml-10" type="button" onclick="$('#projectDetailLocalfile').click()"><i class="fa fa-upload" aria-hidden="true"></i> 上传</button>
	</div>
 	<table class="layui-table folderTable" lay-size="sm" lay-even lay-skin="line">
	  <thead>
	    <tr>
	      <th>名称</th>
	      <th>大小</th>
	      <th>创建时间</th>
	      <th>创建者</th>
	      <th>操作</th>
	    </tr> 
	  </thead>
	  <tbody class="folderTbody" data-template="project-folder-template-files" data-mars="FolderDao.list">

	  </tbody>
	  
	  <tbody class="folderTbody" data-template="project-link-template" data-mars="FolderDao.projectLink">

	  </tbody>
	  
	  <tbody class="fileTbody" data-template="project-detail-template-files" data-mars="FileDao.fileListDetail">

	  </tbody>
	</table>
 </div>

		<script id="project-detail-template-files" type="text/x-jsrender">
			{{if data.length==0}}
				<tr>
					<td colspan="5">
						<button class="btn btn-sm btn-link mt-10" type="button" onclick="$('#projectDetailLocalfile').click()">+上传</button>
					</td>
				</tr>
			{{/if}}
			{{for data}}
				<tr>
				  <td>{{call:FILE_TYPE fn='getFileIcon'}} <a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}&nbsp;</span></a></td>
				  <td nowrap>{{:FILE_SIZE}}</td>
				  <td nowrap>{{:CREATE_TIME}}</td>
				  <td nowrap>
					  {{:CREATE_NAME}}
				  </td>
				  <td nowrap>
						<div class="btn-group btn-group-sm">
							<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">操作<span class="caret"></span></button>
							<ul class="dropdown-menu dropdown-menu-right">
								   {{if FILE_TYPE=='.xml'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}/project?action=display" target="_blank">编辑器打开</a>&nbsp;</li>
									{{else FILE_TYPE=='.zip'||FILE_TYPE=='.war'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}/project?action=zip.view" target="_blank">预览</a>&nbsp;</li>
									{{else FILE_TYPE=='.pdf'||FILE_TYPE=='.txt'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}/project?action=get" target="_blank">本地打开</a>&nbsp;</li>
									{{else FILE_TYPE=='.xlsx'||FILE_TYPE=='.xls'||FILE_TYPE=='.docx'||FILE_TYPE=='.doc'||FILE_TYPE=='.ppt'||FILE_TYPE=='.pptx'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}/project?action=office" target="_blank">预览</a>&nbsp;</li>
									{{else FILE_TYPE=='.log'||FILE_TYPE=='.txt'||FILE_TYPE=='.out'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}/project?action=tail" target="_blank">tail</a>&nbsp;</li>
										<li><a href="/yq-work/files/view/{{:FILE_ID}}/project?action=less" target="_blank">less</a>&nbsp;</li>
										<li><a href="/yq-work/files/view/{{:FILE_ID}}/project?action=grep" target="_blank">grep</a>&nbsp;</li>
								   {{/if}}
								<li><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" target="_blank">下载</a></li>
								<li data-user-id="{{:CREATOR}}"><a href="javascript:;" onclick="delProjectFile('{{:FILE_ID}}',this);">删除</a></li>
							</ul>
						</div>
				  </td>
				</tr>
			{{/for}}
		</script>
			<script id="project-folder-template-files" type="text/x-jsrender">
			{{for data}}
				<tr>
				  <td><svg class="icon" aria-hidden="true"><use xlink:href="#icon-wenjian"></use></svg><a href="javascript:;" onclick="openFolder('{{:FOLDER_ID}}','{{:FOLDER_NAME}}')">{{:FOLDER_NAME}}</a></td>
				  <td></td>
				  <td>{{:CREATE_TIME}}</td>
				  <td>
					  {{call:CREATOR fn='getUserName'}}
				  </td>
				  <td>
					
				  </td>
				</tr>
			{{/for}}
		</script>
			<script id="project-link-template" type="text/x-jsrender">
			{{for data}}
				<tr>
				  <td><i class="fa fa-link" aria-hidden="true"></i> <a title="{{:REMARK}}" href="{{:LINK_URL}}" target="_blank">{{:LINK_NAME}}</a></td>
				  <td></td>
				  <td>{{:CREATE_TIME}}</td>
				  <td>
					  {{call:CREATOR fn='getUserName'}}
				  </td>
				  <td>
						<div class="btn-group btn-group-sm">
							<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">操作<span class="caret"></span></button>
							<ul class="dropdown-menu dropdown-menu-right">
								<li><a href="{{:LINK_URL}}" target="_blank">访问</a></li>
								<li data-user-id="{{:CREATOR}}"><a href="javascript:;" onclick="editLink('{{:LINK_ID}}')">修改</a></li>
								<li data-user-id="{{:CREATOR}}"><a href="javascript:;" onclick="delLink('{{:LINK_ID}}',this)">删除</a></li>
							</ul>
						</div>
				  </td>
				</tr>
			{{/for}}
		</script>
		
	<script type="text/javascript">
		var projectUploadFile = function(){
			var fkId=projectId;
			var folderId = $("#folderId").val();
			easyUploadFile({callback:'projectDetailCallback',fkId:fkId,folderId:folderId,source:'project',formId:'projectDetailFileForm',fileId:'projectDetailLocalfile'});
			
		}
	 	 var addFolder = function(){
			var folderName = $("#folderName").val();
			var folderAuth = '1';
			var fkId = projectId;
			var pid = $("#folderId").val();
			popup.layerShow({type:1,maxmin:true,area:['450px','300px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'新增文件夹',data:{id:pid,folderName:folderName,folderAuth:folderAuth,source:'project',fkId:fkId}});
		}
	 	 
	 	 var addLink = function(){
	 		var fkId = projectId;
			var pid = $("#folderId").val();
			popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/project/include/link-edit.jsp',title:'新增链接',data:{pId:pid,source:'addLink',projectId:fkId}});
	 		 
	 	 }
	 	 
	 	 var delProjectFile = function(id,obj){
	 		$(obj).parent().remove();	 
	 		
	 	 }
	 	 
	 	 var editLink = function(linkId){
	 		var fkId = projectId;
			popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/project/include/link-edit.jsp',title:'新增链接',data:{linkId:linkId,source:'addLink',projectId:fkId}});
	 	 }
	 	 
	 	 var delLink = function(linkId,el){
	 		layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/folder?action=deleteLink",{'link.LINK_ID':linkId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							reloadLink();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
	 	 }
	 	 
	 	 var loadTree = function(){
	 		folderHome();
	 	 }
	 	 
	 	 var reloadLink= function(){
	 		folderHome();
	 	 }
	 	 
	 	 var openFolder = function(id,name){
	 		 $("#folderId").val(id);
	 		 $("#folderName").val(name);
	 		 $(".folderTbody").show();
	 		 $(".fileTbody").show();
	 		 $("#fileList").render({data:{folderId:id,fkId:projectId,projectId:projectId,source:'project'}});
	 	 }
	 	 
	 	 var folderHome = function(){
	 		 $(".folderTbody,.fileTbody").show();
	 		 $("#fileList").render({data:{folderId:'0',fkId:projectId,projectId:projectId,source:'project'}});
	 	 }
	
	</script>