package com.yunqu.work.servlet.flow;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/flow/node")
public class NodeServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private int getNodeId() {
		try {
			return this.getQuery().queryForInt("select max(node_id) from yq_flow_approve_node");
		} catch (SQLException e) {
			this.error(null, e);
			return 0;
		}
	}
	
	public EasyResult actionForUpdateNodeConf(){
		EasyRecord model = new EasyRecord("yq_flow_approve_node","node_id");
		try {
			model.setColumns(getJSONObject("conf"));
			this.getQuery().update(model);
			this.updateRole(model.getPrimaryValue().toString());
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddNodeConf(){
		EasyRecord model = new EasyRecord("yq_flow_approve_node","node_id");
		try {
			model.setColumns(getJSONObject("conf"));
			model.set("create_time", EasyDate.getCurrentDateString());
			model.set("node_id",getNodeId()+1);
			this.getQuery().save(model);
			this.updateRole(model.getPrimaryValue().toString());
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void updateRole(String nodeId) {
		try {
			this.getQuery().executeUpdate("update yq_flow_approve_node t1,yq_flow_approve_role t2 set t1.user_id = t2.role_user_id,t1.user_name = t2.role_user_name where t1.role_id = t2.role_id and t1.check_type = 10 and t1.node_id = ?",nodeId );
			this.getQuery().executeUpdate("update yq_flow_category t1 set t1.node_count = (select count(1) from yq_flow_approve_node t2 where t1.flow_code = t2.flow_code)");
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	public EasyResult actionForDelNodeConf() {
		String nodeId  = getJsonPara("nodeId");
		try {
			this.getQuery().executeUpdate("update yq_flow_approve_node set p_node_id = null where p_node_id = ?", nodeId);
			this.getQuery().executeUpdate("update yq_flow_approve_node set c_node_id = null where c_node_id = ?", nodeId);
			this.getQuery().executeUpdate("delete from yq_flow_approve_node where NODE_ID = ?", nodeId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
}
