<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
   
    <parent>
        <groupId>com.yunqu.mars</groupId>
        <artifactId>mars-poi3</artifactId>
        <version>3.5</version>
    </parent>

    <groupId>com.yunqu.work</groupId>
    <artifactId>yq-work</artifactId>
    <packaging>war</packaging>
    <version>2.1</version>

	<properties>
	    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
	    <java.version>1.8</java.version>
	    <maven.compiler.source>${java.version}</maven.compiler.source>
	    <maven.compiler.target>${java.version}</maven.compiler.target>
	    <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
	    <maven-war-plugin.version>3.3.2</maven-war-plugin.version>
	    <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
	    <maven.test.skip>true</maven.test.skip>
	</properties>
	
	<dependencies>
         <dependency>
             <groupId>com.jfinal</groupId>
             <artifactId>jfinal</artifactId>
             <version>5.2.3</version>
         </dependency>
         <dependency>
             <groupId>com.jfinal</groupId>
             <artifactId>jfinal-weixin</artifactId>
             <version>3.4</version>
         </dependency>
         <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.13.1</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
             <groupId>eu.bitwalker</groupId>
             <artifactId>UserAgentUtils</artifactId>
             <version>1.21</version>
         </dependency>
         <dependency>
             <groupId>org.apache.pdfbox</groupId>
             <artifactId>pdfbox</artifactId>
             <version>2.0.8</version>
             <scope>provided</scope>
         </dependency>
         <dependency>
             <groupId>org.commonmark</groupId>
             <artifactId>commonmark</artifactId>
             <version>0.21.0</version>
         </dependency>
         <dependency>
			<groupId>javax.mail</groupId>
			<artifactId>javax.mail-api</artifactId>
			<version>1.5.6</version>
			<scope>provided</scope>
		 </dependency>
		 <dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.5.3</version>
			<scope>provided</scope>
		 </dependency>
		 <dependency>
		    <groupId>com.squareup.okhttp3</groupId>
		    <artifactId>okhttp</artifactId>
		    <version>4.12.0</version>
	     </dependency>
	     <dependency>
		    <groupId>com.squareup.okio</groupId>
		    <artifactId>okio-jvm</artifactId>
		    <version>3.10.2</version>
		    <scope>runtime</scope>
		 </dependency>
		 <dependency>
		    <groupId>com.squareup.okhttp3</groupId>
		    <artifactId>okhttp-sse</artifactId>
		    <version>4.12.0</version>
	     </dependency>
		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-stdlib</artifactId>
			<version>1.9.0</version>
		</dependency>
	</dependencies>

   	<!-- 如是标准Maven项目结构sourceDirectory,resources,webResources不用配置 -->
    <build>
	    <sourceDirectory>src</sourceDirectory>
	    <resources>
	        <resource>
	            <directory>resource</directory>
	        </resource>
	    </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                 </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <webResources>
                   	  <resource>
	                        <directory>src</directory>
	                        <targetPath>WEB-INF/src</targetPath>
	                        <includes>
	                            <include>**/*.java</include>
	                        </includes>
                       </resource>
                        <resource>
                            <directory>WebContent</directory>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
            <plugin>
				 <groupId>com.yunqu.mars.plugin</groupId>
				 <artifactId>console-upgrade</artifactId>
				 <version>1.0</version>
				<executions>
					<execution>
						<goals>
							<goal>generate-version</goal>
							<goal>push-mars</goal>
						</goals>
						<configuration>
							<cleanLibDirectory>true</cleanLibDirectory>
							<copyPath>D:\home</copyPath>
							<jarPatterns>ok*,User*,jfinal*,cos-*,commonmark*</jarPatterns>
							<!-- 项目主版本号 -->
							<primaryVersion>2.0</primaryVersion>
							<!-- 次要版本号格式 -->
							<versionFormat>yyyyMMdd</versionFormat>
							<!-- 是否开启war包推送mars -->
							<enableWarPushMars>true</enableWarPushMars>
							<!-- console地址 -->
							<consoleUrl>http://172.16.85.100:9999/easitline-console</consoleUrl>
							<!-- console登录账号 -->
							<consoleUsername>admin@mars</consoleUsername>
							<!-- console登录密码 -->
							<consolePassword>KS38TYWAG6</consolePassword>
						</configuration>
					</execution>
				</executions>
			</plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
</project>
