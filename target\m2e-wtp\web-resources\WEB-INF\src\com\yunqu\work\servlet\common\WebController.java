package com.yunqu.work.servlet.common;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.core.ActionKey;
import com.jfinal.core.Path;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.base.Constants;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.service.KqNoticeService;
import com.yunqu.work.service.LookLogService;
import com.yunqu.work.service.StaffService;

@Before(AuthInterceptor.class)
@Path(value = "/web")
public class WebController extends BaseController {

	@ActionKey(value = "/task")
	public void yqTask() {
		String taskId = getPara();
		if(StringUtils.isBlank(taskId)) {
			taskId = getPara("taskId");
		}
		render("/pages/task/task-detail.jsp?isDiv=0&taskId="+taskId);
	}
	
	@ActionKey(value = "/weekly/my")
	public void myWeekly() {
		render("/pages/weekly/weekly-my.jsp?isDiv=0");
	}
	
	@ActionKey(value = "/web/kqTop")
	public void kqTop() {
		LogEngine.getLogger("cust-mars-access").info(">kqTop>"+getStaffInfo().getUserName());
		if(hasRole("HR_MGR")||isSuperUser()) {
			setAttr("ismgr", "1");
		}else {
			setAttr("ismgr", "0");
		}
		render("/pages/ehr/kq/kq-console.jsp");
	}
	
	public void task() {
		yqTask();
	}

	
	@ActionKey(value = "/fa/faAppoint")
	public void faAppoint() {
		AppContext app = AppContext.getContext(Constants.APP_NAME);
		String faCheckWirte = app.getProperty("faCheckWirte", "0");
		if("0".equals(faCheckWirte)) {
			renderHtml("暂没开启");
			return;
		}
		render("/pages/ehr/fa/fa-appoint.jsp");
	}
	
	@ActionKey(value = "/fa/check")
	public void faSb() {
		AppContext app = AppContext.getContext(Constants.APP_NAME);
		String faCheckWirte = app.getProperty("faCheckWirte", "0");
		if("0".equals(faCheckWirte)) {
			renderHtml("暂没开启");
			return;
		}
		String selectUserId = getPara("selectUserId");
		if(StringUtils.notBlank(selectUserId)) {
			setAttr("staffInfo", StaffService.getService().getStaffInfo(selectUserId));
		}else {
			setAttr("staffInfo", StaffService.getService().getStaffInfo(getUserId()));
		}
		
		setAttr("LoginAcct", getUserPrincipal().getLoginAcct());
		
		int periodNum = 0 ;
		try {
			periodNum = this.getQuery().queryForInt("select max(period_num) from yq_fa_check");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			periodNum = 0;
		}
		if(periodNum==0) {
			renderHtml("暂无数据");
			return;
		}
		if(String.valueOf(periodNum).endsWith("01")) {
			setAttr("checkTitle", EasyCalendar.newInstance().getYear()+"年半年度盘点");
		}
		if(String.valueOf(periodNum).endsWith("02")) {
			setAttr("checkTitle", EasyCalendar.newInstance().getYear()+"年年终盘点");
		}
		setAttr("periodNum",periodNum);
		render("/pages/ehr/fa/fa-check.jsp");
	}
	
	@ActionKey(value = "/mytask")
	public void mytask() {
		render("/pages/task/my-task-list.jsp");
	}
	
	@ActionKey(value = "/receivedWeekly")
	public void receivedWeekly() {
		render("/pages/weekly/weekly-received.html");
	}
	
	@ActionKey(value = "/version")
	public void version() {
		render("/pages/ops/version-detail.jsp?versionId="+getPara());
	}
	
	@ActionKey(value = "/sj")
	public void sj() {
		render("/pages/crm/contacts/contact-query.jsp?businessId="+getPara());
	}
	
	@ActionKey(value = "/weekly")
	public void weekly() {
		int isDiv = getParaToInt("isDiv", 0);
		String weeklyId = getPara("weeklyId");
		if(StringUtils.isBlank(weeklyId)) {
			weeklyId = getPara();
		}
		render("/pages/weekly/weekly-detail.jsp?isDiv="+isDiv+"&weeklyId="+weeklyId);
	}
	
	@ActionKey(value = "/platform")
	public void platform() {
		render("/pages/ops/platform-list.jsp");
	}
	
	@Clear
	@ActionKey(value = "/accessLog")
	public void accessLog() {
		JSONObject request = JsonKit.getJSONObject(getRequest(), null);
		if(request!=null) {
			LogEngine.getLogger("cust-mars-access").info(request.toJSONString());
		}
	}
	
	
	@ActionKey(value = "/h5/2024")
	public void h52024() {
		List<String> list = new ArrayList<String>();
		list.add("2025年已经悄然降临，让我们怀着满心的期待，为新的机遇和挑战做好准备。<br>每个人都在这星光中闪耀着独特的光芒。<br>在这个新的一年里，让我们齐心协力，共同追求卓越，努力拼搏，为共同的目标而努力奋斗。<br>愿新年给予你无尽的快乐、成长和成功。<br>加油2025。");
		list.add("新年的曙光照耀着我们，2025年为我们带来了新的机遇和挑战。<br>在这一年里，让我们齐心协力，敢于冒险，勇于创新，超越自我，创造属于我们的辉煌。<br>愿新的一年为你带来启发，充满喜悦和成功。<br>让我们共同走向2025年的全新征程！<br>加油2025。");
		list.add("2025年的曙光已现，让我们怀揣梦想，共同启航。在这一年里，让我们同心协力，迎接挑战，创造更加耀眼的成就。<br>愿新年为你带来无尽的创造力、快乐和成功。<br>让我们携手迈向2025年的全新征程！<br>加油2025。");
		list.add("这一年的过往皆为序章<br>未来皆为可望<br>冬去春来，希望至美<br>拨开云雾见天日,守得云开见月明<br>加油2025。");
		list.add("2025年，我们不仅仅是观望者，更是创造者。<br>每一个梦想，每一次尝试，都是对未来的馈赠。<br>让我们以勇气和智慧，书写属于自己的精彩篇章。<br>相信奇迹，创造奇迹。<br>加油2025。");

		list.add("在这个充满可能的年份，让我们突破自我，超越极限。<br>每一个挑战都是成长的机会，每一次尝试都是进步的契机。<br>相信自己，相信团队，相信未来。<br>2025，我们一起绽放光芒！<br>加油2025。");

		list.add("时间如流水，机遇如阳光。<br>2025年，我们将学会珍惜每一刻，把握每一个机会。<br>勇敢追梦，不惧挑战，让生命绽放出最璀璨的光芒。<br>每一步都是精彩，每一天都是希望。<br>加油2025。");

		list.add("生命不仅仅是等待，更是创造。<br>2025年，让我们成为生命的建筑师，用梦想和行动构筑理想的蓝图。<br>相信积跬步，至千里；相信集小流，成大海。<br>我们的未来，正在路上。<br>加油2025。");

		list.add("变化是永恒的主题，创新是不变的旋律。<br>2025年，让我们拥抱变化，激发创新，在每一个瞬间绽放智慧的光芒。<br>不设限，不妥协，勇往直前。<br>我们的潜力，远比想象更加广阔。<br>加油2025。");

		list.add("人生没有彩排，每一天都是现场直播。<br>2025年，我们要活出最精彩的自己，书写最动人的剧本。<br>相信希望，相信力量，相信自己可以改变一切。<br>生命是一场华丽的旅行。<br>加油2025。");

		list.add("在这个充满可能的年份，让我们成为自己的主角。<br>不要被过去的阴影束缚，勇敢地追逐内心的光。<br>每一个梦想，都值得全力以赴。<br>相信奇迹，创造奇迹。<br>加油2025。");

		list.add("2025年，是梦想起航的时刻。<br>我们不再等待机会，而是主动创造机会。<br>每一个挑战都是成长的阶梯，每一次尝试都是进步的动力。<br>相信自己，超越自己。<br>加油2025。");

		list.add("生命的意义不在于等待风暴过去，而是学会在风暴中翩翩起舞。<br>2025年，让我们用勇气和智慧，将挑战转化为机遇。<br>坚韧如钢，柔软如水。<br>我们，注定不凡。<br>加油2025。");

		list.add("时间是最公平的礼物，每个人都有24小时。<br>2025年，让我们用激情点亮时间，用行动丈量梦想。<br>不盲目，不懈怠，勇敢前行。<br>成功属于有准备的人。<br>加油2025。");

		list.add("在这个充满可能的年份，让我们成为希望的种子。<br>播撒梦想，浇灌信念，收获成长。<br>每一个微小的努力，都是通向成功的桥梁。<br>相信过程，相信自己。<br>加油2025。");

		list.add("生命是一场没有彩排的旅行，每一步都充满未知与惊喜。<br>2025年，我们要勇敢探索，敢于尝试，不断突破自我。<br>相信内心的力量，相信梦想的力量。<br>我们，就是奇迹。<br>加油2025。");

		list.add("变化是唯一不变的定律。<br>2025年，让我们拥抱变化，学会适应，勇于创新。<br>每一个挑战都是成长的机会，每一次失败都是成功的基石。<br>相信自己，超越自己。<br>加油2025。");

		list.add("人生没有退路，唯有勇往直前。<br>2025年，我们要成为自己的英雄，书写最动人的故事。<br>不被过去束缚，不被恐惧阻挡。<br>勇气，源于内心的力量。<br>加油2025。");

		list.add("梦想不是等待，而是行动。<br>2025年，让我们用脚步丈量理想，用汗水浇灌希望。<br>每一个微小的进步，都是值得庆祝的胜利。<br>相信过程，相信自己。<br>加油2025。");

		list.add("生命的美妙在于不断探索和成长。<br>2025年，我们要成为终身学习者，保持好奇心和激情。<br>每一天都是新的开始，每一刻都充满可能。<br>勇敢追梦，不惧挑战。<br>加油2025。");

		list.add("时间不会等待任何人，但机会总是留给有准备的人。<br>2025年，让我们提前布局，主动出击。<br>不盲目，不懈怠，勇敢前行。<br>成功，属于有准备的人。<br>加油2025。");

		list.add("人生最大的敌人不是困难，而是没有行动的自己。<br>2025年，我们要打破自我设限，勇敢追逐梦想。<br>相信希望，相信力量，相信自己可以改变一切。<br>行动，是最好的证明。<br>加油2025。");

		list.add("每一个伟大的开始，都源于一个勇敢的决定。<br>2025年，让我们成为自己生命的导演。<br>不被过去束缚，不被恐惧阻挡。<br>相信奇迹，创造奇迹。<br>加油2025。");

		list.add("生命是一场持续的修行，成长是最美的礼物。<br>2025年，我们要学会接纳、感恩和成长。<br>每一个挑战都是成长的机会，每一次尝试都是进步的动力。<br>相信自己，超越自己。<br>加油2025。");
		
		list.add("梦想不分大小，重要的是勇于追逐。<br>2025年，让我们用热情点燃内心，用行动定义未来。<br>不计较得失，只专注当下。<br>每一步都是精彩，每一天都是希望。<br>加油2025。");

		list.add("生命的价值在于不断突破自我，超越极限。<br>2025年，我们要成为更好的自己。<br>勇敢面对挑战，学会从失败中成长。<br>相信内心的力量，相信梦想的力量。<br>加油2025。");

		list.add("机遇总是留给有准备和勇气的人。<br>2025年，让我们主动出击，创造属于自己的精彩。<br>不抱怨，不放弃，勇往直前。<br>成功从未被动等待，而是主动争取。<br>加油2025。");

		list.add("生活不仅是活着，更是绽放。<br>2025年，我们要活出最精彩的自己。<br>用激情点亮生命，用行动丈量梦想。<br>每一个瞬间都值得被珍惜和纪念。<br>加油2025。");

		list.add("改变从未如此容易，但需要勇气和决心。<br>2025年，让我们突破舒适区，拥抱未知。<br>相信自己的潜力，相信团队的力量。<br>我们，注定不凡。<br>加油2025。");

		list.add("人生最大的财富是不断学习和成长。<br>2025年，让我们保持好奇心和学习的热情。<br>每一天都是新的机会，每一刻都充满可能。<br>知识改变命运，行动成就梦想。<br>加油2025。");

		list.add("成功不是一蹴而就，而是日积月累。<br>2025年，我们要保持耐心和恒心。<br>相信点滴努力，相信积跬步至千里。<br>坚持就是最大的胜利。<br>加油2025。");

		list.add("生命中最珍贵的是态度和勇气。<br>2025年，让我们以积极乐观的心态面对一切。<br>不抱怨，不放弃，勇往直前。<br>每一个挑战都是成长的机会。<br>加油2025。");

		list.add("梦想是最美的风景，行动是最好的风采。<br>2025年，我们要成为梦想的实践者。<br>用行动证明自己，用毅力创造奇迹。<br>相信自己，超越自己。<br>加油2025。");

		list.add("生活从不给弱者优待，但总眷顾勇敢的人。<br>2025年，让我们勇敢追梦，不惧挑战。<br>相信希望，相信力量，相信自己。<br>我们的潜力，远比想象更加广阔。<br>加油2025。");
		
		list.add("人生是一场没有彩排的旅行，每一步都充满可能。<br>2025年，我们要成为自己命运的主宰。<br>不被过去束缚，勇敢追逐内心的光。<br>相信奇迹，创造奇迹。<br>加油2025。");

		list.add("成长不仅仅是年龄的增长，更是思维的进化。<br>2025年，让我们保持开放和学习的心态。<br>每一个挑战都是成长的机会，每一次尝试都是进步的动力。<br>我们，注定不凡。<br>加油2025。");

		list.add("生命的价值在于不断突破自我，挑战极限。<br>2025年，我们要成为更好的自己。<br>勇敢面对困难，从失败中汲取力量。<br>相信内心的坚韧，相信梦想的力量。<br>加油2025。");

		list.add("时间是最公平的礼物，关键在于如何珍惜和利用。<br>2025年，让我们用激情点亮时间，用行动丈量梦想。<br>不盲目，不懈怠，勇敢前行。<br>成功属于有准备的人。<br>加油2025。");

		list.add("变化是生命的常态，适应是生存的关键。<br>2025年，我们要学会拥抱变化，勇于创新。<br>每一个挑战都是成长的机会，每一次转变都是新的起点。<br>相信自己，超越自己。<br>加油2025。");

		list.add("梦想不是遥不可及的幻想，而是可以通过行动实现的目标。<br>2025年，让我们付诸行动，追逐内心的理想。<br>不计较得失，只专注当下。<br>我们，就是奇迹。<br>加油2025。");

		list.add("生活的意义不在于等待风暴过去，而是学会在风暴中舞蹈。<br>2025年，我们要用勇气和智慧应对挑战。<br>坚韧如钢，柔软如水。<br>每一个挫折都是成长的养分。<br>加油2025。");

		list.add("人生最大的敌人不是困难，而是没有行动的自己。<br>2025年，让我们打破自我设限，勇敢追逐梦想。<br>相信希望，相信力量，相信自己可以改变一切。<br>行动，是最好的证明。<br>加油2025。");

		list.add("成功不是偶然，而是日复一日的积累和坚持。<br>2025年，我们要保持耐心和恒心。<br>相信点滴努力，相信积跬步至千里。<br>每一个微小的进步都值得庆祝。<br>加油2025。");

		list.add("生命是一场持续的修行，成长是最美的礼物。<br>2025年，我们要学会接纳、感恩和成长。<br>每一个经历都是学习的机会，每一刻都充满可能。<br>相信自己，超越自己。<br>加油2025。");
		
		list.add("人生最大的智慧在于不断学习和适应。<br>2025年，让我们保持好奇心和开放的心态。<br>每一天都是新的机会，每一刻都是成长的契机。<br>知识改变命运，态度决定高度。<br>加油2025。");

		list.add("梦想是内心的指南针，行动是前进的动力。<br>2025年，我们要成为梦想的实践者。<br>不被过去束缚，勇敢追逐内心的光芒。<br>相信自己，创造奇迹。<br>加油2025。");

		list.add("生活从不给弱者优待，但总眷顾勇敢的人。<br>2025年，让我们以勇气和决心面对每一个挑战。<br>不抱怨，不放弃，勇往直前。<br>我们的潜力，远比想象更加广阔。<br>加油2025。");

		list.add("成长是一个持续的过程，需要耐心和毅力。<br>2025年，我们要学会从每一次经历中汲取力量。<br>相信积跬步，至千里；相信集小流，成大海。<br>每一步都是进步。<br>加油2025。");

		list.add("生命的美妙在于不断探索和突破自我。<br>2025年，让我们成为终身学习者。<br>保持好奇心，拥抱变化，勇于创新。<br>每一个挑战都是成长的机会。<br>加油2025。");

		list.add("机遇总是留给有准备和勇气的人。<br>2025年，我们要主动出击，创造属于自己的精彩。<br>不等待，不依赖，勇敢前行。<br>成功从未被动等待，而是主动争取。<br>加油2025。");

		list.add("人生最宝贵的财富是态度和信念。<br>2025年，让我们以积极乐观的心态面对一切。<br>相信希望，相信力量，相信自己可以改变一切。<br>我们，注定不凡。<br>加油2025。");

		list.add("生活不仅是活着，更是绽放和成长。<br>2025年，我们要活出最精彩的自己。<br>用激情点亮生命，用行动丈量梦想。<br>每一个瞬间都值得被珍惜。<br>加油2025。");

		list.add("改变从未如此容易，但需要勇气和决心。<br>2025年，让我们突破舒适区，拥抱未知。<br>不被恐惧阻挡，勇敢追逐内心的梦想。<br>相信自己的潜力。<br>加油2025。");

		list.add("成功是一场持续的修行，需要耐心和坚持。<br>2025年，我们要保持专注和毅力。<br>相信点滴努力，相信积累的力量。<br>坚持就是最大的胜利。<br>加油2025。");
		
		list.add("生命是一场没有彩排的旅行，每一步都充满未知与可能。<br>2025年，我们要成为命运的掌控者。<br>不被过往束缚，勇敢追逐内心的星光。<br>相信奇迹，创造奇迹。<br>加油2025。");

		list.add("成长不仅是技能的积累，更是灵魂的进化。<br>2025年，让我们突破自我的边界。<br>每一个挑战都是成长的养分，每一次尝试都是进步的契机。<br>我们，注定超越平凡。<br>加油2025。");

		list.add("人生最大的勇气，是在迷茫中依然保持前进的信念。<br>2025年，我们要成为自己最坚强的后盾。<br>不惧挫折，不畏艰难，勇往直前。<br>希望在脚下，梦想在心中。<br>加油2025。");

		list.add("生命的价值在于不断探索未知，挑战极限。<br>2025年，我们要成为更优秀的自己。<br>学会接纳失败，从挫折中汲取力量。<br>每一次跌倒，都是重新站起的机会。<br>加油2025。");

		list.add("时间是最公平的礼物，关键在于如何用心雕琢。<br>2025年，让我们用激情点亮每一秒。<br>不盲目，不懈怠，勇敢前行。<br>生命不仅仅是等待，更是创造。<br>加油2025。");

		list.add("变化是生命的本质，适应是生存的智慧。<br>2025年，我们要学会拥抱变化，勇于创新。<br>每一次转变都是重生的机会，每一个挑战都是成长的跳板。<br>相信自己，超越自己。<br>加油2025。");

		list.add("梦想不是遥不可及的幻想，而是可以用行动点亮的灯塔。<br>2025年，让我们成为梦想的建筑师。<br>不计较得失，只专注当下。<br>我们，就是改变世界的力量。<br>加油2025。");

		list.add("生活的意义不在于等待暴风雨过去，而是学会在风暴中起舞。<br>2025年，我们要用智慧和勇气应对挑战。<br>坚韧如钢，柔软如水。<br>每一次磨砺，都是成长的见证。<br>加油2025。");

		list.add("人生最大的敌人不是外在的困难，而是内心的懦弱。<br>2025年，让我们突破自我设限。<br>相信希望，相信力量，相信改变。<br>行动，是最好的宣言。<br>加油2025。");

		list.add("成功不是偶然，而是日复一日的坚持与积累。<br>2025年，我们要保持耐心和恒心。<br>相信点滴努力，相信汇流成海。<br>每一个微小的进步都值得庆祝。<br>加油2025。");

		list.add("生命是一场持续的修行，成长是最美的礼物。<br>2025年，我们要学会接纳、感恩和蜕变。<br>每一个经历都是成长的养分，每一刻都充满可能。<br>相信自己，超越自己。<br>加油2025。");

		list.add("勇气不是没有恐惧，而是在恐惧中依然前行。<br>2025年，我们要直面内心的恐惧。<br>突破舒适区，拥抱未知的挑战。<br>真正的成长，来自于勇敢的尝试。<br>加油2025。");

		list.add("生活是一场没有终点的马拉松，重要的是保持前进的姿态。<br>2025年，我们要成为自己最好的陪跑者。<br>不与他人比较，只与过去的自己竞争。<br>进步，就是最大的胜利。<br>加油2025。");

		list.add("知识改变命运，态度决定高度。<br>2025年，让我们成为终身学习者。<br>保持好奇心，拥抱新知识。<br>学习是最好的投资，成长是最大的财富。<br>加油2025。");

		list.add("梦想是内心的指南针，行动是前进的发动机。<br>2025年，我们要成为梦想的实践者。<br>不被过去束缚，勇敢追逐内心的光芒。<br>相信自己，创造奇迹。<br>加油2025。");
		
		list.add("人生最珍贵的不是结果，而是成长的过程。<br>2025年，我们要学会欣赏每一个瞬间。<br>接纳不完美，享受成长的痛苦与快乐。<br>生命是一场美丽的修行。<br>加油2025。");

		list.add("内心的平静，是最强大的力量。<br>2025年，我们要修炼内心的从容。<br>不被外界干扰，保持内心的坚定。<br>真正的强大，来自内心的平静。<br>加油2025。");

		list.add("每一个梦想都是一次勇敢的冒险。<br>2025年，让我们成为梦想的探险家。<br>不设限，不退缩，勇往直前。<br>生命因梦想而精彩，因勇气而璀璨。<br>加油2025。");

		list.add("成长是一个不断拆解自我的过程。<br>2025年，我们要勇于挑战自己的舒适区。<br>打破固有思维，拥抱新的可能。<br>真正的成长，往往在不舒服中发生。<br>加油2025。");

		list.add("生活就像一面镜子，你笑它就笑，你哭它就哭。<br>2025年，我们选择用积极的态度面对一切。<br>改变不了环境，就改变自己的心态。<br>快乐是一种选择，幸福是一种能力。<br>加油2025。");

		list.add("真正的智慧，是学会用不同的角度看世界。<br>2025年，我们要培养开放的心态。<br>保持好奇，学会倾听，尊重差异。<br>世界因多元而美丽。<br>加油2025。");

		list.add("每一个挫折都是成长的垫脚石。<br>2025年，我们要学会从失败中汲取力量。<br>不怕犯错，勇于尝试，善于总结。<br>失败不可怕，可怕的是失去尝试的勇气。<br>加油2025。");

		list.add("生命的意义不在于活得有多长，而在于活得有多精彩。<br>2025年，我们要活出最真实的自己。<br>追求内心的热情，实现内心的梦想。<br>精彩不是等来的，是拼出来的。<br>加油2025。");

		list.add("坚持不等于固执，成长不等于完美。<br>2025年，我们要学会灵活调整。<br>保持初心，但不拘泥于固有模式。<br>成长是一个不断调整的过程。<br>加油2025。");

		list.add("最大的对手永远是昨天的自己。<br>2025年，我们要不断突破自我极限。<br>每一天都比昨天更强，每一步都更坚定。<br>进步，就是对自己最大的尊重。<br>加油2025。");

		list.add("生活的美，在于学会用感恩的心看世界。<br>2025年，我们要学会感恩。<br>感恩生活，感恩挑战，感恩成长。<br>感恩是一种智慧，也是一种修行。<br>加油2025。");

		list.add("真正的自由，来自内心的强大。<br>2025年，我们要解放内心。<br>不被期望束缚，不被标签定义。<br>做最真实的自己，活出最精彩的人生。<br>加油2025。");

		list.add("每一个梦想的实现，都需要付出超越常人的努力。<br>2025年，我们要保持专注。<br>不轻易放弃，不被暂时的困难打倒。<br>坚持，是最大的超能力。<br>加油2025。");

		list.add("生命中最重要的不是位置，而是方向。<br>2025年，我们要明确人生方向。<br>选择比努力更重要，态度决定高度。<br>相信自己的选择，相信未来。<br>加油2025。");

		list.add("勇气不是没有恐惧，而是在恐惧中依然前行。<br>2025年，我们要直面内心的恐惧。<br>接纳不完美，拥抱未知的可能。<br>成长，就是不断战胜恐惧的过程。<br>加油2025。");
		
		list.add("人生最大的智慧，是学会与自己和解。<br>2025年，我们要接纳内心的每一个模样。<br>不苛责过去，不过分担心未来。<br>当下，是最美的时刻。<br>加油2025。");

		list.add("生命的意义，不在于完美，而在于真实。<br>2025年，我们要勇敢做真实的自己。<br>不需要讨好所有人，只需要成为内心最想成为的人。<br>真实，是最美的姿态。<br>加油2025。");

		list.add("成长是一场没有终点的旅程。<br>2025年，我们要保持对知识的渴望。<br>每一天都是学习的机会，每一刻都是成长的契机。<br>好奇心，是永远的年轻。<br>加油2025。");

		list.add("人生最大的财富，不是金钱，而是内心的富足。<br>2025年，我们要丰盈内心。<br>培养同理心，享受当下，珍惜关系。<br>真正的富有，来自内心的平静。<br>加油2025。");

		list.add("意志力，是通向梦想的唯一通行证。<br>2025年，我们要磨炼意志。<br>不轻易放弃，不被困难击倒。<br>坚持，就是最大的超能力。<br>加油2025。");

		list.add("生活不会等待任何人，时间不会为谁停留。<br>2025年，我们要及时行动。<br>不再犹豫，不再拖延。<br>现在不开始，未来只会更远。<br>加油2025。");

		list.add("最深的成长，往往发生在最艰难的时刻。<br>2025年，我们要学会从逆境中汲取力量。<br>每一次挫折都是成长的机会。<br>坚韧，是生命最美的姿态。<br>加油2025。");

		list.add("自我怀疑是成长的开始，自我相信是成长的力量。<br>2025年，我们要建立自信。<br>相信自己的潜能，相信改变的力量。<br>自信，源于不断的尝试。<br>加油2025。");

		list.add("生命的美妙在于不确定性。<br>2025年，我们要拥抱未知。<br>不被固有思维限制，勇于尝试新的可能。<br>未知，是最大的惊喜。<br>加油2025。");

		list.add("真正的成长，是学会独立思考。<br>2025年，我们要培养独立的思维。<br>不盲从，不随波逐流。<br>思考，是最珍贵的能力。<br>加油2025。");

		list.add("情绪管理，是最重要的生活技能。<br>2025年，我们要学会控制情绪。<br>不被负面情绪左右，保持内心的平静。<br>情绪，决定生活的质量。<br>加油2025。");

		list.add("生活不是等待暴风雨过去，而是学会在雨中翩翩起舞。<br>2025年，我们要保持乐观。<br>接纳生活的不完美，享受每一个瞬间。<br>态度，改变一切。<br>加油2025。");

		list.add("最大的投资，永远是投资自己。<br>2025年，我们要持续学习。<br>提升技能，拓展视野，丰富阅历。<br>学习，是最有价值的资本。<br>加油2025。");

		list.add("人生没有彩排，每一天都是现场直播。<br>2025年，我们要活出精彩。<br>珍惜当下，勇敢追梦。<br>生命，因热情而美丽。<br>加油2025。");

		list.add("真正的强大，是内心的从容。<br>2025年，我们要修炼心灵。<br>不被外界干扰，保持内心的平静。<br>从容，是最高级的修为。<br>加油2025。");
		
		list.add("人生最大的智慧，是学会平衡。<br>2025年，我们要在追求与放松间找到节奏。<br>不盲目内卷，不完全放纵。<br>生活，是一场精妙的平衡艺术。<br>加油2025。");

		list.add("每一个选择，都是一次重新定义自我的机会。<br>2025年，我们要勇于选择。<br>不被过去定义，不被标签限制。<br>选择，是生命最大的自由。<br>加油2025。");

		list.add("真正的成长，是认知的升级。<br>2025年，我们要扩展思维边界。<br>突破固有思维模式，拥抱新的可能。<br>思维的广度，决定人生的高度。<br>加油2025。");

		list.add("生命最大的敌人不是困难，而是迷茫。<br>2025年，我们要明确方向。<br>设定清晰的目标，坚定地前行。<br>方向比努力更重要。<br>加油2025。");

		list.add("内心的强大，源于不断地自我突破。<br>2025年，我们要挑战自我极限。<br>走出舒适区，拥抱未知的挑战。<br>成长，就是不断超越自己。<br>加油2025。");

		list.add("生活的艺术，在于保持好奇与热情。<br>2025年，我们要重拾对世界的好奇。<br>像孩子一样学习，像诗人一样感受。<br>好奇心，是永远的青春。<br>加油2025。");

		list.add("最深的力量，来自内心的平静。<br>2025年，我们要修炼内心。<br>不被外界干扰，保持内心的从容。<br>平静，是最强大的武器。<br>加油2025。");

		list.add("人生没有标准答案，只有独特的精彩。<br>2025年，我们要活出自己的样子。<br>不与他人比较，只与过去的自己对话。<br>独特，是最大的价值。<br>加油2025。");

		list.add("真正的智慧，是学会同理与包容。<br>2025年，我们要扩展心胸。<br>尊重差异，理解不同。<br>包容，是最高级的智慧。<br>加油2025。");

		list.add("生命的意义，在于不断创造价值。<br>2025年，我们要找到内心的使命。<br>为自己，为他人，为这个世界贡献力量。<br>价值，决定生命的厚度。<br>加油2025。");

		list.add("最大的成长，是学会独立思考。<br>2025年，我们要摆脱思维的桎梏。<br>质疑、思考、判断。<br>思考，是生命最珍贵的能力。<br>加油2025。");

		list.add("生活的韧性，来自于接纳与感恩。<br>2025年，我们要修炼感恩之心。<br>感谢生活的每一个考验。<br>感恩，是最美的生活态度。<br>加油2025。");

		list.add("真正的自由，源于内心的强大。<br>2025年，我们要解放自己。<br>不被期望束缚，不被标签定义。<br>自由，是生命最高的追求。<br>加油2025。");

		list.add("生命最美的状态，是保持热爱。<br>2025年，我们要点亮内心的火焰。<br>对生活充满热情，对梦想执着追求。<br>热爱，是生命最美的模样。<br>加油2025。");

		list.add("成长是一场与自己的对话。<br>2025年，我们要保持自我觉察。<br>审视内心，接纳不完美。<br>成长，是最美的修行。<br>加油2025。");
		
		Random random = new Random();
        // 获取随机索引
        int randomIndex = random.nextInt(list.size());
        // 通过随机索引获取列表中的元素
        String randomElement = list.get(randomIndex);
		set("word", randomElement);
		render("/pages/my/2022/index.jsp");
	}
	
	@ActionKey(value = "/affair")
	public void affair() {
		String userId = getPara("userId");
		String affairId = getPara();
		if(StringUtils.notBlank(userId)) {
			set("userId", userId);
			render("/pages/affair/affair-index.jsp?source=write");
		}else if(StringUtils.isBlank(affairId)) {
			render("/pages/affair/affair-index.jsp");
		}else if("write".equals(affairId)){
			render("/pages/affair/affair-index.jsp?source=write");
		}else if("send".equals(affairId)){
			render("/pages/affair/affair-index.jsp?source=send");
		}else {
			String creator = Db.queryStr("select creator from yq_affair where affair_id = ?",affairId);
			if(StringUtils.notBlank(creator)) {
				setAttr("userData",StaffService.getService().getStaffInfo(creator));
				render("/pages/affair/affair-detail.jsp?isDiv=0&affairId="+affairId);
			}else {
				render("/pages/affair/affair-index.jsp");
			}
		}
	}
	
	@ActionKey(value = "/notice")
	public void notice() {
		keepPara();
		String remindId = getPara("remindId");
		if(StringUtils.isBlank(remindId)) {
			remindId = getPara();
		}
		set("remindId",remindId);
		set("remindType",getPara("remindType"));
		render("/pages/remind/remind-detail.jsp");
	}
	
	@ActionKey(value = "/article")
	public void article() {
		String id = getPara();
		if(StringUtils.isBlank(id)) {
			renderHtml("请求不合法");
			return;
		}
		Record record = Db.findFirst("select * from yq_remind where remind_id = ?", id);
		if(record==null) {
			renderHtml("您的链接存在错误");
			return;
		}
		int status = record.getInt("status");
		if(status!=0) {
			renderHtml("内容未设置公开访问");
			return;
		}
		
		if(getRequest().getRemoteUser()!=null) {
			String creator = record.get("creator");
			if(!getUserId().equals(creator)){
				LookLogService.getService().addLog(getUserId(),id,"remind",getRequest());
				Db.update("update yq_remind t1 set t1.view_count = (select count(1) from yq_look_log t2 where t2.fk_id = t1.remind_id) where t1.remind_id = ?", id);
			}
		}
		
		setAttr("record",record);
		render("/pages/remind/h5-detail.jsp");
	}
	
	@ActionKey(value = "/news")
	public void news() {
		EasySQL sql = new EasySQL("select remind_id,remind_type,title,publish_by,publish_time,summary,cover_url from yq_remind where 1=1");
		sql.append("and status = 0");
		sql.append("and remind_type in(1,2)");
		sql.append("order by publish_time desc limit 30");
		List<Record> list = Db.find(sql.getSQL(), sql.getParams());
		setAttr("list", list);
		render("/pages/remind/h5-list.jsp");
	}
	
	@ActionKey(value = "/twitter")
	public void twitter() {
		keepPara();
		render("/pages/remind/twitter-list.jsp");
	}
	
	
	@ActionKey(value = "/docEdit")
	public void docEdit() {
		String docId = getPara();
		if(StringUtils.notBlank(docId)) {
			if(docId.length()<20) {
				docId = Db.queryStr("select doc_id from yq_doc where doc_code = ?",docId);
			}
			render("/pages/doc/doc-edit.jsp?isDiv=0&docId="+docId);
		}else {
			renderHtml("文档不存在.");
		}
	}
	
	@ActionKey(value = "/doc")
	public void doc() {
		String docId = getPara();
		if(StringUtils.notBlank(docId)) {
			if(docId.length()<20) {
				docId = Db.queryStr("select doc_id from yq_doc where doc_code = ?",docId);
			}
			render("/pages/doc/doc-detail.jsp?isDiv=0&docId="+docId);
		}else {
			renderHtml("文档不存在.");
		}
	}
	
	@ActionKey(value = "/folder")
	public void folder() {
		keepPara();
		String folderId = getPara(0,"0");
		if(StringUtils.notBlank(folderId)) {
			String folderCode = null;
			if(folderId.length()<20) {
				folderCode = folderId;
				folderId = Db.queryStr("select folder_id from YQ_FOLDER where folder_code = ?",folderId);
			}else {
				folderCode = getPara(1);
			}
			render("/pages/doc/folder-list.jsp?isDiv=0&folderId="+folderId+"&folderCode="+folderCode+"&folderAuth="+getPara(2,"0"));
		}else {
			renderHtml("目录不存在.");
		}
	}
	
	@ActionKey(value = "/docs")
	public void docs() {
		keepPara();
		String folderCode = getPara(0);
		if(StringUtils.notBlank(folderCode)) {
			String	folderId = Db.queryStr("select folder_id from YQ_FOLDER where folder_code = ?",folderCode);
			render("/pages/doc/docs-list.jsp?isDiv=0&folderId="+folderId+"&folderCode="+folderCode);
		}else {
			renderHtml("目录不存在.");
		}
	}
	
	@ActionKey("/security/code")
	public void code() {
		String code = RandomKit.smsAuthCode(4);
		try {
			this.getMainQuery().executeUpdate("update easi_user set auth_code = ?,auth_code_time = ? where user_id = ?",code,System.currentTimeMillis(),getUserId());
		} catch (SQLException e) {
			e.printStackTrace();
			renderHtml("请稍后再试");
			return;
		}
		renderHtml("<div style='margin: 0 auto;font-size:50px;'>【云趣科技】您的验证码为: <b>"+code+"</b>。有效期5分钟，不要告诉任何人。</div>");
	}
	
	
	@ActionKey("/")
	public void index() {
		renderHtml("404");
	}
	
	public void test() {
		renderHtml("test");
		
	}
	
	
	@ActionKey(value = "/kq")
	public void kq() {
		boolean flag =  KqNoticeService.getService().kqFlag();
		if(!flag){
			//renderHtml("今日法定休息， 无须打卡。");
			//return;
		}
		set("overtime", flag?"1":"0");
		Record kqInfo  = Db.findFirst("select * from yq_kq_people where kq_user_id = ? and state = 0", getUserId());
		set("kqInfo",kqInfo);
		set("zaoanWord",KqNoticeService.getService().getZaoanWord());
		set("staffInfo", StaffService.getService().getStaffInfo(getUserId()));
		set("nowTime",EasyDate.getCurrentDateString());
		set("currentTime", EasyDate.getCurrentDateString("yyyy/MM/dd HH:mm"));
		set("currentDate", EasyDate.getCurrentDateString("yyyy/MM/dd"));
		render("/wx/kq.jsp");
	}
	
	@ActionKey(value = "/myKqRecord")
	public void myKqRecord() {
		render("/pages/ehr/kq/kq-record-h5.html");
	}
	
	@ActionKey("/map/req")
	public void mapReq() {
		String url = get("url");
		if(StringUtils.isBlank(url)) {
			renderJson(EasyResult.fail("url is empty"));
			return;
		}
		Connection connection = Jsoup.connect(url);
		try {
			Map<String,String[]> map = getParaMap();
			Map<String,String> newMap = new HashMap<String,String>();
			for(String key:map.keySet()) {
				String[] array = map.get(key);
				if(array!=null) {
					newMap.put(key,array[0]);
				}
			}
			connection.data(newMap);
			connection.data("key","7QTBZ-JKQEX-QMI4U-T3QCV-CBTXJ-HHBRI");
			connection.header("Host", "apis.map.qq.com");
			connection.header("Origin", "https://lbs.qq.com/");
			connection.header("Sec-Fetch-Mode", "cors");
			connection.header("Referer", "https://lbs.qq.com/");
			Response response = connection.ignoreContentType(true).timeout(5000).method(Method.GET).execute();
			String body = response.body();
			renderJson(EasyResult.ok(JSONObject.parseObject(body)));
		} catch (IOException e) {
			getLogger().error(null,e);
			renderJson(EasyResult.fail(e.getMessage()));
		}
	}
}
