<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择项目</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectProjectForm">
       	   <input type="hidden" name="dataType" id="dataType">
	       <div class="row">
           	   	 <div class="layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: 0px 15px 10px;">
         		 	 	 <ul class="layui-tab-title"><li data-val="0" class="layui-this">全部</li><li data-val="1">我负责的</li><li data-val="2">我参与的</li><li data-val="3">最近负责任务</li><li data-val="4">最近发起任务</li><li data-val="5">最近周报</li></ul>
         		 	 </div>
	   			 <div class="input-group input-group-sm ml-15" style="width: 220px">
					 <span class="input-group-addon">名称|编号</span>	
					 <input type="text" name="projectName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter" onclick="SelectProject.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		</div>
            	<div class="ibox">
              		<table id="projectList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectProject.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectProject = {
					sid:'${param.sid}',
					query : function(){
						$("#selectProjectForm").queryData({id:'projectList'});
					},
					initTable : function(){
						$("#selectProjectForm").initTable({
							mars:'ProjectDao.selectProjectList',
							id:'projectList',
							page:true,
							limit:50,
							height:'400px',
							rowDoubleEvent:'SelectProject.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'PROJECT_NAME',
								title: '合同名称'
							},{
							    field: 'PROJECT_NO',
								title: '编号',
								width:120
							}
							]]
					       }
						);
						
						layui.element.on('tab(stateFilter)', function(){
							$("#dataType").val(this.getAttribute('data-val'));
							SelectProject.query();
						});
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectProject.sid+"']");
						var itemId = el.data('id')||SelectProject.sid;
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('projectList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var nos = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['PROJECT_NAME']);
									ids.push(data[index]['PROJECT_ID']);
									nos.push(data[index]['PROJECT_NO']);
								}
								
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								if(el.prev().prev().length>0){
									el.prev().prev().val(nos.join(','));
								}
								try{
								　if($.isFunction(selctProjectCallBack)){
							　　　　     selctProjectCallBack(data[0]);
								  } 
								}catch(e){}
								
								popup.layerClose("selectProjectForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
								if(el.prev().prev().length>0){
									el.prev().prev().val('');
								}
							}
						}else{
							el.val(selectRow['PROJECT_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['PROJECT_ID']);
							}
							if(el.prev().prev().length>0){
								el.prev().prev().val(selectRow['PROJECT_NO']);
							}
							
							try{
							　 if($.isFunction(selctProjectCallBack)){
						　　　　     selctProjectCallBack(selectRow);
							  } 
							}catch(e){}
							popup.layerClose("selectProjectForm");
						}
					}
					
			};
			$(function(){
				SelectProject.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>