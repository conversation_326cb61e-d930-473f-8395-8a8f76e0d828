package com.yunqu.work.servlet.crm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.IncomeStageModel;
import com.yunqu.work.service.ContractService;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;


import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
@WebServlet("/servlet/incomeStage/*")
public class IncomeStageServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public EasyResult actionForAdd(){
        IncomeStageModel model = getModel(IncomeStageModel.class,"incomeStage");
        model.setCreator(getUserId());
        model.addCreateTime();
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        model.setPrimaryValues(RandomKit.uuid().toUpperCase());
        String contractStageId = model.getString("CONTRACT_STAGE_ID");
        String incomeStageId = model.getIncomeStageId();
        String compDate = model.getString("ACT_COMP_DATE");
        String contractId = model.getString("CONTRACT_ID");
        String setStageResult = addStageIdForContractStage(contractStageId,incomeStageId,compDate);
        if(! "success".equals(setStageResult)){
            return EasyResult.fail(setStageResult);
        }

        String amount = model.getString("PLAN_AMOUNT");
        model.set("UNCONFIRM_AMOUNT_A",amount);
        model.set("UNCONFIRM_AMOUNT_B",amount);

        try {
            model.save();
            ContractService.getService().addCount("INCOME_STAGE_COUNT",contractId);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdate(){

        IncomeStageModel model = getModel(IncomeStageModel.class,"incomeStage");
        model.set("UPDATE_TIME",EasyDate.getCurrentDateString());
        model.set("UPDATE_BY",getUserId());
        String oldContractStageIds = getJsonPara("oldContractStageIds");
        String incomeStageId = model.getIncomeStageId();
        String contractStageIds = model.getString("CONTRACT_STAGE_ID");
        String compDate = model.getString("ACT_COMP_DATE");

        if(!oldContractStageIds.equals( contractStageIds )){
            String setContractStageResult2 = removeStageIdForContractStage(oldContractStageIds);
            if(! "success".equals(setContractStageResult2)){
                return EasyResult.fail(setContractStageResult2);
            }
            String setContractStageResult = addStageIdForContractStage(contractStageIds,incomeStageId,compDate);
            if(! "success".equals(setContractStageResult)){
                return EasyResult.fail(setContractStageResult);
            }
        }else {
            String setContractStageResult = addStageIdForContractStage(contractStageIds,incomeStageId,compDate);
            if(! "success".equals(setContractStageResult)){
                return EasyResult.fail(setContractStageResult);
            }
        }

        String oldAmount = getJsonPara("oldAmount");
        String newAmount = model.getString("PLAN_AMOUNT");
        if(!newAmount.equals(oldAmount)){
            model.set("UNCONFIRM_AMOUNT_A",newAmount);
            model.set("UNCONFIRM_AMOUNT_B",newAmount);
        }

        try {
            model.update();
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }


    public EasyResult actionForBatchDel(){
        JSONArray incomeStageArray = getJSONArray();
        for(int i=0;i<incomeStageArray.size();i++){
            JSONObject incomeStageObject=incomeStageArray.getJSONObject(i);
            String contractStageId = incomeStageObject.getString("CONTRACT_STAGE_ID");
            String incomeStageId = incomeStageObject.getString("INCOME_STAGE_ID");
            if(StringUtils.notBlank(contractStageId)){
                String setContractStageResult = removeStageIdForContractStage(contractStageId);
                if(! "success".equals(setContractStageResult)){
                    return EasyResult.fail(setContractStageResult);
                }
            }
            try {
                this.getQuery().executeUpdate("delete from yq_contract_income_stage where INCOME_STAGE_ID = ?",incomeStageId );
                String contractId = incomeStageObject.getString("CONTRACT_ID");
                ContractService.getService().subCount("INCOME_STAGE_COUNT",contractId);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }

    public String addStageIdForPayment(String paymentIds,String incomeStageId){
        String []payments = paymentIds.split(",");
        for (String paymentId:payments) {
            String sql = "UPDATE yq_contract_payment SET INCOME_STAGE_FLAG = 1 ,INCOME_STAGE_ID = ? WHERE PAYMENT_ID = ?";
            try {
                this.getQuery().executeUpdate(sql, new Object[]{incomeStageId,paymentId});
            }catch (SQLException e){
                this.error(e.getMessage(),e);
                return "修改款项状态失败："+e.getMessage();
            }
        }
        return "success";
    }

    public String addStageIdForContractStage(String contractStageIds,String incomeStageId,String compDate){
        String []contractStages = contractStageIds.split(",");
        for (String contractStageId:contractStages) {
            String sql = "UPDATE yq_contract_stage SET INCOME_STAGE_FLAG = 1 ,INCOME_STAGE_ID = ? ,ACT_COMP_DATE = ? WHERE STAGE_ID = ?";
            try {
                this.getQuery().executeUpdate(sql, new Object[]{incomeStageId,compDate,contractStageId,});
            }catch (SQLException e){
                this.error(e.getMessage(),e);
                return "修改合同阶段状态失败："+e.getMessage();
            }
        }
        return "success";
    }

    public String removeStageIdForPayment(String paymentIds){
        String[] payments = paymentIds.split(",");
        for (String paymentId : payments) {
            String sql = "UPDATE yq_contract_payment SET INCOME_STAGE_FLAG = 0 ,INCOME_STAGE_ID = '' WHERE PAYMENT_ID = ?";
            try {
                this.getQuery().executeUpdate(sql, paymentId);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return "删除款项状态失败："+e.getMessage();
            }
        }
        return "success";
    }
 public String removeStageIdForContractStage(String contractStageIds){
        String[] contractStages = contractStageIds.split(",");
        for (String contractStageId : contractStages) {
            String sql = "UPDATE yq_contract_stage SET INCOME_STAGE_FLAG = 0 ,INCOME_STAGE_ID = '' ,ACT_COMP_DATE = '' WHERE STAGE_ID = ?";
            try {
                this.getQuery().executeUpdate(sql, contractStageId);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return "删除合同阶段状态失败："+e.getMessage();
            }
        }
        return "success";
    }


}
