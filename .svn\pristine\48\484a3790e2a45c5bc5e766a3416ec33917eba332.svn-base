package com.yunqu.work.service;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.List;

public class ContractService extends AppBaseService {
    private static class Holder {
        private static ContractService service = new ContractService();
    }

    public static ContractService getService() {
        return ContractService.Holder.service;
    }

    public void reloadCount(String countName, String tableName, String contractId) {
        try {
            if (StringUtils.isBlank(contractId) || StringUtils.isBlank(tableName) || StringUtils.isBlank(countName)) {
                return;
            } else {
                EasySQL sql = new EasySQL("select CONTRACT_ID , COUNT(*) AS COUNT_NUM ");
                sql.append("FROM "+tableName);
                sql.append(contractId, " WHERE CONTRACT_ID = ? ");
                if("INCOME_CONFIRM_COUNT".equals(countName)){
                    sql.append("and CONFIRM_TYPE = 'A'");
                }else if("INCOME_CONFIRM_COUNT_B".equals(countName)){
                    sql.append("and CONFIRM_TYPE = 'B'");
                }
                sql.append("GROUP BY CONTRACT_ID");
                EasyRow row = this.getQuery().queryForRow(sql.getSQL(), sql.getParams());
                String count = "";
                if(row == null){
                    count = "0";
                }else {
                    count = row.getColumnValue("COUNT_NUM");
                }
                if (StringUtils.isBlank(count)) {
                    count = "0";
                }
                setCount(countName, contractId, count);
                return;
            }
        } catch (SQLException e) {
            getLogger().error(e.getMessage(), e);
            return;
        }
    }


    public void reloadAllCount(String countName,String tableName){
        try {
            EasySQL sql = new EasySQL("select CONTRACT_ID , COUNT(*) AS COUNT_NUM from");
            sql.append(tableName);
            if("INCOME_CONFIRM_COUNT".equals(countName)){
                sql.append("where CONFIRM_TYPE = 'A'");
            }else if("INCOME_CONFIRM_COUNT_B".equals(countName)){
                sql.append("where CONFIRM_TYPE = 'B'");
            }
            sql.append("GROUP BY CONTRACT_ID");
            List<EasyRow> rows = this.getQuery().queryForList(sql.getSQL());
            if (rows == null || rows.size() == 0) {
                return;
            }
            for (EasyRow row : rows) {
                String contractId = row.getColumnValue("CONTRACT_ID");
                String count = row.getColumnValue("COUNT_NUM");
                if (StringUtils.isBlank(count)) {
                    count = "0";
                }
                setCount(countName, contractId, count);
            }
        } catch (SQLException e) {
            getLogger().error(e.getMessage(), e);
            return;
        }
    }


    public void setCount(String countName, String contractId, String count) {
        String textJsonStr = null;
        try {
            textJsonStr = this.getQuery().queryForString("select TEXT_JSON from yq_project_contract where contract_id = ?", contractId);
            JSONObject textJson = null;
            if (StringUtils.isBlank(textJsonStr)) {
                textJson = new JSONObject();
            } else {
                textJson = JSONObject.parseObject(textJsonStr);
            }
            textJson.put(countName, count);
            String newTextJson = textJson.toJSONString();
            this.getQuery().executeUpdate("UPDATE yq_project_contract SET TEXT_JSON = ? WHERE contract_id = ?", new Object[]{newTextJson, contractId});
        } catch (SQLException e) {
            getLogger().error(e.getMessage(), e);
            return;
        }
    }


}
