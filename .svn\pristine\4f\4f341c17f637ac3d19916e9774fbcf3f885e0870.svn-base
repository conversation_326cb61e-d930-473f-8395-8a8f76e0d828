<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>文件夹</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="FolderDao.record" autocomplete="off" data-mars-prefix="folder.">
   		   		<input type="hidden" value="${param.pFolderId}" name="pFolderId"/>
   		   		<input type="hidden" value="${param.fkId}" name="folder.FK_ID"/>
   		   		<input type="hidden" value="${param.folderId}" name="folder.FOLDER_ID"/>
   		   		<input type="hidden" value="${param.source}" name="folder.SOURCE"/>
   		   		<input type="hidden" value="${param.folderAuth}" name="folder.FOLDER_AUTH"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <c:if test="${!empty param.folderName}">
				            <tr>
				            	<td>上级目录</td>
				            	<td>
				            		<input name="" class="form-control input-sm" readonly="readonly" value="${param.folderName}"/>
				            	</td>
				            </tr>
			            </c:if>
			            <tr>
		                    <td style="width: 80px" class="required">文件夹名称</td>
		                    <td><input data-rules="required" maxlength="100" value="" type="text" name="folder.FOLDER_NAME" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td class="required">排序</td>
		                    <td><input data-rules="required" value="0" type="number" name="folder.ORDER_INDEX" class="form-control input-sm"></td>
			            </tr>
			            <c:if test="${param.folderAuth=='3'}">
				            <tr>
			                    <td class="required">选择人员</td>
			                    <td>
			                    	<input name="folder.SHARE_USER_IDS" type="hidden">
									<input onclick="multiUser(this)" id="userIds" name="folder.SHARE_USER_STRS" class="form-control input-sm">
			                    </td>
				            </tr>
			            </c:if>
			        </tbody>
				 </table>
				 <div class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
			    	  <c:if test="${!empty param.folderId}">
				    	  <button type="button" class="btn btn-warning btn-sm ml-15"  onclick="Edit.delData()"> 删除 </button>
			    	  </c:if>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
	
		Edit.folderId='${param.folderId}';
		
		$(function(){
			$("#editForm").render({success:function(){
				
			}});  
		});
		
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.folderId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/folder?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						loadTree();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/folder?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						loadTree();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		Edit.delData = function(){
			layer.confirm('确认是否要删除',{icon:3},function(){
				ajax.remoteCall("${ctxPath}/servlet/folder?action=del",{'folder.FOLDER_ID':Edit.folderId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							loadTree();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
			
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>