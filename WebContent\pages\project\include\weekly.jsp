<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table class="layui-hide" id="personWeekly"></table>
<script type="text/javascript">
	
	function loadPersonWeekly(){
		$("#projectDetail").initTable({
			mars:'WeeklyDao.weeklyProjectList',
			id:"personWeekly",
			data:{},
			height:'full-130',
			cols: [[
	         {
				title: '序号',
				type:'numbers'
			 },{
			    field: 'WEEK_NO',
				title: '周期/工时',
				width:90,
				align:'left',
				templet:'<div>{{d.YEAR}}.{{d.WEEK_NO}}({{d.WORK_DAY}}day)</div>'
			 },{
			    field: 'CREATE_NAME',
				title: '填写人',
				width:70,
				align:'left'
			},{
			    field: 'DEPT_NAME',
				title: '所属部门',
				width:90,
				align:'left'
			},{
			    field: 'SEND_TIME',
				title: '填写时间',
				width:140,
				align:'left'
			},{
			    field: 'WORK_CONTENT',
				title: '工作内容',
				align:'left',
				templet:function(row){
					var fileCount = row['FILE_COUNT'];
					if(fileCount>0){
						return "【附件】"+getContent(row['WORK_CONTENT']);
					}else{
						return getContent(row['WORK_CONTENT']);
					}
				}
			},{
			    field: '',
				title: '操作',
				align:'left',
				width:60,
				event:'detailPersonWeekly',
				templet:'<div><a href="javascript:;">查看</a></div>'
			}
			]],done:function(result){
				table.resize('personWeekly');
				$(".n1").text(result.totalRow);
			}});
		
	}
	
 	function detailPersonWeekly(data){
 		var weeklyId = data['WEEKLY_ID'];
 		if(!weeklyId){
 			weeklyId=data;
 		}
	    popup.layerShow({type:1,maxmin:true,anim:0,shadeClose:true,scrollbar:false,offset:'20px',area:['80%','80%'],url:'${ctxPath}/weekly',title:'周报明细',data:{isDiv:1,projectId:'${param.projectId}',weeklyId:weeklyId}});
 		
 	}
</script>