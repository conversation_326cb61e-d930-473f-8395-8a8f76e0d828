<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>库存管理</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="orderId" type="hidden" value="${param.orderId}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>库存管理</h5>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">订单号</span>
						 	<input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">合同号</span>
						 	<input class="form-control input-sm" name="contractNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">物料编号</span>
						 	<input class="form-control input-sm" name="goodsNo">
						 </div>
					  	<div class="input-group input-group-sm" style="width: 250px">
	          		    	<span class="input-group-addon">订单日期</span>	
                     		<input data-mars="CommonDao.currentMonthRange" type="text" data-laydate="{type:'date',range: '到'}" name="dateRange" class="form-control input-sm">
                    	 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="queryData()">查询</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="goodsTable"></table>
					</div>
				</div>
		</form>
		
		<script  type="text/html" id="toolbar">
			 <button type="button" class="btn btn-sm btn-info" lay-event="inStore">入库</button>
			 <button type="button" class="btn btn-sm btn-warning ml-5" lay-event="outStore">出库</button>
			 <button type="button" class="btn btn-sm btn-danger ml-5" lay-event="returnStore">退货</button>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
			var doNums = {};
			
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'GoodsDao.goodsList',
					id:'goodsTable',
					page:true,
					edit:'editFn',
					toolbar:'#toolbar',
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left',
						fixed:'left'
					 },{
	            	 	type: 'checkbox',
						align:'left',
						fixed:'left'
					 },{
						 field:'NAME',
						 title:'物料名称',
						 minWidth:100,
						 fixed:'left'
					 },{
						 field:'ORDER_NO',
						 title:'订单号',
						 event:'orderDetail',
						 style:'text-decoration:underline;'
					 },{
						 field:'CONTRACT_NAME',
						 title:'所属合同',
						 minWidth:150
					 },{
					     field: 'GOODS_NO',
						 title: '物料编号',
						 align:'center'
					 },{
						 field:'TOTAL_PRICE',
						 title:'含税总价'
					 },{
						 field:'DO_NUM',
						 title:'填写数量',
						 width:70,
						 edit:'text'
					 },{
						 field:'NUMBER',
						 width:70,
						 title:'采购数量'
					 },{
						 field:'IN_NUMBER',
						 title:'入库数量',
						 width:70,
						 templet:function(row){
							 return row['IN_NUMBER'] - row['RETURN_SUPPLIER_NUM'];
						 }
					 },{
						 field:'OUT_NUMBER',
						 title:'出库数量',
						 width:70,
						 templet:function(row){
							 return row['OUT_NUMBER'];
						 }
					 },{
						 field:'RETURN_NUMBER',
						 width:150,
						 title:'退货(总数/供应商/仓库)',
						 templet:'<div>{{d.RETURN_NUMBER}}/{{d.RETURN_SUPPLIER_NUM}}/{{d.RETURN_STORE_NUM}}</div>'
					 },{
						 field:'',
						 title:'实际出库数量',
						 width:70,
						 templet:function(row){
							 var val  = row['OUT_NUMBER']-row['RETURN_NUMBER'];
							 return val<0?0:val;
						 }
					 },{
						 field:'',
						 width:70,
						 title:'现有量',
						 templet:function(row){
							 return (row['IN_NUMBER'] - row['RETURN_SUPPLIER_NUM']) - (row['OUT_NUMBER'] - row['RETURN_NUMBER']);
						 }
					},{
						field:'REMARK',
						title:'备注',
						edit:'text'
					},{
					    field: 'CREATE_USER_NAME',
						title: '操作人',
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '操作时间',
						align:'center'
					}
				]],done:function(){
					
			  	}
			});
			}
			
			function editFn(obj){
			  var data  = obj.data;
			  var v1 = obj.value; 
			  var v2 = data.NUMBER;
			  if(v1>v2){
				  layer.msg('填写的数'+v1+'不能大于采购数'+v2);
				 
			  }
			}
			
			function orderDetail(data){
				popup.openTab({id:'orderDetail',title:'采购详情',url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{orderId:data.ORDER_ID,custId:data.CUST_ID}});
			}
			
			function queryData(){
				$("#dataMgrForm").queryData({id:'goodsTable'});
			}
		
			function inStore(data){
		 		var sum = data.length;
		 		if(sum <= 0){
		 			layer.msg('请选择物料。',{icon : 7, time : 1000});
		 			return;
		 		}
		 		var ids = [];
		 		
		 		for(var i = 0; i < sum; i ++){
					var doNum =  data[i]['DO_NUM'];
					if(doNum){
						ids.push(data[i].GOODS_ID);
						doNums[data[i].GOODS_ID] = doNum;
					}else{
						layer.msg('请填写数量');
						return false;
					}
		 		}
		 		if(ids.length <= 0){
		 			layer.msg('请选择。',{icon : 7, time : 1000});
		 			return;
		 		}
		 		var data= {goodsIds:ids.join(','),type:'put',orderId:'',title:'入库'};
		 		popup.layerShow({id:'storageEdit',area:['550px','400px'],shadeClose:false,title:'入库',url:'${ctxPath}/pages/erp/goods/storage-batch-edit.jsp',data:data});
		 		
			}
			
			function outStore(data){
		 		var sum = data.length;
		 		if(sum <= 0){
		 			layer.msg('请选择物料。',{icon : 7, time : 1000});
		 			return;
		 		}
		 		var ids = [];
		 		
		 		for(var i = 0; i < sum; i ++){
					var doNum =  data[i]['DO_NUM'];
					if(doNum){
						ids.push(data[i].GOODS_ID);
						doNums[data[i].GOODS_ID] = doNum;
					}else{
						layer.msg('请填写数量');
						return false;
					}
		 		}
		 		if(ids.length <= 0){
		 			layer.msg('请选择。',{icon : 7, time : 1000});
		 			return;
		 		}
		 		var data= {goodsIds:ids.join(','),type:'out',orderId:'',title:'出库'};
		 		popup.layerShow({id:'storageEdit',area:['550px','400px'],shadeClose:false,title:'出库',url:'${ctxPath}/pages/erp/goods/storage-batch-edit.jsp',data:data});
			}
			
			function returnStore(data){
		 		var sum = data.length;
		 		if(sum <= 0){
		 			layer.msg('请选择物料。',{icon : 7, time : 1000});
		 			return;
		 		}
		 		var ids = [];
		 		
		 		for(var i = 0; i < sum; i ++){
					var doNum =  data[i]['DO_NUM'];
					if(doNum){
						ids.push(data[i].GOODS_ID);
						doNums[data[i].GOODS_ID] = doNum;
					}else{
						layer.msg('请填写数量');
						return false;
					}
		 		}
		 		if(ids.length <= 0){
		 			layer.msg('请选择。',{icon : 7, time : 1000});
		 			return;
		 		}
		 		var data= {goodsIds:ids.join(','),type:'return',orderId:'',title:'退货'};
		 		popup.layerShow({id:'storageEdit',area:['550px','400px'],shadeClose:false,title:'退货',url:'${ctxPath}/pages/erp/goods/storage-batch-edit.jsp',data:data});
				
			}
			
			function reloadDataList(){
				$("#dataMgrForm").queryData({id:'goodsTable'});
			}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>

