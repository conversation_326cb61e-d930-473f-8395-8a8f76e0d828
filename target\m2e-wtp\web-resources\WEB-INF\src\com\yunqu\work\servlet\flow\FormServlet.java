package com.yunqu.work.servlet.flow;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/flow/form")
public class FormServlet extends AppBaseServlet {
	private static final long serialVersionUID = 4986188937002726883L;

	public void actionForIndex() {
		
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = new EasyRecord("yq_flow_form","form_id");
		try {
			model.setColumns(getJSONObject("form"));
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAdd(){
		EasyRecord model = new EasyRecord("yq_flow_form","form_id");
		try {
			model.setColumns(getJSONObject("form"));
			model.set("create_time", EasyDate.getCurrentDateString());
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
}
