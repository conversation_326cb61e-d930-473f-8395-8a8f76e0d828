<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的申请</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input type="hidden" name="flowCode" value="${flowCode}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          			 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">类型</span>
						 	<select class="form-control input-sm" id="dataType" name="dataType" onchange="queryData()">
						 		<option value="0">我接收的委托</option>
						 		<option value="1">我发起的委托</option>
						 	</select>
						 </div>
	          			 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">流程名称</span>
						 	<input class="form-control input-sm" name="flowName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <button class="btn btn-sm btn-info pull-right" onclick="trustConf();" type="button">委托规则设置</button>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		</form>
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="flowDetail" href="javascript:;">详情</a>
		  </script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'FlowDao.myTrustFlowList',
					id:'flowTable',
					page:true,
					limit:20,
					rowDoubleEvent:'flowDetail',
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						field:'',
						title:'操作',
						align:'center',
						width:70,
						templet:function(row){
							return renderTpl('bar',row);
						 }
					},{
						 field:'APPLY_NO',
						 title:'编号',
						 width:130,
						 templet:function(row){
							 return row['APPLY_NO'];
						 }
					 },{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:200
					 },{
						 field:'CREATE_BY_NAME',
						 title:'委托人',
						 width:100
					 },{
						 field:'TRUST_USER_NAME',
						 title:'被委托人',
						 width:100
					 },{
						 field:'TRUST_TIME',
						 title:'委托时间',
						 width:140
					 },{
						 field:'CHECK_RESULT',
						 title:'状态',
						 width:80,
						 templet:function(row){
							var val = row['CHECK_RESULT'];
							var dataType = $('#dataType').val();
							if(val=='0'&&dataType=='0'){
								return '<a  class="layui-btn layui-btn-xs" href="javascript:;" lay-event="approveFlow">审批</a>';
							}else if(val=='0'){
								return '待办';
							}else{
								return '已办';
							}
						}
					 },{
						field:'NODE_NAME',
						title:'节点名称',
						width:80
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function approveFlow(data){
			var flowCode = data['FLOW_CODE'];
			var readTime = data['READ_TIME'];
			var resultId = data['RESULT_ID'];
			if(readTime==''){
				ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=addReadTime",{resultId:resultId},function(result) {});
			}
			var json  = $.extend({},{businessId:data['APPLY_ID'],flowCode:flowCode,isApprovalFlag:'1','handle':'approval',resultId:resultId});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		function flowDetail(data){
			var flowCode = data['FLOW_CODE'];
			var readTime = data['READ_TIME'];
			var resultId = data['RESULT_ID'];
			if(readTime==''){
				ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=addReadTime",{resultId:resultId},function(result) {});
			}
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
		
		function trustConf(){
			popup.openTab({id:'trustConf',title:'委托设置',url:'/yq-work/pages/flow/config/trust-list.jsp',data:{}});
		}
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>