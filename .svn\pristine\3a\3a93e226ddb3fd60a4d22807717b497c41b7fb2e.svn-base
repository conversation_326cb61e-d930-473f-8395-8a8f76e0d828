<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>
		.layui-table-cell {
	        line-height: 20px !important;
	        vertical-align: middle;
	        height: auto;
	        padding: 6px 6px;
	        overflow: visible;
	        text-overflow: inherit;
	        white-space: normal;
	    }
		[data-week-no]{color: #999;cursor: pointer;}
		.weekActive{font-size: 16px;color: #e32424;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
					<div class="ibox-title">
				    	  <div class="input-group input-group-sm ml-5"  style="width: 220px">
							 <span class="input-group-addon">日期</span>	
							 <input data-mars="CommonDao.prevWeekly" data-mars-reload="false" type="text" data-laydate="{type:'date',range: '到'}" name="updateDate" class="form-control input-sm">
				        </div>
				        <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					</div>
					<div class="ibox-content">
						<table class="layui-hide mt-15" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'WeeklyDao.weeklyFeedback',
					id:'list',
					height:'full-100',
					cellMinWidth:80,
					limit:50,
					rowEvent:'openWeeklyDetail',
					lineStyle:'max-hegiht:100px;',
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'CREATOR',
						title: '填写人',
						align:'left',
						width:80,
						templet:function(row){
							return getUserName(row.CREATOR);
						}
					   }
					 ,{
					    field: 'TITLE',
						title: '周报标题',
						width:270,
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						templet:function(row){
							return row.TITLE;
						}
					}
					 ,{
					    field: 'ITEM_3',
						title: '反馈内容',
						align:'left',
						style:'cursor:pointer',
						templet:function(row){
							return row.ITEM_3;
						}
					}
					 ,{
					    field: 'RECEIVER',
						title: '审批人',
						align:'left',
						width:90,
						templet:function(row){
							return getUserName(row.RECEIVER);
						}
					 }
					]],done:function(result){
						$("[data-field='ITEM_3']").click(function(){
							var t = $(this);
							layer.msg(t.data("content"),{closeBtn:1,time:10000,offset:'30px'});
						});
					}}
				);
		}
	 }
		
	function openWeeklyDetail(data){
		var width=$(window).width();
		var fullFlag=false;
		var width ='850px';
		if(width<700){
			fullFlag=true;
		}
		if(width>1900){
			width='70%';
		}
		popup.layerShow({type:1,full:fullFlag,shade: 0.1,shadeClose:true,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/weekly',title:'周报详情',data:{isDiv:1,weeklyId:data.WEEKLY_ID}});
	}
		
    function queryData(){
    	 $("#searchForm").queryData({id:'list'});
    }
		
	$(function(){
		$('#searchForm').render({success:function(){
			list.init();
		}});
	});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>