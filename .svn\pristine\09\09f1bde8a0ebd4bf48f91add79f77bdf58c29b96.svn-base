package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;

public class MemosService extends AppBaseService implements Job{

	private static class Holder{
		private static MemosService service = new MemosService();
	}
	
	public static MemosService getService(){
		return Holder.service;
	}
	
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		this.run();
	}
	
	public  void run(){
		getLogger().info("执行同步用户任务....");
		try {
			String pwd="$2a$10$TnDLBC.ArSuKWQuNZtKKguSkhR4hPIaBCQKaRcdPu1qY.rwUCXMvK";//yunqu168@2024
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT t1.USER_ID,USERNAME,NIKE_NAME,SEX,MOBILE,EMAIL,BORN,USER_PWD,DATA3,USER_ACCT,DEPTS,ACCT_STATE from easi_user t1 INNER JOIN easi_user_login t2 on t1.USER_ID=t2.USER_ID ",null,new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
				 int acctState = jsonObject.getIntValue("ACCT_STATE");
				 String id = this.getMainQuery().queryForString("select id from memos.user where username = ?",jsonObject.getString("USER_ACCT"));
				 if(StringUtils.isBlank(id)&&acctState==0){
					 EasyRecord record = new EasyRecord("memos.user");
					 record.set("username", jsonObject.getString("USER_ACCT"));
					 record.set("nickname",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),jsonObject.getString("USER_ACCT")));
					 record.set("email", jsonObject.getString("EMAIL")+"");
					 record.set("role", "USER");
					 record.set("avatar_url","");
					 record.set("password_hash", pwd);
					 this.getMainQuery().save(record);
					 
				 }else if(StringUtils.notBlank(id)){
					 EasyRecord record = new EasyRecord("memos.user","id");
					 record.setPrimaryValues(id);
					 record.set("username", jsonObject.getString("USER_ACCT"));
					 record.set("nickname",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),jsonObject.getString("USER_ACCT")));
					 record.set("email", jsonObject.getString("EMAIL")+"");
					 record.set("password_hash",pwd);
					 if(acctState!=0){
						 record.set("row_status", "ARCHIVED");
					 }else{
						 record.set("row_status", "NORMAL");
					 }
					 try {
						this.getMainQuery().update(record);
					} catch (Exception e) {
						getLogger().error(e.getMessage(),e);
					}
				 }
			  }
			}
		} catch (SQLException e2) {
			this.getLogger().error(e2.getMessage(),e2);
		}finally {
			synMindoc();
		}
	}
	public  void synMindoc(){
		getLogger().info("执行同步用户任务synMindoc....");
		try {
			String pwd="zG9zqWSqYeYDN46b-F-amP7dIiwuRL66CSZFh4PcMM12O9dO9sDCQ5AioMhgUTaJxkMB$15$5bc2335a8843d775b92fc75d567daeac0022ceaa58c6981ab45c0c17$8651f92d976b08c65d60f094146f4cd00f798140cee83bc384305bf679bb8897";//yunqu168@2024
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT t1.USER_ID,USERNAME,NIKE_NAME,SEX,MOBILE,EMAIL,BORN,USER_PWD,DATA3,USER_ACCT,DEPTS,ACCT_STATE from easi_user t1 INNER JOIN easi_user_login t2 on t1.USER_ID=t2.USER_ID ",null,new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					int acctState = jsonObject.getIntValue("ACCT_STATE");
					String id = this.getMainQuery().queryForString("select member_id from mindoc_db.md_members where account = ?",jsonObject.getString("USER_ACCT"));
					if(StringUtils.isBlank(id)&&acctState==0){
						EasyRecord record = new EasyRecord("mindoc_db.md_members");
						record.set("account", jsonObject.getString("USER_ACCT"));
						record.set("real_name",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),jsonObject.getString("USER_ACCT")));
						record.set("email", jsonObject.getString("EMAIL")+"");
						record.set("role", 2);
						record.set("status",0);
						record.set("auth_method", "local");
						record.set("avatar", "/static/images/middle.gif");
						record.set("phone",jsonObject.getString("MOBILE"));
						record.set("password", pwd);
						record.set("create_time", EasyDate.getCurrentDateString());
						record.set("description","");
						this.getMainQuery().save(record);
						
					}else if(StringUtils.notBlank(id)){
						EasyRecord record = new EasyRecord("mindoc_db.md_members","member_id");
						record.setPrimaryValues(id);
						record.set("account", jsonObject.getString("USER_ACCT"));
						record.set("real_name",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),jsonObject.getString("USER_ACCT")));
						record.set("email", jsonObject.getString("EMAIL")+"");
						record.set("phone",jsonObject.getString("MOBILE"));
						record.set("password",pwd);
						if(acctState!=0){
							record.set("status", 1);
						}else{
							record.set("status",0);
						}
						try {
							this.getMainQuery().update(record);
						} catch (Exception e) {
							getLogger().error(e.getMessage(),e);
						}
					}
				}
			}
		} catch (SQLException e2) {
			this.getLogger().error(e2.getMessage(),e2);
		}
	}
}
