<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>周报详情</title>
	<style>
		.c-title{color: red;}
		#editForm{
			margin-bottom: 100px;
			height: 100%;
		}
		#editForm .userName{
		    font-size: 1em;
		    color: rgba(0,0,0,.87);
		    font-weight: 500;
		}
		#editForm .createTime{
		    display: inline-block;
		    margin-left: .5em;
		    color: rgba(0,0,0,.4);
		    font-size: .875em;
		}
		#editForm td{text-align: left;}
		#editForm .t1 td{line-height: 16px;}
		.avatar{
			float: left;
			height: 3em;
			width: 3em;
			display: block;
		    margin: .2em 0 0;
		}
    	.avatar img{
		    display: inline-block;
		    height: 100%;
		    width: 100%;
		    border-radius: 100%;
		    overflow: hidden;
		    font-size: inherit;
		    vertical-align: middle;
		    -webkit-box-shadow: 0 0 1px rgba(0,0,0,.3);
		    box-shadow: 0 0 1px rgba(0,0,0,.3);
		}
		.b_content{
			margin: 10px 15px;
		}
		.d_content{
			margin-left: 4em;
			padding: 6px 0px;
		}
		.layui-elem-quote{line-height: 1.7;padding: 8px;font-size: 13px;}
		.color999{color: #999;margin-right: 7px;}
		.w-70{width: 70px!important;min-width:70px!important;}
		#taskDesc img{max-width: 700px;}
		#comment{display: block!important;}
		#editForm legend{width: inherit;border-bottom:none;margin-bottom: 0px;}
		[data-fn]{font-size: 14px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editForm" class="ibox-index" data-mars="WeeklyDao.record" data-text-model="false" autocomplete="off" data-mars-prefix="weekly.">
	     		   <input type="hidden" id="weeklyId" value="${param.weeklyId}" name="weekly.WEEKLY_ID"/>
	     		   <input type="hidden" name="fkId" value="${param.weeklyId}"/>
	     		   <input type="hidden" name="weeklyId" value="${param.weeklyId}"/>
					<table class="table table-edit table-vzebra t1">
				            <tr>
			                    <td colspan="2">
			                   		 <span style="font-size: 16px;font-weight: bold;" name="weekly.TITLE"></span>
			                    </td>
				            </tr>
				            <tr >
				            	<td style="width: 50%">
				            		 <i class="glyphicon glyphicon-time"></i> 
				            		 <span data-fn="getUserName" name="weekly.CREATOR"></span>
				            		 <span class="color999">创建于</span>
				            		 <span class="color999" name="weekly.CREATE_TIME"></span>
				            	</td>
				            	<td>
				            		<i class="glyphicon glyphicon-time"></i> 
				            		<span data-fn="getUserName" name="weekly.UPDATE_BY"></span>
		                    		<span class="color999">更新于</span>
		                    		<span name="weekly.UPDATE_TIME"></span>
			                    </td>
				            </tr>
			                <tr>
			              	  <td>
			                    	<i class="glyphicon glyphicon-hand-right"></i>
			                    	<span class="color999">接收人</span>
		                    		<span name="weekly.RECEIVER_NAME"></span>
			                    	
			                    </td>
			                	<td colspan="2">
			                    	<i class="glyphicon glyphicon-flag"></i>
			                		<span class="color999 ml-10">抄送人</span>
			                    	<span name="weekly.CC_NAMES" id="mailtos"></span>
			                	</td>
			                </tr>
			                <tr>
			              	    <td>
			                    	<i class="fa fa-adjust"></i>
			                    	<span class="color999">工作饱和度</span>
		                    		<span style="color: red;font-size: 20px;" name="weekly.ITEM_5"></span>
			                    </td>
			              	    <td>
			                    	<i class="fa fa-adjust"></i>
			                    	<span class="color999">工作量/天</span>
		                    		<span style="color: red;font-size: 20px;" id="workDay">--</span>
			                    </td>
			                </tr>
  					  </table>
  					  <table class="table table-edit" style="margin-bottom: 0px;">
			                <tr>
			                	<td colspan="2">
			                		<fieldset class="content-title">
 											<legend>周报详情</legend>
					  			    </fieldset>
			                	</td>
			                </tr>
				            <tr>
				               	<td colspan="2">
				               		<div id="weeklyDesc" style="overflow:auto;">
				               			<fieldset class="layui-elem-field">
										  <legend>引导语</legend>
										  <div class="layui-field-box">
											  <div data-fn="contentFn" name="weekly.ITEM_3"></div>
										  </div>
										</fieldset>
										<br>
				               			<fieldset class="layui-elem-field" data-mars="WeeklyDao.weeklyProject">
										  <legend>本周工作总结</legend>
										  <div class="layui-field-box">
											  <div id="nowWeeklyContent" data-fn="weeklyContentFn" name="weekly.ITEM_1"></div>
										  </div>
										</fieldset>
										<br>
				               			<fieldset class="layui-elem-field">
										  <legend>下周工作计划</legend>
										  <div class="layui-field-box">
											  <div id="nextWeeklyContent" data-fn="contentFn" name="weekly.ITEM_2"></div>
										  </div>
										</fieldset>
										<br>
				               			<fieldset class="layui-elem-field">
										  <legend>备注</legend>
										  <div class="layui-field-box">
											  <div data-fn="contentFn" name="weekly.ITEM_4"></div>
										  </div>
										</fieldset>
				               		</div>
				               	</td>
				            </tr>
  					  </table>
	               	 <div style="display: inline-block;" data-template="template-files" data-mars="FileDao.fileList"></div>
  					 <EasyTag:hasRole roleId="DEV_MGR">
  					  <fieldset class="content-title">
 							<legend>本周表现评分</legend>
					  </fieldset>
					    <p class="mb-15 mt-15">
					    	<label class="radio radio-info radio-inline ml-30">
                    			<input type="radio" value="3" name="weekly.GRADE"> <span>A(优秀)</span>
	                    	</label>
		            		<label class="radio radio-info radio-inline">
                    			<input type="radio" value="2" name="weekly.GRADE"> <span>B(较好)</span>
	                    	</label>
		            		<label class="radio radio-info radio-inline">
                    			<input type="radio" value="1" name="weekly.GRADE"> <span>C(及格)</span>
	                    	</label>
		            		<label class="radio radio-info radio-inline">
                    			<input type="radio" value="0" name="weekly.GRADE"> <span>D(不合格)</span>
	                    	</label>
	                    	<span style="color: red" name="weekly.GRADE_LOG" data-fn="gradeFn" class="pull-right">暂未评分</span>
					    </p>
  					  </EasyTag:hasRole>
  					  <fieldset class="content-title">
 							<legend>意见</legend>
					  </fieldset>
  					  <table class="table table-edit table-vzebra"  style="width:100%">
				            <tr>
				               	<td>
		                           <textarea placeholder="在此输入评论内容" style="height: 80px;width:100%;display: block;" class="form-control input-sm" name="comment" id="comment"></textarea>
				               	</td>
				            </tr>
				            <tr>
				            	<td>
			            			<div class="pull-left">
				            			<label class="checkbox checkbox-default radio-inline" style="margin-top: 0px;">
				                            <input type="checkbox" checked="checked" name="commentNoticeType" value="wx"/> <span>微信通知</span> 
						               	</label>
						               	<label class="checkbox checkbox-default radio-inline mr-30">
				                            <input type="checkbox" name="commentNoticeType" checked="checked" value="email"/> <span>邮件通知</span>  
						               	</label>
			            			</div>
		                            <button type="button" style="box-shadow: 0 4px 8px 0 rgba(31,93,234,.35);" class="btn btn-primary btn-sm  pull-right" onclick="addComment()"> <i class="glyphicon glyphicon-send"></i> 发布评论 </button>
				            	</td>
				            </tr>
  					  </table>
                      <div class="layui-tab layui-tab-brief">
						  <ul class="layui-tab-title">
						    <li class="layui-this">评论动态</li>
						    <li>浏览动态</li>
						  </ul>
						  <div class="layui-tab-content">
						    <div class="layui-tab-item layui-show">
						   	 	<div id="history"></div>
						    </div>
						    <div class="layui-tab-item">
						 	   <table id="viewTable"></table>
						    </div>
						  </div>
					  </div> 
					  
				   <div class="layer-foot text-c" <c:if test="${param.isDiv==0}">style="position: fixed;"</c:if>>
			    	  <button type="button" class="btn btn-info btn-sm ml-15"  onclick="nearWeekly('prev')"> 上一周 </button>
				      <button type="button" class="btn btn-info btn-sm ml-15" onclick="nearWeekly('next');"> 下一周 </button>
				  </div>
	  	  </form>
	  	  <script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a></div>
			{{/for}}
		  </script>
  		 <script id="template-weeklyContent" type="text/x-jsrender">
			{{for data}}
				<blockquote class="layui-elem-quote layui-quote-nm"><b>{{:PROJECT_NAME}}</b><br>
					 <div class="c-content">{{call:WORK_CONTENT fn='contentFn'}}</div>
					 <span class="c-title">工时</span> {{:WORK_DAY}}/天 <br>
					{{if PLAN_FINISH_DATE}}<span class="c-title">计划完成日期</span>{{:PLAN_FINISH_DATE}}<br>{{/if}}
					{{if PROGRESS_RATE}}<span class="c-title">进度</span>{{:PROGRESS_RATE}}%<br>{{/if}}
				</blockquote>
			{{/for}}
		</script>
  		 <script id="template-nextWeeklyContent" type="text/x-jsrender">
			{{for data}}
				<blockquote class="layui-elem-quote layui-quote-nm"><b>{{:PROJECT_NAME}}</b><br>
					<div class="c-content">{{call:PLAN_WORK_CONTENT fn='contentFn'}}</div>
				</blockquote>
			{{/for}}
		</script>
  		 <script id="template-comments" type="text/x-jsrender">
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{call:CREATOR fn='getUserName'}}</span> <span class="createTime">{{:CREATE_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{:CONTENT}}</div>{{if #parent.parent.data.currentUserId == CREATOR}}<a style class="btn btn-xs btn-link hover ib" href="javascript:void(0)" onclick="delComment('{{:COMMENT_ID}}',$(this))">删除</a>	{{/if}}
						</div>						
					</div>
				</div>
			{{/for}}
			{{if data.length==0}}
				<p class="text-center mt-20">暂无评论</p>
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
	    layui.use('element',function(){
		  var element=layui.element;
	    });
	
		var weeklyDetail = {
			weeklyId:'${param.weeklyId}'
		};
	    
		var weeklyId='${param.weeklyId}';
		
		var weeklyRecord;
		function initFormData(){
			$("#editForm").render({success:function(result){
				var record = result['WeeklyDao.record'];
				var data = record.data;
				weeklyRecord = data;
				
				if(weeklyRecord.WRITE_MODE=='2'){
					var weeklyProject = result['WeeklyDao.weeklyProject'];
					var tpl = $.templates('#template-weeklyContent');
					var html = tpl.render(weeklyProject);
					$('#nowWeeklyContent').html(html);
					
					var tpl2 = $.templates('#template-nextWeeklyContent');
					var html2 = tpl2.render(weeklyProject);
					$('#nextWeeklyContent').html(html2);
					
					var sumWorkDay = 0;
					var list = weeklyProject.data;
					for(var index in list){
						sumWorkDay = sumWorkDay + Number(list[index]['WORK_DAY']);
					}
					$('#workDay').html(sumWorkDay);
				}
			    loadCommentHistory();
				lookLog();
			}});  
		}
		$(function(){
			$("[name='weekly.GRADE']").change(function(){
				var t =$(this);
				var name=getUserName(getCurrentUserId());
				ajax.remoteCall("${ctxPath}/servlet/weekly?action=doGrade",{weeklyId:weeklyId,grade:t.val(),name:name},function(result) { 
					layer.msg(result.msg);
					var data=result.data;
					$("[name='weekly.GRADE_LOG']").html(gradeFn(JSON.stringify(data)));
				});
			});
			  initFormData();
			
		});
		function contentFn(val){
			if(val){
				return getContent(val);
			}else{
				return "暂无";
			}
		}
		function gradeFn(val){
			if(val){
				var data=eval('(' + val + ')'); 
				return data['name']+"于"+data['time']+"提交评分";
			}else{
				return '暂未评分';
			}
		}
		
		function weeklyContentFn(val,row,el){
			if(row.WRITE_MODE=='2'){
				return contentFn(val);
			}else{
				return contentFn(val);
			}
		}
		
		function nearWeekly(type){
			var width=$(window).width();
			var fullFlag=false;
			var width ='850px';
			if(width<700){
				fullFlag=true;
			}
			if(width>1900){
				width='70%';
			}
			var weeklyId = $('#weeklyId').val();
			ajax.remoteCall("${ctxPath}/servlet/weekly?action=getNearWeekly",{weeklyId:weeklyId,type:type},function(result) { 
				weeklyId = result.data;
				if(weeklyId==''){
					layer.msg('没有了',{icon:7});
					return;
				}
				setTimeout(function(){
					popup.layerShow({id:'weeklyDetail',type:1,full:fullFlag,shade: 0.1,shadeClose:true,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/weekly',title:'周报详情',data:{isDiv:1,weeklyId:weeklyId}});
				},200);
				layer.closeAll();
			});
			
		}
		
		function loadCommentHistory(){
			ajax.remoteCall("${ctxPath}/webcall?action=CommonDao.commentsList",{fkId:weeklyId},function(result) { 
				result['currentUserId']=getCurrentUserId();
				var html=renderTpl("template-comments",result);
				$("#history").html(html);
			});
		}
		function lookLog(){
			loadLookLog({tableId:'viewTable',formId:'editForm',fkId:weeklyDetail.weeklyId})
		}
		var addComment = function() {
			if($("#comment").val()){
			   var commentNoticeType ='';
			 	$.each($("input[name='commentNoticeType']:checked"),function(){
	                commentNoticeType += $(this).val()+",";
	            });
				var commentParams = {content:$("#comment").val(),fkId:weeklyId,commentNoticeType:commentNoticeType,source:'weekly'};
				var data=$.extend({},weeklyRecord,commentParams);
				ajax.remoteCall("${ctxPath}/servlet/comment?action=add",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							list.recentList();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		function delComment(id,obj){
			ajax.remoteCall("${ctxPath}/servlet/comment?action=del",{commentId:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(obj).parents(".b_content").remove();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<c:choose>
	<c:when test="${param.isDiv==0}">
		<%@ include file="/pages/common/layout_form.jsp" %>
	</c:when>
	<c:otherwise>
		<%@ include file="/pages/common/layout_div.jsp" %>
	</c:otherwise>
</c:choose>
