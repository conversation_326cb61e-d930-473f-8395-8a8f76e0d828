package com.yunqu.work.dao.devops;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.AppModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="AppDao")
public class AppDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		AppModel model=new AppModel();
		model.setColumns(getParam("app"));
		if(StringUtils.notBlank(model.getGroupId())){
			LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getGroupId());
		}
		return queryForRecord(model);
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from YQ_APP_GROUP where 1=1");
		sql.appendLike(param.getString("appName"),"and app_name like ?");
		sql.append("order by last_version_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="appList",type=Types.LIST)
	public JSONObject appList(){
		EasySQL sql=getEasySQL("select * from yq_ops_version where 1=1");
		sql.append(param.getString("groupId"),"and group_id = ?");
		sql.appendLike(param.getString("versionName"),"and version_name like ?");
		sql.append(param.getString("projectId"),"and project_id = ?");
		sql.append("order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
