<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报首页</title>
	<style>
		.stat-index span{font-size: 22px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="weeklyForm" data-page-hide="true">
			  <input name="source" value="manger" type="hidden">
		      <div class="layui-row layui-col-space16 stat-index" data-mars="WeeklyDao.weekyIndexStat">
				  <div class="layui-col-md2">
				    <div class="layui-card">
				        <div class="layui-card-header">我本年参与项目数</div>
				        <div class="layui-card-body">
				        	<span id="yearSelfCount"></span>
				        </div>
				      </div>
				  </div>
				  <div class="layui-col-md2">
				     <div class="layui-card">
				        <div class="layui-card-header">近1月参与项目数</div>
				        <div class="layui-card-body">
				        		<span id="month1SelfCount"></span>
				        </div>
				      </div>
				  </div>
				  <div class="layui-col-md2">
				    <div class="layui-card">
				        <div class="layui-card-header">近3月参与项目数</div>
				        <div class="layui-card-body">
				        		<span id="month3SelfCount"></span>
				        </div>
				      </div>
				  </div>
				  <div class="layui-col-md2">
				    <div class="layui-card">
				        <div class="layui-card-header">部门本年项目数</div>
				        <div class="layui-card-body">
				        		<span id="yearDeptCount"></span>
				        </div>
				      </div>
				  </div>
				  <div class="layui-col-md2">
				    <div class="layui-card">
				        <div class="layui-card-header">部门近1月项目数</div>
				        <div class="layui-card-body">
				        		<span id="month1DeptCount"></span>
				        </div>
				      </div>
				  </div>
				  <div class="layui-col-md2">
				    <div class="layui-card">
				        <div class="layui-card-header">部门近3月项目数</div>
				        <div class="layui-card-body">
				        		<span id="month3DeptCount"></span>
				        </div>
				      </div>
				  </div>
		      </div>
		      <div class="layui-row layui-col-space16">
				  <div class="layui-col-md12">
				       <div class="layui-card">
				        <div class="layui-card-header">我本年项目投入情况</div>
				        <div class="layui-card-body">
					        <table class="layui-hide" id="myWeeklyProjectStat"></table> 
				        </div>
				      </div>
				  </div>
				  <div class="layui-col-md12">
				       <div class="layui-card">
				        <div class="layui-card-header">近3个月项目投入情况</div>
				        <div class="layui-card-body">
					        <table class="layui-hide" id="myWeeklyMonthProjectStat"></table> 
				        </div>
				      </div>
				  </div>
				  <div class="layui-col-md12">
				    <div class="layui-card">
				        <div class="layui-card-header">部门本年项目投入情况</div>
				        <div class="layui-card-body">
						        <table class="layui-hide" id="weeklyProjectStat"></table> 
				        </div>
				      </div>
				  </div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		var list = {
			weeklyProjectStat:function(){
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.deptYearProjectWeeklyStat',
					id:'weeklyProjectStat',
					cellMinWidth:100,
					limits:[10,30,50,100,200],
					title:'部门本年项目投入情况',
					limit:10,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'PROJECT_NO',
						title: '项目编号',
						align:'left',
						width:110
					 },{
						 field:'PROJECT_NAME',
						 title:'项目名称',
						 minWidth:250,
						 event:'projectDetail'
					 },{
					    field: 'BEGIN_DATE',
						title: '开工日期',
						align:'left',
						width:90
					 },{
					    field: 'PROJECT_PO_NAME',
						title: '项目经理',
						align:'left',
						width:90
					 },{
					    field: 'PO_NAME',
						title: '开发负责人',
						align:'left',
						width:90
					 },{
					    field: 'COUNT',
						title: '投入人周次',
						align:'left',
						width:90
					 },{
					    field: 'USER_NAMES',
						title: '部门参与人',
						align:'left',
						width:160
					 },{
					    field: 'PEOPLE_COUNT',
						title: '部门参与人数',
						align:'left',
						width:90
					 },{
					    field: 'WORK_DAY',
						title: '部门总消耗工时/天',
						align:'left',
						width:120,
						templet:function(row){
							return Number(row['WORK_DAY']).toFixed(1);
						}
					 },{
					    field: 'WORK_PEOPLE_COUNT',
						title: '项目总投入人',
						align:'left',
						width:90
					 }
					]],done:function(result){
						
					}}
				);
			},
			myWeeklyProjectStat:function(){
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.myYearProjectWeeklyStat',
					id:'myWeeklyProjectStat',
					cellMinWidth:100,
					limits:[10,50,100,200],
					title:'我本年项目投入情况',
					limit:10,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'PROJECT_NO',
						title: '项目编号',
						align:'left',
						width:110
					 },{
						 field:'PROJECT_NAME',
						 title:'项目名称',
						 minWidth:250,
						 event:'projectDetail'
					 },{
					    field: 'BEGIN_DATE',
						title: '开工日期',
						align:'left',
						width:90
					 },{
					    field: 'PROJECT_PO_NAME',
						title: '项目经理',
						align:'left',
						width:90
					 },{
					    field: 'PO_NAME',
						title: '开发负责人',
						align:'left',
						width:90
					 },{
					    field: 'COUNT',
						title: '投入周次',
						align:'left',
						width:90
					 },{
					    field: 'WORK_DAY',
						title: '总投入工时/天',
						align:'left',
						width:120,
						templet:function(row){
							return Number(row['WORK_DAY']).toFixed(1);
						}
					 }
					]],done:function(result){
						
					}}
				);
			},
			myWeeklyMonthProjectStat:function(){
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.myYearProjectWeeklyStat',
					id:'myWeeklyMonthProjectStat',
					cellMinWidth:100,
					data:{date:90},
					limits:[10,50,100,200],
					title:'3月内项目投入情况',
					limit:10,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'PROJECT_NO',
						title: '项目编号',
						align:'left',
						width:110
					 },{
						 field:'PROJECT_NAME',
						 title:'项目名称',
						 minWidth:250,
						 event:'projectDetail'
					 },{
					    field: 'BEGIN_DATE',
						title: '开工日期',
						align:'left',
						width:90
					 },{
					    field: 'PROJECT_PO_NAME',
						title: '项目经理',
						align:'left',
						width:90
					 },{
					    field: 'PO_NAME',
						title: '开发负责人',
						align:'left',
						width:90
					 },{
					    field: 'COUNT',
						title: '投入周次',
						align:'left',
						width:90
					 },{
					    field: 'WORK_DAY',
						title: '总投入工时/天',
						align:'left',
						width:120,
						templet:function(row){
							return Number(row['WORK_DAY']).toFixed(1);
						}
					 }
					]],done:function(result){
						
					}}
				);
			}
		}
		
		
		$(function(){
			$("#weeklyForm").render({success:function(){
				list.myWeeklyProjectStat();
				list.myWeeklyMonthProjectStat();
				list.weeklyProjectStat();
			}});
		});
		
		
		function projectDetail(data){
			var projectId = data['PROJECT_ID'];
			projectBoard(projectId);
		}
		
		function contentFn(val){
			if(val){
				return getContent(val);
			}else{
				return "暂无";
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>