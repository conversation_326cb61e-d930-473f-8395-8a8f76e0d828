<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>任务分组</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="TaskDao.groupInfo" autocomplete="off" data-mars-prefix="group.">
     		   <input type="hidden" value="${param.groupId}" name="groupId"/>
     		   <input type="hidden" value="${param.pGroupId}" name="group.P_GROUP_ID"/>
     		   <input type="hidden" value="${param.groupId}" name="group.GROUP_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td class="required">分组名称</td>
		                    <td><input value="" data-rules="required"  type="text" name="group.GROUP_NAME" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td style="width: 70px" class="required">排序</td>
		                    <td><input data-rules="required"  type="text" name="group.ORDER_INDEX" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td>备注</td>
		                    <td>
			                   	 <textarea name="group.REMARK" style="height: 80px" class="form-control input-sm"></textarea>
		                    </td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				 		  <c:if test="${!empty param.groupId}">
						    	  <button type="button" class="btn btn-info btn-sm"  onclick="Edit.del()"> 删除 </button>
				 		  </c:if>
				    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("Edit");
		Edit.groupId='${param.groupId}';
		
		$(function(){
			$("#editForm").render({success:function(){
				
			}});  
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.groupId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			data['group.PROJECT_ID'] = projectId;
			ajax.remoteCall("${ctxPath}/servlet/task?action=addGroup",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						location.reload();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/task?action=updateGroup",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						location.reload();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		};
		Edit.del=function(data){
			layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/userNav?action=delGroup",{'groupId':Edit.groupId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							location.reload();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>