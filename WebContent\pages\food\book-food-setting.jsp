<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>预约</title>
	<style>
		#editForm p{line-height: 30px;}
		#data-container{padding: 5px 30px;}
		#editForm .checkbox.checkbox-inline{vertical-align: text-top;}
		#editForm .a-r{color: #1E9FFF;}
		#editForm .a-b{color: red;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm"  autocomplete="off">
			<div id="data-container">
			
			</div>
		   <div class="layer-foot text-c">
		    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm()"> 确认预定 </button>
			      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
		  </div>
  		</form>
  		<script type="text/x-jsrender" id="book-food-template">
			{{for data}}
				<p class="w_{{:WEEK}} d_{{:YMD}}">
					<label class="checkbox checkbox-info checkbox-inline"><input type="checkbox" value="{{:YMD}}" name="dates" {{if STATE ==2}} disabled="disabled"{{/if}} {{if STATE ==1}}checked="true" disabled="disabled"{{/if}}/><span></span></label> 
					<input type="hidden" name="{{:YMD}}_week" value="{{:WEEK}}"/>
 					<input type="hidden" name="{{:YMD}}_ym" value="{{:YM}}"/> <span>{{:YMD}} 
 				    {{call:WEEK fn='getWeek'}}</span>  
					{{if STATE ==1}}<span class="ml-15 label label-success label-outline">已预定</span>  <button class="ml-15 btn btn-warning btn-xs btn-outline" type="button" onclick="Edit.cancel('{{:ID}}')">取消</button>{{/if}} 
					{{if STATE ==2}}<span class="ml-15 label label-info label-outline">已不点</span>  <a class="ml-15 a-r" href="javascript:void(0)" onclick="sureBookPlan('{{:ID}}');">我要预定</a>{{/if}} 
					{{if STATE==null}}<a class="ml-20 a-r" href="javascript:void(0)" onclick="bookPlan('{{:YMD}}');">我要预定</a>  <a class="ml-10 a-b" href="javascript:void(0)" onclick="notBookPlan('{{:YMD}}');">不点</a>{{/if}}
				</p>
				
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		var weeks = ["星期日","星期一","星期二","星期三","星期四","星期五","星期六"];
		$(function(){
			reloadBookData();
		});
		function reloadBookData(){
			ajax.remoteCall("${ctxPath}/webcall?action=FoodDao.bookFoodPlan",{},function(result) { 
				var data=result.data;
			    var planDays=result.planDays;
				var tdata=$.extend([],getNewJson(planDays),getNewJson(data));
				var tmp = $.templates("#book-food-template");
				$("#data-container").html(tmp.render({data:getNewJson2(tdata)}));
				
				$(".w_7").after("<hr>");
				
				$(".w_7").css("display",result.week6);
				$(".w_1").css("display",result.week7);
			},{loading:false});
		}
		function getNewJson(data){
			var newJson=[];
			for(var index in data){
				newJson[data[index].YMD]=data[index];
			}
			return newJson;
		}
		function getNewJson2(data){
			var newJson=[];
			for(var index in data){
				newJson.push(data[index]);
			}
			return newJson;
		}
		function getWeek(val){
			 if(val){
				return weeks[val-1];
			}else{
				return '';
			}
		}
		Edit.ajaxSubmitForm = function(){
			var data = form.getJSONObject("#editForm");
			if(data.dates&&data.dates.length>0){
				ajax.remoteCall("${ctxPath}/servlet/food?action=bookPlan",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							reloadBookData();
							popup.layerClose('#editForm');
							// $.get("${ctxPath}/servlet/food?action=dsapi",function(result){
							//	layer.alert(result.content+"<br>"+result.note+"<br>"+result.translation,{btn:['打赏','关闭'],btnAlign:'c',offset:'rb',time:30000,shade:0,title:false,closeBtn:0},function(){
							//		layer.alert("<img style='width:200px;height:200px' src='${ctxPath}/static/images/wx.jpg'/>",{offset:'rb',time:'30000',shadeClose:true,title:false,closeBtn:0,btn:['关闭'],btnAlign:'c'});
							//	});
							//});
						    var msg=result.msg;
							if(msg.indexOf(".")>-1){
								var userId=getCurrentUserId();
								layer.open({type:1,title:msg,area:['300px','390px'],shadeClose:false,closeBtn:0,btn:['我已绑定','稍后再试'],content:'<div style=""><img style="width:250px;hight:250px;margin-left:25px;" src="http://qr.liantu.com/api.php?text=http://work.yunqu-info.cn/yq-work/api/getAuthorize?userId='+userId+'"/></div><br><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;微信扫码关注,再次扫码绑定后关闭弹出窗</p>'});
							}
						 });
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}else{
				layer.msg("未选择预定日期!",{offset:'20px',icon:7});
			}
		}
		Edit.cancel = function(id){
			ajax.remoteCall("${ctxPath}/servlet/food?action=cannelBook",{id:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadBookData();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		var notBookPlan = function(ymd){
			$(".d_"+ymd).find("input[name='dates']").prop("checked",false);
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/food?action=notBookPlan",$.extend(data,{ymd:ymd}),function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadBookData();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function sureBookPlan(id){
			ajax.remoteCall("${ctxPath}/servlet/food?action=sureBookPlan",{id:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadBookData();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function bookPlan(ymd){
			$(".d_"+ymd).find("input[name='dates']").prop("checked",true);
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>