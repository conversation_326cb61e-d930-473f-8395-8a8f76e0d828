package com.yunqu.work.servlet.crm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;

@WebServlet("/servlet/incomeConfirm/*")
public class IncomeConfirmServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;


    public EasyRecord getConfirmModel(String prefix) {
        EasyRecord confirmModel = new EasyRecord("yq_contract_income_confirm", "CONFIRM_ID");
        confirmModel.setColumns(getJSONObject(prefix));
        return confirmModel;
    }

    public EasyResult actionForAdd() {
        EasyRecord confirmModel = getConfirmModel("confirm");
        confirmModel.setPrimaryValues(RandomKit.uuid().toUpperCase());
        confirmModel.set("CREATE_TIME", EasyDate.getCurrentDateString());
        confirmModel.set("CREATOR", getUserId());
        confirmModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        confirmModel.set("UPDATE_BY", getUserId());
        try {
            this.getQuery().save(confirmModel);
        } catch (SQLException e) {
            this.error(null, e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdate() {
        EasyRecord confirmModel = getConfirmModel("confirm");
        confirmModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        confirmModel.set("UPDATE_BY", getUserId());
        try {
            this.getQuery().update(confirmModel);
        } catch (SQLException e) {
            this.error(null, e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForDel() {
        String confirmId = getJsonPara("confirmId");
        try {
            this.getQuery().executeUpdate("delete from yq_contract_income_confirm where CONFIRM_ID = ?", confirmId);
        } catch (SQLException e) {
            e.printStackTrace();
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForBatchDel() {
        JSONArray confirmArray = getJSONArray();
        for (int i = 0; i < confirmArray.size(); i++) {
            JSONObject confirmObject = confirmArray.getJSONObject(i);
            try {
                this.getQuery().executeUpdate("delete from yq_contract_income_confirm where CONFIRM_ID = ?", confirmObject.getString("CONFIRM_ID"));
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }
}
