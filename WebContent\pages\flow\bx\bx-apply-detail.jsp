<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.layui-card:last-child{margin-top: -10px;}
		.layui-card-header{border-bottom: none;}
		.layui-table td, .layui-table th{padding: 8px;}
		.layui-tab-content{background-color: #fff;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.gray-bg{background-color: #e8edf7;}
		.layui-icon{font-size: 12px;}
		.updateHzAmount,.eInvoiceBtn,.isGetPaper{display: none;}
		
		.titleInfo td{
			height:24px!important;line-height: 20px!important;padding: 3px 4px!important;font-size: 14px!important;color: #444444;
		}
		
		.approveResult td,.approveResult th{
			border:none;
			border-bottom: 1px dashed #ccc!important;
			padding: 10px 6px;
		}
		
		@media print{
			.bxItems .layui-table td,.bxItems .layui-table th{line-height: 14px;padding: 2px;}
			.layui-table td, .layui-table th{font-size: 12px;line-height: 26px;color: #444;}
			.table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td{
			    line-height: 20px;
			    padding: 5px;
			    height: 20px;
			}
			.approve-table td,.approve-table th{
				font-size: 50%!important;
			}
		
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			 <div class="layui-row ibox-content" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				 		<div class="flow-approval-btn">
				  			<EasyTag:res resId="HZ_BX_MONEY">
								<button class="btn btn-info btn-sm hz-bx-money" onclick="$('.updateHzAmount').show();$('.hzAmountVal').hide();" type="button">核准金额</button>
				  			</EasyTag:res>
				 		</div>
				 		<div class="flow-btn">
							<button class="btn btn-info btn-sm btn-outline eInvoiceBtn" onclick="eInvoiceLook(this)" type="button">电子发票</button>
				  			<EasyTag:res resId="HZ_BX_MONEY">
								<button class="btn btn-success btn-sm isGetPaper ml-10" onclick="updatePaperState();" type="button">纸质单据签收</button>
				  			</EasyTag:res>
				 		</div>
				    	<table class="table table-edit table-vzebra titleInfo">
					  		<tr>
					  			<td style="width: 100px;">当前处理人：</td>
					  			<td style="width: 38%;">
					  				<span name="apply.checkName"></span>
					  			</td>
					  			<td style="width: 100px;">申请时间：</td>
					  			<td>
					  				<span name="apply.applyTime"></span>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td style="width: 100px;">当前状态：</td>
					  			<td>
					  				<span name="apply.nodeName"></span>
					  				<span class="paperState hidden-print ml-10"></span>
					  			</td>
					  			<td style="width: 100px;">单号：</td>
					  			<td>
					  				<span name="apply.applyNo"></span>
					  			</td>
					  		</tr>
					  </table>
					  <table class="table table-vzebra flow-table detailInfo mt-10">
					  		<tr>
					  			<td style="width: 100px;">申请人</td>
					  			<td style="width: 38%;">
					  				<span name="apply.applyName"></span>
					  				<span name="apply.applyStaffNo"></span>
					  			</td>
					  			<td style="width: 100px;">部门</td>
					  			<td>
					  				<span name="business.BX_DEPT_NAME"></span>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>说明</td>
					  			<td colspan="3">
									<span name="apply.applyRemark"></span>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>付款方式</td>
					  			<td colspan="3">
					  				<span name="business.PAY_TYPE"  data-fn="showContactUnit"></span>
					  			</td>
					  		</tr>
					  		<tr class="showContactUnit">
					  			<td>公司名</td>
					  			<td colspan="3">
					  				<span name="business.CONTACT_UNIT"></span>
					  			</td>
					  		</tr>
					  		<tr class="showContactUnit">
					  			<td>银行名</td>
					  			<td>
					  				<span name="business.CONTACT_BANK"></span>
					  			</td>
					  			<td>银行账号</td>
					  			<td>
					  				<span name="business.CONTACT_BANK_NO" data-fn="bankCardFormat" class="ml-5"></span>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>费用总计</td>
					  			<td>
					  				<span name="business.HZ_MONEY" data-fn="toThousandsFormates"></span>
					  			</td>
					  			<td>冲帐金额</td>
					  			<td>
					  				<span name="business.REVERSE_MONEY"></span>
					  				<c:if test="${param.handle=='approval'}">
					  					<input type="number" class="form-control input-sm" onchange="onlyNumber(this);updateReverseMoney(this)" style="width: 100px;display: none;"/>
						  				<EasyTag:res resId="HZ_BX_MONEY">
						  					<a href="javascript:;" class="hidden-print" onclick="editReverseMoney(this)">修改</a>
						  				</EasyTag:res>
					  				</c:if>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>应付金额</td>
					  			<td>
					  				<span name="business.PAY_MONEY" data-fn="toThousandsFormates"></span>
					  			</td>
					  			<td>大写</td>
					  			<td>
					  				<span name="business.PAY_MONEY_DX"></span>
					  			</td>
					  		</tr>
				  			<tr style="display: none;">
					  			<td>报销凭证</td>
					  			<td colspan="3" class="fileList"></td>
					  		</tr>
					  	</table>
				 </div>
				 <div class="layui-col-md12">
				 	<div class="bxItems" style="min-height: 100px;">
						 <table class="layui-table approve-table">
						   <thead>
						    <tr class="hidden-print">
						      <th>行</th>
						      <th>费用日期</th>
						      <th class="hidden-print">费用类型(内部)</th>
						      <th>费用类型</th>
						      <th class="hidden-print">费用说明</th>
						      <th class="hidden-print">发票类型</th>
						      <th>金额</th>
						      <th>核准金额</th>
						      <th>部门</th>
						      <th>项目号</th>
						      <th>产品线</th>
						      <th class="visible-print">会计科目</th>
						      <th>税率(%)</th>
						      <th style="width: 70px;">税金</th>
						      <th style="width: 80px;">金额(不含税)</th>
						    </tr>
 						    <tr class="visible-print">
						      <th style="width: 20px!important;">行</th>
						      <th style="width: 50px;">费用日期</th>
						      <th class="hidden-print">费用类型(内部)</th>
						      <th style="width: 70px;">费用类型</th>
						      <th  style="min-width: 100px;" class="hidden-print">费用说明</th>
						      <th class="hidden-print">发票类型</th>
						      <th style="width: 85px;">金额</th>
						      <th style="width: 80px;">核准金额</th>
						      <th style="width: 70px;">部门</th>
						      <th style="width: 120px;">项目号</th>
						      <th  style="width: 50px;">产品线</th>
						      <th style="width: 100px;" class="visible-print">会计科目</th>
						      <th style="width: 30px!important;">税率(%)</th>
						      <th style="width: 70px;">税金</th>
						      <th style="width: 80px;">金额(不含税)</th>
						    </tr>  
						  </thead>
						  <tbody data-mars="BxDao.bxItems" data-template="bxItems">
						  
						  </tbody>
						  <tbody>
						  	<tr>
						        <td class="hidden-print"></td>
						        <td class="hidden-print"></td>
						        <td class="hidden-print"></td>
						        <td class="visible-print"></td>
						  		<td colspan="9" style="text-align: right;">合计</td>
						  		<td style="width: 70px;border-top:0!important;"><span name="business.TAX_MONEY"></span></td>
						  		<td style="width: 80px;border-top:0!important;"><span name="business.NO_TAX_MONEY"></span></td>
						  	</tr>
						  </tbody>
						</table>
					 </div>
				 </div>
				 <div class="layui-col-md12 approve-result">
					 	<table class="layui-table approveResult" lay-skin="nob" style="margin-top: 10px;">
						  	<tbody data-mars="FlowDao.approveResult" data-template="approveList"></tbody>
						</table>
				</div>
			</div>
		</form>
	<script id="bxItems" type="text/x-jsrender">
  	 {{for data}}
  		<tr>
  			<td>{{:#index+1}}</td>
  			<td>{{:BX_DATE}}</td>
  			<td class="hidden-print">{{:FEE_IN_TYPE}}</td>
  			<td>{{:FEE_TYPE}}</td>
  			<td class="hidden-print">{{cutText:FEE_DESC 100}} {{if INVOICE_NO}}({{cutText:INVOICE_NO 17}}){{/if}} <a href="javascript:void(0);" onclick="itemDetail('{{:ITEM_ID}}')" style="text-decoration:underline;color:red;" class="pull-right">详情</a></td>
  			<td class="hidden-print">{{:INVOICE_TYPE}}</td>
  			<td>{{call:AMOUNT fn='amtFmt'}}</td>
  			<td><span class="hzAmountVal">{{call:HZ_AMOUNT fn='amtFmt'}}</span><input type="number" data-text="false" class="form-control input-sm updateHzAmount" value="{{:HZ_AMOUNT}}" style="width:80px;" onblur="onlyNumber(this);" onchange="updateHzAmount(this,{{:AMOUNT}},'{{:ITEM_ID}}','{{:BUSINESS_ID}}','{{:TAX_RATE}}')"/></td>
  			<td>{{:DEPT_CODE}}&nbsp;{{:DEPT_NAME}}</td>
  			<td>{{:CONTRACT_NO}}&nbsp;{{:CONTRACT_NAME}}</td>
  			<td>{{:PRODUCT_LINE_NO}}&nbsp;{{:PRODUCT_LINE}}</td>
  			<td class="visible-print">{{:SUBJECT_NO}}&nbsp;{{:SUBJECT_NAME}}</td>
  			<td>{{:TAX_RATE}}</td>
  			<td>{{:TAXES}}</td>
  			<td>{{:R_AMOUNT}}</td>
  		</tr>
  	  {{/for}}
	</script>
	
	<script id="approveList" type="text/x-jsrender">
  	 {{for data}}
  		<tr {{if CHECK_RESULT=='0'}} class="hidden-print" {{/if}}>
  			<td style="text-align:left;width:50%;">{{if NODE_ID!='0'}} {{call:CHECK_RESULT fn='checkResultLable'}}   {{if CHECK_DESC}}({{:CHECK_DESC}}) {{/if}} {{/if}}</td>
  			<td style="text-align:right;"> <span style="color:#333;">{{:NODE_NAME}}</span> / {{:CHECK_NAME}} / {{:DEPT_NAME}} / {{:CHECK_TIME}}</td>
  		</tr>
  	  {{/for}}
	 {{if data.length==0}}
		<tr><td  colspan="2">暂无数据</td></tr>
	{{/if}}
	</script>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
	    var Flow = {};
	    var baseData = {};
	    var applyData = {};
	    
		$(function(){
			FlowCore.initPage({
				hideApplyNo:true,
				textModel:false,
				success:function(result){
					baseData  = result['FlowDao.businessInfo']['data'];
					applyData  = result['FlowDao.baseInfo']['data'];
					initSetting();
					
					if(applyData.deptName=='人力行政部'){
						$('.fileList').parent().show();
					}
				}
			});
			
			var param = FlowCore.params;
			if(param.handle!='add'){
				renderInvoice(param);
			}
		});
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({reqUrl:'${ctxPath}/servlet/bx?action=doApprove'});
		}
		
		function checkResultLable(checkResult){
			 var json = {'1':'<span class="text-success">通过</span>','2':'<span class="text-waring">拒绝</span>'};
			 return json[checkResult]||'<span class="text-danger">待审批</span>';
		}
		
		function itemDetail(itemId){
			var data = {itemId:itemId};
			popup.layerShow({id:'showFeeInfo',title:'费用说明',full:fullShow(),area:['550px','400px'],offset:'30px',url:'/yq-work/pages/flow/bx/include/fee-detail.jsp',data:data});
		}
		
		function delFlow(){
			layer.confirm('确认删除吗，不可恢复',{offset:'20px'},function(index){
				layer.close(index);
				var businessId = $("#businessId").val();
				ajax.remoteCall("${ctxPath}/servlet/bx?action=delApply",{businessId:businessId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							popup.closeTab();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		function updateHzAmount(el,amount,itemId,businessId,taxRate){
			onlyNumber(el);
			var hzAmount = $(el).val();
			if(hzAmount>amount){
				$(el).val(amount);
				hzAmount = amount;
			}
			var _taxRate = Number(taxRate);
			
			var payMoneyDx = digitUppercase(hzAmount);
			
			var json  = {hzAmount:hzAmount,payMoneyDx:payMoneyDx,itemId:itemId,businessId:businessId};
			json['taxes'] = _calcRowRate1(hzAmount,_taxRate);
			json['rAmount'] = _calcRowRate2(hzAmount,_taxRate);
			
			ajax.remoteCall("${ctxPath}/servlet/bx/fun?action=updateHzMoney",json,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(el).hide();
						$(el).prev().text(hzAmount);
						$(el).prev().show();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		
		function _calcRowRate1(amount_,taxRate){
			var val =amount_;
			var v2 = numDiv(taxRate,100)+0;
			var v3 = val/(1+parseFloat(v2));
			return (val-v3).toFixed(2);
			
		}
		
		function _calcRowRate2(amount_,taxRate){
			var val =amount_;
			var v2 = numDiv(taxRate,100)+0;
			var v3 = val/(1+parseFloat(v2));
			return v3.toFixed(2);
		}
		
		function amtFmt(val){
			return Number(val);
		}
		
		function onlyNumber(obj){
			obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"和"."以外的字符
			obj.value = obj.value.replace(/^\./g,"");//验证第一个字符是数字而不是字符
			obj.value = obj.value.replace(/\.{2,}/g,".");//只保留第一个.清除多余的
			obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
			obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
	    }
		
		Flow.flowPrint = function(){
			$('title').text('.');
			var nodeName = $("[name='apply.nodeName']").text();
			if(nodeName!='会计主管'){
				layer.confirm("审批进度是【会计主管】才可以打印,是否继续",{offset:'20px'},function(index){
					layer.close(index);
				 	window.print();
				});
			}else{
				  window.print();
			}
		}
		
		
		function initSetting(){
			debugger;
			var nodeName =  applyData['nodeName'];
			var payState =  baseData['PAY_STATE'];
			var paperState =  baseData['PAPER_STATE'];
			if(nodeName=='费用会计'||nodeName=='会计主管'||nodeName=='准备付款'){
				$('.hz-bx-money').show();
			}else{
				$('.hz-bx-money').hide();
			}
			if(nodeName=='会计主管'&&payState=='0'){
				layer.msg('请打印单据贴票递交财务',{icon:1,offset:'rt'});
			}
			if(payState=='1'){//待付款
				$("[name='apply.nodeName']").text('准备付款');
				$("[name='apply.checkName']").text('--');
			}else if(payState=='10'){
				$("[name='apply.nodeName']").text('已付款');
				$("[name='apply.checkName']").text('--');
			}
			
			if(paperState=='1'){
				//待交纸质单据
				$('.isGetPaper').show();
				$('.paperState').html('<label class="label label-danger">待交纸质单据<label>');
			}
			if(paperState=='2'){
				$('.paperState').html('<label class="label label-success">已交纸质单据</label>');
			}
			if(paperState=='9'){
				$('.paperState').html('<label class="label label-warning">无效报销单据</label>');
			}
			
			$('.approve-node').remove();
		}
		
		function showContactUnit(val){
			if(val=='个人付款'){
				$('.showContactUnit').hide();
			}else{
				$('.showContactUnit').show();
			}
			return val;
		}
		
		function editReverseMoney(el){
			$(el).prev().prev().hide();
			$(el).prev().val($(el).prev().prev().text());
			$(el).prev().show();
			$(el).hide();
		}
		
		function updateReverseMoney(el){
			var businessId = $("#businessId").val();
			var reverseMoney = $(el).val();
			var hzMoney = Number(baseData['HZ_MONEY']);
			if(reverseMoney>hzMoney){
				layer.msg("冲账金额不能大于"+hzMoney);
				return;
			}
			ajax.remoteCall("${ctxPath}/servlet/bx/fun?action=updateReverseMoney",{reverseMoney:reverseMoney,businessId:businessId},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(el).prev().text($(el).val());
						$(el).prev().show();
						$(el).next().show();
						$(el).hide();
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function updatePaperState(){
			var businessId = $("#businessId").val();
			layer.confirm('确认已签收报销的纸质单据吗',function(index){
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/bx/fun?action=getPaperState",{businessId:businessId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							location.reload();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		FlowCore.flowPrint = function(){
			var html = [];
			html.push('<a style="text-decoration: underline;color: #366ec5;" target="_blank" href="/yq-work/doc/84558301431099998044913">点击查看打印教程</a>');
			html.push('<br>请在打印前预览，确保合适的纵向打印尺寸');
			html.push('<br>报销申请单（贴于最上方）、电子发票（如有），以及粘贴底单，这三项均需打印。');
			
			layer.open({title:'打印须知',offset:'20px',content:'<div>'+html.join(',')+'</div>',btn:['打印预览','取消'],yes:function(index){
				layer.close(index);
				if(Flow.flowPrint){
					Flow.flowPrint();
				}else{
					ajax.remoteCall("/yq-work/servlet/flow/fun?action=addPrintLog",FlowCore.params,function(result) { 
					if(result.state == 1){
						$('title').text('.');
						window.print();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				 });
				}
			}});
		  }
		
	    function toThousandsFormates(num) {
	        // 判断传进来的数字是否为非空数字
	       if (!isNaN(parseFloat(num))) {
	            var reg = /\./g
	            var newNum = Number(Number(num).toFixed(2)).toLocaleString()
	            // 判断转换后的数字是否带有小数
	            if (reg.test(newNum)) {
	                var numArr = newNum.split('.')
	                // 判断小数点后数字长度为1，则自动补0
	                numArr[1] = numArr[1].length === 1 ? numArr[1] + '0' : numArr[1]
	                return numArr.join('.')
	            } else {
	                // 整数直接在后面补上0.00
	                return newNum + '.00'
	            }

	        } else {
	            return ''
	        }
	    }
	    
	    function bankCardFormat(v){
	    	if(v){
		    	if(/\S{5}/.test(v)){
		    	  return v.replace(/\s/g, '').replace(/(.{4})/g, "$1 ");
		    	}else{
		    		return v;
		    	}
	    	}else{
	    		return '';
	    	}
	    }
	    
	    var eInvoiceLook = function(){
	    	var applyInfo = FlowCore.applyInfo;
	    	var applyNo = applyInfo.applyNo;
	    	popup.layerShow({url:'/yq-work/pages/flow/bx/invoice/invoice-get.jsp',full:fullShow(),area:['80%','80%'],shadeClose:true,scrollbar:false,offset:'20px',data:{businessId:FlowCore.businessId,applyNo:applyNo},id:'eInvoiceLayer',title:'电子发票'});
	    }
	     
		var renderInvoice = function(){
			ajax.remoteCall("/yq-work/webcall?action=BxInvoiceDao.bxApplyInvoiceCount",FlowCore.params,function(result){
				var data = result.data;
				if(data>0){
				    $('.eInvoiceBtn').html('<i class="fa fa-file-pdf-o"></i> 电子发票('+data+')').show();
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>