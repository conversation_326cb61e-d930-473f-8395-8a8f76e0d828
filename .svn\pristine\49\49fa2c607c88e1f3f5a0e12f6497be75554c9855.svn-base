<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
		.layui-timeline-item:hover a{opacity:1;}
		.layui-table-cell a{font-size: 12px;color: #3E84E9;cursor: pointer;}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 14px;}
		.layui-tab-card>.layui-tab-title{background-color: #fff;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		.layui-timeline-content p{font-size: 13px;color: #666;}
		.layui-timeline-title{margin-bottom: 2px;}
		.layui-timeline-item{margin-bottom: 2px;}
		.layui-text h3{font-size: 16px;}
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.gray-bg{background-color: #e8edf7;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="ReviewDao.info" data-mars-prefix="review." data-text-model="true">
				<input type="hidden" name="review.REVIEW_ID" value="${param.reviewId}"/>
				<input type="hidden" id="reviewId" name="reviewId" value="${param.reviewId}"/>
				<input type="hidden" id="nodeId" name="nodeId" value="${param.nodeId}"/>
			    <input type="hidden" name="fkId" value="${param.reviewId}"/>
				<div class="ibox-content" style="padding: 5px;margin: -15px -15px 0px;">
					<div class="layui-row" style="padding:6px 15px;padding-bottom:0px;">
					  <div class="layui-col-md8">
							<span style="line-height: 30px;">合同评审</span>
					  </div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm">
									<button class="btn btn-info btn-xs mr-10" onclick="addReviewResult();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 填写评审意见</button>
									<EasyTag:res resId="CONTRACT_MGR">
										<button class="btn btn-default btn-xs" onclick="editReview();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
									</EasyTag:res>
								</div>
								<div class="btn-group btn-group-sm hidden">
									<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">•••</button>
									<ul class="dropdown-menu dropdown-menu-right">
									    <li><a href="#">删除</a></li>
									</ul>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
				</div>
				<div class="layui-row">
					<div class="layui-col-md12">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">相关信息</li>
						    <li>评审结果(<span id="resultCount">0</span>)</li>
						    <li>评审小组(<span id="nodeCount">0</span>)</li>
						  </ul>
						  <div class="layui-tab-content" style="margin:0px;background-color: #fff;min-height: calc( 100vh - 120px)">
						    <div class="layui-tab-item layui-show baseInfo">
						    	<jsp:include page="include/base-info.jsp"></jsp:include>
						    </div>
						    <div class="layui-tab-item">
						    	<table class="layui-table" id="reviewResult"></table>
						    </div>
						    <div class="layui-tab-item">
						    	<table class="layui-table" id="reviewNode"></table>
						    </div>
						  </div>
					</div>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">

		var Review = {reviewId:$('#reviewId').val()};
		
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
			});
			$("#flowForm").render({success:function(result){
				var row = result['ReviewDao.info'].data;
				var jsonText = row['TEXT_JSON'];
				var json = eval('(' + jsonText + ')');
				fillRecord(json,'review.','','#flowForm');
				initNodeResult();
				initNodeList();
			}});
			
		});
		function getContent(val){
			if(val){
				return val.replace(/\n|\r\n/g,'<br/>');
			}else{
				return '--';
			}
		}
		
		function initNodeList(){
			$("#flowForm").initTable({
				mars:'ReviewDao.reviewNodeDetail',
				id:'reviewNode',
				page:false,
				cellMinWidth:80,
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
					 field:'NODE_NAME',
					 title:'评审小组'
				 },{
					 field:'FIRST_CHECK_NAMES',
					 title:'初审人'
				 },{
					 field:'LAST_CHECK_NAMES',
					 title:'终审人'
				 },{
					 field:'CHECK_STATE',
					 title:'当前状态',
					 templet:function(row){
						 var firstCheckNames = row['FIRST_CHECK_NAMES'];
						 var checkState = row['CHECK_STATE'];
						 return '初审';
					 }
				 }
			]],done:function(data){
				$("#nodeCount").text(data.total);
		  	}
		 });
		}
		function editReview(){
			var reviewId = $("[name='reviewId']").val();
			popup.openTab({id:'editReview',url:'${ctxPath}/pages/crm/contract/review/review-apply.jsp',title:'修改合同评审',data:{reviewId:reviewId}});
		}
		function addReviewResult(){
			var nodeId = $("[name='nodeId']").val();
			var reviewId = $("[name='reviewId']").val();
			popup.layerShow({id:'addReviewResult',title:'评审意见',area:['500px','400px'],url:'${ctxPath}/pages/crm/contract/review/include/review-result.jsp',data:{nodeId:nodeId,reviewId:reviewId}});
		}
		
		function initNodeResult(){
			$("#flowForm").initTable({
				mars:'ReviewDao.reviewResultDetail',
				id:'reviewResult',
				page:false,
				cellMinWidth:80,
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
					 field:'NODE_NAME',
					 title:'评审部门'
				 },{
					 field:'COMMENT',
					 title:'评审描述'
				 },{
					 field:'ADD_NAME',
					 title:'评审者'
				 },{
					 field:'ADD_TIME',
					 title:'评审时间'
				 },{
					 field:'RESULT_STATE',
					 title:'可行性',
					 templet:function(row){
						 return '可行';
					 }
				 }
			]],done:function(data){
				$("#resultCount").text(data.total);
		  	}
		 });
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>