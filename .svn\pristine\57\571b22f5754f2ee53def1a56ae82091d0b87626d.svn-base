<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>订购管理</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>

<EasyTag:override name="content">
    <div class="container-fluid">
        <form id="addOrderForm" class="form-horizontal" data-mars="PlatformOrderDao.getOrder" data-mars-prefix="order.">
            <table class="table table-edit table-vzebra">
                <tbody>
                <tr class="hidden">
                    <td colspan="2">
                        <input type="hidden" name="orderId" value="${param.orderId}"/>
                        <input type="hidden" name="order.ORDER_ID" value="${param.orderId}"/>
                        <input type="hidden" name="order.PLATFORM_ID" value="${param.platformId}" id="platformId"/>
                        <input type="hidden" name="order.PLATFORM_NAME" value="${param.platformName}"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">合同<i class="layui-icon layui-icon-tips" lay-tips="编辑时不可修改关联合同信息，若需修改请删除此订购信息。"></i></td>
                    <td colspan="3">
                        <input type="hidden" name="order.CONTRACT_NO"/>
                        <input type="hidden" name="order.CONTRACT_ID"/>
                        <input type="text" name="order.CONTRACT_NAME" class="form-control input-sm" readonly onclick="singleContract(this)" placeholder="请点击选择合同"  data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">商机<i class="layui-icon layui-icon-tips" lay-tips="请选择实际客户订购该业务平台的实际商机，非合同总商机。商机-销售-客户三者对应。"></i></td>
                    <td colspan="3">
                        <input type="hidden" name="order.BUSINESS_ID"/>
                        <input type="text" name="order.BUSINESS_NAME" class="form-control input-sm" readonly
                               data-ref-cust-id="order.CUST_ID"
                               data-ref-cust-name="order.CUST_NAME"
                               data-ref-sale-id="order.SALES_BY"
                               data-ref-sale-name="order.SALES_BY_NAME"
                               onclick="singleSjByPlatform(this,$('#platformId').val())" placeholder="请选择实际商机，非平台合同总商机。" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">客户<i class="layui-icon layui-icon-tips" lay-tips="请选择实际订购业务的客户，非平台合同总客户。"></i></td>
                    <td colspan="3">
                        <input type="hidden" name="order.CUST_ID"/>
                        <input type="text" name="order.CUST_NAME" class="form-control input-sm" readonly onclick="singleCust(this)" placeholder="请选择实际订购业务的客户，非平台合同总客户。" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">销售人员<i class="layui-icon layui-icon-tips" lay-tips="请选择该订购客户对应的负责销售，并非一定是平台合同总销售。"></i></td>
                    <td colspan="3">
                        <input type="hidden" name="order.SALES_BY"/>
                        <input type="text" name="order.SALES_BY_NAME" class="form-control input-sm" readonly onclick="singleUser(this)" placeholder="请选择该订购客户对应的负责销售，非平台合同总销售。" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td>订购状态</td>
                    <td>
                        <select name="order.ORDER_STATUS" class="form-control input-sm">
                            <option value="0">正常</option>
                            <option value="1">暂停</option>
                            <option value="2">终止</option>
                        </select>
                    </td>
                    <td class="required" ">订购金额</td>
                    <td>
                        <input type="number" name="order.ORDER_AMOUNT" class="form-control input-sm" value="0" placeholder="非合同总金额，为合同下该订购客户的实际金额。" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td>开始日期</td>
                    <td>
                        <input type="text" name="order.START_DATE" class="form-control input-sm Wdate"
                               onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                    </td>
                    <td>结束日期</td>
                    <td>
                        <input type="text" name="order.END_DATE" class="form-control input-sm Wdate"
                               onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                    </td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td colspan="3">
                        <textarea name="order.REMARK" class="form-control input-sm" rows="4"></textarea>
                    </td>
                </tr>
                </tbody>
            </table>
            <p class="layer-foot text-c">
                <button type="button" class="btn btn-primary btn-sm" style="width: 80px"
                        onclick="OrderEdit.ajaxSubmitForm()">保 存
                </button>
                <button type="button" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll()">关 闭
                </button>
            </p>
        </form>
    </div>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("OrderEdit");

        OrderEdit.orderId = '${param.orderId}';

        $(function(){
            $("#addOrderForm").render({success:function(result){
                    if(OrderEdit.orderId) {
                        $("[name='order.CONTRACT_NAME']").attr("onclick", "");
                        $("[name='order.CONTRACT_NAME']").attr("readonly", "readonly");
                    }
            }});
        });

        OrderEdit.ajaxSubmitForm = function(){
            if(form.validate("#addOrderForm")){
                if(this.orderId){
                    this.updateData();
                }else{
                    this.insertData();
                }
            }
        }

        OrderEdit.insertData = function(){
            var data = form.getJSONObject("#addOrderForm");
            ajax.remoteCall("${ctxPath}/servlet/platform-order?action=addOrder",data,function(result){
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadOrderList();
                    });
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        OrderEdit.updateData = function(){
            var data = form.getJSONObject("#addOrderForm");
            ajax.remoteCall("${ctxPath}/servlet/platform-order?action=updateOrder",data,function(result){
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadOrderList();
                    });
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        $('*[lay-tips]').on('mouseenter', function(){
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">'+ content + '</div>', this, {
                time: -1
                ,maxWidth: 280
                ,tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function(){
            layer.close(this.index);
        });


        function singleSjByPlatform(el,sjPlatformId){
            if (sjPlatformId === undefined) sjPlatformId = '';
            var id = new Date().getTime();
            $(el).attr('data-sid',id);
            addClearTag(el);
            popup.layerShow({id:'selectSj',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择商机',url:'/yq-work/pages/flow/select/select-sj.jsp',data:{sid:id,type:'radio',sjPlatformId:sjPlatformId}});
        }


        function selctCallBack(id,row){
            $("[name='order.CONTRACT_NAME']").val(row['CONTRACT_NAME']);
            $("[name='order.CONTRACT_ID']").val(row['CONTRACT_ID']);
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>