<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>项目反馈</title>
	<style>
		.radio.radio-inline{margin-top: 12px;}
		.publishProjectStateBody .layui-table-cell {
			padding:6px 6px;
			height: auto;
			word-break: normal;
		 	display: block;
		 	white-space: pre-wrap;
		 	word-wrap: break-word;
		 	overflow: hidden;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="projectAction" class="form-inline publishProjectState">
		 <input name="projectId" type="hidden" value="${param.projectId}">
		  <div class="layui-card">
		  		<div class="layui-card-header" style="padding: 0px 10px;">
		  		    <div class="input-group input-group-sm" style="width: 160px;margin-bottom: 10px;">
						<span class="input-group-addon">反馈类别*</span>	
		  				<select class="form-control input-sm" name="followState" style="width: 120px;display: inline-block;">
	                  		<option value="">--请选择--</option>
	                  		<option value="进度更新">进度更新</option>
							<option value="问题报告">问题报告</option>
							<option value="风险管理">风险管理</option>
							<option value="需求变更">需求变更</option>
							<option value="技术讨论">技术讨论</option>
							<option value="质量保证">质量保证</option>
							<option value="资源分配">资源分配</option>
							<option value="决策支持">决策支持</option>
							<option value="用户反馈">用户反馈</option>
							<option value="会议纪要">会议纪要</option>
							<option value="文档更新">文档更新</option>
							<option value="其他">其他</option>
	                   </select>
				     </div>
		  		    <div class="input-group input-group-sm" style="width: 160px;margin-bottom: 10px;">
						<span class="input-group-addon">反馈日期*</span>	
						<input name="followDate" type="date" class="form-control input-sm">
				    </div>
		  		    <div class="input-group input-group-sm" style="width: 160px;margin-bottom: 10px;">
						<span class="input-group-addon">回复负责人</span>	
						<input name="stakeholderId" type="hidden">
						<input name="stakeholder" type="text" placeholder="可选" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm select-icon">
				    </div>
		  		    <div class="input-group input-group-sm" style="width: 160px;margin-bottom: 10px;">
						<span class="input-group-addon">抄送人</span>	
						<input name="ccIds" type="hidden">
						<input name="ccNames" type="text" placeholder="可选"  onclick="multiUser(this);" readonly="readonly" class="form-control input-sm select-icon">
				    </div>
	                <label class="radio radio-success radio-inline ml-20">
	   					<input type="radio" checked="checked" name="color" value="success"><span></span>
	  				</label>
					<label class="radio radio-warning radio-inline">
	   					<input type="radio" name="color" value="warning"><span></span>
	  				</label>
					<label class="radio radio-danger radio-inline">
	   					<input type="radio" name="color" value="danger"><span></span>
	  				</label>
		  		</div>
			  	<div class="layui-card-body">
				  	<textarea name="projectUpdateDesc" id="projectUpdateDesc" maxlength="300" placeholder="请输入项目跟进内容" class="form-control input-sm" style="height: 70px;width:100%;resize:none;"></textarea>
				  	<button type="button" onclick="publishProjectState()" class="btn btn-sm btn-info mt-5">发布</button>
			  	</div>
		 </div>
		<div class="layui-card">
			<div class="layui-card-header">交流反馈</div>
		    <div class="layui-card-body publishProjectStateBody" style="padding-top: 0px;min-height: calc(100vh - 300px)">
		  		<table class="layui-table" id="prjectStateTable"></table>
		    </div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">

	var projectId = '${param.projectId}';
	function projectStateLog(){
		$("#projectAction").initTable({
			mars:'ProjectDao.projectStateLog',
			id:"prjectStateTable",
			cellMinWidth:100,
			data:{pageType:3},
			page:true,
			cols: [[
	      	{
				title: '序号',
				type:'numbers'
			 },{
			    field: 'UPDATE_REASON',
				title: '更新内容',
				minWidth:400,
				templet:function(row){
					var operateId = row['OPERATE_ID'];
					var stakeholderId = row['STAKEHOLDER_ID'];
					var stakeholder = row['STAKEHOLDER'];
					var updateBy = row['UPDATE_BY'];
					var array = [];
					array.push('<span class="text-'+row['FONT_COLOR']+'">');
					if(stakeholder){
						array.push('<a href="javascript:;">@'+stakeholder+'</a>：');
					}
					array.push(row['UPDATE_REASON']);
					array.push('</span>');
					
					var replyContent = row['REPLY_CONTENT'];
					if(replyContent){
						array.push('<br>回复：<b>'+replyContent+'</b>')
					}else if(stakeholderId==updateBy){
						array.push('<br><button class="btn btn-xs btn-info" type="button" onclick="replyAction(this,\''+operateId+'\')">回复</button>')
					}
					return array.join('');
				}
			},{
			    field: 'UPDATE_STATE',
				title: '反馈类别',
				width:80,
				align:'center'
			},{
			    field: 'UPDATE_BY_NAME',
				title: '提出人',
				align:'center',
				width:90
			},{
			    field: 'UPDATE_TIME',
				title: '提交时间',
				align:'center',
				width:150,
				align:'center'
			},{
				field:'REPLY_TIME',
				title:'回复状态',
				width:100,
				templet:function(row){
					var replyTime = row.REPLY_TIME;
					var stakeholder = row.STAKEHOLDER;
					if(stakeholder&&replyTime){
						return stakeholder+'已回复';
					}
					if(stakeholder==''){
						return '无设置';
					}else{
						return stakeholder+'待回复'; 
					}
				}
			}
			]],done:function(result){
				
			}});
		
	}
	
	function publishProjectState(){
		var data = form.getJSONObject(".publishProjectState");
		data['projectId']=projectId;
		var followState = data.followState;
		var followDate = data.followDate;
		if(followState==''){
			layer.msg("请选择反馈类别.");
			return;
		}
		if(followDate==''){
			layer.msg("请选择反馈日期.");
			return;
		}
		var projectUpdateDesc=$("#projectUpdateDesc").val();
		if(projectUpdateDesc==''){
			layer.msg("描述理由不能为空.");
			return;
		}
		if(projectUpdateDesc.length<10){
			layer.msg("描述理由不能少于十个字");
			return;
		}
		ajax.remoteCall("${ctxPath}/servlet/project?action=updateProjectState",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg);
				$("#projectUpdateDesc").val("");
				$("#projectAction").queryData({id:'prjectStateTable',page:false});
			}else{
				layer.alert(result.msg,{icon: 7});
			}
		});
	}
	
	function replyAction(el,id){
		layer.prompt({title: '请输入回复内容',formType: 2,value:'',icon:3},function(val){
			ajax.remoteCall("${ctxPath}/servlet/project?action=addReply",{comment:val,id:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$("#projectAction").queryData({id:'prjectStateTable',page:false});
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
			
		});
	}
	
 	$(function(){
 		projectStateLog();
 	});
 	
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>