<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的申请</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input type="hidden" name="flowCode" value="${flowCode}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          			 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程名称</span>
						 	<input class="form-control input-sm" name="flowName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">待办人</span>
						 	<input class="form-control input-sm" name="checkName">
						 </div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">状态</span>	
							<select name="applyState" onchange="queryData();" class="form-control input-sm">
							      <option value="">请选择 </option>
                    			  <option data-class="label label-default" value="0">草稿</option>
                    			  <option data-class="label label-warning" value="1">作废</option>
							      <option data-class="label label-warning" value="10">待审批</option>
                    			  <option data-class="label label-info" value="20">审批中</option>
                    			  <option data-class="label label-danger" value="21">审批退回</option>
                    			  <option data-class="label label-success" value="30">已完成</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		</form>
		   <script type="text/x-jsrender" id="bar">
				{{if APPLY_STATE=='21'}}
 					<a class="layui-btn layui-btn-danger layui-btn-xs" href="javascript:;">重新提交</a>
					{{else APPLY_STATE =='0'}}
 					  <a class="layui-btn layui-btn-normal layui-btn-xs" href="javascript:;">修改</a>
					{{else}}
 					 <a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>
 				{{/if}}
		  </script>
		 <script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default" lay-event="refreshData">刷新</button>
			 	<button class="btn btn-sm btn-warning ml-10" lay-event="cannelApply">作废</button>
			 	<button class="btn btn-sm btn-danger ml-10" lay-event="delApply">删除</button>
 				<EasyTag:res resId="copyFlow">
			 		<button class="btn btn-sm btn-default ml-10" lay-event="copyData">复制</button>
 				</EasyTag:res>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
					loadFlowNum();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'FlowDao.myFlowApply',
					id:'flowTable',
					page:true,
					limit:20,
					toolbar:'#btnBar',
					rowDoubleEvent:'flowDetail',
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'radio',
						title: '序号',
						align:'left'
					 },{
						field:'',
						title:'操作',
						align:'center',
						width:70,
						fixed:'right',
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						 }
					},{
						 field:'APPLY_NO',
						 title:'编号',
						 width:230,
						 templet:function(row){
							 return row['APPLY_NO']+"_"+row['FLOW_NAME'];
						 }
					 },{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:200
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					},{
						field:'APPLY_STATE',
						title:'当前状态',
						width:80,
						templet:function(row){
							var val = row['APPLY_STATE'];
							return getText(val,'applyState');
						}
					},{
						field:'NODE_NAME',
						title:'当前节点',
						width:80
					},{
						field:'CHECK_NAME',
						title:'当前审批人',
						width:80,
						templet:function(row){
							return row['CHECK_NAME'];
						}
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function flowDetail(data){
			var id = data['FLOW_CODE'];
			var json  = $.extend({},{businessId:data['APPLY_ID'],id:id,applyState:data['APPLY_STATE']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/servlet/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
			
	   function copyData(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
			var data = dataList[0];
			var flowCode = data['FLOW_CODE'];
			if(flowCode!='finance_bx'){
				layer.msg('该流程暂不支持复制功能');
				return;
			}
			var json  = $.extend({copyData:'1'},{businessId:data['APPLY_ID'],id:flowCode,applyState:data['APPLY_STATE']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/servlet/flow',data:json});
	   }
	   
	   function refreshData(){
		   location.reload();
	   }
	   
	   function delApply(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
			var flowCode = data['APPLY_STATE'];
			if(flowCode!='0'&&flowCode!='1'){
				layer.msg('只有草稿或作废状态才可以删除');
				return;
			}
			layer.confirm('确认删除吗,不可恢复',{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/flow?action=delApply",{businessId:data['APPLY_ID'],flowCode:data['FLOW_CODE']},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,function(){
							queryData();
						})
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
	   }
	   
	   function cannelApply(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
			var applyState = data['APPLY_STATE'];
			if(applyState>=10&&applyState<30){
				layer.confirm('确认作废吗,不可恢复',{icon:3,offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/flow?action=cannelApply",{businessId:data['APPLY_ID']},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,function(){
								queryData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			}else{
				layer.msg('该流程状态不支持作废');
				return;
			}
	   }
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>