package com.yunqu.work.base;

import java.sql.SQLException;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.WebSocketUtils;
public abstract class BaseFlowServlet extends AppBaseServlet{ 
	private static final long serialVersionUID = 1L;

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME+"-flow";
	}
	
	protected MessageModel getMessageModel(String businessId,String receiver,String msgLabel,String desc) {
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		return getMessageModel(apply, receiver, msgLabel, desc);
	}
	
	protected MessageModel getMessageModel(FlowApplyModel apply,String receiver,String msgLabel,String desc) {
		MessageModel msgModel = new MessageModel();
		msgModel.setFkId(apply.getApplyId());
		msgModel.setTitle(apply.getApplyTitle());
		msgModel.setTypeName(apply.getFlowCode());
		msgModel.setSender(getUserId());
		msgModel.setSendName(getUserName());
		if(desc==null) {
			msgModel.setDesc(apply.getApplyRemark());
		}else {
			msgModel.setDesc(desc);
		}
		msgModel.setWxData(apply.getFlowName(),apply.getApplyName(),apply.getApplyTime(),"待审批");
		if(msgLabel==null) {
			msgModel.setMsgLabel("待审批");
		}else {
			msgModel.setMsgLabel(msgLabel);
			msgModel.setData4(msgLabel);
		}
		msgModel.setData4("操作人："+getUserName()+"\n"+msgModel.getData4());
		if(receiver==null) {
			msgModel.setReceiver(apply.getApplyBy());
		}else {
			msgModel.setReceiver(receiver);
		}
		msgModel.setData((JSONObject)JSONObject.toJSON(apply));
		return msgModel;
	}
	
	protected void sendAllMsg(MessageModel msgModel) {
		if(msgModel==null) {
			return;
		}
		WxMsgService.getService().sendFlowTodoCheck(msgModel);
		MessageService.getService().sendMsg(msgModel);
		EmailService.getService().sendFlowNoticeEmail(msgModel);
		WebSocketUtils.sendMessage(new MsgModel(msgModel.getReceiver(),"流程审批", msgModel.getTitle()));
	}
	
	
	protected MessageModel sendToApplyMsgModel(String businessId,int checkResult) {
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		FlowModel flow = ApproveService.getService().getFlow(apply.getFlowCode());
		MessageModel msgModel = new MessageModel();
		msgModel.setTypeName(apply.getFlowCode());
		msgModel.setSender(getUserId());
		msgModel.setReceiver(apply.getApplyBy());
		msgModel.setSendName(getUserName());
		msgModel.setFkId(businessId);
		msgModel.setTitle(apply.getApplyTitle());
		String resultDesc = checkResult==1?"审批通过。":"审批退回";
		msgModel.setMsgLabel(getUserName()+"："+resultDesc);
		msgModel.setData1("您在"+apply.getApplyTime()+"申请的【"+flow.getFlowName()+"】，单据编号"+apply.getApplyNo()+"已经被"+getUserName()+resultDesc+"。");
		msgModel.setData2(resultDesc);
		msgModel.setData3(EasyDate.getCurrentDateString());
		msgModel.setDesc("更多信息,请登录云趣工作台查看,感谢您的使用。");
		
		WxMsgService.getService().sendFlowCheckOk(msgModel);
		
		msgModel.setDesc(msgModel.getData1());
//		EmailService.getService().sendFlowNoticeEmail(msgModel);
		
		WebSocketUtils.sendMessage(new MsgModel(msgModel.getReceiver(),"流程审批", msgModel.getData1()));
		
		return msgModel;
		
	}
	
	protected void sendFeedback(String businessId,String toUserId,String msgContent,String replyContent) {
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		MessageModel model = new MessageModel();
		model.setTitle(apply.getApplyTitle());
		model.setFkId(businessId);
		if(toUserId==null) {
			model.setReceiver(apply.getApplyBy());
		}else {
			model.setReceiver(toUserId);
		}
		model.setData1(EasyDate.getCurrentDateStr());
		model.setData2(msgContent);
		if(replyContent!=null) {
			model.setData3(replyContent);
		}else {
			model.setData3("--");
		}
		model.setDesc("更多信息，请登录云趣工作台查看详情，或联系处理人 "+getUserName());
		WxMsgService.getService().sendFlowFeedback(model);
		WebSocketUtils.sendMessage(new MsgModel(model.getReceiver(),"流程审批", model.getTitle()));
	}
	
	protected void sendFeedback(String businessId,String toUserId,String msgContent) {
		sendFeedback(businessId,toUserId,msgContent,null);
	}
	protected void sendFeedback(String businessId,String msgContent) {
		sendFeedback(businessId,null,msgContent,null);
	}
	
	protected void updateApplyConf(EasyQuery query,String businessId,JSONObject params) throws SQLException {
		EasySQL sql = new EasySQL("update yq_flow_apply set ");
		sql.append("config_json = JSON_SET (config_json,'$.flowOkNotice',1,'$.flowNewNotice','2','$.flowNotFinishNotice','2') ");
		sql.append(businessId,"where apply_id = ?");
		query.executeUpdate(sql.getSQL(), sql.getParams());
	}
	
	protected EasyResult getApproveNextNodeId(String nextNodeId,FlowApplyModel apply,JSONObject params) throws SQLException {
		String _nextNodeId = params.getString("nextNodeId");
		String _nextNodeCode = params.getString("nextNodeCode");
		
		if(StringUtils.isNotBlank(_nextNodeCode)) {
			if("0".equals(_nextNodeCode)) {
				nextNodeId = "0";
			}else {
				ApproveNodeModel node = ApproveService.getService().getNodeInfoByNodeCode( _nextNodeCode, apply,params);
				if(node==null) {
					return EasyResult.fail(_nextNodeCode+"不存在,请联系管理员.");
				}
				nextNodeId = node.getNodeId();
			}
		}else if(StringUtils.isNotBlank(_nextNodeId)) {
			nextNodeId = _nextNodeId;
		}
		return EasyResult.ok(nextNodeId);
	}
	
	
	public abstract EasyResult actionForSubmitApply();
	
	public abstract EasyResult actionForUpdateApply();
	
	protected boolean setFlowApplyData(String businessId,EasyRecord applyRecord,JSONObject params) {
		return true;
	}
	
	protected boolean setFlowBusiData(String businessId,EasyRecord busiRecord,JSONObject params) {
		return true;
	}
	
	protected EasyResult submitApply() {
		JSONObject params = getJSONObject();
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String businessId = params.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			businessId =  FlowService.getService().getID();
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		
		String doAction = params.getString("doAction");
		EasyRecord applyRecord = getApplyRecord(flowCode);
		applyRecord.set("apply_state","submit".equals(doAction)?10:0);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.set("apply_id",businessId);
		applyRecord.setColumns(applyInfo);
		JSONObject extInfo  = JsonKit.getJSONObject(params, "extend");
		applyRecord.set("data_json",extInfo.toJSONString());
		
		if("submit".equals(doAction)) {
			this.setApplyNo(applyRecord);
		}
		
		boolean bl = true;
		EasyRecord record = null;
		JSONObject business = JsonKit.getJSONObject(params, "business");
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
		}
		try {
			if(!this.setFlowApplyData(businessId,applyRecord,params)||!this.setFlowBusiData(businessId,record,params)) {
				return EasyResult.fail();
			}
			
			if(record!=null) {
				bl = this.getQuery().save(record);
			}
			if(bl) {
				this.getQuery().save(applyRecord);
			}
			this.saveFile(businessId,params);
			this.saveItems(businessId, params);
			
			if("submit".equals(doAction)) {
				String result = this.startFlow(businessId,applyState,params);
				if(result==null) {
					this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
					return EasyResult.fail("流程节点未配置,请联系管理员");
				}
				return EasyResult.ok(businessId,"提交到审批节点:"+result);
			}
			return EasyResult.ok(businessId);
		} catch (Exception e) {
			this.error(null, e);
			this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
			return EasyResult.fail("流程异常,请联系管理员"+e.getMessage());
		}
	}
	
	protected EasyResult updateApply() {
		JSONObject params = getJSONObject();
		
		String businessId = params.getString("businessId");
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String doAction = params.getString("doAction");
		
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		
		boolean bl = true;
		EasyRecord record = null;
		JSONObject business = JsonKit.getJSONObject(params, "business");
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
		}
		
		EasyRecord applyRecord = null;
		if("submit".equals(doAction)) {
			applyRecord =  getApplyRecord(businessId,flowCode);
		}else {
			applyRecord = getApplyRecord();
		}
		applyRecord.setPrimaryValues(businessId);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.setColumns(applyInfo);
		applyRecord.set("apply_id",businessId);
		JSONObject extInfo  = JsonKit.getJSONObject(params, "extend");
		applyRecord.set("data_json",extInfo.toJSONString());
		
		if("submit".equals(doAction)&&applyState!=FlowConstants.FLOW_STAT_CHECK_RETURN) {
			this.setApplyNo(applyRecord);
		}
		
		try {
			if(!this.setFlowApplyData(businessId,applyRecord,params)||!this.setFlowBusiData(businessId,record,params)) {
				return EasyResult.fail();
			}
			
			if(record!=null) {
				bl = this.getQuery().update(record);
			}
			if(bl) {
				this.getQuery().update(applyRecord);
			}
			this.saveItems(businessId, params);
			
			if("submit".equals(doAction)) {
				String result = this.startFlow(businessId,applyState,params);
				if(result==null) {
					return EasyResult.fail("没有配置审批节点");
				}
			}
			this.addReviseLog(businessId, "修改流程");
		} catch (Exception e) {
			this.error(null, e);
			return EasyResult.fail("保存异常,请联系管理员"+e.getMessage());
		}
		return EasyResult.ok();
	}
	
	protected String startFlow(String businessId,int applyState,JSONObject params) throws SQLException {
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		FlowModel flow = apply.getFlow();
		String _nextNodeId = params.getString("nextNodeId");
		String _nextNodeCode = params.getString("nextNodeCode");
		
		ApproveNodeModel nodeInfo = null;
		if(StringUtils.notBlank(_nextNodeCode)) {
			nodeInfo = ApproveService.getService().getNodeInfoByNodeCode(_nextNodeCode, apply,params);
		}else if(StringUtils.notBlank(_nextNodeId)) {
			nodeInfo = ApproveService.getService().getNodeInfoByNodeId(_nextNodeId,apply,params);
		}else {
			nodeInfo = ApproveService.getService().getStartNode(apply,params);
		}
		if(nodeInfo==null) {
		    return null;
		}
		if(StringUtils.isBlank(nodeInfo.getCheckBy())) {
			return null;
		}
		
		String todoCheckUserId = nodeInfo.getCheckBy();
		if(todoCheckUserId.indexOf(apply.getApplyBy())>-1) {
			ApproveNodeModel  _nodeInfo = ApproveService.getService().getNode(nodeInfo.getNextNodeId());
			if(_nodeInfo!=null&&_nodeInfo.getCheckType()!=FlowConstants.FLOW_APPROVER_SELECT_USER) {
				nodeInfo = _nodeInfo;
			}
		}
		
		String nextResultId = RandomKit.uniqueStr();
		
		EasyRecord resultRecord = getApproveResultRecord(businessId);
		if(applyState==21) {
			resultRecord.set("node_name",flow.getStartNodeName()+"修改流程");
		}else {
			resultRecord.set("node_name",flow.getStartNodeName()+"发起流程");
		}
		String checkDesc = params.getString("checkDesc");
		if(StringUtils.notBlank(checkDesc)) {
			resultRecord.set("check_desc",checkDesc);
		}
		this.getQuery().save(resultRecord);
		String nowResultId = resultRecord.getPrimaryValue().toString();
		
		int signFlag = nodeInfo.getSignFlag();
		if(signFlag==1) {
			String checkBys = nodeInfo.getCheckBy();
			String[] checkIds = checkBys.split(",");
			int i= 0 ;
			for(String checkBy:checkIds) {
				EasyRecord nextResult = getApproveResultRecord(nodeInfo, businessId, nextResultId,nowResultId);
				nextResult.set("check_user_id", checkBy);
				nextResult.set("check_name", StaffService.getService().getUserName(checkBy));
				if(i>0) {
					nextResult.set("result_id", RandomKit.uniqueStr());
				}
				this.getQuery().save(nextResult);
				i++;
			}
		}else {
			EasyRecord nextResult = getApproveResultRecord(nodeInfo, businessId, nextResultId,nowResultId);
			this.getQuery().save(nextResult);
		}
		
		
		FlowService.getService().updateApplyBegin(businessId, nodeInfo.getNodeId(),nowResultId,nextResultId);
		
		//微信推送给审批人
		MessageModel msgModel  = getMessageModel(apply, nodeInfo.getCheckBy(), null, null);
		this.sendAllMsg(msgModel);
		
		return nodeInfo.getNodeName();
	}
	
	
	protected void saveFile(String businessId,JSONObject params) {
		Object obj = params.get("fileIds");
		if(obj!=null) {
			try {
				if(obj instanceof String) {
					this.getQuery().executeUpdate("update yq_files set fk_id = ? where file_id = ?",businessId,obj.toString());
				}else {
					JSONArray array = params.getJSONArray("fileIds");
					for(int i=0;i<array.size();i++) {
						this.getQuery().executeUpdate("update yq_files set fk_id = ? where file_id = ?",businessId,array.getString(i));
					}
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
	}
	
	protected void saveItems(String businessId,JSONObject params) throws SQLException {
		Object itemIndexObj = params.get("itemIndex");
		if(itemIndexObj!=null) {
			JSONArray itemIndexs = null;
			if(itemIndexObj instanceof String) {
				itemIndexs = new JSONArray();
				itemIndexs.add(itemIndexObj.toString());
			}else {
				itemIndexs = params.getJSONArray("itemIndex");
			}
			for(int i= 0;i<itemIndexs.size();i++) {
				EasyRecord itemModel = new EasyRecord("YQ_FLOW_APPLY_ITEM","ITEM_ID");
				String itemId = itemIndexs.getString(i);
				itemModel.setColumns(getItem(params, itemId));
				itemModel.setPrimaryValues(itemId);
				itemModel.set("BUSINESS_ID", businessId);
				if(itemId.length()>20) {
					itemModel.set("ITEM_ID", itemId);
					this.getQuery().update(itemModel);
				}else {
					itemModel.setPrimaryValues(RandomKit.uniqueStr());
					this.getQuery().save(itemModel);
				}
			}
		}
	}
	
	private JSONObject getItem(JSONObject jsonObject,String itemId){
		if(StringUtils.isBlank(itemId)){
			return jsonObject;
		}else{
			JSONObject newJsonObject=new JSONObject();
			for(String key :jsonObject.keySet()){
				if(key.endsWith("_"+itemId)){
					newJsonObject.put(key.replace("_"+itemId, "").trim(),jsonObject.get(key));
				}
			}
			return newJsonObject;
		}
	}
	
	
	protected void setApplyNo(EasyRecord applyRecord) {
		String applyNo = applyRecord.getString("APPLY_NO");
		if(StringUtils.isBlank(applyNo)) {
			String prefix = EasyCalendar.newInstance().getFullMonth();
			try {
				String no = this.getQuery().queryForString("select max(apply_no) from yq_flow_apply where apply_no like '"+prefix+"%'");
				String num = prefix+"0001";
				if(StringUtils.isNotBlank(no)) {
					num = String.valueOf(Long.valueOf(no)+1);
				}
				applyRecord.set("APPLY_NO", num);
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
		
	}
	
	protected void delApplyData(String id) {
		try {
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id  = ?", id);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	protected void delApplyDatas(String id,String... params) {
		try {
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_files where fk_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id  = ?", id);
			if(params!=null) {
				for(String obj:params) {
					if(StringUtils.isBlank(obj)) {
						continue;
					}
					if(!"yq_flow_apply".equalsIgnoreCase(obj)) {
						this.getQuery().executeUpdate("delete from "+obj+" where business_id  = ?", id);
					}
				}
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	protected EasyRecord getApproveResultRecord(String businessId) {
		EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
		resultRecord.set("result_id", RandomKit.uniqueStr());
		resultRecord.set("business_id", businessId);
		resultRecord.set("check_name", getUserName());
		resultRecord.set("dept_name", getDeptName());
		resultRecord.set("check_user_id", getUserId());
		resultRecord.set("check_result", 1);
		resultRecord.set("node_id",0);
		resultRecord.set("ip_address",WebKit.getIP(getRequest()));
		resultRecord.set("check_desc","");
		resultRecord.set("check_time",EasyDate.getCurrentDateString());
		resultRecord.set("get_time",EasyDate.getCurrentDateString());
		return resultRecord;
	}
	
	/**
	 * 修改
	 * @param resultId
	 * @param checkResult
	 * @param checkDesc
	 * @return
	 */
	protected EasyRecord getApproveResultRecord(String resultId,int checkResult,String checkDesc) {
		EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
		record.set("result_id", resultId);
		record.set("check_name", getUserName());
		record.set("check_user_id", getUserId());
		record.set("dept_name", getDeptName());
		record.set("check_result",checkResult);
		record.set("check_desc", checkDesc);
		record.set("check_time", EasyDate.getCurrentDateString());
		record.set("ip_address",WebKit.getIP(getRequest()));
		return record;
	}
	
	/**
	 * 新增下节点
	 * @param model
	 * @return
	 */
	protected EasyRecord getApproveResultRecord(ApproveNodeModel nextNodeInfo,String businessId,String resultId,String prevResultId) {
		EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
		record.set("result_id", resultId);
		record.set("prev_result_id", prevResultId);
		record.set("business_id", businessId);
		record.set("check_name", nextNodeInfo.getCheckName());
		record.set("check_user_id", nextNodeInfo.getCheckBy());
		record.set("check_result", 0);
		record.set("node_id",nextNodeInfo.getNodeId());
		record.set("node_name",nextNodeInfo.getNodeName());
		record.set("check_desc","");
		record.set("get_time",EasyDate.getCurrentDateString());
		if(nextNodeInfo.getHourLimit()>0) {
			record.set("over_time",DateUtils.hourAfter(nextNodeInfo.getHourLimit()));
		}
		return record;
	}
	
	protected EasyRecord getApplyRecord(String flowCode) {
		return getApplyRecord(null,flowCode);
	}
	
	protected EasyRecord getApplyRecord(String flowCode,String applyId,String applyState) {
		EasyRecord record =  getApplyRecord(null,flowCode);
		record.set(applyState, applyState);
		record.setPrimaryValues(applyId);
		return record;
	}
	
	protected EasyRecord getApplyRecord() {
		EasyRecord model = new EasyRecord("yq_flow_apply","apply_id"); 
		model.set("update_by",getUserId());
		model.set("update_time",EasyDate.getCurrentDateString());
		return model;
	}
	
	protected EasyRecord getApplyRecord(String id,String flowCode) {
		EasyRecord model = new EasyRecord("yq_flow_apply","apply_id"); 
		model.set("apply_time", EasyDate.getCurrentDateString());
		model.set("apply_date", EasyCalendar.newInstance().getDateInt());
		model.set("apply_by", getUserId());
		model.set("apply_staff_no", getStaffInfo().getStaffNo());
		model.set("apply_name", getUserName());
		model.set("dept_id", getDeptId());
		model.set("dept_name", getDeptName());
		model.set("flow_code", flowCode);
		if(id!=null) {
			model.setPrimaryValues(id);
		}
		return model;
	}
	
	protected EasyResult addReviseLog(String businessId,String relationId,String reviseContent) {
		EasyRecord record = new EasyRecord("yq_flow_revise_record","revise_id");
		try {
			record.setPrimaryValues(RandomKit.uniqueStr());
			record.set("business_id",businessId);
			record.set("relation_id",relationId);
			record.set("revise_content",reviseContent);
			record.set("add_time", EasyDate.getCurrentDateString());
			record.set("add_user_id", getUserId());
			record.set("add_user_name", getUserName());
			this.getQuery().save(record);
			this.getQuery().executeUpdate("update yq_flow_apply set revise_count = revise_count + 1 where apply_id = ?",businessId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	protected EasyResult addReviseLog(String businessId,String reviseContent) {
		return addReviseLog(businessId,null,reviseContent);
	}
	
	protected int addCCLog(EasyQuery query ,String ccIdsStr,String businessId,String resultId) throws SQLException {
		int ccCount = 0;
		if(StringUtils.notBlank(ccIdsStr)) {
			String[] ccIds = ccIdsStr.split(",");
			ccCount = ccIds.length;
			for(String ccId:ccIds) {
				EasyRecord record = new EasyRecord("yq_flow_cc","cc_id");
				record.set("cc_id", RandomKit.uniqueStr());
				record.set("cc_by", ccId);
				record.set("cc_by_name", StaffService.getService().getUserName(ccId));
				record.set("business_id", businessId);
				record.set("create_by", getUserId());
				record.set("create_name", getUserName());
				record.set("result_id", resultId);
				record.set("cc_time", EasyDate.getCurrentDateString());
				query.save(record);
			}
		}
		return ccCount;
	}
}
