package com.yunqu.work.service;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.sql.SQLException;
import java.util.List;

public class ContractNoticeService extends AppBaseService {

    private static class Holder {
        private static ContractNoticeService service = new ContractNoticeService();
    }

    public static ContractNoticeService getService() {
        return ContractNoticeService.Holder.service;
    }

    public void sendContractReceiveNoticeToSale() {
        sendContractReceiveNoticeToSale(EasyCalendar.YEAR, EasyCalendar.MONTH,"");
    }
    public void sendContractReceiveNoticeToSale(String saleId) {
        sendContractReceiveNoticeToSale(EasyCalendar.YEAR, EasyCalendar.MONTH,saleId);
    }
    public void sendContractReceiveNoticeToSale(int year,int month) {
        sendContractReceiveNoticeToSale(year, month,"");
    }

    public void sendContractReceiveNoticeToSale(int year, int month,String saleId) {
        String ddlDate = DateUtils.getMonthEndDay(year, month);
        try {
            EasySQL sql = new EasySQL("select t1.CONTRACT_ID,t2.CONTRACT_NAME,t2.SALES_BY from yq_contract_stage t1");
            sql.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
            sql.append("where 1=1");
            sql.append(saleId,"and t2.SALES_BY = ?");
            sql.append("and t1.ACT_COMP_DATE = '' and (");
            //计划完成时间<= ddl月底
            sql.append(ddlDate, "(t1.PLAN_COMP_DATE IS NOT NULL AND t1.PLAN_COMP_DATE != '' and t1.PLAN_COMP_DATE <= ? )");
            //未填计划完成时间，用 应收时间<= ddl月底
            sql.append(ddlDate, "or ((t1.PLAN_COMP_DATE IS NULL OR t1.PLAN_COMP_DATE = '') and(t1.RCV_DATE IS NOT NULL AND t1.RCV_DATE != '' AND  t1.RCV_DATE <= ? )))");
            sql.append("group by t1.CONTRACT_ID");
            sql.append("order by PLAN_COMP_DATE desc");

            List<JSONObject> contractList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

            if (contractList != null && contractList.size() > 0) {
                for (JSONObject jsonObject : contractList) {
                    String contractId = jsonObject.getString("CONTRACT_ID");
                    this.sendContractReceiveNoticeToSale2(year, month, contractId);
                }
            }
        } catch (SQLException e) {
            this.getLogger().error(e);
        }
    }

    public void sendContractReceiveNoticeToSale2(int year, int month, String contractId) throws SQLException {
        if ("9999999999999".equals(contractId) || "0".equals(contractId)) {
            return;
        }
        String ddlDate = DateUtils.getMonthEndDay(year, month);
        StringBuffer affairDesc = new StringBuffer();
        JSONObject contractInfo = this.getQuery().queryForRow("SELECT CONTRACT_NAME,CONTRACT_NO,ERP_CONTRACT_NO,CUSTOMER_NAME,YEAR,AMOUNT,SALES_BY,SIGN_DATE,SALES_BY_NAME,PM_BY_NAME,PAYEE, JSON_UNQUOTE(JSON_EXTRACT(TEXT_JSON, '$.PAYEE_NAME')) AS PAYEE_NAME from yq_project_contract where CONTRACT_ID = ?", new Object[]{contractId}, new JSONMapperImpl());
        if (contractInfo == null || contractInfo.isEmpty()) {
            return;
        }
        EasySQL sql1 = new EasySQL("SELECT STAGE_NAME,RCV_DATE,PLAN_COMP_DATE,PLAN_RCV_AMT,RECEIVED_AMT,RATIO from yq_contract_stage where 1=1 ");
        sql1.append(contractId, "and CONTRACT_ID = ?");
        sql1.append("and ACT_COMP_DATE = ''");
        sql1.append(ddlDate, "and ((PLAN_COMP_DATE IS NOT NULL AND PLAN_COMP_DATE != '' and PLAN_COMP_DATE <= ? )");
        sql1.append(ddlDate, "or ((PLAN_COMP_DATE IS NULL OR PLAN_COMP_DATE = '') and(RCV_DATE IS NOT NULL AND RCV_DATE != '' AND RCV_DATE <= ? )))");
        sql1.append("order by PLAN_COMP_DATE desc");

        List<JSONObject> stageInfoList = this.getQuery().queryForList(sql1.getSQL(), sql1.getParams(), new JSONMapperImpl());
        if (stageInfoList.size() == 0) {
            this.getLogger().info("sendContractReceiveNoticeToSale>stage is zero.contractId>" + contractId);
            return;
        }
        String contractName = contractInfo.getString("CONTRACT_NAME");

        affairDesc.append("<p><b>" + year + "年" + month + "月" + "【" + contractName + "】待收款提醒</b></p>");
        affairDesc.append("<br>");

        affairDesc.append("<p>合同号：" + emptyStr(contractInfo.getString("CONTRACT_NO")));
        affairDesc.append("，项目经理：" + emptyStr(contractInfo.getString("PM_BY_NAME")));
        affairDesc.append("，销售：" + emptyStr(contractInfo.getString("SALES_BY_NAME")));
        affairDesc.append("，收款负责人：" + emptyStr(contractInfo.getString("PAYEE_NAME")));
        affairDesc.append("，合同金额：" + emptyStr(contractInfo.getString("AMOUNT")));
        affairDesc.append("，签约时间：" + emptyStr(contractInfo.getString("SIGN_DATE")));
        affairDesc.append("</p><br>");

        affairDesc.append("截止[" + ddlDate + "] 待收款合同阶段【" + stageInfoList.size() + "】：");
        for (JSONObject stageInfo : stageInfoList) {
            affairDesc.append("<p>阶段名称：" + stageInfo.getString("STAGE_NAME"));
            affairDesc.append("，计划收款金额：" + emptyStr(stageInfo.getString("PLAN_RCV_AMT")));
            affairDesc.append("，金额占比：" + stageInfo.getDoubleValue("RATIO") + "%");
            affairDesc.append("，应收日期：" + emptyStr(stageInfo.getString("RCV_DATE")));
            affairDesc.append("，计划完成日期：" + emptyStr(stageInfo.getString("PLAN_COMP_DATE")));
            affairDesc.append("，已收款金额：" + emptyStr(stageInfo.getString("RECEIVED_AMT")));
            affairDesc.append("</p>");

        }
        affairDesc.append("<br>");

        this.getLogger().info("contractId>" + contractId + ">" + affairDesc.toString());

        String sales_by = contractInfo.getString("SALES_BY");
        String payee = contractInfo.getString("PAYEE");
        if (StringUtils.notBlank(sales_by)) {
            StaffModel staffModel = StaffService.getService().getStaffInfo(sales_by);
            if (staffModel != null && staffModel.isNormal()) {
                String deptLeaderUserId = staffModel.getDeptLeader();
                String ccIds = deptLeaderUserId;
                //同时发给收款负责人
                String receiver = sales_by + "," + payee;
                AffairService.getService().addAffair("【" + +year + "年" + month + "月" + "】待收款合同-" + contractName, affairDesc.toString(), sales_by, receiver, ccIds);
            } else {
                this.getLogger().error(contractName + ">sales_by>" + sales_by + " is leave");
            }
        } else {
            this.getLogger().error(contractName + ">sales_by is empty");
        }
    }


    private String emptyStr(String str) {
        if (StringUtils.isBlank(str)) {
            return "--";
        }
        return str;
    }


}
