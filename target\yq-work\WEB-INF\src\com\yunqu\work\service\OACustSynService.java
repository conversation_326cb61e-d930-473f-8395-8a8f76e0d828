package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.CustomerModel;

public class OACustSynService extends AppBaseService{
	
	private Logger logger = LogEngine.getLogger(Constants.APP_NAME);
	
	private static class Holder{
		private static OACustSynService service=new OACustSynService();
	}
	public static OACustSynService getService(){
		return Holder.service;
	}
	public String synData(){
        try {
          StringBuffer msg = new StringBuffer();
           OAService.getService().login("100078","10304603");
           String xml = OAService.getService().loadCustXml("100078");
           if(xml==null){
        	   return null;
           }
            xml = xml.replaceAll("\n", "");
            xml = xml.replaceAll("\t", "");
			Document doc = DocumentHelper.parseText(xml);
			Element root = doc.getRootElement();
			@SuppressWarnings("unchecked")
			List<Element> childElements  = root.elements("Item");
			for (Element child : childElements) {
				 Element attr=child.element("ItemAttributes");
				 if(attr!=null){
					 CustomerModel model=new CustomerModel();
					 String id=child.elementText("DocUNID");
					 String custNo = attr.elementText("CustomerID");
					 String Customer = attr.elementText("Customer");
					 String CXingZi = attr.elementText("CXingZi");
					 if("潜在客户".equals(CXingZi)) {
						 CXingZi="1";
					 }else if("曾经客户".equals(CXingZi)) {
						 CXingZi="2";
					 }else if("新客户".equals(CXingZi)) {
						 CXingZi="3";
					 }else if("老客户".equals(CXingZi)) {
						 CXingZi="4";
					 }
					 String CImportant = attr.elementText("CImportant");
					 if("重要".equals(CImportant)) {
						 CImportant="2";
					 }else if("不重要".equals(CImportant)) {
						 CImportant="3";
					 }else if("很重要".equals(CImportant)) {
						 CImportant="1";
					 }else if("VIP".equals(CImportant)) {
						 CImportant="1";
					 }
					 String createTime = attr.elementText("WF_DocCreated");
					 try {
						JSONObject custModel=this.getQuery().queryForRow("select * from yq_crm_customer where cust_id = ?",new String[]{id}, new JSONMapperImpl());
						if(custModel==null||custModel.size()==0){
							 model.set("cust_id",id);
							 model.set("cust_no",custNo);
							 model.set("cust_name",Customer);
							 model.set("CUST_NATURE", CXingZi);
							 model.set("CUST_LEVEL", CImportant);
							 model.set("create_time", createTime);
							 model.set("update_time", EasyDate.getCurrentDateString());
							 model.set("cust_state", 10);
							 model.set("creator", "7acdd364b8974ed2936d565e6f8bf8ea");
							 model.set("owner_id", "7acdd364b8974ed2936d565e6f8bf8ea");
							try {
								model.save();
							} catch (SQLException e) {
								logger.error(e.getMessage(),e);
							}
						 }
					} catch (SQLException e1) {
						logger.error(null,e1);
					}
				 }
			 }
			 try {
				this.getQuery().executeUpdate("update yq_project_contract t1,yq_crm_customer t2 set t1.CUST_ID=t2.cust_id where t1.CUST_NO=t2.cust_no and  t1.CUST_NO<>'' and t1.CUST_ID =''");
			} catch (SQLException e) {
				logger.error(e.getMessage(),e);
			}
			return msg.toString();
		} catch (DocumentException e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}
	
}







