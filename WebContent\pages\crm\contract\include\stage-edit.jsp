<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>project</title>
    <style>
        .form-horizontal {
            width: 100%;
        }
        .select2 {z-index: 90;}
        .select2-container{width: 100%!important;}
        .select2-container--open .select2-dropdown{left:0;z-index:999999999;}
        .select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
            background-color: #fff;
        }
    </style>
    <link href="/easitline-static/lib/select2/css/select2.min.css" rel="stylesheet">
    <link href="/easitline-static/lib/select2/css/select2-bootstrap.min.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
    <form id="stageEditForm" class="form-horizontal" data-mars="ContractStageDao.record" autocomplete="off"
          data-mars-prefix="contractStage.">
        <input type="hidden" value="${param.stageId}" name="contractStage.STAGE_ID"/>
        <input type="hidden" value="${param.stageId}" name="stageId"/>
        <input type="hidden" name="contractStage.CONTRACT_ID" value="${param.contractId}">
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td class="required">阶段名称</td>
                <td colspan="3">
                    <SELECT data-rules="required" id="selectStageName" name="contractStage.STAGE_NAME" class="form-control"
<%--                            <c:if test="${param.isIncomeStage == '1'}"> disabled </c:if> --%>
                    >
                        <option value="">--请选择--</option>
                        <option value="预付款">预付款</option>
                        <option value="货到款">货到款</option>
                        <option value="上线款">上线款</option>
                        <option value="分成款">分成款</option>
                        <option value="阶段款">阶段款</option>
                        <option value="初验款">初验款</option>
                        <option value="终验款">终验款</option>
                        <option value="竣工款">竣工款</option>
                        <option value="质保款">质保款</option>
                        <option value="第一期">第一期</option>
                        <option value="第二期">第二期</option>
                        <option value="第三期">第三期</option>
                        <option value="第四期">第四期</option>
                        <option value="第五期">第五期</option>
                        <option value="第六期">第六期</option>
                        <option value="第七期">第七期</option>
                        <option value="第八期">第八期</option>
                        <option value="第九期">第九期</option>
                        <option value="第九期">第九期</option>
                        <option value="第十期">第十期</option>
                        <option value="第十一期">第十一期</option>
                        <option value="第十二期">第十二期</option>
                        <option value="第十三期">第十三期</option>
                        <option value="第十四期">第十四期</option>
                        <option value="第十五期">第十五期</option>
                        <option value="第十六期">第十六期</option>
                        <option value="第十七期">第十七期</option>
                        <option value="第十八期">第十八期</option>
                        <option value="第十九期">第十九期</option>
                        <option value="第二十期">第二十期</option>
                        <option value="第二十一期">第二十一期</option>
                        <option value="第二十二期">第二十二期</option>
                        <option value="第二十三期">第二十三期</option>
                        <option value="第二十四期">第二十四期</option>
                        <option value="第二十五期">第二十五期</option>
                        <option value="第二十六期">第二十六期</option>
                        <option value="第二十七期">第二十七期</option>
                        <option value="第二十八期">第二十八期</option>
                        <option value="第二十九期">第二十九期</option>
                        <option value="第三十期">第三十期</option>
                        <option value="第三十一期">第三十一期</option>
                        <option value="第三十二期">第三十二期</option>
                        <option value="第三十三期">第三十三期</option>
                        <option value="第三十四期">第三十四期</option>
                        <option value="第三十五期">第三十五期</option>
                        <option value="第三十六期">第三十六期</option>
                    </SELECT>
                </td>
            </tr>
            <tr>
                <td style="min-width: 110px" class="required">是否应收</td>
                <td>
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio" value="Y" name="contractStage.IS_RECEIVE"><span>是</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="N" name="contractStage.IS_RECEIVE"><span>否</span>
                    </label>
                </td>
                <td style="min-width: 110px">应收时间</td>
                <td>
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contractStage.RCV_DATE"
                           class="form-control input-sm Wdate">
                </td>
            </tr>
            <tr>
                    <td>计划完成时间</td>
                    <td>
                        <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                               name="contractStage.PLAN_COMP_DATE" class="form-control input-sm Wdate">
                    </td>
                    <td>实际完成时间</td>
                    <td>
                        <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                               name="contractStage.ACT_COMP_DATE" class="form-control input-sm Wdate">
                    </td>
            </tr>
            <tr>
                <td class="required">比例(%)</td>
                <td>
                    <input type="number"
                    <%--                    <c:if test="${param.isIncomeStage == '1'}"> readonly </c:if> --%>
                           data-rules="required"
                           name="contractStage.RATIO" class="form-control input-sm" oninput="StageEdit.calcPlanAmt(this.value)"
                           placeholder="填写自动计算金额">
                </td>
                <td class="required">计划收款金额</td>
                <td>
                    <input type="number"
<%--                    <c:if test="${param.isIncomeStage == '1'}"> readonly </c:if> --%>
                           name="contractStage.PLAN_RCV_AMT"
                           data-rules="required" class="form-control input-sm" oninput="StageEdit.calcStageRatio()"
                           placeholder="填写自动计算比例">
                </td>
            </tr>
            <tr>
                <td>余款维保条件</td>
                <td>
                    <input type="text" name="contractStage.YK_WB_COND" class="form-control input-sm">
                </td>
                <td>余款条件天数</td>
                <td>
                    <input type="text" name="contractStage.YK_COND_DAYS" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>坏账金额</td>
                <td colspan="3">
                    <input type="number" name="contractStage.BAD_DEBT_AMT" class="form-control input-sm" value="0">
                </td>
            </tr>
            </tbody>
        </table>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px"
                    onclick="StageEdit.ajaxSubmitForm()"> 保 存
            </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px"
                    onclick="layer.closeAll();"> 关闭
            </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="${ctxPath}/static/js/pinyin.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/lib/select2/select2.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("StageEdit");

        StageEdit.stageId = '${param.stageId}';
        StageEdit.isNew = '${param.isNew}';
        StageEdit.contractId = '${param.contractId}';
        StageEdit.amount = '${param.amount}';
        <%--StageEdit.isIncomeStage = '${param.isIncomeStage}';--%>

        $(function () {
            $("#stageEditForm").render({
                success: function (result) {
                    $("#selectStageName").select2({theme: "bootstrap",placeholder:'请选择(输入拼音可搜索)'});
                }
            });
        });

        StageEdit.ajaxSubmitForm = function () {
            if (form.validate("#stageEditForm")) {
                if (StageEdit.isNew == '1') {
                    StageEdit.insertData();
                } else if (StageEdit.stageId) {
                    StageEdit.updateData();
                } else {
                    StageEdit.insertData();
                }
            }
            ;
        }

        StageEdit.insertData = function () {
            var data = form.getJSONObject("#stageEditForm");
            ajax.remoteCall("${ctxPath}/servlet/contractStage?action=add", data, function (result) {
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadStageList();
                    });
                } else {
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        StageEdit.updateData = function () {
            var data = form.getJSONObject("#stageEditForm");
            ajax.remoteCall("${ctxPath}/servlet/contractStage?action=update", data, function (result) {
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadStageList();
                    });
                } else {
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        StageEdit.calcPlanAmt = function (ratio) {
            var ratio = parseFloat(ratio);
            if (isNaN(ratio) || ratio == '') {
                ratio = 0;
            }
            if (ratio > 100) {
                layer.alert("占合同总金额比例不能大于100%", {icon: 5});
                return;
            }
            var amount = parseFloat(StageEdit.amount);
            var planAmt = amount * ratio / 100;
            $("#stageEditForm input[name='contractStage.PLAN_RCV_AMT']").val(planAmt.toFixed(2))
        }

        StageEdit.calcStageRatio = function () {
            var planAmt = parseFloat($("input[name='contractStage.PLAN_RCV_AMT']").val());
            if (isNaN(planAmt) || planAmt == '') {
                planAmt = 0;
            }
            var contractAmount = parseFloat(StageEdit.amount);
            if (planAmt > contractAmount) {
                layer.alert("阶段金额不能大于合同金额", {icon: 5});
                return;
            }
            if (planAmt < 0) {
                layer.alert("阶段金额不能小于0", {icon: 5});
                return;
            }
            var ratio = planAmt * 100 / contractAmount;
            $("#stageEditForm input[name='contractStage.RATIO']").val(ratio.toFixed(2))
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>