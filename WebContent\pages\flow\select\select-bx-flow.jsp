<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>选择客户</title>
</EasyTag:override>
<EasyTag:override name="content">
    <form method="post" class="form-inline" onsubmit="return false;" id="selecFlowForm">
        <input name="flowCode" type="hidden" value="${param.flowCode}">
        <div class="row">
            <div class="input-group input-group-sm ml-15" style="width: 160px">
                <span class="input-group-addon">名称</span>
                <input type="text" name="applyTitle" class="form-control input-sm">
            </div>
            <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectFlow.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
        </div>
        <div class="ibox">
            <table id="SelectFlowList"></table>
        </div>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary"  type="button" onclick="SelectFlow.ok()">确定</button>
            <button class="btn btn-sm btn-default ml-10"  type="button" onclick="SelectFlow.clear(this)">清空</button>
            <button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">

    <script type="text/javascript">
        var SelectFlow={
            sid:'${param.sid}',
            query : function(){
                $("#selecFlowForm").queryData();
            },
            initTable : function(){
                $("#selecFlowForm").initTable({
                        mars:'FlowDao.selectBxFlow',
                        id:'SelectFlowList',
                        page:true,
                        limit:30,
                        height:'full-160',
                        rowEvent:'rowEvent',
                        rowDoubleEvent:'SelectFlow.ok',
                        cols: [[
                            {type:'${param.type}'},
                            {
                                field: 'APPLY_TITLE',
                                title: '流程名称'
                            },{
                                field:'APPLY_REMARK',
                                title:'申请说明',
                                width:100
                            },{
                                field: 'BX_MONEY',
                                title: '报销金额',
                                width:80
                            },{
                                field: 'NO_TAX_MONEY',
                                title: '去税金额',
                                width:80
                            },{
                                field: 'TAX_MONEY',
                                title: '税金',
                                width:80
                            },{
                                field: 'APPLY_TIME',
                                title: '申请时间',
                                width:120
                            }
                        ]]
                    }
                );
            },
            ok : function(selectRow){
                var el = $("[data-sid='"+SelectFlow.sid+"']");
                if(selectRow==undefined){
                    var checkStatus = table.checkStatus('SelectFlowList');
                    if(checkStatus.data.length>0){
                        var names = [];
                        var ids = [];
                        var nos = [];
                        var data = checkStatus.data;
                        for(var index in data){
                            names.push(data[index]['APPLY_TITLE']);
                            ids.push(data[index]['APPLY_ID']);
                            nos.push(data[index]['APPLY_NO']);
                        }
                        el.val(names.join(','));
                        if(el.prev().length>0){
                            el.prev().val(ids.join(','));
                        }
                        if(el.prev().prev().length>0){
                            el.prev().prev().val(nos.join(','));
                        }
                        try{
                            if($.isFunction(selctFlowCallBack)){
                                selctFlowCallBack(data.length>1?data:data[0]);
                            }
                        }catch(e){}
                        popup.layerClose("selecFlowForm");
                    }else{
                        el.val('');
                        if(el.prev().length>0){
                            el.prev().val('');
                        }
                    }
                }else{
                    el.val(selectRow['APPLY_TITLE']);
                    if(el.prev().length>0){
                        el.prev().val(selectRow['APPLY_ID']);
                    }
                    if(el.prev().prev().length>0){
                        el.prev().prev().val(selectRow['APPLY_NO']);
                    }
                    try{
                        if($.isFunction(selctFlowCallBack)){
                            selctFlowCallBack(selectRow);
                        }
                    }catch(e){}
                    popup.layerClose("selecFlowForm");
                }
            },
            clear:function(){
                var el = $("[data-sid='"+SelectFlow.sid+"']");
                el.val('');
                if(el.prev().length>0){
                    el.prev().val('');
                }
                popup.layerClose("selecFlowForm");
            }

        };
        $(function(){
            SelectFlow.initTable();
        });
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>