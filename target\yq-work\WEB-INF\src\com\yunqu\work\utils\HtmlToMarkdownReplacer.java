package com.yunqu.work.utils;

public class HtmlToMarkdownReplacer {

    public static String convert(String html) {
        if (html == null) {
            return null;
        }

        // 替换<br>标签为换行符
        html = html.replace("<br>", "\n")
                    .replace("<br/>", "\n")
                    .replace("<br >", "\n");

        // 替换<p>标签为换行符
        html = html.replace("</p>", "\n\n")
                    .replace("<P>", "\n\n")
                    .replace("</P>", "\n\n");

        // 替换<div>标签为换行符
        html = html.replace("</div>", "\n\n")
                    .replace("<div>", "\n\n")
                    .replace("<div ", "\n\n");

        // 替换<h1>, <h2>, <h3>, <h4>, <h5>, <h6>标签为Markdown格式
        html = html.replaceAll("<h1>(.+?)</h1>", "# $1\n\n");
        html = html.replaceAll("<h2>(.+?)</h2>", "## $1\n\n");
        html = html.replaceAll("<h3>(.+?)</h3>", "### $1\n\n");
        html = html.replaceAll("<h4>(.+?)</h4>", "#### $1\n\n");
        html = html.replaceAll("<h5>(.+?)</h5>", "##### $1\n\n");
        html = html.replaceAll("<h6>(.+?)</h6>", "###### $1\n\n");

        // 替换<strong>和<b>标签为Markdown加粗格式
        html = html.replace("<strong>", "**")
                    .replace("</strong>", "**")
                    .replace("<b>", "**")
                    .replace("</b>", "**");

        // 替换<em>和<i>标签为Markdown斜体格式
        html = html.replace("<em>", "*")
                    .replace("</em>", "*")
                    .replace("<i>", "*")
                    .replace("</i>", "*");

        // 移除剩余的HTML标签
        html = html.replaceAll("<[^>]+>", "");

        return html;
    }

    public static void main(String[] args) {
        String html = "<div>Hello <strong>world</strong>!</div><div>This is a <em>test</em> paragraph.</div>";
        String markdown = convert(html);
        System.out.println(markdown);
    }
}
