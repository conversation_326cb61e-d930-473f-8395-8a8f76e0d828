<!-- Generated by SpecStory -->

# 考勤打卡记录查询与H5页面实现 (2025-04-02 20:27:43)

_**User**_

参考项目代码写一个我最近一个月考勤打卡记录，在KqDao实现查询的代码，H5写页面代码

---

_**Assistant**_

```thinking
让我思考一下如何实现这个功能...

首先需要理解当前的需求和现有代码:
1. 需要查看最近一个月的考勤打卡记录
2. 从现有代码看:
- KqDao.java 中已有考勤相关的查询方法
- 有 kq-record-h5.html 但是是空文件
- wx/kq.jsp 是打卡页面,可以参考其UI风格
- 使用了 weui 的UI组件库

实现思路:
1. 在 KqDao 中添加查询最近一个月记录的方法
- 需要按日期倒序排列
- 需要限制只查最近一个月
- 需要返回打卡时间、打卡类型等信息

2. H5页面设计:
- 参考 wx/kq.jsp 的样式
- 使用 weui 组件
- 显示打卡列表,包含:
  * 日期
  * 上班打卡时间
  * 下班打卡时间
  * 打卡状态(正常/异常)
  * 打卡地点

3. 页面交互:
- 页面加载时自动查询
- 可以下拉刷新
- 使用 weui 的 cell 组件展示列表

让我开始编写代码...
```

首先在KqDao.java中添加查询方法:

---

```diff
  		return queryForPageList(sql.getSQL(), sql.getParams());
  	}
+ 	
+ 	@WebControl(name="recentRecord",type=Types.LIST) 
+ 	public JSONObject recentRecord(){
+ 		EasySQL sql = getEasySQL("select * from yq_kq_data where 1=1");
+ 		sql.append(getUserId(),"and user_Id = ?");
+ 		sql.append("and DK_DATE >= date_format(date_sub(now(),interval 1 month),'%Y%m%d')");
+ 		sql.append("order by DK_DATE desc, SIGN_IN desc");
+ 		return queryForPageList(sql.getSQL(), sql.getParams());
+ 	}
  	
  	private void setCondition(EasySQL sql) {
```

---

接下来创建H5页面:

---

```diff
- 
+ <!DOCTYPE html>
+ <html>
+ <head>
+     <title>考勤记录</title>
+     <meta charset="utf-8">
+     <meta http-equiv="pragma" content="no-cache">
+     <meta name="viewport" content="initial-scale=1.0, user-scalable=no"/>
+     <meta http-equiv="cache-control" content="no-cache">
+     <meta http-equiv="expires" content="0">
+     <link rel="stylesheet" href="https://res.wx.qq.com/open/libs/weui/1.1.3/weui.min.css">
+     <style type="text/css">
+         .weui-cells{font-size: 14px;}
+         .weui-cell__ft{font-size: 13px;color:#666;}
+         .weui-cell__bd{color:#333;}
+         .late{color:red;}
+         .normal{color:green;}
+         .header{
+             padding: 15px;
+             text-align: center;
+             font-size: 16px;
+             color: #333;
+             background: #f8f8f8;
+             border-bottom: 1px solid #eee;
+         }
+     </style>
+ </head>
+ <body ontouchstart>
+     <div class="header">最近一个月考勤记录</div>
+     <div class="weui-cells" id="recordList">
+     </div>
+ 
+     <script src="https://weui.io/zepto.min.js"></script>
+     <script src="https://res.wx.qq.com/open/libs/weuijs/1.2.1/weui.min.js"></script>
+     <script type="text/javascript">
+         $(function(){
+             loadRecords();
+ 
+             // 下拉刷新
+             var startY = 0;
+             $(document).on('touchstart', function(e){
+                 startY = e.touches[0].pageY;
+             });
+             
+             $(document).on('touchmove', function(e){
+                 var moveY = e.touches[0].pageY;
+                 if(moveY - startY > 100 && $(window).scrollTop() === 0){
+                     loadRecords();
+                 }
+             });
+         });
+ 
+         function loadRecords(){
+             weui.loading('加载中...');
+             $.ajax({
+                 url:'/yq-work/servlet/kaoqin?action=recentRecord',
+                 type:'post',
+                 dataType:'json',
+                 success:function(res){
+                     weui.loading().hide();
+                     if(res.state == 1){
+                         var html = '';
+                         var data = res.data;
+                         if(data && data.length > 0){
+                             data.forEach(function(item){
+                                 var date = item.DK_DATE.substring(0,4) + '-' + 
+                                          item.DK_DATE.substring(4,6) + '-' + 
+                                          item.DK_DATE.substring(6,8);
+                                 
+                                 var signInClass = item.LATE_TIME ? 'late' : 'normal';
+                                 var signOutClass = item.OVER_TIME ? 'normal' : '';
+                                 
+                                 html += '<div class="weui-cell">' +
+                                     '<div class="weui-cell__bd">' + date + '</div>' +
+                                     '<div class="weui-cell__ft">' +
+                                     '<span class="' + signInClass + '">上班:' + (item.SIGN_IN || '--:--') + '</span>' +
+                                     ' / ' +
+                                     '<span class="' + signOutClass + '">下班:' + (item.SIGN_OUT || '--:--') + '</span>' +
+                                     '</div>' +
+                                     '</div>';
+                                 
+                                 if(item.LATE_TIME || item.address){
+                                     html += '<div class="weui-cell">' +
+                                         '<div class="weui-cell__ft" style="font-size:12px;">';
+                                     if(item.LATE_TIME){
+                                         html += '<span class="late">迟到:' + item.LATE_TIME + '</span> ';
+                                     }
+                                     if(item.address){
+                                         html += item.address;
+                                     }
+                                     html += '</div></div>';
+                                 }
+                             });
+                         }else{
+                             html = '<div class="weui-cell">' +
+                                 '<div class="weui-cell__bd">暂无打卡记录</div>' +
+                                 '</div>';
+                         }
+                         $('#recordList').html(html);
+                     }else{
+                         weui.toast(res.msg, 3000);
+                     }
+                 },
+                 error:function(){
+                     weui.loading().hide();
+                     weui.toast('网络出现故障', 3000);
+                 }
+             });
+         }
+     </script>
+ </body>
+ </html>
+ 
```

---

```thinking
让我解释一下实现的功能:

1. KqDao中新增的recentRecord方法:
- 查询yq_kq_data表获取打卡记录
- 使用date_sub和date_format限制只查最近一个月的记录
- 按日期和打卡时间倒序排列
- 只查询当前登录用户的记录

2. H5页面实现:
- 使用weui的样式组件
- 页面布局:
  * 顶部标题
  * 打卡记录列表
  * 每条记录显示日期、上下班时间
  * 迟到显示红色,加班显示绿色
  * 显示打卡地点和异常信息

3. 交互功能:
- 页面加载自动查询数据
- 实现下拉刷新功能
- 使用loading提示加载状态
- 错误提示使用weui的toast组件

4. 样式设计:
- 保持与现有打卡页面风格一致
- 使用weui的cell组件展示列表
- 合理的字体大小和颜色搭配
- 清晰的信息层级

主要特点:
1. 简洁清晰的界面
2. 完整的打卡信息展示
3. 良好的交互体验
4. 异常情况的醒目提示
5. 移动端适配良好

这样用户可以方便地查看自己最近一个月的考勤记录,包括上下班时间、迟到早退、打卡地点等信息。
```

让我用中文总结一下实现的功能:

1. 在KqDao中添加了查询最近一个月考勤记录的方法recentRecord:
- 查询条件限制为最近一个月
- 只查询当前登录用户的记录
- 按日期倒序排列

2. 创建了移动端考勤记录查询页面:
- 页面顶部显示"最近一个月考勤记录"标题
- 使用weui组件库实现列表展示
- 每条记录显示:
  * 打卡日期
  * 上班打卡时间(迟到显示红色)
  * 下班打卡时间(加班显示绿色) 
  * 迟到时长(如果有)
  * 打卡地点(如果有)

3. 实现了以下交互功能:
- 页面加载时自动查询数据
- 支持下拉刷新更新数据
- 加载中显示loading提示
- 出错时显示友好提示

4. 页面样式:
- 整体风格与现有打卡页面保持一致
- 使用合适的字体大小和颜色
- 异常情况(如迟到)使用醒目的红色提示
- 布局清晰,信息层次分明

这样员工可以随时查看自己最近一个月的考勤记录,包括上下班打卡时间、是否迟到早退、打卡地点等信息,方便员工了解自己的考勤情况。

---

_**User**_

h5补上字段REASON_IN,REASON_OUT，当不为空才显示，就是签到签退的额外备注，还有MREARK字段，如果存在也显示

---

_**Assistant**_

```thinking
我来分析一下需要添加的内容:

1. 需要在打卡记录中显示3个额外字段:
- REASON_IN: 签到备注
- REASON_OUT: 签退备注  
- MREARK: 其他备注

2. 显示规则:
- 只有当这些字段不为空时才显示
- 应该放在打卡时间下方,作为额外信息展示
- 可以用小字体,灰色文字展示

3. 修改思路:
- 在现有的item.LATE_TIME || item.address判断中添加这3个字段的判断
- 使用同样的样式(weui-cell__ft)展示
- 用不同的颜色或样式区分不同类型的备注

让我来修改H5页面代码...
```

---

```diff
          .late{color:red;}
          .normal{color:green;}
+         .remark{color:#666;font-size:12px;}
          .header{
              padding: 15px;
                                      '</div>';
                                  
-                                 if(item.LATE_TIME || item.address){
+                                 // 显示额外信息
+                                 if(item.LATE_TIME || item.address || item.REASON_IN || item.REASON_OUT || item.MREARK){
                                      html += '<div class="weui-cell">' +
                                          '<div class="weui-cell__ft" style="font-size:12px;">';
                                      }
                                      if(item.address){
-                                         html += item.address;
+                                         html += item.address + ' ';
+                                     }
+                                     if(item.REASON_IN){
+                                         html += '<div class="remark">签到备注: ' + item.REASON_IN + '</div>';
+                                     }
+                                     if(item.REASON_OUT){
+                                         html += '<div class="remark">签退备注: ' + item.REASON_OUT + '</div>';
+                                     }
+                                     if(item.MREARK){
+                                         html += '<div class="remark">其他备注: ' + item.MREARK + '</div>';
                                      }
                                      html += '</div></div>';
```

---

```thinking
我来解释一下对代码的修改:

1. 样式修改:
- 添加了.remark类样式
- 使用灰色(#666)和12px字体大小
- 确保备注信息与其他信息区分开

2. 显示逻辑修改:
- 在判断是否显示额外信息时,添加了对REASON_IN、REASON_OUT和MREARK的判断
- 只有当这些字段不为空时才会显示对应的内容
- 每个备注都单独一行显示,使用div标签换行
- 给每个备注加上了说明文字(签到备注/签退备注/其他备注)

3. 显示格式:
- 签到备注: [内容]
- 签退备注: [内容]  
- 其他备注: [内容]

4. 其他优化:
- 在address后面添加了空格,避免文字挤在一起
- 使用统一的remark样式,保持视觉一致性

现在页面可以完整显示打卡记录的所有信息,包括:
- 打卡日期和时间
- 迟到/加班标记
- 打卡地点
- 签到备注
- 签退备注  
- 其他备注

布局清晰,信息层次分明,用户可以很容易地查看所有考勤相关信息。
```

让我用中文总结一下修改:

1. 在页面中添加了三个备注字段的显示:
- REASON_IN: 签到备注
- REASON_OUT: 签退备注
- MREARK: 其他备注

2. 显示规则:
- 只有当字段不为空时才显示
- 每个备注单独一行显示
- 使用灰色小字体(12px)显示
- 每个备注都有对应的说明文字

3. 显示效果:
- 在打卡时间下方显示
- 与地点、迟到信息一起显示
- 使用统一的样式保持视觉一致性
- 信息层次清晰,易于阅读

4. 具体格式:
```
2024-03-20
上班:08:30 / 下班:17:30
迟到:10分钟 广州市天河区
签到备注: 堵车
签退备注: 加班
其他备注: 项目紧急
```

这样员工可以看到更完整的考勤信息,包括迟到早退的原因说明和其他相关备注信息。

---

_**User**_

address不准确，因为上班是ADDRESS_IN 下班是ADDRESS_OUT

---

_**Assistant**_

```thinking
明白了,需要修改address的显示逻辑:

1. 原来使用的item.address需要改为:
- ADDRESS_IN: 上班打卡地点
- ADDRESS_OUT: 下班打卡地点

2. 显示规则:
- 如果有上班打卡地点,显示"上班地点: xxx"
- 如果有下班打卡地点,显示"下班地点: xxx"
- 与其他备注保持一致的样式
- 按照时间顺序,上班地点在前,下班地点在后

3. 修改思路:
- 修改判断条件,加入ADDRESS_IN和ADDRESS_OUT
- 移除原来的address判断
- 使用同样的remark样式显示地点信息

让我修改代码...
```

---

```diff
                                  
                                  // 显示额外信息
-                                 if(item.LATE_TIME || item.address || item.REASON_IN || item.REASON_OUT || item.MREARK){
+                                 if(item.LATE_TIME || item.ADDRESS_IN || item.ADDRESS_OUT || item.REASON_IN || item.REASON_OUT || item.MREARK){
                                      html += '<div class="weui-cell">' +
                                          '<div class="weui-cell__ft" style="font-size:12px;">';
                                      if(item.LATE_TIME){
                                          html += '<span class="late">迟到:' + item.LATE_TIME + '</span> ';
                                      }
-                                     if(item.address){
-                                         html += item.address + ' ';
+                                     if(item.ADDRESS_IN){
+                                         html += '<div class="remark">上班地点: ' + item.ADDRESS_IN + '</div>';
                                      }
                                      if(item.REASON_IN){
                                          html += '<div class="remark">签到备注: ' + item.REASON_IN + '</div>';
                                      }
+                                     if(item.ADDRESS_OUT){
+                                         html += '<div class="remark">下班地点: ' + item.ADDRESS_OUT + '</div>';
+                                     }
                                      if(item.REASON_OUT){
                                          html += '<div class="remark">签退备注: ' + item.REASON_OUT + '</div>';
```

---

```thinking
我来解释一下对代码的修改:

1. 修改了判断条件:
- 移除了item.address
- 添加了item.ADDRESS_IN和item.ADDRESS_OUT的判断

2. 显示顺序调整:
- 上班相关信息放在一起:
  * 上班地点(ADDRESS_IN)
  * 签到备注(REASON_IN)
- 下班相关信息放在一起:
  * 下班地点(ADDRESS_OUT) 
  * 签退备注(REASON_OUT)
- 其他备注(MREARK)放在最后

3. 显示格式:
- 使用统一的remark样式
- 每个信息独占一行
- 清晰的信息标识:
  * "上班地点: xxx"
  * "下班地点: xxx"

4. 信息展示顺序的逻辑性:
迟到信息 -> 上班信息(地点+备注) -> 下班信息(地点+备注) -> 其他备注

现在打卡记录会显示更准确的地点信息,并且信息排列更有逻辑性。
```

让我用中文总结一下修改:

1. 地点显示的修改:
- 原来的address字段拆分为:
  * ADDRESS_IN: 上班打卡地点
  * ADDRESS_OUT: 下班打卡地点

2. 显示顺序调整为:
```
2024-03-20
上班:08:30 / 下班:17:30
迟到:10分钟
上班地点: 广州市天河区xxx
签到备注: 堵车
下班地点: 广州市天河区xxx
签退备注: 加班
其他备注: 项目紧急
```

3. 具体改动:
- 修改了判断条件,使用ADDRESS_IN和ADDRESS_OUT替代address
- 调整了显示顺序,使相关信息集中显示
- 保持了统一的样式和格式
- 每个信息都有清晰的标识

这样修改后,打卡记录的地点信息更准确,显示更合理,便于用户查看完整的考勤信息。

---

_**User**_

截图的排版有点丑 优化下

---

_**Assistant**_

```thinking
看了截图,确实需要优化一下排版,主要问题:

1. 日期和打卡时间的布局不够协调:
- 日期在左边
- 打卡时间在右边,颜色区分不明显

2. 地点和备注信息排版拥挤:
- 所有信息都是灰色,层次不分明
- 行间距太小
- 地点和备注没有很好的视觉分组

优化思路:
1. 调整日期和打卡时间的样式:
- 日期加粗显示
- 上下班时间右对齐,颜色更鲜明

2. 优化地点和备注信息:
- 增加行间距
- 地点信息和备注信息分组
- 使用不同的缩进
- 调整字体大小区分主次信息

3. 整体视觉优化:
- 增加卡片式样式
- 添加适当的内边距
- 使用更清晰的分割线

让我来修改代码...
```

---

```diff
      <link rel="stylesheet" href="https://res.wx.qq.com/open/libs/weui/1.1.3/weui.min.css">
      <style type="text/css">
-         .weui-cells{font-size: 14px;}
+         .weui-cells{font-size: 14px;margin-top:0;}
+         .weui-cell{padding: 12px 15px;}
          .weui-cell__ft{font-size: 13px;color:#666;}
-         .weui-cell__bd{color:#333;}
-         .late{color:red;}
-         .normal{color:green;}
-         .remark{color:#666;font-size:12px;}
+         .weui-cell__bd{color:#333;font-weight: 500;}
+         .late{color:#ff4949;}
+         .normal{color:#67c23a;}
+         .remark{color:#666;font-size:12px;line-height:1.8;padding-left:10px;}
          .header{
              padding: 15px;
              background: #f8f8f8;
              border-bottom: 1px solid #eee;
+             position: sticky;
+             top: 0;
+             z-index: 1;
+         }
+         .record-card {
+             margin: 10px;
+             background: #fff;
+             border-radius: 8px;
+             box-shadow: 0 1px 3px rgba(0,0,0,0.1);
+             overflow: hidden;
+         }
+         .record-header {
+             padding: 12px 15px;
+             border-bottom: 1px solid #eee;
+             display: flex;
+             justify-content: space-between;
+             align-items: center;
+         }
+         .record-date {
+             font-size: 15px;
+             font-weight: 500;
+             color: #333;
+         }
+         .record-time {
+             text-align: right;
+         }
+         .record-content {
+             padding: 10px 15px;
+         }
+         .record-group {
+             margin-bottom: 8px;
+         }
+         .record-group:last-child {
+             margin-bottom: 0;
+         }
+         body {
+             background: #f5f5f5;
          }
      </style>
  </head>
  <body ontouchstart>
      <div class="header">最近一个月考勤记录</div>
-     <div class="weui-cells" id="recordList">
+     <div id="recordList">
      </div>
  
                                  var signOutClass = item.OVER_TIME ? 'normal' : '';
                                  
-                                 html += '<div class="weui-cell">' +
-                                     '<div class="weui-cell__bd">' + date + '</div>' +
-                                     '<div class="weui-cell__ft">' +
-                                     '<span class="' + signInClass + '">上班:' + (item.SIGN_IN || '--:--') + '</span>' +
+                                 html += '<div class="record-card">' +
+                                     '<div class="record-header">' +
+                                     '<div class="record-date">' + date + '</div>' +
+                                     '<div class="record-time">' +
+                                     '<span class="' + signInClass + '">上班 ' + (item.SIGN_IN || '--:--') + '</span>' +
                                      ' / ' +
-                                     '<span class="' + signOutClass + '">下班:' + (item.SIGN_OUT || '--:--') + '</span>' +
+                                     '<span class="' + signOutClass + '">下班 ' + (item.SIGN_OUT || '--:--') + '</span>' +
                                      '</div>' +
                                      '</div>';
  
                                  // 显示额外信息
                                  if(item.LATE_TIME || item.ADDRESS_IN || item.ADDRESS_OUT || item.REASON_IN || item.REASON_OUT || item.MREARK){
-                                     html += '<div class="weui-cell">' +
-                                         '<div class="weui-cell__ft" style="font-size:12px;">';
+                                     html += '<div class="record-content">';
+                                     
+                                     // 上班信息组
+                                     if(item.ADDRESS_IN || item.REASON_IN){
+                                         html += '<div class="record-group">';
                                          if(item.LATE_TIME){
-                                         html += '<span class="late">迟到:' + item.LATE_TIME + '</span> ';
+                                             html += '<div class="remark late">迟到: ' + item.LATE_TIME + '</div>';
                                          }
                                          if(item.ADDRESS_IN){
                                              html += '<div class="remark">上班地点: ' + item.ADDRESS_IN + '</div>';
+                                         }
+                                         if(item.REASON_IN){
+                                             html += '<div class="remark">签到备注: ' + item.REASON_IN + '</div>';
+                                         }
+                                         html += '</div>';
                                      }
+                                     
+                                     // 下班信息组
+                                     if(item.ADDRESS_OUT || item.REASON_OUT){
+                                         html += '<div class="record-group">';
                                          if(item.ADDRESS_OUT){
                                              html += '<div class="remark">下班地点: ' + item.ADDRESS_OUT + '</div>';
-                                     }
-                                     if(item.REASON_IN){
-                                         html += '<div class="remark">签到备注: ' + item.REASON_IN + '</div>';
                                          }
                                          if(item.REASON_OUT){
                                              html += '<div class="remark">签退备注: ' + item.REASON_OUT + '</div>';
+                                         }
+                                         html += '</div>';
                                      }
+                                     
+                                     // 其他备注
                                      if(item.MREARK){
-                                         html += '<div class="remark">其他备注: ' + item.MREARK + '</div>';
+                                         html += '<div class="record-group">' +
+                                             '<div class="remark">其他备注: ' + item.MREARK + '</div>' +
+                                             '</div>';
                                      }
-                                     html += '</div></div>';
+                                     
+                                     html += '</div>';
                                  }
+                                 
+                                 html += '</div>';
                              });
                          }else{
```

---

```thinking
我来解释一下样式优化的改动:

1. 整体布局优化:
- 添加卡片式布局(record-card)
- 设置圆角和阴影效果
- 背景色改为浅灰色(#f5f5f5)
- 增加卡片间距

2. 标题栏优化:
- 设置为sticky定位,滚动时固定在顶部
- 增加z-index确保显示在最上层

3. 打卡记录头部优化:
- 使用flex布局,左右对齐
- 日期加粗显示,字号增大
- 打卡时间右对齐
- 优化颜色:
  * 迟到改为#ff4949(鲜艳但不刺眼的红色)
  * 正常改为#67c23a(清新的绿色)

4. 内容区域优化:
- 增加内边距
- 将信息分组显示(record-group)
- 组间增加间距
- 备注文字增加行高和左侧缩进
- 使用不同的视觉层次区分信息

5. 信息分组展示:
- 上班信息一组(迟到+地点+备注)
- 下班信息一组(地点+备注)
- 其他备注单独一组

这样改动后的页面更加美观清晰:
1. 信息层次分明
2. 重要信息突出显示
3. 相关信息集中展示
4. 整体视觉效果更现代化
5. 阅读体验更好
```

让我用中文总结一下优化效果:

1. 视觉层次优化:
- 采用卡片式设计
- 日期和打卡时间更醒目
- 信息分组展示更清晰
- 配色更加合理

2. 具体显示效果:
```
[日期]                [上班/下班时间]
------------------------
上班信息:
  迟到: xxx
  上班地点: xxx
  签到备注: xxx
  
下班信息:
  下班地点: xxx
  签退备注: xxx
  
其他备注: xxx
```

3. 交互体验提升:
- 标题栏固定在顶部
- 卡片阴影效果
- 信息间距更合理
- 字体大小层次分明

4. 配色方案:
- 背景采用浅灰色
- 迟到使用柔和的红色
- 正常使用清新的绿色
- 备注使用灰色区分层次

这样的设计既美观又实用,让用户可以更轻松地查看和理解考勤记录。

---

_**User**_

考勤日期后面备注星期 比如 (星期一)

---

_**Assistant**_

```thinking
需要在日期后面添加星期显示。

实现思路:
1. 使用JavaScript的Date对象获取星期几
2. 将星期几转换为中文显示
3. 在日期后面添加括号和星期信息
4. 保持原有样式,只是在日期文本后追加

具体实现:
1. 添加一个获取星期几的函数
2. 在显示日期的地方调用该函数
3. 使用小号字体显示星期,保持整体美

---



---

