<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head"><title>平台类型编辑</title></EasyTag:override>
<EasyTag:override name="content">
    <form id="easyform" data-mars="BusinessPlatformDao.getPlatformType" method="post" autocomplete="off" data-mars-prefix="platformType.">
        <input type="hidden" name="platformType.PLATFORM_TYPE_ID" value="${param.platformTypeId}"/>
        <table class="table table-edit table-vzebra" style="margin-top: 12px;">
            <tbody>
                <c:if test="${param.op=='add'}">
                    <tr>
                        <td class="required">上级类型</td>
                        <td>
                            <input type="hidden" name="platformType.P_PLATFORM_TYPE_ID" value="${param.pPlatformTypeId}"/>
                            <input type="text" value="${param.pPlatformTypeName}" readonly="readonly" data-rules="required" class="form-control input-sm">
                        </td>
                    </tr>
                </c:if>
                <tr>
                    <td class="required">名称</td>
                    <td><input type="text" name="platformType.PLATFORM_TYPE_NAME" data-rules="required" class="form-control input-sm"></td>
                </tr>
                <tr>
                    <td class="required">排序</td>
                    <td><input type="number" name="platformType.IDX_ORDER" data-rules="required|number" value="99" class="form-control input-sm"></td>
                </tr>
            </tbody>
        </table>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button" onclick="PlatformTypeEdit.ajaxSubmitForm()">保存</button>
            <c:if test="${!empty param.platformTypeId}">
                <button class="btn btn-sm btn-info ml-15" type="button" onclick="PlatformTypeEdit.delPlatformType()">删除</button>
            </c:if>
            <button class="btn btn-sm btn-default ml-20" type="button" onclick="layer.closeAll();">关闭</button>
        </div>
    </form>     
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript">
    jQuery.namespace("PlatformTypeEdit");
    
    PlatformTypeEdit.platformTypeId='${param.platformTypeId}';
    PlatformTypeEdit.platformTypeName='${param.platformTypeName}';

    $(function(){
        $("#easyform").render({data:{pk:'${param.platformTypeId}'}});
    });
    
    PlatformTypeEdit.ajaxSubmitForm = function(){
        if(form.validate("easyform")){
            if(PlatformTypeEdit.platformTypeId==''){
                PlatformTypeEdit.insertData(); 
            }else{
                PlatformTypeEdit.updateData(); 
            }
        };
    }
    
    PlatformTypeEdit.delPlatformType = function (){
        var platformTypeName = PlatformTypeEdit.platformTypeName;
        layer.confirm('该类型['+platformTypeName+']下属的平台也将一同被删除，是否确认删除，继续操作?',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
            layer.close(index);
            var data = {platformTypeId:platformTypeId};
            ajax.remoteCall("${ctxPath}/servlet/business-platform?action=deletePlatformType",data,function(result) { 
                if(result.state == 1){
                    layer.msg(result.msg,{icon: 1,time:1200},function(){
                        PlatformType.searchData();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            }); 
        });
    }
    
    PlatformTypeEdit.insertData = function() {
        var data = form.getJSONObject("easyform");
        ajax.remoteCall("${ctxPath}/servlet/business-platform?action=addPlatformType",data,function(result) { 
            if(result.state == 1){
                layer.msg(result.msg,{icon: 1,offset:'100px',time:1600},function(){
                    layer.closeAll();
                    PlatformType.searchData();
                });
            }else{
                layer.alert(result.msg,{icon: 5});
            }
        });
    }
    
    PlatformTypeEdit.updateData = function(){
        var data = form.getJSONObject("easyform");
        ajax.remoteCall("${ctxPath}/servlet/business-platform?action=modPlatformType",data,function(result) { 
            if(result.state == 1){
                layer.msg(result.msg,{icon: 1,offset:'100px',time:1600},function(){
                    layer.closeAll();
                    PlatformType.searchData();
                });
            } else {
                layer.alert(result.msg,{icon: 5});
            }
        });
    }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
