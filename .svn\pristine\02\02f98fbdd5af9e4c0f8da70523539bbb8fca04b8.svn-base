<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>文档库管理</title>
	<style>
		.layui-btn,.editBtn,.mgrEditBtn{display: none;}
		.layui-table-hover .layui-btn{
			display: inline-block;
		}
		.layui-tree-txt{color:#7b6c6c;font-size: 13px;margin-left: -6px;}
		.layui-btn+.layui-btn{margin-left: 2px;}
		#searchForm .btn .caret{color: #fff;}
		/* #searchForm .layui-table-page{display: none;} */
		.layui-tree-icon{height: 16px;width: 16px;line-height: 13px;}
	    .left{
			overflow-y: scroll;width: 280px;float: left;margin: -15px;padding: 15px;background-color: #fff;height: calc(100vh - 15px);
		}
		.right{
			float: left;width:calc(100% - 280px);
			margin-left: 25px;
			height: 100%;
		}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
		@media screen and (max-width:768px){
			.right{width:calc(100% - 0px);margin-left:0px;}
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
			<input type="hidden" name="folderId" id="folderId" value=""/>
			<input type="hidden" name="folderIds" id="folderIds" value=""/>
			<input type="hidden" name="folderCode" id="folderCode" value=""/>
			<input type="hidden"  id="folderName"/>
			<div class="ibox-content layui-hide-xs left">
				<div style="line-height:40px;height: 40px;border-bottom: 1px solid #eee;">
					文件夹列表
					 <EasyTag:res resId="FOLDER_MGR">
						 <a class="btn btn-xs btn-info pull-right mt-10 mgrEditBtn" href="javascript:list.editFolder()">修改</a>
					 </EasyTag:res>
					 <EasyTag:res resId="FOLDER_MGR" hasPermission="false">
						 <a class="btn btn-xs btn-info pull-right mt-10 editBtn" href="javascript:list.editFolder()">修改</a>
					 </EasyTag:res>
					 <a class="btn btn-xs btn-default pull-right mt-10 mr-5" href="javascript:list.addFolder()">新建</a>
				</div>
				<div id="tree"></div>
			</div>
			<div class="right">
				<div class="ibox">
					<div class="ibox-title layui-hide-xs clearfix">
		          		 <div class="form-group">
		          		     <div class="input-group input-group-sm" style="width: 200px">
								 <span class="input-group-addon">文件名称</span>	
								 <input type="text" name="fileName" class="form-control input-sm">
						     </div>
		          		     <div class="input-group input-group-sm" style="width: 140px">
								 <span class="input-group-addon">上传人</span>	
								 <input type="text" name="userName" class="form-control input-sm">
						     </div>
		          		     <div class="input-group input-group-sm" style="width: 140px">
								 <span class="input-group-addon">文件夹</span>	
								 <input type="text" name="folderName" class="form-control input-sm">
						     </div>
							 <div class="input-group input-group-sm">
								 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 </div>
							 <div class="input-group input-group-sm pull-right">
									<button type="button" class="btn btn-sm btn-info" onclick="clickUpload();">+ 上传文件</button>
							 </div>
		          	     </div>
		              </div> 
						<div class="ibox-content" style="min-height: 300px">
						    <table class="layui-hide" id="files"></table>
						</div>
					</div>
			</div>
		</form>
		<script type="text/x-jsrender" id="bar">
			<a class="layui-btn layui-btn-xs" lay-event="list.download"><i class="layui-icon layui-icon-download-circle"></i></a>
  			{{if currentUserId==MANAGER || currentUserId == CREATOR}}
			  <a class="layui-btn layui-btn-xs" lay-event="list.del"><i class="layui-icon layui-icon-delete"></i></a>
			{{/if}}
		</script>
		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" multiple="multiple" id="localfile" onchange="uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		var source='${param.source}'||'rules';
		
		$(function(){
			loadTree();
			list.loadFile();
		});
		
		function clickUpload(){
			var id = $("#folderId").val();
			if(id=='0'||id==''){
				layer.msg('请选择目录');
				return;
			}
			var name = $("#folderName").val();
			layer.msg('上传至：'+name,{time:700,offset:'20px',icon:7},function(){
				$('#localfile').click();
			});
		}
		var uploadFile = function(callback){
			$("#randomId").render({success:function(){
				var docId=$("#randomId").val();
				var folderId = $("#folderId").val();
				easyUploadFile({callback:'callback',fileMaxSize:(1024*100),fkId:docId,source:source,folderId:folderId});
			}});
		}
		
		var callback = function(data,params){
			
			var array = [];
			if(isArray(result)){
				array = result;
			}else{
				array[0] = result;
			}
			for(var index in array){
				var data = array[index];
				var id=data.id;
				var url=data.url;
				var name=data.name;
				var docId=params.fkId;
				
			}
			list.query();
		}
		
		function loadTree(){
			ajax.remoteCall("${ctxPath}/webcall?action=FolderDao.docFolder",{source:source},function(rs) { 
				var result= rs.data;
				var data = dataToTree(result,{idFiled: 'FOLDER_ID', textFiled: 'FOLDER_NAME', parentField: 'P_FOLDER_ID', childField: '',def:{spread:false}, map: {FOLDER_ID: 'id', FOLDER_NAME: 'title' } });
				data = [{title:'文档目录',spread:true,FOLDER_ID:'0',FOLDER_NAME:'',children:data}];
				layui.use('tree', function(){
				    var tree = layui.tree;
				    tree.render({
				        elem: '#tree',
				        data: data,
				        click: function(obj){
				        	var row = obj.data;
				           	var id = row['FOLDER_ID'];
				           	var folderCode = row['FOLDER_CODE'];
				           	var name = row['FOLDER_NAME'];
				           	var creator = row['CREATOR'];
				           	var currentUserId = getCurrentUserId();
				        	$('.mgrEditBtn').show();
				           	if(creator==currentUserId){
					        	$('.editBtn').show();
				           	}else{
					        	$('.editBtn').hide();
				           	}
				           	list.folderData = row;
				           	
				           	$("#folderName").val(name);
				            if(id=='0'){
					            $("#folderId").val('');
				           		list.query();
				           		return;
			           		 }else{
					           	var childrenIds = [];
					           	var findId = function(arr) {
					           	　　arr.forEach(function(item) {
					           	　　　　if (item.id) {
					           	　　　　　　childrenIds.push(item.id);
					           	　　　　}
					           	　　　　if (item.children && item.children.length > 0) {
					           	　　　　　　findId(item.children);
					           	　　　　}
					           	　　});
					           	}

					           	var array = new Array();
					           	array.push(row);
					           	findId(array);
					           	$('#folderIds').val(childrenIds.join());
					           	
					           	$("#folderId").val(id);
					            $("#folderCode").val(folderCode);
					           	list.query();
			           		}
				        }
				   });
				});
				
			});
		}
		var list={
			folderData:{},
			loadFile:function(){
				var height = 'full-120';
				var device = layui.device();
			    if(device.mobile){
			    	height = 'full-50';
			    } 
				$("#searchForm").initTable({
					mars:'FolderDao.myfiles',
					id:'files',
					skin:'line',
					page:true,
					height:height,
					limit:'15',
					limit:20,
					data:{source:source},
					cols: [[
					 {type:'numbers',width:50,align:'center',title:'序号'},
		             {
					    field: 'FILE_ID',
						title: '文件名称',
						align:'left',
						minWidth:200,
						style:'padding-left:0px',
						templet:function(d){
							return  '<div>'+getFileIcon(''+d.FILE_TYPE+'')+'<a href="javascript:;" onclick="list.preview(\''+d.FILE_ID+'\')">'+d.FILE_NAME+'</a></div>'
							
						}
					},{
						field:'FOLDER_NAME',
						title:'文件夹',
						width:100
					},{
					    field: 'CREATE_NAME',
						title: '发布人',
						align:'left',
						width:80
					},{
						field:'FILE_SIZE',
						title:'文件大小',
						width:80,
						align:'center'
					},{
						field:'DOWNLOAD_COUNT',
						title:'下载量',
						event:'list.log',
						align:'center',
						style:'text-decoration:underline;',
						width:60
					},{
						field:'CREATE_TIME',
						align:'center',
						title:'上传时间',
						width:140
					},{
						title: '操作',
						align:'center',
						width:90,
						hide:device.mobile,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]],done:function(res){

					}}
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'files',data:{source:source}});
			},
			preview:function(id){
				window.open('${ctxPath}/fileview/'+id+'?view=online');
			},
			download:function(data){
				var id = data['FILE_ID'];
				window.open('${ctxPath}/fileview/'+id);
			},
			log:function(data){
				downloadLogLayer('',data['FILE_ID'])
			},
			del:function(data){
				layer.confirm('确认删除吗,不可恢复?',function(){
					ajax.remoteCall("${ctxPath}/servlet/doc?action=del",{id:data.FILE_ID},function(result) { 
						if(result.state == 1){
							layer.msg("删除成功！",{icon:1,time:1200});
							list.query();
						}else{
							layer.msg(result.msg,{icon: 7,time:1200});
						}
					});
				});
			},
			addFolder:function(){
				var folderId = $("#folderId").val()||'0';
				popup.layerShow({type:1,maxmin:true,area:['450px','300px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'新增文件夹',data:{folderAuth:'1',id:folderId,folderName:$("#folderName").val()||'根目录',source:source}});
			},
			editFolder:function(){
				var data = list.folderData;
				var folderName = data[''];
				popup.layerShow({type:1,maxmin:true,area:['450px','300px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'编辑文件夹',data:{folderName:folderName,folderAuth:'1',folderId:data.FOLDER_ID}});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>