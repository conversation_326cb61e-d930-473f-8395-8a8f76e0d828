package com.yunqu.work.servlet.common;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.CommentService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.WxMsgService;
@WebServlet("/servlet/comment")
public class CommentServlet extends AppBaseServlet {
	private static final long serialVersionUID = -9064225788599799486L;
	
	public EasyResult actionForAdd(){
		JSONObject jsonObject=getJSONObject();
		String title=jsonObject.getString("title");
		String content=jsonObject.getString("content");
		String fkId=jsonObject.getString("fkId");
		String commentNoticeType=jsonObject.getString("commentNoticeType");
		if(StringUtils.isBlank(content)||StringUtils.isBlank(fkId)){
			return EasyResult.fail("不能为空!");
		}
		EasyResult result=CommentService.getService().addComment(getUserId(),fkId, content, WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"));
		if(result.isOk()){
			if(StringUtils.notBlank(commentNoticeType)){
				MessageModel model=new MessageModel();
				String assignUserId=jsonObject.getString("ASSIGN_USER_ID");
				//任务提醒
				if(StringUtils.notBlank(assignUserId)){
					String creator=jsonObject.getString("CREATOR");
					model.setTitle(title);
					model.setSender(getUserId());
					model.setDesc(content);
					if(getUserId().equals(creator)){
						model.setReceiver(assignUserId);
						this.sendMsg(commentNoticeType,model,true);
					}else if(getUserId().equals(assignUserId)){
						model.setReceiver(creator);
						this.sendMsg(commentNoticeType,model,true);
					}else{
						//其他人评论 非创建人和执行人 都要发
						model.setReceiver(assignUserId);
						this.sendMsg(commentNoticeType,model,true);
						model.setReceiver(creator);
						this.sendMsg(commentNoticeType,model,true);
					}
				}
				//周报提醒
				String receiver=jsonObject.getString("RECEIVER");
				if(StringUtils.notBlank(receiver)){
					if(getUserId().equals(receiver)){//周报审批人
						model.setTitle(jsonObject.getString("TITLE"));
						model.setSender(getUserId());
						model.setReceiver(jsonObject.getString("CREATOR"));
						model.setDesc(content);
						this.sendMsg(commentNoticeType,model);
					}
				}
			}
		}
		return EasyResult.ok(null,"评论成功.");
	}
	private void sendMsg(String commentNoticeType,MessageModel model){
		sendMsg(commentNoticeType,model,false);
	}
	private void sendMsg(String commentNoticeType,MessageModel model,boolean isTask){
		String[] commentNoticeTypes=commentNoticeType.split(",");
		for(int i=0;i<commentNoticeTypes.length;i++){
			String type=commentNoticeTypes[i];
			if(StringUtils.notBlank(type)){
				switch (type) {
				case "wx":
					WxMsgService.getService().sendCommentTaskMsg(model);
					break;
				case "email":
					if(isTask){
						model.setTplName("taskComment.html");
						EmailService.getService().sendTaskCommentEmail(model);
					}
					break;
				default:
					break;
				}
			}
		}
	}
	public EasyResult actionForDel(){
		String commentId=getJsonPara("commentId");
		try {
			this.getQuery().executeUpdate("delete from yq_comments where comment_id = ?", commentId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("删除失败!");
		}
		return EasyResult.ok(null,"删除成功.");
	}
}
