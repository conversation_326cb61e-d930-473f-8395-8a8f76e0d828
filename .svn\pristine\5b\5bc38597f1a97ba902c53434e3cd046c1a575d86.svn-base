package com.yunqu.work.dao.contract;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="ProjectContractDao")
public class ContractDao extends AppDaoContext {
	
	@WebControl(name="getContractNo",type=Types.TEXT)
	public JSONObject getOrderNo(){
		long dateId = Long.valueOf(EasyCalendar.newInstance().getDateInt());
		try {
			String val = this.getQuery().queryForString("select max(CONTRACT_NO) from yq_project_contract where CONTRACT_NO like '"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult(dateId+"001");
			}else {
				return getJsonResult(Long.valueOf(val)+1);
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	@WebControl(name="record",type=Types.TEMPLATE)
	public JSONObject record(){
		String contractId = param.getString("contractId");
		if(StringUtils.notBlank(contractId)){
			return queryForRecord("select * from yq_project_contract where contract_id = ?", contractId);
		}else{
			return getJsonResult(new JSONObject());
		}
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		EasySQL sql = new EasySQL("select CONTRACT_ID,CONTRACT_NAME from YQ_PROJECT_CONTRACT where 1=1");
		sql.append(param.getString("custId"),"and CUST_ID = ?",false);
		return getDictByQuery(sql.getSQL(),sql.getParams());
	}
	
	/**
	 * 全部合同项目
	 * @return
	 */
	@WebControl(name="contractList",type=Types.LIST)
	public JSONObject contractList(){
		EasySQL sql=getEasySQL("select t1.*,t2.ht_auth from YQ_PROJECT_CONTRACT t1 ");
		sql.append("left join yq_crm_cust_team t2 on t1.cust_id = t2.cust_id");
		sql.append("where 1=1");
		sql.appendLike(param.getString("contractName"),"and t1.CONTRACT_NAME like ?");
		sql.appendLike(param.getString("contractNo"),"and t1.CONTRACT_NO like ?");
		if(!isSuperUser()) { 
			sql.append("and (");
			sql.append(getUserId(),"(t1.creator = ?)");
			sql.append("or");
			sql.append(getDeptId(),"(t1.sale_dept_id = ?)");
			sql.append("or");
			sql.append(getUserId(),"(t1.sales_by = ?)");
			sql.append("or");
			sql.append(getUserId(),"(t2.user_id = ?)");
			sql.append(")");
		}
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 全部合同项目
	 * @return
	 */
	@WebControl(name="contractSelect",type=Types.LIST)
	public JSONObject contractSelect(){
		EasySQL sql=getEasySQL("select * from YQ_PROJECT_CONTRACT where 1=1");
		String contractName = param.getString("contractName");
		sql.appendLike(contractName,"and CONTRACT_NAME like ?");
		if(StringUtils.notBlank(contractName)){
			sql.appendLike(contractName,"or CONTRACT_NO like ?");
		}
		sql.append("order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="custContractList",type=Types.LIST)
	public JSONObject custContractList(){
		EasySQL sql=getEasySQL("select * from YQ_PROJECT_CONTRACT where 1=1");
		sql.append(param.getString("custId"),"and CUST_ID = ?");
		sql.append("order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 全部项目
	 * @return
	 */
	@WebControl(name="projectList",type=Types.LIST)
	public JSONObject projectList(){
		EasySQL sql=getEasySQL("select * from YQ_PROJECT where 1=1");
		sql.append("and is_delete = 0");
		sql.append(20,"and project_state < ?");
		sql.appendLike(param.getString("projectName"),"and PROJECT_NAME like ?");
		sql.append("order by UPDATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 全部项目
	 * @return
	 */
	@WebControl(name="projectModuleList",type=Types.LIST)
	public JSONObject projectModuleList(){
		EasySQL sql=getEasySQL("select t1.* from yq_project t1 where 1=1");
		sql.append("and t1.is_delete = 0");
		sql.append(20,"and t1.project_state < ?");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.append("order by t1.UPDATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
