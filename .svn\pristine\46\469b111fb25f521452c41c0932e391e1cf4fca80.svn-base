<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>收入确认报表</title>
    <style type="text/css">
        #dataList tr td ,#totalRow1 tr td {
            white-space: nowrap;
            min-width: 50px;
            max-width: 260px;
            text-overflow: ellipsis;
            overflow: hidden
        }


        #searchForm th {
            white-space: nowrap;
        }

        .fixed-column {
            position: sticky;
            left: 0;
            background-color: #f8f8f8; /* 可选：设置固定列的背景色以区分 */
            width: 400px;
        }

    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form action="" method="post" name="searchForm" class="form-inline" id="searchForm">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5 id="queryTitle">收入确认报表(税后)</h5>
                    <span data-mars="ContractStatisticDao.statisticUpdateTime(yq_contract_income_stat)" data-mars-top="true" id="updateTimeSpan"></span>
                    <i class="layui-icon layui-icon-tips" lay-tips="报表每小时自动更新。可点击按钮手动更新，每次更新间隔时间不得小于10分钟。"></i>
                    <button type="button" class="btn btn-default btn-sm" onclick="updateStat()">点击更新</button>
                    <div class="input-group input-group-sm pull-right">
                        <a class="btn btn-sm btn-success" href="javascript:void(0)" onclick="Income.exportStat()"><i class="glyphicon glyphicon-export"></i> <span>全部导出</span> </a>
                    </div>
                </div>
                <hr style="margin: 5px -15px">
                <div class="form-group">
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">查找时长</span>
                        <select name="period" id="period" class="form-control input-sm" onchange="Income.onChangePeriod(this.value)">
                            <option value="1" selected> 一年</option>
                            <option value="2"> 两年</option>
                            <option value="3"> 三年</option>
                            <option value="5"> 五年</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">开始年份</span>
                        <input type="text" name="startYear" class="form-control input-sm Wdate" onClick="WdatePicker({dateFmt:'yyyy'})" onchange="Income.onClickSearch()" style="width: 100px;">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">合同名称</span>
                        <input type="text" name="contractName" class="form-control input-sm" style="width: 150px;">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">合同号</span>
                        <input type="text" name="contractNo" class="form-control input-sm" style="width: 122px;">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">客户名称</span>
                        <input type="text" name="custName" class="form-control input-sm" style="width: 122px;">
                    </div>
                    <div class="input-group input-group-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="Income.onClickSearch()"><span class="glyphicon glyphicon-search"></span> <span data-i18n="搜索">搜索</span></button>
                    </div>
                </div>
            </div>

            <div class="ibox-content table-responsive">
                <table class="table table-auto table-bordered table-hover table-condensed text-nowrap" data-auto-fill="10" id="tableHead" data-mars="ContractStatisticDao.incomeStatList2">
                    <thead>
                    <tr id="head1">
                        <th colspan="4" class="text-c sticky-row">合同基础信息</th>
                        <th id="IncomeHeadTh" colspan="12" class="text-c sticky-row">收入确认阶段</th>
                        <th id="IncomeAHeadTh" colspan="12" class="text-c sticky-row">收入确认A</th>
                        <th id="IncomeBHeadTh" colspan="12" class="text-c sticky-row">收入确认B</th>
                    </tr>
                    <tr id="head2" class="sticky-row">
                    </tr>
                    </thead>
                    <tbody id="totalRow1">

                    </tbody>
                    <tbody id="dataList">

                    </tbody>

                </table>
                <script id="head-template" type="text/x-jsrender">
					<th class="fixed-column">合同名</th>
						{{for list}}
							<th>{{:name}}</th>
						{{/for}}


                </script>
                <script id="list-template" type="text/x-jsrender">
					{{call: fn='clearTotalRow'}}
					{{for list}}
						<tr>
							<td class="fixed-column">{{:CONTRACT_SIMPILE_NAME}}</td>
							<td>{{:CONTRACT_NO}}</td>
							<td>{{:CUSTOMER_NAME}}</td>
							<td>{{:AMOUNT}}</td>
							{{call: fn='initTd'}}
						</tr>
					{{/for}}

                </script>
                <script id="totalRowTemplat" type="text/x-jsrender">
					<tr class="sticky-row">
						<td class="fixed-column">本页合计</td>
						<td></td>
						<td></td>
						<td></td>
						{{call: fn='getTotalRow'}}
					</tr>

                </script>
                <div class="row paginate">
                    <jsp:include page="/pages/common/pagination.jsp"/>
                </div>
            </div>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("Income");

        $(function () {
            Income.rendData();
            var currentYear = new Date().getFullYear();
            $("input[name='startYear']").val(currentYear);
        });

        Income.rendData = function () {
            $("#searchForm").render({
                success: function () {

                    var totalTemp = $.templates("#totalRowTemplat");
                    var totalHtml = totalTemp.render();
                    $("#totalRow1").html(totalHtml);
                    var period = $("#period").val();
                    listTdArray = [];
                    Income.getHeadArray(period);
                    Income.initData();

                }
            });
        }

        Income.searchData = function () {
            $("#searchForm").searchData({
                success: function () {
                    var totalTemp = $.templates("#totalRowTemplat");
                    var totalHtml = totalTemp.render();
                    $("#totalRow1").html(totalHtml);
                }
            });
        }

        Income.onClickSearch = function () {
            var period = $("#period").val();
            Income.onChangePeriod(period);
        }

        var listObj = {};		//模板对象
        var listObjArray = [];	//查询字段
        var listHeadArray = [];//表头

        var listTdArray = [];	//显示模板字段

        var totalRow = [];

        Income.onChangePeriod = function (period) {
            listTdArray = [];									//初始化显示模板字段
            //此处还应该有一个年份
            Income.getHeadArray(period);					//将年份转化为表头
            Income.initData();								//模板渲染
            Income.searchData();								//查询数据
        }

        Income.getHeadArray = function (period) {
            listHeadArray = [{name: '合同号'}, {name: '客户名'}, {name: '合同金额'}];	//表头初始化
            period = Number(period);
            var startYear = Number($("input[name='startYear']").val());
            $('#IncomeHeadTh').attr('colspan', period * 12);
            $('#IncomeAHeadTh').attr('colspan', period * 12);
            $('#IncomeBHeadTh').attr('colspan', period * 12);
            for (var head = 1; head <= 3; head++) {
                for (var i = 0; i < period; i++) {
                    var curYear = Number(startYear + i);
                    for (var j = 1; j <= 12; j++) {
                        var monthId = "";
                        if (j < 10) monthId = curYear + '0' + j;
                        else monthId = curYear + '' + j;
                        listHeadArray.push({name: monthId});
                        if (head == 1) {
                            listTdArray.push(monthId);
                        }
                    }
                }
            }
            return;
        }

        Income.initData = function () {
            var headTmp = $.templates("#head-template");
            var headHtml = headTmp.render({list: listHeadArray});
            $("#head2").html(headHtml);

        }

        Income.exportStat = function () {

            var period = $('#period option:selected').text();
            var startYear = $("input[name='startYear']").val();

            layer.confirm('导出【' + startYear + '年】起【' + period + '】的全部收入确认统计报表。<br>导出时间较长，请勿关闭页面。', {icon: 3, title: '导出提示', offset: '20px'}, function (index) {
                window.open('${ctxPath}/servlet/contractStat?action=ExportIncomeStat&data=' + encodeURI(JSON.stringify(form.getJSONObject('#searchForm'))));
                layer.close(index);
            });
        }

        function initTd(row) {
            var html = "";
            var length = listTdArray.length;
            for (var i = 0; i < listTdArray.length; i++) {
                html += "<td style='width: calc(100% + 10px); white-space: nowrap;'>" + row["STAGE_" + listTdArray[i]] + "</td>";
                totalRow[i] = totalRow[i] + Number(row["STAGE_" + listTdArray[i]]);
            }
            for (var i = 0; i < listTdArray.length; i++) {
                html += "<td style='width: calc(100% + 10px);white-space: nowrap;'>" + row["TOTAL_A_" + listTdArray[i]] + "</td>";
                totalRow[i + length] = totalRow[i + length] + Number(row["TOTAL_A_" + listTdArray[i]]);
            }
            for (var i = 0; i < listTdArray.length; i++) {
                html += "<td style='width: calc(100% + 10px); white-space: nowrap;'>" + row["TOTAL_B_" + listTdArray[i]] + "</td>";
                totalRow[i + length * 2] = totalRow[i + length * 2] + Number(row["TOTAL_B_" + listTdArray[i]]);
            }
            return html;
        }

        function clearTotalRow() {
            totalRow = [];//total清空
            for (var i = 0; i < (listTdArray.length * 3); i++) {
                totalRow.push(0);
            }
        }

        function getTotalRow() {
            var html = "";
            for (var i = 0; i < totalRow.length; i++) {
                html += "<td>" + totalRow[i].toFixed(2) + "</td>";
            }
            return html;
        }


        function updateStat() {
            var updateTimeText = $('#updateTimeSpan').text().trim();
            var updateTime = new Date(updateTimeText);
            var currentTime = new Date();

            var timeDiff = (currentTime - updateTime) / (1000 * 60);
            if (timeDiff < 10) {
                layer.msg("收入确认报表十分钟内只能更新一次！请稍后再试。", {icon: 1, time: 1200});
            } else {
                var data = {"nothing": "nothing"};
                ajax.remoteCall("${ctxPath}/servlet/contractStat?action=UpdateIncomeStat", data, function (result) {
                    if (result.state == 1) {
                        layer.msg(result.msg, {icon: 1, time: 1200});
                        Income.onClickSearch();
                        $('#updateTimeSpan').render();
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                });
            }

        }

        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
