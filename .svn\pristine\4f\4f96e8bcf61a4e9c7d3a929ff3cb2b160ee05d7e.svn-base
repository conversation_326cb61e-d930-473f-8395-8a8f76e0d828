<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<table id="goodsTable"></table>
		<script  type="text/html" id="toolbar">
			<button class="btn btn-info btn-sm" onclick="addGoods()" type="button">新增</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="refreshDataList" type="button">刷新</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="copyGoods" type="button">复制</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="updateGoods" type="button">修改</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="uploadPz" type="button">上传凭证</button>
		</script>
	<script type="text/javascript">
		$(function(){
			$("#OrderDetailForm").initTable({
				mars:'OrderDao.goodsList',
				id:'goodsTable',
				page:false,
				toolbar:'#toolbar',
				cellMinWidth:100,
				totalRow:true,
				rowDoubleEvent:'goodsDetail',
				cols: [[
	             {
            	 	type: 'checkbox',
					align:'left'
				 },{
				    field: 'GOODS_NO',
					title: '物料编号',
					align:'left'
				},{
				    field: 'NAME',
					title: '物料描述',
					align:'left',
					minWidth:120,
					totalRowText:'汇总'
				},{
				    field: 'FILE_COUNT',
					title: '上传凭证',
					align:'center',
					width:80,
					templet:function(row){
						 var fileCount = row['FILE_COUNT'];
						 if(fileCount==0){
							 return '<font color=red>请上传</font>';
						 }else{
							 return fileCount;
						 }
					}
				},{
				    field: 'PRICE',
					title: '含税单价',
					totalRow:true,
					align:'center'
				},{
					 field:'NUMBER',
					 totalRow:true,
					 width:70,
					 title:'采购数量'
				 },{
				    field: 'TOTAL_PRICE',
					title: '含税总价',
					totalRow:true,
					align:'center'
				},{
				    field: 'TAX_RATE',
					title: '税率',
					width:70,
					align:'center',
					templet:'<div>{{d.TAX_RATE}}%</div>'
				},{
					field:'REMARK',
					title:'备注',
					edit:'text'
				},{
				    field: 'CREATE_USER_NAME',
					title: '创建人',
					align:'center'
				},{
				    field: 'UPDATE_TIME',
					title: '更新时间',
					align:'center'
				}
			]],done:function(row){
				$("#goodsCount").text(row['total']);
		  	}
		});
		});
		
		function refreshDataList(data){
			var sum = data.length;
	 		if(sum > 0){
				ajax.remoteCall("${ctxPath}/servlet/goods?action=reloadStorageNum",{goodsId:data[0]['GOODS_ID']},function(result) { 
					$("#OrderDetailForm").queryData({id:'goodsTable',page:false});
				});
	 		}else{
				$("#OrderDetailForm").queryData({id:'goodsTable',page:false});
	 		}
		}
		
		function reloadDataList(){
			$("#OrderDetailForm").queryData({id:'goodsTable',page:false});
		}
		
		function editGoods(id,isCopy){
			var supplierId = $("[name='apply.SUPPLIER_ID']").val();
			var title = isCopy=='1'?'复制数据':'修改数据';
			popup.layerShow({id:'goods',scrollbar:false,title:title,area:['600px','460px'],shadeClose:false,data:{supplierId:supplierId,goodsId:id,isCopy:isCopy},url:'${ctxPath}/pages/erp/goods/goods-edit.jsp'});
		}
		
		function addGoods(){
			var supplierId = $("[name='apply.SUPPLIER_ID']").val();
			popup.layerShow({id:'goods',scrollbar:false,title:'新增',area:['600px','460px'],shadeClose:false,data:{supplierId:supplierId,orderId:'${param.orderId}'},url:'${ctxPath}/pages/erp/goods/goods-edit.jsp'});
		}
		
		function updateGoods(data){
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择物料。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		editGoods(data[0]['GOODS_ID'],'0');
		}
		
		function goodsDetail(data){
	 		editGoods(data['GOODS_ID'],'0');
		}
		
		function copyGoods(data){
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择物料。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		editGoods(data[0]['GOODS_ID'],'1');
		}
		
		function uploadPz(data){
			var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择物料。',{icon : 7, time : 1000});
	 			return;
	 		}
			$("#goodsId").val(data[0]['GOODS_ID']);
			$('#orderDetailLocalfile').click();
		}
		
		
		
</script>
