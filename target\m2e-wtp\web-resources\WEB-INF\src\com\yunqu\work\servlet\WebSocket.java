package com.yunqu.work.servlet;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;

import com.yunqu.work.utils.WebSocketUtils;

@ServerEndpoint(value="/websocket/{userId}/{userAcct}")
public class WebSocket{
	
	private static Logger logger =  LogEngine.getLogger("websocket");
 
    /**
     * 连接建立成功调用的方法
     *
     * @param session 可选的参数。session为与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    @OnOpen
    public void onOpen(@PathParam("userId") String userId ,@PathParam("userAcct") String userAcct,Session session){
        WebSocketUtils.add(userId,userAcct, session);
    }
 
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(@PathParam("userId") String userId,@PathParam("userAcct") String userAcct,Session closeSession) {
        WebSocketUtils.remove(userId,userAcct);
    }
 
    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     * @param session 可选的参数
     * @throws Exception 
     */
    @OnMessage
    public void onMessage(@PathParam("userId") String userId,@PathParam("userAcct") String userAcct,Session session,String message) throws Exception {
    	WebSocketUtils.add(userId,userAcct, session);
    	WebSocketUtils.receive(userId,userAcct, message);
    }
   
 
    /**
     * 发生错误时调用
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        logger.info("发生错误",error);
    }

}
