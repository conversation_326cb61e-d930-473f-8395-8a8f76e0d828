<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>采购付款申请</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">采购订单号</span>
						 	<input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">付款单号</span>
						 	<input class="form-control input-sm" name="paymentNo">
						 </div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">付款状态</span>	
							<select name="payState" id="payStateId" onchange="queryData();" class="form-control input-sm">
							      <option value="">请选择 </option>
							      <option value="0" data-class="label label-warning">未付款</option>
                    			  <option value="10" data-class="label label-success">已付款</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="paymentTable"></table>
					</div>
				</div>
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="paymentDetail" href="javascript:;">详情</a>
 				<a class="layui-btn layui-btn-xs" href="javascript:;" lay-event="doPay">付款</a>
		  </script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'OrderFlowDao.paymentList',
					id:'paymentTable',
					page:true,
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 type:'radio'
					 },{
						 field:'APPLY_NO',
						 title:'申请单号',
						 width:100
					 },{
						 field:'ORDER_NOS',
						 title:'采购订单号',
						 width:100
					 },{
						 field:'APPLY_TITLE',
						 title:'标题',
						 minWidth:160
					 },{
						 field:'SUPPLIER_NAME',
						 title:'供应商',
						 minWidth:130
					 },{
						 field:'AMOUNT',
						 title:'付款金额',
						 width:80
					 },{
						 field:'PAY_STATE',
						 title:'当前状态',
						 width:80,
						 templet:function(row){
							 return getText(row['PAY_STATE'],'payState');
						 }
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center'
					},{
					    field: 'APPLY_NAME',
						title: '申请人',
						width:70,
						align:'center'
					},{
						field:'',
						title:'操作',
						width:120,
						fixed:'right',
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function paymentDetail(data){
			var supplierId = data['SUPPLIER_ID'];
			var json = {supplierId:supplierId,paymentId:data['PAYMENT_ID'],resultId:data['RESULT_ID']};
			popup.openTab({id:'paymentDetail',title:'采购付款申请',url:'${ctxPath}/pages/erp/order/payment/apply-detail.jsp',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'paymentTable'});
		}
		
		function doPay(list){
			if(list.length==0){
				layer.msg('请选择');
				return;
			}
			layer.confirm('是否确认操作?',{offset:'20px',icon:3},function(){
				var data = list[0];
				ajax.remoteCall("${ctxPath}/servlet/order?action=doPay",{paymentId:data['PAYMENT_ID'],orderIds:data['ORDER_ID']},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							queryData();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		function applyPay(){
			layer.msg('请去采购订单查看详情发起付款申请');
		}
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>