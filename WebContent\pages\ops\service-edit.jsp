<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>服务</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="ServiceDao.record" autocomplete="off" data-mars-prefix="service.">
     		   <input type="hidden" value="${param.serviceId}" name="service.SERVICE_ID"/>
						<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td style="width: 80px" class="required">服务名称</td>
				                    <td><input data-rules="required"  type="text" name="service.SERVICE_NAME" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td class="required">服务版本</td>
				                    <td><input data-rules="required"  type="text" name="service.SERVICE_VERSION" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td>开发负责人</td>
				                    <td>
				                    	<select data-mars="CommonDao.userDict" name="service.DEVEL" class="form-control input-sm">
				                    		<option value="">请选择</option>
				                    	</select>
				                    </td>
					            </tr>
					            <tr>
				                    <td>运维负责人</td>
				                    <td>
				                    	<select data-mars="CommonDao.userDict" name="service.OPS" class="form-control input-sm">
				                    		<option value="">请选择</option>
				                    	</select>
				                    </td>
					            </tr>
					            <tr>
				                    <td>描述</td>
				                    <td>
				                    	<textarea style="height: 120px" name="service.SERVICE_DESC" class="form-control input-sm"></textarea>
				                    </td>
					            </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c">
						    	  <button type="button" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("Edit");
		Edit.serviceId='${param.serviceId}';
		
		$(function(){
			$("#editForm").render({success:function(){
				requreLib.setplugs('select2',function(){
				   $("#editForm select").select2({theme: "bootstrap",placeholder:'请选择'});
				});
			}});  
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.serviceId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/service?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/service?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>