<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>菜单</title>
	<style>
		.h .layui-btn{display: none;}
		.h .layui-table-hover .layui-btn{
			display: inline-block;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="dcEditForm"  autocomplete="off">
     		<div id="msg"></div>
			<div class="layui-tab layui-tab-brief" lay-filter="dc">
			  <ul class="layui-tab-title">
			    <li  lay-id="type-2">午餐</li>
			    <li  lay-id="type-3">加班餐</li>
			  </ul>
			  <div class="layui-tab-content">
			    <div class="layui-tab-item layui-show">
			   		<table id="t2" class="layui-hide"></table>
			   		<div style="line-height: 26px;padding: 10px;">
			   			<p>1、请在规定时间内点餐</p>
			   			<p>1、请适量打菜,否则排队后面同事吃的是剩饭剩菜</p>
						<p>2、适量自选 <b>1主菜</b> 、2配菜、1青菜及1例汤</p>
						<p>3、同上 </p>
			   		</div>
			    </div>
			    <div class="layui-tab-item h">
			 		<table id="t3" class="layui-hide"></table>
			    </div>
			  </div>
			</div> 
  		</form>
  		<script type="text/html" id="dc_bar">
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="doDc">点餐</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		var load2=false;
		var load3=false;
		$(function(){
			layui.use('element', function(){
				var $ = layui.jquery;
				var element = layui.element;
				element.on('tab(dc)', function(){
				    var id=this.getAttribute('lay-id');
				    if(id=='type-3'){
				    	loadDinnerType3();
				    }
				    if(id=='type-2'){
				    	loadDinnerType2();
				    }
			    });
				var myDate = new Date();
				if((myDate.getHours()+1)>=13){
					$("[lay-id='type-3']").click();
					$("[lay-id='type-2']").hide();
					$(".layui-tab-item").first().hide();
				}else{
					$("[lay-id='type-2']").click();
					$("[lay-id='type-3']").hide();
					$(".layui-tab-item").last().hide();
				}
			});
		});
		function loadDinnerType3(){
			if(load3)return;
			$("#dcEditForm").initTable({
				mars:'FoodDao.todayMeals',
				page:false,
				skin:'line',
				id:'t3',
				data:{dinnerType:3},
				cols: [[
				  {title:'序号',type:'numbers',width:50},
				  {
				    field: 'FOOD_NAME',
					title: '菜单名',
					align:'center'
				  },{
					title: '操作',
					align:'center',
					width:120,
					cellMinWidth:100,
					toolbar: '#dc_bar'
				}
			 ]],success:function(){
				load3=true;
			 }}
			);
		}
		function loadDinnerType2(){
			if(load2)return;
			$("#dcEditForm").initTable({
				mars:'FoodDao.todayMeals',
				page:false,
				skin:'line',
				id:'t2',
				data:{dinnerType:2},
				cols: [[
				  {
				    field: 'FOOD_NAME',
					title: '菜单名',
					align:'center'
				  },{
					title: '操作',
					align:'center',
					width:120,
					cellMinWidth:100,
					toolbar: '#dc_bar'
				}
			 ]],success:function(){
				load2=true;
			 }}
			);
		}
		var doDc=function(data) {
			data['bookType']=1;
			ajax.remoteCall("${ctxPath}/servlet/food?action=todayBookFood",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						initBookFoodData();
						layer.closeAll();
						 var msg=result.msg;
						 if(msg.indexOf(".")>-1){
							var userId=getCurrentUserId();
							layer.open({type:1,title:msg,area:['300px','390px'],shadeClose:false,closeBtn:0,btn:['我已绑定','稍后再试'],content:'<div style=""><img style="width:250px;hight:250px;margin-left:25px;" src="http://qr.liantu.com/api.php?text=http://work.yunqu-info.cn/yq-work/api/getAuthorize?userId='+userId+'"/></div><br><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;微信扫码关注,再次扫码绑定后关闭弹出窗</p>'});
						 }
					});
				}else{
					layer.msg(result.msg,{icon: 7});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>