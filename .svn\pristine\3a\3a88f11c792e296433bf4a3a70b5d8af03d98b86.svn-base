package com.yunqu.work.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.HostModel;

@WebObject(name="HostDao")
public class HostDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		HostModel model=new HostModel();
		model.setColumns(getParam("host"));
		return queryForRecord(model);
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		EasySQL sql=getEasySQL("select HOST_ID,HOST_NAME from YQ_OPS_HOST where 1=1");
		sql.append(param.getString("serverRoomId"),"and SERVER_ROOM_ID = ?");
		return getDictByQuery(sql.getSQL(),sql.getParams());
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL easySQL=getEasySQL("select t1.*,t2.SERVICE_ROOM_NAME from YQ_OPS_HOST t1 inner join yq_ops_serverroom t2 on t2.server_room_id=t1.SERVER_ROOM_ID where 1=1");
		easySQL.appendLike(param.getString("hostName"),"and t1.HOST_NAME like ?");
		easySQL.append(param.getString("serverRoomId"),"and t1.SERVER_ROOM_ID = ?");
		easySQL.append(param.getString("projectId"),"and t1.project_id = ?");
		if(!isSuperUser()){
			easySQL.append(getUserPrincipal().getUserId(),"and t1.CREATOR = ?");
		}
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}

}
