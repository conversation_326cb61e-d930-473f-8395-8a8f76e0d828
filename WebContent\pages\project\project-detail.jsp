<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>项目详情</title>
	<style>
		.layui-tab-title li{padding: 0px 10px;}
		.text-r{font-size: 13px!important;}
		.layui-table-cell{padding: 0 6px;font-size: 13px;}
		.layui-table td, .layui-table th{padding: 9px 8px;}
		.n1,.n2,.n3,.n4,.n5,.n6,.n7{left: -2px!important;}
		.layui-col-md8 .layui-card-header{border-bottom: 1px solid #eee;}
		#projectUpdateDesc:focus{
			box-shadow:none;
			-webkit-box-shadow:none;
		}
		#weekly .layui-col-md4{
			margin-bottom: 10px;
		}
		#weekly .layui-col-md4 div{
			min-height:110px;
			line-height:24px;
			background-color: #f8f8f8;
			padding: 10px;
		}
		.task-tab li{font-size: 13px;}
		.task-tab li span{left: -2px!important;top: 0px!important;}
		.task-tab li .layui-badge{height: 16px;line-height: 16px;padding: 0px 5px;}
		.layui-table[lay-size="sm"] td, .layui-table[lay-size="sm"] th{padding: 8px 10px;}
		.publishProjectStateBody .layui-table-cell {
			height: auto!important;
		}
		.kanban .layui-card{
		    border: 1px solid #ddd;
		}
		.kanban .layui-card .layui-card-body{font-size:13px;line-height:20px;}
		
		.icon {width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:2px;}
		
		.weekly-item .layui-table-main .layui-table-cell {
	        line-height: 20px !important;
	        vertical-align: middle;
	        height: auto;
	        padding: 6px 6px;
	        overflow: visible;
	        text-overflow: inherit;
	        white-space: normal;
	    }
    
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form class="form-inline ibox-index" style="padding: 0px;width: 100%;margin: 0 auto;" id="projectDetail"  data-mars="ProjectDao.record"  data-mars-prefix="project.">
			   <input type="hidden" id="projectId" value="${param.projectId}"  name="project.PROJECT_ID"/>
			   <input type="hidden" name="project.CONTRACT_ID"/>
 		   	   <input type="hidden" value="${param.projectId}" name="projectId"/>
 		   	   <input type="hidden" value="project" name="source"/>
 		   	   <input type="hidden" value="0" id="folderId" name="folderId"/>
 		   	   <input type="hidden" value="根目录"  id="folderName"/>
 		   	   <input type="hidden" value="${param.projectId}" name="fkId"/>
 		   	   <input name="taskState" id="taskState" type="hidden"/>
				<div class="layui-row layui-col-space10">
				  <div class="layui-col-md9">
				  	<div class="layui-card">
				  	<div class="layui-card-body" style="min-height: calc(100vh - 50px)">
						<div class="layui-tab layui-tab-brief" lay-filter="tabFilter">
						  <ul class="layui-tab-title">
						    <li class="layui-this">任务 (<span class="n2">0</span>)</li>
						    <li>规划  (<span class="n7">0</span>)</li>
						    <li>进度  ( <span class="n6">0</span>)</li>
						    <li lay-id="weekly">个人周报 (<span class="n1">0</span>)</li>
						    <li lay-id="projectWeekly">项目周报 (<span class="n9">0</span>)</li>
						    <li>人员 ( <span class="n3">0</span>)</li>
						    <li>模块 (<span class="n4">0</span>)</li>
						    <li>版本 (<span class="n8">0</span>)</li>
						    <li>文档 (<span class="n5">0</span>)</li>
						    <li>描述</li>
						  </ul>
						  <div class="layui-tab-content" style="min-height: 300px;padding: 10px 2px;">
						  
						  	<!-- 任务列表 -->
						     <div class="layui-tab-item layui-show" style="padding: 0px;">
						     	<jsp:include page="include/task.jsp"></jsp:include>
						    </div>
						    <div class="layui-tab-item kanban">
						    	<jsp:include page="include/plan.jsp"></jsp:include>
						    </div> 
						    <!-- 项目动态 -->
						    <div class="layui-tab-item" style="padding: 0px;">
						    	<jsp:include page="include/action.jsp"></jsp:include>
						    </div>
						     <div class="layui-tab-item weekly-item" style="margin: -10px -15px;">
							    <jsp:include page="include/weekly.jsp"></jsp:include>
						     </div>
						     <div class="layui-tab-item project-weekly-item" style="margin: -10px -15px;">
							    <jsp:include page="include/project-weekly.jsp"></jsp:include>
						     </div>
						    <div class="layui-tab-item" style="margin: -10px -15px;">
						    	<table class="layui-hide" id="teams"></table>
						    	<div class="layui-row"><a onclick="synProjectTeam()" href="javascript:;" class="pull-right btn btn-xs btn-link mb-20">从关联任务同步参与人</a></div>
						    </div>
						    <div class="layui-tab-item" style="margin: -10px -15px;">
						    	<table class="layui-hide" id="modules"></table>
						    	<button class="btn btn-info btn-sm" type="button" onclick="publishVersion('1');">发布版本</button>
						    </div>
						    <div class="layui-tab-item" style="margin: -10px -15px;">
						    	<jsp:include page="include/version.jsp"></jsp:include>
						    	<button class="btn btn-info btn-sm" type="button" onclick="publishVersion('2');">发布版本</button>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/files.jsp"></jsp:include>
						    </div>
						    <div class="layui-tab-item">
						    	<div name="project.PROJECT_DESC" data-fn="contentFn" style="padding: 10px;border: 1px solid #eee;"></div>
						    </div>
						  </div>
						</div>
					 </div>
				  </div>
				  </div>
				  <div class="layui-col-md3">
				   <div class="layui-card">
			  			<div class="layui-card-header">常用操作</div>
					  	<div class="layui-card-body">
					  	    <div class="layui-row layui-col-space15">
							    <div class="layui-col-md4">
							  		 <button type="button" onclick="addWeekly()" class="btn btn-default  btn-link">+项目周报</button>
							    </div>
							    <EasyTag:hasRole roleId="PROJECT_MANAGER">
								    <div class="layui-col-md4">
								  		 <button type="button" onclick="editContract()" class="btn btn-warning btn-link">项目合同</button>
								    </div>
							    </EasyTag:hasRole>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="addTask()" class="btn btn-default btn-link">发起任务</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="addBatchTask()" class="btn btn-default btn-link">+批量任务</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="projectKanban()" class="btn btn-default btn-link">项目看板</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="prjectWorkhour()" class="btn btn-default btn-link">项目工时</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="versionMgr()" class="btn btn-default btn-link">版本管理</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="hostMgr()" class="btn btn-default btn-link">主机管理</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="platformMgr()" class="btn btn-default btn-link">平台管理</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="systemInspection()" class="btn btn-default btn-link">系统巡检</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="mindoc()" class="btn btn-default btn-link">mindoc</button>
							    </div>
						     </div>
					  	</div>
					</div>
			  		<div class="layui-card">
			  			<div class="layui-card-header">项目信息  <button type="button" onclick="favoriteProject()" class="btn btn-xs btn-info btn-outline pull-right mt-10 mr-15">关注项目</button></div>
					  	<div class="layui-card-body">
					  		<table class="layui-table projectInfo" lay-skin="nob">
						        <tbody>
						           <tr>
					                    <td colspan="2">
					                        <span style="color: #5cb85c;" name="project.PROJECT_NAME"></span>
					                    </td>
						           </tr>
						           <tr>
					                    <td class="text-r" width="95px">项目编号：</td>
					                    <td><span name="project.PROJECT_NO"></span></td>
						           </tr>
						           <tr>
					                   <td class="text-r">项目负责人：</td>
					                   <td name="project.PROJECT_PO_NAME"></td>
						           </tr>
						           <tr>
					                   <td class="text-r">开发负责人：</td>
					                   <td name="project.PO_NAME"></td>
						           </tr>
						            <tr>
					                    <td class="text-r">项目级别：</td>
					                    <td data-fn="projectLevel" name="project.PROJECT_LEVEL"></td>
					                </tr>
						            <tr>
					                    <td class="text-r">项目状态：</td>
					                    <td><label class="label label-info" data-fn="projectState" name="project.PROJECT_STATE"></label></td>
					                </tr>
						            <tr>
					                    <td class="text-r">项目周期：</td>
					                    <td>
						                    <span name="project.BEGIN_DATE"></span> - 
						                    <span name="project.END_DATE"></span>
					                    </td>
					                </tr>
						            <tr>
					                    <td class="text-r">更新时间：</td>
					                    <td name="project.UPDATE_TIME"></td>
					                </tr>
						            <tr>
						            	<td colspan="2">
						            		<EasyTag:hasRole roleId="DEV_MGR,PROJECT_MANAGER">
							            		<button class="btn btn-info btn-sm" onclick="editProject()" type="button">修改</button>
						            		</EasyTag:hasRole>
						            		<EasyTag:hasRole roleId="PROJECT_MANAGER">
							            		<button class="btn btn-default btn-link" onclick="delProject()" type="button">删除</button>
						            		</EasyTag:hasRole>
						            		<EasyTag:hasRole roleId="SYS_SUPER_USER">
							            		<button class="btn btn-default btn-link" onclick="transferProject()" type="button">转移</button>
						            		</EasyTag:hasRole>
						            	</td>
						            </tr>
						        </tbody>
			 				</table>
					  	</div>
				  	</div>
				  </div>
				</div>
				
			</form>
			<div style="display: none;" id="transferProject">
				<div style="padding: 15px;">
					<input id="transferProjectId" type="hidden"/>
					<input id="transferProjectName" placeholder="请选择新的项目"  onclick="singleProject(this);" class="form-control input-sm"  type="text"/>
				</div>
			</div>
			<form  id="projectDetailFileForm" enctype="multipart/form-data"  method="post">
  				<input style="display: none;" name="file" type="file" id="projectDetailLocalfile" onchange="projectUploadFile()"/>
  			</form>
			
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		layui.use('element',function(){
			var element=layui.element;
		});
		
		var record={};
	
		var projectId='${param.projectId}';
		
		 function isSelfShow(id){
			   var currId=getCurrentUserId();
			   if(id==currId){
				   return 'opacity:1;';
			   }else{
				   return 'opacity:0;';
			   }
		}
		 
	 	$(function(){
			
			layui.use('element',function(){
				var element=layui.element;
				element.on('tab(task)', function(elem){
					var  state=$(this).data('state');
					$("#taskState").val(state);
					reloadTaskList();
				});
				
				var tabId = '${param.tabId}';
				if(tabId){
					element.tabChange('tabFilter', tabId);
				}
				
			  });
			
	 		$("#projectDetail").render({success:function(result){
 				record=result['ProjectDao.record'].data;
 				if(record['PROJECT_ID']){
 					loadTaskTable();
 					loadPersonWeekly();
 					loadProjectWeekly();
			 		projectTeams();
			 		projectModule();
			 		projectStateLog();
			 		loadVersion();
			 		
			 		$(".n5").text($('.folderTable tr').length-1);
			 		
			 		var taskCountStat = result['TaskDao.taskCountStat'];
			 		fillRecord(taskCountStat['data']);
			 		
			 		layui.use('element',function(){
						var element=layui.element;
						element.render();
					});
			 		
			 		var currentUserId = getCurrentUserId();
			 		$('[data-user-id]').each(function(){
			 			var t = $(this);
			 			var userId = t.data('userId');
			 			if(currentUserId!=userId){
			 				t.remove();
			 			}
			 		});
			 		
 				}else{
 					popup.closeTab();
 				}
	 		},then:function(){
	 			$(window).resize();
	 		}});
	 	});
	 	
	 	function editContract(){
	 		var contractId = $("[name='project.CONTRACT_ID']").val();
	 		if(contractId){
		 		popup.openTab({id:'contractDetail',title:'合同详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:['75%','100%'],url:'${ctxPath}/project/contract',title:'合同详情 ',data:{contractId:contractId,isDiv:0}});
	 		}else{
				layer.msg('合同不存在.');	 			
	 		}
	 	}
	 	
	 	function kanbanDetail(kanbanId){
	    	   popup.openTab({id:'kanbanDetail',url:'${ctxPath}/pages/kanban/kanban-detail.jsp',title:'看板',data:{kanbanId:kanbanId}});
	    }
	 	 
		var projectDetailCallback = function(data){
			$("#fileList").render({data:{fkId:projectId,folderId:'0',source:'project'}});
		}
		
	 	function reloadTaskList(){
	 		$("#projectDetail").queryData({id:'task'});
	 	}
	 	
	 	function favoriteProject(){
	 		ajax.remoteCall("${ctxPath}/servlet/project?action=addFavorite",{fkId:'${param.projectId}'},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg);
				}else{
					layer.msg(result.msg,{icon: 7,time:1200});
				}
			},{loading:false});
	 	}
	 	
	 	function prjectWorkhour(){
		    popup.openTab({id:'prjectWorkhour',type:2,anim:0,scrollbar:false,offset:'r',area:['768px','100%'],url:'${ctxPath}/pages/project/workhour/project-workhour.jsp',title:'项目工时',data:{projectId:projectId}});
	 	}
	 	function versionMgr(){
	 		var projectName=$("[name='project.PROJECT_NAME']").text();
	 		popup.openTab({id:'versionMgr',url:'${ctxPath}/pages/ops/version-list.jsp',title:'版本管理',data:{projectId:projectId,appName:projectName,projectName:projectName}});
	 	}
	 	function hostMgr(){
	 		popup.openTab({id:'hostMgr',url:'${ctxPath}/pages/ops/host-list.jsp',title:'主机管理',data:{projectId:projectId}});
	 	}
	 	function platformMgr(){
	 		popup.openTab({id:'platformMgr',url:'${ctxPath}/pages/ops/platform-list.jsp',title:'平台管理',data:{projectId:projectId}});
	 	}
	 	
	 	function systemInspection(){
	 		layer.msg('输入巡检人（多人），巡检日期(每月多个日期)，巡检内容（先读取模板，然后填写任务文本），支持立即生成巡检报告',{icon:7,offset:'20px',time:1200});
	 	}
	 	
	 	
	 	function mindoc(){
	 		ajax.remoteCall("${ctxPath}/servlet/project?action=synToMindoc",{projectId:projectId},function(result) { 
				if(result.state == 0){
					window.open('${ctxPath}/servlet/project?action=goMindoc&projectId='+projectId,'mindoc');
				}else{
					if(result.msg=='已创建'){
						window.open('${ctxPath}/servlet/project?action=goMindoc&projectId='+projectId,'mindoc');
					}else{
						layer.alert(result.msg,{icon: 7});
					}
				}
			});
	 	}
	 	
		
		function projectModule(){
			$("#projectDetail").initTable({
				mars:'ProjectDao.projectModule',
				height:400,
				id:"modules",
				page:false,
				data:{},
				edit:'editModule',
				cols: [[
				 {type:'numbers'},
				 {type:'checkbox'},
	             {
            	 	field: 'MODULE_NAME',
					title: '模块名称',
					edit:'text',
					align:'left'
				 },
				 {
            	 	field: 'LAST_VERSION',
					title: '最新版本',
					align:'left'
				},
				 {
            	 	field: 'REMARK',
					title: '备注',
					edit:'text',
					align:'center'
				},
				{
				    field: 'UPDATE_BY',
					title: '更新人',
					align:'left',
					width:90,
					templet:function(row){
						return getUserName(row.UPDATE_BY);
					}
				},{
				    field: 'UPDATE_TIME',
					title: '更新时间',
					align:'left'
				}
				]],done:function(result){
					$(".n4").text(result.total);
				}});
		}
		function projectTeams(){
			$("#projectDetail").initTable({
				mars:'ProjectDao.projectTeams',
				id:"teams",
				data:{},
				edit:'editTeam',
				limit:20,
				rowEvent:'teamUserInfo',
				cols: [[
				 {type:'numbers'},
	             {
            	 	field: 'USERNAME',
					title: '姓名',
					align:'left',
					templet:'<div>{{d.DEPTS}}.{{d.USERNAME}}</div>'
				 },{
				    field: 'MOBILE',
					title: '手机号码',
					align:'left'
				},{
				    field: 'EMAIL',
					title: '邮箱',
					align:'left'
				},{
				    field: 'LAST_LOGIN_TIME',
					title: '最近登录时间',
					align:'left'
				},{
				    field: 'ROLE_NAME',
					title: '角色',
					edit:'text',
					align:'left'
				},{
				    field: 'REMARK',
					title: '备注',
					edit:'text',
					hide:true,
					align:'left'
				},{
				    field: 'JOIN_TIME',
					title: '加入时间',
					align:'left',
					hide:true
				}
				]],done:function(result){
					$(".n3").text(result.totalRow);
					table.resize("teams");
				}});
		}
		function editTeam(obj){
			var data=obj.data;
		   // if(data.USER_ID==getCurrentUserId()||record.PO==getCurrentUserId() || isSuperUser){
				var params={PROJECT_ID:data.PROJECT_ID,USER_ID:data.USER_ID,ROLE_NAME:data.ROLE_NAME,REMARK:data.REMARK};
				ajax.remoteCall("${ctxPath}/servlet/project?action=updateTeam",params,function(result) { 
					if(result.state != 1){
						layer.alert(result.msg,{icon: 7});
					}
				},{loading:false});
		   // }else{
		   // 	layer.msg("修改无效,您无权修改!");
		   // }
		}
		function contentFn(val){
			if(val){
				return val;
			}else{
				return '暂无';
			}
		}
		function editModule(obj){
			var data=obj.data;
		    if(record.PO==getCurrentUserId() || isSuperUser){
				var params={PROJECT_ID:'${param.projectId}',MODULE_ID:data.MODULE_ID,MODULE_NAME:data.MODULE_NAME,REMARK:data.REMARK};
				ajax.remoteCall("${ctxPath}/servlet/project?action=updateModule",params,function(result) { 
					$("#projectDetail").queryData({id:'modules'});
					if(result.state != 1){
						layer.alert(result.msg,{icon: 7});
					}
				},{loading:false});
		    }else{
		    	layer.msg("修改无效,您无权修改!");
		    }
		}
		function delProject(){
				layer.confirm("确定删除吗?",{offset:'30px',icon:7},function(){
					ajax.remoteCall("${ctxPath}/servlet/project?action=delProject",{projectId:projectId},function(result) { 
						popup.closeTab();
						if(result.state != 1){
							layer.alert(result.msg,{icon: 7});
						}else{
							layer.msg(result.msg);
						}
					});
				});
		}
		function synProjectTeam(){
			ajax.remoteCall("${ctxPath}/servlet/project?action=updateProjectTeam",{projectId:projectId},function(result) { 
				if(result.state != 1){
					layer.alert(result.msg,{icon: 7});
				}else{
					$("#projectDetail").queryData({id:'teams',jumpOne:true});
					layer.msg(result.msg);
				}
			});
		}
		function reloadProjectList(){
			$("#projectDetail").render();
		}
		function editProject(data){
			popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'编辑项目',data:{projectId:projectId}});
		}
		function projectKanban(){
			popup.openTab({id:'projectKanban',url:'${ctxPath}/pages/kanban/kanban-index.jsp',title:'项目看板',data:{projectId:'${param.projectId}'}});
		}
		function addBatchTask(){
			var projectName=$("[name='project.PROJECT_NAME']").text();
			popup.openTab({type:1,id:'addBatchTask',full:true,maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-batch-add.jsp',title:'安排任务',closeBtn:0,data:{projectId:'${param.projectId}',projectName:projectName}});
		}
		
		var addTask=function(groupId){
			var projectName = $("[name='project.PROJECT_NAME']").text();
			groupId = groupId ||'';
			popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务',data:{projectId:projectId,projectName:projectName,groupId:groupId}});
		}
		
		var addGroupTask=function(groupId,groupName){
			var projectName = $("[name='project.PROJECT_NAME']").text();
			popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务>'+groupName,data:{projectId:projectId,projectName:projectName,groupId:groupId,groupName:groupName}});
		}
		
		function taskMgr(){
			popup.openTab({url:'${ctxPath}/pages/task/task-query.jsp',id:'taskMgr',title:'项目任务管理',data:{projectId:projectId}});
		}
		function teamUserInfo(data){
			userInfoLayer(data['USER_ID']);
		}
		
		function transferProject(){
			popup.layerShow({title:'转移到其他项目',content:$('#transferProject'),area:['300px','200px'],type:1,btn:['提交','取消'],yes:function(index){
				var transferProjectId = $("#transferProjectId").val();
				if(transferProjectId==''){
					layer.msg('请选择转移到哪个项目.');
					return;
				}
				layer.close(index);
				layer.confirm("确定转移吗",{icon:7,offset:'20px'},function(i){
					layer.close(i);
					ajax.remoteCall("${ctxPath}/servlet/project?action=transferProject",{newProjectId:transferProjectId,projectId:projectId},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,function(){
								location.reload();
							});
						}else{
							layer.alert(result.msg,{icon: 7});
						}
					});
				});
			}});
			
		}
		
		function cloudDoc(){
			var width = window.screen.availWidth-100;
			var height = window.screen.availHeight-100;
			window.open('http://*************/s/GzHaSKc3JHDNKFp','云文档','width='+width+',height='+height+',top=10,left=50,status=no,fullscreen=yes');
		}
		
		function exportTask(){
			layer.msg('正在导出',{time:500});
			location.href = '${ctxPath}/servlet/task?action=exportTask&data='+encodeURI(JSON.stringify(form.getJSONObject('#projectDetail')));
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>