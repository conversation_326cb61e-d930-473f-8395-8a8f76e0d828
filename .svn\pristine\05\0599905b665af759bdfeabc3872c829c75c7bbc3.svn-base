<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<link href="${ctxPath}/static/css/sidebar.css" rel="stylesheet">
	<link href="/yq-work/static/css/driver.min.css" rel="stylesheet">
	<title>我的申请</title>
	<style type="text/css">
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		  <div class="page-box">
	  		 <div class="left-sidebar" style="display: none;">
                <div class="left-sidebar-item" data-mars="FlowDao.myFlowApplyTree" data-template="template-list"></div>
       		 </div>
        	<div onclick="leftShow(this)" class="leftShow" style="position: absolute;display: inline-flex;align-items: center;height: 50px;background-color: #00abfc;top:40%;cursor: pointer;color: #fff;"><i class="fa fa-chevron-right" aria-hidden="true"></i></div>
  			<div class="right-content" style="width: 100%;margin-left: 0px;">
  			  <div class="ibox" >
				<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="flowMgrForm">
					<input type="hidden" name="flowCode" value="${param.flowCode}"/>
				   <div class="ibox-title clearfix">
          		 		<div class="pull-left layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: 0px;margin-bottom: -8px;">
          		 	 		<ul class="layui-tab-title"><li data-val="" class="layui-this">全部</li><li data-val="10,20,21">进行中</li><li data-val="30">已结束</li></ul>
          		 	 	</div>
						<div class="input-group input-group-sm pull-right layui-hide-xs">
							 <button type="button" id="conditionFiter" class="btn btn-sm btn-default"><span class="glyphicon glyphicon-filter"></span> 高级查询</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="loadGuide();"><span class="fa fa-hand-o-right"></span> 引导 </button>
						</div>
	                </div> 
					<div class="ibox-content">
						 <table id="_flowTable" style="margin-top: 3px;"></table>
					</div>
				  </form>
				</div>
				<div class="flowQuery"></div>
  		</div>
 	 </div>
 	 
 	 <script id="queryCondition" type="text/x-jsrender">
		<form id="condtionForm">
			<table class="table table-edit table-vzebra">
			 <tr>
				<td>流程标题</td>
				<td colspan="3">
					<input class="form-control input-sm" name="applyTitle">
				</td>
			</tr>
			 <tr>
				<td>申请编号</td>
				<td>
					<input class="form-control input-sm" name="applyNo">
				</td>
				<td>显示数据</td>
				<td>
					<select class="form-control input-sm" name="approveAll">
						<option value="0">当前节点</option>
						<option value="1">全部节点</option>
					</select>
				</td>
			</tr>
			 <tr>
				<td>待办人</td>
				<td>
					<input class="form-control input-sm" name="checkName">
				</td>
				<td>流程状态</td>
				<td>
					<select name="applyState" onchange="flowList.queryData();" class="form-control input-sm">
					  <option value="">请选择 </option>
					  <option data-class="label label-default" value="0">草稿</option>
					  <option data-class="label label-warning" value="1">作废</option>
					  <option data-class="label label-warning" value="5">已挂起</option>
					  <option data-class="label label-warning" value="10">待审批</option>
					  <option data-class="label label-info" value="20">审批中</option>
					  <option data-class="label label-danger" value="21">审批退回</option>
					  <option data-class="label label-success" value="30">已完成</option>
				   </select>	
				</td>
			</tr>
			 <tr>
				 <td style="width:80px!important;">发起日期</td>	
				 <td style="width: 35%;">
				   <input type="text" name="applyBeginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 108px;display: inline-block">
				   <input type="text" name="applyEndDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 108px;display: inline-block">
				 </td>
				 <td>紧急程度</td>
				<td>
					<select class="form-control input-sm" name="applyLevel">
						<option value="正常">正常</option>
						<option value="重要">重要</option>
						<option value="紧急">紧急</option>
					</select>
				</td>
			 </tr>
			{{for list}}
			 {{if #index%2==0}} <tr>{{/if}}
				<td>{{:title}}</td>
				<td>
					{{if query=='daterange'}}
					  <input class="form-control input-sm" style="width: 108px;display: inline-block" data-laydate="{type:'date'}" name="condition.{{:field}}_{{:query}}_begin">
					  <input class="form-control input-sm" style="width: 108px;display: inline-block" data-laydate="{type:'date'}" name="condition.{{:field}}_{{:query}}_end">
					{{else query=='range'}}
					  <input class="form-control input-sm" style="width: 108px;display: inline-block" name="condition.{{:field}}_{{:query}}_begin">
					  <input class="form-control input-sm" style="width: 108px;display: inline-block" name="condition.{{:field}}_{{:query}}_end">
					{{else}}
					  <input class="form-control input-sm" {{if query=='date'}}data-laydate="{type:'date'}"{{/if}} name="condition.{{:field}}_{{:query}}">
					{{/if}}
				</td>
			 {{if #index%2!=0}}</tr>{{/if}}
			{{/for}}
			<tr>
				<td  colspan="4">
					<button type="button" class="btn btn-sm btn-default" onclick="flowList.conditionReset()"><span class="fa fa-eraser"></span> 清空</button>
					<button type="button" id="searchBtn" class="btn btn-sm btn-info ml-10" onclick="flowList.queryData()"><span class="glyphicon glyphicon-search"></span> 查询</button>
				</td>
			</tr>
		</table>
	  </form>
    </script>
	 <script id="template-list" type="text/x-jsrender">
			{{for flowCategory}}
 				<div class="sidebar-item">
					<a href="javascript:void(0);" class="sidebar-nav-sub-title active"><i class="sidebar-icon glyphicon glyphicon-menu-right"></i>{{:P_FLOW_NAME}}</a>
				    <ul class="sidebar-nav-sub">
						{{for #parent.parent.data.data}}
							{{if #parent.parent.data.P_FLOW_CODE==P_FLOW_CODE}}
						  		<li>
								  <a href="javascript:void(0)" data-url="{{:QUERY_URL}}" data-code="{{:FLOW_CODE}}">{{:FLOW_NAME}}({{:COUNT}})</a>
						  	  </li>
							{{/if}}
  						 {{/for}}
				    </ul>
				</div>
			{{/for}}
			{{if flowCategory.length==0}}
				<p class="text-c mt-30">无数据</p>
			{{/if}}
 		</script>
		   <script type="text/x-jsrender" id="bar">
				{{if APPLY_STATE=='21'}}
 					<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="flowList.flowEdit" href="javascript:;">重新提交</a>
				{{else}}
 					 <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="flowList.flowDetail" href="javascript:;">详情</a>
 				{{/if}}
		  </script>
		 <script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default layui-hide-xs" type="button" lay-event="flowList.refreshData"><i class="fa fa-refresh" aria-hidden="true"></i> 刷新</button>
				<div class="select-btn" style="display:none;">
			 	<button class="btn btn-sm btn-info edit-btn ml-10" type="button" lay-event="flowList.flowEdit"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> 修改</button>
			 	<button class="btn btn-sm btn-danger layui-hide-xs flow-cannel ml-10" type="button" lay-event="flowList.cannelApply"><i class="fa fa-recycle" aria-hidden="true"></i> 作废</button>
			 	<button class="btn btn-sm btn-warning handup-btn layui-hide-xs ml-10" type="button" lay-event="flowList.hangupApply"><i class="fa fa-warning" aria-hidden="true"></i> 挂起</button>
			 	<button class="btn btn-sm btn-success layui-hide-xs active-btn ml-10" type="button" lay-event="flowList.activeApply"><i class="fa fa-heart-o" aria-hidden="true"></i> 激活</button>
			 	<button class="btn btn-sm btn-danger layui-hide-xs del-btn ml-10" type="button" lay-event="flowList.delApply"><i class="fa fa-trash-o" aria-hidden="true"></i> 删除</button>
 				<EasyTag:res resId="copyFlow">
			 		<button class="btn btn-sm btn-default ml-10 hidden" type="button" lay-event="copyData"><i class="fa fa-clipboard" aria-hidden="true"></i> 复制</button>
 				</EasyTag:res>
			 	<button class="btn btn-sm btn-default layui-hide-xs flow-progress ml-10" type="button" lay-event="flowList.approveList"><i class="fa fa-tasks" aria-hidden="true"></i> 过程</button>
			 	<button class="btn btn-sm btn-default urge-btn ml-10" type="button" lay-event="flowList.urge"><i class="fa fa-bullhorn" aria-hidden="true"></i> 催办</button>
			 	<button class="btn btn-sm btn-default layui-hide-xs copy-url ml-10" type="button" lay-event="flowList.copyUrl"><i class="fa fa-external-link" aria-hidden="true"></i> 复制链接</button>
 				</div>
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript" src="/yq-work/static/js/driver.min.js"></script>
	<script type="text/javascript" src="/yq-work/static/js/flow-list.js?v=20220507"></script>
	
	<script type="text/javascript">
		
			var flowList = {};
			
			var condtionData = {};
	
			$(function(){
				$(".page-box").render({success:function(){
					flowList.flowInitList();
					flowList.renderTree();
					loadFlowNum();
				}});
			});
			
			function loadGuide(){
				  setTimeout(function(){
					var driver = new Driver({opacity:0.15,nextBtnText:'下一个',prevBtnText:'上一个',doneBtnText:'确定',closeBtnText:'关闭'});
					driver.defineSteps([
					 {
					  element: '.layui-table-tool',
					  popover: {
					    title: '功能操作区域',
					    position: 'bottom'
					  }
					 },
					 {
					  element: '.layui-tab-title',
					  popover: {
					    title: '流程状态区域',
					    position: 'bottom'
					  }
					},{
					  element: '.left-sidebar',
					  popover: {
					    title: '点击按流程分类查询',
					    position: 'right'
					  }
				     },{
					  element: '#conditionFiter',
					  popover: {
					    title: '所有查询条件在这里',
					    description:'支持模糊查询',
					    position: 'left'
					  }
				    }
				   ]);
				   driver.start(); 
				},200);
			}
			
			function intiCondition(data){
			   var conditionList = FlowMgr.getCondition(data);
			   var queryCondition = $.templates("#queryCondition");
			   var ins = layui.dropdown.render({
				    elem: '#conditionFiter'
				    ,content:queryCondition.render({list:conditionList})
				    ,style: 'width: 700px; min-height: 250px; padding: 15px 15px;border-radius: 6px;box-shadow: 2px 2px 30px rgb(0 0 0 / 12%);'
				    ,ready: function(elemPanel, elem){
				    	fillRecord(condtionData,'',',','#condtionForm');
				    	renderDate('#condtionForm');
				    }
			  });
			  $('#conditionFiter').data('layui_dropdown_index', ins.config.id); 
			 
			  layui.element.on('tab(stateFilter)', function(){
				  flowList.queryData({applyState:this.getAttribute('data-val')});
			  });
			}
			 
			flowList.renderTree = function(){
				$('body').on('click','.sidebar-nav-sub-title',function(e){
	    			$(this).toggleClass('active')
	    		})
				$(".left-sidebar-item [data-code]").click(function(){
	    			var t=$(this);
	    			var flowCode = t.data("code");
	    			var queryUrl = t.data("url");
	    			if(queryUrl){
	    				$.get(queryUrl,{flowCode:flowCode,isDiv:'1'},function(html){
		  					$(".ibox").hide();
		  					$(".flowQuery").html(html);
		  					$(".flowQuery .container-fluid").removeClass('container-fluid');
	    				});
	    				return;
	    			}
	    			if(flowCode){
	    				$(".flowQuery").empty();
	    				$(".ibox").show();
	    				$("[name='flowCode']").val(flowCode);
		    			$(".left-sidebar-item a").removeClass("activeItem");
		    			t.addClass("activeItem");
		    			flowList.flowInitList();
	    			}
	    		});
				var device = layui.device();
			    if(!device.mobile){
					$('.leftShow').click();
			    }
			}
			flowList.flowInitList = function(){
				 var param = {
					mars:'FlowDao.myFlowApply',
					id:'_flowTable',
					page:true,
					limit:50,
					title:'我的流程申请',
					toolbar:'#btnBar',
					rowDoubleEvent:'flowList.flowDetail',
					height:'full-95',
					rowEvent:'rowEvent',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'radio',
						title: '序号',
						align:'left'
					 },{
						field:'',
						title:'操作',
						align:'center',
						width:70,
						templet:function(row){
							return renderTpl('bar',row);
						 }
					},{
						field:'APPLY_STATE',
						title:'流程状态',
						width:80,
						templet:function(row){
							var val = row['APPLY_STATE'];
							return flowApplyState(val);
						}
					},{
						field:'CHECK_RESULT',
						title:'审批状态',
						width:70,
						templet:function(row){
							var checkResult = row['CHECK_RESULT'];
							var json = {'0':'待审批','1':'通过','2':'拒绝'};
							return json[checkResult]||'--';
						}
					},{
						 field:'APPLY_NO',
						 title:'申请编号',
						 width:230,
						 templet:function(row){
							 return row['APPLY_NO']+"_"+row['FLOW_NAME'];
						 }
					 },{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:200
					 },{
						 field:'APPLY_LEVEL',
						 title:'紧急程度',
						align:'center',
						 width:70,
						 templet:function(row){
							return row['APPLY_LEVEL'];
						}
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:120,
						 templet:'<div>{{d.DEPT_NAME}}.{{d.APPLY_NAME}}</div>'
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					},{
						field:'NODE_NAME',
						title:'待办节点',
						width:80
					},{
						field:'CHECK_NAME',
						title:'待审批人',
						width:80,
						templet:function(row){
							return row['CHECK_NAME'];
						}
					}
				]],done:function(){
					$('.select-btn').hide();
					$("tbody [data-field='APPLY_LEVEL'][data-content='紧急']").css('background-color','#f6f1c0');
					table.on('radio(_flowTable)',function(obj){
						$('.select-btn').css('display','inline-block');
						var data = obj.data;
						var applyState = data['APPLY_STATE'];
						var flowCode = data['FLOW_CODE'];
						if(applyState=='10'||applyState=='20'||applyState=='21'){
							$('.urge-btn,.handup-btn,.flow-cannel').show();
						}else{
							$('.urge-btn,.handup-btn,.flow-cannel').hide();
						}
						if(applyState=='5'){
							$('.active-btn').show();
						}else{
							$('.active-btn').hide();
						}
						if(applyState=='0'){
							$('.flow-progress,.copy-url').hide();
						}else{
							$('.flow-progress,.copy-url').show();
						}
						if(applyState=='0'||applyState=='21'){
							$('.del-btn,.edit-btn').show();
						}else{
							$('.del-btn,.edit-btn').hide();
						}
						
						if(applyState=='10'&&flowCode.indexOf('finance')==-1){
							$('.edit-btn').show();
						}
					});
					$('tbody tr').first().click();
			  	}
			};
		
			FlowMgr.joinCols(param,5,function(data){
				$("#flowMgrForm").initTable(param);
				intiCondition(data);
			}); 
		 }
		
		 flowList.flowDetail = function(data){
			var flowCode = data['FLOW_CODE'];
			var applyState = data['APPLY_STATE'];
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE'],resultId:data['RESULT_ID']});
			if(applyState=='0'){
				json['handle'] = 'edit';				
			}
			var url = '${ctxPath}/web/flow';
			if(self==top){
				location.href = url+"?"+jQuery.param(json);				
			}else{
				popup.openTab({id:'flowDetail',title:'流程详情',url:url,data:json});
			}
		}
		
		flowList.flowEdit = function(dataList){
			var data = dataList;
			if(isArray(dataList)){
			    if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    data = dataList[0];
			}
		    
		    var applyState = data['APPLY_STATE'];
			if(applyState=='0'||applyState=='10'||applyState=='20'||applyState=='21'){
				var flowCode = data['FLOW_CODE'];
				var json  = $.extend({handle:'edit',mode:'edit'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE']});
				var url = '${ctxPath}/web/flow';
				if(self==top){
					location.href = url+"?"+jQuery.param(json);				
				}else{
					popup.openTab({id:'flowDetail',title:'流程详情',url:url,data:json});
				}
			}else{
				layer.msg('草稿或没审批过的申请才能修改',{icon:2,offset:'20px',time:1000});
			}
		}
		
		flowList.queryData = function(params){
			if (params === undefined) params = {};
			var data = form.getJSONObject("#condtionForm");
			condtionData = data;
			var d = $.extend({},data,params);
			$("#flowMgrForm").queryData({id:'_flowTable',data:d});
		}
		
		flowList.conditionReset = function(){
			$("#condtionForm")[0].reset();
			flowList.queryData();
		}
			
		flowList.copyData = function(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
			var data = dataList[0];
			var flowCode = data['FLOW_CODE'];
			if(flowCode!='finance_bx'){
				layer.msg('该流程暂不支持复制功能');
				return;
			}
			var json  = $.extend({copyData:'1'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
	   }
	   
		flowList.copyUrl = function(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
			var data = dataList[0];
			var flowCode = data['FLOW_CODE'];
			var id = data['FLOW_CODE'];
			var url  = "http://"+location.host+'${ctxPath}/web/flow/'+data['APPLY_ID']+"#"+data['APPLY_TITLE'];
			
			var textarea = document.createElement('textarea');
		    document.body.appendChild(textarea);
		    textarea.style.position = 'fixed';
		    textarea.style.clip = 'rect(0 0 0 0)';
		    textarea.style.top = '10px';
		    textarea.value = url;
		    textarea.select();
		    document.execCommand('copy', true);
		    document.body.removeChild(textarea);
		    
		    top.layer.alert(url,{icon:1,offset:'80px',title:'复制成功'},function(index){
				top.layer.close(index);
			});
	   }
	   
		flowList.urge = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    var applyState = data['APPLY_STATE'];
		    var checkResult = data['CHECK_RESULT'];
			if((applyState=='10'||applyState=='20')&&checkResult=='0'){
			    var title = data['APPLY_TITLE'];
			    var businessId = data['APPLY_ID'];
			    var resultId = data['RESULT_ID'];
			    var staffInfo = getStaffInfo();
			    var content = '流程：'+title+'，已达你处，请尽快办理，谢谢！--'+staffInfo.userName;
			    layer.prompt({formType: 2,value: content,offset:'20px',title: '请输入催办信息',area: ['400px', '150px']}, function(msgContent, index, elem){
		    	 	layer.close(index);
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addUrge",{resultId:resultId,businessId:businessId,msgContent:msgContent},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
		    	});
			}else{
				layer.msg('只有审批中待办的流程才能催办。');
				return;
			}
		    
	   }
	   
	   flowList.approveList = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    var businessId = data['APPLY_ID'];
		   popup.layerShow({url:'/yq-work/pages/flow/comm/approve-progress.jsp',full:fullShow(),area:['90%','80%'],scrollbar:false,offset:'20px',data:{businessId:businessId},id:'approveList',title:'流程记录'});
	   }
	   
	   flowList.refreshData = function(){
		   location.reload();
	   }
	   
	   flowList.delApply = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
			var applyState = data['APPLY_STATE'];
			if(applyState!='0'){
				layer.msg('只有草稿状态才可以删除');
				return;
			}
			layer.confirm('确认删除吗,不可恢复',{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=delApply",{businessId:data['APPLY_ID'],flowCode:data['FLOW_CODE']},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:800},function(){
							flowList.queryData();
						})
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
	   }
	   
	   flowList.cannelApply = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
			var applyState = data['APPLY_STATE'];
			var flowCode = data['FLOW_CODE'];
			if((flowCode.indexOf('finance')>-1&&applyState==21)||(flowCode.indexOf('finance')==-1&&applyState>=10&&applyState<30)){
				layer.confirm('确认作废吗,不可恢复',{icon:3,offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=cannelApply",{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:applyState},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800},function(){
								flowList.queryData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			}else{
				layer.msg('该流程状态不支持作废');
				return;
			}
	   }
	   flowList.hangupApply = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
			var applyState = data['APPLY_STATE'];
			if(applyState==10||applyState==20){
				layer.confirm('确认操作吗',{icon:3,offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=handupApply",{businessId:data['APPLY_ID']},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800},function(){
								flowList.queryData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			}else{
				layer.msg('审批中流程状态才可以挂起.');
				return;
			}
	   }
	   
	   flowList.activeApply = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
			var applyState = data['APPLY_STATE'];
			if(applyState==5){
				layer.confirm('确认操作吗',{icon:3,offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=activeApply",{businessId:data['APPLY_ID']},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800},function(){
								flowList.queryData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			}else{
				layer.msg('挂起中流程状态才可以激活.');
				return;
			}
	   }
	   
	  
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>