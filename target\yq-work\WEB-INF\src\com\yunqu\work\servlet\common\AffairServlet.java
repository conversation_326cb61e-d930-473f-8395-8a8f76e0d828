package com.yunqu.work.servlet.common;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.AffairModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.AffairService;
import com.yunqu.work.service.CommentService;
import com.yunqu.work.service.TaskNoticeService;
import com.yunqu.work.service.WxMsgService;

@WebServlet("/servlet/affair/*")
public class AffairServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		AffairModel model=getModel(AffairModel.class, "affair");
		model.addCreateTime();
		model.setCreator(getUserId());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		model.set("CREATE_NAME", getUserName());
		try {
			int sendType = model.getIntValue("SEND_TYPE");
			this.getQuery().save(model);
			AffairService.getService().updateAffairObj(model,true);
			this.getQuery().executeUpdate("update yq_affair t1 set t1.receiver_count =  (select count(1) from yq_affair_obj t2 where t2.affair_id = t1.affair_id) where t1.affair_id= ?", model.getAffairId());
			int affairState = model.getIntValue("AFFAIR_STATE");
			if(sendType==0&&affairState==10) {
				TaskNoticeService.getService().affairNotice(model.getAffairId());
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"创建成功.");
	}
	
	
	public EasyResult actionForUpdate(){
		AffairModel model=getModel(AffairModel.class, "affair");
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		try {
			AffairService.getService().updateAffairObj(model,false);
			this.getQuery().update(model);
			this.getQuery().executeUpdate("update yq_affair t1 set t1.receiver_count =  (select count(1) from yq_affair_obj t2 where t2.affair_id = t1.affair_id) where t1.affair_id= ?", model.getAffairId());
			int sendType = model.getIntValue("SEND_TYPE");
			int affairState = model.getIntValue("AFFAIR_STATE");
			if(sendType==0&&affairState==10) {
				TaskNoticeService.getService().affairNotice(model.getAffairId());
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"修改成功.");
	}
	
	
	
	public EasyResult actionForDel(){
		String id = getJsonPara("id");
		try {
			int count = this.getQuery().executeUpdate("delete from yq_affair where affair_id = ? and affair_state = 0", id);
			if(count==0) {
				this.getQuery().executeUpdate("update yq_affair set affair_state = 9 where affair_id = ?", id);
			}else {
				this.getQuery().executeUpdate("delete from yq_affair_obj where affair_id = ?", id);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForPause(){
		String id = getJsonPara("id");
		try {
			this.getQuery().executeUpdate("update yq_affair set affair_state = 21 where affair_id = ?", id);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateLookRecord(){
		String id = getJsonPara("id");
		try {
			this.getQuery().executeUpdate("update yq_affair_obj set look_time = ?,state = 2 where affair_id = ? and user_id = ? and state = 1",EasyDate.getCurrentDateString(),id,getUserId());
			
			this.getQuery().executeUpdate("update yq_affair t1 set t1.last_visit_time = ?,t1.look_count = (select count(1) from yq_affair_obj t2 where t2.affair_id = t1.affair_id and t2.state = 2) where t1.affair_id = ?",EasyDate.getCurrentDateString(),id);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddComment(){
		JSONObject params = getJSONObject();
		int noticeFlag = params.getIntValue("noticeFlag");
		String content = params.getString("content");
		String fkId = params.getString("fkId");
		if(StringUtils.isBlank(content)||StringUtils.isBlank(fkId)){
			return EasyResult.fail("不能为空!");
		}
		EasyResult result=CommentService.getService().addComment(getUserId(),getUserName(),fkId, content, WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"),"affair");
		if(result.isOk()){
			try {
				this.getQuery().executeUpdate("update yq_affair set last_reply_time = ? where affair_id = ?",EasyDate.getCurrentDateString(),fkId);
				JSONObject row = this.getQuery().queryForRow("select * from yq_affair where affair_id = ?", new Object[] {fkId},new JSONMapperImpl());
				MessageModel model = new MessageModel();
				model.setSender(getUserId());
				model.setFkId(fkId);
				model.setTitle(row.getString("AFFAIR_NAME"));
				model.setReceiver(row.getString("CREATOR"));
				model.setDesc(content);
				model.setData3(getUserName());
				model.setData4(content);
				model.setUrl("/yq-work/affair/"+fkId);
				if(noticeFlag==0) {
					WxMsgService.getService().sendCommentTaskMsg(model);
				}else {
					WxMsgService.getService().sendCommentTaskMsg(model);
					List<JSONObject> list = this.getQuery().queryForList("select user_id from yq_affair_obj where affair_id = ?", new Object[] {fkId},new JSONMapperImpl());
					for(JSONObject obj:list) {
						model.setReceiver(obj.getString("USER_ID"));
						WxMsgService.getService().sendCommentTaskMsg(model);
					}
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
		return EasyResult.ok(null,"评论成功.");
	}
	
}
