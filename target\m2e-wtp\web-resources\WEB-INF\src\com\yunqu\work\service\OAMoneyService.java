package com.yunqu.work.service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.easitline.common.core.Globals;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;

public class OAMoneyService extends AppBaseService{
	
	private Logger logger = LogEngine.getLogger(Constants.APP_NAME);
	@SuppressWarnings("unused")
	private HttpServletRequest request;
	
	private static class Holder{
		private static OAMoneyService service=new OAMoneyService();
	}
	public static OAMoneyService getService(){
		return Holder.service;
	}
	public static OAMoneyService getService(HttpServletRequest request){
		OAMoneyService service=Holder.service;
		service.request=request;
		return service;
	}
	
	private static Map<String,Map<String,String>> cookies=new HashMap<String,Map<String,String>>();
	
	public Map<String,String> getUserCookie(String userName){
		return cookies.get(userName);
	}
	
	public  boolean login(String userName,String pwd){
		try {
			Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/names.nsf?Login");
			connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
			connection.data("Username",userName);
			connection.data("Password",pwd);
			Response resp=connection.method(Method.POST).ignoreContentType(true).timeout(2000).execute();
			Map<String, String> loginCookies = resp.cookies();
			if(loginCookies==null||loginCookies.size()==0){
				cookies.remove(userName);
				return false;
			}else{
				cookies.put(userName,loginCookies);
				return true;
			}
		} catch (IOException e) {
			logger.error(null,e);
			return false;
		}
				
	}
	
	public static void main(String[] args) throws IOException {
		OAMoneyService.getService().login("100078","********");
		OAMoneyService.getService().loadMoneyDetailXml("100078");
//		OAMoneyService.getService().loadTodoMoneyDetailXml("100078");
//		OAMoneyService.getService().loadTitle("100078","","");
	}
	
	
	public  void loadMoneyDetailXml(String account){
		try {
			for(int i=1;i<=10;i++) {
				long begin = System.currentTimeMillis();
				getLogger().info(">>>>>>>>>>>>>"+i);
				getLogger().info(">>>>>>>>>>>>>"+(600*(i-1)));
				Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/app.nsf/LinkeyGetXmlData?openagent&Num=G_App099_Restore001&rdc=0.****************");
				connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
				connection.cookies(cookies.get(account));
				connection.data("start",600*(i-1)+"");
				connection.data("limit","600");
				connection.data("Num","G_App099_Restore001");
				connection.data("QViewName","");
				connection.data("UserName","陈琛100078");
				
				Response  fileResp=connection.timeout(1000*60*10).ignoreContentType(true).execute();
				
				
				File file=new File(Globals.TMP_DIR+ File.separator+"xml"+File.separator+i+".xml");
				if(!file.exists()) {
					file.createNewFile();
				}else {
					file.delete();
					file.createNewFile();
				}
				FileOutputStream out = (new FileOutputStream(file));
				out.write(fileResp.bodyAsBytes());           
				out.close();
				
				long end = System.currentTimeMillis();
				
				getLogger().info(i+">>>>>"+(end-begin)/1000);
				
			}
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
		}
	}
	public  void loadTodoMoneyDetailXml(String account){
		try {
			for(int i=1;i<=1;i++) {
				long begin = System.currentTimeMillis();
				getLogger().info(">>>>>>>>>>>>>"+i);
				getLogger().info(">>>>>>>>>>>>>"+(600*(i-1)));
				Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/app.nsf/LinkeyGetXmlData?openagent&Num=G_App099_005&rdc=0.****************");
				connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
				connection.cookies(cookies.get(account));
				connection.data("start",600*(i-1)+"");
				connection.data("limit","600");
				connection.data("Num","G_App099_005");
				connection.data("QViewName","");
				connection.data("UserName","陈琛100078");
				
				Response  fileResp=connection.timeout(1000*60*10).ignoreContentType(true).execute();
				File file=new File("d:/xml/done/"+i+".xml");
				if(!file.exists()) {
					file.createNewFile();
				}
				FileOutputStream out = (new FileOutputStream(file));
				out.write(fileResp.bodyAsBytes());           
				out.close();
				
				long end = System.currentTimeMillis();
				
				getLogger().info(i+">>>>>"+(end-begin)/1000);
				
			}
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
		}
	}
	public  String loadTitle(String account,String docUnId,String wfId){
		try {
			
			Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/linkey_workflow_engine.nsf/workflow_doc?readform");
			connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
			connection.cookies(cookies.get(account));
			connection.data("WF_ProcessUNID","060874588BF08B5A48257FB900260502");
			connection.data("WF_DocUNID",wfId);
			connection.data("RuleNum","RU016_001");
			connection.data("ProcessNumber","");
			connection.data("UserName","陈琛100078");
			
			org.jsoup.nodes.Document  doc=connection.timeout(1000*60*10).ignoreContentType(true).get();
			String title = doc.getElementById("Purpose").text();
//			String title = doc.selectFirst("[name='Purpose']").val();
			return title;
				
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
			return "";
		}
	}
		
	public void renderTitle() {
		String sql = "select * from yq_finance_pay where Purpose='' order by WF_DocCreated desc limit 1000";
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql, new Object[] {},new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject object:list) {
					String docunid = object.getString("DOCUNID");
					String wfDocunid = object.getString("WF_DOCUNID");
					String title = loadTitle("100078",docunid,wfDocunid);
					
					this.getQuery().executeUpdate("update yq_finance_pay set Purpose = ? where DocUNID = ?",title,docunid);
				}
			}
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	
	@SuppressWarnings("unchecked")
	public void toDbSave(){
		for(int i =1;i<=10;i++) {
			try {
				String path = Globals.TMP_DIR+ File.separator+"xml"+File.separator+i+".xml";
				String xml = FileKit.readToString(path);
				
				Document doc = DocumentHelper.parseText(xml);
				Element root = doc.getRootElement();
				List<Element> childElements  = root.elements("Item");
				for (Element child : childElements) {
					Element itemAttributes = child.element("ItemAttributes");
					EasyRecord record =  new EasyRecord("yq_finance_pay","DocUNID");
					record.set("DocUNID", child.elementText("DocUNID"));
					record.set("import_time", EasyDate.getCurrentDateString());
					Iterator<Element>  iterator =	itemAttributes.elementIterator();
					while(iterator.hasNext()) {
						Element element = iterator.next();
						record.set(element.getName(),element.getTextTrim());
					}
					try {
						this.getQuery().save(record);
					} catch (SQLException ex) {
						ex.printStackTrace();
					}
				}
			} catch (DocumentException e) {
				getLogger().error(null,e);
			} catch (IOException e) {
				getLogger().error(null,e);
			}
			
			
		}
	}	

}







