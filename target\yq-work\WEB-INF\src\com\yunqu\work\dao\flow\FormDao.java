package com.yunqu.work.dao.flow;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "FormDao")
public class FormDao extends AppDaoContext{

	@WebControl(name = "formInfo",type = Types.RECORD)
	public JSONObject formInfo() {
		return queryForRecord("select * from yq_flow_form where form_id = ?", param.getString("formId"));
	}
	
	@WebControl(name = "formDict",type = Types.DICT)
	public JSONObject formDict() {
		return getDictByQuery("select form_id,form_name from yq_flow_form where  1=1");
	}
	
	@WebControl(name = "formList",type = Types.TEMPLATE)
	public JSONObject formList() {
		EasySQL sql = getEasySQL("select * from yq_flow_form where 1=1");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
}
