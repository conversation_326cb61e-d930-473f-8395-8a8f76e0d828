package com.yunqu.work.base;

import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.db.EasyQuery;
public abstract class AppBaseServlet extends EasyBaseServlet { 
	private static final long serialVersionUID = 1L;
	
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_NAME;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getResId() {
		return null;
	}
	public EasyQuery getMainQuery(){
		return EasyQuery.getQuery(getAppName(), Constants.MARS_DS_NAME);
	}

	protected String getRequestUrl() {
		return getRequest().getRequestURI()+"?"+getRequest().getQueryString();
	}
	protected String getUserId() {
		return getUserPrincipal().getUserId();
	}
}
