function handleAjax(url, param, type) {
    return $.ajax(url, param, type).then(function(resp){
        // 成功回调
        if(resp.result){
            return resp.data; // 直接返回要处理的数据，作为默认参数传入之后done()方法的回调
        }
        else{
            return $.Deferred().reject(resp.msg); // 返回一个失败状态的deferred对象，把错误代码作为默认参数传入之后fail()方法的回调
        }
    }, function(err){
        // 失败回调
        console.log(err.status); // 打印状态码
    });
}