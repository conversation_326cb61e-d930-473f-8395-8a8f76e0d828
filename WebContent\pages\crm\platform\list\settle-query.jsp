<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<EasyTag:override name="head">
    <title>结算明细</title>
    <style>
        .ibox-content .layui-table-cell {
            font-size: 13px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form autocomplete="off" onsubmit="return false;" class="form-inline" id="searchForm">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <div class="input-group input-group-sm" style="width: 180px">
                        <span class="input-group-addon">平台类型</span>
                        <SELECT name="platformTypeId" class="form-control input-sm"
                                data-mars="BusinessPlatformDao.getPlatformTypeDict"
                                onchange="SettleList.loadPlatforms(this.value)">
                            <option value="">全部</option>
                        </SELECT>
                    </div>
                    <div class="input-group input-group-sm" style="width: 200px">
                        <span class="input-group-addon">业务平台</span>
                        <SELECT name="platformId" class="form-control input-sm"
                                data-mars="BusinessPlatformDao.getPlatformDict">
                            <option value="">全部</option>
                        </SELECT>
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">结算类型</span>
                        <select name="settlementType" class="form-control input-sm">
                            <option value="">全部</option>
                            <option value="SAAS">SAAS</option>
                            <option value="PAAS">PAAS</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">回款状态</span>
                        <select name="paymentStatus" class="form-control input-sm">
                            <option value="">全部</option>
                            <option value="0">未回款</option>
                            <option value="1">部分回款</option>
                            <option value="2">已回款</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">客户名称</span>
                        <input type="text" name="custName" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">合同名称</span>
                        <input type="text" name="contractName" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">结算编号</span>
                        <input type="text" name="settlementNo" class="form-control input-sm">
                    </div>
                </div>
                <div class="form-group input-group-sm">
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">结算月份</span>
                        <input type="text" name="monthBegin" class="form-control input-sm" style="width: 90px;"
                               onClick="WdatePicker({dateFmt:'yyyy-MM'})">
                        <input type="text" name="monthEnd" class="form-control input-sm" style="width: 90px;"
                               onClick="WdatePicker({dateFmt:'yyyy-MM'})">
                    </div>
                    <div class="input-group input-group-sm" style="width: 130px">
                        <span class="input-group-addon">合同销售</span>
                        <input type="hidden" name="saleBy">
                        <input type="text" name="saleName" onclick="singleUser(this);" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm">
                        <button type="button" class="btn btn-sm btn-info" data-event="enter"
                                onclick="SettleList.query()"><span class="glyphicon glyphicon-search"></span> 搜索
                        </button>
                        <button type="button" class="btn btn-sm btn-default ml-5"
                                onclick="$('#searchForm')[0].reset();SettleList.query();">重置
                        </button>
                    </div>
                </div>
            </div>
            <div class="ibox-content">
                <table class="layui-hide" id="settleListTable"></table>
            </div>
        </div>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        var SettleList = {
            init: function () {
                $("#searchForm").initTable({
                        mars: 'CustOperateDao.settleQueryPageList',
                        id: 'settleListTable',
                        height: 'full-110',
                        limit: 30,
                        toolbar: true,
                        defaultToolbar: ['filter', 'print', 'exports'],
                        autoSort: false,
						rowDoubleEvent(row) {
							settleDetail(row.SETTLEMENT_ID);
						},
                        cols: [[
                            {
                                title: '序号',
                                type: 'numbers',
                                align: 'left'
                            }, {
                                field: 'PLATFORM_TYPE_NAME',
                                title: '平台类型',
                                width: 120
                            }, {
                                field: 'PLATFORM_NAME',
                                title: '平台名称',
                                width: 120,
                                templet: '<div><a href="javascript:;" onclick="platformDetail(\'{{d.PLATFORM_ID}}\')">{{d.PLATFORM_NAME}}</a></div>'
                            }, {
                                field: 'SETTLEMENT_NO',
                                title: '结算编号',
                                width: 140,
                                templet: '<div><a href="javascript:;" onclick="settleDetail(\'{{d.SETTLEMENT_ID}}\')">{{d.SETTLEMENT_NO}}</a></div>'
                            }, {
                                field: 'MONTH_ID',
                                title: '结算月份',
                                width: 80,
                                align: 'center'
                            }, {
                                field: 'MARK_DATE',
                                title: '开票时间',
                                width: 100,
                                align: 'center'
                            }, {
                                field: 'SETTLEMENT_TYPE',
                                title: '结算类型',
                                width: 80,
                                align: 'center'
                            }, {
                                field: 'CONTRACT_NO',
                                title: '合同编号',
                                width: 120,
                                templet: '<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_NO}}</a></div>'
                            }, {
                                field: 'CONTRACT_SIMPILE_NAME',
                                title: '合同名称',
                                minWidth: 180,
                                templet: '<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_NAME}}</a></div>'
                            }, {
                                field: 'CUST_NAME',
                                title: '客户名称',
                                width: 150,
                                templet: '<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
                            }, {
                                field: 'SALES_BY_NAME',
                                title: '合同销售',
                                width: 80,
                                align: 'center'
                            }, {
                                field: 'TOTAL_KP_AMOUNT',
                                title: '开票金额(含税)',
                                width: 110,
                                align: 'center'
                            }, {
                                field: 'TOTAL_KP_NO_TAX',
                                title: '开票金额(税后)',
                                width: 110,
                                align: 'center'
                            }, {
                                field: 'TOTAL_COST_AMOUNT',
                                title: '成本(含税)',
                                width: 100,
                                align: 'center'
                            }, {
                                field: 'TOTAL_COST_NO_TAX',
                                title: '成本(不含税)',
                                width: 110,
                                align: 'center'
                            }, {
                                field: 'GROSS_PROFIT',
                                title: '毛利',
                                width: 90,
                                align: 'center'
                            }, {
                                field: 'GROSS_PROFIT_RATE',
                                title: '毛利率%',
                                width: 90,
                                align: 'center',
								templet:'<div>{{d.GROSS_PROFIT_RATE}}%</div>'
                            }, {
                                field: 'PAYMENT_STATUS',
                                title: '回款状态',
                                width: 100,
                                align: 'center',
                                templet: function (d) {
                                    var status = ['未回款', '部分回款', '已回款'];
                                    return status[d.PAYMENT_STATUS] || '未知';
                                }
                            }, {
                                field: 'PAY_AMOUNT',
                                title: '已回款金额',
                                width: 100,
                                align: 'center'
                            }, {
                                field: 'INVOICE_NOS',
                                title: '发票编号',
                                width: 120,
                            }, {
                                field: 'PAYMENT_TIME',
                                title: '最近回款',
                                width: 90,
                                align: 'center'
                            }, {
                                field: 'REMARK',
                                title: '备注',
                                width: 150
                            }, {
                                field: 'CREATE_NAME',
                                title: '创建人',
                                width: 90,
                                align: 'center'
                            }, {
                                field: 'CREATE_TIME',
                                title: '创建时间',
                                width: 160,
                                align: 'center'
                            }
                        ]]
                    }
                );
            },
            loadPlatforms: function (platformTypeId) {
                $("[name='platformId']").render({data: {platformTypeId: platformTypeId}});
            },
            query: function () {
                $("#searchForm").queryData({id: 'settleListTable', jumpOne: true});
            }
        }

        $(function () {
            $("#searchForm").render({
                success: function () {
                    SettleList.init();
                }
            });
        });

        function platformDetail(platformId) {
            popup.openTab({
                id: 'platformDetail',
                title: '平台详情',
                url: '${ctxPath}/pages/crm/platform/business-platform-detail.jsp',
                data: {platformId: platformId}
            });
        }

        function contractDetail(contractId) {
            popup.openTab({
                id: 'contractDetail',
                title: '合同详情',
                url: '${ctxPath}/project/contract',
                data: {contractId: contractId, isDiv: 0}
            });
        }

        function custDetail(custId) {
            popup.openTab({
                id: 'custDetail',
                title: '客户详情',
                url: '${ctxPath}/pages/crm/cust/cust-detail.jsp',
                data: {custId: custId, isDiv: 0}
            });
        }

        function settleDetail(settlementId) {
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                shadeClose: false,
                title: '结算详情',
                offset: 'r',
                area: ['800px', '100%'],
                url: "${ctxPath}/pages/crm/platform/include/settle-detail.jsp",
                data: {settlementId: settlementId}
            });
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %> 
