<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<form id="approveForm" style="padding: 0px 15px;">
	<input id="checkType" type="hidden"/>
	<table class="table table-vzebra mt-10">
		<tbody id="checkResultTr">
	   	  <tr>
	   		<td class="checkTitle" style="width: 120px;">审批结果</td>
	   		<td>
	   			<div class="pull-left">
		   			<label class="radio radio-inline radio-success">
		            	<input type="radio" value="1" name="checkResult" checked="checked"> <span>通过</span>
		            </label>
		            <label class="radio radio-inline radio-success">
		            	<input type="radio" value="2" name="checkResult"> <span>退回</span>
		            </label>
	   			</div>
	            
	            <button class="btn btn-xs btn-default" type="button" style="position: absolute;right: 20px;" href="javascript:;" onclick="$(this).next().toggle();layer.tips('再次点击恢复',this)"><i class="glyphicon glyphicon-play"></i>更多操作</button>
	            
	            <div  class="pull-left ml-10" style="display: none;">
		            <label class="radio radio-inline radio-danger">
		            	<input type="radio" value="10" name="checkResult"> <span>委托</span>
		            </label>
		            <label class="radio radio-inline radio-danger">
		            	<input type="radio" value="20" name="checkResult"> <span>沟通</span>
		            </label>
		            <label class="radio radio-inline radio-danger">
		            	<input type="radio" value="30" name="checkResult"> <span>协办</span>
		            </label>
	            </div>
	   		</td>
	   	  </tr>
	   	</tbody>
	   	<tbody class="checkTbody">
		   	<tr>
		   		<td class="checkTitle">即将流向</td>
		   		<td>
		   			<span class="nextCheckInfo mr-20"></span>
		   			<span class="returnCheckInfo"></span>
		   			<label class="checkbox checkbox-inline checkbox-success hidden">
	                    <input type="checkbox" value="1" checked="checked" name="noticeType"><span>邮件</span>
	              	</label>
		   			<label class="checkbox checkbox-inline checkbox-success hidden">
	                    <input type="checkbox" value="2" checked="checked" name="noticeType"><span>微信</span>
	              	</label>
	              	
	           		 <button class="btn btn-xs btn-default" type="button" style="position: absolute;right: 44px;" onclick="$('.ccSelect').toggle();layer.tips('再次点击恢复',this)"><i class="glyphicon glyphicon-play"></i>抄送</button>
		   		</td>
		   	</tr>
		   	<tr class="hidden">
		   		<td>通知紧急程度</td>
		   		<td>
		   			<label class="radio radio-inline radio-success">
		            	<input type="radio" value="1" name="urgent"> <span>紧急</span>
		            </label>
		            <label class="radio radio-inline radio-success">
		            	<input type="radio" value="2" name="urgent"> <span>急</span>
		            </label>
		            <label class="radio radio-inline radio-success">
		            	<input type="radio" value="3" name="urgent" checked="checked"> <span>一般</span>
		            </label>
		   		</td>
		   	</tr>
		   	<tr class="checkSelect" style="display: none;">
		   		<td style="width: 120px;">选择下步处理人</td>
		   		<td>
		   			<input type="hidden" id="selectUserId" name="selectUserId"/>
				    <input type="text" style="width: 95%;" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm select-icon" id="selectUserName" name="selectUserName"/>
		   		</td>
		   	</tr>
		   	<tr class="ccSelect" style="display: none;">
		   		<td style="width: 120px;">抄送人/可选</td>
		   		<td>
		   			<input type="hidden" name="ccIds"/>
				    <input type="text" style="width: 95%;" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm select-icon" name="ccNames"/>
		   		</td>
		   	</tr>
		   	<tr>
		   		<td rowspan="2" id="opinionTitle">
		   			审批意见
		   		</td>
		   		<td>
		   			<select class="form-control input-sm" onchange="$('#checkDesc').val(this.value)" data-mars="FlowCommon.approveWordDict" style="width: 180px;display: inline-block;">
		   				<option value="">选择[常用意见]</option>
		   				<option value="同意">同意</option>
		   				<option value="不同意">不同意</option>
		   			</select>
		   			<a href="javascript:;" class="ml-5 f-12" onclick="selectCommonOpinion()">自定义</a>
		   			<a href="javascript:;" class="ml-10 f-12" onclick="FlowCore.writeSignature(this)">电子签名</a>
		   			<input name="signtrue" id="signtrue" type="hidden">
		   		</td>
		   	</tr>
		   	<tr>
		   		<td>
		   			<textarea style="height: 100px;width: 95%;" id="checkDesc" name="checkDesc" class="form-control"></textarea>
		   		</td>
		   	</tr>
	   	</tbody>
	   	<tbody  class="trustTbody" style="display: none;">
	   		<tr>
		   		<td style="width: 120px;">选择委托人</td>
		   		<td>
		   			<input type="hidden" id="trustUserId" name="trustUserId"/>
				    <input type="text" style="width: 95%;" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" id="trustUserName" name="trustUserName"/>
		   		</td>
		   	</tr>
		   	<tr>
		   		<td>委托原因</td>
		   		<td>
		   			<textarea style="height: 100px;width: 95%;" id="trustReason" name="trustReason" class="form-control"></textarea>
		   		</td>
		   	</tr>
	   	</tbody>
	   	<tbody  class="xbTbody" style="display: none;">
	   		<tr>
		   		<td class="checkTitle" style="width: 120px;">选择协办人</td>
		   		<td>
		   			<input type="hidden" id="xbUserId" name="xbUserId"/>
				    <input type="text" style="width: 95%;" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" id="xbUserName" name="xbUserName"/>
		   		</td>
		   	</tr>
		   	<tr>
		   		<td class="checkTitle">协办原因</td>
		   		<td>
		   			<textarea style="height: 100px;width: 95%;" id="xbReason" name="xbReason" class="form-control"></textarea>
		   		</td>
		   	</tr>
	   	</tbody>
	   <tbody class="chatTbody" style="display: none;">
	   		<tr>
		   		<td class="checkTitle" style="width: 120px;">选择沟通人</td>
		   		<td>
		   			<input type="hidden" id="toUserId" name="toUserId"/>
				    <input type="text" style="width: 95%;" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" id="toUserName" name="toUserName"/>
		   		</td>
		   	</tr>
		   	<tr>
		   		<td class="checkTitle">沟通反馈内容</td>
		   		<td>
		   			<textarea style="height: 100px;width: 95%;" name="flowContent" class="form-control"></textarea>
		   		</td>
		   	</tr>
	   </tbody>
   		<!-- <tr>
	   		<td>通知选项</td>
	   		<td>
	   			<select class="form-control input-sm f-12" name="flowNotFinishNotice" style="width: 180px;display: inline-block;">
	   				<option value="">流程未完成通知我</option>
	   				<option value="73">3天</option>
	   				<option value="120">5天</option>
	   				<option value="168">7天</option>
	   				<option value="240">10天</option>
	   			</select>
	   			<label class="checkbox checkbox-inline checkbox-success">
                    <input type="checkbox" value="1" name="flowOkNotice"><span>流程结束后通知我</span>
              	</label>
	   			<label class="checkbox checkbox-inline checkbox-success">
                    <input type="checkbox" value="1" name="flowNewNotice"><span>流程跟踪</span>
              	</label>
	   		</td>
	   	</tr> -->
	   	<tr class="approve-upload" style="display: none;">
	   		<td>附件</td>
	   		<td>
	   			<button type="button" class="btn btn-xs btn-default approve-upload-btn">+上传附件</button>
	   		</td>
	   	</tr>
	 </table>
	<div class="layer-foot text-c">
	  	 <button class="btn btn-info btn-sm ok-layer" type="button" onclick="Flow.submitCheck();"> 确 定 </button>
	  	 
	  	 <div class="fail-layer" style="display: none;">
		  	 <button class="btn btn-danger btn-sm" type="button" onclick="Flow.submitCheck();"> 退回申请人 </button>
		  	 <div class="btn-group btn-group-sm dropup hidden">
				 <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown"> 退回其它 <span class="caret"></span></button>
				 <ul class="dropdown-menu">
				    <li><a href="javascript:;" onclick="Flow.submitCheck('1');">退回申请人</a></li>
				    <li><a href="javascript:;" onclick="Flow.submitCheck('2');">退回上一级</a></li>
				</ul>
			</div>
	  	 </div>
	  	 
	  	  <button class="btn btn-warning btn-sm xbTbody" style="display: none;" type="button" onclick="FlowCore.toFlowAssist();"> 提交  </button>
	  	  
	  	  <button class="btn btn-success btn-sm chatTbody" style="display: none;" type="button" onclick="FlowCore.doFlowChat();"> 提交  </button>
	  	 
	  	  <button class="btn btn-success btn-sm trustTbody" style="display: none;" type="button" onclick="FlowCore.doFlowTrust();"> 提交  </button>
	</div>
</form>
<script type="text/javascript">

	 function selectCommonOpinion(){
		 popup.layerShow({id:'selectCommonOpinion',full:fullShow(),url:'/yq-work/pages/flow/config/word-edit.jsp',title:'常用回复语',area:['600px','100%'],offset:'r'});
	 }
	 
	 $(function(){
		 $('#approveForm').render();
		 
		 $('#checkResultTr input[type=radio][name=checkResult]').change(function() {
			 	$('.checkTbody,.xbTbody,.chatTbody,.trustTbody,.ok-layer,.fail-layer').hide();
		        if (this.value == '1') {
		        	$('.checkTbody,.ok-layer,.nextCheckInfo').show();
		        	$('.returnCheckInfo').hide();
		        }else if (this.value == '2') {
		        	$('.checkTbody,.fail-layer,.returnCheckInfo').show();
		        	$('.nextCheckInfo').hide();
		        	$('.returnCheckInfo').text('申请人');
		        }else if (this.value == '10') {
		        	$('.trustTbody').show();
		        }else if (this.value == '20') {
		        	$('.chatTbody').show();
		        }else if (this.value == '30') {
		        	$('.xbTbody').show();
		        }
		    });
	 });

</script>