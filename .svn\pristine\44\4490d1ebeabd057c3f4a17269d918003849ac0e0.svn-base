package com.yunqu.work.base;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.db.EasyQuery;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;
public abstract class AppBaseServlet extends EasyBaseServlet { 
	private static final long serialVersionUID = 1L;
	
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_NAME;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	protected boolean hasRole(String roleId) {
		return getUserPrincipal().isRole(roleId);
	}
	
	protected boolean hasRes(String resId) {
		return getUserPrincipal().isResource(resId);
	}
	
	@Override
	protected String getResId() {
		return null;
	}
	
	public void renderHtml(String html) {
		super.renderHtml("<div style='padding:30px;background-color: #fff;height: calc(100vh - 100px);font-size:22px'>"+html+"</div>");
	}
	
	protected EasyQuery getMainQuery(){
		return EasyQuery.getQuery(getAppName(), Constants.MARS_DS_NAME);
	}
	
	protected EasyQuery getQuery(int convertField) {
		EasyQuery query =  super.getQuery();
		query.setConvertField(convertField);
		return query;
	}

	protected String getRequestUrl() {
		return getRequest().getRequestURI()+"?"+getRequest().getQueryString();
	}
	protected String getDeptId() {
		return getStaffInfo().getDeptId();
	}
	
	protected String getDeptName() {
		return getStaffInfo().getDeptName();
	}
	
	protected String getUserId() {
		return getUserPrincipal().getUserId();
	}
	
	protected String getUserName() {
		return getUserPrincipal().getUserName();
	}
	
	protected StaffModel getStaffInfo() {
		Object object =  getUserPrincipal().getAttribute("staffInfo");
		if(object==null) {
			StaffModel staffModel = StaffService.getService().getStaffInfo(getUserId());
			getUserPrincipal().setAttribute("staffInfo",staffModel);
			return staffModel;
		}
		String jsonStr = JSONObject.toJSONString(object);
		JSONObject jsonObject = JSONObject.parseObject(jsonStr);
		return JSONObject.toJavaObject(jsonObject, StaffModel.class);
	}
	protected String getBaseDir() {
		String basePath = AppContext.getContext(Constants.APP_NAME).getProperty("basePath", "/home/<USER>/");
		return basePath+"files/";
	}
	

}
