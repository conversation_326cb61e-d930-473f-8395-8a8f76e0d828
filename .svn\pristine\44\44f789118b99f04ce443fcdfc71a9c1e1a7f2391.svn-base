package com.yunqu.work.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class DateUtils {

	public static int getPlanMaxDay(int days){
		 Calendar cal = Calendar.getInstance();
		 cal.setTime(new Date());
		 cal.add(Calendar.DATE, days);
         SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");  
         return Integer.valueOf(yyyyMMdd.format(cal.getTime()));
	}
	public static String getTodayHm(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		SimpleDateFormat hhmm = new SimpleDateFormat("HH:mm");  
		return hhmm.format(cal.getTime());
	}
	public static int getPlanToday(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");  
		return Integer.valueOf(yyyyMMdd.format(cal.getTime()));
	}
	public static int getHour(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		SimpleDateFormat hh = new SimpleDateFormat("HH");  
		return Integer.valueOf(hh.format(cal.getTime()));
	}
	public static void main(String[] args) {
		System.out.println(getHour());
	}
	public static int getTodayYm(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMM");  
		return Integer.valueOf(yyyyMMdd.format(cal.getTime()));
	}
	public static String getDate(String date,String patten){
		SimpleDateFormat sdf = new SimpleDateFormat(patten);  
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");  
		try {
			Date d=sdf2.parse(date);
			Calendar cal = Calendar.getInstance();
			cal.setTime(d);
			return sdf.format(cal.getTime());
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return null;
	}
	public static int getWeek(String dateStr){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");  
		try {
			Date date=sdf.parse(dateStr);
			Calendar cal = Calendar.getInstance();
			cal.setTime(date);
			return cal.get(Calendar.DAY_OF_WEEK);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return 0;
	}
	public static int getTodayWeek(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		return cal.get(Calendar.DAY_OF_WEEK);
	}
	
	public static JSONArray getBookFoodPlatData(int days){  
		 JSONArray array=new JSONArray();
		 SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");  
		 SimpleDateFormat yyyyMM = new SimpleDateFormat("yyyyMM");  
         Calendar dBegin=Calendar.getInstance();
         dBegin.setTime(new Date());
         
         Calendar endDate = Calendar.getInstance();
         endDate.setTime(new Date());
         endDate.add(Calendar.DATE,days);
         
	     while (endDate.getTime().after(dBegin.getTime())){  
	    	 dBegin.add(Calendar.DAY_OF_MONTH, 1); 
	    	 JSONObject result=new JSONObject();
	    	 Date date=dBegin.getTime();
	    	 result.put("YMD",yyyyMMdd.format(date));
        	 result.put("YM",yyyyMM.format(date));
        	 result.put("WEEK", dBegin.get(Calendar.DAY_OF_WEEK));
        	 array.add(result);
	     }  
	     return array;  
	 }
}
