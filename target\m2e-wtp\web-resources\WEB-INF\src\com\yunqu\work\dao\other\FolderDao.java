package com.yunqu.work.dao.other;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.FolderModel;

@WebObject(name="FolderDao")
public class FolderDao extends AppDaoContext {
	
	@WebControl(name = "parentFolderName",type = Types.TEXT)
	public JSONObject parentFolderName() {
		EasySQL sql = new EasySQL();
		sql.append(param.getString("pFolderId"),"select folder_name from  yq_folder where folder_id = ?");
		return getText(queryForString(sql.getSQL(),sql.getParams()));
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		FolderModel model=new FolderModel();
		model.setColumns(getParam("folder"));
		return queryForRecord(model);
	}
	
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select folder_id,folder_name from yq_folder");
	}
	
	/**
	 *0 全员可见， 1:自己可见 ，2 部门可见 ，3 指定可见
	 * @return
	 */
	@WebControl(name="docFolder",type=Types.LIST)
	public JSONObject docFolder(){
		EasySQL sql = new EasySQL();
		int folderAuth = param.getIntValue("folderAuth");
		if(folderAuth==0) {
			sql.append("select * from YQ_FOLDER where 1=1");
			sql.append(0,"and folder_auth = ?");
		}else if(folderAuth==1) {
			sql.append(getUserId(),"select * from yq_folder where folder_auth = 1 and creator= ?");
		}else if(folderAuth==2) {
			sql.append(getDeptId(),"select * from yq_folder where folder_auth = 2 and dept_id = ?");
		}else if(folderAuth==3) {
			sql.append("select * from yq_folder where folder_auth = 3");
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,share_user_ids)");
			sql.append("or");
			sql.append(getDeptId(),"find_in_set(?,dept_id)");
			sql.append("or");
			sql.append(getUserId(),"creator = ?");
			sql.append(")");
		}
		String folderCode = param.getString("folderCode");
		if(StringUtils.notBlank(folderCode)) {
			sql.append(folderCode,"and find_in_set(?,path)");
		}
		sql.append(0,"and folder_state = ?");
		sql.append("and source = '0'");
		sql.append("order by order_index,create_time");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="fileFolder",type=Types.LIST)
	public JSONObject fileFolder(){
		String source = param.getString("source");
		if(StringUtils.isBlank(source)) {
			return emptyPage();
		}
		EasySQL sql = new EasySQL();
		sql.append("select * from yq_folder where 1=1");
		sql.append(0,"and folder_auth = ?");
		sql.append(0,"and folder_state = ?");
		sql.append(source,"and source = ?");
		String folderCode = param.getString("folderCode");
		if(StringUtils.notBlank(folderCode)) {
			sql.append(folderCode,"and find_in_set(?,path)");
		}
		sql.append("order by order_index,create_time");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="docsFolder",type=Types.LIST)
	public JSONObject docsFolder(){
		EasySQL sql = new EasySQL();
		sql.append("select * from yq_folder where 1=1");
		sql.append(0,"and folder_state = ?");
		sql.append("and source = '0'");
		String folderCode = param.getString("folderCode");
		if(StringUtils.notBlank(folderCode)) {
			sql.append(folderCode,"and find_in_set(?,path)");
		}else {
			sql.append(0,"and folder_auth = ?");
		}
		sql.append("order by order_index,create_time");
		JSONObject result =  queryForList(sql.getSQL(), sql.getParams());
		
		EasySQL _sql = new EasySQL("select t1.title,t1.folder_id,t1.doc_code,t1.doc_id from yq_doc t1,yq_folder t2 where t1.folder_id = t2.folder_id");
		_sql.append(0,"and t2.folder_state = ?");
		_sql.append("and t2.source = '0'");
		if(StringUtils.notBlank(folderCode)) {
			_sql.append(folderCode,"and find_in_set(?,t2.path)");
		}else {
			_sql.append(0,"and t2.folder_auth = ?");
		}
	    try {
			List<JSONObject> list = getQuery().queryForList(_sql.getSQL(), _sql.getParams(),new JSONMapperImpl());
			result.put("docs", list);
		} catch (SQLException e) {
			this.error(null, e);
		}		
		return result;
		
	}
	
	/**
	 * 文档库
	 * @return
	 */
	@WebControl(name="list",type=Types.TEMPLATE)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
		sql.append(0,"and folder_state = ?");
		sql.append(param.getString("folderId"),"and p_folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and folder_name like ?");
		sql.append(param.getString("fkId"),"and fk_id = ?");
		sql.append(0,"and folder_auth = ?");
		sql.append(param.getString("source"),"and source = ?","0");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 文件夹下面的doc文档
	 * @return
	 */
	@WebControl(name="folderDocList",type=Types.LIST)
	public JSONObject folderDocList(){
		EasySQL sql=getEasySQL("select t2.folder_name,t1.doc_id,t1.doc_code,t1.folder_id,t1.manager,t1.title,t1.doc_auth,t1.view_count,t1.efficient,t1.create_time,t1.update_time,t1.creator,t1.create_name,t1.update_by from yq_doc t1,yq_folder t2 where t1.folder_id = t2.folder_id");
		String folderId = param.getString("folderId");
		String folderCode = param.getString("folderCode");
		int subQuery = param.getIntValue("subQuery");
		if(subQuery==0&&StringUtils.notBlank(folderCode)) {
			sql.append(folderCode,"and find_in_set(?,t2.path)");
		}else {
			sql.append(folderId,"and t1.folder_id = ?");
		}
		sql.appendLike(param.getString("folderName"),"and t1.title like ?");
		int folderAuth = param.getIntValue("folderAuth");
		if(folderAuth==0) {
			sql.append(0,"and t2.folder_auth = ?");
		}else if(folderAuth==1) {
			sql.append(getUserId(),"and t2.folder_auth = 1 and t2.creator= ?");
		}else if(folderAuth==2) {
			sql.append(getDeptId(),"and t2.folder_auth = 2 and t2.dept_id = ?");
		}else if(folderAuth==3) {
			sql.append("and t2.folder_auth = 3");
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,t2.share_user_ids)");
			sql.append("or");
			sql.append(getDeptId(),"find_in_set(?,t2.dept_id)");
			sql.append("or");
			sql.append(getUserId(),"t2.creator = ?");
			sql.append(")");
		}
		sql.append(1,"and t1.doc_auth = ?");
		sql.append(0,"and t2.folder_state = ?");
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectLink",type=Types.TEMPLATE)
	public JSONObject projectLink(){
		EasySQL sql = getEasySQL("select * from yq_project_link where 1=1");
		sql.append(param.getString("folderId"),"and p_id = ?");
		sql.append(param.getString("projectId"),"and project_id = ?",false);
		sql.append(0,"and state = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectAIDoc",type=Types.TEMPLATE)
	public JSONObject projectAIDoc(){
		EasySQL sql = getEasySQL("select * from yq_ai_editor where 1=1");
		sql.append(param.getString("projectId"),"and project_id = ?",false);
		sql.append("order by update_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="linkInfo",type=Types.RECORD)
	public JSONObject linkInfo(){
		EasySQL sql=getEasySQL("select * from yq_project_link where 1=1");
		sql.append(param.getString("linkId"),"and link_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 自己创建的文档目录
	 * @return
	 */
	@WebControl(name="myFolderlist",type=Types.LIST)
	public JSONObject myFolderlist(){
		EasySQL sql=getEasySQL("select * from yq_folder where 1=1");
		sql.append(0,"and folder_state = ?");
		sql.append(param.getString("folderId"),"and p_folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and folder_name like ?");
		sql.append(getUserPrincipal().getUserId(),"and creator = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 文件库
	 * @return
	 */
	@WebControl(name="folderFiles",type=Types.LIST)
	public JSONObject folderFiles(){
		EasySQL sql=getEasySQL("select t1.*,t2.folder_name from yq_files t1 left join yq_folder t2 on t1.folder_id = t2.folder_id where 1=1");
		String folderId = param.getString("folderId");
		String folderCode = param.getString("folderCode");
		String seftCreate = param.getString("seftCreate");
		
		String subQuery = param.getString("subQuery");
		if(StringUtils.notBlank(subQuery)&&StringUtils.notBlank(folderCode)) {
			sql.append(folderCode,"and find_in_set(?,t2.path)");
		}else {
			sql.append(folderId,"and t1.folder_id = ?");
		}
		sql.appendLike(param.getString("fileName"),"and t1.file_name like ?");
		sql.appendLike(param.getString("userName"),"and t1.create_name like ?");
		sql.appendLike(param.getString("folderName"),"and t2.folder_name like ?");
		sql.append(param.getString("source"),"and t1.source = ?");
		if(StringUtils.notBlank(seftCreate)) {
			sql.append(getUserId(),"and t1.creator = ?");
		}
		this.setOrderBy(sql);
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	private void setOrderBy(EasySQL sql) {
		String sortName =  param.getString("sortName");
		String sortType =  param.getString("sortType");
		if(StringUtils.notBlank(sortType)) {
			sql.append("order by ").append("t1."+sortName).append(sortType);
		}else {
			sql.append("order by t1.create_time desc");
		}
	}
	
	
	/**
	 * 自己创建的文档文件
	 * @return
	 */
	@WebControl(name="mydoc",type=Types.LIST)
	public JSONObject mydoc(){
		EasySQL sql=getEasySQL("select * from yq_doc where 1=1");
		sql.append(param.getString("folderId"),"and folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and title like ?");
		sql.append(getUserPrincipal().getUserId(),"and creator = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}

}
