package com.yunqu.work.servlet.common;

import java.io.IOException;
import java.util.Map;
import java.util.Set;

import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Path;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.service.StaffService;

@Path(value = "/auth")
public class AuthController extends BaseController {

	public void memos() {
		String acct = getRequest().getRemoteUser();
		this.info(acct+">访问memos", null);
		String url = getRequest().getRequestURL().toString();
		String goUrl = "http://172.16.68.152:5230/api/v1/auth/signin";
		if(url.indexOf("yunqu-info")>-1) {
			
		}
		Connection conn = Jsoup.connect(goUrl);
		JSONObject params = new JSONObject();
		params.put("username", acct);
		params.put("password", "yunqu168@2024");
		params.put("remember", true);
		conn.requestBody(params.toJSONString());
		try {
			Response response = conn.ignoreContentType(true).timeout(3000).method(Method.POST).execute();
			Map<String,String> cookies = response.cookies();
			this.info(cookies, null);
			if(cookies!=null) {
				Set<String> keys = cookies.keySet();
				for(String key:keys) {
					setCookie(key, cookies.get(key), 60*60*24*31, true);
				}
			}else {
				renderHtml("账号异常 请联系管理员");
				return;
			}
			redirect("http://172.16.68.152:5230/");
		} catch (IOException e) {
			this.error(e.getMessage(), e);
			renderHtml(e.getMessage());
		}
	}
	
	public void yapi() {
		String acct = getRequest().getRemoteUser();
		this.info(acct+">访问yapi", null);
		Connection conn = Jsoup.connect("http://172.16.68.152:3001/api/user/login");
		JSONObject params = new JSONObject();
		params.put("email", StaffService.getService().getUserEmail(getUserId()));
		params.put("password", "yq123456");
		conn.requestBody(params.toJSONString());
		try {
			Response response = conn.ignoreContentType(true).timeout(3000).method(Method.POST).execute();
			this.info("yapi>response>"+response.body(),null);
			Map<String,String> cookies = response.cookies();
			this.info(cookies, null);
			if(cookies!=null) {
				Set<String> keys = cookies.keySet();
				for(String key:keys) {
					setCookie(key, cookies.get(key), 60*60*24*6, true);
				}
			}else {
				this.getLogger().info("yapi账号异常 请联系管理员");
				redirect("http://172.16.68.152:3001/");
				return;
			}
			redirect("http://172.16.68.152:3001/");
		} catch (IOException e) {
			this.error(e.getMessage(), e);
			renderHtml(e.getMessage());
		}
	}
	
	public void mindoc() {
		String acct = getRequest().getRemoteUser();
		String url = getPara("url");
		this.info(acct+">访问mindoc>url:"+url, null);
		
		String sourceUrl = getRequest().getRequestURL().toString();
		String goUrl = "http://172.16.68.152:8181/login";
		if(sourceUrl.indexOf("yunqu-info")>0) {
			goUrl = "http://mindoc.yunqu-info.cn/login";
		}
		Connection conn = Jsoup.connect(goUrl);
		
		conn.data("account", acct);
		conn.data("password", "yunqu168@2024");
		conn.data("is_remember", "yes");
		try {
			Response response = conn.ignoreContentType(true).timeout(3000).method(Method.POST).execute();
			Map<String,String> cookies = response.cookies();
			this.info(cookies, null);
			if(cookies!=null) {
				Set<String> keys = cookies.keySet();
				for(String key:keys) {
					setCookie(key, cookies.get(key), 60*60*24*31, true);
				}
			}else {
				renderHtml("账号异常 请联系管理员");
				return;
			}
			this.info(acct+">重定向："+url, null);
			if(StringUtils.notBlank(url)) {
				if(sourceUrl.indexOf("yunqu-info")>0) {
					redirect("http://mindoc.yunqu-info.cn"+url);
				}else {
					redirect("http://172.16.68.152:8181"+url);
				}
			}else {
				if(sourceUrl.indexOf("yunqu-info")>0) {
					redirect("http://mindoc.yunqu-info.cn");
				}else {
					redirect("http://172.16.68.152:8181/");
				}
			}
		} catch (IOException e) {
			this.error(e.getMessage(), e);
			renderHtml(e.getMessage());
		}
	}

}
