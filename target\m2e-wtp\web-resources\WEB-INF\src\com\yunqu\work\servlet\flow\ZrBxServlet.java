package com.yunqu.work.servlet.flow;

import java.math.BigDecimal;
import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.BxService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.utils.NumberToCN;

@WebServlet("/servlet/zr/flow/bx/*")
public class ZrBxServlet extends BaseFlowServlet {
	private static final long serialVersionUID = 1L;

	public EasyResult actionForSubmitApply() {
		JSONObject params = getJSONObject();
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String businessId = params.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			businessId =  FlowService.getService().getID();
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		
		String doAction = params.getString("doAction");
		EasyRecord applyRecord = getApplyRecord(flowCode);
		applyRecord.set("apply_state","submit".equals(doAction)?10:0);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.set("apply_id",businessId);
		applyRecord.setColumns(applyInfo);
		applyRecord.set("apply_no","submit".equals(doAction)?BxService.getService().getZrBxNo():"");
		
		boolean bl = true;
		EasyRecord record = null;
		JSONObject business = JsonKit.getJSONObject(params, "business");
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
			record.set("HZ_MONEY", record.getString("BX_MONEY"));
		}
		try {
			updateItem(params,businessId,false);
			if(record!=null) {
				bl = this.getQuery().save(record);
			}
			if(bl) {
				this.getQuery().save(applyRecord);
			}
			this.saveFile(businessId,params);
			this.updateBxRecord(businessId);
			if("submit".equals(doAction)) {
				String result = this.startFlow(businessId,applyState,params);
				if(result==null) {
					this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
					return EasyResult.fail("流程节点未配置,请联系管理员");
				}
				return EasyResult.ok(businessId,"提交到审批节点:"+result);
			}
			return EasyResult.ok(businessId);
		} catch (Exception e) {
			this.error(null, e);
			this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
			return EasyResult.fail("流程异常,请联系管理员"+e.getMessage());
		}
	}
	
	public EasyResult actionForUpdateApply() {
		JSONObject params = getJSONObject();
		
		String businessId = params.getString("businessId");
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String doAction = params.getString("doAction");
		
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		
		boolean bl = true;
		EasyRecord record = null;
		JSONObject business = JsonKit.getJSONObject(params, "business");
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
		}
		
		EasyRecord applyRecord = null;
		if("submit".equals(doAction)) {
			applyRecord =  getApplyRecord(businessId,flowCode);
		}else {
			applyRecord = getApplyRecord();
		}
		applyRecord.setPrimaryValues(businessId);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.setColumns(applyInfo);
		applyRecord.set("apply_id",businessId);
		
		if("submit".equals(doAction)&&applyState!=FlowConstants.FLOW_STAT_CHECK_RETURN) {
			applyRecord.set("apply_no",BxService.getService().getZrBxNo());
		}

		try {
			updateItem(params,businessId,true);
			if(record!=null) {
				bl = this.getQuery().update(record);
			}
			if(bl) {
				this.getQuery().update(applyRecord);
			}
			this.updateBxRecord(businessId);
			if("submit".equals(doAction)) {
				this.startFlow(businessId,applyState,params);
			}
		} catch (Exception e) {
			this.error(null, e);
			return EasyResult.fail("流程异常,请联系管理员"+e.getMessage());
		}
		return EasyResult.ok();
		
	}
	

	public void updateBxRecord(String businessId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("update zr_flow_bx_base t1 set ");
			sql.append("t1.bx_money = ( select sum(amount) from zr_flow_bx_item t2 where t2.business_id = t1.business_id ),");
			sql.append("t1.hz_money = ( select sum(hz_amount) from zr_flow_bx_item t2 where t2.business_id = t1.business_id)");
			sql.append(businessId,"where t1.business_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(), sql.getParams());
			
			this.getQuery().executeUpdate("update zr_flow_bx_base t1 set t1.pay_money = t1.hz_money - t1.reverse_money where t1.business_id = ?", businessId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
	}
	
	
	private void updateItem(JSONObject params,String bussinessId,boolean isUpdate) throws SQLException {
		Object itemIndexObj = params.get("itemIndex");
		JSONArray itemIndexs = null;
		if(itemIndexObj instanceof String) {
			itemIndexs = new JSONArray();
			itemIndexs.add(itemIndexObj.toString());
		}else {
			itemIndexs = params.getJSONArray("itemIndex");
		}
		for(int i= 0;i<itemIndexs.size();i++) {
			EasyRecord itemModel = new EasyRecord("zr_flow_bx_item","ITEM_ID");
			String item = itemIndexs.getString(i);
			itemModel.set("BUSINESS_ID", bussinessId);
			itemModel.set("BX_DATE", params.getString("bxDate_"+item));
			itemModel.set("CONTRACT_ID", params.getString("contractId_"+item));
			itemModel.set("INVOICE_TYPE", params.getString("invoiceType_"+item));
			itemModel.set("CONTRACT_NAME", params.getString("contractName_"+item));
			itemModel.set("CONTRACT_NO", params.getString("contractNo_"+item));
			itemModel.set("FEE_TYPE", params.getString("feeType_"+item));
			itemModel.set("FEE_DESC", params.getString("feeDesc_"+item));
			itemModel.set("AMOUNT", params.getString("amount_"+item));//含税金额
			itemModel.set("HZ_AMOUNT", params.getString("amount_"+item));//核准金额
			itemModel.set("ITEM_INDEX", params.getString("orderIndex_"+item));
			itemModel.set("DEPT_ID", params.getString("deptId_"+item));
			itemModel.set("DEPT_NAME", params.getString("deptName_"+item));
			itemModel.set("TAX_RATE", params.getString("taxRate_"+item));//税率
			itemModel.set("TAXES", params.getString("taxes_"+item));//税金
			itemModel.set("R_AMOUNT", params.getString("rAmount_"+item));//去税金额
			itemModel.set("PRODUCT_LINE", params.getString("productLine_"+item));
			
			String itemId = params.getString("itemId_"+item);
			if(itemId.length()>20) {
				itemModel.set("ITEM_ID", itemId);
				this.getQuery().update(itemModel);
			}else {
				itemModel.setPrimaryValues(RandomKit.uniqueStr());
				this.getQuery().save(itemModel);
			}
		}
	}
	
	
	public EasyResult actionForUpdateHzMoney() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		BigDecimal hzAmount = params.getBigDecimal("hzAmount");
		String businessId = params.getString("businessId");
		try {
			
			this.getQuery().executeUpdate("update zr_flow_bx_item set hz_amount = ? where item_id = ?",hzAmount,itemId);
			this.getQuery().executeUpdate("update zr_flow_bx_base t1 set t1.hz_money = (select sum(t2.hz_amount) from zr_flow_bx_item t2 where t1.business_id = t2.business_id) where t1.business_id = ?",businessId);
			String num = this.getQuery().queryForString("select t1.hz_money - t1.reverse_money from zr_flow_bx_base t1 where t1.business_id = ?", businessId);
			
			String payMoneyDx = NumberToCN.number2CNMontrayUnit(new BigDecimal(num));
			this.getQuery().executeUpdate("update zr_flow_bx_base t1 set t1.pay_money = ?,t1.pay_money_dx = ? where t1.business_id = ?",num,payMoneyDx,businessId);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelItem() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		try {
			this.getQuery().executeUpdate("delete from zr_flow_bx_item where item_id = ?",itemId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}

}
