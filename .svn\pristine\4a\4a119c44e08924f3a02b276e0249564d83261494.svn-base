<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectDeptForm">
	   			 <div class="layui-row">
		   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
						 <span class="input-group-addon">名称</span>	
						 <input type="text" name="deptName" class="form-control input-sm">
				     </div>
	    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectDept.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	    			 <button type="button" class="btn btn-default btn-sm ml-10" onclick="SelectDept.selfDept()"><i class="fa fa-location-arrow" aria-hidden="true"></i> 当前部门</button>
	    			 
	    			 <label class="checkbox checkbox-inline checkbox-success ml-10">
	                  	<input type="checkbox" value="0" checked="checked" id="fastSelctSelftDept"> <span>记忆选择</span>
	                 </label>
                </div>
            	<div class="ibox" style="padding: 5px 10px;">
              		<table id="deptList"></table>
               </div>
               <div class="layer-foot text-c" style="position: absolute;">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectDept.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectDept={
					sid:'${param.sid}',
					query : function(){
						$("#selectDeptForm").queryData();
					},
					selfDept:function(){
						var staffInfo  = getStaffInfo();
						$("#selectDeptForm [name='deptName']").val(staffInfo.deptName);
						SelectDept.query();
					},
					initTable : function(){
						//$('#selectDept .container-fluid').removeClass('container-fluid');
						
						var data = layui.data('yqwork');
						var fastSelctSelftBxDeptFlag = data.fastSelctSelftBxDeptFlag;
						if(fastSelctSelftBxDeptFlag=='1'){
							var fastSelctSelftBxDept = data.fastSelctSelftBxDept;
							if(fastSelctSelftBxDept){
								$("#selectDeptForm [name='deptName']").val(fastSelctSelftBxDept);
							}
						}else{
							$("#fastSelctSelftDept").click();
						}
						
						$("#selectDeptForm").initTable({
							mars:'BxDao.selectDept',
							id:'deptList',
							page:true,
							limit:50,
							rowDoubleEvent:'SelectDept.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left',
								width:50
							 },{
							    field: 'DEPT_NAME',
								title: '部门',
								width:180
							},{
							    field: 'FINANCE_DEPT_CODE',
								title: '编号',
								width:80
							},{
								field:'TEMP_NAME',
								title:'类型',
								width:100
							}
							]],
							done:function(){
								if(fastSelctSelftBxDeptFlag=='1'){
									var tr = $('#selectDeptForm').find('tbody tr').first();
									tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
									tr.find('.layui-form-radio').trigger("click");
								}
							}
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectDept.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('deptList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var deptTypes = [];
								var deptCodes = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['DEPT_NAME']);
									ids.push(data[index]['DEPT_ID']);
									deptTypes.push(data[index]['TEMP_NAME']);
									deptCodes.push(data[index]['FINANCE_DEPT_CODE']);
									el.attr('data-dept-code',data[index]['FINANCE_DEPT_CODE']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								if(el.prev().prev().length>0){
									el.prev().prev().val(deptTypes.join(','));
								}
								if(el.prev().prev().prev().length>0){
									el.prev().prev().prev().val(deptCodes.join(','));
								}
								selctCallBack && selctCallBack(SelectDept.sid,'dept');
								
								var fastSelctSelftDept = $("#fastSelctSelftDept:checked").val();
								if(fastSelctSelftDept=='0'){
									layui.data('yqwork',{key:'fastSelctSelftBxDept',value:names.join(',')});
									layui.data('yqwork',{key:'fastSelctSelftBxDeptFlag',value:1});
								}else{
									layui.data('yqwork',{key:'fastSelctSelftBxDeptFlag',value:0});
								}
								
								popup.layerClose("selectDeptForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							el.val(selectRow['DEPT_NAME']);
							el.attr('data-dept-code',selectRow['FINANCE_DEPT_CODE']);
							if(el.prev().length>0){
								el.prev().val(selectRow['DEPT_ID']);
							}
							if(el.prev().prev().length>0){
								el.prev().prev().val(selectRow['TEMP_NAME']);
							}
							if(el.prev().prev().prev().length>0){
								el.prev().prev().prev().val(selectRow['FINANCE_DEPT_CODE']);
							}
							selctCallBack && selctCallBack(SelectDept.sid,'dept');
							
							var fastSelctSelftDept = $("#fastSelctSelftDept:checked").val();
							if(fastSelctSelftDept=='0'){
								layui.data('yqwork',{key:'fastSelctSelftBxDept',value:selectRow['DEPT_NAME']});
								layui.data('yqwork',{key:'fastSelctSelftBxDeptFlag',value:1});
							}else{
								layui.data('yqwork',{key:'fastSelctSelftBxDeptFlag',value:0});
							}
							popup.layerClose("selectDeptForm");
						}
					}
					
			};
			$(function(){
				SelectDept.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>