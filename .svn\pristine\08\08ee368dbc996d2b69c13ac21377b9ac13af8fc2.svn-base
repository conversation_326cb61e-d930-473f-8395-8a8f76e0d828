<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目文档</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
		.layui-table-cell {
			height: auto;
		}
		.layui-table-cell{padding: 0 6px;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 2px;
		}
		.btn-group-sm>.btn, .btn-sm {
	       padding: 4px 5px;
	    }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="folderId" id="folderId" type="hidden"/>
			<input name="source" value="project" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>项目文档</h5>
	          		     <div class="input-group input-group-sm" style="width: 160px;">
							 <span class="input-group-addon">项目名称</span>	
							 <input type="text" name="projectName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 160px;">
							 <span class="input-group-addon">项目编号</span>	
							 <input type="text" name="projectNo" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="initPage()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					 	<table class="layui-table folderTable" lay-size="sm" lay-even lay-skin="line">
						  <thead>
						    <tr>
						      <th>名称</th>
						      <th>项目</th>
						      <th>大小</th>
						      <th>创建时间</th>
						      <th>创建者</th>
						      <th>操作</th>
						    </tr> 
						  </thead>
						  <tbody class="folderTbody" data-template="project-link-template" data-mars="ProjectDao.projectLink">
					
						  </tbody>
						  <tbody>
							<tr>
								<td colspan="6" id="pageContainer1"></td>
							</tr>
						  </tbody>
						  <tbody class="fileTbody" data-template="project-detail-template-files" data-mars="ProjectDao.fileListDetail">
					
						  </tbody>
						  <tbody>
							<tr>
								<td colspan="6" id="pageContainer2"></td>
							</tr>
						  </tbody>
						</table>
					</div>
				</div>
		</form>
		<script id="project-detail-template-files" type="text/x-jsrender">
			{{for data}}
				<tr>
				  <td>{{call:FILE_TYPE fn='getFileIcon'}} <a style="color: #337ab7;text-decoration:underline;" href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}&nbsp;</span></a></td>
				  <td onclick="projectDetail('{{:FK_ID}}')">{{:PROJECT_NAME}}</td>
				  <td>{{:FILE_SIZE}}</td>
				  <td nowrap>{{:CREATE_TIME}}</td>
				  <td nowrap>
					  {{:CREATE_NAME}}
				  </td>
				  <td nowrap>
						<div class="btn-group btn-group-sm">
							<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">操作<span class="caret"></span></button>
							<ul class="dropdown-menu dropdown-menu-right">
								   {{if FILE_TYPE=='.xml'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source=project&action=display" target="_blank">编辑器打开</a>&nbsp;</li>
									{{else FILE_TYPE=='.zip'||FILE_TYPE=='.war'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source=project&action=zip.view" target="_blank">预览</a>&nbsp;</li>
									{{else FILE_TYPE=='.pdf'||FILE_TYPE=='.txt'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source=project&action=get" target="_blank">本地打开</a>&nbsp;</li>
									{{else FILE_TYPE=='.xlsx'||FILE_TYPE=='.xls'||FILE_TYPE=='.docx'||FILE_TYPE=='.doc'||FILE_TYPE=='.ppt'||FILE_TYPE=='.pptx'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source=project&action=office" target="_blank">预览</a>&nbsp;</li>
									{{else FILE_TYPE=='.log'||FILE_TYPE=='.txt'||FILE_TYPE=='.out'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source=project&action=tail" target="_blank">tail</a>&nbsp;</li>
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source=project&action=less" target="_blank">less</a>&nbsp;</li>
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source=project&action=grep" target="_blank">grep</a>&nbsp;</li>
								   {{/if}}
								<li><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" target="_blank">下载</a></li>
							</ul>
						</div>
				  </td>
				</tr>
			{{/for}}
		</script>
			<script id="project-link-template" type="text/x-jsrender">
			{{for data}}
				<tr>
				  <td><i class="fa fa-link" aria-hidden="true"></i> <a style="color: #337ab7;text-decoration:underline;" title="{{:REMARK}}" href="{{:LINK_URL}}" target="_blank">{{:LINK_NAME}}</a></td>
				  <td onclick="projectDetail('{{:PROJECT_ID}}')" nowrap>{{:PROJECT_NAME}}</td>
				  <td title="{{:REMARK}}" onclick="top.layer.alert('{{:REMARK}}',{icon:1,offset:'20px'});">{{cutText:REMARK 20}}</td>
				  <td>{{:CREATE_TIME}}</td>
				  <td>
					  {{call:CREATOR fn='getUserName'}}
				  </td>
				  <td>	
					  {{if LINK_URL.indexOf('qq.com')>-1}}
						<a onclick="popup.openTab({id:'docUrl',title:'{{:LINK_NAME}}',url:'{{:LINK_URL}}'});" href="javascript:void(0)">访问</a>
						{{else}}<a href="{{:LINK_URL}}" target="_blank">访问</a>		
					  {{/if}}
				  </td>
				</tr>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		
		function initPage(){
			$("#searchForm").render({data:{pageIndex:1,pageType:3,pageSize:10},success:function(result){
				 layui.use('laypage', function(){
					  var data = result['ProjectDao.projectLink'];
					  if(data){
						  var laypage = layui.laypage;
						  laypage.render({
						    elem: 'pageContainer1',
						    limit:10,
						    layout:['page','prev','next','limit','count'],
						    count: data.totalRow,
						    jump:function(obj, first){
						        if(!first){
						        	$("#searchForm").render({data:{pageIndex:obj.curr,pageType:3,pageSize:obj.limit}});
						        }
						    }
						  });
					  }
					  
					  var data2 = result['ProjectDao.fileListDetail'];
					  if(data2){
						  var laypage = layui.laypage;
						  laypage.render({
						    elem: 'pageContainer2',
						    limit:10,
						    layout:['page','prev','next','limit','count'],
						    count: data2.totalRow,
						    jump:function(obj, first){
						        if(!first){
						        	$("#searchForm").render({data:{pageIndex:obj.curr,pageType:3,pageSize:obj.limit}});
						        }
						    }
						  });
					  }
					});
			}});
		}
		
		var projectDetail  = function(projectId){
			popup.openTab({id:projectId,url:'${ctxPath}/pages/project/project-detail.jsp',title:'项目总览',data:{projectId:projectId}});
		}
		
		$(function(){
			initPage();
		});
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>