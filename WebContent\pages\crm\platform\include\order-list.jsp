<%--
  Created by IntelliJ IDEA.
  User: jily
  Date: 2025/5/15
  Time: 17:50
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/pages/common/global.jsp" %>

<script type="text/html" id="orderToolbar">
    <button type="button" class="btn btn-sm btn-info" onclick="orderList.add()">+ 新增订购</button>
</script>

<table id="orderTable" class="layui-table"></table>

<script type="text/javascript">
    var orderList = {
        init: function(){
            $("#PlatformDetailForm").initTable({
                mars:'PlatformOrderDao.orderPageList',
                id:'orderTable',
                page:true,
                toolbar: '#orderToolbar',
                rowDoubleEvent(row) {
                    orderList.detail(row.ORDER_ID);
                },
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align:'left'
                    },{
                        field: '',
                        title: '操作',
                        width: 100,
                        align: 'center',
                        templet: function(d){
                            var html = '<a href="javascript:;" onclick="orderList.detail(\''+d.ORDER_ID+'\')">详情</a>';
                            html += ' <a href="javascript:;" onclick="orderList.edit(\''+d.ORDER_ID+'\')">编辑</a>';
                            html += ' <a href="javascript:;" onclick="orderList.del(\''+d.ORDER_ID+'\')">删除</a>';
                            return html;
                        }
                    },{
                        field: 'CUST_NAME',
                        title: '客户名称',
                        width:150,
                        templet:'<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
                    },{
                        field: 'SALES_BY_NAME',
                        title: '销售',
                        align: 'center',
                        width:80
                    },{
                        field: 'CONTRACT_NAME',
                        title: '合同名称',
                        width:150,
                        templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_NAME}}</a></div>'
                    },{
                        field: 'BUSINESS_NAME',
                        title: '商机名称',
                        width:150
                    },{
                        field: 'ORDER_STATUS',
                        title: '订购状态',
                        width:80,
                        align: 'center',
                        templet: function(d){
                            var status = ['正常','暂停','终止'];
                            return status[d.ORDER_STATUS] || '未知';
                        }
                    },{
                        field: 'ORDER_AMOUNT',
                        title: '订购金额',
                        width:100,
                        align: 'center'
                    },{
                        field: 'START_DATE',
                        title: '开始日期',
                        width:100,
                        align: 'center'
                    },{
                        field: 'END_DATE',
                        title: '结束日期',
                        width:100,
                        align: 'center'
                    },{
                        field: 'CREATOR',
                        title: '创建人',
                        sort:true,
                        align:'left',
                        width:190,
                        templet:function(row){
                            return getUserName(row.CREATOR)+" "+row.CREATE_TIME;
                        }
                    }
                ]],
                done:function(res){
                    if(res.totalRow != undefined){
                        $("[name='extend.ORDER_COUNT']").text(res.totalRow);
                    }
                }
            });
        },
        add: function(){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'新增订购',
                offset:'20px',
                area:['700px','500px'],
                url:"${ctxPath}/pages/crm/platform/include/add-order.jsp",
                data:{platformId:platformId,platformName:platformInfo.PLATFORM_NAME}
            });
        },
        edit: function(orderId){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'编辑订购',
                offset:'20px',
                area:['700px','500px'],
                url:"${ctxPath}/pages/crm/platform/include/add-order.jsp",
                data:{orderId:orderId}
            });
        },
        del: function(orderId){
            layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function(){
                ajax.remoteCall("${ctxPath}/servlet/platform-order?action=deleteOrder",{orderId:orderId},function(result){
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            reloadOrderList();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            });
        },
        detail: function(orderId){
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                shadeClose: false,
                title: '订购详情',
                offset: 'r',
                area: ['800px', '100%'],
                url:"${ctxPath}/pages/crm/platform/include/order-detail.jsp",
                data:{orderId:orderId}
            });
        }
    };

    function reloadOrderList(){
        $("#PlatformDetailForm").queryData({id:'orderTable',page:true});
    }

    orderList.init();
</script>
