package com.yunqu.work.servlet.common;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.string.StringUtils;

import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.jfinal.kit.SseEmitter;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.ai.Bigmodel;
import com.yunqu.work.ai.SiliconCloudAI;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;

@Before(AuthInterceptor.class)
@Path(value = "/llm")
public class LLMController extends BaseController {

	public static Map<String,List<String>> cacheData = new ConcurrentHashMap<String,List<String>>();
	
	public void taskAnswerStreamAI() {
		SseEmitter sseEmitter = new SseEmitter(getResponse());
		String taskId = getPara();
		String content = Db.queryStr("select task_desc from yq_task where task_id = ?",taskId);
		if(StringUtils.notBlank(content)) {
			List<String> result = SiliconCloudAI.requestSSE("Qwen/Qwen2.5-Coder-7B-Instruct","请解答的这个任务,如果遇到开发问题,请使用Java,如果遇到数据,请使用mysql", content);
			if(result!=null) {
				cacheData.put(taskId, result);
				for(String msg:result) {
					sseEmitter.sendMessage(msg);
					try {
						Thread.sleep(20);
					} catch (InterruptedException e) {
						this.error(e.getMessage(), e);
					}
				}
			}
		}
		sseEmitter.complete();
		renderNull();
	}
	
	public void taskAnswerAI() {
		String taskId = getJsonPara("taskId");
		String content = Db.queryStr("select task_desc from yq_task where task_id = ?",taskId);
		if(StringUtils.notBlank(content)) {
			String result = Bigmodel.request("请回答这个任务,如果是开发相关请使用Java或mysql,如果是其他问题正常回答就行。", content);
			if(StringUtils.notBlank(result)) {
				renderJson(EasyResult.ok(result));
				return;
			}
		}
		renderJson(EasyResult.fail());
	}
	
	public void weeklyAnswerAI() {
		String weeklyId = getJsonPara("weeklyId");
		String content = Db.queryStr("select item_1 from yq_weekly where weekly_id = ?",weeklyId);
		if(StringUtils.notBlank(content)) {
			String html = Bigmodel.request("请总结周报的风险或进度问题", content);
			if(StringUtils.notBlank(html)) {
				renderJson(EasyResult.ok(html));
				return;
			}
		}
		renderJson(EasyResult.fail());
	}
	
	public void markdownToHtml() {
		String id = getPara("id");
		List<String> list = cacheData.get(id);
		if(list==null) {
			renderText("");
			return;
		}
		StringBuffer markdown = new StringBuffer();
		for(String str:list) {
			markdown.append(str);
		}
		cacheData.remove(id);
		Parser parser = Parser.builder().build();
        HtmlRenderer renderer = HtmlRenderer.builder().build();
        Node document = parser.parse(markdown.toString());
        String result =  renderer.render(document);
        renderText(result);
	}
}
