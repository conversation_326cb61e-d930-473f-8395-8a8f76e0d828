<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowName}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的采购合同评审"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tbody class="orderInfo" data-mars-prefix="order.">
						  		<tr>
						  			<td class="required">采购订单编号</td>
						  			<td>
						  				<input type="hidden" name="orderId" value="${param.orderId}"/>
						  				<input type="hidden" name="apply.fkId" id="orderId" value="${param.orderId}"/>
						  				<input type="text" data-rules="required" readonly="readonly" value="${param.orderNo}" class="form-control input-sm" name="apply.data1"/>
						  			</td>
					  				<td class="required">供应商</td>
					  				<td>
					  					<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" name="order.SUPPLIER_NAME"/>
					  				</td>
						  		</tr>
					  			<tr>
					  				<td class="required">采购组织</td>
					  				<td>
					  					<input type="text" class="form-control input-sm" readonly="readonly" name="order.ORDER_ORG"/>
					  				</td>
					  				<td class="required">销售合同</td>
					  				<td>
					  					<input type="text" readonly="readonly" class="form-control input-sm" name="order.CONTRACT_NAME"/>
					  				</td>
					  			</tr>
					  			<tr>
					  				<td>维保合同</td>
					  				<td>
					  					<input type="text" class="form-control input-sm" readonly="readonly" name="order.WB_CONTRACT_NAME"/>
					  				</td>
					  				<td>付款工作日</td>
					  				<td>
					  					<input type="text" readonly="readonly" class="form-control input-sm" name="order.PAY_DAY"/>
					  				</td>
					  			</tr>
					  			<tr>
					  				<td>采购类型</td>
					  				<td>
					  					<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" name="order.PURCHASE_TYPE"/>
					  				</td>
						  			<td>用印类别</td>
						  			<td>
						  				<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" name="order.USE_SEAL_TYPE"/>
						  			</td>
					  			</tr>
					  			<tr>
					  				<td>备案类型</td>
					  				<td>
					  					<input type="text" class="form-control input-sm" readonly="readonly" name="order.BEIAN_TYPE"/>
					  				</td>
					  				<td>框架协议</td>
					  				<td>
					  					<input type="text" readonly="readonly" class="form-control input-sm" name="order.FRAMEWORK_XY"/>
					  				</td>
					  			</tr>
					  			<tr>
					  				<td class="required">合同类型</td>
					  				<td>
					  					<input type="text" readonly="readonly" class="form-control input-sm" name="order.PURCHASE_CONTRACT_TYPE"/>
					  				</td>
					  				<td class="required">采购合同总金额</td>
					  				<td>
					  					<input type="text" class="form-control input-sm" readonly="readonly" name="order.TOTAL_PRICE"/>
					  				</td>
					  			</tr>
					  			<tr>
					  				<td class="required">销售合同收款</td>
					  				<td>
					  					<input type="text" class="form-control input-sm" readonly="readonly" name="order.SALE_PAY_TYPE"/>
					  				</td>
					  				<td class="required">采购合同付款</td>
					  				<td>
					  					<input type="text" readonly="readonly" class="form-control input-sm" name="order.PAY_RATE"/>
					  				</td>
					  			</tr>
					  		</tbody>
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>合同方案附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				<div class="layui-col-md12">
		  				<div class="layui-col-md12 mt-10">
					     <div class="layui-table-tool">
							<div class="pull-left">采购合同明细</div>
							<div class="pull-right unedit-remove">
								<button class="btn btn-sm btn-default hidden" onclick="importData();" type="button"><i class="layui-icon layui-icon-addition"></i> 导入物料 </button>
								<button class="btn btn-sm btn-default" onclick="addTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增行</button>
								<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
								<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
								<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
							</div>
					    </div>
					  	<div class="table-responsive" style="min-height: 100px;">
								 <table class="layui-table approve-table" style="margin-top: 0px;">
								     <thead>
									    <tr>
									      <th style="width: 50px;">序号</th>
									      <th>物料编号</th>
									      <th>物料名称</th>
									      <th>数量</th>
									      <th>含税单价</th>
									      <th>含税金额</th>
									      <th>发票类型</th>
									      <th>税率</th>
									    </tr> 
									  </thead>
								  <tbody data-mars="FlowCommon.items" data-template="flow-items" data-mars-reload="false"></tbody>
								  <tbody>
								  	<c:forEach var="item" begin="1000" end="1030">
									    <tr id="item_${item}" data-hide-tr="1" style="display:none;">
									      <td>
									      	<input type="hidden" name="order_index_${item}"/>
									      	<input type="hidden" value="${item}" name="itemIndex"/>
									        <label class="checkbox checkbox-inline checkbox-success">
										      	 <input tabindex="-1" type="checkbox" name="ids" value="${item}"><span></span>
									        </label>
									      </td>
									      <td>
									      	    <input name="data1_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									     	   <input name="data2_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									      	   <input type="number" class="form-control input-sm"  data-rules="required" name="data3_${item}"/>
									      </td>
									      <td>
									     	   <input name="data4_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									     	   <input name="data5_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									     	   <input name="data6_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									     	   <input name="data7_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									    </tr>
								  	</c:forEach>
								  </tbody>	
								</table>
							</div>
					   </div>
				 
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
			
	   <script id="flow-items" type="text/x-jsrender">
  		 {{for data}}
			<tr id="item_{{:ITEM_ID}}">
			  <td>
				<input type="hidden" name="order_index_{{:ITEM_ID}}" value="{{:ITEM_INDEX}}"/>
				<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
				<span class="edit-remove">{{:ORDER_INDEX}}</span>
				<label class="checkbox checkbox-inline checkbox-success">
					 <input tabindex="-1" type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
				</label>
			  </td>
			  <td>
				  <input name="data1_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA1}}">
			  </td>
			  <td>
				  <input name="data2_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA2}}">
			  </td>
			  <td>
					<input type="number" class="form-control input-sm" data-rules="required" name="data3_{{:ITEM_ID}}" value="{{:DATA3}}"/>
			  </td>
			  <td>
				  <input name="data4_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA4}}">
			  </td>
			  <td>
				  <input name="data5_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA5}}">
			  </td>
			  <td>
				  <input name="data6_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA6}}">
			  </td>
			  <td>
				  <input name="data7_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA7}}">
			  </td>
			</tr>
 	     {{/for}}
		 {{if data.length==0}}
			<tr class="edit-remove"><td  colspan="8">暂无数据</td></tr>
		{{/if}}
   	  </script>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({title:'的采购合同评审-{{:data1}}',fileCheck:true,success:function(data){
				var param = FlowCore.params;
				var orderId = '';
				if(param.handle=='add'){
					orderId = $('#orderId').val();
					addTr();
				}else{
					orderId = FlowCore.applyInfo.fkId;
				}
				if(orderId==''){
					layer.alert('请从采购订单入口申请',function(){
						popup.closeTab();
					});
				}else{
					$('#orderId').prev().val(orderId);
					$('.orderInfo').attr('data-mars','OrderDao.applyInfo');
					$('#flowForm').render({success:function(){
						Flow.initData();
					}});
				}
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			$("[data-hide-tr]").remove();
			var index  = 1;
			$("[name^='order_index_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		FlowCore.flowStart =  function(){
			var json  = {'follow.ORDER_ID':FlowCore.applyInfo.fkId,'follow.FOLLOW_STATE':'20','follow.FOLLOW_STATE_LABEL':'合同评审','follow.FOLLOW_CONTENT':'创建合同评审'};
			ajax.remoteCall("${ctxPath}/servlet/order?action=addFollow",json,function(result) {});
		}
		
		FlowCore.flowEnd =  function(){
			var json  = {'follow.ORDER_ID':FlowCore.applyInfo.fkId,'follow.FOLLOW_STATE':'30','follow.FOLLOW_STATE_LABEL':'合同盖章','follow.FOLLOW_CONTENT':'合同评审完成'};
			ajax.remoteCall("${ctxPath}/servlet/order?action=addFollow",json,function(result) {});
		}
		
		Flow.initData = function(){
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var type = $("[name='order.USE_SEAL_TYPE']").val();
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='总裁'){
				var checkBys = {'法人章':'杨曼丽','人力资源章':'庞小翠','广州云趣公章':'庞小翠','北京云趣公章':'朱志华','财务专用章':'杨曼丽','合同专用章':'陈琛','中融合同章':'赖翠玲','工程专用章':'李千帆','中融公章':'庞小翠'};
				var types = type.split(',');
				var selectUserNames = [];
				for(var index in types){
					selectUserNames.push(checkBys[types[index]]);
				}
				selectUserNames = arrayUnique(selectUserNames);
				
				var selectUserName  = selectUserNames.join();
				params['selectUserName'] = selectUserName;
				
			}
			if(nodeCode=='合同盖章'){
				var checkName = approveInfo.nowResult.checkName;
				if(checkName.indexOf('陈琛')>-1){
					params['ccNames'] = '杨曼丽';
				}
			}
			FlowCore.approveData = params;
	  }
		
	  function addTr(){
			var obj  = $("[data-hide-tr]").first();
			if(obj.length==0){
				layer.msg('不能再新增啦',{icon:7,shade: 0.1});
				return;
			}
			obj.removeAttr("data-hide-tr");
			obj.show();
		}

		function delTr(){
			layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
				layer.close(index);
				$("input[name='ids']:checked").each(function(){
					var id = this.value;
					if(id.length>20){
						ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=delItems",{itemId:id},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg);
								$('#item_'+id).remove();
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});					
					}else{
						$('#item_'+id).remove();
					}
					
				});
			});
		}
		
		function upTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var prevTR  = $('#item_'+id).prev();
				if (prevTR.length > 0) {
					prevTR.insertAfter($('#item_'+id));
				}
			}
		}
		function downTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var nextTR  = $('#item_'+id).next();
				if (nextTR.length > 0) {
					nextTR.insertBefore($('#item_'+id));
				}
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>