<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="confirmTable"></table>
<script  type="text/html" id="confirmToolbar">
    <button class="btn btn-info btn-sm" onclick="IncomeConfirmList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-15" onclick="reloadIncomeConfirmList()" type="button">刷新</button>
    <button class="btn btn-default btn-sm ml-15" onclick="IncomeConfirmList.edit()" type="button">修改</button>
    <button class="btn btn-default btn-sm ml-15" onclick="IncomeConfirmList.del()" type="button">删除</button>
</script>
<script type="text/javascript">
    var IncomeConfirmList={
        init:function(){
            $("#ContractDetailForm").initTable({
                mars:'IncomeConfirmDao.incomeConfirmList',
                id:'confirmTable',
                page:false,
                toolbar:'#confirmToolbar',
                cellMinWidth:60,
                cols: [[
                    {
                        type: 'checkbox',
                        align:'left'
                    },{
                        field:'CONFIRM_DATE',
                        title:'日期',
                        minWidth:80
                    },{
                        field:'INCOME_STAGE_ID',
                        title:'对应阶段',
                        minWidth:280,
                        templet:function(d){
                            return getText(d.INCOME_STAGE_ID,"incomeStageName");
                        }
                    },{
                        field:'SOURCE',
                        title:'来源',
                        align:'left',
                        minWidth:80
                    },{
                        field:'AMOUNT_WITH_TAX',
                        title:'含税金额',
                        minWidth:100
                    },{
                        field:'CONFIRM_DESC',
                        title:'摘要',
                    },{
                        field:'INVOICE_SOURCE',
                        title:'发票来源',
                    },{
                        field:'CUSTOMER_NAME',
                        title:'开票客户',
                        minWidth:150,
                        templet:'<div><a href="javascript:;" onclick="IncomeConfirmList.custDetail(\'{{d.CUST_ID}}\')">{{d.CUSTOMER_NAME}}</a></div>'
                    }
                ]],
                done:function(res){
                    if(res.total!=undefined){
                        $("#incomeConfirmCount").text("("+res.total+")");
                    }
                }
            });

        },
        add:function (){
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增收入确认',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/income-confirm-edit.jsp',data:{contractId:contractId,isNew:'1'}});
        },
        edit:function(){
            var checkStatus = table.checkStatus('confirmTable');
            if(checkStatus.data.length > 1){
                layer.msg('一次只能更新一个收入确认',{icon : 7, time : 1000});
            }
            else if(checkStatus.data.length == 1){
                var confirmId = checkStatus.data[0]['CONFIRM_ID'];
                popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新收入确认',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/income-confirm-edit.jsp',data:{contractId:contractId,confirmId:confirmId,isNew:'0'}});
            }else{
                layer.msg("请选择!");
            }
        },
        del:function(){
            var checkStatus = table.checkStatus('confirmTable');
            if(checkStatus.data.length>0){
                layer.confirm("确认要删除吗?<br>删除后，对应阶段的状态将变回【未收入确认】",{icon:3,offset:'120px'},function(){
                    ajax.remoteCall("${ctxPath}/servlet/incomeConfirm?action=BatchDel",checkStatus.data,function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                layer.closeAll();
                                reloadIncomeConfirmList();
                                reloadIncomeStageList();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                });
            }else{
                layer.msg("请选择!");
            }
        },
        custDetail:function(custId){
            var width = $(window).width();
            var w = '80%';
            if(width>1500){
                w = '800px';
            }else if(width<1000){
                w = '100%';
            }else{

            }
            popup.openTab({id:'custDetail',title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:[w,'100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId}});
        }
    }

    function reloadIncomeConfirmList(){
        $("#ContractDetailForm").queryData({id:'confirmTable',page:false});
        $("[name='incomeStageName']").render();
    }
</script>
