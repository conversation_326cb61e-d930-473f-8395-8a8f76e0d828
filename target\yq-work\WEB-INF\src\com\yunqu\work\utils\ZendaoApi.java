package com.yunqu.work.utils;

import java.io.IOException;

import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.Constants;

public class ZendaoApi {
	
	private static String baseUrl = "http://172.16.68.152:6688/api.php/v1/";
	
	public static String getToken(){
		Connection connection = Jsoup.connect(baseUrl+"tokens");
		try {
			JSONObject data = new JSONObject();
			data.put("account","100133");
			data.put("password","Rocky@100133");
			connection.requestBody(data.toJSONString());
	 		Response response = connection.header("Content-Type", "application/json").timeout(10000).ignoreHttpErrors(true).ignoreContentType(true).method(Method.POST).execute();
	 		String strResult  = response.body();
	 		LogUtils.getZt().info("getToken result:"+strResult);
	 		if(response.statusCode()==200||response.statusCode()==201){
	 			JSONObject result = JSONObject.parseObject(strResult);
	 			if(result.containsKey("error")){
	 				LogUtils.getZt().error("ZendaoApi.token is error:"+data.toJSONString());
	 				return null;
	 			}else{
	 				return result.getString("token");
	 			}
	 		}
		} catch (IOException e) {
			LogUtils.getZt().error(e.getMessage(),e);
		}
		return null;
	}
	
	
	public static JSONObject addProject(String projectName,String projectCode) {
		if(!Constants.isProd()) {
			return null;
		}
		String productId = addProduct(projectName, projectCode);
		if(StringUtils.isBlank(productId)) {
			 productId = "1";
		}
		Connection connection = Jsoup.connect(baseUrl+"projects");
		try {
			JSONObject data = new JSONObject();
			data.put("name",projectName);
			data.put("code",projectCode);
			data.put("parent",1);
			JSONArray array = new JSONArray();array.add(productId);
			data.put("products",array);
			data.put("begin",EasyDate.getCurrentDateString("yyyy-MM-dd"));
			data.put("end","2059-12-31");
			connection.requestBody(data.toJSONString());
			connection.header("Token", getToken());
			LogUtils.getZt().info("addProject>"+data.toJSONString());
	 		Response response = connection.header("Content-Type", "application/json").timeout(10000).ignoreContentType(true).ignoreHttpErrors(true).method(Method.POST).execute();
	 		String strResult  = response.body();
	 		LogUtils.getZt().info("addProject result:"+strResult);
	 		if(response.statusCode()==200||response.statusCode()==201){
	 			JSONObject result = JSONObject.parseObject(strResult);
	 			if(result.containsKey("error")){
	 				LogUtils.getZt().error("ZendaoApi.addProject is error:"+data.toJSONString());
	 			}else{
	 			   return result;
	 			}
	 		}
		} catch (IOException e) {
			LogUtils.getZt().error(e.getMessage(),e);
		}
		return null;
	}
	
	public static String addProduct(String projectName,String projectCode) {
		if(!Constants.isProd()) {
			return null;
		}
		Connection connection = Jsoup.connect(baseUrl+"products");
		try {
			JSONObject data = new JSONObject();
			data.put("name",projectName);
			data.put("code",projectCode);
			data.put("acl","open");
			data.put("program",1);
			data.put("line",101);
			connection.requestBody(data.toJSONString());
			connection.header("Token", getToken());
			LogUtils.getZt().info("addProduct>"+data.toJSONString());
			Response response = connection.header("Content-Type", "application/json").timeout(10000).ignoreContentType(true).ignoreHttpErrors(true).method(Method.POST).execute();
			String strResult  = response.body();
			LogUtils.getZt().info("addProduct result:"+strResult);
			if(response.statusCode()==200||response.statusCode()==201){
				JSONObject result = JSONObject.parseObject(strResult);
				if(result.containsKey("error")){
					LogUtils.getZt().error("ZendaoApi.addProduct is error:"+data.toJSONString());
				}else{
					String id = result.getString("id");
					Db.update("update zentao.zt_product set code = ? where id = ?",projectCode,id);
//					Db.update("update yq_project set zt_product_id = ? where project_id = ?", id,projectCode);
					return id;
				}
			}
		} catch (Exception e) {
			LogUtils.getZt().error(e.getMessage(),e);
		}
		return null;
	}
	
	
	public static JSONObject addUser(String account,String password,String realname) {
		return addUser(getToken(),account, password, realname);
	}
	
	public static JSONObject addUser(String token,String account,String password,String realname) {
		if(!Constants.isProd()) {
			return null;
		}
		Connection connection = Jsoup.connect(baseUrl+"users");
		try {
			JSONObject data = new JSONObject();
			data.put("account",account);
			data.put("password",password);
			data.put("realname",realname);
			data.put("gender","f");
			connection.requestBody(data.toJSONString());
			connection.header("Token", token);
			Response response = connection.header("Content-Type", "application/json").timeout(10000).ignoreHttpErrors(true).ignoreContentType(true).method(Method.POST).execute();
			String strResult  = response.body();
			LogUtils.getZt().info("addUser result:"+strResult);
			if(response.statusCode()==200||response.statusCode()==201){
				JSONObject result = JSONObject.parseObject(strResult);
				if(result.containsKey("error")){
					LogUtils.getZt().error("ZendaoApi.addUser is error:"+data.toJSONString());
				}else{
					return result;
				}
			}
		} catch (Exception e) {
			LogUtils.getZt().error(e.getMessage(),e);
		}
		return null;
	}
	
}
