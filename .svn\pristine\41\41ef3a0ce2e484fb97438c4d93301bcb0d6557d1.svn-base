<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>商机管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
		.layui-btn + .layui-btn{margin-left: 0px;}
		.layui-table-tool .ml-10{margin-left: 10px!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="businessMgrForm">
			<input name="sjDataType" type="hidden" id="sjDataType">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		 <span class="input-group-addon">跟进阶段</span>
						 	 <select data-rules="required" name="businessStage" class="form-control input-sm">
					 	 		<option value="">请选择</option>
	                   		 	<option value="10">沟通交流</option>
		              		 	<option value="20">立项阶段</option>
		              		 	<option value="25">POC阶段</option>
		              		 	<option value="30">投标阶段</option>
		              		 	<option value="40">商务阶段</option>
		              		 	<option value="50">交付阶段</option>
		                   	  </select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		 <span class="input-group-addon">商机结果</span>
						 	 <select data-rules="required" name="businessResult" class="form-control input-sm">
					 	 		<option value="">请选择</option>
	                   		 	<option value="0">跟进中</option>
	                   		 	<option value="60" data-class="label label-success">赢单</option>
	                   		 	<option value="50" data-class="label label-warning">输单</option>
	                   		 	<option value="99"  data-class="label label-danger">取消</option>
		                   	  </select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 			<span class="input-group-addon">创建人</span>
	                    		<input name="creator" type="hidden">
					 			<input data-rules="required" onclick="singleUser(this);" class="form-control input-sm">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">所属销售</span>
					 			<input name="sales" type="hidden">
					 			<input data-rules="required" onclick="singleUser(this);" class="form-control input-sm">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">商机名称</span>
						 	<input class="form-control input-sm" name="name">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">客户名称</span>
						 	<input class="form-control input-sm" name="custName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="BusinessMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	          	     <div class="form-group">
	          	      	  <div class="input-group input-group-sm">
							 <span class="input-group-addon">开始跟进时间</span>	
							 <input type="text" name="followBeginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
							 <input type="text" name="followEndDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
				    	</div>
	          	      	  <div class="input-group input-group-sm">
							 <span class="input-group-addon">最后跟进时间</span>	
							 <input type="text" name="beginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
							 <input type="text" name="endDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
				    	</div>
				    	 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">所属部门</span>
					 			<input name="deptId" type="hidden">
					 			<input data-rules="required" onclick="singleDept(this);" class="form-control input-sm">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
							 <span class="input-group-addon">业务平台</span>
							 <input name="platformId" type="hidden">
							 <input onclick="singleBusinessPlatForm(this);" class="form-control input-sm">
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <div class="layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: 0px;margin-bottom: -8px;">
         		 	 		<ul class="layui-tab-title">
          		 	 		<li class="layui-this" data-flag="1">我创建的商机</li>
						    <li data-flag="2">全部商机</li>
						    <li data-flag="3">我部门的商机</li>
						    <li data-flag="9">商机统计</li>
         		 	 		</ul>
         		 	 	</div>
						<div class="sj-content mt-15">
						    <div class="d-table">
						    	 <table class="layui-hide" id="sjMgrTable"></table>
						    </div>
						    <div class="d-stat">
						    	<div class="layui-row layui-col-space10">
								    <div class="layui-col-xs6 layui-col-md3">
								   		<table class="layui-hide" id="stat1"></table>
								   		<table class="layui-hide" id="stat3"></table>
								    </div>
								    <div class="layui-col-xs6 layui-col-md3">
								   		<table class="layui-hide" id="stat2"></table>
								    </div>
								    <div class="layui-col-xs4 layui-col-md3">
								   		<table class="layui-hide" id="stat4"></table>
								    </div>
								    <div class="layui-col-xs4 layui-col-md3">
								   		<table class="layui-hide" id="stat5"></table>
								    </div>
								  </div>
						    </div>
					      </div>
					</div>
				</div>
		</form>
		 <script type="text/html" id="opBar">
				<button type="button" class="btn btn-sm btn-info" onclick="BusinessMgr.add()">+新建商机</button>
				<a href="javascript:;" onclick="exportExcel()" class="layui-btn layui-btn-primary layui-btn-sm ml-10">导出数据</a>
				<a class="layui-btn layui-btn-sm ml-10" lay-event="BusinessMgr.addTask">+任务</a>
				<a class="layui-btn layui-btn-sm ml-10" lay-event="BusinessMgr.task">任务查询</a>
				<a class="layui-btn layui-btn-sm layui-btn-danger ml-10" lay-event="BusinessMgr.addFollow">+商机跟进</a>
				<a class="layui-btn layui-btn-normal layui-btn-sm ml-10" lay-event="BusinessMgr.edit">编辑</a>
				<a class="layui-btn layui-btn-warm layui-btn-sm ml-10" lay-event="BusinessMgr.detail">跟进记录</a>
		 </script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var BusinessMgr={
			init:function(){
				layui.element.on('tab(stateFilter)', function(){
					var type = this.getAttribute('data-flag');
					if(type=='9'){
						$('.d-table').hide();
						$('.d-stat').show();
					}else{
						$('.d-stat').hide();
						$('.d-table').show();
						$('#sjDataType').val(type);
						$("#businessMgrForm").queryData({id:'sjMgrTable',jumpOne:true,data:{sjDataType:type}});
					}
				});
				
				$("#businessMgrForm").initTable({
					mars:'CustDao.businessPage',
					id:'sjMgrTable',
					height:'full-90',
					toolbar:'#opBar',
					limit:50,
					page:true,
					rowEvent:'rowEvent',
					rowDoubleEvent:'BusinessMgr.sjEdit',
					limits:[10,15,20,30,50,100,200,300],
					totalRow:true,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 type:'radio'
					 },{
					    field: 'BUSINESS_NAME',
						title: '商机名称',
						align:'left',
						minWidth:200
					},{
					    field: 'CUST_NAME',
						title: '所属客户',
						minWidth:180,
						totalRowText:'统计',
						align:'left'
					},{
					    field: 'PLATFORM_NAME',
						title: '业务平台',
						minWidth:100,
						align:'left'
					},{
					    field: 'CREATOR',
						title: '创建人',
						align:'center',
						width:90,
						templet:function(row){
							return getUserName(row.CREATOR);
						}
					},{
					    field: 'SALES_BY',
						title: '销售经理',
						align:'center',
						width:90,
						templet:function(row){
							return getUserName(row.SALES_BY);
						}
					},{
						field:'BUSINESS_STAGE',
						title:'跟进 阶段',
						width:80,
						templet:function(row){
							return getText(row['BUSINESS_STAGE'],'businessStage');
						}
					},{
						field:'BUSINESS_RESULT',
						title:'商机结果',
						width:80,
						templet:function(row){
							return getText(row['BUSINESS_RESULT'],'businessResult');
						}
					},{
						field:'FIRST_FOLLOW_DATE',
						width:160,
						title:'跟进时间',
						templet:'<div>{{d.FIRST_FOLLOW_DATE}}~{{d.LAST_FOLLOW_DATE}}</div>'
					},{
						field:'LAST_FOLLOW_DATE',
						width:80,
						title:'跟进天数',
						templet:function(row){
							return getDiffByDay(row['FIRST_FOLLOW_DATE'],row['LAST_FOLLOW_DATE']);
						}
					},{
						field:'FOLLOW_COUNT',
						width:80,
						title:'跟进次数',
						totalRow:true
					},{
						field:'FOLLOW_DAY',
						width:80,
						title:'消耗工时',
						totalRow:true
					},{
					    field: 'POSSIBILITY',
						title: '可能性',
						align:'left',
						width:90,
						templet:'<div>{{d.POSSIBILITY}}%</div>'
					},{
					    field: 'AMOUNT',
						title: '预计金额',
						align:'left',
						width:100,
						templet:'<div>{{d.AMOUNT}}万</div>'
					},{
					    field: 'COST_BUDGET',
						title: '预计成本',
						align:'left',
						width:100,
						templet:'<div>{{d.COST_BUDGET}}万</div>'
					},{
						field:'LAST_FOLLOW_TIME',
						width:140,
						title:'最后跟进时间'
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						width:140,
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					}
				]]});
				
				$("#businessMgrForm").initTable({
					mars:'CustDao.businessStageNum',
					id:'stat1',
					limit:50,
					page:false,
					limits:[10,15,20,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						field:'BUSINESS_STAGE',
						width:80,
						title:'商机阶段',
						templet:function(row){
							return getText(row['BUSINESS_STAGE'],'businessStage');
						}
					},{
						field:'COUNT',
						width:80,
						title:'商机数量'
					}
				  ]]
				});
				
				$("#businessMgrForm").initTable({
					mars:'CustDao.businessResultNum',
					id:'stat3',
					limit:50,
					page:false,
					limits:[10,15,20,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						field:'BUSINESS_RESULT',
						width:80,
						title:'商机结果',
						templet:function(row){
							return getText(row['BUSINESS_RESULT'],'businessResult');
						}
					},{
						field:'COUNT',
						width:80,
						title:'商机数量'
					}
				  ]]
				});
				
				$("#businessMgrForm").initTable({
					mars:'CustDao.businessSalesNum',
					id:'stat2',
					height:500,
					limit:50,
					page:true,
					groups:2,
					layout:['count','prev','next'],
					limits:[10,15,20,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						field:'SALES_BY_NAME',
						width:80,
						title:'销售'
					},{
						field:'COUNT',
						width:80,
						title:'商机数量'
					}
				  ]]
				});
				
				$("#businessMgrForm").initTable({
					mars:'CustDao.businessPreSalesNum',
					id:'stat4',
					height:500,
					limit:50,
					groups:2,
					layout:['count','prev','next'],
					page:true,
					limits:[10,15,20,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						field:'CREATE_NAME',
						width:80,
						title:'售前'
					},{
						field:'COUNT',
						width:80,
						title:'商机数量'
					}
				  ]]
				});
				
				$("#businessMgrForm").initTable({
					mars:'CustDao.businessFollowDay',
					id:'stat5',
					groups:2,
					layout:['count','prev','next'],
					height:500,
					limit:50,
					page:true,
					limits:[10,15,20,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						field:'BUSINESS_NAME',
						minWidth:180,
						title:'商机名称'
					},{
						field:'DAY',
						width:80,
						title:'跟进天数'
					}
				  ]]
				});
				
			},
			query:function(){
				$("#businessMgrForm").queryData({id:'sjMgrTable',jumpOne:true});
				$("#businessMgrForm").queryData({id:'stat1',page:false});
				$("#businessMgrForm").queryData({id:'stat2'});
				$("#businessMgrForm").queryData({id:'stat3',page:false});
				$("#businessMgrForm").queryData({id:'stat4'});
				$("#businessMgrForm").queryData({id:'stat5'});
			},
			edit:function(dataList){
				if(dataList.length==0){
					layer.msg('请选择商机');
					return;
				}
				var data = dataList[0];
				BusinessMgr.sjEdit(data);
			},
			sjEdit:function(data){
				var  businessId = data['BUSINESS_ID'];
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['80%','80%'],url:'${ctxPath}/pages/crm/shangji/sj-edit.jsp',title:'编辑',data:{businessId:businessId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['80%','80%'],url:'${ctxPath}/pages/crm/shangji/sj-edit.jsp',title:'新增',closeBtn:0});
			},
			detail:function(dataList){
				if(dataList.length==0){
					layer.msg('请选择商机');
					return;
				}
				var data = dataList[0];
				var businessId = data['BUSINESS_ID'];
				var custId = data['CUST_ID'];
				var custName = data['CUST_NAME'];
				popup.openTab({id:'bussinessContactQuery',title:'跟进记录',url:'${ctxPath}/sj/'+businessId,data:{businessId:businessId,custId:custId,custName:custName}});
			},
			addTask:function(dataList){
				if(dataList.length==0){
					layer.msg('请选择商机');
					return;
				}
				var data = dataList[0];
				var businessId = data['BUSINESS_ID'];
				var projectName = data['BUSINESS_NAME'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['780px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'安排任务',closeBtn:0,data:{businessId:businessId,projectName:projectName}});
			},
			task:function(dataList){
				if(dataList.length==0){
					layer.msg('请选择商机');
					return;
				}
				var data = dataList[0];
				var businessId = data['BUSINESS_ID'];
				popup.openTab({id:'bussinessTask',title:'商机任务',url:'${ctxPath}/pages/task/task-query.jsp',data:{businessId:businessId}});
				
			},
			addFollow:function(dataList){
				var followSource = '1';
				if(dataList.length==0){
					layer.msg('若存在跟进过的商机，请选择后再操作',{title:'温馨提醒',icon:1,offset:'20px',time:1800},function(){
						popup.layerShow({type:1,anim:0,scrollbar:false,maxmin:true,shadeClose:false,offset:'20px',area:['60%','80%'],url:'${ctxPath}/pages/crm/include/add-follow.jsp',title:'新增跟进记录',data:{followSource:followSource}});
					});
					return;
				}
				var data = dataList[0];
				var custId = data['CUST_ID'];
				var custName = data['CUST_NAME'];
				var businessId = data['BUSINESS_ID'];
				var sjName = data['BUSINESS_NAME'];
				popup.layerShow({type:1,anim:0,scrollbar:false,maxmin:true,shadeClose:false,offset:'20px',area:['60%','80%'],url:'${ctxPath}/pages/crm/include/add-follow.jsp',title:'新增跟进记录',data:{custId:custId,custName:custName,businessId:businessId,sjName:sjName,followSource:followSource}});
			}
		}
		
		function reloadTaskList(){
			
		}
		
		function reloadBusinessList(){
			BusinessMgr.query();
		}
		$(function(){
			$("#businessMgrForm").render({success:function(){
				 requreLib.setplugs('select2',function(){
						$("#businessMgrForm select").select2({theme: "bootstrap",placeholder:'请选择'});
						$(".select2-container").css("width","100%");
						BusinessMgr.init();
			 	 });
			}});
		});
		function exportExcel(){
			$(".layui-icon-export").click();
			$(".layui-table-tool-panel li").last().click();
		}
		

		function getDiffByDay(start, end){
		    var s = Date.parse(start), e = Date.parse(end);
		    //取绝对值
		    var diff = Math.abs(e - s);

		    var result = 0,hour = Math.floor(diff / (1000 * 60 * 60)),day = Math.floor(diff / (1000 * 60 * 60 * 24));

		    result = day;
		    if (day > 0){
		        //去掉天数部分，仅留小时数
		        hour -= day * 24;
		    }
		    if (hour > 4){
		        //如果大于半天(5小时)
		        result += 0.5;
		        hour = Math.floor((diff - (day * 24 + 4) * 1000 * 60 * 60) / (1000 * 60 * 60));
		    }
		    if (hour > 1){
		        result += hour * 0.1;
		    }
		    return  Math.floor(result * 100) / 100;
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>