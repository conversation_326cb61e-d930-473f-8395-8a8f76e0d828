package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

/**
 * <AUTHOR>
 */
@WebObject(name="ContractStageDao")
public class ContractStageDao extends AppDaoContext {

    /**
     * 查询stage列表
     * 可以带条件查找:contractId,stageName,revDate,planCompDate,actCompDate,ykWbCond
     * ykWbCond模糊查询 '条件bbcc'的纪录可以单传入字母b获得
     * 还可以通过sortName、sortType指定排序顺序
     */
    @WebControl(name="stageList",type = Types.LIST)
    public JSONObject stageList(){
        EasySQL sql = getEasySQL("select t1.* from yq_contract_stage t1 ");
        //查单表yq_contract_stage,无需连接
        sql.append("where 1=1");
        sql.append(param.getString("contractId")," and t1.CONTRACT_ID = ?");
        sql.append(param.getString("stageName")," and t1.STAGE_NAME like ?");
        sql.append(param.getString("rcvDate")," and t1.RCV_DATE = ?");
        sql.append(param.getString("planCompDate")," and t1.PLAN_COMP_DATE = ?");
        sql.append(param.getString("actCompDate")," and t1.ACT_COMP_DATE = ?");
        //合同维保条件用模糊查询
        if(StringUtils.notBlank(param.getString("ykWbCond"))) {
            String ykWbCond=param.getString("ykWbCond");
            sql.append( " and t1.YK_WB_COND like '%"+ykWbCond+"%' " );
        }
        //此处不确定是否需要user筛选条件
        if(!isSuperUser()&&!hasRole("CONTRACT_MGR")&&!hasRes("CONTRACT_LOOK_AUTH")) {
            sql.append("and (");
            sql.append(getUserId(),"(t1.CREATOR = ?)");
            sql.append("or");
            sql.append(getUserId(),"(t1.UPDATE_BY = ?)");
            sql.append(")");
        }
        setOrderBy(sql," order by t1.CREATE_TIME desc");
        return queryForPageList(sql.getSQL(),sql.getParams());
    }

    /**
     * 通过stageId，查询单条stage信息。
     *
     * @return 返回一个JSONObject对象，包含查询结果或错误信息。
     */
    @WebControl(name="record",type = Types.RECORD)
    public JSONObject record() {
        String stageId = param.getString("contractStage.STAGE_ID");

        if(StringUtils.notBlank(stageId)) {
            return queryForRecord("select * from yq_contract_stage where stage_id = ?", stageId);
        }else {
            return  getJsonResult(new JSONObject());
        }
    }


    private void setOrderBy(EasySQL sql,String defaultOrder) {
        String sortName =  param.getString("sortName");
        String sortType =  param.getString("sortType");
        if(StringUtils.notBlank(sortName)) {
            sql.append("order by t1.").append(sortName).append(sortType);
        }else {
            sql.append(defaultOrder);
        }
    }
}
