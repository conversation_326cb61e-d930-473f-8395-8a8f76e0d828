package com.yunqu.work.utils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.tagext.TagSupport;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.taglib.log.TaglibLogger;

/**
 * 用户控制页面的代码块的显示权限
 *
 */
public class YqRoleTag extends TagSupport{

	private static final long serialVersionUID = 1709247206558483719L;
	/**
	 * 角色ID
	 */
	private String roleId;
	
	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	/**
	 * @return EVAL_BODY_INCLUDE or EVAL_BODY_BUFFERED or SKIP_BODY
	 */
	@Override
	public int doStartTag() throws JspException {
		HttpServletRequest request = (HttpServletRequest)this.pageContext.getRequest();
		UserPrincipal up = (UserPrincipal)request.getUserPrincipal();
		//当前用户没有登录
		if(up == null) {
			if(ServerContext.isDebug()){
				TaglibLogger.getLogger().info ("EasyResTag->登录用户为空，资源不给予显示！");
			}
			
			return SKIP_BODY;
		}
		if(up.isSuperUser()){
			return EVAL_PAGE;
		}

		if(StringUtils.isBlank(this.getRoleId())) {
			if(ServerContext.isDebug()){
				TaglibLogger.getLogger().info ("EasyResTag->roleId为空，资源不给予显示！");
			}
			return SKIP_BODY;
		}
		
		String roles = this.getRoleId();
		String[] array  = roles.split(",");
		for(String roleId:array){
			if(up.isRole(roleId)){
				return EVAL_PAGE;
			}
		}
		
		return SKIP_BODY;
	}

	/**
	 * @return EVAL_PAGE or SKIP_PAGE
	 */
	@Override
	public int doEndTag() throws JspException {
	
		return EVAL_PAGE;
	}
	

}
