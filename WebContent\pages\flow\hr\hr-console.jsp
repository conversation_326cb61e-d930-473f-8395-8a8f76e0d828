<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>人力看板</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }


        .layui-this {
            font-weight: bold;
        }


        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-stat .layui-col-md2 {
            text-align: center;
            padding: 10px;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .top-3 {
            font-size: 20px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .top-4 {
            font-size: 22px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .bx-stat .layui-card {
            border-radius: 5px;
        }

        .bx-stat .layui-card-hover:hover {
            transform: scale(1.02);
            transition: transform .3s ease;
            box-shadow: 0 4px 11px #0003;
            cursor: pointer;
        }


        .layui-icon-tips {
            margin-left: 3px;
            font-size: 14px;
        }

        .layui-progress {
            margin-top: 26px;
            height: 8px
        }


        .divInline * {
            display: inline;
        }

        .divInline {
            height: 24px;
        }

        .layui-tab-item {
            padding: 0px 0px;
        }


    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm" autocomplete="off">
        <div class="layui-row"  data-mars="HrStatisticDao.hrConsoleStat">
            <div class="layui-row layui-col-space10 bx-stat">
                <div style="display: none">
                    <div class="top-1" id="LEAVE_AMOUNT_THIS_YEAR_TODAY_1">0</div>
                    <div class="top-1" id="JOIN_AMOUNT_THIS_YEAR_TODAY_1">0</div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="top-4" id="TOTAL_AMOUNT_NOW">0</div>
                            <div class="top-2">当前总员工<i class="layui-icon layui-icon-tips" lay-tips="总员工包括正式员工和实习、外包、试用期等其他员工。"></i></div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="top-1" id="TOTAL_AMOUNT_ZS">0</div>
                            <div class="top-2">当前正式员工</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="top-1" id="AVERAGE_AGE_TOP">0</div>
                            <div class="top-2">平均年龄<i class="layui-icon layui-icon-tips" lay-tips="统计全部员工、包括实习、试用期等，下同。"></i></div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="top-1" id="AVERAGE_JOIN_TOP">0</div>
                            <div class="top-2">平均司龄</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10 bx-stat">
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="divInline">
                                <div class="top-1" id="JOIN_AMOUNT_THIS_MONTH">0</div>
                            </div>
                            <div class="top-2">本月入职数<i class="layui-icon layui-icon-tips" lay-tips="总员工入职数，包括实习转正、试用期、外包等。"></i></div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="divInline">
                                <div class="top-1" id="LEAVE_AMOUNT_THIS_MONTH">0</div>
                            </div>
                            <div class="top-2">本月离职数</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="divInline">
                                <div class="top-1" id="JOIN_AMOUNT_THIS_YEAR">0</div>
                            </div>
                            <div class="top-2">年度入职数<i class="layui-icon layui-icon-tips" lay-tips="右侧增长/下降率环比的是去年第一天到去年今日的数据，非去年全年数据。"></i><span id="growthDiv1" style="float: right"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md3">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="divInline">
                                <div class="top-1" id="LEAVE_AMOUNT_THIS_YEAR">0</div>
                            </div>
                            <div class="top-2">年度离职数<i class="layui-icon layui-icon-tips" lay-tips="右侧增长/下降率环比的是去年第一天到去年今日的数据，非去年全年数据。"></i><span id="growthDiv2" style="float: right"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-header">
                        中心人数占比
                    </div>
                    <div class="layui-card-body layui-card">
                        <div style="line-height: 12px">
                            <div id="deptChart1" style="height: 360px;width: 100%" data-anim="fade"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md8">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <span style="flex-shrink: 0;">总人力资源-时间趋势</span>
                        <div class="form-group pull-right">
                            <div class="input-group input-group-sm ml-10" style="width: 360px;display: none">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-addon">查找时长</span>
                                    <input type="number" value="12" name="monthPeriod" id="monthPeriod" class="form-control input-sm">
                                    <span class="input-group-addon">开始月份</span>
                                    <input data-mars="CommonDao.yearMonthBegin" name="startMonth" id="startMonth" size="12"
                                           onClick="WdatePicker({dateFmt:'yyyy-MM'})"
                                           class="form-control input-sm">
                                </div>
                            </div>
                            <div class="btn-group-sm btn-group date1" style="width: 200px;margin-left: 20px">
                                <button class="btn btn-default btn-xs" type="button" value="halfYear">半年内</button>
                                <button class="btn btn-default btn-xs" type="button" value="oneYear">1年内</button>
                                <button class="btn btn-info btn-xs" type="button" value="nowYear">今年</button>
                                <button class="btn btn-default btn-xs" type="button" value="lastYear">去年</button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body layui-card">
                        <div id="monthChart1" style="height: 360px;width: 100%" data-anim="fade"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card-body layui-card" style="margin-bottom: 2px;">
                    <div class="layui-tab" lay-filter="deptFilter1" style="padding-left: 0px">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="deptTab11">在职人数排名</li>
                            <li lay-id="deptTab12">入职排名</li>
                            <li lay-id="deptTab13">离职排名</li>
                            <li lay-id="deptTab14">完整排名表</li>
                            <li lay-id="button" id="customButton" class="layui-tab-title-right pull-right">
                                <div class="form-group pull-right">
                                    <div class="input-group input-group-sm ml-10" style="width: 360px;display: none">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-addon">查找时长</span>
                                            <input type="number" value="12" name="monthPeriod2" id="monthPeriod2" class="form-control input-sm">
                                            <span class="input-group-addon">开始月份</span>
                                            <input data-mars="CommonDao.yearMonthBegin" name="startMonth2" id="startMonth2"
                                                   onClick="WdatePicker({dateFmt:'yyyy-MM'})"
                                                   class="form-control input-sm">
                                        </div>
                                        <input type="text" name="deptType" id="deptType" value="center">
                                    </div>
                                    <div class="btn-group-sm btn-group dept-select-staff" style="width: 100px;margin-left: 20px">
                                        <button class="btn btn-info btn-xs" value="center" type="button">中心</button>
                                        <button class="btn btn-default btn-xs" value="dept" type="button">部门</button>
                                    </div>
                                    <div class="btn-group-sm btn-group date2" style="width: 200px;">
                                        <button class="btn btn-default btn-xs" type="button" value="halfYear">半年内</button>
                                        <button class="btn btn-default btn-xs" type="button" value="oneYear">1年内</button>
                                        <button class="btn btn-info btn-xs" type="button" value="nowYear">今年</button>
                                        <button class="btn btn-default btn-xs" type="button" value="lastYear">去年</button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show" id="deptDiv2">
                                <div id="deptChart2" style="height: 360px;width: 100%;" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item" id="deptDiv3">
                                <div id="deptChart3" style="height: 360px;width: 100%;" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item" id="deptDiv4">
                                <div id="deptChart4" style="height: 360px;width: 100%;" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item" id="deptDiv5">
                                <table id="deptRankTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12 layui-card">
                <div class="layui-card-body" style="margin-bottom: 2px;">
                    <div class="layui-tab" lay-filter="deptFilter2" style="padding-left: 0px">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="deptTab21">年龄分布</li>
                            <li lay-id="deptTab22">司龄分布</li>
                            <li lay-id="deptTab23">学历分布</li>
                            <li lay-id="button" id="customButton2" class="layui-tab-title-right pull-right">
                                <div class="form-group pull-right">
                                    <div class="btn-group-sm btn-group dept-select-age" style="width: 100px;margin-left: 20px">
                                        <button class="btn btn-info btn-xs" value="center" type="button">中心</button>
                                        <button class="btn btn-default btn-xs" value="dept" type="button">部门</button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show layui-col-md12" id="deptDiv21">
                                <div class="layui-row layui-col-space20">
                                    <div class="layui-col-md7" style="padding-right: 30px">
                                        <table id="ageRankTable"></table>
                                    </div>
                                    <div class="layui-col-md5">
                                        <div id="AgeChart" style="height: 400px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item layui-col-md12" id="deptDiv22">
                                <div class="layui-row layui-col-space10">
                                    <div class="layui-col-md7" style="padding-right: 30px">
                                        <table id="joinRankTable"></table>
                                    </div>
                                    <div class="layui-col-md5">
                                        <div id="JoinChart" style="height: 400px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-tab-item layui-col-md12" id="deptDiv23">
                                <div class="layui-row layui-col-space10">
                                    <div class="layui-col-md7" style="padding-right: 30px">
                                        <table id="eduRankTable"></table>
                                    </div>
                                    <div class="layui-col-md5">
                                        <div id="EduChart" style="height: 400px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <br><br><br>
</EasyTag:override>
<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/console-echarts.js"></script>

    <script>
        var loadMap = {};

        $(function () {

            $("#searchForm").render({
                success: function () {
                    loadStatRatio();
                    loadMonthChart1();
                    loadDeptNameTable();

                    setTimeout(function () {
                        loadDeptChart3();
                        loadDeptChart4();
                    }, 300)
                    setTimeout(function () {
                        loadAgeRankTable();
                        loadJoinRankTable();
                        loadEduRankTable();
                        loadEduChart();
                        loadAgeChart();
                        loadJoinChart();
                    }, 500)

                    $('.date1 button').click(function () {
                        getYearMonth($(this)[0], 'startMonth', 'monthPeriod');
                        $(this).siblings().removeClass('btn-info');
                        $(this).siblings().addClass('btn-default');
                        $(this).addClass('btn-info');
                        loadMonthChart1();
                    });

                    $('.date2 button').click(function () {
                        getYearMonth($(this)[0], 'startMonth2', 'monthPeriod2');
                        $(this).siblings().removeClass('btn-info');
                        $(this).siblings().addClass('btn-default');
                        $(this).addClass('btn-info');
                        load3DeptChart();
                    });

                    $('.dept-select-staff button').click(function () {
                        var val = $(this)[0].value;
                        $(this).siblings().removeClass('btn-info');
                        $(this).siblings().addClass('btn-default');
                        $(this).addClass('btn-info');
                        $('#deptType').val(val)
                        load3DeptChart();
                    });

                    $('.dept-select-age button').click(function () {
                        var val = $(this)[0].value;
                        $(this).siblings().removeClass('btn-info');
                        $(this).siblings().addClass('btn-default');
                        $(this).addClass('btn-info');
                        $("#searchForm").queryData({id: 'ageRankTable', page: true, autoSort: false, data: {'deptType': val}});
                        $("#searchForm").queryData({id: 'joinRankTable', page: true, autoSort: false, data: {'deptType': val}});
                        $("#searchForm").queryData({id: 'eduRankTable', page: true, autoSort: false, data: {'deptType': val}});
                        loadAgeChart();
                        loadJoinChart();
                        loadEduChart();
                    });


                    //取消切换layui-this
                    $('#customButton').on('click', function (event) {
                        event.stopPropagation();
                    });
                    layui.element.on('tab(deptFilter1)', function (data) {
                        if (data.id == 'deptTab11') {
                            echarts.getInstanceByDom(document.getElementById("deptChart2")).resize();
                        } else if (data.id == 'deptTab12') {
                            echarts.getInstanceByDom(document.getElementById("deptChart3")).resize();
                        } else if (data.id == 'deptTab13') {
                            echarts.getInstanceByDom(document.getElementById("deptChart4")).resize();
                        } else if (data.id == 'button') {
                            return false;
                        }
                    });

                    $('#customButton2').on('click', function (event) {
                        event.stopPropagation();
                    });
                    layui.element.on('tab(deptFilter2)', function (data) {
                        if (data.id == 'deptTab21') {
                            echarts.getInstanceByDom(document.getElementById("AgeChart")).resize();
                        } else if (data.id == 'deptTab22') {
                            echarts.getInstanceByDom(document.getElementById("JoinChart")).resize();
                        } else if (data.id == 'deptTab23') {
                            echarts.getInstanceByDom(document.getElementById("EduChart")).resize();
                        } else if (data.id == 'button') {
                            return false;
                        }
                    });

                }
            });

        })

        function loadMonthChart1() {
            var monthPeriod = $('#monthPeriod').val();
            var startMonth = $('#startMonth').val();
            ajax.remoteCall("/yq-work/webcall?action=HrStatisticDao.staffAmountMonthlyStat", {"startMonth": startMonth, "monthPeriod": monthPeriod}, function (result) {
                var leaveAmountObject = result["leave_date_SUM"];
                var joinAmountObject = result["join_date_SUM"];
                var data3 = result["在职"];
                var months = Object.keys(data3);

                var leaveAmount1 = months.map(month => (leaveAmountObject[month] || "0"));
                var joinAmount1 = months.map(month => (joinAmountObject[month] || "0"));
                var staffAmount = months.map(month => (data3[month] || "0"));
                var monthName = months.map(month => (month + "月"));
                loadStaffAmountMultiChart(monthName, leaveAmount1, joinAmount1, staffAmount, "monthChart1");
            })
        }

        function loadStaffAmountMultiChart(xAxisName, leaveAmount1, joinAmount1, staffAmount, tableName) {
            var staffType = ['总员工离职', '总员工入职', '全部在职数'];
            var options2 = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                legend: {
                    data: staffType
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                barGap: '0%',
                barCategoryGap: '50%',
                xAxis: {
                    type: 'category',
                    data: xAxisName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0,
                        color: 'gray'
                    },
                    axisLine: {
                        color: 'gray'
                    },
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}',
                            color: 'gray'
                        },
                        axisLine: {
                            color: 'gray'
                        },
                        splitArea: {
                            show: true,
                            areaStyle: {
                                color: ['rgba(250,250,250,0.6)', 'rgba(200,200,200,0.1)']
                            }
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: ['#808080'],
                                width: 1,
                                opacity: 0.1
                            }
                        }
                    },
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}',
                            color: 'gray'
                        },
                        axisLine: {
                            color: 'gray'
                        },
                        splitLine: {
                            show: false
                        },
                        splitArea: {
                            show: false,
                        },
                    }
                ],
                series: [
                    {
                        name: staffType[0],
                        type: 'bar',
                        data: leaveAmount1,
                        stack: '离职',
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: function (params) {
                                return leaveAmount1[params.dataIndex] > 0 ? leaveAmount1[params.dataIndex] : '';
                            },
                        },
                        itemStyle: {
                            color: '#91cc75'
                        }
                    },
                    {
                        name: staffType[1],
                        type: 'bar',
                        data: joinAmount1,
                        stack: '入职',
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: function (params) {
                                return joinAmount1[params.dataIndex] > 0 ? joinAmount1[params.dataIndex] : '';
                            },
                        },
                        itemStyle: {
                            color: "#5470c6"
                        }
                    },
                    {
                        name: staffType[2],
                        type: 'line',
                        data: staffAmount,
                        yAxisIndex: 1,
                        label: {
                            show: true,
                            position: 'top',
                            textStyle: {
                                fontSize: 14,
                                fontWeight: 'bold'
                            }
                        },
                        itemStyle: {
                            color: '#fac858'
                        }
                    },
                ]
            };

            var myChart = echarts.init(document.getElementById(tableName));
            myChart.setOption(options2);
        }

        function load3DeptChart() {
            loadDeptNameTable();
            loadDeptChart3();
            loadDeptChart4();
        }

        function loadDeptNameTable() {
            $("#searchForm").initTable({
                    mars: 'HrStatisticDao.deptNameAmountRank',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '340px',
                    autoSort: false,
                    id: 'deptRankTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed: 'left'
                        }, {
                            field: 'DEPT_CENTER',
                            title: '中心/部门',
                            width: 140,
                            minWidth: 80,
                            fixed: 'left'
                        }, {
                            field: 'STAFF_AMOUNT_SUM',
                            title: '全部在职员工',
                            minWidth: 110,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'JOIN_AMOUNT',
                            title: '员工入职数',
                            minWidth: 120,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'JOIN_AMOUNT',
                            title: '新员工比率',
                            minWidth: 120,
                            align: 'center',
                            templet: function (d) {
                                var joinAmount = parseFloat(d.JOIN_AMOUNT);
                                if (joinAmount == undefined || joinAmount === 0) return '0';
                                var staffAmount = parseFloat(d.STAFF_AMOUNT_SUM);
                                if (staffAmount == undefined || staffAmount === 0) return '0';
                                var num = (joinAmount) * 100 / staffAmount;
                                return num.toFixed(2) + '%';
                            }
                        }, {
                            field: 'LEAVE_AMOUNT',
                            title: '员工离职数',
                            minWidth: 120,
                            sort: true,
                            align: 'center',
                        }, {
                            field: '',
                            title: '员工离职率',
                            minWidth: 120,
                            align: 'center',
                            templet: function (d) {
                                var leaveAmount = parseFloat(d.LEAVE_AMOUNT);
                                if (leaveAmount == undefined || leaveAmount === 0) return '0';
                                var staffAmount = parseFloat(d.STAFF_AMOUNT_SUM);
                                if (staffAmount == undefined || staffAmount === 0) return '0';
                                var num = (leaveAmount) * 100 / staffAmount;
                                return num.toFixed(2) + '%';
                            }
                        }
                    ]],
                    done: function (data) {
                        var pageNumber = data.pageNumber;
                        if (pageNumber !== 1) return;
                        //点过手动sort，数据排序与原降序不同，故不进行loadChart
                        if ((data.othis.data)) {
                            return;
                        }
                        var data2 = data.data;

                        var deptName = data2.map(item => item.DEPT_CENTER);
                        var leaveAmount = data2.map(item => item.LEAVE_AMOUNT);
                        var joinAmount = data2.map(item => item.JOIN_AMOUNT);
                        var staffAmount = data2.map(item => item.STAFF_AMOUNT_SUM);


                        loadStaffAmountChart(deptName, leaveAmount, joinAmount, staffAmount, "deptChart2");

                        const echartsData = data2.map(item => {
                            return {
                                name: item.DEPT_CENTER,
                                value: item.STAFF_AMOUNT_SUM
                            };
                        });
                        if (loadMap["pieChart1"]) {
                            return;
                        }
                        loadRosePieChart(echartsData, "deptChart1");
                        loadMap["pieChart1"] = true;
                    }
                },
            )
        }

        function loadStaffAmountChart(deptName, leaveAmount1, joinAmount1, staffAmount,tableName) {
            //员工流动率
            var ldRatio = staffAmount.map((staff, index) => {
                var ldAmount = Number(leaveAmount1[index]) + Number(joinAmount1[index]);
                if (ldAmount === 0 || ldAmount == "0" || staff === 0 || staff == "0") {
                    return 0;
                }
                return (ldAmount / staff * 100).toFixed(2);
            });
            var sortedRatios = [...ldRatio].sort((a, b) => b - a);
            var ratio3 = parseFloat(sortedRatios[2]);
            ratio3 = ratio3 < 10 ? 10 : ratio3;

            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function (params) {
                        var result = params[0].name + '<br/>';
                        params.forEach(function (item) {
                            if (item.seriesType === 'bar') {
                                result += item.marker + item.seriesName + ': ' + (item.value) + '<br/>';
                            } else {
                                result += item.marker + item.seriesName + ': ' + (item.value) + '% <br/>';
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['总在职员工', '员工流动率']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '1%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '50%',
                xAxis: {
                    type: 'category',
                    data: deptName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        },
                        splitLine:{
                            lineStyle: {
                                color: '#f1f0f0'
                            }
                        }
                    },
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}%'
                        },
                        splitLine:{
                            show:false
                        }
                    }
                ],
                series: [
                    {
                        name: '总在职员工',
                        type: 'bar',
                        data: staffAmount,
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: function (params) {
                                return staffAmount[params.dataIndex] > 0 ? staffAmount[params.dataIndex] : '';
                            },
                            textStyle: {
                                fontWeight: 'bold',
                            },
                        },
                        itemStyle: {
                            color: '#91cc75',
                        }
                    }, {
                        name: '员工流动率',
                        type: 'line',
                        yAxisIndex: 1,
                        data: ldRatio.map(Number),
                        smooth: false,
                        itemStyle: {
                            color: '#fac858'
                        },
                        symbolSize: 6,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                return ldRatio[params.dataIndex] >= ratio3 ? ldRatio[params.dataIndex] + '%' : '';
                            },
                            textStyle: {
                                color: '#b82843',
                                fontWeight: 'bold',
                            },
                        }
                    }
                ],
            };

            var chart = echarts.init(document.getElementById(tableName));
            chart.setOption(option);
        }

        function loadDeptChart3() {
            var monthPeriod = $('#monthPeriod2').val();
            var startMonth = $('#startMonth2').val();
            var deptType = $('#deptType').val();
            ajax.remoteCall("/yq-work/webcall?action=HrStatisticDao.deptNameAmountRank", {
                "pageSize": 20,
                "startMonth2": startMonth,
                "monthPeriod2": monthPeriod,
                "deptType": deptType,
                "sortName": "JOIN_AMOUNT",
                "sortType": "desc"
            }, function (result) {
                var data2 = result.data;
                var deptName = data2.map(item => item.DEPT_CENTER);
                var joinAmount = data2.map(item => item.JOIN_AMOUNT);
                var staffAmount = data2.map(item => item.STAFF_AMOUNT_SUM);
                loadDeptBarChart(deptName, ['员工入职数', '新员工比率'], joinAmount, staffAmount, 'deptChart3')

            })
        }

        function loadDeptChart4() {
            var monthPeriod = $('#monthPeriod2').val();
            var startMonth = $('#startMonth2').val();
            var deptType = $('#deptType').val();
            ajax.remoteCall("/yq-work/webcall?action=HrStatisticDao.deptNameAmountRank", {
                "pageSize": 20,
                "startMonth2": startMonth,
                "monthPeriod2": monthPeriod,
                "deptType": deptType,
                "sortName": "LEAVE_AMOUNT",
                "sortType": "desc"
            }, function (result) {
                var data2 = result.data;
                var deptName = data2.map(item => item.DEPT_CENTER);
                //员工离职数
                var leaveAmount = data2.map(item => item.LEAVE_AMOUNT);
                var staffAmount = data2.map(item => item.STAFF_AMOUNT_SUM);
                loadDeptBarChart(deptName, ['员工离职数', '员工离职率'], leaveAmount, staffAmount, 'deptChart4')
            })
        }

        function loadDeptBarChart(deptName, legend, ldAmount1, staffAmount1, chartName) {
            //员工入职/离职率
            var ldRatio = ldAmount1.map((ldAmount, index) => {
                var staff = staffAmount1[index];
                if (ldAmount === 0 || ldAmount == "0" || staff === 0 || staff == "0") {
                    return 0;
                }
                return (ldAmount / staff * 100).toFixed(2);
            });
            var sortedRatios = [...ldRatio].sort((a, b) => b - a);
            var ratio3 = parseFloat(sortedRatios[2]);
            ratio3 = ratio3 < 10 ? 10 : ratio3;
            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function (params) {
                        var result = params[0].name + '<br/>';
                        params.forEach(function (item) {
                            if (item.seriesType === 'bar') {
                                result += item.marker + item.seriesName + ': ' + (item.value) + '<br/>';
                            } else {
                                result += item.marker + item.seriesName + ': ' + (item.value) + '% <br/>';
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '1%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '50%',
                xAxis: {
                    type: 'category',
                    data: deptName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#f1f0f0'
                            }
                        }
                    },
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}%'
                        },
                        splitLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: ldAmount1,
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: function (params) {
                                return ldAmount1[params.dataIndex] > 0 ? ldAmount1[params.dataIndex] : '';
                            },
                        },
                        itemStyle: {
                            color: '#91cc75'
                        }
                    },
                    {
                        name: legend[1],
                        type: 'line',
                        yAxisIndex: 1,
                        data: ldRatio.map(Number),
                        smooth: false,
                        itemStyle: {
                            color: '#fac858'
                        },
                        symbolSize: 6,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                return ldRatio[params.dataIndex] >= ratio3 ? ldRatio[params.dataIndex] + '%' : '';
                            },
                            textStyle: {
                                color: '#b82843',
                                fontWeight: 'bold',
                            },
                        }
                    }
                ],
            };

            var chart = echarts.init(document.getElementById(chartName));
            chart.setOption(option);
        }


        function loadEduChart() {
            ajax.remoteCall("/yq-work/webcall?action=HrStatisticDao.eduRatioStat", {}, function (result) {
                var data2 = result.data;
                var data = data2.map(function (item) {
                    return {
                        name: item.EDU,
                        value: parseInt(item.AMOUNT, 10)
                    };
                });
                loadPyramidChart(data, '全司学历分布','EduChart', );
            })
        }

        function loadAgeChart() {
            ajax.remoteCall("/yq-work/webcall?action=HrStatisticDao.ageRatioStat", {}, function (result) {
                var data2 = result.data;
                var ageCategory = Object.keys(data2);
                var ageAmount = ageCategory.map(key => (data2[key] || "0"));
                loadHorizontalBarChart(ageCategory, ageAmount, 'AgeChart', '全司年龄分布');
            })
        }

        function loadAgeRankTable() {
            $("#searchForm").initTable({
                    mars: 'HrStatisticDao.ageRankByDept',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '380px',
                    autoSort: false,
                    id: 'ageRankTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed: 'left'
                        }, {
                            field: 'DEPT_CENTER',
                            title: '部门',
                            minWidth: 80,
                            width: 140,
                            fixed: 'left',
                            style: 'text-decoration: underline;cursor:pointer',
                            event: 'deptAgeRatio',
                        }, {
                            field: 'AVERAGE_AGE',
                            title: '平均年龄',
                            minWidth: 100,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'STAFF_AMOUNT',
                            title: '总员工人数',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'AGE_25',
                            title: '25岁以下',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'AGE_25_30',
                            title: '25-30岁',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'AGE_31_40',
                            title: '31-40岁',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'AGE_41_50',
                            title: '41-50岁',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'AGE_50',
                            title: '50岁以上',
                            width: 80,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {
                    }
                },
            )
        }

        function loadJoinRankTable() {
            $("#searchForm").initTable({
                    mars: 'HrStatisticDao.joinYearRankByDept',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '380px',
                    autoSort: false,
                    id: 'joinRankTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed: 'left'
                        }, {
                            field: 'DEPT_CENTER',
                            title: '部门',
                            minWidth: 80,
                            width: 140,
                            fixed: 'left',
                            style: 'text-decoration: underline;cursor:pointer',
                            event: 'deptJoinRatio',
                        }, {
                            field: 'AVERAGE_JOIN',
                            title: '平均司龄',
                            minWidth: 100,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'STAFF_AMOUNT',
                            title: '员工人数',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'YEAR_1',
                            title: '1年以下',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'YEAR_1_3',
                            title: '1-3年',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'YEAR_3_5',
                            title: '3-5年',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'YEAR_5_7',
                            title: '5-7年',
                            width: 80,
                            align: 'center',
                        }, {
                            field: 'YEAR_7',
                            title: '7年以上',
                            width: 80,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {
                    }
                },
            )
        }

        function loadEduRankTable(){
            ajax.remoteCall("/yq-work/webcall?action=HrStatisticDao.allEduNames", {}, function (result) {
                var data2 = result.data;
                var eduCategory = data2.map(function (item){
                    return item.EDU;
                });
                eduCategory.push("其它")
                var cols= [
                    {
                        type: 'numbers',
                        title: '序号',
                        align: 'left',
                        fixed: 'left'
                    }, {
                        field: 'DEPT_CENTER',
                        title: '部门',
                        minWidth: 80,
                        width: 140,
                        fixed: 'left',
                        style: 'text-decoration: underline;cursor:pointer',
                        event: 'deptEduRatio',
                    }, {
                        field: 'STAFF_AMOUNT',
                        title: '员工人数',
                        width: 80,
                        align: 'center',
                    }, {
                        field: 'BK_RATIO',
                        title: '本科以上比例(%)',
                        width: 120,
                        align: 'center',
                    },
                ]

                cols = cols.concat(eduCategory.map(function (eduName) {
                        return {
                            field: "EDU_"+eduName,
                            title: eduName,
                            width: 80,
                            align: 'center',
                        }
                }));

                $("#searchForm").initTable({
                    mars: 'HrStatisticDao.eduAmountRankByDept',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '380px',
                    autoSort: false,
                    id: 'eduRankTable',
                    cols: [cols],
                    done: function (data) {
                    }
                })
            })}

        function deptAgeRatio(data) {
            var ageCategory = Object.keys(data).filter(key => key.startsWith("AGE_"));
            var ageCategoryName = ["25岁以下", "25-30岁", "31-40岁", "41-50岁", "50岁以上"];
            var deptName = data["DEPT_CENTER"];
            var ageAmount = ageCategory.map(key => (data[key] || "0"));
            var title = deptName + ' 年龄分布'
            loadHorizontalBarChart(ageCategoryName, ageAmount, 'AgeChart', title);
        }

        function deptJoinRatio(data) {
            var yearCategory = Object.keys(data).filter(key => key.startsWith("YEAR_"));
            var yearCategoryName = ["1年以下", "1-3年", "3-5年", "5-7年", "7年以上"];
            var deptName = data["DEPT_CENTER"];
            var yearAmount = yearCategory.map(key => (data[key] || "0"));
            loadHorizontalBarChart(yearCategoryName, yearAmount, 'JoinChart', deptName + ' 司龄分布');
        }

        function deptEduRatio(data) {
            var eduCategory = Object.keys(data).filter(key => key.startsWith("EDU_"));
            var data2 = eduCategory.map(function(key) {
                var name = key.substring(4);
                var value = parseInt(data[key], 10);
                if (value > 0) {
                    return { name: name, value: value };
                }
                return undefined;
            }).filter(function(item) {
                return item !== undefined;
            });
            var deptName = data["DEPT_CENTER"];

            loadPyramidChart(data2, deptName + ' 学历分布', 'EduChart');
        }

        function loadJoinChart() {
            ajax.remoteCall("/yq-work/webcall?action=HrStatisticDao.joinYearStat", {}, function (result) {
                var data2 = result.data;
                var ageCategory = Object.keys(data2);
                var ageAmount = ageCategory.map(key => (data2[key] || "0"));
                var totalAmount = ageAmount.reduce((sum, value) => sum + Number(value), 0);
                loadHorizontalBarChart(ageCategory, ageAmount, 'JoinChart', '全司司龄分布');
            })
        }


        var getYearMonth = function (obj, startMonthName, monthPeriodName) {
            var val = obj.value;
            var bdate = new Date();
            var monthNums = 12;
            if (val == 'halfYear') {
                bdate.setMonth(bdate.getMonth() - 5);
                monthNums = 6;
            } else if (val == 'oneYear') {
                bdate.setMonth(bdate.getMonth() - 11);
                monthNums = 12;
            } else if (val == 'nowYear') {
                bdate.setMonth(0);
                monthNums = 12;
            } else if (val == 'lastYear') {
                bdate.setFullYear(bdate.getFullYear() - 1);
                bdate.setMonth(0);
                bdate.setDate(1);
                monthNums = 12;
            }
            if (val) {
                var bdateVal = DateUtils.dateFormat(bdate, 'yyyy-MM');
                $('[name="' + startMonthName + '"]').val(bdateVal);
                $('[name="' + monthPeriodName + '"]').val(monthNums);
            } else {
                $('[name="' + startMonthName + '"]').val('');
                $('[name="' + monthPeriodName + '"]').val('');
            }
        }

        function loadStatRatio() {
            calGrowthRatio("LEAVE_AMOUNT_THIS_YEAR", "LEAVE_AMOUNT_LAST_YEAR_TODAY", "growthDiv1", '环比去年')
            calGrowthRatio("JOIN_AMOUNT_THIS_YEAR", "JOIN_AMOUNT_LAST_YEAR_TODAY", "growthDiv2", '环比去年')
        }

        function calGrowthRatio(nameThis, nameLast, nameDiv, word) {
            var sumThis = parseFloat($('#' + nameThis).text());
            var sumLast = parseFloat($('#' + nameLast).text());
            var growthRatio = 0;
            if (sumLast !== 0) {
                growthRatio = ((sumThis - sumLast) / sumLast * 100).toFixed(2);
            }
            if (growthRatio > 0) {
                $('#' + nameDiv).html(
                    word + '<span class="layui-edge layui-edge-top" ></span>' + growthRatio + '%'
                );
            } else if (growthRatio < 0) {
                $('#' + nameDiv).html(
                    word + '<span class="layui-edge layui-edge-bottom"></span>' + Math.abs(growthRatio) + '%'
                );
            }
        }


    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>