<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>项目文档</title>
	<style>
		.icon {width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:2px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="projectDocs" class="form-inline">
		<input name="fkId" type="hidden" value="${param.projectId}">
		<input name="projectId" type="hidden" value="${param.projectId}">
		<input type="hidden" value="project,yqproject" name="source">
		<div class="ibox">
			  <div class="ibox-content" style="height: calc(100vh - 80px);overflow-y: scroll;">
				<div id="fileList">
					<div class="mb-5 pull-left">
					  	<button class="btn btn-sm btn-info ml-10" type="button" onclick="$('#projectDetailLocalfile').click()"><i class="fa fa-upload" aria-hidden="true"></i> 上传</button>
					  	<a class="btn btn-sm btn-success ml-10" href="/yq-work/aiEditor/create?projectId=${param.projectId}&projectName=${param.projectName}" target="blank"> <i class="fa fa-plus" aria-hidden="true"></i> AI文档 </a>
					  	<button class="btn btn-sm btn-default ml-10" type="button" onclick="addLink()"> <i class="fa fa-link" aria-hidden="true"></i> 挂载链接 </button>
					  	<button class="btn btn-sm btn-default ml-10" type="button" onclick="noticeTeam()"> <i class="fa fa-bell" aria-hidden="true"></i> 提醒查看 </button>
						<a class="btn btn-xs btn-link ml-10" href="https://doc.weixin.qq.com/home/<USER>" target="blank">企微文档</a>
						<a class="btn btn-xs btn-link ml-10" href="https://www.kdocs.cn/latest" target="blank">WPS云文档</a>
						<a class="btn btn-xs btn-link ml-10" href="https://docs.feishu.cn" target="blank">飞书云文档</a>
						<button class="btn btn-xs btn-link ml-10" type="button" onclick="downloadLogLayer('${param.projectId}','');">下载日志</button>
					</div>
				 	<table class="layui-table folderTable" style="margin:15px 5px;">
					  <thead>
					    <tr>
					      <th style="width:60px;">序号</th>
					      <th>名称</th>
					      <th>大小</th>
					      <th>创建时间</th>
					      <th>创建者</th>
					      <th>操作</th>
					    </tr> 
					  </thead>
					  <tbody data-template="project-link-template" data-mars="FolderDao.projectLink">
				
					  </tbody>
					  <tbody data-template="ai-doc-template" data-mars="FolderDao.projectAIDoc">
				
					  </tbody>
					  <tbody class="fileTbody" data-template="project-detail-template-files" data-mars="FileDao.fileListDetail">
				
					  </tbody>
					</table>
				 </div>
			 </div>
		</div>
	</form>
	<form  id="projectDetailFileForm" enctype="multipart/form-data"  method="post">
  		<input style="display: none;" name="file" type="file" id="projectDetailLocalfile" multiple="multiple" onchange="projectUploadFile()"/>
  	</form>
	<script id="project-detail-template-files" type="text/x-jsrender">
			{{if data.length==0}}
				<tr>
					<td colspan="6">
						<button class="btn btn-sm btn-link mt-10" type="button" onclick="$('#projectDetailLocalfile').click()">+上传</button>
					</td>
				</tr>
			{{/if}}
			{{for data}}
				<tr>
				  <td>{{:#getIndex()+1}}</td>
				  <td>{{call:FILE_TYPE fn='getFileIcon'}} <a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}&nbsp;</span></a></td>
				  <td nowrap>{{:FILE_SIZE}}</td>
				  <td nowrap>{{:CREATE_TIME}}</td>
				  <td nowrap>
					  {{:CREATE_NAME}}
				  </td>
				  <td nowrap>
						<div class="btn-group btn-group-sm">
							<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">操作<span class="caret"></span></button>
							<ul class="dropdown-menu dropdown-menu-right">
								   {{if FILE_TYPE=='.xml'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source={{:SOURCE}}&action=display" target="_blank">编辑器打开</a>&nbsp;</li>
									{{else FILE_TYPE=='.zip'||FILE_TYPE=='.war'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source={{:SOURCE}}&action=zip.view" target="_blank">预览</a>&nbsp;</li>
									{{else FILE_TYPE=='.pdf'||FILE_TYPE=='.txt'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source={{:SOURCE}}&action=get" target="_blank">本地打开</a>&nbsp;</li>
									{{else FILE_TYPE=='.xlsx'||FILE_TYPE=='.xls'||FILE_TYPE=='.docx'||FILE_TYPE=='.doc'||FILE_TYPE=='.ppt'||FILE_TYPE=='.pptx'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source={{:SOURCE}}&action=office" target="_blank">预览</a>&nbsp;</li>
									{{else FILE_TYPE=='.log'||FILE_TYPE=='.txt'||FILE_TYPE=='.out'}}
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source={{:SOURCE}}&action=tail" target="_blank">tail</a>&nbsp;</li>
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source={{:SOURCE}}&action=less" target="_blank">less</a>&nbsp;</li>
										<li><a href="/yq-work/files/view/{{:FILE_ID}}?source={{:SOURCE}}&action=grep" target="_blank">grep</a>&nbsp;</li>
								   {{/if}}
								<li><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" target="_blank">下载</a></li>
								<li data-user-id="{{:CREATOR}}"><a href="javascript:;" onclick="delProjectFile('{{:FILE_ID}}','{{:SOURCE}}',this);">删除</a></li>
							</ul>
						</div>
				  </td>
				</tr>
			{{/for}}
		</script>
	    <script id="project-link-template" type="text/x-jsrender">
			{{for data}}
				<tr>
				  <td>{{:#getIndex()+1}}</td>
				  <td><i class="fa fa-link" aria-hidden="true"></i> <a title="{{:REMARK}}" href="{{:LINK_URL}}" target="_blank">{{:LINK_NAME}}</a></td>
				  <td></td>
				  <td>{{:CREATE_TIME}}</td>
				  <td>
					  {{call:CREATOR fn='getUserName'}}
				  </td>
				  <td>
						<div class="btn-group btn-group-sm">
							<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">操作<span class="caret"></span></button>
							<ul class="dropdown-menu dropdown-menu-right">
								<li><a href="{{:LINK_URL}}" target="_blank">访问</a></li>
								<li data-user-id="{{:CREATOR}}"><a href="javascript:;" onclick="editLink('{{:LINK_ID}}')">修改</a></li>
								<li data-user-id="{{:CREATOR}}"><a href="javascript:;" onclick="delLink('{{:LINK_ID}}',this)">删除</a></li>
							</ul>
						</div>
				  </td>
				</tr>
			{{/for}}
		</script>
	    <script id="ai-doc-template" type="text/x-jsrender">
			{{for data}}
				<tr>
				  <td>{{:#getIndex()+1}}</td>
				  <td><svg class="icon" aria-hidden="true"><use xlink:href="#icon-AIwendang"></use></svg> <a href="/yq-work/aiEditor/l/{{:DOC_ID}}" target="_blank">{{:TITLE}}</a></td>
				  <td>{{if UPDATE_COUNT>0}}版本({{:UPDATE_COUNT}}){{/if}}</td>
				  <td>{{:UPDATE_TIME}}</td>
				  <td>
					  {{:UPDATE_USER_NAME}}
				  </td>
				  <td>
						<a href="/yq-work/aiEditor/l/{{:DOC_ID}}" target="_blank">访问</a>
				  </td>
				</tr>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

  <script type="text/javascript">
		
	var projectId = '${param.projectId}';
	
	var projectUploadFile = function(){
		var fkId = projectId;
		easyUploadFile({callback:'reloadLink',fkId:fkId,folderId:'0',source:'project',formId:'projectDetailFileForm',fileId:'projectDetailLocalfile'});
	}
	 
	 var addLink = function(){
		var fkId = projectId;
		popup.layerShow({type:1,area:['550px','400px'],url:'${ctxPath}/pages/project/include/link-edit.jsp',title:'新增链接',data:{pId:'0',source:'addLink',projectId:fkId}});
	 }
	 
	 var noticeTeam = function(){
		 layer.msg('todo',{icon:7,offset:'20px',time:1200});
	 }
	 
	 var delProjectFile = function(id,source,obj){
		if(source=='contract'){
			layer.msg('合同文件不允许删除',{icon:7,offset:'20px',time:1200});
			return;
		}
		if(top.isAdmin=='1'||isSuperUser){
			delFile(id,obj);
		}else{
			layer.msg("您无权操作!");
		}
	 }
	 
	 var editLink = function(linkId){
		var fkId = projectId;
		popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/project/include/link-edit.jsp',title:'新增链接',data:{linkId:linkId,source:'addLink',projectId:fkId}});
	 }
	 
	 var delLink = function(linkId,el){
		layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
		ajax.remoteCall("${ctxPath}/servlet/folder?action=deleteLink",{'link.LINK_ID':linkId},function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:1200},function(){
					reloadLink();
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
		
	   });
    }
	 
	 function reloadFile(){
		 reloadLink();
	 }
	 
	 var reloadLink= function(){
		location.reload();
	 }

	 $(function(){
		 $('#projectDocs').render();
	 });
 	
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>