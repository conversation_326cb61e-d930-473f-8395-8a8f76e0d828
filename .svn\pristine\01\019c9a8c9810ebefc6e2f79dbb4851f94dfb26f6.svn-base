<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 140px;">标题</td>
					  			<td style="width: 38%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 140px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">所属项目</td>
					  			<td>
					  				<input name="apply.data1" type="hidden">
									<input type="text" data-rules="required" onclick="singleProject(this)" readonly="readonly" data-rules="required" name="apply.data2" class="form-control input-sm"/>
					  			</td>
					  			<td class="required">需求预算</td>
					  			<td>
					  				<input type="text" data-rules="required" placeholder="若无填写0" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  		</tr>
				  			<tr>
					  			<td class="required">开发评审</td>
					  			<td>
					  				<input type="hidden" name="apply.data4"/>
					  				<input type="text" data-rules="required" onclick="singleUser(this);" data-ref-update="prev" readonly="readonly" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td class="required">销售经理</td>
					  			<td>
					  				<input type="hidden" name="apply.data6"/>
					  				<input type="text" data-rules="required" onclick="singleUser(this);" data-ref-update="prev" readonly="readonly" class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请原因</td>
					  			<td colspan="3">
									<textarea style="height: 100px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>文件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12">
		  				<div class="layui-col-md12 mt-10">
					     <div class="layui-table-tool">
							<div class="pull-left">需求列表</div>
							<div class="pull-right unedit-remove">
								<button class="btn btn-sm btn-default" onclick="addTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增行</button>
								<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
								<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
								<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
							</div>
					    </div>
					  	<div class="table-responsive" style="min-height: 100px;">
								 <table class="layui-table approve-table" style="margin-top: 0px;">
								   <thead>
								    <tr>
								      <th style="width: 50px;">序号</th>
								      <th>需求描述</th>
								      <th style="width: 120px;">客户期望时间点</th>
								      <th style="width: 90px;">是否可行</th>
								      <th style="width: 100px;">负责人</th>
								      <th style="width: 100px;">工作量/天</th>
								      <th style="width: 120px;">预计开发开始时间</th>
								      <th style="width: 120px;">预计开发结束时间</th>
								      <th style="width: 120px;">反馈备注</th>
								    </tr> 
								  </thead>
								  <tbody data-mars="FlowCommon.items" id="items" data-template="flow-items"></tbody>
								  <tbody>
								  	<c:forEach var="item" begin="1000" end="1020">
									    <tr id="item_${item}" data-hide-tr="1" style="display:none;">
									      <td style="width: 50px!important;">
									      	<input type="hidden" name="order_index_${item}"/>
									      	<input type="hidden" value="${item}" name="itemIndex"/>
									        <label class="checkbox checkbox-inline checkbox-success">
										      	 <input tabindex="-1" type="checkbox" name="ids" value="${item}"><span></span>
									        </label>
									      </td>
									      <td>
									      	    <textarea name="data1_${item}" data-rules="required" class="form-control input-sm"></textarea>
									      </td>
									      <td>
									     	   <input name="data2_${item}" data-laydate="{type:'date'}" readonly class="form-control input-sm">
									      </td>
									      <td></td>
									      <td>
									      		<input type="hidden" name="data3_${item}">
									      	   <input type="text" class="form-control input-sm" onclick="singleUser(this)" onblur="hasSelectUser(this)" placeholder="开发填写" name="data4_${item}"/>
									      </td>
									      <td>
									     	   <input name="data5_${item}" placeholder="开发填写" class="form-control input-sm">
									      </td>
									      <td>
									      	   <input type="text" data-laydate="{type:'date'}" class="form-control input-sm" placeholder="开发填写" name="data6_${item}"/>
									      </td>
									      <td>
									      	   <input type="text" data-laydate="{type:'date'}" class="form-control input-sm" placeholder="开发填写" name="data7_${item}"/>
									      </td>
									      <td>
									     	   <input name="data9_${item}" placeholder="开发填写" class="form-control input-sm">
									      </td>
									    </tr>
								  	</c:forEach>
								  </tbody>	
								</table>
							</div>
					   </div>
				 
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
			
			<script id="flow-items" type="text/x-jsrender">
  		 {{for data}}
			<tr id="item_{{:ITEM_ID}}">
			  <td>
				<input type="hidden" name="order_index_{{:ITEM_ID}}" value="{{:ITEM_INDEX}}"/>
				<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
				<span class="edit-remove">{{:ORDER_INDEX}}</span>
				<label class="checkbox checkbox-inline checkbox-success">
					 <input tabindex="-1" type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
				</label>
			  </td>
			  <td>
					<textarea style="height: 40px;resize: none;" maxlength="255" class="form-control input-sm" name="data1_{{:ITEM_ID}}">{{:DATA1}}</textarea>
			  </td>
			  <td>
					<input type="text" class="form-control input-sm" data-laydate="{type:'date'}" readonly name="data2_{{:ITEM_ID}}" value="{{:DATA2}}"/>
			  </td>
			  <td>
					<select class="form-control input-sm" data-rules="required" data-value="{{:DATA8}}" name="data8_{{:ITEM_ID}}">
						<option {{if DATA8=='可行'}} selected="selected"{{/if}} value="可行">可行</option>
						<option {{if DATA8=='不可行'}} selected="selected"{{/if}} value="不可行">不可行</option>
					</select>
			  </td>
			  <td>
				  	<input name="data3_{{:ITEM_ID}}" type="hidden" value="{{:DATA3}}">
					<input type="text" class="form-control input-sm" onfocus="singleUser(this)" data-flag="0" onblur="hasSelectUser(this)" data-id="{{:ITEM_ID}}" name="data4_{{:ITEM_ID}}" value="{{:DATA4}}"/>
			  </td>
			  <td>
				  <input type="number" placeholder="人/天"  name="data5_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA5}}">
			  </td>
			  <td>
					<input type="text" class="form-control input-sm" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',startDate:'%y-%M-%d',doubleCalendar:true,alwaysUseStartDate:true})" name="data6_{{:ITEM_ID}}" value="{{:DATA6}}"/>
			  </td>
			  <td>
					<input type="text" class="form-control input-sm" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',startDate:'%y-%M-%d',doubleCalendar:true,alwaysUseStartDate:true})" name="data7_{{:ITEM_ID}}" value="{{:DATA7}}"/>
			  </td>
			  <td>
					<textarea class="form-control input-sm" style="height:30px;" name="data9_{{:ITEM_ID}}" onclick="textareaEditLayer(this);">{{:DATA9}}</textarea>
			  </td>
			</tr>
 	     {{/for}}
   	  </script>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(result){
				if(FlowCore.businessId){
					$("[name^=data8]").each(function(){
						 $(this).val($(this).data('value'));
					});
				}else{
					addTr();
				}
				var param = FlowCore.params;
				if(param.handle=='edit'||param.handle=='add'){
					$("[name^=data3],[name^=data4],[name^=data5],[name^=data6],[name^=data7],[name^=data8],[name^=data9]").remove();
				}
				Flow.initData();
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			$("[data-hide-tr]").remove();
			var index  = 1;
			$("[name^='order_index_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			
			
			var data5 = $("[name='apply.data5']").val();
			FlowCore.approveData = {selectUserName:data5};
			FlowCore.ajaxSubmitForm(state);
			
			var pjName = $("[name='apply.data2']").val();
			sendWxMsg('PROJECT','<font color=\"warning\">userName提交项目需求变更</font>\n> '+pjName);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		FlowCore.flowEnd =  function(){
			var applyInfo = FlowCore.applyInfo;
			var formData = form.getJSONObject("#items");
			var itemIndex = formData['itemIndex'];
			var array = [];
			for(var index in itemIndex){
				var itemId = itemIndex[index];
				var kxy = formData['data8_'+itemId];
				if(kxy=='可行'){
					var taskDesc = formData['data1_'+itemId];
					var assignUserId = formData['data3_'+itemId];
					var taskName = cutText(taskDesc,70,'');
					taskDesc = taskDesc.replaceAll('\n','<br>');
					var planWorkDay = formData['data5_'+itemId];
					var planStartedAt = formData['data6_'+itemId]+" 09:00";
					var deadlineAt = formData['data7_'+itemId]+" 18:00";
					var projectId = applyInfo['data1'];
					var projName = applyInfo['data2'];
					var planWorkHour = planWorkDay * 8;
					array.push({applyId:applyInfo.applyId,applyBy:applyInfo.applyBy,projName:projName,planWorkDay:planWorkDay,planWorkHour:planWorkHour,taskId:itemId,taskName:taskName,taskDesc:taskDesc,assignUserId:assignUserId,assignUserId:assignUserId,planStartedAt:planStartedAt,deadlineAt:deadlineAt,projectId:projectId});
				
				}
			}
			if(array.length>0){
				ajax.remoteCall("${ctxPath}/servlet/task?action=addFlowTask",array,function(result) { 
					if(result.state == 1){
						
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});	
			}
		}
		
		Flow.initData = function(){
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var type = applyInfo.data1;;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			var nextNode = approveInfo.nextNode.nodeCode;
			if(nodeCode=='开发确认'&&nextNode!='销售确认'){
				$('.split-symbol').after('<button class="btn btn-success btn-sm mr-5 save-field" data-save="fail" onclick="Flow.saveItem();" type="button"><i class="fa fa-save"></i> 保存修改 </button>');
				Flow.showEdit();
			}
			if(nodeCode=='开发主管'||nextNode=='销售确认'){
				var data7 = $("[name='apply.data7']").val();
				params['selectUserName'] = data7;
				$('.split-symbol').after('<button class="btn btn-success btn-sm mr-5 save-field" data-save="ok" onclick="Flow.saveItem();" type="button"><i class="fa fa-save"></i> 保存修改 </button>');
				Flow.showEdit();
			}
			if(nodeCode=='销售确认'){
				
			}
			FlowCore.approveData = params;
		}
		
		Flow.showEdit = function(){
			$('#items [name^=data3],#items [name^=data4],#items [name^=data5],#items [name^=data6],#items [name^=data7],#items [name^=data8],#items [name^=data9]').each(function(){
				var el = $(this);
				var tagName = el[0].tagName.toLowerCase();
				if(tagName=='span'){
					el.remove();
				}else{
					el.show();
				}
			});
		}
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='开发确认'||nodeCode=='开发主管'){
				var flag = true;
				$("#items [name^='data4']").each(function(){
					var val = $(this).val();
					var itemId = $(this).data('id');
					var data8 = $("[name='data8_"+itemId+"']").val();
					if(data8=='不可行'){
						$("#item_"+itemId).find('input').removeAttr('data-rules');
					}else{
						var dflag = $(this).attr('data-flag');
						if(val==''&&dflag=='0'){
							layer.tips('请选择负责人',this,{tips:[2,'#5fb878'],time:3000,tipsMore:true});
							flag = false;
						}
						if(dflag=='2'){
							layer.tips('请先点击保存',this,{tips:[2,'#5fb878'],time:3000,tipsMore:true});
							flag = false;
						}
					}
				});
				if(!form.validate("#items")){
					flag = false;
				}
				return flag;
			}
			return true;
	     }
		
		Flow.saveItem = function(){
			var itemFields = form.getJSONObject("#items");
			var data = $.extend(FlowCore.params,{itemFields:itemFields,updateFieldStr:'data3,data4,data5,data6,data7,data8,data9'});
			ajax.remoteCall("/yq-work/servlet/flow/fun?action=updateField",data,function(result) { 
				if(result.state == 1){
					$("#items [name^='data4']").attr('data-flag','1');
					$('.save-field').attr('data-save','ok');
					layer.msg(result.msg,{icon:1,time:800},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
	   function addTr(){
			var obj  = $("[data-hide-tr]").first();
			if(obj.length==0){
				layer.msg('不能再新增啦',{icon:7,shade: 0.1});
				return;
			}
			obj.removeAttr("data-hide-tr");
			obj.show();
		}

		function delTr(){
			layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
				layer.close(index);
				$("input[name='ids']:checked").each(function(){
					var id = this.value;
					if(id.length>20){
						ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=delItems",{itemId:id},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg);
								$('#item_'+id).remove();
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});					
					}else{
						$('#item_'+id).remove();
					}
					
				});
			});
		}
			
		function upTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var prevTR  = $('#item_'+id).prev();
				if (prevTR.length > 0) {
					prevTR.insertAfter($('#item_'+id));
				}
			}
		}
		
		function downTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var nextTR  = $('#item_'+id).next();
				if (nextTR.length > 0) {
					nextTR.insertBefore($('#item_'+id));
				}
			}
		}
			
		function selctCallBack(){
			
		}
		
		function hasSelectUser(el){
			$(el).attr('data-flag','2');
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>