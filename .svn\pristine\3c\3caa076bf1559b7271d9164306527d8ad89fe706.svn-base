package com.yunqu.work.servlet;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.poi.ss.usermodel.IndexedColors;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.WeeklyModel;
import com.yunqu.work.service.CommonService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.WeeklyNoticeService;
import com.yunqu.work.service.WeeklyStatService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.WeekUtils;
/**
 * 周报管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/weekly/*")
public class WeeklyServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		JSONObject params  = getJSONObject();
		WeeklyModel model=getModel(WeeklyModel.class, "weekly");
		int draft = params.getIntValue("draft");
		//model.setPrimaryValues(RandomKit.uniqueStr());
		model.addCreateTime();
		model.set("update_date",EasyCalendar.newInstance().getDateInt());
		model.set("update_time",EasyDate.getCurrentDateString());
		model.setCreator(getUserId());
		model.set("create_name", getUserName());
		model.set("dept_id", getDeptId());
		model.set("dept_name", getDeptName());
		String ccIds = model.getString("CC_IDS");
		try {
			String receiver=model.getString("RECEIVER");
			if(StringUtils.isBlank(receiver)||draft==0){
				model.set("status","0");//未发送
			}else{
				model.set("SEND_TIME",EasyDate.getCurrentDateString());//已发送
				model.set("status","1");//已发送
				
				MessageModel messageModel=new MessageModel();
				messageModel.setReceiver(receiver);
				messageModel.setData(model);
				messageModel.setContents(model.getString("TITLE"));
				messageModel.setTypeName("weekly");
				messageModel.setFkId(model.getWeeklyId());
				messageModel.setSender(getUserId());
				messageModel.setSendName(getUserName());
				messageModel.setFileList(CommonService.getService().getFileList(model.getWeeklyId()));
				if(StringUtils.notBlank(ccIds)) {
					if(ccIds.indexOf(getUserId())==-1) {
						ccIds = ccIds+","+getUserId();
						messageModel.setCc(ccIds.split(","));
					}else {
						messageModel.setCc(ccIds.split(","));
					}
				}else {
					messageModel.setCc(new String[] {getUserId()});
				}
				messageModel.setMsgState(1);
				messageModel.setMsgType(2001);
				messageModel.setSenderType(0);
				
				messageModel.setData(model);
				
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendWeeklyEmail(messageModel);
					messageModel.setDesc(model.getString("ITEM_1"));
					WxMsgService.getService().sendNewWeeklyMsg(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			model.save();
			setItem(params,model.getWeeklyId());
			
			this.getQuery().executeUpdate("update yq_weekly t1,yq_weekly_date t2 set t1.week_begin= t2.week_begin,t1.week_end = t2.week_end where t1.year = t2.year and t1.week_no = t2.week_no and t1.weekly_id = ? ", model.getWeeklyId());
	
			this.getQuery().executeUpdate("update yq_weekly t1 set t1.file_count = (select count(1) from yq_files t2 where t2.fk_id = t1.weekly_id) where t1.weekly_id  = ?",model.getWeeklyId());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void setItem(JSONObject params,String weeklyId) throws SQLException {
		Object itemIndexObj = params.get("itemIndex");
		if(itemIndexObj!=null) {
			JSONArray itemIndexs = null;
			if(itemIndexObj instanceof String) {
				itemIndexs = new JSONArray();
				itemIndexs.add(itemIndexObj.toString());
			}else {
				itemIndexs = params.getJSONArray("itemIndex");
			}
			for(int i= 0;i<itemIndexs.size();i++) {
				EasyRecord itemModel = new EasyRecord("yq_weekly_project","item_id");
				String itemId = itemIndexs.getString(i);
				String projectId = params.getString("projectId_"+itemId);
				itemModel.setPrimaryValues(itemId);
				itemModel.set("weekly_id", weeklyId);
				itemModel.set("project_id",projectId);
				itemModel.set("project_name",params.getString("projectName_"+itemId));
				itemModel.set("work_day",params.getString("workDay_"+itemId));
				itemModel.set("work_content",params.getString("workContent_"+itemId).trim());
				itemModel.set("plan_work_content",params.getString("planWorkContent_"+itemId).trim());
				itemModel.set("progress_rate",params.getString("progressRate_"+itemId));
				itemModel.set("plan_finish_date",params.getString("planFinishDate_"+itemId));
				if(StringUtils.isBlank(projectId)) {
					continue;
				}
				if(itemId.length()>20) {
					itemModel.set("item_id", itemId);
					this.getQuery().update(itemModel);
				}else {
					itemModel.setPrimaryValues(RandomKit.uniqueStr());
					this.getQuery().save(itemModel);
				}
			}
		}
		
	}
	
	
	public EasyResult actionForGetNearWeekly() {
		JSONObject params = getJSONObject();
		String weeklyId = params.getString("weeklyId");
		String type = params.getString("type");
		try {
			JSONObject row = this.getQuery().queryForRow("select creator,year,week_no from yq_weekly where weekly_id = ?", new Object[] {weeklyId}, new JSONMapperImpl());
			
			EasySQL sql = new EasySQL();
			sql.append(row.getString("CREATOR"),"select weekly_id from yq_weekly where creator = ?");
			
			if("next".equals(type)) {
				sql.append(row.getString("YEAR"),"and `year` >= ?");
				sql.append(row.getString("WEEK_NO"),"and week_no > ?");
				sql.append(" order by `year`,week_no limit 1");
			}else {
				sql.append(row.getString("YEAR"),"and `year` <= ?");
				sql.append(row.getString("WEEK_NO"),"and week_no < ?");
				sql.append(" order by `year` desc,week_no desc limit 1");
			}
			String newId = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			if(StringUtils.isBlank(newId)) {
				newId = "";
			}
			return EasyResult.ok(newId);
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForNoticeWriteWeekly(){
		JSONObject jsonObject = getJSONObject();
		String userId=jsonObject.getString("userId");
		MessageModel messageModel=new MessageModel();
		messageModel.setReceiver(userId);
		messageModel.setSender(getUserId());
		messageModel.setTitle("请及时填报"+jsonObject.getString("year")+"年第"+jsonObject.getString("weekNo")+"的周报");
		EmailService.getService().sendNoticeWriteWeeklyEmail(messageModel);
		return EasyResult.ok();
	}
	public EasyResult actionForDoGrade(){
		JSONObject jsonObject = getJSONObject();
		String weeklyId=jsonObject.getString("weeklyId");
		String grade=jsonObject.getString("grade");
		JSONObject log=new JSONObject();
		try {
			log.put("userId", getUserId());
			log.put("grade", grade);
			log.put("name", jsonObject.getString("name"));
			log.put("time",EasyDate.getCurrentDateString());
			this.getQuery().executeUpdate("update yq_weekly set grade = ?,grade_log = ? where weekly_id = ?", grade,log.toJSONString(),weeklyId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(log);
	}
	public EasyResult actionForUpdate(){
		JSONObject params  = getJSONObject();
		int draft = params.getIntValue("draft");
		WeeklyModel model=getModel(WeeklyModel.class, "weekly");
		model.set("update_date",EasyCalendar.newInstance().getDateInt());
		model.set("update_time",EasyDate.getCurrentDateString());
		String ccIds = model.getString("CC_IDS");
		try {
			String receiver=model.getString("RECEIVER");
			int status=model.getIntValue("STATUS");
			if(status<2){
				if(StringUtils.isBlank(receiver)||draft==0){
					model.set("status","0");//未发送
				}else{
					model.set("SEND_TIME",EasyDate.getCurrentDateString());//已发送
					model.set("status","1");//已发送
					
					MessageModel messageModel=new MessageModel();
					messageModel.setReceiver(receiver);
					messageModel.setContents(model.getString("TITLE"));
					messageModel.setFkId(model.getWeeklyId());
					messageModel.setSender(getUserId());
					messageModel.setSendName(getUserName());
					messageModel.setMsgState(1);
					messageModel.setData(model);
					messageModel.setMsgType(2001);
					messageModel.setTypeName("weekly");
					messageModel.setSenderType(0);
					if(StringUtils.notBlank(ccIds)) {
						if(ccIds.indexOf(getUserId())==-1) {
							ccIds = ccIds+","+getUserId();
							messageModel.setCc(ccIds.split(","));
						}else {
							messageModel.setCc(ccIds.split(","));
						}
					}else {
						messageModel.setCc(new String[] {getUserId()});
					}
					
					messageModel.setData(model);
					
					try {
						MessageService.getService().sendMsg(messageModel);
						EmailService.getService().sendWeeklyEmail(messageModel);
						messageModel.setDesc(model.getString("ITEM_1"));
						WxMsgService.getService().sendNewWeeklyMsg(messageModel);
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			model.update();
			setItem(params,model.getWeeklyId());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelItem(){
		String itemId = getJsonPara("itemId");
		try {
			this.getQuery().executeUpdate("delete from yq_weekly_project where item_id = ?", itemId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		WeeklyModel model=getModel(WeeklyModel.class, "weekly");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	public EasyResult actionForNoticeWeekly(){
		String weeklyNo=getPara("weeklyNo", ""+WeekUtils.getWeekNo());
		WeeklyNoticeService.getService().run(Integer.valueOf(weeklyNo));
		return EasyResult.ok("","提醒成功!");
	}
	public EasyResult actionForUpdateWeeklyStat(){
		WeeklyStatService.getService().run();
		return EasyResult.ok();
	}
	
	
	private boolean hasWeekyAuth() {
		return this.getUserPrincipal().isRole("WEEKLY_MANGER");
	}
	
	private String getCenterName(String deptName) {
		if(StringUtils.notBlank(deptName)) {
			if(deptName.indexOf("创新")>-1||deptName.indexOf("分部")>-1||deptName.indexOf("开发")>-1||deptName.indexOf("研究")>-1) {
				return "开发中心";
			}
			if(deptName.indexOf("工程")>-1) {
				return "工程中心";
			}
			if(deptName.indexOf("解决")>-1) {
				return "售前";
			}
		}
		return "其他";
	}
	
	public void actionForExportProjectWeeklyWh(){
		String _data = getPara("data");
		JSONObject param = JSONObject.parseObject(_data);
		List<String> headers=new ArrayList<String>();
		
		EasySQL sql = new EasySQL("select t1.*,t3.project_name,t3.project_no,t2.work_content,t2.plan_work_content,t2.work_day from yq_weekly t1,yq_weekly_project t2,yq_project t3 where t1.weekly_id = t2.weekly_id and t3.project_id = t2.project_id");
		String projectId = param.getString("projectId");
		sql.append(projectId, "and t2.project_id = ?");
		sql.appendLike(param.getString("projectName"), "and t2.project_name like ?");
		sql.appendLike(param.getString("userName"),"and t1.create_name like ?");
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and t1.week_begin >= ?");
			sql.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		boolean hasAuth = hasWeekyAuth();
		if(!hasAuth&&StringUtils.isBlank(projectId)){
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,t1.cc_ids)");
			sql.append("or");
			sql.append(getUserId(),"t1.creator = ?");
			sql.append("or");
			sql.append(getUserId(),"t1.receiver = ?");
			sql.append(")");
		}
		if(!hasAuth&&StringUtils.notBlank(projectId)) {
			sql.append("9999999999999", "and t2.project_id <> ?");
		}
		
		sql.append(0,"and t1.status <> ?");
		sql.append("order by t1.send_time desc");
		
		File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
		/**创建头部*/
		headers.add("项目编号");
		headers.add("项目名称");
		headers.add("填写部门");
		headers.add("填写人");
		headers.add("人员类别");
		headers.add("所属年");
		headers.add("所属周");
		headers.add("消耗工时/天");
		headers.add("填写时间");
		headers.add("填写内容");
		
		List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
		int x=0;
		for(String header:headers){
			ExcelHeaderStyle style=new ExcelHeaderStyle();
			style.setData(header);
			if(x==1){
				style.setWidth(12000);
			}else {
				style.setWidth(4500);
			}
			style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
			styles.add(style);
			x++;
		}
		
		List<List<String>> excelData=new ArrayList<List<String>>();
		EasyQuery query=this.getQuery();
		query.setMaxRow(50000);
		
		List<JSONObject> data = null;
		try {
			data = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		} catch (SQLException e) {
			this.error(null, e);
			renderHtml(e.getMessage());
			return;
		}
		
		if(data!=null && data.size()>0){
			for (int i = 0; i < data.size(); i++) {
				JSONObject map = data.get(i);
				List<String> list=new ArrayList<String>();
				list.add(map.getString("PROJECT_NO"));
				list.add(map.getString("PROJECT_NAME"));
				list.add(map.getString("DEPT_NAME"));
				list.add(map.getString("CREATE_NAME"));
				list.add(getCenterName(map.getString("DEPT_NAME")));
				list.add(map.getString("YEAR"));
				list.add(map.getString("WEEK_NO"));
				list.add(map.getString("WORK_DAY"));
				list.add(map.getString("SEND_TIME"));
				list.add(map.getString("WORK_CONTENT"));
				
				excelData.add(list);
			}
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
		} catch (IOException e) {
			e.printStackTrace();
		}
		String fileName="项目工时填报列表.xlsx";
		renderFile(file,fileName,true);
	}
	
	
	public EasyResult actionForInit() {
		for(int year = 2018;year<2030;year++) {
			for(int weekNo=1;weekNo<=52;weekNo++) {
				String a=WeekUtils.getStartDayOfWeekNo(year,weekNo);
				String b=WeekUtils.getEndDayOfWeekNo(year,weekNo);
				EasyRecord record = new EasyRecord("yq_weekly_date");
				record.set("year", year);
				record.set("week_no", weekNo);
				record.set("week_begin", a);
				record.set("week_end", b);
				try {
					this.getQuery().save(record);
				} catch (SQLException e) {
					
				}
			}
		}
		return EasyResult.ok();
	}
}





