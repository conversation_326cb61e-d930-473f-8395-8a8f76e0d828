package com.yunqu.work.servlet.wx;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.StrKit;
import com.jfinal.weixin.iot.msg.InEquDataMsg;
import com.jfinal.weixin.iot.msg.InEqubindEvent;
import com.jfinal.weixin.sdk.jfinal.MsgController;
import com.jfinal.weixin.sdk.msg.in.InImageMsg;
import com.jfinal.weixin.sdk.msg.in.InLinkMsg;
import com.jfinal.weixin.sdk.msg.in.InLocationMsg;
import com.jfinal.weixin.sdk.msg.in.InNotDefinedMsg;
import com.jfinal.weixin.sdk.msg.in.InShortVideoMsg;
import com.jfinal.weixin.sdk.msg.in.InTextMsg;
import com.jfinal.weixin.sdk.msg.in.InVideoMsg;
import com.jfinal.weixin.sdk.msg.in.InVoiceMsg;
import com.jfinal.weixin.sdk.msg.in.card.InCardPassCheckEvent;
import com.jfinal.weixin.sdk.msg.in.card.InCardPayOrderEvent;
import com.jfinal.weixin.sdk.msg.in.card.InCardSkuRemindEvent;
import com.jfinal.weixin.sdk.msg.in.card.InMerChantOrderEvent;
import com.jfinal.weixin.sdk.msg.in.card.InUpdateMemberCardEvent;
import com.jfinal.weixin.sdk.msg.in.card.InUserCardEvent;
import com.jfinal.weixin.sdk.msg.in.card.InUserConsumeCardEvent;
import com.jfinal.weixin.sdk.msg.in.card.InUserGetCardEvent;
import com.jfinal.weixin.sdk.msg.in.card.InUserGiftingCardEvent;
import com.jfinal.weixin.sdk.msg.in.card.InUserPayFromCardEvent;
import com.jfinal.weixin.sdk.msg.in.event.InCustomEvent;
import com.jfinal.weixin.sdk.msg.in.event.InFollowEvent;
import com.jfinal.weixin.sdk.msg.in.event.InLocationEvent;
import com.jfinal.weixin.sdk.msg.in.event.InMassEvent;
import com.jfinal.weixin.sdk.msg.in.event.InMenuEvent;
import com.jfinal.weixin.sdk.msg.in.event.InNotDefinedEvent;
import com.jfinal.weixin.sdk.msg.in.event.InPoiCheckNotifyEvent;
import com.jfinal.weixin.sdk.msg.in.event.InQrCodeEvent;
import com.jfinal.weixin.sdk.msg.in.event.InShakearoundUserShakeEvent;
import com.jfinal.weixin.sdk.msg.in.event.InTemplateMsgEvent;
import com.jfinal.weixin.sdk.msg.in.event.InVerifyFailEvent;
import com.jfinal.weixin.sdk.msg.in.event.InVerifySuccessEvent;
import com.jfinal.weixin.sdk.msg.in.event.InWifiEvent;
import com.jfinal.weixin.sdk.msg.in.speech_recognition.InSpeechRecognitionResults;
import com.jfinal.weixin.sdk.msg.out.News;
import com.jfinal.weixin.sdk.msg.out.OutImageMsg;
import com.jfinal.weixin.sdk.msg.out.OutMusicMsg;
import com.jfinal.weixin.sdk.msg.out.OutNewsMsg;
import com.jfinal.weixin.sdk.msg.out.OutTextMsg;
import com.jfinal.weixin.sdk.msg.out.OutVoiceMsg;

public class WeixinMsgController extends MsgController {

	private Logger getLogger() {
		return LogEngine.getLogger("weixin-req");
	}
	
	@Override
	protected void processInCardPassCheckEvent(InCardPassCheckEvent arg0) {
		getLogger().info("processInCardPassCheckEvent");
	}

	@Override
	protected void processInCardPayOrderEvent(InCardPayOrderEvent arg0) {

	}

	@Override
	protected void processInCardSkuRemindEvent(InCardSkuRemindEvent arg0) {

	}

	@Override
	protected void processInCustomEvent(InCustomEvent arg0) {

	}

	@Override
	protected void processInEquDataMsg(InEquDataMsg arg0) {

	}

	@Override
	protected void processInEqubindEvent(InEqubindEvent arg0) {

	}



	@Override
	protected void processInMassEvent(InMassEvent arg0) {

	}


	@Override
	protected void processInMerChantOrderEvent(InMerChantOrderEvent arg0) {

	}

	@Override
	protected void processInPoiCheckNotifyEvent(InPoiCheckNotifyEvent arg0) {

	}


	@Override
	protected void processInShakearoundUserShakeEvent(InShakearoundUserShakeEvent arg0) {

	}

	/**
	 * 处理接收到的小视频消息
	 * @param inShortVideoMsg 处理接收到的小视频消息
	 */
	@Override
	protected void processInShortVideoMsg(InShortVideoMsg arg0) {

	}

	/**
	 * 处理接收到的模板消息是否送达成功通知事件
	 * @param inTemplateMsgEvent 处理接收到的模板消息是否送达成功通知事件
	 */
	@Override
	protected void processInTemplateMsgEvent(InTemplateMsgEvent arg) {
		getLogger().info(arg.getToUserName());
		getLogger().info(arg.getMsgId());
	}
	
	@Override
	protected void processInTextMsg(InTextMsg inTextMsg) {
		getLogger().info("open_id:"+inTextMsg.getFromUserName());
		String msgContent = inTextMsg.getContent().trim();
		getLogger().info("msgContent:"+msgContent);
		
		 if ("news".equalsIgnoreCase(msgContent)) {
			OutNewsMsg outMsg = new OutNewsMsg(inTextMsg);
			outMsg.addNews("图文消息title", "图文消息description", "图文消息片 url", "图文消息 url");
			render(outMsg);
		}
		// 音乐消息测试
		else if ("music".equalsIgnoreCase(msgContent)) {
			OutMusicMsg outMsg = new OutMusicMsg(inTextMsg);
			outMsg.setTitle("Day By Day");
			outMsg.setDescription("建议在 WIFI 环境下流畅欣赏此音乐");
			outMsg.setMusicUrl("http://www.jfinal.com/DayByDay-T-ara.mp3");
			outMsg.setHqMusicUrl("http://www.jfinal.com/DayByDay-T-ara.mp3");
			outMsg.setFuncFlag(true);
			render(outMsg);
		}
		else if ("美女".equalsIgnoreCase(msgContent)) {
			OutNewsMsg outMsg = new OutNewsMsg(inTextMsg);
			outMsg.addNews("秀色可餐", "JFinal Weixin 极速开发就是这么爽，有木有 ^_^", "http://mmbiz.qpic.cn/mmbiz/zz3Q6WSrzq2GJLC60ECD7rE7n1cvKWRNFvOyib4KGdic3N5APUWf4ia3LLPxJrtyIYRx93aPNkDtib3ADvdaBXmZJg/0", "http://mp.weixin.qq.com/s?__biz=MjM5ODAwOTU3Mg==&mid=200987822&idx=1&sn=7eb2918275fb0fa7b520768854fb7b80#rd");
			render(outMsg);
		}
		// 其它文本消息直接返回原值 + 帮助提示
		else {
			//http://www.itpk.cn/  
			//http://i.itpk.cn/api.php?question=%E6%9D%A5%E4%B8%AA%E7%AC%91%E8%AF%9D&api_key=91ca6b5e2df278965c41d5ffba1e1f76&api_secret=zn8a39ukhbho
			String result=HttpKit.get("http://www.tuling123.com/openapi/api?key=a024e0df63354a338420835c251ea270&info="+inTextMsg.getContent());
			JSONObject jsonObject=JSONObject.parseObject(result);
    		String code=jsonObject.getString("code");
    		if("100000".equals(code)){
    			OutTextMsg outMsg = new OutTextMsg(inTextMsg);
    			outMsg.setContent(jsonObject.getString("text"));
    			render(outMsg);
    			return;
    		}else if("200000".equals(code)){
    			OutNewsMsg newsMsg=new OutNewsMsg(inTextMsg);
    			newsMsg.addNews(jsonObject.getString("text"), jsonObject.getString("text"), jsonObject.getString("url"), jsonObject.getString("url"));
    			render(newsMsg);
    			return;
    		}else if("302000".equals(code)){
    			JSONArray jsonArray=jsonObject.getJSONArray("list");
    			OutNewsMsg outMsg = new OutNewsMsg(inTextMsg);
    			for(int i=0;i<jsonArray.size();i++){
    				JSONObject object=jsonArray.getJSONObject(i);
    				News news=new News();
    				news.setTitle(object.getString("article"));
    				news.setUrl(object.getString("detailurl"));
    				news.setDescription(object.getString("article"));
    				news.setPicUrl(object.getString("icon"));
    				if(StrKit.notBlank(news.getTitle())){
    					outMsg.addNews(news);
    				}
    			}
    			render(outMsg);
    			return;
    		}else{
    			OutTextMsg outMsg = new OutTextMsg(inTextMsg);
    			outMsg.setContent("\t文本消息已成功接收，内容为： " + inTextMsg.getContent() + "\n\n");
    			render(outMsg);
    		}
        }
	}
	
	@Override
	protected void processInImageMsg(InImageMsg inImageMsg) {
		OutImageMsg outMsg = new OutImageMsg(inImageMsg);
		// 将刚发过来的图片再发回去
		outMsg.setMediaId(inImageMsg.getMediaId());
		render(outMsg);
	}
	
	@Override
	protected void processInVoiceMsg(InVoiceMsg inVoiceMsg) {
		OutVoiceMsg outMsg = new OutVoiceMsg(inVoiceMsg);
		// 将刚发过来的语音再发回去
		outMsg.setMediaId(inVoiceMsg.getMediaId());
		render(outMsg);
	}
	
	@Override
	protected void processInVideoMsg(InVideoMsg inVideoMsg) {
		/* 腾讯 api 有 bug，无法回复视频消息，暂时回复文本消息代码测试
		OutVideoMsg outMsg = new OutVideoMsg(inVideoMsg);
		outMsg.setTitle("OutVideoMsg 发送");
		outMsg.setDescription("刚刚发来的视频再发回去");
		// 将刚发过来的视频再发回去，经测试证明是腾讯官方的 api 有 bug，待 api bug 却除后再试
		outMsg.setMediaId(inVideoMsg.getMediaId());
		render(outMsg);
		*/
		OutTextMsg outMsg = new OutTextMsg(inVideoMsg);
		outMsg.setContent("\t视频消息已成功接收，该视频的 mediaId 为: " + inVideoMsg.getMediaId());
		render(outMsg);
	}
	
	@Override
	protected void processInLocationMsg(InLocationMsg inLocationMsg) {
		OutTextMsg outMsg = new OutTextMsg(inLocationMsg);
		outMsg.setContent("已收到地理位置消息:" +
							"\nlocation_X = " + inLocationMsg.getLocation_X() +
							"\nlocation_Y = " + inLocationMsg.getLocation_Y() + 
							"\nscale = " + inLocationMsg.getScale() +
							"\nlabel = " + inLocationMsg.getLabel());
		render(outMsg);
	}
	
	@Override
	protected void processInLinkMsg(InLinkMsg inLinkMsg) {
		OutNewsMsg outMsg = new OutNewsMsg(inLinkMsg);
		outMsg.addNews("链接消息已成功接收", "链接使用图文消息的方式发回给你，还可以使用文本方式发回。点击图文消息可跳转到链接地址页面，是不是很好玩 :)" , "http://mmbiz.qpic.cn/mmbiz/zz3Q6WSrzq1ibBkhSA1BibMuMxLuHIvUfiaGsK7CC4kIzeh178IYSHbYQ5eg9tVxgEcbegAu22Qhwgl5IhZFWWXUw/0", inLinkMsg.getUrl());
		render(outMsg);
	}
	
	@Override
	protected void processInFollowEvent(InFollowEvent inFollowEvent) {
		OutTextMsg outMsg = new OutTextMsg(inFollowEvent);
		outMsg.setContent("感谢关注云趣官方工作协作平台~ ");
		// 如果为取消关注事件，将无法接收到传回的信息
		render(outMsg);
	}
	
	@Override
	protected void processInQrCodeEvent(InQrCodeEvent inQrCodeEvent) {
		OutTextMsg outMsg = new OutTextMsg(inQrCodeEvent);
		outMsg.setContent("processInQrCodeEvent() 方法测试成功");
		render(outMsg);
	}
	
	@Override
	protected void processInLocationEvent(InLocationEvent inLocationEvent) {
		OutTextMsg outMsg = new OutTextMsg(inLocationEvent);
		outMsg.setContent("processInLocationEvent() 方法测试成功");
		render(outMsg);
	}
	
	@Override
	protected void processInMenuEvent(InMenuEvent inMenuEvent) {
		OutTextMsg outMsg = new OutTextMsg(inMenuEvent);
		outMsg.setContent("processInMenuEvent() 方法测试成功");
		render(outMsg);
	}
	
	@Override
	protected void processInSpeechRecognitionResults(InSpeechRecognitionResults inSpeechRecognitionResults) {
		OutTextMsg outMsg = new OutTextMsg(inSpeechRecognitionResults);
		outMsg.setContent("processInSpeechRecognitionResults() 方法测试成功");
		render(outMsg);
	}
	@Override
	protected void processInUpdateMemberCardEvent(InUpdateMemberCardEvent arg0) {

	}

	@Override
	protected void processInUserCardEvent(InUserCardEvent arg0) {

	}

	@Override
	protected void processInUserConsumeCardEvent(InUserConsumeCardEvent arg0) {

	}

	@Override
	protected void processInUserGetCardEvent(InUserGetCardEvent arg0) {

	}

	@Override
	protected void processInUserGiftingCardEvent(InUserGiftingCardEvent arg0) {

	}

	@Override
	protected void processInUserPayFromCardEvent(InUserPayFromCardEvent arg0) {

	}

	@Override
	protected void processInVerifyFailEvent(InVerifyFailEvent arg0) {

	}

	@Override
	protected void processInVerifySuccessEvent(InVerifySuccessEvent arg0) {

	}

	@Override
	protected void processInWifiEvent(InWifiEvent arg0) {

	}

	@Override
	protected void processIsNotDefinedEvent(InNotDefinedEvent arg0) {

	}

	@Override
	protected void processIsNotDefinedMsg(InNotDefinedMsg arg0) {

	}

}
