package com.yunqu.work.servlet.work;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.CommentModel;
import com.yunqu.work.model.ProjectModel;
import com.yunqu.work.service.CommentService;
@WebServlet("/servlet/project/*")
public class ProjectServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForUpdateProjectInfo(){
		String sql="select PROJECT_ID,PROJECT_NAME from yq_project";
		try {
			List<EasyRow> list = this.getQuery().queryForList(sql);
			for(EasyRow row:list){
				String projectId = row.getColumnValue("PROJECT_ID");
				String projectName = row.getColumnValue("PROJECT_NAME");
				 if(projectName.length()>13){
					 String[] array = projectName.split(" ");
					 if(array[0].length()==13&&array[0].startsWith("20")){
						 projectName=projectName.substring(14);
					 }
				 }
				 this.getQuery().executeUpdate("update yq_project set PROJECT_NAME = ? where PROJECT_ID = ?",projectName, projectId);
				 this.getQuery().executeUpdate("update yq_project_contract set CONTRACT_NAME = ? where CONTRACT_ID = ?",projectName, projectId);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateProjectTeam(){
		String id=getJsonPara("projectId");
		String sql="select DISTINCT(t1.assign_user_id) from yq_task t1  where t1.project_id = ?";
		try {
			List<JSONObject> list0 = this.getQuery().queryForList(sql,new Object[]{id},new JSONMapperImpl());
			list0.forEach((JSONObject json) ->{
				String userId = json.getString("ASSIGN_USER_ID");
				//String projectId = json.getString("PROJECT_ID");
				//String createTime = json.getString("CREATE_TIME");
				
				EasyRecord record=new  EasyRecord("yq_project_team","PROJECT_ID","USER_ID");
				record.set("USER_ID", userId);
				record.set("PROJECT_ID", id);
				//record.set("JOIN_TIME", createTime);
				try {
					this.getQuery().save(record);
				} catch (Exception e) {
					this.error(null, e);
				}
			});
			sql="select DISTINCT(t2.user_id) from yq_task t1 INNER JOIN yq_cc t2 on t2.fk_id=t1.task_id where t1.project_id = ?";
			List<JSONObject> list = this.getQuery().queryForList(sql,new Object[]{id},new JSONMapperImpl());
			list.forEach((JSONObject json) ->{
				String userId = json.getString("USER_ID");
				//String projectId = json.getString("PROJECT_ID");
				//String createTime = json.getString("CREATE_TIME");
				
				EasyRecord record=new  EasyRecord("yq_project_team","PROJECT_ID","USER_ID");
				record.set("USER_ID", userId);
				record.set("PROJECT_ID", id);
				//record.set("JOIN_TIME", createTime);
				try {
					this.getQuery().save(record);
				} catch (Exception e) {
					this.error(null, e);
				}
			});
			try {
				int count= this.getQuery().queryForInt("select count(1) from yq_project_team where PROJECT_ID = ? ",id);
				this.getQuery().executeUpdate("update   yq_project  set person_count =? where PROJECT_ID= ?", count,id);
			} catch (Exception e) {
				this.error(null, e);
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForAdd(){
		ProjectModel model=getModel(ProjectModel.class, "project");
		try {
			//判断非0的合同号是否存在
			String projectNo=model.getString("PROJECT_NO");
			if(!"0".equals(projectNo)){
				boolean flag = this.getQuery().queryForExist("select count(1) from yq_project where PROJECT_NO = ?", projectNo);
				if(flag){
					return EasyResult.fail("合同号已存在.");
				}
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		
		JSONObject jsonObject=getJSONObject();
		JSONArray teams=jsonObject.getJSONArray("teams");
		model.setCreator(getUserPrincipal().getUserId());
		model.addCreateTime();
		model.setProjectState(11);
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		try {
			if(teams!=null){
				for(int i=0;i<teams.size();i++){
					String usertId=teams.getString(i);
					EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM", "PROJECT_ID","USER_ID");
					record.setPrimaryValues(model.getProjectId(),usertId);
					record.set("JOIN_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
				}
				model.set("PERSON_COUNT", teams.size());
			}
			model.save();
			
			try {
				this.getQuery().executeUpdate("update yq_project t1,"+Constants.DS_MAIN_NAME+".easi_dept t2 ,"+Constants.DS_MAIN_NAME+".easi_dept_user t3 set t1.dept_id=t2.DEPT_ID where t1.PO=t3.USER_ID and t2.DEPT_ID=t3.DEPT_ID and t1.PO=t3.USER_ID and t1.PROJECT_ID = ?",model.getProjectId());
			} catch (SQLException e) {
				getLogger().error(null,e);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		ProjectModel model=getModel(ProjectModel.class, "project");
		model.set("UPDATE_TIME",EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		JSONObject jsonObject=getJSONObject();
		try {
			JSONArray teams=jsonObject.getJSONArray("teams");
			if(teams!=null){
				this.getQuery().executeUpdate("delete from yq_project_team where PROJECT_ID = ?", model.getProjectId());
				for(int i=0;i<teams.size();i++){
					String usertId=teams.getString(i);
					EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM", "PROJECT_ID","USER_ID");
					record.setPrimaryValues(model.getProjectId(),usertId);
					record.set("JOIN_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
				}
				model.set("PERSON_COUNT", teams.size());
			}
			model.update();
			try {
				this.getQuery().executeUpdate("update yq_project t1,"+Constants.DS_MAIN_NAME+".easi_dept t2 ,"+Constants.DS_MAIN_NAME+".easi_dept_user t3 set t1.dept_id=t2.DEPT_ID where t1.PO=t3.USER_ID and t2.DEPT_ID=t3.DEPT_ID and t1.PO=t3.USER_ID and t1.PROJECT_ID = ?",model.getProjectId());
			} catch (SQLException e) {
				getLogger().error(null,e);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateTeam(){
		EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
		record.setColumns(getJSONObject());
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateModule(){
		EasyRecord record=new EasyRecord("YQ_PROJECT_MODULE","MODULE_ID");
		try {
			record.setColumns(getJSONObject());
			record.set("UPDATE_BY", getUserId());
			record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			if(StringUtils.isBlank(record.getString("MODULE_NAME"))){
				return EasyResult.fail("模块名不能为空.");
			}
			if(StringUtils.notBlank(record.getString("MODULE_ID"))){
				this.getQuery().update(record);
			}else{
				record.setPrimaryValues(RandomKit.randomStr());
				this.getQuery().save(record);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		ProjectModel model=getModel(ProjectModel.class, "project");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	
	public EasyResult actionForAddFavorite(){
		EasyRecord record=new EasyRecord("YQ_FAVORITE","FAVORITE_ID");
		try {
			String fkId=getJsonPara("fkId");
			String sql="select count(1) from YQ_FAVORITE where fk_id = ? and favorite_by = ?";
			int count=this.getQuery().queryForInt(sql, fkId,getUserId());
			if(count>0){
				return EasyResult.fail("不能重复关注!");
			}
			record.setPrimaryValues(RandomKit.uuid());
			record.set("fk_id", fkId);
			record.set("favorite_by", getUserId());
			record.set("favorite_time",EasyDate.getCurrentDateString());
			this.getQuery().save(record);
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelFavorite(){
		try {
			String favoriteId=getJsonPara("favoriteId");
			this.getQuery().executeUpdate("delete from YQ_FAVORITE  where fk_id = ? and favorite_by = ?",favoriteId,getUserId());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForTransferProject(){
		try {
			String projectId=getJsonPara("projectId");
			String newProjectId=getJsonPara("newProjectId");
			this.getQuery().executeUpdate("update YQ_TASK set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_version set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_weekly set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_work_hour set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_ops_version set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_link set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_kanban set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_folder set fk_id = ?  where fk_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_files set fk_id = ?  where fk_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_module set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_state_log set project_id = ?  where project_id = ?",newProjectId,projectId);
			this.getQuery().executeUpdate("update yq_project_team set project_id = ?  where project_id = ?",newProjectId,projectId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelProject(){
		try {
			String projectId=getJsonPara("projectId");
			this.getQuery().executeUpdate("update yq_project set is_delete =1  where project_id = ?",projectId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForSaveComment(){
		JSONObject jsonObject=getJSONObject();
		CommentModel model=new CommentModel();
		model.setFkId(jsonObject.getString("fkId"));
		model.setContent(jsonObject.getString("content"));
		model.setCreator(getUserPrincipal().getUserId());
		model.setCreateTime(EasyDate.getCurrentDateString());
		model.setUserAgent(getRequest().getHeader("user-agent"));
		model.setIp(WebKit.getIP(getRequest()));
		return CommentService.getService().addComment(model);
	}
	public EasyResult actionForUpdateProjectState(){
		JSONObject jsonObject=getJSONObject();
		EasyRecord model=new EasyRecord("yq_project_state_log","operate_id");
		model.set("operate_id", RandomKit.uuid());
		model.set("update_by",getUserPrincipal().getUserId());
		model.set("update_time",EasyDate.getCurrentDateString());
		model.set("project_id", jsonObject.getString("projectId"));
		model.set("update_state", jsonObject.getString("projectState"));
		model.set("update_reason", jsonObject.getString("projectUpdateDesc"));
		model.set("font_color", jsonObject.getString("color"));
		try {
			this.getQuery().executeUpdate("update yq_project set project_state = ? ,UPDATE_TIME = ? where project_id  = ?",jsonObject.getString("projectState"),EasyDate.getCurrentDateString(),jsonObject.getString("projectId"));
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddProjectWorkHour(){
		JSONObject params = getJSONObject();
		
		String monthId=  params.getString("monthId");
		String id=  params.getString("id");
		Object obj = params.get("itemId");
		
		JSONArray array = new JSONArray();
		if(obj instanceof String) {
			array.add(obj.toString());
		}else {
			array = params.getJSONArray("itemId");
		}
		
		for(int i=0;i<array.size();i++){
			String itemId = array.getString(i);
			boolean isAdd = true;
			if(itemId.startsWith("update_")) {
				isAdd = false;
				itemId = itemId.substring(7);
			}
			EasyRecord record=new EasyRecord("yq_project_work_hour","item_id");
			record.set("wh_id",id);
			record.set("work_result",params.getString("result_"+itemId));
			record.set("project_id",params.getString("projectId_"+itemId));
			record.set("work_time",params.getString("time_"+itemId));
			record.set("project_name",params.getString("projectName_"+itemId));
			try {
				if(isAdd) {
					record.set("worker",getUserId());
					record.set("year",monthId.substring(0, 4));
					record.set("month_id",monthId);
					record.set("create_time",EasyDate.getCurrentDateString());
					record.set("creator",getUserId());
					record.set("dept_id",getDeptId());
					this.getQuery().save(record);
				}else {
					record.set("item_id",itemId);
					this.getQuery().update(record);
				}
				
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		
		EasySQL sql = new EasySQL("update yq_project_wh t1 set");
		sql.append("t1.project_num = (select count(DISTINCT(t2.project_id)) from yq_project_work_hour t2 where t2.wh_id = t1.id),");
		sql.append(EasyDate.getCurrentDateString(),"t1.update_time = ?,");
		sql.append(1,"t1.state = ?,");
		sql.append("t1.work_time = (select sum(t3.work_time) from yq_project_work_hour t3 where t3.wh_id = t1.id)");
		sql.append(id,"where t1.id = ?");
		
		try {
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		return EasyResult.ok();
	}
	
}




