<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择任务</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selecTaskForm">
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 140px">
					 <span class="input-group-addon">编号</span>	
					 <input type="text" name="id" class="form-control input-sm">
			     </div>
	   			 <div class="input-group input-group-sm ml-5" style="width: 180px">
					 <span class="input-group-addon">名称</span>	
					 <input type="text" name="title" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectTask.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		</div>
            	<div class="ibox">
              		<table id="selectTaskList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectTask.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectTask={
					sid:'${param.sid}',
					query : function(){
						$("#selecTaskForm").queryData();
					},
					initTable : function(){
						$("#selecTaskForm").initTable({
							mars:'TaskDao.zentaoBugList',
							id:'selectTaskList',
							page:true,
							limit:20,
							height:'400px',
							rowDoubleEvent:'SelectTask.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	field: 'STATUS',
								title: '状态',
								align:'left',
								width:50,
								templet:function(row){
									var json = {'active':'激活','resolved':'已解决','closed':'已关闭'};
									return json[row['STATUS']]||'';
								}
							 },{
							    field: 'TITLE',
								title: '标题',
								templet:'<div>{{d.ID}}_{{d.TITLE}}</div>'
							},{
								field:'OPENEDDATE',
								title:'创建时间',
								width:100,
								templet:function(row){
									return cutText(row['OPENEDDATE'],12,'');
								}
							},{
								field:'OPENEDBY',
								title:'发起人',
								width:80
							},{
							    field: 'ASSIGNEDTO',
								title: '负责人',
								width:80
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectTask.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('selectTaskList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['TITLE']);
									ids.push(data[index]['ID']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								popup.layerClose("selecTaskForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							el.val(selectRow['TITLE']);
							if(el.prev().length>0){
								el.prev().val(selectRow['ID']);
							}
							popup.layerClose("selecTaskForm");
						}
					}
					
			};
			$(function(){
				SelectTask.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>