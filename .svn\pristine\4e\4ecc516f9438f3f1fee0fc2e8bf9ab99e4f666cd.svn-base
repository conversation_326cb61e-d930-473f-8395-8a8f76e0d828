package com.yunqu.work.dao.project;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name = "ProjectStatisticDao")
public class ProjectStatisticDao extends AppDaoContext {

    @WebControl(name = "projectDashboardStat", type = Types.RECORD)
    public JSONObject projectDashboardStat() {
        JSONObject object = new JSONObject();
        int projectCount = Db.queryInt("select count(1) from yq_project where is_delete = 0 and project_state < 20");
        object.put("projectCount", projectCount);
        int taskCount = Db.queryInt("select count(1) from yq_task where task_state =20");
        object.put("taskCount", taskCount);
        int riskCount = Db.queryInt("select count(1) from yq_project_risk where risk_state = 0");
        object.put("riskCount", riskCount);
        return getJsonResult(object);
    }

    @WebControl(name = "projectStage", type = Types.LIST)
    public JSONObject projectStage() {
        EasySQL sql = getEasySQL("SELECT count(project_id) AS NUMS, COALESCE(NULLIF(PROJECT_STAGE, ''), '未填') AS PROJECT_STAGE");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("GROUP BY COALESCE(NULLIF(PROJECT_STAGE, ''), '未填')");
        setOrderBy(sql, " order by NUMS desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectState", type = Types.LIST)
    public JSONObject projectState() {
        EasySQL sql = getEasySQL("SELECT count(project_id) AS NUMS, PROJECT_STATE");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("GROUP BY COALESCE(NULLIF(PROJECT_STATE, ''), '未填')");
        setOrderBy(sql, " order by NUMS desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectPersonCountStat", type = Types.LIST)
    public JSONObject projectPersonCountStat() {
        EasySQL sql = getEasySQL("SELECT");
        sql.append("SUM(CASE WHEN PERSON_COUNT < 5 THEN 1 ELSE 0 END) AS '低于5人',");
        sql.append("SUM(CASE WHEN PERSON_COUNT >= 5 AND PERSON_COUNT < 10 THEN 1 ELSE 0 END) AS '5-10人',");
        sql.append("SUM(CASE WHEN PERSON_COUNT >= 10 AND PERSON_COUNT < 15 THEN 1 ELSE 0 END) AS '10-15人',");
        sql.append("SUM(CASE WHEN PERSON_COUNT >= 15 AND PERSON_COUNT < 20 THEN 1 ELSE 0 END) AS '15-20人',");
        sql.append("SUM(CASE WHEN PERSON_COUNT >= 20 THEN 1 ELSE 0 END) AS '20人及以上'");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectWorkPeopleCountStat", type = Types.LIST)
    public JSONObject projectWorkPeopleCountStat() {
        EasySQL sql = getEasySQL("SELECT");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT < 5 THEN 1 ELSE 0 END) AS '低于5人',");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT >= 5 AND WORK_PEOPLE_COUNT < 10 THEN 1 ELSE 0 END) AS '5-10人',");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT >= 10 AND WORK_PEOPLE_COUNT < 15 THEN 1 ELSE 0 END) AS '10-15人',");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT >= 15 AND WORK_PEOPLE_COUNT < 20 THEN 1 ELSE 0 END) AS '15-20人',");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT >= 20 THEN 1 ELSE 0 END) AS '20人及以上'");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectPersonCountList", type = Types.LIST)
    public JSONObject projectPersonCountList() {
        EasySQL sql = getEasySQL("SELECT PROJECT_ID,PROJECT_NAME,PROJECT_NO,PERSON_COUNT,WORK_PEOPLE_COUNT");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        setOrderBy(sql,"ORDER BY PERSON_COUNT desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "workHourRank", type = Types.LIST)
    public JSONObject workHourRank() {
        EasySQL sql = getEasySQL("SELECT PROJECT_ID,PROJECT_NAME,PROJECT_NO,CAST(TRUNCATE(WORKHOUR_DAY,0) AS SIGNED) AS WORKHOUR_DAY");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("ORDER BY CAST(WORKHOUR_DAY AS DECIMAL(10,2)) DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    private void setCondition(EasySQL sql) {
        String timeType = param.getString("timeType");
        if(StringUtils.isBlank(timeType)){
            return;
        }
        if ("create_time".equals(timeType)) {
            sql.append(param.getString("beginDate") + " 00:00:00", "and " + timeType + " >= ?");
            sql.append(param.getString("endDate") + " 23:59:59", "and " + timeType + " <= ?");
        } else {
            sql.append(param.getString("beginDate"), "and " + timeType + " >= ?");
            sql.append(param.getString("endDate"), "and " + timeType + " <= ?");
        }
    }

    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by ").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }
}
