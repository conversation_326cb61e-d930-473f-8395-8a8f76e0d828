<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<input type="hidden" name="costListBy" value="contractId">
<div class="pull-left mb-5 mt-10">
    <button type="button" class="btn btn-sm btn-info" onclick="contractCostList.add()">+ 新增成本</button>
</div>

<table id="costList" class="layui-table"></table>


<script type="text/javascript">
    var contractCostList = {
        init: function () {
            $("#ContractDetailForm").initTable({
                    mars:'CustOperateDao.costList',
                    id:'costList',
                    page:false,
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align:'left'
                        },{
                            field:'',
                            title:'操作',
                            width:100,
                            align: "center",
                            templet:function(d) {
                                var html = '<a href="javascript:;" onclick="contractCostList.edit(\'' + d.COST_ID + '\',\'' + d.RELATE_TYPE + '\')">编辑</a>';
                                html += ' <a href="javascript:;" onclick="contractCostList.del(\'' + d.COST_ID + '\')">删除</a>';
                                return html;
                            }
                        }, {
                            field: 'DATE_ID',
                            title: '成本日期',
                            width: 100
                        },{
                            field: 'CUST_NAME',
                            title: '客户名称',
                            width:150
                        },{
                            field: 'PLATFORM_NAME',
                            title: '业务平台',
                            width:100
                        },{
                            field: 'COST_TYPE',
                            title: '成本类型',
                            width:80
                        },{
                            field: 'COST_NAME',
                            title: '成本名称',
                            width:150
                        },{
                            field: 'COST_OWNER',
                            title: '成本归属',
                            width:80,
                            align: 'center',
                            templet: function(row){
                                var types = ['平台成本','客户成本','其他'];
                                return types[row.COST_OWNER] || '其他';
                            }
                        },{
                            field: 'RELATE_TYPE',
                            title: '成本来源',
                            width:80,
                            align: 'center',
                            templet: function(row){
                                var types = ['其他','采购订单','个人报销'];
                                return types[row.RELATE_TYPE] || '其他';
                            }
                        },{
                            field: 'UNIT_PRICE',
                            title: '单价',
                            width:80
                        },{
                            field: 'NUMBER',
                            title: '数量',
                            width:80
                        },{
                            field: 'TOTAL_AMOUNT',
                            title: '总金额',
                            width:80
                        },{
                            field: 'TAX_RATE',
                            title: '税率%',
                            width:80
                        },{
                            field: 'NO_TAX_AMOUNT',
                            title: '不含税金额',
                            width:100
                        },{
                            field: 'ORDER_NO',
                            title: '采购订单号',
                            width:120,
                            templet:'<div><a href="javascript:;" onclick="purchaseDetail(\'{{d.ORDER_ID}}\',\'{{d.CUST_ID}}\')">{{d.ORDER_NO}}</a></div>'
                        },{
                            field: 'ORDER_TIME',
                            title: '采购下单时间',
                            width:120
                        },{
                            field: 'PAY_TIME',
                            title: '付款时间',
                            width:120
                        },{
                            field: 'SUPPLIER_NAME',
                            title: '供应商',
                            width:120,
                            templet:'<div><a href="javascript:;" onclick="supplierDetail(\'{{d.SUPPLIER_ID}}\')">{{d.SUPPLIER_NAME}}</a></div>'
                        },{
                            field: 'BX_APPLY_ID',
                            title: '报销流程',
                            width:100,
                            templet: function(row){
                                return row.BX_APPLY_ID ? '<a href="javascript:;" onclick="bxDetail(\''+row.BX_APPLY_ID+'\')">查看流程</a>' : '';
                            }
                        },{
                            field: 'REMARK',
                            title: '备注',
                            width:150
                        }
                    ]],done:function(res){

                    }});

        },
        add: function (){
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                shadeClose: false,
                title: '新增成本',
                offset: '20px',
                area: ['700px', '600px'],
                url: ctxPath + '/pages/crm/platform/include/add-cost.jsp',
                data: {contractId:contractId, contractName:contractInfo.CONTRACT_NAME,platformId: contractInfo.PLATFORM_ID,platformName:contractInfo.PLATFORM_NAME, relateType:"1"}
            });
        },
        edit: function (costId, relateType){
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                shadeClose: false,
                offset: '20px',
                area: ['700px', '600px'],
                url: ctxPath + '/pages/crm/platform/include/add-cost.jsp',
                title: '成本编辑',
                data: { contractId: contractId, contractName: contractInfo.CONTRACT_NAME, costId: costId, relateType: relateType || "1" }
            });
        },
        del: function (costId){
            layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function () {
                ajax.remoteCall("${ctxPath}/servlet/custOperate?action=deleteCost", {costId:costId}, function (result) {
                    if (result.state == 1) {
                        layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                            layer.closeAll();
                            reloadCostList();
                        });
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                });
            });
        }

    }

    function reloadCostList(){
        $("#ContractDetailForm").queryData({id:'costList',page:false});
    }

</script>