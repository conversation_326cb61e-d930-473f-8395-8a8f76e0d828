package com.yunqu.work.kingdee;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.easitline.common.core.log.LogEngine;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class InvokeHelper {

	public static String POST_K3CloudURL = "http://172.16.88.88/K3Cloud/";
	
	public static void info(String msg) {
		LogEngine.getLogger("k3cloud").info(msg);
	}

	// Cookie 值
	private static String CookieVal = null;

	private static Map<String, String> map = new HashMap<String, String>();
	static {
		map.put("Save","Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc");
		map.put("View","Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.View.common.kdsvc");
		map.put("Submit","Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc");
		map.put("Audit","Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit.common.kdsvc");
		map.put("UnAudit","Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit.common.kdsvc");
		map.put("StatusConvert","Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.StatusConvert.common.kdsvc");
		map.put("businessInfo","Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.QueryBusinessInfo.common.kdsvc");
		map.put("groupInfo","Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.QueryGroupInfo.common.kdsvc");
		map.put("BillQuery","Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc");
	}

	private static HttpURLConnection initUrlConn(String url, JSONArray paras)throws Exception {
		URL postUrl = new URL(POST_K3CloudURL.concat(url));
		HttpURLConnection connection = (HttpURLConnection) postUrl.openConnection();
		if (CookieVal != null) {
			connection.setRequestProperty("Cookie", CookieVal);
		}
		if (!connection.getDoOutput()) {
			connection.setDoOutput(true);
		}
		connection.setConnectTimeout(180*1000);
		connection.setReadTimeout(180*1000);
		connection.setRequestMethod("POST");
		connection.setUseCaches(false);
		connection.setInstanceFollowRedirects(true);
		connection.setRequestProperty("Content-Type", "application/json");
		DataOutputStream out = new DataOutputStream(connection.getOutputStream());

		UUID uuid = UUID.randomUUID();
		int hashCode = uuid.toString().hashCode();

		JSONObject jObj = new JSONObject();

		jObj.put("format", 1);
		jObj.put("useragent", "ApiClient");
		jObj.put("rid", hashCode);
		jObj.put("parameters", chinaToUnicode(paras.toString()));
		jObj.put("timestamp", new Date().toString());
		jObj.put("v", "1.0");

		out.writeBytes(jObj.toString());
		out.flush();
		out.close();

		return connection;
	}

	// Login
	public static boolean Login(String dbId, String user, String pwd, int lang)throws Exception {
		boolean bResult = false;
		String sUrl = "Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc";

		JSONArray jParas = new JSONArray();
		jParas.add(dbId);// 帐套Id
		jParas.add(user);// 用户名
		jParas.add(pwd);// 密码
		jParas.add(lang);// 语言

		HttpURLConnection connection = initUrlConn(sUrl, jParas);
		// 获取Cookie
		String key = null;
		for (int i = 1; (key = connection.getHeaderFieldKey(i)) != null; i++) {
			if (key.equalsIgnoreCase("Set-Cookie")) {
				String tempCookieVal = connection.getHeaderField(i);
				if (tempCookieVal.startsWith("kdservice-sessionid")) {
					CookieVal = tempCookieVal;
					break;
				}
			}
		}

		BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
		String line;
		info(" ============================= ");
		info(" Contents of post request ");
		info(" ============================= ");
		while ((line = reader.readLine()) != null) {
			String sResult = new String(line.getBytes(), "utf-8");
			info(sResult);
			bResult = line.contains("\"LoginResultType\":1");
		}
		info(" ============================= ");
		info(" Contents of post request ends ");
		info(" ============================= ");
		reader.close();

		connection.disconnect();

		return bResult;
	}

	// Save
	public static void Save(String formId, String content) throws Exception {
		Invoke("Save", formId, content);
	}

	// View
	public static String View(String formId, String content) throws Exception {
		return Invoke("View", formId, content);
	}
	// View
	public static String BillQuery(String formId, String content) throws Exception {
		return Invoke("BillQuery", formId, content);
	}

	// Submit
	public static void Submit(String formId, String content) throws Exception {
		Invoke("Submit", formId, content);
	}

	// Audit
	public static void Audit(String formId, String content) throws Exception {
		Invoke("Audit", formId, content);
	}

	// UnAudit
	public static void UnAudit(String formId, String content) throws Exception {
		Invoke("UnAudit", formId, content);
	}

	// StatusConvert
	public static void StatusConvert(String formId, String content)throws Exception {
		Invoke("StatusConvert", formId, content);
	}
	
	//businessInfo
	public static String businessInfo(String formId, String content)throws Exception {
		return Invoke("businessInfo", formId, content);
	}

	private static String Invoke(String deal, String formId, String content)throws Exception {
		String sUrl = map.get(deal).toString();
		JSONArray jParas = new JSONArray();
		jParas.add(formId);
		jParas.add(content);

		HttpURLConnection connectionInvoke = initUrlConn(sUrl, jParas);

		BufferedReader reader = new BufferedReader(new InputStreamReader(connectionInvoke.getInputStream()));
		String sResult = null;
		String line;
		info(" ============================= ");
		info(" Contents of post request ");
		info(" ============================= ");
		while ((line = reader.readLine()) != null) {
			sResult = new String(line.getBytes(), "utf-8");
			info(sResult);
		}
		info(" ============================= ");
		info(" Contents of post request ends ");
		info(" ============================= ");
		reader.close();

		connectionInvoke.disconnect();
		
		return sResult;
		
	}

	
	public static String getCookieVal() {
		return CookieVal;
	}

	public static void setCookieVal(String cookieVal) {
		CookieVal = cookieVal;
	}

	/**
	 * 把中文转成Unicode码
	 * 
	 * @param str
	 * @return
	 */
	public static String chinaToUnicode(String str) {
		String result = "";
		for (int i = 0; i < str.length(); i++) {
			int chr1 = (char) str.charAt(i);
			if (chr1 >= 19968 && chr1 <= 171941) {// 汉字范围 \u4e00-\u9fa5 (中文)
				result += "\\u" + Integer.toHexString(chr1);
			} else {
				result += str.charAt(i);
			}
		}
		return result;
	}
}
