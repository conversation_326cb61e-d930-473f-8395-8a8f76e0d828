<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowName}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 140px;">标题</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的人事资料调用申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 150px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请材料</td>
					  			<td>
						  			<select id="sealType" data-rules="required" class="form-control input-sm" name="apply.data1">
						  				<option value="云趣营业执照">云趣营业执照</option>
						  				<option value="中融营业执照">中融营业执照</option>
						  				<option value="公司资质证书">公司资质证书</option>
						  			</select>
					  			</td>
					  			<td class="required">材料类型</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data2">
						  				<option value=""></option>
						  				<option value="原件">原件</option>
						  				<option value="复印件">复印件</option>
						  				<option value="扫描件">扫描件</option>
						  			</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">最晚提供时间</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data3"/>
					  			</td>
					  			<td>预计归还时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" placeholder="如是原件"  class="form-control input-sm Wdate" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="vertical-align: top;">申请原因</td>
					  			<td colspan="3">
									<textarea style="height: 70px;" data-rules="required" class="form-control input-sm" maxlength="500" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){
				Flow.initData();
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			var type = $("[name='apply.data2']").val();
			var useDate = $("[name='apply.data3']").val();
			var returnDate = $("[name='apply.data4']").val();
			if(type=='原件'&&returnDate==''){
				layer.msg('原件需填写归还时间');
				return;
			}
			if(returnDate&&returnDate<useDate){
				layer.msg('归还时间应大于使用时间');
				return;
			}
			FlowCore.ajaxSubmitForm(state);
		}
		
		
		Flow.initData = function(){
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var type = applyInfo.data1;;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='人力行政主管'){
				var selectUserName = '杨曼丽';
				if(type=='公司资质证书'||type=='云趣营业执照'){
					selectUserName = '庞小翠';
				}
				params['selectUserName'] = selectUserName;
			}
			FlowCore.approveData = params;
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>