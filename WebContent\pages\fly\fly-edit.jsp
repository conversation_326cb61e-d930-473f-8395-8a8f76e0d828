<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>云趣乐享平台</title>
	<style>
		#fileList{line-height: 36px;}
		.file-item{margin-right: 10px;display: inline-block;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
 <div class="layui-container fly-marginTop">
  <div class="fly-panel" pad20 style="padding-top: 5px;">
    <!--<div class="fly-none">没有权限</div>-->
    <div class="layui-form layui-form-pane">
      <div class="layui-tab layui-tab-brief" lay-filter="user">
        <ul class="layui-tab-title">
          <li class="layui-this"><c:choose><c:when test="${pageMode=='edit'}">编辑帖子</c:when><c:otherwise>发表新帖</c:otherwise></c:choose></li>
        </ul>
        <div class="layui-form layui-tab-content" id="LAY_ucm" style="padding: 20px 0;">
          <div class="layui-tab-item layui-show">
            <form action="/yq-work/fly/doAdd" method="post" class="layui-form" lay-filter="editForm">
              <div class="layui-row layui-col-space15 layui-form-item">
                <div class="layui-col-md3">
                  <label class="layui-form-label">所在专栏</label>
                  <div class="layui-input-block">
                    <select lay-verify="required" name="classType" lay-filter="column"> 
                      <option></option> 
                      <optgroup label="乐享">
	                      <option value="10">提问</option> 
	                      <option value="20">分享</option> 
	                      <option value="30">AI</option> 
	                      <option value="40">建议</option> 
	                      <option value="50">开发</option> 
	                      <option value="60">软件</option> 
                      </optgroup>
                      <optgroup label="MORE">
                      	<option value="100">课堂</option>
                      	<option value="200">活动</option>
                      </optgroup>
                    </select>
                  </div>
                </div>
                <div class="layui-col-md9">
                  <label for="L_title" class="layui-form-label">标题</label>
                  <div class="layui-input-block">
                    <input type="text" id="L_title" name="title" required lay-verify="required" autocomplete="off" lay-affix="clear" class="layui-input" value="${record.title}">
                     <input type="hidden" name="id" value="${record.remind_id}">
                     <input type="hidden" name="newId" value="${newId}">
                  </div>
                </div>
              </div>
              <div class="layui-row layui-col-space15 layui-form-item" id="LAY_type">
                <div class="layui-col-md3">
                  <label class="layui-form-label">编辑器类型</label>
                  <div class="layui-input-block">
                    <select name="editType" lay-filter="editType">
                      <option value="layui">默认</option>
                      <option value="html">html</option>
                      <option value="markdown">markdown</option>
                    </select>
                  </div>
                </div>
                <div class="layui-col-md6">
                  <div class="layui-form-label">标签</div>
                    <div class="layui-input-block">
                  		<input name="tag" placeholder="选择标签" class="layui-input" lay-affix="clear" value="${record.tag}" id="tagInput">
                    </div>
                </div>
              </div>
              <!-- <div class="layui-row layui-col-space15 layui-form-item layui-hide" id="LAY_quiz">
                <div class="layui-col-md3">
                  <label class="layui-form-label">所属产品</label>
                  <div class="layui-input-block">
                    <select name="project">
                      <option></option>
                      <option value="layui">layui</option>
                      <option value="独立版layer">独立版layer</option>
                      <option value="独立版layDate">独立版layDate</option>
                      <option value="LayIM">LayIM</option>
                      <option value="Fly社区模板">Fly社区模板</option>
                    </select>
                  </div>
                </div>
                <div class="layui-col-md3">
                  <label class="layui-form-label" for="L_version">版本号</label>
                  <div class="layui-input-block">
                    <input type="text" id="L_version" value="" name="version" autocomplete="off" class="layui-input">
                  </div>
                </div>
                <div class="layui-col-md6">
                  <label class="layui-form-label" for="L_browser">浏览器</label>
                  <div class="layui-input-block">
                    <input type="text" id="L_browser"  value="" name="browser" placeholder="浏览器名称及版本，如：IE 11" autocomplete="off" class="layui-input">
                  </div>
                </div>
              </div> -->
              <div class="layui-form-item layui-form-text">
                <div class="layui-input-block">
                  <textarea id="L_content" name="content" required lay-verify="required" placeholder="详细描述" class="layui-textarea fly-editor" style="height: 260px;">${record.content}</textarea>
                </div>
              </div>
              <div class="layui-row fileContainer" style="display: none;">
              	<div class="layui-col-md12">
              		<div id="fileList"></div>
              	</div>
              </div>
              <div class="layui-row layui-col-space15 layui-form-item">
                <div class="layui-col-md6">
                  <div class="layui-form-label">上传附件：</div>
               	  <div class="layui-input-block">
	                  <button type="button" class="layui-btn upload-btn" lay-options="{accept: 'file'}"><i class="layui-icon layui-icon-upload"></i>上传文件</button>
               	  </div>
                </div>
                <div class="layui-col-md6">
                    <div class="layui-inline">
                	 <label class="layui-form-label">悬赏积分</label>
	                  <div class="layui-input-inline" style="width: 190px;">
	                    <select name="experience" <c:if test="${pageMode=='edit'}">disabled</c:if>>
	                      <option value="20">20</option>
	                      <option value="30">30</option>
	                      <option value="50">50</option>
	                      <option value="60">60</option>
	                      <option value="80">80</option>
	                    </select>
	                  </div>
                  	  <div class="layui-form-mid layui-word-aux">发表后无法更改积分</div>
                  </div>
                </div>
               </div>
              <div class="layui-form-item">
                <button class="layui-btn" lay-filter="*" lay-submit>立即发布</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

</EasyTag:override>
 
<EasyTag:override name="script">
	<script type="text/javascript">
	    layui.cache.page = 'jie';
	    layui.cache.user = {username: '${staffInfo.userName}' , uid: '${staffInfo.userId}', avatar: '${staffInfo.picUrl}', experience: 83 , sex: '男'};
		layui.config({version: "3.2",base: '/yq-work/pages/fly/static/mods/'}).extend({fly: 'index'}).use(['fly','jie','form'],function(){
		   var form = layui.form;
		   var fly = layui.fly;
		   var id = '${record.remind_id}';
		   var newId = '${newId}';
		   if(id){
			   var editType = '${record.edit_type}';
			   top.editType = editType;
			   form.val('editForm', {'editType':editType,'classType':'${record.remind_type}','experience':'${record.experience}'});
			   if(editType!='layui'){
					$('.fly-edit span').hide();
					$("span[type='yulan']").show();
				}else{
					$('.fly-edit span').show();
				}
		   }
		   
		   $.get('/yq-work/fly/getTags',function(result){
			   console.log(result);
			   var data = result.data;
			   var array = [];
			   for(var index in data){
				   array.push({title:data[index],id:data[index]});
			   }
		   	  layui.dropdown.render({
			    elem: '#tagInput',
			    data: array,
			    click: function(obj){
			       this.elem.val(obj.title);
			    },
			    style: 'min-width: 235px;'
			  });
		   });
		   
		   layui.use('upload', function(){
			 	  var upload = layui.upload;
				  var uploadInst = upload.render({
				    elem: '.upload-btn'
				    ,url: '/yq-work/servlet/upload?action=index'
				    ,accept: 'file'
				    ,exts:'sh|xls|xlsx|txt|jpg|zip|ppt|pptx|doc|docx|tar|png|pdf|csv|rar|tar|7z|mp4|mp3|eml|java|war|sql'
				    ,size:1024*200
					,multiple:true
					,number:20
				    ,field: 'flyFile'
				    ,data: {
						fkId:function(){
						   return id?id:newId;
						},
						source:'fly'
					}
				    ,done: function(res, index, upload){
						var item = this.item;
					    layer.closeAll('loading');
					    if(res&&res.state==1){
					    	$('.fileContainer').show();
					    	layer.msg(res.msg,{icon: 1,time:800},function(){
					    		$('#fileList').append('<div class="file-item"><input type="hidden" name="fileId" value="'+res.data.id+'"><a class="fly-link" target="_blank" href="'+res.data.url+'">'+res.data.name+'</a> <a href="javascript:;" onclick="$(this).parent().remove();">x</a></div>');
							}); 
						}else{
							layer.alert(res.msg,{icon: 5});
						}
				    },before:function(){
						layer.load();
				    },allDone: function(obj){ 
					
					}
				    ,error: function(res, index, upload){
						layer.closeAll('loading');
				    	layer.alert("上传文件请求异常！",{icon: 5});
				    }
		      });
	  	  });
		   
		   if(id){
			   fly.json('/yq-work/webcall?action=FileDao.fileList', {fkId: id}, function(res){
					var fileArray = [];
					for(var index in res.data){
						var row = res.data[index];
						fileArray.push('<div class="file-item"><a target="_blank" href="'+row.ACCESS_URL+'" class="fly-link"><i class="layui-icon layui-icon-download-circle"></i> '+row.FILE_NAME+'</a> <a href="javascript:;" onclick="removeFile(this,\''+row.FILE_ID+'\');">x</a></div>');
					}
					$('#fileList').html(fileArray.join(''));
					if(fileArray.length>0){
						$('.fileContainer').show();
					}
			   });
		   }
		   
	    });
		
		function removeFile(el,id){
			ajax.remoteCall("/yq-work/servlet/upload?action=del",{id:id},function(result) {
				layer.msg('删除成功',{icon:1,offset:'20px',time:1200});
			    $(el).parent().remove();
			});
		 }
   </script>
</EasyTag:override>
<%@ include file="fly-layout.jsp" %>
