INSERT into yq_project_module(module_id,module_name,project_id,update_time,remark) SELECT project_id module_id,'默认模块' module_name,project_id,'2022-06-24 17:43:49' update_time,'无' remark from yq_project where project_id not in(select t2.project_id from yq_project_module t2);

update yq_task t1 set t1.module_id = (select t2.module_id from yq_project_module t2 where t2.project_id = t1.project_id and t2.module_name='默认模块'),t1.module_name='默认模块' where t1.module_id='';




ALTER TABLE `yq_flow_bx_item` 
ADD COLUMN `contract_state` varchar(30) NULL DEFAULT '' COMMENT '合同状态' AFTER `contract_name`;


ALTER TABLE `yq_finance_contact_unit` 
ADD COLUMN `id` int NULL FIRST,
ADD COLUMN `bank_name` varchar(100) NULL DEFAULT '' AFTER `unit_name`,
ADD COLUMN `bank_acount` varchar(100) NULL DEFAULT '' AFTER `bank_name`,
ADD COLUMN `creator` varchar(32) NULL DEFAULT '' AFTER `bank_acount`,
ADD COLUMN `create_name` varchar(30) NULL DEFAULT '' AFTER `creator`,
ADD COLUMN `create_time` varchar(19) NULL DEFAULT '' AFTER `create_name`;

ALTER TABLE `yq_flow_apply` 
ADD COLUMN `apply_end_time` varchar(19) NULL DEFAULT '' AFTER `next_node_id`;

ALTER TABLE `yq_flow_bx_base` 
MODIFY COLUMN `pay_type` varchar(20) NULL DEFAULT '' COMMENT '付款类型' AFTER `pay_state`,
ADD COLUMN `contact_unit` varchar(100) NULL COMMENT '往来单位' AFTER `pay_type`;

ALTER TABLE `yq_flow_bx_base` 
ADD COLUMN `contact_code` varchar(30) NULL COMMENT '往来单位编号' AFTER `contact_unit`;

ALTER TABLE `yq_flow_bx_dept` 
ADD COLUMN `dept_state` int NULL DEFAULT 0 COMMENT '状态' AFTER `approve_flow`;



CREATE TABLE `yq_finance_contact_unit` (
  `id` int NOT NULL AUTO_INCREMENT,
  `unit_no` varchar(30) DEFAULT NULL,
  `unit_name` varchar(100) DEFAULT '',
  `bank_name` varchar(100) DEFAULT '',
  `bank_acount` varchar(100) DEFAULT '',
  `creator` varchar(32) DEFAULT '',
  `create_name` varchar(30) DEFAULT '',
  `create_time` varchar(19) DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;