<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>待办任务管理</title>
	<style>
		.layui-tree-txt{font-size: 13px;}
		.layui-btn+.layui-btn{margin-left: 2px;}
		.layui-tab-item{padding: 0px 5px;margin-top: -5px;}
		.icon{width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:1px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="status" id="status" type="hidden" value="1"/>
			<input name="taskState" id="taskState" type="hidden"/>
			<div class="ibox">
					<div class="ibox-title clearfix">
		          		 <div class="form-group">
						      <div class="input-group input-group-sm"  style="width: 150px">
								 <span class="input-group-addon">任务类型</span>	
								<select data-mars-reload="false" name="taskTypeId" onchange="list.query()" data-mars="TaskDao.taskType" class="form-control">
									<option value="">请选择</option>
								</select>
						     </div>
		          		     <div class="input-group input-group-sm" style="width: 180px">
								 <span class="input-group-addon">任务名称</span>	
								 <input type="text" name="taskName" class="form-control input-sm">
						     </div>
		          		     <div class="input-group input-group-sm"  style="width: 160px">
								 <span class="input-group-addon">项目</span>	
								 <input type="hidden" name="projectId" class="form-control input-sm">
								 <input type="text" id="projectId" onclick="singleProject(this);" class="form-control input-sm">
						     </div>
							 <div class="input-group input-group-sm">
								 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								 <button type="button" class="btn btn-sm btn-default ml-5" onclick="list.clearForm()">清空</button>
							 </div>
							 <div class="input-group input-group-sm pull-right">
								 <button type="button" class="btn btn-sm btn-info" onclick="list.add()"> + 发起任务</button>
								 <button type="button" class="btn btn-sm btn-default ml-10" onclick="list.importExcel()"> 导入任务</button>
								 <button type="button" class="btn btn-sm btn-warning ml-10" onclick="flowApplyLink('ts_rd')"> + 需求流程</button>
								  <script type="text/html" id="taskBar">
						     	   <a href="javascript:;" lay-event="finishTask" class="btn btn-xs btn-default btn-info finishTask">批量完成</a>
						     	   <a href="javascript:;" onclick="exportTask()" class="btn btn-xs btn-default ml-10">导出任务</a>
							 </script>
							 </div>
		          	     </div>
		              </div> 
					  <div class="ibox-content">
						  <div class="layui-tab layui-tab-brief" style="margin-top: 6px;" lay-filter="task" data-mars="TaskDao.myTaskListCountStat">
							  <ul class="layui-tab-title">
							    <li data-state="" class="layui-this">全部 <span class="layui-badge layui-bg-cyan ALL">0</span></li>
							    <li data-state="10">待办中 <span class="layui-badge layui-bg-orange" id="A">0</span></li>
							    <li data-state="11">已退回 <span class="layui-badge" id="F">0</span></li>
							    <li data-state="12">暂停中 <span class="layui-badge layui-bg-cyan" id="G">0</span></li>
							    <li data-state="13">已取消 <span class="layui-badge layui-bg-orange" id="H">0</span></li>
							    <li data-state="20">进行中 <span class="layui-badge" id="B">0</span></li>
							     <li data-state="25">超时未完成 <span class="layui-badge" id="E">0</span></li>
							    <li data-state="30">已完成 <span class="layui-badge layui-bg-blue" id="C">0</span></li>
							    <li data-state="40">已验收 <span class="layui-badge layui-bg-green" id="D">0</span></li>
							  </ul>
							  <div class="layui-tab-content" style="padding: 5px 0px;margin-top: 5px;">
							    <div class="layui-tab-item layui-show">
							    	 <table class="layui-hide" id="todoTaskTable"></table>
							    </div>
							  </div>
						  </div> 
					</div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">

		var firstLoad = true;
		var list = {
			initTable:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					title:'我负责的任务',
					cellMinWidth:50,
					limit:30,
					data:{status:1},
					height:'full-140',
					toolbar:'#taskBar',
					autoSort:false,
					id:'todoTaskTable',
					cols: [[
						{
						 	type: 'checkbox',
							title: '序号',
							width:50,
							align:'left'
						 },{
						    field: 'CREATOR',
							title: '发起人',
							align:'left',
							width:70,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['CREATOR']+'\')">'+row.CREATE_NAME+'</a>';
							}
						  },{
						    field: 'TASK_NAME',
							title: '任务名称',
							event:'list.myTaskDetail',
							minWidth:220,
							align:'left',
							style:'color:#1E9FFF;cursor:pointer',
							templet:function(row){
								var groupName = row['GROUP_NAME'];
								var fileCount = row['FILE_COUNT'];
								var id = row['P_TASK_ID'];
								var html = '';
								var fileDes = fileCount>0?getFileIcon('.file'):'';
								if(groupName){
									html = html + '【'+groupName+'】';
								}
								if(id){
									html = html + fileDes+"<i class='layui-icon layui-icon-senior'></i>"+row['TASK_NAME'];
								}else{
									html = html + fileDes+row['TASK_NAME'];
								}
								return html;
							}
						},{
						    field: 'PROJ_NAME',
							title: '项目名称',
							event:'list.projectDetail',
							align:'left',
							sort:true,
							minWidth:150
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							sort:true,
							width:80,
							event:'list.updateState',
							templet:function(row){
								var state = row.TASK_STATE;
								var progress = row.PROGRESS;
								if(state==20&&progress>0){
									return taskStateLabel(row.TASK_STATE,progress+"%");
								}else{
									return taskStateLabel(row.TASK_STATE);
								}
							}
						},{
							field:'PROGRESS',
							title:'进度',
							width:70,
							templet:'<div>{{d.PROGRESS}}%</div>'
						},{
						    field: 'TASK_TYPE_ID',
							title: '任务类型',
							align:'center',
							sort:true,
							width:80,
							templet:function(row){
								return getText(row['TASK_TYPE_ID'],'taskTypeId');
							}
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'center',
							sort:true,
							width:70,
							templet:function(row){
								return taskTextLevel(row.TASK_LEVEL);
							}
						},{
						    field: 'CREATE_TIME',
							title: '发起时间',
							align:'center',
							sort:true,
							width:90,
							templet:function(row){
								var time= row['CREATE_TIME'];
								return cutText(time,12,'');
							}
						},{
						    field: 'DEADLINE_AT',
							title: '截止时间',
							align:'left',
							sort:true,
							width:100,
							templet:function(row){
								var time= row['DEADLINE_AT'];
								var state= row['TASK_STATE'];
								if(state==10||state==12||state==20){
									return timeBetween(time);
								}
								return cutText(time,12,'');
							}
						}
					]],done:function(result){
						showFun();
						if(firstLoad){
							$(".ALL").text(result.totalRow);
						}
						firstLoad = false;
						table.on('sort(todoTaskTable)', function(obj){
							$("#searchForm").queryData({id:'todoTaskTable',jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
						});
				}});
			},
			query:function(){
				firstLoad = true;
				$("#searchForm").render();
				$("#searchForm").queryData({id:'todoTaskTable',jumpOne:true});
			},
			clearForm:function(){
				$("[name='projectId']").val('');
				$("[name='assignUserId']").val('');
				$("#searchForm")[0].reset();
				list.query();
			},
			edit:function(data){
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				var businessId = data['BUSINESS_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'编辑任务',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,businessId:businessId}});
			},
			add:function(){
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务'});
			},
			importExcel:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['550px','400px'],url:'${ctxPath}/pages/task/task-import.jsp',title:'导入任务'});
			},
			detail:function(data){
				var state = data['TASK_STATE'];
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				if(state==40){
					popup.openTab({id:"task_"+data.TASK_ID,type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-detail.jsp?isDiv=0',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:1}});
				}else{
					popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:1}});
				}
			},
			myTaskDetail:function(data){
				list.detail(data);
			},
			projectDetail:function(data){
				var projectId = data['PROJECT_ID'];
				if(projectId){
					projectBoard(projectId);
				}
			},
			share:function(data){
				ajax.remoteCall("${ctxPath}/servlet/task?action=share",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			},
			delTask:function(data){
				layer.confirm("是否确认删除?",{offset:'20px',icon:7},function(index){
					layer.close(index)
					ajax.remoteCall("${ctxPath}/servlet/task?action=delTask",{taskId:data['TASK_ID']},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								layer.closeAll();
								list.query();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			updateState:function(data){
				if(data['TASK_STATE']==20){
					//todo
				}
			},
			cannelShare:function(data){
				ajax.remoteCall("${ctxPath}/servlet/task?action=cannelShare",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		
		function reloadTaskList(source){
			$("#searchForm").queryData({id:'todoTaskTable',jumpOne:true});
		}
		
		function showFun(){
			$('.finishTask').show();
		}
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.initTable();
			}});
			
			layui.use('element',function(){
				var element = layui.element;
				element.on('tab(task)', function(elem){
					var state = $(this).data('state');
					$("#taskState").val(state);
					$("#searchForm").queryData({id:'todoTaskTable',jumpOne:true});
				});
			 });
		});
		
		
		function exportTask(){
			layer.msg('正在导出',{time:500});
			location.href = '${ctxPath}/servlet/task/ext?action=exportTask&data='+encodeURI(JSON.stringify(form.getJSONObject('#searchForm')));
		}
		
		function finishTask(dataList){
			if(dataList.length>0){
				layer.confirm('确认操作吗',{offset:'20px'},function(index){
			    	layer.close(index);
					for(var index in dataList){
						var taskObj = dataList[index];
						var taskState = taskObj['TASK_STATE'];
						if(taskState=='10'||taskState=='20'){
							var taskId = taskObj['TASK_ID'];
							var data = {};
							data['task.TASK_ID'] = taskId;
							data['ASSIGN_USER_ID'] = taskObj['ASSIGN_USER_ID'];
							data['CREATOR'] = taskObj['CREATOR'];
							data['TASK_NAME'] = taskObj['TASK_NAME'];
							data['workHour'] = taskObj['task.PLAN_WORK_HOUR'];
							data['TASK_STATE'] = taskObj['task.TASK_STATE'];;
							data['task.TASK_STATE'] = 30;
							
							ajax.remoteCall("${ctxPath}/servlet/task?action=updateState",data,function(result) { 
								if(result.state == 1){
									layer.msg('操作成功',{icon:1,time:1200});
									list.query();
								}else{
									layer.alert(result.msg,{icon: 5});
								}
							});
						}
					}
				});
			}else{
				layer.msg('请选择');
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>