<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>结算详情</title>
    <style>
        .form-horizontal {
            width: 100%;
        }
        .table-vzebra {
            width: 100%;
            table-layout: fixed;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="settleDetailForm" class="form-horizontal" data-mars="CustOperateDao.settleRecord" data-text-model="true" autocomplete="off" data-mars-prefix="settle.">
        <input type="hidden" value="${param.settlementId}" name="settle.SETTLEMENT_ID"/>
        <input type="hidden" value="${param.settlementId}" name="settlementId"/>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td style="width: 120px">结算编号</td>
                <td>
                    <input type="text" name="settle.SETTLEMENT_NO" class="form-control input-sm" readonly>
                </td>
                <td style="width: 130px">结算月份</td>
                <td>
                    <input type="text" name="settle.MONTH_ID" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>结算类型</td>
                <td>
                    <input type="text" name="settle.SETTLEMENT_TYPE" class="form-control input-sm" readonly>
                </td>
                <td>业务平台</td>
                <td>
                    <input type="text" name="settle.PLATFORM_NAME" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>合同名称</td>
                <td>
                    <input type="text" name="settle.CONTRACT_NAME" class="form-control input-sm" readonly>
                </td>
                <td>客户名称</td>
                <td>
                    <input type="text" name="settle.CUST_NAME" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>开票日期</td>
                <td>
                    <input type="text" name="settle.MARK_DATE" class="form-control input-sm" readonly>
                </td>
                <td>签订月份</td>
                <td><span id="signMonth"></span></td>
            </tr>
            <tr>
                <td>开票总额(含税)</td>
                <td>
                    <input type="text" name="settle.TOTAL_KP_AMOUNT" class="form-control input-sm" readonly>
                </td>
                <td>开票总额(不含税)</td>
                <td>
                    <input type="text" name="settle.TOTAL_KP_NO_TAX" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>成本总额(含税)</td>
                <td>
                    <input type="text" name="settle.TOTAL_COST_AMOUNT" class="form-control input-sm" readonly>
                </td>
                <td>成本总额(不含税)</td>
                <td>
                    <input type="text" name="settle.TOTAL_COST_NO_TAX" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>毛利(不含税)</td>
                <td>
                    <input type="text" name="settle.GROSS_PROFIT" class="form-control input-sm" readonly>
                </td>
                <td>毛利率(%)</td>
                <td>
                    <input type="text" name="settle.GROSS_PROFIT_RATE" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>回款状态</td>
                <td>
                    <span id="paymentStatusText"></span>
                </td>
                <td>已回款金额(含税)</td>
                <td>
                    <input type="text" name="settle.PAY_AMOUNT" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>最近回款时间</td>
                <td>
                    <input type="text" name="settle.PAYMENT_TIME" class="form-control input-sm" readonly>
                </td>
                <td>创建人</td>
                <td>
                    <input type="text" name="settle.CREATE_NAME" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>创建时间</td>
                <td>
                    <input type="text" name="settle.CREATE_TIME" class="form-control input-sm" readonly>
                </td>
                <td>更新时间</td>
                <td>
                    <input type="text" name="settle.UPDATE_TIME" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>关联成本</td>
                <td colspan="3">
                    <table id="settleCostTable"></table>
                </td>
            </tr>
            <tr>
                <td>关联发票</td>
                <td colspan="3">
                    <table id="settleInvoiceTable"></table>
                </td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <textarea name="settle.REMARK" class="form-control input-sm" rows="3" readonly></textarea>
                </td>
            </tr>
            </tbody>
        </table>
        <br>
        <p class="layer-foot text-c">
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll();"> 关闭</button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        $(function () {
            $("#settleDetailForm").render({
                success: function (result) {
                    var record = result && result['CustOperateDao.settleRecord'];
                    var data = record && record.data;
                    if(data){
                        var paymentStatus = data['PAYMENT_STATUS'];
                        var statusText = ['未回款','部分回款','已回款'][paymentStatus] || '未知';
                        $('#paymentStatusText').text(statusText);
                        var markDate = data['MARK_DATE'];
                        if(markDate && markDate.length >= 7){
                            $('#signMonth').text(markDate.substring(0,7));
                        }else{
                            $('#signMonth').text('');
                        }
                    }

                    settleCostList.init();

                    settleInvoiceList.init();
                }
            });
        });

        var settleCostList = {
            init: function () {
                $("#settleDetailForm").initTable({
                    mars: 'CustOperateDao.costList',
                    id: 'settleCostTable',
                    page: false,
                    data: {'costListBy': 'settlementId'},
                    cols: [[
                        {
                            field: 'COST_NAME',
                            title: '成本名称',
                            width: 150,
                            align: 'left'
                        },{
                            field: 'DATE_ID',
                            title: '日期',
                            width: 100,
                            align: 'center'
                        }, {
                            field: 'COST_TYPE',
                            title: '成本类型',
                            width: 100,
                            align: 'center'
                        }, {
                            field: 'TOTAL_AMOUNT',
                            title: '成本金额(含税)',
                            width: 110,
                            align: 'right'
                        }, {
                            field: 'NO_TAX_AMOUNT',
                            title: '成本金额(去税)',
                            width: 110,
                            align: 'right'
                        },
                        {
                            field: 'REMARK',
                            title: '备注',
                            minWidth: 80,
                            align: 'left'
                        }
                    ]]
                });
            }
        }

        var settleInvoiceList = {
            init: function () {
                $("#settleDetailForm").initTable({
                    mars: 'CustOperateDao.settleInvoiceList',
                    id: 'settleInvoiceTable',
                    page: false,
                    cols: [[
                        {
                            field: 'INVOICE_NO',
                            title: '发票编号',
                            width: 150,
                            align: 'left'
                        }, {
                            field: 'MARK_DATE',
                            title: '开票日期',
                            width: 100,
                            align: 'center'
                        }, {
                            field: 'INVOICE_TYPE',
                            title: '发票类型',
                            width: 100,
                            align: 'center'
                        }, {
                            field: 'MARK_MONEY',
                            title: '开票金额(含税)',
                            width: 110,
                            align: 'right'
                        }, {
                            field: 'AMOUNT_IN_FIGUERS',
                            title: '开票金额(去税)',
                            width: 110,
                            align: 'right'
                        },  {
                            field: 'REMARK',
                            title: '备注',
                            minWidth: 80,
                            align: 'left'
                        }
                    ]]
                });
            }
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
