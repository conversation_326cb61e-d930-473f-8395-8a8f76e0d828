<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  				<!-- <input type="hidden" name="apply.applyBy"/>
					  				<button class="btn btn-xs btn-default hidden-print pull-right edit-remove" onclick="applyQuestion(this)" type="button">+发起提问</button> -->
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">支持开始日期</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" data-laydate="{type:'date'}" data-mars="CommonDao.today" class="form-control input-sm Wdate" name="apply.data1"/>
					  			</td>
					  			<td class="required">支持结束日期</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">支持类型</td>
					  			<td>
					  				<input type="text" data-rules="required"  onclick="selectText(this)" placeholder="点击此选择" readonly="readonly" data-text="方案,报价,交流,应标,其它" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  			<td class="required">销售产品</td>
					  			<td>
					  				<input type="text" data-rules="required" onclick="multiDict(this,'Y012')" placeholder="点击此选择" readonly="readonly" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">客户名称</td>
					  			<td>
					  				<input type="hidden" name="apply.data14"/>
					  				<input type="text" data-rules="required" placeholder="点击选择"  id="flowCustName" class="form-control input-sm" name="apply.data5" onclick="singleCust(this);"/>
					  			</td>
					  			<td class="required">项目名称</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">客户行业</td>
					  			<td>
					  				<input type="text" data-rules="required" onclick="singleDict(this,'Y008')" readonly="readonly" class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  			<td class="required">解决方案部门</td>
					  			<td>
					  				<input type="hidden" id="deptLeaderName" name="extend.f1"/>
					  				<input type="hidden" id="deptLeader" name="extend.f2"/>
					  				<input type="text" data-rules="required" id="jjDeptName" class="form-control input-sm" readonly="readonly" placeholder="点击此选择" data-dept-name="解决" data-ref-dept-leader="deptLeader" data-ref-dept-leader-name="deptLeaderName" onclick="singleDept(this);" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">支持负责人</td>
					  			<td>
					  				<input type="hidden" name="apply.data10"/>
					  				<input type="text" data-rules="required" onclick="multiUser(this);" data-dept-name="解决" data-ref-update="prev" readonly="readonly" class="form-control input-sm" name="apply.data9"/>
					  				<button class="btn btn-xs btn-default hidden-print pull-right edit-remove" onclick="applyQuestion(this)" type="button">+发起提问</button>
					  			</td>
					  			<td class="required">关联商机</td>
					  			<td>
					  				<input type="hidden" name="apply.data13"/>
					  				<input type="text" data-rules="required" style="width: 200px;display: inline-block;"  class="form-control input-sm" data-ref-update="prev" readonly="readonly" placeholder="点击此选择"  onclick="singleSj(this);" name="apply.data11"/>
					  				<button class="btn btn-sm btn-default hidden-print edit-remove" onclick="autoCreateSj(this);" type="button">自动创建</button>
					  				<button class="pull-right btn btn-xs btn-info layui-hide-xs hidden-print sjDetail" style="display: none;" type="button" onclick="sjDetail(this)">详情</button>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">项目需求</td>
					  			<td colspan="3">
									<textarea style="height: 100px;" data-rules="required" placeholder="1、平台规模 &#10;2、建设方式 &#10;3、软硬件提供" onfocus="layer.tips($(this).attr('placeholder'),this,{tips:[1,'#5fb878'],time:3000});" class="form-control input-sm"  name="apply.data12"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>相关文件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript" src="${ctxPath}/static/js/bootstrap3-typeahead.min.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){
				var sjId = $("[name='apply.data13']").val();
				if(sjId){
					$('.sjDetail').show();
				}
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
			if(state=='submit'){
				var applyTitle = $("[name='apply.applyTitle']").val();
				var name = $("[name='apply.data6']").val();
				sendWxMsg('PRESLAES',applyTitle+'\n'+name);
				sendWxMsg('seftDept',applyTitle+'\n'+name);
			}
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		Flow.initData = function(){
			var selectUserName = $("#deptLeaderName").val();
			var selectUserId = $("#deptLeader").val();
			
			var staffInfo = getStaffInfo();
			var params = {selectUserName:selectUserName,selectUserId:selectUserId,ccNames:staffInfo.deptLeaderName,ccIds:staffInfo.deptLeader};
			FlowCore.approveData = params;
		}
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='部门主管'){
				Flow.initData();
			}
			if(nodeCode=='解决方案部门主管'){
				var data9 = $("[name='apply.data9']").val();
				var data10 = $("[name='apply.data10']").val();
				if(data9==''){
					layer.msg('请选择负责人');
					return false;
				}
				var params = {selectUserName:data9,selectUserId:data10};
				FlowCore.approveData = params;
			}
			if(nodeCode=='解决方案部门员工'){
				var data11 = $("[name='apply.data11']").val();
				if(data11==''){
					layer.msg('请选择商机');
					return false;
				}
			}
			return true;
	    }
		
		FlowCore.flowEnd =  function(){
			var applyInfo = FlowCore.applyInfo;
			var formData = form.getJSONObject("#flowForm");
			var array = [];
			var data = {};
			data.applyBy = applyInfo.applyBy;
			data.taskId = applyInfo.applyId;
			data.taskName = applyInfo.applyTitle;
			if(applyInfo.data13){
				data.businessId = applyInfo.data13;
			}else{
				data.businessId = $("[name='apply.data13']").val();
			}
			
			var assignUserIds = applyInfo.data10;
			
			data.projName = applyInfo.data6;
			//data.assignUserId = applyInfo.data10;
			data.taskDesc = applyInfo.data12+"<br>"+applyInfo.applyRemark;
			data.planStartedAt = applyInfo.data1+" 09:00";
			data.deadlineAt = applyInfo.data2+" 18:00";
			data.planWorkHour = dateDiffInDays(applyInfo.data1,applyInfo.data2)*8;
			data.projectId = '';
			
			var _array = assignUserIds.split(',');
			for(var index in _array){
				var _row = $.extend({},data,{assignUserId:_array[index]});
				array.push(_row);
			}
			
			ajax.remoteCall("${ctxPath}/servlet/task?action=addFlowTask",array,function(result) { 
				if(result.state == 1){
					
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});	
		}
		
		function sjDetail(el){
			var sjId = $("[name='apply.data13']").val();
			popup.openTab({id:'sjDetail',url:'/yq-work/pages/crm/contacts/contact-query.jsp',title:'商机详情',data:{businessId:sjId}});
		}
		
		function autoCreateSj(el){
			layer.confirm('确定自动创建商机吗',{icon:3},function(){
				var formData = form.getJSONObject("#flowForm");
				var applyInfo = FlowCore.applyInfo;
				
				var data = {};
				data['business.BUSINESS_NAME'] = applyInfo.data6;
				data['business.CUST_ID'] = applyInfo.data14;
				data['business.CUSTOMER_NAME'] = applyInfo.data5;
				data['business.BUSINESS_STAGE'] = 10;
				data['business.BUSINESS_RESULT'] = 0;
				data['business.AMOUNT'] = 0;
				data['business.POSSIBILITY'] = 60;
				data['business.BUSINESS_AREA'] = applyInfo.deptName;
				data['business.SALES_BY'] = applyInfo.applyBy;
				data['business.SALES_BY_NAME'] = applyInfo.applyName;
				data['business.REMARK'] = applyInfo.data12;
				
				ajax.remoteCall("${ctxPath}/servlet/cust?action=addBusiness",data,function(result) { 
					if(result.state == 1){
						$(el).prev().attr('data-update','1').val(formData['apply.data6']);
						$(el).prev().prev().attr('data-update','1').val(result.data);
						FlowCore.updateField();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		function dateDiffInDays(date1, date2) {  
			const oneDay = 24 * 60 * 60 * 1000; // 一天的毫秒数 
			const firstDate = new Date(date1); 
			const secondDate = new Date(date2); 
			// 将日期转换为毫秒数并计算差异  
			const diffDays = Math.round(Math.abs((firstDate - secondDate) / oneDay));  
			return diffDays;
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>