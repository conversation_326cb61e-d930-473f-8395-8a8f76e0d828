<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>项目首页</title>
	<style type="text/css">
		.role-job p{height: 30px;font-weight: 700;}
		.layui-badge-rim{height: 26px;line-height: 16px;padding: 4px 6px;margin-bottom: 5px;}
		.hover-click:hover{color: #409eff;cursor: pointer;}
		.teams-tips{display: none;}
		.contract-url{display: none;margin-left: 10px;color: red;}
		.console-div{margin-bottom: 100px;}
		.edit-btn{float: right;margin-top: 5px;color: #1b9aee;cursor: pointer;}
		.no-data{text-align: center;padding:30px;}
		.stat-item{width: 24%;display: inline-block;}
		.top-1{font-size: 24px;font-weight: 500;color: #22cd0f;text-align: center;}
		.top-2{font-size: 14px;color: #666;text-align: center;}
		.layui-text h3 {
		    font-size: 15px;
		    font-weight: 500;
		}
		pre{background-color: #fff;border: none;}
		.layui-card{
			min-width: 100px;
		    border: 1px solid #e9edf0;
		    border: 1px;
		    border-radius: 4px;
		    -webkit-box-shadow: none;
		    overflow: hidden;
		}
		
		.yc-step {
		    box-sizing: border-box;
		    margin: 0;
		    padding: 0;
		    color: #000000d9;
		    font-size: 14px;
		    font-variant: tabular-nums;
		    line-height: 1.5715;
		    list-style: none;
		    font-feature-settings: tnum;
		    display: flex;
		    width: 100%;
		    font-size: 0;
		    overflow:hidden;
		}
		
		.yc-step *{box-sizing: border-box}
		
		.yc-step-item {
		    position: relative;
		    display: inline-block;
		    flex: 1;
		    overflow: hidden;
		    vertical-align: top
		}
		
		.yc-step-item-container {
		    outline: none
		}
		
		.yc-step-item:last-child {
		    flex: none
		}
		
		.yc-step-item:last-child>.yc-step-item-container>.yc-step-item-tail,.yc-step-item:last-child>.yc-step-item-container>.yc-step-item-content>.yc-step-item-title:after {
		    display: none
		}
		
		.yc-step-item-icon,.yc-step-item-content {
		    display: inline-block;
		    vertical-align: top
		}
		
		.yc-step-item-icon {
		    width: 32px;
		    height: 32px;
		    margin-right: 8px;
		    font-size: 16px;
		    font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";
		    line-height: 32px;
		    text-align: center;
		    border: 1px solid rgba(0,0,0,.25);
		    border-radius: 32px;
		    transition: background-color .3s,border-color .3s
		}
		
		.yc-step-item-icon>.yc-step-icon {
		    position: relative;
		    top: -1px;
		    color: #1890ff;
		    line-height: 1
		}
		
		.yc-step-item-tail {
		    position: absolute;
		    top: 12px;
		    left: 0;
		    width: 100%;
		    padding: 0 10px
		}
		
		.yc-step-item-tail:after {
		    display: inline-block;
		    width: 100%;
		    height: 1px;
		    background: #f0f0f0;
		    border-radius: 1px;
		    transition: background .3s;
		    content: ""
		}
		
		.yc-step-item-title {
		    position: relative;
		    display: inline-block;
		    padding-right: 16px;
		    color: #000000d9;
		    font-size: 16px;
		    line-height: 32px
		}
		
		.yc-step-item-title:after {
		    position: absolute;
		    top: 16px;
		    left: 100%;
		    display: block;
		    width: 9999px;
		    height: 1px;
		    background: #f0f0f0;
		    content: ""
		}
		
		.yc-step-item-subtitle {
		    display: inline;
		    margin-left: 8px;
		    color: #00000073;
		    font-weight: 400;
		    font-size: 14px
		}
		
		.yc-step-item-desc {
		    color: #00000073;
		    font-size: 14px
		}
		
		
		.yc-step-label-vertical .yc-step-item {
		    overflow: visible;
		}
		
		
		.yc-step-label-vertical .yc-step-item {
		    overflow: visible
		}
		
		.yc-step-label-vertical .yc-step-item-tail {
		    margin-left: 80px;
		    padding: 3.5px 24px
		}
		
		.yc-step-label-vertical .yc-step-item-content {
		    display: block;
		    width: 170px;
		    margin-top: 8px;
		    text-align: center
		}
		
		.yc-step-label-vertical .yc-step-item-icon {
		    display: inline-block;
		    margin-left: 65px
		}
		
		.yc-step-label-vertical .yc-step-item-title {
		    padding-right: 0
		}
		
		.yc-step-label-vertical .yc-step-item-title:after {
		    display: none
		}
		
		
		.yc-step-item-container>.yc-step-item-tail:after {
		    background-color: #1890ff;
		    width: calc(100% - 20px);
		    height: 1px;
		    margin-left: 12px;
		}
		
		 .yc-step-item-active .yc-step-item-tail:after,.yc-step-item-active ~ .yc-step-item .yc-step-item-tail:after{
			background-color: #f0f0f0;
		}
		
		.yc-step-item-icon {
		    background-color: #fff;
		    border-color: #1890ff;
		    overflow: hidden;
		}
		
		.yc-step-item-icon img{
			max-height: 100%;
			max-width: 100%;
		}
		
		.yc-step-item-active .yc-step-item-icon {
		    background-color: #1890ff;
		    border-color: #1890ff;
		    color: #fff; 
		}
		
		
		.yc-step-item-active ~ .yc-step-item .yc-step-item-icon {
		    background-color: #fff;
		    border-color: #00000040;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
  	<form id="searchForm" data-mars="ProjectDao.record">
  		<input name="project.PROJECT_ID" type="hidden" value="${param.projectId}">
  		<input name="isAdmin" type="hidden" value="${isAdmin}">
  		<input name="projectId" type="hidden" value="${param.projectId}">
  		<div class="layui-row layui-col-space10 console-div">
  			<div class="layui-col-md7">
	  	        <div class="layui-card">
		  	   	 <div class="layui-card-header">项目概括</div>
	    		 <div class="layui-card-body" style="min-height: 200px;">
		  		 	<table class="layui-table">
		  		 		<tr>
		  		 			<td style="width: 120px;">名称</td>
		  		 			<td>${projectInfo.PROJECT_NAME }</td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>编号</td>
		  		 			<td><span>${projectInfo.PROJECT_NO}</span> 
		  		 				<a class="contract-url" href="/yq-work/project/contract/${projectInfo.CONTRACT_ID}">查看合同</a>
		  		 			</td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>项目铁三角</td>
		  		 			<td>
		  		 				项目经理：${projectInfo.PROJECT_PO_NAME }，
		  		 				商务：<span id="custSales">--</span>，
		  		 				开发：${projectInfo.PO_NAME }
		  		 				<button class="btn btn-xs btn-info ml-10 layui-hide" onclick="addHy();" type="button">+定期会议</button>
		  		 				<a class="contract-url layui-hide" href="javascript:;" onclick="L2cView(this);">查看关联L2C流程</a>
		  		 			</td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>解决方案</td>
		  		 			<td><span id="preSales">--</span></td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>签订时间</td>
		  		 			<td>${projectInfo.SIGN_DATE }</td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>创建时间</td>
		  		 			<td>${projectInfo.CREATE_TIME }</td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>考核日期</td>
		  		 			<td>${projectInfo.KH_DATE }</td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>项目类型</td>
		  		 			<td id="projectType">${projectInfo.PROJECT_TYPE}</td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>项目进度</td>
		  		 			<td id="projectState">${projectInfo.PROJECT_STATE}</td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>项目阶段</td>
		  		 			<td>${projectInfo.PROJECT_STAGE}</td>
		  		 		</tr>
		  		 		<tr>
		  		 			<td>公开性</td>
		  		 			<td>
		  		 				<c:choose>
		  		 					<c:when test="${projectInfo.PROJECT_PUBLIC=='1'}">私密·仅成员可访问</c:when>
		  		 					<c:otherwise>公开·企业全员可访问</c:otherwise>
		  		 				</c:choose>
		  		 			</td>
		  		 		</tr>
		  		 	</table>
		  		 </div>
	  		    </div>
	  		    <div class="layui-card risk-card">
		  	   	   <div class="layui-card-header">风险</div>
	    		   <div class="layui-card-body" style="min-height: 50px;">
	    		   		<div class="layui-timeline riskList"></div>
	    		   </div>
	  		   </div>
	  		   <div class="layui-card">
		  	   	   <div class="layui-card-header">验收计划</div>
	    		   <div class="layui-card-body">
	    		   		<div class="yc-step yc-step-label-vertical">
						    <div class="yc-step-item">
						        <div class="yc-step-item-container">
						            <div class="yc-step-item-tail"></div>
						            <div class="yc-step-item-icon">1</div>
						            <div class="yc-step-item-content">
						                <div class="yc-step-item-title">项目开工</div>
						                <div class="yc-step-item-desc begin-date">${projectInfo.BEGIN_DATE}</div>
						            </div>
						        </div>
						    </div>
						    <div class="yc-step-item">
						        <div class="yc-step-item-container">
						            <div class="yc-step-item-tail"></div>
						            <div class="yc-step-item-icon">2</div>
						            <div class="yc-step-item-content">
						                <div class="yc-step-item-title">项目上线</div>
						                <div class="yc-step-item-desc sx-date">${projectInfo.SX_DATE}</div>
						            </div>
						        </div>
						    </div>
					
						    <div class="yc-step-item ">
						        <div class="yc-step-item-container">
						            <div class="yc-step-item-tail"></div>
						            <div class="yc-step-item-icon">3</div>
						            <div class="yc-step-item-content">
						                <div class="yc-step-item-title">项目初验</div>
						                <div class="yc-step-item-desc cy-date">${projectInfo.CY_DATE}</div>
						            </div>
						        </div>
						    </div>
					
						    <div class="yc-step-item">
						        <div class="yc-step-item-container">
						            <div class="yc-step-item-tail"></div>
						            <div class="yc-step-item-icon">4</div>
						            <div class="yc-step-item-content">
						                <div class="yc-step-item-title">项目终验</div>
						                <div class="yc-step-item-desc zy-date" data-r="${projectInfo.ZY_REAL_DATE}">${projectInfo.ZY_DATE} ${projectInfo.ZY_REAL_DATE }
						                </div>
						            </div>
						        </div>
						    </div>
						</div>
	    		   </div>
	  		   </div>
	  		   <div class="layui-card">
		  	   	   <div class="layui-card-header">项目计划</div>
	    		   <div class="layui-card-body" style="min-height: 150px;max-height: 250px;overflow-y: scroll;">
	    		   		<div class="layui-timeline plan"></div>
	  		   	   </div>
	  		   </div>
	  		   <div class="layui-card">
		  	   	   <div class="layui-card-header">动态
		  	   	   		<a onclick="lookLog()" class="pull-right" href="javascript:;">访问记录</a>
		  	   	   </div>
	    		   <div class="layui-card-body">
	    		   		<div style="padding-bottom: 10px;min-height: 250px;max-height: 500px;overflow-y: scroll;">
		    		   		<div class="layui-timeline action"></div>
	    		   		</div>
	    		   </div>
	  		   </div>
	       </div>
	       <div class="layui-col-md5">
	  		   <div class="layui-card" data-mars="TaskDao.taskCountStat">
		  	   	   <div class="layui-card-header">统计</div>
	    		   <div class="layui-card-body" style="min-height: 150px;" data-mars="ProjectDataDao.consoleIndexStat">
	    		   		<div class="stat-item">
			    			<div class="top-1" id="A">0</div>
		    				<div class="top-2">待办任务</div>
		    			</div>
	    		   		<div class="stat-item">
			    			<div class="top-1" id="E">0</div>
		    				<div class="top-2">超时任务</div>
		    			</div>
	    		   		<div class="stat-item">
			    			<div class="top-1" id="B">0</div>
		    				<div class="top-2">进行中任务</div>
		    			</div>
	    		   		<div class="stat-item">
			    			<div class="top-1" id="C">0</div>
		    				<div class="top-2">已完成任务</div>
		    			</div>
		    			<hr>
	    		   		<div class="stat-item">
			    			<div class="top-1" id="riskCount">0</div>
		    				<div class="top-2">风险</div>
		    			</div>
	    		   		<div class="stat-item">
			    			<div class="top-1" id="weeklyCount">0</div>
		    				<div class="top-2">周报</div>
		    			</div>
	    		   		<div class="stat-item">
			    			<div class="top-1">${projectInfo.BUG_COUNT }</div>
		    				<div class="top-2">禅道BUG</div>
		    			</div>
	    		   		<!-- <div class="stat-item">
			    			<div class="top-1" id="fileCount">0</div>
		    				<div class="top-2">文档</div>
		    			</div> -->
	    		   		<div class="stat-item">
			    			<div class="top-1" id="versionCount">0</div>
		    				<div class="top-2">版本</div>
		    			</div>
	    		   </div>
	  		   </div>
	  		   <div class="layui-card">
		  	   	   <div class="layui-card-header">备注栏
		  	   	   	 <a class="edit-btn mt-10" href="javascript:;" onclick="editNotice()">编辑</a>
		  	   	   </div>
	    		   <div class="layui-card-body notice-content" style="min-height: 30px;max-height: 400px;width: 100%;overflow-y: scroll;margin-bottom: 10px;">
	    		 		<c:choose>
	    		 			<c:when test="${!empty projectInfo.PROJECT_NOTICE}">
			    		 		<pre>${projectInfo.PROJECT_NOTICE}</pre>
	    		 			</c:when>
	    		 			<c:otherwise>
	    		 				<p class="no-data">暂无设置</p>
	    		 			</c:otherwise>
	    		 		</c:choose>
		  		 </div>
	  		   </div>
	  		   <div class="layui-card">
		  	   	   <div class="layui-card-header">反馈记录</div>
	    		   <div class="layui-card-body" style="min-height: 50px;">
	    		   		<div class="layui-timeline project-state-log"></div>
	    		   </div>
	  		   </div>
	  		   <div class="layui-card">
		  	   	   <div class="layui-card-header">成员</div>
	    		   <div class="layui-card-body teams" style="min-height: 150px;">
	    		   		<div class="no-data teams-tips">请添加成员</div>
	    		   </div>
	  		   </div>
	       </div>
  		</div>
  	</form>
  	<script id="plan-list" type="text/x-jsrender">
		{{for data}}
			<div class="layui-timeline-item">
			   <i class="layui-icon layui-timeline-axis layui-icon-circle"></i>
				<div class="layui-timeline-content layui-text">
				 <div class="layui-timeline-title" {{if PLAN_STATE=='9'}}style="text-decoration:line-through;"{{/if}}>{{:PLAN_BEGIN_DATE}}{{if PLAN_FINISH_DATE}} ~ {{/if}} {{if PLAN_STATE!=9}} {{call:PLAN_FINISH_DATE fn='dateDifference'}} {{else}} {{:PLAN_FINISH_DATE}} {{/if}}，{{:PLAN_NAME}} - {{:PLAN_PERSON_NAME}}
					{{if PLAN_PROCESS>0}} ，进度 {{:PLAN_PROCESS}}%{{/if}}
					{{if PLAN_STATE > 0}} ，{{if PLAN_STATE=='5'}}进行中{{else PLAN_STATE=='9'}}已完成{{else PLAN_STATE=='1'}}待开始{{/if}}{{/if}}
				 </div>
			   </div>
			</div>
	    {{/for}}
		{{if data.length==0}} <div class="no-data">暂无记录</div> {{/if}}
	 </script>
  	<script id="action-list" type="text/x-jsrender">
		{{for data}}
			 <div class="layui-timeline-item">
    			<i class="layui-icon layui-timeline-axis"></i>
    			<div class="layui-timeline-content layui-text">
      				<h3 class="layui-timeline-title">{{:CREATE_NAME}} # {{call:CREATE_TIME fn='dateShow'}}</h3>
					<p>{{if TYPE=='task'}}<span class="layui-badge mr-5">创建任务</span>{{else TYPE=='weekly'}}<span class="layui-badge layui-bg-green mr-5">周报</span>{{else TYPE=='comment'}}<span class="layui-badge layui-bg-blue mr-5">评论</span>{{/if}} <a href="javascript:;" onclick="openContent('{{:ID}}','{{:TYPE}}')">{{cutText:CONTENT 250}}</a></p>
  				</div>
  			</div>
	    {{/for}}
		{{if data.length==0}} <div class="no-data">暂无记录</div> {{/if}}
	 </script>
  	<script id="state-log-list" type="text/x-jsrender">
		{{for data}}
			 <div class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis {{if LEVEL=='紧急'}}layui-icon-face-cry{{else}}layui-icon-face-smile{{/if}}"></i>
				<div class="layui-timeline-content layui-text">
					<div class="layui-timeline-title"><span style="color:#999999;">{{:CREATE_NAME}} {{:UPDATE_TIME}}</span> <span> {{cutText:FEEDBACK 200}} </span> <span class="label label-info label-outline"> {{:FEEDBACK_TYPE}}</span></div>
				</div>
			</div>
	    {{/for}}
		{{if data.length==0}} <div class="no-data">暂无记录</div> {{/if}}
	 </script>
  	<script id="risk-list" type="text/x-jsrender">
		{{for data}}
			 <div class="layui-timeline-item">
				<i class="layui-icon layui-timeline-axis layui-icon-fire"></i>
				<div class="layui-timeline-content layui-text">
					<div class="layui-timeline-title"><span style="color:#999999;">{{:CREATOR_NAME}} {{:CREATE_TIME}}</span> <span class="hover-click" onclick="riskDetail('{{:RISK_ID}}','{{:RISK_STATE}}')" {{if RISK_STATE=='1'}}style="text-decoration:line-through;"{{/if}}> {{cutText:RISK_DESC 120}} </span></div>
				</div>
			</div>
	    {{/for}}
		{{if data.length==0}} <div class="no-data">暂无风险</div> {{/if}}
	 </script>
</EasyTag:override>
<EasyTag:override name="script">
	<script>
		
		var projectId = '${param.projectId}';
	
	 	$(function(){
	 		$("#searchForm").render({data:{isSelfDept:0},success:function(result){
	 			ysPlan(result);
	 			queryContractInfo();
		 		loadTeams();
		 		loadPlan();
		 		loadAction();
		 		riskList();
		 		projectStateLog();
		 		loadProjectInfo();
		 		renderStepDesc();
	 		}});
	 	});
	 	
	 	function ysPlan(result){
	 		var data = result['ProjectDao.record'].data;
	 		$('.begin-date').html(getFirstNonEmpty(data['BEGIN_DATE'],'','未填写'));
	 		$('.sx-date').html(getFirstNonEmpty(data['SX_DATE'],'','未填写'));
			if(data['SX_DATE']){
		 		$('.sx-date').parents('.yc-step-item').addClass('yc-step-item-active');
	 		}
	 		$('.cy-date').html(getFirstNonEmpty(data['CY_DATE'],data['CY_REAL_DATE'],'未填写'));
	 		if(data['CY_REAL_DATE']){
		 		$('.cy-date').parents('.yc-step-item').addClass('yc-step-item-active');
	 		}
	 		$('.zy-date').html(getFirstNonEmpty(data['ZY_DATE'],data['ZY_REAL_DATE'],'未填写'));
	 		if(data['ZY_REAL_DATE']){
		 		$('.zy-date').parents('.yc-step-item').addClass('yc-step-item-active');
	 		}
			if($('.yc-step-item-active').length==0){
				$('.begin-date').parents('.yc-step-item').addClass('yc-step-item-active');
			}
	 	}
	 	
	 	
	 	function dateDifference(targetDate) {
	 		 // 验证日期格式是否正确
	 		  var datePattern = /^\d{4}-\d{2}-\d{2}$/;
	 		  if (!datePattern.test(targetDate)) {
	 		    return targetDate;
	 		  }

	 		  var today = new Date(); // 获取当前日期
	 		  var target = new Date(targetDate); // 将目标日期转换为 Date 对象

	 		  // 检查转换后的日期是否有效
	 		  if (isNaN(target.getTime())) {
	 		    return targetDate;
	 		  }

	 		  // 计算两个日期之间的时间差（以毫秒为单位）
	 		  var difference = target - today;

	 		  // 将时间差转换为天数（取整）
	 		  var daysDifference = Math.floor(difference / (1000 * 60 * 60 * 24));

	 		  if (daysDifference > 0) {
	 		    return targetDate+"/倒计时" + daysDifference + "天";
	 		  } else if (daysDifference < 0) {
	 		    return "<span style='color:#4298fd;'>"+targetDate+"/已过去" + Math.abs(daysDifference) + "天</span>";
	 		  } else {
	 		    return "今天";
	 		  }
 		}
	 	
	 	function getFirstNonEmpty(param1, param2, param3) {
	 	    if (param2 !== null && param2 !== undefined && param2 !== '') {
	 	    	  return '[完成]'+dateDifference(param2);
	 	    }else if (param1 !== null && param1 !== undefined && param1 !== '') {
	 	        return dateDifference(param1);
	 	    }  else {
	 	    	  return dateDifference(param3);
	 	    }
	 	}
	 	
	 	function loadProjectInfo(){
	 		getProjectInfo(projectId,function(data){
	 			var _projectType = data['PROJECT_TYPE'];
	 			var _projectState = data['PROJECT_STATE'];
	 			$('#projectState').text(projectState(_projectState));
	 			$('#projectType').text(projectTypeText(_projectType));
	 		});
	 		
	 		if('${isAdmin}'=='1'){
	 			$('.contract-url').show();
	 		}
	 	}
	 	
	 	function projectTypeText(state){
	 		var json = {10:'合同项目',11:'提前执行',20:'自研项目',30:'维保项目'};
	 		var result=json[state]||'';
	 		if(result){
	 			return result;
	 		}else{
	 			return '';
	 		}
	 	}
	 	
	 	function loadTeams(){
	 		ajax.remoteCall("/yq-work/webcall?action=ProjectDao.projectTeams",{pageType:1,pageIndex:1,pageSize:100,projectId:projectId},function(result){
	 			var list = result.data;
	 			for(var index in list){
	 				if(list[index]['ADMIN_FLAG']=='1'){
		 				$('.teams').append('<span class="layui-badge mr-5">'+list[index]['USERNAME']+'<span>');
	 				}else{
		 				$('.teams').append('<span class="layui-badge layui-bg-gray mr-5">'+list[index]['USERNAME']+'<span>');
	 				}
	 			}
	 			if(list.length==0){
	 				$('.teams-tips').show();
	 			}
	 		});
	 	}
	 	
	 	function editNotice(){
	 		if(top.isAdmin=='1'||isSuperUser){
	 		  getProjectInfo(projectId,function(data){
		 		  var text = data['PROJECT_NOTICE'];
		 		  layer.prompt({formType: 2,value: text,offset:'20px',maxlength:50000,shade:0,title: '请输入备忘栏内容',area: ['550px', '350px']}, function(content, index, elem){
			    	 	layer.close(index);
			    		var data = {content:content,projectId:projectId};
			    	  	ajax.remoteCall("${ctxPath}/servlet/project/conf?action=editNotice",data,function(result) { 
							if(result.state == 1){
								layer.msg(result.msg,{icon:1,time:800},function(){
		    						$('.notice-content pre').html(content);
								})
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});	    	  	
			    	});
		 	  });
	 		}else{
	 			layer.msg("您无权操作!");
	 		}
	 	}
	 	
	 	function loadPlan(){
	 		ajax.remoteCall("/yq-work/webcall?action=ProjectDataDao.projectPlanList",{projectId:projectId},function(result){
	 			var list = result.data;
	 			var tmp = $.templates('#plan-list');
	 			var html = tmp.render({data:list});
	 			$('.plan').html(html);
	 		});
	 	}
	 	
	 	function loadAction(){
	 		ajax.remoteCall("/yq-work/webcall?action=ProjectDataDao.projectAction",{projectId:projectId},function(result){
	 			var list = result.data;
	 			var tmp = $.templates('#action-list');
	 			var html = tmp.render({data:list});
	 			$('.action').html(html);
	 		});
	 	}
	 	
	 	function projectStateLog(){
	 		ajax.remoteCall("/yq-work/webcall?action=ProjectDataDao.projectStateLog",{projectId:projectId},function(result){
	 			var list = result.data;
	 			var tmp = $.templates('#state-log-list');
	 			var html = tmp.render({data:list});
	 			$('.project-state-log').html(html);
	 		});
	 	}
	 	
	 	function riskList(){
	 		ajax.remoteCall("/yq-work/webcall?action=ProjectDataDao.projectRiskList",{projectId:projectId},function(result){
	 			var list = result.data;
	 			var tmp = $.templates('#risk-list');
	 			var html = tmp.render({data:list});
	 			$('.riskList').html(html);
	 			if(list.length==0){
	 			  $('.risk-card').hide();
	 			}
	 		});
	 	}
	 	
	 	function openContent(id,type){
	 		if(type=='task'||type=='comment'){
				window.open('/yq-work/task/'+id);
			}else if(type=='weekly'){
				window.open('/yq-work/weekly/'+id);
			}else if(type=='project'){
				projectBoard(id);
			}
	 	}
	 	
		function dateShow(time){
	  		var result = layui.util.timeAgo(time, true);
	  		return result;
	  	}
		
		function queryContractInfo(){
			var contractId = '${projectInfo.CONTRACT_ID}';
			if(contractId){
				ajax.remoteCall("/yq-work/webcall?action=ProjectContractDao.record",{contractId:contractId},function(result) { 
					var data = result.data;
					if(data){
						$('#custSales').text(data['SALES_BY_NAME']);
						$('#preSales').text(data['PM_BY_NAME']);
					}
				});
			}
		}
		
		function renderStepDesc(){
			$('.yc-step-item-desc').each(function(){
				var text = $(this).text()
				if(text==''){
					$(this).text('未定');
				}
			});
		}
		
		function L2cView(){
			popup.layerShow({url:'/yq-work/pages/project/module/L2C.jsp',title:'L2C流程管理',id:'L2cView',shade:0,area:['500px','500px']});
		}
		
		function riskDetail(riskId,riskState){
	        popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'风险详情',offset:'r',area:['700px','100%'],url:'${ctxPath}/pages/project/risk/risk-detail.jsp',title:'风险详情',data:{riskId:riskId,riskState:riskState,isAdmin:0}});
		}
		
		function addHy(){
			popup.layerShow({url:'/yq-work/pages/project/module/meet.jsp',title:'定期会议',id:'meet',shade:0,area:['500px','500px']});
		}
		
		function lookLog(){
			var contractId = '${projectInfo.CONTRACT_ID}';
			lookLogLayer(contractId);
		}
		
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>