/*!
 * WeUI v2.0 (https://github.com/weui/weui)
 */
html{
  -ms-text-size-adjust:100%;
  -webkit-text-size-adjust:100%;
  font-size:16px;
}
body{
  line-height:1.6;
  font-size:16px;
  font-family:-apple-system-font, "Helvetica Neue", sans-serif;
}
*{
  margin:0;
  padding:0;
}
a img{
  border:0;
}
a{
  text-decoration:none;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
@font-face{
  font-weight:normal;
  font-style:normal;
  font-family:"weui";
  src:url('data:application/octet-stream;base64,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') format('truetype');
}
[class^="weui-icon-"],
[class*=" weui-icon-"]{
  display:inline-block;
  vertical-align:middle;
  font:normal normal normal 14px/1 "weui";
  font-size:inherit;
  text-rendering:auto;
  -webkit-font-smoothing:antialiased;
}
[class^="weui-icon-"]:before,
[class*=" weui-icon-"]:before{
  display:inline-block;
  margin-left:.2em;
  margin-right:.2em;
}
.weui-icon-circle:before{
  content:"\EA01";
}
.weui-icon-download:before{
  content:"\EA02";
}
.weui-icon-info:before{
  content:"\EA03";
}
.weui-icon-safe-success:before{
  content:"\EA04";
}
.weui-icon-safe-warn:before{
  content:"\EA05";
}
.weui-icon-success:before{
  content:"\EA06";
}
.weui-icon-success-circle:before{
  content:"\EA07";
}
.weui-icon-success-no-circle:before{
  content:"\EA08";
}
.weui-icon-waiting:before{
  content:"\EA09";
}
.weui-icon-waiting-circle:before{
  content:"\EA0A";
}
.weui-icon-warn:before{
  content:"\EA0B";
}
.weui-icon-info-circle:before{
  content:"\EA0C";
}
.weui-icon-cancel:before{
  content:"\EA0D";
}
.weui-icon-search:before{
  content:"\EA0E";
}
.weui-icon-clear:before{
  content:"\EA0F";
}
.weui-icon-back:before{
  content:"\EA10";
}
.weui-icon-delete:before{
  content:"\EA11";
}
[class^="weui-icon_"]:before,
[class*=" weui-icon_"]:before{
  margin:0;
}
.weui-icon-success{
  font-size:23px;
  color:#07C160;
}
.weui-icon-waiting{
  font-size:23px;
  color:#10AEff;
}
.weui-icon-warn{
  font-size:23px;
  color:#FA5151;
}
.weui-icon-info{
  font-size:23px;
  color:#10AEff;
}
.weui-icon-success-circle{
  font-size:23px;
  color:#07C160;
}
.weui-icon-success-no-circle{
  font-size:23px;
  color:#07C160;
}
.weui-icon-waiting-circle{
  font-size:23px;
  color:#10AEff;
}
.weui-icon-circle{
  font-size:23px;
  color:#C9C9C9;
}
.weui-icon-download{
  font-size:23px;
  color:#07C160;
}
.weui-icon-info-circle{
  font-size:23px;
  color:#07C160;
}
.weui-icon-safe-success{
  color:#07C160;
}
.weui-icon-safe-warn{
  color:#FFBE00;
}
.weui-icon-cancel{
  color:#FA5151;
  font-size:22px;
}
.weui-icon-search{
  color:#B2B2B2;
  font-size:14px;
}
.weui-icon-clear{
  color:#B2B2B2;
  font-size:14px;
}
.weui-icon-delete.weui-icon_gallery-delete{
  color:#FFFFFF;
  font-size:22px;
}
.weui-icon_msg{
  font-size:93px;
}
.weui-icon_msg.weui-icon-warn{
  color:#FA5151;
}
.weui-icon_msg-primary{
  font-size:93px;
}
.weui-icon_msg-primary.weui-icon-warn{
  color:#FFBE00;
}
.weui-btn{
  position:relative;
  display:block;
  margin-left:auto;
  margin-right:auto;
  padding-left:14px;
  padding-right:14px;
  box-sizing:border-box;
  font-size:18px;
  text-align:center;
  text-decoration:none;
  color:#FFFFFF;
  line-height:2.55555556;
  border-radius:5px;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  overflow:hidden;
}
.weui-btn:after{
  content:" ";
  width:200%;
  height:200%;
  position:absolute;
  top:0;
  left:0;
  border:1px solid rgba(0, 0, 0, 0.2);
  -webkit-transform:scale(0.5);
          transform:scale(0.5);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  box-sizing:border-box;
  border-radius:10px;
}
.weui-btn_inline{
  display:inline-block;
}
.weui-btn_default{
  color:#000000;
  background-color:#F8F8F8;
}
.weui-btn_default:not(.weui-btn_disabled):visited{
  color:#000000;
}
.weui-btn_default:not(.weui-btn_disabled):active{
  color:rgba(0, 0, 0, 0.6);
  background-color:#DEDEDE;
}
.weui-btn_primary{
  background-color:#07c160;
}
.weui-btn_primary:not(.weui-btn_disabled):visited{
  color:#FFFFFF;
}
.weui-btn_primary:not(.weui-btn_disabled):active{
  color:rgba(255, 255, 255, 0.6);
  background-color:#06ad56;
}
.weui-btn_warn{
  background-color:#FA5151;
}
.weui-btn_warn:not(.weui-btn_disabled):visited{
  color:#FFFFFF;
}
.weui-btn_warn:not(.weui-btn_disabled):active{
  color:rgba(255, 255, 255, 0.6);
  background-color:#CE3C39;
}
.weui-btn_disabled{
  color:rgba(255, 255, 255, 0.6);
}
.weui-btn_disabled.weui-btn_default{
  color:rgba(0, 0, 0, 0.3);
  background-color:#F7F7F7;
}
.weui-btn_disabled.weui-btn_primary{
  background-color:#9ED99D;
}
.weui-btn_disabled.weui-btn_warn{
  background-color:#EC8B89;
}
.weui-btn_loading .weui-loading{
  margin:-0.2em 0.34em 0 0;
}
.weui-btn_loading.weui-btn_primary,
.weui-btn_loading.weui-btn_warn{
  color:rgba(255, 255, 255, 0.6);
}
.weui-btn_loading.weui-btn_primary{
  background-color:#06ad56;
}
.weui-btn_loading.weui-btn_warn{
  background-color:#CE3C39;
}
.weui-btn_plain-primary{
  color:#07c160;
  border:1px solid #07c160;
}
.weui-btn_plain-primary:not(.weui-btn_plain-disabled):active{
  color:rgba(26, 173, 25, 0.6);
  border-color:rgba(26, 173, 25, 0.6);
}
.weui-btn_plain-primary:after{
  border-width:0;
}
.weui-btn_plain-default{
  color:#353535;
  border:1px solid #353535;
}
.weui-btn_plain-default:not(.weui-btn_plain-disabled):active{
  color:rgba(53, 53, 53, 0.6);
  border-color:rgba(53, 53, 53, 0.6);
}
.weui-btn_plain-default:after{
  border-width:0;
}
.weui-btn_plain-disabled{
  color:rgba(0, 0, 0, 0.2);
  border-color:rgba(0, 0, 0, 0.2);
}
button.weui-btn,
input.weui-btn{
  width:100%;
  border-width:0;
  outline:0;
  -webkit-appearance:none;
}
button.weui-btn:focus,
input.weui-btn:focus{
  outline:0;
}
button.weui-btn_inline,
input.weui-btn_inline,
button.weui-btn_mini,
input.weui-btn_mini{
  width:auto;
}
button.weui-btn_plain-primary,
input.weui-btn_plain-primary,
button.weui-btn_plain-default,
input.weui-btn_plain-default{
  border-width:1px;
  background-color:transparent;
}
.weui-btn_mini{
  display:inline-block;
  padding:0 1.32em;
  line-height:2.3;
  font-size:13px;
}
.weui-btn + .weui-btn{
  margin-top:15px;
}
.weui-btn.weui-btn_inline + .weui-btn.weui-btn_inline{
  margin-top:auto;
  margin-left:15px;
}
.weui-btn-area{
  margin:1.17647059em 15px 0.3em;
}
.weui-btn-area_inline{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.weui-btn-area_inline .weui-btn{
  margin-top:auto;
  margin-right:15px;
  width:100%;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-btn-area_inline .weui-btn:last-child{
  margin-right:0;
}
.weui-cells{
  margin-top:1.17647059em;
  background-color:#FFFFFF;
  line-height:1.47058824;
  font-size:17px;
  overflow:hidden;
  position:relative;
}
.weui-cells:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #e5e5e5;
  color:#e5e5e5;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  z-index:2;
}
.weui-cells:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #e5e5e5;
  color:#e5e5e5;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  z-index:2;
}
.weui-cells__title{
  margin-top:.77em;
  margin-bottom:.3em;
  padding-left:15px;
  padding-right:15px;
  color:#999999;
  font-size:14px;
}
.weui-cells__title + .weui-cells{
  margin-top:0;
}
.weui-cells__tips{
  margin-top:.3em;
  color:#999999;
  padding-left:15px;
  padding-right:15px;
  font-size:14px;
}
.weui-cell{
  padding:10px 15px;
  position:relative;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-cell:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #e5e5e5;
  color:#e5e5e5;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  left:15px;
  z-index:2;
}
.weui-cell:first-child:before{
  display:none;
}
.weui-cell_primary{
  -webkit-box-align:start;
  -webkit-align-items:flex-start;
          align-items:flex-start;
}
.weui-cell__bd{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-cell__ft{
  text-align:right;
  color:#999999;
}
.weui-cell_swiped{
  display:block;
  padding:0;
}
.weui-cell_swiped > .weui-cell__bd{
  position:relative;
  z-index:1;
  background-color:#FFFFFF;
}
.weui-cell_swiped > .weui-cell__ft{
  position:absolute;
  right:0;
  top:0;
  bottom:0;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  color:#FFFFFF;
}
.weui-swiped-btn{
  display:block;
  padding:10px 1em;
  line-height:1.47058824;
  color:inherit;
}
.weui-swiped-btn_default{
  background-color:#C7C7CC;
}
.weui-swiped-btn_warn{
  background-color:#FF3B30;
}
.weui-cell_access{
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  color:inherit;
}
.weui-cell_access:active{
  background-color:#ECECEC;
}
.weui-cell_access .weui-cell__ft{
  padding-right:13px;
  position:relative;
}
.weui-cell_access .weui-cell__ft:after{
  content:" ";
  display:inline-block;
  height:6px;
  width:6px;
  border-width:2px 2px 0 0;
  border-color:#C8C8CD;
  border-style:solid;
  -webkit-transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
          transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position:relative;
  top:-2px;
  position:absolute;
  top:50%;
  margin-top:-4px;
  right:2px;
}
.weui-cell_link{
  color:#586C94;
  font-size:14px;
}
.weui-cell_link:first-child:before{
  display:block;
}
.weui-check__label{
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-check__label:active{
  background-color:#ECECEC;
}
.weui-check{
  position:absolute;
  left:-9999em;
}
.weui-cells_radio .weui-cell__ft{
  padding-left:0.35em;
}
.weui-cells_radio .weui-check + .weui-icon-checked{
  min-width:16px;
}
.weui-cells_radio .weui-check:checked + .weui-icon-checked:before{
  display:block;
  content:'\EA08';
  color:#07C160;
  font-size:16px;
}
.weui-cells_checkbox .weui-cell__hd{
  padding-right:0.35em;
}
.weui-cells_checkbox .weui-icon-checked:before{
  content:'\EA01';
  color:#C9C9C9;
  font-size:23px;
  display:block;
}
.weui-cells_checkbox .weui-check:checked + .weui-icon-checked:before{
  content:'\EA06';
  color:#07C160;
}
.weui-label{
  display:block;
  width:105px;
  word-wrap:break-word;
  word-break:break-all;
  font-weight: bold;
}
.weui-input{
  width:100%;
  border:0;
  outline:0;
  -webkit-appearance:none;
  background-color:transparent;
  font-size:inherit;
  color:inherit;
  height:1.47058824em;
  line-height:1.47058824;
}
.weui-input::-webkit-outer-spin-button,
.weui-input::-webkit-inner-spin-button{
  -webkit-appearance:none;
  margin:0;
}
.weui-textarea{
  display:block;
  border:0;
  resize:none;
  width:100%;
  color:inherit;
  font-size:1em;
  line-height:inherit;
  outline:0;
}
.weui-textarea-counter{
  color:#B2B2B2;
  text-align:right;
}
.weui-cell_warn .weui-textarea-counter{
  color:#FA5151;
}
.weui-toptips{
  display:none;
  position:fixed;
  -webkit-transform:translateZ(0);
          transform:translateZ(0);
  top:0;
  left:0;
  right:0;
  padding:5px;
  font-size:14px;
  text-align:center;
  color:#FFF;
  z-index:5000;
  word-wrap:break-word;
  word-break:break-all;
}
.weui-toptips_warn{
  background-color:#FA5151;
}
.weui-cells_form .weui-cell__ft{
  font-size:0;
}
.weui-cells_form .weui-icon-warn{
  display:none;
}
.weui-cells_form input,
.weui-cells_form textarea,
.weui-cells_form label[for]{
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-cell_warn{
  color:#FA5151;
}
.weui-cell_warn .weui-icon-warn{
  display:inline-block;
}
.weui-form-preview{
  position:relative;
  background-color:#FFFFFF;
}
.weui-form-preview:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #e5e5e5;
  color:#e5e5e5;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-form-preview:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #e5e5e5;
  color:#e5e5e5;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-form-preview__hd{
  position:relative;
  padding:10px 15px;
  text-align:right;
  line-height:2.5em;
}
.weui-form-preview__hd:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #e5e5e5;
  color:#e5e5e5;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  left:15px;
}
.weui-form-preview__hd .weui-form-preview__value{
  font-style:normal;
  font-size:1.6em;
}
.weui-form-preview__bd{
  padding:10px 15px;
  font-size:.9em;
  text-align:right;
  color:#999999;
  line-height:2;
}
.weui-form-preview__ft{
  position:relative;
  line-height:50px;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.weui-form-preview__ft:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #D5D5D6;
  color:#D5D5D6;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-form-preview__item{
  overflow:hidden;
}
.weui-form-preview__label{
  float:left;
  margin-right:1em;
  min-width:4em;
  color:#999999;
  text-align:justify;
  text-align-last:justify;
}
.weui-form-preview__value{
  display:block;
  overflow:hidden;
  word-break:normal;
  word-wrap:break-word;
}
.weui-form-preview__btn{
  position:relative;
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  color:#07c160;
  text-align:center;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
button.weui-form-preview__btn{
  background-color:transparent;
  border:0;
  outline:0;
  line-height:inherit;
  font-size:inherit;
}
.weui-form-preview__btn:active{
  background-color:#EEEEEE;
}
.weui-form-preview__btn:after{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  width:1px;
  bottom:0;
  border-left:1px solid #D5D5D6;
  color:#D5D5D6;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-form-preview__btn:first-child:after{
  display:none;
}
.weui-form-preview__btn_default{
  color:#999999;
}
.weui-form-preview__btn_primary{
  color:#0BB20C;
}
.weui-cell_select{
  padding:0;
}
.weui-cell_select .weui-select{
  padding-right:30px;
}
.weui-cell_select .weui-cell__bd:after{
  content:" ";
  display:inline-block;
  height:6px;
  width:6px;
  border-width:2px 2px 0 0;
  border-color:#C8C8CD;
  border-style:solid;
  -webkit-transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
          transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position:relative;
  top:-2px;
  position:absolute;
  top:50%;
  right:15px;
  margin-top:-4px;
}
.weui-select{
  -webkit-appearance:none;
  border:0;
  outline:0;
  background-color:transparent;
  width:100%;
  font-size:inherit;
  height:45px;
  line-height:45px;
  position:relative;
  z-index:1;
  padding-left:15px;
}
.weui-cell_select-before{
  padding-right:15px;
}
.weui-cell_select-before .weui-select{
  width:105px;
  box-sizing:border-box;
}
.weui-cell_select-before .weui-cell__hd{
  position:relative;
}
.weui-cell_select-before .weui-cell__hd:after{
  content:" ";
  position:absolute;
  right:0;
  top:0;
  width:1px;
  bottom:0;
  border-right:1px solid #e5e5e5;
  color:#e5e5e5;
  -webkit-transform-origin:100% 0;
          transform-origin:100% 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-cell_select-before .weui-cell__hd:before{
  content:" ";
  display:inline-block;
  height:6px;
  width:6px;
  border-width:2px 2px 0 0;
  border-color:#C8C8CD;
  border-style:solid;
  -webkit-transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
          transform:matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
  position:relative;
  top:-2px;
  position:absolute;
  top:50%;
  right:15px;
  margin-top:-4px;
}
.weui-cell_select-before .weui-cell__bd{
  padding-left:15px;
}
.weui-cell_select-before .weui-cell__bd:after{
  display:none;
}
.weui-cell_select-after{
  padding-left:15px;
}
.weui-cell_select-after .weui-select{
  padding-left:0;
}
.weui-cell_vcode{
  padding-top:0;
  padding-right:0;
  padding-bottom:0;
}
.weui-vcode-img{
  margin-left:5px;
  height:45px;
  vertical-align:middle;
}
.weui-vcode-btn{
  display:inline-block;
  height:45px;
  margin-left:5px;
  padding:0 0.6em 0 0.7em;
  border-left:1px solid #E5E5E5;
  line-height:45px;
  vertical-align:middle;
  font-size:17px;
  color:#07c160;
}
button.weui-vcode-btn{
  background-color:transparent;
  border-top:0;
  border-right:0;
  border-bottom:0;
  outline:0;
}
.weui-vcode-btn:active{
  color:#52a341;
}
.weui-gallery{
  display:none;
  position:fixed;
  top:0;
  right:0;
  bottom:0;
  left:0;
  background-color:#000000;
  z-index:1000;
}
.weui-gallery__img{
  position:absolute;
  top:0;
  right:0;
  bottom:60px;
  left:0;
  background:center center no-repeat;
  background-size:contain;
}
.weui-gallery__opr{
  position:absolute;
  right:0;
  bottom:0;
  left:0;
  background-color:#0D0D0D;
  color:#FFFFFF;
  line-height:60px;
  text-align:center;
}
.weui-gallery__del{
  display:block;
}
.weui-cell_switch{
  padding-top:6.5px;
  padding-bottom:6.5px;
}
.weui-switch{
  -webkit-appearance:none;
          appearance:none;
}
.weui-switch,
.weui-switch-cp__box{
  position:relative;
  width:52px;
  height:32px;
  border:1px solid #DFDFDF;
  outline:0;
  border-radius:16px;
  box-sizing:border-box;
  background-color:#DFDFDF;
  -webkit-transition:background-color 0.1s, border 0.1s;
  transition:background-color 0.1s, border 0.1s;
}
.weui-switch:before,
.weui-switch-cp__box:before{
  content:" ";
  position:absolute;
  top:0;
  left:0;
  width:50px;
  height:30px;
  border-radius:15px;
  background-color:#FDFDFD;
  -webkit-transition:-webkit-transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
  transition:-webkit-transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
  transition:transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
  transition:transform 0.35s cubic-bezier(0.45, 1, 0.4, 1), -webkit-transform 0.35s cubic-bezier(0.45, 1, 0.4, 1);
}
.weui-switch:after,
.weui-switch-cp__box:after{
  content:" ";
  position:absolute;
  top:0;
  left:0;
  width:30px;
  height:30px;
  border-radius:15px;
  background-color:#FFFFFF;
  box-shadow:0 1px 3px rgba(0, 0, 0, 0.4);
  -webkit-transition:-webkit-transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
  transition:-webkit-transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
  transition:transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
  transition:transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35), -webkit-transform 0.35s cubic-bezier(0.4, 0.4, 0.25, 1.35);
}
.weui-switch:checked,
.weui-switch-cp__input:checked ~ .weui-switch-cp__box{
  border-color:#07c160;
  background-color:#07c160;
}
.weui-switch:checked:before,
.weui-switch-cp__input:checked ~ .weui-switch-cp__box:before{
  -webkit-transform:scale(0);
          transform:scale(0);
}
.weui-switch:checked:after,
.weui-switch-cp__input:checked ~ .weui-switch-cp__box:after{
  -webkit-transform:translateX(20px);
          transform:translateX(20px);
}
.weui-switch-cp__input{
  position:absolute;
  left:-9999px;
}
.weui-switch-cp__box{
  display:block;
}
.weui-uploader__hd{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  padding-bottom:10px;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-uploader__title{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-uploader__info{
  color:#B2B2B2;
}
.weui-uploader__bd{
  margin-bottom:-4px;
  margin-right:-9px;
  overflow:hidden;
}
.weui-uploader__files{
  list-style:none;
}
.weui-uploader__file{
  float:left;
  margin-right:9px;
  margin-bottom:9px;
  width:79px;
  height:79px;
  background:no-repeat center center;
  background-size:cover;
}
.weui-uploader__file_status{
  position:relative;
}
.weui-uploader__file_status:before{
  content:" ";
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  background-color:rgba(0, 0, 0, 0.5);
}
.weui-uploader__file_status .weui-uploader__file-content{
  display:block;
}
.weui-uploader__file-content{
  display:none;
  position:absolute;
  top:50%;
  left:50%;
  -webkit-transform:translate(-50%, -50%);
          transform:translate(-50%, -50%);
  color:#FFFFFF;
}
.weui-uploader__file-content .weui-icon-warn{
  display:inline-block;
}
.weui-uploader__input-box{
  float:left;
  position:relative;
  margin-right:9px;
  margin-bottom:9px;
  width:77px;
  height:77px;
  border:1px solid #D9D9D9;
}
.weui-uploader__input-box:before,
.weui-uploader__input-box:after{
  content:" ";
  position:absolute;
  top:50%;
  left:50%;
  -webkit-transform:translate(-50%, -50%);
          transform:translate(-50%, -50%);
  background-color:#D9D9D9;
}
.weui-uploader__input-box:before{
  width:2px;
  height:39.5px;
}
.weui-uploader__input-box:after{
  width:39.5px;
  height:2px;
}
.weui-uploader__input-box:active{
  border-color:#999999;
}
.weui-uploader__input-box:active:before,
.weui-uploader__input-box:active:after{
  background-color:#999999;
}
.weui-uploader__input{
  position:absolute;
  z-index:1;
  top:0;
  left:0;
  width:100%;
  height:100%;
  opacity:0;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-msg{
  padding-top:36px;
  text-align:center;
}
.weui-msg__icon-area{
  margin-bottom:30px;
}
.weui-msg__text-area{
  margin-bottom:25px;
  padding:0 20px;
}
.weui-msg__text-area a{
  color:#586C94;
}
.weui-msg__title{
  margin-bottom:5px;
  font-weight:400;
  font-size:20px;
  word-wrap:break-word;
  word-break:break-all;
}
.weui-msg__desc{
  font-size:14px;
  color:#999999;
  word-wrap:break-word;
  word-break:break-all;
}
.weui-msg__opr-area{
  margin-bottom:25px;
}
.weui-msg__extra-area{
  margin-bottom:15px;
  font-size:14px;
  color:#999999;
}
.weui-msg__extra-area a{
  color:#586C94;
}
@media screen and (min-height: 438px){
  .weui-msg__extra-area{
    position:fixed;
    left:0;
    bottom:0;
    width:100%;
    text-align:center;
  }
}
@media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3){
  .weui-msg__extra-area{
    margin-bottom:49px;
  }
}
.weui-article{
  padding:20px 15px;
  font-size:15px;
}
.weui-article section{
  margin-bottom:1.5em;
}
.weui-article h1{
  font-size:18px;
  font-weight:400;
  margin-bottom:.9em;
}
.weui-article h2{
  font-size:16px;
  font-weight:400;
  margin-bottom:.34em;
}
.weui-article h3{
  font-weight:400;
  font-size:15px;
  margin-bottom:.34em;
}
.weui-article *{
  max-width:100%;
  box-sizing:border-box;
  word-wrap:break-word;
}
.weui-article p{
  margin:0 0 .8em;
}
.weui-tabbar{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  position:absolute;
  z-index:500;
  bottom:0;
  width:100%;
  background-color:#F7F7FA;
}
.weui-tabbar:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #C0BFC4;
  color:#C0BFC4;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-tabbar__item{
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  padding:5px 0 0;
  font-size:0;
  color:#999999;
  text-align:center;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon,
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__icon > i,
.weui-tabbar__item.weui-bar__item_on .weui-tabbar__label{
  color:#07C160;
}
.weui-tabbar__icon{
  display:inline-block;
  width:27px;
  height:27px;
}
i.weui-tabbar__icon,
.weui-tabbar__icon > i{
  font-size:24px;
  color:#999999;
}
.weui-tabbar__icon img{
  width:100%;
  height:100%;
}
.weui-tabbar__label{
  text-align:center;
  color:#999999;
  font-size:10px;
  line-height:1.8;
}
.weui-navbar{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  position:absolute;
  z-index:500;
  top:0;
  width:100%;
  background-color:#FAFAFA;
}
.weui-navbar:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #CCCCCC;
  color:#CCCCCC;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-navbar + .weui-tab__panel{
  padding-top:50px;
  padding-bottom:0;
}
.weui-navbar__item{
  position:relative;
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  padding:13px 0;
  text-align:center;
  font-size:15px;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
.weui-navbar__item:active{
  background-color:#EDEDED;
}
.weui-navbar__item.weui-bar__item_on{
  background-color:#EAEAEA;
}
.weui-navbar__item:after{
  content:" ";
  position:absolute;
  right:0;
  top:0;
  width:1px;
  bottom:0;
  border-right:1px solid #CCCCCC;
  color:#CCCCCC;
  -webkit-transform-origin:100% 0;
          transform-origin:100% 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-navbar__item:last-child:after{
  display:none;
}
.weui-tab{
  position:relative;
  height:100%;
}
.weui-tab__panel{
  box-sizing:border-box;
  height:100%;
  padding-bottom:50px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
}
.weui-tab__content{
  display:none;
}
.weui-progress{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-progress__bar{
  background-color:#EBEBEB;
  height:3px;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-progress__inner-bar{
  width:0;
  height:100%;
  background-color:#07C160;
}
.weui-progress__opr{
  display:block;
  margin-left:15px;
  font-size:0;
}
.weui-panel{
  background-color:#FFFFFF;
  margin-top:10px;
  position:relative;
  overflow:hidden;
}
.weui-panel:first-child{
  margin-top:0;
}
.weui-panel:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #E5E5E5;
  color:#E5E5E5;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-panel:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #E5E5E5;
  color:#E5E5E5;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-panel__hd{
  padding:14px 15px 10px;
  color:#999999;
  font-size:13px;
  position:relative;
}
.weui-panel__hd:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #E5E5E5;
  color:#E5E5E5;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  left:15px;
}
.weui-media-box{
  padding:15px;
  position:relative;
}
.weui-media-box:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #E5E5E5;
  color:#E5E5E5;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
  left:15px;
}
.weui-media-box:first-child:before{
  display:none;
}
a.weui-media-box{
  color:#000000;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
}
a.weui-media-box:active{
  background-color:#ECECEC;
}
.weui-media-box__title{
  font-weight:400;
  font-size:17px;
  width:auto;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
  word-wrap:normal;
  word-wrap:break-word;
  word-break:break-all;
}
.weui-media-box__desc{
  color:#999999;
  font-size:13px;
  line-height:1.2;
  overflow:hidden;
  text-overflow:ellipsis;
  display:-webkit-box;
  -webkit-box-orient:vertical;
  -webkit-line-clamp:2;
}
.weui-media-box__info{
  margin-top:15px;
  padding-bottom:5px;
  font-size:13px;
  color:#CECECE;
  line-height:1em;
  list-style:none;
  overflow:hidden;
}
.weui-media-box__info__meta{
  float:left;
  padding-right:1em;
}
.weui-media-box__info__meta_extra{
  padding-left:1em;
  border-left:1px solid #CECECE;
}
.weui-media-box_text .weui-media-box__title{
  margin-bottom:8px;
}
.weui-media-box_appmsg{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-media-box_appmsg .weui-media-box__hd{
  margin-right:.8em;
  width:60px;
  height:60px;
  line-height:60px;
  text-align:center;
}
.weui-media-box_appmsg .weui-media-box__thumb{
  width:100%;
  max-height:100%;
  vertical-align:top;
}
.weui-media-box_appmsg .weui-media-box__bd{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  min-width:0;
}
.weui-media-box_small-appmsg{
  padding:0;
}
.weui-media-box_small-appmsg .weui-cells{
  margin-top:0;
}
.weui-media-box_small-appmsg .weui-cells:before{
  display:none;
}
.weui-grids{
  position:relative;
  overflow:hidden;
}
.weui-grids:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #D9D9D9;
  color:#D9D9D9;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-grids:after{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  width:1px;
  bottom:0;
  border-left:1px solid #D9D9D9;
  color:#D9D9D9;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-grid{
  position:relative;
  float:left;
  padding:20px 10px;
  width:33.33333333%;
  box-sizing:border-box;
}
.weui-grid:before{
  content:" ";
  position:absolute;
  right:0;
  top:0;
  width:1px;
  bottom:0;
  border-right:1px solid #D9D9D9;
  color:#D9D9D9;
  -webkit-transform-origin:100% 0;
          transform-origin:100% 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-grid:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #D9D9D9;
  color:#D9D9D9;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-grid:active{
  background-color:#ECECEC;
}
.weui-grid__icon{
  width:28px;
  height:28px;
  margin:0 auto;
}
.weui-grid__icon img{
  display:block;
  width:100%;
  height:100%;
}
.weui-grid__icon + .weui-grid__label{
  margin-top:5px;
}
.weui-grid__label{
  display:block;
  text-align:center;
  color:#000000;
  font-size:14px;
  white-space:nowrap;
  text-overflow:ellipsis;
  overflow:hidden;
}
.weui-footer{
  color:#999999;
  font-size:14px;
  text-align:center;
}
.weui-footer a{
  color:#586C94;
}
.weui-footer_fixed-bottom{
  position:fixed;
  bottom:.52em;
  left:0;
  right:0;
}
.weui-footer__links{
  font-size:0;
}
.weui-footer__link{
  display:inline-block;
  vertical-align:top;
  margin:0 .62em;
  position:relative;
  font-size:14px;
}
.weui-footer__link:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  width:1px;
  bottom:0;
  border-left:1px solid #C7C7C7;
  color:#C7C7C7;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
  left:-0.65em;
  top:.36em;
  bottom:.36em;
}
.weui-footer__link:first-child:before{
  display:none;
}
.weui-footer__text{
  padding:0 .34em;
  font-size:12px;
}
.weui-flex{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.weui-flex__item{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-dialog{
  position:fixed;
  z-index:5000;
  width:80%;
  max-width:300px;
  top:50%;
  left:50%;
  -webkit-transform:translate(-50%, -50%);
          transform:translate(-50%, -50%);
  background-color:#FFFFFF;
  text-align:center;
  border-radius:3px;
  overflow:hidden;
}
.weui-dialog__hd{
  padding:1.3em 1.6em 0.5em;
}
.weui-dialog__title{
  font-weight:400;
  font-size:18px;
}
.weui-dialog__bd{
  padding:0 1.6em 0.8em;
  min-height:40px;
  font-size:15px;
  line-height:1.3;
  word-wrap:break-word;
  word-break:break-all;
  color:#999999;
}
.weui-dialog__bd:first-child{
  padding:2.7em 20px 1.7em;
  color:#353535;
}
.weui-dialog__ft{
  position:relative;
  line-height:48px;
  font-size:18px;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
}
.weui-dialog__ft:after{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #D5D5D6;
  color:#D5D5D6;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-dialog__btn{
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  color:#07c160;
  text-decoration:none;
  -webkit-tap-highlight-color:rgba(0, 0, 0, 0);
  position:relative;
}
.weui-dialog__btn:active{
  background-color:#EEEEEE;
}
.weui-dialog__btn:after{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  width:1px;
  bottom:0;
  border-left:1px solid #D5D5D6;
  color:#D5D5D6;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleX(0.5);
          transform:scaleX(0.5);
}
.weui-dialog__btn:first-child:after{
  display:none;
}
.weui-dialog__btn_default{
  color:#353535;
}
.weui-dialog__btn_primary{
  color:#0BB20C;
}
.weui-skin_android .weui-dialog{
  text-align:left;
  box-shadow:0 6px 30px 0 rgba(0, 0, 0, 0.1);
}
.weui-skin_android .weui-dialog__title{
  font-size:21px;
}
.weui-skin_android .weui-dialog__hd{
  text-align:left;
}
.weui-skin_android .weui-dialog__bd{
  color:#999999;
  padding:0.25em 1.6em 2em;
  font-size:17px;
  text-align:left;
}
.weui-skin_android .weui-dialog__bd:first-child{
  padding:1.6em 1.6em 2em;
  color:#353535;
}
.weui-skin_android .weui-dialog__ft{
  display:block;
  text-align:right;
  line-height:42px;
  font-size:16px;
  padding:0 1.6em 0.7em;
}
.weui-skin_android .weui-dialog__ft:after{
  display:none;
}
.weui-skin_android .weui-dialog__btn{
  display:inline-block;
  vertical-align:top;
  padding:0 .8em;
}
.weui-skin_android .weui-dialog__btn:after{
  display:none;
}
.weui-skin_android .weui-dialog__btn:active{
  background-color:rgba(0, 0, 0, 0.06);
}
.weui-skin_android .weui-dialog__btn:visited{
  background-color:rgba(0, 0, 0, 0.06);
}
.weui-skin_android .weui-dialog__btn:last-child{
  margin-right:-0.8em;
}
.weui-skin_android .weui-dialog__btn_default{
  color:#808080;
}
@media screen and (min-width: 1024px){
  .weui-dialog{
    width:35%;
  }
}
.weui-toast{
  position:fixed;
  z-index:5000;
  width:7.6em;
  min-height:7.6em;
  top:180px;
  left:50%;
  margin-left:-3.8em;
  background:rgba(17, 17, 17, 0.7);
  text-align:center;
  border-radius:5px;
  color:#FFFFFF;
}
.weui-icon_toast{
  margin:22px 0 0;
  display:block;
}
.weui-icon_toast.weui-icon-success-no-circle:before{
  color:#FFFFFF;
  font-size:55px;
}
.weui-icon_toast.weui-loading{
  margin:30px 0 0;
  width:38px;
  height:38px;
  vertical-align:baseline;
}
.weui-toast__content{
  margin:0 0 15px;
}
.weui-mask{
  position:fixed;
  z-index:1000;
  top:0;
  right:0;
  left:0;
  bottom:0;
  background:rgba(0, 0, 0, 0.6);
}
.weui-mask_transparent{
  position:fixed;
  z-index:1000;
  top:0;
  right:0;
  left:0;
  bottom:0;
}
.weui-actionsheet{
  position:fixed;
  left:0;
  bottom:0;
  -webkit-transform:translate(0, 100%);
          transform:translate(0, 100%);
  -webkit-backface-visibility:hidden;
          backface-visibility:hidden;
  z-index:5000;
  width:100%;
  background-color:#EFEFF4;
  -webkit-transition:-webkit-transform .3s;
  transition:-webkit-transform .3s;
  transition:transform .3s;
  transition:transform .3s, -webkit-transform .3s;
}
.weui-actionsheet__title{
  position:relative;
  height:65px;
  padding:0 20px;
  line-height:1.4;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-pack:center;
  -webkit-justify-content:center;
          justify-content:center;
  -webkit-box-orient:vertical;
  -webkit-box-direction:normal;
  -webkit-flex-direction:column;
          flex-direction:column;
  text-align:center;
  font-size:14px;
  color:#888;
  background:#FCFCFD;
}
.weui-actionsheet__title:before{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #e5e5e5;
  color:#e5e5e5;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-actionsheet__title .weui-actionsheet__title-text{
  overflow:hidden;
  text-overflow:ellipsis;
  display:-webkit-box;
  -webkit-box-orient:vertical;
  -webkit-line-clamp:2;
}
.weui-actionsheet__menu{
  background-color:#FCFCFD;
}
.weui-actionsheet__action{
  margin-top:6px;
  background-color:#FCFCFD;
}
.weui-actionsheet__cell{
  position:relative;
  padding:10px 0;
  text-align:center;
  font-size:18px;
}
.weui-actionsheet__cell:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #e5e5e5;
  color:#e5e5e5;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-actionsheet__cell:active{
  background-color:#ECECEC;
}
.weui-actionsheet__cell:first-child:before{
  display:none;
}
.weui-skin_android .weui-actionsheet{
  position:fixed;
  left:50%;
  top:50%;
  bottom:auto;
  -webkit-transform:translate(-50%, -50%);
          transform:translate(-50%, -50%);
  width:274px;
  box-sizing:border-box;
  -webkit-backface-visibility:hidden;
          backface-visibility:hidden;
  background:transparent;
  -webkit-transition:-webkit-transform .3s;
  transition:-webkit-transform .3s;
  transition:transform .3s;
  transition:transform .3s, -webkit-transform .3s;
}
.weui-skin_android .weui-actionsheet__action{
  display:none;
}
.weui-skin_android .weui-actionsheet__menu{
  border-radius:2px;
  box-shadow:0 6px 30px 0 rgba(0, 0, 0, 0.1);
}
.weui-skin_android .weui-actionsheet__cell{
  padding:13px 24px;
  font-size:16px;
  line-height:1.4;
  text-align:left;
}
.weui-skin_android .weui-actionsheet__cell:first-child{
  border-top-left-radius:2px;
  border-top-right-radius:2px;
}
.weui-skin_android .weui-actionsheet__cell:last-child{
  border-bottom-left-radius:2px;
  border-bottom-right-radius:2px;
}
.weui-actionsheet_toggle{
  -webkit-transform:translate(0, 0);
          transform:translate(0, 0);
}
.weui-loadmore{
  width:65%;
  margin:1em auto;
  line-height:1.6em;
  font-size:14px;
  text-align:center;
}
.weui-loadmore__tips{
  display:inline-block;
  vertical-align:middle;
}
.weui-loadmore_line{
  border-top:1px solid #E5E5E5;
  margin-top:2.4em;
}
.weui-loadmore_line .weui-loadmore__tips{
  position:relative;
  top:-0.9em;
  padding:0 .55em;
  background-color:#FFFFFF;
  color:#999999;
}
.weui-loadmore_dot .weui-loadmore__tips{
  padding:0 .16em;
}
.weui-loadmore_dot .weui-loadmore__tips:before{
  content:" ";
  width:4px;
  height:4px;
  border-radius:50%;
  background-color:#E5E5E5;
  display:inline-block;
  position:relative;
  vertical-align:0;
  top:-0.16em;
}
.weui-badge{
  display:inline-block;
  padding:.15em .4em;
  min-width:8px;
  border-radius:18px;
  background-color:#FA5151;
  color:#FFFFFF;
  line-height:1.2;
  text-align:center;
  font-size:12px;
  vertical-align:middle;
}
.weui-badge_dot{
  padding:.4em;
  min-width:0;
}
.weui-search-bar{
  position:relative;
  padding:8px 10px;
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  box-sizing:border-box;
  background-color:#EFEFF4;
  -webkit-text-size-adjust:100%;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-search-bar:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #D7D6DC;
  color:#D7D6DC;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-search-bar:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #D7D6DC;
  color:#D7D6DC;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-search-bar.weui-search-bar_focusing .weui-search-bar__cancel-btn{
  display:block;
}
.weui-search-bar.weui-search-bar_focusing .weui-search-bar__label{
  display:none;
}
.weui-search-bar__form{
  position:relative;
  -webkit-box-flex:1;
  -webkit-flex:auto;
          flex:auto;
  background-color:#EFEFF4;
}
.weui-search-bar__form:after{
  content:'';
  position:absolute;
  left:0;
  top:0;
  width:200%;
  height:200%;
  -webkit-transform:scale(0.5);
          transform:scale(0.5);
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  border-radius:10px;
  border:1px solid #E6E6EA;
  box-sizing:border-box;
  background:#FFFFFF;
}
.weui-search-bar__box{
  position:relative;
  padding-left:30px;
  padding-right:30px;
  height:100%;
  width:100%;
  box-sizing:border-box;
  z-index:1;
}
.weui-search-bar__box .weui-search-bar__input{
  padding:4px 0;
  width:100%;
  height:1.42857143em;
  border:0;
  font-size:14px;
  line-height:1.42857143em;
  box-sizing:content-box;
  background:transparent;
}
.weui-search-bar__box .weui-search-bar__input:focus{
  outline:none;
}
.weui-search-bar__box .weui-icon-search{
  position:absolute;
  top:50%;
  left:10px;
  margin-top:-14px;
  line-height:28px;
}
.weui-search-bar__box .weui-icon-clear{
  position:absolute;
  top:50%;
  right:0;
  margin-top:-14px;
  padding:0 10px;
  line-height:28px;
}
.weui-search-bar__label{
  position:absolute;
  top:1px;
  right:1px;
  bottom:1px;
  left:1px;
  z-index:2;
  border-radius:3px;
  text-align:center;
  color:#9B9B9B;
  background:#FFFFFF;
}
.weui-search-bar__label span{
  display:inline-block;
  font-size:14px;
  vertical-align:middle;
}
.weui-search-bar__label .weui-icon-search{
  margin-right:5px;
}
.weui-search-bar__cancel-btn{
  display:none;
  margin-left:10px;
  line-height:28px;
  color:#07C160;
  white-space:nowrap;
}
.weui-search-bar__input:not(:valid) ~ .weui-icon-clear{
  display:none;
}
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration{
  display:none;
}
.weui-picker{
  position:fixed;
  width:100%;
  left:0;
  bottom:0;
  z-index:5000;
  -webkit-backface-visibility:hidden;
          backface-visibility:hidden;
  -webkit-transform:translate(0, 100%);
          transform:translate(0, 100%);
  -webkit-transition:-webkit-transform .3s;
  transition:-webkit-transform .3s;
  transition:transform .3s;
  transition:transform .3s, -webkit-transform .3s;
}
.weui-picker__hd{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  padding:9px 15px;
  background-color:#fff;
  position:relative;
  text-align:center;
  font-size:17px;
}
.weui-picker__hd:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #E5E5E5;
  color:#E5E5E5;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-picker__action{
  display:block;
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  color:#07c160;
}
.weui-picker__action:first-child{
  text-align:left;
  color:#888;
}
.weui-picker__action:last-child{
  text-align:right;
}
.weui-picker__bd{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  position:relative;
  background-color:#fff;
  height:238px;
  overflow:hidden;
}
.weui-picker__group{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
  position:relative;
  height:100%;
}
.weui-picker__mask{
  position:absolute;
  top:0;
  left:0;
  width:100%;
  height:100%;
  margin:0 auto;
  z-index:3;
  background:-webkit-linear-gradient(top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6)), -webkit-linear-gradient(bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6));
  background:linear-gradient(180deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6)), linear-gradient(0deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6));
  background-position:top, bottom;
  background-size:100% 102px;
  background-repeat:no-repeat;
  -webkit-transform:translateZ(0);
          transform:translateZ(0);
}
.weui-picker__indicator{
  width:100%;
  height:34px;
  position:absolute;
  left:0;
  top:102px;
  z-index:3;
}
.weui-picker__indicator:before{
  content:" ";
  position:absolute;
  left:0;
  top:0;
  right:0;
  height:1px;
  border-top:1px solid #E5E5E5;
  color:#E5E5E5;
  -webkit-transform-origin:0 0;
          transform-origin:0 0;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-picker__indicator:after{
  content:" ";
  position:absolute;
  left:0;
  bottom:0;
  right:0;
  height:1px;
  border-bottom:1px solid #E5E5E5;
  color:#E5E5E5;
  -webkit-transform-origin:0 100%;
          transform-origin:0 100%;
  -webkit-transform:scaleY(0.5);
          transform:scaleY(0.5);
}
.weui-picker__content{
  position:absolute;
  top:0;
  left:0;
  width:100%;
}
.weui-picker__item{
  padding:0;
  height:34px;
  line-height:34px;
  text-align:center;
  color:#000;
  text-overflow:ellipsis;
  white-space:nowrap;
  overflow:hidden;
}
.weui-picker__item_disabled{
  color:#999999;
}
@-webkit-keyframes slideUp{
  from{
    -webkit-transform:translate3d(0, 100%, 0);
            transform:translate3d(0, 100%, 0);
  }
  to{
    -webkit-transform:translate3d(0, 0, 0);
            transform:translate3d(0, 0, 0);
  }
}
@keyframes slideUp{
  from{
    -webkit-transform:translate3d(0, 100%, 0);
            transform:translate3d(0, 100%, 0);
  }
  to{
    -webkit-transform:translate3d(0, 0, 0);
            transform:translate3d(0, 0, 0);
  }
}
.weui-animate-slide-up{
  -webkit-animation:slideUp ease .3s forwards;
          animation:slideUp ease .3s forwards;
}
@-webkit-keyframes slideDown{
  from{
    -webkit-transform:translate3d(0, 0, 0);
            transform:translate3d(0, 0, 0);
  }
  to{
    -webkit-transform:translate3d(0, 100%, 0);
            transform:translate3d(0, 100%, 0);
  }
}
@keyframes slideDown{
  from{
    -webkit-transform:translate3d(0, 0, 0);
            transform:translate3d(0, 0, 0);
  }
  to{
    -webkit-transform:translate3d(0, 100%, 0);
            transform:translate3d(0, 100%, 0);
  }
}
.weui-animate-slide-down{
  -webkit-animation:slideDown ease .3s forwards;
          animation:slideDown ease .3s forwards;
}
@-webkit-keyframes fadeIn{
  from{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
@keyframes fadeIn{
  from{
    opacity:0;
  }
  to{
    opacity:1;
  }
}
.weui-animate-fade-in{
  -webkit-animation:fadeIn ease .3s forwards;
          animation:fadeIn ease .3s forwards;
}
@-webkit-keyframes fadeOut{
  from{
    opacity:1;
  }
  to{
    opacity:0;
  }
}
@keyframes fadeOut{
  from{
    opacity:1;
  }
  to{
    opacity:0;
  }
}
.weui-animate-fade-out{
  -webkit-animation:fadeOut ease .3s forwards;
          animation:fadeOut ease .3s forwards;
}
.weui-agree{
  display:block;
  padding:.5em 15px;
  font-size:13px;
}
.weui-agree a{
  color:#586C94;
}
.weui-agree__text{
  color:#999999;
}
.weui-agree__checkbox{
  -webkit-appearance:none;
          appearance:none;
  outline:0;
  font-size:0;
  border:1px solid #D1D1D1;
  background-color:#FFFFFF;
  border-radius:3px;
  width:13px;
  height:13px;
  position:relative;
  vertical-align:0;
  top:2px;
}
.weui-agree__checkbox:checked:before{
  font-family:"weui";
  font-style:normal;
  font-weight:normal;
  font-variant:normal;
  text-transform:none;
  text-align:center;
  speak:none;
  display:inline-block;
  vertical-align:middle;
  text-decoration:inherit;
  content:"\EA08";
  color:#07C160;
  font-size:13px;
  position:absolute;
  top:50%;
  left:50%;
  -webkit-transform:translate(-50%, -48%) scale(0.73);
          transform:translate(-50%, -48%) scale(0.73);
}
.weui-agree__checkbox:disabled{
  background-color:#E1E1E1;
}
.weui-agree__checkbox:disabled:before{
  color:#ADADAD;
}
.weui-loading{
  width:20px;
  height:20px;
  display:inline-block;
  vertical-align:middle;
  -webkit-animation:weuiLoading 1s steps(12, end) infinite;
          animation:weuiLoading 1s steps(12, end) infinite;
  background:transparent url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E9E9E9' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23989697' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%239B999A' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23A3A1A2' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23ABA9AA' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23B2B2B2' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23BAB8B9' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23C2C0C1' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23CBCBCB' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23D2D2D2' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23DADADA' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='%23E2E2E2' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E") no-repeat;
  background-size:100%;
}
.weui-loading.weui-loading_transparent,
.weui-btn_loading.weui-btn_primary .weui-loading,
.weui-btn_loading.weui-btn_warn .weui-loading{
  background-image:url("data:image/svg+xml;charset=utf8, %3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 100 100'%3E%3Cpath fill='none' d='M0 0h100v100H0z'/%3E%3Crect xmlns='http://www.w3.org/2000/svg' width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.56)' rx='5' ry='5' transform='translate(0 -30)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.5)' rx='5' ry='5' transform='rotate(30 105.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.43)' rx='5' ry='5' transform='rotate(60 75.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.38)' rx='5' ry='5' transform='rotate(90 65 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.32)' rx='5' ry='5' transform='rotate(120 58.66 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.28)' rx='5' ry='5' transform='rotate(150 54.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.25)' rx='5' ry='5' transform='rotate(180 50 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.2)' rx='5' ry='5' transform='rotate(-150 45.98 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.17)' rx='5' ry='5' transform='rotate(-120 41.34 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.14)' rx='5' ry='5' transform='rotate(-90 35 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.1)' rx='5' ry='5' transform='rotate(-60 24.02 65)'/%3E%3Crect width='7' height='20' x='46.5' y='40' fill='rgba(255,255,255,.03)' rx='5' ry='5' transform='rotate(-30 -5.98 65)'/%3E%3C/svg%3E");
}
@-webkit-keyframes weuiLoading{
  0%{
    -webkit-transform:rotate3d(0, 0, 1, 0deg);
            transform:rotate3d(0, 0, 1, 0deg);
  }
  100%{
    -webkit-transform:rotate3d(0, 0, 1, 360deg);
            transform:rotate3d(0, 0, 1, 360deg);
  }
}
@keyframes weuiLoading{
  0%{
    -webkit-transform:rotate3d(0, 0, 1, 0deg);
            transform:rotate3d(0, 0, 1, 0deg);
  }
  100%{
    -webkit-transform:rotate3d(0, 0, 1, 360deg);
            transform:rotate3d(0, 0, 1, 360deg);
  }
}
.weui-slider{
  padding:15px 18px;
  -webkit-user-select:none;
          user-select:none;
}
.weui-slider__inner{
  position:relative;
  height:2px;
  background-color:#E9E9E9;
}
.weui-slider__track{
  height:2px;
  background-color:#07c160;
  width:0;
}
.weui-slider__handler{
  position:absolute;
  left:0;
  top:50%;
  width:28px;
  height:28px;
  margin-left:-14px;
  margin-top:-14px;
  border-radius:50%;
  background-color:#FFFFFF;
  box-shadow:0 0 4px rgba(0, 0, 0, 0.2);
}
.weui-slider-box{
  display:-webkit-box;
  display:-webkit-flex;
  display:flex;
  -webkit-box-align:center;
  -webkit-align-items:center;
          align-items:center;
}
.weui-slider-box .weui-slider{
  -webkit-box-flex:1;
  -webkit-flex:1;
          flex:1;
}
.weui-slider-box__value{
  margin-left:.5em;
  min-width:24px;
  color:#888888;
  text-align:center;
  font-size:14px;
}
