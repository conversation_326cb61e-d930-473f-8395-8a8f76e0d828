<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>审批首页</title>
	<style type="text/css">
		.flow-stat .layui-card-body{padding: 0px;}
		.layui-card{margin-bottom: 5px;}
		.layui-card-header,.layui-card-header select{color:rgb(62, 104, 254)!important;}
		.fast-url a{display: inline-block;font-size: 14px;color: #292929;padding:0px 10px;width: 29%;}
		.top-stat .layui-col-md3{text-align: center;padding: 10px;}
		.top-1{font-size: 24px;font-weight: 500;color: #22cd0f;text-align: center;margin-top: 8px;}
		.top-2{font-size: 14px;color: #666;text-align:center;}
		.flow-stat .layui-card{padding: 10px;border-radius: 5px;}
		.flow-stat .layui-card:hover {
		   transform: scale(1.02);
		   transition: transform .3s ease;
		   box-shadow: 0 4px 11px #0003;
		   cursor: pointer;
		}
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="searchForm">
	  <div class="layui-row layui-col-space10 flow-stat">
	    <div class="layui-col-md2">
	    	<div class="layui-card" data-category="1">
	    		<div class="layui-card-body">
	    			<div class="top-2"><i class="layui-icon layui-icon-flag"></i> 待办流程</div>
	    			<div class="top-1" id="flow_my_todo">0</div>
	    		</div>
	    	</div>
	    </div>
	    <div class="layui-col-md2">
	    	<div class="layui-card" data-category="2">
	    		<div class="layui-card-body">
    				<div class="top-2"><i class="layui-icon layui-icon-edit"></i> 我的请求</div>
	    			<div class="top-1" id="flow_my_apply">0</div>
    			</div>
	    	</div>
	    </div>
	    <div class="layui-col-md2">
	    	<div class="layui-card" data-category="3">
	    		<div class="layui-card-body">
    				<div class="top-2"><i class="layui-icon layui-icon-log"></i> 已办流程</div>
	    			<div class="top-1" id="flow_my_done">0</div>
    			</div>
	    	</div>
	    </div>
	    <div class="layui-col-md2">
	    	<div class="layui-card" data-category="4">
	    		<div class="layui-card-body">
    				<div class="top-2"><i class="layui-icon layui-icon-read"></i> 抄送流程</div>
	    			<div class="top-1" id="flow_my_cc">0</div>
    			</div>
	    	</div>
	    </div>
	    <div class="layui-col-md2">
	    	<div class="layui-card" data-category="5">
	    		<div class="layui-card-body">
    				<div class="top-2"><i class="layui-icon layui-icon-ok"></i> 办结流程</div>
	    			<div class="top-1" id="flow_my_finish">0</div>
    			</div>
	    	</div>
	    </div>
	    <div class="layui-col-md2">
	    	<div class="layui-card" data-category="6">
	    		<div class="layui-card-body">
    				<div class="top-2">新建流程</div>
	    			<div class="top-1"><i class="layui-icon layui-icon-addition"></i></div>
    			</div>
	    	</div>
	    </div>
	  </div>
	  <div class="layui-row layui-col-space10">
	  	<div class="layui-col-md6">
	  	 <div class="layui-card">
	  	 	<div class="layui-card-header">
	  	 		<select data-mars="FlowDao.myFlowTodoDict" class="form-control input-sm selectTodoFlow" style="display: inline-block;width: 150px;float: left;margin-top: 5px;color:#000000;box-shadow:none;border: none;">
	  	 			<option value="">我的待办 </option>
	  	 		</select>
	  	 	</div>
    		<div class="layui-card-body" style="height: 350px;">
    			<table id="myFlowTable"></table>
    		</div>
	     </div>
	  	</div>
	  	<div class="layui-col-md6">
	  	   <div class="layui-card">
	  	   	 <div class="layui-card-header"><i class="layui-icon layui-icon-edit"></i> 我的申请</div>
    		 <div class="layui-card-body" style="height: 350px;">
	  		 	<table id="myFlowApplyTable"></table>
	  		 </div>
	  		</div>
	    </div>
	  </div>
	  <div class="layui-row layui-col-space10">
	   		<div class="layui-col-md6">
	   			 <div class="layui-card">
			  	   	 <div class="layui-card-header"><i class="layui-icon layui-icon-read"></i> 抄送我的流程</div>
		    		 <div class="layui-card-body" style="height: 350px;">
			  		 	<table id="myCcFlowTable"></table>
			  		 </div>
			     </div>
	   		</div>
	   		<div class="layui-col-md6">
	   			 <div class="layui-card">
			  	   	 <div class="layui-card-header"><i class="layui-icon layui-icon-log"></i> 我审批记录</div>
		    		 <div class="layui-card-body" style="height: 350px;">
			  		 	<table id="myCheckRecordTable"></table>
			  		 </div>
			     </div>
	   		</div>
	  </div>
	  <div class="layui-row layui-col-space10">
	   		<div class="layui-col-md6">
	   		  <div class="layui-card">
		  	   	 <div class="layui-card-header"><i class="layui-icon layui-icon-read"></i> 我的流程监控</div>
	    		 <div class="layui-card-body" style="height: 350px;">
		  		 	<table id="myApplyMonitorTable"></table>
		  		 </div>
	  		   </div>
	   		</div>
	   		<div class="layui-col-md6">
	   		  <div class="layui-card">
		  	   	 <div class="layui-card-header"><i class="layui-icon layui-icon-chart"></i> 其他汇总</div>
	    		 <div class="layui-card-body" style="height: 350px;">
	    		 		<div class="layui-row layui-col-space5">
	    		 			<div class="layui-col-md6">
			  		 			<div data-mars="FlowCommon.personBxData" data-template="personBxTpl"></div>
	    		 			</div>
	    		 			<div class="layui-col-md6">
			  		 			<div data-mars="FlowCommon.kqTypeStat" data-template="kqStatTpl"></div>
	    		 			</div>
	    		 		</div>
					  <script id="personBxTpl" type="text/x-jsrender">
							<table class="layui-table">
								<thead><tr><th>报销日期</th><th>报销金额</th></tr></thead>
  	 							{{for data}}
									<tr><td>{{:APPLY_DATE}}</td><td>{{:BX_MONEY}}</td></tr>
 								{{/for}}
								{{if data.length==0}}<tr><td colspan="2">无报销记录</td></tr>{{/if}}
							</table>
					 </script>
					 <script id="kqStatTpl" type="text/x-jsrender">
							 <table class="layui-table">
								<thead><tr><th>考勤类型</th><th>天数</th></tr></thead>
  	 							{{for data}}
									<tr><td>{{:TYPE}}</td><td>{{:NUM}}</td></tr>
 								{{/for}}
								{{if data.length==0}}<tr><td colspan="2">无考勤流程记录</td></tr>{{/if}}
							</table>
					 </script>
		  		 </div>
	  		   </div>
	   		</div>
	  </div>
   </form>   
   <script type="text/x-jsrender" id="bar">
		{{if APPROVE_TYPE=='1'}}
 			<a class="btn btn-xs btn-warning" href="javascript:;"><i class="fa fa-paper-plane-o"></i> 协办</a>
		{{else APPROVE_TYPE=='2'}}
 			<a class="btn btn-xs btn-danger" href="javascript:;"><i class="fa fa-paper-plane-o"></i> 审批</a>
		{{else}}
 			<a class="layui-btn layui-btn-normal layui-btn-xs" href="javascript:;"><i class="fa fa-paper-plane-o"></i> 审批</a>
		{{/if}}
   </script> 
    <script type="text/x-jsrender" id="bar2">
		{{if APPLY_STATE=='21'}}
 			<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="flowEdit" href="javascript:;">重新提交</a>
		{{else}}
 			 <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="flowDetail" href="javascript:;">详情</a>
 		{{/if}}
   </script>
    <script type="text/x-jsrender" id="bar3">
 		 <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="flowDetail" href="javascript:;">详情</a>
   </script>
</EasyTag:override>
<EasyTag:override name="script">
	<script>
	  	$(function(){
	  		$("#searchForm").render({success:function(){
		  		loadTables();
		  		loadFlowNum();
	  		}});
	  		
	  		$("[data-category]").click(function(){
	  			var type = $(this).data('category');
	  			if(type=='6'){
	  				popup.openTab({id:'flow_apply',title:'流程发起',url:'/yq-work/pages/flow/flow-apply.jsp'})
	  			}else if(type=='1'){
	  				popup.openTab({id:'flow_my_todo',title:'我的待办',url:'/yq-work/pages/flow/my/flow-todo.jsp'})
	  			}else if(type=='2'){
	  				popup.openTab({id:'flow_my_apply',title:'我的申请',url:'/yq-work/pages/flow/my/flow-list.jsp'})
	  			}else if(type=='4'){
	  				popup.openTab({id:'flow_cc',title:'抄送流程',url:'/yq-work//pages/flow/my/flow-cc.jsp'})
	  			}
	  		});
	  		
	  		
	  		$('.selectTodoFlow').on('change',function(){
	  			var value = $(this).val();
	  			$("#searchForm").queryData({data:{flowCode:value}});
	  		});
	  		
	  	});
	  	
	  	function loadTables(){
	  		$("#searchForm").initTable({
				mars:'FlowDao.myFlowTodo',
				title:'待办流程',
				cellMinWidth:50,
				limit:20,
				data:{homePage:0},
				height:'320px',
				id:'myFlowTable',
				cols: [[
					{
						field:'',
						title:'操作',
						width:80,
						align:'center',
						event:'checkFlowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					},{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:220
					},{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
						 field:'APPLY_REMARK',
						 title:'申请说明',
						 minWidth:140
					 },{
						 field:'NODE_NAME',
						 title:'当前节点',
						 width:110
					 }
			]]});
	  		
	  		$("#searchForm").initTable({
				mars:'FlowDao.myFlowApply',
				title:'我的申请流程',
				cellMinWidth:50,
				limit:20,
				data:{homePage:0},
				height:'320px',
				id:'myFlowApplyTable',
				cols: [[
					{
						field:'',
						title:'操作',
						width:80,
						align:'center',
						templet:function(row){
							return renderTpl('bar2',row);
						}
					},{
						field:'APPLY_STATE',
						title:'流程状态',
						width:80,
						templet:function(row){
							var val = row['APPLY_STATE'];
							return flowApplyState(val);
						}
					},{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:220
					},{
						 field:'CHECK_NAME',
						 title:'待审批人',
						 width:80
					 },{
						 field:'APPLY_REMARK',
						 title:'申请说明',
						 minWidth:140
					 },{
						 field:'NODE_NAME',
						 title:'当前节点',
						 width:110
					 },{
					     field: 'APPLY_TIME',
						 title: '申请时间',
						 align:'center',
					     width:160
					}
			]]});
	  		
	  		$("#searchForm").initTable({
				mars:'FlowCommon.approveRecordList',
				title:'我审批记录',
				cellMinWidth:50,
				limit:30,
				data:{source:'checked'},
				height:'320px',
				id:'myCheckRecordTable',
				cols: [[
					{
						field:'',
						title:'操作',
						width:80,
						align:'center',
						event:'checkFlowDetail',
						templet:function(row){
							return renderTpl('bar3',row);
						}
					},{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:220
					},{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
						 field:'APPLY_REMARK',
						 title:'申请说明',
						 minWidth:140
					 },{
						 field:'NODE_NAME',
						 title:'审批节点',
						 width:110
					 },{
						 field:'CHECK_RESULT',
						 title:'审批状态',
						 width:70,
						 templet:function(row){
							var checkResult = row['CHECK_RESULT'];
							var json = {'0':'待审批','1':'通过','2':'<span class="label label-danger">拒绝</span>'};
							return json[checkResult]||'--';
						 }
					 }
			]]});
	  		
	  		$("#searchForm").initTable({
				mars:'FlowCommon.approveRecordList',
				title:'我的流程监控',
				cellMinWidth:50,
				limit:30,
				layout:['prev','next'],
				data:{source:'apply'},
				height:'320px',
				id:'myApplyMonitorTable',
				cols: [[
					{
						field:'',
						title:'操作',
						width:80,
						align:'center',
						event:'checkFlowDetail',
						templet:function(row){
							return renderTpl('bar3',row);
						}
					},{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:220
					},{
						field:'CHECK_RESULT',
						title:'审批状态',
						width:70,
						templet:function(row){
							var checkResult = row['CHECK_RESULT'];
							var json = {'0':'待审批','1':'通过','2':'<span class="label label-danger">拒绝</span>'};
							return json[checkResult]||'--';
						}
					},{
						 field:'CHECK_NAME',
						 title:'审批人',
						 width:80
					 },{
						 field:'NODE_NAME',
						 title:'审批节点',
						 width:110
					 },{
						 field:'APPLY_REMARK',
						 title:'申请说明',
						 minWidth:140
					 }
			]]});
	  		
	  		$("#searchForm").initTable({
				mars:'FlowDao.myCCFlowList',
				title:'我的抄送流程',
				cellMinWidth:50,
				limit:30,
				layout:['prev','next'],
				data:{source:'apply'},
				height:'320px',
				id:'myCcFlowTable',
				cols: [[
					{
						field:'',
						title:'操作',
						width:80,
						align:'center',
						event:'checkFlowDetail',
						templet:function(row){
							return renderTpl('bar3',row);
						}
					},{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:220
					},{
						 field:'APPLY_NAME',
						 title:'申请人',
						 width:80
					 },{
						 field:'APPLY_REMARK',
						 title:'申请说明',
						 minWidth:140
					 },{
						 field:'NODE_NAME',
						 title:'当前节点',
						 width:110
					 }
				]]});
		 }
	  	
	  	
		function checkFlowDetail(data){
			var flowCode = data['FLOW_CODE'];
			var readTime = data['READ_TIME'];
			var resultId = data['RESULT_ID'];
			if(readTime==''){
				ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=addReadTime",{resultId:resultId},function(result) {});
			}
			var json  = $.extend({},{businessId:data['APPLY_ID'],flowCode:flowCode,isApprovalFlag:'1','handle':'approval',resultId:resultId});
			var url = '${ctxPath}/web/flow';
			
			if(self==top){
				location.href = url+"?"+jQuery.param(json);				
			}else{
				popup.openTab({id:'flowDetail',title:'审批-'+data['FLOW_NAME'],url:url,data:json});
			}
		}
		
		var flowDetail = function(data){
			var flowCode = data['FLOW_CODE'];
			var applyState = data['APPLY_STATE'];
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE'],resultId:data['RESULT_ID']});
			if(applyState=='0'){
				json['handle'] = 'edit';				
			}
			var url = '${ctxPath}/web/flow';
			if(self==top){
				location.href = url+"?"+jQuery.param(json);				
			}else{
				popup.openTab({id:'flowDetail',title:'流程详情',url:url,data:json});
			}
		}
		
		var flowEdit = function(dataList){
			var data = dataList;
			if(isArray(dataList)){
			    if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    data = dataList[0];
			}
		    
		    var applyState = data['APPLY_STATE'];
			if(applyState=='0'||applyState=='10'||applyState=='20'||applyState=='21'){
				var flowCode = data['FLOW_CODE'];
				var json  = $.extend({handle:'edit',mode:'edit'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE']});
				var url = '${ctxPath}/web/flow';
				if(self==top){
					location.href = url+"?"+jQuery.param(json);				
				}else{
					popup.openTab({id:'flowDetail',title:'流程详情',url:url,data:json});
				}
			}else{
				layer.msg('草稿或没审批过的申请才能修改',{icon:2,offset:'20px',time:1000});
			}
		}
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>