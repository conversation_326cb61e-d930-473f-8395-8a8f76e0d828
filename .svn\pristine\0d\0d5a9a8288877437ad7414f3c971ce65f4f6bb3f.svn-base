package com.yunqu.work.servlet.common;

import java.sql.SQLException;

import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;


@Before(AuthInterceptor.class)
@Path(value = "/project")
public class ProjectController extends BaseController {

	public void projectIndex() {
		render("/pages/project/project-index.jsp");
	}
	
	public void projectNav() {
		keepPara();
		String projectId = getPara();
		if(StringUtils.isBlank(projectId)) {
			renderHtml("页面链接错误");
			return;
		}
		try {
			JSONObject result = this.getQuery().queryForRow("select * from yq_project where project_id  = ?",new Object[] {projectId}, new JSONMapperImpl());
			if(result==null) {
				renderHtml("页面链接错误");
				return;
			}
			setAttr("projectInfo", result);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		render("/pages/project/project-nav.jsp");
	}
}
