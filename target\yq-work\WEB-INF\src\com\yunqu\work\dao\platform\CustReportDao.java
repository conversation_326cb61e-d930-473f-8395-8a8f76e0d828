package com.yunqu.work.dao.platform;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "CustReportDao")
public class CustReportDao extends AppDaoContext {

	@WebControl(name = "businessNumByMonth",type = Types.LIST)
	public JSONObject businessNumByMonth() {
		EasySQL sql = new EasySQL();
		sql.append("select left(REPLACE(create_time,'-',''),6) MONTH_ID,count(1) count from yq_crm_business ");
		sql.append("GROUP BY left(REPLACE(create_time,'-',''),6) ORDER BY MONTH_ID desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "followNumByUser",type = Types.LIST)
	public JSONObject followNumByUser() {
		EasySQL sql = new EasySQL();
		sql.append("select create_user_name,count(1) count,sum(follow_day) follow_day from yq_crm_follow GROUP BY create_user_name ");
		sql.append("GROUP BY create_user_name ORDER BY count desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "businessArea",type = Types.LIST)
	public JSONObject businessArea() {
		EasySQL sql = new EasySQL();
		sql.append("select BUSINESS_AREA,count(1) count from yq_crm_business ");
		sql.append("GROUP BY BUSINESS_AREA");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	
	
}
