<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>费用报销审批</title>
	<style>
		.layui-table td, .layui-table th{padding: 8px;}
		.layui-tab-content{background-color: #fff;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.gray-bg{background-color: #e8edf7;}
		.layui-icon{font-size: 12px;}
		.updateHzAmount{display: none;}
		.extTr td{border: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo">
                <input type="hidden" name="flowCode" value="finance_bx"/>
                <input type="hidden" id="businessId" name="businessId" value="${param.businessId}"/>
                <input type="hidden" id="resultId" name="resultId" value="${param.resultId}"/>
				<div class="layui-row" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
					<div class="layui-col-md12">
						<div class="layui-card">
						  <div class="layui-card-header text-c" style="font-size: 20px;height: 60px;line-height: 60px;">
						  		广州云趣信息技术有限公司-费用报销
						  		
						  		<div class="pull-right mr-20 hidden-print">
							  		<div class="btn-group btn-group-sm">
							  			<button class="btn btn-sm btn-default" type="button" onclick="window.print();">打印</button>
							  			<yq:user userId="${applyInfo.applyBy}">
							  				<c:if test="${applyInfo.applyState=='0'}">
								  				<button class="btn btn-default btn-xs" onclick="editFlow();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
							  				</c:if>
							  				<c:if test="${applyInfo.applyState=='21'}">
								  				<button class="btn btn-danger btn-xs" onclick="editFlow();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 修改申请</button>
							  				</c:if>
							  				<c:if test="${applyInfo.applyState=='0'||applyInfo.applyState=='21'}">
								  				<button class="btn btn-warning btn-xs" onclick="delFlow();" type="button"><i class="fa fa-trash-o" aria-hidden="true"></i> 删除</button>
							  				</c:if>
							  			</yq:user>
							  			<EasyTag:res resId="HZ_BX_MONEY">
								  			<yq:user userId="${applyInfo.applyBy}" hasPermission="false">
								  				<c:if test="${applyInfo.applyState=='0'||applyInfo.applyState=='21'}">
									  				<button class="btn btn-warning btn-xs" onclick="delFlow();" type="button"><i class="fa fa-trash-o" aria-hidden="true"></i> 删除</button>
								  				</c:if>
								  			</yq:user>
											<button class="btn btn-info btn-xs" onclick="$('.updateHzAmount').show();$('.hzAmountVal').hide();" type="button">核准金额</button>
							  			</EasyTag:res>
									</div>
					  		</div>
						  </div>
						  <div class="layui-card-body">
						    	<table class="table table-vzebra">
							  		<tr>
							  			<td style="width: 120px;">当前处理人</td>
							  			<td style="width: 40%;">
							  				<span name="checkName"></span>
							  			</td>
							  			<td style="width: 120px;">申请时间</td>
							  			<td>
							  				<span name="applyTime"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td style="width: 120px;">当前状态</td>
							  			<td>
							  				<span name="nodeName"></span>
							  			</td>
							  			<td style="width: 120px;">单号</td>
							  			<td>
							  				<span name="applyNo"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>申请人</td>
							  			<td>
							  				<span name="applyName"></span>
							  			</td>
							  			<td>部门</td>
							  			<td>
							  				<span name="deptName"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>说明</td>
							  			<td colspan="3">
											<span name="applyRemark"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>付款方式</td>
							  			<td colspan="3">
							  				<span name="business.PAY_TYPE"></span>
							  				<span name="business.CONTACT_UNIT" class="ml-5"></span>
							  				<span name="business.CONTACT_BANK" class="ml-5"></span>
							  				<span name="business.CONTACT_BANK_NO" class="ml-5"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>费用总计</td>
							  			<td>
							  				<span name="business.HZ_MONEY"></span>
							  			</td>
							  			<td>冲帐金额</td>
							  			<td>
							  				<span name="business.REVERSE_MONEY"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>应付金额</td>
							  			<td>
							  				<span name="business.PAY_MONEY"></span>
							  			</td>
							  			<td>大写</td>
							  			<td>
							  				<span name="business.PAY_MONEY_DX"></span>
							  			</td>
							  		</tr>
							  	</table>
						  </div>
						</div>
					</div>
				</div>
				<div class="layui-row" style="margin-top: 0px;">
					<div class="layui-col-md12">
						<div class="layui-card">
						  <div class="layui-card-body">
							 <div class="layui-table-box" style="min-height: 100px;">
								 <table class="layui-table" style="margin-top: -1px;">
								   <thead>
								    <tr>
								      <th>序号</th>
								      <th>日期</th>
								      <th>费用类型</th>
								      <th class="hidden-print">费用说明</th>
								      <th>发票类型</th>
								      <th>金额</th>
								      <th>核准金额</th>
								      <th>部门</th>
								      <th>项目号</th>
								      <th>产品线</th>
								      <th class="visible-print">会计科目</th>
								      <th>税率(%)</th>
								      <th>税金</th>
								      <th>金额</th>
								    </tr> 
								  </thead>
								  <tbody data-mars="BxDao.bxItems" data-template="bxItems">
								  
								  
								  </tbody>
								</table>
							</div>
							 <div class="layui-table-box" style="min-height: 100px;overflow-x:auto;">
							 	 <table class="layui-table" style="margin-top: 10px;">
								   <thead>
								      <tr>
									      <th>序号</th>
									      <th>节点名称</th>
									      <th>经办人</th>
									      <th>审批结果</th>
									      <th class="hidden-print">接收时间</th>
									      <th>审批时间</th>
									      <th>审批描述</th>
								      </tr>
								  </thead>
								  <tbody class="hidden">
								    <tr>
								      <th>1</th>
								      <th>申请填写</th>
								      <th>${applyInfo.applyName}</th>
								      <th></th>
								      <th class="hidden-print"></th>
								      <th>${applyInfo.applyTime}</th>
								      <th>--</th>
								    </tr> 
								  </tbody>
								  <tbody data-mars="FlowDao.approveResult" data-template="approveList">
								  
								  
								  </tbody>
								</table>
								 <table class="layui-table text-nowrap" id="bxCheckTable" style="margin-top: -1px;"></table>
							</div>
					  </div>
					</div>
				</div>
			</div>
			
			<c:if test="${param.isApprovalFlag=='1'}">
			<div class="layui-row hidden-print" style="margin-top: 0px;">
				<div class="layui-col-md12">
					<div class="layui-card">
					  <div class="layui-card-body">
					 		<table class="table table-edit table-vzebra">
							   	<tr>
							   		<td style="width: 120px;">审批结果：</td>
							   		<td>
							   			<label class="radio radio-inline radio-success">
							            	<input type="radio" value="1" name="checkResult" checked="checked"> <span>通过</span>
							            </label>
							            <label class="radio radio-inline radio-success">
							            	<input type="radio" value="2" name="checkResult"> <span>退回</span>
							            </label>
							   		</td>
							   	</tr>
							   	<tr>
							   		<td style="vertical-align: top;margin-top: 20px;">审批描述：</td>
							   		<td>
							   			<textarea style="height: 80px" name="checkDesc" class="form-control"></textarea>
							   		</td>
							   	</tr>
							   	<tr>
							   		<td colspan="2" style="text-align: left;">
								   		<button class="btn btn-info btn-sm" style="margin-left:120px;" type="button" onclick="addCheckOrder();">提交</button>
							   		</td>
							   	</tr>
							  </table>
							   <br>
							   <br>
							   <br>
							   <br>
							</div>
					  </div>
					</div>
			   </div>
			  </c:if>
						  
</form>
</EasyTag:override>

<EasyTag:override name="script">

<script id="bxItems" type="text/x-jsrender">
  	 {{for data}}
  		<tr>
  			<td>{{:#index+1}}</td>
  			<td>{{:BX_DATE}}</td>
  			<td>{{:FEE_TYPE}}</td>
  			<td class="hidden-print">{{:FEE_DESC}} <a href="javascript:void(0);" onclick="itemDetail('{{:ITEM_ID}}')" style="text-decoration:underline;" class="pull-right">详情</a></td>
  			<td>{{:INVOICE_TYPE}}</td>
  			<td>{{:AMOUNT}}</td>
  			<td><span class="hzAmountVal">{{:HZ_AMOUNT}}</span><input type="number" class="form-control input-sm updateHzAmount" value="{{:HZ_AMOUNT}}" style="width:80px;" onchange="updateHzAmount(this.value,'{{:ITEM_ID}}','{{:BUSINESS_ID}}')"/></td>
  			<td>{{:DEPT_NAME}}</td>
  			<td>{{:CONTRACT_NAME}}</td>
  			<td>{{:PRODUCT_LINE}}</td>
  			<td class="visible-print">{{:SUBJECT_NO}}&nbsp;{{:SUBJECT_NAME}}</td>
  			<td>{{:TAX_RATE}}</td>
  			<td>{{:TAXES}}</td>
  			<td>{{:R_AMOUNT}}</td>
  		</tr>
  	  {{/for}}
</script>
<script id="approveList" type="text/x-jsrender">
  	 {{for data}}
  		<tr {{if CHECK_RESULT=='0'}} class="hidden-print" {{/if}}>
  			<td>{{:#index+1}}</td>
  			<td>{{:NODE_NAME}}</td>
  			<td>{{:CHECK_NAME}}</td>
  			<td>{{if NODE_ID!='0'}} {{call:CHECK_RESULT fn='checkResultLable'}} {{/if}}</td>
  			<td class="hidden-print">{{:GET_TIME}}</td>
  			<td>{{:CHECK_TIME}}</td>
  			<td>{{:CHECK_DESC}}</td>
  		</tr>
  	  {{/for}}
	 {{if data.length==0}}
		<tr><td  colspan="7">暂无数据</td></tr>
	{{/if}}
</script>
	<script type="text/javascript">
		
		$(function(){
			var device = layui.device();
			if(device.mobile){
				$('.table,.layui-table-box').addClass('table-responsive');
				$('.table').parent().addClass('table-responsive');
				$('.table,.layui-table').addClass('text-nowrap');
				$('.layui-card-header,.btn-group').hide();
			}
			layui.use(['element','form'],function(){
				var element = layui.element;
				var form = layui.form;
				form.render();
			});
			
			$("#flowForm").render({success:function(result){

			}});
			
		});
		
		function checkResultLable(checkResult){
			 var json = {'1':'通过','2':'拒绝'};
			 return json[checkResult]||'待审批';
		}
		
		function addCheckOrder(){
			var checkResult = $("[name='checkResult']:checked").val();
			var checkDesc = $("[name='checkDesc']").val();
			var businessId = $("#businessId").val();
			var resultId = $("#resultId").val();
			if(businessId==''||resultId==''){
				layer.msg('请从审批页面入口进来',{icon:7,offset:'50px'});
				return;
			}
			if(checkResult==undefined){
				layer.msg('请选择审批结果',{icon:7,offset:'50px'});
				return;
			}
			if(checkResult=='2'&&checkDesc==''){
				layer.msg('请输入退回理由',{icon:7,offset:'50px'});
				return;
			}else{
				if(checkDesc==''){
					checkDesc='同意';
				}
			}
			var data = {flowCode:'finance_bx',checkResult:checkResult,checkDesc:checkDesc,businessId:businessId,resultId:resultId};
			ajax.remoteCall("${ctxPath}/servlet/bx?action=doApprove",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.closeTab({callback:function(){
							popup.openTab({id:'flow_my_todo',url:'/yq-work/pages/flow/my/flow-todo.jsp',title:'我的待办',reload:true})
						}});
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function editFlow(){
			var businessId = $("#businessId").val();
			popup.openTab({id:'bxFlowEdit',url:'/yq-work/servlet/flow?id=finance_bx&mode=edit&businessId='+businessId,data:{},title:'编辑表单'});
		}
		
		function itemDetail(itemId){
			var data = {itemId:itemId};
			popup.layerShow({id:'showFeeInfo',title:'费用说明',area:['550px','400px'],offset:'30px',url:'/yq-work/pages/flow/bx/include/fee-detail.jsp',data:data});
		}
		
		function delFlow(){
			layer.confirm('确认删除吗，不可恢复',{offset:'20px'},function(index){
				layer.close(index);
				var businessId = $("#businessId").val();
				ajax.remoteCall("${ctxPath}/servlet/bx?action=delApply",{businessId:businessId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							popup.closeTab();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		function updateHzAmount(hzAmount,itemId,businessId){
			var payMoneyDx = digitUppercase(hzAmount);
			ajax.remoteCall("${ctxPath}/servlet/bx?action=updateHzMoney",{hzAmount:hzAmount,payMoneyDx:payMoneyDx,itemId:itemId,businessId:businessId},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>