<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div class="input-group input-group-sm ml-5" >
    <span class="input-group-addon">成本日期</span>
    <input name="costBeginDate" class="form-control input-sm date-inline" onchange="reloadCostList()" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
    <input name="costEndDate" class="form-control input-sm date-inline" onchange="reloadCostList()" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
</div>
<script type="text/html" id="costToolbar">
    <button type="button" class="btn btn-sm btn-info" onclick="costList.add()">+ 新增成本</button>
</script>

<table id="costTable" class="layui-table"></table>

<script type="text/javascript">
    var costList = {
        init: function(){
            $("#PlatformDetailForm").initTable({
                mars:'CustOperateDao.costPageList',
                id:'costTable',
                page:true,
                toolbar: '#costToolbar',
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align:'left'
                    },{
                        field: '',
                        title: '操作',
                        width: 100,
                        align: 'center',
                        templet: function(d){
                            var html = '';

                            if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
                                html += '<span style="color:#ccc;" title="该成本已被结算，不允许修改">编辑</span>';
                                html += ' <span style="color:#ccc;" title="该成本已被结算，不允许删除">删除</span>';
                            } else {
                                html += ' <a lay-event="costList.edit">编辑</a>';
                                html += ' <a lay-event="costList.del">删除</a>';
                            }
                            return html;
                        }
                    },{
                        field: 'SETTLEMENT_ID',
                        title: '结算状态',
                        width: 100,
                        align: 'center',
                        templet: function(d){
                            if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
                                return '<span style="color:#ff5722;">已结算</span>';
                            } else {
                                return '<span style="color:#009688;">未结算</span>';
                            }
                        }
                    },{
                        field: 'DATE_ID',
                        title: '成本日期',
                        width: 100
                    },{
                        field: 'CUST_NAME',
                        title: '客户名称',
                        width:150,
                        templet:'<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
                    },{
                        field: 'CONTRACT_NAME',
                        title: '合同名称',
                        width:150,
                        templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_NAME}}</a></div>'
                    },{
                        field: 'COST_TYPE',
                        title: '成本类型',
                        width:80,
                        align: 'center'
                    },{
                        field: 'COST_NAME',
                        title: '成本名称',
                        width:150
                    },{
                        field: 'COST_OWNER',
                        title: '成本归属',
                        width:80,
                        align: 'center',
                        templet: function(row){
                            var types = ['平台成本','客户成本','其他'];
                            return types[row.COST_OWNER] || '其他';
                        }
                    },{
                        field: 'RELATE_TYPE',
                        title: '成本来源',
                        width:80,
                        align: 'center',
                        templet: function(row){
                            var types = ['其他','采购订单','个人报销'];
                            return types[row.RELATE_TYPE] || '其他';
                        }
                    },{
                        field: 'UNIT_PRICE',
                        title: '单价',
                        width:80,
                        align: 'center'
                    },{
                        field: 'NUMBER',
                        title: '数量',
                        width:60,
                        align: 'center'
                    },{
                        field: 'TOTAL_AMOUNT',
                        title: '总金额',
                        width:80,
                        align: 'center'
                    },{
                        field: 'TAX_RATE',
                        title: '税率%',
                        width:80,
                        align: 'center'
                    },{
                        field: 'NO_TAX_AMOUNT',
                        title: '不含税金额',
                        width:100,
                        align: 'center'
                    },{
                        field: 'ORDER_NO',
                        title: '采购订单号',
                        width:110,
                        // templet:'<div><a href="javascript:;" onclick="purchaseDetail(\'{{d.ORDER_ID}}\',\'{{d.CUST_ID}}\')">{{d.ORDER_NO}}</a></div>'
                    },{
                        field: 'ORDER_TIME',
                        title: '采购下单时间',
                        width:110
                    },{
                        field: 'PAY_TIME',
                        title: '付款时间',
                        width:110
                    },{
                        field: 'SUPPLIER_NAME',
                        title: '供应商',
                        width:120,
                        templet:'<div><a href="javascript:;" onclick="supplierDetail(\'{{d.SUPPLIER_ID}}\')">{{d.SUPPLIER_NAME}}</a></div>'
                    },{
                        field: 'BX_APPLY_ID',
                        title: '报销流程',
                        width:100,
                        hide:true,
                        templet: function(row){
                            return row.BX_APPLY_ID ? '<a href="javascript:;" onclick="bxDetail(\''+row.BX_APPLY_ID+'\')">查看流程</a>' : '';
                        }
                    },{
                        field: 'REMARK',
                        title: '备注',
                        width:150
                    }
                ]],
                done:function(res){
                    if(res.totalRow != undefined){
                        $("[name='extend.COST_COUNT']").text(res.totalRow);
                    }
                }
            });
        },
        add: function(){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'新增成本',
                offset:'20px',
                area:['700px','600px'],
                url:"${ctxPath}/pages/crm/platform/include/add-cost.jsp",
                data:{platformId:platformId,platformName:platformInfo.PLATFORM_NAME,relateType:"1"}
            });
        },
        edit: function(data){
            if(data.SETTLEMENT_ID && data.SETTLEMENT_ID.trim() !== '') {
                layer.alert('该成本记录已被结算（结算编号：' + (data.SETTLEMENT_NO || data.SETTLEMENT_ID) + '），不允许修改', {icon: 0});
                return;
            }
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'编辑成本',
                offset:'20px',
                area:['700px','600px'],
                url:"${ctxPath}/pages/crm/platform/include/add-cost.jsp",
                data:{costId:data.COST_ID,relateType:data.RELATE_TYPE||"1"}
            });
        },
        del: function(data){
            if(data.SETTLEMENT_ID && data.SETTLEMENT_ID.trim() !== '') {
                layer.alert('该成本记录已被结算（结算编号：' + (data.SETTLEMENT_NO || data.SETTLEMENT_ID) + '），不允许修改', {icon: 0});
                return;
            }
            layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function(){
                ajax.remoteCall("${ctxPath}/servlet/custOperate?action=deleteCost",{costId:data.COST_ID},function(result){
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            reloadCostList();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            });
        }
    };

    function reloadCostList(){
        $("#PlatformDetailForm").queryData({id:'costTable',page:true});
    }

    function purchaseDetail(orderId,custId){
        popup.openTab({
            id:'orderDetail',
            title:'采购详情',
            url:"${ctxPath}/pages/erp/order/order-detail.jsp",
            data:{orderId:orderId,custId:custId}
        });
    }

    function supplierDetail(supplierId){
        popup.openTab({
            id:'supplierDetail',
            url:"${ctxPath}/pages/erp/supplier/supplier-detail.jsp",
            title:'供应商详情',
            data:{supplierId:supplierId}
        });
    }

    function bxDetail(applyId){
        popup.openTab({
            id: 'flowDetail',
            title: '报销流程详情',
            url: "/yq-work/web/flow",
            data: {
                businessId: applyId,
                flowCode: 'finance_bx',
                handle: 'detail',
            }
        });
    }
</script>
