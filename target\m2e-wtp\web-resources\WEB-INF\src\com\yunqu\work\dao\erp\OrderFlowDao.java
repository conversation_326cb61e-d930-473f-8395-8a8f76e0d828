package com.yunqu.work.dao.erp;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "OrderFlowDao")
public class OrderFlowDao extends AppDaoContext{
	
	@WebControl(name = "getApplyName",type = Types.TEXT)
	public JSONObject getApplyName() {
		return getJsonResult(getUserPrincipal().getUserName()+"的采购付款申请");
	}
	
	@WebControl(name = "paymentList",type=Types.PAGE)
	public JSONObject paymentList() {
		EasySQL sql = getEasySQL("select t1.*,t2.*,t4.name supplier_name from yq_erp_order_payment t1,yq_flow_apply t2,");
		sql.append("yq_erp_supplier t4");
		sql.append(" where 1=1");
		sql.append("and t2.apply_id = t1.business_id");
		sql.append("and t1.supplier_id = t4.supplier_id");
		sql.appendLike(param.getString("orderNo"),"and t1.order_nos like ?");
		sql.appendLike(param.getString("paymentNo"),"and t1.payment_no like ?");
		sql.append(param.getString("payState"),"and t1.pay_state = ?");
		sql.append("order by t2.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	
	@WebControl(name = "paymentOrderList",type = Types.OTHER)
	public JSONObject paymentOrderList() {
		EasySQL sql = getEasySQL("select t2.*,t1.APPLY_REMARK,t1.has_pay_amount,t1.make_amount,t1.apply_amount,t1.has_apply_amount,t1.apply_time from yq_erp_order_payment_detail t1 ,yq_erp_order t2 where FIND_IN_SET(t2.order_id,t1.order_id) ");
		sql.append(param.getString("businessId"),"and t1.payment_id= ?",false);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "getPaymentInfo",type = Types.RECORD)
	public JSONObject getPaymentInfo() {
		String businessId = param.getString("businessId");
		return queryForRecord("select * from yq_erp_order_payment where business_id = ?", businessId);
	}



}
