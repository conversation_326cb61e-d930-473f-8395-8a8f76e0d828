<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>事务协同</title>
	<link href="${ctxPath}/static/css/sidebar.css" rel="stylesheet">
	<style type="text/css">
		.layui-table-cell{font-size: 13px;}
		.glyphicon-menu-right{line-height: 40px!important;}
		.sidebar-item a{
			font-size: 13px!important;
			height: 40px!important;
			line-height: 40px!important;
		}
		.right-content .container-fluid,pt{
			padding: 0px;
		}	
		.page-box .left-sidebar{width: 130px!important;}
		.page-box .right-content{margin-left: 130px!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
  <div class="page-box">
	  <div class="left-sidebar">
           <div class="left-sidebar-item">
           		 <div class="sidebar-item">
                   <a href="javascript:void(0);" class="sidebar-nav-sub-title active"><i class="sidebar-icon glyphicon glyphicon-menu-right"></i>事务协同</a>
                   <ul class="sidebar-nav-sub">
                      <li>
                      	 <a class="wirte" href="javascript:void(0)" data-url="${ctxPath}/pages/affair/affair-edit.jsp?isDiv=1&sendType=0&userName=${param.userName}"><i class="layui-icon layui-icon-right"></i> 立即发送</a>
                      	 <a class="wirte" href="javascript:void(0)" data-url="${ctxPath}/pages/affair/affair-edit.jsp?isDiv=1&sendType=1"><i class="layui-icon layui-icon-right"></i> 定时发送</a>
			             <a class="send" href="javascript:void(0)" data-url="${ctxPath}/pages/affair/affair-send.jsp?isDiv=1"><i class="layui-icon layui-icon-right"></i> 发件箱</a>
			             <a class="receive" href="javascript:void(0)" data-url="${ctxPath}/pages/affair/affair-received.jsp?isDiv=1"><i class="layui-icon layui-icon-right"></i> 收件箱 <span data-mars="AffairDao.receivedCount" id="receivedCount"></span></a>
                      </li>
                   </ul>
                 </div>
           </div>
       </div>
  	   <div class="right-content">
  		
  	   </div>
  </div>
        
        
</EasyTag:override>
<EasyTag:override name="script">
	<script>
    	$(function(){
    		$(".page-box").render({success:function(result){
    			$("#receivedCount").prepend("(");
    			$("#receivedCount").append(")");
    		}});
    		$('body').on('click','.sidebar-nav-sub-title',function(e){
    			$(this).toggleClass('active')
    		})
    		$(".left-sidebar-item a").click(function(){
    			var t=$(this);
    			var url=t.data("url");
    			if(url){
    				$.get(url,{},function(result){
	  					$(".right-content").html(result);
    				});
	    			$(".left-sidebar-item a").removeClass("activeItem");
	    			t.addClass("activeItem");
    			}
    		});
    		
    		var source = '${param.source}';
    		if(source=='write'){
			    $(".left-sidebar-item .wirte").first().click();
    		}else if(source=='send'){
			    $(".left-sidebar-item .send").first().click();
    		}else{
			    $(".left-sidebar-item a").last().click();
    		}
    	});
    	
    
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>