<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购详情</title>
	<style>
		.form-horizontal{width: 100%;}
		.layui-table-cell a{font-size: 12px;color: #3E84E9;cursor: pointer;}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 14px;}
		.layui-timeline-item:hover a{opacity:1;}
		.layui-tab-card>.layui-tab-title{background-color: #fff;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		.layui-timeline-content p{font-size: 13px;color: #666;}
		.layui-timeline-title{margin-bottom: 2px;}
		.layui-timeline-item{margin-bottom: 2px;}
		.layui-text h3{font-size: 16px;}
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.gray-bg{background-color: #e8edf7;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="OrderDetailForm">
			    <input type="hidden" value="${param.orderId}" id="orderId" name="orderId"/>
 	  	        <input type="hidden" value="${param.orderId}" name="pFkId"/>
 	  	        <input type="hidden" value="${param.custId}" name="custId"/>
 	  	        <input type="hidden" value="${param.resultId}" id="resultId" name="resultId"/>
				<div style="padding: 5px" class="ibox-content" data-mars="OrderDao.applyInfo" data-mars-prefix="apply.">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md8">
		 	  	         <input type="hidden" value="${param.reviewId}" id="reviewId" name="apply.REVIEW_ID"/>
		 	  	         <input type="hidden" value="${param.paymentId}" id="paymentId" name="apply.PAYMENT_ID"/>
					     <i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 18px;" name='apply.CONTRACT_NAME'></span>
					  </div>
					  <div class="layui-col-md4">
					  		<EasyTag:hasRole roleId="ORDER_MGR">
						  		<div class="pull-right mr-50">
							  		<div class="btn-group btn-group-sm">
										<button class="btn btn-info btn-xs" onclick="addReviewResult();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 填写评审意见</button>
									</div>
						  		</div>
					  		</EasyTag:hasRole>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding:10px 15px;">
					  <div class="layui-col-md2">
					  	<span class="h-title">单据编号</span>
					  	<span class="h-value" name="apply.ORDER_NO"></span>
					  	<input type="hidden" name="apply.SUPPLIER_ID"/>
					  	<input type="hidden" name="apply.CONTRACT_ID"/>
					  </div>
					  <div class="layui-col-md3">
					  	<span class="h-title">供应商</span>
					  	<span class="h-value" name="apply.SUPPLIER_NAME">--</span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">状态</span>
					  	<span class="h-value" name="apply.LAST_FOLLOW_LABEL">--</span>
					  </div>
					  <div class="layui-col-md3">
					  	<span class="h-title">客户名称</span>
					  	<span class="h-value" name="apply.CUST_NAME"></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">更新时间</span>
					  	<span class="h-value" name="apply.UPDATE_TIME"></span>
					  </div>
					</div>
				</div>
				<div class="layui-row">
					<div class="layui-col-md12">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li>基本信息</li>
						    <li class="layui-this">物料信息(<span id="goodsCount">0</span>)</li>
						    <li>附件列表(<span id="fileCount">0</span>)</li>
						    <li>评审记录(<span id="checkCount">0</span>)</li>
						  </ul>
						  <div class="layui-tab-content" style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
						    <div class="layui-tab-item">
								<jsp:include page="include/base-info.jsp"/>
						    </div>
						    <div class="layui-tab-item layui-show">
						    	<jsp:include page="include/goods-detail.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/file-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="review/review-check-list.jsp"/>
						    </div>
						  </div>
					</div>
					</div>
				</div>
		</form>
		<form  id="orderDetailFileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="orderDetailLocalfile" onchange="orderUploadFile()"/>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   
		jQuery.namespace("OrderDetail");
	    
		OrderDetail.orderId='${param.orderId}';
		
		var orderId = '${param.orderId}';
		
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
			});
			$("#OrderDetailForm").render({success:function(result){
				
			}});  
		});
		
		function addReviewResult(){
			var resultId = $('#resultId').val();
			var reviewId = $('#reviewId').val();
			popup.layerShow({id:'addResult',area:['500px','400px'],title:'填写评审意见',url:'${ctxPath}/pages/erp/order/review/review-check.jsp',data:{orderId:orderId,resultId:resultId,reviewId:reviewId}});
		}
		
		function contractDetail(){
			var contractId = $("[name='apply.CONTRACT_ID']").val();
			var custId = $("[name='custId']").val();
			popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/pages/crm/contract/contract-detail.jsp',data:{contractId:contractId,custId:custId}});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>