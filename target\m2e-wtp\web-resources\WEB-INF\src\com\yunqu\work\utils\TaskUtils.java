package com.yunqu.work.utils;

import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

public class TaskUtils {
	
	public static void setTaskConditionWithState(JSONObject param,String deptId,EasySQL sql){
		String state = param.getString("taskState");
		if("25".equals(state)){
			sql.append("and t1.task_state in (10,20)");
			sql.append(EasyDate.getCurrentDateString(),"and t1.deadline_at < ?");
		}else{
			if(StringUtils.notBlank(state)) {
				sql.appendIn(state.split(","),"and t1.task_state");
			}
		}
		TaskUtils.setTaskCondition(param,deptId,sql);
		
	}
	
	public static void setTaskCondition(JSONObject param,String deptId,EasySQL sql){
		sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
		String taskBusinessId = param.getString("taskBusinessId");
		if("0".equals(taskBusinessId)) {
			sql.append("and t1.BUSINESS_ID <>'' ");
		}else {
			sql.append(taskBusinessId,"and t1.BUSINESS_ID = ?");
		}
		sql.append(param.getString("yearId"),"and left(t1.month_id,4) = ?");
		sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
		sql.append(param.getString("moduleId"),"and t1.MODULE_ID = ?");
		sql.append(param.getString("moduleName"),"and t1.MODULE_NAME = ?");
		
		String groupId = param.getString("groupId");
		if(StringUtils.notBlank(groupId)&&"0".equals(groupId)) {
			sql.append("and t1.GROUP_ID  <>''");
		}else {
			sql.append(groupId,"and t1.GROUP_ID = ?");
		}
		sql.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
		sql.append(param.getString("deptId"),"and t1.dept_id = ?");
		sql.appendRLike(param.getString("assignDeptName"),"and t1.assign_dept_name like ?");
		sql.append(param.getString("creator"),"and t1.creator = ?");
		String isSelfDept = param.getString("isSelfDept");
		String selectUser = param.getString("selectUser");
		if(!"1".equals(selectUser)){
			sql.append(param.getString("assignUserId"),"and t1.assign_user_id = ?");
		}
		if("1".equals(isSelfDept)){
			sql.appendIn(deptId.split(","),"and t1.assign_dept_id");
		}else{
			sql.append(param.getString("assignDeptId"),"and t1.assign_dept_id = ?");
		}
		String createTime = param.getString("createTime");
		if(StringUtils.isNotBlank(createTime) && createTime.split(" 到 ").length > 1){
			String[] arr = createTime.split(" 到 ");
			sql.append(arr[0]+" 00:00:00",  "and t1.CREATE_TIME >= ?");
			sql.append(arr[1]+" 23:59:59",  "and t1.CREATE_TIME <= ?");
		}
		String finishTime = param.getString("finishTime");
		if(StringUtils.isNotBlank(finishTime) && finishTime.split(" 到 ").length > 1){
			String[] arr = finishTime.split(" 到 ");
			sql.append(arr[0]+" 00:00:00",  "and t1.finish_time >= ?");
			sql.append(arr[1]+" 23:59:59",  "and t1.finish_time <= ?");
		}
		String updateTime = param.getString("updateTime");
		if(StringUtils.isNotBlank(updateTime) && updateTime.split(" 到 ").length > 1){
			String[] arr = updateTime.split(" 到 ");
			sql.append(arr[0]+" 00:00:00",  "and t1.update_time >= ?");
			sql.append(arr[1]+" 23:59:59",  "and t1.update_time <= ?");
		}
		String planDate = param.getString("planDate");
		if(StringUtils.isNotBlank(planDate) && planDate.split(" 到 ").length > 1){
			String[] arr = planDate.split(" 到 ");
			sql.append("and ((");
			sql.append(arr[0]+" 00:00",  "t1.plan_started_at >= ?");
			sql.append(arr[1]+" 23:59",  "and t1.deadline_at <= ?");
			sql.append(")");
			sql.append("or (");
			sql.append(arr[0]+" 00:00",  "t1.deadline_at >= ?");
			sql.append(arr[1]+" 23:59",  "and t1.plan_started_at <= ?");
			sql.append("))");
		}
		String beforeDay = param.getString("beforeDay");
		if(StringUtils.notBlank(beforeDay)) {
			sql.append(DateUtils.getBeforeDate(Integer.valueOf(beforeDay), "yyyy-MM-dd HH:mm:ss"),"and t1.create_time > ?");
		}
	}
}
