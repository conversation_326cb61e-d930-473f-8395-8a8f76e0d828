<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div class="input-group input-group-sm ml-5" >
    <span class="input-group-addon">开票日期</span>
    <input name="kpBeginDate" class="form-control input-sm date-inline" onchange="reloadInvoiceList()" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
    <input name="kpEndDate" class="form-control input-sm date-inline" onchange="reloadInvoiceList()" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
</div>
<script type="text/html" id="invoiceToolbar">
    <button type="button" class="btn btn-sm btn-info" onclick="invoiceList.add()">+ 新增发票</button>
</script>

<table id="invoiceTable" class="layui-table"></table>

<script type="text/javascript">
    var invoiceList = {
        init: function(){
            $("#PlatformDetailForm").initTable({
                mars:'InvoiceDao.pageList',
                id:'invoiceTable',
                toolbar:'#invoiceToolbar',
                page:true,
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align:'left'
                    },{
                        field: '',
                        title: '操作',
                        width: 120,
                        align: 'center',
                        templet: function(d) {
                            var html = '';
                            if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
                                html += '<span style="color:#ccc;" title="该发票已被结算，不允许修改">编辑</span>';
                                html += ' <span style="color:#ccc;" title="该发票已被结算，不允许删除">删除</span>';
                            } else {
                                html += ' <a lay-event="invoiceList.edit">编辑</a>';
                                html += ' <a lay-event="invoiceList.del">删除</a>';
                            }
                            return html;
                        }
                    },{
                        field: 'SETTLEMENT_ID',
                        title: '结算状态',
                        width: 100,
                        align: 'center',
                        templet: function(d){
                            if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
                                return '<span style="color:#ff5722;">已结算</span>';
                            } else {
                                return '<span style="color:#009688;">未结算</span>';
                            }
                        }
                    },{
                        field: 'INVOICE_NO',
                        title: '发票编号',
                        align:'left',
                        minWidth:100,
                        style:'color:#1E9FFF;cursor:pointer',
                        event:'invoiceList.detail'
                    },{
                        field: 'INVOICE_TYPE',
                        title: '开票类型',
                        align:'center',
                        width:110
                    },{
                        field: 'MARK_DATE',
                        title: '开票日期',
                        align:'center',
                        width:100
                    },{
                        field: 'MARK_MONEY',
                        title: '开票金额',
                        align:'center',
                        width:100
                    },{
                        field: 'TAX_RATE',
                        title: '税率(%)',
                        align:'center',
                        width: 60
                    },{
                        field: 'TOTAL_TAX',
                        title: '税额',
                        align:'center',
                        width: 80
                    },{
                        field: 'AMOUNT_IN_FIGUERS',
                        title: '不含税金额',
                        align:'center',
                        width: 100
                    },{
                        field: 'REMARK',
                        title: '备注',
                        align:'center',
                        width: 100
                    },{
                        field: 'CUST_NAME',
                        title: '客户名称',
                        minWidth:150,
                        templet:'<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
                    },{
                        field: 'CONTRACT_NAME',
                        title: '合同名称',
                        minWidth:150,
                        templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_NAME}}</a></div>'
                    },{
                        field: 'CREATE_NAME',
                        title: '创建人',
                        align: 'center',
                        width:80
                    },{
                        field: 'CREATE_TIME',
                        title: '创建时间',
                        width:140
                    }
                ]],
                done:function(res){
                    if(res.total != undefined){
                        $("[name='extend.INVOICE_COUNT']").text(res.total);
                    }
                }
            });
        },
        add: function(){
            popup.layerShow({
                type:1,
                full:fullShow(),
                maxmin:true,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                offset:'20px',
                area:['800px','500px'],
                url:"${ctxPath}/pages/crm/invoice/invoice-edit.jsp",
                title:'新增发票',
                data:{platformId:platformId,platformName:platformInfo.PLATFORM_NAME}
            });
        },
        edit: function(data){
            if(data.SETTLEMENT_ID && data.SETTLEMENT_ID.trim() !== '') {
                layer.alert('该发票记录已被结算（结算编号：' + (data.SETTLEMENT_NO || data.SETTLEMENT_ID) + '），不允许修改', {icon: 0});
                return;
            }
            
            popup.layerShow({
                type:1,
                full:fullShow(),
                maxmin:true,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                offset:'20px',
                area:['800px','500px'],
                url:"${ctxPath}/pages/crm/invoice/invoice-edit.jsp",
                title:'编辑发票',
                data:{invoiceId:data.INVOICE_ID}
            });
        },
        detail: function(data){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'发票详情',
                offset:'r',
                area:['60%','100%'],
                url:"${ctxPath}/pages/crm/invoice/invoice-detail.jsp",
                data:{invoiceId:data.INVOICE_ID}
            });
        },
        del: function(data){
            if(data.SETTLEMENT_ID && data.SETTLEMENT_ID.trim() !== '') {
                layer.alert('该发票记录已被结算（结算编号：' + (data.SETTLEMENT_NO || data.SETTLEMENT_ID) + '），不允许删除', {icon: 0});
                return;
            }
            
            layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function(){
                ajax.remoteCall("${ctxPath}/servlet/invoice?action=del",data,function(result){
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            reloadInvoiceList();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            });
        }
    };

    function reloadInvoiceList(){
        console.log("onchange")
        $("#PlatformDetailForm").queryData({id:'invoiceTable',page:true});
    }
</script>