package com.yunqu.work.servlet.devops;

import java.io.File;
import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.CommonService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.StaffService;
/**
 *   安全漏洞管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/security/*")
public class SecurityServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		JSONObject params = getJSONObject();
		int state = params.getIntValue("state");
		EasyRecord model = new EasyRecord("YQ_OPS_SECURITY", "SECURITY_ID");
		model.setColumns(JsonKit.getJSONObject(params, "security"));
		model.set("CREATE_TIME",EasyDate.getCurrentDateString());
		model.set("DEPT_ID", getDeptId());
		model.set("CREATE_NAME",getUserName());
		model.set("CREATOR",getUserPrincipal().getUserId());
		try {
			if(state==1) {
				model.set("security_state", -1);
			}else {
				model.set("security_state", 0);
			}
			String receiverName = model.getString("RECEIVER_NAME");
			if(receiverName.length()>500) {
				model.set("RECEIVER_NAME",receiverName.substring(0,500));
			}
			this.getQuery().save(model);
			
			this.getQuery().executeUpdate("UPDATE YQ_OPS_SECURITY t1 LEFT JOIN (SELECT fk_id, GROUP_CONCAT(file_name) AS file_names FROM yq_files WHERE fk_id = ? GROUP BY fk_id) t2 ON t1.security_id = t2.fk_id SET t1.file_desc = t2.file_names WHERE t1.security_id = ?",model.getPrimaryValue(),model.getPrimaryValue());

			String receiverIdStr = params.getString("receiverIds");
			model.set("RECEIVER_ID",receiverIdStr);
			
			if(state==2) {
				this.sendMsg(model);
			}
			
			String[] receiverIds = receiverIdStr.split(",");
			for(String userId:receiverIds) {
				EasyRecord record = new EasyRecord("YQ_OPS_SECURITY_DO");
				record.put("security_id", model.getPrimaryValue());
				record.put("user_id", userId);
				record.put("user_name", StaffService.getService().getUserName(userId));
				record.put("do_desc", "");
				record.put("add_time", EasyDate.getCurrentDateString());
				record.put("add_by", getUserId());
				record.put("add_by_name", getUserName());
				this.getQuery().save(record);
			}
			if(receiverIds.length>0) {
				this.getQuery().executeUpdate("update yq_ops_security set do_people_count = ? where security_id = ?", receiverIds.length,model.getPrimaryValue());
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void sendMsg(EasyRecord model) throws SQLException {
		String mailtos = model.getString("RECIPIENT_ID");
		MessageModel messageModel=new MessageModel();
		messageModel.setData(this.getQuery().queryForRow("select * from yq_ops_security where security_id = ?", new Object[] {model.getPrimaryValue().toString()},new JSONMapperImpl()));
		messageModel.setReceiver(model.getString("RECEIVER_ID"));
		if(StringUtils.notBlank(mailtos)){
			mailtos = mailtos +","+getUserId();
			messageModel.setCc(mailtos.split(","));
		}
		messageModel.setFkId(model.getPrimaryValue().toString().toString());
		messageModel.setSender(getUserId());
		messageModel.setSendName(getUserName());
		messageModel.setTplName("newVersion.html");
		messageModel.setTitle(model.getString("SECURITY_TITLE"));
		messageModel.setFileList(CommonService.getService().getFileList(model.getPrimaryValue().toString()));
		EmailService.getService().sendTaskEmail(messageModel);
	}
	
	public EasyResult actionForUpdateFile(){
		String id = getJsonPara("id");
		try {
			this.getQuery().executeUpdate("update YQ_OPS_SECURITY t1 set t1.file_desc = (select GROUP_CONCAT(t2.file_name) from yq_files t2 where t2.fk_id = ? and t1.security_id = t2.fk_id) where t1.security_id = ?",id,id);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForUpdate(){
		JSONObject params = getJSONObject();
		try {
			EasyRecord model = new EasyRecord("YQ_OPS_SECURITY", "SECURITY_ID");
			model.setColumns(JsonKit.getJSONObject(params, "security"));
			model.set("update_time",EasyDate.getCurrentDateString());
			
			int versionState = model.getIntValue("SECURITY_STATE");
			int state = params.getIntValue("state");
			if(state==2&&versionState==-1) {
				this.sendMsg(model);
				model.set("security_state",0);
			}
			String receiverName = model.getString("RECEIVER_NAME");
			if(StringUtils.notBlank(receiverName)&&receiverName.length()>500) {
				model.set("RECEIVER_NAME",receiverName.substring(0,500));
			}
			this.getQuery().update(model);
			
//			String receiverIdStr = params.getString("receiverIds");
//			String[] receiverIds = receiverIdStr.split(",");
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}

	
	//删除附件
	public EasyResult actionForDelete(){
		String securityId = getJsonPara("securityId");
		try {
			this.getQuery().executeUpdate("delete from yq_ops_security where security_id = ?", securityId);
			String fkId = securityId;
			try {
				List<JSONObject> list = getQuery().queryForList("select * from yq_files where fk_id = ?", new Object[]{fkId}, new JSONMapperImpl());
				for(JSONObject jsonObject:list) {
					String id=jsonObject.getString("FILE_ID");
					String diskPath=jsonObject.getString("DISK_PATH");
					String basePath = getBaseDir();
					File file =  new File(basePath+diskPath);
					boolean fileNotExist = false;
					if(file==null||!file.exists()){
						fileNotExist = true;
						this.error("文件不存在",null);
					}
					boolean bl = file.delete();
					if(bl||fileNotExist) {
						if(StringUtils.isNotBlank(id)) {
							this.getQuery().executeUpdate("delete from yq_file_download_log where file_id =  ?",id);
							this.getQuery().executeUpdate("delete from yq_look_log where fk_id =  ?",id);
							this.getQuery().executeUpdate("delete from yq_files where file_id =  ?",id);
						}
					}
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}





