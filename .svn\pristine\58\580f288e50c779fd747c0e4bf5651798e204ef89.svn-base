package com.yunqu.work.servlet.flow;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.BxService;
import com.yunqu.work.service.FlowService;

@WebServlet("/servlet/flow/approve/*")
public class ApproveServlet extends BaseFlowServlet {
	private static final long serialVersionUID = 1L;

	public EasyResult actionForDoApprove() {
		EasyQuery query = this.getQuery();
		String msg = "操作成功";
		try {
			query.begin();
			JSONObject params = getJSONObject();
			String flowCode = params.getString("flowCode");
			String businessId = params.getString("businessId");
			String resultId = params.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("审批人不存在");
			}
			
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);
			
			int ccCount = addCCLog(query,params.getString("ccIds"),businessId,resultId);
			
			//获取下一级节点
			ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
			String nodeId = approveNode.getNodeId();
			String nextNodeId = approveNode.getNextNodeId();
			String _nextNodeId = params.getString("nextNodeId");
			String _nextNodeCode = params.getString("nextNodeCode");
			
			if(StringUtils.isNotBlank(_nextNodeCode)) {
				if("0".equals(_nextNodeCode)) {
					nextNodeId = "0";
				}else {
					ApproveNodeModel node = ApproveService.getService().getNodeInfo(flowCode, _nextNodeCode, apply.getApplyBy(),params);
					if(node==null) {
						return EasyResult.fail(_nextNodeCode+"不存在,请联系管理员.");
					}
					nextNodeId = node.getNodeId();
				}
			}else if(StringUtils.isNotBlank(_nextNodeId)) {
				nextNodeId = _nextNodeId;
			}
			
			FlowModel flow = ApproveService.getService().getFlow(flowCode);

			int checkResult = params.getIntValue("checkResult");//1 通过 2拒绝
			String checkDesc = params.getString("checkDesc");
			EasyRecord record = getApproveResultRecord(resultId,checkResult,checkDesc);
			record.set("cc_count", ccCount);
			if("0".equals(nextNodeId)||checkResult==2) {
				query.update(record);
			}
			
			MessageModel msgModel = new MessageModel();
			msgModel.setTypeName(flow.getFlowCode());
			msgModel.setSender(getUserId());
			msgModel.setSendName(getUserName());
			msgModel.setFkId(businessId);
			msgModel.setMsgLabel("待审批");
			msgModel.setTitle(apply.getApplyTitle());
			msgModel.setData1(flow.getFlowName());
			msgModel.setData2(apply.getApplyName());
			msgModel.setData3(apply.getApplyTime());
			msgModel.setData4("待审批");
			msgModel.setDesc(apply.getApplyRemark());
			
			if(checkResult==2) {
				FlowService.getService().updateApplyReturn(businessId);
				msgModel.setData4("审批退回");
				msgModel.setMsgLabel("审批退回");
				msgModel.setDesc("审批退回："+checkDesc);
				msgModel.setReceiver(apply.getApplyBy());
			}else {
				if("0".equals(nextNodeId)) {//完成了
					msg = "审批完成,流程结束";
					msgModel.setMsgLabel("审批完成");
					msgModel.setReceiver(apply.getApplyBy());
					FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
				}else {
					msgModel.setMsgLabel("请尽快审批");
					
					ApproveNodeModel nextNodeInfo = ApproveService.getService().getNodeInfo(nextNodeId,apply.getApplyBy(),params);
					if(nextNodeInfo==null) {
						return EasyResult.fail("获取不到下一级审批,请联系管理员");
					}
					query.update(record);
					
					String todoCheckUserId = nextNodeInfo.getCheckBy();
					if(todoCheckUserId.indexOf(apply.getApplyBy())>-1) {
						nextNodeId = nextNodeInfo.getNextNodeId();
						if("0".equals(nextNodeId)) {
							FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
							return EasyResult.ok();
						}else {
							nextNodeInfo = ApproveService.getService().getNodeInfo(nextNodeId,apply.getApplyBy(),params);
						}
					}
					msgModel.setMsgLabel(nextNodeInfo.getNodeName());
					msgModel.setReceiver(nextNodeInfo.getCheckBy());
					
					String nextResultId = RandomKit.uniqueStr();
					EasyRecord resultRecord = getApproveResultRecord(nextNodeInfo, businessId, nextResultId,resultId);
					query.save(resultRecord);
					msg = "提交成功,审批下一个节点:"+nextNodeInfo.getNodeName();
					
					FlowService.getService().updateApplyInfo(query,businessId, resultId,nextResultId,nodeId,nextNodeId, 20);
					
					this.doSendToApplyMsg(sendToApplyMsgModel(businessId,checkResult));
				}
			}
			query.commit();
			this.sendAllMsg(msgModel);
		} catch (SQLException e) {
			this.error(null, e);
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(null, ex);
			}
			return EasyResult.fail(e.getMessage());
			
		}
		return EasyResult.ok(null,msg);
	}
	
	public EasyResult actionForUpdateApply() {
		return updateApply();
	}
	

	
	/**
	 *提交流程
	 */
	public EasyResult actionForSubmitApply() {
		return submitApply();
	}
	
	
	public EasyResult actionForAddReadTime() {
		JSONObject params = getJSONObject();
		String resultId = params.getString("resultId");
		try {
			this.getQuery().executeUpdate("update yq_flow_approve_result set read_time = ? where result_id = ? and read_time = ''", EasyDate.getCurrentDateString(),resultId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForGetStartNode() throws SQLException {
		JSONObject params = getJSONObject();
		String flowCode = params.getString("flowCode");
		JSONObject result = new JSONObject();
		result.put("nextNode", ApproveService.getService().getStartNode(flowCode, getUserId(), params));
		return EasyResult.ok(result);
	}
	
	@SuppressWarnings("unused")
	public EasyResult actionForGetNodeInfoByResultId() throws SQLException {
		JSONObject params = getJSONObject();
		String businessId = params.getString("businessId");
		String resultId = params.getString("resultId");
		JSONObject result = new JSONObject();
		
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		String flowCode = apply.getFlowCode();
		ApproveNodeModel approveNode = null;
		
		boolean isBxFlow = "finance_bx".equals(flowCode)||"finance_loan".equals(flowCode);
		if(isBxFlow) {
			approveNode = BxService.getService().getNodeByResultId(resultId);
		}else {
			approveNode = ApproveService.getService().getNodeByResultId(resultId);
		}
		
		String nextNodeId =  approveNode.getNextNodeId();
		
		if(approveNode!=null) {
			result.put("nowNode", approveNode);
			ApproveResultModel approveResult = ApproveService.getService().getResult(resultId);
			result.put("nowResult", approveResult);
			if("0".equals(approveNode.getNextNodeId())) {
				result.put("nextNode", new ApproveNodeModel());
			}else {
				ApproveNodeModel nextNodeInfo = null;
				if(isBxFlow) {
					nextNodeInfo = BxService.getService().getNodeInfo(nextNodeId, flowCode,businessId,params);
				}else {
					nextNodeInfo = ApproveService.getService().getNodeInfo(nextNodeId,apply.getApplyBy(),params);
				}
				if(nextNodeInfo==null) {
					result.put("nextNode", new ApproveNodeModel());
				}else {
					result.put("nextNode", nextNodeInfo);
				}
			}
		}else {
			result.put("nowResult", new ApproveResultModel());
			result.put("nowNode",new ApproveNodeModel());
			result.put("nextNode", new ApproveNodeModel());
		}
		return EasyResult.ok(result);
	}
}
