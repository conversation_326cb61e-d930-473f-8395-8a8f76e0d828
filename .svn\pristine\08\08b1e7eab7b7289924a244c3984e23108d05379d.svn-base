<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowName}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 140px;">标题</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的印章使用申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 150px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">印章类别</td>
					  			<td>
						  			<input id="sealType" onclick="selectText(this)" readonly="readonly" data-rules="required" data-text="广州云趣公章,北京云趣公章,中融公章,法人章,合同专用章,中融合同章,人力资源章,工程专用章,财务专用章" class="form-control input-sm" name="apply.data1">
					  			</td>
					  			<td class="required">是否外借</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data2">
						  				<option value="否">否</option>
						  				<option value="是">是</option>
						  			</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">使用时间</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data3"/>
					  			</td>
					  			<td>预计归还时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">用印文件名称</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td>用印文件份数</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">用印文件合同额</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  			<td class="required">用印文件合同额(含税)</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="vertical-align: top;">申请事由</td>
					  			<td colspan="3">
									<textarea style="height: 70px;" data-rules="required" class="form-control input-sm" maxlength="500" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>盖章文件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  		<tr class="remindInfo hidden-print">
					  			<td>注意事项</td>
					  			<td colspan="3">
					  				<small>1、凡使用公司印章须填写此表；印章包括公章、财务章、合同章、法人印鉴等<br>
									2、申请事由一栏应将用印的文本名及文本内容填写清楚；如文本是合同的应填写涉及金额数及签订合同的对方单位名称。<br>
									3、<b>如外借本人承诺在借用期间对印章负管理责任，妥善保管借用印章，严格按公司规定使用印章。如有违反，本人愿承担一切法律责任。电子审批流程完成后，请打印本表单至施印人处盖章，外借还需在施印人处签署纸质版印章借用承诺书，承诺书由施印人统一归档。</b>
									</small>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({title:'{{:data1}}使用申请',fileCheck:true,success:function(data){
				Flow.initData();
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			var type = $("[name='apply.data2']").val();
			var useDate = $("[name='apply.data3']").val();
			var returnDate = $("[name='apply.data4']").val();
			if(type=='是'&&returnDate==''){
				layer.msg('外借需填写归还时间');
				return;
			}
			if(returnDate&&returnDate<useDate){
				layer.msg('归还时间应大于使用时间');
				return;
			}
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.initData = function(){
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var type = applyInfo.data1;;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='总裁'){
				var checkBys = {'法人章':'杨曼丽','人力资源章':'庞小翠','广州云趣公章':'庞小翠','北京云趣公章':'朱志华','财务专用章':'杨曼丽','合同专用章':'杨曼丽','中融合同章':'杨曼丽','工程专用章':'李千帆','中融公章':'庞小翠'};
				
				var types = type.split(',');
				var selectUserNames = [];
				for(var index in types){
					selectUserNames.push(checkBys[types[index]]);
				}
				selectUserNames = arrayUnique(selectUserNames);
				params['selectUserName'] = selectUserNames.join();
			}
			FlowCore.approveData = params;
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({data:FlowCore.approveData});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>