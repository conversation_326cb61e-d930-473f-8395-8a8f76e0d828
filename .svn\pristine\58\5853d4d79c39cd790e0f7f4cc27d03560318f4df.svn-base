package com.yunqu.work.base;

import java.sql.SQLException;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.model.StaffRowMapper;
public abstract class AppBaseServlet extends EasyBaseServlet { 
	private static final long serialVersionUID = 1L;
	
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_NAME;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	protected boolean hasRole(String roleId) {
		return getUserPrincipal().isRole(roleId);
	}
	
	protected boolean hasRes(String resId) {
		return getUserPrincipal().isResource(resId);
	}
	
	@Override
	protected String getResId() {
		return null;
	}
	public EasyQuery getMainQuery(){
		return EasyQuery.getQuery(getAppName(), Constants.MARS_DS_NAME);
	}

	protected String getRequestUrl() {
		return getRequest().getRequestURI()+"?"+getRequest().getQueryString();
	}
	protected String getDeptId() {
		return getStaffInfo().getDeptId();
	}
	
	protected String getDeptName() {
		return getStaffInfo().getDeptName();
	}
	
	protected String getUserId() {
		return getUserPrincipal().getUserId();
	}
	
	protected String getUserName() {
		return getUserPrincipal().getUserName();
	}
	
	protected StaffModel getStaffInfo() {
		Object object =  getUserPrincipal().getAttribute("staffInfo");
		if(object==null) {
			try {
				StaffModel staffModel = this.getMainQuery().queryForRow("select t1.ROOT_ID,t1.USER_ID,t1.DEPTS,t1.USERNAME,t1.NIKE_NAME,t1.OPEN_ID,t1.EMAIL,t1.MOBILE,t1.PIC_URL,t2.SUPERIOR,t2.STAFF_NO,t2.JOB_TITLE,t3.DEPT_ID from easi_user t1 LEFT JOIN yq_staff_info t2 on t1.USER_ID = t2.staff_user_id LEFT JOIN easi_dept_user t3 on t3.USER_ID = t1.USER_ID where t1.USER_ID = ?",new Object[]{getUserId()},new StaffRowMapper());
				getUserPrincipal().setAttribute("staffInfo",staffModel);
				return staffModel;
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
		String jsonStr = JSONObject.toJSONString(object);
		JSONObject jsonObject = JSONObject.parseObject(jsonStr);
		return JSONObject.toJavaObject(jsonObject, StaffModel.class);
	}
	protected String getBaseDir() {
		String basePath = AppContext.getContext(Constants.APP_NAME).getProperty("basePath", "/home/<USER>/");
		return basePath+"files/";
	}
	
	
	protected FlowApplyModel getApplyRecord(String categoryId) {
		return getApplyRecord(null,categoryId);
	}
	
	protected FlowApplyModel getApplyRecord(String id,String categoryId) {
		FlowApplyModel model = new FlowApplyModel(); 
		model.set("apply_time", EasyDate.getCurrentDateString());
		model.set("apply_by", getUserId());
		model.set("apply_name", getUserName());
		model.set("dept_id", getDeptId());
		model.set("dept_name", getDeptName());
		model.set("category_id", categoryId);
		if(id!=null) {
			model.setPrimaryValues(id);
		}
		return model;
	}
}
