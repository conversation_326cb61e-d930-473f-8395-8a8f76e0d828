<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>项目周报</title>
	<style>
		 .layui-table-cell {
	        line-height: 20px !important;
	        vertical-align: middle;
	        height: auto;
	        padding: 6px 6px;
	        overflow: visible;
	        text-overflow: inherit;
	        white-space: normal;
	    }
	    #msg{margin-bottom: 10px;padding: 10px;display: none;}
	    .dept-item{
	    	line-height: 24px;
	    }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="projectWeekly" class="form-inline">
		<input name="projectId" type="hidden" value="${param.projectId}">
		<div class="ibox-content">
			<div class="layui-tab layui-tab-brief">
			  <ul class="layui-tab-title" style="margin-top: 6px;">
			    <li class="layui-this">项目周报统计（个人）</li>
			    <li>项目周报明细（个人）</li>
			    <li>项目周报明细（项目组）</li>
			  </ul>
			  <div class="layui-tab-content">
			    <div class="layui-tab-item layui-show">
			    	 <div class="layui-row layui-panel" id="msg"></div>
					 <table class="layui-hide" id="personWeeklyStat"></table>
			    </div>
			    <div class="layui-tab-item">
	          		 <div class="form-group" style="margin-bottom: 8px;">
				       <div class="input-group input-group-sm" style="width: 120px;">
							<span class="input-group-addon">部门</span> 
							<input name="deptName" class="form-control input-sm">
						</div>
				       <div class="input-group input-group-sm" style="width: 120px;">
							<span class="input-group-addon">姓名</span> 
							<input name="userName" class="form-control input-sm">
						</div>
				       <div class="input-group input-group-sm"  style="width: 150px;">
							<span class="input-group-addon">开始月份</span> 
							<input name="startDate" autocomplete="off" data-mars="CommonDao.yearMonthBegin" data-mars-reload="false" id="startDate" data-laydate="{type:'month'}" class="form-control input-sm Wdate">
						</div>
						<div class="input-group input-group-sm"  style="width: 150px;">
							<span class="input-group-addon">结束月份</span> 
							<input name="endDate" autocomplete="off" data-mars="CommonDao.nowMonthId" data-mars-reload="false" id="endDate" data-laydate="{type:'month'}" class="form-control Wdate">
						</div>
						<div class="input-group input-group-sm">
						 	<button type="button" class="btn btn-sm btn-default" onclick="loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					   </div>
	          	     </div>
					 <table class="layui-hide" id="personWeekly"></table>
			    </div>
			     <div class="layui-tab-item">
			     	即将上线
			     </div>
			  </div>
			</div>
						
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
	
	function loadPersonWeekly(){
		$("#projectWeekly").initTable({
			mars:'WeeklyDao.weeklyProjectList',
			id:"personWeekly",
			data:{},
			height:'full-160',
			limit:30,
			cols: [[
	         {
				title: '序号',
				type:'numbers'
			 },{
			    field: 'WEEK_NO',
				title: '周期/工时',
				width:110,
				align:'left',
				templet:'<div>{{d.YEAR}}.{{d.WEEK_NO}}({{d.WORK_DAY}}day)</div>'
			 },{
			    field: 'CREATE_NAME',
				title: '填写人',
				width:70,
				align:'left'
			},{
			    field: 'DEPT_NAME',
				title: '所属部门',
				width:90,
				align:'left'
			},{
			    field: 'SEND_TIME',
				title: '填写时间',
				width:140,
				align:'left'
			},{
			    field: 'WORK_CONTENT',
				title: '工作内容',
				align:'left',
				templet:function(row){
					var fileCount = row['FILE_COUNT'];
					if(fileCount>0){
						return "【附件】"+getContent(row['WORK_CONTENT']);
					}else{
						return getContent(row['WORK_CONTENT']);
					}
				}
			},{
			    field: '',
				title: '操作',
				align:'left',
				width:60,
				hide:true,
				event:'detailPersonWeekly',
				templet:'<div><a href="javascript:;">查看</a></div>'
			}
			]],done:function(result){
				
		}});
	}
	
	function loadPersonWeeklyStat(){
		$("#projectWeekly").initTable({
			mars:'WeeklyDao.projectWeeklyStat',
			id:"personWeeklyStat",
			data:{},
			height:'full-120',
			limit:90,
			totalRow:true,
			toolbar:true,
			cols: [[
	         {
				title: '序号',
				type:'numbers'
			 },{
			    field: 'USERNAME',
				title: '姓名',
				width:70,
				align:'left'
			},{
			    field: 'DEPTS',
				title: '部门',
				width:90,
				align:'left'
			},{
			    field: 'COUNT',
				title: '参数周数',
				width:100,
				align:'left',
				totalRow:true
			},{
			    field: 'WORK_DAY',
				title: '投入工时/天',
				width:100,
				align:'left',
				totalRow:true
			},{
			    field: 'LAST_FILL_TIME',
				title: '最后填报时间',
				width:140,
				align:'left'
			},{
			    field: 'WORK_CONTENT',
				title: '最后填报内容',
				align:'left',
				templet:function(row){
					return getContent(row['WORK_CONTENT']);
				}
			}
			]],done:function(result){
				
		}});
	}
	
	
	function loadStat(){
		$('#msg').html('');
		ajax.remoteCall("/yq-work/webcall?action=WeeklyDao.projectWeeklyDeptStat",{projectId:'${param.projectId}'},function(result){
 			var list = result.data;
 			var sumTime = 0 ;
 			var html = [];
 			var i = 0;
 			for(var index in list){
 				sumTime = sumTime + Number(list[index]['WORK_DAY']);
 			}
 			for(var index in list){
 				var workday = Number(list[index]['WORK_DAY']);
 				html.push('<div class="layui-col-md3 dept-item">'+list[index]['DEPT_NAME']+"投入工时："+workday+'/人/天('+calculatePercentage(workday,sumTime)+')</div>');
 				i++;
 			}
 			if(list.length==0){
 				$('#msg').hide();
 			}else{
 				$('#msg').append('<div class="layui-col-md3 dept-item">总投入工时：'+sumTime+'/人/天</div>');
 				$('#msg').append(html.join(''));
 			}
 		});
	}
	
	function calculatePercentage(part, whole) {
		  if (whole === 0) {
		    return '不能除以0';
		  }
		  var percentage = (part / whole) * 100;
		  return percentage.toFixed(2) + '%';
	}

	
 	function detailPersonWeekly(data){
 		var weeklyId = data['WEEKLY_ID'];
 		if(!weeklyId){
 			weeklyId=data;
 		}
	    popup.layerShow({type:1,maxmin:true,anim:0,shadeClose:true,scrollbar:false,offset:'20px',area:['80%','80%'],url:'${ctxPath}/weekly',title:'周报明细',data:{isDiv:1,projectId:'${param.projectId}',weeklyId:weeklyId}});
 	}
 	
 	function loadData(){
 		$("#projectWeekly").queryData({id:'personWeekly'});
 	}
 	
 	$(function(){
 		$("#projectWeekly").render({success:function(){
 			loadPersonWeekly();
 			loadPersonWeeklyStat();
 			//loadStat();
		}});
 	});
 	
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>