<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-content">
			    	<table class="layui-hide" id="list"></table>
				</div>
			</div>
		</form>
		<script type="text/x-jsrender" id="bar">
			{{if WEEKLY_ID}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>
				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看</a>
			{{else}}
				<a class="layui-btn layui-btn-info layui-btn-xs" lay-event="list.add">填写</a>
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'WeeklyDao.myWeeklyList',
					id:'list',
					limit:30,
					height:'full-40',
					cellMinWidth:100,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'TITLE',
						title: '标题',
						align:'left',
						minWidth:250,
						templet:function(row){
							var title = row.TITLE;
							if(title){
								return title;
							}
							return '<span class="newData">'+row._TITLE+'</span>';
						}
					},{
					    field: 'WORK_DAY',
						title: '工时/天',
						align:'left'
					},{
					    field: 'PROJECT_NUM',
						title: '参与项目数',
						align:'left'
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						align:'left'
					},{
					    field: 'STATUS',
						title: '状态',
						align:'left',
						templet:function(row){
							var status = row.STATUS;
							if(status==''){
								return '未填写';
							}
							if(status==0){
								return '<span class="layui-badge">草稿</span>';
							}if(status==1||status==2){
								return '已发出';
							}else{
								return '未填写';
							}
						}
					},{
						title: '操作',
						align:'left',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]],done:function(){
						$("tbody .newData").parents('tr').css('background-color','#ddd');
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'list'});
			},
			edit:function(data){
				var _width = $(window).width();
				var width ='100%';
				if(_width>1200){
					width='70%';
				}
				popup.openTab({type:1,full:fullShow(),shade: 0.1,shadeClose:false,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/weekly/weekly-edit.jsp',title:'编辑周报',data:{weeklyId:data.WEEKLY_ID,status:data.STATUS,isDiv:0}});
			},
			add:function(data){
				var _width=$(window).width();
				var width ='100%';
				if(_width>1200){
					width='70%';
				}
				data['YEAR'] = data['_YEAR']
				data['WEEK_NO'] = data['_WEEK_NO']
				data['TITLE'] = data['_TITLE']
				data['isDiv'] = '0';
				popup.openTab({id:'addWeekly',type:1,full:fullShow(),shade: 0.1,shadeClose:false,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/weekly/weekly-edit.jsp',title:'新增周报',data:data});
				layer.confirm('是否刷新当前页面?',{icon:3},function(){
					location.reload();
				});
			},
			detail:function(data){
				var _width=$(window).width();
				var width ='850px';
				if(_width>1900){
					width='70%';
				}
				popup.layerShow({id:'weeklyDetail',type:1,full:fullShow(),shade: 0.1,shadeClose:true,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/weekly',title:'周报详情',data:{isDiv:1,weeklyId:data.WEEKLY_ID}});
			}
		}
		$(function(){
			list.init();
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>