<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="SelectTaskForm">
		       <div class="row hidden">
		   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
						 <span class="input-group-addon">任务名称</span>	
						 <input type="text" name="taskName" class="form-control input-sm">
				     </div>
	    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectTask.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
		   		</div>
            	<div class="ibox">
              		<table id="taskList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectTask.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectTask={
					type:'${param.type}',
					query : function(){
						$("#SelectTaskForm").queryData();
					},
					initTable : function(){
						$("#SelectTaskForm").initTable({
							mars:'TaskDao.myTaskList',
							id:'taskList',
							page:true,
							limit:20,
							data:{status:'1'},
							height:'full-150',
							rowDoubleEvent:'SelectTask.ok',
							cols: [[
					        {type:'checkbox'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'TASK_NAME',
								title: '标题'
							},{
							    field: 'TASK_STATE',
								title: '任务状态',
								filter: true,
								sort:true,
								align:'center',
								width:80,
								templet:function(row){
									return taskStateLabel(row.TASK_STATE);
								}
							},{
							    field: 'DEADLINE_AT',
								title: '截止时间',
								width:120,
								templet:'<div>{{d.DEADLINE_AT}}</div>'
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('taskList');
							if(checkStatus.data.length>0){
								var names = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push("<p>"+stateSymbol()+" "+data[index]['TASK_NAME']+"#"+taskState(data[index]['TASK_STATE'])+"</p>");
								}
								selectTaskCallback(SelectTask.type,names.join(''));
								popup.layerClose("SelectTaskForm");
							}else{
								
							}
						}else{
							selectTaskCallback(SelectTask.type,"<p>"+stateSymbol()+" "+selectRow['TASK_NAME']+"#"+selectRow['TASK_STATE']+"</p>");
							popup.layerClose("SelectTaskForm");
						}
					}
					
			};
			function stateSymbol(){
				if(SelectTask.type=='1'){
					return '☑';
				}else{
					return '☐';
				}
			}
			$(function(){
				SelectTask.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>