<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>服务管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>自研产品管理</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">产品名称</span>	
							 <input type="text" name="serviceName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 添加</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
  			{{if currentUserId == CREATOR}}<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ServiceDao.list',
					limit:20,
					height:'full-120',
					cols: [[
					{type:'numbers'},
		             {
					    field: 'SERVICE_NAME',
						title: '产品名称',
						align:'left'
					},{
					    field: 'DEVEL',
						title: '开发负责人',
						align:'left',
						templet:function(row){
							return getUserName(row.DEVEL);
						}
					},{
					    field: 'OPS',
						title: '运维负责人',
						align:'left',
						templet:function(row){
							return getUserName(row.OPS);
						}
					},{
					    field: 'SERVICE_DESC',
						title: '描述',
						align:'left'
					},{
					    field: 'SERVICE_VERSION',
						title: '版本号',
						align:'left'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'left'
					},{
						title: '操作',
						align:'left',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/ops/service-edit.jsp',title:'编辑',data:{serviceId:data.SERVICE_ID}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/ops/service-edit.jsp',title:'新增产品'});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>