package com.yunqu.work.utils;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.jsoup.Connection;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;

public class WeChatWebhookSender {
	
	public static void sendHtmlMessage(String webhookKey, String message) {
		message = HtmlToMarkdownReplacer.convert(message);
		sendMessage(webhookKey,"markdown",message);
	}
	
	public static void sendMarkdownMessage(String webhookKey, String message) {
		sendMessage(webhookKey,"markdown",message);
	}
	
	public static void sendMarkdownMessage(String webhookKey,String title,String message) {
		String content = "<font color=\"warning\">"+title+"</font>\n> "+message;
		sendMessage(webhookKey,"markdown",content);
	}
	
	public static void sendTextMessage(String webhookKey, String message) {
		sendMessage(webhookKey,"text",message);
	}
	
	public static void sendMessage(String webhookKey,String msgtype,String message) {
		MessageModel messageModel = new MessageModel();
		messageModel.setDesc(message);
		sendMessage(webhookKey, msgtype, messageModel);
	}
	
	public static boolean sendMessage(String webhookKey,String msgtype,MessageModel messageModel) {
		String message = messageModel.getDesc();
		String msgSwitch = AppContext.getContext(Constants.APP_NAME).getProperty("msgSwitch","0");
		if(!"1".equals(msgSwitch)||!ServerContext.isLinux()) {
			webhookKey = WebhookKey.TEST;
		}
		try {
			JSONObject msg = new JSONObject();
			msg.put("msgtype", msgtype);
			if("news".equals(msgtype)) {
				
			}else {
				JSONObject contentJson = new JSONObject();
				contentJson.put("content", message);
				JSONArray toUser = new JSONArray();
				toUser.add("@all");
				contentJson.put("mentioned_list", toUser);
				msg.put(msgtype, contentJson);
			}
			String jsonMessage = msg.toJSONString();
			LogUtils.getWebhook().info(webhookKey+">WeChatWebhookSender Req jsonMessage: " + jsonMessage);
			Connection.Response response = Jsoup.connect("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key="+webhookKey)
					.method(Connection.Method.POST).timeout(5000).ignoreContentType(true)
					.header("Content-Type", "application/json")
					.requestBody(jsonMessage)
					.execute();
			int statusCode = response.statusCode();
			LogUtils.getWebhook().info("WeChatWebhookSender Response: " + statusCode+">body>"+response.body());
			return statusCode == 200;
		} catch (Exception e) {
			e.printStackTrace();
			LogUtils.getWebhook().error(e.getMessage(),e);
			return false;
		}
    }
	
	public static void main(String[] args) {
		sendHtmlMessage(WebhookKey.TEST, "<b>你踢球受过最重的伤是女友到球场给对手喂水！</b>");
	}
}
