<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>机房</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="ServerRoomDao.record" autocomplete="off" data-mars-prefix="serverRoom.">
     		   <input type="hidden" value="${param.serverRoomId}" name="serverRoom.SERVER_ROOM_ID"/>
     		   <input type="hidden" value="${param.projectId}" name="serverRoom.PROJECT_ID"/>
						<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td style="width: 80px" class="required">机房名称</td>
				                    <td><input data-rules="required"  type="text" name="serverRoom.SERVICE_ROOM_NAME" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td class="required">所在地址</td>
				                    <td><input data-rules="required"  type="text" name="serverRoom.ADDRESS" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td class="required">线路类型</td>
				                    <td>
				                    	<select name="serverRoom.LINE" class="form-control input-sm">
				                    		<option value="1">电信</option>
				                    		<option value="2">联通</option>
				                    		<option value="3">移动</option>
				                    		<option value="4">双线</option>
				                    		<option value="5">BGP</option>
				                    	</select>
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required">带宽</td>
				                    <td><input data-rules="required"  type="text" name="serverRoom.BANDWIDTH" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td class="required">服务商</td>
				                    <td><input data-rules="required" value="" placeholder="阿里/腾讯/自建..." type="text" name="serverRoom.PROVIDER" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td class="required">负责人</td>
				                    <td>
				                    	<select data-rules="required" data-mars="CommonDao.userDict" name="serverRoom.OWNER" class="form-control input-sm">
				                    		<option value="">请选择</option>
				                    	</select>
				                    </td>
				                </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c">
						    	  <button type="button" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("Edit");
		Edit.serverRoomId='${param.serverRoomId}';
		
		$(function(){
			$("#editForm").render({success:function(){
				requreLib.setplugs('select2',function(){
				   $("#editForm select").select2({theme: "bootstrap",placeholder:'请选择'});
				});
			}}); 
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.serverRoomId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/serverRoom?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/serverRoom?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>