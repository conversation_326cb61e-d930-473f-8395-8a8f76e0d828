<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购详情</title>
	<style>
		.form-horizontal{width: 100%;}
		.layui-table-cell a{font-size: 12px;color: #3E84E9;cursor: pointer;}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 14px;}
		.layui-timeline-item:hover a{opacity:1;}
		.layui-tab-card>.layui-tab-title{background-color: #fff;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		.layui-timeline-content p{font-size: 13px;color: #666;}
		.layui-timeline-title{margin-bottom: 2px;}
		.layui-timeline-item{margin-bottom: 2px;}
		.layui-text h3{font-size: 16px;}
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.uploadBtn{display: none;}
		.gray-bg{background-color: #e8edf7;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
		blockquote{font-size: 12px;padding: 6px!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="OrderDetailForm"  data-mars="OrderFlowDao.reviewInfo" data-mars-prefix="review.">
			    <input type="hidden" value="${fdata.ORDER_ID}" id="orderId" name="orderId"/>
 	  	        <input type="hidden" value="${fdata.ORDER_ID}" name="pFkId"/>
 	  	        <input type="hidden" value="${param.custId}" name="custId"/>
 	  	        <input type="hidden" value="${param.nodeId}" id="nodeId" name="nodeId"/>
 	  	        <input type="hidden" value="${param.resultId}" id="resultId" name="resultId"/>
				<div style="padding: 5px" class="ibox-content" data-mars="OrderDao.applyInfo" data-mars-prefix="apply.">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md8">
		 	  	         <input type="hidden" value="${param.businessId}" id="businessId" name="apply.REVIEW_ID"/>
		 	  	         <input type="hidden" value="${param.businessId}" name="businessId"/>
		 	  	         <input type="hidden" value="${param.paymentId}" id="paymentId" name="apply.PAYMENT_ID"/>
					     <i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 18px;" name='review.APPLY_NAME'></span>
					  </div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm">
						  			<yq:user userId="${param.applyBy}">
							  			<button class="btn btn-default btn-xs" onclick="editOrder();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
						  			</yq:user>
						  			<c:if test="${param.checkResult=='0'}">
										<button class="btn btn-info btn-xs" onclick="addReviewResult();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 填写评审意见</button>
						  			</c:if>
						  			<c:if test="${param.reviewState=='21'}">
										<button class="btn btn-warning btn-xs" onclick="restartReviewResult();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 重新发起</button>
						  			</c:if>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding:10px 15px;">
					  <div class="layui-col-md12">
					  		<table class="table table-vzebra mb-10">
							  <tbody>
							  	<tr>
								  	<td style="width: 90px;">发起人</td>
								  	<td>
									  	<span class="h-value text-info" name="review.CREATE_NAME"></span>
								  	</td>
								  	<td style="width: 90px;">发起时间</td>
									<td>
								  		<span class="h-value text-info" name="review.APPLY_TIME">--</span>
								 	</td>
								  	<td style="width: 90px;">审批人</td>
								  	<td>
								  		<span  class="h-value text-info" name="review.CHECK_NAMES"></span>
								  	</td>
								 </tr>
								 <tr>
								  	<td>申请说明</td>
								  	<td colspan="5">
								  		<span class="h-value text-info" name="review.REMARK"></span>
								  	</td>
								</tr>
							  </tbody>
							  <tbody>
					  			<tr>
					  				<td style="vertical-align: top;">修订日志</td>
					  				<td  colspan="5" data-mars="ReviewDao.reviseRecordList" data-template="template-revise">
					  					
					  				</td>
					  			</tr>
					  		</tbody>
						</table>
						 <jsp:include page="include/base-info.jsp"/>
					  </div>
					  <div class="layui-col-md12">
						  <jsp:include page="include/file-list.jsp"/>
					  </div>
					</div>
				</div>
				
				 <script id="template-revise" type="text/x-jsrender">
		{{for data}}
			<blockquote class="layui-elem-quote layui-quote-nm">{{:FOLLOW_CONTENT}} <span class="pl-10"> 【{{:ADD_TIME}}/{{:ADD_USER_NAME}}】</span></blockquote>
		{{/for}}
	</script>
	
	
				<div class="layui-row">
					<div class="layui-col-md12">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">物料信息(<span id="goodsCount">0</span>)</li>
						  </ul>
						  <div class="layui-tab-content" style="margin: 0px;background-color: #fff;">
						    <div class="layui-tab-item layui-show">
						    	<jsp:include page="include/goods-detail.jsp"/>
						    </div>
						  </div>
					   </div>
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">评审记录(<span id="checkCount">0</span>)</li>
						  </ul>
						  <div class="layui-tab-content" style="margin: 0px;background-color: #fff;">
						    <div class="layui-tab-item layui-show">
						    	<jsp:include page="review/review-check-list.jsp"/>
						    </div>
						  </div>
					</div>
					</div>
				</div>
		</form>
		<form  id="orderDetailFileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="orderDetailLocalfile" onchange="orderUploadFile()"/>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   
		jQuery.namespace("OrderDetail");
	    
		OrderDetail.orderId='${fdata.ORDER_ID}';
		
		var orderId = '${fdata.ORDER_ID}';
		
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
			});
			$("#OrderDetailForm").render({success:function(result){
				
			}});  
		});
		
		function addReviewResult(){
			var resultId = $('#resultId').val();
			var businessId = $('#businessId').val();
			var nodeId = $('#nodeId').val();
			popup.layerShow({id:'addResult',area:['500px','350px'],title:'填写评审意见',url:'${ctxPath}/pages/erp/order/review/review-check.jsp',data:{nodeId:nodeId,orderId:orderId,resultId:resultId,businessId:businessId}});
		}
		
		function addUpdateLog(){
			var businessId = $("#businessId").val();
	 		var json = {businessId:businessId};
			popup.layerShow({id:'addUpdateLog',area:['400px','300px'],url:'${ctxPath}/pages/crm/contract/review/include/revise-log.jsp',title:'新增修订日志',data:json});
		}
		
		function restartReviewResult(){
			layer.confirm('确认重新发起审批吗',{icon:3,offset:'50px'},function(index){
				layer.close(index);
				var businessId = $("#businessId").val();
				ajax.remoteCall("${ctxPath}/servlet/order/flow?action=restartReview",{businessId:businessId,orderId:orderId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							popup.closeTab();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		function contractDetail(){
			var contractId = $("[name='apply.CONTRACT_ID']").val();
			var custId = $("[name='custId']").val();
			popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/pages/crm/contract/contract-detail.jsp',data:{contractId:contractId,custId:custId}});
		}
		
		function editOrder(){
			var custId = $("[name='custId']").val();
			popup.openTab({title:'订单详情',url:'/yq-work/pages/erp/order/order-detail.jsp',data:{orderId:orderId,custId:custId}});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>