package com.yunqu.work.servlet.common;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.ContractNoticeService;
import com.yunqu.work.service.KqNoticeService;
import com.yunqu.work.service.MemosService;
import com.yunqu.work.service.NextCloudService;
import com.yunqu.work.service.OAService;
import com.yunqu.work.service.PlanNoticeService;
import com.yunqu.work.service.TaskNoticeService;
import com.yunqu.work.service.WeeklyNoticeService;
import com.yunqu.work.service.YApiService;
import com.yunqu.work.service.ZendaoService;
import com.yunqu.work.utils.WeekUtils;

@WebServlet("/servlet/excuteJob/*")
public class ExcuteJobServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	public void actionForPlanNotice(){
		PlanNoticeService.getService().run();
		renderText("ok");
	}
	
	public void actionForDeptKqData(){
		KqNoticeService.getService().deptKqDataNew(getPara("monthId"));
		KqNoticeService.getService().deptKqData(getPara("monthId"));
		renderText("ok");
	}
	
	public void actionForNoticeDeptWork(){
		WeeklyNoticeService.getService().noticeDeptWork();
		renderText("ok");
	}
	
	public void actionForZendao(){
		ZendaoService.getService().run();
	    renderText("ok");
	}
	
	public void actionForNewZendao(){
		ZendaoService.getService().newZendao();
		renderText("ok");
	}
	
	public void actionForSyncProduct(){
		ZendaoService.getService().syncProduct();
		renderText("ok");
	}
	
	public void actionForSyncProject(){
		ZendaoService.getService().syncProject();
		renderText("ok");
	}
	
	public void actionForMemos(){
		MemosService.getService().run();
		renderText("ok");
	}
	
	public void actionForRegYApi() {
		YApiService.getService().regUser();
		renderText("ok");
	}
	
	public void actionForOA(){
		OAService.getService().scanOaAcount();
		renderText("ok");
	}
	
	public void actionForNextCloudSyn(){
		 NextCloudService.getService().run();
		renderText("同步数据");
	}
	
	public void actionForWxMsgWaitTask(){
		TaskNoticeService.getService().wxMsgWaitTask();
		renderText("actionForWxMsgWaitTask通知");
	}
	
	public void actionForCreateProjectWorkHour(){
		String monthId = getPara("monthId");
		String deptName = getPara("deptName");
		if(StringUtils.isBlank(monthId)) {
			monthId = EasyCalendar.newInstance().getFullMonth();
		}
		WeeklyNoticeService.getService().createProjectWorkHour(monthId,deptName);
		renderText("同步数据ok");
	}
	
	public void actionForWhNotice(){
		String monthId = getPara("monthId");
		if(StringUtils.isBlank(monthId)) {
			monthId = EasyCalendar.newInstance().getFullMonth();
		}
		WeeklyNoticeService.getService().workhourNotice(monthId, getUserId());
		renderText("同步数据ok");
	}
	
	public void actionForSendAllWeeklyToLeader(){
		String weekNoStr = getPara("weekNo");
		int weekNo = 0;
		if(StringUtils.isBlank(weekNoStr)) {
			weekNo = WeekUtils.getWeekNo();
		}else {
			weekNo = Integer.valueOf(weekNoStr);
		}
		WeeklyNoticeService.getService().sendAllWeeklyToLeader(weekNo);
		renderHtml("ok");
	}
	
	public void actionForSendDeptWeeklyNotice(){
		String weekNoStr = getPara("weekNo");
		String deptId = getPara("deptId");
		String isAll = getPara("isAll");
		if(StringUtils.isBlank(deptId)) {
			deptId = getDeptId();
		}
		int weekNo = 0;
		if(StringUtils.isBlank(weekNoStr)) {
			weekNo = WeekUtils.getWeekNo();
		}else {
			weekNo = Integer.valueOf(weekNoStr);
		}
		try {
			if("1".equals(isAll)) {
				WeeklyNoticeService.getService().sendDeptWeeklyToLeader(weekNo,true);
			}else {
				WeeklyNoticeService.getService().sendDeptWeeklyToLeader(weekNo, deptId,true);
			}
		} catch (NumberFormatException | SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderText("actionForSendDeptWeeklyNotice执行完成ok");
	}
	
	public void actionForSendDeptWeeklyToLeader(){
		String weekNoStr = getPara("weekNo");
		String deptId = getPara("deptId");
		String isAll = getPara("isAll");
		if(StringUtils.isBlank(deptId)) {
			deptId = getDeptId();
		}
		int weekNo = 0;
		if(StringUtils.isBlank(weekNoStr)) {
			weekNo = WeekUtils.getWeekNo();
		}else {
			weekNo = Integer.valueOf(weekNoStr);
		}
		try {
			if("1".equals(isAll)) {
				WeeklyNoticeService.getService().sendDeptWeeklyToLeader(weekNo);
			}else {
				WeeklyNoticeService.getService().sendDeptWeeklyToLeader(weekNo, deptId);
			}
		} catch (NumberFormatException | SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderText("actionForSendDeptWeeklyToLeader执行完成ok");
	}
	
	public void actionForSendProjectWeeklyToPm(){
		String weekNoStr = getPara("weekNo");
		String projectId = getPara("projectId");
		String isAll = getPara("isAll");
		int weekNo = 0;
		if(StringUtils.isBlank(weekNoStr)) {
			weekNo = WeekUtils.getWeekNo();
		}else {
			weekNo = Integer.valueOf(weekNoStr);
		}
		try {
			if("1".equals(isAll)) {
				WeeklyNoticeService.getService().sendProjectWeeklyToPm(weekNo);
			}else {
				if(StringUtils.isBlank(projectId)) {
					renderHtml("projectId is null");
					return;
				}
				WeeklyNoticeService.getService().sendProjectWeeklyToPm(weekNo,projectId);
			}
		} catch (NumberFormatException | SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderText("actionForSendProjectWeeklyToPm执行完成ok");
	}

	public void actionForSendContractMonthlyToSale() {
		String year = getPara("year");
		String month = getPara("month");
		String saleId = getPara("saleId");
		if(StringUtils.isNoneBlank(year,month,saleId)){
			ContractNoticeService.getService().sendContractReceiveNoticeToSale(Integer.valueOf(year),Integer.valueOf(month),saleId);
		}else if(StringUtils.isNoneBlank(year,month)){
			ContractNoticeService.getService().sendContractReceiveNoticeToSale(Integer.valueOf(year),Integer.valueOf(month));
		}else if(StringUtils.isNotBlank(saleId)){
			ContractNoticeService.getService().sendContractReceiveNoticeToSale(saleId);
		}else{
			ContractNoticeService.getService().sendContractReceiveNoticeToSale();
		}
		renderText("actionForSendContractMonthlyToSale执行完成ok");
	}
	
}
