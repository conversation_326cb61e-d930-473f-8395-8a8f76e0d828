<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>合同阶段看板</title>
    <style type="text/css">
        .hover-click:hover {
            color: #409eff;
            cursor: pointer;
        }

        .teams-tips {
            display: none;
        }

        .contract-url {
            display: none;
            margin-left: 10px;
            color: red;
        }

        .console-div {
            margin-bottom: 100px;
        }

        .edit-btn {
            float: right;
            margin-top: 5px;
            color: #1b9aee;
            cursor: pointer;
        }

        .no-data {
            text-align: center;
            padding: 30px;
        }

        .stat-item {
            width: 19%;
            display: inline-block;
        }

        .top-1 {
            font-size: 22px;
            font-weight: 500;
            color: #22cd0f;
            text-align: center;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
            text-align: center;
        }

        .top-3 {
            font-size: 22px;
            font-weight: 500;
            color: #f1a325;
            text-align: center;
        }

        .top-4 {
            font-size: 22px;
            font-weight: 500;
            color: #ea644a;
            text-align: center;
        }

        .top-5 {
            font-size: 22px;
            font-weight: 500;
            color: #03b8cf;
            text-align: center;
        }

        .layui-text h3 {
            font-size: 15px;
            font-weight: 500;
        }

        pre {
            background-color: #fff;
            border: none;
        }

        .layui-card {
            min-width: 100px;
            border: 1px solid #e9edf0;
            border: 1px;
            border-radius: 4px;
            -webkit-box-shadow: none;
            overflow: hidden;
        }

        .input-group-sm {
            margin-left: 5px;
        }

        .layui-icon-tips{
            margin-left: 5px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm" autocomplete="off">
        <input type="hidden" name="source" value="user">
        <input type="hidden" name="stageType" value="all">
        <div class="layui-row layui-col-space10 console-div">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header"> <span name="currentYear"></span>待回款客户排行</div>
                    <div class="layui-card-body" style="min-height: 250px;max-height: 400px;overflow-y: auto;">
                        <table class="layui-table">
                            <thead>
                            <tr>
                                <th>客户</th>
                                <th style="min-width: 90px">今年待收款</th>
                            </tr>
                            </thead>
                            <tbody data-template="yearRank-template" data-mars="ContractStatisticDao.unReceiptCustomerTop10">

                            </tbody>
                            <script id="yearRank-template" type="text/x-jsrender">
                             {{for data}}
                             <tr>
                                 <td><p class="text"><a href="javascript:custDetail('{{:CUST_ID}}')">{{:CUSTOMER_NAME}}</a></p></td>
                                 <td>{{:TOTAL_AMOUNT}}</td>
                            </tr>
                           {{/for}}
                              </script>
                        </table>
                    </div>
                </div>
                <div class="layui-card">
                    <div class="layui-card-header"> <span name="currentYear"></span>待回款合同排行<i class="layui-icon layui-icon-tips" lay-tips="统计每个销售的合同在今年里收款金额的总和。合同年限不限，收款日期为今年。以合同的销售经理为准，与收款负责人无关。"></i></div>
                    <div class="layui-card-body" style="min-height: 200px;max-height: 400px;overflow-y: auto;">
                        <table class="layui-table">
                            <thead>
                            <tr>
                                <th>合同名</th>
                                <th style="min-width: 90px">今年待收款</th>
                            </tr>
                            </thead>
                            <tbody data-template="rank-template" data-mars="ContractStatisticDao.unReceiptContractTop10">
                            </tbody>
                            <script id="rank-template" type="text/x-jsrender">
                             {{for data}}
                             <tr>
                                 <td><p class="text"><a href="javascript:contractDetail('{{:CUST_ID}}','{{:CONTRACT_ID}}')">{{:CONTRACT_SIMPILE_NAME}}</a></p></td>
                                 <td>{{:TOTAL_AMOUNT}}</td>
                            </tr>
                            {{/for}}
                              </script>
                        </table>
                    </div>
                </div>
            </div>
            <div class="layui-col-md9">
                <div class="layui-card" data-mars="ContractStatisticDao.contractStageConsoleStat">
                    <div class="layui-card-header">合同阶段统计(含税)<i class="layui-icon layui-icon-tips" lay-tips="以下金额数据全部为含税金额。"></i></div>
                    <div class="layui-card-body" style="min-height: 150px;">
                        <div class="stat-item">
                            <div class="top-5" id="COUNT_ALL">0</div>
                            <div class="top-2">全部待收款阶段数<i class="layui-icon layui-icon-tips" lay-tips="全部未完成收款的合同阶段总数。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-5" id="COUNT_YEAR">0</div>
                            <div class="top-2">今年待收款阶段数<i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为2024年且未完成收款的合同阶段总数。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-5" id="COUNT_MONTH">0</div>
                            <div class="top-2">本月待收款阶段数<i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为当月且未完成收款的合同阶段总数。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-3" id="COUNT_DUE">0</div>
                            <div class="top-2">即将到期阶段数<i class="layui-icon layui-icon-tips" lay-tips="距离计划完成日期不足7天且未完成收款的合同阶段总数。"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-4" id="COUNT_OVER">0</div>
                            <div class="top-2">已超时阶段数<i class="layui-icon layui-icon-tips" lay-tips="已超过计划完成日期仍未完成收款的合同阶段总数。"></i></div>
                        </div>
                        <hr>
                        <div class="stat-item">
                            <div class="top-5" id="SUM_ALL">0</div>
                            <div class="top-2">全部待收款金额<i class="layui-icon layui-icon-tips" lay-tips="全部未完成收款的合同阶段的 待收款金额总和(计划完成金额-已收款金额)。"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-5" id="SUM_YEAR">0</div>
                            <div class="top-2">今年待收款金额<i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为2024年且未完成收款的合同阶段 的 待收款金额总和。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-5" id="SUM_MONTH">0</div>
                            <div class="top-2">本月待收款金额<i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为当月且未完成收款的合同阶段 的 待收款金额总和。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-3" id="SUM_DUE">0</div>
                            <div class="top-2">即将到期阶段金额<i class="layui-icon layui-icon-tips" lay-tips="距离计划完成日期不足7天且未完成收款的合同阶段 的 待收款金额总和。"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-4" id="SUM_OVER">0</div>
                            <div class="top-2">已超时未收款金额<i class="layui-icon layui-icon-tips" lay-tips="已超过计划完成日期仍未完成收款的合同阶段 的 待收款的金额总和。包括今年和往年未完成的阶段。"></i></div>
                        </div>
                    </div>
                </div>
                <div class="layui-card">
                    <div class="layui-card-header">合同阶段列表</div>
                    <div class="layui-card-body teams" style="min-height: 150px;">
                        <div class="form-group" width="100%" style="display:flex">
                            <div class="input-group input-group-sm" style="width: 150px">
                                <span class="input-group-addon">合同号</span>
                                <input type="text" name="contractNo" class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm" style="width: 150px">
                                <span class="input-group-addon">合同名称</span>
                                <input type="text" name="contractName" class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm">
                                <label class="radio radio-info radio-inline">
                                    <input name="searchType" type="radio" value="1" checked onchange="toggleDateFields(this.value)"> <span>年</span>
                                </label>
                                <label class="radio radio-info radio-inline">
                                    <input name="searchType" type="radio" value="0" onchange="toggleDateFields(this.value)"> <span>月</span>
                                </label>
                            </div>
                            <div class="input-group input-group-sm" style="display: none">
                                <input type="text" name="dateField" class="form-control input-sm" value="year">
                            </div>
                            <div class="input-group input-group-sm" id="yearInput">
                                <span class="input-group-addon">计划完成年份</span>
                                <input type="text" name="yearId" class="form-control input-sm Wdate" onClick="WdatePicker({dateFmt:'yyyy'})" style="width: 100px;">
                            </div>
                            <div class="input-group input-group-sm" id="monthInput" style="display: none">
                                <span class="input-group-addon">计划完成月份</span>
                                <input type="text" name="monthId" class="form-control input-sm Wdate" onClick="WdatePicker({dateFmt:'yyyyMM'})" style="width: 100px;">
                            </div>
                            <div class="input-group input-group-sm">
                                <button type="button" class="btn btn-sm btn-default" onclick="StageConsole.query()"><span class="glyphicon glyphicon-search"></span> <span>搜索</span></button>
                                <button type="button" class="btn btn-sm btn-default ml-5" onclick="StageConsole.clearForm()">清空</button>
                            </div>
                        </div>
                        <div class="layui-tab layui-tab-card" lay-filter="contentFilter" style="border-top: 1px solid #ddd;">
                            <ul class="layui-tab-title">
                                <li lay-id="A" class="layui-this">全部</li>
                                <li lay-id="B">即将到期 <i class="layui-icon layui-icon-tips" lay-tips="距离计划完成日期不足7天，且收款未完成。"></i></li>
                                <li lay-id="C">本月待收款 <i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为本月，且收款未完成。"></i></li>
                                <li lay-id="D">全部待收款 <i class="layui-icon layui-icon-tips" lay-tips="所有未完成收款 的合同阶段(包含即将到期、已超时、未填写计划完成日期)。"></i></li>
                                <li lay-id="E">已超时 <i class="layui-icon layui-icon-tips" lay-tips="已过计划完成日期，且收款未完成。"></i></li>
                                <li lay-id="F">已完成 <i class="layui-icon layui-icon-tips" lay-tips="所有已完成收款 的合同阶段(包含超时完成)。"></i></li>
                                <li lay-id="G">超时完成 <i class="layui-icon layui-icon-tips" lay-tips="超时完成收款 的合同阶段。"></i></li>
                            </ul>
                            <div class="layui-tab-content" style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
                                <div class="layui-tab-item layui-show">
                                    <div id="table1">
                                        <table id="myContractStageTable"></table>
                                    </div>
                                    <div id="table2" style="display: none">
                                        <table id="myContractStageTable2"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </form>


</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script>
        jQuery.namespace("StageConsole");

        StageConsole.tabsId = 'A';
        var loadMap = {};
        $(function () {
            $("#searchForm").render({
                success: function () {
                    contractStageList.initContractStageList();
                    contractStageList.initContractStageList2();
                }
            });
        });
        var contractStageList = {
            initContractStageList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStageDao.myContractStageList',
                        cellMinWidth: 50,
                        limit: 10,
                        data: {homePage: 0, "stageType": "all"},
                        autoSort: false,
                        totalRow: true,
                        id: 'myContractStageTable',
                        cols: [[
                            {
                                type: 'numbers',
                                align: 'left',
                                totalRowText: "合计"
                            }, {
                                field: 'CONTRACT_SIMPILE_NAME',
                                title: '合同名称',
                                minWidth: 120,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractStageList.contractDetail',
                            }, {
                                field: 'STAGE_NAME',
                                title: '阶段名称',
                                align: 'center',
                                minWidth: 80,
                                width: 100,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractStageList.contractStageDetail',
                            }, {
                                title: '收款状态',
                                width: 80,
                                align: 'center',
                                templet: function (d) {
                                    return getStageCompleteStatus(d.PLAN_COMP_DATE, d.ACT_COMP_DATE, d.RCV_DATE);
                                }
                            }, {
                                field: 'RCV_DATE',
                                title: '应收日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'PLAN_COMP_DATE',
                                title: '计划完成日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'ACT_COMP_DATE',
                                title: '实际完成日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'PLAN_RCV_AMT',
                                title: '计划收款金额',
                                width: 110,
                                totalRow: true,
                                sort: true,
                            }, {
                                field: 'RECEIVED_AMT',
                                title: '已收款金额',
                                width: 110,
                                totalRow: true,
                                sort: true,
                            }, {
                                field: 'SALES_BY',
                                title: '销售经理',
                                width: 110,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    return getUserName(d.SALES_BY);
                                }
                            },
                        ]]
                    },
                )
            },
            initContractStageList2: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStageDao.myContractStageList',
                        cellMinWidth: 50,
                        limit: 10,
                        data: {homePage: 0, "stageType": "due"},
                        autoSort: false,
                        totalRow: true,
                        id: 'myContractStageTable2',
                        cols: [[
                            {
                                type: 'numbers',
                                align: 'left',
                                totalRowText: "合计"
                            }, {
                                field: 'CONTRACT_SIMPILE_NAME',
                                title: '合同名称',
                                minWidth: 120,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractStageList.contractDetail',
                            }, {
                                field: 'STAGE_NAME',
                                title: '阶段名称',
                                align: 'center',
                                minWidth: 80,
                                width: 100,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractStageList.contractStageDetail',
                            }, {
                                title: '收款状态',
                                width: 80,
                                align: 'center',
                                templet: function (d) {
                                    return getStageCompleteStatus(d.PLAN_COMP_DATE, d.ACT_COMP_DATE, d.RCV_DATE);
                                }
                            }, {
                                field: 'RCV_DATE',
                                title: '应收日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'PLAN_COMP_DATE',
                                title: '计划完成日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'PLAN_RCV_AMT',
                                title: '计划收款金额',
                                width: 110,
                                totalRow: true,
                                sort: true,
                            }, {
                                field: 'RECEIVED_AMT',
                                title: '已收款金额',
                                width: 110,
                                totalRow: true,
                                sort: true,
                            }, {
                                field: 'SALES_BY',
                                title: '销售经理',
                                width: 110,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    return getUserName(d.SALES_BY);
                                }
                            },
                        ]]
                    },
                )
            },
            contractDetail: function (data) {
                if (isArray(data)) {
                    data = data[0];
                }
                popup.openTab({id: 'contractDetail', title: '合同详情', url: '${ctxPath}/project/contract', data: {contractId: data.CONTRACT_ID, custId: data['CUST_ID'], isDiv: 0}});
            },
            contractStageDetail: function (data) {
                var contractId = data.CONTRACT_ID;
                var stageId = data.STAGE_ID;
                popup.layerShow({
                    type: 1,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    title: '合同阶段详情',
                    offset: 'r',
                    area: ['800px', '100%'],
                    url: '${ctxPath}/pages/crm/contract/include/stage-detail.jsp',
                    data: {contractId: contractId, stageId: stageId}
                });
            }
        }


        StageConsole.clearForm = function () {
            $("#searchForm")[0].reset();
            $('#yearInput').show();
            $('#monthInput').hide();
            $('input[name="dateField"]').val("year")

            layui.use('element', function(){
                var element = layui.element;
                element.tabChange('contentFilter', 'A'); // 'contentFilter' 是 lay-filter 对应的值，'A' 是第一个标签的 lay-id
            });

            StageConsole.tabsId = 'A';
           StageConsole.query();
        }

        StageConsole.query = function () {
            if (StageConsole.tabsId == 'A') {
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            } else if (StageConsole.tabsId == 'B') {
                $("#searchForm").queryData({id: 'myContractStageTable2', data: {}});
            } else if (StageConsole.tabsId == 'C') {
                $("#searchForm").queryData({id: 'myContractStageTable2', data: {}});
            } else if (StageConsole.tabsId == 'D') {
                $("#searchForm").queryData({id: 'myContractStageTable2', data: {}});
            } else if (StageConsole.tabsId == 'E') {
                $("#searchForm").queryData({id: 'myContractStageTable2', data: {}});
            } else if (StageConsole.tabsId == 'F') {
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            } else if (StageConsole.tabsId == 'G') {
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            }
        }

        layui.element.on('tab(contentFilter)', function (data) {
            StageConsole.tabsId = data.id;
            if (data.id == 'A') {
                $('#table1').show()
                $('#table2').hide()
                $('input[name="stageType"]').val("all");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            }
            else if (data.id == 'B') {
                $('#table1').hide()
                $('#table2').show()
                $('input[name="stageType"]').val("due");
                $("#searchForm").queryData({id: 'myContractStageTable2', data: {}});
            } else if (data.id == 'C') {
                $('#table1').hide()
                $('#table2').show()
                $('input[name="stageType"]').val("month");
                $("#searchForm").queryData({id: 'myContractStageTable2', data: {}});
            } else if (data.id == 'D') {
                $('#table1').hide()
                $('#table2').show()
                $('input[name="stageType"]').val("unComp");
                $("#searchForm").queryData({id: 'myContractStageTable2', data: {}});
            } else if (data.id == 'E') {
                $('#table1').hide()
                $('#table2').show()
                $('input[name="stageType"]').val("timeover");
                $("#searchForm").queryData({id: 'myContractStageTable2', data: {}});
            } else if (data.id == 'F') {
                $('#table1').show()
                $('#table2').hide()
                $('input[name="stageType"]').val("complete");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            } else if (data.id == 'G') {
                $('#table1').show()
                $('#table2').hide()
                $('input[name="stageType"]').val("complete2");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            }
        });


        function toggleDateFields(value) {
            if (value === '0') {
                $('#yearInput').hide();
                $('#monthInput').show();
                $('input[name="dateField"]').val("month")
            } else {
                $('#yearInput').show();
                $('#monthInput').hide();
                $('input[name="dateField"]').val("year")
            }
        }

        function custDetail(custId) {
            popup.openTab({id: 'custDetail', title: '客户详情', url: '${ctxPath}/pages/crm/cust/cust-detail.jsp', data: {custId: custId}});
        }

        function contractDetail(custId, contractId) {
            popup.openTab({id: 'contractDetail', title: '合同详情', url: '${ctxPath}/project/contract', data: {contractId: contractId, custId:custId, isDiv: 0}});
        }


        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>