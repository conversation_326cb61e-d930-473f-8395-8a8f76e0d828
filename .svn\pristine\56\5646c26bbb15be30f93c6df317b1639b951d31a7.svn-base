<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<!doctype html>
<html lang="en">
<head>
    <title>${docInfo.title}</title>
    <meta charset="UTF-8"/>
    <link rel="icon" type="image/x-icon" href="${ctxPath}/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link type="text/css" rel="stylesheet" href="/yq-work/static/lib/aieditor/style.css">
    <script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script> 
   	<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
    <style>
    
    	#msg{font-size: 12px;color: #999;margin-left: 10px;}
        .input {
	      width: 250px;
	      height: 20px;
	      line-height:20px;
	      border:none;
	      border-radius: 5px;
	      padding: 5px 10px;
	      font-size: 13px;
	      background-color: #f6f2fe;
	      margin-left: 10px;margin-top: 10px;margin-right: 10px;
	    }
	
	   .input:hover {
	      outline: none;
	      border-color: #336699;
	      background-color: #ffffff;
	    }
	    
	    .myDocs{
	    	position: absolute;
	    	right: 10px;
	    	color: #4298fd;
	    }
	    .collaboration{
	    	position: absolute;
	    	right: 100px;
	    	color: #4298fd;
	    }
	    
	    .update-by,.update-time{
	    	font-size: 13px;
	    	color: #000;
	    }
	    .update-time{margin-left: 10px;}
	    
        .page-header {
            background-color: #f6f2fe;
            height: 50px;
            line-height: 50px;
            padding: 0 2rem;
            display: flex;
            position: sticky;
            border-bottom: 1px solid #efefef;
            top: 0;
            z-index: 1;
        }

        .aie-header-panel {
            position: sticky;
            top: 51px;
            z-index: 1;
        }

        .aie-header-panel aie-header > div {
            align-items: center;
            justify-content: center;
            padding: 10px 0;
        }

        .aie-container {
            border: none !important;
        }

        .aie-container-panel {
            width: calc(100% - 314px);
            float:right;
            height: 100%;
            min-height: 100vh;
            padding: 1rem;
            z-index: 99;
        }

        .aie-main {
            position: relative;
            background-color: #fff;
            margin: 10px;    
            box-shadow: 0 0 0 1px hsla(0,0%,5%,.06),0 1px 4px hsla(0,0%,5%,.1);
            overflow: hidden;
            border-top-left-radius: 8px;
    		border-top-right-radius: 8px;
        }

        .aie-directory {
            position: absolute;
            top: 30px;
            left: 10px;
            width: 260px;
            z-index: 0;

        }

        .aie-directory h5 {
            color: #000000c4;
            font-size: 16px;
            text-indent: 4px;
            line-height: 32px;
            margin-top: 0px;
            margin-bottom: 0px;
        }

        .aie-directory a {
            height: 30px;
            font-size: 14px;
            color: #000000a3;
            text-indent: 4px;
            line-height: 30px;
            text-decoration: none;
            width: 100%;
            display: inline-block;
            margin: 0;
            padding: 0;
            white-space: nowrap;
            overflow: hidden;
            -o-text-overflow: ellipsis;
            text-overflow: ellipsis;
        }

        .aie-directory a:hover {
            cursor: pointer;
            background-color: #334d660f;
            border-radius: 4px;
        }

        .aie-title1 {
            font-size: 14px;
            font-weight: 500;
        }

        .aie-title2, .aie-title3, .aie-title4, .aie-title5, .aie-title6 {
            font-size: 12px;
        }

        .aie-directory-content {
        	 position: sticky;
        	 top: 10px;
        }

        @media screen and (max-width: 1280px) {
            .aie-directory {
                display: none;
            }
        }

        @media screen and (max-width: 1400px) {
            .aie-directory {
                width: 200px;
            }
        }
        #aiEditor{min-height: calc(100% - 100px);}
        
    </style>
</head>
<body style="padding: 0;margin: 0;background: #f3f4f6;">
	<div class="page-header">
	    <div>AI文档</div>
	    <input type="text" class="input" value="${docInfo.title}" id="title" name="title"/>
	    
	    <c:choose>
	    	<c:when test="${hisVersion=='1'}">
			    <span class="update-by">修改人：${docInfo.create_user_name}</span>
			    <span class="update-time">修改时间：${docInfo.create_time}</span>
	    	</c:when>
	    	<c:otherwise>
			    <span class="update-by">最后修改人：${docInfo.update_user_name}</span>
			    <span class="update-time">最后修改时间：${docInfo.update_time}</span>
	    	</c:otherwise>
	    </c:choose>
	    <span id="msg"></span>
	    <c:if test="${readonlyContent=='1'}">
		    <a class="collaboration" onclick="shareInfo();" href="javascript:;">协作(${hisVersionCount})</a>
	    </c:if>
	    <a class="myDocs" href="/yq-work/aiEditor/mine">我的文档</a>
	</div>
	<div id="aiEditor" style="padding: 0;margin: 0">
		<div class="aie-container" style="background-color: #f3f4f6">
        <div class="aie-header-panel">
            <div class="aie-container-header" style="background: #fff;"></div>
        </div>
        <div class="aie-main">
            <div class="aie-directory-content">
                <div class="aie-directory">
                    <h5>文档目录</h5>
                    <div id="outline"></div>
                </div>
            </div>
            <div class="aie-container-panel">
                <div class="aie-container-main"></div>
            </div>
        </div>
        <div class="aie-container-footer"></div>
       </div>
	</div>
	<script type="module">
        import {AiEditor} from '/yq-work/static/lib/aieditor/index.js'
        let aiEditor = new AiEditor({
            element: "#aiEditor",
            placeholder: "点击输入内容...",
			contentRetention:true,
			editable:readonlyContent=='1',
            content: '',
            ai: {
                models: {
                    spark: {
                        appId: "0e961b8b",
                        apiKey: "6fb921b31ca920f064deb940750c3991",
                        apiSecret: "ZmY4MWVkOTc0OTI2N2RlMGRkNzJkZmFl",
                    }
                }
            },
			attachment: {
        		customMenuInvoke: (editor) => {
       		
			 	},
        		uploadUrl: "/yq-work/servlet/upload?action=upload",
       	    	uploadFormName: "attachment",
 				uploadHeaders: ()=>{
           		  return {
               		"source": "aieditor",
               		"folderId": "0"
           		  }
        	    }
			},
  			onChange:(aiEditor)=>{
				$('#outline').html('');
				genTree(aiEditor);
				$('#msg').html('当前内容已发生变更，您可以通过按下“Ctrl+S”键来保存版本');
    		},
 			onCreated:(aiEditor)=>{
				aiEditorExtend(aiEditor);
        		genTree(aiEditor);
    		},
  			onSave:(editor)=>{
				  if(readonlyContent=='0'){
					 layer.msg('只读模式不允许修改保存',{icon:7,offset:'20px',time:1200});
					 return;
				  }
				  let title = $("#title").val();
				  let content = aiEditor.getHtml();
				  $.ajax({       					
    				url: '/yq-work/aiEditor/editDoc',       					
					type : 'post',cache:false,dataType : 'json',contentType : "application/x-www-form-urlencoded; charset=UTF-8", 					
    				data: {id:'${docInfo.doc_id}',content:content,title:title},       					
    				success: function (response) {
        				layer.msg('保存成功',{time:1200,icon:1});
						$('#msg').html('');
						lastEditInfo(); 			
    				},       					
    				error: function (error) {        
        				layer.alert('提交失败');       					
    				}     		        
				 });
    		}
        })
    </script>
    <script type="text/javascript">
    
    	var hisVersion = '${hisVersion}';
    	var readonlyContent = '${readonlyContent}';
    
    	document.addEventListener('keydown', function (event) {
          if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
          }
       });
	
       function shareInfo(){
    	   layer.open({content:'/yq-work/aiEditor/shareInfo?docId=${docInfo.doc_id}',type:2,area:['500px','100%'],shade:0.05,shadeClose:true,id:'share',scrollbar:false,offset:'r',title:false,closeBtn:false});
    	   
       }
       
       function genTree(aiEditor){
    	    var treeData = aiEditor.getOutline();
			for(var index in treeData){
			  var row = treeData[index];
			   $('#outline').append('<div class="aie-title'+row.level+'" style="margin-left: '+row.level*14+'px;"><a href="#'+row.id+'">'+row.text+'</a></div>');
			}
       }
    	
       function aiEditorExtend(aiEditor){
    	   $.ajax({url:"${ctxPath}/aiEditor/editorData/${docInfo.doc_id}",data:{hisVersion:hisVersion,hisId:'${hisId}'},success:function(result) { 
				if(result.state == 1){
					var data = result.data;
					aiEditor.setContent(data.content);
					 setTimeout(delayedAction, 2000);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			}});
       }
       
       function delayedAction() {
    	   $('#msg').html('');
       }
       
       function lastEditInfo(){
    	   $.ajax({url:"${ctxPath}/aiEditor/lastEditInfo/${docInfo.doc_id}",data:{},success:function(result) { 
				if(result.state == 1){
					var data = result.data;
					$('.update-by').html('修改人：'+data.update_user_name);
					$('.update-time').html('修改时间：'+data.update_time);
					$('.collaboration').html('协作('+data.hisVersionCount+')');
					if(data.self_user_id!=data.update_user_id&&areTimesWithinFiveSeconds(data.update_time,data.current_date)){
						layer.msg(data.update_user_name+'刚刚修改了版本',{icon:7,offset:'rt',time:3000});
					}
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			}});
       }
       
       
       function areTimesWithinFiveSeconds(time1Str, time2Str) {
    	    const time1 = new Date(time1Str);
    	    const time2 = new Date(time2Str);
    	    const timeDifferenceInMilliseconds = Math.abs(time1 - time2);
    	    return timeDifferenceInMilliseconds <= 5000;
    	}
    	
       $(function(){
    	   if(readonlyContent=='0'){
    		   layer.msg('当前是预览模式',{icon:7,offset:'auto',time:1800});
    	   }
    	   setInterval(lastEditInfo, 5000);
       });
    	
    </script>
</body>
</html>