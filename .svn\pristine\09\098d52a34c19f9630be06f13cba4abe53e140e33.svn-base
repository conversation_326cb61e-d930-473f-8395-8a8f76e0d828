<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div id="fileList">
 	<table class="layui-table folderTable" lay-size="sm" lay-even lay-skin="line">
	  <thead>
	    <tr>
	      <th>名称</th>
	      <th>大小</th>
	      <th>创建时间</th>
	      <th>创建者</th>
	      <th>操作</th>
	    </tr> 
	  </thead>
	  <tbody class="folderTbody" data-template="project-folder-template-files" data-mars="FolderDao.list">

	  </tbody>
	  <tbody class="fileTbody" data-template="project-detail-template-files" data-mars="FileDao.fileListDetail">

	  </tbody>
	</table>
  	<button class="btn btn-sm btn-info mt-30" type="button" onclick="addFolder()">+ 创建文件夹</button>
  	<button class="btn btn-sm btn-info mt-30 ml-15" type="button" onclick="$('#projectDetailLocalfile').click()">+上传</button>
  	<button class="btn btn-sm btn-default mt-30 ml-15" type="button" onclick="folderHome()">返回首页</button>
 </div>

		<script id="project-detail-template-files" type="text/x-jsrender">
			{{if data.length==0}}
				<tr>
					<td colspan="5">
						<button class="btn btn-sm btn-link mt-10" type="button" onclick="$('#projectDetailLocalfile').click()">+上传</button>
					</td>
				</tr>
			{{/if}}
			{{for data}}
				<tr>
				  <td>{{call:FILE_TYPE fn='getFileIcon'}} <a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}} &nbsp;</span></a></td>
				  <td>{{:FILE_SIZE}}</td>
				  <td>{{:CREATE_TIME}}</td>
				  <td>
					  {{:USERNAME}}
				  </td>
				  <td>
					  <a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" target="_blank"><span style="color:#17a6f0">下载<span></a>
				  </td>
				</tr>
			{{/for}}
		</script>
			<script id="project-folder-template-files" type="text/x-jsrender">
			{{for data}}
				<tr>
				  <td><svg class="icon" aria-hidden="true"><use xlink:href="#icon-wenjian"></use></svg><a href="javascript:;" onclick="openFolder('{{:FOLDER_ID}}')">{{:FOLDER_NAME}}</a></td>
				  <td></td>
				  <td>{{:CREATE_TIME}}</td>
				  <td>
					  {{call:CREATOR fn='getUserName'}}
				  </td>
				  <td>
					
				  </td>
				</tr>
			{{/for}}
		</script>
		
		
	<script type="text/javascript">
		var projectUploadFile = function(){
			var fkId=projectId;
			var folderId = $("#folderId").val();
			easyUploadFile({callback:'projectDetailCallback',fkId:fkId,folderId:folderId,source:'project',formId:'projectDetailFileForm',fileId:'projectDetailLocalfile'});
			
		}
	 	 var addFolder = function(){
			var folderName = '根目录';
			var folderAuth = '1';
			var fkId = projectId;
			var pid = $("#folderId").val();
			popup.layerShow({type:1,maxmin:true,area:['450px','300px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'新增文件夹',data:{id:pid,folderName:folderName,folderAuth:folderAuth,source:'project',fkId:fkId}});
		}
	 	 
	 	 var openFolder = function(id){
	 		 $("#folderId").val(id);
	 		 $(".folderTbody").show();
	 		 $(".fileTbody").show();
	 		 $("#fileList").render({data:{folderId:id,fkId:projectId,source:'project'}});
	 	 }
	 	 
	 	 var folderHome = function(){
	 		 $(".folderTbody,.fileTbody").show();
	 		 $("#fileList").render({data:{folderId:'0',fkId:projectId,source:'project'}});
	 	 }
	
	</script>