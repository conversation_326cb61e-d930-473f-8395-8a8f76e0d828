package com.yunqu.work.servlet.ehr;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.HttpKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.dao.other.FoodDao;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.DcNoticeService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.DateUtils;

@WebServlet("/servlet/food")
public class FoodServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public JSONObject actionForDsapi(){
		String url = "http://open.iciba.com/dsapi/";
		String result=HttpKit.get(url);
		return JSONObject.parseObject(result);
	}
	public String getOpenId(){
		try {
			String openId=this.getQuery().queryForString("select OPEN_ID from "+Constants.DS_MAIN_NAME+".EASI_USER WHERE USER_ID = ?",getUserId());
			return openId;
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return "";
	}
	public EasyResult actionForAddFood(){
		EasyRecord record=new EasyRecord("DC_FOOD","ID");
		record.setColumns(getJSONObject("food"));
		record.remove("ID");
		record.set("CREATE_TIME", EasyDate.getCurrentDateString());
		try {
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateFood(){
		EasyRecord record=new EasyRecord("DC_FOOD","ID");
		record.setColumns(getJSONObject("food"));
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	/**
	 * 中餐
	 * @return
	 */
	private String getFoodId(){
		String sql="select food_id from dc_meals where dinner_type =2";
		try {
			return this.getQuery().queryForString(sql);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return null;
		}
	}
	/**
	 * 批量预约
	 * @return
	 * state：0  确认订餐  1 预约 2  不点了
	 */
	public EasyResult actionForBookPlan(){
		JSONObject jsonObject=getJSONObject();
		JSONArray dates=jsonObject.getJSONArray("dates");
		EasyRecord record=new EasyRecord("dc_reserve_list","id");
		String foodId=getFoodId();
		if(StringUtils.isBlank(foodId))return EasyResult.fail("无菜单,无法预约");
		for(Object obj:dates){
			String ymd=obj.toString();
			record.set("ymd", ymd);
			record.set("ym", jsonObject.getString(ymd+"_ym"));
			record.set("week", jsonObject.getString(ymd+"_week"));
			record.set("state", 1);
			record.set("book_type", 1);
			record.set("id", RandomKit.uniqueStr());
			record.set("dinner_type", 2);
			record.set("food_list_id", foodId);
			record.set("user_id", getUserId());
			record.set("create_by", getUserId());
			record.set("user_name", getUserName());
			record.set("dept_id", getDeptId());
			record.set("create_time", EasyDate.getCurrentDateString());
			try {
				this.getQuery().save(record);
			} catch (SQLException e) {
				e.printStackTrace();
				this.error(e.getMessage(), e);
			}
		}
		String openId=getOpenId();
		if(StringUtils.isBlank(openId)){
			return EasyResult.ok(dates,"预约成功.");
		}
		return EasyResult.ok(dates);
	}
	
	public EasyResult actionForTodayBookFood(){
		try {
			JSONObject jsonObject=getJSONObject();
			String dinnerType=jsonObject.getString("DINNER_TYPE");
			String userId=jsonObject.getString("userId");
			String date=jsonObject.getString("dcDate");
			String ymd=null;
			if(StringUtils.isBlank(userId)){
				userId=getUserId();
				ymd=String.valueOf(DateUtils.getPlanToday());
			}else{
			   ymd= DateUtils.getDate(date, "yyyyMMdd");
			}
			String result=this.getQuery().queryForString("select state from dc_reserve_list where user_id = ? and dinner_type = ? and ymd = ? ",userId,dinnerType,ymd);
			if(StringUtils.notBlank(result)){
				if("1".equals(result)){
					return EasyResult.fail("您已预约,不能重复!");
				}else{
					return EasyResult.fail("您已经点餐过,不能重复!");
				}
			}
			String bookType=jsonObject.getString("bookType");
			// todo 
			//bookType=2 补点
			//点餐时间控制
			if(!"2".equals(bookType)){
				int week=DateUtils.getTodayWeek();
				if("1".equals(FoodDao.dcSwitch)){
					return EasyResult.fail("订餐已经关闭,不能点餐.");
				}
				if(week==7&&"none".equals(FoodDao.week6)){
					return EasyResult.fail("周六不能点餐");
				}
				if(week==1&&"none".equals(FoodDao.week7)){
					return EasyResult.fail("周日不能点餐");
				}
				String current=DateUtils.getTodayHm();
				JSONObject object=this.getQuery().queryForRow("select * from dc_eat_time where dinner_type = ?", new Object[]{dinnerType}, new JSONMapperImpl());
				String b=object.getString("BEGIN_TIME");
				String e=object.getString("END_TIME");
				if(b.compareTo(current)>0){
					return EasyResult.fail("还没到点餐时间.");
				}else if(current.compareTo(e)>0){
					return EasyResult.fail("点餐已结束.");
				}
				if("2".equals(result)){
					this.getQuery().executeUpdate("update dc_reserve_list set state = 0,create_time = ? where user_id = ? and dinner_type = ? and ymd = ? ",EasyDate.getCurrentDateString(),userId,dinnerType,ymd);
					return EasyResult.ok(null,"点餐成功!");
				}
			}
			
			EasyRecord record=new EasyRecord("dc_reserve_list","id");
			record.set("state", 0);//0 已点  1 预约待确认点  2 不点
			record.set("food_list_id", jsonObject.getString("ID"));
			record.set("book_type", bookType);
			record.set("id", RandomKit.uniqueStr());
			record.set("dinner_type", dinnerType);
			record.set("user_id", userId);
			record.set("create_by", getUserId());
			record.set("user_name", getUserName());
			record.set("dept_id", getDeptId());
			record.set("ymd",ymd);
			if(StringUtils.notBlank(date)){
				record.set("ym", DateUtils.getDate(date, "yyyyMM"));
				record.set("week",DateUtils.getWeek(date));
				record.set("create_time",date);
			}else{
				record.set("ym", DateUtils.getTodayYm());
				record.set("week", DateUtils.getTodayWeek());
				record.set("create_time", EasyDate.getCurrentDateString());
			}
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		String openId=getOpenId();
		if(StringUtils.isBlank(openId)){
			return EasyResult.ok(null,"点餐成功.");
		}else {
//			WxMsgService.getService().sendFoodServedMsg(null);
		}
		return EasyResult.ok(null,"点餐成功!");
	}
	public EasyResult actionForJoinMeals(){
		JSONArray array=getJSONArray();
		for(int i=0;i<array.size();i++){
			JSONObject jsonObject=array.getJSONObject(i);
			EasyRecord record=new EasyRecord("dc_meals","id");
			record.set("dinner_type",jsonObject.getString("DINNER_TYPE"));
			record.set("food_id",jsonObject.getString("ID"));
			try {
				int x=0;
				int count=this.getQuery().queryForInt("select count(1) from dc_meals where food_id = ?", jsonObject.getString("ID"));
				if(count<=0){
					this.getQuery().save(record);
				}else{
					x++;
				}
				if(x>0)return EasyResult.fail(x+"个菜单重复加入.");
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}
	public EasyResult actionForSetting(){
		JSONObject jsonObject=getJSONObject();
		String b_1=jsonObject.getString("b_1");
		String e_1=jsonObject.getString("e_1");
		String b_2=jsonObject.getString("b_2");
		String e_2=jsonObject.getString("e_2");
		String dcSwitch=jsonObject.getString("dcSwitch");
		
		try {
			this.getQuery().executeUpdate("update dc_eat_time set begin_time = ? ,end_time = ? where dinner_type = ?", b_1,e_1,2);
			this.getQuery().executeUpdate("update dc_eat_time set begin_time = ? ,end_time = ? where dinner_type = ?", b_2,e_2,3);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		FoodDao.dcSwitch=dcSwitch;
		if(jsonObject.getJSONArray("week6")!=null){
			FoodDao.week6="block";
		}else{
			FoodDao.week6="none";
		}
		if(jsonObject.getJSONArray("week7")!=null){
			FoodDao.week7="block";
		}else{
			FoodDao.week7="none";
		}
		
		return EasyResult.ok();
	}
	public EasyResult actionForCannelBook(){
		JSONObject jsonObject=getJSONObject();
		EasyRecord record=new EasyRecord("dc_reserve_list","id");
		record.set("id",jsonObject.getString("id"));
		try {
			this.getQuery().deleteById(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForBatchCannelBook(){
		JSONObject jsonObject=getJSONObject();
		String ids = jsonObject.getString("ids");
		try {
			EasySQL sql = new EasySQL("delete from dc_reserve_list where 1=1");
			sql.appendIn(ids.split(","),"and id");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelMeal(){
			JSONObject jsonObject=getJSONObject();
			EasyRecord record=new EasyRecord("dc_meals","id");
			record.set("id",jsonObject.getString("MEAL_ID"));
			try {
				this.getQuery().deleteById(record);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelMeals(){
		JSONArray array=getJSONArray();
		for(int i=0;i<array.size();i++){
			JSONObject jsonObject=array.getJSONObject(i);
			EasyRecord record=new EasyRecord("dc_meals","id");
			record.set("id",jsonObject.getString("MEAL_ID"));
			try {
				this.getQuery().deleteById(record);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}
	public EasyResult actionForJoinMeal(){
		JSONObject jsonObject=getJSONObject();
		String foodId=jsonObject.getString("ID");
		try {
			int count=this.getQuery().queryForInt("select count(1) from dc_meals where food_id = ?", foodId);
			if(count>0)return EasyResult.fail("已经存在,不能重复添加.");
			EasyRecord record=new EasyRecord("dc_meals","id");
			record.set("dinner_type",jsonObject.getString("DINNER_TYPE"));
			record.set("food_id",jsonObject.getString("ID"));
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	/**
	 * 有事情不点餐了
	 * @return
	 */
	public EasyResult actionForNotBookPlan(){
		try {
			String foodId=getFoodId();
			if(StringUtils.isBlank(foodId))return EasyResult.fail("无菜单,无法取消预约");
			
			JSONObject jsonObject=getJSONObject();
			EasyRecord record=new EasyRecord("dc_reserve_list","id");
			String ymd=getJsonPara("ymd");
			record.set("ymd", ymd);
			record.set("ym", jsonObject.getString(ymd+"_ym"));
			record.set("week", jsonObject.getString(ymd+"_week"));
			record.set("state", 2);
			record.set("book_type", 1);
			record.set("food_list_id", getFoodId());
			record.set("id", RandomKit.uniqueStr());
			record.set("dinner_type", 2);
			record.set("user_id", getUserId());
			record.set("create_by", getUserId());
			record.set("user_name", getUserName());
			record.set("dept_id", getDeptId());
			record.set("create_time", EasyDate.getCurrentDateString());
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok(null,"取消成功!");
	}
	public EasyResult actionForSureBookPlan(){
		try {
			this.getQuery().executeUpdate("update dc_reserve_list set state = 1 ,create_time = ? where ID = ?",EasyDate.getCurrentDateString(),getJsonPara("id") );
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForBookPlanOk(){
		try {
			this.getQuery().executeUpdate("update dc_reserve_list set state = 0 ,create_time = ? where ID = ?",EasyDate.getCurrentDateString(),getJsonPara("id") );
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForBatchDel(){
		JSONArray array=getJSONArray();
		for(int i=0;i<array.size();i++){
			JSONObject jsonObject=array.getJSONObject(i);
			try {
				this.getQuery().executeUpdate("update DC_FOOD set state = 0 where ID = ?",jsonObject.getString("ID") );
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelFood(){
		try {
			this.getQuery().executeUpdate("update DC_FOOD set state = 0 where ID = ?",getJsonPara("id") );
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForEnableFood(){
		try {
			this.getQuery().executeUpdate("update DC_FOOD set state = 1 where ID = ?",getJsonPara("id") );
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForNotice(){
		DcNoticeService.getService().sendDcNotice(1);
		return EasyResult.ok();
	}
	
	public void actionForFoodServedMsg(){
			String remark=getJsonPara("remark");
			String userId=getJsonPara("userId");
			if(StringUtils.isBlank(remark))remark="祝您用餐愉快,吃肉开心！";
			String sql="select t1.user_id,t2.food_name from dc_reserve_list t1 INNER JOIN dc_food t2 on t2.id=t1.food_list_id where t1.ymd= ? and t1.state=0 ";
			if(StringUtils.notBlank(userId)){
				sql=sql+" and t1.USER_ID = '"+userId+"'";
			}else{
				int dinnerType=2;
				if(DateUtils.getHour()>14){
					dinnerType=3;
				}
				sql=sql+" and t1.dinner_type = "+dinnerType+"";
			}
			List<JSONObject> list;
			try {
				list = EasyQuery.getQuery(Constants.APP_NAME,Constants.DS_NAME).queryForList(sql,new Object[]{DateUtils.getPlanToday()},new JSONMapperImpl());
				if(list!=null){
					for(int i=0;i<list.size();i++){
						MessageModel messageModel=new MessageModel();
						messageModel.setTitle(list.get(i).getString("FOOD_NAME"));
						messageModel.setReceiver(list.get(i).getString("USER_ID"));
						messageModel.setDesc(remark);
						WxMsgService.getService().sendFoodServedMsg(messageModel);
					}
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}

		renderJson(EasyResult.ok());
	}
	public EasyResult actionForDc(){
		return null;
	}

}
