<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的订餐统计</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form name="searchForm" autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-cutlery"></span> 我的订餐统计</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">月份</span>	
							 <input type="text" name="ym" placeholder="默认当月" onClick="WdatePicker({dateFmt:'yyyyMM'})" class="form-control input-sm Wdate" style="width: 90px;">
					     </div>
	        		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">类型</span>	
							 <select name="dinnerType" class="form-control input-sm" >
							 	<option value="">全部</option>
							 	<option value="2">中餐</option>
							 	<option value="3">晚餐</option>
							 </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						<table class="layui-hide" id="t"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		requreLib.setplugs('wdate');
		$(function(){
			list.init1();
		});
		var list={
			init1:function(){
				$("#searchForm").initTable({
					mars:'FoodDao.myBookStat',
					limit:30,
					id:'t',
					height:'full-90',
					cols: [[
					  {title:'序号',type:'numbers',width:50},
		              {
					    field: 'FOOD_NAME',
						title: '菜单',
						align:'left',
						templet:function(row){
							var state=row.STATE;
							var s='';
							if(state==2){
								s='<label class="label label-info label-outline mr-5">取消</label>';
							}
							if(state==1){
								s='<label class="label label-info label-outline mr-5">预约</label>';
							}
							return s+row.FOOD_NAME;
						}
					},{
					    field: 'YMD',
						title: '点餐日期',
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center'
					},{
					    field: 'DINNER_TYPE',
						title: '类型',
						align:'center',
						templet:function(row){
							if(row.DINNER_TYPE==1){
								return '早餐';
							}
							if(row.DINNER_TYPE==2){
								return '中餐';
							}
							if(row.DINNER_TYPE==3){
								return '晚餐';
							}
							return '';
						}
					}
				 ]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>