package com.yunqu.work.servlet.erp;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.service.WxMsgService;

@WebServlet("/servlet/order/flow")
public class OrderFlowServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	/**
	 * 执行审批
	 * @return
	 */
	public EasyResult actionForAddCheck() {
		EasyRecord record = new EasyRecord("yq_erp_order_review_result","result_id");
		try {
			JSONObject object = getJSONObject();
			String reviewId = object.getString("reviewId");
			String orderId = object.getString("orderId");
			String resultId = object.getString("resultId");
			String nodeId = object.getString("nodeId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("评审人不存在");
			}
			int checkResult = object.getIntValue("checkResult");//1 通过 2拒绝
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("review_id", reviewId);
			record.set("order_id", orderId);
			record.set("check_user_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc", object.getString("checkDesc"));
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			JSONObject nodeInfo= this.getQuery().queryForRow("select * from yq_erp_order_check_node where node_id = ?", new Object[]{nodeId}, new JSONMapperImpl());
			
			int reviewState = 0; // 0 待审批 10审批中 20审批完成
			String cNodeId = nodeInfo.getString("C_NODE_ID");//获取下一级节点
			String userId = nodeInfo.getString("USER_ID");//给此人发待办提醒
			
			MessageModel msgModel = new MessageModel();
			if("0".equals(cNodeId)) {
				//完成了
				reviewState = 20;
				msgModel.setReceiver(userId);
				WxMsgService.getService().sendFlowTodoCheck(msgModel);
			}else {
				reviewState = 10;
				this.getQuery().executeUpdate("update yq_erp_order_review set current_node_id = ? where review_id = ?",cNodeId,reviewId);
			}
			this.getQuery().executeUpdate("update yq_erp_order_review set current_result_id = ?,review_state = ?,last_result_time = ? where review_id = ?", resultId,reviewState,EasyDate.getCurrentDateString(),reviewId);

		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddPaymentCheck() {
		EasyRecord record = new EasyRecord("yq_erp_order_payment_result","result_id");
		try {
			JSONObject object = getJSONObject();
			String paymentId = object.getString("paymentId");
			String orderId = object.getString("orderId");
			String resultId = object.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("评审人不存在");
			}
			int checkResult = object.getIntValue("checkResult");
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("payment_id", paymentId);
			record.set("order_id", orderId);
			record.set("check_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc", object.getString("checkDesc"));
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			addOrderLog(orderId,"合同评审审批","20","合同评审");
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	/**
	 * 发起评审
	 * @return
	 */
	public EasyResult actionForReviewApply() {
		JSONObject object = getJSONObject();
		String id = RandomKit.uniqueStr();
		
		String sql = "select GROUP_CONCAT(user_id) user_ids,GROUP_CONCAT(user_name) user_names from yq_erp_order_check_node where busi_type='review' and node_state = 0 ORDER BY check_index";
		JSONObject checkObj = null;
		try {
			checkObj = this.getQuery().queryForRow(sql,new Object[] {},new JSONMapperImpl());
		} catch (SQLException e) {
			this.error(null, e);
		}
		
		String userIds = checkObj.getString("USER_IDS");
		String userNames = checkObj.getString("USER_NAMES");
		String orderId = object.getString("orderId");
		String reviewLevel = object.getString("reviewLevel");
		EasyRecord record = new EasyRecord("yq_erp_order_review","review_id");
		record.set("apply_time", EasyDate.getCurrentDateString());
		record.set("apply_by", getUserId());
		record.set("last_review_date", object.getString("lastReviewDate"));
		record.set("review_level", reviewLevel);
		record.set("review_no", object.getString("reviewNo"));
		record.set("remark", object.getString("remark"));
		record.set("order_id", orderId);
		record.set("check_ids", userIds);
		record.set("check_names", userNames);
		record.set("create_name", getUserName());
		record.setPrimaryValues(id);
		try {
			this.getQuery().save(record);
			this.getQuery().executeUpdate("update yq_erp_order set review_id = ?,last_follow_label = '采购评审',last_follow_time = ?  where order_id = ?",id,EasyDate.getCurrentDateString(),orderId);
			
			List<JSONObject> list = this.getQuery().queryForList("select * from yq_erp_order_check_node where busi_type = ? and node_state = 0 order by check_index", new Object[] {"review"}, new JSONMapperImpl());
			for(int i=0;i<list.size();i++) {
				EasyRecord resultRecord = new EasyRecord("yq_erp_order_review_result","result_id");
				JSONObject obj = list.get(i);
				String nodeId = obj.getString("NODE_ID");
				String userId =obj.getString("USER_ID");
				String userName =obj.getString("USER_NAME");
				resultRecord.set("result_id", RandomKit.uniqueStr());
				resultRecord.set("review_id", id);
				resultRecord.set("order_id", orderId);
				resultRecord.set("check_name", userName);
				resultRecord.set("check_user_id", userId);
				resultRecord.set("check_result", 0);
				resultRecord.set("node_id",nodeId);
				resultRecord.set("check_desc","");
				if(i==0) {
					resultRecord.set("get_time",EasyDate.getCurrentDateString());
					this.getQuery().executeUpdate("update yq_erp_order_review set current_node_id = ?  where review_id = ?",nodeId,id);
					//微信推送给审批人
					MessageModel msgModel  = new MessageModel();
					msgModel.setReceiver(userId);
					WxMsgService.getService().sendFlowTodoCheck(msgModel);
					
				}
				this.getQuery().save(resultRecord);
			}
			addOrderLog(orderId,"发起合同评审","20","合同评审");
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForPaymentApply() {
		JSONObject object = getJSONObject();
		String id = RandomKit.uniqueStr();
		String orderId = object.getString("orderId");
		String userIds = object.getString("userIds");
		String paymentLevel = object.getString("paymentLevel");
		String userNames = object.getString("userNames");
		EasyRecord record = new EasyRecord("yq_erp_order_payment","payment_id");
		record.set("apply_time", EasyDate.getCurrentDateString());
		record.set("apply_by", getUserId());
		record.set("last_do_date", object.getString("lastDoDate"));
		record.set("payment_level", paymentLevel);
		record.set("payment_no", object.getString("paymentNo"));
		record.set("remark", object.getString("remark"));
		record.set("order_id", orderId);
		record.set("check_ids", userIds);
		record.set("check_names", userNames);
		record.set("create_name", getUserName());
		record.setPrimaryValues(id);
		try {
			this.getQuery().save(record);
			this.getQuery().executeUpdate("update yq_erp_order set payment_id = ?,last_follow_label = '付款申请',last_follow_time = ?  where order_id = ?",id,EasyDate.getCurrentDateString(),orderId);
			
			String[] userIdsArray = userIds.split(",");
			for(int i=0;i<userIdsArray.length;i++) {
				EasyRecord resultRecord = new EasyRecord("yq_erp_order_payment_result","result_id");
				String userId =userIdsArray[i];
				resultRecord.set("result_id", RandomKit.uniqueStr());
				resultRecord.set("payment_id", id);
				resultRecord.set("order_id", orderId);
				resultRecord.set("check_id", userId);
				resultRecord.set("check_result", 0);
				resultRecord.set("check_line", 0);
				resultRecord.set("check_desc","");
				resultRecord.set("create_time",EasyDate.getCurrentDateString());
				this.getQuery().save(resultRecord);
			}
			this.getQuery().executeUpdate("update yq_erp_order_payment_result t1,"+Constants.DS_MAIN_NAME+".easi_user t2 set t1.check_name = t2.username where t1.check_id = t2.user_id and t1.payment_id = ?", id);
			this.getQuery().executeUpdate("update yq_erp_order_payment_result t1,yq_erp_order_check_node t2 set t1.check_line = t2.check_line where t1.check_id = t2.user_id and t1.payment_id = ?", id);
			
			addOrderLog(orderId,"发起付款申请","30","付款申请");
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	private void addOrderLog(String orderId,String followContent,String followState,String followStateLabel) {
		EasyRecord model = new EasyRecord("YQ_ERP_ORDER_FOLLOW","FOLLOW_ID");
		model.set("FOLLOW_DATE", EasyCalendar.newInstance().getDateInt());
		model.set("ADD_TIME", EasyDate.getCurrentDateString());
		model.set("ADD_BY", getUserId());
		model.set("USER_NAME", getUserName());
		model.set("FOLLOW_STATE", followState);
		model.set("FOLLOW_STATE_LABEL", followStateLabel);
		model.set("FOLLOW_CONTENT", followContent);
		try {
			this.getQuery().executeUpdate("update yq_erp_order set last_follow_content = ?,last_follow_time = ?,last_follow_label = ? where order_id = ?",followContent,EasyDate.getCurrentDateString(),followStateLabel,orderId);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
}
