var ctxPath = getCtxPath();

function editContract(){
	var contractId = $("#contractId").val();
	var custId = $("#contractEditForm [name='custId']").val();
	popup.layerShow({type:1,full:false,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['70%','100%'],url:ctxPath+'/pages/crm/contract/contract-edit.jsp',title:'编辑合同',data:{contractId:contractId,custId:custId}});
}

function projectMgr(){
	var projectId = $("#contractId").val();
	popup.openTab({id:'projectId',url:ctxPath+'/pages/project/project-detail.jsp',title:'项目总览',data:{projectId:projectId}});	
}

function getValText(val,row,el){
	var textJson = row['TEXT_JSON'];
	var name = el.attr('name')||el.attr('id');
	var realName = name.split('.')[1];
	if(textJson){
		var json = eval('(' + textJson + ')');
		return json[realName];
	}else{
		return val;
	}
}


layui.config({
	  base: ctxPath+'/static/js/'
}).use('tableSelect');



$(function(){


});



