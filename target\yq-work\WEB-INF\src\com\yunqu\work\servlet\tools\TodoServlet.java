package com.yunqu.work.servlet.tools;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.TodoModel;
/**
 * todo
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/todo/*")
public class TodoServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		TodoModel model=getModel(TodoModel.class, "todo");
		model.setPrimaryValues(RandomKit.uniqueStr());
		model.addCreateTime();
		model.setCreator(getUserPrincipal().getUserId());
		String date =  model.getString("BEGIN_TIME");
		model.set("DATE_ID", date.replaceAll("-","").substring(0,8));
		try {
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		TodoModel model=getModel(TodoModel.class, "todo");
		try {
			String date =  model.getString("BEGIN_TIME");
			if(StringUtils.notBlank(date)) {
				model.set("DATE_ID", date.replaceAll("-","").substring(0,8));
			}
			model.set("update_time",EasyDate.getCurrentDateString());
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		TodoModel model=getModel(TodoModel.class, "todo");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}





