<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>项目模块</title>
	<style>
		.icon {width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:2px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="projectModules" class="form-inline">
		<input name="projectId" type="hidden" value="${param.projectId}">
		<div class="ibox-content" style="min-height: calc(100vh - 100px)">
			<table class="layui-hide" id="modules"></table>
			<button class="btn btn-info btn-sm mt-10" type="button" onclick="publishVersion('1');">发布版本</button>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">

  <script type="text/javascript">
		
	var projectId = '${param.projectId}';
	
	function projectModule(){
		$("#projectModules").initTable({
			mars:'ProjectDao.projectModule',
			id:"modules",
			page:false,
			data:{},
			edit:'editModule',
			cols: [[
			 {type:'numbers'},
			 {type:'checkbox'},
             {
        	 	field: 'MODULE_NAME',
				title: '模块名称',
				edit:'text',
				align:'left'
			 },
			 {
        	 	field: 'LAST_VERSION',
				title: '最新版本',
				align:'left'
			},
			 {
        	 	field: 'REMARK',
				title: '备注',
				edit:'text',
				align:'center'
			},
			{
			    field: 'UPDATE_BY',
				title: '更新人',
				align:'left',
				width:90,
				templet:function(row){
					return getUserName(row.UPDATE_BY);
				}
			},{
			    field: 'UPDATE_TIME',
				title: '更新时间',
				align:'left'
			}
			]],done:function(result){
				
			}});
	}
	
	function editModule(obj){
		var data = obj.data;
	    if(isSuperUser){
			var params={PROJECT_ID:projectId,MODULE_ID:data.MODULE_ID,MODULE_NAME:data.MODULE_NAME,REMARK:data.REMARK};
			ajax.remoteCall("${ctxPath}/servlet/project?action=updateModule",params,function(result) { 
				$("#projectModules").queryData({id:'modules',page:false});
				if(result.state != 1){
					layer.alert(result.msg,{icon: 7});
				}
			},{loading:false});
	    }else{
	    	layer.msg("修改无效,您无权修改!");
	    }
	}
	
	function publishVersion(type){
		var moduleIds = '';
		if(type=='1'){
			var checkStatus = table.checkStatus('modules')
	 		var data = checkStatus.data;
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择模块。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		
	 		var array = [];
	 		for(var index in data){
	 			var moduleId = data[index]['MODULE_ID'];
	 			if(moduleId){
	 				array.push(moduleId);
	 			}
	 		}
	 		moduleIds = array.join(',');
		}
		
		var projectName = $("[name='project.PROJECT_NAME']").text();
		popup.layerShow({type:1,maxmin:true,anim:0,full:true,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/ops/version-edit.jsp',title:'新增版本',data:{type:type,moduleIds:moduleIds,appName:projectName,versionType:1,projectId:projectId,projectName:projectName}});
	}

	 $(function(){
		 projectModule();
	 });
 	
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>