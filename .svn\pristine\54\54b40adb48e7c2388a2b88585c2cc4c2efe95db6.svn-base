package com.yunqu.work.servlet.crm;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.ContractStageModel;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;


import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
@WebServlet("/servlet/contractStage/*")
public class ContractStageServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    /**
     * 执行添加操作的函数。
     * 本函数用于创建一个新的合同阶段模型实例并保存到数据库中。
     *
     * 由于数据库要求，stage_name不能为空
     * model还需要考虑把contract_id带上
     *
     * @return EasyResult
     */
    public EasyResult actionForAdd(){
        ContractStageModel model = getModel(ContractStageModel.class, "contractStage");
        model.setCreator(getUserId());
        model.addCreateTime();
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        //随机生成主键STAGE_ID
        //用setStageId应该有同样效果
        model.setPrimaryValues(RandomKit.uuid().toUpperCase());
       try {
           //这个save包含了getQuery().save(this)，提交到数据库
           model.save();
           String contractId=model.getContractId();
           if(StringUtils.notBlank(contractId)){
               //可能进行对project_contract表的更新

           }
       } catch (SQLException e) {
           this.error(e.getMessage(), e);
           return EasyResult.fail(e.getMessage());
       }

        return EasyResult.ok();
    }

    public EasyResult actionForUpdate(){
        ContractStageModel model=getModel(ContractStageModel.class,"contractStage");
        model.set("UPDATE_TIME",EasyDate.getCurrentDateString());
        model.set("UPDATE_BY",getUserId());
        try {
            model.update();
            String contractId=model.getContractId();
            if(StringUtils.notBlank(contractId)){
                //可能进行对project_contract表的更新
                //this.getQuery().executeUpdate("update yq_project_contract ...", contractId);

            }

        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    /**
     *通过STAGE_ID删除单条纪录
     */
    public EasyResult actionForDelData(){

            String id = getJsonPara("stageId");
            try {
                this.getQuery().executeUpdate("delete from yq_contract_stage where STAGE_ID = ?", id);
                //可能对project_contract表也进行更新
                //this.getQuery().executeUpdate("update yq_project_contract ...", contractId);
            } catch (SQLException e) {
                e.printStackTrace();
                return EasyResult.fail(e.getMessage());
            }

        return EasyResult.ok();
    }

    /**
     * 不通过model，更改指定字段
     * stage_id必传
     * 前端传入例子： data : {"stage_id":"1ADCACE6BE254186B14E4ECE6FE04983","stage_name":"test","kaipiao_amt":"8000"}
     */
    public EasyResult actionForUpdateObj(){
        EasyRecord record = new EasyRecord("YQ_CONTRACT_STAGE","STAGE_ID");
        try {
            record.setColumns(getJSONObject("contractStage"));
            record.set("UPDATE_BY", getUserId());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getQuery().update(record);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }
}
