<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
	
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="ContractDetailForm" data-mars="ProjectContractDao.record" data-mars-prefix="contract.">
			    <input type="hidden" value="${param.contractId}" id="contractId" name="contractId"/>
				<table class="table table-vzebra">
				  <tbody>
				    <tr>
				      <td style="width: 50px;">客户名称：</td>
				      <td style="width: 35%;"><span name='contract.CUSTOMER_NAME'></span></td>
				      <td style="width: 50px;">合同金额：</td>
				      <td style="width: 35%;"><span name='contract.AMOUNT'></span></td>
				    </tr>
				    <tr>
				      <td>合同年限：</td>
				      <td><span name='contract.YEAR'></span></td>
				      <td>签订时间：</td>
				      <td><span name='contract.SIGN_DATE'></span></td>
				    </tr>
				    <tr>
				      <td>创建时间：</td>
				      <td><span name='contract.CREATE_TIME'></span></td>
				      <td>签约人：</td>
				      <td><span name='contract.SALES_BY' data-fn='getValText'></span></td>
				    </tr>
				    <tr>
				      <td>产品线：</td>
				      <td><span name='contract.PROD_LINE' data-fn='getValText'></span></td>
				      <td>合同类型：</td>
				      <td><span name='contract.CONTRACT_TYPE' data-fn='getValText'></span></td>
				    </tr>
				    <tr>
				      <td>产品经理：</td>
				      <td><span name='contract.PM_BY' data-fn='getValText'></span></td>
				      <td>所签办事处：</td>
				      <td><span name='contract.SIGN_OFFICE' data-fn='getValText'></span></td>
				    </tr>
				    <tr>
				      <td>合同描述：</td>
				      <td colspan="3"><span name='contract.CONTRAC_DESC'></span></td>
				    </tr>
				  </tbody>
			   </table>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/pages/crm/contract/static/mgr.js"></script>
	<script type="text/javascript">
   
		$(function(){
			$("#ContractDetailForm").render({success:function(result){
				
			}});  
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>