<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>合同评审</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">评审编号</span>	
							 <input type="text" name="applyNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 180px">
							 <span class="input-group-addon">合同名称</span>	
							 <input type="text" name="contractName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">签约单位</span>	
							 <select class="form-control input-sm" onchange="list.query();" name="signEnt">
		                    	<option value="">请选择</option>
		                    	<option value="yunqu">云趣</option>
		                    	<option value="zhongrong">中融</option>
		                    	<option value="pci">佳都</option>
		                     </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
		          	     <div class="pull-right">
				  			<EasyTag:res resId="CONTRACT_FLOW_SETTING">
					  			<button class="btn btn-sm btn-default ml-10" type="button" onclick="list.setting()"> 评审部门</button>
				  			</EasyTag:res>
		          	     </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
			 <script type="text/html" id="btnBar">
				<EasyTag:hasRole roleId="CONTRACT_REVIEW_MGR">
			 		<button class="btn btn-sm btn-info" type="button" lay-event="list.flowApply">发起流程</button>
			 		<button class="btn btn-sm btn-info ml-10" type="button" lay-event="list.flowEdit">修改</button>
				</EasyTag:hasRole>
 		    </script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ReviewDao.list',
					height:'full-90',
					limit:20,
					cellMinWidth:80,
					rowEvent:'rowEvent',
					toolbar:'#btnBar',
					id:'list',
					cols: [[
					  {title:'',type:'radio'},
					  {title:'序号',type:'numbers'},
					  {
					    field: 'APPLY_NO',
						title: '评审编号',
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail',
						width:100
					 },
					 {
					    field: 'CONTRACT_NAME',
						title: '合同名称',
						align:'left',
						minWidth:280
					},{
						field:'CONTRACT_TYPE',
						title:'合同类型',
						width:100
					},{
						field:'CUST_NAME',
						title:'客户名称',
						minWidth:150
					},{
						field:'REVIEW_STATE',
						title:'评审状态',
						width:100,
						templet:function(row){
							var val = row['APPLY_STATE'];
							return flowApplyState(val);
						}
					},{
						field:'PROD_LINE',
						title:'产品线',
						width:120
					},{
						field:'SIGN_DEPT_NAME',
						title:'营销大区',
						width:100
					},{
					    field: 'SALES_BY_NAME',
						title: '销售经理',
						align:'left',
						width:90
					},{
					    field: 'APPLY_TIME',
						title: '发起时间',
						width:120,
						align:'center'
					},{
					    field: 'APPLY_NAME',
						title: '发起人',
						width:90,
						align:'center'
					}
				   ]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(row){
				var data = {businessId:row['APPLY_ID']};
				popup.openTab({id:'reviewDetail',title:'评审详情',url:'${ctxPath}/web/flow',data:data});
			},
			setting:function(){
				popup.openTab({id:'reviewNode',url:'${ctxPath}/pages/crm/contract/review/review-node.jsp',title:'合同评审部门'});
			}
		}
		
		list.flowEdit = function(dataList){
			var data = dataList;
			if(isArray(dataList)){
			    if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    data = dataList[0];
			}
		    var applyState = data['APPLY_STATE'];
			if(applyState=='0'||applyState=='10'||applyState=='20'||applyState=='21'){
				var flowCode = data['FLOW_CODE'];
				var json  = $.extend({handle:'edit',mode:'edit'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE']});
				popup.openTab({id:'flowDetail',title:'修改流程',url:'${ctxPath}/web/flow',data:json});
			}else{
				layer.msg('草稿或没审批过的申请才能修改',{icon:2,offset:'20px',time:1000});
			}
		}
		
		list.flowApply = function(){
			popup.openTab({id:'contract_review',url:'/yq-work/web/flow?flowCode=contract_review',title:'合同评审',data:{handle:'add'}});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>