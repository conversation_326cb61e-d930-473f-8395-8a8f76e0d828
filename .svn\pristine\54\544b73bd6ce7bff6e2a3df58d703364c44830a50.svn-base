package com.yunqu.work.dao.ehr;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name="KqStatDao")
public class KqStatDao extends AppDaoContext {

	@WebControl(name="userStatRecord",type=Types.RECORD)
	public JSONObject userStatRecord(){
		EasySQL sql = getEasySQL("select USER_NAME,STAFF_ID,IFNULL(SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) <> 0 THEN 1 ELSE 0 END), 0) AS WORK_DAY,IFNULL(SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) = 0 AND REMARK != '公出' THEN 1 ELSE 0 END), 0) AS NOT_WORK,IFNULL(ROUND(SUM(LATE_TIME/60),1), 0) AS LATE_TIME, IFNULL(SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END), 0) AS LATE_TIME_COUNT, IFNULL(ROUND(SUM(OVER_TIME/3600),1), 0) AS OVER_TIME,IFNULL(SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END), 0) AS OVER_TIME_COUNT from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append(getUserId(),"and USER_ID = ?");
		String yearBegin =  EasyCalendar.newInstance().getYear()+"0101";
		String yearEnd =  EasyCalendar.newInstance().getYear()+"1231";
		sql.append(yearBegin,"and DK_DATE >= ?");
		sql.append(yearEnd,"and DK_DATE <= ?");
		return queryForRecord(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="lateTimeRank",type=Types.LIST)
	public JSONObject lateTimeRank(){
		EasySQL sql = getEasySQL("select dept_name DEPT,USER_NAME,STAFF_ID,SUM(LATE_TIME/60) LATE_TIME, SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) AS LATE_TIME_COUNT, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) <> 0 THEN 1 ELSE 0 END) AS work_day from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append("GROUP BY USER_ID");
		setOrderBy(sql, " order by LATE_TIME_COUNT desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}


	@WebControl(name="lateRatioRank",type=Types.LIST)
	public JSONObject lateRatioRank(){
		EasySQL sql = getEasySQL("select dept_name DEPT,USER_NAME,STAFF_ID,SUM(LATE_TIME)/60 / SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) AS AVG_LATE_TIME,SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) / SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) <> 0 THEN 1 ELSE 0 END) AS LATE_RATIO from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append("GROUP BY USER_ID");
		setOrderBy(sql, " order by LATE_RATIO desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="multiLateTimeRank",type=Types.LIST)
	public JSONObject multiLateTimeRank(){
		EasySQL sql = getEasySQL("select dept_name DEPT,USER_NAME,STAFF_ID,SUM(LATE_TIME/60) LATE_TIME, SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) AS LATE_TIME_COUNT, SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) / SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) <> 0 THEN 1 ELSE 0 END) AS LATE_RATIO,SUM(LATE_TIME)/60 / SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) AS AVG_LATE_TIME from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append("GROUP BY USER_ID");
		setOrderBy(sql, " order by LATE_TIME_COUNT desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="lateTimeRankByDept",type=Types.LIST)
	public JSONObject lateTimeRankByDept(){
		EasySQL sql = getEasySQL("select dept_name DEPT,DEPT_ID,ROUND((SUM(LATE_TIME)/60) /count(DISTINCT staff_id),1) as AVG_LATE_TIME, SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) /count(DISTINCT staff_id) as AVG_LATE_TIME_COUNT from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append("GROUP BY DEPT_ID");
		setOrderBy(sql, " order by AVG_LATE_TIME_COUNT desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="overTimeRank",type=Types.LIST)
	public JSONObject overTimeRank(){
		EasySQL sql = getEasySQL("select dept_name DEPT,USER_NAME,STAFF_ID,SUM(OVER_TIME/3600) AS OVER_TIME,SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) AS OVER_TIME_COUNT from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append("GROUP BY USER_ID");
		setOrderBy(sql, " order by OVER_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="overRatioRank",type=Types.LIST)
	public JSONObject overRatioRank(){
		EasySQL sql = getEasySQL("select dept_name DEPT,USER_NAME,STAFF_ID,SUM(OVER_TIME)/60 / SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) AS AVG_OVER_TIME,SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) / SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) <> 0 THEN 1 ELSE 0 END) AS OVER_RATIO from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append("GROUP BY USER_ID");
		setOrderBy(sql, " order by OVER_RATIO desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="multiOverTimeRank",type=Types.LIST)
	public JSONObject multiOverTimeRank(){
		EasySQL sql = getEasySQL("select dept_name DEPT,USER_NAME,STAFF_ID,SUM(OVER_TIME/3600) AS OVER_TIME,SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) AS OVER_TIME_COUNT,SUM(OVER_TIME)/60 / SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) AS AVG_OVER_TIME,SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) / SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) <> 0 THEN 1 ELSE 0 END) AS OVER_RATIO from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append("GROUP BY USER_ID");
		setOrderBy(sql, " order by OVER_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="overTimeRankByDept",type=Types.LIST)
	public JSONObject overTimeRankByDept(){
		EasySQL sql = getEasySQL("select dept_name DEPT,DEPT_ID,(SUM(OVER_TIME)/3600)/count(DISTINCT staff_id)as AVG_OVER_TIME,SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END)/count(DISTINCT(staff_id)) AS AVG_OVER_TIME_COUNT from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append("GROUP BY DEPT_ID");
		setOrderBy(sql, " order by AVG_OVER_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="overTimeStatByMonth",type=Types.RECORD)
	public JSONObject overTimeStatByMonth(){
		String beginDate = param.getString("beginDate");
		String endDate = param.getString("endDate");
		String year = EasyCalendar.newInstance().getYear()+"";
		if(!StringUtils.isAllBlank(beginDate,endDate)){
			year = StringUtils.isBlank(beginDate)?endDate.substring(0,4):beginDate.substring(0,4);
		}
		String[] months = {"01","02","03","04","05","06","07","08","09","10","11","12"};
		EasySQL sql = new EasySQL("select "+year+" as year");
        for (String month : months) {
            String monthId = year + month;
            String monthName = year +'_'+ month;
			sql.append(monthId, ",ROUND(SUM(CASE WHEN MONTH_ID = ? THEN OVER_TIME ELSE 0 END)/3600,2) AS OVER_TIME_"+monthName);
			sql.append(monthId, ",SUM(CASE WHEN MONTH_ID = ? AND OVER_TIME<>'' THEN 1 ELSE 0 END) AS OVER_TIME_COUNT_"+monthName);
        }
		sql.append("from yq_kq_obj where 1=1");
		return queryForRecord(sql.getSQL(), sql.getParams());
	}

	
	@WebControl(name="deptReport",type=Types.LIST)
	public JSONObject deptReport(){
		EasySQL sql = getEasySQL("select dept_name dept,dept_Id,count(DISTINCT(staff_id)) staff_count,SUM(LATE_TIME/60) LATE_TIME, SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) AS LATE_TIME_COUNT, SUM(OVER_TIME/60) OVER_TIME, SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) AS OVER_TIME_COUNT, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) = 5 THEN 1 ELSE 0 END) AS lose_dk, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) = 0 THEN 1 ELSE 0 END) AS not_work, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) <> 0 THEN 1 ELSE 0 END) AS work_day from yq_kq_obj where 1=1");
		String beginDate = param.getString("beginDate");
		String endDate = param.getString("endDate");
		if(StringUtils.notBlank(beginDate)) {
			sql.append(beginDate.replaceAll("-",""),"and DK_DATE >= ?");
		}
		if(StringUtils.notBlank(endDate)) {
			sql.append(endDate.replaceAll("-",""),"and DK_DATE <= ?");
		}
		sql.append("GROUP BY dept");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql = getEasySQL("select * from yq_kq_obj where 1=1");
		this.setCondition(sql);
		sql.append(" order by DK_DATE desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	private void setCondition(EasySQL sql) {
		String beginDate = param.getString("beginDate");
		String endDate = param.getString("endDate");
		if(StringUtils.notBlank(beginDate)) {
			sql.append(beginDate.replaceAll("-",""),"and DK_DATE >= ?");
		}
		if(StringUtils.notBlank(endDate)) {
			sql.append(endDate.replaceAll("-",""),"and DK_DATE <= ?");
		}
	}

	private void setOrderBy(EasySQL sql, String defaultOrder) {
		String sortName = param.getString("sortName");
		String sortType = param.getString("sortType");
		if (StringUtils.notBlank(sortName)) {
			sql.append("order by ").append(sortName).append(sortType);
		} else {
			sql.append(defaultOrder);
		}
	}
}
