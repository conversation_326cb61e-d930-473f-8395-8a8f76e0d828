<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>配置</title>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editConfForm" autocomplete="off" data-mars="BxDao.contactUnitInfo" data-mars-prefix="conf.">
	     	    <input type="hidden" id="nodeId" value="${param.id}" name="conf.ID"/>
	     	    <input type="hidden" value="${param.id}" name="id"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px;">公司名</td>
		                    <td>
		                    	<input name="conf.UNIT_NAME" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
		                <c:if test="${!empty param.id}">
			              <tr>
		                    <td>编码</td>
		                    <td>
		                    	<input name="conf.UNIT_NO" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                 </tr>
		                </c:if>
			            <tr>
		                    <td>银行名</td>
		                    <td>
		                    	<input name="conf.BANK_NAME" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
			            <tr>
		                    <td>银行账号</td>
		                    <td>
		                    	<input name="conf.BANK_ACOUNT" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
			        </tbody>
				 </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="ContactUnit.ajaxSubmitForm()"> 确 定  </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
	
        var ContactUnit={id:'${param.id}'};
        
        ContactUnit.ajaxSubmitForm = function(){
			if(form.validate("#editConfForm")){
				if(ContactUnit.id==''){
					ContactUnit.updateData('add'); 
				}else{
					ContactUnit.updateData('update'); 
				}
			};
		}
        ContactUnit.updateData = function(flag) {
			var data = form.getJSONObject("#editConfForm");
			data['action'] = flag;
			data['conf.BANK_ACOUNT'] = data['conf.BANK_ACOUNT'].replace(/\s+/g,'');
			ajax.remoteCall("${ctxPath}/servlet/bx/conf?action=contactUnit",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadContactUnit();
						popup.layerClose("#editConfForm");
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		$(function(){
			$("#editConfForm").render({success:function(result){

			}});
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>