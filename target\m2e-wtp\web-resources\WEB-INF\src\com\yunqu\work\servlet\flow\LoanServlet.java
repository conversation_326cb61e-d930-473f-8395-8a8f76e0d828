package com.yunqu.work.servlet.flow;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.ApproveResultRecord;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.BxService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.utils.NumberToCN;

@WebServlet("/servlet/flow/loan/*")
public class LoanServlet extends BaseFlowServlet {
	private static final long serialVersionUID = 1L;
	
	public void actionForIndex() {
		renderHtml("404");
	}
	
	public EasyResult actionForSubmitApply() {
		JSONObject params = getJSONObject();
		JSONObject business = JsonKit.getJSONObject(params, "business");
		
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String businessId = params.getString("businessId");
		String deptId = business.getString("BX_DEPT_ID");
		if(StringUtils.isBlank(businessId)) {
			businessId = FlowService.getService().getID();
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		String[] userInfo =  BxService.getService().getApproverUser(deptId);
		if(userInfo==null) {
			return EasyResult.fail("请配置审批人");
		}
		
		String doAction = params.getString("doAction");
		EasyRecord applyRecord = getApplyRecord(flowCode);
		applyRecord.set("apply_state","submit".equals(doAction)?10:0);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.set("apply_id",businessId);
		applyRecord.setColumns(applyInfo);
		applyRecord.set("apply_no","submit".equals(doAction)?getBxLoanNo(flowCode):"");
		
		boolean bl = true;
		EasyRecord record = null;
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
			String payMoney = record.getString("PAY_MONEY");
			if(StringUtils.notBlank(payMoney)) {
				record.set("HZ_MONEY",payMoney );
			}
		}
		try {
			updateItem(params,businessId,false);
			if(record!=null) {
				record.remove("ZM_MONEY");
				record.remove("ZT_MONEY");
				bl = this.getQuery().save(record);
			}
			if(bl) {
				this.getQuery().save(applyRecord);
			}
			this.saveFile(businessId,params);
			this.updateBxRecord(businessId);
			this.saveContactUnit(record);
			if("submit".equals(doAction)) {
				String result = this.startFlow(flow,businessId,deptId,applyState);
				if(result==null) {
					this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
					return EasyResult.fail("流程节点未配置,请联系管理员");
				}
				return EasyResult.ok(businessId,"提交到审批节点:"+result);
			}
			return EasyResult.ok(businessId);
		} catch (Exception e) {
			this.error(null, e);
			this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
			return EasyResult.fail("流程异常,请联系管理员"+e.getMessage());
		}
	}
	
	public EasyResult actionForUpdateApply() {
		JSONObject params = getJSONObject();
		JSONObject business = JsonKit.getJSONObject(params, "business");
		
		String businessId = params.getString("businessId");
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String doAction = params.getString("doAction");
		String deptId = business.getString("BX_DEPT_ID");
		
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		String[] userInfo =  BxService.getService().getApproverUser(deptId);
		if(userInfo==null) {
			return EasyResult.fail("请配置审批人");
		}
		
		boolean bl = true;
		EasyRecord record = null;
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
			String payMoney = record.getString("PAY_MONEY");
			if(StringUtils.notBlank(payMoney)) {
				record.set("HZ_MONEY",payMoney );
			}
		}
		
		EasyRecord applyRecord = null;
		if("submit".equals(doAction)) {
			applyRecord =  getApplyRecord(businessId,flowCode);
		}else {
			applyRecord = getApplyRecord();
		}
		applyRecord.setPrimaryValues(businessId);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.setColumns(applyInfo);
		applyRecord.set("apply_id",businessId);
		if("submit".equals(doAction)) {
			applyRecord.set("apply_state",10);
			if(applyState!=FlowConstants.FLOW_STAT_CHECK_RETURN) {
				applyRecord.set("apply_no",getBxLoanNo(flowCode));
			}
		}

		try {
			updateItem(params,businessId,true);
			if(record!=null) {
				bl = this.getQuery().update(record);
			}
			if(bl) {
				this.getQuery().update(applyRecord);
			}
			this.updateBxRecord(businessId);
			this.saveContactUnit(record);
			if("submit".equals(doAction)) {
				this.startFlow(flow,businessId,deptId,applyState);
			}
		} catch (Exception e) {
			this.error(null, e);
			return EasyResult.fail("流程异常,请联系管理员"+e.getMessage());
		}
		return EasyResult.ok();
		
	}
	
	private String startFlow(FlowModel flow,String businessId,String deptId,int applyState) throws SQLException {
		List<ApproveNodeModel> approvers = BxService.getService().getApproversByUser(getUserId(),deptId);
		ApproveNodeModel nodeInfo = approvers.get(0);
		
		String todoCheckUserId = nodeInfo.getCheckBy();
		if(todoCheckUserId.indexOf(getUserId())>-1) {
			 nodeInfo = ApproveService.getService().getNode(nodeInfo.getNextNodeId());
		}
		
		String nextResultId = RandomKit.uniqueStr();
		
		EasyRecord resultRecord = getApproveResultRecord(businessId);
		if(applyState==21) {
			resultRecord.set("node_name","修改申请");
		}else {
			resultRecord.set("node_name","申请填写");
		}
		this.getQuery().save(resultRecord);
		String nowResultId = resultRecord.getPrimaryValue().toString();
		
		ApproveResultRecord nextResult = getApproveResultRecord(nodeInfo,businessId,nextResultId,nowResultId);
		this.saveApproveNode(nextResult,flow.getFlowCode());
//		this.getQuery().save(nextResult);
		
		FlowService.getService().updateApplyBegin(businessId, nodeInfo.getNodeId(),nowResultId,nextResultId);
		
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		//微信推送给审批人
		MessageModel msgModel  = getMessageModel(apply,nodeInfo.getCheckBy(), null,null);
		this.sendAllMsg(msgModel);
		
		return nodeInfo.getNodeName();
	}
	
	/**
	 * 执行审批
	 * @return
	 */
	public EasyResult actionForDoApprove() {
		try {
			String msg = "操作成功";
			JSONObject params = getJSONObject();
			String flowCode = params.getString("flowCode");
			String businessId = params.getString("businessId");
			String resultId = params.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("审批人不存在");
			}
			
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			
			
			//获取下一级节点
			ApproveNodeModel approveNode = BxService.getService().getNodeByResultId(resultId);
			if(approveNode==null) {
				return EasyResult.fail("请联系管理员配置审批人员");
			}
			String nodeId = approveNode.getNodeId();
			String nextNodeId =approveNode.getNextNodeId();
			
			
			FlowModel flow = ApproveService.getService().getFlow(flowCode);
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);
			String deptId = this.getQuery().queryForString("select bx_dept_id from yq_flow_loan_base where business_id = ?", businessId);
			

			int checkResult = params.getIntValue("checkResult");//1 通过 2拒绝
			String checkDesc = params.getString("checkDesc");
			EasyRecord record = getApproveResultRecord(resultId,checkResult,checkDesc);
			this.getQuery().update(record);
			
			
			MessageModel msgModel = new MessageModel();
			msgModel.setTypeName(flow.getFlowCode());
			msgModel.setSender(getUserId());
			msgModel.setSendName(getUserName());
			msgModel.setFkId(businessId);
			msgModel.setMsgLabel("待审批");
			msgModel.setTitle(apply.getApplyTitle());
			msgModel.setData1(flow.getFlowName());
			msgModel.setData2(apply.getApplyName());
			msgModel.setData3(apply.getApplyTime());
			msgModel.setData4("待审批");
			msgModel.setDesc(apply.getApplyRemark());
			
			if(checkResult==2) {
				this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ?,last_result_time = ? where apply_id = ?",21,EasyDate.getCurrentDateString(),businessId);
				msgModel.setData4("审批退回");
				msgModel.setMsgLabel("审批退回");
				msgModel.setDesc("审批退回："+checkDesc);
				msgModel.setReceiver(apply.getApplyBy());
			}else {
				if("0".equals(nextNodeId)) {
					msg = "审批完成,流程结束";
					msgModel.setMsgLabel("审批完成");
					msgModel.setReceiver(apply.getApplyBy());
					FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
					this.checkEnd(businessId);
				}else {
					msgModel.setMsgLabel("请尽快审批");
					
					ApproveNodeModel nextNodeInfo = BxService.getService().getNodeInfo(nextNodeId,deptId,params);
					
					String todoCheckUserId = nextNodeInfo.getCheckBy();
					if(todoCheckUserId.indexOf(apply.getApplyBy())>-1) {
						nextNodeId = nextNodeInfo.getNextNodeId();
						nextNodeInfo = BxService.getService().getNodeInfo(nextNodeId,deptId,params);
					}
					msgModel.setMsgLabel(nextNodeInfo.getNodeName());
					msgModel.setReceiver(nextNodeInfo.getCheckBy());
					
					String nextResultId = RandomKit.uniqueStr();
					ApproveResultRecord resultRecord = getApproveResultRecord(nextNodeInfo, businessId, nextResultId,resultId);
					this.saveApproveNode(resultRecord,apply.getFlowCode());
//					this.getQuery().save(resultRecord);
					FlowService.getService().updateApplyInfo(businessId, resultId,nextResultId,nodeId,nextNodeId, 20);
					msg = "提交成功,审批下一个节点:"+nextNodeInfo.getNodeName();
				}
			}
			this.sendAllMsg(msgModel);
			return EasyResult.ok(businessId,msg);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	
	public void updateBxRecord(String businessId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("update yq_flow_loan_base t1 set ");
			sql.append("t1.pay_money = ( select sum(amount) from yq_flow_loan_item t2 where t2.business_id = t1.business_id )");
			sql.append(businessId,"where t1.business_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(), sql.getParams());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
	}
	
	//保存往来单位
	public void saveContactUnit(EasyRecord bxBase) {
		String payType = bxBase.getString("PAY_TYPE");
		if("对公付款".equals(payType)) {
			String unitNo = bxBase.getString("CONTACT_CODE");
			String bankAcount = bxBase.getString("CONTACT_BANK_NO");
			try {
				boolean result = this.getQuery().queryForExist("select count(1) from yq_finance_contact_unit where unit_no = ? and bank_acount = ?",unitNo,bankAcount);
				if(!result) {
					EasyRecord record = new EasyRecord("yq_finance_contact_unit","unit_no","bank_acount");
					record.set("unit_no",unitNo);
					record.set("unit_name",bxBase.getString("CONTACT_UNIT"));
					record.set("bank_acount",bankAcount);
					record.set("bank_name",bxBase.getString("CONTACT_BANK"));
					record.set("create_time",EasyDate.getCurrentDateString());
					record.set("creator",getUserId());
					record.set("create_name",getUserName());
					this.getQuery().save(record);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
	}
	
	
	private void updateItem(JSONObject params,String bussinessId,boolean isUpdate) throws SQLException {
		Object itemIndexObj = params.get("itemIndex");
		JSONArray itemIndexs = null;
		if(itemIndexObj instanceof String) {
			itemIndexs = new JSONArray();
			itemIndexs.add(itemIndexObj.toString());
		}else {
			itemIndexs = params.getJSONArray("itemIndex");
		}
			for(int i= 0;i<itemIndexs.size();i++) {
				EasyRecord itemModel = new EasyRecord("yq_flow_loan_item","ITEM_ID");
				String item = itemIndexs.getString(i);
				itemModel.set("BUSINESS_ID", bussinessId);
				String bxDate = params.getString("bxDate_"+item);
				itemModel.set("BX_DATE", bxDate);
				itemModel.set("INVOICE_TYPE", params.getString("invoiceType_"+item));
				itemModel.set("FEE_TYPE", params.getString("feeType_"+item));
				itemModel.set("FEE_CODE", params.getString("feeCode_"+item));
				itemModel.set("SUBJECT_NO", params.getString("subjectNo_"+item));
				itemModel.set("SUBJECT_NAME", params.getString("subjectName_"+item));
				itemModel.set("FEE_DESC", params.getString("feeDesc_"+item));
				itemModel.set("AMOUNT", params.getString("amount_"+item));//含税金额
				itemModel.set("HZ_AMOUNT", params.getString("amount_"+item));//核准金额
				itemModel.set("ITEM_INDEX", params.getString("orderIndex_"+item));
				itemModel.set("DEPT_ID", params.getString("deptId_"+item));
				itemModel.set("DEPT_NAME", params.getString("deptName_"+item));
				if(item.length()>20) {
					itemModel.set("ITEM_ID", item);
					this.getQuery().update(itemModel);
				}else {
					itemModel.setPrimaryValues(RandomKit.uniqueStr());
					this.getQuery().save(itemModel);
				}
		}
	}
	
	private void checkEnd(String businessId) throws SQLException {
		this.getQuery().executeUpdate("update yq_flow_loan_base set pay_state = ? where business_id  = ?", 1,businessId);
	}
	
	public EasyResult actionForDelItem() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		try {
			this.getQuery().executeUpdate("delete from yq_flow_loan_item where item_id = ?",itemId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateHzMoney() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		BigDecimal hzAmount = params.getBigDecimal("hzAmount");
		String businessId = params.getString("businessId");
		try {
			
			this.getQuery().executeUpdate("update yq_flow_loan_item set hz_amount = ? where item_id = ?",hzAmount,itemId);
			this.getQuery().executeUpdate("update yq_flow_loan_base t1 set t1.hz_money = (select sum(t2.hz_amount) from yq_flow_loan_item t2 where t1.business_id = t2.business_id) where t1.business_id = ?",businessId);
			String num = this.getQuery().queryForString("select t1.hz_money from yq_flow_loan_base t1 where t1.business_id = ?", businessId);
			
			String payMoneyDx = NumberToCN.number2CNMontrayUnit(new BigDecimal(num));
			this.getQuery().executeUpdate("update yq_flow_loan_base t1 set t1.pay_money = ?,t1.pay_money_dx = ? where t1.business_id = ?",num,payMoneyDx,businessId);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelApply() {
		JSONObject params = getJSONObject();
		String businessId = params.getString("businessId");
		try {
			this.getQuery().executeUpdate("delete from yq_flow_loan_item where business_id = ?",businessId);
			this.getQuery().executeUpdate("delete from yq_flow_loan_base where business_id = ?",businessId);
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id = ?",businessId);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id = ?",businessId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public void actionForExportData() {
		renderHtml("todo");
	}
	

}
