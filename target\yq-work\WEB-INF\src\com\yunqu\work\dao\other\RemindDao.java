package com.yunqu.work.dao.other;

import java.sql.SQLException;
import java.util.Map;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.RemindModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="RemindDao")
public class RemindDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		RemindModel model=new RemindModel();
		model.setPrimaryValues(param.getString("remindId"));
		model.setColumns(getParam("remind"));
		try {
			if(StringUtils.notBlank(param.getString("remindId"))){
				Map<String, String> jsonObject = this.getQuery().findById(model,"CREATOR");
				String creator=jsonObject.get("CREATOR");
				if(!getUserId().equals(creator)){
					LookLogService.getService().addLog(getUserPrincipal().getUserId(),param.getString("remindId"),"remind",request);
					this.getQuery().executeUpdate("update yq_remind t1 set t1.view_count = (select count(1) from yq_look_log t2 where t2.fk_id = t1.remind_id) where t1.remind_id = ?", param.getString("remindId"));
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return queryForRecord(model);
	}
	
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select remind_id,title from YQ_REMIND");
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from YQ_REMIND where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		String type = param.getString("type");
		if("0".equals(type)){
			sql.append(getUserId(),"and creator = ?");
		}
		sql.append(type,"and remind_type = ?");
		sql.append(" order by publish_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="wxList",type=Types.LIST)
	public JSONObject wxList(){
		EasySQL sql=getEasySQL("select * from YQ_REMIND where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append("and status = 0");
		sql.append("and remind_type in(1,2)");
		sql.append(" order by publish_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="mylist",type=Types.LIST)
	public JSONObject mylist(){
		EasySQL sql=getEasySQL("select * from YQ_REMIND where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append(param.getString("type"),"and remind_type = ?");
		sql.append(0,"and status = ?");
		sql.append(" order by publish_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
