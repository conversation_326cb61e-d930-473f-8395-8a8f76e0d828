<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购付款申请</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
				<input type="hidden" value="${param.orderIds}" name="orderIds"/>
   		   		<input type="hidden" value="${param.orderNos}" name="orderNos"/>
   		   		<input type="hidden" value="${param.supplierId}" name="supplierId"/>
   		   		<input type="hidden" value="${param.planIds}" name="planIds"/>
			   <div class="layui-row ibox-content" data-mars="SupplierDao.record" data-mars-prefix="supplier.">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}采购付款申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
			            	<td style="width: 80px;" class="required">付款编号</td>
			            	<td>
			            		<input type="text" data-rules="required" data-mars="OrderDao.getPaymentNo" name="paymentNo" class="form-control"/>
			            	</td>
			            	<td style="width: 80px;" class="required">评审标题</td>
			            	<td>
			            		<input type="text" data-rules="required" data-mars="OrderFlowDao.getApplyName" name="applyName" class="form-control"/>
			            	</td>
			            </tr>
			            <tr>
			            	<td class="required">计划付款日期</td>
			            	<td>
			            		<input type="text" data-rules="required"  data-laydate="{type:'date'}" name="planPayDate" class="form-control"/>
			            	</td>
			            	<td class="required">付款合计金额</td>
			            	<td>
			            		<input type="number" data-rules="required" name="amount" class="form-control"/>
			            	</td>
			            </tr>
			            <tr>
			            	<td class="required">供应商</td>
			            	<td>
			            		<input type="text" data-rules="required" readonly="readonly" name="supplier.NAME" class="form-control"/>
			            	</td>
			            	<td class="required">采购组织</td>
			            	<td>
			            		<select data-rules="required" name="supplier.ORG" class="form-control">
			            			<option value="云趣">云趣</option>
			            			<option value="中融">中融</option>
			            		</select>
			            	</td>
			            </tr>
			            
			            
			            <tr>
			            	<td class="required">收款银行</td>
			            	<td>
			            		<input type="text" data-rules="required" name="supplier.PAY_BANK_NAME" class="form-control"/>
			            	</td>
			            	<td class="required">收款账号</td>
			            	<td>
			            		<input type="text" data-rules="required" name="supplier.PAY_BANK_ACCOUNT" class="form-control"/>
			            	</td>
			            </tr>
			            <tr>
			            	<td>申请说明</td>
			            	<td colspan="7">
			            		<textarea style="height: 80px" name="apply.applyRemark" class="form-control"></textarea>
			            	</td>
			            </tr>
					  </table>
					   <table class="layui-table" id="orders"></table>
 				 	   <button onclick="calcAmount()" type="button" class="btn btn-xs btn-default" style="position: relative;float:left;">计算</button>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("PaymentEdit");

		var Flow = {};
		
		$(function(){
			var supplierId = $("[name='supplierId']").val();
			if(supplierId==''){
				alert('供应商id是空的');
				popup.closeTab();
			}
			FlowCore.initPage({success:function(){
				PaymentEdit.initOrders();
			}});
		});

		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			var data = form.getJSONObject("#flowForm");
			data['paymentState'] = '10';
			
			var checkStatus= table.checkStatus('orders')
	 		var orderJson = checkStatus.data;
	 		if(orderJson.length==0){
	 			return;
	 		}
	 		var flag = true;
	 		var _orderJson = {};
	 		var orderJsonArray = [];
	 		for(var index in orderJson){
	 			var row  = orderJson[index];
	 			var applyAmount = row['APPLY_AMOUNT'];
	 			if(applyAmount==''||applyAmount==undefined){
	 				flag = false;
	 			}
	 			_orderJson[row['ORDER_ID']] = {PLAN_ID:row['PLAN_ID'],APPLY_REMARK:row['APPLY_REMARK'],ORDER_ID:row['ORDER_ID'],HAS_APPLY_AMOUNT:row['HAS_APPLY_AMOUNT']||'0',HAS_PAY_AMOUNT:row['HAS_PAY_AMOUNT']||'0',MAKE_AMOUNT:row['MAKE_AMOUNT']||'0',APPLY_AMOUNT:applyAmount||'0'};
	 			orderJsonArray.push(_orderJson[row['ORDER_ID']]);
	 		}
	 		if(!flag){
	 			layer.msg('请填写订单本次结算金额');
	 			return;
	 		}
			data['orderJson'] = _orderJson;
			data['orderJsonArray'] = orderJsonArray;
			
			FlowCore.insertData({reqUrl:'/yq-work/servlet/order/flow?action=paymentApply',data:data});
			
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		PaymentEdit.initOrders = function(){
			$("#flowForm").initTable({
				mars:'OrderDao.orderPayPlanList',
				id:'orders',
				page:false,
				cellMinWidth:100,
				edit:'inputPrice',
				cols: [[
				 {type:'numbers'},
				 {type:'checkbox',hide:true},
				 {
					 field:'ORDER_NO',
					 title:'订单号',
					 width:90
				 },
				 {
				    field: 'CONTRACT_NAME',
					title: '合同名称',
					align:'left',
					minWidth:160
				 },
	             {
					 field:'TOTAL_PRICE',
					 title:'订单金额',
					 align:'center',
					 width:90
				 },
	             {
					 field:'APPLY_AMOUNT',
					 title:'本次结算金额(输入)',
					 align:'center',
					 edit:'text',
					 width:160
				 },{
					 field:'RK_AMOUNT',
					 title:'已入库金额'
				 },{
					 field:'HAS_APPLY_AMOUNT',
					 title:'已申请金额'
				 },{
				    field: 'MAKE_AMOUNT',
					title: '已开票金额',
					align:'center'
				},{
					field:'HAS_PAY_AMOUNT',
					title:'已付款金额'
				},{
					field:'APPLY_REMARK',
					title:'备注(输入)',
				    edit:'text',
				    minWidth:200
				}
			]],done:function(data){
				calcAmount();
			}
		  });
		}
		
		function inputPrice(obj){
			var field = obj.field;
			var value = obj.value;
			if(field=='APPLY_AMOUNT'){
				var val = 0;
				//$("#flowForm [name='amount']").val(value);
				$(".layui-table-body [data-field='APPLY_AMOUNT']").each(function(){
					var t = $(this);
					val = numAdd(val,t.text());
				});
				val = numAdd(val,value);
				$("#flowForm [name='amount']").val(val);
				
			}
			if(field=='APPLY_REMARK'){
				//$("#flowForm [name='remark']").val(value);
			}
		}
		function calcAmount(){
			var val = 0;
			$(".layui-table-body [data-field='APPLY_AMOUNT']").each(function(){
				var t = $(this);
				val = numAdd(val,t.text());
			});
			$("#flowForm [name='amount']").val(val);
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>