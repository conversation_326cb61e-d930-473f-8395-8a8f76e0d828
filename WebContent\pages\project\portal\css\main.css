:root {
  --color-state-04: rgba(177, 184, 191, 0.25);
  --color-other-7: rgba(177, 184, 191, 0.25);
  --color-text1-8: #404040;
  --color-tag-disable: #68bbf2;
  --color-other-16: #68bbf2;
  --color-tag-hover: #003e7c;
  --color-other-15: #003e7c;
  --color-tag-bg: #c5e8ff;
  --color-other-14: #c5e8ff;
  --color-tag-al: #0055ab;
  --color-other-13: #0055ab;
  --color-fill1-10: #f7f7f7;
  --color-link-3: #0055ab;
  --color-link-2: #0488de;
  --color-link-1: #006ad4;
  --color-fill1-2: #f2f5f7;
  --color-priority-3: #c6cdd4;
  --color-other-12: #c6cdd4;
  --color-priority-2: #0488de;
  --color-other-11: #0488de;
  --color-priority-2-n: #ffdf40;
  --color-other-10: #ffdf40;
  --color-priority-1: #ff9d28;
  --color-other-9: #ff9d28;
  --color-priority-0: #e95648;
  --color-other-8: #e95648;
  --color-state-02: #5fce9d;
  --color-other-5: #5fce9d;
  --color-state-03: #ffa428;
  --color-other-6: #ffa428;
  --color-data1-6: #ffa428;
  --color-state-01: #2a9feb;
  --color-other-4: #2a9feb;
  --color-notice-6: #0055ab;
  --color-brand2-1: #ddf1ff;
  --color-brand2-2: #c5e8ff;
  --color-notice-7: #2a9feb;
  --color-warning-7: #f29849;
  --color-error-7: #f36e62;
  --color-success-7: #5bce85;
  --color-brand1-7: #0055ab;
  --color-brand1-8: #003e7c;
  --color-fill1-7: #7d8389;
  --color-notice-5: #006ad4;
  --color-warning-5: #e28200;
  --color-warning-6: #a96500;
  --color-error-5: #cd3021;
  --color-error-6: #a91002;
  --color-success-6: #24933a;
  --color-success-5: #35af58;
  --color-brand1-3: #68bbf2;
  --color-brand1-4: #2a9feb;
  --color-brand1-5: #0488de;
  --color-brand1-10: #00254c;
  --color-error-4: #e95648;
  --color-error-3: #f7968d;
  --color-error-2: #f8bbb5;
  --color-error-1: #ffe8e6;
  --color-warning-4: #ed9011;
  --color-warning-3: #f7b174;
  --color-warning-2: #ffd9b7;
  --color-warning-1: #ffefe3;
  --color-notice-4: #0488de;
  --color-notice-3: #68bbf2;
  --color-notice-1: #ddf1ff;
  --color-success-4: #45c572;
  --color-success-3: #78d794;
  --color-success-2: #aae4ba;
  --color-success-1: #d6f6da;
  --color-brand1-9: #003263;
  --color-brand1-6: #006ad4;
  --color-brand1-1: #ddf1ff;
  --color-fill1-6: #6b6b6b;
  --color-text1-6: hsla(0, 0%, 80.8%, 0.7);
  --color-text1-7: #8b8b8b;
  --color-gradient-4: linear-gradient(270deg, #ffa3a6, #f52743);
  --color-gradient-3: linear-gradient(270deg, #ffed75, #f5cb22);
  --color-gradient-2: linear-gradient(270deg, #7deeff, #03c1fd);
  --color-gradient-1: linear-gradient(270deg, #79e8c7, #08c29e);
  --color-transparent: transparent;
  --color-black: #000;
  --color-white: #fff;
  --color-fill1-8: #62656f;
  --color-fill1-9: #494e54;
  --color-line1-5: #b1b8bf;
  --color-fill1-5: #c6cdd4;
  --color-fill1-4: #dae0e5;
  --color-fill1-3: #e9edf0;
  --color-line1-3: #c6cdd4;
  --color-line1-2: #dae0e5;
  --color-line1-1: #e9edf0;
  --color-brand3-1: #f2f5f7;
  --color-brand3-2: #e9edf0;
  --color-brand3-4: #c6cdd4;
  --color-brand3-3: #dae0e5;
  --color-brand3-6: #7d8389;
  --color-brand3-5: #b1b8bf;
  --color-brand3-7: #62656f;
  --color-brand3-8: #494e54;
  --color-text1-5: hsla(0, 0%, 80.8%, 0.4);
  --color-help-7: #ffeb6b;
  --color-help-5: #fad414;
  --color-help-6: #d1b10d;
  --color-brand1-2: #c5e8ff;
  --color-text1-4: #292929;
  --color-text1-3: #575757;
  --color-text1-2: #6e6e6e;
  --color-text1-1: #adadad;
  --color-fill1-1: #fff;
  --color-line1-4: #fff;
  --color-other-3: #131415;
  --color-other-2: #fff;
  --color-other-1: #3a3c4b;
  --color-help-4: #f8e71c;
  --color-help-3: #ffee96;
  --color-help-2: #fff5c2;
  --color-help-1: #fffcf0;
  --color-notice-2: #c5e8ff;
  --color-data1-8: #5c60e6;
  --color-data1-7: #4ad051;
  --color-data1-5: #ff6a00;
  --color-data1-4: #ff656b;
  --color-data1-3: #9979f2;
  --color-data1-2: #006cd9;
  --color-data1-1: #5ccdbb;
  --font-size-caption: 12px;
  --font-size-body-1: 14px;
  --font-size-body-2: 14px;
  --font-weight-ultra-bold: 900;
  --font-weight-extra-bold: 800;
  --font-weight-3: bold;
  --font-weight-semi-bold: 600;
  --font-weight-medium: 500;
  --font-weight-2: normal;
  --font-weight-light: 300;
  --font-weight-thin: 200;
  --font-weight-1: lighter;
  --font-lineheight-2: 1.5;
  --font-name-bold: Alibaba-PuHuiTi-Bold;
  --font-name-medium: Alibaba-PuHuiTi-Medium;
  --font-name-light: Alibaba-PuHuiTi-Light;
  --font-name-regular: Alibaba-PuHuiTi-Regular;
  --font-custom-name: 阿里巴巴普惠体;
  --font-name-thin: Alibaba-PuHuiTi-Light;
  --font-custom-path: "//alifd.alicdn.com/fonts/ali-puhuiti/";
  --font-size-subhead: 14px;
  --font-size-title: 16px;
  --font-size-headline: 18px;
  --font-size-display-3: 40px;
  --font-lineheight-3: 2;
  --font-lineheight-1: 1;
  --font-family-base: 阿里巴巴普惠体,"Helvetica Neue","PingFang SC","Noto Sans","Noto Sans CJK SC","Microsoft YaHei","微软雅黑",sans-serif;
  --font-size-display-1: 24px;
  --font-size-display-2: 32px;
  --line-dotted: dotted;
  --line-dashed: dashed;
  --line-solid: solid;
  --line-2: 2px;
  --line-1: 1px;
  --line-zero: 0px;
  --line-3: 4px;
  --mask-background: var(--color-black, #000);
  --mask-opacity: 0.4;
  --s-50: 200px;
  --s-49: 196px;
  --s-48: 192px;
  --s-47: 188px;
  --s-46: 184px;
  --s-45: 180px;
  --s-44: 176px;
  --s-43: 172px;
  --s-42: 168px;
  --s-41: 164px;
  --s-40: 160px;
  --s-39: 156px;
  --s-38: 152px;
  --s-37: 148px;
  --s-36: 144px;
  --s-35: 140px;
  --s-34: 136px;
  --s-33: 132px;
  --s-32: 128px;
  --s-31: 124px;
  --s-30: 120px;
  --s-29: 116px;
  --s-28: 112px;
  --s-27: 108px;
  --s-26: 104px;
  --s-25: 100px;
  --s-24: 96px;
  --s-23: 92px;
  --s-22: 88px;
  --s-21: 84px;
  --s-20: 80px;
  --s-19: 76px;
  --s-18: 72px;
  --s-17: 68px;
  --s-16: 64px;
  --s-15: 60px;
  --s-14: 56px;
  --s-13: 52px;
  --s-12: 48px;
  --s-11: 44px;
  --s-10: 40px;
  --s-9: 36px;
  --s-8: 32px;
  --s-7: 28px;
  --s-6: 24px;
  --s-5: 20px;
  --s-4: 16px;
  --s-3: 12px;
  --s-2: 8px;
  --s-1: 4px;
  --s-zero: 0px;
  --size-base: 4px;
  --shadow-3-left: 0px 0px 20px 0px rgba(0, 0, 0, 0.3);
  --shadow-3-down: 0px 10px 20px 0px rgba(0, 0, 0, 0.3);
  --shadow-3-right: 0px 0px 20px 0px rgba(0, 0, 0, 0.3);
  --shadow-3-up: 0px -10px 20px 0px rgba(0, 0, 0, 0.3);
  --shadow-3: 0px 10px 20px 0px rgba(0, 0, 0, 0.3);
  --shadow-2-left: 0px 0px 15px 0px rgba(0, 0, 0, 0.15);
  --shadow-2-down: 0px 5px 15px 0px rgba(0, 0, 0, 0.15);
  --shadow-2-right: 0px 0px 15px 0px rgba(0, 0, 0, 0.15);
  --shadow-2-up: 0px -5px 15px 0px rgba(0, 0, 0, 0.15);
  --shadow-2: 0px 5px 15px 0px rgba(0, 0, 0, 0.15);
  --shadow-1-left: 0px 0px 8px 0px rgba(0, 0, 0, 0.06);
  --shadow-1-down: 0px 3px 8px 0px rgba(0, 0, 0, 0.06);
  --shadow-1-right: 0px 0px 8px 0px rgba(0, 0, 0, 0.06);
  --shadow-1-up: 0px -3px 8px 0px rgba(0, 0, 0, 0.06);
  --shadow-1: 0px 3px 8px 0px rgba(0, 0, 0, 0.06);
  --shadow-zero: none;
  --shadow-spread-sd3: 0;
  --shadow-spread-sd2: 0;
  --shadow-spread-sd1: 0;
  --shadow-color-sd3: var(--color-black, #000);
  --shadow-color-sd1: var(--color-black, #000);
  --shadow-sides-left: "left";
  --shadow-sides-down: "down";
  --shadow-sides-right: "right";
  --shadow-sides-up: "up";
  --shadow-sides-base: "base";
  --shadow-blur-sd3: 20;
  --shadow-blur-sd2: 15;
  --shadow-opacity-sd3: 0.3;
  --shadow-opacity-sd2: 0.15;
  --shadow-opacity-sd1: 0.06;
  --shadow-distance-sd3y: 10;
  --shadow-distance-sd2y: 5;
  --shadow-blur-sd1: 8;
  --shadow-color-sd2: var(--color-black, #000);
  --shadow-distance-sd1y: 3;
  --shadow-distance-sd3: 0;
  --shadow-distance-sd2: 0;
  --shadow-distance-sd1: 0;
  --icon-reset: "";
  --icon-xxxl: var(--s-16, 64px);
  --icon-xxl: var(--s-12, 48px);
  --icon-xl: var(--s-8, 32px);
  --icon-l: var(--s-6, 24px);
  --icon-m: var(--s-5, 20px);
  --icon-s: var(--s-4, 16px);
  --icon-xs: var(--s-3, 12px);
  --icon-xxs: var(--s-2, 8px);
  --color-link-1: var(--color-brand1-6, #006ad4);
  --color-link-2: var(--color-brand1-5, #0488de);
  --color-link-3: var(--color-brand1-7, #0055ab);
}
.el-avatar {
  background: #0D92E1;
}
body {
  line-height: 1.5;
}
code,
kbd,
pre,
samp {
  font-family: Consolas, Menlo, Courier, monospace;
}
a:active,
a:hover,
a:link,
a:visited {
  text-decoration: none;
}
*::-webkit-scrollbar {
  width: 8px;
  /* Width of the scrollbar */
}
*::-webkit-scrollbar-track {
  background: none;
  /* Color of the track */
}
*::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.01);
  /* Color of the thumb */
  opacity: 0;
  border-radius: 4px;
}
*:hover::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  /* Color of the thumb */
  opacity: 0.8;
}
.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}
.clearfix:after {
  clear: both;
}
.abs-center {
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.flex {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.flex-row {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.flex-item {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  overflow: auto;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, PingFang SC, Noto Sans, Noto Sans CJK SC, Microsoft YaHei, '微软雅黑', sans-serif;
}
body,
html {
  width: 100%;
  height: 100%;
}
[v-cloak] {
  display: none;
}
.yc-navigation-overlay {
  z-index: 100;
}
.yc-navigation-overlay .left-shell {
  -webkit-box-shadow: 0 1px 3px #e5e5e5, inset 0 0 3px #fff;
  box-shadow: 0 1px 3px #e5e5e5, inset 0 0 3px #fff;
  background: #fff;
  z-index: 100;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.yc-navigation-overlay .left-shell.yc-navigation-overlay-inner {
  position: fixed!important;
  height: 100%;
  top: 0!important;
  left: 0;
  right: auto!important;
  -webkit-animation: none;
  animation: none;
  -webkit-transition: all 0.33s cubic-bezier(0, 1, 0.39, 1);
  -o-transition: 0.33s all cubic-bezier(0, 1, 0.39, 1);
  transition: all 0.33s cubic-bezier(0, 1, 0.39, 1);
  -webkit-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
}
.yc-navigation-overlay .left-shell.yc-navigation-overlay-inner.left-to-right {
  -webkit-transform: none!important;
  -ms-transform: none!important;
  transform: none !important;
}
.yc-navigation-overlay .left-shell.yc-navigation-overlay-inner .left-shell-title {
  padding: 10px 0;
  font-size: 16px;
  font-weight: 800;
  position: relative;
}
.yc-navigation-overlay .left-shell.yc-navigation-overlay-inner .left-shell-title .left-shell-close {
  position: absolute;
  cursor: pointer;
  right: 0;
  top: calc(50% - 12px);
}
.left-shell-not-visible {
  -webkit-box-shadow: none!important;
  box-shadow: none !important;
}
.yc-navigation-overlay .right-shell {
  -webkit-box-shadow: 0 1px 3px #e5e5e5, inset 0 0 3px #fff;
  box-shadow: 0 1px 3px #e5e5e5, inset 0 0 3px #fff;
  border-right: 1px solid #e5e5e5;
  background: #fff;
  z-index: 100;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.yc-navigation-overlay .right-shell.yc-navigation-overlay-inner {
  position: fixed!important;
  bottom: 0;
  top: 0;
  right: 0;
  left: auto!important;
  -webkit-animation: none;
  animation: none;
  -webkit-transition: all 0.33s cubic-bezier(0, 1, 0.39, 1);
  -o-transition: 0.33s all cubic-bezier(0, 1, 0.39, 1);
  transition: all 0.33s cubic-bezier(0, 1, 0.39, 1);
  -webkit-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
}
.yc-navigation-overlay .right-shell.yc-navigation-overlay-inner.right-to-left {
  -webkit-transform: none!important;
  -ms-transform: none!important;
  transform: none !important;
}
.yc-navigation-overlay .right-shell.yc-navigation-overlay-inner .right-shell-title {
  padding: 10px 0;
  font-size: 16px;
  font-weight: 800;
  position: relative;
}
.yc-navigation-overlay .right-shell.yc-navigation-overlay-inner .right-shell-title .right-shell-close {
  position: absolute;
  cursor: pointer;
  right: 0;
  top: calc(50% - 12px);
}
.right-shell-not-visible {
  -webkit-box-shadow: none!important;
  box-shadow: none !important;
}
.yc-navigation-menu-divider-no-margin {
  margin: 4px 0!important;
  width: 240px;
  position: absolute;
  left: 0;
  top: 4px;
}
.yc-navigation-overlay-inner-wrapper {
  overflow: hidden;
}
.yc-navigation-overlay-wrapper .yc-navigation-balloon-tooltip {
  z-index: 1001 !important;
}
.yc-navigation-balloon-tooltip-content {
  overflow: unset!important;
  max-height: unset !important;
}
.yc-navigation-project-icon {
  display: inline-block;
  line-height: 0;
  text-align: center;
  vertical-align: -0.25em;
}
.yc-navigation-project-icon svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
  overflow: hidden;
}
.yc-navigation-project-icon-loading-fill,
.yc-navigation-project-icon-loading-line {
  -webkit-animation: loadingCircle 1s linear infinite;
  animation: loadingCircle 1s linear infinite;
}
@-webkit-keyframes loadingCircle {
  0% {
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@keyframes loadingCircle {
  0% {
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
.yc-navigation-project-icon.yc-navigation-project-xxs {
  font-size: 12px;
  -webkit-transform: scale(0.5);
  -ms-transform: scale(0.5);
  transform: scale(0.5);
}
.yc-navigation-project-icon.yc-navigation-project-xs {
  font-size: 12px;
}
.yc-navigation-project-icon.yc-navigation-project-small {
  font-size: 16px;
}
.yc-navigation-project-icon.yc-navigation-project-medium {
  font-size: 20px;
}
.yc-navigation-project-icon.yc-navigation-project-large {
  font-size: 24px;
}
.yc-navigation-project-icon.yc-navigation-project-xl {
  font-size: 32px;
}
.yc-navigation-project-icon.yc-navigation-project-xxl {
  font-size: 48px;
}
.yc-navigation-project-icon.yc-navigation-project-xxxl {
  font-size: 64px;
}
.yc-navigation-project-icon.yc-navigation-project-inherit {
  font-size: inherit;
}
.yc-navigation-contextSwitcher {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 14px;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-image {
  height: 28px;
  width: 140px;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-text {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
  width: 191px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap,
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-icon-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-icon-wrap {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  position: relative;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: -webkit-box-shadow 218ms;
  transition: -webkit-box-shadow 218ms;
  -o-transition: box-shadow 218ms;
  transition: box-shadow 218ms;
  transition: box-shadow 218ms, -webkit-box-shadow 218ms;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-icon-wrap > i {
  color: #fff;
  opacity: 0;
  -webkit-transition: opacity 218ms;
  -o-transition: opacity 218ms;
  transition: opacity 218ms;
  font-size: 14px;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-icon-wrap:hover .yc-navigation-logo-icon {
  opacity: 0;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-homepage:hover .yc-navigation-logo-icon,
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-icon-wrap:hover > i {
  opacity: 1;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap > i {
  margin: 0 4px;
  font-size: 20px;
  color: #8c8c8c;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-logo-icon {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: 50%;
  opacity: 1;
  -webkit-transition: opacity 218ms;
  -o-transition: opacity 218ms;
  transition: opacity 218ms;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-logo-name {
  margin-left: 8px;
  font-size: 15px;
  font-weight: 600;
  color: #262626;
  line-height: 24px;
  max-width: 178px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-logo-name-en {
  font-size: 16px;
  margin-left: 4px;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-logo-icon-new {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}
.yc-navigation-contextSwitcher .yc-navigation-logo-wrap .yc-navigation-logo-name-new {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  font-family: PingFangSC-Regular;
  max-width: 178px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.yc-navigation-contextSwitcher .yc-navigation-contextSwitcher-element {
  height: 24px;
  padding: 2px 4px;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  -webkit-transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
  -o-transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
  transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
}
.yc-navigation-contextSwitcher .yc-navigation-contextSwitcher-element .yc-navigation-contextSwitcher-element-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.yc-navigation-contextSwitcher .yc-navigation-contextSwitcher-element .yc-navigation-contextSwitcher-element-title {
  font-size: 14px;
  line-height: 20px;
  color: #262626;
  font-weight: 500;
}
.yc-navigation-contextSwitcher .yc-navigation-contextSwitcher-element > i {
  margin-left: 4px;
  padding: 0 2px;
}
.yc-navigation-contextSwitcher .yc-navigation-contextSwitcher-element:hover {
  background: #f7f7f7;
}
.yc-navigation-breadcrumb,.yc-navigation-breadcrumb-home {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  
}

.yc-navigation-title{
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: inline-block;
	min-width: 200px;
	max-width: 500px;
}

.yc-navigation-breadcrumb-home {
  word-break: keep-all;
  color: var(--color-text1-7, #8b8b8b) !important;
  font-size: 14px;
  cursor: pointer;
}
.yc-navigation-breadcrumb-home-logo {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 4px;
}
.yc-navigation-breadcrumb .yc-navigation-breadcrumb-onelevel-icon,
.yc-navigation-breadcrumb .yc-navigation-breadcrumb-twolevel-icon {
  display: none;
}
.yc-navigation-breadcrumb-onelevel .yc-navigation-breadcrumb-onelevel-icon,
.yc-navigation-breadcrumb-twolevel .yc-navigation-breadcrumb-onelevel-icon,
.yc-navigation-breadcrumb-twolevel .yc-navigation-breadcrumb-twolevel-icon {
  display: block;
}
.yc-nav-breadcrumb-more {
  width: 240px;
  max-height: 202px;
  background: #fff;
  overflow-y: auto;
}
.yc-nav-breadcrumb-more-icon {
  cursor: pointer;
}
.yc-nav-breadcrumb-more .next-menu-item {
  width: 240px;
}
.yc-nav-breadcrumb-more .next-menu-item .next-menu-item-inner {
  padding-right: 0;
}
.yc-nav-breadcrumb-more .next-menu-item .next-menu-item-inner .next-menu-item-text {
  width: 208px;
}
.yc-nav-breadcrumb-more-icon-container .yc-nav-breadcrumb-more-icon {
  color: #8c8c8c;
}
.yc-nav-breadcrumb-more-icon-container:hover .yc-nav-breadcrumb-more-icon {
  color: inherit;
}
.yc-navigation-color-span-content {
  color: grey;
}
.yc-navigation-color-span-content:hover {
  color: unset;
}
.yc-navigation-color-span-icon-content {
  background: #fff;
  color: grey;
  width: 32px;
  height: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
  -o-transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
  transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
}
.yc-navigation-color-span-icon-content:hover {
  background: transparent;
  color: unset;
}
.isActive {
  border-radius: 4px;
  background-color: var(--color-notice-1, #f0f9ff);
  color: var(--color-brand1-6, #1b9aee);
}
.isActive svg {
  color: var(--color-brand1-6, #1b9aee) !important;
}
.yc-navigation-color-span-icon {
  width: 32px;
  border-radius: 4px;
}
.yc-navigation-color-span-icon,
.yc-navigation-entrance {
  height: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.yc-navigation-entrance {
  font-size: 14px;
  margin: 0 12px 0 0;
  line-height: 32px;
  position: relative;
  cursor: pointer;
}
#yc-navigation-custom-help .yc-help-custom-container {
  width: 100%;
  height: 144px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #dcdcdc;
}
#yc-navigation-custom-help .yc-help-custom-container .codeScan {
  margin-top: 21px;
  width: 78px;
  height: 78px;
  background-color: #fff;
}
#yc-navigation-custom-help .yc-help-custom-container p {
  width: 100%;
  font-size: 12px;
  color: var(--color-text1-2, #6e6e6e);
  text-align: center;
  line-height: 18px;
  font-weight: 400;
}
#yc-navigation-help .next-menu-item {
  padding: 0 8px;
}
.yc-navigation-help {
  margin: 0 16px 0 4px;
  border-radius: 4px;
}
.yc-navigation-help[data-active=true] .yc-navigation-color-span-icon-content {
  background: var(--color-brand1-1, #f0f9ff) !important;
  color: var(--color-brand1-6, #1b9aee);
  border-radius: 4px;
}
.yc-navigation-help[data-active=true] svg {
  color: var(--color-brand1-6, #1b9aee) !important;
}
.yc-navigation-help-dot {
  background-color: var(--color-white, #fff) !important;
  height: 12px;
  width: 12px;
  position: absolute;
  border-radius: 100%;
  top: 2px;
  right: 2px;
}
.yc-navigation-help-dot .dot-container {
  position: absolute;
  left: 2px;
  top: 2px;
  border-radius: 100%;
  width: 8px;
  z-index: 1;
  height: 8px;
  background: #3da8f5;
}
.yc-navigation-avatar {
  width: 28px;
  height: 28px;
  padding: 0;
  margin-right: 0;
  position: relative;
  cursor: pointer;
}
.yc-navigation-avatar img {
  width: 28px;
  height: 28px;
  border-radius: 100%;
}
.yc-navigation-avatar-mask {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0;
  opacity: 0;
  width: 28px;
  height: 28px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: rgba(56, 56, 56, 0.6);
  -webkit-transition: opacity 0.3s;
  -o-transition: opacity 0.3s;
  transition: opacity 0.3s;
  border-radius: 50%;
}
.yc-csutom-dropdown .next-menu-item {
  border-radius: 4px;
}
.yc-csutom-dropdown .next-menu-item .next-menu-item-inner {
  line-height: 32px !important;
}
.yc-csutom-dropdown .next-menu-item .next-menu-item-inner .next-menu-item-text {
  width: 100%;
}
.yc-navigation-avatar:hover .yc-navigation-avatar-mask {
  opacity: 1;
}
.yc-navigation-avatar:hover .yc-navigation-avatar-mask i {
  color: #fff;
}
.yc-navigation-top .yc-navigation-avatar-tooltip-container {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  height: 40px;
  background-color: var(--color-brand1-1, #f0f9ff);
  border-radius: 4px 4px 0 0;
}
.yc-navigation-top .yc-navigation-avatar-tooltip-container,
.yc-navigation-top .yc-navigation-avatar-tooltip-container .avatar-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.yc-navigation-top .yc-navigation-avatar-tooltip-container .avatar-container {
  background-color: var(--color-white, #fff);
  position: relative;
  top: 18px;
  z-index: 999;
  width: 44px;
  height: 44px;
  border-radius: 50%;
}
.yc-navigation-top .yc-navigation-avatar-tooltip-container .avatar-container .avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.yc-navigation-top .avatar-user-name {
  position: relative;
  width: 100%;
  top: 22px;
  margin: 8px 0;
}
.yc-navigation-top .avatar-user-name p {
  color: var(--color-text1-4, #292929);
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
  text-align: center;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 190px;
  margin: 0 auto;
}
.yc-navigation-menu .yc-navigation-menu-item {
  padding: 0 8px;
  height: 32px;
  width: 100%;
}
.yc-navigation-menu .yc-navigation-menu-item .yc-navigation-menu-item-inner {
  padding-right: 0;
}
.yc-navigation-org-name-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-right: -8px;
}
.yc-navigation-org-name-item .yc-navigation-org-name {
  max-width: 200px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#yc-navigation-container .change-organization-item {
  font-size: 13px;
  height: 32px;
  width: 200px;
}
#yc-navigation-container .change-organization-item:first-of-type {
  margin-top: 30px;
}
#yc-navigation-container .change-organization-item .next-menu-item {
  height: 32px;
}
#yc-navigation-container .change-organization-item .next-menu-item-inner {
  height: 32px;
  width: 100%;
}
#yc-navigation-container .change-organization-item .yc-navigation-menu-item-inner {
  padding-right: 0;
}
#yc-navigation-container .change-organization-item .change-organization-container .organization-name {
  margin-left: 8px;
  font-size: 12px;
  line-height: 18px;
  font-weight: 400;
  color: var(--color-fill1-6, #8b8b8b);
}
#yc-navigation-container .change-organization-item .current-organization {
  padding: 0 8px;
  color: #8c8c8c;
  font-size: 12px;
  line-height: 48px;
  width: 100%;
  background: var(--color-fill1-2, #f7f7f7);
  border-radius: 4px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
#yc-navigation-container .change-organization-item .current-organization .current-organization-name {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 13px;
  font-weight: 500;
  color: var(--color-text1-4, #292929);
}
#yc-navigation-container .change-organization-item .current-organization .current-organization-name img {
  width: 20px;
  height: 20px;
  border-radius: 2px;
  margin-right: 8px;
}
#yc-navigation-container .change-organization-item .current-organization .current-organization-name div {
  width: 125px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}
#yc-navigation-container .change-organization-item .current-organization .switch {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 48px;
}
#yc-navigation-container .change-current-org-item {
  margin-top: 8px;
  padding: 8px!important;
  height: auto;
}
#yc-navigation-container .change-current-org-item > .switchOrg {
  height: auto;
  padding: 0!important;
  margin-bottom: 8px;
  font-size: 13px;
}
#yc-navigation-container .change-current-org-item > .switchOrg .next-menu-item {
  height: auto;
}
#yc-navigation-container .change-current-org-item > .switchOrg .next-menu-item-inner {
  height: auto;
  width: 100%;
}
#yc-navigation-container .change-current-org-item > .switchOrg .next-menu-item-inner .next-menu-item-text {
  margin-right: 0 !important;
}
#yc-navigation-container .change-current-org-item > .switchOrg .yc-navigation-menu-item-inner {
  padding-right: 0;
}
#yc-navigation-container .change-current-org-item > .switchOrg:hover {
  background-color: var(--color-white, #fff) !important;
}
#yc-navigation-container .change-current-org-item > .switchOrg .yc-navigation-menu-divider-no-margin {
  margin: 2px 0 !important;
}
#yc-navigation-container .no-border-custom {
  border: none;
  padding: 0 !important;
}
.yc-navigation-switch-language {
  width: 240px;
}
.yc-navigation-switch-language .yc-navigation-menu-icon-selected {
  position: absolute;
  left: 12px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: #1b9aee;
  font-size: 20px;
}
.yc-navigation-switch-language-header {
  position: relative;
  padding: 12px 40px;
  border-bottom: 1px solid #f0f0f0;
  line-height: 24px;
  font-size: 16px;
  font-weight: 500;
  background: #fff;
  color: #262626;
  text-align: center;
}
.yc-navigation-switch-language-header-icon {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: #8c8c8c;
  cursor: pointer;
}
.yc-navigation-switch-language-header-back {
  left: 16px;
}
.yc-navigation-switch-language-header-close {
  right: 16px;
}
.yc-navigation-avatar-logout {
  padding-bottom: 0 !important;
}
.yc-navigation-avatar-logout:first-of-type {
  margin-top: 30px;
}
.yc-navigation-avatar-logout:last-child {
  padding-bottom: 8px !important;
}
.yc-navigation-avatar-logout .next-menu-item-inner {
  width: 100%;
  height: 32px !important;
}
.yc-navigation-custom-entrance {
  font-size: 14px;
  height: 24px;
  line-height: 24px;
  position: relative;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.yc-navigation-custom-entrance[data-active=true] .yc-navigation-color-span-icon-content {
  background: var(--color-notice-1, #f0f9ff) !important;
}
.yc-navigation-custom-entrance-separator {
  width: 1px;
  height: 16px;
  background: #e5e5e5;
  position: absolute;
  top: 3px;
  right: 0;
}
#Notification .yc-navigation-custom-entrance-separator {
  display: none;
}
.yc-navigation-icon-wraper {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
}
.yc-navigation-icon-wraper:hover > i {
  color: inherit;
}
#yc-navigation-dock > div > div:first-child {
  height: 24px;
}
#yc-navigation-dock .yc-navigation-badge {
  vertical-align: unset !important;
}
#yc-navigation-dock .yc-navigation-badge-dot {
  -webkit-animation: dotBreath 2s linear infinite;
  animation: dotBreath 2s linear infinite;
}
@-webkit-keyframes dotBreath {
  0% {
    -webkit-box-shadow: 0 0 0 1px #cce1ff;
    box-shadow: 0 0 0 1px #cce1ff;
  }
  50% {
    -webkit-box-shadow: 0 0 0 5px #cce1ff;
    box-shadow: 0 0 0 5px #cce1ff;
  }
  to {
    -webkit-box-shadow: 0 0 0 1px #cce1ff;
    box-shadow: 0 0 0 1px #cce1ff;
  }
}
@keyframes dotBreath {
  0% {
    -webkit-box-shadow: 0 0 0 1px #cce1ff;
    box-shadow: 0 0 0 1px #cce1ff;
  }
  50% {
    -webkit-box-shadow: 0 0 0 5px #cce1ff;
    box-shadow: 0 0 0 5px #cce1ff;
  }
  to {
    -webkit-box-shadow: 0 0 0 1px #cce1ff;
    box-shadow: 0 0 0 1px #cce1ff;
  }
}
#Dock_CONTAINER {
  min-width: 250px;
}
.yc-navigation-appMode-dock #Dock_CONTAINER {
  min-width: 324px;
}
.yc-navigation-appMode-dock #Dock_CONTAINER .yc-docker-top-container {
  padding: 12px 14px 12px 18px !important;
}
.yc-navigation-dock-guide {
  position: fixed;
  width: 200px;
  height: 258px;
  border-radius: 4px;
  z-index: 100;
  background: #1b9aee;
  top: 46px;
  left: 13px;
  -webkit-animation: dock-shake 3s linear infinite;
  animation: dock-shake 3s linear infinite;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
.yc-navigation-dock-guide:before {
  content: "";
  position: absolute;
  left: 16px;
  top: -12px;
  height: 0;
  width: 0;
  border: 6px solid transparent;
  border-bottom-color: #1b9aee;
}
.yc-navigation-dock-guide-btn {
  background: #fff;
  border-radius: 4px;
  width: 92px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  color: #1b9aee;
  cursor: pointer;
  margin-top: 12px;
  margin-left: auto;
  margin-right: auto;
  font-size: 14px;
}
.yc-navigation-dock-guide-desc {
  font-size: 12px;
  padding: 0 16px;
  line-height: 1.5;
  color: #fff;
  margin-top: 10px;
}
.yc-navigation-dock-guide-title {
  color: #fff;
  text-align: center;
  font-size: 14px;
  margin-top: 16px;
}
.yc-navigation-dock-guide-img {
  width: 180px;
  height: 102px;
  margin-top: 20px;
  margin-left: 10px;
}
.yc-navigation-dock-guide-img img {
  width: 100%;
  height: 100%;
}
@-webkit-keyframes dock-shake {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  40% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  50% {
    -webkit-transform: translateY(6px);
    transform: translateY(6px);
  }
  60% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  70% {
    -webkit-transform: translateY(6px);
    transform: translateY(6px);
  }
  80% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  to {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
}
@keyframes dock-shake {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  40% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  50% {
    -webkit-transform: translateY(6px);
    transform: translateY(6px);
  }
  60% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  70% {
    -webkit-transform: translateY(6px);
    transform: translateY(6px);
  }
  80% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  to {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
}
.yc-navigation-logo-container {
  padding: 0 12px 0 16px;
  height: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
}
.yc-navigation-logo-container img {
  height: 20px;
}
.yc-navigation-logo-container .yc-navigation-logo-separator {
  width: 1px;
  height: 16px;
  background: #f7f7f7;
  position: absolute;
  top: 3px;
  right: 0;
}
.yc-navigation-locale {
  margin: 0 18px 0 2px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, PingFang SC, Noto Sans, Noto Sans CJK SC, Microsoft YaHei, '微软雅黑', sans-serif;
}
.row-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.yc-navigation-fixed-top {
  top: 0;
  border-width: 0 0 1px;
  position: fixed;
  right: 0;
  left: 0;
}
.no-layout-shadow .system-bar {
  -webkit-box-shadow: none!important;
  box-shadow: none !important;
}
.no-layout-shadow.yc-navigation-navbar {
  border-bottom-color: #f0f0f0;
}
.yc-navigation-navbar {
  min-height: 50px;
  border-bottom: 1px solid var(--color-line1-1, #e8e8e8);
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: var(--color-white, #fff);
}
.yc-navigation-navbar > i {
  font-size: 16px;
}
.yc-navigation-navbar * {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.yc-navigator-container {
  z-index: 100;
  -webkit-transform: none;
  -ms-transform: none;
  transform: none;
}
.yc-navigator-container .yc-navigator-nav-inner {
  z-index: 100;
  height: 48px;
}
.yc-navigator-container .system-bar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0 20px;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}
.yc-navigator-container .system-bar .system-bar-left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -ms-flex-preferred-size: calc(50vw - 160px);
  flex-basis: calc(50vw - 160px);
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  z-index: 5;
  padding-right: 16px;
}
.yc-navigator-container .system-bar .system-bar-left #yc-navigation-customAction {
  max-width: 100px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-left: 8px;
  height: 100%;
}
.yc-navigator-container .system-bar .system-bar-left #yc-navigation-customAction > i {
  cursor: pointer;
}
.yc-navigator-container .system-bar .system-bar-right {
  -ms-flex-align: center;
  position: relative;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -ms-flex-preferred-size: calc(50vw - 16px);
  flex-basis: calc(50vw - 16px);
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  padding-left: 16px;
  z-index: 4;
}
.yc-navigator-container .system-bar #yc-navigation-dock,
.yc-navigator-container .system-bar .system-bar-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-flex: 0;
}
.yc-navigator-container .system-bar #yc-navigation-dock {
  -ms-flex-align: center;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
  height: 100%;
}
.yc-navigator-container .system-bar #yc-navigation-customInvite {
  margin-right: 16px;
}
.yc-navigator-container .system-bar #yc-navigation-customInvite:empty {
  margin-right: 0;
}
.yc-navigator-container .system-bar #yc-navigation-customInvite > div > div {
  background: var(--color-brand1-6, #1b9aee);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.yc-navigator-container .system-bar #yc-navigation-customInvite > div > div:hover {
  background: var(--color-brand1-7, #127fc7);
}
.yc-navigator-container .system-bar #yc-navigation-searcher {
  padding-right: 12px;
}
.yc-navigator-container .system-bar #yc-navigation-searcher:empty {
  padding-right: 0;
  width: 0 !important;
}
.yc-navigator-container .system-bar #yc-navigation-searcher ~ div {
  display: block;
}
.yc-navigator-container .system-bar #yc-navigation-searcher.yc-navigation-searcher-for-global {
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  width: 240px;
}
.yc-navigator-container .system-bar #yc-navigation-searcher.yc-navigation-searcher-for-global #yc-navigation-searcher-input {
  display: none;
}
.yc-navigator-container .system-bar #yc-navigation-searcher.yc-navigation-searcher-for-global .yc-navigation-searcher {
  background: transparent;
}
.yc-navigator-container .system-bar #yc-navigation-searcher.yc-navigation-searcher-for-global .search-bar-mask,
.yc-navigator-container .system-bar #yc-navigation-searcher.yc-navigation-searcher-for-global .yc-navigation-search-shortcut {
  cursor: pointer;
}
.yc-navigator-container .system-bar #yc-navigation-searcher.yc-navigation-searcher-for-global:hover {
  background-color: var(--color-brand3-1, #f2f5f7);
}
.yc-navigator-container .system-bar #yc-navigation-searcher.yc-navigation-searcher-for-global:hover .yc-navigation-searcher:before {
  display: none;
}
.yc-navigator-container .system-bar #Notification_CONTAINER {
  border-top: 1px solid #f0f0f0;
  margin-top: -1px;
}
.yc-navigator-container .system-bar #yc-navigation-createEntrance {
  margin: 8px 0!important;
  max-height: 400px;
}
.yc-navigator-container .system-bar #yc-navigation-platformEntrance {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.yc-navigator-container .system-bar #yc-navigation-platformEntrance .yc-navigation-custom-entrance {
  padding: 0 4px;
}
.yc-navigator-container .system-bar #yc-navigation-customEntrance-more-trigger,
.yc-navigator-container .system-bar #yc-navigation-platformEntrance,
.yc-navigator-container .system-bar .create-entrance,
.yc-navigator-container .system-bar .yc-navigation-entrance {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}
.yc-navigator-container .system-bar .create-entrance {
  margin-left: 30px;
}
.yc-navigator-container .system-bar #yc-navigation-customEntrance {
  visibility: hidden;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.yc-navigator-container .system-bar #yc-navigation-customEntrance .custom-entrance-item {
  display: inline-block;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}
.yc-navigator-container .system-bar #yc-navigation-customEntrance.active-custom-entrance {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  visibility: visible;
  -webkit-box-flex: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}
.yc-navigator-container .system-bar #yc-navigation-createEntrance-container {
  visibility: hidden;
}
.yc-navigator-container .system-bar #yc-navigation-createEntrance-container.active-custom-entrance {
  visibility: visible;
}
.yc-navigator-container .system-bar #yc-navigation-platform-operation {
  max-width: 124px;
}
.yc-navigator-container .system-bar .yc-navigation-color-span,
.yc-navigator-container .system-bar .yc-navigation-icon-wraper {
  -webkit-transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
  -o-transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
  transition: background-color 0.3s ease, border 0.3s ease, color 0.3s ease;
}
.yc-navigator-container .system-bar #yc-navigation-customOperation {
  margin-right: 14px;
}
.yc-navigator-container .system-bar #yc-navigation-customOperation:empty {
  margin-right: 0;
}
.yc-navigator-container .docker-trigger {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  height: 100%;
  margin-left: 2px;
  -ms-flex-item-align: center;
  align-self: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
}
.yc-navigator-container .docker-trigger,
.yc-navigator-container .navigation-breadcrumbs,
.yc-navigator-container .navigation-breadcrumbs .main {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.yc-navigator-container .navigation-breadcrumbs .main {
  margin: 0 4px 0 14px;
}
.yc-navigator-container .navigation-breadcrumbs .main .yc-navigation-logo-image {
  display: inline-block;
  width: 92px;
  height: 14px;
  cursor: pointer;
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
}
.yc-navigation-fullscreen {
  width: 100%;
  height: 100vh;
  top: 0 !important;
}
#yc-navigation-announcement-content {
  position: relative;
  z-index: -1;
  text-align: center;
}
#yc-navigation-announcement-content .next-message-symbol {
  display: none;
}
.project-lead-changelog-dialog .project-lead-changelog-dialog-body {
  padding: 0 !important;
}
.project-lead-changelog-dialog > [class$=-loading] {
  overflow-y: auto;
}
.yc-navigation-searcher {
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  -ms-flex-item-align: center;
  align-self: center;
  border-radius: 0;
  -webkit-transition: all 218ms;
  -o-transition: all 218ms;
  transition: all 218ms;
  height: 32px;
  margin: auto;
  background-color: var(--color-white, #fff);
  font-size: 14px;
  line-height: 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.yc-navigation-searcher .yc-navigation-search-clear-button {
  position: absolute;
  right: 8px;
  z-index: 2;
  color: #8c8c8c;
  cursor: pointer;
  -webkit-transition: color 0.3s linear;
  -o-transition: color 0.3s linear;
  transition: color 0.3s linear;
}
.yc-navigation-searcher .yc-navigation-search-clear-button:hover {
  color: #595959;
}
.yc-navigation-searcher .search-bar-mask {
  max-width: 170px;
  position: absolute;
  width: auto;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  top: 6px;
  height: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  left: 0;
  -webkit-transform: translateX(12px) translateZ(0);
  transform: translateX(12px) translateZ(0);
  transition-property: transform, -webkit-transform;
  -webkit-transition-duration: 168ms;
  -o-transition-duration: 168ms;
  transition-duration: 168ms;
  -webkit-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: text;
}
.yc-navigation-searcher .search-bar-mask .icon-search {
  margin-right: 8px;
  padding: 0;
}
.yc-navigation-searcher .search-bar-mask .search-bar-mask-text {
  color: inherit;
  font-weight: 400;
  font-size: 14px;
  -webkit-transition: color 168ms linear;
  -o-transition: color 168ms linear;
  transition: color 168ms linear;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.yc-navigation-searcher input {
  border: none;
  outline: none;
  background: transparent;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  font-size: 14px;
  height: 40px;
  line-height: 26px;
  border-radius: 3px;
  padding: 8px 28px 8px 40px;
  display: block;
  opacity: 1;
  width: 240px;
  position: relative;
  z-index: 1;
}
.yc-navigation-searcher input::-webkit-input-placeholder {
  color: #bfbfbf;
  font-weight: 400;
}
.yc-navigation-searcher input::-moz-placeholder {
  color: #bfbfbf;
  font-weight: 400;
}
.yc-navigation-searcher input:-ms-input-placeholder {
  color: #bfbfbf;
  font-weight: 400;
}
.yc-navigation-searcher input::-ms-input-placeholder {
  color: #bfbfbf;
  font-weight: 400;
}
.yc-navigation-searcher input::placeholder {
  color: #bfbfbf;
  font-weight: 400;
}
.yc-navigation-searcher .yc-navigation-searcher-icon-place {
  width: 24px;
  height: 16px;
  cursor: text;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  padding: 0 0 0 12px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.yc-navigation-searcher:before {
  content: "";
  display: block;
  position: absolute;
  left: 1px;
  width: 1px;
  height: 20px;
  background-color: var(--color-line1-1, #e8e8e8);
}
.inactive .cds-input-icon-wrapper-item-prefix {
  left: 80px;
}
.yc-navigation-searcher-has-query .search-bar-mask {
  padding-right: 0;
  -webkit-transform: translateX(12px) translateZ(0);
  transform: translateX(12px) translateZ(0);
}
.yc-navigation-searcher-has-query .search-bar-mask .search-bar-mask-text {
  position: absolute;
  visibility: hidden;
  height: 0;
}
.yc-navigation-searcher-has-query input {
  -webkit-animation: tbOpacityShow 168ms;
  animation: tbOpacityShow 168ms;
  opacity: 1;
  color: #262626 !important;
}
@-webkit-keyframes tbOpacityShow {
  0% {
    opacity: 0;
  }
  90% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes tbOpacityShow {
  0% {
    opacity: 0;
  }
  90% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes tbMoveToRight {
  0% {
    left: 12px;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  to {
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}
@keyframes tbMoveToRight {
  0% {
    left: 12px;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  to {
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}
@-webkit-keyframes tbMoveToLeft {
  0% {
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }
  to {
    left: 12px;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@keyframes tbMoveToLeft {
  0% {
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }
  to {
    left: 12px;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}
@-webkit-keyframes navHiddenText {
  0% {
    opacity: 1;
  }
  99% {
    opacity: 1;
  }
  to {
    opacity: 0;
    max-height: 0;
    visibility: hidden;
    position: absolute;
  }
}
@keyframes navHiddenText {
  0% {
    opacity: 1;
  }
  99% {
    opacity: 1;
  }
  to {
    opacity: 0;
    max-height: 0;
    visibility: hidden;
    position: absolute;
  }
}
.yc-navigation-search-shortcut {
  position: absolute;
  padding: 2px 6px;
  right: 0;
  background: var(--color-brand3-1, #f2f5f7);
  border-radius: 4px;
  font-size: 14px;
  color: #8b8b8b;
  line-height: 16px;
  font-weight: 400;
}
.yc-navigation-dropdown-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  vertical-align: middle;
}
.next-menu-item.next-menu-item-disabled {
  color: #bfbfbf;
  background-color: #fff;
  cursor: not-allowed;
}
.next-menu-item.next-menu-item-disabled:hover {
  color: #bfbfbf !important;
  background-color: #fff !important;
}
.project-title {
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.project-title-tooltip-trigger {
  cursor: pointer;
}
.system-bar-right .yc-navigation-create-entrance-container {
  height: 24px;
  width: 24px;
  padding: 2px 0;
}
.system-bar-right .yc-navigation-create-entrance-container,
.system-bar-right .yc-navigation-create-entrance-container .yc-navigation-create-entrance {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.system-bar-right .yc-navigation-create-entrance-container .yc-navigation-create-entrance {
  margin-left: 0;
  border-radius: 4px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  background: #fff;
}
.system-bar-right .yc-navigation-create-entrance-container .yc-navigation-create-entrance:hover {
  background-color: var(--color-fill1-2, #f7f7f7);
}
.system-bar-right .yc-navigation-create-entrance-container .yc-navigation-create-entrance[data-active=true] {
  background-color: var(--color-brand1-1, #f0f9ff);
}
.system-bar-right .yc-navigation-create-entrance-container .yc-navigation-create-svg {
  -webkit-mask: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxwYXRoIGQ9Ik0xMCAyMEM0LjQ3NyAyMCAwIDE1LjUyMyAwIDEwUzQuNDc3IDAgMTAgMHMxMCA0LjQ3NyAxMCAxMC00LjQ3NyAxMC0xMCAxMHptLjYxNS02LjU4NHYtMi44MDFoMi44MDFhLjYxNi42MTYgMCAwMDAtMS4yM2gtMi44MDFWNi41ODVhLjYxNS42MTUgMCAxMC0xLjIzIDB2Mi44bC0yLjguMDAyYS42MTQuNjE0IDAgMTAtLjAwMiAxLjIzaDIuODAydjIuOGEuNjE1LjYxNSAwIDAwMS4yMyAweiIgaWQ9ImEiLz48L2RlZnM+PHVzZSBmaWxsPSIjMDAwIiB4bGluazpocmVmPSIjYSIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+) no-repeat center center;
  mask: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxwYXRoIGQ9Ik0xMCAyMEM0LjQ3NyAyMCAwIDE1LjUyMyAwIDEwUzQuNDc3IDAgMTAgMHMxMCA0LjQ3NyAxMCAxMC00LjQ3NyAxMC0xMCAxMHptLjYxNS02LjU4NHYtMi44MDFoMi44MDFhLjYxNi42MTYgMCAwMDAtMS4yM2gtMi44MDFWNi41ODVhLjYxNS42MTUgMCAxMC0xLjIzIDB2Mi44bC0yLjguMDAyYS42MTQuNjE0IDAgMTAtLjAwMiAxLjIzaDIuODAydjIuOGEuNjE1LjYxNSAwIDAwMS4yMyAweiIgaWQ9ImEiLz48L2RlZnM+PHVzZSBmaWxsPSIjMDAwIiB4bGluazpocmVmPSIjYSIgZmlsbC1ydWxlPSJldmVub2RkIi8+PC9zdmc+) no-repeat center center;
  width: 20px;
  height: 20px;
  -webkit-transition: -webkit-filter 0.3s ease-in-out;
  transition: -webkit-filter 0.3s ease-in-out;
  -o-transition: filter 0.3s ease-in-out;
  transition: filter 0.3s ease-in-out;
  transition: filter 0.3s ease-in-out, -webkit-filter 0.3s ease-in-out;
  border-radius: 50%;
}
.system-bar-right .yc-navigation-create-entrance-container .yc-navigation-create-svg:hover {
  -webkit-filter: brightness(0.9);
  filter: brightness(0.9);
}
.system-bar-right .yc-navigation-create-entrance-container > i {
  color: #fff;
  font-size: 14px;
}
#yc-navigation-createEntrance-container {
  margin: 0 8px;
  position: relative;
}
.platform-operation-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.platform-operation-container .redirect-img {
  height: 24px;
}
.yc-navigation-badge-container {
  position: absolute;
  display: inline-block;
  line-height: 0;
  vertical-align: middle;
  right: 6px;
  top: 4px;
  cursor: pointer;
}
.yc-navigation-badge-container .yc-navigation-badge {
  position: absolute;
  top: 0;
  right: -5px;
  padding: 0 4px;
  border: 2px solid #fff;
  color: #fff;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
  opacity: 0.9;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  line-height: 16px;
}
.yc-navigation-badge-container .yc-navigation-badge-dot {
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid #fff;
  position: absolute;
  right: 1px;
  top: -6px;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  pointer-events: none;
}
@-webkit-keyframes fadein {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadein {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
#yc-common-sidebar-wrapper {
  height: calc(100vh - 48px);
  display: inline-block;
  position: fixed;
  left: 0;
  top: 48px;
  z-index: 99;
  -webkit-transition: width 2s;
  -o-transition: width 2s;
  transition: width 2s;
}
.edit-mode {
  margin-top: -8px;
  width: 220px !important;
}
.edit-mode,
.nav-popup-class {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 8px !important;
}
.nav-popup-class {
  margin-left: 10px;
  min-width: 240px;
  border: none!important;
  -webkit-box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.15) !important;
}
.nav-popup-class:after {
  display: none;
}
.nav-popup-class .yc-common-sidebar-main-item {
  font-weight: 400;
  width: auto;
}
.nav-popup-class .yc-common-sidebar-main-item:not(:last-child) {
  margin: 0 0 8px !important;
}
.nav-popup-class .yc-common-sidebar-main-item:last-child {
  margin: 0;
}
.nav-popup-class .yc-common-sidebar-main-item-name {
  font-weight: 400;
  font-size: 13px;
}
.nav-popup-class .yc-common-sidebar-main-item-name:hover {
  font-weight: 500;
}
.nav-popup-class .is-group-parent-ballon {
  cursor: default;
}
.nav-popup-class .is-group-parent-ballon:hover {
  background-color: var(--color-white, #fff);
}
.nav-popup-class .is-group-parent-ballon .yc-common-sidebar-main-item-name {
  margin-left: 12px;
  color: var(--color-fill1-6, #8b8b8b);
  font-weight: 600;
}
.nav-popup-class .is-group-parent-ballon .yc-common-sidebar-main-item-name:hover {
  font-weight: 600;
}
.nav-popup-class .is-group-item-ballon .yc-common-sidebar-main-item-name {
  margin-left: 24px;
}
.nav-popup-class .is-group-item-ballon:hover,
.nav-popup-class .is-group-item-ballon[data-active=true] {
  color: var(--color-text1-4, #292929);
  font-weight: 600;
}
.yc-common-sidebar {
  width: 221px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 16px 8px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  border-right: 1px solid var(--color-line1-1, #e8e8e8);
  z-index: 90;
  left: 0;
  top: 0;
  height: 100%;
  background-color: var(--color-white, #fff);
}
.yc-common-sidebar,
.yc-common-sidebar-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
}
.yc-common-sidebar-header {
  width: 188px;
  height: 32px;
  margin-top: 16px;
  overflow: hidden;
}
.yc-common-sidebar-header-clickable {
  cursor: pointer;
}
.yc-common-sidebar-header-tooltip {
  opacity: 0.8;
  z-index: 1000;
  cursor: pointer;
  position: absolute;
  width: 32px;
  display: none;
  height: 32px;
  background-color: #1f3957;
  border-radius: 4px;
}
.yc-common-sidebar-header-tooltip svg {
  z-index: 1000;
  position: absolute;
  display: inline-block;
  width: 20px;
  height: 20px;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  color: var(--color-white, #fff);
  border-radius: 4px;
}
.yc-common-sidebar-header:hover > .yc-common-sidebar-header-tooltip {
  display: inline;
}
.yc-common-sidebar-header-logo {
  display: inline-block;
  width: 32px;
  height: 32px;
  background-color: #8c8c8c;
  border-radius: 4px;
}
.yc-common-sidebar-header-name {
  display: inline-block;
  position: absolute;
  margin-left: 48px;
  -webkit-box-flex: 1;
  -ms-flex: 1 1;
  flex: 1 1;
  width: 142px;
  height: 32px;
  line-height: 32px;
  font-size: 16px;
  color: var(--color-text1-3, #575757);
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}
.yc-common-sidebar-main {
  height: 100%;
  overflow-x: hidden;
  overflow-y: hidden;
  margin-top: 48px;
  -webkit-box-flex: 1;
  -ms-flex: 1 1;
  flex: 1 1;
  position: relative;
}
.yc-common-sidebar-main-scroll {
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(30%, var(--color-fill1-2, #f7f7f7)), to(hsla(0, 0%, 100%, 0))), -webkit-gradient(linear, left top, left bottom, from(hsla(0, 0%, 100%, 0)), color-stop(70%, var(--color-fill1-2, #f7f7f7))) 0 100%, radial-gradient(farthest-side at 50% 0, rgba(0, 0, 0, 0.2), transparent), radial-gradient(farthest-side at 50% 100%, rgba(0, 0, 0, 0.2), transparent) 0 100%;
  background: -o-linear-gradient(var(--color-fill1-2, #f7f7f7) 30%, hsla(0, 0%, 100%, 0)), -o-linear-gradient(hsla(0, 0%, 100%, 0), var(--color-fill1-2, #f7f7f7) 70%) 0 100%, -o-radial-gradient(50% 0, farthest-side, rgba(0, 0, 0, 0.2), transparent), -o-radial-gradient(50% 100%, farthest-side, rgba(0, 0, 0, 0.2), transparent) 0 100%;
  background: linear-gradient(var(--color-fill1-2, #f7f7f7) 30%, hsla(0, 0%, 100%, 0)), linear-gradient(hsla(0, 0%, 100%, 0), var(--color-fill1-2, #f7f7f7) 70%) 0 100%, radial-gradient(farthest-side at 50% 0, rgba(0, 0, 0, 0.2), transparent), radial-gradient(farthest-side at 50% 100%, rgba(0, 0, 0, 0.2), transparent) 0 100%;
  background-repeat: no-repeat;
  background-size: 100% 40px,100% 40px,100% 14px,100% 14px;
  background-attachment: local,local,scroll,scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.yc-common-sidebar-main-scroll::-webkit-scrollbar {
  display: none;
}
.yc-common-sidebar-main-item {
  position: relative;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  width: 188px;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: width 0.3s ease-in-out;
  -o-transition: width 0.3s ease-in-out;
  transition: width 0.3s ease-in-out;
  cursor: pointer;
  margin: 16px 0;
}
.yc-common-sidebar-main-item,
.yc-common-sidebar-main-item-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  height: 32px;
}
.yc-common-sidebar-main-item-icon {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: 0;
  width: 32px;
  color: var(--color-text1-2, #6e6e6e);
  -webkit-transition: color 0.1s ease;
  -o-transition: color 0.1s ease;
  transition: color 0.1s ease;
}
.yc-common-sidebar-main-item-icon .next-icon,
.yc-common-sidebar-main-item-icon .project-icon,
.yc-common-sidebar-main-item-icon .yx-icon {
  margin: auto;
}
.yc-common-sidebar-main-item-icon i {
  width: 20px;
  height: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.yc-common-sidebar-main-item-icon i svg {
  width: 20px;
  height: 20px;
}
.yc-common-sidebar-main-item-jsx,
.yc-common-sidebar-main-item-name {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1 1;
  flex: 1 1;
  line-height: 32px;
  font-size: 14px;
  color: var(--color-text1-3, #575757);
  overflow: hidden;
  -webkit-transition: all 0.1s ease;
  -o-transition: all 0.1s ease;
  transition: all 0.1s ease;
  margin-left: 48px;
}
.yc-common-sidebar-main-item-jsx:hover,
.yc-common-sidebar-main-item-name:hover {
  font-weight: 500;
}
.yc-common-sidebar-main-item-jsx {
  margin-left: 6px;
}
.yc-common-sidebar-main-item + .is-group-item .yc-common-sidebar-main-item-jsx {
  margin-left: 46px;
}
.yc-common-sidebar-main-item-group-arrow {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  color: var(--color-fill1-6, #8b8b8b);
}
.yc-common-sidebar-main-item-group-arrow:hover {
  color: var(--color-brand1-6, #1b9aee);
}
.yc-common-sidebar-main-item-tag {
  display: none;
}
.yc-common-sidebar-main-item.is-group-parent {
  cursor: pointer;
}
.yc-common-sidebar-main-item.is-group-parent .yc-common-sidebar-main-item-jsx {
  color: var(--color-text1-2, #6e6e6e);
}
.yc-common-sidebar-main .is-group-parent-item-jsx {
  margin-left: 48px;
}
.yc-common-sidebar-main-group:not(:first-child) {
  margin: 16px 0;
}
.yc-common-sidebar-main-group:first-child {
  margin-top: 16px;
}
.yc-common-sidebar-main .is-no-cursor {
  cursor: default;
}
.yc-common-sidebar-main .is-no-cursor:hover {
  background-color: initial;
}
.yc-common-sidebar-main .is-no-cursor:hover .yc-common-sidebar-extra-item-name,
.yc-common-sidebar-main .is-no-cursor:hover .yc-common-sidebar-main-item-name {
  font-weight: 400;
}
.yc-common-sidebar-custom-split {
  margin: 16px 0;
  border-top: 1px solid var(--color-line1-1, #e8e8e8);
}
.yc-common-sidebar-extra {
  padding: 4px 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow-x: hidden;
}
.yc-common-sidebar-extra-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  overflow: hidden;
  position: relative;
  height: 32px;
  width: 188px;
  cursor: pointer;
  margin-bottom: 16px;
  border-radius: 4px;
}
.yc-common-sidebar-extra-item:nth-last-child(2) {
  position: relative;
  margin-bottom: unset;
  width: calc(100% - 40px);
}
.yc-common-sidebar-extra-item:nth-last-child(2) > .fold-icon {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  position: relative;
  left: 40px;
}
.yc-common-sidebar-extra-item:nth-last-child(2) > .fold-icon i {
  left: 6px;
  top: 6px;
}
.yc-common-sidebar-extra-item:nth-last-child(2) > .fold-icon:hover {
  background-color: var(--color-fill1-3, #e8e8e8);
}
.yc-common-sidebar-extra-item-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: 0;
  width: 32px;
  height: 32px;
  color: var(--color-text1-2, #6e6e6e);
  -webkit-transition: color 0.1s ease;
  -o-transition: color 0.1s ease;
  transition: color 0.1s ease;
}
.yc-common-sidebar-extra-item-icon .next-icon,
.yc-common-sidebar-extra-item-icon .project-icon,
.yc-common-sidebar-extra-item-icon .yx-icon {
  margin: auto;
}
.yc-common-sidebar-extra-item-name {
  display: inline-block;
  -webkit-box-flex: 1;
  -ms-flex: 1 1;
  flex: 1 1;
  font-size: 14px;
  color: var(--color-text1-3, #575757);
  overflow: hidden;
  -webkit-transition: all 0.1s ease;
  -o-transition: all 0.1s ease;
  transition: all 0.1s ease;
  line-height: 32px;
  margin-left: 48px;
}
.yc-common-sidebar-extra-item-name:hover {
  font-weight: 500;
}
.yc-common-sidebar-extra-item-name-balloon span {
  color: #fff !important;
}
.yc-common-sidebar-extra-item-right {
  line-height: 32px;
  font-size: 14px;
  color: var(--color-fill1-6, #8b8b8b);
  display: inline-block;
  position: absolute;
  top: 0;
  height: 32px;
  left: 160px;
  width: 32px;
}
.yc-common-sidebar-extra > .fold-icon {
  width: 32px;
  height: 0;
  position: relative;
  right: 0;
  top: -32px;
}
.yc-common-sidebar-extra > .fold-icon .yc-common-sidebar-extra-item-right {
  border-radius: 4px;
  top: 0;
  left: 156px;
  width: 32px;
  height: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: var(--color-text1-2, #6e6e6e);
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.yc-common-sidebar-extra > .fold-icon .yc-common-sidebar-extra-item-right:hover {
  cursor: pointer;
  background-color: var(--color-fill1-3, #e8e8e8);
}
.yc-common-sidebar-extra-item,
.yc-common-sidebar-extra-item-is-last,
.yc-common-sidebar-main-item {
  -webkit-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}
.yc-common-sidebar-extra-item-is-last:hover,
.yc-common-sidebar-extra-item:hover,
.yc-common-sidebar-main-item:hover {
  background-color: var(--color-fill1-3, #e8e8e8);
}
.yc-common-sidebar-extra-item-is-last[data-active=true],
.yc-common-sidebar-extra-item[data-active=true],
.yc-common-sidebar-main-item[data-active=true] {
  background-color: var(--color-brand1-2, #c5e8ff);
}
.yc-common-sidebar-extra-item-is-last[data-active=true] > .yc-common-sidebar-extra-item-icon,
.yc-common-sidebar-extra-item-is-last[data-active=true] > .yc-common-sidebar-main-item-icon,
.yc-common-sidebar-extra-item[data-active=true] > .yc-common-sidebar-extra-item-icon,
.yc-common-sidebar-extra-item[data-active=true] > .yc-common-sidebar-main-item-icon,
.yc-common-sidebar-main-item[data-active=true] > .yc-common-sidebar-extra-item-icon,
.yc-common-sidebar-main-item[data-active=true] > .yc-common-sidebar-main-item-icon {
  color: var(--color-brand1-6, #1b9aee);
}
.yc-common-sidebar-extra-item-is-last[data-active=true] .yc-common-sidebar-extra-item-name,
.yc-common-sidebar-extra-item-is-last[data-active=true] .yc-common-sidebar-main-item-name,
.yc-common-sidebar-extra-item[data-active=true] .yc-common-sidebar-extra-item-name,
.yc-common-sidebar-extra-item[data-active=true] .yc-common-sidebar-main-item-name,
.yc-common-sidebar-main-item[data-active=true] .yc-common-sidebar-extra-item-name,
.yc-common-sidebar-main-item[data-active=true] .yc-common-sidebar-main-item-name {
  font-weight: 500;
}
.yc-common-sidebar-extra-item-is-last.is-breathe:after,
.yc-common-sidebar-extra-item.is-breathe:after,
.yc-common-sidebar-main-item.is-breathe:after {
  position: absolute;
  content: "";
  border: 2px solid #6e74e0;
  border-radius: 4px;
  height: 32px;
  width: 100%;
  -webkit-box-shadow: 0 2px 20px 0 #d2d4fb;
  box-shadow: 0 2px 20px 0 #d2d4fb;
}
.yc-common-sidebar-extra-item-is-last {
  overflow: visible;
}
.yc-common-sidebar-extra-item-fold {
  margin-bottom: unset;
}
.yc-common-sidebar-extra-item-no-permission:hover {
  cursor: default;
  background-color: var(--color-fill1-2, #f7f7f7);
}
.yc-common-sidebar-wrap {
  width: 64px;
  height: 100%;
}
.yc-common-sidebar-wrap[data-stick=true] {
  width: 221px;
}
.yc-common-sidebar-wrap.type-lite {
  width: 64px;
}
.yc-common-sidebar-wrap.type-lite .yc-common-sidebar-extra-item,
.yc-common-sidebar-wrap[data-stick=false] .yc-common-sidebar-extra-item {
  overflow: visible;
}
.yc-common-sidebar-wrap.type-lite .fold-icon,
.yc-common-sidebar-wrap[data-stick=false] .fold-icon {
  display: none;
}
.yc-common-sidebar-wrap[data-stick=false].type-common .yc-common-sidebar[data-mini=true] {
  padding: 0 0 8px;
}
.yc-common-sidebar-wrap[data-stick=false].type-common .yc-common-sidebar-extra-item,
.yc-common-sidebar-wrap[data-stick=false].type-common .yc-common-sidebar-header,
.yc-common-sidebar-wrap[data-stick=false].type-common .yc-common-sidebar-main-item {
  margin-left: 16px;
  margin-right: 16px;
}
.yc-common-sidebar-wrap[data-stick=false].type-common .nav-item-a {
  margin-left: 16px;
  margin-right: 16px;
  display: block;
}
.yc-common-sidebar-wrap[data-stick=false].type-common .nav-item-a .yc-common-sidebar-extra-item,
.yc-common-sidebar-wrap[data-stick=false].type-common .nav-item-a .yc-common-sidebar-main-item {
  margin-left: 0;
  margin-right: 0;
}
.yc-common-sidebar-wrap.type-lite .yc-common-sidebar-count-badge {
  -webkit-transform: scale(0.8);
  -ms-transform: scale(0.8);
  transform: scale(0.8);
}
.yc-common-sidebar-wrap.type-lite .yc-common-sidebar-count-badge-one-digit {
  left: 38px;
}
.yc-common-sidebar-wrap.type-lite .yc-common-sidebar-count-badge-two-digit {
  left: 37px;
}
.yc-common-sidebar-wrap.type-lite .yc-common-sidebar-count-badge-three-digit {
  left: 36px;
}
.yc-common-sidebar-wrap.type-lite .yc-common-sidebar-dot-badge {
  left: 38px;
}
.yc-common-sidebar-wrap-hide {
  display: none !important;
}
.yc-common-sidebar[data-mini=false] {
  -webkit-box-shadow: rgba(0, 0, 0, 0.12) 0 15px 32px;
  box-shadow: 0 15px 32px rgba(0, 0, 0, 0.12);
  -webkit-transition-duration: 0.3s;
  -o-transition-duration: 0.3s;
  transition-duration: 0.3s;
}
.yc-common-sidebar[data-mini=false] .yc-common-sidebar-header {
  -webkit-transition: width 0.3s ease-in-out;
  -o-transition: width 0.3s ease-in-out;
  transition: width 0.3s ease-in-out;
}
.yc-common-sidebar[data-mini=false] .yc-common-sidebar-extra-item,
.yc-common-sidebar[data-mini=false] .yc-common-sidebar-main-item {
  -webkit-transition: all 0.1s ease-in-out;
  -o-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}
.yc-common-sidebar[data-mini=false] .yc-common-sidebar-main-custom-item > div {
  width: 100%;
}
.yc-common-sidebar[data-mini=false] .hide-when-stick {
  display: none;
}
.yc-common-sidebar[data-mini=true] {
  width: 64px;
  -webkit-transition-property: width;
  -o-transition-property: width;
  transition-property: width;
  -webkit-transition-duration: 0.15s;
  -o-transition-duration: 0.15s;
  transition-duration: 0.15s;
  -webkit-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
  padding: 0 16px 8px;
  border-right: 0;
  -webkit-box-shadow: inset -1px 0 0 0 var(--color-line1-1, #e8e8e8);
  box-shadow: inset -1px 0 0 0 var(--color-line1-1, #e8e8e8);
}
.yc-common-sidebar[data-mini=true] .yc-common-sidebar-extra-item,
.yc-common-sidebar[data-mini=true] .yc-common-sidebar-header,
.yc-common-sidebar[data-mini=true] .yc-common-sidebar-main-item {
  width: 32px;
}
.yc-common-sidebar[data-mini=true] .yc-common-sidebar-header {
  -webkit-transition: width 0.15s ease-in-out;
  -o-transition: width 0.15s ease-in-out;
  transition: width 0.15s ease-in-out;
}
.yc-common-sidebar[data-mini=true] .yc-common-sidebar-extra-item,
.yc-common-sidebar[data-mini=true] .yc-common-sidebar-main-item {
  -webkit-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}
.yc-common-sidebar[data-mini=true] .yc-common-sidebar-custom-split {
  margin: 16px 0;
  border-top: 1px solid var(--color-line1-1, #e8e8e8);
}
.yc-common-sidebar[data-mini=true] .is-group-item {
  display: none;
}
.yc-common-sidebar[data-stick=true] {
  -webkit-box-shadow: unset;
  box-shadow: unset;
}
.yc-common-sidebar {
  background-color: var(--color-fill1-2, #f7f7f7);
  -webkit-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
  -webkit-transition-property: width, -webkit-box-shadow;
  transition-property: width, -webkit-box-shadow;
  -o-transition-property: box-shadow, width;
  transition-property: box-shadow, width;
  transition-property: box-shadow, width, -webkit-box-shadow;
}
.yc-common-sidebar .special {
  margin-bottom: 32px;
}
.yc-common-sidebar .special:last-child {
  margin-bottom: unset;
}
.yc-common-sidebar .nav-item-a > .special {
  margin-bottom: 32px !important;
}
.yc-common-sidebar .nav-item-a:last-child > .special {
  margin-bottom: unset !important;
}
.yc-common-sidebar .yc-common-sidebar-special-line {
  width: 100%;
  border-top: 1px solid var(--color-line1-1, #e8e8e8);
  height: 1px;
  position: absolute;
  left: 0;
  right: 0;
  background-color: var(--color-line1-1, #e8e8e8);
  bottom: -16px;
}
.yc-common-sidebar .yc-common-sidebar-count-badge {
  position: absolute;
  top: -3px;
  background-color: var(--color-brand3-6, #7d8389);
  font-size: 12px;
  color: #fff;
  text-align: center;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  font-weight: 500;
  -webkit-transform: scale(0.86);
  -ms-transform: scale(0.86);
  transform: scale(0.86);
}
.yc-common-sidebar .yc-common-sidebar-count-badge-one-digit {
  border-radius: 100%;
  left: 20px;
}
.yc-common-sidebar .yc-common-sidebar-count-badge-three-digit,
.yc-common-sidebar .yc-common-sidebar-count-badge-two-digit {
  border-radius: 8px;
  padding: 0 4px;
  left: 19px;
}
.yc-common-sidebar .yc-common-sidebar-dot-badge {
  position: absolute;
  top: 0;
  right: 0;
  text-align: center;
  width: 12px;
  height: 12px;
  font-weight: 500;
  border-radius: 100%;
  background-color: var(--color-notice-5, #006ad4);
  border: 2px solid var(--color-white, #fff);
}
.yc-common-sidebar.type-lite {
  width: 64px;
  padding: 0 0 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}
.yc-common-sidebar.type-lite[data-mini=false] {
  -webkit-box-shadow: initial;
  box-shadow: none;
  -webkit-transition-duration: initial;
  -o-transition-duration: initial;
  transition-duration: 0s;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-header {
  width: 32px;
  margin-left: 16px;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  height: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  overflow: visible;
  color: #7d8089;
  width: 100%;
  padding-bottom: 9px;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item-name {
  margin-left: 0;
  line-height: 18px;
  font-size: 12px;
  text-align: center;
  color: var(--color-text1-2, var(--color-text1-2, #6e6e6e));
  position: static;
  width: 49px;
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item-icon {
  height: 28px;
  line-height: 24px;
  position: static;
  margin: 0;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item-tag {
  display: block;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item:hover,
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item[data-active=true] {
  background-color: initial;
  color: var(--color-brand1-6, #1b9aee);
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item:hover .yc-common-sidebar-main-item-name,
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item[data-active=true] .yc-common-sidebar-main-item-name {
  color: var(--color-text1-4, #292929);
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item:hover .yc-common-sidebar-main-item-icon,
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item[data-active=true] .yc-common-sidebar-main-item-icon {
  color: var(--color-brand1-6, #1b9aee);
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item:link,
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item:visited {
  color: #7d8089;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item + .is-group-item {
  display: none;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-main-item + .is-group-parent {
  margin-bottom: 0;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: auto;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  overflow: visible;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item-icon {
  position: static;
  width: inherit;
  height: 32px;
  line-height: 32px;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item-name {
  margin-left: 0;
  line-height: 18px;
  font-size: 12px;
  text-align: center;
  color: var(--color-text1-2, var(--color-text1-2, #6e6e6e));
  position: static;
  width: auto;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item-right {
  display: none;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item:hover,
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item[data-active=true] {
  background-color: initial;
}
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item:hover .yc-common-sidebar-extra-item-icon,
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item[data-active=true] .yc-common-sidebar-extra-item-icon {
  color: var(--color-brand1-6, #1b9aee);
}
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item:hover .yc-common-sidebar-extra-item-name,
.yc-common-sidebar.type-lite .yc-common-sidebar-extra-item[data-active=true] .yc-common-sidebar-extra-item-name {
  color: var(--color-text1-4, #292929);
}
.yc-common-sidebar.type-lite .yc-common-sidebar-header-name,
.yc-common-sidebar.type-lite .yc-common-sidebar-sticker {
  display: none;
}
body {
  padding-top: 0;
}
#yc-common-sidebar-wrapper {
  top: 50px;
  height: calc(100vh - 50px);
}
body.withAnnouncement {
  padding-top: 36px;
}
body.withAnnouncement #yc-common-sidebar-wrapper {
  top: 77px;
  height: calc(100vh - 77px);
}
body.withAnnouncement .project-layout-layout.full-page {
  padding-top: calc(var(--layout-padding-top-and-bottom, 16px) + var(--layout-nav-height, 50px) + 27px) !important;
}
body.withAnnouncement .project-layout-layout.full-page.no-padding {
  padding-top: calc(var(--layout-nav-height, 50px) + 27px) !important;
}
body.withAnnouncement .project-cloud-dev-frame {
  padding-top: 60px;
}
#container {
  padding-top: 50px;
  padding-left: 64px;
}
.el-icon-setting{cursor: pointer;}

.project-star:hover{color: #16b777;}
.project-star{cursor: pointer;font-size: 14px;line-height: 30px;}
.project-star i{font-size: 20px;}