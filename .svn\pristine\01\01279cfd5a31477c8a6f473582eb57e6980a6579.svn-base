<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的票夹</title>
	<style>
		.layui-badge{left: -1px!important;}
		.layui-table-cell{padding: 0 6px;}
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>发票管理</h5>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">发票号</span>
						 	<input class="form-control input-sm" name="invoiceNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">开票单位</span>
						 	<input class="form-control input-sm" name="saleCompany">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">报销状态</span>
						 	<select class="form-control input-sm" name="bxState">
						 		<option value="">--</option>
						 		<option value="0" data-class="label label-info">待报销</option>
						 		<option value="1" data-class="label label-success">已报销</option>
						 	</select>
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="InvoiceMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="InvoiceMgr.add()">+ 导入发票</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="InvoiceMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="InvoiceMgr.edit">查看</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var InvoiceMgr={
			init:function(){
				$("#dataForm").initTable({
					mars:'BxDao.bxInvoiceList',
					id:'InvoiceMgrTable',
					height:'full-100',
					autoSort:false,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						title: '操作',
						align:'center',
						width:80,
						templet:function(row){
						    return renderTpl('bar',row);
						}
					},{
						 field:'BX_STATE',
						 title:'报销状态',
						 width:80,
						 templet:function(row){
							 return getText(row.BX_STATE,'bxState');
						 }
					 },{
					    field: 'INVOICE_NO',
						title: '发票号',
						align:'left',
						width:160
					},{
					    field: 'TOTAL_AMOUNT',
						title: '含税金额',
						align:'left',
						width:90
					},{
						field:'AMOUNT_IN_FIGUERS',
						title:'不含税',
						width:90
					},{
						field:'TOTAL_TAX',
						title:'税额',
						width:90
					},{
						field:'TAX_RATE',
						title:'税率',
						width:90,
						templet:function(row){
							if(row.TAX_RATE<1){
								return row.TAX_RATE*100+"%";
							}
							return row.TAX_RATE;
						}
					},{
					    field: 'INVOICE_DATE',
						title: '开票日期',
						align:'center',
						width:110
					},{
					    field: 'INVOICE_TYPE',
						title: '开票类型',
						width:110
					},{
					    field: 'SALE_COMPANY',
						title: '开票方',
						width:120
					},{
					    field: 'BUY_COMPANY',
						title: '购买方',
						width:120
					},{
					    field: 'KP_REMARK',
						title: '开票备注',
						minWidth:120
					},{
					    field: 'FILE_NAME',
						title: '发票文件',
						width:160
					},{
					    field: 'CREATE_TIME',
						title: '导入时间',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'CREATOR',
						title: '导入人',
						align:'center',
						width:70,
						templet:function(row){
							return getUserName(row.CREATOR);
						}
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataForm").queryData({id:'InvoiceMgrTable',jumpOne:true});
			},
			edit:function(data){
				var  invoiceId = data['INVOICE_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/flow/bx/invoice/invoice-edit.jsp',title:'查看发票',data:{invoiceId:invoiceId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,offset:'20px',area:['500px','400px'],url:'${ctxPath}/pages/flow/bx/invoice/invoice-import.jsp',title:'导入发票',btn:['关闭']});
			}
		}
		
		function reloadInvoiceList(){
			InvoiceMgr.query();
		}
		
		$(function(){
			$("#dataForm").render({success:function(){
				InvoiceMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>