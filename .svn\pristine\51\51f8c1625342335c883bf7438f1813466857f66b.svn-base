package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.db.impl.JSONMapperImpl;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.utils.WeekUtils;

public class WeeklyNoticeService extends AppBaseService implements Job{

	private static class Holder{
		private static WeeklyNoticeService service=new WeeklyNoticeService();
	}
	public static WeeklyNoticeService getService(){
		return Holder.service;
	}
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		 this.run(WeekUtils.getWeekNo());
		 
	}
	public  void run(int weekNo){
		//待办提醒
		getLogger().info("执行周报提醒任务....");
		try {
			int year=WeekUtils.getYear();
			List<JSONObject> list = this.getQuery().queryForList("SELECT USER_ID FROM "+Constants.DS_MAIN_NAME+".easi_user WHERE USER_ID NOT IN ( SELECT t1.USER_ID FROM "+Constants.DS_MAIN_NAME+".easi_user t1, yq_weekly t2 WHERE t1.USER_ID = t2.creator AND t2.week_no = ? and t2.year = ? ) AND STATE = 0 ",new Object[]{weekNo,year},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(creator);
					model.setSender(creator);
					model.setTitle(WeekUtils.getWeekTitle(year,weekNo)+"\n");
					WxMsgService.getService().sendWeeklyNoticeMsg(model);
				}
			}
		} catch (SQLException e2) {
			e2.printStackTrace();
			this.getLogger().error(e2);
		}
	}

}
