package com.yunqu.work.servlet.work;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.utils.TaskUtils;

@WebServlet("/servlet/task/excel")
public class TaskExcelServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	private static JSONObject taskNames=new JSONObject();
	static{
		taskNames.put("10", "待办");
		taskNames.put("11", "已退回");
		taskNames.put("12", "已暂停");
		taskNames.put("13", "已取消");
		taskNames.put("20", "进行中");
		taskNames.put("30", "已完成");
		taskNames.put("40", "已验收");
		taskNames.put("42", "验收不通过");
	}
	
	public JSONObject getSpanJson(int r,int c,String value,String extendStr) {
		JSONObject json = getSpanJson(r, c, value);
		String[] strs = extendStr.split(",");
		for(int i=0;i<strs.length;i++) {
			String str = strs[i];
			String[] config = str.split(":");
			json.put(config[0], config[1]);
		}
		return json;
	}
	
	public JSONObject getSpanJson(int r,int c,String value) {
		JSONObject span = new JSONObject();
		span.put("r", r);
		span.put("c", c);
		span.put("bg", "#f9f9f9");
        JSONObject vObject = new JSONObject();
        vObject.put("v", value);
        vObject.put("m", value);
        JSONObject ctObject = new JSONObject();
        ctObject.put("fa", "General");
        ctObject.put("t", "n");
        vObject.put("ct", ctObject);
        span.put("v", vObject);
        return span;
	}
	
	public void actionForProjectAllTaskList(){ 
		String projectId = getPara("projectId");
		JSONObject params = getJSONObject();
		Map<String,String> taskTypes = getTaskType();
		EasySQL easySQL = new EasySQL("select t1.* from YQ_TASK t1 where 1=1");
		easySQL.append(projectId,"and t1.project_id = ?");
		TaskUtils.setTaskCondition(params, getDeptId(), easySQL);
		easySQL.append("order by t1.CREATE_TIME desc");
		
		if(StringUtils.isBlank(params.getString("planDate"))&&StringUtils.isBlank(params.getString("projectId"))) {
			easySQL.append("limit 1000");
		}
		
		JSONArray result = new JSONArray();
		try {
			List<JSONObject> list = this.getQuery().queryForList(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
			JSONArray array = new JSONArray();
			 array.add(getSpanJson(0,0,"任务名称","bl:1,fc:#990000,bg:#f5f5f5"));
			 array.add(getSpanJson(0,1,"模块名称","bl:1,fc:#990000,bg:#f5f5f5"));
			 array.add(getSpanJson(0,2,"类型","bl:1,bg:#f5f5f5"));
			 array.add(getSpanJson(0,3,"创建人","bl:1,bg:#f5f5f5"));
			 array.add(getSpanJson(0,4,"创建人部门","bl:1,bg:#f5f5f5"));
			 array.add(getSpanJson(0,5,"备注","bl:1,bg:#f5f5f5"));
			 array.add(getSpanJson(0,6,"优先级","bl:1"));
			 array.add(getSpanJson(0,7,"任务状态","bl:1"));
			 array.add(getSpanJson(0,8,"负责人","bl:1"));
			 array.add(getSpanJson(0,9,"负责人部门","bl:1"));
			 array.add(getSpanJson(0,10,"创建时间","bl:1"));
			 array.add(getSpanJson(0,11,"创建日期","bl:1"));
			 array.add(getSpanJson(0,12,"任务截止日期","bl:1"));
			 array.add(getSpanJson(0,13,"延期次数","bl:1"));
			 array.add(getSpanJson(0,14,"计划工时/小时","bl:1"));
			 array.add(getSpanJson(0,15,"实际工时/小时","bl:1"));
			 array.add(getSpanJson(0,16,"计划名称","bl:1"));
			 if(StringUtils.isBlank(projectId)) {
				 array.add(getSpanJson(0,17,"项目名称","bl:1"));
			 }
			
			int r = 1;
			for(JSONObject data:list) {
				String desc = Jsoup.clean(data.getString("TASK_DESC"), Whitelist.simpleText());
				array.add(getSpanJson(r,0,data.getString("TASK_NAME")));
				array.add(getSpanJson(r,1,data.getString("MODULE_NAME")));
				array.add(getSpanJson(r,2,taskTypes.get(data.getString("TASK_TYPE_ID"))));
				array.add(getSpanJson(r,3,data.getString("CREATE_NAME")));
				array.add(getSpanJson(r,4,data.getString("DEPT_NAME")));
		        array.add(getSpanJson(r,5,desc));
		        array.add(getSpanJson(r,6,data.getString("TASK_LEVEL")));
		        array.add(getSpanJson(r,7,taskNames.getString(data.getString("TASK_STATE"))));
		        array.add(getSpanJson(r,8,data.getString("ASSIGN_USER_NAME")));
		        array.add(getSpanJson(r,9,data.getString("ASSIGN_DEPT_NAME")));
		        array.add(getSpanJson(r,10,data.getString("CREATE_TIME")));
		        array.add(getSpanJson(r,11,data.getString("DATE_ID")));
		        array.add(getSpanJson(r,12,data.getString("DEADLINE_AT")));
		        array.add(getSpanJson(r,13,data.getString("DELAY_COUNT")));
		        array.add(getSpanJson(r,14,data.getString("PLAN_WORK_HOUR")));
		        array.add(getSpanJson(r,15,data.getString("WORK_HOUR")));
		        array.add(getSpanJson(r,16,data.getString("GROUP_NAME")));
		        if(StringUtils.isBlank(projectId)) {
		        	array.add(getSpanJson(r,17,data.getString("PROJ_NAME")));
		        }
		        r++;
			}
			JSONObject row1 = new JSONObject();
			row1.put("name","TaskData");
			row1.put("index",0);
			row1.put("order",0);
			row1.put("status",1);
			row1.put("celldata",array);
			result.add(row1);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		renderText(result.toJSONString());
	}
	
	private Map<String,String> getTaskType() {
		Map<String,String> map=new HashMap<String,String>();
		try {
			List<EasyRow> list=this.getQuery().queryForList("select task_type_id,task_type_name FROM yq_task_type");
			if(list!=null){
				for(EasyRow row:list){
					map.put(row.getColumnValue("task_type_id"),row.getColumnValue("task_type_name"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return map;
	}
}
