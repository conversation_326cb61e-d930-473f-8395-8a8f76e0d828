<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>数电发票XML管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataForm">
			<input name="type" type="hidden" value="9">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          	     <div class="form-group">
						 <div class="input-group input-group-sm">
							 <span class="input-group-addon">开票日期</span>	
							 <input type="text" name="kpBeginDate" data-mars="CommonDao.threeMonthBefore" style="width: 90px;" data-laydate="{type:'date'}" class="form-control input-sm">
							 <input type="text" name="kpEndDate" style="width: 90px;" data-laydate="{type:'date'}" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <span class="input-group-addon">上传日期</span>	
							 <input type="text" name="beginDate" style="width: 90px;" data-laydate="{type:'date'}" class="form-control input-sm">
							 <input type="text" name="endDate" style="width: 90px;" data-laydate="{type:'date'}" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm" style="width: 170px;">
					 		<span class="input-group-addon">发票号</span>
						 	<input class="form-control input-sm" name="invoiceNo">
						 </div>
	          	     </div>
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 180px;">
					 		<span class="input-group-addon">数电发票类型</span>
						 	<select class="form-control input-sm" name="invoiceType" onchange="InvoiceMgr.query();">
						 		<option value="">--</option>
						 		<option value="1001" data-class="label label-info">普通发票</option>
						 		<option value="2001" data-class="label label-success">增值税专用发票</option>
						 		<option value="3001" data-class="label label-info">电子发票(铁路电子客票)</option>
						 		<option value="3002" data-class="label label-success">电子发票(航空运输电子客票行程单)</option>
						 	</select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">关联报销人</span>
						 	<select class="form-control input-sm" name="relateBxPerson" onchange="InvoiceMgr.query();">
						 		<option value="">--</option>
						 		<option value="1" data-class="label label-info">是</option>
						 		<option value="0" data-class="label label-success">否</option>
						 	</select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">开票单位</span>
						 	<input class="form-control input-sm" name="saleCompany">
						 </div>
						 <div class="input-group input-group-sm" style="width: 150px;">
					 		<span class="input-group-addon">报销人</span>
						 	<input class="form-control input-sm" type="hidden" name="belongUserId">
						 	<input class="form-control input-sm" onclick="singleUser(this);" data-fn="InvoiceMgr.query">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="InvoiceMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="InvoiceMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="btnBar">
			<button type="button" class="btn btn-sm btn-success mr-10" lay-event="InvoiceMgr.importData"><i class="fa fa-paper-plane"></i> 导入数电发票XML</button>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var InvoiceMgr={
			init:function(){
				$("#dataForm").initTable({
					mars:'BxInvoiceDao.xmlInvoiceQuery',
					id:'InvoiceMgrTable',
					height:'full-100',
					limit:30,
					toolbar:'#btnBar',
					totalRow:true,
					autoSort:false,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 type:'checkbox'
					 },{
					    field: 'XML_UPLOAD_TIME',
						title: '导入时间',
						align:'left',
						width:140
					},{
						 field:'XML_FILE_ID',
						 title:'XML发票',
						 width:80,
						 templet:function(row){
							var xmlFileId = row.XML_FILE_ID;
							return "<a style='color:#1e9fff;' href='/yq-work/fileview/"+xmlFileId+"' target='_blank'>下载</a>";								 
						 }
					 },{
						 field:'BELONG_USER_NAME',
						 title:'关联报销人',
						 width:80,
						 templet:function(row){
							 return '<a style="color:#1e9fff;"  href="javascript:void(0);" onclick="InvoiceMgr.edit(\''+row.BX_INVOICE_ID+'\');">'+row['BELONG_USER_NAME']+'</a>';
						 }
					 },{
					    field: 'INVOICE_NO',
						title: '发票号',
						align:'left',
						width:160,
						totalRowText:'统计'
					},{
					    field: 'TOTAL_AMOUNT',
						title: '含税金额',
						align:'left',
						width:90,
						totalRow:true
					},{
						field:'AMOUNT_IN_FIGUERS',
						title:'不含税',
						width:90,
						totalRow:true
					},{
						field:'TOTAL_TAX',
						title:'税额',
						width:90,
						totalRow:true
					},{
						field:'TAX_RATE',
						title:'税率',
						width:90,
						templet:function(row){
							if(row.TAX_RATE<1){
								return row.TAX_RATE*100+"%";
							}
							return row.TAX_RATE;
						}
					},{
					    field: 'INVOICE_DATE',
						title: '开票日期',
						align:'center',
						width:110
					},{
					    field: 'INVOICE_TYPE',
						title: '开票类型',
						width:110
					},{
					    field: 'SALE_COMPANY',
						title: '开票方',
						width:120
					},{
					    field: 'BUY_COMPANY',
						title: '购买方',
						width:120
					},{
					    field: 'KP_REMARK',
						title: '开票备注',
						minWidth:120
					},{
					    field: 'FILE_NAME',
						title: '发票文件',
						width:160
					},{
					    field: 'CREATE_NAME',
						title: '导入人',
						align:'center',
						width:70
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataForm").queryData({id:'InvoiceMgrTable',jumpOne:true});
			},
			edit:function(invoiceId){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/flow/bx/invoice/invoice-edit.jsp',title:'查看发票',data:{invoiceId:invoiceId,op:'detail'}});
			},
			importData:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,offset:'20px',area:['500px','400px'],url:'${ctxPath}/pages/flow/bx/invoice/invoice-xml-import.jsp',title:'导入XML发票',btn:['关闭']});
			}
		}
		
		function reloadInvoiceList(){
			InvoiceMgr.query();
		}
		
		$(function(){
			$("#dataForm").render({success:function(){
				InvoiceMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>