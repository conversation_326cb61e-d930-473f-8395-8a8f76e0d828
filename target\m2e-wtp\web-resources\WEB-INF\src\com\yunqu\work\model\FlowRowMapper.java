package com.yunqu.work.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;


public class FlowRowMapper implements EasyRowMapper<FlowModel> {

	@SuppressWarnings("unchecked")
	@Override
	public FlowModel mapRow(ResultSet rs, int rowNum) {
		FlowModel vo = new FlowModel();
		try {
			vo.setFlowName(rs.getString("FLOW_NAME"));
			vo.setFlowTitle(rs.getString("FLOW_TITLE"));
			vo.setApplyUrl(rs.getString("APPLY_URL"));
			vo.setDetailUrl(rs.getString("DETAIL_URL"));
			vo.setFlowCode(rs.getString("FLOW_CODE"));
			vo.setFaqUrl(rs.getString("FAQ_URL"));
			vo.setFormId(rs.getString("FORM_ID"));
			vo.setItemTableName(rs.getString("ITEM_TABLE"));
			vo.setApplyNoRule(rs.getString("APPLY_NO_RULE"));
			vo.setSourceType(rs.getInt("SOURCE_TYPE"));
			vo.setFlowRemark(rs.getString("FLOW_REMARK"));
			vo.setJsCode(rs.getString("JS_CODE"));
			vo.setStartNodeName(rs.getString("START_NODE_NAME"));
			vo.setTableName(rs.getString("BUSI_TABLE"));
			vo.setXlCheckSkip(rs.getInt("XL_CHECK_SKIP"));
			vo.setRepeatCheckSkip(rs.getInt("REPEAT_CHECK_SKIP"));
			vo.setApplyCheckSkip(rs.getInt("APPLY_CHECK_SKIP"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
