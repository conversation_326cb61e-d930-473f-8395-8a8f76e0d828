package com.yunqu.work.servlet;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.FolderModel;
import com.yunqu.work.service.FlowService;
/**
 * 文档
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/folder/*")
public class FolderServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	
	public EasyResult actionForAdd(){
		JSONObject params = getJSONObject();
		FolderModel model = getModel(FolderModel.class, "folder");
		String pFolderId = params.getString("pFolderId");
		if(StringUtils.isBlank(pFolderId)){
			model.set("p_folder_id", "0");
		}else{
			model.set("p_folder_id", pFolderId);
		}
		model.setPrimaryValues(RandomKit.uuid());
		model.addCreateTime();
		model.setCreator(getUserPrincipal().getUserId());
		model.set("manager",getUserPrincipal().getUserId());
		model.set("update_time",EasyDate.getCurrentDateString());
		
		model.set("dept_id", getDeptId());
		model.set("dept_name", getDeptName());
		
		String code = FlowService.getService().getID(5);
		model.set("folder_code",code);
		
		try {
			JSONObject row = this.getQuery().queryForRow("select * from yq_folder where folder_id = ?",new Object[] {pFolderId},new JSONMapperImpl());
			if(row!=null) {
				model.set("path", row.getString("PATH")+","+code);
			}else {
				model.set("path",code);
			}
			
			model.save();
			
			String synFlag = getJsonPara("synFlag");
			if(StringUtils.notBlank(synFlag)) {
				//this.updateFolderData(code);
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void updateFolderData(String _folderId) {
		try {
			JSONObject row = this.getQuery().queryForRow("select * from yq_folder where folder_id = ?", new Object[] {_folderId}, new JSONMapperImpl());
			String folderCode = row.getString("FOLDER_CODE");
			String deptName = row.getString("DEPT_NAME");
			String deptId = row.getString("DEPT_ID");
			String shareUserIds = row.getString("SHARE_USER_IDS");
			String shareUserStrs = row.getString("SHARE_USER_STRS");
			int  folderAuth = row.getIntValue("FOLDER_AUTH");
			
			EasySQL sql = new EasySQL("select * from yq_folder where 1=1");
			sql.append(folderCode,"and find_in_set(?,path)");
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(), new JSONMapperImpl());
			
			for(JSONObject obj:list) {
				String folderId = obj.getString("FOLDER_ID");
				this.getQuery().executeUpdate("update yq_folder set folder_auth = ?,dept_id = ?,dept_name = ?,share_user_ids = ?,share_user_strs = ? where folder_id = ?",folderAuth,deptId,deptName,shareUserIds,shareUserStrs,folderId);
			}
			
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	public EasyResult actionForUpdateAllPath() {
		updateFolderPath(null,null);
		return EasyResult.ok();
	}
	
	private void updateFolderPath(String id,String source) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("select * from yq_folder where 1=1");
			sql.append(source,"and source = ?");
			
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(), new JSONMapperImpl());
			Map<String,String> map = new HashMap<String,String>();
			Map<String,String> map2 = new HashMap<String,String>();
			for(JSONObject row:list) {
				String folderId = row.getString("FOLDER_ID");
				String pFolderId = row.getString("P_FOLDER_ID");
				String folderCode = row.getString("FOLDER_CODE");
				map.put(folderId, folderCode);
				map2.put(folderId, pFolderId);
			}
			for(JSONObject row:list) {
				String folderId = row.getString("FOLDER_ID");
				if(StringUtils.notBlank(id)) {
					if(!folderId.equals(id)) {
						continue;
					}
				}
				StringBuffer path = new StringBuffer();
				getPath(folderId,map,map2,path);
				
				String _path = path.toString();
				_path = path.substring(1,_path.length());
				
				this.info(folderId+">"+_path,null);
				this.getQuery().executeUpdate("update yq_folder set path = ? where folder_id = ?", _path,folderId);
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
	}
	
	
	private void getPath(String folderId,Map<String,String> map,Map<String,String> map2,StringBuffer path) {
		String folderCode = map.get(folderId);
		if(folderCode!=null) {
			path.append(",").append(folderCode);
			
			String _folderId = map2.get(folderId);
			getPath(_folderId,map,map2,path);
		}
	}

	public EasyResult actionForUpdate(){
		JSONObject params = getJSONObject();
		FolderModel model = getModel(FolderModel.class, "folder");
		String source = model.getString("SOURCE");
		String folderId = null;
		try {
			model.set("update_time",EasyDate.getCurrentDateString());
			
			String newFolderName = params.getString("newFolderName");
			if(StringUtils.notBlank(newFolderName)) {
				newFolderName = newFolderName.trim();
				List<JSONObject> folderList = this.getQuery().queryForList("select * from yq_folder where folder_name = ? and source = ?",new Object[] {newFolderName,source}, new JSONMapperImpl());
				if(folderList==null||folderList.isEmpty()) {
					return EasyResult.fail(newFolderName+"不存在");
				}
				if(folderList.size()>1) {
					return EasyResult.fail("存在多个同名文件夹："+newFolderName+",请换个目录吧");
				}
				JSONObject folderInfo = folderList.get(0);
				model.set("P_FOLDER_ID",folderInfo.getString("FOLDER_ID"));
				folderId = model.getString("FOLDER_ID");
			}
			
			model.update();
			
			if("yqproject".equals(source)) {
				this.getQuery().executeUpdate("UPDATE yq_folder t1,yq_files t2 set t2.fk_id = t1.fk_id where t1.folder_id = t2.folder_id and t1.folder_id = ?",model.getString("FOLDER_ID"));
			}
			
			if(folderId!=null) {
				this.updateFolderPath(folderId,source);
			}
			
			String synFlag = getJsonPara("synFlag");
			if(StringUtils.notBlank(synFlag)) {
				this.updateFolderData(model.getPrimaryValue().toString());
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDel(){
		FolderModel model=getModel(FolderModel.class, "folder");
		try {
			model.set("folder_state",1);
			model.set("update_time",EasyDate.getCurrentDateString());
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForDelete(){
		FolderModel model=getModel(FolderModel.class, "folder");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	
	
	public EasyResult actionForAddLink(){
		EasyRecord  model = new EasyRecord("yq_project_link","link_id");
		model.setColumns(getJSONObject("link"));
		model.setPrimaryValues(RandomKit.uuid());
		model.set("create_time",EasyDate.getCurrentDateString());
		model.set("update_time",EasyDate.getCurrentDateString());
		model.set("creator",getUserPrincipal().getUserId());
		model.set("dept_id", getDeptId());
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateLink(){
		EasyRecord  model = new EasyRecord("yq_project_link","link_id");
		try {
			model.setColumns(getJSONObject("link"));
			model.set("update_time",EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDeleteLink(){
		EasyRecord  model = new EasyRecord("yq_project_link","link_id");
		try {
			model.setColumns(getJSONObject("link"));
			this.getQuery().deleteById(model);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
}





