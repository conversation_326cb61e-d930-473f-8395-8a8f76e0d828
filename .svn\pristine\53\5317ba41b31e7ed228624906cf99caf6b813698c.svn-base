<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>产品介绍</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
		.layui-table-cell {
			height: auto;
		}
		
		.layui-progress{margin-top: 13px;}
		
		.ibox-content{min-height: 400px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  class="form-inline" id="searchForm" data-mars="ProductDao.info">
			<input type="hidden" name="productId" value="${param.productId}"/>
			<input type="hidden" name="fkId" value="${param.productId}"/>
			<div class="ibox-content">
				<div class="layui-tab layui-tab-brief">
				  <ul class="layui-tab-title">
				    <li class="layui-this">产品介绍</li>
				    <li id="docTotal">产品文档</li>
				    <li>演示环境</li>
				  </ul>
				  <div class="layui-tab-content">
				  	    <div class="layui-tab-item layui-show layui-text" id="INTRODUCE">
							产品介绍				  	    
				  	    </div>
					    <div class="layui-tab-item layui-text">
							 <table class="layui-table">
							  <colgroup>
							    <col>
							    <col width="180">
							    <col width="120">
							  </colgroup>
							  <thead>
							    <tr>
							      <th>文件名</th>
							      <th>上传时间</th>
							      <th>上传人</th>
							    </tr> 
							  </thead>
							  <tbody data-template="template-files" data-mars="FileDao.fileListDetail">

							  </tbody>
							</table>
					    </div>
					    <div class="layui-tab-item layui-text" id="DEMO">
					  		演示环境
					    </div>
				  </div>
				</div>   
			</div>	
  	</form>
  	<script id="template-files" type="text/x-jsrender">
			{{if data.length==0}}
				<tr>
					<td colspan="3">暂无数据</td>
				</tr>
			{{/if}}
			{{for data}}
				<tr>
				  <td>{{:#index+1}}、<a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}} &nbsp;</span> <span style="color:#17a6f0">下载 | {{:FILE_SIZE}}<span></a></td>
				  <td>{{:CREATE_TIME}}</td>
				  <td>
						{{:CREATE_NAME}}
					</td>
				</tr>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		layui.use('element',function(){
			var element=layui.element;
			element.render();
		});
		$("#searchForm").render({success:function(result){
			var data = result['FileDao.fileListDetail'];
			$("#docTotal").append("("+data.total+")");
			layer.photos({photos: '#searchForm',anim: 0,shade:0,shadeClose:true,closeBtn:true}); 
		}});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>