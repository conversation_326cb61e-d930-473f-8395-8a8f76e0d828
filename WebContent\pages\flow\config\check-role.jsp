<%@ page language="java" contentType="text/html;charset=UTF-8"%> 
<%@ include file="/pages/common/global.jsp" %> 
<%@ page language="java" contentType="text/html;charset=UTF-8"%> 
<EasyTag:override name="head"> 
    <title>流程节点配置</title> 
    <style> 
        .layui-timeline-item:hover a{opacity:1;} 
        .ibox-content{min-height: calc(100vh - 100px)}; 
    </style> 
</EasyTag:override> 
<EasyTag:override name="content"> 
	        <form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm"> 
	            <div class="ibox"> 
	                <div class="ibox-title clearfix"> 
	                     <div class="form-group"> 
	                         <h5>审批角色配置</h5> 
	                         <div class="input-group input-group-sm pull-right"> 
	                             <button type="button" class="btn btn-sm btn-info" onclick="addRole()"> 新增</button> 
	                         </div> 
	                     </div> 
	                  </div>  
	                    <div class="ibox-content"> 
	                            <table id="nodeTable"></table> 
	                    </div> 
	                </div> 
	        </form> 
        	<script type="text/html" id="bar">
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="editRole">编辑</a>
  				<a class="layui-btn layui-btn-warn layui-btn-xs" lay-event="delRole">删除</a>
		   </script>
</EasyTag:override> 
<EasyTag:override name="script"> 
    <script type="text/javascript"> 
      
            $(function(){ 
                $("#dataMgrForm").render({success:function(){ 
                    initList(); 
                }}); 
            }); 
              
            function initList(){ 
                $("#dataMgrForm").initTable({ 
                    mars:'FlowConfigDao.roleConfig', 
                    id:'nodeTable', 
                    page:false, 
                    height:'full-130', 
                    cellMinWidth:100, 
                    cols: [[ 
                     { 
                        field:'ROLE_ID', 
                        title:'编号',
                        width:80
                     },
                     { 
                         field:'ROLE_NAME', 
                         title:'角色名称' 
                     },{ 
                         field:'ROLE_USER_NAME', 
                         title:'审批人', 
                         minWidth:160
                     },{ 
                        field: 'CREATE_TIME', 
                        title: '操作时间', 
                        align:'center' 
                    },{ 
                        field:'ROLE_STATE', 
                        title:'状态', 
                        width:70,
                        templet:function(row){ 
                            var state = row['ROLE_STATE']; 
                            return state=='0'?'启用':'暂停'; 
                        } 
                    },{ 
                        field:'', 
                        title:'操作', 
                        toolbar: '#bar'
                    } 
                ]],done:function(){ 
                      
                } 
             }); 
           } 
              
            function editRole(data){ 
                var id = data['ROLE_ID']; 
                popup.layerShow({id:'editRole',area:['500px','400px'],offset:'20px',url:'${ctxPath}/pages/flow/config/role-edit.jsp',title:'修改',data:{roleId:id}}); 
            } 
            
            function addRole(){ 
                popup.layerShow({id:'editRole',area:['500px','400px'],offset:'20px',url:'${ctxPath}/pages/flow/config/role-edit.jsp',title:'新增',data:{roleId:''}}); 
            } 
            
            function delRole(data){
            	layer.confirm('确定是否删除?',{icon:3,icon:'50px'},function(){
            		ajax.remoteCall("${ctxPath}/web/flow/config?action=delRole",{roleId:data['ROLE_ID']},function(result) { 
        				if(result.state == 1){
        					layer.msg(result.msg,{icon:1,time:1200},function(){
        						location.reload();
        					});
        				}else{
        					layer.alert(result.msg,{icon: 5});
        				}
        			});
            	});
            }
            
              
            function queryData(){ 
                $("#dataMgrForm").queryData({id:'nodeTable'}); 
            } 
              
    </script> 
</EasyTag:override> 
<%@ include file="/pages/common/layout-layui-auto.jsp" %> 