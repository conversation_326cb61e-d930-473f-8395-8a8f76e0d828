<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的申请</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="historyFlowForm">
			<input type="hidden" name="businessId" value="${param.businessId}"/>
			<input type="hidden" name="flowCode" value="${param.flowCode}"/>
			<input type="hidden" name="applyBy" value="${param.applyBy}"/>
			<div class="ibox">
				<div class="ibox-content">
					<table id="historyFlowTable"></table>
				</div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				initList();
			});
			
			function initList(){
				$("#historyFlowForm").initTable({
					mars:'FlowDao.historyFlowList',
					id:'historyFlowTable',
					page:true,
					limit:20,
					rowDoubleEvent:'flowDetail',
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 title:'操作',
						 field:'',
						 event:'flowDetail',
						 width:70,
						 templet:function(row){
							 return '<span class="btn btn-xs btn-default">查看</span>';
						 }
					 },{
						field:'APPLY_STATE',
						title:'当前状态',
						width:80,
						templet:function(row){
							var val = row['APPLY_STATE'];
							return flowApplyState(val);
						}
					},{
						 field:'APPLY_NO',
						 title:'编号',
						 width:130
					 },{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:200
					 },{
						 field:'APPLY_REMARK',
						 title:'申请说明',
						 minWidth:200
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					},{
						field:'NODE_NAME',
						title:'当前节点',
						width:80
					},{
						field:'CHECK_NAME',
						title:'当前审批人',
						width:80,
						templet:function(row){
							return row['CHECK_NAME'];
						}
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function flowDetail(data){
			var flowCode = data['FLOW_CODE'];
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
	 
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout_div.jsp" %>