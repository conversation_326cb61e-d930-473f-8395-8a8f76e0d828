<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>里程碑</title>
	<style>
		.base-info tr td:nth-child(odd){
			background-color: #f8f8f8;
		}
		.delayTask{display: none;}
		.init-btn{display: none;}
		#extMsg{margin-left: 10px;font-size: 12px;color: red;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input type="hidden" name="project.PROJECT_ID" value="${param.projectId}"/>
			<input type="hidden" name="projectId" value="${param.projectId}"/>
			<input type="hidden" name="projectName" id="projectName" value="${param.projectName}"/>
			<div class="ibox">
				<div class="ibox-content">
					<table class="layui-table base-info" data-mars="ProjectDao.record" data-mars-prefix="project.">
						<tr>
							<td style="width: 100px;">开工日期</td>
							<td><span name="project.BEGIN_DATE"></span></td>
							<td style="width: 100px;">上线日期</td>
							<td><span name="project.SX_DATE"></span></td>
							<td>初验日期</td>
							<td><span name="project.CY_DATE"></span></td>
							<td>终验日期</td>
							<td><span name="project.ZY_DATE"></span></td>
						</tr>
						<tr>
						  <td>项目经理</td>
						  <td>
						  	<span name="project.PROJECT_PO_NAME"></span>
						  </td>
						  <td>开发负责人</td>
						  <td>
						  	<span name="project.PO_NAME"></span>
						  </td>
						  <td>项目计划</td>
						  <td class="pj-plan">
						  	<button class="btn btn-xs btn-link" type="button" onclick="lookFlow('plan',0);">查看流程</button>
						  </td>
						  <td>项目任命</td>
						  <td class="pj-pm">
						  	<button class="btn btn-xs btn-link" type="button" onclick="lookFlow('pm',0);">查看流程</button>
						  </td>
						</tr>
						<tbody class="pj-planchange">
							
						</tbody>
						<tbody class="plan-update-info">
							
						</tbody>
					</table>
				    <table id="tree" lay-filter="tree"></table>
				</div>
			</div>
		</form>
		<script type="text/x-jsrender" id="btnBar">
			 <button onclick="dataMgr.editMode();" type="button" class="btn btn-info btn-sm change-mode mr-10">切换模式</button>
			 <button onclick="dataMgr.saveData();" type="button" class="btn btn-info btn-sm save-btn mr-10">保存</button>
			 
			 <button onclick="dataMgr.planAllTask();" type="button" class="btn btn-link btn-sm plan-task mr-10">计划任务</button>

			<div class="btn-group mr-20">
			    <button lay-event="dataMgr.expanTree" type="button" class="btn btn-default btn-sm del-btn ml-20">展开</button>
			    <button lay-event="dataMgr.closeTree" type="button" class="btn btn-default btn-sm del-btn ml-20">折叠</button>
			</div>

			<div class="btn-group">
			 	<button lay-event="dataMgr.delItemData" type="button" class="btn btn-warning btn-sm del-btn ml-20">删除项</button>
			 	<button lay-event="dataMgr.addFirstData" type="button" class="btn btn-info btn-sm add-btn ml-5">新增一级项</button>
			 	<button lay-event="dataMgr.addItemData" type="button" class="btn btn-default btn-sm add-btn ml-5">新增子项</button>
			 	<button lay-event="dataMgr.upItemData" type="button" class="btn btn-default btn-sm add-btn ml-5 layui-hide">上移项</button>
			 	<button lay-event="dataMgr.downItemData" type="button" class="btn btn-default btn-sm add-btn ml-5 layui-hide">下移项</button>
			 	<button lay-event="dataMgr.sortItem" type="button" class="btn btn-default btn-sm add-btn ml-15">修改排序</button>
			 	<button lay-event="dataMgr.initData" type="button" class="btn btn-default btn-sm init-btn ml-15">初始化</button>
			</div>
		</script>
		<script type="text/x-jsrender" id="planNameTpl">
			{{if hasAuth}}
			   <input type="text" name="{{:ID}}_pname" style="width:150px" value="{{:PLAN_NAME}}" onmouseover="layer.tips('排序：{{:SORT}}',this)" class="form-control input-sm plan-name">
			{{else}}
			  {{:PLAN_NAME}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="poUserTpl">
			{{if hasAuth}}
			  <input type="hidden" name="{{:ID}}_doUserId" value="{{:PLAN_PERSON_ID}}">
			  <input type="text" name="{{:ID}}_doUserName" readonly="readonly" style="width:80px" placeholder="为空本人" onclick="singleUser(this);" value="{{:PLAN_PERSON_NAME}}" class="form-control input-sm">
			{{else}}
			  {{:PLAN_PERSON_NAME}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="beginDateTpl">
			{{if hasAuth}}
			  <input type="text" data-laydate="{type:'date'}" style="width:100px" name="{{:ID}}_planBeginDate" value="{{:PLAN_BEGIN_DATE}}" class="form-control input-sm">
			{{else}}
			  {{:PLAN_BEGIN_DATE}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="endDateTpl">
			{{if hasAuth}}
			  <input type="text" data-laydate="{type:'date'}" style="width:100px" name="{{:ID}}_planEndDate" value="{{:PLAN_FINISH_DATE}}" class="form-control input-sm">
			{{else}}
			  {{:PLAN_FINISH_DATE}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="rBeginDateTpl">
			{{if hasAuth}}
			  <input type="text" data-laydate="{type:'date'}" style="width:100px" name="{{:ID}}_beginDate" value="{{:BEGIN_DATE}}" class="form-control input-sm">
			{{else}}
			  {{:BEGIN_DATE}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="rEndDateTpl">
			{{if hasAuth}}
			  <input type="text" data-laydate="{type:'date'}" style="width:100px" name="{{:ID}}_endDate" value="{{:FINISH_DATE}}" class="form-control input-sm">
			{{else}}
			  {{:FINISH_DATE}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="taskTpl">
			{{if hasChildren==0}}
			  {{if hasAuth&&PROJECT_ID}}
				<button class="btn btn-xs btn-default" onclick="addTask('{{:PLAN_ID}}','{{:PLAN_NAME}}','{{:PROJECT_ID}}','{{:PLAN_PERSON_ID}}','{{:PLAN_BEGIN_DATE}}','{{:PLAN_FINISH_DATE}}')" type="button">+任务</button>
				{{else}}
				<button class="btn btn-xs btn-default" onclick="addTask('{{:PLAN_ID}}','{{:PLAN_NAME}}','{{:PROJECT_ID}}','{{:PLAN_PERSON_ID}}','{{:PLAN_BEGIN_DATE}}','{{:PLAN_FINISH_DATE}}')" type="button"><i class="fa fa-edit"></i> 任务</button>
			  {{/if}}
			{{/if}}
			{{if TASK_COUNT>0}}
				{{if hasChildren==0}}
			 		<a href="javascript:;" onclick="taskList('{{:PLAN_ID}}','{{:PLAN_NAME}}')" title="已完成数/任务总数">查看({{:TASK_FINISH_COUNT}}/{{:TASK_COUNT}})</a>
				{{else}}
			 		已完成{{:TASK_FINISH_COUNT}},总数{{:TASK_COUNT}}
				{{/if}}
			{{/if}}
			{{if TASK_DELAY_COUNT>0}}
			  <span class="delayTask">{{:TASK_DELAY_COUNT}}</span>
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="processTpl">
			{{if hasAuth}}
			  <select style="width:70px" name="{{:ID}}_process" data-value="{{:PLAN_PROCESS}}" onchange="planProcess(this,'{{:ID}}')" class="form-control input-sm">
					<option value="0">0%</option>
					<option value="10">10%</option>
					<option value="20">20%</option>
					<option value="30">30%</option>
					<option value="40">40%</option>
					<option value="50">50%</option>
					<option value="60">60%</option>
					<option value="70">70%</option>
					<option value="80">80%</option>
					<option value="90">90%</option>
					<option value="100">100%</option>
			  </select>
			{{else}}
			  {{:PLAN_PROCESS}}%
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="remarkTpl">
			 <input type="hidden" name="{{:ID}}_order" class="orderIndex" value="{{:SORT}}">
			 <input type="hidden" name="{{:ID}}_planName" value="{{:MENU_NAME}}">
			 <input type="hidden" name="{{:ID}}_parentPlanName" value="{{:PARENT_NAME}}">
			 <input type="hidden" name="planIds" value="{{:PLAN_ID}}">
			 <input type="hidden" name="{{:ID}}_planId" value="{{:PLAN_ID}}">
			 <input type="hidden" name="{{:ID}}_pPlanId" value="{{:PARENT_ID}}">
			 <input type="hidden" name="ids" value="{{:ID}}">
			{{if hasAuth}}
			   <textarea name="{{:ID}}_remark" class="form-control input-sm" style="width:99%;height:28px;" onclick="textareaEditLayer(this);">{{:PLAN_REMARK}}</textarea>
			{{else}}
			   {{:PLAN_REMARK}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="stateTpl">
			{{if hasAuth}}
			  {{if PLAN_STATE}}
			   <select class="form-control input-sm" name="{{:ID}}_state" data-value="{{:PLAN_STATE}}">
					<option value="1" style="color:#ff3b30;">待开始</option>
					<option value="5" style="color:#4298fd;">进行中</option>
					<option value="9" style="color:#05e5f2;">已完成</option>
				</select>
				{{else}}
				   <input type="hidden" name="{{:ID}}_state" value="1"> 待填写
				{{/if}}
			{{else}}
			    {{call:PLAN_STATE fn='stateDesc'}}
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
	    var projectId = '${param.projectId}';
	   
	    var lastSelectRow = null;
	    var hasAuth = false;
	    var editModeFlag = false;
		
	    $(function(){
			if(top.isAdmin=='1'){
				hasAuth = true;
				editModeFlag = true;
			}
			$("#searchForm").render({success:function(rs){
				var recordInfo = rs['ProjectDao.record'].data;
				debugger;
				if(recordInfo){
					var planEditTime = recordInfo.PLAN_EDIT_TIME;					
					var planEditBy = recordInfo.PLAN_EDIT_BY;		
					if(planEditTime){
						var html = '<tr><td>最后修改信息</td><td colspan="7">';
						html = html + '修改时间：'+planEditTime+',修改人：'+planEditBy;
						html += '</td></tr>';
						$('.plan-update-info').html(html);
					}
				}
				
				loadData();
				lookFlow('plan',1);
				lookFlow('pm',1);
				lookFlow('planchange',1);
			}});
		});
	    
	    function loadData(){
	    	dataMgr.init();
	    }
		
	    
		var dataMgr = {
			init:function(){
				var data = dataMgr.getData();
				dataMgr.initData(data);
			},
			initData:function(data){				
				var treeTable = layui.treeTable;
				var _data = {data:{cascade:'children',rootPid:'0'},view:{expandAllDefault:true,showIcon:false},customName:{pid:'PARENT_ID',id:'ID',name:'MENU_NAME'}};
				var inst = treeTable.render({
					 elem: '#tree',
					 id:'tree',
					 toolbar:'#btnBar',
					 data:data,
					 title:'项目计划',
					 even:!hasAuth,
					 height:'full-150',
					 tree:_data,
					 cols:[[
						 {type:'radio',hide:!hasAuth},
						 {type:'checkbox',hide:!hasAuth},
						 {type:'numbers'},
						 {field:'MENU_NAME',width:220,align:'left',title:'名称',templet:function(row){
							 row.hasAuth = hasAuth;
							 if(row.PLAN_NAME==''){
								 row['PLAN_NAME'] = row.MENU_NAME;
							 }
							 return renderTpl('planNameTpl',row);
						   }
						 },
						 {field:'PLAN_PERSON_NAME',width:100,align:'center',title:'负责人',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('poUserTpl',row);
						   }
						 },
						 {field:'TASK_ID',width:120,align:'left',title:'任务',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('taskTpl',row);
						   },exportTemplet: function(d, obj){
							   return d.TASK_COUNT;
						   }
						 },
						 {field:'PLAN_BEGIN_DATE',width:130,align:'center',title:'计划开始时间',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('beginDateTpl',row);
						   }
						 },
						 {field:'PLAN_FINISH_DATE',width:130,align:'center',title:'计划结束时间',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('endDateTpl',row);
						   }
						 },
						 {field:'BEGIN_DATE',width:130,align:'center',title:'实际开始时间',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('rBeginDateTpl',row);
						   }
						 },
						 {field:'FINISH_DATE',width:130,align:'center',title:'实际结束时间',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('rEndDateTpl',row);
						   }
						 },
						 {field:'PLAN_PROCESS',width:90,align:'center',title:'进度',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('processTpl',row);
						   }
						 },
						 {field:'PLAN_STATE',title:'状态',align:'center',width:100,templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('stateTpl',row);
						   }
						 },
						 {field:'PLAN_REMARK',title:'备注',minWidth:100,templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('remarkTpl',row);
						   }
						 }
					]],
					done:function(res, curr, count, origin){
						renderDate('#searchForm');
						
						$("select[data-value]").each(function(){
							var val = $(this).attr('data-value');
							console.log($(this).attr('name'),val);
							if(val){
								$(this).val(val);
							}
						});
						
						$("[data-level='0']").find('.plan-name').css('font-weight','700');
						
						$("tbody .delayTask").parents('tr').css('background-color','#f6f1c0');
						
						if(!editModeFlag){
							$('.change-mode').hide();
						}
						if(hasAuth){
							$('.save-btn,.add-btn,.del-btn').show();
							if(count>20){
								setTimeout(dataMgr.closeTree,500);
								layer.msg('超过20项计划,已自动折叠',{icon:7,offset:'20px',time:1200});
							}
						}else{
							$('.save-btn,.add-btn,.del-btn').hide();
							dataMgr.expanTree();
						}
					},
					defaultToolbar: ['filter',{name: 'exports',onClick: function(obj) {
						      var data = obj.data;
						      var options = obj.config;
						      obj.openPanel({
						        list: ['<li data-type="csv">导出 CSV 文件</li>','<li data-type="xlsx">导出 XLSX 文件</li>'].join(''),
						        done: function(panel, list) {
						          list.on('click', function() {
						            var type = $(this).data('type');
						            if(hasAuth){
						            	layer.msg('请切换预览模式再操作',{icon:7,offset:'20px',time:1200},function(){
							            	dataMgr.editMode();
							            	return;
						            	});
						            }
						            if (type === 'csv') {
						            	layui.table.exportFile(options.id, null, type);
						            } else if(type === 'xlsx') {
						            	layui.table.exportFile(options.id, null, type);
						            }
						          });
						        }
						      });
						    }
						  },'print']
				 });
				 
				 treeTable.on('row(tree)', function(obj) {
					rowEvent(obj.data,obj);
					// treeTable.setRowChecked('tree', {index:obj.index,type:'radio'});
				 });
				 
				 treeTable.on('radio(tree)', function(obj){
					 lastSelectRow = obj.data;
				 });
				 
				 treeTable.on('toolbar(tree)', function(obj){
					  var checkStatus = treeTable.checkStatus('tree'); 
					  var data = checkStatus.data;
		    		  var event = obj.event;
		    		  if(event){
		    			  excuteFn(event,[data,obj]);
		    		  }
				 });
				 window.treeTable = treeTable;
			},
			expanTree:function(){
				treeTable.expandAll('tree', true); 
			},
			closeTree:function(){
				treeTable.expandAll('tree', false); 
			},
			getData:function(){
				var _data = form.getJSONObject("#searchForm");
				var data = [];
				ajax.remoteCall("${ctxPath}/webcall?action=ProjectPlanDao.projectPlanList",_data,function(rs) { 
					data = rs.data;
					if(data.length==0){
						$('.init-btn').show();
					}
				},{async:false});
				var _data = dataToTree(data,{idFiled: 'ID', textFiled: 'MENU_NAME', parentField: 'PARENT_ID', childField: '',def:{spread:true}, map: {ID: 'id', MENU_NAME: 'title' } });
				return _data;
			},
			query:function(){
				$("#searchForm").queryData();
			},
			planAllTask:function(){
				taskList('0','项目所有计划关联的任务');
			},
			saveData:function(fn){
				var data = form.getJSONObject("#searchForm");
				if(data.ids){
					ajax.remoteCall("${ctxPath}/servlet/project/conf?action=editPlan",data,function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								loadData();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}else{
					loadData();
				}
			},
			editMode:function(){
				if(hasAuth){
					hasAuth = false;
					$('.save-btn,.add-btn,.del-btn').hide();
				}else{
					hasAuth = true;
					$('.save-btn').show();
				}
				dataMgr.init();
			},
			delItemData:function(list){
				if(list.length==0){
					layer.msg('请先选择',{icon:7,offset:'20px',time:1200});
					return;
				}
				var arrayName = [];
				var array = [];
				for(var index in list){
					var _id = list[index]['ID'];
					if(_id){
						array.push(_id);
						arrayName.push(list[index]['PLAN_NAME']);
					}
				}
				if(array.length==0){
					layer.msg('没有选择数据',{icon:7,offset:'20px',time:1200});
					return;
				}
				layer.confirm("确认要删除["+arrayName.join()+"]吗?",{offset:'20px',icon:3},function(){
					ajax.remoteCall("${ctxPath}/servlet/project/conf?action=delPlanItem",{projectId:projectId,'planId':array.join()},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								loadData();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			addFirstData:function(){
				layer.prompt({title:"新增一级类别",placeholder:'每行输入一个',offset:'30px',formType:2,icon:3,area:['300px','300px']},function(value, index, elem){
					if(value === '') return elem.focus();
					ajax.remoteCall("${ctxPath}/servlet/project/conf?action=addPlanItem",{'pPlanId':'0',planName:value,pPlanName:'',projectId:projectId},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								dataMgr.saveData();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			addItemData:function(list){
				if(lastSelectRow==null){
					layer.msg('请先选择',{icon:7,offset:'20px',time:1200});
					return;
				}
				var data = lastSelectRow;
				var pPlanId = data['P_PLAN_ID'];
				layer.prompt({title:"["+data.PLAN_NAME+"]新增子类别",placeholder:'每行输入一个',offset:'30px',formType:2,icon:3,area:['300px','300px']},function(value, index, elem){
					if(value === '') return elem.focus();
					ajax.remoteCall("${ctxPath}/servlet/project/conf?action=addPlanItem",{'pPlanId':data.PLAN_ID,planName:value,pPlanName:data.PLAN_NAME,projectId:data.PROJECT_ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								dataMgr.saveData();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			sortItem:function(list){
				if(lastSelectRow==null){
					layer.msg('请先选择',{icon:7,offset:'20px',time:1200});
					return;
				}
				var orderIndex = lastSelectRow['ORDER_INDEX'];
				var id = lastSelectRow['PLAN_ID'];
				layer.prompt({value:orderIndex,title:'排序，从小到大',offset:'20px'},function(value, index, elem){
					$("[name='"+id+"_order']").val(value);
					layer.close(index);
					layer.msg('点击保存生效',{icon:1,offset:'20px',time:1200});
				});
			},
			addPlanFlow:function(){
				var contractId = projectId
				flowApply('pj_plan',{contractId:contractId});
			},
			downItemData:function(list){
				if(lastSelectRow=null){
					layer.msg('请先选择',{icon:7,offset:'20px',time:1200});
					return;
				}
				var $row = $('.layui-table-checked');
				var rowLevel = $row.data('level');
				var $next = $row.next();
				var nextLevel = $next.data('level');
				if ($next.length !== 0 && rowLevel === nextLevel) {
				     $row.insertAfter($next);
					// var sort1  = $row.find('.orderIndex').val();
					// var sort2  = $next.find('.orderIndex').val();
					// $row.val(sort1);
					// $next.val(sort2);
				}else{
			    	layer.msg('不能跨级移动',{icon:7,offset:'20px',time:1200});
			    }
			},
			upItemData:function(list){
				if(lastSelectRow=null){
					layer.msg('请先选择',{icon:7,offset:'20px',time:1200});
					return;
				}
				var $row = $('.layui-table-checked');
				var rowLevel = $row.data('level');
				var $prev = $row.prev(); 
				var prevLevel = $prev.data('level');
				if($prev.length !== 0&&rowLevel==prevLevel) {
			         $row.insertBefore($prev);
					// var sort1  = $row.find('.orderIndex').val();
					// var sort2  = $prev.find('.orderIndex').val();
					// $row.val(sort1);
					// $prev.val(sort2);
			    }else{
			    	layer.msg('不能跨级移动',{icon:7,offset:'20px',time:1200});
			    }
			}
		}
		
		function lookFlow(flow,flag){
			ajax.remoteCall("${ctxPath}/webcall?action=ProjectDataDao.projectFlowList",{'projectId':'${param.projectId}',flow:flow},function(result) {
				var data = result.data;
				if(flow=='pm'){
					var json = {businessId:data['pmApplyId']};
					if(flag==0){
						if(json.businessId){
							popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:json});
						}else{
							layer.msg('流程不存在',{icon:7,offset:'20px',time:1200});
						}
					}else{
						if(json.businessId==''||json.businessId==undefined){
							$('.pj-pm').html('暂无发起流程');
						}
					}
				}else if(flow=='plan'){
					var json = {businessId:data['planApplyId']};
					if(flag==0){
						if(json.businessId){
							popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:json});
						}else{
							layer.msg('流程不存在',{icon:7,offset:'20px',time:1200});
						}
					}else{
						if(json.businessId==''||json.businessId==undefined){
							$('.pj-plan').html('暂无发起流程 <button onclick="dataMgr.addPlanFlow();" type="button" class="btn btn-success btn-xs add-btn ml-5">+发布计划</button>');
						}
					}
				}else if(flow=='planchange'){
					planChangeList(data['planchangeList']);
				}
			});
		}
		
		function flowDetail(id){
			popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:{businessId:id}});
		}
		
		function planChangeList(planchangeList){
			console.log(planchangeList);
			if(planchangeList&&planchangeList.length>0){
				var html = '<tr><td>变更计划</td><td colspan="7">';
				for(var i=0;i<planchangeList.length;i++){
					var planchange = planchangeList[i];
					html += '<a href="javascript:;" onclick="flowDetail(\''+planchange.APPLY_ID+'\');">'+planchange.APPLY_DATE+' '+planchange.APPLY_TITLE+'</a>&nbsp;';
					if(i<planchangeList.length-1){
						html += '<br>';
					}
				}
				html += '</td></tr>';
				$('.pj-planchange').html(html);
			}else{
				$('.pj-planchange').html('<tr><td colspan="8">暂无变更计划</td></tr>');
			}
		}
		
		function addTask(planId,planName,projectId,planPersonId,beginDate,endDate){
			if(planId){
				var projectName = $('#projectName').val();
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务',data:{projectId:projectId,projectName:projectName,assignUserId:planPersonId,groupId:planId,groupName:planName,beginDate:beginDate,endDate:endDate}});
			}else{
				layer.msg('当前计划是空计划，请项目经理初始化计划',{icon:7,time:1200});
			}
			
		}

		function initData(){	
			ajax.remoteCall("${ctxPath}/webcall?action=ProjectPlanDao.projectPlanList",{'projectId':'${param.projectId}',isInit:1},function(result) {
				if(result.state == 1){
					dataMgr.initData(result.data);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function taskList(planId,planName){
			var projectId = $("[name='projectId']").val();
			popup.openTab({id:"task_"+planId,url:'${ctxPath}/pages/task/task-query.jsp',title:'【'+planName+'】关联任务',data:{groupId:planId,planName:planName,projectId:projectId}});
		}
		
		function stateDesc(val){
			if(val){
				var json = {'0':'待填写','1':'<span class="label label-warning">待开始</span>','5':'<span class="label label-info">进行中</span>','9':'<span class="label label-success">已完成</span>'};
				return json[val]||'';
			}
			return '--';
		}
		
		function planProcess(el,id){
			var val = $(el).val();
			var state = $("[name='"+id+"_state']").val();
			if(val==100&&state==1){
				$("[name='"+id+"_state']").val(9);
			}
			else if(val>0&&state==1){
				$("[name='"+id+"_state']").val(5);
			}
		}
		
		function reloadTaskList(){
			location.reload();
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>