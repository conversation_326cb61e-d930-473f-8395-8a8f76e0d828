package com.yunqu.work.dao.other;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.FolderModel;

@WebObject(name="FolderDao")
public class FolderDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		FolderModel model=new FolderModel();
		model.setColumns(getParam("folder"));
		return queryForRecord(model);
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select folder_id,folder_name from YQ_FOLDER");
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
		sql.append(param.getString("folderId"),"and p_folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and folder_name like ?");
		sql.append(1,"and folder_auth = ?");
		sql.append(0,"and source = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject projectList(){
		EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
		sql.append(param.getString("folderId"),"and p_folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and folder_name like ?");
		sql.append(1,"and folder_auth = ?");
		sql.append(1,"and source = ?");
		sql.append(param.getString("fkId"),"and fk_id = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="files",type=Types.LIST)
	public JSONObject files(){
		EasySQL sql=getEasySQL("select * from yq_doc where 1=1");
		sql.append(param.getString("folderId"),"and folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and title like ?");
		sql.append(1,"and doc_auth = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="mylist",type=Types.LIST)
	public JSONObject mylist(){
		EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
		sql.append(param.getString("folderId"),"and p_folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and folder_name like ?");
		sql.append(getUserPrincipal().getUserId(),"and creator = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="myfiles",type=Types.LIST)
	public JSONObject myfiles(){
		EasySQL sql=getEasySQL("select * from yq_doc where 1=1");
		sql.append(param.getString("folderId"),"and folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and title like ?");
		sql.append(getUserPrincipal().getUserId(),"and creator = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}

}
