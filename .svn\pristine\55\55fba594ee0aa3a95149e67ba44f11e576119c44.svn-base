<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="paymentTotal" class="table table-vzebra" style="border: none">
    <tbody>
    <tr>
        <td >合同总额：</td>
        <td style="width: 15%;"><span readonly type="hidden" id="paymentContractAmount"/></td>
        <td>已输入合同总额：</td>
        <td style="width: 15%;"><span readonly type="text" id="inputContractAmount" /></td>
        <td>未输入合同总额：</td>
        <td style="width: 15%;"><span readonly type="text" id="unInputContractAmount" /></td>
        <td>已输入合同不含税总额：</td>
        <td style="width: 15%;"><span readonly type="text" id="contractNoTaxAmount" /></td>
    </tr>
</table>

<table id="paymentTable"></table>
<script  type="text/html" id="PaymentToolbar">
    <button class="btn btn-info btn-sm" onclick="paymentList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-15" onclick="reloadPaymentList()" type="button">刷新</button>
    <button class="btn btn-default btn-sm ml-15" onclick="paymentList.edit()" type="button">修改</button>
    <button class="btn btn-default btn-sm ml-15" onclick="paymentList.del()" type="button">删除</button>
</script>
<script type="text/javascript">
    var paymentList={
        init:function(){
            $("#ContractDetailForm").initTable({
                mars:'ContractPaymentDao.paymentList',
                id:'paymentTable',
                page:false,
                toolbar:'#PaymentToolbar',
                cellMinWidth:50,
                cols: [[
                    {
                        type: 'checkbox',
                        align:'left'
                    },{
                        field: 'PAYMENT_TYPE',
                        title: '款项类型',
                        align:'left',
                        minWidth:120,
                        templet:function(d){
                            return getText(d.PAYMENT_TYPE,'paymentType');
                        }
                    },{
                        field:'AMOUNT_WITH_TAX',
                        title:'合同金额(含税)',
                        align:'left',
                        sort:true,
                        minWidth:100
                    },{
                        field:'AMOUNT_NO_TAX',
                        title:'合同金额(不含税)',
                        minWidth:100
                    },{
                        field:'TAX_RATE',
                        title:'税率(%)',
                        minWidth:80
                    },{
                        field:'JICHENG_FEE',
                        title:'其中：集成费',
                    },{
                        field:'FINANCIAL_FEE',
                        title:'其中：财务费用',
                    },{
                        field:'INVOICED_AMOUNT',
                        title:'已开票金额',
                    },{
                        field:'CHECKED_FLAG',
                        title:'已审核',
                        minWidth:80,
                        templet:function(d){
                            return '<input type="checkbox" lay-skin="primary" ' + (d.CHECKED_FLAG == 1 ? 'checked' : '') + ' disabled>';
                        }
                    }
                ]],
                done:function(data){
                    var totalAmount = 0;
                    var totalAmountNoTax = 0;
                    $('#paymentContractAmount').text(contractInfo.AMOUNT);
                    $.each(data.data, function(index, item) {
                        var amountWithTax = parseFloat(item.AMOUNT_WITH_TAX);
                        var amountNoTax1 = parseFloat(item.AMOUNT_NO_TAX);
                        totalAmount += amountWithTax;
                        totalAmountNoTax += amountNoTax1;
                    });
                    totalAmount = totalAmount.toFixed(2);
                    totalAmountNoTax = totalAmountNoTax.toFixed(2);
                    $('#inputContractAmount').text(totalAmount);
                    $('#contractNoTaxAmount').text(totalAmountNoTax);

                    var totalContractAmount =  parseFloat(contractInfo.AMOUNT);
                    var unInput = totalContractAmount-totalAmount;
                    unInput = unInput.toFixed(2);
                    $('#unInputContractAmount').text(unInput);


                    if(data.total!=undefined){
                       $("#contractPaymentCount").text("("+data.total+")");
                     }
                }
            });
        },
        del:function(){
            var checkStatus = table.checkStatus('paymentTable');
            if(checkStatus.data.length>0){
                layer.confirm("确认要删除吗?",{icon:3,offset:'120px'},function(){
                    ajax.remoteCall("${ctxPath}/servlet/contractPayment?action=BatchDel",checkStatus.data,function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                layer.closeAll();
                                reloadPaymentList();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                });
            }else{
                layer.msg("请选择!");
            }
        },
        edit:function(){
            var checkStatus = table.checkStatus('paymentTable');

            if(checkStatus.data.length > 1){
                layer.msg('一次只能更新一个款项',{icon : 7, time : 1000});
            }
            else if(checkStatus.data.length == 1){
                var paymentId = checkStatus.data[0]['PAYMENT_ID'];
                popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新款项',offset:'auto',area:['50%','50%'],url:ctxPath+'/pages/crm/contract/include/payment-edit.jsp',data:{contractId:contractId,paymentId:paymentId,isNew:'0'}});
            }else{
                layer.msg("请选择!");
            }
        },
        add:function (){
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增款项信息',offset:'auto',area:['50%','50%'],url:ctxPath+'/pages/crm/contract/include/payment-edit.jsp',data:{contractId:contractId,isNew:'1'}});

        }
    }

    function reloadPaymentList(){
        $("#ContractDetailForm").queryData({id:'paymentTable',page:false});
    }
</script>
