<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<EasyTag:override name="head">
    <title>发票管理</title>
    <style>
        .layui-badge {
            left: -1px !important;
        }

        .layui-table-cell {
            padding: 0 6px;
        }

        .filterCondition {
            display: none;
            padding-bottom: 20px;
            overflow: auto;
            height: 100%;
        }

        .layui-progress {
            margin-top: 12px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form autocomplete="off" onsubmit="return false;" class="form-inline" id="custMgrForm">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5>发票管理</h5>
                    <div class="input-group input-group-sm" style="width: 140px;">
                        <span class="input-group-addon">客户id</span>
                        <input class="form-control input-sm" name="custId">
                    </div>
                    <div class="input-group input-group-sm" style="width: 140px;">
                        <span class="input-group-addon">合同id</span>
                        <input class="form-control input-sm" name="contractId">
                    </div>
                    <div class="input-group input-group-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="CustMgr.query()"><span
                                class="glyphicon glyphicon-search"></span> 搜索
                        </button>
                    </div>
                    <div class="input-group input-group-sm pull-right">
                        <button type="button" class="btn btn-sm btn-default" style="margin-right: 5px"
                                onclick="CustMgr.query()"> 刷新
                        </button>
                        <button type="button" class="btn btn-sm btn-info" onclick="CustMgr.add()">+ 新建发票</button>
                    </div>
                </div>
            </div>
            <div class="ibox-content">
                <table class="layui-hide" id="custMgrTable"></table>
            </div>
        </div>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">

        var CustMgr = {
            init: function () {
                $("#custMgrForm").initTable({
                    mars: 'InvoiceDao.pageList',
                    id: 'custMgrTable',
                    autoSort: false,
                    rowDoubleEvent(row) {
                        CustMgr.invoiceDetail2(row);
                    },
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed: 'left'
                        }, {
                            field: 'INVOICE_NO',
                            title: '发票编号',
                            align: 'left',
                            width: 100,
                            style: 'color:#1E9FFF;cursor:pointer',
                            event: 'CustMgr.detail'
                        }, {
                            field: 'INVOICE_TYPE',
                            title: '开票类型',
                            align: 'center',
                            width: 110
                        }, {
                            field: 'CUST_NAME',
                            title: '客户名称',
                            align: 'left',
                            width: 140,
                            templet: '<div><a href="javascript:;" onclick="CustMgr.custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
                        }, {
                            field: 'CONTRACT_NAME',
                            title: '所属合同',
                            align: 'left',
                            width: 140,
                            templet: '<div><a href="javascript:;" onclick="CustMgr.contractDetail(\'{{d.CONTRACT_ID}}\',\'{{d.CUST_ID}}\')">{{d.CONTRACT_NAME}}</a></div>'
                        }, {
                            field: 'MARK_DATE',
                            title: '开票日期',
                            align: 'center'
                        }, {
                            field: 'MARK_MONEY',
                            title: '开票金额',
                            align: 'center'
                        }, {
                            field: 'TAX_RATE',
                            title: '税率(%)',
                            align: 'center'
                        }, {
                            field: 'TOTAL_TAX',
                            title: '税额',
                            align: 'center'
                        }, {
                            field: 'AMOUNT_IN_FIGUERS',
                            title: '不含税金额',
                            align: 'center'
                        }, {
                            field: 'REMARK',
                            title: '备注',
                            align: 'center'
                        }, {
                            title: '操作',
                            align: 'center',
                            width: 120,
                            fixed: 'right',
                            templet: function (row) {
                                var html = '<a lay-event="CustMgr.detail">查看</a>';
                                var hasPermission = (getCurrentUserId() == row.CREATE_USER_ID || isSuperUser);
                                if(row.SETTLEMENT_ID && row.SETTLEMENT_ID.trim() !== '') {
                                    if (hasPermission) {
                                        html += ' <span style="color:#ccc;" title="该发票已被结算，不允许修改">编辑</span>';
                                        html += ' <span style="color:#ccc;" title="该发票已被结算，不允许删除">删除</span>';
                                    }
                                } else {
                                    if (hasPermission) {
                                        html += ' <a lay-event="CustMgr.edit">编辑</a>';
                                        html += ' <a lay-event="CustMgr.del">删除</a>';
                                    }
                                }

                                return html;
                            }
                        }, {
                            field: 'SETTLEMENT_ID',
                            title: '结算状态',
                            align: 'center',
                            width: 100,
                            templet: function(d){
                                if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
                                    return '<span style="color:#ff5722;">已结算</span>';
                                } else {
                                    return '<span style="color:#009688;">未结算</span>';
                                }
                            }
                        }
                    ]], done: function () {

                    }
                });
            },
            query: function () {
                $("#custMgrForm").queryData({id: 'custMgrTable', jumpOne: true});
            },
            edit: function (data) {
                if(data.SETTLEMENT_ID && data.SETTLEMENT_ID.trim() !== '') {
                    layer.alert('该发票记录已被结算（结算编号：' + (data.SETTLEMENT_NO || data.SETTLEMENT_ID) + '），不允许修改', {icon: 0});
                    return;
                }
                var invoiceId = data['INVOICE_ID'];
                popup.layerShow({
                    type: 1,
                    full: fullShow(),
                    maxmin: true,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    offset: '20px',
                    area: ['60%', '80%'],
                    url: '${ctxPath}/pages/crm/invoice/invoice-edit.jsp',
                    title: '编辑发票',
                    data: {invoiceId: invoiceId}
                });
            },
            add: function () {
                popup.layerShow({
                    type: 1,
                    full: fullShow(),
                    maxmin: true,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    offset: '20px',
                    area: ['60%', '80%'],
                    url: '${ctxPath}/pages/crm/invoice/invoice-edit.jsp',
                    title: '新增发票',
                    closeBtn: 1
                });
            },
            detail: function (data) {
                var invoiceId = data['INVOICE_ID'];
                popup.layerShow({
                    type: 1,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    title: '发票详情',
                    offset: 'r',
                    area: ['60%', '100%'],
                    url: '${ctxPath}/pages/crm/invoice/invoice-detail.jsp',
                    data: {invoiceId: invoiceId}
                });
            },
            invoiceDetail2: function (data) {
                var invoiceId = data.INVOICE_ID;
                popup.layerShow({
                    type: 1,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    title: '发票详情',
                    offset: 'r',
                    area: ['60%', '100%'],
                    url: '${ctxPath}/pages/crm/invoice/invoice-detail.jsp',
                    data: {invoiceId: invoiceId}
                });
            },
            custDetail: function (custId) {
                var width = $(window).width();
                var w = '80%';
                if (width > 1500) {
                    w = '800px';
                } else if (width < 1000) {
                    w = '100%';
                } else {

                }
                popup.openTab({
                    id: 'custDetail',
                    title: '客户详情',
                    type: 1,
                    closeBtn: 0,
                    shade: false,
                    maxmin: false,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: true,
                    offset: 'r',
                    area: [w, '100%'],
                    url: '${ctxPath}/pages/crm/cust/cust-detail.jsp',
                    data: {custId: custId}
                });
            },
            contractDetail: function (contractId, custId) {
                popup.layerClose('contractDetail');
                popup.openTab({
                    id: 'contractDetail',
                    title: '合同详情',
                    url: '${ctxPath}/project/contract',
                    data: {contractId: contractId, custId: custId}
                });
            },
            del: function (data) {
                if(data.SETTLEMENT_ID && data.SETTLEMENT_ID.trim() !== '') {
                    layer.alert('该发票记录已被结算（结算编号：' + (data.SETTLEMENT_NO || data.SETTLEMENT_ID) + '），不允许删除', {icon: 0});
                    return;
                }

                layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function () {
                    ajax.remoteCall("${ctxPath}/servlet/invoice?action=del", data, function (result) {
                        if (result.state == 1) {
                            layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                                layer.closeAll();
                                reloadInvoiceList();
                            });
                        } else {
                            layer.alert(result.msg, {icon: 5});
                        }
                    });
                });
            }
        }

        function reloadInvoiceList() {
            CustMgr.query();
        }

        $(function () {
            $("#custMgrForm").render({
                success: function () {
                    CustMgr.init();
                }
            });
        });


    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>