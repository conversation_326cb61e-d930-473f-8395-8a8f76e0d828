package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.utils.WeekUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;

/**
 * <AUTHOR>
 */
@WebObject(name = "ContractStatisticDao")
public class ContractStatisticDao extends AppDaoContext {


    @WebControl(name = "statisticUpdateTime", type = Types.TEXT)
    public JSONObject receiptUpdateTime() {

        String tableName = param.getString("tableName");
        if (StringUtils.isBlank(tableName)) {
            tableName = this.getMethodParam(0).toString();
        }
        EasySQL sql = getEasySQL("select UPDATE_TIME FROM yq_contract_stat_table_info");
        sql.append(tableName, "where TABLE_NAME = ? ");
        try {
            String updateTime = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
            return getJsonResult(updateTime);
        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
            return getJsonResult("更新时间缺失");
        }
    }

    //240815直接从收款表里进行统计
    @WebControl(name = "receiptStatList", type = Types.LIST)
    public JSONObject receiptStatListFromReceipt() {
        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        EasySQL sql = getEasySQL("select t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN yq_contract_receipt.MONTH_ID = ? THEN yq_contract_receipt.AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_" + monthId);
            }
        }
        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN  yq_contract_receipt ON t1.CONTRACT_ID = yq_contract_receipt.CONTRACT_ID");

        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");

        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        sql.append("order by t1.create_time desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    //240906从统计表里进行查询
    @WebControl(name = "receiptStatList2", type = Types.LIST)
    public JSONObject receiptStatListFromStatTable() {
        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        EasySQL sql = getEasySQL("select t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {

                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_" + monthId);
            }
        }
        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN yq_contract_receipt_stat t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");

        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");

        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        sql.append("order by t1.create_time desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    //240815从incomeStage、incomeConfirm两个表直接统计
    @WebControl(name = "incomeStatList", type = Types.LIST)
    public JSONObject incomeStatList() {
        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        EasySQL sql = getEasySQL("select t3.CONTRACT_SIMPILE_NAME,t3.CONTRACT_NO,t3.CUSTOMER_NAME,t3.AMOUNT,");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append("t3.STAGE_" + monthId + ",");
            }
        }

        for (int ab = 0; ab < 2; ab++) {
            for (int i = 0; i < period; i++) {
                int curYear = startYear + i;
                for (int j = 1; j <= 12; j++) {
                    String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                    if (ab == 0) {
                        sql.append(monthId, "SUM(CASE WHEN yq_contract_income_confirm.MONTH_ID = ? AND yq_contract_income_confirm.CONFIRM_TYPE = 'A' THEN yq_contract_income_confirm.AMOUNT_NO_TAX ELSE 0 END)");
                        sql.append("AS " + "TOTAL_A_" + monthId + ",");
                    } else {
                        if (j == 12 && i == period - 1) {
                            sql.append(monthId, "SUM(CASE WHEN yq_contract_income_confirm.MONTH_ID = ? AND yq_contract_income_confirm.CONFIRM_TYPE = 'B' THEN yq_contract_income_confirm.AMOUNT_NO_TAX ELSE 0 END)");
                            sql.append("AS " + "TOTAL_B_" + monthId);
                        } else {
                            sql.append(monthId, "SUM(CASE WHEN yq_contract_income_confirm.MONTH_ID = ? AND yq_contract_income_confirm.CONFIRM_TYPE = 'B' THEN yq_contract_income_confirm.AMOUNT_NO_TAX ELSE 0 END)");
                            sql.append("AS " + "TOTAL_B_" + monthId + ",");
                        }
                    }
                }
            }
        }

        sql.append("from (");
        sql.append("select t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT,t1.create_time");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN yq_contract_income_stage.MONTH_ID = ? THEN yq_contract_income_stage.AMOUNT_NO_TAX ELSE 0 END)");
                sql.append("AS " + "STAGE_" + monthId);
            }
        }

        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN  yq_contract_income_stage ON t1.CONTRACT_ID = yq_contract_income_stage.CONTRACT_ID");
        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");
        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT,t1.CREATE_TIME ");
        sql.append("order by t1.create_time desc");

        sql.append(" ) t3");

        sql.append("LEFT JOIN yq_contract_income_confirm ON t3.CONTRACT_ID = yq_contract_income_confirm.CONTRACT_ID");
        sql.append("GROUP BY t3.CONTRACT_SIMPILE_NAME,t3.CONTRACT_NO,t3.CUSTOMER_NAME,t3.AMOUNT");
        sql.append("order by t3.create_time desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    //240906 从yq_contract_income_stat表查询
    @WebControl(name = "incomeStatList2", type = Types.LIST)
    public JSONObject incomeStatListFromStatTable() {

        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        EasySQL sql = getEasySQL("select t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? AND t2.INCOME_TYPE = 'S' THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "STAGE_" + monthId);
            }
        }
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? AND t2.INCOME_TYPE = 'A' THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_A_" + monthId);
            }
        }
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? AND t2.INCOME_TYPE = 'B' THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_B_" + monthId);
            }
        }
        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN yq_contract_income_stat t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");

        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");

        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        sql.append("order by t1.create_time desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "incomeStageRankMonthBySales", type = Types.TEMPLATE)
    public JSONObject incomeStageRankMonthBySales() {
        EasySQL sql = getEasySQL("SELECT pc.SALES_BY, SUM(cis.UNCONFIRM_NO_TAX_A) AS TOTAL_UNCONFIRM_NO_TAX_A FROM yq_project_contract pc JOIN yq_contract_income_stage cis ON pc.CONTRACT_ID = cis.CONTRACT_ID");
        sql.append(EasyCalendar.newInstance().getFullMonth(), "where cis.MONTH_ID = ?");
        sql.append("GROUP BY pc.SALES_BY");
        sql.append("ORDER BY TOTAL_UNCONFIRM_NO_TAX_A DESC");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "incomeStageRankYearBySales", type = Types.TEMPLATE)
    public JSONObject incomeStageRankYearBySales() {
        EasySQL sql = getEasySQL("SELECT pc.SALES_BY, SUM(cis.UNCONFIRM_NO_TAX_A) AS TOTAL_UNCONFIRM_NO_TAX_A FROM yq_project_contract pc JOIN yq_contract_income_stage cis ON pc.CONTRACT_ID = cis.CONTRACT_ID");
        sql.append(EasyCalendar.newInstance().getYear(), "where cis.YEAR_ID = ?");
        sql.append("GROUP BY pc.SALES_BY");
        sql.append("ORDER BY TOTAL_UNCONFIRM_NO_TAX_A DESC");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "unConfirmContractRankYear", type = Types.TEMPLATE)
    public JSONObject unConfirmContractRankYear() {
        EasySQL sql = getEasySQL("SELECT pc.CONTRACT_NAME, SUM(cis.UNCONFIRM_NO_TAX_A) AS TOTAL_UNCONFIRM_NO_TAX_A FROM yq_project_contract pc JOIN yq_contract_income_stage cis ON pc.CONTRACT_ID = cis.CONTRACT_ID");
        sql.append(EasyCalendar.newInstance().getYear(), "where cis.YEAR_ID = ?");
        sql.append(getUserId(), "and pc.SALES_BY = ?");
        sql.append("GROUP BY pc.CONTRACT_NAME");
        sql.append("ORDER BY TOTAL_UNCONFIRM_NO_TAX_A DESC");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "unConfirmCustomerRankYear", type = Types.TEMPLATE)
    public JSONObject unConfirmCustomerRankYear() {
        EasySQL sql = getEasySQL("SELECT pc.CUSTOMER_NAME, SUM(cis.UNCONFIRM_NO_TAX_A) AS TOTAL_UNCONFIRM_NO_TAX_A FROM yq_project_contract pc JOIN yq_contract_income_stage cis ON pc.CONTRACT_ID = cis.CONTRACT_ID");
        sql.append(EasyCalendar.newInstance().getYear(), "where cis.YEAR_ID = ?");
        sql.append(getUserId(), "and pc.SALES_BY = ?");
        sql.append("GROUP BY pc.CUSTOMER_NAME");
        sql.append("ORDER BY TOTAL_UNCONFIRM_NO_TAX_A DESC");
        return queryForList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "contractConsoleStat", type = Types.RECORD)
    public JSONObject contractConsoleStat() {
        EasySQL sql = getEasySQL("select ");
        sql.append(" COUNT(*) AS COUNT_CONTRACT,");
        sql.append(" SUM(AMOUNT) AS SUM_CONTRACT");
        sql.append("from yq_project_contract t1 ");
        sql.append("where 1=1");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  t1.SALES_BY = ? ");
        }
        sql.append(EasyCalendar.newInstance().getYear(), "and t1.YEAR = ?");

        JSONObject result = queryForRecord(sql.getSQL(), sql.getParams());
        JSONObject data = result.getJSONObject("data");
        String[] keys = new String[]{"COUNT_CONTRACT", "SUM_CONTRACT"};
        for (int i = 0; i < keys.length; i++) {
            if (StringUtils.isBlank(data.getString(keys[i]))) {
                data.put(keys[i], "0");
            }
        }

        EasySQL sql2 = getEasySQL("select IFNULL(COUNT(*), 0) as COUNT_CUST from yq_crm_customer WHERE 1=1");
        if ("user".equals(param.getString("source"))) {
            sql2.append(getUserId(), "and owner_id = ?");
        }
        sql2.appendLike(EasyCalendar.newInstance().getYear(), "and create_time like ?");
        String countCust = queryForString(sql2.getSQL(), sql2.getParams());
        data.put("COUNT_CUST", StringUtils.isBlank(countCust) ? "0" : countCust);

        EasySQL sql3 = getEasySQL("select ");
        sql3.append("SUM(t1.PLAN_RCV_AMT) AS SUM_STAGE");
        sql3.append("from yq_contract_stage t1 ");
        sql3.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql3.append("where 1=1");
        sql3.append(EasyCalendar.newInstance().getYear(), "and t1.YEAR_ID = ?");
        if ("user".equals(param.getString("source"))) {
            sql3.append(getUserId(), "and t2.SALES_BY = ? ");
        }
        String sumStage = queryForString(sql3.getSQL(), sql3.getParams());
        data.put("SUM_STAGE", StringUtils.isBlank(sumStage) ? "0" : sumStage);

        result.put("data", data);
        return result;
    }

    @WebControl(name = "contractConsoleStat2", type = Types.RECORD)
    public JSONObject contractConsoleStat2() {

        EasySQL sql = getEasySQL("select ");
        sql.append("SUM(t1.PLAN_RCV_AMT) AS SUM_STAGE");
        sql.append("from yq_contract_stage t1 ");
        sql.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("where 1=1");
        sql.append(EasyCalendar.newInstance().getYear(), "and t1.YEAR_ID = ?");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and t2.SALES_BY = ? ");
        }

        JSONObject result = queryForRecord(sql.getSQL(), sql.getParams());
        JSONObject data = result.getJSONObject("data");
        String[] keys = new String[]{"SUM_STAGE"};
        for (int i = 0; i < keys.length; i++) {
            if (StringUtils.isBlank(data.getString(keys[i]))) {
                data.put(keys[i], "0");
            }
        }

        EasySQL sql2 = getEasySQL("select IFNULL(SUM(AMOUNT), 0) as SUM_RECEIPT from yq_contract_receipt WHERE 1=1");
        if ("user".equals(param.getString("source"))) {
            sql2.append(getUserId(), "and owner_id = ?");
        }
        sql2.append(EasyCalendar.newInstance().getYear(), "and YEAR_ID = ?");
        String receiptSum = queryForString(sql2.getSQL(), sql2.getParams());
        data.put("SUM_RECEIPT", StringUtils.isBlank(receiptSum) ? "0" : receiptSum);


        double sumStage = Double.parseDouble(data.getString("SUM_STAGE"));
        double receiptSumDouble = Double.parseDouble(receiptSum);
        double unReceive = sumStage - receiptSumDouble;
        data.put("UN_RECEIVE", String.format("%.2f", unReceive));


        EasySQL sql3 = getEasySQL("select ");
        sql3.append(EasyCalendar.newInstance().getYear(), "SUM(CASE WHEN YEAR_ID = ? THEN UNCONFIRM_NO_TAX_A ELSE 0 END) AS SUM_UNCONFIRM");
        sql3.append("from yq_contract_income_stage t1 ");
        if ("user".equals(param.getString("source"))) {
            sql3.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        }
        sql3.append("where 1=1");
        sql3.append(EasyCalendar.newInstance().getYear(), "and  t1.INCOME_CONF_FLAG = 0 and t1.YEAR_ID = ?");
        if ("user".equals(param.getString("source"))) {
            sql3.append(getUserId(), "and  t2.SALES_BY = ? ");
        }
        String unconfirm = queryForString(sql3.getSQL(), sql3.getParams());
        data.put("SUM_UNCONFIRM", StringUtils.isBlank(unconfirm) ? "0" : unconfirm);


        EasySQL sql4 = getEasySQL("select SUM(AMOUNT_NO_TAX) AS SUM_CONFIRM from yq_contract_income_confirm t1 ");
        if ("user".equals(param.getString("source"))) {
            sql4.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        }
        sql4.append("where 1=1 ");
        sql4.append("and CONFIRM_TYPE = 'A'");
        if ("user".equals(param.getString("source"))) {
            sql4.append(getUserId(), "and  t2.SALES_BY = ? ");
        }
        String confirm = queryForString(sql4.getSQL(), sql4.getParams());
        data.put("SUM_CONFIRM", StringUtils.isBlank(confirm) ? "0" : confirm);

        result.put("data", data);
        return result;
    }


}

