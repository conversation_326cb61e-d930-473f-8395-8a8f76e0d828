<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>版本</title>
	<style>
		#versionEditForm table,tr,td,p{max-width: 100%!important;}
		#versionEditForm img{max-width: 98%!important;height: auto;}
		#versionEditForm .w-e-text{max-width: 100%!important;}
		.file-div{display: block;float: inherit;}
		.layui-icon-edit{font-size: 12px;}
		.avatar{
			float: left;
			height: 3em;
			width: 3em;
			display: block;
		    margin: .2em 0 0;
		}
    	.avatar img{
		    display: inline-block;
		    height: 100%;
		    width: 100%;
		    border-radius: 100%;
		    overflow: hidden;
		    font-size: inherit;
		    vertical-align: middle;
		    -webkit-box-shadow: 0 0 1px rgba(0,0,0,.3);
		    box-shadow: 0 0 1px rgba(0,0,0,.3);
		}
		#versionEditForm .userName{
		    font-size: 1em;
		    color: rgba(0,0,0,.87);
		    font-weight: 500;
		}
		#versionEditForm .createTime{
		    display: inline-block;
		    margin-left: .5em;
		    color: rgba(0,0,0,.4);
		    font-size: .875em;
		}
		.b_content{
			margin: 10px 15px;
		}
		.d_content{
			margin-left: 4em;
			padding: 6px 0px;
		}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 4px;
		}
		#moduleTd{margin-top: 10px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="versionEditForm" data-mars="VersionDao.securityRecord" autocomplete="off" data-mars-prefix="version.">
     		    <input type="hidden" value="${param.securityId}" id="securityId" name="security.SECURITY_ID"/>
     		    <input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
 		   		<input type="hidden" value="${param.securityId}" name="fkId"/>
 		   		<div class="layui-tab layui-tab-brief">
				  <ul class="layui-tab-title">
				    <li class="layui-this">漏洞信息</li>
				    <li>相关附件<span id="fileCount"></span></li>
				    <li>交流反馈</li>
				  </ul>
				  <div class="layui-tab-content">
				  	 <div class="layui-tab-item layui-show">
						<table class="table table-vzebra">
					        <tbody>
					           <tr>
				                    <td style="width: 80px" class="required">漏洞名称</td>
				                    <td>
				                    	<span name="version.SECURITY_TITLE"></span>
				                    </td>
				                    <td style="width: 80px" class="required">漏洞模块</td>
				                    <td>
				                    	<span name="version.PROJECT_NAME"></span>
				                    </td>
					            </tr>
		            	        <tr>
				                    <td>抄送人</td>
				                    <td colspan="3">
				                    	<span name="version.RECIPIENT_NAME"></span>
				                    </td>
				               </tr>
				               <tr>
				                    <td>创建时间</td>
				                    <td>
				                    	<span name="version.CREATE_TIME"></span>
				                    </td>
				                    <td>漏洞类型</td>
				                    <td>
				                    	<span name="version.SECURITY_TYPE"></span>
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required" style="vertical-align: top;">漏洞描述</td>
				                    <td colspan="3">
			                          	 <div name="version.VERSION_DESC"></div>
				                    </td>
					            </tr>
					        </tbody>
		 			     </table>
		 			     <div id="moduleTd" class="layui-row layui-col-space10 mt-10"></div>
		 			     <br><br><br>
				  	 </div>
				  	 <div class="layui-tab-item">
					  	  <table id="fileList" class="layui-table"></table>
	               		  <div id="vfileList"></div>
						  <button class="btn btn-xs btn-info mt-5" type="button" onclick="$('#vlocalfile').click()">+批量上传</button>
				  	 </div>
				  	 <div class="layui-tab-item">
				  	 	 <div style="width: 100%;margin: 10px auto;display: inline-block;">
						  	  <fieldset class="content-title history">
		 								<legend>我来评论</legend>
							  </fieldset>
		  					  <textarea placeholder="在此输入评论内容" name="content" style="height: 70px;" id="comment" class="mt-5 form-control input-sm"></textarea>
						  </div>
						  <div class="clearfix mt-5">
		                   <div class="btn-group pull-left">
								<a type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown">
									动态 <span class="caret"></span>
								</a>
								<ul class="dropdown-menu" role="menu">
									<li><a href="javascript:void(0)" onclick="loadCommentHistory();">评论动态</a></li>
									<li><a href="javascript:void(0)" onclick="lookLog();">浏览动态</a></li>
									<li><a href="javascript:void(0)" onclick="downloadLog();">下载记录</a></li>
								</ul>
							</div>
		                   	<button type="button" style="box-shadow: 0 4px 8px 0 rgba(31,93,234,.35);" class="btn btn-primary btn-sm  pull-right" onclick="addComment()"> <i class="glyphicon glyphicon-send"></i> 发布评论 </button>
						  </div>
		  				     <div id="lookLog">
		 					  	<table id="viewTable"></table>
		 				    </div>
		 				    <div id="opLog">
		 					  	<table id="downloadLogTable"></table>
		 					</div>
		 					<div id="history"></div>
				  	 </div>
				  </div>
				</div>  
				
				<div class="layer-foot text-c updateState" style="display: none;z-index: 99999;position: fixed;">
                   	<button type="button" onclick="VersionEdit.editResult(10);" class="btn btn-sm btn-success"><i class="layui-icon layui-icon-edit"></i>提交处理结果</button>
                   	&nbsp;<button type="button" onclick="closeTab();" class="btn btn-sm btn-default ml-10"><i class="layui-icon layui-icon-edit"></i>关闭页面</button>
                </div>
  		</form>
  		<script id="template-comments" type="text/x-jsrender">
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{call:CREATOR fn='getUserName'}}</span> <span class="createTime">{{:CREATE_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{:CONTENT}}</div>{{if #parent.parent.data.currentUserId == CREATOR}}<a style class="btn btn-xs btn-link hover ib" href="javascript:void(0)" onclick="delComment('{{:COMMENT_ID}}',$(this))">删除</a>	{{/if}}
						</div>						
					</div>
				</div>
			{{/for}}
		</script>
		 <script id="now-module-template" type="text/x-jsrender">
			{{for data}}
				<div class="layui-col-md12">
					<div class="layui-panel">
						<div style="padding: 10px;">
							<p>{{:#index+1}}.处理人：{{:USER_NAME}}</p>
							<p>
								处理描述：
							</p>
							<p class="mt-5">{{:DO_DESC}}</p>
						</div>
					</div>
				</div>
		 {{/for}}
		</script>
		
		 <script id="v-template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/> {{:CREATE_NAME}} 于 {{:CREATE_TIME}} 上传文件 【{{:FILE_NAME}}】 {{:FILE_SIZE}}<a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank">下载</a>({{:DOWNLOAD_COUNT}})</div>
			{{/for}}
		</script>
  		<form  id="vfileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file"  multiple="multiple" type="file" id="vlocalfile" onchange="VersionEdit.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		jQuery.namespace("VersionEdit");
		
		VersionEdit.securityId='${param.securityId}';
		var securityId='${param.securityId}';
		
		var versionRow = {};
		$(function(){
			$("#versionEditForm").render({success:function(result){
				loadFileList();
				loadCommentHistory();
				versionRow = result['VersionDao.securityRecord']['data'];
				
				if(versionRow['SECURITY_STATE']=='0'){
					$('.updateState').show();
				}else{
					$('.updateState').hide();
				}
				
				var moduleCount = versionRow['DO_PEOPLE_COUNT'];
				if(moduleCount==0){
					 $('#moduleTd').remove();
				}else{
					var modules = result['VersionDao.securityRecord']['modules'];
					var tpl = $.templates('#now-module-template');
					var html = tpl.render({data:modules});
					$('#moduleTd').html(html);;
				}
			}});
		});

		VersionEdit.uploadFile = function(){
			var fkId='';
			if(VersionEdit.securityId){
				fkId=securityId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'vcallback',fileId:'vlocalfile',formId:'vfileForm',fkId:fkId,source:'security',fileMaxSize:1024*50});
			
		}
	
		var vcallback = function(result,params){
			var array = [];
			if(isArray(result)){
				array = result;
			}else{
				array[0] = result;
			}
			for(var index in array){
				var data = array[index];
				$("#vfileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span>'+data.name+'-('+data.size+')<a href="'+data.url+'" target="_blank">下载</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this),true)">x</i></div>');
			}
			ajax.remoteCall("${ctxPath}/servlet/security?action=updateFile",{id:params['fkId']},function(result) {
				
			});
		}
		
		VersionEdit.editResult = function(type) {
			layer.prompt({formType: 2,value: '',title: '填写备注',offset:'20px',area: ['400px', '180px']}, function(value, index, elem){
			    layer.close(index);
				var data = {'security.SECURITY_ID':VersionEdit.securityId,'security.SECURITY_STATE':type};
				ajax.remoteCall("${ctxPath}/servlet/security?action=update",data,function(result) { 
					if(result.state == 1){
						layer.msg("操作成功！",{icon:1,time:1200},function(){
							$('.updateState').remove();
							$("#comment").val(value);
							addComment();
							reloadVersion();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			  }
			);
			
		}

		function loadFileList(){
			$("#versionEditForm").initTable({
				mars:'FileDao.fileListDetail',
				id:'fileList',
				page:false,
				cols: [[
		        {
		        	type: 'numbers',
		        	title: '序号',
		        	align:'left'
		        },{
		        	field: 'FILE_NAME',
		        	title: '文件名',
		        	width:300,
					templet:'<div><a target="_blank" href="${ctxPath}/fileview/{{d.FILE_ID}}?view=online">{{d.FILE_NAME}}</a></div>'
		        },{
		        	field: 'FILE_SIZE',
		        	title: '文件大小 ',
		        	width:80
		        },{
		        	field: 'DOWNLOAD_COUNT',
		        	title: '下载次数',
		        	width:130,
		        	event:'dlog'
		        },{
		        	field: 'CREATE_NAME',
		        	title: '上传人',
		        	width:80
		        },{
		        	field: 'CREATE_TIME',
		        	title: '上传时间',
		        	width:140
		        },{
		        	field:'',
		        	title:'操作',
		        	width:60,
		        	templet:'<div><a href="${ctxPath}/fileview/{{d.FILE_ID}}?filename={{d.FILE_NAME}}" title="点击查看" target="_blank">下载</a></div>'
		        }
		        ]],done:function(res,curr,count){
					$("#fileCount").text("("+res.total+")");
		        	
		        }});
		}
		function loadCommentHistory(){
			$("#history").show();
			$("#lookLog").hide();
			$("#opLog").hide();
			ajax.remoteCall("${ctxPath}/webcall?action=CommonDao.commentsList",{fkId:VersionEdit.securityId},function(result) { 
				if(result.data==null||result.data.length<=0){
					$("#versionEditForm").find('#history').hide();
				}else{
					result['currentUserId']=getCurrentUserId();
					var html=renderTpl("template-comments",result);
					$("#history").html(html);
				}
			});
		}
		
		function lookLog(){
			$("#lookLog").show();
			$("#history").hide();
			$("#opLog").hide();
			loadLookLog({tableId:'viewTable',formId:'versionEditForm',fkId:VersionEdit.securityId});
		}
		function downloadLog(){
			$("#opLog").show();
			$("#lookLog").hide();
			$("#history").hide();
			loadDownloadLog({tableId:'downloadLogTable',formId:'versionEditForm',fkId:VersionEdit.securityId});
		}
		var addComment = function() {
			var data = {content:$("#comment").val(),fkId:VersionEdit.securityId,commentNoticeType:'wx',RECEIVER:getCurrentUserId(),CREATOR:versionRow['CREATOR'],TITLE:versionRow['SECURITY_TITLE']};
			ajax.remoteCall("${ctxPath}/servlet/comment?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$("#comment").val("");
						loadCommentHistory();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function delComment(id,obj){
			ajax.remoteCall("${ctxPath}/servlet/comment?action=del",{commentId:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(obj).parents(".b_content").remove();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function dlog(data){
			downloadLogLayer(data['FK_ID'],data['FILE_ID']);
		}
		
		function closeTab(){
			
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>