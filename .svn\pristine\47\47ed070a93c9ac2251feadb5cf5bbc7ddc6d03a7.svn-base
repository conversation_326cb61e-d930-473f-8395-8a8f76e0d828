	body, html {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
    }
	ul{list-style: none;padding: 0;margin: 0;}
    .page-box {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: #f2f2f2;
    }

    .page-box .left-sidebar {
        width: 160px;
        height: 100%;
        background: #fff;
        float: left;
        overflow: auto;
        /*padding-top: 10px;*/
    }

    .page-box .left-sidebar-title {
        height: 60px;
        line-height: 60px;
        text-align: center;
        font-size: 14px;
        color: #333;
    }
    .left-sidebar-item{position: relative;}
	.left-sidebar .left-sidebar-item  a{
	    display: block;
	    color: #333;
	    font-size: 12px;
	    height: 32px;
		line-height: 32px;
		padding-left: 27px;
       
	}
	/*.left-sidebar .left-sidebar-item a:link,*/
	.left-sidebar .left-sidebar-item a:visited,
	.left-sidebar .left-sidebar-item a:hover,
	.left-sidebar .left-sidebar-item a:active {
	    background: #f5f5f5;
	    text-decoration: none;
	}
	.left-sidebar .left-sidebar-item a:link{text-decoration: none;}
	.left-sidebar .left-sidebar-item .sidebar-item a{
        height: 32px;
		line-height: 32px;
		padding-left: 18px;
		text-overflow: ellipsis;
		overflow: hidden;
	}
	.sidebar-item{position: relative;}
	.left-sidebar .left-sidebar-item .activeItem{
	    background: #f5f5f5;
	    color:#0ca8ef;
	    text-decoration: none;
	}
	.left-sidebar .left-sidebar-item .sidebar-item .sidebar-nav-sub-title{
		padding-left: 30px;
		font-size: 13px;
	}
	.left-sidebar .left-sidebar-item .sidebar-item .sidebar-nav-sub-title.active{background: none;color: #0e010a;}
	.left-sidebar .left-sidebar-item .sidebar-item .sidebar-nav-sub {
	    display: none;
	    padding-left: 8px;
	}
	.left-sidebar .left-sidebar-item .sidebar-item > a.active + .sidebar-nav-sub {
	    display: block;
	}
	
	.left-sidebar .left-sidebar-item .sidebar-item a .sidebar-icon {
	    position: absolute;
	    top:-1px;
	    left: 14px;
	    display: inline-block;
	    transition: all 0.3s ease-in-out;
	    font-size: 12px;
	    line-height: 32px;
	}

	.left-sidebar .left-sidebar-item .sidebar-item > a.active .sidebar-icon {
	    transform: rotate(90deg);
	}

    .page-box .right-content {padding: 10px; height: 100%; overflow: auto; margin-left: 160px;}