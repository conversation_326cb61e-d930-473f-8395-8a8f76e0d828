<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<!DOCTYPE html>
<html lang="zh-CN" class="no-js">
  <head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="renderer" content="webkit"> 
	<title>云趣项目列表</title>
	<head>
	  	<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/plugins/css/pluginsCss.css' />
		<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/plugins/plugins.css' />
		<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/css/luckysheet.css' />
		<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/assets/iconfont/iconfont.css' />
		<script src="https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/plugins/js/plugin.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/luckysheet@latest/dist/luckysheet.umd.js"></script>
		<style type="text/css">
			.luckysheet-cell{
				width: 100px;
				height: 50px!important;
			}
		</style>
	</head>
<body>
  <div id="luckysheet" style="margin:0px;padding:0px;position:absolute;width:100%;height:100%;left: 0px;top: 0px;"></div>
 
  <script>
    $(function () {
        //配置项
        var options = {
            container: 'luckysheet', //luckysheet为容器id
           	title: '云趣项目', // 设定表格名称
            lang: 'zh', // 设定表格语言
            defaultFontSize:14,
            gridKey:'task',
            data:[{"name":"ProjectData","status":1,"color":"red", "column": 10,"defaultRowHeight": 300,"defaultColWidth":100,"index": 0,"order": 0}],
            loadUrl:'/yq-work/servlet/project/excel?action=projectAllList&projectId=${param.projectId}'
        }
        luckysheet.create(options);
    })
</script>
</body>
</html>