<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		.layui-tab-item {
		    padding: 5px 5px;
		}
		.layui-tab-content {
		    padding: 5px 0px;
		}
		[rowspan]{vertical-align: top;}
	  /*   #taskMgrForm tr .layui-btn{opacity:0;}
		#taskMgrForm tr:hover .layui-btn{opacity:1;} */
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
		.icon {width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:1px;}
		
		.layui-tree-txt{font-size: 13px;}
		.groupTree-left-content{width: 280px;float: left;display: inline-block;min-height: calc(100vh - 20px);}
		.groupTree-right-content{float:left;margin-left: 10px;width: calc(100% - 298px);}
		#organizationTreeBar {
		    padding: 10px 15px;
		    border: 1px solid #e6e6e6;
		    background-color: #f2f2f2;
		}
		#organizationTree {
		    border: 1px solid #e6e6e6;
		    border-top: none;
		    padding: 5px 2px;
		    overflow: auto;
		    height: -webkit-calc(100vh - 100px);
		    height: -moz-calc(100vh - 100px);
		    height: calc(100vh - 100px);
		}
		.layui-tree-entry .layui-tree-txt {
		    padding: 0 5px;
		    border: 1px transparent solid;
		    text-decoration: none !important;
		}
		.layui-tree-entry.ew-tree-click .layui-tree-txt {
		    background-color: #fff3e0;
		    border: 1px #FFE6B0 solid;
		}
		.layui-tree-set{padding: 3px 5px;}
		.layui-tree-iconClick{margin: 0 5px;display: none;}
		.layui-tree-entry .layui-tree-txt{padding: 0px;color: #616062;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="taskMgrForm">
			<input name="taskState" id="taskState" type="hidden"/>
			<input type="hidden" name="isSelfDept" value="1">
			<input name="taskBusinessId" id="taskBusinessId" value="${param.businessId}" type="hidden"/>
			<div onclick="showProjectTree()" class="leftShow" style="position: absolute;display: inline-flex;align-items: center;height: 50px;background-color: #00abfc;top:40%;cursor: pointer;color: #fff;"><i class="fa fa-chevron-left"></i></div>
			<div class="layui-card">
		        <div class="layui-card-header">
		         		<div class="input-group input-group-sm" style="width: 260px">
	          		    	<span class="input-group-addon">执行时间</span>	
                     		<input data-mars="CommonDao.threeMonthRange" data-mars-top="true" data-mars-reload="false" type="text" data-laydate="{type:'date',range: '到'}" name="planDate" class="form-control input-sm">
                    	 </div>
                    	<div class="btn-group-sm btn-group select-date ml-10">
							<button class="btn btn-default btn-xs" type="button" value="today">今日</button>
							<button class="btn btn-default btn-xs" type="button" value="yesterday">昨天</button>
							<button class="btn btn-default btn-xs" type="button" value="currentMonth">本月</button>
							<button class="btn btn-default btn-xs" type="button" value="threeDay">3天内</button>
							<button class="btn btn-default btn-xs" type="button" value="oneWeek">1周内</button>
							<button class="btn btn-default btn-xs" type="button" value="oneMonth">1个月内</button>
							<button class="btn btn-info  btn-xs" type="button" value="threeMonth">3个月内</button>
							<button class="btn btn-default btn-xs" type="button" value="halfYear">半年内</button>
							<button class="btn btn-default btn-xs" type="button" value="oneYear">1年内</button>
							<button class="btn btn-default btn-xs" type="button" value="nowYear">今年</button>
							<button class="btn btn-default btn-xs" type="button" value="">全部</button>
                     </div>
		        	 <div class="input-group input-group-sm pull-right">
						 <button type="button" class="btn btn-sm btn-info" onclick="taskMgr.add()">+ 发起任务</button>
					 </div>
		        </div>
		        <div class="layui-card-body">
		             	<div class="input-group input-group-sm">
		            		 <span class="input-group-addon">任务名称</span>
		         			 <input type="text" style="width:100px" name="taskName" class="form-control input-sm">
		                 </div>
						<c:choose>
			             	<c:when test="${!empty param.projectId}">
								 <input type="hidden" name="projectId" value="${param.projectId}" class="form-control input-sm">
			             	</c:when>
			             	<c:otherwise>
				             	<div class="input-group input-group-sm" style="width: 160px">
		          		    		<span class="input-group-addon">项目</span>	
								    <input type="hidden" name="projectId" class="form-control input-sm">
								    <input type="text" id="projectId" onclick="singleProject(this);" class="form-control input-sm">
		          		    	</div>
			             	</c:otherwise>
			             </c:choose>
                    	  <div class="input-group input-group-sm">
			             		 <span class="input-group-addon">创建人</span>
								 <input type="hidden" name="creator" class="form-control input-sm">
								 <input type="text" onclick="singleUser(this)" class="form-control input-sm" style="width: 60px">
			             </div>
			              <div class="input-group input-group-sm">
			             		 <span class="input-group-addon">负责人</span>
								 <input type="hidden" name="assignUserId" class="form-control input-sm">
								 <input type="text" onclick="singleUser(this)" class="form-control input-sm" style="width: 60px;">
			             </div>
		             	<div class="input-group input-group-sm">
		            		 <span class="input-group-addon">发起部门</span>
			             	 <input name="deptId" type="hidden">
							 <input style="width: 80px" onclick="singleDept(this);" class="form-control input-sm">
		                 </div>
		             	<div class="input-group input-group-sm">
		            		 <span class="input-group-addon">任务类型</span>
			             	 <input name="deptId" type="hidden">
							 <select style="width: 80px" data-mars-reload="false" name="taskTypeId" data-mars="TaskDao.taskType" class="form-control input-sm">
								<option value="">请选择</option>
							 </select>
		                 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="taskMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="taskMgr.clearForm()">清空</button>
						 </div>
						 <div  class="input-group input-group-sm" id="headerEnd"></div>
		        </div>
		    </div>
		    <script type="text/html" id="taskBar">
			   <a href="javascript:;" onclick="exportTask()" class="btn btn-xs btn-default btn-link">导出任务</a>
			</script>
			<div class="ibox" style="margin-top: -10px;">
				<div class="layui-card groupTree-left-content" style="padding: 10px;">
					<div class="layui-form toolbar" id="organizationTreeBar">项目列表<small>（最长时间1年内）</small></div>
					<div class="layui-card-body" id="organizationTree"></div>
				</div>
				<div class="groupTree-right layui-card groupTree-right-content">
				  <div class="ibox-content">
					<div class="layui-tab layui-tab-card pt-10" lay-filter="filterData" >
					  <ul class="layui-tab-title">
					    <li class="layui-this" data-flag="1">任务列表</li>
					    <li data-flag="2">人员任务统计</li>
					    <li data-flag="3">人员项目统计</li>
					    <li data-flag="4">整体项目统计</li>
					  </ul>
					  <div class="layui-tab-content">
					    <div class="layui-tab-item layui-show mt-5">
					    	<div class="taskMgrContainer">
								<div class="layui-tab layui-tab-brief" lay-filter="taskMgr" data-mars="TaskDao.taskCountStat">
								  <ul class="layui-tab-title">
								    <li data-state="" class="layui-this">全部 <span class="layui-badge layui-bg-cyan ALL">0</span></li>
								    <li data-state="10">待办中 <span class="layui-badge layui-bg-orange" id="A">0</span></li>
								    <li data-state="20">进行中 <span class="layui-badge" id="B">0</span></li>
								    <li data-state="30">已完成 <span class="layui-badge layui-bg-blue" id="C">0</span></li>
								    <li data-state="40">已验收 <span class="layui-badge layui-bg-green" id="D">0</span></li>
								    <li data-state="25">超时未完成 <span class="layui-badge" id="E">0</span></li>
								    <li data-state="11">已退回 <span class="layui-badge" id="F">0</span></li>
								    <li data-state="12">暂停中 <span class="layui-badge layui-bg-orange" id="G">0</span></li>
								   	<li data-state="13">已取消 <span class="layui-badge layui-bg-cyan" id="H">0</span></li>
								  </ul>
								  <div class="layui-tab-content" style="padding: 0px;">
								  	<div class="layui-tab-item layui-show"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  </div>
								</div> 
							    <table class="layui-hide" id="taskMgrTable"></table>
							</div>
					    </div>
					    <div class="layui-tab-item">
				    		<div  class="userTaskStatContainer">
								<table id="userTaskStat" class="layui-hide mb-30"></table>
							</div>
					    </div>
					    <div class="layui-tab-item">
								<table id="taskProjectStat" class="layui-hide mb-30"></table>
					    </div>
					    <div class="layui-tab-item">
								<table id="projectStat" class="layui-hide mb-30"></table>
					    </div>
					  </div>
					</div>
				  </div>
				</div>
			</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
			{{if TASK_STATE!=40}}
	  			{{if currentUserId == CREATOR && TASK_STATE !=11}}
					<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="taskMgr.edit"><i class="layui-icon layui-icon-edit"></i></a>
				{{/if}}
			{{/if}}
			<a class="layui-btn layui-btn-normal layui-btn-xs layui-hide" lay-event="taskMgr.detail">查看</a>
		</script>
		<script type="text/x-jsrender" id="taskProgress">
			{{if F>0}}
			<div class="layui-progress" lay-showPercent="true">
  				<div class="layui-progress-bar {{if F==H}} layui-bg-green {{else H/F <= 0.6}} layui-bg-red {{else H/F <= 0.8}} layui-bg-orange  {{else}} layui-bg-blue {{/if}}" lay-percent="{{:H}}/{{:F}}"></div>
			</div>
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="projectTree">
			<ul class="layui-nav layui-nav-tree">
				{{for data}}
 					<li class="layui-nav-item"><a href="javascript:;">{{:PROJECT_NAME}}</a></li>
				{{/for}}
			</ul>
		</script>
	</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
<script type="text/javascript">
		
		var isSelfDept = '${param.isSelfDept}'||'0';
		
		var tabFlag = '1';
		
		layui.use('element',function(){
			var element=layui.element;
			element.on('tab(taskMgr)', function(elem){
				var  state=$(this).data('state');
				$("#taskState").val(state);
				reloadTaskList();
			});
			element.on('tab(filterData)', function(elem){
				var flag = $(this).data('flag');
				tabFlag = flag;
				if(flag=='2'){
					userTaskStatTable('init');
				}else if(flag=='3'){
					taskProjectStatTable('init');
				}else if(flag=='4'){
					projectStatTable('init');
				}
				localStorage.setItem("lastSelectTaskMgrTab",flag);
			});
		});
		
		var firstLoad=true;
		var taskMgr={
			init:function(){
				layui.config({
					  base: '${ctxPath}/static/module/tableMerge/'
				}).use('tableMerge'); //加载自定义模块

				layui.config({
					  base: '${ctxPath}/static/module/soul/'
				}).use('soulTable'); //加载自定义模块
				
				layui.use(['tableMerge'],function(){
					var tableMerge  = layui.tableMerge;
					$("#taskMgrForm").initTable({
							mars:'TaskDao.taskList',
							//skin:'line',
							elem:'#taskMgrTable',
							id:'taskMgrTable',
							autoSort:false,
							toolbar:'#taskBar',
							filter:true,
							limit:30,
							limits:[30,50,100,200,300,500],
							height:'full-130',
							defaultToolbar:['filter', 'print', 'exports'],
							cols: [[
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'TASK_NAME',
								title: '任务名称',
								align:'left',
								minWidth:220,
								event:'taskMgr.detail',
								style:'color:#1E9FFF;cursor:pointer',	
								templet:function(row){
									var groupName = row['GROUP_NAME'];
									var fileCount = row['FILE_COUNT'];
									var id = row['P_TASK_ID'];
									var html = '';
									var fileDes = fileCount>0?getFileIcon('.file'):'';
									if(groupName){
										html = html + '【'+groupName+'】';
									}
									if(id){
										html = html + fileDes+"<i class='layui-icon layui-icon-senior'></i>"+row['TASK_NAME'];
									}else{
										html = html + fileDes+row['TASK_NAME'];
									}
									return html;
								}
							},{
							    field: 'PROJECT_ID',
								title: '项目名称',
								filter: true,
								event:'taskMgr.projectDetail',
								align:'left',
								sort:true,
								minWidth:150,
								templet:'<div>{{d.PROJ_NAME}}</div>'
							},{
							    field: 'TASK_TYPE_ID',
								title: '类型',
								event:'taskMgr.detail',
								filter: true,
								align:'center',
								sort:true,
								width:70,
								templet:function(row){
									return getText(row['TASK_TYPE_ID'],'taskTypeId');
								}
							},{
							    field: 'TASK_STATE',
								title: '任务状态',
								filter: true,
								sort:true,
								align:'center',
								width:80,
								templet:function(row){
									return taskStateLabel(row.TASK_STATE);
								}
							},{
								field:'PROGRESS',
								title:'进度',
								width:70,
								templet:'<div>{{d.PROGRESS}}%</div>'
							},{
							    field: 'CREATOR',
								title: '发起人',
								sort:true,
								align:'center',
								width:70,
								templet:function(row){
									return '<a href="javascript:;" onclick="userInfoLayer(\''+row['CREATOR']+'\')">'+row.CREATE_NAME+'</a>';
								}
							},{
							    field: 'ASSIGN_USER_ID',
								title: '负责人',
								align:'center',
								merge:true,
								sort:true,
								width:70,
								templet:function(row){
									return '<a href="javascript:;" onclick="userInfoLayer(\''+row['ASSIGN_USER_ID']+'\')">'+row.ASSIGN_USER_NAME+'</a>';
								}
							},{
							    field: 'VIEW_COUNT',
								title: '浏览数',
								align:'center',
								sort:true,
								width:70
							},{
							    field: 'CREATE_TIME',
								title: '发起时间',
								align:'center',
								width:130,
								sort:true,
								templet:function(row){
									var time= row['CREATE_TIME'];
									return cutText(time,19,'');
								}
							},{
							    field: 'DEADLINE_AT',
								title: '截止时间',
								align:'left',
								width:100,
								sort:true,
								templet:function(row){
									var time= row['DEADLINE_AT'];
									var state= row['TASK_STATE'];
									if(state==10||state==20){
										return timeBetween(time);
									}
									return cutText(time,12,'');
								}
							},{
								field:'WORK_HOUR',
								title:'耗时/小时',
								width:80,
								templet:function(row){
									var v1 = row['WORK_HOUR'];
									var v2 = row['PLAN_WORK_HOUR'];
									if(v1){
										return v1;
									}else{
										return '<span class="text-danger">'+v2+'</span>';
									}
								}
							},{
							    field: 'UPDATE_TIME',
								title: '更新时间',
								width:90,
								align:'center',
								hide:true,
								templet:function(row){
									var time= row['UPDATE_TIME'];
									return cutText(time,12,'');
								}
							},{
								title: '操作',
								align:'center',
								width:90,
								cellMinWidth:90,
								templet:function(row){
									row['currentUserId']=getCurrentUserId();
								    return renderTpl('bar1',row);
								}
							}
						]],done:function(result){
							tableMerge.render(layTables['taskMgrTable'].config);
							if(firstLoad){
								$(".ALL").text(result.totalRow)
							}
							firstLoad=false;
							table.on('sort(taskMgrTable)', function(obj){
								  $("#taskMgrForm").queryData({id:'taskMgrTable',jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
							});
						}
				   })
				});
			},
			query:function(qType){
				firstLoad=true;
				if(tabFlag=='2'){
					userTaskStatTable('query');
				}else if(tabFlag=='3'){
					taskProjectStatTable('query');
				}else if(tabFlag=='4'){
					projectStatTable('query');
				}
				
				$("#taskMgrForm").render({success:function(result){
					 if(result['TaskDao.taskCountStat']){
						$("#taskMgrForm").queryData({id:'taskMgrTable',jumpOne:true});
						if(qType==undefined){
							loadProjectTree();
						}
					 }
				}});
			},
			clearForm:function(){
				var planDate = $("[name='planDate']").val();
				$("[name='projectId']").val('');
				$("[name='creator']").val('');
				$("[name='assignUserId']").val('');
				$("[name='deptId']").val('');
				$("#taskMgrForm")[0].reset();
				$("[name='planDate']").val(planDate);
				taskMgr.query();
			},
			edit:function(data){
				var businessId = data['BUSINESS_ID'];
				var projectName = data['PROJ_NAME'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['780px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'编辑任务',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,businessId:businessId,projectName:projectName}});
			},
			add:function(){
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				var businessId = $('#taskBusinessId').val();
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'安排任务',closeBtn:0,data:{businessId:businessId}});
			},
			projectDetail:function(data){
				var projectId = data['PROJECT_ID'];
				if(projectId){
					projectBoard(projectId);
					return;
				}
				var businessId = data['BUSINESS_ID'];
				if(businessId){
					popup.openTab({id:'businessFollowList',url:'${ctxPath}/pages/crm/contacts/contact-query.jsp',title:'商机跟进记录',data:{businessId:businessId}});
				}
			},
			detail:function(data){
				var state = data['TASK_STATE'];
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				if(state==40){
					popup.openTab({id:"task_"+data.TASK_ID,type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-detail.jsp?isDiv=0',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
				}else{
					popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
				}
			},
			notice:function(){
				ajax.remoteCall("${ctxPath}/servlet/task?action=noticeMsg",{},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		function reloadTaskList(){
			$("#taskMgrForm").queryData({id:'taskMgrTable',jumpOne:true});
		}
		$(function(){
			//初始化加载
			$("#taskMgrForm").render({success:function(result){
				 if(result['TaskDao.taskCountStat']){
					taskMgr.init();

					var lastSelectTaskTab = localStorage.getItem("lastSelectTaskMgrTab");
					if(lastSelectTaskTab){
						$("[lay-filter='filterData']").find("[data-flag='"+lastSelectTaskTab+"']").click();
						loadProjectTree();
					}else{
						localStorage.setItem("lastSelectTaskMgrTab",'1');
						loadProjectTree();
					}
					initTreeShow();
				 }
			}});
			
			$('.select-date button').click(function(){
				getDate($(this)[0],'planDate');
				$(this).siblings().removeClass('btn-info');
				$(this).siblings().addClass('btn-default');
				$(this).addClass('btn-info');
			});
		});
		
		var getDate = function(obj,inputName){
			var val = obj.value;
			var bdate = new Date();
			var edate = new Date();
			if('createTime'==inputName||'planDate'==inputName){
				if(val == 'today'){
					
				}else if(val == 'yesterday'){
					bdate.setDate(bdate.getDate() - 1);
					edate = bdate;
				}else if(val == 'threeDay'){
					bdate.setDate(bdate.getDate() - 3);
				}else if(val == 'oneWeek'){
					bdate.setDate(bdate.getDate() - 7);
				}else if(val == 'oneMonth'){
					bdate.setMonth(bdate.getMonth() - 1);
				}else if(val == 'threeMonth'){
					bdate.setMonth(bdate.getMonth() - 3);
				}else if(val == 'halfYear'){
					bdate.setMonth(bdate.getMonth() - 6);
				}else if(val == 'oneYear'){
					bdate.setMonth(bdate.getMonth() - 12);
				}else if(val == 'currentMonth'){
					bdate.setDate(1);
					edate.setMonth(bdate.getMonth() + 1);
					edate.setDate(0);
				}else if(val == 'nowYear'){
					bdate.setMonth(0);
					bdate.setDate(1);
				}
				if(val){
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}else{
				if(val){
					bdate.setDate(bdate.getDate() - val);
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}
			taskMgr.query();
		}
		function selectCondition(){
			popup.layerShow({type:1,anim:0,full:fullShow(),scrollbar:false,shadeClose:false,offset:'l',area:['400px','100%'],content:$('.filterCondition'),title:'筛选条件'});
		}
		
		var userTaskStat = true;
		function userTaskStatTable(type){
			if(userTaskStat){
				userTaskStat=false;
				$("#taskMgrForm").initTable({
					id:'userTaskStat',
					mars:'TaskDao.userTaskStat',
					limit:300,
					page:false,
					data:{'selectUser':'1'},
					cellMinWidth:100,
					totalRow:true,
					toolbar:true,
					rowDoubleEvent:'rowEvent',
					defaultToolbar:['filter', 'print', 'exports'],
					limits:[10,15,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'center'
					 },{
						 title: '序号',
						 type:'radio'
					 },{
						 field:'ASSIGN_DEPT_NAME',
						 title:'部门',
						 width:80
					 },{
					    field: 'ASSIGN_USER_NAME',
						title: '姓名',
						align:'center',
						width:80,
						event:'_selectUser',
						totalRowText:'汇总'
					},{
						field:'PROJECT_NAME',
						title:'参与项目',
						minWidth:220
					},{
						field:'PROJECT_COUNT',
						title:'项目数',
						width:70
					},{
						field:'',
						title: '已完成/总数',
						event:'_selectUser',
						minWidth:160,
						templet:function(row){
							return renderTpl('taskProgress',row);
						}
					},{
					    field: 'F',
						title: '任务总数',
						sort:true,
						width:80,
						totalRow:true
					},{
					    field: 'G',
						title: '未完成数',
						event:'_selectUser',
						align:'center',
						width:80,
						sort:true,
						totalRow:true
					},{
					    field: 'A',
						title: '待办数',
						event:'_selectUser',
						align:'center',
						width:80,
						sort:true,
						totalRow:true
					}
					 ,{
					    field: 'B',
						title: '进行中',
						sort:true,
						align:'center',
						width:80,
						totalRow:true
					},{
					    field: 'E',
						width:92,
						title: '超时未完成',
						sort:true,
						align:'center',
						totalRow:true
					}
				  ,{
					   field: 'C',
					   title: '已完未验收',
						width:92,
					   sort:true,
					   align:'center',
						totalRow:true
				},{
					   field: 'D',
					   title: '已验收',
					   sort:true,
						width:80,
					   align:'center',
					   totalRow:true
					}
				]],done:function(res,curr,count){
					layui.use(['element'], function(){
						var element = layui.element;
						element.render();
					});
					table.on('radio(userTaskStat)', function(obj){
						_selectUser(obj.data);
					});
				}}
			  );
			}else if(type=='query'){
				$("#taskMgrForm").queryData({id:'userTaskStat',data:{'selectUser':'1'},jumpOne:true,page:false});
			}
			
		}
		
		var taskProjectStat = true;
		function taskProjectStatTable(type){
			if(taskProjectStat){
				taskProjectStat=false;
				$("#taskMgrForm").initTable({
					id:'taskProjectStat',
					mars:'TaskDao.projectUserTaskStat',
					limit:30,
					page:true,
					data:{'selectUser':'1'},
					cellMinWidth:100,
					totalRow:true,
					toolbar:true,
					defaultToolbar:['filter', 'print', 'exports'],
					limits:[10,15,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'center'
					 },{
						 field:'PROJECT_NAME',
						 title:'项目名称',
						 minWidth:300
					 },{
						 field:'ASSIGN_DEPT_NAME',
						 title:'部门',
						 sort:true
					 },{
					    field: 'ASSIGN_USER_NAME',
						title: '姓名',
						align:'center',
						width:100,
						sort:true,
						event:'_selectUser',
						totalRowText:'汇总'
					},{
						field:'',
						title: '已完成/总数',
						event:'_selectUser',
						width:90,
						templet:function(row){
							return row['H']+"/"+row['F'];
						}
					},{
					    field: 'F',
						title: '任务总数',
						sort:true,
						totalRow:true
					},{
					    field: 'G',
						title: '未完成数',
						event:'_selectUser',
						align:'center',
						sort:true,
						totalRow:true
					},{
					    field: 'A',
						title: '待办数',
						event:'_selectUser',
						align:'center',
						sort:true,
						totalRow:true
					}
					 ,{
					    field: 'B',
						title: '进行中',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'E',
						title: '超时未完成',
						sort:true,
						align:'center',
						totalRow:true
					}
				  ,{
					   field: 'C',
					   title: '已完未验收',
					   sort:true,
					   align:'center',
						totalRow:true
				},{
					   field: 'D',
					   title: '已验收',
					   sort:true,
					   align:'center',
					   totalRow:true
					}
				]],done:function(res,curr,count){
					table.on('radio(taskProjectStat)', function(obj){
						_selectUser(obj.data);
					});
				}}
			  );
			}else if(type=='query'){
				$("#taskMgrForm").queryData({id:'taskProjectStat',data:{'selectUser':'1'},jumpOne:true,page:true});
			}
			
		}
		
		var projectStat = true;
		function projectStatTable(type){
			if(projectStat){
				projectStat=false;
				$("#taskMgrForm").initTable({
					id:'projectStat',
					mars:'TaskDao.projectTaskStat',
					limit:30,
					page:true,
					data:{'selectUser':'1'},
					cellMinWidth:100,
					totalRow:true,
					toolbar:true,
					rowDoubleEvent:'taskMgr.projectDetail',
					defaultToolbar:['filter', 'print', 'exports'],
					limits:[10,15,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'center'
					 },{
						 field:'PROJECT_NO',
						 title:'项目编号',
						 width:110
					 },{
						 field:'PROJECT_NAME',
						 title:'项目名称',
						 minWidth:300,
						 totalRowText:'汇总'
					 },{
						 field:'ASSIGN_DEPT_NAME',
						 title:'参与部门',
						 width:120
					 },{
						field:'',
						title: '已完成/总数',
						event:'_selectUser',
						width:90,
						templet:function(row){
							return row['H']+"/"+row['F'];
						}
					},{
					    field: 'F',
						title: '任务总数',
						sort:true,
						totalRow:true
					},{
					    field: 'G',
						title: '未完成数',
						event:'_selectUser',
						align:'center',
						sort:true,
						totalRow:true
					},{
					    field: 'A',
						title: '待办数',
						event:'_selectUser',
						align:'center',
						sort:true,
						totalRow:true
					}
					 ,{
					    field: 'B',
						title: '进行中',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'E',
						title: '超时未完成',
						sort:true,
						align:'center',
						totalRow:true
					}
				  ,{
					   field: 'C',
					   title: '已完未验收',
					   sort:true,
					   align:'center',
						totalRow:true
				},{
					   field: 'D',
					   title: '已验收',
					   sort:true,
					   align:'center',
					   totalRow:true
					}
				]],done:function(res,curr,count){
					
				}}
			  );
			}else if(type=='query'){
				$("#taskMgrForm").queryData({id:'projectStat',data:{'selectUser':'1'},jumpOne:true,page:true});
			}
			
		}
		
		function _selectUser(data){
			var userId=data['ASSIGN_USER_ID'];
			var userName = data['ASSIGN_USER_NAME'];
			$("[name='assignUserId']").val(userId);
			$("[name='assignUserId']").next().val(userName);
			taskMgr.query();
			$("#headerEnd").html("<div style='cursor:pointer' class='label label-success label-outline ml-10' onclick='delUser(this)'><span class='selectUserSpan'>"+userName+"</span> x</div>");
			$("[data-flag='1']").click();
		}
		
		function delUser(el){
			$("[name='assignUserId']").val('');
			$("[name='assignUserId']").next().val('');
			$(el).remove();
			taskMgr.query();
		}
		function exportTask(){
			layer.msg('正在导出',{time:500});
			location.href = '${ctxPath}/servlet/task/ext?action=exportTask&data='+encodeURI(JSON.stringify(form.getJSONObject('#taskMgrForm')));
		}
		
		var loadProjectTree = function(){
			 var el = $('.groupTree-right');
			 if(!el.hasClass('groupTree-right-content')){
				return;
			 }
			 $('.groupTree-right').addClass('groupTree-right-content');
			 $('.groupTree-right').prev().addClass('groupTree-left-content').show(100);
			 layui.use(['tree'], function () {
			        var tree = layui.tree;
			        var _params = form.getJSONObject('#taskMgrForm');
			        delete _params['projectId'];
		        	ajax.remoteCall(getCtxPath()+"/webcall?action=TaskDao.taskProjectTree",_params,function(rs) { 
						var result= rs.data;
						
						//
						//var projectTreeTpl = $.templates("#projectTree");
						//var tplHtml = projectTreeTpl.render(rs);
						//$('#organizationTree').html(tplHtml);
						
						 var data = dataToTree(result,{idFiled: 'PROJECT_ID', textFiled: 'PROJECT_NAME', parentField: 'PROJECT_ID', childField: '',def:{spread:false}, map: {PROJECT_ID: 'id', PROJECT_NAME: 'title' } });
						data.unshift({title:'(all)全部项目',id:'',PROJECT_ID:'',PROJECT_NAME:''});
						tree.render({
		                    elem: '#organizationTree',
		                    onlyIconControl: true,
		                    data: data,
		                    click: function (obj) {
		                        $("[name='projectId']").val(obj.data.PROJECT_ID);
		                        $('#projectId').val(obj.data.PROJECT_NAME);
		                        $('#organizationTree').find('.ew-tree-click').removeClass('ew-tree-click');
		                        $(obj.elem).children('.layui-tree-entry').addClass('ew-tree-click');
		                        taskMgr.query('tree');
		                    }
		                });
		        	});
			    }
			 );
		}
		
		function showProjectTree(){
			var _el = $('.leftShow');
			var el = $('.groupTree-right');
			if(el.hasClass('groupTree-right-content')){
				el.removeClass('groupTree-right-content');
				el.prev().hide(100);
				layui.data("userSetting",{key:"showProjectTree",value:0});
				$(_el).html('<i class="fa fa-chevron-right"></i>');
			}else{
				el.addClass('groupTree-right-content');
				el.prev().addClass('groupTree-left-content').show(100);
				layui.data("userSetting",{key:"showProjectTree",value:1});
				$(_el).html('<i class="fa fa-chevron-left"></i>');
			}
		}
		
		function initTreeShow(){
			var data = layui.data("userSetting");
			if(data.showProjectTree=='0'){
				showProjectTree();
			}
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>