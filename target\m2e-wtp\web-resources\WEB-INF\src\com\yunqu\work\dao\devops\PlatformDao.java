package com.yunqu.work.dao.devops;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.PlatformModel;

@WebObject(name="PlatformDao")
public class PlatformDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		PlatformModel model=new PlatformModel();
		model.setColumns(getParam("platform"));
		JSONObject jsonObject=queryForRecord(model);
		String platformId=model.getPlatformId();
		try {
			String sql="select host_id from  yq_platform_host where platform_id = ?";
			List<EasyRow> list= this.getQuery().queryForList(sql, platformId);
			if(list!=null&&list.size()>0){
				String[] hosts=new String[list.size()];
				for(int i=0;i<list.size();i++){
					hosts[i]=list.get(i).getColumnValue(1);
				}
				jsonObject.put("hosts", hosts);
			}
			sql="select service_id from  yq_platform_service where platform_id = ?";
			list= this.getQuery().queryForList(sql, platformId);
			if(list!=null&&list.size()>0){
				String[] services=new String[list.size()];
				for(int i=0;i<list.size();i++){
					services[i]=list.get(i).getColumnValue(1);
				}
				jsonObject.put("services",services);
			}
			/*sql="select project_id from  yq_platform_project where platform_id = ?";
			list= this.getQuery().queryForList(sql, platformId);
			if(list!=null&&list.size()>0){
				String[] projects=new String[list.size()];
				for(int i=0;i<list.size();i++){
					projects[i]=list.get(i).getColumnValue(1);
				}
				jsonObject.put("projects",projects);
			}*/
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return jsonObject;
	}
	
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select platform_id,platform_name from yq_platform");
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from YQ_PLATFORM where 1=1");
		sql.appendLike(param.getString("platformName"),"and platform_name like ?");
		sql.append(param.getString("projectId"),"and project_id = ?");
		if(!isSuperUser()) {
			sql.append(getUserId(),"and ( owner = ?");
			sql.append(getUserId()," or creator = ?)");
		}
		sql.append("order by update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="host",type=Types.LIST)
	public JSONObject host(){
		EasySQL sql=getEasySQL("select t2.* from yq_platform_host t1 INNER JOIN yq_ops_host t2 on t2.HOST_ID=t1.host_id where  1=1");
		sql.append(param.getString("platformId"),"and t1.platform_id = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="service",type=Types.LIST)
	public JSONObject service(){
		EasySQL sql=getEasySQL("select t2.* from yq_platform_service t1 INNER JOIN yq_ops_service t2 on t2.service_id=t1.service_id where 1=1");
		sql.append(param.getString("platformId"),"and t1.platform_id = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="entlist",type=Types.LIST)
	public JSONObject entlist(){
		this.setQuery(EasyQuery.getQuery(getAppName(), Constants.MONITOR_DS_NAME));
		EasySQL sql=getEasySQL("select t1.* from  yc_ent_base t1 where 1=1");
		sql.append(param.getString("platformId"),"and t1.platform_id = ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
