<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<style>
	.layui-colla-title{background-color: #f6f2f2;}
	.layui-colla-content{padding: 0px 0px;}
	.layui-colla-item table{border:none!important;margin: 0px 2px!important;}
	.layui-icon-set{opacity: 0!important;cursor: pointer;margin-left: 15px;}
	.layui-colla-title:hover .layui-icon-set{opacity: 1!important;}
</style>
<div class="mb-10"></div>
<div class="layui-row" data-mars="TaskDao.projectTaskGroupList"  data-template="planTpl">
  
</div>
<script type="text/x-jsrender" id="planTpl">
		<div class="layui-collapse" lay-accordion>
			{{call:taskCount fn='setTaskPlanCount'}}
  			{{for data}}
 			 <div class="layui-colla-item">
   			  <h2 class="layui-colla-title id_{{:GROUP_ID}}">{{:GROUP_NAME}}({{:TASK_COUNT}})  <i class="layui-icon layui-icon-set" onclick="updateTaskGroup('{{:GROUP_ID}}')"></i> <button onclick="addChildTaskGroup('{{:GROUP_ID}}')" class="btn btn-xs btn-default ml-10" type="button">+新增子类</button> <div class="pull-right" style="color:#999;"> {{cutText:PLAN_STARTED_AT 12 ''}} {{if PLAN_STARTED_AT}}至{{/if}}  {{cutText:DEADLINE_AT 12 ''}}  {{if DEADLINE_AT}} <span class="label label-info label-outline">{{call:PLAN_STARTED_AT DEADLINE_AT fn='getDays'}}天</span> {{/if}}</div> </h2>
   			  <div class="layui-colla-content {{if #index==0}}layui-show{{/if}}">
				 <table class="layui-table" lay-skin="row" lay-size="sm">
					<thead>
					  <tr>
							<td colspan="2" style="width:130px">状态</td>
							<td style="width:40%">任务名称</td>
							<td style="width:120px">截止时间</td>
							<td style="width:100px">工时</td>
							<td style="width:12%">发起者</td>
							<td style="width:12%">执行者</td>
					   </tr>
					</thead>
					<tbody>
 						 {{for #parent.parent.data.taskList}}
							{{if #parent.parent.data.GROUP_ID==GROUP_ID}}
								<tr style="cursor:pointer;" onclick="projectTaskDetail('{{:TASK_ID}}','{{:TASK_STATE}}','{{:CREATOR}}','{{:ASSIGN_USER_ID}}')">
									<td style="width:30px">{{:#getIndex()+1}}</td>
									<td style="width:100px">{{call:TASK_STATE fn='taskStateLabel'}}</td>
									<td style="width:40%">{{cutText:TASK_NAME 80}}</td>
									<td style="width:120px">{{cutText:DEADLINE_AT 12 ''}}</td>
									<td style="width:100px">{{call:PLAN_STARTED_AT DEADLINE_AT fn='getDays'}}天</td>
									<td style="width:12%">{{call:CREATOR fn='getUserName'}}</td>
									<td style="width:12%">{{call:ASSIGN_USER_ID fn='getUserName'}}</td>
								</tr>
							 {{/if}}
						 {{/for}}
						<tr>
							<td colspan="7"><button onclick="addGroupTask('{{:GROUP_ID}}','{{:GROUP_NAME}}')" class="btn btn-link btn-sm pull-left ml-20" type="button"><i class="layui-icon layui-icon-addition"></i>添加任务</button></td>
						</tr>
					</tbody>

				</table>
			  </div>
  			</div>
		   {{/for}}
		</div>
		<button onclick="initTaskGroup()" class="btn btn-link btn-sm pull-left ml-10" type="button"><i class="layui-icon layui-icon-addition"></i>初始化分组</button>
		<button onclick="updateTaskGroup('')" class="btn btn-link btn-sm pull-left ml-10" type="button"><i class="layui-icon layui-icon-addition"></i>添加分组</button>
</script>