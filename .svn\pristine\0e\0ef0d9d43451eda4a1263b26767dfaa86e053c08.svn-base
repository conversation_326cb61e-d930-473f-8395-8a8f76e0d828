package com.yunqu.work.servlet.common;

import java.util.List;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.service.FlowService;

@Before(AuthInterceptor.class)
@Path(value = "/aiEditor")
public class AiEditorController extends BaseController {

	public void l() {
		String id = getPara();
		set("id",id);
		Record record = Db.findFirst("select * from yq_ai_editor where doc_id = ?", id);
		if(record==null) {
			renderHtml(id+"不存在");
			return;
		}
		String docAuth = record.getStr("doc_auth");
		String linkAuth = record.getStr("link_auth");
		String createUserId = record.getStr("create_user_id");
		if(!getUserId().equalsIgnoreCase(createUserId)) {
			if(!"any".equals(docAuth)) {
				renderHtml("没开启公开访问权限");
				return;
			}
			Record logRecord = new Record();
			logRecord.set("log_id", RandomKit.uuid());
			logRecord.set("fk_id",id);
			logRecord.set("look_by", getUserId());
			logRecord.set("look_name", getNickName());
			logRecord.set("look_time",EasyDate.getCurrentDateString());
			logRecord.set("url",getRequest().getRequestURL().toString());
			logRecord.set("agent_info",getRequest().getHeader("user-agent"));
			logRecord.set("ip",WebKit.getIP(getRequest()));
			logRecord.set("type","AiEditor");
			Db.save("yq_look_log", "log_id", logRecord);
			if("read".equals(linkAuth)) {
				set("readonlyContent", "0");
			}else {
				set("readonlyContent", "1");
			}
		}else {
			set("readonlyContent", "1");
		}
		this.set("hisVersionCount", Db.queryInt("select count(1) from yq_ai_editor_his where doc_id = ?", id));
		set("docInfo", record);
		set("hisVersion", "0");
		renderJsp("/pages/doc/aieditor.jsp");
	}
	
	public void hisVersion() {
		String hisId = getPara();
		Record record = Db.findFirst("select t2.* from yq_ai_editor t1,yq_ai_editor_his t2 where t2.his_id = ? and t1.doc_id = t2.doc_id", hisId);
		if(record==null) {
			renderHtml(hisId+"不存在");
			return;
		}
		this.set("hisVersionCount", Db.queryInt("select count(1) from yq_ai_editor_his where doc_id = ?", record.getStr("doc_id")));
		set("docInfo", record);
		set("hisId", hisId);
		set("readonlyContent", "0");
		set("hisVersion", "1");
		renderJsp("/pages/doc/aieditor.jsp");
	}
	
	public void editorData() {
		Record record = null;
		String id = getPara();
		String hisId = getPara("hisId");
		if(StringUtils.notBlank(hisId)) {
			record = Db.findFirst("select * from yq_ai_editor_his where his_id = ?", hisId);
		}else {
			record = Db.findFirst("select * from yq_ai_editor where doc_id = ?", id);
		}
		renderJson(EasyResult.ok(record));
	}
	
	public void editorHisData() {
		String id = getPara();
		List<Record> list = Db.find("select his_id,create_time,create_user_name,version_num from yq_ai_editor_his where doc_id = ?  order by create_time desc limit 20", id);
		renderJson(EasyResult.ok(list));
	}
	
	public void create() {
		String id = FlowService.getService().getID();
		Record record = new Record();
		record.set("create_time",EasyDate.getCurrentDateString());
		record.set("create_user_id",getUserId());
		record.set("create_user_name",getNickName());
		record.set("update_time",EasyDate.getCurrentDateString());
		record.set("update_user_id",getUserId());
		record.set("update_user_name",getNickName());
		record.set("title","未命名文档");
		record.set("content","");
		record.set("auth","self");
		record.set("doc_id",id);
		if(Db.save("yq_ai_editor", "doc_id", record)) {
			redirect("/aiEditor/l/"+id);
		}else {
			renderHtml("error");
		}
	}
	
	public void saveConfig() {
		JSONObject data = getJSONObject();
		String id = getPara();
		Record record = new Record();
		record.set("update_time",EasyDate.getCurrentDateString());
		record.set("update_user_id",getUserId());
		record.set("update_user_name",getNickName());
		record.set("doc_auth",data.getString("docAuth"));
		record.set("link_auth",data.getString("linkAuth"));
		record.set("public_auth",data.getString("publicAuth"));
		record.set("doc_id",id);
		Db.update("yq_ai_editor", "doc_id", record);
		renderJson(EasyResult.ok());
	}
	
	public void mine() {
		renderJsp("/pages/doc/aieditor-list.jsp");
	}
	
	public void shareInfo() {
		renderJsp("/pages/doc/aieditor-share.jsp");
	}
	
	public void myDocsData() {
		EasySQL sql = new EasySQL();
		sql.append("select t1.*,t2.create_time edit_time from yq_ai_editor t1,yq_ai_editor_his t2 where t1.doc_id = t2.doc_id");
		sql.append(getUserId(),"and t2.create_user_id = ?");
		sql.append("group by t2.doc_id");
		sql.append("order by t2.create_time desc");
		List<Record> list = Db.find(sql.getSQL(), sql.getParams());
		renderJson(EasyResult.ok(list));
	}
	
	private void addHis(String id) {
		Record record = new Record();
		record.set("his_id", RandomKit.uuid());
		record.set("doc_id", id);
		record.set("create_time",EasyDate.getCurrentDateString());
		record.set("create_user_id",getUserId());
		record.set("create_user_name",getNickName());
		record.set("title",getPara("title"));
		record.set("content",getPara("content"));
		Integer versionNum = Db.queryInt("select max(version_num) from yq_ai_editor_his where doc_id = ? ", id);
		int updateCount = 1;
		if(versionNum==null) {
			record.set("version_num", 1);
		}else {
			updateCount = versionNum + 1;
			record.set("version_num",versionNum + 1);
		}
		Db.save("yq_ai_editor_his", "his_id", record);
		Db.update("update yq_ai_editor set update_count = ? where doc_id = ?",updateCount,id);
	}
	
	public void editDoc() {
		Record record = new Record();
		record.set("update_time",EasyDate.getCurrentDateString());
		record.set("update_user_id",getUserId());
		record.set("update_user_name",getNickName());
		record.set("title",getPara("title"));
		record.set("content",getPara("content"));
		record.set("auth",getPara("auth", "self"));
		String id = getPara("id");
		record.set("doc_id",id);
		try {
			Db.update("yq_ai_editor", "doc_id", record);
			addHis(id);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
		Record info = Db.findFirst("select * from yq_ai_editor where doc_id = ?", id);
		renderJson(EasyResult.ok(info));
	}
	
	public void lastEditInfo() {
		String id = getPara();
		Record record = Db.findFirst("select update_user_id,update_user_name,update_time from yq_ai_editor where doc_id = ? ", id);
		record.set("hisVersionCount", Db.queryInt("select count(1) from yq_ai_editor_his where doc_id = ?", id));
		record.set("self_user_id", getUserId());
		record.set("current_date", EasyDate.getCurrentDateString());
		renderJson(EasyResult.ok(record));
	}
	
	public void delDoc() {
		String id = getPara("id");
		Db.update("delete from yq_ai_editor where doc_id = ?", id);
		Db.update("delete from yq_ai_editor_his where doc_id = ?", id);
		renderJson(EasyResult.ok());
	}
	
}
