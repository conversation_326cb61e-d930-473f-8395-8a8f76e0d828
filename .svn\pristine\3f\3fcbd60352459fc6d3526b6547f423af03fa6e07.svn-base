package com.yunqu.work.servlet.flow;

import java.sql.SQLException;
import java.util.Calendar;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowFormModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.FlowService;

@Path("/web/flow")
@Before(AuthInterceptor.class)
public class FlowServlet extends BaseController {
	
	public void index() {
		String flowCode = getPara("id");
		String businessId = getPara("businessId");
		String mode = getPara("mode");
		String queryString = getRequest().getQueryString();
		try {
			if(StringUtils.isBlank(businessId)) {
				businessId = getPara();
			}
			if(StringUtils.isBlank(flowCode)&&StringUtils.notBlank(businessId)) {
				flowCode = this.getQuery().queryForString("select flow_code from yq_flow_apply where apply_id = ?", businessId);
			}
			if(StringUtils.isBlank(flowCode)) {
				renderHtml("id not empty.");
				return;
			}
			keepPara();
			FlowModel flowModel =  ApproveService.getService().getFlow(flowCode);
			if(flowModel==null) {
				renderHtml("流程编码【"+flowCode+"】不存在,请与管理员联系。");
				return;
			}
			set("businessId", businessId);
			
			setAttr("flow", flowModel);
			setAttr("fdata", new JSONObject());
			setAttr("applyInfo",new FlowApplyModel());
			
			int sourceType = flowModel.getSourceType();
			String url = flowModel.getApplyUrl();
			
			if(sourceType==1) {//表单
				url = "/pages/flow/form/common/flow-form.jsp";
				String formId  = flowModel.getFormId();
				FlowFormModel flowFormModel = FlowService.getService().getFlowFormModel(formId);
				setAttr("flowForm", flowFormModel);
			}
			
			if(StringUtils.isNotBlank(businessId)) {
				if(!"edit".equals(mode)) {
					if(sourceType==1) {
						url = "/pages/flow/form/common/flow-detail.jsp";
					}else {
						url = flowModel.getDetailUrl();
					}
				}
				
				FlowApplyModel applyInfo = ApproveService.getService().getApply(businessId);
				setAttr("applyInfo", applyInfo);
				
				String resultId = getPara("resultId");
				if(StringUtils.notBlank(resultId)) {
					ApproveResultModel approveResultModel = ApproveService.getService().getResult(resultId);
					setAttr("resultInfo", approveResultModel);
				}
			}else {
				if("finance_bx".equals(flowCode)) {
					Calendar calendar=Calendar.getInstance();
					int day = calendar.get(Calendar.DAY_OF_MONTH);
					if(day>=27) {
						renderHtml("每月27号费用报销系统自动关闭，次月1号开启。");
						return;
					}
				}
			}
			
			String tableName = flowModel.getTableName();
			if(StringUtils.notBlank(tableName)&&StringUtils.notBlank(businessId)) {
				JSONObject  data = this.getQuery(3).queryForRow("select * from "+tableName+" where business_id = ?", new Object[] {businessId}, new JSONMapperImpl());
				setAttr("bdata", data);
			}
			if(StringUtils.isBlank(url)) {
				renderHtml("url not empty.");
				return;
			}
			url = url.replace("/yq-work", "");
			if(url.indexOf("?")==-1&&StringUtils.notBlank(queryString)) {
				url = url +"?"+queryString;
			}else if(url.indexOf("?")==-1&&StringUtils.notBlank(businessId)){
				url = url +"?businessId="+businessId;
			}
			renderJsp(url);
		} catch (SQLException e) {
			this.error(null, e);
			renderHtml(e.getMessage());
		}
	}
	
	public void getFlow() {
		String flowCode = getJsonPara("flowCode");
		if(StringUtils.isBlank(flowCode)){
			renderJson(EasyResult.fail());
			return;
		}
		renderJson(EasyResult.ok(ApproveService.getService().getFlow(flowCode)));
	}
	
	public void cannelApply() {
		String businessId = getJsonPara("businessId");
		if(StringUtils.isBlank(businessId)){
			renderJson(EasyResult.fail());
			return;
		}
		try {
			this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ? where apply_id = ?",FlowConstants.FLOW_STAT_CANNEL,businessId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void delApply() {
		JSONObject params = getJSONObject();
		String businessId = params.getString("businessId");
		String flowCode = params.getString("flowCode");
		if(StringUtils.isBlank(businessId)||StringUtils.isBlank(flowCode)){
			renderJson(EasyResult.fail());
			return;
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		try {
			String itemTable = flow.getItemTableName();
			if(StringUtils.notBlank(itemTable)) {
				this.getQuery().executeUpdate("delete from "+itemTable+" where business_id = ?",businessId);
			}
			this.getQuery().executeUpdate("delete from "+flow.getTableName()+" where business_id = ?",businessId);
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id = ?",businessId);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id = ?",businessId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderJson(EasyResult.ok());
		return;
	}
	
	
	public void updateCategory(){
		EasyRecord model = new EasyRecord("yq_flow_category","flow_code");
		try {
			model.setColumns(getJSONObject("conf"));
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void addCategory(){
		EasyRecord model = new EasyRecord("yq_flow_category","flow_code");
		try {
			model.setColumns(getJSONObject("conf"));
			model.set("create_time", EasyDate.getCurrentDateString());
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
}
