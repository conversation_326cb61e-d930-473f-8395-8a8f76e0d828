<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title></title>
    <style>
        .form-horizontal {
            width: 100%;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="incomeConfirmEditForm" class="form-horizontal" data-mars="IncomeConfirmDao.record" autocomplete="off" data-mars-prefix="confirm.">
        <input type="hidden" value="${param.confirmId}" name="confirm.CONFIRM_ID"/>
        <input type="hidden" value="${param.contractId}" name="confirm.CONTRACT_ID"/>
        <input type="hidden" value="${param.confirmType}" name="confirm.CONFIRM_TYPE"/>
        <table class="table table-vzebra">
            <tbody>
            <tr style="display: none">
                <td width="120px">开票编号</td>
                <td colspan="3">
                    <input type="text" name="confirm.INVOICE_ID" class="form-control">
                </td>
            </tr>
            <tr>
                <td class="required">日期</td>
                <td>
                    <input type="text" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="confirm.CONFIRM_DATE" class="form-control input-sm Wdate">
                </td>
                <td width="120px">来源</td>
                <td>
                    <select name="confirm.SOURCE" class="form-control">
                        <option value="">请选择</option>
                        <option value="来源1">来源1</option>
                        <option value="来源2">来源2</option>
                        <option value="来源3">来源3</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>收入确认阶段</td>
                <td colspan="3">
                    <SELECT name="confirm.INCOME_STAGE_ID" onchange="incomeConfirmEdit.getStageAmount(this.value)" <c:if test="${!empty param.confirmId}"> disabled </c:if> class="form-control">
                        <option value="">请选择</option>
                    </SELECT>
                </td>
            </tr>
            <tr>
                <td class="required">确认金额</td>
                <td colspan="3">
                    <input type="number" data-rules="required" name="confirm.AMOUNT_WITH_TAX" class="form-control" placeholder="选择收入确认阶段后自动获取" oninput="incomeConfirmEdit.limitAmount(this.value)">
                </td>
            </tr>
            <tr style="display: none">
                <td class="required">税金</td>
                <td>
                    <input type="number" value="0" data-rules="required" name="confirm.TAX" class="form-control" placeholder="选择收入确认阶段后自动获取">
                </td>
                <td class="required">税后金额</td>
                <td>
                    <input type="number" value="0" data-rules="required" name="confirm.AMOUNT_NO_TAX" class="form-control" placeholder="选择收入确认阶段后自动获取">
                </td>
            </tr>
            <tr>
                <td>摘要</td>
                <td colspan="3">
                    <textarea name="confirm.CONFIRM_DESC" class="form-control" rows="3"></textarea>
                </td>
            </tr>
            <tr>
                <td>发票来源</td>
                <td>
                    <input type="text" name="confirm.INVOICE_SOURCE" class="form-control input-sm">
                </td>
                <td>开票客户</td>
                <td>
                    <input type="hidden" name="confirm.CUST_NO">
                    <input type="hidden" name="confirm.CUST_ID" id="custId">
                    <input type="text" onclick="singleCust(this);" readonly="readonly" placeholder="请点击选择客户" name="confirm.CUSTOMER_NAME" class="form-control input-sm">
                </td>
            </tr>
            </tbody>
        </table>
        <br>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px"
                    onclick="incomeConfirmEdit.ajaxSubmitForm()"> 保 存
            </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px"
                    onclick="popup.layerClose(this);"> 关闭
            </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("incomeConfirmEdit");

        incomeConfirmEdit.confirmId = '${param.confirmId}';
        incomeConfirmEdit.isNew = '${param.isNew}';
        incomeConfirmEdit.contractId = '${param.contractId}';
        incomeConfirmEdit.confirmType = '${param.confirmType}';
        incomeConfirmEdit.amountLimit = 0;

        $(function () {
            $("#incomeConfirmEditForm").render({
                success: function (result) {
                    $('select[name="confirm.INCOME_STAGE_ID"]').attr("data-mars", "IncomeStageDao.selectIncomeStage");
                    $('select[name="confirm.INCOME_STAGE_ID"]').render({
                        data: {contractId: incomeConfirmEdit.contractId, confirmType: incomeConfirmEdit.confirmType},
                        success:function(){
                            if (incomeConfirmEdit.confirmId && incomeConfirmEdit.isNew != '1') {
                                var recordData = result["IncomeConfirmDao.record"] && result["IncomeConfirmDao.record"].data;
                                $('select[name="confirm.INCOME_STAGE_ID"]').val(recordData.INCOME_STAGE_ID);
                            }
                        }
                    });
                }
            });
        });

        incomeConfirmEdit.ajaxSubmitForm = function () {
            if (form.validate("#incomeConfirmEditForm")) {
                if (incomeConfirmEdit.isNew == '1') {
                    incomeConfirmEdit.insertData();
                } else if (incomeConfirmEdit.confirmId) {
                    incomeConfirmEdit.updateData();
                } else {
                    incomeConfirmEdit.insertData();
                }
            }
        }

        incomeConfirmEdit.insertData = function () {
            var data = form.getJSONObject("#incomeConfirmEditForm");
            ajax.remoteCall("${ctxPath}/servlet/incomeConfirm?action=add", data, function (result) {
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        if (incomeConfirmEdit.confirmType == 'A') {
                            reloadIncomeConfirmList();
                        } else if (incomeConfirmEdit.confirmType == 'B') {
                            reloadIncomeConfirmListB();
                        }
                        reloadIncomeStageList();
                    });
                } else {
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        incomeConfirmEdit.updateData = function () {
            var data = form.getJSONObject("#incomeConfirmEditForm");
            data["confirm.INCOME_STAGE_ID"] = $("SELECT[name='confirm.INCOME_STAGE_ID']").val();
            ajax.remoteCall("${ctxPath}/servlet/incomeConfirm?action=update", data, function (result) {
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        if (incomeConfirmEdit.confirmType == 'A') {
                            reloadIncomeConfirmList();
                        } else if (incomeConfirmEdit.confirmType == 'B') {
                            reloadIncomeConfirmListB();
                        }
                    });
                    reloadIncomeStageList();
                } else {
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        incomeConfirmEdit.getTaxRate = function (paymentType) {
            $("#incomeConfirmTaxRate").val(paymentType);
            incomeConfirmEdit.calcTax();
        }

        incomeConfirmEdit.calcTax = function () {
            var taxRate = parseFloat($("#incomeConfirmTaxRate option:selected").text());
            if (isNaN(taxRate)) {
                taxRate = 0;
            }
            var amount = $("#incomeConfirmEditForm input[name='confirm.AMOUNT_WITH_TAX']").val();
            var amountNoTax = amount * 100 / (100 + taxRate);
            $("input[name='confirm.AMOUNT_NO_TAX']").val(amountNoTax.toFixed(2));
            var tax = taxRate * amountNoTax / 100;
            $("input[name='confirm.TAX']").val(tax.toFixed(2));
        }

        incomeConfirmEdit.getStageAmount = function (stageId) {
            if (stageId) {
                var data = {"params": {"incomeStage.INCOME_STAGE_ID": stageId}, "controls": ["IncomeStageDao.record"]};
                ajax.remoteCall("${ctxPath}/webcall", data, function (result) {
                    var stage = result["IncomeStageDao.record"]["data"];
                    if (stage && Object.keys(stage).length > 0) {
                        if (incomeConfirmEdit.confirmType == 'A') {
                            incomeConfirmEdit.amountLimit = Number(stage["UNCONFIRM_AMOUNT_A"]);
                        } else if (incomeConfirmEdit.confirmType == 'B') {
                            incomeConfirmEdit.amountLimit = Number(stage["UNCONFIRM_AMOUNT_B"]);
                        }
                        $('input[name="confirm.AMOUNT_WITH_TAX"]').val(incomeConfirmEdit.amountLimit);
                        $('input[name="confirm.AMOUNT_WITH_TAX"]').attr("max", incomeConfirmEdit.amountLimit);
                    } else {
                        layer.alert("该收入确认阶段信息有误", {icon: 5});
                    }
                })
            }
        }

        incomeConfirmEdit.limitAmount = function (inputAmount) {
            if (inputAmount > incomeConfirmEdit.amountLimit) {
                $('input[name="confirm.AMOUNT_WITH_TAX"]').val(incomeConfirmEdit.amountLimit);
                layer.alert("确认金额不能大于 收入确认阶段剩余未确认的金额：" + incomeConfirmEdit.amountLimit, {icon: 5});
            }else if(inputAmount <= 0){
                $('input[name="confirm.AMOUNT_WITH_TAX"]').val(0);
                layer.alert("确认金额不能小于或等于0!", {icon: 5});
            }
        }

        function selectReviewCallBack(row) {
            $("[name='confirm.CUSTOMER_NAME']").val(row['CUST_NAME']);
            $("[name='confirm.CUST_ID']").val(row['CUST_ID']);
            $("[name='confirm.CUST_NO']").val(row['CUST_NO']);
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>