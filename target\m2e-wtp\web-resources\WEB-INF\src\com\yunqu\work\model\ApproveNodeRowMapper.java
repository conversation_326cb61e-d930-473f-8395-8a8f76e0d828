package com.yunqu.work.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;


public class ApproveNodeRowMapper implements EasyRowMapper<ApproveNodeModel> {

	@SuppressWarnings("unchecked")
	@Override
	public ApproveNodeModel mapRow(ResultSet rs, int rowNum) {
		ApproveNodeModel vo = new ApproveNodeModel();
		try {
			vo.setNodeName(rs.getString("NODE_NAME"));
			vo.setCheckBy(rs.getString("USER_ID"));
			vo.setCheckName(rs.getString("USER_NAME"));
			vo.setNextNodeId(rs.getString("C_NODE_ID"));
			vo.setPrevNodeId(rs.getString("P_NODE_ID"));
			vo.setNodeId(rs.getString("NODE_ID"));
			vo.setFlowCode(rs.getString("FLOW_CODE"));
			vo.setCheckRoleId(rs.getString("ROLE_ID"));
			vo.setCheckType(rs.getInt("CHECK_TYPE"));
			vo.setNodeCode(rs.getString("NODE_CODE"));
			vo.setCcIds(rs.getString("CC_IDS"));
			vo.setCcNames(rs.getString("CC_NAMES"));
			vo.setHourLimit(rs.getInt("HOUR_LIMIT"));
			vo.setCcFlag(rs.getInt("CC_FLAG"));
			vo.setReturnRule(rs.getInt("RETURN_RULE"));
			vo.setSignFlag(rs.getInt("SIGN_FLAG"));
			vo.setOpinionTitle(rs.getString("OPINION_TITLE"));
			vo.setEditField(rs.getString("EDIT_FIELD"));
			vo.setApproveCode(rs.getString("APPROVE_CODE"));
			vo.setSubmitCode(rs.getString("SUBMIT_CODE"));
			vo.setCheckIndex(rs.getInt("CHECK_INDEX"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
