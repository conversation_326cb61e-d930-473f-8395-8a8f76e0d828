<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>菜单</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="FoodDao.foodObj" autocomplete="off" data-mars-prefix="food.">
			<div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
			  <ul class="layui-tab-title">
			    <li class="layui-this">已点清单</li>
			    <li>待点名单</li>
			  </ul>
			  <div class="layui-tab-content">
			    <div class="layui-tab-item layui-show">
			    	<table class="layui-hiden" id="t1"></table>
			    </div>
			    <div class="layui-tab-item">
			    	待开发。。。。
			    </div>
			  </div>
			</div> 
		   <div class="layer-foot text-c">
			      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
		  </div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		
		$(function(){
			layui.use('element', function(){
				var $ = layui.jquery;
				var element = layui.element;
			});
			var myDate = new Date();
			if((myDate.getHours()+1)>=14){
				loadT1(3);
			}else{
				loadT1(2);
			}
		});
		function loadT1(dinnerType){
			$("#editForm").initTable({
				mars:'FoodDao.todayBookFoodList',
				id:'t1',
				page:false,
				skin:'line',
				data:{dinnerType:dinnerType},
				cols: [[
				  {title:'序号',type:'numbers'},
				  {
					    field: 'USERNAME',
						title: '姓名',
						align:'left',
						width:80
				 },{
					    field: 'DEPTS',
						title: '部门',
						align:'left',
						width:115
				 },
	              {
				    field: 'FOOD_NAME',
					title: '菜品',
					align:'left'
				},{
					field:'CREATE_TIME',
					title: '订餐时间',
					align:'center'
				}
				]]}
			);
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>