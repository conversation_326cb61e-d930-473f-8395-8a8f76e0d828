<!-- Generated by SpecStory -->

# 解决弹出层关闭后表格高度问题 (2025-03-07 13:40:48)

_**User**_

@task-list.jsp  @common.js  当弹出层关闭后 layui-table-body layui-table-main 的height一直在改变

---

_**Assistant**_

```thinking
让我分析一下这个问题:

1. 问题描述:
- 在task-list.jsp页面中,当弹出层关闭后,表格主体部分的高度一直在变化
- 涉及到layui-table-body和layui-table-main这两个class的元素

2. 可能的原因:
- 弹出层关闭后触发了resize事件,导致表格重新计算高度
- selectProject_函数中的end回调可能引起了问题
- layui table的自适应高度设置可能有问题

3. 解决思路:
- 检查selectProject_函数中的end回调
- 需要在弹出层关闭后重新固定表格高度
- 可以尝试移除或修改end回调中的alert

4. 具体修改:
- 需要修改common.js中的selectProject_函数
- 移除不必要的alert调试代码
- 可以在end回调中重新计算表格高度

让我来修改代码:
```

```javascript:static/js/common.js
// ... existing code ...

function selectProject_(el,type){
	window.layer = layui.layer;
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	popup.layerShow({
		id:'selectProject',
		full:fullShow(),
		scrollbar:false,
		area:['650px','600px'],
		offset:'20px',
		title:'选择项目',
		url:'/yq-work/pages/flow/select/select-project.jsp',
		data:{sid:id,type:type},
		end:function(){
			// 移除alert,重新渲染表格以修复高度问题
			if(layTables['taskMgrTable']) {
				layTables['taskMgrTable'].reload();
			}
		}
	});
}

// ... existing code ...
```

```thinking
修改说明:

1. 主要修改:
- 移除了end回调中的alert('end')调试代码
- 添加了表格重新加载的逻辑来修复高度问题

2. 修改原理:
- alert会阻塞UI线程,可能导致表格高度计算异常
- 通过重新加载表格来强制重新计算高度

3. 预期效果:
- 弹出层关闭后表格高度应该保持稳定
- 不会出现高度持续变化的问题

4. 其他建议:
- 如果问题仍然存在,可以考虑:
  1. 检查表格初始化时的height配置
  2. 监听window resize事件来调整表格高度
  3. 使用固定高度而不是自适应高度

这个修改应该能解决表格高度不稳定的问题。如果问题仍然存在,可能需要进一步检查表格初始化配置和页面布局结构。
```

---

_**User**_

layer关闭层然后resize 这个bug怎么解决

---

_**Assistant**_

```thinking
让我分析一下这个问题:

1. 问题本质:
- layer弹出层关闭后会触发window的resize事件
- resize事件会导致layui table重复计算高度
- 这是layui layer和table组件之间的一个已知bug

2. 可能的解决方案:
- 方案1: 阻止或延迟resize事件的处理
- 方案2: 在layer关闭后手动设置表格高度
- 方案3: 使用定时器延迟处理resize

3. 最佳解决方案:
- 使用定时器debounce方式处理resize
- 在layer关闭时重置表格高度
- 确保只执行一次resize处理

4. 实现思路:
- 在task-list.jsp中添加resize处理函数
- 使用定时器防抖处理resize事件
- 在layer关闭后手动触发一次resize

让我来编写具体的解决方案:
```

```javascript:WebContent/pages/task/task-list.jsp
// ... existing code ...

<script type="text/javascript">
// 添加在script开始处
var resizeTimer = null;
$(window).on('resize', function() {
    // 清除之前的定时器
    if(resizeTimer) clearTimeout(resizeTimer);
    
    // 设置新的定时器,延迟200ms执行
    resizeTimer = setTimeout(function() {
        // 重新渲染表格
        if(layTables['taskMgrTable']) {
            layTables['taskMgrTable'].reload({
                height: 'full-130'  // 重设高度
            });
        }
    }, 200);
});

// 修改taskMgr.init中的表格初始化
taskMgr.init = function(){
    // ... existing code ...
    
    $("#taskMgrForm").initTable({
        mars:'TaskDao.taskList',
        elem:'#taskMgrTable',
        id:'taskMgrTable',
        autoSort:false,
        toolbar:'#taskBar',
        filter:true,
        limit:30,
        limits:[30,50,100,200,300,500],
        height:'full-130',  // 保持固定高度配置
        defaultToolbar:['filter', 'print', 'exports'],
        // ... other existing options ...
        done: function(result) {
            // ... existing code ...
            
            // 添加resize事件处理
            if(layer.index > 0) {
                setTimeout(function() {
                    $(window).trigger('resize');
                }, 100);
            }
        }
    });
    
    // ... existing code ...
}

// 修改popup.layerShow的调用,添加end回调
function selectProject_(el,type){
    // ... existing code ...
    popup.layerShow({
        // ... existing options ...
        end: function() {
            // 延迟触发resize
            setTimeout(function() {
                $(window).trigger('resize');
            }, 100);
        }
    });
}

// ... rest of existing code ...
</script>
```

```thinking
解决方案说明:

1. 核心解决思路:
- 使用定时器debounce处理resize事件
- 确保layer关闭后只触发一次resize
- 保持表格高度配置的一致性

2. 具体实现:
- 添加全局resize事件监听器
- 使用setTimeout防抖处理
- 在layer关闭后延迟触发resize

3. 优点:
- 避免频繁触发resize事件
- 确保表格高度计算准确
- 解决layer和table的冲突问题

4. 注意事项:
- 延迟时间可以根据实际情况调整
- 需要确保表格初始化时的height配置正确
- 可能需要根据具体场景调整触发时机

5. 其他建议:
- 如果还有问题,可以尝试:
  1. 增加延迟时间
  2. 使用固定像素高度而不是full-130
  3. 监听layer的specific事件来处理resize

这个解决方案应该能有效解决layer关闭后resize导致的表格高度问题。如果仍有问题,可以进一步调整延迟时间或尝试其他触发方式。
```

---



---

