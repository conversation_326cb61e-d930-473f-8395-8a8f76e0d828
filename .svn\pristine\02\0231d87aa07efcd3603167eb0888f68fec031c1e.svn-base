package com.yunqu.work.servlet;

import java.io.IOException;
import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.DESUtil;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.OAService;
import com.yunqu.work.service.XuanService;
import com.yunqu.work.service.ZendaoService;

@WebServlet("/servlet/ehr/*")
public class EhrServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	public void actionForZendao(){
		ZendaoService.getService().run();
	    renderText("ok");
	}
	
	public void actionForOA(){
		OAService.getService().scanOaAcount();
		renderText("ok");
	}
	public EasyResult actionForAddOA(){
		JSONObject jsonObject = getJSONObject();
		String type=jsonObject.getString("type");
		String account=jsonObject.getString("account");
		String pwd=jsonObject.getString("pwd");
		boolean loginResult = OAService.getService().login(account,pwd);
		if(loginResult){
			EasyRecord record = new EasyRecord("YQ_OA_USER","OA_ACCOUNT");
			record.set("OA_ACCOUNT",account);
			record.set("USER_ID",getUserId());
			record.set("UPDATE_TIME",EasyDate.getCurrentDateString());
			record.set("OA_PWD",DESUtil.getInstance().encryptStr(pwd));
			record.set("STATE","ok");
			if("add".equals(type)){
				try {
					this.getQuery().save(record);
				} catch (SQLException e) {
				
				}
			}else{
				try {
					this.getQuery().update(record);
				} catch (SQLException ex) {
					
				}
			}
			return EasyResult.ok("校验成功");
		}else{
			return EasyResult.fail("密码错误");
		}
		
	}
	public void actionForEmail(){
		try {
			Object object=getUserPrincipal().getAttribute("cookie");
			if(object==null){
				Render.renderHtml(getRequest(), getResponse(), "<h1>您没绑定OA账号或绑定失效.</h1>");
				return;
			}
			OAService.getService().emailLoginJump(this.getRequest(),this.getResponse());
		} catch (IOException e) {
		     renderHtml(e.getMessage());
		}
		return;
	}
	public void actionForMyWfDetail(){
		try {
			Object object=getUserPrincipal().getAttribute("cookie");
			if(object==null){
				Render.renderHtml(getRequest(), getResponse(), "<h1>您没绑定OA账号或绑定失效.</h1>");
				return;
			}
			OAService.getService().myWfDetail(this.getRequest(),this.getResponse(),getPara("no"));
		} catch (IOException e) {
			renderHtml(e.getMessage());
		}
		return;
	}
	
	public void actionForXuan(){
		XuanService.getService().run();
		renderText("ok");
	}
	
	
}
