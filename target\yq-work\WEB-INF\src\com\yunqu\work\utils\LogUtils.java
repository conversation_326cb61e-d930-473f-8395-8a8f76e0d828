package com.yunqu.work.utils;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;

import com.yunqu.work.base.Constants;

public class LogUtils {

	public static Logger getLogger(){
		return LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME);
	}
	
	public static Logger getWebhook(){
		return LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME+"-webhook");
	}
	
	public static Logger getLLM(){
		return LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME+"-llm");
	}
	
	public static Logger getZt(){
		return LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME+"-zt");
	}
	
	public static Logger getAccess(){
		return LogEngine.getLogger(Constants.APP_NAME, "Access");
	}
}
