package com.yunqu.work.dao.project;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ProjectModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="ProjectDao")
public class ProjectDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		ProjectModel model=new ProjectModel();
		model.setColumns(getParam("project"));
		if(StringUtils.notBlank(model.getProjectId())){
			JSONObject jsonObject=queryForRecord(model);
			String sql="select group_concat(t1.USER_ID) a,group_concat(t2.username) b from  yq_project_team t1,"+Constants.DS_MAIN_NAME+".easi_user t2 where t1.user_id = t2.user_id and PROJECT_ID = ?";
			try {
				JSONObject row = this.getQuery().queryForRow(sql, new Object[]{model.getProjectId()},new JSONMapperImpl());
				String userIdStr = row.getString("A");
				String userNameStr = row.getString("B");
				if(StringUtils.notBlank(userIdStr)){
					jsonObject.put("teams",userIdStr);
					jsonObject.put("teamNames",userNameStr);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
			if(StringUtils.notBlank(model.getString("PROJECT_TYPE"))){
				LookLogService.getService().addLog(getUserId(),model.getProjectId(),"project",request);
			}
			return jsonObject;
		}else{
			return getJsonResult(new JSONObject());
		}
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select PROJECT_ID,PROJECT_NAME from yq_project where is_delete = 0");
	}
	
	@WebControl(name="modules",type=Types.DICT)
	public JSONObject modules(){
		String projectId = param.getString("projectId");
		return getDictByQuery("select module_id,module_name from yq_project_module where project_id = ?",projectId);
	}
	
	@WebControl(name="myProjectDic",type=Types.DICT)
	public JSONObject myProjectDic(){
		return getDictByQuery("select t1.PROJECT_ID,t1.PROJECT_NAME from yq_project t1 INNER JOIN yq_project_team t2 on t2.PROJECT_ID=t1.PROJECT_ID where t2.USER_ID= ?",getUserId());
	}
	

	@WebControl(name="selectProjectList",type=Types.LIST)
	public JSONObject selectProjectList(){
		int dataType = param.getIntValue("dataType");
		EasySQL sql=getEasySQL("select t1.* from YQ_PROJECT t1 where 1=1");
		if(dataType==1) {
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,t1.po)");
			sql.append("or");
			sql.append(getUserId(),"find_in_set(?,t1.project_po)");
			sql.append(")");
		}else if(dataType==2){
			sql=getEasySQL("select t1.* from yq_project t1 INNER JOIN yq_project_team t2 ON t2.PROJECT_ID=t1.PROJECT_ID where 1=1");
			sql.append(getUserId(),"and t2.user_id=  ? ");
		}else if(dataType==3) {
			sql=getEasySQL("select t1.* from yq_project t1,yq_task t2 where t1.project_id = t2.project_id");
			sql.append(getUserId(),"and t2.assign_user_id =  ? ");
		}else if(dataType==4) {
			sql=getEasySQL("select t1.* from yq_project t1,yq_task t2 where t1.project_id = t2.project_id");
			sql.append(getUserId(),"and t2.creator = ? ");
		}else if(dataType==5) {
			sql=getEasySQL("select t1.* from yq_project t1,yq_weekly t2,yq_weekly_project t3 where t1.project_id = t3.project_id and t2.weekly_id = t3.weekly_id");
			sql.append(getUserId(),"and t2.creator = ? ");
		}
		sql.append("and t1.is_delete = 0");
		sql.append(20,"and t1.project_state < ?");
		sql.appendLike(param.getString("projectName"),"and t1.project_name like ?");
		if(dataType==3||dataType==4||dataType==5) {
			sql.append("group by t1.project_id");
			sql.append("order by t2.create_time desc");
		}else {
			sql.append("order by t1.last_task_time desc");
		}
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		int pageIndex = this.param.getIntValue("pageIndex");
		if(pageIndex==1&&dataType==0) {
			JSONObject other = new JSONObject();
			other.put("PROJECT_NAME","其他");
			other.put("PROJECT_NO","9999999999999");
			other.put("PROJECT_ID","9999999999999");
			result.getJSONArray("data").add(0, other);
		}
		return result;
	}
	
	/**
	 * 全部项目
	 * @return
	 */
	@WebControl(name="projectList",type=Types.LIST)
	public JSONObject projectList(){
		if(isSuperUser()||getUserPrincipal().isRole("PROJECT_MANAGER")){
			EasySQL sql=getEasySQL("select t1.*,");
			sql.append(10,"SUM(CASE WHEN t2.task_state = ? THEN 1 ELSE 0 END) AS a,");
			sql.append(20,"SUM(CASE WHEN t2.task_state = ? THEN 1 ELSE 0 END) AS b,");
			sql.append(30,"SUM(CASE WHEN t2.task_state = ? THEN 1 ELSE 0 END) AS c,");
			sql.append(30,"SUM(CASE WHEN t2.task_state < ? THEN 1 ELSE 0 END) AS d,");
			sql.append(30,"SUM(CASE WHEN t2.task_state >= ? THEN 1 ELSE 0 END) AS k,");
			sql.append(40,"SUM(CASE WHEN t2.task_state >= ? THEN 1 ELSE 0 END) AS f,");
			sql.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t2.deadline_at < ? and t2.task_state < 30  THEN 1 ELSE 0 END) AS g,");
			sql.append("SUM(CASE WHEN t2.task_state > 0 THEN 1 ELSE 0 END) AS h");
			
			sql.append("from yq_project t1 LEFT JOIN yq_task t2 on t1.project_id = t2.project_id  where 1=1");
			sql.append("and t1.is_delete = 0");
			sql.appendLike(param.getString("projectName"),"and t1.project_name like ?");
			sql.append(param.getString("projectState"),"and t1.project_state = ?");
			sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
			sql.appendLike(param.getString("po"),"and t1.PO_NAME like ?");
			sql.append(param.getString("projectType"),"and t1.project_type = ?");
			sql.append(param.getString("deptId"),"and t1.dept_id = ?");
			sql.append("group by t1.project_id");
			
			String field=param.getString("field");
			String order=param.getString("order");
			if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
				sql.append("order by").append(field).append(order);
			}else{
//				sql.append("order by d desc,h desc");
				sql.append("order by t1.last_task_time desc");
			}
			return queryForPageList(sql.getSQL(), sql.getParams());
		}else{
			return emptyPage();
		}
	}
	/**
	 * 我创建的项目
	 * @return
	 */
	@WebControl(name="myCreateProjectList",type=Types.LIST)
	public JSONObject myCreateProjectList(){
		EasySQL sql=getEasySQL("select count(1) count,t1.* from yq_project t1 INNER JOIN yq_task t2 on t1.project_id=t2.project_id  where 1=1");
		sql.append("and t1.is_delete = 0");
		sql.append(getUserId(),"and t2.CREATOR=  ? ");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("GROUP BY t1.project_id");
		sql.append("order by count desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 我负责的项目
	 * @return
	 */
	@WebControl(name="myMangerProjectList",type=Types.LIST)
	public JSONObject myMangerProjectList(){
		EasySQL sql=getEasySQL("select t1.* from yq_project t1  where 1=1");
		sql.append("and t1.is_delete = 0 and");
		sql.append(getUserId(),"(t1.PO=  ? or");
		sql.append(getUserId()," t1.PROJECT_PO=  ? )");
		
		sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
		sql.appendLike(param.getString("projectName"),"and project_name like ?");
		sql.append(param.getString("projectState"),"and project_state = ?");
		sql.append("order by last_task_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 我关注的项目
	 * @return
	 */
	@WebControl(name="myFavoriteProject",type=Types.LIST)
	public JSONObject myFavoriteProject(){
		EasySQL sql=getEasySQL("select t1.*,t2.favorite_time from yq_project t1 INNER JOIN yq_favorite t2 on t2.fk_id=t1.PROJECT_ID where  1=1");
		sql.append(getUserId(),"and t2.favorite_by= ?");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.append("and t1.is_delete = 0");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("ORDER BY t2.favorite_time desc ");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 我参与的项目
	 * @return
	 */
	@WebControl(name="myJoinProjectList",type=Types.LIST)
	public JSONObject myProjectList(){
		EasySQL sql=getEasySQL("select t1.* from yq_project t1 INNER JOIN yq_project_team t2 ON t2.PROJECT_ID=t1.PROJECT_ID where 1=1");
		sql.append(getUserId(),"and t2.USER_ID=  ? ");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("and t1.is_delete = 0");
		sql.append("order by t1.CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目团队
	 * @return
	 */
	@WebControl(name="projectTeams",type=Types.LIST)
	public JSONObject projectTeams(){
		EasySQL sql=getEasySQL("select t2.USERNAME,t2.MOBILE,t2.EMAIL,t2.DEPTS,t3.LAST_LOGIN_TIME,t1.* from yq_project_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user_login t3 on t3.USER_ID=t2.USER_ID where 1 = 1");
		sql.append("and t2.STATE = 0");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",false);
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目团队
	 * @return
	 */
	@WebControl(name="projectTeamsDict",type=Types.DICT)
	public JSONObject projectTeamsDict(){
		EasySQL sql=getEasySQL("select t2.USER_ID,t2.USERNAME from yq_project_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID where 1 = 1");
		sql.append("and t2.STATE = 0");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",false);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目周报
	 * @return
	 */
	@WebControl(name="projectWeeklys",type=Types.LIST)
	public JSONObject projectWeeklys(){
		EasySQL sql=getEasySQL("select t2.USERNAME,t2.MOBILE,t2.EMAIL,t2.DEPTS,t1.* from YQ_PROJECT_WEEKLY t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.CREATOR where 1 = 1");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",false);
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目状态更新日志
	 * @return
	 */
	@WebControl(name="projectStateLog",type=Types.LIST)
	public JSONObject projectStateLog(){
		EasySQL sql=getEasySQL("select * from yq_project_state_log where 1 = 1");
		sql.append(param.getString("projectId")," and PROJECT_ID = ?",false);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目模块
	 * @return
	 */
	@WebControl(name="projectModule",type=Types.LIST)
	public JSONObject projectModule(){
		EasySQL sql=getEasySQL("select t1.* from yq_project_module t1  where 1 = 1");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",true);
		JSONObject result=queryForList(sql.getSQL(), sql.getParams());
		JSONArray array=result.getJSONArray("data");
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("MODULE_NAME", "点击这添加模块");
		array.add(jsonObject);
		result.put("data",array);
		return result;
	}
	

		
	@WebControl(name="fileListDetail",type=Types.TEMPLATE)
	public JSONObject fileListDetail(){
		EasySQL sql = new EasySQL("select  t2.project_name,t2.project_no,t1.* from yq_files t1,yq_project t2 where 1=1");
		sql.append("and t1.fk_id = t2.project_id");
		if(!hasRole("PROJECT_MANAGER")&&!isSuperUser()) {
			sql.append(getUserId(),"and t2.project_id in (select t3.PROJECT_ID from yq_project_team t3 where t3.PROJECT_ID = t2.PROJECT_ID and t3.USER_ID = ?)");
		}
		sql.append("project","and t1.source = ?");
		sql.appendLike(param.getString("projectNo"),"and t2.project_no like ? ");
		sql.appendLike(param.getString("projectName"),"and t2.project_name like ? ");
		sql.append(" order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectLink",type=Types.TEMPLATE)
	public JSONObject projectLink(){
		EasySQL sql=getEasySQL("select  t2.project_name,t2.project_no,t1.* from yq_project_link  t1,yq_project t2 where 1=1");
		sql.append("and t1.project_id = t2.project_id");
		if(!hasRole("PROJECT_MANAGER")&&!isSuperUser()) {
			sql.append(getUserId(),"and t2.project_id in (select t3.PROJECT_ID from yq_project_team t3 where t3.PROJECT_ID = t2.PROJECT_ID and t3.USER_ID = ?)");
		}
		sql.appendLike(param.getString("projectNo"),"and t2.project_no like ? ");
		sql.appendLike(param.getString("projectName"),"and t2.project_name like ? ");
		sql.append(0,"and state = ?");
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}	
		
		

}
