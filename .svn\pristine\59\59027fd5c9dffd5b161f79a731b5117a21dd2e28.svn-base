<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>收入阶段详情</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="incomeStageDetailForm" class="form-horizontal" data-mars="IncomeStageDao.record"  data-text-model="true" autocomplete="off" data-mars-prefix="incomeStage.">
        <input type="hidden" value="${param.incomeStageId}" name="incomeStage.INCOME_STAGE_ID"/>
        <input type="hidden" value="${param.incomeStageId}" name="incomeStageId"/>
        <input type="hidden" value="${param.contractId}" name="incomeStage.CONTRACT_ID"/>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td width="140px">阶段名称</td>
                <td>
                    <input type="text" name="incomeStage.STAGE_NAME" data-rules="required" class="form-control">
                </td>
                <td width="140px">计划完成日期</td>
                <td>
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="incomeStage.PLAN_COMP_DATE" class="form-control input-sm Wdate">
                </td>
            </tr>
            <tr>
                <td>对应合同阶段</td>
                <td colspan="3">
                    <table style="width: 95%" id="stageListTable"></table>
                </td>
            </tr>
            <tr>
                <td>计划收款金额(含税)</td>
                <td>
                    <input type="text" name="incomeStage.PLAN_AMOUNT" readonly data-rules="required" class="form-control input-sm" placeholder="选择合同阶段后自动计算">
                </td>
                <td>占合同总额比例(%)</td>
                <td>
                    <input type="text" name="incomeStage.RATIO" readonly data-rules="required"  class="form-control" placeholder="选择合同阶段后自动计算">
                </td>
            </tr>
            <tr>
                <td>信用期（天）</td>
                <td>
                    <input type="text" name="incomeStage.CREDIT_PRD" class="form-control">
                </td>
                <td>预留比例(%)</td>
                <td>
                    <input type="text" name="incomeStage.YULIU_RATIO" class="form-control">
                </td>
            </tr>
            <tr>
                <td>已审核</td>
                <td>
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio"  value="1" name="incomeStage.CHECKED_FLAG"><span>√</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="0" name="incomeStage.CHECKED_FLAG"><span>×</span>
                    </label>
                </td>
                <td>硬件已确认</td>
                <td>
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio"  value="1" name="incomeStage.HARDWARE_CONF_FLAG"><span>√</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="0" name="incomeStage.HARDWARE_CONF_FLAG"><span>×</span>
                    </label>
                </td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <input type="text"  name="incomeStage.REMARK" class="form-control">
                </td>
            </tr>
            <tr>
                <td>最后更新时间</td>
                <td colspan="3">
                    <input type="text" name="incomeStage.UPDATE_TIME" class="form-control">
                </td>
            </tr>
            <tr>
                <td>已确认金额A</td>
                <td>
                    <span id="detailConfirmA"></span>
                </td>
                <td>未确认金额A</td>
                <td>
                    <input type="text" name="incomeStage.UNCONFIRM_AMOUNT_A" class="form-control">
                </td>
            </tr>
            <tr>
                <td>收入确认A</td>
                <td colspan="3">
                    <table style="width: 95%" id="detailConfirmListA"></table>
                </td>
            </tr>
            <tr>
                <td>已确认金额B</td>
                <td>
                    <span id="detailConfirmB"></span>
                </td>
                <td>未确认金额B</td>
                <td>
                    <input type="text" name="incomeStage.UNCONFIRM_AMOUNT_B" class="form-control">
                </td>
            </tr>
            <tr>
                <td>收入确认B</td>
                <td colspan="3">
                    <table style="width: 95%" id="detailConfirmListB"></table>
                </td>
            </tr>
            </tbody>
        </table>
        <br>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="IncomeStageEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll();"> 关闭 </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        var detailContractStageList = {
            init:function(){
                $("#incomeStageDetailForm").initTable({
                    mars:'ContractStageDao.stageList',
                    id:'stageListTable',
                    page:false,
                    cols: [[
                        {
                            field: 'STAGE_NAME',
                            title: '阶段名称',
                            align:'left',
                            width:100
                        },{
                            field:'RCV_DATE',
                            title:'应收日期',
                            width:100
                        },{
                            field:'PLAN_COMP_DATE',
                            title:'计划完成日期',
                            width:100
                        },{
                            field:'RATIO',
                            title:'比例(%)',
                            width:100,
                        },{
                            field:'PLAN_RCV_AMT',
                            title:'计划收款金额',
                            width:120,
                            align:'left',
                        }
                    ]]
                });
            },
        }

        var detailConfirmListA = {
            init:function(){
                $("#incomeStageDetailForm").initTable({
                    mars:'IncomeConfirmDao.incomeConfirmList',
                    id:'detailConfirmListA',
                    data:{'confirmType':'A'},
                    page:false,
                    cols: [[
                        {
                            field:'CONFIRM_DATE',
                            title:'日期',
                            minWidth:80
                        },{
                            field:'SOURCE',
                            title:'来源',
                            align:'left',
                            minWidth:80
                        },{
                            field:'AMOUNT_WITH_TAX',
                            title:'金额(含税)',
                            minWidth:100,
                            totalRow:true,
                        },{
                            field:'CONFIRM_DESC',
                            title:'摘要',
                        },
                    ]],
                    done:function(data){
                        var totalAmount = 0;
                        $.each(data.data, function(index, item) {
                            var amountWithTax = parseFloat(item.AMOUNT_WITH_TAX);
                            totalAmount += amountWithTax;
                        });
                        totalAmount = totalAmount.toFixed(2);
                        $('#detailConfirmA').text(totalAmount);
                    }
                })
            }
        }

        var detailConfirmListB = {
            init:function(){
                $("#incomeStageDetailForm").initTable({
                    mars:'IncomeConfirmDao.incomeConfirmList',
                    id:'detailConfirmListB',
                    data:{'confirmType':'B'},
                    page:false,
                    cols: [[
                        {
                            field:'CONFIRM_DATE',
                            title:'日期',
                            minWidth:80
                        },{
                            field:'SOURCE',
                            title:'来源',
                            align:'left',
                            minWidth:80
                        },{
                            field:'AMOUNT_WITH_TAX',
                            title:'金额(含税)',
                            minWidth:100,
                            totalRow:true,
                        },{
                            field:'CONFIRM_DESC',
                            title:'摘要',
                        },
                    ]],
                    done:function(data){
                        var totalAmount = 0;
                        $.each(data.data, function(index, item) {
                            var amountWithTax = parseFloat(item.AMOUNT_WITH_TAX);
                            totalAmount += amountWithTax;
                        });
                        totalAmount = totalAmount.toFixed(2);
                        $('#detailConfirmB').text(totalAmount);
                    }
                })
            }
        }

        $(function(){
            $("#incomeStageDetailForm").render({success:function(result){
                    detailContractStageList.init();
                    detailConfirmListA.init();
                    detailConfirmListB.init();
                }});
        });
    </script>

</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>