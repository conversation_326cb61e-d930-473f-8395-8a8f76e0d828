<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>业务平台详情</title>
    <style>
        .base-info tr td:nth-child(odd) {
            text-align: right;
            width: 80px;
            font-weight: 500;
            color: #000000;
            background-color: #f8f8f8;
        }
        .layui-link{color: #5d99cd;}
        .close-el{display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;}
        .date-inline{
            display: inline-block !important;
            width: 120px !important;
        }
    </style>
</EasyTag:override>

<EasyTag:override name="content">
    <form id="PlatformDetailForm">
        <input type="hidden" value="${param.platformId}" id="platformId" name="platformId"/>
        <input type="hidden" value="${param.platformId}" name="fkId"/>
        <div style="padding: 5px" class="ibox-content"  data-mars="BusinessPlatformDao.getPlatform" data-mars-prefix="platform.">
            <div class="layui-row" style="padding:6px 15px;">
                <div class="layui-col-md10">
                    <i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i>
                    <span style="font-size: 18px;" name='platform.PLATFORM_NAME'></span>
                </div>
                <div class="layui-col-md2">
                    <div class="op-right pull-right mr-50">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-default btn-xs edit-btn" onclick="editPlatform();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
                        </div>
                    </div>
                    <div class="close-el" onclick="closePage(this)"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
                </div>
            </div>
            <div class="layui-row" style="padding:10px 15px;">
                <div class="layui-col-md2">
                    <span class="h-title">平台编号</span>
                    <span class="h-value" name="platform.PLATFORM_CODE"></span>
                </div>
                <div class="layui-col-md2">
                    <span class="h-title">平台类型</span>
                    <span class="h-value" name="platform.PLATFORM_TYPE_NAME"></span>
                </div>
                <div class="layui-col-md3">
                    <span class="h-title">状态</span>
                    <span class="h-value" name="platform.STATUS" data-match="{0:'启用',1:'停用'}"></span>
                </div>
                <div class="layui-col-md2">
                    <span class="h-title">更新时间</span>
                    <span class="h-value" name="platform.UPDATE_TIME"></span>
                </div>
            </div>
        </div>
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-tab layui-tab-card" lay-filter="contentFilter" style="border-top: 1px solid #ddd;">
                    <ul class="layui-tab-title">
                        <li lay-id="A" class="layui-this">基本信息</li>
                        <li lay-id="C">合同(<span name="extend.CONTRACT_COUNT">0</span>)</li>
                        <li lay-id="B">商机(<span name="extend.BUSINESS_COUNT">0</span>)</li>
                        <li lay-id="H">客户订购(<span name="extend.ORDER_COUNT">0</span>)</li>
                        <li lay-id="D">销售价格(<span name="extend.PRICE_COUNT">0</span>)</li>
                        <li lay-id="E">成本(<span name="extend.COST_COUNT">0</span>)</li>
                        <li lay-id="F">发票(<span name="extend.INVOICE_COUNT">0</span>)</li>
                        <li lay-id="G">结算(<span name="extend.SETTLE_COUNT">0</span>)</li>
                    </ul>
                    <div class="layui-tab-content" data-mars="BusinessPlatformDao.getPlatform" data-mars-prefix="platform." style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
                        <div class="layui-tab-item layui-show">
                            <table class="table table-vzebra">
                                <tr>
                                    <td width="120">平台名称</td>
                                    <td name="platform.PLATFORM_NAME"></td>
                                    <td width="120">平台编号</td>
                                    <td name="platform.PLATFORM_CODE"></td>
                                </tr>
                                <tr>
                                    <td>平台类型</td>
                                    <td name="platform.PLATFORM_TYPE_NAME"></td>
                                    <td>状态</td>
                                    <td name="platform.STATUS" data-match="{0:'启用',1:'停用'}"></td>
                                </tr>
                                <tr>
                                    <td>创建时间</td>
                                    <td name="platform.CREATE_TIME"></td>
                                    <td>更新时间</td>
                                    <td name="platform.UPDATE_TIME"></td>
                                </tr>
                                <tr>
                                    <td>备注</td>
                                    <td colspan="3" name="platform.REMARK"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="layui-tab-item" id="contractDiv">
                            <jsp:include page="include/contract-list.jsp"/>
                        </div>
                        <div class="layui-tab-item" id="businessDiv">
                            <jsp:include page="include/business-list.jsp"/>
                        </div>
                        <div class="layui-tab-item" id="orderDiv">
                            <jsp:include page="include/order-list.jsp"/>
                        </div>
                        <div class="layui-tab-item" id="priceDiv">
                            <jsp:include page="include/price-list.jsp"/>
                        </div>
                        <div class="layui-tab-item" id="costDiv">
                            <jsp:include page="include/cost-list.jsp"/>
                        </div>
                        <div class="layui-tab-item" id="invoiceDiv">
                            <jsp:include page="include/invoice-list.jsp"/>
                        </div>
                        <div class="layui-tab-item" id="settleDiv">
                            <jsp:include page="include/settle-list.jsp"/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        var platformInfo = {};
        var loadMap = {};
        var platformId = '${param.platformId}';
        
        $(function(){
            $("#PlatformDetailForm").render({success:function(result){
                var record = result && result['BusinessPlatformDao.getPlatform'];
                var data = record && record.data;
                if(data){
                    platformInfo = data;
                }
            }});
            businessList.init();
            orderList.init();
            contractList.init();
            priceList.init();
            costList.init();
            invoiceList.init();
            settleList.init();
        });

        function editPlatform(){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'编辑业务平台',
                offset:'20px',
                area:['500px','430px'],
                url:"${ctxPath}/pages/crm/platform/business-platform-edit.jsp",
                data:{platformId:platformId,platformTypeName:platformInfo.PLATFORM_TYPE_NAME}
            });
        }

        function closePage(){
            if (typeof top._removePage === 'function') {
                popup.closeTab();
            } else {
                top.location.reload();
            }
        }

        function reloadPlatformDetail(){
            $("#PlatformDetailForm").render();
        }

        function custDetail(custId){
            popup.openTab({id: 'custDetail', title: '客户详情', url: '${ctxPath}/pages/crm/cust/cust-detail.jsp', data: {custId:custId}});
        }

        function contractDetail(contractId){
            popup.openTab({
                id:'contractDetail',
                title:'合同详情',
                url:"${ctxPath}/project/contract",
                data:{contractId:contractId,isDiv:0}
            });
        }
    </script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>
