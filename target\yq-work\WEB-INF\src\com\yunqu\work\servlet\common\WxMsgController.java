package com.yunqu.work.servlet.common;

import java.sql.SQLException;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.utils.LogUtils;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;

@Before(AuthInterceptor.class)
@Path(value = "/wxMsg")
public class WxMsgController extends BaseController{
	
	public void sendMsg() {
		JSONObject params = this.getJSONObject();
		String type = params.getString("type");
		if(StringUtils.isBlank(type)) {
			renderJson(EasyResult.fail("type is null"));
			return;
		}
		String key = null;
		if("seftDept".equals(type)) {
			key = seftDeptKey();
		}else if(type.contains("pj@")){
			String projectId = type.substring(3);
			key = projectKey(projectId);
		}else {
			key = getValueByKey(type);
		}
		if(StringUtils.isBlank(key)) {
			renderJson(EasyResult.fail("key is error"));
			return;
		}
		String message = params.getString("message");
		message = message.replaceAll("userName", getNickName());
		boolean result = WeChatWebhookSender.sendMarkdownMessage(key, message);
		if(result) {
			renderJson(EasyResult.ok());
		}else {
			renderJson(EasyResult.fail());
		}
	}
	
	private String seftDeptKey() {
		String webhook = null;
		try {
			webhook = this.getMainQuery().queryForString("select WEBHOOK from easi_dept where dept_id = ?", getDeptId());
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
		return webhook;
	}
	
	private String projectKey(String projectId) {
		String webhook = null;
		try {
			webhook = this.getQuery().queryForString("select WEBHOOK from yq_project where project_id = ?",projectId);
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
		return webhook;
	}

	private String getValueByKey(String key) {
	    try {
	        return (String) WebhookKey.class.getField(key).get(null);
	    } catch (NoSuchFieldException | IllegalAccessException e) {
	        LogUtils.getWebhook().error(e.getMessage(),e);
	        return WebhookKey.TEST;
	    }
	}
}
