<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>菜单</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm">
				<div style="padding: 20px;line-height: 25px;">
					<p class="mb-10">
						<span class="f-12">关闭点餐 </span>
						<input type="radio"  name="dcSwitch" value="1" class="ml-10 mb-15 "/> 是
						<input type="radio" checked="checked" name="dcSwitch" value="0" class="mb-15 "/> 否
					</p>
					<p class="mb-10">
						<span class="f-12">是否可以点餐或预约 </span><input type="checkbox"  name="week6" value="1" class="ml-10 mb-15 "/> 周六
						<input type="checkbox"  name="week7" value="1" class="mb-15 "/> 周日
					</p>
					<p>
						<input type="text" onClick="WdatePicker({dateFmt:'HH:mm'})" placeholder="中餐点餐开始时间" name="b_1" class="form-control input-sm mb-10 Wdate"/>
						<input type="text" onClick="WdatePicker({dateFmt:'HH:mm'})" placeholder="中餐点餐开始时间" name="e_1" class="form-control input-sm mb-10 Wdate"/>
					</p>
					<p>
						<input type="text" onClick="WdatePicker({dateFmt:'HH:mm'})" placeholder="晚餐点餐开始时间" name="b_2" class="form-control input-sm mb-10 Wdate"/>
						<input type="text" onClick="WdatePicker({dateFmt:'HH:mm'})" placeholder="晚餐点餐开始时间" name="e_2" class="form-control input-sm mb-10 Wdate"/>
					</p>
				</div>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="saveSetting()"> 保 存 </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		$(function(){
			ajax.remoteCall("${ctxPath}/webcall?action=FoodDao.settingData",form.getJSONObject("#settingForm"),function(result) { 
				var data=result.data;
				for(var index in data){
					var d=data[index];
					if(d.DINNER_TYPE==2){
						$("input[name='b_1']").val(d.BEGIN_TIME);
						$("input[name='e_1']").val(d.END_TIME);
					}
					if(d.DINNER_TYPE==3){
						$("input[name='b_2']").val(d.BEGIN_TIME);
						$("input[name='e_2']").val(d.END_TIME);
					}
				}
				if(result.week6=='block'){
					$("input[name='week6']").prop("checked", true);
				}
				if(result.week7=='block'){
					$("input[name='week7']").prop("checked", true);
				}
				$("input[name='dcSwitch'][value='"+result.dcSwitch+"']").prop("checked", true);
			});
		});
		var saveSetting=function(){
			ajax.remoteCall("${ctxPath}/servlet/food?action=setting",form.getJSONObject("#editForm"),function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>