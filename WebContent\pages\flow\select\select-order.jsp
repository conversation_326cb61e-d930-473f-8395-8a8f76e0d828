<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择商机</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selecOrderForm">
       	     <input name="supplierId" value="${param.supplierId}" type="hidden"/>
       	     <input id="itemId" value="${param.itemId}" type="hidden"/>
	         <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 170px">
					 <span class="input-group-addon">采购订单号</span>	
					 <input type="text" name="orderNo" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectOrder.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		   </div>
           	   <div class="ibox">
              		<table id="SelectOrderList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectOrder.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectOrder = {
					sid:'${param.sid}',
					query : function(){
						$("#selecOrderForm").queryData();
					},
					initTable : function(){
						$("#selecOrderForm").initTable({
							mars:'OrderDao.list',
							id:'SelectOrderList',
							page:true,
							limit:20,
							height:'350px',
							rowDoubleEvent:'SelectOrder.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'ORDER_NO',
								title: '订单号',
								width:80
							},{
							    field: 'CONTRACT_NAME',
								title: '销售合同'
							},{
								field:'TOTAL_PRICE',
								title:'采购金额',
								width:80
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectOrder.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('SelectOrderList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['CONTRACT_NAME']);
									ids.push(data[index]['ORDER_NO']);
								}
								var itemId = $("#itemId").val();
								if(itemId){
									var rowData = data[0];
									$("#item_"+itemId+" [name='data1_"+itemId+"']").val(rowData['ORDER_NO']);
									$("#item_"+itemId+" [name='data2_"+itemId+"']").val(rowData['CONTRACT_NAME']);
									$("#item_"+itemId+" [name='data3_"+itemId+"']").val(rowData['TOTAL_PRICE']);
								}else{
									el.val(names.join(','));
									if(el.prev().length>0){
										el.prev().val(ids.join(','));
									}
								}
								popup.layerClose("selecOrderForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							var itemId = $("#itemId").val();
							if(itemId){
								var rowData = selectRow;
								$("#item_"+itemId+" [name='data1_"+itemId+"']").val(rowData['ORDER_NO']);
								$("#item_"+itemId+" [name='data2_"+itemId+"']").val(rowData['CONTRACT_NAME']);
								$("#item_"+itemId+" [name='data3_"+itemId+"']").val(rowData['TOTAL_PRICE']);
							}else{
								el.val(selectRow['CONTRACT_NAME']);
								if(el.prev().length>0){
									el.prev().val(selectRow['ORDER_NO']);
								}
							}
							popup.layerClose("selecOrderForm");
						}
					}
					
			};
			$(function(){
				SelectOrder.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>