package com.yunqu.work.servlet.crm;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.service.ContractService;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.AppBaseServlet;
import org.easitline.common.utils.kit.RandomKit;

@WebServlet("/servlet/invoice/*")
public class InvoiceServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_crm_invoice","invoice_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	private EasyRecord getTitleModel(String prefix){
		EasyRecord model = new EasyRecord("yq_crm_invoice_title","title_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	public EasyResult actionForAdd(){
		EasyRecord model = getModel("invoice");
		model.setPrimaryValues(RandomKit.uuid().toUpperCase());
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		model.set("CREATE_NAME", getUserName());
		String contractId = model.getString("CONTRACT_ID");
		String markDate = model.getString("MARK_DATE");
		model.set("MONTH_ID", DateUtils.getMonthIdFormat(markDate));
		try {
			this.getQuery().save(model);
			ContractService.getService().reloadCount("INVOICE_COUNT","yq_crm_invoice",contractId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("invoice");
		String invoiceId = model.getString("INVOICE_ID");

		if(StringUtils.notBlank(invoiceId)) {
			try {
				boolean isSettled = this.getQuery().queryForExist("SELECT COUNT(1) FROM yq_crm_invoice WHERE invoice_id = ? AND SETTLEMENT_ID IS NOT NULL AND SETTLEMENT_ID != ''", invoiceId);
				if(isSettled) {
					return EasyResult.fail("该发票记录已被结算，不允许修改");
				}
			} catch (SQLException e) {
				this.error("检查结算状态失败", e);
				return EasyResult.fail("检查结算状态失败:" + e.getMessage());
			}
		}
		try {
			String markDate = model.getString("MARK_DATE");
			model.set("MONTH_ID", DateUtils.getMonthIdFormat(markDate));
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail("更新失败:" + e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDel(){
		JSONObject invoiceObject = getJSONObject("");
		String invoiceId = invoiceObject.getString("INVOICE_ID");
		String contractId = invoiceObject.getString("CONTRACT_ID");

		try {
			boolean isSettled = this.getQuery().queryForExist("SELECT COUNT(1) FROM yq_crm_invoice WHERE invoice_id = ? AND SETTLEMENT_ID IS NOT NULL AND SETTLEMENT_ID != ''", invoiceId);
			if(isSettled) {
				return EasyResult.fail("该发票记录已被结算，不允许删除");
			}
		} catch (SQLException e) {
			this.error("检查结算状态失败", e);
			return EasyResult.fail("检查结算状态失败:" + e.getMessage());
		}

		try {
			this.getQuery().executeUpdate("delete from yq_crm_invoice where invoice_id = ?", invoiceId);
			ContractService.getService().reloadCount("INVOICE_COUNT","yq_crm_invoice",contractId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("删除失败:" + e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForAddTitle(){
		EasyRecord model = getTitleModel("title");
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		model.set("CREATE_NAME", getUserName());
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateTitle(){
		EasyRecord model = getTitleModel("title");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelTitle(){

		try {
			this.getQuery().executeUpdate("delete from yq_crm_invoice_title where title_id = ?", getJsonPara("titleId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	

}
