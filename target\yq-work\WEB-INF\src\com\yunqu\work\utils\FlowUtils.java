package com.yunqu.work.utils;

import org.easitline.common.utils.string.StringUtils;

public class FlowUtils {
	
	public static boolean isBxLoanFlow(String flowCode) {
		if(StringUtils.notBlank(flowCode)&&(flowCode.startsWith("finance_bx")||flowCode.startsWith("finance_loan"))) {
			return true;
		}
		return false;
	}
	
	public static boolean isBxFlow(String flowCode) {
		if(StringUtils.notBlank(flowCode)&&flowCode.startsWith("finance_bx")) {
			return true;
		}
		return false;
	}
	
	public static boolean isZrFlow(String flowCode) {
		if(StringUtils.notBlank(flowCode)&&flowCode.endsWith("_zr")) {
			return true;
		}
		return false;
	}
	
	public static boolean isLoanFlow(String flowCode) {
		if(StringUtils.notBlank(flowCode)&&flowCode.startsWith("finance_loan")) {
			return true;
		}
		return false;
	}

}
