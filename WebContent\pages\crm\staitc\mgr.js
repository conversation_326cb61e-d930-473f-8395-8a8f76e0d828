var ctxPath = getCtxPath();

/***common***/

function getCustLevel(val){
	var json={1:'A(重点客户)',2:'B(普通客户) ',3:'C(非优先客户)'};
	return json[val]||'';
}
function editCust(){
	var custId = $("#custId").val();
	popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:ctxPath+'/pages/crm/cust/cust-edit.jsp',title:'编辑客户',data:{custId:custId}});
}
function reloadCustInfo(){
	$("#custDetailForm").render();
}
function reloadFollowInfo(){
	var data = form.getJSONObject("#custDetailForm");
	$(".followList").render({data:data});
}

function addTeam(){
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['400px','300px'],url:ctxPath+'/pages/crm/include/add-user.jsp',title:'添加团队成员',data:{custId:custId}});
}
/***跟进管理***/
function addFollow(followSource){
	var custId = $("#custId").val();
	var custName = $("[name='cust.CUST_NAME']").text();
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['650px','550px'],url:ctxPath+'/pages/crm/include/add-follow.jsp',title:'新增跟进记录',data:{custId:custId,custName:custName,followSource:followSource}});
}

//合同详情
function contractDetail(contractId){
	popup.layerClose('contractDetail');
	popup.openTab({id:'contractDetail',type:1,scrollbar:false,shadeClose:true,offset:'20px',area:['600px','400px'],url:ctxPath+'/project/contract',title:'合同详情',data:{contractId:contractId}});
}
function contractDetail2(contractId,custId){
	popup.layerClose('contractDetail');
	popup.openTab({id:'contractDetail',title:'合同详情',url:ctxPath+'/project/contract',data:{contractId:contractId,custId:custId}});
}

function invoiceDetail(invoiceId){
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'发票详情',offset:'r',area:['60%','100%'],url:ctxPath+'/pages/crm/invoice/invoice-detail.jsp',data:{invoiceId:invoiceId}});
}

function purchaseDetail(orderId,custId){
	popup.openTab({id:'orderDetail',title:'采购详情',url:ctxPath+'/pages/erp/order/order-detail.jsp',data:{orderId:orderId,custId:custId}});
}

function reviewDetail(businessId){
	popup.openTab({id:'reviewDetail',url:ctxPath+'/web/flow',title:'评审详情',data:{businessId:businessId}});
}

function businessDetail(businessId,custId,custName){
	popup.openTab({id:'bussinessContactQuery',title:'跟进记录',url:ctxPath+'/pages/crm/contacts/contact-query.jsp',data:{businessId:businessId,custId:custId,custName:custName}});
}

function moreFollow(){
	var custId = $("#custId").val();
	popup.openTab({id:'custContactQuery',title:'跟进记录',url:ctxPath+'/pages/crm/contacts/contact-query.jsp',data:{custId:custId}});	
}

function uploadFile(){
	var fkId=$("#custId").val();
	easyUploadFile({callback:'reloadCustFile',fkId:fkId,source:'cust',formId:'custFileForm',fileId:'custLocalfile'});
}

/***发票管理***/
$(function(){
	loadInvoiceTitleList();
	loadInvoiceList();
	loadContractList();
	loadReviewList();
	loadBuyGoodsList();
	loadFileList();
	loadBusinessList();
	loadPriceList();
	loadCostList();
	loadPlatformOrderList();
});

function addBusiness(businessType){
	var custId = $("#custId").val();
	var custName = $("[name='cust.CUST_NAME']").text();
	popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['650px','500px'],url:ctxPath+'/pages/crm/shangji/sj-edit.jsp',title:'新增',closeBtn:0,data:{custId:custId,custName:custName,businessType:businessType}});
}

function addInvoiceTitle(){
	var custId = $("#custId").val();
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['480px','400px'],url:ctxPath+'/pages/crm/invoice/title/title-edit.jsp',title:'发票抬头',data:{custId:custId}});
}
function editInvoiceTitle(titleId){
	var custId = $("#custId").val();
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['480px','400px'],url:ctxPath+'/pages/crm/invoice/title/title-edit.jsp',title:'发票抬头',data:{custId:custId,titleId:titleId}});
}

function reloadInvoiceTitleList(){
	$("#custDetailForm").queryData({id:'invoiceTitleList',page:false});
}

function reloadInvoiceList(){
	$("#custDetailForm").queryData({id:'invoiceList',page:true});
}

function reloadTeamList(){
	$("#custDetailForm").queryData({id:'teamList',page:false});
}

function reloadCustFile(){
	$("#custDetailForm").queryData({id:'fileList',page:false});
}
function reloadBusinessList(){
	$("#custDetailForm").queryData({id:'businessList',page:false});
}


function reloadPriceList(){
	$("#custDetailForm").queryData({id:'salePriceList',page:false});
}

function reloadCostList(){
	$("#custDetailForm").queryData({id:'costList',page:false});
}

String.prototype.replaceAll = function(s1, s2) {
    return this.replace(new RegExp(s1, "gm"), s2);
}
function  editFollow(id,followSource){
	var custId = $("#custId").val();
	var custName = $("[name='cust.CUST_NAME']").text();
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['650px','550px'],url:ctxPath+'/pages/crm/include/add-follow.jsp',title:'跟进记录',data:{followId:id,custId:custId,custName:custName,followSource:followSource}});
}
function getFollowContent(val){
	if(val){
		return val.replaceAll('\n','<br>');
	}
	return '-';
}

/***联系人管理****/

$(function(){
	loadContactsList();
	loadTeamList();
});
function removeTeamUser(custId,userId){
	ajax.remoteCall(ctxPath+"/servlet/cust?action=removeTeamUser",{custId:custId,userId:userId},function(result) { 
		if(result.state == 1){
			layer.msg(result.msg,{icon:1,time:1200},function(){
				reloadTeamList();
			});
		}else{
			layer.alert(result.msg,{icon: 5});
		}
	});
}
function reloadContactsList(){
	$("#custDetailForm").queryData({id:'contactsList',page:false})
}
function addContact(){
	var custId = $("#custId").val();
	var custName = $("[name='cust.CUST_NAME']").text();
	 popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:ctxPath+'/pages/crm/contacts/contacts-edit.jsp',title:'新增联系人',closeBtn:0,data:{custId:custId,custName:custName,isEdit:1}});
}
function editContact(contactsId,isEdit){
	var custName = $("[name='cust.CUST_NAME']").text();
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:true,offset:'20px',area:['600px','500px'],url:ctxPath+'/pages/crm/contacts/contacts-edit.jsp',title:'联系人',data:{isEdit:isEdit,contactsId:contactsId,custName:custName}});
}

/***运营管理****/
function supplierDetail(id){
	popup.openTab({id:'supplierDetail',url:ctxPath+'/pages/erp/supplier/supplier-detail.jsp',title:'供应商详情',data:{supplierId:id}});
}

function bxDetail(applyId){
	popup.openTab({
		id: 'flowDetail',
		title: '报销流程详情',
		url: '/yq-work/web/flow',
		data: {
			businessId: applyId,
			flowCode: 'finance_bx',
			handle: 'detail',
		}
	});
}

function addSalePrice(){
	var custId = $("#custId").val();
	var custName =  $("[name='cust.CUST_NAME']").text();
	popup.layerShow({
		type: 1,
		anim: 0,
		scrollbar: false,
		shadeClose: false,
		title: '新增销售价格',
		offset: '20px',
		area: ['600px', '540px'],
		url: ctxPath + '/pages/crm/platform/include/add-price.jsp',
		data: {custId:custId,custName:custName}
	});
}

function editPrice(priceId){
	popup.layerShow({
		type: 1,
		anim: 0,
		scrollbar: false,
		shadeClose: false,
		title: '编辑销售价格',
		offset: '20px',
		area: ['600px', '540px'],
		url: ctxPath + '/pages/crm/platform/include/add-price.jsp',
		data: {isNew:"0",priceId:priceId}
	});
}

function deletePrice(priceId){
	layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function () {
		ajax.remoteCall("${ctxPath}/servlet/custOperate?action=deletePrice", {priceId:priceId}, function (result) {
			if (result.state == 1) {
				layer.msg(result.msg, {icon: 1, time: 1200}, function () {
					layer.closeAll();
					reloadPriceList();
				});
			} else {
				layer.alert(result.msg, {icon: 5});
			}
		});
	});
}


function addCost(){
	var custId = $("#custId").val();
	var custName =  $("[name='cust.CUST_NAME']").text();
	popup.layerShow({
		type: 1,
		anim: 0,
		scrollbar: false,
		shadeClose: false,
		title: '新增成本',
		offset: '20px',
		area: ['700px', '600px'],
		url: ctxPath + '/pages/crm/platform/include/add-cost.jsp',
		data: {custId:custId, custName:custName, relateType:"1", costOwner:"1"}
	});
}

function editCost(costId, relateType) {
	var custId = $("#custId").val();
	var custName = $("[name='cust.CUST_NAME']").text();
	popup.layerShow({
		type: 1,
		anim: 0,
		scrollbar: false,
		shadeClose: false,
		offset: '20px',
		area: ['700px', '600px'],
		url: ctxPath + '/pages/crm/platform/include/add-cost.jsp',
		title: '成本编辑',
		data: { costId: costId, custId: custId, custName: custName, relateType: relateType || "1" }
	});
}

function deleteCost(costId){
	layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function () {
		ajax.remoteCall("${ctxPath}/servlet/custOperate?action=deleteCost", {costId:costId}, function (result) {
			if (result.state == 1) {
				layer.msg(result.msg, {icon: 1, time: 1200}, function () {
					layer.closeAll();
					reloadCostList();
				});
			} else {
				layer.alert(result.msg, {icon: 5});
			}
		});
	});
}

function addPlatformOrder(){
	var custId = $("#custId").val();
	var custName = $("[name='cust.CUST_NAME']").text();
	popup.layerShow({
		type:1,
		anim:0,
		scrollbar:false,
		shadeClose:false,
		title:'新增订购',
		offset:'20px',
		area:['700px','500px'],
		url:ctxPath+'/pages/crm/include/add-platform-order.jsp',
		data:{custId:custId,custName:custName}
	});
}

function editPlatformOrder(orderId){
	popup.layerShow({
		type:1,
		anim:0,
		scrollbar:false,
		shadeClose:false,
		title:'编辑订购',
		offset:'20px',
		area:['700px','500px'],
		url:ctxPath+'/pages/crm/include/add-platform-order.jsp',
		data:{orderId:orderId}
	});
}

function deletePlatformOrder(orderId){
	layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function(){
		ajax.remoteCall(ctxPath+"/servlet/platform-order?action=deleteOrder",{orderId:orderId},function(result){
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:1200},function(){
					reloadPlatformOrderList();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	});
}

function reloadPlatformOrderList(){
	$("#custDetailForm").queryData({id:'platformOrderList',page:true});
}