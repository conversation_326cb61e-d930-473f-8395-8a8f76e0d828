package com.yunqu.work.dao.erp;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="SupplierDao")
public class SupplierDao extends AppDaoContext {

	@WebControl(name="getSupplierNo",type=Types.TEXT)
	public JSONObject getSupplierNo(){
		try {
			String val = this.getQuery().queryForString("select supplier_no from yq_erp_supplier order by create_time desc limit 1");
			if(StringUtils.isBlank(val)) {
				return getJsonResult("YQ10000001");
			}else {
				if(val.startsWith("YQ")) {
					val =  val.substring(2);
				}
				return getJsonResult("YQ"+String.valueOf(Long.valueOf(val)+1));
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String supplierId = param.getString("supplierId");
		return queryForRecord("select * from yq_erp_supplier where supplier_id = ?",supplierId);
	}
	@WebControl(name="productInfo",type=Types.RECORD)
	public JSONObject productInfo(){
		String productId = param.getString("productId");
		return queryForRecord("select * from yq_erp_product where product_id = ?",productId);
	}
	
	@WebControl(name="productList",type=Types.LIST)
	public JSONObject productList(){
		EasySQL sql=getEasySQL("select t1.*,t2.name SUPPLIER_NAME from YQ_ERP_PRODUCT t1,yq_erp_supplier t2 where t1.SUPPLIER_ID = t2.SUPPLIER_ID");
		sql.append(param.getString("supplierId"),"and t1.SUPPLIER_ID = ?");
		sql.appendLike(param.getString("productName"),"and t1.PRODUCT_NAME like ?");
		sql.appendLike(param.getString("supplierName"),"and t2.name like ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="productDict",type=Types.DICT)
	public JSONObject productDict(){
		EasySQL sql=getEasySQL("select product_id,product_name from YQ_ERP_PRODUCT where 1=1");
		sql.append(param.getString("supplierId"), "and SUPPLIER_ID = ?");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="productSelect",type=Types.PAGE)
	public JSONObject productSelect(){
		EasySQL sql=getEasySQL("select * from yq_erp_product where 1=1");
		sql.append(param.getString("supplierId"), "and SUPPLIER_ID = ?");
		sql.appendLike(param.getString("productName"), "and PRODUCT_NAME like ?");
		sql.append("order by goods_count desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from yq_erp_supplier where 1=1");
		sql.appendLike(param.getString("supplierNo"),"and SUPPLIER_NO like ?");
		sql.appendLike(param.getString("supplierName"),"and name like ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="paymentList",type=Types.PAGE)
	public JSONObject paymentList(){
		EasySQL sql=getEasySQL("select * from yq_erp_order_payment where 1=1");
		sql.append(param.getString("supplierId"),"and supplier_id = ?",false);
//		sql.append("order by apply_time");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		EasySQL sql=getEasySQL("select supplier_id,name from yq_erp_supplier where 1=1");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
}
