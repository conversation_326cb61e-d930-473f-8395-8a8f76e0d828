<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="supplierId" type="hidden" value="${param.supplierId}">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>采购申请查询</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		 <span class="input-group-addon">订单号</span>
						 	 <input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		 <span class="input-group-addon">合同名称</span>
						 	 <input class="form-control input-sm" name="contractName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		 <span class="input-group-addon">合同号</span>
						 	 <input class="form-control input-sm" name="contractNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">采购类型</span>
						 	 <select name="orderType" onchange="DataMgr.query()" class="form-control">
	                   		 	<option value="">请选择</option>
	                   		 	<option value="0">销售采购</option>
	                   		 	<option value="1">固定资产</option>
	                   		 </select>
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" data-event="enter" class="btn btn-sm btn-default" onclick="DataMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="DataMgr.add()">+ 发起采购申请</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var DataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.list',
					id:'dataMgrTable',
					autoSort:false,
					height:'full-120',
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 field:'ORDER_NO',
						 title:'订单编号',
						 event:'DataMgr.detail',
						 width:120,
						 style:'text-decoration:underline;',
						 templet:'<div><a href="javascript:;">{{d.ORDER_NO}}</a></div>'
					 },{
					    field: 'CONTRACT_NAME',
						title: '合同名称',
						align:'left',
						event:'DataMgr.contractDetail',
						style:'text-decoration:underline;'
					},{
					    field: 'ORDER_TYPE',
						title: '采购类型',
						width:90,
						align:'center',
						templet:function(row){
							var json = {'0':'销售采购','1':'固定资产'};
							return json[row['ORDER_TYPE']];
						}
					},{
						field:'TOTAL_PRICE',
						width:90,
						title:'订单金额'
					},{
					    field: 'SUPPLIER_NAME',
						title: '供应商',
						align:'center',
					    style:'text-decoration:underline;',
						minWidth:160,
						event:'DataMgr.supplierDetail'
					},{
					    field: 'LAST_FOLLOW_LABEL',
						title: '最新状态',
						width:90,
						align:'center'
					},{
					    field: 'LAST_FOLLOW_CONTENT',
						title: '跟进内容',
						align:'center'
					},{
					    field: 'LAST_FOLLOW_TIME',
						title: '跟进时间',
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '操作时间',
						align:'center',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'CREATE_USER_ID',
						title: '制单人',
						align:'center',
						width:70,
						templet:function(row){
							return getUserName(row.CREATE_USER_ID);
						}
					}
				]],done:function(){
					
			  }});
			},
			detail:function(data){
				popup.openTab({id:'orderDetail',title:'采购详情',url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{orderId:data.ORDER_ID,reviewId:data['REVIEW_ID'],custId:data.CUST_ID,applyBy:data['CREATE_USER_ID']}});
			},
			supplierDetail:function(data){
				var id = data['SUPPLIER_ID'];
				popup.openTab({id:'supplierDetail',url:'${ctxPath}/pages/erp/supplier/supplier-detail.jsp',title:'供应商详情',data:{supplierId:id}});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable',jumpOne:true});
			},
			edit:function(orderId){
				popup.openTab({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:'${ctxPath}/pages/erp/order/apply-edit.jsp',title:'修改信息',data:{orderId:orderId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:'${ctxPath}/pages/erp/order/order-add.jsp',title:'新建采购申请'});
			},
			contractDetail:function(data){
				popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/pages/crm/contract/contract-detail.jsp',data:{contractId:data.CONTRACT_ID,custId:data['CUST_ID']}});
			}
		}
		
		function reloadDataList(){
			DataMgr.query();
		}
		$(function(){
			$("#dataMgrForm").render({success:function(){
				DataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>