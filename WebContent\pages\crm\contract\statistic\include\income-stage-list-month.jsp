<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="incomeStageTable3"></table>
<script type="text/javascript">
    var incomeStageList3={
        init:function(){
            $("#searchForm").initTable({
                mars:'IncomeStageDao.allIncomeStageList',
                id:'incomeStageTable3',
                page:true,
                cellMinWidth:60,
                rowDoubleEvent(row){
                    incomeStageList3.detail(row);
                },
                data:{'incomeStageType':"month"},
                limit:15,
                limits:[15,30,50,300,500],
                title:'收入确认阶段-本月待确认',
                toolbar:true,
                autoSort:false,
                totalRow:true,
                cols: [[
                    {
                        type: 'numbers',
                        align:'left',
                        totalRowText:"合计"
                    },{
                        field: 'CONTRACT_NAME',
                        title: '合同名称',
                        align:'left',
                        width:180,
                        style:'color:#1E9FFF;cursor:pointer',
                        event:'incomeStageList3.contractDetail',
                    },{
                        field: 'STAGE_NAME',
                        title: '阶段名称',
                        align:'left',
                        width:80,
                        style:'color:#1E9FFF;cursor:pointer',
                        event:'incomeStageList3.detail',
                    },{
                        field:'INCOME_CONF_FLAG',
                        title:'状态',
                        minWidth:100,
                        width: 100,
                        templet:function(d){
                            return getIncomeStageStatus(d.INCOME_CONF_FLAG,d.PLAN_COMP_DATE,d.ACT_COMP_DATE);
                        }
                    },{
                        field:'PLAN_COMP_DATE',
                        title:'计划完成时间',
                        align:'left',
                        sort:true,
                        width:110
                    },{
                        field:'RATIO',
                        title:'占合同总额(%)',
                        width:100,
                    },{
                        field:'PLAN_AMOUNT',
                        title:'计划收款金额(含税)',
                        width:140,
                        totalRow:true,
                        sort:true,
                    },{
                        field:'AMOUNT_NO_TAX',
                        title:'计划收款金额(税后)',
                        width:140,
                        totalRow:true,
                        sort:true,
                    },
                    {
                        field:'UNCONFIRM_NO_TAX_A',
                        title:'未确认金额A(税后)',
                        width:140,
                        totalRow:true,
                        sort:true,
                    },{
                        field:'REMARK',
                        title:'备注',
                        width:120
                    },{
                        field:'UPDATE_BY',
                        title:'操作人',
                        width:90
                    },{
                        field: 'UPDATE_TIME',
                        title: '更新时间',
                        width:120,
                        align:'center',
                    }
                ]],
                done:function(res){

                }
            });
        },
        detail:function (data){
            var  incomeStageId = data.INCOME_STAGE_ID;
            var  contractId = data.CONTRACT_ID;
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'收入确认阶段详情',offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/crm/contract/include/income-stage-detail.jsp',data:{contractId:contractId,incomeStageId:incomeStageId}});
        },
        contractDetail:function(data){
            var  contractId = data.CONTRACT_ID;
            var  custId = data.CUST_ID;
            popup.layerClose('contractDetail');
            popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/project/contract',data:{contractId:contractId,custId:custId}});
        }
    }


</script>
