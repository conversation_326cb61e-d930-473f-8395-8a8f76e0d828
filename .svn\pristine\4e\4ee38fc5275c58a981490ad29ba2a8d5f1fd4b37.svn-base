<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
	<style>
		.layui-badge{left: -1px!important;}
	</style>
	<style>
		.layui-table-cell{padding: 0 6px;}
	  /*   #taskMgrForm tr .layui-btn{opacity:0;}
		#taskMgrForm tr:hover .layui-btn{opacity:1;} */
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
	<link href="${ctxPath}/static/module/soul/soulTable.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="taskMgrForm">
			<input name="taskState" id="taskState" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>任务管理</h5>
	          		     <div class="input-group input-group-sm" style="width: 310px">
	          		    	<span class="input-group-addon">执行时间</span>	
                     		<input data-mars="CommonDao.currentMonthRange" data-mars-top="true" data-mars-reload="false" style="border-right: 0;" type="text" data-laydate="{type:'date',range: '到'}" name="planDate" class="form-control input-sm">
                     		<div class="input-group-btn">
	                     		<select class="form-control input-sm" style="width:70px;" onchange="getDate(this,'planDate')">
		                     		<option value="">全部</option>
		                     		<option value="today">今日</option>
		                     		<option value="yesterday">昨天</option>
		                     		<option selected="selected" value="currentMonth">本月</option>
		                     		<option value="threeDay">三天内</option>
		                     		<option value="oneWeek">一周内</option>
		                     		<option value="oneMonth">一个月内</option>
		                     		<option value="threeMonth">三个月内</option>
		                     	</select>
                     	   </div>
                    	 </div>
					      <div class="input-group input-group-sm ml-10 mr-10">
					    		  <button type="button" class="btn btn-sm btn-default" onclick="selectCondition()"><span class="glyphicon glyphicon-filter"></span> 高级筛选</button>
				    		  </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="taskMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-xs btn-info btn-outline ml-10" onclick="userTaskStatTable()"><i class="layui-icon layui-icon-chart"></i>统计</button>
						 </div>
						 <div  class="input-group input-group-sm" id="headerEnd"></div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="taskMgr.add()">+ 发起任务</button>
							 <script type="text/html" id="taskBar">
						     	   		<a href="javascript:;" onclick="exportTask()" class="btn btn-xs btn-default btn-link">导出任务</a>
							 </script>
						 </div>
	          	     </div>
	          	     <div class="filterCondition">
						<jsp:include page="task-condition.jsp"></jsp:include>
					</div>
	              </div> 
					<div class="ibox-content">
						<div  class="userTaskStatContainer">
							<table id="userTaskStat" class="layui-hide mb-30"></table>
						</div>
						<div class="taskMgrContainer">
							<div class="layui-tab layui-tab-brief" lay-filter="taskMgr" data-mars="TaskDao.taskCountStat">
							  <ul class="layui-tab-title">
							    <li data-state="" class="layui-this">全部 <span class="layui-badge layui-bg-cyan ALL">0</span></li>
							    <li data-state="10">待办中 <span class="layui-badge layui-bg-orange" id="A">0</span></li>
							    <li data-state="20">进行中 <span class="layui-badge" id="B">0</span></li>
							    <li data-state="25">超时未完成 <span class="layui-badge" id="E">0</span></li>
							    <li data-state="30">已完成 <span class="layui-badge layui-bg-blue" id="C">0</span></li>
							    <li data-state="40">已验收 <span class="layui-badge layui-bg-green" id="D">0</span></li>
							  </ul>
							  <div class="layui-tab-content" style="padding: 0px;">
							  	<div class="layui-tab-item layui-show"></div>
							  	<div class="layui-tab-item"></div>
							  	<div class="layui-tab-item"></div>
							  	<div class="layui-tab-item"></div>
							  	<div class="layui-tab-item"></div>
							  	<div class="layui-tab-item"></div>
							  </div>
							</div> 
						    <table class="layui-hide" id="taskMgrTable"></table>
						</div>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
			{{if TASK_STATE!=40}}
	  			{{if currentUserId == CREATOR && TASK_STATE !=11}}
					<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="taskMgr.edit"><i class="layui-icon layui-icon-edit"></i></a>
					{{else deptId == ASSIGN_DEPT_ID && isDevLead && TASK_STATE!=11}}
					<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="taskMgr.edit"><i class="layui-icon layui-icon-edit"></i></a>
				{{/if}}
			{{/if}}
			<a class="layui-btn layui-btn-normal layui-btn-xs layui-hide" lay-event="taskMgr.detail">查看</a>
		</script>
		<script type="text/x-jsrender" id="taskProgress">
			{{if F>0}}
			<div class="layui-progress" lay-showPercent="true">
  				<div class="layui-progress-bar {{if F==H}} layui-bg-green {{else H/F <= 0.6}} layui-bg-red {{else H/F <= 0.8}} layui-bg-orange  {{else}} layui-bg-blue {{/if}}" lay-percent="{{:H}}/{{:F}}"></div>
			</div>
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
<script type="text/javascript">
		
		 var isSelfDept = '${param.isSelfDept}'||'0';
		 
		layui.use('element',function(){
			var element=layui.element;
			element.on('tab(taskMgr)', function(elem){
				var  state=$(this).data('state');
				$("#taskState").val(state);
				reloadTaskList();
			});
		  });
		var firstLoad=true;
		var taskMgr={
			init:function(){
				layui.config({
					  base: '${ctxPath}/static/module/tableMerge/'
				}).use('tableMerge'); //加载自定义模块

				layui.config({
					  base: '${ctxPath}/static/module/soul/'
				}).use('soulTable'); //加载自定义模块
				
				layui.use(['tableMerge','soulTable'],function(){
					var tableMerge  = layui.tableMerge,soulTable=layui.soulTable;
					$("#taskMgrForm").initTable({
							mars:'TaskDao.taskList',
							//skin:'line',
							elem:'#taskMgrTable',
							id:'taskMgrTable',
							autoSort:false,
							toolbar:'#taskBar',
							filter:true,
							defaultToolbar:['filter', 'print', 'exports'],
							cols: [[
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'TASK_NAME',
								title: '任务名称',
								align:'left',
								minWidth:220,
								event:'taskMgr.detail',
								style:'color:#1E9FFF;cursor:pointer',	
								templet:function(row){
									var id = row['P_TASK_ID'];
									if(id){
										return "<i class='layui-icon layui-icon-senior'></i>"+row['TASK_NAME'];
									}else{
										return row['TASK_NAME'];
									}
								}
							},{
							    field: 'PROJECT_ID',
								title: '项目名称',
								merge: true,
								filter: true,
								event:'taskMgr.projectDetail',
								align:'left',
								sort:true,
								minWidth:150,
								templet:'<div>{{d.PROJECT_NAME}}</div>'
							},{
							    field: 'TASK_TYPE_ID',
								title: '类型',
								merge: true,
								event:'taskMgr.detail',
								filter: true,
								align:'center',
								sort:true,
								width:70,
								templet:function(row){
									return getText(row['TASK_TYPE_ID'],'taskTypeId');
								}
							},{
							    field: 'TASK_STATE',
								title: '任务状态',
								filter: true,
								sort:true,
								align:'center',
								width:80,
								templet:function(row){
									return taskStateLabel(row.TASK_STATE);
								}
							},{
							    field: 'CREATOR',
								title: '发起人',
								sort:true,
								merge:true,
								align:'center',
								width:70,
								templet:function(row){
									return '<a href="javascript:;" onclick="userInfoLayer(\''+row['CREATOR']+'\')">'+getUserName(row.CREATOR)+'</a>';
								}
							},{
							    field: 'ASSIGN_USER_ID',
								title: '负责人',
								align:'center',
								merge:true,
								sort:true,
								width:70,
								templet:function(row){
									return '<a href="javascript:;" onclick="userInfoLayer(\''+row['ASSIGN_USER_ID']+'\')">'+getUserName(row.ASSIGN_USER_ID)+'</a>';
								}
							},{
							    field: 'VIEW_COUNT',
								title: '浏览数',
								align:'center',
								sort:true,
								width:70
							},{
							    field: 'CREATE_TIME',
								title: '发起时间',
								align:'center',
								width:130,
								sort:true,
								templet:function(row){
									var time= row['CREATE_TIME'];
									return cutText(time,19,'');
								}
							},{
							    field: 'DEADLINE_AT',
								title: '截止时间',
								align:'left',
								width:100,
								sort:true,
								templet:function(row){
									var time= row['DEADLINE_AT'];
									var state= row['TASK_STATE'];
									if(state<30){
										return timeBetween(time);
									}
									return cutText(time,12,'');
								}
							},{
							    field: 'UPDATE_TIME',
								title: '更新时间',
								width:90,
								align:'center',
								hide:true,
								templet:function(row){
									var time= row['UPDATE_TIME'];
									return cutText(time,12,'');
								}
							},{
								title: '操作',
								align:'center',
								width:90,
								cellMinWidth:90,
								templet:function(row){
									row['currentUserId']=getCurrentUserId();
									row['isSuperUser']=isSuperUser;
									row['deptId']=getDeptId();
									row['isDevLead']=isDevLead();
								    return renderTpl('bar1',row);
								}
							}
						]],done:function(result){
							soulTable.render(layTables['taskMgrTable'].config);
							tableMerge.render(layTables['taskMgrTable'].config);
							if(firstLoad){
								$(".ALL").text(result.totalRow)
							}
							firstLoad=false;
							$(".taskMgrContainer").show();
							if(isSelfDept==0)$(".userTaskStatContainer").hide();
							table.on('sort(taskMgrTable)', function(obj){
								  $("#taskMgrForm").queryData({id:'taskMgrTable',jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
							});
						}
				   })
				});
			},
			query:function(){
				firstLoad=true;
				$("#taskMgrForm").render();
				$("#taskMgrForm").queryData({id:'taskMgrTable',jumpOne:true});
			},
			edit:function(data){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['780px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'编辑任务',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['780px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'安排任务',closeBtn:0});
			},
			projectDetail:function(data){
				var projectId = data['PROJECT_ID'];
				if(projectId){
					popup.openTab({id:'projectId',url:'${ctxPath}/pages/project/project-detail.jsp',title:'项目总览',data:{projectId:projectId}});
				}
			},
			detail:function(data){
				var state = data['TASK_STATE'];
				if(state==40){
					popup.openTab({id:"task_"+data.TASK_ID,type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/task/task-detail.jsp?isDiv=0',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
				}else{
					popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
				}
			},
			notice:function(){
				ajax.remoteCall("${ctxPath}/servlet/task?action=noticeMsg",{},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		function reloadTaskList(){
			$("#taskMgrForm").queryData({id:'taskMgrTable',jumpOne:true});
		}
		$(function(){
			layui.config({
				  base: '${ctxPath}/static/js/'
			}).use('tableSelect'); //加载自定义模块
			
			layui.use(['element','tableSelect'], function(){
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#projectId',
					searchKey: 'projectName',
					checkedKey: 'PROJECT_ID',
					page:true,
					searchPlaceholder: '请输入项目名称',
					table: {
						mars: 'ProjectContractDao.projectList',
						cols: [[
							{ type: 'radio' },
							{ field: 'PROJECT_ID',hide:true,title: 'ID'},
							{ field: 'PROJECT_NAME', title: '名称', width: 320 },
							{ field: 'PROJECT_NO', title: '编号', width: 150 }
						]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.PROJECT_NAME)
							ids.push(item.PROJECT_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
					},
					clear:function(elem){
					  elem.prev().val("");
					}
				});
				$("#creator,#assignUserId").each(function(){
					var id =$(this).attr("id");
					tableSelect.render({
						elem: "#"+id,
						searchKey: 'userName',
						checkedKey: 'USER_ID',
						page:true,
						searchPlaceholder: '请输入姓名',
						table: {
							mars: 'EhrDao.userList',
							cols: [[
								{ type: 'radio' },
								{ field: 'USER_ID',hide:true,title: 'ID'},
								{ field: 'USERNAME', title: '姓名' },
								{ field: 'DEPTS', title: '部门' },
								{ field: 'DATA1', title: '工号'}
							]]
						},
						done: function (elem, data) {
							var names = [];
							var ids = [];
							layui.each(data.data, function (index, item) {
								names.push(item.USERNAME)
								ids.push(item.USER_ID)
							});
							elem.attr("ts-selected",ids.join(","));
							elem.val(names.join(","));
							elem.prev().val(ids.join(","));
							
							if(id=='assignUserId'){
								$(".selectUserSpan").text(names.join(","));
							}
						},
						clear:function(elem){
						  elem.prev().val("");
						}
					});
					
				});
			});
			//初始化加载
			$("#taskMgrForm").render({success:function(result){
				 if(result['TaskDao.taskCountStat']){
					taskMgr.init();
					if(isSelfDept==1){userTaskStatTable();}
				 }
			}});
		});
		var getDate = function(obj,inputName){
			var val = obj.value;
			var bdate = new Date();
			var edate = new Date();
			if('createTime'==inputName||'planDate'==inputName){
				if(val == 'today'){
					
				}else if(val == 'yesterday'){
					bdate.setDate(bdate.getDate() - 1);
					edate = bdate;
				}else if(val == 'threeDay'){
					bdate.setDate(bdate.getDate() - 3);
				}else if(val == 'oneWeek'){
					bdate.setDate(bdate.getDate() - 7);
				}else if(val == 'oneMonth'){
					bdate.setMonth(bdate.getMonth() - 1);
				}else if(val == 'threeMonth'){
					bdate.setMonth(bdate.getMonth() - 3);
				}else if(val == 'currentMonth'){
					bdate.setDate(1);
					edate.setMonth(bdate.getMonth() + 1);
					edate.setDate(0);
				}
				if(val){
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}else{
				if(val){
					bdate.setDate(bdate.getDate() - val);
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}
			taskMgr.query();
		}
		function selectCondition(){
			popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'l',area:['400px','100%'],content:$('.filterCondition'),title:'筛选条件'});
		}
		var userTaskStat = true;
		function userTaskStatTable(){
			if(userTaskStat){
				userTaskStat=false;
				$("#taskMgrForm").initTable({
					id:'userTaskStat',
					mars:'TaskDao.userTaskStat',
					limit:10,
					page:true,
					data:{'selectUser':'1'},
					cellMinWidth:100,
					totalRow:true,
					toolbar:true,
					defaultToolbar:['filter', 'print', 'exports'],
					limits:[10,15,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'center'
					 },{
						 title: '序号',
						 type:'radio'
					 },{
					    field: 'ASSIGN_USER_ID',
						title: '姓名',
						align:'center',
						width:100,
						event:'selectUser',
						totalRowText:'汇总',
						templet:function(row){
							return getUserName(row['ASSIGN_USER_ID']);
						}
					},{
						field:'',
						title: '已完成/总数',
						event:'selectUser',
						minWidth:220,
						templet:function(row){
							return renderTpl('taskProgress',row);
						}
					},{
					    field: 'F',
						title: '任务总数',
						sort:true,
						totalRow:true
					},{
					    field: 'G',
						title: '未完成数',
						event:'selectUser',
						align:'center',
						sort:true,
						totalRow:true
					},{
					    field: 'A',
						title: '待办数',
						event:'selectUser',
						align:'center',
						sort:true,
						totalRow:true
					}
					 ,{
					    field: 'B',
						title: '进行中',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'E',
						title: '超时未完成',
						sort:true,
						align:'center',
						totalRow:true
					}
				  ,{
					   field: 'C',
					   title: '已完未验收',
					   sort:true,
					   align:'center',
						totalRow:true
				},{
					   field: 'D',
					   title: '已验收',
					   sort:true,
					   align:'center',
					   totalRow:true
					}
				]],done:function(res,curr,count){
					if(isSelfDept==0)$(".taskMgrContainer").hide();
					$(".userTaskStatContainer").show();
					$(".userTaskStatContainer").addClass("mb-30");
					layui.use(['element'], function(){
						var element = layui.element;
						element.render();
					});
					table.on('radio(userTaskStat)', function(obj){
						selectUser(obj.data);
					});
					if(res.totalPage==1){
						$(".userTaskStatContainer .layui-table-page").hide();
					}else{
						$(".userTaskStatContainer .layui-table-page").show();
					}
				}}
			  );
			}else{
				$("#taskMgrForm").queryData({id:'userTaskStat',data:{'selectUser':'1'},jumpOne:true});
			}
			
		}
		function selectUser(data){
			var userId=data['ASSIGN_USER_ID'];
			$("[name='assignUserId']").val(userId);
			taskMgr.query();
			$("#assignUserId").val(getUserName(data['ASSIGN_USER_ID']));
			$("#headerEnd").html("<div style='cursor:pointer' class='label label-success label-outline ml-10' onclick='delUser(this)'><span class='selectUserSpan'>"+getUserName(data['ASSIGN_USER_ID'])+"</span> x</div>");
		}
		function delUser(el){
			$("[name='assignUserId']").val('');
			$("#assignUserId").val('');
			$(el).remove();
			taskMgr.query();
		}
		function exportTask(){
			layer.msg('正在导出',{time:500});
			location.href = '${ctxPath}/servlet/task?action=exportTask&data='+encodeURI(JSON.stringify(form.getJSONObject('#searchForm')));
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>