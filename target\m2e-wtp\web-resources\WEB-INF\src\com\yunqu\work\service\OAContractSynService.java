package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ProjecContractModel;
import com.yunqu.work.model.ProjectModel;

public class OAContractSynService extends AppBaseService{
	
	private Logger logger = LogEngine.getLogger(Constants.APP_NAME);
	
	private static class Holder{
		private static OAContractSynService service=new OAContractSynService();
	}
	public static OAContractSynService getService(){
		return Holder.service;
	}
	public static void main(String[] args) {
		OAService.getService().login("100078","10304603");
	}
	public String synData(){
        try {
          StringBuffer msg = new StringBuffer();
           OAService.getService().login("100078","10304603");
           String xml = OAService.getService().loadContractXml("100078");
           if(xml==null){
        	   return null;
           }
            xml = xml.replaceAll("\n", "");
            xml = xml.replaceAll("\t", "");
			Document doc = DocumentHelper.parseText(xml);
			Element root = doc.getRootElement();
			@SuppressWarnings("unchecked")
			List<Element> childElements  = root.elements("Item");
			for (Element child : childElements) {
				 Element attr=child.element("ItemAttributes");
				 if(attr!=null){
					 ProjectModel model=new ProjectModel();
					 String id=child.elementText("DocUNID");
					 String subject = attr.elementText("Subject");
					 String year = attr.elementText("year");
					 String Money = attr.elementText("Money");
					 String signDate = attr.elementText("Date");
					 String createTime = attr.elementText("WF_DocCreated");
					 String department = attr.elementText("department_show");
					 if(subject.indexOf(" ")==-1){
						 return null;
					 }
					 String[] array = subject.split(" ");
					 String accCode = StringUtils.trimToEmpty(array[0]);
					 String projectName = subject.substring(accCode.length()+1);;
					 try {
						JSONObject projectModel=this.getQuery().queryForRow("select * from YQ_PROJECT where PROJECT_NO = ?",new String[]{accCode}, new JSONMapperImpl());
						if(projectModel==null||projectModel.size()==0){
							 model.set("PROJECT_TYPE",10);
							 model.set("PROJECT_NO", StringUtils.trimToEmpty(accCode));
							 model.set("PROJECT_NAME",projectName);
							 model.set("PROJECT_ID", id);
							 model.set("CONTRACT_ID", id);
							 model.set("BEGIN_DATE", signDate);
							 model.set("CREATE_TIME", createTime);
							 model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
							try {
								model.save();
								msg.append(projectName+",");
							} catch (SQLException e) {
								logger.error(e.getMessage(),e);
							}
						 }else{
							 id=projectModel.getString("PROJECT_ID");
							 model.set("PROJECT_ID", id);
//							 model.set("PROJECT_NAME",projectName);
//							 model.set("PROJECT_NO",projectModel.getString("PROJECT_NO"));
//							 model.set("CREATE_TIME",createTime);
							 if(StringUtils.notBlank(signDate)){
								model.set("BEGIN_DATE", signDate);
//								model.update();
							 }
						 }
					} catch (SQLException e1) {
						logger.error(null,e1);
					}
					 
					//合同表
					ProjecContractModel contractModel=new ProjecContractModel();
					try {
						JSONObject contractObj=this.getQuery().queryForRow("select * from YQ_PROJECT_CONTRACT where CONTRACT_NO = ?",new String[]{accCode}, new JSONMapperImpl());
						if(contractObj==null||contractObj.size()==0){
							contractModel.set("CONTRACT_ID", id);
							contractModel.set("CONTRACT_NAME", model.getString("PROJECT_NAME"));
							contractModel.set("CONTRACT_NO", model.getString("PROJECT_NO"));
							contractModel.set("PROJECT_ID", id);
							contractModel.set("YEAR", year);
							contractModel.set("CREATE_TIME", createTime);
							contractModel.set("SIGN_DATE", signDate);
							contractModel.set("CONTRAC_DESC", department);
							contractModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
							try {
								contractModel.save();
								OAContractSynDetailService.getService().synData(id);
							} catch (SQLException e) {
								logger.error(e.getMessage(),e);
							}
						}else{
							contractModel.set("CONTRACT_ID",contractObj.getString("CONTRACT_ID"));
//							contractModel.set("CONTRACT_NAME",projectName);
							contractModel.set("AMOUNT",Money);
							contractModel.set("SIGN_DATE", signDate);
							contractModel.set("YEAR", year);
//							contractModel.set("CREATE_TIME", createTime);
							contractModel.update();
						}
					} catch (SQLException e) {
						logger.error(null,e);
					}
				 }
			 }
			return msg.toString();
		} catch (DocumentException e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}
	
}







