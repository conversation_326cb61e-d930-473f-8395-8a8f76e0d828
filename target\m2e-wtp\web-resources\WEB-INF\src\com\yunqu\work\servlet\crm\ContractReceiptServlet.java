package com.yunqu.work.servlet.crm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.ContractService;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;

@WebServlet("/servlet/contractReceipt/*")
public class ContractReceiptServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public EasyRecord getReceiptModel(String prefix)
    {
        EasyRecord receiptModel = new EasyRecord("yq_contract_receipt","RECEIPT_ID");
        receiptModel.setColumns(getJSONObject(prefix));
        return receiptModel;
    }

    public EasyResult actionForAdd() {
        EasyRecord receiptModel = getReceiptModel("receipt");
        receiptModel.setPrimaryValues(RandomKit.uuid().toUpperCase());
        receiptModel.set("CREATE_TIME", EasyDate.getCurrentDateString());
        receiptModel.set("CREATOR", getUserName());
        receiptModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        receiptModel.set("UPDATE_BY", getUserName());
        String contractId = receiptModel.getString("CONTRACT_ID");
        String receiptDate = receiptModel.getString("RECEIPT_DATE");
        receiptModel.set("MONTH_ID", DateUtils.getMonthIdFormat(receiptDate));
        receiptModel.set("YEAR_ID",  receiptDate.substring(0, 4));

        String contractStageId = receiptModel.getString("STAGE_ID");
        try {
            this.getQuery().save(receiptModel);
            ContractService.getService().reloadCount("RECEIPT_COUNT","yq_contract_receipt",contractId);
            ContractService.getService().setCompleteTime(contractStageId);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdate() {
        EasyRecord receiptModel = getReceiptModel("receipt");
        receiptModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        receiptModel.set("UPDATE_BY", getUserName());
        String receiptDate = receiptModel.getString("RECEIPT_DATE");
        receiptModel.set("MONTH_ID", DateUtils.getMonthIdFormat(receiptDate));
        receiptModel.set("YEAR_ID",  receiptDate.substring(0, 4));

        String contractStageId = receiptModel.getString("STAGE_ID");
        try {
            this.getQuery().update(receiptModel);
            ContractService.getService().setCompleteTime(contractStageId);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForBatchDel() {
        JSONArray receiptArray = getJSONArray();
        for (int i = 0; i < receiptArray.size(); i++) {
            JSONObject receiptObject = receiptArray.getJSONObject(i);
            try {
                this.getQuery().executeUpdate("delete from yq_contract_receipt where RECEIPT_ID = ?", receiptObject.getString("RECEIPT_ID"));
                String contractId = receiptObject.getString("CONTRACT_ID");
                ContractService.getService().reloadCount("RECEIPT_COUNT","yq_contract_receipt",contractId);
                String contractStageId = receiptObject.getString("STAGE_ID");
                ContractService.getService().setCompleteTime(contractStageId);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }

}
