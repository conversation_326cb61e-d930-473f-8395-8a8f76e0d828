package com.yunqu.work.base;

import java.sql.SQLException;

import org.easitline.common.core.dao.DaoContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;
/**
 * dao base class
 * <AUTHOR>
 *
 */

public class AppDaoContext extends DaoContext {

	protected EasyQuery query;
	
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_NAME;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	public EasyQuery getMainQuery(){
		return EasyQuery.getQuery(getAppName(), Constants.MARS_DS_NAME);
	}
	
	@Override
	public EasyQuery getQuery(){
		if(query!=null) {
			return query;
		}
		EasyQuery _query = super.getQuery();
		_query.setLogger(getLogger());
		return _query;
	}
	
	protected String queryForString(String sql,Object... params) {
		try {
			return this.getQuery().queryForString(sql, params);
		} catch (SQLException e) {
			this.error(null, e);
			return "";
		}
	}
	
	@Override
	protected boolean loginCheck() {
		return true;
	}
	protected String getUserId() {
		return getUserPrincipal().getUserId();
	}
	protected String getLoginAcct() {
		return getUserPrincipal().getLoginAcct();
	}
	
	@Override
	protected boolean isSuperUser(){
		return this.getUserPrincipal().isRole("SYS_SUPER_USER")||hasRole("DATA_SUPER_USER");
	}
	
	protected boolean isFlowMgr(){
		return hasRole("FLOW_SUPER_USER");
	}
	
	protected boolean hasRole(String roleId) {
		return getUserPrincipal().isRole(roleId);
	}
	
	protected boolean hasRes(String resId) {
		return getUserPrincipal().isResource(resId);
	}
	
	protected boolean isDeptLeader() {
		StaffModel model =  getStaffInfo();
		return model.getDeptLeader().equalsIgnoreCase(getUserId());
	}
	
	protected String getDeptId() {
		return getStaffInfo().getDeptId();
	}
	
	protected String getMgrDeptIds() {
		String deptIds = null;
		String ids  = StaffService.getService().getUserMgrDeptIds(getUserId());
		if(StringUtils.notBlank(ids)) {
			deptIds =  ids+","+getStaffInfo().getDeptId();
		}else {
			deptIds =  getStaffInfo().getDeptId();
		}
		return deptIds;
	}
	
	protected String[] getMgrDeptArray() {
		return getMgrDeptIds().split(",");
	}
	
	protected StaffModel getStaffInfo() {
		Object object =  getUserPrincipal().getAttribute("staffInfo");
		if(object==null) {
			StaffModel staffModel = StaffService.getService().getStaffInfo(getUserId());
			getUserPrincipal().setAttribute("staffInfo",staffModel);
			return staffModel;
		}
		String jsonStr = JSONObject.toJSONString(object);
		JSONObject jsonObject = JSONObject.parseObject(jsonStr);
		return JSONObject.toJavaObject(jsonObject, StaffModel.class);
	}
}
