<%@ page language="java" contentType="text/html;charset=UTF-8"%> 
<%@ include file="/pages/common/global.jsp" %> 
<%@ page language="java" contentType="text/html;charset=UTF-8"%> 
<EasyTag:override name="head"> 
    <title>流程节点配置</title> 
    <style> 
        .layui-timeline-item:hover a{opacity:1;} 
    </style> 
</EasyTag:override> 
<EasyTag:override name="content"> 
        <form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm"> 
       		<input value="${param.flowCode}" type="hidden" id="flowCode" name="flowCode"/>
           	<svg id="svg"></svg>
        </form> 
</EasyTag:override> 
<EasyTag:override name="script"> 

	<!--LogicFlow core包js-->
	<script src="/yq-work/static/js/snap.svg.js"></script>
	<script src="/yq-work/static/js/diagram.js?x=1"></script>
    <script type="text/javascript"> 
      
    
    ajax.remoteCall("/yq-work/webcall?action=FlowConfigDao.nodeConfig",{flowCode:$('#flowCode').val()},function(result){
		var data = result.data;
		var nodes = [];
		var lines = [];
		var i = 1;
		var z = 0;
		var startNodeId;
		for(var index in data){
			var row = data[index];
			var type = row['NODE_TYPE'];
			var orderIndex = row['CHECK_INDEX'];
			var nodeName = row['NODE_NAME'];
			var nextNodeId = row['C_NODE_ID'];
			var pNodeId = row['P_NODE_ID'];
			var conditionConf = row['CONDITION_CONF'];
			var id = row['NODE_ID'];
			
			nodes.push({type:'task',id:id,name:nodeName,cell:''+i+','+z+'',w:150});
			
			if(nextNodeId=='0'){
				lines.push({from:id,to:'end'});
			}else{
				lines.push({from:id,to:nextNodeId});
			}
			if(pNodeId=='0'){
				startNodeId = id;
			}
			i++;
		}
		
		nodes.push({type:'start',id:'start',name:'开始',cell:'0,0',w:120});
		nodes.push({type:'end',id:'end',name:'结束',cell:''+i+',0',w:120});
		
		lines.push({from:'start',to:startNodeId});
		
		 var _data = {
            "cellPadding" : 15,
            "tableMargin" : 10,
            "nodes": nodes,
            "lines": lines
        };
        var flow = new FlowDraw("svg", _data);
        flow.renderByTableLayout();
		        
	});
    
    function nodeClick(data){
    	 var id = data.id; 
         var flowCode = $("[name='flowCode']").val(); 
    }
   
	   
              
    </script> 
</EasyTag:override> 
<%@ include file="/pages/common/layout_div.jsp" %> 