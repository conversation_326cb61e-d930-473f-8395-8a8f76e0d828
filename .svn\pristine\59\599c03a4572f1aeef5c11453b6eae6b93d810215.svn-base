<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
	<table id="stageTable"></table>
		<script  type="text/html" id="toolbar">
			<button class="btn btn-info btn-sm" onclick="addStage()" type="button">新增</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="reloadDataList" type="button">刷新</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="updateStage" type="button">修改</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="deleteStage" type="button">删除</button>
		</script>
	<script type="text/javascript">
		$(function(){
			var contractId='${param.contractId}';
			$("#ContractDetailForm").initTable({
				mars:'ContractStageDao.stageList',
				id:'stageTable',
				page:true,
				toolbar:'#toolbar',
				cellMinWidth:50,
				cols: [[
					{
						type: 'checkbox',
						align:'left'
					},{
						field: 'STAGE_NAME',
						title: '阶段名称',
						align:'left',
						minWidth:80
					},{
						field:'IS_RECEIVE',
						title:'是否应收',
						align:'left',
						minWidth:70
					},{
						field:'RCV_DATE',
						title:'应收日期',
						width:80
					},{
						field:'PLAN_COMP_DATE',
						title:'计划完成日期',
						width:80
					},{
						field:'ACT_COMP_DATE',
						title:'实际完成日期',
						sort:true,
						minWidth:80
					},{
						field:'RATIO',
						title:'比例(%)',
						width:50,
					},{
						field:'PLAN_RCV_AMT',
						title:'计划收款金额',
						sort:true,
						minWidth:100
					},{
						field:'KAIPIAO_AMT',
						title:'已开票金额',
						sort:true,
						minWidth:100
					},{
						field:'SHOU_AMT',
						title:'已收款金额',
						sort:true,
						minWidth:100
					},{
						field:'YK_WB_COND',
						title:'余款维保条件',
						minWidth:100
					},{
						field:'YK_COND_DAYS',
						title:'余款条件天数',
						width:90
					},{
						field:'BAD_DEBT_AMT',
						title:'坏账金额',
						width:80
					},{
						field: 'UPDATE_TIME',
						title: '更新时间',
						width:120,
						sort:true,
						align:'center'
					}
				]]
			});
		});

		
		function reloadDataList(){
			$("#ContractDetailForm").queryData({id:'stageTable',page:false});
		}

		
		function addStage(){
			var contractId = $("[name='contractId']").val();
			popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增阶段信息',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/stage-edit.jsp',title:'新增阶段信息',data:{contractId:contractId,isNew:'1'}});
		}
		
		function updateStage(data){
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择阶段',{icon : 7, time : 1000});
	 			return;
	 		}
			if(sum > 1 ){
				layer.msg('一次只能更新一个阶段',{icon : 7, time : 1000});
				return;
			}
			var stageId = data[0]['STAGE_ID'];
			var contractId = $("[name='contractId']").val();
			popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增阶段信息',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/stage-edit.jsp',title:'编辑阶段信息',data:{contractId:contractId,stageId:stageId,isNew:'0'}});
		}

		function deleteStage(data){
			var sum = data.length;
			if(sum <= 0){
				layer.msg('请选择要删除的阶段',{icon : 7, time : 1000});
				return;
			}
			if(sum > 1 ){
				layer.msg('一次只能删除一个阶段',{icon : 7, time : 1000});
				return;
			}
			layer.confirm('确认要删除阶段信息吗？', {
				btn: ['确认删除', '取消']
			}, function(){
				//确认删除
				//取出表单提交的data中的stageId
				var data1 ={stageId : data[0]['STAGE_ID']};
				ajax.remoteCall("${ctxPath}/servlet/contractStage?action=DelData",data1,function(result) {
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
							reloadDataList();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}, function(){
				layer.msg('取消删除');
			});
		}


		
		
		
</script>
