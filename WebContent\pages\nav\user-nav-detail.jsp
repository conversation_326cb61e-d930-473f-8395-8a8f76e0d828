<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>nav</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="UserNavDao.record" autocomplete="off" data-mars-prefix="">
		  	<input type="hidden" value="${param.navId}" name="navId"/>
			<table class="table">
		        <tbody>
		            <tr>
	                    <td>
	                    	<div id="URL" style="display: inline-block;"></div>
	                    	<a class="pull-right" href="javascript:navMgr();">修改</a>
	                    </td>
		            </tr>
		            <tr>
	                    <td>
		                   	 <div id="REMARK"></div>
	                    </td>
		            </tr>
		        </tbody>
					  </table>
			 <div class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm"  onclick="goUrl()">立即访问 </button>
			</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		$(function(){
			$("#editForm").render({success:function(result){
				
			}});  
		});
		function goUrl(){
			var url=$("#URL").text();
			window.open(url);
			layer.closeAll();
		}
		function navMgr(){
			popup.openTab({title:'导航管理',id:'userNavMgr',url:'${ctxPath}/pages/nav/user-nav-mgr.jsp',data:{id:'${param.navId}'}});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>