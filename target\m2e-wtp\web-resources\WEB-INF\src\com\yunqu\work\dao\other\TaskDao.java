package com.yunqu.work.dao.other;

import java.sql.SQLException;
import java.util.List;

import com.yunqu.work.utils.WeekUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.TaskModel;
import com.yunqu.work.service.LookLogService;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.TaskUtils;

@WebObject(name="TaskDao")
public class TaskDao extends AppDaoContext {
	
	@WebControl(name = "projectTaskGroupList",type = Types.TEMPLATE)
	public JSONObject projectTaskGroupList() {
		String projectId = param.getString("projectId");
		EasySQL sql = getEasySQL("select t1.*,count(t2.group_id) task_count,min(t2.plan_started_at) plan_started_at,max(t2.deadline_at) deadline_at  from yq_task_group t1 LEFT JOIN yq_task t2 on t1.group_id = t2.group_id where 1=1");
		sql.append(projectId,"and t2.project_id = ?",false);
		sql.append("group BY t1.group_id");
		sql.append("order by t1.order_index");
		JSONObject result  = queryForList(sql.getSQL(), sql.getParams());
		try {
			List<JSONObject> task = this.getQuery().queryForList("select t1.* from yq_task t1 where t1.project_id = ? and t1.group_id <> '' order by t1.plan_started_at,t1.group_id",new Object[]{projectId},new JSONMapperImpl());
			result.put("taskList", task);
			result.put("taskCount", task.size());
		} catch (SQLException e) {
			this.error(null, e);
		}
		return result;
	}
	
	@WebControl(name = "groupInfo",type = Types.RECORD)
	public JSONObject groupInfo() {
		String groupId = param.getString("groupId");
		if(StringUtils.isBlank(groupId)) {
			return getJsonResult(new JSONObject());
		}
		EasySQL sql = getEasySQL("select * from yq_task_group where 1=1");
		sql.append(groupId,"and group_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		TaskModel model=new TaskModel();
		model.setColumns(getParam("task"));
		JSONObject jsonObject= queryForRecord(model);
		JSONObject data=jsonObject.getJSONObject("data");
		if(StringUtils.isBlank(model.getTaskId())) {
			return getJsonResult(new JSONObject());
		}
		String sql="select USER_ID from  yq_cc where FK_ID = ?";
		List<EasyRow> list;
		try {
			list = this.getQuery().queryForList(sql, model.getTaskId());
			if(list!=null&&list.size()>0){
				String[] mailtos=new String[list.size()];
				for(int i=0;i<list.size();i++){
					mailtos[i]=list.get(i).getColumnValue(1);
				}
				data.put("mailtos",mailtos);
				jsonObject.put("mailtos", mailtos);
				jsonObject.put("data", data);
			}
			String sjId = data.getString("BUSINESS_ID");
			if(StringUtils.notBlank(sjId)) {
				String sjName = this.getQuery().queryForString("select BUSINESS_NAME from YQ_CRM_BUSINESS where BUSINESS_ID = ?",sjId);
				jsonObject.put("sjName",sjName);
			}
			
			String projectId = data.getString("PROJECT_ID");
			if(StringUtils.notBlank(projectId)){
				String projectName=this.getQuery().queryForString("select PROJECT_NAME from YQ_PROJECT where PROJECT_ID = ?",projectId);
				jsonObject.put("projectName",projectName);
			}
			
			String taskId=data.getString("P_TASK_ID");
			if(StringUtils.notBlank(taskId)){
				String taskName=this.getQuery().queryForString("select task_name from yq_task where TASK_ID = ?",taskId);
				jsonObject.put("taskName",taskName);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		if(StringUtils.notBlank(model.getTaskId())){
			String creator=data.getString("CREATOR");
			String assignUserId=data.getString("assign_user_id");
			if(!getUserId().equals(creator)||creator.equals(assignUserId)){
				try {
					this.getQuery().executeUpdate("update yq_task t1 set t1.view_count = t1.view_count +1 where task_id  = ?",model.getTaskId());
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
				LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getTaskId(),"task",request);
			}
		}
		if(StringUtils.notBlank(model.getTaskId())){
			try {
				List<JSONObject> childList= this.getQuery().queryForList("select * from yq_task where P_TASK_ID = ?",new Object[]{model.getTaskId()},new JSONMapperImpl());
				jsonObject.put("childTaskList", childList);
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
		return jsonObject;
	}
	
	@WebControl(name="taskType",type=Types.DICT)
	public JSONObject taskType(){
		String projectId=param.getString("projectId");
		if(StringUtils.notBlank(projectId)){
			return getDictByQuery("select task_type_id,task_type_name from yq_task_type where project_id in (0,'"+ param.getString("projectId")+"') order by order_index");
		}else{
			return getDictByQuery("select task_type_id,task_type_name from yq_task_type where project_id = ? order by order_index",0);
		}
	}

	
	@WebControl(name="taskSelector",type=Types.LIST)
	public JSONObject taskSelector(){
		EasySQL sql=getEasySQL("select task_id,task_name,assign_user_id,create_time,assign_user_name,create_name from YQ_TASK where 1=1");
		String taskName = param.getString("taskName");
		if(StringUtils.isBlank(taskName)) {
			sql.append(getUserId(),"and creator  = ?");
		}
		sql.append(param.getString("projectId"),"and project_id  = ?");
		sql.appendLike(taskName,"and TASK_NAME like ?");
		sql.append("order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	/**
	 * 我发起的任务
	 * @return
	 */
	@WebControl(name="myTaskList",type=Types.LIST)
	public JSONObject myTaskList(){
		int status=param.getIntValue("status");
		EasySQL sql=null;
		if(status==1){//我负责的任务
			sql=getEasySQL("select t1.*,t2.PROJECT_NAME from yq_task t1  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserId(),"and t1.ASSIGN_USER_ID = ?",true);
		}else if(status==2){
			sql=getEasySQL("select t1.*,t2.PROJECT_NAME from yq_task t1  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserId(),"and t1.CREATOR = ?",true);
		}else if(status==3){ //抄送我的任务
			sql=getEasySQL("select t1.*,t3.create_time cc_time,t2.PROJECT_NAME from yq_task t1 INNER JOIN yq_cc t3 on t3.fk_id=t1.task_id  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserId(),"and t3.user_id = ?",true);
		}else if(status==4){
			//共享任务
			sql=getEasySQL("select t1.* from yq_task t1  where 1=1");
			sql.append(Constants.TASK_OK,"and t1.task_state >= ? and t1.task_auth = 0");
			TaskUtils.setTaskCondition(param, getDeptId(),sql);
		}else if(status==9) {
			sql = getEasySQL("select t1.*,t3.create_time delay_time,t3.old_plan_time,t3.new_plan_time,t3.reason delay_reason,t2.PROJECT_NAME from yq_task t1 INNER JOIN yq_task_delay t3 on t3.task_id = t1.task_id  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserId(),"and t3.user_id = ?",true);
		}else{
			return emptyPage();
		}
		String field=param.getString("field");
		String order=param.getString("order");
		if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
			sql.append("order by").append(field).append(order);
			
		}else{
			sql.append("order by t1.CREATE_TIME desc");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	
	@WebControl(name="myTaskProjectTree",type = Types.TEMPLATE)
	public JSONObject myTaskProjectTree(){
		int status=param.getIntValue("status");
		EasySQL sql = null;
		if(status==1){//我负责的任务
			sql=getEasySQL("select t1.PROJECT_ID,concat('(',count(1),')',t2.PROJECT_NAME) PROJECT_NAME,count(1) count from yq_task t1  INNER join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserId(),"and t1.ASSIGN_USER_ID = ?",true);
		}else if(status==2){
			sql=getEasySQL("select t1.PROJECT_ID,concat('(',count(1),')',t2.PROJECT_NAME) PROJECT_NAME,count(1) count from yq_task t1  INNER join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserId(),"and t1.CREATOR = ?",true);
		}else if(status==3){
			sql=getEasySQL("select t1.PROJECT_ID,concat('(',count(1),')',t2.PROJECT_NAME) PROJECT_NAME,count(1) count from yq_task t1 INNER JOIN yq_cc t3 on t3.fk_id=t1.task_id  INNER join YQ_PROJECT t2 on t1.PROJECT_ID = t2.PROJECT_ID  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserId(),"and t3.USER_ID = ?",true);
		}else{
			return emptyPage();
		}
		sql.append(DateUtils.getPlanMaxDay(-365),"and t1.DATE_ID > ?");
		sql.append("group by t2.PROJECT_ID");
		sql.append("order by t1.CREATE_TIME desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="myTaskListCountStat",type=Types.RECORD)
	public JSONObject myTaskListCountStat(){
		int status=param.getIntValue("status");
		EasySQL sql=null;
		if(status==1){//我负责的任务
			sql=getEasySQL("select");
			sql.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
			sql.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
			sql.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
			sql.append(40,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS d,");
			sql.append(11,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS f,");
			sql.append(12,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS g,");
			sql.append(13,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS h,");
			sql.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
			sql.append("from yq_task t1  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserPrincipal().getUserId(),"and t1.ASSIGN_USER_ID = ?",true);
		}else if(status==2){
			sql=getEasySQL("select");
			sql.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
			sql.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
			sql.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
			sql.append(40,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS d,");
			sql.append(11,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS f,");
			sql.append(12,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS g,");
			sql.append(13,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS h,");
			sql.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
			sql.append("from yq_task t1  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserPrincipal().getUserId(),"and t1.CREATOR = ?",true);
		}else if(status==3){
			sql=getEasySQL("select");
			sql.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
			sql.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
			sql.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
			sql.append(40,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS d,");
			sql.append(11,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS f,");
			sql.append(12,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS g,");
			sql.append(13,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS h,");
			sql.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
			sql.append("from yq_task t1 INNER JOIN yq_cc t2 on t2.fk_id=t1.task_id  where 1=1");
			TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			sql.append(getUserPrincipal().getUserId(),"and t2.user_id = ?",true);
		}else if(status==4){
			//共享任务
			sql=getEasySQL("select t1.* from yq_task t1  where 1=1");
			sql.append(Constants.TASK_OK,"and t1.task_state = ? and t1.task_auth = 0");
			TaskUtils.setTaskCondition(param, getDeptId(),sql);
		}else{
			return getJsonResult(new JSONObject());
		}
		JSONObject result = queryForRecord(sql.getSQL(), sql.getParams());
		JSONObject data = result.getJSONObject("data");
		String[] keys=new String[]{"A","B","C","D","E","G","H"};
		for(int i=0;i<keys.length;i++){
			if(StringUtils.isBlank(data.getString(keys[i]))){
				data.put(keys[i],"0");
			}
		}
		result.put("data", data);
		return result;
	}


	@WebControl(name="myTaskListKanban",type=Types.TEMPLATE)
	public JSONObject myTaskListKanban(){
		String taskType = this.getMethodParam(0).toString();
		JSONObject result = new JSONObject();

		// 获取任务列表
		EasySQL sql = buildSql(taskType);
		sql.append("and t1.task_state in(10,20)");
		result = this.queryForList(sql.getSQL(), sql.getParams());

		// 获取超时未完成任务
		EasySQL sql2 = buildSql(taskType);
		sql2.append(EasyDate.getCurrentDateString(),"and t1.deadline_at < ?");
		sql2.append("and t1.task_state in(10,20)");
		JSONObject result2 = this.queryForList(sql2.getSQL(), sql2.getParams());
		result.put("data2", result2.getJSONArray("data"));

		// 获取本周已完成任务
		EasySQL sql3 = buildSql(taskType);
		sql3.append(getCurrWeekBeginDay(),"and t1.finish_time >= ?");
		sql3.append(getCurrWeekEndDay(),"and t1.finish_time <= ?");
		sql3.append("and t1.task_state in(30,40)");
		JSONObject result3 = this.queryForList(sql3.getSQL(), sql3.getParams());
		result.put("data3", result3.getJSONArray("data"));
		result.put("taskType",taskType);
		return result;
	}

	private EasySQL buildSql(String taskType){
		// 构建基础查询条件
		String baseSelect = "select t1.*,t2.PROJECT_NAME from YQ_TASK t1 left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID ";
		String ccJoin = "INNER JOIN yq_cc t3 on t3.fk_id=t1.task_id ";
		String condition = "1".equals(taskType) ? "t1.ASSIGN_USER_ID" : "t1.CREATOR";

		EasySQL sql = getEasySQL(baseSelect + "where 1=1");
		if("3".equals(taskType)){
			sql = getEasySQL(baseSelect + ccJoin + "where 1=1");
			sql.append(getUserId(),"and t3.user_id = ?");
		} else {
			sql.append(getUserId(),"and " + condition + " = ?");
		}
		return sql;
	}

	/**
	 * 项目任务列表
	 * @return
	 */
	@WebControl(name="projectTaskList",type=Types.LIST)
	public JSONObject projectTaskList(){ 
		EasySQL easySQL=getEasySQL("select t1.* from YQ_TASK t1 where 1=1");
		TaskUtils.setTaskConditionWithState(param, getDeptId(),easySQL);
		
		String field=param.getString("field");
		String order=param.getString("order");
		if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
			easySQL.append("order by").append(field).append(order);
		}else{
			easySQL.append("order by t1.CREATE_TIME desc");
		}
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	
	/**
	 * 任务查询
	 * @return
	 */
	@WebControl(name="taskList",type=Types.LIST)
	public JSONObject taskList(){
		EasySQL sql = getEasySQL("select t1.*,t2.PROJECT_NAME from YQ_TASK t1 left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID where 1=1");
		TaskUtils.setTaskConditionWithState(param, getMgrDeptIds(),sql);
		String field = param.getString("field");
		String order = param.getString("order");
		if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
			sql.append("order by").append(field).append(order);
		}else{
			sql.append("order by t1.CREATE_TIME desc");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="delayTaskList",type=Types.LIST)
	public JSONObject delayTaskList(){
		EasySQL sql = getEasySQL("select t1.*,t2.PROJECT_NAME,t3.create_time delay_time,t3.old_plan_time,t3.new_plan_time,t3.reason delay_reason from yq_task t1 INNER JOIN yq_task_delay t3 on t3.task_id = t1.task_id  left join YQ_PROJECT t2 on t1.PROJECT_ID = t2.PROJECT_ID  where 1=1");
		TaskUtils.setTaskConditionWithState(param, getMgrDeptIds(),sql);
		String applyDeplayTime = param.getString("applyDeplayTime");
		if(StringUtils.isNotBlank(applyDeplayTime) && applyDeplayTime.split(" 到 ").length > 1){
			String[] arr = applyDeplayTime.split(" 到 ");
			sql.append(arr[0]+" 00:00:00",  "and t3.create_time >= ?");
			sql.append(arr[1]+" 23:59:59",  "and t3.create_time <= ?");
		}
		String field = param.getString("field");
		String order = param.getString("order");
		if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
			sql.append("order by").append(field).append(order);
		}else{
			sql.append("order by t3.CREATE_TIME desc");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="taskProjectTree",type = Types.TEMPLATE)
	public JSONObject taskProjectTree(){
		String isSelfDept = param.getString("isSelfDept");
		String businessId = param.getString("taskBusinessId");
		boolean taskManager = hasRole("TASK_MANAGER");
		if(!"1".equals(isSelfDept)&&isDeptLeader()&&!taskManager) {
			param.put("isSelfDept","1");
			isSelfDept = "1";
		}
		if(isSuperUser()||taskManager||"1".equals(isSelfDept)||StringUtils.notBlank(businessId)){
			EasySQL easySQL=getEasySQL("select t1.PROJECT_ID,concat('(',count(1),')',t2.PROJECT_NAME) PROJECT_NAME,count(1) count from YQ_TASK t1 inner join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID where 1=1");
			TaskUtils.setTaskConditionWithState(param, getMgrDeptIds(),easySQL);
			easySQL.append(DateUtils.getPlanMaxDay(-365),"and t1.DATE_ID > ?");
			easySQL.append("group by t2.PROJECT_ID");
			easySQL.append("order by t1.CREATE_TIME desc");
			return queryForList(easySQL.getSQL(), easySQL.getParams());
		}else{
			return emptyPage();
		}
	}
	
	
	/**
	 * 人员任务统计
	 * @return
	 */
	@WebControl(name="userTaskStat",type=Types.LIST)
	public JSONObject userTaskStat(){
		EasySQL easySQL=getEasySQL("select t1.assign_user_id,t1.assign_user_name,");
		easySQL.append("t1.assign_dept_name,");
		easySQL.append("count(distinct(t1.project_id)) as project_count,");
		easySQL.append("GROUP_CONCAT(distinct(t2.project_name)) AS project_name,");
		easySQL.append("SUM(1) AS f,");
		easySQL.append("SUM(CASE WHEN t1.task_state in(30,40) THEN work_hour ELSE 0 END) AS work_hour,");
		easySQL.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
		easySQL.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state < ? THEN 1 ELSE 0 END) AS g,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS h,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
		easySQL.append(40,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS d,");
		easySQL.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
		easySQL.append("from yq_task t1 left join yq_project t2 on t1.project_id = t2.project_id  where 1=1");
		
		String isSelfDept = param.getString("isSelfDept");
		boolean taskManager = hasRole("TASK_MANAGER");
		if(taskManager&&!"1".equals(isSelfDept)) {
			TaskUtils.setTaskConditionWithState(param,"0",easySQL);
		}else {
			TaskUtils.setTaskConditionWithState(param, getMgrDeptIds(),easySQL);
		}
		easySQL.append("GROUP BY t1.ASSIGN_USER_ID");
		easySQL.append("order by f desc,g desc");
		return queryForList(easySQL.getSQL(), easySQL.getParams());
	}
	
	@WebControl(name="projectUserTaskStat",type=Types.LIST)
	public JSONObject projectUserTaskStat(){
		EasySQL easySQL=getEasySQL("select t2.project_name,t1.project_id,t1.assign_user_id,t1.assign_user_name,t1.assign_dept_name,");
		easySQL.append("SUM(1) AS f,");
		easySQL.append("SUM(CASE WHEN t1.task_state in(30,40) THEN work_hour ELSE 0 END) AS work_hour,");
		easySQL.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
		easySQL.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state < ? THEN 1 ELSE 0 END) AS g,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS h,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
		easySQL.append(40,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS d,");
		easySQL.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
		easySQL.append("from yq_task t1 INNER JOIN yq_project t2 on t2.PROJECT_ID=t1.project_id where 1=1");
		easySQL.append("and t1.project_id <>''");
		TaskUtils.setTaskConditionWithState(param, getMgrDeptIds(),easySQL);
		easySQL.append("GROUP BY t1.project_id,t1.assign_user_id");
		easySQL.append("order by t2.last_task_time desc,t1.project_id,t1.dept_id");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	
	@WebControl(name="deptProejctList",type = Types.TEMPLATE)
	public JSONObject deptProejctList(){
		Object deptId = getMethodParam(0);
		EasySQL easySQL=getEasySQL("select t2.project_name,t1.project_id,t1.assign_dept_id,t1.assign_dept_name,");
		easySQL.append("SUM(1) AS task_num,");
		easySQL.append("count(distinct(t1.assign_user_id)) as assign_user_num,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state < ? THEN 1 ELSE 0 END) AS not_do");
		easySQL.append("from yq_task t1 inner join yq_project t2 on t2.project_id=t1.project_id where 1=1");
		easySQL.append("and t1.project_id <>''");
		easySQL.append(deptId,"and t1.assign_dept_id = ?");
		
		easySQL.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and t1.create_time >= ?");
		
		TaskUtils.setTaskConditionWithState(param, getMgrDeptIds(),easySQL);
		easySQL.append("group by t1.project_id");
		easySQL.append("order by task_num desc limit 30");
		return queryForList(easySQL.getSQL(), easySQL.getParams());
	}
	
	@WebControl(name="personProjectTaskStat",type = Types.TEMPLATE)
	public JSONObject personProjectTaskStat(){
		EasySQL easySQL=getEasySQL("select t2.project_name,t1.project_id,");
		easySQL.append(getUserId(),"SUM(CASE WHEN t1.creator = ? THEN 1 ELSE 0 END) AS create_num,");
		easySQL.append(getUserId(),"SUM(CASE WHEN t1.assign_user_id = ? THEN 1 ELSE 0 END) AS assign_num,");
		easySQL.append(30,"SUM(CASE WHEN T1.TASK_STATE < ?");
		easySQL.append(getUserId()," AND  ASSIGN_USER_ID = ? THEN 1 ELSE 0 END) AS notdo_num");
		easySQL.append("from yq_task t1 inner join yq_project t2 on t2.project_id=t1.project_id where 1=1");
		easySQL.append("and t1.project_id <>''");
		
		easySQL.append("and (");
		easySQL.append(getUserId(),"t1.creator = ?");
		easySQL.append("or ");
		easySQL.append(getUserId(),"t1.assign_user_id = ?");
		easySQL.append(")");
		
		TaskUtils.setTaskConditionWithState(param, getMgrDeptIds(),easySQL);
		easySQL.append("group by t1.project_id");
		easySQL.append("order by t1.plan_started_at desc limit 12");
		return queryForList(easySQL.getSQL(), easySQL.getParams());
	}
	
	@WebControl(name="myAssignTask",type = Types.TEMPLATE)
	public JSONObject myAssignTask(){
		EasySQL sql=getEasySQL("select t1.* from yq_task t1   where 1=1");
		sql.append(getUserId(),"and t1.ASSIGN_USER_ID = ? limit 12");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	/**
	 * 部门人员任务统计
	 * @return
	 */
	@WebControl(name="deptTaskStat",type=Types.LIST)
	public JSONObject deptTaskStat(){
		EasySQL easySQL=getEasySQL("select t2.USERNAME,t1.assign_user_id,");
		easySQL.append("SUM(1) AS f,");
		easySQL.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
		easySQL.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
		easySQL.append(40,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS d,");
		easySQL.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
		easySQL.append("from yq_task t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t1.assign_user_id=t2.USER_ID where 1=1");
		easySQL.append("and t2.STATE=0 ");
		String deptId=param.getString("deptId");
		String projectId=param.getString("projectId");
		if(StringUtils.notBlank(deptId)){
			easySQL.appendIn(deptId.split(","),"and t1.assign_dept_id");
		}else{
			if(StringUtils.isBlank(projectId)){
				easySQL.appendIn(getMgrDeptArray(),"and t1.assign_dept_id");
			}else{
				easySQL.append(projectId,"and t1.project_id = ?");
			}
		}
		//createTime
		String createTime = param.getString("createTime");
		if(StringUtils.isNotBlank(createTime) && createTime.split(" 到 ").length > 1){
			String[] arr = createTime.split(" 到 ");
			easySQL.append(arr[0]+" 00:00:00",  "and t1.CREATE_TIME >= ?");
			easySQL.append(arr[1]+" 23:59:59",  "and t1.CREATE_TIME <= ?");
		}
		String planDate = param.getString("planDate");
		if(StringUtils.isNotBlank(planDate) && planDate.split(" 到 ").length > 1){
			String[] arr = planDate.split(" 到 ");
			easySQL.append("and ((");
			easySQL.append(arr[0]+" 00:00",  "t1.plan_started_at >= ?");
			easySQL.append(arr[1]+" 23:59",  "and t1.deadline_at <= ?");
			easySQL.append(")");
			easySQL.append("or (");
			easySQL.append(arr[0]+" 00:00",  "t1.deadline_at >= ?");
			easySQL.append(arr[1]+" 23:59",  "and t1.plan_started_at <= ?");
			easySQL.append("))");
		}
		easySQL.append("GROUP BY t1.assign_user_id");

		return queryForList(easySQL.getSQL(), easySQL.getParams());
	}
	/**
	 * 项目任务统计
	 * @return
	 */
	@WebControl(name="projectTaskStat",type=Types.LIST)
	public JSONObject projectTaskStat(){
		EasySQL easySQL=getEasySQL("select t2.project_name,t2.project_no,t1.project_id,GROUP_CONCAT(distinct(t1.assign_dept_name)) AS assign_dept_name,");
		easySQL.append("SUM(1) AS f,");
		easySQL.append("SUM(CASE WHEN t1.task_state in(30,40) THEN work_hour ELSE 0 END) AS work_hour,");
		easySQL.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
		easySQL.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state < ? THEN 1 ELSE 0 END) AS g,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS h,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
		easySQL.append(40,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS d,");
		easySQL.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
		easySQL.append("from yq_task t1 INNER JOIN yq_project t2 on t2.PROJECT_ID=t1.project_id where 1=1");
		easySQL.append("and t1.project_id <>''");
		TaskUtils.setTaskConditionWithState(param, getMgrDeptIds(),easySQL);
		easySQL.append("GROUP BY t1.project_id");
		easySQL.append("order by t2.last_task_time desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	
	
	@WebControl(name="taskCountStat",type=Types.RECORD)
	public JSONObject taskCountStat(){
		EasySQL easySQL=getEasySQL("select ");
		easySQL.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
		easySQL.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
		easySQL.append(40,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS d,");
		easySQL.append(11,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS f,");
		easySQL.append(12,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS g,");
		easySQL.append(13,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS h,");
		easySQL.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
		easySQL.append("from YQ_TASK t1 where 1=1");
		TaskUtils.setTaskCondition(param, getDeptId(),easySQL);
		JSONObject result = queryForRecord(easySQL.getSQL(), easySQL.getParams());
		JSONObject data = result.getJSONObject("data");
		String[] keys=new String[]{"A","B","C","D","E","F","G","H"};
		for(int i=0;i<keys.length;i++){
			if(StringUtils.isBlank(data.getString(keys[i]))){
				data.put(keys[i],"0");
			}
		}
		result.put("data", data);
		return result;
	}
	
	private void addDate(EasySQL sql) {
		sql.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and t3.create_time >= ?");
	}
	
	@WebControl(name="taskAllLookLog",type=Types.LIST)
	public JSONObject taskAllLookLog(){
		EasySQL easySQL=getEasySQL("select t3.*,t2.USERNAME,t1.look_time from yq_look_log t1,"+Constants.DS_MAIN_NAME+".easi_user t2,yq_task t3 where t1.look_by=t2.USER_ID and t3.task_id=t1.fk_id ");
		String projectId = param.getString("projectId");
		easySQL.append(projectId," and t3.project_id = ?");
		if(StringUtils.isBlank(projectId)&&!isSuperUser()) {
			easySQL.append(getDeptId()," and t3.dept_id = ?");
		}
		this.addDate(easySQL);
		easySQL.append("ORDER BY t1.look_time desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	
	@WebControl(name="taskAllCommentLog",type=Types.LIST)
	public JSONObject taskAllCommentLog(){
		EasySQL easySQL=getEasySQL("select t3.*,t2.USERNAME,t1.create_time as comment_time,t1.content,t1.creator comment_by from yq_comments t1,"+Constants.DS_MAIN_NAME+".easi_user t2,yq_task t3 where t1.creator=t2.USER_ID and t3.task_id=t1.fk_id");
		String projectId = param.getString("projectId");
		easySQL.append(projectId," and t3.project_id = ?");
		if(StringUtils.isBlank(projectId)&&!isSuperUser()) {
			easySQL.appendIn(getMgrDeptArray()," and t3.dept_id");
		}
		this.addDate(easySQL);
		easySQL.append("ORDER BY t1.create_time desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	
	@WebControl(name="taskAllOpLog",type=Types.LIST)
	public JSONObject taskAllOpLog(){
		EasySQL easySQL=getEasySQL("select t3.*,t2.USERNAME,t1.op_time,t1.content from yq_oplog t1,"+Constants.DS_MAIN_NAME+".easi_user t2,yq_task t3 where t1.op_by=t2.USER_ID and t3.task_id=t1.fk_id");
		String projectId = param.getString("projectId");
		easySQL.append(projectId," and t3.project_id = ?");
		if(StringUtils.isBlank(projectId)&&!isSuperUser()) {
			easySQL.appendIn(getMgrDeptArray()," and t3.dept_id");
		}
		this.addDate(easySQL);
		easySQL.append("ORDER BY t1.op_time desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	
	@WebControl(name="getBugList",type=Types.LIST)
	public JSONObject getBugList(){
		String sql="select * from zt_bug where assignedTo = ? ";
		EasyQuery query=EasyQuery.getQuery(getAppName(), Constants.ZENTAO_DS_NAME);
		this.setQuery(query);
		return queryForPageList(sql, new Object[]{getUserPrincipal().getLoginAcct()});
	}
	
	@WebControl(name="taskV1",type=Types.TEMPLATE)
	public JSONObject taskV1(){
		EasySQL sql=getEasySQL("select CREATOR,count(1) count from yq_task where 1=1");
		sql.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and create_time >= ?");
		sql.append("GROUP BY CREATOR order by count desc");
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		this.setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="taskV2",type=Types.TEMPLATE)
	public JSONObject taskV2(){
		EasySQL sql=getEasySQL("select ASSIGN_USER_ID,count(1) count from yq_task where 1=1");
		sql.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and create_time >= ?");
		sql.append("GROUP BY ASSIGN_USER_ID order by count desc");
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		this.setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	//待办
	@WebControl(name="taskV3",type=Types.TEMPLATE)
	public JSONObject taskV3(){
		EasySQL sql=getEasySQL("select ASSIGN_USER_ID,count(1) count from yq_task where  task_state=10 ");
		sql.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and create_time >= ?");
		sql.append("GROUP BY ASSIGN_USER_ID order by count desc");
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		this.setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	//超时待办
	@WebControl(name="taskV4",type=Types.TEMPLATE)
	public JSONObject taskV4(){
		EasySQL sql=getEasySQL("select ASSIGN_USER_ID,count(1) count from yq_task where  task_state in (10,20) ");
		sql.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and create_time >= ?");
		sql.append(EasyDate.getCurrentDateString(),"and deadline_at < ?");
		sql.append("GROUP BY ASSIGN_USER_ID order by count desc");
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		this.setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="taskD1",type=Types.TEMPLATE)
	public JSONObject taskD1(){
		EasySQL sql=getEasySQL("select t1.dept_id,t2.DEPT_NAME,count(1) count from yq_task t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_dept t2 on t1.dept_id=t2.dept_id where 1=1");
		sql.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and t1.create_time >= ?");
		sql.append("GROUP BY t1.dept_id ORDER BY count desc");
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		this.setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="taskD2",type=Types.TEMPLATE)
	public JSONObject taskD2(){
		EasySQL sql=getEasySQL("select t1.assign_dept_id,t2.DEPT_NAME,count(1) count from yq_task t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_dept t2 on t1.assign_dept_id=t2.dept_id where 1=1");
		sql.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and t1.create_time >= ?");
		sql.append("GROUP BY t1.assign_dept_id ORDER BY count desc");
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		this.setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	//部门待办
	@WebControl(name="taskD3",type=Types.TEMPLATE)
	public JSONObject taskD3(){
		EasySQL sql=getEasySQL("select t1.assign_dept_id,t2.DEPT_NAME,count(1) count from yq_task t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_dept t2 on t1.assign_dept_id=t2.dept_id where t1.task_state=10");
		sql.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and t1.create_time >= ?");
		sql.append("GROUP BY t1.assign_dept_id ORDER BY count desc");
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		this.setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	//超时待办
	@WebControl(name="taskD4",type=Types.TEMPLATE)
	public JSONObject taskD4(){
		EasySQL sql=getEasySQL("select t1.assign_dept_id,t2.DEPT_NAME,count(1) count from yq_task t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_dept t2 on t1.assign_dept_id=t2.dept_id where t1.task_state in(10,20) ");
		sql.append(EasyDate.getCurrentDateString(),"and t1.deadline_at < ?");
		sql.append(DateUtils.getBeforeDate(param.getIntValue("day")),"and t1.create_time >= ?");
		sql.append("GROUP BY t1.assign_dept_id ORDER BY count desc");
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		this.setQuery(query);
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="projectTaskCreateDayStat",type=Types.OTHER)
	public JSONObject projectTaskCreateDayStat(){
		EasySQL sql=getEasySQL("select DATE_ID date,count(1) count from yq_task where 1=1");
		sql.append(param.getString("projectId"),"and project_id = ?");
		sql.append(" GROUP BY DATE_ID  ORDER BY DATE_ID desc");
		EasyQuery query=getQuery();
		query.setMaxRow(30);
		setQuery(query);
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="zentaoBugList",type = Types.PAGE)
	public JSONObject zentaoBugList(){
		EasyQuery query = this.getQuery();
		EasySQL sql = getEasySQL("select t1.ID,t1.TITLE,t1.STATUS,t1.OPENEDDATE,t1.OPENEDBY,t1.ASSIGNEDTO from zentao.zt_bug t1 where 1=1");
		//openedBy 创建人  assignedTo 负责人 openedDate 指派时间 status(active resolved closed)
		
		String userAcct = getUserPrincipal().getLoginAcct();
		sql.append("and (");
		sql.append(userAcct,"t1.assignedTo = ?");
		sql.append("or ");
		sql.append(userAcct,"t1.openedBy = ?");
		sql.append(")");
		sql.appendLike(param.getString("title"),"and t1.title like ?");
		sql.append(param.getString("id"),"and t1.id = ?");
		sql.append("closed","and t1.assignedTo <> ?");
		sql.append("order by t1.openedDate desc");
		
		this.setQuery(query);
		if(ServerContext.isLinux()) {
			return queryForPageList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		}else {
			return getJsonResult(new JSONArray());
		}
	}

	
	@WebControl(name="zentaoBugs",type = Types.TEMPLATE)
	public JSONObject zentaoBugs(){
		EasyQuery query = EasyQuery.getQuery(getAppName(), Constants.ZENTAO_DS_NAME);
		EasySQL sql = getEasySQL("select t1.* from zt_bug t1 where 1=1");
		String taskId = param.getString("fkId");
		String bugIds = this.queryForString("select bug_ids from yq_task where task_id = ?",taskId );
		sql.appendIn(bugIds.split(","),"and id");
		sql.append("order by t1.openedDate desc");
		this.setQuery(query);
		if(ServerContext.isLinux()) {
			return queryForList(sql.getSQL(), sql.getParams());
		}else {
			return getJsonResult(new JSONArray());
		}
	}
	
	
	@WebControl(name="taskDayStat",type=Types.TEMPLATE)
	public JSONObject taskDayStat(){
		EasySQL sql = new EasySQL("select DATE_ID date,count(1) count1 from yq_task where 1=1");
		if(!isSuperUser()) {
			sql.appendIn(getMgrDeptArray()," and dept_id");
		}
		sql.append("GROUP BY DATE_ID  ORDER BY DATE_ID desc");
		EasyQuery query=getQuery();
		query.setMaxRow(30);
		try {
			List<JSONObject> list = query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			sql = new EasySQL("select FINISH_DATE_ID date,count(1) count2 from yq_task where 1=1");
			if(!isSuperUser()) {
				sql.appendIn(getMgrDeptArray()," and dept_id");
			}
			sql.append("GROUP BY FINISH_DATE_ID  ORDER BY FINISH_DATE_ID desc");
			
			List<JSONObject> list2= query.queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			
			JSONObject result=new JSONObject();
			result.put("rs1",list);
			result.put("rs2",list2);
			return getJsonResult(result);
		} catch (SQLException e) {
			return getJsonResult(new JSONObject());
		}
	}
	

	public String getCurrWeekBeginDay(){
		int weekNo = WeekUtils.getWeekNo();
		if(weekNo<=0) {
			weekNo =  1;
		}
		int year =  EasyCalendar.newInstance().getYear();
		return WeekUtils.getStartDayOfWeekNo(year,weekNo)+" 00:00:00";
	}

	public String getCurrWeekEndDay(){
		int weekNo = WeekUtils.getWeekNo();
		if(weekNo<=0) {
			weekNo =  1;
		}
		int year =  EasyCalendar.newInstance().getYear();
		return WeekUtils.getEndDayOfWeekNo(year,weekNo)+" 23:59:59";
	}
}



