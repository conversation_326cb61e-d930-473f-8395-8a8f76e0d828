package com.yunqu.work.dao.erp;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="OrderDao")
public class OrderDao extends AppDaoContext {

	@WebControl(name="followInfo",type=Types.RECORD)
	public JSONObject followInfo(){
		String followId = param.getString("fkId");
		return queryForRecord("select * from yq_erp_order_follow where follow_id = ?",followId);
	}
	
	@WebControl(name="getOrderNo",type=Types.TEXT)
	public JSONObject getOrderNo(){
		try {
			String val = this.getQuery().queryForString("select max(order_no) from yq_erp_order where order_no like '1200%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult("12000001");
			}else {
				return getJsonResult(Integer.valueOf(val)+1);
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	@WebControl(name="getReviewNo",type=Types.TEXT)
	public JSONObject getReviewNo(){
		long dateId = Long.valueOf(EasyCalendar.newInstance().getFullMonth());
		try {
			String val = this.getQuery().queryForString("select max(review_no) from yq_erp_order_review where review_no like 'CP-"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult("CP-"+dateId+"001");
			}else {
				val = val.replace("CP-", "");
				return getJsonResult("CP-"+String.valueOf(Long.valueOf(val)+1));
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	@WebControl(name="getPaymentNo",type=Types.TEXT)
	public JSONObject getPaymentNo(){
		long dateId = Long.valueOf(EasyCalendar.newInstance().getFullMonth());
		try {
			String val = this.getQuery().queryForString("select max(payment_no) from yq_erp_order_payment where payment_no like '"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult(dateId+"001");
			}else {
				return getJsonResult(String.valueOf(Long.valueOf(val)+1));
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String orderId = param.getString("orderId");
		return queryForRecord("select * from yq_erp_order where order_id = ?",orderId);
	}
	
	@WebControl(name="getOrderPrice",type=Types.TEXT)
	public JSONObject getOrderPrice(){
		String orderId = param.getString("orderId");
		try {
			String str = this.getQuery().queryForString("select total_price from yq_erp_order where order_id = ?",orderId);
			return getJsonResult(str);
		} catch (SQLException e) {
			return getJsonResult("0");
		}

	}
	
	@WebControl(name="applyInfo",type=Types.RECORD)
	public JSONObject applyInfo(){
		String orderId = param.getString("orderId");
		if(StringUtils.isBlank(orderId)) {
			return getJsonResult(new JSONObject());
		}
		EasySQL sql = new EasySQL();
		sql.append("select t1.*,t3.`name` SUPPLIER_NAME,t3.supplier_no,concat(t3.LINK_MAN,t3.LINK_PHONE) supplier_link,t4.cust_name,t2.contract_no from yq_erp_order t1");
		sql.append("LEFT JOIN yq_project_contract t2 on t1.contract_id=t2.CONTRACT_ID ");
		sql.append("LEFT JOIN yq_erp_supplier t3 on t3.supplier_id=t1.supplier_id ");
		sql.append("left join yq_crm_customer t4 on t4.cust_id = t1.cust_id");
		sql.append(orderId,"where t1.order_id = ?");
		return queryForRecord(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="followList",type=Types.TEMPLATE)
	public JSONObject followList(){
		EasySQL sql=getEasySQL("select * from yq_erp_order_follow where 1=1");
		sql.append(param.getString("orderId"),"and order_id = ?");
		sql.append("order by add_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="followPage",type=Types.PAGE)
	public JSONObject followPage(){
		EasySQL sql=getEasySQL("select t1.*,t2.CONTRACT_NAME,t2.ORDER_NO,t2.CUST_ID from yq_erp_order_follow t1,yq_erp_order t2 where 1=1");
		sql.append("and t1.order_id = t2.order_id");
		sql.append(param.getString("followState"),"and t1.FOLLOW_STATE = ?");
		sql.append(param.getString("orderId"),"and t1.ORDER_ID = ?");
		sql.appendLike(param.getString("orderNo"),"and t2.ORDER_NO like ?");
		sql.append("order by t1.add_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select t1.*,t3.`name` supplier_name from yq_erp_order t1");
		sql.append("LEFT JOIN yq_project_contract t2 on t1.contract_id=t2.contract_id LEFT JOIN yq_erp_supplier t3 on t3.supplier_id=t1.supplier_id");
		sql.append("where 1=1");
		if(!hasRole("ERP_ORDER_BUYER")&&!isSuperUser()) {
			sql.append(getUserId(),"and t1.create_user_id = ?");
		}
		sql.append(param.getString("orderOrg"),"and t1.ORDER_ORG = ?");
		sql.append(param.getString("supplierId"),"and t1.SUPPLIER_ID = ?");
		sql.appendLike(param.getString("orderNo"),"and t1.ORDER_NO like ?");
		sql.appendLike(param.getString("contractNo"),"and t1.CONTRACT_NO like ?");
		sql.appendLike(param.getString("contractName"),"and t1.CONTRACT_NAME like ?");
		sql.append(param.getString("orderType"),"and t1.ORDER_TYPE = ?");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="orderList",type=Types.TEMPLATE)
	public JSONObject orderList(){
		EasySQL sql=getEasySQL("select 'true' LAY_CHECKED,t1.* from yq_erp_order t1 where 1=1");
		sql.append(param.getString("supplierId"),"and t1.SUPPLIER_ID = ?");
		sql.appendLike(param.getString("orderNo"),"and t1.ORDER_NO like ?");
		sql.append(param.getString("orderType"),"and t1.ORDER_TYPE = ?");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		sql.append(param.getString("orderId"),"and t1.order_id = ?");
		String orderIds = param.getString("orderIds");
		if(StringUtils.notBlank(orderIds)) {
			sql.appendIn(orderIds.split(","),"and t1.order_id");
		}
		sql.append("order by t1.create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="orderPayPlanList",type=Types.TEMPLATE)
	public JSONObject orderPayPlanList(){
		EasySQL sql=getEasySQL("select 'true' LAY_CHECKED,t1.*,t2.PAYMENT_ID,t2.plan_id,t2.plan_pay_amount APPLY_AMOUNT,t2.pay_reason APPLY_REMARK,t2.goods_desc from yq_erp_order t1,yq_erp_order_pay_plan t2 where 1=1");
		sql.append("and t1.order_id = t2.order_id");
		sql.append(param.getString("supplierId"),"and t1.SUPPLIER_ID = ?");
		String planIds = param.getString("planIds");
		if(StringUtils.notBlank(planIds)) {
			sql.appendIn(planIds.split(","),"and t2.plan_id");
		}
		sql.append("order by t1.create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="goodsList",type=Types.TEMPLATE)
	public JSONObject goodsList(){
		EasySQL sql=getEasySQL("select * from yq_erp_goods where 1=1");
		sql.append(param.getString("orderId"),"and order_id = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="custList",type=Types.LIST)
	public JSONObject custList(){
		EasySQL sql=getEasySQL("select t1.*,t2.CONTRACT_NAME,t3.`name` supplier_name from yq_erp_order t1,yq_project_contract t2,yq_erp_supplier t3 where t1.supplier_id = t3.supplier_id and t1.contract_id = t2.contract_id");
		sql.append(param.getString("custId"),"and t1.cust_id = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="payTypeDict",type=Types.DICT)
	public JSONObject payTypeDict(){
		EasySQL sql=getEasySQL("select type_id,pay_period from yq_erp_order_pay_type where 1=1");
		sql.append(param.getString("orderId"),"and order_id = ?",false);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="invoiceList",type=Types.LIST)
	public JSONObject invoiceList(){
		EasySQL sql=getEasySQL("select * from yq_erp_order_invoice where 1=1");
		sql.append(param.getString("orderId"),"and order_id = ?",false);
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
		
	}
	
	@WebControl(name="payTypeList",type=Types.LIST)
	public JSONObject payTypeList(){
		EasySQL sql=getEasySQL("select * from yq_erp_order_pay_type where 1=1");
		sql.append(param.getString("orderId"),"and order_id = ?",false);
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
		
	}
	
	@WebControl(name="payTypeMgr",type=Types.PAGE)
	public JSONObject payTypeMgr(){
		EasySQL sql=getEasySQL("select t1.*,t2.order_title,t2.order_no,t2.cust_id from yq_erp_order_pay_type t1,yq_erp_order t2 where 1=1 and t1.order_id = t2.order_id");
		sql.appendLike(param.getString("contractName"),"and t2.contract_name like ?");
		sql.appendLike(param.getString("orderNo"),"and t2.order_no like ?");
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
		
	}
	
	@WebControl(name="payTypeInfo",type=Types.RECORD)
	public JSONObject payTypeInfo(){
		EasySQL sql=getEasySQL("select * from yq_erp_order_pay_type where 1=1");
		sql.append(param.getString("typeId"),"and type_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
		
	}
	
	@WebControl(name="payPlanInfo",type=Types.RECORD)
	public JSONObject payPlanInfo(){
		EasySQL sql=getEasySQL("select * from yq_erp_order_pay_plan where 1=1");
		sql.append(param.getString("planId"),"and plan_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="payPlanList",type=Types.PAGE)
	public JSONObject payPlanList(){
		EasySQL sql=getEasySQL("select t1.*,t5.contract_no,t2.order_org,t2.order_no,t2.total_price,t2.has_pay_amount,t2.supplier_id,t2.cust_id,t2.contract_id,t3.pay_period,t4.name supplier_name from yq_erp_order_pay_plan t1,yq_erp_order t2,yq_erp_order_pay_type t3");
		sql.append(",yq_erp_supplier t4");
		sql.append(",yq_project_contract t5");
		sql.append("where 1=1 and t1.order_id = t2.order_id and t3.order_id = t1.order_id and t3.type_id = t1.type_id");
		sql.append("and t4.supplier_id = t2.supplier_id");
		sql.append("and t5.contract_id = t2.contract_id");
		sql.append(param.getString("monthId"),"and t1.plan_pay_month = ?");
		sql.appendLike(param.getString("supplierName"),"and t4.name like ?");
		sql.appendLike(param.getString("contractName"),"and t2.contract_name like ?");
		sql.appendLike(param.getString("orderNo"),"and t2.order_no like ?");
		String payState = param.getString("payState");
		if("0".equals(payState)) {
			sql.append("and t1.payment_id <>''");
		}else if("1".equals(payState)) {
			sql.append("and t1.payment_id = ''");
		}
		String planIds = param.getString("planIds");
		if(StringUtils.notBlank(planIds)) {
			sql.appendIn(planIds.split(","),"and t1.plan_id");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="invoiceInfo",type=Types.RECORD)
	public JSONObject invoiceInfo(){
		EasySQL sql=getEasySQL("select * from yq_erp_order_invoice where 1=1");
		sql.append(param.getString("invoiceId"),"and invoice_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
		
	}
	
	
}
