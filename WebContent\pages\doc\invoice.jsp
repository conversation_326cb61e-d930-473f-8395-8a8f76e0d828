<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>文档库管理</title>
	<link href="http://invoice.heycore.com/interface/code-formatter.css" rel="noreferrer" rel="stylesheet"/>
	<style>
		body{background-color: #fff!important;}
		li {
		    list-style: none;
		    border: 1px solid #eee;
		    height: 34px;
		    line-height: 32px;
		    padding: 2px 10px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<div class="row mt-20"><center><h3>电子普票、电子专票在线解析识别（非OCR识别，不能识别扫描图像）</h3></center></div>
		<div class="row mt-20">
            <div class="col-md-3"></div>
             <div class="col-md-6">
				 <p class="message"></p>
                 <form role="form" id="form1" action="http://invoice.heycore.com/invoice/extrat" enctype="multipart/form-data">
                    <div class="form-group">
                        <label>电子发票PDF文件</label>
                        <input type="file" name="file" class="form-control" onchange="$('#form1').submit();" accept="application/pdf"/>
                    </div>
                </form>
             </div>
             <div class="col-md-3"></div>
        </div>
		<div id="detail">
    	</div>
        <div class="row hidden">
            <div class="col-md-2"></div>
            <div class="col-md-10">
                <textarea id="rawJson" class="form-control">这里将显示发票数据</textarea>
            </div>
            <div class="col-md-2"></div>
            <div class="col-md-10">
                <div class="code-canvas"></div>
            </div>
            <div class="col-md-2"></div>
        </div>
        
		<script id="inv-tpl"  type="text/x-jsrender">
		<div class="row">
			<center><h3>{{:#data.title}}</h3></center>
		</div>
		<div class="row" >
            <div class="col-md-1"></div>
			<div class="col-md-3">机器编号：{{: #data.machineNumber}}</div>
			<div class="col-md-3"></div>
			<div class="col-md-3">
				<ul class="unstyled">
	                <li>发票代码：{{:#data.code}}</li>
	                <li>发票号码：{{:#data.number}}</li>
	                <li>开票日期：{{:#data.date}}</li>
	                <li>校验码：{{:#data.checksum}}</li>
	           </ul>
			</div>
            <div class="col-md-1"></div>
		</div>
		
		<div class="row">
            <div class="col-md-1"></div>
			<div class="col-md-5">
				<p class="mb-10">购买方</p>
				<ul class="unstyled">
					<li>名称：{{:#data.buyerName}}</li>
					<li>纳税人识别号：{{:#data.buyerCode}}</li>
					<li>地址、电话：{{:#data.buyerAddress}}</li>
					<li>开户行及账号：{{:#data.buyerAccount}}</li>
				</ul>
			</div>
			<div class="col-md-5">
			 <p class="mb-10">密码区</p>
			 <pre class="pd-10">{{:#data.password}}</pre>
			</div>
            <div class="col-md-1"></div>
		</div>
		<div class="row">
            <div class="col-md-1"></div>
            <div class="col-md-10">
			             <table class="table table-bordered">
                            <thead>
                                <tr>
								{{if type == '通行费'}}
									<th>项目名称</th>
                                    <th>车牌号</th>
                                    <th>类型</th>
                                    <th>通行日期起</th>
                                    <th>通行日期止</th>
								{{else}}
									<th>货物或应税劳务、服务名称</th>
                                    <th>规格型号</th>
                                    <th>单位</th>
                                    <th>数 量</th>
                                    <th>单 价</th>
								{{/if}}
                                    <th>金 额</th>
                                    <th>税率</th>
                                    <th>税 额</th>
                                </tr>
                            </thead>
                            <tbody>
							{{for detailList}}
								<tr>
                                    <td style="text-align:left">{{: #data.name}}</td>
                                    <td style="text-align:left">{{: #data.model}}</td>
                                    <td style="text-align:left">{{: #data.unit}}</td>
                                    <td style="text-align:right">{{: #data.count}}</td>
                                    <td style="text-align:right">{{: #data.price}}</td>
                                    <td style="text-align:right">{{: #data.amount}}</td>
                                    <td style="text-align:right">{{: #data.taxRate * 100}}%</td>
                                    <td style="text-align:right">{{: #data.taxAmount}}</td>
                                </tr>
							{{/for}}
								<tr>
									<td colspan=8>&nbsp;</td>
								</tr>
                                <tr>
                                	<td>合计</td>
                                	<td colspan="4">&nbsp;</td>
                                	<td style="text-align:right">￥{{:#data.totalAmount}}</td>
                                	<td>&nbsp;</td>
                                	<td style="text-align:right">￥{{:#data.taxAmount}}</td>
                                </tr>
                                <tr>
                                	<td>税价合计（大写）</td>
									<td colspan="4">ⓧ{{:#data.totalAmountString}} </td>
                                	<td colspan="3">（小写）￥{{:#data.totalAmount}}</td>
                                </tr>
                            </tbody>
                        </table>
            </div>
            <div class="col-md-1"></div>
		</div>
		<div class="row">
            <div class="col-md-1"></div>
			<div class="col-md-5">
				<p>销售方</p>
				<ul class="unstyled">
					<li>名称：{{:#data.sellerName}}</li>
					<li>纳税人识别号：{{:#data.sellerCode}}</li>
					<li>地址、电话：{{:#data.sellerAddress}}</li>
					<li>开户行及账号：{{:#data.sellerAccount}}</li>
				</ul>
			</div>
			<div class="col-md-5">
				<p>备注</p>
			</div>
            <div class="col-md-1"></div>
		</div>
		<div class="row mt-10">
            <div class="col-md-1"></div>
            <div class="col-md-3">收款人：{{:#data.payee}}</div>
            <div class="col-md-3">复核：{{:#data.reviewer}}</div>
            <div class="col-md-4">开票人：{{:#data.drawer}}</div>
            <div class="col-md-1"></div>
        </div>
		<br>
		<br>
		<br>
		<br>
</script>
        
</EasyTag:override>
<EasyTag:override name="script">
    <script src="http://invoice.heycore.com/interface/code-formatter.js" rel="noreferrer"></script>
    <script src="http://invoice.heycore.com/interface/json2.js" rel="noreferrer"></script>
	<script  type="text/javascript">
	 var message=$('.message').text();
 	 $("#form1").submit(function () {
		if($("input[name=file]").val() == ""&&$("input[name=url]").val() == "") {
			alert("请选择文件！")
		} else {
	    	$('#rawJson').val('loading...');
			var fd = new FormData(document.getElementById("form1"));
			$.ajax({
 				url: "/yq-work/invoice/extrat",
 			  	type: "POST",
 			  	data: fd,
 			  	dataType: "json",
 			  	processData: false,  // 不处理数据
 			  	contentType: false,   // 不设置内容类型
 			 	success: function (data) {
 			 		$("#detail").empty();
 			 		var tpl = $.templates("#inv-tpl");
					if(data.password){
						data.password=data.password.replace(new RegExp('<','g'),'&lt;').replace(new RegExp('>','g'),'&gt;');
					}
 			 		$('#detail').append(tpl.render(data));
 			 		$('#rawJson').val(JSON.stringify(data));
 			 		process($('#rawJson').val(),$('.code-canvas'));
             	},error: function(xhr, status, response){
             	   $('#rawJson').val(xhr.responseText);
             	   process($('#rawJson').val(),$('.code-canvas'));
             	}
		});
	}
	return false;
});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>