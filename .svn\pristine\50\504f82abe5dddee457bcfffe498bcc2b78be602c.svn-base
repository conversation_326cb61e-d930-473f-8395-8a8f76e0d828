<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 70px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowName}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的采购到货确认单"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tbody class="orderInfo" data-mars-prefix="order.">
						  		<tr>
						  			<td class="required">采购订单号</td>
						  			<td>
						  				<input type="hidden" name="orderId" value="${param.orderId}"/>
						  				<input type="hidden" name="apply.fkId" id="orderId" value="${param.orderId}"/>
						  				<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" name="order.ORDER_NO"/>
						  			</td>
						  			<td>销售合同号</td>
					  				<td>
					  					<input type="text" readonly="readonly" class="form-control input-sm" name="order.CONTRACT_NAME"/>
					  				</td>
						  		</tr>
					  			<tr>
					  				<td class="required">供应商</td>
					  				<td>
					  					<input type="text" readonly="readonly" class="form-control input-sm" name="order.SUPPLIER_NAME"/>
					  				</td>
					  				<td class="required">供方联系信息</td>
					  				<td>
					  					<input type="text" readonly="readonly" class="form-control input-sm" name="order.SUPPLIER_LINK"/>
					  				</td>
					  			</tr>
					  			<tr>
					  				<td class="required">收货方名称</td>
					  				<td>
					  					<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" name="order.ORDER_ORG"/>
					  				</td>
					  				<td class="required">客户名称</td>
					  				<td>
					  					<input type="text" data-rules="required" class="form-control input-sm" name="order.CUST_NAME"/>
					  				</td>
					  			</tr>
					  		</tbody>
					  		<tr>
				  				<td class="required">签收人</td>
				  				<td>
				  					<input type="hidden" name="apply.data1">
				  					<input type="text" placeholder="点击此选择"  onclick="singleUser(this)" data-ref-dept="#deptName" class="form-control input-sm" name="apply.data2"/>
				  				</td>
				  				<td class="required">签收部门</td>
				  				<td>
				  					<input type="text" readonly="readonly" id="deptName" class="form-control input-sm" name="apply.data3"/>
				  				</td>
				  			</tr>
					  		<tr  class="edit-remove">
				  				<td class="required">签收日期</td>
				  				<td>
				  					<input type="text" data-edit-node="signer" class="form-control input-sm" data-laydate="{type:'date'}" name="apply.data4"/>
				  				</td>
				  				<td class="required">签收人地点</td>
				  				<td>
				  					<input type="text" data-edit-node="signer" class="form-control input-sm" name="apply.data5"/>
				  				</td>
				  			</tr>
					  		<tr>
					  			<td class="required">说明</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12">
		  				<div class="layui-col-md12 mt-10">
					     <div class="layui-table-tool">
							<div class="pull-left">到货物料清单</div>
							<div class="pull-right unedit-remove">
								<button class="btn btn-sm btn-default" onclick="addTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增行</button>
								<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
								<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
								<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
							</div>
					    </div>
					  	<div class="table-responsive" style="min-height: 100px;">
								 <table class="layui-table approve-table" style="margin-top: 0px;">
								   <thead>
								    <tr>
								      <th>序号</th>
								      <th>物料编码</th>
								      <th>货物名称</th>
								      <th>规格型号</th>
								      <th>计量单位</th>
								      <th>数量</th>
								      <th>备注</th>
								    </tr> 
								  </thead>
								  <tbody data-mars="FlowCommon.items" data-template="flow-items"></tbody>
								  <tbody>
								  	<c:forEach var="item" begin="1000" end="1020">
									    <tr id="item_${item}" data-hide-tr="1" style="display:none;">
									      <td>
									      	<input type="hidden" name="order_index_${item}"/>
									      	<input type="hidden" value="${item}" name="itemIndex"/>
									        <label class="checkbox checkbox-inline checkbox-success">
										      	 <input tabindex="-1" type="checkbox" name="ids" value="${item}"><span></span>
									        </label>
									      </td>
									      <td>
									      	    <input name="data1_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									     	   <input name="data2_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									     	   <input name="data3_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									     	   <input name="data4_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									      	   <input type="number" class="form-control input-sm"  data-rules="required" name="data5_${item}"/>
									      </td>
									      <td>
									      	  <textarea style="height: 40px;resize: none;" maxlength="255" class="form-control input-sm" name="data6_${item}"></textarea>
									      </td>
									    </tr>
								  	</c:forEach>
								  </tbody>	
								</table>
							</div>
					   </div>
				 
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
			
	    <script id="flow-items" type="text/x-jsrender">
  		 {{for data}}
			<tr id="item_{{:ITEM_ID}}">
			  <td>
				<input type="hidden" name="order_index_{{:ITEM_ID}}" value="{{:ITEM_INDEX}}"/>
				<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
				<span class="edit-remove">{{:ORDER_INDEX}}</span>
				<label class="checkbox checkbox-inline checkbox-success">
					 <input tabindex="-1" type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
				</label>
			  </td>
			  <td>
				  <input name="data1_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA1}}">
			  </td>
			  <td>
				  <input name="data2_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA2}}">
			  </td>
			  <td>
				  <input name="data3_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA3}}">
			  </td>
			  <td>
				  <input name="data4_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA4}}">
			  </td>
			  <td>
					<input type="number" class="form-control input-sm" data-rules="required" name="data5_{{:ITEM_ID}}" value="{{:DATA5}}"/>
			  </td>
			  <td>
					<textarea style="height: 40px;resize: none;" maxlength="255" class="form-control input-sm" name="data6_{{:ITEM_ID}}">{{:DATA6}}</textarea>
			  </td>
			</tr>
 	     {{/for}}
   	  </script>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideUserTitle:true,title:'{{:data5}}{{:data4}}的采购到货确认单',success:function(data){
				var param = FlowCore.params;
				var orderId = '';
				if(param.handle=='add'){
					orderId = $('#orderId').val();
					addTr();
				}else{
					orderId = FlowCore.applyInfo.fkId;
				}
				if(orderId==''){
					layer.alert('请从采购订单入口申请',function(){
						popup.closeTab();
					});
				}else{
					$('#orderId').prev().val(orderId);
					$('.orderInfo').attr('data-mars','OrderDao.applyInfo');
					$('#flowForm').render();
				}
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			$("[data-hide-tr]").remove();
			var index  = 1;
			$("[name^='order_index_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			Flow.initData();
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		
	 Flow.initData = function(){
		var selectUserName = $("[name='apply.data2']").val();
		var selectUserId = $("[name='apply.data1']").val();
		var params = {selectUserId:selectUserId,selectUserName:selectUserName};
		FlowCore.approveData = params;
	 }
	 
	  function addTr(){
			var obj  = $("[data-hide-tr]").first();
			if(obj.length==0){
				layer.msg('不能再新增啦',{icon:7,shade: 0.1});
				return;
			}
			obj.removeAttr("data-hide-tr");
			obj.show();
		}

		function delTr(){
			layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
				layer.close(index);
				$("input[name='ids']:checked").each(function(){
					var id = this.value;
					if(id.length>20){
						ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=delItems",{itemId:id},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg);
								$('#item_'+id).remove();
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});					
					}else{
						$('#item_'+id).remove();
					}
					
				});
			});
		}
		
		function upTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var prevTR  = $('#item_'+id).prev();
				if (prevTR.length > 0) {
					prevTR.insertAfter($('#item_'+id));
				}
			}
		}
		
		function downTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var nextTR  = $('#item_'+id).next();
				if (nextTR.length > 0) {
					nextTR.insertBefore($('#item_'+id));
				}
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>