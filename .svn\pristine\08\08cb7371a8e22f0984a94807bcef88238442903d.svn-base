<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="businessEditForm" class="form-horizontal" data-mars="CustDao.busniessInfo" autocomplete="off" data-mars-prefix="business.">
 		   		<input type="hidden" value="${param.businessId}" name="businessId"/>
 		   		<input type="hidden" value="${param.businessId}" name="business.BUSINESS_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td class="required">客户名称</td>
		                    <td>
		                    	<input type="hidden" name="business.CUST_ID" value="${param.custId}"/>
								<input data-rules="required" value="${param.custName}" type="text" readonly="readonly" id="_custId" placeholder="请点击选择客户"  name="business.CUSTOMER_NAME" class="form-control input-sm"/>
		                    </td>
			            </tr>
			            <tr>
		                    <td class="required">销售经理</td>
		                    <td>
		                    	 <select data-rules="required" data-mars="CommonDao.userDict" name="business.SALES_BY"  class="form-control input-sm">
		                    	 	<option value="">请选择</option>
		                    	 </select>
		                    </td>
			            </tr>
			            <tr>
		                    <td width="100px" class="required">商机简称</td>
		                    <td>
		                   		 <input data-rules="required" data-rules="required" type="text" name="business.BUSINESS_NAME" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
		                    <td>商机内容</td>
		                    <td>
		                    	 <textarea style="height: 100px;" name="business.REMARK" class="form-control input-sm"></textarea>
		                    </td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="CustBusiness.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		layui.config({
			  base: '${ctxPath}/static/js/'
		}).use('tableSelect');
	
		jQuery.namespace("CustBusiness");
	    
		CustBusiness.businessId='${param.businessId}';
		
		var businessId='${param.businessId}';
		
		$(function(){
			$("#businessEditForm").render({success:function(){
				 requreLib.setplugs('select2',function(){
					$("#businessEditForm select").select2({theme: "bootstrap",placeholder:'请选择'});
					$(".select2-container").css("width","100%");
			 	 });
			}}); 
			initSelectCust();
		});
		
		CustBusiness.ajaxSubmitForm = function(){
			if(form.validate("#businessEditForm")){
				if(CustBusiness.businessId){
					CustBusiness.updateData(); 
				}else{
					CustBusiness.insertData(); 
				}
			};
		}
		CustBusiness.insertData = function(flag) {
			var data = form.getJSONObject("#businessEditForm");
			ajax.remoteCall("${ctxPath}/servlet/cust?action=addBusiness",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('businessEditForm');
						reloadBusinessList();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		CustBusiness.updateData = function(flag) {
			var data = form.getJSONObject("#businessEditForm");
			ajax.remoteCall("${ctxPath}/servlet/cust?action=updateBusiness",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('businessEditForm');
						reloadBusinessList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function initSelectCust(){
			layui.use(['form','tableSelect'], function(){
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#_custId',
					searchKey: 'custName',
					checkedKey: 'CUST_ID',
					page:true,
					searchPlaceholder: '请输入客户名称',
					table: {
						mars: 'CustDao.custList',
						cols: [[
						        { type: 'radio' },
						        { field: 'CUST_NO',title: '编号'},
						        { field: 'CUST_NAME', title: '名称', width: 320 }
						        ]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.CUST_NAME)
							ids.push(item.CUST_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
					},
					clear:function(elem){
						elem.prev().val("");
					}
				});
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>