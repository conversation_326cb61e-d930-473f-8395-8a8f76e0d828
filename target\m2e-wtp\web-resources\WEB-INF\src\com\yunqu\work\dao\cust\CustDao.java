 package com.yunqu.work.dao.cust;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;

@WebObject(name="CustDao")
public class CustDao extends AppDaoContext{

	@WebControl(name = "genCustId",type = Types.TEXT)
	public JSONObject genCustId() {
		String month = EasyCalendar.newInstance().getFullMonth();
		try {
			String result = this.getQuery().queryForString("select cust_no from yq_crm_customer where cust_no like 'YQ"+month+"%' ORDER BY cust_no desc");
			if(StringUtils.isBlank(result)) {
				return getJsonResult("YQ"+month+"0001");
			}else {
				result ="YQ"+(Integer.valueOf(result.substring(2))+1);
				return getJsonResult(result);
			}
		} catch (SQLException e) {
			this.error(null, e);
			return getJsonResult("YQ"+month+"0001");
		}
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String custId = param.getString("custId");
		return queryForRecord("select * from YQ_CRM_CUSTOMER where cust_id = ?",custId);
	}
	
	@WebControl(name="selectCustList",type=Types.LIST)
	public JSONObject selectCustList(){
		EasySQL sql=getEasySQL("select t1.* from YQ_CRM_CUSTOMER t1");
		sql.append("where 1=1");
		sql.appendLike(param.getString("custName"),"and t1.CUST_NAME like ?");
		sql.append(param.getString("custState"),"and t1.CUST_STATE = ?");
		sql.append(param.getString("custSource"),"and t1.CUST_SOURCE = ?");
		sql.append(param.getString("industry"),"and t1.INDUSTRY = ?");
		sql.append(param.getString("custLevel"),"and t1.CUST_LEVEL = ?");
		sql.append("order by t1.last_follow_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="custList",type=Types.LIST)
	public JSONObject custList(){
		EasySQL sql=getEasySQL("select t1.* from YQ_CRM_CUSTOMER t1");
		sql.append(getUserId(),"left join (select b1.* from yq_crm_cust_team b1 where b1.user_id = ?) t2 on t2.cust_id = t1.cust_id");
		sql.append("where 1=1");
		sql.appendLike(param.getString("custName"),"and t1.CUST_NAME like ?");
		sql.append(param.getString("custState"),"and t1.CUST_STATE = ?");
		sql.append(param.getString("custSource"),"and t1.CUST_SOURCE = ?");
		sql.append(param.getString("industry"),"and t1.INDUSTRY = ?");
		sql.append(param.getString("custLevel"),"and t1.CUST_LEVEL = ?");
//		if(!isSuperUser()&&!hasRole("CUST_MGR")) { 
//			sql.append("and (");
//			sql.append(getUserId(),"(t1.creator = ?)");
//			sql.append("or");
//			sql.append(getUserId(),"(t1.owner_id = ?)");
//			sql.append(")");
//		}
		sql.append("group by t1.cust_id");
		sql.append("order by t1.last_follow_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="custContactsList",type=Types.LIST)
	public JSONObject custContactsList(){
		EasySQL sql=getEasySQL("select * from YQ_CRM_CONTACTS where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="followList",type=Types.TEMPLATE)
	public JSONObject followList(){
		EasyQuery query = this.getQuery();
		query.setMaxRow(15);
		EasySQL sql=getEasySQL("select * from YQ_CRM_FOLLOW where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append("order by create_time desc");
		setQuery(query);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="userFollowList",type=Types.TEMPLATE)
	public JSONObject userFollowList(){
		EasySQL sql=getEasySQL("select t1.*,t2.cust_name,t3.business_name from YQ_CRM_FOLLOW t1 left join yq_crm_customer t2 on t1.cust_id = t2.cust_id ");
		sql.append("left join yq_crm_business t3 on t1.business_id = t3.business_id ");
		sql.append("where 1=1");
		sql.append(param.getString("beginDate"),"and t1.follow_begin >= ?");
		sql.append(param.getString("endDate"),"and t1.follow_end <= ?");
		sql.appendLike(param.getString("userName"),"and t1.create_user_name like ?");
		sql.appendLike(param.getString("customerName"),"and t2.cust_name like ?");
		sql.append(param.getString("contactsId"),"and t1.CONTACTS_ID = ?");
		String custId = param.getString("custId");
		String businessId = param.getString("businessId");
		
		String weekNo = param.getString("weekNo");
		if(StringUtils.isNotBlank(weekNo)) {
			sql.append(weekNo.substring(0,4),"and t1.year = ?");
			sql.append(weekNo.substring(4),"and t1.week_no = ?");
		}
		if(StringUtils.isNotBlank(custId)||StringUtils.isNotBlank(businessId)) {
			sql.append(businessId,"and t1.BUSINESS_ID = ?");
			sql.append(custId,"and t1.cust_id = ?");
		}else {
			if(!isSuperUser()) {
				if(getUserPrincipal().isRole("PRODUCT_MGR")) {
//					sql.append(getDeptId(),"and t1.dept_id = ?");
				}else {
					sql.append(getUserId(),"and t1.create_user_id = ?");
				}
			}
		}
		sql.append("order by t1.create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="custFollowList",type=Types.LIST)
	public JSONObject custFollowList(){
		EasySQL sql=getEasySQL("select t1.*,t2.cust_name,t3.business_name from YQ_CRM_FOLLOW t1 left join yq_crm_customer t2 on t1.cust_id = t2.cust_id ");
		sql.append("left join yq_crm_business t3 on t1.business_id = t3.business_id ");
		sql.append("where 1=1");
		sql.append(param.getString("beginDate"),"and t1.follow_begin >= ?");
		sql.append(param.getString("endDate"),"and t1.follow_end <= ?");
		sql.append(param.getString("userId"),"and t1.create_user_id = ?");
		sql.appendLike(param.getString("userName"),"and t1.create_user_name like ?");
		sql.appendLike(param.getString("customerName"),"and t2.cust_name like ?");
		sql.append(param.getString("deptId"),"and t1.dept_id = ?");
		sql.append(param.getString("contactsId"),"and t1.contacts_id = ?");
		String custId = param.getString("custId");
		String businessId = param.getString("businessId");
		
		String weekNo = param.getString("weekNo");
		if(StringUtils.isNotBlank(weekNo)) {
			sql.append(weekNo.substring(0,4),"and t1.year = ?");
			sql.append(weekNo.substring(4),"and t1.week_no = ?");
		}
		Object obj = getMethodParam(0);
		if(obj!=null&&StringUtils.notBlank(obj.toString())) {
			sql.append(obj.toString(),"and t1.create_user_id = ?");
		}else {
			if(StringUtils.isNotBlank(custId)||StringUtils.isNotBlank(businessId)) {
				sql.append(param.getString("businessId"),"and t1.BUSINESS_ID = ?");
				sql.append(param.getString("custId"),"and t1.cust_id = ?");
			}else {
				if(!isSuperUser()) {
					if(getUserPrincipal().isRole("PRODUCT_MGR")) {
//						sql.append(getDeptId(),"and t1.dept_id = ?");
					}else {
						sql.append(getUserId(),"and t1.create_user_id = ?");
					}
				}
			}
		}
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="followInfo",type=Types.RECORD)
	public JSONObject followInfo(){
		String followId = param.getString("followId");
		return queryForRecord("select * from YQ_CRM_FOLLOW where follow_id = ?",followId);
	}
	
	@WebControl(name="followAllList",type=Types.LIST)
	public JSONObject followAllList(){
		EasySQL sql=getEasySQL("select * from YQ_CRM_FOLLOW where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="teamList",type=Types.LIST)
	public JSONObject teamList(){
		EasySQL sql=getEasySQL("select t2.USERNAME,t1.* from yq_crm_cust_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t1.USER_ID=t2.USER_ID where 1=1");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="busniessInfo",type=Types.RECORD)
	public JSONObject busniessInfo(){
		String businessId = param.getString("businessId");
		return queryForRecord("select * from yq_crm_business where business_id = ?",businessId);
	}
	
	@WebControl(name="custBusniessDict",type=Types.DICT)
	public JSONObject custBusniessDict(){
		String custId = param.getString("custId");
		if(StringUtils.isBlank(custId)) {
			custId = param.getString("contract.CUST_ID");
		}
		return getDictByQuery("select business_id,business_name from yq_crm_business where cust_id = ?",custId);
	}
	
	@WebControl(name="businessStageNum",type=Types.TEMPLATE)
	public JSONObject businessStagePage(){
		EasySQL sql=getEasySQL("select t1.business_stage,count(1) count from yq_crm_business t1 INNER JOIN yq_crm_customer t2 on t1.cust_id=t2.cust_id");
		sql.append(getUserId(),"left join (select b1.* from yq_crm_cust_team b1 where b1.sj_auth = 1 and b1.user_id = ?) t3 on t2.cust_id = t1.cust_id");
		sql.append("where 1=1");
		this.setSjCondition(sql);
		sql.append("group by t1.business_stage");
		sql.append("order by count desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="businessResultNum",type=Types.TEMPLATE)
	public JSONObject businessResultNum(){
		EasySQL sql=getEasySQL("select t1.business_result,count(1) count from yq_crm_business t1 INNER JOIN yq_crm_customer t2 on t1.cust_id=t2.cust_id");
		sql.append(getUserId(),"left join (select b1.* from yq_crm_cust_team b1 where b1.sj_auth = 1 and b1.user_id = ?) t3 on t2.cust_id = t1.cust_id");
		sql.append("where 1=1");
		this.setSjCondition(sql);
		sql.append("group by t1.business_result");
		sql.append("order by count desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="businessSalesNum",type=Types.TEMPLATE)
	public JSONObject businessSalesNum(){
		EasySQL sql=getEasySQL("select t1.sales_by,t1.sales_by_name,count(1) count from yq_crm_business t1 INNER JOIN yq_crm_customer t2 on t1.cust_id=t2.cust_id");
		sql.append(getUserId(),"left join (select b1.* from yq_crm_cust_team b1 where b1.sj_auth = 1 and b1.user_id = ?) t3 on t2.cust_id = t1.cust_id");
		sql.append("where 1=1");
		this.setSjCondition(sql);
		sql.append("group by t1.sales_by");
		sql.append("order by count desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="businessPreSalesNum",type=Types.TEMPLATE)
	public JSONObject businessPreSalesNum(){
		EasySQL sql=getEasySQL("select t1.creator,t1.create_name,count(1) count from yq_crm_business t1 INNER JOIN yq_crm_customer t2 on t1.cust_id=t2.cust_id");
		sql.append(getUserId(),"left join (select b1.* from yq_crm_cust_team b1 where b1.sj_auth = 1 and b1.user_id = ?) t3 on t2.cust_id = t1.cust_id");
		sql.append("where 1=1");
		this.setSjCondition(sql);
		sql.append("group by t1.creator");
		sql.append("order by count desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="businessFollowDay",type=Types.TEMPLATE)
	public JSONObject businessFollowDay(){
		EasySQL sql=getEasySQL("select t1.business_name,datediff(t1.last_follow_date,t1.first_follow_date) day from yq_crm_business t1 INNER JOIN yq_crm_customer t2 on t1.cust_id=t2.cust_id");
		sql.append(getUserId(),"left join (select b1.* from yq_crm_cust_team b1 where b1.sj_auth = 1 and b1.user_id = ?) t3 on t2.cust_id = t1.cust_id");
		sql.append("where 1=1");
		this.setSjCondition(sql);
		sql.append("order by day desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="selectSj",type=Types.LIST)
	public JSONObject selectSj(){
		String custId = param.getString("custId");
		EasySQL sql = new EasySQL();
		sql.append("select t1.* from YQ_CRM_BUSINESS t1 where 1=1");
		sql.append(custId,"and t1.CUST_ID = ?");
		sql.append(param.getString("platformId"),"and t1.PLATFORM_ID = ?");
		sql.appendLike(param.getString("name"),"and t1.BUSINESS_NAME like ?");
		sql.appendLike(param.getString("custName"),"and t1.CUSTOMER_NAME like ?");
		sql.appendLike(param.getString("platformName"),"and t1.PLATFORM_NAME like ?");
		if(StringUtils.notBlank(custId)) {
			sql.append("union");
			sql.append("0","select * from YQ_CRM_BUSINESS  where business_id  = ?");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="businessPage",type=Types.LIST)
	public JSONObject businessPage(){
		EasySQL sql=getEasySQL("select t1.*,t2.CUST_NAME,t3.sj_auth from YQ_CRM_BUSINESS t1 INNER JOIN YQ_CRM_CUSTOMER t2 on t1.cust_id=t2.cust_id");
		sql.append(getUserId(),"left join (select b1.* from yq_crm_cust_team b1 where b1.sj_auth = 1 and b1.user_id = ?) t3 on t2.cust_id = t1.cust_id");
		sql.append("where 1=1");
		this.setSjCondition(sql);
		sql.append("group by t1.business_id");
		sql.append("order by t1.last_follow_time desc,t1.update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	private void setSjCondition(EasySQL sql) {
		sql.appendLike(param.getString("name"),"and t1.business_name like ?");
		sql.append(param.getString("businessStage"),"and t1.business_stage = ?");
		sql.append(param.getString("creator"),"and t1.creator = ?");
		sql.append(param.getString("sales"),"and t1.sales_by = ?");
		sql.append(param.getString("deptId"),"and t1.dept_id = ?");
		sql.append(param.getString("businessResult"),"and t1.business_result = ?");
		sql.appendLike(param.getString("custName"),"and t1.customer_name like ?");
		sql.append(param.getString("followBeginDate"),"and t1.first_follow_date >= ?");
		sql.append(param.getString("followEndDate"),"and t1.first_follow_date <= ?");
		
		sql.append(param.getString("beginDate"),"and left(t1.last_follow_time,10) >= ?");
		sql.append(param.getString("endDate"),"and left(t1.last_follow_time,10) <= ?");
		sql.append(param.getString("custId"),"and t1.cust_id = ?");
		sql.append(param.getString("platformId"),"and t1.platform_id = ?");
		sql.append(param.getString("businessType"),"and t1.business_type = ?");

		int sjDataType = param.getIntValue("sjDataType");
		if(sjDataType==2) {
			
		}else if(sjDataType==3){
			sql.append(getDeptId(),"and t1.dept_id = ?");
		}else {
			sql.append(getUserId(),"and t1.creator = ?");
		}
	}
	
	@WebControl(name="folowByWeek",type=Types.TEMPLATE)
	public JSONObject folowByWeek(){
		EasySQL sql=getEasySQL("select count(1) count,sum(follow_day) follow_day,create_user_id,create_user_name from yq_crm_follow  where 1=1 ");
		if(!isSuperUser()) {
			if(getUserPrincipal().isRole("PRODUCT_MGR")) {
//				sql.append(getDeptId(),"and dept_id = ?");
			}else {
				sql.append(getUserId(),"and create_user_id = ?");
			}
		}
		sql.append(param.getString("userName"),"and create_user_name = ?");
		sql.appendLike(param.getString("customerName"),"and customer_name like ?");
		sql.append(param.getString("beginDate"),"and follow_begin >= ?");
		sql.append(param.getString("endDate"),"and follow_end <= ?");
		
		String weekNo = param.getString("weekNo");
		if(StringUtils.isNotBlank(weekNo)) {
			sql.append(weekNo.substring(0,4),"and year = ?");
			sql.append(weekNo.substring(4),"and week_no = ?");
		}
		sql.append("GROUP BY create_user_id,create_user_name");
		sql.append("order by follow_day desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="folowCustByWeek",type=Types.TEMPLATE)
	public JSONObject folowCustByWeek(){
		EasySQL sql=getEasySQL("select count(1) count,sum(t1.follow_day) follow_day,t1.cust_id,t2.cust_name from yq_crm_follow t1,yq_crm_customer t2  where 1=1 ");
		sql.append("and t1.cust_id = t2.cust_id");
		if(!isSuperUser()) {
			if(getUserPrincipal().isRole("PRODUCT_MGR")) {
//				sql.append(getDeptId(),"and t1.dept_id = ?");
			}else {
				sql.append(getUserId(),"and t1.create_user_id = ?");
			}
		}
		sql.append(param.getString("userName"),"and t1.create_user_name = ?");
		sql.appendLike(param.getString("customerName"),"and t1.customer_name like ?");
		sql.append(param.getString("beginDate"),"and t1.follow_begin >= ?");
		sql.append(param.getString("endDate"),"and t1.follow_end <= ?");
		
		String weekNo = param.getString("weekNo");
		if(StringUtils.isNotBlank(weekNo)) {
			sql.append(weekNo.substring(0,4),"and t1.year = ?");
			sql.append(weekNo.substring(4),"and t1.week_no = ?");
		}
		sql.append("GROUP BY t1.cust_id,t2.cust_name");
		sql.append("order by follow_day desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="folowSjByWeek",type=Types.TEMPLATE)
	public JSONObject folowSjByWeek(){
		EasySQL sql=getEasySQL("select count(1) count,sum(t1.follow_day) follow_day,t1.business_id,t2.business_name from yq_crm_follow t1,yq_crm_business t2  where 1=1 ");
		sql.append("and t1.business_id = t2.business_id");
		if(!isSuperUser()) {
			if(getUserPrincipal().isRole("PRODUCT_MGR")) {
//				sql.append(getDeptId(),"and t1.dept_id = ?");
			}else {
				sql.append(getUserId(),"and t1.create_user_id = ?");
			}
		}
		sql.append(param.getString("userName"),"and t1.create_user_name = ?");
		sql.appendLike(param.getString("customerName"),"and t1.customer_name like ?");
		sql.append(param.getString("beginDate"),"and t1.follow_begin >= ?");
		sql.append(param.getString("endDate"),"and t1.follow_end <= ?");
		
		
		String weekNo = param.getString("weekNo");
		if(StringUtils.isNotBlank(weekNo)) {
			sql.append(weekNo.substring(0,4),"and t1.year = ?");
			sql.append(weekNo.substring(4),"and t1.week_no = ?");
		}
		sql.append("GROUP BY t1.business_id,t2.business_name");
		sql.append("order by follow_day desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="businessList",type=Types.LIST)
	public JSONObject businessList(){
		EasySQL sql=getEasySQL("select t1.*,t2.CUST_NAME from YQ_CRM_BUSINESS t1 INNER JOIN YQ_CRM_CUSTOMER t2 on t1.cust_id=t2.cust_id where 1=1");
		sql.appendLike(param.getString("name"),"and t1.BUSINESS_NAME like ?");
		sql.append(param.getString("businessStage"),"and t1.BUSINESS_STAGE = ?");
		sql.append(param.getString("platformId"),"and t1.PLATFORM_ID = ?");
		sql.appendLike(param.getString("custName"),"and t1.CUSTOMER_NAME like ?");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
}
