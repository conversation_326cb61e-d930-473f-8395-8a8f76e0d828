<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>待办流程</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input type="hidden" name="flowCode" value="${flowCode}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		 	 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程名称</span>
						 	<input class="form-control input-sm" name="flowName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
 				<a class="btn btn-xs btn-info" href="javascript:;"><i class="fa fa-paper-plane-o"></i> 审批</a>
		  </script>
		  	 <script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default" lay-event="refreshData">刷新</button>
 		</script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
					loadFlowNum();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'FlowDao.myOverTimeFlow',
					id:'flowTable',
					page:true,
					limit:20,
					toolbar:'#btnBar',
					rowDoubleEvent:'flowDetail',
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						field:'',
						title:'操作',
						width:80,
						align:'center',
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					},{
						 field:'APPLY_NO',
						 title:'编号',
						 width:220,
						 templet:function(row){
							 var readTime = row['READ_TIME'];
							 var html = '';
							 if(readTime==''){
								 html = '<span class="layui-badge-dot mr-5"></span>';
							 }
							 return html+row['APPLY_NO']+"_"+row['FLOW_NAME'];
						 }
					 },{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:220
					 },{
						 field:'NODE_NAME',
						 title:'当前节点',
						 width:110
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					},{
					    field: 'OVER_TIME',
						title: '超时日期',
						align:'center',
					    width:160
					},{
						field:'',
						title:'超时时间',
						width:90,
						templet:function(row){
							return twoTimeInterval(row.OVER_TIME,'');
						}
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function flowDetail(data){
			var flowCode = data['FLOW_CODE'];
			var readTime = data['READ_TIME'];
			var resultId = data['RESULT_ID'];
			if(readTime==''){
				ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=addReadTime",{resultId:resultId},function(result) {});
			}
			var json  = $.extend({},{businessId:data['APPLY_ID'],flowCode:flowCode,isApprovalFlag:'1','handle':'approval',resultId:resultId});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
			
	    function refreshData(){
		   location.reload();
	    }
		  
		function entrust(dataList){
			
		}
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>