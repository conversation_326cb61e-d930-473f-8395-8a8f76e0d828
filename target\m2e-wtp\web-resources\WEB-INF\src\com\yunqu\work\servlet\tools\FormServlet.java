package com.yunqu.work.servlet.tools;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.WebKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/form")
public class FormServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	public EasyResult actionForAddXf(){
		JSONObject jsonObject = getJSONObject("form");
		EasyRecord record=new EasyRecord("XF_FORM");
		try {
			record.setColumns(jsonObject);
			record.set("DEPT_ID", getDeptId());
			record.set("USER_ID", getUserId());
			record.set("AGENT", getRequest().getHeader("User-Agent"));
			record.set("IP", WebKit.getIP(getRequest()));
			record.set("CREATE_TIME",EasyDate.getCurrentDateString());
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
}
