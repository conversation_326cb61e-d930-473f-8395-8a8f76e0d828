package com.yunqu.work.servlet.crm;


import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.ContractService;

@WebServlet("/servlet/contractPayment/*")
public class ContractPaymentServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public EasyRecord getPaymentModel(String prefix) {
        EasyRecord paymentModel = new EasyRecord("yq_contract_payment", "PAYMENT_ID");
        paymentModel.setColumns(getJSONObject(prefix));
        return paymentModel;
    }

    public EasyResult actionForAdd() {
        EasyRecord paymentModel = getPaymentModel("payment");
        paymentModel.setPrimaryValues(RandomKit.uuid().toUpperCase());
        paymentModel.set("CREATE_TIME", EasyDate.getCurrentDateString());
        paymentModel.set("CREATOR", getUserName());
        paymentModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        paymentModel.set("UPDATE_BY", getUserName());
        String contractId = paymentModel.getString("CONTRACT_ID");
        try {
            this.getQuery().save(paymentModel);
            ContractService.getService().addCount("PAYMENT_COUNT",contractId);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdate() {
        EasyRecord paymentModel = getPaymentModel("payment");
        paymentModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        paymentModel.set("UPDATE_BY", getUserName());
        try {
            this.getQuery().update(paymentModel);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForBatchDel() {
        JSONArray paymentArray = getJSONArray();
        for (int i = 0; i < paymentArray.size(); i++) {
            JSONObject paymentObject = paymentArray.getJSONObject(i);
            try {
                this.getQuery().executeUpdate("delete from yq_contract_payment where PAYMENT_ID = ?", paymentObject.getString("PAYMENT_ID"));
                String contractId = paymentObject.getString("CONTRACT_ID");
                ContractService.getService().subCount("PAYMENT_COUNT",contractId);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }

}
