<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="incomeStageTable"></table>
<script  type="text/html" id="toolbar2">
    <button class="btn btn-info btn-sm" onclick="incomeStageList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-10" onclick="incomeStageList.edit()" type="button">修改</button>
    <button class="btn btn-warning btn-sm ml-10" onclick="incomeStageList.del()" type="button">删除</button>
    <button class="btn btn-default btn-sm ml-10" onclick="reloadIncomeStageList()" type="button">刷新</button>
    <button style="display: none" class="btn btn-info btn-sm" onclick="reloadCompDate()" type="button">刷新实际完成日期</button>
</script>
<script type="text/x-jsrender" id="contractStageBar">
	<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="incomeStageList.detail">查看详情</a>
</script>
<script type="text/javascript">
    var incomeStageList={
        init:function(){
            $("#ContractDetailForm").initTable({
                mars:'IncomeStageDao.incomeStageList',
                id:'incomeStageTable',
                page:false,
                toolbar:'#toolbar2',
                rowEvent:'rowEvent',
                cellMinWidth:60,
                rowDoubleEvent(row){
                    incomeStageList.detail(row);
                },
                totalRow:true,
                autoSort:true,
                cols: [[
                    {
                        type: 'checkbox',
                        align:'left',
                        totalRowText:"合计"
                    },{
                        field: 'STAGE_NAME',
                        title: '阶段名称',
                        align:'left',
                        width:80,
                        style:'color:#1E9FFF;cursor:pointer',
                        event:'incomeStageList.detail',
                    },{
                        field:'INCOME_CONF_FLAG',
                        title:'收入确认A',
                        minWidth:100,
                        width: 100,
                        templet:function(d){
                            return getIncomeStageStatus(d.INCOME_CONF_FLAG,d.PLAN_COMP_DATE,d.ACT_COMP_DATE);
                        }
                    },{
                        field:'PLAN_COMP_DATE',
                        title:'计划完成时间',
                        align:'left',
                        sort:true,
                        width:110
                    },{
                        field:'ACT_COMP_DATE',
                        title:'实际完成日期A',
                        align:'left',
                        sort:true,
                        width:120
                    },{
                        field:'RATIO',
                        title:'占合同总额(%)',
                        width:100,
                        totalRow:true,
                    }, {
                        field:'PAYMENT_ID',
                        title:'对应款项',
                        minWidth:280,
                        templet:function(d){
                            return getText(d.PAYMENT_ID,'paymentName');
                        }
                    },{
                        field:'PLAN_AMOUNT',
                        title:'计划收款金额(含税)',
                        minWidth:130,
                        totalRow:true,
                    },{
                        field:'AMOUNT_NO_TAX',
                        title:'计划收款金额(税后)',
                        minWidth:130,
                        totalRow:true,
                    },
                    // {
                    //     field:'CONTRACT_STAGE_ID',
                    //     title:'对应合同阶段',
                    //     minWidth:280,
                    //     templet:function(d){
                    //         if(d.WRITE_MODE === '2'){
                    //             return "无";
                    //         }
                    //         return getText(d.CONTRACT_STAGE_ID,'contractStage');
                    //     }
                    // },
                    {
                        field:'UNCONFIRM_NO_TAX_A',
                        title:'未确认金额A(税后)',
                        width:130,
                        totalRow:true,
                    },{
                        field:'UNCONFIRM_NO_TAX_B',
                        title:'未确认金额B(税后)',
                        width:130,
                        totalRow:true,
                    },{
                        field:'CREDIT_PRD',
                        title:'信用期(天)',
                    },{
                        field:'YULIU_RATIO',
                        title:'预留比例',
                        width: 80
                    },{
                        field:'REMARK',
                        title:'备注',
                        width:120
                    },{
                        field:'CHECKED_FLAG',
                        title:'已审核',
                        width:65,
                        templet:function(d){
                            if(d.CHECKED_FLAG == '1'){
                                return '√';
                            }else{
                                return '×';
                            }
                        }
                    },{
                        field:'HARDWARE_CONF_FLAG',
                        title:'硬件确认',
                        width:80,
                        templet:function(d){
                            if(d.HARDWARE_CONF_FLAG == '1'){
                                return '√';
                            }else{
                                return '×';
                            }
                        }
                    },{
                        title: '操作',
                        align:'center',
                        width:100,
                        templet:function(row){
                            return renderTpl('contractStageBar',row);
                        }
                    },{
                        field:'UPDATE_BY',
                        title:'操作人',
                        width:90
                    },{
                        field: 'UPDATE_TIME',
                        title: '更新时间',
                        width:120,
                        align:'center',
                    }
                ]],
                done:function(res){
                    if(res.total!=undefined){
                        $("#incomeStageCount").text("("+res.total+")");
                    }

                    //total
                    var divArr = $('#incomeStageDiv .layui-table-total .layui-table-cell');
                    var ratioDiv = $(divArr[5]);
                    var totalAmount = $(divArr[7]).html();
                    var totalRatio = parseFloat(totalAmount) * 100 / contractInfo.AMOUNT;
                    ratioDiv.html(totalRatio.toFixed(2))
                }
            });
        },
        add:function (){
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增收入确认阶段',offset:'auto',area:['860px','500px'],url:ctxPath+'/pages/crm/contract/include/income-stage-edit.jsp',title:'新增收入确认阶段',data:{contractId:contractId,isNew:'1',amount: contractInfo.AMOUNT}});
        },
        edit:function (){
            var checkStatus = table.checkStatus('incomeStageTable');
            if(checkStatus.data.length > 1){
                layer.msg('一次只能更新一个收入确认阶段',{icon : 7, time : 1000});
            }
            else if(checkStatus.data.length == 1){
                var stage = checkStatus.data[0];
                var incomeStageId = checkStatus.data[0]['INCOME_STAGE_ID'];
                // var writeMode = checkStatus.data[0]['WRITE_MODE'];
                if(stage.UNCONFIRM_AMOUNT_A != stage.PLAN_AMOUNT || stage.UNCONFIRM_AMOUNT_B != stage.PLAN_AMOUNT ){
                    layer.confirm("此确认收入阶段已进行收入确认，<br>修改对应款项、计划金额后将重新计算未确认金额和实际完成日期。",{icon:3,offset:'120px'},function(){
                        popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新收入确认阶段',offset:'auto',area:['860px','500px'],url:ctxPath+'/pages/crm/contract/include/income-stage-edit.jsp',title:'编辑收入确认阶段',data:{contractId:contractId,incomeStageId:incomeStageId,isNew:'0',isConfirm:'1',
                                // writeMode:writeMode,
                                amount: contractInfo.AMOUNT}});
                    });
                }else {
                    popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新收入确认阶段',offset:'auto',area:['860px','500px'],url:ctxPath+'/pages/crm/contract/include/income-stage-edit.jsp',title:'编辑收入确认阶段',data:{contractId:contractId,incomeStageId:incomeStageId,isNew:'0',
                            // writeMode:writeMode,
                            amount: contractInfo.AMOUNT}});
                }
            }else{
                layer.msg("请选择!");
            }
        },
        del:function (){
            var checkStatus = table.checkStatus('incomeStageTable');
            if(checkStatus.data.length>0){
                var flag = false;
                for(var i=0;i<checkStatus.data.length;i++){
                    var stage = checkStatus.data[i];
                    if(stage.UNCONFIRM_AMOUNT_A != stage.PLAN_AMOUNT || stage.UNCONFIRM_AMOUNT_B != stage.PLAN_AMOUNT ){
                        flag = true;
                    }
                }
                if(flag == true){
                    layer.confirm('包含已收入确认的确认收入阶段，无法删除，<br>请先删除对应的收入确认信息!',{icon:3,offset:'120px'},function(){
                        layer.closeAll();
                    })
                }else {
                    layer.confirm('确认要删除吗?',{icon:3,offset:'120px'},function(){
                        ajax.remoteCall("${ctxPath}/servlet/incomeStage?action=BatchDel",checkStatus.data,function(result) {
                            if(result.state == 1){
                                layer.msg(result.msg,{icon:1,time:1200},function(){
                                    layer.closeAll();
                                    reloadIncomeStageList();
                                    // reloadStageList();
                                });
                            }else{
                                layer.alert(result.msg,{icon: 5});
                            }
                        });
                    });
                }

            }else{
                layer.msg("请选择!");
            }
        },
        detail:function (data){
            var  incomeStageId = data.INCOME_STAGE_ID;
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'收入确认阶段详情',offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/crm/contract/include/income-stage-detail.jsp',data:{contractId:contractId,incomeStageId:incomeStageId}});
        }
    }


    function reloadIncomeStageList(){
        $("#ContractDetailForm").queryData({id:'incomeStageTable',page:false});
        $("[name='incomeStageName']").render();
        $("#ContractDetailForm").queryData({id:'paymentTable',page:false});
        // $("[name='contractStage']").render();
        reloadContractDetail();
    }


    function reloadCompDate() {
        var data = {nothing: "nothing"};
        ajax.remoteCall("${ctxPath}/servlet/incomeConfirm?action=ReloadAllCompleteDateForIncomeStage", data, function (result) {
            if (result.state == 1) {
                layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                    layer.closeAll();
                });
            } else {
                layer.alert(result.msg, {icon: 5});
            }
        });
    }


</script>
