package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.PathKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.template.Engine;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.model.StaffRowMapper;

public class StaffService extends AppBaseService {
	
	public static Map<String,String> userNames = new HashMap<String, String>();
	public static Map<String,String> nameIds = new HashMap<String, String>();
	public static Map<String,String> userStaffNos = new HashMap<String, String>();
	public static Map<String,String> deptLeader = new HashMap<String, String>();
	
	private static class Holder{
		private static StaffService service=new StaffService();
	}
	public static StaffService getService(){
		return Holder.service;
	}

	public StaffService() {
		try {
			List<JSONObject> list = this.getMainQuery().queryForList("select t1.USER_ID,t1.USERNAME,t2.STAFF_NO from easi_user t1 INNER JOIN yq_staff_info t2 on t1.USER_ID = t2.staff_user_id",new Object[]{},new JSONMapperImpl());
			for(JSONObject row :list) {
				userNames.put(row.getString("USER_ID"), row.getString("USERNAME"));
				userStaffNos.put(row.getString("USER_ID"), row.getString("STAFF_NO"));
				nameIds.put(row.getString("USERNAME"), row.getString("USER_ID"));
			}
			List<JSONObject> depts = this.getMainQuery().queryForList("select t1.DEPT_ID,t1.LEADER from easi_dept t1",new Object[]{},new JSONMapperImpl());
			for(JSONObject row :depts) {
				deptLeader.put(row.getString("DEPT_ID"), row.getString("LEADER"));
			}
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	
	public StaffModel getStaffInfo(String userId) {
		try {
			if(userId.length()<5) {
				userId = getUserId(userId);
			}
			StaffModel staffModel = this.getMainQuery().queryForRow("select t1.ROOT_ID,t1.USER_ID,t1.DEPTS,t1.USERNAME,t1.NIKE_NAME,t1.OPEN_ID,t1.EMAIL,t1.MOBILE,t1.PIC_URL,t2.SUPERIOR,t2.P_SUPERIOR,t2.STAFF_NO,t2.JOB_TITLE,t3.DEPT_ID,t2.JOIN_DATE,t2.OFTEN_WORK_CITY from easi_user t1 LEFT JOIN yq_staff_info t2 on t1.USER_ID = t2.staff_user_id LEFT JOIN easi_dept_user t3 on t3.USER_ID = t1.USER_ID where t1.USER_ID = ?",new Object[]{userId},new StaffRowMapper());
			staffModel.setDeptLeader(getLeader(staffModel.getDeptId()));
			return staffModel;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new StaffModel();
		}
	}
	
	public String getLeader(String deptId) {
		String leader = deptLeader.get(deptId);
		if(leader==null) {
			try {
				leader =  this.getMainQuery().queryForString("select t1.DEPT_ID,t1.LEADER from easi_dept t1 where t1.dept_id = ?",new Object[]{deptId});
			    deptLeader.put(deptId, leader);
			} catch (SQLException e) {
				this.getLogger().error(null,e);
			}
		}
		return leader;
	}
	
	public String getUserId(String userName) {
		String[] userNameArray = userName.split(",");
		StringBuffer strs = new StringBuffer();
		if(userName.indexOf(",")>-1) {
			for(String str:userNameArray) {
				strs.append(queryUserId(str)).append(",");
			}
		}else {
			return queryUserId(userName);
		}
		String result = strs.toString();
		result = result.substring(0,result.length()-1);
		return result;

	}
	
	public String getStaffNo(String userIds) {
		StringBuffer sb = new StringBuffer();
		if(StringUtils.notBlank(userIds)) {
			String[] userIdArray = userIds.split(",");
			for(String userId:userIdArray) {
				String staffNo = userId;
				if(userId.length()>10) {
					staffNo = userStaffNos.get(userId);
					if(staffNo==null) {
						try {
							staffNo =  this.getMainQuery().queryForString("select staff_no from yq_staff_info where staff_user_id = ?", userId);
							userStaffNos.put(userId,staffNo);
						} catch (SQLException e) {
							getLogger().error(null,e);
						}
					}
				}
				sb.append(staffNo+",");
			}
			return sb.deleteCharAt(sb.length() - 1).toString();
		}
		return "";
		
	}
	
	public String getUserName(String userId) {
		String[] userIdArray = userId.split(",");
		StringBuffer strs = new StringBuffer();
		if(userId.indexOf(",")>-1) {
			for(String str:userIdArray) {
				strs.append(queryUserName(str)).append(",");
			}
		}else {
			return queryUserName(userId);
		}
		String result = strs.toString();
		result = result.substring(0,result.length()-1);
		return result;
	}
	
	public String queryUserId(String userName) {
		try {
			String userId = nameIds.get(userName);
			if(userId!=null) {
				return userId;
			}
			userId  = this.getMainQuery().queryForString("select user_id from easi_user where username = ?", userName);
			userNames.put(userId, userName);
			nameIds.put(userName, userId);
			return userId;
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
		return null;
	}
	public String queryUserName(String userId) {
		try {
			String userName = userNames.get(userId);
			if(userName!=null) {
				return userName;
			}
			userName  = this.getMainQuery().queryForString("select username from easi_user where user_id = ?", userId);
			userNames.put(userId, userName);
			nameIds.put(userName, userId);
			return userName;
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
		return null;
	}
	
	public void sendJoinYearEmail() {
		try {
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT LEFT(t1.join_date,4) year,t2.USER_ID,t2.EMAIL,t1.join_date,t2.USERNAME,t2.OPEN_ID from yq_staff_info t1,easi_user t2 where t1.staff_user_id = t2.USER_ID and t1.account_state=0  and RIGHT(t1.join_date,5) = ?", new Object[] {EasyDate.getCurrentDateString("MM-dd")}, new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject obj:list) {
					int year = obj.getIntValue("YEAR");
					int nowYear = EasyCalendar.newInstance().getYear();
					String x = String.valueOf(nowYear - year);
					String userId = obj.getString("USER_ID");
					String username = obj.getString("USERNAME");
					
					Map<String,String> map=new HashMap<String, String>();
					MessageModel model = new MessageModel();
					model.setSender("7b4dc6a807214e58afa51cca9dc83e76");//小翠id
					model.setTplName("joinYearNotice.html");
					model.setReceiver(userId);
					model.setTitle("今天是你入职"+x+"周年纪念日");
					map.put("title", model.getTitle());
					map.put("username",username);
					map.put("year",x);
					map.put("date",EasyDate.getCurrentDateString("yyyy年MM月dd日"));
					
					model.setTitle(model.getTitle());
					String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(map);
					model.setDesc(bodyHtml);
					
					
					EmailService.getService().sendEmail(model);
					
					this.getLogger().info(username+","+model.getTitle());
				}
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
	}
	public void sendBirthdayEmail() {
		try {
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT t2.USER_ID,t2.EMAIL,t2.BORN,t2.USERNAME,t2.OPEN_ID from easi_user t2 where  t2.state = 0  and RIGHT(t2.BORN,5) = ?", new Object[] {EasyDate.getCurrentDateString("MM-dd")}, new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject obj:list) {
					String userId = obj.getString("USER_ID");
					String username = obj.getString("USERNAME");
					
					Map<String,String> map=new HashMap<String, String>();
					MessageModel model = new MessageModel();
					model.setSender("7b4dc6a807214e58afa51cca9dc83e76");//小翠id
					model.setTplName("birthdayNotice.html");
					model.setReceiver(userId);
					model.setTitle("生日快乐");
					map.put("title", model.getTitle());
					map.put("username",username);
					map.put("date",EasyDate.getCurrentDateString("yyyy年MM月dd日"));
					
					model.setTitle(model.getTitle());
					String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(map);
					model.setDesc(bodyHtml);
					
					EmailService.getService().sendEmail(model);
					
					this.getLogger().info(username+","+model.getTitle());
				}
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
	}
	
	public void createProjectWorkHour() {
		createProjectWorkHour(EasyCalendar.newInstance().getFullMonth());
	}
	
	public void createProjectWorkHour(String monthId) {
		try {
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT t2.USER_ID,t1.DEPT_ID,t2.USERNAME from easi_user t2,easi_dept_user t1 where  t2.state = 0 and t1.USER_ID = t2.USER_ID and (t2.depts like '开发%'  or t2.depts like '创新%'  or t2.depts like '工程%')", new Object[] {}, new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject obj:list) {
					String userId = obj.getString("USER_ID");
					String username = obj.getString("USERNAME");
					String deptId = obj.getString("DEPT_ID");
					
					EasyRecord record = new EasyRecord("yq_project_wh","id");
					record.set("id",monthId+"_"+userId);
					record.set("month_id",monthId );
					record.set("user_id", userId);
					record.set("user_name", username);
					record.set("dept_id", deptId);
					boolean bl = this.getQuery().update(record);
					if(!bl) {
						record.set("create_time", EasyDate.getCurrentDateString());
						this.getQuery().save(record);
					}
				}
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
	}
}
