package com.yunqu.work.model;

import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;

public class MsgModel extends JSONObject  implements Serializable{
	private static final long serialVersionUID = 1L;

	public MsgModel(){
		
	}
	public MsgModel(String msg){
		setMsg(msg);
	}
    public MsgModel(String userId,String title,String msg){
		setReceiver(userId);
		setTitle(title);
		setMsg(msg);
	}
    public MsgModel(String userId,String title,String msg,String token){
    	setReceiver(userId);
    	setTitle(title);
    	setMsg(msg);
    	setToken(token);
    }
	public String getMsgId() {
		return getString("msgId");
	}

	public void setMsgId(String msgId) {
		put("msgId", msgId);
	}

	public String getSender() {
		return getString("sender");
	}

	public void setSender(String sender) {
		put("sender", sender);
	}

	public String getReceiver() {
		return getString("receiver");
	}

	public void setReceiver(String receiver) {
		put("receiver", receiver);
	}
	
	public void setToken(String token) {
		put("token", token);
	}

	public int getSenderType() {
		return getIntValue("senderType");
	}

	public void setSenderType(int senderType) {
		put("senderType", senderType);
	}

	public String getCreateTime() {
		return getString("createTime");
	}

	public void setCreateTime(String createTime) {
		put("createTime", createTime);
	}

	public String getMsg() {
		return getString("msg");
	}

	public void setMsg(String msg) {
		put("msg", msg);
	}
	public String getTitle() {
		return getString("title");
	}
	public void setTitle(String title) {
		put("title", title);
	}
	
	
	
}
