<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>采购出入库查询</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">订单号</span>
						 	<input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">销售合同号</span>
						 	<input class="form-control input-sm" name="contractNo">
						 </div>
					  	<div class="input-group input-group-sm" style="width: 250px">
	          		    	<span class="input-group-addon">出入库日期</span>	
                     		<input data-mars="CommonDao.currentMonthRange" type="text" data-laydate="{type:'date',range: '到'}" name="dateRange" class="form-control input-sm">
                    	 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					   <div class="layui-tab layui-tab-brief" lay-filter="tab">
						  <ul class="layui-tab-title">
						    <li lay-type="10" lay-id="inGoods" class="layui-this">出库记录(<span id="inCount">0</span>)</li>
						    <li lay-type="20" lay-id="outGoods">入库记录(<span id="outCount">0</span>)</li>
						    <li lay-type="30" lay-id="returnGoods">退货记录(<span id="returnCount">0</span>)</li>
						  </ul>
						  <div class="layui-tab-content">
						  	 <div class="layui-tab-item layui-show">
								<table id="inGoods"></table>
						    </div>
						    <div class="layui-tab-item">
								<table id="outGoods"></table>
						    </div>
						    <div class="layui-tab-item">
								<table id="returnGoods"></table>
						    </div>
						  </div>
						</div> 
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
			var currentId = 'inGoods';
			var currentType = '10';
			layui.use(['element','form'],function(){
				var element = layui.element;
				element.on('tab(tab)', function(){
				    var id = this.getAttribute('lay-id');
				    var type = this.getAttribute('lay-type');
				    currentId = id;
				    currentType = type;
				});
			});
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
					initList2();
					initList3();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'GoodsDao.stockPage',
					id:'inGoods',
					page:true,
					data:{type:'10'},
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 field:'ORDER_NO',
						 title:'订单号',
						 event:'orderDetail',
						 style:'text-decoration:underline;'
					 },{
						 field:'CONTRACT_NAME',
						 title:'所属合同',
						 minWidth:160
					 },{
						 field:'NAME',
						 title:'物料名称',
						 minWidth:120
					 },{
						 field:'GOODS_NO',
						 title:'物料编号',
						 minWidth:100
					 },{
						 field:'NUMBER',
						 title:'采购数量'
					 },{
						 field:'NUM',
						 title:'入库数量'
					 },{
						 field:'PRICE',
						 title:'成本',
						 width:70,
						 templet:function(row){
							 return row['PRICE']*row['NUM'];
						 }
					 },{
					    field: 'DATE_ID',
						title: '入库日期',
						align:'center'
					},{
						field:'REMARK',
						title:'备注',
						edit:'text'
					},{
					    field: 'CREAT_NAME',
						title: '操作人',
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '操作时间',
						align:'center'
					}
				]],done:function(data){
					$('#inCount').text(data.totalRow);
			  	}
			 });
			}
			
			function initList2(){
				$("#dataMgrForm").initTable({
					mars:'GoodsDao.stockPage',
					id:'outGoods',
					page:true,
					data:{type:'20'},
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 field:'ORDER_NO',
						 title:'订单号',
						 event:'orderDetail',
						 style:'text-decoration:underline;'
					 },{
						 field:'CONTRACT_NAME',
						 title:'所属合同',
						 minWidth:160
					 },{
						 field:'NAME',
						 title:'物料名称',
						 minWidth:120
					 },{
						 field:'GOODS_NO',
						 title:'物料编号',
						 minWidth:100
					 },{
						 field:'NUMBER',
						 title:'采购数量'
					 },{
						 field:'NUM',
						 title:'出库数量'
					 },{
						 field:'OUT_TYPE',
						 title:'出库类型',
						 width:70,
						 templet:function(row){
							 var json = {'1':'整体出库','2':'零件出库'};
							 return json[row['OUT_TYPE']]||'';
						 }
					 },{
						 field:'PRICE',
						 title:'成本',
						 width:70,
						 templet:function(row){
							 return row['PRICE']*row['NUM'];
						 }
					 },{
					    field: 'DATE_ID',
						title: '出库日期',
						align:'center'
					},{
						field:'REMARK',
						title:'备注',
						edit:'text'
					},{
					    field: 'CREAT_NAME',
						title: '操作人',
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '操作时间',
						align:'center'
					}
				]],done:function(data){
					$('#outCount').text(data.totalRow);
			  	}
			});
			}
			
			function initList3(){
				$("#dataMgrForm").initTable({
					mars:'GoodsDao.stockPage',
					id:'returnGoods',
					page:true,
					data:{type:'30'},
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 field:'ORDER_NO',
						 title:'订单号',
						 event:'orderDetail',
						 style:'text-decoration:underline;'
					 },{
						 field:'CONTRACT_NAME',
						 title:'所属合同',
						 minWidth:160
					 },{
						 field:'NAME',
						 title:'物料名称',
						 minWidth:120
					 },{
						 field:'GOODS_NO',
						 title:'物料编号',
						 minWidth:100
					 },{
						 field:'RETURN_TYPE',
						 title:'退货方式',
						 width:80,
						 templet:function(row){
							 var returnType = row['RETURN_TYPE'];
							 if(returnType=='2'){
								 return '退货仓库';
							 }
							 if(returnType=='1'){
								 return '退货供应商';
							 }
						 }
					 },{
						 field:'NUMBER',
						 title:'采购数量'
					 },{
						 field:'NUM',
						 title:'退货数量'
					 },{
					    field: 'DATE_ID',
						title: '退货日期',
						align:'center'
					},{
						field:'REMARK',
						title:'备注',
						edit:'text'
					},{
					    field: 'CREAT_NAME',
						title: '操作人',
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '操作时间',
						align:'center'
					}
				]],done:function(data){
					$('#returnCount').text(data.totalRow);
			  	}
			});
			}
			
			function orderDetail(data){
				popup.openTab({id:'orderDetail',title:'采购详情',url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{orderId:data.ORDER_ID,custId:data.CUST_ID}});
			}
			
			function queryData(){
				$("#dataMgrForm").queryData({id:currentId,data:{type:currentType}});
			}
			
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>

