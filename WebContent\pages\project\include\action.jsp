<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<style>
	.publishProjectStateBody .layui-table-cell {
		height: auto;
		word-break: normal;
	 	display: block;
	 	white-space: pre-wrap;
	 	word-wrap: break-word;
	 	overflow: hidden;
	}
</style>
<div class="layui-row layui-col-space15">
	<div class="layui-card publishProjectState" style="margin-bottom: 0px;">
		  <div class="layui-card">
		  	<div style="border: 1px solid #e5e5e5;padding: 10px;">
			  	<p>
	  				<select class="form-control input-sm" name="projectState" style="width: 120px;margin-bottom: 10px;display: inline-block;margin-right: 20px;">
	                  		<option value="11">进度正常</option>
	                  		<option value="12">延时风险</option>
	                  		<option value="13">进度失控</option>
	                  		<option value="20">挂起中</option>
	                  		<option value="30">已完成</option>
	                   </select>
	                   <label class="radio radio-success radio-inline">
	   					<input type="radio" checked="checked" name="color" value="success"><span></span>
	  				 </label>
					<label class="radio radio-warning radio-inline">
	   					<input type="radio" name="color" value="warning"><span></span>
	  				 </label>
					<label class="radio radio-danger radio-inline">
	   					<input type="radio" name="color" value="danger"><span></span>
	  				 </label>
			  	</p>
			  	<textarea name="projectUpdateDesc" id="projectUpdateDesc" maxlength="300" placeholder="请输入项目状态变更理由" class="form-control input-sm" style="height: 70px;width:100%;resize:none;border: none;"></textarea>
			  	<p>
			  		&nbsp; <button type="button" onclick="publishProjectState()" class="btn btn-sm pull-right btn-info">发布</button>
			  	</p>
		  	</div>
		  </div>
		</div>
		<div class="layui-card">
		  <div class="layui-card-body publishProjectStateBody" style="padding-top: 0px;">
		  		<table class="layui-table" id="prjectStateTable"></table>
		  </div>
		</div>

 </div>
 
 <script type="text/javascript">
 
	 function projectStateLog(){
			$("#projectDetail").initTable({
				mars:'ProjectDao.projectStateLog',
				id:"prjectStateTable",
				cellMinWidth:100,
				data:{},
				skin:'line',
				page:false,
				cols: [[
	          	{
					title: '序号',
					type:'numbers'
				 },{
				    field: 'UPDATE_REASON',
					title: '更新内容',
					minWidth:400,
					templet:'<div><span class="text-{{d.FONT_COLOR}}">{{d.UPDATE_REASON}}</span></div>'
				},{
				    field: 'UPDATE_STATE',
					title: '更新状态',
					width:80,
					align:'center',
					templet:function(row){
						return projectState(row.UPDATE_STATE);
					}
				},{
				    field: 'UPDATE_BY',
					title: '更新人',
					align:'center',
					width:90,
					templet:function(row){
						return getUserName(row.UPDATE_BY);
					}
				},{
				    field: 'UPDATE_TIME',
					title: '提交时间',
					align:'center',
					width:150,
					align:'center'
				}
				]],done:function(result){
					$(".n6").text(result.total);
				}});
			
		}
 
	 function publishProjectState(){
			var data = form.getJSONObject(".publishProjectState");
			data['projectId']=projectId;
			var projectUpdateDesc=$("#projectUpdateDesc").val();
			if(projectUpdateDesc==''){
				layer.msg("描述理由不能为空.");
				return;
			}
			if(projectUpdateDesc.length<10){
				layer.msg("描述理由不能少于十个字");
				return;
			}
			ajax.remoteCall("${ctxPath}/servlet/project?action=updateProjectState",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg);
					$("#projectUpdateDesc").val("");
					$("#projectDetail").queryData({id:'prjectStateTable',page:false});
				}else{
					layer.alert(result.msg,{icon: 7});
				}
			});
			
		}
 </script>