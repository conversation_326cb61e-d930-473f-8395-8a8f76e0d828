var FlowCore = {
	businessId:'',
	params:{},
	approveData:{},
	isApplySelf:false,
	setting:{formId:'flowForm'},
	applyInfo:{},
	writeBeginTime:new Date().getTime(),
	intervalId:null,
	hasInput:false,
	lastNoticeTime:new Date().getTime(),
	initPage:function(option){
		if (option === undefined) option = {};
		if (Flow === undefined) Flow = {};
		FlowCore.params = $.extend({businessId:getUrlParam('businessId'),handle:getUrlParam('handle'),flowCode:getUrlParam('flowCode'),resultId:getUrlParam('resultId')},option.data);
		var defaultSetting={
			showNextNode:true,
			fileCheck:false,
			hideApplyNo:false,
			textModel:true,title:'',
			approvePageUrl:'',
			hideHistoryApply:false,
			hideUserTitle:false,
			addUpdateLog:true,
			uploadAuth:false
			
		};
		FlowCore.setting =$.extend(defaultSetting,option);
		var param = FlowCore.params;
		var setting = FlowCore.setting;
		if(param.flowCode==''){
			alert('flowCode is empty');
			$('body').html('');
		}
		FlowCore.businessId = param['businessId'];
		if(param.handle=='approval'){
			//$('#flowForm textarea').attr('data-fn','getContent');
			if(setting.textModel)$('#flowForm').attr('data-text-model','true');
			$('.upload-btn,.remindInfo,.unedit-remove,.appproval-remove').remove();
			
		}else if(param.handle=='edit'||param.handle=='add'){
			$("#flowForm input[type='text']").attr('autocomplete','off');
			$('.approve-result,.flowInfo,.edit-remove,.flow-approval-btn').remove();
			$('.edit-empty').html('');
			$('.edit-hide').hide();
		}else{
			//详情
			//$('#flowForm textarea').attr('data-fn','getContent');
			if(setting.textModel)$('#flowForm').attr('data-text-model','true');
			$('.upload-btn,.remindInfo,.unedit-remove,.flow-approval-btn').remove();
			top.layer.msg('已记录查看日志',{icon:7,offset:'rt',time:2000});
		}
		if(param.handle=='add'){
			FlowCore.writeTimeNotice();
		}
		
		var resultId = param.resultId;
		if(resultId!=''){
			FlowCore.renderResult(resultId);
		}
		if(param.handle=='edit'||param.handle=='add'){
			ajax.remoteCall("/yq-work/servlet/flow/approve?action=getStartNode",FlowCore.params,function(result) { 
				FlowCore.approveInfo  = result.data;
			},{async:false});
		}
		
		$("#flowForm").prepend('<input name="doAction" id="doAction" type="hidden"><input name="apply.fillTime" value="1" id="fillTime" type="hidden">');
		if($("#flowForm [name='businessId']").length==0){
			$("#flowForm").prepend('<input name="businessId" id="businessId" type="hidden" value="'+param.businessId+'">');
		}
		if($("#flowForm [name='flowCode']").length==0){
			$("#flowForm").prepend('<input name="flowCode" id="flowCode" type="hidden" value="'+param.flowCode+'">');
		}
		if($("#flowForm .upload-btn").length>0){
			FlowCore.uploadFile();
		}
		
		$("#flowForm").render({success:function(result){
			var baseInfo = result['FlowDao.baseInfo'].data;
			
			if(param.handle=='edit'||param.handle=='add'){
				$('.edit-remove').remove();
			}
						
			var dataJsonStr = baseInfo['dataJson'];
			if(dataJsonStr){
				var fieldJson = eval("("+dataJsonStr+")");
				fillRecord(fieldJson,"extend.",",","#flowForm");
			}
			
			FlowCore.applyUserInfo = result['FlowDao.baseInfo']['applyUserInfo'];
			FlowCore.applyInfo = baseInfo;
			FlowCore.renderApplyInfo(baseInfo);
			renderDate('#flowForm');
			FlowCore.handleEditBtn(baseInfo);
			FlowCore.renderHeader(option);
			FlowCore.renderFile(param,setting);
			FlowCore.bindEdit(param);
			FlowCore.openCopy(param);
			
			if(param.handle=='add'){
				FlowCore.flowTempDataRender(param);
			}
			
			var device = layui.device();
			if(device.mobile){
				$('.table,.layui-table').addClass('table-responsive');
				$('.table,.layui-table').parent().addClass('table-responsive');
				$('.table,.layui-table').addClass('text-nowrap');
			}
			
			option.success && option.success(result);
		}});
		
		FlowCore.initFaq();
	},
	flowStart:function(){
		
	},
	flowEnd:function(){
			
	},
	flowTempData:function(){
			
	},
	flowTempDataRender:function(param){
		var flowCode = param.flowCode;
		var _data = layui.data('yqflow');
		var flowTempData = _data[flowCode];
		if(flowTempData){
			layer.confirm('已读取到上次的未提交的数据，是否加载',{icon:3,offset:'20px'},function(index){
				layer.close(index);
				FlowCore.flowTempRender(flowTempData);
			},function(){
				layui.data('yqflow', {key:flowCode,remove: true});
			});
		}
 	},
	flowTempRender:function(flowTempData){
		fillRecord(flowTempData,'',',','#flowForm');
	},
	initFaq:function(){
		var flowCode = FlowCore.params.flowCode;
		var yqwork = layui.sessionData('yqwork');
		var faqUrl = yqwork['flowFaq_'+flowCode];
		if(faqUrl){
				doShow(faqUrl);
		}else{
			ajax.remoteCall("/yq-work/webcall?action=FlowConfigDao.getFaqUrl",FlowCore.params,function(result){
				var data = result.data;
				layui.sessionData('yqwork',{key:'flowFaq_'+flowCode,value:data});
				doShow(data);
			});
		}
		function doShow(url){
		  var flag = url?'&#xe607;':false;
		  layui.util.fixbar({
		    bar1: '&#xe669;'
		    ,bar2: flag
		    ,css: {right: 20, bottom: 50}
		    ,bgcolor: '#393D49'
		    ,click: function(type){
		      if(type === 'bar1'){
		        location.reload();
		      } else if(type === 'bar2') {
					window.open(url);
		      }
		    }
		});
	  }
	},
	renderResult:function(resultId){
		var param = FlowCore.params;
		param['resultId'] = resultId;
		if($("#flowForm [name='resultId']").length==0){
			$("#flowForm").prepend('<input name="resultId" id="resultId" type="hidden" value="'+resultId+'">');
		}
		ajax.remoteCall("/yq-work/servlet/flow/approve?action=getNodeInfoByResultId",param,function(result) { 
			FlowCore.approveInfo  = result.data;
			
			if(param.handle=='approval'){
				var currentNode = FlowCore.approveInfo.currentNode;
				
				var nodeCode = currentNode.nodeCode;
			
				$("[data-edit-node='"+nodeCode+"']").attr('data-text','false');

				var editField = currentNode.editField;
				if(editField){
					var editFieldArray = editField.split(',');
					for(var index in editFieldArray){
						var fieldName = editFieldArray[index];
						$("[name='apply."+fieldName+"']").attr({'data-edit-node':'true','data-text':'false','readonly':'false'}).removeAttr('readonly');
						$("[name='extend."+fieldName+"']").attr({'data-edit-node':'true','data-text':'false','readonly':'false'}).removeAttr('readonly');
					}
				}
				
			}else{
				
			}
		},{async:false});
	},
	getCurrentNodeCode:function(){
		var approveInfo = FlowCore.approveInfo;
		var nodeCode = approveInfo.currentNode.nodeCode;
		return nodeCode;
	},
	getApplyValue:function(fieldName){
		var applyInfo = FlowCore.applyInfo;
		var val = applyInfo[fieldName];
		return val||'';
	},
	renderApplyInfo:function(applyInfo){
		var applyState = applyInfo['applyState']||'0';
		if($("#flowForm [name='applyState']").length==0){
			$("#flowForm").prepend('<input name="applyState" id="applyState" type="hidden" value="'+applyState+'">');
		}
		if(applyState=='30'){
			$('.applyNodeName').text('--');
			$('.applyCheckName').text('--');
		}
		if(applyState>=10&&applyState!=21){
			var nextResultId = applyInfo.nextResultId;
			var param = FlowCore.params;
			var resultId = param.resultId;
			if(applyState!='30'&resultId==''&&nextResultId!=''){
				FlowCore.renderResult(nextResultId);
			}
		}
		var relatedFlow = applyInfo['relatedFlow'];
		if(relatedFlow){
			ajax.remoteCall("/yq-work/webcall?action=FlowDao.getApplyTitle",{id:relatedFlow},function(result){
				var data = result.data;
				$("[name='apply.relatedFlow']").next().val(data);
			});
		}
	},
	bindEdit:function(param){
		if(param.handle=='approval'){
			$("[data-edit-node]").blur(function(){
				var t = $(this);
				var refUpdate = t.data('refUpdate');
				if(refUpdate){
					if(refUpdate=='prev'){
						t.prev().attr('data-update','1');
					}else if(refUpdate=='next'){
						t.next().attr('data-update','1');
					}
				}
				t.attr('data-update','1');
				$('.save-field').attr('data-save','fail');
				if($('.save-field').length==0){
					$('.split-symbol').after('<button class="btn btn-success btn-sm mr-5 save-field" data-save="fail" onclick="FlowCore.updateField();" type="button"><i class="fa fa-save"></i> 保存修改 </button>');
					layer.msg('请修改后点击【保存修改】后再审批',{icon:7});
				}
			});
		}
	},
	updateField:function(){
		var fields = {};
		$("[data-update='1']").each(function(){
			var t = $(this);
			t.attr('data-update','0');
			var fieldName = t.attr('name');
			if(fieldName=='apply.applyRemark'){
				fieldName = 'apply.apply_remark';
			}
			var val = t.val();
			fields[fieldName] = val;
		});
		
		var data = $.extend(FlowCore.params,{fields:fields});
		ajax.remoteCall("/yq-work/servlet/flow/fun?action=updateField",data,function(result) { 
			if(result.state == 1){
				$('.save-field').attr('data-save','ok');
				layer.msg(result.msg,{icon:1,time:800},function(){
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
			
	},
	openCopy:function(param){
		if(param.handle!='edit'&&param.handle!='add'){
			$('.copy').each(function(){
				$(this).append('<button class="btn btn-default btn-xs pull-right copy-btn" type="button">复制</button>');
			});
			 var clipboard = new ClipboardJS('.copy-btn', {
			     text: function (trigger) {
			        return copyText(trigger);
			      }
			  });
			  clipboard.on('success', function (e) {
				  layer.msg('复制成功',{icon:1,time:800});
			  });
			  clipboard.on('error', function (e) {
				  console.error(e);
				  layer.msg('复制失败',{icon:2,time:800});
			  });
			  function copyText(trigger){
				var text = '';
				var el = $(trigger).parent();
				if(el.is('input')){
					text = el.val();
				}else if(el.is('td')){
					if(el.find('span').length>0){
						text = el.find('span').last().text();
					}else{
						text = el.text();
					}
				}else{
					text = el.text();
				}
				return text;
			}
		}
	},
	renderHeader:function(option){
		var param = FlowCore.params;
		var setting = FlowCore.setting;
		var staffInfo = getStaffInfo();
		var applyInfo = FlowCore.applyInfo;
		var applyBy = applyInfo.applyBy;
		var isApplySelf = (applyBy==staffInfo.userId);
		FlowCore.isApplySelf = isApplySelf;
		if(param.handle!='edit'&&param.handle!='add'){
			$("[name='apply.applyName']").attr('onclick',"userInfoLayer('"+applyBy+"')");
			$("[name='apply.applyName']").append('<a class="layui-hide-xs hidden-print" href="javascript:;"> 详情</a>');
			
			if(!setting.hideApplyNo){
				$('.flow-title').after("<div style='position: absolute;left: 10px;top: 40px;' class='visible-print'>流水号："+applyInfo.applyNo+"</div>");
			}
			var html = [];
			html.push('<div class="layui-row hidden-print flow-header"><div class="layui-col-md12">');
			html.push('<div class="pull-left">');
			
			if(param.handle=='approval'){
				html.push('<button class="btn btn-info btn-info btn-sm mr-5" onclick="FlowCore.approveLayer()" type="button"><i class="fa fa-paper-plane"></i> 立即审批</button>');
				html.push('<button class="btn btn-default btn-sm mr-5 layui-hide" onclick="FlowCore.trustLayer()" type="button"><i class="fa fa-exchange"></i> 委托</button>');
			}else{
				html.push('<button class="btn btn-outline btn-info btn-sm layui-hide-xs printBtn ml-5 mr-5" onclick="FlowCore.flowPrint()" type="button"><i class="fa fa-print"></i> 打印</button>');
			}
			html.push('<span class="split-symbol hidden"></span>');
			
			html.push($('.flow-btn').html());
			$('.flow-btn').remove();
			
			if(param.handle=='approval'){
				html.push($('.flow-approval-btn').html());
			}
			$('.flow-approval-btn').remove();
			
			var commentCount  = applyInfo.commentCount;
			if(commentCount>0){
				html.push('<button class="btn btn-outline btn-info btn-sm mr-5" onclick="FlowCore.commentLayer();" type="button"><i class="fa fa-pencil"></i> 留言('+commentCount+') </button>');
			}
			var ccCount  = applyInfo.ccCount;
			if(ccCount>0){
				html.push('<button class="btn btn-outline btn-info btn-sm mr-5" onclick="FlowCore.lookCCLayer();" type="button"><i class="fa fa-mail-forward"></i> 抄送('+ccCount+') </button>');
			}
			
			var reviseCount  = applyInfo.reviseCount;
			if(isApplySelf){
				html.push('<button class="btn btn-outline btn-info btn-sm mr-5" onclick="FlowCore.updateLogLayer();" type="button"><i class="fa fa-pencil"></i> 修订('+reviseCount+') </button>');
			}
			
			var relationFlow  = applyInfo.relatedFlow;
			if(relationFlow){
				var flowCount = relationFlow.split(',').length;
				html.push('<button class="btn btn-info btn-sm mr-5" onclick="FlowCore.lookRelatedFlow(\''+relationFlow+'\');" type="button"><i class="fa fa-random"></i> 关联流程('+flowCount+') </button>');
			}
			
			html.push('<button class="btn btn-outline btn-info btn-sm mr-5 moreBtn" type="button">更多操作<i class="layui-icon layui-icon-down layui-font-12"></i></button>');
			
			html.push('</div>');
			
			html.push('<div class="pull-right layui-hide-xs">');
			
			html.push('流水号：'+applyInfo.applyNo);

			html.push('</div>');
			html.push('</div></div>');
			$("#flowForm").prepend(html.join(''));
			
			var dropdownData = [
				{title: '<i class="fa fa-comment-o"></i> 留言栏',id: 6},
				{title: '<i class="fa fa-paper-plane-o"></i> 流程抄送',id: 0},
				{title: '<i class="fa fa-eye"></i> 审批步骤',id: 1},
				{title: '<i class="fa fa-history"></i> 历史申请',id: 2},
				{title: '<i class="fa fa-image"></i> 保存图片</button>',id: 3},
				{title: '<i class="fa fa-share-alt"></i> 分享链接',id: 4},
				{title: '<i class="fa fa-eye"></i> 查看日志',id: 5}
			];
			
			if(setting.hideHistoryApply){
				dropdownData.splice(3,1);
			}
			if(commentCount>0){
				dropdownData.splice(0,1);
			}
			
			layui.dropdown.render({elem: '.moreBtn',data: dropdownData,trigger:'hover',click: function(data, othis){
				var _id = data.id;
				if(_id=='1'){
					FlowCore.approveNode();
				}else if(_id=='2'){
					FlowCore.historyApply();
				}else if(_id=='3'){
					FlowCore.flowSaveImg();
				}else if(_id=='4'){
					FlowCore.flowShare();
				}else if(_id=='5'){
					FlowCore.flowLog();
				}else if(_id=='0'){
					FlowCore.ccLayer();
				}else if(_id=='6'){
					FlowCore.commentLayer();
				}
		      }
		 	});
		
			$('.flow-title').after('<div class="flow-state  layui-hide-xs hidden-print"><svg style="width: 16rem;height: 16rem;" class="icon" aria-hidden="true"><use xlink:href="#icon-flow-state-'+applyInfo.applyState+'"></use></svg></div>');
			
			//$("#flowForm").append('<div class="text-r mr-20 f-12 visible-print"><span>打印人：'+staffInfo.userName+' '+staffInfo.staffNo+'</span><span class="ml-20">打印时间：'+getCurrentTime()+'</span></div>');
		}
		
	},
	renderFile:function(param,setting){
		if(param.handle=='add'){
			return;
		}
		var type = 1;
		if(param.handle=='edit'){
			type = 0;
		}
		if(FlowCore.businessId){
			var  uploadAuth= setting.uploadAuth||false;
			var existFileList = $("#flowForm .fileList").length>0;
			if(existFileList||uploadAuth){
				ajax.daoCall({loading:false,controls:['FileDao.fileList'],params:{fkId:FlowCore.businessId}},function(rs){
				var result = rs['FileDao.fileList'].data;
				if(existFileList){
					for(var index in result){
						var data = result[index];
						var no = Number(index)+1;
						if(type==0){
							$("#flowForm .fileList").append('<div class="file-div">'+data.CREATE_NAME+'于'+data.CREATE_TIME+'上传 <input name="fileIds" value='+data.FILE_ID+' type="hidden"/><span><a href="/yq-work/fileview/'+data.FILE_ID+'?view=online" target="_blank">'+data.FILE_NAME+'</a></span> ('+data.FILE_SIZE+')<i title="删除" data-id="'+data.FILE_ID+'" onclick="delFile($(this),true)">x</i></div>');
						}else{
							$("#flowForm .fileList").append('<span class="visible-print">'+data.FILE_NAME+'</span><div class="file-div hidden-print"> <input name="fileIds" value='+data.FILE_ID+' type="hidden"/><span> '+no+'.&nbsp;<a class="hidden-print" href="/yq-work/fileview/'+data.FILE_ID+'?view=online" target="_blank">'+data.FILE_NAME+'</a> <a  href="/yq-work/fileview/'+data.FILE_ID+'" target="_blank" class="f-12"><i class="fa fa-download"></i></a></span> ('+data.FILE_SIZE+') '+data.CREATE_NAME+'于'+data.CREATE_TIME+'上传</div>');
						}
					}
				}
				if(param.handle!='edit'){
					if(result.length>0){
						$('.split-symbol').after('<button class="btn btn-outline btn-info btn-sm mr-5" onclick="FlowCore.flowFile();" type="button"><i class="fa fa-eye"></i> 附件('+result.length+') </button>');
				   }else{
					   $("#flowForm .fileList").append('无上传');
				  }
				}
			});
		 }
	 }
	},
	handleEditBtn:function(applyInfo){
		var param = FlowCore.params;
		var applyState = applyInfo['applyState'];
		var businessId = param.businessId;
		var handle = param.handle;
		if(param.handle=='edit'||param.handle=='add'){
			var array = [];
			array.push('<div class="layui-row hidden-print flow-header"><div class="layui-col-md12">');
			array.push('<div class="pull-left">');
			
			if(applyState=='0'||businessId==''){
				array.push('<button class="btn btn-sm btn-info mr-10 saveBtn" type="button" onclick="Flow.' +
					'(\'submit\');"><i class="fa fa-paper-plane"></i> 提交审批</button>');
				array.push('<button class="btn btn-sm btn-default mr-10 btn-outline draftBtn" type="button" onclick="Flow.ajaxSubmitForm(\'draft\');">保存草稿</button>');
				array.push('<button class="btn btn-sm btn-default mr-10 btn-outline" type="button" onclick="FlowCore.historyApply();">申请记录</button>');
			}else{
				if(handle=='edit'&&applyState!='0'&&applyState!='21'){
					array.push('<button class="btn btn-sm btn-info mr-15 saveBtn" type="button" onclick="Flow.ajaxSubmitForm(\'edit\');">修改保存</button>');
				}
				if(handle=='edit'&&applyState=='21'){
					array.push('<button class="btn btn-sm btn-danger mr-15 saveBtn" type="button" onclick="Flow.ajaxSubmitForm(\'submit\');"><i class="fa fa-paper-plane"></i> 提交审批</button>');
					array.push('<button class="btn btn-sm btn-default mr-15 saveBtn" type="button" onclick="Flow.ajaxSubmitForm(\'edit\');"> 暂存 </button>');
				}
			}
			if(businessId){
				array.push('<button class="btn btn-sm btn-warning layui-hide-xs mr-15" type="button" onclick="FlowCore.updateLogLayer();"><i class="fa fa-pencil"></i> 修订日志</button>');
			}
			
			array.push('<button class="btn btn-sm btn-default mr-15 layui-hide-xs hidden" type="button" onclick="FlowCore.setRelatedFlow();"><i class="fa fa-random"></i> 关联流程 </button>');
			
			array.push($('.flow-btn').html());
			$('.flow-btn').remove();
			
			array.push('</div>');
			array.push('</div></div>');
			$("#flowForm").prepend(array.join(''));
		}
		
	},
	setRelatedFlow:function(){
		
	},
	lookRelatedFlow:function(relationFlowIds){
		if(relationFlowIds.indexOf(',')>-1){
			var array = relationFlowIds.split(',');
			var i = 1;
			for(var index in array){
				var id = array[index];
				popup.openTab({id:'flowDetail',title:'关联流程('+i+')',url:'/yq-work/web/flow',data:{handle:'detail',businessId:id}});
				i++;
			}
		}else{
			popup.openTab({id:'flowDetail',title:'关联流程',url:'/yq-work/web/flow',data:{handle:'detail',businessId:relationFlowIds}});
		}
	},
	addCheckOrder:function(option) {
		if (option === undefined) option = {data:{nextNodeId:'',nextNodeCode:''},openLayer:true};
		
		var doAction = $('#doAction').val();
		if(doAction=='submit'){
			if(FlowCore.businessId){
				Flow.updateData(); 
			}else{
				Flow.insertData(); 
			}
			return;
		}
		
		var reqUrl = option.reqUrl||'/yq-work/servlet/flow/approve/?action=doApprove';
		var params = FlowCore.params;
		var businessId = params.businessId,resultId = params.resultId;
		if(businessId==''||resultId==''){
			layer.msg('请从审批页面入口进来',{icon:7,offset:'50px'});
			return;
		}
		var openLayer = option.openLayer;
		if(option.openLayer==undefined){
			openLayer = true;
		}
		if(openLayer){
			var checkResult = $("#approveForm [name='checkResult']:checked").val();
			if(checkResult==undefined){
				layer.msg('请选择审批结果',{icon:7,offset:'50px'});
				return;
			}
			var checkDesc = $("#approveForm [name='checkDesc']").val();
			if(checkResult=='2'&&checkDesc==''){
				layer.msg('请输入退回理由',{icon:7,offset:'50px'});
				return;
			}else{
				if(checkDesc==''){
					checkDesc='同意';
				}
			}
			var checkType = $("#approveForm #checkType").val();
			
			var formData = form.getJSONObject("#approveForm");
			
			var ccIds = $("#approveForm [name='ccIds']").val();
			var ccNames = $("#approveForm [name='ccNames']").val();
			
			var selectUserName = $("#approveForm [name='selectUserName']").val();
			var selectUserId = $("#approveForm [name='selectUserId']").val();
			
			var custData = {checkResult:checkResult,checkDesc:checkDesc,ccIds:ccIds,ccNames:ccNames,selectUserId:selectUserId,selectUserName:selectUserName};
			
			var data = $.extend(params,formData,custData,FlowCore.approveData,option.data);
			
			if(checkType=='30'&&(data.selectUserName==''&&data.selectUserId=='')){
				layer.msg('请选择审批人',{icon:2});
				return;
			}
			FlowCore.doApprove($.extend(option,{reqUrl:reqUrl,data:data}));
		}else{
			var data = $.extend(params,{checkResult:'1',checkDesc:'同意'},option.data);
			var newData = $.extend(data,option.data);
			FlowCore.doApprove($.extend(option,{reqUrl:reqUrl,data:newData}));
		}
		
  },
  // 提交到协办
  toFlowAssist:function(){
		var formData = form.getJSONObject("#approveForm");
	  	ajax.remoteCall("/yq-work/servlet/flow/fun?action=approveAssist",$.extend(formData,FlowCore.params),function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:800},function(){
					location.reload();
				})
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
  },
  //协办处理
  doFlowAssist:function(){
	  var formData = form.getJSONObject("#approveForm");
	  FlowCore.doApprove({data:formData});
  },
  doFlowChat:function(){
	 var formData = form.getJSONObject("#approveForm");
	 ajax.remoteCall("/yq-work/servlet/flow/fun?action=addComment",$.extend(formData,FlowCore.params),function(result) { 
	   if(result.state == 1){
		layer.msg(result.msg,{icon:1,time:800},function(){
			location.reload();
		})
	  }else{
		layer.alert(result.msg,{icon: 5});
	 }
   });
  },
  doFlowTrust:function(){
	 var formData = form.getJSONObject("#approveForm");
	  FlowCore.submitTrust(formData);
  },
  doApprove:function(option){
	if (option === undefined) option = {};
		var data = $.extend(FlowCore.params,option.data);
		var reqUrl = option.reqUrl||'/yq-work/servlet/flow/approve/?action=doApprove';
		
		ajax.remoteCall(reqUrl,data,function(result) { 
			 layer.closeAll();
			if(result.state == 1){
				var msg = result.msg;
				if(msg.indexOf('流程结束')>-1){
					FlowCore.flowEnd();
				}
			    layer.msg(msg,{time:800,icon:1},function(){
					//html2canvas(document.querySelector(".ibox-content"),{useCORS: true, scale: window.devicePixelRatio < 3 ? window.devicePixelRatio : 2,allowTaint: true}).then(function(canvas) {
					//	 const imgData = canvas.toDataURL('image/png', 1.0);
					//	 ajax.remoteCall("/yq-work/servlet/flow/fun?action=saveFlowImg",$.extend(FlowCore.params,{imgData:imgData}),function() {
							 FlowCore.myTodoList();
					//	});
					//});
				});
			}else{
				$('button').removeAttr('disabled');
				layer.alert(result.msg,{icon: 5});
			}
		},{then:function(result){
			option.success && option.success(result);
		},beforeSend:function(){
			$('button').attr('disabled','disabled');
			layer.load();
		},error:function(){
			layer.closeAll('loading');
		}});	
  },
  myApplyList:function(){
		layer.closeAll();
		popup.closeTab({callback:function(){
			popup.openTab({id:'flow_my_apply',url:'/yq-work/pages/flow/my/flow-list.jsp',title:'我的申请',reload:true})
		}});
   },
  myTodoList:function(){
		layer.closeAll();
		if(self==top){
			location.href='/yq-work/flow/todo';
		}else{
			popup.closeTab({callback:function(){
				popup.openTab({id:'flow_my_todo',url:'/yq-work/pages/flow/my/flow-todo.jsp',title:'我的待办',reload:true})
			}});
		}
   },
   lookTrust:function(trustId){
		ajax.daoCall({loading:false,controls:['FlowCommon.trustInfo'],params:{trustId:trustId}},function(rs){
			var data = rs['FlowCommon.trustInfo'].data;
			var html = [];
			html.push('委托人：'+data['CREATE_BY_NAME']);
			html.push('委托时间：'+data['TRUST_TIME']);
			html.push('委托原因：'+data['TRUST_REASON']);
			layer.alert(html.join('<br>'),{title:'委托信息',offset:'20px'});
		});
	},
   lookCC:function(resultId){
		ajax.daoCall({loading:false,controls:['FlowCommon.resultCCList'],params:{resultId:resultId}},function(rs){
			var list = rs['FlowCommon.resultCCList'].data;
			var data = list[0];
			var html = [];
			html.push('创建人：'+data['CREATE_NAME']);
			html.push('创建时间：'+data['CC_TIME']);
			html.push('备注：'+data['CC_REMARK']);
			for(var index in list){
				html.push("抄送人："+list[index]['CC_BY_NAME']+" "+list[index]['READ_TIME']);
			}
			layer.alert(html.join('<br>'),{title:'抄送信息',offset:'20px'});
		});
  },
  approveCommentLayer:function(toUserId,toUserName,resultId,el){
	FlowCore.tempReplayObj = el;
	if(toUserName){
		if(toUserName.indexOf(',')>-1){
		   var toUserNames = toUserName.split(',');
		   var toUserIds = toUserId.split(',');
		   var html = [];
		   for(var index in toUserNames){
			   html.push('<button class="btn btn-link" type="button" onclick="FlowCore.selectReplyUser(\''+toUserIds[index]+'\',\''+toUserNames[index]+'\',\''+resultId+'\')">'+toUserNames[index]+'</button>');
		   }
		   layer.open({content:html.join(''),offset:'20px',shade:0.1,title:'请选择其中一个',type:1,area:['300px','200px']});
		}else{
			FlowCore.selectReplyUser(toUserId,toUserName,resultId);					
		}
	}
 },
  selectReplyUser:function(toUserId,toUserName,resultId){
	layer.closeAll();
	var el = FlowCore.tempReplayObj;
	var data = {toUserId:toUserId,toUserName:toUserName,resultId:resultId,businessId:FlowCore.businessId};
	data['replyContent'] = $(el).find('textarea').val();
	popup.layerShow({url:'/yq-work/pages/flow/comm/flow-comment.jsp',full:fullShow(),area:['700px','100%'],shadeClose:true,scrollbar:false,offset:'r',data:data,id:'commentLayer',title:'留言栏'});
  },	
  selectRelatedFlow:function(el,type){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	var data = $.extend($(el).data(),{sid:id,type:type});
	popup.layerShow({id:'selectRelatedFlow',full:fullShow(),scrollbar:false,area:['700px','100%'],offset:'r',title:'选择关联流程',url:'/yq-work/pages/flow/select/select-related-flow.jsp',data:data});
  },
  setFillTime:function(){
	 var writeEndTime = new Date().getTime();
	 var fillTime = writeEndTime - FlowCore.writeBeginTime;
	 fillTime = Math.round(fillTime/1000);
	 $('#fillTime').val(fillTime);
 },
 writeTimeNotice:function(){
	$('input, textarea,select').on('change', function() {
    	FlowCore.hasInput = true;
	});
	var flowCode = FlowCore.params.flowCode;
	if(flowCode=='finance_bx'){
		FlowCore.hasInput = true;
	}


	FlowCore.intervalId = setInterval(function() {
	  var writeEndTime = new Date().getTime();
	  var hasWriteTime = writeEndTime - FlowCore.writeBeginTime;
	  var fillTime = writeEndTime - FlowCore.lastNoticeTime;
	  var noticeTime = 1000*60*3;//3分钟

	  if(FlowCore.hasInput){
		  var _formData = form.getJSONObject("#flowForm");
		  var tempData = $.extend({},_formData,FlowCore.flowTempData());
		  delete tempData['apply.applyNo'];
		  layui.data('yqflow', {key:flowCode,value:tempData});
		  layer.msg('当前流程表单数据缓存成功',{icon:1,offset:'rb'});
		  if(fillTime>noticeTime){
			  FlowCore.lastNoticeTime = new Date().getTime();
			  top.layer.msg('您填写时间已经超过了'+formatMilliseconds(hasWriteTime)+',请及时提交或保存草稿，当前系统已经自动帮你保存到本地缓存',{offset:'rt',icon:7});
		  }
	  }
	}, 10000);
	
	function formatMilliseconds(milliseconds) {
	  var totalSeconds = Math.floor(milliseconds / 1000);
	  var minutes = Math.floor(totalSeconds / 60);
	  var seconds = totalSeconds % 60;
	  return minutes + " 分钟 " + seconds + " 秒";
	}

  },
  ajaxSubmitForm:function(state){
		$("[name='doAction']").val(state);
		if(form.validate("#flowForm")){
			var approveInfo = FlowCore.approveInfo;
			var nextNode = approveInfo.nextNode;
			
			var currentNode = approveInfo.currentNode;
			if(currentNode.submitCode){
				eval(currentNode.submitCode);
			}
			
			var flowCode = FlowCore.params.flowCode;
			layui.data('yqflow', {key:flowCode,remove: true});
			
			FlowCore.checkFileCount(function(){
				if(state=='submit'&&nextNode.checkType=='30'){
					FlowCore.approveLayer();
				}else{
					if(FlowCore.businessId){
						Flow.updateData(state); 
					}else{
						Flow.insertData(state); 
					}
				}
			});
		};
	},
	checkFileCount:function(fn){
		var fileCheck = FlowCore.setting.fileCheck;
		var limit = 0;
		if($.type(fileCheck)=='boolean'&&fileCheck){
			limit = 1;
		}else{
			limit = fileCheck;
		}
		if(limit>0){
			var fileCount = $('.file-div').length;
			if(fileCount<limit){
				layer.msg('请至少上传'+limit+'个附件',{icon:2});
				return;
			}
		}
		fn && fn();
	},
	insertData:function(option){
		if (option === undefined) option = {};
		var data = option.data||{};
		FlowCore.setFillTime();
		
		var approveFormData = {};
		if($('#approveLayer').length>0){
			approveFormData = form.getJSONObject("#approveForm");
			var selectUserName = approveFormData['selectUserName'];
			if(selectUserName==''){
				layer.msg('请选择');
				return;
			}
		}
		
		
		var postData = $.extend(form.getJSONObject("#flowForm"),data,approveFormData,FlowCore.approveData);
		FlowCore.setApplyTitle(postData);
		
		$('[data-mapping]').each(function(){
			var name = $(this).attr('name');
			var mapping = $(this).data('mapping');
			postData['apply.'+mapping] = postData[name];
		});
		
		var reqUrl = option.reqUrl||'/yq-work/servlet/flow/approve?action=submitApply';
		ajax.remoteCall(reqUrl,toDbField(postData,'apply'),function(result) { 
			 layer.closeAll('loading');
			if(result.state == 1){
				option.success && option.success(result);
				
				if(postData['doAction']=='submit'){
					FlowCore.flowStart();
				}
				
				layer.msg(result.msg,{time:800,icon:1},function(){
					html2canvas(document.querySelector(".applyInfo"),{useCORS: true, scale: window.devicePixelRatio < 3 ? window.devicePixelRatio : 2,allowTaint: true}).then(function(canvas) {
						 const imgData = canvas.toDataURL('image/png', 1.0);
						 FlowCore.params['businessId'] = result.data;
						 ajax.remoteCall("/yq-work/servlet/flow/fun?action=saveFlowImg",$.extend(FlowCore.params,{imgData:imgData}),function() {
							 FlowCore.myApplyList();
						});
					});
				});
			}else{
				$('button').removeAttr('disabled');
				layer.alert(result.msg,{icon: 5});
			}
		},{then:function(result){
			
		},beforeSend:function(){
			$('button').attr('disabled','disabled');
			layer.load();
		},error:function(){
			layer.closeAll('loading');
		}});
	},
	updateData:function(option){
		if (option === undefined) option = {};
		var data = option.data||{};
		FlowCore.setFillTime();
		
		var approveFormData = {};
		if($('#approveLayer').length>0){
			approveFormData = form.getJSONObject("#approveForm");
		}
		
		var postData = $.extend(form.getJSONObject("#flowForm"),data,approveFormData,FlowCore.approveData);
		FlowCore.setApplyTitle(postData);
		
		$('[data-mapping]').each(function(){
			var name = $(this).attr('name');
			var mapping = $(this).data('mapping');
			postData['apply.'+mapping] = postData[name];
		});
		
		var reqUrl = option.reqUrl||'/yq-work/servlet/flow/approve?action=updateApply';
		var applyInfo = FlowCore.applyInfo;
		var applyState = applyInfo['applyState'];
		var addUpdateLog = FlowCore.setting.addUpdateLog;
		if(addUpdateLog&&applyState=='20'){
			layer.prompt({formType: 2,value: '',offset:'20px',title: '请输入修改日志',area: ['400px', '150px']}, function(msgContent, index, elem){
		    	 layer.close(index);
		    	 ajax.remoteCall("/yq-work/servlet/flow/fun?action=addReviseLog",{businessId:FlowCore.businessId,reviseContent:msgContent},function(result) {
					submitData();
				});	    	  	
	    	});
		}else{
			submitData();
		}

		function submitData(){
			ajax.remoteCall(reqUrl,toDbField(postData,'apply'),function(result) { 
				 layer.closeAll('loading');
				if(result.state == 1){
					option.success && option.success(result);
					
					if(postData['doAction']=='submit'){
						FlowCore.flowStart();
					}
				
					layer.msg(result.msg,{time:800,icon:1},function(){
					html2canvas(document.querySelector(".applyInfo"),{useCORS: true, scale:window.devicePixelRatio < 3 ? window.devicePixelRatio : 2,allowTaint: true}).then(function(canvas) {
						 const imgData = canvas.toDataURL('image/png', 1.0);
						 ajax.remoteCall("/yq-work/servlet/flow/fun?action=saveFlowImg",$.extend(FlowCore.params,{imgData:imgData,resultId:''}),function() {
							 FlowCore.myApplyList();
						});
					});
				  });
				}else{
					$('button').removeAttr('disabled');
					layer.alert(result.msg,{icon: 5});
				}
			},{then:function(result){
				
			},beforeSend:function(){
				$('button').attr('disabled','disabled');
				layer.load();
			},error:function(){
				layer.closeAll('loading');
			}});
		}
	},
	setApplyTitle:function(data){
		var newData = $.extend({},data);
		newData = removeFieldPrefix(newData);
		var staffInfo = getStaffInfo();
		var userName = staffInfo.userName+staffInfo.staffNo;
		var applyTitle = $("[name='apply.applyTitle']");
		var template = applyTitle.data('template');
		var title = FlowCore.setting.title;
		var hideUserTitle = FlowCore.setting.hideUserTitle;
		if(hideUserTitle){
			userName = '';
		}
		var setVal = '';
		if(title){
			var tmp = $.templates(title);
			setVal = userName+tmp.render(newData);
		}else if(template){
			var tmp = $.templates(template);
			setVal = userName+tmp.render(newData);
		}
		if(setVal){
			applyTitle.val(setVal);
			data['apply.applyTitle'] = setVal;
		}
	},
	uploadFile:function(option){
		if (option === undefined) option = {};
		
		var _elem = option.elem || '.upload-btn';
		$(_elem).hover(function(){
			var msg = $(this).data('msg')||'';
			if(msg){msg = msg +"<br>";}
			layer.tips(msg+'请命名好文件名再上传<br>如文件大于10M，请使用内网上传<br>手机传文件到电脑http://2w.ma',this,{tips:[2,'#5fb878'],time:3000});
		});
		
		layui.use('upload', function(){
		 	  var upload = layui.upload;
			  var uploadInst = upload.render($.extend({
			    elem: '.upload-btn'
			    ,url: '/yq-work/servlet/upload?action=index'
			    ,accept: 'file'
			    ,exts:'xls|xlsx|txt|jpg|zip|ppt|pptx|doc|docx|wps|tar|png|pdf|csv|rar|tar|7z|mp4|mp3|eml|war|jsp'
			    ,size:1024*200
				,multiple:true
				,number:20
			    ,field: 'flowFile'
			    ,data: {
					fkId:function(){
						return FlowCore.params.businessId||'';
					},
					source:'flow/'+FlowCore.params.flowCode
				}
			    ,done: function(res, index, upload){
					var item = this.item;
				    layer.closeAll('loading');
				    if(res&&res.state==1){
				    	layer.msg(res.msg,{icon: 1,time:800},function(){
							callback(res.data,item);
						}); 
					}else{
						layer.alert(res.msg,{icon: 5});
					}
			    },before:function(){
					layer.load();
			    },allDone: function(obj){ 
				
				}
			    ,error: function(res, index, upload){
					layer.closeAll('loading');
			    	layer.alert("上传文件请求异常！",{icon: 5});
			    }
	      },option));
  	  });
	 var callback = function(result,el){
			var array = [];
			if(isArray(result)){
				array = result;
			}else{
				array[0] = result;
			}
			var target = option.target;
			for(var index in array){
				var data = array[index];
				var fileName = data.name;
				var html = '<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="/yq-work/fileview/'+data.id+'?view=online" target="_blank">'+fileName+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this),true)">x</i></div>';
				$(el).before(html);
				if(target){
					$($g(target)).prepend(html);
				}
				if(fileName.length==35||fileName.indexOf('微信图片')>-1||fileName.indexOf('QQ图片')>-1||fileName.indexOf('WeChat')>-1||fileName.indexOf('IMG')>-1){
					layer.alert('文件名：'+fileName+'，不规范，建议删除后规范命名再上传',{title:'上传提醒',icon:7});
				}
			}
	}
  },
  newNextNode:function(nextNode,approveData){
	if(approveData.nextNodeCode){
		nextNode.checkName = '';
		nextNode.nodeName = approveData.nextNodeName || approveData.nextNodeCode;
		if(approveData.nextNodeCode=='0'){
			nextNode.nodeName = '审批结束';
		}
		if(approveData.nextNodeName==''){
			nextNode.nextNodeName = approveData.nextNodeCode;
		}
	}
	if(approveData.selectUserName){
		nextNode.checkName = approveData.selectUserName;
	}
	if(approveData.selectUserId){
		nextNode.checkBy = approveData.selectUserId;
	}
	return nextNode;
  },
  approveLayer:function(){
	var setting = FlowCore.setting;
	
	var  checkFlag = true;
	if(Flow.approverLayerBefore){
		checkFlag = Flow.approverLayerBefore();
	}
	if(!checkFlag){
		return;
	}
	
	var saveFieldBtn = $('.save-field');
	if(saveFieldBtn.length>0){
		if(saveFieldBtn.attr('data-save')!='ok'){
			layer.msg('请先点击保存',{icon:7,time:800});
			return;
		}
	}
	
	var flowCode = FlowCore.params.flowCode;
	var showNextNode = setting.showNextNode;
	
	var approveInfo = FlowCore.approveInfo;
	
	var currentNode = approveInfo.currentNode;
	
	var nowResult = approveInfo.nowResult;
	
	var currentNodeName = nowResult.nodeName;
	var title = '流程审批';
	if(currentNodeName){
		title = '流程审批 #'+currentNodeName;
	}
	
	if(nowResult.approveType=='1'){
		popup.layerShow({url:'/yq-work/pages/flow/comm/approve-assist.jsp',full:fullShow(),area:['600px','500px'],shadeClose:false,scrollbar:false,offset:'20px',data:{flowCode:flowCode},id:'approveLayer',title:title});
		return;
	}
	
	var approvePageUrl = setting.approvePageUrl || '/yq-work/pages/flow/comm/approve-do.jsp';
	
	var device = layui.device();
	if(device.mobile){
		approvePageUrl = setting.approvePageUrl || '/yq-work/pages/flow/comm/approve-do-h5.jsp';
	}
	
	popup.layerShow({url:approvePageUrl,full:fullShow(),area:['600px','500px'],shadeClose:false,scrollbar:false,offset:'20px',data:{flowCode:flowCode},id:'approveLayer',title:title,success:function(layero, index){
		if(setting.uploadAuth){
			$('.approve-upload').show();
			FlowCore.uploadFile({elem:'.approve-upload-btn'});
		}
		
		var doAction = $('#doAction').val();
		if(doAction=='submit'){
			$('#checkResultTr').hide();
		}
		
		var approveCode = currentNode.approveCode;
		if(approveCode){
			eval(approveCode);
		}
		
		var approveData = FlowCore.approveData;
		
		var nextNode = FlowCore.newNextNode(approveInfo.nextNode,approveData);
		var ccIds = currentNode.ccIds;
		if(ccIds){
			$("#approveForm [name='ccIds']").val(ccIds);
			$("#approveForm [name='ccNames']").val(currentNode.ccNames);
			$('.ccSelect').show();
		}
		
		var ccNames = approveData.ccNames;
		if(ccNames){
			if(ccIds){
				ccNames = currentNode.ccNames+","+ccNames;
			}
			$("#approveForm [name='ccNames']").val(ccNames);
			$('.ccSelect').show();
		}
		var nextNodeName = nextNode.nodeName;
		if(nextNodeName&&showNextNode){
			$('#approveForm #nextNodeName').val(nextNodeName);
			$('#approveForm #selectUserName').val(nextNode.checkName);
			$('#approveForm #selectUserId').val(nextNode.checkBy);
			//$('#approveForm .checkSelect').show();
			
		    $('.nextCheckInfo').text(nextNodeName+"_"+nextNode.checkName);
		}else{
			//$('#approveForm .checkSelect').hide();
			$('.nextCheckInfo').text("流程结束");
			$('.nextCheckInfo').parent().find('.checkbox').hide();
		}
		
		var opinionTitle = nextNode.opinionTitle||'审批意见';
		$("#opinionTitle").text(opinionTitle);
		
		var checkType = nextNode.checkType;
		if(nextNodeName&&checkType=='30'){
			$('#approveForm .checkSelect').show();
			if(approveData.selectUserId||approveData.selectUserName){
				$('#approveForm #selectUserName').removeAttr('onclick');
				$('#approveForm #selectUserId').val(approveData.selectUserId);
				$('#approveForm #selectUserName').val(approveData.selectUserName);
			}else{
				$('#approveForm #selectUserName').attr('placeholder','请选择'+nextNode.nodeName);
			}
		}
		$('#approveForm #checkType').val(checkType);
		
		Flow.approveLayer && Flow.approveLayer(approveInfo);
	}});
		
  },
 writeSignature:function(el){
	popup.layerShow({url:'/yq-work/pages/flow/comm/flow-signature.jsp',maxmin:true,full:fullShow(),area:['600px','500px'],shadeClose:false,scrollbar:false,offset:'20px',data:{businessId:FlowCore.businessId},id:'writeSignature',title:'电子签名',btn:['确定','重签','取消'],yes: function(index, layero){
   	 	var img = $(".g-signatrue-body").jSignature('getData','default');
		$('#signtrue').val(img);
		$('#signatrueImg').remove();
		$(el).after("<div id='signatrueImg' style='display:inline-block;'><span class='label label-success label-outline ml-10' onclick='$(this).remove();$(\"#signtrue\").val(\"\");'>已签 X</span></div>");
		layer.close(index); 
    },btn2:function(){
		$(".g-signatrue-body").jSignature('reset');
		return false;
	},btn3:function(index){
		layer.close(index); 
	}});
		
 },
 trustLayer:function(){
	popup.layerShow({url:'/yq-work/pages/flow/comm/trust-do.jsp',full:fullShow(),area:['500px','350px'],shadeClose:false,scrollbar:false,offset:'20px',data:{businessId:FlowCore.businessId},id:'trustLayer',title:'流程委托'});
 },
 ccLayer:function(){
	popup.layerShow({url:'/yq-work/pages/flow/comm/flow-cc.jsp',full:fullShow(),area:['500px','350px'],shadeClose:false,scrollbar:false,offset:'20px',data:{businessId:FlowCore.businessId},id:'ccLayer',title:'流程抄送'});
 },
 updateLogLayer:function(){
	popup.layerShow({url:'/yq-work/pages/flow/comm/flow-revise.jsp',full:fullShow(),area:['700px','100%'],shadeClose:true,scrollbar:false,offset:'r',data:{businessId:FlowCore.businessId},id:'updateLogLayer',title:'操作日志'});
 },
 commentLayer:function(){
	popup.layerShow({url:'/yq-work/pages/flow/comm/flow-comment.jsp',full:fullShow(),area:['700px','100%'],shadeClose:true,scrollbar:false,offset:'r',data:{businessId:FlowCore.businessId},id:'commentLayer',title:'意见列表'});
 },
 lookCCLayer:function(){
	popup.layerShow({url:'/yq-work/pages/flow/comm/flow-cc-list.jsp',full:fullShow(),area:['80%','80%'],shadeClose:true,scrollbar:false,offset:'20px',data:{businessId:FlowCore.businessId},id:'lookCCLayer',title:'抄送列表'});
 },
 tips:function(el){
	var obj = $(el).find('textarea');
	var val = '';
	if(obj.length>0){
		val = obj.val();
		val = getContent(val);
	}else{
		val = $(el).text();
	}
	if(val){
		layer.tips(val,el,{tips:[1,'#5fb878'],time:10000});
	}
 },
 submitCC:function(){
	var ccIds = $("[name='ccIds']").val();
	var ccNames = $("[name='ccNames']").val();
	var ccRemark = $("[name='ccRemark']").val();
	if(ccNames==''){
		layer.msg('请选择抄送人',{icon:7});
		return;
	}
	var param = FlowCore.params;
	var data = $.extend(param,{ccIds:ccIds,ccNames:ccNames,ccRemark:ccRemark});
	ajax.remoteCall("/yq-work/servlet/flow/fun?action=submitCC",data,function(result) { 
		if(result.state == 1){
			layer.msg(result.msg,{icon:1,time:800},function(){
				layer.closeAll();
			});
		}else{
			layer.alert(result.msg,{icon: 5});
		}
	});
 },
 submitTrust:function(data){
	if (data === undefined) data = {};
	var formData = form.getJSONObject("#trustForm");
	var param = FlowCore.params;
	var postData = $.extend(param,formData,data);
	if(postData.trustUserId==''){
		layer.msg('请选择委托人',{icon:7});
		return;
	}
	if(postData.trustReason==''){
		layer.msg('请选择委托原因',{icon:7});
		return;
	}
	ajax.remoteCall("/yq-work/servlet/flow/fun?action=submitTrust",postData,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:800},function(){
					layer.closeAll();
					FlowCore.myTodoList();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
	});
 },
  flowShare:function(){
	var applyInfo = FlowCore.applyInfo;
	var url = "http://"+location.host+'/yq-work/web/flow/'+FlowCore.businessId+"#"+applyInfo.applyTitle;
	var textarea = document.createElement('textarea');
    document.body.appendChild(textarea);
    // 隐藏此输入框
    textarea.style.position = 'fixed';
    textarea.style.clip = 'rect(0 0 0 0)';
    textarea.style.top = '10px';
    // 赋值
    textarea.value = url;
    // 选中
    textarea.select();
    // 复制
    document.execCommand('copy', true);
    // 移除输入框
    document.body.removeChild(textarea);
	layer.msg('复制成功:'+url,{icon:1,offset:'20px'});
  },
  flowSaveImg:function(){
     popup.layerShow({title:'请右键点击复制图片或另存为',content:'<div style="padding:10px;text-align:center;overflow-y:scroll;height:calc(100vh - 200px)"><div id="flowImg"></div></div>',scrollbar:false,area:['80%','80%'],success:function(){
		html2canvas(document.querySelector(".ibox-content"),{}).then(function(canvas) {
		    document.querySelector("#flowImg").appendChild(canvas);
		    $('canvas').css('width','90%');
		    $('canvas').css('height','90%');
		});
     }});
  },
  flowPrint:function(){
	layer.open({title:'打印须知',offset:'20px',content:'<div><a style="text-decoration: underline;color: #366ec5;" target="_blank" href="/yq-work/doc/84558301431099998044913">点击查看打印教程</a><br>请打印前预览合适尺寸打印</div>',btn:['打印预览','取消'],yes:function(index){
		layer.close(index);
		if(Flow.flowPrint){
			Flow.flowPrint();
		}else{
			ajax.remoteCall("/yq-work/servlet/flow/fun?action=addPrintLog",FlowCore.params,function(result) { 
			if(result.state == 1){
				$('title').text('.');
				window.print();
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		 });
		}
	}});
  },
  flowLog:function(){
	popup.layerShow({url:'/yq-work/pages/flow/comm/flow-view.jsp',full:fullShow(),area:['600px','550px'],scrollbar:false,offset:'20px',data:{fkId:FlowCore.businessId},id:'lookLogLayer',title:'查看日志'});
  },
  flowFile:function(){
	popup.layerShow({url:'/yq-work/pages/common/file-view.jsp',full:fullShow(),area:['680px','400px'],scrollbar:false,offset:'20px',data:{fkId:FlowCore.businessId},id:'fileViewLayer',title:'查看文件'});
  },
  approveNode:function(){
	var flowCode = FlowCore.params.flowCode;
	popup.layerShow({type:1,url:'/yq-work/pages/flow/comm/approve-node.jsp',full:fullShow(),area:['600px','450px'],scrollbar:false,offset:'20px',data:{flowCode:flowCode},id:'approveNode',title:'流程审批节点'});
 },
 historyApply:function(){
	var applyInfo = FlowCore.applyInfo;
	var data = FlowCore.params;
	data['applyBy'] = applyInfo.applyBy;
	popup.layerShow({type:1,url:'/yq-work/pages/flow/comm/flow-history.jsp',full:fullShow(),area:['68%','100%'],scrollbar:false,offset:'r',data:data,id:'historyApply',title:'历史流程'});
 }

	
};


function checkResultLable(checkResult){
	 var json = {'0':'待审批','1':'通过','2':'拒绝'};
	 return json[checkResult]||'待审批';
}

// 字符串的驼峰格式转下划线格式，eg：helloWorld => hello_world
function hump2Underline(s) {
    return s.replace(/([A-Z])/g, '_$1').toLowerCase()
}

// JSON对象的key值转换为下划线格式
function jsonToUnderline(obj) {
    if (obj instanceof Array) {
        obj.forEach(function (v, i) {
            jsonToUnderline(v)
        })
    } else if (obj instanceof Object) {
        Object.keys(obj).forEach(function (key) {
            var newKey = hump2Underline(key)
            if (newKey !== key) {
                obj[newKey] = obj[key]
                delete obj[key]
            }
            jsonToUnderline(obj[newKey])
        })
    }
    return obj
}

function toDbField(data,modelName){
	for(var key in data){
		if(key.indexOf(modelName+".")>-1){
			var newKey = hump2Underline(key);
			var val = data[key];
			delete data[key];
			data[newKey] = val;
		}
	}
	return data;
}
function removeFieldPrefix(data,modelNames){
	if (modelName === undefined) modelNames = 'apply.,business.,extend.';
	  var array  = modelNames.split(',');
	  for(var key in data){
		for(var index in array){
			var modelName = array[index];
			if(key.indexOf(modelName)>-1){
				var newKey = key.replace(modelName,'');
				var val = data[key];
				delete data[key];
				data[newKey] = val;
			}
		}
	}
	return data;
}

function timeDiff(time1,time2){
	if(time1==''||time2==''){
		return '';
	}
    var time1 = new Date(time1);    //转换为中国标准时间
    var time2 = new Date(time2);
    var time1 = time1.getTime();    //转换为时间戳
    var time2 = time2.getTime();
    var runTime = (time2 - time1) / 1000;       //开始得出时间差,然后计算
    var year = Math.floor(runTime / 86400 / 365);       
    runTime = runTime % (86400 * 365);
    var month = Math.floor(runTime / 86400 / 30);
    runTime = runTime % (86400 * 30);
    var day = Math.floor(runTime / 86400);
    runTime = runTime % 86400;
    var hour = Math.floor(runTime / 3600);
    runTime = runTime % 3600;
    var minute = Math.floor(runTime / 60);
    runTime = runTime % 60;
    var second = runTime;
	
	var array = [];
	if(year>0){
		array.push(year+'年');
	}
	if(month>0){
		array.push(month+'月');
	}
	if(day>0){
		array.push(day+'日');
	}
	if(hour>0){
		array.push(hour+'时');
	}
	if(minute>0){
		array.push(minute+'分');
	}
	if(second>0){
		array.push(second+'秒');
	}

    return array.join('');
}

 
function getCurrentTime() {
    var date = new Date();//当前时间
    var year = date.getFullYear() //返回指定日期的年份
    var month = repair(date.getMonth() + 1);//月
    var day = repair(date.getDate());//日
    var hour = repair(date.getHours());//时
    var minute = repair(date.getMinutes());//分
    var second = repair(date.getSeconds());//秒
    var curTime = year + "-" + month + "-" + day+ " " + hour + ":" + minute + ":" + second;

	function repair(i){
	    if (i >= 0 && i <= 9) {
	        return "0" + i;
	    } else {
	        return i;
	    }
	}
    return curTime;
}


function getDiffByDay(start, end){
	/****
     * start:请假开始时间
     * end:请假结束时间
             * 计算天数，半天按0.5天计算,小于半天，1小时计0.1天，2小时计0.2天，3小时计0.3天，4小时计0.4天(1位小数)
     * ***/
    var s = Date.parse(start), e = Date.parse(end);
    //取绝对值
    var diff = Math.abs(e - s);

    var result = 0,hour = Math.floor(diff / (1000 * 60 * 60)),day = Math.floor(diff / (1000 * 60 * 60 * 24));

    result = day;
    if (day > 0){
        //去掉天数部分，仅留小时数
        hour -= day * 24;
    }
    if (hour > 4){
        //如果大于半天(5小时)
        result += 0.5;
        hour = Math.floor((diff - (day * 24 + 4) * 1000 * 60 * 60) / (1000 * 60 * 60));
    }
    if (hour > 1){
        result += hour * 0.1;
    }
    return  Math.floor(result * 100) / 100;
}

function formatMoney(num) {
   num = num.toString().replace(/\$|\,/g,'');
   if(isNaN(num)){
	   return num;
   }
   sign = (num == (num = Math.abs(num)));
   num = Math.floor(num*100+0.50000000001);
   cents = num%100;
   num = Math.floor(num/100).toString();
   if(cents<10)
   cents = "0" + cents;
   for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++)
   num = num.substring(0,num.length-(4*i+3))+','+
   num.substring(num.length-(4*i+3));
   return (((sign)?'':'-') + num + '.' + cents);
}
