package com.yunqu.work.ext.interceptor;

import javax.servlet.http.HttpServletRequest;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;

public class AuthInterceptor implements Interceptor {

	@Override
	public void intercept(Invocation inv) {
		if(inv.getController().getRequest().getRemoteUser()==null) {
			HttpServletRequest request = inv.getController().getRequest();
			String url = request.getRequestURL().toString();
			if(request.getQueryString()!=null) {
				url = url+"?"+request.getQueryString();
			}
			inv.getController().renderHtml("<script>document.write('自动登录中...');setTimeout(dd,1200);function dd(){location.href='/workbench/sso?url="+url+"'}</script>");
		}else {
			inv.invoke();
		}
	}

}
