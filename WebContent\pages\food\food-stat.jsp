<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>订餐统计</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form name="searchForm" autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-cutlery"></span> 订餐统计</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">月份</span>	
							 <input type="text" name="ym" onClick="WdatePicker({dateFmt:'yyyyMM'})" class="form-control input-sm Wdate" style="width: 90px;">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">供应商</span>	
							 <input type="text" name="supplier" class="form-control input-sm" style="width: 90px;">
					     </div>
	        		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">中晚餐</span>	
							 <select name="dinnerType" class="form-control input-sm" >
							    <option value="">--</option>
							 	<option value="2">中餐</option>
							 	<option value="3">晚餐</option>
							 </select>
					     </div>
	        		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">统计维度</span>	
							 <select id="statType" class="form-control input-sm" >
							 	<option value="1">按部门</option>
							 	<option value="2">按工号</option>
							 </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						<table class="layui-hide" id="t"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		requreLib.setplugs('wdate');
		$(function(){
			list.init1();
			$("#statType").change(function(){
				var t=$(this);
				var val=t.val();
				if(val==1){
					list.init1();
				}
				if(val==2){
					list.init2();
				}
			});
		});
		var list={
			init1:function(){
				$("#searchForm").initTable({
					mars:'FoodDao.deptStat',
					limit:100,
					id:'t',
					height:'full-90',
					totalRow:true,
					toolbar:true,
					limits:[10,30,50,100,150,200,300,500],
					cols: [[
					  {title:'序号',type:'numbers',width:50},
					  {
					    field: 'DEPTS',
						title: '部门',
						align:'center',
						totalRowText:'统计'
					  },
		              {
					    field: 'PRICE',
						title: '总费用',
						sort:true,
						align:'left',
						totalRow:true
						
					},{
					    field: 'LUNCH_PRICE',
						title: '午餐费用',
						sort:true,
						align:'center',
						totalRow:true,
						totalFormat:'¥this'
					},{
					    field: 'SUPPER_PRICE',
						title: '晚餐费用',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'COUNT',
						title: '总次数',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'LUNCH',
						title: '午餐次数',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'SUPPER',
						title: '晚餐次数',
						sort:true,
						align:'center',
						totalRow:true
					}
				 ]],done:function(res){
					 
					 
				 }}
				);
			},
			init2:function(){
				$("#searchForm").initTable({
					mars:'FoodDao.personStat',
					limit:30,
					id:'t',
					totalRow:true,
					toolbar:true,
					height:'full-200',
					limits:[10,30,50,100,150,200,300,500],
					cols: [[
					  {type:'checkbox',width:50},
					  {title:'序号',type:'numbers',width:50},
					  {
					    field: 'USERNAME',
						title: '用户名',
						align:'center'
					  },
		              {
					    field: 'DEPTS',
						title: '部门',
						align:'left'
					},
		              {
					    field: 'PRICE',
						title: '总费用',
						align:'left',
						sort:true,
						totalRow:true
					},{
					    field: 'LUNCH_PRICE',
						title: '午餐费用',
						align:'center',
						totalRow:true
					},{
					    field: 'SUPPER_PRICE',
						title: '晚餐费用',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'COUNT',
						title: '总次数',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'LUNCH',
						title: '午餐次数',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'SUPPER',
						title: '晚餐次数',
						sort:true,
						align:'center',
						totalRow:true
					}
				 ]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>