function loadInvoiceTitleList(){
	$("#custDetailForm").initTable({
		mars:'InvoiceDao.titleList',
		id:'invoiceTitleList',
		page:false,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field: 'TITLE_NAME',
				title: '发票抬头',
				align:'left'
			},{
				field: 'TAX_NO',
				title: '税号',
				align:'center'
			},{
				field: 'BANK_NAME',
				title: '开户银行',
				align:'left'
			},{
				field: 'BANK_NO',
				title: '银行账户',
				align:'center'
			},{
				field: 'ADDRESS',
				title: '开票地址',
				align:'center'
			},{
				field: 'TEL',
				title: '电话',
				align:'center'
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="editInvoiceTitle(\'{{d.TITLE_ID}}\')">编辑</a></div>'
			}
		]],done:function(){

		}});
}
function loadBusinessList(){
	var isColumnProcessed = false;
	$("#custDetailForm").initTable({
		mars:'CustDao.businessList',
		id:'businessList',
		page:false,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field: 'BUSINESS_NAME',
				title: '商机名称',
				align:'left'
			},{
				field: 'BUSINESS_TYPE',
				title: '商机类型',
				align:'left',
				width: 80,
				templet: function(row){
					return row['BUSINESS_TYPE']=='2'?'运营商机':'普通商机';
				}
			},{
				field: 'AMOUNT',
				title: '预计金额',
				align:'left',
				width: 80,
			},{
				field: 'COST_BUDGET',
				title: '预计成本',
				align:'left',
				width: 80,
			},{
				field:'POSSIBILITY',
				title:'可行性',
				width: 60,
				templet:'<div>{{d.POSSIBILITY}}%</div>'
			},{
				field:'PLATFORM_NAME',
				title:'业务平台',
				align:'left',
				width: 110,
			},{
				field: 'REMARK',
				title: '描述',
				align:'left'
			},{
				field: 'CREATE_TIME',
				title: '创建时间',
				align:'left',
				width: 140,
			},{
				field: 'SALES_BY',
				title: '销售',
				align:'center',
				width: 80,
				templet:function(row){
					return getUserName(row.SALES_BY);
				}
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="businessDetail(\'{{d.BUSINESS_ID}}\',\'{{d.CUST_ID}}\',\'{{d.CUSTOMER_NAME}}\')">查看</a></div>'
			}
		]],done:function(res){
			$("#businessCount").text("("+res.total+")");

			if(isColumnProcessed) {
				return;
			}

			var hasOperatorBusiness = false;
			if(res.data && res.data.length > 0) {
				hasOperatorBusiness = res.data.some(function(item) {
					return item.BUSINESS_TYPE == '2';
				});
			}

			if(!hasOperatorBusiness) {

				// 获取表格配置
				var tableOptions = layui.table.getOptions('businessList');

				if(tableOptions && tableOptions.cols) {
					var cols = tableOptions.cols[0];
					for(var i = 0; i < cols.length; i++) {
						if(cols[i].field === 'PLATFORM_NAME'||cols[i].field === 'BUSINESS_TYPE'||cols[i].field === 'COST_BUDGET') {
							cols[i].hide = true;
						}
					}

					isColumnProcessed = true;
					layui.table.reload('businessList', {
						cols: [cols]
					});
				}
			} else {
				isColumnProcessed = true;
			}
		}});
}
function loadInvoiceList(){
	$("#custDetailForm").initTable({
		mars:'InvoiceDao.list',
		id:'invoiceList',
		page:false,
		rowDoubleEvent(row){
			invoiceDetail(row.INVOICE_ID);
		},
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field: 'INVOICE_NO',
				title: '编号',
				align:'left',
				style:'color:#1E9FFF;cursor:pointer',
				templet:'<div><a href="javascript:;" onclick="invoiceDetail(\'{{d.INVOICE_ID}}\')">{{d.INVOICE_NO}}</a></div>'
			},{
				field: 'MARK_MONEY',
				title: '开票金额'
			},{
				field: 'MARK_DATE',
				title: '开票日期'
			},{
				field: 'INVOICE_TYPE',
				title: '发票类型'
			},{
				field: 'CONTRACT_NAME',
				title: '所属合同',
				align:'left',
				width:140,
				templet:'<div><a href="javascript:;" onclick="contractDetail2(\'{{d.CONTRACT_ID}}\',\'{{d.CUST_ID}}\')">{{d.CONTRACT_NAME}}</a></div>'
			},{
				field: 'REMARK',
				title: '备注',
				align:'center'
			}
		]],done:function(res){
			if(res.totalRow!=undefined){
				$("#invoiceCount").text("("+res.totalRow+")");
			}
			}});
}


function loadContactsList(){
	$("#custDetailForm").initTable({
		mars:'ContactsDao.list',
		id:'contactsList',
		page:false,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field: 'NAME',
				title: '姓名',
				align:'left',
				templet:'<div><a href="javascript:;" onclick="editContact(\'{{d.CONTACTS_ID}}\',0)">{{d.NAME}}</a></div>'

			},{
				field: 'MOBILE',
				title: '手机号',
				align:'left'
			},{
				field: 'EMAIL',
				title: '邮箱',
				align:'left'
			},{
				field: 'POST',
				title: '职务',
				align:'left'
			},{
				field: 'ROLE',
				title: '角色',
				align:'left'
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="editContact(\'{{d.CONTACTS_ID}}\',1)">编辑</a></div>'
			}
		]],done:function(res){
			$("#contactsCount").text("("+res.total+")");
		}});
}
function loadBuyGoodsList(){
	$("#custDetailForm").initTable({
		mars:'OrderDao.custList',
		id:'buyGoodsList',
		page:false,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field: 'ORDER_NO',
				title: '单据号',
				align:'left'
			},{
				field: 'CONTRACT_NAME',
				title: '所属合同',
				align:'left'
			},{
				field: 'SUPPLIER_NAME',
				title: '合同供应商',
				align:'left'
			},{
				field: 'TOTAL_PRICE',
				title: '含税总金额',
				align:'left'
			},{
				field:'CREATE_USER_NAME',
				title:'制单人'
			},{
				field: 'CREATE_TIME',
				title: '发起时间',
				align:'left'
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="purchaseDetail(\'{{d.ORDER_ID}}\',\'{{d.CUST_ID}}\')">详情</a></div>'
			}
		]],done:function(res){
			$("#orderCount").text("("+res.total+")");
		}});
}


function loadTeamList(){
	$("#custDetailForm").initTable({
		mars:'CustDao.teamList',
		id:'teamList',
		page:false,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field: 'USERNAME',
				title: '姓名'

			},{
				field: 'RW_AUTH',
				title: '读写权限',
				align:'left',
				templet:function(row){
					var auth = row['RW_AUTH'];
					if(auth=='0'){
						return '负责人';
					}else if(auth=='1'){
						return '只读';
					}else{
						return '读写';
					}
				}
			},{
				field: 'HT_AUTH',
				title: '合同权限',
				align:'left',
				templet:function(row){
					var auth = row['HT_AUTH'];
					if(auth=='1'){
						return '√';
					}else{
						return '×';
					}
				}
			},{
				field: 'SJ_AUTH',
				title: '商机权限',
				align:'left',
				templet:function(row){
					var auth = row['SJ_AUTH'];
					if(auth=='1'){
						return '√';
					}else{
						return '×';
					}
				}
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="removeTeamUser(\'{{d.CUST_ID}}\',\'{{d.USER_ID}}\')">移除</a></div>'
			}
		]],done:function(res){
			$("#teamCount").text("("+res.total+")");
		}});
}
function loadContractList(){
	$("#custDetailForm").initTable({
		mars:'ProjectContractDao.custContractList',
		id:'contractList',
		page:true,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field: 'CONTRACT_NAME',
				title: '合同名称'
			},{
				field: 'CONTRACT_NO',
				title: '合同号',
				width:100
			},{
				field: 'CREATE_TIME',
				title: '创建时间',
				width:120
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">查看</a></div>'
			}
		]],done:function(res){
			$("#contractCount").text("("+res.totalRow+")");
		}});
}
function loadReviewList(){
	$("#custDetailForm").initTable({
		mars:'ReviewDao.custReview',
		id:'reviewList',
		page:false,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field: 'APPLY_TITLE',
				title: '合同名称'
			},{
				field: 'APPLY_NO',
				title: '评审编号',
				width:130
			},{
				field: 'APPLY_TIME',
				title: '发起时间',
				width:140
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="reviewDetail(\'{{d.APPLY_ID}}\')">查看</a></div>'
			}
		]],done:function(res){
			$("#reviewCount").text("("+res.total+")");
		}});
}

function loadPriceList(){
	$("#custDetailForm").initTable({
		mars:'CustOperateDao.salePriceList',
		id:'salePriceList',
		page:false,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field:'',
				title:'操作',
				width:100,
				align: "center",
				templet:function(d){
					var html = '<a href="javascript:;" onclick="editPrice(\''+d.PRICE_ID+'\')">编辑</a>';
					html += ' <a href="javascript:;" onclick="deletePrice(\''+d.PRICE_ID+'\')">删除</a>';
					return html;
				}
			},{
				field: 'PLATFORM_NAME',
				title: '业务平台 ',
				width:80
			},{
				field: 'PRODUCT_NAME',
				title: '产品名称',
				minWidth:100,
			},{
				field: 'PRICE_TYPE',
				title: '价格类型',
				width:80
			},{
				field: 'PRICE_UNIT',
				title: '价格单位',
				width:80
			},{
				field: 'SALES_PRICE',
				title: '销售价格',
				width:80
			},{
				field: 'SALE_TAX_RATE',
				title: '销售税率%',
				width:80
			},{
				field: 'SUPPLIER_PRICE',
				title: '供应商价格',
				width:80
			},{
				field: 'BUY_TAX_RATE',
				title: '采购税率%',
				width:80
			},{
				field: 'SUPPLIER_NAME',
				title: '供应商',
				width:100,
				templet:'<div><a href="javascript:;" onclick="supplierDetail(\'{{d.SUPPLIER_ID}}\')">{{d.SUPPLIER_NAME}}</a></div>'
			},{
				field: 'PAYMENT_METHOD',
				title: '付款方式',
				width:80
			},{
				field: 'REMARK',
				title: '备注',
				width:80
			},{
				field: 'STATUS',
				title: '状态',
				width:80,
				align: 'center',
				templet: function(row){
					return row.STATUS=='0'?'正常':'停用';
				}
			}
		]],done:function(res){

		}});
}

function loadCostList(){
	$("#custDetailForm").initTable({
		mars:'CustOperateDao.costList',
		id:'costList',
		page:false,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field:'',
				title:'操作',
				width:100,
				align: "center",
				templet:function(d) {
					var html = '<a href="javascript:;" onclick="editCost(\'' + d.COST_ID + '\',\'' + d.RELATE_TYPE + '\')">编辑</a>';
					html += ' <a href="javascript:;" onclick="deleteCost(\'' + d.COST_ID + '\')">删除</a>';
					return html;
				}
			}, {
				field: 'DATE_ID',
				title: '成本日期',
				width: 100
			},{
				field: 'CONTRACT_NAME',
				title: '合同名称',
				width:150
			},{
				field: 'PLATFORM_NAME',
				title: '业务平台',
				width:100
			},{
				field: 'COST_TYPE',
				title: '成本类型',
				width:80
			},{
				field: 'COST_NAME',
				title: '成本名称',
				width:150
			},{
				field: 'RELATE_TYPE',
				title: '成本来源',
				width:80,
				align: 'center',
				templet: function(row){
					var types = ['其他','采购订单','个人报销'];
					return types[row.RELATE_TYPE] || '其他';
				}
			},{
				field: 'COST_OWNER',
				title: '成本归属',
				width:80,
				align: 'center',
				templet: function(row){
					var types = ['平台成本','客户成本','其他'];
					return types[row.COST_OWNER] || '其他';
				}
			},{
				field: 'UNIT_PRICE',
				title: '单价',
				width:80
			},{
				field: 'NUMBER',
				title: '数量',
				width:80
			},{
				field: 'TOTAL_AMOUNT',
				title: '总金额',
				width:80
			},{
				field: 'TAX_RATE',
				title: '税率%',
				width:80
			},{
				field: 'NO_TAX_AMOUNT',
				title: '不含税金额',
				width:100
			},{
				field: 'ORDER_NO',
				title: '采购订单号',
				width:120,
				templet:'<div><a href="javascript:;" onclick="purchaseDetail(\'{{d.ORDER_ID}}\',\'{{d.CUST_ID}}\')">{{d.ORDER_NO}}</a></div>'
			},{
				field: 'ORDER_TIME',
				title: '采购下单时间',
				width:120
			},{
				field: 'PAY_TIME',
				title: '付款时间',
				width:120
			},{
				field: 'SUPPLIER_NAME',
				title: '供应商',
				width:120,
				templet:'<div><a href="javascript:;" onclick="supplierDetail(\'{{d.SUPPLIER_ID}}\')">{{d.SUPPLIER_NAME}}</a></div>'
			},{
				field: 'BX_APPLY_ID',
				title: '报销流程',
				width:100,
				templet: function(row){
					return row.BX_APPLY_ID ? '<a href="javascript:;" onclick="bxDetail(\''+row.BX_APPLY_ID+'\')">查看流程</a>' : '';
				}
			},{
				field: 'REMARK',
				title: '备注',
				width:150
			}
		]],done:function(res){

		}});
}

function loadPlatformOrderList(){
	$("#custDetailForm").initTable({
		mars:'PlatformOrderDao.orderPageList',
		id:'platformOrderList',
		page:true,
		cols: [[
			{
				type: 'numbers',
				title: '序号',
				align:'left'
			},{
				field: '',
				title: '操作',
				width: 100,
				align: 'center',
				templet: function(d){
					var html = '<a href="javascript:;" onclick="editPlatformOrder(\''+d.ORDER_ID+'\')">编辑</a>';
					html += ' <a href="javascript:;" onclick="deletePlatformOrder(\''+d.ORDER_ID+'\')">删除</a>';
					return html;
				}
			},{
				field: 'PLATFORM_NAME',
				title: '平台名称',
				width:150
			},{
				field: 'SALES_BY_NAME',
				title: '销售',
				align: 'center',
				width:80
			},{
				field: 'CONTRACT_NAME',
				title: '合同名称',
				width:150,
				templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_NAME}}</a></div>'
			},{
				field: 'BUSINESS_NAME',
				title: '商机名称',
				width:150
			},{
				field: 'ORDER_STATUS',
				title: '订购状态',
				width:80,
				align: 'center',
				templet: function(d){
					var status = ['正常','暂停','终止'];
					return status[d.ORDER_STATUS] || '未知';
				}
			},{
				field: 'ORDER_AMOUNT',
				title: '订购金额',
				width:100,
				align: 'center'
			},{
				field: 'START_DATE',
				title: '开始日期',
				width:100,
				align: 'center'
			},{
				field: 'END_DATE',
				title: '结束日期',
				width:100,
				align: 'center'
			}
		]],done:function(res){
			if(res.totalRow!=undefined){
				$("#orderCount").text("("+res.totalRow+")");
			}
		}});
}
