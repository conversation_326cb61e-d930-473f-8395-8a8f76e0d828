<%@ page language="java" contentType="text/html;charset=UTF-8"%>
 <table class="table table-vzebra">
  		<tr>
  			<td style="width: 120px;">标题</td>
  			<td>
  				<input type="text" data-rules="required" class="form-control input-sm" data-mars="BxDao.bxTitle" name="flow.BX_TITLE"/>
  			</td>
  			<td style="width: 120px;">单号</td>
  			<td>
  				<input type="text" class="form-control input-sm" data-mars="BxDao.bxNo" name="flow.BX_NO"/>
  			</td>
  		</tr>
  		<tr>
  			<td class="required">姓名</td>
  			<td>
  				<input type="text" data-rules="required" value="${staffInfo.userName}${staffInfo.staffNo}" readonly="readonly" class="form-control input-sm" name="flow.APPLY_USER_NAME"/>
  			</td>
  			<td class="required">部门</td>
  			<td>
  				<select class="form-control" data-rules="required" data-value="${staffInfo.deptId}" name="flow.DEPT_ID" data-mars="EhrDao.allDept">
  					<option value="">请选择</option>
  				</select>
  			</td>
  		</tr>
  		<tr>
  			<td class="required">费用模板</td>
  			<td>
  				<select data-rules="required" class="form-control input-sm" name="flow.BX_TPL" onchange="getBxTpl(this.value)">
  					<option value="">----</option>
  					<option value="借款">借款</option>
  					<option value="职能模板">职能模板</option>
  					<option value="营销模板">营销模板</option>
  					<option value="工程(研发)模板">工程(研发)模板</option>
  				</select>
  			</td>
  			<td>报销币种</td>
  			<td>
  				<select data-rules="required" class="form-control input-sm" name="">
  					<option value="人民币">人民币</option>
  				</select>
  			</td>
  		</tr>
  		<tr>
  			<td>说明</td>
  			<td colspan="3">
				<textarea style="height: 60px;" class="form-control input-sm" name="flow.APPLY_DESC"></textarea>
  			</td>
  		</tr>
  		
  		<tr>
  			<td>付款类型</td>
  			<td>
  				<select data-rules="required" class="form-control input-sm" name="x" onchange="selectPaymentType(this.value)">
  					<option value="个人付款">个人付款</option>
  					<option value="对公付款">对公付款</option>
  				</select>
  			</td>
  			<td>冲帐金额</td>
  			<td>
  				<input type="number" data-rules="required" placeholder="如无借款请填0" class="form-control input-sm" name=""/>
  			</td>
  		</tr>
  		<tr id="companyPaymentInfo" style="display: none;">
  			<td>对公付款信息</td>
  			<td colspan="3">
			 	<input class="form-control input-sm" placeholder="公司名称"  name="" style="width: 33%;display: inline-block;">
			 	<input class="form-control input-sm" placeholder="开户行"  name="" style="width: 33%;display: inline-block;">
			 	<input class="form-control input-sm" placeholder="开户账号" name="" style="width: 33.3%;display: inline-block;">
  			</td>
  		</tr>
  		<tr>
  			<td>合计金额</td>
  			<td>
  				<input type="number" data-rules="required" class="form-control input-sm" name="flow.BX_MONEY"/>
  			</td>
  			<td>说明</td>
  			<td>
  				每月27号关闭借款以外的费用报销申请
  			</td>
  		</tr>
  		<tr>
  			<td style="vertical-align: top;">附件</td>
  			<td colspan="3">
				<a onclick="$('#localfile').click();" href="javascript:;">+上传</a><br>
            	<div id="fileList" style="display: inline-block;" data-template="template-files" data-mars="FileDao.fileList"></div>
  			</td>
  		</tr>
  	</table>
  	
  	
<script id="template-files" type="text/x-jsrender">
	{{if data.length==0}}

	{{/if}}
	{{for data}}
		{{:#index+1}}.{{:CREATE_NAME}}于{{:CREATE_TIME}}上传<a style="color:#20a0ff;" href="/yq-work/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank">{{:FILE_NAME}}</a>
		<br>
	{{/for}}
</script>
<script>

	var uploadFile = function(){
		var randomId = $("#randomId").val();
		var fkId = Bx.bxId||randomId;
		easyUploadFile({callback:'callback',fkId:fkId,source:'bx',formId:'fileForm',fileId:'localfile'});
	}

	var callback = function(data){
		var randomId = $("#randomId").val();
		var fkId = Bx.bxId||randomId;
		$("#fileList").render({data:{fkId:fkId}});
	}
	
	function selectPaymentType(val){
		if(val=='对公付款'){
			$('#companyPaymentInfo').show();
		}else{
			$('#companyPaymentInfo').hide();
		}
	}
	
</script>


