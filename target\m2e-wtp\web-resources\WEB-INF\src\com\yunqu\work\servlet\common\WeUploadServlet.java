package com.yunqu.work.servlet.common;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.easitline.common.core.Globals;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
/**
 * wandEditor文件上传辅助类
 *
 */
@WebServlet(urlPatterns = { "/servlet/weUpload" })
@MultipartConfig(maxFileSize=50*1024*1024)
public class WeUploadServlet extends AppBaseServlet{
	private static final long serialVersionUID = 1L;
	
	public JSONObject actionForUpload(){
		HttpServletRequest request=getRequest();
		JSONObject result=new JSONObject();
		result.put("errno",403);
		try {
			Collection<Part> fileList=request.getParts();
			if(fileList==null||fileList.size()<=0){return result;}
			List<String> fileResultList=new ArrayList<String>();
			for (Part part:fileList) {
				if(part.getSubmittedFileName()==null){continue;}
				InputStream is = part.getInputStream();  
				String filename = new String(getFilename(part).getBytes(), "UTF-8"); 
				if(StringUtils.isBlank(filename)){
					part.delete();
					continue;
				}
				String fileId=RandomKit.randomStr();
				String fileDir=FileKit.getFileDir();
				File file = new File(getRootDir()+fileDir);  
				if (!file.exists()) {  
					file.mkdirs();
				}  
				String newFileName=getNewFileName(fileId,filename);
				String urlPath=fileDir+newFileName;
				
				fileResultList.add(getFileURL(urlPath));
				File newFile=new File(file +File.separator + newFileName);
				FileOutputStream fos = new FileOutputStream(newFile);
				byte[] buf = new byte[1024];  
				while (is.read(buf) != -1) {  
					fos.write(buf);  
				}  
				fos.flush();  
				fos.close();  
				is.close();  
				
				part.delete();// 删除临时文件
				
			}
			
			result.put("data",fileResultList );
			result.put("errno",0);
			return result;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.put("errno",500);
			return result;
	    }
	}
	public static  String getFileURL(String filePath){
		 if(filePath.toLowerCase().startsWith("http")) return filePath;
		 String path="/yq-work/files?url=/wangEditor/"+ filePath;
		 return path.replaceAll("//", "/");
		 
	}
	private String getNewFileName(String id,String oldFileName) {
		String filename = id+FileKit.getHouzui(oldFileName);
		return filename;
	}
	 private String getFilename(Part part) {  
	        String contentDispositionHeader = part.getHeader("content-disposition");  
	        String[] elements = contentDispositionHeader.split(";");  
	        for (String element : elements) {  
	            if (element.trim().startsWith("filename")) {  
	                return element.substring(element.indexOf('=') + 1).trim().replace("\"", "");  
	            }  
	        }  
	        return null;  
	}
    public static String getRootDir() {
    	String basePath = AppContext.getContext(Constants.APP_NAME).getProperty("basePath", "/home/<USER>/");
		String saveDirectory = basePath + File.separator + "files/wangEditor";
		return saveDirectory;
    }
}
