package com.yunqu.work.model;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.easitline.common.db.EasyRowMapper;


public class FlowApplyRowMapper implements EasyRowMapper<FlowApplyModel> {

	@SuppressWarnings("unchecked")
	@Override
	public FlowApplyModel mapRow(ResultSet rs, int rowNum) {
		FlowApplyModel vo = new FlowApplyModel();
		try {
			vo.setApplyId(rs.getString("APPLY_ID"));
			vo.setApplyTitle(rs.getString("APPLY_TITLE"));
			vo.setApplyNo(rs.getString("APPLY_NO"));
			vo.setApplyBy(rs.getString("APPLY_BY"));
			vo.setApplyState(rs.getInt("APPLY_STATE"));
			vo.setApplyName(rs.getString("APPLY_NAME"));
			vo.setApplyTime(rs.getString("APPLY_TIME"));
			vo.setApplyRemark(rs.getString("APPLY_REMARK"));
			vo.setApplyLevel(rs.getString("APPLY_LEVEL"));
			vo.setFlowCode(rs.getString("FLOW_CODE"));
			vo.setFlowName(rs.getString("FLOW_NAME"));
			vo.setRelatedFlow(rs.getString("RELATED_FLOW"));
			vo.setApproverField(rs.getString("APPROVER_FIELD"));
			vo.setDeptId(rs.getString("DEPT_ID"));
			vo.setDeptName(rs.getString("DEPT_NAME"));
			vo.setNextNodeId(rs.getString("NEXT_NODE_ID"));
			vo.setNextResultId(rs.getString("NEXT_RESULT_ID"));
			vo.setFkId(rs.getString("FK_ID"));
			
			Map<String,String> data = new HashMap<String, String>();
			for(int i=1;i<=20;i++) {
				data.put("data"+i, rs.getString("DATA"+i));
			}
			vo.setData(data);
			
			FlowModel flowModel = new FlowModel();
			flowModel.setFlowName(vo.getFlowName());
			flowModel.setFlowCode(vo.getFlowCode());
			flowModel.setStartNodeName(rs.getString("START_NODE_NAME"));
			flowModel.setXlCheckSkip(rs.getInt("XL_CHECK_SKIP"));
			flowModel.setRepeatCheckSkip(rs.getInt("REPEAT_CHECK_SKIP"));
			vo.setFlow(flowModel);
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
