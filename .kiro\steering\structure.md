# Project Structure

## Directory Organization

### Source Code (`src/com/yunqu/work/`)
```
src/com/yunqu/work/
├── ai/                 # AI integration services
│   ├── Bigmodel.java         # AI model interface
│   ├── GLM4Client.java       # GLM4 language model client
│   └── SiliconCloudAI.java   # SiliconCloud AI service
├── base/              # Base classes and application constants
│   ├── AppBaseModel.java     # Base model class
│   ├── AppBaseService.java   # Base service class
│   ├── AppBaseServlet.java   # Base servlet class
│   ├── BaseController.java   # Base controller
│   ├── Constants.java        # Application constants
│   └── FlowConstants.java    # Workflow constants
├── dao/               # Data Access Objects organized by module
│   ├── bx/           # 报销 (Expense reimbursement)
│   ├── contract/     # Contract management
│   ├── cust/         # Customer management
│   ├── devops/       # DevOps operations
│   ├── ehr/          # Human Resources
│   ├── erp/          # Enterprise Resource Planning
│   ├── flow/         # Workflow engine
│   ├── other/        # Miscellaneous DAOs
│   ├── platform/     # Platform management
│   └── project/      # Project management
├── ext/              # Framework extensions and configurations
│   ├── handler/      # Custom request handlers
│   ├── interceptor/  # Request/response interceptors
│   ├── oauth/        # OAuth authentication providers
│   └── AppWebConfig.java     # Main web configuration
├── model/            # Data models and database mappers
│   ├── rowmapper/    # Custom result set mappers
│   └── *Model.java   # Entity models (Project, Staff, etc.)
├── service/          # Business logic services
│   └── *Service.java # Service classes for each module
├── servlet/          # Web controllers organized by module
│   ├── common/       # Common/shared servlets
│   ├── crm/          # CRM-related servlets
│   ├── devops/       # DevOps management servlets
│   ├── ehr/          # HR management servlets
│   ├── erp/          # ERP-related servlets
│   ├── flow/         # Workflow servlets
│   ├── platform/     # Platform management servlets
│   ├── tools/        # Utility tool servlets
│   ├── work/         # Work management servlets
│   └── wx/           # WeChat integration servlets
└── utils/            # Utility classes and helpers
    ├── exmail/       # Email utilities
    └── *Utils.java   # Various utility classes
```

### Web Content (`WebContent/`)
```
WebContent/
├── META-INF/         # Application metadata and configurations
│   ├── script/       # Database scripts
│   ├── appconfig.xml # Application configuration
│   ├── appdict.xml   # Data dictionary
│   ├── appinfo.xml   # Application information
│   ├── appresource.xml # Resource configuration
│   └── appsql.xml    # SQL configurations
├── WEB-INF/          # Web application configuration
│   ├── lib/          # JAR dependencies
│   ├── tag/          # Custom JSP tags
│   └── web.xml       # Web application descriptor
├── pages/            # JSP pages organized by functional module
│   ├── affair/       # 事务管理 (Affairs management)
│   ├── common/       # Common/shared pages
│   ├── crm/          # CRM interface pages
│   ├── doc/          # Document management pages
│   ├── ehr/          # HR management pages
│   ├── erp/          # ERP interface pages
│   ├── excel/        # Excel processing pages
│   ├── flow/         # Workflow pages
│   ├── kanban/       # Kanban board pages
│   ├── project/      # Project management pages
│   ├── weekly/       # Weekly report pages
│   └── index.jsp     # Main application entry
├── static/           # Static web resources
│   ├── css/          # Stylesheets
│   ├── images/       # Image assets
│   ├── js/           # JavaScript files
│   ├── lib/          # Third-party JavaScript libraries
│   ├── module/       # Custom JavaScript modules
│   └── zui/          # ZUI framework files
├── tpl/              # Templates for emails and documents
│   ├── bx/           # Expense report templates
│   └── email/        # Email templates
├── wx/               # WeChat-specific pages and integration
└── index.jsp         # Application main entry point
```

### Configuration (`resource/`)
```
resource/
├── all.sql           # Complete database schema
├── config.txt        # Main application configuration
├── ehcache.xml       # EhCache configuration
├── kdwebapi.properties # Kingdee ERP API configuration
├── log4j.properties  # Log4j logging configuration
└── oauth.properties  # OAuth authentication configuration
```

## Naming Conventions

### Java Classes
- **Models**: `*Model.java` (e.g., `ProjectModel.java`, `StaffModel.java`)
- **Services**: `*Service.java` (e.g., `ProjectService.java`, `FlowService.java`)
- **Servlets**: `*Servlet.java` (e.g., `ProjectServlet.java`, `WorkbenchServlet.java`)
- **DAOs**: `*Dao.java` (e.g., `HomeDao.java`)
- **Utils**: `*Utils.java` or `*Util.java` (e.g., `DateUtils.java`, `FlowUtils.java`)
- **Base Classes**: `Base*` or `App*` prefix (e.g., `AppBaseModel`, `BaseController`)

### Database Mapping
- **Row Mappers**: `*RowMapper.java` for custom result set mapping
- **Records**: `*Record.java` for data transfer objects
- **Models**: Extend `AppBaseModel` for ActiveRecord pattern

### Web Resources
- **Pages**: Organized by functional module in `pages/` directory
- **Static Files**: Organized by type (`css/`, `js/`, `images/`) in `static/`
- **Templates**: Email and document templates in `tpl/`

## Architecture Patterns

### MVC Structure
- **Controllers**: Servlets in `servlet/` package handle HTTP requests
- **Models**: Data models in `model/` package represent business entities
- **Views**: JSP pages in `WebContent/pages/` render user interface

### Service Layer Pattern
- Business logic encapsulated in service classes (`*Service.java`)
- Services use DAOs for data access operations
- Services are dependency-injected into servlets/controllers
- Transaction management handled at service layer

### Data Access Patterns
- **JFinal ActiveRecord**: For simple CRUD operations on models
- **Custom DAOs**: For complex queries and business-specific data operations
- **Row Mappers**: For custom result set mapping and data transformation
- **Base Classes**: `AppBaseModel`, `AppBaseService` provide common functionality

## Module Organization
The codebase follows a **modular architecture** organized by business domains:

### Core Business Modules
- **CRM**: Customer relationship management and sales tracking
- **ERP**: Enterprise resource planning and financial management
- **EHR**: Human resources and employee management
- **Flow**: Workflow engine and approval processes
- **Project**: Project management and task tracking
- **DevOps**: Development operations and system management
- **Platform**: System platform and administrative functions

### Supporting Modules
- **Common**: Shared functionality across modules
- **Tools**: Utility tools and helper functions
- **Work**: General work management features
- **WeChat (wx)**: WeChat and Enterprise WeChat integration

## Key Architectural Principles
- **Separation of Concerns**: Clear separation between web, service, and data layers
- **Modular Design**: Business functionality organized by domain modules
- **Configuration-Driven**: Extensive use of configuration files for flexibility
- **Integration-Ready**: Built-in support for third-party integrations (WeChat, Kingdee, etc.)
- **Extensible**: Plugin architecture through extensions and interceptors