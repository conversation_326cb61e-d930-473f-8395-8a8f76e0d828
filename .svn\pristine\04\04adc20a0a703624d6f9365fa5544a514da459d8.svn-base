package com.yunqu.work.dao.devops;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.VersionModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="VersionDao")
public class VersionDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		VersionModel model=new VersionModel();
		model.setColumns(getParam("version"));
		if(StringUtils.notBlank(model.getVersionId())){
			LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getVersionId());
		}
		JSONObject result = queryForRecord(model);
		try {
			List<JSONObject> list = this.getQuery().queryForList("select t2.module_name,t1.* from yq_project_module_version t1,yq_project_module t2,yq_ops_version t3 where t1.version_id = t3.version_id and t2.module_id = t1.module_id and t1.version_id = ?",new Object[] {model.getPrimaryValue()},new JSONMapperImpl());
			result.put("modules",list);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		return result;
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select version_id,version_name from yq_ops_version");
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from yq_ops_version where 1=1");
		sql.appendLike(param.getString("versionName"),"and version_name like ?");
		sql.appendLike(param.getString("versionTitle"),"and version_title like ?");
		sql.append(param.getString("groupId"),"and GROUP_ID = ?");
		sql.append(param.getString("appId"),"and app_id = ?");
		sql.append(param.getString("projectId"),"and project_id = ?");
		sql.append(param.getString("serviceId"),"and service_id = ?");
		if(!isSuperUser()) {
			sql.append("and ((");
			sql.append(getUserId(),"creator = ?");
			sql.append(")");
			sql.append("or (");
			sql.append(getUserId(),"FIND_IN_SET(?,recipient_id)");
			sql.append("))");
		}
		sql.append("order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
