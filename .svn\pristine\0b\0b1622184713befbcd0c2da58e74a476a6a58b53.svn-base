<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div class="layui-table-tool">
	<button class="btn btn-sm btn-default" onclick="addTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增列</button>
	<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
	<button class="btn btn-sm btn-default ml-10" onclick="copyTr();" type="button"><i class="layui-icon layui-icon-template"></i> 复制</button>
	<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
	<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
</div>
<div class="layui-table-box" style="min-height: 300px;">
 <table class="layui-table" style="margin-top: -1px;">
   <thead>
    <tr>
      <th></th>
      <th>日期</th>
      <th>费用类型(内部)</th>
      <th>费用说明(内部)</th>
      <th>金额</th>
      <th>发票类型</th>
      <th>部门</th>
      <th>项目号</th>
      <th>产品线</th>
      <th>税率(%)</th>
      <th>税金</th>
      <th>金额</th>
    </tr> 
  </thead>
  <tbody>
  	<c:forEach var="item" begin="1" end="20">
	    <tr id="item_${item}" <c:if test="${item>5}">data-hide-tr="1" style="display:none;"</c:if>>
	      <td>
	      	<input type="hidden" name="orderIndex_${item}"/>
	      	<input type="hidden" name="itemId_${item}"/>
	      	<input type="hidden" value="${item}" name="itemIndex"/>
	        <label class="checkbox checkbox-inline checkbox-success">
		      	 <input type="checkbox" name="ids" value="${item}"><span></span>
	        </label>
	      </td>
	      <td>
	      	    <input type="text" data-mars="CommonDao.today" data-laydate="{type:'date'}" style="width: 110px;" name="bxDate_${item}" class="form-control input-sm"/>
	      </td>
	      <td>
	     	   <select data-rules="required" class="form-control input-sm feeType" name="feeType_${item}" onchange="getInvoiceType(this.value,'${item}')">
	 			  <option value=""></option>
	 		   </select>
	      </td>
	      <td>
	      		<textarea style="height: 40px;resize: none;" class="form-control input-sm" name="feeDesc_${item}"></textarea>
	      </td>
	      <td>
	      		<input type="number" style="width: 80px;" class="form-control input-sm" name="amount_${item}"/>
	      </td>
	      <td>
	     	   <select data-rules="required" style="width: 110px;" class="form-control input-sm invoiceType" name="invoiceType_${item}">
	
	 		   </select>
	      </td>
	      <td>
	           <select data-rules="required" style="width: 110px;" class="form-control input-sm" data-value="${staffInfo.deptId}"  name="deptId_${item}" data-mars="EhrDao.allDept">
	           
	           </select>
	      </td>
	      <td>
	        	<input type="hidden" name="contractNo_${item}"/>
	        	<input type="hidden" name="contractId_${item}"/>
	        	<input type="text" style="width: 110px;" readonly="readonly" id="contractName_${item}" data-id="${item}" name="contractName_${item}" class="form-control input-sm selectContract"/>
	      </td>
	      <td>
	        	<select style="width: 110px;" class="form-control input-sm" name="productLine_${item}" data-mars="YqAdmin.DictDao.select('Y001')">
	        		<option value="">--</option>
	        	</select>
	      </td>
	      <td>
	        	<select style="width: 70px;" class="form-control input-sm" name="taxRate_${item}">
	        		<option value="0.00">0.00</option>
	        		<option value="0.01">0.01</option>
	        		<option value="0.015">0.015</option>
	        		<option value="0.03">0.03</option>
	        		<option value="0.05">0.05</option>
	        		<option value="0.06">0.06</option>
	        		<option value="0.07">0.07</option>
	        		<option value="0.09">0.09</option>
	        		<option value="0.10">0.10</option>
	        		<option value="0.11">0.11</option>
	        		<option value="0.13">0.13</option>
	        		<option value="0.16">0.16</option>
	        		<option value="0.17">0.17</option>
	        	</select>
	      </td>
	      <td>
	        	<input type="number" style="width: 70px;" value="0.00" class="form-control input-sm" name="taxes_${item}"/>
	      </td>
	      <td>
	        	<input type="text" style="width: 70px;" class="form-control input-sm" name="rAmount_${item}"/>
	      </td>
	    </tr>
  	</c:forEach>
  </tbody>	
</table>
</div>
<br>	
<br>	
<br>	
<br>	
  	
<script id="tpl1-files" type="text/x-jsrender">
	
</script>
<script>
	
	var dict = {
		"借款":"用于办理职工借款,对公费用预付款,对公往来预付款,备用金借款,租赁押金借款,其他押金借款,投标保证金,合同保证金,诉讼保证金,购买标书借款,中标服务费预付款",	
		"职能模板":"职工福利费,手机费,办公费,差旅费-机票费,差旅费-车船费,差旅费-市内交通费,差旅费-住宿费,差旅费-出差补助,差旅费-其他,交通费,业务招待费,培训费,电话费（座机）,网络使用费,年费及顾问咨询费,审计及信息公告费,法律服务费,项目协作费/外包服务,会务费,广告及宣传费,物业管理费,租赁费,水费,电费,维修费,运杂费,零星物料,其他,协会及资质费,汽车费用,招聘费,人才代理费,保险费,在建工程,固定资产",
		"营销模板":"项目费用,商务费用,职工福利费,手机费,办公费,差旅费-机票费,差旅费-车船费,差旅费-市内交通费,差旅费-住宿费,差旅费-出差补助,差旅费-其他,交通费,业务招待费,培训费,电话费（座机）,网络使用费,审计及信息公告费,法律服务费,项目协作费/外包费用,会务费,广告及宣传费,租赁费,维修费,运杂费,零星物料,合同招投标费用,顾问咨询费,协会及资质费,汽车费用,招聘费,人才代理费,保险费,固定资产",
		"工程(研发)模板":"项目费用,验收费,赔补费,工程监理费,零星工程施工,零星物料,差旅费-机票费,差旅费-车船费,差旅费-市内交通费,差旅费-住宿费,差旅费-出差补助,差旅费-其他,交通费,培训费,其他,项目协作费/外包劳务,职工福利费,办公费,法律服务费,广告及宣传费,会务费,手机费,业务招待费,运杂费,电话费（座机）,网络使用费,审计及信息公告费,租赁费,水费,电费,维修费,汽车费用,招聘费,人才代理费,调试检测费,保险费,顾问咨询费,协会及资质费,中标服务费,固定资产"
	};
	var feeTypes = {
			"商务费用":"差旅费-机票费,差旅费-车船费,差旅费-市内交通费,差旅费-住宿费,差旅费-其他,交通费,会务费",
			"项目费用":"差旅费-机票费,办公费,差旅费-车船费,差旅费-市内交通费,差旅费-住宿费,差旅费-出差补助,差旅费-其他,交通费,业务招待费,会务费,广告及宣传费"
	};

	function getFeeTypeOptions(val){
		$('.feeType').html("<option value=''>----</option>");
		var tempHtml = [];
		var data = dict[val].split(',');
		for(var index in data){ 
			var val = data[index];
			tempHtml.push("<option value='"+val+"'>"+val+"</option>");
		}
		$('.feeType').append(tempHtml.join(''));
	}
	
	$(function(){
		renderDate('#flowForm');
	});
	
	function getBxTpl(val){
		getFeeTypeOptions(val);
	}
	
	function getInvoiceType(feeType,id){
		var items  = feeTypes[feeType]||'';
		if(items){
			var tempHtml = [];
			var data = items.split(',');
			for(var index in data){ 
				var val = data[index];
				tempHtml.push("<option value='"+val+"'>"+val+"</option>");
			}
			$('#item_'+id).find('.invoiceType').html(tempHtml.join(''));
		}else{
			$('#item_'+id).find('.invoiceType').html("<option value='"+feeType+"'>"+feeType+"</option>");
		}
	}
	
	function addTr(){
		var obj  = $("[data-hide-tr]").first();
		if(obj.length==0){
			layer.msg('不能再新增啦',{icon:7,shade: 0.1});
			return;
		}
		obj.removeAttr("data-hide-tr");
		obj.show();
		
		obj.find("select").val(obj.prev().find("select:eq(0)").val());
	}
	
	function delTr(){
		layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
			layer.close(index);
			$("input[name='ids']:checked").each(function(){
				$('#item_'+this.value).remove();
			});
		});
	}
	
	function copyTr(){
		
	}
	
	function upTr(){
		var obj = $("input[name='ids']:checked");
		if(obj.length==1){
			var id = obj.val();
			var prevTR  = $('#item_'+id).prev();
			if (prevTR.length > 0) {
				prevTR.insertAfter($('#item_'+id));
			}
		}
	}
	function downTr(){
		var obj = $("input[name='ids']:checked");
		if(obj.length==1){
			var id = obj.val();
			var nextTR  = $('#item_'+id).next();
			if (nextTR.length > 0) {
				nextTR.insertBefore($('#item_'+id));
			}
		}
	}
	
	function initSelectCust(){
		layui.use(['form','tableSelect'], function(){
			var tableSelect = layui.tableSelect;
			$('.selectContract').each(function(){
				var t = $(this);
				tableSelect.render({
					elem: '#contractName_'+t.data('id'),
					searchKey: 'contractName',
					checkedKey: 'CONTRACT_NO',
					page:true,
					searchPlaceholder: '请输入合同名称活编号',
					table: {
						mars: 'ProjectContractDao.contractSelect',
						cols: [[
						        { type: 'radio' },
						        { field: 'CONTRACT_NO',title: '编号'},
						        { field: 'CONTRACT_NAME', title: '名称', width: 320 }
						        ]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						var nos = [];
						layui.each(data.data, function (index, item) {
							names.push(item.CONTRACT_NAME)
							ids.push(item.CONTRACT_ID)
							nos.push(item.CONTRACT_NO)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
						elem.prev().prev().val(nos.join(","));
					},
					clear:function(elem){
						elem.prev().val("");
					}
				});
				
			});
		});
	}
</script>


