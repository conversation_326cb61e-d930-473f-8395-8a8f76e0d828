<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>JFinal-weixin支付测试</title>
  </head>
<body>
  JFinal-weixin支付测试<br>
  微信支付v3官方文档地址：https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=7_7&index=6
</body>
<script type="text/javascript">
  
	if (typeof WeixinJSBridge == "undefined"){
	   if( document.addEventListener ){
	       document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);
	   }else if (document.attachEvent){
	       document.attachEvent('WeixinJSBridgeReady', onBridgeReady); 
	       document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);
	   }
	}else{
	   onBridgeReady();
	}
	
	function onBridgeReady(){
		   WeixinJSBridge.invoke(
		      'getBrandWCPayRequest', {
		         "appId":"wxb72739f825c3480d",     //公众号ID，由商户传入     
		         "timeStamp":"<%=System.currentTimeMillis()%>",         //时间戳，自1970年以来的秒数     
		         "nonceStr":"e61463f8efa94090b1f366cccfbbb444", //随机串     
		         "package":"prepay_id=u802345jgfjsdfgsdg888",     
		         "signType":"MD5",         //微信签名方式：     
		         "paySign":"70EA570631E4BB79628FBCA90534C63FF7FADD89" //微信签名 
		      },
		      function(res){
			      if(res.err_msg == "get_brand_wcpay_request:ok" ){
			      // 使用以上方式判断前端返回,微信团队郑重提示：
			            //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
			      } 
		   }); 
		}
</script>
</html>
