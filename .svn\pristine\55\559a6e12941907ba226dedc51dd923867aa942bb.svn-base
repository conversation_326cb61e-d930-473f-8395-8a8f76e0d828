<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>我的年假</title>
    <style>
        .form-horizontal {
            width: 100%;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="annualDetailForm" data-mars="LeaveDao.annualLeaveRecord" data-text-model="true" autocomplete="off"
          data-mars-prefix="annualLeave.">
        <input name="applyBy" type="hidden" value="${param.applyBy}"/>
        <div class="ibox-content">
            <fieldset class="content-title">
                <legend>本年总年假天数</legend>
            </fieldset>
            <table class="table table-vzebra">
                <tbody>
                <tr>
                    <td width="15%">姓名</td>
                    <td>
                        <input type="text" name="annualLeave.USERNAME" class="">
                    </td>
                    <td width="16%">入职日期</td>
                    <td>
                        <input type="text" id="joinDate" name="annualLeave.JOIN_DATE">
                    </td>
                    <td width="20%">截至<span id="endDate"></span>入职天数</td>
                    <td>
                        <span id="entryDays"></span>
                    </td>
                </tr>
                <tr>
                    <td ><span name="currentYear"></span>年总休假(天)</td>
                    <td>
                        <span id="annualLeaveDays1"></span>
                    </td>
                    <td><span name="currentYear"></span>年春节安排（天)</td>
                    <td>
                        <span id="springFestival"></span>
                    </td>
                    <td><span name="currentYear"></span>年假天数（除春节）</td>
                    <td>
                        <span id="annualLeaveDays2"></span>
                    </td>
                </tr>
                <tr>
                    <td>年假统一计算规则</td>
                    <td colspan="5">
	              <span>
	                  ①截止到<span name="lastYear"></span>年12月31日，在云趣工作不满1年的，年假基数为5天，<span name="currentYear"></span>年休假天数按照<span
                          name="lastYear"></span>年在本单位在职天数折算，折算后的天数减掉<span
                          name="currentYear"></span>春节统一安排休的3天年假即为该员工<span name="currentYear"></span>年剩余可自由支配的年假，折算后不满1整天的部分不享受年休假；
	                  <br/>
	                  ②截止到<span name="lastYear"></span>年12月31日，在云趣工作1-9年的，除春节统一休的3天年假，剩余4天年假可自由支配；10-19年的，年假10天（需扣掉春节统一安排的年假天数）；20年及以上的，年假15天（需扣掉春节统一安排的年假天数）。
	              </span>
                    </td>
                </tr>
                </tbody>
            </table>
            <div id="remainDays">
                <fieldset class="content-title">
                    <legend>已休年假纪录</legend>
                </fieldset>
                <table class="layui-hide" id="annualLeaveApplyList"></table>
                <fieldset class="content-title">
                    <legend>剩余休假天数</legend>
                </fieldset>
                <table class="layui-hide" id="annualLeaveSum"></table>
            </div>
        </div>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">

        var annualLeaveApplyList = {
            init: function () {
                $('#annualDetailForm').initTable({
                        mars: 'LeaveDao.annualLeaveApplyList',
                        id: "annualLeaveApplyList",
                        page: false,
                        cols: [[
                            {
                                type: 'numbers',
                                align: 'left',
                                midWith: '10%'
                            }, {
                                field: 'DATA13',
                                title: '休假天数',
                                align: 'left',
                            }, {
                                field: 'DATA11',
                                title: '休假起始时间',
                                align: 'left',
                            }, {
                                field: 'DATA12',
                                title: '休假结束时间',
                                align: 'left',

                            }
                        ]],
                        done: function () {

                        }
                    }
                );
            },

        }

        var annualLeaveSum = {
            init: function () {
                $('#annualDetailForm').initTable({
                        mars: 'LeaveDao.annualLeaveApplySum',
                        id: "annualLeaveSum",
                        page: false,
                        cols: [[
                            {
                                field: 'TOTAL_DAYS',
                                title: '已休假天数总和(除春节)',
                                align: 'left'
                            }, {
                                title: '最终剩余天数',
                                align: 'left',
                                templet: function (d) {
                                    if ($('#annualLeaveDays2').text() == "无年假天数" || $('#annualLeaveDays2').text() == "入职时间为空！") {
                                        return "无剩余天数";
                                    }
                                    return $('#annualLeaveDays2').text() - d.TOTAL_DAYS;
                                }
                            }
                        ]],
                        done: function () {
                        }
                    }
                );
            },

        }

        $(function () {
            $("[name='currentYear']").text(new Date().getFullYear());
            $("[name='lastYear']").text(new Date().getFullYear() - 1);
            $("#annualDetailForm").render({
                success: function (result) {
                    calculateAnnualLeave1(result);
                    annualLeaveApplyList.init();
                    annualLeaveSum.init();
                }
            });
        });


        function calculateAnnualLeave1(result) {
            var springFestival = 3;
            $('#springFestival').text(springFestival);
            var annualLeaveDays1 = $('#annualLeaveDays1');
            var annualLeaveDays2 = $('#annualLeaveDays2');
            $('#endDate').text((new Date().getFullYear() - 1) + "-12-31");

            var joinDate = result['LeaveDao.annualLeaveRecord']['data']['JOIN_DATE'];
            var start = new Date(joinDate);

            var currentYear = new Date().getFullYear();
            var end = new Date(currentYear - 1, 11, 31, 8, 0, 0);
            var days = Math.abs(end - start) / (1000 * 60 * 60 * 24) + 1;
            $('#entryDays').text(days);
            var years = days / 365;
            if (joinDate == '') {
                annualLeaveDays1.text('入职时间为空！');
                annualLeaveDays2.text('入职时间为空！');
                return;
            }
            if (start > end) {
                annualLeaveDays1.text('无年假天数');
                annualLeaveDays2.text('无年假天数');
                return;
            }
            if (years < 1) {
                annualLeaveDays1.text(Math.floor(years * 5));
                var leaveDays = (Math.floor(years * 5) - springFestival);
                leaveDays = leaveDays>=0?leaveDays:0;
                annualLeaveDays2.text(leaveDays);
            } else if (years <= 9) {
                annualLeaveDays1.text(7);
                annualLeaveDays2.text(7 - springFestival);
            } else if (years <= 19) {
                annualLeaveDays1.text(10);
                annualLeaveDays2.text(10 - springFestival);
            } else if (years >= 20) {
                annualLeaveDays1.text(15);
                annualLeaveDays2.text(15 - springFestival);
            }


        }

    </script>
</EasyTag:override>
<c:choose>
    <c:when test="${param.isDiv==0}">
        <%@ include file="/pages/common/layout_form.jsp" %>
    </c:when>
    <c:otherwise>
        <%@ include file="/pages/common/layout_div.jsp" %>
    </c:otherwise>
</c:choose>