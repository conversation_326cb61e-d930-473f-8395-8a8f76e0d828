<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">开始交付日期</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" data-laydate="{type:'date'}" data-mars="CommonDao.today" class="form-control input-sm Wdate" name="apply.data1"/>
					  			</td>
					  			<td class="required">最晚交付日期</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">支持类型</td>
					  			<td>
					  				<input type="text" data-rules="required"  onclick="selectText(this)" placeholder="点击此选择" readonly="readonly" data-text="方案,报价,交流,应标,其它" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  			<td class="required">销售产品</td>
					  			<td>
					  				<input type="text" data-rules="required" onclick="multiDict(this,'Y012')" placeholder="点击此选择" readonly="readonly" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">客户名称</td>
					  			<td>
					  				<input type="text" data-rules="required" placeholder="两个字以上"  id="custName" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td class="required">项目名称</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">客户行业</td>
					  			<td>
					  				<input type="text" data-rules="required" onclick="singleDict(this,'Y008')" readonly="readonly" class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  			<td class="required">解决方案部门</td>
					  			<td>
					  				<input type="hidden" id="deptLeaderName" name="extend.f1"/>
					  				<input type="hidden" id="deptLeader" name="extend.f2"/>
					  				<input type="text" data-rules="required" id="jjDeptName" class="form-control input-sm" readonly="readonly" placeholder="点击此选择" data-dept-name="解决" data-ref-dept-leader="deptLeader" data-ref-dept-leader-name="deptLeaderName" onclick="singleDept(this);" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">方案负责人</td>
					  			<td>
					  				<input type="hidden" name="apply.data10"/>
					  				<input type="text" data-rules="required" onclick="multiUser(this);" data-dept-name="" readonly="readonly" class="form-control input-sm" name="apply.data9"/>
					  			</td>
					  			<td class="required">关联商机</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" readonly="readonly" placeholder="点击此选择"  onclick="singleSj(this);" name="apply.data11"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">项目需求</td>
					  			<td colspan="3">
									<textarea style="height: 100px;" data-rules="required" placeholder="1、平台规模 &#10;2、建设方式 &#10;3、软硬件提供" onfocus="layer.tips($(this).attr('placeholder'),this,{tips:[1,'#5fb878'],time:3000});" class="form-control input-sm"  name="apply.data12"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>相关文件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript" src="${ctxPath}/static/js/bootstrap3-typeahead.min.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){

			}});
			
			var param = FlowCore.params;
			if(param.handle=='edit'||param.handle=='add'){
				$('#custName').typeahead({
					minLength:2,
					delay: 500,
					fitToElement:false,
					items:'all',
					autoSelect:true,
					source: function (query, process) {
				        return $.ajax({
				            url: '${ctxPath}/servlet/cust?action=queryCompanyName',type: 'post',data: {data:JSON.stringify({companyName: query})},dataType: 'json',async:false,
				            success: function (result) {        
				            	  if(result.state == "1"&&result.data.bsData) {
				            		   nameToIdMap = {};
				                       var json = JSON.parse(result.data.bsData); 
				                       var resultList = json.map(function (item) {
				                          nameToIdMap[item.Name]=item.CreditCode;;
				                          return item.Name;
				                        });          
				                      return process(resultList);                               
				                 } else {
				                	 if(query!=''){
					                    // alert(result.msg);
				                	 }
				                 }  
				            }
				        });
					    },
					    displayText:function(item){
					    	return item;
					    },
					    afterSelect:function(item){
					    	
					    },
					    updater:function(item){
					    	return item;
					    }
				});
			}
		});
		
		Flow.ajaxSubmitForm = function(state){
			Flow.initData();
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		Flow.initData = function(){
			var selectUserName = $("#deptLeaderName").val();
			var selectUserId = $("#deptLeader").val();
			var params = {selectUserName:selectUserName,selectUserId:selectUserId};
			FlowCore.approveData = params;
		}
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='解决方案部门主管'){
				var data9 = $("[name='apply.data9']").val();
				var data10 = $("[name='apply.data10']").val();
				if(data9==''){
					layer.msg('请选择负责人');
					return false;
				}
				var params = {selectUserName:data9,selectUserId:data10};
				FlowCore.approveData = params;
			}
			if(nodeCode=='解决方案部门员工'){
				var data11 = $("[name='apply.data11']").val();
				if(data11==''){
					layer.msg('请选择商机');
					return false;
				}
			}
			return true;
	     }
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>