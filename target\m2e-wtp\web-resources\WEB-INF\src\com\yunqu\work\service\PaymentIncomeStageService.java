package com.yunqu.work.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class PaymentIncomeStageService extends AppBaseService {

    private static class Holder {
        private static PaymentIncomeStageService service = new PaymentIncomeStageService();
    }

    public static PaymentIncomeStageService getService() {
        return Holder.service;
    }

    public void AddPaymentIncomeStage(String extendInfo, String contractId, String incomeStageId) {
        JSONArray extendInfoJsonArray = JSONArray.parseArray(extendInfo);
        for (int i = 0; i < extendInfoJsonArray.size(); i++) {
            JSONObject selectObject = extendInfoJsonArray.getJSONObject(i);
            EasyRecord paymentIncomeStage = new EasyRecord("yq_contract_payment_stage", "PAYMENT_STAGE_ID");
            paymentIncomeStage.set("PAYMENT_STAGE_ID", RandomKit.uuid().toUpperCase());
            paymentIncomeStage.set("PAYMENT_ID", selectObject.getString("PAYMENT_ID"));
            paymentIncomeStage.set("CONTRACT_ID", contractId);
            paymentIncomeStage.set("INCOME_STAGE_ID", incomeStageId);
            paymentIncomeStage.set("AMOUNT_WITH_TAX", selectObject.getString("AMOUNT_WITH_TAX"));
            paymentIncomeStage.set("AMOUNT_NO_TAX", selectObject.getString("AMOUNT_NO_TAX"));
            paymentIncomeStage.set("RATIO", selectObject.getString("CHOSEN_RATIO"));
            try {
                this.getQuery().save(paymentIncomeStage);
            } catch (SQLException e) {
                this.getLogger().error("插入yq_contract_payment_stage表错误", e);
            }
            loadPaymentFlag(selectObject.getString("PAYMENT_ID"));
        }
    }

    public void UpdatePaymentIncomeStage(String extendInfo, String contractId, String incomeStageId) {

        //先全部删除
        DeleteAllPaymentIncomeStage(incomeStageId);
        //再全部添加
        AddPaymentIncomeStage(extendInfo, contractId, incomeStageId);

    }

    public void DeleteAllPaymentIncomeStage(String incomeStageId) {
        try {
            List<EasyRow> paymentIncomeStage = this.getQuery().queryForList("select * from yq_contract_payment_stage where INCOME_STAGE_ID = ?", incomeStageId);
            List<String> paymentIds = new ArrayList<>();
            for (EasyRow row : paymentIncomeStage) {
                paymentIds.add(row.getColumnValue("PAYMENT_ID"));
            }
            this.getQuery().executeUpdate("delete from yq_contract_payment_stage where INCOME_STAGE_ID = ?", incomeStageId);
            for (String paymentId : paymentIds) {
                loadPaymentFlag(paymentId);
            }

        } catch (SQLException e) {
            this.getLogger().error(null, e);
        }

    }

    /*
     * 判断payment在select表中为空，则把payment的select_flag置为0
     */
    public void loadPaymentFlag(String paymentId) {
        try {
            Boolean isSelected = this.getQuery().queryForExist("select count(1) from yq_contract_payment_stage where PAYMENT_ID = ?", new Object[]{paymentId});
            EasyRecord payment = new EasyRecord("yq_contract_payment", "PAYMENT_ID");
            payment.set("PAYMENT_ID", paymentId);
            if (isSelected) {
                payment.set("SELECT_FLAG", "1");
            } else {
                payment.set("SELECT_FLAG", "0");
            }
            this.getQuery().update(payment);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
