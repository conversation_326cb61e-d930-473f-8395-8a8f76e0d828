<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>菜单管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form name="searchForm2" autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm2">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5 style="line-height: 32px;"><span class="glyphicon glyphicon-cutlery"></span> 本周菜单管理</h5>
						  <span class="f-12">必须包含中餐菜单</span>
						  <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info mr-15" onclick="list2.delMeals()">- 批量移除</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						<table class="layui-hide" id="t2"></table>
					</div>
				</div>
		</form>
		<script type="text/html" id="bar">
  			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list2.del">删除</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		$(function(){
			list2.init();
		});
		var list2={
			init:function(){
				$("#searchForm2").initTable({
					mars:'FoodDao.mealsList',
					limit:30,
					id:'t2',
					cols: [[
					  {type:'checkbox',width:50},
					  {title:'序号',type:'numbers',width:50},
					  {
					    field: 'FOOD_NAME',
						title: '菜单名',
						align:'center'
					  },
		              {
					    field: 'FOOD_PRICE',
						title: '价格',
						width:60,
						align:'left'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						width:180,
						align:'center',
					},{
					    field: 'DINNER_TYPE',
						title: '类型',
						width:80,
						align:'center',
						templet:function(row){
							if(row.DINNER_TYPE==1){
								return '早餐';
							}
							if(row.DINNER_TYPE==2){
								return '中餐';
							}
							if(row.DINNER_TYPE==3){
								return '晚餐';
							}
							return '';
						}
					},{
						title: '操作',
						align:'center',
						width:90,
						toolbar: '#bar'
					}
				 ]]}
				);
			},
			query:function(){
				$("#searchForm2").queryData();
			},
			del:function(data){
				layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/food?action=delMeal",data,function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list2.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			delMeals:function(){
				var checkStatus = table.checkStatus('t2');
				console.log(checkStatus.data);
				if(checkStatus.data.length>0){
					ajax.remoteCall("${ctxPath}/servlet/food?action=delMeals",checkStatus.data,function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list2.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}else{
					layer.msg("请选择!");
				}
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>