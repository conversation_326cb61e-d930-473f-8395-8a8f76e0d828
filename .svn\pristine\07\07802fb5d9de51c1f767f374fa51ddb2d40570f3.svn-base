<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>合同评审</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5> 合同评审</h5>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">合同号</span>	
							 <input type="text" name="contractNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">合同名称</span>	
							 <input type="text" name="contractName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">营销大区</span>	
							 <select class="form-control input-sm" data-rules="required" data-mars="CommonDao.deptDict" name="salesDeptId">
		                    	<option value="">请选择</option>
		                     </select>
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">产品线</span>	
					     	 <SELECT  data-rules="required" data-mars="YqAdmin.DictDao.select('Y001')"  name="prodLine"  class="form-control input-sm text-val">
			                    	<option value="">请选择</option>
			                 </SELECT>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
		          	     <div class="pull-right">
				  			<button class="btn btn-sm btn-info ml-10" type="button" onclick="list.add()">+ 发起评审</button>
				  			<button class="btn btn-sm btn-info ml-10" type="button" onclick="list.setting()">+ 评审设置</button>
		          	     </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ReviewDao.list',
					height:'full-120',
					limit:20,
					cols: [[
					  {title:'',type:'radio'},
					  {title:'序号',type:'numbers'},
					  {
					    field: 'REVIEW_NO',
						title: '合同编号',
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail',
						width:120
					 },
					 {
					    field: 'CONTRACT_NAME',
						title: '合同名称',
						align:'left'
					},{
						field:'CUST_NAME',
						title:'客户名称',
						style:'color:#1E9FFF;cursor:pointer',
						event:'custDetail'
					},{
						field:'PROD_LINE',
						title:'产品线',
						width:120,
						templet:function(row){
							var jsonText = row['TEXT_JSON'];
							if(jsonText){
								var json = eval('(' + jsonText + ')');
								return json['PROD_LINE']||'';
							}
							return '';
						}
					},{
						field:'SIGN_DEPT_ID',
						title:'营销大区',
						width:100,
						templet:function(row){
							var jsonText = row['TEXT_JSON'];
							if(jsonText){
								var json = eval('(' + jsonText + ')');
								return json['SIGN_DEPT_ID']||'';
							}
							return ''; 
						}
					},{
						field:'PAY_TYPE',
						title:'付款方式',
						width:90
					},{
					    field: 'PRE_SALES_BY',
						title: '售前经理',
						align:'center',
						width:100,
						templet:function(row){
						    var jsonText = row['TEXT_JSON'];
							if(jsonText){
								var json = eval('(' + jsonText + ')');
								return json['PRE_SALES_BY']||'';
							}
							return ''; 
						}
					},{
					    field: 'SALES_BY',
						title: '销售经理',
						align:'center',
						width:100,
						templet:function(row){
						    var jsonText = row['TEXT_JSON'];
							if(jsonText){
								var json = eval('(' + jsonText + ')');
								return json['SALES_BY']||'';
							}
							return ''; 
						}
					},{
					    field: 'CREATE_TIME',
						title: '发起时间',
						width:120,
						align:'center'
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(data){
				popup.openTab({id:'reviewDetail',title:'评审详情',url:'${ctxPath}/pages/crm/contract/review/review-detail.jsp',title:data.CONTRACT_NAME,data:{contractId:data.CONTRACT_ID,custId:data['CUST_ID'],reviewId:data['REVIEW_ID'],isDiv:0}});
			},
			edit:function(data){
				popup.openTab({id:'reviewDetail',url:'${ctxPath}/pages/crm/contract/review/review-detail.jsp',title:'编辑',data:{reviewId:data.REVIEW_ID}});
			},
			add:function(){
				popup.openTab({id:'reviewDetail',url:'${ctxPath}/pages/crm/contract/review/review-apply.jsp',title:'合同评审'});
			},
			setting:function(){
				popup.openTab({id:'reviewNode',url:'${ctxPath}/pages/crm/contract/review/review-node.jsp',title:'合同评审部门'});
			}
		}
		function custDetail(data){
			var custId = data['CUST_ID'];
			popup.layerClose('contractDetail');
			popup.layerClose('custDetail');
			if(custId==''){
				return;
			}
			popup.openTab({id:'custDetail',title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:['75%','100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId,isDiv:0}});
		}
		function reloadTaskList(){
			
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>