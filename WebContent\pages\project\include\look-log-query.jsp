<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目日志查询</title>
	<style>
	
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="projectLogQueryForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          			 <div class="input-group input-group-sm"  style="width: 260px">
							 <span class="input-group-addon">操作日期</span>	
							 <input data-mars="CommonDao.today" style="width: 90px;display: inline-block" type="text" name="startTime" data-laydate="{type:'date'}" class="form-control input-sm">
							 <input style="width: 90px;display: inline-block" type="text" name="endTime" data-laydate="{type:'date'}" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 170px">
							 <span class="input-group-addon">查看人</span>	
							 <input type="text" name="lookName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 170px">
							 <span class="input-group-addon">项目</span>	
							 <input type="text" name="projectName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="ProjectLogQuery.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="opLogTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var ProjectLogQuery = {
			init:function(){
				$("#projectLogQueryForm").initTable({
					mars:'ProjectDataDao.lookLogQuery',
					id:'opLogTable',
					autoSort:false,
					height:'full-90',
					limit:50,
					cols: [[
						{type:'numbers',title:'序号',width:60},
						{field:'PROJECT_NAME',title:'项目名称',minWidth:100},
						{field:'LOOK_NAME',title:'查看人',width:100},
						{field:'LOOK_TIME',title:'查看时间',width:160},
						{field:'IP',title:'IP',width:120},
						{field:'URL',title:'URL',width:200}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#projectLogQueryForm").queryData({id:'opLogTable',jumpOne:true});
			}
		}
		
		function reloadCustInfo(){
			ProjectLogQuery.query();
		}
		
		$(function(){
			$("#projectLogQueryForm").render({success:function(){
				ProjectLogQuery.init();
			}});
		});
	
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>