<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>账户管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="sqlKey" value="contactUnitList" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>往来单位</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">编码</span>	
							 <input type="text" name="unitNo" class="form-control input-sm" style="width: 90px;">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">公司名</span>	
							 <input type="text" name="unitName" class="form-control input-sm" style="width: 120px;">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="dataMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="dataMgr.add()">+ 新建</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="dataMgr.edit">编辑</a>
				<a class="layui-btn layui-btn-danger layui-btn-xs ml-5" lay-event="dataMgr.del">删除</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var dataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'BxDao.contactUnit',
					id:'dataMgrTable',
					limit:50,
					title:'往来单位',
					limits:[10,30,50,200,500,1000],
					toolbar:true,
					height:'full-90',
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'UNIT_NO',
						title: '编码',
						align:'left'
					},{
					    field: 'UNIT_NAME',
						title: '往来单位',
						align:'left'
					},{
					    field: 'BANK_NAME',
						title: '开户行',
						align:'left'
					},{
					    field: 'BANK_ACOUNT',
						title: '开户账号',
						align:'left'
					},{
					    field: 'CREATE_NAME',
						title: '创建人',
						align:'left'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'left'
					},{
						title: '操作',
						align:'center',
						width:120,
						templet:function(row){
						    return renderTpl('bar1',row);
						}
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable'});
			},
			edit:function(data){
				var  id = data['ID'];
				popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['400px','350px'],url:'/yq-work/pages/flow/bx/config/contact-unit-edit.jsp',title:'编辑',data:{id:id}});
			},
			add:function(){
				popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['400px','350px'],url:'/yq-work/pages/flow/bx/config/contact-unit-edit.jsp',title:'新增',closeBtn:0});
			},
			del:function(data){
				layer.confirm('确认删除?',function(){
					var  id = data['ID'];
					ajax.remoteCall("${ctxPath}/servlet/bx/conf?action=contactUnit",{'conf.ID':id,action:'delete'},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								reloadContactUnit();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			}
		}
		
		function reloadContactUnit(){
			dataMgr.query();
		}
		$(function(){
			$("#dataMgrForm").render({success:function(){
				dataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>