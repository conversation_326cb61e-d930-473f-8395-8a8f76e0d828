## memos openid
oauthMemos.openid = OA
oauthMemos.openkey = yunqu168
oauthMemos.redirect = http://172.16.68.152:5230/auth/callback

## qq openid
oauthQQ.openid = 100413274
oauthQQ.openkey = 0738a22b862ead14bb7976272b0a1eec
oauthQQ.redirect = https://work.yunqu-info.cn/oauth2/qq/callback

## weixin openid http://www.jianshu.com/p/3733777a2020
oauthWeixin.openid = xxxxxx
oauthWeixin.openkey = xxxxxx
oauthWeixin.redirect =  xxxxxx

## sina openid
oauthSina.openid = 4193705357
oauthSina.openkey = bc7bbdfe92be06b42b38206f8bca3645
oauthSina.redirect = https://work.yunqu-info.cn/oauth2/sina/callback

## baidu openid
oauthBaidu.openid = xwKOgtKjbbrn9dOb7ZkGrAo5
oauthBaidu.openkey = dNlKN4vVqgZvROrWW8twc4wESGWkSfF8
oauthBaidu.redirect = https://work.yunqu-info.cn/oauth2/baidu/callback

## renren openid
oauthRenren.openid = 80cd6ab8bc924c97b78e06568196456e
oauthRenren.openkey = 51aab0e2633f43a2aeda89f299a7b4f8
oauthRenren.redirect = https://work.yunqu-info.cn/oauth2/renren/callback

## osc openid
oauthOsc.openid = R6XS1Qnhist6jy5UABer
oauthOsc.openkey = llvILNi5ThQj2YwgbM6qx7BOEKIfJjjM
oauthOsc.redirect = https://work.yunqu-info.cn/oauth2/osc/callback

## douban openid
oauthDouban.openid = 04e962ea4e22c5980ebc28beea6850c8
oauthDouban.openkey = d7ae6ac47ddf75e2
oauthDouban.redirect = https://work.yunqu-info.cn/oauth2/douban/callback

## github openid `no tested`
oauthGithub.openid = 
oauthGithub.openkey = 
oauthGithub.redirect = 