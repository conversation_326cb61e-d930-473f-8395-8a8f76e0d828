package com.yunqu.work.dao.platform;

import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * 平台业务DAO
 */
@WebObject(name="BusinessPlatformDao")
public class BusinessPlatformDao extends AppDaoContext {

    @WebControl(name="getTree",type=Types.TREE)
    public JSONObject getTree(){
        return queryForList("SELECT PLATFORM_TYPE_ID,P_PLATFORM_TYPE_ID,PLATFORM_TYPE_NAME FROM YQ_BUSINESS_PLATFORM_TYPE ORDER BY IDX_ORDER",new Object[]{});
    }

    @WebControl(name="getPlatformType",type=Types.RECORD) 
    public JSONObject getPlatformType(){
        String pk = param.getString("pk");
        EasyRecord record = new EasyRecord("YQ_BUSINESS_PLATFORM_TYPE","PLATFORM_TYPE_ID").setPrimaryValues(pk);
        return queryForRecord(record);
    }

    @WebControl(name="getPlatform",type=Types.RECORD)
    public JSONObject getPlatform(){
        EasySQL sql = this.getEasySQL("select * from YQ_BUSINESS_PLATFORM");
        if(!StringUtils.isBlank(param.getString("platformId"))){
            sql.append(param.getString("platformId"), " where PLATFORM_ID = ?");
            return queryForRecord(sql.getSQL(), sql.getParams());
        }
        JSONObject resultJson = new JSONObject();
        resultJson.put("data", new JSONObject());
        return resultJson;
    }

    @WebControl(name="platformList",type=Types.LIST) 
    public JSONObject platformList(){
        String platformTypeId = param.getString("platformTypeId");
        String platformName = param.getString("platformName");
        
        EasySQL sql = this.getEasySQL("select t1.*,t2.PLATFORM_TYPE_NAME from YQ_BUSINESS_PLATFORM t1 , YQ_BUSINESS_PLATFORM_TYPE t2 where t1.PLATFORM_TYPE_ID=t2.PLATFORM_TYPE_ID ");
        if(!"0".equals(platformTypeId)) {
            sql.appendLike(platformTypeId,"and ( t1.PLATFORM_TYPE_ID like ?");
            sql.appendLike(platformTypeId,"or t2.P_PLATFORM_TYPE_ID like ? )");
        }
        sql.appendLike(platformName, " and t1.PLATFORM_NAME like ?");
        sql.append(param.getInteger("status"),"and t1.STATUS = ?");
        sql.append("order by t2.IDX_ORDER,t2.PLATFORM_TYPE_ID,t1.IDX_ORDER ASC");
        return this.queryForPageList(sql.getSQL(),sql.getParams(),null);
    }
}