package com.yunqu.work.servlet;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Collection;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.easitline.common.core.Globals;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.FileModel;
/**
 *文件上传辅助类
 *
 */
@WebServlet(urlPatterns = { "/servlet/upload/*" })
@MultipartConfig(maxFileSize=100*1024*1024)
public class UploadServlet extends AppBaseServlet{
	private static final long serialVersionUID = 1L;
	
	public JSONObject actionForIndex(){
		HttpServletRequest request=getRequest();
		try {
			Collection<Part> fileList=request.getParts();
			String source=getPara("source");
			if(fileList==null||fileList.size()<=0){return EasyResult.fail("未选择文件.");}
			JSONObject jsonObject=new JSONObject();
			for (Part part:fileList) {
				if(part.getSubmittedFileName()==null){continue;}
				InputStream is = part.getInputStream();  
				String filename = new String(getFilename(part).getBytes(), "UTF-8"); 
				if(StringUtils.isBlank(filename)){
					part.delete();
					continue;
				}
				String fileId=RandomKit.uuid();
				String fileDir=FileKit.getFileDir();
				File file = new File(getDir()+source+fileDir);  
				if (!file.exists()) {  
					file.mkdirs();
				}  
				String newFileName=getNewFileName(fileId,filename);
				String urlPath="/files/"+source+fileDir+newFileName;
				File newFile=new File(file +File.separator + newFileName);
				
				jsonObject.put("id",fileId);
				jsonObject.put("url",urlPath);
				jsonObject.put("name",filename);
				jsonObject.put("fsType",FileKit.getHouzui(filename));
				jsonObject.put("path",newFile.getAbsolutePath());
				
				FileModel model=new FileModel();
				model.setFileId(fileId);
				model.addCreateTime();
				model.setCreator(getRemoteUser());
				model.setFileName(filename);
				model.setAccessUrl(urlPath);
				model.setDiskPath(newFile.getAbsolutePath());
				model.setFileType(FileKit.getHouzui(filename));
				model.setFkId(getPara("fkId"));
				model.setSource(getPara("source"));
				
				FileOutputStream fos = new FileOutputStream(newFile);
				byte[] buf = new byte[1024];  
				while (is.read(buf) != -1) {  
					fos.write(buf);  
				}  
				fos.flush();  
				fos.close();  
				is.close();  
				
				part.delete();// 删除临时文件
				
				model.setFileSize(getPrintSize(newFile.length()));
				model.save();
			}
			
			return EasyResult.ok(jsonObject, "上传成功!");
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
	    }
	}
	private String getPrintSize(long size) {
		// 如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
		double value = (double) size;
		if (value < 1024) {
			return String.valueOf(value) + "B";
		} else {
			value = new BigDecimal(value / 1024).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
		}
		// 如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
		// 因为还没有到达要使用另一个单位的时候
		// 接下去以此类推
		if (value < 1024) {
			return String.valueOf(value) + "KB";
		} else {
			value = new BigDecimal(value / 1024).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
		}
		if (value < 1024) {
			return String.valueOf(value) + "MB";
		} else {
			// 否则如果要以GB为单位的，先除于1024再作同样的处理
			value = new BigDecimal(value / 1024).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
			return String.valueOf(value) + "GB";
		}
	}
	private String getNewFileName(String id,String oldFileName) {
		String filename = id+FileKit.getHouzui(oldFileName);
		return filename;
	}
	 private String getFilename(Part part) {  
	        String contentDispositionHeader = part.getHeader("content-disposition");  
	        String[] elements = contentDispositionHeader.split(";");  
	        for (String element : elements) {  
	            if (element.trim().startsWith("filename")) {  
	                return element.substring(element.indexOf('=') + 1).trim().replace("\"", "");  
	            }  
	        }  
	        return null;  
	}
	 public static String getDir() {
		String saveDirectory = Globals.WEBAPPS_DIR+File.separator+"/files/";
		return saveDirectory;
	}
}
