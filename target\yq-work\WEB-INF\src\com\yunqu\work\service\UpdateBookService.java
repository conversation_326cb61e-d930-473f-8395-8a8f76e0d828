package com.yunqu.work.service;

import java.sql.SQLException;

import org.easitline.common.utils.calendar.EasyDate;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.utils.DateUtils;

public class UpdateBookService extends AppBaseService implements Job{

	private static class Holder{
		private static UpdateBookService service=new UpdateBookService();
	}
	public static UpdateBookService getService(){
		return Holder.service;
	}
	
	public void run(){
		try {
			getLogger().info("执行订餐调度任务....");
			this.getQuery().executeUpdate("update dc_reserve_list set state = 0 ,create_time = ? where state =1 and  ymd = ?",EasyDate.getCurrentDateString(), DateUtils.getPlanToday());
		} catch (SQLException e) {
			e.printStackTrace();
			getLogger().error(e.getMessage(),e);
		}
	}
	
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		run();
	}

}
