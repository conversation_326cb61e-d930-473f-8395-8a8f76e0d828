<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>客户管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		.layui-table-cell{padding: 0 6px;font-size: 13px;}
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="custMgrForm">
			<input name="custMgr" id="custMgr" value="1" type="hidden"/>
			<input name="taskState" id="taskState" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>客户管理</h5>
	          		     <div class="input-group input-group-sm" style="width: 170px">
							 <span class="input-group-addon">客户名称</span>	
							 <input type="text" name="custName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
					 		<span class="input-group-addon">状态</span>
						 	<select class="form-control input-sm" name="custState">
	                    		<option value="">请选择</option>
	                    		<option value="10">初次接洽</option>
	                   		 	<option value="20">需求讨论</option>
	                   		 	<option value="30">方案/报价</option>
	                   		 	<option value="40">合同审核</option>
	                   		 	<option value="50">成交客户</option>
	                   		 	<option value="60">流失客户</option>
	                    	</select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px">
					 		<span class="input-group-addon">客户性质</span>
	                    	<input class="form-control" onclick="singleDict(this,'Y006')" readonly="readonly" name="custNature">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px">
					 			<span class="input-group-addon">行业</span>
	                    		<input class="form-control" onclick="singleDict(this,'Y008')" readonly="readonly" name="industry">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px">
					 			<span class="input-group-addon">客户级别</span>
		                    	<input class="form-control" onclick="singleDict(this,'Y007')" readonly="readonly" name="custLevel">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="CustMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="CustMgr.add()">+ 新建客户</button>
							 <button type="button" class="btn btn-sm btn-default ml-10 mr-10 hidden" onclick="synCust()">同步客户</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="custMgrTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script src="http://api.map.baidu.com/api?v=2.0&ak=EZPCgQ6zGu6hZSmXlRrUMTpr"></script>
<script type="text/javascript">
		
		var CustMgr={
			init:function(){
				$("#custMgrForm").initTable({
					mars:'CustDao.custList',
					id:'custMgrTable',
					autoSort:false,
					height:'full-90',
					limit:50,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'CUST_NAME',
						title: '客户名称',
						align:'left',
						minWidth:150,
						event:'CustMgr.detail',
						style:'color:#1E9FFF;cursor:pointer'
					},{
					    field: 'CUST_NATURE',
						title: '客户性质',
						align:'center',
						width:70
					},{
					    field: 'INDUSTRY',
						title: '行业',
						align:'left',
						width:80
					},{
					    field: 'CUST_LEVEL',
						title: '客户级别',
						align:'left',
						width:90
					},{
					    field: 'CUST_RESULT',
						title: '客户状态',
						align:'center',
						width:80,
						templet:function(row){
							return getText(row['CUST_STATE'],'custState');
						}
					},{
					    field: 'LAST_FOLLOW_TIME',
						title: '最后跟进时间',
						align:'center',
						width:110
					},{
					    field: 'LAST_FOLLOW_CONTENT',
						title: '最后跟进内容',
						align:'left'
					},{
					    field: 'OWNER_ID',
						title: '所属销售',
						align:'center',
						width:70,
						templet:function(row){
							return '<a href="javascript:;" onclick="userInfoLayer(\''+row['OWNER_ID']+'\')">'+getUserName(row.OWNER_ID)+'</a>';
						}
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						width:90,
						align:'center',
						templet:function(row){
							var time= row['UPDATE_TIME'];
							return cutText(time,12,'');
						}
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
						width:130,
						sort:true,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#custMgrForm").queryData({id:'custMgrTable',jumpOne:true});
			},
			detail:function(data){
				var  custId = data['CUST_ID'];
				var width = $(window).width();
				var w = '80%';
				if(width>1500){
					w = '800px';
				}else if(width<1000){
					w = '100%';
				}else{
					
				}
				popup.layerClose('custDetail');
				popup.openTab({id:'custDetail',title:false,title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:[w,'100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId,isDiv:0}});
			},
			edit:function(data){
				var  custId = data['CUST_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:'${ctxPath}/pages/crm/cust/cust-edit.jsp',title:'编辑客户',data:{custId:custId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:'${ctxPath}/pages/crm/cust/cust-edit.jsp',title:'新增客户',closeBtn:0});
			}
		}
		function reloadCustInfo(){
			CustMgr.query();
		}
		$(function(){
			$("#custMgrForm").render({success:function(){
				CustMgr.init();
			}});
		});
		function synCust(){
			ajax.remoteCall("${ctxPath}/servlet/cust?action=synCust",{},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadCustInfo();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function synCustDetail(){
			ajax.remoteCall("${ctxPath}/servlet/cust?action=synCustDetail",{},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadCustInfo();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function synCustId(){
			ajax.remoteCall("${ctxPath}/servlet/cust?action=synCustId",{},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadCustInfo();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>