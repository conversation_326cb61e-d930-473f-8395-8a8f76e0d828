package com.yunqu.work.model.rowmapper;

public class ProjectDomain {

	public String projectId;
	
	public String projectName;
	
	public String projectNo;
	
	public String po;
	
	public String projectPo;
	
	public String salesBy;
	
	public String preSalesBy;
	
	public String poName;
	
	public String salesByName;
	
	public String preSalesName;
	
	public String projectPoName;
	
	public String webhook;

	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	public String getProjectName() {
		return projectName;
	}

	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}

	public String getProjectPoName() {
		return projectPoName;
	}

	public void setProjectPoName(String projectPoName) {
		this.projectPoName = projectPoName;
	}

	public String getWebhook() {
		return webhook;
	}

	public void setWebhook(String webhook) {
		this.webhook = webhook;
	}

	public String getProjectNo() {
		return projectNo;
	}

	public void setProjectNo(String projectNo) {
		this.projectNo = projectNo;
	}

	public String getPo() {
		return po;
	}

	public void setPo(String po) {
		this.po = po;
	}

	public String getProjectPo() {
		return projectPo;
	}

	public void setProjectPo(String projectPo) {
		this.projectPo = projectPo;
	}

	public String getSalesBy() {
		return salesBy;
	}

	public void setSalesBy(String salesBy) {
		this.salesBy = salesBy;
	}

	public String getPreSalesBy() {
		return preSalesBy;
	}

	public void setPreSalesBy(String preSalesBy) {
		this.preSalesBy = preSalesBy;
	}

	public String getPoName() {
		return poName;
	}

	public void setPoName(String poName) {
		this.poName = poName;
	}

	public String getSalesByName() {
		return salesByName;
	}

	public void setSalesByName(String salesByName) {
		this.salesByName = salesByName;
	}

	public String getPreSalesName() {
		return preSalesName;
	}

	public void setPreSalesName(String preSalesName) {
		this.preSalesName = preSalesName;
	}
	
	
	
}
