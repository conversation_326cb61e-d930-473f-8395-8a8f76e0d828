package com.yunqu.work.servlet.devops;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.ServiceModel;
/**
 * 服务管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/service/*")
public class ServiceServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		ServiceModel model=getModel(ServiceModel.class, "service");
		model.setPrimaryValues(RandomKit.orderId());
		model.addCreateTime();
		model.setCreator(getUserId());
		try {
			model.save();
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		ServiceModel model=getModel(ServiceModel.class, "service");
		try {
			model.update();
		} catch (SQLException e) {
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		ServiceModel model=getModel(ServiceModel.class, "service");
		try {
			model.delete();
		} catch (SQLException e) {
			return EasyResult.fail();
		}
	   return EasyResult.ok();
	}
}





