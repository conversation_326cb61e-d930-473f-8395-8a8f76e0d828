<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
    <title>发票明细</title>
    <style>
        .ibox-content .layui-table-cell {
            font-size: 13px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form autocomplete="off" onsubmit="return false;" class="form-inline" id="searchForm">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <div class="input-group input-group-sm" style="width: 180px">
                        <span class="input-group-addon">平台类型</span>
                        <select name="platformTypeId" class="form-control input-sm" data-mars="BusinessPlatformDao.getPlatformTypeDict" onchange="InvoiceList.loadPlatforms(this.value)">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm" style="width: 200px">
                        <span class="input-group-addon">业务平台</span>
                        <select name="platformId" class="form-control input-sm" data-mars="BusinessPlatformDao.getPlatformDict">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">客户名称</span>
                        <input type="text" name="custName" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">合同编号</span>
                        <input type="text" name="contractNo" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">合同名称</span>
                        <input type="text" name="contractName" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 130px">
                        <span class="input-group-addon">合同销售</span>
                        <input type="hidden" name="saleBy">
                        <input type="text" name="saleName" onclick="singleUser(this);" class="form-control input-sm">
                    </div>
                </div>
                <div class="form-group input-group-sm">
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">开票日期</span>
                        <input type="text" name="kpBeginDate" class="form-control input-sm" style="width: 90px;" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
                        <input type="text" name="kpEndDate" class="form-control input-sm" style="width: 90px;" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">发票编号</span>
                        <input type="text" name="invoiceNo" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">开票类型</span>
                        <select name="invoiceType" class="form-control input-sm" data-mars="YqAdmin.DictDao.select('Y009')">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">收入类型</span>
                        <select name="paymentType" class="form-control input-sm" data-mars="ContractPaymentDao.getPaymentType()">
                            <option value="">全部</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm">
                        <button type="button" class="btn btn-sm btn-info" data-event="enter" onclick="InvoiceList.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
                        <button type="button" class="btn btn-sm btn-default ml-5" onclick="$('#searchForm')[0].reset();InvoiceList.query();">重置</button>
                    </div>
                </div>
            </div>
            <div class="ibox-content">
                <table class="layui-hide" id="invoiceQueryTable"></table>
            </div>
        </div>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        var InvoiceList = {
            init: function(){
                $("#searchForm").initTable({
                    mars:'InvoiceDao.platformQueryList',
                    id:'invoiceQueryTable',
                    height:'full-110',
                    limit:30,
                    autoSort:false,
                    toolbar:true,
                    defaultToolbar:['filter', 'print', 'exports'],
                    cols: [[
                        {
                            title: '序号',
                            type: 'numbers',
                            align:'left'
                        },{
                            field: 'PLATFORM_TYPE_NAME',
                            title: '平台类型',
                            minWidth:100
                        },{
                            field: 'PLATFORM_NAME',
                            title: '业务平台',
                            minWidth:120,
                            templet:'<div><a href="javascript:;" onclick="platformDetail(\'{{d.PLATFORM_ID}}\')">{{d.PLATFORM_NAME}}</a></div>'
                        },{
                            field: 'INVOICE_NO',
                            title: '发票编号',
                            width:120
                        },{
                            field: 'PLATFORM_NAME',
                            title: '业务平台',
                            minWidth:120
                        },{
                            field: 'CUST_NAME',
                            title: '客户名称',
                            minWidth:150,
                            templet:'<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
                        },{
                            field: 'CONTRACT_NO',
                            title: '合同编号',
                            minWidth:120,
                            templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_NO}}</a></div>'
                        },{
                            field: 'CONTRACT_SIMPILE_NAME',
                            title: '合同名称',
                            minWidth:150,
                            templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_SIMPILE_NAME}}</a></div>'
                        },{
                            field: 'SALES_BY_NAME',
                            title: '合同销售',
                            width:80,
                            align: 'center'
                        },{
                            field: 'INVOICE_TYPE',
                            title: '开票类型',
                            width:100
                        },{
                            field: 'MARK_DATE',
                            title: '开票日期',
                            width:90
                        },{
                            field: 'MARK_MONEY',
                            title: '开票金额',
                            width:100,
                            align: 'center'
                        },{
                            field: 'TAX_RATE',
                            title: '税率(%)',
                            width:80,
                            align: 'center'
                        },{
                            field: 'TOTAL_TAX',
                            title: '税额',
                            width:100,
                            align: 'center'
                        },{
                            field: 'AMOUNT_IN_FIGUERS',
                            title: '不含税金额',
                            width:100,
                            align: 'center'
                        },{
                            field: 'PAYMENT_TYPE',
                            title: '收入类型',
                            width:100,
                            templet:function(row){
                                return getText(row.PAYMENT_TYPE,'paymentType');
                            }
                        },{
                            field: 'SETTLEMENT_ID',
                            title: '结算状态',
                            width:80,
                            align:'center',
                            templet: function(d){
                                if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
                                    return '<span style="color:#ff5722;">已结算</span>';
                                } else {
                                    return '<span style="color:#009688;">未结算</span>';
                                }
                            }
                        },{
                            field: 'REMARK',
                            title: '备注',
                            minWidth:100
                        },{
                            field: 'CREATE_NAME',
                            title: '创建人',
                            width:90,
                            align: 'center'
                        },{
                            field: 'CREATE_TIME',
                            title: '创建时间',
                            width:160
                        }
                    ]]
                });
            },
            loadPlatforms: function(platformTypeId){
                $("[name='platformId']").render({data:{platformTypeId:platformTypeId}});
            },
            query: function(){
                $("#searchForm").queryData({id:'invoiceQueryTable',jumpOne:true});
            }
        }

        $(function(){
            $("#searchForm").render({success:function(){
                InvoiceList.init();
            }});
        });

        function platformDetail(platformId){
            popup.openTab({id: 'platformDetail', title: '平台详情', url: '${ctxPath}/pages/crm/platform/business-platform-detail.jsp', data: {platformId:platformId}});
        }

        function contractDetail(contractId){
            popup.openTab({
                id:'contractDetail',
                title:'合同详情',
                url:'${ctxPath}/project/contract',
                data:{contractId:contractId,isDiv:0}
            });
        }

        function custDetail(custId){
            popup.openTab({
                id:'custDetail',
                title:'客户详情',
                url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',
                data:{custId:custId,isDiv:0}
            });
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
