function getUserName(userId){
	if(userId=='')return '';
	var cacheStr=sessionStorage.getItem("userList");
	if(cacheStr){
		var json=$.extend({},eval('(' + cacheStr + ')'));
		var userInfo=json[userId];
		if(userInfo){
			return userInfo.userName||'';
		}else{
			return '';
		}
	}else{
		initUserList();
		return '';
	}
}
function getFileIcon(fileType){
	if(fileType){
		fileType = fileType.replace('.','-');
		fileType = fileType.toLowerCase();
		return '<svg class="icon" aria-hidden="true"><use xlink:href="#icon'+fileType+'"></use></svg>';
	}else{
		return '';
	}
}

function dataToTree(data, opt) { //树数据源扁平结构转嵌套
    opt = opt || {};
    var idFiled = opt.idFiled || 'id';
    var textFiled = opt.textFiled || 'text';
    var parentField = opt.parentField || 'parent'; //决定父级的字段
    var childField = opt.childField || 'children'; //决定子级的字段
    var i, l, treeData = [],
        tmpMap = [];
    for (i = 0, l = data.length; i < l; i++) tmpMap[data[i][idFiled]] = data[i];
    for (i = 0, l = data.length; i < l; i++) {
        if (tmpMap[data[i][parentField]] && data[i][idFiled] != data[i][parentField]) {
            if (!tmpMap[data[i][parentField]][childField]){ tmpMap[data[i][parentField]][childField] = [];}
            if(opt.map){
            	for(var key in opt.map){
            		data[i][opt.map[key]] = data[i][key];
            	}
            }
            if(opt.def){
            	for(var key in opt.def){
            		data[i][key] = opt.def[key];
            	}
            }
            data[i].hasChildren = 0; // 默认设置为0
            tmpMap[data[i][parentField]][childField].push(data[i]);
            tmpMap[data[i][parentField]].hasChildren = 1; // 父节点设置为1
        } else {
            if(opt.map){
            	for(var key in opt.map){
            		data[i][opt.map[key]] = data[i][key];
            	}
            }
            if(opt.def){
            	for(var key in opt.def){
            		data[i][key] = opt.def[key];
            	}
            }
            data[i].hasChildren = 0; // 默认设置为0
            treeData.push(data[i]);
        }
    }
    return treeData;
}

String.prototype.replaceAll = function(s1, s2) {
    return this.replace(new RegExp(s1, "gm"), s2);
}

function limitLenth(el,limitLen){
	var val = el.value;
	var len = 0;
    for (var i = 0; i < val.length; i++) {
         var a = val.charAt(i);
         if (a.match(/[^\x00-\xff]/ig) != null) {
            len += 1;
        }else{
            len += 1;
        }
    }
	if(len>limitLen){
		alert('当前文本框字数'+len+',超过最大长度'+limitLen+",请删减！");
	}
}
	
function locationCompany(){
	var url=location.href;
	if(url.indexOf('yunqu')>0){
		return false;
	}
	return true;
}
function fullShow(){
	var device = layui.device();
	var height = $(window).height();
    if(device.mobile||height<600){
       return true;
    }else{
    	return false;
    }
}

function projectDetailByRow(data){
	projectBoard(data.PROJECT_ID);
}

function projectBoard(id){
	var a = $("<a href='/yq-work/project/"+id+"' target='_blank'>Apple</a>").get(0);
    var e = document.createEvent('MouseEvents');
    e.initEvent('click', true, true );
    a.dispatchEvent(e);
}

function getProjectInfo(projectId,fn){
	ajax.remoteCall(getCtxPath()+"/servlet/project?action=getProjectInfo",{projectId:projectId},function(result) { 
		if(result.state == 1){
			fn && fn(result.data);
		}else{
			layer.alert(result.msg);
		}
	});
}

function getUserPic(userId){
	if(userId=='')return '';
	var cacheStr=sessionStorage.getItem("userList");
	if(cacheStr){
		var json=$.extend({},eval('(' + cacheStr + ')'));
		var userInfo=json[userId];
		if(userInfo){
			return userInfo.userPic||'';
		}else{
			return '';
		}
	}else{
		initUserList();
		return '';
	}
}

var isSuperUser=isSuperUser();

function isSuperUser(){
	var cacheStr=sessionStorage.getItem("isSuperUser");
	if(cacheStr){
		return cacheStr;
	}else{
		initUserList();
		return false;
	}
}
function getDeptId(){
	var cacheStr=localStorage.getItem("deptId");
	return cacheStr;
}
function isDevLead(){
	var cacheStr=localStorage.getItem("devLead")=='1';
	return cacheStr;
}

function getCurrentUserId(){
	var cacheStr=localStorage.getItem("yq_userId");
	if(cacheStr==null||cacheStr==''){
		return initUserInfo();
	}
	return cacheStr;
}

function initUserInfo(){
	ajax.remoteCall("/yq-work/webcall?action=CommonDao.myUserInfo",{},function(result){
		var data = result.data;
		localStorage.setItem("yq_userId",data.userId);
		localStorage.setItem("deptId",data.deptId);
		
		layui.data('yqwork',{key:'staffInfo',value:data});
		layui.data('yqwork',{key:'userId',value:data.userId});
		layui.data('yqwork',{key:'deptId',value:data.deptId});
		
		return data.userId;
	},{async:false});
}

function sendWxMsg(type,message){
	ajax.remoteCall("/yq-work/wxMsg/sendMsg",{type:type,message:message},function(result){
		console.log(result);
	},{loading:false});
}

function getStaffInfo(){
	var data = layui.data('yqwork');
	var staffInfo = data['staffInfo'];
	if(staffInfo==undefined){
		initUserInfo();
		return getStaffInfo();
	}
	return staffInfo;
}

function initUserList(){
	var cache=sessionStorage.getItem("userList");
	if(cache==null||cache==''){
		ajax.remoteCall("/yq-work/webcall?action=CommonDao.userList",{},function(result){
			var userList={};
			var userPic={};
			var data=result.data;
			var isSuperUser=result.isSuperUser
			for(var index in data){
				userList[data[index].USER_ID]={userName:data[index].USERNAME,userPic:data[index].PIC_URL};
			}
			sessionStorage.setItem("userList",JSON.stringify(userList));
			sessionStorage.setItem("isSuperUser",isSuperUser);
		});
	}
}
function userInfoLayer(userId){
	if(userId){
		if(userId.indexOf(',')>-1){
			return;
		}
		ajax.remoteCall("/yq-work/webcall?action=CommonDao.userInfo",{userId:userId},function(result){
			var index = 0;
			var data=result.data;
			var content = [];
			content.push('<div style="padding:20px;line-height:30px;">');
			content.push('<img onerror="this.src=\'/yq-work/static/images/user-avatar-large.png\'" style="width:60px;height:60px;border-radius:50%;" src="{{:HEAD_IMG}}"><span class="ml-10">{{:USER_NAME}}&nbsp;<span class="f-13">{{:NIKE_NAME}}</span></span>');
			content.push('<br>部门：{{:DEPT_NAME}}<br>岗位：{{:POST}}');
			content.push('<br>邮箱：{{:EMAIL}}');
			content.push('<br>手机号码：{{:MOBILE}}');
			content.push('<br>最近登录时间：{{:LAST_LOGIN_TIME}}');
			content.push('<br><a class="btn btn-xs btn-info" href="/yq-work/affair?userId={{:USER_ID}}&userName={{:USER_NAME}}" target="_blank"><i class="fa fa-send"></i> 发事务</a>');
			content.push('</div>');
			var tpl = $.templates(content.join(""));
			var html = tpl.render(data);
			index = layer.open({type:1,content:html,area:['300px','300px'],offset:'30px',shade:0.1,closeBtn:2,title:false,shadeClose:true});
		});
	}
}

function getUserInfo(userId,fn){
	ajax.remoteCall("/yq-work/webcall?action=CommonDao.userInfo",{userId:userId},function(result){
		var data = result.data;
		fn&&fn(data);
	});
}

function getDeptInfo(deptId,fn){
	ajax.remoteCall("/yq-work/webcall?action=CommonDao.deptInfo",{deptId:deptId},function(result){
		var data = result.data;
		fn&&fn(data);
	});
}

function loadFlowNum(){
	ajax.daoCall({loading:false,controls:['FlowDao.myFlowTodoCount','FlowDao.myFlowApplyCount','FlowDao.myFlowFinishApplyCount','FlowDao.myFlowDoneCount','FlowDao.myCCFlowCount'],params:{}},function(rs){
		var myFlowTodoCount = rs['FlowDao.myFlowTodoCount'].data;
		var myFlowApplyCount = rs['FlowDao.myFlowApplyCount'].data;
		var myFlowDoneCount = rs['FlowDao.myFlowDoneCount'].data;
		var myCCFlowCount = rs['FlowDao.myCCFlowCount'].data;
		var myFlowFinishApplyCount = rs['FlowDao.myFlowFinishApplyCount'].data;
		top.$("[data-id='flow_my_todo'] cite").text('我的待办('+myFlowTodoCount+')');
		top.$("[data-id='flow_my_apply'] cite").text('我的申请('+myFlowApplyCount+')');
		top.$("[data-id='flow_my_done'] cite").text('已办流程('+myFlowDoneCount+')');
		top.$("[data-id='flow_cc'] cite").text('抄送流程('+myCCFlowCount+')');
		
		
		$('#flow_my_todo').text(myFlowTodoCount);
		$('#flow_my_apply').text(myFlowApplyCount);
		$('#flow_my_done').text(myFlowDoneCount);
		$('#flow_my_cc').text(myCCFlowCount);
		$('#flow_my_finish').text(myFlowFinishApplyCount);
		
		
	});
}

function projectState(state){
	var json={11:'进度正常',12:'存在风险',13:'进度失控',14:'进度提前',15:'进度滞后',20:'挂起中',30:'已完成'};
	var result=json[state]||'';
	if(result){
		return result;
	}else{
		return '';
	}
}


function timeBetween(dateBegin){
	return timeFn(dateBegin,1);
}

function twoTimeInterval(dateBegin,nullStr) {
   if(dateBegin==undefined||dateBegin==''||dateBegin.indexOf("NaN")>-1){
		return nullStr||'';
	}
	dateBegin = dateBegin+":00";
	dateBegin = dateBegin.replace(/-/g,"/");
	var date = new Date(dateBegin);
	
    //如果时间格式是正确的，那下面这一步转化时间格式就可以不用了
    var dateEnd = new Date();//获取当前时间
    var dateDiff = dateEnd.getTime() - date.getTime();//时间差的毫秒数
    if(dateDiff<0){
    	dateDiff=Math.abs(dateDiff);
    }
    var dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000));//计算出相差天数
    var leave1=dateDiff%(24*3600*1000)    //计算天数后剩余的毫秒数
    var hours=Math.floor(leave1/(3600*1000))//计算出小时数
    //计算相差分钟数
    var leave2=leave1%(3600*1000)    //计算小时数后剩余的毫秒数
    var minutes=Math.floor(leave2/(60*1000))//计算相差分钟数
	return (dayDiff>0?dayDiff+"天":'')+(hours>0?hours+"时":'')+(minutes>0&&hours==0?minutes+"分":'');
}



//计算两个时间差 dateBegin 开始时间
function timeFn(dateBegin,showFlag) {
	showFlag = showFlag||'0';
	if(dateBegin==''||dateBegin.indexOf("NaN")>-1||dateBegin.length!=16){
		return '--';
	}
	dateBegin = dateBegin+":00";
	dateBegin = dateBegin.replace(/-/g,"/");
	var date = new Date(dateBegin);
	
	var flag=true;
    //如果时间格式是正确的，那下面这一步转化时间格式就可以不用了
    var dateEnd = new Date();//获取当前时间
    var dateDiff = dateEnd.getTime() - date.getTime();//时间差的毫秒数
    if(dateDiff<0){
    	flag=false;
    	dateDiff=Math.abs(dateDiff);
    }
    var dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000));//计算出相差天数
    var leave1=dateDiff%(24*3600*1000)    //计算天数后剩余的毫秒数
    var hours=Math.floor(leave1/(3600*1000))//计算出小时数
    //计算相差分钟数
    var leave2=leave1%(3600*1000)    //计算小时数后剩余的毫秒数
    var minutes=Math.floor(leave2/(60*1000))//计算相差分钟数
    if(flag){
    	//超时
    	return "<span class='layui-badge'>超时"+(dayDiff>0?dayDiff+"天":'')+(hours>0?hours+"时":'')+(minutes>0&&hours==0?minutes+"分":''+"</span>");
    }else{
    	if(showFlag==1)return "<span class='layui-badge layui-bg-blue'>剩余"+(dayDiff>0?dayDiff+"天":'')+(hours>0?hours+"时":'')+(minutes>0&&hours==0?minutes+"分":''+"</span>");
    	return '';
    }
}
function taskStateLabel(state,progress){
	progress = progress||'';
	var json={10:'<label class="label label-danger label-outline">待办中</label>',11:'<label class="label label-warning label-outline">已退回</label>',12:'<label class="label label-warning label-outline">任务暂停</label>',13:'<label class="label label-warning label-outline">已取消</label>',20:'<label class="label label-info label-outline">进行中'+progress+'</label>',30:'<label class="label label-warning">已完成</label>',40:'<label class="label label-success">已验收</label>',42:'<label class="label label-danger">验收不通过</label>',43:'<label class="label label-danger">已挂起</label>',44:'<label class="label label-danger">已取消</label>'};
	var result=json[state]||'';
	if(result){
		return result;
	}else{
		return '';
	}
}
function followLevelLabel(level){
	var json={1:'<label class="label label-danger label-outline">最高</label>',2:'<label class="label label-warning label-outline">较高</label>',3:'<label class="label label-info label-outline">一般</label>',4:'<label class="label label-warning">较低</label>'};
	var result=json[level]||'';
	if(result){
		return result;
	}else{
		return '';
	}
}
function noticeLabel(state){
	var json={1:'公告',2:'动态 ',3:'分享',4:'问答',5:'招聘'};
	var result=json[state]||'';
	if(result){
		return result;
	}else{
		return '其他';
	}
}

function taskState(state){
	var json={10:'待办中',11:'已退回',20:'进行中 ',30:'已完成',40:'已验收',42:'验收不通过',43:'已挂起',44:'已取消'};
	var result=json[state]||'';
	if(result){
		return result;
	}else{
		return '';
	}
}

function flowApplyState(val){
	if(val==0)return '<label class="label label-default">草稿</label>';
	if(val==1)return '<label class="label label-danger">已作废</label>';
	if(val==5)return '<label class="label label-danger">已挂起</label>';
	if(val==10)return '<label class="label label-warning">待审批</label>';
	if(val==20)return '<label class="label label-info">审批中</label>';
	if(val==21)return '<label class="label label-danger">审批退回</label>';
	if(val==30)return '<label class="label label-success">审批通过</label>';
	return "--";
}

function flowApplyStateText(val){
	if(val==0)return '草稿';
	if(val==1)return '作废';
	if(val==5)return '已挂起';
	if(val==10)return '待审批';
	if(val==20)return '审批中';
	if(val==21)return '审批退回';
	if(val==30)return '审批通过';
	return "--";
}

function todoLevel(val){
	if(val==1)return '<label class="label label-default">一般</label>';
	if(val==2)return '<label class="label label-warning">最高</label>';
	if(val==3)return '<label class="label label-danger">较高</label>';
	if(val==4)return '<label class="label label-info">较低</label>';
	return "--";
}
function taskLevel(val){
	if(val==1)return '<label class="label label-default">较低</label>';
	if(val==2)return '<label class="label label-warning">普通</label>';
	if(val==3)return '<label class="label label-danger">紧急</label>';
	if(val==4)return '<label class="label label-info">非常紧急</label>';
	return "--";
}
function taskTextLevel(val){
	if(val==1)return '<span class="text-default">较低</span>';
	if(val==2)return '<span class="text-warning">普通</span>';
	if(val==3)return '<span class="text-danger">紧急</span>';
	if(val==4)return '<span class="text-info">非常紧急</span>';
	return "--";
}
function projectLevel(val){
	if(val==1)return '<label class="label label-default">正常</label>';
	if(val==2)return '<label class="label label-warning">紧急</label>';
	if(val==3)return '<label class="label label-danger">重要</label>';
	if(val==4)return '<label class="label label-info">重要且紧急</label>';
	return "--";
}
function taskLevelText(val){
	if(val==1)return '较低';
	if(val==2)return '普通';
	if(val==3)return '严重';
	if(val==4)return '非常严重';
	return "--";
}
function renderSelect(){
	var select=$("body").find("select");
	if(select.leng>0&&requreLib){
		requreLib.setplugs('select2',function(){
			select.select2({theme: "bootstrap"});
		});
	}
}
function downloadLogLayer(fkId,fileId){
	popup.layerShow({url:'/yq-work/pages/common/download-log.jsp',full:fullShow(),area:['700px','500px'],scrollbar:false,offset:'20px',data:{fkId:fkId,fileId:fileId},id:'downloadLogLayer',title:'下载日志'});
}
function loadDownloadLog(option){
	var formId=option.formId;
	var tableId=option.tableId;
	$("#"+formId).initTable({
		mars:'CommonDao.downloadLog',
		id:tableId,
		data:option,
		cols: [[
         {
    	 	type: 'numbers',
			title: '序号',
			align:'left'
		 },{
		    field: 'FILE_NAME',
			title: '文件名',
			align:'left'
		},
		{
		    field: 'FILE_SIZE',
			title: '文件大小',
			width:80,
			align:'center'
		},
		{
		    field: 'DOWNLOAD_BY_NAME',
			title: '下载人',
			align:'left',
			width:70
		},{
		    field: 'IP',
			title: '来源IP',
			width:100,
			align:'left'
		},{
		    field: 'DOWNLOAD_TIME',
			title: '下载时间',
			width:145,
			align:'center'
		}
		]]}
	);
}

function lookLogLayer(fkId){
	popup.layerShow({url:'/yq-work/pages/common/look-log.jsp',full:fullShow(),area:['600px','450px'],scrollbar:false,offset:'20px',data:{fkId:fkId},id:'lookLogLayer',title:'查看日志'});
}

function loadLookLog(option){
	var formId=option.formId;
	var tableId=option.tableId;
	$("#"+formId).initTable({
		mars:'CommonDao.lookLogList',
		id:tableId,
		data:option,
		cols: [[
         {
    	 	type: 'numbers',
			title: '序号',
			align:'left'
		 },{
		    field: 'LOOK_BY',
			title: '查看人',
			align:'center',
			width:80,
			templet:function(row){
				return getUserName(row.LOOK_BY);
			}
		},{
		    field: 'LOOK_TIME',
			title: '查看时间',
			align:'center'
		},{
		    field: 'IP',
			title: '访问ip',
			align:'left'
		},{
		    field: 'DEVICE',
			title: '访问设备',
			hide:true,
			align:'left'
		}
		]]}
	);
}
function loadOpLog(option){
	var formId=option.formId;
	var tableId=option.tableId;
	$("#"+formId).initTable({
		mars:'CommonDao.lookOpList',
		id:tableId,
		data:option,
		page:false,
		cols: [[
		        {
		        	type: 'numbers',
		        	title: '序号',
		        	align:'center'
		        },{
		        	field: 'CONTENT',
		        	title: '操作内容',
		        	align:'center'
		        },{
		        	field: 'OP_BY',
		        	title: '操作人',
		        	align:'center',
		        	templet:function(row){
		        		return getUserName(row.OP_BY);
		        	}
		        },{
		        	field: 'OP_TIME',
		        	title: '操作时间',
		        	align:'center'
		        }
		   ]]}
	);
}

var currentUserId = '';
function getDelFileElement(userId,fileId){
	if(currentUserId==''){
		currentUserId = getCurrentUserId();
	}
	if(currentUserId==userId){
		return '<a href="javascript:;" style="color:#17a6f0;font-size:12px;margin-left:5px;" onclick="delFile($(\'#tr_'+fileId+'\'))">删除</a>';
	}
	return '';
	
}

function delFile(obj,delParent){
	if(typeof(delParent)== 'undefined'){
		delParent = true;
	}
	var id = '';
	var flag = false;
	if(typeof(obj)=='string'){
		id = obj;
		flag = false;
	}else{
		flag = true;
		id=$(obj).data("id");
	}
	layer.confirm('确认删除?',{icon:3,offset:'50px'},function(index){
		layer.close(index);
		ajax.remoteCall("/yq-work/servlet/upload?action=del",{id:id},function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:1200},function(){
					if(flag){
						if(delParent){
							$(obj).parent().remove();
						}else{
							$(obj).remove();
						}
					}else{
						reloadFile();
						layer.closeAll();
					}
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	});
}
		
function easyUploadFile(options){
	var callback=options.callback;
	var formId=options.formId||'fileForm';
	var fileId=options.fileId||'localfile';
	if(callback&&$.isFunction(window[callback])){
		
	}else{
		layer.alert("必须填写正确的回调方法！",{icon:7},function(index){
			layer.close(index);
			return;
		});
	}
	var fileList = document.getElementById(fileId).files;
	if(fileList.length<=0){
    	alert("请选择上传的文件!")
    	return;
	}
	for(var i=0;i<fileList.length;i++){
		var fileObj=fileList[i];
		if(!checkfile(fileObj,options)){
			return;
		}
	}
	var formData = new FormData($("#"+formId)[0]); 
	var params=$.extend({},options);
	var paramStr=jQuery.param(params);
	$.ajax({  
          url: '/yq-work/servlet/upload?'+paramStr,  
          type: 'POST',  
          data: formData,async: false,cache: false,contentType: false,processData: false,  
          success: function (result) {
		    	 layer.msg(result.msg,{time:1000,offset:'rb',icon:1},function(){
		    		 layer.closeAll('dialog');
			    	 if(result.state  == 1){
			    		 window[callback](result.data,params);
			    	 }
		    	 });
          },error: function (returndata) {  
	             layer.msg('上传失败!'); 
	             layer.closeAll('dialog');
          },beforeSend:function(){
        	    layer.msg('正在上传', {icon: 16,time:1000*100,offset:'30px'});
          },complete:function(){
        	    layer.closeAll('loading');
          }  
     }); 
}

function getContent(val){
	if(val){
		if(isArray(val)){
		    var str = [];
			for(var index in val){
				str.push(val[index]);
			}
			val= str.join(',');
		}
		return val.replace(/\n|\r\n/g,'<br/>');
	}else{
		return '--';
	}
}
		
function rowEvent(data,obj){
	obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
	obj.tr.find('i[class="layui-anim layui-icon layui-icon-circle"]').trigger("click");
}

function checkfile(fileObj,options){
	//校验文件类型
	var filename=fileObj.name.toLowerCase(); 
	var fileType=options.fileType;
	if(fileType!=null&&fileType!=undefined&&fileType!=''){
		var fileExtension = filename.substring(filename.lastIndexOf('.') + 1);
		fileExtension=fileExtension.toLowerCase();
		if(fileType.indexOf(fileExtension)==-1){
			alert("上传仅支持格式："+fileType);
			return false;
		}
	}
	//校验文件大小
	var fileMaxSize=options.fileMaxSize;
	if(fileMaxSize!=null&&fileMaxSize!=undefined&&fileMaxSize!=''){
		return checkFileSize(fileObj,fileMaxSize);
	}
	return true;
}
	
var isIE = /msie/i.test(navigator.userAgent) && !window.opera; 
function checkFileSize(target,fileMaxSize) { 
	var fileSize = 0; 
	if (isIE && !target.files) { 
		target.select(); target.blur();
		var filePath = document.selection.createRange().text;
		var fileSystem = new ActiveXObject("Scripting.FileSystemObject"); 
		if(!fileSystem.FileExists(filePath)){ 
			alert("附件不存在，请重新输入！"); 
			return false; 
		} 
		var file = fileSystem.GetFile(filePath); 
		fileSize = file.Size; 
	} else { 
		fileSize = target.size; 
	}  
	var size = fileSize / 1024; 
	if(size>fileMaxSize){ 
		if(fileMaxSize>=1024){
			alert("文件大小限制"+(fileMaxSize/1024).toFixed(2)+"M内！"); 
		}else{
			alert("文件大小限制"+fileMaxSize+"k内！"); 
		}
		target.value =""; 
		return false; 
	} 
	return true;
} 

var digitUppercase = function(n) {
	    var fraction = ['角', '分'];
	    var digit = [
	        '零', '壹', '贰', '叁', '肆',
	        '伍', '陆', '柒', '捌', '玖'
	    ];
	    var unit = [
	        ['元', '万', '亿'],
	        ['', '拾', '佰', '仟']
	    ];
	    var head = n < 0 ? '欠' : '';
	    n = Math.abs(n);
	    var s = '';
	    //分隔小数点
		var fen = String(n).split('.')
		if(fen.length == 2){//有分号
			fen = fen[1].split('');//把小数点分隔
		    for (var i = 0; i < fraction.length; i++) {
		    	let num = fen[i]
		    	if(typeof(num)!= 'undefined'){
			    	let ss = (digit[num] + fraction[i]).replace(/零./, '')
			        s += ss;
		    	}
		    }
		}
	    s = s || '整';
	    n = Math.floor(n);
	    for (var i = 0; i < unit[0].length && n > 0; i++) {
	        var p = '';
	        for (var j = 0; j < unit[1].length && n > 0; j++) {
	            p = digit[n % 10] + unit[1][j] + p;
	            n = Math.floor(n / 10);
	        }
	        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
	    }
	    return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
}
	

function renderDate(element){
	element = element ||'form';
	var laydates=$($g(element)).find("[data-laydate]");
	if(laydates.length>0){
		layui.use('laydate', function(){
			var laydate = layui.laydate;
			laydates.each(function(){
				  var obj=$(this);
				  var config=eval('(' + obj.data("laydate") + ')');
				  laydate.render($.extend({},{elem:this},config));
		    });
		});
	}
}

function dateIdFormat(dateString){
	if(dateString){
		var pattern = /(\d{4})(\d{2})(\d{2})/;
		var formatedDate = dateString.replace(pattern, '$1-$2-$3');
		return formatedDate;
	}else{
		return '';
	}
}

function selectUser(el,type,option){
	if (option === undefined) option = {};
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	var data = $.extend($(el).data(),option,{sid:id,type:type});
	var height = '600px';
	if(type=='radio'){
		height = '530px';
	}
	popup.layerShow({id:'selectUser',full:fullShow(),maxmin:true,scrollbar:false,area:['800px',height],offset:'20px',title:'选择用户',url:'/yq-work/pages/flow/select/select-user.jsp',data:data});
}

function singleUser(el,option){
	selectUser(el,'radio',option);
}

function multiUser(el,option){
	selectUser(el,'checkbox',option);
}

function selectDept(el,type){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	addClearTag(el);
	var data = $.extend($(el).data(),{sid:id,type:type});
	popup.layerShow({id:'selectDept',full:fullShow(),scrollbar:false,area:['500px','500px'],offset:'20px',title:'选择部门',url:'/yq-work/pages/flow/select/select-dept.jsp',data:data});
}

function selectCenter(el,type){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	addClearTag(el);
	var data = $.extend($(el).data(),{sid:id,type:type});
	popup.layerShow({id:'selectCenter',full:fullShow(),scrollbar:false,area:['500px','500px'],offset:'20px',title:'选择中心',url:'/yq-work/pages/flow/select/select-center.jsp',data:data});
}

function selectCust(el,type){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	addClearTag(el);
	popup.layerShow({id:'selectCust',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择客户',url:'/yq-work/pages/flow/select/select-cust.jsp',data:{sid:id,type:type}});
}

function selectBxDept(el){
	var type = 'radio';
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	popup.layerShow({id:'selectDept',full:fullShow(),scrollbar:false,area:['500px','500px'],offset:'20px',title:'选择部门',url:'/yq-work/pages/flow/select/select-bx-dept.jsp',data:{sid:id,type:type}});
}

function singleDept(el){
	selectDept(el,'radio');
}

function multiDept(el){
	selectDept(el,'checkbox');
}

function singleCenter(el){
	selectCenter(el,'radio');
}

function multiCenter(el){
	selectCenter(el,'checkbox');
}

function addClearTag(el){
	var nextEl = $(el).next();
	if(nextEl.length>0&&nextEl.hasClass('select-clear')){
		nextEl.remove();
	}
	$(el).parents('td').css('position','relative');
	//$(el).parents('div').css('position','relative');
	$(el).css('padding-right','15px');
	$(el).after('<span class="select-clear" onclick="clearSelectValue(this);">X</span></div>');
}

function clearSelectValue(el){
	$(el).prev().val('');
	$(el).prev().prev().val('');
	
	var firstEl = $(el).prev().prev().prev();
	if(firstEl.length>0){
		if(firstEl.is('input')){
			firstEl.val('');
		}
	}
	var extendFun = $(el).prev().attr('data-fn');
	if(typeof(extendFun)!='undefined'){
		excuteFn(extendFun);
	}
	$(el).remove();
}


function singleCust(el){
	selectCust(el,'radio');
}

function singleSj(el,sjCustId){
	if (sjCustId === undefined) sjCustId = '';
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	addClearTag(el);
	popup.layerShow({id:'selectSj',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择商机',url:'/yq-work/pages/flow/select/select-sj.jsp',data:{sid:id,type:'radio',sjCustId:sjCustId}});

}

function multiCust(el){
	selectCust(el,'checkbox');
}

function selectContract(el,type){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	popup.layerShow({id:'selectContract',full:fullShow(),scrollbar:false,area:['650px','500px'],offset:'20px',title:'选择合同',url:'/yq-work/pages/flow/select/select-contract.jsp',data:{sid:id,type:type}});
}

function singleService(el){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	popup.layerShow({id:'singleService',full:fullShow(),scrollbar:false,area:['600px','500px'],offset:'20px',title:'选择自研产品',url:'/yq-work/pages/flow/select/select-service.jsp',data:{sid:id,type:'radio'}});
}

function singleContract(el){
	selectContract(el,'radio');
}

function multiContract(el){
	selectContract(el,'checkbox');
}
function singleProject(el){
	selectProject_(el,'radio');
}

function multiProject(el){
	selectProject_(el,'checkbox');
}

function selectProject_(el,type){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	popup.layerShow({id:'selectProject',full:fullShow(),scrollbar:false,area:['650px','600px'],offset:'20px',title:'选择项目',url:'/yq-work/pages/flow/select/select-project.jsp',data:{sid:id,type:type},end:function(){

	}});
}

function selectZentaoBug(el,type){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	popup.layerShow({id:'selectProject',full:fullShow(),scrollbar:false,area:['650px','600px'],offset:'20px',title:'选择禅道BUG',url:'/yq-work/pages/flow/select/select-bug.jsp',data:{sid:id,type:type}});
}

function singleZentaoBug(el){
	selectZentaoBug(el,'radio');
}

function multiZentaoBug(el){
	selectZentaoBug(el,'checkbox');
}


function selectDict(el,type,dictId){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	addClearTag(el);
	popup.layerShow({id:'selectDict',full:fullShow(),scrollbar:false,area:['450px','500px'],offset:'20px',title:'选择字典',url:'/yq-work/pages/flow/select/select-dict.jsp',data:{sid:id,type:type,dictId:dictId}});
}

function selectText(el){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	popup.layerShow({id:'selectDict',full:fullShow(),scrollbar:false,area:['450px','500px'],offset:'20px',title:'请选择',url:'/yq-work/pages/flow/select/select-text.jsp',data:{sid:id}});
}

function singleDict(el,dictId){
	selectDict(el,'radio',dictId);
}

function multiDict(el,dictId){
	selectDict(el,'checkbox',dictId);
}


function singleBusinessPlatForm(el){
	selectBusinessPlatForm(el,'radio');
}

function multiBusinessPlatForm(el){
	selectBusinessPlatForm(el,'checkbox');
}

function selectBusinessPlatForm(el,type){
	var id = new Date().getTime();
	$(el).attr('data-sid',id);
	addClearTag(el);
	popup.layerShow({id:'selectBusinessPlatForm',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择业务平台',url:'/yq-work/pages/flow/select/select-business-platform.jsp',data:{sid:id,type:type}});
}

function flowApplyLink(flowCode){
	flowApply(flowCode,{});
}

function flowApply(flowCode,params){
	if(flowCode==''){
		popup.openTab({url:'/yq-work/pages/flow/flow-apply.jsp',title:'发起流程',id:'flow_apply'})
		return;
	}
	ajax.remoteCall("/yq-work/web/flow/getFlow",{flowCode:flowCode},function(result) { 
		if(result.state == 1){
			var data = result.data;
			var title = data.flowName;
			popup.openTab({id:flowCode,url:'/yq-work/web/flow?flowCode='+flowCode,title:title,data:$.extend({},params,{handle:'add'})});
		}else{
			layer.alert(result.msg,{icon: 5});
		}
	});
 }

function fastSelectDateArray(type){
	var array =   [
      { text: "今天", value: Date.now()},
	 {
	    text: "昨天",
        value: function(){
          var now = new Date();
          now.setDate(now.getDate() - 1);
          return now;
        }()
      },
      {
	    text: "一周内",
        value: function(){
          var now = new Date();
          now.setDate(now.getDate() - 7);
          return now;
        }()
      },
 	 {
        text: "近半月",
        value: function(){
          var now = new Date();
          now.setDate(now.getDate() - 15);
          return [now];
        }()
      },
		{
        text: "近一月",
        value: function(){
          var now = new Date();
          now.setDate(now.getDate() - 31);
          return [now];
        }()
      },
      {
        text: "上个月",
        value: function(){
          var now = new Date();
          now.setMonth(now.getMonth() - 1);
          return [now];
        }()
      },
	  {
        text: "近三个月",
        value: function(){
          var now = new Date();
          now.setMonth(now.getMonth() - 3);
          return [now];
        }()
      },{
        text: "近半年",
        value: function(){
          var now = new Date();
          now.setMonth(now.getMonth() - 6);
          return [now];
        }()
    }];
	if(type=='theLastMonth'){
		array = array.slice(0, 5);
	}
	return array;
}

function textareaEditLayer(el){
   var content = $(el).val();
   var data = $(el).data();
   var device = layui.device();
	var offset = '20px';
   var wh = ['420px', '220px'];
   if(device.mobile){
		wh = [($(window).width()-30)+'px', ($(window).height()-100)+'px'];
		offset = '0px';
   }
   var index  = layer.prompt($.extend({formType: 2,full:device.mobile,value: content,offset:offset,title: '填写信息',area: wh},data), function(newContent, index, elem){
	 	layer.close(index);
	    $(el).val(newContent);	  	
   });
	if(device.mobile){
		layer.full(index);
	}
}


function getStageCompleteStatus(planDate, completeDate ){
	if(planDate == "" || planDate == undefined){
		if(completeDate != ""){
			return "<label class='label label-success'>已完成</label>";
		}
		return "<label class='label label-info'>待收款</label>";
	}
	var today = new Date();
	var plan = new Date(planDate);
	var timeDiff = plan.getTime() - today.getTime() ;
	var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
	if(completeDate != ""){
		if(completeDate > planDate){
			return "<label class='label label-success label-outline'>超时完成</label>";
		}
		return "<label class='label label-success'>已完成</label>";
	}else if(days < 7 && days >= 0){
		return "<label class='label label-warning'>即将到期</label>";
	}else if(days<0){
		return "<label class='label label-danger'>已超时</label>";
	} else {
		return "<label class='label label-info'>待收款</label>";
	}
}

function getIncomeStageStatus(confirmFlag,planDate,completeDate){
	if(planDate == "" || planDate == undefined){
		return "<label class='label label-info'>待收入确认</label>";
	}
	var today = new Date();
	var plan = new Date(planDate);
	var timeDiff = plan.getTime() - today.getTime() ;
	var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
	if(confirmFlag == '1'){
		if(completeDate > planDate){
			return "<label class='label label-success label-outline'>超时完成</label>";
		}
		return "<label class='label label-success'>已完成</label>";
	}else if(days < 7 && days >= 0){
		return "<label class='label label-warning'>即将到期</label>";
	}else if(days<0){
		return "<label class='label label-danger'>已超时</label>";
	} else {
		return "<label class='label label-info'>待收入确认</label>";
	}
}

function subContractName(contractName) {
	if (contractName.length > 10) {
		if (/^\d{4}-\d{4}/.test(contractName.substring(0, 9))) {
			return contractName.substring(0, 15) + "...";
		} else if (/^\d{4}/.test(contractName.substring(0, 4))) {
			return contractName.substring(0, 13) + "...";
		}
		return contractName.substring(0, 10) + "...";
	} else {
		return contractName;
	}
}

$('*[lay-tips]').on('mouseenter', function () {
	var content = $(this).attr('lay-tips');
	this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
		time: -1
		, maxWidth: 280
		, tips: [3, '#3A3D49']
	});
}).on('mouseleave', function () {
	layer.close(this.index);
});


$(function(){
	//renderSelect();
	initUserList();
	getCurrentUserId();


});


