package com.yunqu.work.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.FolderModel;
/**
 * 文档
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/folder/*")
public class FolderServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	
	public EasyResult actionForAdd(){
		FolderModel model=getModel(FolderModel.class, "folder");
		String id=getJsonPara("id");
		if(StringUtils.isBlank(id)){
			model.set("p_folder_id", "0");
		}else{
			model.set("p_folder_id", id);
		}
		model.setPrimaryValues(RandomKit.uuid());
		model.addCreateTime();
		model.setCreator(getUserPrincipal().getUserId());
		model.set("manager",getUserPrincipal().getUserId());
		model.set("update_time",EasyDate.getCurrentDateString());
		try {
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		FolderModel model=getModel(FolderModel.class, "folder");
		try {
			model.set("update_time",EasyDate.getCurrentDateString());
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		FolderModel model=getModel(FolderModel.class, "folder");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}





