package com.yunqu.work.dao.ehr;

import java.util.Map;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.service.OAService;
import com.yunqu.work.utils.XmlToJsonUtils;

@WebObject(name="OaDao")
public class OaDao extends AppDaoContext {
	
	/**
	 * 我的所有提交的流程
	 * @return
	 */
	@WebControl(name="loadMyAllWfXml",type=Types.OTHER)
	public JSONObject loadMyAllWfXml(){
		Map<String, String> cookies = OAService.getService().getUserCookie(getLoginAcct());
		if(cookies==null){
			return getJsonResult(null);
		}
		String result=OAService.getService().loadMyWfXml(getLoginAcct());
		if(result!=null){
			result = result.replaceAll("\n", "");
			result = result.replaceAll("\t", "");
			
			try {
				JSONObject jsonObject = XmlToJsonUtils.xmltoJson(result);
				return getJsonResult(jsonObject);
			} catch (Exception e) {
				return getJsonResult(null);
			}
		}else{
			return getJsonResult(null);
		}
	}
	/*
	 * 待我审批的
	 */
	@WebControl(name="loadMyDoXml",type=Types.OTHER)
	public JSONObject loadMyDoXml(){
		Map<String, String> cookies = OAService.getService().getUserCookie(getLoginAcct());
		if(cookies==null){
			return getJsonResult(null);
		}
		String result=OAService.getService().loadMyDoXml(getLoginAcct());
		if(result!=null){
			result = result.replaceAll("\n", "");
			result = result.replaceAll("\t", "");
			
			try {
				JSONObject jsonObject = XmlToJsonUtils.xmltoJson(result);
				return getJsonResult(jsonObject);
			} catch (Exception e) {
				return getJsonResult(null);
			}
		}else{
			return getJsonResult(null);
		}
	}

}
