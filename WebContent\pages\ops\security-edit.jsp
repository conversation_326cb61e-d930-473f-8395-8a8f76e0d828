<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>版本</title>
	<style>
		#SecurityEditForm table,tr,td,p{max-width: 100%!important;}
		#SecurityEditForm img{max-width: 98%!important;height: auto;}
		#SecurityEditForm .w-e-text{max-width: 100%!important;}
		.file-div{display: block;float: inherit;}
		.w-e-toolbar p, .w-e-text-container p, .w-e-menu-panel p {
		    font-size: 14px !important;
		}
	</style>
	<link href="/yq-work/static/css/monokai_sublime.min.css" rel="stylesheet">
	<script src="/yq-work/static/js/highlight.min.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="SecurityEditForm" data-mars="VersionDao.securityRecord" autocomplete="off" data-mars-prefix="security.">
     		    <input type="hidden" value="${param.securityId}" id="securityId" name="security.SECURITY_ID"/>
     		    <input type="hidden" value="${param.groupId}"  name="security.GROUP_ID"/>
     		    <input type="hidden" name="security.SECURITY_STATE"/>
     		    <input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
     		    <input type="hidden" id="dateId" data-mars="CommonDao.date03"/>
 		   		<input type="hidden" value="${param.securityId}" name="fkId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			           <tr>
		                    <td style="width: 80px" class="required">漏洞名称</td>
		                    <td><input data-rules="required" type="text" name="security.SECURITY_TITLE" id="securityTitle" value="" class="form-control input-sm"></td>
			            </tr>
			             <tr>
		               	<td>漏洞类型</td>
		               	<td>
		               		<select data-rules="required" class="form-control input-sm" name="security.SECURITY_TYPE">
		               			<option value="">--</option>
		               			<option value="反射型xss">反射型xss</option>
								<option value="存储型xss">存储型xss</option>
								<option value="设计缺陷/逻辑错误">设计缺陷/逻辑错误</option>
								<option value="未授权的访问/权限绕过">未授权的访问/权限绕过</option>
								<option value="敏感信息泄漏">敏感信息泄漏</option>
								<option value="csrf">csrf</option>
								<option value="url跳转">url跳转</option>
								<option value="登录接口认证漏洞">登录接口认证漏洞</option>
								<option value="sql注入">sql注入</option>
								<option value="拒绝服务">拒绝服务</option>
								<option value="弱口令">弱口令</option>
								<option value="命令执行">命令执行</option>
								<option value="json**">json**</option>
								<option value="任意文件上传漏洞">任意文件上传漏洞</option>
								<option value="文件包含">文件包含</option>
								<option value="文件遍历/下载">文件遍历/下载</option>
								<option value="目录遍历">目录遍历</option>
								<option value="ssrf">ssrf</option>
								<option value="webshell">webshell</option>
								<option value="crlf注入">crlf注入</option>
								<option value="管理后台对外">管理后台对外</option>
								<option value="不安全加密算法">不安全加密算法</option>
								<option value="不安全的第三方资源引用">不安全的第三方资源引用</option>
								<option value="配置错误">配置错误</option>
								<option value="cookie设置不当">cookie设置不当</option>
								<option value="第三方应用软件漏洞">第三方应用软件漏洞</option>
								<option value="在站外泄露敏感信息">在站外泄露敏感信息</option>
								<option value="ifame页面引用">ifame页面引用</option>
								<option value="会话定制">会话定制</option>
								<option value="seo暗链">seo暗链</option>
								<option value="配置错误">配置错误</option>
								<option value="访问控制漏洞">访问控制漏洞</option>
								<option value="系统基础软件漏洞">系统基础软件漏洞</option>
								<option value="未授权的访问/权限绕过">未授权的访问/权限绕过</option>
								<option value="其他">其他</option>
		               		</select>
		               	</td>
		               </tr>
			            <tr>
		                    <td style="width: 80px" class="required">所属模块</td>
		                    <td>
				     		    <input type="hidden" value="${param.projectId}"  name="security.PROJECT_ID"/>
		                    	<input data-rules="required" type="text" onclick="singleService(this)" readonly="readonly" name="security.PROJECT_NAME"  value="${param.projectName}" class="form-control input-sm">
		                    </td>
			            </tr>
			            
            	        <tr>
		                    <td>待办人</td>
		                    <td>
		                    	<input type="hidden" name="receiverIds"/>
		                    	<input type="text" data-rules="required" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="security.RECEIVER_NAME"/>
		                    </td>
		               </tr>
            	        <tr>
		                    <td>抄送人</td>
		                    <td>
		                    	<input type="hidden" name="security.RECIPIENT_ID"/>
		                    	<input type="text" data-rules="required" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="security.RECIPIENT_NAME"/>
		                    </td>
		               </tr>
			           <tr>
		                    <td style="width: 80px" class="required">漏洞等级</td>
		                    <td>
		                    	<select data-rules="required" name="security.SECURITY_LEVEL" class="form-control input-sm">
								  <option value="低">低</option>
								  <option value="中">中</option>
								  <option value="高">高</option>
								  <option value="严重">严重</option>
		                    	</select>
		                    </td>
			            </tr>
		                <tr>
			                <td style="vertical-align: top;">相关文件</td>
			               	<td>
			               		 <div data-template="v-template-files" data-mars="FileDao.fileList"></div>
			               		 <div id="vfileList"></div>
								 <button class="btn btn-xs btn-info mt-5" type="button" onclick="$('#vlocalfile').click()">+批量添加</button>
			               	</td>
	            	    </tr>
			            <tr>
		                    <td class="required" style="vertical-align: top;">漏洞描述<br>
		                    	<a href="javascript:;" class="hidden mr-5" onclick="$('.w-e-icon-fullscreen').click();">选择模板</a><br>
		                    	<a href="javascript:;" class="mr-5" onclick="$('.w-e-icon-fullscreen').click();">全屏编辑</a>
		                    </td>
		                    <td>
		                          <div id="editor"></div>
	                          	 <textarea id="wordText" data-text="false" style="height: 500px;width:400px;display: none;" class="form-control input-sm" name="security.VERSION_DESC">
	                          	 </textarea>
		                    </td>
			            </tr>
			        </tbody>
 			     </table>
				 <div class="layer-foot text-c" style="z-index: 99999;">
			    	  <button type="button" class="btn btn-default draft-btn btn-sm detail" onclick="SecurityEdit.ajaxSubmitForm(1)"> 保存草稿 </button>
			    	  <button type="button" class="btn btn-primary btn-sm ml-10 detail" onclick="SecurityEdit.ajaxSubmitForm(2)"> 提交发布 </button>
				</div>
  		</form>
  		
  		<div id="contentTpl" style="display: none;">
  			<p><b><font color="#c24f4a">【1】详细说明：</font></b></p>
  			<p>其中包括场景、截图、漏洞重现的方法，涉及账号相关漏洞，请提供测试账号，若复现过程复杂，可录制视频，上传至淘盘，附链接。</p>
  			<p><br/></p>
  			
  			<p><b><font color="#c24f4a">【2】漏洞证明：</font></b></p>
  			<p><br/></p>
  			
  			<p><b><font color="#c24f4a">【3】修复方案：</font></b></p>
  			<p><br/></p>

  			<p><b><font color="#c24f4a">【4】升级回退：</font></b></p>
			
  			<p><br/></p>
  			<p><br/></p>
  		
  		</div>
		
		 <script id="v-template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/> {{:CREATE_NAME}} 于 {{:CREATE_TIME}} 上传文件 【{{:FILE_NAME}}】 {{:FILE_SIZE}}<a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank">下载</a>({{:DOWNLOAD_COUNT}})<i title="删除" class="detail" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
		
  		<form  id="vfileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file"  multiple="multiple" type="file" id="vlocalfile" onchange="SecurityEdit.uploadFile()"/>
  		</form>
  		
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/pinyin.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
		
		jQuery.namespace("SecurityEdit");
		
		SecurityEdit.securityId='${param.securityId}';
		var securityId='${param.securityId}';
		var op='${param.op}';
		
		$(function(){
			var E = window.wangEditor;
			var editor = new E('#editor');
			editor.highlight = hljs;
			editor.config.menus = [
				'head', 'bold', 'fontSize', 'fontName', 'italic', 'underline', 'strikeThrough', 'indent', 'lineHeight', 'foreColor', 'backColor', 'link', 'list', 'todo', 'justify', 'quote', 'emoticon', 'image', 'video', 'table', 'code', 'splitLine', 'undo', 'redo'
	      	];
			weUpload(editor,{uploadImgMaxLength:3});
			editor.config.onchange = function (html) {
			     $("#wordText").val(html)
			}
			editor.create();
			
			$("#SecurityEditForm").render({success:function(result){
				if(securityId==''){
					editor.txt.append($('#contentTpl').html());
					
				}else{
					editor.txt.html($("#wordText").val());
					var data = result['VersionDao.securityRecord']['data'];
					var securityState = data['SECURITY_STATE'];
					if(securityState>=0){
						$('.draft-btn').remove();
					}
				}
				 
				 if(op=='detail'){
					$("#editor").html($("#wordText").val());
					$("#editor").css("color","#555").css("min-height","200px").css("border","1px solid #ddd").css("padding","10px");
					$("#SecurityEditForm input").attr("readonly","readonly").removeAttr("onClick");
					$(".detail").remove();
					$('#editor').css('border','1px solid #ddd').css('padding','10px');
				 }
				 
				 $(".w-e-text-container").css("height","160px");
				 $("#SecurityEditForm .w-e-text-container").css("height",$(window).height()-450);
				 
				 $("#SecurityEditForm .w-e-text-container").keydown(function(event) {
					  if(event.keyCode == "13") {
						 var height = $(this).height();
						 $(this).css('height',(height+20)+'px');
					  }
					   if(event.keyCode == 8) {
						 var height = $(this).height();
						 height=height>300?height:300;
						// $(this).css('height',(height-20)+'px');
					   }
				});
				 
			}});
		});
		
		SecurityEdit.ajaxSubmitForm = function(flag){
			if(form.validate("#SecurityEditForm")){
				if(SecurityEdit.securityId){
					SecurityEdit.updateData(flag); 
				}else{
					SecurityEdit.insertData(flag); 
				}
			};
		}
		
		SecurityEdit.uploadFile = function(){
			var fkId='';
			if(SecurityEdit.securityId){
				fkId=securityId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'vcallback',fileId:'vlocalfile',formId:'vfileForm',fkId:fkId,source:'security',fileMaxSize:1024*50});
			
		}
	
		var vcallback = function(result,params){
			var array = [];
			if(isArray(result)){
				array = result;
			}else{
				array[0] = result;
			}
			for(var index in array){
				var data = array[index];
				$("#vfileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span>'+data.name+'-('+data.size+')<a href="'+data.url+'" target="_blank">下载</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this),true)">x</i></div>');
			}
			ajax.remoteCall("${ctxPath}/servlet/security?action=updateFile",{id:params['fkId']},function(result) {
				
			});
		}
		
		SecurityEdit.insertData = function(flag) {
			$("#securityId").val($("#randomId").val());
			var data = form.getJSONObject("#SecurityEditForm");
			data['state'] = flag;
			ajax.remoteCall("${ctxPath}/servlet/security?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadVersion();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		SecurityEdit.updateData = function(flag) {
			var data = form.getJSONObject("#SecurityEditForm");
			data['state'] = flag;
			ajax.remoteCall("${ctxPath}/servlet/security?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						reloadVersion();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		
		

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>