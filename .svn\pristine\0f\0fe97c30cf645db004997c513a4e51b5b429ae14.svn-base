var FlowCore = {
	businessId:'',
	params:{},
	setting:{formId:'flowForm'},
	applyInfo:{},
	initPage:function(option){
		if (option === undefined) option = {};
		FlowCore.params = $.extend({businessId:getUrlParam('businessId'),handle:getUrlParam('handle'),flowCode:getUrlParam('flowCode'),resultId:getUrlParam('resultId')},option.data);
		FlowCore.setting =$.extend({hideApplyNo:false,textModel:true},option);
		var param = FlowCore.params;
		var setting = FlowCore.setting;
		if(param.flowCode==''){
			alert('flowCode is empty');
			$('body').html('');
		}
		FlowCore.businessId = param['businessId'];
		if(param.handle=='approval'){
			if(setting.textModel)$('#flowForm').attr('data-text-model','true');
			$('.upload-btn,.remindInfo,.unedit-hide').remove();
			
		}else if(param.handle=='edit'||param.handle=='add'){
			$("#flowForm input[type='text']").attr('autocomplete','off');
			$('.approve-result,.flowInfo').remove();
		}else{
			//详情
			if(setting.textModel)$('#flowForm').attr('data-text-model','true');
			$('.upload-btn,.remindInfo,.unedit-hide').remove();
		}
		
		var resultId = param.resultId;
		if(resultId!=''){
			if($("#flowForm [name='resultId']").length==0){
				$("#flowForm").prepend('<input name="resultId" id="resultId" type="hidden" value="'+resultId+'">');
			}
			ajax.remoteCall("/yq-work/servlet/flow/approve?action=getNodeInfoByResultId",{resultId:resultId},function(result) { 
				FlowCore.approveInfo  = result.data;
			});
		}
		
		$("#flowForm").prepend('<input name="doAction" id="doAction" type="hidden">');
		if($("#flowForm [name='businessId']").length==0){
			$("#flowForm").prepend('<input name="businessId" id="businessId" type="hidden" value="'+param.businessId+'">');
		}
		if($("#flowForm [name='flowCode']").length==0){
			$("#flowForm").prepend('<input name="flowCode" id="flowCode" type="hidden" value="'+param.flowCode+'">');
		}
		if($("#flowForm .upload-btn").length>0){
			FlowCore.uploadFile();
		}
		
		$("#flowForm").render({success:function(result){
			var baseInfo = result['FlowDao.baseInfo'].data;
			FlowCore.applyInfo = baseInfo;
			FlowCore.renderApplyInfo(baseInfo);
			renderDate('#flowForm');
			FlowCore.handleEditBtn(baseInfo);
			FlowCore.renderHeader(option);
			FlowCore.renderFile(param);
			option.success && option.success(result);
		}});
	},
	renderApplyInfo:function(applyInfo){
		var applyState = applyInfo['applyState']||'0';
		if($("#flowForm [name='applyState']").length==0){
			$("#flowForm").prepend('<input name="applyState" id="applyState" type="hidden" value="'+applyState+'">');
		}
	},
	renderHeader:function(option){
		var param = FlowCore.params;
		var setting = FlowCore.setting;
		var staffInfo = getStaffInfo();
		if(param.handle!='edit'&&param.handle!='add'){
			var applyInfo = FlowCore.applyInfo;
			var applyBy = applyInfo.applyBy;
			if(!setting.hideApplyNo){
				$('.flow-title').after("<div style='position: absolute;left: 10px;top: 26px;' class='visible-print'>流水号："+applyInfo.applyNo+"</div>");
			}
			var html = [];
			html.push('<div class="layui-row hidden-print layui-hide-xs flow-header"><div class="layui-col-md12">');
			html.push('<div class="pull-left">');
			
			if(param.handle=='approval'){
				html.push('<button class="btn btn-info btn-info btn-sm mr-10" onclick="FlowCore.approveLayer()" type="button"><i class="fa fa-paper-plane" aria-hidden="true"></i> 立即审批</button>');
				html.push('<button class="btn btn-default btn-sm mr-10" onclick="FlowCore.trustLayer()" type="button"><i class="fa fa-exchange" aria-hidden="true"></i> 委托</button>');
			}
			
			html.push('<button class="btn btn-default btn-sm approve-node" onclick="FlowCore.approveNode()" type="button"><i class="fa fa-hourglass" aria-hidden="true"></i> 审批流程</button>');
			html.push('<button class="btn btn-default btn-sm ml-10" onclick="FlowCore.historyApply()" type="button"><i class="fa fa-history" aria-hidden="true"></i> 历史申请</button>');
			html.push('<button class="btn btn-outline btn-info btn-sm ml-10" onclick="FlowCore.flowPrint()" type="button"><i class="fa fa-print" aria-hidden="true"></i> 打印</button>');
			html.push('<button class="btn btn-outline btn-info btn-sm ml-10" onclick="FlowCore.flowSaveImg()" type="button"><i class="fa fa-image" aria-hidden="true"></i> 保存图片</button>');
			html.push('<button class="btn btn-outline btn-info btn-sm ml-10" onclick="FlowCore.flowShare()" type="button"><i class="fa fa-share-alt" aria-hidden="true"></i> 分享链接</button>');
			html.push('<button class="btn btn-outline btn-info btn-sm ml-10 flowLog" onclick="FlowCore.flowLog();" type="button"><i class="fa fa-eye" aria-hidden="true"></i> 查看日志</button>');
			
			html.push($('.flow-btn').html());
			$('.flow-btn').remove();
			
			if(param.handle=='approval'){
				html.push($('.flow-approval-btn').html());
			}
			$('.flow-approval-btn').remove();
			
			html.push('</div>');
			
			html.push('<div class="pull-right">');
			
			html.push('流水号：'+applyInfo.applyNo);

			html.push('</div>');
			html.push('</div></div>');
			$("#flowForm").prepend(html.join(''));
			
			if(applyInfo.applyState=='30'){
				$('.flow-title').after('<div class="flow-state hidden-print"><img src="/yq-work/static/images/pass.png"></div>');
			}else if(applyInfo.applyState=='20'){
				$('.flow-title').after('<div class="flow-state hidden-print"><img src="/yq-work/static/images/approve.png"></div>');
			}
			
			$("#flowForm").append('<div class="text-r mr-20 f-12 visible-print"><span>打印人：'+staffInfo.userName+' '+staffInfo.staffNo+'</span><span class="ml-20">打印时间：'+getCurrentTime()+'</span></div>');
		}
		
	},
	renderFile:function(param){
		if(param.handle=='add'){
			return;
		}
		var type = 1;
		if(param.handle=='edit'){
			type = 0;
		}
		if($("#flowForm .fileList").length>0&&FlowCore.businessId){
			ajax.daoCall({loading:false,controls:['FileDao.fileList'],params:{fkId:FlowCore.businessId}},function(rs){
			var result = rs['FileDao.fileList'].data;
			for(var index in result){
				var data = result[index];
				if(type==0){
					$("#flowForm .fileList").append('<div class="file-div"><input name="fileIds" value='+data.FILE_ID+' type="hidden"/><span><a href="'+data.ACCESS_URL+'" target="_blank">'+data.FILE_NAME+'</a></span><i title="删除" data-id="'+data.FILE_ID+'" onclick="delFile($(this),true)">x</i></div>');
				}else{
					$("#flowForm .fileList").append('<div class="file-div"><input name="fileIds" value='+data.FILE_ID+' type="hidden"/><span><a href="'+data.ACCESS_URL+'" target="_blank">'+data.FILE_NAME+'</a></span></div>');
				}
			}
			if(param.handle!='edit'){
				if(result.length>0){
					$('.flowLog').after('<button class="btn btn-outline btn-info btn-sm ml-10 flowLog" onclick="FlowCore.flowFile();" type="button"><i class="fa fa-eye" aria-hidden="true"></i> 查看附件('+result.length+') </button>');
			   }
			}
		});
	 }
		
	},
	handleEditBtn:function(applyInfo){
		var param = FlowCore.params;
		var applyState = applyInfo['applyState'];
		var businessId = param.businessId;
		var handle = param.handle;
		if(param.handle=='edit'||param.handle=='add'){
			var array = [];
			array.push('<div class="layui-row hidden-print layui-hide-xs flow-header"><div class="layui-col-md12">');
			array.push('<div class="pull-left">');
			
			if(applyState=='0'||businessId==''){
				array.push('<button class="btn btn-sm btn-info mr-10 saveBtn" type="button" onclick="Flow.ajaxSubmitForm(\'submit\');"><i class="fa fa-paper-plane" aria-hidden="true"></i> 提交审批</button>');
				array.push('<button class="btn btn-sm btn-default mr-10 btn-outline draftBtn" type="button" onclick="Flow.ajaxSubmitForm(\'draft\');">保存草稿</button>');
			}else{
				if(handle=='edit'&&applyState!='0'){
					array.push('<button class="btn btn-sm btn-info mr-15 saveBtn" type="button" onclick="Flow.ajaxSubmitForm(\'edit\');">修改保存</button>');
				}
				if(handle=='edit'&&applyState=='21'){
					array.push('<button class="btn btn-sm btn-info mr-15 saveBtn" type="button" onclick="Flow.ajaxSubmitForm(\'submit\');">提交审批</button>');
				}
			}
			array.push($('.flow-btn').html());
			$('.flow-btn').remove();
			
			array.push('</div>');
			array.push('</div></div>');
			$("#flowForm").prepend(array.join(''));
		}
		
	},
	addCheckOrder:function(option) {
		if (option === undefined) option = {data:{nextNodeId:'',nextNodeCode:''}};
		var params = FlowCore.params;
		var businessId = params.businessId,resultId = params.resultId;
		if(businessId==''||resultId==''){
			layer.msg('请从审批页面入口进来',{icon:7,offset:'50px'});
			return;
		}
		var checkResult = $("[name='checkResult']:checked").val();
		if(checkResult==undefined){
			layer.msg('请选择审批结果',{icon:7,offset:'50px'});
			return;
		}
		var checkDesc = $("[name='checkDesc']").val();
		if(checkResult=='2'&&checkDesc==''){
			layer.msg('请输入退回理由',{icon:7,offset:'50px'});
			return;
		}else{
			if(checkDesc==''){
				checkDesc='同意';
			}
		}
		var ccIds = $("[name='ccIds']").val();
		var ccNames = $("[name='ccNames']").val();
		
		var reqUrl = option.reqUrl||'/yq-work/servlet/flow/approve/?action=doApprove';
		var data = $.extend(params,option.data,{checkResult:checkResult,checkDesc:checkDesc});
		data['ccIds'] = ccIds;
		data['ccNames'] = ccNames;
		ajax.remoteCall(reqUrl,data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:1200},function(){
					FlowCore.myTodoList();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		},{then:function(result){
			option.success && option.success(result);
		}});
  },
  myApplyList:function(){
		layer.closeAll();
		popup.closeTab({callback:function(){
			popup.openTab({id:'flow_my_apply',url:'/yq-work/pages/flow/my/flow-list.jsp',title:'我的申请',reload:true})
		}});
   },
  myTodoList:function(){
		layer.closeAll();
		popup.closeTab({callback:function(){
			popup.openTab({id:'flow_my_todo',url:'/yq-work/pages/flow/my/flow-todo.jsp',title:'我的待办',reload:true})
		}});
   },
   lookTrust:function(trustId){
		//todo
	},
   lookCC:function(resultId){
		//todo
  },
  ajaxSubmitForm:function(state){
		$("[name='doAction']").val(state);
		if(form.validate("#flowForm")){
			if(FlowCore.businessId){
				Flow.updateData(); 
			}else{
				Flow.insertData(); 
			}
		};
	},
	insertData:function(option){
		if (option === undefined) option = {};
		var data = option.data;
		if($.isEmptyObject(data)){
		     data = form.getJSONObject("#flowForm");
		}
		var reqUrl = option.reqUrl||'/yq-work/servlet/flow/approve?action=submitApply';
		ajax.remoteCall(reqUrl,toDbField(data,'apply'),function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:1200},function(){
					FlowCore.myApplyList();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		},{then:function(result){
			option.success && option.success(result);
		}});
	},
	updateData:function(option){
		if (option === undefined) option = {};
		var data = option.data;
		if($.isEmptyObject(data)){
		     data = form.getJSONObject("#flowForm");
		}
		var reqUrl = option.reqUrl||'/yq-work/servlet/flow/approve?action=updateApply';
		ajax.remoteCall(reqUrl,toDbField(data,'apply'),function(result) { 
			if(result.state == 1){
				option.success && option.success(result);
				layer.msg(result.msg,{icon:1,time:1200},function(){
					FlowCore.myApplyList();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		},{then:function(result){
			option.success && option.success(result);
		}});
	},
	uploadFile:function(){
		layui.use('upload', function(){
		 	  var upload = layui.upload;
			  var uploadInst = upload.render({
			    elem: '.upload-btn'
			    ,url: '/yq-work/servlet/upload?action=index'
			    ,number: 10
			    ,accept: 'file'
			    ,exts:'xls|xlsx|txt|jpg|zip|ppt|pptx|doc|docx|tar|png'
			    ,size:1024*50
				,multiple:false
			    ,field: 'flowFile'
			    ,data: {
					fkId:function(){
						return FlowCore.params.businessId||'';
					},
					source:'flow'
				}
			    ,done: function(res, index, upload){
				    layer.closeAll('loading');
				    if(res&&res.state==1){
				    	layer.msg(res.msg,{icon: 1,time:800},function(){
							callback(res.data);
						}); 
					}else{
						layer.alert(res.msg,{icon: 5});
					}
			    },before:function(){
					layer.load();
			    },allDone: function(obj){ 
				
				}
			    ,error: function(res, index, upload){
					layer.closeAll('loading');
			    	layer.alert("上传文件请求异常！",{icon: 5});
			    }
	      });
  	  });
	 var callback = function(result){
			var array = [];
			if(isArray(result)){
				array = result;
			}else{
				array[0] = result;
			}
			for(var index in array){
				var data = array[index];
				$(".upload-btn").before('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this),true)">x</i></div>');
			}
	}
  },
  approveLayer:function(){
	var flowCode = FlowCore.params.flowCode;
	popup.layerShow({url:'/yq-work/pages/flow/comm/approve-do.jsp',full:fullShow(),area:['500px','350px'],shadeClose:false,scrollbar:false,offset:'20px',data:{flowCode:flowCode},id:'approveLayer',title:'流程审批',success:function(layero, index){
		//加载是否抄送
		var approveInfo = FlowCore.approveInfo;
		var nowNode = approveInfo.nowNode;
		var ccIds = nowNode.ccIds;
		if(ccIds){
			$("[name='ccIds']").val(ccIds);
			$("[name='ccNames']").val(nowNode.ccNames);
		}
	}});
		
  },
 trustLayer:function(){
	popup.layerShow({url:'/yq-work/pages/flow/comm/trust-do.jsp',full:fullShow(),area:['500px','350px'],shadeClose:false,scrollbar:false,offset:'20px',data:{businessId:FlowCore.businessId},id:'trustLayer',title:'流程委托'});
 },
 submitTrust:function(){
	var trustUserId = $("[name='trustUserId']").val();
	var trustUserName = $("[name='trustUserName']").val();
	var trustReason = $("[name='trustReason']").val();
	if(trustUserId==''){
		layer.msg('请选择委托人',{icon:7});
		return;
	}
	if(trustReason==''){
		layer.msg('请选择委托原因',{icon:7});
		return;
	}
	var param = FlowCore.params;
	var data = $.extend(param,{trustUserId:trustUserId,trustUserName:trustUserName,trustReason:trustReason});
	ajax.remoteCall("/yq-work/servlet/flow/approve?action=submitTrust",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,function(){
					layer.closeAll();
					FlowCore.myTodoList();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
 },
  flowShare:function(){
	var applyInfo = FlowCore.applyInfo;
	var url = "http://"+location.host+'/yq-work/web/flow/'+FlowCore.businessId+"#"+applyInfo.applyTitle;
	var textarea = document.createElement('textarea');
    document.body.appendChild(textarea);
    // 隐藏此输入框
    textarea.style.position = 'fixed';
    textarea.style.clip = 'rect(0 0 0 0)';
    textarea.style.top = '10px';
    // 赋值
    textarea.value = url;
    // 选中
    textarea.select();
    // 复制
    document.execCommand('copy', true);
    // 移除输入框
    document.body.removeChild(textarea);
	layer.msg('复制成功:'+url,{icon:1,offset:'20px'});
  },
  flowSaveImg:function(){
     popup.layerShow({title:'请右键点击复制图片或另存为',content:'<div style="padding:10px;text-align:center;overflow-y:scroll;height:calc(100vh - 200px)"><div id="flowImg"></div></div>',scrollbar:false,area:['80%','80%'],success:function(){
		html2canvas(document.querySelector(".ibox-content"),{}).then(canvas => {
		    document.querySelector("#flowImg").appendChild(canvas);
		    $('canvas').css('width','90%');
		    $('canvas').css('height','90%');
		});
     }});
  },
  flowPrint:function(){
	if(Flow.flowPrint){
		Flow.flowPrint();
	}else{
		window.print();
	}
  },
  flowLog:function(){
	lookLogLayer(FlowCore.businessId);
  },
  flowFile:function(){
	popup.layerShow({url:'/yq-work/pages/common/file-view.jsp',full:fullShow(),area:['680px','400px'],scrollbar:false,offset:'20px',data:{fkId:FlowCore.businessId},id:'fileViewLayer',title:'查看文件'});
  },
  approveNode:function(){
	var flowCode = FlowCore.params.flowCode;
	popup.layerShow({url:'/yq-work/pages/flow/comm/approve-node.jsp',full:fullShow(),area:['600px','450px'],scrollbar:false,offset:'20px',data:{flowCode:flowCode},id:'approveNode',title:'流程审批节点'});
 },
 historyApply:function(){
	popup.layerShow({type:1,url:'/yq-work/pages/flow/comm/flow-history.jsp',full:fullShow(),area:['68%','100%'],scrollbar:false,offset:'r',data:FlowCore.params,id:'historyApply',title:'历史流程'});
 }
	
};




function checkResultLable(checkResult){
	 var json = {'1':'通过','2':'拒绝'};
	 return json[checkResult]||'待审批';
}

// 字符串的驼峰格式转下划线格式，eg：helloWorld => hello_world
function hump2Underline(s) {
    return s.replace(/([A-Z])/g, '_$1').toLowerCase()
}

// JSON对象的key值转换为下划线格式
function jsonToUnderline(obj) {
    if (obj instanceof Array) {
        obj.forEach(function (v, i) {
            jsonToUnderline(v)
        })
    } else if (obj instanceof Object) {
        Object.keys(obj).forEach(function (key) {
            var newKey = hump2Underline(key)
            if (newKey !== key) {
                obj[newKey] = obj[key]
                delete obj[key]
            }
            jsonToUnderline(obj[newKey])
        })
    }
    return obj
}

function toDbField(data,modelName){
	for(var key in data){
		if(key.indexOf(modelName+".")>-1){
			var newKey = hump2Underline(key);
			var val = data[key];
			delete data[key];
			data[newKey] = val;
		}
	}
	return data;
}

 var dateDiff =  function (time1,time2){
		if(time1==''||time2==''){
			return '';
		}
        var time1 = new Date(time1);    //转换为中国标准时间
        var time2 = new Date(time2);
        var time1 = time1.getTime();    //转换为时间戳
        var time2 = time2.getTime();
        var runTime = (time2 - time1) / 1000;       //开始得出时间差,然后计算
        var year = Math.floor(runTime / 86400 / 365);       
        runTime = runTime % (86400 * 365);
        var month = Math.floor(runTime / 86400 / 30);
        runTime = runTime % (86400 * 30);
        var day = Math.floor(runTime / 86400);
        runTime = runTime % 86400;
        var hour = Math.floor(runTime / 3600);
        runTime = runTime % 3600;
        var minute = Math.floor(runTime / 60);
        runTime = runTime % 60;
        var second = runTime;
		
		var array = [];
		if(year>0){
			array.push(year+'年');
		}
		if(month>0){
			array.push(month+'月');
		}
		if(day>0){
			array.push(day+'日');
		}
		if(hour>0){
			array.push(hour+'时');
		}
		if(minute>0){
			array.push(minute+'分');
		}
		if(second>0){
			array.push(second+'秒');
		}

        return array.join('');
}

 
function getCurrentTime() {
    var date = new Date();//当前时间
    var year = date.getFullYear() //返回指定日期的年份
    var month = repair(date.getMonth() + 1);//月
    var day = repair(date.getDate());//日
    var hour = repair(date.getHours());//时
    var minute = repair(date.getMinutes());//分
    var second = repair(date.getSeconds());//秒
    
    //当前时间 
    var curTime = year + "-" + month + "-" + day
            + " " + hour + ":" + minute + ":" + second;
    return curTime;
}
function repair(i){
    if (i >= 0 && i <= 9) {
        return "0" + i;
    } else {
        return i;
    }
}
