/**
* 日期格式化
* */
if (!Date.format) {
	Date.prototype.format = function(fmt) {
		
		var o = {
			"M+": this.getMonth() + 1, //月份
			"d+": this.getDate(), //日
			"h+": this.getHours(), //小时
			"m+": this.getMinutes(), //分
			"s+": this.getSeconds(), //秒
			"q+": Math.floor((this.getMonth() + 3) / 3), //季度
			"S": this.getMilliseconds() //毫秒
		};
		if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
		for (var k in o)
			if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
		return fmt;
	};
}
//var festival = new Festival();
var festival = (function(){
	var __fesData = {"20250101":{"date":20250101,"date_chn":"2025年1月1日","name":"元旦","isHoliday":true,"isSwap":false,"year":2025,"month":1},"20250128":{"date":20250128,"date_chn":"2025年1月28日","name":"春节","isHoliday":true,"isSwap":true,"year":2025,"month":1},"20250129":{"date":20250129,"date_chn":"2025年1月29日","name":"春节","isHoliday":true,"isSwap":true,"year":2025,"month":1},"20250130":{"date":20250130,"date_chn":"2025年1月30日","name":"春节","isHoliday":true,"isSwap":true,"year":2025,"month":1},"20250131":{"date":20250131,"date_chn":"2025年1月31日","name":"春节","isHoliday":true,"isSwap":true,"year":2025,"month":1},"20250201":{"date":20250201,"date_chn":"2025年2月1日","name":"春节","isHoliday":true,"isSwap":true,"year":2025,"month":2},"20250202":{"date":20250202,"date_chn":"2025年2月2日","name":"春节","isHoliday":true,"isSwap":true,"year":2025,"month":2},"20250203":{"date":20250203,"date_chn":"2025年2月3日","name":"春节","isHoliday":true,"isSwap":true,"year":2025,"month":2},"20250204":{"date":20250204,"date_chn":"2025年2月4日","name":"春节","isHoliday":true,"isSwap":true,"year":2025,"month":2},"20250404":{"date":20250404,"date_chn":"2025年4月4日","name":"清明节","isHoliday":true,"isSwap":false,"year":2025,"month":4},"20250501":{"date":20250501,"date_chn":"2025年5月1日","name":"劳动节","isHoliday":true,"isSwap":false,"year":2025,"month":5},"20250502":{"date":20250502,"date_chn":"2025年5月2日","name":"劳动节","isHoliday":true,"isSwap":false,"year":2025,"month":5},"20250607":{"date":20250607,"date_chn":"2025年6月7日","name":"端午节","isHoliday":true,"isSwap":false,"year":2025,"month":6},"20250929":{"date":20250929,"date_chn":"2025年9月29日","name":"中秋节","isHoliday":true,"isSwap":false,"year":2025,"month":9},"20251001":{"date":20251001,"date_chn":"2025年10月1日","name":"国庆节","isHoliday":true,"isSwap":true,"year":2025,"month":10},"20251002":{"date":20251002,"date_chn":"2025年10月2日","name":"国庆节","isHoliday":true,"isSwap":true,"year":2025,"month":10},"20251003":{"date":20251003,"date_chn":"2025年10月3日","name":"国庆节","isHoliday":true,"isSwap":true,"year":2025,"month":10},"20251004":{"date":20251004,"date_chn":"2025年10月4日","name":"国庆节","isHoliday":true,"isSwap":true,"year":2025,"month":10},"20251005":{"date":20251005,"date_chn":"2025年10月5日","name":"国庆节","isHoliday":true,"isSwap":true,"year":2025,"month":10},"20251006":{"date":20251006,"date_chn":"2025年10月6日","name":"国庆节","isHoliday":true,"isSwap":true,"year":2025,"month":10},"20251007":{"date":20251007,"date_chn":"2025年10月7日","name":"国庆节","isHoliday":true,"isSwap":true,"year":2025,"month":10},"20251008":{"date":20251008,"date_chn":"2025年10月8日","name":"国庆节调休","isHoliday":false,"isSwap":true,"year":2025,"month":10},"20251009":{"date":20251009,"date_chn":"2025年10月9日","name":"国庆节调休","isHoliday":false,"isSwap":true,"year":2025,"month":10}};
	return {
		isHoliday:function(day){
			return __fesData[day] && __fesData[day].isHoliday || false
		},
		isUnhappy:function(day){
			return __fesData[day] && __fesData[day].isSwap || false
		},
		isWeekend:function(){},
		getJson:function(url){

		}
	}
})();
/**
 * [请假计算]
 * @param  {Object} options){		var defOptions    [description]
 * @return {[type]}                 [description]
 */
var dateDiff = (function(options){
	//默认设置
	var defOptions = {
		start:'09:00', //上班时间
		end:'18:00', // 下班时间
		noonStart:'12:30',// 午休开始
		noonEnd:'13:30',// 午休开始
		output:'day',//输出 day日 hour小时
		tofixed:2,//小数点后多少位
		log:true
	}
	var workHour = 8; // 实际工作时间,最终根据配置计算
	var holidayList = [];
	var finalOpts = Object.assign ? Object.assign({},defOptions,options):$.extend({},defOptions,options); //最终生成的配置

	// 日志
	function log(msg){
		if(finalOpts.log) console.log(msg)
	}

	//更新设置
	function updateConfig(option){
		finalOpts = Object.assign ? Object.assign({},finalOpts,options):$.extend({},finalOpts,options);
	}

	//判断当天是否不用上班:节假日/调休/周末
	function isNonWorkingDay(date){
		var day = new Date(date).format('yyyyMMdd');
		return festival.isHoliday(day) || festival.isUnhappy(day)  || isWeekend(date) 
	}

	// 是否周末
	function isWeekend(date){
		var weekday = new Date(date).getDay()
		return weekday == 0 || weekday == 6
	}

	// 计算请假
	function diff(start,end,allWorking){
		start = iosDate(start),
		end = iosDate(end);
		// 计算日期差
		if(!start || !end || Date.parse(end) - Date.parse(start)<0){
			log('日期错误')
			return 0;	
		}
		var time = 0,workDaySplit = 0;
		var startDay = new Date(start),endDay = new Date(end);
		//计算间隔
		// var days = getDaysOfPeriod(startDay,endDay)
		if(startDay.format('yyyyMMdd') == endDay.format('yyyyMMdd')){ // 当天
			if(!allWorking && isNonWorkingDay(startDay)){ //休息日直接不算
				return 0
			}else{
				time+=getOneDayTime(startDay,endDay);
			}

		}else{ //跨天
			// 计算开始和结束之间有多少工作日
			workDaySplit = getWorkDay(startDay,endDay,allWorking)
			//time+= workDaySplit*8*1000*3600;
			// 计算开始日的实际请假时间
			var startDayTime = getOneDayTime(startDay);
			time+= startDayTime;
			// 计算结束日的实际请假时间
			var endDayTime = getOneDayTime(null,endDay);
			time+= endDayTime;
		}
		if(finalOpts.output == 'day'){//计算为日
			time = Number(time/(workHour*1000*3600))
			time = Number(time) + Number(workDaySplit);
		}else if(finalOpts.output == 'hour'){//其他格式 有空再写
			time = Number(time/(1000*3600)).toFixed(finalOpts.tofixed)
			time = Number(time) + Number(workDaySplit)*workHour;
		}
		time = String(Number(time.toFixed(finalOpts.tofixed)));
		return time;
	}

	/**
	 * 获取一段时期内的天数，包含起止日期
	 * @start:起始时间
	 * @end:截止时间
	 * */
	function getDaysOfPeriod(start, end) {
		try {
			var startObj = {
					'y': start.getFullYear(),
					'm': start.getMonth() + 1,
					'd': start.getDate()
				},
				endObj = {
					'y': end.getFullYear(),
					'm': end.getMonth() + 1,
					'd': end.getDate()
				};

			//start和end必须包含，所以需要+1
			var startPart = start.getTotalDaysOfMonth() - startObj.d + 1,
				monthPart = 0,
				endPart = endObj.d;

			if(startObj.y != endObj.y) { //跨年的情况
				var startMonths = 12 - startObj.m; //获取start年份中剩下的月份数
				for(var i = startObj.m + 1; i <= startObj.m + startMonths; i++) {
					monthPart += (new Date(startObj.y, i - 1, 1)).getTotalDaysOfMonth();
				}
				for(var i = 1; i <= endObj.m - 1; i++) {
					monthPart += (new Date(endObj.y, i - 1, 1)).getTotalDaysOfMonth();
				}
			} else {
				if(startObj.m != endObj.m) { //跨月
					for(var i = startObj.m + 1; i < endObj.m; i++) {
						monthPart += (new Date(startObj.y, i - 1, 1)).getTotalDaysOfMonth();
					}
				} else {
					startPart = 0;
					endPart = endObj.d - startObj.d + 1;
				}
			}
			console.log("【" + start + "】到【" + end + "】期间的天数：" + (startPart + monthPart + endPart));
			return startPart + monthPart + endPart;
		} catch(e) {
			console.log("计算【" + start + "】到【" + end + "】期间的天数发生异常:" + e.message);
			return 0;
		}
	};

	/**
	 * 计算当天请假时长(毫秒)
	 */
	function getOneDayTime(start,end){
		var date = start || end || '';
		var today = new Date(date).format('yyyy/MM/dd');
		// 格式化当天各个时间段
		var wt = {
			start:Date.parse(today+' '+finalOpts.start),
			end:Date.parse(today+' '+finalOpts.end),
			noonStart:Date.parse(today+' '+finalOpts.noonStart),
			noonEnd:Date.parse(today+' '+finalOpts.noonEnd),
		}
		var startInt = start?Date.parse(start):wt.start,
			endInt = end?Date.parse(end):wt.end,
			time=0;
		try{
			// 正常工作时间（除去午休）
			var normalWorkTime = wt.end - wt.start - (wt.noonEnd - wt.noonStart);
			
			// 情况1：如果完全在工作时间内
			if(startInt >= wt.start && endInt <= wt.end) {
				// 需要判断是否跨越午休时间
				if(startInt < wt.noonStart && endInt > wt.noonEnd) {
					// 跨越整个午休
					time = endInt - startInt - (wt.noonEnd - wt.noonStart);
				} else if(startInt < wt.noonStart && endInt > wt.noonStart && endInt <= wt.noonEnd) {
					// 开始在午休前，结束在午休中
					time = endInt - startInt - (endInt - wt.noonStart);
				} else if(startInt >= wt.noonStart && startInt < wt.noonEnd && endInt > wt.noonEnd) {
					// 开始在午休中，结束在午休后
					time = endInt - startInt - (wt.noonEnd - startInt);
				} else if(startInt >= wt.noonStart && endInt <= wt.noonEnd) {
					// 完全在午休中，不算时间
					time = 0;
				} else {
					// 不跨午休
					time = endInt - startInt;
				}
			}
			// 情况2：开始时间在工作时间前，结束时间在工作时间内
			else if(startInt < wt.start && endInt <= wt.end) {
				// 计算工作开始到结束的时间
				if(endInt <= wt.noonStart) {
					// 结束在午休前
					time = endInt - wt.start;
				} else if(endInt > wt.noonStart && endInt <= wt.noonEnd) {
					// 结束在午休中
					time = wt.noonStart - wt.start;
				} else {
					// 结束在午休后
					time = endInt - wt.start - (wt.noonEnd - wt.noonStart);
				}
			}
			// 情况3：开始时间在工作时间内，结束时间在工作时间后
			else if(startInt >= wt.start && endInt > wt.end) {
				// 计算开始到工作结束的时间，再加上超出部分
				var withinWorkTime = 0;
				if(startInt < wt.noonStart) {
					// 开始在午休前
					withinWorkTime = wt.end - startInt - (wt.noonEnd - wt.noonStart);
				} else if(startInt >= wt.noonStart && startInt <= wt.noonEnd) {
					// 开始在午休中
					withinWorkTime = wt.end - wt.noonEnd;
				} else {
					// 开始在午休后
					withinWorkTime = wt.end - startInt;
				}
				
				// 加上超出部分
				var extraTime = endInt - wt.end;
				time = withinWorkTime + extraTime;
			}
			// 情况4：完全超出工作时间（开始时间在工作时间前，结束时间在工作时间后）
			else if(startInt < wt.start && endInt > wt.end) {
				// 包含全部工作时间，再加上前后超出部分
				time = normalWorkTime + (endInt - wt.end);
			}
		}catch(e){
			log("计算【" + start + "】到【" + end + "】期间的请假天数发生异常:" + e.message);
		}
		return time;
	}

	/*排除开始时间和结束时间计算2个日期之间有多少个工作日*/
	function getWorkDay(start,end,allWorking){
		var startDay = new Date(start).format('yyyyMMdd');
		var endDay = new Date(end).format('yyyyMMdd');
		var count = 0;
		while(startDay!=endDay){
			startDay = getFutureDate(startDay,1);//获取下一天
			var startDayText = splitDay(startDay);//格式化
			if(startDay!= endDay && allWorking){
				count++
			}else if(startDay!= endDay && !isNonWorkingDay(startDayText)){
				count++
			}
		}
		return count;
	}

	function getFutureDate(start,days,format){
		start = splitDay(start)
		var nowDate = new Date(start)
		var nowTime = nowDate.getTime()  //当前时间戳
		var futureTime = Math.abs(nowTime) + (days * 24 * 60 * 60 * 1000) //days天后的时间戳
		var futureDate = new Date(futureTime);
		format = format || 'yyyyMMdd'
		return futureDate.format(format)
	}

	function splitDay(str){
		var arr = str.split('')
	      arr.splice(4, 0, '-');
	      arr.splice(7, 0, '-');
	      return arr.join("");
	}

	function iosDate(str){
		var strType = typeof(str)
		return strType =='number' || strType =='string'  ? String(str).replace(/\-/g, '/') : str
	}

	// 不用计算
	function diffAllDay(start,end){
		return diff(start,end,true)
	}

	return {
		diff:diff,// 计算请假时间
		diffAllDay:diffAllDay,//计算请假时间,不排除非工作日
		updateConfig:updateConfig, //更新配置
		getWorkingDay:getWorkDay, //获取工作日
		isNonWorkingDay:isNonWorkingDay, //判断是否非工作日
		getFutureDate:getFutureDate //计算N天后的日期
	}
})()