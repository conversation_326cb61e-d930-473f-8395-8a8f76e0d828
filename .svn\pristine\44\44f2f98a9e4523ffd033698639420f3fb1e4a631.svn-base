<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">项目名称</td>
					  			<td>
					  				<input type="text" data-rules="required" placeholder="xxx采购项目" class="form-control input-sm"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">客户名称</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" data-rules="required"  placeholder="xx公司" name="apply.data1"/>
					  			</td>
					  			<td class="required">项目预算</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" data-rules="required"  name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">销售</td>
					  			<td>
					  				<input type="text" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm"  name="apply.data3"/>
					  			</td>
					  			<td>售前</td>
					  			<td>
					  				<input type="text" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm"  name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">投标日期</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm"  name="apply.data8"/>
					  			</td>
					  			<td></td>
					  			<td>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">外采需求</td>
					  			<td colspan="3">
									<textarea style="min-height: 40px;" data-rules="required" onchange="limitLenth(this,500);" class="form-control input-sm" placeholder="若没有填写无" name="apply.data5"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">实施需求</td>
					  			<td colspan="3">
									<textarea style="min-height: 40px;" data-rules="required" onchange="limitLenth(this,500);" class="form-control input-sm" placeholder="远程/到达现场时间要求or驻场要求" name="apply.data6"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">交付需求</td>
					  			<td colspan="3">
									<textarea style="min-height: 40px;" data-rules="required" onchange="limitLenth(this,500);" class="form-control input-sm" placeholder="源码交付范围,交付文档" name="apply.data7"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">项目描述</td>
					  			<td colspan="3">
									<textarea style="min-height: 100px;" data-rules="required" onchange="limitLenth(this,500);" class="form-control input-sm" placeholder="项目介绍、客户情况介绍" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传</button>
									<small class="ml-10 unedit-remove">招标文件、<a target="blank" href="/doc/标前评审内部评审文件.docx">标前评审内部评审文件模板.doc</a></small>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({fileCheck:true,hideHistoryApply:true});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData();
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>