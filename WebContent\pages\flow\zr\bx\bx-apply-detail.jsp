<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>费用报销审批</title>
	<style>
		.layui-table td, .layui-table th{padding: 8px;}
		.layui-tab-content{background-color: #fff;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.gray-bg{background-color: #e8edf7;}
		.layui-icon{font-size: 12px;}
		.updateHzAmount{display: none;}
		.extTr td{border: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="flowForm" data-mars="FlowDao.baseInfo">
		<div class="layui-row ibox-content">
	   	   <div class="layui-col-md12 applyInfo" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
		 		<div class="flow-title">中融德勤（广州）信息技术有限公司-费用报销</div>
		 		<div class="flow-approval-btn">
		 			<EasyTag:res resId="HZ_BX_MONEY">
							<button class="btn btn-warning btn-sm ml-10" onclick="$('.updateHzAmount').show();$('.hzAmountVal').hide();" type="button">核准金额</button>
		  			</EasyTag:res>
		 		</div>
		 		<table class="table table-vzebra hidden-print">
			  		<tr>
			  			<td style="width: 120px;">当前处理人</td>
			  			<td style="width: 40%;">
			  				<span name="checkName"/>
			  			</td>
			  			<td style="width: 120px;">申请时间</td>
			  			<td>
			  				<span name="applyTime"/>
			  			</td>
			  		</tr>
			  		<tr>
			  			<td style="width: 120px;">当前状态</td>
			  			<td colspan="3">
			  				<span name="nodeName"/>
			  			</td>
			  		</tr>
			  	</table>
		    	<table class="table table-vzebra mt-10 flow-table">
			  		<tr>
			  			<td style="width: 120px;">标题</td>
			  			<td style="width: 40%;">
			  				<span name="applyTitle"/>
			  			</td>
			  			<td style="width: 120px;">单号</td>
			  			<td>
			  				<span name="applyNo"/>
			  			</td>
			  		</tr>
			  		<tr>
			  			<td class="required">姓名</td>
			  			<td>
			  				<span name="applyName"/>
			  			</td>
			  			<td class="required">部门</td>
			  			<td>
			  				<span name="deptName"/>
			  			</td>
			  		</tr>
			  		<tr>
			  			<td>说明</td>
			  			<td colspan="3">
							<span name="applyRemark"/>
			  			</td>
			  		</tr>
			  		
			  		<tr>
			  			<td>费用总计</td>
			  			<td>
			  				<span name="business.HZ_MONEY"/>
			  			</td>
			  			<td>冲帐金额</td>
			  			<td>
			  				<span name="business.REVERSE_MONEY"/>
			  			</td>
			  		</tr>
			  		<tr>
			  			<td>应付金额</td>
			  			<td>
			  				<span name="business.PAY_MONEY"/>
			  			</td>
			  			<td>大写</td>
			  			<td>
			  				<span name="business.PAY_MONEY_DX"/>
			  			</td>
			  		</tr>
			    </table>
		 </div>
		 <div class="layui-col-md12 mt-10 approve-item">
		 	  <div style="min-height: 100px;">
				 <table class="layui-table approve-table" style="margin-top: -1px;">
				   <thead>
				    <tr>
				      <th>序号</th>
				      <th>日期</th>
				      <th class="hidden-print">费用类型(内部)</th>
				      <th class="hidden-print">费用说明(内部)</th>
				      <th>发票类型</th>
				      <th>金额</th>
				      <th>核准金额</th>
				      <th>部门</th>
				      <th>项目号</th>
				      <th>产品线</th>
				      <th>税率(%)</th>
				      <th>税金</th>
				      <th>金额</th>
				    </tr> 
				  </thead>
				  <tbody data-mars="BxDao.ztBxItems" data-template="bxItems"></tbody>
				</table>
			</div>
		 </div>
		 <div class="layui-col-md12 approve-result">
		 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
		</div>
	</div>
</form>
<script id="bxItems" type="text/x-jsrender">
  	 {{for data}}
  		<tr>
  			<td>{{:#index+1}}</td>
  			<td>{{:BX_DATE}}</td>
  			<td class="hidden-print">{{:FEE_TYPE}}</td>
  			<td class="hidden-print">{{:FEE_DESC}}</td>
  			<td>{{:INVOICE_TYPE}}</td>
  			<td>{{:AMOUNT}}</td>
  			<td><span class="hzAmountVal">{{:HZ_AMOUNT}}</span><input type="number"  data-text="false" class="form-control input-sm updateHzAmount" value="{{:HZ_AMOUNT}}" style="width:80px;" onchange="updateHzAmount(this.value,'{{:ITEM_ID}}','{{:BUSINESS_ID}}')"/></td>
  			<td>{{:DEPT_NAME}}</td>
  			<td>{{:CONTRACT_NAME}}</td>
  			<td>{{:PRODUCT_LINE}}</td>
  			<td>{{:TAX_RATE}}</td>
  			<td>{{:TAXES}}</td>
  			<td>{{:R_AMOUNT}}</td>
  		</tr>
  	  {{/for}}
</script>
<script id="approveList" type="text/x-jsrender">
  	 {{for data}}
  		<tr {{if CHECK_RESULT=='0'}} class="hidden-print" {{/if}}>
  			<td>{{:#index+1}}</td>
  			<td>{{:NODE_NAME}}</td>
  			<td>{{:CHECK_NAME}}</td>
  			<td>{{if NODE_ID!='0'}} {{call:CHECK_RESULT fn='checkResultLable'}} {{/if}}</td>
  			<td class="hidden-print">{{:GET_TIME}}</td>
  			<td>{{:CHECK_TIME}}</td>
  			<td>{{:CHECK_DESC}}</td>
  		</tr>
  	  {{/for}}
</script>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		$(function(){
			FlowCore.initPage({
				hideApplyNo:true,
				success:function(data){

				}
			});
		});
		
		function updateHzAmount(hzAmount,itemId,businessId){
			var payMoneyDx = digitUppercase(hzAmount);
			ajax.remoteCall("${ctxPath}/servlet/zr/flow/bx?action=updateHzMoney",{hzAmount:hzAmount,payMoneyDx:payMoneyDx,itemId:itemId,businessId:businessId},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		var Flow = {};
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>