package com.yunqu.work.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.ProjecContractModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="ProjectContractDao")
public class ProjectContractDao extends AppDaoContext {
	
	
	@WebControl(name="record",type=Types.TEMPLATE)
	public JSONObject record(){
		ProjecContractModel model=new ProjecContractModel();
		model.setColumns(getParam("contract"));
		JSONObject jsonObject=queryForRecord(model);
		if(StringUtils.notBlank(model.getContractId())){
			LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getContractId());
		}
		return jsonObject;
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select CONTRACT_ID,CONTRACT_NAME from YQ_PROJECT_CONTRACT");
	}
	
	/**
	 * 全部合同项目
	 * @return
	 */
	@WebControl(name="contractList",type=Types.LIST)
	public JSONObject contractList(){
		EasySQL sql=getEasySQL("select * from YQ_PROJECT_CONTRACT where 1=1");
		sql.appendLike(param.getString("contractName"),"and CONTRACT_NAME like ?");
		sql.append("order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 全部项目
	 * @return
	 */
	@WebControl(name="projectList",type=Types.LIST)
	public JSONObject projectList(){
		EasySQL sql=getEasySQL("select * from YQ_PROJECT where 1=1");
		sql.append("and is_delete = 0");
		sql.appendLike(param.getString("projectName"),"and PROJECT_NAME like ?");
		sql.append("order by UPDATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
