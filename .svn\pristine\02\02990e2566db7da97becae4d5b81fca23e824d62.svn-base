<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>风险管理</title>
	<style>
		.layui-table-tool{background-color: #ffffff;}
		.layui-table-cell {
	        line-height: 20px !important;
	        vertical-align: middle;
	        height: auto;
	        padding: 6px 6px;
	        overflow: visible;
	        text-overflow: inherit;
	        white-space: normal;
	    }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
  <form  autocomplete="off" onsubmit="return false;" class="form-inline" id="searchForm">
    <input type="hidden" name="riskState" id="riskState" value=""/>
  	<div class="ibox-content">
		<div class="layui-tab layui-tab-brief task-tab" lay-filter="riskTab" style="padding-top: 10px;">
			    <ul class="layui-tab-title" data-mars="ProjectRiskDao.riskCountStat">
			        <li data-state="" class="layui-this">全部 </li>
			        <li data-state="0">进行中</li>
			        <li data-state="1">已处理</li>
			    </ul>
			    <div class="layui-tab-content" style="padding: 0px;">
			        <div class="layui-tab-item layui-show"></div>
			        <div class="layui-tab-item"></div>
			        <div class="layui-tab-item"></div>
			    </div>
			    <div class="form-group mt-10">
			    	  <div class="input-group input-group-sm ml-5"  style="width: 150px">
						 <span class="input-group-addon">提出人</span>	
						 <input type="text" name="creatorName" class="form-control input-sm">
				     </div>
			    	  <div class="input-group input-group-sm"  style="width: 150px">
						 <span class="input-group-addon">负责人</span>	
						 <input type="text" name="ownerName" class="form-control input-sm">
				     </div>
				     <div class="input-group input-group-sm"  style="width: 180px">
						 <span class="input-group-addon">项目</span>	
						 <input type="hidden" name="projectId" class="form-control input-sm">
						 <input type="text" id="projectId" onclick="singleProject(this);" class="form-control input-sm">
				     </div>
				     <div class="input-group input-group-sm">
						 <button type="button" class="btn btn-sm btn-default" onclick="reloadRisk()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					</div>
			    </div>
		 </div>
		 <table class="layui-hide" id="riskList"></table>
  	  </div>
	</form>
	<script  type="text/html" id="riskToolBar">
    	<button class="btn btn-info btn-sm" lay-event="exportData" type="button">导出</button>
	</script>
</EasyTag:override>
<EasyTag:override name="script">
 <script type="text/javascript">

 	var projectId = '${param.projectId}';
    
    function loadRiskTable(){
        $("#searchForm").initTable({
            mars:'ProjectRiskDao.riskList',
            id:"riskList",
            height:'full-110',
            limit:30,
            toolbar:'#riskToolBar',
            data:{},
            cols: [[
                {title:'序号',type:'numbers'},
                {
                    title: '操作',
                    align:'center',
                    width:80,
                    templet:function(row){
                        return '<a class="btn btn-xs btn-link" href="javascript:;" lay-event="riskDetail">详情</a>';
                    }
                },
                {
                    field: 'RISK_BELONG',
                    title: '风险分类',
                    align:'center',
                    width:90
                },{
                    field: 'RISK_DESC',
                    title: '风险说明',
                    align:'left',
                    minWidth:160
                },{
                    field: 'SOLUTION',
                    title: '风险应对措施',
                    align:'left',
                    minWidth:200,
                },{
                    field: 'CREATE_TIME',
                    title: '录入时间',
                    align:'center',
                    width:140,
                },{
                    field: 'RISK_LEVEL',
                    title: '风险等级',
                    align:'center',
                    width:80,
                    templet:function(row){
                        return riskLevelLabel(row.RISK_LEVEL);
                    }
                },{
                    field: 'RISK_STATE',
                    title: '风险状态',
                    align:'center',
                    width:80,
                    templet:function(row){
                        return riskStateLabel(row.RISK_STATE);
                    }
                },{
                    field:'OWNER_NAME',
                    title:'负责人',
                    width:70
                },{
                    field: 'CREATOR',
                    title: '发起人',
                    width:70,
                    align:'center',
                    templet:function(row){
                        return getUserName(row.CREATOR);
                    }
                }
            ]],done:function(result){
            	
         }});
    }

    function  filterElement(){
       layui.element.on('tab(riskTab)', function(elem){
           var state = $(this).data('state');
           $("#riskState").val(state);
           reloadRisk();
       });
    }

    function reloadRisk(){
        $("#searchForm").queryData({id:'riskList'});
    }

    function riskStateLabel(state){
        var json = {0:'<label class="label label-danger label-outline">进行中</label>',1:'<label class="label label-success">已完成</label>'};
        var result=json[state]||'';
        if(result){
            return result;
        }else{
            return '';
        }
    }

    function riskLevelLabel(state){
        var json = {0:'<label class="label  label-outline label-warning">低</label>',1:'<label class="label  label-warning">中</label>',2:'<label class="label label-danger">高</label>'};
        var result=json[state]||'';
        if(result){
            return result;
        }else{
            return '';
        }
    }

    function  riskDetail(data){
    	var isAdmin = 0;
    	if(top.isAdmin=='1'||isSuperUser){
    		isAdmin = 1;
    	}
        var riskId = data['RISK_ID'];
        var riskState = data['RISK_STATE'];
        popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'风险详情',offset:'r',area:['700px','100%'],url:'${ctxPath}/pages/project/risk/risk-detail.jsp',title:'风险详情',data:{riskId:riskId,riskState:riskState,isAdmin:isAdmin}});
    }
    
    function exportData(){
    	layer.msg('todo',{icon:7,offset:'20px',time:1200});
    }
    
    
    $(function(){
    	loadRiskTable();
    	filterElement();
    });

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
