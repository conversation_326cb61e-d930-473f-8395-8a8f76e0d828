<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<table class="layui-hide" id="versionTable"></table>

<script type="text/x-jsrender" id="versionBar">
  <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="versionDdetail">查看</a>
</script>

<script type="text/javascript">
	
	function reloadProjectVersion(){
		$("#projectDetail").queryData({id:'versionTable'});
	}
	
	function loadVersion(){
		$("#projectDetail").initTable({
			mars:'VersionDao.list',
			id:'versionTable',
			height:'full-160',
			limit:20,
			cols: [[
			 {type:'numbers',title:'序号'},
			 {
			    field: 'VERSION_TITLE',
				title: '主题',
				align:'left',
				width:220,
				event:'versionDdetail',
				style:'color:#1890ff;cursor: pointer;'
			},
             {
			    field: 'VERSION_NAME',
				title: '版本名称',
				width:100,
				align:'center'
			},{
			    field: 'CREATE_NAME',
				title: '创建人',
				width:100,
				align:'center'
			},{
			    field: 'RECEIVER_NAME',
				title: '接收人',
				width:100,
				align:'center'
			},{
				field:'RECIPIENT_NAME',
				width:140,
				title:'抄送人'
			},{
				field:'VIEW_COUNT',
				width:80,
				title:'查看次数',
				event:'lookLog',
				style:'color:#1890ff;cursor: pointer;'
			},{
				field:'FILE_DESC',
				title:'版本文件',
				hide:true
			},{
			    field: 'CREATE_TIME',
				title: '创建时间',
				width:140,
				align:'center'
			}
			]],done:function(result){
				$(".n8").text(result.totalRow);
			}}
		);
		
	}

	function lookLog(data){
		lookLogLayer(data['VERSION_ID']);
	}
	
	function versionDdetail(data){
		popup.openTab({id:'versionDdetail',type:1,maxmin:true,anim:0,full:true,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/ops/version-detail.jsp',title:'版本详情',data:{versionId:data.VERSION_ID,op:'detail'}});
		
	}
	
	function publishVersion(type){
		var moduleIds = '';
		if(type=='1'){
			var checkStatus = table.checkStatus('modules')
	 		var data = checkStatus.data;
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择模块。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		
	 		var array = [];
	 		for(var index in data){
	 			var moduleId = data[index]['MODULE_ID'];
	 			if(moduleId){
	 				array.push(moduleId);
	 			}
	 		}
	 		moduleIds = array.join(',');
		}
		
		var projectName = $("[name='project.PROJECT_NAME']").text();
		popup.layerShow({type:1,maxmin:true,anim:0,full:true,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/ops/version-edit.jsp',title:'新增版本',data:{type:type,moduleIds:moduleIds,appName:projectName,versionType:1,projectId:projectId,projectName:projectName}});
	}
	
	function reloadVersion(){
		reloadProjectVersion();
		$("#projectDetail").queryData({id:'modules'});
	}
	
	
	
</script>