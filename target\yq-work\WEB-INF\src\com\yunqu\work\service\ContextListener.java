package com.yunqu.work.service;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.kit.PathKit;

import com.yunqu.work.base.Constants;
import com.yunqu.work.utils.SchedulerTaskUtils;

/**
 * 监听类:监听服务器的启动并将任务添加到任务栈
 *
 */
@WebListener
public class ContextListener implements ServletContextListener {

	private Logger loger=LogEngine.getLogger(Constants.APP_NAME);
	/**
     * 初始化定时器
     * web 程序运行时候自动加载
     */
	@Override
	public void contextInitialized(ServletContextEvent arg) {
		try {
			SchedulerTaskUtils.shutDown();
		} catch (Exception e) {
			loger.error(e);
		}
		
		PathKit.setWebRootPath(arg.getServletContext().getRealPath("/"));
		try {
			String taskSwitch = AppContext.getContext(Constants.APP_NAME).getProperty("taskSwitch", "1");
			
			if("1".equals(taskSwitch)) {
				//每10分钟执行一次
				SchedulerTaskUtils.addTask("com.yunqu.work.service.JobExcute","0 0/10 * * * ?");
				
				SchedulerTaskUtils.start();
				
				loger.info(">>>>task start");
				
			}else {
				loger.info(">>>>task not open.");
			}
		} catch (Exception e) {
			loger.error(e);
		}
	}

	@Override
	public void contextDestroyed(ServletContextEvent arg0) {
		try {
			SchedulerTaskUtils.shutDown();
		} catch (Exception e) {
			loger.error(e);
		}
	}
}
