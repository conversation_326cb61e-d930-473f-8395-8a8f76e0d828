package com.yunqu.work.service;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.List;

public class ContractService extends AppBaseService {
    private static class Holder {
        private static ContractService service = new ContractService();
    }

    public static ContractService getService() {
        return ContractService.Holder.service;
    }

    public void changeAmountRatio(String contractId) throws SQLException {
        EasySQL sql = new EasySQL("UPDATE yq_contract_payment AS t1 JOIN yq_project_contract AS c ON t1.CONTRACT_ID = c.CONTRACT_ID SET t1.RATIO =  LEAST(ROUND((t1.AMOUNT_WITH_TAX / c.amount) * 100, 2), 999.99)");
        sql.append(contractId, " WHERE c.CONTRACT_ID = ? ");
        this.getQuery().executeUpdate(sql.getSQL(), sql.getParams());

        EasySQL sql2 = new EasySQL("UPDATE yq_contract_stage AS t1 JOIN yq_project_contract AS c ON t1.CONTRACT_ID = c.CONTRACT_ID SET t1.RATIO =  LEAST(ROUND((t1.PLAN_RCV_AMT / c.amount) * 100, 2), 999.99)");
        sql2.append(contractId, " WHERE c.CONTRACT_ID = ? ");
        this.getQuery().executeUpdate(sql2.getSQL(), sql2.getParams());

        EasySQL sql3 = new EasySQL("UPDATE yq_contract_income_stage AS t1 JOIN yq_project_contract AS c ON t1.CONTRACT_ID = c.CONTRACT_ID SET t1.RATIO =  LEAST(ROUND((t1.PLAN_AMOUNT / c.amount) * 100, 2), 999.99)");
        sql3.append(contractId, " WHERE c.CONTRACT_ID = ? ");
        this.getQuery().executeUpdate(sql3.getSQL(), sql3.getParams());
    }

    public void reloadCount(String countName, String tableName, String contractId) {
        try {
            if (StringUtils.isBlank(contractId) || StringUtils.isBlank(tableName) || StringUtils.isBlank(countName)) {
                getLogger().error("reloadCount有参数为空,计数失败! contractId:" + contractId + ";tableName:" + tableName + ";countName" + countName);
                return;
            } else {
                EasySQL sql = new EasySQL("select CONTRACT_ID , COUNT(*) AS COUNT_NUM ");
                sql.append("FROM " + tableName);
                sql.append(contractId, " WHERE CONTRACT_ID = ? ");
                if ("INCOME_CONFIRM_COUNT".equals(countName)) {
                    sql.append("and CONFIRM_TYPE = 'A'");
                } else if ("INCOME_CONFIRM_COUNT_B".equals(countName)) {
                    sql.append("and CONFIRM_TYPE = 'B'");
                }
                sql.append("GROUP BY CONTRACT_ID");
                EasyRow row = this.getQuery().queryForRow(sql.getSQL(), sql.getParams());
                String count = "";
                if (row == null) {
                    count = "0";
                } else {
                    count = row.getColumnValue("COUNT_NUM");
                }
                if (StringUtils.isBlank(count)) {
                    count = "0";
                }
                setCount(countName, contractId, count);
                return;
            }
        } catch (SQLException e) {
            getLogger().error(e.getMessage(), e);
            return;
        }
    }

    public void reloadContractCount(String contractId) {
        ContractService.getService().reloadCount("STAGE_COUNT", "yq_contract_stage", contractId);
        ContractService.getService().reloadCount("PAYMENT_COUNT", "yq_contract_payment", contractId);
        ContractService.getService().reloadCount("INCOME_STAGE_COUNT", "yq_contract_income_stage", contractId);
        ContractService.getService().reloadCount("INCOME_CONFIRM_COUNT", "yq_contract_income_confirm", contractId);
        ContractService.getService().reloadCount("INCOME_CONFIRM_COUNT_B", "yq_contract_income_confirm", contractId);
        ContractService.getService().reloadCount("INVOICE_COUNT", "yq_crm_invoice", contractId);
        ContractService.getService().reloadCount("RECEIPT_COUNT", "yq_contract_receipt", contractId);
    }

    public void reloadAllCount(String countName, String tableName) {
        try {
            EasySQL sql = new EasySQL("select CONTRACT_ID , COUNT(*) AS COUNT_NUM from");
            sql.append(tableName);
            if ("INCOME_CONFIRM_COUNT".equals(countName)) {
                sql.append("where CONFIRM_TYPE = 'A'");
            } else if ("INCOME_CONFIRM_COUNT_B".equals(countName)) {
                sql.append("where CONFIRM_TYPE = 'B'");
            }
            sql.append("GROUP BY CONTRACT_ID");
            List<EasyRow> rows = this.getQuery().queryForList(sql.getSQL());
            if (rows == null || rows.size() == 0) {
                return;
            }
            for (EasyRow row : rows) {
                String contractId = row.getColumnValue("CONTRACT_ID");
                String count = row.getColumnValue("COUNT_NUM");
                if (StringUtils.isBlank(count)) {
                    count = "0";
                }
                setCount(countName, contractId, count);
            }
        } catch (SQLException e) {
            getLogger().error(e.getMessage(), e);
            return;
        }
    }


    public void setCount(String countName, String contractId, String count) {
        String textJsonStr = null;
        try {
            textJsonStr = this.getQuery().queryForString("select TEXT_JSON from yq_project_contract where contract_id = ?", contractId);
            JSONObject textJson = null;
            if (StringUtils.isBlank(textJsonStr)) {
                textJson = new JSONObject();
            } else {
                textJson = JSONObject.parseObject(textJsonStr);
            }
            textJson.put(countName, count);
            String newTextJson = textJson.toJSONString();
            this.getQuery().executeUpdate("UPDATE yq_project_contract SET TEXT_JSON = ? WHERE contract_id = ?", new Object[]{newTextJson, contractId});
        } catch (SQLException e) {
            getLogger().error(e.getMessage(), e);
            return;
        }
    }

    /*
     * 当收款的金额到达合同阶段的计划金额
     * 给合同阶段加上完成日期（以最晚的收款日期为完成日期）
     */
    public void setCompleteTime(String contractStageId) {
        if (StringUtils.isBlank(contractStageId)) {
            return;
        }
        try {
            String plan_rcv_amt = this.getQuery().queryForString("select PLAN_RCV_AMT from yq_contract_stage where STAGE_ID = ? ", contractStageId);
            String receive_amt = this.getQuery().queryForString("SELECT IFNULL(SUM(AMOUNT),0.00)  FROM yq_contract_receipt WHERE STAGE_ID = ? ", contractStageId);
            if (plan_rcv_amt == null || receive_amt == null) {
                this.getLogger().error("合同阶段计划收款金额或收款金额为空", null);
            }

            EasySQL sql1 = new EasySQL("UPDATE yq_contract_stage SET RECEIVED_AMT =  ? WHERE STAGE_ID = ?");
            this.getQuery().executeUpdate(sql1.getSQL(),new Object[]{receive_amt, contractStageId});

            double plan_rcv_amt_double = Double.parseDouble(plan_rcv_amt);
            double receive_amt_double = Double.parseDouble(receive_amt);
            final double EPSILON = 1e-9;

            if (receive_amt_double > plan_rcv_amt_double || Math.abs(receive_amt_double - plan_rcv_amt_double) < EPSILON) {
                setCompleteDateForContractStage(contractStageId);
            } else {
                removeCompleteDateForContractStage(contractStageId);
            }
        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
        }
    }

    private void removeCompleteDateForContractStage(String contractStageId) {
        String sql = "UPDATE yq_contract_stage SET ACT_COMP_DATE = '' WHERE STAGE_ID = ?";
        try {
            this.getQuery().executeUpdate(sql, contractStageId);
        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
        }

    }

    private void setCompleteDateForContractStage(String contractStageId) {
        String sql = "UPDATE yq_contract_stage SET ACT_COMP_DATE = (select RECEIPT_DATE from yq_contract_receipt where STAGE_ID = ? ORDER BY RECEIPT_DATE DESC LIMIT 1 ) WHERE STAGE_ID = ? ";
        try {
            this.getQuery().executeUpdate(sql, new Object[]{contractStageId, contractStageId});
        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
        }

    }


}
