<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>合同评审</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
				 		<div class="flow-title">${flow.flowTitle}</div>
				 		<div class="flow-btn">
				 			<button class="btn btn-default btn-sm addUpdateLog" onclick="addUpdateLog();" type="button"><i class="fa fa-plus" aria-hidden="true"></i> 添加修订</button>
				 		</div>
				    	<table class="table table-vzebra baseInfo flow-table">
					  		<tr>
					  			<td style="width: 10%;">评审编号</td>
					  			<td style="width: 25%;"><input type="text" data-rules="required" class="form-control" name="apply.applyNo" data-mars="ReviewDao.getReviewNo"/></td>
					  			<td style="width: 10%;">经办人</td>
					  			<td style="width: 23%;"><input type="text" readonly="readonly" class="form-control" name="apply.applyName"/></td>
					  			<td style="width: 10%;">提交时间</td>
					  			<td style="width: 22%;"><input type="text" readonly="readonly" class="form-control" name="apply.applyTime"/></td>
					  		</tr>
					  		<tr>
					  			<td class="required">签约单位</td>
					  			<td>
					  				<input>
					  				<select data-rules="required" class="form-control" name="business.SIGN_ENT">
					  					<option value="yunqu">云趣</option>
					  					<option value="zhongrong">中融</option>
					  					<option value="pci">佳都</option>
					  				</select>
					  			</td>
					  			<td class="required">合同名称</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control" name="business.CONTRACT_NAME"/>
					  			</td>
					  			<td class="required">产品线</td>
					  			<td>
					  				<input class="form-control" data-rules="required" name="business.PROD_LINE">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>售前经理</td>
					  			<td>
					  				<input name="business.PRE_SALES_BY" readonly="readonly" class="form-control">
					  			</td>
					  			<td class="required">销售经理</td>
					  			<td>
					  				<input data-rules="required" readonly="readonly" name="business.SALES_BY"  class="form-control">
					  			</td>
					  			<td class="required">上级主管</td>
					  			<td>
					  				<input data-rules="required" name="business.UP_SALES_NAME" class="form-control"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>合同金额</td>
					  			<td><input type="text" class="form-control" name="business.AMOUNT"/></td>
					  			<td>付款方式</td>
					  			<td><input type="text" class="form-control" name="business.PAY_TYPE"/></td>
					  			<td>维保时间</td>
					  			<td><input type="text" placeholder="维保合同必填" class="form-control" name="business.WB_TIME"/></td>
					  		</tr>
					  		<tr>
					  			<td class="required">签约部门</td>
					  			<td>
					  				<input name="business.SIGN_DEPT_ID" data-rules="required" class="form-control">
					  			</td>
					  			<td class="required">所属客户</td>
					  			<td>
					  				<input type="hidden" class="form-control" name="business.CUST_ID"/>
					  				<input type="text" data-rules="required" class="form-control" name="business.CUST_NAME" id="custId" readonly="readonly"/>
					  			</td>
					  			<td>所属商机</td>
					  			<td>
					  				<input type="text" class="form-control" name="business.BUSINESS_NAME"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>可阅用户</td>
					  			<td colspan="5">
					  				<input name="business.READ_USER_NAMES"  class="form-control">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="5">
					  				<textarea class="form-control" style="height: 100px;" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td style="vertical-align: top;">合同方案附件</td>
					  			<td colspan="5" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  	</table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	<table class="layui-table approve-table" style="margin-top: 10px;padding: 10px;">
					   <thead>
					      <tr>
						      <th></th>
						      <th>序号</th>
						      <th>节点名称</th>
						      <th>经办人</th>
						      <th>评审结果</th>
						      <th class="hidden-print">接收时间</th>
						      <th class="hidden-print">查看时间</th>
						      <th>审批时间</th>
						      <th>审批描述</th>
						      <th class="hidden-print">用时</th>
					      </tr>
					  </thead>
					  <tbody data-mars="FlowDao.approveResult" data-template="approveList"></tbody>
					</table>
					<script id="approveList" type="text/x-jsrender">
					 {{for data}}
						<tr {{if CHECK_RESULT=='0'}} class="hidden-print" {{/if}}>
							<td class="text-c"> 
								<label class="radio radio-inline radio-success">
			                        <input type="radio" value="{{:RESULT_ID}}" data-text="false" name="selectResultId"><span></span>
			                	</label>
							</td>
							<td>{{:#index+1}}</td>
							<td>{{:NODE_NAME}}{{if DATA1=='1'}}-{{:DATA4}}{{/if}}</td>
							<td>{{:CHECK_NAME}}  {{if TRUST_ID}} <a class="hidden-print pull-right" href="javascript:;" onclick="FlowCore.lookTrust('{{:TRUST_ID}}')">查看委托</a> {{/if}}</td>
							<td>{{if NODE_ID!='0'}} {{if DATA2=='1'}}初审 / {{else DATA2=='2'}}终审/{{/if}}  {{call:CHECK_RESULT fn='checkResultLable'}} {{/if}} {{if CC_COUNT>0}} <a class="hidden-print pull-right" href="javascript:;" onclick="FlowCore.lookCC('{{:RESULT_ID}}')">查看抄送</a> {{/if}}</td>
							<td class="hidden-print">{{:GET_TIME}}</td>
							<td class="hidden-print">{{:READ_TIME}}</td>
							<td>{{:CHECK_TIME}}</td>
							<td>{{:CHECK_DESC}}</td>
							<td class="hidden-print">{{call:GET_TIME CHECK_TIME fn='dateDiff'}}</td>
						</tr>
					  {{/for}}
					{{if data.length==0}}
						<tr><td  colspan="9" class="text-c">暂无数据</td></tr>
					{{/if}}
					</script>
				</div>
			</div>
			
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">

		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(result){
				
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
		}
		
		Flow.updateData = function() {
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({reqUrl:'${ctxPath}/servlet/contract/review?action=doApprove'});
		}
		
		function addUpdateLog(){
			popup.layerShow({id:'addUpdateLog',area:['400px','300px'],url:'${ctxPath}/pages/crm/contract/review/include/revise-log.jsp',title:'新增修订日志',data:FlowCore.params});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>