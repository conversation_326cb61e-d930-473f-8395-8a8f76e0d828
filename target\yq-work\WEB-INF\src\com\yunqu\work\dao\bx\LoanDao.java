package com.yunqu.work.dao.bx;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "LoanDao")
public class LoanDao extends AppDaoContext{
	
	@WebControl(name = "loanItems",type = Types.TEMPLATE)
	public JSONObject loanItems() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from yq_flow_loan_item where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by item_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	
	@WebControl(name = "flowList",type = Types.TEMPLATE)
	public JSONObject flowList() {
		EasySQL sql = new EasySQL("select t1.*,t2.pay_type,t2.contact_unit,t3.fee_type,t3.amount,t3.fee_desc,t3.bx_date from yq_flow_apply t1");
		sql.append("INNER JOIN yq_flow_loan_base t2 on t1.apply_id = t2.business_id");
		sql.append("INNER JOIN yq_flow_loan_item t3 on t2.business_id = t3.business_id");
		sql.append("where 1=1");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		sql.append(param.getString("payState"),"and t2.pay_state = ?");
		
		String applyState = param.getString("applyState");
		if("100".equals(applyState)) {
			sql.append(10,"and t1.apply_state >= ?");
		}else {
			sql.append(applyState,"and t1.apply_state = ?");
		}
		
		sql.append(param.getString("checkName"),"and t1.check_name = ?");
		sql.append(param.getString("applyName"),"and t1.apply_name = ?");
		sql.append(param.getString("payState"),"and t2.pay_state = ?");
		sql.append(param.getString("recorded"),"and t2.recorded = ?");
		sql.append("order by t1.apply_time desc,t1.apply_id");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	

}
