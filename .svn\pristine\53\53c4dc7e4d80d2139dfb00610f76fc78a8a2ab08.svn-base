<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<style type="text/css">
	.avatar.size-L, .avatar.size-L img {
	    width: 38px;
	    height: 38px;
        border-radius: 50%;
	}
	.smallCommentList,.comment-write{margin:5px 15px;}
	.smallCommentList .item {list-style: none outside none;margin: 1rem 0 0;padding-right: 0px;}
	.smallCommentList .item{list-style: none outside none;margin: 1rem 0 0}
	.smallCommentList .avatar{border: 1px solid transparent;float: left}
	.comment-main{position:relative;margin-left:54px;border:1px solid #dedede;border-radius:2px}
	.comment-main:before,.comment-main:after{position:absolute;top:11px;left:-16px;right:100%;width:0;height:0;display:block;content:" ";border-color:transparent;border-style:solid solid outset;pointer-events:none}
	.comment-main:before{border-right-color:#dedede;border-width:8px}
	.comment-main:after{border-width:7px;border-right-color:#f8f8f8;margin-top:1px;margin-left:2px}
   	.comment-header{padding:10px 15px;background:#f8f8f8;border-bottom:1px solid #eee}
    .comment-title{margin:0 0 8px 0;font-size:1rem;line-height:1.2}
    .comment-meta{font-size:13px;color:#999;line-height:1.2}
    .comment-meta a{color:#999}
    .comment-author{font-weight:700;color:#999}
    .comment-body{padding:15px 15px 5px;overflow:hidden;font-size: 13px;}
    .comment-body>:last-child{margin-bottom:0}
    .layui-text{line-height: 1.2;font-size: 13px;}
	.comment-write{margin-top: 20px;height: 80px;line-height: 80px;}
</style>
<form id="commentForm">
		<ul class="smallCommentList" data-template="small-revise-template" data-mars="FlowCommon.followList"></ul><br><br>
        <script id="small-revise-template" type="text/x-jsrender">
			{{for data}}
		      <li data-userid="{{:ADD_USER_ID}}" class="item cl"> <a href="javascript:;"><i class="avatar size-L radius"><img  onclick="userInfoLayer('{{:ADD_USER_ID}}')" onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:ADD_USER_ID fn='getUserPic'}}"></i></a>
              <div class="comment-main">
                <header class="comment-header">
                  <div class="comment-meta"><a class="comment-author" onclick="userInfoLayer('{{:ADD_USER_ID}}')" href="javascript:;">{{:ADD_USER_NAME}}</a> 评论于
                    <time>{{:ADD_TIME}}</time>
                  </div>
                </header>
                <div class="comment-body">
                  <p> {{if PREV_FOLLOW_CONTENT}} <blockquote class="layui-elem-quote layui-text"><a href="javascript:;" onclick="userInfoLayer('{{:TO_USER_ID}}')">{{:TO_USER_NAME}}</a>：{{call:PREV_FOLLOW_CONTENT fn='getContent'}} </blockquote> {{else TO_USER_ID}} <a href="javascript:;" onclick="userInfoLayer('{{:TO_USER_ID}}')">@{{:TO_USER_NAME}}</a> {{/if}} {{call:FOLLOW_CONTENT fn='getContent'}}</p>
                  <p class="hidden">{{:FOLLOW_CONTENT}}</p> 
				 <p class="pull-right"><a class="f-12" onclick="toTaFollow('{{:ADD_USER_ID}}','{{:ADD_USER_NAME}}')" href="javascript:;">@TA</a><a class="f-12 ml-10" onclick="replyFollow('{{:FOLLOW_ID}}','{{:ADD_USER_ID}}',this)" href="javascript:;">回复</a></p>
                </div>
              </div>
             </li>
			{{/for}}
			{{if data.length==0}}
				<div style="padding:20px 35%;" ><img src="/easitline-static/images/nodata.png"></div>
			{{/if}}					         
	   </script>  
       <script type="text/javascript">
       
       		var FlowComment = {toUserName:'${param.toUserName}',toUserId:'${param.toUserId}',resultId:'${param.resultId}'};
       		
       		$(function(){
       			loadFollow();
       			if(FlowComment.toUserName){
	       			$('#flowContent').val('@'+FlowComment.toUserName+"：");
       			}
       		});
       		
       		function loadFollow(){
       			$('#commentForm').render({data:FlowCore.params});
       		}
       		
       		function replyFollow(followId,addUserId,el){
       		   layer.prompt({formType: 2,value: '',offset:'20px',title: '请输入回复信息',area: ['400px', '150px']}, function(flowContent, index, elem){
		    	 	layer.close(index);
		    	 	var prevFollowContent = $(el).parent().prev().text();
		    		var data = $.extend({flowContent:flowContent,pFollowId:followId,addUserId:addUserId,prevFollowContent:prevFollowContent},FlowCore.params);
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addComment",setFollowData(data),function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800},function(){
	    						loadFollow();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
		    	});
       		}
       		
       		function toTaFollow(toUserId,toUserName){
       			layer.prompt({formType: 2,value: '',offset:'20px',title: '请输入内容',area: ['400px', '150px']}, function(flowContent, index, elem){
		    	 	layer.close(index);
		    		var data = $.extend({flowContent:flowContent,toUserId:toUserId,toUserName:toUserName},FlowCore.params);
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addComment",setFollowData(data),function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800},function(){
	    						loadFollow();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
		    	});
       		}
       		
       		function setFollowData(data){
       			if(FlowComment.resultId){
       				data['resultId'] = FlowComment.resultId;
       			}
       			return data;
       		}
       		
       		function  commentDetailLayer(){
       			layer.closeAll();
       			FlowCore.commentLayer();
       		}
       		
       </script>
</form>