<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="feeEditForm" autocomplete="off">
	     	    <input type="hidden" id="itemId" value="${param.itemId}"/>
				<table class="table table-vzebra">
			        <tbody>
		                <tr>
		                	<td style="width: 70px;">往返日期</td>
		                	<td colspan="3">
								<input type="text" style="display: inline-block;width: 150px;" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="START_DATE"/>
								<input type="text" style="display: inline-block;width: 150px;" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="DEST_DATE"/>
		                	</td>
		                </tr>
		                <tr>
		                	<td style="width: 70px;">出发城市</td>
		                	<td>
								<input type="text" class="form-control input-sm" name="START_CITY"/>
		                	</td>
		                	<td>到达城市</td>
		                	<td>
								<input type="text" class="form-control input-sm" name="DEST_CITY"/>
		                	</td>
		                </tr>
		                <tr>
		                	<td class="required">费用地点</td>
		                	<td>
								<input type="text" data-rules="required" class="form-control input-sm" name="COST_PLACE"/>
		                	</td>
		                	<td class="required">费用天数</td>
		                	<td>
								<input type="number" data-rules="required" class="form-control input-sm" name="COST_DAY"/>
		                	</td>
		                </tr>
		                <tr>
		                	<td>
		                		电子发票号
		                		<input type="hidden" name="INVOICE_NO"/>
		                		<div style="display: none;width:40%;" class="mt-5 invoiceTpl">
									<input type="text" class="form-control input-sm inputInvoiceNo" style="width: 120px;display: inline-block;"/>
									<button class="btn btn-sm btn-default" type="button" onclick="$(this).parent().remove();">×</button>
		                		</div>
		                	</td>
		                	<td colspan="3" id="invoiceContainer">
								<button class="btn btn-xs btn-default mt-5" type="button" onclick="addInvoice(this)">新增电子发票号</button>
		                	</td>
		                </tr>
		                <tr>
		                	<td class="required">费用说明<br>(公司内部)</td>
		                	<td colspan="3">
								<textarea class="form-control input-sm" data-rules="required" name="FEE_IN_DESC"></textarea>
		                	</td>
		                </tr>
		                <tr>
		                	<td class="required">费用描述</td>
		                	<td colspan="3">
								<textarea class="form-control input-sm" data-rules="required" name="FEE_OUT_DESC"></textarea>
		                	</td>
		                </tr>
			        </tbody>
 				 </table>
				 <div class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" onclick="FeeEdit.ajaxSubmitForm()"> 确 定  </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
	  		
	  		<script id="invoices" type="text/x-jsrender">
				{{for data}}
					<div style="display: inline-block;width:40%;" class="mt-5 invoiceTpl">
						<input type="text" value="{{:no}}" class="form-control input-sm inputInvoiceNo" style="width: 120px;display: inline-block;"/>
						<button class="btn btn-sm btn-default" type="button" onclick="$(this).parent().remove();">×</button>
		             </div>
				{{/for}}
			</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
       
	    var FeeEdit = {};
	    
	    FeeEdit.ajaxSubmitForm = function() {
	    	if(form.validate("#feeEditForm")){
	    		var array = [];
	    		$('.inputInvoiceNo').each(function(){
	    			var val = $(this).val();
	    			if(val)array.push(val);
	    		});
	    		var invoiceNos =  array.join(',');
	    		if(invoiceNos==''){
	    			layer.msg('请填写发票号');
	    			return;
	    		}
	    		$("#feeEditForm [name='INVOICE_NO']").val(invoiceNos);
		    	var itemId = $('#itemId').val();
				var data = form.getJSONObject("#feeEditForm");
				$("[name='feeJson_"+itemId+"']").val(JSON.stringify(data));
				$("[name='feeDesc_"+itemId+"']").val(data['FEE_IN_DESC']);
				popup.layerClose("#feeEditForm");
	    	}
		}
	    
		$(function(){
			renderDate('#feeEditForm');
			var itemId = $('#itemId').val();
			var feeJson = $("[name='feeJson_"+itemId+"']").val();
			if(feeJson){
				var json = eval('(' + feeJson + ')'); 
				fillRecord(json,'','','#feeEditForm');
				redenrInvoiceNO(json);
			}
			if(itemId.length>20){
				ajax.daoCall({loading:false,controls:['BxDao.bxItemInfo'],params:{itemId:itemId}},function(rs){
					var result = rs['BxDao.bxItemInfo'].data;
					fillRecord(result,'','','#feeEditForm');
					if(feeJson==''){
						redenrInvoiceNO(result);
					}
				});
			}
			
			function redenrInvoiceNO(json){
				var val = json['INVOICE_NO'];
				var array  = val.split(',');
				var data = [];
				for(var index in array){
					data.push({no:array[index]});
				}
				var tmp = $.templates('#invoices');
				var html  = tmp.render({data:data});
				$('#invoiceContainer').prepend(html);
			}
			
		});

		function addInvoice(el){
			var newObj = $('.invoiceTpl').first().clone();
			newObj.css('display','inline-block');
			newObj.removeClass('invoiceTpl');
			$('#invoiceContainer').prepend(newObj);
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>