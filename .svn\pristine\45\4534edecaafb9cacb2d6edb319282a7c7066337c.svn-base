package com.yunqu.work.servlet;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.CommentModel;
import com.yunqu.work.model.ProjectModel;
import com.yunqu.work.service.CommentService;
@WebServlet("/servlet/project/*")
public class ProjectServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForUpdateProjectInfo(){
		String sql="select PROJECT_ID,PROJECT_NAME from yq_project";
		try {
			List<EasyRow> list = this.getQuery().queryForList(sql);
			for(EasyRow row:list){
				String projectId = row.getColumnValue("PROJECT_ID");
				String projectName = row.getColumnValue("PROJECT_NAME");
				 if(projectName.length()>13){
					 String[] array = projectName.split(" ");
					 if(array[0].length()==13&&array[0].startsWith("20")){
						 projectName=projectName.substring(14);
					 }
				 }
				 this.getQuery().executeUpdate("update yq_project set PROJECT_NAME = ? where PROJECT_ID = ?",projectName, projectId);
				 this.getQuery().executeUpdate("update yq_project_contract set CONTRACT_NAME = ? where CONTRACT_ID = ?",projectName, projectId);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateProjectTeam(){
		String sql="select t2.user_id,t1.project_id,t2.create_time from yq_task t1 INNER JOIN yq_cc t2 on t2.fk_id=t1.task_id where t1.project_id<>''";
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql,new Object[]{},new JSONMapperImpl());
			list.forEach((JSONObject json) ->{
				String userId = json.getString("USER_ID");
				String projectId = json.getString("PROJECT_ID");
				String createTime = json.getString("CREATE_TIME");
				
				EasyRecord record=new  EasyRecord("yq_project_team","PROJECT_ID","USER_ID");
				record.set("USER_ID", userId);
				record.set("PROJECT_ID", projectId);
				record.set("JOIN_TIME", createTime);
				try {
					this.getQuery().save(record);
				} catch (Exception e) {
					e.printStackTrace();
				}
			});
			try {
				List<JSONObject> list2 = this.getQuery().queryForList("select PROJECT_ID from yq_project where PO<>''",new Object[]{},new JSONMapperImpl());
			
				list2.forEach((JSONObject json2) ->{
					try {
						int count= this.getQuery().queryForInt("select count(1) from yq_project_team where PROJECT_ID = ? ",json2.getString("PROJECT_ID"));
					
						this.getQuery().executeUpdate("update   yq_project  set person_count =? where PROJECT_ID= ?", count,json2.getString("PROJECT_ID"));
					} catch (Exception e) {
						e.printStackTrace();
					}
					
				});
			} catch (Exception e) {
				e.printStackTrace();
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForAdd(){
		ProjectModel model=getModel(ProjectModel.class, "project");
		JSONObject jsonObject=getJSONObject();
		JSONArray teams=jsonObject.getJSONArray("teams");
		model.setCreator(getUserPrincipal().getUserId());
		model.addCreateTime();
		model.setProjectState(0);
		try {
			if(teams!=null){
				for(int i=0;i<teams.size();i++){
					String usertId=teams.getString(i);
					EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM", "PROJECT_ID","USER_ID");
					record.setPrimaryValues(model.getProjectId(),usertId);
					record.set("JOIN_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
				}
				model.set("PERSON_COUNT", teams.size());
			}
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		ProjectModel model=getModel(ProjectModel.class, "project");
		model.set("UPDATE_TIME",EasyDate.getCurrentDateString());
		JSONObject jsonObject=getJSONObject();
		try {
			JSONArray teams=jsonObject.getJSONArray("teams");
			if(teams!=null){
				this.getQuery().executeUpdate("delete from yq_project_team where PROJECT_ID = ?", model.getProjectId());
				for(int i=0;i<teams.size();i++){
					String usertId=teams.getString(i);
					EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM", "PROJECT_ID","USER_ID");
					record.setPrimaryValues(model.getProjectId(),usertId);
					record.set("JOIN_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
				}
				model.set("PERSON_COUNT", teams.size());
			}
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateTeam(){
		EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
		record.setColumns(getJSONObject());
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateModule(){
		EasyRecord record=new EasyRecord("YQ_PROJECT_MODULE","MODULE_ID");
		try {
			record.setColumns(getJSONObject());
			record.set("UPDATE_BY", getUserId());
			record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			if(StringUtils.isBlank(record.getString("MODULE_NAME"))){
				return EasyResult.fail("模块名不能为空.");
			}
			if(StringUtils.notBlank(record.getString("MODULE_ID"))){
				this.getQuery().update(record);
			}else{
				record.setPrimaryValues(RandomKit.randomStr());
				this.getQuery().save(record);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		ProjectModel model=getModel(ProjectModel.class, "project");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	
	public EasyResult actionForAddFavorite(){
		EasyRecord record=new EasyRecord("YQ_FAVORITE","FAVORITE_ID");
		try {
			String fkId=getJsonPara("fkId");
			String sql="select count(1) from YQ_FAVORITE where fk_id = ? and favorite_by = ?";
			int count=this.getQuery().queryForInt(sql, fkId,getUserId());
			if(count>0){
				return EasyResult.fail("不能重复关注!");
			}
			record.setPrimaryValues(RandomKit.uuid());
			record.set("fk_id", fkId);
			record.set("favorite_by", getUserId());
			record.set("favorite_time",EasyDate.getCurrentDateString());
			this.getQuery().save(record);
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelFavorite(){
		try {
			String favoriteId=getJsonPara("favoriteId");
			this.getQuery().executeUpdate("delete from YQ_FAVORITE  where fk_id = ? and favorite_by = ?",favoriteId,getUserId());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelProject(){
		try {
			String projectId=getJsonPara("projectId");
			this.getQuery().executeUpdate("update yq_project set is_delete =1  where project_id = ?",projectId);
			this.getQuery().executeUpdate("delete from yq_project  where project_id = ?",projectId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForSaveComment(){
		JSONObject jsonObject=getJSONObject();
		CommentModel model=new CommentModel();
		model.setFkId(jsonObject.getString("fkId"));
		model.setContent(jsonObject.getString("content"));
		model.setCreator(getUserPrincipal().getUserId());
		model.setCreateTime(EasyDate.getCurrentDateString());
		model.setUserAgent(getRequest().getHeader("user-agent"));
		model.setIp(WebKit.getIP(getRequest()));
		return CommentService.getService().addComment(model);
	}
	public EasyResult actionForUpdateProjectState(){
		JSONObject jsonObject=getJSONObject();
		EasyRecord model=new EasyRecord("yq_project_state_log","operate_id");
		model.set("operate_id", RandomKit.uuid());
		model.set("update_by",getUserPrincipal().getUserId());
		model.set("update_time",EasyDate.getCurrentDateString());
		model.set("project_id", jsonObject.getString("projectId"));
		model.set("update_state", jsonObject.getString("projectState"));
		model.set("update_reason", jsonObject.getString("projectUpdateDesc"));
		model.set("font_color", jsonObject.getString("color"));
		try {
			this.getQuery().executeUpdate("update yq_project set project_state = ? ,UPDATE_TIME = ? where project_id  = ?",jsonObject.getString("projectState"),EasyDate.getCurrentDateString(),jsonObject.getString("projectId"));
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddProjectWorkHour(){
		JSONObject jsonObject=getJSONObject();
		String year=jsonObject.getString("year");
		String month=jsonObject.getString("month");
		String monthId=year+month;
		JSONArray array = jsonObject.getJSONArray("userId");
		for(int i=0;i<array.size();i++){
			String userId = array.getString(i);
			try {
				getQuery().executeUpdate("delete from yq_project_work_hour where month_id = ? and worker = ?",monthId,userId);
			} catch (SQLException e1) {
				this.error(e1.getMessage(), e1);
			}
			JSONArray items = new JSONArray();
			if(jsonObject.get(userId+"_id") instanceof String){
				items.add(jsonObject.getString(userId+"_id"));
			}else{
				items = jsonObject.getJSONArray(userId+"_id");
			}
			if(items==null||items.size()==0){
				continue;
			}
			for(int j=0;j<items.size();j++){
				String item = items.getString(j);
				String projectId=jsonObject.getString(userId+"_project_"+item);
				String time=jsonObject.getString(userId+"_time_"+item);
				String projectName=jsonObject.getString(userId+"_name_"+item);
				if(StringUtils.isAnyBlank(projectId,time)){
					continue;
				}
				EasyRecord record=new EasyRecord("yq_project_work_hour");
				record.set("year",year);
				record.set("month_id",monthId);
				record.set("worker",userId);
				record.set("project_id",projectId);
				record.set("work_time",time);
				record.set("project_name",projectName);
				record.set("create_time",EasyDate.getCurrentDateString());
				record.set("creator",getUserId());
				record.set("dept_id",getUserPrincipal().getAttribute("deptId"));
				try {
					this.getQuery().save(record);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
			
		}
		return EasyResult.ok();
	}
	
}




