<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
		.ibox-content{min-height: calc(100vh - 80px);}
		.isProjectAward-tr{
			display: none;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="projectEditForm" class="form-horizontal" data-mars="ProjectDao.record" autocomplete="off" data-mars-prefix="project.">
 		   		<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
 		   		<input type="hidden" id="projectId" value="${param.projectId}" name="project.PROJECT_ID"/>
 		   		<input type="hidden" value="${param.projectId}" name="fkId"/>
 		   		<div class="ibox-content" style="padding: 15px;">
				   <div class="layui-tab layui-tab-brief">
					  <ul class="layui-tab-title">
					    <li class="layui-this">基本信息</li>
					    <li>更多设置</li>
					    <li>日志记录</li>
					  </ul>
					  <div class="layui-tab-content">
					    <div class="layui-tab-item layui-show">
							<table class="table table-edit table-vzebra">
						        <tbody>
						            <tr>
					                    <td width="80px" class="required">项目名称</td>
					                    <td style="width: 40%"><input data-rules="required"  type="text" name="project.PROJECT_NAME" class="form-control input-sm"></td>
					                    <td width="80px" class="required">合同编号</td>
					                    <td style="width: 40%"><input placeholder="若无填写0"  data-rules="required"  type="text" name="project.PROJECT_NO" id="projectNo" class="form-control input-sm"></td>
						            </tr>
						            <tr>
					                    <td>项目集</td>
					                    <td>
					                    	<input readonly="readonly" onclick="singleDict(this,'projectItemSet')" name="project.PROJECT_ITEM" class="form-control input-sm">
					                    </td>
					                    <td width="80px">上级项目</td>
					                    <td>
					                    	<input type="hidden" name="project.P_PROJECT_ID" class="form-control input-sm">
					                    	<input type="text" name="project.P_PROJECT_NAME" readonly="readonly" placeholder="如有框架合同请填写" onclick="singleProject(this);" class="form-control input-sm">
					                    </td>
						            </tr>
						            <tr>
					                    <td>项目经理</td>
					                    <td>
					                    	<input name="project.PROJECT_PO" type="hidden">
					                    	<input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="project.PROJECT_PO_NAME"/>
					                    </td>
					                    <td>开发负责人</td>
					                    <td>
					                    	<input name="project.PO" type="hidden">
					                    	<input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="project.PO_NAME"/>
					                    </td>
					                </tr>
					                <tr>
					                    <td>销售</td>
					                    <td>
					                    	<input name="project.SALES_BY" type="hidden">
					                    	<input type="text" placeholder="合同同步无需填写"  readonly="readonly" onclick="singleUser(this);" class="form-control input-sm" name="project.SALES_BY_NAME"/>
					                    </td>
					                    <td>售前</td>
					                    <td>
					                    	<input name="project.PRE_SALES_BY" type="hidden">
					                    	<input type="text" placeholder="合同同步无需填写" readonly="readonly" onclick="singleUser(this);" class="form-control input-sm" name="project.PRE_SALES_NAME"/>
					                    </td>
					                </tr>
						            <tr>
						             	 <td>开工日期</td>
					                     <td>
						                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.KG_DATE" class="form-control input-sm Wdate">
					                     </td>
					                     <td>工程大区</td>
					                     <td>
					                    	<input name="project.DEPT_ID" type="hidden">
					                    	<input type="text" data-dept-name="工程"  onclick="singleDept(this);"  class="form-control input-sm" name="project.PROJECT_DEPT_NAME"/>
					                     </td>
					                </tr>
						            <tr>
					                    <td class="required">提醒</td>
					                    <td>
					                    	<small>如需更改验收日期，需要提交项目变更计划 </small>
					                    	<button type="button" onclick="hisPlanFlow(this)" class="btn btn-xs btn-link">变更查询</button>
					                    	<button type="button" onclick="applyPlanFlow(this)" class="btn btn-xs ml-5 btn-info">+项目计划变更</button>
					                    </td>
					                     <td class="required">产品线</td>
				                    	<td>
					                    	<input lay-verify="required" readonly="readonly" onclick="singleDict(this,'Y001')" name="project.PRODUCT_LINE" class="form-control input-sm">
				                    	</td>
					                </tr>
									<tr>
										<td>考核日期</td>
										<td>
											<input type="text" name="project.KH_DATE" style="background-color: #f0ad4e;" readonly="readonly" placeholder="计划流程同步" class="form-control input-sm">
										</td>
										<td>考核说明</td>
										<td>
											<small>考核日期默认以项目首次计划初验日期为准，若发生首次变更则调整为第一次变更后的初验日期；后续若再次变更，考核日期仍维持首次变更后的初验日期，不再调整。</small>
										</td>
									</tr>
						            <tr>
					                    <td class="required">计划初验日期</td>
					                    <td>
						                    <input type="text" readonly="readonly" placeholder="计划流程同步"  name="project.CY_DATE"  class="form-control input-sm">
					                    </td>
					                    <td class="required">计划终验日期</td>
					                    <td>
						                    <input type="text" readonly="readonly" placeholder="计划流程同步"  name="project.ZY_DATE"  class="form-control input-sm">
					                    </td>
					                </tr>
						            <tr>
					                    <td>实际初验日期</td>
					                    <td>
						                    <input type="text" readonly="readonly" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',onpicked:function(){checkCyRealDate(this);}})" placeholder="盖章确认的初验日期"  name="project.CY_REAL_DATE"  class="form-control input-sm Wdate">
					                    </td>
					                    <td>实际终验日期</td>
					                    <td>
						                    <input type="text" readonly="readonly" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" placeholder="盖章确认的终验日期"  name="project.ZY_REAL_DATE"  class="form-control input-sm Wdate">
					                    </td>
					                </tr>
						            <tr>
					                    <td>维保开始日期</td>
					                    <td>
						                    <input type="text" readonly="readonly" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.WB_BEGIN_DATE" class="form-control input-sm Wdate">
					                    </td>
					                    <td>维保结束日期</td>
					                    <td>
						                    <input type="text" readonly="readonly" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.WB_END_DATE" class="form-control input-sm Wdate">
					                    </td>
					                </tr>
						            <tr>
					                    <td>
					                      	签订日期
					                    </td>
					                    <td>
						                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.SIGN_DATE" class="form-control input-sm Wdate">
					                    </td>
					                    <td>项目起止时间</td>
					                    <td>
						                    <input id="beginDate" type="text" style="display: inline-block;width: 120px" placeholder="开工日期"  onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.BEGIN_DATE"  class="form-control input-sm Wdate">
						                    <input id="endDate" type="text" style="display: inline-block;width: 120px" placeholder="计划完成时间" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" id="orderFinishTime" name="project.END_DATE" class="form-control input-sm Wdate">
					                    	<div class="btn-group">
					                   			<button onclick="fillDate(90)" class="btn btn-sm btn-default" type="button">3个月</button>
					                   			<button onclick="fillDate(180)" class="btn btn-sm btn-default" type="button">半年</button>
					                   			<button onclick="fillDate(365)" class="btn btn-sm btn-default" type="button">一年</button>
					                   		</div>
					                    </td>
					                </tr>
						            <tr>
					                    <td class="required">上线日期</td>
					                    <td>
						                    <input type="text" name="project.SX_DATE" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="form-control input-sm Wdate">
					                    </td>
					                	<td class="required">公开性</td>
					                    <td>
											<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="0" checked="checked" name="project.PROJECT_PUBLIC"/> <span>公开·企业全员可访问</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="1" name="project.PROJECT_PUBLIC"/> <span>私密·仅成员可访问</span>
					                    	</label>
					                    </td>
					                </tr>
						            <tr>
					                    <td class="required">开发进度/%</td>
					                    <td>
						                    <input type="number" name="project.DEV_RATE" placeholder="0-100%" class="form-control input-sm">
					                    </td>
					                    <td class="required">合同类型</td>
					                    <td>
						                    <input type="text" name="project.CONTRACT_TYPE" readonly="readonly" class="form-control input-sm" onclick="singleDict(this,'Y004')">
					                    </td>
					                </tr>
					                <tr>
					                	<td class="required">项目阶段</td>
					                    <td>
					                    	<select class="form-control input-sm" name="project.PROJECT_STAGE" onchange="editProjectState(this);">
					                    		  <option value="">请选择</option>
					                    		  <option value="提前执行">提前执行</option>
					                    		  <option value="提前执行-废弃">提前执行-废弃</option>
					                    		  <option value="提前执行-已正式签订">提前执行-已正式签订</option>
					                    		  <option value="待初验">待初验</option>
												  <option value="待一次性验收">待一次性验收</option>
												  <option value="待终验">待终验</option>
												  <option value="已终验，维保在保">已终验，维保在保</option>
												  <option value="已终验，维保到期">已终验，维保到期</option>
												  <option value="维保到期">维保到期</option>
												  <option value="维保在保">维保在保</option>
												  <option value="在建合作运营">在建合作运营</option>
												  <option value="合作运营结项">合作运营结项</option>
												  <option value="异常终止">异常终止</option>
					                    	</select>
					                    </td>
						              	<td>
						            		项目类型
						            	</td>
						            	<td>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="10" name="project.PROJECT_TYPE"/> <span>合同项目</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="11" name="project.PROJECT_TYPE"/> <span>提前执行</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="30" name="project.PROJECT_TYPE"/> <span>维保项目</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="20" checked="checked" name="project.PROJECT_TYPE"/> <span>自研项目</span>
					                    	</label>
						            	</td>
						            </tr>
					                <tr>
						              	<td>
						            		项目进度
						            	</td>
						            	<td>
						            		<select class="form-control input-sm" name="project.PROJECT_STATE">
						            			<option value="11">进度正常</option>
						            			<option value="12">存在风险</option>
						            			<option value="13">进度失控</option>
						            			<option value="14">进度提前</option>
						            			<option value="15">进度滞后</option>
						            			<option value="20">挂起中</option>
						            			<option value="30">已完成</option>
						            		</select>
						            	</td>
						            	<td class="required">项目级别</td>
					                    <td>
					                    	<select class="form-control input-sm" name="project.PROJECT_LEVEL">
					                    		<option value="1">正常</option>
					                    		<option value="2">紧急</option>
					                    		<option value="3">重要</option>
					                    		<option value="4">重要且紧急</option>
					                    		<option value="9">外包</option>
					                    	</select>
					                    </td>
						            </tr>
						            <tr>
						            	<td>WebhookKey</td>
						               	<td>
				                           <input class="form-control input-sm" placeholder="企微webhook的key" name="project.WEBHOOK">
						               	</td>
						            	<td>Webhook通知范围</td>
						               	<td>
				                           	<label class="checkbox checkbox-info radio-inline">
				                    			<input type="checkbox" value="risk" checked="checked" name="WebhookNotices"/> <span>风险</span>
					                    	</label>
				                           	<label class="checkbox checkbox-info radio-inline">
				                    			<input type="checkbox" value="feedback" checked="checked" name="WebhookNotices"/> <span>反馈</span>
					                    	</label>
				                           	<label class="checkbox checkbox-info radio-inline">
				                    			<input type="checkbox" value="edit" checked="checked" name="WebhookNotices"/> <span>修改</span>
					                    	</label>
						               	</td>
						            </tr>
						            <tr>
						            	<td>当前责任方</td>
						            	<td>
					                    	<input name="project.PROBLEM_BY" readonly type="text" class="form-control input-sm" placeholder="同步过来">
										</td>
						            	<td class="isProjectAward-tr">是否发项目奖</td>
						            	<td class="isProjectAward-tr">
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="1" checked="checked" name="project.IS_PROJECT_AWARD" onchange="updateAwardReasonTitle(1)"/> <span>是</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="0" name="project.IS_PROJECT_AWARD" onchange="updateAwardReasonTitle(1)"/> <span>否</span>
					                    	</label>
						            	</td>
						            </tr>
									<tr style="display: none;" class="sendAwardReason">
										<td id="awardReasonTitle">发放项目奖原因</td>
										<td colspan="3">
											<textarea name="project.PROJECT_AWARD_REASON" class="form-control input-sm"></textarea>
										</td>
									</tr>
						        </tbody>
			 				</table>
			 				<p class="text-l ml-50 mt-30" style="z-index: 999999999;margin-left: 40%;">
						    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="Project.ajaxSubmitForm()"> 保 存 </button>
							</p>
					    </div>
					    <div class="layui-tab-item">
					    	<table class="layui-table">
				    		    <tr>
					            	<td class="required" style="width: 100px;">项目描述</td>
					               	<td colspan="3">
			                           <textarea id="wordText" style="height: 150px;" class="form-control input-sm" name="project.PROJECT_DESC"></textarea>
					               	</td>
					            </tr>
					            <tr>
					            	<td class="required">公告栏</td>
					               	<td colspan="3">
			                           <textarea style="height: 150px;" class="form-control input-sm" name="project.PROJECT_NOTICE"></textarea>
					               	</td>
					            </tr>
					            <tr>
					            	<td></td>
					            	<td colspan="3">
								    	<EasyTag:hasRole roleId="SYS_SUPER_USER">
										      <button class="btn btn-default btn-info" onclick="transferProject()" type="button">项目转移</button>
									    </EasyTag:hasRole>
					            	</td>
					            </tr>
					    	</table>
					    </div>
					    <div class="layui-tab-item">
					    	<table id="logTable"></table>
					    </div>
					  </div>
					</div>
				</div>
  		</form>

  		<div style="display: none;" id="transferProject">
				<div style="padding: 15px;">
					<input id="transferProjectId" type="hidden"/>
					<input id="transferProjectName" placeholder="请选择新的项目"  onclick="singleProject(this);" class="form-control input-sm"  type="text"/>
				</div>
			</div>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">

		jQuery.namespace("Project");

		Project.projectId='${param.projectId}';
		var projectId='${param.projectId}';

		var oldData = null;
		$(function(){
		    $('[data-toggle="tooltip"]').tooltip();
			$("#projectEditForm").render({success:function(result){
				oldData = result['ProjectDao.record'].data;
				loadLog();
				updateAwardReasonTitle(0); // 初始化标题
			}});
			
			if(top.isProjectMgr=='1'){
				$('.isProjectAward-tr').show();
			}
		});

		Project.ajaxSubmitForm = function(){
			if(form.validate("#projectEditForm")){
				if(Project.projectId){
					Project.updateData();
				}else{
					Project.insertData();
				}
			};
		}
		Project.insertData = function(flag) {
			$("#projectEditForm #projectId").val($("#randomId").val());
			var data = form.getJSONObject("#projectEditForm");
			ajax.remoteCall("${ctxPath}/servlet/project?action=add",data,function(result) {
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Project.updateData = function(flag) {
			var data = form.getJSONObject("#projectEditForm");

			// 对比修改前后的数据变化
			var logInfo = [];
			var newData = {};

			// 提取project.xxx格式的数据
			for(var key in data) {
				if(key.startsWith('project.')) {
					var fieldKey = key.replace('project.', '');
					newData[fieldKey] = data[key];
				}
			}

			// 定义字段中文名映射
			var fieldNameMap = {
				PROJECT_NAME: "项目名称",
				PROJECT_NO: "合同编号",
				PROJECT_PO: "项目经理",
				PO: "开发负责人",
				PROJECT_STAGE: "项目阶段",
				PROJECT_STATE: "项目进度",
				PROJECT_DESC: "项目描述",
				PROJECT_TYPE: "项目类型",
				PROJECT_LEVEL: "项目级别",
				PROJECT_PUBLIC: "公开性",
				DEV_RATE: "开发进度(%)",
				CONTRACT_TYPE: "合同类型",
				BEGIN_DATE: "开始日期",
				END_DATE: "结束日期",
				CY_DATE: "计划初验日期",
				ZY_DATE: "计划终验日期",
				CY_REAL_DATE: "实际初验日期",
				ZY_REAL_DATE: "实际终验日期",
				WB_BEGIN_DATE: "维保开始日期",
				WB_END_DATE: "维保结束日期",
				KG_DATE: "开工日期",
				SIGN_DATE: "签约日期",
				SX_DATE: "上线日期",
				P_PROJECT_NAME: "上级项目",
				DEPT_ID: "工程大区",
				PRODUCT_LINE: "产品线",
				WEBHOOK: "WebhookKey",
				SALES_BY: "销售",
				PRE_SALES_BY: "售前",
				PRE_SALES_BY_NAME: "售前",
				PROJECT_DEPT_NAME: "工程大区",
				IS_PROJECT_AWARD: "是否发项目奖",
				PROJECT_AWARD_REASON: "项目奖原因"
			};

			// 项目进度状态映射
			var stateMap = {
				"11": "进度正常",
				"12": "存在风险",
				"13": "进度失控",
				"14": "进度提前",
				"15": "进度滞后",
				"20": "挂起中",
				"30": "已完成"
			};

			// 项目类型映射
			var typeMap = {
				"10": "合同项目",
				"11": "提前执行",
				"20": "自研项目",
				"30": "维保项目"
			};

			// 检查每个字段的变化
			for(var key in newData) {
				if(oldData[key] != newData[key]) {
					var fieldName = fieldNameMap[key] || key;
					var oldValue = oldData[key] || "未设置";
					var newValue = newData[key] || "未设置";

					if(key === 'IS_PROJECT_AWARD') {
						oldValue = oldValue === "1" ? "是" : "否";
						newValue = newValue === "1" ? "是" : "否";
						logInfo.push(fieldName + "：" + oldValue + " → " + newValue);
						continue;
					}

					if(key === 'PROJECT_AWARD_REASON') {
						if(oldValue === "未设置") oldValue = "无";
						if(newValue === "未设置") newValue = "无";
						logInfo.push(fieldName + "：" + oldValue + " → " + newValue);
						continue;
					}

					// 处理项目进度的特殊显示
					if(key === 'PROJECT_STATE') {
						oldValue = stateMap[oldValue] || oldValue;
						newValue = stateMap[newValue] || newValue;
					}

					// 处理项目类型的特殊显示
					if(key === 'PROJECT_TYPE') {
						oldValue = typeMap[oldValue] || oldValue;
						newValue = typeMap[newValue] || newValue;
					}

					// 处理是否发项目奖的显示
					if(key === 'IS_PROJECT_AWARD') {
						oldValue = oldValue === "1" ? "是" : "否";
						newValue = newValue === "1" ? "是" : "否";
					}

					// 处理开发进度，添加百分号
					if(key === 'DEV_RATE') {
						if(oldValue !== "未设置") oldValue = oldValue + "%";
						if(newValue !== "未设置") newValue = newValue + "%";
					}

					// 处理人员ID和部门ID相关字段
					if(['PO', 'PROJECT_PO', 'PRE_SALES_BY', 'SALES_BY', 'P_PROJECT_ID', 'DEPT_ID'].includes(key)) {
						// 使用对应的NAME字段值
						var nameKey = key + '_NAME';
						if(key === 'P_PROJECT_ID') nameKey = 'P_PROJECT_NAME';
						if(key === 'DEPT_ID') nameKey = 'PROJECT_DEPT_NAME';
						// 特殊处理PRE_SALES_BY字段
						if(key === 'PRE_SALES_BY') nameKey = 'PRE_SALES_BY_NAME';

						newValue = newData[nameKey] || newValue;
						oldValue = oldData[nameKey] || oldValue;

						// 如果是人员字段，处理多人显示
						if(['PO', 'PROJECT_PO', 'PRE_SALES_BY', 'SALES_BY'].includes(key)) {
							if(oldValue === "未设置") oldValue = "无";
							if(newValue === "未设置") newValue = "无";
						}
					}

					// 如果是日期字段且值为空，显示为"未设置"
					if(key.indexOf('DATE') > -1) {
						if(!oldValue || oldValue === '') oldValue = "未设置";
						if(!newValue || newValue === '') newValue = "未设置";
					}

					// 跳过某些不需要记录的字段
					if(key.endsWith('_NAME') || ['P_PROJECT_NAME', 'PROJECT_DEPT_NAME', 'PRE_SALES_NAME'].includes(key)) continue;

					logInfo.push(fieldName + "：" + oldValue + " → " + newValue);
				}
			}

			data['logInfoStr'] = logInfo.join("；");

			ajax.remoteCall("${ctxPath}/servlet/project?action=update", data, function(result) {
				if(result.state == 1) {
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
					});
					// 更新成功后刷新oldData
					oldData = newData;
				} else {
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

		function transferProject(){
			popup.layerShow({title:'转移到其他项目',content:$('#transferProject'),area:['300px','200px'],type:1,btn:['提交','取消'],yes:function(index){
				var transferProjectId = $("#transferProjectId").val();
				if(transferProjectId==''){
					layer.msg('请选择转移到哪个项目.');
					return;
				}
				layer.close(index);
				layer.confirm("确定转移吗",{icon:7,offset:'20px'},function(i){
					layer.close(i);
					ajax.remoteCall("${ctxPath}/servlet/project?action=transferProject",{newProjectId:transferProjectId,projectId:projectId},function(result) {
						if(result.state == 1){
							layer.msg(result.msg,function(){
								location.reload();
							});
						}else{
							layer.alert(result.msg,{icon: 7});
						}
					});
				});
			}});

		}

		function fillDate(val){
			var v = addDate(val);
			if(v.indexOf("NaN")>-1){
				layer.msg("时间格式不正确,请检查浏览器兼容性或者换chome");
			}else{
				$("#endDate").val(v);
			}
		}
		function addDate(days) {
            if (days == undefined || days == '') {
                days = 1;
            }
            var d=$("#beginDate").val();
            if(d){
	            var date = new Date(d.replace(/-/g,'/'));
	            date.setDate(date.getDate() + days);
	            var month = date.getMonth() + 1;
	            var day = date.getDate();
	            var h = date.getHours();
	            var m = date.getMinutes();
	            return date.getFullYear() + '-' + getFormatDate(month) + '-' + getFormatDate(day);
            }else{
            	return "";
            }
        }
		function getFormatDate(arg) {
            if (arg == undefined || arg == '') {
                return '';
            }
            var re = arg + '';
            if (re.length < 2) {
                re = '0' + re;
            }
            return re;
        }

		function applyPlanFlow(){
			var contractId = $('#projectId').val();
			flowApply('pj_planchange',{contractId:contractId});
		}

		function hisPlanFlow(){
			var contractId = $('#projectId').val();
			window.open('/yq-work/pages/flow/my/flow-query.jsp?flowCode=pj_planchange&q=DATA19&v='+contractId);
		}

		function editProjectState(el){
			var cyRealDate = $("[name='project.CY_REAL_DATE']").val();
			var zyRealDate = $("[name='project.ZY_REAL_DATE']").val();
			if(cyRealDate==''&&zyRealDate==''){
				//layer.msg('填写了实际初验/终验时间后才能修改项目阶段',{icon:7,offset:'20px',time:1200});
				//$(el).val(oldData.PROJECT_STAGE);
			}
			//return false;
		}

		function loadLog(){
			$("#projectEditForm").initTable({
				mars:'ProjectDao.projectLog',
				id:"logTable",
				data:{projectId:'${param.projectId}'},
				limit:30,
				cols: [[
				 {type:'numbers'},
		        {
				    field: 'OP_NAME',
					title: '操作人',
					align:'left',
					width:100
				},{
				    field: 'OP_TIME',
					title: '操作时间',
					align:'left',
					width:140
				},{
				    field: 'CONTENT',
					title: '操作内容',
					align:'left'
				},
				{
				    field: 'OP_PARAM',
					title: '操作参数',
					align:'left'
				}
				]],done:function(result){

				}});
		}

		// 更新项目奖原因标题
		function updateAwardReasonTitle(type) {
			var isAward = $("input[name='project.IS_PROJECT_AWARD']:checked").val();
			var title = isAward === "1" ? "发放项目奖原因" : "不发放项目奖原因";
			$("#awardReasonTitle").text(title);
			if(isAward==0||type=='1'){
				$(".sendAwardReason").show();
			}
		}

		// 检查实际初验日期与考核日期的关系
		function checkCyRealDate(el) {
			var cyRealDate = $(el).val();
			var khDate = $("[name='project.KH_DATE']").val();

			if(cyRealDate && khDate) {
				var cyRealDateTime = new Date(cyRealDate);
				var khDateTime = new Date(khDate);

				if(cyRealDateTime > khDateTime) {
					// 设置不发项目奖
					$("input[name='project.IS_PROJECT_AWARD'][value='0']").prop("checked", true);
					updateAwardReasonTitle(1);
				}else{
					$("input[name='project.IS_PROJECT_AWARD'][value='1']").prop("checked", true);
					updateAwardReasonTitle(1);
				}
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>