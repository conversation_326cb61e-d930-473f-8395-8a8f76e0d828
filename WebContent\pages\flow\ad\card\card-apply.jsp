<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 70px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowName}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td class="copy">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的名片申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">名片类别</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data1">
						  				<option value="">--</option>
						  				<option value="云趣模板">云趣模板</option>
						  				<option value="云趣模板(含个人微信二维码)">云趣模板(含个人微信二维码)</option>
						  				<option value="综合模板">综合模板 </option>
						  				<option value="综合模板(含个人微信二维码)">综合模板(含个人微信二维码)</option>
						  			</select>
					  			</td>
					  			<td class="required">申请数量/盒</td>
					  			<td>
					  				<input type="number" data-rules="required" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">是否加急</td>
					  			<td>
					  				<select data-rules="required" class="form-control input-sm" name="apply.data4">
						  				<option value="否">否</option>
						  				<option value="是">是</option>
						  			</select>
					  			</td>
					  			<td>邮寄地址</td>
					  			<td class="copy">
					  				<input type="text" class="form-control input-sm" placeholder="外区同事填写"  name="apply.data3"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>相关附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  		<tr class="remindInfo">
					  			<td class="required">注意</td>
					  			<td colspan="3">
					  				①名片每盒100张；<br>
									②如选择加急，流程审批结束后，一般当天可以完成印制；如不加急，次日可完成印制。是否加急，制作费会有所不同。
					  			</td>
					  		</tr>
					  </table>
					  <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
						  <legend style="font-size: 16px;">名片信息</legend>
						  <div class="layui-field-box">
							  <table class="table table-vzebra flow-table">
							  		<tr>
							  			<td style="width: 10%">姓名</td>
							  			<td style="width: 10%" class="copy">
							  				<input type="text" data-rules="required" class="form-control input-sm" name="extend.f1"/>
							  			</td>
							  			<td style="width: 10%">英文名</td>
							  			<td style="width: 10%" class="copy">
							  				<input type="text" data-edit-node="hr" class="form-control input-sm" name="extend.f2"/>
							  			</td>
							  			<td style="width: 10%">职位</td>
							  			<td style="width: 10%" class="copy">
							  				<input type="text" data-rules="required" class="form-control input-sm" name="extend.f3"/>
							  			</td>
							  			<td style="width: 10%">部门</td>
							  			<td style="width: 10%" class="copy">
							  				<input type="text" data-rules="required" class="form-control input-sm" name="extend.f4"/>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>手机号码</td>
							  			<td class="copy">
							  				<input type="text" data-rules="required" maxlength="11" class="form-control input-sm" name="extend.f5"/>
							  			</td>
							  			<td>邮箱</td>
							  			<td class="copy">
							  				<input type="text" data-rules="required" class="form-control input-sm" name="extend.f6"/>
							  			</td>
							  			<td>公司名称</td>
							  			<td class="copy">
							  				<input type="text" data-edit-node="hr" data-rules="required" class="form-control input-sm" name="extend.f7"/>
							  			</td>
							  			<td>公司地址</td>
							  			<td class="copy">
							  				<input type="text" data-edit-node="hr" data-rules="required" class="form-control input-sm" name="extend.f8"/>
							  			</td>
							  		</tr>
							  </table>
						  		
						  </div>
					  </fieldset>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>