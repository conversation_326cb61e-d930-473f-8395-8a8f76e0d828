<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
	<style>
		.select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
   			 background-color: #fff;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editConfForm" autocomplete="off" data-mars="OrderFlowDao.nodeInfo" data-mars-prefix="conf.">
	     	    <input type="hidden" id="nodeId" value="${param.nodeId}" name="conf.NODE_ID"/>
	     	    <input type="hidden" value="${param.nodeId}" name="nodeId"/>
	     	    <input type="hidden" value="${param.busiType}" name="conf.BUSI_TYPE"/>
	     	    <input type="hidden" value="${param.busiType}" name="busiType"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px;">节点名称</td>
		                    <td>
		                    	<input name="conf.NODE_NAME" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">节点排序</td>
		                    <td>
		                    	<input name="conf.CHECK_INDEX" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">上一级节点</td>
		                    <td>
		                    	<select name="conf.P_NODE_ID" class="form-control input-sm" data-mars="OrderFlowDao.nodeDict">
		                    		<option value="0">请选择</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">下一级节点</td>
		                    <td>
		                    	<select name="conf.C_NODE_ID" class="form-control input-sm" data-mars="OrderFlowDao.nodeDict">
		                    		<option value="0">请选择</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">审批人</td>
		                    <td>
		                    	<select data-mars="CommonDao.userDict" id="checkUserId" name="conf.USER_ID" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
		                </tr>
		                <tr>
		                    <td style="width: 80px;">状态</td>
		                    <td>
		                    	<select name="conf.NODE_STATE" class="form-control input-sm">
		                    		<option value="0">启用</option>
		                    		<option value="1">暂停</option>
		                    	</select>
		                    </td>
		                </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="NodeConf.ajaxSubmitForm()"> 确 定  </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
        var NodeConf={nodeId:$("#nodeId").val()};
        NodeConf.ajaxSubmitForm = function(){
			if(form.validate("#editConfForm")){
				if(NodeConf.nodeId==''){
					NodeConf.updateData('add'); 
				}else{
					NodeConf.updateData('update'); 
				}
			};
		}
		NodeConf.updateData = function(flag) {
			var data = form.getJSONObject("#editConfForm");
			data['conf.USER_NAME'] = $("#checkUserId").select2("data")[0].text;
			ajax.remoteCall("${ctxPath}/servlet/flow/node?action="+flag+"NodeConf",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose("#editConfForm");
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		$(function(){
			 requreLib.setplugs('select2',function(){
				$("#editConfForm").render({success:function(result){
					$("#editConfForm select").select2({theme: "bootstrap",placeholder:'请选择'});
				}});
			 });
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>