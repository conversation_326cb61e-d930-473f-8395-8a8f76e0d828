package com.yunqu.work.dao;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;

@WebObject(name="HomeDao")
public class HomeDao extends AppDaoContext {
	
	
	@WebControl(name="tweet",type=Types.TEMPLATE)
	public JSONObject tweet(){
		EasyQuery query=getQuery();
		query.setMaxRow(12);
		setQuery(query);
		EasySQL sql=getEasySQL("select * from YQ_REMIND where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append("and remind_type = 0");
		sql.append(0,"and status = ?");
		sql.append(" order by publish_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="notices",type=Types.TEMPLATE)
	public JSONObject notices(){
		EasyQuery query=getQuery();
		query.setMaxRow(8);
		setQuery(query);
		EasySQL sql=getEasySQL("SELECT REMIND_ID,REMIND_TYPE,TITLE,STATUS,PUBLISH_BY,PUBLISH_TIME,VIEW_COUNT,COMMENT_COUNT,CREATE_TIME,CREATOR,ORDER_INDEX FROM YQ_REMIND where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append("and remind_type <> 0");
		sql.append(0,"and status = ?");
		sql.append(" order by publish_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="notes",type=Types.TEMPLATE)
	public JSONObject notes(){
		EasySQL sql=getEasySQL("select * from yq_note where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append(param.getString("noteAuth")," and note_auth = ?");
		sql.append(getUserPrincipal().getUserId()," and creator = ?");
		sql.append("order by update_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="getBugList",type=Types.TEMPLATE)
	public JSONObject getBugList(){
		String sql="select id,title,assignedDate,openedBy from zt_bug where assignedTo = ? ORDER BY assignedDate desc ";
		EasyQuery query=EasyQuery.getQuery(getAppName(), Constants.ZENTAO_DS_NAME);
		query.setMaxRow(12);
		this.setQuery(query);
		if(ServerContext.isLinux()) {
			return queryForList(sql, new Object[]{getUserPrincipal().getLoginAcct()});
		}else {
			return getJsonResult(new JSONArray());
		}
	}
	
	/**
	 * 我发起的任务
	 * @return
	 */
	@WebControl(name="myCreateTaskList",type=Types.TEMPLATE)
	public JSONObject myCreateTaskList(){
		EasyQuery query=this.getQuery();
		query.setMaxRow(8);
		setQuery(query);
		EasySQL sql=new EasySQL("select t1.task_id,t1.task_name,t1.task_state,t1.task_level,t1.plan_started_at,t1.deadline_at,t1.finish_time,t1.assign_user_id,t1.CREATE_TIME,t1.CREATOR,t2.PROJECT_NAME from yq_task t1 LEFT JOIN yq_project t2 on t2.project_id=t1.project_id  where 1=1");
		sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
		sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
		sql.append(getUserPrincipal().getUserId(),"and t1.CREATOR = ?",false);
		sql.append("order by t1.CREATE_TIME desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 我的待办任务
	 * @return
	 */
	@WebControl(name="myTaskList",type=Types.TEMPLATE)
	public JSONObject myTaskList(){
		EasyQuery query=this.getQuery();
		query.setMaxRow(8);
		setQuery(query);
		EasySQL sql=new EasySQL("select t1.task_id,t1.task_name,t1.task_state,t1.task_level,t1.plan_started_at,t1.deadline_at,t1.finish_time,t1.assign_user_id,t1.CREATE_TIME,t1.CREATOR,t2.PROJECT_NAME from yq_task t1 LEFT JOIN yq_project t2 on t2.project_id=t1.project_id  where 1=1");
		sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
		sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
		sql.append(getUserPrincipal().getUserId(),"and t1.ASSIGN_USER_ID = ?",false);
		sql.append("order by t1.CREATE_TIME desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="queryTopicNav",type=Types.TEMPLATE)
	public JSONObject queryTopicNav(){
		EasyQuery query = getQuery();
		query.setMaxRow(9);
		setQuery(query);
		return queryForList("select * from yq_topic_nav order by order_index,RAND()", null);
	}
	
	@WebControl(name="queryKq",type=Types.TEMPLATE)
	public JSONObject queryKq(){
		EasyQuery query = getQuery();
		query.setMaxRow(8);
		setQuery(query);
		return queryForList("select * from yq_kq_data where STAFF_ID = ? order by DK_DATE desc", new Object[]{getLoginAcct()});
	}
	
	@WebControl(name="queryJk",type=Types.TEMPLATE)
	public JSONObject queryJk(){
		EasyQuery query = getQuery();
		query.setMaxRow(8);
		setQuery(query);
		return queryForList("select * from xf_form where USER_ID = ? order by DATE_ID desc", new Object[]{getUserId()});
	}
	
	@WebControl(name="myProject",type=Types.TEMPLATE)
	public JSONObject myProject(){
		EasyQuery query = getQuery();
		query.setMaxRow(6);
		setQuery(query);
		return queryForList("select t1.PROJECT_ID,t1.PROJECT_NAME from yq_project t1 INNER JOIN yq_project_team t2 on t2.PROJECT_ID=t1.PROJECT_ID where t2.USER_ID= ?", new Object[]{getUserId()});
	}
	
	@WebControl(name="queryUserNav",type=Types.TEMPLATE)
	public JSONObject queryUserNav(){
		return queryForList("select * from yq_user_nav where user_id = ? order by order_index", new Object[]{getUserId()});
	}
	
	@WebControl(name="waitDo",type=Types.RECORD)
	public JSONObject waitDo(){
		JSONObject jsonObject=new JSONObject();
		try {
			jsonObject.put("taskNum", getQuery().queryForInt("select count(1) from yq_task where assign_user_id= ? and task_state < 30 ",getUserId()));
			jsonObject.put("weeklyNum", getQuery().queryForInt("select count(1) from yq_weekly where receiver= ? and status = 1 ",getUserId()));
		
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getJsonResult(jsonObject);
	}
	
	@WebControl(name="doc",type=Types.TEMPLATE)
	public JSONObject doc(){
		EasyQuery query=getQuery();
		query.setMaxRow(8);
		setQuery(query);
		EasySQL sql=getEasySQL("select doc_id,folder_id,title,creator,update_time,view_count from YQ_DOC where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append(param.getString("folderId"),"and folder_id = ?");
		sql.append(1,"and doc_auth = ?");
		sql.append("order by order_index asc,update_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	

}



