package com.yunqu.work.servlet.crm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.IncomeConfirmService;
import com.yunqu.work.service.ContractService;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.util.List;

@WebServlet("/servlet/incomeConfirm/*")
public class IncomeConfirmServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public EasyRecord getConfirmModel(String prefix) {
        EasyRecord confirmModel = new EasyRecord("yq_contract_income_confirm", "CONFIRM_ID");
        confirmModel.setColumns(getJSONObject(prefix));
        return confirmModel;
    }

    public EasyResult actionForAdd() {
        EasyRecord confirmModel = getConfirmModel("confirm");
        confirmModel.setPrimaryValues(RandomKit.uuid().toUpperCase());
        confirmModel.set("CREATE_TIME", EasyDate.getCurrentDateString());
        confirmModel.set("CREATOR", getUserName());
        confirmModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        confirmModel.set("UPDATE_BY", getUserName());
        String contractId = confirmModel.getString("CONTRACT_ID");
        String confirmType = confirmModel.getString("CONFIRM_TYPE");
        String stageId = confirmModel.getString("INCOME_STAGE_ID");
        String confirmDate = confirmModel.getString("CONFIRM_DATE");
        confirmModel.set("MONTH_ID", DateUtils.getMonthIdFormat(confirmDate));
        confirmModel.set("YEAR_ID",  confirmDate.substring(0, 4));

        try {
            this.getQuery().save(confirmModel);
            if ("A".equals(confirmType)) {
                ContractService.getService().reloadCount("INCOME_CONFIRM_COUNT", "yq_contract_income_confirm", contractId);
            } else if ("B".equals(confirmType)) {
                ContractService.getService().reloadCount("INCOME_CONFIRM_COUNT_B", "yq_contract_income_confirm", contractId);
            }
            IncomeConfirmService.getService().setConfirmInfoForIncomeStage(stageId, confirmType);
        } catch (SQLException e) {
            this.error(null, e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdate() {
        EasyRecord confirmModel = getConfirmModel("confirm");
        confirmModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        confirmModel.set("UPDATE_BY", getUserName());
        String confirmType = confirmModel.getString("CONFIRM_TYPE");
        String stageId = confirmModel.getString("INCOME_STAGE_ID");
        String confirmDate = confirmModel.getString("CONFIRM_DATE");
        confirmModel.set("MONTH_ID", DateUtils.getMonthIdFormat(confirmDate));
        confirmModel.set("YEAR_ID",  confirmDate.substring(0, 4));
        String oldStageId = getJsonPara("oldStageId");

        try {
            this.getQuery().update(confirmModel);
        } catch (SQLException e) {
            this.error(null, e);
            return EasyResult.fail(e.getMessage());
        }
        if (StringUtils.isNotEmpty(stageId) && !stageId.equals(oldStageId)) {
            IncomeConfirmService.getService().setConfirmInfoForIncomeStage(oldStageId, confirmType);
        }
        IncomeConfirmService.getService().setConfirmInfoForIncomeStage(stageId, confirmType);
        return EasyResult.ok();
    }

    public EasyResult actionForBatchDel() {
        JSONArray confirmArray = getJSONArray();
        for (int i = 0; i < confirmArray.size(); i++) {
            JSONObject confirmObject = confirmArray.getJSONObject(i);
            String stageId = confirmObject.getString("INCOME_STAGE_ID");
            String contractId = confirmObject.getString("CONTRACT_ID");
            String confirmType = confirmObject.getString("CONFIRM_TYPE");
            try {
                this.getQuery().executeUpdate("delete from yq_contract_income_confirm where CONFIRM_ID = ?", confirmObject.getString("CONFIRM_ID"));
                if ("A".equals(confirmType)) {
                    ContractService.getService().reloadCount("INCOME_CONFIRM_COUNT", "yq_contract_income_confirm", contractId);
                } else if ("B".equals(confirmType)) {
                    ContractService.getService().reloadCount("INCOME_CONFIRM_COUNT_B", "yq_contract_income_confirm", contractId);
                }
                IncomeConfirmService.getService().setConfirmInfoForIncomeStage(stageId, confirmType);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }


    public EasyResult actionForReloadAllCompleteDateForIncomeStage() {
        EasySQL sql = new EasySQL("select * from yq_contract_income_stage");
        try {
            List<EasyRow> rows = this.getQuery().queryForList(sql.getSQL(), sql.getParams());
            for (EasyRow row : rows) {
                String amount = row.getColumnValue("UNCONFIRM_AMOUNT_A");
                String incomeStageId = row.getColumnValue("INCOME_STAGE_ID");
                if ("0.00".equals(amount)) {
                    String confirmDate = this.getQuery().queryForString("select CONFIRM_DATE from yq_contract_income_confirm where INCOME_STAGE_ID = ? and CONFIRM_TYPE = ? ORDER BY CONFIRM_DATE DESC LIMIT 1", new Object[]{incomeStageId, "A"});
                    IncomeConfirmService.getService().setCompleteDateForIncomeStage(incomeStageId, confirmDate);
                } else {
                    IncomeConfirmService.getService().removeCompleteDateForIncomeStage(incomeStageId);
                }
            }
            return EasyResult.ok();
        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
    }
}
 
    

