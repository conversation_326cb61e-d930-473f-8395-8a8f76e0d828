<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>nav</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="FolderDao.linkInfo" autocomplete="off" data-mars-prefix="link.">
     		   <input type="hidden" value="${param.linkId}" name="linkId"/>
     		   <input type="hidden" value="${param.projectId}" name="link.PROJECT_ID"/>
     		   <input type="hidden" value="${param.pId}" name="link.P_ID"/>
     		   <input type="hidden" value="${param.linkId}" name="link.LINK_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td class="required">链接</td>
		                    <td><input value="" data-rules="required" placeholder="金山或腾讯飞书在线文档,SVN,GIT地址等" type="text" name="link.LINK_URL" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td style="width: 90px" class="required">链接名称</td>
		                    <td><input data-rules="required"  type="text" name="link.LINK_NAME" class="form-control input-sm"></td>
			            </tr>
		            	<tr>
		            		<td>状态</td>
		            		<td>
		            			<label class="radio-inline">
								  <input type="radio" checked="checked" name="link.STATE" value="0"> 启用
								</label>
								<label class="radio-inline">
								  <input type="radio" name="link.STATE" value="1"> 暂停
								</label>
		            		</td>
		            	</tr>
			            <tr>
		                    <td>备注</td>
		                    <td>
			                   	 <textarea name="link.REMARK" placeholder="账号密码等信息" style="height: 80px" class="form-control input-sm"></textarea>
		                    </td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				 		  <c:if test="${!empty param.linkId}">
						    	  <button type="button" class="btn btn-info btn-sm"  onclick="Edit.del()"> 删除 </button>
				 		  </c:if>
				    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		jQuery.namespace("Edit");
		
		Edit.linkId='${param.linkId}';
		
		$(function(){
			$("#editForm").render({success:function(){
				
			}});  
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.linkId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/folder?action=addLink",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadLink();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/folder?action=updateLink",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						reloadLink();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		};
		Edit.del=function(data){
			layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/folder?action=deleteLink",{'link.LINK_ID':Edit.linkId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							reloadLink();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>