<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>主机管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="fa fa-desktop"></span> 主机管理</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">所属机房</span>	
							 <select onchange="list.query()" data-value="${param.serverRoomId}" id="serverRoomId" data-mars="ServerRoomDao.dict" name="serverRoomId" class="form-control input-sm">
				                    <option value="">请选择</option>
				              </select>
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">主机名称</span>	
							 <input type="text" name="hostName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 添加</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
			<a class="layui-btn layui-btn-info layui-btn-xs" lay-event="list.monitor">监控</a>
  			{{if currentUserId == CREATOR}}<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
  			<a class="layui-btn layui-btn-xs  layui-btn-normal" lay-event="list.detail">详情</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'HostDao.list',
					autoFill:true,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'HOST_NAME',
						title: '主机名称',
						align:'left'
					},{
					    field: 'SERVICE_ROOM_NAME',
						title: '所属机房',
						align:'left'
					},{
					    field: 'PRIVATE_IP',
						title: '内网IP',
						align:'left'
					},{
					    field: 'PUBLIC_IP',
						title: '公网IP',
						align:'left'
					},{
					    field: 'MEMORY',
						title: '内存',
						align:'left'
					},{
					    field: 'DISK_SIZE',
						title: '磁盘',
						align:'left'
					},{
					    field: 'OS_NAME',
						title: '操作系统',
						align:'left'
					},{
					    field: 'OS_NAME',
						title: '操作系统',
						align:'left'
					},{
					    field: 'REMARK',
						title: '备注',
						align:'left'
					},{
						title: '操作',
						align:'left',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/ops/host-edit.jsp',title:'编辑',data:{hostId:data.HOST_ID,serverRoomId:data.SERVER_ROOM_ID}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/ops/host-edit.jsp',title:'新增主机',data:{serverRoomId:$("#serverRoomId").val()}});
			},
			detail:function(data){
				popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/ops/host-detail.jsp',title:'主机详情',data:{hostId:data.HOST_ID}});
			},
			monitor:function(data){
				layer.msg("待实现");
			},
			del:function(data){
				layer.confirm("确认要删除吗?",{icon:3},function(){
					ajax.remoteCall("${ctxPath}/servlet/host?action=delete",{'host.HOST_ID':data.HOST_ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
					
				});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>