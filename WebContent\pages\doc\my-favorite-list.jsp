<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的收藏文档</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="fa fa-tags"></span> 我的收藏文档</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">文档名称</span>	
							 <input type="text" name="title" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/html" id="bar">
  			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.delFavorite">取消收藏</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'DocDao.favoriteList',
					height:'full-90',
					limit:20,
					cols: [[
					 {type:'numbers'},
		             {
					    field: 'TITLE',
						title: '标题',
						align:'left',
						event:'list.detail',
						style:'color:#1E9FFF;cursor:pointer'
					},{
					    field: 'FAVORITE_TIME',
						title: '收藏时间',
						align:'left'
					},{
						title: '操作',
						align:'left',
						toolbar: '#bar'
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/doc/doc-detail.jsp',title:'详情',data:{op:'detail',docId:data.DOC_ID,folderId:data.FOLDER_ID}});
			},
			delFavorite:function(data) {
				ajax.remoteCall("${ctxPath}/servlet/doc?action=delFavorite",{favoriteId:data.FAVORITE_ID,docId:data.DOC_ID},function(result) { 
					if(result.state == 1){
						layer.msg("取消成功！",{icon:1,time:1200});
						list.query();
					}else{
						layer.msg(result.msg,{icon: 7,time:1200});
					}
				});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>