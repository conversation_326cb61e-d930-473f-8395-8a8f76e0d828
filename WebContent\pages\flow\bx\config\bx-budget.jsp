<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>报销费用设置</title>
	<style>
		.select2-container .select2-selection--single{z-index: 9999!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form id = "searchForm"  name = "searchForm" onsubmit="return false;" class="form-inline" autocomplete="off">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>费用管控</h5>
	          		     <div class="input-group input-group-sm" style="width: 140px;">
							 <span class="input-group-addon">姓名</span>	
							 <input name="budgetType" id="budgetType" value="${param.budgetType}" type="hidden"/>
							 <input type="text" name="userName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 140px;">
							 <span class="input-group-addon">部门</span>	
							 <input type="text" name="deptName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 140px;">
							 <span class="input-group-addon">年份</span>	
							 <input name="yearId" class="form-control input-sm" data-mars="CommonDao.nowYear" onblur="queryData();"/>
						 </div>
						 <button type="button" class="btn btn-sm btn-info pull-right" id="upload-btn"><span class="glyphicon glyphicon-import"></span> 导入</button>
						 <button type="button" class="btn btn-sm btn-default pull-right mr-10" onclick="exportData();"><span class="glyphicon glyphicon-export"></span> 导出</button>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" id="search" onclick="queryData();"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
                  	 </div>
                 </div>
				<div class="ibox-content table-responsive">
				    <table class="table table-auto table-bordered table-hover table-condensed text-c text-nowrap" id="tableHead"  data-mars="BxDao.budgetList" data-mars-reload="false" data-template="list-template" data-container="dataList">
                           <thead>
                        	  <tr>
						          <th rowspan="2" style="width:90px">操作</th>
                        	  	  <th rowspan="2">部门</th>
                        	  	  <th rowspan="2">人员</th>
                        	      <th rowspan="2">年度</th>
                        	      <th rowspan="2">费用类型</th>
						          <th class="text-c" colspan="3">第一季度</th>
						          <th class="text-c" colspan="3">第二季度</th>
						          <th class="text-c" colspan="3">第三季度</th>
						          <th class="text-c" colspan="3">第四季度</th>
   						      </tr>
                        	  <tr>
						          <th>1月</th>
						          <th>2月</th>
						          <th>3月</th>
						          <th>4月</th>
						          <th>5月</th>
						          <th>6月</th>
						          <th>7月</th>
						          <th>8月</th>
						          <th>9月</th>
						          <th>10月</th>
						          <th>11月</th>
						          <th>12月</th>
   						      </tr>
                           </thead>
                           <tbody id="dataList">
                           </tbody>
                           <tbody>
                           		<c:forEach var="item" begin="1" end="2">
                           			<tr id="item_${item}">
											<td  style="width: 70px;">
												 <a href="javascript:void(0)" onclick="Config.saveData('${item}')" id="btn_${item}"> 提交</a>
											</td> 
											<td style="width: 90px;">
												<input type="hidden" name="DEPT_ID_${item}"/>
												<input class="form-control input-sm userId" readonly style="width:90px;" onclick="singleDept(this)" name="DEPT_NAME_${item}">
											</td>
											<td style="width: 90px;">
												<input type="hidden" name="USER_ID_${item}"/>
												<input class="form-control input-sm userId" readonly style="width:70px;" onclick="singleUser(this)" name="USER_NAME_${item}">
											</td>
											<td style="width: 70px;">
												<input type="number" style="width:70px;" class="form-control input-sm" name="YEAR_ID_${item}"/>
											</td> 
											<td style="width: 130px;">
												<select class="form-control input-sm feeType" name="FEE_TYPE_${item}"></select>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_1_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_2_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_3_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_4_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_5_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_6_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_7_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_8_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_9_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_10_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_11_${item}"/>
											</td> 
											<td>
												<input type="number" value="0" onblur="onlyNumber(this)" style="width:80px;" class="form-control input-sm" name="MONTH_12_${item}"/>
											</td> 
									    </tr>
                           		</c:forEach>
                           		
                           </tbody>
                    </table>
                     <div class="row paginate" id="page">
                     	<jsp:include page="/pages/common/pagination.jsp"/>
                	 </div>
                    <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr id="tr_{{:BUDGET_ID}}">
											<td style="width:70px;display:inline-block;">
												 <a href="javascript:void(0)" onclick="Config.saveData('{{:BUDGET_ID}}')" >修改</a>
												 <a href="javascript:void(0)" onclick="Config.delData('{{:BUDGET_ID}}')" >删除</a>
											</td> 
											<td style="width: 90px;">
												<input type="hidden" name="DEPT_ID_{{:BUDGET_ID}}" value="{{:DEPT_ID}}"/>
												<input class="form-control input-sm userId" readonly value="{{:DEPT_NAME}}" style="width:90px;" onclick="singleDept(this)" name="DEPT_NAME_{{:BUDGET_ID}}">
											</td>
											<td style="width: 90px;">
												<input type="hidden" name="USER_ID_{{:BUDGET_ID}}" value="{{:USER_ID}}"/>
												<input class="form-control input-sm userId" readonly style="width:70px;" onclick="singleUser(this)" value="{{:USER_NAME}}" name="USER_NAME_{{:BUDGET_ID}}">
											</td>
											<td  style="width: 70px;">
												<input type="number" value="{{:YEAR_ID}}" style="width:70px;" class="form-control input-sm" name="YEAR_ID_{{:BUDGET_ID}}"/>
											</td> 
											<td style="width: 130px;">
												<select class="form-control input-sm feeType" data-value="{{:FEE_TYPE}}" name="FEE_TYPE_{{:BUDGET_ID}}"></select>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_1}}" class="form-control input-sm" name="MONTH_1_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_2}}" class="form-control input-sm" name="MONTH_2_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_3}}" class="form-control input-sm" name="MONTH_3_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_4}}" class="form-control input-sm" name="MONTH_4_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_5}}" class="form-control input-sm" name="MONTH_5_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_6}}" class="form-control input-sm" name="MONTH_6_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_7}}" class="form-control input-sm" name="MONTH_7_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_8}}" class="form-control input-sm" name="MONTH_8_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_9}}" class="form-control input-sm" name="MONTH_9_{{:BUDGET_ID}}"/>
											</td> 											
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_10}}" class="form-control input-sm" name="MONTH_10_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_11}}" class="form-control input-sm" name="MONTH_11_{{:BUDGET_ID}}"/>
											</td> 
											<td>
												<input type="number" style="width:80px;" onblur="onlyNumber(this)" value="{{:MONTH_12}}" class="form-control input-sm" name="MONTH_12_{{:BUDGET_ID}}"/>
											</td> 											
									    </tr>
								    {{/for}}					         
				    </script>
				</div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		var budgetType = '${param.budgetType}';
		
		function getFeeTypeOptions(val){
			var str = "差旅费,业务招待费,手机费,交通费";
			if(budgetType=='1'){
				str = '项目费用';
			}
			$('.feeType').html("<option value=''>----</option>");
			var tempHtml = [];
			var data = str.split(',');
			for(var index in data){ 
				var val = data[index];
				tempHtml.push("<option value='"+val+"'>"+val+"</option>");
			}
			$('.feeType').append(tempHtml.join(''));
			$('.feeType').each(function(){
				var t = $(this);
				var val = t.data('value');
				t.val(val);
			});
		}
	
		$(function(){
			$("#searchForm").render({success:function(){
				$("#searchForm").render({success:function(){
					 getFeeTypeOptions();
				}});
			}});
		});
		
		jQuery.namespace("Config");
		
		
		function exportData(){
			window.open('${ctxPath}/servlet/bx/conf?action=exportBxBudget&data='+encodeURI(JSON.stringify(form.getJSONObject('.form-group'))));
		}
		
		Config.saveData = function(budgetId){
			var data = form.getJSONObject("#searchForm");
			data['budgetId'] = budgetId;
			data['budgetType'] = $('#budgetType').val();
			ajax.remoteCall("/yq-work/servlet/bx/conf?action=saveBudget",data,function(result) {
				if(result.state == 1){
				    $('#btn_'+budgetId).remove();
				    layer.msg(result.msg,{offset:'rb'});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Config.delData = function(budgetId){
			var data = form.getJSONObject("#searchForm");
			data['budgetId'] = budgetId;
			data['budgetType'] = $('#budgetType').val();
			ajax.remoteCall("/yq-work/servlet/bx/conf?action=delBudget",data,function(result) {
				if(result.state == 1){
				    $('#tr_'+budgetId).remove();
				    layer.msg(result.msg,{offset:'rb'});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function queryData(){
			var yearId = $("[name='yearId']").val();
			if(yearId==''){
				layer.msg('年份不能为空');
				return;
			}
			$("#searchForm").searchData({success:function(){
				getFeeTypeOptions();
			}});
		}
		
		function onlyNumber(obj){
			obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"和"."以外的字符
			obj.value = obj.value.replace(/^\./g,"");//验证第一个字符是数字而不是字符
			obj.value = obj.value.replace(/\.{2,}/g,".");//只保留第一个.清除多余的
			obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
			obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
	     }
		
		 layui.use('upload', function(){
		 	  var upload = layui.upload;
			  var uploadInst = upload.render({
			    elem: '#upload-btn'
			    ,url: '${ctxPath}/servlet/bx/conf?action=uploadBxBudgetFile'
			    ,number: 1
			    ,accept: 'file'
			    ,exts:'xls|xlsx'
			    ,size:1024*50
			    ,field: 'bxData'//设定文件域的字段名
			    ,data: {budgetType:budgetType}
			    ,done: function(res, index, upload){
				    if(res&&res.state==1){
				    	layer.msg(res.msg,{icon: 1,time:800},function(){
				    		location.reload();
						}); 
					}else{
						layer.alert(res.msg,{icon: 5});
					}
			    },before:function(){
			    	
			    }
			    ,error: function(res, index, upload){
			    	layer.alert("上传文件请求异常！",{icon: 5});
			    }
	      });
 	  });
		 
	
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>