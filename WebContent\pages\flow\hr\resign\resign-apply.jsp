<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 140px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的离职申请单"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 140px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">岗位</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.post}"  class="form-control input-sm" name="apply.data1"/>
					  			</td>
					  			<td class="required">离职类型</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data2">
						  				<option value="">--</option>
						  				<option value="辞职">辞职</option>
						  				<option value="合同/协议到期 ">合同/协议到期 </option>
						  				<option value="辞退">辞退</option>
						  				<option value="协商解除 ">协商解除 </option>
						  			</select>
					  			</td>
					  		</tr>
				  			<tr>
					  			<td class="required">员工类型</td>
					  			<td>
					  				<select class="form-control input-sm" data-rules="required" name="apply.data7">
					  					<option value="">请选择</option>
					  					<option value="正式员工">正式员工</option>
					  					<option value="试用期员工">试用期员工</option>
					  					<option value="实习生">实习生</option>
					  				</select>
					  			</td>
					  			<td class="required">工作交接人</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" onclick="multiUser(this)" class="form-control input-sm" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">入职日期</td>
					  			<td style="width: 35%;">
					  				<input type="text" data-rules="required" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data3"/>
					  			</td>
					  			<td class="required">预计离职时间</td>
					  			<td style="width: 35%;">
					  				<input type="text" data-rules="required" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">持有公司资产说明</td>
					  			<td colspan="3">
									<input data-rules="required" placeholder="公司资产领用情况说明：持有电脑xx一台等" class="form-control input-sm" name="apply.data9">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">私人邮箱</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data10"/>
					  			</td>
					  			<td>涉及知识产权内容</td>
					  			<td>
					  				<input type="text" placeholder="岗位涉及的知识产权内容（如软著、专利等）" class="form-control input-sm" name="apply.data11"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">离职原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>离职文件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td colspan="4" style="text-align: left;padding: 15px 40px;">
					  			 <div class="hidden-print">
					  			      亲爱的同事:<br>
                                                                                       很遗憾我们将无法继续并肩奋斗，祝你在未来的岁月里工作顺利，健康快乐！<br>
				      	                          请您在<font color="red">离职前1个月（实习生、试用期提前3天）</font>告知您的离职计划，并在离职前与部门主管、公司人事主管充分沟通，再发起本流程。本流程会签完毕后，请您至人事行政专员处办理纸质签字手续。<br><br>
					  		                 温馨提示：<br>
									以下事项须于最后出勤日前处理完毕：<br>
									1. 线上OA 离职流程：请在最后出勤日前确保OA离职流程除办公账号权限确认外，其他环节已审批完毕。<br>
									2. 报销、欠账、借款处理：如果您有未处理的报销、欠账、借款，请务必在最后出勤日前在OA系统发起相关流程，并将纸质报销/欠账/借款材料按照公司的费用报销制度要求交予财务部同事，否则将无法支付。<br>
									3. 固定资产处理：如有领用公司固定资产，请退还给公司人力行政部或移交给其他同事，请于最后出勤日前联系固定资产管理员办理相应的资产调拨手续。<br>
									4.考勤处理：请于最后出勤日前联系人力行政部查询您本月考勤情况，如您有漏打卡、出差请假未填OA申请单的情况，请在最后出勤日前在线提交相关申请并确保离职前已审批完成。否则将影响薪资核算。<br><br>
					  			</div>
				  				<div class="visible-print">离职承诺：<br>
							     &nbsp;&nbsp;&nbsp;本人同意移交以上事项内所有内容，有关离职手续已按规定办妥，已将公司重要资料交还，并不外泄在职期间所了解的公司相关商业、技术等秘密。
						       	 确认从即日起与公司终止劳动关系，所从事的一切活动与公司无关。
								<br>&nbsp;&nbsp;&nbsp;本人已和公司签有保密协议，对公司所有知识产权内容承担保密义务，且保密期限为无限，直至公司宣布解密或者知识产权信息已公开为止。
								如有侵权行为，公司将依据保密或竞业禁止上的条约，依法追究本人相应责任。
								</div>
				  				<br>
				  				<div class="visible-print">
					  				（请申请人在下方亲笔抄写上述文字）<br><br><br><br><br><br>
				  				</div>
				  				<div class="hidden-print" style="padding-left: 60%;">
					  				签字：<input type="text" style="display: inline-block;width: 100px;margin-right: 20px;" data-rules="required" name="apply.data5" class="form-control input-sm">
					  				<span class="ml-20">申请日期：</span><input type="text" style="display: inline-block;width: 100px" data-rules="required" data-laydate="{type:'date'}" name="apply.data6" class="form-control input-sm Wdate">
				  				</div>
					  			</td>
					  		</tr>
					  		<tr class="visible-print">
					  			<td>申请人签字确认</td>
					  			<td></td>
					  			<td>签字日期</td>
					  			<td></td>
					  		</tr>
					  		<tr class="visible-print">
					  			<td>人力行政部归档</td>
					  			<td></td>
					  			<td>归档日期</td>
					  			<td></td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){
				Flow.initData();
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			var xferPers = $("[name='apply.data8']").val();
			if(xferPers==''){
				layer.msg('交接人不能为空',{icon:7,time:800});
				return;
			}
			var staffInfo = getStaffInfo();
			var userName = staffInfo.userName;
			if(xferPers.indexOf(userName)>-1){
				layer.msg('交接人不能选自己',{icon:7,time:800});
				return;
			}
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.initData = function(){
			var params = {};
			var type = FlowCore.getApplyValue('data7');
			var nodeCode = FlowCore.getCurrentNodeCode();
			if(nodeCode=='中心副总'&&type=='实习生'){
				params['nextNodeCode'] = '0';
			}
			FlowCore.approveData = params;
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>