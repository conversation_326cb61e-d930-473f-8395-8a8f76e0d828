<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>流程配置</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>流程设置</h5>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">分类</span>	
							<select name="pFlowCode" onchange="queryData();" data-mars="FlowDao.categoryDict" class="form-control input-sm">
							      <option value="">-- </option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm ml-15">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="addFlow()"> 新增</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
		</form>
		
		<script type="text/html" id="bar">
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="editFlow">编辑</a>
  			<a class="layui-btn layui-btn-warn layui-btn-xs" lay-event="nodeFlow">流转节点</a>
		</script>
		
		
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
	$(function(){
		$("#dataMgrForm").render({success:function(){
			initList();
		}});
	});
	
	function initList(){
		$("#dataMgrForm").initTable({
			mars:'FlowDao.categoryList',
			id:'flowTable',
			page:false,
			height:'full-130',
			cellMinWidth:100,
			cols: [[
			 {type:'numbers'},
			 {
				 field:'FLOW_NAME',
				 title:'流程名称'
			 },{
				 field:'FLOW_CODE',
				 title:'流程代码'
			 },{
				 field:'P_FLOW_CODE',
				 title:'类别',
				 templet:function(row){
					 var pFlowCode  = row['P_FLOW_CODE'];
					 if(pFlowCode=='0'){
						 return '';
					 }else{
						 return getText(pFlowCode,'pFlowCode');
					 }
				 }
			 },{
				 field:'FLOW_DESC',
				 title:'流程描述',
				 minWidth:160
			 },{
				field:'FLOW_STATE',
				title:'状态',
				templet:function(row){
					var state = row['FLOW_STATE'];
					return state=='0'?'启用':'暂停';
				}
			},{
				field:'',
				title:'操作',
				toolbar: '#bar'
			}
		]],done:function(){
			
	  	}
	 });
   }
			
	function editFlow(data){
		var flowCode = data['FLOW_CODE'];
		popup.layerShow({id:'editNode',area:['550px','100%'],offset:'rb',url:'${ctxPath}/pages/flow/config/category-edit.jsp',title:'修改',data:{flowCode:flowCode}});
	}
	
	function nodeFlow(data){
		var flowCode = data['FLOW_CODE'];
		popup.openTab({id:'nodeFlow',url:'${ctxPath}/pages/flow/config/check-node.jsp',title:'流转设置',data:{flowCode:flowCode}});
	}
	
	function addFlow(){
		popup.layerShow({id:'editNode',area:['550px','100%'],offset:'rb',url:'${ctxPath}/pages/flow/config/category-edit.jsp',title:'新增',data:{}});
	}
	
	function queryData(){
		$("#dataMgrForm").queryData({id:'flowTable',page:false});
	}
			
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>

