package com.yunqu.work.dao.other;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.SqlPara;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.utils.WeekUtils;

@WebObject(name="CommonDao")
public class CommonDao extends AppDaoContext {
	
	@WebControl(name="yearBeginToNow",type=Types.TEXT)
	public JSONObject yearBeginToNow(){
		Calendar calendar = Calendar.getInstance();
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM")+"-01 到 "+EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	
	@WebControl(name="monthRange",type=Types.TEXT)
	public JSONObject monthRange(){
		Calendar calendar=Calendar.getInstance();
		calendar.add(Calendar.MONTH, -1);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM-dd")+" 到 "+EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	
	@WebControl(name="monthBegin",type=Types.TEXT)
	public JSONObject monthBegin(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM")+"-01");
	}

	@WebControl(name="yearBegin",type=Types.TEXT)
	public JSONObject yearBegin(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy")+"-01-01");
	}

	@WebControl(name="threeMonthRange",type=Types.TEXT)
	public JSONObject threeMonthRange(){
		Calendar calendar=Calendar.getInstance();
		calendar.add(Calendar.MONTH, -3);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM-dd")+" 到 "+EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	
	@WebControl(name="prevWeekly",type=Types.TEXT)
	public JSONObject prevWeekly(){
		int weekNo = WeekUtils.getWeekNo() -1;
		if(weekNo<=0) {
			weekNo =  1;
		}
		int year =  EasyCalendar.newInstance().getYear();
		String a = WeekUtils.getStartDayOfWeekNo(year,weekNo);
		String b = WeekUtils.getEndDayOfWeekNo(year,weekNo);
		return getJsonResult(a+" 到 "+b);
	}
	
	@WebControl(name="nowWeekly",type=Types.TEXT)
	public JSONObject nowWeekly(){
		int weekNo = WeekUtils.getWeekNo();
		int year =  EasyCalendar.newInstance().getYear();
		String a = WeekUtils.getStartDayOfWeekNo(year,weekNo);
		String b = WeekUtils.getEndDayOfWeekNo(year,weekNo);
		return getJsonResult(a+" 到 "+b);
	}
	
	@WebControl(name="currentMonthRange",type=Types.TEXT)
	public JSONObject currentMonthRange(){
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Calendar ca = Calendar.getInstance();   
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH)); 
        String last = format.format(ca.getTime());
	        
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM")+"-01"+" 到 "+last);
	}
	
	@WebControl(name="todayBegin",type=Types.TEXT)
	public JSONObject todayBegin(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd")+" 00:00");
	}
	
	@WebControl(name="nowYear",type=Types.TEXT)
	public JSONObject nowYear(){
		return getJsonResult(EasyCalendar.newInstance().getYear());
	}
	
	@WebControl(name="todayEnd",type=Types.TEXT)
	public JSONObject todayEnd(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd") + " 23:59");
	}
	
	@WebControl(name="date01",type=Types.TEXT)
	public JSONObject date01(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd")+" 00:00");
	}
	@WebControl(name="date02",type=Types.TEXT)
	public JSONObject date02(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm"));
	}
	@WebControl(name="date03",type=Types.TEXT)
	public JSONObject data03(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyyMMdd"));
	}
	
	@WebControl(name="prevMonthId",type=Types.TEXT)
	public JSONObject prevMonthId(){
		Calendar calendar=Calendar.getInstance();
		calendar.add(Calendar.MONTH, -1);
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM"));
	}
	
	@WebControl(name="prevMonthDay",type=Types.TEXT)
	public JSONObject prevMonthDay(){
		Calendar calendar=Calendar.getInstance();
		calendar.add(Calendar.MONTH, -1);
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM")+"-01");
	}
	
	@WebControl(name="threeMonthBefore",type=Types.TEXT)
	public JSONObject threeMonthBefore(){
		Calendar calendar=Calendar.getInstance();
		calendar.add(Calendar.MONTH, -3);
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM")+"-01");
	}
	
	@WebControl(name="nowMonthId",type=Types.TEXT)
	public JSONObject nowMonthId(){
		Calendar calendar=Calendar.getInstance();
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM"));
	}
	
	@WebControl(name="yearMonthBegin",type=Types.TEXT)
	public JSONObject yearMonthBegin(){
		Calendar calendar=Calendar.getInstance();
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy"+"-01"));
	}

	@WebControl(name="yearMonthEnd",type=Types.TEXT)
	public JSONObject yearMonthEnd(){
		Calendar calendar=Calendar.getInstance();
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy"+"-12"));
	}

	@WebControl(name="yearDateEnd",type=Types.TEXT)
	public JSONObject yearDateEnd(){
		Calendar calendar=Calendar.getInstance();
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy"+"-12-31"));
	}
	
	@WebControl(name="randomId",type=Types.TEXT)
	public JSONObject randomId(){
		return getJsonResult(FlowService.getService().getID());
	}
	
	@WebControl(name="userName",type=Types.TEXT)
	public JSONObject userName(){
		return getJsonResult(getUserPrincipal().getUserName());
	}
	@WebControl(name="userDict",type=Types.DICT)
	public JSONObject userDict(){
//		String projectId=param.getString("projectId");
//		if(StringUtils.notBlank(projectId)){
//			return	getDictByQuery("select t2.USER_ID,t2.USERNAME from yq_project_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t1.USER_ID=t2.USER_ID where t1.PROJECT_ID = ? and t2.STATE = 0",projectId);
//		}else{
		JSONObject	result=getDictByQuery("select USER_ID,USERNAME from "+Constants.DS_MAIN_NAME+".easi_user where STATE = 0");
		return result;
//		}
		
	}
	
	@WebControl(name="selectUserInfo",type=Types.OTHER)
	public JSONObject selectUserInfo(){
		this.setQuery(getMainQuery());
		String info = param.getString("info");
		EasySQL sql = new EasySQL();
		sql.append("select t2.USER_ID,t2.USERNAME from yq_staff_info t1 INNER JOIN easi_user t2 on t1.staff_user_id = t2.USER_ID where 1=1 and t2.state = 0");
		if(StringUtils.notBlank(info)) {
			sql.appendLike(info, "and (t2.USERNAME like ?");
			sql.appendLike(info,"or t2.depts like ?");
			sql.appendLike(info,"or t1.py_name like ?");
			sql.append(")");
		}
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="userList",type=Types.LIST)
	public JSONObject userList(){
		EasyQuery query=ServerContext.getAdminQuery();
		this.setQuery(query);
		JSONObject jsonObject=queryForList("select USER_ID,USERNAME,PIC_URL from easi_user ",null);//where STATE = 0
		jsonObject.put("isSuperUser", isSuperUser());
		return jsonObject;
	}
	
	@WebControl(name="userInfo",type=Types.OTHER)
	public JSONObject userInfo(){
		String id=param.getString("userId");
		this.setQuery(getMainQuery());
		JSONObject jsonObject=queryForRecord("select t2.USER_ID,t2.PIC_URL HEAD_IMG,t2.USERNAME USER_NAME,t2.NIKE_NAME,t2.DEPTS DEPT_NAME,t1.job_title POST,t2.EMAIL,t2.MOBILE,t3.LAST_LOGIN_TIME from yq_staff_info t1,easi_user t2,easi_user_login t3 where t1.staff_user_id = t2.USER_ID and t3.USER_ID = t2.USER_ID and t2.user_id = ? ",id);
		return jsonObject;
	}
	
	@WebControl(name="deptInfo",type=Types.OTHER)
	public JSONObject deptInfo(){
		String deptId = param.getString("deptId");
		this.setQuery(getMainQuery());
		JSONObject result = queryForRecord("select t1.* from EASI_DEPT t1 where t1.DEPT_ID = ? ",deptId);
		JSONObject deptInfo = result.getJSONObject("data");
		String leader = deptInfo.getString("LEADER");
		if(StringUtils.notBlank(leader)) {
			deptInfo.put("LEADER_NAME", StaffService.getService().getUserName(leader));
		}
		String secondLeader = deptInfo.getString("SECOND_LEADER");
		if(StringUtils.notBlank(secondLeader)) {
			deptInfo.put("SECOND_LEADER_NAME", StaffService.getService().getUserName(secondLeader));
		}
		return result;
	}
	
	@WebControl(name="myUserInfo",type=Types.OTHER)
	public JSONObject myUserInfo(){
		StaffModel staffInfo = getStaffInfo();
		staffInfo.setResIds(getUserPrincipal().getResources());
		staffInfo.setRoleIds(getUserPrincipal().getRoles());
		return getJsonResult(staffInfo);
	}
	
	@WebControl(name="deptDict",type=Types.DICT)
	public JSONObject deptDict(){
		return getDictByQuery("select DEPT_ID,DEPT_NAME from "+Constants.DS_MAIN_NAME+".easi_dept");
	}
	
	@WebControl(name="appointedDay",type=Types.TEXT)
	public JSONObject appointedDay(){
		Object _day = getMethodParam(0);
		if(_day!=null) {
			String day = _day.toString();
			if(StringUtils.isNotBlank(day)) {
				return returnDay(Integer.valueOf(day));
			}
		}
		return returnDay(-3);
	}
	
	private JSONObject returnDay(int day) {
		Calendar c = Calendar.getInstance();
		c.setTime(new Date());
		c.add(Calendar.DATE,day);
		String text = EasyDate.dateToString(c.getTime(), "yyyy-MM-dd");
		return getJsonResult(text);
	}
	
	
	/**
	 * 获取今天时间
	 * @return
	 */
	@WebControl(name="today",type=Types.TEXT)
	public JSONObject today(){
		return getText(EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	
	/**
	 * 获取三天后
	 * @return
	 */
	@WebControl(name="date04", type=Types.TEXT)
	public JSONObject date04() {
		Date date = EasyDate.getCurrentDate();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, 3);
		date = calendar.getTime();
		return getText(EasyDate.dateToString(date, "yyyy-MM-dd HH:mm:ss"));
	}
	/**
	 * 获取两周后
	 * @return
	 */
	@WebControl(name="twoWeeks", type=Types.TEXT)
	public JSONObject twoWeeks() {
		Date date = EasyDate.getCurrentDate();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.WEEK_OF_YEAR, -2);
		date = calendar.getTime();
		return getText(EasyDate.dateToString(date, "yyyy-MM-dd"));
	}
	@WebControl(name="commentsList",type=Types.TEMPLATE)
	public JSONObject fileList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_comments where fk_id = ? order by create_time desc";
			return queryForList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 查看日志
	 * @return
	 */
	@WebControl(name="lookLogList",type=Types.LIST)
	public JSONObject lookLogList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_look_log where fk_id = ? order by look_time desc";
			return queryForPageList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 操作日志
	 * @return
	 */
	@WebControl(name="lookOpList",type=Types.LIST)
	public JSONObject lookOpList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_oplog where fk_id = ? order by op_time desc";
			return queryForList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 下载日志
	 * @return
	 */
	@WebControl(name="downloadLog",type=Types.LIST)
	public JSONObject downloadLog(){
		String fkId=param.getString("fkId");
		String fileId=param.getString("fileId");
		if(StringUtils.notBlank(fileId)){
			String sql="select t2.file_name,t2.file_size,t1.download_time,t1.download_by,t1.ip,t1.user_agent,t1.download_by_name from yq_file_download_log t1,yq_files t2 where t1.file_id=t2.file_id and t1.file_id = ? ORDER BY t1.download_time desc";
			return queryForPageList(sql, new Object[]{fileId});
		}
		if(StringUtils.notBlank(fkId)){
			String sql="select t2.file_name,t2.file_size,t1.download_time,t1.download_by,t1.download_by_name,t1.ip,t1.user_agent from yq_file_download_log t1,yq_files t2 where t1.file_id=t2.file_id and t1.fk_id = ? ORDER BY t1.download_time desc";
			return queryForPageList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 消息
	 * @return
	 */
	@WebControl(name="myMessage",type=Types.LIST)
	public JSONObject myMessage(){
		String msgType=param.getString("msgType");
		if(StringUtils.notBlank(msgType)){
			String sql="select * from YQ_MESSAGE   where RECEIVER = ?  and MSG_TYPE = ? ORDER BY CREATE_TIME desc";
			return queryForPageList(sql, new Object[]{getUserPrincipal().getUserId(),msgType});
		}else{
			EasySQL sql=getEasySQL("select * from YQ_MESSAGE  where 1=1");
			if(!isSuperUser()) {
				sql.append(getUserId(),"and RECEIVER = ?");
			}
			String msgState = param.getString("msgState");
			sql.append(msgState,"and msg_state = ?");
			if("1".equals(msgState)) {
				sql.append("ORDER BY CREATE_TIME desc");
			}else if("2".equals(msgState)) {
				sql.append("ORDER BY READ_TIME desc");
			}else {
				sql.append("ORDER BY CREATE_TIME desc");
			}
			return queryForPageList(sql.getSQL(),sql.getParams());
		}
	}
	
	@WebControl(name = "dictVal",type = Types.OTHER)
	public JSONObject dictVal() {
		setQuery(getMainQuery());
		EasySQL sql =new EasySQL("select DICT_TYPE_ID,DICT_ID,DICT_NAME from easi_pc_dict where 1=1");
		sql.appendIn(param.getString("dicts").split(","), "and DICT_TYPE_ID ");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "findListBySqlkey",type = Types.TEMPLATE)
	public JSONObject findListBySqlkey() {
		String sqlKey = param.getString("sqlKey");
		if(StringUtils.isNotBlank(sqlKey)) {
			SqlPara sqlPara = Db.getSqlPara(sqlKey, param);
			return queryForList(sqlPara.getSql(), sqlPara.getPara());
		}
		return getJsonResult(new JSONArray());
	}
	
	@WebControl(name = "findRecordBySqlkey",type = Types.RECORD)
	public JSONObject findRecordBySqlkey() {
		String sqlKey = param.getString("sqlKey");
		if(StringUtils.isNotBlank(sqlKey)) {
			SqlPara sqlPara = Db.getSqlPara(sqlKey, param);
			return queryForRecord(sqlPara.getSql(), sqlPara.getPara());
		}
		return getJsonResult(new JSONArray());
	}
	
	@WebControl(name = "findPageBySqlkey",type = Types.TEMPLATE)
	public JSONObject findPageBySqlkey() {
		String sqlKey = param.getString("sqlKey");
		if(StringUtils.isNotBlank(sqlKey)) {
			SqlPara sqlPara = Db.getSqlPara(sqlKey, param);
			return queryForPageList(sqlPara.getSql(), sqlPara.getPara());
		}
		return emptyPage();
	}
}





