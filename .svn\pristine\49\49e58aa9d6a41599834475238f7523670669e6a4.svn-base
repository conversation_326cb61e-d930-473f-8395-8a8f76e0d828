<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>结算管理</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>

<EasyTag:override name="content">
    <div class="container-fluid">
        <form id="addSettleForm" class="form-horizontal" data-mars="CustOperateDao.settleRecord" data-mars-prefix="settle.">
            <table class="table table-edit table-vzebra">
                <tbody>
                <tr class="hidden">
                    <td colspan="2">
                        <input type="hidden" name="settle.SETTLEMENT_ID" value="${param.settlementId}"/>
                        <input type="hidden" name="settlementId" value="${param.settlementId}"/>
                    </td>
                </tr>
                <tr>
                    <td width="120px" class="required">结算月份</td>
                    <td >
                        <input type="text" name="settle.MONTH_ID" class="form-control input-sm Wdate" data-rules="required" 
                               onClick="WdatePicker({dateFmt:'yyyy-MM'})"/>
                    </td>
                    <td width="120px" class="required">结算类型</td>
                    <td >
                        <select name="settle.SETTLEMENT_TYPE" class="form-control input-sm">
                            <option value="SAAS">SAAS</option>
                            <option value="PAAS">PAAS</option>
                            <option value="其他">其他</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>客户</td>
                    <td colspan="3">
                        <input type="hidden" name="settle.CUST_ID" value="${param.custId}"/>
                        <input type="text" name="settle.CUST_NAME" value="${param.custName}" class="form-control input-sm" readonly onclick="singleCust(this)"/>
                    </td>
                </tr>
                <tr>
                    <td>合同名称</td>
                    <td colspan="3">
                        <input type="hidden" name="settle.CONTRACT_ID" value="${param.contractId}"/>
                        <input type="text" name="settle.CONTRACT_NAME" value="${param.contractName}" class="form-control input-sm" readonly onclick="singleContract(this)" placeholder="请点击选择合同"/>
                    </td>
                </tr>
                <tr>
                    <td>业务平台</td>
                    <td colspan="3">
                        <input type="hidden" name="settle.PLATFORM_ID" value="${param.platformId}"/>
                        <input type="text" name="settle.PLATFORM_NAME" value="${param.platformName}" class="form-control input-sm" readonly onclick="singleBusinessPlatForm(this)" placeholder="请点击选择平台"/>
                    </td>
                </tr>
                <tr>
                    <td>关联成本</td>
                    <td colspan="3">
                        <input type="hidden" name="settle.COST_IDS"/>
                        <input type="text" name="settle.COST_NAMES" class="form-control input-sm" readonly onclick="selectCosts(this)" placeholder="请点击选择关联成本"/>
                    </td>
                </tr>
                <tr>
                    <td>关联发票</td>
                    <td colspan="3">
                        <input type="hidden" name="settle.INVOICE_IDS"/>
                        <input type="text" name="settle.INVOICE_NOS" class="form-control input-sm" readonly onclick="selectInvoices(this)" placeholder="请点击选择关联发票"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">开票总额(含税)</td>
                    <td>
                        <input type="number" name="settle.TOTAL_KP_AMOUNT" class="form-control input-sm" value="0" data-rules="required"/>
                    </td>
                    <td class="required">开票总额(不含税)</td>
                    <td>
                        <input type="number" name="settle.TOTAL_KP_NO_TAX" class="form-control input-sm" value="0" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">成本总额(含税)</td>
                    <td>
                        <input type="number" name="settle.TOTAL_COST_AMOUNT" class="form-control input-sm" value="0" data-rules="required"/>
                    </td>
                    <td class="required">成本总额(不含税)</td>
                    <td>
                        <input type="number" name="settle.TOTAL_COST_NO_TAX" class="form-control input-sm" value="0" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">毛利(不含税)</td>
                    <td>
                        <input type="number" name="settle.GROSS_PROFIT" class="form-control input-sm" value="0" data-rules="required"/>
                    </td>
                    <td class="required">毛利率</td>
                    <td>
                        <input type="number" name="settle.GROSS_PROFIT_RATE" class="form-control input-sm" value="0" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td>回款状态</td>
                    <td>
                        <select name="settle.PAYMENT_STATUS" class="form-control input-sm">
                            <option value="0">未回款</option>
                            <option value="1">部分回款</option>
                            <option value="2">已回款</option>
                        </select>
                    </td>
                    <td class="required">已回款金额(含税)</td>
                    <td>
                        <input type="number" name="settle.PAY_AMOUNT" class="form-control input-sm" value="0" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td>最近回款时间</td>
                    <td>
                        <input type="text" name="settle.PAYMENT_TIME" class="form-control input-sm Wdate"
                               onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                    </td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td colspan="3">
                        <textarea name="settle.REMARK" class="form-control input-sm" rows="4"></textarea>
                    </td>
                </tr>
                </tbody>
            </table>
            <p class="layer-foot text-c">
                <button type="button" class="btn btn-primary btn-sm" style="width: 80px"
                        onclick="SettleEdit.ajaxSubmitForm()">保 存
                </button>
                <button type="button" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll()">关 闭
                </button>
            </p>
        </form>
    </div>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("SettleEdit");
        
        SettleEdit.settlementId = '${param.settlementId}';
        SettleEdit.isNew = '${param.isNew}';

        $(function(){
            $("#addSettleForm").render({
                success:function(result){
                    // 监听金额字段变化，自动计算毛利
                    $("[name='settle.TOTAL_KP_NO_TAX'],[name='settle.TOTAL_COST_NO_TAX']").on('change', function(){
                        SettleEdit.calculateProfit();
                    });
                }
            });
        });

        // 计算毛利和毛利率
        SettleEdit.calculateProfit = function() {
            var kpNoTaxAmount = parseFloat($("[name='settle.TOTAL_KP_NO_TAX']").val()) || 0;
            var costNoTaxAmount = parseFloat($("[name='settle.TOTAL_COST_NO_TAX']").val()) || 0;
            
            // 计算毛利 = 开票金额(不含税) - 成本(不含税)
            var profit = kpNoTaxAmount - costNoTaxAmount;
            $("[name='settle.GROSS_PROFIT']").val(profit.toFixed(2));
            
            // 计算毛利率 = 毛利/开票金额(不含税) * 100%
            var profitRate = kpNoTaxAmount > 0 ? (profit / kpNoTaxAmount * 100) : 0;
            $("[name='settle.GROSS_PROFIT_RATE']").val(profitRate.toFixed(2));
        }

        SettleEdit.ajaxSubmitForm = function(){
            if(form.validate("#addSettleForm")){
                if(this.settlementId || this.isNew === "0"){
                    this.updateData();
                }else{
                    this.insertData();
                }
            }
        }

        SettleEdit.insertData = function(){
            var data = form.getJSONObject("#addSettleForm");
            ajax.remoteCall("${ctxPath}/servlet/custOperate?action=addSettle",data,function(result){
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadSettleList();
                    });
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        SettleEdit.updateData = function(){
            var data = form.getJSONObject("#addSettleForm");
            ajax.remoteCall("${ctxPath}/servlet/custOperate?action=updateSettle",data,function(result){
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadSettleList();
                    });
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        function selectCosts(el){
            var id = new Date().getTime();
            $(el).attr('data-sid',id);
            popup.layerShow({
                id:'selectCosts',
                full:fullShow(),
                scrollbar:false,
                area:['700px','500px'],
                offset:'20px',
                title:'选择成本',
                url:'/yq-work/pages/crm/platform/include/select-cost.jsp',
                data:{
                    sid:id,
                    type:'checkbox',
                    platformId:$("[name='settle.PLATFORM_ID']").val()
                }
            });
        }

        function selectInvoices(el){
            var id = new Date().getTime();
            $(el).attr('data-sid',id);
            popup.layerShow({
                id:'selectInvoices',
                full:fullShow(),
                scrollbar:false,
                area:['700px','500px'],
                offset:'20px',
                title:'选择发票',
                url:'/yq-work/pages/crm/platform/include/select-invoice.jsp',
                data:{
                    sid:id,
                    type:'radio',
                    platformId:$("[name='settle.PLATFORM_ID']").val()
                }
            });
        }

        function selctCallBack(id,row){
            $("[name='settle.CONTRACT_NAME']").val(row['CONTRACT_NAME']);
            $("[name='settle.CONTRACT_ID']").val(row['CONTRACT_ID']);
        }

        function CostSelectCallBack(data) {
            $("[name='settle.COST_IDS']").val(data.costIds);
            $("[name='settle.COST_NAMES']").val(data.costNames);
            $("[name='settle.TOTAL_COST_AMOUNT']").val(data.totalAmount);
            $("[name='settle.TOTAL_COST_NO_TAX']").val(data.totalNoTaxAmount);
        }

        function InvoiceSelectCallBack(data) {
            $("[name='settle.INVOICE_IDS']").val(data.invoiceIds);
            $("[name='settle.INVOICE_NOS']").val(data.invoiceNos);
            $("[name='settle.TOTAL_KP_AMOUNT']").val(data.totalAmount);
            $("[name='settle.TOTAL_KP_NO_TAX']").val(data.totalNoTaxAmount);
        }

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
