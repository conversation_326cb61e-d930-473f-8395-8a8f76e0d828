package com.yunqu.work.dao.ehr;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;

@WebObject(name = "FaDao")
public class FaDao extends AppDaoContext {

	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from yq_fa_data where 1=1");
		sql.appendLike(param.getString("faName"),"and fa_name like ?");
		sql.appendLike(param.getString("faNo"),"and fa_no like ?");
		sql.appendLike(param.getString("cardNo"),"and card_no like ?");
		sql.appendLike(param.getString("useName"),"and use_name like ?");
		sql.append("order by fee_date desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="myData",type=Types.LIST)
	public JSONObject myData(){
		EasySQL sql=getEasySQL("select * from yq_fa_data where 1=1");
		if(!isSuperUser()) {
			sql.append(getUserId(),"and use_user_id = ?");
		}
		sql.appendLike(param.getString("faName"),"and fa_name like ?");
		sql.appendLike(param.getString("faNo"),"and fa_no like ?");
		sql.appendLike(param.getString("cardNo"),"and card_no like ?");
		sql.appendLike(param.getString("useName"),"and use_name like ?");
		sql.append("order by fee_date desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="checkData",type=Types.TEMPLATE)
	public JSONObject checkData(){
		String selectUserId = param.getString("selectUserId");
		if(StringUtils.isBlank(selectUserId)) {
			selectUserId = getUserId();
		}
		int periodNum = 0 ;
		try {
			periodNum = this.getQuery().queryForInt("select max(period_num) from yq_fa_check where fa_user_id = ?", selectUserId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			periodNum = 0;
		}
		if(periodNum==0) {
			return emptyPage();
		}
		
		EasySQL sql=getEasySQL("select t1.*,t2.check_id,t2.fa_use_state,t2.fa_use_people,t2.fa_check_remark,t2.fa_url,t2.fa_location,t2.file_id,t2.submit_time from yq_fa_data t1,yq_fa_check t2 where t1.fa_id = t2.fa_id");
		sql.append(periodNum,"and t2.period_num = ?");
		sql.append(selectUserId,"and t2.fa_user_id = ?");
		sql.append("order by t1.fee_date desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="checkDataStat",type=Types.TEMPLATE)
	public JSONObject checkDataStat(){
		int periodNum = 0 ;
		try {
			periodNum = this.getQuery().queryForInt("select max(period_num) from yq_fa_check");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			periodNum = 0;
		}
		if(periodNum==0) {
			return emptyPage();
		}
		
		EasySQL sql=getEasySQL("select t1.fa_user_id,t1.fa_user_name,count(1) count,t1.submit_time,t1.submit_name,t2.depts,t2.DATA1 from yq_fa_check t1,"+Constants.DS_MAIN_NAME+".easi_user t2 where t1.fa_user_id = t2.USER_ID");
		sql.append(periodNum,"and t1.period_num = ?");
		sql.append(param.getString("faUserId"),"and t1.fa_user_id = ?");
		sql.appendLike(param.getString("deptName"),"and t2.depts like ?");
		sql.appendLike(param.getString("faUserName"),"and t1.fa_user_name like ?");
		int flag = param.getIntValue("flag");
		if(flag==1) {
			sql.append("and t1.fa_use_state is null");
		}
		if(flag==2) {
			sql.append("and t1.fa_use_state is not null");
		}
		sql.append("GROUP BY t1.fa_user_id");
		sql.append("ORDER BY t1.submit_time");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="checkDataList",type=Types.TEMPLATE)
	public JSONObject checkDataList(){
		EasySQL sql=getEasySQL("select t1.*,t2.period_num,t2.check_id,t2.fa_use_state,t2.fa_use_people,t2.fa_check_remark,t2.fa_url,t2.fa_location,t2.file_id,t2.submit_time,t2.submit_name,t2.submit_ip from yq_fa_data t1,yq_fa_check t2 where t1.fa_id = t2.fa_id");
		sql.append(param.getString("periodNum"),"and t2.period_num = ?");
		sql.append(param.getString("faUseState"),"and t2.fa_use_state = ?");
		sql.append(param.getString("faUsePeople"),"and t2.fa_use_people = ?");
		sql.append(param.getString("faLocation"),"and t2.fa_location = ?");
		sql.appendLike(param.getString("faName"),"and t1.fa_name like ?");
		sql.appendLike(param.getString("faNo"),"and t1.fa_no like ?");
		sql.appendLike(param.getString("cardNo"),"and t1.card_no like ?");
		sql.appendLike(param.getString("useName"),"and t1.use_name like ?");
		sql.append("order by t2.submit_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="info",type=Types.RECORD)
	public JSONObject info(){
		EasySQL sql=getEasySQL("select * from yq_fa_data where 1=1");
		sql.append(param.getString("faId"),"and fa_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
}
