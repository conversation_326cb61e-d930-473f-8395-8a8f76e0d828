<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>config</title>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editConfForm" autocomplete="off" data-mars="FlowConfigDao.roleInfo" data-mars-prefix="conf.">
	     	    <input type="hidden" id="roleId" value="${param.roleId}" name="conf.ROLE_ID"/>
	     	    <input type="hidden" value="${param.roleId}" name="roleId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px;">角色名称</td>
		                    <td>
		                    	<input name="conf.ROLE_NAME" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">审批人</td>
		                    <td>
		                    	<input type="hidden" name="conf.ROLE_USER_ID"/>
		                    	<input type="text" data-rules="required" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="conf.ROLE_USER_NAME"/>
		                    </td>
		                </tr>
		                <tr>
		                    <td style="width: 80px;">状态</td>
		                    <td>
		                    	<select name="conf.ROLE_STATE" class="form-control input-sm" data-rules="required">
		                    		<option value="0">启用</option>
		                    		<option value="1">暂停</option>
		                    	</select>
		                    </td>
		                </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c" style="z-index: 999999999999;">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="RoleConf.ajaxSubmitForm()"> 确 定  </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
        
		var RoleConf={roleId:$("#roleId").val()};
        
		RoleConf.ajaxSubmitForm = function(){
			if(form.validate("#editConfForm")){
				if(RoleConf.roleId==''){
					RoleConf.updateData('add'); 
				}else{
					RoleConf.updateData('update'); 
				}
			};
		}
        RoleConf.updateData = function(flag) {
			var data = form.getJSONObject("#editConfForm");
			ajax.remoteCall("${ctxPath}/web/flow/config/"+flag+"Role",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose("#editConfForm");
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		$(function(){
			 requreLib.setplugs('select2',function(){
				$("#editConfForm").render({success:function(result){
					 $("#editConfForm select").select2({theme: "bootstrap",placeholder:'请选择'});
					 if(RoleConf.roleId){
						 
					 }
				}});
			 });
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>