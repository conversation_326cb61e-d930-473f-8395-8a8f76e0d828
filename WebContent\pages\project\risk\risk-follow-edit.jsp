<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>风险跟进信息</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="riskFollowEditForm" class="form-horizontal" autocomplete="off">
        <input type="hidden" value="${param.riskId}" name="riskId"/>
        <table class="layui-table">
            <tbody>
                <tr>
                    <td>
                        <textarea style="height: 100px;" placeholder="跟进内容" class="form-control input-sm" name="followContent"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="FollowEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
       
    	var FollowEdit = {};

        FollowEdit.riskId = '${param.riskId}';

        FollowEdit.ajaxSubmitForm = function(){
            if(form.validate("#riskFollowEditForm")){
                var data = form.getJSONObject("#riskFollowEditForm");
                ajax.remoteCall("${ctxPath}/servlet/projectRisk?action=addFollow",data,function(result) {
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            layer.closeAll();
                            reloadRisk();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            };
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>