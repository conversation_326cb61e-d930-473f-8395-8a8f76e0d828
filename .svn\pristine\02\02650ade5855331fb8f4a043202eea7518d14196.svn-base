 package com.yunqu.work.dao.cust;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;

@WebObject(name="CustDao")
public class CustDao extends AppDaoContext{

	@WebControl(name = "genCustId",type = Types.TEXT)
	public JSONObject genCustId() {
		String month = EasyCalendar.newInstance().getFullMonth();
		try {
			String result = this.getQuery().queryForString("select cust_no from yq_crm_customer where cust_no like 'YQ"+month+"%' ORDER BY cust_no desc");
			if(StringUtils.isBlank(result)) {
				return getJsonResult("YQ"+month+"0001");
			}else {
				result ="YQ"+(Integer.valueOf(result.substring(2))+1);
				return getJsonResult(result);
			}
		} catch (SQLException e) {
			this.error(null, e);
			return getJsonResult("YQ"+month+"0001");
		}
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String custId = param.getString("custId");
		return queryForRecord("select * from YQ_CRM_CUSTOMER where cust_id = ?",custId);
	}
	
	@WebControl(name="custList",type=Types.LIST)
	public JSONObject custList(){
		EasySQL sql=getEasySQL("select * from YQ_CRM_CUSTOMER where 1=1");
		sql.appendLike(param.getString("custName"),"and CUST_NAME like ?");
		sql.append(param.getString("custState"),"and CUST_STATE = ?");
		sql.append(param.getString("custSource"),"and CUST_SOURCE = ?");
		sql.append(param.getString("industry"),"and INDUSTRY = ?");
		sql.append(param.getString("custLevel"),"and CUST_LEVEL = ?");
		/*
		 * if(!isSuperUser()) { sql.append(getUserId(),"and creator = ?"); }
		 */
		sql.append("order by last_follow_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="custContactsList",type=Types.LIST)
	public JSONObject custContactsList(){
		EasySQL sql=getEasySQL("select * from YQ_CRM_CONTACTS where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="followList",type=Types.TEMPLATE)
	public JSONObject followList(){
		EasySQL sql=getEasySQL("select * from YQ_CRM_FOLLOW where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="userFollowList",type=Types.TEMPLATE)
	public JSONObject userFollowList(){
		EasySQL sql=getEasySQL("select t1.*,t2.cust_name,t3.business_name from YQ_CRM_FOLLOW t1 left join yq_crm_customer t2 on t1.cust_id = t2.cust_id ");
		sql.append("left join yq_crm_business t3 on t1.business_id = t3.business_id ");
		sql.append("where 1=1");
		sql.append(param.getString("beginDate"),"and t1.follow_begin >= ?");
		sql.append(param.getString("endDate"),"and t1.follow_end <= ?");
		sql.appendLike(param.getString("userName"),"and t1.create_user_name like ?");
		sql.appendLike(param.getString("customerName"),"and t2.cust_name like ?");
		sql.append(param.getString("contactsId"),"and t1.CONTACTS_ID = ?");
		String custId = param.getString("custId");
		String businessId = param.getString("businessId");
		
		String weekNo = param.getString("weekNo");
		if(StringUtils.isNotBlank(weekNo)) {
			sql.append(weekNo.substring(0,4),"and t1.year = ?");
			sql.append(weekNo.substring(4),"and t1.week_no = ?");
		}
		if(StringUtils.isNotBlank(custId)||StringUtils.isNotBlank(businessId)) {
			sql.append(businessId,"and t1.BUSINESS_ID = ?");
			sql.append(custId,"and t1.cust_id = ?");
		}else {
			if(!isSuperUser()) {
				if(getUserPrincipal().isRole("DEPT_MGR")) {
					sql.append(getDeptId(),"and t1.dept_id = ?");
				}else {
					sql.append(getUserId(),"and t1.create_user_id = ?");
				}
			}
		}
		sql.append("order by t1.create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="custFollowList",type=Types.LIST)
	public JSONObject custFollowList(){
		EasySQL sql=getEasySQL("select t1.*,t2.cust_name,t3.business_name from YQ_CRM_FOLLOW t1 left join yq_crm_customer t2 on t1.cust_id = t2.cust_id ");
		sql.append("left join yq_crm_business t3 on t1.business_id = t3.business_id ");
		sql.append("where 1=1");
		sql.append(param.getString("beginDate"),"and t1.follow_begin >= ?");
		sql.append(param.getString("endDate"),"and t1.follow_end <= ?");
		sql.appendLike(param.getString("userName"),"and t1.create_user_name like ?");
		sql.appendLike(param.getString("customerName"),"and t2.cust_name like ?");
		sql.append(param.getString("contactsId"),"and t1.CONTACTS_ID = ?");
		String custId = param.getString("custId");
		String businessId = param.getString("businessId");
		
		String weekNo = param.getString("weekNo");
		if(StringUtils.isNotBlank(weekNo)) {
			sql.append(weekNo.substring(0,4),"and t1.year = ?");
			sql.append(weekNo.substring(4),"and t1.week_no = ?");
		}
		Object obj = getMethodParam(0);
		if(obj!=null&&StringUtils.notBlank(obj.toString())) {
			sql.append(obj.toString(),"and t1.create_user_id = ?");
		}else {
			if(StringUtils.isNotBlank(custId)||StringUtils.isNotBlank(businessId)) {
				sql.append(param.getString("businessId"),"and t1.BUSINESS_ID = ?");
				sql.append(param.getString("custId"),"and t1.cust_id = ?");
			}else {
				if(!isSuperUser()) {
					if(getUserPrincipal().isRole("DEPT_MGR")) {
						sql.append(getDeptId(),"and t1.dept_id = ?");
					}else {
						sql.append(getUserId(),"and t1.create_user_id = ?");
					}
				}
			}
		}
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="followInfo",type=Types.RECORD)
	public JSONObject followInfo(){
		String followId = param.getString("followId");
		return queryForRecord("select * from YQ_CRM_FOLLOW where follow_id = ?",followId);
	}
	
	@WebControl(name="followAllList",type=Types.LIST)
	public JSONObject followAllList(){
		EasySQL sql=getEasySQL("select * from YQ_CRM_FOLLOW where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="teamList",type=Types.LIST)
	public JSONObject teamList(){
		EasySQL sql=getEasySQL("select t2.USERNAME,t1.* from yq_crm_cust_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t1.USER_ID=t2.USER_ID where 1=1");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="busniessInfo",type=Types.RECORD)
	public JSONObject busniessInfo(){
		String businessId = param.getString("businessId");
		return queryForRecord("select * from yq_crm_business where business_id = ?",businessId);
	}
	
	@WebControl(name="custBusniessDict",type=Types.DICT)
	public JSONObject custBusniessDict(){
		String custId = param.getString("custId");
		if(StringUtils.isBlank(custId)) {
			custId = param.getString("contract.CUST_ID");
		}
		return getDictByQuery("select business_id,business_name from yq_crm_business where cust_id = ?",custId);
	}
	
	@WebControl(name="businessPage",type=Types.LIST)
	public JSONObject businessPage(){
		EasySQL sql=getEasySQL("select t1.*,t2.CUST_NAME from YQ_CRM_BUSINESS t1 INNER JOIN YQ_CRM_CUSTOMER t2 on t1.cust_id=t2.cust_id");
		sql.append("where 1=1");
		sql.appendLike(param.getString("name"),"and t1.BUSINESS_NAME like ?");
		sql.append(param.getString("businessStage"),"and t1.BUSINESS_STAGE = ?");
		sql.append(param.getString("creator"),"and t1.creator = ?");
		sql.append(param.getString("sales"),"and t1.sales_by = ?");
		sql.append(param.getString("businessResult"),"and t1.BUSINESS_RESULT = ?");
		sql.appendLike(param.getString("custName"),"and t1.CUSTOMER_NAME like ?");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		if(!isSuperUser()) {
			if(getUserPrincipal().isRole("DEPT_MGR")) {
				sql.append(getDeptId(),"and t1.dept_id = ?");
			}else {
				sql.append(getUserId(),"and t1.creator = ?");
			}
		}
		sql.append("order by t1.update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="folowByWeek",type=Types.TEMPLATE)
	public JSONObject folowByWeek(){
		EasySQL sql=getEasySQL("select count(1) count,create_user_id,create_user_name from yq_crm_follow  where 1=1 ");
		if(!isSuperUser()) {
			if(getUserPrincipal().isRole("DEPT_MGR")) {
				sql.append(getDeptId(),"and dept_id = ?");
			}else {
				sql.append(getUserId(),"and create_user_id = ?");
			}
		}
		String weekNo = param.getString("weekNo");
		if(StringUtils.isBlank(weekNo)) {
			return getJsonResult(new JSONArray());
		}
		sql.append(weekNo.substring(0,4),"and year = ?");
		sql.append(weekNo.substring(4),"and week_no = ?");
		sql.append("GROUP BY create_user_id,create_user_name");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="folowCustByWeek",type=Types.TEMPLATE)
	public JSONObject folowCustByWeek(){
		EasySQL sql=getEasySQL("select count(1) count,t1.cust_id,t2.cust_name from yq_crm_follow t1,yq_crm_customer t2  where 1=1 ");
		sql.append("and t1.cust_id = t2.cust_id");
		if(!isSuperUser()) {
			if(getUserPrincipal().isRole("DEPT_MGR")) {
				sql.append(getDeptId(),"and t1.dept_id = ?");
			}else {
				sql.append(getUserId(),"and t1.create_user_id = ?");
			}
		}
		String weekNo = param.getString("weekNo");
		if(StringUtils.isBlank(weekNo)) {
			return getJsonResult(new JSONArray());
		}
		sql.append(weekNo.substring(0,4),"and t1.year = ?");
		sql.append(weekNo.substring(4),"and t1.week_no = ?");
		sql.append("GROUP BY t1.cust_id,t2.cust_name");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="folowSjByWeek",type=Types.TEMPLATE)
	public JSONObject folowSjByWeek(){
		EasySQL sql=getEasySQL("select count(1) count,t1.business_id,t2.business_name from yq_crm_follow t1,yq_crm_business t2  where 1=1 ");
		sql.append("and t1.business_id = t2.business_id");
		if(!isSuperUser()) {
			if(getUserPrincipal().isRole("DEPT_MGR")) {
				sql.append(getDeptId(),"and t1.dept_id = ?");
			}else {
				sql.append(getUserId(),"and t1.create_user_id = ?");
			}
		}
		String weekNo = param.getString("weekNo");
		if(StringUtils.isBlank(weekNo)) {
			return getJsonResult(new JSONArray());
		}
		sql.append(weekNo.substring(0,4),"and t1.year = ?");
		sql.append(weekNo.substring(4),"and t1.week_no = ?");
		sql.append("GROUP BY t1.business_id,t2.business_name");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="businessList",type=Types.LIST)
	public JSONObject businessList(){
		EasySQL sql=getEasySQL("select t1.*,t2.CUST_NAME from YQ_CRM_BUSINESS t1 INNER JOIN YQ_CRM_CUSTOMER t2 on t1.cust_id=t2.cust_id where 1=1");
		sql.appendLike(param.getString("name"),"and t1.BUSINESS_NAME like ?");
		sql.append(param.getString("businessStage"),"and t1.BUSINESS_STAGE = ?");
		sql.appendLike(param.getString("custName"),"and t1.CUSTOMER_NAME like ?");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		if(!isSuperUser()) {
			if(getUserPrincipal().isRole("DEPT_MGR")) {
				sql.append(getDeptId(),"and t1.dept_id = ?");
			}else {
				sql.append(getUserId(),"and t1.creator = ?");
			}
		}
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
}
