<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>菜单</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="FoodDao.foodObj" autocomplete="off" data-mars-prefix="food.">
			 <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
			  <ul class="layui-tab-title">
			    <li class="layui-this">补点排行榜</li>
			    <!-- <li>点餐排行榜</li>
			    <li>点餐效率榜</li> -->
			  </ul>
			  <div class="layui-tab-content">
			    <div class="layui-tab-item layui-show">
			   		 <table id="top"></table>
			    </div>
			    <div class="layui-tab-item">内容2</div>
			    <div class="layui-tab-item">内容3</div>
			  </div>
			</div> 
			
  		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		
		$(function(){
			layui.use('element', function(){
				var $ = layui.jquery;
				var element = layui.element;
			});
			$("#editForm").initTable({
				mars:'FoodDao.addBookTopList',
				page:false,
				skin:'line',
				id:'top',
				data:{dinnerType:3},
				cols: [[
				  {title:'序号',type:'numbers',width:50},
				  {
				    field: 'USER_ID',
					title: '用户',
					align:'center',
					templet:function(row){
						return getUserName(row.USER_ID);
					}
				  },{
				    field: 'COUNT',
					title: '次数',
					align:'center'
				  }
			 ]]}
			);
		});
		

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>