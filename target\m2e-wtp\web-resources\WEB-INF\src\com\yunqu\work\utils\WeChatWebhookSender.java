package com.yunqu.work.utils;

import java.sql.SQLException;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;

public class WeChatWebhookSender {
	
	/**
	 * markdown
	 * @param webhookKey
	 * @param message
	 * @return
	 */
	public static boolean sendHtmlMessage(String webhookKey, String message) {
		message = HtmlToMarkdownReplacer.convert(message);
		return sendMessage(webhookKey,"text",message);
	}
	
	public static boolean sendMarkdownMessage(String webhookKey, String message) {
		return sendMessage(webhookKey,"text",message);
	}
	
	public static boolean sendMarkdownMessage(String webhookKey,String title,String message) {
		return sendMarkdownMessage(webhookKey, title, message, false);
	}
	
	public static boolean sendMarkdownMessage(String webhookKey,String title,String message,boolean noticeAll) {
//		String content = "<font color=\"warning\">"+title+"</font>\n> "+message;
//		return sendMessage(webhookKey,"markdown",content);
		String text = title+"。\n"+message;
		return sendMessage(webhookKey,"text",text);
	}
	
	public static boolean sendTextMessage(String webhookKey, String message) {
		return sendMessage(webhookKey,"text",message);
	}
	
	public static boolean sendMessage(String webhookKey,String msgtype,String message) {
		return sendMessage(webhookKey, msgtype, message, false);
	}
	
	public static boolean sendMessage(String webhookKey,String msgtype,String message,boolean noticeAll) {
		MessageModel messageModel = new MessageModel();
		messageModel.setDesc(message);
		messageModel.setAll(noticeAll);
		return sendMessage(webhookKey, msgtype, messageModel);
	}
	
	public static boolean sendMessage(String webhookKey,String msgtype,MessageModel messageModel) {
		String message = messageModel.getDesc();
		message = message.replaceAll("\n", "\n\n");
		LogUtils.getWebhook().info("webhookKey>"+webhookKey+">msgtype " + msgtype+">"+message);
		long beginTs = System.currentTimeMillis();
		String msgSwitch = AppContext.getContext(Constants.APP_NAME).getProperty("msgSwitch","0");
		if(webhookKey.contains("@")) {
			if(webhookKey.startsWith("dept")) {
				webhookKey = seftDeptKey(webhookKey.substring(5));
			}
		}
		if(!"1".equals(msgSwitch)||!ServerContext.isLinux()) {
			webhookKey = WebhookKey.TEST;
		}
		if(StringUtils.isBlank(webhookKey)) {
			LogUtils.getWebhook().error("webhookKey is empty",null);
			return false;
		}
		try {
			JSONObject msg = new JSONObject();
			msg.put("msgtype", msgtype);
			if("news".equals(msgtype)) {
				
			}else {
				JSONObject contentJson = new JSONObject();
				contentJson.put("content", message);
				if(messageModel.isAll()) {
					JSONArray toUser = new JSONArray();
					toUser.add("@all");
					contentJson.put("mentioned_list", toUser);
				}
				msg.put(msgtype, contentJson);
			}
			String jsonMessage = msg.toJSONString();
			LogUtils.getWebhook().info(webhookKey+">WeChatWebhookSender Req jsonMessage: " + jsonMessage);
			Connection.Response response = Jsoup.connect("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key="+webhookKey)
					.method(Connection.Method.POST).timeout(5000).ignoreContentType(true)
					.header("Content-Type", "application/json")
					.requestBody(jsonMessage)
					.execute();
			int statusCode = response.statusCode();
			long endTs = System.currentTimeMillis();
			long ts = endTs - beginTs;
			LogUtils.getWebhook().info("WeChatWebhookSender Response: " + statusCode+">Time>"+ts+">body>"+response.body());
			return statusCode == 200;
		} catch (Exception e) {
			e.printStackTrace();
			LogUtils.getWebhook().error(e.getMessage(),e);
			return false;
		}
    }
	
	private static String seftDeptKey(String deptId) {
		if(StringUtils.isBlank(deptId)) {
			return null;
		}
		String webhook = null;
		try {
			webhook = getMainQuery().queryForString("select WEBHOOK from easi_dept where dept_id = ?", deptId);
		} catch (SQLException e) {
			LogUtils.getWebhook().error(e.getMessage(),e);
		}
		return webhook;
	}
	
	public EasyQuery getQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	}
	
	private static EasyQuery getMainQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.MARS_DS_NAME);
	}
	
	public static void main(String[] args) {
		sendHtmlMessage(WebhookKey.TEST, "<b>你踢球受过最重的伤是女友到球场给对手喂水！</b>");
	}
}
