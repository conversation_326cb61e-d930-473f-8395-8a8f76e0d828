package com.yunqu.work.utils;

import java.io.IOException;

import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONObject;

public class ExmailUtils {
	
	/**
	 * token有效时间2小时 
	 * @param appSecret
	 * @return
	 */
	public static JSONObject getToken(String appSecret){
		Connection connection = Jsoup.connect("https://api.exmail.qq.com/cgi-bin/gettoken");
		connection.data("corpid","wmd934e950133f8e55");
		connection.data("corpsecret",appSecret);
		try {
	 		Response response = connection.timeout(3000).ignoreContentType(true).method(Method.GET).execute();
	 		if(response.statusCode()==200){
	 			String strResult  = response.body();
	 			JSONObject result = JSONObject.parseObject(strResult);
	 			if(result.getIntValue("errcode")==0){
	 				return result;
	 			}else{
	 				System.out.println(result.toJSONString());
	 				return null;
	 			}
	 		}
		} catch (IOException e) {
		   e.printStackTrace();
		}
		return null;
	}
	
	public static String getSsoToken(){
		JSONObject  result = getToken("V8Vs-S9IKLo_d8qOGLgE1O0pEPzNDfPqFGSpSLlsguta6fpndjzwfAM0sxA5AZeN");
		if(result!=null){
			return result.getString("access_token");
		}
		return null;
	}
	
	public static String getNewEmailToken(){
		JSONObject  result = getToken("gQK8ttz7gVKuZqUH3-Fxx4e2n_ZgZSWwW_noQuMPkOLclxoxdF6eJ1pBphC6QZFp");
		if(result!=null){
			return result.getString("access_token");
		}
		return null;
	}
	
	public static String getAddressBookToken(){
		JSONObject  result = getToken("u_5HQGoMCchciZy1PV0L4eqFRYdKKeXHOPFPBZO-kBfTwJkODFA0PxAOGIXv1AiM");
		if(result!=null){
			return result.getString("access_token");
		}
		return null;
	}
	
	public static String getLoginUrl(String userId){
		Connection connection = Jsoup.connect("https://api.exmail.qq.com/cgi-bin/service/get_login_url");
		connection.data("access_token",getSsoToken());
		connection.data("userid",userId);
		try {
	 		Response response = connection.timeout(3000).ignoreContentType(true).method(Method.GET).execute();
	 		if(response.statusCode()==200){
	 			String strResult  = response.body();
	 			JSONObject result = JSONObject.parseObject(strResult);
	 			if(result.getIntValue("errcode")==0){
	 				return result.getString("login_url");
	 			}else{
	 				System.out.println(result.toJSONString());
	 				return null;
	 			}
	 		}
		} catch (IOException e) {
		   e.printStackTrace();
		}
		return null;
	}
	
	public static String getNewcount(String userId){
		Connection connection = Jsoup.connect("https://api.exmail.qq.com/cgi-bin/mail/newcount");
		connection.data("access_token",getNewEmailToken());
		connection.data("userid",userId);
		connection.data("type","0");
		connection.data("begin_date","2020-01-01");
		connection.data("end_date","2021-12-31");
		try {
			Response response = connection.timeout(5000).ignoreContentType(true).method(Method.GET).execute();
			if(response.statusCode()==200){
				String strResult  = response.body();
				JSONObject result = JSONObject.parseObject(strResult);
				if(result.getIntValue("errcode")==0){
					return result.getString("count");
				}else{
					System.out.println(result.toJSONString());
					return null;
				}
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	
	public static void main(String[] args) {
		System.out.println(getLoginUrl("<EMAIL>"));
	}
	

}
