<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>客户管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		.layui-table-cell{padding: 0 6px;font-size: 13px;}
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="custMgrForm">
			<input name="taskState" id="taskState" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>客户管理</h5>
						 <div class="input-group input-group-sm">
					 		<span class="input-group-addon">渠道</span>
						 	<select class="form-control input-sm" data-mars="YqAdmin.DictDao.select('Y006')" name="custSource">
	                    		<option value="">请选择</option>
	                    	</select>
						 </div>
						 <div class="input-group input-group-sm">
					 			<span class="input-group-addon">行业</span>
						 		<select class="form-control input-sm" data-mars="YqAdmin.DictDao.select('Y008')" name="industry">
		                    		<option value="">请选择</option>
	                    		</select>
						 </div>
						 <div class="input-group input-group-sm">
					 			<span class="input-group-addon">客户级别</span>
						 		<select class="form-control input-sm" data-mars="YqAdmin.DictDao.select('Y007')" name="custLevel">
		                    		<option value="">请选择</option>
		                    	</select>
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="CustMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="CustMgr.add()">+ 新建客户</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="custMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
	  			{{if currentUserId == CREATOR || isDevLead}}
					<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="CustMgr.edit">编辑</a>
				{{/if}}
				<a class="layui-btn layui-btn-normal layui-btn-xs layui-hide" lay-event="CustMgr.detail">查看</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script src="http://api.map.baidu.com/api?v=2.0&ak=EZPCgQ6zGu6hZSmXlRrUMTpr"></script>
<script type="text/javascript">
		
		var CustMgr={
			init:function(){
				$("#custMgrForm").initTable({
					mars:'CustDao.custList',
					skin:'line',
					id:'custMgrTable',
					autoSort:false,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left',
						fixed:'left'
					 },{
					    field: 'CUST_NAME',
						title: '客户名称',
						align:'left',
						minWidth:100,
						event:'CustMgr.detail',
						style:'color:#1E9FFF;cursor:pointer',	
						fixed:'left'
					},{
					    field: 'CUST_SOURCE',
						title: '客户来源',
						align:'center',
						width:70,
						templet:function(row){
							return getText(row['CUST_NATURE'],'custSource');
						}
					},{
					    field: 'MOBILE',
						title: '手机',
						align:'center',
						width:110
					},{
					    field: 'WEBSITE',
						title: '网址',
						align:'left',
						width:140
					},{
					    field: 'INDUSTRY',
						title: '行业',
						align:'left',
						width:100,
						templet:function(row){
							return getText(row['INDUSTRY'],'industry');
						}
					},{
					    field: 'CUST_LEVEL',
						title: '客户级别',
						align:'left',
						width:120,
						templet:function(row){
							return getText(row['CUST_LEVEL'],'custLevel');
						}
					},{
					    field: 'NEXT_FOLLOW_TIME',
						title: '下次联系时间',
						align:'center',
						width:110
					},{
					    field: 'REMARK',
						title: '备注',
						align:'center'
					},{
					    field: 'CUST_RESULT',
						title: '成交状态',
						sort:true,
						align:'center',
						width:80,
						templet:function(row){
							return row['CUST_RESULT']==0?'未成交':'已成交';
						}
					},{
					    field: 'LAST_FOLLOW_TIME',
						title: '最后跟进时间',
						align:'center',
						width:110
					},{
					    field: 'OWNER_ID',
						title: '负责人',
						align:'center',
						sort:true,
						width:70,
						templet:function(row){
							return '<a href="javascript:;" onclick="userInfoLayer(\''+row['OWNER_ID']+'\')">'+getUserName(row.OWNER_ID)+'</a>';
						}
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						width:90,
						align:'center',
						templet:function(row){
							var time= row['UPDATE_TIME'];
							return cutText(time,12,'');
						}
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
						width:130,
						sort:true,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'CREATOR',
						title: '创建人',
						sort:true,
						align:'center',
						width:70,
						templet:function(row){
							return '<a href="javascript:;" onclick="userInfoLayer(\''+row['CREATOR']+'\')">'+getUserName(row.CREATOR)+'</a>';
						}
					},{
						title: '操作',
						align:'center',
						width:80,
						cellMinWidth:80,
						fixed:'right',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							row['isSuperUser']=isSuperUser;
							row['deptId']=getDeptId();
							row['isDevLead']=isDevLead();
						    return renderTpl('bar1',row);
						}
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#custMgrForm").queryData({id:'custMgrTable',jumpOne:true});
			},
			detail:function(data){
				var  custId = data['CUST_ID'];
				var width = $(window).width();
				var w = '75%';
				if(width>1500){
					w = '800px';
				}else if(width<1000){
					w = '100%';
				}else{
					
				}
				popup.layerClose('custDetail');
				popup.layerShow({id:'custDetail',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:[w,'100%'],url:'${ctxPath}/pages/crm/cust-detail.jsp',title:false,data:{custId:custId}});
			},
			edit:function(data){
				var  custId = data['CUST_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/crm/cust-edit.jsp',title:'编辑客户',data:{custId:custId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/crm/cust-edit.jsp',title:'新增客户',closeBtn:0});
			}
		}
		function reloadCustInfo(){
			CustMgr.query();
		}
		$(function(){
			$("#custMgrForm").render({success:function(){
				CustMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>