<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 70px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="text-c" style="font-size: 20px;height: 60px;line-height: 60px;">${flow.flowName}</div>
				    	<table class="table table-vzebra">
					  		<tr class="flowInfo">
					  			<td class="required" style="width: 120px;">当前节点</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.nodeName"/>
					  			</td>
					  			<td class="required" style="width: 120px;">当前处理人</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.checkName"/>
					  			</td>
					  		</tr>
					  		<tr class="flowInfo">
					  			<td class="required" style="width: 120px;">申请时间</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" readonly="readonly" name="apply.applyTime"/>
					  			</td>
					  			<td class="required" style="width: 120px;">流程状态</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" readonly="readonly" name="apply.applyState" data-fn="flowApplyState"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}的请假申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">单号</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" readonly="readonly" data-mars="FlowDao.flowNo" name="apply.applyNo"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">岗位</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.post}"  class="form-control input-sm" name="apply.data1"/>
					  			</td>
					  			<td class="required">申请类别</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data2">
						  				<option value="">--</option>
						  				<option value="外勤">外勤</option>
						  				<option value="年休假">年休假</option>
						  				<option value="调休假">调休假 </option>
						  				<option value="事假">事假</option>
						  				<option value="病假">病假</option>
						  				<option value="婚假">婚假</option>
						  				<option value="丧假">丧假</option>
						  				<option value="产假或陪产假">产假或陪产假</option>
						  				<option value="产检假">产检假</option>
						  				<option value="其他">其他</option>
						  			</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">起止时间</td>
					  			<td style="width: 40%;">
					  				<input type="text" style="width: 40%;display: inline-block;" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 09:00:00',doubleCalendar:true,alwaysUseStartDate:true})" class="form-control input-sm Wdate" name="apply.data3"/>
					  				<input type="text" style="width: 40%;display: inline-block;" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 18:00:00',doubleCalendar:true,alwaysUseStartDate:true})" class="form-control input-sm Wdate" name="apply.data4"/>
					  			</td>
					  			<td class="required">请假天数</td>
					  			<td>
					  				<input type="number" data-rules="required" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">请假原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
				<div class="layui-col-md12 hidden-print approval-do">
				 	 <jsp:include page="/pages/flow/comm/approve-do.jsp"/>
				</div>
			</div>
			<div class="layer-foot text-c" style="position: fixed;"></div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="/yq-work/static/js/flow.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			var params = {};
			var days = $("[name='apply.data5']").val();
			var type = $("[name='apply.data2']").val();
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.nowNode.nodeCode;
			if(nodeCode=='人力行政部'){
				if(type=='事假'||days>=7){
					params['nextNodeCode'] = '中心副总';
				}else{
					params['nextNodeCode'] = '0';
				}
			}
			FlowCore.addCheckOrder(params);
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>