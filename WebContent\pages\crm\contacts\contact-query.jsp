<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>客户商机跟进</title>
	<style>
		.layui-badge{left: -1px!important;}
		
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
	  <style type="text/css">
	  	.editFn{opacity: 0;color: #03a9f4;}
	  	.el-timeline-item:hover .editFn{opacity: 1;text-decoration: underline;}
		.el-timeline{margin:0;font-size:14px;list-style:none}.el-timeline .el-timeline-item:last-child .el-timeline-item__tail{display:none}.el-timeline-item{position:relative;padding-bottom:20px}.el-timeline-item__wrapper{position:relative;padding-left:28px;top:-3px}.el-timeline-item__tail{position:absolute;left:4px;height:100%;border-left:2px solid #e4e7ed}.el-timeline-item__icon{color:#fff;font-size:13px}.el-timeline-item__node{position:absolute;background-color:#e4e7ed;border-radius:50%;display:flex;justify-content:center;align-items:center}.el-timeline-item__node--normal{left:-1px;width:12px;height:12px}.el-timeline-item__node--large{left:-2px;width:14px;height:14px}.el-timeline-item__node--primary{background-color:#409eff}.el-timeline-item__node--success{background-color:#67c23a}.el-timeline-item__node--warning{background-color:#e6a23c}.el-timeline-item__node--danger{background-color:#f56c6c}.el-timeline-item__node--info{background-color:#909399}.el-timeline-item__dot{position:absolute;display:flex;justify-content:center;align-items:center}.el-timeline-item__content{color:#303133}.el-timeline-item__timestamp{color:#909399;line-height:1;font-size:13px}.el-timeline-item__timestamp.is-top{margin-bottom:8px;padding-top:4px}.el-timeline-item__timestamp.is-bottom{margin-top:8px}.el-card{border-radius:4px;border:1px solid #ebeef5;background-color:#fff;overflow:hidden;color:#303133;transition:.3s}.el-card.is-always-shadow,.el-card.is-hover-shadow:focus,.el-card.is-hover-shadow:hover{box-shadow:0 2px 12px 0 rgba(0,0,0,.1)}.el-card__header{padding:18px 20px;border-bottom:1px solid #ebeef5;box-sizing:border-box}.el-card__body{padding:20px}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="CustFollowForm">
			<input name="custId" type="hidden" value="${param.custId}">
			<input name=custName type="hidden" value="${param.custName}">
			<input name="contactsId" type="hidden" value="${param.contactsId}">
			<input name="userId" type="hidden" value="${param.userId}">
			<input name="businessId" type="hidden" value="${param.businessId}">
			<div class="ibox">
				<c:if test="${!empty param.businessId}">
				  <div class="layui-card" style="margin-bottom:8px;">
					  <div class="layui-card-header">商机信息
					  	 <div class="input-group input-group-sm pull-right">
							<button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown">+ 跟进记录</button>
							 <ul class="dropdown-menu dropdown-menu-right">
							    <li><a href="javascript:;" onclick="CustContactsMgr.add('1')">售前跟进</a></li>
							    <li><a href="javascript:;" onclick="CustContactsMgr.add('2')">销售跟进</a></li>
							</ul>
						 </div>
					  </div>
					  <div class="layui-card-body">
						   <table class="table table-vzebra" data-mars="CustDao.busniessInfo" data-mars-prefix="business.">
							<tr style="display: none"> <td colspan="6" name="business.BUSINESS_TYPE"></td></tr>
				 			<tr>
				 				<td style="width: 100px;">商机名称</td>
				 				<td name="business.BUSINESS_NAME"></td>
				 				<td style="width: 100px;">客户名称</td>
				 				<td name="business.CUSTOMER_NAME"></td>
				 				<td style="width: 100px;">跟进阶段</td>
				 				<td style="width: 17%;" data-match="{'10':'沟通交流','20':'立项阶段','25':'POC阶段','30':'投标阶段','40':'商务阶段','50':'交付阶段'}" name="business.BUSINESS_STAGE"></td>
				 			</tr>
				 			<tr>
				 				<td>商机结果</td>
				 				<td name="business.BUSINESS_RESULT" data-match="{'0':'跟进中','60':'赢单','50':'输单','99':'取消'}"></td>
				 				<td>销售区域</td>
				 				<td name="business.BUSINESS_AREA"></td>
				 				<td>销售经理</td>
				 				<td name="business.SALES_BY_NAME"></td>
				 			</tr>
							<tr>
								<td>预计金额(万)</td>
								<td name="business.AMOUNT"></td>
								<td>可能性</td>
								<td><span name="business.POSSIBILITY"></span>%</td>
								<td>创建人</td>
								<td><span data-fn="getUserName" name="business.CREATOR"></span> <span name="business.CREATE_TIME"></span></td>
							</tr>
							<tr class="operator-row" style="display:none;">
								<td>预计成本(万)</td>
								<td name="business.COST_BUDGET"></td>
								<td>预计赢单日期</td>
								<td name="business.EXPECTED_WIN_DATE"></td>
								<td>实际赢单日期</td>
								<td name="business.ACTUAL_WIN_DATE"></td>
							</tr>
							<tr class="operator-row" style="display:none;">
								<td>业务平台</td>
								<td name="business.PLATFORM_NAME"></td>
								<td>平台参数</td>
								<td colspan="3" name="business.PLATFORM_PARAMS"></td>
							</tr>
				 			<tr>
				 				<td>商机内容</td>
				 				<td colspan="5" name="business.REMARK"></td>
				 			</tr>
				 		</table>
					  </div>
					</div>
				</c:if>
					<c:if test="${empty param.businessId}">
						<div class="ibox-title clearfix">
			          		 <div class="form-group">
			          		     <h5>跟进记录</h5>
			          		     <div class="input-group input-group-sm ml-15">
									 <span class="input-group-addon">跟进时间</span>	
									 <input type="text" name="beginDate" onclick="WdatePicker('{}')" class="form-control input-sm" style="width: 90px;">
									 <input type="text" name="endDate" onclick="WdatePicker('{}')" class="form-control input-sm" style="width: 90px;">
						    	</div>
								 <div class="input-group input-group-sm" style="width: 150px;">
							 		<span class="input-group-addon">跟进人</span>
								 	<input class="form-control input-sm" name="userName">
								 </div>
								 <div class="input-group input-group-sm" style="width: 180px;">
							 		<span class="input-group-addon">客户名称</span>
								 	<input class="form-control input-sm" name="customerName">
								 </div>
								  <div class="input-group input-group-sm" style="width: 160px;">
							 		<span class="input-group-addon">所属部门</span>
							 			<input name="deptId" type="hidden">
							 			<input data-rules="required" onclick="singleDept(this);" class="form-control input-sm">
								 </div>
								 <div class="input-group input-group-sm">
									 <button type="button" class="btn btn-sm btn-default" onclick="CustContactsMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								 </div>
								 <div class="input-group input-group-sm pull-right">
										<button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown">+ 跟进记录</button>
										 <ul class="dropdown-menu dropdown-menu-right">
										    <li><a href="javascript:;" onclick="CustContactsMgr.add('1')">售前跟进</a></li>
										    <li><a href="javascript:;" onclick="CustContactsMgr.add('2')">销售跟进</a></li>
										</ul>
								 </div>
			          	     </div>
			              </div> 
	          	     </c:if>
					 <script id="follow-template" type="text/x-jsrender">
						{{for data}}
							 <li class="el-timeline-item">
								<div class="el-timeline-item__tail"></div>
								<div class="el-timeline-item__node el-timeline-item__node--normal el-timeline-item__node--primary">
								</div>
								<div class="el-timeline-item__wrapper">
									<div class="el-timeline-item__timestamp is-top">
										{{:CREATE_TIME}}
									</div>
									<div class="el-timeline-item__content">
										<div class="el-card is-always-shadow">
											<div class="el-card__body">
												<p onclick="CustContactsMgr.custDetail('{{:CUST_ID}}');" style="font-size:13px;color:#4ba2ec;margin-top:-10px;cursor:pointer;">{{:CUSTOMER_NAME}} {{if BUSINESS_NAME}}<span class="ml-5 mr-5">/</span>{{:BUSINESS_NAME}}{{/if}}  <span class="label label-info">{{:FOLLOW_TYPE}}</span>
												  {{if FOLLOW_LEVEL}}<span class="pull-right">{{call:FOLLOW_LEVEL fn='followLevelLabel'}}</span>{{/if}}
												</p>
												<h4 style="font-size:14px;color:#444;margin-top:5px;line-height:26px;">
													{{call:CONTENT fn='getContent'}}
												</h4>
												{{if NEXT_CONTENT}}
													<hr>
													<div style="font-size:13px;color:#444;margin-top:5px;line-height:26px;">
														{{call:NEXT_CONTENT fn='getContent'}}
													</div>
												{{/if}}
												{{if FILE_COUNT>0}}<p onclick="fileDetail('{{:FOLLOW_ID}}')" style="font-size:13px;color:#5190de;line-height:20px;cursor:pointer;" title="点击查看"> <i class="layui-icon layui-icon-file"></i> 附件({{:FILE_COUNT}}个) </p> {{/if}}
												<p style="font-size:12px;color:#999;margin-top:5px;">{{call:CREATE_USER_ID fn='getUserName'}} 提交于{{:CREATE_TIME}} ,&nbsp; 跟进时间 {{:FOLLOW_BEGIN}} ~ {{:FOLLOW_END}}
													{{if FOLLOW_DAY}}，工时：{{:FOLLOW_DAY}}天{{/if}}
 													<a href="javascript:;" class="editFn" onclick="CustContactsMgr.edit('{{:FOLLOW_ID}}','{{:CUST_ID}}')">编辑</a>
 													<a href="javascript:;" class="editFn ml-10" onclick="CustContactsMgr.addFllow('{{:BUSINESS_ID}}','{{:BUSINESS_NAME}}','{{:CUST_ID}}','{{:CUSTOMER_NAME}}')">继续跟进</a>
												</p>
											</div>
										</div>
									</div>
								</div>
							</li>
						{{/for}}
						{{if data.length==0}}
							<div calss="text-c" style="height:200px;line-height:200px;text-align:center;font-size:18px;"><i class="fa fa-info-circle"></i>&nbsp;暂无数据</div>
						{{/if}}
					</script>
	              
					<div class="ibox-content" style="min-height: calc(100vh - 220px);" data-mars="CustDao.custFollowList" data-container="container" data-template="follow-template">
						    <table class="layui-hide" id="CustFollowTable"></table>
						    <ul class="el-timeline pt-10" id="container"></ul>
						    <div id="pageContainer" class="ml-30"></div>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var CustContactsMgr={
			init:function(){
				$("#CustFollowForm").initTable({
					mars:'CustDao.custFollowList',
					id:'CustFollowTable',
					autoSort:false,
					height:'full-160',
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'CREATE_USER_ID',
						title: '创建人',
						align:'center',
						width:70,
						templet:function(row){
							return getUserName(row.CREATE_USER_ID);
					   }
					},{
					    field: 'CONTENT',
						title: '联系内容',
						align:'left'
					},{
						field:'CUST_NAME',
						title:'所属客户',
						width:150,
						templet:'<div><a href="javascript:;" onclick="CustContactsMgr.custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
					},{
						field:'FOLLOW_BEGIN',
						title:'联系时间',
						align:'left',
						width:100
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},
					{
						field:'',
						title:'操作',
						width:80,
						templet:'<div><a href="javascript:;" onclick="CustContactsMgr.edit(\'{{d.FOLLOW_ID}}\',\'{{d.CUST_ID}}\',\'{{d.FOLLOW_SOURCE}}\')">修改</a></div>'
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				initPage();
				//$("#CustFollowForm").queryData({id:'CustFollowTable',jumpOne:true});
			},
			personInfo:function(data){
				var  contactsId = data['CONTACTS_ID'];
				var custName = data['CUST_NAME'];
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/crm/contacts/contacts-edit.jsp',title:'详情',data:{isEdit:0,contactsId:contactsId,custName:custName}});
			},
			edit:function(followId,custId,followSource){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['60%','80%'],url:'${ctxPath}/pages/crm/include/add-follow.jsp',title:'编辑',data:{followId:followId,custId:custId,followSource:followSource}});
			},
			add:function(followSource){
				var custId = $("[name='custId']").val();
				var custName = $("[name='custName']").val();
				var businessId = $("[name='businessId']").val();
				popup.layerShow({type:1,anim:0,full:fullShow(),scrollbar:false,maxmin:true,shadeClose:false,offset:'20px',area:['60%','80%'],url:'${ctxPath}/pages/crm/include/add-follow.jsp',title:'新增跟进记录',data:{custId:custId,custName:custName,businessId:businessId,followSource:followSource}});
			},
			custDetail:function(custId){
				var width = $(window).width();
				var w = '80%';
				if(width>1500){
					w = '800px';
				}else if(width<1000){
					w = '100%';
				}else{
					
				}
				popup.openTab({id:'custDetail',title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:[w,'100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId,isDiv:0}});
			},
			addFllow:function(businessId,sjName,custId,custName){
				var followSource = '1';
				popup.layerShow({type:1,anim:0,full:fullShow(),scrollbar:false,maxmin:true,shadeClose:false,offset:'20px',area:['60%','80%'],url:'${ctxPath}/pages/crm/include/add-follow.jsp',title:'新增跟进记录',data:{custId:custId,custName:custName,businessId:businessId,sjName:sjName,followSource:followSource}});
			}
		}
		function reloadFollowInfo(){
			$("[name='contactsId']").val('');
			CustContactsMgr.query();
		}
		$(function(){
			requreLib.setplugs('wdate');
		    initPage();
		    //CustContactsMgr.init();
		    var device = layui.device();
			if(device.mobile){
				$('.table,.layui-table').addClass('table-responsive');
				$('.table,.layui-table').parent().addClass('table-responsive');
				$('.table,.layui-table').addClass('text-nowrap');
			}
		});
		
		function fileDetail(id){
			popup.layerShow({url:'${ctxPath}/pages/common/file-view.jsp',area:['680px','400px'],scrollbar:false,offset:'20px',data:{fkId:id},id:'fileViewLayer',title:'查看文件'});
		}
		function initPage(){
			$("#CustFollowForm").render({data:{pageIndex:1,pageType:3,pageSize:10},success:function(result){
				 layui.use('laypage', function(){
					  var data = result['CustDao.custFollowList'];
					  if(data){
						  var laypage = layui.laypage;
						  laypage.render({
						    elem: 'pageContainer',
						    limit:10,
						    layout:['page','prev','next','limit','count'],
						    count: data.totalRow,
						    jump:function(obj, first){
						        if(!first){
						        	$("#CustFollowForm").render({data:{pageIndex:obj.curr,pageType:3,pageSize:obj.limit}});
						        }
						    }
						  });
					  }
					});
					
					// 检查商机类型并控制显示
					var businessType = $("[name='business.BUSINESS_TYPE']").text();
					if(businessType == "2") {
						$(".operator-row").show();
					} else {
						$(".operator-row").hide();
					}
			}});
		}
		function getContent(val){
			if(val){
				return val.replace(/\n|\r\n/g,'<br/>');
			}else{
				return '--';
			}
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>