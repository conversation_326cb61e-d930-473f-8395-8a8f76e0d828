<%@ page language="java" contentType="text/html;charset=UTF-8"%>

	<table class="layui-hide" id="productTable"></table>

	<script type="text/javascript">
	
		function loadProduct(){
			$("#SupplierDetailForm").initTable({
				mars:'SupplierDao.productList',
				id:'productTable',
				page:true,
				height:'full-250',
				cols: [[
	             {
	           	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
				    field: 'PRODUCT_NAME',
					title: '产品名称',
					align:'left'
				},{
					field:'TYPE_CODE',
					title:'产品编码'
				},{
					 field:'PRICE',
					 title:'含税价'
				 },{
					 field:'GOODS_COUNT',
					 title:'已采购数量'
				 },{
				    field: 'CREATE_TIME',
					title: '创建时间',
					align:'center'
				}
			]],done:function(data){
				$("#productCount").text(data['totalRow']);
		  }});
		}
		
		$(function(){
			loadProduct();
		});
</script>
