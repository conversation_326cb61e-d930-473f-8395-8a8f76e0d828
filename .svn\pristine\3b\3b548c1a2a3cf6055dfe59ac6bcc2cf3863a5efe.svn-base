package com.yunqu.work.service;

import java.sql.SQLException;

import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;

public class FlowService extends AppBaseService{

	private static class Holder{
		private static FlowService service=new FlowService();
	}
	public static FlowService getService(){
		return Holder.service;
	}
	
	public String getNextNodeId(String nodeId) throws SQLException {
		String nextNodeId = this.getQuery().queryForString("select c_node_id from yq_erp_order_check_node where node_id = ?", nodeId);
		return nextNodeId;
	}
	
	public JSONObject getNodeInfo(String nodeId) throws SQLException {
		JSONObject nodeInfo= this.getQuery().queryForRow("select * from yq_erp_order_check_node where node_id = ?", new Object[]{nodeId}, new JSONMapperImpl());
		return nodeInfo;
	}
	
	public JSONObject getFirstNode(String flowType) throws SQLException {
		JSONObject obj = this.getQuery().queryForRow("select * from yq_erp_order_check_node where p_node_id = 0 and busi_type = ? and node_state = 0 order by check_index", new Object[] {flowType}, new JSONMapperImpl());
		return obj;
	}
	
	public String[] getNodeUser(String flowType) {
		String sql = "select GROUP_CONCAT(user_id) user_ids,GROUP_CONCAT(user_name) user_names from yq_erp_order_check_node where busi_type='review' and node_state = 0 ORDER BY check_index";
		JSONObject checkObj = null;
		try {
			checkObj = this.getQuery().queryForRow(sql,new Object[] {},new JSONMapperImpl());
			String userIds = checkObj.getString("USER_IDS");
			String userNames = checkObj.getString("USER_NAMES");
			return new String[] {userIds,userNames};
		} catch (SQLException e) {
			getLogger().error(null, e);
			return new String[]{"",""};
		}
	}
	
}
