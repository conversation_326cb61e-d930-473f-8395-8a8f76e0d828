package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.HttpKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;

public class WxMsgService extends AppBaseService{
	private static class Holder{
		private static WxMsgService service=new WxMsgService();
	}
	
	@Override
	public Logger getLogger(){
		return LogEngine.getLogger("weixin","weixin");
	}
	
	public static WxMsgService getService(){
		return Holder.service;
	}
	public WxMsgService(){
		
	}
	public void sendWeeklyMsg(MessageModel model){
	
	}
	private void sendTemplateMsg(Map<String,String> queryParas){
		String url = queryParas.get("url");
		if(StringUtils.notBlank(url)) {
			if(url.indexOf("/api/goUrl")>-1) {
				url  = url.replaceAll("=", "_A");
				url = url.replaceAll("&", "_B");
				url = url.replace("goUrl?url_A", "goUrl?url=");
			}
			queryParas.put("url", url);
		}
		try {
			Thread.sleep(1000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		
		getLogger().info("queryParas_url:"+url);
		getLogger().info("queryParas:"+JSONObject.toJSONString(queryParas));
		
		if(ServerContext.isLinux()){
			String msgSwitch = AppContext.getContext(Constants.APP_NAME).getProperty("msgSwitch","0");
			if("1".equals(msgSwitch)) {
				String result=HttpKit.post("http://172.16.68.152/yq-work/api/sendTemplateMsg", queryParas, null);
				getLogger().info("result >"+result);
			}
		}
	}
	
	public void sendTextMsg(String openId,String msg){
		if(ServerContext.isLinux()){
			Map<String,String> queryParas = new HashMap<String,String>();
			queryParas.put("openId", openId);
			queryParas.put("msg", msg);
			String result=HttpKit.post("http://172.16.68.152/yq-work/api/sendTextMsg", queryParas, null);
			getLogger().info("result >"+result);
		}
	}
	
	/**
	 * 流程待办提醒
	{{first.DATA}}
	流程名称：{{keyword1.DATA}}
	申请人：{{keyword2.DATA}}
	申请时间：{{keyword3.DATA}}
	流程摘要：{{keyword4.DATA}}
	{{remark.DATA}}
	 */
	public void sendFlowTodoCheck(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","gv5U1Jb1bvAl0Dcwbv8xoxuVU-1NQ6xENcfrUdLctGY");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",StringUtils.defaultString(model.getData1(),"--"));
		queryParas.put("keyword2",StringUtils.defaultString(model.getData2(),"--"));
		queryParas.put("keyword3",StringUtils.defaultString(model.getData3(),"--"));
		queryParas.put("keyword4",StringUtils.defaultString(model.getData4(),"--"));
		queryParas.put("remark",StringUtils.defaultString(model.getDesc(),"--"));
		sendTemplateMsg(queryParas);
	}
	/**
	 * 流程审批完成提醒
	 {{first.DATA}}
	    审批事项：{{keyword1.DATA}}
	   审批结果：{{keyword2.DATA}}
	    处理时间：{{keyword3.DATA}}
	  {{remark.DATA}}
	 */
	public void sendFlowCheckOk(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","y2JCaESaOqnsS56e1MXrhVKL2i-3CDYkcMu2x2bjNzs");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",model.getData1());
		queryParas.put("keyword2",model.getData2());
		queryParas.put("keyword3",model.getData3());
		queryParas.put("remark",model.getDesc());
		sendTemplateMsg(queryParas);
	}
	
	/**
	 * 发起验收提醒
	 * @param model
	 */
	public void sendCheckTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/pages/task/task-detail.jsp?isDiv=0&taskId="+model.getFkId());
		queryParas.put("templateId","81uHwyil81Nkx2cTZRL8x57ZJbocYgi1O4va-_JGIPk");
		queryParas.put("first","有一项任务需要您验收\n");
		queryParas.put("keyword1",model.getFkId());
		queryParas.put("keyword2",model.getTitle());
		queryParas.put("keyword3",getUserName(model.getReceiver()));
		queryParas.put("keyword4",model.getData().get("DEADLINE_AT").toString());
		queryParas.put("remark","\n请尽快跟进验收该项任务。");
		sendTemplateMsg(queryParas);
	}
	public void sendFinishTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/pages/task/task-detail.jsp?isDiv=0&taskId="+model.getFkId());
		queryParas.put("templateId","U-VveUQheJZ3jn3DCXM4sfjrIqVqFf30LboSmMWtWgE");
		queryParas.put("first","提交的任务已经完成,请尽快去验收任务。\n");
		queryParas.put("keyword1",model.getTitle());
		queryParas.put("keyword2","已完成");
		queryParas.put("keyword3",EasyDate.getCurrentDateString());
		queryParas.put("remark","\n登录系统获取详情。");
		sendTemplateMsg(queryParas);
		
	}
	/**
	 * 反馈处理进度通知
	 * {{first.DATA}}
		留言时间：{{keyword1.DATA}}
		留言内容：{{keyword2.DATA}}
		处理进展：{{keyword3.DATA}}
		回复内容：{{keyword4.DATA}}
		{{remark.DATA}}
	 * @param model
	 */
	public void sendCommentTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","LNajqTxju_pSHohJYI45byLAintXjUO5DDmgQbJZJoE");
		queryParas.put("first",model.getTitle()+"\n");
		queryParas.put("keyword1",EasyDate.getCurrentDateString());
		queryParas.put("keyword2","--");
		queryParas.put("keyword3","--");
		queryParas.put("keyword4",delHTMLTag(model.getDesc()));
		queryParas.put("remark","\n登录系统获取详情。");
		sendTemplateMsg(queryParas);
		
	}
	public void sendReplyMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","LNajqTxju_pSHohJYI45byLAintXjUO5DDmgQbJZJoE");
		queryParas.put("first",model.getTitle()+"\n");
		queryParas.put("keyword1",model.getData1());
		queryParas.put("keyword2",model.getData2());
		queryParas.put("keyword3",model.getData3());
		queryParas.put("keyword4",model.getData4());
		queryParas.put("remark",model.getDesc());
		sendTemplateMsg(queryParas);
		
	}
	/**
	 * 到时未完成提醒
	 * @param model
	 */
	public void sendUnFinshTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/pages/task/task-detail.jsp?isDiv=0&taskId="+model.getFkId());
		queryParas.put("templateId","vUkl3AJ6kZGZ6WCjEepFjgHw1Gn0IYK5wa0V2YgXzi8");
		queryParas.put("first",model.getTitle()+"\n");
		queryParas.put("keyword1",model.getFkId());
		queryParas.put("keyword2",model.getData().get("PLAN_STARTED_AT").toString());
		queryParas.put("keyword3",model.getData().get("TASK_LEVEL").toString());
		queryParas.put("keyword4","--");
		queryParas.put("remark","\n已经超过任务截止处理时间,请尽快去处理。");
		sendTemplateMsg(queryParas);
		
	}
	public void sendNewTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/pages/task/task-detail.jsp?isDiv=0&taskId="+model.getFkId());
		queryParas.put("templateId","FIJ_BXh0eOMYDkPwJAYaoI7iyHf2E4BKDFH-rsvAUkI");
		queryParas.put("first",model.getTitle()+"\n");
		queryParas.put("keyword1",model.getFkId());
		queryParas.put("keyword2",getUserName(model.getSender()));
		queryParas.put("keyword3",model.getData().get("PLAN_STARTED_AT").toString());
		queryParas.put("remark","\n"+delHTMLTag(model.getDesc()));
		sendTemplateMsg(queryParas);
		
	}
	public void sendNewWeeklyMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		String weeklyId = model.getData().getString("WEEKLY_ID");
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/pages/weekly/weekly-detail.jsp?isDiv=0&weeklyId="+weeklyId);
		queryParas.put("templateId","YFUbnl2mCoolZAP3UMnq8GxDA0PifuRxdvh5W1qStBc");
		queryParas.put("first",model.getTitle()+"\n");
		queryParas.put("keyword1","--");
		queryParas.put("keyword2",getUserName(model.getSender()));
		queryParas.put("keyword3",EasyDate.getCurrentDateString());
		queryParas.put("remark","\n"+delHTMLTag(model.getDesc()));
		sendTemplateMsg(queryParas);
		
	}
	public void sendWeeklyNoticeMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送周报填写推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/pages/weekly/weekly-my.jsp?isDiv=0");
		queryParas.put("templateId","wBRZ1_BsUhSutcNe1f6H_EwSY6LZJdyLIxSoEBjBJcI");
		queryParas.put("first","您的周报待填写\n");
		queryParas.put("keyword1",model.getTitle());
		queryParas.put("keyword2",getUserName(model.getSender())+"\n");
		queryParas.put("keyword3",EasyDate.getCurrentDateString("yyyy-MM-dd"));
		queryParas.put("remark","\n请您及时处理！");
		sendTemplateMsg(queryParas);
		
	}
	/**
	 * {{first.DATA}}
		计划：{{keyword1.DATA}}
		关联联系人：{{keyword2.DATA}}
		计划时间：{{keyword3.DATA}}
		{{remark.DATA}}
	 * @param model
	 */
	public void sendScheduleNoticeMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送日程推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","wBRZ1_BsUhSutcNe1f6H_EwSY6LZJdyLIxSoEBjBJcI");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",model.getData1());
		queryParas.put("keyword2",getUserName(model.getSender())+"\n");
		queryParas.put("keyword3",model.getData3());
		queryParas.put("remark",model.getDesc());
		sendTemplateMsg(queryParas);
		
	}
	public void sendFoodServedMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送sendFoodServedMsg微信推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info(model.getReceiver()+" openId is empty.....");
			return;
		}
		
		this.sendTextMsg(getUserOpenIds(model.getReceiver()), "您的餐品已送达,请尽快食用");
		
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",getUserOpenIds(model.getReceiver()));
		queryParas.put("url","http://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","rKUI8ABcnJ7q17rj_rnZo-kWnzobwQG660e4Hirk_d4");
		queryParas.put("first","您的餐品已送达,请尽快食用。"+"\n");
		queryParas.put("keyword1","--");
		queryParas.put("keyword2","--");
		queryParas.put("keyword3",model.getTitle());
		queryParas.put("remark","\n"+model.getDesc());
		sendTemplateMsg(queryParas);
		
	}

    public String delHTMLTag(String htmlStr){ 
    	if(StringUtils.isBlank(htmlStr))return "请登录系统查看详细。";
    	return Jsoup.clean(htmlStr, Whitelist.simpleText());
        /*String regEx_script="<script[^>]*?>[\\s\\S]*?<\\/script>"; //定义script的正则表达式 
        String regEx_style="<style[^>]*?>[\\s\\S]*?<\\/style>"; //定义style的正则表达式 
        String regEx_html="<[^>]+>"; //定义HTML标签的正则表达式 
         
        Pattern p_script=Pattern.compile(regEx_script,Pattern.CASE_INSENSITIVE); 
        Matcher m_script=p_script.matcher(htmlStr); 
        htmlStr=m_script.replaceAll(""); //过滤script标签 
         
        Pattern p_style=Pattern.compile(regEx_style,Pattern.CASE_INSENSITIVE); 
        Matcher m_style=p_style.matcher(htmlStr); 
        htmlStr=m_style.replaceAll(""); //过滤style标签 
         
        Pattern p_html=Pattern.compile(regEx_html,Pattern.CASE_INSENSITIVE); 
        Matcher m_html=p_html.matcher(htmlStr); 
        htmlStr=m_html.replaceAll(""); //过滤html标签 
        String result=htmlStr.trim();
        if(result.length()>200){
        	return result.substring(0, 200);
        }
        return result; //返回文本字符串 
*/    } 
	private String getUserNames(String... userIds){
		if(userIds==null||userIds.length==0)return "";
		JSONObject result=null;//EhcacheKit.get(CacheTime.CacheName.hour.get(), "userList");
		if(result==null){
			EasyQuery query=ServerContext.getAdminQuery();
			String sql="select USER_ID,USERNAME from easi_user where STATE = 0";
			try {
				List<EasyRow> list=query.queryForList(sql);
				if(list!=null){
					Map<String, String> dict = new LinkedHashMap<String, String>();
					for(EasyRow row:list){
						String userId=row.getColumnValue(1);
						String userName=row.getColumnValue(2);
						dict.put(userId, userName);
					}
					result=new JSONObject();
					result.put("data",dict);
					//EhcacheKit.put(CacheTime.CacheName.hour.get(), "userList",result);
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		JSONObject data=result.getJSONObject("data");
		if(userIds.length==1){
			return data.getString(userIds[0]);
		}else{
			String sb=new String();
			for(String id:userIds){
				sb=sb+data.getString(id)+",";
			}
			sb = sb.substring(0,sb.length() - 1);
			return sb.toString();
		}
	}
	public String getUserName(String userId){
		try {
			String userName=this.getQuery().queryForString("select USERNAME from "+Constants.DS_MAIN_NAME+".EASI_USER WHERE USER_ID = ?",userId);
			return userName;
		} catch (SQLException e) {
			return "";
		}
	}
	public String getOpenId(String userId){
		try {
			String openId=this.getQuery().queryForString("select OPEN_ID from "+Constants.DS_MAIN_NAME+".EASI_USER WHERE USER_ID = ?",userId);
			this.getLogger().info(userId+" openId is " +openId);
			return openId;
		} catch (SQLException e) {
			return "";
		}
	}
	private String getUserOpenIds(String... userIds){
		if(userIds==null||userIds.length==0)return "";
		JSONObject result=null;//EhcacheKit.get(CacheTime.CacheName.threeMinutes.get(), "userOpenIds");
		if(result==null){
			result=new JSONObject();
			EasyQuery query=ServerContext.getAdminQuery();
			String sql="select USER_ID,OPEN_ID from easi_user where STATE = 0";
			try {
				List<EasyRow> list=query.queryForList(sql);
				if(list!=null){
					for(EasyRow row:list){
						String userId=row.getColumnValue(1);
						String userName=row.getColumnValue(2);
						result.put(userId, userName);
					}
					//EhcacheKit.put(CacheTime.CacheName.threeMinutes.get(), "userOpenIds",result);
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		if(userIds.length==1){
			return result.getString(userIds[0]);
		}else{
			String sb=new String();
			for(String id:userIds){
				sb=sb+result.getString(id)+",";
			}
			sb = sb.substring(0,sb.length() - 1);
			return sb.toString();
		}
	}

}
