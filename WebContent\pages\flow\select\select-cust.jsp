<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择客户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selecCustForm">
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
					 <span class="input-group-addon">名称</span>	
					 <input type="text" name="custName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectCust.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
    			 <button type="button" class="btn btn-sm btn-success pull-right mr-20" onclick="SelectCust.addCust()">+创建客户</button>
	   		</div>
            	<div class="ibox">
              		<table id="selectCustList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectCust.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectCust={
					sid:'${param.sid}',
					query : function(){
						$("#selecCustForm").queryData();
					},
					addCust:function(){
						popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:'${ctxPath}/pages/crm/cust/cust-edit.jsp',title:'新增客户',closeBtn:0});
					},
					initTable : function(){
						$("#selecCustForm").initTable({
							mars:'CustDao.selectCustList',
							id:'selectCustList',
							page:true,
							limit:20,
							height:'400px',
							rowDoubleEvent:'SelectCust.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'CUST_NAME',
								title: '客户名称'
							},{
							    field: 'CUST_NO',
								title: '编号',
								width:120
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectCust.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('selectCustList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var nos = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['CUST_NAME']);
									ids.push(data[index]['CUST_ID']);
									nos.push(data[index]['CUST_NO']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								if(el.prev().prev().length>0){
									el.prev().prev().val(nos.join(','));
								}
								popup.layerClose("selecCustForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							el.val(selectRow['CUST_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['CUST_ID']);
							}
							if(el.prev().prev().length>0){
								el.prev().prev().val(selectRow['CUST_NO']);
							}
							popup.layerClose("selecCustForm");
						}
					}
					
			};
			$(function(){
				SelectCust.initTable();
			});
			
			function reloadCustInfo(data){
				$("#selecCustForm [name='custName']").val(data['cust.CUST_NAME']);
				SelectCust.query();
			}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>