package com.yunqu.work.servlet.ehr;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;

@WebServlet("/servlet/ehrdata/*")
public class EhrDataInitServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private String getImgName(String[] files,String name){
		for(String imageName:files){
			if(imageName.contains(name)){
				return imageName;
			}
		}
		return "";
	}
	public EasyResult actionForDept(){
			String sql="select * from "+Constants.DS_MAIN_NAME+".easi_user";
			try {
				List<JSONObject> list=this.getQuery().queryForList(sql, null,new JSONMapperImpl());
				for(JSONObject json:list){
					String userId=json.getString("USER_ID");
					String depts=json.getString("DEPTS");
					if(StringUtils.notBlank(depts)){
						sql="select DEPT_ID from "+Constants.DS_MAIN_NAME+".easi_dept where DEPT_NAME = ?";
						String deptId=this.getQuery().queryForString(sql, depts);
						if(deptId==null){
							System.out.println(deptId);
						}
						EasyRecord record=new EasyRecord(Constants.DS_MAIN_NAME+".easi_dept_user");
						record.set("USER_ID", userId);
						record.set("DEPT_ID", deptId);
						record.set("POST_TYPE", 9);
						record.set("IDX_ORDER", 99);
						this.getQuery().save(record);
					}
				}
				//this.getQuery().executeUpdate(sql, id,s);
			} catch (SQLException e) {
				e.printStackTrace();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDept2(){
		String sql="select * from "+Constants.DS_MAIN_NAME+".easi_user";
		try {
			List<JSONObject> list=this.getQuery().queryForList(sql, null,new JSONMapperImpl());
			for(JSONObject json:list){
				String userId=json.getString("USER_ID");
				String depts=json.getString("DEPTS");
				if(StringUtils.notBlank(depts)){
					sql="select DEPT_ID from "+Constants.DS_MAIN_NAME+".easi_dept where DEPT_NAME = ?";
					String deptId=this.getQuery().queryForString(sql, depts);
					sql="update "+Constants.DS_MAIN_NAME+".easi_user set OWNER_DEPT_ID = ? where USER_ID = ?";
					this.getQuery().executeUpdate(sql, deptId,userId);
					if(deptId==null){
						System.out.println(">>>>>>>>>>>>>>>>>>"+depts);
					}
					
				}
			}
			//this.getQuery().executeUpdate(sql, id,s);
		} catch (SQLException e) {
			e.printStackTrace();
	}
	return EasyResult.ok();
}
	public EasyResult actionForReadExcel() throws SQLException{
		try {
			List<JSONObject>  dicts=this.getQuery().queryForList("select * from "+Constants.DS_MAIN_NAME+".easi_pc_dict",null,new JSONMapperImpl());
			Map<String,String> map=new HashMap<String,String>();
			
			for(JSONObject obj:dicts){
				map.put(obj.getString("DICT_NAME"),obj.getString("DICT_ID"));
			}
			File file=new File("F:\\picimg");
			
			String[] files=file.list();
			
			
			String path="F:\\abc.xlsx";
			List<List<String>> list=ExcelUtils.getInstance().readExcel2List(path);
			int i=0;
			for(List<String> ls:list){
				i++;
				/*for(int i=0;i<ls.size();i++){
					System.out.println(ls.get(i));
				}
				System.out.println("====================");*/
			    if(i>1){
			    	String dept=ls.get(1);//部门
			    	String type=ls.get(2);//员工类型
			    	String name=ls.get(3);//姓名
			    	String t4=ls.get(4);//工号
			    	String t5=ls.get(5);//性别
			    	String t6=ls.get(6);//负责区域
			    	String t7=ls.get(7);//常驻地
			    	String t8=ls.get(8);//职位
			    	String t9=ls.get(9);//手机号码
			    	String t10=ls.get(10);//身份证号码
			    	String t11=ls.get(11);//民族
			    	String t12=ls.get(12);//籍贯
			    	String t13=ls.get(13);//身份证地址
			    	String t14=ls.get(14);//出身年月
			    	String t15=ls.get(15);//出生月份
			    	String t16=ls.get(16);//年龄
			    	String t17=ls.get(17);//婚
			    	String t18=ls.get(18);//政治面貌
			    	String t19=ls.get(19);//最高学历
			    	String t20=ls.get(20);//毕业学校
			    	String t21=ls.get(21);//专业
			    	String t22=ls.get(22);//参加工作时间
			    	String t23=ls.get(23);//入司日期
			    	String t24=ls.get(24);//合同日期
			    	String t25=ls.get(25);//合同结束日期
			    	String t26=ls.get(26);//户籍类型
			    	String t27=ls.get(27);//邮箱
			    	
			    	if(t19.contains("本科")){
			    		t19="本科";
			    	}
			    	if(t19.contains("专科")){
			    		t19="专科";
			    	}
			    	if(t19.contains("大专")){
			    		t19="专科";
			    	}
			    	if(t19.contains("硕士")){
			    		t19="研究生";
			    	}
			    	
			    	System.out.println(name+"===="+t19);
			    	String userId=RandomKit.uuid();
			    	EasyRecord record=new EasyRecord(Constants.DS_MAIN_NAME+".easi_user", "USER_ID");
			    	try {
			    		record.setPrimaryValues(userId);
			    		record.set("USERNAME",name);
			    		record.set("NATION",map.get(t11));
			    		record.set("EMAIL",t27);
			    		record.set("MOBILE",t9);
			    		record.set("MAJOR",t21);
			    		record.set("UNIVERSITY",t20);
			    		record.set("BORN",t14.replaceAll("/", "-"));
			    		record.set("SEX",map.get(t5));
			    		record.set("EDU",map.get(t19));
			    		record.set("ADDR",t13);
			    		record.set("IDCARD",t10);
			    		record.set("PIC_URL","/files/headimages/"+getImgName(files,name));
			    		record.set("DEPTS",dept);
			    		record.set("CREATE_TIME",EasyDate.getCurrentDateString());
			    		
			    		record.set("DATA1",t4);//工号
			    		record.set("DATA2",t8 );
			    		record.set("DATA3",t23 );
			    		record.set("DATA4",t24);
			    		record.set("DATA5", t25 );
			    		record.set("DATA6", t17);
			    		record.set("DATA7",t6 );
			    		record.set("DATA8",t7);
			    		record.set("DATA9",type );
			    		record.set("DATA10",t26 );
			    		
			    		
						this.getQuery().save(record);
						
						EasyRecord login=new EasyRecord(Constants.DS_MAIN_NAME+".easi_user_login", "USER_ID");
						login.setPrimaryValues(userId);
						login.set("USER_ACCT",t4);
						login.set("USER_PWD","E10ADC3949BA59ABBE56E057F20F883E");
						login.set("LOGIN_TYPE",1);
						login.set("ACCT_STATE",0);
						login.set("LOCK_STATE",0);
						this.getQuery().save(login);
						
						
					} catch (Exception e) {
						e.printStackTrace();
					}
			    }
			    
			    
				
			}
			return EasyResult.ok(list);
		} catch (InvalidFormatException | IOException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}
}
