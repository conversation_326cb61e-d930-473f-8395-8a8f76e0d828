<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<SELECT style="display: none" name="paymentName" data-mars="ContractPaymentDao.selectPayment(${param.contractId})"  data-mars-top="true" ></SELECT>
<table id="incomeStageTable"></table>
<script  type="text/html" id="toolbar2">
    <button class="btn btn-info btn-sm" onclick="incomeStageList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-15" onclick="reloadIncomeStageList()" type="button">刷新</button>
    <button class="btn btn-default btn-sm ml-15" onclick="incomeStageList.edit()" type="button">修改</button>
    <button class="btn btn-default btn-sm ml-15" onclick="incomeStageList.del()" type="button">删除</button>
</script>
<script type="text/javascript">
    var incomeStageList={
        init:function(){
            $("#ContractDetailForm").initTable({
                mars:'IncomeStageDao.incomeStageList',
                id:'incomeStageTable',
                page:true,
                toolbar:'#toolbar2',
                cellMinWidth:50,
                cols: [[
                    {
                        type: 'checkbox',
                        align:'left'
                    },{
                        field: 'STAGE_NAME',
                        title: '阶段名称',
                        align:'left',
                        minWidth:80
                    },{
                        field:'ACT_COMP_DATE',
                        title:'实际完成日期',
                        align:'left',
                        sort:true,
                        minWidth:65
                    },{
                        field:'RATIO',
                        title:'比例（%）',
                        minWidth:60
                    },{
                        field:'PLAN_AMOUNT',
                        title:'计划收款金额',
                        minWidth:100
                    },{
                        field:'CREDIT_PRD',
                        title:'信用期（天）',
                        minWidth:80
                    },{
                        field:'PAYMENT_ID',
                        title:'对应款项',
                        minWidth:280,
                        templet:function(d){
                            return getText(d.PAYMENT_ID,'paymentName');
                        }
                    },{
                        field:'REMARK',
                        title:'备注',
                        minWidth:120
                    },{
                        field:'HARDWARE_CONF_FLAG',
                        title:'硬件已确认',
                        minWidth:100,
                        templet:function(d){
                            if(d.HARDWARE_CONF_FLAG == '1'){
                                return '是';
                            }else{
                                return '否';
                            }
                        }
                    },{
                        field:'INCOME_CONF_FLAG',
                        title:'收入已确认',
                        minWidth:100,
                        templet:function(d){
                            if(d.INCOME_CONF_FLAG == '1'){
                                return '是';
                            }else{
                                return '否';
                            }
                        }
                    },{
                        field:'YULIU_RATIO',
                        title:'预留比例',
                        minWidth:100
                    },{
                        field:'CHECKED_FLAG',
                        title:'已审核',
                        width:65,
                        templet: function(d) {
                            return '<input type="checkbox" lay-skin="primary" ' + (d.CHECKED_FLAG == 1 ? 'checked' : '') + ' disabled>';
                        }
                    },{
                        field: 'UPDATE_TIME',
                        title: '更新时间',
                        width:120,
                        align:'center'
                    }
                ]],
                done:function(res){
                    if(res.totalRow!=undefined){
                        $("#incomeStageCount").text("("+res.totalRow+")");
                    }
                }
            });
        },
        add:function (){
            var contractId = $("[name='contractId']").val();
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增收入确认阶段',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/income-stage-edit.jsp',title:'新增收入确认阶段',data:{contractId:contractId,isNew:'1'}});
        },
        edit:function (){
            var checkStatus = table.checkStatus('incomeStageTable');

            if(checkStatus.data.length > 1){
                layer.msg('一次只能更新一个款项',{icon : 7, time : 1000});
            }
            else if(checkStatus.data.length == 1){
                var incomeStageId = checkStatus.data[0]['INCOME_STAGE_ID'];
                var contractId = $("[name='contractId']").val();
                popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新收入确认阶段',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/income-stage-edit.jsp',title:'编辑收入确认阶段',data:{contractId:contractId,incomeStageId:incomeStageId,isNew:'0'}});
            }else{
                layer.msg("请选择!");
            }
        },
        del:function (){
            var checkStatus = table.checkStatus('incomeStageTable');
            if(checkStatus.data.length>0){
                layer.confirm("确认要删除吗?",{icon:3,offset:'120px'},function(){
                    ajax.remoteCall("${ctxPath}/servlet/incomeStage?action=BatchDel",checkStatus.data,function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                layer.closeAll();
                                reloadIncomeStageList();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                });
            }else{
                layer.msg("请选择!");
            }
        }


    }

    $(function(){
        var contractId='${param.contractId}';
        $('#ContractDetailForm').render({success:function(){
                incomeStageList.init();
            }});
    });

    function reloadIncomeStageList(){
        $("#ContractDetailForm").queryData({id:'incomeStageTable',page:false});
    }
</script>
