<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<form id="approveForm">
	<table class="table table-edit table-vzebra mt-10">
	   	<tr>
	   		<td style="width: 120px;">评审结果</td>
	   		<td>
	   			<label class="radio radio-inline radio-success">
	            	<input type="radio" value="1" name="checkResult" checked="checked"> <span>可行</span>
	            </label>
	            <label class="radio radio-inline radio-success">
	            	<input type="radio" value="2" name="checkResult"> <span>不可行</span>
	            </label>
	            <label class="radio radio-inline radio-success">
	            	<input type="radio" value="3" name="checkResult"> <span>沟通后再确定</span>
	            </label>
	            <a class="ml-5 addXbBtn" style="display: none;" data-flag="1" href="javascript:selectXbBtn(this);">+选择协办人员</a>
	   		</td>
	   	</tr>
	   	<tr class="selectXbTr" style="display: none;">
	   		<td>选择协办人员</td>
	   		<td>
	   			<input type="hidden" id="xbUserId" name="xbUserId"/>
			    <input type="text" style="width: 95%;" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" id="xbUserName" name="xbUserName"/>
	   		</td>
	   	</tr>
	   	<tr>
	   		<td style="width: 120px;vertical-align: top;">结论性意见</td>
	   		<td>
	   			<label class="radio radio-inline radio-success">
	            	<input type="radio" value="1" name="finalResult"> <span>是</span>
	            </label>
	            <label class="radio radio-inline radio-success">
	            	<input type="radio" value="2" name="finalResult"> <span>否</span>
	            </label>
	            <br>
	            <small>如果选择[是]会流转到下一个审批人,当前审批完成,否则继续沟通确认评审结果</small>	
	   		</td>
	   	</tr>
	   	<tr class="checkSelect hidden">
	   		<td>下步节点</td>
	   		<td>
	   			<input id="nodeId" name="nodeId" style="width: 90%;" readonly="readonly" class="form-control"/>
	   		</td>
	   	</tr>
	   	<tr class="checkSelect hidden">
	   		<td style="width: 120px;">下步处理人</td>
	   		<td>
	   			<input type="hidden" name="checkIds"/>
			    <input type="text" style="width: 90%;" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" id="ccNames" name="checkNames"/>
	   		</td>
	   	</tr>
	   	<tr class="ccSelect hidden">
	   		<td style="width: 120px;">抄送人/可选</td>
	   		<td>
	   			<input type="hidden" name="ccIds"/>
			    <input type="text" style="width: 90%;" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="ccNames"/>
	   		</td>
	   	</tr>
	   	<tr>
	   		<td style="vertical-align: top;margin-top: 20px;">评审描述</td>
	   		<td>
	   			<textarea style="height: 100px;width: 92%;" name="checkDesc" class="form-control"></textarea>
	   		</td>
	   	</tr>
	   	<tr>
	   		<td>相关附件</td>
	   		<td>
	   			<button type="button" class="btn btn-xs btn-default approve-upload-btn">+上传附件</button>
	   		</td>
	   	</tr>
	 </table>
	<div class="layer-foot text-c">
	  	 <button class="btn btn-info btn-sm" type="button" onclick="Flow.submitCheck();"> 确 定 </button>
	</div>
	<script>
		$(function(){
			FlowCore.uploadFile({elem:'.approve-upload-btn'});
			$('#approveForm input[type=radio][name=checkResult]').change(function() {
		        if (this.value == '3') {
		        	$('.addXbBtn').show();
		        }else{
		        	$('.addXbBtn,.selectXbTr').hide();
		        }
		    });
		});
		
		function selectXbBtn(el){
			var flag = $(el).data('flag');
			if(flag=='1'||flag==undefined){
				$(el).data('flag','2');
				$('.selectXbTr').show();
				$('.addXbBtn').html('✗移除协办');
				$('.selectXbTr input').val('');
			}else{
				$(el).data('flag','1');
				$('.selectXbTr').hide();
				$('.addXbBtn').text('+新增协办');
			}
		}
		
	</script>
</form>