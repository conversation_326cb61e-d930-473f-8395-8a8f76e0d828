package com.yunqu.work.utils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;


/**
 * https://platform.deepseek.com/
 * https://platform.deepseek.com/api-docs/zh-cn/
 * <AUTHOR>
 */
public class DeepSeekApi {
    private static String apiKey = "sk-a70891a410244db1a2aa351ba93bdcb7";
    private static String apiUrl = "https://api.deepseek.com/chat/completions";
    private static String model = "deepseek-chat";
    private static Boolean isStream = false;

    public static String generateContent(String roleDesc,String userContent){
    	Map<String, String> messages = new HashMap<String,String>();
    	messages.put("system",roleDesc);
    	messages.put("user",userContent);
        return excuteDeepSeekReq(messages,model,isStream);
    }
    
    /**
          * 优化大师
     * @param userContent
     * @return
     */
    public static String gcByOptimizeHelper(String userContent){
    	String system = "你是我的内容优化智能助手";
    	return generateContent(system,userContent);
    }
    
    /**
          * 代码大师
     * @param userContent
     * @return
     */
    public static String gcByCodeHelper(String userContent){
    	String system = "你好 我是代码智能助手，任何关于编程的问题，都可以问我。";
    	return generateContent(system,userContent);
    }
   
    public  static String excuteDeepSeekReq(Map<String, String> messages, String model, Boolean stream){
       String response = "";
        try {
        	JSONObject params = getReqParams(messages,model,stream);
            response = requestApiResponse(params);
        } catch (Exception e) {
        	LogUtils.getLogger().error("将参数转为JSON对象并发起API请求出错:"+e.getMessage(),e);
            response = "发生异常: "+e.getMessage();
        }
        return response;
    }

    /**
          * 将给定的参数转换为一个JSONObject对象。
     * @param role 包含角色信息的字符串列表。
     * @param content 包含内容信息的字符串列表，与role列表项对应。
     * @param model 模型名称字符串。
     * @param stream 是否为流式处理的布尔值。
     * @return 返回构建好的JSONObject对象，包含model，messages和stream字段。
     */
    private static JSONObject getReqParams(Map<String, String> messages,String model,Boolean stream){
        // 创建JSONObject对象
        JSONObject requestBody = new JSONObject();
        // 创建messages数组
        JSONArray messagesArray = new JSONArray();
        Set<String> keys =  messages.keySet();
        for(String key:keys) {
        	JSONObject roleMessage = new JSONObject();
        	roleMessage.put("role", key);
        	roleMessage.put("content", messages.get(key));
        	messagesArray.add(roleMessage);
        }
        // 添加字段到requestBody
        requestBody.put("model", model);
        requestBody.put("messages", messagesArray);
        requestBody.put("stream",stream);
        return requestBody;
    }


    /**
          * 向指定API发送请求，并获取响应数据。
     *
     * @param requestBody 请求体，以JSONObject形式提供。
     * @return 响应数据中的特定对象，如果处理失败或异常，则返回null。
     */
    private static String requestApiResponse(JSONObject requestBody){
        String result = "";
        HttpURLConnection connection = null;
        BufferedReader reader = null;
        try {
            //  创建URL对象
            URL url = new URL(apiUrl);
            // 打开连接
             connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法为POST
            connection.setRequestMethod("POST");
            // 设置请求头
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Authorization", "Bearer " + apiKey);
            // 启用输出流
            connection.setDoOutput(true);
            // 发送请求体数据
            OutputStream outputStream = connection.getOutputStream();
            outputStream.write(requestBody.toString().getBytes());
            outputStream.flush();

            // 获取API响应数据
            reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            //处理函数
            String textAfterExtract = JSONPath.read(response.toString(), "$.choices[0].message.content").toString();
            result = textAfterExtract;
        }catch (Exception e){
        	LogUtils.getLogger().error("发起API请求出错:"+e.getMessage(),e);
            result = "发生异常: "+e.getMessage();
        }finally {
        	if(reader != null){
        		try{
        			reader.close();
        		}catch (Exception e){
        			LogUtils.getLogger().error("关闭获取API响应数据的reader 出错:"+e.getMessage(),e);
        		}
        	}
            if(connection != null){
                try{
                    // 关闭连接
                    connection.disconnect();
                }catch (Exception e){
                	LogUtils.getLogger().error("关闭HttpConnection出错:"+e.getMessage(),e);
                }
            }
        }
        return  result;

    }
    
    public static void main(String[] args) {
        String testContent = gcByCodeHelper("java mysql jdbc代码");
        System.out.println(testContent);

    }

}


