<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title></title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="incomeStageEditForm" class="form-horizontal" data-mars="IncomeStageDao.record" autocomplete="off" data-mars-prefix="incomeStage.">
        <input type="hidden" value="${param.incomeStageId}" name="incomeStage.INCOME_STAGE_ID"/>
        <input type="hidden" value="${param.contractId}" name="incomeStage.CONTRACT_ID"/>
        <input type="hidden" name="oldContractStageIds"/>
        <input type="hidden" name="oldAmount"/>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td class="required">阶段名称</td>
                <td>
                    <select name="incomeStage.STAGE_NAME" data-rules="required" class="form-control">
                        <option value="初验">初验</option>
                        <option value="终验">终验</option>
                        <option value="维护">维护</option>
                        <option value="分成">分成</option>
                        <option value="上线">上线</option>
                        <option value="阶段">阶段</option>
                    </select>
                </td>
                <td class="required">计划完成日期</td>
                <td>
                    <input data-rules="required" type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="incomeStage.PLAN_COMP_DATE"  <c:if test="${!empty param.incomeStageId}"> disabled </c:if> class="form-control input-sm Wdate">
                </td>
            </tr>
            <tr>
                <td>对应合同阶段</td>
                <td colspan="3">
                    <input type="hidden" name="incomeStage.CONTRACT_STAGE_ID" class="form-control input-sm">
                    <button type="button" name="contractStageButton" onclick="incomeStageSelectStage()" class="btn btn-primary btn-sm"> 选择合同阶段</button>
                    <button type="button" name="contractStageButtonDisabled" style="display: none" disabled class="btn btn-primary btn-sm"> 已收入确认不可修改</button>
                    <br><span name="contractStageSpan">已选合同阶段：</span>
                </td>
            </tr>
            <tr>
                <td class="required">计划收款金额(含税)</td>
                <td>
                    <input type="text" name="incomeStage.PLAN_AMOUNT" readonly data-rules="required" class="form-control input-sm" placeholder="选择合同阶段后自动计算">
                </td>
                <td class="required">占合同总额比例(%)</td>
                <td>
                    <input type="text" name="incomeStage.RATIO" readonly data-rules="required"  class="form-control" placeholder="选择合同阶段后自动计算">
                </td>
            </tr>
            <tr>
                <td>信用期（天）</td>
                <td>
                    <input type="text" name="incomeStage.CREDIT_PRD" class="form-control">
                </td>
                <td>预留比例(%)</td>
                <td>
                    <input type="text" name="incomeStage.YULIU_RATIO" class="form-control">
                </td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <input type="text"  name="incomeStage.REMARK" class="form-control">
                </td>
            </tr>
            <tr>
                <td>已审核</td>
                <td colspan="3">
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio"  value="1" name="incomeStage.CHECKED_FLAG"><span>是</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="0" name="incomeStage.CHECKED_FLAG"><span>否</span>
                    </label>
                </td>
            </tr>
            <tr style="display: none">
                <td class="required">硬件已确认</td>
                <td colspan="3">
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio"  value="1" name="incomeStage.HARDWARE_CONF_FLAG"><span>是</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="0" name="incomeStage.HARDWARE_CONF_FLAG"><span>否</span>
                    </label>
                </td>
            </tr>
            </tbody>
        </table>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="IncomeStageEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll();"> 关闭 </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("IncomeStageEdit");

        IncomeStageEdit.incomeStageId = '${param.incomeStageId}';
        IncomeStageEdit.isNew = '${param.isNew}';
        IncomeStageEdit.contractId = '${param.contractId}';
        IncomeStageEdit.isConfirm = '${param.isConfirm}';

        $(function(){
            $("#incomeStageEditForm").render({success:function(result){
                    $('#incomeStageEditForm td').css('min-width', '120px');
                    $('#incomeStageEditForm th').css('min-width', '120px');
                    loadContractStageSelected(result);
                    if(IncomeStageEdit.isConfirm === '1') {
                        $('[name="contractStageButtonDisabled"]').show();
                        $('[name="contractStageButton"]').hide();
                    }

                }});
        });

        IncomeStageEdit.ajaxSubmitForm = function(){
            if(form.validate("#incomeStageEditForm")){
                if(IncomeStageEdit.isNew=='1'){
                    IncomeStageEdit.insertData();
                }
                else if(IncomeStageEdit.incomeStageId){
                    IncomeStageEdit.updateData();
                }else{
                    IncomeStageEdit.insertData();
                }
            };
        }

        IncomeStageEdit.insertData = function() {
            var data = form.getJSONObject("#incomeStageEditForm");
            ajax.remoteCall("${ctxPath}/servlet/incomeStage?action=add",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadIncomeStageList();
                        reloadStageList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        IncomeStageEdit.updateData = function() {
            var data = form.getJSONObject("#incomeStageEditForm");
            ajax.remoteCall("${ctxPath}/servlet/incomeStage?action=update",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadIncomeStageList();
                        reloadStageList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        function incomeStageSelectStage(){
            var id = new Date().getTime();
            var contractId = IncomeStageEdit.contractId;
            var incomeStageId = IncomeStageEdit.incomeStageId;
            popup.layerShow({full:fullShow(),scrollbar:false,area:['700px','500px'],offset:'20px',title:'选择合同阶段',url:'${ctxPath}/pages/crm/contract/include/select/stage-select.jsp',data:{sid:id,type:'checkbox',contractId:contractId,incomeStageId:incomeStageId}});
        }

        function loadContractStageSelected(result){
            var contractStageIds = [];
            var incomeRecordData = result["IncomeStageDao.record"] && result["IncomeStageDao.record"].data;
            if (incomeRecordData && Object.keys(incomeRecordData).length > 0 && incomeRecordData["CONTRACT_STAGE_ID"] !== "") {
                $('input[name="oldContractStageIds"]').val(result["IncomeStageDao.record"]["data"]["CONTRACT_STAGE_ID"]);
                contractStageIds = result["IncomeStageDao.record"]["data"]["CONTRACT_STAGE_ID"].split(",");
                $('input[name="oldAmount"]').val(result["IncomeStageDao.record"]["data"]["PLAN_AMOUNT"]);
            } else {
                return;
            }

            for(var i=0;i<contractStageIds.length;i++){
                var newSpan = $('<span name="contractStageNames"></span>');
                newSpan.text(getText(contractStageIds[i],'contractStage'));
                $("[name='contractStageSpan']").after(newSpan);
                $("[name='contractStageSpan']").after($('<br>'));
            }
        }

        function StageSelectCallBack(row){
            $("[name='incomeStage.CONTRACT_STAGE_ID']").val(row['CONTRACT_STAGE_ID']);
            $("[name='incomeStage.PLAN_AMOUNT']").val(row['PLAN_RCV_AMT']);
            $("[name='incomeStage.RATIO']").val(row['RATIO']);
                var contractStageNames = [];
                contractStageNames = row['CONTRACT_STAGE_NAME'].split(",");
               for(var i=0;i<contractStageNames.length;i++){
                   var newSpan = $('<span name="contractStageNames"></span>');
                   newSpan.text(contractStageNames[i]);
                   $("[name='contractStageSpan']").after(newSpan);
                   $("[name='contractStageSpan']").after($('<br>'));
               }
        }

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>