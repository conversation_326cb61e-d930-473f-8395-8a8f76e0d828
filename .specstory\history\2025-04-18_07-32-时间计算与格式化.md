<!-- Generated by SpecStory -->

# 时间计算与格式化 (2025-04-18 15:32:48)

_**User**_

我现在2025-04-18 09:00到2025-04-18 20:00 怎么返回是一天，应该是1+2/8

---

_**Assistant**_

Read file: WebContent/static/js/datediff.js

---

Read file: WebContent/pages/flow/hr/overtime/overtime-apply.jsp

---

```diff
  			time=0;
  		try{
- 			if(startInt <= wt.start && wt.end <= endInt){ //范围外直接返回一天
- 				time = workHour * 3600 *1000;
+ 			if(startInt <= wt.start && wt.end <= endInt){ 
+ 				// 修改：计算超出工作时间的部分
+ 				// 先计算正常工作时间
+ 				var normalWorkTime = wt.end - wt.start - (wt.noonEnd - wt.noonStart);
+ 				// 计算开始时间到上班开始的时间（如有）
+ 				var beforeWorkTime = startInt < wt.start ? 0 : 0;
+ 				// 计算下班后到结束时间的时间（如有）
+ 				var afterWorkTime = endInt > wt.end ? (endInt - wt.end) : 0;
+ 				// 总时间 = 正常工作时间 + 额外工作时间
+ 				time = normalWorkTime + beforeWorkTime + afterWorkTime;
  			}else{//控制时间范围
  				startInt = startInt<=wt.start?wt.start:startInt;//确保在上班时间后
```

---



---

```diff
  			time=0;
  		try{
- 			if(startInt <= wt.start && wt.end <= endInt){ 
- 				// 修改：计算超出工作时间的部分
- 				// 先计算正常工作时间
+ 			// 正常工作时间（除去午休）
  			var normalWorkTime = wt.end - wt.start - (wt.noonEnd - wt.noonStart);
- 				// 计算开始时间到上班开始的时间（如有）
- 				var beforeWorkTime = startInt < wt.start ? 0 : 0;
- 				// 计算下班后到结束时间的时间（如有）
- 				var afterWorkTime = endInt > wt.end ? (endInt - wt.end) : 0;
- 				// 总时间 = 正常工作时间 + 额外工作时间
- 				time = normalWorkTime + beforeWorkTime + afterWorkTime;
- 			}else{//控制时间范围
- 				startInt = startInt<=wt.start?wt.start:startInt;//确保在上班时间后
- 				endInt = endInt>=wt.end?wt.end:endInt;//确保在下班时间前
- 				if(startInt>=wt.noonStart && startInt<=wt.noonEnd){//开始时间在午休期间,设置为午休结束
- 					startInt = wt.noonEnd
- 				}
- 
- 				if(endInt>=wt.noonStart && endInt<=wt.noonEnd){//结束时间在午休期间,设置为午休开始
- 					endInt = wt.noonStart
- 				}
- 
- 				//时间在前半段或者后半段,直接计算
- 				if(endInt<=wt.noonStart || startInt>=wt.noonEnd){
- 					time = endInt - startInt
- 				}else{//需要去除午休时间
- 					time = endInt - startInt - (wt.noonEnd - wt.noonStart)
- 				}
+ 			
+ 			// 情况1：如果完全在工作时间内
+ 			if(startInt >= wt.start && endInt <= wt.end) {
+ 				// 需要判断是否跨越午休时间
+ 				if(startInt < wt.noonStart && endInt > wt.noonEnd) {
+ 					// 跨越整个午休
+ 					time = endInt - startInt - (wt.noonEnd - wt.noonStart);
+ 				} else if(startInt < wt.noonStart && endInt > wt.noonStart && endInt <= wt.noonEnd) {
+ 					// 开始在午休前，结束在午休中
+ 					time = endInt - startInt - (endInt - wt.noonStart);
+ 				} else if(startInt >= wt.noonStart && startInt < wt.noonEnd && endInt > wt.noonEnd) {
+ 					// 开始在午休中，结束在午休后
+ 					time = endInt - startInt - (wt.noonEnd - startInt);
+ 				} else if(startInt >= wt.noonStart && endInt <= wt.noonEnd) {
+ 					// 完全在午休中，不算时间
+ 					time = 0;
+ 				} else {
+ 					// 不跨午休
+ 					time = endInt - startInt;
+ 				}
+ 			}
+ 			// 情况2：开始时间在工作时间前，结束时间在工作时间内
+ 			else if(startInt < wt.start && endInt <= wt.end) {
+ 				// 计算工作开始到结束的时间
+ 				if(endInt <= wt.noonStart) {
+ 					// 结束在午休前
+ 					time = endInt - wt.start;
+ 				} else if(endInt > wt.noonStart && endInt <= wt.noonEnd) {
+ 					// 结束在午休中
+ 					time = wt.noonStart - wt.start;
+ 				} else {
+ 					// 结束在午休后
+ 					time = endInt - wt.start - (wt.noonEnd - wt.noonStart);
+ 				}
+ 			}
+ 			// 情况3：开始时间在工作时间内，结束时间在工作时间后
+ 			else if(startInt >= wt.start && endInt > wt.end) {
+ 				// 计算开始到工作结束的时间，再加上超出部分
+ 				var withinWorkTime = 0;
+ 				if(startInt < wt.noonStart) {
+ 					// 开始在午休前
+ 					withinWorkTime = wt.end - startInt - (wt.noonEnd - wt.noonStart);
+ 				} else if(startInt >= wt.noonStart && startInt <= wt.noonEnd) {
+ 					// 开始在午休中
+ 					withinWorkTime = wt.end - wt.noonEnd;
+ 				} else {
+ 					// 开始在午休后
+ 					withinWorkTime = wt.end - startInt;
+ 				}
+ 				
+ 				// 加上超出部分
+ 				var extraTime = endInt - wt.end;
+ 				time = withinWorkTime + extraTime;
+ 			}
+ 			// 情况4：完全超出工作时间（开始时间在工作时间前，结束时间在工作时间后）
+ 			else if(startInt < wt.start && endInt > wt.end) {
+ 				// 包含全部工作时间，再加上前后超出部分
+ 				time = normalWorkTime + (endInt - wt.end);
  			}
  		}catch(e){
```

---

```bash
cd /d%3A/develop/workbench202111/yq-work && dir WebContent\static\js\datediff.js
```

```
PS D:\develop\workbench202111\yq-work> cd /d%3A/develop/workbench202111/yq-work 
&& dir WebContent\static\js\datediff.js
Set-Location: Cannot find path 'D:\d%3A\develop\workbench202111\yq-work' because
 it does not exist.
PS D:\develop\workbench202111\yq-work>
```

---

```bash
dir WebContent\static\js\datediff.js
```

```
PS D:\develop\workbench202111\yq-work> dir WebContent\static\js\datediff.js

    Directory: D:\develop\workbench202111\yq-work\WebContent\static\js

Mode                 LastWriteTime         Length Name
----                 -------------         ------ ----
-a---           2025/4/18    15:34          13222 datediff.js

PS D:\develop\workbench202111\yq-work>
```

---

```thinking
我需要解决用户提出的时间计算问题。问题是当用户输入2025-04-18 09:00到2025-04-18 20:00这段时间范围时，系统目前返回的是1天，但用户期望的是1+2/8天，即1.25天。

我已经看了datediff.js文件，其中包含了时间计算的逻辑。问题出在getOneDayTime函数中，当时间范围超出正常工作时间（09:00-18:00，中间有1小时午休）时，原本的逻辑是直接返回一整天的工时（8小时），而不是根据实际超出的时间来计算。

我已经修改了这个函数，现在它会根据不同情况正确计算工作时间：
1. 完全在工作时间内的情况
2. 开始时间在工作时间前，结束时间在工作时间内的情况
3. 开始时间在工作时间内，结束时间在工作时间后的情况
4. 完全超出工作时间的情况

对于用户提到的例子（09:00-20:00），属于第3种情况，应该计算的是：
- 正常工作时间（09:00-18:00，除去1小时午休）= 8小时
- 额外工作时间（18:00-20:00）= 2小时
- 总计 = 8+2 = 10小时

然后系统会将这个时间转换为天数，基准是8小时/天，所以应该是10/8 = 1.25天，也就是用户期望的1+2/8天。

我通过编辑datediff.js文件，完全重写了getOneDayTime函数的逻辑，使其能够正确处理所有时间范围情况。现在，当用户输入2025-04-18 09:00到2025-04-18 20:00时，系统应该会返回1.25天。
```

我已经修复了加班时间计算的问题。之前的计算方式存在缺陷，当加班时间超出正常工作时间（09:00-18:00）时，系统会直接按一天计算，而非根据实际超出的时间来计算。

对于您提到的2025-04-18 09:00到2025-04-18 20:00这段时间，应该是：
- 正常工作时间（09:00-18:00，扣除1小时午休）= 8小时
- 额外加班时间（18:00-20:00）= 2小时
- 总计 = 10小时 = 1.25天（以8小时/天计算）

我重写了时间计算逻辑，现在系统会正确处理以下所有情况：
1. 完全在工作时间内的加班
2. 早于上班时间开始的加班
3. 晚于下班时间结束的加班
4. 跨越整个工作时间的加班

这样无论是标准工作时间内的加班还是超出工作时间的加班，都能够准确计算加班时长。您再次提交09:00-20:00的加班申请时，系统应该会显示1.25天（即1+2/8天）。

---



---

