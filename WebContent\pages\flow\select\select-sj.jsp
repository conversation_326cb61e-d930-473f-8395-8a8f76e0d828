<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择商机</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selecSjForm">
       	   <input name="sjDataType" value="2" type="hidden"/>
       	   <input name="custId" value="${param.sjCustId}" type="hidden"/>
       	   <input name="platformId" value="${param.sjPlatformId}" type="hidden"/>
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
					 <span class="input-group-addon">商机名称</span>	
					 <input type="text" name="name" class="form-control input-sm">
			     </div>
	   			 <div class="input-group input-group-sm ml-5" style="width: 160px">
					 <span class="input-group-addon">客户名称</span>	
					 <input type="text" name="custName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectSj.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
    			 <button type="button" class="btn btn-sm btn-info pull-right mr-20" onclick="SelectSj.addSj()">+新增商机</button>
	   		</div>
            	<div class="ibox">
              		<table id="selectSjList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectSj.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectSj={
					sid:'${param.sid}',
					query : function(){
						$("#selecSjForm").queryData();
					},
					initTable : function(){
						$("#selecSjForm").initTable({
							mars:'CustDao.selectSj',
							id:'selectSjList',
							page:true,
							limit:20,
							height:'400px',
							rowEvent:'rowEvent',
							rowDoubleEvent:'SelectSj.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'BUSINESS_NAME',
								title: '商机'
							},{
							    field: 'CUSTOMER_NAME',
								title: '客户'
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectSj.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('selectSjList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['BUSINESS_NAME']);
									ids.push(data[index]['BUSINESS_ID']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								
								// 增加客户、销售、平台信息回填功能
								var refCustId = el.data('refCustId');
								var refCustName = el.data('refCustName');
								if(refCustId && refCustName){
									$("[name='"+refCustId+"']").val(data[0]['CUST_ID']);
									$("[name='"+refCustName+"']").val(data[0]['CUSTOMER_NAME']); 
								}
								var refSaleId = el.data('refSaleId');
								var refSaleName = el.data('refSaleName');
								if(refSaleId && refSaleName){
									$("[name='"+refSaleId+"']").val(selectRow['SALES_BY']);
									$("[name='"+refSaleName+"']").val(selectRow['SALES_BY_NAME']);
								}
								var refPlatformId = el.data('refPlatformId');
								var refPlatformName = el.data('refPlatformName');
								if(refPlatformId && refPlatformName){
									$("[name='"+refPlatformId+"']").val(selectRow['PLATFORM_ID']);
									$("[name='"+refPlatformName+"']").val(selectRow['PLATFORM_NAME']);
								}
								popup.layerClose("selecSjForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							el.val(selectRow['BUSINESS_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['BUSINESS_ID']);
							}
							
							// 增加客户、销售、平台信息回填功能
							var refCustId = el.data('refCustId');
							var refCustName = el.data('refCustName');
							if(refCustId && refCustName){
								$("[name='"+refCustId+"']").val(selectRow['CUST_ID']);
								$("[name='"+refCustName+"']").val(selectRow['CUSTOMER_NAME']);
							}
							var refSaleId = el.data('refSaleId');
							var refSaleName = el.data('refSaleName');
							if(refSaleId && refSaleName){
								$("[name='"+refSaleId+"']").val(selectRow['SALES_BY']);
								$("[name='"+refSaleName+"']").val(selectRow['SALES_BY_NAME']);
							}
							var refPlatformId = el.data('refPlatformId');
							var refPlatformName = el.data('refPlatformName');
							if(refPlatformId && refPlatformName){
								$("[name='"+refPlatformId+"']").val(selectRow['PLATFORM_ID']);
								$("[name='"+refPlatformName+"']").val(selectRow['PLATFORM_NAME']);
							}
							
							popup.layerClose("selecSjForm");
						}
					},
					addSj:function(){
						popup.openTab({id:'shangji_mgr',url:'/yq-work/pages/crm/shangji/sj-list.jsp',title:'商机管理'});
					}
					
			};
			$(function(){
				SelectSj.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>