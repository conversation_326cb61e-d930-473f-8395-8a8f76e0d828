package com.yunqu.work.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;

@WebObject(name="FileDao")
public class FileDao extends AppDaoContext {
	
	@WebControl(name="fileList",type=Types.TEMPLATE)
	public JSONObject fileList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_files where fk_id = ?";
			return queryForList(sql, new Object[]{fkId});
		}
		return getJsonResult(new JSONArray());
	}
	@WebControl(name="fileListDetail",type=Types.TEMPLATE)
	public JSONObject fileListDetail(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select  t1.*,t2.USERNAME from yq_files t1,"+Constants.DS_MAIN_NAME+".easi_user t2 where t1.creator=t2.USER_ID and t1.fk_id = ?";
			return queryForList(sql, new Object[]{fkId});
		}
		return getJsonResult(new JSONArray());
	}

}
