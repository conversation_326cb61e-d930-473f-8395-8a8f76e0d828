package com.yunqu.work.service;

import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.ai.Bigmodel;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.NumberToChinese;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;
import com.yunqu.work.utils.WeekUtils;

public class WeeklyNoticeService extends AppBaseService implements Job{

	private static class Holder{
		private static WeeklyNoticeService service=new WeeklyNoticeService();
	}
	public static WeeklyNoticeService getService(){
		return Holder.service;
	}
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		 this.run(WeekUtils.getWeekNo());
		 
	}
	
	public  void run(int weekNo){
		//待办提醒
		getLogger().info("执行周报提醒任务....");
		try {
			int year=WeekUtils.getYear();
			List<JSONObject> list = this.getQuery().queryForList("SELECT USER_ID,DEPTS FROM "+Constants.DS_MAIN_NAME+".easi_user WHERE USER_ID NOT IN ( SELECT t1.USER_ID FROM "+Constants.DS_MAIN_NAME+".easi_user t1, yq_weekly t2 WHERE t1.USER_ID = t2.creator AND t2.week_no = ? and t2.year = ? ) AND STATE = 0 ",new Object[]{weekNo,year},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("USER_ID");
					String depts = jsonObject.getString("DEPTS");
					if(StringUtils.notBlank(depts)) {
						if(depts.contains("开发")||depts.contains("创新")||depts.contains("分部")||depts.contains("工程")||depts.contains("交互")||depts.contains("研究")||depts.contains("组")) {
							MessageModel model=new MessageModel();
							model.setReceiver(creator);
							model.setSender(creator);
							model.setTitle(WeekUtils.getWeekTitle(year,weekNo)+" 周报"+"\n");
							WxMsgService.getService().sendWeeklyNoticeMsg(model);
						}
					}
				}
			}
		} catch (SQLException e2) {
			this.getLogger().error(e2);
		}
	}
	
	//月报提醒
	public  void workhourNotice(String monthId,String userId){
		getLogger().info("执行项目工时上报待填写任务....");
		try {
			if(StringUtils.isBlank(monthId)) {
				monthId = EasyCalendar.newInstance().getFullMonth();
			}
			EasySQL sql = new EasySQL("select t1.user_id from yq_project_wh t1 where t1.state=0 and t1.user_id in(select t2.USER_ID from "+Constants.DS_MAIN_NAME+".easi_user t2 where t2.STATE = 0)");
			sql.append(monthId,"and t1.month_id = ?");
			if(StringUtils.isNotBlank(userId)) {
				sql.append(userId,"and t1.user_id = ?");
			}
			
			try {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");  
				Date date = sdf.parse(monthId);
				monthId = new SimpleDateFormat("yyyy年MM月").format(date);
			} catch (ParseException e) {
				this.getLogger().error(e);
			}
			
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator = jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(creator);
					model.setSender(creator);
					model.setData1(monthId);
					model.setTitle("项目工时上报");
					WxMsgService.getService().sendWhNoticeMsg(model);
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
	}
	
	public void noticeTodo(String sql){
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql,new Object[] {},new JSONMapperImpl());
			for(JSONObject object :list) {
				String creator=object.getString("CREATOR");
				MessageModel model=new MessageModel();
				model.setReceiver(creator);
				model.setSender(creator);
				model.setTitle("您有个日程安排即将开始");
				model.setData1(object.getString("TITLE"));
				model.setData3(object.getString("BEGIN_TIME")+" ~ "+object.getString("END_TIME"));
				model.setDesc(WxMsgService.delHTMLTag(object.getString("TODO_DESC")));
				WxMsgService.getService().sendScheduleNoticeMsg(model);
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
	}
	
	public void noticeDeptWork() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT dept_id,dept_name,count(1) count from yq_weekly where 1=1 ");
		sql.append(DateUtils.getPlanMaxDay(-90),"and week_begin >= ? GROUP BY dept_id");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			for(JSONObject row:list) {
				String deptName  = row.getString("DEPT_NAME");
				String deptId  = row.getString("DEPT_ID");
				if(StringUtils.notBlank(deptName)&&(deptName.contains("开发")||deptName.contains("创新")||deptName.contains("分部")||deptName.contains("组"))) {
					EasySQL deptSql = new EasySQL();
					deptSql.append("SELECT t2.project_name,sum(work_day) work_day,count(DISTINCT(t1.creator)) people_count from yq_weekly t1,yq_weekly_project t2 where t1.weekly_id = t2.weekly_id");
					deptSql.append(deptId,"and t1.dept_id = ? ");
					deptSql.append(DateUtils.getPlanMaxDay(-90),"and t1.week_begin > ?");
					deptSql.append(" and work_day > 1 GROUP BY t2.project_id ORDER BY work_day desc");
					List<JSONObject> deptProject =  this.getQuery().queryForList(deptSql.getSQL(), deptSql.getParams(),new JSONMapperImpl());
					StringBuffer sb = new StringBuffer();
					int i = 0;
					for(JSONObject project:deptProject) {
						i++;
						sb.append(i+"."+project.getString("PROJECT_NAME")+",投入"+project.getString("WORK_DAY")+"/人/天,参与"+project.getString("PEOPLE_COUNT")+"人\n");
					}
					if(i>5) {
						WeChatWebhookSender.sendMarkdownMessage("dept@"+deptId, deptName+"-最近3个月人力投入情况", sb.toString(),true);
					}
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
	}

	
	public void createProjectWorkHour() {
		createProjectWorkHour(EasyCalendar.newInstance().getFullMonth(),null);
	}
	
	public void createProjectWorkHour(String monthId,String deptName) {
		try {
			EasySQL sql = new EasySQL("SELECT t2.USER_ID,t1.DEPT_ID,t2.USERNAME,t2.DEPTS from easi_user t2,easi_dept_user t1 where  t2.state = 0 and t1.USER_ID = t2.USER_ID");
			if(StringUtils.isBlank(deptName)) {
				sql.append("and (t2.depts like '开发%'  or t2.depts like '创新%'  or t2.depts like '%工程%'  or t2.depts like '%分部' or t2.depts like '%智能交互部' or t2.depts like '%交付%')");
			}else {
				sql.appendLike(deptName,"and t2.depts like ?");
			}
			List<JSONObject> list = this.getMainQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject obj:list) {
					String userId = obj.getString("USER_ID");
					String username = obj.getString("USERNAME");
					String deptId = obj.getString("DEPT_ID");
					String _deptName = obj.getString("DEPTS");
					
					EasyRecord record = new EasyRecord("yq_project_wh","id");
					record.set("id",monthId+"_"+userId);
					record.set("month_id",monthId );
					record.set("user_id", userId);
					record.set("user_name", username);
					record.set("dept_id", deptId);
					record.set("dept_name", _deptName);
					boolean bl = this.getQuery().update(record);
					if(!bl) {
						record.set("state", 0);
						record.set("create_time", EasyDate.getCurrentDateString());
						this.getQuery().save(record);
					}
				}
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
	}
	
	public void sendDeptWeeklyToLeader() {
		sendDeptWeeklyToLeader(WeekUtils.getWeekNo());
	}
	
	public void sendDeptWeeklyToLeader(int weekNo) {
		this.sendDeptWeeklyToLeader(weekNo,false);
	}
	
	public void sendDeptWeeklyToLeader(int weekNo,boolean isWriteNotice) {
		getLogger().info("执行周报汇报提醒....");
		try {
			int year = WeekUtils.getYear();
			List<JSONObject> list = this.getQuery().queryForList("SELECT DEPT_ID,count(1) count from yq_weekly where `year`= ? and week_no = ? and status > 0 GROUP BY dept_id",new Object[]{year,weekNo},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					int weeklyWriteCount = jsonObject.getIntValue("COUNT");
					String deptId = jsonObject.getString("DEPT_ID");
					this.sendDeptWeeklyToLeader(weekNo,deptId,weeklyWriteCount,isWriteNotice);
				}
			}
		} catch (SQLException ex) {
			this.getLogger().error(ex);
		}
	}
	
	public void sendDeptWeeklyToLeader(int weekNo,String deptId) throws SQLException {
		sendDeptWeeklyToLeader(weekNo,deptId,false);
	}
	
	public void sendDeptWeeklyToLeader(int weekNo,String deptId,boolean isWriteNotice) throws SQLException {
		if(StringUtils.isBlank(deptId)) {
			return;
		}
	   int year = WeekUtils.getYear();
	   int weeklyWriteCount = this.getQuery().queryForInt("select count(1) from yq_weekly where status > 0 and YEAR = ? and week_no = ? and dept_id = ?",year,weekNo,deptId);
	   this.sendDeptWeeklyToLeader(weekNo,deptId,weeklyWriteCount,isWriteNotice);
	}
	
	/**
	 * 部门周报
	 * @param weekNo
	 * @param deptId
	 * @param weeklyWriteCount
	 * @throws SQLException
	 */
	public void sendDeptWeeklyToLeader(int weekNo,String deptId,int weeklyWriteCount,boolean isWriteNotice) throws SQLException {
		if(weeklyWriteCount==0) {
			this.getLogger().info("sendDeptWeeklyToLeader>weeklyWriteCount is zero.weekNo>"+weekNo);
			return;
		}
		String deptName =  this.getMainQuery().queryForString("select t1.dept_name from easi_dept t1 where t1.dept_id = ?",new Object[]{deptId});
		
		StringBuffer affairDesc = new StringBuffer();
		StringBuffer deptWxMsg = new StringBuffer();
		int notWriteCount = 0;
		int year = WeekUtils.getYear();
		String notWritePerson = this.getQuery().queryForString("SELECT GROUP_CONCAT(t1.username) from yq_main.easi_user t1,yq_main.easi_dept_user t3 where t3.user_id = t1.user_id and t3.dept_id = ? and t1.state = 0 and t1.user_id not in(select t2.creator from yq_weekly t2 where t2.status > 0 and t2.YEAR = ? and t2.week_no = ? and t3.dept_id = t2.dept_id)",deptId,year,weekNo);
		if(StringUtils.notBlank(notWritePerson)){
			String[] notWriteArray = notWritePerson.split(",");
			notWriteCount  = notWriteArray.length;
			if(notWriteCount>1) {
				if(StringUtils.notBlank(deptName)&&!deptName.contains("工程")) {
					WeChatWebhookSender.sendMarkdownMessage("dept@"+deptId, WeekUtils.getWeekTitle(year,weekNo)+"-未提交周报名单", notWritePerson,true);
				}
				WeChatWebhookSender.sendMarkdownMessage(WebhookKey.TEST, WeekUtils.getWeekTitle(year,weekNo)+"-未提交周报名单", notWritePerson,true);
			}
		}
		if(isWriteNotice) {
			return;
		}
		
		int deptPerson = notWriteCount + weeklyWriteCount;
		if(notWriteCount==0||weeklyWriteCount==0) {
			return;
		}
		
		if(weeklyWriteCount<3){
			return;
		}
		
		affairDesc.append("<p><b style='font-size:16px;'>"+WeekUtils.getWeekTitle(year,weekNo)+"-部门周报数据汇总</b></p>");
		affairDesc.append("<br>");
		
		affairDesc.append("<p style='font-size:14px;'>部门人数："+deptPerson+"，已写周报："+weeklyWriteCount+"，未提交周报："+notWriteCount+"人。</p>");
		if(notWriteCount>1) {
			affairDesc.append("<p style='color:red;font-size:14px;'>未提交周报名单："+notWritePerson+"</p>");
		}
		
		affairDesc.append("<br>");
		
		List<JSONObject> list = this.getQuery().queryForList("SELECT t3.project_id,t3.project_name,t3.po_name,t3.project_po_name,t3.cy_date,t3.zy_date,t3.project_stage,sum(t2.work_day) work_day,GROUP_CONCAT(t1.create_name) all_user,count(1) people_count,GROUP_CONCAT(t2.work_content SEPARATOR '<br>') work_content from yq_weekly t1,yq_weekly_project t2,yq_project t3 where t1.weekly_id = t2.weekly_id and t2.project_id = t3.project_id and t1.dept_id = ? and t1.year = ? and t1.week_no = ? GROUP BY t2.project_id order by work_day desc", new Object[] {deptId,year,weekNo},new JSONMapperImpl());
		int i = 1;
		for(JSONObject row:list) {
			String content = row.getString("WORK_CONTENT");
			String projectId = row.getString("PROJECT_ID");
			if(StringUtils.isBlank(projectId)||StringUtils.isBlank(content)) {
				continue;
			}
			affairDesc.append("<p><b style='font-size:16px;'>"+i+"."+row.getString("PROJECT_NAME")+"</b></p>");
			
			if(!"9999999999999".equals(projectId)) {
				String projectStage = row.getString("PROJECT_STAGE");
				affairDesc.append("<p>项目经理："+emptyStr(row.getString("PROJECT_PO_NAME")));
				affairDesc.append("，开发负责人："+emptyStr(row.getString("PO_NAME")));
				affairDesc.append("，当前项目阶段："+emptyStr(projectStage));
				
				if(StringUtils.notBlank(projectStage)&&!projectStage.contains("已终验")) {
					String cyDate = row.getString("CY_DATE");
					String zyDate = row.getString("ZY_DATE");
					if(StringUtils.notBlank(cyDate)&&!cyDate.contains("分成")) {
						affairDesc.append("，计划初验日期："+emptyStr(cyDate));
					}
					if(StringUtils.notBlank(zyDate)&&!zyDate.contains("分成")) {
						affairDesc.append("，计划终验日期："+emptyStr(zyDate)+";");
					}
				}
				affairDesc.append("</p>");
			}
			
			affairDesc.append("<p>本周投入人数："+row.getString("PEOPLE_COUNT"));
			affairDesc.append("，参与人："+row.getString("ALL_USER"));
			affairDesc.append("，投入工时："+row.getString("WORK_DAY")+"/人/天;");
			affairDesc.append("</p>");
			affairDesc.append("<p>工作内容：</p>");
			
			affairDesc.append("<div>");
			
			content = content.replaceAll("\n", "<br>");
			affairDesc.append(content);
			affairDesc.append("</div>");
			affairDesc.append("<br>");
			
			deptWxMsg.append("\n"+i+"."+row.getString("PROJECT_NAME"));
			deptWxMsg.append("\n参与人："+row.getString("ALL_USER")+",投入工时："+row.getString("WORK_DAY")+"/人/天");
			
			i++;
		}
		
		StringBuffer deptWxMsgSb = new StringBuffer();
		String deptWxResult = Bigmodel.request("总结部门周报进度、风险和下一步工作事项，字数控制在300字以内。请包含以下内容：项目进展：当前任务完成情况，是否按计划进行，有无重要里程碑。存在的风险或问题：是否有延误、资源不足或其他影响进度的问题。下周工作计划：明确下一步要做的主要任务，是否需要额外资源支持。", affairDesc.toString());
		if(deptWxResult!=null) {
			deptWxMsgSb.append("AI大模型总结本周部门工作\n");
			String content = Jsoup.clean(deptWxResult, Whitelist.none());
			content = content.replaceAll("  ","\n");
			content = content.replaceAll(" ","\n");
			deptWxMsgSb.append(content);
		}
	
		if(StringUtils.notBlank(deptWxMsg.toString())&&StringUtils.notBlank(deptName)&&!deptName.contains("工程")) {
			WeChatWebhookSender.sendTextMessage("dept@"+deptId, WeekUtils.getWeekTitle(year,weekNo)+"-部门项目"+deptWxMsg.toString());
		}
		
		if(StringUtils.notBlank(deptWxMsgSb.toString())&&StringUtils.notBlank(deptName)&&deptWxMsgSb.toString().length()>30&&!deptName.contains("工程")) {
			WeChatWebhookSender.sendTextMessage("dept@"+deptId, deptWxMsgSb.toString());
		}
		
		
		String aiResult = Bigmodel.request("请列出部门项目风险和突出的问题以及下一步要做的事,不能超过1000字", affairDesc.toString());
		if(aiResult!=null) {
			affairDesc.append("<hr><h2>AI大模型总结</h2>");
			affairDesc.append("<div class=\"ai-content\">"+aiResult+"</div>");
		}
		
		this.getLogger().info("weekNo>"+weekNo+">deptId>"+deptId+">"+affairDesc.toString());
		
		if(StringUtils.notBlank(deptId)) {
			String deptLeaderUserId = StaffService.getService().getLeader(deptId);
			AffairService.getService().addDeptWeeklyAffair("DeepSeek汇总："+year+"年第"+weekNo+"周-部门周报", affairDesc.toString(), deptLeaderUserId);
		}
	}
	
	@Deprecated
	public void sendAllWeeklyToLeader(int weekNo){
		StringBuffer affairDesc = new StringBuffer();
		StringBuffer deptWxMsg = new StringBuffer();
		int year = WeekUtils.getYear();
		StringBuffer allContent = new StringBuffer();
		
		try {
			List<JSONObject> list = this.getQuery().queryForList("SELECT t3.project_name,t3.po_name,t3.project_po_name,t3.cy_date,t3.zy_date,t3.project_stage,sum(t2.work_day) work_day,GROUP_CONCAT(t1.create_name) all_user,count(1) people_count,GROUP_CONCAT(t2.work_content SEPARATOR '<br>') work_content from yq_weekly t1,yq_weekly_project t2,yq_project t3 where t1.weekly_id = t2.weekly_id and t2.project_id = t3.project_id and t1.year = ? and t1.week_no = ? GROUP BY t2.project_id order by work_day desc", new Object[] {year,weekNo},new JSONMapperImpl());
			int i = 1;
			for(JSONObject row:list) {
				String projectName = row.getString("PROJECT_NAME");
				String content = row.getString("WORK_CONTENT");
				if(StringUtils.isBlank(content)||"其他".equals(projectName)) {
					continue;
				}
				affairDesc.append("<p><b style='font-size:16px;'>"+i+"."+projectName+"</b></p>");
				affairDesc.append("<p>项目经理："+emptyStr(row.getString("PROJECT_PO_NAME")));
				affairDesc.append("，开发负责人："+emptyStr(row.getString("PO_NAME")));
				affairDesc.append("，当前项目阶段："+emptyStr(row.getString("PROJECT_STAGE")));
				
				String cyDate = row.getString("CY_DATE");
				String zyDate = row.getString("ZY_DATE");
				if(StringUtils.notBlank(cyDate)) {
					affairDesc.append("，计划初验日期："+emptyStr(cyDate));
				}
				if(StringUtils.notBlank(zyDate)) {
					affairDesc.append("，计划终验日期："+emptyStr(zyDate)+";");
				}
				
				affairDesc.append("</p>");
				affairDesc.append("<p>本周投入人数："+row.getString("PEOPLE_COUNT"));
				affairDesc.append("，参与人："+row.getString("ALL_USER"));
				affairDesc.append("，投入工时："+row.getString("WORK_DAY")+"/人/天;");
				affairDesc.append("</p>");
				affairDesc.append("<p>工作内容：</p>");
				
				affairDesc.append("<div>");
				
				content = content.replaceAll("\n", "<br>");
				affairDesc.append(content);
				affairDesc.append("</div>");
				affairDesc.append("<br>");
				
				deptWxMsg.append("\n"+i+"."+projectName);
				deptWxMsg.append("\n参与人："+row.getString("ALL_USER")+",投入工时："+row.getString("WORK_DAY")+"/人/天");
				
				i++;
				
				if(i>30) {
					break;
				}
			}
			
			StringBuffer companyWxMsgSb = new StringBuffer();
			String companyWxResult = Bigmodel.request("总结公司项目进度、风险和下一步工作事项，字数控制在300字以内。请包含以下内容：项目进展：当前任务完成情况，是否按计划进行，有无重要里程碑。存在的风险或问题：是否有延误、资源不足或其他影响进度的问题。下周工作计划：明确下一步要做的主要任务，是否需要额外资源支持。", affairDesc.toString());
			if(companyWxResult!=null) {
				companyWxMsgSb.append("AI大模型总结本周公司项目进展\n");
				String content = Jsoup.clean(companyWxResult, Whitelist.none());
				content = content.replaceAll("  ","\n");
				content = content.replaceAll(" ","\n");
				companyWxMsgSb.append(content);
			}
			
//			if(StringUtils.notBlank(deptWxMsg.toString())) {
//				WeChatWebhookSender.sendTextMessage(WebhookKey.TOP, WeekUtils.getWeekTitle(year,weekNo)+"-全部项目"+deptWxMsg.toString());
//			}
			
			if(StringUtils.notBlank(companyWxMsgSb.toString())&&companyWxMsgSb.toString().length()>30) {
				WeChatWebhookSender.sendTextMessage(WebhookKey.TOP, companyWxMsgSb.toString());
			}
			
			
			String aiResult = Bigmodel.request("请预测项目风险,是否按计划进行，有无重要里程碑,是否有延误、资源不足或其他影响进度的问题,下一步要做的主要任务，是否需要额外资源支持。字数控制在800字以内", affairDesc.toString());
			if(aiResult!=null) {
				allContent.append("<p><b style='font-size:16px;'>AI大模型项目风险预判</b></p>");
				allContent.append("<div>"+aiResult+"</div>");
				allContent.append("<br><br>");
			}
			
			allContent.append(affairDesc.toString());
			
			this.getLogger().info("weekNo>"+weekNo+">comapny>"+allContent.toString());
			
			if(i>0) {
				String creator = "陈小丰";//"张明东";
				creator = StaffService.getService().getUserId(creator);
				String receiver = "陈小丰";//"邓从健,张志青,许志远,汤湛成";
				receiver = StaffService.getService().getUserId(receiver);
				String ccIds = "陈小丰";//"朱栩,陈茂强";
				ccIds = StaffService.getService().getUserId(ccIds);
				AffairService.getService().addAffair("AI大模型："+year+"年第"+weekNo+"周-YQ项目周报", allContent.toString(),creator,receiver,ccIds);
			}
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
	}
	
	
	/**
	 * 项目周报
	 * @param weekNo
	 */
	public void sendProjectWeeklyToPm(int weekNo) {
		getLogger().info("执行项目周报汇报提醒....");
		try {
			int year = WeekUtils.getYear();
			List<JSONObject> list = this.getQuery().queryForList("SELECT t2.project_id from yq_weekly t1,yq_weekly_project t2 where t1.weekly_id = t2.weekly_id and t1.`year`= ? and t1.week_no = ?  and t1.status > 0 GROUP BY t2.project_id",new Object[]{year,weekNo},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String projectId = jsonObject.getString("PROJECT_ID");
					this.sendProjectWeeklyToPm(weekNo,projectId);
				}
			}
		} catch (SQLException ex) {
			this.getLogger().error(ex);
		}
	}
	
	 /**
	  * 项目周报
	 * @param weekNo
	 * @param projectId
	 * @throws SQLException
	 */
	public void sendProjectWeeklyToPm(int weekNo,String projectId) throws SQLException {
		if("9999999999999".equals(projectId)) {
			return;
		}
		StringBuffer affairDesc = new StringBuffer();
		int year = WeekUtils.getYear();
		JSONObject projectInfo = this.getQuery().queryForRow("SELECT t3.project_name,t3.po_name,t3.project_po,t3.po,t3.pre_sales_name,t3.pre_sales_by,t3.sales_by_name,t3.sales_by,t3.project_po_name,t3.cy_date,t3.zy_date,t3.project_stage,t3.workhour_day,t3.work_people_count from yq_project t3 where t3.project_id = ?", new Object[] {projectId},new JSONMapperImpl());
		if(projectInfo==null||projectInfo.isEmpty()) {
			return;
		}
		JSONObject weekyInfo = this.getQuery().queryForRow("SELECT sum(t2.work_day) work_day,GROUP_CONCAT(t1.create_name) all_user,count(1) people_count from yq_weekly t1,yq_weekly_project t2 where t1.weekly_id = t2.weekly_id and t2.project_id = ? and t1.year = ? and t1.week_no = ?", new Object[] {projectId,year,weekNo},new JSONMapperImpl());
		String workDay = weekyInfo.getString("WORK_DAY");
		if(StringUtils.isBlank(workDay)) {
			return;
		}
		String projectName = projectInfo.getString("PROJECT_NAME");
		
		if(weekyInfo==null||weekyInfo.isEmpty()) {
			this.getLogger().info("sendProjectWeeklyToPm>data is zero.weekNo>"+weekNo+">"+projectId);
			return;
		}
		
		affairDesc.append("<p><b>"+WeekUtils.getWeekTitle(year,weekNo)+"【"+projectName+"】项目周报数据汇总</b></p>");
		affairDesc.append("<br>");

		String projectStage = projectInfo.getString("PROJECT_STAGE");
		
		affairDesc.append("<p>项目经理："+emptyStr(projectInfo.getString("PROJECT_PO_NAME")));
		affairDesc.append("，开发负责人："+emptyStr(projectInfo.getString("PO_NAME")));
		affairDesc.append("，销售："+emptyStr(projectInfo.getString("SALES_BY_NAME")));
		affairDesc.append("，售前："+emptyStr(projectInfo.getString("PRE_SALES_NAME")));
		affairDesc.append("，当前项目阶段："+emptyStr(projectStage));
		
		if(StringUtils.notBlank(projectStage)&&!projectStage.contains("已终验")&&!projectStage.contains("合作")) {
			String cyDate = projectInfo.getString("CY_DATE");
			String zyDate = projectInfo.getString("ZY_DATE");
			if(StringUtils.notBlank(cyDate)) {
				affairDesc.append("，计划初验日期："+emptyStr(cyDate));
			}
			if(StringUtils.notBlank(zyDate)) {
				affairDesc.append("，计划终验日期："+emptyStr(zyDate));
			}
		}
		affairDesc.append("</p>");
		affairDesc.append("<p>本周投入人数："+weekyInfo.getString("PEOPLE_COUNT"));
		affairDesc.append("，参与人："+weekyInfo.getString("ALL_USER"));
		affairDesc.append("，本周投入工时："+weekyInfo.getString("WORK_DAY")+"/人/天");
		affairDesc.append("，已投入所有工时："+emptyStr(projectInfo.getString("WORKHOUR_DAY"))+"/人/天");
		affairDesc.append("，已投入所有人数："+emptyStr(projectInfo.getString("WORK_PEOPLE_COUNT"))+"人");
		affairDesc.append("</p>");
		
		affairDesc.append("<br>");
		
		List<JSONObject> list = this.getQuery().queryForList("SELECT t2.WORK_CONTENT,t2.PLAN_WORK_CONTENT,t2.WORK_DAY,t1.DEPT_NAME,t1.CREATE_NAME from yq_weekly t1,yq_weekly_project t2 where t1.weekly_id = t2.weekly_id and t2.project_id = ? and t1.year = ? and t1.week_no = ? order by t2.work_day desc", new Object[] {projectId,year,weekNo},new JSONMapperImpl());
		int i = 1;
		affairDesc.append("<div>");
		affairDesc.append("<p><b>本周工作内容</b></p>");
		for(JSONObject row:list) {
			String content = row.getString("WORK_CONTENT");
			if(StringUtils.notBlank(content)&&content.length()>5) {
				String userStr = row.getString("DEPT_NAME");
				affairDesc.append("<p><b>"+NumberToChinese.convertToChinese(i)+"、"+userStr+"</b>("+row.getString("WORK_DAY")+"/天)</p>");
				content = content.replaceAll("\n", "<br>");
				affairDesc.append(content);
				affairDesc.append("<br>");
				i++;
			}
		}
		affairDesc.append("</div>");
		affairDesc.append("<hr>");
		
		int y = 1;
		affairDesc.append("<div>");
		affairDesc.append("<p><b>下周工作计划</b></p>");
		boolean hasNextContent = false;
		for(JSONObject row:list) {
			String nextContent = row.getString("PLAN_WORK_CONTENT");
			if(StringUtils.notBlank(nextContent)&&nextContent.length()>5) {
				hasNextContent = true;
				String userStr = row.getString("DEPT_NAME");
				affairDesc.append("<p><b>"+NumberToChinese.convertToChinese(y)+"、"+userStr+"</b></p>");
				nextContent = nextContent.replaceAll("\n", "<br>");
				affairDesc.append(nextContent);
				affairDesc.append("<br>");
				y++;
			}
		}
		if(!hasNextContent) {
			affairDesc.append("<p>无</p>");
		}
		affairDesc.append("</div>");
		affairDesc.append("<br>");
		
		String aiResult = Bigmodel.request("请列出当前项目中存在的风险和突出的主要问题，简要说明这些风险和问题对项目进度或结果的潜在影响。之后，预测下一步的工作重点，包括需要优先解决的事项、资源需求以及任何可能影响项目推进的关键因素。字数不超过800字", affairDesc.toString());
		if(aiResult!=null) {
			affairDesc.append("<p><b style='font-size:20px;'>AI大模型总结</b></p>");
			affairDesc.append("<div class=\"ai-content\">"+aiResult+"</div>");
		}
		
		this.getLogger().info("weekNo>"+weekNo+">projectId>"+projectId+">"+affairDesc.toString());
		
		String projectPm = projectInfo.getString("PROJECT_PO");
		
		if(StringUtils.notBlank(projectPm)&&y>2) {
			StaffModel staffModel = StaffService.getService().getStaffInfo(projectPm);
			if(staffModel!=null&&staffModel.isNormal()) {
				String deptLeaderUserId = staffModel.getDeptLeader();
				String receiver = projectInfo.getString("PO")+","+projectInfo.getString("SALES_BY");
				String ccIds = deptLeaderUserId;
				AffairService.getService().addAffair("DeepSeek汇总："+year+"年第"+weekNo+"周【"+projectName+"】项目周报", affairDesc.toString(), projectPm,receiver,ccIds);
			}else {
				this.getLogger().error(projectName+">projectPm>"+projectPm+" is leave");
			}
		}else {
			this.getLogger().error(projectName+">projectPm is empty,y:"+y);
		}
	}
	
	private String emptyStr(String str) {
		if(StringUtils.isBlank(str)) {
			return "--";
		}
		return str;
	}
}
