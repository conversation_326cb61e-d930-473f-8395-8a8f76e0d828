package com.yunqu.work.servlet.flow;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.WxMsgService;

@WebServlet("/servlet/flow/approve/*")
public class ApproveServlet extends BaseFlowServlet {
	private static final long serialVersionUID = 1L;

	public EasyResult actionForDoApprove() {
		String msg = "操作成功";
		try {
			JSONObject params = getJSONObject();
			String flowCode = params.getString("flowCode");
			String businessId = params.getString("businessId");
			String resultId = params.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("审批人不存在");
			}
			
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			
			//获取下一级节点
			ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
			String nodeId = approveNode.getNodeId();
			String nextNodeId = approveNode.getNextNodeId();
			String _nextNodeId = params.getString("nextNodeId");
			String _nextNodeCode = params.getString("nextNodeCode");
			
			if(StringUtils.isNotBlank(_nextNodeCode)) {
				nextNodeId = ApproveService.getService().getNodeInfo(flowCode, _nextNodeCode, getUserId()).getNodeId();
			}else if(StringUtils.isNotBlank(_nextNodeId)) {
				nextNodeId = _nextNodeId;
			}
			
			FlowModel flow = ApproveService.getService().getFlow(flowCode);
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);

			int checkResult = params.getIntValue("checkResult");//1 通过 2拒绝
			String checkDesc = params.getString("checkDesc");
			EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("business_id", businessId);
			record.set("check_user_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc", checkDesc);
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			
			MessageModel msgModel = new MessageModel();
			msgModel.setTypeName(flow.getFlowCode());
			msgModel.setSender(getUserId());
			msgModel.setSendName(getUserName());
			msgModel.setFkId(businessId);
			msgModel.setMsgLabel("待审批");
			msgModel.setTitle(apply.getApplyTitle());
			msgModel.setData1(flow.getFlowName());
			msgModel.setData2(apply.getApplyName());
			msgModel.setData3(apply.getApplyTime());
			msgModel.setData4("待审批");
			msgModel.setDesc(apply.getApplyRemark());
			
			if(checkResult==2) {
				this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ?,last_result_time = ? where apply_id = ?",21,EasyDate.getCurrentDateString(),businessId);
				msgModel.setData4("审批退回");
				msgModel.setMsgLabel("审批退回");
				msgModel.setDesc("审批退回："+checkDesc);
				msgModel.setReceiver(apply.getApplyBy());
				WxMsgService.getService().sendFlowTodoCheck(msgModel);
				MessageService.getService().sendMsg(msgModel);
				EmailService.getService().sendFlowNoticeEmail(msgModel);
				return EasyResult.ok();
			}
			
			
			String nextResultId  = "0";
			int reviewState = 10; // 10 待审批 20审批中 21 拒绝 30审批完成
			if("0".equals(nextNodeId)) {//完成了
				reviewState = 30;
				msg = "审批完成,流程结束";
				msgModel.setMsgLabel("审批完成");
				msgModel.setReceiver(apply.getApplyBy());
				FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
			}else {
				msgModel.setMsgLabel("请尽快审批");
				
				reviewState = 20;
				ApproveNodeModel nextNodeInfo = ApproveService.getService().getNodeInfo(nextNodeId,getUserId(),params);
				
				String todoCheckUserId = nextNodeInfo.getCheckBy();
				if(todoCheckUserId.indexOf(apply.getApplyBy())>-1) {
					 nextNodeId = nextNodeInfo.getNextNodeId();
					 nextNodeInfo = ApproveService.getService().getNodeInfo(nextNodeId,getUserId(),params);
				}
				msgModel.setMsgLabel(nextNodeInfo.getNodeName());
				msgModel.setReceiver(nextNodeInfo.getCheckBy());
				

				EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
				nextResultId = RandomKit.uniqueStr();
				resultRecord.set("result_id", nextResultId);
				resultRecord.set("business_id", businessId);
				resultRecord.set("check_name", nextNodeInfo.getCheckName());
				resultRecord.set("check_user_id", nextNodeInfo.getCheckBy());
				resultRecord.set("check_result", 0);
				resultRecord.set("node_id",nextNodeId);
				resultRecord.set("node_name",nextNodeInfo.getNodeName());
				resultRecord.set("check_desc","");
				resultRecord.set("ip_address",WebKit.getIP(getRequest()));
				resultRecord.set("get_time",EasyDate.getCurrentDateString());
				this.getQuery().save(resultRecord);
				msg = "提交成功,审批下一个节点:"+nextNodeInfo.getNodeName();
				
				FlowService.getService().updateApplyInfo(businessId, resultId,nextResultId,nodeId,nextNodeId, reviewState);
			}
			
			WxMsgService.getService().sendFlowTodoCheck(msgModel);
			MessageService.getService().sendMsg(msgModel);
			EmailService.getService().sendFlowNoticeEmail(msgModel);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,msg);
	}
	
	public EasyResult actionForUpdateApply() {
		JSONObject params = getJSONObject();
		
		String businessId = params.getString("businessId");
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String doAction = params.getString("doAction");
		
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		
		boolean bl = true;
		EasyRecord record = null;
		JSONObject business = JsonKit.getJSONObject(params, "business");
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
		}
		
		EasyRecord applyRecord = null;
		if("submit".equals(doAction)) {
			applyRecord =  getApplyRecord(businessId,flowCode);
		}else {
			applyRecord = getApplyRecord();
		}
		applyRecord.setPrimaryValues(businessId);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.setColumns(applyInfo);
		applyRecord.set("apply_id",businessId);
		if("submit".equals(doAction)) {
			applyRecord.set("apply_state",10);
		}

		try {
			if(record!=null) {
				bl = this.getQuery().save(record);
			}
			if(bl) {
				this.getQuery().update(applyRecord);
			}
			if("submit".equals(doAction)) {
				this.startFlow(flow,businessId,applyState,params);
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
		
	}
	
	/**
	 *提交流程
	 */
	public EasyResult actionForSubmitApply() {
		JSONObject params = getJSONObject();
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String businessId = params.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			businessId =  RandomKit.uniqueStr();
		}
		
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		
		String doAction = params.getString("doAction");
		EasyRecord applyRecord = getApplyRecord(flowCode);
		applyRecord.set("apply_state","submit".equals(doAction)?10:0);
		
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.set("apply_id",businessId);
		applyRecord.setColumns(applyInfo);
		
		boolean bl = true;
		EasyRecord record = null;
		JSONObject business = JsonKit.getJSONObject(params, "business");
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
		}
		try {
			if(record!=null) {
				bl = this.getQuery().save(record);
			}
			if(bl) {
				this.getQuery().save(applyRecord);
			}
			if("submit".equals(doAction)) {
				this.startFlow(flow,businessId,applyState,params);
			}
		} catch (SQLException e) {
			this.error(null, e);
			this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(businessId);
	}
	
	private void startFlow(FlowModel flow,String businessId,int applyState,JSONObject params) throws SQLException {
		ApproveNodeModel nodeInfo = ApproveService.getService().getStartNode(flow.getFlowCode(),getUserId(),params);
		
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		
		String todoCheckUserId = nodeInfo.getCheckBy();
		if(todoCheckUserId.indexOf(apply.getApplyBy())>-1) {
			 nodeInfo = ApproveService.getService().getNode(nodeInfo.getNextNodeId());
		}
		
		String resultId = RandomKit.uniqueStr();
		EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
		String nextNodeId =  nodeInfo.getNodeId();
		String userId = nodeInfo.getCheckBy();
		String userName = nodeInfo.getCheckName();
		
		resultRecord.set("result_id", RandomKit.uniqueStr());
		resultRecord.set("business_id", businessId);
		resultRecord.set("check_name", getUserName());
		resultRecord.set("check_user_id", getUserId());
		resultRecord.set("check_result", 1);
		resultRecord.set("node_id",0);
		if(applyState==21) {
			resultRecord.set("node_name","修改申请");
		}else {
			resultRecord.set("node_name","申请填写");
		}
		resultRecord.set("check_desc","");
		resultRecord.set("check_time",EasyDate.getCurrentDateString());
		resultRecord.set("get_time",EasyDate.getCurrentDateString());
		this.getQuery().save(resultRecord);
		
		EasyRecord nextResult = new EasyRecord("yq_flow_approve_result","result_id");
		nextResult.set("result_id", resultId);
		nextResult.set("business_id", businessId);
		nextResult.set("check_name", userName);
		nextResult.set("check_user_id", userId);
		nextResult.set("check_result", 0);
		nextResult.set("node_id",nextNodeId);
		nextResult.set("node_name",nodeInfo.getNodeName());
		nextResult.set("check_desc","");
		nextResult.set("get_time",EasyDate.getCurrentDateString());
		this.getQuery().save(nextResult);
		
		FlowService.getService().updateApplyBegin(businessId, nextNodeId,resultId);
		
		//微信推送给审批人
		MessageModel msgModel  = new MessageModel();
		msgModel.setSender(getUserId());
		msgModel.setSendName(getUserName());
		msgModel.setFkId(businessId);
		msgModel.setTitle(apply.getApplyTitle());
		msgModel.setTypeName(flow.getFlowCode());
		msgModel.setMsgLabel(nodeInfo.getNodeName());
		msgModel.setReceiver(userId);
		msgModel.setData1(flow.getFlowName());
		msgModel.setData2(apply.getApplyName());
		msgModel.setData3(apply.getApplyTime());
		msgModel.setDesc(apply.getApplyRemark());
		WxMsgService.getService().sendFlowTodoCheck(msgModel);
		MessageService.getService().sendMsg(msgModel);
		EmailService.getService().sendFlowNoticeEmail(msgModel);
	}
	
	public EasyResult actionForAddReadTime() {
		JSONObject params = getJSONObject();
		String resultId = params.getString("resultId");
		try {
			this.getQuery().executeUpdate("update yq_flow_approve_result set read_time = ? where result_id = ?", EasyDate.getCurrentDateString(),resultId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForGetNodeInfoByResultId() {
		JSONObject params = getJSONObject();
		String resultId = params.getString("resultId");
		ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
		return EasyResult.ok(approveNode);
	}
}
