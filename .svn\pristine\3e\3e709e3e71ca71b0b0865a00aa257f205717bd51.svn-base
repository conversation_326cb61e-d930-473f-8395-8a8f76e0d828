package com.yunqu.work.base;

import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.db.EasyQuery;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.model.StaffModel;
public abstract class AppBaseServlet extends EasyBaseServlet { 
	private static final long serialVersionUID = 1L;
	
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_NAME;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getResId() {
		return null;
	}
	public EasyQuery getMainQuery(){
		return EasyQuery.getQuery(getAppName(), Constants.MARS_DS_NAME);
	}

	protected String getRequestUrl() {
		return getRequest().getRequestURI()+"?"+getRequest().getQueryString();
	}
	protected String getDeptId() {
		return getStaffInfo().getDeptId();
//		return getUserPrincipal().getAttribute("deptId");
	}
	
	protected String getUserId() {
		return getUserPrincipal().getUserId();
	}
	
	protected String getUserName() {
		return getUserPrincipal().getUserName();
	}
	
	protected StaffModel getStaffInfo() {
		Object object =  getUserPrincipal().getAttribute("staffInfo");
		String jsonStr = JSONObject.toJSONString(object);
		JSONObject jsonObject = JSONObject.parseObject(jsonStr);
		return JSONObject.toJavaObject(jsonObject, StaffModel.class);
	}
	
}
