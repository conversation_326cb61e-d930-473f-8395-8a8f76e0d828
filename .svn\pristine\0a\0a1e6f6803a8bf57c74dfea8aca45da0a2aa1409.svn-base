<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>费用报销申请</title>
	<style>
		.layui-tab-content{background-color: #fff;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.gray-bg{background-color: #e8edf7;}
		.items .form-control{padding: 3px 4px;font-size: 12px;}
		.items .layui-table td, .layui-table th {padding: 6px;font-size: 12px;}
		.items .select2-container--bootstrap .select2-selection{font-size: 12px;}
		.layui-icon{font-size: 12px;}
		.extTr td{border: none;}
		.select2-results li{font-size: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="">
                <input type="hidden" name="flowCode" value="finance_bx"/>
                <input type="hidden" id="businessId" name="businessId" value="${param.businessId}"/>
                <input type="hidden" name="doAction"/>
                <input type="hidden" name="applyState" value="${applyInfo.applyState}"/>
				
				<input type="hidden" id="randomId" name="id" data-mars="CommonDao.randomId"/>
                <input type="hidden" name="fkId" value="${param.businessId}"/>
				
				<div class="layui-row" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
					<div class="layui-col-md12">
						<div class="layui-card">
						  <div class="layui-card-header text-c" style="font-size: 20px;height: 60px;line-height: 60px;">广州云趣信息科技有限公司-费用报销</div>
						  <div class="layui-card-body">
						    	<table class="table table-vzebra">
							  		<tr>
							  			<td style="width: 120px;">标题</td>
							  			<td>
							  				<input type="text" data-rules="required" class="form-control input-sm" data-mars="BxDao.bxTitle" name="applyTitle"/>
							  			</td>
							  			<td style="width: 120px;">单号</td>
							  			<td>
							  				<input type="text" readonly="readonly" class="form-control input-sm" data-mars="BxDao.yqBxNo" name="applyNo"/>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td class="required">姓名</td>
							  			<td>
							  				<input type="text" data-rules="required" value="${staffInfo.userName}${staffInfo.staffNo}" readonly="readonly" class="form-control input-sm" name="applyName"/>
							  			</td>
							  			<td class="required">部门</td>
							  			<td>
							  				<input type="hidden" value="${staffInfo.deptId}" name="deptId">
							  				<input type="text" data-rules="required" name="deptName" class="form-control input-sm" value="${staffInfo.deptName}" onclick="singleDept(this)">
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>说明</td>
							  			<td colspan="3">
											<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="applyRemark"></textarea>
							  			</td>
							  		</tr>
							  		
							  		<tr>
							  			<td>付款类型</td>
							  			<td>
							  				<select data-rules="required" class="form-control input-sm" id="payType" name="business.PAY_TYPE" onchange="selectPaymentType(this.value)">
							  					<option value="个人付款">个人付款</option>
							  					<option value="对公付款">对公付款</option>
							  				</select>
							  			</td>
							  			<td>冲帐金额</td>
							  			<td>
							  				<input type="number" data-rules="required" placeholder="如无借款请填0" onchange="calcBxMoney();" class="form-control input-sm" style="width: 80%;display: inline-block;" value="0" name="business.REVERSE_MONEY"/>
							  				<span>借款(0.00)</span>
							  			</td>
							  		</tr>
							  		<tr id="companyPaymentInfo" style="display: none;">
							  			<td>往来单位</td>
							  			<td colspan="3">
										 	<input type="hidden" name="business.CONTACT_CODE">
										 	<input class="form-control input-sm" readonly="readonly" type="text" onclick="selectContactUnit(this);" placeholder="点击选择" name="business.CONTACT_UNIT" style="width: 15%;display: inline-block;">
										 	<input class="form-control input-sm" placeholder="开户行"  type="text" name="business.CONTACT_BANK" style="width: 15%;display: inline-block;">
										 	<input class="form-control input-sm" placeholder="开户账号" type="text" name="business.CONTACT_BANK_NO" style="width: 15%;display: inline-block;">
										 	<button class="btn btn-sm btn-default" type="button" onclick="addContactUnit(this)">+新建往来单位</button>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>费用总计</td>
							  			<td>
							  				<input type="number" data-rules="required" readonly="readonly" placeholder="自动计算无需填写" class="form-control input-sm" name="business.BX_MONEY"/>
							  			</td>
							  			<td>说明</td>
							  			<td>
							  				每月27号关闭借款以外的费用报销申请
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>应付金额</td>
							  			<td>
							  				<input type="number" data-rules="required" readonly="readonly" placeholder="自动计算无需填写" class="form-control input-sm" name="business.PAY_MONEY"/>
							  			</td>
							  			<td>大写</td>
							  			<td>
							  				<input type="text" readonly="readonly" placeholder="自动计算无需填写" class="form-control input-sm" name="business.PAY_MONEY_DX"/>
							  			</td>
							  		</tr>
							  	</table>
						  </div>
						</div>
					</div>
				</div>
				<div class="layui-row" style="margin-top: 0px;">
					<div class="layui-col-md12">
						<div class="layui-card">
						  <div class="layui-card-body items">
						    	<jsp:include page="include/bx-item.jsp"></jsp:include>
						  </div>
						</div>
					</div>
				</div>
					<div class="layer-foot text-c" style="position: fixed;z-index: 99999999999999999;">
				<c:if test="${applyInfo.applyState=='0'||empty param.businessId}">
					<button class="btn btn-sm btn-default btn-outline draftBtn" type="button" onclick="Bx.ajaxSubmitForm('draft');">保存草稿</button>
				</c:if>
				<c:if test="${empty param.businessId||applyInfo.applyState=='0'}">
						<button class="btn btn-sm btn-info ml-15 saveBtn" type="button" onclick="Bx.ajaxSubmitForm('submit');">提交审批</button>
				</c:if>
				<c:if test="${mode=='edit'&&applyInfo.applyState!='0'}">
					<button class="btn btn-sm btn-info ml-15 saveBtn" type="button" onclick="Bx.ajaxSubmitForm('edit');">修改保存</button>
				</c:if>
				<c:if test="${mode=='edit'&&applyInfo.applyState=='21'}">
					<button class="btn btn-sm btn-success ml-15 saveBtn" type="button" onclick="Bx.ajaxSubmitForm('submit');">提交审批</button>
				</c:if>
		  </div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		function selectPaymentType(val){
			if(val=='对公付款'){
				$('#companyPaymentInfo').show();
			}else{
				$('#companyPaymentInfo').hide();
			}
		}

		var Bx = {bxId:$('#businessId').val()};
		
		$(function(){
			if(top.$('.admin-nav-mini').length==0){
				top.$("[ew-event='flexible']").click();
			}
			
			layui.use(['element','form'],function(){
				var element = layui.element;
				var form = layui.form;
				form.render();
			});
			
			
			$("#flowForm").render({success:function(result){
			
				renderDate('#flowForm');
				
				if(Bx.bxId){
					var businessInfo = result['FlowDao.businessInfo'];
					var data = businessInfo['data'];
					var payType = data['PAY_TYPE'];
					if(payType=='对公付款'){
						$('#companyPaymentInfo').show();
					}
				}else{
					addTr();
				}
				
				$(".taxRate").each(function(){
					 $(this).val($(this).data('value'));
				});
				
					
			}});
			
		});
		
		function calcBxMoney(){
			var sum  = 0 ;
			$("[name^='amount_']").each(function(){
				var t = $(this);
				sum = numAdd(sum,t.val());	
			  }
		     );
			$("[name='business.BX_MONEY']").val(sum);
			 var reverseMoney = $("[name='business.REVERSE_MONEY']").val();
			$("[name='business.PAY_MONEY']").val(sum - reverseMoney);
			
			var dxVal = digitUppercase(sum - reverseMoney);
			$("[name='business.PAY_MONEY_DX']").val(dxVal);
		}
		
		Bx.ajaxSubmitForm = function(state){
			$("[name='doAction']").val(state);
			var index  = 1;
			$("[name^='orderIndex_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			var payType = $('#payType').val();
			if(payType=='对公付款'){
				$('#companyPaymentInfo input[type="text"]').attr('data-rules','required');
			}else{
				$('#companyPaymentInfo input[type="text"]').removeAttr('data-rules');
			}
			if(form.validate("#flowForm")){
				doSubmit();
			};
			function doSubmit(){
				if(Bx.bxId){
					Bx.updateData(); 
				}else{
					Bx.insertData(); 
				}
			}
		}
		
		Bx.insertData = function(flag) {
			var data = form.getJSONObject("#flowForm");
			ajax.remoteCall("${ctxPath}/servlet/bx?action=submitApply",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.closeTab({callback:function(){
							popup.openTab({id:'flow_my_apply',url:'/yq-work/pages/flow/my/flow-list.jsp',title:'我的申请',reload:true})
						}});
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Bx.updateData = function(flag) {
			var data = form.getJSONObject("#flowForm");
			ajax.remoteCall("${ctxPath}/servlet/bx?action=updateApply",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.closeTab({callback:function(){
							popup.openTab({id:'flow_my_apply',url:'/yq-work/pages/flow/my/flow-list.jsp',title:'我的申请',reload:true})
						}});
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		
		function selectContactUnit(el){
			var id = new Date().getTime();
			$(el).attr('data-sid',id);
			popup.layerShow({id:'selectContactUnit',scrollbar:false,area:['700px','500px'],offset:'20px',title:'往来单位',url:'/yq-work/pages/flow/select/select-contact-unit.jsp',data:{sid:id,type:'radio'}});
		}
		
		function addContactUnit(){
			popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['400px','350px'],url:'/yq-work/pages/flow/bx/config/contact-unit-edit.jsp',title:'新增',closeBtn:0});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>