package com.yunqu.work.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.utils.WeekUtils;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import java.sql.SQLException;

public class ContractStatService extends AppBaseService {
    private static class Holder {
        private static ContractStatService service = new ContractStatService();
    }

    public static ContractStatService getService() {
        return Holder.service;
    }

    public String updateContractReceiptStat() {
        getLogger().info("-----updateContractReceiptStat执行合同收款统计 ....");
        try {
            this.getQuery().executeUpdate("TRUNCATE TABLE yq_contract_receipt_stat");

            EasySQL sql = new EasySQL("INSERT INTO yq_contract_receipt_stat (CONTRACT_ID, MONTH_ID, TOTAL_AMOUNT) SELECT t1.CONTRACT_ID,t1.MONTH_ID,SUM(t1.AMOUNT) AS TOTAL_AMOUNT FROM yq_contract_receipt t1 GROUP BY t1.CONTRACT_ID,t1.MONTH_ID");
            this.getQuery().executeUpdate(sql.getSQL());
            updateStatTime("yq_contract_receipt_stat");
            return "ok";
        } catch (SQLException e) {
            getLogger().error("-----updateContractReceiptStat合同收款统计出错,原因：", e);
            return "fail";
        } finally {
            getLogger().info("-----updateContractReceiptStat合同收款统计完成");
        }
    }

    public String updateContractIncomeStat() {
        getLogger().info("----updateContractIncomeStat执行合同收入确认统计 ....");
        try {
            this.getQuery().executeUpdate("TRUNCATE TABLE yq_contract_income_stat;");
            this.getQuery().executeUpdate(" INSERT INTO yq_contract_income_stat (CONTRACT_ID, MONTH_ID, TOTAL_AMOUNT,INCOME_TYPE) SELECT t1.CONTRACT_ID,t1.MONTH_ID,SUM(t1.AMOUNT_NO_TAX) AS TOTAL_AMOUNT,'S' FROM yq_contract_income_stage t1 GROUP BY t1.CONTRACT_ID,t1.MONTH_ID;");
            this.getQuery().executeUpdate(" INSERT INTO yq_contract_income_stat (CONTRACT_ID, MONTH_ID, TOTAL_AMOUNT,INCOME_TYPE) SELECT t1.CONTRACT_ID,t1.MONTH_ID,SUM(t1.AMOUNT_NO_TAX) AS TOTAL_AMOUNT,'A' FROM yq_contract_income_confirm t1 WHERE t1.CONFIRM_TYPE = 'A' GROUP BY t1.CONTRACT_ID,t1.MONTH_ID;");
            this.getQuery().executeUpdate(" INSERT INTO yq_contract_income_stat (CONTRACT_ID, MONTH_ID, TOTAL_AMOUNT,INCOME_TYPE) SELECT t1.CONTRACT_ID,t1.MONTH_ID,SUM(t1.AMOUNT_NO_TAX) AS TOTAL_AMOUNT,'B' FROM yq_contract_income_confirm t1 WHERE t1.CONFIRM_TYPE = 'B' GROUP BY t1.CONTRACT_ID,t1.MONTH_ID;");
            updateStatTime("yq_contract_income_stat");
            return "ok";
        } catch (SQLException e) {
            getLogger().error("-----updateContractIncomeStat合同收入确认统计出错,原因：", e);
            return "fail";
        } finally {
            getLogger().info("-----updateContractIncomeStat合同收入确认统计完成");
        }
    }


    private void updateStatTime(String tableName) throws SQLException {
        EasySQL sql = new EasySQL("UPDATE yq_contract_stat_table_info ");
        sql.append(EasyDate.getCurrentDateString(), "SET UPDATE_TIME = ?");
        sql.append(tableName, "WHERE TABLE_NAME = ?");
        this.getQuery().executeUpdate(sql.getSQL(), sql.getParams());
    }
}
