package com.yunqu.work.servlet.erp;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/supplier/*")
public class SupplierServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_erp_supplier","supplier_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	public EasyResult actionForAdd(){
		EasyRecord model = getModel("supplier");
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		model.setPrimaryValues(RandomKit.uniqueStr());
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("supplier");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDel(){
		try {
			this.getQuery().executeUpdate("delete from yq_erp_supplier where supplier_id = ?", getJsonPara("invoiceId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	

}
