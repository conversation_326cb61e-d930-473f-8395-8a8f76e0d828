<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="incomeStageTable"></table>
<script  type="text/html" id="toolbar2">
    <button class="btn btn-info btn-sm" onclick="incomeStageList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-15" onclick="reloadIncomeStageList()" type="button">刷新</button>
    <button class="btn btn-default btn-sm ml-15" onclick="incomeStageList.edit()" type="button">修改</button>
    <button class="btn btn-default btn-sm ml-15" onclick="incomeStageList.del()" type="button">删除</button>
</script>
<script type="text/javascript">
    var incomeStageList={
        init:function(){
            $("#ContractDetailForm").initTable({
                mars:'IncomeStageDao.incomeStageList',
                id:'incomeStageTable',
                page:false,
                toolbar:'#toolbar2',
                cellMinWidth:60,
                cols: [[
                    {
                        type: 'checkbox',
                        align:'left'
                    },{
                        field: 'STAGE_NAME',
                        title: '阶段名称',
                        align:'left',
                        minWidth:80
                    },{
                        field:'ACT_COMP_DATE',
                        title:'实际完成日期',
                        align:'left',
                        sort:true,
                        minWidth:80
                    },{
                        field:'RATIO',
                        title:'比例(%)',
                        minWidth:70
                    },{
                        field:'PLAN_AMOUNT',
                        title:'计划收款金额',
                        minWidth:80
                    },{
                        field:'AMOUNT_NO_TAX',
                        title:'税后金额',
                        minWidth:80
                    },{
                        field:'CREDIT_PRD',
                        title:'信用期（天）',
                    },{
                        field:'PAYMENT_ID',
                        title:'对应款项',
                        minWidth:280,
                        width: 380,
                        templet:function(d){
                            return getText(d.PAYMENT_ID,'paymentName');
                        }
                    },{
                        field:'REMARK',
                        title:'备注',
                        minWidth:120
                    },{
                        field:'HARDWARE_CONF_FLAG',
                        title:'硬件确认',
                        minWidth:100,
                        templet:function(d){
                            if(d.HARDWARE_CONF_FLAG == '1'){
                                return '√';
                            }else{
                                return '×';
                            }
                        }
                    },{
                        field:'INCOME_CONF_FLAG',
                        title:'收入确认',
                        minWidth:100,
                        templet:function(d){
                            if(d.INCOME_CONF_FLAG == '1'){
                                return '√';
                            }else{
                                return '×';
                            }
                        }
                    },{
                        field:'YULIU_RATIO',
                        title:'预留比例',
                        minWidth:100
                    },{
                        field:'CHECKED_FLAG',
                        title:'已审核',
                        width:65,
                        templet:function(d){
                            if(d.CHECKED_FLAG == '1'){
                                return '√';
                            }else{
                                return '×';
                            }
                        }
                    },{
                        field: 'UPDATE_TIME',
                        title: '更新时间',
                        width:120,
                        align:'center'
                    }
                ]],
                done:function(res){
                    if(res.total!=undefined){
                        $("#incomeStageCount").text("("+res.total+")");
                    }
                }
            });
        },
        add:function (){
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增收入确认阶段',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/income-stage-edit.jsp',title:'新增收入确认阶段',data:{contractId:contractId,isNew:'1'}});
        },
        edit:function (){
            var checkStatus = table.checkStatus('incomeStageTable');
            for(var i=0;i<checkStatus.data.length;i++){
                if(checkStatus.data[i].CHECKED_FLAG === '1'){
                    layer.msg("已审核的阶段不能编辑!",{icon:7,time:1000});
                    return;
                }
                if(checkStatus.data[i].INCOME_CONF_FLAG === '1'){
                    layer.msg("已收入确认的阶段不能编辑，<br>请先删除对应的确认收入信息。",{icon:7,time:2000});
                    return;
                }
                if(checkStatus.data[i].HARDWARE_CONF_FLAG === '1'){
                    layer.msg("已硬件确认的阶段不能编辑!",{icon:7,time:1000});
                    return;
                }
            }


            if(checkStatus.data.length > 1){
                layer.msg('一次只能更新一个款项',{icon : 7, time : 1000});
            }
            else if(checkStatus.data.length == 1){
                var incomeStageId = checkStatus.data[0]['INCOME_STAGE_ID'];
                popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新收入确认阶段',offset:'auto',area:['60%','70%'],url:ctxPath+'/pages/crm/contract/include/income-stage-edit.jsp',title:'编辑收入确认阶段',data:{contractId:contractId,incomeStageId:incomeStageId,isNew:'0'}});
            }else{
                layer.msg("请选择!");
            }
        },
        del:function (){
            var checkStatus = table.checkStatus('incomeStageTable');
            if(checkStatus.data.length>0){
                for(var i=0;i<checkStatus.data.length;i++){
                    if(checkStatus.data[i].CHECKED_FLAG === '1'){
                        layer.msg("已审核的阶段不能删除!",{icon:7,time:1000});
                        return;
                    }
                    if(checkStatus.data[i].INCOME_CONF_FLAG === '1'){
                        layer.msg("已收入确认的阶段不能删除，<br>请先删除对应的确认收入信息。",{icon:7,time:2000});
                        return;
                    }
                    if(checkStatus.data[i].HARDWARE_CONF_FLAG === '1'){
                        layer.msg("已硬件确认的阶段不能删除!",{icon:7,time:1000});
                        return;
                    }
                }
                layer.confirm("确认要删除吗?",{icon:3,offset:'120px'},function(){
                    ajax.remoteCall("${ctxPath}/servlet/incomeStage?action=BatchDel",checkStatus.data,function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                layer.closeAll();
                                reloadIncomeStageList();
                                reloadPaymentList();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                });
            }else{
                layer.msg("请选择!");
            }
        }
    }

    function reloadIncomeStageList(){
        $("#ContractDetailForm").queryData({id:'incomeStageTable',page:false});
        $("[name='paymentName']").render();
    }
</script>
