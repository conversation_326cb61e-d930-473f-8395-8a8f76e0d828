<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择任务</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selecTaskForm">
       	   <input name="projectId" value="${param.projectId}" type="hidden"/>
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
					 <span class="input-group-addon">任务名称</span>	
					 <input type="text" name="taskName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectTask.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		</div>
            	<div class="ibox">
              		<table id="selectTaskList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectTask.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectTask={
					sid:'${param.sid}',
					query : function(){
						$("#selecTaskForm").queryData();
					},
					initTable : function(){
						$("#selecTaskForm").initTable({
							mars:'TaskDao.taskSelector',
							id:'selectTaskList',
							page:true,
							limit:20,
							height:'400px',
							rowDoubleEvent:'SelectTask.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'TASK_NAME',
								title: '任务名称'
							},{
							    field: 'ASSIGN_USER_NAME',
								title: '负责人',
								width:80
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectTask.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('selectTaskList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['TASK_NAME']);
									ids.push(data[index]['TASK_ID']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								popup.layerClose("selecTaskForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							el.val(selectRow['TASK_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['TASK_ID']);
							}
							popup.layerClose("selecTaskForm");
						}
					}
					
			};
			$(function(){
				SelectTask.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>