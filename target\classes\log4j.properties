log4j.rootLogger=DEBUG,console

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %-4r %-5p %c{2} - %m%n

log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.File=easyserver/logs/yq-work.log
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH\:mm\:ss.SSS} %-4r %-5p %c{2} - %m%n
log4j.appender.console.MaxFileSize=100MB
log4j.appender.console.MaxBackupIndex=5
log4j.logger.org.quartz=INFO
