<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我负责的项目</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm" style="width: 160px;">
							 <span class="input-group-addon">项目编号</span>	
							 <input type="text" name="projectNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 160px;">
							 <span class="input-group-addon">项目名称</span>	
							 <input type="text" name="projectName" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目类型</span>	
							 <select name="projectType" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="10" data-class="label label-info">合同项目</option>
		                    		<option value="11" data-class="label label-info">提前执行</option>
		                    		<option value="20" data-class="label label-warning">自研项目</option>
		                    		<option value="30" data-class="label label-warning">维保项目</option>
	                    	</select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
				  <div class="ibox-content">
						<table class="layui-hide" id="mylist1"></table>
				  </div>
			</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
  			{{if CREATOR == currentUserId|| isSuperUser || currentUserId == PO || currentUserId == PROJECT_PO}}<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

<script type="text/javascript">

		var list = {
			init1:function(){
				$("#searchForm").initTable({
					mars:'ProjectDao.myMangerProjectList',
					cellMinWidth:70,
					title:'我负责的项目',
					height:'full-90',
					limit:30,
					id:'mylist1',
					cols: [[
					  {title:'序号',type:'numbers'},
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						align:'left',
						minWidth:200,
						style:'cursor:pointer',
						event:'list.detail',
						templet:function(row){
							var type = row['PROJECT_TYPE'];
							var no = row['PROJECT_NO'];
							no = no==0?'':no;
							if(type==10){
								return no +"&nbsp;"+ row.PROJECT_NAME;
							}else{
								var val = getText(type,'projectType');
								return val + "&nbsp;" + no + row.PROJECT_NAME;
							}
						}
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:90,
						hide:true,
						templet:function(row){
							return projectState(row.PROJECT_STATE);
						}
					},{
					    field: 'PO_NAME',
						title: '开发负责人',
						align:'center',
						width:95
					},{
					    field: 'PROJECT_PO_NAME',
						title: '项目经理',
						align:'center',
						width:95
					},{
					    field: 'TASK_COUNT',
						title: '任务数',
						width:70,
						align:'center'
					},{
					    field: 'PERSON_COUNT',
						title: '参与人数',
						width:90,
						event:'list.taskMgr',
						align:'center'
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						width:100,
						align:'center'
					},{
					    field: 'LAST_TASK_TIME',
						title: '最近任务时间',
						width:130,
						align:'left'
					},{
						title: '操作',
						align:'center',
						width:80,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							row['isSuperUser']=isSuperUser;
							return renderTpl('bar1',row);
						}
					}
					]]}
				);
			},
			taskMgr:function(data){
				popup.openTab({url:'${ctxPath}/pages/task/task-query.jsp',id:'taskMgr',title:'项目任务管理',data:{projectId:data['PROJECT_ID']}});
			},
			query:function(){
				$("#searchForm").queryData({id:'mylist1'});
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'编辑项目',data:{projectId:data.PROJECT_ID}});
			},
			detail:function(data){
				projectDetailByRow(data);
			}
		}
		
		function reloadProjectList(){
			list.query();
		}
		
		function reloadTaskList(){
			
		}
		
		$(function(){
			list.init1();
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>