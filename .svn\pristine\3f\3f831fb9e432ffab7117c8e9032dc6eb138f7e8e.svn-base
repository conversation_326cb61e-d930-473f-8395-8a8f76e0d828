<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="payTypeEditForm" class="form-horizontal" data-mars="OrderDao.payTypeInfo" autocomplete="off" data-mars-prefix="plan.">
 		   		<input type="hidden" value="${param.typeId}" name="typeId"/>
 		   		<input type="hidden" value="${param.typeId}" name="plan.TYPE_ID"/>
 		   		<input type="hidden" value="${param.orderId}" name="plan.ORDER_ID"/>
 		   		<input type="hidden" value="${param.orderId}" name="orderId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
	                     <tr>
		                     <td width="120px" class="required">付款条件</td>
		                     <td>
		                   		  <select class="form-control" data-rules="required" name="plan.PAY_PERIOD">
	                           		<option value="预付款">预付款</option>
	                           		<option value="货到款">货到款</option>
	                           		<option value="背靠背付款">背靠背付款</option>
	                           		<option value="初验款">初验款</option>
	                           		<option value="阶段款">阶段款</option>
	                           		<option value="终验款">终验款</option>
	                           		<option value="维护款">维护款</option>
	                           		<option value="其他方式">其他方式</option>
	                           	 </select>
		                    </td>
		                </tr>
			            <tr>
		                	<td class="required">工作日付款</td>
		                    <td>
		                   		 <input data-rules="required" type="text" name="plan.PAY_DAY" class="form-control input-sm">
		                    </td>
	                     </tr>
                        <tr>
		                    <td class="required">采购金额(含税)</td>
		                    <td>
								<input type="number" id="orderPrice" data-mars="OrderDao.getOrderPrice" readonly="readonly" class="form-control input-sm"/>
		                    </td>
			            </tr>
                        <tr>
		                    <td class="required">付款比例(%)</td>
		                    <td>
								<input data-rules="required" type="text" onchange="setPayMoney(this.value)" name="plan.PAY_RATE" placeholder="0~100" class="form-control input-sm"/>
		                    </td>
			            </tr>
			            <tr>
		                    <td>付款金额</td>
		                    <td>
		                    	 <input type="text" name="plan.PAY_AMOUNT" class="form-control input-sm">
		                    </td>
	                    </tr>
		                 <tr>
		                	<td class="required">付款方式</td>
		                    <td>
		                   		 <select data-rules="required" name="plan.PAY_TYPE" class="form-control input-sm">
		                   		 	<option value="电汇">电汇</option>
		                   		 </select>
		                    </td>
	                     </tr>
	                     <tr>
		                    <td>备注</td>
		                    <td>
		                   		 <input type="text" name="plan.REMARK" class="form-control input-sm">
		                    </td>
		                </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="PayTypeEit.ajaxSubmitForm()"> 保 存 </button>
					  <c:if test="${!empty param.typeId }">
					      <button type="button" class="btn btn-default btn-sm ml-15" onclick="PayTypeEit.del();"> 删除 </button>
					  </c:if>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("PayTypeEit");
	    
		PayTypeEit.typeId='${param.typeId}';
		
		var typeId='${param.typeId}';
		
		$(function(){
			$("#payTypeEditForm").render({success:function(result){

			}});  
		});
		
		PayTypeEit.ajaxSubmitForm = function(){
			if(form.validate("#payTypeEditForm")){
				if(PayTypeEit.typeId){
					PayTypeEit.updateData(); 
				}else{
					PayTypeEit.insertData(); 
				}
			};
		}
		PayTypeEit.insertData = function() {
			var data = form.getJSONObject("#payTypeEditForm");
			delete data['plan.TYPE_ID'];
			ajax.remoteCall("${ctxPath}/servlet/order?action=addPayType",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadPayTypeList();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		PayTypeEit.updateData = function() {
			var data = form.getJSONObject("#payTypeEditForm");
			ajax.remoteCall("${ctxPath}/servlet/order?action=updatePayType",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadPayTypeList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		PayTypeEit.del = function() {
			layer.confirm('是否确认删除?',function(index){
				layer.close(index);
				var data = form.getJSONObject("#payTypeEditForm");
				ajax.remoteCall("${ctxPath}/servlet/order?action=delPayType",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
							reloadPayTypeList();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		function setPayMoney(rate){
			if(rate>100||rate<0){
				layer.msg('0~100范围');
				return;
			}
			var orderPrice = $('#orderPrice').val();
			$("[name='plan.PAY_AMOUNT']").val((orderPrice*((100-rate)/100)).toFixed(2));
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>