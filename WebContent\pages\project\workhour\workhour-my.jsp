<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>月度项目工时填报</title>
	<style>
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-content">
			    	<table class="layui-hide" id="list"></table>
				</div>
			</div>
		</form>
		<script type="text/x-jsrender" id="bar">
			{{if ID}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>
				{{if STATE!=0}}
				 <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看</a>
				{{/if}}
			{{else}}
				<a class="layui-btn layui-btn-info layui-btn-xs" lay-event="list.add">补填</a>
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'WorkHourDao.queryPersonWorkHour',
					id:'list',
					limit:20,
					cellMinWidth:100,
					height:'full-35',
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'MONTH_ID',
						title: '工作月份',
						align:'left',
						templet:function(row){
							return monthIdFormat(row['MONTH_ID']);
						}
					},{
					    field: 'PROJECT_NUM',
						title: '参与项目数',
						align:'left'
					},{
					    field: 'WORK_TIME',
						title: '工时/天',
						align:'left'
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						align:'left'
					},{
					    field: 'STATE',
						title: '状态',
						align:'left',
						templet:function(row){
							if(row.STATE==0){
								return '<span class="layui-badge">待填写</span>';
							}if(row.STATE==1||row.STATE==2){
								return '已发出';
							}else{
								return '未填写';
							}
						}
					},{
						title: '操作',
						align:'left',
						width:80,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'list'});
			},
			edit:function(data){
				var json = {id:data.ID,monthId:data['MONTH_ID'],state:data.STATE,opType:'edit'};
				popup.layerShow({type:1,full:fullShow(),shade: 0.1,shadeClose:false,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['85%','100%'],url:'${ctxPath}/pages/project/workhour/workhour-edit.jsp',title:'编辑_'+monthIdFormat(json['monthId'])+'_工时明细【请如实填报，项目工时最终关系到项目成本核算】',data:json});
			},
			add:function(data){
				var width=$(window).width();
				var json = {id:data.ID,monthId:data['MONTH_ID'],state:data.STATE};
				popup.layerShow({type:1,full:true,shade: 0.1,shadeClose:false,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/project/workhour/workhour-edit.jsp',title:'新增_'+monthIdFormat(json['monthId']),data:json});
			},
			detail:function(data){
				var json = {id:data.ID,monthId:data['MONTH_ID'],state:data.STATE,opType:'detail'};
				popup.layerShow({id:'weeklyDetail',type:1,full:true,shade: 0.1,shadeClose:true,maxmin:true,anim:0,scrollbar:false,url:'${ctxPath}/pages/project/workhour/workhour-edit.jsp',title:monthIdFormat(json['monthId'])+'_工时明细',data:json});
			}
		}
		
		function monthIdFormat(dateString){
			if(dateString){
				var pattern = /(\d{4})(\d{2})/;
				var formatedDate = dateString.replace(pattern, '$1年$2月');
				return formatedDate;
			}else{
				return '';
			}
		}

		
		$(function(){
			list.init();
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>