<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
	<style>
		.select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
   			 background-color: #fff;
		}
		.w-e-toolbar p, .w-e-text-container p, .w-e-menu-panel p {
   	 		font-size: 14px !important;
		}
		#editForm img{max-width: 98%!important;height: auto;}
		#editForm .w-e-text{max-width: 100%!important;}
		#editForm table,tr,td,p{max-width: 100%!important;}
		.layui-table-tips{z-index: 999999999999999999!important;};
		.btn-group-sm > .btn, .btn-sm{padding: 5px!important;}
		.select2 {z-index: 90;}
		/** 控制表格单元格换行 **/
		.tableSelect .layui-table-cell {
			height: auto;
			word-break: normal;
		 	display: block;
		 	white-space: pre-wrap;
		 	word-wrap: break-word;
		 	overflow: hidden;
		}
	</style>
	<link href="/easitline-static/lib/select2/css/select2.min.css" rel="stylesheet">
	<link href="/easitline-static/lib/select2/css/select2-bootstrap.min.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editForm" style="margin-bottom: 100px;" data-mars="TaskDao.record" autocomplete="off" data-mars-prefix="task.">
	     		   <input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
	     		   <input type="hidden" value="${param.groupId}" name="task.GROUP_ID"/>
	     		   <input type="hidden" id="taskId" value="${param.taskId}" name="task.TASK_ID"/>
	     		   <input type="hidden" value="${param.taskId}" name="fkId"/>
							<table class="table table-edit table-vzebra">
						        <tbody>
						            <tr>
					                    <td class="required">标题</td>
					                    <td colspan="3">
					                    	<input data-rules="required" maxlength="200" type="text" name="task.TASK_NAME" class="form-control input-sm">
					                    </td>
						            </tr>
						            <tr id="condition" class="hidden">
						            	<td>
						            		类别
						            	</td>
						            	<td colspan="3">
						            		<label style="vertical-align:text-bottom;" class="radio radio-info radio-inline">
				                    			<input type="radio" checked="checked" value="1" name="task.TASK_TYPE"/> <span>项目合同任务</span>
					                    	</label>
					                    </td>
						            </tr>
						          <tr>
						            	<td>任务描述</td>
						               	<td colspan="3">
						               	   <div id="editor"></div>
				                           <textarea id="wordText" data-text="false" style="height: 100px;width:400px;display: none;" class="form-control input-sm" name="task.TASK_DESC"></textarea>
						               	</td>
						            </tr>
						            <tr class="businessHide">
						            	<td>父任务</td>
						            	<td colspan="3">
						            		<input type="hidden" name="task.P_TASK_ID" value="${param.pTaskId}"/>
						            		<input type="text" id="pTasktId" value="无"  onclick="singleTask(this);" class="form-control"/>
						            	</td>
						            </tr>
					                <tr class="businessHide">
					                	<td class="required">
					                		所属项目
					                	</td>
					                	<td>
				                		   <input type="hidden" name="projectId" value="${param.projectId}"/>
			                    	  	   <input type="hidden" name="task.PROJECT_ID" value="${param.projectId}"/>
                    					   <input id="taskProjectId" value="${param.projectName}" ts-selected="${param.projectId}" onclick="singleProject(this);" name="task.PROJ_NAME" type="text" class="form-control input-sm"/>
					                	</td>
					                	<td>
					                		所属模块
					                	</td>
					                	<td colspan="3">
					                		<input type="hidden" name="task.MODULE_NAME"/>
					                		<select name="task.MODULE_ID" id="moduleId" onchange="changeModule($(this))" class="form-control input-sm" data-mars="ProjectDao.modules"></select>
					                	</td>
					                </tr>
					                <tr class="businessShow">
					                	<td class="required">
					                		所属商机
					                	</td>
					                	<td colspan="3">
			                    	  	    <input type="hidden" id="businessId" name="task.BUSINESS_ID" value="${param.businessId}"/>
                    					    <input id="businessIdSelect" data-rules="required" value="${param.projectName}" onclick="singleSj(this,'')" ts-selected="${param.businessId}" name="task.PROJ_NAME" type="text" class="form-control input-sm"/>
					                	</td>
					                </tr>
						            <tr>
					                    <td style="width: 80px;" class="required">指派给</td>
					                    <td style="width: 40%;">
					                    	<select data-rules="required" id="users" data-mars="CommonDao.userDict" name="task.ASSIGN_USER_ID" class="form-control input-sm">
					                    		<option value="">请选择</option>
					                    	</select>
					                    </td>
					                    <td class="required" style="width: 80px;">
						            		任务类型
						            	</td>
						            	<td>
					                    	<select  name="task.TASK_TYPE_ID" data-rules="required" data-mars="TaskDao.taskType" class="form-control">
					                    		<option value="">请选择</option>
					                    	</select>
					                    </td>
					                </tr>
						            <tr>
					                    <td style="width: 80px;" class="required">预估工时</td>
					                    <td style="width: 40%;">
					                    	<input data-rules="required" type="number" placeholder="小时"  name="task.PLAN_WORK_HOUR" class="form-control input-sm">
					                    </td>
					                    <td class="required" style="width: 80px;">
						            		任务难度
						            	</td>
						            	<td>
					                    	<select name="task.TASK_DIFFCULTY" data-rules="required" class="form-control input-sm">
					                    		<option value="">请选择</option>
					                    		<option value="简单">简单</option>
					                    		<option value="中等">中等</option>
					                    		<option value="复杂">复杂</option>
					                    	</select>
					                    </td>
					                </tr>
					                <tr>
					                    <td>任务标签</td>
					                    <td colspan="3">
					                    	<input name="task.TASK_TAGS" id="tags" class="layui-hide">
					                    </td>
					                </tr>
					                <tr>
					                    <td>抄送人</td>
					                    <td colspan="3">
					                    	<select  multiple="multiple" data-mars="CommonDao.userDict" id="mailtos" name="mailtos" class="form-control input-sm">
					                    	</select>
					                    </td>
					                </tr>
						            <tr>
					                    <td class="required">任务起始时间</td>
					                    <td colspan="3">
					                    	<input id="beginDate" type="text" style="display: inline-block;width: 140px" data-rules="required" data-mars="CommonDao.date02" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" name="task.PLAN_STARTED_AT"  class="form-control input-sm Wdate">
					                    	<input id="endDate" type="text" style="display: inline-block;width: 140px" data-rules="required" placeholder="截至时间" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 18:00:00',doubleCalendar:true,alwaysUseStartDate:true})" name="task.DEADLINE_AT" class="form-control input-sm Wdate">
					                   		<div class="btn-group btn-group-xs">
					                   			<button onclick="fillDate(1)" class="btn btn-sm btn-default" type="button">1天</button>
					                   			<button onclick="fillDate(2)" class="btn btn-sm btn-default" type="button">2天</button>
					                   			<button onclick="fillDate(3)" class="btn btn-sm btn-default" type="button">3天</button>
					                   			<button onclick="fillDate(7)" class="btn btn-sm btn-default" type="button">7天</button>
					                   			<button onclick="fillDate(15)" class="btn btn-sm btn-default" type="button">15天</button>
					                   			<button onclick="fillDate(30)" class="btn btn-sm btn-default" type="button">30天</button>
					                   		</div>
					                    </td>
					                </tr>
						            <tr>
					                    <td class="required">优先级</td>
					                    <td colspan="3">
												<label class="radio radio-success radio-inline">
								   					<input type="radio" name="task.TASK_LEVEL" value="1"> <span>较低</span>
								  				 </label>
												<label class="radio radio-success radio-inline">
								   					<input type="radio" checked="checked" name="task.TASK_LEVEL" value="2"> <span>普通</span>
								  				 </label>
												<label class="radio radio-success radio-inline">
								   					<input type="radio" name="task.TASK_LEVEL" value="3"> <span>紧急</span>
								  				 </label>
												<label class="radio radio-success radio-inline">
								   					<input type="radio" name="task.TASK_LEVEL" value="4"> <span>非常紧急</span>
								  				 </label>
					                    </td>
					                </tr>
						            <tr>
						              	<td>
						            		提醒方式
						            	</td>
						            	<td colspan="3">
						            		<label class="checkbox checkbox-default checkbox-inline">
				                    			<input type="checkbox" value="1" disabled="disabled" name="task.NOTICE_TYPES"/> <span>即时通讯</span>
					                    	</label>
						            		<label class="checkbox checkbox-default checkbox-inline">
				                    			<input type="checkbox" checked="checked" value="2" onclick="$(this).prop('checked', true);" name="task.NOTICE_TYPES"/> <span>系统私信</span>
					                    	</label>
					                    	<label class="checkbox checkbox-default checkbox-inline">
						                    	<input type="checkbox" value="3" checked="checked"  name="task.NOTICE_TYPES"/> <span>邮件提醒</span>
					                    	</label>
					                    	<label class="checkbox checkbox-default checkbox-inline">
						                    	<input type="checkbox" value="4" name="task.NOTICE_TYPES" checked="checked"/> <span>微信推送</span>
					                    	</label>
						            	</td>
						            </tr>
						             <tr>
						                <td style="width: 80px">附件</td>
						               	<td colspan="3">
						               		<div data-template="template-files" data-mars="FileDao.fileList"></div>
						               		<div id="fileList"></div>
											<button class="btn btn-xs btn-info mt-5" type="button" onclick="$('#localfile').click()">+批量上传</button>
						               	</td>
						            </tr>
						        </tbody>
		  					  </table>
							 <div class="layer-foot text-c" style="z-index: 99999999999;box-shadow: 0 -10px 20px 0 rgba(45,67,118,.1);">
							    	  <button type="button" class="btn btn-primary btn-sm" onclick="Task.ajaxSubmitForm()"> 保 存  </button>
								      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
							</div>			
	  		</form>
  		 <script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" data-id="{{:FILE_ID}}" onclick="delFile($(this),true)">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" multiple="multiple" type="file" id="localfile" onchange="Task.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/pinyin.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/lib/select2/select2.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js?v=0311"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/tableSelect.js"></script>
	<script type="text/javascript">
		
		var Task = {taskId:'${param.taskId}'}
	    
		var taskId='${param.taskId}';
		var projectId='${param.projectId}';
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.config.menus = ['head','bold','fontSize','fontName','foreColor','link','list','justify','quote','code','emoticon','image','table'];
		editor.config.pasteFilterStyle = false;
		editor.config.pasteIgnoreImg = false;
		editor.config.pasteTextHandle = function (content) {
		      if (content == '' && !content) return ''
		      var str = content
		      if(str.indexOf('MsoNormal')>-1){
		    	  layer.msg("复制word文档内容可能格式不正确,请对格式进行调整。",{icon:7});
		      }
		      if(str.indexOf('file://')>-1){
		    	  layer.msg("复制word文档附加的图片路径不存在,请单独复制图片。",{icon:7});
		      }
		      return str;
		} 
		weUpload(editor,{uploadImgMaxLength:3});
		editor.config.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.config.height = 100;
		editor.create();
		
		$(function(){
			var businessId = $('#editForm #businessId').val();
			if(businessId!=''){
				$('.businessHide').hide();
				$('.businessShow').show();
			}else{
				$('.businessShow').hide();
			}
			
			if(taskId!=''&&businessId!=''){
				$('#taskProjectId').remove();
			}
			if(businessId!=''){
				$('#taskProjectId').remove();
			}
			if(businessId==''){
				$('#businessIdSelect').remove();
			}
			
			layui.config({
				  base: '${ctxPath}/static/module/'
			}).extend({
				tagsInput: 'tagsInput/tagsInput'
			}).use('tagsInput');
			

		    $('[data-toggle="tooltip"]').tooltip();
			$("#editForm").render({success:function(result){
				 var c = $("#wordText").val();
				 c = c.replaceAll("http://work.yunqu-info.cn","");
				 editor.txt.html(c);
				 var record=result['TaskDao.record'];
				 if(typeof record.data != 'string'){
						var mailtos=record.mailtos;
						var _projectId = record.data['PROJECT_ID'];
						$("[name='mailtos']").val(mailtos);
						$("#taskProjectId").val(record.projectName);
						$("#taskProjectId").attr("ts-selected",_projectId);
						$("#pTasktId").val(record.taskName||'无');
						$("#pTasktId").attr("ts-selected",record.data['P_TASK_ID']);
						
						$("#moduleId").render({data:{projectId:_projectId},success:function(){
							$("#moduleId").val(record.data['MODULE_ID']);;
						}});
					}
					if(taskId==''){
						var taskAssignUserId = localStorage.getItem("taskAssignUserId")||'';
						var taskMailtos = localStorage.getItem("taskMailtos")||'';
						$("#users").val(taskAssignUserId);
						$("[name='mailtos']").val(taskMailtos.split(","));
					}
					
					$("#editForm #users,#editForm #mailtos").select2({theme: "bootstrap",placeholder:'请选择'});
					if(taskId){
						var state = record['data']['TASK_STATE'];
						if(state > 10){
							$("#mailtos,#users").prop("disabled", true);
						}
						$("input[name='task.NOTICE_TYPES']").prop("disabled", true);
				 }
				 layer.photos({photos:'#editor',anim: 0,shade:0,shadeClose:true,closeBtn:true}); 
				 
				 $("#editForm .w-e-text-container").keydown(function(event) {
					  if(event.keyCode == "13") {
						 var height = $(this).height();
						 $(this).css('height',(height+20)+'px');
					  }
					   if(event.keyCode == 8) {
						 var height = $(this).height();
						 height=height>120?height:120;
						 $(this).css('height',(height-20)+'px');
					   }
				});
				 if(taskId==''){
					var taskProjectId = localStorage.getItem("taskProjectId");
					var taskProjectName = localStorage.getItem("taskProjectName");
					var businessId = $('#editForm #businessId').val();
					if(projectId==''&&taskProjectId&&businessId==''){
						$("#editForm [name='task.PROJECT_ID']").val(taskProjectId);
						$("#taskProjectId").val(taskProjectName);
						$("#taskProjectId").attr("ts-selected",taskProjectId);
						loadModule(taskProjectId);
					}
			    }
					
				layui.use(['tagsInput'], function(){
					var tagsInput = layui.tagsInput;
					$('#tags').tagsInput({
						skin: 'tagsinput-default',
						autocomplete_url: '/yq-work/servlet/task?action=tags',
					    autocomplete: {type: 'post',data: {}}
					});
				});
			}});
			
			if(projectId){
				$("[name='task.PROJECT_ID']").val(projectId);
			}
		});
		
		function fillDate(val){
			var v = addDate(val);
			if(v.indexOf("NaN")>-1){
				layer.msg("时间格式不正确,请检查浏览器兼容性或者换chome");
			}else{
				$("#endDate").val(v);
			}
		}
		function addDate(days) {
            if (days == undefined || days == '') {
                days = 1;
            }
            var d=$("#beginDate").val();
            if(d){
	            var date = new Date(d.replace(/-/g,'/'));
	            date.setDate(date.getDate() + days);
	            var month = date.getMonth() + 1;
	            var day = date.getDate();
	            var h = date.getHours();
	            var m = date.getMinutes();
	           // return date.getFullYear() + '-' + getFormatDate(month) + '-' + getFormatDate(day)+" "+ getFormatDate(h)+":"+ getFormatDate(m);
	            return date.getFullYear() + '-' + getFormatDate(month) + '-' + getFormatDate(day)+" 18:00";
            }else{
            	return "";
            }
        }
		function getFormatDate(arg) {
            if (arg=='0'||arg) {
	            var re = arg + '';
	            if (re.length < 2) {
	                re = '0' + re;
	            }
	            return re;
            }else{
                return '';
            }
        }

		Task.uploadFile = function(callback){
			var fkId='';
			if(Task.taskId){
				fkId=taskId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'callback',fileMaxSize:(1024*50),fkId:fkId,source:'task'});
		}
		var callback = function(result){
			var array = [];
			if(isArray(result)){
				array = result;
			}else{
				array[0] = result;
			}
			for(var index in array){
				var data = array[index];
				$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this),true)">x</i></div>');
			}
		}
		
		Task.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				var endDate=$("#endDate").val();
				if(endDate==''){
					layer.msg('任务截至时间不能为空!');
					return;
				}
				 var html=$("#wordText").val();
				 html = html.replaceAll("http://work.yunqu-info.cn","");
				 $("#wordText").val(html);
				 
				 var taskTypeId = $("[name='task.TASK_TYPE_ID']").val();
				 //开发类型
				 if(taskTypeId=='4'){
					var tags = $('#tags').val();
					if(tags==''){
						layer.msg('请输入任务标签！',{icon:7});
						return;
					}
				 }
				 
				if(Task.taskId){
					Task.updateData(); 
				}else{
					Task.insertData(); 
				}
			};
		}
		Task.insertData = function(flag) {
			$("#taskId").val($("#randomId").val());
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/task?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						localStorage.setItem("taskAssignUserId",$("#users").val());
						var ids = $("[name='mailtos']").val()||[];
						localStorage.setItem("taskMailtos",ids.join());
						
						var projectId = $("#editForm [name='task.PROJECT_ID']").val();
						if(projectId){
							var projectName = $("#taskProjectId").val();
							localStorage.setItem("taskProjectId",projectId);
							localStorage.setItem("taskProjectName",projectName);
						}
						
						var currentUserId = getCurrentUserId();
						var assignUserId = data['task.ASSIGN_USER_ID'];
						
						if(currentUserId==assignUserId){
							layer.confirm('发起人和负责人是同一人,是否修改状态',{icon:3,btn:['进行中','已完成','已验收','关闭'],btn3:function(){
								updateState(40);
							},btn4:function(){
								
							}},function(){
								updateState(20);
							},function(){
								layer.prompt({id:'workHourLayer',title:'请输入任务消耗工时/人日',offset:'20px',success:function(){
									$('.layui-layer-input').attr('type','number').attr('placeholder','人/日');;
								}},function(value, index, elem){
									if(value>10){
										layer.confirm('确认工时要'+value+"/人/日?请确认无误",{offset:'20px',shade:0.1,icon:3},function(){
											layer.close(index);
											data['workHour'] = value;
											updateState(30);
										});
									}else{
										layer.close(index);
										data['workHour'] = value;
										updateState(30);
									}
								});
							});
						}else{
							reloadTaskList();
							layer.closeAll();
						}
						
						function updateState(state){
							ajax.remoteCall("${ctxPath}/servlet/task?action=updateState",$.extend({},data,{'task.TASK_STATE':state}),function(result) { 
								if(result.state == 1){
									layer.msg(result.msg,{icon:1,time:1200},function(){
										reloadTaskList();
										layer.closeAll();
									});
								}else{
									layer.alert(result.msg,{icon: 5});
								}
							});
						}
						
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Task.updateData = function(flag) {
			var data = form.getJSONObject("#editForm");
			data['comment']='修改了任务';
			ajax.remoteCall("${ctxPath}/servlet/task?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadTaskList();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function changeModule(el){
			el.prev().val(el.find('option:selected').text())
		}
		
		function selctProjectCallBack(row){
			var projectId = row['PROJECT_ID']
			loadModule(projectId);
		}
		
		function loadModule(projectId){
			var el = $("#moduleId");
			el.render({data:{projectId:projectId},success:function(){
				$("#moduleId option:first").prop("selected", 'selected'); 
				changeModule(el);
			}});
		}
		
		function singleTask(el){
			var id = new Date().getTime();
			var projectId = '';
			$(el).attr('data-sid',id);
			popup.layerShow({id:'selectSj',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择任务',url:'/yq-work/pages/flow/select/select-task.jsp',data:{sid:id,type:'radio',projectId:projectId}});

		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>