package com.yunqu.work.service;

import java.io.File;
import java.sql.SQLException;
import java.util.List;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.FileModel;
import com.yunqu.work.model.FileRowMapper;

public class CommonService extends AppBaseService {
	private static class Holder{
		private static CommonService service=new CommonService();
	}
	public static CommonService getService(){
		return Holder.service;
	}
	
	public List<FileModel> getFileList(String fkId){
		if(StringUtils.notBlank(fkId)) {
			try {
				List<FileModel> list = this.getQuery().queryForList("select * from yq_files where fk_id = ?", new Object[] {fkId},new FileRowMapper());
				return list;
			} catch (SQLException e) {
				getLogger().error(e.getMessage(),e);
			}
		}
		return null;
	}
	
	public String getRealDiskPath(String path) {
		String basePath = AppContext.getContext(Constants.APP_NAME).getProperty("basePath", "/home/<USER>/");
		basePath =  basePath+"files/";
		File distFile =  new File(basePath+path);
	    if(distFile.isFile()) {
	    	return distFile.getAbsolutePath();
	    }
	    return null;
	}
	
	public File getFileByPath(String path) {
		String realPath = getRealDiskPath(path);
		if(StringUtils.notBlank(realPath)) {
			File distFile =  new File(realPath);
			if(distFile.isFile()) {
				return distFile;
			}
		}
		return null;
	}
	
	
}
