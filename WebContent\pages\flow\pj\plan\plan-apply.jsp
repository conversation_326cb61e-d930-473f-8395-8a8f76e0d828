<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				 		<div class="flow-btn">
				 			<a class="btn btn-info btn-sm layui-hide-xs plan-url" target="_blank" href="">填写计划</a>
				 		</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">评审编号</td>
					  			<td>
					  				<input type="hidden" name="apply.relatedFlow"/>
					  				<input type="text" onclick="selectReview(this)" placeholder="点击此选择" readonly="readonly" class="form-control input-sm select-icon" name="apply.data1"/>
					  			</td>
					  			<td class="required">合同类型</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">合同名称</td>
					  			<td>
					  				<input name="apply.data17" type="hidden"/>
					  				<input type="text" readonly="readonly" placeholder="点击此选择" onclick="singleContract(this);" class="form-control input-sm select-icon" name="apply.data3"/>
					  			</td>
					  			<td class="required">合同号</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">签约单位</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td>签订日期</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr class="unedit-remove">
					  			<td>项目任命流程</td>
					  			<td colspan="3">
					  				<input type="hidden" name="apply.data16"/>
					  				<input type="text" data-flow-code="pj_pm" id="pmFlowName" readonly="readonly" placeholder="关联项目经理任命流程"  onclick="FlowCore.selectRelatedFlow(this,'radio');" class="form-control input-sm"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">销售经理</td>
					  			<td>
					  				<input type="text" readonly="readonly" onclick="singleUser(this)" class="form-control input-sm select-icon" name="apply.data7"/>
					  			</td>
					  			<td class="required">项目经理</td>
					  			<td>
					  				<input type="text" data-rules="required" onclick="singleUser(this)" class="form-control input-sm select-icon" name="apply.data11"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">工程大区</td>
					  			<td>
					  				<input name="apply.data10" id="dn" type="hidden"/>
					  				<input type="text" onclick="singleDept(this);" data-dept-name="工程" data-ref-dept-leader-name="#dn" class="form-control input-sm select-icon" name="apply.data9"/>
					  			</td>
					  			<td>开发负责人</td>
					  			<td>
					  				<input type="text" onclick="multiUser(this)" class="form-control input-sm select-icon" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td>计划开工时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data12"/>
					  			</td>
					  			<td>计划上线时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data13"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td>计划初验时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" placeholder="正式合同需填写" class="form-control input-sm Wdate" name="apply.data14"/>
					  			</td>
					  			<td>计划终验时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" placeholder="正式合同需填写" class="form-control input-sm Wdate" name="apply.data15"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>计划情况说明</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" name="apply.data18"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr class="fileListTr">
					  			<td>相关附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  				<small class="ml-10 unedit-remove">项目启动会会议纪要附件,项目综合计划附件</small>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({approveUploadBtn:true,hideUserTitle:true,title:'项目计划-{{:data3}}',success:function(data){
				var param = FlowCore.params;
				if(param.handle=='add'){
					var json = getQueryParams();
					fillRecord(json,"apply.",",","#flowForm");
					queryContractInfo(getUrlParam('contractId'));
					queryProjectInfo(getUrlParam('contractId'));
					loadPmFlow(getUrlParam('contractId'));
					$('.plan-url').hide();
				}else{
					var applyInfo = FlowCore.applyInfo;
					var contractId = applyInfo['data17'];
					if(contractId){
						$('.plan-url').attr('href','/yq-work/project/'+contractId+'#计划');
					}else{
						$('.plan-url').hide();
					}
				}
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			Flow.initData();
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		Flow.initData = function(){
			var pmLeader = $("[name='apply.data11']").val();
			var params = {selectUserName:pmLeader,ccNames:'许志远,张明东,李千帆,陈莹'};
			FlowCore.approveData = params;
		}
		
		Flow.approveLayer = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='填写项目计划'){
				$('[name="ccIds"]').val('');
			}
			if(nodeCode=='发布计划'){
				$('[name="ccIds"]').val('');
			}
		}
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='部门主管'){
				var selectUserName = $("[name='apply.data7']").val();
				var params = {selectUserName:selectUserName};
				FlowCore.approveData = params;
				return true;
			}
			if(nodeCode=='填写项目计划'){
				var data12 = $("[name='apply.data12']").val();
				if(data12==''){
					layer.msg('填写计划开工时间',{icon:7,offset:'20px',time:1200});
					return false;
				}
				var data13 = $("[name='apply.data13']").val();
				if(data13==''){
					layer.msg('填写计划上线时间',{icon:7,offset:'20px',time:1200});
					return false;
				}
				//var data2 = $("[name='apply.data2']").val();
				//if(data2=='正式合同'){
				var data14 = $("[name='apply.data14']").val();
				if(data14==''){
					layer.msg('填写计划初验时间',{icon:7,offset:'20px',time:1200});
					return false;
				}
				var data15 = $("[name='apply.data15']").val();
				if(data15==''){
					layer.msg('填写计划终验时间',{icon:7,offset:'20px',time:1200});
					return false;
				}
				//}
				FlowCore.setting.uploadAuth = true;
				var selectUserName = $("[name='apply.data10']").val();
				var params = {selectUserName:selectUserName};
				FlowCore.approveData = params;
				
				var applyInfo = FlowCore.applyInfo;
				var params = {projectId:applyInfo['data17']};
				ajax.remoteCall("${ctxPath}/servlet/project/conf?action=getProjectPlan",params,function(result) { 
					if(result.state == 1){
						if(result.data.length>0){
							return true;
						}else{
							alert('请点击【填写计划】跳转到项目填写计划,若没填写权限请联系PMO');
							return false;
						}
					}else{
						layer.alert(result.msg,{icon: 5});
						return false;
					}
				},{async:false});
			}
			if(nodeCode=='发布计划'){
				var array = [];
				var data7 = $("[name='apply.data7']").val();
				var data8 = $("[name='apply.data8']").val();
				var data10 = $("[name='apply.data10']").val();
				var data11 = $("[name='apply.data11']").val();
				if(data7){
					array.push(data7.split(','));
				}
				if(data8){
					array.push(data8.split(','));
				}
				if(data10){
					array.push(data10.split(','));
				}
				if(data11){
					array.push(data11.split(','));
				}
				var params = {ccNames:array.join(','),ccIds:''};
				FlowCore.approveData = params;
			}
			return true;
	    }
		
		FlowCore.flowEnd = function(){
			var applyInfo = FlowCore.applyInfo;
			var data = {applyId:applyInfo['applyId'],contractId:applyInfo['data17'],cyDate:applyInfo['data14'],zyDate:applyInfo['data15'],sxDate:applyInfo['data13'],beginDate:applyInfo['data12']};
			ajax.remoteCall("${ctxPath}/servlet/project/conf?action=setProjectPlan",data,function(result) { 
				if(result.state == 1){
					
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});	
		}
		
		FlowCore.flowStart = function(flowResult){
			var applyId = flowResult.data;
			var contractId = $("[name='apply.data17']").val();
			var data = {applyId:applyId,contractId:contractId};
			ajax.remoteCall("${ctxPath}/servlet/project/conf?action=startProjectPlan",data,function(result) { 
				if(result.state == 1){
					
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});	
		}
		
		function selectReviewCallBack(row){
			queryContractInfoByReview(row['APPLY_NO']);
			/* $("[name='apply.data1']").val(row['APPLY_NO']);
			$("[name='apply.relatedFlow']").val(row['APPLY_ID']);
			
			$("[name='apply.data2']").val(row['CONTRACT_TYPE']);
			
			$("[name='apply.data5']").val(row['SIGN_ENT']);
			
			$("[name='apply.data7']").val(row['SALES_BY_NAME']); */
			
		}
		
		function selctCallBack(id,row){
			$("[name='apply.data3']").val(row['CONTRACT_NAME']);
			$("[name='apply.data4']").val(row['CONTRACT_NO']);
		}
		
		function selectReview(el){
			var id = new Date().getTime();
			$(el).attr('data-sid',id);
			popup.layerShow({id:'selectCust',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择评审',url:'/yq-work/pages/flow/select/select-review.jsp',data:{sid:id,type:'radio'}});
		}
		
		function selctFlowCallBack(row) {
		    $("[name='apply.data11']").val(row['DATA13']);
		    const data8 = [row['DATA11'], row['DATA12'], row['DATA15']]
		        .filter(value => value !== undefined && value !== null && value !== '')
		        .join(',');
		    $("[name='apply.data8']").val(data8);
		}
		
		function queryContractInfoByReview(reviewId){
			ajax.remoteCall(getCtxPath()+"/servlet/project/conf?action=queryContractIdByReviewNo",{reviewId:reviewId},function(result) { 
				var contractId = result.data;
				if(contractId){
					queryContractInfo(contractId);
				}else{
					layer.alert('未找到对应的合同设置的评审号'+reviewId,{icon:7,offset:'20px'});
				}
			});
		}
		
		function queryContractInfo(contractId){
			if(contractId){
				ajax.remoteCall(getCtxPath()+"/webcall?action=ProjectContractDao.record",{contractId:contractId},function(result) { 
					var reviewId = result.data['REVIEW_APPLY_ID'];
					ajax.remoteCall(getCtxPath()+"/webcall?action=ReviewDao.reviewInfo",{reviewId:reviewId},function(rs) { 
						readContractData(result.data,rs.data);
					});
				});
			}else{
				layer.alert('建议从合同管理入口申请此流程',{icon:7,offset:'20px'});
			}
		}
		
		function queryProjectInfo(contractId){
			if(contractId){
				ajax.remoteCall(getCtxPath()+"/webcall?action=ProjectDao.projectInfo",{contractId:contractId},function(result) { 
					$("[name='apply.data8']").val(result.data.PO_NAME);
					$("[name='apply.data11']").val(result.data.PROJECT_PO_NAME);
				});
			}
		}
		
		function readContractData(contractInfo,reviewInfo){
			$("[name='apply.data1']").val(contractInfo['REVIEW_ID']);
			$("[name='apply.relatedFlow']").val(contractInfo['REVIEW_APPLY_ID']);
			$("[name='apply.data3']").val(contractInfo['CONTRACT_NAME']);
			$("[name='apply.data4']").val(contractInfo['CONTRACT_NO']);
			$("[name='apply.data17']").val(contractInfo['CONTRACT_ID']);
			$("[name='apply.data5']").val(contractInfo['SIGN_ENT']);
			$("[name='apply.data6']").val(contractInfo['SIGN_DATE']);
			
			$("[name='apply.data2']").val(reviewInfo['CONTRACT_TYPE']);
			$("[name='apply.data7']").val(reviewInfo['SALES_BY_NAME']);
			
			
			$("[name='apply.data9']").val(contractInfo['PROJECT_DEPT_NAME']);
			
			var projectDeptId = contractInfo['PROJECT_DEPT_ID'];
			if(projectDeptId){
				getDeptInfo(projectDeptId,function(data){
					$("[name='apply.data10']").val(data.LEADER_NAME);
				});
			}else{
				layer.alert('合同未设置工程大区',{icon:7,offset:'20px',time:1200});
			}
			
		}
		
		function loadPmFlow(projectId){
			ajax.remoteCall("${ctxPath}/webcall?action=ProjectDataDao.projectFlowList",{'projectId':projectId},function(result) {
				var data = result.data;
				var businessId = data['pmApplyId'];
				if(businessId){
					ajax.remoteCall("${ctxPath}/webcall?action=FlowDao.applyInfo",{'businessId':businessId},function(rs) {
						var flowData = rs.data;
						if(flowData){
							$("[name='apply.data16']").val(businessId);
							$("#pmFlowName").val(flowData.APPLY_TITLE);
						}
					});
				}
			});
		}
		
		function getQueryParams() {
		  const searchParams = new URLSearchParams(window.location.search);
		  const paramsJson = {};
		  for (let param of searchParams.entries()) {
		    paramsJson[param[0]] = param[1];
		  }
		  return paramsJson;
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>