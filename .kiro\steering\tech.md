# Technology Stack

## Backend Framework
- **JFinal 5.2.3**: Lightweight Java web framework for rapid development
- **Java 8**: Target JDK version (1.8 compatibility)
- **Maven**: Build and dependency management
- **MySQL**: Primary database system
- **EhCache**: In-memory caching layer
- **Druid**: Database connection pool with monitoring

## Key Dependencies
- **JFinal Weixin 3.4**: WeChat and Enterprise WeChat integration
- **OkHttp 4.12.0**: Modern HTTP client for external API calls
- **CommonMark 0.21.0**: Markdown parsing and processing
- **Apache PDFBox 2.0.8**: PDF document processing and manipulation
- **JSoup 1.13.1**: HTML parsing and manipulation
- **UserAgentUtils 1.21**: User agent detection and parsing
- **Kotlin stdlib 1.9.0**: Kotlin language support
- **JavaMail API 1.5.6**: Email sending capabilities

## Frontend Technologies
- **ZUI**: Modern responsive UI framework
- **JSP**: Server-side templating engine
- **JavaScript/jQuery**: Client-side scripting and DOM manipulation
- **CSS3**: Modern styling and responsive design
- **WebSocket**: Real-time communication support

## AI Integration
- **GLM4**: Large language model integration
- **SiliconCloud AI**: Cloud-based AI services
- **DeepSeek API**: AI-powered text processing
- **Baidu OCR**: Optical character recognition

## Build System & Project Structure
Maven is used with a **custom directory structure**:
- **Source directory**: `src/` (instead of standard `src/main/java/`)
- **Resources directory**: `resource/` (instead of `src/main/resources/`)
- **Web content**: `WebContent/` (instead of `src/main/webapp/`)
- **Final artifact**: `yq-work.war`

## Common Commands

### Build and Package
```bash
# Clean and compile
mvn clean compile

# Package as WAR file
mvn clean package

# Skip tests during build (default behavior)
mvn clean package -Dmaven.test.skip=true

# Clean target directory
mvn clean
```

### Development and Deployment
```bash
# Deploy to Tomcat (manual)
# Copy target/yq-work.war to Tomcat webapps directory

# View Druid monitoring
# Access: http://localhost:8080/yq-work/servlet/monitor/druid/
```

## Configuration Files
- **`resource/config.txt`**: Main application configuration (database, WeChat, system parameters)
- **`resource/log4j.properties`**: Logging configuration and levels
- **`resource/ehcache.xml`**: Cache configuration and policies
- **`resource/oauth.properties`**: OAuth and third-party authentication
- **`resource/kdwebapi.properties`**: Kingdee ERP API integration
- **`WebContent/WEB-INF/web.xml`**: Web application configuration

## Environment Requirements
- **JDK**: Java 8 or higher
- **Maven**: 3.x for build management
- **Database**: MySQL 5.7+ 
- **Application Server**: Tomcat 8.5+ or compatible servlet container
- **Memory**: Minimum 2GB RAM recommended

## Deployment Architecture
- **Packaging**: WAR file deployment model
- **Session Management**: 720-minute session timeout
- **Security**: Form-based authentication with role-based access
- **Monitoring**: Druid connection pool monitoring
- **Error Handling**: Custom 404/500 error pages