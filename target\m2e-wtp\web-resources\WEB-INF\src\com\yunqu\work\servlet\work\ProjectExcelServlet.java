package com.yunqu.work.servlet.work;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/project/excel")
public class ProjectExcelServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	public JSONObject getSpanJson(int r,int c,String value,String extendStr) {
		JSONObject json = getSpanJson(r, c, value);
		String[] strs = extendStr.split(",");
		for(int i=0;i<strs.length;i++) {
			String str = strs[i];
			String[] config = str.split(":");
			json.put(config[0], config[1]);
		}
		return json;
	}
	
	public JSONObject getSpanJson(int r,int c,String value) {
		JSONObject span = new JSONObject();
		span.put("r", r);
		span.put("c", c);
		span.put("bg", "#f9f9f9");
        JSONObject vObject = new JSONObject();
        vObject.put("v", value);
        vObject.put("m", value);
        JSONObject ctObject = new JSONObject();
        ctObject.put("fa", "General");
        ctObject.put("t", "n");
        vObject.put("ct", ctObject);
        span.put("v", vObject);
        return span;
	}
	
	public void actionForProjectAllList(){ 
		EasySQL easySQL = new EasySQL("select t1.* from YQ_PROJECT t1 where 1=1");
		easySQL.append("order by t1.CREATE_TIME desc");
		JSONArray result = new JSONArray();
		try {
			List<JSONObject> list = this.getQuery().queryForList(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl());
			JSONArray array = new JSONArray();
			 array.add(getSpanJson(0,0,"项目名称","bl:1,fc:#990000,bg:#f5f5f5"));
			 array.add(getSpanJson(0,1,"项目编号","bl:1,fc:#990000,bg:#f5f5f5"));
			 array.add(getSpanJson(0,2,"项目经理","bl:1,bg:#f5f5f5"));
			 array.add(getSpanJson(0,3,"开发负责人","bl:1,bg:#f5f5f5"));
			 array.add(getSpanJson(0,4,"团队人员","bl:1,bg:#f5f5f5"));
			 array.add(getSpanJson(0,5,"任务数","bl:1,bg:#f5f5f5"));
			 array.add(getSpanJson(0,6,"开始时间","bl:1"));
			 array.add(getSpanJson(0,7,"创建时间","bl:1"));
			 array.add(getSpanJson(0,8,"项目阶段","bl:1"));
			 array.add(getSpanJson(0,9,"工时/人/天","bl:1"));
			 array.add(getSpanJson(0,10,"项目参与人","bl:1"));
			 array.add(getSpanJson(0,11,"工程大区","bl:1"));
			 array.add(getSpanJson(0,12,"合同类型","bl:1"));
			 array.add(getSpanJson(0,13,"销售","bl:1"));
			 array.add(getSpanJson(0,14,"售前","bl:1"));
			 array.add(getSpanJson(0,15,"初验日期","bl:1"));
			 array.add(getSpanJson(0,16,"终验日期","bl:1"));
			 array.add(getSpanJson(0,17,"上线日期","bl:1"));
			
			int r = 1;
			for(JSONObject data:list) {
				array.add(getSpanJson(r,0,data.getString("PROJECT_NAME")));
				array.add(getSpanJson(r,1,data.getString("PROJECT_NO")));
				array.add(getSpanJson(r,2,data.getString("PROJECT_PO_NAME")));
				array.add(getSpanJson(r,3,data.getString("PO_NAME")));
				array.add(getSpanJson(r,4,data.getString("PERSON_COUNT")));
		        array.add(getSpanJson(r,5,data.getString("TASK_COUNT")));
		        array.add(getSpanJson(r,6,data.getString("BEGIN_DATE")));
		        array.add(getSpanJson(r,7,data.getString("CREATE_TIME")));
		        array.add(getSpanJson(r,8,data.getString("PROJECT_STAGE")));
		        array.add(getSpanJson(r,9,data.getString("WORKHOUR_DAY")));
		        array.add(getSpanJson(r,10,data.getString("WORK_PEOPLE_COUNT")));
		        array.add(getSpanJson(r,11,data.getString("PROJECT_DEPT_NAME")));
		        array.add(getSpanJson(r,12,data.getString("CONTRACT_TYPE")));
		        array.add(getSpanJson(r,13,data.getString("SALES_BY_NAME")));
		        array.add(getSpanJson(r,14,data.getString("PRE_SALES_NAME")));
		        array.add(getSpanJson(r,15,data.getString("CY_DATE")));
		        array.add(getSpanJson(r,16,data.getString("ZY_DATE")));
		        array.add(getSpanJson(r,17,data.getString("SX_DATE")));
		        r++;
			}
			JSONObject row1 = new JSONObject();
			row1.put("name","ProjectData");
			row1.put("index",0);
			row1.put("order",0);
			row1.put("status",1);
			row1.put("celldata",array);
			result.add(row1);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		renderText(result.toJSONString());
	}
	
}
