<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">申请事项</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" placeholder="${staffInfo.userName}${staffInfo.staffNo}事项申请单"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">负责人</td>
					  			<td>
					  				<input name="apply.data2" type="hidden">
					  				<input type="text" readonly="readonly" onclick="multiUser(this)" class="form-control input-sm"  name="apply.data1"/>
					  			</td>
					  			<td>抄送人</td>
					  			<td>
					  				<input name="apply.data4" type="hidden">
					  				<input type="text" readonly="readonly" onclick="multiUser(this)" class="form-control input-sm"  name="apply.data3"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">风险描述</td>
					  			<td colspan="3">
									<textarea style="min-height: 100px;" data-rules="required" onchange="limitLenth(this,3000);" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">风险解除答复</td>
					  			<td colspan="3">
									<textarea style="min-height: 100px;" data-rules="required" onchange="limitLenth(this,3000);" class="form-control input-sm" name="apply.data7"></textarea>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideHistoryApply:true});
		});
		
		Flow.ajaxSubmitForm = function(state){
			var selectUserName = $("[name='apply.data1']").val();
			if(selectUserName==''){
				layer.msg('请选择审批人',{icon:7,time:800});
				return;
			}
			Flow.initData();
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData();
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='处理人回复'){
				var content = $("[name='apply.data7']").val();
				if(content==''||content.length<5){
					layer.msg('请填写答复内容(大于5个字符以上)',{icon:7,offset:'20px',time:1200});
					return false;
				}
			}
			return true;
	    }
		
		FlowCore.flowEnd = function(){
			var applyInfo = FlowCore.applyInfo;
			var content = $("[name='apply.data7']").val();
			var data = {applyId:applyInfo['applyId'],comment:content};
			ajax.remoteCall("${ctxPath}/servlet/project/conf?action=addRiskReply",data,function(result) { 
				if(result.state == 1){
					
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});	
		}
		
		Flow.initData = function(){
			var selectUserName = $("[name='apply.data1']").val();
			var selectUserId = $("[name='apply.data2']").val();
			var ccNames = $("[name='apply.data3']").val();
			var ccIds = $("[name='apply.data4']").val();
			var params = {selectUserId:selectUserId,selectUserName:selectUserName,ccNames:ccNames,ccIds:ccIds};
			FlowCore.approveData = params;
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>