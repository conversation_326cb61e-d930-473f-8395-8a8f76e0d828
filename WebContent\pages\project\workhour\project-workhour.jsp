<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
		.layui-table td, .layui-table th{padding: 9px 6px;}
		.layui-table input{width: 100%!important;}
		.stat-container{margin-bottom: 10px;padding: 10px;line-height: 24px;}
		.dept-item{
	    	line-height: 24px;
	    }
	    hr{
	    	padding: 5px 0px;
	    }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="editForm">
			<input type="hidden" name="projectId" value="${param.projectId}"/>
			<div class="ibox">
				<div class="ibox-content stat-container">
					<div class="layui-row" id="msg"></div>
				</div>
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		       <h5> 项目工作量统计（月）</h5>
					       <div class="input-group input-group-sm"  style="width: 150px;">
								<span class="input-group-addon">开始月份</span> 
								<input name="startDate" autocomplete="off"  data-mars="CommonDao.yearMonthBegin" data-mars-reload="false" id="startDate" data-mars-top="true" data-laydate="{type:'month'}" class="form-control input-sm Wdate">
							</div>
							<div class="input-group input-group-sm"  style="width: 150px;">
								<span class="input-group-addon">结束月份</span> 
								<input name="endDate" autocomplete="off" data-mars="CommonDao.nowMonthId" data-mars-reload="false" id="endDate" data-mars-top="true" data-laydate="{type:'month'}" class="form-control Wdate">
							</div>
							<div class="input-group input-group-sm">
							 	<button type="button" class="btn btn-sm btn-default" onclick="loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						   </div>
	          	     </div>
	              </div> 
	             <div class="ibox-content" style="min-height:calc(100vh - 100px)">
				    <div data-mars="WorkHourDao.projectWorkhourDetail" data-template="detailTpl" data-container="container" class="layui-row" id="container"></div>
	             </div>
				<script type="text/x-jsrender" id="detailTpl">
					<table class="layui-table">
					  <thead>
						<tr>
						  <th style="width:90px;">部门</th>
						  <th style="width:90px;">姓名</th>
						  <th style="width:90px;">工作月</th>
						  <th style="width:110px;">工作量(人/日)</th>
						  <th>内容描述</th>
						  <th style="width:80px;">填报者</th>
						  <th style="width:130px;">填写时间</th>
						</tr> 
					  </thead>
					  <tbody>
					  {{for data}}
						<tr>
						  <td> {{:DEPTS}}</td>
						  <td>{{:USERNAME}}</td>
						  <td>{{:MONTH_ID}}</td>
						  <td>{{:WORK_TIME}}</td>
						  <td>{{:WORK_RESULT}}</td>
						  <td>{{call:CREATOR fn='getUserName'}}</td>
						  <td>{{:CREATE_TIME}}</td>
						</tr>
					  {{/for}}
					   {{if data.length==0}}<tr><td  colspan="6">暂无数据</td></tr>{{/if}}
					  </tbody>
					</table>
						
				</script>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		function loadData(){
			$("#editForm").render({success:function(){
				renderDate();
			}});
		}
		$(function(){
			//设置默认月份
			loadData();
			loadMonthStat();
		});
		
		function loadMonthStat(){
			ajax.remoteCall("/yq-work/webcall?action=WorkHourDao.projectDeptWorkTimeSum",{projectId:'${param.projectId}'},function(result){
	 			var list = result.data;
	 			var sumTime = 0 ;
	 			var html = [];
	 			var i = 0;
	 			for(var index in list){
					var deptName = list[index]['DEPT_NAME'];
					if(deptName.indexOf("工程")>-1){
						var workTime = list[index]['WORK_TIME'];
						if(workTime>0){
							var _workTime = Number(workTime);
							_workTime = roundOne(_workTime);
							sumTime = sumTime + _workTime;
							if(i<30){
								html.push('<div class="layui-col-md3">'+deptName+"投入工时："+_workTime+'/人/天</div>');
							}
							i++;
						}
					}				
	 			}
	 			if(list.length==0){
	 				//$('.stat-container').hide();
	 			}else{
	 				sumTime  = roundOne(sumTime);
	 				$('#msg').append('<div class="layui-col-md3">工程总投入工时：'+sumTime+'/人/天</div>');
	 				$('#msg').append(html.join(''));
	 			}
	 			
	 			loadWeeklyStat(list);
	 			
	 		});
		}
		
		function roundOne(num) {
			  return Math.round(num * 10) / 10;
		}

		
		function loadWeeklyStat(monthHourData){
			ajax.remoteCall("/yq-work/webcall?action=WeeklyDao.projectWeeklyDeptStat",{projectId:'${param.projectId}'},function(result){
	 			var list = result.data;
	 			var sumTime = 0 ;
	 			for(var index in list){
	 				var deptName = list[index]['DEPT_NAME'];
					if(!deptName.includes("工程")){
		 				sumTime = sumTime + Number(list[index]['WORK_DAY']);
					}
	 			}
	 			var html = [];
	 			var i = 0;
	 			for(var index in list){
					var deptName = list[index]['DEPT_NAME'];
					if(!deptName.includes("工程")){
						var workday = Number(list[index]['WORK_DAY']);
						workDay = roundOne(workday);
						if(workday>0){
							html.push('<div class="layui-col-md3 dept-item">'+deptName+"投入工时："+workday+'/人/天('+calculatePercentage(workday,sumTime)+')</div>');
							i++;
						}
					}
	 			}
				if(monthHourData.length>0){
					$('#msg').append('<hr>');
				}
	 			if(monthHourData.length==0&&list.length==0){
	 				$('.stat-container').hide();
	 			}else{
	 				sumTime = roundOne(sumTime);
	 				$('#msg').append('<div class="layui-col-md3 dept-item">开发总投入工时：'+sumTime+'/人/天</div>');
	 				$('#msg').append(html.join(''));
	 			}
	 		});
		}
		
		function calculatePercentage(part, whole) {
			  if (whole === 0) {
			    return '不能除以0';
			  }
			  var percentage = (part / whole) * 100;
			  return percentage.toFixed(2) + '%';
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>