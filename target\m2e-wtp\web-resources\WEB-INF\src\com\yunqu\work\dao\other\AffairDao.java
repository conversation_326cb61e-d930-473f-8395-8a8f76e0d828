package com.yunqu.work.dao.other;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.AffairModel;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.LookLogService;

@WebObject(name="AffairDao")
public class AffairDao extends AppDaoContext {

	@WebControl(name="randomId",type=Types.TEXT)
	public JSONObject randomId(){
		return getJsonResult(FlowService.getService().getID(8));
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		AffairModel model=new AffairModel();
		model.setColumns(getParam("affair"));
		try {
			if(StringUtils.notBlank(model.getAffairId())) {
				JSONObject result = queryForRecord(model);
				JSONObject data = result.getJSONObject("data");
				String creator  = data.getString("CREATOR");
				if(StringUtils.notBlank(creator)&&!creator.equals(getUserId())&&!"100133".equals(request.getRemoteUser())) {
					LookLogService.getService().addLog(getUserId(), model.getAffairId(), "affair", request);
					this.getQuery().executeUpdate("update yq_affair set view_count = view_count + 1 where affair_id = ?", model.getAffairId());
				}
				return result;
			}
		} catch (SQLException e) {
			this.error(null, e);
		}		
		return getJsonResult(new JSONObject());
	}
	
	@WebControl(name="sendAffairList",type=Types.LIST)
	public JSONObject sendAffairList(){
		EasySQL sql=getEasySQL("select t1.* from yq_affair t1 where 1=1");
		sql.appendLike(param.getString("title"),"and t1.AFFAIR_NAME like ?");
		sql.append(getUserId(),"and t1.creator = ?");
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="receivedAffairList",type=Types.LIST)
	public JSONObject receivedAffairList(){
		EasySQL sql=getEasySQL("select t1.*,t2.obj_id,t2.user_name,t2.send_time,t2.look_time,t2.state from yq_affair t1,yq_affair_obj t2 where 1=1 and t1.affair_id = t2.affair_id and t1.affair_state = 20");
		sql.appendLike(param.getString("title"),"and t1.affair_name like ?");
		if("100133".equals(request.getRemoteUser())) {
			
		}else {
			sql.append(getUserId(),"and (t2.user_id = ? or ");
			sql.append(getUserId(),"FIND_IN_SET(?,t1.share_ids))");
		}
		sql.append("order by t1.update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="affairObjList",type=Types.LIST)
	public JSONObject affairObjList(){
		EasySQL sql=getEasySQL("select t1.* from yq_affair_obj t1 where 1=1 ");
		sql.append(param.getString("affairId"),"and t1.affair_id = ?");
		sql.append("order by t1.look_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="receivedCount",type=Types.TEXT)
	public JSONObject receivedCount(){
		EasySQL sql=getEasySQL("select count(1) from yq_affair t1,yq_affair_obj t2 where 1=1 and t1.affair_id = t2.affair_id and t2.state = 1 and t1.affair_state = 20");
		sql.append(getUserId(),"and t2.user_id = ?");
		try {
			int count = this.getQuery().queryForInt(sql.getSQL(), sql.getParams());
			return getJsonResult(count);
		} catch (SQLException e) {
			return getJsonResult("0");
		}
	}

			
}
