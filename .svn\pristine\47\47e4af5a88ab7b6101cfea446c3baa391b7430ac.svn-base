package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.Calendar;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.utils.BdOcrUtiles;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.WebSocketUtils;
import com.yunqu.work.utils.WeekUtils;

public class JobExcute extends AppBaseService implements Job {

	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
			Calendar calendar=Calendar.getInstance();
			
			int month = calendar.get(Calendar.MONTH)+1;
			int week = calendar.get(Calendar.DAY_OF_WEEK);//周日 1，周一 2 周六是7
			int day = calendar.get(Calendar.DAY_OF_MONTH);
			int hour = calendar.get(Calendar.HOUR_OF_DAY);
			int minute = calendar.get(Calendar.MINUTE);
			
			getLogger().info("job>>month:"+month+",week:"+week+",day:"+day+",hour:"+hour+",minute:"+minute);
			
			TaskNoticeService.getService().affairSpecifiedTmNotice();
			
		   if(minute==0) {
			if(hour%2==0&&hour<23&&hour>7){
				try {
					//更新任务创建部门ID
					this.getQuery().executeUpdate("UPDATE yq_task t1 JOIN "+Constants.DS_MAIN_NAME+".easi_dept_user t3 ON t1.CREATOR = t3.USER_ID JOIN "+Constants.DS_MAIN_NAME+".easi_dept t2 ON t3.DEPT_ID = t2.DEPT_ID SET t1.dept_id = t2.DEPT_ID");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				try {
					//更新任务负责人部门ID
					this.getQuery().executeUpdate("UPDATE yq_task t1 JOIN "+Constants.DS_MAIN_NAME+".easi_dept t2 ON t1.assign_dept_id = t2.DEPT_ID JOIN "+Constants.DS_MAIN_NAME+".easi_dept_user t3 ON t1.assign_user_id = t3.USER_ID AND t2.DEPT_ID = t3.DEPT_ID SET t1.assign_dept_id = t2.DEPT_ID");
					
					this.getQuery().executeUpdate("update yq_task t1,"+Constants.DS_MAIN_NAME+".easi_user t2 set t1.assign_user_name = t2.USERNAME where t1.assign_user_id = t2.USER_ID");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新合同的客户ID
					this.getQuery().executeUpdate("update yq_crm_customer t1,yq_project_contract t2 set t2.CUST_ID = t1.CUST_ID where t2.CUST_NO = t1.CUST_NO");
				
					//更新项目名称
					this.getQuery().executeUpdate("UPDATE yq_project AS t1 INNER JOIN yq_project_contract AS t2 ON t1.contract_id = t2.contract_id SET t1.project_name = t2.contract_simpile_name, t1.project_no = t2.contract_no WHERE t1.project_name <> t2.contract_simpile_name");
				
					//更新项目人员
					this.getQuery().executeUpdate("UPDATE yq_project AS t1 INNER JOIN yq_project_contract AS t2 ON t1.contract_id = t2.contract_id SET t1.sales_by = t2.sales_by, t1.sales_by_name = t2.sales_by_name, t1.pre_sales_by = t2.PM_BY, t1.pre_sales_name = t2.PM_BY_NAME");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新任务的文件数
					this.getQuery().executeUpdate("update yq_task t1 set t1.file_count = (select count(1) from yq_files t2 where t2.FK_ID=t1.task_id) where month_id = ?",EasyCalendar.newInstance().getFullMonth());
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				try {
					//更新跟进记录的文件数
					this.getQuery().executeUpdate("update yq_crm_follow t1 set t1.file_count = (select count(1) from yq_files t2 where t2.FK_ID=t1.follow_id)");
					
					//更新周报
					this.getQuery().executeUpdate("update yq_weekly t1 set t1.file_count = (select count(1) from yq_files t2 where t2.fk_id = t1.weekly_id) where t1.week_end > ?",DateUtils.getPlanMaxDay(-20));
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新商机统计字段
					this.getQuery().executeUpdate("UPDATE yq_crm_business t1 LEFT JOIN( SELECT business_id, SUM(follow_day) AS follow_day_sum FROM yq_crm_follow GROUP BY business_id) t2 ON t1.business_id = t2.business_id SET t1.follow_day = IFNULL(t2.follow_day_sum, 0)");
					this.getQuery().executeUpdate("UPDATE yq_crm_business t1 LEFT JOIN( SELECT business_id, COUNT(*) AS follow_count FROM yq_crm_follow GROUP BY business_id) t2 ON t1.business_id = t2.business_id SET t1.follow_count = IFNULL(t2.follow_count, 0)");
					this.getQuery().executeUpdate("UPDATE yq_crm_business t1 INNER JOIN( SELECT business_id, MIN(follow_begin) AS min_follow_begin, MAX(follow_end) AS max_follow_end FROM yq_crm_follow GROUP BY business_id) t2 ON t1.business_id = t2.business_id SET t1.first_follow_date = t2.min_follow_begin, t1.last_follow_date = t2.max_follow_end");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新项目的任务数 项目最后任务跟进时间 总工时
					this.getQuery().executeUpdate("update yq_project t1 set t1.task_count = (select count(1) from yq_task t3 where t3.PROJECT_ID= t1.PROJECT_ID)");
					this.getQuery().executeUpdate("update yq_project t1 set t1.last_task_time = (select max(create_time) from yq_task t2 where t1.project_id = t2.project_id)");
					this.getQuery().executeUpdate("UPDATE yq_project t1 JOIN(SELECT project_id, SUM(work_day) AS total_work_day FROM yq_weekly_project GROUP BY project_id) t2 ON t1.project_id = t2.project_id SET t1.workhour_day = t2.total_work_day");
					this.getQuery().executeUpdate("update yq_project t1 set t1.work_people_count =(select count(DISTINCT(t2.CREATOR)) from yq_weekly_project t2 where t2.project_id = t1.project_id GROUP BY t2.project_id HAVING sum(t2.work_day)>10)");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新登录时间
					this.getQuery().executeUpdate("update yq_user_info t1,"+Constants.DS_MAIN_NAME+".easi_user_login t2  set t1.last_login_time=t2.last_login_time where t1.user_id=t2.user_id");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				try {
					//更新基本信息
					this.getQuery().executeUpdate("update yq_user_info t1,"+Constants.DS_MAIN_NAME+".easi_user t2  set t1.email=t2.email,t1.open_id=t2.open_id,t1.dept_name=t2.depts,t1.head_img = t2.PIC_URL,t1.post=t2.data2 where t1.user_id=t2.user_id");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				try {
					//更新部门信息
					this.getQuery().executeUpdate("update yq_user_info t1,"+Constants.DS_MAIN_NAME+".easi_dept_user t2  set t1.dept_id=t2.dept_id where t1.user_id=t2.user_id");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				//周报统计
				WeeklyStatService.getService().run();
			}
			
			if(!ServerContext.isLinux()){
				return;
			}
			
			if(!Constants.isProd()) {
				return;
			}
			
			if(hour==22) {
				ZendaoService.getService().updateData();
			}
			
			if(hour==9) {
				StaffService.getService().sendJoinYearEmail();
				StaffService.getService().sendBirthdayEmail();
				FlowService.getService().overtimeFlowNotice();
				ZendaoService.getService().syncProject();
			}
			
			
			if(hour==10) {
				FlowService.getService().todoFlowNotice();
				TaskNoticeService.getService().affairMonthOfDayNotice();
			}
			
			if(hour==17) {
				String sql  = "select creator,begin_time,end_time,title,todo_desc from yq_todo where `status`=1 and date_id = DATE_FORMAT(DATE_ADD(NOW(),INTERVAL 1 DAY),'%Y%m%d')";
				WeeklyNoticeService.getService().noticeTodo(sql);
			}
			if(hour==8) {
				String sql  = "select creator,begin_time,end_time,title,todo_desc from yq_todo where `status`=1 and date_id = "+EasyCalendar.newInstance().getDateInt();
				WeeklyNoticeService.getService().noticeTodo(sql);
			}
			
			if(week==6&&hour==17){//周五五点提醒写周报
				WeeklyNoticeService.getService().run(WeekUtils.getWeekNo());
				TaskNoticeService.getService().createTask();
			}
			if(week==7&&hour==20){//周六晚上八点提醒写周报
				WeeklyNoticeService.getService().run(WeekUtils.getWeekNo());
			}
			if(week==1&&hour==20){//周日晚上八点九点提醒写周报
				WeeklyNoticeService.getService().run(WeekUtils.getWeekNo());
			}
			
			if(week==2&&hour==9){//周一上午9点提醒写周报
				WeeklyNoticeService.getService().run(WeekUtils.getWeekNo()-1);
				WeeklyNoticeService.getService().sendDeptWeeklyToLeader(WeekUtils.getWeekNo()-1,true);
			}
			
			if(week==2&&hour==15){//周一下午3点提醒写周报
				WeeklyNoticeService.getService().run(WeekUtils.getWeekNo()-1);
				WeeklyNoticeService.getService().sendDeptWeeklyToLeader(WeekUtils.getWeekNo()-1,true);
				TaskNoticeService.getService().createTask();
			}
			
			if(week==3&&hour==9){//周二上午9点提醒没填写的人
				WeeklyNoticeService.getService().sendDeptWeeklyToLeader(WeekUtils.getWeekNo()-1,true);
			}
			
			if((week==4||week==2||week==6)&&hour==10){//周一或周三上午10点提醒任务上报
				TaskNoticeService.getService().wxMsgWaitTask();
			}
			
			if(week==2&&hour==10){//周一上午10点
//				WeeklyNoticeService.getService().sendAllWeeklyToLeader(WeekUtils.getWeekNo()-1);
			}
			
			if(week==2&&hour==10){//周一下午2点
				WeeklyNoticeService.getService().noticeDeptWork();
			}
			
			if(week==1&&hour==20){//每周日晚上20点给主管汇报填写周报情况
				WeeklyNoticeService.getService().sendDeptWeeklyToLeader(WeekUtils.getWeekNo());
			}
			
			
			if(week==2&&hour==10){//每周一的10点项目经理给相关人汇报自己负责的部门情况
				//周报的项目情况，
				WeeklyNoticeService.getService().sendProjectWeeklyToPm(WeekUtils.getWeekNo()-1);
			}
			
			if(hour==7){
				UpdateBookService.getService().run();
				BdOcrUtiles.clearData();
			}
			
			if(hour==7||hour==13){
				//OAContractSynService.getService().synData();
				//OACustSynService.getService().synData();
			}
			if(hour==13||hour==20){
				ZendaoService.getService().run();
				ZendaoService.getService().newZendao();
				MemosService.getService().run();
				
				try {
					this.getQuery().executeUpdate("update yq_finance_bx_invoice t1,yq_finance_xml_invoice t2 set t1.total_amount = t2.total_amount,t1.amount_in_figuers = t2.amount_in_figuers,t1.total_tax = t2.total_tax,t1.tax_rate = t2.tax_rate,t1.kp_remark = t2.kp_remark where t1.invoice_no = t2.invoice_no");
				} catch (SQLException e) {
					this.getLogger().error(e.getMessage(),e);
				}
//				ProjectService.getService().updateAllTeams();
			}
			
			if(hour==9&&week==4){
				TaskNoticeService.getService().overtimeTask();
			}
			if(week==6&&hour==16){
				TaskNoticeService.getService().waitTask();
			}
			if(week==6&&hour==15){
				TaskNoticeService.getService().checkTask();
			}
			
			if((day==30||day==15)&&week==6&&hour==17) {
				//周5上报考勤数据
				KqNoticeService.getService().deptKqData();
			}
			
			if(hour==10||hour==15) {
				try {
					this.getMainQuery().executeUpdate("update yq_staff_info t1,easi_user t2 set t1.account_state = t2.state where t1.staff_user_id = t2.USER_ID");
					
					this.getMainQuery().executeUpdate("update yq_staff_info t1,easi_dept_user t2 set t1.dept_id = t2.DEPT_ID where t1.staff_user_id = t2.USER_ID");
					
					this.getMainQuery().executeUpdate("update yq_staff_info t1 set t1.p_dept_id  = (select DEPT_ID from easi_dept t3 where t3.dept_code =  (select P_DEPT_CODE from easi_dept t2 where t1.dept_id = t2.dept_id))");
					
					this.getMainQuery().executeUpdate("update yq_staff_info t1 ,easi_dept t2 set t1.p_superior = t2.LEADER where t1.p_dept_id = t2.dept_id");
				
					this.getMainQuery().executeUpdate("update EASI_USER t1,easi_dept t2,easi_dept_user t3 set t1.DEPTS = t2.DEPT_NAME where t1.USER_ID = t3.USER_ID and t2.DEPT_ID = t3.DEPT_ID");
					
					this.getMainQuery().executeUpdate("update easi_user t1 set t1.ROLES = (select GROUP_CONCAT(t2.ROLE_NAME) from easi_role t2 ,easi_role_user t3 where t2.ROLE_ID = t3.ROLE_ID and t1.USER_ID = t3.USER_ID)");
				
					this.getMainQuery().executeUpdate("update yq_staff_info t1,easi_user t2 set t1.py_name = getPY(t2.USERNAME) where t1.staff_user_id = t2.USER_ID");
				} catch (SQLException e) {
					this.getLogger().error(null,e);
				}
			}
			if(hour==22) {
				NextCloudService.getService().run();
				
				YApiService.getService().regUser();
				
				StaffService.getService().clearCache();
					
			}
			if(hour==1) {
				WeeklyNoticeService.getService().createProjectWorkHour();
				if(KqNoticeService.getService().kqFlag()) {
					KqNoticeService.getService().addKqRecord(null);
				}
			}
			
			if((day==30||day==1)&&hour==9) {
				//项目计划提醒
				PlanNoticeService.getService().run();
			}
			
			if(day==15&&hour==1) {
				WeeklyNoticeService.getService().createProjectWorkHour(String.valueOf(DateUtils.getPlanMaxDay(17)).substring(0, 6),null);
			}
			
			if((day==1||day==2||day==3||day==10)&&hour==10) {
//				WeeklyNoticeService.getService().workhourNotice(DateUtils.getPrevMonth(),null);
			}
			
			if(hour==9||hour==12||hour==18) {
				MsgModel model=new MsgModel();
				model.setSenderType(2);
				model.setTitle("时间提醒");
				model.setMsg("当时时间："+hour+"点");
				WebSocketUtils.sendAllMessage(model);
			}
		}
		   
		if(minute==30&&hour==9) {
			if(week!=0&&week!=1){
				MsgModel model=new MsgModel();
				model.setSenderType(2);
				model.setTitle("今日点餐提醒");
				model.setMsg("请及时点餐,如已点或不在公司请忽略此提醒.");
				WebSocketUtils.sendAllMessage(model);
		   }
		}
		
		if(minute==50&&hour==9) {
			//推送点餐消息给每个人
			if(KqNoticeService.getService().kqFlag()) {
				DcNoticeService.getService().sendDcNotice(0);
			}else {
				this.getLogger().info("kqFlag 今天不上班");
			}
		}
		if(minute==0&&hour==10) {
			//推送点餐消息给HR
			if(KqNoticeService.getService().kqFlag()) {
				DcNoticeService.getService().sendDcNotice(1);
			}else {
				this.getLogger().info("kqFlag 今天不上班");
			}
		}
		
		if(hour>=8 && hour<=20 && (minute == 10 || minute == 40)){
			ContractStatService.getService().updateContractReceiptStat();
		}

		if(hour>=8 && hour<=20 && minute == 20){
			ContractStatService.getService().updateContractIncomeStat();
		}
		
		if(hour==8&&minute==50) {
			if(KqNoticeService.getService().kqFlag()) {
				KqNoticeService.getService().sbDkNotice();
			}
		}
		if(hour==18&&minute==30) {
			if(KqNoticeService.getService().kqFlag()) {
				KqNoticeService.getService().xbDkNotice();
			}
		}
	}

}
