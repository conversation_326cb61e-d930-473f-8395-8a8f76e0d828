<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectContactUnitForm">
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
					 <span class="input-group-addon">名称</span>	
					 <input type="text" name="unitName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectContactUnit.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		</div>
            	<div class="ibox">
              		<table id="contactUnitList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectContactUnit.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectContactUnit={
					sid:'${param.sid}',
					query : function(){
						$("#selectContactUnitForm").queryData();
					},
					initTable : function(){
						$("#selectContactUnitForm").initTable({
							mars:'BxDao.contactUnit',
							id:'contactUnitList',
							page:true,
							limit:20,
							cellMinWidth:150,
							rowDoubleEvent:'SelectContactUnit.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'UNIT_NAME',
								title: '公司名'
							},{
							    field: 'UNIT_NO',
								title: '编号'
							},{
							    field: 'BANK_NAME',
								title: '银行名'
							},{
							    field: 'BANK_ACOUNT',
								title: '银行账号'
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectContactUnit.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('contactUnitList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['UNIT_NAME']);
									ids.push(data[index]['UNIT_NO']);
									$("[name='business.CONTACT_BANK_NO']").val(data[index]['BANK_ACOUNT']);
									$("[name='business.CONTACT_BANK']").val(data[index]['BANK_NAME']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								popup.layerClose("selectContactUnitForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							el.val(selectRow['UNIT_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['UNIT_NO']);
							}
							$("[name='business.CONTACT_BANK_NO']").val(selectRow['BANK_ACOUNT']);
							$("[name='business.CONTACT_BANK']").val(selectRow['BANK_NAME']);
							popup.layerClose("selectContactUnitForm");
						}
					}
					
			};
			$(function(){
				SelectContactUnit.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>