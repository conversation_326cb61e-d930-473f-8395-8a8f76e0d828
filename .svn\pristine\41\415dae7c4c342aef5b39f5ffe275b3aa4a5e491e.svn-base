package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;

public class NextCloudService extends AppBaseService{

	private static class Holder{
		private static NextCloudService service=new NextCloudService();
	}
	public static NextCloudService getService(){
		return Holder.service;
	}
	
	public void run() {
		syn251GroupData();
		syn251UserData();
		
		
		syn252GroupData();
		syn252UserData();
	}
	public void syn251GroupData() {
		try {
			EasyQuery query251 = EasyQuery.getQuery("nextcloud-251");
			List<JSONObject>  groupList  = query251.queryForList("select * from oc_groups",new Object[] {},new JSONMapperImpl());
		    String[] groupNames = new String[groupList.size()];
		    int i = 0;
			for(JSONObject object:groupList) {
				groupNames[i] = object.getString("GID");
				i++;
		    }

			EasySQL sql = new EasySQL("select * from easi_dept where length(DEPT_CODE) = 6");
			sql.appendIn(groupNames,"and dept_name not");
		    List<JSONObject>  deptList  = this.getMainQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
		    if(deptList!=null&&deptList.size()>0) {
		    	for(JSONObject object : deptList) {
		    		EasyRecord record = new EasyRecord("oc_groups","gid");
		    		record.set("gid",object.getString("DEPT_NAME"));
		    		record.set("displayname",object.getString("DEPT_NAME"));
		    		query251.save(record);
		    	}
		    }
			
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	public void syn251UserData() {
		try {
			EasyQuery query251 = EasyQuery.getQuery("nextcloud-251");
			List<JSONObject>  userList  = query251.queryForList("select * from oc_users",new Object[] {},new JSONMapperImpl());
			String[] userAccts = new String[userList.size()];
			int i = 0;
			for(JSONObject object:userList) {
				userAccts[i] = object.getString("UID");
				i++;
			}
			
			EasySQL sql = new EasySQL("select t2.staff_no,t1.USERNAME,t1.EMAIL from easi_user t1,yq_staff_info t2 where t1.USER_ID = t2.staff_user_id and t1.STATE = 0");
			sql.appendIn(userAccts,"and t2.staff_no not");
			
			List<JSONObject>  staffList  = this.getMainQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(staffList!=null&&staffList.size()>0) {
				for(JSONObject object : staffList) {
					EasyRecord record = new EasyRecord("oc_users","uid");
					record.set("uid",object.getString("STAFF_NO"));
					record.set("uid_lower",object.getString("STAFF_NO"));
					record.set("password","3|$argon2id$v=19$m=65536,t=4,p=1$cVlZNzhtdHAzL2RicldPeQ$985t4o5+Rq3UkhWUXGhGW7n+H2F4FdaDEiZCALtN74w");
					record.set("displayname",object.getString("USERNAME"));
					query251.save(record);
				}
			}
			
			
			
			
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	public void syn252UserData() {
		try {
			EasyQuery query252 = EasyQuery.getQuery("nextcloud-252");
			List<JSONObject>  userList  = query252.queryForList("select * from oc_users",new Object[] {},new JSONMapperImpl());
			String[] userAccts = new String[userList.size()];
			int i = 0;
			for(JSONObject object:userList) {
				userAccts[i] = object.getString("UID");
				i++;
			}
			
			EasySQL sql = new EasySQL("select t2.staff_no,t1.USERNAME,t1.EMAIL from easi_user t1,yq_staff_info t2 where t1.USER_ID = t2.staff_user_id and t1.STATE = 0");
			sql.appendIn(userAccts,"and t2.staff_no not");
			
			List<JSONObject>  staffList  = this.getMainQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(staffList!=null&&staffList.size()>0) {
				for(JSONObject object : staffList) {
					EasyRecord record = new EasyRecord("oc_users","uid");
					record.set("uid",object.getString("STAFF_NO"));
					record.set("uid_lower",object.getString("STAFF_NO"));
					record.set("password","3|$argon2id$v=19$m=65536,t=4,p=1$cVlZNzhtdHAzL2RicldPeQ$985t4o5+Rq3UkhWUXGhGW7n+H2F4FdaDEiZCALtN74w");
					record.set("displayname",object.getString("USERNAME"));
					query252.save(record);
				}
			}
			
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	
	public void syn252GroupData() {
		try {
			EasyQuery query252 = EasyQuery.getQuery("nextcloud-252");
			
			List<JSONObject>  groupList  = query252.queryForList("select * from oc_groups",new Object[] {},new JSONMapperImpl());
			String[] groupNames = new String[groupList.size()];
			int i = 0;
			for(JSONObject object:groupList) {
				groupNames[i] = object.getString("GID");
				i++;
			}
			
			EasySQL sql = new EasySQL("select * from easi_dept where length(DEPT_CODE) = 6");
			sql.appendIn(groupNames,"and dept_name not");
			List<JSONObject>  deptList  = this.getMainQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(deptList!=null&&deptList.size()>0) {
				for(JSONObject object : deptList) {
					EasyRecord record = new EasyRecord("oc_groups","gid");
					record.set("gid",object.getString("DEPT_NAME"));
					record.set("displayname",object.getString("DEPT_NAME"));
					query252.save(record);
				}
			}
			
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
	}
	
	
}
