package com.yunqu.work.servlet.flow;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.IndexedColors;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.NumberToCN;
@WebServlet("/servlet/bx/fun/*")
public class YqBxFunServlet extends BaseFlowServlet {
	
	private static final long serialVersionUID = 1L;
	
	
	
	public EasyResult actionForUpdateHzMoney() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		BigDecimal hzAmount = params.getBigDecimal("hzAmount");
		BigDecimal taxes = params.getBigDecimal("taxes");
		BigDecimal rAmount = params.getBigDecimal("rAmount");
		String businessId = params.getString("businessId");
		try {
			
			this.getQuery().executeUpdate("update yq_flow_bx_item set hz_amount = ?,R_AMOUNT = ?,TAXES = ? where item_id = ?",hzAmount,rAmount,taxes,itemId);
			
			
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.hz_money = (select sum(t2.hz_amount) from yq_flow_bx_item t2 where t1.business_id = t2.business_id) where t1.business_id = ?",businessId);
			String num = this.getQuery().queryForString("select t1.hz_money - t1.reverse_money from yq_flow_bx_base t1 where t1.business_id = ?", businessId);
			
			String payMoneyDx = NumberToCN.number2CNMontrayUnit(new BigDecimal(num));
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.pay_money = ?,t1.pay_money_dx = ? where t1.business_id = ?",num,payMoneyDx,businessId);
			
			addReviseLog(businessId,itemId, "核准金额调整>"+rAmount);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateReverseMoney() {
		JSONObject params = getJSONObject();
		BigDecimal reverseMoney = params.getBigDecimal("reverseMoney");
		String businessId = params.getString("businessId");
		try {
			
			this.getQuery().executeUpdate("update yq_flow_bx_base set reverse_money = ? where business_id = ?",reverseMoney,businessId);
			String num = this.getQuery().queryForString("select t1.hz_money - t1.reverse_money from yq_flow_bx_base t1 where t1.business_id = ?", businessId);
			String payMoneyDx = NumberToCN.number2CNMontrayUnit(new BigDecimal(num));
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.pay_money = ?,t1.pay_money_dx = ? where t1.business_id = ?",num,payMoneyDx,businessId);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdatePayState() {
		JSONObject params = getJSONObject();
		String date = params.getString("date");
		JSONArray array = params.getJSONArray("array");
		try {
			String payTime = EasyDate.getCurrentDateString();
			int y = array.size();
			int len = array.size();
			for(int i=0;i<len;i++) {
				JSONObject row  = array.getJSONObject(i);
				EasySQL sql = new EasySQL();
				
				double thisMoney = row.getDoubleValue("thisMoney");
				if(thisMoney<0) {
					continue;
				}
				sql.append(10,"update yq_flow_bx_base set pay_state = ?");// 0 流程中 1 待付款 10已付款 11部分付款
				sql.append(payTime, ",pay_time = ?");
				sql.append(y, ",pay_index = ?");
				sql.append(thisMoney,",has_pay_money = has_pay_money + ?");
				sql.append(date,",pay_date = ?");
				sql.append("where 1=1");
				sql.append(row.getString("id"),"and business_id = ?");
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
				
				sql = new EasySQL();
				sql.append(11,"update yq_flow_bx_base set pay_state = ?");
				sql.append("where has_pay_money < pay_money");
				sql.append(row.getString("id"),"and business_id = ?");
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
				
				y = y -1;
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateBxRecorded() {
		JSONObject params = getJSONObject();
		String date = params.getString("date");
		JSONArray array = params.getJSONArray("array");
		try {
			for(int i=0;i<array.size();i++) {
				JSONObject row  = array.getJSONObject(i);
				EasySQL sql = new EasySQL();
				sql.append(1,"update yq_flow_bx_base set recorded = ?");
				sql.append(EasyDate.getCurrentTimeStampString(), ",recorded_time = ?");
				sql.append(date,",recorded_date = ?");
				sql.append("where 1=1");
				sql.append(row.getString("id"),"and business_id = ?");
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForReturnFlow() {
		JSONObject params = getJSONObject();
		String businessId = params.getString("businessId");
		if(StringUtils.notBlank(businessId)) {
			try {
				this.getQuery().executeUpdate("update yq_flow_apply set apply_state = 20 where apply_id = ?", businessId);
				this.getQuery().executeUpdate("update yq_flow_bx_base set pay_state = 0 where business_id = ?", businessId);
				this.getQuery().executeUpdate("UPDATE yq_flow_approve_result SET check_result = 0,check_time = '',check_desc = '' WHERE result_id = ( SELECT result_id FROM ( SELECT result_id FROM yq_flow_approve_result WHERE business_id = ? AND check_result = 1 ORDER BY check_time DESC LIMIT 1 ) AS t )", businessId);
				this.addReviseLog(businessId, "退回到上一步审批");
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		return EasyResult.ok();
	}
	
	
   public EasyResult actiionForAddPaperState() {
		  JSONObject params = getJSONObject();
		  String businessId = params.getString("businessId");
		  if(StringUtils.notBlank(businessId)) {
			try {
				this.getQuery().executeUpdate("update yq_flow_bx_base set paper_state = ? where business_id = ?",1,businessId);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		  }
		  return EasyResult.ok();
	}
	
   public EasyResult actionForGetPaperState() {
	   JSONObject params = getJSONObject();
		String businessId = params.getString("businessId");
		if(StringUtils.notBlank(businessId)) {
			try {
				this.getQuery().executeUpdate("update yq_flow_bx_base set paper_state = ? where business_id = ?",2,businessId);
				this.addReviseLog(businessId, "接收到纸质报销文件");
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		return EasyResult.ok();
   }
   
   public EasyResult actionForNoticeSendPaper() {
	   JSONObject params = getJSONObject();
	   String businessIds = params.getString("ids");
	   if(StringUtils.notBlank(businessIds)) {
		   String[] array = businessIds.split(",");
		   for(String businessId:array) {
			   MessageModel model = new MessageModel();
			   FlowApplyModel apply = ApproveService.getService().getApply(businessId);
			   model.setTitle("【费用报销】"+apply.getApplyTitle());
			   model.setReceiver(apply.getApplyBy());
			   model.setSender(getUserId());
			   model.setDesc("请尽快将纸质报销凭证（报销单号：【"+apply.getApplyNo ()+"】）提交至财务部，若逾期提交，可能会影响您的报销进度。");
			   EmailService.getService().sendEmail(model);
//			   WxMsgService.getService().sendFlowTodoCheck(model);
			   
			   this.addReviseLog(businessId, "纸质报销单据催提交提醒");
			   
		   }
		   EasySQL sql = new EasySQL();
		   sql.append("update yq_flow_bx_base set paper_notice_count = paper_notice_count + 1 where 1=1");
		   sql.appendIn(array, "and business_id");
		   try {
			   this.getQuery().executeUpdate(sql.getSQL(), sql.getParams());
			} catch (SQLException e) {
				e.printStackTrace();
			}
	   }
	   return EasyResult.ok();
   }
   
   public EasyResult actionForRemovePaper() {
	   JSONObject params = getJSONObject();
	   String businessIds = params.getString("ids");
	   if(StringUtils.notBlank(businessIds)) {
		   String[] array = businessIds.split(",");
		   for(String businessId:array) {
				try {
					this.getQuery().executeUpdate("update yq_flow_bx_base set paper_state = ? where business_id = ?",9,businessId);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
		   }
	   }
	   return EasyResult.ok();
   }
	
	
	public void actionForExportPdfInvoice(){
		String invoiceIds = getPara("invoiceIds");
		String applyNo = getPara("applyNo");
		if(StringUtils.isBlank(applyNo)) {
			applyNo = RandomKit.orderId();
		}
		EasySQL sql = new EasySQL("select t2.disk_path,t2.file_name from yq_finance_bx_invoice t1,yq_files t2 where t1.file_id = t2.file_id");
		sql.appendIn(invoiceIds.split(","),"and t1.invoice_id");
		try {
			String zipFileName = applyNo+".zip";
			// 创建输出流
		    FileOutputStream fos = new FileOutputStream(zipFileName);
		    ZipOutputStream zos = new ZipOutputStream(fos);
			
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			for(JSONObject row:list) {
				String diskPath = row.getString("DISK_PATH");
				String fileName = row.getString("FILE_NAME");
				String filePath = getBaseDir()+File.separator+diskPath;
				
				if(!new File(filePath).exists()) {
					continue;
				}
				
				// 创建输入流
		        FileInputStream fis = new FileInputStream(filePath);
		        // 创建ZipEntry对象，设置文件名
		        ZipEntry zipEntry = new ZipEntry(fileName);
		        // 将ZipEntry对象加入到ZipOutputStream中
		        zos.putNextEntry(zipEntry);

		        // 读取文件内容并写入到ZipOutputStream中
		        byte[] bytes = new byte[1024];
		        int length;
		        while ((length = fis.read(bytes)) >= 0) {
		            zos.write(bytes, 0, length);
		        }
		        // 关闭输入流
		        fis.close();
				
			}
			
			// 关闭ZipOutputStream和FileOutputStream
		    zos.closeEntry();
		    zos.close();
		    fos.close();
		    
		    
		    // 下载zip文件
		    HttpServletResponse response = this.getResponse();
		    response.setContentType("application/zip");
		    response.setHeader("Content-Disposition", "attachment; filename=\"" + zipFileName + "\"");
		    FileInputStream fis = new FileInputStream(zipFileName);
		    OutputStream os = response.getOutputStream();
		    byte[] buffer = new byte[4096];
		    int bytesRead;
		    while ((bytesRead = fis.read(buffer)) != -1) {
		        os.write(buffer, 0, bytesRead);
		    }
		    os.flush();
		    os.close();
		    fis.close();
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			renderHtml(e.getMessage());
		} catch (FileNotFoundException e) {
			this.error(e.getMessage(), e);
			renderHtml(e.getMessage());
		} catch (IOException e) {
			this.error(e.getMessage(), e);
			renderHtml(e.getMessage());
		}
		
	}
	
	
	public void actionForExportBxInvoice(){
		String _data = getPara("data");
		JSONObject param = JSONObject.parseObject(_data);
		List<String> headers=new ArrayList<String>();
		try {
			EasySQL sql =  new EasySQL();
			sql.append("select  t1.apply_no,t1.apply_remark,t1.apply_name,t1.apply_time,t1.apply_staff_no,");
			sql.append("t2.bx_money,t2.recorded_date,t2.pay_type,t2.pay_money,t2.reverse_money,t2.hz_money,t2.contact_code,t2.contact_unit,t2.bx_dept_name,");
			sql.append("t3.invoice_no,t3.invoice_type,t3.invoice_type_code,t3.invoice_date,t3.TOTAL_AMOUNT,t3.tax_rate,t3.AMOUNT_IN_FIGUERS,t3.TOTAL_TAX,t3.SALE_COMPANY_NO,t3.SALE_COMPANY,t3.BUY_COMPANY_NO,t3.BUY_COMPANY,t3.INVOICE_CODE");
			sql.append("from yq_flow_apply t1,yq_flow_bx_base t2,yq_finance_bx_invoice t3 where t1.apply_id = t2.business_id and t2.business_id = t3.business_id");
			sql.append(param.getString("flowCode"),"and t1.flow_code = ?");
			sql.append(param.getString("applyName")," and t1.apply_name = ?");
			String applyState = param.getString("applyState");
			if("100".equals(applyState)) {
				sql.append(10,"and t1.apply_state >= ?");
			}else {
				sql.append(applyState,"and t1.apply_state = ?");
			}
			
			sql.append(param.getString("recorded")," and t2.recorded = ?");
			sql.append(param.getString("payBeginDate"),"and t2.pay_date >= ?");
			sql.append(param.getString("payEndDate"),"and t2.pay_date <= ?");
			sql.append(param.getString("beginDate"),"and t2.recorded_date >= ?");
			sql.append(param.getString("endDate"),"and t2.recorded_date <= ?");
			
			sql.append("ORDER BY t1.apply_end_time desc,t1.apply_time desc");
			
			File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
			/**创建头部 */		
			
		   headers.add("入账日期");
		   headers.add("单号");
		   headers.add("申请人");
		   headers.add("发票类型");
		   headers.add("发票号");
		   headers.add("发票代码（非数电发票时必录）");	
		   headers.add("开票日期");
		   headers.add("发票金额");
		   headers.add("核准金额");
		   headers.add("税率(%)");
		   headers.add("发票金额(不含税)");
		   headers.add("发票税金");
		   headers.add("销售方纳税人识别号");
		   headers.add("销售方纳税人名称");
		   headers.add("购买方纳税人识别号");
		   headers.add("购买方纳税人名称");		   

			List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
			int x=0;
			for(String header:headers){
				ExcelHeaderStyle style = new ExcelHeaderStyle();
				style.setData(header);
				style.setWidth(4000);
				style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
				styles.add(style);
				x++;
			}
			/**数据***/
			List<List<String>> excelData=new ArrayList<List<String>>();
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			
			
			if(list!=null && list.size()>0){
				for (int i = 0; i < list.size(); i++) {
					JSONObject map = list.get(i);
					List<String> row=new ArrayList<String>();
					
					row.add(map.getString("RECORDED_DATE"));
					row.add(map.getString("APPLY_NO"));
					row.add(map.getString("APPLY_NAME"));
					row.add(map.getString("INVOICE_TYPE"));
					row.add(map.getString("INVOICE_NO"));
					row.add(map.getString("INVOICE_CODE"));
					row.add(map.getString("INVOICE_DATE"));
					row.add(map.getString("TOTAL_AMOUNT"));
					row.add("--");
					row.add(getTaxRate(map.getString("TAX_RATE")));
					row.add(map.getString("AMOUNT_IN_FIGUERS"));
					row.add(map.getString("TOTAL_TAX"));
					row.add(map.getString("SALE_COMPANY_NO"));
					row.add(map.getString("SALE_COMPANY"));
					row.add(map.getString("BUY_COMPANY_NO"));
					row.add(map.getString("BUY_COMPANY"));										
					excelData.add(row);
				}
			}
			
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			String fileName="云趣费用报销-数电发票明细.xlsx";
			renderFile(file,fileName,true);
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
	}
	
	private String getTaxRate(String taxRate){
		if(StringUtils.isBlank(taxRate)) {
			return "";
		}
		if(taxRate.equals("0")) {
			return "0";
		}
		if(taxRate.contains("%")) {
			return taxRate.substring(0,taxRate.length()-1);
		}
		if(taxRate.contains(".")) {
			try {
				double rate = Double.parseDouble(taxRate);
				return String.valueOf((int)(rate * 100));
			} catch(NumberFormatException e) {
				this.error(e.getMessage(), e);
				return taxRate;
			}
		}
		return taxRate;
	}
	
	public void actionForExportBx(){
		String _data = getPara("data");
		JSONObject param = JSONObject.parseObject(_data);
		List<String> headers=new ArrayList<String>();
		try {
			EasySQL sql =  new EasySQL();
			sql.append("select  t1.apply_no,t1.apply_remark,t1.apply_name,t1.apply_time,t1.apply_staff_no,");
			sql.append("t2.bx_money,t2.recorded_date,t2.pay_type,t2.pay_money,t2.reverse_money,t2.hz_money,t2.contact_code,t2.contact_unit,t2.bx_dept_name,");
			sql.append("t3.* from yq_flow_apply t1,yq_flow_bx_base t2,yq_flow_bx_item t3 where t1.apply_id = t2.business_id and t2.business_id = t3.business_id");
			sql.append(param.getString("flowCode"),"and t1.flow_code = ?");
			sql.append(param.getString("applyName")," and t1.apply_name = ?");
			String applyState = param.getString("applyState");
			if("100".equals(applyState)) {
				sql.append(10,"and t1.apply_state >= ?");
			}else {
				sql.append(applyState,"and t1.apply_state = ?");
			}
			String exportSource = param.getString("exportSource");
			if("sdInvoice".equals(exportSource)) {
				sql.appendLike("数电","and t3.INVOICE_TYPE like ?");
			}
			
			sql.append(param.getString("recorded")," and t2.recorded = ?");
			sql.append(param.getString("payBeginDate"),"and t2.pay_date >= ?");
			sql.append(param.getString("payEndDate"),"and t2.pay_date <= ?");
			sql.append(param.getString("beginDate"),"and t2.recorded_date >= ?");
			sql.append(param.getString("endDate"),"and t2.recorded_date <= ?");
			
			sql.append("ORDER BY t1.apply_end_time desc,t1.apply_time desc,t3.item_index");
			
			File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
			/**创建头部*/
			headers.add("入账日期");
			headers.add("申请人");
			headers.add("工号");
			headers.add("部门");
			headers.add("单号");
			headers.add("申请时间");
			headers.add("说明");
			headers.add("付款方式");
			headers.add("往来单位编号");
			headers.add("往来单位");
			headers.add("费用总计");
			headers.add("冲账金额");
			headers.add("应付金额");
			
			headers.add("序号");
			headers.add("费用日期");
			headers.add("费用类型(内部)");
			headers.add("费用类型");
			headers.add("费用说明");
			headers.add("发票类型");
			headers.add("发票号");
			headers.add("金额");
			headers.add("核准金额");
			headers.add("部门");
			headers.add("项目号");
			headers.add("项目ERP号");
			headers.add("产品线");
			headers.add("科目编号");
			headers.add("科目名称");
			headers.add("税率(%)");
			headers.add("税金");
			headers.add("金额(不含税)");
			
			List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
			int x=0;
			for(String header:headers){
				ExcelHeaderStyle style=new ExcelHeaderStyle();
				style.setData(header);
				if(x<13) {
					style.setWidth(5000);
					style.setBackgroundColor(IndexedColors.BLUE_GREY.index);
				}else {
					style.setWidth(4000);
					style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
				}
				styles.add(style);
				x++;
			}
			/**数据***/
			List<List<String>> excelData=new ArrayList<List<String>>();
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			
			String gBusinessId = null;
			
			if(list!=null && list.size()>0){
				for (int i = 0; i < list.size(); i++) {
					JSONObject map = list.get(i);
					List<String> row=new ArrayList<String>();
					String businessId = map.getString("BUSINESS_ID");
					if(!businessId.equals(gBusinessId)||gBusinessId==null) {
						row.add(map.getString("RECORDED_DATE"));
						row.add(map.getString("APPLY_NAME"));
						row.add(map.getString("APPLY_STAFF_NO"));
						row.add(map.getString("BX_DEPT_NAME"));
						row.add(map.getString("APPLY_NO"));
						row.add(map.getString("APPLY_TIME"));
						row.add(map.getString("APPLY_REMARK"));
						row.add(map.getString("PAY_TYPE"));
						row.add(map.getString("CONTACT_CODE"));
						row.add(map.getString("CONTACT_UNIT"));
						row.add(map.getString("HZ_MONEY"));
						row.add(map.getString("REVERSE_MONEY"));
						row.add(map.getString("PAY_MONEY"));
					}else {
						for(int y = 0;y<13;y++) {
							row.add("");
						}
					}
					gBusinessId = businessId;
					
					
					row.add(map.getString("ITEM_INDEX"));
					row.add(map.getString("BX_DATE"));
					row.add(map.getString("FEE_IN_TYPE"));
					row.add(map.getString("FEE_TYPE"));
					row.add(map.getString("FEE_DESC"));
					row.add(map.getString("INVOICE_TYPE"));
					row.add(map.getString("INVOICE_NO"));
					row.add(map.getString("AMOUNT"));
					row.add(map.getString("HZ_AMOUNT"));
					row.add(map.getString("DEPT_NAME"));
					row.add(map.getString("CONTRACT_NO"));
					row.add(map.getString("CONTRACT_NO"));
					row.add(map.getString("PRODUCT_LINE"));
					row.add(map.getString("SUBJECT_NO"));
					row.add(map.getString("SUBJECT_NAME"));
					row.add(map.getString("TAX_RATE"));
					row.add(map.getString("TAXES"));
					row.add(map.getString("R_AMOUNT"));
					
					
					excelData.add(row);
				}
			}
			
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			String fileName="云趣费用报销清单.xlsx";
			renderFile(file,fileName,true);
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
	}
	
	public void actionForExportBxInvoiceExcel(){
		String _data = getPara("data");
		JSONObject param = JSONObject.parseObject(_data);
		List<String> headers=new ArrayList<String>();
		try {
			EasySQL sql = new EasySQL("select t1.* from yq_finance_bx_invoice t1 where 1=1");
			sql.append(param.getString("bxUserId"),"and t1.creator = ?");
			sql.appendRLike(param.getString("invoiceType"),"and t1.invoice_type_code like ?");
			sql.appendLike(param.getString("invoiceNo"),"and t1.invoice_no like ?");
			sql.appendLike(param.getString("saleCompany"),"and t1.sale_company like ?");
			sql.append(param.getString("bxState"),"and t1.bx_state = ?");
			
			String selfData = param.getString("selfData");
			if("1".equals(selfData)) {
				sql.append(getUserId(),"and t1.creator = ?");
				
				String invoiceIds = param.getString("invoiceIds");
				if(StringUtils.notBlank(invoiceIds)) {
					sql.appendIn(invoiceIds.split(","),"and t1.invoice_id");
				}
			}
			
			sql.append("ORDER BY t1.create_time desc");
			
			File file = FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
			/**创建头部*/
			headers.add("发票号");
			headers.add("含税金额");
			headers.add("不含税");
			headers.add("税额");
			headers.add("税率");
			headers.add("开票日期");
			headers.add("开票类型");
			headers.add("开票方");
			headers.add("购买方");
			headers.add("开票备注");
			headers.add("发票文件");
			headers.add("导入时间");
			headers.add("导入人");
			List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
			for(String header:headers){
				ExcelHeaderStyle style=new ExcelHeaderStyle();
				style.setData(header);
				style.setWidth(4000);
				style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
				styles.add(style);
			}
			/**数据***/
			List<List<String>> excelData=new ArrayList<List<String>>();
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			
			if(list!=null && list.size()>0){
				for (int i = 0; i < list.size(); i++) {
					JSONObject map = list.get(i);
					List<String> row=new ArrayList<String>();
					row.add(map.getString("INVOICE_NO"));
					row.add(map.getString("TOTAL_AMOUNT"));
					row.add(map.getString("AMOUNT_IN_FIGUERS"));
					row.add(map.getString("TOTAL_TAX"));
					row.add(map.getString("TAX_RATE"));
					row.add(map.getString("INVOICE_DATE"));
					row.add(map.getString("INVOICE_TYPE"));
					row.add(map.getString("SALE_COMPANY"));
					row.add(map.getString("BUY_COMPANY"));
					row.add(map.getString("KP_REMARK"));
					row.add(map.getString("FILE_NAME"));
					row.add(map.getString("CREATE_TIME"));
					row.add(map.getString("CREATE_NAME"));
					excelData.add(row);
				}
			}
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			String fileName="云趣费用报销发票清单.xlsx";
			renderFile(file,fileName,true);
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
	}
	
	public void actionForExportBxInvoiceFile(){
		String _data = getPara("data");
		JSONObject param = JSONObject.parseObject(_data);
		try {
			EasySQL sql =  new EasySQL();
			sql.append("select t1.invoice_id,t1.invoice_no,t2.disk_path pdf_disk_path,t3.disk_path xml_disk_path,t2.file_name pdf_file_name,t3.file_name xml_file_name from yq_finance_bx_invoice t1 INNER JOIN yq_files t2 on t1.file_id = t2.file_id LEFT JOIN yq_files t3 on t3.file_id = t1.xml_file_id where 1=1");
			sql.append(param.getString("bxUserId"),"and t1.creator = ?");
			sql.appendRLike(param.getString("invoiceType"),"and t1.invoice_type_code like ?");
			sql.appendLike(param.getString("invoiceNo"),"and t1.invoice_no like ?");
			sql.appendLike(param.getString("saleCompany"),"and t1.sale_company like ?");
			sql.append(param.getString("bxState"),"and t1.bx_state = ?");
			
			String selfData = param.getString("selfData");
			if("1".equals(selfData)) {
				sql.append(getUserId(),"and t1.creator = ?");
				
				String invoiceIds = param.getString("invoiceIds");
				if(StringUtils.notBlank(invoiceIds)) {
					sql.appendIn(invoiceIds.split(","),"and t1.invoice_id");
				}
			}
			
			sql.append("ORDER BY t1.create_time desc");
			
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			
			
			if(list!=null && list.size()>0){
				String zipFileName = EasyCalendar.newInstance().getDateInt()+"_发票凭证("+list.size()+").zip";
				FileOutputStream fos = new FileOutputStream(zipFileName);
				ZipOutputStream zos = new ZipOutputStream(fos);
				
				for (int i = 0; i < list.size(); i++) {
					JSONObject row = list.get(i);
					String invoiceNo = row.getString("INVOICE_NO");
					String pdfDiskPath = row.getString("PDF_DISK_PATH");
					String xmlDiskPath = row.getString("XML_DISK_PATH");
					String pdfFileName = row.getString("PDF_FILE_NAME");
					String xmlFileName = row.getString("XML_FILE_NAME");
					if(StringUtils.isBlank(pdfDiskPath)&&StringUtils.isBlank(xmlDiskPath)) {
						continue;
					}
					String newPdfFileName = invoiceNo+"_"+pdfFileName;
					String pdfFilePath = getBaseDir()+File.separator+pdfDiskPath;
					
					if(!new File(pdfFilePath).exists()) {
						continue;
					}
					// 创建输入流
			        FileInputStream fis = new FileInputStream(pdfFilePath);
			        // 创建ZipEntry对象，设置文件名
			        ZipEntry zipEntry = new ZipEntry(newPdfFileName);
			        // 将ZipEntry对象加入到ZipOutputStream中
			        zos.putNextEntry(zipEntry);

			        // 读取文件内容并写入到ZipOutputStream中
			        byte[] bytes = new byte[1024];
			        int length;
			        while ((length = fis.read(bytes)) >= 0) {
			            zos.write(bytes, 0, length);
			        }
			        // 关闭输入流
			        fis.close();
			        
			        if(StringUtils.notBlank(xmlFileName)) {
			        	String newxmlFileName = invoiceNo+"_"+xmlFileName;
			        	String xmlFilePath = getBaseDir()+File.separator+xmlDiskPath;
			        	if(new File(xmlFilePath).exists()) {
			        		// 创建输入流
			        		FileInputStream fis2 = new FileInputStream(xmlFilePath);
			        		// 创建ZipEntry对象，设置文件名
			        		ZipEntry zipEntry2 = new ZipEntry(newxmlFileName);
			        		// 将ZipEntry对象加入到ZipOutputStream中
			        		zos.putNextEntry(zipEntry2);
			        		
			        		// 读取文件内容并写入到ZipOutputStream中
			        		byte[] bytes2 = new byte[1024];
			        		int length2;
			        		while ((length2 = fis2.read(bytes2)) >= 0) {
			        			zos.write(bytes2, 0, length2);
			        		}
			        		// 关闭输入流
			        		fis2.close();
			        	}
			        	
			        }
			        
				}
				// 关闭ZipOutputStream和FileOutputStream
			    zos.closeEntry();
			    zos.close();
			    fos.close();
			    
			    
			    // 下载zip文件
			    HttpServletResponse response = this.getResponse();
			    response.setContentType("application/zip");
			    response.setHeader("Content-Disposition", "attachment;"+Render.encodeFileName(getRequest(), zipFileName));
			    FileInputStream fis = new FileInputStream(zipFileName);
			    OutputStream os = response.getOutputStream();
			    byte[] buffer = new byte[4096];
			    int bytesRead;
			    while ((bytesRead = fis.read(buffer)) != -1) {
			        os.write(buffer, 0, bytesRead);
			    }
			    os.flush();
			    os.close();
			    fis.close();
			}else {
				renderHtml("暂无数据");
			}
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
			renderHtml(ex.getMessage());
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			renderHtml(e.getMessage());
		}
	}
	
	
	public void actionForExportBxXml(){
		String _data = getPara("data");
		JSONObject param = JSONObject.parseObject(_data);
		try {
			EasySQL sql =  new EasySQL();
			sql.append("select t1.apply_no,t2.recorded_date,t4.invoice_no,t5.disk_path");
			sql.append(" from yq_flow_apply t1,yq_flow_bx_base t2,yq_flow_bx_item t3,yq_finance_bx_invoice t4,yq_files t5 where");
			sql.append("t1.apply_id = t2.business_id and t2.business_id = t3.business_id and t4.bx_item_id = t3.item_id and t5.file_id = t4.xml_file_id");
			sql.append(param.getString("flowCode"),"and t1.flow_code = ?");
			sql.append(param.getString("applyName")," and t1.apply_name = ?");
			String applyState = param.getString("applyState");
			if("100".equals(applyState)) {
				sql.append(10,"and t1.apply_state >= ?");
			}else {
				sql.append(applyState,"and t1.apply_state = ?");
			}
			sql.append(param.getString("recorded")," and t2.recorded = ?");
			sql.append(param.getString("payBeginDate"),"and t2.pay_date >= ?");
			sql.append(param.getString("payEndDate"),"and t2.pay_date <= ?");
			sql.append(param.getString("beginDate"),"and t2.recorded_date >= ?");
			sql.append(param.getString("endDate"),"and t2.recorded_date <= ?");
			
			sql.append("ORDER BY t1.apply_end_time desc,t1.apply_time desc,t3.item_index");
			
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			
			
			if(list!=null && list.size()>0){
				String zipFileName = EasyCalendar.newInstance().getDateInt()+"_数电发票("+list.size()+").zip";
				FileOutputStream fos = new FileOutputStream(zipFileName);
				ZipOutputStream zos = new ZipOutputStream(fos);
				
				for (int i = 0; i < list.size(); i++) {
					JSONObject row = list.get(i);
					String diskPath = row.getString("DISK_PATH");
					String recordedDate = row.getString("RECORDED_DATE");
					if(StringUtils.isBlank(recordedDate)||StringUtils.isBlank(diskPath)) {
						continue;
					}
					recordedDate = recordedDate.replaceAll("-","");
					String fileName = recordedDate+"_"+row.getString("APPLY_NO")+"_"+row.getString("INVOICE_NO")+".xml";
					String filePath = getBaseDir()+File.separator+diskPath;
					
					// 创建输入流
			        FileInputStream fis = new FileInputStream(filePath);
			        // 创建ZipEntry对象，设置文件名
			        ZipEntry zipEntry = new ZipEntry(fileName);
			        // 将ZipEntry对象加入到ZipOutputStream中
			        zos.putNextEntry(zipEntry);

			        // 读取文件内容并写入到ZipOutputStream中
			        byte[] bytes = new byte[1024];
			        int length;
			        while ((length = fis.read(bytes)) >= 0) {
			            zos.write(bytes, 0, length);
			        }
			        // 关闭输入流
			        fis.close();
				}
				
				// 关闭ZipOutputStream和FileOutputStream
			    zos.closeEntry();
			    zos.close();
			    fos.close();
			    
			    
			    // 下载zip文件
			    HttpServletResponse response = this.getResponse();
			    response.setContentType("application/zip");
			    response.setHeader("Content-Disposition", "attachment;"+Render.encodeFileName(getRequest(), zipFileName));
			    FileInputStream fis = new FileInputStream(zipFileName);
			    OutputStream os = response.getOutputStream();
			    byte[] buffer = new byte[4096];
			    int bytesRead;
			    while ((bytesRead = fis.read(buffer)) != -1) {
			        os.write(buffer, 0, bytesRead);
			    }
			    os.flush();
			    os.close();
			    fis.close();
			}else {
				renderHtml("暂无数据");
			}
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
			renderHtml(ex.getMessage());
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			renderHtml(e.getMessage());
		}
	}

	@Override
	public EasyResult actionForSubmitApply() {
		return null;
	}

	@Override
	public EasyResult actionForUpdateApply() {
		return null;
	}
}
