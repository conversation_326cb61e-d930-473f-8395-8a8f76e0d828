 package com.yunqu.work.dao.project;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="KanbanDao")
public class KanbanDao extends AppDaoContext {

	@WebControl(name="projectKanban",type=Types.TEMPLATE)
	public JSONObject projectKanban(){
		EasySQL sql=getEasySQL("select t1.*,t2.project_name from yq_kanban t1 inner join yq_project t2 on t2.project_id = t1.project_id where 1=1");
		String  projectId = param.getString("projectId");
		if(StringUtils.isBlank(projectId)){
			sql.append(getUserId(),"and t1.creator = ?");
		}
		sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
		sql.append("order by t1.create_time desc");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="persionKanban",type=Types.TEMPLATE)
	public JSONObject persionKanban(){
		EasySQL sql=getEasySQL("select t1.* from yq_kanban t1  where t1.project_id = 0 ");
		sql.append(getUserId(),"and t1.creator = ?");
		sql.append("order by t1.create_time desc");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="panelItemInfo",type=Types.RECORD)
	public JSONObject panelItemInfo(){
		EasySQL sql=getEasySQL("select t1.* from yq_kanban_panel_item t1  where 1=1 ");
		sql.append(param.getString("itemId"),"and t1.item_id = ?",false);
		sql.append("order by t1.create_time");
		return this.queryForRecord(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="panelInfo",type=Types.RECORD)
	public JSONObject panelInfo(){
		EasySQL sql=getEasySQL("select t1.* from yq_kanban_panel t1  where 1=1 ");
		sql.append(param.getString("panelId"),"and t1.panel_id = ?",false);
		return this.queryForRecord(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="kanbanInfo",type=Types.RECORD)
	public JSONObject kanbanInfo(){
		EasySQL sql=getEasySQL("select t1.* from yq_kanban t1  where 1=1 ");
		sql.append(param.getString("kanbanId"),"and t1.kanban_id = ?",false);
		return this.queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="kanbanPanel",type=Types.TEMPLATE)
	public JSONObject kanbanPanel(){
		EasySQL sql=getEasySQL("select t1.* from yq_kanban_panel t1  where 1=1");
		sql.append(param.getString("kanbanId"),"and t1.kanban_id = ?",false);
		sql.append("order by t1.order_index");
		
		JSONObject result = this.queryForList(sql.getSQL(), sql.getParams());
		
		
		EasySQL itemSql=getEasySQL("select t1.* from yq_kanban_panel_item t1  where 1=1");
		itemSql.append(param.getString("kanbanId"),"and t1.kanban_id = ?",false);
		sql.append("order by t1.create_time,t1.order_index");
		
		try {
			result.put("items", this.getQuery().queryForList(itemSql.getSQL(), itemSql.getParams(), new JSONMapperImpl()));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		return result;
		
	}
	
	
}
