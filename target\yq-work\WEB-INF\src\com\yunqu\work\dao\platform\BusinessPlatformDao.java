package com.yunqu.work.dao.platform;

import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * 平台业务DAO
 */
@WebObject(name="BusinessPlatformDao")
public class BusinessPlatformDao extends AppDaoContext {

    @WebControl(name="getTree",type=Types.TREE)
    public JSONObject getTree(){
        return queryForList("SELECT PLATFORM_TYPE_ID,P_PLATFORM_TYPE_ID,PLATFORM_TYPE_NAME FROM YQ_BUSINESS_PLATFORM_TYPE ORDER BY IDX_ORDER",new Object[]{});
    }

    @WebControl(name="getPlatformType",type=Types.RECORD) 
    public JSONObject getPlatformType(){
        String pk = param.getString("pk");
        EasyRecord record = new EasyRecord("YQ_BUSINESS_PLATFORM_TYPE","PLATFORM_TYPE_ID").setPrimaryValues(pk);
        return queryForRecord(record);
    }

    @WebControl(name="getPlatform",type=Types.RECORD)
    public JSONObject getPlatform(){
        EasySQL sql = this.getEasySQL("select t1.*,t2.PLATFORM_TYPE_NAME from YQ_BUSINESS_PLATFORM t1 LEFT JOIN yq_business_platform_type t2 on t1.PLATFORM_TYPE_ID =t2.PLATFORM_TYPE_ID");
        if(!StringUtils.isBlank(param.getString("platformId"))){
            sql.append(param.getString("platformId"), " where t1.PLATFORM_ID = ?");
            return queryForRecord(sql.getSQL(), sql.getParams());
        }
        JSONObject resultJson = new JSONObject();
        resultJson.put("data", new JSONObject());
        return resultJson;
    }

    @WebControl(name="platformList",type=Types.LIST) 
    public JSONObject platformList(){
        String platformTypeId = param.getString("platformTypeId");
        String platformName = param.getString("platformName");
        
        EasySQL sql = this.getEasySQL("select t1.*,t2.PLATFORM_TYPE_NAME from YQ_BUSINESS_PLATFORM t1 , YQ_BUSINESS_PLATFORM_TYPE t2 where t1.PLATFORM_TYPE_ID=t2.PLATFORM_TYPE_ID ");
        if(!"0".equals(platformTypeId)) {
            sql.append(platformTypeId,"and ( t1.PLATFORM_TYPE_ID = ?");
            sql.append(platformTypeId,"or t2.P_PLATFORM_TYPE_ID = ? )");
        }
        sql.appendLike(platformName, " and t1.PLATFORM_NAME like ?");
        sql.append(param.getInteger("status"),"and t1.STATUS = ?");
        sql.append("order by t2.IDX_ORDER,t2.PLATFORM_TYPE_ID,t1.IDX_ORDER ASC");
        return this.queryForPageList(sql.getSQL(),sql.getParams(),null);
    }

    @WebControl(name="platformStatList",type=Types.LIST)
    public JSONObject platformStatList(){
        String platformTypeId = param.getString("platformTypeId");
        String platformName = param.getString("platformName");

        EasySQL sql = this.getEasySQL("SELECT t1.*,t2.PLATFORM_TYPE_NAME,IFNULL(t3.ORDER_COUNT,0) AS ORDER_COUNT,IFNULL(t4.INVOICE_AMOUNT,0) AS YEAR_INVOICE_AMOUNT,IFNULL(t5.SJ_COUNT,0) AS SJ_COUNT FROM YQ_BUSINESS_PLATFORM t1 INNER JOIN YQ_BUSINESS_PLATFORM_TYPE t2 on t1.PLATFORM_TYPE_ID=t2.PLATFORM_TYPE_ID LEFT JOIN( SELECT PLATFORM_ID,COUNT(1) AS ORDER_COUNT FROM yq_crm_platform_order GROUP BY PLATFORM_ID)t3 ON t3.PLATFORM_ID = t1.PLATFORM_ID");
        sql.appendLike(EasyCalendar.newInstance().getYear(),"LEFT JOIN(SELECT PLATFORM_ID,SUM(MARK_MONEY) AS INVOICE_AMOUNT FROM yq_crm_invoice WHERE MONTH_ID LIKE ? GROUP BY PLATFORM_ID ) t4 ON t4.PLATFORM_ID = t1.PLATFORM_ID");
        sql.append("LEFT JOIN (SELECT PLATFORM_ID,COUNT(1) AS SJ_COUNT FROM yq_crm_business GROUP BY PLATFORM_ID ) t5 ON t5.PLATFORM_ID = t1.PLATFORM_ID");
        sql.append("WHERE 1=1");
        if(!"0".equals(platformTypeId)) {
            sql.append(platformTypeId,"and ( t1.PLATFORM_TYPE_ID = ?");
            sql.append(platformTypeId,"or t2.P_PLATFORM_TYPE_ID = ? )");
        }
        sql.appendLike(platformName, " and t1.PLATFORM_NAME like ?");
        sql.append(param.getInteger("status"),"and t1.STATUS = ?");
        sql.append("order by t2.IDX_ORDER,t2.PLATFORM_TYPE_ID,t1.IDX_ORDER ASC");
        return this.queryForPageList(sql.getSQL(),sql.getParams(),null);
    }

    @WebControl(name = "getPlatformTypeDict", type = Types.DICT)
    public JSONObject getPlatformTypeDict() {
        EasySQL sql = new EasySQL("select PLATFORM_TYPE_ID, PLATFORM_TYPE_NAME from YQ_BUSINESS_PLATFORM_TYPE where P_PLATFORM_TYPE_ID = '0' order by IDX_ORDER");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "getPlatformDict", type = Types.DICT)
    public JSONObject getPlatformDict() {
        EasySQL sql = new EasySQL("select PLATFORM_ID, PLATFORM_NAME from YQ_BUSINESS_PLATFORM where 1=1");
        sql.append(param.getString("platformTypeId"), "and PLATFORM_TYPE_ID = ?");
        sql.append("order by IDX_ORDER");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }
}