package com.yunqu.work.servlet.work;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.StaffService;

@WebServlet("/servlet/project/conf")
public class ProjectConfServlet extends AppBaseServlet {
	private static final long serialVersionUID = -6366774540430826942L;

	public EasyResult actionForAddTeamUser(){
		JSONObject params = getJSONObject();
		String projectId = params.getString("projectId");
		String teamStr = params.getString("teamIds");
		int succcess = 0 ;
		if(StringUtils.notBlank(teamStr)){
			String[] teams = teamStr.split(",");
			for(int i=0;i<teams.length;i++){
				String userId=teams[i];
				EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM", "PROJECT_ID","USER_ID");
				record.setPrimaryValues(projectId,userId);
				record.set("JOIN_TIME", EasyDate.getCurrentDateString());
				try {
					this.getQuery().save(record);
					succcess ++;
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}else {
			return EasyResult.fail("请选择");
		}
		return EasyResult.ok(succcess,succcess+"个成员新增成功");
	}
	
	public EasyResult actionForUpdateTeamAdminFlag(){
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
		try {
			record.set("PROJECT_ID", params.getString("projectId"));
			record.set("USER_ID", params.getString("userId"));
			record.set("ADMIN_FLAG", params.getIntValue("adminFlag"));
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForRemoveProjectTeamUser(){
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
		try {
			record.set("PROJECT_ID", params.getString("projectId"));
			record.set("USER_ID", params.getString("userId"));
			this.getQuery().deleteById(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForEditNotice(){
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("YQ_PROJECT","PROJECT_ID");
		try {
			record.set("PROJECT_ID", params.getString("projectId"));
			record.set("PROJECT_NOTICE", params.getString("content"));
			record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			record.set("UPDATE_BY", getUserName());
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForEditPlan(){
		JSONObject params = getJSONObject();
		JSONArray ids = params.getJSONArray("ids");
		for(int i=0,len=ids.size();i<len;i++) {
			 String configId = ids.getString(i);
			 String planId = params.getString(configId+"_planId");
			 EasyRecord record = new EasyRecord("YQ_PROJECT_PLAN","PLAN_ID");
			 try {
				 record.set("MENU_ID", configId);
				 String planEndDate = params.getString(configId+"_planEndDate");
				 if(StringUtils.isBlank(planEndDate)) {
					 continue;
				 }
				 String doUserId = params.getString(configId+"_doUserId");
				 String doUserName = params.getString(configId+"_doUserName");
				 if(StringUtils.isBlank(doUserId)) {
					 doUserId = getUserId();
				 }
				 if(StringUtils.isBlank(doUserName)) {
					 doUserName = getUserName();
				 }
				 record.set("ORDER_INDEX", params.getIntValue(configId+"_order"));
				 record.set("PLAN_STATE", params.getString(configId+"_state"));
				 record.set("PLAN_NAME", params.getString(configId+"_planName"));
				 record.set("P_PLAN_NAME", params.getString(configId+"_parentPlanName"));
				 record.set("PLAN_PERSON_ID", doUserId);
				 record.set("PLAN_PERSON_NAME",doUserName);
				 record.set("PLAN_BEGIN_DATE", params.getString(configId+"_planBeginDate"));
				 record.set("PLAN_FINISH_DATE",planEndDate );
				 record.set("BEGIN_DATE", params.getString(configId+"_beginDate"));
				 record.set("FINISH_DATE", params.getString(configId+"_endDate"));
				 record.set("PLAN_REMARK",params.getString(configId+"_remark"));
				 record.set("PLAN_PROCESS",params.getString(configId+"_process"));
				 String pname = params.getString(configId+"_pname");
				 if(StringUtils.notBlank(pname)) {
					 record.set("PLAN_NAME",pname);
				 }
				 record.set("PROJECT_ID", params.getString("projectId"));
				 record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				 record.set("CREATOR", getUserName());
				 if(StringUtils.notBlank(planId)) {
					 record.set("PLAN_ID",planId);
					 this.getQuery().update(record);
				 }else {
					 record.set("PLAN_ID",RandomKit.uniqueStr());
					 this.getQuery().save(record);
				 }
			 } catch (SQLException e) {
				 this.error(e.getMessage(), e);
				 return EasyResult.fail();
			 }
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForQueryContractIdByReviewNo() {
		String result = Db.queryStr("SELECT CONTRACT_ID from YQ_PROJECT_CONTRACT where REVIEW_ID = ?",getJsonPara("reviewId"));
		return EasyResult.ok(result);
	}
	
	public EasyResult actionForFavoriteState() {
		int result = Db.queryInt("select count(1) from yq_favorite where favorite_by = ? and fk_id = ?",getUserId(),getPara("id"));
		return EasyResult.ok(result);
	}
	public EasyResult actionForUpdateFavoriteState() {
		String id = getPara("projectId");
		String favoriteFlag = getPara("favoriteFlag");
		if("1".equals(favoriteFlag)) {
			try {
				this.getQuery().executeUpdate("delete from yq_favorite where favorite_by = ? and fk_id = ?",getUserId(),id);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}else {
			EasyRecord record = new EasyRecord("yq_favorite","fk_id","favorite_by");
			record.set("favorite_id",RandomKit.uniqueStr());
			record.set("fk_id", id);
			record.set("favorite_time", EasyDate.getCurrentDateString());
			record.set("favorite_by", getUserId());
			try {
				this.getQuery().save(record);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForSetProjectPlan() {
		JSONObject params = getJSONObject();
		String contractId = params.getString("contractId");
		try {
			EasyRecord projectRecord = new EasyRecord("YQ_PROJECT","PROJECT_ID");
			projectRecord.setPrimaryValues(contractId);
			projectRecord.set("BEGIN_DATE", params.getString("beginDate"));
			projectRecord.set("CY_DATE", params.getString("cyDate"));
			projectRecord.set("ZY_DATE", params.getString("zyDate"));
			projectRecord.set("SX_DATE", params.getString("sxDate"));
			this.getQuery().update(projectRecord);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForSetProjectPmInfo() {
		JSONObject params = getJSONObject();
		String contractId = params.getString("contractId");
		try {
			EasyRecord projectRecord = new EasyRecord("YQ_PROJECT","PROJECT_ID");
			projectRecord.setPrimaryValues(contractId);
			//项目经理
			String pmName =  params.getString("pmName");
			projectRecord.set("project_po",StaffService.getService().getUserId(pmName));
			projectRecord.set("project_po_name",pmName);
			projectRecord.set("dept_id", params.getString("deptId"));
			projectRecord.set("project_dept_name", params.getString("deptName"));
			//开发负责人
			String devName =  params.getString("devName");
			projectRecord.set("po", StaffService.getService().getUserId(devName));
			projectRecord.set("po_name", devName);
			this.getQuery().update(projectRecord);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
}
