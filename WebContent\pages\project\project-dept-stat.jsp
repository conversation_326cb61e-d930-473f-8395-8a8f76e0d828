<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>部门项目</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
		.layui-table td, .layui-table th{padding: 9px 6px;}
		.layui-table input{width: 100%!important;}
		
		.layui-table>tbody>tr .hover{
			opacity:0;
		}
		.layui-table>tbody>tr:hover .hover{
			background-color: #f4f4f4;
			opacity:1;
		}

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="editForm">
			<div class="ibox">
				<div class="ibox-title mb-15 clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-inbox"></span> 部门项目清单</h5>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">时间区间</span>	
							 <select name="day" onchange="changeDay(this.value)" class="form-control input-sm">
		                    		<option value="">全部</option>
		                    		<option value="7">7天内</option>
		                    		<option value="30">一月内</option>
		                    		<option value="60">2个月内</option>
		                    		<option value="90" selected="selected">3个月内</option>
		                    		<option value="180">近半年</option>
		                    		<option value="365">近一年</option>
	                    	</select>
					     </div>
	          	     </div>
	              </div> 
	             <div class="layui-row layui-col-space10">
	             	<c:forEach items="${list}" var="item">
		            	  <div class="layui-col-md6">
		            	  		<div class="layui-card pb-10">
								    <div class="layui-card-header">${item.ASSIGN_DEPT_NAME }（项目数：${item.PROJECT_NUM}）</div>
								    <div class="layui-card-body" style="height: 300px;overflow-y: scroll;padding-bottom:20px;" data-mars="TaskDao.deptProejctList('${item.ASSIGN_DEPT_ID}')" data-template="v1Tpl">
								  
								    </div>
							    </div>
		            	  </div>
	             	</c:forEach>
	             </div>
	             
				<script type="text/x-jsrender" id="v1Tpl">
						<table class="layui-table">
						  <thead>
							<tr>
							  <th style="width:50px;">序号</th>
							  <th>项目名称</th>
							  <th style="width:80px;">总任务数</th>
							  <th style="width:80px;">未完成数</th>
							  <th style="width:80px;">参与人数</th>
							</tr> 
						  </thead>
						  <tbody>
						  {{for data}}
							<tr>
							  <td>{{:#index+1}}</td>
							  <td>
									<a href="javascript:;" onclick="projectDetail('{{:PROJECT_ID}}','{{:ASSIGN_DEPT_ID}}','{{:ASSIGN_DEPT_NAME}}')">{{call:PROJECT_NAME 100 fn='cutText'}}</a>
 							   </td>
							  <td>{{:TASK_NUM}}</td>
							  <td>{{:NOT_DO}}</td>
							  <td>{{:ASSIGN_USER_NUM}}</td>
							</tr>
						  {{/for}}
						  </tbody>
						</table>
				</script>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		function loadData(){
			$("#editForm").render({success:function(){
				
			}});
		}
		$(function(){
			var day = getUrlParam('day');
			if(day){
				$("[name='day']").val(day);
			}
			loadData();
		});
		
		function projectDetail(projectId,assignDeptId,assignDeptName){
			projectBoard(projectId);
			//popup.openTab({id:'projectDetail',url:'${ctxPath}/pages/project/project-detail.jsp',title:'项目总览',data:{projectId:projectId,assignDeptId:assignDeptId,assignDeptName:assignDeptName}});
		}
		
		function changeDay(day){
			location.href = '/yq-work/servlet/task?action=toDeptTaskStat&day='+day;
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>