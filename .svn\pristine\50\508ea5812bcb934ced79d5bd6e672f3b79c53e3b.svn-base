package com.yunqu.work.service;

import java.sql.SQLException;

import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.StaffModel;

public class FlowService extends AppBaseService{

	private static class Holder{
		private static FlowService service=new FlowService();
	}
	public static FlowService getService(){
		return Holder.service;
	}
	
	public String getNextNodeId(String nodeId) throws SQLException {
		String nextNodeId = this.getQuery().queryForString("select c_node_id from yq_flow_approve_node where node_id = ?", nodeId);
		return nextNodeId;
	}
	
	public JSONObject getNodeInfo(String nodeId,String userId,JSONObject params) throws SQLException {
		JSONObject nodeInfo= this.getQuery().queryForRow("select * from yq_flow_approve_node where node_id = ?", new Object[]{nodeId}, new JSONMapperImpl());
		String _userId = nodeInfo.getString("USER_ID");
		if("superior".equals(_userId)) {
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
			nodeInfo.put("USER_ID", staffModel.getSuperior());
			nodeInfo.put("USER_NAME", staffModel.getStaffNo());
		}else if("selectUser".equals(_userId)) {
			nodeInfo.put("USER_ID", params.getString("selectUserId"));
			nodeInfo.put("USER_NAME", params.getString("selectUserName"));
		}
		return nodeInfo;
	}
	
	public JSONObject getFirstNode(String flowType) throws SQLException {
		JSONObject obj = this.getQuery().queryForRow("select * from yq_flow_approve_node where p_node_id = 0 and busi_type = ? and node_state = 0 order by check_index", new Object[] {flowType}, new JSONMapperImpl());
		return obj;
	}
	
	public String[] getNodeUser(String flowType) {
		String sql = "select GROUP_CONCAT(user_id) user_ids,GROUP_CONCAT(user_name) user_names from yq_flow_approve_node where busi_type = ? and node_state = 0 ORDER BY check_index";
		JSONObject checkObj = null;
		try {
			checkObj = this.getQuery().queryForRow(sql,new Object[] {flowType},new JSONMapperImpl());
			String userIds = checkObj.getString("USER_IDS");
			String userNames = checkObj.getString("USER_NAMES");
			return new String[] {userIds,userNames};
		} catch (SQLException e) {
			getLogger().error(null, e);
			return new String[]{"",""};
		}
	}
	
	public void updateApplyInfo(String businessId,String currentResultId,String nextResultId,String nodeId,String nextNodeId,int applyState) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(nodeId,"current_node_id = ?,");
			sql.append(nextNodeId,"next_node_id = ?,");
			sql.append(currentResultId,"current_result_id = ?,");
			sql.append(nextResultId,"next_result_id = ?,");
			sql.append(applyState,"apply_state = ?,");
			sql.append(EasyDate.getCurrentDateString(),"last_result_time = ?");
			sql.append(businessId,"where apply_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	public void updateApplyBegin(String businessId,String nextNodeId,String nextResultId) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(0,"current_node_id = ?,");
			sql.append(0,"current_result_id = ?,");
			sql.append(nextResultId,"next_result_id = ?,");
			sql.append(nextNodeId,"next_node_id = ?");
			sql.append(businessId,"where apply_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	public void updateApplyEnd(String businessId,String nextNodeId,String nextResultId) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(nextNodeId,"current_node_id = ?,");
			sql.append(nextResultId,"current_result_id = ?,");
			sql.append(30,"apply_state = ?");// 10 待审批 20审批中 21 拒绝 30审批完成
			sql.append(businessId,"where apply_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	public void updateApplyInfo(String businessId,String nextNodeId,int applyState) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(nextNodeId,"current_node_id = ?,");
			sql.append(applyState,"apply_state = ?,");
			sql.append(businessId,"where apply_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	
	
}
