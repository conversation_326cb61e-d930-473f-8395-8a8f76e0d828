<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectContractForm">
       	   <input type="hidden" name="dataType" id="dataType">
	       <div class="layui-row">
	      		 <div class="layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: 0px 15px 10px;">
         		 	 	 <ul class="layui-tab-title"><li lay-id="0" class="layui-this">全部</li><li lay-id="1">最近选择</li></ul>
         		 </div>
	   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
					 <span class="input-group-addon">名称</span>	
					 <input type="text" name="contractName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter" onclick="SelectContract.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   			<div class="btn-group btn-group-sm layui-hide-xs ml-10">
					<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown" aria-expanded="true">其他</button>
					<ul class="dropdown-menu dropdown-menu-right">
					    <li><a href="javascript:;" onclick="SelectContract.fastQuery($(this))">缺省</a></li>
					    <li><a href="javascript:;" onclick="SelectContract.fastQuery($(this))">其他</a></li>
					</ul>
				</div>
				<small class="pull-right mr-15 layui-hide-xs mt-5">若无项目,请选择其他或缺省</small>
	   		</div>
          <div class="ibox">
           		<table id="contractList"></table>
          </div>
          <div class="layer-foot text-c" style="z-index: 999999;">
	   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectContract.ok()">确定</button>
	   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
		</div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectContract = {
					sid:'${param.sid}',
					query : function(){
						$("#selectContractForm").queryData({id:'contractList'});
					},
					fastQuery:function(el){
						$("#selectContractForm [name='contractName']").val(el.text());
						SelectContract.query();
					},
					initTable : function(){
						var data = layui.data('yqwork');
						var selectBxContractTab = data.selectBxContractTab;
						if(selectBxContractTab){
							$("#dataType").val(selectBxContractTab);
							var element = layui.element;
							element.tabChange('stateFilter', selectBxContractTab);
						}
						
						$("#selectContractForm").initTable({
							mars:'ProjectContractDao.bxContractSelect',
							id:'contractList',
							page:true,
							limit:20,
							height:'320px',
							rowDoubleEvent:'SelectContract.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'CONTRACT_NAME',
								title: '合同名称',
								minWidth:300
							},{
							    field: 'ERP_CONTRACT_NO',
								title: '合同编号(ERP)',
								width:120
							},{
							    field: 'CONTRACT_STATE',
								title: '状态',
								hide:true,
								width:80
							}
							]]
					       }
						);
						
						layui.element.on('tab(stateFilter)', function(){
							var type = this.getAttribute('lay-id');
							$("#dataType").val(type);
							layui.data('yqwork',{key:'selectBxContractTab',value:type});
							SelectContract.query();
						});
						
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectContract.sid+"']");
						var itemId = el.data('id')||SelectContract.sid;
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('contractList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var nos = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['CONTRACT_NAME']);
									ids.push(data[index]['CONTRACT_ID']);
									nos.push(data[index]['ERP_CONTRACT_NO']);
									el.attr('data-contact-state',data[index]['CONTRACT_STATE']);
									$("[name='contractState_"+itemId+"']").val(data[index]['CONTRACT_STATE']);
								}
								
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								if(el.prev().prev().length>0){
									el.prev().prev().val(nos.join(','));
								}
								if($.isFunction(selctCallBack)){
									selctCallBack(SelectContract.sid,'contract');
								}
								popup.layerClose("selectContractForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
								if(el.prev().prev().length>0){
									el.prev().prev().val('');
								}
							}
						}else{
							el.val(selectRow['CONTRACT_NAME']);
							el.attr('data-contact-state',selectRow['CONTRACT_STATE']);
							$("[name='contractState_"+itemId+"']").val(selectRow['CONTRACT_STATE']);
							if(el.prev().length>0){
								el.prev().val(selectRow['CONTRACT_ID']);
							}
							if(el.prev().prev().length>0){
								el.prev().prev().val(selectRow['ERP_CONTRACT_NO']);
							}
							if($.isFunction(selctCallBack)){
								selctCallBack(SelectContract.sid,'contract');
							}
							popup.layerClose("selectContractForm");
						}
					}
					
			};
			$(function(){
				SelectContract.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>