<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="goodsStorageReturnTable"></table>
	<script type="text/javascript">
		$(function(){
			$("#OrderDetailForm").initTable({
				mars:'GoodsDao.stockList',
				id:'goodsStorageReturnTable',
				page:false,
				data:{type:'30'},
				rowDoubleEvent:'editReturnStock',
				height:300,
				cellMinWidth:80,
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
            	 	type: 'checkbox',
					align:'left'
				 },{
					 field:'NAME',
					 title:'物料名称'
				 },{
					 field:'NUM',
					 title:'退货数量',
					 width:70
				 },{
					 field:'RETURN_TYPE',
					 title:'退货至',
					 width:80,
					 templet:function(row){
						 var returnType = row['RETURN_TYPE'];
						 if(returnType=='2'){
							 return '仓库';
						 }else{
							 return '供应商';
						 }
					 }
				 },{
				    field: 'DATE_ID',
					title: '退货日期',
					align:'center'
				},{
					field:'REMARK',
					title:'备注',
					edit:'text'
				},{
				    field: 'CREAT_NAME',
					title: '操作人',
					align:'center'
				},{
				    field: 'CREATE_TIME',
					title: '操作时间',
					align:'center'
				}
			]],done:function(data){
				$("#stockReturnCount").text(data.total);
		  	}
		});
	});
	
	function storageReturnReload(){
		$("#OrderDetailForm").queryData({page:false,id:'goodsStorageReturnTable',data:{type:'30'}});
	}
		
	function editReturnStock(data){
		popup.layerShow({id:'storageEdit',area:['600px','400px'],title:'修改',url:'${ctxPath}/pages/erp/goods/storage-edit.jsp',data:{title:'退货',type:'return',stockId:data['STOCK_ID']}});
	}
	
</script>
