<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>菜单管理</title>
	<style>
		.layui-btn{display: none;}
		.layui-table-hover .layui-btn{
			display: inline-block;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form name="searchForm" autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-cutlery"></span> 菜品管理</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">菜品名</span>	
							 <input type="text" name="foodName" class="form-control input-sm" style="width: 90px;">
					     </div>
					       <div class="input-group input-group-sm">
							 <span class="input-group-addon">供应商</span>	
							 <input type="text" name="supplier" class="form-control input-sm" style="width: 90px;">
					     </div>
	        		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">中晚餐</span>	
							 <select name="dinnerType" class="form-control input-sm" >
							 	<option value="">--</option>
							 	<option value="2">中餐</option>
							 	<option value="3">晚餐</option>
							 </select>
					     </div>
	        		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">状态</span>	
							 <select name="state" class="form-control input-sm" >
							 	<option value="1" selected="selected">使用中</option>
							 	<option value="0">已删除</option>
							 </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						  <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-success mr-15" onclick="list.lookMeals()">本周菜单</button>
							 <button type="button" class="btn btn-sm btn-info mr-15" onclick="list.joinMeals()">+ 加入菜单</button>
							 <button type="button" class="btn btn-sm btn-warning " onclick="list.batchDel()"> - 批量删除</button>
							 <button type="button" class="btn btn-sm btn-info ml-15" onclick="list.add()">+ 添加菜品</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						<table class="layui-hide" id="t"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
		{{if STATE==1}}
  			<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="list.joinMeal">+菜单</a>
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>
  			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.del">删除</a>
		{{else}}
  			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.enable">启用</a>
		{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		$(function(){
			list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'FoodDao.foodList',
					limit:30,
					id:'t',
					cols: [[
					  {type:'checkbox',width:50},
					  {title:'序号',type:'numbers',width:50},
					  {
					    field: 'SUPPLIER',
						title: '供应商',
						align:'center'
					  },
					  {
					    field: 'FOOD_NAME',
						title: '菜单名',
						align:'center'
					  },
		              {
					    field: 'FOOD_PRICE',
						title: '价格',
						align:'left'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
					},{
					    field: 'DINNER_TYPE',
						title: '类型',
						align:'center',
						templet:function(row){
							if(row.DINNER_TYPE==1){
								return '早餐';
							}
							if(row.DINNER_TYPE==2){
								return '中餐';
							}
							if(row.DINNER_TYPE==3){
								return '晚餐';
							}
							return '';
						}
					},{
						title: '操作',
						align:'center',
						width:200,
						cellMinWidth:200,
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				 ]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,area:['450px','350px'],url:'${ctxPath}/pages/food/food-edit.jsp',title:'添加菜单'});
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,area:['450px','350px'],url:'${ctxPath}/pages/food/food-edit.jsp',title:'编辑',data:{foodId:data.ID}});
			},
			lookMeals:function(){
				popup.layerShow({type:2,maxmin:true,area:['80%','90%'],url:'${ctxPath}/pages/food/meals-mgr.jsp',title:'本周菜单'});
			},
			del:function(data){
				layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/food?action=delFood",{'id':data.ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			enable:function(data){
					ajax.remoteCall("${ctxPath}/servlet/food?action=enableFood",{'id':data.ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
			},
			batchDel:function(){
				var checkStatus = table.checkStatus('t');
				if(checkStatus.data.length>0){
					ajax.remoteCall("${ctxPath}/servlet/food?action=batchDel",checkStatus.data,function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}else{
					layer.msg("请选择!");
				}
			},
			joinMeals:function(){
				var checkStatus = table.checkStatus('t');
				if(checkStatus.data.length>0){
					ajax.remoteCall("${ctxPath}/servlet/food?action=joinMeals",checkStatus.data,function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}else{
					layer.msg("请选择!");
				}
			},
			joinMeal:function(data){
					ajax.remoteCall("${ctxPath}/servlet/food?action=joinMeal",data,function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>