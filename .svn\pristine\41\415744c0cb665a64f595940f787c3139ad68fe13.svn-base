<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>流程监控</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form id="flowViewForm">
		  <div class="ibox-content">
		  <div class="layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: 0px;margin-bottom: -8px;">
        	<ul class="layui-tab-title">
	        	<li data-val="1" class="layui-this">审批记录<span id="one"></span></li>
	        	<li data-val="2">查看记录<span id="two"></span></li>
	        	<li data-val="3">沟通记录<span id="three"></span></li>
        	</ul>
        	<div class="layui-tab-content" style="margin:-10px;">
			    <div class="layui-tab-item layui-show">
					 <table id="checkRecordTable"></table>
			    </div>
			    <div class="layui-tab-item">
					 <table id="lookLogTable"></table>
			    </div>
			    <div class="layui-tab-item">
			    	<table id="followRecordTable"></table>
			    </div>
		  </div>
        </div>
        </div>
	  </form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var list={
			init1:function(){
				$("#flowViewForm").initTable({
					mars:'FlowCommon.approveRecordList',
					title:'审批记录',
					id:'checkRecordTable',
					limit:50,
					height:'full-100',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							width:50,
							align:'left'
						 },{
						    field: 'APPLY_TITLE',
							title: '流程标题',
							align:'left',
							event:'list.detail',
							minWidth:200,
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'APPLY_TIME',
							title: '申请时间',
							align:'center',
							width:160
						},{
						    field: 'APPLY_DEPT_NAME',
							title: '申请部门',
							align:'left',
							width:140
						},{
						    field: 'APPLY_NAME',
							title: '申请人',
							align:'center',
							width:80
						},{
						    field: 'CHECK_NAME',
							title: '审批人',
							align:'center',
							width:80
						},{
						    field: 'NODE_NAME',
							title: '审批节点',
							align:'center',
							width:80
						},{
							field:'CHECK_RESULT',
							title:'审批结果',
							width:80,
							templet:function(row){
								var json  = {'0':'待审批','1':'审批通过','2':'审批拒绝'};
								var checkResult = row['CHECK_RESULT']||'';
								return json[checkResult]||'--';
							}
						},{
						    field: 'CHECK_TIME',
							title: '审批时间',
							align:'center',
							width:140
						},{
						    field: 'CHECK_DESC',
							title: '审批描述',
							align:'left',
							minWidth:80
						}
					]],done:function(res,curr,count){
						$('#one').text("("+res.totalRow+")");
					}}
				);
			},
			init2:function(){
				$("#flowViewForm").initTable({
					mars:'FlowCommon.lookRecordList',
					title:'查看记录',
					id:'lookLogTable',
					limit:50,
					height:'full-100',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							width:50,
							align:'left'
						 },{
						    field: 'APPLY_TITLE',
							title: '流程标题',
							align:'left',
							event:'list.detail',
							minWidth:200,
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'APPLY_TIME',
							title: '申请时间',
							align:'center',
							width:160
						},{
						    field: 'APPLY_DEPT_NAME',
							title: '申请部门',
							align:'left',
							width:140
						},{
						    field: 'APPLY_NAME',
							title: '申请人',
							align:'center',
							width:80
						},{
						    field: 'LOOK_NAME',
							title: '查看人',
							align:'center',
							width:80
						},{
						    field: 'LOOK_TIME',
							title: '查看时间',
							align:'center',
							width:140
						},{
						    field: 'IP',
							title: '查看IP',
							align:'left',
							width:140
						},{
						    field: 'DEVICE',
							title: '查看设备',
							align:'left',
							width:160
						}
					]],done:function(res,curr,count){
						$('#two').text("("+res.totalRow+")");
					}}
				);
			},
			init3:function(){
				$("#flowViewForm").initTable({
					mars:'FlowCommon.followRecordList',
					title:'沟通记录',
					id:'followRecordTable',
					limit:50,
					height:'full-100',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							width:50,
							align:'left'
						 },{
						    field: 'APPLY_TITLE',
							title: '流程标题',
							align:'left',
							event:'list.detail',
							minWidth:200,
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'APPLY_TIME',
							title: '申请时间',
							align:'center',
							width:160
						},{
						    field: 'APPLY_DEPT_NAME',
							title: '申请部门',
							align:'left',
							width:140
						},{
						    field: 'APPLY_NAME',
							title: '申请人',
							align:'center',
							width:80
						},{
						    field: 'ADD_USER_NAME',
							title: '发表人',
							align:'center',
							width:80
						},{
						    field: 'ADD_TIME',
							title: '发表时间',
							align:'center',
							width:140
						},{
						    field: 'FOLLOW_CONTENT',
							title: '发表内容',
							align:'left',
							minWidth:140
						}
					]],done:function(res,curr,count){
						$('#three').text("("+res.totalRow+")");
					}}
				);
			},
			detail:function(data){
				var flowCode = data['FLOW_CODE'];
				var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,resultId:data['CURRENT_RESULT_ID']});
				popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
			}
		}
		$(function(){
			list.init1();
			list.init2();
			list.init3();
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>