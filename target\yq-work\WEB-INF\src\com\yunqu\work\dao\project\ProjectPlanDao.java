package com.yunqu.work.dao.project;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "ProjectPlanDao")
public class ProjectPlanDao extends AppDaoContext{

	@WebControl(name="planConfig",type=Types.TEMPLATE)
	public JSONObject planConfig(){
		EasySQL sql=getEasySQL("select t1.* from yq_project_stage_config t1  where 1=1 and t1.state = 0");
		sql.append("order by t1.sort");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="planInfo",type=Types.RECORD)
	public JSONObject planInfo(){
		EasySQL sql = getEasySQL("select t1.* from yq_project_plan t1  where 1=1 ");
		sql.append(param.getString("plan_id"),"and t1.plan_id = ?",false);
		return this.queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectPlanList",type=Types.TEMPLATE)
	public JSONObject projectPlanList(){
		String projectId = param.getString("projectId");
		JSONObject result = null;
		try {
			int count = this.getQuery().queryForInt("select count(1) from yq_project_plan where project_id = ? and plan_state > 0",projectId);
			if(count>0) {
				result =  queryForList("select *,PLAN_NAME MENU_NAME,PLAN_ID ID,P_PLAN_ID PARENT_ID,P_PLAN_NAME PARENT_NAME,ORDER_INDEX SORT from yq_project_plan where project_id = ? and plan_state > 0 order by order_index", new Object[] {projectId});
			}else {
				result =  queryForList("select *,MENU_NAME PLAN_NAME from yq_project_stage_config where state = ? order by sort", new Object[] {0});
			}
			return result;
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return emptyPage();
		}
	}
	
	@WebControl(name="planListQuery",type=Types.TEMPLATE)
	public JSONObject planListQuery(){
		EasySQL sql = new EasySQL();
		sql.append("SELECT t2.project_name,t1.* from yq_project_plan t1,yq_project t2 where t1.project_id = t2.project_id");
		sql.append("and t1.plan_state > 0 and t1.plan_begin_date<>''");
		sql.append(param.getString("planState"),"and t1.plan_state = ?");
		sql.append(param.getString("projectId"),"and t1.project_id = ?");
		sql.appendLike(param.getString("planName"),"and t1.plan_name like ?");
		
		String source = param.getString("source");
		if("all".equals(source)&&(isSuperUser()||hasRole("PROJECT_MANAGER"))) {
			sql.appendLike(param.getString("personName"),"and t1.plan_person_name like ?");
		}else {
			sql.append(getUserId(),"and t1.plan_person_id = ?");
		}
		
		String planBegin1Date = param.getString("planBegin1Date");
		String planBegin2Date = param.getString("planBegin2Date");
		if(StringUtils.notBlank(planBegin1Date)) {
			sql.append(planBegin1Date,"and t1.plan_begin_date >= ?");
		}
		if(StringUtils.notBlank(planBegin2Date)) {
			sql.append(planBegin2Date,"and t1.plan_begin_date <= ?");
		}
		
		String planEnd1Date = param.getString("planEnd1Date");
		String planEnd2Date = param.getString("planEnd2Date");
		if(StringUtils.notBlank(planEnd1Date)) {
			sql.append(planEnd1Date,"and t1.plan_finish_date >= ?");
		}
		if(StringUtils.notBlank(planEnd2Date)) {
			sql.append(planEnd2Date,"and t1.plan_finish_date <= ?");
		}
		
		sql.append("ORDER BY t1.plan_begin_date desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
}
