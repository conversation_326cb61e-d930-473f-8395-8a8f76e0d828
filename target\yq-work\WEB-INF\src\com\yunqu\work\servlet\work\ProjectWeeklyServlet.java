package com.yunqu.work.servlet.work;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.ProjectWeeklyModel;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.WeeklyNoticeService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.WeekUtils;
/**
 * 项目周报管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/project/weekly/*")
public class ProjectWeeklyServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		ProjectWeeklyModel model=getModel(ProjectWeeklyModel.class, "weekly");
		model.addCreateTime();
		model.set("update_time",EasyDate.getCurrentDateString());
		model.setCreator(getUserId());
		model.set("create_name",getUserName());
		
		JSONObject jsonObject=getJSONObject();
		JSONArray mailtos=jsonObject.getJSONArray("mailtos");
		String projectName=jsonObject.getString("projectName");
		String[] cc=null;
		if(mailtos!=null){
			cc=new String[mailtos.size()];
			for(int i=0;i<mailtos.size();i++){
				String usertId=mailtos.getString(i);
				cc[i]=usertId;
				EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
				record.setPrimaryValues(model.getWeeklyId(),usertId);
				record.set("CREATOR", getUserPrincipal().getUserId());
				record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				try {
					this.getQuery().save(record);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}
		try {
			String receiver=model.getString("RECEIVER");
			if(StringUtils.isBlank(receiver)){
				model.set("status","0");//未发送
			}else{
				model.set("SEND_TIME",EasyDate.getCurrentDateString());//已发送
				model.set("status","1");//已发送
				
				MessageModel messageModel=new MessageModel();
				messageModel.setReceiver(receiver);
				messageModel.setData(model);
				if(StringUtils.isBlank(projectName)){
					messageModel.setContents("【云趣协同】"+model.getString("TITLE"));
				}else{
					messageModel.setContents("【"+projectName+"】"+model.getString("TITLE"));
				}
				messageModel.setFkId(model.getWeeklyId());
				messageModel.setSender(getUserId());
				messageModel.setCc(cc);
				messageModel.setMsgState(1);
				messageModel.setMsgType(2001);
				messageModel.setSenderType(0);
				
				messageModel.setData(model);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendProjectWeeklyEmail(messageModel);
					messageModel.setDesc(model.getString("THIS_WORK"));
					WxMsgService.getService().sendNewWeeklyMsg(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			model.save();
			
			this.getQuery().executeUpdate("update yq_project_weekly t1,yq_weekly_date t2 set t1.week_begin= t2.week_begin,t1.week_end = t2.week_end where t1.year = t2.year and t1.week_no = t2.week_no and t1.weekly_id = ? ", model.getWeeklyId());
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	private List<String> ccList(String weeklyId){
		String sql="select USER_ID from  yq_cc where FK_ID = ?";
		List<String> mailtos=new ArrayList<String>();
		List<EasyRow> list;
		try {
			list = this.getQuery().queryForList(sql,weeklyId);
			if(list!=null&&list.size()>0){
				for(int i=0;i<list.size();i++){
					mailtos.add(list.get(i).getColumnValue(1));
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return mailtos;
	}
	public EasyResult actionForUpdate(){
		ProjectWeeklyModel model=getModel(ProjectWeeklyModel.class, "weekly");
		JSONObject jsonObject=getJSONObject();
		JSONArray mailtos=jsonObject.getJSONArray("mailtos");
		List<String> cc=new ArrayList<String>();
		List<String> ccList=ccList(model.getWeeklyId());
		if(mailtos!=null){
			for(int i=0;i<mailtos.size();i++){
				String usertId=mailtos.getString(i);
				cc.add(usertId);
				EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
				record.setPrimaryValues(model.getWeeklyId(),usertId);
				record.set("CREATOR", getUserPrincipal().getUserId());
				record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				try {
					if(ccList.contains(usertId))continue;
					this.getQuery().save(record);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
			for(String userId:ccList){
				if(!cc.contains(userId)){
					try {
						this.getQuery().executeUpdate("delete from YQ_CC where fk_id = ? and user_id = ?",model.getWeeklyId(),userId);
					} catch (SQLException e) {
						e.printStackTrace();
					}
				}
			}
		}
		
		model.set("update_time",EasyDate.getCurrentDateString());
		try {
			String receiver=model.getString("RECEIVER");
			int status=model.getIntValue("STATUS");
			if(status<2){
				if(StringUtils.isBlank(receiver)){
					model.set("status","0");//未发送
				}else{
					model.set("SEND_TIME",EasyDate.getCurrentDateString());//已发送
					model.set("status","1");//已发送
					
					MessageModel messageModel=new MessageModel();
					messageModel.setReceiver(receiver);
					messageModel.setContents("【云趣协同】"+model.getString("TITLE"));
					messageModel.setFkId(model.getWeeklyId());
					messageModel.setSender(getUserId());
					messageModel.setMsgState(1);
					messageModel.setData(model);
					messageModel.setMsgType(2001);
					messageModel.setSenderType(0);
					
					messageModel.setData(model);
					
					try {
						MessageService.getService().sendMsg(messageModel);
						EmailService.getService().sendWeeklyEmail(messageModel);
						messageModel.setDesc(model.getString("THIS_WORK"));
						WxMsgService.getService().sendNewWeeklyMsg(messageModel);
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		ProjectWeeklyModel model=getModel(ProjectWeeklyModel.class, "weekly");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	public EasyResult actionForNoticeWeekly(){
		String weeklyNo=getPara("weeklyNo", ""+WeekUtils.getWeekNo());
		WeeklyNoticeService.getService().run(Integer.valueOf(weeklyNo));
		return EasyResult.ok("","提醒成功!");
	}
}





