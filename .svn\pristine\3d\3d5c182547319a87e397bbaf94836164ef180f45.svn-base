<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 140px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}自购笔记本电脑补助申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 140px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">入职时间</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date',max:0}"  class="form-control input-sm Wdate" name="apply.data1"/>
					  			</td>
					  			<td class="required">合同到期时间</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date',min:0}" class="form-control input-sm Wdate" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">购机日期</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date',max:0}"  class="form-control input-sm Wdate" name="apply.data3"/>
					  			</td>
					  			<td class="required">购机品牌</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">购机价格</td>
					  			<td>
					  				<input type="number" data-rules="required" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td></td>
					  			<td></td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">审查发票</td>
					  			<td>
					  				<select data-edit-node="hr" data-required="true" class="form-control input-sm" name="apply.data6">
					  					<option value="">请选择</option>
					  					<option value="符合条件">符合条件</option>
					  					<option value="不符合条件">不符合条件</option>
					  				</select>
					  			</td>
					  			<td class="required">审查申请补助条件</td>
					  			<td>
					  				<select data-edit-node="hr" data-required="true" class="form-control input-sm" name="apply.data7">
					  					<option value="">请选择</option>
					  					<option value="初次申请">初次申请</option>
					  					<option value="符合再次申请">符合再次申请</option>
					  					<option value="不符合再次申请">不符合再次申请</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">人力行政部核算</td>
					  			<td colspan="3">
					  				<input data-edit-node="hrhs" class="form-control input-sm" name="apply.data8">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>申请原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				
			}});
		}

		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>