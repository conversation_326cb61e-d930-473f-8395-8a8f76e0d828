<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
  <title>选择业务平台</title>
</EasyTag:override>
<EasyTag:override name="content">
  <form method="post" class="form-inline" onsubmit="return false;" id="selectPlatformForm">
    <input type="hidden" name="platformTypeId" value="0">
    <input type="hidden" name="status" value="0">
    <div class="row">
      <div class="input-group input-group-sm ml-15" style="width: 160px">
        <span class="input-group-addon">名称</span>
        <input type="text" name="platformName" class="form-control input-sm">
      </div>
      <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectPlatform.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
    </div>
    <div class="ibox">
      <table id="SelectPlatformList"></table>
    </div>
    <div class="layer-foot text-c">
      <button class="btn btn-sm btn-primary"  type="button" onclick="SelectPlatform.ok()">确定</button>
      <button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
    </div>
  </form>
</EasyTag:override>

<EasyTag:override name="script">

  <script type="text/javascript">
    var SelectPlatform={
      sid:'${param.sid}',
      query : function(){
        $("#selectPlatformForm").queryData();
      },
      initTable : function(){
        console.log("startINITTABLE")
        $("#selectPlatformForm").initTable({
                  mars:'BusinessPlatformDao.platformList',
                  id:'SelectPlatformList',
                  page:true,
                  limit:20,
                  height:'400px',
                  rowDoubleEvent:'SelectPlatform.ok',
                  cols: [[
                    {type:'${param.type}'},
                    {
                      type: 'numbers',
                      title: '序号',
                      align:'left'
                    },{
                      field: 'PLATFORM_TYPE_NAME',
                      title: '一级平台'
                    },{
                      field: 'PLATFORM_NAME',
                      title: '业务平台'
                    },{
                      field: 'REMARK',
                      title: '备注',
                      width:120
                    }
                  ]]
                }
        );
      },
      ok : function(selectRow){
        var el = $("[data-sid='"+SelectPlatform.sid+"']");
        if(selectRow==undefined){
          var checkStatus = table.checkStatus('SelectPlatformList');
          if(checkStatus.data.length>0){
            var names = [];
            var ids = [];
            var data = checkStatus.data;
            for(var index in data){
              names.push(data[index]['PLATFORM_NAME']);
              ids.push(data[index]['PLATFORM_ID']);
            }
            el.val(names.join(','));
            if(el.prev().length>0){
              el.prev().val(ids.join(','));
            }
            popup.layerClose("selectPlatformForm");
          }else{
            el.val('');
            if(el.prev().length>0){
              el.prev().val('');
            }
          }
        }else{
          el.val(selectRow['PLATFORM_NAME']);
          if(el.prev().length>0){
            el.prev().val(selectRow['PLATFORM_ID']);
          }
          popup.layerClose("selectPlatformForm");
        }
      }

    };
    $(function(){
      SelectPlatform.initTable();
    });


  </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>