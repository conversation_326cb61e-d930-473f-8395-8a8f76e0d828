<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>报销已付款</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input type="hidden" name="flowCode" value="${param.flowCode}"/>
			<input type="hidden" name="applyState" value="30"/>
			<input type="hidden" name="payState" value="10"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm ml-5">
							 <span class="input-group-addon">付款日期</span>	
							 <input type="text" name="payBeginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
							 <input type="text" name="payEndDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
				    	 </div>
				    	 <div class="input-group input-group-sm">
						    <span class="input-group-addon">入账状态</span>	
							<select name="recordedState" onchange="queryData();" class="form-control input-sm">
							      <option value="">请选择 </option>
							      <option data-class="label label-success" value="0">待入账</option>
                    			  <option data-class="label label-warning" value="1">已入账</option>
						    </select>									  
					  	</div>
						<div class="input-group input-group-sm ml-5">
							 <span class="input-group-addon">入账日期</span>	
							 <input type="text" name="beginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
							 <input type="text" name="endDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
				    	 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>

		  </script>
		</form>
		
		<div id="selectDate" style="display: none;">
			<div style="padding: 10px 30px;">
		 		<input class="form-control input-sm Wdate" type="text" id="date" style="width:160px;" placeholder="请选择日期" >
		 		<textarea class="form-control input-sm mt-5 hidden" id="remark" style="width:100%;" placeholder="备注" ></textarea>
			</div>
		</div>
		
		 <script type="text/html" id="btnBar">
 				<EasyTag:res resId="To_PAY_AUTH">
			 		<button class="btn btn-sm btn-warning" lay-event="entryPay"><span class="glyphicon glyphicon-ok"></span> 入账</button>
 				</EasyTag:res>
				<button class="btn btn-sm btn-info ml-10" lay-event="exportData"><span class="glyphicon glyphicon-export"></span> 导出 </button>
			 	<button class="btn btn-sm btn-default ml-10" lay-event="refreshData"><span class="glyphicon glyphicon-refresh"></span> 刷新</button>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'BxReportDao.hasyPayList',
					id:'flowTable',
					page:true,
					title:'已付款',
					toolbar:'#btnBar',
					rowDoubleEvent:'flowDetail',
					height:'full-90',
					limit:50,
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },
					 {
	            	 	type: 'checkbox',
						title: '序号',
						align:'left'
					 },{
						 field:'APPLY_NO',
						 title:'编号',
						 width:160,
						 templet:function(row){
							 return row['APPLY_NO'];
						 }
					 },{
						 field:'PAY_MONEY',
						 title:'付款金额',
						 sort:true,
						 width:100
					 },{
						 field:'PAY_DATE',
						 title:'付款日期',
						 //edit:'text',
						 sort:true,
						 width:100
					 },{
						 field:'RECORDED',
						 title:'入账状态',
						 width:80,
						 templet:function(row){
							 if(row.RECORDED=='0'){
								 return '未入账';
							 }else{
								 return '已入账';
							 }
						 }
					 },{
						 field:'APPLY_REMARK',
						 title:'报销说明',
						 width:120
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:140
					},{
					    field: 'APPLY_END_TIME',
						title: '审批完成时间',
						align:'center',
					    width:140
					},{
						field:'',
						title:'操作',
						width:50,
						fixed:'right',
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				]],done:function(){
					layui.use('laydate', function(){
						 var laydate = layui.laydate;
						 laydate.render({max:0, min:-365,elem: "#date"});
					});
			  	}
			});
		 }
			
		function flowDetail(data){
			var id = data['FLOW_CODE'];
			var json  = $.extend({},{businessId:data['APPLY_ID'],flowCode:id});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
		
		function refreshData(){
		   location.reload();
		}
		
		function entryPay(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
		   }
		   var array = [];
		   for(var index in dataList){
			   var row  = dataList[index];
			   var recorded = row['RECORDED'];
			   if(recorded=='0'){
				   array.push({id:row['APPLY_ID']});
			   }
		   } 
		   if(array.length==0){
				layer.msg('没有符合条件的记录。',{icon : 7, time : 1000});
			   return;
		   }
		   var index = layer.open({type:1,content:$('#selectDate'),shade:0.1,offset:'20px',area:['300px','220px'],btn:['提交','取消'],yes:function(){
			   layer.confirm('确认操作吗,不可逆',{icon:3,offset:'20px'},function(){
				   var date = $('#date').val();
				   if(date==''){
						layer.msg('请选择入账日期。',{icon : 7, time : 1000});
					    return;
				   }
				   layer.close(index);
					ajax.remoteCall("${ctxPath}/servlet/bx/fun?action=updateBxRecorded",{array:array,date:date},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,function(){
								queryData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
		   }},function(){
			  
		   });
		}
		

		function exportData(dataList){
			layer.msg('正在导出',{time:500});
			location.href = '${ctxPath}/servlet/bx/fun?action=exportBx&data='+encodeURI(JSON.stringify(form.getJSONObject('#dataMgrForm')));
		}
			
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>