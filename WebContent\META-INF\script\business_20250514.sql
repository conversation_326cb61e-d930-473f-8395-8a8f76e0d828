--20250514 新增结算表
CREATE TABLE `yq_crm_settlement` (
    `SETTLEMENT_ID` varchar(32) NOT NULL COMMENT '结算ID',
    `SETTLEMENT_NO` varchar(100) DEFAULT NULL COMMENT '结算编号',
    `MONTH_ID` varchar(7) NOT NULL COMMENT '结算月份',
    `PLATFORM_ID` varchar(32) DEFAULT NULL COMMENT '平台ID',
    `PLATFORM_NAME` varchar(100) DEFAULT NULL COMMENT '平台名称',
    `CONTRACT_ID` varchar(32) NOT NULL COMMENT '合同ID',
    `CONTRACT_NO` varchar(100) DEFAULT NULL COMMENT '合同编号',
    `CONTRACT_NAME` varchar(255) DEFAULT NULL COMMENT '合同名称',
    `CUST_ID` varchar(32) DEFAULT NULL COMMENT '客户ID',
    `CUST_NAME` varchar(100) DEFAULT NULL COMMENT '客户名称',
    `BU_DEPT` varchar(50) DEFAULT NULL COMMENT 'BU事业部',
    `SALES_ID` varchar(32) DEFAULT NULL COMMENT '销售经理ID',
    `SALES_NAME` varchar(50) DEFAULT NULL COMMENT '销售经理姓名',
    `PROJECT_NAME` varchar(100) DEFAULT NULL COMMENT '项目简称',
    `SETTLEMENT_TYPE` varchar(50) DEFAULT NULL COMMENT '结算类型',
    
    -- 关联ID字段
    `COST_IDS` varchar(255) DEFAULT NULL COMMENT '关联的成本ID列表，逗号分隔',
    `COST_NAMES` varchar(255) DEFAULT NULL COMMENT '关联的成本的名字列表，逗号分隔',
    `INVOICE_IDS` varchar(255) DEFAULT NULL COMMENT '关联的发票ID列表，逗号分隔',
    `INVOICE_NOS` varchar(255) DEFAULT NULL COMMENT '关联发票的编号列表，逗号分隔',
    
    -- 金额相关字段
    `TOTAL_KP_AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '开票总金额(含税)',
    `TOTAL_KP_NO_TAX` decimal(10,2) DEFAULT '0.00' COMMENT '开票总金额(不含税)',
    `TOTAL_COST_AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '成本总额(含税)',
    `TOTAL_COST_NO_TAX` decimal(10,2) DEFAULT '0.00' COMMENT '成本总额(不含税)',
    `GROSS_PROFIT` decimal(10,2) DEFAULT '0.00' COMMENT '毛利',
    `GROSS_PROFIT_RATE` decimal(10,4) DEFAULT '0.0000' COMMENT '毛利率',

    -- 回款相关字段
    `PAYMENT_STATUS` tinyint(4) DEFAULT '0' COMMENT '回款状态(0未回款/1部分回款/2已回款)',
    `PAY_AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '已回款金额',
    `PAYMENT_TIME` varchar(19) DEFAULT NULL COMMENT '回款时间',
    
    -- 基础字段
    `REMARK` varchar(500) DEFAULT NULL COMMENT '备注',
    `CREATE_TIME` varchar(19) DEFAULT NULL COMMENT '创建时间',
    `UPDATE_TIME` varchar(19) DEFAULT NULL COMMENT '更新时间',
    `CREATOR` varchar(32) DEFAULT NULL COMMENT '创建人ID',
    `CREATE_NAME` varchar(30) DEFAULT NULL COMMENT '创建人姓名',
    PRIMARY KEY (`SETTLEMENT_ID`) USING BTREE,
    KEY `IDX_MONTH_ID` (`MONTH_ID`) USING BTREE,
    KEY `IDX_CONTRACT_ID` (`CONTRACT_ID`) USING BTREE,
    KEY `IDX_PLATFORM_ID` (`PLATFORM_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结算表';


-- 20250514 成本表加上成本归属字段
ALTER TABLE yq_crm_cost
    ADD COLUMN `COST_OWNER` tinyint(4) DEFAULT '0' COMMENT '成本归属(0平台成本、1客户成本、2其他)';
