package com.yunqu.work.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.CommentModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.TaskModel;
import com.yunqu.work.service.CommentService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.OpLogService;
import com.yunqu.work.service.TaskNoticeService;
import com.yunqu.work.service.WxMsgService;
@WebServlet("/servlet/task/*")
public class TaskServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private static JSONObject taskNames=new JSONObject();
	static{
		taskNames.put("10", "待办");
		taskNames.put("20", "进行中");
		taskNames.put("30", "已完成");
		taskNames.put("40", "已验收");
		taskNames.put("42", "验收不通过");
	}
	public EasyResult actionForAdd(){
		TaskModel model=getModel(TaskModel.class, "task");
		model.addCreateTime();
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.setCreator(getUserPrincipal().getUserId());
		model.set("TASK_STATE", 10);
		model.set("update_by", getUserId());
		if("0".equals(model.getString("CONTRACT_ID"))){
			model.remove("CONTRACT_ID");
		}
		String[] cc=null;
		try {
			JSONArray array=model.getJSONArray("NOTICE_TYPES");
			JSONObject jsonObject=getJSONObject();
			JSONArray mailtos=jsonObject.getJSONArray("mailtos");
			model.remove("NOTICE_TYPES");
			model.put("NOTICE_TYPES",StringUtils.join(array.toArray(), ","));
			
			if(mailtos!=null){
				cc=new String[mailtos.size()];
				for(int i=0;i<mailtos.size();i++){
					String userId=mailtos.getString(i);
					cc[i]=userId;
					EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
					record.setPrimaryValues(model.getTaskId(),userId);
					record.set("CREATOR", getUserPrincipal().getUserId());
					record.set("CREATE_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
					if(ServerContext.isLoginAuth()){
						String desc=model.getString("TASK_DESC");
						if(StringUtils.notBlank(desc)){
							desc=desc.replaceAll("/files", "http://work.yunqu-info.cn/files");
							model.set("TASK_DESC", desc);
						}
					}
					MessageModel messageModel=new MessageModel();
					messageModel.setReceiver(userId);
					messageModel.setContents("【云趣协同】抄送|"+model.getTaskName());
					messageModel.setFkId(model.getTaskId());
					messageModel.setSender(getUserId());
					messageModel.setMsgState(1);
					messageModel.setMsgType(2001);
					messageModel.setSenderType(0);
					messageModel.setDesc(model.getString("TASK_DESC"));
					messageModel.setData(model);
					messageModel.setTplName("newTask.html");
					try {
						MessageService.getService().sendMsg(messageModel);
						EmailService.getService().sendEmail(messageModel);
						WxMsgService.getService().sendNewTaskMsg(messageModel);
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			MessageModel messageModel=new MessageModel();
			messageModel.setReceiver(model.getString("ASSIGN_USER_ID"));
			messageModel.setContents("【云趣协同】"+model.getTaskName());
			messageModel.setFkId(model.getTaskId());
			messageModel.setSender(getUserId());
			messageModel.setMsgState(1);
			messageModel.setMsgType(2001);
			messageModel.setSenderType(0);
			if(ServerContext.isLoginAuth()){
				String desc=model.getString("TASK_DESC");
				if(StringUtils.notBlank(desc)){
					desc=desc.replaceAll("/files", "http://work.yunqu-info.cn/files");
					model.set("TASK_DESC", desc);
				}
			}
			messageModel.setCc(cc);
			messageModel.setData(model);
			messageModel.setTplName("newTask.html");
			try {
				MessageService.getService().sendMsg(messageModel);
				EmailService.getService().sendTaskEmail(messageModel);
				messageModel.setDesc(model.getString("TASK_DESC"));
				WxMsgService.getService().sendNewTaskMsg(messageModel);
			} catch (Exception e) {
				this.error(e.getMessage(), e);
			}
			
			model.save();
			OpLogService.getService().addLog(getUserId(), model.getTaskId(),"新增任务",getRequestUrl());
			String projectId=model.getString("PROJECT_ID");
			if(StringUtils.notBlank(projectId)){
				this.getQuery().executeUpdate("update YQ_PROJECT set UPDATE_TIME = ? where project_id = ?", EasyDate.getCurrentDateString(),projectId);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"创建成功.");
	}
	public EasyResult actionForUpdateState(){
		JSONObject jsonObject=getJSONObject();
		TaskModel model=getModel(TaskModel.class, "task");
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		int taskState=model.getTaskState();
		Integer oldTaskState=jsonObject.getIntValue("TASK_STATE");
		String assignUserId=jsonObject.getString("ASSIGN_USER_ID");//负责人
		String creator=jsonObject.getString("CREATOR");//发起人
		String taskName=jsonObject.getString("TASK_NAME");
		if(oldTaskState==taskState&&oldTaskState!=42){
			return EasyResult.fail("状态没更改!");
		}
		try {
			MessageModel messageModel=new MessageModel();
			messageModel.setFkId(model.getTaskId());
			messageModel.setSender(getUserId());
			messageModel.setMsgState(1);
			messageModel.setMsgType(2001);
			messageModel.setSenderType(0);
			
			messageModel.setData(model);
			messageModel.setTitle("【云趣协同】"+jsonObject.getString("TITLE"));
			//任务完成
			if(taskState==30){
				model.set("FINISH_TIME", EasyDate.getCurrentDateString());
				messageModel.setTplName("finishedTask.html");
				messageModel.setContents(taskName);
				messageModel.setReceiver(creator);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
					WxMsgService.getService().sendFinishTaskMsg(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			//校验完毕
			if(taskState==40){
				messageModel.setTplName("checkdTask.html");
				model.set("CHECK_TIME", EasyDate.getCurrentDateString());
				messageModel.setReceiver(assignUserId);
				messageModel.setContents(taskName);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			//校验不通过
			if(taskState==42){
				String desc=jsonObject.getString("comment");
				messageModel.setTplName("failedcheckdTask.html");
				messageModel.setReceiver(assignUserId);
				messageModel.setContents(taskName);
				model.set("TASK_DESC", desc);
				messageModel.setData(model);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			model.update();
			OpLogService.getService().addLog(getUserPrincipal().getUserId(), model.getTaskId(),"修改任务进度["+taskNames.getString(String.valueOf(taskState))+"]",getRequestUrl());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"修改成功.");
	}
	/**
	 * 转派
	 * @return
	 */
	public EasyResult actionForToOther(){
		TaskModel model=getModel(TaskModel.class, "task");
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("update_by", getUserId());
		try {
			String[] cc=null;
			JSONObject jsonObject=getJSONObject();
			JSONArray mailtos=jsonObject.getJSONArray("mailtos");
			if(mailtos!=null&&mailtos.size()>0){
				cc=new String[mailtos.size()];
				for(int i=0;i<mailtos.size();i++){
					String userId=mailtos.getString(i);
					try {
						EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
						record.setPrimaryValues(model.getTaskId(),userId);
						record.set("CREATOR", getUserPrincipal().getUserId());
						record.set("CREATE_TIME", EasyDate.getCurrentDateString());
						this.getQuery().save(record);
						cc[i]=userId;
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			String otherUserId=getJsonPara("otherUserId");
			if(StringUtils.notBlank(otherUserId)){
				model.set("ASSIGN_USER_ID", otherUserId);
				model.update();
				String comment="转派给"+getJsonPara("toOtherName");
				if(StringUtils.notBlank(comment)){
					CommentService.getService().addComment(getUserId(), model.getTaskId(), comment, WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"));
				}
				MessageModel messageModel=new MessageModel();
				messageModel.setReceiver(otherUserId);
				messageModel.setContents("【云趣协同】"+model.getTaskName());
				messageModel.setFkId(model.getTaskId());
				messageModel.setSender(getUserId());
				messageModel.setMsgState(1);
				messageModel.setMsgType(2001);
				messageModel.setSenderType(0);
				if(ServerContext.isLoginAuth()){
					String desc=model.getString("TASK_DESC");
					if(StringUtils.notBlank(desc)){
						desc=desc.replaceAll("/files", "http://work.yunqu-info.cn/files");
						model.set("TASK_DESC", desc);
					}
				}
				messageModel.setCc(cc);
				messageModel.setData(model);
				messageModel.setTplName("newTask.html");
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
					messageModel.setDesc(model.getString("TASK_DESC"));
					WxMsgService.getService().sendNewTaskMsg(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
				
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"转派成功.");
	}
	public EasyResult actionForUpdate(){
		TaskModel model=getModel(TaskModel.class, "task");
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("update_by", getUserId());
		try {
			JSONArray array=model.getJSONArray("NOTICE_TYPES");
			model.remove("NOTICE_TYPES");
			if(array!=null&&array.size()>0){
				model.put("NOTICE_TYPES",StringUtils.join(array.toArray(), ","));
			}
			JSONObject jsonObject=getJSONObject();
			JSONArray mailtos=jsonObject.getJSONArray("mailtos");
			if(mailtos!=null&&mailtos.size()>0){
				this.getQuery().executeUpdate("delete from yq_cc where FK_ID = ?", model.getTaskId());
				for(int i=0;i<mailtos.size();i++){
					String usertId=mailtos.getString(i);
					EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
					record.setPrimaryValues(model.getTaskId(),usertId);
					record.set("CREATOR", getUserPrincipal().getUserId());
					record.set("CREATE_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
				}
			}
			model.update();
			String comment=jsonObject.getString("comment");
			if(StringUtils.notBlank(comment)){
				CommentService.getService().addComment(getUserId(), model.getTaskId(), comment, WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"));
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"修改成功.");
	}
	public EasyResult actionForSaveComment(){
		JSONObject jsonObject=getJSONObject();
		CommentModel model=new CommentModel();
		model.setFkId(jsonObject.getString("fkId"));
		model.setContent(jsonObject.getString("content"));
		model.setCreator(getUserPrincipal().getUserId());
		model.setCreateTime(EasyDate.getCurrentDateString());
		model.setUserAgent(getRequest().getHeader("user-agent"));
		model.setIp(WebKit.getIP(getRequest()));
		return CommentService.getService().addComment(model);
	}
	
	public EasyResult actionForShare(){
		String taskId=getJsonPara("TASK_ID");
		try {
			this.getQuery().executeUpdate("update yq_task set task_auth = 0 where task_id = ?", taskId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"分享成功!");
	}
	public EasyResult actionForDelete(){
		TaskModel model=getModel(TaskModel.class, "task");
		try {
			model.delete();
			OpLogService.getService().addLog(getUserPrincipal().getUserId(), model.getTaskId(),"删除任务",getRequestUrl());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	public EasyResult actionForNoticeMsg(){
		new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					TaskNoticeService.getService().run();
				} catch (Exception e) {
					Thread.currentThread().interrupt();
					getLogger().error(e.getMessage(),e);
				}
			}
		}).start();
		return EasyResult.ok();
	}
}





