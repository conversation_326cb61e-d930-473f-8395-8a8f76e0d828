<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>首页</title>
	<link href="${ctxPath}/static/css/admin.css" rel="stylesheet">
	<link href="//at.alicdn.com/t/font_1002403_34h7imibmwx.css" rel="stylesheet">
	<style>
    	 a{text-decoration: none!important;}
    	 .layuiadmin-card-status li h3{font-size:16px;}
    	 .tw-list{padding: 0px 0px 10px!important;}
    	 .tw-list [data-w-e]{width: 24px;}
    	 .tw-list span img{max-width: 90%!important;margin: 0 2px;}
    	 .layuiadmin-card-link a{width: auto;padding:0px 10px}
    	 .layuiadmin-card-text .layui-text-top i{font-size: 22px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md8">
        <div class="layui-row layui-col-space15">
          <div class="layui-col-md6">
            <div class="layui-card">
              <div class="layui-card-header">快捷入口</div>
              <div class="layui-card-body">
                <div class="layui-carousel layadmin-carousel layadmin-shortcut" lay-anim="" lay-indicator="inside" lay-arrow="none" style="width: 100%; height: 280px;">
                  <div carousel-item="">
                    <ul class="layui-row layui-col-space10 layui-this">
                      <li class="layui-col-xs3">
                        <a href="javascript:void(0)" onclick="weekly()">
                          <i class="layui-icon iconfont icon-zhoubao"></i>
                          <cite>填写周报</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:void()" onclick="addressList()">
                          <i class="layui-icon iconfont icon-tongxunlu"></i>
                          <cite>通讯录</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:void()" onclick="moreDoc()">
                          <i class="layui-icon iconfont icon-zhishiku"></i>
                          <cite>文档</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a  href="javascript:void(0)" onclick="top.chat();">
                          <i class="layui-icon layui-icon-chat"></i>
                          <cite>IM聊天</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:void(0)" onclick="moreTask()">
                          <i class="layui-icon iconfont icon-renwu"></i>
                          <cite>任务</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:void()" onclick="zendao()">
                          <i class="layui-icon iconfont icon-chandao"></i>
                          <cite>禅道</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="http://*************:17210/cloud/" target="_blank">
                          <i class="layui-icon iconfont icon-qiyeyunzongji"></i>
                          <cite>云总机</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a target="_blank" href="http://yqoa.pcitech.com/">
                          <i class="layui-icon iconfont icon-OAliuchengguanli"></i>
                          <cite>OA流程</cite>
                        </a>
                      </li>
                    </ul>
                    <ul class="layui-row layui-col-space10">
                      <li class="layui-col-xs3">
                        <a href="/finder-web/sso.jsp" target="_blank">
                          <i class="layui-icon iconfont icon-xiazai"></i>
                          <cite>下载</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:void()" onclick="moreDc()">
                          <i class="layui-icon iconfont icon-dingcan"></i>
                          <cite>订餐</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:void(0)" onclick="moreTodo()">
                          <i class="layui-icon iconfont icon-richeng"></i>
                          <cite>日程</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:void(0)" onclick="moreNote()">
                          <i class="layui-icon iconfont icon-shenhebijijishibenxiezi"></i>
                          <cite>笔记</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:void(0)" onclick="moreTw()">
                          <i class="layui-icon iconfont icon-DT"></i>
                          <cite>我的动弹</cite>
                        </a>
                      </li>
                      <li class="layui-col-xs3">
                        <a href="javascript:void(0)" onclick="moreNav()">
                          <i class="layui-icon iconfont icon-wangzhidaohang"></i>
                          <cite>我的网址</cite>
                        </a>
                      </li>
                    </ul>
                    
                  </div>
                <div class="layui-carousel-ind"><ul><li class="layui-this"></li><li class=""></li></ul></div><button class="layui-icon layui-carousel-arrow" lay-type="sub"></button><button class="layui-icon layui-carousel-arrow" lay-type="add"></button></div>
                
              </div>
            </div>
          </div>
          <div class="layui-col-md6 render-1">
            <div class="layui-card">
              <div class="layui-card-header">待办事项</div>
              <div class="layui-card-body">

                <div class="layui-carousel layadmin-carousel layadmin-backlog" lay-anim="" lay-indicator="inside" lay-arrow="none" style="width: 100%; height: 280px;">
                  <div carousel-item="" data-mars="HomeDao.waitDo">
                    <ul class="layui-row layui-col-space10 layui-this">
                      <li class="layui-col-xs6">
                        <a href="${ctxPath}/servlet/ehr?action=email" target="_blank" class="layadmin-backlog-body">
                          <h3>未读邮件</h3>
                          <p><cite id="emailNum">0</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs6">
                        <a class="layadmin-backlog-body" href="javascript:void(0)" onclick="moreTask()">
                          <h3>代办任务数</h3>
                          <p><cite id="taskNum">0</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs6">
                        <a  class="layadmin-backlog-body" href="javascript:void(0)" onclick="weekly()">
                          <h3>周报未阅数</h3>
                          <p><cite id="weeklyNum">0</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs6">
                        <a href="javascript:void(0)" class="layadmin-backlog-body" onclick="openMydoWf();">
                          <h3>流程待批数</h3>
                          <p><cite id="wfDpNum">0</cite></p>
                        </a>
                      </li>
                    </ul>
                    <ul class="layui-row layui-col-space10">
                      <li class="layui-col-xs6">
                        <a href="javascript:void(0)" class="layadmin-backlog-body" onclick="openMyAllWf()">
                          <h3>流程代办数</h3>
                          <p><cite id="wfDbNum">0</cite></p>
                        </a>
                      </li>
                      <li class="layui-col-xs6">
                        <a href="javascript:void(0)" class="layadmin-backlog-body"  onclick="openMyAllWf()">
                          <h3>流程待阅数</h3>
                          <p><cite id="wfDyNum" style="color: #FF5722;">0</cite></p>
                        </a>
                      </li>
                    </ul>
                  </div>
                <div class="layui-carousel-ind"><ul><li class="layui-this"></li><li></li></ul></div><button class="layui-icon layui-carousel-arrow" lay-type="sub"></button><button class="layui-icon layui-carousel-arrow" lay-type="add"></button></div>
              </div>
            </div>
          </div>
          <div class="layui-col-md12 render-2">
            <div class="layui-card">
              <div class="layui-tab layui-tab-brief layadmin-latestData taskList">
                <ul class="layui-tab-title">
                  <li class="layui-this">我的任务</li>
                  <li class="" id="zendaoNum">我的禅道</li>
                </ul>
                <div class="layui-tab-content ">
                  <div class="layui-tab-item layui-show">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="task-template" data-mars="HomeDao.myTaskList" style="width: 100%;">
			           
		            </ul>
                  </div>
                  <script id="task-template" type="text/x-jsrender">
						 {{if data.length==0}}<li><p style="text-align:center;">暂无任务</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> {{call:TASK_STATE fn='taskStateLabel'}}&nbsp;<a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')">{{:TASK_NAME}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{call:CREATOR fn='getUserName'}}</span>
			                <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:CREATE_TIME}}</span>
			                <a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">处理</a>
			              </li>
			              {{/for}}
				 </script>
                  <div class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="zendao-template" data-mars="HomeDao.getBugList" style="width: 100%;">
		            </ul> 
                  </div>
                  <script id="zendao-template" type="text/x-jsrender">
						{{call:data fn='zendaoNum'}}
						{{if data.length==0}}<li><p style="text-align:center;">暂无数据</p></li>{{/if}}
			            {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i>&nbsp;<a href="javascript:;" onclick="zentao('{{:ID}}')">{{:TITLE}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{:OPENEDBY}}</span>
			                <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:ASSIGNEDDATE}}</span>
			                <a href="javascript:;" onclick="zentao('{{:ID}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			            {{/for}}
				 </script>
                </div>
              </div>
            </div>
           <div class="layui-card">
	          <div class="layui-tab layui-tab-brief layadmin-latestData">
	             <a style="position: absolute;;right: 20px;margin-top: 10px;z-index: 9999;color: rgb(1, 170, 237);" href="javascript:void(0)" onclick="popup.openTab({url:'${ctxPath}/pages/doc/doc-list.jsp',title:'知识库'})" class="layui-a-tips">查看全部</a>
                <ul class="layui-tab-title">
                  <li class="layui-this"> 知识库  </li>
                <!--   <li>问答区</li> -->
                </ul>
                <div class="layui-tab-content">
                  <div class="layui-tab-item layui-show">
                      <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-mars="HomeDao.doc" style="width: 100%;">
			            {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> <a href="javascript:;" onclick="docDetail('{{:DOC_ID}}','{{:FOLDER_ID}}')">{{:TITLE}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{call:CREATOR fn='getUserName'}}</span>
			                <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:UPDATE_TIME}}</span>
			                <span class="ml-15"><i class="fa fa-eye"></i> {{:VIEW_COUNT}}</span>
			                <a href="javascript:;" onclick="docDetail('{{:DOC_ID}}','{{:FOLDER_ID}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
		            </ul>
                  </div>
                 <!--  <div class="layui-tab-item">
		                <dl class="layuiadmin-card-status">
			              <dd>
			                <div class="layui-status-img"><a href="javascript:;"><img src="https://www.layui.com/admin/std/dist/layuiadmin/style/res/logo.png"></a></div>
			                <div>
			                  <p>胡歌 在 <a lay-href="http://fly.layui.com/vipclub/list/layuiadmin/">layuiadmin专区</a> 回答问题</p>
			                  <span>几秒前</span>
			                </div>
			              </dd>
			            </dl> 
                  </div> -->
                </div>
              </div>
	        </div>
            <div class="layui-card">
	          <div class="layui-card-header">通知公告<a onclick="openNotices()" href="javascript:void(0)" class="layui-a-tips">查看全部</a></div>
	          <div class="layui-card-body">
	            <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-mars="HomeDao.notices" style="width: 100%">
	             {{for data}}
	              <li>
	                <p><span class="layui-badge-rim" style="color: #b4a2a2;height: 20px;line-height: 20px;">{{call:REMIND_TYPE fn='noticeLabel'}}</span> <a href="javascript:void(0)"  onclick="remindDetail('{{:REMIND_ID}}','{{:REMIND_TYPE}}')">{{:TITLE}}</a></p>
	                <span class="ml-5"><i class="fa fa-user-o"></i>  {{:PUBLISH_BY}}</span>
	                <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:PUBLISH_TIME}}</span>
	                <span class="ml-15"><i class="fa fa-eye"></i> {{:VIEW_COUNT}}</span>
	                <a href="javascript:void(0)" onclick="remindDetail('{{:REMIND_ID}}','{{:REMIND_TYPE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
	              </li>
	              {{/for}}
	            </ul>
	          </div>
	        </div>
            <div class="layui-card">
		          <div class="layui-card-header">
		                                    工具推荐
		          </div>
		          <div class="layui-card-body">
		            <div class="layui-row layui-col-space10" data-mars="HomeDao.queryTopicNav">
		              {{for data}}
		              <div class="layui-col-xs12 layui-col-sm4 layui-text">
		                <div class="layuiadmin-card-text">
		                  <div class="layui-text-top"><i class="{{:ICON}}"></i><a target="_blank" href="{{:URL}}">{{:NAV_NAME}}</a></div>
		                  <p class="layui-text-center">{{:NAV_DESC}}</p>
		                  <p class="layui-text-bottom"><a href="javascript:void(0)">{{:TYPE_NAME}}</a><span>{{:PUBLISH_TIME}}</span></p>
		                </div>
		              </div>
		              {{/for}}
		            </div>
	             </div>
	         </div>
          </div>
        </div>
      </div>
      
      <div class="layui-col-md4 render-3">
        <div class="layui-card">
          <div class="layui-card-header">个人中心</div>
          <div class="layui-card-body layui-text">
            <table class="layui-table" lay-skin="nob">
              <tbody>
                <tr> 
                  <td>
                  	 <c:choose>
                  	 	<c:when test="${openId==''}">
                  	 	         您没绑定微信  <a href="javascript:void(0)" onclick="bindWx();" style="padding-left: 15px;">立即绑定</a> 
                  	 	</c:when>
                  	 	<c:otherwise>
		                     <img style="border-radius:50%;width: 38px;height: 38px;" onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="${user.picUrl}">
		                     ${user.nikeName} <a href="javascript:void(0)" onclick="bindWx();" style="padding-left: 15px;">更绑微信</a> 
                  	 	</c:otherwise>
                  	 </c:choose>
                  </td>
                </tr>
                <tr>
                  <td>
                   ${user.username } /
					${user.depts} /
					${user.data2}
                 </td>
                </tr>
                <tr>
                  <td>
                  		<c:choose>
                  			<c:when test="${OA=='ok'}">
                  				OA已绑定
                  			</c:when>
                  			<c:when test="${OA=='fail'}">
                  				OA绑定失效 &nbsp;<a href="javascript:void(0)" onclick="bindOA('update')" class="layui-btn layui-btn-danger layui-btn-xs">重新绑定</a>
                  			</c:when>
                  			<c:otherwise>
                  				OA没用绑定 	&nbsp;<a href="javascript:void(0)" onclick="bindOA('add')" class="layui-btn layui-btn-danger layui-btn-xs">马上绑定</a>
                  			</c:otherwise>
                  		</c:choose>
                  	
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
         <div class="layui-card dc">
	          <div class="layui-card-header">一日三餐 <a href="javascript:void(0);" onclick="foodSetting();" class="layui-a-tips">午餐预定</a></div>
	          <div class="layui-card-body layui-text">
           			<table class="layui-table" lay-skin="nob" style="padding: 10px 6px;width: 100%">
						<tbody id="booksInfo"></tbody>
					</table>
					<div class="text-c mt-10">
						<button type="button" id="doDc" class="btn btn-info btn-sm mr-10 text-r btn-outline" onclick="bookFood()"> <i class="glyphicon glyphicon-cutlery"></i> 点餐</button>
						<a href="tencent://message/?uin=874618121&Menu=yes" id="addDoDc" class="btn btn-success btn-sm btn-outline mr-10 text-r" onclick="bookFood()">申请补点</a>
					</div>
	          </div>
         </div>
        <div class="layui-card">
          <div class="layui-card-header">网址导航
          	  <button class="layui-btn layui-btn-primary layui-btn-xs" style="position: absolute;right: 10px;top: 10px;" onclick="addUserNav()">
                + 添加
              </button>
          </div>
          <div class="layui-card-body nav-card">
            <div class="layuiadmin-card-link layui-text" data-template="userNav-template" data-mars="HomeDao.queryUserNav"></div>
             <script id="userNav-template" type="text/x-jsrender">  
              	  {{if data.length==0}}<a href="javascript:void(0)" onclick="addUserNav()">+立即添加</a>{{/if}}
              	  {{for data}}
	             	 <a onclick="userNavDetail('{{:NAV_ID}}')" href="javascript:void(0);">{{:TITLE}}</a>
	              {{/for}}
			</script>      
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">我参与的项目</div>
          <div class="layui-card-body">
         		 <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="project-template" data-mars="HomeDao.myProject">
	                <script id="project-template" type="text/x-jsrender">
						    {{if data.length==0}}<li><p style="text-align:center;">暂无数据</p></li>{{/if}}
		              		{{for data}}
		               			<li>
	                				<p style="padding-bottom:0px;cursor: pointer;"><i class="layui-icon layui-icon-right"></i> <a onclick="projectDetail('{{:PROJECT_NAME}}','{{:PROJECT_ID}}')">{{call:PROJECT_NAME 40 fn='cutText'}}</a></p>
	             				 </li>
		              		{{/for}}
        			</script>
	             </ul>
          </div>
        </div>
        
       <div class="layui-card tw-card">
	          <div class="layui-card-header">动弹
	          	 <button class="layui-btn layui-btn-xs layui-btn-primary ml-15" style="margin-top: -4px;" type="button" onclick="addTwitter()">+发布</button>
	         	 <!-- <a lay-href="" class="layui-a-tips">查看全部</a> -->
	         	 </div>
	          	 <div class="layui-card-body">
	              <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text tw-list" data-template="tw-template" data-mars="HomeDao.tweet"></ul>
        			<script id="tw-template" type="text/x-jsrender">
						    {{if data.length==0}}<li><p style="text-align:center;">暂无数据</p></li>{{/if}}
		              		{{for data}}
		               			<li>
		               				{{if PUBLISH_BY=='匿名'}}<i style="width: 48px;height: 48px;float:left;font-size:30px;color:#ce67b8;text-align:center;" class="iconfont icon-niming1 mt-10"></i>{{else}} <img style="width: 48px;height: 48px;border-radius:50%;float:left"  onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}"> {{/if}}
								     <p style="margin-top:8px;margin-left:50px;" onclick="remindDetail('{{:REMIND_ID}}','{{:REMIND_TYPE}}')"> 
									  <span style="color:red">{{:PUBLISH_BY}}</span>
		               	 			  <span style="color:#111;"  title="{{:PUBLISH_TIME}}">{{call:CONTENT fn='getTwContent'}}</span>
								     </p>
									 <div style="margin-left:48px;color:#777;font-size:12px;">{{call:PUBLISH_TIME fn='getDateDes'}}<a href="javascript:void(0)" onclick="remindDetail('{{:REMIND_ID}}','{{:REMIND_TYPE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a></div>
		               			</li>
		              		{{/for}}
        			</script>
	          </div>
	        </div>
        <!-- <div class="layui-card">
          <div class="layui-card-header">产品动态</div>
          <div class="layui-card-body">
            <div class="layui-carousel layadmin-carousel layadmin-news" data-autoplay="true" data-anim="fade" lay-filter="news" lay-anim="fade" lay-indicator="inside" lay-arrow="none" style="width: 100%; height: 280px;">
              <div carousel-item="">
                <div class=""><a href="http://fly.layui.com/docs/2/" target="_blank" class="layui-bg-red">layuiAdmin 快速上手文档</a></div>
                <div class="layui-this"><a href="http://fly.layui.com/vipclub/list/layuiadmin/" target="_blank" class="layui-bg-green">layuiAdmin 会员讨论专区</a></div> 
                <div class=""><a href="http://www.layui.com/admin/#get" target="_blank" class="layui-bg-blue">获得 layui 官方后台模板系统</a></div>
              </div>
            <div class="layui-carousel-ind"><ul><li class=""></li><li class="layui-this"></li><li class=""></li></ul></div><button class="layui-icon layui-carousel-arrow" lay-type="sub"></button><button class="layui-icon layui-carousel-arrow" lay-type="add"></button></div>
          </div>
        </div> -->
  		<div class="layui-card">
          <div class="layui-card-header">我的考勤 </div>
          <div class="layui-card-body" data-mars="HomeDao.queryKq" data-template="kq-template">
             <script id="kq-template" type="text/x-jsrender">
               {{if data.length==0}}<p>暂无数据</p>{{/if}}
               <table class="layui-table" lay-skin="line">
              	 {{if data.length>0}}
					<colgroup>
        			 <col width="40%">
       				 <col width="30%">
       				 <col width="30%">
     				</colgroup> 
					<thead>
   					   <tr>
     				 	 <th>日期</th>
     					 <th>上班时间</th>
      					 <th>下班时间</th>
   						</tr> 
  					</thead>
              	  {{for data}}
                    <tr>
                 	 <td>
                  		{{:DK_DATE}}
                  	</td>
                 	 <td>
                  		{{:SIGN_IN}}&nbsp;{{:REMARK}}
                  	</td>
                 	 <td>
                  		{{:SIGN_OUT}}&nbsp;{{:REMARK}}
                  	</td>
                	</tr>
                   {{/for}}
				 {{/if}}
			</table>
		   </script>
          </div>
        </div>
  		<!-- <div class="layui-card">
          <div class="layui-card-header">在线用户</div>
          <div class="layui-card-body">
            
                  
          </div>
        </div> -->
        <div class="layui-card">
          <div class="layui-card-header">
            	微信扫码关注
            	<a href="http://work.yunqu-info.cn/" class="layui-a-tips" target="_blank">公网访问</a>
          </div>
          <div class="layui-card-body layui-text layadmin-text">
            	<img alt="" src="/yq-work/static/images/qrcode.jpg">
          </div>
        </div>
      </div>
    </div>
	<div style="display: none;" id="qcode">
		<div style="margin-left:40px;" id="codeContainer"></div><br><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;微信扫码关注,再次扫码绑定后关闭弹出窗</p>
	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		var ctxPath='${ctxPath}';
		var userId='${user.userId}';
		var pwd='${pwd}';
		var openId='${openId}';
		var oaFlag='${OA}';

	</script>
   <script type="text/javascript" src="${ctxPath}/static/js/jquery.qrcode.min.js"></script>
   <script type="text/javascript" src="${ctxPath}/static/js/index.js?v=1000"></script>
   <script type="text/javascript" src="${ctxPath}/static/js/dc.js?v=20190107"></script>
   <script src="${ctxPath}/static/js/ws.js"></script>

<script type="text/javascript">
	ws.init("ws://"+location.host+"${ctxPath}/websocket/${userData.userId}/${userData.loginAcct}",{
		autoReconnect:true,
		onmessage:function(data){
			console.log(new Date()+">>>"+JSON.stringify(data));
		    layer.msg(data.msg,{offset:'rt',title:data.title||'消息通知',time:30000,icon:7});
		},//收到消息
		onopen:function(event){
			console.log(new Date()+' connection success');
		},//ws链接成功
		onclose:function(event){
			console.log(new Date()+' connection close');
		},//ws关闭
		onerror:function(event){
			console.log(new Date()+' connection error');
		},//ws错误
	})
</script>
</EasyTag:override>

<%@ include file="/pages/common/layout_list.jsp" %>
