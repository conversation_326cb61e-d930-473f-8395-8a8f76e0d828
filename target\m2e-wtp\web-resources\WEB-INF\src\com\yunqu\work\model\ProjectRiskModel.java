package com.yunqu.work.model;

import com.yunqu.work.base.AppBaseModel;
import org.easitline.common.utils.string.StringUtils;

public class ProjectRiskModel extends AppBaseModel {
    private static final long serialVersionUID = 1L;

    public ProjectRiskModel() {
        setTableInfo("yq_project_risk","risk_id");
    }

    public String getRiskId(){
        return getString("risk_id");
    }

    public void setRiskId(String riskId){
        this.set("risk_id",riskId);
    }

    public String getProjectId(){
        return getString("project_id");
    }

    public void setProjectId(String projectId){
        this.set("project_id",projectId);
    }

    public String getProjectName(){
        return getString("project_name");
    }

    public void setProjectName(String projectName){
        this.set("project_name",projectName);
    }

}
