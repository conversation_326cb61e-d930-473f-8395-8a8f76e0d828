package com.yunqu.work.servlet.common;

import java.util.List;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.base.Constants;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.utils.DateUtils;



@Before(AuthInterceptor.class)
@Path(value = "/fly")
public class FlyController extends BaseController {

	public void index() {
		setAttr("indexPageFlag", "1");
		commonData();
		render("/pages/fly/fly-index.jsp");
	}
	
	public void my() {
		commonData();
		render("/pages/fly/user-index.jsp");
	}
	
	public void add() {
		commonData();
		render("/pages/fly/fly-index.jsp");
	}
	
	public void edit() {
		commonData();
		render("/pages/fly/fly-index.jsp");
	}
	
	public void detail() {
		commonData();
		String id = getPara();
		Record record = Db.findFirst("select * from yq_remind where remind_id = ?", id);
		String userId = record.getStr("creator");
		StaffModel model = StaffService.getService().getStaffInfo(userId);
		setAttr("userInfo",model);
		setAttr("articleInfo", record);
		setAttr("isSelf", userId.equalsIgnoreCase(getUserId())||isSuperUser());
		setAttr("isSuperUser", isSuperUser());
		
		Record selfData = Db.findFirst("select * from yq_fly_record where remind_id = ? and user_id = ?", id,getUserId());
		setAttr("selfData", selfData);
		
		List<Record> list = Db.find("select t1.*,t2.username,t2.pic_url from yq_comments t1,"+Constants.DS_MAIN_NAME+".easi_user t2  where t1.creator = t2.user_id and t1.fk_id = ? order by t1.accept_flag desc,t1.like_count desc,t1.create_time desc", id);
		setAttr("commentList", list);
		
		
		Record logRecord = new Record();
		logRecord.set("log_id", RandomKit.uuid());
		logRecord.set("fk_id",id);
		logRecord.set("look_by", getUserId());
		logRecord.set("look_name", getNickName());
		logRecord.set("look_time",EasyDate.getCurrentDateString());
		logRecord.set("url",getRequest().getRequestURL().toString());
		logRecord.set("agent_info",getRequest().getHeader("user-agent"));
		logRecord.set("ip",WebKit.getIP(getRequest()));
		logRecord.set("type","fly");
		Db.save("yq_look_log", "log_id", logRecord);
		Db.update("update yq_remind t1 JOIN( SELECT fk_id, COUNT(1) AS view_count FROM yq_look_log GROUP BY fk_id) t2 ON t2.fk_id = t1.remind_id SET t1.view_count = t2.view_count WHERE t1.remind_id = ?", id);
		
		updateFlag(id,"look_flag",true);
		
		render("/pages/fly/fly-detail.jsp");
	}
	
	public void jie() {
		setAttr("indexPageFlag", "0");
		commonData();
		render("/pages/fly/fly-index.jsp");
	}
	
	public void messageNums() {
		renderJson(EasyResult.ok(10));
	}
	
	public void reply() {
		String fkId = getPara("jid");
		String content = getPara("content");
		Record record = new Record();
		record.set("fk_id",fkId);
		record.set("content",content);
		record.set("comment_id", RandomKit.uuid());
		record.set("creator", getUserId());
		record.set("create_name", getNickName());
		record.set("create_time",EasyDate.getCurrentDateString());
		record.set("date_id",EasyCalendar.newInstance().getDateInt());
		record.set("user_agent",getRequest().getHeader("user-agent"));
		record.set("ip",WebKit.getIP(getRequest()));
		record.set("source","fly");
		Db.save("yq_comments", "comment_id", record);
		Db.update("UPDATE yq_remind t1 JOIN( SELECT fk_id, COUNT(1) AS comment_count FROM yq_comments GROUP BY fk_id) t2 ON t2.fk_id = t1.remind_id SET t1.comment_count = t2.comment_count WHERE t1.remind_id = ?", fkId);
		renderJson(EasyResult.ok(10));
	}
	
	public void getComment() {
		String id = getPara("id");
		EasyResult result = new EasyResult();
		JSONObject row = new JSONObject();
		String text = Db.queryStr("select content from  yq_comments where comment_id = ?",id);
		row.put("content",text);
		result.put("rows", row);
		renderJson(result);
	}
	
	public void zan() {
		String id = getPara("id");
		boolean ok = getBoolean("ok");
		if(ok) {
			Db.update("update yq_comments set like_count = like_count - 1 where comment_id = ?",id);
		}else {
			Db.update("update yq_comments set like_count = like_count + 1 where comment_id = ?",id);
		}
		renderJson(EasyResult.ok());
	}
	
	public void setTop() {
		String id = getPara("id");
		String field = getPara("field");
		String rank = getPara("rank");
		if("stick".equals(field)) {
			Db.update("update yq_remind set recommend_flag = ? where remind_id = ?", "0".equals(rank)?1:0,id);
		}else if("status".equals(field)) {
			Db.update("update yq_remind set wonderful_flag = ? where remind_id = ?", "0".equals(rank)?0:1,id);
			updateFlag(id,"accept_flag",true);
		}
		renderJson(EasyResult.ok(id));
	}
	
	private void updateFlag(String remindId,String fieldName,boolean isAdd) {
		this.updateFlag(remindId, null,fieldName, isAdd);
	}
	
	private void updateFlag(String remindId,String userId,String fieldName,boolean isAdd) {
		Record record = new Record();
		if(userId==null) {
			userId = getUserId();
		}
		record.set("record_id", MD5Util.getHexMD5(remindId+userId));
		record.set("remind_id",remindId);
		record.set("user_id",userId);
		record.set(fieldName, isAdd?1:0);
		boolean result = Db.update("yq_fly_record", "record_id", record);
		if(!result) {
			Db.save("yq_fly_record", "record_id", record);
		}
	}
	
	public void updateComment() {
		String id = getPara("id");
		String content = getPara("content");
		Db.update("update yq_comments set content = ? where comment_id = ?", content,id);
		EasyResult result = new EasyResult();
		renderJson(result);
	}
	
	public void rewardComment() {
		String id = getPara("id");
		Db.update("update yq_comments set accept_flag = ? where comment_id = ?", 1,id);
		String remindId =  Db.queryStr("select fk_id from yq_comments where comment_id = ?",id);
		Db.update("update yq_remind set close_flag = ? where remind_id = ?", 1,remindId);
		renderJson(EasyResult.ok(id));
	}
	
	public void deleteArticle() {
		String id = getPara("id");
		Db.update("update yq_remind set status = ? where remind_id = ?", 2,id);
		renderJson(EasyResult.ok(id));
	}
	
	public void deleteComment() {
		String id = getPara("id");
		String remindId =  Db.queryStr("select fk_id from yq_comments where comment_id = ?",id);
		Db.delete("delete from yq_comments where comment_id = ?", id);
		Db.update("update yq_remind t1 JOIN( SELECT fk_id, COUNT(1) AS comment_count FROM yq_comments GROUP BY fk_id) t2 ON t2.fk_id = t1.remind_id SET t1.comment_count = t2.comment_count WHERE t1.remind_id = ?", remindId);
		renderJson(EasyResult.ok(id));
	}
	
	public void collectionremove() {
		String id = getPara("cid");
		updateFlag(id,"collect_flag",false);
		renderJson(EasyResult.ok());
	}
	
	public void collectionadd() {
		String id = getPara("cid");
		updateFlag(id,"collect_flag",true);
		renderJson(EasyResult.ok());
	}
	
	public void collectionFind() {
		String cid = getPara("cid");
		JSONObject object = new JSONObject();
		object.put("collection", Db.queryInt("select collect_flag from yq_fly_record where remind_id = ? and user_id = ?", cid,getUserId())==1);
		renderJson(EasyResult.ok(object));
	}
	
	public void u() {
		String userId = getPara();
		if(StringUtils.isBlank(userId)) {
			userId =  getUserId();
		}
		StaffModel model = StaffService.getService().getStaffInfo(userId);
		if(model==null) {
			renderHtml("参数错误");
			return;
		}
		setAttr("userInfo",model);
		
		EasySQL userArticleSql = new EasySQL();
		userArticleSql.append("from yq_remind t1 where 1=1");
		userArticleSql.append("and t1.status = 0");
		userArticleSql.append(userId,"and t1.creator = ?");
		userArticleSql.append("order by t1.publish_time desc");
		Page<Record>  userArticleList = Db.paginate(1, 12, "select t1.*",userArticleSql.getSQL(),userArticleSql.getParams());
		setAttr("userArticleList",userArticleList.getList());
		
		EasySQL userCommentSql = new EasySQL();
		userCommentSql.append("from yq_remind t1,yq_comments t2 where t1.remind_id = t2.fk_id");
		userArticleSql.append("and t1.status = 0");
		userCommentSql.append(userId,"and t2.creator = ?");
		userCommentSql.append("order by t1.publish_time desc");
		Page<Record>  userCommentList = Db.paginate(1, 5, "select t1.title,t1.remind_id,t2.comment_id,t2.content,t2.create_time comment_time",userCommentSql.getSQL(),userCommentSql.getParams());
		setAttr("userCommentList",userCommentList.getList());
		render("/pages/fly/user-home.jsp");
	}
	
	public void remind() {
		commonData();
		render("/pages/fly/fly-index.jsp");
	}
	
	public void signStatus() {
		JSONObject data = new JSONObject();
		data.put("signed",true);
		data.put("days",20);
		data.put("experience",10);
		EasyResult result = EasyResult.ok();
		result.put("status",0);
//		result.setData(data);
		renderJson(result);
	}
	
	public void signIn() {
		JSONObject data = new JSONObject();
		data.put("signed",true);
		data.put("days",10);
		data.put("experience",100);
		EasyResult result = EasyResult.ok();
		result.put("status",0);
		result.setData(data);
		renderJson(result);
	}
	
	public void signTop() {
		JSONArray array = new JSONArray();
		JSONArray array1 = new JSONArray();
		JSONArray array2 = new JSONArray();
		JSONArray array3 = new JSONArray();
		
		List<Record> list = Db.find("select * from yq_user_sign order by visit_time limit 20");
		for(Record record:list) {
			JSONObject row = new JSONObject();
			row.put("uid", record.getStr("user_id"));
			row.put("time", record.getStr("visit_time"));
			row.put("days", 1);
			JSONObject userInfo = new JSONObject();
			userInfo.put("username", record.getStr("user_id"));
			row.put("user", userInfo);
			row.put("avatar", "https://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83ep3XqO6z7icxMxXUfcn4VgFf56JeZJrkricYZokJx81u0rx6d6GIOXHaP7D3a06hfZRaCxkjOZ2aicXA/132");
			array1.add(row);
		}
		array.add(array1);
		array.add(array2);
		array.add(array3);
		EasyResult result = EasyResult.ok();
		result.setData(array);
		renderJson(result);
	}
	
	
	private void commonData() {
	    String navStr = "[{id:'1',title:'公告'},{id:'2',title:'动态'}]";
	    JSONArray array = JSONObject.parseArray(navStr);
	    setAttr("itemList", array);
	    
		EasySQL sql = new EasySQL();
		String commonSql = "from yq_remind t1,"+Constants.DS_MAIN_NAME+".easi_user t2 where 1=1 and t1.creator = t2.user_id and t1.status = 0";
		sql.append(commonSql);
		String category = getPara("category","");
		if(StringUtils.notBlank(category)) {
			sql.append(category,"and t1.remind_code = ?");
		}
		String sort = getPara("sort","time");
		if("time".equals(sort)) {
			sql.append("order by t1.publish_time desc");
		}else {
			sql.append("order by t1.comment_count desc,t1.publish_time desc");
		}
		int pageNo = getParaToInt("page", 1);
		Page<Record>  list = Db.paginate(pageNo, 20, "select t1.*,t2.pic_url,t2.depts,t2.nike_name,t2.username,t2.user_id",sql.getSQL(),sql.getParams());
		setAttr("pageNo", pageNo+1);
		setAttr("allList",list.getList());
		
		EasySQL hotSql = new EasySQL();
		hotSql.append(commonSql);
		hotSql.append(DateUtils.getBeforeDate(60, "yyyy-MM-dd HH:mm:ss"),"and t1.publish_time >= ?");
		hotSql.append("order by t1.comment_count desc");
		Page<Record>  hostList = Db.paginate(1, 10, "select t1.*,t2.pic_url,t2.depts,t2.nike_name,t2.username",hotSql.getSQL(),hotSql.getParams());
		setAttr("hostList",hostList.getList());
		
		EasySQL topSql = new EasySQL();
		topSql.append(commonSql);
		topSql.append(0,"and t1.recommend_flag = ?");
		topSql.append(DateUtils.getBeforeDate(180, "yyyy-MM-dd HH:mm:ss"),"and t1.publish_time >= ?");
		topSql.append("order by t1.publish_time desc");
		Page<Record>  topList = Db.paginate(1, 10, "select t1.*,t2.pic_url,t2.depts,t2.nike_name,t2.username",topSql.getSQL(),topSql.getParams());
		setAttr("topList",topList.getList());
		
		EasySQL replyTopSql = new EasySQL();
		replyTopSql.append("from yq_comments t1,"+Constants.DS_MAIN_NAME+".easi_user t2 where 1=1 and t1.creator = t2.user_id");
		replyTopSql.append(DateUtils.getBeforeDate(180, "yyyyMMdd"),"and t1.DATE_ID >= ?");
		replyTopSql.append("task","and t1.SOURCE = ?");
		replyTopSql.append("GROUP BY t1.CREATOR");
		replyTopSql.append("ORDER BY COUNT DESC");
		Page<Record> replyTopList = Db.paginate(1, 12, "SELECT t2.username,t2.user_id,t2.pic_url,count(1) count",replyTopSql.getSQL(),replyTopSql.getParams());
		setAttr("replyTopList",replyTopList.getList());
	    
	}
	
}
