package com.yunqu.work.utils;

import java.util.Calendar;

import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class WeekUtils {
	
	public static JSONArray  getYearRecentWeek(int year){
		int weekNo=getWeekNo();
		JSONArray array=new JSONArray();
		int begin = 1;
		if(weekNo>10){
			begin=weekNo-(weekNo-10);
		}
		Calendar calendar = Calendar.getInstance();
		int week= calendar.get(Calendar.DAY_OF_WEEK);
		for(int i=begin;i<=weekNo;i++){
			JSONObject jsonObject=new JSONObject();
			if(week==6||week==1||week==7){
				jsonObject.put("currentWeekNo",weekNo);
			}else{
				jsonObject.put("currentWeekNo",weekNo-1>0?(weekNo-1):1);
			}
			jsonObject.put("YEAR", year);
			jsonObject.put("WEEK_NO", i);
			jsonObject.put("YEAR_WEEK_NO", year+"_"+i);
			jsonObject.put("TITLE", getWeekTitle(year,i)+" 周报");
			array.add(jsonObject);
		}
		return array;
		
	}
	public static JSONArray  getRecentWeek(int year,int weekyCount,String title){
		int currentWeekNo=getWeekNo();
		int weekNo=getWeekNo();
		JSONArray array=new JSONArray();
		Calendar calendar = Calendar.getInstance();
		int week= calendar.get(Calendar.DAY_OF_WEEK);
		int thisVal= weekNo;
		if(week==6||week==1||week==7){
		}else{
			thisVal= weekNo-1>0?(weekNo-1):1;
		}
		int x=1;
		for(int i=weekNo;i>-weekyCount;i--){
			JSONObject jsonObject=new JSONObject();
			if(weekNo>0){
				jsonObject.put("thisWeekNo",thisVal);
				jsonObject.put("currentWeekNo",currentWeekNo);
				jsonObject.put("startDay",EasyDate.formatStringDate(getStartDayOfWeekNo(year,weekNo),"yyyy-MM-dd"));
				jsonObject.put("endDay", EasyDate.formatStringDate(getEndDayOfWeekNo(year,weekNo),"yyyy-MM-dd"));
				jsonObject.put("YEAR", year);
				jsonObject.put("WEEK_NO", weekNo);
				jsonObject.put("YEAR_WEEK_NO", year+"_"+weekNo);
				jsonObject.put("TITLE", getWeekTitle(year,weekNo)+title);
				array.add(jsonObject);
				weekNo--;
				x++;
				if(x==weekyCount)break;
			}else{
				year=year-1;
				if(weekNo==0){
					weekNo=52;
				}else{
					weekNo=52-(-i);
				}
			}
		}
		return array;
	}
	public static JSONArray  getRecentWeek(int year){
		return getRecentWeek(year,15," 周报");
	}
	public static JSONArray  getRecentWeek(){
		return getRecentWeek(getYear());
	}
	
	//获得当前日期属于今年的第几周
	public static int getWeekNo(){
		 Calendar calendar = Calendar.getInstance();
         calendar.setFirstDayOfWeek(Calendar.MONDAY);
         calendar.setTimeInMillis(System.currentTimeMillis());
         int weekOfYear = calendar.get(Calendar.WEEK_OF_YEAR);
         int month = calendar.get(Calendar.MONTH)+1;
         if(month==12&&weekOfYear==1){
        	 return 53;
         }
         return weekOfYear;
	}
	//获得当前日期年
	public static int getYear(){
		Calendar calendar = Calendar.getInstance();
		int year = calendar.get(Calendar.YEAR);
		return year;
	}
	public static String getWeekTitle(int year,int weekNo){
		String a=getStartDayOfWeekNo(year,weekNo);
		String b=getEndDayOfWeekNo(year,weekNo);
		return year +" - "+weekNo+"周 "+"("+a+" ~ "+b+")";
	}
	public static String getWeekTitle(){
		int weekNo=getWeekNo();
		int year=getYear();
		return getWeekTitle(year,weekNo);
	}
 
	
    /**
     * get first date of given month and year
     * @param year
     * @param month
     * @return
     */
    public static String getFirstDayOfMonth(int year,int month){
        String monthStr = month < 10 ? "0" + month : String.valueOf(month);
        return year + "-"+monthStr+"-" +"01";
    }
    /**
     * get the last date of given month and year
     * @param year
     * @param month
     * @return
     */
    public static String getLastDayOfMonth(int year,int month){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR , year);
        calendar.set(Calendar.MONTH , month - 1);
        calendar.set(Calendar.DATE , 1);
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DAY_OF_YEAR , -1);
        return calendar.get(Calendar.YEAR) + "-" + (calendar.get(Calendar.MONTH) + 1) + "-" +
                calendar.get(Calendar.DAY_OF_MONTH);
    }
    /**
     * get Calendar of given year
     * @param year
     * @return
     */
    private static Calendar getCalendarFormYear(int year){
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.YEAR, year);
        return cal;
    }
    /**
     * get start date of given week no of a year
     * @param year
     * @param weekNo
     * @return
     */
    public static String getStartDayOfWeekNo(int year,int weekNo){
        Calendar cal = getCalendarFormYear(year);
        cal.set(Calendar.WEEK_OF_YEAR, weekNo);
        int month = cal.get(Calendar.MONTH) + 1;
        String monthStr  = String.valueOf(month);
        if(month<10) {
        	monthStr = "0"+month;
        }
        int day = cal.get(Calendar.DAY_OF_MONTH);
        String dayStr = String.valueOf(day);
        if(day<10) {
        	dayStr = "0"+day;
        }
        return cal.get(Calendar.YEAR) + "-" + (monthStr) + "-" +dayStr;
    }
    /**
     * get the end day of given week no of a year.
     * @param year
     * @param weekNo
     * @return
     */
    public static String getEndDayOfWeekNo(int year,int weekNo){
        Calendar cal = getCalendarFormYear(year);
        cal.set(Calendar.WEEK_OF_YEAR, weekNo);
        cal.add(Calendar.DAY_OF_WEEK, 6);
        
        int month = cal.get(Calendar.MONTH) + 1;
        String monthStr  = String.valueOf(month);
        if(month<10) {
        	monthStr = "0"+month;
        }
        int day = cal.get(Calendar.DAY_OF_MONTH);
        String dayStr = String.valueOf(day);
        if(day<10) {
        	dayStr = "0"+day;
        }
        return cal.get(Calendar.YEAR) + "-" + (monthStr) + "-" + dayStr;
    }

}
