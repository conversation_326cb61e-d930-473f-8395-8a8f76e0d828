<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="orderId" type="hidden" value="${param.orderId}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>采购跟进记录</h5>
						 <div class="input-group input-group-sm" style="width: 180px;">
					 		<span class="input-group-addon">订单号</span>
						 	<input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="followTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				initList();
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.followPage',
					id:'followTable',
					page:true,
					height:'full-130',
					cellMinWidth:100,
					cols: [[
		             {
	            	 	type: 'radio',
						align:'left'
					 },{
					    field:'ORDER_NO',
						title: '订单号',
						align:'left',
						event:'orderDetail',
						style:'text-decoration:underline;'
					},{
					    field: 'CONTRACT_NAME',
						title: '所属合同',
						minWidth:220,
						align:'left'
					},{
					    field: 'USER_NAME',
						title: '跟进人',
						align:'left'
					},{
					    field: 'FOLLOW_STAT_LABEL',
						title: '跟进阶段',
						align:'left'
					},{
					    field: 'FOLLOW_CONTENT',
						title: '跟进内容',
						align:'left'
					},{
					    field: 'FOLLOW_DATE',
						title: '跟进日期',
						align:'center'
					},{
					    field: 'ADD_TIME',
						title: '录入时间',
						align:'center'
					},{
					    field: '',
						title: '操作',
						align:'center',
						templet:'<div><a href="javascript:;" onclick="editFollow(\'{{d.FOLLOW_ID}}\')">编辑</a></div>'
					}
				]],done:function(){
					
			  	}
			});
			}
			
			function orderDetail(data){
				popup.openTab({id:'orderDetail',title:'采购详情',url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{orderId:data.ORDER_ID,custId:data.CUST_ID}});
			}
			
			function editFollow(followId){
				popup.layerShow({id:'addFollow',area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'修改跟进',data:{followId:followId}});
			}
			function queryData(){
				$("#dataMgrForm").queryData({id:'followTable'});
			}
			
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>

