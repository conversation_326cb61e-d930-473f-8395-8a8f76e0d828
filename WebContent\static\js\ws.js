var wsHeartbeat =null;
var ws = {
	ws:null,
	url:null,
	retryLock:false,
	options:{
		autoReconnect:true,
		onmessage:null,
		onopen:null,
		onerror:null,
		onclose:null,
	},
	init:function(url,options){
		var _self = this;
		if(WebSocket){
			if(url){_self.url = url}
			_self.options = $.extend({},_self.options,options);
			_self.ws = new WebSocket(_self.url);
			_self.ws.onmessage = _self.onmessage;
			_self.ws.onopen = _self.onopen;
			_self.ws.onclose = _self.onclose;

		}else{
			alert('系统不支持websocket')
		}
	},
	onmessage:function(data){
		var data = JSON.parse(data.data);
		ws.options.onmessage && ws.options.onmessage(data);
	},
	onopen:function(event){
		ws.retryLock = false;
		ws.options.onopen && ws.options.onopen(event);
		clearInterval(wsHeartbeat);
		wsHeartbeat = setInterval(function(){
			ws.heartbeat();
		},30*1000);
	},
	onclose:function(event){
		clearInterval(wsHeartbeat);
		console.log(event);
		ws.options.onclose && ws.options.onclose(event);
		if(event.code!=1000) ws.retry();
	},
	onerror:function(event){
		ws.options.onerror && ws.options.onerror(event);
		ws.retry();
	},
	heartbeat:function(){
		var data = 'heartbeat';
		ws.send(data);
	},
	close:function(){
		try{
			this.ws.close();
			console.log('主动关闭ws');
		}catch(e){
			console.log('ws关闭失败,',e);
		}
	},
	send:function(data){
		data = typeof(data) == 'string'?data:JSON.stringify(data)
		if (ws.ws.readyState === WebSocket.OPEN) {
			ws.ws.send(data);
        }else{ console.log('链接已关闭')
		}
	},
	retry:function(){
		if ((this.ws.readyState != WebSocket.OPEN) && this.options.autoReconnect && !this.retryLock) {
			this.retryLock = true;
			console.log('发起重连')
			setTimeout(function(){
				ws.init();
			},2000);
			
		}
	}
}