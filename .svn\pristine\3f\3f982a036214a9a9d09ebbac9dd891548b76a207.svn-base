<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
	<div class="layui-tab layui-tab-brief task-tab" lay-filter="task" data-mars="TaskDao.taskCountStat">
   	    <EasyTag:hasRole roleId="DEV_MGR">
   	   		 <a href="javascript:;" onclick="taskMgr()" class="btn btn-default btn-link" style="position: absolute;right: 38px;margin-top: 5px;z-index: 999;">任务管理</a>
   	 	 </EasyTag:hasRole>
   	 	
   	 	<script type="text/html" id="taskBar">
				<a href="javascript:;" onclick="exportTask()" class="btn btn-default btn-link">导出任务</a>
		</script>
   	 	
   	 	<div class="input-group input-group-sm" style="width: 140px">
   	 		  <span class="input-group-addon">类型</span>
     	  <select onchange="reloadTaskList()" data-mars-reload="false" name="taskTypeId" data-mars="TaskDao.taskType" class="form-control">
				<option value="">请选择</option>
		  </select>
   	   </div>
   	 	<div class="input-group input-group-sm" style="width: 140px;">
   	 		<span class="input-group-addon">负责人</span>
     	<input type="hidden" name="assignUserId" class="form-control input-sm">
			<input type="text" id="assignUserId" class="form-control input-sm">
   	   </div>
   	 	<div class="input-group input-group-sm" style="width: 160px;">
   	 		<span class="input-group-addon">部门</span>
			<input type="hidden" name="assignDeptId" value="${param.assignDeptId}">
			<input type="text" value="${param.assignDeptName}" onclick="singleDept(this)" class="form-control input-sm">
   	   </div>
   	 	<div class="input-group input-group-sm" style="width: 160px;">
   	 		<span class="input-group-addon">主题</span>
			<input type="text" name="taskName" class="form-control input-sm">
   	   </div>
   	   <div class="input-group input-group-sm">
	 		<button type="button" class="btn btn-sm btn-default" data-event='enter' onclick="reloadTask()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
      </div>
   	   
	  <ul class="layui-tab-title">
	    <li data-state="" class="layui-this">全部 (<span class="ALL">0</span>)</li>
	    <li data-state="10">待办中 (<span id="A">0</span>)</li>
	    <li data-state="20">进行中( <span id="B">0</span>)</li>
	    <li data-state="25">超时未完成( <span id="E">0</span>)</li>
	    <li data-state="30">已完成 (<span id="C">0</span>)</li>
	    <li data-state="40">已验收 (<span id="D">10</span>)</li>
	  </ul>
	  <div class="layui-tab-content" style="padding: 0px;">
	  	<div class="layui-tab-item layui-show"></div>
	  	<div class="layui-tab-item"></div>
	  	<div class="layui-tab-item"></div>
	  	<div class="layui-tab-item"></div>
	  	<div class="layui-tab-item"></div>
	  	<div class="layui-tab-item"></div>
	  </div>
</div> 
<table class="layui-hide" id="task"></table>


<script type="text/javascript">

	var isLoadTask=false;
	function loadTaskTable(){
		layui.config({
			  base: '${ctxPath}/static/module/tableMerge/'
		}).use('tableMerge'); //加载自定义模块
	
		
		layui.use(['tableMerge'],function(){
			var tableMerge=layui.tableMerge;
			$("#projectDetail").initTable({
				mars:'TaskDao.projectTaskList',
				id:"task",
				limit:15,
				height:'full-180',
				toolbar:'#taskBar',
				limits:[10,15,50,100,200,300,500,1000,2000],
				data:{},
				autoSort:false,
				cols: [[
	             {
					title: '序号',
					type:'numbers'
				 },{
				    field: 'TASK_TYPE_ID',
					title: '类型',
					sort:true,
					align:'center',
					width:70,
					templet:function(row){
						return getText(row.TASK_TYPE_ID,'taskTypeId');
					}
				  },{
					  field:'MODULE_NAME',
					  merge: true,
					  title:'模块',
					  minWidth:100
				  },{
				    field: 'TASK_NAME',
					title: '任务名称',
					align:'left',
					minWidth:220,
					event:'taskDetail',
					style:'color:#1E9FFF;cursor:pointer',	
					templet:function(row){
						var fileCount = row['FILE_COUNT'];
						var id = row['P_TASK_ID'];
						var fileDes = fileCount>0?getFileIcon('.file'):''
						if(id){
							return fileDes+"<i class='layui-icon layui-icon-senior'></i>"+row['TASK_NAME'];
						}else{
							return fileDes+row['TASK_NAME'];
						}
					}
				},{
				    field: 'TASK_STATE',
					title: '状态',
					align:'center',
					sort:true,
					width:70,
					templet:function(row){
						return taskStateLabel(row.TASK_STATE);
					}
				},{
					field:'PROGRESS',
					title:'进度',
					width:50,
					templet:'<div>{{d.PROGRESS}}%</div>'
				},{
				    field: 'CREATOR',
					title: '发起人',
					width:70,
					sort:true,
					align:'center',
					templet:function(row){
						return getUserName(row.CREATOR);
					}
				},{
				    field: 'ASSIGN_USER_ID',
					title: '指派给',
					width:70,
					sort:true,
					align:'center',
					templet:function(row){
						return getUserName(row.ASSIGN_USER_ID);
					}
				},{
				    field: 'CREATE_TIME',
					title: '发起时间',
					sort:true,
					align:'center',
					width:130,
					templet:function(row){
						var time= row['CREATE_TIME'];
						return cutText(time,19,'');
					}
				},{
				    field: 'DEADLINE_AT',
					title: '任务截止时间',
					align:'left',
					width:100,
					sort:true,
					templet:function(row){
						var time= row['DEADLINE_AT'];
						var state= row['TASK_STATE'];
						if(state<30){
							return timeBetween(time);
						}
						return cutText(time,12,'');
					}
				},{
				    field: '',
					title: '操作',
					width:60,
					align:'left',
					templet:function(row){
						var id = getCurrentUserId();
						var creator = row['CREATOR'];
						var po = record['PO'];
						var state = row['TASK_STATE'];
						if((creator==id&&state<30)||po==id){
							return '<a class="btn btn-xs btn-link" href="javascript:;" onclick="editTask(\''+row['TASK_ID']+'\')">编辑</a>';
						}
						return '';
					}
				}
				]],done:function(result){
					tableMerge.render(layTables['task'].config);
					table.resize('task');
					if(isLoadTask==false){
						$(".ALL,.n2").text(result.totalRow);
					}
					table.on('sort(task)', function(obj){
						  $("#projectDetail").queryData({id:'task',jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
					});
					isLoadTask=true;
				}});
		});
		
	}
	function reloadTask(){
		 $("#projectDetail").queryData({id:'task'});
	}
	function editTask(taskId){
		popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'编辑任务',data:{taskId:taskId}});
	}
	function  taskDetail(data){
		var status=0;
		if(data['CREATOR']==getCurrentUserId()){
			status=2;
		}
		if(data['ASSIGN_USER_ID']==getCurrentUserId()){
			status=1;
		}
		popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
	}
	
	function  projectTaskDetail(taskId,taskState,creator,assignUserId){
		var status=0;
		if(creator==getCurrentUserId()){
			status=2;
		}
		if(assignUserId==getCurrentUserId()){
			status=1;
		}
		popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:taskId,taskState:taskState,status:status}});
	}
	
	function getDays(strDateStart, strDateEnd) {
		if(strDateStart&&strDateEnd){
			strDateStart = strDateStart.substring(0,10);
			strDateEnd = strDateEnd.substring(0,10);
			var strSeparator = "-";
			var oDate1;
			var oDate2;
			var iDays;
			oDate1 = strDateStart.split(strSeparator);
			oDate2 = strDateEnd.split(strSeparator);
			var strDateS = new Date(oDate1[0], oDate1[1] - 1, oDate1[2]);
			var strDateE = new Date(oDate2[0], oDate2[1] - 1, oDate2[2]);
			iDays = parseInt(Math.abs(strDateS - strDateE) / 1000 / 60 / 60 / 24);//把相差的毫秒数转换为天数
			return iDays;
		}else{
			return '';
		}
	}
	
	function updateTaskGroup(groupId){
		var title = groupId==''?'新增':'修改';
		popup.layerShow({id:'updateTaskGroup',area:['450px','350px'],url:'/yq-work/pages/project/include/plan-edit.jsp',title:title,data:{groupId:groupId}});
	}

	function setTaskPlanCount(count){
		$(".n7").text(count);
		return '';
	}
</script>

