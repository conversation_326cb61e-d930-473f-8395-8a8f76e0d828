package com.yunqu.work.servlet;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.framework.vo.Department;
import org.easitline.common.core.framework.vo.User;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.crypt.DESUtil;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.service.OAService;
import com.yunqu.work.utils.WebSocketUtils;

@WebServlet("/servlet/workbench/*")
public class WorkbenchServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	public void actionForIndex(){
		String account=getUserPrincipal().getLoginAcct();
		getUserPrincipal().getDepartment();
		User user=getUserPrincipal().getUserInfo();
		Department department=user.getDepartment();
		setAttr("user", user);
		setAttr("userData", getUserPrincipal());
		String pwd="";
		Object obj=getUserPrincipal().getAttribute("pwd");
		if(obj!=null){
			pwd=obj.toString();
		}
		try {
			if(StringUtils.isBlank(pwd)){
			    pwd=this.getQuery().queryForString("select USER_PWD from "+Constants.DS_MAIN_NAME+".EASI_USER_LOGIN WHERE USER_ID = ?", getUserId());
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		try {
			String open_id = this.getQuery().queryForString("select OPEN_ID from "+Constants.DS_MAIN_NAME+".EASI_USER WHERE USER_ID = ?",getUserId());
			setAttr("openId",open_id);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		try {
			String deptId = this.getQuery().queryForString("select DEPT_ID from "+Constants.DS_MAIN_NAME+".EASI_DEPT_USER WHERE USER_ID = ?",getUserId());
			setAttr("deptId",deptId);
			getUserPrincipal().setAttribute("deptId", deptId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		try {
			List<JSONObject> list = this.getQuery().queryForList("select * from YQ_OA_USER WHERE OA_ACCOUNT = ?",new Object[]{account},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				JSONObject jsonObject=list.get(0);
				String oaPwd=jsonObject.getString("OA_PWD");
				oaPwd =	DESUtil.getInstance().decryptStr(oaPwd);
				boolean flag=OAService.getService().login(jsonObject.getString("OA_ACCOUNT"),oaPwd);
				if(!flag){
					setAttr("OA", "fail");
				}else{
					setAttr("OA", "ok");
					getUserPrincipal().setAttribute("cookie",OAService.getService().getUserCookie(account));
				}
			}else{
				setAttr("OA", "none");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		try {
			JSONObject userInfo = this.getQuery().queryForRow("select * from yq_user_info where user_id = ?",new Object[]{getUserId()},new JSONMapperImpl());
			if(userInfo!=null&&userInfo.size()>0){
				//todo
			}else{
				this.getQuery().executeUpdate("INSERT INTO yq_user_info(user_id,email,mobile,nike_name,dept_name,open_id,head_img,user_name,post)select user_id,EMAIL,MOBILE,nike_name,depts,open_id,PIC_URL,username,data2 from "+Constants.DS_MAIN_NAME+".easi_user where USER_ID = ?",getUserId());
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		setAttr("userId",user.getUserId());
		setAttr("pwd",pwd);
		getUserPrincipal().removeAttribute("pwd");
		setAttr("department",department);
		//setAttr("roles", getUserPrincipal().getRoles());
		setAttr("devLead",getUserPrincipal().isRole("mgr")?1:0);
		renderJsp("/pages/index.jsp");
	}
	public EasyResult actionForPushMsg(){
		String sql="select count(1) from yq_task where assign_user_id = ? and task_state<30";
		try {
			int count = this.getQuery().queryForInt(sql, getUserId());
			if(count>0){
				WebSocketUtils.sendMessage(new MsgModel(getUserId(), "任务待办结提醒","您目前还有"+count+"任务未完成,请及时处理哦."));
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForRefreshOaCookie(){
		String account=getUserPrincipal().getLoginAcct();
		List<JSONObject> list;
		try {
			list = this.getQuery().queryForList("select * from YQ_OA_USER WHERE OA_ACCOUNT = ?",new Object[]{account},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				JSONObject jsonObject=list.get(0);
				String oaPwd=jsonObject.getString("OA_PWD");
				oaPwd =	DESUtil.getInstance().decryptStr(oaPwd);
				boolean flag=OAService.getService().login(jsonObject.getString("OA_ACCOUNT"),oaPwd);
				if(flag){
					getUserPrincipal().setAttribute("cookie",OAService.getService().getUserCookie(account));
				}
			}
			return EasyResult.ok();
		} catch (SQLException e) {
			return EasyResult.fail();
		}
	}
	public void actionForWxAuth(){
		
	}

}
