<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>project</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="InvoiceDetailForm" class="form-horizontal" data-mars="InvoiceDao.record" autocomplete="off" data-mars-prefix="invoice.">
        <input type="hidden" value="${param.invoiceId}" name="invoiceId"/>
        <input type="hidden" value="${param.invoiceId}" name="invoice.INVOICE_ID"/>
        <fieldset class="content-title">
            <legend><span class="layui-badge-rim">1</span> 基本信息</legend>
        </fieldset>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td width="100px" >发票申请编号</td>
                <td style="width: 40%">
                    <input data-rules="required"  type="text" name="invoice.INVOICE_NO" class="form-control">
                </td>
                <td width="100px" >客户名称</td>
                <td style="width: 40%">
                    <input name="invoice.CUST_ID" type="hidden"/>
                    <span type="text" name="invoice.CUST_NAME" onclick="InvoiceDetail.custDetail()" style="color:#1E9FFF;cursor:pointer"></span>
                </td>
            </tr>
            <tr>
                <td>合同编号</td>
                <td>
                    <input name="invoice.CONTRACT_NO" type="hidden"/>
                    <input name="invoice.CONTRACT_ID" type="hidden"/>
                    <span name="invoice.CONTRACT_NAME" onclick="InvoiceDetail.contractDetail()" style="color:#1E9FFF;cursor:pointer"></span>
                </td>
                <td>对应合同阶段</td>
                <td>
                    <SELECT name="invoice.STAGE_ID" class="form-control">
                        <option value=""></option>
                    </SELECT>
                </td>
            </tr>
            <tr>
                <td>业务平台</td>
                <td>
                    <input type="text" name="invoice.PLATFORM_NAME" class="form-control" >
                </td>
                <td>开票流程</td>
                <td>
                    <input type="text" name="invoice.APPLY_TITLE" class="form-control" >
                </td>
            </tr>
            <tr>
                <td>开票日期</td>
                <td>
                    <input type="text" data-rules="required" name="invoice.MARK_DATE" class="form-control Wdate">
                </td>
                <td>开票类型</td>
                <td>
                    <select data-mars="YqAdmin.DictDao.select('Y009')" name="invoice.INVOICE_TYPE"  class="form-control text-val">
                        <option value=""></option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>开票金额（元）</td>
                <td colspan="3">
                    <input type="number" data-rules="required" name="invoice.MARK_MONEY" class="form-control" value="0" >
                </td>
            </tr>
            <tr>
                <td>收入类型</td>
                <td>
                    <SELECT name="invoice.PAYMENT_TYPE" data-mars="ContractPaymentDao.getPaymentType()" class="form-control">
                        <option value=""></option>
                    </SELECT>
                </td>
                <td >税率</td>
                <td>
                    <input data-rules="required" type="text" name="invoice.TAX_RATE" class="form-control" placeholder="选择类型后自动填写">
                </td>
            </tr>
            <tr>
                <td>税额</td>
                <td>
                    <input type="text" name="invoice.TOTAL_TAX" class="form-control" placeholder="选择收入类型并输入开票金额后自动计算">
                </td>
                <td>不含税总额</td>
                <td>
                    <input type="text" name="invoice.AMOUNT_IN_FIGUERS" class="form-control" placeholder="选择收入类型并输入开票金额后自动计算">
                </td>
            </tr>
            <tr>
                <td>创建人</td>
                <td>
                    <input type="text" name="invoice.CREATE_NAME" class="form-control">
                </td>
                <td>更新时间</td>
                <td>
                    <input type="text" name="invoice.UPDATE_TIME" class="form-control">
                </td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <textarea style="height: 80px;" class="form-control" name="invoice.REMARK"></textarea>
                </td>
            </tr>
            </tbody>
        </table>
        <fieldset class="content-title">
            <legend><span class="layui-badge-rim">2</span>抬头信息</legend>
        </fieldset>
        <table class="table table-vzebra">
            <tbody>
            <tr style="display: none">
                <td>
                    <textarea id="textJson" class="form-control" name="invoice.TEXT_JSON"></textarea>
                </td>
            </tr>
            <tr>
                <td width="100px" >开票抬头</td>
                <td style="width: 40%">
                    <input name="title.TITLE_ID" type="hidden">
                    <input type="text" name="title.TITLE_NAME" class="form-control" placeholder="点击选择抬头">
                </td>
                <td width="100px" >纳税人识别号</td>
                <td style="width: 40%">
                    <input type="text" name="title.TAX_NO" class="form-control"/>
                </td>
            </tr>
            <tr>
                <td >开户行</td>
                <td>
                    <input type="text" name="title.BANK_NAME" class="form-control">
                </td>
                <td>开户账号</td>
                <td>
                    <input type="text" readonly="readonly" name="title.BANK_NO" class="form-control">
                </td>
            </tr>
            <tr>
                <td>开票地址</td>
                <td>
                    <input type="text" name="title.ADDRESS" class="form-control">
                </td>
                <td>电话</td>
                <td>
                    <input type="text" name="title.TEL" class="form-control">
                </td>
            </tr>
            </tbody>
        </table>
        <fieldset class="content-title">
            <legend><span class="layui-badge-rim">3</span> 邮寄信息</legend>
        </fieldset>
        <table class="table table-vzebra">
            <tbody>
            <tr style="display: none">
                <td>
                    <textarea id="textJson2" class="form-control" name="invoice.MAIL_JSON"></textarea>
                </td>
            </tr>
            <tr>
                <td width="100px" >联系人</td>
                <td style="width: 40%">
                    <input  type="text" name="mail.CONTACTS" class="form-control">
                </td>
                <td width="100px" >联系方式</td>
                <td style="width: 40%">
                    <input type="text" name="mail.CONTACTS_TEL" class="form-control"/>
                </td>
            </tr>
            <tr>
                <td>邮寄地址</td>
                <td colspan="3">
                    <input type="text" name="mail.MAIL_ADDRESS" class="form-control">
                </td>
            </tr>
            </tbody>
        </table>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">

        jQuery.namespace("InvoiceDetail");

        InvoiceDetail.invoiceId = '${param.invoiceId}';
        InvoiceDetail.contractId = '${param.contractId}';
        InvoiceDetail.stageId = '';
        var invoiceId='${param.invoiceId}';

        $(function(){
            $("#InvoiceDetailForm").render({success:function(result){
                    var dataJsonStr = $('#textJson').val();
                    if(dataJsonStr){
                        var textJson = eval("("+dataJsonStr+")");
                        fillRecord(textJson,"title.",",","#InvoiceDetailForm");
                    }
                    var dataJsonStr2 = $('#textJson2').val();
                    if(dataJsonStr2){
                        var textJson = eval("("+dataJsonStr2+")");
                        fillRecord(textJson,"mail.",",","#InvoiceDetailForm");
                    }

                    if(!$.isEmptyObject(result['InvoiceDao.record'])) {
                        if(!$.isEmptyObject(result['InvoiceDao.record']['data'])){
                            InvoiceDetail.stageId = result['InvoiceDao.record']['data']['STAGE_ID'];
                        }
                    }
                    if($('[name="invoice.CONTRACT_ID"]').val().trim()!="" || $('[name="invoice.CONTRACT_ID"]').val().length != 0){
                        InvoiceDetail.selectStageId($('[name="invoice.CONTRACT_ID"]').val());
                    }else {
                        $('#InvoiceDetailForm :input').prop('readonly', true).css('border', 'none').css('background','white');
                        $('#InvoiceDetailForm select').prop('disabled', true).css('border', 'none').css('appearance', 'none').css('color','black').css('background','white');;
                    }

                }});
        });



        InvoiceDetail.selectStageId = function(contractId){
            var stageId=InvoiceDetail.stageId;
            $('select[name="invoice.STAGE_ID"]').attr('data-mars','ContractStageDao.selectStageSimple');
            $('select[name="invoice.STAGE_ID"]').render({data:{contractId:contractId},success:function(result){
                    $('select[name="invoice.STAGE_ID"] option:first').text("请选择");
                    if(stageId != null && stageId != ""){
                        $('select[name="invoice.STAGE_ID"]').val(stageId);
                    }
                    $('#InvoiceDetailForm :input').prop('readonly', true).css('border', 'none').css('background','white');
                    $('#InvoiceDetailForm select').prop('disabled', true).css('border', 'none').css('appearance', 'none').css('color','black').css('background','white');;
                }});
        }


        function getTitleVal(formData){
            var data = $.extend({},formData);
            var json = {};
            for(var key in data){
                if(key.indexOf("title.")>-1){
                    var newKey = key.replace('title.','');
                    json[newKey] = data[key];
                }
            }
            return JSON.stringify(json);
        }

        function getMailVal(formData){
            var data = $.extend({},formData);
            var json = {};
            for(var key in data){
                if(key.indexOf("mail.")>-1){
                    var newKey = key.replace('mail.','');
                    json[newKey] = data[key];
                }
            }
            return JSON.stringify(json);
        }

        InvoiceDetail.contractDetail = function(){
            var contractId = $('[name="invoice.CONTRACT_ID"]').val();
            var custId = $('[name="invoice.CUST_ID"]').val();
            popup.layerClose('contractDetail');
            popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/project/contract',data:{contractId:contractId,custId:custId}});
        }

        InvoiceDetail.custDetail = function(){
            var custId = $('[name="invoice.CUST_ID"]').val();
            popup.layerClose('custDetail');
            popup.openTab({id:'custDetail',title:'客户详情',url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId}});
        }
     
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>