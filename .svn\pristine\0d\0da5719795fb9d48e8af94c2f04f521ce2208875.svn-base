<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="goodsStorageInTable"></table>
<button class="btn btn-default btn-sm ml-15" onclick="goodsStockList('in')" type="button">入库单</button>
<button class="btn btn-default btn-info btn-sm ml-15" onclick="outStorage()" type="button">办理出库</button>
	<script type="text/javascript">
		$(function(){
			$("#OrderDetailForm").initTable({
				mars:'GoodsDao.stockList',
				id:'goodsStorageInTable',
				page:false,
				data:{type:'10'},
				rowDoubleEvent:'editStock',
				height:300,
				cellMinWidth:80,
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
            	 	type: 'checkbox',
					align:'left'
				 },{
					 field:'NAME',
					 title:'物料名称'
				 },{
					 field:'GOODS_NO',
					 title:'物料编号'
				 },{
					 field:'NUM',
					 title:'入库数量',
					 width:70
				 },{
					 field:'PRICE',
					 title:'入库成本',
					 width:70,
					 templet:function(row){
						 return row['PRICE']*row['NUM'];
					 }
				 },{
				    field: 'DATE_ID',
					title: '入库日期',
					align:'center'
				},{
					field:'REMARK',
					title:'备注',
					edit:'text'
				},{
				    field: 'CREAT_NAME',
					title: '操作人',
					align:'center'
				},{
				    field: 'CREATE_TIME',
					title: '操作时间',
					align:'center'
				}
			]],done:function(data){
				$("#stockInCount").text(data.total);
		  	}
		});
	});
	
	function storageInReload(){
		$("#OrderDetailForm").queryData({page:false,id:'goodsStorageInTable',data:{type:'10'}});	
	}
	
	function goodsStockList(type){
		
	}
	function editInStock(data){
		popup.layerShow({id:'storageEdit',area:['600px','400px'],title:'修改',url:'${ctxPath}/pages/erp/goods/storage-edit.jsp',data:{title:'入库',type:'put',stockId:data['STOCK_ID']}});
	}
	
</script>
