<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<style type="text/css">
	.avatar.size-L, .avatar.size-L img {
	    width: 48px;
	    height: 48px;
        border-radius: 50%;
	}
	.commentList,.comment-write{margin:5px 15px;}
	.commentList .item.comment-flip {padding-left: 64px;padding-right: 0;}
	.commentList .item {list-style: none outside none;margin: 1.6rem 0 0;padding-right: 64px;}
	.commentList .item{list-style: none outside none;margin: 1.6rem 0 0}
	.commentList .avatar{border: 1px solid transparent;float: left}
	.comment-main{position:relative;margin-left:64px;border:1px solid #dedede;border-radius:2px}
	.comment-main:before,.comment-main:after{position:absolute;top:11px;left:-16px;right:100%;width:0;height:0;display:block;content:" ";border-color:transparent;border-style:solid solid outset;pointer-events:none}
	.comment-main:before{border-right-color:#dedede;border-width:8px}
	.comment-main:after{border-width:7px;border-right-color:#f8f8f8;margin-top:1px;margin-left:2px}
   	.comment-header{padding:10px 15px;background:#f8f8f8;border-bottom:1px solid #eee}
    .comment-title{margin:0 0 8px 0;font-size:1.6rem;line-height:1.2}
    .comment-meta{font-size:13px;color:#999;line-height:1.2}
    .comment-meta a{color:#999}
    .comment-author{font-weight:700;color:#999}
    .comment-body{padding:15px;overflow:hidden}
    .comment-body>:last-child{margin-bottom:0}
	.commentList .comment-flip .avatar {float: right}
	.comment-flip .comment-main{margin-left: 0; margin-right: 64px}
	.comment-flip .comment-main:before {border-left-color: #dedede;border-right-color: transparent}
	.comment-flip .comment-main:before, .comment-flip .comment-main:after {left: 100%;position: absolute;right: -16px}
	.comment-flip .comment-main:after {border-left-color: #f8f8f8;border-right-color: transparent;margin-left: auto;margin-right: 2px}
	
	.comment-write{margin-top: 20px;height: 80px;line-height: 80px;}
</style>
<form id="updateLogForm">
		<div class="comment-write">
			<div class="pull-left" style="width: 66px;margin-top: -15px;">
				<i class="avatar size-L radius"><img src="/yq-work/static/images/user-avatar-large.png"></i>
			</div>
			<textarea class="form-control" id="reviseContent" style="display: inline-block;width:calc(100% - 130px);height: 70px;"></textarea>
			<button class="btn btn-danger pb-10" onclick="submitRevise()" type="button" style="width: 56px;height: 64px;line-height: 16px;margin-bottom: 65px">发表<br>评论</button>
		</div>
		<ul class="commentList" data-template="revise-template" data-mars="FlowCommon.reviseList"></ul><br><br>
        <script id="revise-template" type="text/x-jsrender">
			{{for data}}
		      <li class="item cl"> <a href="javascript:;"><i class="avatar size-L radius"><img  onclick="userInfoLayer('{{:ADD_USER_ID}}')" src="/yq-work/static/images/user-avatar-large.png"></i></a>
              <div class="comment-main">
                <header class="comment-header">
                  <div class="comment-meta"><a class="comment-author" onclick="userInfoLayer('{{:ADD_USER_ID}}')" href="javascript:;">{{:ADD_USER_NAME}}</a> 评论于
                    <time>{{:ADD_TIME}}</time>
                  </div>
                </header>
                <div class="comment-body">
                  <p>{{call:REVISE_CONTENT fn='getContent'}}</p>
                </div>
              </div>
             </li>
			{{/for}}					         
	   </script>  
       <script type="text/javascript">
       
       		$(function(){
       			$('#updateLogForm').render({data:FlowCore.params});
       		});
       		
       		function submitRevise(){
       			var val = $('#reviseContent').val();
       			if(val==''){
       			    layer.msg('不能为空',{icon:2});
       				return;
       			}
       			var data = $.extend({reviseContent:val},FlowCore.params);
    			ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addReviseLog",data,function(result) { 
    				if(result.state == 1){
    					layer.msg(result.msg,{icon:1,time:1200},function(){
    						$('#reviseContent').val('');
    						$('#updateLogForm').render({data:FlowCore.params});
    					});
    				}else{
    					layer.alert(result.msg,{icon: 5});
    				}
    			});
       		}
       </script>
</form>