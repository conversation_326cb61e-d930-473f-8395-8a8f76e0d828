package com.yunqu.work.servlet.ehr;

import java.io.IOException;
import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.framework.StructureImpl;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.DESUtil;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.OAService;
import com.yunqu.work.service.StaffService;

@WebServlet("/servlet/ehr/*")
public class EhrServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	public EasyResult actionForGetFrameworkTree(){
		JSONObject jsonObject=new JSONObject();
		try {
			jsonObject=JSONObject.parseObject(StructureImpl.getStructTree(new int[]{1,2,3}));
		} catch (Exception e) {
			return EasyResult.fail();
		}
		return EasyResult.ok(jsonObject);
	}
	
	
	public EasyResult actionForAddOA(){
		JSONObject jsonObject = getJSONObject();
		String type=jsonObject.getString("type");
		String account=jsonObject.getString("account");
		String pwd=jsonObject.getString("pwd");
		boolean loginResult = OAService.getService().login(account,pwd);
		if(loginResult){
			EasyRecord record = new EasyRecord("YQ_OA_USER","OA_ACCOUNT");
			record.set("OA_ACCOUNT",account);
			record.set("USER_ID",getUserId());
			record.set("UPDATE_TIME",EasyDate.getCurrentDateString());
			record.set("OA_PWD",DESUtil.getInstance().encryptStr(pwd));
			record.set("STATE","ok");
			if("add".equals(type)){
				try {
					this.getQuery().save(record);
				} catch (SQLException e) {
				
				}
			}else{
				try {
					this.getQuery().update(record);
				} catch (SQLException ex) {
					
				}
			}
			return EasyResult.ok("校验成功");
		}else{
			return EasyResult.fail("密码错误");
		}
		
	}
	public void actionForEmail(){
		try {
			Object object=getUserPrincipal().getAttribute("cookie");
			if(object==null){
				Render.renderHtml(getRequest(), getResponse(), "<h1>您没绑定OA账号或绑定失效.</h1>");
				return;
			}
			OAService.getService().emailLoginJump(this.getRequest(),this.getResponse());
		} catch (IOException e) {
		     renderHtml(e.getMessage());
		}
		return;
	}
	
	public void actionForMyWfDetail(){
		try {
			Object object=getUserPrincipal().getAttribute("cookie");
			if(object==null){
				Render.renderHtml(getRequest(), getResponse(), "<h1>您没绑定OA账号或绑定失效.</h1>");
				return;
			}
			OAService.getService().myWfDetail(this.getRequest(),this.getResponse(),getPara("no"));
		} catch (IOException e) {
			renderHtml(e.getMessage());
		}
		return;
	}


	public void actionForGetUserInfo() {
		String userId = getJsonPara("userId");
		if(StringUtils.isBlank(userId)) {
			renderJson(new StaffModel());
		}else {
			StaffModel model = StaffService.getService().getStaffInfo(userId);
			renderJson(model);
		}
	}
	
	public EasyResult actionForGetUserListById() {
		JSONArray array = new JSONArray();
		String userIds = getJsonPara("userIds");
		if(StringUtils.isBlank(userIds)) {
			return EasyResult.fail();
		}
		String[] strs = userIds.split(",");
		for(String userId:strs) {
			JSONObject row = new EasyResult();
			row.put("userId",userId);
			row.put("userName",StaffService.getService().getUserName(userId));
			array.add(row);
		}
		return EasyResult.ok(array);
	}
	
}
