<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<style>
 #fileList a{background-color: #f8f8f8;border: 1px solid #ccc;padding: 3px 5px;margin: 3px;display: inline-block;}
 #businessState{display: none;}
</style>
<div class="container-fluid">
   <table class="table table-edit table-vzebra"  id="addFollow" data-mars="CustDao.followInfo" data-mars-prefix="follow.">
       <tbody>
       	   <tr>
       			<td>客户名称</td>
       			<td colspan="3">
                 	<input type="hidden" name="follow.YEAR"/>
                 	<input type="hidden" name="follow.WEEK_NO"/>
                 	<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
                 	<input type="hidden" value="${param.followSource}" name="follow.FOLLOW_SOURCE"/>
                 	<input type="hidden" name="fkId" value="${param.followId}"/>
                 	<input type="hidden" id="businessId" value="${param.businessId}"/>
                 	<input type="hidden" name="custId" value="${param.custId}"/>
                 	<input type="hidden" name="followId" value="${param.followId}"/>
                 	<input type="hidden" name="follow.FOLLOW_ID" value="${param.followId}"/>
       				<input type="hidden" name="follow.CUST_ID" value="${param.custId}"/>
					<input data-rules="required" value="${param.custName}" type="text" readonly="readonly" id="_custId" placeholder="请点击选择客户"  name="follow.CUSTOMER_NAME" class="form-control input-sm"/>
       			</td>
       	   </tr>
           <tr>
           	<td class="required">周期</td>
           	<td>
				 <input class="form-control" data-rules="required"  name="follow.WEEK_TITLE" id="selectWeek">
           	</td>
           	<td class="required">工时/天</td>
           	<td>
           		<input name="follow.FOLLOW_DAY" type="number" data-rules="required" class="form-control">
           	</td>
           </tr>
           <tr>
                 <td class="required" style="width: 90px;">开始时间</td>
                 <td style="width: 40%;">
					<input type="text" data-rules="required" name="follow.FOLLOW_BEGIN" autocomplete="off" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="form-control input-sm"/>
                 </td>
                 <td class="required" style="width: 90px;">结束时间</td>
                 <td>
					<input type="text" data-rules="required" name="follow.FOLLOW_END" autocomplete="off" onclick="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="form-control input-sm"/>
                 </td>
           </tr>
       	   <tr>
                 <td class="required" width="100px">跟进方式</td>
                 <td>
              		<select class="form-control input-sm" data-rules="required" name="follow.FOLLOW_TYPE">
              			<option value="">请选择</option>
              			<option value="见面拜访">见面拜访</option>
              			<option value="打电话">打电话</option>
              			<option value="发邮件">发邮件</option>
              			<option value="微信">微信</option>
              			<option value="发短信">发短信</option>
              			<option value="现场会议">现场会议</option>
              			<option value="视频会议">视频会议</option>
              			<option value="其它">其它</option>
              		 </select>
                 </td>
                 <td class="required" width="100px">关注程度</td>
                 <td>
              		<select class="form-control input-sm" data-rules="required" name="follow.FOLLOW_LEVEL">
              			<option value="">请选择</option>
              			<option value="1">最高</option>
              			<option value="2">较高</option>
              			<option value="3">一般</option>
              			<option value="4">较低</option>
              		 </select>
                 </td>
       	   </tr>
           <tr>
           		<td>关联商机</td>
                <td colspan="3">
                	<select name="follow.BUSINESS_ID" data-mars="CustDao.custBusniessDict" class="form-control input-sm">
                    	<option value="">请选择</option>
                    </select>
                 </td>
           </tr>
           <tr id="businessState">
           		<td>商机状态</td>
                <td>
                	<select name="follow.BUSINESS_STATE" class="form-control input-sm">
                    	<option value="">请选择</option>
              		 	<option value="10">沟通交流</option>
              		 	<option value="20">立项阶段</option>
              		 	<option value="25">POC阶段</option>
              		 	<option value="30">投标阶段</option>
              		 	<option value="40">商务阶段</option>
                    </select>
                 </td>
           		<td>商机结果</td>
                <td>
                	<select name="follow.BUSINESS_RESULT" class="form-control input-sm">
              		 	<option value="0">跟进中</option>
                   	 	<option value="99">中止</option>
                   	 	<option value="50">输单</option>
                   	 	<option value="60">赢单</option>
                    </select>
                 </td>
           </tr>
           <tr>
                <td>关联合同</td>
                <td>
                	<select name="follow.CONTRACT_ID" data-mars="ProjectContractDao.dict" class="form-control input-sm">
                    	<option value="">请选择</option>
                    </select>
                 </td>
                <td>关联联系人</td>
                <td>
                  	 <select data-mars="ContactsDao.custContacts" name="follow.CONTACTS_ID" class="form-control input-sm">
                  	 	<option value="">请选择</option>
                  	 </select>
                </td>
           </tr>
            <tr>
            	<td  class="required">附件</td>
            	<td colspan="3">
            		<a onclick="$('#localfile').click();" href="javascript:;">+上传</a>
            		<div id="fileList" style="display: inline-block;" data-template="template-files" data-mars="FileDao.fileList"></div>
            	</td>
            </tr>
            <tr>
            	<td  class="required">跟进内容</td>
            	<td colspan="3">
            		<textarea name="follow.CONTENT" data-rules="required" style="height: 120px"  class="form-control"></textarea>
            	</td>
            </tr>
       </tbody>
	</table>
    <p class="layer-foot text-c">
   	   <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="FollowEit.ajaxSubmitForm()"> 保 存 </button>
       <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
	</p>
</div>

<form  id="fileForm" enctype="multipart/form-data"  method="post">
  	<input style="display: none;" name="file" type="file" id="localfile" onchange="uploadFile()"/>
</form>
<script id="template-files" type="text/x-jsrender">
			{{if data.length==0}}

			{{/if}}
			{{for data}}
				<a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}} &nbsp;</span></a>
			{{/for}}
</script>
<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<script>
	layui.config({
		  base: '${ctxPath}/static/js/'
	}).use('tableSelect');

	$(function(){
		var followSource = '${param.followSource}';
		if(followSource=='1'){
			$("#businessState").show();
			$("[name='follow.BUSINESS_ID']").attr('data-rules','required');
		}
		$("[name='follow.BUSINESS_ID']").on('change',function(){
			var t =$(this);
			if(t.val()){
				$("#businessState").show();
			}else{
				$("#businessState select").val('');
				$("#businessState").hide();
			}
		});
		
		$("#addFollow textarea").keydown(function(event) {
			  if(event.keyCode == "13") {
				 var height = $(this).height();
				 $(this).css('height',(height+20)+'px');
			  }
			   if(event.keyCode == 8) {
				 var height = $(this).height();
				 height=height>120?height:120;
				 $(this).css('height',(height-20)+'px');
			   }
		});
		$("#addFollow").render({success:function(result){
			var businessId = $('#businessId').val();
			if(businessId){
				$("[name='follow.BUSINESS_ID']").val(businessId);
			}
			var record = result['CustDao.followInfo'];
			if(record.data['BUSINESS_STATE']){
				$("#businessState").show();
			}
			initSelectCust();
		}});
	});
	
	var FollowEit ={
		followId:'${param.followId}',
		ajaxSubmitForm:function(){
			if(form.validate("#addFollow")){
				if(FollowEit.followId){
					FollowEit.updateData(); 
				}else{
					FollowEit.insertData(); 
				}
			};
			
		},
		insertData:function(){
			var data = form.getJSONObject("#addFollow");
			var randomId = $("#randomId").val();
			data['follow.FOLLOW_ID'] = randomId;
			
			var businessId = data['follow.BUSINESS_ID'];
			if(businessId!=''){
				var businessState = data['follow.BUSINESS_STATE'];
				if(businessState==''){
					layer.msg('选择商机状态.');
					return;
				}
				var businessResult = data['follow.BUSINESS_RESULT'];
				if(businessResult==''){
					layer.msg('选择商机结果.');
					return;
				}
			}
			ajax.remoteCall("${ctxPath}/servlet/cust?action=addFollow",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('addFollow');
						reloadFollowInfo();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		},
		updateData:function(){
			var data = form.getJSONObject("#addFollow");
			
			var businessId = data['follow.BUSINESS_ID'];
			if(businessId!=''){
				var businessState = data['follow.BUSINESS_STATE'];
				if(businessState==''){
					layer.msg('选择商机状态.');
					return;
				}
			}
			
			ajax.remoteCall("${ctxPath}/servlet/cust?action=updateFollow",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('addFollow');
						reloadFollowInfo();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	
	}
	function initSelectCust(){
		layui.use(['form','tableSelect'], function(){
			var tableSelect = layui.tableSelect;
			tableSelect.render({
				elem: '#_custId',
				searchKey: 'custName',
				checkedKey: 'CUST_ID',
				page:true,
				searchPlaceholder: '请输入客户名称',
				table: {
					mars: 'CustDao.custList',
					cols: [[
					        { type: 'radio' },
					        { field: 'CUST_NO',title: '编号'},
					        { field: 'CUST_NAME', title: '名称', width: 320 }
					        ]]
				},
				done: function (elem, data) {
					var names = [];
					var ids = [];
					layui.each(data.data, function (index, item) {
						names.push(item.CUST_NAME)
						ids.push(item.CUST_ID)
					});
					elem.attr("ts-selected",ids.join(","));
					elem.val(names.join(","));
					elem.prev().val(ids.join(","));
					
					$("[name='follow.CONTRACT_ID']").render({data:{custId:ids[0]}});
					$("[name='follow.CONTACTS_ID']").render({data:{custId:ids[0]}});
					$("[name='follow.BUSINESS_ID']").render({data:{custId:ids[0]},success:function(){
						
					}});
				},
				clear:function(elem){
					elem.prev().val("");
				}
			});
			
			tableSelect.render({
				elem: '#selectWeek',
				searchKey: 'TITLE',
				checkedKey: 'title',
				page:false,
				searchPlaceholder: '请输入周数',
				table: {
					mars: 'WeeklyDao.getProjectRecentWeekPage',
					page:false,
					limit:90,
					cols: [[
					        { type: 'radio' },
					        { field: 'TITLE', title: '名称'}
					 ]]
				},
				done: function (elem, data) {
					var item = data['data'][0];
					
					var names = item['TITLE'];
					var ids = item['WEEK_NO'];
					elem.attr("ts-selected",names);
					elem.val(names);

					$("[name='follow.YEAR']").val(item['WEEK_NO']);
					$("[name='follow.WEEK_NO']").val(item['WEEK_NO']);
					$("[name='follow.FOLLOW_BEGIN']").val(item['startDay']);
					$("[name='follow.FOLLOW_END']").val(item['endDay']);
					
					
				},
				clear:function(elem){
					elem.prev().val("");
				}
			});
		});
	}

	var uploadFile = function(){
		var custId = $("[name='follow.CUST_ID']").val();
		if(custId==''){
			layer.msg('请选择客户');
			return;
		}
		var randomId = $("#randomId").val();
		var fkId = FollowEit.followId||randomId;
		easyUploadFile({callback:'callback',pFkId:custId,fkId:fkId,source:'custFollow',formId:'fileForm',fileId:'localfile'});
		
	}
 	 
	var callback = function(data){
		var randomId = $("#randomId").val();
		var fkId = FollowEit.followId||randomId;
		$("#fileList").render({data:{fkId:fkId}});
	}
</script>
