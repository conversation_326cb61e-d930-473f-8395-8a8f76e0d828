var ctxPath = getCtxPath();

/***common***/

function getCustLevel(val){
	var json={1:'A(重点客户)',2:'B(普通客户) ',3:'C(非优先客户)'};
	return json[val]||'';
}
function editCust(){
	var custId = $("#custId").val();
	popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:ctxPath+'/pages/crm/cust/cust-edit.jsp',title:'编辑客户',data:{custId:custId}});
}
function reloadCustInfo(){
	$("#custDetailForm").render();
}
function reloadFollowInfo(){
	var data = form.getJSONObject("#custDetailForm");
	$(".followList").render({data:data});
}

function addTeam(){
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['400px','300px'],url:ctxPath+'/pages/crm/include/add-user.jsp',title:'添加团队成员',data:{custId:custId}});
}
/***跟进管理***/
function addFollow(followSource){
	var custId = $("#custId").val();
	var custName = $("[name='cust.CUST_NAME']").text();
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['650px','550px'],url:ctxPath+'/pages/crm/include/add-follow.jsp',title:'新增跟进记录',data:{custId:custId,custName:custName,followSource:followSource}});
}

//合同详情
function contractDetail(contractId){
	popup.layerClose('contractDetail');
   //popup.layerShow({id:'contractDetail',type:1,scrollbar:false,shadeClose:true,offset:'20px',area:['600px','400px'],url:ctxPath+'/pages/crm/contract/simple-detail.jsp',title:'详情',data:{contractId:contractId}});
	popup.openTab({id:'contractDetail',type:1,scrollbar:false,shadeClose:true,offset:'20px',area:['600px','400px'],url:ctxPath+'/pages/crm/contract/contract-detail.jsp',title:'合同详情',data:{contractId:contractId}});
}

function purchaseDetail(orderId){
	
}

function reviewDetail(reviewId){
	popup.openTab({id:'reviewDetail',url:ctxPath+'/pages/bpm/review/review-apply.jsp',title:'评审详情',data:{reviewId:reviewId}});
}

function businessDetail(businessId,custId,custName){
	popup.openTab({id:'bussinessContactQuery',title:'跟进记录',url:ctxPath+'/pages/crm/contacts/contact-query.jsp',data:{businessId:businessId,custId:custId,custName:custName}});
}

function moreFollow(){
	var custId = $("#custId").val();
	popup.openTab({id:'custContactQuery',title:'跟进记录',url:ctxPath+'/pages/crm/contacts/contact-query.jsp',data:{custId:custId}});	
}

function uploadFile(){
	var fkId=$("#custId").val();
	easyUploadFile({callback:'reloadCustFile',fkId:fkId,source:'cust',formId:'custFileForm',fileId:'custLocalfile'});
}

/***发票管理***/
$(function(){
	loadInvoiceTitleList();
	loadInvoiceList();
	loadContractList();
	loadReviewList();
	loadBuyGoodsList();
	loadFileList();
	loadBusinessList();
});

function addBusiness(){
	var custId = $("#custId").val();
	var custName = $("[name='cust.CUST_NAME']").text();
	popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['650px','500px'],url:ctxPath+'/pages/crm/shangji/sj-edit.jsp',title:'新增',closeBtn:0,data:{custId:custId,custName:custName}});
}

function addInvoiceTitle(){
	var custId = $("#custId").val();
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['480px','400px'],url:ctxPath+'/pages/crm/invoice/title/title-edit.jsp',title:'发票抬头',data:{custId:custId}});
}
function editInvoiceTitle(titleId){
	var custId = $("#custId").val();
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['480px','400px'],url:ctxPath+'/pages/crm/invoice/title/title-edit.jsp',title:'发票抬头',data:{custId:custId,titleId:titleId}});
}

function reloadInvoiceTitleList(){
	$("#custDetailForm").queryData({id:'invoiceTitleList',page:false});
}

function reloadInvoiceList(){
	$("#custDetailForm").queryData({id:'invoiceList',page:true});
}

function reloadTeamList(){
	$("#custDetailForm").queryData({id:'teamList',page:false});
}

function reloadCustFile(){
	$("#custDetailForm").queryData({id:'fileList',page:false});
}
function reloadBusinessList(){
	$("#custDetailForm").queryData({id:'businessList',page:false});
}

String.prototype.replaceAll = function(s1, s2) {
    return this.replace(new RegExp(s1, "gm"), s2);
}
function  editFollow(id){
	
}
function getFollowContent(val){
	if(val){
		return val.replaceAll('\n','<br>');
	}
	return '-';
}

/***联系人管理****/

$(function(){
	loadContactsList();
	loadTeamList();
});
function removeTeamUser(custId,userId){
	ajax.remoteCall(ctxPath+"/servlet/cust?action=removeTeamUser",{custId:custId,userId:userId},function(result) { 
		if(result.state == 1){
			layer.msg(result.msg,{icon:1,time:1200},function(){
				reloadTeamList();
			});
		}else{
			layer.alert(result.msg,{icon: 5});
		}
	});
}
function reloadContactsList(){
	$("#custDetailForm").queryData({id:'contactsList',page:false})
}
function addContact(){
	var custId = $("#custId").val();
	var custName = $("[name='cust.CUST_NAME']").text();
	 popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:ctxPath+'/pages/crm/contacts/contacts-edit.jsp',title:'新增联系人',closeBtn:0,data:{custId:custId,custName:custName,isEdit:1}});
}
function editContact(contactsId,isEdit){
	var custName = $("[name='cust.CUST_NAME']").text();
	popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:true,offset:'20px',area:['600px','500px'],url:ctxPath+'/pages/crm/contacts/contacts-edit.jsp',title:'联系人',data:{isEdit:isEdit,contactsId:contactsId,custName:custName}});
}