package com.yunqu.work.model;

import com.yunqu.work.base.AppBaseModel;

public class FileModel extends AppBaseModel{

	private static final long serialVersionUID = 1L;
	
	public FileModel(){
		setTableInfo("YQ_FILES","FILE_ID");
	}
	public void setFileId(String fileId){
		this.set("FILE_ID",fileId);
	}
	public void setFileName(String fileName){
		this.set("FILE_NAME",fileName);
	}
	public void setFileSize(String fileSize){
		this.set("FILE_SIZE", fileSize);
	}
	public void setFileLenth(long fileLenth){
		this.set("FILE_LENTH", fileLenth);
	}
	public void setFileType(String fileType){
		this.set("FILE_TYPE",fileType);
	}
	public void setDiskPath(String diskPath){
		this.set("DISK_PATH",diskPath);
	}
	public void setAccessUrl(String accessUrl){
		this.set("ACCESS_URL",accessUrl);
	}
	public void setFolderId(String folderId){
		this.set("FOLDER_ID",folderId);
	}
	public void setPFkId(String pFkId){
		this.set("P_FK_ID",pFkId);
	}
	
	public void setFileLabel(String fileLabel){
		this.set("FILE_LABEL",fileLabel);
	}
	
	public void setFkId(String fkId){
		this.set("FK_ID",fkId);
	}
	public void setSource(String source){
		this.set("SOURCE",source);
	}
	
	public void setIp(String ip){
		this.set("IP",ip);
	}
	
	public String getFileId(){
		return this.getString("FILE_ID");
	}
	
	public String getFileName(){
		return this.getString("FILE_NAME");
	}
	
	public String getDiskPath(){
		return this.getString("DISK_PATH");
	}
	
	public Long getFileLenth(){
		return this.getLong("FILE_LENTH");
	}

	
}
