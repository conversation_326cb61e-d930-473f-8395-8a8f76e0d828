<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云趣科技 - 全媒体智能呼叫平台解决方案提供商</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1e2c72;
            --text-color: #333;
            --light-bg: #f8f9fa;
        }
        
        body {
            font-family: "Microsoft YaHei", sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }
        
        .navbar {
            background-color: var(--primary-color);
            padding: 0;
        }
        
        .navbar-brand img {
            height: 40px;
            padding: 8px 0;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            padding: 25px 20px !important;
            font-size: 16px;
        }
        
        .nav-link:hover {
            color: #fff !important;
            background-color: rgba(255,255,255,0.1);
        }
        
        .hero {
            background-color: var(--primary-color);
            color: white;
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-content {
            max-width: 600px;
        }
        
        .hero-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn-try {
            background-color: white;
            color: var(--primary-color);
            padding: 12px 30px;
            border-radius: 4px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.3s;
        }
        
        .btn-try:hover {
            transform: translateY(-2px);
            color: var(--primary-color);
        }
        
        .section {
            padding: 60px 0;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 50px;
            font-size: 28px;
            position: relative;
            padding-bottom: 15px;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 2px;
            background: #00a0e9;
        }
        
        .product-item {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .product-icon {
            width: 64px;
            height: 64px;
            margin-bottom: 20px;
        }
        
        .product-title {
            font-size: 20px;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .product-desc {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .btn-more {
            color: #00a0e9;
            text-decoration: none;
            font-size: 14px;
        }
        
        .btn-more:hover {
            color: var(--primary-color);
        }
        
        .partner-section {
            background: var(--light-bg);
        }
        
        .partner-logo {
            font-size: 2.5rem;
            color: #666;
            margin: 20px 20px 10px;
            transition: all 0.3s;
        }
        
        .partner-name {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
            transition: all 0.3s;
        }
        
        .partner-item:hover .partner-logo,
        .partner-item:hover .partner-name {
            color: var(--primary-color);
            transform: scale(1.1);
        }
        
        .footer {
            background: var(--primary-color);
            color: white;
            padding: 40px 0;
        }
        
        .footer-title {
            font-size: 16px;
            margin-bottom: 20px;
            color: rgba(255,255,255,0.9);
        }
        
        .footer-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .footer-list li {
            margin-bottom: 10px;
        }
        
        .footer-list a {
            color: rgba(255,255,255,0.7);
            text-decoration: none;
        }
        
        .footer-list a:hover {
            color: white;
        }
        
        .footer-qrcode {
            font-size: 5rem;
            color: rgba(255,255,255,0.7);
            transition: all 0.3s;
        }
        
        .footer-qrcode:hover {
            color: white;
        }
        
        .copyright {
            text-align: center;
            padding-top: 20px;
            margin-top: 40px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.7);
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .hero {
                padding: 40px 0;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .nav-link {
                padding: 15px !important;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <img src="https://www.yunqu-info.com/static/images/logo.png" alt="云趣科技">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#products">产品服务</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">核心优势</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#news">新闻动态</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">关于我们</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">全媒体智能呼叫平台<br>解决方案提供商</h1>
                <p class="hero-subtitle">国内领先的专注语音通信研究的智能化解决方案提供商</p>
                <a href="#" class="btn-try">免费试用</a>
            </div>
        </div>
    </section>

    <section class="section" id="products">
        <div class="container">
            <h2 class="section-title">产品服务</h2>
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <div class="product-item">
                        <img src="https://www.yunqu-info.com/static/images/index/icon1_1.png" alt="云客服" class="product-icon">
                        <h3 class="product-title">云客服</h3>
                        <p class="product-desc">依托于互联网云计算技术，将客户服务的产品、软件放在云上运行</p>
                        <a href="#" class="btn-more">了解更多 →</a>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="product-item">
                        <img src="https://www.yunqu-info.com/static/images/index/icon2_1.png" alt="云电销" class="product-icon">
                        <h3 class="product-title">云电销</h3>
                        <p class="product-desc">针对销售型需求客户开发的高效电话营销Saas服务</p>
                        <a href="#" class="btn-more">了解更多 →</a>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="product-item">
                        <img src="https://www.yunqu-info.com/static/images/index/icon1_4.png" alt="云总机" class="product-icon">
                        <h3 class="product-title">云总机</h3>
                        <p class="product-desc">基于云平台的企业级通讯解决方案</p>
                        <a href="#" class="btn-more">了解更多 →</a>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="product-item">
                        <img src="https://www.yunqu-info.com/static/images/index/icon4_1.png" alt="云录音" class="product-icon">
                        <h3 class="product-title">云录音</h3>
                        <p class="product-desc">基于互联网的录音数据统一管理服务</p>
                        <a href="#" class="btn-more">了解更多 →</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section partner-section">
        <div class="container">
            <h2 class="section-title">合作伙伴</h2>
            <div class="row align-items-center justify-content-center">
                <div class="col-4 col-md-2 text-center partner-item">
                    <i class="fa-solid fa-building partner-logo" title="中国电信"></i>
                    <div class="partner-name">中国电信</div>
                </div>
                <div class="col-4 col-md-2 text-center partner-item">
                    <i class="fa-solid fa-signal partner-logo" title="中国移动"></i>
                    <div class="partner-name">中国移动</div>
                </div>
                <div class="col-4 col-md-2 text-center partner-item">
                    <i class="fa-solid fa-globe partner-logo" title="中国联通"></i>
                    <div class="partner-name">中国联通</div>
                </div>
                <div class="col-4 col-md-2 text-center partner-item">
                    <i class="fa-solid fa-cloud partner-logo" title="腾讯云"></i>
                    <div class="partner-name">腾讯云</div>
                </div>
                <div class="col-4 col-md-2 text-center partner-item">
                    <i class="fa-solid fa-cloud partner-logo" title="阿里云"></i>
                    <div class="partner-name">阿里云</div>
                </div>
                <div class="col-4 col-md-2 text-center partner-item">
                    <i class="fa-solid fa-cloud partner-logo" title="华为云"></i>
                    <div class="partner-name">华为云</div>
                </div>
            </div>
        </div>
    </section>

    <section class="section partner-section">
        <div class="container">
            <h2 class="section-title">关于我们</h2>
            <div class="row align-items-center justify-content-center">
                <br>
                广州云趣信息科技有限公司（简称“云趣科技”）成立于2017年1月，由佳都新太科技股份有限公司（股票代码：600728）原通讯事业部核心员工与佳都新太科技股份有限公司内部孵化共同持股创办。目前公司总部设在广州市天河国家级科技园，业务辐射全国，重点布局国内一线城市，目前公司拥有北京、上海2家分公司，另在西安、郑州、武汉、长沙、南京、杭州、成都、沈阳、吉林、大庆、海南、新疆和西藏等20几个省市设置办事处或驻点。
                <br> <br>
                继承中国第一家专注于电信语音增值业务的服务提供商新太科技的强大基因，云趣科技的研发团队技术沉淀已超过20年，是业界最具经验的研发团队，同时对行业前沿发展有着深刻理解，掌握各项高精尖核心技术，包括云通讯平台、智能AI应用、大数据、自主化业务开发平台等核心技术，主要产品包括通信增值、智能云客服平台、云总机等技术和业务平台。
                <br> <br>
                目前，云趣科技自主研发的AI产品被广泛应用于电信、金融、保险、政务、公共事业、物流、汽车出行、教育、电子商务、生产制造等众多行业领域，已承接中国电信、中国移动、中国联通、2022北京冬奥会、中国人民保险、国家广电总局、中国石油大庆油田、三一重工、美的集团、碧桂园、华润、海康威视等各行业头部企业的大型项目，同时与阿里、腾讯成功建立生态合作模式。
                <br> <br>
                云趣科技专注于通讯技术的产品研发，依托自身多年的技术积累，凭借在行业信息化领域的技术实力和产品方案，持续为行业客户提供更加专业的通讯服务。未来，云趣科技将致力于成为持久卓越的世界级智慧通讯科技领先企业，不断探索及整合通讯技术及应用的无限可能性，用技术改变世界，让生活更美好。
                <br>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5 class="footer-title">联系我们</h5>
                    <ul class="footer-list">
                        <li>广州市天河区建工路4号佳都新太大厦6楼</li>
                        <li>020-32255688</li>
                        <li><EMAIL></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="footer-title">关于我们</h5>
                    <ul class="footer-list">
                        <li><a href="#">公司简介</a></li>
                        <li><a href="#">新闻动态</a></li>
                        <li><a href="#">加入我们</a></li>
                        <li><a href="#">联系我们</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="footer-title">产品服务</h5>
                    <ul class="footer-list">
                        <li><a href="#">云客服</a></li>
                        <li><a href="#">云电销</a></li>
                        <li><a href="#">云总机</a></li>
                        <li><a href="#">云录音</a></li>
                        <li><a href="#">12345政府服务热线</a></li>
                    </ul>
                </div>
            </div>
            <div class="copyright">
                <a target="_blank" href="https://beian.miit.gov.cn/">版权所有 © 2022 广州云趣信息科技有限公司粤ICP备17071801号</a>
            </div>
        </div>
    </footer>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>

