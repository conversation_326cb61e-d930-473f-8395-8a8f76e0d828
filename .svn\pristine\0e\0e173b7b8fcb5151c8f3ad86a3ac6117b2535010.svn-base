<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>选择款项</title>
</EasyTag:override>
<EasyTag:override name="content">
    <form method="post" class="form-inline" onsubmit="return false;" id="PaymentSelectForm">
        <input type="hidden" name="contractId" value="${param.contractId}"/>
        <div class="row">
            <div class="input-group input-group-sm ml-15" style="width: 200px">
                <span class="input-group-addon">款项类型</span>
                <SELECT name="paymentType1" data-mars="ContractPaymentDao.getPaymentType"  class="form-control">
                    <option value="" >全部</option>
                </SELECT>
            </div>
            <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="PaymentSelect.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
        </div>
        <div class="ibox">
            <table id="PaymentSelectList"></table>
        </div>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary"  type="button" onclick="PaymentSelect.ok()">确定</button>
            <button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">

    <script type="text/javascript">
        var PaymentSelect={
            sid:'${param.sid}',
            contractId:'${param.contractId}',

            query : function(){
                $("#PaymentSelectForm").queryData({id:'PaymentSelectList'});
            },


            initTable : function(){

                $("#PaymentSelectForm").initTable({
                        mars:'ContractPaymentDao.paymentListForSelect',
                        id:'PaymentSelectList',
                        page:true,
                        limit:20,
                        height:'400px',
                        rowDoubleEvent:'PaymentSelect.ok',
                        cols: [[
                            {type:'${param.type}'},
                            {
                                type: 'numbers',
                                title: '序号',
                                align:'left'
                            },{
                                field: 'PAYMENT_TYPE',
                                title: '款项类型',
                                minWidth:150,
                                templet:function(d){
                                    return getText(d.PAYMENT_TYPE,'paymentType1');
                                }
                            },{
                                field: 'RATIO',
                                title: '款项占比(%)',
                            },{
                                field: 'AMOUNT_WITH_TAX',
                                title: '含税金额',
                            },{
                                field: 'AMOUNT_NO_TAX',
                                title: '税后金额',
                            }
                        ]]
                    }
                );
            },
            ok : function(selectRow,obj){
                if($("span[name='paymentNames']")!=undefined || $("span[name='paymentNames']") != null){
                    $("span[name='paymentNames']").remove();
                }

                var el = $("[data-sid='"+PaymentSelect.sid+"']");

                if(selectRow==undefined){
                    //用勾选后确定
                    var checkStatus = table.checkStatus('PaymentSelectList');
                    if(checkStatus.data.length>0){
                        var data = checkStatus.data;
                        var amount  = 0;
                        var tax  = 0;
                        var amountNoTax  = 0;
                        var paymentIds = [];
                        var paymentName = [];
                        var ratio = 0;
                        for(var index in data){
                            paymentIds.push(data[index]['PAYMENT_ID']);
                            paymentName.push(getText(data[index]['PAYMENT_TYPE'],'paymentType1')+"-比例"+data[index]['RATIO']+"%--金额:"+data[index]['AMOUNT_WITH_TAX']);
                            amount = amount + Number(data[index]['AMOUNT_WITH_TAX']);
                            tax = tax + Number(data[index]['TAX']);
                            ratio = ratio + Number(data[index]['RATIO']);
                            amountNoTax = amountNoTax + Number(data[index]['AMOUNT_NO_TAX']);

                        }
                        var total = {
                            AMOUNT_WITH_TAX:amount,
                            TAX:tax,
                            AMOUNT_NO_TAX:amountNoTax,
                            RATIO:ratio,
                            PAYMENT_ID:paymentIds.join(","),
                            PAYMENT_NAME:paymentName.join(","),
                            paymentNums:data.length
                        };
                        PaymentSelectCallBack(total);
                        popup.layerClose("PaymentSelectForm");
                    }else{
                        layer.msg("请选择!");
                    }
                }else{
                    //用了双击的方法
                    selectRow['PAYMENT_NAME']=getText(selectRow['PAYMENT_TYPE'],'paymentType1')+"-比例"+selectRow['RATIO']+"%--金额:"+selectRow['AMOUNT_WITH_TAX'];
                    selectRow['paymentNums']= 1;
                    PaymentSelectCallBack(selectRow);
                    popup.layerClose("PaymentSelectForm");
                }
            }
        };
        $(function(){
            $('[name="paymentType1"]').render({success:function(result){
                    PaymentSelect.initTable();
                }});
        });

        function reloadPaymentSelectList(){
            PaymentSelect.query();
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>