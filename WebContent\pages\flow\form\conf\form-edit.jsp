<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>FormEdit</title>
	<style>
		#editForm img{max-width: 98%!important;height: auto;}
		#editForm .w-e-text{max-width: 100%!important;}
		#editForm table,tr,td,p{max-width: 100%!important;}
	</style>
	<link href="${ctxPath}/static/css/wangEditor-fullscreen.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" style="margin-bottom: 100px;" data-mars="FormDao.formInfo" autocomplete="off" data-mars-prefix="form.">
   		    <input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
   		    <input type="hidden" id="formId" value="${param.formId}" name="form.FORM_ID"/>
   		    <input type="hidden" value="${param.formId}" name="formId"/>
   		    <input type="hidden" value="${param.formId}" name="fkId"/>
   		    <div class="layui-panel" style="min-height: calc(100vh - 100px);padding: 20px;">
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 100px;" class="required">表单名称</td>
		                    <td>
		                    	<input data-rules="required" maxlength="200" type="text" name="form.FORM_NAME" class="form-control input-sm">
		                    </td>
		                    <td class="required">库表名称</td>
		                    <td>
		                    	<input data-rules="required" maxlength="200" type="text" name="form.TABLE_NAME" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
			            	<td>表单代码</td>
			               	<td colspan="3">
	                           <textarea style="height: 300px;width:100%;" class="form-control input-sm" name="form.FORM_CODE"></textarea>
			               	</td>
			            </tr>
			            <tr>
			            	<td>详情代码</td>
			               	<td colspan="3">
	                           <textarea style="height: 300px;width:100%;" class="form-control input-sm" name="form.DETAIL_CODE"></textarea>
			               	</td>
			            </tr>
			        </tbody>
				 </table>
   		    
   		    </div>
			 <div class="layer-foot text-c" style="z-index: 99999999999;position: fixed;">
		    	  <button type="button" class="btn btn-primary btn-sm" onclick="FormEdit.ajaxSubmitForm()"> 保 存  </button>
			      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.closeTab();"> 关闭 </button>
			</div>			
</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
	    String.prototype.replaceAll = function(s1, s2) {
		    return this.replace(new RegExp(s1, "gm"), s2);
		}
	
		jQuery.namespace("FormEdit");
	    
		FormEdit.formId='${param.formId}';
		var formId='${param.formId}';
		
		
		
		$(function(){
			$("#editForm").render({success:function(result){

			}});  
		});

		
		FormEdit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(FormEdit.formId){
					FormEdit.updateData(); 
				}else{
					FormEdit.insertData(); 
				}
			};
		}
		FormEdit.insertData = function(flag) {
			$("#formId").val($("#randomId").val());
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/flow/form?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,function(){
						popup.closeTab();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		FormEdit.updateData = function(flag) {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/flow/form?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,function(){
						popup.closeTab();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>