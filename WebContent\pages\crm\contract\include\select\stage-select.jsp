<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>选择款项</title>
</EasyTag:override>
<EasyTag:override name="content">
    <form method="post" class="form-inline" onsubmit="return false;" id="StageSelectForm">
        <input type="hidden" name="contractId" value="${param.contractId}"/>
        <input type="hidden" name="incomeStageId" value="${param.incomeStageId}"/>
        <div class="row">
            <div class="input-group input-group-sm ml-15" style="width: 200px">
                <span class="input-group-addon">合同阶段</span>
                <SELECT data-rules="required" name="stageName" class="form-control">
                    <option value="">--全部--</option>
                    <option value="第一期">第一期</option>
                    <option value="第二期">第二期</option>
                    <option value="第三期">第三期</option>
                    <option value="第四期">第四期</option>
                    <option value="第五期">第五期</option>
                    <option value="第六期">第六期</option>
                    <option value="第七期">第七期</option>
                    <option value="第八期">第八期</option>
                    <option value="第九期">第九期</option>
                    <option value="第九期">第九期</option>
                    <option value="第十期">第十期</option>
                    <option value="预付款">预付款</option>
                    <option value="货到款">货到款</option>
                    <option value="上线款">上线款</option>
                    <option value="分成款">分成款</option>
                    <option value="阶段款">阶段款</option>
                    <option value="初验款">初验款</option>
                    <option value="终验款">终验款</option>
                    <option value="竣工款">竣工款</option>
                    <option value="质保款">质保款</option>
                </SELECT>
            </div>
            <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="StageSelect.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
        </div>
        <div class="ibox">
            <table id="StageSelectList"></table>
        </div>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary"  type="button" onclick="StageSelect.ok()">确定</button>
            <button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">

    <script type="text/javascript">
        var StageSelect={
            sid:'${param.sid}',
            contractId:'${param.contractId}',

            query : function(){
                $("#StageSelectForm").queryData({id:'StageSelectList'});
            },

            initTable : function(){

                $("#StageSelectForm").initTable({
                        mars:'ContractStageDao.stageListForSelect',
                        id:'StageSelectList',
                        page:true,
                        limit:20,
                        height:'400px',
                        rowDoubleEvent:'StageSelect.ok',
                        cols: [[
                            {type:'${param.type}'},
                            {
                                type: 'numbers',
                                title: '序号',
                                align:'left'
                            },{
                                field: 'STAGE_NAME',
                                title: '阶段名称',
                                minWidth:80,
                            },{
                                field: 'RCV_DATE',
                                title: '应收日期',
                            },{
                                field: 'PLAN_COMP_DATE',
                                title: '计划完成日期',
                            },{
                                field: 'RATIO',
                                title: '比例(%)',
                            },{
                                field: 'PLAN_RCV_AMT',
                                title: '计划收款金额',
                                minWidth:100
                            },{
                                field: 'INCOME_STAGE_FLAG',
                                title: '状态',
                                templet:function(d){
                                    if(d.INCOME_STAGE_FLAG=='1'){
                                        return '原选择阶段';
                                    }else{
                                        return '未选择阶段';
                                    }
                                }
                            }
                        ]]
                    }
                );
            },
            ok : function(selectRow,obj){

                var el = $("[data-sid='"+StageSelect.sid+"']");

                if(selectRow==undefined){
                    //用勾选后确定
                    var checkStatus = table.checkStatus('StageSelectList');
                    if(checkStatus.data.length>0){
                        if($("span[name='contractStageNames']")!=undefined || $("span[name='contractStageNames']") != null){
                            $("span[name='contractStageNames']").remove();
                        }
                        var data = checkStatus.data;
                        var amount  = 0;
                        var stageIds = [];
                        var stageNames = [];
                        var ratio = 0;
                        for(var index in data){
                            stageIds.push(data[index]['STAGE_ID']);
                            stageNames.push(data[index]['STAGE_NAME']+"--比例:"+data[index]['RATIO']+"%--金额:"+data[index]['PLAN_RCV_AMT']);
                            amount = amount + Number(data[index]['PLAN_RCV_AMT']);
                            ratio = ratio + Number(data[index]['RATIO']);
                        }
                        var total = {
                            PLAN_RCV_AMT:amount.toFixed(2),
                            RATIO:ratio.toFixed(2),
                            CONTRACT_STAGE_ID:stageIds.join(","),
                            CONTRACT_STAGE_NAME:stageNames.join(","),
                            selectNums:data.length
                        };
                        StageSelectCallBack(total);
                        popup.layerClose("StageSelectForm");
                    }else{
                        layer.msg("请选择!");
                    }
                }else{
                    if($("span[name='contractStageNames']")!=undefined || $("span[name='contractStageNames']") != null){
                        $("span[name='contractStageNames']").remove();
                    }
                    //用了双击的方法
                    selectRow['CONTRACT_STAGE_NAME'] = selectRow['STAGE_NAME']+"--比例:"+selectRow['RATIO']+"%--金额:"+selectRow['PLAN_RCV_AMT'];
                    selectRow['CONTRACT_STAGE_ID'] = selectRow['STAGE_ID'];

                    selectRow['selectNums']= 1;
                    StageSelectCallBack(selectRow);
                    popup.layerClose("StageSelectForm");
                }
            }
        };
        $(function(){
              StageSelect.initTable();
        });

        function reloadStageSelectList(){
            StageSelect.query();
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>