	body, html {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
    }
	ul{list-style: none;padding: 0;margin: 0;}
    .page-box {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: #f2f2f2;
    }

    .page-box .left-sidebar {
        width: 140px;
        height: 100%;
        background: #fff;
        float: left;
        overflow: auto;
        /*padding-top: 10px;*/
    }

    .page-box .left-sidebar-title {
        height: 60px;
        line-height: 60px;
        text-align: center;
        font-size: 14px;
        color: #333;
    }
    .left-sidebar-item{position: relative;}
	.left-sidebar .left-sidebar-item  a{
	    display: block;
	    color: #333;
	    font-size: 13px;
	     min-height: 40px;
		line-height: 40px;
		padding-left: 27px;
       
	}
	/*.left-sidebar .left-sidebar-item a:link,*/
	.left-sidebar .left-sidebar-item a:visited,
	.left-sidebar .left-sidebar-item a:hover,
	.left-sidebar .left-sidebar-item a:active {
	    background: #f5f5f5;
	    text-decoration: none;
	}
	.left-sidebar .left-sidebar-item a:link{text-decoration: none;}
	.left-sidebar .left-sidebar-item .sidebar-item a{
	    
        min-height: 40px;
		line-height: 40px;
		padding-left: 27px;
	}
	.sidebar-item{position: relative;}
	.left-sidebar .left-sidebar-item .activeItem{
	    background: #f5f5f5;
	    text-decoration: none;
	}
	.left-sidebar .left-sidebar-item .sidebar-item .sidebar-nav-sub-title.active{background: none}
	.left-sidebar .left-sidebar-item .sidebar-item .sidebar-nav-sub {
	    display: none;
	    padding-left: 8px;
	}
	.left-sidebar .left-sidebar-item .sidebar-item > a.active + .sidebar-nav-sub {
	    display: block;
	}
	
	.left-sidebar .left-sidebar-item .sidebar-item a .sidebar-icon {
	    position: absolute;
	    left: 8px;
	    display: inline-block;
	    transition: all 0.3s ease-in-out;
	    font-size: 12px;
	    line-height: 40px;
	}

	.left-sidebar .left-sidebar-item .sidebar-item > a.active .sidebar-icon {
	    transform: rotate(90deg);
	}

    .page-box .right-content {padding: 10px; height: 100%; overflow: auto; margin-left: 140px;}