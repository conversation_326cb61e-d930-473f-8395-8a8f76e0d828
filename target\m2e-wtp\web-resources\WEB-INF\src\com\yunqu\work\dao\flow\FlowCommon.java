package com.yunqu.work.dao.flow;

import java.sql.SQLException;
import java.util.Calendar;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.utils.DateUtils;

@WebObject(name = "FlowCommon")
public class FlowCommon extends AppDaoContext {

	@WebControl(name = "approveWord",type = Types.LIST)
	public JSONObject approveWord() {
		EasySQL sql = getEasySQL("select * from yq_flow_approve_word where 1=1");
		sql.append(getUserId(),"and create_by = ?");
		sql.append("order by order_index,create_time");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "approveWordDict",type = Types.DICT)
	public JSONObject approveWordDict() {
		EasySQL sql = getEasySQL("select word_content,word_content from yq_flow_approve_word where 1=1");
		sql.append(getUserId(),"and create_by = ?");
		sql.append("order by order_index,create_time");
		return getDictByQuery(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "items",type = Types.TEMPLATE)
	public JSONObject items() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from yq_flow_apply_item where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by order_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "reviseList",type = Types.TEMPLATE)
	public JSONObject reviseList() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from yq_flow_revise_record where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by add_time desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "printList",type = Types.TEMPLATE)
	public JSONObject printList() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from yq_flow_print where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by print_time desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "approveRecordList",type = Types.TEMPLATE)
	public JSONObject approveRecordList() {
		EasySQL sql = getEasySQL("select t1.*,t2.apply_id,t2.flow_code,t2.current_result_id,t2.apply_time,t2.apply_remark,t2.apply_name,t2.dept_name apply_dept_name,t2.apply_title,t2.apply_state from yq_flow_approve_result t1,yq_flow_apply t2 where 1=1");
		sql.append("and t1.business_id = t2.apply_id and t1.check_result>0 and t1.node_id<>'0' ");
		String source = param.getString("source");
		if(StringUtils.notBlank(source)) {
			if("checked".equals(source)) {
				sql.append(getUserId(),"and t1.check_user_id = ?");
			}else {
				sql.append(getUserId(),"and t2.apply_by = ?");
			}
		}else if(!isFlowMgr()) {
			sql.append(getUserId(),"and t2.apply_by = ?");
		}
		sql.append(DateUtils.getPlanMaxDay(-30),"and t2.apply_date >= ?");
		sql.append("ORDER BY t1.check_time desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "lookRecordList",type = Types.TEMPLATE)
	public JSONObject lookRecordList() {
		EasySQL sql = getEasySQL("select t1.*,t2.apply_id,t2.flow_code,t2.current_result_id,t2.apply_time,t2.apply_name,t2.dept_name apply_dept_name,t2.apply_title,t2.apply_state from yq_look_log t1,yq_flow_apply t2 where 1=1");
		sql.append("and t1.fk_id = t2.apply_id");
		if(!isFlowMgr()) {
			sql.append(getUserId(),"and t2.apply_by = ?");
		}
		sql.append(DateUtils.getPlanMaxDay(-30),"and t2.apply_date >= ?");
		sql.append("ORDER BY t1.look_time desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "followRecordList",type = Types.TEMPLATE)
	public JSONObject followRecordList() {
		EasySQL sql = getEasySQL("select t1.*,t2.apply_id,t2.flow_code,t2.current_result_id,t2.apply_time,t2.apply_name,t2.dept_name apply_dept_name,t2.apply_title,t2.apply_state from yq_flow_follow t1,yq_flow_apply t2 where 1=1");
		sql.append("and t1.business_id = t2.apply_id");
		if(!isFlowMgr()) {
			sql.append(getUserId(),"and t2.apply_by = ?");
		}
		sql.append(DateUtils.getPlanMaxDay(-30),"and t2.apply_date >= ?");
		sql.append("ORDER BY t1.add_time desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "ccList",type = Types.TEMPLATE)
	public JSONObject ccList() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from yq_flow_cc where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by cc_time desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "followList",type = Types.TEMPLATE)
	public JSONObject followList() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from yq_flow_follow where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by add_time desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "trustInfo",type = Types.TEMPLATE)
	public JSONObject trustInfo() {
		String trustId = param.getString("trustId");
		if(StringUtils.isBlank(trustId)) {
			return getJsonResult(new JSONObject());
		}
		EasySQL sql = getEasySQL("select * from yq_flow_trust where 1=1");
		sql.append(trustId,"and trust_id = ?");
		return queryForRecord(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "resultCCList",type = Types.TEMPLATE)
	public JSONObject resultCCList() {
		String resultId = param.getString("resultId");
		if(StringUtils.isBlank(resultId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select t1.* from yq_flow_cc t1,yq_flow_approve_result t2 where 1=1 and t1.result_id = t2.result_id");
		sql.append(resultId,"and t2.result_id = ?");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	
	@WebControl(name = "roomApplyList",type = Types.TEMPLATE)
	public JSONObject roomApplyList() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_apply t1 where 1=1");
		sql.append(10,"and t1.apply_state >= ?");
		sql.append("ad_rome","and t1.flow_code = ?");
		sql.append("order by t1.apply_time desc limit 10");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "monthWorkDay",type = Types.TEXT)
	public JSONObject monthWorkDay() {
		String month = EasyCalendar.newInstance().getFullMonth();
		try {
			Calendar calendar = Calendar.getInstance();
	        int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
			int notWorkDay = this.getQuery().queryForInt("select count(1) from yq_no_work_day where day like '"+month+"%'");
			if(notWorkDay>0) {
				return getText(String.valueOf(daysInMonth - notWorkDay));
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getText("");
	}
	
	@WebControl(name = "monthWorkDay2",type = Types.TEXT)
	public JSONObject monthWorkDay2() {
		Calendar calendar = Calendar.getInstance();
		int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
		String month = EasyCalendar.newInstance().getFullMonth();
		try {
			int notWorkDay = this.getQuery().queryForInt("select count(1) from yq_no_work_day where  type = 1 and day like '"+month+"%'");
			return getText(String.valueOf(daysInMonth - notWorkDay));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getText(String.valueOf(daysInMonth));
	}
	
	@WebControl(name = "personBxData",type = Types.TEMPLATE)
	public JSONObject personBxData() {
		String userId = getUserId();
		EasySQL sql = new EasySQL();
		sql.append("select apply_date,sum(t1.pay_money) bx_money from yq_flow_bx_base t1,yq_flow_apply t2 where t2.apply_id= t1.business_id and t2.apply_state in (10,20,30)");
		sql.append(userId,"and t2.apply_by = ?");
		sql.append("GROUP BY t2.apply_date ORDER BY t2.apply_date desc limit 8");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name = "kqTypeStat",type = Types.TEMPLATE)
	public JSONObject kqTypeStat() {
		String userId = getUserId();
		String date = null;
		String businessId = param.getString("businessId");
		if(StringUtils.isNotBlank(businessId)) {
			try {
				JSONObject row = this.getQuery().queryForRow("select apply_by,data11 from yq_flow_apply where apply_id = ?",new Object[] {businessId},new JSONMapperImpl());
				userId = row.getString("APPLY_BY");
				date = row.getString("DATA11");
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		EasySQL sql = new EasySQL();
		Calendar calendar = Calendar.getInstance();
		int year = calendar.get(Calendar.YEAR);
		String month = EasyDate.getCurrentDateString("yyyy-MM");
		if(date!=null) {
			year = Integer.valueOf(date.substring(0, 4));
			month = date.substring(0, 7);
		}
		
		String flowCode = param.getString("flowCode");
		sql.append("select data14 type,ROUND(sum(data13),2) num from yq_flow_apply where 1=1");
		sql.append(flowCode,"and flow_code = ?");
		if("hr_kq_yc".equals(flowCode)) {
			sql.append(month,"and LEFT(data11,7) = ?");
		}else {
			sql.append(year,"and LEFT(data11,4) = ?");
		}
		sql.append(userId,"and apply_by = ?");
		sql.appendIn(new int[]{10,20,30},"and apply_state");
		sql.append("GROUP BY data14");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}
	
}
