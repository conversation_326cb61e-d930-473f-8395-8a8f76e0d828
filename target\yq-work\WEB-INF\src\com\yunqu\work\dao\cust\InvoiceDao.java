package com.yunqu.work.dao.cust;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name="InvoiceDao")
public class InvoiceDao extends AppDaoContext{

	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String invoiceId = param.getString("invoiceId");
		return queryForRecord("select * from yq_crm_invoice where invoice_id = ?",invoiceId);
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from yq_crm_invoice where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append(param.getString("contractId"),"and contract_id = ?");
		sql.append(param.getString("platformId"),"and PLATFORM_ID = ?");
		sql.appendLike(param.getString("invoiceNo"),"and invoice_no like ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="pageList",type=Types.LIST)
	public JSONObject pageList(){
		EasySQL sql=getEasySQL("select * from yq_crm_invoice where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append(param.getString("contractId"),"and contract_id = ?");
		sql.append(param.getString("platformId"),"and PLATFORM_ID = ?");
		sql.append(param.getString("kpBeginDate"),"and mark_date >= ?");
		sql.append(param.getString("kpEndDate"),"and mark_date <= ?");
		sql.appendLike(param.getString("invoiceNo"),"and invoice_no like ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="platformQueryList",type=Types.LIST)
	public JSONObject platformQueryList(){
		EasySQL sql=getEasySQL("select t1.*,t2.PLATFORM_TYPE_NAME,t4.CONTRACT_SIMPILE_NAME,t4.SALES_BY_NAME from yq_crm_invoice t1 left join yq_business_platform t3 on t1.PLATFORM_ID = t3.PLATFORM_ID left join yq_business_platform_type t2 on t3.PLATFORM_TYPE_ID = t2.PLATFORM_TYPE_ID left join yq_project_contract t4 on t1.CONTRACT_ID = t4.CONTRACT_ID where 1=1");
		sql.append("and t1.PLATFORM_ID IS NOT NULL AND t1.PLATFORM_ID != '' ");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		sql.append(param.getString("contractId"),"and t1.CONTRACT_ID = ?");
		sql.append(param.getString("paymentType"),"and t1.PAYMENT_TYPE = ?");
		sql.append(param.getString("invoiceType"),"and t1.INVOICE_TYPE = ?");
		sql.append(param.getString("platformId"),"and t1.PLATFORM_ID = ?");
		sql.append(param.getString("platformTypeId"),"and t3.PLATFORM_TYPE_ID = ?");
		sql.append(param.getString("kpBeginDate"),"and t1.MARK_DATE >= ?");
		sql.append(param.getString("kpEndDate"),"and t1.MARK_DATE <= ?");
		sql.append(param.getString("saleBy"),"and t4.SALES_BY = ?");
		sql.appendLike(param.getString("invoiceNo"),"and t1.INVOICE_NO like ?");
		sql.appendLike(param.getString("custName"),"and t1.CUST_NAME like ?");
		sql.appendLike(param.getString("contractNo"),"and t1.CONTRACT_NO like ?");
		sql.appendLike(param.getString("contractName"),"and t1.CONTRACT_NAME like ?");
		sql.append("order by t1.mark_date desc,t1.CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="kpSumSelect",type=Types.DICT)
	public JSONObject kpSumSelect(){
		EasySQL sql = new EasySQL("select t1.STAGE_ID, SUM(COALESCE(t2.mark_money, 0)) AS SUN_KP_AMOUNT from yq_contract_stage t1");
		sql.append("left join yq_crm_invoice t2 on t1.STAGE_ID = t2.STAGE_ID");
		sql.append("WHERE 1=1 ");
		sql.append(param.getString("contractId")," and t1.CONTRACT_ID = ?");
		sql.append("GROUP BY t1.STAGE_ID ");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="titleRecord",type=Types.RECORD)
	public JSONObject titleRecord(){
		String titleId = param.getString("titleId");
		return queryForRecord("select * from yq_crm_invoice_title where title_id = ?",titleId);
	}
	
	@WebControl(name="titleList",type=Types.LIST)
	public JSONObject titleList(){
		EasySQL sql=getEasySQL("select t1.*,t2.cust_name from yq_crm_invoice_title t1,yq_crm_customer t2 where 1=1");
		sql.append("and t1.cust_id = t2.cust_id");
		sql.append(param.getString("custId"),"and t1.cust_id = ?");
		sql.append(param.getString("custName"),"and t2.cust_name = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}

}
