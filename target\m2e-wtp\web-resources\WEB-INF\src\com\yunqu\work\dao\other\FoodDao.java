package com.yunqu.work.dao.other;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.utils.DateUtils;

@WebObject(name="FoodDao")
public class FoodDao extends AppDaoContext{
	
	public static String week6="none";
	public static String week7="none";
	// 0：开启 1关闭点餐
	public static String dcSwitch="0";

	@WebControl(name="foodList",type=Types.LIST)
	public JSONObject foodList(){
		EasySQL sql=getEasySQL("select * from dc_food where 1=1 ");
		sql.appendLike(param.getString("foodName"), "and food_name like ?");
		sql.append(param.getString("state"), "and state = ?");
		sql.append(param.getString("supplier"),"and supplier  = ?");
		sql.append(param.getString("dinnerType"), "and dinner_type = ?");
		sql.append("order by dinner_type asc,create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="myTodayBook",type=Types.TEMPLATE)
	public JSONObject myTodayBook(){
		EasySQL sql=getEasySQL("select t2.FOOD_NAME,t1.* from dc_reserve_list t1 INNER JOIN dc_food t2 on t2.id=t1.food_list_id  where 1=1 and t1.state  = 0");
		sql.append(getUserPrincipal().getUserId(), " and t1.user_id= ? ");
		sql.append(DateUtils.getPlanToday(), "and t1.ymd = ? ");
		JSONObject result=queryForList(sql.getSQL(), sql.getParams());
		result.put("currentTime", EasyDate.getCurrentDateString("yyyy/MM/dd HH:mm"));
		result.put("currentDate", EasyDate.getCurrentDateString("yyyy/MM/dd"));
		result.put("eatTimes",queryForList("select * from dc_eat_time where state = 1",null).getJSONArray("data"));
		result.put("dcSwitch", dcSwitch);
		return result;
	}
	/**
	 * 我的订餐统计
	 * @return
	 */
	@WebControl(name="myBookStat",type=Types.TEMPLATE)
	public JSONObject myBookStat(){
		EasySQL sql=getEasySQL("select t2.FOOD_NAME,t1.* from dc_reserve_list t1 INNER JOIN dc_food t2 on t2.id=t1.food_list_id  where 1=1 ");
		sql.append(getUserPrincipal().getUserId(), " and t1.user_id= ? ");
		sql.append(param.getString("dinnerType"), "and t1.dinner_type = ?");
		sql.append(param.getString("ym"), "and t1.ym = ? ");
		sql.append("order by t1.create_time desc");
		JSONObject result=queryForPageList(sql.getSQL(), sql.getParams());
		return result;
	}
	
	@WebControl(name="bookMgr",type=Types.TEMPLATE)
	public JSONObject bookMgr(){
		EasySQL sql=getEasySQL("select t2.FOOD_NAME,t1.*,t4.STAFF_NO,t3.DEPTS,t3.USERNAME from dc_reserve_list t1 INNER JOIN dc_food t2 on t2.id=t1.food_list_id");
		sql.append("INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t3  on t3.USER_ID = t1.USER_ID ");
		sql.append("INNER JOIN "+Constants.DS_MAIN_NAME+".yq_staff_info t4  on t4.STAFF_USER_ID = t1.USER_ID ");
		sql.append(" where 1=1");
		sql.append(param.getString("state"), " and t1.state= ? ");
		sql.appendLike(param.getString("userName"), " and t3.USERNAME like  ? ");
		sql.append(param.getString("userId"), " and t1.user_id= ? ");
		sql.append(param.getString("bookType"), " and t1.book_type= ? ");
		sql.append(param.getString("dinnerType"), " and t1.dinner_type= ? ");
		sql.append(param.getString("date"), "and t1.ymd = ? ",String.valueOf(DateUtils.getPlanToday()));
		sql.append("order by t1.create_time desc");
		JSONObject result=queryForPageList(sql.getSQL(), sql.getParams());
		return result;
	}
	@WebControl(name="deptStat",type=Types.TEMPLATE)
	public JSONObject deptStat(){
		EasySQL sql=getEasySQL("select t2.DEPTS,count(1) count,SUM(t3.food_price) price,sum(case WHEN t1.dinner_type=2 then t3.food_price else 0 end) lunch_price,sum(case WHEN t1.dinner_type=3 then t3.food_price else 0 end) supper_price,sum(case WHEN t1.dinner_type=2 then 1 else 0 end) lunch,sum(case WHEN t1.dinner_type=3 then 1 else 0 end) supper  from dc_reserve_list t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.user_id INNER JOIN dc_food t3 on t3.id=t1.food_list_id where 1=1");
		sql.append("and t1.state = 0");
		sql.append(param.getString("dinnerType"), "and t1.dinner_type = ?");
		sql.append(param.getString("supplier"),"and t3.supplier  = ?");
		sql.append(param.getString("ym"), "and t1.ym = ? ",String.valueOf(DateUtils.getTodayYm()));
		sql.append("GROUP BY t2.DEPTS");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="personStat",type=Types.TEMPLATE)
	public JSONObject personStat(){
		EasySQL sql=getEasySQL("select t2.USERNAME,t2.DEPTS,count(1) count,SUM(t3.food_price) price,sum(case WHEN t1.dinner_type=2 then t3.food_price else 0 end) lunch_price,sum(case WHEN t1.dinner_type=3 then t3.food_price else 0 end) supper_price,sum(case WHEN t1.dinner_type=2 then 1 else 0 end) lunch,sum(case WHEN t1.dinner_type=3 then 1 else 0 end) supper from dc_reserve_list t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.user_id=t1.user_id INNER JOIN dc_food t3 on t3.id=t1.food_list_id where 1=1");
		sql.append("and t1.state = 0");
		sql.append(param.getString("dinnerType"), "and t1.dinner_type = ?");
		sql.append(param.getString("supplier"),"and t3.supplier  = ?");
		sql.append(param.getString("ym"), "and t1.ym = ? ",String.valueOf(DateUtils.getTodayYm()));
		sql.append("GROUP BY t2.user_id");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="todayMeals",type=Types.TEMPLATE)
	public JSONObject todayMenus(){
		EasySQL sql=getEasySQL("select t1.* from dc_food t1,dc_meals t2  where 1=1 and t1.id=t2.food_id");
		sql.append(param.getString("dinnerType"), "and t1.dinner_type = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="mealsList",type=Types.LIST)
	public JSONObject mealsList(){
		EasySQL sql=getEasySQL("select t2.*,t1.id meal_id from dc_meals t1,dc_food t2 where 1=1 and t1.food_id = t2.id");
		sql.append(param.getString("dinnerType"), "and t2.dinner_type = ?");
		sql.append("order by t2.dinner_type asc,t2.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="mealsDict",type=Types.DICT)
	public JSONObject mealsDict(){
		EasySQL sql=getEasySQL("select t2.id,t2.food_name from dc_meals t1,dc_food t2 where 1=1 and t1.food_id = t2.id");
		sql.append(param.getString("dinnerType"), "and t2.dinner_type = ?");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="bookFoodPlan",type=Types.TEMPLATE)
	public JSONObject bookFoodPlan(){
		EasySQL sql=getEasySQL("select * from dc_reserve_list where 1=1 ");
		sql.append(DateUtils.getPlanToday(), " and ymd > ?");
		sql.append(DateUtils.getPlanMaxDay(14), " and ymd <= ?");
		sql.append(param.getString("dinnerType"), "and dinner_type = ?");
		sql.append(getUserPrincipal().getUserId(), "and user_id = ?");
		JSONObject result=queryForList(sql.getSQL(), sql.getParams());
		result.put("planDays", DateUtils.getBookFoodPlatData(14));
		result.put("week6", week6);
		result.put("week7", week7);
		return result;
	}
	@WebControl(name="settingData",type=Types.TEMPLATE)
	public JSONObject settingData(){
		EasySQL sql=getEasySQL("select * from dc_eat_time where 1=1 ");
		JSONObject result=queryForList(sql.getSQL(), sql.getParams());
		result.put("week6", week6);
		result.put("week7", week7);
		result.put("dcSwitch", dcSwitch);
		return result;
	}
	/**
	 * 今日点餐列表
	 * @return
	 */
	@WebControl(name="todayBookFoodList",type=Types.OTHER)
	public JSONObject todayBookFoodList(){
		EasySQL sql=getEasySQL("select t2.DEPTS,t2.USERNAME,t3.food_name,t1.* from dc_reserve_list t1,"+Constants.DS_MAIN_NAME+".easi_user t2,dc_food t3 where t2.USER_ID=t1.user_id and t3.id=t1.food_list_id  ");
		sql.append(DateUtils.getPlanToday(), " and t1.ymd = ?");
		sql.append(0, " and t1.state = ?");
		sql.append(param.getString("dinnerType"), "and t1.dinner_type = ?");
		sql.append("order by t1.create_time desc");
		JSONObject result=queryForList(sql.getSQL(), sql.getParams());
		return result;
	}
	/**
	 * 本月补点排行榜
	 * @return
	 */
	@WebControl(name="addBookTopList",type=Types.OTHER)
	public JSONObject addBookTopList(){
		EasySQL sql=getEasySQL("select USER_ID,count(1) count  from dc_reserve_list where book_type=2 ");
		sql.append(DateUtils.getTodayYm(), " and ym = ?");
		sql.append("GROUP BY user_id  order by count desc  limit 30 ");
		JSONObject result=queryForList(sql.getSQL(), sql.getParams());
		return result;
	}
	
	@WebControl(name="foodObj",type=Types.RECORD)
	public JSONObject foodObj(){
		return queryForRecord(new EasyRecord("dc_food","ID").setPrimaryValues(param.getString("food.ID")));
	}
	
}
