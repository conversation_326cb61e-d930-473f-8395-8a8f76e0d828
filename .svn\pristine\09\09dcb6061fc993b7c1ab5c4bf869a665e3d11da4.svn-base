<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>版本</title>
	<style>
		img{max-width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="versionEditForm" data-mars="VersionDao.record" autocomplete="off" data-mars-prefix="version.">
     		    <input type="hidden" value="${param.versionId}" id="versionId" name="version.VERSION_ID"/>
     		    <input type="hidden" value="${param.groupId}"  name="version.GROUP_ID"/>
     		    <input type="hidden" value="${param.projectId}"  name="version.PROJECT_ID"/>
     		    <input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
 		   		<input type="hidden" value="${param.versionId}" name="fkId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			             <tr>
			                <td>相关版本文件</td>
			               	<td>
			               		 <div data-template="template-files" data-mars="FileDao.fileList"></div>
			               		 <div id="fileList"></div>
								 <EasyTag:res resId="VERSION_MANAGER">
									 <button class="btn btn-sm btn-default detail" type="button" onclick="$('#localfile').click()">+添加</button>
								 </EasyTag:res>
			               	</td>
	            	    </tr>
			           <tr>
		                    <td style="width: 80px" class="required">版本名称</td>
		                    <td><input data-rules="required" id="versionName" data-mars="CommonDao.date03" type="text" name="version.VERSION_NAME" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td class="required">版本日期</td>
		                    <td><input type="text" data-rules="required" data-rules="required" data-mars="CommonDao.date02" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" name="version.VERION_DATE"  class="form-control input-sm Wdate"></td>
		                </tr>
			            <tr>
		                    <td class="required" style="vertical-align: top;">版本升级描述</td>
		                    <td>
		                          <div id="editor" style="background-color: #f8f8f8;padding: 10px;"></div>
	                          	 <textarea id="wordText" data-text="false" style="height: 500px;width:400px;display: none;" class="form-control input-sm" name="version.VERSION_DESC"></textarea>
		                    </td>
			            </tr>
			        </tbody>
 			     </table>
				 <div class="layer-foot text-c">
				    	  <!-- <button type="button" class="btn btn-success btn-sm" style="display: none;"  onclick="Edit.ajaxSubmitForm(2)"> 审核通过 </button>
				    	  <button type="button" class="btn btn-danger btn-sm ml-10" style="display: none;" onclick="Edit.ajaxSubmitForm(3)"> 审核不通过 </button> -->
				    	  <EasyTag:res resId="VERSION_MANAGER">
					    	   <button type="button" class="btn btn-primary btn-sm m-l0 detail"  onclick="Edit.ajaxSubmitForm(2)"> 保 存 </button>
				    	  </EasyTag:res>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>
  		</form>
		 <script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" class="detail" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Edit.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		
		Edit.versionId='${param.versionId}';
		var versionId='${param.versionId}';
		var op='${param.op}';
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.customConfig.menus = [
      		    'code',  // 代码
      		    'head',  // 标题
      		    'bold',  // 粗体
      		    'fontSize',  // 字号
      		    'fontName',  // 字体
      		    'foreColor',  // 文字颜色
      		    'link',  // 插入链接
      		    'list',  // 列表
      		    'justify',  // 对齐方式
      		    'quote',  // 引用
      		    'emoticon',  // 表情
      		    'image',  // 插入图片
      		    'table',  // 表格'
      	];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.customConfig.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.create();
		
		$(function(){
			$("#versionEditForm").render({success:function(result){
				 editor.txt.html($("#wordText").val());
				 var appName='${param.appName}';
				 $("#versionName").val(appName+"#"+$("#versionName").val());
				 if(op=='detail'){
					 $("#editor").html($("#wordText").val());
					 $("#versionEditForm input").attr("readonly","readonly").removeAttr("onClick");
					 $(".detail").remove();
				 }
				 $(".w-e-text-container").css("height","120px");
			}});
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#versionEditForm")){
				if(Edit.versionId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.uploadFile = function(){
			var fkId='';
			if(Edit.versionId){
				fkId=versionId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'callback',fkId:fkId,source:'version',fileMaxSize:1024*50});
			
		}
		var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
		}
		Edit.insertData = function() {
			$("#versionId").val($("#randomId").val());
			var data = form.getJSONObject("#versionEditForm");
			ajax.remoteCall("${ctxPath}/servlet/version?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#versionEditForm");
			ajax.remoteCall("${ctxPath}/servlet/version?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>