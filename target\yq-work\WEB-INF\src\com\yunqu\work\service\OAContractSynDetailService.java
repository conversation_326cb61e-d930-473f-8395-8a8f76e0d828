package com.yunqu.work.service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ProjecContractModel;

public class OAContractSynDetailService extends AppBaseService{
	
	private Logger logger = LogEngine.getLogger(Constants.APP_NAME);
	
	private static Map<String, String> depts = null;
	
	private static class Holder{
		private static OAContractSynDetailService service=new OAContractSynDetailService();
	}
	public static OAContractSynDetailService getService(){
		return Holder.service;
	}
	
	public OAContractSynDetailService() {
		depts = getDepts();
	}
	
	private Map<String,String> getDepts() {
		Map<String,String> depts=new HashMap<String,String>();
		try {
			List<EasyRow> list=this.getMainQuery().queryForList("select * from easi_dept");
			if(list!=null){
				for(EasyRow row:list){
					depts.put(row.getColumnValue("DEPT_NAME"),row.getColumnValue("DEPT_ID"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return depts;
	}

	private String getSelectText(Elements els) {
		for(Element el:els) {
			if(el.hasAttr("selected")) {
				return el.text();
			}
		}
		return "";
	}
	
	public void synData(String id){
		
		Map<String,String> maps = OAService.getService().getUserCookie("100078");
		if(maps==null) {
			OAService.getService().login("100078","10304603");
		}
		
		
		Document doc = OAService.getService().loadContractDetail("100078",id);
		logger.info("id>"+id);
		if(doc==null) {
			return;
		}
//		logger.info("result>"+doc.html());
		logger.info("==============================================");
		String code = doc.selectFirst("[name='T_HeTongHao']").val();
		logger.info(code);
		if(StringUtils.isBlank(code)) {
			logger.info(id+"合同没获取到");
			return;
		}
		String Customer = doc.selectFirst("[name='Customer']").val();
		logger.info(Customer);
		String CUST_NO = doc.selectFirst("[name='CustomerID']").val();
		String SIGN_DATE = doc.selectFirst("[name='Date_1']").val();
		String date = doc.selectFirst("[name='Date']").val();
		if(StringUtils.isBlank(SIGN_DATE)) {
			SIGN_DATE = date;
		}
		String Subject = doc.selectFirst("[name='Subject']").val();
		String SALE_DEPT_ID = doc.selectFirst("[name='department_show']").val();
		String SALES_BY = doc.selectFirst("[name='Contractor']").val();
		String Receiptor = doc.selectFirst("[name='Receiptor']").val();
		String aProductManager = doc.selectFirst("[name='aProductManager']").val();
//		 aProductManager = doc.getElementsByAttributeValue("name", "aProductManager").first().attr("value");
		String SensorNo = doc.selectFirst("[name='SensorNo']").val();
		String CustomerContractID = doc.selectFirst("[name='CustomerContractID']").val();
		String CONTRACT_TYPE = getSelectText(doc.selectFirst("[name='aContractType']").select("option"));
		String TXProdLine = getSelectText(doc.selectFirst("[name='TXProdLine']").select("option"));
		String Money = doc.selectFirst("[name='Money']").val();
		String SIGN_OFFICE = getSelectText(doc.selectFirst("[name='type']").select("option"));
		String CONTRAC_DESC = doc.selectFirst("[name='ViewAll']").val();
		String ERP_code = doc.selectFirst("[name='ERP_code']").val();
		String year = doc.selectFirst("[name='year']").text();
		String CheckDD = doc.selectFirst("[name='CheckDD']").text();
		
		ProjecContractModel contractModel=new ProjecContractModel();
		contractModel.setPrimaryValues(id);
		contractModel.set("CONTRACT_NAME", Subject);
		contractModel.set("CONTRACT_NO", code);
		contractModel.set("CUSTOMER_NAME", Customer);
		contractModel.set("CUST_NO", CUST_NO);
		contractModel.set("ERP_CONTRACT_NO", ERP_code);
		contractModel.set("REVIEW_ID", SensorNo);
		contractModel.set("AMOUNT", Money);
		contractModel.set("YEAR", year);
		if("未正式签订".equals(CheckDD)) {
			contractModel.set("SIGN_FLAG", 1);
		}else {
			contractModel.set("SIGN_FLAG", 0);
		}
		contractModel.set("CUST_CONTRACT_NO", CustomerContractID);
		contractModel.set("SIGN_DATE", SIGN_DATE);
		contractModel.set("CONTRACT_TYPE", CONTRACT_TYPE);
		contractModel.set("PROD_LINE", TXProdLine);
		contractModel.set("CONTRAC_DESC", CONTRAC_DESC);
		contractModel.set("SIGN_OFFICE", SIGN_OFFICE);
		contractModel.set("SALE_DEPT_ID", depts.get(SALE_DEPT_ID));
//		contractModel.set("SALES_BY", StaffService.getService().getUserId(SALES_BY));
//		contractModel.set("PAYEE", StaffService.getService().getUserId(Receiptor));
//		contractModel.set("PM_BY", StaffService.getService().getUserId(aProductManager));
		
		JSONObject ext = new JSONObject();
		ext.put("SALES_BY_NAME",SALES_BY);
		ext.put("PAYEE_NAME",Receiptor);
		ext.put("PM_BY_NAME",aProductManager);
		ext.put("SALE_DEPT_NAME", SALE_DEPT_ID);
		contractModel.set("TEXT_JSON",ext.toJSONString());
		
		try {
			contractModel.update();
		} catch (SQLException ex) {
			ex.printStackTrace();
			logger.error(null, ex);
			return;
		}
		if(contractModel!=null) {
			return;
		}
//		Connection jsoup = Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/WF_RunRule?openagent&RuleNum=RU016_001&WF_DocUNID="+id+"&WF_ProcessUNID=F_App006_003&Nodeid=&indexnum=1&AclName=File&DocAction=edit&ISUIDB=1&FormNumber=F_App006_003").timeout(10000);
//		jsoup.cookies(maps);
//		try {
//			Document d= jsoup.get();
//			if(d==null) {
//				return;
//			}
//			if(!d.hasText()) {
//				return;
//			}
//			Elements elements = d.select("a");
//			
//			if(elements!=null&&elements.size()>0) {
//				for(int i=0;i<elements.size();i++) {
//					Element  e = elements.get(i);
//					String url  = e.attr("href");
//					if(StringUtils.notBlank(url)) {
//						String text = e.text();
//						String title = e.attr("title");//本附件由陈丹莉在2019-10-10 17:58添加
//						url="http://yqoa.yunqu-info.com/"+url;
//						try {
//							jsoup=Jsoup.connect(url).timeout(90000);
//							jsoup.cookies(maps);
//							Response  fileResp=jsoup.ignoreContentType(true).execute();
//							String filename = text.substring(0,text.lastIndexOf("("));
//							
//							String fileId=RandomKit.uuid();
//							
//							String hz= FileKit.getHouzui(filename);
//							File file=new File(getDir()+"contract"+File.separator+fileId+hz);
//							
//							FileOutputStream out = (new FileOutputStream(file));
//							out.write(fileResp.bodyAsBytes());
//							out.close();
//							
//							try {
//								Thread.sleep(600);
//							} catch (InterruptedException e4) {
//								e4.printStackTrace();
//							}
//							try {
//								List<JSONObject> ls = this.getQuery().queryForList("select disk_path from yq_files where fk_id = ?", new Object[] {id},new JSONMapperImpl());
//								if(ls!=null&&ls.size()>0) {
//									for(JSONObject j:ls) {
//										String path = j.getString("DISK_PATH");
//										File f=new File(path);
//										if(f.exists()) {
//											f.delete();
//										}
//									}
//								}
//							} catch (SQLException e3) {
//								e3.printStackTrace();
//							}
//							
//							try {
//								this.getQuery().executeUpdate("delete from yq_files where fk_id = ? ",id);
//							} catch (SQLException e2) {
//								e2.printStackTrace();
//							}
//							
//							FileModel model=new FileModel();
//							model.setFileId(fileId);
//							model.set("create_time",title.substring(title.indexOf("在")+1,title.indexOf("添加")));
//							model.setFileName(filename);
//							String userName= title.substring(title.indexOf("由")+1,title.indexOf("在"));
//							model.setCreator(users2.get(userName));
//							model.setAccessUrl("/yq-work/files?url=/contract/"+fileId+hz);
//							model.setDiskPath(file.getAbsolutePath());
//							model.setFileType(FileKit.getHouzui(filename));
//							model.setFkId(id);
//							model.setSource("contract");
//							
//							
//							model.setFileLenth(file.length());
//							model.setFileSize(getPrintSize(file.length()));
//							try {
//								model.save();
//							} catch (SQLException e1) {
//								e1.printStackTrace();
//								logger.error(null, e1);
//							}
//							
//						} catch (IOException e1) {
//							e1.printStackTrace();
//							logger.error(null, e1);
//						}           
//					}
//				}
//			}
//		} catch (IOException e2) {
//			e2.printStackTrace();
//			logger.error(null, e2);
//		}
	}
	
	public void synReviewData(String id){
		Map<String,String> maps = OAService.getService().getUserCookie("admin");
		if(maps==null) {
			OAService.getService().login("admin","Yq!@#.123");
		}
		Document doc = OAService.getService().loadContractReviewDetail("admin",id);
		logger.info("id>"+id);
		if(doc==null) {
			return;
		}
		logger.info("==============================================");
//		logger.info("result>"+doc.html());
		String SensorNo = doc.selectFirst("[name='SensorNo']").val();
		logger.info(SensorNo);
		if(StringUtils.isBlank(SensorNo)) {
			logger.info(id+"合同没获取到");
			return;
		}
		
		EasyRecord record = new EasyRecord("yq_flow_apply","apply_id");
		record.setPrimaryValues(id);
		record.set("apply_id", id);
		record.set("apply_no", SensorNo);
		record.set("apply_state",30);
		record.set("flow_code", "contract_review");
		record.set("apply_time", doc.selectFirst("[name='CreatedTime']").val());
		record.set("apply_name", doc.selectFirst("[name='HandlePeople']").val());
		record.set("apply_title", doc.selectFirst("[name='aSubject']").val());
		record.set("apply_remark", doc.selectFirst("[name='aBeizhu']").val());
		
		EasyRecord revise = new EasyRecord("yq_flow_revise_record","revise_id");
		revise.setPrimaryValues(id);
		revise.set("business_id",id);
		revise.set("add_time",EasyDate.getCurrentDateString());
		revise.set("revise_content", doc.selectFirst("[name='Log']").val());
		try {
			this.getQuery().save(revise);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		
		EasyRecord review = new EasyRecord("yq_contract_review","business_id");
		review.setPrimaryValues(id);
		review.set("contract_name", doc.selectFirst("[name='aSubject']").val());
		review.set("cust_no", doc.selectFirst("[name='CustomerID']").val());
		review.set("cust_name", doc.selectFirst("[name='Customer']").val());
		review.set("prod_line", getSelectText(doc.selectFirst("[name='ProductLine']").select("option")));
		review.set("sign_dept_name", doc.selectFirst("[name='aSign_Dept_show']").val());
		review.set("pre_sales_name", doc.selectFirst("[name='aProductManager_CN']").val());
		review.set("sales_by_name", doc.selectFirst("[name='aSaleManager_CN']").val());
		review.set("pay_type", doc.selectFirst("[name='aFukuanfangshi']").val());
		review.set("wb_time", doc.selectFirst("[name='aBaoxiuqi']").val());
		review.set("up_sales_name", doc.selectFirst("[name='aSaleLeader']").val());
		review.set("amount", doc.selectFirst("[name='aMoneyTotal']").val());
		
		try {
			if(!this.getQuery().update(record)) {
				this.getQuery().save(record);
			}
			if(!this.getQuery().update(review)) {
				this.getQuery().save(review);
			}
		} catch (SQLException ex) {
			ex.printStackTrace();
			LogEngine.getLogger("review").error(null, ex);
			return;
		}
		if(record!=null) {
			return;
		}
	}
	
	
	 public static String getDir() {
	   //String saveDirectory = Globals.WEBAPPS_DIR+File.separator+"ROOT"+File.separator+"files"+File.separator;
		String basePath = AppContext.getContext(Constants.APP_NAME).getProperty("basePath", "/home/<USER>/");
		return basePath+"files/";
	}
	 
	private String getPrintSize(long size) {
		// 如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
		double value = (double) size;
		if (value < 1024) {
			return String.valueOf(value) + "B";
		} else {
			value = new BigDecimal(value / 1024).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
		}
		// 如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
		// 因为还没有到达要使用另一个单位的时候
		// 接下去以此类推
		if (value < 1024) {
			return String.valueOf(value) + "KB";
		} else {
			value = new BigDecimal(value / 1024).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
		}
		if (value < 1024) {
			return String.valueOf(value) + "MB";
		} else {
			// 否则如果要以GB为单位的，先除于1024再作同样的处理
			value = new BigDecimal(value / 1024).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
			return String.valueOf(value) + "GB";
		}
	}
	
	public void synData(){
		OAService.getService().login("100078","10304603");
        try {
			List<JSONObject> list = this.getQuery().queryForList("select CONTRACT_ID from yq_project_contract ORDER BY CREATE_TIME desc limit 10",new Object[]{},new JSONMapperImpl());
			for(JSONObject json:list) {
				String id = json.getString("CONTRACT_ID");
				synData(id);
			}
			
		} catch (SQLException e) {
			e.printStackTrace();
		}
          
     }
	
}







