package com.yunqu.work.servlet;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.RemindModel;
import com.yunqu.work.service.CommentService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.WxMsgService;
/**
 * 通知公告
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/remind/*")
public class RemindServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		RemindModel model=getModel(RemindModel.class, "remind");
		if(StringUtils.isBlank(model.getString("REMIND_ID"))){
			model.setPrimaryValues(RandomKit.uniqueStr());
		}
		model.addCreateTime();
		model.setCreator(getRemoteUser());
		try {
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		RemindModel model=getModel(RemindModel.class, "remind");
		try {
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateViewCount(){
		String sql="update yq_remind set view_count=view_count +1 where remind_id = ?";
		try {
			this.getQuery().executeUpdate(sql, getJsonPara("remindId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForSaveComment(){
		JSONObject jsonObject=getJSONObject();
		if(StringUtils.notBlank(jsonObject.getString("content"))&&StringUtils.notBlank(jsonObject.getString("fkId"))){
			return CommentService.getService().addComment(getUserId(),getUserName(),jsonObject.getString("fkId"),jsonObject.getString("content"),WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"),"remind");
		}else{
			return EasyResult.fail();
		}
	}
	public EasyResult actionForDelete(){
		RemindModel model=getModel(RemindModel.class, "remind");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	
	public EasyResult actionForNotice() {
	  JSONObject params = getJSONObject();
	  int type = params.getIntValue("type");
	  String remindId = params.getString("remindId");
	  Record record = Db.findFirst("select * from yq_remind where remind_id = ?", remindId);
	  MessageModel model = new MessageModel();
	  model.setFkId(remindId);
	  model.setTitle(record.getStr("title"));
	  
	  Map<String, String>  map = new HashMap<String,String>();
	  map.put("1", "日常公告");
	  map.put("2", "新闻动态");
	  map.put("3", "每周菜单");
	  map.put("0", "动弹");
	  
	  model.setData1(map.get(record.getStr("remind_type")));
	  model.setData2(record.getStr("publish_time"));
	  
	  String summary = record.getStr("summary");
	  if(StringUtils.notBlank(summary)) {
		  model.setDesc(summary);
	  }else {
		  model.setDesc("请点击查看详情");
	  }
	  if(type==0) {
		  model.setOpenId(StaffService.getService().getStaffInfo(getUserId()).getOpenId());
		  WxMsgService.getService().sendRemindNoticeMsg(model);
	  }else {
		  try {
			List<JSONObject> list = this.getMainQuery().queryForList("select user_id,open_id from easi_user where state = ?", new Object[] {0}, new JSONMapperImpl());
			for(JSONObject row:list) {
				model.setOpenId(row.getString("OPEN_ID"));
				WxMsgService.getService().sendRemindNoticeMsg(model);
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
	  }
	  return EasyResult.ok();
	}
	
	public EasyResult actionForYearReport() {
		String type = getPara("type");
		MessageModel model = new MessageModel();
		model.setUrl("http://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/h5/2023");
		model.setTitle("‼️你的2023专属报告已送达！");
		model.setData1("年度报告");
		model.setData2(EasyDate.getCurrentDateString());
		model.setDesc("请点击查看详情");
		
		if("1".equals(type)) {
			try {
				List<JSONObject> list = this.getMainQuery().queryForList("select user_id,open_id from easi_user where state = ?", new Object[] {0}, new JSONMapperImpl());
				for(JSONObject row:list) {
					model.setOpenId(row.getString("OPEN_ID"));
					WxMsgService.getService().sendRemindNoticeMsg(model);
					try {
						Thread.sleep(500);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
		}else {
			model.setOpenId(StaffService.getService().getStaffInfo(getUserId()).getOpenId());
			WxMsgService.getService().sendRemindNoticeMsg(model);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForFaCheckMsg() {
		String type = getPara("type");
		MessageModel model = new MessageModel();
		model.setUrl("http://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/fa/check");
		model.setTitle("‼️固定资产盘点！");
		model.setData1("固定资产盘点,请尽快上报");
		model.setData2(EasyDate.getCurrentDateString());
		model.setDesc("请点击查看详情");
		
		int periodNum = 0 ;
		try {
			periodNum = this.getQuery().queryForInt("select max(period_num) from yq_fa_check");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			periodNum = 0;
		}
		if(periodNum==0) {
			return EasyResult.fail();
		}
		
		if("1".equals(type)) {
			try {
				List<JSONObject> list = this.getQuery().queryForList("select t1.user_id,t1.open_id from "+Constants.DS_MAIN_NAME+".easi_user t1,yq_fa_check t2 where t1.state = ? and t2.fa_user_id = t1.USER_ID and t2.PERIOD_NUM = ? GROUP BY t1.USER_ID", new Object[] {0,periodNum}, new JSONMapperImpl());
				for(JSONObject row:list) {
					model.setOpenId(row.getString("OPEN_ID"));
					WxMsgService.getService().sendFaCheckMsg(model);
					try {
						Thread.sleep(500);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
		}else {
			String userId = getPara("userId");
			if(StringUtils.isBlank(userId)) {
				userId = getJsonPara("userIds");
				if(StringUtils.isBlank(userId)) {
					userId = getUserId();
				}
			}
			String[] array = userId.split(",");
			
			for(String id:array) {
				model.setSender(getUserId());
				model.setReceiver(id);
				model.setOpenId(StaffService.getService().getStaffInfo(id).getOpenId());
				WxMsgService.getService().sendFaCheckMsg(model);
			}
			
			for(String id:array) {
				model.setTitle("请尽快填报固定资产盘点");
				model.setSender(getUserId());
				model.setReceiver(id);
				model.setDesc("请尽快填报固定资产盘点，<br>外网：https://work.yunqu-info.cn/yq-work/fa/check,<br>内网：http://172.16.68.152/yq-work/fa/check<br><br>系统发送，请勿回复。");
				EmailService.getService().sendEmail(model);
			}
		}
		return EasyResult.ok();
	}
}





