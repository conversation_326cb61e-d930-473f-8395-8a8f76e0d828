<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div class="container-fluid">
 <table class="table table-edit table-vzebra">
   	<tr>
   		<td style="width: 80px;">评审结果</td>
   		<td>
   			<label class="radio radio-inline radio-success">
            	<input type="radio" value="1" name="checkResult" checked="checked"> <span>通过</span>
            </label>
            <label class="radio radio-inline radio-success">
            	<input type="radio" value="2" name="checkResult"> <span>拒绝</span>
            </label>
   		</td>
   	</tr>
   	<tr>
   		<td style="vertical-align: top;margin-top: 20px;">评审描述</td>
   		<td>
   			<textarea style="height: 120px" name="checkDesc" class="form-control"></textarea>
   		</td>
   	</tr>
  </table>
    <p class="layer-foot text-c">
   		<button class="btn btn-info btn-sm" type="button" onclick="addCheckOrder();">提交</button>
    </p>
</div>
<script type="text/javascript">
	function addCheckOrder(){
		var checkResult = $("[name='checkResult']:checked").val();
		var checkDesc = $("[name='checkDesc']").val();
		var businessId = $("#businessId").val();
		var resultId = $("#resultId").val();
		var nodeId = $("#nodeId").val();
		if(businessId==''||nodeId==''){
			layer.msg('请从审批页面入口进来',{icon:7,offset:'50px'});
			return;
		}
		if(checkResult==undefined){
			layer.msg('请选择评审结果',{icon:7,offset:'50px'});
			return;
		}
		if(checkResult=='2'&&checkDesc==''){
			layer.msg('请输入评审拒绝理由',{icon:7,offset:'50px'});
			return;
		}else{
			if(checkDesc==''){
				checkDesc='同意';
			}
		}
		var data = {checkResult:checkResult,checkDesc:checkDesc,orderId:orderId,businessId:businessId,resultId:resultId,nodeId:nodeId};
		ajax.remoteCall("${ctxPath}/servlet/order/flow?action=addCheck",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:1200},function(){
					popup.closeTab();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
</script>