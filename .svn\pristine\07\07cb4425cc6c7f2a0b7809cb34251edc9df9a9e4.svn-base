<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="projectEditForm" class="form-horizontal" data-mars="ProjectDao.record" autocomplete="off" data-mars-prefix="project.">
 		   		<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
 		   		<input type="hidden" id="projectId" value="${param.projectId}" name="project.PROJECT_ID"/>
 		   		<input type="hidden" value="${param.projectId}" name="fkId"/>
 		   		<div class="ibox-content" style="padding: 15px;">
				   <div class="layui-tab layui-tab-brief">
					  <ul class="layui-tab-title">
					    <li class="layui-this">基本信息</li>
					    <li>更多</li>
					  </ul>
					  <div class="layui-tab-content">
					    <div class="layui-tab-item layui-show">
							<table class="table table-edit table-vzebra">
						        <tbody>
						            <tr>
					                    <td width="80px" class="required">项目名称</td>
					                    <td style="width: 40%"><input data-rules="required"  type="text" name="project.PROJECT_NAME" class="form-control input-sm"></td>
					                    <td width="80px" class="required">合同编号</td>
					                    <td style="width: 40%"><input placeholder="若无填写0"  data-rules="required"  type="text" name="project.PROJECT_NO" id="projectNo" class="form-control input-sm"></td>
						            </tr>
						            <tr>
					                    <td>项目经理</td>
					                    <td>
					                    	<input name="project.PROJECT_PO" type="hidden">
					                    	<input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="project.PROJECT_PO_NAME"/>
					                    </td>
					                    <td>开发负责人</td>
					                    <td>
					                    	<input name="project.PO" type="hidden">
					                    	<input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="project.PO_NAME"/>
					                    </td>
					                </tr>
						            <tr>
					                    <td class="required">项目起止时间</td>
					                    <td colspan="3">
						                    <input id="beginDate" type="text" style="display: inline-block;width: 120px" placeholder="开工日期"  onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.BEGIN_DATE"  class="form-control input-sm Wdate">
						                    <input id="endDate" type="text" style="display: inline-block;width: 120px" placeholder="计划完成时间" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" id="orderFinishTime" name="project.END_DATE" class="form-control input-sm Wdate">
					                    	<div class="btn-group">
					                   			<button onclick="fillDate(30)" class="btn btn-sm btn-default" type="button">1个月</button>
					                   			<button onclick="fillDate(60)" class="btn btn-sm btn-default" type="button">2个月</button>
					                   			<button onclick="fillDate(90)" class="btn btn-sm btn-default" type="button">3个月</button>
					                   			<button onclick="fillDate(180)" class="btn btn-sm btn-default" type="button">半年</button>
					                   			<button onclick="fillDate(365)" class="btn btn-sm btn-default" type="button">一年</button>
					                   		</div>
					                    </td>
					                </tr>
						            <tr>
					                    <td class="required">初验日期</td>
					                    <td>
						                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.CY_DATE"  class="form-control input-sm Wdate">
					                    </td>
					                    <td class="required">终验日期</td>
					                    <td>
						                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.ZY_DATE"  class="form-control input-sm Wdate">
					                    </td>
					                </tr>
						            <tr>
					                    <td class="required">上线日期</td>
					                    <td>
						                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.SX_DATE"  class="form-control input-sm Wdate">
					                    </td>
					                </tr>
					                <tr>
					                	<td class="required">公开性</td>
					                    <td colspan="3">
											<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="0" checked="checked" name="project.PROJECT_PUBLIC"/> <span>公开·企业全员可访问</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="1" name="project.PROJECT_PUBLIC"/> <span>私密·仅成员可访问</span>
					                    	</label>
					                    </td>
					                </tr>
					                <tr>
					                	<td class="required">项目阶段</td>
					                    <td colspan="3">
											<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="实施中" checked="checked" name="project.PROJECT_STAGE"/> <span>实施中</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="上线" name="project.PROJECT_STAGE"/> <span>上线</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="初验" name="project.PROJECT_STAGE"/> <span>初验</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="终验" name="project.PROJECT_STAGE"/> <span>终验</span>
					                    	</label>
					                    </td>
					                </tr>
					                <tr>
						              	<td>
						            		项目类型
						            	</td>
						            	<td colspan="3">
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="20" checked="checked" name="project.PROJECT_TYPE"/> <span>自研项目</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="10" name="project.PROJECT_TYPE"/> <span>合同项目</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="11" name="project.PROJECT_TYPE"/> <span>提前执行</span>
					                    	</label>
						            		<label class="radio radio-info radio-inline">
				                    			<input type="radio" value="30" name="project.PROJECT_TYPE"/> <span>维保项目</span>
					                    	</label>
						            	</td>
						            </tr>
					                <tr>
						              	<td>
						            		项目进度
						            	</td>
						            	<td>
						            		<select disabled="disabled" class="form-control input-sm" name="project.PROJECT_STATE">
						            			<option value="11">进度正常</option>
						            			<option value="12">存在风险</option>
						            			<option value="13">进度失控</option>
						            			<option value="20">挂起中</option>
						            			<option value="30">已完成</option>
						            		</select>
						            	</td>
						            	<td class="required">项目级别</td>
					                    <td>
					                    	<select class="form-control input-sm"" name="project.PROJECT_LEVEL">
					                    		<option value="1">正常</option>
					                    		<option value="2">紧急</option>
					                    		<option value="3">重要</option>
					                    		<option value="4">重要且紧急</option>
					                    	</select>
					                    </td>
						            </tr>
						            <tr>
						            	<td class="required">描述</td>
						               	<td colspan="3">
				                           <textarea id="wordText" style="height: 70px;" class="form-control input-sm" name="project.PROJECT_DESC"></textarea>
						               	</td>
						            </tr>
						            <tr>
						            	<td class="required">公告</td>
						               	<td colspan="3">
				                           <textarea style="height: 70px;" class="form-control input-sm" name="project.PROJECT_NOTICE"></textarea>
						               	</td>
						            </tr>
						        </tbody>
			 				</table>
			 				<p class="text-c mt-10" style="z-index: 999999999">
						    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="Project.ajaxSubmitForm()"> 保 存 </button>
							</p>
					    </div>
					    <div class="layui-tab-item">
					    	<br><br>--<br><br><br>
					    </div>
					  </div>
					</div>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
   
		jQuery.namespace("Project");
	    
		Project.projectId='${param.projectId}';
		var projectId='${param.projectId}';
		
		$(function(){
		    $('[data-toggle="tooltip"]').tooltip();
			$("#projectEditForm").render({success:function(result){
				
			}});  
		});
		
		Project.ajaxSubmitForm = function(){
			if(form.validate("#projectEditForm")){
				if(Project.projectId){
					Project.updateData(); 
				}else{
					Project.insertData(); 
				}
			};
		}
		Project.insertData = function(flag) {
			$("#projectEditForm #projectId").val($("#randomId").val());
			var data = form.getJSONObject("#projectEditForm");
			ajax.remoteCall("${ctxPath}/servlet/project?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Project.updateData = function(flag) {
			var data = form.getJSONObject("#projectEditForm");
			ajax.remoteCall("${ctxPath}/servlet/project?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function fillDate(val){
			var v = addDate(val);
			if(v.indexOf("NaN")>-1){
				layer.msg("时间格式不正确,请检查浏览器兼容性或者换chome");
			}else{
				$("#endDate").val(v);
			}
		}
		function addDate(days) {
            if (days == undefined || days == '') {
                days = 1;
            }
            var d=$("#beginDate").val();
            if(d){
	            var date = new Date(d.replace(/-/g,'/'));
	            date.setDate(date.getDate() + days);
	            var month = date.getMonth() + 1;
	            var day = date.getDate();
	            var h = date.getHours();
	            var m = date.getMinutes();
	            return date.getFullYear() + '-' + getFormatDate(month) + '-' + getFormatDate(day);
            }else{
            	return "";
            }
        }
		function getFormatDate(arg) {
            if (arg == undefined || arg == '') {
                return '';
            }
            var re = arg + '';
            if (re.length < 2) {
                re = '0' + re;
            }
            return re;
        }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>