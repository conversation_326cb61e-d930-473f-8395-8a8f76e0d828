<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购详情</title>
	<style>
		.form-horizontal{width: 100%;}
		.layui-table-cell a{font-size: 12px;color: #3E84E9;cursor: pointer;}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 14px;}
		.layui-timeline-item:hover a{opacity:1;}
		.layui-tab-card>.layui-tab-title{background-color: #fff;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		.layui-timeline-content p{font-size: 13px;color: #666;}
		.layui-timeline-title{margin-bottom: 2px;}
		.layui-timeline-item{margin-bottom: 2px;}
		.layui-text h3{font-size: 16px;}
		.layui-tab-title li{padding: 0px 12px;}
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.gray-bg{background-color: #e8edf7;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
		.reviewBtn,.paymentBtn,#createReviewMsg{display: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="OrderDetailForm">
			    <input type="hidden" value="${param.orderId}" id="orderId" name="orderId"/>
 	  	        <input type="hidden" value="${param.orderId}" name="pFkId"/>
 	  	         <input type="hidden" id="goodsId"/>
 	  	        <input type="hidden" value="${param.custId}" name="custId"/>
 	  	        <input type="hidden" value="${param.resultId}" id="resultId" name="resultId"/>
				<div style="padding: 5px" class="ibox-content" data-mars="OrderDao.applyInfo" data-mars-prefix="apply.">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md8">
		 	  	         <input type="hidden" id="supplierId" name="apply.SUPPLIER_ID"/>
		 	  	         <input type="hidden" value="${param.reviewId}" id="reviewId" name="apply.REVIEW_ID"/>
		 	  	         <input type="hidden" value="${param.paymentId}" id="paymentId" name="apply.PAYMENT_ID"/>
		 	  	         <input type="hidden" value="${param.orderState}" id="orderState" name="apply.ORDER_STATE"/>
					     <i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 18px;" id="orderTitle" name='apply.ORDER_TITLE'></span>
					  </div>
					  <div class="layui-col-md4">
						  		<div class="pull-right mr-50">
							  		<div class="btn-group btn-group-sm">
							  			<yq:user userId="${param.applyBy}">
											<button class="btn btn-default btn-xs" onclick="editOrder();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
											<button class="btn btn-default btn-xs reviewBtn" onclick="applyReview();" type="button"><i class="fa fa-plus" aria-hidden="true"></i> 采购评审</button>
							  			</yq:user>
					  					<EasyTag:hasRole roleId="ERP_ORDER_APPLY_PAYMENT">
											<button class="btn btn-default btn-xs paymentBtn" onclick="applyPay();" type="button"><i class="fa fa-plus" aria-hidden="true"></i> 付款申请</button>
					  					</EasyTag:hasRole>
									</div>
									<div class="btn-group btn-group-sm">
										<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">•••</button>
										<ul class="dropdown-menu dropdown-menu-right">
										    <li><a href="javascript:;" onclick="addFollow()">新增跟进记录</a></li>
										    <li><a href="javascript:;" onclick="cannelOrder()">取消采购</a></li>
										    <li><a href="javascript:;" onclick="stopOrder()">终止采购</a></li>
										</ul>
									</div>
						  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding:10px 15px;">
						  <div class="layui-col-md12">
						  		<jsp:include page="include/base-info.jsp"/>
						  </div>
					</div>
				</div>
				<div class="layui-row">
					<div class="layui-col-md9">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">物料信息(<span id="goodsCount">0</span>)</li>
						    <li>入库记录(<span id="stockInCount">0</span>)</li>
						    <li>出库记录(<span id="stockOutCount">0</span>)</li>
						    <li>退货记录(<span id="stockReturnCount">0</span>)</li>
						    <li>附件列表(<span id="fileCount">0</span>)</li>
						    <li>评审记录(<span id="checkCount">0</span>)</li>
						    <li>付款申请(<span id="paymentCount">0</span>)</li>
						    <li>发票管理(<span id="invoiceCount">0</span>)</li>
						    <li>付款方式(<span id="payTypeCount">0</span>)</li>
						  </ul>
						  <div class="layui-tab-content" style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
						    <div class="layui-tab-item layui-show">
						    	<jsp:include page="include/goods-mgr.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/storage-in.jsp">
						    		<jsp:param value="10" name="type"/>
						    	</jsp:include>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/storage-out.jsp">
						    		<jsp:param value="20" name="type"/>
						    	</jsp:include>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/storage-return.jsp">
						    		<jsp:param value="30" name="type"/>
						    	</jsp:include>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/file-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
					    		<table class="table table-vzebra" data-mars="OrderFlowDao.reviewInfo" data-mars-prefix="review.">
								  <tbody>
								  	<tr>
									  	<td style="width: 90px;">发起人</td>
									  	<td>
										  	<span class="h-value text-info" name="review.CREATE_NAME"></span>
									  	</td>
									  	<td style="width: 90px;">发起时间</td>
										<td>
									  		<span class="h-value text-info" name="review.APPLY_TIME">--</span>
									 	</td>
									  	<td style="width: 90px;">审批人</td>
									  	<td>
									  		<span  class="h-value text-info" name="review.CHECK_NAMES"></span>
									  	</td>
									 </tr>
									 <tr>
									  	<td>备注</td>
									  	<td colspan="5">
									  		<span class="h-value text-info" name="review.REMARK"></span>
									  	</td>
									</tr>
								  </tbody>
								</table>
						    	<jsp:include page="review/review-check-list.jsp"/>
						    	<button class="btn btn-sm btn-info ml-5" type="button" onclick="reviewCb();"> 催办 </button>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="payment/order-payment-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="invoice/invoice-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="pay/type/type-list.jsp"/>
						    </div>
						  </div>
					</div>
					</div>
					<div class="layui-col-md3">
						<div class="layui-card" style="margin: 10px 0px 15px 5px;border: 1px solid #eee;">
						  <div class="layui-card-header" style="background-color: #fff;border-bottom: 1px solid #eee;">动态
							 <button type="button" class="btn btn-xs btn-default pull-right mt-10" onclick="addFollow()">+新建</button>
						  </div>
						  <div class="layui-card-body followList" style="height: calc( 100vh - 180px);overflow-y: scroll;">
								<jsp:include page="include/follow-list.jsp"></jsp:include>
						  </div>
						</div>
					</div>
				</div>
		</form>
		<form  id="orderDetailFileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="orderDetailLocalfile" onchange="orderUploadFile()"/>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   
		jQuery.namespace("OrderDetail");
	    
		OrderDetail.orderId='${param.orderId}';
		
		var orderId = '${param.orderId}';
		
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
				var form = layui.form;
				form.render();
			});
			$("#OrderDetailForm").render({success:function(result){
				var reviewId = $('#reviewId').val();
				if(reviewId){
					$('.reviewBtn').remove();
					$('.paymentBtn').show();
				}else{
					$('.reviewBtn,#createReviewMsg').show();
				}
				var orderState = $("#orderState").val();
				if(orderState=='90'||orderState=='99'){
					var style=document.createElement('style');
					style.type='text/css';
					if(style.styleSheet){
					    style.styleSheet.cssText='button{display:none!important;}';
					}else{
					    style.appendChild(document.createTextNode('button{display:none!important;}'));
					}
					document.getElementsByTagName('head')[0].appendChild(style);
				}
				
				var newOrder = '${param.newOrder}';
				var ts = '${param.ts}';
				var _ts = Date.parse(new Date());
				var calcTs = _ts-ts;
				if(newOrder=='1'&&calcTs<3000){
					$('.layui-tab-title li').first().click();
					addGoods();
				}
			}});  
		});
		
		
		function addFollow(){
			popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'新增跟进',data:{orderId:orderId}});
			
		}
		
		function applyReview(){
			var applyName  = $('#orderTitle').text()+"-采购合同评审";
			popup.layerShow({id:'applyReview',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/review/apply-review.jsp',title:'发起评审',data:{orderId:orderId,applyName:applyName}});
		}
		
		function applyPay(){
			var supplierId = $("#supplierId").val();
			popup.layerShow({id:'applyPayment',full:true,scrollbar:false,area:['750px','600px'],offset:'20px',url:'${ctxPath}/pages/erp/order/payment/apply-payment.jsp',title:'发起付款申请',data:{supplierId:supplierId,orderIds:orderId}});
		}
		
		function editFollow(followId){
			popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'修改跟进',data:{orderId:orderId,followId:followId}});
			
		}
		function moreFollow(){
			popup.openTab({id:'orderFollowList',url:'${ctxPath}/pages/erp/order/follow-query.jsp',title:'采购跟进记录',data:{orderId:orderId}});
		}
		
		function editOrder(){
			popup.layerShow({id:'editOrder',scrollbar:false,title:'修改',area:['700px','500px'],url:'${ctxPath}/pages/erp/order/order-add.jsp',data:{orderId:orderId}});
		}
		
		function contractDetail(){
			var contractId = $("[name='apply.CONTRACT_ID']").val();
			var custId = $("[name='custId']").val();
			popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/pages/crm/contract/contract-detail.jsp',data:{contractId:contractId,custId:custId}});
		}
		
		function stopOrder(){
			popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'终止采购',data:{orderId:orderId,followState:90}});
		}
		
		function cannelOrder(){
			layer.confirm('确认操作吗,数据将会被重置',{icon:3,offset:'30px'},function(index){
				layer.close(index);
				popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'取消采购',data:{orderId:orderId,followState:99}});
			});
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>