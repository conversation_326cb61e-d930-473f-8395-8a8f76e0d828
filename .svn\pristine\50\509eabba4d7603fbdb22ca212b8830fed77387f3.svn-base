<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
		.layui-table-cell {
			height: auto;
		}
		.select2-search__field{min-width: 120px!important;}
		.layui-progress{margin-top: 13px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off" onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="source" type="hidden" value="${param.source}">
			<input name="projectStates" type="hidden" value="">
			<div class="ibox">
				<div class="ibox-title clearfix">
				 	<div class="pull-left layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: -4px 2px 5px;">
	          		 	 <ul class="layui-tab-title"><li data-val="" lay-id="0" class="layui-this">全部</li><li lay-id="1" data-val="11,12,13,20">进行中</li><li lay-id="2" data-val="30">已结束</li></ul>
          		 	 </div>
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm" style="width: 180px;">
							 <span class="input-group-addon">项目名称</span>	
							 <input type="text" name="projectName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 180px;">
							 <span class="input-group-addon">项目编号</span>	
							 <input type="text" name="projectNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px;">
							 <span class="input-group-addon">上级项目</span>	
							 <input type="text" name="pProjectName" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目类型</span>	
							 <select name="projectType" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="10" data-class="label label-info">合同项目</option>
		                    		<option value="11" data-class="label label-info">提前执行</option>
		                    		<option value="20" data-class="label label-warning">自研项目</option>
		                    		<option value="30" data-class="label label-warning">维保项目</option>
	                    	</select>
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目阶段</span>	
							 <select name="projectStage" onchange="list.query()" multiple="multiple" class="form-control input-sm" style="min-width: 160px;">
								  <option value="">--</option>
								  <option value="提前执行">提前执行</option>
								  <option value="提前执行-废弃">提前执行-废弃</option>
	                    		  <option value="提前执行-已正式签订">提前执行-已正式签订</option>
								  <option value="待初验">待初验</option>
								  <option value="待一次性验收">待一次性验收</option>
								  <option value="待终验">待终验</option>
								  <option value="已终验，维保在保">已终验，维保在保</option>
								  <option value="已终验，维保到期">已终验，维保到期</option>
								  <option value="维保到期">维保到期</option>
								  <option value="维保在保">维保在保</option>
								  <option value="在建合作运营">在建合作运营</option>
								  <option value="合作运营结项">合作运营结项</option>
								  <option value="异常终止">异常终止</option>
	                    	</select>
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目状态</span>	
							 <select name="projectState" onchange="list.query()" multiple="multiple" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="11" data-class="text-info">进度正常</option>
		                    		<option value="12" data-class="text-warning">存在风险</option>
		                    		<option value="13" data-class="text-danger">进度失控</option>
		                    		<option value="20" data-class="text-danger">挂起中</option>
		                    		<option value="30" data-class="text-success">已完成</option>
	                    	</select>
					     </div>
					     <div class="input-group input-group-sm"  style="width: 140px">
							 <span class="input-group-addon">产品线</span>	
							 <input type="text" name="productLine" readonly="readonly" onclick="singleDict(this,'Y001')" class="form-control input-sm">
					     </div>
					     <div class="input-group input-group-sm"  style="width: 140px">
							 <span class="input-group-addon">项目经理</span>	
							 <input type="text" name="projectPoName" class="form-control input-sm">
					     </div>
					     <div class="input-group input-group-sm"  style="width: 150px">
							 <span class="input-group-addon">开发负责人</span>	
							 <input type="text" name="po" class="form-control input-sm">
					     </div>
					     <c:if test="${param.source!='dept'}">
	          	     		 <div class="input-group input-group-sm" style="width: 160px">
								 <span class="input-group-addon">工程大区</span>	
								 <input type="text" name="projectDeptName" data-dept-name="工程" onclick="singleDept(this)" readonly="readonly" class="form-control input-sm select-icon">
						     </div>
					     </c:if>
          	     		 <div class="input-group input-group-sm" style="width: 170px">
							 <span class="input-group-addon">合同类型</span>	
							 <input type="text" name="contractType" readonly="readonly" onclick="singleDict(this,'Y004')" class="form-control input-sm select-icon">
					     </div>
					      <div class="input-group input-group-sm"  style="width: 140px">
							 <span class="input-group-addon">销售经理</span>	
							 <input type="text" name="salesName" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm"  style="width: 140px">
							 <span class="input-group-addon">售前</span>	
							 <input type="text" name="preSalesName" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm"  style="width: 280px">
							 <span class="input-group-addon">计划初验日期</span>	
							 <input style="width: 90px;display: inline-block" type="text" name="cyBeginDate" data-laydate="{type:'date'}" class="form-control input-sm">
							 <input style="width: 90px;display: inline-block" type="text" name="cyEndDate" data-laydate="{type:'date'}" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm"  style="width: 280px">
							 <span class="input-group-addon">计划终验日期</span>	
							 <input style="width: 90px;display: inline-block" type="text" name="zyBeginDate" data-laydate="{type:'date'}" class="form-control input-sm">
							 <input style="width: 90px;display: inline-block" type="text" name="zyEndDate" data-laydate="{type:'date'}" class="form-control input-sm">
					     </div>
					     <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="list.clear()">清空</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="progressTpl">
			{{if H>0}}
			<div class="layui-progress" lay-showPercent="true">
  				<div class="layui-progress-bar {{if H==K}} layui-bg-green {{else K/H <= 0.6}} layui-bg-red {{else K/H <= 0.8}} layui-bg-orange  {{else}} layui-bg-blue {{/if}}" lay-percent="{{:K}}/{{:H}}"></div>
			</div>
			{{else}}
				0/0
			{{/if}}
		</script>
		 <script type="text/html" id="btnBar">
			 <EasyTag:res resId="PROJECT_ADD">
				<div class="input-group input-group-sm">
					<button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 新增</button>
				</div>
			</EasyTag:res>
 		</script>
 		<script type="text/x-jsrender" id="planTpl">
			{{if PLAN_ID}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.plan">查看</a>
				{{else}}
				--
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ProjectDao.projectList',
					height:'full-90',
					autoSort:false,
					toolbar:'#btnBar',
					limit:50,
					rowEvent:'rowEvent',
					limits:[50,100,200,300,500],
					cols: [[
					  {title:'序号',type:'radio'},
					  {title:'序号',type:'numbers'},
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						align:'left',
						minWidth:250,
						style:'cursor:pointer;color:#4298fd;',
						event:'list.detail',
						templet:function(row){
							var type = row['PROJECT_TYPE'];
							var contractId = row['CONTRACT_ID'];
							var no = row['PROJECT_NO'];
							no = no==0?'':no;
							if(contractId==''){
								no  = " <span class='label label-danger'>无合同</span> "+ no;
							}
							if(type==10){
								return no +"&nbsp;"+ row.PROJECT_NAME;
							}else{
								var val = getText(type,'projectType');
								return val + "&nbsp;" + no + row.PROJECT_NAME;
							}
						}
					},{
					    field: 'PRODUCT_LINE',
						title: '产品线',
						width:110
					},{
					    field: 'PROJECT_STAGE',
						title: '项目阶段',
						width:110
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:80,
						hide:false,
						templet:function(row){
							return getText(row.PROJECT_STATE,'projectState');
						}
					},{
					    field: 'TASK_COUNT',
						title: '任务数',
						width:90,
						sort:true
					},{
					    field: 'BUG_COUNT',
						title: 'BUG数',
						width:90,
						event:'projectBugUrl',
						sort:true
					},
					{
					    field: 'D',
						title: '未完成数',
						align:'left',
						width:120,
						sort:true,
						event:'list.taskMgr',
						templet:function(row){
							return row['D']+"（<span title='待办/进行中/超时'>"+row['A']+"&nbsp;/&nbsp;"+row['B']+"&nbsp;/&nbsp;"+row['G']+"</span>）";
						}
					},{
					    field: 'PLAN_ID',
						title: '项目计划',
						align:'center',
						width:80,
						templet:function(row){
							return renderTpl('planTpl',row);
						}
					},{
					    field: 'SALES_BY_NAME',
						title: '销售',
						width:80
					},{
					    field: 'PROJECT_PO_NAME',
						title: '项目经理',
						sort:true,
						width:80
					},{
					    field: 'PO_NAME',
						title: '开发负责人',
						width:100
					},{
					    field: 'PROJECT_DEPT_NAME',
						title: '工程大区',
						sort:true,
						width:90
					},{
					    field: 'CY_DATE',
						title: '计划初验',
						width:90
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						width:90,
						sort:true,
						align:'center',
						hide:true
					},{
					    field: 'LAST_TASK_TIME',
						title: '最近任务',
						width:140,
						align:'left'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						width:140,
						align:'left'
					}
					]],done:function(){
						table.on('sort(list)', function(obj){
						  $("#searchForm").queryData({jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
						});
						layui.use(['element'], function(){
							var element = layui.element;
							element.render();
						});
					}}
				);
			},
			query:function(){
				var data = form.getJSONObject("#searchForm");
				var projectStage = data.projectStage;
				if(projectStage&&isArray(projectStage)){
					data['projectStage'] = projectStage.join(',');
				}
				var projectState = data.projectState;
				if(projectState&&isArray(projectState)){
					data['projectState'] = projectState.join(',');
				}
				$("#searchForm").queryData({jumpOne:true,data:data});
			},
			clear:function(){
				$('#searchForm')[0].reset();
			    $("[name='contractType']").val('');
				list.query();
			},
			detail:function(data){
				var projectId = data.PROJECT_ID;
				projectBoard(projectId);
				//popup.openTab({id:projectId,url:'${ctxPath}/project/'+projectId,title:'项目总览',data:{projectId:projectId}});
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'编辑项目',data:{projectId:data.PROJECT_ID}});
			},
			taskMgr:function(data){
				popup.openTab({url:'${ctxPath}/pages/task/task-query.jsp',id:'taskMgr',title:'项目任务管理',data:{projectId:data['PROJECT_ID']}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'新增项目'});
			},
			addTask:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务',data:{projectId:data.PROJECT_ID}});
			},
			plan:function(data){
				window.open('/yq-work/web/flow/'+data.PLAN_ID);
			}
		}
		
		
		function projectBugUrl(data){
			window.open('/yq-work/project/bug/'+data['PROJECT_ID']);
		}
		
		function reloadTaskList(){
			
		}
		function reloadProjectList(){
			list.query({jumpOne:true});
		}
		
		$(function(){
			layui.element.on('tab(stateFilter)', function(){
				$("[name='projectStates']").val(this.getAttribute('data-val'));
				reloadProjectList();
			});
			var isDevProject = '${param.isDevProject}';
			if(isDevProject=='1'){
				$("[name='contractType']").val('技术开发合同');
				layui.element.tabChange('stateFilter', '1');
			}
			$("#searchForm").render({success:function(){
				list.init();
			}});
			
			 requreLib.setplugs('select2',function(){
				 $("#searchForm select").select2({theme: "bootstrap",placeholder:'请选择'});
				 $(".select2-container").css("width","100%");
			 });
			
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>