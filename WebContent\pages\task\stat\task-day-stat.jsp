<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务统计</title>
	 <link rel="stylesheet" href="${ctxPath}/static/css/admin.css" media="all">
</EasyTag:override>
<EasyTag:override name="content">

		<div class="layui-row layui-col-space10">
			  <div class="layui-col-md12">
			  <div class="layui-card">
			  <div class="layui-card-header">任务创建数/趋势分析</div>
			  <div class="layui-card-body">
			  	<div id="charts1" class="layadmin-dataview" data-anim="fade"></div>
			  </div>
			</div>
			  </div>
			  <div class="layui-col-md12">
			  <div class="layui-card">
			  <div class="layui-card-header">任务办结数/趋势分析</div>
			  <div class="layui-card-body">
			  	<div id="charts2" class="layadmin-dataview" data-anim="fade"></div>
			  </div>
			</div>
			  </div>
		</div>
</EasyTag:override>
<EasyTag:override name="script">
  <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
  <script src="${ctxPath}/static/js/echartsTheme.js"></script>
  <script type="text/javascript">
  	
  	 var taskStat = {
  			loadEntOrderStatic : function(){
  				ajax.daoCall({controls:['TaskDao.taskDayStat'],params:form.getJSONObject("#taskForm")},function(result){
  					var data=result['TaskDao.taskDayStat'].data;
  					
  					var json ={};
  					
  					var rs1=data.rs1;
  					var rs2=data.rs2;
  					
  					
  					var json1={};
  					for(var index in rs1){
  						json1[rs1[index]['DATE']]=rs1[index]['COUNT1']||0;
  					}
  					var json2={};
  					for(var index in rs2){
  						json2[rs2[index]['DATE']]=rs2[index]['COUNT2']||0;
  					}
  					
  					var names=[];
  					var createCount=[];
  					var finishCount=[];
  					
  					for(var index in rs1){
  						names[index]=rs1[index]['DATE'];
  						createCount[index]=rs1[index]['COUNT1']||0;
  						finishCount[index]=json2[names[index]]||0;
  					}
  					
  					var names2=[];
  					var createCount2=[];
  					var finishCount2=[];
  					
  					for(var index in rs2){
  						names2[index]=rs2[index]['DATE'];
  						finishCount2[index]=rs2[index]['COUNT2']||0;
  						createCount2[index]=json1[names2[index]]||0;
  					}
  				  	var options1 = {
  				            tooltip: {
  				                trigger: "axis"
  				            },
  				            calculable: !0,
  				            legend: {
  				                data: ["日期","创建任务数"]
  				            },
  				            xAxis: [{
  				                type: "category",
  				                axisLabel: {  
  				                	   interval:0,  
  				                	   rotate:40  
  				                },
  				                data: names
  				            }],
  				            yAxis: [
  					            {
  					                type: "value",
  					                name: "提交数",
  					                axisLabel: {
  					                    formatter: "{value} 条"
  					                }
  					            },
  					            {
  					                type: "value",
  					                name: "办结数",
  					                axisLabel: {
  					                    formatter: "{value} 条"
  					                }
  					            }
  				            ],
  				            series: [
  					            {
  					                name: "提交数",
  					                type: "bar",
  					                barWidth:'20',
  					                data: createCount
  					            },
  					            {
  					                name: "办结数",
  					                type: "line",
  					                data: finishCount,
  					                yAxisIndex: 1,
  					                itemStyle:{
  			                            normal:{
  			                                color:'#999'
  			                            }
  			                        }
  					            }
  				            ]
  				       	};
  					    var myCharts = echarts.init(document.getElementById('charts1'), myEchartsTheme);
  					    myCharts.setOption(options1);
  					    
	  				  	var options2 = {
	  				            tooltip: {
	  				                trigger: "axis"
	  				            },
	  				            calculable: !0,
	  				            legend: {
	  				                data: ["日期","办结任务数"]
	  				            },
	  				            xAxis: [{
	  				                type: "category",
	  				                axisLabel: {  
	  				                	   interval:0,  
	  				                	   rotate:40  
	  				                },
	  				                data: names2
	  				            }],
	  				            yAxis: [
	  					            {
	  					                type: "value",
	  					                name: "办结数",
	  					                axisLabel: {
	  					                    formatter: "{value} 条"
	  					                }
	  					            },
	  					            {
	  					                type: "value",
	  					                name: "提交数",
	  					                axisLabel: {
	  					                    formatter: "{value} 条"
	  					                }
	  					            }
	  				            ],
	  				            series: [
	  					            {
	  					                name: "办结数",
	  					                type: "bar",
	  					                barWidth:'20',
	  					                data: finishCount2
	  					            },
	  					            {
	  					                name: "提交数",
	  					                type: "line",
	  					                data: createCount2,
	  					                yAxisIndex: 1,
	  					                itemStyle:{
	  			                            normal:{
	  			                                color:'#999'
	  			                            }
	  			                        }
	  					            }
	  				            ]
	  				       	};
	  					    var myCharts2 = echarts.init(document.getElementById('charts2'), myEchartsTheme);
	  					    myCharts2.setOption(options2);
  				});
  			}
  		};
	  	$(function(){
	  		taskStat.loadEntOrderStatic();
	  	});
  </script>
 </EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>