package com.yunqu.work.utils;

import javax.websocket.Session;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.impl.DefaultEasyCacheImpl;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.model.MsgModel;

public class WebSocketUtils {

	private static Logger log = LogEngine.getLogger("websocket");
	 
	//若要实现服务端与指定客户端通信的话，可以使用Map来存放，其中Key可以为用户标识
    
    public static DefaultEasyCacheImpl clients = DefaultEasyCacheImpl.getInstance();
   
    public static boolean existUser(String userId){
    	Session session = clients.get(userId);
    	if(session != null&&session.isOpen()){
    		return true;
    	}
    	
    	return false;
    }
    
    /*
    Add Session
     */
    public static void add(String userId,String userAcct, Session session) {
    	clients.put(userId,session);
        log.info(userAcct+"创建连接成功,当前连接数 = " + clients.getKeyValues().size());

    }
    public static int getOnlineCount(){
    	return  clients.getKeyValues().size();
    }

    /*
    Receive Message
     */
    public static void receive(String userId,String userAcct, String message) {
        log.info("连接数 >" + clients.getKeyValues().size()+",收到消息 : userAcct>" + userAcct + ",msg>" + message);
    }

    /*
    Remove Session
     */
    public static void remove(String userId,String userAcct) {
    	clients.delete(userId);
        log.info(userAcct+",断开连接,当前连接数 = " + clients.getKeyValues().size());

    }
    public static  void sendMessage(MsgModel message){
    	String userId=message.getReceiver();
    	sendMessage(userId, message.toJSONString());
    }
    /*
    Get Session
     */
    private static boolean sendMessage(String userId , String message) {
        log.info("当前连接数 = " + clients.getKeyValues().size()+",sendMessage="+message);
        try {
			if(StringUtils.isBlank(userId))return false;
			Session session = clients.get(userId);
			if(session == null){
			    return false;
			}else{
				if(session.isOpen()) {
					session.getAsyncRemote().sendText(message);
					return true;
				}
				clients.delete(userId);
				return false;
			}
		} catch (Exception e) {
			clients.delete(userId);
			log.error(null, e);
			return false;
		}

    }
    public static  int  sendAllMessage(MsgModel message){
    	return sendAllMessage(message.toJSONString());
    }
    private static  int  sendAllMessage(String message) {
    	log.info("当前连接数 = " + clients.getKeyValues().size()+",sendAllMessage="+message);
    	int i=0;
    	for(String toUserId:clients.getKeyValues().keySet()){
    		Session session = clients.get(toUserId);
    		if(session == null){
    			continue;
    		}else{
    			if(session.isOpen()) {
    				session.getAsyncRemote().sendText(message);
    				i++;
    			}
    		}
    	}
    	return i;
    }
}
