<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table class="layui-hide" id="projectWeekly"></table>
<script type="text/javascript">

	function reloadProjectWeekly(){
		$("#projectDetail").queryData({id:'projectWeekly'});
	}
	
	function loadProjectWeekly(){
		$("#projectDetail").initTable({
			mars:'ProjectDao.projectWeeklys',
			id:"projectWeekly",
			data:{},
			height:'full-130',
			cols: [[
	         {
				title: '序号',
				type:'numbers'
			 },{
			    field: 'USERNAME',
				title: '填写人',
				width:90,
				align:'left'
			},{
			    field: 'DEPTS',
				title: '所属部门',
				width:90,
				align:'left'
			},{
			    field: 'CREATE_TIME',
				title: '填写时间',
				width:160,
				align:'left'
			},{
			    field: 'TITLE',
				title: '周报名称',
				align:'left',
				event:'detailWeekly',
			    style:'color:#1E9FFF;cursor:pointer;text-decoration:underline;'
			},{
			    field: '',
				title: '操作',
				align:'left',
				width:80,
				templet:function(row){
					var creator = row['CREATOR'];
					if(getCurrentUserId()==creator){
						return '<a href="javascript:;" onclick="editWeekly(\''+row['WEEKLY_ID']+'\')" class="layui-text">修改</a>';
					}else{
						return '';
					}
				}
			}
			]],done:function(result){
				table.resize('projectWeekly');
				$(".n9").text(result.totalRow);
			}});
		
	}
	
	function addWeekly(){
 		var projectName=$("[name='project.PROJECT_NAME']").text();
	    popup.layerShow({type:1,maxmin:true,anim:0,full:fullShow(),shadeClose:false,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/weekly/project-weekly-edit.jsp',title:'新增项目周报',data:{projectId:'${param.projectId}',projectName:projectName}});
 	}
	
 	function editWeekly(data){
 		var weeklyId = data['WEEKLY_ID'];
 		if(!weeklyId){
 			weeklyId=data;
 		}
	    popup.layerShow({type:1,maxmin:true,anim:0,shadeClose:false,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/weekly/project-weekly-edit.jsp',title:'修改项目周报',data:{projectId:'${param.projectId}',weeklyId:weeklyId}});
 	}
 	
 	function detailWeekly(data){
 		var weeklyId = data['WEEKLY_ID'];
 		if(!weeklyId){
 			weeklyId=data;
 		}
	    popup.layerShow({type:1,maxmin:true,anim:0,shadeClose:true,scrollbar:false,offset:'20px',area:['80%','80%'],url:'${ctxPath}/pages/weekly/project-weekly-detail.jsp',title:'周报明细',data:{projectId:'${param.projectId}',weeklyId:weeklyId}});
 		
 	}
</script>