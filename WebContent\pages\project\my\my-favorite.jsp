<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我收藏的项目</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm" style="width: 180px;">
							 <span class="input-group-addon">项目编号</span>	
							 <input type="text" name="projectNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 180px;">
							 <span class="input-group-addon">项目名称</span>	
							 <input type="text" name="projectName" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目类型</span>	
							 <select name="projectType" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="10" data-class="label label-info">合同项目</option>
		                    		<option value="11" data-class="label label-info">提前执行</option>
		                    		<option value="20" data-class="label label-warning">自研项目</option>
		                    		<option value="30" data-class="label label-warning">维保项目</option>
	                    	</select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						 <table class="layui-hide" id="mylist4"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
  			{{if CREATOR == currentUserId|| isSuperUser || currentUserId == PO || currentUserId == PROJECT_PO}}<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

<script type="text/javascript">

		var list = {
			init4:function(){
				$("#searchForm").initTable({
					mars:'ProjectDao.myFavoriteProject',
					cellMinWidth:90,
					height:'full-90',
					title:'我关注的项目',
					id:'mylist4',
					limit:30,
					cols: [[
					  {title:'序号',type:'numbers'},
					  {
						title: '操作',
						align:'center',
						width:100,
						hide:true,
						templet:'<div><a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.delFavorite">取消关注</div>'
					  },
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						minWidth:280,
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail',
						templet:function(row){
							var type = row['PROJECT_TYPE'];
							var no = row['PROJECT_NO'];
							no = no==0?'':no;
							if(type==10){
								return no +"&nbsp;"+ row.PROJECT_NAME;
							}else{
								var val = getText(type,'projectType');
								return val + "&nbsp;" + no + row.PROJECT_NAME;
							}
						}
					},{
					    field: 'PROJECT_STAGE',
						title: '项目阶段',
						align:'left',
						width:110
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:90,
						templet:function(row){
							return projectState(row.PROJECT_STATE);
						}
					},{
					    field: 'PROJECT_PO_NAME',
						title: '项目经理',
						align:'center',
						width:100
					},{
					    field: 'TASK_COUNT',
						title: '任务数',
						width:70,
						event:'list.taskMgr',
						align:'center'
					},{
					    field: 'PERSON_COUNT',
						title: '参与人数',
						width:90,
						align:'center'
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						align:'center',
						width:100,
						sort:true
					},{
					    field: 'CY_REAL_DATE',
						title: '实际初验日期',
						width:120
					},{
					    field: 'ZY_DATE',
						title: '计划终验日期',
						width:120
					},{
					    field: 'ZY_REAL_DATE',
						title: '实际终验日期',
						width:120
					},{
					    field: 'WB_END_DATE',
						title: '维保到期时间',
						width:120
					},{
					    field: 'FAVORITE_TIME',
						title: '关注时间',
						width:120,
						sort:true,
						align:'center',
						templet:function(row){
							return cutText(row['FAVORITE_TIME'],12,'');
						}
					}
					]]}
				);
			},
			taskMgr:function(data){
				popup.openTab({url:'${ctxPath}/pages/task/task-query.jsp',id:'taskMgr',title:'项目任务管理',data:{projectId:data['PROJECT_ID']}});
			},
			query:function(){
				$("#searchForm").queryData({id:'mylist4'});
			},
			detail:function(data){
				projectDetailByRow(data);
			},
			delFavorite:function(data){
				ajax.remoteCall("${ctxPath}/servlet/project?action=delFavorite",{favoriteId:data['PROJECT_ID']},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							list.query();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		
		function reloadProjectList(){
			list.query();
		}
		
		function reloadTaskList(){
			
		}
		
		$(function(){
			list.init4();
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>