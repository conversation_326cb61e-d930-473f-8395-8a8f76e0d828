<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>周报</title>
	<style>
		.w-e-toolbar p, .w-e-text-container p, .w-e-menu-panel p {
		    font-size: 13px !important;
		}
		.common-mode{display: none;}
		.text-l{text-align: left!important;}
		.layui-panel{max-height: 500px;overflow-y: scroll;border-width: 1px;border-top: 2px solid #8eb2d7;}
		.project-mode .form-control{color: #c01002;border-radius: 0px;}
		.project-mode textarea,.project-mode input{
			border: 1px solid #ddd;
		}
		.project-mode textarea:hover{
			border: 1px solid red;
		}
		
		#editForm .form-control:focus {
		    border-color: #d2d2d2!important;
		}

		.td-header{
		    height: 40px;
		    line-height: 40px;
		    background-color: #f8f8f8;
		    width: 100%;
		    border: 1px solid #eee;
		    float: left;
		    text-align: left;
		    padding: 0px 10px;
		    border-bottom: none;
		}
		.textarea-content{
		   margin-bottom: -25px;
		}
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="WeeklyDao.record" autocomplete="off" data-mars-prefix="weekly.">
  		   		<input type="hidden"  name="weekly.WEEKLY_TYPE" value="0"/>
  		   		<input type="hidden"  name="weekly.STATUS"/>
  		   		<input type="hidden" id="workTimeLevel" name="weekly.WORK_TIME_LEVEL"/>
  		   		<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
  		   		<input type="hidden" id="weeklyId" value="${param.weeklyId}" name="weekly.WEEKLY_ID"/>
  		   		<input type="hidden" value="${param.weeklyId}" name="fkId"/>
  		   		<input type="hidden" value="${param.weeklyId}" name="weeklyId"/>
  		   		<input type="hidden" value="${param.YEAR}" name="weekly.YEAR"/>
  		   		<input type="hidden" value="${param.WEEK_NO}" name="weekly.WEEK_NO"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
						<tr style="display: none;">
							<td class="required">周报模板</td>
							<td>
								<select name="weekly.TPL_ID" class="form-control input-sm" onchange="loadWeeklyData()">
									<option value="default_tpl">默认模板</option>
								</select>
							</td>
						</tr>
						<tr>
							<td class="required"  style="width:120px;">标题</td>
							<td>
								<input name="weekly.TITLE" value="${param.TITLE }" id="title" type="text" class="form-control input-sm"/>
							</td>
						</tr>
						<tr>
		                    <td>主送</td>
		                    <td>
		                    	<input id="receiver" type="hidden" name="weekly.RECEIVER"/>
		                    	<input id="receiverName" type="text" data-rules="required" onclick="singleUser(this)" readonly="readonly" class="form-control input-sm" name="weekly.RECEIVER_NAME"/>
		                    </td>
		                </tr>
		                 <tr>
		                    <td>抄送</td>
		                    <td>
		                    	<input type="hidden" id="ccIds" name="weekly.CC_IDS"/>
		                    	<input id="ccNames" type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="weekly.CC_NAMES"/>
		                    </td>
	              		</tr>
			        </tbody>
				</table>
				<table class="table table-edit table-vzebra tplContent">
			        <tbody>
			        	<tr>
			           	 <td class="required" style="vertical-align: top;width: 120px;">
			             	  工作饱和度
			           	 </td>
			             <td>
			             	<select id="workDesc" data-rules="required" name="weekly.ITEM_5" onchange="setWorkTimeLevel(this)" class="form-control input-sm" >
			             		  <option value="">请选择</option>
								  <option value="很不饱和" data-val="10">很不饱和(60%↓)</option>
								  <option value="不饱和" data-val="10">不饱和(60~70%)</option>
								  <option value="低饱和" data-val="30">低饱和(70~85%)</option>
								  <option value="饱和" data-val="40">饱和(85~100%)</option>
								  <option value="超饱和" data-val="50">超饱和(100%↑)</option>
							</select>
			             </td>
			           </tr>
			           <tr class="hidden">
			           		<td>填写模式</td>
			           		<td>
		                        <label class="radio radio-inline radio-success">
		                        	<input type="radio" value="2" name="weekly.WRITE_MODE" checked="checked" onclick="toggleType(2)"><span>项目模式</span>
		                        </label>
			           			<label class="radio radio-inline radio-success">
			                        	<input type="radio" value="1" onclick="toggleType(1)" name="weekly.WRITE_MODE"> <span>普通模式</span>
		                        </label>
			           		</td>
			           </tr>
			           <tr class="common-mode">
			           	 <td style="width:120px;vertical-align: top;" class="required">
			           	  	 本周工作总结 <br><a href="javascript:;" class="selectTask" onclick="selectTask('1')">选择任务</a>
			           	 </td>
			             <td>
			             	<div id="editor"></div>
			             	<textarea id="wordText" name="weekly.ITEM_1" maxlength="5000" style="height:150px;display: none;" class="form-control input-sm"></textarea>
			             </td>
			           </tr>
			           <tr class="common-mode">
			           	 <td class="required" style="vertical-align: top;">
			           		 下周工作计划 <br><a href="javascript:;" onclick="selectTask('2')">选择任务</a>
			           	 </td>
			             <td>
			             	<div id="editor2"></div>
			             	<textarea id="wordText2" name="weekly.ITEM_2" style="height:80px;display: none;" class="form-control input-sm"></textarea>
			             </td>
			           </tr>
			           <tr class="project-mode">
			           	 <td>项目周报</td>
			             <td>
			           		<div class="layui-row layui-col-space15" data-mars="WeeklyDao.weeklyProject" data-template="project-items"></div>
			           		<div class="layui-row layui-col-space15">
			           			<c:forEach var="item" begin="1001" end="1030">
								  <div class="layui-col-md12" id="item_${item}" data-hide-tr="1" style="display:none;">
								    <div class="layui-panel">
								      <div style="padding: 10px;">
								      <table class="table table-edit table-vzebra">
								      	   <tr>
						           				<td colspan="4">
						           					<div class="td-header">
						           						<div class="pull-left">
								           					${item-1000}、参与的项目
						           						</div>
							           					<div style="margin-left: 120px;">
							           						<span>消耗天/周</span>
						           				    		<input style="width: 70px;display: inline-block;margin-left: 5px;" placeholder="0~7" type="number" name="workDay_${item}" onchange="checkWorkDay(this)" class="form-control input-sm"/>
							           						<a href="javascript:void(0)" onclick="$('#item_${item}').remove();" class="btn btn-xs pull-right btn-link ml-10 mt-10">删除</a>
							           					</div>
						           					</div>
						           					<div>
							           					<input type="hidden" name="projectId_${item}"/>
							           					<input type="text" name="projectName_${item}" readonly="readonly" onclick="singleProject(this);" placeholder="点击选择项目,若无固定项目请选择其他" class="form-control"/>
						           						<input type="hidden" value="${item}" name="itemIndex"/>
						           					</div>
						           				</td>
						           			</tr>
						           			<tr>
						           				<td colspan="4">
						           					<div class="td-header">
							           					本周工作<a href="javascript:;" class="selectTask pull-right" onclick="selectProjectTask('1','${item}')">选择任务</a>
						           					</div>
						           					<div class="textarea-content">
							           					<textarea style="height: 100px" placeholder="请输入本周工作内容 &#10;1、xxxx &#10;2、xxxx &#10;3、xxxx" name="workContent_${item}" class="form-control input-sm"></textarea>
						           						 <a href="javascript:void(0)" onclick="fullShowInput(this)" style="position: relative;top: -25px;right: 10px;">全屏编辑</a>
						           					</div>
						           				</td>
						           			</tr>
						           			<tr>
						           				<td  colspan="4">
							           				<div class="td-header">
						           						下周工作
						           						<a href="javascript:void(0)" onclick="$('.advTr').toggle();" class="btn btn-xs pull-right btn-link mt-10 ml-10">高级</a>
								           				<a href="javascript:;" class="selectTask pull-right" onclick="selectProjectTask('2','${item}')">选择任务</a>
							           				</div>
						           					<div class="textarea-content">
								           				<textarea style="height: 80px" placeholder="请输入下周计划，若没有，请填写无&#10;1、xxxx &#10;2、xxxx" name="planWorkContent_${item}" class="form-control input-sm"></textarea>
								           				<a href="javascript:void(0)" onclick="fullShowInput(this)" style="position: relative;top: -25px;right: 10px;">全屏编辑</a>
						           					</div>
						           				</td>
						           			</tr>
						           			<tr style="display: none;" class="advTr">
						           				<td class="text-l" colspan="2">
						           					<div class="td-header">本项目个人进度(%)</div>
						           					<input type="number" class="form-control input-sm" placeholder="0~100" onchange="checkProgressRate(this)" name="progressRate_${item}"/>
						           				</td>
						           				<td class="text-l" colspan="2">
							           				<div class="td-header">计划完成时间</div>
						           					<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="planFinishDate_${item}"/>
						           				</td>
						           			</tr>
						           		</table>
								      </div>
								    </div>   
								  </div>
			           			</c:forEach>
							</div> 
							<div class="layui-row">
				           		<button class="btn btn-sm btn-info" onclick="addTr();" type="button">+新增项目</button>
							</div>
			             </td>
			           </tr>
			           <tr>
			           	 <td style="vertical-align: top;">
			             	   问题/帮助/建议
			           	 </td>
			             <td>
			             	<textarea name="weekly.ITEM_3" style="height:60px" class="form-control input-sm"></textarea>
			             </td>
			           </tr>
			           <c:if test="${empty param.weeklyId}">
				           <tr>
				           	 <td style="vertical-align: top;">
				             	 备注
				           	 </td>
				             <td>
				             	<input name="weekly.ITEM_4"  data-mars="WeeklyDao.taskDesc" readonly="readonly" type="text" class="form-control input-sm">
				             </td>
				           </tr>
			           </c:if>
		               <tr>
		                <td>附件</td>
		               	<td>
		               		<div data-template="template-files" data-mars="FileDao.fileList"></div>
		               		<div id="fileList"></div>
							<button class="btn btn-sm btn-default mt-5" type="button" onclick="$('#localfile').click()">+添加</button>
		               	</td>
			           </tr>
			        </tbody>
				 </table>
				 <div class="layer-foot text-c" style="z-index: 99999999999;">
				 		  <c:if test="${empty param.weeklyId || param.status=='0'}">
					    	  <button type="button" class="btn btn-danger btn-sm ml-15"  onclick="Edit.ajaxSubmitForm(0)"> 草 稿  </button>
				 		  </c:if>
				    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm(1)"> 提 交 </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
  		
  		<script id="project-items" type="text/x-jsrender">
  		 {{for data}}
			<div class="layui-col-md12 data-ok" id="item_{{:ITEM_ID}}">
			  <div class="layui-panel">
				  <div style="padding: 10px;">
				  <table class="table table-edit table-vzebra">
						<tr>
							<td colspan="4">
								<div class="td-header">
									<div class="pull-left">
									   {{:#index+1}}、参与的项目
									</div>
									   <div style="margin-left:120px;">
								  		 <span>消耗天/周</span>
						           		 <input style="width: 70px;display: inline-block;margin-left: 5px;" value="{{:WORK_DAY}}" type="number" name="workDay_{{:ITEM_ID}}" onchange="checkWorkDay(this)" class="form-control input-sm"/>
								  		 <a href="javascript:void(0)" onclick="delItem('{{:ITEM_ID}}')" class="btn btn-xs btn-link pull-right mt-10 ml-10">删除</a>
									  </div>
								</div>
								<input type="hidden" name="projectId_{{:ITEM_ID}}" value="{{:PROJECT_ID}}"/>
								<input type="text" name="projectName_{{:ITEM_ID}}" value="{{:PROJECT_NAME}}" readonly="readonly" onclick="singleProject(this);" placeholder="点击选择项目" class="form-control"/>
								<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
							</td>
						</tr>
						<tr>
							<td colspan="4">
								<div class="td-header">
									本周工作<a href="javascript:;" class="selectTask pull-right" onclick="selectProjectTask('1','{{:ITEM_ID}}')">选择任务</a>
								</div>
								<div class="textarea-content">
									<textarea style="height: 100px" placeholder="请输入本周工作内容" name="workContent_{{:ITEM_ID}}" class="form-control input-sm">{{:WORK_CONTENT}}</textarea>
									<a href="javascript:void(0)" onclick="fullShowInput(this)" style="position: relative;top: -25px;right: 10px;">全屏编辑</a>							
								</div>
							</td>
						</tr>
						<tr>
							<td  colspan="4">
								<div class="td-header">
									下周工作
									<a href="javascript:void(0)" onclick="$('.advTr').toggle();" class="btn btn-xs pull-right btn-link mt-10 ml-10">高级</a>
									<a href="javascript:;" class="selectTask pull-right" onclick="selectProjectTask('2','{{:ITEM_ID}}')">选择任务</a>
								</div>
								<div class="textarea-content">
									<textarea style="height: 70px" placeholder="请输入下周计划，若没有，请填写无"  name="planWorkContent_{{:ITEM_ID}}" class="form-control input-sm">{{:PLAN_WORK_CONTENT}}</textarea>
									<a href="javascript:void(0)" onclick="fullShowInput(this)" style="position: relative;top: -25px;right: 10px;">全屏编辑</a>								
								</div>
							</td>
						</tr>
						 <tr style="{{if PROGRESS_RATE}}{{else}}display: none;{{/if}}" class="advTr">
						         <td class="text-l" colspan="2">
						             <div class="td-header">本项目个人进度(%)</div>
									 <input type="number" value="{{:PROGRESS_RATE}}" class="form-control input-sm" onchange="checkProgressRate(this)" placeholder="0~100" name="progressRate_{{:ITEM_ID}}"/>
						         </td>
						         <td class="text-l" colspan="2">
							          <div class="td-header">计划完成时间</div>
									   <input type="text" value="{{:PLAN_FINISH_DATE}}" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="planFinishDate_{{:ITEM_ID}}"/>
						         </td>
						   </tr>
					</table>
				  </div>
				</div> 
			</div>
 	     {{/for}}
   	  </script>
  		<script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Edit.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.config.menus = [
				'todo',//todo
	 		    'head',  // 标题
	 		    'bold',  // 粗体
	 		    'fontSize',  // 字号
	 		    'fontName',  // 字体
	 		    'foreColor',  // 文字颜色
	 		    'link',  // 插入链接
	 		    'list',  // 列表
	 		    'justify',  // 对齐方式
	 		    'quote',  // 引用
	 		    'code',  // 插入代码
	 		    'emoticon',  // 表情
	 		    'image'  // 插入图片
	 	];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.config.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.config.height = 100;
		editor.create();
		
		var editor2 = new E('#editor2');
		editor2.config.menus = [
				'todo',//todo
	 		    'head',  // 标题
	 		    'bold',  // 粗体
	 		    'fontSize',  // 字号
	 		    'fontName',  // 字体
	 		    'foreColor',  // 文字颜色
	 		    'link',  // 插入链接
	 		    'list',  // 列表
	 		    'justify',  // 对齐方式
	 		    'quote',  // 引用
	 		    'code',  // 插入代码
	 		    'emoticon',  // 表情
	 		    'image'  // 插入图片
	 	];
		weUpload(editor2,{uploadImgMaxLength:3});
		editor2.config.onchange = function (html) {
		     $("#wordText2").val(html)
		}
		editor2.config.height = 100;
		editor2.create();
	
		jQuery.namespace("Edit");
		Edit.weeklyId='${param.weeklyId}';
		function autoHeight(){
			$("textarea").keydown(function(event) {
				  if(event.keyCode == "13") {
					 var height = $(this).height();
					 $(this).css('height',(height+20)+'px');
				  }
				   if(event.keyCode == 8) {
					 var height = $(this).height();
					 height=height>120?height:120;
					 $(this).css('height',(height-20)+'px');
				   }
			});
			$(".w-e-text-container").keydown(function(event) {
				  if(event.keyCode == "13") {
					 var height = $(this).height();
					 $(this).css('height',(height+20)+'px');
				  }
				   if(event.keyCode == 8) {
					 var height = $(this).height();
					 height=height>120?height:120;
					 $(this).css('height',(height-20)+'px');
				   }
			});
		}
		function loadWeeklyData(){
			$("#editForm").render({success:function(result){
				 editor.txt.html($("#wordText").val());
				 editor2.txt.html($("#wordText2").val());
				 
				renderDate('#editForm');
				if(Edit.weeklyId){
					var record=result['WeeklyDao.record'];
					var data=record.data;
					if(data&&data.STATUS==2){
						$("#receiverName,#ccNames").prop("disabled", true);
					}
					
					toggleType(data['WRITE_MODE']);
					
					fillRecord(data,'weekly.','',"#editForm");
					
				}else{
					addTr();
				}
				
				$(".w-e-text-container").first().css("min-height","120px");
				$(".w-e-text-container").last().css("min-height","120px");
				if(Edit.weeklyId==''){
					var weeklyReceiver = localStorage.getItem("weeklyReceiver")||'';
					var weeklyMailtos = localStorage.getItem("weeklyMailtos")||'';
				
					$("#receiver").val(weeklyReceiver);
					$("#ccIds").val(weeklyMailtos);
					
					var weeklyReceiverName = localStorage.getItem("weeklyReceiverName")||'';
					var weeklyMailtoName = localStorage.getItem("weeklyMailtoName")||'';
					
					$("#receiverName").val(weeklyReceiverName);
					$("#ccNames").val(weeklyMailtoName);
				}
				autoHeight();
			}});  
		}
		$(function(){
			easitline_config['escClose'] = 'none';
			loadWeeklyData();
		});
		Edit.uploadFile = function(callback){
			var fkId='';
			if(Edit.weeklyId){
				fkId=Edit.weeklyId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'callback',fileMaxSize:(1024*50),fkId:fkId,source:'weekly'});
		}
		var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
		}
		Edit.ajaxSubmitForm = function(draft){
			if(form.validate("#editForm")){
				 var writeMode = $("[name='weekly.WRITE_MODE']:checked").val();
				 if(writeMode=='2'){
					 var html = [];
					 var nextWeek = [];
					 var index = 1;
					 var flag = true;
					 $("[name='itemIndex']").each(function(){
						 var itemId = $(this).val();
						 var v1 = $("[name='projectId_"+itemId+"']").val();
						 if(v1){
							 var v2 = $("[name='projectName_"+itemId+"']").val();
							 var v3 = $("[name='workContent_"+itemId+"']").val();
							 var v4 = $("[name='workDay_"+itemId+"']").val();
							 var v5 = $("[name='planWorkContent_"+itemId+"']").val();
							 var v6 = $("[name='progressRate_"+itemId+"']").val();
							 var v7 = $("[name='planFinishDate_"+itemId+"']").val();
							 if(v3==''&&v5==''){
								 flag = false;
								 layer.msg('本周和下周不能同时为空',{icon:7});
								 //return false;
							 }
							 if(v4==''){
								 flag = false;
								 layer.msg('工时不能为空',{icon:7});
								// return false;
							 }
							 v3 = v3.trim();
							 v3 = v3.replaceAll('\n','<br>');
							 if(v3==''){
								 v3 = '无';
							 }
							 
							 v5 = v5.trim();
							 v5 = v5.replaceAll('\n','<br>');
							 if(v5==''){
								 v5 = '无';
							 }
							 html.push("<blockquote class='layui-elem-quote layui-quote-nm'><b>"+index+"、"+v2+"</b><br>"+v3);
							 if(v6){
								 html.push("<br>完成进度："+v6+"%");
							 }
							 if(v7){
								 html.push("<br>计划完成日期："+v7);
							 }
							 html.push("</blockquote>");
							 
							 nextWeek.push("<blockquote class='layui-elem-quote layui-quote-nm'><b>"+index+"、"+v2+"</b><br>"+v5+"</blockquote>");
							 index = index +1;
						 }
					 });
					 if(html.length==0){
						 layer.msg('请填写项目工作内容',{icon:7});
						 return;
					 }
					 if(!flag&&draft=='1'){
						 layer.msg('请填写完整',{icon:7});
						 return;
					 }
					 editor.txt.html(html.join(''));
					 $("#wordText").val(html.join(''));
					 
					 editor2.txt.html(nextWeek.join(''));
					 $("#wordText2").val(nextWeek.join(''));
				 }
				 
				if(draft=='1'){
					 var  users = $("#receiver").val();
					 if(users==''){
						 layer.msg("请选择主送。",{icon:7});
						 return;
					 }
					 var text=$.trim(editor.txt.text()||'');
					 var text2=$.trim(editor2.txt.text()||'');
					 var fileDiv =$(".file-div").length;
					 if(text.length==0&writeMode=='1'){
						 layer.msg("本周工作总结不能为空。",{icon:7});
						 return;
					 }
					 if(text2.length==0){
						 layer.msg("下周工作计划不能为空。",{icon:7});
						 return;
					 }
					 
					 if(text.length < 50&&fileDiv==0){
				    	 layer.confirm("当前本周工作总结描述("+text.length+")字数,<br>内容太短(低于50字),是否继续提交?",{offset:'auto',icon:7},function(index){
				    		 layer.close(index);
				    		 doSubmit();
				    	 });
				     }else{
				    	 doSubmit();
				     }
				}else{
					doSubmit();
				}
			     
			};
		   function doSubmit(){
			 if(Edit.weeklyId){
				Edit.updateData(draft); 
			 }else{
				Edit.insertData(draft); 
			}
		  }
			 
		}
		
		function toggleType(type){
		  if(type=='1'){
			  $('.common-mode').show();
			  $('#editForm .project-mode').hide();
		  }else{
			  $('#editForm .project-mode').show();
			  $('.common-mode').hide();
		  }
		}
		
		Edit.insertData = function(draft) {
			$("#weeklyId").val($("#randomId").val());
			
			var data = form.getJSONObject("#editForm");
			data['draft'] = draft;
			ajax.remoteCall("${ctxPath}/servlet/weekly?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						localStorage.setItem("weeklyReceiver",$("#receiver").val());
						localStorage.setItem("weeklyMailtos",$("#ccIds").val());
						localStorage.setItem("weeklyReceiverName",$("#receiverName").val());
						localStorage.setItem("weeklyMailtoName",$("#ccNames").val());
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function(draft) {
			var data = form.getJSONObject("#editForm");
			data['draft'] = draft;
			ajax.remoteCall("${ctxPath}/servlet/weekly?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		};
		Edit.del=function(data){
			layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/weekly?action=delete",{'weekly.WEEKLY_ID':Edit.weeklyId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							list.query();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}
		
	    function addTr(){
			var obj  = $("[data-hide-tr]").first();
			if(obj.length==0){
				layer.msg('不能再新增啦',{icon:7,shade: 0.1});
				return;
			}
			obj.addClass('data-ok');
			obj.removeAttr("data-hide-tr");
			obj.show();
		}
		  
		  
		function selectProjectTask(type,itemId){
			var projectId = $("[name='projectId_"+itemId+"']").val();
			if(projectId){
				popup.layerShow({id:'selectProjectTask',url:'/yq-work/pages/weekly/select-project-task.jsp',offset:'l',title:'选择任务',area:['700px','100%'],data:{type:type,itemId:itemId,projectId:projectId}});
			}else{
				layer.msg('请选择项目',{time:800,icon:7});
			}
		}
		
		function selectTask(type){
			popup.layerShow({id:'selectTask',url:'/yq-work/pages/weekly/weekly-select-task.jsp',offset:'l',title:'选择任务',area:['700px','100%'],data:{type:type}});
		}
		
		function selectTaskCallback(type,taskNames){
			if(type=='1'){
			   editor.txt.append(taskNames);
			}else{
			  editor2.txt.append(taskNames);
			}
		}
		
		function selectProjectTaskCallback(type,itemId,taskNames){
			if(type=='1'){
				$("[name='workContent_"+itemId+"']").val(taskNames);
			}else{
				$("[name='planWorkContent_"+itemId+"']").val(taskNames);
			}
		}
		
		function setWorkTimeLevel(el){
			var obj = $(el);
			var val = obj.find("option:selected").data('val'); 
			$('#workTimeLevel').val(val);
		}
		
		function delItem(id){
			layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/weekly?action=delItem",{itemId:id},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg);
						$('#item_'+id).remove();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});					
			});
		}
		
		function checkWorkDay(el){
			var t = $(el);
			var num  = t.val();
			if(num>7){
				t.val(7);
				layer.msg('一周内小于7天',{icon:7});
			}
			if(num<0.1){
				t.val(0);
				layer.msg('不能小于0天',{icon:7});
			}
		}
		
		function checkProgressRate(el){
			var t = $(el);
			var num  = t.val();
			if(num>100){
				t.val(100);
				layer.msg('0~100',{icon:7});
			}
			if(num<0){
				t.val(0);
				layer.msg('不能小于0天',{icon:7});
			}
		}
		
		function fullShowInput(el){
			var val = $(el).prev().val();
			layer.prompt({title:'编辑',offset:'20px',formType:2,value:val,area:['600px','400px']},function(value, index, elem){
				$(el).prev().val(value);
				layer.close(index);
			});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>