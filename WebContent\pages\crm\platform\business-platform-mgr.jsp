<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>业务平台管理</title>
    <style>
        .layui-table-tool-temp{padding-right:0px;}
        .layui-table-tool-self{display: none;}
        #organizationTree {
            border: 1px solid #e6e6e6;
            border-top: none;
            padding: 10px 5px;
            overflow: auto;
            height: -webkit-calc(100vh - 50px);
            height: -moz-calc(100vh - 50px);
            height: calc(100vh - 50px);
        }
        /* 添加选中节点的样式 */
        .ew-tree-click {
            background-color: #f2f2f2 !important;
            color: #009688 !important;
            font-weight: bold;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form autocomplete="off" onsubmit="return false;" class="form-inline" id="searchForm">
        <input type="hidden" name="platformTypeId" id="platformTypeId">
        <div class="layui-row layui-col-space10 mt-10">
            <div class="layui-col-md3" style="width: 20%">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="layui-form toolbar" id="organizationTreeBar">
                            <button onclick="PlatformType.addData();" type="button" class="layui-btn layui-btn-xs icon-btn">+类别 </button>&nbsp;
                            <button onclick="PlatformType.editData();" type="button" class="layui-btn layui-btn-xs layui-btn-warm icon-btn">修改</button>&nbsp;
                            <button onclick="PlatformType.delPlatformType();" type="button" class="layui-btn layui-btn-xs layui-btn-normal icon-btn">删除</button>&nbsp;
                        </div>
                        <div id="organizationTree"></div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md9" style="width: 80%">
                <div class="layui-card">
                    <div class="ibox-title clearfix">
                        <div class="form-group">
                            <fieldset class="content-title">
                                <legend>平台信息</legend>
                            </fieldset>
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">一级平台</span>
                                <input type="text" readonly id="platformTypeName" class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">平台名称</span>
                                <input type="text" name="platformName" placeholder="输入关键字" class="form-control input-sm" >
                            </div>
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">状态</span>
                                <select name="status" class="form-control input-sm" >
                                    <option value="">全部</option>
                                    <option value="0">启用</option>
                                    <option value="1">停用</option>
                                </select>
                            </div>
                            <div class="input-group input-group-sm" style="width: 180px">
                                <button type="button" class="btn btn-sm btn-info" onclick="Platform.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
                                <button type="button" class="btn btn-sm btn-default" onclick="Platform.resetData()" style="margin-left: 2%"> 清 空</button>
                            </div>
                        </div>

                    </div>
                    <div class="layui-card-body">
                        <table id="platformList"></table>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <script  type="text/html" id="platformListBar">
        <button onclick="Platform.addData()" type="button" class="btn btn-sm btn-info ">+新增平台</button>
    </script>

</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript">
    var list = {};
    var platformTypeId = '';
    
    $(function(){
        $("#searchForm").render({success:function(){
            loadTree();
            initTable();
        }});
    });

    function loadTree(){
        list.load();
    }
    
    list.load = function(){
        ajax.remoteCall("${ctxPath}/webcall?action=BusinessPlatformDao.getTree",form.getJSONObject("#searchForm"),function(rs) { 
            var result = rs.data;
            loadTreeTable(result);
        });
    }
    
    function loadTreeTable(result){
        layui.use(['tree'], function () {
            var tree = layui.tree;
            // 构建根节点
            var rootNode = {
                title: '全部',
                id: '0',
                PLATFORM_TYPE_ID: '0',
                PLATFORM_TYPE_NAME: '',
                spread: true,
                children: []
            };
            
            // 将原始数据转换为树形结构
            var treeData = dataToTree(result,{
                idFiled: 'PLATFORM_TYPE_ID', 
                textFiled: 'PLATFORM_TYPE_NAME', 
                parentField: 'P_PLATFORM_TYPE_ID', 
                childField: 'children',
                def:{spread:true}, 
                map: {
                    PLATFORM_TYPE_ID: 'id', 
                    PLATFORM_TYPE_NAME: 'title' 
                } 
            });
            
            // 将所有节点放入根节点的 children 中
            rootNode.children = treeData;
            
            tree.render({
                elem: '#organizationTree',
                onlyIconControl: true,
                data: [rootNode], // 只传入包含所有子节点的根节点
                click: function (obj) {
                    console.log("obj");
                    console.log(obj);
                    platformTypeId = obj.data.id;
                    $('#organizationTree').find('.ew-tree-click').removeClass('ew-tree-click');
                    $(obj.elem).children('.layui-tree-entry').addClass('ew-tree-click');
                    $('#platformTypeId').val(platformTypeId);
                    $('#platformTypeName').val(obj.data.title);
                    Platform.searchData();
                }
            });
            // 默认选中"全部"节点
            $('#organizationTree').find('.layui-tree-entry:first>.layui-tree-main>.layui-tree-txt').trigger('click');
        });
    }
    
    function initTable(){
        var year = new Date().getFullYear();

        $("#searchForm").initTable({
            mars:'BusinessPlatformDao.platformStatList',
            id:'platformList',
            limit:20,
            toolbar: '#platformListBar',
            height: 'full-150',
            cols: [[
                {title:'序号',type:'numbers'},
               {
                    field: 'PLATFORM_TYPE_NAME',
                    title: '一级平台',
                   align: 'center',
                   minWidth: 120,
                },{
                    field: 'PLATFORM_NAME',
                    title: '平台名称',
                    align: 'center',
                    minWidth: 140,
                    sort:true
                },{
                    field: 'SJ_COUNT',
                    title: '商机数',
                    align: 'center',
                    width: 80,
                    sort:true
                },{
                    field: 'ORDER_COUNT',
                    title: '订购数',
                    align: 'center',
                    width: 80,
                    sort:true
                },{
                    field: 'YEAR_INVOICE_AMOUNT',
                    title: year+'开票金额',
                    align: 'center',
                    minWidth: 120,
                    sort:true
                },{
                    field: 'REMARK',
                    title: '备注',
                    align: 'left',
                    minWidth: 140
                },{
                    field: 'STATUS',
                    title: '状态',
                    width:70,
                    align: 'center',
                    templet: function(row){
                        return row.STATUS=='0'?'启用':'停用';
                    }
                },{
                    field: 'IDX_ORDER',
                    title: '排序',
                    width:80,
                    hide:true
                },{
                    field:'UPDATE_TIME',
                    title:'修改时间',
                    hide:true
                },{
                    field: '',
                    title: '操作',
                    align: 'center',
                    minWidth:140,
                    width: 140,
                    templet: function(row){
                        var html='<a class="btn btn-xs btn-link text-info f-12" href="javascript:void(0)" onclick="Platform.detail(\''+row['PLATFORM_ID']+'\')">详情</a>';
                        html+='<a class="btn btn-xs btn-link text-warning f-12" href="javascript:void(0)" onclick="Platform.editData(\''+row['PLATFORM_TYPE_ID']+'\',\''+row['PLATFORM_ID']+'\',\''+row['PLATFORM_TYPE_NAME']+'\')">修改</a>';
                        html+='<a class="btn btn-xs btn-link f-12" href="javascript:void(0)" onclick="Platform.delPlatform(\''+row['PLATFORM_TYPE_ID']+'\',\''+row['PLATFORM_ID']+'\',\''+row['PLATFORM_NAME']+'\')">删除</a>';
                        return html;
                    }
                }
            ]]
        });
    }

    var PlatformType = {
        addData: function(){
            var pPlatformTypeId = $("#platformTypeId").val();
            var pPlatformTypeName = $("#platformTypeName").val();
            var data = {pPlatformTypeId:pPlatformTypeId,pPlatformTypeName:pPlatformTypeName,op:'add'};
            if(pPlatformTypeId=='0'){
                data.platformTypeName = '根目录';
            }
            popup.layerShow({
                type:1,
                title:'增加平台类型',
                offset:'20px',
                area:['420px','320px'],
                url:"${ctxPath}/pages/crm/platform/business-platform-type-edit.jsp",
                data:data
            });
        },
        editData:function(){
            var platformTypeId = $("#platformTypeId").val();
            var platformTypeName = $("#platformTypeName").val();
            if(platformTypeId==''){
                return;
            }
            if(platformTypeId=='0'){
                layer.msg('根目录不能修改',{icon: 5});
                return;
            }
            popup.layerShow({
                type:1,
                title:'修改平台类型',
                offset:'20px',
                area:['420px','320px'],
                url:"${ctxPath}/pages/crm/platform/business-platform-type-edit.jsp",
                data:{platformTypeId:platformTypeId,platformTypeName:platformTypeName,op:'edit'}
            });
        },
        delPlatformType:function(){
            if(platformTypeId==''){
                return;
            }
            var platformTypeName = $("#platformTypeName").val();
            layer.confirm('一级平台['+platformTypeName+']将要被删除，该类别下的平台也会一同删除，是否继续?',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
                layer.close(index);
                var data = {platformTypeId:platformTypeId};
                ajax.remoteCall("${ctxPath}/servlet/business-platform?action=deletePlatformType",data,function(result) { 
                    if(result.state == 1){
                        layer.msg(result.msg,{icon: 1,time:1200},function(){
                            Platform.searchData();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                }); 
            });
        }
    }

    var Platform = {
        addData:function(){
            var platformTypeId = $("#platformTypeId").val();
            var platformTypeName = $("#platformTypeName").val();
            if(platformTypeName){
                popup.layerShow({
                    type:1,
                    title:'新增业务平台',
                    offset:'20px',
                    area:['420px','430px'],
                    url:"${ctxPath}/pages/crm/platform/business-platform-edit.jsp",
                    data:{
                        platformTypeId:platformTypeId,
                        platformTypeName:platformTypeName
                    }
                });
            }else{
                layer.msg("请选择平台类别");
            }
        },

        editData:function(platformTypeId,platformId,platformTypeName){
            popup.layerShow({
                type:1,
                title:'修改业务平台',
                offset:'20px',
                area:['420px','430px'],
                url:"${ctxPath}/pages/crm/platform/business-platform-edit.jsp",
                data:{
                    platformId:platformId,
                    platformTypeId:platformTypeId,
                    platformTypeName:platformTypeName
                }
            });
        },
        delPlatform:function(platformTypeId,platformId,platformName){
            layer.confirm('平台['+platformName+']将要被删除，是否继续?',{icon: 3, title:'删除提示'},  function(index){
                layer.close(index);
                var data = {};
                data.platformId = platformId;
                data.platformTypeId = platformTypeId;
                ajax.remoteCall("${ctxPath}/servlet/business-platform?action=deletePlatform", data, function(result) {
                    if(result.state == 1){
                        layer.msg(result.msg,{icon: 1,time:1200},function(){
                            Platform.searchData();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            });  
        },
        searchData:function(){
            $("#searchForm").queryData({id:'platformList'});
        },
        resetData:function (){
            $("#searchForm")[0].reset();
            $("#searchForm").queryData({id:'platformList'});
        },
        detail:function(platformId){
            popup.openTab({id: 'platformDetail', title: '平台详情', url: '${ctxPath}/pages/crm/platform/business-platform-detail.jsp', data: {platformId:platformId}});
        }
    }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
