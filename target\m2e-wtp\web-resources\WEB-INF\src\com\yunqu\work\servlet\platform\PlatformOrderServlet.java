package com.yunqu.work.servlet.platform;

import javax.servlet.annotation.WebServlet;
import org.springframework.stereotype.Controller;
import com.yunqu.work.base.AppBaseServlet;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import java.sql.SQLException;

@WebServlet("/servlet/platform-order")
@Controller
public class PlatformOrderServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    public EasyResult actionForAddOrder() {
        try {
            EasyRecord record = new EasyRecord("yq_crm_platform_order", "ORDER_ID");
            record.setColumns(this.getJSONObject("order"));
            record.setPrimaryValues(RandomKit.uuid().toUpperCase());
            record.set("CREATE_TIME", EasyDate.getCurrentDateString());
            record.set("CREATOR", this.getUserId());

            this.getQuery().save(record);

            return EasyResult.ok(null, "添加成功!");
        } catch (SQLException e) {
            this.error("添加订购记录失败", e);
            return EasyResult.fail("添加失败:" + e.getMessage());
        }
    }

    public EasyResult actionForUpdateOrder() {
        try {
            EasyRecord record = new EasyRecord("yq_crm_platform_order", "ORDER_ID");
            record.setColumns(this.getJSONObject("order"));
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());

            this.getQuery().update(record);

            return EasyResult.ok(null, "修改成功!");
        } catch (SQLException e) {
            this.error("修改订购记录失败", e);
            return EasyResult.fail("修改失败:" + e.getMessage());
        }
    }

    public EasyResult actionForDeleteOrder() {
        try {
            String orderId = this.getJsonPara("orderId");
            if(orderId == null || orderId.trim().isEmpty()) {
                return EasyResult.fail("订购ID为空!");
            }
            if(this.getQuery().queryForExist("select count(1) from yq_crm_sales_price where PLATFORM_ORDER_ID = ?", orderId)) {
                return EasyResult.fail("该订购信息已被销售价格引用,不能删除!");
            }
            if(this.getQuery().queryForExist("select count(1) from yq_crm_cost where PLATFORM_ORDER_ID = ?", orderId)) {
                return EasyResult.fail("该订购信息已被客户成本引用,不能删除!");
            }

            this.getQuery().executeUpdate("delete from yq_crm_platform_order where ORDER_ID = ?", orderId);

            return EasyResult.ok(null, "删除成功!");
        } catch (SQLException e) {
            this.error("删除订购记录失败", e);
            return EasyResult.fail("删除失败:" + e.getMessage());
        }
    }
}