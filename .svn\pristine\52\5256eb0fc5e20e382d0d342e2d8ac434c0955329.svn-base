<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="InvoiceEditForm" class="form-horizontal" autocomplete="off">
     			<input id="fkId" type="hidden" data-mars="CommonDao.randomId">
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
			            	<td style="text-align: left;">备注：支持格式 pdf，ofd，xml</td>
			            </tr>
			            <tr>
			            	<td>
			            			<div class="layui-upload-drag" style="display: block;height: 170px;" id="upload-drag">
									  	<i class="layui-icon layui-icon-upload"></i> 
									  	<div>点击上传，或将文件拖拽到此处，pdf|ofd|xml</div>
									</div>
			            	</td>
			            </tr>
			        </tbody>
 				</table>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		$(function(){
			$('#InvoiceEditForm').render({success:function(){
				layui.use('upload', function(){
				 	  var upload = layui.upload;
					  var uploadInst = upload.render({
					    elem: '#upload-drag'
					    ,url: '/yq-work/servlet/upload?action=index'
					    ,accept: 'file'
					    ,exts:'pdf|ofd|xml'
					    ,size:1024*20
						,multiple:true
						,number:10
					    ,field: 'file'
					    ,data:{
					    	source:function(){
								return 'bxInvoice';
							},
							fkId:function(){
								return $('#fkId').val();
							}
					    },done: function(result, index, upload){
							var item = this.item;
						    if(result.state==1){
						    	layer.msg(result.msg,{icon: 1,time:800},function(){
									callback(result.data,item);
								}); 
							}else{
								 layer.closeAll('dialog');
								 layer.closeAll('loading');
								 layer.alert(result.msg,{icon: 5});
							}
					    },choose:function(obj){
					    	
					    },before:function(obj){
					    	
					    },allDone: function(obj){
			
						}
					    ,error: function(res, index, upload){
					    	layer.close(loadIndex);
					    	layer.alert("上传文件请求异常！",{icon: 5});
					    }
			    });
			  });
			}});
			
		});
		
	 function callback(data){
		 data['invoiceId'] = $('#fkId').val();
		 ajax.remoteCall('${ctxPath}/servlet/bx/invoice?action=index',data,function(result){
				if(result.state == 1){
					layer.msg(result.msg,{icon : 1, time : 1000},function(){
						reloadInvoiceList();
					});
				}else{
					layer.msg(result.msg,{icon : 7, time : 2000});
				}
		}); 
	 }
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>