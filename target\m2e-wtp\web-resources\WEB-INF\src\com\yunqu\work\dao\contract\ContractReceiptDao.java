package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name = "ContractReceiptDao")
public class ContractReceiptDao extends AppDaoContext {
    @WebControl(name = "record", type = Types.RECORD)
    public JSONObject record() {
        String receiptId = param.getString("receipt.RECEIPT_ID");
        if (StringUtils.notBlank(receiptId)){
            return queryForRecord("select * from yq_contract_receipt where RECEIPT_ID = ?", receiptId);
        }else {
            return getJsonResult(new JSONObject());
        }
    }

    @WebControl(name = "receiptList" , type = Types.LIST)
    public JSONObject receiptList() {
        EasySQL sql = getEasySQL("select t1.* from yq_contract_receipt t1");
        sql.append(" where 1=1");
        sql.append(param.getString("contractId")," and t1.CONTRACT_ID = ?");
        sql.append(param.getString("stageId")," and t1.STAGE_ID = ?");
        setOrderBy(sql, " order by t1.CREATE_TIME ");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name  = "myReceiptList" , type = Types.LIST)
    public JSONObject myReceiptList(){
        EasySQL sql = getEasySQL("select t1.*,t2.CONTRACT_NAME,t2.CONTRACT_SIMPILE_NAME,t3.STAGE_NAME from yq_contract_receipt t1 ");
        sql.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("left join yq_contract_stage t3 ON t3.STAGE_ID = t1.STAGE_ID");
        sql.append("where 1=1");
        if ("year".equals(param.getString("receiptTime"))) {
            sql.append(EasyCalendar.newInstance().getYear(), "and t1.YEAR_ID = ?");
        } else if ("month".equals(param.getString("receiptTime"))) {
            sql.append(EasyCalendar.newInstance().getFullMonth(), "and t1.MONTH_ID = ?");
        }
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  t1.OWNER_ID = ? ");

        }

        setOrderBy(sql, " order by RECEIPT_DATE desc");
        return queryForPageList(sql.getSQL(), sql.getParams());

    }


    @WebControl(name="shouSumSelect",type=Types.DICT)
    public JSONObject shouSumSelect(){
        EasySQL sql = new EasySQL("select t1.STAGE_ID, SUM(COALESCE(t2.AMOUNT, 0)) AS SUN_SHOU_AMOUNT from yq_contract_stage t1");
        sql.append("left join yq_contract_receipt t2 on t1.STAGE_ID = t2.STAGE_ID");
        sql.append("WHERE 1=1 ");
        sql.append(param.getString("contractId")," and t1.CONTRACT_ID = ?");
        sql.append("GROUP BY t1.STAGE_ID ");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }


    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by t1.").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }




}
