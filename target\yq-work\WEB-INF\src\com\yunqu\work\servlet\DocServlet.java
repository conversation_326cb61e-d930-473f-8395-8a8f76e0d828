package com.yunqu.work.servlet;

import java.io.File;
import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.DocModel;
import com.yunqu.work.service.CommentService;
import com.yunqu.work.service.FlowService;
/**
 * 文档
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/doc/*")
public class DocServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		DocModel model=getModel(DocModel.class, "doc");
		model.addCreateTime();
		model.setCreator(getUserPrincipal().getUserId());
		model.set("order_index", 0);
		model.set("update_time",EasyDate.getCurrentDateString());
		model.set("update_by", getUserId());
		model.set("doc_code", FlowService.getService().getID(8));
		model.set("create_name", getUserName());
		model.set("remind_id",model.getString("DOC_ID"));
		if(StringUtils.isBlank(model.getString("MANAGER"))){
			model.set("MANAGER",getUserPrincipal().getUserId());
		}
		try {
			model.save();
		} catch (SQLException e) {
			return EasyResult.fail();
		}
		return EasyResult.ok(null,"创建成功.");
	}
	
	public EasyResult actionForDel(){
		String id=getJsonPara("id");
		try {
			String sql="select disk_path from yq_files where FILE_ID = ?";
			String diskPath=this.getQuery().queryForString(sql, id);
			if(StringUtils.notBlank(diskPath)){
				diskPath = getBaseDir()+diskPath;
				File file=new File(diskPath);
				if(file.exists()){
					boolean bl = file.delete();
					if(bl) {
						this.getQuery().executeUpdate(" delete from YQ_FILES where FILE_ID = ?", id);
					}
				}
			}
		} catch (SQLException e) {
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		DocModel model=getModel(DocModel.class, "doc");
		try {
			model.set("update_time",EasyDate.getCurrentDateString());
			model.set("update_by", getUserId());
			model.update();
			String comment=getJsonPara("comment");
			if(StringUtils.notBlank(comment)){
				CommentService.getService().addComment(getUserId(),getUserName(), model.getDocId(), comment,WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"),"doc");
			}
		} catch (SQLException e) {
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForAddFavorite(){
		EasyRecord record=new EasyRecord("YQ_FAVORITE","FAVORITE_ID");
		try {
			String fkId=getJsonPara("fkId");
			String sql="select count(1) from YQ_FAVORITE where fk_id = ?";
			int count=this.getQuery().queryForInt(sql, fkId);
			if(count>0){
				return EasyResult.fail("不能重复收藏!");
			}
			record.setPrimaryValues(RandomKit.uuid());
			record.set("fk_id", fkId);
			record.set("favorite_by", getUserId());
			record.set("favorite_time",EasyDate.getCurrentDateString());
			this.getQuery().save(record);
			
			this.getQuery().executeUpdate("update YQ_DOC set favorite_count = favorite_count + 1 where doc_id = ?",fkId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelFavorite(){
		EasyRecord record=new EasyRecord("YQ_FAVORITE","FAVORITE_ID");
		try {
			String favoriteId=getJsonPara("favoriteId");
			String docId=getJsonPara("docId");
			if(StringUtils.notBlank(favoriteId)){
				record.setPrimaryValues(favoriteId);
				this.getQuery().deleteById(record);
			}else{
				this.getQuery().executeUpdate("delete from YQ_DOC  where doc_id = ?",docId);
			}
			this.getQuery().executeUpdate("update YQ_DOC set favorite_count = favorite_count - 1 where doc_id = ?",docId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUseful(){
		String docId=getJsonPara("docId");
		try {
			this.getQuery().executeUpdate("update YQ_DOC set useful_count = useful_count +1 where doc_id = ?",docId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForNoUse(){
		String docId=getJsonPara("docId");
		try {
			this.getQuery().executeUpdate("update YQ_DOC set nouse_count = nouse_count +1 where doc_id = ?",docId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		DocModel model=getModel(DocModel.class, "doc");
		try {
			String sql="select file_id,disk_path from yq_files where FK_ID = ? ";
			List<JSONObject> list=this.getQuery().queryForList(sql, new Object[]{model.getDocId()},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject obj:list){
					String diskPath=obj.getString("DISK_PATH");
					if(StringUtils.notBlank(diskPath)){
						File file=new File(diskPath);
						if(file.exists()){
							file.delete();
						}
					}
					this.getQuery().executeUpdate(" delete from YQ_FILES where FILE_ID = ?", obj.getString("FILE_ID"));
				}
			}
			model.delete();
		} catch (SQLException e) {
			return EasyResult.fail();
		}
	   return EasyResult.ok();
	}
}





