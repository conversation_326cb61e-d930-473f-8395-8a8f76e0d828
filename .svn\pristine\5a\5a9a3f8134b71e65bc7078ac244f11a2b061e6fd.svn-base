<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="GoodsEditForm" class="form-horizontal" data-mars="GoodsDao.record" autocomplete="off" data-mars-prefix="goods.">
 		   		<input type="hidden" value="${param.goodsId}" name="goodsId"/>
 		   		<input type="hidden" value="${param.goodsId}" name="goods.GOODS_ID"/>
 		   		<input type="hidden" value="${param.orderId}" name="goods.ORDER_ID"/>
				<table class="table table-vzebra">
			        <tbody>
			            <tr>
		                    <td width="80px" class="required">大类</td>
		                    <td style="width: 45%">
		                    	<select name="goods.TYPE1" onchange="$('#code').render({data:{pcode:this.value}});" data-mars="GoodsDao.parentCategory" class="form-control">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
		                    <td width="80px">子类</td>
		                    <td style="width: 45%">
		                    	<select name="goods.TYPE2" id="code" data-mars="GoodsDao.childCategory" class="form-control">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
		                </tr>
		                <tr>
		                	<td class="required">物料名称</td>
		                    <td colspan="3">
								<input type="text" name="goods.NAME" class="form-control"/>
		                    </td>
		                </tr>
			            <tr>
		                    <td class="required">物料编号</td>
		                    <td>
		                   		 <input type="text" name="goods.GOODS_NO" class="form-control">
		                    </td>
		                    <td>采购类型</td>
		                    <td>
		                   		 <select name="goods.BUY_TYPE" class="form-control">
		                   		 	<option value="1">设备</option>
		                   		 	<option value="2">外包</option>
		                   		 	<option value="3">费用</option>
		                   		 	<option value="9">其它</option>
		                   		 </select>
		                    </td>
			            </tr>
		                  <tr>
		                	<td>物料类型</td>
		                    <td>
		                   		 <select name="goods.UNIT" class="form-control">
		                   		 	<option value="">请选择</option>
		                   		 	<option value="1">套件</option>
		                   		 	<option value="2">散件</option>
		                   		 </select>
		                    </td>
		                    <td>数量</td>
		                    <td>
		                   		 <input type="number" value="1" name="goods.NUMBER" class="form-control">
		                    </td>
		                </tr>
		                  <tr>
		                	<td>含税单价</td>
		                    <td>
		                   		 <input type="number" name="goods.PRICE" class="form-control">
		                    </td>
		                    <td>含税总价</td>
		                    <td>
		                   		 <input type="number" name="goods.TOTAL_PRICE" class="form-control">
		                    </td>
		                </tr>
		                  <tr>
		                	<td>发票类型</td>
		                    <td>
		                   		 <select name="goods.INVOICE_TYPE" class="form-control">
		                   		 	<option value="1">增值税专用发票</option>
		                   		 	<option value="2">普通发票</option>
		                   		 </select>
		                    </td>
		                    <td>税率</td>
		                    <td>
		                   		 <input type="number" name="goods.TAX_RATE" class="form-control">
		                    </td>
		                </tr>
		                <tr>
			            	<td>到货日期</td>
			               	<td colspan="3">
	                           <input class="form-control" name="goods.PLAN_ARRIVAL_TIME"/>
			               	</td>
			            </tr>
		                <tr>
			            	<td>备注说明</td>
			               	<td colspan="3">
	                           <textarea style="height: 80px;" class="form-control" name="goods.REMARK"></textarea>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="GoodsEdit.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("GoodsEdit");
	    
		GoodsEdit.goodsId='${param.goodsId}';
		
		var goodsId='${param.goodsId}';
		
		$(function(){
			$("#GoodsEditForm").render({success:function(result){

			}});  
		});
		
		GoodsEdit.ajaxSubmitForm = function(){
			if(form.validate("#GoodsEditForm")){
				if(GoodsEdit.goodsId){
					GoodsEdit.updateData(); 
				}else{
					GoodsEdit.insertData(); 
				}
			};
		}
		GoodsEdit.insertData = function(flag) {
			var data = form.getJSONObject("#GoodsEditForm");
			ajax.remoteCall("${ctxPath}/servlet/goods?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadDataList();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		GoodsEdit.updateData = function(flag) {
			var data = form.getJSONObject("#GoodsEditForm");
			ajax.remoteCall("${ctxPath}/servlet/goods?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadDataList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>