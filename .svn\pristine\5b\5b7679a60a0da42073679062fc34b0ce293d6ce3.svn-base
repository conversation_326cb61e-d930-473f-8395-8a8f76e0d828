<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>费用报销审批</title>
	<style>
		.layui-table td, .layui-table th{padding: 8px;}
		.layui-tab-content{background-color: #fff;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.gray-bg{background-color: #e8edf7;}
		.layui-icon{font-size: 12px;}
		.updateHzAmount{display: none;}
		.layui-table td, .layui-table th{font-size: 13px;line-height: 24px;color: #444;}
		.titleInfo td{
			height:24px!important;line-height: 20px!important;padding: 3px 4px!important;font-size: 14px!important;color: #444444;
		}
		.approveResult td{border-style:dashed!important;}
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo">
				<div data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
                <input type="hidden" name="flowCode" value="finance_bx"/>
                <input type="hidden" id="businessId" name="businessId" value="${param.businessId}"/>
                <input type="hidden" id="resultId" name="resultId" value="${param.resultId}"/>
				<div class="layui-row">
					<div class="layui-col-md12">
						<div class="layui-card">
						  <div class="layui-card-header text-c" style="font-size: 20px;height: 70px;line-height: 70px;">
						  		广州云趣信息技术有限公司-费用报销
						  		
						  		<div class="pull-right mr-20 hidden-print">
							  		<div class="btn-group btn-group-sm">
							  			<button class="btn btn-sm btn-default" type="button" onclick="printBx()">打印</button>
							  			<yq:user userId="${applyInfo.applyBy}">
							  				<c:if test="${applyInfo.applyState=='0'}">
								  				<button class="btn btn-default btn-xs" onclick="editFlow();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
							  				</c:if>
							  				<c:if test="${applyInfo.applyState=='21'}">
								  				<button class="btn btn-danger btn-xs" onclick="editFlow();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 修改申请</button>
							  				</c:if>
							  				<c:if test="${applyInfo.applyState=='0'||applyInfo.applyState=='21'}">
								  				<button class="btn btn-warning btn-xs" onclick="delFlow();" type="button"><i class="fa fa-trash-o" aria-hidden="true"></i> 删除</button>
							  				</c:if>
							  			</yq:user>
							  			<EasyTag:res resId="HZ_BX_MONEY">
								  			<yq:user userId="${applyInfo.applyBy}" hasPermission="false">
								  				<c:if test="${applyInfo.applyState=='0'||applyInfo.applyState=='21'}">
									  				<button class="btn btn-warning btn-xs" onclick="delFlow();" type="button"><i class="fa fa-trash-o" aria-hidden="true"></i> 删除</button>
								  				</c:if>
								  			</yq:user>
											<button class="btn btn-info btn-xs" onclick="$('.updateHzAmount').show();$('.hzAmountVal').hide();" type="button">核准金额</button>
							  			</EasyTag:res>
									</div>
					  		</div>
						  </div>
						  <div class="layui-card-body" style="margin-bottom: -15px;">
						    	<table class="table table-edit table-vzebra titleInfo">
							  		<tr>
							  			<td style="width: 120px;">当前处理人：</td>
							  			<td style="width: 40%;">
							  				<span name="checkName"></span>
							  			</td>
							  			<td style="width: 120px;">申请时间：</td>
							  			<td>
							  				<span name="applyTime"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td style="width: 120px;">当前状态：</td>
							  			<td>
							  				<span name="nodeName"></span>
							  			</td>
							  			<td style="width: 120px;">单号：</td>
							  			<td>
							  				<span name="applyNo"></span>
							  			</td>
							  		</tr>
							  	</table>
						  </div>
						  <div class="layui-card-body detailInfo">
						    	<table class="table table-vzebra">
							  		<tr>
							  			<td style="width: 120px;">申请人</td>
							  			<td style="width: 40%;">
							  				<span name="applyName"></span>
							  			</td>
							  			<td style="width: 120px;">部门</td>
							  			<td>
							  				<span name="deptName"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>说明</td>
							  			<td colspan="3">
											<span name="applyRemark"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>付款方式</td>
							  			<td colspan="3">
							  				<span name="business.PAY_TYPE"></span>
							  				<span name="business.CONTACT_UNIT" class="ml-5"></span>
							  				<span name="business.CONTACT_BANK" class="ml-5"></span>
							  				<span name="business.CONTACT_BANK_NO" class="ml-5"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>费用总计</td>
							  			<td>
							  				<span name="business.HZ_MONEY" data-fn="toThousandsFormates"></span>
							  			</td>
							  			<td>冲帐金额</td>
							  			<td>
							  				<span name="business.REVERSE_MONEY"></span>
							  			</td>
							  		</tr>
							  		<tr>
							  			<td>应付金额</td>
							  			<td>
							  				<span name="business.PAY_MONEY" data-fn="toThousandsFormates"></span>
							  			</td>
							  			<td>大写</td>
							  			<td>
							  				<span name="business.PAY_MONEY_DX"></span>
							  			</td>
							  		</tr>
							  	</table>
						  </div>
						</div>
					</div>
				</div>
				<div class="layui-row" style="margin-top: 0px;">
					<div class="layui-col-md12">
						<div class="layui-card">
						  <div class="layui-card-body">
							 <div class="layui-table-box bxItems" style="min-height: 100px;">
								 <table class="layui-table" style="margin-top: -1px;">
								   <thead>
								    <tr>
								      <th>序号</th>
								      <th>费用日期</th>
								      <th>费用类型</th>
								      <th class="hidden-print">费用说明</th>
								      <th>发票类型</th>
								      <th>金额</th>
								      <th>核准金额</th>
								      <th>部门</th>
								      <th>项目号</th>
								      <th>产品线</th>
								      <th class="visible-print">会计科目</th>
								      <th>税率(%)</th>
								      <th>税金</th>
								      <th>金额</th>
								    </tr> 
								  </thead>
								  <tbody data-mars="BxDao.bxItems" data-template="bxItems">
								  
								  
								  </tbody>
								  <tbody>
								  	<tr>
								  		<td colspan="11" style="text-align: right;">合计</td>
								  		<td><span name="business.TAX_MONEY" data-fn="toThousandsFormates"></span></td>
								  		<td><span name="business.NO_TAX_MONEY" data-fn="toThousandsFormates"></span></td>
								  	</tr>
								  </tbody>
								</table>
							</div>
							 <div class="layui-table-box" style="min-height: 100px;overflow-x:auto;">
							 	<table class="layui-table approveResult" lay-skin="line" style="margin-top: 10px;">
								  	<tbody data-mars="FlowDao.approveResult" data-template="approveList"></tbody>
								</table>
							</div>
					  </div>
					</div>
				</div>
			</div>
			
			<c:if test="${param.isApprovalFlag=='1'}">
			<div class="layui-row hidden-print" style="margin-top: 0px;">
				<div class="layui-col-md12">
					<div class="layui-card">
					  <div class="layui-card-body">
					 		<table class="table table-edit table-vzebra">
							   	<tr>
							   		<td style="width: 120px;">审批结果：</td>
							   		<td>
							   			<label class="radio radio-inline radio-success">
							            	<input type="radio" value="1" name="checkResult" checked="checked"> <span>通过</span>
							            </label>
							            <label class="radio radio-inline radio-success">
							            	<input type="radio" value="2" name="checkResult"> <span>退回</span>
							            </label>
							   		</td>
							   	</tr>
							   	<tr>
							   		<td style="vertical-align: top;margin-top: 20px;">审批描述：</td>
							   		<td>
							   			<textarea style="height: 80px" name="checkDesc" class="form-control"></textarea>
							   		</td>
							   	</tr>
							   	<tr>
							   		<td colspan="2" style="text-align: left;">
								   		<button class="btn btn-info btn-sm" style="margin-left:120px;" type="button" onclick="addCheckOrder();">提交</button>
							   		</td>
							   	</tr>
							  </table>
							   <br>
							   <br>
							   <br>
							   <br>
							</div>
					  </div>
					</div>
			   </div>
			  </c:if>
	</div>			  
</form>
</EasyTag:override>

<EasyTag:override name="script">

<script id="bxItems" type="text/x-jsrender">
  	 {{for data}}
  		<tr>
  			<td>{{:#index+1}}</td>
  			<td>{{:BX_DATE}}</td>
  			<td>{{:FEE_TYPE}}</td>
  			<td class="hidden-print">{{:FEE_DESC}} <a href="javascript:void(0);" onclick="itemDetail('{{:ITEM_ID}}')" style="text-decoration:underline;color:red;" class="pull-right">详情</a></td>
  			<td>{{:INVOICE_TYPE}}</td>
  			<td>{{call:AMOUNT fn='toThousandsFormates'}}</td>
  			<td><span class="hzAmountVal">{{call:HZ_AMOUNT fn='toThousandsFormates'}}</span><input type="number" class="form-control input-sm updateHzAmount" value="{{:HZ_AMOUNT}}" style="width:80px;" onchange="updateHzAmount(this.value,'{{:ITEM_ID}}','{{:BUSINESS_ID}}')"/></td>
  			<td>{{:DEPT_NAME}}</td>
  			<td>{{:CONTRACT_NAME}}</td>
  			<td>{{:PRODUCT_LINE}}</td>
  			<td class="visible-print">{{:SUBJECT_NO}}&nbsp;{{:SUBJECT_NAME}}</td>
  			<td>{{:TAX_RATE}}</td>
  			<td>{{call:TAXES fn='toThousandsFormates'}}</td>
  			<td>{{call:R_AMOUNT fn='toThousandsFormates'}}</td>
  		</tr>
  	  {{/for}}
</script>

<script id="approveList3" type="text/x-jsrender">
  	 {{for data}}
  		<tr {{if CHECK_RESULT=='0'}} class="hidden-print" {{/if}}>
  			<td>{{:#index+1}}</td>
  			<td>{{:NODE_NAME}}</td>
  			<td>{{:CHECK_NAME}}</td>
  			<td>{{if NODE_ID!='0'}} {{call:CHECK_RESULT fn='checkResultLable'}} {{/if}}</td>
  			<td class="hidden-print">{{:GET_TIME}}</td>
  			<td>{{:CHECK_TIME}}</td>
  			<td>{{:CHECK_DESC}}</td>
  		</tr>
  	  {{/for}}
	 {{if data.length==0}}
		<tr><td  colspan="7">暂无数据</td></tr>
	{{/if}}
</script>

<script id="approveList" type="text/x-jsrender">
  	 {{for data}}
  		<tr {{if CHECK_RESULT=='0'}} class="hidden-print" {{/if}}>
  			<td style="text-align:left;width:60%;">{{if NODE_ID!='0'}} {{call:CHECK_RESULT fn='checkResultLable'}}   {{if CHECK_DESC}}({{:CHECK_DESC}}) {{/if}} {{/if}}</td>
  			<td style="text-align:right;">{{:NODE_NAME}}/{{:CHECK_NAME}}/{{:CHECK_TIME}}</td>
  		</tr>
  	  {{/for}}
	 {{if data.length==0}}
		<tr><td  colspan="2">暂无数据</td></tr>
	{{/if}}
</script>

	<script type="text/javascript">
		
		$(function(){
			var device = layui.device();
			if(device.mobile){
				$('.table,.layui-table-box').addClass('table-responsive');
				$('.table').parent().addClass('table-responsive');
				$('.table,.layui-table').addClass('text-nowrap');
				$('.layui-card-header,.btn-group').hide();
			}
			layui.use(['element','form'],function(){
				var element = layui.element;
				var form = layui.form;
				form.render();
			});
			
			$("#flowForm").render({success:function(result){

			}});
			
		});
		
		function checkResultLable(checkResult){
			 var json = {'1':'<span class="text-success">通过</span>','2':'<span class="text-waring">拒绝</span>'};
			 return json[checkResult]||'<span class="text-danger">待审批</span>';
		}
		
		function addCheckOrder(){
			var checkResult = $("[name='checkResult']:checked").val();
			var checkDesc = $("[name='checkDesc']").val();
			var businessId = $("#businessId").val();
			var resultId = $("#resultId").val();
			if(businessId==''||resultId==''){
				layer.msg('请从审批页面入口进来',{icon:7,offset:'50px'});
				return;
			}
			if(checkResult==undefined){
				layer.msg('请选择审批结果',{icon:7,offset:'50px'});
				return;
			}
			if(checkResult=='2'&&checkDesc==''){
				layer.msg('请输入退回理由',{icon:7,offset:'50px'});
				return;
			}else{
				if(checkDesc==''){
					checkDesc='同意';
				}
			}
			var data = {flowCode:'finance_bx',checkResult:checkResult,checkDesc:checkDesc,businessId:businessId,resultId:resultId};
			ajax.remoteCall("${ctxPath}/servlet/bx?action=doApprove",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.closeTab({callback:function(){
							popup.openTab({id:'flow_my_todo',url:'/yq-work/pages/flow/my/flow-todo.jsp',title:'我的待办',reload:true})
						}});
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function editFlow(){
			popup.closeTab({callback:function(){
				var businessId = $("#businessId").val();
				popup.openTab({id:'bxFlowEdit',url:'/yq-work/servlet/flow?id=finance_bx&mode=edit&businessId='+businessId,data:{},title:'编辑表单'});
			}});
		}
		
		function itemDetail(itemId){
			var data = {itemId:itemId};
			popup.layerShow({id:'showFeeInfo',title:'费用说明',area:['550px','400px'],offset:'30px',url:'/yq-work/pages/flow/bx/include/fee-detail.jsp',data:data});
		}
		
		function delFlow(){
			layer.confirm('确认删除吗，不可恢复',{offset:'20px'},function(index){
				layer.close(index);
				var businessId = $("#businessId").val();
				ajax.remoteCall("${ctxPath}/servlet/bx?action=delApply",{businessId:businessId,flowCode:'finance_bx'},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							popup.closeTab();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		function updateHzAmount(hzAmount,itemId,businessId){
			var payMoneyDx = digitUppercase(hzAmount);
			ajax.remoteCall("${ctxPath}/servlet/bx?action=updateHzMoney",{hzAmount:hzAmount,payMoneyDx:payMoneyDx,itemId:itemId,businessId:businessId},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function printBx(){
			var nodeName = $("[name='nodeName']").text();
			if(nodeName!='会计主管'){
				layer.confirm("审批进度是【会计主管】才可以打印,是否继续",function(index){
					layer.close(index);
					window.print();
				});
			}else{
				window.print();
			}
		}
		
		
	    function toThousandsFormates(num) {
	        // 判断传进来的数字是否为非空数字
	       if (!isNaN(parseFloat(num))) {
	            var reg = /\./g
	            var newNum = Number(Number(num).toFixed(2)).toLocaleString()
	            // 判断转换后的数字是否带有小数
	            if (reg.test(newNum)) {
	                var numArr = newNum.split('.')
	                // 判断小数点后数字长度为1，则自动补0
	                numArr[1] = numArr[1].length === 1 ? numArr[1] + '0' : numArr[1]
	                return numArr.join('.')
	            } else {
	                // 整数直接在后面补上0.00
	                return newNum + '.00'
	            }

	        } else {
	            return ''
	        }
	    }
	    
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>