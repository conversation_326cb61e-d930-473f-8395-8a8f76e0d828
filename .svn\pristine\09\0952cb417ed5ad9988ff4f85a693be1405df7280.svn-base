<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
	<style>
		.layui-btn+.layui-btn{margin-left: 2px;}
		.layui-table-cell{padding: 0 6px;}
	    /* tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;} */
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="status" type="hidden" value="${param.status}"/>
			<input name="taskState" id="taskState" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span id="title">任务管理</span></h5>
	          		     <div class="input-group input-group-sm"  style="width: 180px">
							 <span class="input-group-addon">任务名称</span>	
							 <input type="text" name="taskName" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm"  style="width: 150px">
							 <span class="input-group-addon">任务类型</span>	
							<select data-mars-reload="false" name="taskTypeId" onchange="list.query()" data-mars="TaskDao.taskType" class="form-control">
								<option value="">请选择</option>
							</select>
					     </div>
	          		     <div class="input-group input-group-sm"  style="width: 160px">
							 <span class="input-group-addon">项目</span>	
							 <input type="hidden" name="projectId" class="form-control input-sm">
							 <input type="text" id="projectId" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()"> + 发起任务</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					   <div class="layui-tab layui-tab-brief" lay-filter="task" data-mars="TaskDao.myTaskListCountStat">
						  <ul class="layui-tab-title">
						    <li data-state="" class="layui-this">全部 <span class="layui-badge layui-bg-cyan ALL">0</span></li>
						    <li data-state="10">待办中 <span class="layui-badge layui-bg-orange" id="A">0</span></li>
						    <li data-state="20">进行中 <span class="layui-badge" id="B">0</span></li>
						     <li data-state="25">超时未完成 <span class="layui-badge" id="E">0</span></li>
						    <li data-state="30">已完成 <span class="layui-badge layui-bg-blue" id="C">0</span></li>
						    <li data-state="40">已验收 <span class="layui-badge layui-bg-green" id="D">0</span></li>
						  </ul>
						  <div class="layui-tab-content" style="padding: 0px;">
						  	<div class="layui-tab-item layui-show"></div>
						  	<div class="layui-tab-item"></div>
						  	<div class="layui-tab-item"></div>
						  	<div class="layui-tab-item"></div>
						  	<div class="layui-tab-item"></div>
						  	<div class="layui-tab-item"></div>
						  </div>
						</div> 
					    <table class="layui-hide" id="myTaskTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
			{{if TASK_STATE==40}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
			 {{else TASK_STATE==42}}
  				<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.detail">重新处理</a>
			 {{else TASK_STATE==10}}
  				<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="list.detail">处理任务</a>
			 {{else TASK_STATE==30}}
  				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看详情</a>
			 {{else}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">更改进度</a>
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="bar2">
			{{if TASK_STATE<40}}
				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>
			{{/if}}  			
			{{if TASK_STATE==30}}
  				<a class="layui-btn layui-btn-success layui-btn-xs" lay-event="list.detail">验收</a>
			 {{else TASK_STATE==42}}
  				<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.detail">重新验收</a>
			 {{else TASK_STATE==10}}
				{{if VIEW_COUNT<=20}}<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.delTask">删除</a>{{/if}}
			{{else TASK_STATE<40}}

			{{else TASK_STATE==40}}
  				{{if TASK_AUTH==0}}<a class="layui-btn layui-btn-xs" lay-event="list.cannelShare">取消分享</a>{{else}}<a class="layui-btn layui-btn-xs" lay-event="list.share">分享</a>{{/if}}
			 {{else}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
			{{/if}}
		</script>
		<script type="text/html" id="bar3">
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		var status='${param.status}';
		var firstLoad=true;
		var list={
			init1:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					title:'我负责的任务',
					skin:'line',
					autoSort:false,
					id:'myTaskTable',
					cols: [[
						{
						 	type: 'checkbox',
							title: '序号',
							width:36,
							align:'left'
						 },{
						    field: 'CREATOR',
							title: '发起人',
							align:'left',
							width:70,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['CREATOR']+'\')">'+getUserName(row.CREATOR)+'</a>';
							}
						  },{
						    field: 'TASK_NAME',
							title: '任务名称',
							event:'list.myTaskDetail',
							minWidth:220,
							align:'left',
							style:'color:#1E9FFF;cursor:pointer',
							templet:function(row){
								var id = row['P_TASK_ID'];
								if(id){
									return "<i class='layui-icon layui-icon-senior'></i>"+row['TASK_NAME'];
								}else{
									return row['TASK_NAME'];
								}
							}
						},{
						    field: 'PROJECT_NAME',
							title: '项目名称',
							event:'list.myTaskDetail',
							align:'left',
							sort:true,
							minWidth:150
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							sort:true,
							width:80,
							event:'list.updateState',
							templet:function(row){
								var state = row.TASK_STATE;
								var progress = row.PROGRESS;
								if(state==20&&progress>0){
									return taskStateLabel(row.TASK_STATE,progress+"%");
								}else{
									return taskStateLabel(row.TASK_STATE);
								}
							}
						},{
						    field: 'TASK_TYPE_ID',
							title: '任务类型',
							align:'center',
							sort:true,
							width:80,
							templet:function(row){
								return getText(row['TASK_TYPE_ID'],'taskTypeId');
							}
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'center',
							sort:true,
							width:70,
							templet:function(row){
								return taskTextLevel(row.TASK_LEVEL);
							}
						},{
						    field: 'CREATE_TIME',
							title: '发起时间',
							align:'center',
							sort:true,
							width:90,
							templet:function(row){
								var time= row['CREATE_TIME'];
								return cutText(time,12,'');
							}
						},{
						    field: 'DEADLINE_AT',
							title: '截止时间',
							align:'left',
							sort:true,
							width:100,
							templet:function(row){
								var time= row['DEADLINE_AT'];
								var state= row['TASK_STATE'];
								if(state<30){
									return timeBetween(time);
								}
								return cutText(time,12,'');
							}
						},{
							title: '操作',
							align:'center',
							width:80,
							hide:true,
							templet:function(row){
							    return renderTpl('bar1',row);
							}
						}
					]],done:function(result){
						if(firstLoad)$(".ALL").text(result.totalRow);
						firstLoad=false;
						table.on('sort(myTaskTable)', function(obj){
							  $("#searchForm").queryData({id:'myTaskTable',jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
						});
					}}
				);
			},
			init2:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					skin:'line',
					id:'myTaskTable',
					autoSort:false,
					title:'我发起的任务',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left',
							width:40
						 },{
						    field: 'ASSIGN_USER_ID',
							title: '负责人',
							align:'left',
							width:70,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['ASSIGN_USER_ID']+'\')">'+getUserName(row.ASSIGN_USER_ID)+'</a>';
							}
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							minWidth:220,
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer',
							templet:function(row){
								var id = row['P_TASK_ID'];
								if(id){
									return "<i class='layui-icon layui-icon-senior'></i>"+row['TASK_NAME'];
								}else{
									return row['TASK_NAME'];
								}
							}
						},{
						    field: 'PROJECT_NAME',
							title: '项目名称',
							minWidth:150,
							event:'list.detail',
							align:'left'
						 },{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							width:80,
							templet:function(row){
								var state = row.TASK_STATE;
								var progress = row.PROGRESS;
								if(state==20&&progress>0){
									return taskStateLabel(row.TASK_STATE,progress+"%");
								}else{
									return taskStateLabel(row.TASK_STATE);
								}
							}
						},{
						    field: 'VIEW_COUNT',
							title: '浏览数',
							align:'center',
							sort:true,
							width:70
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							sort:true,
							align:'center',
							width:70,
							templet:function(row){
								return taskTextLevel(row.TASK_LEVEL);
							}
						},{
						    field: 'CREATE_TIME',
							title: '发起时间',
							sort:true,
							width:90,
							align:'left',
							templet:function(row){
								var time= row['CREATE_TIME'];
								return cutText(time,12,'');
							}
						},{
						    field: 'DEADLINE_AT',
							title: '截止时间',
							sort:true,
							align:'center',
							width:90,
							templet:function(row){
								var time= row['DEADLINE_AT'];
								return cutText(time,12,'');
							}
						},{
							title: '操作',
							align:'left',
							cellMinWidth:90,
							width:90,
							templet:function(row){
							    return renderTpl('bar2',row);
							}
						}
					]],done:function(result){
						if(firstLoad)$(".ALL").text(result.totalRow);
						firstLoad=false;
						table.on('sort(myTaskTable)', function(obj){
							  $("#searchForm").queryData({id:'myTaskTable',jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
						});
					}}
				);
			},
			init3:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					skin:'line',
					id:'myTaskTable',
					title:'我参与的任务',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							minWidth:220,
							event:'list.detail',
							align:'left',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'PROJECT_NAME',
							title: '项目名称',
							minWidth:150,
							event:'list.detail',
							align:'left'
						 },{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:90,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['CREATOR']+'\')">'+getUserName(row.CREATOR)+'</a>';
							}
						},{
						    field: 'ASSIGN_USER_ID',
							title: '负责人',
							align:'center',
							width:80,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['ASSIGN_USER_ID']+'\')">'+getUserName(row.ASSIGN_USER_ID)+'</a>';
							}
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							width:80,
							templet:function(row){
								return taskStateLabel(row.TASK_STATE);
							}
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'center',
							width:70,
							templet:function(row){
								return taskTextLevel(row.TASK_LEVEL);
							}
						},{
						    field: 'CREATE_TIME',
							title: '创建时间',
							align:'center',
							width:120,
							templet:function(row){
								var time= row['CREATE_TIME'];
								return cutText(time,12,'');
							}
							
						},{
						    field: 'UPDATE_TIME',
							title: '更新时间',
							align:'left',
							hide:true,
							width:100
						},{
							title: '操作',
							align:'left',
							hide:true,
							width:80,
							toolbar: '#bar3'
						}
					]],done:function(result){
						if(firstLoad)$(".ALL").text(result.totalRow);
						firstLoad=false;
					}}
				);
			},
			init4:function(){
				$(".layui-tab-brief").remove();
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					skin:'line',
					id:'myTaskTable',
					title:'任务参考解决',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							minWidth:220,
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'FINISH_TIME',
							title: '完成时间',
							align:'center',
							width:160,
							templet:function(row){
								var time= row['FINISH_TIME'];
								return cutText(time,12,'');
							}
						},{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:120,
							templet:function(row){
								return getUserName(row.CREATOR);
							}
						},{
						    field: 'ASSIGN_USER_ID',
							title: '负责人',
							align:'left',
							width:120,
							templet:function(row){
								return getUserName(row.ASSIGN_USER_ID);
							}
						},{
						    field: 'UPDATE_TIME',
							title: '更新时间',
							width:160,
							align:'left',
							templet:function(row){
								var time= row['UPDATE_TIME'];
								return cutText(time,12,'');
							}
						}
					]],success:function(){
						$(".layui-tab-brief").remove();
					}}
				);
			},
			query:function(){
				firstLoad=true;
				$("#searchForm").render();
				$("#searchForm").queryData({id:'myTaskTable',jumpOne:true});
			},
			edit:function(data){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'编辑任务',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务'});
			},
			detail:function(data){
				var state = data['TASK_STATE'];
				if(state==40){
					popup.openTab({id:"task_"+data.TASK_ID,type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/task/task-detail.jsp?isDiv=0',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
				}else{
					popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
				}
			},
			myTaskDetail:function(data){
				list.detail(data);
			},
			share:function(data){
				ajax.remoteCall("${ctxPath}/servlet/task?action=share",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			},
			delTask:function(data){
				layer.confirm("是否确认删除?",{offset:'20px',icon:7},function(index){
					layer.close(index)
					ajax.remoteCall("${ctxPath}/servlet/task?action=delTask",{taskId:data['TASK_ID']},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								layer.closeAll();
								list.query();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			updateState:function(data){
				if(data['TASK_STATE']==20){
					//todo
				}
			},
			cannelShare:function(data){
				ajax.remoteCall("${ctxPath}/servlet/task?action=cannelShare",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		function reloadTaskList(){
			$("#searchForm").queryData({id:'myTaskTable',jumpOne:true});
		}
		layui.use('element',function(){
			var element=layui.element;
			element.on('tab(task)', function(elem){
				var  state=$(this).data('state');
				$("#taskState").val(state);
				if((state==10||state==20)&&status==1){
					//显示处理按钮
				}
				reloadTaskList();
			});
		  });
		$(function(){
			
			layui.config({
				  base: '${ctxPath}/static/js/'
			}).use('tableSelect'); //加载自定义模块
			
			layui.use(['element','tableSelect'], function(){
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#projectId',
					searchKey: 'projectName',
					checkedKey: 'PROJECT_ID',
					page:true,
					searchPlaceholder: '请输入项目名称',
					table: {
						mars: 'ProjectContractDao.projectList',
						cols: [[
							{ type: 'radio' },
							{ field: 'PROJECT_ID',hide:true,title: 'ID'},
							{ field: 'PROJECT_NAME', title: '名称', width: 320 },
							{ field: 'PROJECT_NO', title: '编号', width: 150 }
						]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.PROJECT_NAME)
							ids.push(item.PROJECT_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
					},
					clear:function(elem){
					  elem.prev().val("");
					}
				});
			});
			
			$("#searchForm").render({success:function(){
				if(status==1){
					list.init1();
					$("#title").text("我负责的任务");
				}
				if(status==2){
					list.init2();
					$("#title").text("我创建的任务");
				}
				if(status==3){
					list.init3();
					$("#title").text("我参与的任务");
				}
				if(status==4){
					list.init4();
					$("#title").text("范例任务");
				}
			}});
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>