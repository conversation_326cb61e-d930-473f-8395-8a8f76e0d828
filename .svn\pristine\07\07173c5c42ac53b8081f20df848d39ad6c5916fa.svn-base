package com.yunqu.work.utils;

import java.io.IOException;

import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class ZendaoApi {
	
	private static String baseUrl = "http://172.16.68.152:6688/api.php/v1/";
	
	public static String getToken(){
		Connection connection = Jsoup.connect(baseUrl+"tokens");
		try {
			JSONObject data = new JSONObject();
			data.put("account","100133");
			data.put("password","Rocky@100133");
			connection.requestBody(data.toJSONString());
	 		Response response = connection.header("Content-Type", "application/json").timeout(10000).ignoreContentType(true).method(Method.POST).execute();
	 		if(response.statusCode()==200||response.statusCode()==201){
	 			String strResult  = response.body();
	 			LogUtils.getLogger().info("getToken result:"+strResult);
	 			JSONObject result = JSONObject.parseObject(strResult);
	 			if(result.containsKey("error")){
	 				LogUtils.getLogger().error("ZendaoApi.token is error:"+data.toJSONString());
	 				return null;
	 			}else{
	 				return result.getString("token");
	 			}
	 		}
		} catch (IOException e) {
		   LogUtils.getLogger().error(e.getMessage(),e);
		}
		return null;
	}
	
	
	public static JSONObject addProject(String projectName,String projectCode) {
		Connection connection = Jsoup.connect(baseUrl+"projects");
		try {
			JSONObject data = new JSONObject();
			data.put("name",projectName);
			data.put("code",projectCode);
			data.put("parent",1);
			JSONArray array = new JSONArray();array.add(1);
			data.put("products",array);
			data.put("begin","2024-07-01");
			data.put("end","2059-12-31");
			connection.requestBody(data.toJSONString());
			connection.header("Token", getToken());
	 		Response response = connection.header("Content-Type", "application/json").timeout(10000).ignoreContentType(true).method(Method.POST).execute();
	 		if(response.statusCode()==200||response.statusCode()==201){
	 			String strResult  = response.body();
	 			JSONObject result = JSONObject.parseObject(strResult);
	 			LogUtils.getLogger().info("addProject result:"+result.toJSONString());
	 			if(result.containsKey("error")){
	 				LogUtils.getLogger().error("ZendaoApi.addProject is error:"+data.toJSONString());
	 			}else{
	 			   return result;
	 			}
	 		}
		} catch (IOException e) {
		   LogUtils.getLogger().error(e.getMessage(),e);
		}
		return null;
	}
	public static JSONObject addUser(String account,String password,String realname) {
		Connection connection = Jsoup.connect(baseUrl+"users");
		try {
			JSONObject data = new JSONObject();
			data.put("account",account);
			data.put("password",password);
			data.put("realname",realname);
			connection.requestBody(data.toJSONString());
			connection.header("Token", getToken());
			Response response = connection.header("Content-Type", "application/json").timeout(10000).ignoreContentType(true).method(Method.POST).execute();
			if(response.statusCode()==200||response.statusCode()==201){
				String strResult  = response.body();
				JSONObject result = JSONObject.parseObject(strResult);
				LogUtils.getLogger().info("addUser result:"+result.toJSONString());
				if(result.containsKey("error")){
					LogUtils.getLogger().error("ZendaoApi.addUser is error:"+data.toJSONString());
				}else{
					return result;
				}
			}
		} catch (Exception e) {
			LogUtils.getLogger().error(e.getMessage(),e);
		}
		return null;
	}
	
}
