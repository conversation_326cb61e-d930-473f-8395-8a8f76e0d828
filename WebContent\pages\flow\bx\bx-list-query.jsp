<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>报销list</title>
    <style type="text/css">
        .layui-table-cell{
            height: 32px;
            line-height: 32px;
            padding: 1px 8px;
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            box-sizing: border-box;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="bxListForm" class="form-inline" autocomplete="off">
        <input name="nothing" type="hidden" data-mars="CommonDao.today">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <input type="hidden" name="timeType" id="timeType" value="${param.timeType}">
                    <div class="btn-group-sm btn-group time-type" style="width: 140px;">
                        <button class="btn btn-default btn-xs" id="btn_bx_date" type="button" value="bx_date">报销时间</button>
                        <button class="btn btn-default btn-xs" id="btn_recorded_date" type="button" value="recorded_date">入账时间</button>
                    </div>
                    <div class="input-group input-group-sm" style="width: 240px">
                        <span class="input-group-addon">时间</span>
                        <input name="startDate" id="startDate" size="12"
                               value="${param.startDate}" class="form-control input-sm"
                               onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
                        <span class="input-group-addon">-</span>
                        <input name="endDate" id="endDate"
                               value="${param.endDate}" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                               size="12" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 190px;display: inline-flex">
                        <span class="input-group-addon">报销部门</span>
                        <input type="text" name="deptName" id="deptName"
                               value="${param.deptName}" class="form-control input-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="btnSelectDept()"><span>选择</span></button>
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px">
                        <span class="input-group-addon">费用类型</span>
                        <input type="text" name="feeType" value="${param.feeType}" class="form-control input-sm">
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group input-group-sm" style="width: 140px;">
                        <span class="input-group-addon">报销人</span>
                        <input id="bxBy" name="bxBy" value="${param.bxBy}" type="hidden">
                        <input onclick="singleUser(this);" value="${param.applyName}" class="form-control input-sm">
                    </div>

                    <div class="input-group input-group-sm" style="width: 240px;display: inline-flex">
                        <span class="input-group-addon">合同名称</span>
                        <input type="text" id="contractName" name="contractName"
                               value="${param.contractName}" class="form-control input-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="btnSelectContract()"><span>选择</span></button>
                    </div>
                    <div class="input-group input-group-sm" style="width: 190px">
                        <span class="input-group-addon">费用说明</span>
                        <input type="text" name="feeDesc" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm pull-right">
                        <i class="layui-icon layui-icon-tips" lay-tips="只统计审批完成的报销明细，在途流程不计入。"></i>
                        <button type="button" class="btn btn-sm btn-default ml-5" onclick="bxItemList.query()"><span class="glyphicon glyphicon-search"></span> <span>搜索</span></button>
                        <button type="button" class="btn btn-sm btn-default ml-5" onclick="bxItemList.clearForm()">清空</button>
                    </div>
                </div>
            </div>
            <div class="ibox-content">
                <table class="layui-hide" id="bxListTable"></table>
            </div>
        </div>
    </form>
    <p class="layer-foot text-c">
        <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll();"> 关闭</button>
    </p>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script>

        $(function () {
            $("#bxListForm").render({
                success: function () {
                    bxItemList.init();
                    var timeType = $('[name="timeType"]').val();
                    $("#btn_"+timeType).addClass('btn-info');

                    $('.time-type button').click(function () {
                        $('[name="timeType"]').val($(this)[0].value);
                        $(this).siblings().removeClass('btn-info');
                        $(this).siblings().addClass('btn-default');
                        $(this).addClass('btn-info');
                        bxItemList.query();
                    });

                }
            });
        });

        var bxItemList = {
            init: function () {
                $("#bxListForm").initTable({
                    mars: 'BxDao.bxItemList',
                    id: 'bxListTable',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '420px',
                    autoSort: false,
                    totalRow: true,
                    toolbar: true,
                    cols: [[
                        {
                            type: 'numbers',
                            align: 'left',
                            totalRowText: "合计"
                        }, {
                            field: 'BX_DATE',
                            title: '费用日期',
                            sort: true,
                            width: 100
                        }, {
                            field: 'RECORDED_DATE',
                            title: '入账日期',
                            sort: true,
                            width: 100
                        }, {
                            field: 'APPLY_BY',
                            title: '报销人',
                            width: 80,
                            templet: function (d) {
                                return getUserName(d.APPLY_BY);
                            }
                        }, {
                            field: 'DEPT_NAME',
                            title: '部门',
                            width: 100
                        }, {
                            field: 'FEE_TYPE',
                            title: '费用类型',
                            minWidth: 100,
                            maxWidth: 200
                        }, {
                            field: 'R_AMOUNT',
                            title: '含税金额',
                            sort: true,
                            width: 100,
                            totalRow: true,
                        }, {
                            field: 'AMOUNT',
                            title: '税后金额',
                            sort: true,
                            width: 100,
                            hide: true,
                            totalRow: true,
                        }, {
                            field: 'TAXES',
                            title: '税金',
                            sort: true,
                            width: 80,
                            hide: true,
                            totalRow: true,
                        }, {
                            field: 'CONTRACT_NAME',
                            title: '合同名称',
                            minWidth: 200,
                        }, {
                            field: 'FEE_DESC',
                            title: '费用说明',
                            minWidth: 80,
                            width: 130
                        }
                    ]],
                    done: function (res) {

                    }
                });
            },

            detail: function (data) {
            },
            query: function () {
                var timeType = $('#timeType').val();
                $("#bxListForm").queryData({id: 'bxListTable', initSort: {field: timeType, type: 'DESC'}, data: {field: timeType, order: 'DESC',"timeType":timeType}});
            },

            clearForm: function () {
                $('#bxListForm input').val('');
                $("#bxListForm .select-clear").click();
                bxItemList.query();
            }

        }
        

        function btnSelectDept() {
            var el = document.getElementById("deptName");
            singleDept(el);
        }

        function btnSelectContract() {
            var el = document.getElementById("contractName");
            singleContract(el);
        }

        function selctCallBack(id, row) {
            $("[name='contractName']").val(row['CONTRACT_NAME']);
        }

        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>