<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
 <table lay-filter="goodsList" class="layui-table" data-mars="OrderDao.goodsList" data-template="list-template" data-container="goodsList">
		<thead>
			<tr>
				<th lay-data="{field:'INDEX',width:40}">序号</th>
				<th lay-data="{field:'GOODS_NO'}">编号</th>
				<th lay-data="{field:'NAME'}">名称</th>
				<th lay-data="{field:'NUMBER'}">数量</th>
				<th lay-data="{field:'TAX_RATE'}">税率</th>
				<th lay-data="{field:'PRICE'}">含税单价</th>
				<th lay-data="{field:'TOTAL_PRICE'}">含税总价</th>
				<th lay-data="{field:'CREATE_USER_NAME',width:70}">添加人</th>
				<th lay-data="{field:'UPDATE_TIME',width:140}">更新时间</th>
				<th lay-data="{field:'op',width:40}">操作</th>
			</tr>
		</thead>
        <tbody id="goodsList">
           
        </tbody>
  </table>
        		<button class="btn btn-info btn-sm" onclick="addGoods()" type="button">新增</button>
  <script id="list-template" type="text/x-jsrender">
				{{for  list}}
					<tr>
						<td>{{:#index+1}}</td>
						<td>{{:GOODS_NO}}</td>
						<td>{{:NAME}}</td>
						<td>{{:NUMBER}}</td>
						<td>{{:TAX_RATE}}</td>
						<td>{{:PRICE}}</td>
						<td>{{:TOTAL_PRICE}}</td>
						<td>{{:CREATE_USER_NAME}}</td>
						<td>{{:UPDATE_TIME}}</td>
						<td><a href="javascript:;" onclick="editGoods('{{:GOODS_ID}}')">编辑</a></td>
    				</tr>
				{{/for}}					         
   </script>
	<script type="text/javascript">
   
		$(function(){
			$("#DataEditForm").render({success:function(){
				layui.use('table', function(){
					  var table = layui.table;
					 table.init('goodsList',{height:'full-300'});
					  
			    });
			}});
		});
		
		function editGoods(id){
			popup.layerShow({id:'goods',title:'编辑',area:['600px','400px'],data:{goodsId:id},url:'${ctxPath}/pages/erp/goods/goods-edit.jsp'});
		}
		
		function addGoods(){
			popup.layerShow({id:'goods',title:'新增',area:['600px','400px'],data:{orderId:'${param.orderId}'},url:'${ctxPath}/pages/erp/goods/goods-edit.jsp'});
		}
</script>
