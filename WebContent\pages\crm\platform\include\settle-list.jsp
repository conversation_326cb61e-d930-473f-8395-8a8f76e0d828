<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<script type="text/html" id="settleToolbar">
    <button type="button" class="btn btn-sm btn-info" onclick="settleList.add()">+ 新增结算</button>
</script>

<table id="settleTable" class="layui-table"></table>

<script type="text/javascript">
    var settleList = {
        init: function(){
            $("#PlatformDetailForm").initTable({
                mars:'CustOperateDao.settlePageList',
                id:'settleTable',
                page:true,
                toolbar: '#settleToolbar',
                rowDoubleEvent(row) {
                    settleList.detail(row.SETTLEMENT_ID);
                },
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align:'left'
                    },{
                        field: '',
                        title: '操作',
                        width: 120,
                        align: 'center',
                        templet: function(d){
                            var html = '<a href="javascript:;" onclick="settleList.detail(\''+d.SETTLEMENT_ID+'\')">详情</a>';
                            html += ' <a href="javascript:;" onclick="settleList.edit(\''+d.SETTLEMENT_ID+'\')">编辑</a>';
                            html += ' <a href="javascript:;" onclick="settleList.del(\''+d.SETTLEMENT_ID+'\')">删除</a>';
                            return html;
                        }
                    },{
                        field: 'SETTLEMENT_NO',
                        title: '结算编号',
                        width: 140,
                        templet: function(d){
                            return '<a href="javascript:;" onclick="settleList.detail(\''+d.SETTLEMENT_ID+'\')">'+d.SETTLEMENT_NO+'</a>';
                        }
                    },{
                        field: 'MONTH_ID',
                        title: '结算月份',
                        width: 100
                    },{
                        field: 'SETTLEMENT_TYPE',
                        title: '结算类型',
                        width: 80
                    },{
                        field: 'CUST_NAME',
                        title: '客户名称',
                        width:150,
                        templet:'<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
                    },{
                        field: 'CONTRACT_NAME',
                        title: '合同名称',
                        width:150,
                        templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">{{d.CONTRACT_NAME}}</a></div>'
                    },{
                        field: 'TOTAL_KP_AMOUNT',
                        title: '开票总额',
                        width:90,
                        align: 'right'
                    },{
                        field: 'TOTAL_COST_AMOUNT',
                        title: '成本总额',
                        width:90,
                        align: 'right'
                    },{
                        field: 'GROSS_PROFIT',
                        title: '毛利',
                        width:90,
                        align: 'right'
                    },{
                        field: 'GROSS_PROFIT_RATE',
                        title: '毛利率%',
                        width:80,
                        align: 'right',
                        templet: function(d){
                            return d.GROSS_PROFIT_RATE + '%';
                        }
                    },{
                        field: 'PAYMENT_STATUS',
                        title: '回款状态',
                        width:100,
                        align: 'center',
                        templet: function(d){
                            var status = ['未回款','部分回款','已回款'];
                            return status[d.PAYMENT_STATUS] || '未知';
                        }
                    },{
                        field: 'PAY_AMOUNT',
                        title: '已回款(含税)',
                        width:100
                    },{
                        field: 'PAYMENT_TIME',
                        title: '回款时间',
                        width:120
                    },{
                        field: 'CREATE_NAME',
                        title: '创建人',
                        width:100
                    },{
                        field: 'CREATE_TIME',
                        title: '创建时间',
                        width:120
                    }
                ]],
                done:function(res){
                    if(res.totalRow != undefined){
                        $("[name='extend.SETTLE_COUNT']").text(res.totalRow);
                    }
                }
            });
        },
        add: function(){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'新增结算',
                offset:'20px',
                area:['800px','600px'],
                url:"${ctxPath}/pages/crm/platform/include/add-settle.jsp",
                data:{platformId:platformId,platformName:platformInfo.PLATFORM_NAME}
            });
        },
        detail: function(settlementId){
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                shadeClose: false,
                title: '结算详情',
                offset: 'r',
                area: ['800px', '100%'],
                url:"${ctxPath}/pages/crm/platform/include/settle-detail.jsp",
                data:{settlementId:settlementId}
            });
        },
        edit: function(settlementId){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'编辑结算',
                offset:'20px',
                area:['800px','600px'],
                url:"${ctxPath}/pages/crm/platform/include/add-settle.jsp",
                data:{settlementId:settlementId}
            });
        },
        del: function(settlementId){
            layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function(){
                ajax.remoteCall("${ctxPath}/servlet/custOperate?action=deleteSettle",{settlementId:settlementId},function(result){
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            reloadSettleList();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            });
        }
    };

    function reloadSettleList(){
        $("#PlatformDetailForm").queryData({id:'settleTable',page:true});
    }
</script>