<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
	<table class="layui-hide" id="orderTable"></table>

	<script type="text/javascript">
	
		function loadOrder(){
			$("#SupplierDetailForm").initTable({
				mars:'OrderDao.list',
				id:'orderTable',
				page:true,
				height:'full-250',
				cols: [[
				 {
					 field:'ORDER_NO',
					 title:'单据编号',
					 event:'orderDetail',
					 width:120,
					 style:'text-decoration:underline;',
					 templet:'<div><a href="javascript:;">{{d.ORDER_NO}}</a></div>'
				 },{
				    field: 'CONTRACT_NAME',
					title: '合同名称',
					align:'left'
				},{
				    field: 'ORDER_TYPE',
					title: '采购类型',
					align:'center',
					templet:function(row){
						var json = {'0':'销售采购','1':'固定资产'};
						return json[row['ORDER_TYPE']];
					}
				},{
					field:'TOTAL_PRICE',
					title:'含税总价'
				},{
				    field: 'LAST_FOLLOW_LABEL',
					title: '最新状态',
					align:'center'
				},{
				    field: 'LAST_FOLLOW_CONTENT',
					title: '跟进内容',
					align:'center'
				},{
				    field: 'LAST_FOLLOW_TIME',
					title: '跟进时间',
					align:'center'
					
				},{
				    field: 'CREATE_TIME',
					title: '操作时间',
					align:'center',
					width:130,
					templet:function(row){
						var time= row['CREATE_TIME'];
						return cutText(time,19,'');
					}
				},{
				    field: 'CREATE_USER_ID',
					title: '制单人',
					align:'center',
					width:70,
					templet:function(row){
						return getUserName(row.CREATE_USER_ID);
					}
				}
			]],done:function(data){
				 $("#orderCount").text(data['totalRow']);
		  }});
		}
		
		$(function(){
			loadOrder();
		});
		
		function orderDetail(data){
			popup.openTab({id:'orderDetail',title:'采购详情',url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{orderId:data.ORDER_ID,custId:data.CUST_ID}});
		}
</script>
