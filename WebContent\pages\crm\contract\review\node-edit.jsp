<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
	<style>
		.select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
   			 background-color: #fff;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editConfForm" autocomplete="off" data-mars="ReviewDao.nodeInfo" data-mars-prefix="conf.">
	     	    <input type="hidden" value="${param.pNodeId}" name="conf.P_NODE_ID"/>
	     	    <input type="hidden" id="nodeId" value="${param.nodeId}" name="conf.NODE_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px;">名称</td>
		                    <td>
		                    	<input name="conf.NODE_NAME" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">初审人</td>
		                    <td>
		                    	<input type="hidden" name="conf.FIRST_CHECK_IDS">
		                    	<input id="firstCheckIds" name="conf.FIRST_CHECK_NAMES" onclick="multiUser(this)" class="form-control input-sm">
		                    </td>
		                </tr>
			            <tr>
		                    <td>终审人</td>
		                    <td>
		                    	<input type="hidden" name="conf.LAST_CHECK_IDS">
		                    	<input id="lastCheckIds" name="conf.LAST_CHECK_NAMES" onclick="multiUser(this)" class="form-control input-sm">
		                    </td>
		                </tr>
			            <tr>
		                    <td>状态</td>
		                    <td>
		                    	<label class="radio radio-inline radio-success">
			                        	<input type="radio" value="0" name="conf.NODE_STATE"> <span>启用</span>
		                        </label>
		                        <label class="radio radio-inline radio-success">
		                        	<input type="radio" value="1" checked="checked" name="conf.NODE_STATE"> <span>暂停</span>
		                        </label>
		                    </td>
		                </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="NodeConf.ajaxSubmitForm()"> 确 定  </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
        var NodeConf={nodeId:$("#nodeId").val()};
        NodeConf.ajaxSubmitForm = function(){
			if(form.validate("#editConfForm")){
				if(NodeConf.nodeId==''){
					NodeConf.updateData('add'); 
				}else{
					NodeConf.updateData('update'); 
				}
			};
		}
		NodeConf.updateData = function(flag) {
			var data = form.getJSONObject("#editConfForm");
			ajax.remoteCall("${ctxPath}/servlet/contract/review?action="+flag+"NodeConf",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose("#editConfForm");
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	
		$(function(){
			 requreLib.setplugs('select2',function(){
				$("#editConfForm").render();
			 });
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>