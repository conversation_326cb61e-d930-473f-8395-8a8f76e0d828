
## PROJECT OVERVIEW
This project, yq-work, is a comprehensive web application managing various aspects of project workflow, including task management, contract management, and employee information.  It utilizes a Java backend with a JSP frontend and incorporates various third-party libraries and APIs.

## CODE STYLE
Adhere to standard Java coding conventions.  Maintain consistent indentation (4 spaces).  Use descriptive variable and method names.

## FOLDER ORGANIZATION
The project follows a standard Maven structure.  All Java source code resides in `src/main/java`, while JSP pages are located in `WebContent`. Resources are in `resource` and `WebContent/static` contains static assets (CSS, JavaScript, images).

## TECH STACK
- Java (version: [Specify Java version])
- JSP (version: [Specify JSP version])
- JFinal (version: 5.2.3)
- MySQL (version: [Specify MySQL version])  or Oracle (version: [Specify Oracle version]) (Note: Support for both databases is required)
- Maven
-  Various JavaScript libraries (Layui, jQuery, etc.)
-  Various Java libraries (listed in project dependencies)
- commonmark-0.21.0.jar
- cos-2020.4.jar
- jfinal-weixin-3.4.jar
- okhttp-4.12.0.jar
- UserAgentUtils-1.21.jar


## PROJECT-SP<PERSON><PERSON><PERSON> STANDARDS
- All database interactions should be performed through the DAO layer.
- Use consistent date and time formats throughout the application.
- Implement robust error handling and logging.
- **"Is/Is not project bonus issued" field:** If "Yes" is selected, provide a reason in the subsequent field. If "No" is selected, provide a reason in the subsequent field.


## WORKFLOW & RELEASE RULES
- Follow the standard Maven release process.
- All code changes must be reviewed before merging into the main branch.
- Automated testing is required before release.  [Specify testing framework and coverage requirements]
- Weekly reports and project documentation should be automatically generated. (See 2025-03-14_05-53-定时生成周报和项目文档.md)


## REFERENCE EXAMPLES
[Add links or examples of best practices and coding patterns here]

## PROJECT DOCUMENTATION & CONTEXT SYSTEM
- Documentation should follow a consistent structure (See `doc/开发文档`).
-  Version updates should be recorded in this file and the relevant sections.


## DEBUGGING
- Use a consistent logging strategy (log4j is used in this project).
-  [Add project-specific debugging tips and troubleshooting steps here]

## FINAL DOs AND DON'Ts
- **DO:** Write clean, well-documented code.
- **DO:** Follow the workflow and release rules.
- **DO:** Use version control effectively (SVN is used in this project).
- **DON'T:** Commit broken code.
- **DON'T:** Hardcode values.
- **DON'T:** Ignore error messages.