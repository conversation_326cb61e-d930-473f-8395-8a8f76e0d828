<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<table id="goodsTable"></table>
		<script  type="text/html" id="toolbar">
			<button class="btn btn-info btn-sm" onclick="addGoods()" type="button">新增</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="reloadDataList" type="button">刷新</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="copyGoods" type="button">复制</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="updateGoods" type="button">修改</button>
		
			<EasyTag:hasRole roleId="ERP_ORDER_STORE_MGR">
				<button class="btn btn-default btn-sm ml-15" onclick="putStorage()" type="button">入库</button>
				<button class="btn btn-default btn-sm ml-15" onclick="outStorage()" type="button">出库</button>
			</EasyTag:hasRole>
			<button class="btn btn-default btn-sm ml-15" onclick="returnStorage()" type="button">退货</button>
			<button class="btn btn-default btn-sm ml-15" lay-event="uploadPz" type="button">上传凭证</button>
		</script>
	<script type="text/javascript">
		$(function(){
			$("#OrderDetailForm").initTable({
				mars:'OrderDao.goodsList',
				id:'goodsTable',
				page:false,
				toolbar:'#toolbar',
				cellMinWidth:100,
				totalRow:true,
				rowDoubleEvent:'goodsDetail',
				cols: [[
	             {
            	 	type: 'checkbox',
					align:'left'
				 },{
				    field: 'GOODS_NO',
					title: '物料编号',
					align:'left'
				},{
				    field: 'NAME',
					title: '物料描述',
					align:'left',
					minWidth:120,
					totalRowText:'汇总'
				},{
				    field: 'FILE_COUNT',
					title: '上传凭证',
					align:'center',
					width:80,
					templet:function(row){
						 var fileCount = row['FILE_COUNT'];
						 if(fileCount==0){
							 return '<font color=red>请上传</font>';
						 }else{
							 return fileCount;
						 }
					}
				},{
				    field: 'PRICE',
					title: '含税单价',
					totalRow:true,
					align:'center'
				},{
					 field:'NUMBER',
					 totalRow:true,
					 width:70,
					 title:'采购数量'
				 },{
					 field:'IN_NUMBER',
					 title:'入库数量',
					 width:70,
					 templet:function(row){
						 return row['IN_NUMBER'] - row['RETURN_SUPPLIER_NUM'];
					 }
				 },{
					 field:'OUT_NUMBER',
					 title:'出库数量',
					 width:70,
					 templet:function(row){
						 return row['OUT_NUMBER'];
					 }
				 },{
					 field:'RETURN_NUMBER',
					 width:150,
					 title:'退货(总数/供应商/仓库)',
					 templet:'<div>{{d.RETURN_NUMBER}}/{{d.RETURN_SUPPLIER_NUM}}/{{d.RETURN_STORE_NUM}}</div>'
				 },{
					 field:'',
					 width:100,
					 title:'实际出库数量',
					 templet:function(row){
						  var val  = row['OUT_NUMBER']-row['RETURN_NUMBER'];
						  return val<0?0:val;
					 }
				 },{
					 field:'',
					 width:70,
					 title:'现有量',
					 templet:function(row){
						return (row['IN_NUMBER'] - row['RETURN_SUPPLIER_NUM']) - (row['OUT_NUMBER'] - row['RETURN_NUMBER']);
					 }
				 },{
				    field: 'TOTAL_PRICE',
					title: '含税总价',
					totalRow:true,
					align:'center'
				},{
				    field: 'TAX_RATE',
					title: '税率',
					width:70,
					align:'center',
					templet:'<div>{{d.TAX_RATE}}%</div>'
				},{
					field:'REMARK',
					title:'备注',
					edit:'text'
				},{
				    field: 'CREATE_USER_NAME',
					title: '创建人',
					align:'center'
				},{
				    field: 'UPDATE_TIME',
					title: '更新时间',
					align:'center'
				}
			]],done:function(row){
				$("#goodsCount").text(row['total']);
		  	}
		});
		});
		
		function reloadDataList(data){
			var sum = data.length;
	 		if(sum > 0){
				ajax.remoteCall("${ctxPath}/servlet/goods?action=reloadStorageNum",{goodsId:data[0]['GOODS_ID']},function(result) { 
					$("#OrderDetailForm").queryData({id:'goodsTable',page:false});
				});
	 		}else{
				$("#OrderDetailForm").queryData({id:'goodsTable',page:false});
	 		}
		}
		
		function editGoods(id,isCopy){
			var supplierId = $("[name='apply.SUPPLIER_ID']").val();
			var title = isCopy=='1'?'复制数据':'修改数据';
			popup.layerShow({id:'goods',scrollbar:false,title:title,area:['600px','460px'],shadeClose:false,data:{supplierId:supplierId,goodsId:id,isCopy:isCopy},url:'${ctxPath}/pages/erp/goods/goods-edit.jsp'});
		}
		
		function addGoods(){
			var supplierId = $("[name='apply.SUPPLIER_ID']").val();
			popup.layerShow({id:'goods',scrollbar:false,title:'新增',area:['600px','460px'],shadeClose:false,data:{supplierId:supplierId,orderId:'${param.orderId}'},url:'${ctxPath}/pages/erp/goods/goods-edit.jsp'});
		}
		
		function updateGoods(data){
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择物料。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		editGoods(data[0]['GOODS_ID'],'0');
		}
		
		function goodsDetail(data){
	 		editGoods(data['GOODS_ID'],'0');
		}
		
		function copyGoods(data){
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择物料。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		editGoods(data[0]['GOODS_ID'],'1');
		}
		
		function returnStorage(){
			doStorage('return','退货');
		}
		
		function putStorage(){
			doStorage('put','入库');
		}
		
		function outStorage(){
			doStorage('out','出库');
		}
		
		function uploadPz(data){
			var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择物料。',{icon : 7, time : 1000});
	 			return;
	 		}
			$("#goodsId").val(data[0]['GOODS_ID']);
			$('#orderDetailLocalfile').click();
		}
		
		
		function doStorage(type,title){
			var checkStatus = table.checkStatus('goodsTable')
	 		var data = checkStatus.data;
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择物料。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		if(sum > 1){
	 			layer.msg('只能选择一个物料。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		var _data = {};
	 		if(type=='put'){
		 		var row = data[0];
				var inNumber = row['IN_NUMBER'] - row['RETURN_SUPPLIER_NUM'];	
				var number = row['NUMBER'];
				if(number <= inNumber){
					layer.msg('入库数不能大于采购数');
					return;
				}
				_data['number'] = number;
				_data['inNumber'] = inNumber;
				_data['maxInput'] = number - inNumber;
				
	 		}else if(type=='out'){
	 			var row = data[0];
				var outNumber = row['OUT_NUMBER'] - row['RETURN_NUMBER'];	
				var number = row['NUMBER'];
				if(number <= outNumber){
					layer.msg('出库数不能大于采购数');
					return;
				}
				_data['number'] = number;
				_data['outNumber'] = outNumber;
				_data['maxInput'] = number - outNumber;
	 		}else if(type=='return'){
	 			var row = data[0];
	 			var inNumber = row['IN_NUMBER'] - row['RETURN_SUPPLIER_NUM'];	
				_data['maxInput'] = inNumber;
	 		}
	 		
	 		var ids = [];
	 		for(var i = 0; i < sum; i ++){
 				ids.push(data[i].GOODS_ID);
	 		}
	 		if(ids.length <= 0){
	 			layer.msg('请选择。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		var data= $.extend(_data,{goodsId:ids.join(','),type:type,orderId:orderId,title:title});
	 		
	 		popup.layerShow({id:'storageEdit',area:['550px','500px'],shadeClose:false,title:title,url:'${ctxPath}/pages/erp/goods/storage-edit.jsp',data:data});
			
		}
		
		
		
</script>
