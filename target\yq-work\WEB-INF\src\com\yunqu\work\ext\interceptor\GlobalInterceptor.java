package com.yunqu.work.ext.interceptor;

import javax.servlet.http.HttpServletRequest;

import org.easitline.common.core.sso.UserPrincipal;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;

public class GlobalInterceptor implements Interceptor {

	@Override
	public void intercept(Invocation inv) {
		HttpServletRequest request = inv.getController().getRequest();
		UserPrincipal principal = (UserPrincipal)request.getUserPrincipal();
		request.setAttribute("isLogin", principal!=null);
		request.setAttribute("principal", principal);
		if(principal!=null) {
			 String userId = principal.getUserId();
			 StaffModel staffInfo = StaffService.getService().getStaffInfo(userId);
			 request.setAttribute("staffInfo", staffInfo);
		}
		inv.getController().keepPara();
		inv.invoke();
		
		
	}

}
