package com.yunqu.work.dao.erp;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="GoodsDao")
public class GoodsDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String goodsId = param.getString("goodsId");
		return queryForRecord("select * from yq_erp_goods where goods_id = ?",goodsId);
	}
	
	@WebControl(name="getStockNo",type=Types.TEXT)
	public JSONObject getStockNo(){
		int dateId = EasyCalendar.newInstance().getDateInt();
		try {
			String val = this.getQuery().queryForString("select max(stock_no) from yq_erp_goods_stock where stock_no like '"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult(dateId+"1001");
			}else {
				return getJsonResult(String.valueOf(Long.valueOf(val)+1));
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	
	@WebControl(name="stockInfo",type=Types.RECORD)
	public JSONObject stockInfo(){
		String stockId = param.getString("stock.STOCK_ID");
		return queryForRecord("select * from yq_erp_goods_stock where stock_id = ?",stockId);
	}
	
	@WebControl(name="goodsList",type=Types.PAGE)
	public JSONObject goodsList(){
		EasySQL sql = getEasySQL("select t1.*,t2.order_no,t2.contract_name,t2.cust_id from yq_erp_goods t1,yq_erp_order t2,yq_project_contract t3 where 1=1");
		sql.append("and t1.order_id = t2.order_id");
		sql.append("and t2.contract_id = t3.contract_id");
		sql.append(param.getString("orderId"),"and t2.order_id = ?");
		sql.appendLike(param.getString("orderNo"),"and t2.order_no like ?");
		sql.appendLike(param.getString("goodsNo"),"and t1.goods_no like ?");
		sql.appendLike(param.getString("contractNo"),"and t3.contract_no like ?");
		sql.appendLike(param.getString("contractName"),"and t3.contract_name like ?");
		String dateRange = param.getString("dateRange");
		if(StringUtils.notBlank(dateRange)) {
			String[] array  = dateRange.replaceAll("-","").split(" 到 ");
			sql.append(array[0],"and t2.date_id >= ?");
			sql.append(array[1],"and t2.date_id <= ?");
		}
		sql.append("order by t2.date_id,t2.order_no desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
		
	}
	
	
	@WebControl(name="stockList",type=Types.TEMPLATE)
	public JSONObject stockList(){
		EasySQL sql = getEasySQL("select t1.*,t2.`name` goods_name,t2.out_number,t2.in_number,t2.return_number,t2.GOODS_NO,t2.number,t2.PRICE,t2.R_PRICE,t2.TAX_RATE,t3.UNIT from yq_erp_goods_stock t1,yq_erp_goods t2,yq_erp_product t3 where t1.goods_id = t2.goods_id");
		sql.append("and t3.product_id = t2.product_id");
		sql.append(param.getString("orderId"),"and t1.order_id = ?");
		sql.append(param.getString("type"),"and t1.type = ?");
		String stockIds = param.getString("stockIds");
		if(StringUtils.notBlank(stockIds)) {
			sql.appendIn(stockIds.split(","), "and t1.stock_id");
		}
		sql.append("order by t1.goods_id,t1.date_id,t1.create_time desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="goodsStockList",type=Types.TEMPLATE)
	public JSONObject goodsStockList(){
		EasySQL sql = getEasySQL("select t1.*,t2.UNIT,t2.PRODUCT_NAME from yq_erp_goods t1,yq_erp_product t2 where t1.product_id = t2.product_id");
		sql.append(param.getString("orderId"),"and t1.order_id = ?");
		String type = param.getString("type");
		if("in".equals(type)) {
			sql.append("and (t1.in_number - t1.return_supplier_num) > 0");
		}else {
			sql.append("and (t1.out_number - t1.return_number) > 0");
		}
		String goodsIds = param.getString("goodsIds");
		if(StringUtils.notBlank(goodsIds)) {
			sql.appendIn(goodsIds.split(","), "and t1.goods_id");
		}
		sql.append("order by t1.in_date,t1.out_date desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="stockPage",type=Types.PAGE)
	public JSONObject stockPage(){
		EasySQL sql = getEasySQL("select t1.*,t2.`name`,t2.GOODS_NO,t2.number,t2.PRICE,t3.CONTRACT_ID,t3.order_no,t3.contract_name,t3.cust_id from yq_erp_goods_stock t1,yq_erp_goods t2,yq_erp_order t3 where t1.goods_id = t2.goods_id");
		sql.append("and t3.order_id = t1.order_id");
		sql.append(param.getString("orderId"),"and t1.order_id = ?");
		sql.append(param.getString("orderNo"),"and t3.order_no = ?");
		sql.append(param.getString("type"),"and t1.type = ?");
		sql.append(param.getString("contractNo"),"and t3.contract_no = ?");
		String dateRange = param.getString("dateRange");
		if(StringUtils.notBlank(dateRange)) {
			String[] array  = dateRange.replaceAll("-","").split(" 到 ");
			sql.append(array[0],"and t1.date_id >= ?");
			sql.append(array[1],"and t1.date_id <= ?");
		}
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	
	@WebControl(name="categoryList",type=Types.LIST)
	public JSONObject categoryList(){
		EasySQL sql = getEasySQL("select * from yq_erp_goods_category where 1=1 ");
		sql.appendLike(param.getString("name"),"and name like ?");
		sql.append(param.getString("pCode"),"and p_code = ?");
		sql.append(param.getString("code"),"and code = ?");
		sql.append("order by p_code,code");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="productCategory",type=Types.DICT)
	public JSONObject productCategory(){
		String sql="select id,concat(id,' ',name) name from yq_erp_goods_category order by id";
		return getDictByQuery(sql);
	}
	
	@WebControl(name="fileListDetail",type=Types.TEMPLATE)
	public JSONObject fileListDetail(){
		String fkId=param.getString("fkId");
		String businessId=param.getString("businessId");
		String orderId=param.getString("orderId");
		String pFkId = param.getString("pFkId");
		if(StringUtils.isBlank(pFkId)) {
			pFkId = businessId;
			if(StringUtils.isBlank(pFkId)) {
				pFkId = orderId;
			}
		}
		EasySQL sql = new EasySQL("select  t1.*,t2.name goods_name from yq_files t1 LEFT JOIN yq_erp_goods t2  on t1.FK_ID = t2.goods_id where 1=1");
		sql.append(fkId,"and t1.fk_id = ? ");
		sql.append(pFkId,"and t1.p_fk_id = ? ");
		sql.append(" order by t1.file_label,t1.create_time desc");
		if(StringUtils.isBlank(fkId)&&StringUtils.isBlank(pFkId)) {
			return getJsonResult(new JSONArray());
		}
		return queryForList(sql.getSQL(), sql.getParams());
	}

}
