<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>收入阶段详情</title>
    <style>
        .form-horizontal {
            width: 100%;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <SELECT style="display: none" name="paymentName2"
            data-mars="ContractPaymentDao.selectPayment2(${param.contractId})"></SELECT>
    <form id="incomeStageDetailForm" class="form-horizontal" data-mars="IncomeStageDao.record" data-text-model="true" autocomplete="off" data-mars-prefix="incomeStage.">
        <input type="hidden" value="${param.incomeStageId}" name="incomeStage.INCOME_STAGE_ID"/>
        <input type="hidden" value="${param.incomeStageId}" name="incomeStageId"/>
        <input type="hidden" value="${param.contractId}" name="incomeStage.CONTRACT_ID"/>
        <table class="table table-vzebra">
            <tbody>

            <tr>
                <td>阶段名称</td>
                <td>
                    <input type="text" name="incomeStage.STAGE_NAME" class="form-control input-sm">
                </td>
                <td>所属合同</td>
                <td>
                    <input type="text" name="incomeStage.CONTRACT_NAME" class="form-control input-sm" style="padding-right: 10px">
                </td>
            </tr>
            <tr>
                <td style="min-width: 140px">计划完成日期</td>
                <td width="252px">
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="incomeStage.PLAN_COMP_DATE" class="form-control input-sm Wdate">
                </td>
                <td width="140px">实际完成日期A</td>
                <td width="252px">
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="incomeStage.ACT_COMP_DATE" class="form-control input-sm Wdate">
                </td>
            </tr>
                <%--            <tr id="detailStageList">--%>
                <%--                <td>对应合同阶段</td>--%>
                <%--                <td colspan="3">--%>
                <%--                    <table style="width: 95%" id="stageListTable"></table>--%>
                <%--                </td>--%>
                <%--            </tr>--%>
            <tr>
                <td>对应合同款项</td>
                <td colspan="3">
                    <table id="paymentListTable"></table>
                </td>
            </tr>
            <tr>
                <td>计划收款金额(含税)</td>
                <td>
                    <input type="text" name="incomeStage.PLAN_AMOUNT" readonly data-rules="required" class="form-control input-sm" placeholder="选择合同阶段后自动计算">
                </td>
                <td>计划收款金额(税后)</td>
                <td>
                    <input type="text" name="incomeStage.AMOUNT_NO_TAX" readonly data-rules="required" class="form-control input-sm" placeholder="选择合同阶段后自动计算">
                </td>
            </tr>
            <tr>
                <td>占合同总额比例(%)</td>
                <td>
                    <input type="text" name="incomeStage.RATIO" readonly data-rules="required" class="form-control input-sm" placeholder="选择合同阶段后自动计算">
                </td>
                <td colspan="2"></td>
            </tr>
            <tr>
                <td>信用期(天)</td>
                <td>
                    <input type="text" name="incomeStage.CREDIT_PRD" class="form-control input-sm">
                </td>
                <td>预留比例(%)</td>
                <td>
                    <input type="text" name="incomeStage.YULIU_RATIO" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>已审核</td>
                <td>
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio" value="1" name="incomeStage.CHECKED_FLAG"><span>√</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="0" name="incomeStage.CHECKED_FLAG"><span>×</span>
                    </label>
                </td>
                <td>硬件已确认</td>
                <td>
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio" value="1" name="incomeStage.HARDWARE_CONF_FLAG"><span>√</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="0" name="incomeStage.HARDWARE_CONF_FLAG"><span>×</span>
                    </label>
                </td>
            </tr>
            <tr>
                <td style="text-align: center;" colspan="4">收入确认A</td>
            </tr>
            <tr>
                <td>已确认金额A(含税)</td>
                <td>
                    <span id="detailConfirmA"></span>
                </td>
                <td>未确认金额A(含税)</td>
                <td>
                    <input type="text" name="incomeStage.UNCONFIRM_AMOUNT_A" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>已确认金额A(税后)</td>
                <td>
                    <span id="detailConfirmNoTaxA"></span>
                </td>
                <td>未确认金额A(税后)</td>
                <td>
                    <input type="text" name="incomeStage.UNCONFIRM_NO_TAX_A" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>收入确认A</td>
                <td colspan="3">
                    <table id="detailConfirmListA"></table>
                </td>
            </tr>
            <tr>
                <td style="text-align: center;" colspan="4" >收入确认B</td>
            </tr>
            <tr>
                <td>已确认金额B(含税)</td>
                <td>
                    <span id="detailConfirmB"></span>
                </td>
                <td>未确认金额B(含税)</td>
                <td>
                    <input type="text" name="incomeStage.UNCONFIRM_AMOUNT_B" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>已确认金额B(税后)</td>
                <td>
                    <span id="detailConfirmNoTaxB"></span>
                </td>
                <td>未确认金额B(税后)</td>
                <td>
                    <input type="text" name="incomeStage.UNCONFIRM_NO_TAX_B" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>收入确认B</td>
                <td colspan="3">
                    <table id="detailConfirmListB"></table>
                </td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <input type="text" name="incomeStage.REMARK" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>最后更新时间</td>
                <td colspan="3">
                    <input type="text" name="incomeStage.UPDATE_TIME" class="form-control input-sm">
                </td>
            </tr>
            </tbody>
        </table>
        <br>
        <p class="layer-foot text-c">
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll();"> 关闭</button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        $(function () {
            $("#incomeStageDetailForm").render({
                success: function (result) {
                    // var record = result && result['IncomeStageDao.record'];
                    // var data = record && record.data;
                    // if(data){
                    //     if(data['WRITE_MODE'] == '1'){
                    //         detailContractStageList.init();
                    //         $("#detailStageList").show();
                    //     }else{
                    //         $("#detailStageList").hide();
                    //     }
                    // }

                    $('[name="paymentName2"]').render({
                        success: function () {
                            detailPaymentList.init();
                        }
                    })
                    detailConfirmListA.init();
                    detailConfirmListB.init();
                }
            });
        });

        // var detailContractStageList = {
        //     init:function(){
        //         $("#incomeStageDetailForm").initTable({
        //             mars:'ContractStageDao.stageList',
        //             id:'stageListTable',
        //             page:false,
        //             cols: [[
        //                 {
        //                     field: 'STAGE_NAME',
        //                     title: '阶段名称',
        //                     align:'left',
        //                     width:100
        //                 },{
        //                     field:'RCV_DATE',
        //                     title:'应收日期',
        //                     width:100
        //                 },{
        //                     field:'PLAN_COMP_DATE',
        //                     title:'计划完成日期',
        //                     width:100
        //                 },{
        //                     field:'RATIO',
        //                     title:'比例(%)',
        //                     width:100,
        //                 },{
        //                     field:'PLAN_RCV_AMT',
        //                     title:'计划收款金额',
        //                     width:120,
        //                     align:'left',
        //                 }
        //             ]]
        //         });
        //     },
        // }

        var detailPaymentList = {
            init: function () {
                $("#incomeStageDetailForm").initTable({
                    mars: 'ContractPaymentDao.paymentIncomeStageList',
                    id: 'paymentListTable',
                    page: false,
                    cols: [[
                        {
                            field: 'PAYMENT_ID',
                            title: '对应款项',
                            width: 280,
                            templet: function (d) {
                                return getText(d.PAYMENT_ID, "paymentName2");
                            },
                        }, {
                            field: 'RATIO',
                            title: '收入确认比例',
                            width: 100,
                            templet: function (d) {
                                return d.RATIO+"%";
                            },
                        }, {
                            field: 'AMOUNT_WITH_TAX',
                            title: '计划收款金额(含税)',
                            width: 130,
                            align: 'left',
                        }, {
                            field: 'AMOUNT_NO_TAX',
                            title: '计划收款金额(税后)',
                            width: 130,
                            align: 'left',
                        }
                    ]]
                });
            },
        }

        var detailConfirmListA = {
            init: function () {
                $("#incomeStageDetailForm").initTable({
                    mars: 'IncomeConfirmDao.incomeConfirmList',
                    id: 'detailConfirmListA',
                    data: {'confirmType': 'A'},
                    page: false,
                    cols: [[
                        {
                            field: 'CONFIRM_DATE',
                            title: '日期',
                            width: 120,
                            align: 'left',
                        }, {
                            field: 'SOURCE',
                            title: '来源',
                            align: 'left',
                            width: 120,
                        }, {
                            field: 'AMOUNT_WITH_TAX',
                            title: '金额(含税)',
                            width: 120,
                            align: 'left',
                        },{
                            field: 'AMOUNT_NO_TAX',
                            title: '金额(税后)',
                            width: 120,
                            align: 'left',
                        }, {
                            field: 'CONFIRM_DESC',
                            title: '摘要',
                            width: 80,
                            align: 'left',
                        },
                    ]],
                    done: function (data) {
                        var totalAmount = 0;
                        var totalAmountNoTax = 0;
                        $.each(data.data, function (index, item) {
                            var amountWithTax = parseFloat(item.AMOUNT_WITH_TAX);
                            var amountNoTax = parseFloat(item.AMOUNT_NO_TAX);
                            totalAmount += amountWithTax;
                            totalAmountNoTax += amountNoTax;
                        });
                        totalAmount = totalAmount.toFixed(2);
                        totalAmountNoTax = totalAmountNoTax.toFixed(2);
                        $('#detailConfirmA').text(totalAmount);
                        $('#detailConfirmNoTaxA').text(totalAmountNoTax);
                    }
                })
            }
        }

        var detailConfirmListB = {
            init: function () {
                $("#incomeStageDetailForm").initTable({
                    mars: 'IncomeConfirmDao.incomeConfirmList',
                    id: 'detailConfirmListB',
                    data: {'confirmType': 'B'},
                    page: false,
                    cols: [[
                        {
                            field: 'CONFIRM_DATE',
                            title: '日期',
                            width: 120,
                            align: 'left',
                        }, {
                            field: 'SOURCE',
                            title: '来源',
                            align: 'left',
                            width: 120,
                        }, {
                            field: 'AMOUNT_WITH_TAX',
                            title: '金额(含税)',
                            width: 120,
                            align: 'left',
                        },{
                            field: 'AMOUNT_NO_TAX',
                            title: '金额(税后)',
                            width: 120,
                            align: 'left',
                        },{
                            field: 'CONFIRM_DESC',
                            title: '摘要',
                            width: 80,
                            align: 'left',
                        },
                    ]],
                    done: function (data) {
                        var totalAmount = 0;
                        var totalAmountNoTax = 0;
                        $.each(data.data, function (index, item) {
                            var amountWithTax = parseFloat(item.AMOUNT_WITH_TAX);
                            var amountNoTax = parseFloat(item.AMOUNT_NO_TAX);
                            totalAmount += amountWithTax;
                            totalAmountNoTax += amountNoTax;
                        });
                        totalAmount = totalAmount.toFixed(2);
                        totalAmountNoTax = totalAmountNoTax.toFixed(2);
                        $('#detailConfirmB').text(totalAmount);
                        $('#detailConfirmNoTaxB').text(totalAmountNoTax);
                    }
                })
            }
        }


    </script>

</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>