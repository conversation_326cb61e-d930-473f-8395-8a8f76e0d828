package com.yunqu.work.servlet.common;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.core.ActionKey;
import com.jfinal.core.Path;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.service.KqNoticeService;
import com.yunqu.work.service.LookLogService;
import com.yunqu.work.service.StaffService;

@Before(AuthInterceptor.class)
@Path(value = "/web")
public class WebController extends BaseController {

	@ActionKey(value = "/task")
	public void task() {
		render("/pages/task/task-detail.jsp?isDiv=0&taskId="+getPara());
	}
	@ActionKey(value = "/mytask")
	public void mytask() {
		render("/pages/task/my-task-list.jsp");
	}
	
	@ActionKey(value = "/receivedWeekly")
	public void receivedWeekly() {
		render("/pages/weekly/weekly-received.html");
	}
	
	@ActionKey(value = "/version")
	public void version() {
		render("/pages/ops/version-detail.jsp?versionId="+getPara());
	}
	
	@ActionKey(value = "/weekly")
	public void weekly() {
		render("/pages/weekly/weekly-detail.jsp?isDiv=0&weeklyId="+getPara());
	}
	
	@ActionKey(value = "/platform")
	public void platform() {
		render("/pages/ops/platform-list.jsp");
	}
	
	@Clear
	@ActionKey(value = "/accessLog")
	public void accessLog() {
		JSONObject request = JsonKit.getJSONObject(getRequest(), null);
		if(request!=null) {
			LogEngine.getLogger("cust-mars-access").info(request.toJSONString());
		}
	}
	
	
	@ActionKey(value = "/h5/2022")
	public void h52022() {
		render("/pages/my/2022/index.jsp");
	}
	
	@ActionKey(value = "/affair")
	public void affair() {
		String userId = getPara("userId");
		String affairId = getPara();
		if(StringUtils.notBlank(userId)) {
			set("userId", userId);
			render("/pages/affair/affair-index.jsp?source=write");
		}else if(StringUtils.isBlank(affairId)) {
			render("/pages/affair/affair-index.jsp");
		}else if("write".equals(affairId)){
			render("/pages/affair/affair-index.jsp?source=write");
		}else if("send".equals(affairId)){
			render("/pages/affair/affair-index.jsp?source=send");
		}else {
			String creator = Db.queryStr("select creator from yq_affair where affair_id = ?",affairId);
			if(StringUtils.notBlank(creator)) {
				setAttr("userData",StaffService.getService().getStaffInfo(creator));
				render("/pages/affair/affair-detail.jsp?isDiv=0&affairId="+affairId);
			}else {
				render("/pages/affair/affair-index.jsp");
			}
		}
	}
	
	@ActionKey(value = "/notice")
	public void notice() {
		keepPara();
		String remindId = getPara("remindId");
		if(StringUtils.isBlank(remindId)) {
			remindId = getPara();
		}
		set("remindId",remindId);
		set("remindType",getPara("remindType"));
		render("/pages/remind/remind-detail.jsp");
	}
	
	@ActionKey(value = "/article")
	public void article() {
		String id = getPara();
		if(StringUtils.isBlank(id)) {
			renderHtml("请求不合法");
			return;
		}
		Record record = Db.findFirst("select * from yq_remind where remind_id = ?", id);
		if(record==null) {
			renderHtml("您的链接存在错误");
			return;
		}
		int status = record.getInt("status");
		if(status!=0) {
			renderHtml("内容未设置公开访问");
			return;
		}
		
		if(getRequest().getRemoteUser()!=null) {
			String creator = record.get("creator");
			if(!getUserId().equals(creator)){
				LookLogService.getService().addLog(getUserId(),id,"remind",getRequest());
				Db.update("update yq_remind t1 set t1.view_count = (select count(1) from yq_look_log t2 where t2.fk_id = t1.remind_id) where t1.remind_id = ?", id);
			}
		}
		
		setAttr("record",record);
		render("/pages/remind/h5-detail.jsp");
	}
	
	@ActionKey(value = "/news")
	public void news() {
		EasySQL sql = new EasySQL("select remind_id,remind_type,title,publish_by,publish_time,summary,cover_url from yq_remind where 1=1");
		sql.append("and status = 0");
		sql.append("and remind_type in(1,2)");
		sql.append("order by publish_time desc limit 30");
		List<Record> list = Db.find(sql.getSQL(), sql.getParams());
		setAttr("list", list);
		render("/pages/remind/h5-list.jsp");
	}
	
	
	
	
	@ActionKey(value = "/doc")
	public void doc() {
		String docId = getPara();
		if(StringUtils.notBlank(docId)) {
			if(docId.length()<20) {
				docId = Db.queryStr("select doc_id from yq_doc where doc_code = ?",docId);
			}
			render("/pages/doc/doc-detail.jsp?isDiv=0&docId="+docId);
		}else {
			renderHtml("文档不存在.");
		}
	}
	
	@ActionKey(value = "/folder")
	public void folder() {
		keepPara();
		String folderId = getPara(0,"0");
		if(StringUtils.notBlank(folderId)) {
			String folderCode = null;
			if(folderId.length()<20) {
				folderCode = folderId;
				folderId = Db.queryStr("select folder_id from YQ_FOLDER where folder_code = ?",folderId);
			}else {
				folderCode = getPara(1);
			}
			render("/pages/doc/folder-list.jsp?isDiv=0&folderId="+folderId+"&folderCode="+folderCode+"&folderAuth="+getPara(2,"0"));
		}else {
			renderHtml("目录不存在.");
		}
	}
	
	@ActionKey(value = "/docs")
	public void docs() {
		keepPara();
		String folderCode = getPara(0);
		if(StringUtils.notBlank(folderCode)) {
			String	folderId = Db.queryStr("select folder_id from YQ_FOLDER where folder_code = ?",folderCode);
			render("/pages/doc/docs-list.jsp?isDiv=0&folderId="+folderId+"&folderCode="+folderCode);
		}else {
			renderHtml("目录不存在.");
		}
	}
	
	@ActionKey("/security/code")
	public void code() {
		String code = RandomKit.smsAuthCode(4);
		try {
			this.getMainQuery().executeUpdate("update easi_user set auth_code = ?,auth_code_time = ? where user_id = ?",code,System.currentTimeMillis(),getUserId());
		} catch (SQLException e) {
			e.printStackTrace();
			renderHtml("请稍后再试");
			return;
		}
		renderHtml("<div style='margin: 0 auto;font-size:50px;'>【云趣科技】您的验证码为: <b>"+code+"</b>。有效期5分钟，不要告诉任何人。</div>");
	}
	
	
	@ActionKey("/")
	public void index() {
		renderHtml("404");
	}
	
	public void test() {
		renderHtml("test");
		
	}
	
	
	@ActionKey(value = "/kq")
	public void kq() {
		boolean flag =  KqNoticeService.getService().kqFlag();
		if(!flag){
			//renderHtml("今日法定休息， 无须打卡。");
			//return;
		}
		set("overtime", flag?"1":"0");
		Record kqInfo  = Db.findFirst("select * from yq_kq_people where kq_user_id = ? and state = 0", getUserId());
		set("kqInfo",kqInfo);
		set("staffInfo", StaffService.getService().getStaffInfo(getUserId()));
		set("nowTime",EasyDate.getCurrentDateString());
		set("currentTime", EasyDate.getCurrentDateString("yyyy/MM/dd HH:mm"));
		set("currentDate", EasyDate.getCurrentDateString("yyyy/MM/dd"));
		render("/wx/kq.jsp");
	}
	
	@ActionKey("/map/req")
	public void mapReq() {
		String url = get("url");
		if(StringUtils.isBlank(url)) {
			renderJson(EasyResult.fail("url is empty"));
			return;
		}
		Connection connection = Jsoup.connect(url);
		try {
			Map<String,String[]> map = getParaMap();
			Map<String,String> newMap = new HashMap<String,String>();
			for(String key:map.keySet()) {
				String[] array = map.get(key);
				if(array!=null) {
					newMap.put(key,array[0]);
				}
			}
			connection.data(newMap);
			connection.data("key","7QTBZ-JKQEX-QMI4U-T3QCV-CBTXJ-HHBRI");
			connection.header("Host", "apis.map.qq.com");
			connection.header("Origin", "https://lbs.qq.com/");
			connection.header("Sec-Fetch-Mode", "cors");
			connection.header("Referer", "https://lbs.qq.com/");
			Response response = connection.ignoreContentType(true).timeout(5000).method(Method.GET).execute();
			String body = response.body();
			renderJson(EasyResult.ok(JSONObject.parseObject(body)));
		} catch (IOException e) {
			getLogger().error(null,e);
			renderJson(EasyResult.fail(e.getMessage()));
		}
	}
}
