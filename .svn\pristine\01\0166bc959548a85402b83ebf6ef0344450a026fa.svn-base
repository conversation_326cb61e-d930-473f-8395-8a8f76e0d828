<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>供应商管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>供应商管理</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">供应商名称</span>
						 	<input class="form-control input-sm" name="supplierName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">供应商编号</span>
						 	<input class="form-control input-sm" name="supplierNo">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" data-event='enter' class="btn btn-sm btn-default" onclick="DataMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
						 	<EasyTag:res resId="ERP_ORDER_SUPPLIER_ADD">
								 <button type="button" class="btn btn-sm btn-info" onclick="DataMgr.add()">+ 新建供应商</button>
						 	</EasyTag:res>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var DataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'SupplierDao.list',
					id:'dataMgrTable',
					autoSort:false,
					limit:30,
					height:'full-90',
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'NAME',
						title: '名称',
						align:'left',
						minWidth:220,
						event:'DataMgr.detail',
						style:'color:#1E9FFF;cursor:pointer'
					},{
						 field:'SUPPLIER_NO',
						 title:'编号'
					 },{
						 field:'ORDER_COUNT',
						 title:'采购订单数'
					 },{
						 field:'PRODUCT_COUNT',
						 title:'供货产品数',
						 width:80
					 },{
					    field: 'REMARK',
						title: '备注',
						align:'center'
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						align:'center',
						templet:function(row){
							var time= row['UPDATE_TIME'];
							return cutText(time,12,'');
						}
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'CREATE_USER_NAME',
						title: '创建人',
						align:'center'
					},{
					    field: '',
						title: '采购记录',
						align:'center',
						width:70,
						templet:'<div><a href="javascript:void(0)" onclick="DataMgr.orderDetail(\'{{d.SUPPLIER_ID}}\')">查看</a></div>'
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable',jumpOne:true});
			},
			detail:function(data){
				var id = data['SUPPLIER_ID'];
				popup.openTab({id:'supplierDetail',url:'${ctxPath}/pages/erp/supplier/supplier-detail.jsp',title:'供应商详情',data:{supplierId:id}});
			},
			edit:function(data){
				var  supplierId = data['SUPPLIER_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','400px'],url:'${ctxPath}/pages/erp/supplier/supplier-edit.jsp',title:'修改信息',data:{supplierId:supplierId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','400px'],url:'${ctxPath}/pages/erp/supplier/supplier-edit.jsp',title:'新建供应商'});
			},
			orderDetail:function(id){
				popup.openTab({id:'orderDetail',url:'${ctxPath}/pages/erp/order/order-list.jsp',title:'采购记录',data:{supplierId:id}});
			}
		}
		
		function reloadDataList(){
			DataMgr.query();
		}
		$(function(){
			$("#dataMgrForm").render({success:function(){
				DataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>