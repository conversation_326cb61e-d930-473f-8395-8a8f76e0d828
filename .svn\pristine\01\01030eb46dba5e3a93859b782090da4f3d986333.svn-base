package com.yunqu.work.servlet.common;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.AffairModel;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.utils.WebSocketUtils;

@WebServlet("/servlet/affair/*")
public class AffairServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME+"-affair";
	}
	
	public EasyResult actionForAdd(){
		AffairModel model=getModel(AffairModel.class, "affair");
		model.addCreateTime();
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.setCreator(getUserId());
		model.set("UPDATE_BY", getUserId());
		model.set("CREATE_NAME", getUserName());
		try {
			String  receiveUserId = model.getString("RECEIVE_USER_ID");
			this.getQuery().save(model);
			WebSocketUtils.sendMessage(new MsgModel());
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"创建成功.");
	}
	
	
	public EasyResult actionForUpdate(){
		AffairModel model=getModel(AffairModel.class, "affair");
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		try {
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"修改成功.");
	}
	
	public EasyResult actionForDel(){
		String id = getJsonPara("id");
		try {
			this.getQuery().executeUpdate("update yq_affair set affair_state = 0 where affair_id = ?", id);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
}
