<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目看板</title>
	<link href="${ctxPath}/static/zui/css/zui.min.css" rel="stylesheet">
	<script src="${ctxPath}/static/zui/js/zui.min.js"></script>
	<link href="${ctxPath}/static/zui/lib/board/zui.board.min.css" rel="stylesheet">
	<script src="${ctxPath}/static/zui/lib/board/zui.board.min.js"></script>
	<style type="text/css">
		.text-gray, .text-muted{line-height: 16px;height: 40px;}

	</style>
</EasyTag:override>
<EasyTag:override name="content">
  <form id="form">
  	<input type="hidden" name="projectId" value="${param.projectId}"/>
  	<c:if test="${empty param.projectId}">
		<div class="layui-card">
	 	 <div class="layui-card-header"><i class="layui-icon layui-icon-username"></i> 个人看板</div>
	  	 <div class="layui-card-body">
			<div class="cards">
				<div data-mars="KanbanDao.persionKanban" data-template="tpl1">
					
				</div>
			   <div class="col-md-4 col-sm-6 col-lg-3">
			    <div class="card">
			    	 <div class="card-heading"><strong>&nbsp;</strong></div>
					  <div class="card-content text-muted">&nbsp;
					  	<a style="margin-left: 36%;font-size: 16px;" href="javascript:;" onclick="addKanban(0)">+新建看板</a>
					  </div>
					  <div class="card-actions">
					    &nbsp;
					  </div>
			    </div>
			  </div>
			</div>
	  	</div>
	   </div>
  	</c:if>
		  <script type="text/x-jsrender" id="tpl1">
  			{{for data}}
				<div class="col-md-4 col-sm-6 col-lg-3">
				    <div class="card">
				    	 <div class="card-heading"><strong>{{:KANBAN_NAME}}</strong></div>
						  <div class="card-content text-muted">{{if KANBAN_DESC}}{{cutText:KANBAN_DESC 70}}{{else}}暂无{{/if}}</div>
						  <div class="card-actions">
							<button type="button" onclick="kanbanEdit('{{:KANBAN_ID}}',0)" class="btn btn-sm btn-default">修改</button>
						    <div class="pull-right text-danger"><button onclick="kanbanDetail('{{:KANBAN_ID}}')" type="button" class="btn btn-sm btn-info"> 查看</button></div>
						  </div>
				    </div>
			  </div>
			{{/for}}
		   </script>
	<div class="layui-card">
 	 <div class="layui-card-header"><i class="layui-icon layui-icon-app"></i> 项目看板</div>
  	 <div class="layui-card-body">
		<div class="cards">
		  <div data-mars="KanbanDao.projectKanban" data-template="tpl2">
			  
		  </div>
		  <div class="col-md-4 col-sm-6 col-lg-3">
		    <div class="card">
		    	 <div class="card-heading"><strong>&nbsp;</strong></div>
				  <div class="card-content text-muted">&nbsp;
				  	<a style="margin-left: 36%;font-size: 16px;" onclick="addKanban(1)" href="javascript:;">+新建看板</a>
				  </div>
				  <div class="card-actions">
				    &nbsp;
				  </div>
		    </div>
		  </div>
		  <script type="text/x-jsrender" id="tpl2">
  			{{for data}}
				<div class="col-md-4 col-sm-6 col-lg-3">
			    <div class="card">
			    	 <div class="card-heading"><strong>{{:KANBAN_NAME}}</strong></div>
					  <div class="card-content text-muted">{{cutText:PROJECT_NAME 76}}</div>
					  <div class="card-actions">
						<button type="button" onclick="kanbanEdit('{{:KANBAN_ID}}',1)" class="btn btn-sm btn-default">修改</button>
					    <div class="pull-right text-danger"><button onclick="kanbanDetail('{{:KANBAN_ID}}')" type="button" class="btn btn-sm btn-info"> 查看</button></div>
					  </div>
			    </div>
			  </div>
			{{/for}}
		   </script>
  	</div>
   </div>
   </div>
</form>
		
</EasyTag:override>


<EasyTag:override name="script">
	<script type="text/javascript">
       function addKanban(type){
    	   popup.layerShow({type:1,anim:0,scrollbar:false,offset:'30px',area:['400px','300px'],url:'${ctxPath}/pages/kanban/kanban-edit.jsp',title:'新增看板',data:{type:type,projectId:'${param.projectId}'}});
       }
       function kanbanEdit(kanbanId,type){
    	   popup.layerShow({type:1,anim:0,scrollbar:false,offset:'30px',area:['400px','300px'],url:'${ctxPath}/pages/kanban/kanban-edit.jsp',title:'编辑看板',data:{kanbanId:kanbanId,type:type}});
       }
       function kanbanDetail(kanbanId){
    	   popup.openTab({id:'kanbanDetail',url:'${ctxPath}/pages/kanban/kanban-detail.jsp',title:'看板',data:{kanbanId:kanbanId}});
       }
       $(function(){
    	   reloadKanban();
       });
		function reloadKanban(){
    	   $("#form").render();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>