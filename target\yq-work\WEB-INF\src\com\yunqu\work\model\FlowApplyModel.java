package com.yunqu.work.model;

import java.io.Serializable;
import java.util.Map;

import org.easitline.common.utils.string.StringUtils;

public class FlowApplyModel implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String applyId;
	
	private String applyTitle;
	
	private int applyState;
	
	private String applyNo;
	
	private String applyBy;
	
	private String flowName;
	
	private String flowCode;
	
	private String applyName;
	
	private String applyTime;
	
	private String applyRemark;
	
	private String applyLevel;
	
	private String fkId;
	
	private String deptId;
	
	private String deptName;
	
	private String nextResultId;
	
	private String nextNodeId;
	
	private String relatedFlow;
	
	private String approverField;
	
	private Map<String,String> data;
	
	private FlowModel flow;
	
	public FlowApplyModel(String flowCode,String applyBy) {
		this.setFlowCode(flowCode);
		this.setApplyBy(applyBy);
	}
	public FlowApplyModel(String applyBy) {
		this.setApplyBy(applyBy);
	}
	
	public FlowApplyModel() {
		
	}
	
	public void setApplyNo(String applyNo) {
		this.applyNo = applyNo;
	}
	
	public void setTitle(String applyTitle) {
		this.applyTitle = applyTitle;
	}
	
	public void setApplyState(int applyState) {
		this.applyState = applyState;
	}

	public String getApplyTitle() {
		return applyTitle;
	}

	public void setApplyTitle(String applyTitle) {
		this.applyTitle = applyTitle;
	}

	public int getApplyState() {
		return applyState;
	}

	public String getApplyNo() {
		return applyNo;
	}

	public String getApplyId() {
		return applyId;
	}

	public void setApplyId(String applyId) {
		this.applyId = applyId;
	}

	public String getApplyBy() {
		return applyBy;
	}

	public void setApplyBy(String applyBy) {
		this.applyBy = applyBy;
	}

	public String getApplyName() {
		return applyName;
	}

	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}

	public String getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(String applyTime) {
		this.applyTime = applyTime;
	}

	public String getApplyRemark() {
		return applyRemark;
	}

	public void setApplyRemark(String applyRemark) {
		this.applyRemark = applyRemark;
	}

	public String getApplyLevel() {
		return applyLevel;
	}

	public void setApplyLevel(String applyLevel) {
		this.applyLevel = applyLevel;
	}

	public String getFlowCode() {
		return flowCode;
	}

	public void setFlowCode(String flowCode) {
		this.flowCode = flowCode;
	}

	public String getFlowName() {
		return flowName;
	}

	public void setFlowName(String flowName) {
		this.flowName = flowName;
	}

	public String getDeptId() {
		return deptId;
	}

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getNextResultId() {
		return nextResultId;
	}

	public void setNextResultId(String nextResultId) {
		this.nextResultId = nextResultId;
	}

	public String getNextNodeId() {
		return nextNodeId;
	}

	public void setNextNodeId(String nextNodeId) {
		this.nextNodeId = nextNodeId;
	}

	public FlowModel getFlow() {
		return flow;
	}

	public void setFlow(FlowModel flow) {
		this.flow = flow;
	}

	public String getApproverField() {
		if(approverField==null) {
			return "appply_by";
		}
		return approverField;
	}

	public void setApproverField(String approverField) {
		this.approverField = approverField;
	}
	
	
	public String getFkId() {
		return fkId;
	}
	public void setFkId(String fkId) {
		this.fkId = fkId;
	}
	public String getRelatedFlow() {
		return relatedFlow;
	}
	public void setRelatedFlow(String relatedFlow) {
		this.relatedFlow = relatedFlow;
	}
	public Map<String, String> getData() {
		return data;
	}
	
	public void setData(Map<String, String> data) {
		this.data = data;
	}
	
	public String getData(int num) {
		if(num==0) {
			return getApplyBy();
		}
		return data.get("data"+num);
	}
	
	public String getData(String dataName) {
		if(StringUtils.isBlank(dataName)) {
			return getApplyBy();
		}
		String val = data.get(dataName);
		return val;
	}
	
	public String getApplyUserId() {
		String approverField = getApproverField();
		if(approverField.startsWith("data")) {
			return getData(approverField);
		}
		return getApplyBy();
	}
	
}
