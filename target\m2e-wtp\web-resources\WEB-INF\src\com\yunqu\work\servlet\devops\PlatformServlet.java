package com.yunqu.work.servlet.devops;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.PlatformModel;
/**
 * 文档
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/platform/*")
public class PlatformServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		PlatformModel model=getModel(PlatformModel.class, "platform");
		model.setPrimaryValues(RandomKit.uniqueStr());
		model.addCreateTime();
		model.setCreator(getUserId());
		model.set("update_time", EasyDate.getCurrentDateString());
		model.set("create_name",getUserName());
		model.set("platform_name",model.getString("PROJECT_NAME"));
		try {
			JSONObject jsonObject=getJSONObject();
			JSONArray hosts=jsonObject.getJSONArray("hosts");
			JSONArray services=jsonObject.getJSONArray("services");
			JSONArray projects=jsonObject.getJSONArray("projects");
			model.save();
			if(hosts!=null){
				for(int i=0;i<hosts.size();i++){
					String hostId=hosts.getString(i);
					EasyRecord record=new EasyRecord("yq_platform_host", "platform_id","host_id");
					record.setPrimaryValues(model.getPlatformId(),hostId);
					this.getQuery().save(record);
				}
			}
			if(services!=null){
				for(int i=0;i<services.size();i++){
					String serviceId=services.getString(i);
					EasyRecord record=new EasyRecord("yq_platform_service", "platform_id","service_id");
					record.setPrimaryValues(model.getPlatformId(),serviceId);
					this.getQuery().save(record);
				}
			}
			if(projects!=null){
				for(int i=0;i<projects.size();i++){
					String projectId=projects.getString(i);
					EasyRecord record=new EasyRecord("yq_platform_project", "platform_id","project_id");
					record.setPrimaryValues(model.getPlatformId(),projectId);
					this.getQuery().save(record);
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		PlatformModel model=getModel(PlatformModel.class, "platform");
		try {
			model.set("platform_name",model.getString("PROJECT_NAME"));
			model.set("update_time", EasyDate.getCurrentDateString());
			JSONObject jsonObject=getJSONObject();
			JSONArray hosts=jsonObject.getJSONArray("hosts");
			JSONArray services=jsonObject.getJSONArray("services");
			JSONArray projects=jsonObject.getJSONArray("projects");
			if(hosts!=null){
				this.getQuery().executeUpdate("delete from yq_platform_host where platform_id = ?", model.getPlatformId());
				for(int i=0;i<hosts.size();i++){
					String hostId=hosts.getString(i);
					EasyRecord record=new EasyRecord("yq_platform_host", "platform_id","host_id");
					record.setPrimaryValues(model.getPlatformId(),hostId);
					this.getQuery().save(record);
				}
			}
			if(services!=null){
				this.getQuery().executeUpdate("delete from yq_platform_service where platform_id = ?", model.getPlatformId());
				for(int i=0;i<services.size();i++){
					String serviceId=services.getString(i);
					EasyRecord record=new EasyRecord("yq_platform_service", "platform_id","service_id");
					record.setPrimaryValues(model.getPlatformId(),serviceId);
					this.getQuery().save(record);
				}
			}
			if(projects!=null){
				this.getQuery().executeUpdate("delete from yq_platform_project where platform_id = ?", model.getPlatformId());
				for(int i=0;i<projects.size();i++){
					String projectId=projects.getString(i);
					EasyRecord record=new EasyRecord("yq_platform_project", "platform_id","project_id");
					record.setPrimaryValues(model.getPlatformId(),projectId);
					this.getQuery().save(record);
				}
			}
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		PlatformModel model=getModel(PlatformModel.class, "platform");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	   return EasyResult.ok();
	}
}





