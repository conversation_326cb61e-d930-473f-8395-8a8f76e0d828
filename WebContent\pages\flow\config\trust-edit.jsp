<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>config</title>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editConfForm" autocomplete="off" data-mars="FlowConfigDao.trustInfo" data-mars-prefix="conf.">
	     	    <input type="hidden" id="id" value="${param.id}" name="conf.ID"/>
	     	    <input type="hidden" value="${param.id}" name="id"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px;">流程编号</td>
		                    <td>
		                    	<select name="conf.FLOW_CODE"  data-mars="FlowConfigDao.flowDict" data-rules="required" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="0">全部流程</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">委托人</td>
		                    <td>
		                    	<input type="hidden" name="conf.ASSIGNOR"/>
		                    	<input type="text" data-rules="required" onclick="singleUser(this)" readonly="readonly" class="form-control input-sm" name="conf.ASSIGNOR_NAME"/>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">被委托人</td>
		                    <td>
		                    	<input type="hidden" name="conf.ENTRUSTED"/>
		                    	<input type="text" data-rules="required" onclick="singleUser(this)" readonly="readonly" class="form-control input-sm" name="conf.ENTRUSTED_NAME"/>
		                    </td>
		                </tr>
		                <tr>
		                    <td style="width: 80px;">备注</td>
		                    <td>
		                    	<input name="conf.REMARK" class="form-control input-sm" data-rules="required">
		                    </td>
		                </tr>
		                <tr>
		                    <td style="width: 80px;">状态</td>
		                    <td>
		                    	<select name="conf.STATE" class="form-control input-sm" data-rules="required">
		                    		<option value="0">启用</option>
		                    		<option value="1">暂停</option>
		                    	</select>
		                    </td>
		                </tr>
		                
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c" style="z-index: 999999999999;">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="RoleConf.ajaxSubmitForm()"> 确 定  </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
        
		var RoleConf={id:$("#id").val()};
        
		RoleConf.ajaxSubmitForm = function(){
			if(form.validate("#editConfForm")){
				if(RoleConf.id==''){
					RoleConf.updateData('add'); 
				}else{
					RoleConf.updateData('update'); 
				}
			};
		}
        RoleConf.updateData = function(flag) {
			var data = form.getJSONObject("#editConfForm");
			ajax.remoteCall("${ctxPath}/web/flow/config/"+flag+"Trust",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose("#editConfForm");
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		$(function(){
			 requreLib.setplugs('select2',function(){
				$("#editConfForm").render({success:function(result){
					 $("#editConfForm select").select2({theme: "bootstrap",placeholder:'请选择'});
					 if(RoleConf.id){
						 
					 }
				}});
			 });
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>