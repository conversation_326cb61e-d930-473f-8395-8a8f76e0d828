package com.yunqu.work.model;

import java.io.Serializable;

import org.easitline.common.db.EasyRecord;

public class ApproveNodeRecord extends EasyRecord implements Serializable{
	private static final long serialVersionUID = 1L;

	public ApproveNodeRecord() {
		this.setTableInfo("YQ_FLOW_APPROVE_NODE","NODE_ID");
	}

	public ApproveNodeRecord(String nodeId,String nodeName,String checkBy,String checkName) {
		setCheckBy(checkBy);
		setNodeId(nodeId);
		setNodeName(nodeName);
		setNodeCode(nodeId);
		setCheckName(checkName);
	}
	
	private String nodeId;
	
	private String prevNodeId;
	
	private String nextNodeId;
	
	private String nodeName;
	
	private String checkBy;
	
	private Integer checkType;
	
	private String checkName;
	
	private String checkRoleId;
	
	private String flowCode;
	
	private String nodeCode;
	
	private String ccIds;
	
	private String ccNames;
	
	private int hourLimit = 0;
	
	private int ccFlag;
	
	private int returnRule;
	
	private int signFlag;
	
	private String opinionTitle;
	
	private String editField;
	
	private String approveCode;
	
	private String submitCode;
	

	public String getNodeId() {
		return nodeId;
	}

	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
		this.set("node_id",nodeId);
	}

	public String getPrevNodeId() {
		return prevNodeId;
	}

	public void setPrevNodeId(String prevNodeId) {
		this.prevNodeId = prevNodeId;
		this.set("p_node_id",prevNodeId);
	}

	public String getNextNodeId() {
		return nextNodeId;
	}

	public void setNextNodeId(String nextNodeId) {
		this.nextNodeId = nextNodeId;
		this.set("c_node_id",nextNodeId);
		
	}

	public String getNodeName() {
		return nodeName;
	}

	public void setNodeName(String nodeName) {
		this.nodeName = nodeName;
		this.set("node_name",nodeName);
	}

	public String getCheckBy() {
		return checkBy;
	}

	public void setCheckBy(String checkBy) {
		this.checkBy = checkBy;
		this.set("user_id",checkBy);
	}

	public String getCheckName() {
		return checkName;
	}

	public void setCheckName(String checkName) {
		this.checkName = checkName;
		this.set("user_name",checkName);
	}

	public String getFlowCode() {
		return flowCode;
	}

	public void setFlowCode(String flowCode) {
		this.flowCode = flowCode;
	}

	public Integer getCheckType() {
		return checkType;
	}

	public void setCheckType(Integer checkType) {
		this.checkType = checkType;
		this.set("check_type",checkType);
	}

	public String getCheckRoleId() {
		return checkRoleId;
	}

	public void setCheckRoleId(String checkRoleId) {
		this.checkRoleId = checkRoleId;
		this.set("role_id",checkRoleId);
	}

	public String getNodeCode() {
		return nodeCode;
	}

	public void setNodeCode(String nodeCode) {
		this.nodeCode = nodeCode;
		this.set("node_code",nodeCode);
	}

	public String getCcIds() {
		return ccIds;
	}

	public void setCcIds(String ccIds) {
		this.ccIds = ccIds;
		this.set("cc_ids",ccIds);
	}

	public String getCcNames() {
		return ccNames;
	}

	public void setCcNames(String ccNames) {
		this.ccNames = ccNames;
		this.set("cc_names",ccNames);
	}

	public int getHourLimit() {
		return hourLimit;
	}

	public void setHourLimit(int hourLimit) {
		this.hourLimit = hourLimit;
		this.set("hour_limit",hourLimit);
	}

	public int getCcFlag() {
		return ccFlag;
	}

	public void setCcFlag(int ccFlag) {
		this.ccFlag = ccFlag;
		this.set("cc_flag",ccFlag);
	}

	public int getReturnRule() {
		return returnRule;
	}

	public void setReturnRule(int returnRule) {
		this.returnRule = returnRule;
		this.set("return_rule",returnRule);
	}

	/**
	 * 会签 1开始 0 关闭
	 * @return
	 */
	public int getSignFlag() {
		return signFlag;
	}

	public void setSignFlag(int signFlag) {
		this.signFlag = signFlag;
		this.set("sign_flag",signFlag);
	}

	public String getOpinionTitle() {
		return opinionTitle;
	}

	public void setOpinionTitle(String opinionTitle) {
		this.opinionTitle = opinionTitle;
		this.set("opinion_title",opinionTitle);
	}

	public String getEditField() {
		return editField;
	}

	public void setEditField(String editField) {
		this.editField = editField;
		this.set("edit_field",editField);
	}

	public String getApproveCode() {
		return approveCode;
	}

	public void setApproveCode(String approveCode) {
		this.approveCode = approveCode;
		this.set("approve_code",approveCode);
	}

	public String getSubmitCode() {
		return submitCode;
	}

	public void setSubmitCode(String submitCode) {
		this.submitCode = submitCode;
		this.set("submit_code",submitCode);
	}

	
	

}
