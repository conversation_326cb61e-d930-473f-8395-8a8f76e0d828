package com.yunqu.work.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Random;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.StaffModel;
/**
 * https://console.bce.baidu.com/tools/#/api?product=AI&project=文字识别&parent=鉴权认证机制&api=oauth/2.0/token&method=post
 * <AUTHOR>
 *
 */
public class BdOcrUtiles extends AppBaseService{
	
	private static class Holder{
		private static BdOcrUtiles service=new BdOcrUtiles();
	}
	public static BdOcrUtiles getService(){
		return Holder.service;
	}
	
	private static List<String> clientIds = new ArrayList<String>();
	private static List<String> clientSecrets = new ArrayList<String>();
	private static List<String> tokens = new ArrayList<String>();
	private static List<Long> tokenExpires = new ArrayList<Long>();
	
	//默认30天
	private static Long tokenTime = null;
	
	static {
		//602932044
		clientIds.add(0,"gaWQiSpnFrIQpBw1Vsyfc2nl");
		clientSecrets.add(0,"xAjpEcuMWQQ1GL5n4EV6moE0oGwGx7au");
		tokens.add(0,"");
		
		//464193096
		clientIds.add(1,"jUa6zBo1jGqgYdik7aRBAz2Z");
		clientSecrets.add(1,"yVvID1tabd4XO6rEWoyuvOEjwUUxu0Nc");
		tokens.add(1,"");
		
		//dv
		clientIds.add(2,"Zxs2mg827i3xnWQyCQX3hg4t");
		clientSecrets.add(2,"M4gB1Il7ihZHNO9NLhoGIxOYQ1i8UDhZ");
		tokens.add(2,"");
	}
	
	public static void clearData() {
		tokens.clear();
		tokenExpires.clear();
		tokenTime = null;
		getToken();
	}
	
	public static void getToken() {
		for(int index=0;index<clientIds.size();index++) {
			String url = "https://aip.baidubce.com/oauth/2.0/token";
			Connection connect = Jsoup.connect(url);
			try {
				connect.data("client_id", clientIds.get(index));
				connect.data("client_secret", clientSecrets.get(index));
				
				connect.data("grant_type", "client_credentials");
				connect.header("Content-Type", "application/json");
				connect.header("Accept", "application/json");
				Response execute = connect.ignoreContentType(true).timeout(5000).method(Method.POST).execute();
				String result = execute.body();
				LogUtils.getLogger().info("OCR_Token>>"+result, null);
				
				JSONObject object = JSONObject.parseObject(result);
				tokens.add(index,object.getString("access_token"));
				tokenExpires.add(index, object.getLongValue("expires_in"));
			} catch (Exception e) {
				LogUtils.getLogger().error(e.getMessage(), e);
			}
		}
		
	}
	
	public static String[] token() {
		if(tokenTime==null) {
			tokenTime = System.currentTimeMillis();
			getToken();
		}
		if(System.currentTimeMillis() - tokenTime>1000*60*60*24*10) {
			tokenTime = System.currentTimeMillis();
			getToken();
		}
		
		int min = 0; // 最小值
        int max =  clientIds.size()-1; // 最大值
		Random random = new Random();
        int index = random.nextInt(max - min + 1) + min;
		return new String[] {tokens.get(index),clientIds.get(index)};
	}
	
	
	/**
	 * https://console.bce.baidu.com/tools/#/api?product=AI&project=文字识别&parent=财务票据OCR&api=rest/2.0/ocr/v1/vat_invoice&method=post
	 * @return
	 */
	public String fpOcr(StaffModel staffModel,String fileUrl,String fsType) {
		try {
			Thread.sleep(200);
		} catch (InterruptedException ex) {
			LogUtils.getLogger().error(ex.getMessage(), ex);
		}
		
		String url = "https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice";
		Connection connect = Jsoup.connect(url);
		try {
			String[] tokenInfo = token();
			
			connect.header("Content-Type", "application/x-www-form-urlencoded");
			connect.header("Accept", "application/json");
			connect.data("access_token",tokenInfo[0]);
			
			if(fsType.equals(".pdf")) {
				connect.data("pdf_file",convertPdfToBase64(fileUrl));
			}else if(fsType.equals(".ofd")){
				connect.data("ofd_file",convertPdfToBase64(fileUrl));
			}else if(fsType.equals(".jpg")||fsType.equals(".jpeg")||fsType.equals(".png")||fsType.equals(".bmp")) {
				connect.data("image",imageToBase64(fileUrl));
			}
			Response execute = connect.ignoreContentType(true).timeout(12000).method(Method.POST).execute();
			String result = execute.body();
			
			EasyRecord record = new EasyRecord("yq_finance_ocr","id");
			record.set("id", RandomKit.uniqueStr());
			record.set("client_id", tokenInfo[1]);
			if(staffModel!=null) {
				record.set("req_user_id", staffModel.getUserId());
				record.set("req_user_name", staffModel.getUserName());
			}
			record.set("req_time",EasyDate.getCurrentDateString());
			record.set("month_id",EasyCalendar.newInstance().getFullMonth());
			record.set("date_id",EasyCalendar.newInstance().getDateInt());
			record.set("file_url",fileUrl);
			record.set("result",result);
			this.getQuery().save(record);
			
			LogUtils.getLogger().info(fileUrl+">>"+result, null);
			return result;
		} catch (Exception e) {
			LogUtils.getLogger().error(e.getMessage(), e);
		}
		return null;
	}
	
	
	  public static String convertPdfToBase64(String filePath) throws IOException {
        File file = new File(filePath);
        byte[] bytes = new byte[(int) file.length()];
        
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            fileInputStream.read(bytes);
        }
        
        return Base64.getEncoder().encodeToString(bytes);
    }
	  
	  public static String imageToBase64(String imagePath){
	        String base64Image = "";
	        File file = new File(imagePath);
	        try (FileInputStream imageInFile = new FileInputStream(file)) {
	            // 读取图片文件并转换为字节数组
	            byte[] imageData = new byte[(int) file.length()];
	            imageInFile.read(imageData);
	            // 将字节数组编码为base64字符串
	            base64Image = Base64.getEncoder().encodeToString(imageData);
	        } catch (IOException e) {
	        	LogUtils.getLogger().error("无法读取文件 " + imagePath + ": " + e.getMessage(),e);
	        }
	        return base64Image;
    }
	  
	  
	public static void main(String[] args) {
		System.out.println(BdOcrUtiles.getService().fpOcr(null,"E:\\download\\234420000001_18760351_广州云趣信息科技有限公司.pdf",".pdf"));
	}

}
