/*! @wcjiang/notify v2.1.0 | MIT (c) 2022 kenny wang | http://jaywcjlove.github.io/iNotify */
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(t="undefined"!=typeof globalThis?globalThis:t||self).Notify=i()}(this,(function(){"use strict";window.Notification&&"granted"!==window.Notification.permission&&window.Notification.requestPermission();let t="";const i=["flash","scroll"],e={title:"iNotify !",body:"You have a new message.",openurl:""};function o(t){const i=document.createElement("audio");let e;if(i.autoplay=!0,i.muted=!0,n=t,"[object Array]"===Object.prototype.toString.call(n)&&t.length>0)for(let n=0;n<t.length;n++)e=document.createElement("source"),e.src=t[n],e.type=`audio/${o=t[n],o.match(/\.([^\\.]+)$/)[1]}`,i.appendChild(e);else i.src=t;var o,n;return i}function n(i,e){const o=document.createElement("canvas"),n=document.getElementsByTagName("head")[0],s=document.createElement("link");let l=null;return o.height=32,o.width=32,l=o.getContext("2d"),l.fillStyle=e.backgroundColor,l.fillRect(0,0,32,32),l.textAlign="center",l.font='22px "helvetica", sans-serif',l.fillStyle=e.textColor,i&&l.fillText(i,16,24),s.setAttribute("rel","shortcut icon"),s.setAttribute("type","image/x-icon"),s.setAttribute("id",`new${e.id}`),s.setAttribute("href",o.toDataURL("image/png")),t=o.toDataURL("image/png"),n.appendChild(s)}function s(t){t&&this.init(t)}return s.prototype={init(i){return i||(i={}),this.interval=i.interval||100,this.effect=i.effect||"flash",this.title=i.title||document.title,this.message=i.message||this.title,this.onclick=i.onclick||this.onclick,this.openurl=i.openurl||this.openurl,this.disableFavicon=i.disableFavicon||!1,this.updateFavicon=!this.disableFavicon&&(i.updateFavicon||{id:"favicon",textColor:"#fff",backgroundColor:"#2F9A00"}),this.audio=i.audio||"",this.favicon=!this.disableFavicon&&function(t){let i=document.querySelectorAll("link[rel~=shortcut]")[0];return i||(i=n("O",t)),i}(this.updateFavicon),this.cloneFavicon=this.favicon&&this.favicon.cloneNode(!0),t=i.notification&&i.notification.icon?i.notification.icon:i.icon?i.icon:this.favicon.href,e.icon=t,this.notification=i.notification||e,this.audio&&this.audio.file&&this.setURL(this.audio.file),this},render(){if("flash"===this.effect)document.title=this.title===document.title?this.message:this.title;else if("scroll"===this.effect){const t=this.message||document.title;this.scrollTitle&&this.scrollTitle.slice(1)?(this.scrollTitle=this.scrollTitle.slice(1),document.title=this.scrollTitle):(document.title=t,this.scrollTitle=t)}return this},setTitle(t){if(!0===t){if(i.indexOf(this.effect)>=0)return this.addTimer()}else t?(this.message=t,this.scrollTitle="",this.addTimer()):this.clearTimer();return this},setURL(t){return t&&(this.audioElm&&this.audioElm.remove(),this.audioElm=o(t),document.body.appendChild(this.audioElm)),this},loopPlay(){return this.setURL(),this.audioElm.loop=!0,this.player(),this},stopPlay(){return this.audioElm&&(this.audioElm.loop=!1,this.audioElm.pause()),this},player(){if(!this.audio||!this.audio.file)return;this.audioElm||(this.audioElm=o(this.audio.file),document.body.appendChild(this.audioElm)),this.audioElm.muted=!1;const t=this.audioElm.play();return void 0!==t&&t.then((()=>{})).catch((()=>{})),this},notify(i){let o=this.notification;const n=i.openurl?i.openurl:this.openurl,s=i.onclick?i.onclick:this.onclick;if(window.Notification){o=i?function(t,i){for(const e in i)t[e]&&(i[e]=t[e]);return i}(i,o):e;const l={};l.icon=i.icon?i.icon:t,l.body=o.body||i.body,i.dir&&(l.dir=i.dir);const c=new Notification(o.title||i.title,l);c.onclick=()=>{s&&"function"==typeof s&&s(c),n&&window.open(n)},c.onshow=()=>{i.onshow&&"function"==typeof i.onshow&&i.onshow(c)},c.onclose=()=>{i.onclose&&"function"==typeof i.onclose&&i.onclose(c)},c.onerror=()=>{i.onerror&&"function"==typeof i.onerror&&i.onerror(c)},this.Notifiy=c}return this},isPermission:()=>window.Notification&&"granted"===Notification.permission,setInterval(t){return t&&(this.interval=t,this.addTimer()),this},setFavicon(t){if(!t&&0!==t)return this.faviconClear();const i=document.getElementById(`new${this.updateFavicon.id}`);return this.favicon&&this.favicon.remove(),i&&i.remove(),this.updateFavicon.num=t,n(t,this.updateFavicon),this},setFaviconColor(t){return t&&(this.faviconRemove(),this.updateFavicon.textColor=t,n(this.updateFavicon.num,this.updateFavicon)),this},setFaviconBackgroundColor(t){return t&&(this.faviconRemove(),this.updateFavicon.backgroundColor=t,n(this.updateFavicon.num,this.updateFavicon)),this},faviconRemove(){this.faviconClear();const t=document.getElementById(`new${this.updateFavicon.id}`);this.favicon&&this.favicon.remove(),t&&t.remove()},addTimer(){return this.clearTimer(),i.indexOf(this.effect)>=0&&(this.timer=setInterval(this.render.bind(this),this.interval)),this},close(){this.Notifiy&&this.Notifiy.close()},faviconClear(){const i=document.getElementById(`new${this.updateFavicon.id}`),e=document.getElementsByTagName("head")[0],o=document.querySelectorAll("link[rel~=shortcut]");if(i&&i.remove(),o.length>0)for(let t=0;t<o.length;t++)o[t].remove();return e.appendChild(this.cloneFavicon),t=this.cloneFavicon.href,this.favicon=this.cloneFavicon,this},clearTimer(){return this.timer&&clearInterval(this.timer),document.title=this.title,this}},s}));
//# sourceMappingURL=notify.min.js.map