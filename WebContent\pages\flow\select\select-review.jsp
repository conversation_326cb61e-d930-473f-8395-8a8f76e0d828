<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择客户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selecReviewForm">
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
					 <span class="input-group-addon">名称</span>	
					 <input type="text" name="contractName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectReview.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		</div>
            	<div class="ibox">
              		<table id="selectReviewList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectReview.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectReview={
					sid:'${param.sid}',
					query : function(){
						$("#selecReviewForm").queryData();
					},
					initTable : function(){
						$("#selecReviewForm").initTable({
							mars:'ReviewDao.selectData',
							id:'selectReviewList',
							page:true,
							limit:20,
							height:'400px',
							rowDoubleEvent:'SelectReview.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'CONTRACT_NAME',
								title: '合同名称'
							},{
							    field: 'APPLY_NO',
								title: '编号',
								width:120
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectReview.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('selectReviewList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var nos = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['CONTRACT_NAME']);
									ids.push(data[index]['BUSINESS_ID']);
									nos.push(data[index]['APPLY_NO']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								if(el.prev().prev().length>0){
									el.prev().prev().val(nos.join(','));
								}
								selectReviewCallBack(data[0]);
								popup.layerClose("selecReviewForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							el.val(selectRow['CONTRACT_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['BUSINESS_ID']);
							}
							if(el.prev().prev().length>0){
								el.prev().prev().val(selectRow['APPLY_NO']);
							}
							selectReviewCallBack(selectRow);
							popup.layerClose("selecReviewForm");
						}
					}
					
			};
			$(function(){
				SelectReview.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>