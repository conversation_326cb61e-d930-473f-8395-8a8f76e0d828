package com.yunqu.work.dao.other;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.FolderModel;

@WebObject(name="FolderDao")
public class FolderDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		FolderModel model=new FolderModel();
		model.setColumns(getParam("folder"));
		return queryForRecord(model);
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select folder_id,folder_name from YQ_FOLDER");
	}
	
	@WebControl(name="docFolder",type=Types.LIST)
	public JSONObject docFolder(){
		if(isSuperUser()) {
			EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
			sql.append(param.getString("source"),"and source = ?","0");
			return queryForList(sql.getSQL(), sql.getParams());
		}
		EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
		sql.append(1,"and folder_auth = ?");
		sql.append(param.getString("source"),"and source = ?","0");
		
		sql.append(getUserId(),"UNION select * from yq_folder where folder_auth = 2 and creator= ?");
		sql.append(param.getString("source"),"and source = ?","0");
		
		
		sql.append(getDeptId(),"UNION select * from yq_folder where folder_auth = 3 and dept_id = ?");
		sql.append(param.getString("source"),"and source = ?","0");
		
		sql.append("UNION select * from yq_folder where folder_auth = 4");
		sql.append(param.getString("source"),"and source = ?","0");
		sql.append(getUserId(),"and FIND_IN_SET(?,SHARE_USER_IDS)");
		
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 文档库
	 * @return
	 */
	@WebControl(name="list",type=Types.TEMPLATE)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
		sql.append(param.getString("folderId"),"and p_folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and folder_name like ?");
		sql.appendLike(param.getString("folderCode"),"and folder_code like ?");
		sql.append(param.getString("fkId"),"and fk_id = ?");
		sql.append(1,"and folder_auth = ?");
		sql.append(param.getString("source"),"and source = ?","0");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 文件夹下面的文件
	 * @return
	 */
	@WebControl(name="files",type=Types.LIST)
	public JSONObject files(){
		EasySQL sql=getEasySQL("select * from yq_doc where 1=1");
		sql.append(param.getString("folderId"),"and folder_id = ?");
		sql.appendLike(param.getString("folderName"),"and title like ?");
		sql.append(1,"and doc_auth = ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 自己创建的文档目录
	 * @return
	 */
	@WebControl(name="mylist",type=Types.LIST)
	public JSONObject mylist(){
		EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
		sql.append(param.getString("folderId"),"and p_folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and folder_name like ?");
		sql.append(getUserPrincipal().getUserId(),"and creator = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 文件库
	 * @return
	 */
	@WebControl(name="myfiles",type=Types.LIST)
	public JSONObject myfiles(){
		EasySQL sql=getEasySQL("select * from yq_files where 1=1");
		sql.append(param.getString("folderId"),"and folder_id = ?");
		sql.appendLike(param.getString("fileName"),"and file_name like ?");
		sql.append(param.getString("source"),"and source = ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 自己创建的文档文件
	 * @return
	 */
	@WebControl(name="mydoc",type=Types.LIST)
	public JSONObject mydoc(){
		EasySQL sql=getEasySQL("select * from yq_doc where 1=1");
		sql.append(param.getString("folderId"),"and folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and title like ?");
		sql.append(getUserPrincipal().getUserId(),"and creator = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}

}
