:root {
    --color-state-04: rgba(177,184,191,0.25);
    --color-other-7: rgba(177,184,191,0.25);
    --color-text1-8: #404040;
    --color-tag-disable: #68bbf2;
    --color-other-16: #68bbf2;
    --color-tag-hover: #003e7c;
    --color-other-15: #003e7c;
    --color-tag-bg: #c5e8ff;
    --color-other-14: #c5e8ff;
    --color-tag-al: #0055ab;
    --color-other-13: #0055ab;
    --color-fill1-10: #f7f7f7;
    --color-link-3: #0055ab;
    --color-link-2: #0488de;
    --color-link-1: #006ad4;
    --color-fill1-2: #f2f5f7;
    --color-priority-3: #c6cdd4;
    --color-other-12: #c6cdd4;
    --color-priority-2: #0488de;
    --color-other-11: #0488de;
    --color-priority-2-n: #ffdf40;
    --color-other-10: #ffdf40;
    --color-priority-1: #ff9d28;
    --color-other-9: #ff9d28;
    --color-priority-0: #e95648;
    --color-other-8: #e95648;
    --color-state-02: #5fce9d;
    --color-other-5: #5fce9d;
    --color-state-03: #ffa428;
    --color-other-6: #ffa428;
    --color-data1-6: #ffa428;
    --color-state-01: #2a9feb;
    --color-other-4: #2a9feb;
    --color-notice-6: #0055ab;
    --color-brand2-1: #ddf1ff;
    --color-brand2-2: #c5e8ff;
    --color-notice-7: #2a9feb;
    --color-warning-7: #f29849;
    --color-error-7: #f36e62;
    --color-success-7: #5bce85;
    --color-brand1-7: #0055ab;
    --color-brand1-8: #003e7c;
    --color-fill1-7: #7d8389;
    --color-notice-5: #006ad4;
    --color-warning-5: #e28200;
    --color-warning-6: #a96500;
    --color-error-5: #cd3021;
    --color-error-6: #a91002;
    --color-success-6: #24933a;
    --color-success-5: #35af58;
    --color-brand1-3: #68bbf2;
    --color-brand1-4: #2a9feb;
    --color-brand1-5: #0488de;
    --color-brand1-10: #00254c;
    --color-error-4: #e95648;
    --color-error-3: #f7968d;
    --color-error-2: #f8bbb5;
    --color-error-1: #ffe8e6;
    --color-warning-4: #ed9011;
    --color-warning-3: #f7b174;
    --color-warning-2: #ffd9b7;
    --color-warning-1: #ffefe3;
    --color-notice-4: #0488de;
    --color-notice-3: #68bbf2;
    --color-notice-1: #ddf1ff;
    --color-success-4: #45c572;
    --color-success-3: #78d794;
    --color-success-2: #aae4ba;
    --color-success-1: #d6f6da;
    --color-brand1-9: #003263;
    --color-brand1-6: #006ad4;
    --color-brand1-1: #ddf1ff;
    --color-fill1-6: #6b6b6b;
    --color-text1-6: hsla(0,0%,80.8%,0.7);
    --color-text1-7: #8b8b8b;
    --color-gradient-4: linear-gradient(270deg,#ffa3a6,#f52743);
    --color-gradient-3: linear-gradient(270deg,#ffed75,#f5cb22);
    --color-gradient-2: linear-gradient(270deg,#7deeff,#03c1fd);
    --color-gradient-1: linear-gradient(270deg,#79e8c7,#08c29e);
    --color-transparent: transparent;
    --color-black: #000;
    --color-white: #fff;
    --color-fill1-8: #62656f;
    --color-fill1-9: #494e54;
    --color-line1-5: #b1b8bf;
    --color-fill1-5: #c6cdd4;
    --color-fill1-4: #dae0e5;
    --color-fill1-3: #e9edf0;
    --color-line1-3: #c6cdd4;
    --color-line1-2: #dae0e5;
    --color-line1-1: #e9edf0;
    --color-brand3-1: #f2f5f7;
    --color-brand3-2: #e9edf0;
    --color-brand3-4: #c6cdd4;
    --color-brand3-3: #dae0e5;
    --color-brand3-6: #7d8389;
    --color-brand3-5: #b1b8bf;
    --color-brand3-7: #62656f;
    --color-brand3-8: #494e54;
    --color-text1-5: hsla(0,0%,80.8%,0.4);
    --color-help-7: #ffeb6b;
    --color-help-5: #fad414;
    --color-help-6: #d1b10d;
    --color-brand1-2: #c5e8ff;
    --color-text1-4: #292929;
    --color-text1-3: #575757;
    --color-text1-2: #6e6e6e;
    --color-text1-1: #adadad;
    --color-fill1-1: #fff;
    --color-line1-4: #fff;
    --color-other-3: #131415;
    --color-other-2: #fff;
    --color-other-1: #3a3c4b;
    --color-help-4: #f8e71c;
    --color-help-3: #ffee96;
    --color-help-2: #fff5c2;
    --color-help-1: #fffcf0;
    --color-notice-2: #c5e8ff;
    --color-data1-8: #5c60e6;
    --color-data1-7: #4ad051;
    --color-data1-5: #ff6a00;
    --color-data1-4: #ff656b;
    --color-data1-3: #9979f2;
    --color-data1-2: #006cd9;
    --color-data1-1: #5ccdbb;
    --font-size-caption: 12px;
    --font-size-body-1: 14px;
    --font-size-body-2: 14px;
    --font-weight-ultra-bold: 900;
    --font-weight-extra-bold: 800;
    --font-weight-3: bold;
    --font-weight-semi-bold: 600;
    --font-weight-medium: 500;
    --font-weight-2: normal;
    --font-weight-light: 300;
    --font-weight-thin: 200;
    --font-weight-1: lighter;
    --font-lineheight-2: 1.5;
    --font-name-bold: Alibaba-PuHuiTi-Bold;
    --font-name-medium: Alibaba-PuHuiTi-Medium;
    --font-name-light: Alibaba-PuHuiTi-Light;
    --font-name-regular: Alibaba-PuHuiTi-Regular;
    --font-custom-name: 阿里巴巴普惠体;
    --font-name-thin: Alibaba-PuHuiTi-Light;
    --font-custom-path: "//alifd.alicdn.com/fonts/ali-puhuiti/";
    --font-size-subhead: 14px;
    --font-size-title: 16px;
    --font-size-headline: 18px;
    --font-size-display-3: 40px;
    --font-lineheight-3: 2;
    --font-lineheight-1: 1;
    --font-family-base: 阿里巴巴普惠体,"Helvetica Neue","PingFang SC","Noto Sans","Noto Sans CJK SC","Microsoft YaHei","微软雅黑",sans-serif;
    --font-size-display-1: 24px;
    --font-size-display-2: 32px;
   
    --line-dotted: dotted;
    --line-dashed: dashed;
    --line-solid: solid;
    --line-2: 2px;
    --line-1: 1px;
    --line-zero: 0px;
    --line-3: 4px;
    --mask-background: var(--color-black,#000);
    --mask-opacity: 0.4;
    --s-50: 200px;
    --s-49: 196px;
    --s-48: 192px;
    --s-47: 188px;
    --s-46: 184px;
    --s-45: 180px;
    --s-44: 176px;
    --s-43: 172px;
    --s-42: 168px;
    --s-41: 164px;
    --s-40: 160px;
    --s-39: 156px;
    --s-38: 152px;
    --s-37: 148px;
    --s-36: 144px;
    --s-35: 140px;
    --s-34: 136px;
    --s-33: 132px;
    --s-32: 128px;
    --s-31: 124px;
    --s-30: 120px;
    --s-29: 116px;
    --s-28: 112px;
    --s-27: 108px;
    --s-26: 104px;
    --s-25: 100px;
    --s-24: 96px;
    --s-23: 92px;
    --s-22: 88px;
    --s-21: 84px;
    --s-20: 80px;
    --s-19: 76px;
    --s-18: 72px;
    --s-17: 68px;
    --s-16: 64px;
    --s-15: 60px;
    --s-14: 56px;
    --s-13: 52px;
    --s-12: 48px;
    --s-11: 44px;
    --s-10: 40px;
    --s-9: 36px;
    --s-8: 32px;
    --s-7: 28px;
    --s-6: 24px;
    --s-5: 20px;
    --s-4: 16px;
    --s-3: 12px;
    --s-2: 8px;
    --s-1: 4px;
    --s-zero: 0px;
    --size-base: 4px;
    --shadow-3-left: 0px 0px 20px 0px rgba(0,0,0,0.3);
    --shadow-3-down: 0px 10px 20px 0px rgba(0,0,0,0.3);
    --shadow-3-right: 0px 0px 20px 0px rgba(0,0,0,0.3);
    --shadow-3-up: 0px -10px 20px 0px rgba(0,0,0,0.3);
    --shadow-3: 0px 10px 20px 0px rgba(0,0,0,0.3);
    --shadow-2-left: 0px 0px 15px 0px rgba(0,0,0,0.15);
    --shadow-2-down: 0px 5px 15px 0px rgba(0,0,0,0.15);
    --shadow-2-right: 0px 0px 15px 0px rgba(0,0,0,0.15);
    --shadow-2-up: 0px -5px 15px 0px rgba(0,0,0,0.15);
    --shadow-2: 0px 5px 15px 0px rgba(0,0,0,0.15);
    --shadow-1-left: 0px 0px 8px 0px rgba(0,0,0,0.06);
    --shadow-1-down: 0px 3px 8px 0px rgba(0,0,0,0.06);
    --shadow-1-right: 0px 0px 8px 0px rgba(0,0,0,0.06);
    --shadow-1-up: 0px -3px 8px 0px rgba(0,0,0,0.06);
    --shadow-1: 0px 3px 8px 0px rgba(0,0,0,0.06);
    --shadow-zero: none;
    --shadow-spread-sd3: 0;
    --shadow-spread-sd2: 0;
    --shadow-spread-sd1: 0;
    --shadow-color-sd3: var(--color-black,#000);
    --shadow-color-sd1: var(--color-black,#000);
    --shadow-sides-left: "left";
    --shadow-sides-down: "down";
    --shadow-sides-right: "right";
    --shadow-sides-up: "up";
    --shadow-sides-base: "base";
    --shadow-blur-sd3: 20;
    --shadow-blur-sd2: 15;
    --shadow-opacity-sd3: 0.3;
    --shadow-opacity-sd2: 0.15;
    --shadow-opacity-sd1: 0.06;
    --shadow-distance-sd3y: 10;
    --shadow-distance-sd2y: 5;
    --shadow-blur-sd1: 8;
    --shadow-color-sd2: var(--color-black,#000);
    --shadow-distance-sd1y: 3;
    --shadow-distance-sd3: 0;
    --shadow-distance-sd2: 0;
    --shadow-distance-sd1: 0;
    --icon-reset: "";
    --icon-xxxl: var(--s-16,64px);
    --icon-xxl: var(--s-12,48px);
    --icon-xl: var(--s-8,32px);
    --icon-l: var(--s-6,24px);
    --icon-m: var(--s-5,20px);
    --icon-s: var(--s-4,16px);
    --icon-xs: var(--s-3,12px);
    --icon-xxs: var(--s-2,8px);
   
    --color-link-1: var(--color-brand1-6,#006ad4);
    --color-link-2: var(--color-brand1-5,#0488de);
    --color-link-3: var(--color-brand1-7,#0055ab);
}
