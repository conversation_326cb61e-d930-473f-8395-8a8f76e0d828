<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
	    .layui-table-tool {border: 1px solid #a9b7c1!important;}
		.layui-icon{font-size: 12px;}
		.ibox-content{min-height: calc(100vh - 70px)}
		.text-l{text-align: left!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					 		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}固定资产申请单"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请类别</td>
					  			<td>
					  				<input data-rules="required" data-text="显示器,主机,整套办公电脑,服务器,其他"   onclick="selectText(this);" class="form-control input-sm" name="apply.data1">
					  			</td>
					  			<td class="edit-empty">是否有存货</td>
					  			<td class="edit-empty">
					  				<select class="form-control input-sm" data-edit-node="warehouse" name="apply.data4">
					  					<option value=""></option>
					  					<option value="从存货调拨">从存货调拨</option>
					  					<option value="无存货,需新购">无存货,需新购</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">用途</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  			<td class="required">期望到货时间</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm Wdate"  data-laydate="{type:'date',min:0}" name="apply.data3"/>
					  			</td>
					  		</tr>
				  			<tr>
					  			<td>备注</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12">
		  				<div class="layui-col-md12 mt-10">
					     <div class="layui-table-tool">
							<div class="pull-left">资产信息</div>
							<div class="pull-right unedit-remove">
								<button class="btn btn-sm btn-default" onclick="addTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增行</button>
								<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
								<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
								<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
							</div>
					    </div>
					  	<div class="table-responsive" style="min-height: 100px;">
								 <table class="layui-table approve-table" style="margin-top: 0px;">
								   <thead>
								    <tr>
								      <th>序号</th>
								      <th>设备名称</th>
								      <th>规格</th>
								      <th>数量</th>
								      <th>预算金额</th>
								      <th>备注</th>
								    </tr> 
								  </thead>
								  <tbody data-mars="FlowCommon.items" data-template="flow-items"></tbody>
								  <tbody>
								  	<c:forEach var="item" begin="1000" end="1020">
									    <tr id="item_${item}" data-hide-tr="1" style="display:none;">
									      <td>
									      	<input type="hidden" name="order_index_${item}"/>
									      	<input type="hidden" value="${item}" name="itemIndex"/>
									        <label class="checkbox checkbox-inline checkbox-success">
										      	 <input tabindex="-1" type="checkbox" name="ids" value="${item}"><span></span>
									        </label>
									      </td>
									      <td>
									      	    <input name="data1_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									     	   <input name="data2_${item}" data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									      	   <input type="number" class="form-control input-sm"  data-rules="required" name="data3_${item}"/>
									      </td>
									      <td>
									     	   <input name="data5_${item}" class="form-control input-sm">
									      </td>
									      <td>
									      	  <textarea style="height: 40px;resize: none;" maxlength="255" class="form-control input-sm" name="data4_${item}"></textarea>
									      </td>
									    </tr>
								  	</c:forEach>
								  </tbody>	
								</table>
							</div>
					   </div>
				 
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	   <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
			
	  <script id="flow-items" type="text/x-jsrender">
  		 {{for data}}
			<tr id="item_{{:ITEM_ID}}">
			  <td>
				<input type="hidden" name="order_index_{{:ITEM_ID}}" value="{{:ITEM_INDEX}}"/>
				<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
				<span class="edit-remove">{{:ORDER_INDEX}}</span>
				<label class="checkbox checkbox-inline checkbox-success">
					 <input tabindex="-1" type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
				</label>
			  </td>
			  <td>
				  <input name="data1_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA1}}">
			  </td>
			  <td>
				  <input name="data2_{{:ITEM_ID}}" data-rules="required" class="form-control input-sm" value="{{:DATA2}}">
			  </td>
			  <td>
					<input type="number" class="form-control input-sm" data-rules="required" name="data3_{{:ITEM_ID}}" value="{{:DATA3}}"/>
			  </td>
			  <td>
				  <input name="data5_{{:ITEM_ID}}" class="form-control input-sm" value="{{:DATA5}}">
			  </td>
			  <td>
					<textarea style="height: 40px;resize: none;" maxlength="255" class="form-control input-sm" name="data4_{{:ITEM_ID}}">{{:DATA4}}</textarea>
			  </td>
			</tr>
 	     {{/for}}
   	  </script>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		var Flow = {};

		$(function(){
			FlowCore.initPage({success:function(result){
				if(FlowCore.businessId){
					
				}else{
					addTr();
				}
			}});
			
		});
		
		Flow.ajaxSubmitForm = function(state){
			$("[data-hide-tr]").remove();
			var index  = 1;
			$("[name^='order_index_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			
			FlowCore.ajaxSubmitForm(state);
		}
		
		
		Flow.insertData = function() {
		    FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.approverLayerBefore = function(){
			var params = {};
			var type = $("[name='apply.data4']").val();
			
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='warehouse'){
				if(type==''){
					layer.msg('请选择是否有存货并保存');
					return false;
				}
				if(type=='从存货调拨'){
					params['nextNodeCode'] = '部门主管';
				}
			}else if(nodeCode=='部门主管'&&type=='从存货调拨'){
				params['nextNodeCode'] = '0';
			}
			FlowCore.approveData = params;
			return true;
	  }
		
	  Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
	  }
		
	  function addTr(){
			var obj  = $("[data-hide-tr]").first();
			if(obj.length==0){
				layer.msg('不能再新增啦',{icon:7,shade: 0.1});
				return;
			}
			obj.removeAttr("data-hide-tr");
			obj.show();
		}

		function delTr(){
			layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
				layer.close(index);
				$("input[name='ids']:checked").each(function(){
					var id = this.value;
					if(id.length>20){
						ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=delItems",{itemId:id},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg);
								$('#item_'+id).remove();
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});					
					}else{
						$('#item_'+id).remove();
					}
					
				});
			});
		}
		
		function upTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var prevTR  = $('#item_'+id).prev();
				if (prevTR.length > 0) {
					prevTR.insertAfter($('#item_'+id));
				}
			}
		}
		function downTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var nextTR  = $('#item_'+id).next();
				if (nextTR.length > 0) {
					nextTR.insertBefore($('#item_'+id));
				}
			}
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>