package com.yunqu.work.utils;

public class NumberToChinese {
    public static void main(String[] args) {
        int number = 1; // 可以更改为其他数字
        System.out.println(convertToChinese(number));
    }

    public static String convertToChinese(int number) {
        String[] chineseNumbers = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        StringBuilder result = new StringBuilder();

        if (number >= 1000) {
            result.append(chineseNumbers[number / 1000]).append("千");
            number %= 1000;
        }
        if (number >= 100) {
            result.append(chineseNumbers[number / 100]).append("百");
            number %= 100;
        }
        if (number >= 10) {
            result.append(chineseNumbers[number / 10]).append("十");
            number %= 10;
        }
        if (number > 0) {
            result.append(chineseNumbers[number]);
        }

        return result.toString();
    }
}



