<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>data</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="DataEditForm" class="form-horizontal" data-mars="OrderDao.applyInfo" data-mars-prefix="order.">
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width:80px" class="required">订单号</td>
		                    <td style="width:50%;">
		                    	<input name="orderId" type="hidden" value="${param.orderId}"/>
		                    	<input name="order.ORDER_ID" type="hidden" value="${param.orderId}"/>
								<input data-rules="required" type="text" data-mars="OrderDao.getOrderNo" autocomplete="off" name="order.ORDER_NO" class="form-control"/>
		                    </td>
		                    <td style="width:80px" class="required">供应商</td>
		                    <td style="width:40%;">
		                   		 <select data-rules="required" data-mars="SupplierDao.dict" name="order.SUPPLIER_ID" class="form-control">
		                   			<option value="">请选择</option>
		                   		 </select>
		                    </td>
			            </tr>
			             <tr>
		                    <td width="100px" class="required">销售合同</td>
		                    <td>
		                    	<input  name="order.CONTRACT_NO" type="hidden"/>
		                    	<input  name="order.CUST_ID" type="hidden"/>
		                    	<input  name="order.CONTRACT_ID" type="hidden"/>
								<input type="text" data-rules="required" id="contractId" readonly="readonly" name="order.CONTRACT_NAME" autocomplete="off" class="form-control"/>
		                    </td>
		                	<td>采购类型</td>
		                    <td>
		                   		 <select data-rules="required" name="order.ORDER_TYPE" class="form-control">
		                   		 	<option value="">请选择</option>
		                   		 	<option value="0">销售采购</option>
		                   		 	<option value="1">固定资产</option>
		                   		 </select>
		                    </td>
			            </tr>
			            <tr>
		                    <td width="100px" class="required">采购标题</td>
		                    <td colspan="3">
								<input type="text" data-rules="required" name="order.ORDER_TITLE" autocomplete="off" class="form-control"/>
		                    </td>
			            </tr>
			            <tr>
		                    <td width="100px">维保合同</td>
		                    <td>
		                    	<input  name="order.WB_CONTRACT_ID" type="hidden"/>
								<input type="text" id="wbContractId" readonly="readonly" name="order.WB_CONTRACT_NAME" autocomplete="off" class="form-control"/>
		                    </td>
			            	<td class="required">付款工作日</td>
			            	<td>
			            		<input type="text" data-rules="required" name="order.PAY_DAY" autocomplete="off" class="form-control"/>
			            	</td>
			            </tr>
			            <tr>
			            	<td>签订到货天数</td>
			            	<td>
			            		<input type="text" name="order.GET_GOODS_DAY" autocomplete="off" class="form-control"/>
			            	</td>
			            	<td>保修年限</td>
			            	<td>
			            		<input type="text" name="order.BX_DATE" autocomplete="off" class="form-control"/>
			            	</td>
			            </tr>
			            <tr>
			            	<td class="required">付款比例</td>
			            	<td colspan="3">
			            		<input  data-rules="required" type="text" name="order.PAY_RATE" autocomplete="off" class="form-control"/>
			            	</td>
			            </tr>
			            <tr>
			            	<td>备注</td>
			               	<td colspan="3">
	                           <textarea style="height: 70px;" class="form-control" name="order.ORDER_REMARK"></textarea>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 100px" onclick="DataEit.ajaxSubmitForm()"> 提交 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("DataEit");
		
		layui.config({
			  base: '${ctxPath}/static/js/'
		}).use('tableSelect'); //加载自定义模块
		
		layui.use(['tableSelect'], function(){
			var tableSelect = layui.tableSelect;
			var array = ['contractId','wbContractId'];
			for(var index in array){
				tableSelect.render({
					elem: '#'+array[index],
					searchKey: 'contractName',
					checkedKey: 'CONTRACT_ID',
					page:true,
					searchPlaceholder: '请输入项目名称',
					table: {
						mars: 'ProjectContractDao.contractSelect',
						cols: [[
							{ type: 'radio' },
							{ field: 'CONTRACT_ID',hide:true,title: 'ID'},
							{ field: 'CONTRACT_NAME', title: '名称', width: 320 },
							{ field: 'CONTRACT_NO', title: '编号', width: 150 }
						]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						var custIds = [];
						var contractNo = [];
						layui.each(data.data, function (index, item) {
							names.push(item.CONTRACT_NAME)
							ids.push(item.CONTRACT_ID)
							custIds.push(item.CUST_ID)
							contractNo.push(item.CONTRACT_NO)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
						
						var id = elem.attr('id');
						if(id=='contractId'){
							$("[name='order.ORDER_TITLE']").val(names.join(","));
							elem.prev().prev().val(custIds.join(","));
							elem.prev().prev().prev().val(contractNo.join(","));
						}
					},
					clear:function(elem){
						 elem.prev().val("");
					}
				});
				
			}
		});
		
		$(function(){
			$("#DataEditForm").render({success:function(result){
				var orderInfo = result['OrderDao.applyInfo'];
				 requreLib.setplugs('select2',function(){
					$("select").select2({theme: "bootstrap",placeholder:'请选择'});
					$(".select2-container").css("width","100%");
			 	 });
			}});  
		});
		
		DataEit.ajaxSubmitForm = function(){
			if(form.validate("#DataEditForm")){
				var orderId = $("[name='orderId']").val();
				if(orderId){
					DataEit.updateData(); 
				}else{
					DataEit.insertData(); 
				}
			}
		}
		DataEit.insertData = function(flag) {
			var data = form.getJSONObject("#DataEditForm");
			ajax.remoteCall("${ctxPath}/servlet/order?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadDataList();
						popup.openTab({url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{orderId:result.data,newOrder:'1',ts:Date.parse(new Date())},id:result.data,title:'采购详情'});
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		DataEit.updateData = function(flag) {
			var data = form.getJSONObject("#DataEditForm");
			ajax.remoteCall("${ctxPath}/servlet/order?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						location.reload();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>