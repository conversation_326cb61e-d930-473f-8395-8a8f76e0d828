<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="status" type="hidden" value="${param.status}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-tasks"></span> <span id="title">任务管理</span></h5>
	          		     <div class="input-group input-group-sm"  style="width: 180px">
							 <span class="input-group-addon">所属项目</span>	
							 <select data-mars="ProjectDao.dict" name="projectId" class="form-control input-sm">
				                    <option value="">请选择</option>
				              </select>
					     </div>
	          		     <div class="input-group input-group-sm"  style="width: 180px">
							 <span class="input-group-addon">任务名称</span>	
							 <input type="text" name="taskName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm taskState" style="width: 180px">
							 <span class="input-group-addon">任务状态</span>	
							 <select name="taskState" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="10" data-class="label label-warning">待办中</option>
		                    		<option value="20" data-class="label label-info">进行中</option>
		                    		<option value="30" data-class="label label-success">已完成</option>
		                    		<option value="40" data-class="label label-default">已验收</option>
		                    		<option value="42" data-class="label label-danger">验收不通过</option>
	                    	</select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()"> + 发起任务</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
			{{if TASK_STATE==40}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
			 {{else TASK_STATE==42}}
  				<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.detail">重新处理</a>
			 {{else TASK_STATE==10}}
  				<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="list.detail">处理任务</a>
			 {{else TASK_STATE==30}}
  				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看详情</a>
			 {{else}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">更改进度</a>
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="bar2">
			{{if TASK_STATE==30}}
  				<a class="layui-btn layui-btn-success layui-btn-xs" lay-event="list.detail">验收</a>
			 {{else TASK_STATE==42}}
  				<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.detail">重新验收</a>
			 {{else TASK_STATE==10}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>
			{{else TASK_STATE==40}}
  				{{if TASK_AUTH==0}}<a class="layui-btn layui-btn-xs" lay-event="list.cannelShare">取消分享</a>{{else}}<a class="layui-btn layui-btn-xs" lay-event="list.share">分享</a>{{/if}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
			 {{else}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
			{{/if}}
		</script>
		<script type="text/html" id="bar3">
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		var status='${param.status}';
		$(function(){
			$("#searchForm").render({success:function(){
				if(status==1){
					list.init1();
					$("#title").text("我负责的任务");
				}
				if(status==2){
					list.init2();
					$("#title").text("我创建的任务");
				}
				if(status==3){
					list.init3();
					$("#title").text("我参与的任务");
				}
				if(status==4){
					list.init4();
					$("#title").text("范例任务");
				}
			}});
		});
		var list={
			init1:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					autoFill:true,
					title:'我负责的任务',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							width:80,
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'left',
							width:100,
							templet:function(row){
								return taskStateLabel(row.TASK_STATE);
							}
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'left',
							width:90,
							templet:function(row){
								return taskLevelText(row.TASK_LEVEL);
							}
						},{
						    field: 'CREATE_TIME',
							title: '发起时间',
							align:'center',
							width:140
						},{
						    field: 'DEADLINE_AT',
							title: '截止时间',
							align:'center',
							width:120
						},{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:100,
							templet:function(row){
								return getUserName(row.CREATOR);
							}
						},{
						    field: 'UPDATE_TIME',
							title: '更新时间',
							width:160,
							align:'left'
						},{
							title: '操作',
							align:'left',
							width:120,
							templet:function(row){
							    return renderTpl('bar1',row);
							}
						}
					]]}
				);
			},
			init2:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					autoFill:true,
					title:'我发起的任务',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'left',
							width:120,
							templet:function(row){
								return taskState(row.TASK_STATE);
							}
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'left',
							width:90,
							templet:function(row){
								return taskLevel(row.TASK_LEVEL);
							}
						},{
						    field: 'DEADLINE_AT',
							title: '截止时间',
							align:'center',
							width:120
						},{
						    field: 'ASSIGN_USER_ID',
							title: '负责人',
							align:'left',
							width:120,
							templet:function(row){
								return getUserName(row.ASSIGN_USER_ID);
							}
						},{
						    field: 'UPDATE_TIME',
							title: '更新时间',
							width:180,
							align:'left'
						},{
							title: '操作',
							align:'left',
							cellMinWidth:180,
							width:140,
							templet:function(row){
							    return renderTpl('bar2',row);
							}
						}
					]]}
				);
			},
			init3:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					autoFill:true,
					title:'我参与的任务',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'left',
							templet:function(row){
								return taskState(row.TASK_STATE);
							}
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'left',
							width:90,
							templet:function(row){
								return taskLevel(row.TASK_LEVEL);
							}
						},{
						    field: 'DEADLINE_AT',
							title: '截止时间',
							align:'center',
							width:120
						},{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:120,
							templet:function(row){
								return getUserName(row.CREATOR);
							}
						},{
						    field: 'ASSIGN_USER_ID',
							title: '负责人',
							align:'left',
							templet:function(row){
								return getUserName(row.ASSIGN_USER_ID);
							}
						},{
						    field: 'UPDATE_TIME',
							title: '更新时间',
							align:'left'
						},{
							title: '操作',
							align:'left',
							cellMinWidth:180,
							toolbar: '#bar3'
						}
					]]}
				);
			},
			init4:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					autoFill:true,
					title:'任务参考解决',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'left',
							width:90,
							templet:function(row){
								return taskLevel(row.TASK_LEVEL);
							}
						},{
						    field: 'FINISH_TIME',
							title: '完成时间',
							align:'center',
							width:160
						},{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:120,
							templet:function(row){
								return getUserName(row.CREATOR);
							}
						},{
						    field: 'ASSIGN_USER_ID',
							title: '负责人',
							align:'left',
							width:120,
							templet:function(row){
								return getUserName(row.ASSIGN_USER_ID);
							}
						},{
						    field: 'UPDATE_TIME',
							title: '更新时间',
							width:160,
							align:'left'
						}
					]],success:function(){
						$(".taskState").remove();
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'list'});
			},
			edit:function(data){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'编辑任务',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务'});
			},
			detail:function(data){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
			},
			share:function(data){
				ajax.remoteCall("${ctxPath}/servlet/task?action=share",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			},
			cannelShare:function(data){
				ajax.remoteCall("${ctxPath}/servlet/task?action=cannelShare",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		function reloadTaskList(){
			list.query();
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>