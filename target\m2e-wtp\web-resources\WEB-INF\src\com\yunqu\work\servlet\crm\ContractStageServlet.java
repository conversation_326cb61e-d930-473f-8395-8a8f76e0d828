package com.yunqu.work.servlet.crm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.ContractStageModel;
import com.yunqu.work.service.ContractService;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;


import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
@WebServlet("/servlet/contractStage/*")
public class ContractStageServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public EasyResult actionForAdd(){
        ContractStageModel model = getModel(ContractStageModel.class, "contractStage");
        model.setCreator(getUserName());
        model.addCreateTime();
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        model.set("UPDATE_BY", getUserName());
        model.setPrimaryValues(RandomKit.uuid().toUpperCase());
        String contractId = model.getString("CONTRACT_ID");
        String planDate = model.getString("PLAN_COMP_DATE");
        if(StringUtils.isNotBlank(planDate)){
            model.set("MONTH_ID", DateUtils.getMonthIdFormat(planDate));
            model.set("YEAR_ID",  planDate.substring(0, 4));
        }else {
            model.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
            model.set("YEAR_ID",  EasyCalendar.newInstance().getYear());
        }


        try {
           model.save();
           ContractService.getService().reloadCount("STAGE_COUNT","yq_contract_stage",contractId);
       } catch (SQLException e) {
           this.error(e.getMessage(), e);
           return EasyResult.fail(e.getMessage());
       }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdate(){
        ContractStageModel model = getModel(ContractStageModel.class,"contractStage");
        model.set("UPDATE_TIME",EasyDate.getCurrentDateString());
        model.set("UPDATE_BY",getUserName());
        String planDate = model.getString("PLAN_COMP_DATE");
        String stageId = model.getString("STAGE_ID");
        if(StringUtils.isNotBlank(planDate)) {
            model.set("MONTH_ID", DateUtils.getMonthIdFormat(planDate));
            model.set("YEAR_ID",  planDate.substring(0, 4));
        }
        try {
            model.update();
            ContractService.getService().setCompleteTime(stageId);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForBatchDel(){
        JSONArray stageArray = getJSONArray();
        for(int i=0;i<stageArray.size();i++){
            JSONObject stageObject = stageArray.getJSONObject(i);
            try {
                this.getQuery().executeUpdate("delete from yq_contract_stage where STAGE_ID = ?",stageObject.getString("STAGE_ID") );
                String contractId = stageObject.getString("CONTRACT_ID");
                ContractService.getService().reloadCount("STAGE_COUNT","yq_contract_stage",contractId);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdateObj(){
        EasyRecord record = new EasyRecord("YQ_CONTRACT_STAGE","STAGE_ID");
        try {
            record.setColumns(getJSONObject("contractStage"));
            record.set("UPDATE_BY", getUserName());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getQuery().update(record);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }
}
