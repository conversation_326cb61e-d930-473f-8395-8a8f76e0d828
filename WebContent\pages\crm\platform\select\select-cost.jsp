<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择成本</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectCostForm">
       	   <input name="platformId" value="${param.platformId}" type="hidden"/>
       	   <input name="contractId" value="${param.contractId}" type="hidden"/>
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
					 <span class="input-group-addon">成本名称</span>	
					 <input type="text" name="costName" class="form-control input-sm">
			     </div>
	   			 <div class="input-group input-group-sm ml-5" style="width: 160px">
					 <span class="input-group-addon">成本类型</span>	
					 <select name="costType" class="form-control input-sm">
                        <option value="">全部</option>
                        <option value="1">采购</option>
                        <option value="2">报销</option>
                        <option value="0">其他</option>
                     </select>
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter" onclick="SelectCost.query()">
                    <span class="glyphicon glyphicon-search"></span> 搜索
                 </button>
	   		</div>
           	<div class="ibox">
              	<table id="selectCostList"></table>
            </div>
            <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary" type="button" onclick="SelectCost.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20" type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
			var SelectCost = {
					sid:'${param.sid}',
					query : function(){
						$("#selectCostForm").queryData();
					},
					initTable : function(){
						$("#selectCostForm").initTable({
							mars:'CustOperateDao.costPageList',
							id:'selectCostList',
							page:true,
							limit:20,
							height:'350px',
							rowDoubleEvent:'SelectCost.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
                                field: 'SETTLEMENT_ID',
                                title: '结算状态',
                                width: 80,
                                align: 'center',
                                templet: function(d){
                                    if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
                                        if(d.SETTLEMENT_ID === '${param.settlementId}') {
                                            return '<span style="color:#2a63f4;">本结算</span>';
                                        } else {
                                            return '<span style="color:#ff5722;">已结算</span>';
                                        }
                                    } else {
                                        return '<span style="color:#009688;">未结算</span>';
                                    }
                                }
                            },{
							    field: 'COST_NAME',
								title: '成本名称',
								width:150
							},{
							    field: 'COST_TYPE',
								title: '成本类型',
								width:80
							},{
                                field: 'TOTAL_AMOUNT',
                                title: '总金额(含税)',
                                width:100,
                                align:'right'
                            },{
                                field: 'NO_TAX_AMOUNT', 
                                title: '不含税金额',
                                width:100,
                                align:'right'
                            },{
                                field: 'DATE_ID',
                                title: '成本日期',
                                width:100
                            },{
								field: 'ORDER_NO',
								title: '采购订单号',
								width:110,
							},{
                                field: 'SUPPLIER_NAME',
                                title: '供应商',
                                width:150
                            },{
                                field: 'REMARK',
                                title: '备注'
                            }
							]],
							done: function(res, curr, count){
								$('#selectCostList').next('.layui-table-view').find('tbody tr').each(function(){
									var index = $(this).attr('data-index');
									if(index !== undefined && res.data[index]){
										var rowData = res.data[index];
										// 当记录被其他结算关联时才置灰禁用
										if(rowData.SETTLEMENT_ID && rowData.SETTLEMENT_ID.trim() !== '' && 
                                           rowData.SETTLEMENT_ID !== '${param.settlementId}') {
											$(this).css({'background-color': '#f5f5f5', 'color': '#999'});
											$(this).find('input[type="checkbox"]').prop('disabled', true);
										}
									}
								});
							}
					    });
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectCost.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('selectCostList');
							if(checkStatus.data.length>0){
								// 检查是否有被其他结算关联的记录
								var settledRecords = [];
								var validData = [];
								var data = checkStatus.data;
								for(var index in data){
									if(data[index]['SETTLEMENT_ID'] && data[index]['SETTLEMENT_ID'].trim() !== '' && 
                                       data[index]['SETTLEMENT_ID'] !== '${param.settlementId}') {
										settledRecords.push(data[index]['COST_NAME']);
									} else {
										validData.push(data[index]);
									}
								}
								
								if(settledRecords.length > 0) {
									layer.alert('以下成本记录已被其他结算关联，不允许选择：<br>' + settledRecords.join('<br>'), {icon: 0});
									return;
								}
								
								var names = [];
								var ids = [];
								var totalAmount = 0;
								var totalNoTaxAmount = 0;
								for(var index in validData){
									names.push(validData[index]['COST_NAME']);
									ids.push(validData[index]['COST_ID']);
									totalAmount += Number(validData[index]['TOTAL_AMOUNT']) || 0;
									totalNoTaxAmount += Number(validData[index]['NO_TAX_AMOUNT']) || 0;
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}

								if(typeof CostSelectCallBack === 'function') {
									CostSelectCallBack({
										totalAmount: totalAmount.toFixed(2),
										totalNoTaxAmount: totalNoTaxAmount.toFixed(2),
										costIds: ids.join(','),
										costNames: names.join(',')
									});
								}
								
								popup.layerClose("selectCostForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							// 检查单选记录是否被其他结算关联
							if(selectRow['SETTLEMENT_ID'] && selectRow['SETTLEMENT_ID'].trim() !== '' && 
                               selectRow['SETTLEMENT_ID'] !== '${param.settlementId}') {
								layer.alert('该成本记录已被其他结算关联，不允许选择', {icon: 0});
								return;
							}
							
							el.val(selectRow['COST_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['COST_ID']);
							}

							if(typeof CostSelectCallBack === 'function') {
								CostSelectCallBack({
									totalAmount: selectRow['TOTAL_AMOUNT'],
									totalNoTaxAmount: selectRow['NO_TAX_AMOUNT'],
									costIds: selectRow['COST_ID'],
									costNames: selectRow['COST_NAME']
								});
							}
							
							popup.layerClose("selectCostForm");
						}
					}
			};
			$(function(){
				SelectCost.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
