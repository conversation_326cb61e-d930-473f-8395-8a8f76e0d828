<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>回复记录</title>
	<style>
		.el-timeline{margin:0;font-size:14px;list-style:none}.el-timeline .el-timeline-item:last-child .el-timeline-item__tail{display:none}.el-timeline-item{position:relative;padding-bottom:20px}.el-timeline-item__wrapper{position:relative;padding-left:28px;top:-3px}.el-timeline-item__tail{position:absolute;left:4px;height:100%;border-left:2px solid #e4e7ed}.el-timeline-item__icon{color:#fff;font-size:13px}.el-timeline-item__node{position:absolute;background-color:#e4e7ed;border-radius:50%;display:flex;justify-content:center;align-items:center}.el-timeline-item__node--normal{left:-1px;width:12px;height:12px}.el-timeline-item__node--large{left:-2px;width:14px;height:14px}.el-timeline-item__node--primary{background-color:#409eff}.el-timeline-item__node--success{background-color:#67c23a}.el-timeline-item__node--warning{background-color:#e6a23c}.el-timeline-item__node--danger{background-color:#f56c6c}.el-timeline-item__node--info{background-color:#4f5051}.el-timeline-item__dot{position:absolute;display:flex;justify-content:center;align-items:center}.el-timeline-item__content{color:#303133}.el-timeline-item__timestamp{color:#4f5051;line-height:1;font-size:13px}.el-timeline-item__timestamp.is-top{margin-bottom:8px;padding-top:4px}.el-timeline-item__timestamp.is-bottom{margin-top:8px}.el-card{border-radius:4px;border:1px solid #ebeef5;background-color:#fff;overflow:hidden;color:#303133;transition:.3s}.el-card.is-always-shadow,.el-card.is-hover-shadow:focus,.el-card.is-hover-shadow:hover{box-shadow:0 2px 12px 0 rgba(0,0,0,.1)}.el-card__header{padding:18px 20px;border-bottom:1px solid #ebeef5;box-sizing:border-box}.el-card__body{padding:20px}
   		.el-timeline{margin: 15px 30px;}
   		.opDesc{   
   		 	font-size: 13px;
	    	line-height: 18px;
	    	color: #222;
    	}
    	.opName{color: #982d2d;}
    	.layui-elem-quote{
    		 margin-bottom: 4px;
			 padding: 6px 12px;
			 font-size: 12px;
			 line-height: 18px;
    	}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
    	<form id="replyForm">
    		<input type="hidden" name="reviewId" value="${param.reviewId}">
    		<input type="hidden" name="resultId" value="${param.resultId}">
			<div data-mars="ReviewDao.followList" data-template="timeline-tpl">
			
			</div>
			 <script id="timeline-tpl" type="text/x-jsrender">
							{{for data}}
							   <li class="el-timeline-item">
					            <div class="el-timeline-item__tail"></div>
					            <div class="el-timeline-item__node el-timeline-item__node--normal el-timeline-item__node--primary">
					                
					            </div>
					            <div class="el-timeline-item__wrapper">
					                 <div class="el-timeline-item__content">
					                    	<p class="opName">{{:ADD_USER_NAME}} {{:ADD_TIME}}</p>
											<p class="opDesc">{{:FOLLOW_CONTENT}}</p>
					                </div>
					                <div class="el-timeline-item__timestamp is-bottom">
										{{if TO_USER_NAME&&PREV_CONTENT}}
					                  		 <blockquote class="layui-elem-quote layui-quote-nm">	
 												<span class="text-info">{{:TO_USER_NAME}}</span>{{:PREV_CONTENT}}  
									   		</blockquote>
										{{/if}}
					                </div>
					            </div>
					       		 </li>
							{{/for}}
							{{if data.length==0}} 暂无数据  {{/if}}
 					</script>
 		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		$(function(){
			$("#replyForm").render({success:function(){
				
			}});  
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>