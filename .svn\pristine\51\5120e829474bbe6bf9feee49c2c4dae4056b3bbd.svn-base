package com.yunqu.work.servlet.flow;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/flow/node")
public class NodeServlet extends AppBaseServlet {

	public EasyResult actionForUpdateNodeConf(){
		EasyRecord model = new EasyRecord("yq_erp_order_check_node","node_id");
		try {
			model.setColumns(getJSONObject("conf"));
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForAddNodeConf(){
		EasyRecord model = new EasyRecord("yq_erp_order_check_node","node_id");
		try {
			model.setColumns(getJSONObject("conf"));
			model.set("create_time", EasyDate.getCurrentDateString());
			model.remove("NODE_ID");
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
}
