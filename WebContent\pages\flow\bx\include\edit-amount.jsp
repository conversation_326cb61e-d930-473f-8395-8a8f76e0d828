<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<style>
		.small{
			color: #999;
			font-size: 12px;
			margin-top: 5px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editInvoiceMoneyForm" autocomplete="off">
	     	   <input type="hidden" id="bxItemId" value="${param.itemId}" name="bxItemId"/>
	     	   <input type="hidden" id="invoiceIdArray" value="${param.invoiceIdArray}" name="invoiceIdArray"/>
				<table class="layui-table">
					<thead>
						<tr>
							<th>
								发票号码
							</th>
							<th>
								开票金额
							</th>
							<th>
								调整后金额
							</th>
						</tr>
					</thead>
			        <tbody data-mars="BxDao.bxItemInvoice" data-template="edit-invoice-template" data-container="editInvoiceList" id="editInvoiceList">
			           
			        </tbody>
 				</table>
				 <div class="layer-foot text-c">
						<button type="button" class="btn btn-primary btn-sm" onclick="editInvoiceMoney.ajaxSubmitForm()"> 确 定  </button>
				</div>			
	  		</form>

			 <script id="edit-invoice-template" type="text/x-jsrender">
				{{for list}}
					<tr>
						<td>
							{{:INVOICE_NO}}
						</td>
						<td>
							{{:TOTAL_AMOUNT}}
						</td>
						<td>
							<input type="number" class="form-control input-sm input-money" data-no="{{:INVOICE_NO}}" data-money="{{:TOTAL_AMOUNT}}" data-id="{{:INVOICE_ID}}" name="invoiceMoney_{{:INVOICE_ID}}" value="{{:TOTAL_AMOUNT}}"/>
						</td>
					</tr>
				{{/for}}
			</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
       
	    var editInvoiceMoney = {};
	    
		editInvoiceMoney.ajaxSubmitForm = function() {
			var data = form.getJSONObject("#editInvoiceMoneyForm");
			var flag = true;
			var sumMoney = 0;
			var json = {};
			$('.input-money').each(function(){
				var val = $(this).val();
				var id = $(this).attr('data-id');
				var invoiceNo = $(this).attr('data-no');
				var invoiceMoney = Number($(this).attr('data-money'));
				if(val==''){
					flag = false;
				}
				var _val = Number(val);
				if(_val>invoiceMoney){
					layer.msg('发票号码'+invoiceNo+'中的填写金额['+val+']不能大于'+invoiceMoney,{icon:7,offset:'20px',time:1200});
					flag = false;
				}
				sumMoney = sumMoney + _val;
				json[id] = _val;
			});
			if(!flag){
				layer.msg('金额每项必填',{icon:7,offset:'20px',time:1200});
				return;
			}
			var bxItemId = $('#bxItemId').val();
			$("[name=amount_"+bxItemId+"]").val(sumMoney);
			$("[name=invoiceJson_"+bxItemId+"]").val(JSON.stringify(json));
			calcBxMoney();
			layer.closeAll();
		}

		$(function(){
			initFormData();
		});

		function initFormData(){
			$("#editInvoiceMoneyForm").render();
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
