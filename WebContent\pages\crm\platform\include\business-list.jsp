<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<div class="pull-left mb-5">
    <button type="button" class="btn btn-sm btn-info" onclick="businessList.add()">+ 新增商机</button>
</div>

<table id="businessTable" class="layui-table"></table>

<script type="text/javascript">
    var businessList = {
        init: function(){
            $("#PlatformDetailForm").initTable({
                mars:'CustDao.businessList',
                id:'businessTable',
                page:false,
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align:'left'
                    },{
                        field: 'BUSINESS_NAME',
                        title: '商机名称',
                        align:'left',
                        minWidth:150,
                        style:'color:#1E9FFF;cursor:pointer',
                        event:'businessList.detail'
                    },{
                        field: 'BUSINESS_TYPE',
                        title: '商机类型',
                        align: "center",
                        width: 80,
                        templet: function(row){
                            return row['BUSINESS_TYPE']=='2'?'运营商机':'普通商机';
                        }
                    },{
                        field: 'EXPECTED_WIN_DATE',
                        title: '预计赢单时间',
                        align: "center",
                        width: 110
                    },{
                        field: 'AMOUNT',
                        title: '预计金额(万)',
                        align: "center",
                        width: 100
                    },{
                        field: 'COST_BUDGET',
                        title: '预计成本(万)',
                        align: "center",
                        width: 100
                    },{
                        field:'POSSIBILITY',
                        title:'可行性',
                        width: 80,
                        align: "center",
                        templet:'<div>{{d.POSSIBILITY}}%</div>'
                    },{
                        field: 'CUSTOMER_NAME',
                        title: '客户名称',
                        minWidth:150,
                        templet:'<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUSTOMER_NAME}}</a></div>'
                    },{
                        field: 'SALES_BY_NAME',
                        title: '销售经理',
                        width: 80,
                        align: 'center',
                    },{
                        field:'LAST_FOLLOW_TIME',
                        title:'最后跟进时间',
                        width:140,
                    },{
                        field: '',
                        title: '操作',
                        width: 80,
                        align: 'center',
                        templet: function(d){
                            return '<a href="javascript:;" onclick="businessList.edit(\''+d.BUSINESS_ID+'\')">编辑</a>';
                        }
                    }
                ]],
                done:function(res){
                    if(res.total != undefined){
                        $("[name='extend.BUSINESS_COUNT']").text(res.total);
                    }
                }
            });
        },
        add: function(){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'新增商机',
                offset:'20px', 
                area:['700px','500px'],
                url:"${ctxPath}/pages/crm/shangji/sj-edit.jsp",
                data:{platformId:platformId,platformName:platformInfo.PLATFORM_NAME,businessType:'2'}
            });
        },
        edit: function(businessId){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'编辑商机',
                offset:'20px',
                area:['700px','500px'],
                url:"${ctxPath}/pages/crm/shangji/sj-edit.jsp",
                data:{businessId:businessId,businessType:'2'}
            });
        },
        detail: function(data){
            var businessId = data['BUSINESS_ID'];
            var custId = data['CUST_ID'];
            var custName = data['CUST_NAME'];
            popup.openTab({id:'bussinessContactQuery',title:'跟进记录',url:'${ctxPath}/sj/'+businessId,data:{businessId:businessId,custId:custId,custName:custName}});

        }
    };

    function reloadBusinessList(){
        $("#PlatformDetailForm").queryData({id:'businessTable',page:false});
    }

</script>
