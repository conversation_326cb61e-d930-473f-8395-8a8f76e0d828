<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>成本管理</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>

<EasyTag:override name="content">
    <div class="container-fluid">
        <form id="addCostForm" class="form-horizontal" data-mars="CustOperateDao.costRecord" data-mars-prefix="cost.">
            <table class="table table-edit table-vzebra">
                <tbody>
                <tr class="hidden">
                    <td colspan="2">
                        <input type="hidden" name="cost.COST_ID" value="${param.costId}"/>
                        <input type="hidden" name="costId" value="${param.costId}"/>
                        <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM'})"
                               name="cost.MONTH_ID" class="form-control input-sm Wdate">
                    </td>
                </tr>
                <tr>
                    <td width="100px" class="required">成本名称</td>
                    <td>
                        <input type="text" name="cost.COST_NAME" class="form-control input-sm" data-rules="required"/>
                    </td>
                    <td class="required">成本归属</td>
                    <td>
                        <select name="cost.COST_OWNER" value="${param.costOwner}" class="form-control input-sm" data-rules="required">
                            <option value="0">平台成本</option>
                            <option value="1">客户成本</option>
                            <option value="2">其他</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="required">客户<i class="layui-icon layui-icon-tips" lay-tips="请填写实际产生成本的客户（可能与合同签订客户不同，平台成本填写平台合同的签订客户即可）。"></i></td>
                    <td colspan="3">
                        <input type="hidden" name="cost.CUST_ID" value="${param.custId}"/>
                        <input type="text" name="cost.CUST_NAME" value="${param.custName}" class="form-control input-sm" readonly onclick="singleCust(this)" data-rules="required"/>
                    </td>
                </tr>
                <tr class="order-select-row" style="display: none;">
                    <td>订购信息</td>
                    <td colspan="3">
                        <input type="hidden" name="cost.PLATFORM_ORDER_ID" />
                        <input type="text" name="cost.PLATFORM_ORDER_NAME" class="form-control input-sm" readonly onclick="selectPlatformOrder(this)" placeholder="请点击选择订购信息"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">合同名称</td>
                    <td colspan="3">
                        <input type="hidden" name="cost.CONTRACT_ID" value="${param.contractId}"/>
                        <input type="text" name="cost.CONTRACT_NAME" value="${param.contractName}" class="form-control input-sm" readonly onclick="singleContract(this)" placeholder="请点击选择合同" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">业务平台</td>
                    <td colspan="3">
                        <input type="hidden" name="cost.PLATFORM_ID" value="${param.platformId}"/>
                        <input type="text" name="cost.PLATFORM_NAME" value="${param.platformName}" class="form-control input-sm" readonly onclick="singleBusinessPlatForm(this)" placeholder="请点击选择平台" data-rules="required"/>
                    </td>
                </tr>
                <tr>
                    <td>成本类型</td>
                    <td>
                        <select name="cost.COST_TYPE" class="form-control input-sm">
                            <option value="硬件">硬件</option>
                            <option value="软件">软件</option>
                            <option value="技术服务">技术服务</option>
                            <option value="其他">其他</option>
                        </select>
                    </td>
                    <td width="100px" class="required">成本日期</td>
                    <td>
                        <input type="text" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                               name="cost.DATE_ID" class="form-control input-sm Wdate">
                    </td>
                </tr>
                <tr class="purchase-mode" style="display: none;">
                    <td class="required">单价</td>
                    <td>
                        <input type="number" name="cost.UNIT_PRICE" class="form-control input-sm" data-rules="required" value="0"/>
                    </td>
                    <td class="required">采购数量</td>
                    <td>
                        <input type="number" name="cost.NUMBER" class="form-control input-sm" data-rules="required" value="1"/>
                    </td>
                </tr>
                <tr class="purchase-mode" style="display: none;">
                    <td class="required">总金额(含税)</td>
                    <td>
                        <input type="number" name="cost.TOTAL_AMOUNT" class="form-control input-sm" data-rules="required" value="0" readonly/>
                    </td>
                    <td class="required">税率(%)</td>
                    <td>
                        <input type="number" name="cost.TAX_RATE" class="form-control input-sm" data-rules="required" value="0"/>
                    </td>
                </tr>
                <tr class="purchase-mode" style="display: none;">
                    <td class="required">不含税金额</td>
                    <td>
                        <input type="number" name="cost.NO_TAX_AMOUNT" class="form-control input-sm" data-rules="required" value="0" readonly/>
                    </td>
                    <td>成本来源</td>
                    <td>
                        <label class="radio radio-inline radio-success">
                            <input  checked="checked" type="radio" value="1" name="cost.RELATE_TYPE" onclick="toggleRelateType(1)"><span>采购订单</span>
                        </label>
                        <label class="radio radio-inline radio-success">
                            <input type="radio" value="2" name="cost.RELATE_TYPE" onclick="toggleRelateType(2)"><span>个人报销</span>
                        </label>
                        <label class="radio radio-inline radio-success">
                            <input type="radio" value="0" name="cost.RELATE_TYPE" onclick="toggleRelateType(0)"><span>其他</span>
                        </label>
                    </td>
                </tr>
                <tr class="purchase-mode" style="display: none;">
                    <td>采购订单</td>
                    <td colspan="3">
                        <input type="hidden" id="orderId" name="cost.ORDER_ID"/>
                        <input type="text" id="orderNo" name="cost.ORDER_NO" readonly="readonly" onclick="selectOrder(this)" class="form-control input-sm" placeholder="点击选择采购订单"/>
                    </td>
                </tr>
                <tr class="supplier-row">
                    <td>供应商</td>
                    <td colspan="3">
                        <input type="hidden" name="cost.SUPPLIER_ID"/>
                        <input type="text" name="cost.SUPPLIER_NAME" class="form-control input-sm" readonly onclick="selectSupplier(this)" placeholder="请点击选择供应商"/>
                    </td>
                </tr>
                <tr class="purchase-mode" style="display: none;">
                    <td>采购下单时间</td>
                    <td>
                        <input type="text" name="cost.ORDER_TIME" class="form-control input-sm Wdate" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                    </td>
                    <td>采购付款时间</td>
                    <td>
                        <input type="text" name="cost.PAY_TIME" class="form-control input-sm Wdate" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"/>
                    </td>
                </tr>
                <tr class="expense-mode" style="display: none;">
                    <td>报销流程</td>
                    <td colspan="3">
                        <input type="hidden" id="bxApplyId" name="cost.BX_APPLY_ID"/>
                        <input type="text" id="bxDesc" name="cost.APPLY_TITLE" readonly="readonly" onclick="selectBxFlow(this)" class="form-control input-sm" placeholder="点击选择报销单"/>
                    </td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td colspan="3">
                        <textarea name="cost.REMARK" class="form-control input-sm" rows="4"></textarea>
                    </td>
                </tr>
                </tbody>
            </table>
            <p class="layer-foot text-c">
                <button type="button" class="btn btn-primary btn-sm" style="width: 80px"
                        onclick="CostEdit.ajaxSubmitForm()">保 存
                </button>
                <button type="button" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll()">关 闭
                </button>
            </p>
        </form>
    </div>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("CostEdit");
        
        CostEdit.costId = '${param.costId}';
        CostEdit.isNew = '${param.isNew}';
        CostEdit.custId = '${param.custId}';
        CostEdit.custName = '${param.custName}';
        CostEdit.relateType = '${param.relateType}';


        $(function(){
            $("#addCostForm").render({
                success:function(result){
                    $("[name='cost.NUMBER'],[name='cost.UNIT_PRICE'],[name='cost.TAX_RATE']").on('change', function(){
                        CostEdit.updateAllAmounts();
                    });


                    $("[name='cost.COST_OWNER']").on('change', function() {
                        var costOwner = $(this).val();
                        if(costOwner == "1") {
                            $(".order-select-row").show();
                        } else {
                            $(".order-select-row").hide();
                            $("[name='cost.PLATFORM_ORDER_ID']").val("");
                            $("[name='cost.PLATFORM_ORDER_NAME']").val("");
                        }
                    });

                    toggleRelateType(CostEdit.relateType);

                    $("[name='cost.RELATE_TYPE']").on('change', function() {
                        toggleRelateType($(this).val());
                    });

                    var initCostOwner = $("[name='cost.COST_OWNER']").val();
                    if(initCostOwner == "1") {
                        $(".order-select-row").show();
                    }
                }
            });
        });

        // 自动计算总金额和不含税金额
        CostEdit.updateAllAmounts = function() {
            var number = parseFloat($("[name='cost.NUMBER']").val()) || 0;
            var unitPrice = parseFloat($("[name='cost.UNIT_PRICE']").val()) || 0;
            var taxRate = parseFloat($("[name='cost.TAX_RATE']").val()) || 0;
            
            var totalAmount = number * unitPrice;
            $("[name='cost.TOTAL_AMOUNT']").val(totalAmount.toFixed(2));
     
            var noTaxAmount = totalAmount / (1 + taxRate/100);
            $("[name='cost.NO_TAX_AMOUNT']").val(noTaxAmount.toFixed(2));
        }

        CostEdit.ajaxSubmitForm = function(){
            if(form.validate("#addCostForm")){
                var data = form.getJSONObject("#addCostForm");
                delete data['cost.APPLY_TITLE'];
                delete data['cost.PLATFORM_ORDER_NAME'];

                var costOwner = $("[name='cost.COST_OWNER']").val();
                 if(costOwner === "1") {
                    if(!data['cost.PLATFORM_ORDER_ID']) {
                        layer.msg("客户成本必须选择对应的订购信息", {icon: 2, time: 2000});
                        return;
                    }
                }else{
                     data['cost.PLATFORM_ORDER_ID'] = "";
                 }

                if(this.costId || this.isNew === "0"){
                    this.updateData(data);
                }else{
                    this.insertData(data);
                }
            }
        }

        CostEdit.insertData = function(data){
            ajax.remoteCall("${ctxPath}/servlet/custOperate?action=addCost",data,function(result){
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadCostList();
                    });
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        CostEdit.updateData = function(data){
            ajax.remoteCall("${ctxPath}/servlet/custOperate?action=updateCost",data,function(result){
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadCostList();
                    });
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        function selctCallBack(id,row){
            $("[name='cost.CONTRACT_NAME']").val(row['CONTRACT_NAME']);
            $("[name='cost.CONTRACT_ID']").val(row['CONTRACT_ID']);
            $("[name='cost.PLATFORM_NAME']").val(row['PLATFORM_NAME']);
            $("[name='cost.PLATFORM_ID']").val(row['PLATFORM_ID']);
        }

        function selectSupplier(el){
            var type='radio';
            var id = new Date().getTime();
            $(el).attr('data-sid',id);
            popup.layerShow({id:'selectSupplier',full:fullShow(),scrollbar:false,area:['650px','500px'],offset:'20px',title:'选择采购供应商',url:'/yq-work/pages/flow/select/select-supplier.jsp',data:{sid:id,type:type}});
        }

        function selectBxFlow(el){
           var id = new Date().getTime();
            $(el).attr('data-sid',id);
            var data = $.extend($(el).data(),{sid:id,type:'radio'});
            popup.layerShow({id:'selectBxFlow',full:fullShow(),scrollbar:false,area:['700px','100%'],offset:'r',title:'选择报销流程',url:'/yq-work/pages/flow/select/select-bx-flow.jsp',data:data});
        }

        function selctFlowCallBack(row){
            $("[name='cost.BX_APPLY_ID']").val(row['APPLY_ID']);
        }

        function selectOrder(el){
            var id = new Date().getTime();
            $(el).attr('data-sid',id);
            var contractName = $("[name='cost.CONTRACT_NAME']").val();
            popup.layerShow({id:'selectOrder',full:fullShow(),scrollbar:false,area:['650px','500px'],offset:'20px',title:'选择采购订单',url:'/yq-work/pages/flow/select/select-cost-order.jsp',data:{sid:id,type:'radio',contractName:contractName}});
         }

        function toggleRelateType(type) {
            // 所有类型都显示采购相关的基本字段（采购数量、单价、总金额等）
            $(".purchase-mode").show();
            
            // 针对采购订单特有行进行处理
            var orderRow = $(".purchase-mode:has(#orderId)");
            var orderTimeRow = $(".purchase-mode:has([name='cost.ORDER_TIME'])");
            var supplierRow = $(".supplier-row");
            
            if (type == "1") { // 采购订单
                orderRow.show();
                orderTimeRow.show();
                supplierRow.show();
                $(".expense-mode").hide();
            } else if (type == "2") { // 个人报销
                orderRow.hide();
                orderTimeRow.hide();
                supplierRow.hide();
                $(".expense-mode").show();
            } else { // 无关联
                orderRow.hide();
                orderTimeRow.hide();
                supplierRow.show();
                $(".expense-mode").hide();
            }
        }

        function selectPlatformOrder(el) {
            var id = new Date().getTime();
            $(el).attr('data-sid', id);
            var custId = $("[name='cost.CUST_ID']").val();
            var custName = $("[name='cost.CUST_NAME']").val();

            if(!custId || !custName) {
                layer.msg("请先选择客户", {icon: 0, time: 2000});
                return;
            }

            popup.layerShow({
                id: 'selectPlatformOrder',
                full: fullShow(),
                scrollbar: false,
                area: ['800px','600px'],
                offset: '20px',
                title: '选择订购信息',
                url: '/yq-work/pages/crm/platform/select/select-platform-order.jsp',
                data: {
                    sid: id,
                    type: 'radio',
                    custName: custName,
                    platformId:$("[name='cost.PLATFORM_ID']").val()
                }
            });
        }

        function PlatformOrderSelectCallBack(data) {
            if(data) {
                $("[name='cost.PLATFORM_ORDER_ID']").val(data.orderIds);
                $("[name='cost.PLATFORM_ORDER_NAME']").val(data.orderNames);
                // 回填合同信息
                if(data.contractId && data.contractName) {
                    $("[name='cost.CONTRACT_ID']").val(data.contractId);
                    $("[name='cost.CONTRACT_NAME']").val(data.contractName);
                }
            }
        }

        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %> 