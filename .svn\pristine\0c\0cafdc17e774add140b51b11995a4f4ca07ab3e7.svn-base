<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div class="container-fluid">
<form id="storageEditForm">
   <table class="table table-edit table-vzebra" data-mars="GoodsDao.stockInfo" data-mars-prefix="stock.">
       <tbody>
       		
       </tbody>
       <tbody>
           <tr class="hidden">
           		<td class="required" width="100px">操作类型</td>
                <td>
                	<input type="hidden" name="goodsIds" value="${param.goodsIds}"/>
                	<input type="hidden" name="stock.ORDER_ID" value="${param.orderId}"/>
                	<input type="hidden" name="stock.STOCK_ID" value="${param.stockId}"/>
                    <label class="radio radio-inline radio-success">
                      	<input <c:if test="${param.type=='put'}">checked="checked"</c:if> type="radio" value="10" name="stock.TYPE"><span>入库</span>
                    </label>
                    <label class="radio radio-inline radio-success">
                      	<input <c:if test="${param.type=='out'}">checked="checked"</c:if> type="radio" value="20" name="stock.TYPE"><span>出库</span>
                    </label>
                    <label class="radio radio-inline radio-success">
                      	<input <c:if test="${param.type=='return'}">checked="checked"</c:if> type="radio" value="30" name="stock.TYPE"><span>退货</span>
                    </label>
                 </td>
           </tr>
            <tr>
            	<td  width="100px" class="required">${param.title}日期</td>
            	<td>
            		<input type="text" id="dateId" data-mars="CommonDao.date03" autocomplete="off" name="stock.DATE_ID" data-rules="required" class="form-control"/>
            	</td>
            </tr>
            <c:if test="${param.type=='out'}">
	            <tr>
	            	<td class="required">出库类型</td>
	            	<td>
	            		<select name="stock.OUT_TYPE" data-rules="required" class="form-control">
	            			<option value="">请选择</option>
	            			<option value="1">正常出库</option>
	            			<option value="2">零星物料出库</option>
	            			<option value="3">项目协作费出库</option>
	            			<option value="4">固定资产领用出库</option>
	            		</select>
	            	</td>
	            </tr>
	             <tr>
	            	<td>领用部门</td>
	            	<td>
	            		<input type="hidden" name="stock.OUT_DEPT_NAME"/>
	            		<select name="stock.OUT_DEPT_ID" onchange="$(this).prev().val(this.options[this.selectedIndex].text);" class="form-control" data-mars="CommonDao.deptDict">
	            			<option value="">请选择</option>
	            		</select>
	            	</td>
	            </tr>
            </c:if>
            <c:if test="${param.type=='return'}">
	            <tr>
	            	<td class="required">货物退至</td>
	            	<td>
	            		<select name="stock.RETURN_TYPE" data-rules="required" class="form-control">
	            			<option value="">请选择</option>
	            			<option value="1">供应商</option>
	            			<option value="2">仓库</option>
	            		</select>
	            	</td>
	            </tr>
            </c:if>
            <tr>
            	<td>备注</td>
            	<td>
            		<textarea name="stock.REMARK" style="height: 80px" class="form-control"></textarea>
            	</td>
            </tr>
       </tbody>
	</table>
    <p class="layer-foot text-c">
   	   <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="StorageEdit.ajaxSubmitForm()"> 保 存 </button>
       <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
	</p>
</form>
</div>
<script>
	$(function(){
		layui.use('laydate',function(){
			var laydate = layui.laydate;
			laydate.render({elem: '#dateId',format:'yyyyMMdd'});
		});
		
		$("#storageEditForm").render({success:function(result){

		}});
	});
	
	var StorageEdit ={
		ajaxSubmitForm:function(){
			if(form.validate("#storageEdit")){
				var outType = $("[name='stock.OUT_TYPE']").val();
				if(outType=='2'||outType=='3'||outType=='4'){
					var outDeptId = $("[name='stock.OUT_DEPT_ID']").val();
					if(outDeptId==''){
						layer.msg('请选择领用部门',{icon:7});
						return;
					}
				}
				StorageEdit.insertData(); 
			}
		},
		insertData:function(){
			var data = $.extend(form.getJSONObject("#storageEditForm"),doNums);
			ajax.remoteCall("${ctxPath}/servlet/goods?action=batchAddStorage",data,function(result) { 
				if(result.state == 1){
					var type = '${param.type}';
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadDataList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	
	}

</script>
