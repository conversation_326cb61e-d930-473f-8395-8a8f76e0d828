<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>我的合同看板</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }

        .layui-card-header, .layui-card-header select {
            color: rgb(62, 104, 254) !important;
        }

        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-stat .layui-col-md3 {
            text-align: center;
            padding: 10px;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .contract-stat .layui-card {
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm">
        <input type="hidden" name="source" value="user">
        <input type="hidden" name="userType" value="sale">
        <input type="hidden" name="stageTime" value="year">
        <input type="hidden" name="stageType" value="all">
        <input type="hidden" name="receiptTime" value="year">
        <input type="hidden" name="dateField2" value="year">
        <div class="layui-row layui-col-space10 contract-stat" data-mars="ContractStatisticDao.contractConsoleStat">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="COUNT_CUST">0</div>
                        <div class="top-2">今年新增客户<i class="layui-icon layui-icon-tips" lay-tips="以客户信息的创建时间年份为统计标准。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="COUNT_CONTRACT">0</div>
                        <div class="top-2">今年新增销售合同数<i class="layui-icon layui-icon-tips" lay-tips="以合同年限的年份为准。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_CONTRACT">0</div>
                        <div class="top-2">今年新增销售合同金额<i class="layui-icon layui-icon-tips" lay-tips="以合同年限的年份为准。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_STAGE">0</div>
                        <div class="top-2">今年收款目标金额<i class="layui-icon layui-icon-tips" lay-tips="计划完成时间为今年的合同阶段总额，合同阶段的计划完成时间与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10 contract-stat" data-mars="ContractStatisticDao.contractConsoleStat2">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_RECEIPT">0</div>
                        <div class="top-2">今年已收款金额<i class="layui-icon layui-icon-tips" lay-tips="收款时间为今年的收款总额，与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="UN_RECEIVE">0</div>
                        <div class="top-2">今年待收款金额<i class="layui-icon layui-icon-tips" lay-tips="今年的合同阶段总额 减去 今年的已收款总额。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_UNCONFIRM">0</div>
                        <div class="top-2">今年待收入确认金额(税后)<i class="layui-icon layui-icon-tips" lay-tips="计划完成时间为今年的收入确认阶段总额减去收入确认A总额，与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_CONFIRM">0</div>
                        <div class="top-2">今年已收入确认金额(税后)<i class="layui-icon layui-icon-tips" lay-tips="收入确认时间为今年的收入确认A总额，与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <select class="form-control input-sm selectContractType" style="display: inline-block;width: 150px;float: left;margin-top: 5px;color:#000000;box-shadow:none;border: none;">
                            <option value="sale" selected> 我的新增销售合同</option>
                            <option value="create"> 我的新增创建合同</option>
                        </select>
                        <i class="layui-icon layui-icon-tips" lay-tips="合同年限为今年的合同列表，销售合同为销售人的合同，创建合同为创建人的合同。"></i>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="myContractTable"></table>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <select class="form-control input-sm selectStageType" style="display: inline-block;width: 200px;float: left;margin-top: 5px;color:#000000;box-shadow:none;border: none;">
                            <option value="1" selected> 今年全部合同阶段</option>
                            <option value="2"> 今年待收款合同阶段</option>
                            <option value="3"> 本月全部合同阶段</option>
                            <option value="4"> 本月待收款合同阶段</option>
                        </select>
                        <i class="layui-icon layui-icon-tips" lay-tips="以合同阶段信息的计划完成时间为统计标准，显示我销售的合同下的所有合同阶段。"></i>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="myContractStageTable"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <select class="form-control input-sm selectReceiptType" style="display: inline-block;width: 200px;float: left;margin-top: 5px;color:#000000;box-shadow:none;border: none;">
                            <option value="year" selected> 今年全部合同收款</option>
                            <option value="month"> 本月全部合同收款</option>
                        </select>
                        <i class="layui-icon layui-icon-tips" lay-tips="以收款信息中的收款负责人和收款日期做为统计标准，显示我的收款信息。"></i>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="myReceiptTable"></table>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <select class="form-control input-sm selectIncomeStageType" style="display: inline-block;width: 200px;float: left;margin-top: 5px;color:#000000;box-shadow:none;border: none;">
                            <option value="1" selected> 今年全部收入确认阶段</option>
                            <option value="2"> 今年待完成收入确认阶段</option>
                            <option value="3"> 本月全部收入确认阶段</option>
                            <option value="4"> 本月待完成收入确认阶段</option>
                        </select>
                        <button type="button" class="btn btn-sm btn-default" onclick="myIncomeConsole()">我的收入确认阶段总览</button>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="myIncomeStageTable"></table>
                    </div>
                </div>
            </div>
        </div>

        </div>
    </form>
    <script type="text/x-jsrender" id="bar3">
 		 <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="contractList.contractDetail" href="javascript:;">合同详情</a>



    </script>
</EasyTag:override>
<EasyTag:override name="script">
    <script>
        $(function () {
            $("#searchForm").render({
                success: function () {
                    contractList.initContractList();
                    contractList.initContractStageList();
                    contractList.initReceiptList();
                    contractList.initIncomeStageList();
                }
            });
        });

        var contractList = {
            initContractList: function () {
                $("#searchForm").initTable({
                        mars: 'ProjectContractDao.myContractList',
                        cellMinWidth: 50,
                        limit: 20,
                        data: {"userType": "sale"},
                        height: '320px',
                        autoSort: true,
                        totalRow: true,
                        id: 'myContractTable',
                        cols: [[
                            {
                                field: '',
                                title: '操作',
                                width: 80,
                                align: 'center',
                                templet: function (row) {
                                    return renderTpl('bar3', row);
                                },
                                totalRowText: "合计"
                            }, {
                                field: 'CONTRACT_NO',
                                title: '合同号',
                                width: 110
                            }, {
                                field: 'CONTRACT_NAME',
                                title: '合同名称',
                                minWidth: 120,
                                width: 260
                            }, {
                                field: 'CUSTOMER_NAME',
                                title: '客户名称',
                                sort: true,
                                minWidth: 180
                            }, {
                                field: 'AMOUNT',
                                title: '合同金额',
                                sort: true,
                                width: 100,
                                totalRow: true
                            }, {
                                field: 'SIGN_DATE',
                                title: '签订时间',
                                width: 110,
                                sort: true,
                                align: 'center'
                            },
                        ]]
                    },
                )
            },

            initContractStageList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStageDao.myContractStageList',
                        cellMinWidth: 50,
                        limit: 20,
                        data: {homePage: 0, "stageTime": "year", "stageType": "all"},
                        height: '320px',
                        autoSort: true,
                        totalRow: true,
                        id: 'myContractStageTable',
                        cols: [[
                            {
                                field: '',
                                title: '操作',
                                width: 80,
                                align: 'center',
                                templet: function (row) {
                                    return renderTpl('bar3', row);
                                },
                                totalRowText: "合计"
                            }, {
                                field: 'CONTRACT_NAME',
                                title: '合同名称',
                                minWidth: 120,
                                width: 220
                            }, {
                                field: 'STAGE_NAME',
                                title: '阶段名称',
                                align: 'center',
                                minWidth: 80,
                                style:'color:#1E9FFF;cursor:pointer',
                                event:'contractList.contractStageDetail',
                            }, {
                                title: '收款状态',
                                width: 80,
                                align: 'center',
                                templet: function (d) {
                                    return getStageCompleteStatus(d.PLAN_COMP_DATE, d.ACT_COMP_DATE, d.RCV_DATE);
                                }
                            }, {
                                field: 'RCV_DATE',
                                title: '应收日期',
                                width: 100,
                                sort: true,
                            }, {
                                field: 'PLAN_COMP_DATE',
                                title: '计划完成日期',
                                width: 100,
                                sort: true,
                            }, {
                                field: 'ACT_COMP_DATE',
                                title: '实际完成日期',
                                width: 100,
                                sort: true,
                            }, {
                                field: 'PLAN_RCV_AMT',
                                title: '计划收款金额',
                                width: 100,
                                totalRow: true,
                                sort: true,
                            }
                        ]]
                    },
                )
            },

            initReceiptList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractReceiptDao.myReceiptList',
                        cellMinWidth: 50,
                        limit: 20,
                        data: {homePage: 0, "receiptTime": "year"},
                        height: '320px',
                        autoSort: true,
                        totalRow: true,
                        id: 'myReceiptTable',
                        cols: [[
                            {
                                field: '',
                                title: '操作',
                                width: 80,
                                align: 'center',
                                templet: function (row) {
                                    return renderTpl('bar3', row);
                                },
                                totalRowText: "合计"
                            },{
                                field:'RECEIPT_DATE',
                                title:'收款日期',
                                align:'left',
                                width:100,
                                sort:true
                            },{
                                field:'AMOUNT',
                                title:'收款金额',
                                totalRow:true,
                                width: 110
                            },{
                                field: 'CONTRACT_NAME',
                                title: '合同名称',
                                minWidth: 120,
                                width: 200
                            },{
                                field:'STAGE_NAME',
                                title:'合同阶段',
                                minWidth: 80,
                                width: 100,
                            },
                        ]]
                    },
                )
            },

            initIncomeStageList: function () {
                $("#searchForm").initTable({
                    mars: 'IncomeStageDao.allIncomeStageList',
                    id: 'myIncomeStageTable',
                    page: true,
                    cellMinWidth: 60,
                    rowDoubleEvent(row) {
                        contractList.incomeStageDetail(row);
                    },
                    data: {'stageType': "all"},
                    autoSort: true,
                    totalRow: true,
                    cols: [[
                        {
                            type: 'numbers',
                            align: 'left',
                            totalRowText: "合计"
                        }, {
                            field: 'CONTRACT_NAME',
                            title: '合同名称',
                            align: 'left',
                            width: 180,
                            style: 'color:#1E9FFF;cursor:pointer',
                            event: 'contractList.contractDetail',
                        }, {
                            field: 'STAGE_NAME',
                            title: '收入确认阶段',
                            align: 'left',
                            width: 100,
                            style: 'color:#1E9FFF;cursor:pointer',
                            event: 'contractList.incomeStageDetail',
                        }, {
                            field: 'INCOME_CONF_FLAG',
                            title: '状态',
                            minWidth: 100,
                            width: 100,
                            templet: function (d) {
                                return getIncomeStageStatus(d.INCOME_CONF_FLAG, d.PLAN_COMP_DATE, d.ACT_COMP_DATE);
                            }
                        }, {
                            field: 'PLAN_COMP_DATE',
                            title: '计划完成时间',
                            align: 'left',
                            sort: true,
                            width: 110
                        }, {
                            field: 'ACT_COMP_DATE',
                            title: '实际完成日期A',
                            align: 'left',
                            sort: true,
                            width: 120
                        }, {
                            field: 'RATIO',
                            title: '占合同总额(%)',
                            width: 100,
                        }, {
                            field: 'PLAN_AMOUNT',
                            title: '计划收款金额(含税)',
                            minWidth: 130,
                            totalRow: true,
                        }, {
                            field: 'AMOUNT_NO_TAX',
                            title: '计划收款金额(税后)',
                            minWidth: 130,
                            totalRow: true,
                        },
                        {
                            field: 'UNCONFIRM_NO_TAX_A',
                            title: '未确认金额A(税后)',
                            width: 130,
                            totalRow: true,
                        }, {
                            field: 'REMARK',
                            title: '备注',
                            width: 120
                        }
                    ]],
                    done: function (res) {

                    }
                });

            },

            contractDetail: function (data) {
                if (isArray(data)) {
                    data = data[0];
                }
                popup.openTab({id: 'contractDetail', title: '合同详情', url: '${ctxPath}/project/contract', data: {contractId: data.CONTRACT_ID, custId: data['CUST_ID'], isDiv: 0}});
            },
            incomeStageDetail: function (data) {
                var incomeStageId = data.INCOME_STAGE_ID;
                var contractId = data.CONTRACT_ID;
                popup.layerShow({
                    type: 1,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    title: '收入确认阶段详情',
                    offset: 'r',
                    area: ['800px', '100%'],
                    url: '${ctxPath}/pages/crm/contract/include/income-stage-detail.jsp',
                    data: {contractId: contractId, incomeStageId: incomeStageId}
                });
            },
            contractStageDetail : function (data){
                var contractId = data.CONTRACT_ID;
                var  stageId = data.STAGE_ID;
                popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'合同阶段详情',offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/crm/contract/include/stage-detail.jsp',data:{contractId:contractId,stageId:stageId}});
            }
        }


        $('.selectContractType').on('change', function () {
            var value = $(this).val();
            $('input[name="userType"]').val(value);
            $("#searchForm").queryData({id: 'myContractTable'});
        });

        $('.selectStageType').on('change', function () {
            var value = $(this).val();

            if (value === "1") {
                $("#searchForm").queryData({id: 'myContractStageTable', data: {"stageTime": "year", "stageType": "all"}});
            } else if (value === "2") {
                $("#searchForm").queryData({id: 'myContractStageTable', data: {"stageTime": "year", "stageType": "unComp"}});
            } else if (value === "3") {
                $("#searchForm").queryData({id: 'myContractStageTable', data: {"stageTime": "month", "stageType": "all"}});
            } else if (value === "4") {
                $("#searchForm").queryData({id: 'myContractStageTable', data: {"stageTime": "month", "stageType": "unComp"}});
            }

        });

        $('.selectIncomeStageType').on('change', function () {
            var value = $(this).val();

            if (value === "1") {
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {"dateField2": "year", "stageType": "all"}});
            } else if (value === "2") {
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {"dateField2": "year", "stageType": "unConfirm"}});
            } else if (value === "3") {
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {"dateField2": "month", "stageType": "all"}});
            } else if (value === "4") {
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {"dateField2": "month", "stageType": "unConfirm"}});
            }

        });

        $('.selectReceiptType').on('change', function () {
            var value = $(this).val();
            $('input[name="receiptTime"]').val(value);

            $("#searchForm").queryData({id: 'myReceiptTable'}, {data: {"receiptTime": value}});
        });


        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });

        function myIncomeConsole() {
            popup.openTab({id: 'myIncomeConsole', title: '我的收入确认', url: '${ctxPath}/pages/crm/contract/statistic/my-income-console.jsp',});
        }


    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>