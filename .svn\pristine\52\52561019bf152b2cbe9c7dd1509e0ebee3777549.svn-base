<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>固定资产管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">盘点阶段</span>
						 	<input class="form-control input-sm" name="periodNum">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">资产名称</span>
						 	<input class="form-control input-sm" name="faName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">资产编号</span>
						 	<input class="form-control input-sm" name="faNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">使用状态</span>
						 	<input class="form-control input-sm" name="faUseState">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">使用人</span>
						 	<input class="form-control input-sm" name="faUsePeople">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">卡片编号</span>
						 	<input class="form-control input-sm" name="cardNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">使用人</span>
						 	<input class="form-control input-sm" name="useName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" data-event='enter' class="btn btn-sm btn-default" onclick="DataMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default layui-hide-xs" type="button" lay-event="DataMgr.edit"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var DataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'FaDao.checkDataList',
					id:'dataMgrTable',
					autoSort:false,
					toolbar:'#btnBar',
					limit:30,
					limits:[30,100,500,1000,2000],
					cellMinWidth:100,
					rowEvent:'rowEvent',
					height:'full-90',
					cols: [[
					 {type:'radio'},
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'FA_NAME',
						title: '资产名称',
						align:'left',
						minWidth:220,
						event:'DataMgr.detail',
						style:'color:#1E9FFF;cursor:pointer'
					},{
					    field: 'USE_NAME',
						title: '使用人'
					},{
						field:'PERIOD_NUM',
						title:'盘点阶段'
					},{
						field:'SUBMIT_TIME',
						title:'提交时间',
						style:'color:red'
					},{
						field:'SUBMIT_NAME',
						title:'提交人',
						style:'color:red'
					},{
						field:'FA_USE_STATE',
						title:'使用状态',
						style:'color:red'
					},{
						field:'FA_USE_PEOPLE',
						title:'使用人',
						style:'color:red'
					},{
						field:'FA_REMARK',
						title:'备注',
						style:'color:red'
					},{
						field:'FA_URL',
						title:'资产照片',
						style:'color:red'
					},{
						 field:'FA_TYPE',
						 title:'资产类别'
					 },{
						 field:'FA_NO',
						 title:'资产编号'
					 },{
						 field:'CARD_NO',
						 title:'卡片编号',
						 width:80
					 },{
					    field: 'CARD_NUM',
						title: '卡片数量'
					},{
					    field: 'FA_STATE',
						title: '资产状态'
					},{
					    field: 'CHANGE_TYPE',
						title: '变动方式'
					},{
					    field: 'USE_BEGIN_DATE',
						title: '开始使用日期'
					},{
					    field: 'FEE_DATE',
						title: '入账日期'
					},{
					    field: 'NO_TAX_MONEY',
						title: '未税成本'
					},{
					    field: 'FA_MONEY',
						title: '资产原值'
					},{
					    field: 'ZJ_METHOD',
						title: '折旧方法'
					},{
					    field: 'PLAN_USE_YEAR',
						title: '预计使用年限'
					},{
					    field: 'SURPLUS_USE_DAY',
						title: '预计使用期间数'
					},{
					    field: 'HAS_USE_MOTH',
						title: '累计使用期间数'
					},{
					    field: 'MODE',
						title: '规格型号'
					},{
					    field: 'FA_ADDRESS',
						title: '资产位置'
					},{
					    field: 'USE_DEPT',
						title: '使用部门'
					},{
					    field: 'USE_DEPT_NO',
						title: '使用部门编码'
					},{
					    field: 'REMARK',
						title: '备注'
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						align:'center',
						templet:function(row){
							var time= row['UPDATE_TIME'];
							return cutText(time,12,'');
						}
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'CREATE_USER_NAME',
						title: '创建人',
						align:'center'
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable',jumpOne:true});
			},
			edit:function(dataList){
				var data = dataList;
				if(isArray(dataList)){
				    if(dataList.length == 0){
						layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
						return;
					}
				    data = dataList[0];
				}
				var  faId = data['FA_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['700px','100%'],url:'${ctxPath}/pages/ehr/fa/fa-edit.jsp',title:'修改信息',data:{faId:faId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['700px','100%'],url:'${ctxPath}/pages/ehr/fa/fa-edit.jsp',title:'新建'});
			}
		}
		
		function reloadDataList(){
			DataMgr.query();
		}
		$(function(){
			$("#dataMgrForm").render({success:function(){
				DataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>