<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>版本</title>
	<style>
		img{max-width: 100%;}
		#appDesc{padding: 15px;line-height: 25px;text-align: left;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="AppDao.record" autocomplete="off" data-mars-prefix="app.">
     		    <input type="hidden" value="${param.groupId}" name="groupId"/>
     		    <input type="hidden" value="${param.groupId}" id="groupId" name="app.GROUP_ID"/>
 		   		<input type="hidden" value="${param.groupId}" name="fkId"/>
 			    <table class="layui-hide" id="versionList"></table>
 			    <table class="table table-edit table-vzebra">
			        <tbody>
    		             <tr>
			                <td style="width: 80px">应用相关文件</td>
			               	<td>
			               		 <div data-template="template-files" data-mars="FileDao.fileList"></div>
			               		 <div id="fileList"></div>
			               	</td>
	            	    </tr>
			            <tr>
		                    <td colspan="2">
		                       <div name="app.APP_DESC" id="appDesc"></div>
		                    </td>
			            </tr>
			        </tbody>
 			     </table>
				 <div class="layer-foot text-c">
				    	  <!-- <button type="button" class="btn btn-success btn-sm" style="display: none;"  onclick="Edit.ajaxSubmitForm(2)"> 审核通过 </button>
				    	  <button type="button" class="btn btn-danger btn-sm ml-10" style="display: none;" onclick="Edit.ajaxSubmitForm(3)"> 审核不通过 </button> -->
				    	  <EasyTag:res resId="VERSION_MANAGER">
					    	   <button type="button" class="btn btn-primary btn-sm m-l0 detail"  onclick="Edit.ajaxSubmitForm(2)"> 保 存 </button>
				    	  </EasyTag:res>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>
  		</form>
		 <script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" class="detail" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<script type="text/x-jsrender" id="versionBar">
  			    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="version.detail">下载</a>
 				{{if currentUserId == CREATOR || isSuperUser}}<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="version.edit">编辑</a>{{/if}}
 				{{if currentUserId == CREATOR || isSuperUser}}<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="version.del">删除</a>{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		
		Edit.groupId='${param.groupId}';
		var groupId='${param.groupId}';
		var op='${param.op}';
		
		$(function(){
			$("#editForm").render({success:function(result){
				version.init();
			}});
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.groupId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/app?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		var version={
				init:function(){
					$("#editForm").initTable({
						mars:'VersionDao.list',
						id:'versionList',
						cols: [[
						 {type:'numbers',title:'序号'},
						 {
						    field: 'APP_ID',
							title: 'AppId',
							align:'center'
						 },
			             {
						    field: 'VERSION_NAME',
							title: '版本名称',
							align:'center'
						},
			             {
						    field: 'VERION_DATE',
							title: '版本日期',
							align:'center'
						},{
						    field: 'CREATOR',
							title: '发版人',
							align:'center',
							templet:function(row){
								return getUserName(row.CREATOR);
							}
						},{
						    field: 'CREATE_TIME',
							title: '创建时间',
							align:'center'
						},{
							title: '操作',
							align:'center',
							width:200,
							templet:function(row){
								row['currentUserId']=getCurrentUserId();
								row['isSuperUser']=isSuperUser;
								return renderTpl('versionBar',row);
							}
						}
						]]}
					);
				},
				query:function(){
					$("#editForm").queryData();
				},
				edit:function(data){
					popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'20px',area:['750px','600px'],url:'${ctxPath}/pages/ops/version-edit.jsp',title:'编辑',data:{versionId:data.VERSION_ID}});
				},
				detail:function(data){
					popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'20px',area:['750px','500px'],url:'${ctxPath}/pages/ops/version-edit.jsp',title:'详情',data:{versionId:data.VERSION_ID,op:'detail'}});
				},
				add:function(){
					popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/ops/version-edit.jsp',title:'新增版本'});
				},
				del:function(data){
					layer.confirm("确认要删除吗?",{icon:3},function(){
						ajax.remoteCall("${ctxPath}/servlet/version?action=delete",{'version.VERSION_ID':data.VERSION_ID},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg,{icon:1,time:1200},function(){
									version.query();
									layer.closeAll();
								});
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});
						
					});
				}
			}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>