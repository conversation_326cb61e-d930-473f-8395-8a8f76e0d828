<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
	<style>
		.layui-tree-txt{font-size: 13px;}
		.layui-btn+.layui-btn{margin-left: 2px;}
		.layui-table-cell{padding: 0 6px;}
		.layui-tab-item{padding: 0px 5px;margin-top: -5px;}
		.icon{width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:1px;}
	    /* tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;} */
		
		.groupTree-left-content{width: 280px;float: left;display: inline-block;min-height: calc(100vh - 20px);}
		.groupTree-right-content{float:left;margin-left: 10px;width: calc(100% - 298px);}
		#organizationTreeBar {
		    padding: 10px 15px;
		    border: 1px solid #e6e6e6;
		    background-color: #f2f2f2;
		}
		#organizationTree {
		    border: 1px solid #e6e6e6;
		    border-top: none;
		    padding: 5px 2px;
		    overflow: auto;
		    height: -webkit-calc(100vh - 100px);
		    height: -moz-calc(100vh - 100px);
		    height: calc(100vh - 100px);
		}
		.layui-tree-entry .layui-tree-txt {
		    padding: 0 5px;
		    border: 1px transparent solid;
		    text-decoration: none !important;
		}
		.layui-tree-entry.ew-tree-click .layui-tree-txt {
		    background-color: #fff3e0;
		    border: 1px #FFE6B0 solid;
		}
		.layui-tree-set{padding: 3px 5px;}
		.layui-tree-iconClick{margin: 0 5px;display: none;}
		.layui-tree-entry .layui-tree-txt{padding: 0px;color: #616062;}
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="status" id="status" type="hidden" value="1"/>
			<input name="taskState" id="taskState" type="hidden"/>
			<div onclick="showProjectTree()" class="leftShow" style="position: absolute;display: inline-flex;align-items: center;height: 50px;background-color: #00abfc;top:40%;cursor: pointer;color: #fff;"><i class="fa fa-chevron-left"></i></div>
			<div class="ibox">
				<div class="layui-card groupTree-left-content" style="padding: 10px;">
					<div class="layui-form toolbar" id="organizationTreeBar">项目列表<small>（最长时间1年内）</small></div>
					<div class="layui-card-body" id="organizationTree"></div>
				</div>
				<div class="groupTree-right layui-card groupTree-right-content">
					<div class="ibox-title clearfix">
		          		 <div class="form-group">
						      <div class="input-group input-group-sm"  style="width: 100px">
								 <span class="input-group-addon">年份</span>	
								 <input data-mars-reload="false" readonly="readonly" onclick="singleDict(this,'Y005')" name="yearId"  class="form-control input-sm">
						     </div>
						      <div class="input-group input-group-sm"  style="width: 150px">
								 <span class="input-group-addon">任务类型</span>	
								<select data-mars-reload="false" name="taskTypeId" onchange="list.query()" data-mars="TaskDao.taskType" class="form-control">
									<option value="">请选择</option>
								</select>
						     </div>
		          		     <div class="input-group input-group-sm"  style="width: 180px">
								 <span class="input-group-addon">任务名称</span>	
								 <input type="text" name="taskName" class="form-control input-sm">
						     </div>
		          		     <div class="input-group input-group-sm"  style="width: 160px">
								 <span class="input-group-addon">项目</span>	
								 <input type="hidden" name="projectId" class="form-control input-sm">
								 <input type="text" id="projectId" onclick="singleProject(this);" class="form-control input-sm">
						     </div>
		          		     <div class="input-group input-group-sm">
			             		 <span class="input-group-addon">负责人</span>
								 <input type="hidden" name="assignUserId" class="form-control input-sm">
								 <input type="text" onclick="singleUser(this)" class="form-control input-sm" style="width: 60px;">
			                 </div>
							 <div class="input-group input-group-sm">
								 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								 <button type="button" class="btn btn-sm btn-default ml-5" onclick="list.clearForm()">清空</button>
							 </div>
							 <div class="input-group input-group-sm pull-right">
								 <button type="button" class="btn btn-sm btn-info" onclick="list.add()"> + 发起任务</button>
								 <button type="button" class="btn btn-sm btn-default ml-10" onclick="list.importExcel()"> 导入任务</button>
								 <button type="button" class="btn btn-sm btn-warning ml-10" onclick="flowApplyLink('ts_rd')"> + 需求流程</button>
								  <script type="text/html" id="taskBar">
						     	   <a href="javascript:;" lay-event="finishTask" class="btn btn-xs btn-default btn-info finishTask">批量完成</a>
						     	   <a href="javascript:;" lay-event="cbTask" class="btn btn-xs btn-default btn-info cbTask">批量催办</a>
						     	   <a href="javascript:;" lay-event="ysTask" class="btn btn-xs btn-default btn-info ysTask ml-10">批量验收</a>
						     	   <a href="javascript:;" onclick="exportTask()" class="btn btn-xs btn-default ml-10">导出任务</a>
							 </script>
							 </div>
		          	     </div>
		              </div> 
						<div class="ibox-content">
							<div class="layui-tab layui-tab-card" lay-filter="filterData" >
							  <ul class="layui-tab-title">
							    <li class="layui-this" lay-id="1" data-flag="1">我负责的任务</li>
							    <li lay-id="2" data-flag="2">我发起的任务</li>
							    <li lay-id="3" data-flag="3">抄送我的任务</li>
							  </ul>
							  <div class="layui-tab layui-tab-brief" style="margin-top: 6px;" lay-filter="task" data-mars="TaskDao.myTaskListCountStat">
								  <ul class="layui-tab-title">
								    <li data-state="" class="layui-this">全部 <span class="layui-badge layui-bg-cyan ALL">0</span></li>
								    <li data-state="10">待办中 <span class="layui-badge layui-bg-orange" id="A">0</span></li>
								    <li data-state="11">已退回 <span class="layui-badge" id="F">0</span></li>
								    <li data-state="12">暂停中 <span class="layui-badge layui-bg-cyan" id="G">0</span></li>
								    <li data-state="13">已取消 <span class="layui-badge layui-bg-orange" id="H">0</span></li>
								    <li data-state="20">进行中 <span class="layui-badge" id="B">0</span></li>
								     <li data-state="25">超时未完成 <span class="layui-badge" id="E">0</span></li>
								    <li data-state="30">已完成 <span class="layui-badge layui-bg-blue" id="C">0</span></li>
								    <li data-state="40">已验收 <span class="layui-badge layui-bg-green" id="D">0</span></li>
								  </ul>
							  </div> 
							  <div class="layui-tab-content" style="padding: 5px 0px;margin-top: 5px;">
							    <div class="layui-tab-item layui-show">
							    	 <table class="layui-hide" id="myTaskTable1"></table>
							    </div>
							    <div class="layui-tab-item">
							    	 <table class="layui-hide" id="myTaskTable2"></table>
							    </div>
							    <div class="layui-tab-item">
							    	 <table class="layui-hide" id="myTaskTable3"></table>
							    </div>
							  </div>
							</div>
						</div>
					</div>
			</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
			{{if TASK_STATE==40}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
			 {{else TASK_STATE==42}}
  				<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.detail">重新处理</a>
			 {{else TASK_STATE==10}}
  				<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="list.detail">处理任务</a>
			 {{else TASK_STATE==30}}
  				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看详情</a>
			 {{else}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">更改进度</a>
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="bar2">
			{{if TASK_STATE<40&&TASK_STATE!=11}}
				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit"><i class="layui-icon layui-icon-edit"></i></a>
			{{/if}}  			
			{{if TASK_STATE==30}}
  				<a class="layui-btn layui-btn-success layui-btn-xs" lay-event="list.detail">验收</a>
			 {{else TASK_STATE==42}}
  				<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.detail">重新验收</a>
			 {{else TASK_STATE==10||TASK_STATE==11}}
				{{if VIEW_COUNT<=20}}<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.delTask">删除</a>{{/if}}
			 {{else (TASK_STATE==10||TASK_STATE==20)&&CREATOR==ASSIGN_USER_ID}}
				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">更改进度</a>
			{{else TASK_STATE<40}}

			{{else TASK_STATE==40}}
  				{{if TASK_AUTH==0}}<a class="layui-btn layui-btn-xs" lay-event="list.cannelShare">取消</a>{{else}}<a class="layui-btn layui-btn-xs" lay-event="list.share"><i class="layui-icon layui-icon-share"></i></a>{{/if}}
			 {{else}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
			{{/if}}
		</script>
		<script type="text/html" id="bar3">
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">

		var status = '1';
		var firstLoad=true;
		var list={
			init1:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					title:'我负责的任务',
					cellMinWidth:50,
					limit:30,
					data:{status:1},
					height:'full-160',
					toolbar:'#taskBar',
					autoSort:false,
					id:'myTaskTable1',
					cols: [[
						{
						 	type: 'checkbox',
							title: '序号',
							width:36,
							align:'left'
						 },{
						    field: 'CREATOR',
							title: '发起人',
							align:'left',
							width:70,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['CREATOR']+'\')">'+row.CREATE_NAME+'</a>';
							}
						  },{
						    field: 'TASK_NAME',
							title: '任务名称',
							event:'list.myTaskDetail',
							minWidth:220,
							align:'left',
							style:'color:#1E9FFF;cursor:pointer',
							templet:function(row){
								var fileCount = row['FILE_COUNT'];
								var id = row['P_TASK_ID'];
								var fileDes = fileCount>0?getFileIcon('.file'):''
								if(id){
									return fileDes+"<i class='layui-icon layui-icon-senior'></i>"+row['TASK_NAME'];
								}else{
									return fileDes+row['TASK_NAME'];
								}
							}
						},{
						    field: 'PROJ_NAME',
							title: '项目名称',
							event:'list.projectDetail',
							align:'left',
							sort:true,
							minWidth:150
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							sort:true,
							width:80,
							event:'list.updateState',
							templet:function(row){
								var state = row.TASK_STATE;
								var progress = row.PROGRESS;
								if(state==20&&progress>0){
									return taskStateLabel(row.TASK_STATE,progress+"%");
								}else{
									return taskStateLabel(row.TASK_STATE);
								}
							}
						},{
							field:'PROGRESS',
							title:'进度',
							width:70,
							templet:'<div>{{d.PROGRESS}}%</div>'
						},{
						    field: 'TASK_TYPE_ID',
							title: '任务类型',
							align:'center',
							sort:true,
							width:80,
							templet:function(row){
								return getText(row['TASK_TYPE_ID'],'taskTypeId');
							}
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'center',
							sort:true,
							width:70,
							templet:function(row){
								return taskTextLevel(row.TASK_LEVEL);
							}
						},{
						    field: 'CREATE_TIME',
							title: '发起时间',
							align:'center',
							sort:true,
							width:90,
							templet:function(row){
								var time= row['CREATE_TIME'];
								return cutText(time,12,'');
							}
						},{
						    field: 'DEADLINE_AT',
							title: '截止时间',
							align:'left',
							sort:true,
							width:100,
							templet:function(row){
								var time= row['DEADLINE_AT'];
								var state= row['TASK_STATE'];
								if(state<30&&state!=12){
									return timeBetween(time);
								}
								return cutText(time,12,'');
							}
						},{
							title: '操作',
							align:'center',
							width:80,
							hide:true,
							templet:function(row){
							    return renderTpl('bar1',row);
							}
						}
					]],done:function(result){
						showFun();
						if(firstLoad)$(".ALL").text(result.totalRow);
						firstLoad=false;
						table.on('sort(myTaskTable1)', function(obj){
							$("#searchForm").queryData({id:'myTaskTable1',jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
						});
					}}
				);
			},
			init2:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					id:'myTaskTable2',
					cellMinWidth:50,
					autoSort:false,
					limit:30,
					height:'full-160',
					data:{status:2},
					toolbar:'#taskBar',
					title:'我发起的任务',
					cols: [[
						 {
						 	type: 'checkbox',
							title: '序号',
							width:36,
							align:'left'
						 },{
						 	type: 'numbers',
							title: '序号',
							align:'left',
							width:40
						 },{
						    field: 'ASSIGN_USER_ID',
							title: '负责人',
							align:'left',
							width:70,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['ASSIGN_USER_ID']+'\')">'+row.ASSIGN_USER_NAME+'</a>';
							}
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							minWidth:220,
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer',
							templet:function(row){
								var id = row['P_TASK_ID'];
								if(id){
									return "<i class='layui-icon layui-icon-senior'></i>"+row['TASK_NAME'];
								}else{
									return row['TASK_NAME'];
								}
							}
						},{
						    field: 'PROJ_NAME',
							title: '项目名称',
							minWidth:150,
							event:'list.projectDetail',
							align:'left'
						 },{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							width:80,
							templet:function(row){
								var state = row.TASK_STATE;
								var progress = row.PROGRESS;
								if(state==20&&progress>0){
									return taskStateLabel(row.TASK_STATE,progress+"%");
								}else{
									return taskStateLabel(row.TASK_STATE);
								}
							}
						},{
							field:'PROGRESS',
							title:'进度',
							width:70,
							templet:'<div>{{d.PROGRESS}}%</div>'
						},{
						    field: 'VIEW_COUNT',
							title: '浏览数',
							align:'center',
							sort:true,
							width:70
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							sort:true,
							align:'center',
							width:70,
							templet:function(row){
								return taskTextLevel(row.TASK_LEVEL);
							}
						},{
						    field: 'CREATE_TIME',
							title: '发起时间',
							sort:true,
							width:90,
							align:'left',
							templet:function(row){
								var time= row['CREATE_TIME'];
								return cutText(time,12,'');
							}
						},{
						    field: 'DEADLINE_AT',
							title: '截止时间',
							sort:true,
							align:'center',
							width:90,
							templet:function(row){
								var time= row['DEADLINE_AT'];
								return cutText(time,12,'');
							}
						},{
							title: '操作',
							align:'left',
							cellMinWidth:100,
							width:100,
							templet:function(row){
							    return renderTpl('bar2',row);
							}
						}
					]],done:function(result){
						showFun();
						if(firstLoad)$(".ALL").text(result.totalRow);
						firstLoad=false;
						table.on('sort(myTaskTable2)', function(obj){
							  $("#searchForm").queryData({id:'myTaskTable2',jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
						});
					}}
				);
			},
			init3:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					id:'myTaskTable3',
					limit:30,
					height:'full-160',
					data:{status:3},
					title:'我参与的任务',
					toolbar:'#taskBar',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							minWidth:220,
							event:'list.detail',
							align:'left',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'PROJ_NAME',
							title: '项目名称',
							minWidth:150,
							event:'list.projectDetail',
							align:'left'
						 },{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:90,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['CREATOR']+'\')">'+row.CREATE_NAME+'</a>';
							}
						},{
						    field: 'ASSIGN_USER_ID',
							title: '负责人',
							align:'center',
							width:80,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['ASSIGN_USER_ID']+'\')">'+row.ASSIGN_USER_NAME+'</a>';
							}
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							width:80,
							templet:function(row){
								return taskStateLabel(row.TASK_STATE);
							}
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'center',
							width:70,
							templet:function(row){
								return taskTextLevel(row.TASK_LEVEL);
							}
						},{
							field:'PROGRESS',
							title:'进度',
							width:70,
							templet:'<div>{{d.PROGRESS}}%</div>'
						},{
						    field: 'CREATE_TIME',
							title: '创建时间',
							align:'center',
							width:120,
							templet:function(row){
								var time= row['CREATE_TIME'];
								return cutText(time,12,'');
							}
							
						},{
						    field: 'UPDATE_TIME',
							title: '更新时间',
							align:'left',
							hide:true,
							width:100
						},{
							title: '操作',
							align:'left',
							hide:true,
							width:80,
							toolbar: '#bar3'
						}
					]],done:function(result){
						showFun();
						if(firstLoad)$(".ALL").text(result.totalRow);
						firstLoad=false;
					}}
				);
			},
			init4:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					id:'myTaskTable4',
					data:{status:4},
					title:'任务参考解决',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							minWidth:220,
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'FINISH_TIME',
							title: '完成时间',
							align:'center',
							width:160,
							templet:function(row){
								var time= row['FINISH_TIME'];
								return cutText(time,12,'');
							}
						},{
						    field: 'CREATE_NAME',
							title: '发起人',
							align:'center',
							width:120
						},{
						    field: 'ASSIGN_USER_NAME',
							title: '负责人',
							align:'left',
							width:120
						},{
						    field: 'UPDATE_TIME',
							title: '更新时间',
							width:160,
							align:'left',
							templet:function(row){
								var time= row['UPDATE_TIME'];
								return cutText(time,12,'');
							}
						}
					]],success:function(){
						showFun();
						$(".layui-tab-brief").remove();
					}}
				);
			},
			query:function(){
				firstLoad=true;
				$("#searchForm").render();
				$("#searchForm").queryData({id:'myTaskTable'+status,jumpOne:true});
			},
			clearForm:function(){
				$("[name='projectId']").val('');
				$("[name='assignUserId']").val('');
				$("#searchForm")[0].reset();
				list.query();
			},
			edit:function(data){
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				var businessId = data['BUSINESS_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'编辑任务',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,businessId:businessId}});
			},
			add:function(){
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务'});
			},
			importExcel:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['550px','400px'],url:'${ctxPath}/pages/task/task-import.jsp',title:'导入任务'});
			},
			detail:function(data){
				var state = data['TASK_STATE'];
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				if(state==40){
					popup.openTab({id:"task_"+data.TASK_ID,type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-detail.jsp?isDiv=0',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
				}else{
					popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
				}
			},
			myTaskDetail:function(data){
				list.detail(data);
			},
			projectDetail:function(data){
				var projectId = data['PROJECT_ID'];
				if(projectId){
					popup.openTab({id:'projectId',url:'${ctxPath}/pages/project/project-detail.jsp',title:'项目总览',data:{projectId:projectId}});
				}
			},
			share:function(data){
				ajax.remoteCall("${ctxPath}/servlet/task?action=share",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			},
			delTask:function(data){
				layer.confirm("是否确认删除?",{offset:'20px',icon:7},function(index){
					layer.close(index)
					ajax.remoteCall("${ctxPath}/servlet/task?action=delTask",{taskId:data['TASK_ID']},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								layer.closeAll();
								list.query();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			updateState:function(data){
				if(data['TASK_STATE']==20){
					//todo
				}
			},
			cannelShare:function(data){
				ajax.remoteCall("${ctxPath}/servlet/task?action=cannelShare",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		function reloadTaskList(source){
			$("#searchForm").queryData({id:'myTaskTable1',jumpOne:true});
			if(source){
				layui.element.tabChange('filterData', source);
			}
		}
		
		var _flag = '1';
		layui.use('element',function(){
			var element=layui.element;
			element.on('tab(task)', function(elem){
				var  state = $(this).data('state');
				$("#taskState").val(state);
				if((state==10||state==20)&&status==1){
					//显示处理按钮
				}
				$("#searchForm").queryData({id:'myTaskTable'+_flag,jumpOne:true});
			});
			
			element.on('tab(filterData)', function(elem){
				var  flag = $(this).data('flag');
				$('#status').val(flag);
				
				showFun();
				
				localStorage.setItem("lastSelectTaskTab",flag);
				status = flag;_flag = flag;
				list.query();
				loadProjectTree();
			});
		  });
		
		function showFun(){
			var flag = $('#status').val();
			if(flag=='1'){
				$('.cbTask').hide();
				$('.finishTask,.ysTask').show();
			}else if(flag=='2'){
				$('.cbTask').show();
				$('.finishTask,.ysTask').hide();
			}else if(flag=='3'){
				$('.cbTask,.ysTask,.finishTask').hide();
			}
		}
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.init1();
				list.init2();
				list.init3();
				//list.init4();
				initTreeShow();
				
				var lastSelectTaskTab = localStorage.getItem("lastSelectTaskTab");
				if(lastSelectTaskTab){
					status = lastSelectTaskTab;
					layui.element.tabChange("filterData",lastSelectTaskTab);
				}else{
					localStorage.setItem("lastSelectTaskTab",1);
					loadProjectTree();
				}
			}});
		});
		
		
		var loadProjectTree = function(){
			 var device = layui.device();
			 if(device.mobile){
				 showProjectTree();
				return;
			 }
			
			 var el = $('.groupTree-right');
			 if(!el.hasClass('groupTree-right-content')){
				return;
			 }
			 $('.groupTree-right').addClass('groupTree-right-content');
			 $('.groupTree-right').prev().addClass('groupTree-left-content').show(100);
			 layui.use(['tree'], function () {
			        var tree = layui.tree;
		        	ajax.remoteCall(getCtxPath()+"/webcall?action=TaskDao.myTaskProjectTree",{status:status},function(rs) { 
						var result= rs.data;
						var data = dataToTree(result,{idFiled: 'PROJECT_ID', textFiled: 'PROJECT_NAME', parentField: 'PROJECT_ID', childField: '',def:{spread:false}, map: {PROJECT_ID: 'id', PROJECT_NAME: 'title' } });
		              	data.unshift({title:'全部项目',id:'',PROJECT_ID:'',PROJECT_NAME:''});
						tree.render({
		                    elem: '#organizationTree',
		                    onlyIconControl: true,
		                    data: data,
		                    click: function (obj) {
		                        $("[name='projectId']").val(obj.data.PROJECT_ID);
		                        $('#projectId').val(obj.data.PROJECT_NAME);
		                        $('#organizationTree').find('.ew-tree-click').removeClass('ew-tree-click');
		                        $(obj.elem).children('.layui-tree-entry').addClass('ew-tree-click');
		                        list.query();
		                    }
		                });
		        	});
			    }
			 );
		}
		
		function showProjectTree(){
			var _el = $('.leftShow');
			var el = $('.groupTree-right');
			if(el.hasClass('groupTree-right-content')){
				el.removeClass('groupTree-right-content');
				el.prev().hide(100);
				layui.data("userSetting",{key:"showProjectTree",value:0});
				$(_el).html('<i class="fa fa-chevron-right"></i>');
			}else{
				el.addClass('groupTree-right-content');
				el.prev().addClass('groupTree-left-content').show(100);
				layui.data("userSetting",{key:"showProjectTree",value:1});
				$(_el).html('<i class="fa fa-chevron-left"></i>');
				
				if($('.layui-tree').length==0){
					loadProjectTree();
				}
			}
		}
		
		function initTreeShow(){
			var data = layui.data("userSetting");
			if(data.showProjectTree=='0'){
				showProjectTree();
			}
		}
		
		function exportTask(){
			layer.msg('正在导出',{time:500});
			location.href = '${ctxPath}/servlet/task?action=exportTask&data='+encodeURI(JSON.stringify(form.getJSONObject('#searchForm')));
		}
		
		function cbTask(dataList){
			if(dataList.length>0){
				layer.prompt({formType: 2,value: '',offset:'20px',title: '请输入催办',area: ['400px', '150px']}, function(remark, index, elem){
			    	layer.close(index);
					for(var index in dataList){
						var taskObj = dataList[index];
						var taskId = taskObj['TASK_ID'];
						var data = $.extend({},taskObj,{content:'催办：'+remark,fkId:taskId,title:taskObj['TASK_NAME'],commentNoticeType:'wx,email'});
						ajax.remoteCall("${ctxPath}/servlet/comment?action=add",data,function(result) { 
							if(result.state == 1){
								layer.msg('操作成功',{icon:1,time:1200});
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});
					}
				});
			}else{
				layer.msg('请选择');
			}
		}
		
		function finishTask(dataList){
			if(dataList.length>0){
				layer.confirm('确认操作吗',{offset:'20px'},function(index){
			    	layer.close(index);
					for(var index in dataList){
						var taskObj = dataList[index];
						var taskState = taskObj['TASK_STATE'];
						if(taskState=='10'||taskState=='20'){
							var taskId = taskObj['TASK_ID'];
							var data = {};
							data['task.TASK_ID'] = taskId;
							data['ASSIGN_USER_ID'] = taskObj['ASSIGN_USER_ID'];
							data['CREATOR'] = taskObj['CREATOR'];
							data['TASK_NAME'] = taskObj['TASK_NAME'];
							data['workHour'] = taskObj['task.PLAN_WORK_HOUR'];
							data['TASK_STATE'] = taskObj['task.TASK_STATE'];;
							data['task.TASK_STATE'] = 30;
							
							ajax.remoteCall("${ctxPath}/servlet/task?action=updateState",data,function(result) { 
								if(result.state == 1){
									layer.msg('操作成功',{icon:1,time:1200});
									list.query();
								}else{
									layer.alert(result.msg,{icon: 5});
								}
							});
						}
					}
				});
			}else{
				layer.msg('请选择');
			}
		}
		
		function ysTask(dataList){
			if(dataList.length>0){
				layer.confirm('确认操作吗',{offset:'20px'},function(index){
			    	layer.close(index);
					for(var index in dataList){
						var taskObj = dataList[index];
						var taskState = taskObj['TASK_STATE'];
						if(taskState=='30'){
							var taskId = taskObj['TASK_ID'];
							var data = {};
							data['task.TASK_ID'] = taskId;
							data['ASSIGN_USER_ID'] = taskObj['ASSIGN_USER_ID'];
							data['CREATOR'] = taskObj['CREATOR'];
							data['TASK_NAME'] = taskObj['TASK_NAME'];
							data['TASK_STATE'] = taskObj['task.TASK_STATE'];;
							data['task.TASK_STATE'] = 40;
							ajax.remoteCall("${ctxPath}/servlet/task?action=updateState",data,function(result) { 
								if(result.state == 1){
									layer.msg('操作成功',{icon:1,time:1200});
									list.query();
								}else{
									layer.alert(result.msg,{icon: 5});
								}
							});
						}
					}
				});
			}else{
				layer.msg('请选择');
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>