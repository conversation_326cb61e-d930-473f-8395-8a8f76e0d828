<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择发票</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectInvoiceForm">
       	   <input name="platformId" value="${param.platformId}" type="hidden"/>
       	   <input name="contractId" value="${param.contractId}" type="hidden"/>
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
					 <span class="input-group-addon">发票编号</span>	
					 <input type="text" name="invoiceNo" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter" onclick="SelectInvoice.query()">
                    <span class="glyphicon glyphicon-search"></span> 搜索
                 </button>
	   		</div>
           	<div class="ibox">
              	<table id="selectInvoiceList"></table>
            </div>
            <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary" type="button" onclick="SelectInvoice.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20" type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
			var SelectInvoice = {
					sid:'${param.sid}',
					query : function(){
						$("#selectInvoiceForm").queryData();
					},
					initTable : function(){
						$("#selectInvoiceForm").initTable({
							mars:'InvoiceDao.pageList',
							id:'selectInvoiceList',
							page:true,
							limit:20,
							height:'350px',
							rowDoubleEvent:'SelectInvoice.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
							 },{
                                field: 'SETTLEMENT_ID',
                                title: '结算状态',
                                width: 80,
                                align: 'center',
                                templet: function(d){
                                    if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
                                        if(d.SETTLEMENT_ID === '${param.settlementId}') {
                                            return '<span style="color:#2a63f4;">本结算</span>';
                                        } else {
                                            return '<span style="color:#ff5722;">已结算</span>';
                                        }
                                    } else {
                                        return '<span style="color:#009688;">未结算</span>';
                                    }
                                }
                            },{
							    field: 'INVOICE_NO',
								title: '发票编号',
								width:90
							},{
							    field: 'INVOICE_TYPE',
								title: '开票类型',
								width:100
							},{
                                field: 'MARK_DATE',
                                title: '开票日期',
                                width:90
                            },{
                                field: 'MARK_MONEY',
                                title: '开票金额',
                                width:90,
                                align:'left'
                            },{
                                field: 'TAX_RATE',
                                title: '税率(%)',
                                width:80,
                                align:'center'
                            },{
                                field: 'AMOUNT_IN_FIGUERS',
                                title: '不含税金额',
                                width:90,
								align:'left'
								},{
                                field: 'REMARK',
                                title: '备注'
                            }
							]],
							done: function(res, curr, count){
								// 对已结算的行进行样式处理
								$('#selectInvoiceList').next('.layui-table-view').find('tbody tr').each(function(){
									var index = $(this).attr('data-index');
									if(index !== undefined && res.data[index]){
										var rowData = res.data[index];
										// 只有当记录被其他结算关联时才置灰禁用
										if(rowData.SETTLEMENT_ID && rowData.SETTLEMENT_ID.trim() !== '' && 
                                           rowData.SETTLEMENT_ID !== '${param.settlementId}') {
											$(this).css({'background-color': '#f5f5f5', 'color': '#999'});
											$(this).find('input[type="checkbox"]').prop('disabled', true);
										}
									}
								});
							}
					    });
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectInvoice.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('selectInvoiceList');
							if(checkStatus.data.length>0){
								// 检查是否有被其他结算关联的记录
								var settledRecords = [];
								var validData = [];
								var data = checkStatus.data;
								for(var index in data){
									if(data[index]['SETTLEMENT_ID'] && data[index]['SETTLEMENT_ID'].trim() !== '' && 
                                       data[index]['SETTLEMENT_ID'] !== '${param.settlementId}') {
										settledRecords.push(data[index]['INVOICE_NO']);
									} else {
										validData.push(data[index]);
									}
								}
								
								if(settledRecords.length > 0) {
									layer.alert('以下发票记录已被其他结算关联，不允许选择：<br>' + settledRecords.join('<br>'), {icon: 0});
									return;
								}
								
								var nos = [];
								var ids = [];
								var totalAmount = 0;
								var totalNoTaxAmount = 0;
								var markDate = '';
								for(var index in validData){
									nos.push(validData[index]['INVOICE_NO']);
									ids.push(validData[index]['INVOICE_ID']);
									totalAmount += Number(validData[index]['MARK_MONEY']) || 0;
									totalNoTaxAmount += Number(validData[index]['AMOUNT_IN_FIGUERS']) || 0;
									if(validData[index]['MARK_DATE'] && (!markDate || validData[index]['MARK_DATE'] > markDate)) {
										markDate = validData[index]['MARK_DATE'];
									}
								}
								el.val(nos.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								
								// 回调函数,传递金额数据
								if(typeof InvoiceSelectCallBack === 'function') {
									InvoiceSelectCallBack({
										totalAmount: totalAmount.toFixed(2),
										totalNoTaxAmount: totalNoTaxAmount.toFixed(2),
										invoiceIds: ids.join(','),
										invoiceNos: nos.join(','),
										markDate: markDate
									});
								}
								
								popup.layerClose("selectInvoiceForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							// 检查单选记录是否被其他结算关联
							if(selectRow['SETTLEMENT_ID'] && selectRow['SETTLEMENT_ID'].trim() !== '' && 
                               selectRow['SETTLEMENT_ID'] !== '${param.settlementId}') {
								layer.alert('该发票记录已被其他结算关联，不允许选择', {icon: 0});
								return;
							}
							
							el.val(selectRow['INVOICE_NO']);
							if(el.prev().length>0){
								el.prev().val(selectRow['INVOICE_ID']);
							}
							
							// 单选时也调用回调
							if(typeof InvoiceSelectCallBack === 'function') {
								InvoiceSelectCallBack({
									totalAmount: selectRow['MARK_MONEY'],
									totalNoTaxAmount: selectRow['AMOUNT_IN_FIGUERS'],
									invoiceIds: selectRow['INVOICE_ID'],
									invoiceNos: selectRow['INVOICE_NO'],
									markDate: selectRow['MARK_DATE']
								});
							}
							
							popup.layerClose("selectInvoiceForm");
						}
					}
			};
			$(function(){
				SelectInvoice.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
