<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">使用时长</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data1"/>
					  			</td>
					  			<td class="required">CPU线程数</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">内存容量</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data3"/>
					  			</td>
					  			<td class="required">磁盘容量</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">操作系统版本</td>
					  			<td colspan="3">
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data5"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" maxlength="500" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>