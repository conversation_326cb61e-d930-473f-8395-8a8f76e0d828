package com.yunqu.work.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;


public class FlowApplyRowMapper implements EasyRowMapper<FlowApplyModel> {

	@SuppressWarnings("unchecked")
	@Override
	public FlowApplyModel mapRow(ResultSet rs, int rowNum) {
		FlowApplyModel vo = new FlowApplyModel();
		try {
			vo.setApplyId(rs.getString("APPLY_ID"));
			vo.setApplyTitle(rs.getString("APPLY_TITLE"));
			vo.setApplyNo(rs.getString("APPLY_NO"));
			vo.setApplyBy(rs.getString("APPLY_BY"));
			vo.setApplyState(rs.getInt("APPLY_STATE"));
			vo.setApplyName(rs.getString("APPLY_NAME"));
			vo.setApplyTime(rs.getString("APPLY_TIME"));
			vo.setApplyRemark(rs.getString("APPLY_REMARK"));
			vo.setApplyLevel(rs.getString("APPLY_LEVEL"));
			vo.setFlowCode(rs.getString("FLOW_CODE"));
			vo.setFlowName(rs.getString("FLOW_NAME"));
			vo.setDeptId(rs.getString("DEPT_ID"));
			vo.setDeptName(rs.getString("DEPT_NAME"));
			vo.setNextNodeId(rs.getString("NEXT_NODE_ID"));
			vo.setNextResultId(rs.getString("NEXT_RESULT_ID"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
