<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>平台管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input type="hidden" name="projectId" value="${param.projectId}">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="fa fa-codepen"></span> 平台管理</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">平台名称</span>	
							 <input type="text" name="platformName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 添加</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
  			{{if currentUserId == CREATOR || isSuperUser}}<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
 			<a class="layui-btn layui-btn-primary layui-btn-xs layui-hide" lay-event="list.detail">查看</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		var  projectId='${param.projectId}';
		
		$(function(){
				list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'PlatformDao.list',
					limit:20,
					height:'full-90',
					cols: [[
					{
						title: '序号',
						align:'left',
						type:'numbers'
					},
		             {
					    field: 'PROJECT_NAME',
						title: '关联项目',
						align:'left'
					},{
					    field: 'PLATFORM_ADDRESS',
						title: '所在省市',
						width:120,
						align:'center'
					},{
					    field: 'PLATFORM_URL',
						title: '访问地址',
						align:'left',
						width:200,
						templet:'<div><a style="color:#1E9FFF" target="_blank" href="{{d.PLATFORM_URL}}">{{d.PLATFORM_URL}}</a></div>'
					},{
					    field: 'OWNER',
						title: '负责人',
						align:'center',
						width:120,
						templet:function(row){
							return getUserName(row.OWNER);
						}
					},{
					    field: 'PLATFORM_STATE',
						title: '状态',
						width:120,
						align:'center',
						templet:function(row){
							var val=row.PLATFORM_STATE;
							if(val==1){
								return '运行中';
							}else if(val==2){
								return '已暂停';
							}else{
								return '已关停';
							}
						}
					},{
					    field: 'CREATE_NAME',
						title: '创建人',
						align:'center',
						width:120
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						width:180,
						align:'center'
					},{
						title: '操作',
						width:120,
						align:'center',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							row['isSuperUser']=isSuperUser;
							return renderTpl('bar',row);
						}
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/ops/platform-edit.jsp',title:'编辑',data:{platformId:data.PLATFORM_ID}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/ops/platform-edit.jsp',title:'新增平台',data:{projectId:projectId}});
			},
			detail:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/ops/platform-detail.jsp',title:'平台详情',data:{platformId:data.PLATFORM_ID}});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>