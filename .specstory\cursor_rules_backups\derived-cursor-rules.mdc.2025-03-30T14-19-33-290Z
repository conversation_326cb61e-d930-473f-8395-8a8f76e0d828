
## PROJECT OVERVIEW
This project, yq-work, is a comprehensive web application managing various aspects of project workflow, including task management, contract management, and employee information.  It utilizes a Java backend with a JSP frontend and incorporates various third-party libraries and APIs.  Jsoup has been replaced with OkHttp 4.12.0 for SSE support in `SiliconCloudAI.java`.

## CODE STYLE
Adhere to standard Java coding conventions.  Maintain consistent indentation (4 spaces).  Use descriptive variable and method names.

## FOLDER ORGANIZATION
The project follows a standard Maven structure.  All Java source code resides in `src/main/java`, while JSP pages are located in `WebContent`. Resources are in `resource` and `WebContent/static` contains static assets (CSS, JavaScript, images).

## TECH STACK
- Java (version: [Specify Java version])
- JSP (version: [Specify JSP version])
- JFinal (version: 5.2.3)
- MySQL (version: [Specify MySQL version])  or Oracle (version: [Specify Oracle version]) (Note: Support for both databases is required)
- Maven
- Various JavaScript libraries (Layui, jQuery, etc.)
- Various Java libraries (listed in project dependencies)
- commonmark-0.21.0.jar
- cos-2020.4.jar
- jfinal-weixin-3.4.jar
- okhttp-4.12.0.jar
- UserAgentUtils-1.21.jar
- OkHttp (version: 4.12.0)


## PROJECT-SPECIFIC STANDARDS
- All database interactions should be performed through the DAO layer.
- Use consistent date and time formats throughout the application.
- Implement robust error handling and logging.
- **"Is/Is not project bonus issued" field:** This field requires a reason.  If "Yes" is selected, the subsequent field should contain the reason for issuing the bonus. If "No" is selected, the subsequent field should contain the reason for not issuing the bonus.  The textarea for the reason (class `sendAwardReason`) should be shown/hidden based on the radio button selection, including during initialization.  If "No" is selected, the reason field is mandatory for submission.
- **Dynamic Project Bonus Reason Title:** The title of the text area for specifying the reason for issuing or not issuing a project bonus should dynamically update based on the selection in the "Is/Is not project bonus issued" radio button.  This is implemented using JavaScript in `config.jsp`. (See 2025-03-17_08-01-项目奖发放原因动态显示.md)

## WORKFLOW & RELEASE RULES
- Follow the standard Maven release process.
- All code changes must be reviewed before merging into the main branch.
- Automated testing is required before release.  [Specify testing framework and coverage requirements]
- Weekly reports and project documentation should be automatically generated. (See 2025-03-14_05-53-定时生成周报和项目文档.md)
- Optimize SQL queries to avoid redundancy.  Merge similar queries where possible. (See 2025-03-25_02-35-代码优化与SQL执行分析.md)
- Implement a common method for sending notifications (email, WeChat, WebSocket). (See 2025-03-25_02-35-代码优化与SQL执行分析.md)
- Create helper classes for common operations like adding comments and managing files. (See 2025-03-25_02-35-代码优化与SQL执行分析.md)
- Implement a `TaskStateManager` for handling task state changes, including validation, special handling, saving updates, and sending notifications. (See 2025-03-25_02-35-代码优化与SQL执行分析.md)
- Use transactional annotations for database operations to ensure data consistency. (See 2025-03-25_02-35-代码优化与SQL执行分析.md)
- Define constants for task states and notification types. (See 2025-03-25_02-35-代码优化与SQL执行分析.md)


## REFERENCE EXAMPLES
[Add links or examples of best practices and coding patterns here]

## PROJECT DOCUMENTATION & CONTEXT SYSTEM
- Documentation should follow a consistent structure (See `doc/开发文档`).
- Version updates should be recorded in this file and the relevant sections.  OkHttp 4.12.0 replaces Jsoup for SSE handling (2025-03-30).

## DEBUGGING
- Use a consistent logging strategy (log4j is used in this project).
- [Add project-specific debugging tips and troubleshooting steps here]
- Consider using a debugging tool like [Suggest a debugging tool].

## FINAL DOs AND DON'Ts
- **DO:** Write clean, well-documented code.
- **DO:** Follow the workflow and release rules.
- **DO:** Use version control effectively (SVN is used in this project).
- **DON'T:** Commit broken code.
- **DON'T:** Hardcode values.
- **DON'T:** Ignore error messages.