<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" readonly="readonly" data-template='{{:data2}}{{:data13}}天{{:data14}}申请' value="${staffInfo.userName}${staffInfo.staffNo}的出差申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">出发城市</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" name="apply.data1"/>
					  			</td>
					  			<td class="required">到达城市</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">交通工具</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data3">
						  				<option value="">--</option>
						  				<option value="飞机">飞机</option>
						  				<option value="高铁">高铁 </option>
						  				<option value="汽车">汽车</option>
						  				<option value="自驾">自驾</option>
						  				<option value="无">无</option>
						  			</select>
					  			</td>
					  			<td class="required">类别</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data14">
						  				<option value="出差">出差</option>
						  				<option value="外勤">外勤 </option>
						  				<option value="居家办公">居家办公</option>
						  			</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">出差时间</td>
					  			<td style="width: 40%;">
					  				<input type="text" style="width: 40%;display: inline-block;" placeholder="启程日期"  onchange="calcDay()" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 09:00:00',minTime:'9:00',maxTime:'18:00',doubleCalendar:true,alwaysUseStartDate:true})" class="form-control input-sm Wdate" name="apply.data11"/>
					  				~ <input type="text" style="width: 40%;display: inline-block;" placeholder="返程日期" onchange="calcDay()" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 18:00:00',minTime:'9:00',maxTime:'18:00',doubleCalendar:true,alwaysUseStartDate:true})" class="form-control input-sm Wdate" name="apply.data12"/>
					  			</td>
					  			<td class="required">出差天数</td>
					  			<td>
					  				<input type="number" data-edit-node="考勤备案" data-rules="required" readonly="readonly" placeholder="自动计算"  class="form-control input-sm" name="apply.data13"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">项目名称</td>
					  			<td>
					  				<input name="apply.data9" type="hidden">
									<input type="text" data-rules="required" onclick="singleContract(this)" readonly="readonly" data-rules="required" name="apply.data8" class="form-control input-sm"/>
					  			</td>
					  			<td>陪同人员</td>
					  			<td>
					  				 <input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="apply.data10"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">出差事由</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>相关附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/yq-work/static/js/datediff.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideHistoryApply:true,success:function(data){
				Flow.initData();
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.initData = function(){
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var days = applyInfo.data13;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			
			if(nodeCode=='部门主管'){
				if(days>=4){
					params['nextNodeCode'] = '分管副总';
				}else{
					params['nextNodeCode'] = '考勤备案';
				}
			}else if(nodeCode=='分管副总'){
				if(days>7){
					params['nextNodeCode'] = '总裁';
				}else{
					params['nextNodeCode'] = '考勤备案';
				}
			}
			FlowCore.approveData = params;
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		
		function selctCallBack(){
			
		}
		
		function calcDay(){
			var start = $("[name='apply.data11']").val();
			var end = $("[name='apply.data12']").val();
			if(start&&end){
				if(end<=start){
					layer.msg('结束日期应该小于开始日期',{icon:2});
					$("[name='apply.data12']").val('');
					return;
				}
				var val = dateDiff.diffAllDay(start,end);
				$("[name='apply.data13']").val(val);
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>