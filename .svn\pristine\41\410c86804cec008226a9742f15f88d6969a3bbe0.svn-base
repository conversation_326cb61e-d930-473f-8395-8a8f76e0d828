<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="projectId" type="hidden" value="${param.projectId}"/>
			<div class="ibox">
			     <div class="ibox-title mb-15 clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-inbox"></span> 任务日志</h5>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">时间区间</span>	
							 <select name="day" onchange="loadData()" class="form-control input-sm">
		                    		<option value="7">7天内</option>
		                    		<option value="30">一月内</option>
		                    		<option value="60">2个月内</option>
		                    		<option value="90" selected="selected">3个月内</option>
		                    		<option value="180">近半年</option>
		                    		<option value="365">近1年</option>
		                    		<option value="730">近2年</option>
	                    	</select>
					     </div>
	          	     </div>
	                </div> 
					<div class="ibox-content">
						<fieldset class="content-title"><legend>操作日志</legend></fieldset>
					    <table class="layui-hide" id="list2"></table>
						<fieldset class="content-title"><legend>评论日志</legend></fieldset>
					    <table class="layui-hide" id="list3"></table>
						<fieldset class="content-title"><legend>查看日志</legend></fieldset>
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/html" id="bar3">
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">查看</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		var status='${param.status}';
		
		var list={
			init1:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.taskAllLookLog',
					autoFill:true,
					title:'阅读日志',
					id:'list',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							width:50,
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'LOOK_TIME',
							title: '查看时间',
							align:'center',
							width:160
						},{
						    field: 'USERNAME',
							title: '查看人',
							align:'center',
							width:80
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							width:90,
							templet:function(row){
								return taskStateLabel(row.TASK_STATE);
							}
						},{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:80,
							templet:function(row){
								return getUserName(row.CREATOR);
							}
						},{
						    field: 'ASSIGN_USER_ID',
							title: '处理人',
							align:'center',
							width:80,
							templet:function(row){
								return getUserName(row.ASSIGN_USER_ID);
							}
						},{
						    field: 'CREATE_TIME',
							title: '创建时间',
							width:160,
							align:'left'
						}
					]]}
				);
			},
			init2:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.taskAllOpLog',
					autoFill:true,
					title:'任务操作日志',
					id:'list2',
					cellMinWidth:100,
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							width:50,
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'OP_TIME',
							title: '操作时间',
							align:'center',
							width:160
						},{
						    field: 'CONTENT',
							title: '操作内容',
							align:'left'
						},{
						    field: 'USERNAME',
							title: '操作人',
							align:'center',
							width:80
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							width:90,
							templet:function(row){
								return taskStateLabel(row.TASK_STATE);
							}
						},{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:100,
							templet:function(row){
								return getUserName(row.CREATOR);
							}
						},{
						    field: 'CREATE_TIME',
							title: '创建时间',
							width:160,
							align:'center'
						}
					]]}
				);
			},
			init3:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.taskAllCommentLog',
					autoFill:true,
					title:'任务评论日志',
					id:'list3',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							width:50,
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							align:'left',
							event:'list.detail',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'COMMENT_TIME',
							title: '评论时间',
							align:'center',
							width:160
						},{
						    field: 'COMMENT_BY',
							title: '评论人',
							align:'center',
							width:80,
							templet:function(row){
								return getUserName(row.COMMENT_BY);
							}
						},{
						    field: 'CONTENT',
							title: '评论内容',
							align:'left'
						},{
						    field: 'TASK_STATE',
							title: '状态',
							align:'center',
							width:80,
							templet:function(row){
								return taskStateLabel(row.TASK_STATE);
							}
						},{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:80,
							templet:function(row){
								return getUserName(row.CREATOR);
							}
						},{
						    field: 'CREATE_TIME',
							title: '创建时间',
							width:160,
							align:'center'
						}
					]]}
				);
			},
			detail:function(data){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
			}
		}
		
		function loadData(){
			list.init1();
			list.init2();
			list.init3();
		}
		
		$(function(){
			loadData();
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>