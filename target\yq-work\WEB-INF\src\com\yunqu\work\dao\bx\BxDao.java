package com.yunqu.work.dao.bx;

import java.sql.SQLException;

import com.yunqu.work.base.FlowConstants;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.service.BxService;
import com.yunqu.work.utils.FlowUtils;

@WebObject(name = "BxDao")
public class BxDao extends AppDaoContext{
	
	@WebControl(name = "bxTitle",type = Types.TEXT)
	public JSONObject bxTitle() {
		return getText(getStaffInfo().getUserName()+getStaffInfo().getStaffNo()+"费用报销单");
	}
	
	@WebControl(name = "yqBxNo",type = Types.TEXT)
	public JSONObject yqBxNo() {
		String flowCode = param.getString("flowCode");
		if(FlowUtils.isZrFlow(flowCode)) {
			return getText(BxService.getService().getZrBxNo());
		}else {
			return getText(BxService.getService().getYqBxNo());
		}
	}
	
	@WebControl(name = "zrBxNo",type = Types.TEXT)
	public JSONObject zrBxNo() {
		return getJsonResult(BxService.getService().getZrBxNo());
	}

	@WebControl(name="selectDept",type=Types.PAGE)
	public JSONObject selectDept(){
		EasySQL sql = new EasySQL();
		sql.append("select t1.DEPT_ID,t2.FINANCE_DEPT_CODE,t1.DEPT_NAME,t2.TEMP_NAME from  "+Constants.DS_MAIN_NAME+".easi_dept t1 right join yq_flow_bx_dept t2 on t1.dept_id = t2.dept_id where 1=1");
		sql.append("and t2.dept_state = 0");
		if(!isSuperUser()&&!hasRole("HR_MGR")&&!hasRole("FINANCE_ROLE")) {
			sql.append(getStaffInfo().getRootId(),"and t1.root_id = ?");
		}
		sql.appendLike(param.getString("deptName"),"and t1.DEPT_NAME like ?");
		sql.append("ORDER BY t1.DEPT_CODE desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="selectFeeType",type=Types.PAGE)
	public JSONObject selectFeeType(){
		EasySQL sql = new EasySQL("select fee_name fee_name,fee_code from yq_flow_bx_fee_type where 1=1");
		sql.appendLike(param.getString("feeName"),"and fee_name like ?");
		sql.appendLike(param.getString("feeCode"),"and fee_code like ?");
		String selectType = param.getString("selectType");
		if("out".equals(selectType)) {
			sql.append(1,"and project_cost = ?");
		}else {
			sql.append(0,"and project_cost = ?");
		}
		sql.append("ORDER BY order_index");
		JSONObject result =  queryForList(sql.getSQL(), sql.getParams());
		
		return result;
	}
	
	
	@WebControl(name = "bxItemInfo",type = Types.RECORD)
	public JSONObject bxItemInfo() {
		String itemId = param.getString("itemId");
		if(StringUtils.isBlank(itemId)) {
			return getJsonResult(null);
		}
		EasySQL sql = getEasySQL("select * from yq_flow_bx_item where 1=1");
		sql.append(itemId,"and item_id = ?");
		JSONObject result = queryForRecord(sql.getSQL(),sql.getParams());
		try {
			String invoiceIdArray  = this.getQuery().queryForString("select group_concat(invoice_id) from yq_finance_bx_invoice where bx_item_id = ?", new Object[] {itemId});
			result.getJSONObject("data").put("invoiceIdArray", invoiceIdArray);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return result;
	}
	
	@WebControl(name = "bxItems",type = Types.TEMPLATE)
	public JSONObject bxItems() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from yq_flow_bx_item where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by item_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "ztBxItems",type = Types.TEMPLATE)
	public JSONObject ztBxItems() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONArray());
		}
		EasySQL sql = getEasySQL("select * from zr_flow_bx_item where 1=1");
		sql.append(businessId,"and business_id = ?");
		sql.append("order by item_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "allDept",type = Types.TEMPLATE)
	public JSONObject allDept() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT t1.dept_path_name d_name, t1.dept_name dd_name, t1.root_id, t1.dept_id d_id, t2.* FROM ( SELECT b1.* FROM "+Constants.DS_MAIN_NAME+".easi_dept b1 GROUP BY b1.DEPT_ID ) t1 ");
		sql.append(param.getIntValue("deptState"),"INNER JOIN ( SELECT b4.* FROM yq_flow_bx_dept b4 WHERE b4.dept_state = ? ");
		sql.appendLike(param.getString("deptName"),"and b4.dept_name like ?");
		sql.append(") t2 ");
		sql.append("ON t1.DEPT_ID = t2.dept_id ORDER BY t1.dept_name");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "myBudget",type = Types.RECORD)
	public JSONObject myBudget() {
		EasySQL sql = getEasySQL("select * from");
		return queryForRecord(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "budgetList",type = Types.PAGE)
	public JSONObject budgetList() {
		EasySQL sql = getEasySQL("select * from yq_flow_bx_budget where 1=1");
		sql.append(param.getString("budgetType"),"and budget_type = ?","0");
		sql.append(param.getString("yearId"),"and year_id = ?");
		sql.appendLike(param.getString("userName"),"and user_name like ?");
		sql.appendLike(param.getString("deptName"),"and dept_name like ?");
		sql.append("order by user_id,create_time");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name = "contactUnit",type = Types.PAGE)
	public JSONObject contactUnit() {
		EasySQL sql = getEasySQL("select * from yq_finance_contact_unit where 1=1");
		sql.appendLike(param.getString("unitName"),"and unit_name like ?");
		sql.appendLike(param.getString("unitNo"),"and unit_no like ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "contactUnitInfo",type = Types.RECORD)
	public JSONObject contactUnitInfo() {
		EasySQL sql = getEasySQL("select * from yq_finance_contact_unit where 1=1");
		sql.append(param.getString("id"),"and id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "flowList",type = Types.TEMPLATE)
	public JSONObject flowList() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t2.check_time,t4.bx_money");
		sql.append("from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("INNER JOIN yq_flow_bx_base t4 on t1.apply_id = t4.business_id");
		sql.append("where 1=1");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		sql.append(param.getString("payState"),"and t4.pay_state = ?");
		
		String applyState = param.getString("applyState");
		if("100".equals(applyState)) {
			sql.append(10,"and t1.apply_state >= ?");
		}else {
			sql.append(applyState,"and t1.apply_state = ?");
		}
		
		sql.append(param.getString("checkName"),"and t1.check_name = ?");
		sql.append(param.getString("applyName"),"and t1.apply_name = ?");
		sql.append(param.getString("payState"),"and t4.pay_state = ?");
		sql.append(param.getString("recorded"),"and t4.recorded = ?");
		sql.append(param.getString("paperState"),"and t4.paper_state = ?");
		sql.append(param.getString("checkResult"),"and t2.check_result = ?");
//		sql.append(param.getString("checkUserId"),"and t2.check_user_id = ?");
		sql.append("order by t1.apply_time desc,t1.apply_id,t2.get_time");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}


	@WebControl(name = "bxItemList",type = Types.LIST)
	public JSONObject bxItemList(){
		String timeType = param.getString("timeType");
		EasySQL sql = new EasySQL("SELECT t1.*,t3.recorded_date,t2.apply_id,t2.apply_name,t2.apply_by");
		sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id INNER JOIN yq_flow_bx_base t3 on t1.business_id = t3.business_id where 1=1");
		if("recorded_date".equals(timeType)){
			sql.append("and t3.recorded = 1");
		}else {
			sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
		}
		sql.appendLike(param.getString("contractName"),"and t1.contract_name like ?");
		sql.appendLike(param.getString("deptName"),"and t1.dept_name like ?");
		sql.appendLike(param.getString("feeType"),"and t1.fee_type like ?");
		sql.appendLike(param.getString("feeDesc"),"and t1.fee_desc like ?");
		sql.append(param.getString("startDate"),"and "+timeType+" >= ?");
		sql.append(param.getString("endDate"),"and "+timeType+" <= ?");
		sql.append(param.getString("bxBy"),"and t2.apply_by = ?");
		setOrderBy(sql, " order by "+timeType+" desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "bxItemInvoice",type = Types.LIST)
	public JSONObject bxItemInvoice(){
		String invoiceIdArray = param.getString("invoiceIdArray");
		if(StringUtils.isBlank(invoiceIdArray)) {
			return emptyPage();
		}
		EasySQL sql = new EasySQL();
		sql.append("select * from yq_finance_bx_invoice where 1=1");
		sql.appendIn(invoiceIdArray.split(","), "and invoice_id");
		return queryForList(sql.getSQL(), sql.getParams());
	}

	private void setOrderBy(EasySQL sql, String defaultOrder) {
		String sortName = param.getString("sortName");
		String sortType = param.getString("sortType");
		if (StringUtils.notBlank(sortName)) {
			sql.append("order by ").append(sortName).append(sortType);
		} else {
			sql.append(defaultOrder);
		}
	}
	
	
	
}
