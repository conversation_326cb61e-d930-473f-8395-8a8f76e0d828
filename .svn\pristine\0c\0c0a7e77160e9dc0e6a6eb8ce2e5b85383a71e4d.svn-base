package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.Calendar;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.utils.WebSocketUtils;
import com.yunqu.work.utils.WeekUtils;

public class JobExcute extends AppBaseService implements Job {

	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
			Calendar calendar=Calendar.getInstance();
			
			int month = calendar.get(Calendar.MONTH)+1;
			int week = calendar.get(Calendar.DAY_OF_WEEK);//周日 1，周一 2 周六是7
			int day = calendar.get(Calendar.DAY_OF_MONTH);
			int hour = calendar.get(Calendar.HOUR_OF_DAY);
			int minute = calendar.get(Calendar.MINUTE);
			
			getLogger().info("job>>month:"+month+",week:"+week+",day:"+day+",hour:"+hour);
			
		   if(minute==0) {
			if(hour%2==0&&hour<23&&hour>7){
				try {
					//更新任务创建部门ID
					this.getQuery().executeUpdate("update yq_task t1,"+Constants.DS_MAIN_NAME+".easi_dept t2 ,"+Constants.DS_MAIN_NAME+".easi_dept_user t3 set t1.dept_id=t2.DEPT_ID where t1.CREATOR=t3.USER_ID and t2.DEPT_ID=t3.DEPT_ID and t1.CREATOR=t3.USER_ID");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				try {
					//更新任务负责人部门ID
					this.getQuery().executeUpdate("update yq_task t1,"+Constants.DS_MAIN_NAME+".easi_dept t2 ,"+Constants.DS_MAIN_NAME+".easi_dept_user t3 set t1.assign_dept_id=t2.DEPT_ID where t1.assign_user_id=t3.USER_ID and t2.DEPT_ID=t3.DEPT_ID and t1.assign_user_id=t3.USER_ID");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				try {
					//更新项目所属负责人的部门
					this.getQuery().executeUpdate("update yq_project t1,"+Constants.DS_MAIN_NAME+".easi_dept t2 ,"+Constants.DS_MAIN_NAME+".easi_dept_user t3 set t1.dept_id=t2.DEPT_ID where t1.PO=t3.USER_ID and t2.DEPT_ID=t3.DEPT_ID and t1.PO=t3.USER_ID");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新合同的客户ID
					this.getQuery().executeUpdate("update yq_crm_customer t1,yq_project_contract t2 set t2.CUST_ID = t1.CUST_ID where t2.CUST_NO = t1.CUST_NO");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新任务的文件数
					this.getQuery().executeUpdate("update yq_task t1 set t1.file_count = (select count(1) from yq_files t2 where t2.FK_ID=t1.task_id) where month_id = ?",EasyCalendar.newInstance().getFullMonth());
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				try {
					//更新跟进记录的文件数
					this.getQuery().executeUpdate("update yq_crm_follow t1 set t1.file_count = (select count(1) from yq_files t2 where t2.FK_ID=t1.follow_id)");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新商机统计字段
					this.getQuery().executeUpdate("update yq_crm_business t1 set t1.follow_day = (select sum(follow_day) from yq_crm_follow t2 where t2.business_id = t1.BUSINESS_ID)");
					this.getQuery().executeUpdate("update yq_crm_business t1 set t1.follow_count = (select count(1) from yq_crm_follow t2 where t2.business_id = t1.BUSINESS_ID)");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新项目的任务数
					this.getQuery().executeUpdate("update yq_project t1 set t1.task_count = (select count(1) from yq_task t3 where t3.PROJECT_ID= t1.PROJECT_ID)");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				
				try {
					//更新登录时间
					this.getQuery().executeUpdate("update yq_user_info t1,"+Constants.DS_MAIN_NAME+".easi_user_login t2  set t1.last_login_time=t2.last_login_time where t1.user_id=t2.user_id");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				try {
					//更新基本信息
					this.getQuery().executeUpdate("update yq_user_info t1,"+Constants.DS_MAIN_NAME+".easi_user t2  set t1.email=t2.email,t1.open_id=t2.open_id,t1.dept_name=t2.depts,t1.head_img = t2.PIC_URL,t1.post=t2.data2 where t1.user_id=t2.user_id");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
				try {
					//更新部门信息
					this.getQuery().executeUpdate("update yq_user_info t1,"+Constants.DS_MAIN_NAME+".easi_dept_user t2  set t1.dept_id=t2.dept_id where t1.user_id=t2.user_id");
				} catch (SQLException e) {
					getLogger().error(null,e);
				}
			}
			if(!ServerContext.isLinux()){
				return;
			}
			
			if(hour==17) {
				String sql  = "select creator,begin_time,end_time,title,todo_desc from yq_todo where `status`=1 and date_id = DATE_FORMAT(DATE_ADD(NOW(),INTERVAL 1 DAY),'%Y%m%d')";
				WeeklyNoticeService.getService().noticeTodo(sql);
			}
			if(hour==8) {
				String sql  = "select creator,begin_time,end_time,title,todo_desc from yq_todo where `status`=1 and date_id = "+EasyCalendar.newInstance().getDateInt();
				WeeklyNoticeService.getService().noticeTodo(sql);
			}
			
			if(week==6&&hour==17){
				WeeklyNoticeService.getService().run(WeekUtils.getWeekNo());
			}
			if(week==2&&hour==9){
				WeeklyNoticeService.getService().run(WeekUtils.getWeekNo()-1);
			}
			if(hour==7){
				UpdateBookService.getService().run();
				OAService.getService().scanOaAcount();
			}
			if(hour==7||hour==13){
				OAContractSynService.getService().synData();
				OACustSynService.getService().synData();
			}
			if(hour==13||hour==20){
				ZendaoService.getService().run();
			}
			
			if(hour==10||hour==13||hour==20){
				WeeklyStatService.getService().run();
			   //XuanService.getService().run();
			}
			
			if(hour==9&&(week==2||week==4)){
				TaskNoticeService.getService().overtimeTask();
			}
			if((hour==9||hour==15)&&week==6){
				TaskNoticeService.getService().overtimeTask();
			}
			if(week==6&&hour==16){
				TaskNoticeService.getService().waitTask();
			}
			if(week==6&&hour==15){
				TaskNoticeService.getService().checkTask();
			}
			
			if(hour==10||hour==15) {
				try {
					this.getMainQuery().executeUpdate("update yq_staff_info t1,easi_user t2 set t1.account_state = t2.state where t1.staff_user_id = t2.USER_ID");
					
					this.getMainQuery().executeUpdate("update yq_staff_info t1,easi_dept_user t2 set t1.dept_id = t2.DEPT_ID where t1.staff_user_id = t2.USER_ID");
					
					this.getMainQuery().executeUpdate("update yq_staff_info t1 set t1.p_dept_id  = (select DEPT_ID from easi_dept t3 where t3.dept_code =  (select P_DEPT_CODE from easi_dept t2 where t1.dept_id = t2.dept_id))");
					
					this.getMainQuery().executeUpdate("update yq_staff_info t1 ,easi_dept t2 set t1.p_superior = t2.LEADER where t1.p_dept_id = t2.dept_id");
				
					this.getMainQuery().executeUpdate("update EASI_USER t1,easi_dept t2,easi_dept_user t3 set t1.DEPTS = t2.DEPT_NAME where t1.USER_ID = t3.USER_ID and t2.DEPT_ID = t3.DEPT_ID");
					
					this.getMainQuery().executeUpdate("update easi_user t1 set t1.ROLES = (select GROUP_CONCAT(t2.ROLE_NAME) from easi_role t2 ,easi_role_user t3 where t2.ROLE_ID = t3.ROLE_ID and t1.USER_ID = t3.USER_ID)");
				} catch (SQLException e) {
					this.getLogger().error(null,e);
				}
			}
		}
		   
		if(minute==30&&hour==9) {
			if(week!=0&&week!=1){
				MsgModel model=new MsgModel();
				model.setSenderType(2);
				model.setTitle("今日点餐提醒");
				model.setMsg("请及时点餐,如已点或不在公司请忽略此提醒.");
				WebSocketUtils.sendAllMessage(model);
		   }
		}
		
		
	}

}
