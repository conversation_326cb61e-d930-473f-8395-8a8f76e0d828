<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title></title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="paymentEditForm" class="form-horizontal" data-mars="ContractPaymentDao.record" autocomplete="off" data-mars-prefix="payment.">
        <input type="hidden" value="${param.paymentId}" name="payment.PAYMENT_ID"/>
        <input type="hidden" value="${param.contractId}" name="payment.CONTRACT_ID"/>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td width="160px" class="required">款项类型</td>
                <td >
                    <SELECT data-rules="required"  <c:if test="${!empty param.paymentId}"> disabled </c:if>  name="payment.PAYMENT_TYPE" data-mars="ContractPaymentDao.getPaymentType()" class="form-control" onchange="paymentEdit.updateTaxRate(this.value)">
                        <option value="" >请选择</option>
                    </SELECT>
                </td>
            </tr>
            <tr>
                <td class="required">税率%</td>
                <td>
                    <input readonly type="text" name="payment.TAX_RATE" class="form-control" placeholder="选择类型后自动填写">
                    <select data-rules="required" style="display: none" id="paymentTaxRate" data-mars="ContractPaymentDao.getPaymentRate()">
                        <option value="0"></option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="required">占合同总金额比例(%)</td>
                <td>
                    <input type="number" data-rules="required"
                        <c:if test="${param.isSelected == '1'}"> readonly </c:if>
                       step="1" min="0" max="100" name="payment.RATIO" class="form-control" oninput="paymentEdit.calcAmountNoTaxByRatio()" placeholder="填写后自动计算金额">
                </td>
            </tr>
            <tr>
                <td class="required">款项合同金额(含税)</td>
                <td>
                    <input type="number" data-rules="required"
                        <c:if test="${param.isSelected == '1'}"> readonly </c:if>
                        name="payment.AMOUNT_WITH_TAX" class="form-control" oninput="paymentEdit.calcAmountNoTaxByAmount()" placeholder="填写后自动计算比例和不含税金额">
                </td>
            </tr>
            <tr>
                <td class="required">款项合同金额(税后)</td>
                <td>
                    <input type="number" data-rules="required"
                        <c:if test="${param.isSelected == '1'}"> readonly </c:if>
                        name="payment.AMOUNT_NO_TAX" class="form-control" placeholder="填写合同金额(含税)后自动计算">
                </td>
            </tr>
            <tr>
                <td class="required">款项税金</td>
                <td>
                    <input type="number" data-rules="required"
                        <c:if test="${param.isSelected == '1'}"> readonly </c:if>
                        name="payment.TAX" class="form-control" placeholder="填写合同金额(含税)后自动计算">
                </td>
            </tr>
            <tr>
                <td>其中：集成费</td>
                <td>
                    <input type="number" name="payment.JICHENG_FEE" class="form-control" value="0" >
                </td>
            </tr>
            <tr>
                <td>其中：财务费用</td>
                <td>
                    <input type="number" name="payment.FINANCIAL_FEE" class="form-control" value="0" >
                </td>
            </tr>
            <tr>
                <td width="80px" class="required">已审核</td>
                <td >
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio"  value="1" name="payment.CHECKED_FLAG"><span>是</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="0" name="payment.CHECKED_FLAG"><span>否</span>
                    </label>
                </td>
            </tr>
            </tbody>
        </table>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="paymentEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("paymentEdit");

        paymentEdit.paymentId = '${param.paymentId}';
        paymentEdit.isNew = '${param.isNew}';
        paymentEdit.contractId = '${param.contractId}';
        paymentEdit.amount = '${param.amount}';

        $(function(){
            $("#paymentEditForm").render({success:function(result){

                }});
        });

        paymentEdit.ajaxSubmitForm = function(){
            if(form.validate("#paymentEditForm")){
                if(paymentEdit.isNew=='1'){
                    paymentEdit.insertData();
                }
                else if(paymentEdit.paymentId){
                    paymentEdit.updateData();
                }else{
                    paymentEdit.insertData();
                }
            };
        }

        paymentEdit.insertData = function() {
            var data = form.getJSONObject("#paymentEditForm");
            ajax.remoteCall("${ctxPath}/servlet/contractPayment?action=add",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadPaymentList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        paymentEdit.updateData = function() {
            var data = form.getJSONObject("#paymentEditForm");
            ajax.remoteCall("${ctxPath}/servlet/contractPayment?action=update",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadPaymentList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        paymentEdit.updateTaxRate = function(paymentType){
            $("#paymentTaxRate").val(paymentType);
            var taxRate = $("#paymentTaxRate option:selected").text();
            $("input[name='payment.TAX_RATE']").val(taxRate);
            paymentEdit.calcAmountNoTax();
        }

        paymentEdit.calcAmountNoTaxByRatio = function (){
            var ratio = parseFloat($("input[name='payment.RATIO']").val());
            var contractAmount = parseFloat(paymentEdit.amount);
            if(isNaN(ratio)|| ratio == ''){
                ratio = 0;
            }
            if(ratio > 100) {
                layer.alert("占合同总金额比例不能大于100%", {icon: 5});
                return;
            }
            var paymentAmount = contractAmount * ratio / 100;
            paymentAmount = paymentAmount.toFixed(2);
            $("input[name='payment.AMOUNT_WITH_TAX']").val(paymentAmount);
            paymentEdit.calcAmountNoTax();
        }

        paymentEdit.calcAmountNoTaxByAmount = function (){
            var paymentAmount = parseFloat($("input[name='payment.AMOUNT_WITH_TAX']").val());
            if(isNaN(paymentAmount)|| paymentAmount == ''){
                paymentAmount = 0;
            }
            var contractAmount = parseFloat(paymentEdit.amount);
            if(paymentAmount > contractAmount){
                layer.alert("款项金额不能大于合同金额",{icon: 5});
                return;
            }
            if(paymentAmount < 0){
                layer.alert("款项金额不能小于0",{icon: 5});
                return;
            }
            var ratio = paymentAmount * 100 / contractAmount;
            ratio = ratio.toFixed(2);
            $("input[name='payment.RATIO']").val(ratio);
            paymentEdit.calcAmountNoTax();
        }


        paymentEdit.calcAmountNoTax = function (){
            var amount = parseFloat($("input[name='payment.AMOUNT_WITH_TAX']").val());
            if(isNaN(amount)){
                amount = 0;
            }
            var taxRate = parseFloat($("input[name='payment.TAX_RATE']").val());
            if(isNaN(taxRate)){
                taxRate = 0;
            }
            var amountNoTax = amount * 100 / (100 + taxRate);
            var tax = amountNoTax * taxRate / 100;
            $("input[name='payment.AMOUNT_NO_TAX']").val(amountNoTax.toFixed(2));
            $("input[name='payment.TAX']").val(tax.toFixed(2));
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>