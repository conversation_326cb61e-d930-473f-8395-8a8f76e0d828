package com.yunqu.work.servlet.devops;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.HostModel;
/**
 * 服务管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/host/*")
public class HostServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		HostModel model=getModel(HostModel.class, "host");
		model.setPrimaryValues(RandomKit.uniqueStr());
		model.addCreateTime();
		model.setCreator(getUserId());
		try {
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		HostModel model=getModel(HostModel.class, "host");
		try {
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		HostModel model=getModel(HostModel.class, "host");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}





