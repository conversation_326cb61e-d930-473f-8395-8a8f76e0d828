<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>通知公告</title>
	<style>
		body{background-color: #f8f8f8;}
		.paper {
			padding:20px 30px;
			margin: 0 auto;
			position:relative;
			width:1000px;
			min-height: 800px;
			background:#fff;
			border:1px solid #eee;
			margin:10px;
			box-shadow:0 0 5px rgba(0,0,0,0.27),0 0 20px  rgba(0,0,0,.1) inset;
			padding-bottom: 50px;
		}
		.paper::after,.paper::before {
			content:'';
			position:absolute;
			bottom:6px;
			width:100px;
			height:1px;
			z-index:-1;
			box-shadow:0 2px 12px 5px rgba(0,0,0,.3);
		}
		.paper::after {
			left:4px;
			transform:rotate(-6deg);
		}
		.paper::before {
			right:4px;
			transform:rotate(6deg);
		}
		/* css注释：设置了浏览器宽度不小于1201px时 paper 显示1000px宽度 */ 
		@media screen and (min-width: 1201px) { 
			.paper {width: 1000px;margin: 0 auto;} 
		} 
		/* 设置了浏览器宽度不大于1200px时 paper 显示800px宽度 */ 
		@media screen and (max-width: 1200px) { 
			.paper {width: 800px;margin: 0 auto;} 
		} 
		#CONTENT img{max-width: 100%;height: auto;}
		#CONTENT{line-height: 26px;}
		
		#editForm .userName{
		    font-size: 1em;
		    color: rgba(0,0,0,.87);
		    font-weight: 500;
		}
		#editForm .createTime{
		    display: inline-block;
		    margin-left: .5em;
		    color: rgba(0,0,0,.4);
		    font-size: .875em;
		}
		.avatar{
			float: left;
			height: 3em;
			width: 3em;
			display: block;
		    margin: .2em 0 0;
		}
    	.avatar img{
		    display: inline-block;
		    height: 100%;
		    width: 100%;
		    border-radius: 100%;
		    overflow: hidden;
		    font-size: inherit;
		    vertical-align: middle;
		    -webkit-box-shadow: 0 0 1px rgba(0,0,0,.3);
		    box-shadow: 0 0 1px rgba(0,0,0,.3);
		}
		.b_content{
			margin: 10px 15px;
		}
		.d_content{
			margin-left: 4em;
			padding: 6px 0px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" class="paper" data-mars="RemindDao.record" autocomplete="off">
     		   			<input type="hidden" value="${param.type}" name="type"/>
     		  			<input type="hidden" value="${param.remindId}" name="remindId"/>
     		  			<input type="hidden" value="${param.remindId}" name="fkId"/>
     		  			
     		  			<div class="row">
     		  				<div style="padding: 15px;text-align: center;font-size: 20px;font-weight: bold;" id="TITLE"></div>
     		  			</div>
     		  			<div class="row">
     		  				<div style="padding: 5px;text-align: center;">
     		  					<i class="glyphicon glyphicon-user"></i> <span id="PUBLISH_BY"></span>
     		  					<i class="glyphicon glyphicon-time ml-20"></i> <span id="PUBLISH_TIME"></span>
     		  				</div>
     		  			</div>
     		  			<div class="row">
     		  				<div style="padding: 50px" id="CONTENT"></div>
     		  			</div>
	  					  <fieldset class="content-title history">
 								<legend>我来评论</legend>
					     </fieldset>
     		  			<div class="row">
     		  				<div style="width: 95%;margin: 0 auto">
     		  					<textarea placeholder="在此输入评论内容" id="comment" name="content" style="height: 100px;width: 100%;" class="form-control input-sm"></textarea>
     		  				</div>
     		  			</div>
						<div class="clearfix mt-10">
		                   <div class="btn-group pull-left">
								<a class="ib btn btn-xs btn-link" href="javascript:void(0)" onclick="loadCommentHistory();">评论动态</a>
								<a class="ib btn-xs btn-link layui-hide" href="javascript:void(0)" onclick="lookLog();">浏览动态</a>
							</div>
		                   	<button type="button" style="box-shadow: 0 4px 8px 0 rgba(31,93,234,.35);" class="btn btn-primary btn-sm  pull-right mr-10" onclick="addComment()"> <i class="glyphicon glyphicon-send"></i> 发布评论 </button>
				     </div>
  				     <div id="lookLog">
 					  	<table id="viewTable"></table>
 				    </div>
 					<div id="history"></div>
  				</form>
				<div style="height: 100px"></div>
						
		 <script id="template-comments" type="text/x-jsrender">
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{call:CREATOR fn='getUserName'}}</span> <span class="createTime">{{:CREATE_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{:CONTENT}}</div>{{if #parent.parent.data.currentUserId == CREATOR}}<a style class="btn btn-xs btn-link hover ib" href="javascript:void(0)" onclick="delComment('{{:COMMENT_ID}}',$(this))">删除</a>	{{/if}}
						</div>						
					</div>
				</div>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		
		Edit.remindId='${param.remindId}';
		
		$(function(){
			var remindType='${param.remindType}';
			if(remindType=='0'){
				$("#TITLE").hide()
			}
			$("#editForm").render({success:function(result){
				Edit.updateData();
				loadCommentHistory(1);
			}});
		});
		function loadCommentHistory(flag){
			$("#history").show();
			$("#lookLog").hide();
			ajax.remoteCall("${ctxPath}/webcall?action=CommonDao.commentsList",{fkId:Edit.remindId},function(result) { 
				if(result.data==null||result.data.length<=0){
					$("#editForm").find('#history').hide();
					//if(flag&&flag==1)lookLog();
				}else{
					result['currentUserId']=getCurrentUserId();
					var html=renderTpl("template-comments",result);
					$("#history").html(html);
				}
			});
		}
		function lookLog(){
			$("#lookLog").show();
			$("#history").hide();
			loadLookLog({tableId:'viewTable',formId:'editForm',fkId:Edit.remindId});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/remind?action=updateViewCount",data,function(result) { 
				
			},{loading:false});
		}
		var addComment = function() {
			var data = {content:$("#comment").val(),fkId:Edit.remindId};
			ajax.remoteCall("${ctxPath}/servlet/comment?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$("#comment").val("");
						loadCommentHistory();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function delComment(id,obj){
			ajax.remoteCall("${ctxPath}/servlet/comment?action=del",{commentId:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(obj).parents(".b_content").remove();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function loadHistory(){
			$("#editForm").initTable({
				mars:'CommonDao.commentsList',
				id:'history',
				page:false,
				data:{fkId:Edit.remindId},
				cols: [[
	             {
					title: '编号',
					type:'numbers',
					width:50
				 },{
				    field: 'CONTENT',
					title: '内容',
					align:'left',
				},{
				    field: 'CREATOR',
					title: '发表人',
					align:'left',
					width:120,
					templet:function(row){
						return getUserName(row.CREATOR);
					}
				},{
				    field: 'CREATE_TIME',
					title: '创建时间',
					width:120,
					align:'left'
				}
				]],done:function(res){
					var data=res.data;
					if(data==null||data.length<=0){
						$("#editForm").find('[lay-id="history"]').remove();
						$("#editForm").find('.history').remove();
					}
				}}
			);
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>