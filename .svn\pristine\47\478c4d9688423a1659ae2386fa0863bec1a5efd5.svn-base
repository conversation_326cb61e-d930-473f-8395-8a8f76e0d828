<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="DataEditForm" class="form-horizontal" data-mars="SupplierDao.record" autocomplete="off" data-mars-prefix="supplier.">
 		   		<input type="hidden" value="${param.supplierId}" name="supplierId"/>
 		   		<input type="hidden" value="${param.supplierId}" name="supplier.SUPPLIER_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
			                <td width="100px" class="required">供应商名称</td>
		                    <td colspan="3">
								<input type="text" name="supplier.NAME" class="form-control input-sm"/>
		                    </td>
			            </tr>
			            <tr>
		                    <td width="100px" class="required">供应商类型</td>
		                    <td>
								<select name="supplier.TYPE" class="form-control input-sm">
									<option value="0">标准供应商</option>
									<option value="1">员工供应商</option>
								</select>
		                    </td>
		                    <td width="100px" class="required">编号</td>
		                    <td style="width: 35%">
		                   		 <input data-rules="required" data-mars="SupplierDao.getSupplierNo" type="text" name="supplier.SUPPLIER_NO" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
		                    <td class="required">法人</td>
		                    <td>
		                    	 <input type="text" name="supplier.CORPORATION" class="form-control input-sm">
		                    </td>
		                    <td class="required">供应商地址</td>
		                    <td>
		                    	 <input type="text" name="supplier.ADDRESS" class="form-control input-sm">
		                    </td>
		                </tr>
		                 <tr>
		                    <td>联系人</td>
		                    <td>
								<input type="text" name="supplier.LINK_MAN" class="form-control input-sm"/>
		                    </td>
		                    <td>联系电话</td>
		                    <td>
		                   		 <input type="text" name="supplier.LINK_PHONE" class="form-control input-sm">
		                    </td>
			            </tr>
		                 <tr>
		                    <td class="required">开户行</td>
		                    <td>
								<input type="text" name="supplier.OPEN_BANK_NAME" class="form-control input-sm"/>
		                    </td>
		                    <td class="required">开户账号</td>
		                    <td>
		                   		 <input data-rules="required"  type="text" name="supplier.OPEN_BANK_ACCOUNT" class="form-control input-sm">
		                    </td>
			            </tr>
		                 <tr>
		                    <td class="required">收款行</td>
		                    <td>
								<input type="text" name="supplier.PAY_BANK_NAME" class="form-control input-sm"/>
		                    </td>
		                    <td class="required">收款账号</td>
		                    <td>
		                   		 <input data-rules="required"  type="text" name="supplier.PAY_BANK_ACCOUNT" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
			            	<td>备注</td>
			               	<td colspan="3">
	                           <textarea style="height: 80px;" class="form-control input-sm" name="supplier.REMARK"></textarea>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="DataEit.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("DataEit");
	    
		DataEit.supplierId='${param.supplierId}';
		
		var supplierId='${param.supplierId}';
		
		$(function(){
			$("#DataEditForm").render({success:function(result){

			}});  
		});
		
		DataEit.ajaxSubmitForm = function(){
			if(form.validate("#DataEditForm")){
				if(DataEit.supplierId){
					DataEit.updateData(); 
				}else{
					DataEit.insertData(); 
				}
			};
		}
		DataEit.insertData = function(flag) {
			var data = form.getJSONObject("#DataEditForm");
			ajax.remoteCall("${ctxPath}/servlet/supplier?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadDataList();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		DataEit.updateData = function(flag) {
			var data = form.getJSONObject("#DataEditForm");
			ajax.remoteCall("${ctxPath}/servlet/supplier?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadDataList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>