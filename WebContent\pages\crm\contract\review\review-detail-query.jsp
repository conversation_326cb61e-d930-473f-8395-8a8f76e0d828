<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>合同评审</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="approveAll" type="hidden" value="1">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">评审编号</span>	
							 <input type="text" name="applyNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 180px">
							 <span class="input-group-addon">合同名称</span>	
							 <input type="text" name="contractName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">签约单位</span>	
							 <select class="form-control input-sm" onchange="list.query();" name="signEnt">
		                    	<option value="">请选择</option>
		                    	<option value="yunqu">云趣</option>
		                    	<option value="zhongrong">中融</option>
		                    	<option value="pci">佳都</option>
		                     </select>
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">流程状态</span>	
							<select name="applyState" onchange="list.query();" class="form-control input-sm">
							  <option value="">请选择 </option>
							  <option data-class="label label-default" value="0">草稿</option>
							  <option data-class="label label-warning" value="1">作废</option>
							  <option data-class="label label-warning" value="5">已挂起</option>
							  <option data-class="label label-warning" value="10">待审批</option>
							  <option data-class="label label-info" selected="selected" value="20">审批中</option>
							  <option data-class="label label-danger" value="21">审批退回</option>
							  <option data-class="label label-success" value="30">已完成</option>
						   </select>	
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">评审状态</span>	
							 <select class="form-control input-sm" onchange="list.query();" name="checkResult">
		                    	<option value="">请选择</option>
		                    	<option value="0">未评审</option>
		                    	<option value="1">可行</option>
		                    	<option value="2">不可行</option>
		                     </select>
					     </div>
					     <div class="input-group input-group-sm" style="width: 160px">
							 <span class="input-group-addon">节点名称</span>	
							 <input type="text" name="checkNodeName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
				<script type="text/html" id="btnBar">
				   <button class="btn btn-sm btn-info" lay-event="list.urge">催办</button>
				   <button class="btn btn-sm btn-warning ml-5" lay-event="list.returnCheck">打回</button>
 			   </script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		$(function(){
			
			layui.config({
				  base: '${ctxPath}/static/module/tableMerge/'
			}).use('tableMerge'); //加载自定义模块
			
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
		var list={
			init:function(){
				layui.use(['tableMerge'],function(){
					var tableMerge  = layui.tableMerge;
					$("#searchForm").initTable({
						mars:'ReviewDao.list',
						height:'full-90',
						limit:50,
						cellMinWidth:80,
						toolbar:'#btnBar',
						rowEvent:'rowEvent',
						id:'list',
						cols: [[
						  {title:'',type:'radio'},
						  {title:'序号',type:'numbers'},
						  {
						    field: 'APPLY_NO',
							title: '评审编号',
							align:'left',
							merge:true,
							style:'color:#1E9FFF;cursor:pointer',
							event:'list.detail',
							width:120
						 },
						 {
						    field: 'CONTRACT_NAME',
							title: '合同名称',
							merge:true,
							align:'left'
						},{
							field:'REVIEW_NODE_NAME',
							title:'节点名称',
							width:100
						},{
							field:'CHECK_NAME',
							title:'经办人',
							width:120
						},{
							field:'CHECK_RESULT',
							title:'评审状态',
							width:90,
							templet:function(row){
								var val = row['CHECK_RESULT'];
								var json = {'0':'待评审','1':'<span class="label label-success">可行</span>','2':'<span class="label label-danger">不可行</span>'};
								return json[val]||'--';
							}
						},{
							field:'CHECK_DESC',
							title:'	审批描述',
							minWidth:100
						},{
						    field: 'CHECK_TIME',
							title: '评审时间',
							width:120,
							align:'center'
						}
					   ]],done:function(){
						   tableMerge.render(layTables['list'].config);
					   }}
					);
				});
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(row){
				var data = {businessId:row['APPLY_ID']};
				popup.openTab({id:'reviewDetail',title:'评审详情',url:'${ctxPath}/web/flow',data:data});
			},
			setting:function(){
				popup.openTab({id:'reviewNode',url:'${ctxPath}/pages/crm/contract/review/review-node.jsp',title:'合同评审部门'});
			},
			returnCheck:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
				var data = dataList[0];
				var businessId = data['APPLY_ID'];
			    var resultId = data['RESULT_ID'];
				layer.prompt({formType: 2,value: '',offset:'20px',title: '请输入退回原因',area: ['400px', '150px']}, function(msgContent, index, elem){
		    	 	layer.close(index);
		    	 	var json  = {businessId:businessId,resultId:resultId,flowContent:msgContent};
		    	  	ajax.remoteCall("${ctxPath}/servlet/contract/review?action=checkReturn",json,function(result) { 
						if(result.state == 1){
							var newData = $.extend({prevFollowContent:data['CHECK_DESC'],toUserId:data['CHECK_USER_ID'],toUserName:data['CHECK_NAME']},json);
							ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addComment",newData,function(rs) { 
								layer.msg(rs.msg,{time:800,icon:1},function(){
									list.query();
								});
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
		    	});
			}
		}
		
		list.urge = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    var checkResult = data['CHECK_RESULT'];
			if(checkResult=='0'){
			    var title = data['APPLY_TITLE'];
			    var businessId = data['APPLY_ID'];
			    var resultId = data['RESULT_ID'];
			    var staffInfo = getStaffInfo();
			    var content = '合同评审：'+title+'，已达你处，请尽快办理，谢谢！--'+staffInfo.userName;
			    layer.prompt({formType: 2,value: content,offset:'20px',title: '请输入催办信息',area: ['400px', '150px']}, function(msgContent, index, elem){
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addUrge",{resultId:resultId,businessId:businessId,msgContent:msgContent},function(result) { 
			    	 	layer.close(index);
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
		    	});
			}else{
				layer.msg('只有待办的流程才能催办。');
				return;
			}
	   }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>