<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>流程发起</title>
	<style>
		.layui-elem-field legend{font-size: 16px;}
		.btn{text-align: left;}
		.layui-icon{font-size: 12px;}
		.layui-field-title{margin-bottom: 30px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
<form id="flowForm">
	<div class="layui-row layui-col-space15">
	  <div class="layui-col-md12">
	    <div class="layui-panel">
	      	<div style="padding:10px 30px;" data-mars="FlowDao.categoryList" data-template="template-list"></div>
	    </div>   
	  </div>
	</div>    
</form>
		
<script id="template-list" type="text/x-jsrender">
			{{for data}}
				{{if P_FLOW_CODE =='0'}}
					<fieldset class="layui-elem-field layui-field-title">
						  <legend>{{:FLOW_NAME}}</legend>
						  <div class="layui-field-box">
						  		<div class="layui-row layui-col-space15">
									{{for #parent.parent.data}}
									 {{if #parent.parent.data.FLOW_CODE==P_FLOW_CODE}}
						  			   <div class="layui-col-md2">
										 <button class="btn btn-sm" onclick="applyFlow('{{:FLOW_CODE}}');" style="width: 85%;" type="button"> <i class="layui-icon layui-icon-addition"></i>{{:FLOW_NAME}}</button>	
						  			   </div>
									 {{/if}}
  									{{/for}}
						  		</div>
						  </div>
					</fieldset>
				{{/if}}
			{{/for}}
</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   	 $(function(){
   		 $('#flowForm').render();
   	 });
		
   	 function applyFlow(id){
   		ajax.remoteCall("${ctxPath}/servlet/flow?action=getFlow",{flowCode:id},function(result) { 
			if(result.state == 1){
				var data = result.data;
				var title = data.flowName;
				popup.openTab({id:id,url:'/yq-work/servlet/flow?id='+id,title:title});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
   	 }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>