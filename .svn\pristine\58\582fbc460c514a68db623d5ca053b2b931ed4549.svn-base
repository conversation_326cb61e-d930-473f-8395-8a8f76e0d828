<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>报销人员设置</title>
	<style>
		.select2-container .select2-selection--single{z-index: 9999!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form id = "searchForm"  name = "searchForm" onsubmit="return false;" class="form-inline" autocomplete="off">
			<div class="ibox">
				<div class="ibox-content">
				    <table class="table table-auto table-bordered table-hover table-condensed" id="tableHead">
                           <thead>
                        	  <tr>
                        	      <th>序号</th>
                        	      <th>部门</th>
                        	      <th>人数</th>
                        	      <th>部门主管</th>
						          <th>费用会计</th>
						          <th>中心副总</th>
						          <th>财务总监</th>
						          <th>董事长</th>
						          <th>会计主管</th>
						          <th>模板</th>
						          <th>部门编码(财务)</th>
						          <th>操作</th>
   						      </tr>
                           </thead>
                           <tbody id="dataList"  data-mars="BxDao.allDept" data-mars-reload="false" data-template="list-template">
                           </tbody>
                    </table>
                    <script id="list-template" type="text/x-jsrender">
								   {{for  data}}
										<tr>
											<td>{{:#index+1}}</td>
											<td>{{:D_NAME}}</td>
											<td>{{:USER_COUNT}}</td>  
											<td>
												<select class="form-control input-sm" data-value="{{:DEPT_MGR_ID}}" data-mars="CommonDao.userDict" name="DEPT_MGR_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
												<input name="" class="form-control input-sm hidden" onclick="singleUser(this)"/>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:ACCT_ID}}" data-mars="CommonDao.userDict" name="ACCT_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:CENTER_ID}}" data-mars="CommonDao.userDict" name="CENTER_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:CFO_ID}}" data-mars="CommonDao.userDict" name="CFO_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:CEO_ID}}" data-mars="CommonDao.userDict" name="CEO_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm" data-value="{{:ACCT_MGR_ID}}" data-mars="CommonDao.userDict" name="ACCT_MGR_ID_{{:D_ID}}">
		                    						<option value="">--</option>
		                    					</select>
											</td>
											<td>
												<select class="form-control input-sm tempName" data-value="{{:TEMP_NAME}}" name="TEMP_NAME_{{:D_ID}}" style="width:80px;">
													<option value="管理部门">管理部门</option>
													<option value="销售部门">销售部门</option>
													<option value="工程研发部门">工程研发部门</option>
												</select>
											</td>
											<td>
												<input class="form-control input-sm" value="{{:FINANCE_DEPT_CODE}}" name="FINANCE_DEPT_CODE_{{:D_ID}}" style="width:80px;"/>
											</td>
											<td>
                                                <a href="javascript:void(0)" onclick="Config.saveData('{{:D_ID}}','{{:DD_NAME}}','{{:D_CODE}}','{{:ROOT_ID}}')" > 提交</a>
                                            </td>                                       
									    </tr>
								    {{/for}}					         
				    </script>
				</div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		$(function(){
			$("#searchForm").render({success:function(){
				$("#searchForm").render({success:function(){
					$(".tempName").each(function(){
						 $(this).val($(this).data('value'));
					});
					 requreLib.setplugs('select2',function(){
						$("#searchForm select").select2({theme: "bootstrap",placeholder:''});
						$(".select2-container").css("width","100%");
				 	 });
				}});
			}});
		});
		
		jQuery.namespace("Config");
		
		Config.saveData = function(deptId,deptName,deptCode,rootId){
			var data = form.getJSONObject("#searchForm");
			data['deptId'] = deptId;
			data['deptName'] = deptName;
			data['deptCode'] = deptCode;
			data['rootId'] = rootId;
			ajax.remoteCall("/yq-work/servlet/bx/conf?action=savePeople",data,function(result) {
				if(result.state == 1){
					top.layer.msg(result.msg);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>