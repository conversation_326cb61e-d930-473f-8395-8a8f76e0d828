package com.yunqu.work.servlet.flow;

import java.io.File;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.apache.poi.ss.usermodel.IndexedColors;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.utils.NumberToCN;
@WebServlet("/servlet/bx/fun/*")
public class YqBxFunServlet extends BaseFlowServlet {
	
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForUpdateHzMoney() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		BigDecimal hzAmount = params.getBigDecimal("hzAmount");
		String businessId = params.getString("businessId");
		try {
			
			this.getQuery().executeUpdate("update yq_flow_bx_item set hz_amount = ? where item_id = ?",hzAmount,itemId);
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.hz_money = (select sum(t2.hz_amount) from yq_flow_bx_item t2 where t1.business_id = t2.business_id) where t1.business_id = ?",businessId);
			String num = this.getQuery().queryForString("select t1.hz_money - t1.reverse_money from yq_flow_bx_base t1 where t1.business_id = ?", businessId);
			
			String payMoneyDx = NumberToCN.number2CNMontrayUnit(new BigDecimal(num));
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.pay_money = ?,t1.pay_money_dx = ? where t1.business_id = ?",num,payMoneyDx,businessId);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateReverseMoney() {
		JSONObject params = getJSONObject();
		BigDecimal reverseMoney = params.getBigDecimal("reverseMoney");
		String businessId = params.getString("businessId");
		try {
			
			this.getQuery().executeUpdate("update yq_flow_bx_base set reverse_money = ? where business_id = ?",reverseMoney,businessId);
			String num = this.getQuery().queryForString("select t1.hz_money - t1.reverse_money from yq_flow_bx_base t1 where t1.business_id = ?", businessId);
			String payMoneyDx = NumberToCN.number2CNMontrayUnit(new BigDecimal(num));
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.pay_money = ?,t1.pay_money_dx = ? where t1.business_id = ?",num,payMoneyDx,businessId);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdatePayState() {
		JSONObject params = getJSONObject();
		String date = params.getString("date");
		JSONArray array = params.getJSONArray("array");
		try {
			for(int i=0;i<array.size();i++) {
				JSONObject row  = array.getJSONObject(i);
				EasySQL sql = new EasySQL();
				
				double payMoney = row.getDoubleValue("payMoney");
				double thisMoney = row.getDoubleValue("thisMoney");
				
				sql.append(payMoney==thisMoney?10:11,"update yq_flow_bx_base set pay_state = ?");// 0 流程中 1 待付款 10已付款 11部分付款
				sql.append(EasyDate.getCurrentTimeStampString(), ",pay_time = ?");
				sql.append(thisMoney,",has_pay_money = has_pay_money + ?");
				sql.append(date,",pay_date = ?");
				sql.append("where 1=1");
				sql.append(row.getString("id"),"and business_id = ?");
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateBxRecorded() {
		JSONObject params = getJSONObject();
		String date = params.getString("date");
		JSONArray array = params.getJSONArray("array");
		try {
			for(int i=0;i<array.size();i++) {
				JSONObject row  = array.getJSONObject(i);
				EasySQL sql = new EasySQL();
				sql.append(1,"update yq_flow_bx_base set recorded = ?");
				sql.append(EasyDate.getCurrentTimeStampString(), ",recorded_time = ?");
				sql.append(date,",recorded_date = ?");
				sql.append("where 1=1");
				sql.append(row.getString("id"),"and business_id = ?");
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	
	public void actionForExportBx(){
		String _data = getPara("data");
		JSONObject param = JSONObject.parseObject(_data);
		List<String> headers=new ArrayList<String>();
		try {
			EasySQL sql =  new EasySQL();
			sql.append("select  t1.apply_no,t1.apply_remark,t1.apply_name,t1.apply_time,");
			sql.append("t2.bx_money,t2.recorded_date,t2.pay_type,t2.pay_money,t2.reverse_money,t2.hz_money,t2.contact_code,t2.contact_unit,");
			sql.append("t3.* from yq_flow_apply t1,yq_flow_bx_base t2,yq_flow_bx_item t3 where t1.apply_id = t2.business_id and t2.business_id = t3.business_id");
			
			
			sql.append(param.getString("applyState")," and t1.apply_state = ?");
			sql.append(param.getString("applyName")," and t1.apply_name = ?");
			sql.append(param.getString("payState")," and t2.pay_state = ?");
			sql.append(param.getString("recorded")," and t2.recorded = ?");
			
			sql.append(param.getString("payBeginDate"),"and t2.pay_date >= ?");
			sql.append(param.getString("payEndDate"),"and t2.pay_date <= ?");
			sql.append(param.getString("beginDate"),"and t2.recorded_date >= ?");
			sql.append(param.getString("endDate"),"and t2.recorded_date <= ?");
			
			sql.append("ORDER BY t1.apply_end_time desc,t1.apply_time desc,t3.item_index");
			
			File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
			/**创建头部*/
			headers.add("入账日期");
			headers.add("申请人");
			headers.add("单号");
			headers.add("申请时间");
			headers.add("说明");
			headers.add("付款方式");
			headers.add("往来单位编号");
			headers.add("往来单位");
			headers.add("费用总计");
			headers.add("冲账金额");
			headers.add("应付金额");
			headers.add("序号");
			headers.add("费用日期");
			headers.add("费用类型(内部)");
			headers.add("费用类型");
			headers.add("费用说明");
			headers.add("发票类型");
			headers.add("金额");
			headers.add("核准金额");
			headers.add("部门");
			headers.add("项目号");
			headers.add("产品线");
			headers.add("税率(%)");
			headers.add("税金");
			headers.add("金额(不含税)");
			
			List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
			int x=0;
			for(String header:headers){
				ExcelHeaderStyle style=new ExcelHeaderStyle();
				style.setData(header);
				if(x<11) {
					style.setWidth(5000);
					style.setBackgroundColor(IndexedColors.BLUE_GREY.index);
				}else {
					style.setWidth(4000);
					style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
				}
				styles.add(style);
				x++;
			}
			/**数据***/
			List<List<String>> excelData=new ArrayList<List<String>>();
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> list = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			
			String gBusinessId = null;
			
			if(list!=null && list.size()>0){
				for (int i = 0; i < list.size(); i++) {
					JSONObject map = list.get(i);
					List<String> row=new ArrayList<String>();
					String businessId = map.getString("BUSINESS_ID");
					if(!businessId.equals(gBusinessId)||gBusinessId==null) {
						row.add(map.getString("RECORDED_DATE"));
						row.add(map.getString("APPLY_NAME"));
						row.add(map.getString("APPLY_NO"));
						row.add(map.getString("APPLY_TIME"));
						row.add(map.getString("APPLY_REMARK"));
						row.add(map.getString("PAY_TYPE"));
						row.add(map.getString("CONTACT_CODE"));
						row.add(map.getString("CONTACT_UNIT"));
						row.add(map.getString("HZ_MONEY"));
						row.add(map.getString("REVERSE_MONEY"));
						row.add(map.getString("PAY_MONEY"));
					}else {
						for(int y = 0;y<11;y++) {
							row.add("");
						}
					}
					gBusinessId = businessId;
					
					
					row.add(map.getString("ITEM_INDEX"));
					row.add(map.getString("BX_DATE"));
					row.add(map.getString("FEE_IN_TYPE"));
					row.add(map.getString("FEE_TYPE"));
					row.add(map.getString("FEE_DESC"));
					row.add(map.getString("INVOICE_TYPE"));
					row.add(map.getString("AMOUNT"));
					row.add(map.getString("HZ_AMOUNT"));
					row.add(map.getString("DEPT_NAME"));
					row.add(map.getString("CONTRACT_NO"));
					row.add(map.getString("PRODUCT_LINE"));
					row.add(map.getString("TAX_RATE"));
					row.add(map.getString("TAXES"));
					row.add(map.getString("R_AMOUNT"));
					
					
					excelData.add(row);
				}
			}
			
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			String fileName="云趣费用报销清单.xlsx";
			renderFile(file,fileName,true);
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
	}
}
