<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>通知公告</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <span class="mr-50" id="title">公告</span>
	          		     <div class="input-group input-group-sm">
							<span class="input-group-addon">类别</span>	
							<select name="type" class="form-control input-sm" onchange="list.query();">
			            		<option value="">请选择</option>
			            		<option value="1">日常公告</option>
			            		<option value="2">新闻动态</option>
			            		<option value="3">每周菜单</option>
			            		<option value="0">分享</option>
			            		<!-- <option value="4">人事调动</option>
			            		<option value="5">招聘信息</option> -->
		            	     </select>
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">标题</span>	
							 <input type="text" name="title" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right" id="pubBtn">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 发布</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/html" id="bar">
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>
  			<a class="layui-btn layui-btn-xs" lay-event="list.notice">提醒</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		var type="${param.type}";
		$(function(){
			list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'RemindDao.list',
					height:'full-90',
					limit:30,
					cols: [[
		             {
						type:'numbers'
					 },
					 {
						field:'REMIND_TYPE',
						title:'类型',
						width:80,
						templet:function(row){
							return getText(row['REMIND_TYPE'],'type');
						}
					},{
					    field: 'TITLE',
						title: '标题',
						align:'left',
						style:'color:#337ab7;cursor:pointer;',
						event:'list.detail',
					},{
					    field: 'PUBLISH_BY',
						title: '发布人',
						width:120,
						align:'left'
					},{
					    field: 'STATUS',
						title: '状态',
						width:120,
						align:'center',
						templet:function(row){
							var json={0:'已发布',1:'草稿',2:'<font color="red">已删除</font>'};
							return json[row['STATUS']]||'';
						}
					},{
					    field: 'CREATOR',
						title: '创建人',
						width:120,
						align:'left',
						templet:function(row){
							return getUserName(row.CREATOR);
						}
					},{
					    field: 'PUBLISH_TIME',
						title: '发布时间',
						width:180,
						align:'left'
					},{
					    field: 'VIEW_COUNT',
						title: '阅读量',
						width:120,
						align:'left'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						width:180,
						align:'left'
					},{
						title: '操作',
						width:120,
						align:'left',
						toolbar: '#bar'
					}
					]],done:function(){
						table.resize({id:'list'});
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				popup.layerShow({type:1,full:true,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/remind/remind-edit.jsp',title:'编辑',data:{remindId:data.REMIND_ID,type:type}});
			},
			add:function(){
				popup.layerShow({type:1,full:true,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/remind/remind-edit.jsp',title:'新增',data:{type:type}});
			},
			detail:function(data){
				//popup.openTab({id:"remindDetail",url:'${ctxPath}/notice/'+data.REMIND_ID,title:'详情',data:{remindId:data.REMIND_ID,type:type}});
				window.open('${ctxPath}/notice/'+data.REMIND_ID);
				//popup.openTab({id:'remindDetail',url:'${ctxPath}/pages/remind/remind-detail.jsp',title:'详情',data:{remindId:data.REMIND_ID,type:type}});
			},
			notice:function(data){
				var json = {remindId:data.REMIND_ID,type:0};
				layer.confirm('请选择发送人',{title:'提醒',offset:'20px',icon:3,btn:['发给自己','全员发送'],yes:function(){
					doSend();
				},btn2:function(){
					json['type'] = 1;
					doSend();
				}});
				function doSend(){
					ajax.remoteCall("${ctxPath}/servlet/remind?action=notice",json,function(result) { 
						if(result.state == 1){
							layer.msg("操作成功！",{icon:1,time:1200},function(){
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>