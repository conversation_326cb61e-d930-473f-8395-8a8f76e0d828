<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectDictForm">
              <div class="ibox">
              		<table id="dictList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectDict.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectDict={
					sid:'${param.sid}',
					query : function(){
						$("#selectDictForm").queryData();
					},
					initTable : function(){
						$("#selectDictForm").initTable({
							mars:'EhrDao.dictList(${param.dictId})',
							id:'dictList',
							page:false,
							limit:20,
							rowEvent:'rowEvent2',
							rowDoubleEvent:'SelectDict.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'DICT_NAME',
								title: '名称'
							},{
							    field: 'DICT_ID',
								title: '编号'
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectDict.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('dictList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['DICT_NAME']);
									ids.push(data[index]['DICT_ID']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								popup.layerClose("selectDictForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
								popup.layerClose("selectDictForm");
							}
						}else{
							el.val(selectRow['DICT_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['DICT_ID']);
							}
							popup.layerClose("selectDictForm");
						}
					}
					
			};
			$(function(){
				SelectDict.initTable();
			});
			
			function rowEvent2(data,obj){
				var type = '${param.type}';
				if(type=='radio'){
					rowEvent(data,obj);
				}else{
					obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
					obj.tr.find('div[class="layui-unselect layui-form-checkbox"]').trigger("click");
				}
			}
			
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>