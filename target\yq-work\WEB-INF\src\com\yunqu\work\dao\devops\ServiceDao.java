package com.yunqu.work.dao.devops;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.ServiceModel;

@WebObject(name="ServiceDao")
public class ServiceDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		ServiceModel model=new ServiceModel();
		model.setColumns(getParam("service"));
		return queryForRecord(model);
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select service_id,service_name from YQ_OPS_SERVICE");
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL easySQL=getEasySQL("select * from YQ_OPS_SERVICE where 1=1");
		easySQL.appendLike(param.getString("serviceName"),"and SERVICE_NAME like ?");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}

}
