<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<SELECT style="display: none" name="paymentType" data-mars="ContractPaymentDao.getPaymentType"></SELECT>
<SELECT style="display: none" name="contractStage"
        data-mars="ContractStageDao.selectStage(${param.contractId})"></SELECT>
<SELECT style="display: none" name="paymentName"
        data-mars="ContractPaymentDao.selectPayment(${param.contractId})"></SELECT>
<SELECT style="display: none" name="incomeStageName"
        data-mars="IncomeStageDao.selectAllIncomeStage(${param.contractId})"></SELECT>
<span>说明<i class="layui-icon layui-icon-tips" lay-tips="收款状态由计划完成时间，和 实际完成日期(收款日期)得出。"></i></span>
<table id="stageTable"></table>

<script type="text/html" id="stageToolbar">
    <button class="btn btn-info btn-sm" onclick="contractStageList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-10" onclick="contractStageList.edit()" type="button">修改</button>
    <button class="btn btn-warning btn-sm ml-10" onclick="contractStageList.del()" type="button">删除</button>
    <button class="btn btn-default btn-sm ml-10" onclick="reloadStageList()" type="button">刷新</button>
</script>

<script type="text/javascript">

    var contractStageList = {
        init: function () {
            $("#ContractDetailForm").initTable({
                mars: 'ContractStageDao.stageList',
                id: 'stageTable',
                page: false,
                rowEvent: 'rowEvent',
                toolbar: '#stageToolbar',
                totalRow: true,
                autoSort: true,
                cols: [[
                    {
                        type: 'checkbox',
                        align: 'left',
                        totalRowText: "合计",
                    }, {
                        field: 'STAGE_NAME',
                        title: '阶段名称',
                        align: 'center',
                        minWidth: 100,
                        style:'color:#1E9FFF;cursor:pointer',
                        event:'contractStageList.detail',
                    },{
                        title: '收款状态',
                        width: 80,
                        align: 'center',
                        templet: function (d) {
                            return getStageCompleteStatus(d.PLAN_COMP_DATE, d.ACT_COMP_DATE,d.RCV_DATE);
                        }
                    }, {
                        field: 'IS_RECEIVE',
                        title: '是否应收',
                        align: 'center',
                        width: 70
                    }, {
                        field: 'RCV_DATE',
                        title: '应收日期',
                        width: 100
                    }, {
                        field: 'PLAN_COMP_DATE',
                        title: '计划完成日期',
                        width: 100
                    }, {
                        field: 'ACT_COMP_DATE',
                        title: '实际完成日期',
                        width: 100
                    }, {
                        field: 'RATIO',
                        title: '比例(%)',
                        width: 50,
                        totalRow: true
                    }, {
                        field: 'PLAN_RCV_AMT',
                        title: '计划收款金额',
                        width: 100,
                        totalRow: true,
                    }, {
                        field: 'KAIPIAO_AMT',
                        title: '已开票金额(含税)',
                        width: 120,
                    },
                    {
                        field: 'RECEIVED_AMT',
                        title: '已收款金额(含税)',
                        width: 120,
                    },
                    {
                        field: 'YK_WB_COND',
                        title: '余款维保条件',
                        minWidth: 100
                    }, {
                        field: 'YK_COND_DAYS',
                        title: '余款条件天数',
                        width: 90
                    }, {
                        field: 'BAD_DEBT_AMT',
                        title: '坏账金额',
                        width: 80,
                        totalRow: true,
                    }, {
                        field: 'UPDATE_BY',
                        title: '操作人',
                        width: 90
                    }, {
                        field: 'UPDATE_TIME',
                        title: '更新时间',
                        width: 140,
                    }
                ]],
                done: function (res) {
                    if (res.total != undefined) {
                        $("#contractStageCount").text("(" + res.total + ")");
                    }

                    //计算totalRatio
                    //直接用totalRow的值会因四舍五入得到99.99%
                    var divArr = $('#stageDiv .layui-table-total .layui-table-cell');
                    var ratioDiv = $(divArr[7]);
                    var totalAmount = $(divArr[8]).html();
                    var totalRatio = parseFloat(totalAmount) * 100 / contractInfo.AMOUNT;
                    ratioDiv.html(totalRatio.toFixed(2))
                }
            });
        },
        add: function () {
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                shadeClose: false,
                title: '新增阶段信息',
                offset: '20px',
                area: ['600px', '400px'],
                url: ctxPath + '/pages/crm/contract/include/stage-edit.jsp',
                data: {amount: contractInfo.AMOUNT, contractId: contractId, isNew: '1'}
            });
        },
        del: function () {
            var checkStatus = table.checkStatus('stageTable');
            if (checkStatus.data.length > 0) {
                var flag = false;
                for (var i = 0; i < checkStatus.data.length; i++) {
                    if (checkStatus.data[i].RECEIVED_AMT != "0.00") {
                        flag = true;
                    }
                }
                if (flag == true) {
                    layer.confirm('包含的合同阶段已收款，<br>请先删除对应的收款信息!', {icon: 3, offset: '120px'}, function () {
                        layer.closeAll();
                    })
                } else {
                    layer.confirm('确认要删除吗?', {icon: 3, offset: '120px'}, function () {
                        ajax.remoteCall("${ctxPath}/servlet/contractStage?action=BatchDel", checkStatus.data, function (result) {
                            if (result.state == 1) {
                                layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                                    layer.closeAll();
                                    reloadStageList();
                                });
                            } else {
                                layer.alert(result.msg, {icon: 5});
                            }
                        });
                    });
                }
            } else {
                layer.msg("请选择!");
            }
        },
        edit: function () {
            var checkStatus = table.checkStatus('stageTable');
            if (checkStatus.data.length > 1) {
                layer.msg('一次只能更新一个阶段', {icon: 7, time: 1000});
            } else if (checkStatus.data.length == 1) {
                var stageId = checkStatus.data[0]['STAGE_ID'];
                popup.layerShow({
                    type: 1,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    title: '更新阶段信息',
                    offset: '20px',
                    area: ['600px', '400px'],
                    url: ctxPath + '/pages/crm/contract/include/stage-edit.jsp',
                        data: {amount: contractInfo.AMOUNT, contractId: contractId, stageId: stageId, isNew: '0'}
                });
            } else {
                layer.msg("请选择!");
            }
        },
        detail: function (data){
            var  stageId = data.STAGE_ID;
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'合同阶段详情',offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/crm/contract/include/stage-detail.jsp',data:{contractId:contractId,stageId:stageId}});
        }
    }

    function reloadStageList() {
        $("#ContractDetailForm").queryData({id: 'stageTable', page: false});
        $("[name='contractStage']").render();
        reloadContractDetail();
    }



</script>
