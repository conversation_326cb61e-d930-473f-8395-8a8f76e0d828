﻿<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<!DOCTYPE>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>excel</title>
    <link rel="stylesheet" type="text/css" href="css/font-awesome.4.6.0.css">
    <link rel="stylesheet" type="text/css" href="css/main.css">
    <script type="text/javascript" src="js/jquery-2.0.3.js"></script>
    <script type="text/javascript" src="js/excel.js"></script>
    <script type="text/javascript" src="js/windowFun.js"></script>
    <script type="text/javascript" src="js/handAjax.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
    <link rel="stylesheet" href="css/excel.css" type="text/css"/>
    <script type="text/javascript">
    	var  excelId = '${param.excelId}';
        $(function () {
        	if(excelId){
        		var dataStr=JSON.stringify({params:{excelId:excelId},controls:['ExcelDao.excelInfo']});
        		   $.ajax({
 						url : "${ctxPath}/webcall",type : 'post',cache:false,dataType : 'json',contentType : "application/x-www-form-urlencoded; charset=UTF-8",
 						data : {data : dataStr},
 						success : function(result) {
 							layer.closeAll('loading');
 							var excelHtml = result['ExcelDao.excelInfo']['data']['EXCEL_CONTENT'];
 							var title = result['ExcelDao.excelInfo']['data']['TITLE'];
 							$(".excel").setExcelHtml(excelHtml);
 						},
 						error : function(jqXHR, textStatus, errorThrown) {
 							layer.closeAll('loading');
 							layer.alert("出现网络故障,请稍后再试!",{icon:7,time:10000});
 						},complete :function(){
 							layer.closeAll('loading');
 						},beforeSend :function(xhr){
 							
 						}
    				});
        	}else{
	            $(".excel").Excel(
	                {setting: {row: 50, col: 20}}
	            );
        	}
        });
        function saveExcel() {
        	if(excelId){
        		var t=$(".excel").getExcelHtml();
        		var dataStr=JSON.stringify({excelId:excelId,content:t});
        		$.ajax({
					url : '${ctxPath}/servlet/excel?action=update',type : 'post',async:false,cache:false,dataType : 'json',contentType : "application/x-www-form-urlencoded; charset=UTF-8",
					data : {data : dataStr},
					success : function(result) {
						layer.msg("保存成功");
					},error : function(jqXHR, textStatus, errorThrown) {
						layer.closeAll('loading');
						layer.alert("出现网络故障,请稍后再试!",{icon:7,time:15000});
					},complete :function(){
						layer.closeAll('loading');
					}});
        	}else{
        		layer.prompt({title:'excel标题',formType:0,offset:'30px'},function(value, index, elem){
       			    layer.close(index);
	        		var t=$(".excel").getExcelHtml();
	        		var dataStr=JSON.stringify({excelId:'',title:value,content:t});
	        		$.ajax({
						url : '${ctxPath}/servlet/excel?action=add',type : 'post',async:false,cache:false,dataType : 'json',contentType : "application/x-www-form-urlencoded; charset=UTF-8",
						data : {data : dataStr},
						success : function(result) {
							layer.msg("新增成功");
							excelId=result['data'];
						},error : function(jqXHR, textStatus, errorThrown) {
							layer.closeAll('loading');
							layer.alert("出现网络故障,请稍后再试!",{icon:7,time:15000});
						},complete :function(){
							layer.closeAll('loading');
					}});
        		});
        	}
        }
    </script>
</head>
<body>
<!--工具栏-->
<div class="toolbars">
    <div class="group common">
        <div class="body">
            <button type="button" class="tag-btn" onclick="saveExcel()">
                <i class="tag modif"></i> <span>保存</span>
            </button>
            <button type="button" class="tag-btn" onclick="saveExcel()">
                <i class="tag save"></i> <span>另存为</span>
            </button>
            <!--<button type="button" class="tag-btn">-->
                <!--<i class="tag view"></i> <span>预览</span>-->
            <!--</button>-->
           <!--  <button type="button" class="tag-btn" id="fileupload">
                <i class="tag upload"></i> <span>导入</span>
            </button> -->
        </div>
        <div class="foot">常用</div>
    </div>
    <div class="group font">
        <div class="body">
            <div class="sub sub-top">
                <select id="fontfamily" class="fontfamily">
                    <option value="微软雅黑" selected = "selected">微软雅黑</option>
                    <option value="SimHei">黑体</option>
                    <option value="SimSun">宋体</option>
                    <option value="NSimSun">新宋体</option>
                    <option value="FangSong">仿宋</option>
                    <option value="KaiTi">楷体</option>
                </select>
                <select id="fontsize" class="fontsize">
                    <option value="12px"  selected = "selected">12</option>
                    <option value="14px">14</option>
                    <option value="15px">15</option>
                    <option value="16px">16</option>
                    <option value="18px">18</option>
                    <option value="20px">20</option>
                    <option value="22px">22</option>
                    <option value="24px">24</option>
                    <option value="26px">26</option>
                    <option value="28px">28</option>
                    <option value="36px">36</option>
                    <option value="48px">48</option>
                    <option value="72px">72</option>
                </select>
            </div>
            <div class="sub sub-bottom">
                <button type="button" class="tag-btn btn-bold">
                    <i class="tag bold"></i>
                </button>
                <button type="button" class="tag-btn btn-italic">
                    <i class="tag italic"></i>
                </button>
                <button type="button" class="tag-btn btn-underline">
                    <i class="tag underline"></i>
                </button>
                <button type="button" class="tag-btn btn-strike">
                    <i class="tag strike"></i>
                </button>
                <button type="button" class="tag-btn" id="bgColor">
                    <i class="tag bgColor"></i>
                </button>
                <input type="color" id="bgColorSelect" style="display: none">
                <button type="button" class="tag-btn" id="fontColor">
                    <i class="tag fontColor"></i>
                </button>
                <input type="color" id="fontColorSelect" style="display: none">
            </div>
        </div>
        <div class="foot">字体</div>
    </div>
    <div class="group alignment">
        <div class="body">
            <div class="sub sub-left">
                <button type="button" class="tag-btn btn-htLeft btn-ah">
                    <i class="tag leftAlign"></i>
                </button>
                <button type="button" class="tag-btn btn-htCenter btn-ah">
                    <i class="tag levelAlign"></i>
                </button>
                <button type="button" class="tag-btn btn-htRight btn-ah">
                    <i class="tag rightAlign"></i>
                </button>
                <button type="button" class="tag-btn btn-htTop btn-av">
                    <i class="tag topAlign"></i>
                </button>
                <button type="button" class="tag-btn btn-htMiddle btn-av">
                    <i class="tag velAlign"></i>
                </button>
                <button type="button" class="tag-btn btn-htBottom btn-av">
                    <i class="tag bottomAlign"></i>
                </button>
            </div>
            <div class="sub sub-right">
                <button type="button" class="tag-btn merge-btn">
                    <i class="tag merge"></i> <span>合并单元格</span>
                </button>
                <button type="button" class="tag-btn split-btn">
                    <i class="tag split"></i> <span>拆分单元格</span>
                </button>
            </div>

        </div>
        <div class="foot">对齐方式</div>
        <a class="corner_mark"></a>
    </div>
    <div class="group cells"  style="padding: 0px 10px;">
        <div class="body">
            <div class="sub sub-right">
                <ul class="border-btn select-ctrl-border">
                    <li class="borderTop"><i class="tag btn-borderTop"></i></li>
                    <li class="borderRight"><i class="tag btn-borderRight"></i></li>
                    <li class="borderBottom"><i class="tag btn-borderBottom"></i></li>
                    <li class="borderLeft"><i class="tag btn-borderLeft"></i></li>
                    <li class="borderAll"><i class="tag btn-borderAll"></i></li>
                    <li class="whiteSpace"><i class="tag wrap"></i></li>
                    <li class="borderColor"><i class="tag borderColor"></i></i></li>
                    <input type="color" id="borderColor" style="display: none"/>
                    <li class="borderStyle" style="position: relative">
                        <i class="tag borderStyleIcon"></i>
                        <div class="selectBorderStyle">
                            <div class="borderSolid borderStyleOption">实线</div>
                            <div class="borderDashed borderStyleOption">虚线</div>
                            <div class="borderDouble borderStyleOption">双线</div>
                            <div class="borderNone borderStyleOption">无线</div>
                        </div>
                        <!--用来记录选择的样式-->
                        <select class="borderStyleOption" style="display: none">
                            <option value="solid" selected = "selected">实线</option>
                            <option value="dashed">虚线</option>
                            <option value="double">双线</option>
                            <option value="none">无线</option>
                        </select>
                    </li>
                </ul>
            </div>

        </div>
        <div class="foot">单元格</div>
        <a class="corner_mark"></a>
    </div>
    <div class="group edit">
        <div class="body">
            <div class="sub sub-left" style="width: 100px;">
                <div class="input-group">
                    <label>列宽</label> <input type="number" id="cell-width" class="cell-width">
                </div>
                <div class="input-group">
                    <label>行高</label> <input type="number" id="cell-height" class="cell-height">
                </div>
            </div>
        </div>
        <div class="foot">编辑</div>
    </div>
</div>
<!--输入框-->
<div style="width: 100%;display: flex;">
    <input type="text" id="selectTdValue" style="width: 100%;outline:none;">
</div>
<div class="excel" onselectstart="return false">
</div>
</body>
</html>

