<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>通知公告</title>
	<style>
		body{background-color: #f8f8f8;}
		.paper {
			padding:20px 30px;
			margin: 0 auto;
			position:relative;
			background:#fff;
			border:1px solid #eee;
			margin:10px;
			padding-bottom: 50px;
		}
		#CONTENT img{max-width: 100%;height: auto;}
		#CONTENT{line-height: 26px;}
		
		#editForm .userName{
		    font-size: 1em;
		    color: rgba(0,0,0,.87);
		    font-weight: 500;
		}
		#editForm .createTime{
		    display: inline-block;
		    margin-left: .5em;
		    color: rgba(0,0,0,.4);
		    font-size: .875em;
		}
		.avatar{
			float: left;
			height: 3em;
			width: 3em;
			display: block;
		    margin: .2em 0 0;
		}
    	.avatar img{
		    display: inline-block;
		    height: 100%;
		    width: 100%;
		    border-radius: 100%;
		    overflow: hidden;
		    font-size: inherit;
		    vertical-align: middle;
		    -webkit-box-shadow: 0 0 1px rgba(0,0,0,.3);
		    box-shadow: 0 0 1px rgba(0,0,0,.3);
		}
		.b_content{
			margin: 10px 15px;
		}
		.d_content{
			margin-left: 4em;
			padding: 6px 0px;
		}
		.title{color: #999;font-size: 12px;}
		._content{color: #444;font-size: 12px;}
		 #editForm pre{padding: 10px;margin: 10px 0px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" class="paper" data-mars="RemindDao.record" autocomplete="off">
     		   			<input type="hidden" value="${type}" name="type"/>
     		  			<input type="hidden" value="${remindId}" name="remindId"/>
     		  			<input type="hidden" value="${remindId}" name="fkId"/>
     		  			
     		  			<div class="row">
     		  				<div style="padding: 15px 30px;text-align: left;font-size: 25px;font-weight: 400;" id="TITLE"></div>
     		  			</div>
     		  			<div class="row">
     		  				<div style="padding: 5px 30px;text-align: left;">
     		  					<span class="title">发布人：</span> <span class="_content" id="PUBLISH_BY"></span>
     		  					<span class="title ml-20">发布时间：</span> <span class="_content" id="PUBLISH_TIME"></span>
     		  					<span class="title ml-20">浏览次数：</span> <span class="_content" id="VIEW_COUNT"></span>
     		  					<span class="title ml-20">评论次数：</span> <span class="_content" id="COMMENT_COUNT"></span>
     		  					<a class="ib btn-xs btn-link ml-20" href="javascript:void(0)" onclick="lookLog();">查看浏览记录</a>
     		  					
     		  				</div>
     		  			</div>
     		  			<hr>
     		  			<div class="row">
     		  				<div style="padding: 20px 30px" id="CONTENT"></div>
     		  			</div>
     		  			<div  class="row">
     		  				<div data-template="template-files" class="ml-20 mb-30" data-mars="FileDao.fileList"></div>
     		  			</div>
	  					  <fieldset class="content-title history">
 								<legend>我来评论</legend>
					     </fieldset>
     		  			<div class="row">
     		  				<div style="width: 95%;margin: 0 auto">
     		  					<textarea placeholder="在此输入评论内容" id="comment" name="content" style="height: 80px;width: 100%;" class="form-control input-sm"></textarea>
     		  				</div>
     		  			</div>
						<div class="clearfix mt-10">
		                    <div class="btn-group pull-left hidden">
								<a class="ib btn btn-xs btn-link" href="javascript:void(0)" onclick="loadCommentHistory();">评论动态</a>
							</div>
		                   	<button type="button" style="box-shadow: 0 4px 8px 0 rgba(31,93,234,.35);" class="btn btn-primary btn-sm  pull-right mr-10" onclick="addComment()"> <i class="glyphicon glyphicon-send"></i> 发布评论 </button>
				     </div>
 					<div id="history"></div>
  				</form>
				<div style="height: 100px"></div>
						
		 <script id="template-comments" type="text/x-jsrender">
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{call:CREATOR fn='getUserName'}}</span> <span class="createTime">{{:CREATE_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{:CONTENT}}</div>{{if #parent.parent.data.currentUserId == CREATOR}}<a style class="btn btn-xs btn-link hover ib" href="javascript:void(0)" onclick="delComment('{{:COMMENT_ID}}',$(this))">删除</a>	{{/if}}
						</div>						
					</div>
				</div>
			{{/for}}
		</script>
		<script id="template-files" type="text/x-jsrender">
			{{if data.length>0}}
				<div class="file-div"><a href="javascript:;" style="font-size:13px;" onclick="downloadLogLayer('${remindId}')">查看下载日志</a></div>
			{{/if}}
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}&view=online&watermark=1" title="点击查看" target="_blank"><span style="color:#00abfc;font-size:14px;">{{:FILE_NAME}}</span> <span style="color:#17a6f0">打开<span></a></div>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		
		Edit.remindId='${remindId}';
		
		$(function(){
			var remindType='${remindType}';
			if(remindType=='0'){
				$("#TITLE").hide()
			}
			$("#editForm").render({success:function(result){
				Edit.updateData();
				loadCommentHistory(1);
			}});
		});
		function loadCommentHistory(flag){
			$("#history").show();
			ajax.remoteCall("${ctxPath}/webcall?action=CommonDao.commentsList",{fkId:Edit.remindId},function(result) { 
				if(result.data==null||result.data.length<=0){
					$("#editForm").find('#history').hide();
				}else{
					result['currentUserId']=getCurrentUserId();
					var html=renderTpl("template-comments",result);
					$("#history").html(html);
				}
			});
		}
		function lookLog(){
			lookLogLayer(Edit.remindId);
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/remind?action=updateViewCount",data,function(result) { 
				
			},{loading:false});
		}
		var addComment = function() {
			var data = {content:$("#comment").val(),fkId:Edit.remindId,source:'remind'};
			ajax.remoteCall("${ctxPath}/servlet/comment?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$("#comment").val("");
						loadCommentHistory();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function delComment(id,obj){
			ajax.remoteCall("${ctxPath}/servlet/comment?action=del",{commentId:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(obj).parents(".b_content").remove();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function loadHistory(){
			$("#editForm").initTable({
				mars:'CommonDao.commentsList',
				id:'history',
				page:false,
				data:{fkId:Edit.remindId},
				cols: [[
	             {
					title: '编号',
					type:'numbers',
					width:50
				 },{
				    field: 'CONTENT',
					title: '内容',
					align:'left',
				},{
				    field: 'CREATOR',
					title: '发表人',
					align:'left',
					width:120,
					templet:function(row){
						return getUserName(row.CREATOR);
					}
				},{
				    field: 'CREATE_TIME',
					title: '创建时间',
					width:120,
					align:'left'
				}
				]],done:function(res){
					var data=res.data;
					if(data==null||data.length<=0){
						$("#editForm").find('[lay-id="history"]').remove();
						$("#editForm").find('.history').remove();
					}
				}}
			);
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>