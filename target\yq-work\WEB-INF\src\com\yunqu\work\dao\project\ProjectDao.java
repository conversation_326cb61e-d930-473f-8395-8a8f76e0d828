package com.yunqu.work.dao.project;

import java.sql.SQLException;
import java.time.LocalDate;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ProjectModel;

@WebObject(name="ProjectDao")
public class ProjectDao extends AppDaoContext {
	
	@WebControl(name="projectInfo",type=Types.RECORD)
	public JSONObject projectInfo(){
		String projectId = param.getString("projectId");
		String contractId = param.getString("contractId");
		try {
			if(StringUtils.isAllBlank(projectId,contractId)) {
				return getJsonResult(new JSONObject());
			}
			EasySQL sql = new EasySQL();
			sql.append("select * from yq_project where 1=1");
			sql.append(projectId,"and project_id = ?");
			sql.append(contractId,"and contract_id = ?");
			JSONObject result = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			return getJsonResult(result);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return getJsonResult(new JSONObject());
		}
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		ProjectModel model=new ProjectModel();
		model.setColumns(getParam("project"));
		if(StringUtils.notBlank(model.getProjectId())){
			JSONObject jsonObject = queryForRecord(model);
			return jsonObject;
		}else{
			return getJsonResult(new JSONObject());
		}
	}
	
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select PROJECT_ID,PROJECT_NAME from yq_project where is_delete = 0");
	}
	
	@WebControl(name="modules",type=Types.DICT)
	public JSONObject modules(){
		String projectId = param.getString("projectId");
		return getDictByQuery("select module_id,module_name from yq_project_module where project_id = ?",projectId);
	}
	
	@WebControl(name="myProjectDic",type=Types.DICT)
	public JSONObject myProjectDic(){
		return getDictByQuery("select t1.PROJECT_ID,t1.PROJECT_NAME from yq_project t1 INNER JOIN yq_project_team t2 on t2.PROJECT_ID=t1.PROJECT_ID where t2.USER_ID= ?",getUserId());
	}
	

	@WebControl(name="selectProjectList",type=Types.LIST)
	public JSONObject selectProjectList(){
		int dataType = param.getIntValue("dataType");
		EasySQL sql=getEasySQL("select t1.* from YQ_PROJECT t1 where 1=1");
		if(dataType==1) {
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,t1.po)");
			sql.append("or");
			sql.append(getUserId(),"find_in_set(?,t1.project_po)");
			sql.append(")");
		}else if(dataType==2){
			sql=getEasySQL("select t1.* from yq_project t1 INNER JOIN yq_project_team t2 ON t2.PROJECT_ID=t1.PROJECT_ID where 1=1");
			sql.append(getUserId(),"and t2.user_id=  ? ");
		}else if(dataType==3) {
			sql=getEasySQL("select t1.* from yq_project t1,yq_task t2 where t1.project_id = t2.project_id");
			sql.append(getUserId(),"and t2.assign_user_id =  ? ");
		}else if(dataType==4) {
			sql=getEasySQL("select t1.* from yq_project t1,yq_task t2 where t1.project_id = t2.project_id");
			sql.append(getUserId(),"and t2.creator = ? ");
		}else if(dataType==5) {
			sql=getEasySQL("select t1.* from yq_project t1,yq_weekly t2,yq_weekly_project t3 where t1.project_id = t3.project_id and t2.weekly_id = t3.weekly_id");
			sql.append(getUserId(),"and t2.creator = ? ");
		}
		sql.append("and t1.is_delete = 0");
//		sql.append(20,"and t1.project_state < ?");
		
		String projectName = param.getString("projectName");
		if(StringUtils.notBlank(projectName)) {
			sql.appendLike(projectName,"and (t1.project_name like ?");
			sql.appendLike(projectName,"or t1.project_no like ?)" );
		}
		
		if(dataType==3||dataType==4||dataType==5) {
			sql.append("group by t1.project_id");
			sql.append("order by t2.create_time desc");
		}else {
			sql.append("order by t1.last_task_time desc");
		}
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		int pageIndex = this.param.getIntValue("pageIndex");
		if(pageIndex==1&&dataType==0) {
			JSONObject other = new JSONObject();
			other.put("PROJECT_NAME","其他");
			other.put("PROJECT_NO","9999999999999");
			other.put("PROJECT_ID","9999999999999");
			result.getJSONArray("data").add(0, other);
		}
		return result;
	}
	
	/**
	 * 全部项目
	 * @return
	 */
	@WebControl(name="projectList",type=Types.LIST)
	public JSONObject projectList(){
			String source = param.getString("source");
			EasySQL sql = getEasySQL("select t1.*,");
			sql.append(10,"SUM(CASE WHEN t2.task_state = ? THEN 1 ELSE 0 END) AS a,");
			sql.append(20,"SUM(CASE WHEN t2.task_state = ? THEN 1 ELSE 0 END) AS b,");
			sql.append(30,"SUM(CASE WHEN t2.task_state = ? THEN 1 ELSE 0 END) AS c,");
			sql.append(30,"SUM(CASE WHEN t2.task_state < ? THEN 1 ELSE 0 END) AS d,");
			sql.append(30,"SUM(CASE WHEN t2.task_state >= ? THEN 1 ELSE 0 END) AS k,");
			sql.append(40,"SUM(CASE WHEN t2.task_state >= ? THEN 1 ELSE 0 END) AS f,");
			sql.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t2.deadline_at < ? and t2.task_state < 30  THEN 1 ELSE 0 END) AS g,");
			sql.append("SUM(CASE WHEN t2.task_state > 0 THEN 1 ELSE 0 END) AS h");
			
			sql.append("from yq_project t1 LEFT JOIN yq_task t2 on t1.project_id = t2.project_id  where 1=1");
			sql.append("and t1.is_delete = 0");
			sql.appendLike(param.getString("projectName"),"and t1.project_name like ?");
			
			String projectState = param.getString("projectState");
			String projectStates = param.getString("projectStates");
			if(StringUtils.notBlank(projectStates)) {
				sql.appendIn(projectStates.split(","),"and t1.project_state");
			}
			
			if(StringUtils.notBlank(projectState)) {
				sql.appendIn(projectState.split(","),"and t1.project_state");
			}
			
			String projectStage = param.getString("projectStage");
			if(StringUtils.notBlank(projectStage)) {
				sql.appendIn(projectStage.split(","),"and t1.project_stage");
			}
			
			String cyBeginDate = param.getString("cyBeginDate");
			String cyEndDate = param.getString("cyEndDate");
			if(StringUtils.notBlank(cyBeginDate)) {
				sql.append(cyBeginDate,"and t1.cy_date >= ?");
			}
			if(StringUtils.notBlank(cyEndDate)) {
				sql.append(cyEndDate,"and t1.cy_date <= ?");
			}
			
			
			String signBeginDate = param.getString("signBeginDate");
			String signEndDate = param.getString("signEndDate");
			if(StringUtils.notBlank(signBeginDate)) {
				sql.append(signBeginDate,"and t1.sign_date >= ?");
			}
			if(StringUtils.notBlank(signEndDate)) {
				sql.append(signEndDate,"and t1.sign_date <= ?");
			}
			
			String khBeginDate = param.getString("khBeginDate");
			String khEndDate = param.getString("khEndDate");
			if(StringUtils.notBlank(khBeginDate)) {
				sql.append(khBeginDate,"and t1.kh_date >= ?");
			}
			if(StringUtils.notBlank(khEndDate)) {
				sql.append(khEndDate,"and t1.kh_date <= ?");
			}
			
			String minPlanChangeCount = param.getString("minPlanChangeCount");
			String maxPlanChangeCount = param.getString("maxPlanChangeCount");
			if(StringUtils.notBlank(minPlanChangeCount)) {
				sql.append(minPlanChangeCount,"and t1.plan_change_count >= ?");
			}
			if(StringUtils.notBlank(maxPlanChangeCount)) {
				sql.append(maxPlanChangeCount,"and t1.plan_change_count <= ?");
			}
			
			String cyRealBeginDate = param.getString("cyRealBeginDate");
			String cyRealEndDate = param.getString("cyRealEndDate");
			if(StringUtils.notBlank(cyRealBeginDate)) {
				sql.append(cyRealBeginDate,"and t1.cy_real_date >= ?");
			}
			if(StringUtils.notBlank(cyRealEndDate)) {
				sql.append(cyRealEndDate,"and t1.cy_real_date <= ?");
			}
			
			String zyRealBeginDate = param.getString("zyRealBeginDate");
			String zyRealEndDate = param.getString("zyRealEndDate");
			if(StringUtils.notBlank(zyRealBeginDate)) {
				sql.append(zyRealBeginDate,"and t1.zy_real_date >= ?");
			}
			if(StringUtils.notBlank(zyRealEndDate)) {
				sql.append(zyRealEndDate,"and t1.zy_real_date <= ?");
			}

			
			String wbBeginDate = param.getString("wbBeginDate");
			String wbEndDate = param.getString("wbEndDate");
			if(StringUtils.notBlank(wbBeginDate)) {
				sql.append(wbBeginDate,"and t1.wb_end_date >= ?");
			}
			if(StringUtils.notBlank(wbEndDate)) {
				sql.append(wbEndDate,"and t1.wb_end_date <= ?");
			}
			
			String signDate = param.getString("signDate");
			if(StringUtils.notBlank(signDate)) {
				sql.appendRLike(signDate, "and sign_date like ?");
			}
			
			
			String zyBeginDate = param.getString("zyBeginDate");
			String zyEndDate = param.getString("zyEndDate");
			if(StringUtils.notBlank(zyBeginDate)) {
				sql.append(zyBeginDate,"and t1.zy_date >= ?");
			}
			if(StringUtils.notBlank(zyEndDate)) {
				sql.append(zyEndDate,"and t1.zy_date <= ?");
			}
			
			sql.appendLike(param.getString("pProjectName"),"and t1.p_project_name like ?");
			sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
			sql.appendLike(param.getString("po"),"and t1.PO_NAME like ?");
			sql.appendLike(param.getString("salesName"),"and t1.sales_by_name like ?");
			sql.appendLike(param.getString("preSalesName"),"and t1.pre_sales_name like ?");
			sql.appendLike(param.getString("projectPoName"),"and t1.project_po_name like ?");
			sql.append(param.getString("projectItem"),"and t1.project_item = ?");
			sql.append(param.getString("productLine"),"and t1.product_line = ?");
			sql.append(param.getString("isProjectAward"),"and t1.is_project_award = ?");
			String projectDeptName = param.getString("projectDeptName");
			if(StringUtils.notBlank(projectDeptName)) {
				sql.appendIn(projectDeptName.split(","),"and t1.project_dept_name");	
			}

			sql.append(param.getString("problemBy"),"and t1.problem_by = ?");
			sql.append(param.getString("projectType"),"and t1.project_type = ?");
			sql.append(param.getString("contractType"),"and t1.contract_type = ?");
			if(hasRole("SALES_ROLE")) {
				if(isDeptLeader()) {
					sql.append(getDeptId(),"and t1.SALES_BY in (SELECT user_id FROM yq_main.easi_dept_user WHERE dept_id = ?)");
				}else {
					sql.append(getUserId(),"and t1.SALES_BY = ?");
				}
			}
			else if("dept".equals(source)) {
//				sql.append(getDeptId(),"and t1.dept_id = ?");
				sql.append(getDeptId(),"and (FIND_IN_SET(t1.project_po, (SELECT GROUP_CONCAT(user_id) FROM yq_main.easi_dept_user WHERE dept_id = ?)) > 0");
				sql.append(getDeptId(),"OR FIND_IN_SET(t1.po, (SELECT GROUP_CONCAT(user_id) FROM yq_main.easi_dept_user WHERE dept_id = ?)) > 0)");
			}else {
				sql.append(param.getString("deptId"),"and t1.dept_id = ?");
				if(!isSuperUser()&&!hasRole("DEPT_MGR")&&!hasRole("PROJECT_MANAGER")){
					return emptyPage();
				}
			}
			sql.append("group by t1.project_id");
			String field = param.getString("field");
			String order = param.getString("order");
			if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
				sql.append("order by").append(field).append(order);
			}else{
//				sql.append("order by d desc,h desc");
				sql.append("order by t1.create_time desc");
			}
			return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="ysProjectList",type=Types.LIST)
	public JSONObject ysProjectList(){
		EasySQL sql = getEasySQL("select t1.*");
		sql.append("from yq_project t1 where 1=1");
		sql.append("and t1.is_delete = 0");
		sql.appendLike(param.getString("projectName"),"and t1.project_name like ?");
		
		String projectState = param.getString("projectState");
		String projectStates = param.getString("projectStates");
		if(StringUtils.notBlank(projectStates)) {
			sql.appendIn(projectStates.split(","),"and t1.project_state");
		}
		
		if(StringUtils.notBlank(projectState)) {
			sql.appendIn(projectState.split(","),"and t1.project_state");
		}
		
		String projectStage = param.getString("projectStage");
		if(StringUtils.notBlank(projectStage)) {
			sql.appendIn(projectStage.split(","),"and t1.project_stage");
		}
		
		String acceptFlag = param.getString("acceptFlag"); // "curr", "prev", "next"
		String beginDate = param.getString("acceptBeginDate");
		String endDate = param.getString("acceptEndDate");

		// 优先按标识生成时间范围
		if (StringUtils.isBlank(beginDate) || StringUtils.isBlank(endDate)) {
		    LocalDate now = LocalDate.now();
		    LocalDate firstDay, lastDay;

		    if ("curr".equalsIgnoreCase(acceptFlag)) {
		        firstDay = now.withDayOfMonth(1);
		        lastDay = now.withDayOfMonth(now.lengthOfMonth());
		    } else if ("prev".equalsIgnoreCase(acceptFlag)) {
		        LocalDate prevMonth = now.minusMonths(1);
		        firstDay = prevMonth.withDayOfMonth(1);
		        lastDay = prevMonth.withDayOfMonth(prevMonth.lengthOfMonth());
		    } else if ("next".equalsIgnoreCase(acceptFlag)) {
		        LocalDate nextMonth = now.plusMonths(1);
		        firstDay = nextMonth.withDayOfMonth(1);
		        lastDay = nextMonth.withDayOfMonth(nextMonth.lengthOfMonth());
		    } else {
		        firstDay = null;
		        lastDay = null;
		    }

		    if (firstDay != null && lastDay != null) {
		        beginDate = firstDay.toString(); // yyyy-MM-dd
		        endDate = lastDay.toString();    // yyyy-MM-dd
		    }
		}

		// 拼接SQL
		if (StringUtils.isNotBlank(beginDate) && StringUtils.isNotBlank(endDate)) {
		    sql.append(" AND (");
		    sql.append(beginDate, "t1.cy_date >= ?");
		    sql.append(endDate, " AND t1.cy_date <= ?");
		    sql.append(beginDate, " OR t1.zy_date >= ?");
		    sql.append(endDate, " AND t1.zy_date <= ?");
		    sql.append(")");
		}

		
		
		
		String signBeginDate = param.getString("signBeginDate");
		String signEndDate = param.getString("signEndDate");
		if(StringUtils.notBlank(signBeginDate)) {
			sql.append(signBeginDate,"and t1.sign_date >= ?");
		}
		if(StringUtils.notBlank(signEndDate)) {
			sql.append(signEndDate,"and t1.sign_date <= ?");
		}
		
		String khBeginDate = param.getString("khBeginDate");
		String khEndDate = param.getString("khEndDate");
		if(StringUtils.notBlank(khBeginDate)) {
			sql.append(khBeginDate,"and t1.kh_date >= ?");
		}
		if(StringUtils.notBlank(khEndDate)) {
			sql.append(khEndDate,"and t1.kh_date <= ?");
		}
		String cyRealBeginDate = param.getString("cyRealBeginDate");
		String cyRealEndDate = param.getString("cyRealEndDate");
		if(StringUtils.notBlank(cyRealBeginDate)) {
			sql.append(cyRealBeginDate,"and t1.cy_real_date >= ?");
		}
		if(StringUtils.notBlank(cyRealEndDate)) {
			sql.append(cyRealEndDate,"and t1.cy_real_date <= ?");
		}
		
		String zyRealBeginDate = param.getString("zyRealBeginDate");
		String zyRealEndDate = param.getString("zyRealEndDate");
		if(StringUtils.notBlank(zyRealBeginDate)) {
			sql.append(zyRealBeginDate,"and t1.zy_real_date >= ?");
		}
		if(StringUtils.notBlank(zyRealEndDate)) {
			sql.append(zyRealEndDate,"and t1.zy_real_date <= ?");
		}
		
		
		String wbBeginDate = param.getString("wbBeginDate");
		String wbEndDate = param.getString("wbEndDate");
		if(StringUtils.notBlank(wbBeginDate)) {
			sql.append(wbBeginDate,"and t1.wb_end_date >= ?");
		}
		if(StringUtils.notBlank(wbEndDate)) {
			sql.append(wbEndDate,"and t1.wb_end_date <= ?");
		}
		
		sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
		sql.appendLike(param.getString("po"),"and t1.PO_NAME like ?");
		sql.appendLike(param.getString("projectPoName"),"and t1.project_po_name like ?");
		String projectDeptName = param.getString("projectDeptName");
		if(StringUtils.notBlank(projectDeptName)) {
			sql.appendIn(projectDeptName.split(","),"and t1.project_dept_name");	
		}
		sql.append(param.getString("projectType"),"and t1.project_type = ?");
		sql.append(param.getString("contractType"),"and t1.contract_type = ?");

		sql.append("group by t1.project_id");
		String field = param.getString("field");
		String order = param.getString("order");
		if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
			sql.append("order by").append(field).append(order);
		}else{
			sql.append("order by t1.create_time desc");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
	/**
	 * 我创建的项目
	 * @return
	 */
	@WebControl(name="myCreateProjectList",type=Types.LIST)
	public JSONObject myCreateProjectList(){
		EasySQL sql=getEasySQL("select count(1) count,t1.* from yq_project t1 INNER JOIN yq_task t2 on t1.project_id=t2.project_id  where 1=1");
		sql.append("and t1.is_delete = 0");
		sql.append(getUserId(),"and t2.CREATOR =  ? ");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("GROUP BY t1.project_id");
		sql.append("order by count desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 我负责的项目
	 * @return
	 */
	@WebControl(name="myMangerProjectList",type=Types.LIST)
	public JSONObject myMangerProjectList(){
		EasySQL sql=getEasySQL("select t1.* from yq_project t1  where 1=1");
		sql.append("and t1.is_delete = 0 and");
		sql.append(getUserId(),"(t1.PO =  ? or");
		sql.append(getUserId()," t1.PROJECT_PO=  ? )");
		sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
		sql.appendLike(param.getString("projectName"),"and project_name like ?");
		sql.append(param.getString("projectState"),"and project_state = ?");
		sql.append("order by last_task_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 我关注的项目
	 * @return
	 */
	@WebControl(name="myFavoriteProject",type=Types.LIST)
	public JSONObject myFavoriteProject(){
		EasySQL sql=getEasySQL("select t1.*,t2.favorite_time from yq_project t1 INNER JOIN yq_favorite t2 on t2.fk_id=t1.PROJECT_ID where  1=1");
		sql.append(getUserId(),"and t2.favorite_by= ?");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.append("and t1.is_delete = 0");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("ORDER BY t2.favorite_time desc ");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 我参与的项目
	 * @return
	 */
	@WebControl(name="myJoinProjectList",type=Types.LIST)
	public JSONObject myProjectList(){
		EasySQL sql=getEasySQL("select t1.* from yq_project t1 INNER JOIN yq_project_team t2 ON t2.PROJECT_ID=t1.PROJECT_ID where 1=1");
		sql.append(getUserId(),"and t2.USER_ID=  ? ");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("and t1.is_delete = 0");
		sql.append("order by t1.CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目团队
	 * @return
	 */
	@WebControl(name="projectTeams",type=Types.LIST)
	public JSONObject projectTeams(){
		EasySQL sql=getEasySQL("select t2.USERNAME,t2.MOBILE,t2.EMAIL,t2.DEPTS,t3.LAST_LOGIN_TIME,t1.* from yq_project_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user_login t3 on t3.USER_ID=t2.USER_ID where 1 = 1");
		sql.append("and t2.STATE = 0");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",false);
		sql.append("order by t1.ADMIN_FLAG desc,t1.ACCESS_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目团队
	 * @return
	 */
	@WebControl(name="projectTeamsDict",type=Types.DICT)
	public JSONObject projectTeamsDict(){
		EasySQL sql=getEasySQL("select t2.USER_ID,t2.USERNAME from yq_project_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID where 1 = 1");
		sql.append("and t2.STATE = 0");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",false);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目周报
	 * @return
	 */
	@WebControl(name="projectWeeklys",type=Types.LIST)
	public JSONObject projectWeeklys(){
		EasySQL sql=getEasySQL("select t2.USERNAME,t2.MOBILE,t2.EMAIL,t2.DEPTS,t1.* from YQ_PROJECT_WEEKLY t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.CREATOR where 1 = 1");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",false);
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目状态更新日志
	 * @return
	 */
	@WebControl(name="projectFollowList",type=Types.LIST)
	public JSONObject projectFollowList(){
		EasySQL sql = getEasySQL("select * from yq_project_follow_record where 1 = 1 and is_public = 0 ");
		sql.append(param.getString("projectId")," and project_id = ?",false);
		sql.append("union");
		sql.append("select * from yq_project_follow_record where 1 = 1 and is_public = 1 ");
		sql.append(getUserId(),"and update_by = ?");
		sql.append(param.getString("projectId")," and project_id = ?",false);
		sql.append("order by update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectFollowQuery",type=Types.LIST)
	public JSONObject projectFollowQuery(){
		EasySQL sql = getEasySQL("select t1.*,t2.project_no,t2.po_name,t2.project_po_name,t2.sales_by_name from yq_project_follow_record t1,yq_project t2 where  t1.project_id = t2.project_id");
		sql.append(param.getString("followState")," and t1.follow_state = ?");
		sql.append(param.getString("hasDevWork")," and t1.has_dev_work = ?");
		sql.append(param.getString("followState")," and t1.feedback_type = ?");
		sql.append(param.getString("effectProgress")," and t1.effect_progress = ?");
		sql.append(param.getString("projectId")," and t1.project_id = ?");
		String problemBy = param.getString("problemBy");
		if(StringUtils.notBlank(problemBy)) {
			sql.append(problemBy,"and find_in_set(?,t1.problem_by)");
		}
		String beginDate = param.getString("beginDate");
		String endDate = param.getString("endDate");
		if(StringUtils.notBlank(beginDate)) {
			sql.append(beginDate,"and t1.follow_date >= ?");
		}
		if(StringUtils.notBlank(endDate)) {
			sql.append(endDate,"and t1.follow_date <= ?");
		}
		sql.appendLike(param.getString("projectName")," and t1.project_name like ?");
		sql.appendLike(param.getString("creatorName")," and t1.create_name like ?");
		sql.appendLike(param.getString("ownerName")," and t1.stakeholder like ?");
		sql.append("order by t1.update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 项目模块
	 * @return
	 */
	@WebControl(name="projectModule",type=Types.LIST)
	public JSONObject projectModule(){
		EasySQL sql=getEasySQL("select t1.* from yq_project_module t1  where 1 = 1");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",true);
		JSONObject result=queryForList(sql.getSQL(), sql.getParams());
		JSONArray array=result.getJSONArray("data");
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("MODULE_NAME", "点击这添加模块");
		array.add(jsonObject);
		result.put("data",array);
		return result;
	}
	

		
	@WebControl(name="fileListDetail",type=Types.TEMPLATE)
	public JSONObject fileListDetail(){
		EasySQL sql = new EasySQL("select  t2.project_name,t2.project_no,t1.* from yq_files t1,yq_project t2 where 1=1");
		sql.append("and t1.fk_id = t2.project_id");
		if(!hasRole("PROJECT_MANAGER")&&!isSuperUser()) {
			sql.append(getUserId(),"and t2.project_id in (select t3.PROJECT_ID from yq_project_team t3 where t3.PROJECT_ID = t2.PROJECT_ID and t3.USER_ID = ?)");
		}
		sql.append("project","and t1.source = ?");
		sql.appendLike(param.getString("projectNo"),"and t2.project_no like ? ");
		sql.appendLike(param.getString("projectName"),"and t2.project_name like ? ");
		sql.append(" order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectLink",type=Types.TEMPLATE)
	public JSONObject projectLink(){
		EasySQL sql=getEasySQL("select  t2.project_name,t2.project_no,t1.* from yq_project_link  t1,yq_project t2 where 1=1");
		sql.append("and t1.project_id = t2.project_id");
		if(!hasRole("PROJECT_MANAGER")&&!isSuperUser()) {
			sql.append(getUserId(),"and t2.project_id in (select t3.PROJECT_ID from yq_project_team t3 where t3.PROJECT_ID = t2.PROJECT_ID and t3.USER_ID = ?)");
		}
		sql.appendLike(param.getString("projectNo"),"and t2.project_no like ? ");
		sql.appendLike(param.getString("projectName"),"and t2.project_name like ? ");
		sql.append(0,"and state = ?");
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}	
		
	
	@WebControl(name="projectLog",type=Types.PAGE)
	public JSONObject projectLog(){
		EasySQL sql=getEasySQL("select * from yq_project_oplog where 1=1");
		sql.append(param.getString("projectId"),"and project_id = ? ");
		sql.append("order by op_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}	
	
		

}
