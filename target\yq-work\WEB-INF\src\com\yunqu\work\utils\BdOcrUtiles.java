package com.yunqu.work.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Random;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.StaffModel;
/**
 * https://console.bce.baidu.com/tools/#/api?product=AI&project=文字识别&parent=鉴权认证机制&api=oauth/2.0/token&method=post
 * <AUTHOR>
 *
 */
public class BdOcrUtiles extends AppBaseService{
	
	private static class Holder{
		private static BdOcrUtiles service=new BdOcrUtiles();
	}
	public static BdOcrUtiles getService(){
		return Holder.service;
	}
	
	private static List<String> clientIds = new ArrayList<String>();
	private static List<String> clientSecrets = new ArrayList<String>();
	private static List<String> tokens = new ArrayList<String>();
	private static List<Long> tokenExpires = new ArrayList<Long>();
	
	//默认30天
	private static Long tokenTime = null;
	
	static {
		//602932044
//		clientIds.add(0,"gaWQiSpnFrIQpBw1Vsyfc2nl");
//		clientSecrets.add(0,"xAjpEcuMWQQ1GL5n4EV6moE0oGwGx7au");
//		tokens.add(0,"");
		
		//464193096
//		clientIds.add(1,"jUa6zBo1jGqgYdik7aRBAz2Z");
//		clientSecrets.add(1,"yVvID1tabd4XO6rEWoyuvOEjwUUxu0Nc");
//		tokens.add(1,"");
		
		//dv
		clientIds.add(0,"Zxs2mg827i3xnWQyCQX3hg4t");
		clientSecrets.add(0,"M4gB1Il7ihZHNO9NLhoGIxOYQ1i8UDhZ");
		tokens.add(0,"");
	}
	
	public static void clearData() {
		tokens.clear();
		tokenExpires.clear();
		tokenTime = null;
		getToken();
	}
	
	private static Logger ocrLogger() {
		return LogEngine.getLogger("bx-invoice");//Logger.getLogger(BdOcrUtiles.class);
	}
	
	public static void getToken() {
		for(int index=0;index<clientIds.size();index++) {
			String url = "https://aip.baidubce.com/oauth/2.0/token";
			Connection connect = Jsoup.connect(url);
			try {
				connect.data("client_id", clientIds.get(index));
				connect.data("client_secret", clientSecrets.get(index));
				
				connect.data("grant_type", "client_credentials");
				connect.header("Content-Type", "application/json");
				connect.header("Accept", "application/json");
				Response execute = connect.ignoreContentType(true).timeout(5000).method(Method.POST).execute();
				String result = execute.body();
				ocrLogger().info("OCR_Token>>"+result, null);
				
				JSONObject object = JSONObject.parseObject(result);
				tokens.add(index,object.getString("access_token"));
				tokenExpires.add(index, object.getLongValue("expires_in"));
			} catch (Exception e) {
				ocrLogger().error(e.getMessage(), e);
			}
		}
		
	}
	
	public static String[] token() {
		if(tokenTime==null) {
			tokenTime = System.currentTimeMillis();
			getToken();
		}
		if(System.currentTimeMillis() - tokenTime>1000*60*60*24*10) {
			tokenTime = System.currentTimeMillis();
			getToken();
		}
		if(tokens.isEmpty()) {
			getToken();
		}
		
		int min = 0; // 最小值
        int max =  clientIds.size()-1; // 最大值
		Random random = new Random();
        int index = random.nextInt(max - min + 1) + min;
		return new String[] {tokens.get(0),clientIds.get(0)};
	}
	
	
	public EasyResult ocrApi(StaffModel staffModel,String invoiceCategory,String ocrReqId,String fileName,String fileUrl,String fsType) {
		File file = new File(fileUrl);
		if(file.exists()) {
			if(StringUtils.isBlank(fileName)) {
				fileName = file.getName();
			}
	        if ("trains".equals(invoiceCategory)||fileName.contains("铁路")) {
	        	EasyResult reqResult =  fpOcr(staffModel,"train_ticket",ocrReqId, fileUrl, fsType);
	        	if(reqResult.isOk()) {
	        		return reqResult;
	        	}
	        }
		}
		EasyResult reqResult = fpOcr(staffModel,"vat_invoice",ocrReqId, fileUrl, fsType);
		if(!reqResult.isOk()) {
    		return fpOcr(staffModel,"multiple_invoice",ocrReqId, fileUrl, fsType);
    	}
		return reqResult;
	}
	
	/**
	 * https://console.bce.baidu.com/tools/#/api?product=AI&project=文字识别&parent=财务票据OCR&api=rest/2.0/ocr/v1/vat_invoice&method=post
	 * @return
	 */
	public EasyResult fpOcr(StaffModel staffModel,String apiName,String ocrReqId,String fileUrl,String fsType) {
		String url = "https://aip.baidubce.com/rest/2.0/ocr/v1/"+apiName;
		Connection connect = Jsoup.connect(url);
		EasyResult reqResult = new EasyResult();
		try {
			String[] tokenInfo = token();
			
			connect.header("Content-Type", "application/x-www-form-urlencoded");
			connect.header("Accept", "application/json");
			connect.data("access_token",tokenInfo[0]);
			
			fsType = fsType.toLowerCase();
			if(fsType.equals(".pdf")) {
				connect.data("pdf_file",convertPdfToBase64(fileUrl));
			}else if(fsType.equals(".ofd")){
				connect.data("ofd_file",convertPdfToBase64(fileUrl));
			}else if(fsType.equals(".jpg")||fsType.equals(".jpeg")||fsType.equals(".png")||fsType.equals(".bmp")) {
				connect.data("image",imageToBase64(fileUrl));
			}
			long begin = System.currentTimeMillis();
			Response execute = connect.ignoreContentType(true).timeout(12000).method(Method.POST).execute();
			String result = execute.body();
			
			if(StringUtils.isBlank(ocrReqId)) {
				ocrReqId = RandomKit.uniqueStr();
			}
			EasyRecord record = new EasyRecord("yq_finance_ocr","id");
			record.set("id", RandomKit.uniqueStr());
			record.set("ocr_req_id", ocrReqId);
			record.set("client_id", tokenInfo[1]);
			if(staffModel!=null) {
				record.set("req_user_id", staffModel.getUserId());
				record.set("req_user_name", staffModel.getUserName());
			}
			record.set("req_time",EasyDate.getCurrentDateString());
			record.set("month_id",EasyCalendar.newInstance().getFullMonth());
			record.set("date_id",EasyCalendar.newInstance().getDateInt());
			record.set("file_url",fileUrl);
			record.set("result",result);
			record.set("excute_ts",System.currentTimeMillis() - begin);
			this.getQuery().save(record);
			
			ocrLogger().info(fileUrl+">>"+result, null);
			
			if(result.contains("error_msg")) {
				JSONObject row = JSONObject.parseObject(result);
				reqResult.addFail(row.getString("error_msg"));
				return reqResult;
			}
			reqResult.setData(result);
			return reqResult;
		} catch (Exception e) {
			ocrLogger().error(e.getMessage(), e);
			reqResult.addFail(e.getMessage());
		}
		return reqResult;
	}
	
	
	
	  public static String convertPdfToBase64(String filePath) throws IOException {
        File file = new File(filePath);
        byte[] bytes = new byte[(int) file.length()];
        
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            fileInputStream.read(bytes);
        }
        
        return Base64.getEncoder().encodeToString(bytes);
    }
	  
	  public static String imageToBase64(String imagePath){
	        String base64Image = "";
	        File file = new File(imagePath);
	        try (FileInputStream imageInFile = new FileInputStream(file)) {
	            // 读取图片文件并转换为字节数组
	            byte[] imageData = new byte[(int) file.length()];
	            imageInFile.read(imageData);
	            // 将字节数组编码为base64字符串
	            base64Image = Base64.getEncoder().encodeToString(imageData);
	        } catch (IOException e) {
	        	ocrLogger().error("无法读取文件 " + imagePath + ": " + e.getMessage(),e);
	        }
	        return base64Image;
    }
	  
	  
	public static void main(String[] args) {
		BdOcrUtiles.getToken();
//		System.out.println(BdOcrUtiles.getService().ocrApi(null,null,null,"E:\\temp\\202412\\24447000000438758537.pdf",".pdf"));
	}

}
