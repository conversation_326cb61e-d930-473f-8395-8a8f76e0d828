package com.yunqu.work.servlet.devops;

import java.io.File;
import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.VersionModel;
import com.yunqu.work.service.EmailService;
/**
 * 版本管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/version/*")
public class VersionServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		VersionModel model=getModel(VersionModel.class, "version");
		model.addCreateTime();
		model.setCreator(getUserPrincipal().getUserId());
		try {
//			String appId=model.getString("APP_ID");
//			AppModel appModel=new AppModel();
//			appModel.setPrimaryValues(appId);
//			appModel.set("last_version_name",model.getString("VERSION_NAME"));
//			appModel.set("last_version_time", EasyDate.getCurrentDateString());
//			appModel.set("last_publisher", getUserPrincipal().getUserId());
//			appModel.update();
			
			model.save();
			
			this.getQuery().executeUpdate("update yq_ops_version t1 set t1.file_desc = (select GROUP_CONCAT(t2.file_name) from yq_files t2 where t2.fk_id = ? and t1.version_id = t2.fk_id)",model.getPrimaryValue());
	
			
			JSONObject params = getJSONObject();
			JSONArray mailtos=params.getJSONArray("mailtos");
			String[] cc=null;
			if(mailtos!=null){
				cc=new String[mailtos.size()];
				for(int i=0;i<mailtos.size();i++){
					String userId=mailtos.getString(i);
					cc[i] = userId;
				}
				MessageModel messageModel=new MessageModel();
				messageModel.setData(model);
				messageModel.setReceivers(cc);
				messageModel.setCc(new String[]{getUserId()});
				messageModel.setFkId(model.getPrimaryValue().toString().toString());
				messageModel.setSender(getUserId());
				messageModel.setSendName(getUserName());
				messageModel.setTplName("newVersion.html");
				messageModel.setTitle(model.getString("VERSION_TITLE"));
				EmailService.getService().sendTaskEmail(messageModel);
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		VersionModel model=getModel(VersionModel.class, "version");
		try {
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelete(){
		VersionModel model=getModel(VersionModel.class, "version");
		try {
			model.delete();
			
			//删除附件
			
			String fkId = model.getPrimaryValue().toString();
			try {
				List<JSONObject> list = getQuery().queryForList("select * from yq_files where fk_id = ?", new Object[]{fkId}, new JSONMapperImpl());
				for(JSONObject jsonObject:list) {
					String id=jsonObject.getString("FILE_ID");
					String diskPath=jsonObject.getString("DISK_PATH");
					String basePath = getBaseDir();
					File file =  new File(basePath+diskPath);
					boolean fileNotExist = false;
					if(file==null||!file.exists()){
						fileNotExist = true;
						this.error("文件不存在",null);
					}
					boolean bl = file.delete();
					if(bl||fileNotExist) {
						this.getQuery().executeUpdate("delete from yq_file_download_log where file_id =  ?",id);
						this.getQuery().executeUpdate("delete from yq_look_log where fk_id =  ?",id);
						this.getQuery().executeUpdate("delete from yq_files where file_id =  ?",id);
					}
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}





