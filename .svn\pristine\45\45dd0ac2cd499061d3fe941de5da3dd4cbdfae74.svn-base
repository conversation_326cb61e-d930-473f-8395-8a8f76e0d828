<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>projectContract</title>
	<style>
		.form-horizontal{width: 100%;}
		.w-e-text-container{z-index: 0!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		  <form id="contractEditForm" class="form-horizontal" data-mars="ProjectContractDao.record" autocomplete="off" data-mars-prefix="contract.">
 		   		<input type="hidden" value="${param.projectId}" name="contract.PROJECT_ID"/>
 		   		<input type="hidden" value="${param.contractId}" name="contractId"/>
 		   		<input type="hidden" id="contractId" value="${param.contractId}" name="contract.CONTRACT_ID"/>
 		   		<div class="layui-tab layui-tab-brief">
				  <ul class="layui-tab-title">
				    <li class="layui-this">基础信息</li>
				    <li>付款信息</li>
				    <li>合同跟进</li>
				    <li>合同概算</li>
				  </ul>
				  <div class="layui-tab-content">
				    <div class="layui-tab-item layui-show">
				    	<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td width="80px" class="required">合同简称</td>
				                    <td style="width: 40%"><input lay-verify="required" onchange="getContractName();" placeholder="不带合同号" type="text" name="contract.CONTRACT_SIMPILE_NAME" class="form-control input-sm"></td>
				                    <td width="80px" class="required">合同编号</td>
				                    <td style="width: 40%">
				                    	<input onchange="getContractName();" lay-verify="required"  type="text" name="contract.CONTRACT_NO" class="form-control input-sm">
				                    </td>
					            </tr>
					            <tr>
				                    <td width="80px" class="required">合同名称</td>
				                    <td colspan="3"><input lay-verify="required" type="text" name="contract.CONTRACT_NAME" class="form-control input-sm"></td>
					            </tr>
					           <tr>
					           		<td>合同评审号</td>
				                    <td>
				                    	<input name="contract.REVIEW_APPLY_ID" type="hidden">
					                    <input type="text" placeholder="请点击选择" readonly="readonly" onclick="selectReview(this)" name="contract.REVIEW_ID"  class="form-control input-sm">
				                    </td>
				                    <td class="required">合同年限</td>
				                    <td>
				                    
					                    <input lay-verify="required" type="number" name="contract.YEAR"  class="form-control input-sm">
				                    </td>
					           </tr>
					           <tr>
				                    <td class="required">ERP合同号</td>
				                    <td>
					                    <input type="text" name="contract.ERP_CONTRACT_NO" lay-verify="required" class="form-control input-sm">
				                    </td>
				                    <td>客户方合同号</td>
				                    <td>
					                    <input type="text" name="contract.CUST_CONTRACT_NO" class="form-control input-sm">
				                    </td>
					           </tr>
					           <tr>
				                    <td class="required">我方签约单位</td>
				                    <td>
					                    <select name="contract.SIGN_ENT" lay-verify="required" class="form-control input-sm">
					                    	<option value="">请选择</option>
					                    	<option value="广州云趣">广州云趣</option>
					                    	<option value="中融德勤">中融德勤</option>
					                    	<option value="佳都科技">佳都科技</option>
					                    </select>
				                    </td>
				                    <td>提前执行日期</td>
				                    <td>
					                    <input type="text" name="contract.ADVANCE_EXCUTE_DATE" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="form-control input-sm Wdate">
				                    </td>
					           </tr>
					           <tr>
				                    <td>合同签订</td>
				                    <td>
					                     <label class="radio radio-info radio-inline" style="margin-top: 2px;">
				                          	<input type="radio" checked value="0" name="contract.SIGN_FLAG"><span>正式签订</span>
				                          </label>
				                          <label class="radio radio-info radio-inline">
				                          	<input type="radio" value="1" name="contract.SIGN_FLAG"><span>未正式签订</span>
				                          </label>
				                    </td>
				                    <td class="required">状态</td>
					            	<td>
					                     <label class="radio radio-info radio-inline" style="margin-top: 2px;">
				                          	<input type="radio" checked value="Y" name="contract.CONTRACT_STATE"><span>正常</span>
				                          </label>
				                          <label class="radio radio-info radio-inline">
				                          	<input type="radio" value="N" name="contract.CONTRACT_STATE"><span>结束</span>
				                          </label>
				                          <label class="radio radio-info radio-inline">
				                          	<input type="radio" value="Z" name="contract.CONTRACT_STATE"><span>资本化</span>
				                          </label>
				                    </td>
					           </tr>
					           <tr>
				                    <td>是否背靠背</td>
				                    <td>
					                     <label class="radio radio-info radio-inline" style="margin-top: 2px;">
				                          	<input type="radio" value="否" name="extend.BKB_PAY"><span>否</span>
				                          </label>
				                          <label class="radio radio-info radio-inline">
				                          	<input type="radio" value="是" name="extend.BKB_PAY"><span>是</span>
				                          </label>
				                    </td>
				                    <td>是否在建</td>
				                    <td>
				                          <label class="radio radio-info radio-inline">
				                          	<input type="radio" value="是" name="extend.IS_ACTIVE"><span>是</span>
				                          </label>
					                     <label class="radio radio-info radio-inline" style="margin-top: 2px;">
				                          	<input type="radio" value="否" name="extend.IS_ACTIVE"><span>否</span>
				                          </label>
				                    </td>
				               </tr>
					           <tr>
				                    <td>收入</td>
				                    <td>
				                         <label class="radio radio-info radio-inline">
				                          	<input type="radio" value="是" name="extend.HAS_MONEY"><span>是</span>
				                          </label>
					                     <label class="radio radio-info radio-inline" style="margin-top: 2px;">
				                          	<input type="radio" value="否" name="extend.HAS_MONEY"><span>否</span>
				                          </label>
				                    </td>
				                    <td>存量or新签</td>
				                    <td>
				                          <label class="radio radio-info radio-inline">
				                          	<input type="radio" value="存量" name="extend.SIGN_TYPE"><span>存量</span>
				                          </label>
					                     <label class="radio radio-info radio-inline" style="margin-top: 2px;">
				                          	<input type="radio" value="新签" name="extend.SIGN_TYPE"><span>新签</span>
				                          </label>
				                    </td>
				               </tr>
				               <tr>
								    <td>行业类型</td>
								    <td>
								        <input class="form-control input-sm" name="extend.INDUSTRY_TYPE">
								    </td>
								    <td>产品产出</td>
								    <td>
								        <input class="form-control input-sm" name="extend.SALE_PRODUCT">
								    </td>
								</tr>
					           <tr>
				                    <td class="required">客户名称</td>
				                    <td>
					                    <input type="hidden" name="contract.CUST_NO">
					                    <input type="hidden" name="contract.CUST_ID" id="custId">
					                    <input type="text" lay-verify="required" onclick="singleCust(this);" readonly="readonly" placeholder="请点击选择客户" name="contract.CUSTOMER_NAME"  class="form-control input-sm">
				                    </td>
				                    <td class="required">关联商机</td>
				                    <td>
					                    <input type="hidden" name="contract.SJ_ID">
					                    <input type="text" onclick="singleSj(this,$('#custId').val());" readonly="readonly" lay-verify="required" name="contract.SJ_NAME"  class="form-control input-sm">
				                    </td>
					           </tr>
					           <tr>
				                    <td>项目开始</td>
				                    <td>
					                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contract.START_DATE" class="form-control input-sm Wdate">
				                    </td>
				                    <td>项目结束</td>
				                    <td>
					                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" placeholder="警告:若项目未结束请勿填写此时间"  name="contract.END_DATE" class="form-control input-sm Wdate">
				                    </td>
					           </tr>
					            <tr>
				                    <td>签订日期</td>
				                    <td>
					                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contract.SIGN_DATE" class="form-control input-sm Wdate">
				                    </td>
				                    <td class="required">合同金额</td>
				                    <td>
					                    <input type="number" lay-verify="required" value="" name="contract.AMOUNT" class="form-control input-sm">
				                    </td>
				                </tr>
				                <tr>
				                    <td class="required">签约人</td>
				                    <td>
				                    	<input name="contract.SALES_BY" type="hidden"/>
				                    	<input class="form-control input-sm" lay-verify="required" readonly="readonly" onclick="multiUser(this)" name="extend.SALES_BY_NAME">
				                    </td>
				                    <td class="required">收款负责人</td>
				                    <td>
				                    	<input name="contract.PAYEE" type="hidden"/>
				                    	<input class="form-control input-sm" lay-verify="required" readonly="readonly" onclick="multiUser(this)" name="extend.PAYEE_NAME">
				                    </td>
					        	</tr>
					        	  <tr>
					        		<td>售前经理</td>
				                    <td>
				                    	<input name="contract.PM_BY" type="hidden"/>
				                    	<input class="form-control input-sm" readonly="readonly" onclick="multiUser(this)" name="extend.PM_BY_NAME">
				                    </td>
				                    <td class="required">营销大区</td>
				                    <td>
				                    	<input type="hidden" name="contract.SALE_DEPT_ID"/>
							  			<input name="extend.SALE_DEPT_NAME" lay-verify="required" readonly="readonly" data-dept-name="营销"  onclick="singleDept(this);" class="form-control">
				                    </td>
				                </tr>
					        	<tr>
				                    <td class="required">工程大区</td>
				                    <td>
				                    	<input type="hidden" name="contract.PROJECT_DEPT_ID"/>
							  			<input name="contract.PROJECT_DEPT_NAME" lay-verify="required" readonly="readonly" data-dept-name="工程"  onclick="singleDept(this);" class="form-control">
				                    </td>
				                    <td>项目阶段状态</td>
								    <td>
								    	<select class="form-control input-sm" name="extend.PROJECT_STAGE">
				                    		  <option value="">请选择</option>
				                    		  <option value="待初验">待初验</option>
											  <option value="待一次性验收">待一次性验收</option>
											  <option value="待终验">待终验</option>
											  <option value="已终验，维保在保">已终验，维保在保</option>
											  <option value="已终验，维保到期">已终验，维保到期</option>
											  <option value="维保到期">维保到期</option>
											  <option value="维保在保">维保在保</option>
											  <option value="在建合作运营">在建合作运营</option>
											  <option value="合作运营结项">合作运营结项</option>
											  <option value="异常终止">异常终止</option>
				                    	</select>
								    </td>
				                </tr>
					            <tr>
				                    <td class="required">产品线</td>
				                    <td>
					                    <input lay-verify="required" readonly="readonly" onclick="singleDict(this,'Y001')" name="contract.PROD_LINE"  class="form-control input-sm">
				                    </td>
				                    <td class="required">合同类型</td>
				                    <td>
				                    	<input lay-verify="required" readonly="readonly" onclick="singleDict(this,'Y004')" name="contract.CONTRACT_TYPE"  class="form-control input-sm">
				                    </td>
				                </tr>
					            <tr>
					            	<td>省份</td>
					               	<td>
			                           <input class="form-control input-sm" name="contract.SIGN_OFFICE">
					               	</td>
					            <!-- 	<td>约定保修记录</td>
					               	<td>
			                           <textarea style="height: 40px;" class="form-control input-sm" name="contract.WARRANTY_PERIOD"></textarea>
					               	</td> -->
					            </tr>
					            <tr>
					            	<td>合同描述</td>
					               	<td colspan="3">
			                           <textarea style="display: none;" id="textJson" class="form-control input-sm" name="contract.TEXT_JSON"></textarea>
			                           <textarea id="wordText" style="height: 120px;" class="form-control input-sm" name="contract.CONTRAC_DESC"></textarea>
					               	</td>
					            </tr>
					        </tbody>
		 				</table>
				    </div>
				    <div class="layui-tab-item">
				    	<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
								    <td style="width:15%">合同额</td>
								    <td style="width: 35%">
								        <input class="form-control input-sm" readonly="readonly" name="extend.A101">
								    </td>
								    <td style="width:15%">云趣合同原始总收入（不含税）---本固</td>
								    <td style="width: 35%">
								        <input class="form-control input-sm" name="extend.A102">
								    </td>
								</tr>
								<tr>
								    <td>云趣合同原始总收入（不含税）---本固</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A103">
								    </td>
								    <td>合同销售收入（软硬件+外购+自产软件费）</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A104">
								    </td>
								</tr>
								<tr>
								    <td>预付款</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A105">
								    </td>
								    <td>验齐货</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A106">
								    </td>
								</tr>
								<tr>
								    <td>系统上线款</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A107">
								    </td>
								    <td>初验款</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A108">
								    </td>
								</tr>
								<tr>
								    <td>分期款1</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A109">
								    </td>
								    <td>分期款2</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A110">
								    </td>
								</tr>
								<tr>
								    <td>分期款3</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A111">
								    </td>
								    <td>分期款4</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A112">
								    </td>
								</tr>
								<tr>
								    <td>分期款5</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A113">
								    </td>
								    <td>分期款6</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A114">
								    </td>
								</tr>
								<tr>
								    <td>终验款</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A115">
								    </td>
								    <td>竣工款</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A116">
								    </td>
								</tr>
								<tr>
								    <td>尾款（质保款）</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A117">
								    </td>
								    <td>已收款</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A118">
								    </td>
								</tr>
								<tr>
								    <td>已收款备注</td>
								    <td colspan="3">
								        <input class="form-control input-sm" name="extend.A119">
								    </td>
								</tr>
								<tr>
								    <td>剩款未收</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A120">
								    </td>
								    <td>实际可催收</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A121">
								    </td>
								</tr>
								<tr>
								    <td>未到收款节点金额</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A122">
								    </td>
								    <td>佳都转包到云趣合同额</td>
								    <td>
								        <input class="form-control input-sm" name="extend.A123">
								    </td>
								</tr>
					        </tbody>
					      </table>
				    </div>
				    <div class="layui-tab-item">
				    		<table class="table table-edit table-vzebra">
					        	<tbody>
						          <tr>
								    <td style="width:15%">计划初验时间或（维保开始时间）</td>
								    <td style="width: 35%">
								        <input class="form-control input-sm" name="extend.B101">
								    </td>
								    <td style="width:15%">实际初验时间</td>
								    <td style="width: 35%">
								        <input class="form-control input-sm" name="extend.B102">
								    </td>
								</tr>
								<tr>
								    <td>初验年份</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B103">
								    </td>
								    <td>计划终验时间或（维保结束时间）</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B104">
								    </td>
								</tr>
								<tr>
								    <td>实际终验时间或（维保结束时间）</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B105">
								    </td>
								    <td>终验年份</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B106">
								    </td>
								</tr>
								<tr>
								    <td>维保结束年份</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B107">
								    </td>
								    <td>合同约定保修记录</td>
								    <td>
								        <textarea class="form-control input-sm" name="extend.B108"></textarea>
								    </td>
								</tr>
								<tr>
								    <td>保修到期时间</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B109">
								    </td>
								    <td>是否已转维保</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B110">
								    </td>
								</tr>
								<tr>
								    <td>变更记录</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B111">
								    </td>
								    <td>变更原因</td>
								    <td>
								        <textarea class="form-control input-sm" name="extend.B112"></textarea>
								    </td>
								</tr>
								<tr>
								    <td>项目验收风险类型</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B113">
								    </td>
								    <td>项目当前阶段</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B114">
								    </td>
								</tr>
								<tr>
								    <td>是否挂起</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B115">
								    </td>
								    <td>是否背靠背</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B116">
								    </td>
								</tr>
								<tr>
								    <td>特殊项目情况说明</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B117">
								    </td>
								    <td>是否已实施完（时间）</td>
								    <td>
								        <input class="form-control input-sm" name="extend.B118">
								    </td>
								</tr>
								<tr>
								    <td>跟进记录</td>
								    <td colspan="3">
								        <textarea class="form-control input-sm" name="extend.B119"></textarea>
								    </td>
								</tr>
					         </tbody>
					      </table>	
				    </div>
				    <div class="layui-tab-item">
				    		啥字段？
				    </div>
				  </div>
				</div>
 		   		
				
			    <p class="layer-foot text-c" style="z-index: 999999999">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="Contract.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  	    </form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
   
		var Contract = {contractId:'${param.contractId}'}
	    
		var contractId = Contract.contractId;
		
		$(function(){
		    $('[data-toggle="tooltip"]').tooltip();
			$("#contractEditForm").render({success:function(result){
				var dataJsonStr = $('#textJson').val();
				if(dataJsonStr){
					var textJson = eval("("+dataJsonStr+")");
					fillRecord(textJson,"extend.",",","#contractEditForm");
				}
			}});  
		});
		Contract.ajaxSubmitForm = function(){
		   if(layui.form.validate('#contractEditForm')){
				if(Contract.contractId){
					Contract.updateData(); 
				}else{
					Contract.insertData(); 
				}
			};
		}
		Contract.insertData = function(flag) {
			var data = form.getJSONObject("#contractEditForm");
			data['contract.TEXT_JSON']= getTextVal(data);
			ajax.remoteCall("${ctxPath}/servlet/projectContract?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Contract.updateData = function(flag) {
			var data = form.getJSONObject("#contractEditForm");
			data['contract.TEXT_JSON']= getTextVal(data);
			ajax.remoteCall("${ctxPath}/servlet/projectContract?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function selectReview(el){
			var id = new Date().getTime();
			$(el).attr('data-sid',id);
			popup.layerShow({id:'selectCust',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择评审',url:'/yq-work/pages/flow/select/select-review.jsp',data:{sid:id,type:'radio'}});
		}
		
		function selectReviewCallBack(row){
			$("[name='contract.REVIEW_ID']").val(row['APPLY_NO']);
			
			$("[name='contract.CONTRACT_SIMPILE_NAME']").val(row['CONTRACT_NAME']);
			$("[name='contract.CONTRACT_NAME']").val(row['CONTRACT_NAME']);
			
			$("[name='contract.CUSTOMER_NAME']").val(row['CUST_NAME']);
			$("[name='contract.CUST_ID']").val(row['CUST_ID']);
			$("[name='contract.CUST_NO']").val(row['CUST_NO']);
			
			$("[name='extend.PM_BY_NAME']").val(row['PRE_SALES_NAME']);
			$("[name='contract.PM_BY']").val(row['PRE_SALES_BY']);
			
			$("[name='extend.SALES_BY_NAME']").val(row['SALES_BY_NAME']);
			$("[name='contract.SALES_BY']").val(row['SALES_BY']);
			
			$("[name='extend.PAYEE_NAME']").val(row['SALES_BY_NAME']);
			$("[name='contract.PAYEE']").val(row['SALES_BY']);
			
			$("[name='extend.SALE_DEPT_NAME']").val(row['SIGN_DEPT_NAME']);
			$("[name='contract.SALE_DEPT_ID']").val(row['SIGN_DEPT_ID']);
			
			$("[name='contract.PROD_LINE']").val(row['PROD_LINE']);
			
			$("[name='contract.SJ_ID']").val(row['SJ_ID']);
			$("[name='contract.SJ_NAME']").val(row['SJ_NAME']);
			
			getContractName();
		}
		
		function getContractName(){
			var v1 = $("[name='contract.CONTRACT_NO']").val();
			var v2 = $("[name='contract.CONTRACT_SIMPILE_NAME']").val();
			$("[name='contract.CONTRACT_NAME']").val(v1+" "+v2);
		}
		
		function getTextVal(formData){
			var data = $.extend({},formData);
			var json = {};
			for(var key in data){
				if(key.indexOf("extend.")>-1){
					var newKey = key.replace('extend.','');
					json[newKey] = data[key];
				}
			}
			return JSON.stringify(json);
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>