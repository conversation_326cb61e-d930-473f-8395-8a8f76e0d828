<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>云趣乐享平台 - ${articleInfo.title}</title>
</EasyTag:override>
<EasyTag:override name="content">
	<div class="layui-container">
  <div class="layui-row layui-col-space15">
    <div class="layui-col-md8 content detail">
      <div class="fly-panel detail-box">
        <h1>${articleInfo.title}</h1>
        <div class="fly-detail-info">
          <!-- <span class="layui-badge">审核中</span> -->
          <span class="layui-badge layui-bg-green fly-detail-column">${articleInfo.category_name}</span>
          
           <c:choose>
            <c:when test="${articleInfo.close_flag=='1'}">
      			<span class="layui-badge" style="background-color: #5FB878;">已结</span>
            </c:when>
           	<c:otherwise>
       			<span class="layui-badge" style="background-color: #999;">未结</span>
           	</c:otherwise>
          </c:choose>
	              
          <c:if test="${articleInfo.recommend_flag=='0'}">
          	<span class="layui-badge layui-bg-black">置顶</span>
          </c:if>
          <c:if test="${articleInfo.wonderful_flag=='1'}">
         	 <span class="layui-badge layui-bg-red">精帖</span>
          </c:if>
          
          <c:if test="${isSelf}">
	          <div class="fly-admin-box" data-id="${articleInfo.remind_id}">
	            <span class="layui-btn layui-btn-xs jie-admin" type="del">删除</span>
	            <c:if test="${isSuperUser}">
	              <c:choose>
		            <c:when test="${articleInfo.recommend_flag=='0'}">
	            		<span class="layui-btn layui-btn-xs jie-admin" type="set" field="stick" rank="0" style="background-color:#ccc;">取消置顶</span>
		            </c:when>
	            	<c:otherwise>
	           			<span class="layui-btn layui-btn-xs jie-admin" type="set" field="stick" rank="1">置顶</span> 
	            	</c:otherwise>
	             </c:choose>
	             <c:choose>
		            <c:when test="${articleInfo.wonderful_flag=='1'}">
	            		<span class="layui-btn layui-btn-xs jie-admin" type="set" field="status" rank="0" style="background-color:#ccc;">取消加精</span>
		            </c:when>
	            	<c:otherwise>
			            <span class="layui-btn layui-btn-xs jie-admin" type="set" field="status" rank="1">加精</span> 
	            	</c:otherwise>
	              </c:choose>
	            </c:if>
	          </div>
          </c:if>
          <span class="fly-list-nums"> 
            <a href="#comment"><i class="iconfont" title="回答">&#xe60c;</i> ${articleInfo.comment_count}</a>
            <i class="iconfont" title="人气">&#xe60b;</i> ${articleInfo.view_count}
          </span>
        </div>
        <div class="detail-about">
          <a class="fly-avatar" href="/yq-work/fly/u/${userInfo.userId}">
            <img src="${userInfo.picUrl}" onerror="this.src='/yq-work/static/images/user-avatar-large.png'">
          </a>
          <div class="fly-detail-user">
            <a href="/yq-work/fly/u/${userInfo.userId}" class="fly-link">
              <cite>${userInfo.userName}</cite>
              <i class="iconfont icon-renzheng" title="认证信息：${userInfo.post}"></i>
              <i class="layui-badge fly-badge-vip">VIP${userInfo.level}</i>
            </a>
            <span>${articleInfo.publish_time}</span>
          </div>
          <div class="detail-hits" id="LAY_jieAdmin" data-id="${articleInfo.remind_id}">
          	<c:if test="${item.experience>0}">
	            <span style="padding-right: 10px; color: #FF7200">悬赏：${articleInfo.experience}积分</span> 
          	</c:if>
            <c:if test="${isSelf}">
	            <span class="layui-btn layui-btn-xs jie-admin" type="edit"><a href="/yq-work/fly/edit/${articleInfo.remind_id}">编辑此贴</a></span>
             </c:if> 
          </div>
        </div>
        <div class="detail-body photos">
      		${articleInfo.content}
     	</div>
	  </div>
      <div class="fly-panel detail-box" id="flyReply">
        <fieldset class="layui-elem-field layui-field-title" style="text-align: center;">
          <legend>回帖</legend>
        </fieldset>
        <ul class="jieda" id="jieda">
         <c:forEach items="${commentList}" var="item">
           <li data-id="${item.comment_id}" class="jieda-daan">
            <a name="item-${item.comment_id}"></a>
            <div class="detail-about detail-about-reply">
              <a class="fly-avatar" href="">
                <img src="${item.pic_url}" onerror="this.src='/yq-work/static/images/user-avatar-large.png'">
              </a>
              <div class="fly-detail-user">
                <a href="" class="fly-link">
                  <cite>${item.username}</cite>
                  <i class="iconfont icon-renzheng" title="认证信息：${item.job_title}"></i>
                  <i class="layui-badge fly-badge-vip">VIP${item.level}</i>              
                </a>
                <c:if test="${staffInfo.userId==userInfo.userId}">
	                <span>(楼主)</span>
                </c:if>
                <!--
                <span style="color:#5FB878">(管理员)</span>
                <span style="color:#FF9E3F">（社区之光）</span>
                <span style="color:#999">（该号已被封）</span>
                -->
              </div>
              <div class="detail-hits">
                <span>${item.create_time}</span>
              </div>
			  <c:if test="${item.accept_flag=='1'}">
	             <i class="iconfont icon-caina" title="最佳答案"></i>
			  </c:if>
            </div>
            <div class="detail-body jieda-body photos">${item.content}</div>
            <div class="jieda-reply">
              <span class="jieda-zan zanok" type="zan">
                <i class="iconfont icon-zan"></i>
                <em>${item.like_count}</em>
              </span>
              <span type="reply">
                <i class="iconfont icon-svgmoban53"></i>回复
              </span>
              <div class="jieda-admin">
              	 <c:if test="${(staffInfo.userId==item.creator||isSuperUser)&&item.accept_flag=='0'}">
                	<span type="edit">编辑</span>
                	<span type="del">删除</span>
                </c:if>
                <c:if test="${item.accept_flag=='0'&&staffInfo.userId==userInfo.userId}">
                	<c:if test="${articleInfo.close_flag=='0'}">
	                	<span class="jieda-accept" type="accept">采纳</span>
                	</c:if>
                </c:if>
              </div>
            </div>
          </li>
          </c:forEach>
          <c:if test="${empty commentList}">
        	<li class="fly-none">消灭零回复</li>
         </c:if>
        </ul>
        
        <div class="layui-form layui-form-pane">
          <form action="/yq-work/fly/reply/" method="post">
            <div class="layui-form-item layui-form-text">
              <a name="comment"></a>
              <div class="layui-input-block">
                <textarea id="L_content" name="content" required lay-verify="required" placeholder="请输入内容"  class="layui-textarea fly-editor" style="height: 150px;"></textarea>
              </div>
            </div>
            <div class="layui-form-item">
              <input type="hidden" name="jid" value="${articleInfo.remind_id}">
              <button class="layui-btn" lay-filter="*" lay-submit>提交回复</button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <div class="layui-col-md4">
      <dl class="fly-panel fly-list-one">
        <dt class="fly-panel-title">最近热议</dt>
        <c:forEach items="${hostList}" var="item">
	        <dd>
	          <a href="/yq-work/fly/detail/${item.remind_id}">${item.title}</a>
	          <span><i class="iconfont icon-pinglun1"></i> ${item.comment_count}</span>
	        </dd>
        </c:forEach>
        <c:if test="${empty hostList}">
        	<div class="fly-none">没有相关数据</div>
        </c:if>
      </dl>

      <div class="fly-panel">
        <div class="fly-panel-title">
          	宣传栏
        </div>
        <div class="fly-panel-main">
          <a href="https://work.yunqu-info.cn/" target="_blank" class="fly-zanzhu" time-limit="2017.09.25-2099.01.01" style="background-color: #5FB878;">乐享，人人快乐分享</a>
        </div>
      </div>
      <div class="fly-panel" style="padding: 20px 0; text-align: center;">
        <img src="https://work.yunqu-info.cn/qrcode_for_gh_562078549f9f_258.jpg" style="max-width: 100%;">
        <p style="position: relative; color: #666;">微信扫码关注公众号</p>
      </div>
    </div>
  </div>
</div>

</EasyTag:override>
 
<EasyTag:override name="script">
	<script type="text/javascript">
	    layui.cache.page = 'jie';
	    layui.cache.user = {username: '${staffInfo.userName}' , uid: '${staffInfo.userId}', avatar: '${staffInfo.picUrl}', experience: 83 , sex: '男'};
		layui.config({version: "3.0.0",base: '/yq-work/pages/fly/static/mods/'}).extend({fly: 'index'}).use(['fly','jie'],function(){
		   var fly = layui.fly;
	       $('.detail-body').each(function(){
	         var othis = $(this), html = othis.html();
	         othis.html(fly.content(html));
	      });
	    });
   </script>
</EasyTag:override>
<%@ include file="fly-layout.jsp" %>
