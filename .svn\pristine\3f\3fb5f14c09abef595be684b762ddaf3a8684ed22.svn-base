<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>事务管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="taskMgrForm">
			<input name="taskState" id="taskState" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5 id="title">收件箱</h5>
	          		     <div class="input-group input-group-sm" style="width: 180px">
	          		    	<span class="input-group-addon">标题</span>	
                     		<input data-mars-reload="false" type="text" name="title" class="form-control input-sm">
                    	 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="taskMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="taskMgrTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
  <script type="text/javascript">
		var taskMgr={
			init:function(){
				$("#taskMgrForm").initTable({
					mars:'AffairDao.receivedAffairList',
					height:'full-120',
					id:'taskMgrTable',
					limit:20,
					even: true,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'AFFAIR_NAME',
						title: '主题',
						align:'left',
						minWidth:220,
						event:'taskMgr.detail',
						style:'color:#036fe2;cursor:pointer',
						templet:function(row){
							var state = row["STATE"];
							if(state=='1'){
								return '<span class="layui-badge ml-5">新</span> '+row['AFFAIR_NAME'];
							}else{
								return row['AFFAIR_NAME'];
							}
						}
					},{
					    field: 'CREATE_NAME',
						title: '发件人',
						align:'left',
						width:120
					},{
					    field: 'USER_NAME',
						title: '收件人',
						align:'left',
						width:120
					},{
					    field: 'SEND_TIME',
						title: '发送时间',
						align:'center',
						width:140
					},{
						field:'LOOK_TIME',
						title:'查看时间',
						width:140
					}
				]],done:function(){
					
			   }});
			},
			query:function(){
				$("#taskMgrForm").render();
				$("#taskMgrForm").queryData({id:'taskMgrTable',jumpOne:true});
			},
			detail:function(data){
				let url = '${ctxPath}/affair/'+data.AFFAIR_ID+"?source=receive";
				$.get(url,{},function(result){
					window.location.hash = "#"+data.AFFAIR_ID;
  					$(".right-content").html(result);
				});
			}
		}
		$(function(){
			$("#taskMgrForm").render({success:function(result){
				taskMgr.init();
			}});
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>