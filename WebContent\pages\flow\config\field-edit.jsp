<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>字段配置</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="easyform" class="mt-20" data-mars="FlowConfigDao.fieldInfo" data-mars-prefix="field.">
			 	  <input name="fieldId" value="${param.fieldId}" type="hidden"/>
			 	  <input name="field.FIELD_ID" value="${param.fieldId }" type="hidden"/>
			 	  <input name="field.FLOW_CODE" value="${param.flowCode}" type="hidden"/>
			 	  <textarea name="field.EXT_CONFIG" class="hidden" id="extConfig"></textarea>
				  <table class="table table-edit table-vzebra">
	                    <tbody>
							  <tr>
			                        <td style="width: 80px;" class="required">释义</td>
			                        <td><input  type="text" name="field.TITLE" data-rules="required" class="form-control input-sm"></td>
			                        <td style="width: 80px;" class="required">导入字段</td>
			                        <td><input  type="text" name="field.HEADER" data-rules="required" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
			                        <td>数据库字段名</td>
			                        <td><input type="text" name="field.TABLE_FIELD" class="form-control input-sm"></td>
			                        <td style="width: 80px;">字段描述</td>
			                        <td><input  type="text" name="field.REMARK" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
			                        <td class="required text-c">字段类型</td>
			                        <td>
			                        	<select name="field.DATA_TYPE" class="form-control input-sm">
			                        		<option value="text">文本</option>
			                        		<option value="identifier">编号</option>
			                        		<option value="number">数字</option>
			                        		<option value="money">金额</option>
			                        		<option value="idcard">身份证号码</option>
			                        		<option value="bankcard">银行卡号</option>
			                        		<option value="tel">电话号码</option>
			                        		<option value="email">邮件</option>
			                        		<option value="date">日期(精确到日)</option>
			                        		<option value="datetime">日期(精确到时分秒)</option>
			                        	</select>
			                        </td>
			                        <td class="required text-c">数据表</td>
			                        <td>
			                        	<select name="field.TABLE_FLAG" class="form-control input-sm">
			                        		<option value="0">流程表字段</option>
			                        		<option value="1">扩展字段</option>
			                        		<option value="2">业务表</option>
			                        	</select>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td class="required text-c">状态</td>
			                        <td>
			                        	<select name="field.STATE" class="form-control input-sm">
			                        		<option value="0">启用</option>
			                        		<option value="1">暂停</option>
			                        	</select>
			                        
			                        </td>
			                        <td class="text-c" >序号</td>
			                        <td><input type="number" name="field.IDX_ORDER" data-rules="required" class="form-control input-sm" value="99"></td>
		                     </tr>
	                    </tbody>
	                    <tbody id="items">
			     	   		<tr>
			     	   			<td>展示</td>
			     	   			<td>
			     	   				<label class="checkbox checkbox-inline checkbox-success">
			                        	<input type="checkbox" value="1" name="listshow"> <span>列表</span>
			                        </label>
			     	   				<label class="checkbox checkbox-inline checkbox-success">
			                        	<input type="checkbox" value="1" name="importshow"> <span>导入</span>
			                        </label>
			     	   				<label class="checkbox checkbox-inline checkbox-success">
			                        	<input type="checkbox" value="1" name="exportshow"> <span>导出</span>
			                        </label>
			     	   				<label class="checkbox checkbox-inline checkbox-success">
			                        	<input type="checkbox" value="1" name="formshow"> <span>表单</span>
			                        </label>
			     	   			</td>
			     	   			<td>查询</td>
			     	   			<td>
			     	   				<select name="query" class="form-control">
			     	   					<option value=""></option>
			     	   					<option value="daterange">日期范围</option>
			     	   					<option value="datetimerange">时间范围</option>
			     	   					<option value="date">日期查询</option>
			     	   					<option value="like">模糊查询</option>
			     	   					<option value="exact">精确查询 </option>
			     	   					<option value="ne"> <> </option>
			     	   					<option value="gt"> > </option>
			     	   					<option value="lt"> < </option>
			     	   					<option value="ge"> >= </option>
			     	   					<option value="le"> <= </option>
			     	   					<option value="range">范围查询</option>
			     	   				</select>
			     	   			</td>
			     	   		</tr>
			     	   		<tr>
			     	   			<td>宽度</td>
			     	   			<td>
			     	   				<input name="width" type="number" class="form-control"/>
			     	   			</td>
			     	   			<td>最小宽度</td>
			     	   			<td>
			     	   				<input name="minWidth" type="number" class="form-control"/>
			     	   			</td>
			     	   		</tr>
			     	   		<tr>
			     	   			<td>表格样式</td>
			     	   			<td>
			     	   				<input name="style" placeholder="color:red;font-size:14px;" type="text" class="form-control"/>
			     	   			</td>
			     	   			<td>列对齐</td>
			     	   			<td>
			     	   				<select name="align" class="form-control">
			     	   					<option value="left">居左</option>
			     	   					<option value="center">居中</option>
			     	   					<option value="right">居右</option>
			     	   				</select>
			     	   			</td>
			     	   		</tr>
			     	   		<tr>
			     	   			<td>排序</td>
			     	   			<td>
			     	   				<select name="sort" class="form-control">
			     	   					<option value="false">否</option>
			     	   					<option value="true">是</option>
			     	   				</select>
			     	   			</td>
			     	   			<td>自定义模板</td>
			     	   			<td>
			     	   				<textarea name="templet" style="height: 50px;" placeholder="function(row){return row[''];}" class="form-control"></textarea>
			     	   			</td>
			     	   		</tr>
			     	   	</tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="Edit.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	var Edit={};
	
	Edit.fieldId='${param.fieldId}';
	
	$(function(){
		$("#easyform").render({success:function(result){
			var jsonStr = $('#extConfig').val();
			if(jsonStr){
				var data = eval('('+jsonStr+')');
				fillRecord(data,'','','#easyform');
			}
		}});  
	});
	
	Edit.insertData = function() {
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/web/flow/config/addField",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1000},function(){
					FieldConfig.reload();
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		  }
		);
	}
	
	Edit.updateData = function(){
		var ext = form.getJSONObject("#items");
		for(var index in ext){
			if(ext[index]==''){
				delete ext[index];
			}
			if(isArray(ext[index])){
				ext[index] = ext[index][0];
			}
		}
		var extStr = JSON.stringify(ext);
		$('#extConfig').val(extStr);
		
		var data = form.getJSONObject("#easyform");
		ajax.remoteCall("${ctxPath}/web/flow/config/updateField",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1000},function(){
					FieldConfig.reload();
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg);
			}
		  }
		);
	}
	Edit.ajaxSubmitForm = function(){
		 if(form.validate("#easyform")){
			 if(Edit.fieldId ==''){
				 Edit.insertData(); 
			 }else{
				 Edit.updateData(); 
			 }
		 };
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>