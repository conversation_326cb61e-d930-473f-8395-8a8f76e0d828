package com.yunqu.work.servlet.devops;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.ServerRoomModel;
/**
 * 机房管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/serverRoom/*")
public class ServerRoomServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		ServerRoomModel model=getModel(ServerRoomModel.class, "serverRoom");
		model.setPrimaryValues(RandomKit.uniqueStr());
		model.addCreateTime();
		model.setCreator(getUserId());
		try {
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		ServerRoomModel model=getModel(ServerRoomModel.class, "serverRoom");
		try {
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		ServerRoomModel model=getModel(ServerRoomModel.class, "serverRoom");
		try {
			String sql="delete from yq_ops_host where SERVER_ROOM_ID = ? ";
			this.getQuery().executeUpdate(sql, model.getPrimaryValue());
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}





