<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>projectContract</title>
	<style>
		.form-horizontal{width: 100%;}
		.w-e-text-container{z-index: 0!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		  <form id="contractEditForm" class="form-horizontal" data-mars="ProjectContractDao.record" autocomplete="off" data-mars-prefix="contract.">
 		   		<input type="hidden" value="${param.projectId}" name="contract.PROJECT_ID"/>
 		   		<input type="hidden" value="${param.custId}" name="custId"/>
 		   		<input type="hidden" value="${param.contractId}" name="contractId"/>
 		   		<input type="hidden" id="contractId" value="${param.contractId}" name="contract.CONTRACT_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td width="80px" class="required">合同名称</td>
		                    <td style="width: 40%"><input data-rules="required"  type="text" name="contract.CONTRACT_NAME" class="form-control input-sm"></td>
		                    <td width="80px" class="required">合同编号</td>
		                    <td style="width: 40%">
		                    	<input data-mars="ProjectContractDao.getContractNo" data-rules="required"  type="text" name="contract.CONTRACT_NO" class="form-control input-sm">
		                    </td>
			            </tr>
			            
			           <tr>
			           		<td>合同评审号</td>
		                    <td>
			                    <input type="text" placeholder="请点击选择" name="contract.REVIEW_ID"  class="form-control input-sm">
		                    </td>
		                    <td class="required">合同年限</td>
		                    <td>
			                    <select data-rules="required" data-mars="YqAdmin.DictDao.select('Y005')" name="contract.YEAR"  class="form-control input-sm text-val">
			                    	<option value="">请选择</option>
			                    </select>
		                    </td>
			           </tr>
			           <tr>
		                    <td>客户方合同号</td>
		                    <td>
			                    <input type="text" name="contract.CUST_CONTRACT_NO"  class="form-control input-sm">
		                    </td>
		                    <td>合同签订</td>
		                    <td>
			                     <label class="radio radio-info radio-inline" style="margin-top: 2px;">
		                          	<input type="radio" checked value="0" class="text-val" name="contract.SIGN_FLAG"><span>正式签订</span>
		                          </label>
		                          <label class="radio radio-info radio-inline">
		                          	<input type="radio" value="1" class="text-val" name="contract.SIGN_FLAG"><span>未正式签订</span>
		                          </label>
		                    </td>
			           </tr>
			           <tr>
		                    <td class="required">客户名称</td>
		                    <td>
			                    <input type="hidden" name="contract.CUST_ID">
			                    <input type="text" data-rules="required"  readonly="readonly" id="custId" placeholder="请点击选择客户" name="contract.CUSTOMER_NAME"  class="form-control input-sm">
		                    </td>
		                     <td>关联商机</td>
		                    <td>
			                    <select data-mars="CustDao.custBusniessDict" name="contract.BUSINESS_ID"  class="form-control input-sm text-val">
			                    	<OPTION value="">--请选择--</OPTION>
			                    </SELECT>
		                    </td>
			           </tr>
			           <tr>
		                    <td>项目开始</td>
		                    <td>
			                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contract.START_DATE" class="form-control input-sm Wdate">
		                    </td>
		                    <td>项目结束</td>
		                    <td>
			                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contract.END_DATE" class="form-control input-sm Wdate">
			                    <!-- <select data-rules="required" data-mars="YqAdmin.DictDao.select('Y003')" name="contract.SIGN_OFFICE"  class="form-control input-sm text-val">
			                    	<OPTION value="">--请选择--</OPTION>
			                    </SELECT> -->
		                    </td>
			           </tr>
			            <tr>
		                    <td>签订日期</td>
		                    <td>
			                    <input type="text" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" data-mars="CommonDao.today" name="contract.SIGN_DATE" class="form-control input-sm Wdate">
		                    </td>
		                    <td class="required">合同金额</td>
		                    <td>
			                    <input type="number" data-rules="required" value="0" name="contract.AMOUNT" class="form-control input-sm">
		                    </td>
		                </tr>
		                <tr>
			        		<td>产品经理</td>
		                    <td>
		                    	<select class="form-control input-sm text-val" data-mars="CommonDao.userDict" name="contract.PM_BY">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
		                    <td class="required">收款负责人</td>
		                    <td>
		                    	<select class="form-control input-sm text-val" data-rules="required" data-mars="CommonDao.userDict" name="contract.PAYEE">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
			        	</tr>
			        	<tr>
			        	</tr>
			        	  <tr>
		                    <td>签约人</td>
		                    <td>
			                    <select class="form-control input-sm text-val" data-rules="required" data-mars="CommonDao.userDict" name="contract.SALES_BY">
			                    		<option value="">请选择</option>
			                     </select>
		                    </td>
		                    <td class="required">营销大区</td>
		                    <td>
		                    	<select class="form-control input-sm text-val" data-rules="required" data-mars="CommonDao.deptDict" name="contract.SALE_DEPT_ID">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td>产品线</td>
		                    <td>
			                    <SELECT  data-rules="required" data-mars="YqAdmin.DictDao.select('Y001')"  name="contract.PROD_LINE"  class="form-control input-sm text-val">
			                    	<option value="">请选择</option>
			                    </SELECT>
		                    </td>
		                    <td class="required">合同类型</td>
		                    <td>
		                    	<select data-rules="required" data-mars="YqAdmin.DictDao.select('Y004')" name="contract.CONTRACT_TYPE"  class="form-control input-sm text-val">
			                    	<option value="">请选择</option>
			                    </select>
		                    </td>
		                </tr>
			            <tr>
			            	<td>合同描述</td>
			               	<td colspan="3">
	                           <textarea id="wordText" style="height: 120px;" class="form-control input-sm" name="contract.CONTRAC_DESC"></textarea>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c" style="z-index: 999999999">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="Contract.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  	    </form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
   
		layui.config({
			  base: '${ctxPath}/static/js/'
		}).use('tableSelect');
	
		jQuery.namespace("Contract");
	    
		Contract.contractId='${param.contractId}';
		var contractId='${param.contractId}';
		
		$(function(){
		    $('[data-toggle="tooltip"]').tooltip();
			$("#contractEditForm").render({success:function(result){
				initSelectCust();
			 	 requreLib.setplugs('select2',function(){
					$("#contractEditForm select").select2({theme: "bootstrap",placeholder:'请选择'});
					$("[name='contract.BUSINESS_ID']").data('select2').destroy();
					$(".select2-container").css("width","100%");
			 	 });
			}});  
		});
		Contract.ajaxSubmitForm = function(){
			if(form.validate("#contractEditForm")){
				if(Contract.contractId){
					Contract.updateData(); 
				}else{
					Contract.insertData(); 
				}
			};
		}
		Contract.insertData = function(flag) {
			var data = form.getJSONObject("#contractEditForm");
			data['contract.TEXT_JSON']= getTextVal();
			ajax.remoteCall("${ctxPath}/servlet/projectContract?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('contractEditForm');
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Contract.updateData = function(flag) {
			var data = form.getJSONObject("#contractEditForm");
			data['contract.TEXT_JSON']= getTextVal();
			ajax.remoteCall("${ctxPath}/servlet/projectContract?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('contractEditForm');
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function initSelectCust(){
			layui.use(['form','tableSelect'], function(){
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#custId',
					searchKey: 'custName',
					checkedKey: 'CUST_ID',
					page:true,
					searchPlaceholder: '请输入客户名称',
					table: {
						mars: 'CustDao.custList',
						cols: [[
						        { type: 'radio' },
						        { field: 'CUST_NO',title: '编号'},
						        { field: 'CUST_NAME', title: '名称', width: 320 }
						        ]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.CUST_NAME)
							ids.push(item.CUST_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
						
						$("[name='contract.BUSINESS_ID']").render({data:{custId:ids[0]}});
					},
					clear:function(elem){
						elem.prev().val("");
					}
				});
			});
		}
		function getTextVal(){
			var json = {};
			$("#contractEditForm .text-val").each(function(){
				var s = $(this);
				var name = s.attr('name');
				var val = s.val();
				var field = name.split('.')[1];
				var tagName = s[0].tagName.toLowerCase();
				var tagNametype = s[0].type.toLowerCase();
		      	if(tagNametype=="checkbox"||tagNametype=="radio"){
	      			var tempVal = $('input:radio[name="'+name+'"]:checked').val();
		      		if(tempVal){
		      		  var text = s.next().text();
		      		  json[field]=text;
		      	    }
		      	}
		      	else if(tagName=="select"){
		      		var text = s.find("option:selected").text();
		      		if(val){
			      		json[field]=text;
		      		}else{
				      	json[field]='';
		      		}
		      	}
			});
			delete json[''];
			return JSON.stringify(json);
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>