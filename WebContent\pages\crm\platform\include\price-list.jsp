<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<script type="text/html" id="priceToolBar">
    <button type="button" class="btn btn-sm btn-info" onclick="priceList.add()">+ 新增价格</button>
</script>


<div class="pull-left mb-5">
</div>

<table id="priceTable" class="layui-table"></table>

<script type="text/javascript">
    var priceList = {
        init: function(){
            $("#PlatformDetailForm").initTable({
                mars:'CustOperateDao.salePricePageList',
                id:'priceTable',
                toolbar:'#priceToolBar',
                page:true,
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align:'left'
                    },{
                        field: '',
                        title: '操作',
                        width: 100,
                        align: 'center',
                        templet: function(d){
                            var html = '<a href="javascript:;" onclick="priceList.edit(\''+d.PRICE_ID+'\')">编辑</a>';
                            html += ' <a href="javascript:;" onclick="priceList.del(\''+d.PRICE_ID+'\')">删除</a>';
                            return html;
                        }
                    },{
                        field: 'CUSTOMER_NAME',
                        title: '客户名称',
                        minWidth:150,
                        templet:'<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUSTOMER_NAME}}</a></div>'
                    },{
                        field: 'PRODUCT_NAME',
                        title: '产品名称',
                        minWidth:100
                    },{
                        field: 'PRICE_TYPE',
                        title: '价格类型',
                        width:80,
                        align: 'center',
                    },{
                        field: 'PRICE_UNIT',
                        title: '价格单位',
                        align: 'center',
                        width:80,
                    },{
                        field: 'SALES_PRICE',
                        title: '销售价格',
                        align: 'center',
                        width:80,
                    },{
                        field: 'SALE_TAX_RATE',
                        title: '销售税率%',
                        align: 'center',
                        width:80,
                    },{
                        field: 'SUPPLIER_PRICE',
                        title: '供应商价格',
                        align: 'center',
                        width:80,
                    },{
                        field: 'BUY_TAX_RATE',
                        title: '采购税率%',
                        align: 'center',
                        width:80,
                    },{
                        field: 'SUPPLIER_NAME',
                        title: '供应商',
                        width:120,
                        templet:'<div><a href="javascript:;" onclick="supplierDetail(\'{{d.SUPPLIER_ID}}\')">{{d.SUPPLIER_NAME}}</a></div>'
                    },{
                        field: 'PAYMENT_METHOD',
                        title: '付款方式',
                        align: 'center',
                        width:80,
                    },{
                        field: 'STATUS',
                        title: '状态',
                        width:80,
                        align: 'center',
                        templet: function(row){
                            return row.STATUS=='0'?'正常':'停用';
                        }
                    }
                ]],
                done:function(res){
                    if(res.totalRow != undefined){
                        $("[name='extend.PRICE_COUNT']").text(res.totalRow);
                    }
                }
            });
        },
        add: function(){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'新增价格',
                offset:'20px',
                area:['600px','540px'],
                url:"${ctxPath}/pages/crm/platform/include/add-price.jsp",
                data:{platformId:platformId,platformName:platformInfo.PLATFORM_NAME}
            });
        },
        edit: function(priceId){
            popup.layerShow({
                type:1,
                anim:0,
                scrollbar:false,
                shadeClose:false,
                title:'编辑价格',
                offset:'20px',
                area:['600px','c'],
                url:"${ctxPath}/pages/crm/platform/include/add-price.jsp",
                data:{isNew:"0",priceId:priceId}
            });
        },
        del: function(priceId){
            layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function(){
                ajax.remoteCall("${ctxPath}/servlet/custOperate?action=deletePrice",{priceId:priceId},function(result){
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            reloadPriceList();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            });
        }
    };

    function reloadPriceList(){
        $("#PlatformDetailForm").queryData({id:'priceTable',page:true});
    }

    function supplierDetail(supplierId){
        popup.openTab({
            id:'supplierDetail',
            url:"${ctxPath}/pages/erp/supplier/supplier-detail.jsp",
            title:'供应商详情',
            data:{supplierId:supplierId}
        });
    }
</script>
