package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.utils.ZendaoApi;

public class ZendaoService extends AppBaseService implements Job{

	private static class Holder{
		private static ZendaoService service=new ZendaoService();
	}
	
	public static ZendaoService getService(){
		return Holder.service;
	}
	
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		this.run();
	}
	
	public  void run(){
		getLogger().info("执行同步用户任务....");
		try {
			String pwd="e91ad915ba65b8de9fd53cafeb9fdd95";//yq123456
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT USERNAME,NIKE_NAME,SEX,MOBILE,EMAIL,BORN,USER_PWD,DATA3,USER_ACCT,DEPTS,ACCT_STATE from easi_user t1 INNER JOIN easi_user_login t2 on t1.USER_ID=t2.USER_ID ",null,new JSONMapperImpl());
			getLogger().info("list>>"+list.size());
			if(list!=null&&list.size()>0){
				Map<String,String> depts=getDepts();
				getLogger().info("depts>>"+depts);
				for(JSONObject jsonObject:list){
				 int acctState=jsonObject.getIntValue("ACCT_STATE");
				 String id= this.getZentaoQuery().queryForString("select id from zt_user where account = ?",jsonObject.getString("USER_ACCT"));
				 if(StringUtils.isBlank(id)&&acctState==0){
					// pwd=jsonObject.getString("USER_PWD").toLowerCase();
					 EasyRecord record=new EasyRecord("zt_user");
					 record.set("realname",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),""));
//					 record.set("nickname",StringUtils.defaultIfEmpty(jsonObject.getString("NIKE_NAME"),""));
					 record.set("phone", jsonObject.getString("MOBILE")+"");
					 record.set("mobile", jsonObject.getString("MOBILE")+"");
					 record.set("email", jsonObject.getString("EMAIL")+"");
					 record.set("role", "dev");
					 record.set("account", jsonObject.getString("USER_ACCT"));
					 record.set("password", pwd);
					 String deptId=depts.get(jsonObject.getString("DEPTS"));
					 record.set("dept", deptId==null?"0":deptId);
					 this.getZentaoQuery().save(record);
					 
					 EasyRecord usergroup=new EasyRecord("zt_usergroup","account","group");
					 usergroup.put("account",jsonObject.getString("USER_ACCT"));
					 usergroup.put("`group`",2);
					 try {
						this.getZentaoQuery().save(usergroup);
					} catch (Exception e) {
						e.printStackTrace();
					}
					 
				 }else{
					// pwd=jsonObject.getString("USER_PWD").toLowerCase();
					 EasyRecord record=new EasyRecord("zt_user","id");
					 record.setPrimaryValues(id);
					 record.set("realname",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),""));
//					 record.set("nickname",StringUtils.defaultIfEmpty(jsonObject.getString("NIKE_NAME"),""));
					 record.set("phone", jsonObject.getString("MOBILE")+"");
					 record.set("mobile", jsonObject.getString("MOBILE")+"");
					 record.set("email", jsonObject.getString("EMAIL")+"");
					 record.set("account", jsonObject.getString("USER_ACCT"));
					 String deptId=depts.get(jsonObject.getString("DEPTS"));
					 record.set("dept", deptId==null?"0":deptId);
					 
					 record.set("password",pwd);
					 if(acctState!=0){
						 record.set("deleted", 1);
					 }else{
						 record.set("deleted", 0);
					 }
					// String deptId=depts.get(jsonObject.getString("DEPTS"));
					 //record.set("dept", deptId==null?"0":deptId);
					 try {
						this.getZentaoQuery().update(record);
					} catch (Exception e) {
						e.printStackTrace();
					}
				 }
				}
			}
		} catch (SQLException e2) {
			e2.printStackTrace();
			this.getLogger().error(e2);
		}
	}
	
	public  void newZendao(){
		getLogger().info("newZendao执行同步用户任务....");
		try {
			//新增或修改
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT USERNAME,NIKE_NAME,SEX,MOBILE,EMAIL,BORN,USER_PWD,DATA3,USER_ACCT,DEPTS,ACCT_STATE,t3.JOIN_DATE from easi_user t1 INNER JOIN easi_user_login t2 on t1.USER_ID=t2.USER_ID INNER JOIN yq_staff_info t3 on t1.USER_ID = t3.STAFF_USER_ID where t2.ACCT_STATE = 0",null,new JSONMapperImpl());
			getLogger().info("list>>"+list.size());
			if(list!=null&&list.size()>0){
				Map<String,String> depts = getNewDepts();
				getLogger().info("depts>>"+depts);
				String token = ZendaoApi.getToken();
				for(JSONObject jsonObject:list){
					int acctState = jsonObject.getIntValue("ACCT_STATE");
					String id = this.getQuery().queryForString("select id from zentao.zt_user where account = ?",jsonObject.getString("USER_ACCT"));
					if(StringUtils.isBlank(id)&&acctState==0){
						JSONObject result = ZendaoApi.addUser(token,jsonObject.getString("USER_ACCT"), "yq123456", StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),""));
						if(result==null) {
							continue;
						}
						id = this.getQuery().queryForString("select id from zentao.zt_user where account = ?",jsonObject.getString("USER_ACCT"));
						EasyRecord record = new EasyRecord("zentao.zt_user","id");
						record.setPrimaryValues(id);
						record.set("realname",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),""));
						record.set("phone", getPhone(jsonObject.getString("MOBILE")+""));
						record.set("mobile", getPhone(jsonObject.getString("MOBILE")+""));
						record.set("email", StringUtils.trim(jsonObject.getString("EMAIL")+""));
						record.set("role", "dev");
						record.set("account", jsonObject.getString("USER_ACCT"));
						record.set("gender", "1".equals(jsonObject.getString("SEX"))?"m":"f");
						String deptId = depts.get(jsonObject.getString("DEPTS"));
						record.set("dept", deptId==null?"0":deptId);
						this.getQuery().update(record);
					}else{
						EasyRecord record = new EasyRecord("zentao.zt_user","id");
						record.setPrimaryValues(id);
						record.set("realname",StringUtils.defaultIfEmpty(jsonObject.getString("USERNAME"),""));
						record.set("phone", getPhone(jsonObject.getString("MOBILE")+""));
						record.set("mobile", getPhone(jsonObject.getString("MOBILE")+""));
						record.set("email", jsonObject.getString("EMAIL")+"");
						record.set("account", jsonObject.getString("USER_ACCT"));
						record.set("gender", "1".equals(jsonObject.getString("SEX"))?"m":"f");
						String deptId = depts.get(jsonObject.getString("DEPTS"));
						record.set("dept", deptId==null?"0":deptId);
						try {
							this.getQuery().update(record);
						} catch (Exception e) {
							this.getLogger().error(null,e);
						}
					}
				}
			}
		} catch (SQLException ex) {
			this.getLogger().error(null,ex);
		}
		try {
			//暂停账号
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT USERNAME,NIKE_NAME,SEX,MOBILE,EMAIL,BORN,USER_PWD,DATA3,USER_ACCT,DEPTS,ACCT_STATE,t3.JOIN_DATE from easi_user t1 INNER JOIN easi_user_login t2 on t1.USER_ID=t2.USER_ID INNER JOIN yq_staff_info t3 on t1.USER_ID = t3.STAFF_USER_ID where t2.ACCT_STATE <> 0",null,new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					EasyRecord record = new EasyRecord("zentao.zt_user","account");
					record.setPrimaryValues(jsonObject.getString("USER_ACCT"));
					record.set("deleted", "1");
					this.getQuery().update(record);
				}
			}
		} catch (SQLException ex) {
			this.getLogger().error(null,ex);
		}
	}
	
	public void syncProject(String projectId) {
		String id = Db.queryStr("select id from zentao.zt_project where code = ?",projectId);
		if(StringUtils.isBlank(id)) {
			try {
				JSONObject row = this.getQuery().queryForRow("select * from yq_project where project_id= ?", new Object[] {projectId}, new JSONMapperImpl());
				JSONObject result = ZendaoApi.addProject(row.getString("PROJECT_NAME"),projectId);
				if(result==null||result.isEmpty()) {
					this.getLogger().error("bug>projectId>"+projectId);
					return;
				}
				this.getLogger().info("update zentao project>"+result.toJSONString());
				id = result.getString("id");
				EasyRecord record = new EasyRecord("zentao.zt_project","id");
				record.set("id",id);
				record.set("code", projectId);
				record.set("type", "project");
				record.set("model", "scrum");
				record.set("acl", "open");
				record.set("auth", "extend");
				record.set("status","doing");
				record.set("firstEnd","2059-12-31");
				record.set("begin", EasyDate.getCurrentDateString("yyyy-MM-dd"));
				record.set("realBegan", EasyDate.getCurrentDateString("yyyy-MM-dd"));
				this.getQuery().update(record);
				
				this.getQuery().executeUpdate("update yq_project set zentao_id = ? where project_id = ?", id,projectId);
				
			} catch (SQLException e) {
				this.getLogger().error(e.getMessage(),e);
			}
		}
	}
	
	public void syncProject() {
		EasySQL sql = new EasySQL();
		sql.append("select zentao_id,project_id from yq_project where zentao_id = ''");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			for(JSONObject projectInfo:list) {
				String zentaoId = projectInfo.getString("ZENTAO_ID");
				if(StringUtils.isBlank(zentaoId)) {
					String projectId = projectInfo.getString("PROJECT_ID");
					syncProject(projectId);
				}
			}
		} catch (SQLException ex) {
			this.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	public void syncProduct() {
		EasySQL sql = new EasySQL();
		sql.append("select project_name,zentao_id,project_id from yq_project");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			for(JSONObject projectInfo:list) {
				String projectId = projectInfo.getString("PROJECT_ID");
				String id = Db.queryStr("select id from zentao.zt_product where code = ?",projectId);
				if(StringUtils.isBlank(id)) {
					ZendaoApi.addProduct(projectInfo.getString("PROJECT_NAME"),projectId);
				}
			}
		} catch (Exception ex) {
			this.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	public void updateData() {
		try {
			Db.update("update zentao.zt_project t1,yq_work.yq_project t2 set t1.`name` = t2.project_name where t1.`code` = t2.project_id");
			Db.update("update zentao.zt_product t1,yq_work.yq_project t2 set t1.`name` = t2.project_name where t1.`code` = t2.project_id");
			Db.update("update zentao.zt_project t1,yq_main.yq_staff_info t2,yq_work.yq_project t3 set t1.PM = t2.staff_no where t3.project_id = t1.`code` and t2.staff_user_id = t3.project_po and t2.account_state = 0 and t1.PM =''");
			Db.update("update zentao.zt_product t1,yq_main.yq_staff_info t2,yq_work.yq_project t3 set t1.PO = t2.staff_no where t3.project_id = t1.`code` and t2.staff_user_id = t3.project_po and t2.account_state = 0 and t1.PO =''");
			Db.update("update zentao.zt_product t1,yq_main.yq_staff_info t2,yq_work.yq_project t3 set t1.QD = t2.staff_no where t3.project_id = t1.`code` and t2.staff_user_id = t3.project_po and t2.account_state = 0 and t1.QD =''");
			Db.update("update zentao.zt_product t1,yq_main.yq_staff_info t2,yq_work.yq_project t3 set t1.RD = t2.staff_no where t3.project_id = t1.`code` and t2.staff_user_id = t3.project_po and t2.account_state = 0 and t1.RD =''");
			Db.update("update zentao.zt_project t1,yq_work.yq_project t2 set t1.`status` = 'closed',closedBy = '100133',closedDate = ?,realEnd = ? where t1.`code` = t2.project_id and t2.project_state = 30 and t1.`status` = 'doing'",EasyDate.getCurrentDateString("yyyy-MM-dd"),EasyDate.getCurrentDateString("yyyy-MM-dd"));
			Db.update("update zentao.zt_product t1,yq_work.yq_project t2 set t1.`status` = 'closed',closedDate = ? where t1.`code` = t2.project_id and t2.project_state = 30 and t1.`status` = 'normal'",EasyDate.getCurrentDateString("yyyy-MM-dd"));
			Db.update("UPDATE yq_work.yq_project t1 JOIN( SELECT t3.`code` AS project_code, COUNT(t2.id) AS bug_count FROM zentao.zt_bug t2 JOIN zentao.zt_project t3 ON t2.project = t3.id GROUP BY t3.`code`) t_sub ON t1.project_id = t_sub.project_code SET t1.bug_count = t_sub.bug_count");
		} catch (Exception e) {
			this.getLogger().error(e.getMessage(),e);
		}
	}
	
	private String getPhone(String val) {
		if(StringUtils.notBlank(val)) {
			if(val.length()>11) {
				return val.substring(0, 11);
			}
		}
		return val;
	}
	
	
	private Map<String,String> getDepts() {
		Map<String,String> depts=new HashMap<String,String>();
		try {
			List<EasyRow> list=this.getZentaoQuery().queryForList("select * from zt_dept");
			if(list!=null){
				for(EasyRow row:list){
					depts.put(row.getColumnValue("NAME"),row.getColumnValue("ID"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return depts;
	}
	
	private Map<String,String> getNewDepts() {
		Map<String,String> depts = new HashMap<String,String>();
		try {
			List<EasyRow> list = this.getQuery().queryForList("select * from zentao.zt_dept");
			if(list!=null){
				for(EasyRow row:list){
					depts.put(row.getColumnValue("NAME"),row.getColumnValue("ID"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return depts;
	}
	
}
