<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购评审</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" autocomplete="off">
   		   		<input type="hidden" value="${param.orderId}" name="orderId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
			            	<td style="width: 80px;" class="required">评审编号</td>
			            	<td>
			            		<input type="text" data-rules="required" data-mars="OrderDao.getReviewNo" name="reviewNo" class="form-control"/>
			            	</td>
			            </tr>
			            <tr>
		                    <td class="required">紧急程度</td>
	            			<td>
		                    	<label class="radio radio-success radio-inline">
			                    	<input type="radio" checked="checked" value="1" name="reviewLevel"><span>一般</span>				                    	
		                    	</label>
		                    	<label class="radio radio-success radio-inline">
			                    	<input type="radio" value="2" name="reviewLevel"> <span>最高</span>
		                    	</label>
		                    	<label class="radio radio-success radio-inline">
			                    	<input type="radio" value="3" name="reviewLevel"> <span>较高</span>
		                    	</label>
		                    	<label class="radio radio-success radio-inline">
			                    	<input type="radio" value="4" name="reviewLevel"> <span>较低</span>
		                    	</label>
		                    </td>
			            </tr>
			            <tr>
			            	<td>申请说明</td>
			            	<td>
			            		<textarea style="height: 120px" name="remark" class="form-control"></textarea>
			            	</td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 提交 </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Edit");

		$(function(){
			$("#editForm").render({success:function(){

			}});  
		});
		
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				Edit.insertData(); 
			};
		}
		
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			data['reviewState'] = 10;
			ajax.remoteCall("${ctxPath}/servlet/order/flow?action=reviewApply",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>