<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>todo list</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-calendar"></span> 我的日程安排</h5>
	          		     <div class="input-group input-group-sm" style="width: 180px">
							 <span class="input-group-addon">日程名称</span>	
							 <input type="text" name="title" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">状态</span>	
							 <select name="status" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="1">未开始</option>
		                    		<option value="2">进行中</option>
		                    		<option value="3">已完成</option>
		                    		<option value="4">已暂停</option>
	                    	</select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 新建</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/html" id="barDemo">
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		$(function(){
				list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'TodoDao.list',
					autoFill:true,
					skin:'line',
					edit:'list.editCol',
					cols: [[
					 {title:'序号',type:'numbers'},
		             {
	            	 	field: 'TITLE',
						title: '名称',
						align:'left',
						edit:'text'
					 },{
	            	 	field: '',
						title: '起始时间',
						align:'left',
						templet:'<div>{{d.BEGIN_TIME}} ~ {{d.END_TIME}}</div>'
					 },{
					    field: 'LEVEL',
						title: '优先级别',
						align:'center',
						width:100,
						templet:function(row){
							var val=row.LEVEL;
							if(val==1)return '<label class="label label-default">一般</label>';
							if(val==2)return '<label class="label label-warning">最高</label>';
							if(val==3)return '<label class="label label-danger">较高</label>';
							if(val==4)return '<label class="label label-info">较低</label>';
							return "--";
						}
					},{
					    field: 'STATUS',
						title: '状态',
						align:'center',
						style:'cursor: pointer;',
						event:'list.updateStatus',
						width:100,
						templet:function(row){
							var val=row.STATUS;
							if(val==1)return '<label class="label label-default">未开始</label>';
							if(val==2)return '<label class="label label-warning">进行中</label>';
							if(val==3)return '<label class="label label-danger">已完成</label>';
							if(val==4)return '<label class="label label-info">已暂停</label>';
							return "--";
						}
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						width:180,
						align:'center'
					},{
						title: '操作',
						align:'center',
						width:120,
						cellMinWidth:120,
						toolbar: '#barDemo'
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			updateStatus:function(data,obj){
				var id=new Date().getTime();
				layer.open( {
					  title:'修改状态',
					  content:'<select id="'+id+'" class="form-control input-sm"><option value="1">未开始</option><option value="2">进行中</option><option value="3">已完成</option><option value="4">已暂停</option></select>',
					  yes:function(index){
						  layer.close(index);
						  var val=$("#"+id).val();
						  var todoId=data.TODO_ID;
						  obj.update({
							  STATUS: val
					      });
						  ajax.remoteCall("${ctxPath}/servlet/todo?action=update",{'todo.TODO_ID':data.TODO_ID,'todo.STATUS':val},function(result) { 
								if(result.state != 1){
									layer.alert(result.msg,{icon: 5});
								}
						  });
					  }
					 
				});
			},
			editCol:function(obj){
				 var value = obj.value;
				 var data=obj.data;
				 var field = obj.field;
				 if(value){
				   ajax.remoteCall("${ctxPath}/servlet/todo?action=update",{'todo.TODO_ID':data.TODO_ID,'todo.TITLE':value},function(result) { 
						if(result.state != 1){
							layer.alert(result.msg,{icon: 5});
						}
					});
				 }
			},
			edit:function(data){
				popup.layerShow({type:1,anim:0,scrollbar:false,offset:'20px',area:['750px','500px'],url:'${ctxPath}/pages/todo/todo-edit.jsp',title:'编辑',data:{todoId:data.TODO_ID}});
			},
			add:function(){
				popup.layerShow({type:1,anim:0,scrollbar:false,offset:'20px',area:['750px','500px'],url:'${ctxPath}/pages/todo/todo-edit.jsp',title:'添加计划',data:{}});
			}
		}
		function reloadTodo(){
			list.query();
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>