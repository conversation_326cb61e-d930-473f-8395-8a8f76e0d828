package com.yunqu.work.dao.erp;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "OrderFlowDao")
public class OrderFlowDao extends AppDaoContext{
	
	@WebControl(name = "getApplyName",type = Types.TEXT)
	public JSONObject getApplyName() {
		return getJsonResult(getUserPrincipal().getUserName()+"的采购付款申请");
	}
	
	@WebControl(name = "reviewList",type=Types.PAGE)
	public JSONObject reviewList() {
		EasySQL sql = getEasySQL("select t1.*,t4.name supplier_name,t2.contract_name,t2.order_no,t2.total_price,t3.node_name,t3.user_name check_by from yq_erp_order_review t1,yq_erp_order t2,");
		sql.append("yq_erp_order_check_node t3");
		sql.append(",yq_erp_supplier t4");
		sql.append(" where 1=1");
		sql.append("and t3.node_id = t1.current_node_id");
		sql.append("and t1.order_id = t2.order_id");
		sql.append("and t2.supplier_id = t4.supplier_id");
		if(!isSuperUser()) {
			sql.append("and (");
			sql.append(getUserId(),"t1.apply_by = ?");
			sql.append("or");
			sql.append(getUserId(),"FIND_IN_SET(?,t1.check_ids)");
			sql.append(")");
		}
		sql.append(param.getString("reviewState"),"and t1.review_state = ?");
		sql.appendLike(param.getString("reviewNo"),"and t1.review_no like ?");
		sql.appendLike(param.getString("orderNo"),"and t2.order_no like ?");
		sql.appendLike(param.getString("contractNo"),"and t2.contract_no like ?");
		sql.append("order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "paymentList",type=Types.PAGE)
	public JSONObject paymentList() {
		EasySQL sql = getEasySQL("select t1.*,t4.name supplier_name,t3.node_name,t3.user_name check_by from yq_erp_order_payment t1,");
		sql.append("yq_erp_order_check_node t3");
		sql.append(",yq_erp_supplier t4");
		sql.append(" where 1=1");
		sql.append("and t3.node_id = t1.current_node_id");
		sql.append("and t1.supplier_id = t4.supplier_id");
		String orderId = param.getString("orderId");
		if(StringUtils.isBlank(orderId)){
			if(!isSuperUser()) {
				sql.append("and (");
				sql.append(getUserId(),"FIND_IN_SET(?,t1.check_ids)");
				sql.append(getUserId(),"or t1.apply_by = ?");
				sql.append(")");
			}
		}else {
			sql.append(orderId,"and FIND_IN_SET(?,t1.order_ids)");
		}
		sql.appendLike(param.getString("orderNo"),"and t1.order_nos like ?");
		sql.appendLike(param.getString("paymentNo"),"and t1.payment_no like ?");
		sql.append(param.getString("payState"),"and t1.pay_state = ?");
		sql.append(param.getString("paymentState"),"and t1.payment_state = ?");
		sql.append("order by t1.apply_time desc");
		if(StringUtils.isBlank(orderId)){
			return queryForPageList(sql.getSQL(), sql.getParams());
		}else {
			return queryForList(sql.getSQL(), sql.getParams());
		}
	}
	
	@WebControl(name = "reviewDetailList",type=Types.PAGE)
	public JSONObject reviewDetailList() {
		EasySQL sql = getEasySQL("select t4.node_name,t5.name supplier_name,t3.get_time,t3.check_desc,t3.read_time,t3.check_time,t3.node_id,t3.result_id,t3.check_result,t3.check_name,t3.check_user_id,t1.*,t2.contract_name,t2.order_no,t2.total_price from yq_erp_order_review t1");
		sql.append(",yq_erp_order t2,yq_erp_order_review_result t3,yq_erp_order_check_node t4");
		sql.append(",yq_erp_supplier t5");
		sql.append("where 1=1");
		sql.append("and t4.node_id = t3.node_id");
		sql.append("and t1.order_id = t2.order_id");
		sql.append("and t1.review_id = t3.review_id");
		sql.append("and t2.supplier_id = t5.supplier_id");
		int isOk= param.getIntValue("todo");
		if(isOk==0) {
			sql.append("and t3.check_result > 0 ");
		}else {
			sql.append("and t1.current_node_id = t3.node_id and t3.check_result = 0");
		}
		if(!isSuperUser()) {
			sql.append("and (");
			sql.append(getUserId(),"t2.create_user_id = ?");
			sql.append("or");
			sql.append(getUserId(),"t3.check_user_id = ?");
			sql.append(")");
		}
		sql.appendLike(param.getString("reviewNo"),"and t1.review_no like ?");
		sql.appendLike(param.getString("orderNo"),"and t2.order_no like ?");
		sql.appendLike(param.getString("contractNo"),"and t2.contract_no like ?");
		sql.append(param.getString("checkResult"),"and t3.check_result = ?");
		sql.append("order by t3.check_time desc,t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "paymentDetailList",type=Types.PAGE)
	public JSONObject paymentDetailList() {
		EasySQL sql = getEasySQL("select t4.node_name,t5.name supplier_name,t3.get_time,t3.read_time,t3.check_desc,t3.check_time,t3.node_id,t3.result_id,t3.check_result,t3.check_name,t3.check_user_id,t1.* from yq_erp_order_payment t1");
		sql.append(",yq_erp_order_payment_result t3,yq_erp_order_check_node t4");
		sql.append(",yq_erp_supplier t5");
		sql.append("where 1=1");
		sql.append("and t4.node_id = t3.node_id");
		sql.append("and t1.payment_id = t3.payment_id");
		sql.append("and t1.supplier_id = t5.supplier_id");
		int isOk= param.getIntValue("todo");
		if(isOk==0) {
			sql.append("and t3.check_result > 0 ");
		}else {
			sql.append("and t1.current_node_id = t3.node_id and t3.check_result = 0");
		}
		if(!isSuperUser()) {
			sql.append("and (");
			sql.append(getUserId(),"t1.apply_by = ?");
			sql.append("or");
			sql.append(getUserId(),"t3.check_user_id = ?");
			sql.append(")");
		}
		sql.append(param.getString("checkResult"),"and t3.check_result = ?");
		sql.append("order by t3.check_time desc,t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "paymentOrderList",type = Types.OTHER)
	public JSONObject paymentOrderList() {
		EasySQL sql = getEasySQL("select t2.*,t1.APPLY_REMARK,t1.has_pay_amount,t1.make_amount,t1.apply_amount,t1.has_apply_amount,t1.apply_time from yq_erp_order_payment_detail t1 ,yq_erp_order t2 where FIND_IN_SET(t2.order_id,t1.order_id) ");
		sql.append(param.getString("paymentId"),"and t1.payment_id= ?",false);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "getPaymentInfo",type = Types.RECORD)
	public JSONObject getPaymentInfo() {
		String paymentId = param.getString("paymentId");
		return queryForRecord("select * from yq_erp_order_payment where payment_id = ?", paymentId);
	}
	

	@WebControl(name = "reviewResult",type=Types.LIST)
	public JSONObject reviewResult() {
		EasySQL sql = getEasySQL("select t1.*,t2.node_name from yq_erp_order_review_result t1,yq_erp_order_check_node t2 where 1=1");
		sql.append("and t1.node_id = t2.node_id");
		sql.append(param.getString("orderId"),"and t1.order_id = ?",false);
		sql.append("order by t1.check_time,t2.check_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "paymentResult",type=Types.LIST)
	public JSONObject paymentResult() {
		EasySQL sql = getEasySQL("select t1.*,t2.node_name from yq_erp_order_payment_result t1,yq_erp_order_check_node t2 where 1=1");
		sql.append("and t1.node_id = t2.node_id");
		sql.append(param.getString("paymentId"),"and t1.payment_id = ?",false);
		sql.append("order by t2.check_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeConfig",type=Types.LIST)
	public JSONObject nodeConfig() {
		EasySQL sql = getEasySQL("select t1.* from yq_erp_order_check_node t1 where 1=1");
		sql.append(param.getString("busiType"),"and t1.busi_type = ?",false);
		sql.append("order by t1.check_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "reviewInfo",type=Types.RECORD)
	public JSONObject reviewInfo() {
		EasySQL sql = getEasySQL("select t1.* from yq_erp_order_review t1 where 1=1");
		sql.append(param.getString("orderId"),"and t1.order_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeInfo",type=Types.RECORD)
	public JSONObject nodeInfo() {
		EasySQL sql = getEasySQL("select t1.* from yq_erp_order_check_node t1 where 1=1");
		sql.append(param.getString("nodeId"),"and t1.node_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeDict",type=Types.DICT)
	public JSONObject nodeDict() {
		EasySQL sql = getEasySQL("select t1.node_id,concat(t1.node_name,'|',t1.user_name) from yq_erp_order_check_node t1 where 1=1");
		sql.append(param.getString("busiType"),"and t1.busi_type = ?",false);
		sql.append(param.getString("nodeId"),"and t1.node_id <> ?");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}

}
