package com.yunqu.work.service;

import java.sql.SQLException;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.CommentModel;

public class CommentService extends AppBaseService{
	private static class Holder{
		private static CommentService service=new CommentService();
	}
	public static CommentService getService(){
		return Holder.service;
	}
	public EasyResult addComment(CommentModel model){
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"提交成功.");
	}
	
	public EasyResult addComment(String opBy,String fkId,String content,String ip,String userAgent){
		return addComment(opBy, null, fkId, content, ip, userAgent,"");
	}
	
	public EasyResult addComment(String opBy,String opName,String fkId,String content,String ip,String userAgent,String source){
		CommentModel model=new CommentModel();
		model.setFkId(fkId);
		model.setCreator(opBy);
		model.setPrimaryValues(RandomKit.uuid());
		model.setContent(content);
		model.setIp(ip);
		if(opName!=null) {
			model.setCreateName(opName);
		}
		model.setUserAgent(userAgent);
		model.setCreateTime(EasyDate.getCurrentDateString());
		model.set("DATE_ID",EasyCalendar.newInstance().getDateInt());
		model.set("source",source);
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"提交成功.");
	}

}
