package com.yunqu.work.dao.ehr;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;

@WebObject(name="KqDao")
public class KqDao extends AppDaoContext {

	@WebControl(name="batchData",type=Types.PAGE)
	public JSONObject batchData(){
		EasySQL sql = getEasySQL("select * from yq_kq_batch where 1=1");
		sql.append("order by upload_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="report",type=Types.LIST)
	public JSONObject report(){
		EasySQL sql = getEasySQL("select DEPT,USER_NAME,STAFF_ID,SUM(TIME_TO_SEC(LATE_TIME)/60) LATE_TIME, SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) AS LATE_TIME_COUNT, SUM(TIME_TO_SEC(OVER_TIME)/60) OVER_TIME, SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) AS OVER_TIME_COUNT, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) = 5 THEN 1 ELSE 0 END) AS lose_dk, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) = 0 THEN 1 ELSE 0 END) AS not_work, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) <> 0 THEN 1 ELSE 0 END) AS work_day from yq_kq_data where 1=1");
		this.setCondition(sql);
		sql.append("GROUP BY USER_ID");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="deptReport",type=Types.LIST)
	public JSONObject deptReport(){
		EasySQL sql = getEasySQL("select dept,dept_Id,count(DISTINCT(staff_id)) staff_count,SUM(TIME_TO_SEC(LATE_TIME)/60) LATE_TIME, SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) AS LATE_TIME_COUNT, SUM(TIME_TO_SEC(OVER_TIME)/60) OVER_TIME, SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) AS OVER_TIME_COUNT, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) = 5 THEN 1 ELSE 0 END) AS lose_dk, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) = 0 THEN 1 ELSE 0 END) AS not_work, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) <> 0 THEN 1 ELSE 0 END) AS work_day from yq_kq_data where 1=1");
		String beginDate = param.getString("beginDate");
		String endDate = param.getString("endDate");
		if(StringUtils.notBlank(beginDate)) {
			sql.append(beginDate.replaceAll("-",""),"and DK_DATE >= ?");
		}
		if(StringUtils.notBlank(endDate)) {
			sql.append(endDate.replaceAll("-",""),"and DK_DATE <= ?");
		}
		sql.append("GROUP BY dept");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="kqPeopleRecord",type=Types.RECORD)
	public JSONObject kqPeopleRecord(){
		String kqUserId = param.getString("kqUserId");
		EasySQL sql = getEasySQL("select t1.* from yq_kq_people t1  where 1=1");
		if(StringUtils.isBlank(kqUserId)) {
			return getJsonResult(new JSONObject());
		}
		sql.append(kqUserId,"and t1.kq_user_id = ?");
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="kqPeople",type=Types.PAGE)
	public JSONObject kqPeople(){
		EasySQL sql = getEasySQL("select t1.*,t2.work_area,t2.often_work_city,t2.job_title from yq_kq_people t1 inner join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2  on t1.kq_user_id = t2.staff_user_id where 1=1");
		sql.append("and t2.account_state = 0");
		sql.appendLike(param.getString("userName"),"and t1.kq_user_name like ?");
		sql.appendLike(param.getString("deptName"),"and t1.dept_name like ?");
		sql.appendLike(param.getString("jobName"),"and t2.job_title like ?");
		sql.appendLike(param.getString("workArea"),"and t2.work_area like ?");
		sql.appendLike(param.getString("oftenWorkCity"),"and t2.often_work_city like ?");
		sql.append(param.getInteger("state"),"and t1.state = ?");
		sql.append("order by t2.staff_no");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql = getEasySQL("select * from yq_kq_data where 1=1");
		this.setCondition(sql);
		sql.append(" order by DK_DATE desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="wxDkList",type=Types.LIST)
	public JSONObject wxDkList(){
		EasySQL sql = getEasySQL("select * from yq_kq_obj where 1=1");
		this.setWxCondition(sql);
		sql.append(" order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="recentRecord",type=Types.LIST) 
	public JSONObject recentRecord(){
		EasySQL sql = getEasySQL("select * from YQ_KQ_OBJ where 1=1");
		sql.append(getUserId(),"and user_Id = ?");
		sql.append("and DK_DATE >= date_format(date_sub(now(),interval 1 month),'%Y%m%d')");
		sql.append("order by DK_DATE desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 微信考勤表 按员工查询报表
	 */
	@WebControl(name="wxKqReport",type=Types.LIST)
	public JSONObject wxKqReport(){
		EasySQL sql = getEasySQL("select DEPT_NAME,USER_NAME,STAFF_ID,SUM(LATE_TIME) LATE_TIME, SUM(CASE WHEN LATE_TIME<>'' THEN 1 ELSE 0 END) AS LATE_TIME_COUNT, SUM(OVER_TIME) OVER_TIME, SUM(CASE WHEN OVER_TIME<>'' THEN 1 ELSE 0 END) AS OVER_TIME_COUNT, SUM(CASE WHEN length(concat(SIGN_IN,SIGN_OUT)) = 5 THEN 1 ELSE 0 END) AS lose_dk, SUM(CASE WHEN NOT_WORK='Y' THEN 1 ELSE 0 END) AS not_work, COUNT(1) AS work_day, SUM(CASE WHEN NOT_WORK = 'N' THEN 1 ELSE 0 END) WORK_DO_DAY from yq_kq_obj where 1=1");
		this.setWxCondition(sql);
		sql.append("GROUP BY USER_ID");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	private void setWxCondition(EasySQL sql) {
		sql.append(param.getString("dateId"),"and date_id = ?");
		
		String monthId = param.getString("monthId");
		if(StringUtils.notBlank(monthId)) {
			sql.append(monthId.replaceAll("-", ""),"and month_id = ?");
		}
		String lateFlag = param.getString("lateFlag");
		if(StringUtils.notBlank(lateFlag)) {
			sql.append("and LATE_TIME <>''");
		}
		String missedPunchFlag = param.getString("missedPunchFlag");
		if(StringUtils.notBlank(missedPunchFlag)) {
			sql.append("and length(concat(SIGN_IN,SIGN_OUT)) = 5 and REMARK = ''");
		}
		String absenteeismFlag = param.getString("absenteeismFlag");
		if(StringUtils.notBlank(absenteeismFlag)) {
			sql.append("Y","and NOT_WORK = ? and REMARK = ''");
		}
		sql.appendRLike(param.getString("kqCity"),"and kq_city like ?");
		sql.appendRLike(param.getString("userName"),"and user_name like ?");
		sql.appendRLike(param.getString("deptName"),"and dept_name like ?");
		sql.append(param.getString("applyNo"),"and apply_no = ?");
		sql.append(param.getString("remark"),"and REMARK = ?");

		String hrFlag = param.getString("hrFlag");
		if("1".equals(hrFlag)&&(hasRole("HR_MGR")||isSuperUser()||isDeptLeader())) {
			
		}else if(hasRole("DEPT_MGR")) {
			sql.appendIn(getMgrDeptArray(),"and dept_Id");
		}else{
			sql.append(getUserId(),"and user_Id = ?");
		}
				
		String beginDate = param.getString("beginDate");
		String endDate = param.getString("endDate");
		
		if(StringUtils.notBlank(beginDate)) {
			sql.append(beginDate.replaceAll("-",""),"and DK_DATE >= ?");
		}
		if(StringUtils.notBlank(endDate)) {
			sql.append(endDate.replaceAll("-",""),"and DK_DATE <= ?");
		}
	}

	private void setCondition(EasySQL sql) {		
		sql.append(param.getString("dateId"),"and date_id = ?");
		String monthId = param.getString("monthId");
		if(StringUtils.notBlank(monthId)) {
			sql.append(monthId.replaceAll("-", ""),"and month_id = ?");
		}
		String lateFlag = param.getString("lateFlag");
		if(StringUtils.notBlank(lateFlag)) {
			sql.append("and LATE_TIME <>''");
		}
		String missedPunchFlag = param.getString("missedPunchFlag");
		if(StringUtils.notBlank(missedPunchFlag)) {
			sql.append("and length(concat(SIGN_IN,SIGN_OUT)) = 5");
		}
		String absenteeismFlag = param.getString("absenteeismFlag");
		if(StringUtils.notBlank(absenteeismFlag)) {
			sql.append("and length(concat(SIGN_IN,SIGN_OUT)) = 0");
		}
		sql.appendLike(param.getString("userName"),"and user_name like ?");
		sql.appendLike(param.getString("deptName"),"and dept like ?");
		
		String batchId = param.getString("batchId");
		
		if(StringUtils.notBlank(batchId)) {
			sql.append(batchId,"and batch_id = ?");
			sql.append(param.getString("userId"),"and user_Id = ?");
		}else {
			String hrFlag = param.getString("hrFlag");
			if("1".equals(hrFlag)&&(hasRole("HR_MGR")||isSuperUser()||isDeptLeader())) {
			
			}else if(hasRole("DEPT_MGR")) {
				sql.appendIn(getMgrDeptArray(),"and dept_Id");
			}else{
				sql.append(getUserId(),"and user_Id = ?");
			}
		}
		
		String beginDate = param.getString("beginDate");
		String endDate = param.getString("endDate");
		
		if(StringUtils.notBlank(beginDate)) {
			sql.append(beginDate.replaceAll("-",""),"and DK_DATE >= ?");
		}
		if(StringUtils.notBlank(endDate)) {
			sql.append(endDate.replaceAll("-",""),"and DK_DATE <= ?");
		}
	}
}
