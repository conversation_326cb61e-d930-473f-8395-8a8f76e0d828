<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>点餐管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form name="searchForm" autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-cutlery"></span> 点餐管理</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">时间</span>	
							 <input type="text" name="date" onClick="WdatePicker({dateFmt:'yyyyMMdd'})" data-mars="CommonDao.date03" data-mars-top="true" class="form-control input-sm Wdate" style="width: 100px;">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">姓名</span>	
							 <input type="text" name="userName" class="form-control input-sm" style="width: 90px;">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">菜品名</span>	
							 <input type="text" name="foodName" class="form-control input-sm" style="width: 90px;">
					     </div>
	        		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">类型</span>	
							 <select name="dinnerType" class="form-control input-sm" onchange="list.query()">
							 	<option value="2" selected="selected">中餐</option>
							 	<option value="3">晚餐</option>
							 </select>
					     </div>
	        		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">点餐状态</span>	
							 <select name="state" class="form-control input-sm" onchange="list.query()">
							 	<option value="0" selected="selected">已确认</option>
							 	<option value="1">预约待确认</option>
							 	<option value="2">不点</option>
							 </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						  <div class="input-group input-group-sm pull-right">
							  <script type="text/html" id="taskBar">
								 <button type="button" class="btn btn-sm btn-success mr-15" onclick="list.add()">补录</button>
								 <button type="button" class="btn btn-sm btn-info mr-15" lay-event="list.batchDel">批量取消</button>
								 <button type="button" class="btn btn-sm btn-default mr-15" lay-event="list.qwNotice">企微提醒</button>
							  </script>
							 <button type="button" class="btn btn-sm btn-info mr-15" onclick="list.setting()">订餐时间设置</button>
							 <button type="button" class="btn btn-sm btn-default" onclick="list.eatNotice()">用餐通知</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						<table class="layui-hide" id="t"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
			{{if STATE==0}}
  				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:list.eatNotice('{{:USER_ID}}')">提醒</a>
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.del">取消</a>
			  {{else STATE==1}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.ok">确定订餐</a>
			  {{else}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.ok">确定订餐</a>
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.del">取消</a>
			{{/if}}
		</script>
		
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'FoodDao.bookMgr',
					id:'t',
					height:'full-90',
					toolbar:'#taskBar',
					limit:100,
					limits:[10,30,50,100,150,200],
					cols: [[
					  {type:'checkbox',width:50},
					  {title:'序号',type:'numbers',width:50},
					  {
					    field: 'USERNAME',
						title: '姓名',
						align:'left'
					  },{
					    field: 'STAFF_NO',
						title: '工号',
						align:'center'
				     },{
					    field: 'DEPTS',
						title: '部门',
						align:'center'
					   },
					  {
					    field: 'FOOD_NAME',
						title: '菜单名',
						align:'left'
				     },{
					    field: 'CREATE_TIME',
						title: '点餐时间',
						width:180,
						align:'center',
					  },{
					    field: 'DINNER_TYPE',
						title: '类型',
						align:'center',
						templet:function(row){
							if(row.DINNER_TYPE==1){
								return '早餐';
							}
							if(row.DINNER_TYPE==2){
								return '中餐';
							}
							if(row.DINNER_TYPE==3){
								return '晚餐';
							}
							return '';
						}
					},{
						title: '操作',
						align:'center',
						width:150,
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				 ]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,area:['450px','350px'],url:'${ctxPath}/pages/food/book-add.jsp',title:'添加菜单'});
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,area:['450px','350px'],url:'${ctxPath}/pages/food/food-edit.jsp',title:'编辑',data:{foodId:data.ID}});
			},
			del:function(data){
				layer.confirm("确认要取消吗?",{icon:3,offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/food?action=cannelBook",{'id':data.ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			batchDel:function(list){
				var ids = [];
				for(var index in list){
					ids.push(list[index]['ID']);
				}
				layer.confirm("确认要取消吗?",{icon:3,offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/servlet/food?action=batchCannelBook",{'ids':ids.join(',')},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			qwNotice:function(data){
				ajax.remoteCall("${ctxPath}/servlet/food?action=notice",{},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
			   });
			},
			ok:function(data){
					ajax.remoteCall("${ctxPath}/servlet/food?action=bookPlanOk",{'id':data.ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
				});
			},
			setting:function(){
				popup.layerShow({type:1,area:['350px','400px'],url:'${ctxPath}/pages/food/food-dc-time.jsp',title:'订餐时间设置'});
			},
			eatNotice:function(userId){
				userId = userId || '';
				layer.prompt({title:'请输入推送内容',formType:2,value:'祝您用餐愉快,吃肉开心！',maxlength:200},function(text,index){
					layer.close(index);
					ajax.remoteCall("${ctxPath}/servlet/food?action=foodServedMsg",{remark:text,userId:userId},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
				  });
				});
			}
			
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>