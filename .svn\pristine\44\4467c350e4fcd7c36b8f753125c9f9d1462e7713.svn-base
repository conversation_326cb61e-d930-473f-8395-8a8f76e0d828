<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
	<style>
		.layui-badge{left: -1px!important;}
	</style>
	<style>
		.layui-table-cell{padding: 0 6px;}
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>任务统计(项目维度)</h5>
	          		     <div class="input-group input-group-sm"  style="width: 180px">
							 <span class="input-group-addon">任务名称</span>	
							 <input type="text" name="taskName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm"  style="width: 150px">
							 <span class="input-group-addon">任务类型</span>	
							<select data-mars-reload="false" name="taskTypeId" onchange="list.query()" data-mars="TaskDao.taskType" class="form-control">
								<option value="">请选择</option>
							</select>
					     </div>
					      <div class="input-group input-group-sm"  style="width: 160px">
							 <span class="input-group-addon">项目</span>	
							 <input type="hidden" name="projectId" class="form-control input-sm">
							 <input type="text" id="projectId" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var taskTable= {
				mars:'TaskDao.projectTaskStat',
				skin:'line',
				limit:15,
				limits:[10,15,30,50,100,200,300],
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
				    field: 'PROJECT_NAME',
					title: '项目名称',
					align:'left',
					event:'list.detail',
					style:'color:#1E9FFF;cursor:pointer'
				},{
				    field: 'E',
					title: '总任务数',
					align:'center',
					width:90,
					sort:true
				},{
				    field: 'A',
					title: '待办数',
					align:'center',
					width:90,
					sort:true
				}
				 ,{
				    field: 'B',
					title: '进行中',
					width:90,
					sort:true,
					align:'center'
				}
			  ,{
				   field: 'C',
				   title: '已完成',
				   sort:true,
					width:90,
				   align:'center'
			},{
				   field: 'D',
				   title: '已验收',
					width:90,
				   sort:true,
				   align:'center'
				}
			]]};
		var list={
			init:function(){
				$("#searchForm").initTable($.extend(taskTable,{id:'list'},{done:function(result){

				}}));
			},
			detail:function(data){
				popup.openTab({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/project/project-detail.jsp',title:'项目总览',data:{projectId:data.PROJECT_ID}});
			},
			query:function(){
				$("#searchForm").queryData({id:'list'});
			},
		}
		$(function(){
			layui.config({
				  base: '${ctxPath}/static/js/'
			}).use('tableSelect'); //加载自定义模块
			
			layui.use(['element','tableSelect'], function(){
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#projectId',
					searchKey: 'projectName',
					checkedKey: 'PROJECT_ID',
					page:true,
					searchPlaceholder: '请输入项目名称',
					table: {
						mars: 'ProjectContractDao.projectList',
						cols: [[
							{ type: 'radio' },
							{ field: 'PROJECT_ID',hide:true,title: 'ID'},
							{ field: 'PROJECT_NAME', title: '名称', width: 320 },
							{ field: 'PROJECT_NO', title: '编号', width: 150 }
						]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.PROJECT_NAME)
							ids.push(item.PROJECT_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
					}
				});
			});
			
			list.init();
			$("#searchForm").render();
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>