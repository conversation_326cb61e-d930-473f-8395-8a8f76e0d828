<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">加班类别</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data1">
						  				<option value="">--</option>
						  				<option value="工作日加班">工作日加班</option>
						  				<option value="周末加班">周末加班 </option>
						  				<option value="法定节假日加班">法定节假日加班</option>
						  			</select>
					  			</td>
					  			<td class="required">申请加班时间</td>
					  			<td style="width: 40%;">
					  				<input type="text" style="width: 30%;display: inline-block;" data-rules="required" onchange="calcDay()" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 09:00:00',minTime:'9:00',maxTime:'18:00',doubleCalendar:true,alwaysUseStartDate:true,maxDate:'#F{$dp.$D(\'dateEnd\')}'})" id="dateStart" class="form-control input-sm Wdate" name="apply.data2"/>
					  				<input type="text" style="width: 30%;display: inline-block;" data-rules="required" onchange="calcDay()" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 18:00:00',minTime:'9:00',maxTime:'18:00',doubleCalendar:true,alwaysUseStartDate:true,minDate:'#F{$dp.$D(\'dateStart\')}'})" id="dateEnd" class="form-control input-sm Wdate" name="apply.data3"/>
					  				<input type="number"  readonly="readonly" style="width: 35%;display: inline-block;" data-edit-node="考勤备案" data-rules="required" placeholder="自动计算"  class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>所属项目</td>
					  			<td>
					  				<input name="apply.data5" type="hidden">
									<input type="text" onclick="singleContract(this)" readonly="readonly" data-rules="required" name="apply.data4" class="form-control input-sm"/>
					  			</td>
					  			<td>证明人</td>
					  			<td>
					  				 <input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">加班事由</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>相关附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/yq-work/static/js/datediff.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			var params = {};
			FlowCore.addCheckOrder({data:params});
		}
		
		function selctCallBack(){
			
		}

		function calcDay(){
			var start = $("[name='apply.data2']").val();
			var end = $("[name='apply.data3']").val();
			if(start&&end){
				if(end<=start){
					layer.msg('结束日期应该小于开始日期',{icon:2});
					$("[name='apply.data3']").val('');
					return;
				}
				var val = dateDiff.diffAllDay(start,end);
				$("[name='apply.data7']").val(val);
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>