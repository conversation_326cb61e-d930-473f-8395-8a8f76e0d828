<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>首页</title>
	<meta name="viewport" content="initial-scale=1.0, user-scalable=no"> 
	<link href="${ctxPath}/static/css/admin.css" rel="stylesheet">
	<link href="//at.alicdn.com/t/font_1002403_34h7imibmwx.css" rel="stylesheet">
	<style>
		  .todayInfo p{line-height: 30px;color: #666;}
		 .flowApply .layui-elem-field legend{font-size: 16px;}
		 .flowApply .btn{text-align: left;}
		 .flowApply .layui-icon{font-size: 12px;}
		 .flowApply .layui-field-title{margin-bottom: 10px;}
		 .layui-text p { margin: 0px 0;}
		 .layui-card .layui-tab-brief .layui-tab-content {padding: 2px 5px;}
    	 p a{text-decoration: none!important;color: #444444!important;font-size: 14px;}
    	 .layuiadmin-card-status li h3{font-size:16px;}
    	 .tw-list{padding: 0px 0px 10px!important;}
    	 .tw-list [data-w-e]{width: 24px;}
    	 .tw-list span img{max-width: 90%!important;margin: 0 2px;}
    	 .layuiadmin-card-link a{width: auto;padding:0px 10px}
    	 .layuiadmin-card-text .layui-text-top i{font-size: 22px;}
    	 .layui-card{margin-bottom: 10px;}
    	 .table-vzebra tbody > tr > td:nth-child(2n+1), .table-vzebra tbody > tr > th:nth-child(2n+1){min-width: 30px;}
    	 .top-nav-card a{display: block;color: #fff;line-height: 70px;text-align: center;}
    	 .top-nav-card a:hover{color: #fff;font-weight: 700;}
    	  /** 卡片轮播图样式 */
        .admin-carousel .layui-carousel-ind {
            position: absolute;
            top: -41px;
            text-align: right;
        }

        .admin-carousel .layui-carousel-ind ul {
            background: 0 0;
        }

        .admin-carousel .layui-carousel-ind li {
            background-color: #e2e2e2;
        }

        .admin-carousel .layui-carousel-ind li.layui-this {
            background-color: #999;
        }

        /** 广告位轮播图 */
        .admin-news .layui-carousel-ind {
            height: 45px;
        }

        .admin-news a {
            display: block;
            line-height: 70px;
            text-align: center;
        }
        /**end*/
        
    	 .layui-tab-item{padding: 0px;}
    	 
    	 .userSign .hideUser{display: none;}
    	 .userSign img {
			height: 52px;width: 52px;border-radius:50%;
		 }
		 #todaySay a{color:#5bc0de!important;}
		 
	</style>
</EasyTag:override>
<EasyTag:override name="content">
    <div class="layui-row layui-col-space10">
       <div class="layui-col-md8">
      		<div class="layui-card">
	             <div class="layui-card-body" style="min-height: 124px;padding: 10px 10px;">
	             		<div class="layui-col-md12 layui-col-xs12">
	             			<div class="pull-left ml-15" style="margin-top: 5px;">
	             				 <span class="layui-hide-xs">
	             				 <c:choose>
			                  	 	<c:when test="${openId==''}">
			                  	 	         您没绑定微信  <a href="javascript:void(0)" onclick="bindWx();" style="padding-left: 15px;">立即绑定</a> 
			                  	 	</c:when>
			                  	 	<c:otherwise>
					                     ${staffInfo.nikeName} <a href="javascript:void(0)" onclick="bindWx();" style="padding-left: 5px;font-size: 12px;">更绑微信</a> 
			                  	 	</c:otherwise>
			                  	 </c:choose>
			                  	 </span>
			                  	 <span class="ml-20"> ${staffInfo.userName} / ${staffInfo.deptName} /${staffInfo.post} /${staffInfo.workCity}</span>  <span class="layui-hide-xs ml-20">入职日期：<span id="joinDate">${staffInfo.joinDate}</span> <span class="label label-info label-outline ml-5" id="joinDateDesc"></span></span>
		             			  <p class="layui-hide-xs"> 
				                  	
				                  	 <span class="layui-hide-xs" id="todaySay">本平台已全面接入deepseek-chat-v3-0324模型</span>
				                  	 <span class="text-info ml-10 mr-10">当前在线人数：${onlineCount }</span>
				                  	 <a class="layui-hide-xs ml-20" onclick="popup.openTab({url:'/yq-admin/pages/log/login-log.jsp',title:'登录日志',id:'loginLog'})" href="javascript:;">登录日志</a>
				                  	 <a class="layui-hide-xs ml-20" onclick="wxds();" href="javascript:;"><i class="layui-icon layui-icon-praise"></i> 点赞开发者</a>
		                		  </p>
		                		  <p>
		                		    <span class="layui-hide-xs" id="todaySay">${zaoanContent}</span>
		                		  </p>
	             			</div>
	             		</div>
	             </div>
             </div>
      </div>
      <div class="layui-col-md4">
         <div class="layui-card">
          <div class="layui-card-body top-nav-card" style="min-height: 124px;padding: 40px 15px 15px 10px;">
            <div class="layui-carousel admin-carousel admin-news layuiadmin-card-link" id="carousel-nav2">
            	<div carousel-item>
	           		<!-- <div style="color:#fff;background-color: #009fde;background-image: linear-gradient(to right, #009fde, #555858);border-radius:6px;">
	             		 <a target="_blank" href="/yq-work/web/kqTop" rel="noreferrer">‌2024年YQ考勤数据分析</a>
					</div> -->
	           		<div style="color:#fff;background-color: #009fde;background-image: linear-gradient(to right, #009fde, #555858);border-radius:6px;">
	             		 <a target="_blank" href="https://uuyc.163.com/download/">卸载Todesk，拥抱网易UU远程</a>
					</div>
	           		<div style="color:#fff;background-color: #009fde;background-image: linear-gradient(to right, #009fde, #555858);border-radius:6px;">
	             		 <a target="_blank" href="/yq-work/fileview/888c2ce177314ac1937b1c69771b83a2?view=online">生成式人工智能应用发展报告（2024）</a>
					</div>
	           		<div style="color:#fff;background-color: #009fde;background-image: linear-gradient(to right, #009fde, #555858);border-radius:6px;">
	             		 <a target="_blank" href="http://172.16.68.159:6688/ui/chat/ba97d0e104af28a3">云趣OA本地大模型知识库</a>
					</div>
	           		<div style="color:#fff;background-color: #009fde;background-image: linear-gradient(to right, #009fde, #555858);border-radius:6px;">
	             		 <a target="_blank" href="/yq-work/fly/docs/7076VLNZ03m5">关于2025年节假日放假安排的通知</a>
					</div>
	           		<div style="color:#fff;background-color: #009fde;background-image: linear-gradient(to right, #009fde, #555858);border-radius:6px;">
	             		 <a target="_blank" href="https://www.superclueai.com/" rel="noreferrer">通用大模型SuperCLUE总排行榜</a>
					</div>
            	</div>
            </div>
          </div>
         </div>
      </div>
      <div class="layui-col-md8 layui-hide-xs">
     	<div class="layui-card">
             <div class="layui-card-body" style="min-height: 140px;padding: 15px 30px;">
             	<ul class="layui-row layui-col-space10">
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void()" onclick="addressList()">
                          <i class="layui-icon iconfont icon-tongxunlu"></i>
                          <cite>通讯录</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="myFa()">
                          <i class="layui-icon iconfont icon-renwu"></i>
                          <cite>固定资产</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)"  onclick="applyFlow('')">
                          <i class="layui-icon iconfont icon-OAliuchengguanli"></i>
                          <cite>发起流程</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="addWeekly()">
                          <i class="layui-icon iconfont icon-zhoubao"></i>
                          <cite>填写周报</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void()" onclick="moreDoc()">
                          <i class="layui-icon iconfont icon-zhishiku"></i>
                          <cite>文档</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a  href="/yq-work/fly" target="_blank">
                          <i class="layui-icon layui-icon-chat"></i>
                          <cite style="color: red;font-size: 16px;">乐享</cite>
                        </a>
<!--                         <a  href="javascript:void(0)" onclick="newMsg();">
                          <i class="layui-icon layui-icon-chat"></i>
                          <cite>未读消息</cite>
                        </a> -->
                      </li>
                    </ul>
             		<ul class="layui-row layui-col-space10">
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="http://*************/yq-work/auth/memos" target="_blank">
                          <i class="layui-icon iconfont icon-renwu"></i>
                          <cite>备忘录 <span class="layui-badge-dot"></span></cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="http://*************:1443/" target="_blank">
                          <i class="layui-icon iconfont icon-renwu"></i>
                          <cite>文件分享 <span class="layui-badge-dot"></span></cite>
                        </a>
                      </li>
<!--                       <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="kanban()">
                          <i class="layui-icon iconfont icon-renwu"></i>
                          <cite>看板</cite>
                        </a>
                      </li> -->
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void()" onclick="zendao()">
                          <i class="layui-icon iconfont icon-chandao"></i>
                          <cite>禅道</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="https://yzj.quyun.yunqu-info.cn/authserver/login" target="_blank">
                          <i class="layui-icon iconfont icon-qiyeyunzongji"></i>
                          <cite>云总机</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="moreTask()">
                          <i class="layui-icon iconfont icon-renwu"></i>
                          <cite>任务</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="/easitline-finder/sso.jsp" target="_blank">
                          <i class="layui-icon iconfont icon-xiazai"></i>
                          <cite>下载</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void()" onclick="moreDc()">
                          <i class="layui-icon iconfont icon-dingcan"></i>
                          <cite>订餐</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="moreTodo()">
                          <i class="layui-icon iconfont icon-richeng"></i>
                          <cite>日程</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="moreNote()">
                          <i class="layui-icon iconfont icon-shenhebijijishibenxiezi"></i>
                          <cite>笔记</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="moreNav()">
                          <i class="layui-icon iconfont icon-wangzhidaohang"></i>
                          <cite>我的网址</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="https://getquicker.net/" target="_blank">
                          <i class="layui-icon layui-icon-windows"></i>
                          <cite>quicker</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="https://www.getxf.cn/" target="_blank">
                          <i class="layui-icon layui-icon-edit"></i>
                          <cite>网站导航</cite> <span class="layui-badge-dot"></span>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="${ctxPath}/servlet/workbench?action=toEmail" target="_blank">
                          <cite>未读邮件</cite> <span id="emailNum">0</span>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="/yq-work/affair" target="_blank">
                          <cite>未读事务</cite> <span id="affairNum"></span>
                        </a>
                      </li>
                      <c:if test="${devLead=='1'}">
                        <li class="layui-col-md2 layui-col-sm3" id="kqTop">
                          <a href="/yq-work/web/kqTop" target="_blank">
                            <cite style="font-weight: bold;">年度考勤报告</cite> <span class="layui-badge-dot"></span>
                          </a>
                        </li>
                     </c:if>
                     <li class="layui-col-md4 layui-col-sm4">
                        <a href="/llm-guide.html" target="_blank">
                          <i class="layui-icon layui-icon-edit"></i>
                          <cite>AI大模型应用入门实战与进阶</cite>
                        </a>
                     </li>
                     <li class="layui-col-md2 layui-col-sm2">
                        <a href="https://www.yuque.com/rocky2171/ai/cursor" target="_blank">
                          <i class="layui-icon layui-icon-edit"></i>
                          <cite>Cursor-AI编码</cite>
                        </a>
                     </li>
             	</ul>
             </div>
         </div>
      </div>
      <div class="layui-col-md4 layui-hide-xs render-1">
     	<div class="layui-card"  data-mars="HomeDao.waitDo">
              <div class="layui-card-body" style="min-height: 160px;padding: 15px 20px;">
              		<div class="layui-row">
	             		<div class="layui-col-md4">
	             		  <a class="layadmin-backlog-body" href="javascript:void(0)" onclick="moreTask()">
	                       	 <span>未办任务数</span><br>
	                         <cite class="f-16" id="taskNum">0</cite>
	                      </a>
	             		</div>
	             		<div class="layui-col-md4">
	             			<a class="layadmin-backlog-body" href="javascript:void(0)" onclick="weekly()">
	                          <span>周报未阅数</span><br>
	                          <cite id="weeklyNum">0</cite>
	                      </a>
	             		</div>
	             		<div class="layui-col-md4">
	             			<a class="layadmin-backlog-body" href="javascript:void(0)" onclick="popup.openTab({url:'/yq-work/pages/flow/my/flow-todo.jsp',id:'flow_my_todo',title:'我的待办'})">
	                         <span>流程未办数</span><br>
	                         <cite id="flow_my_todo">0</cite>
	                       </a>
	             		</div>
              		</div>
              		<div class="layui-row mt-15">
	             		<div class="layui-col-md4">
	             			 <a class="layadmin-backlog-body" href="javascript:void(0)" onclick="popup.openTab({url:'/yq-work/pages/flow/my/flow-list.jsp',id:'flow_my_apply',title:'我的申请'})">
	                         <span>流程申请数</span><br>
	                         <cite id="flow_my_apply">0</cite>
	                       </a>
	             		</div> 
	             		<div class="layui-col-md4">
	                       <a  class="layadmin-backlog-body" href="javascript:void(0)" onclick="popup.openTab({url:'/yq-work/pages/flow/my/flow-done.jsp',id:'flow_my_done',title:'已办流程'})">
	                         <span>流程已办数</span><br>
	                         <cite id="flow_my_done">0</cite>
	                       </a>
	             		</div>
	             		<div class="layui-col-md4">
	           				  <a class="layadmin-backlog-body" href="javascript:void(0)" onclick="popup.openTab({url:'/yq-work/pages/flow/my/flow-cc.jsp',id:'flow_cc',title:'抄送流程'})">
		                        <span>抄送流程数</span><br>
		                        <cite id="flow_my_cc">0</cite>
	                         </a>
	             		</div>
              		</div>
             </div>
         </div>
      </div>
      <div class="layui-col-md8">
        <div class="layui-row layui-col-space10">
          <div class="layui-col-md12 render-2">
          	 <div class="layui-card">
              <div class="layui-tab layui-tab-brief layadmin-latestData flowList" lay-filter="flowFilter">
                <ul class="layui-tab-title">
                  <li data-val="todo" class="layui-this">待办流程</li>
                  <li data-val="apply">我的申请</li>
                  <li data-val="done">已办流程</li>
                  <li data-val="cc">抄送流程</li>
                </ul>
                <div class="layui-tab-content ">
                  <div data-ref-flow="todo" class="layui-tab-item layui-show">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="flow-toto-template" data-mars="FlowDao.myFlowTodo" style="width: 100%;"></ul>
         		  </div>
                  <script id="flow-toto-template" type="text/x-jsrender">
						 {{if data.length==0}}<li><p style="text-align:center;">暂无流程</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> &nbsp;<a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowtodo')">{{:APPLY_NO}}_{{:FLOW_NAME}}_{{:APPLY_TITLE}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{:APPLY_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:APPLY_TIME}}</span>
			                <span class="ml-10"><i class="fa fa-calendar-check-o"></i> {{:NODE_NAME}}</span>
			                <a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowtodo')" class="layui-btn layui-btn-xs layuiadmin-reply">办理</a>
			              </li>
			              {{/for}}
 						  {{if data.length>=8}}<li style="text-align:center;"><a href="javascript:void(0);" onclick="flowMore('myFlowtodo')">查看更多</a></li>{{/if}}
				 </script>
                  <div data-ref-flow="apply" class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="flow-my-template" data-mars="FlowDao.myFlowApply" style="width: 100%;"></ul>
         		  </div>
                  <script id="flow-my-template" type="text/x-jsrender">
						 {{if data.length==0}}<li><p style="text-align:center;">暂无流程</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> &nbsp;<a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowApply')">{{:APPLY_NO}}_{{:FLOW_NAME}}_{{:APPLY_TITLE}}</a></p>
			                <span class="ml-15">{{call:APPLY_STATE fn='flowApplyState'}}</span>
			                <span class="ml-10"><i class="fa fa-user-o"></i>  {{:APPLY_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:APPLY_TIME}}</span>
			                {{if APPLY_STATE!='0'}} <span class="ml-10"><i class="fa fa-hourglass-start"></i> {{:NODE_NAME}}/{{:CHECK_NAME}} </span>{{/if}}
			                <a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowApply')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
						  {{if data.length>=8}}<li style="text-align:center;"><a href="javascript:void(0);" onclick="flowMore('myFlowApply')">查看更多</a></li>{{/if}}
				 </script>
                  <div data-ref-flow="done"  class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="flow-done-template" data-mars="FlowDao.myFlowDone" style="width: 100%;"> </ul> 
                  </div>
                  <script id="flow-done-template" type="text/x-jsrender">
			             {{if data.length==0}}<li><p style="text-align:center;">暂无流程</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> &nbsp;<a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowDone')">{{:APPLY_NO}}_{{:FLOW_NAME}}_{{:APPLY_TITLE}}</a></p>
							<span class="ml-15">{{call:APPLY_STATE fn='flowApplyState'}}</span>			                
							<span class="ml-15"><i class="fa fa-user-o"></i> {{:APPLY_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:APPLY_TIME}}</span>
			                <span class="ml-10"><i class="fa fa-check-square-o"></i> {{:NODE_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-hourglass-3"></i> {{:CURRENT_NODE}}</span>
			                <a href="javascript:;"  onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowDone')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
 						{{if data.length>=8}}<li style="text-align:center;"><a href="javascript:void(0);" onclick="flowMore('myFlowDone')">查看更多</a></li>{{/if}}
				 </script>
                  <div data-ref-flow="cc"  class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="flow-cc-template" data-mars="FlowDao.myCCFlowList" style="width: 100%;"> </ul> 
                  </div>
                  <script id="flow-cc-template" type="text/x-jsrender">
			             {{if data.length==0}}<li><p style="text-align:center;">暂无流程</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> &nbsp;<a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowCC')">{{:APPLY_NO}}_{{:FLOW_NAME}}_{{:APPLY_TITLE}}</a></p>
							<span class="ml-15">{{call:APPLY_STATE fn='flowApplyState'}}</span>			                
							<span class="ml-15"><i class="fa fa-user-o"></i> {{:APPLY_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:APPLY_TIME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:CREATE_NAME}} 抄送于{{:CC_TIME}}</span>
			                <a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowCC')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
 						{{if data.length>=8}}<li style="text-align:center;"><a href="javascript:void(0);" onclick="flowMore('myFlowCC')">查看更多</a></li>{{/if}}
				 </script>
                </div>
              </div>
            </div>
             <div class="layui-card">
             	<div class="layui-tab layui-tab-brief layadmin-latestData">
	                <ul class="layui-tab-title">
	                  <li class="layui-this">DeepSeek</li>
	                  <li>超级智能体</li>
	                  <li>接入DeepSeek</li>
	                  <li>大模型资讯</li>
	                  <li>大模型研究报告</li>
	                  <li>大模型工具</li>
	                </ul>
	                <div class="layui-tab-content ">
	                  <div class="layui-tab-item layui-show">
	                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote" style="width: 100%;">
	                     	<li>
	                     		<a target="_blank" href="/yq-work/fileview/c79cb8a0907649248b65b90ba471d574?view=online">
						           <i class="layui-icon layui-icon-right"></i> 	清华大学第一弹：DeepSeek从入门到精通.pdf
						       </a>
	                     	</li>
	                     	<li>
	                     		<a target="_blank" href="/yq-work/fileview/06eff04ddc724ca2a1fefe36c3a5b753?view=online">
						           <i class="layui-icon layui-icon-right"></i> 	清华大学第二弹：DeepSeek赋能职场.pdf
						       </a>
	                     	</li>
	                     	<li>
	                     		<a target="_blank" href="/yq-work/fileview/9fc9bdb20ff14bc486a2f5158bd2a204?view=online">
						           <i class="layui-icon layui-icon-right"></i> 	清华大学第三弹：普通人如何抓住DeepSeek红利.pdf
						       </a>
	                     	</li>
	                     	<li>
	                     		<a target="_blank" href="https://metaso.cn/s/rsgATP2">
						           <i class="layui-icon layui-icon-right"></i> 	DeepSeek + metaso：新的AI搜索交互体验（search）
						       </a>
	                     	</li>
	                     	<li>
	                     		<a target="_blank" href="https://ima.qq.com/">
						           <i class="layui-icon layui-icon-right"></i> 	DeepSeek + ima：个人智能知识库（RAG）
						       </a>
	                     	</li>
	                     	<li>
	                     		<a target="_blank" href="https://tbox.alipay.com/pro/community">
						           <i class="layui-icon layui-icon-right"></i> 	DeepSeek + alipay百宝箱：企业快速构建专业级智能体（Agent）
						       </a>
	                     	</li>
	                     	<li>
	                     		<a target="_blank" href="https://mcp.so/zh">
						           <i class="layui-icon layui-icon-right"></i> 	DeepSeek + MCP：任务规划和实时决策（MCP）
						       </a>
	                     	</li>
	                     	<li>
	                     		<a target="_blank" href="https://www.trae.com.cn/">
						           <i class="layui-icon layui-icon-right"></i> 	DeepSeek + Trae：高效AI协作开发（Agent+RAG+MCP）
						       </a>
	                     	</li>
	                     	<li>
	                     		<a target="_blank" href="https://metaso.cn/s/nZrP4am">
						           <i class="layui-icon layui-icon-right"></i> 	AI技术前沿：MCP、RAG和Agent系统（<a href="https://metaso.cn/s/NT5EHyr" target="blank">metaso生成的互动页面</a>）
						       </a>
	                     	</li>
	                     </ul>
	         		  </div>
	                  <div class="layui-tab-item">
	                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote" style="width: 100%;">
	                     	<li>
	                     		<a href="https://www.tiangong.cn/login?invite_code=f7080c67d6a6868418230a6f75089f79" target="blank">天工超级智能体</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://manus.im/" target="blank">manus</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://flowith.io/" target="blank">flowith 超级智能体</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.genspark.ai/" target="blank">Genspark 超级智能体</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.n.cn/" target="blank">纳米超级智能体</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://space.coze.cn/" target="blank">扣子空间</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.napkin.ai/" target="blank">napkin</a>
	                     	</li>
                     	  </ul>
	                  </div>
	                  <div class="layui-tab-item">
	                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote" style="width: 100%;">
	                     	<li>
	                     		<a href="https://www.wenxiaobai.com/chat/200006" target="blank">问小白 - DeepSeek R1 推理图片生成独家首发</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://ai.dangbei.com/chat" target="blank">当贝AI官网_DeepSeek满血版_全网优质AI大模型</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://xiaoyi.huawei.com/chat/" target="blank">华为小艺：DeepSeek-R1-联网满血版</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://copilot.wps.cn/" target="blank">WPS灵犀：DeepSeek-R1 大模型</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://mp.weixin.qq.com/s/Rvj-Gmi-94WndQRv7cJ_VQ" target="blank">满血R1限免抛发！DeepSeek暗战升级：多平台已悄悄接入</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://yuanbao.tencent.com/chat" target="blank">腾讯元宝：DeepSeek-R1-联网满血版</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://docs.qq.com/ai?source=desktop&nlc=1" target="blank">腾讯文档：DeepSeek-R1-联网满血版</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://bot.n.cn/" target="blank">纳米AI：DeepSeek-R1-联网满血版</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://wechat-aitalk.cmcm.com/" target="blank">傅盛：DeepSeek-R1 大模型</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://zhida.zhihu.com/" target="blank">知乎直达</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://metaso.cn/" target="blank">秘塔搜索</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://chat.scnet.cn/" target="blank">国家超算互联网平台</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.perplexity.ai/" target="blank">perplexity</a>
	                     	</li>
	                     </ul>
	                  </div>
	                  <div class="layui-tab-item">
	                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote" style="width: 100%;">
	                     	<li>
	                     		<a href="http://www.damoai.com.cn/kuaixun" target="blank">大模型之家：快讯</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://mp.weixin.qq.com/s/6PYqhekOxfs09iN--2zdsw" target="blank">世界级AI科学家加入阿里，出任集团副总裁</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://mp.weixin.qq.com/s/s3tIlDjuXPUPnUzvDQH9vg" target="blank">最新：三大运营商全面接入DeepSeek</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://mp.weixin.qq.com/s/jzrNB_kht_RNg7e_uQFTCA" target="blank">国产 DeepSeek V3 被秒成"前浪"？谷歌开放最强 Gemini 2.0 全家桶：速度快60倍，上下文还长16倍！</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://mp.weixin.qq.com/s/PinjfUM3O-3KbnBo0eoo2Q" target="blank">DeepSeek施压，OpenAI放大招</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://mp.weixin.qq.com/s/oL0iazyv6deRpJ6pK3c3hw" target="blank">国务院重磅！国家平台，上线DeepSeek；暴涨10000%+；海通，告别A股</a>
	                     	</li>
	                     </ul>
	                  </div>
	                  <div class="layui-tab-item">
	                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote" style="width: 100%;">
	                     	<li>
	                     		<a href="https://www.yuque.com/rocky2171/ai/report" target="blank">人工智能研究报告</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.yuque.com/office/yuque/0/2025/pdf/360253/1735962502391-d8451dd6-b34e-45ef-b9a5-b29e3d1b6e18.pdf" target="blank">2024年AI代码平台及产品发展简报</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.yuque.com/office/yuque/0/2024/pdf/360253/1735541088571-b7164bb1-8092-4e6e-93ef-8c32469437fe.pdf" target="blank">2024年中国行业大模型市场报告</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://jkhbjkhb.feishu.cn/wiki/W5D7wuDcbiPXDLkaRLQcAJpOn8f" target="blank">量子位：最新研究报告</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://v11enp9ok1h.feishu.cn/wiki/SClXwLJlziC40vk2izmcTbF8n6f?table=tbld3YwV66PiXQ6f&view=vewboy8foI" target="blank">甲子光年：最新研究报告</a>
	                     	</li>
	                     </ul>
	                  </div>
	                  <div class="layui-tab-item">
	                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote" style="width: 100%;">
	                     	<li>
	                     		<a href="https://www.yuque.com/rocky2171/ai/ollama" target="blank"> Ollama 是一个基于 Go 语言开发的可以本地运行大模型的开源框架</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.yuque.com/rocky2171/ai/hcgm3leidswtxtx2" target="blank">硅基流动 国内做的最大最好的大模型公有云平台</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.yuque.com/rocky2171/ai/xzuo4lg6uwunnl1m" target="blank">大模型统一接入路由器OpenRouter</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.yuque.com/rocky2171/ai/yrea6u9lfhg1s7o3" target="blank">大模型桌面客户端 多元化 LLM 提供商支持</a>
	                     	</li>
	                     	<li>
	                     		<a href="https://www.yuque.com/rocky2171/ai/zgukg4k2c44zimn2" target="blank">AI搜索：搜索或成为今年AI应用一大突破</a>
	                     	</li>
	                     </ul>
	                  </div>
         		    </div>
         		  </div>
             </div>
            <div class="layui-card">
              <div class="layui-tab layui-tab-brief layadmin-latestData taskList">
                <a style="position: absolute;;right: 20px;margin-top: 10px;z-index: 9999;color: rgb(1, 170, 237);" href="javascript:void(0)" onclick="moreTask()" class="layui-a-tips">更多</a>
                <ul class="layui-tab-title">
                  <li class="layui-this">我负责的任务</li>
                  <li>我发起的任务</li>
                  <li class="" id="zendaoNum">我的禅道</li>
                </ul>
                <div class="layui-tab-content ">
                  <div class="layui-tab-item layui-show">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="task-template" data-mars="HomeDao.myTaskList" style="width: 100%;"></ul>
         		  </div>
                  <script id="task-template" type="text/x-jsrender">
						 {{if data.length==0}}<li><p style="text-align:center;">暂无任务</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> {{call:TASK_STATE fn='taskStateLabel'}}&nbsp;<a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')">{{:TASK_NAME}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{call:CREATOR fn='getUserName'}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{call:TASK_LEVEL fn='taskTextLevel'}}</span>
			                <span class="ml-10"><i class="fa fa-calendar-check-o"></i> {{:PLAN_STARTED_AT}} ~{{:DEADLINE_AT}}</span>
			                {{if TASK_STATE < 30}}<span class="ml-5">{{call:DEADLINE_AT fn='timeFn'}}</span></span>{{/if}}
							{{if FINISH_TIME}}<span title="完成时间" class="ml-10"><i class="fa fa-check-square-o"></i> {{:FINISH_TIME}}</span>{{/if}}
			                <a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">处理</a>
			              </li>
			              {{/for}}
				 </script>
                  <div class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="create-task-template" data-mars="HomeDao.myCreateTaskList" style="width: 100%;"></ul>
         		  </div>
                  <script id="create-task-template" type="text/x-jsrender">
						 {{if data.length==0}}<li><p style="text-align:center;">暂无任务</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> {{call:TASK_STATE fn='taskStateLabel'}}&nbsp;<a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')">{{:TASK_NAME}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{call:ASSIGN_USER_ID fn='getUserName'}}</span>
							<span class="ml-10"><i class="fa fa-clock-o"></i> {{call:TASK_LEVEL fn='taskTextLevel'}}</span>
			                <span title="发起时间" class="ml-10"><i class="fa fa-calendar-check-o"></i> {{:CREATE_TIME}}</span>
			                {{if TASK_STATE < 30 && TASK_STATE!=13 &&TASK_STATE!=13}}<span class="ml-5">{{call:DEADLINE_AT fn='timeFn'}}</span></span>{{/if}}
							{{if FINISH_TIME}}<span title="完成时间" class="ml-10"><i class="fa fa-check-square-o"></i> {{:FINISH_TIME}}</span>{{/if}}
			                <a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
				 </script>
                  <div class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="zendao-template" data-mars="HomeDao.getBugList" style="width: 100%;"> </ul> 
                  </div>
                  <script id="zendao-template" type="text/x-jsrender">
						{{call:data fn='zendaoNum'}}
						{{if data.length==0}}<li><p style="text-align:center;">暂无数据</p></li>{{/if}}
			            {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i>&nbsp;<a href="javascript:;" onclick="zentao('{{:ID}}')">{{:TITLE}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{:OPENEDBY}}</span>
			                <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:ASSIGNEDDATE}}</span>
			                <a href="javascript:;" onclick="zentao('{{:ID}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			            {{/for}}
				 </script>
                </div>
              </div>
            </div>
           <div class="layui-card">
	          <div class="layui-tab layui-tab-brief layadmin-latestData">
                <ul class="layui-tab-title">
                  <li class="layui-this"> 通知公告 </li>
                </ul>
                <div class="layui-tab-content">
                	 <div class="layui-tab-item layui-show">
	                	 <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="notice-template" data-mars="HomeDao.notices" style="width: 100%">
		            	
		            	 </ul>
                		<script id="notice-template" type="text/x-jsrender">
	            		 {{for data}}
	              		   <li>
	               			    <p><span class="layui-badge-rim" style="color: #b4a2a2;height: 20px;line-height: 20px;">{{call:REMIND_TYPE fn='noticeLabel'}}</span> <a href="javascript:void(0)"  onclick="remindDetail('{{:REMIND_ID}}','{{:REMIND_TYPE}}')">{{:TITLE}}</a></p>
	                			<span class="ml-5"><i class="fa fa-user-o"></i>  {{:PUBLISH_BY}}</span>
	               				 <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:PUBLISH_TIME}}</span>
	                			 <span class="ml-15 layui-hide"><i class="fa fa-eye"></i> {{:VIEW_COUNT}}</span>
	                			<a href="javascript:void(0)" onclick="remindDetail('{{:REMIND_ID}}','{{:REMIND_TYPE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
	              			</li>
	            		 {{/for}}
					   </script>
                   </div>
                </div>
              </div>
	        </div>
           <div class="layui-card">
	          <div class="layui-tab layui-tab-brief layadmin-latestData">
                <ul class="layui-tab-title">
                  <li class="layui-this"> 公文发布 </li>
                </ul>
                <div class="layui-tab-content">
                	 <div class="layui-tab-item layui-show">
	                	 <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="rules-template" data-mars="HomeDao.rules" style="width: 100%">
		            	
		            	 </ul>
                		<script id="rules-template" type="text/x-jsrender">
	            		 {{for data}}
	              		   <li>
	               			    <p><span class="layui-badge-rim" style="color: #b4a2a2;height: 20px;line-height: 20px;">{{:FOLDER_NAME}}</span> <a target="blank" href="/yq-work/fileview/{{:FILE_ID}}?view=online">{{:FILE_NAME}}</a></p>
	                			<span class="ml-5"><i class="fa fa-user-o"></i>  {{:CREATE_NAME}}</span>
	               				 <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:CREATE_TIME}}</span>
	                			 <span class="ml-15"><i class="fa fa-eye"></i> {{:DOWNLOAD_COUNT}}</span>
	                			 <a target="blank" href="/yq-work/fileview/{{:FILE_ID}}?view=online" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
	              			</li>
	            		 {{/for}}
					   </script>
                   </div>
                </div>
              </div>
	        </div>
           <div class="layui-card">
	          <div class="layui-tab layui-tab-brief layadmin-latestData">
	             <a style="position: absolute;;right: 20px;margin-top: 10px;z-index: 9999;color: rgb(1, 170, 237);" href="javascript:void(0)" onclick="popup.openTab({url:'${ctxPath}/pages/doc/doc-list.jsp',title:'知识库'})" class="layui-a-tips">更多</a>
                <ul class="layui-tab-title">
                  <li class="layui-this"> 知识库  </li>
                </ul>
                <div class="layui-tab-content">
                  <div class="layui-tab-item layui-show">
                      <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="km-template" data-mars="HomeDao.doc" style="width: 100%;">
		            </ul>
                  </div>
                   <script id="km-template" type="text/x-jsrender">
			            {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> <a href="javascript:;" onclick="docDetail('{{:DOC_ID}}','{{:FOLDER_ID}}')">{{:TITLE}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{:CREATE_NAME}}</span>
			                <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:UPDATE_TIME}}</span>
			                <span class="ml-15"><i class="fa fa-eye"></i> {{:VIEW_COUNT}}</span>
			                <a href="javascript:;" onclick="docDetail('{{:DOC_ID}}','{{:FOLDER_ID}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
					</script>
                </div>
              </div>
	        </div>
          </div>
        </div>
      </div>
      
      <div class="layui-col-md4 render-3">
      	 <div class="layui-card">
          <div class="layui-card-header">置顶</div>
          <div class="layui-card-body top-nav-card">
            <div class="layui-carousel  admin-carousel admin-news layuiadmin-card-link" id="carousel-nav">
            	<div data-template="topNav-template" data-mars="HomeDao.queryTopNav" carousel-item></div>
            </div>
             <script id="topNav-template" type="text/x-jsrender">  
              	  {{for data}}
					<div {{if #index%2==0}}style="color:#fff;background-color: #009688;background-image: linear-gradient(to right,#009688,#5fb878);"{{else}} {{/if}} style="color:#fff;background-color: #009fde;background-image: linear-gradient(to right,#009fde,#00beff);">
					 {{if REMARK}}
	             	 	<a {{:STYLE}} onclick="userNavDetail('{{:NAV_ID}}')" href="javascript:void(0);">{{cutText:TITLE 80}}</a>
						{{else}}
	             		 <a {{:STYLE}} target="_blank" href="{{:URL}}" rel="noreferrer">{{cutText:TITLE 80}}</a>
					 {{/if}}
					</div>
	              {{/for}}
			</script>      
          </div>
         </div>
      	 <div class="layui-card">
	          <div class="layui-card-header">快速发起流程
	          	<a style="position: absolute;;right: 20px;margin-top: 0px;z-index: 9999;color: rgb(1, 170, 237);" href="javascript:void(0)" onclick="applyFlow('');" class="layui-a-tips">更多</a>
	          </div>
	          <div class="layui-card-body" style="min-height: 60px;" data-mars="FlowConfigDao.myFlowFavorite" data-template="top-list">
           			
	          </div>
	          <script id="top-list" type="text/x-jsrender">
				<div class="layui-row">
				{{for data}}
					<div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;" data-code="{{:FLOW_CODE}}" data-name="{{:FLOW_NAME}}">
					<button class="btn btn-sm btn-default" onclick="applyFlow('{{:FLOW_CODE}}');" style="width: 85%;" type="button"> +{{:FLOW_NAME}}</button>	
				</div>
			   {{/for}}
				{{if data.length==0}} <button class="btn btn-sm btn-default" onclick="applyFlow('');" style="width: 120px;" type="button"> +发起流程 </button> {{/if}}
				</div>			
			 </script>
         </div>
         <div class="layui-card dc">
	          <div class="layui-card-header">一日三餐 <a href="javascript:void(0);" onclick="foodSetting();"  class="layui-btn layui-btn-primary layui-btn-xs mt-10 layui-a-tips">午餐预定</a></div>
	          <div class="layui-card-body layui-text">
           			<table class="layui-table" lay-skin="nob" style="padding: 10px 6px;width: 100%">
						<tbody id="booksInfo"></tbody>
					</table>
					<div class="text-c mt-10">
						<button type="button" id="doDc" class="btn btn-info btn-sm mr-10 text-r btn-outline" onclick="bookFood()"> <i class="glyphicon glyphicon-cutlery"></i> 点餐</button>
						<a href="tencent://message/?uin=1036305495&Menu=yes" id="addDoDc" class="btn btn-success btn-sm btn-outline mr-10 text-r" onclick="bookFood()">申请补点</a>
					</div>
	          </div>
         </div>
         <div class="layui-card">
         	  <div class="layui-card-header">今日日历
         	  	<a style="position: absolute;;right: 20px;margin-top: 0px;z-index: 9999;color: rgb(1, 170, 237);" href="javascript:void(0)" onclick="top.popup.layerShow({type:2,area:['80%','90%'],url:'https://www.rili.com.cn/',title:false})" class="layui-a-tips">更多</a>
         	  </div>
	          <div class="layui-card-body todayInfo" style="min-height: 170px;">
	          	<div class="layui-col-md6">
		          	 <p>日期：<span id="date"></span></p>
		          	 <p>周次：今年的第<span id="weekOfYear"></span>周</p>
		          	 <p>天次：今年的第<span id="dayOfYear"></span>天</p>
		          	 <p>农历：<span id="yearTips"></span><span id="lunarCalendar"></span></p>
	          	</div>
	          	<div class="layui-col-md6">
		          	 <p>类型：<span id="typeDes"></span></p>
		          	 <p>节气：<span id="solarTerms"></span></p>
		          	 <p>星座：<span id="constellation"></span></p>
		          	 <p>工作：本月第<span id="indexWorkDayOfMonth"></span>工作日</p>
	          	</div>
	          	<div class="layui-col-md12">
		          	 <p>宜项：<span id="avoid"></span></p>
		          	 <p class="hidden">禁忌：<span id="suit"></span></p>
	            </div>
             </div>
         </div>
         <div class="layui-card">
          <div class="layui-card-header">
          		 我的考勤          		 
          		 <a class="mr-5 ml-50" href="javascript:void(0);" onclick="kqMore(1)">微信打卡</a> | 
          		 <a class="mr-5" href="javascript:void(0);" onclick="kqMore(0)">考勤机</a> |
				 <a class="mr-5" href="javascript:void(0);" onclick="kqTop()">考勤数据榜</a>
          </div>
          <div class="layui-card-body">
            <div data-mars="HomeDao.queryKq" data-template="kq-template">
             <script id="kq-template" type="text/x-jsrender">
               {{if data.length==0}}<p>暂无数据</p>{{/if}}
               <table class="layui-table" lay-skin="line">
              	 {{if data.length>0}}
					<thead>
   					   <tr>
     				 	 <th>日期</th>
     				 	 <th>姓名</th>
     					 <th>上班时间</th>
      					 <th>下班时间</th>
   						</tr> 
  					</thead>
              	  {{for data}}
                    <tr>
                 	 <td>
                  		{{:DK_DATE}}
                  	</td>
                 	 <td>
                  		{{:USER_NAME}}
                  	</td>
                 	 <td>
                  		{{:SIGN_IN}}&nbsp;{{:REMARK}}
                  	</td>
                 	 <td>
                  		{{:SIGN_OUT}}&nbsp;{{:REMARK}}
                  	</td>
                	</tr>
                   {{/for}}
				 {{/if}}
			</table>
		   </script>
          </div>
          <p style="text-align: center;margin-top: 15px;margin-bottom: 15px;display: none;"><button class="btn btn-success btn-outline btn-sm" onclick="kqSign(0)" type="button"><i class="fa fa-sign-in" aria-hidden="true"></i> 上班打卡</button> <button class="btn btn-info btn-outline btn-sm ml-10" onclick="kqSign(1)" type="button"><i class="fa fa-sign-out" aria-hidden="true"></i> 下班打卡</button></p>
          </div>
        </div>
        <div class="layui-card layui-hide">
          <div class="layui-card-body">
            	尊敬的同事们：<br>
				请尽快加入公司企业微信，<a style="color: #4298fd;" href="https://work.weixin.qq.com/#indexDownload" target="blank">安装企业微信APP和PC端</a>，绑定企业邮箱，以接入AI+企微机器人，提升协同效率。如需创建新群(部门或项目群)，请提供机器人Key给管理员。微信关注“云趣信息科技”并扫描二维码加入，然后联系部门主管加入部门群。
				谢谢合作。<br><img src="${ctxPath}/static/images/qw.png" style="width: 60%;height: 60%">
          </div>
        </div>
      	<div class="layui-card">
          <div class="layui-card-body">
			因微信服务号最近大更新，为确保您能及时接收到重要信息，请对微信进行以下设置：<br>
			1. 打开“云趣工作台”公众号，选择“更多”>“置顶公众号”。<br>
			2. 在设置中关闭“消息免打扰”。<br>
			3. 将公众号添加到桌面，以便快速访问。<br>
			对于“腾讯企业邮箱”服务号，请执行相同的操作。同时，推荐下载“QQ邮箱”APP，一键绑定您的企业邮箱，APP内也自带发票助手功能。您也可以在企业微信中绑定企业邮箱。<br>
			感谢您的配合。
          </div>
        </div>
       <!--  <div class="layui-collapse mb-5">
		  <div class="layui-colla-item">
		    <div class="layui-colla-title">免费LLM镜像</div>
		    <div class="layui-colla-content">
		      
		    </div>
		  </div>
		  <div class="layui-colla-item">
		    <div class="layui-colla-title">全球大模型排名</div>
		    <div class="layui-colla-content">
		    		 <div class="layui-row">
		      		   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info" href="https://chatgpt.com/" target="_blank" style="width: 85%;" type="button">ChatGPT</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info" href="https://claude.ai/" target="_blank" style="width: 85%;" type="button">Claude</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://gemini.google.com/" target="_blank" style="width: 85%;" type="button">Gemini</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://llama.meta.com/" target="_blank" style="width: 85%;" type="button">Llama</a></div>
		     		</div>
		    </div>
		  </div>
		  <div class="layui-colla-item">
		    <div class="layui-colla-title">国内大模型</div>
		    <div class="layui-colla-content">
		      <ul>
		        <li>Content list</li>
		        <li>Content list</li>
		      </ul>
		    </div>
		  </div>
		  <div class="layui-colla-item">
		    <div class="layui-colla-title">开源</div>
		    <div class="layui-colla-content">
		      <p>折叠面板的内容</p>
		    </div>
		  </div>
		</div> -->

        <div class="layui-card">
	          <div class="layui-card-header">大模型排名 <small></small>
	          	<a style="position: absolute;;right: 20px;margin-top: 0px;z-index: 9999;color: rgb(1, 170, 237);" href="https://ai.seoml.com/tools/chat" target="_blank" class="layui-a-tips">更多</a>
	          </div>
	          <div class="layui-card-body" style="min-height: 60px;">
				  <div class="layui-row">
					   
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://xinghuo.xfyun.cn/" target="_blank" style="width: 85%;" type="button">讯飞星火</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://tongyi.aliyun.com/" target="_blank" style="width: 85%;" type="button">通义千问</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://yiyan.baidu.com/" target="_blank" style="width: 85%;" type="button">文心一言</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://neice.tiangong.cn/" target="_blank" style="width: 85%;" type="button">天工大模型</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href=https://copilot.wps.cn/ target="_blank" style="width: 85%;" type="button">WPS AI</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://www.baichuan-ai.com/home" target="_blank" style="width: 85%;" type="button">百川智能</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://chatglm.cn/detail" target="_blank" style="width: 85%;" type="button">ChatGLM2</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://chat.sensetime.com/" target="_blank" style="width: 85%;" type="button">商量SenseChat</a></div>
					   <div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;"><a class="btn btn-sm btn-info btn-outline" href="https://yuanbao.tencent.com/chat" target="_blank" style="width: 85%;" type="button">腾讯混元</a></div>
				</div>			
	          </div>
         </div>
        <div class="layui-card">
          <div class="layui-card-header">网址收藏
          	  <button class="layui-btn layui-btn-primary layui-btn-xs" style="position: absolute;right: 10px;top: 10px;" onclick="addUserNav()">
             	  + 添加
              </button>
          </div>
          <div class="layui-card-body nav-card">
            <div class="layuiadmin-card-link layui-text" data-template="userNav-template" data-mars="HomeDao.queryUserNav"></div>
             <script id="userNav-template" type="text/x-jsrender">  
              	  {{if data.length==0}}<a href="javascript:void(0)" onclick="addUserNav()">+立即添加</a>{{/if}}
              	  {{for data}}
					 {{if REMARK}}
	             	 	<a onclick="userNavDetail('{{:NAV_ID}}')" href="javascript:void(0);">{{cutText:TITLE 30}}</a>
						{{else}}
	             		 <a target="_blank" href="{{:URL}}" rel="noreferrer">{{cutText:TITLE 30}}</a>
					 {{/if}}
	              {{/for}}
	              <a target="_blank" rel="noreferrer" href="https://getnote.top/${loginAcct}" rel="noreferrer">共享本</a>

			</script>      
          </div>
        </div>
      </div>
    </div>
	<div style="display: none;" id="qcode">
		<div style="margin-left:40px;" id="codeContainer"></div><br><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;微信扫码关注,再次扫码绑定后关闭弹出窗<br><b class="ml-20">请尽快关注绑定，否则影响系统使用</b></p>
	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		var ctxPath='${ctxPath}';
		var pwd='${pwd}';
		var roles='${roles}';
		var deptId='${deptId}';
		var devLead='${devLead}';
		var openId='${openId}';
		var userId = '${userId}';
		var isBirthday='${isBirthday}';
		var loginAcct = ${staffInfo.loginAcct};
		if(isBirthday=='1'){
			var device = layui.device();
			var fullShow=device.mobile;
			top.layer.open({title:false,full:fullShow,type:1,offset:'60px',area:['500px','500px'],shade:0,content:'<p style="position:absolute;top:70px;left:28px;font-size:18px;color:#3fb03d;"><span style="color:#ef0909;">${staffInfo.userName }</span><br>感谢你对公司做出的贡献,在此祝你生日快乐!事事顺心!</p><img style="width:500px;height:470px;background-size: cover;" src="/yq-work/static/images/birthday.png">'});
		}
		
	</script>
   <script type="text/javascript" src="${ctxPath}/static/js/jquery.qrcode.min.js"></script>
   <script type="text/javascript" src="${ctxPath}/static/js/index.js?v=20250402"></script>
   <script type="text/javascript" src="${ctxPath}/static/js/dc.js?v=202106281"></script>
   <script type="text/javascript" src="${ctxPath}/static/js/notify.min.js"></script>
   <script src="${ctxPath}/static/js/ws.js"></script>

<script type="text/javascript">
	layui.config({
		  base: '${ctxPath}/static/module/'
	}).use('notice'); //加载自定义模块
	
	var protocol = location.protocol;
    var urlPrefix = 'ws://';
    if(protocol=='https:'){
    	urlPrefix = 'wss://';
    }
	ws.init(urlPrefix+location.host+"${ctxPath}/websocket/${staffInfo.userId}/${staffInfo.loginAcct}",{
		autoReconnect:true,
		onmessage:function(data){
			console.log(new Date()+">>>"+JSON.stringify(data));
		    layui.use(['notice'], function(){
				var notice = layui.notice;
				notice.info({
					theme:'dark',
					timeout:1000*60,
			        title: data.title||'消息通知',
			       // maxWidth:'650px',
			        message: data.msg||'你有新的消息，请注意查收!',
			        audio: (data['senderType']||"3")+""
			    });
				top.layer.alert(data.msg,{title:data.title,time:20000,icon:1,offset:'rt',shade:0});
			});
		    if(data.token){
			    try{
					var audio4= new Audio("http://tsn.baidu.com/text2audio?lan=zh&ctp=1&cuid=yunqu&tok="+data.token+"&vol=9&per=0&spd=5&pit=5&aue=3&tex="+data.msg);
					audio4.play();
				}catch(e){
					console.log(e)						
				}
		    }
		},//收到消息
		onopen:function(event){
			console.log(new Date()+' connection success');
		},//ws链接成功
		onclose:function(event){
			console.log(new Date()+' connection close');
		},//ws关闭
		onerror:function(event){
			console.log(new Date()+' connection error');
		},//ws错误
	})

	
	function wxds(){
		layer.photos({photos: {"title": "打赏开发者","start": 0,shade:0.1,"data": [{"alt": "打赏开发者","pid": 5,"src": "/yq-work/static/images/wx.jpg"}]},footer: true});
	}
</script>

</EasyTag:override>

<%@ include file="/pages/common/layout_list.jsp" %>
