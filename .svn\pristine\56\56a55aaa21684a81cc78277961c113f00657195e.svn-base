<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>项目成员</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="projectDetail" class="form-inline">
		<input name="projectId" type="hidden" value="${param.projectId}">
		<div class="ibox">
			<div class="ibox-title clearfix">
          		 <div class="form-group">
         		   <h5> 项目成员</h5>
			       <div class="input-group input-group-sm"  style="width: 150px;">
						<span class="input-group-addon">姓名</span> 
						<input name="userName" autocomplete="off" id="startDate" class="form-control input-sm">
					</div>
					<div class="input-group input-group-sm"  style="width: 120px;">
						<span class="input-group-addon">角色</span> 
						<input name="roleName" autocomplete="off" id="endDate" class="form-control">
					</div>
					<div class="input-group input-group-sm">
					 	<button type="button" class="btn btn-sm btn-default" onclick="loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
				   </div>
				 	<button type="button" class="btn btn-sm btn-info pull-right" onclick="addUserData()"><span class="fa fa-plus"></span> 新增成员</button>
          	    	<a onclick="synProjectTeam()" href="javascript:;" class="pull-right btn btn-xs btn-link mt-5">同步</a>
          	     </div>
              </div> 
			  <div class="ibox-content">
				<table class="layui-hide" id="teams"></table>
			 </div>
		</div>
	</form>
	
	<script id="addTeams" type="text/html">
		<table class="layui-table" style="margin: 15px;width: 370px">
			<tr>
				<td>用户</td>
				<td>
					<input type="hidden" name="teamIds" id="teamIds">
					<input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="teamNames"/>
				</td>
			</tr>
		</table>
	</script>
	<script type="text/html" id="adminFlagTpl">
 		<input type="checkbox" value="{{d.USER_ID}}" lay-skin="switch" lay-text="管理员|参与人" lay-filter="adminFlagStateFn" {{ d.ADMIN_FLAG == 0 ? '' : 'checked' }}>
    </script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
	
  var projectId =  '${param.projectId}';

  function projectTeams(){
	$("#projectDetail").initTable({
		mars:'ProjectDao.projectTeams',
		id:"teams",
		data:{},
		edit:'editTeam',
		limit:30,
		height:'full-130',
		cols: [[
		 {type:'numbers'},
         {
    	 	field: 'USERNAME',
			title: '姓名',
			align:'left',
			event:'teamUserInfo',
			templet:'<div>{{d.DEPTS}}.{{d.USERNAME}}</div>'
		 },{
		    field: 'MOBILE',
			title: '手机号码',
			align:'left'
		},{
		    field: 'EMAIL',
			title: '邮箱',
			align:'left'
		},{
		    field: 'LAST_LOGIN_TIME',
			title: '最近登录时间',
			align:'left'
		},
		{
			title: '管理员', width:90, 
			templet: '#adminFlagTpl'
		},{
		    field: 'ROLE_NAME',
			title: '角色',
			edit:'text',
			hide:true,
			align:'left'
		},{
		    field: 'JOIN_TIME',
			title: '加入时间',
			align:'left'
		},{
		    field: 'ACCESS_TIME',
			title: '访问时间',
			align:'left'
		},{
		    field: 'REMARK',
			title: '备注',
			edit:'text',
			align:'left'
		},{
			field:'',
			title:'操作',
			width:90,
			event:'removeUser',
			style:'color:#1fb8dd;cursor:pointer;',
			templet:function(){
				return '移除';
			}
		}
		]],done:function(result){
			
		}});
   }
	
 	function loadData(){
 		$("#projectDetail").queryData({id:'teams'});
 	}
 	
 	function addUserData(){
 		if(top.isAdmin=='1'||isSuperUser){
 			var tpl = $.templates("#addTeams");
	 		var html = tpl.render({});
	 		popup.layerShow({title:'新增成员',content:html,area:['400px','300px'],btn:['确认'],yes:function(index){
	 			var teamIds = $("#teamIds").val();
	 			var data = {teamIds:teamIds,projectId:projectId};
	 			ajax.remoteCall("${ctxPath}/servlet/project/conf?action=addTeamUser",data,function(rs) { 
	 				if(rs.state==0){
	 					layer.msg(rs.msg,{icon:7,offset:'20px',time:1200});	 					
	 				}else{
						layer.msg(rs.msg,{time:800,icon:1},function(){
							location.reload();
						});
			 			layer.close(index);
	 				}
				});
	 		}});
 		}else{
 			layer.msg("您无权操作!");
 		}
 	}
 	
	function teamUserInfo(data){
		userInfoLayer(data['USER_ID']);
	}
	
	function editTeam(obj){
		var data = obj.data;
		getProjectInfo(projectId,function(row){
			if(top.isAdmin=='1'||isSuperUser){
				var params={PROJECT_ID:data.PROJECT_ID,USER_ID:data.USER_ID,ROLE_NAME:data.ROLE_NAME,REMARK:data.REMARK};
				ajax.remoteCall("${ctxPath}/servlet/project?action=updateTeam",params,function(result) { 
					if(result.state != 1){
						layer.alert(result.msg,{icon: 7});
					}
				},{loading:false});
		    }else{
		    	layer.msg("修改无效,您无权修改!");
		    }
		});
	}
	
	function synProjectTeam(){
		if(top.isAdmin=='1'||isSuperUser){
			layer.confirm('是否确认从关联任务或周报同步参与人',function(){
				ajax.remoteCall("${ctxPath}/servlet/project?action=updateProjectTeam",{projectId:projectId},function(result) { 
					if(result.state != 1){
						layer.alert(result.msg,{icon: 7});
					}else{
						$("#projectDetail").queryData({id:'teams',jumpOne:true});
						layer.msg(result.msg);
					}
				});
			});
		}
	}
	
	function removeUser(data){
		if(top.isAdmin=='1'||isSuperUser){
			var userId = data['USER_ID'];
			ajax.remoteCall("${ctxPath}/servlet/project/conf?action=removeProjectTeamUser",{projectId:projectId,userId:userId},function(result) { 
				if(result.state != 1){
					layer.alert(result.msg,{icon: 7});
				}else{
					$("#projectDetail").queryData({id:'teams',jumpOne:true});
					layer.msg(result.msg);
				}
			});
		}
	}
	
	 function adminFlagStateFn(obj){
		 	if(top.isAdmin=='1'||isSuperUser){
			    var data = {adminFlag:obj.elem.checked?1:0,userId:obj.value,projectId:projectId};
				ajax.remoteCall("${ctxPath}/servlet/project/conf?action=updateTeamAdminFlag",data,function(rs) { 
					layer.msg('修改成功',{time:800,icon:1});
				});
		 	}else{
		 		layer.msg("修改无效,您无权修改!");
		 	}
	   }
 	
 	$(function(){
 		$("#projectDetail").render({success:function(){
 			projectTeams();
		}});
 	});
 	
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>