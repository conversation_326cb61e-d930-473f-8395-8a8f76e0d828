package com.yunqu.work.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="CustDao")
public class CustDao extends AppDaoContext{

	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String custId = param.getString("custId");
		return queryForRecord("select * from YQ_CRM_CUSTOMER where cust_id = ?",custId);
	}
	
	@WebControl(name="custList",type=Types.LIST)
	public JSONObject custList(){
		EasySQL sql=getEasySQL("select * from YQ_CRM_CUSTOMER where 1=1");
		
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="custContactsList",type=Types.LIST)
	public JSONObject custContactsList(){
		EasySQL sql=getEasySQL("select * from YQ_CRM_CONTACTS where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
}
