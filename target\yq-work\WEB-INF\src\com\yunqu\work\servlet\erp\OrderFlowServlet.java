package com.yunqu.work.servlet.erp;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.WxMsgService;

@WebServlet("/servlet/order/flow")
public class OrderFlowServlet extends BaseFlowServlet {

	private static final long serialVersionUID = 1L;

	/**
	 * 发起评审
	 * @return
	 */
	public EasyResult actionForReviewApply() {
		EasyQuery query = this.getQuery();
		JSONObject object = getJSONObject();
		
		FlowModel flow = ApproveService.getService().getFlow("order_review");
		
		String[] nodeUser = ApproveService.getService().getNodeUser(flow.getFlowCode());
		String id = RandomKit.uniqueStr();
//		String userIds = nodeUser[0];
//		String userNames = nodeUser[1];
		String orderId = object.getString("orderId");
		if(StringUtils.notBlank(orderId)) {
			id = orderId;
		}
		try {
			EasyRecord record = new EasyRecord("yq_flow_apply_ext","business_id");
			record.setPrimaryValues(id);
			record.set("data1", orderId);
			query.save(record);
			
			EasyRecord applyRecord = getApplyRecord("order_review");
			applyRecord.set("apply_state",object.getIntValue("reviewState"));
			applyRecord.set("apply_no",object.getString("reviewNo"));
			applyRecord.set("apply_title",object.getString("applyName"));
			applyRecord.set("apply_id",id);
//			applyRecord.set("check_ids", userIds);
//			applyRecord.set("check_names", userNames);
			applyRecord.set("apply_level",object.getString("reviewLevel"));
			applyRecord.set("apply_remark",object.getString("remark"));
			
			query.save(applyRecord);
			
			this.startReviewFlow(flow,id);
			
			query.executeUpdate("update yq_erp_order set review_id = ?,last_follow_label = '采购评审',last_follow_time = ?  where order_id = ?",id,EasyDate.getCurrentDateString(),orderId);
			
			this.addOrderLog(orderId,object.getString("remark"), "20","发起评审");

		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void startReviewFlow(FlowModel flow,String reviewId) throws SQLException {
		ApproveNodeModel obj = ApproveService.getService().getStartNode(new FlowApplyModel("order_review",getUserId()));
		String nextNodeId =  obj.getNodeId();
		String userId = obj.getCheckBy();
		String userName = obj.getCheckName();
		String resultId = RandomKit.uniqueStr();
		
		EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
		resultRecord.set("result_id", resultId);
		resultRecord.set("business_id", reviewId);
		resultRecord.set("check_name", userName);
		resultRecord.set("check_user_id", userId);
		resultRecord.set("check_result", 0);
		resultRecord.set("node_id",nextNodeId);
		resultRecord.set("node_name", obj.getNodeName());
		resultRecord.set("check_desc","");
		resultRecord.set("get_time",EasyDate.getCurrentDateString());
		this.getQuery().save(resultRecord);
		
		FlowService.getService().updateApplyBegin(reviewId, nextNodeId,"0",resultId);
		
		FlowApplyModel apply = ApproveService.getService().getApply(reviewId);
		
		//微信推送给审批人
		MessageModel msgModel  = new MessageModel();
		msgModel.setSender(getUserId());
		msgModel.setSendName(getUserName());
		msgModel.setFkId(reviewId);
		msgModel.setTitle(apply.getApplyTitle());
		msgModel.setTypeName(apply.getFlowCode());
		msgModel.setMsgLabel(obj.getNodeName());
		msgModel.setReceiver(userId);
		msgModel.setData1(flow.getFlowName());
		msgModel.setData2(apply.getApplyName());
		msgModel.setData3(apply.getApplyTime());
		msgModel.setDesc(apply.getApplyRemark());
		WxMsgService.getService().sendFlowTodoCheck(msgModel);
		MessageService.getService().sendMsg(msgModel);
	}
	/**
	 * 执行审批
	 * @return
	 */
	public EasyResult actionForAddCheck() {
		try {
			JSONObject params = getJSONObject();
			String businessId = params.getString("businessId");
			String orderId = params.getString("orderId");
			String resultId = params.getString("resultId");
			String checkDesc = params.getString("checkDesc");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("评审人不存在");
			}
			int checkResult = params.getIntValue("checkResult");//1 通过 2拒绝
			
			FlowModel flow = ApproveService.getService().getFlow("order_review");
			EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("business_id", businessId);
			record.set("check_user_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc",checkDesc);
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);
			
			MessageModel msgModel = new MessageModel();
			msgModel.setTypeName(flow.getFlowCode());
			msgModel.setSender(getUserId());
			msgModel.setSendName(getUserName());
			msgModel.setFkId(businessId);
			msgModel.setTitle(apply.getApplyTitle());
			msgModel.setData1(flow.getFlowName());
			msgModel.setData2(apply.getApplyName());
			msgModel.setData3(apply.getApplyTime());
			msgModel.setData4("待评审");
			msgModel.setDesc(checkDesc);
			
			if(checkResult==2) {
				this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ?,last_result_time = ? where business_id = ?",21,EasyDate.getCurrentDateString(),businessId);
				msgModel.setData4("评审退回");
				msgModel.setReceiver(apply.getApplyBy());
				WxMsgService.getService().sendFlowTodoCheck(msgModel);
				MessageService.getService().sendMsg(msgModel);
				return EasyResult.ok();
			}
			
			//获取下一级节点
			ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
			String nodeId = approveNode.getNodeId();
			String nextNodeId = approveNode.getNextNodeId();
			String nextResultId  = "0";
			int reviewState = 10; // 10 待审批 20审批中 21 拒绝 30审批完成
			if("0".equals(nextNodeId)) {//完成了
				reviewState = 30;
				msgModel.setReceiver(apply.getApplyBy());
				FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
				this.addOrderLog(orderId, msgModel.getDesc(), "20","审批完成");
			}else {
				reviewState = 20;
				ApproveNodeModel nodeInfo = ApproveService.getService().getNodeInfoByNodeId(nextNodeId,apply,params);
				msgModel.setReceiver(nodeInfo.getCheckBy());
				
				EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
				nextResultId = RandomKit.uniqueStr();
				resultRecord.set("result_id", nextResultId);
				resultRecord.set("business_id", businessId);
				resultRecord.set("check_name", nodeInfo.getCheckName());
				resultRecord.set("check_user_id", nodeInfo.getCheckBy());
				resultRecord.set("check_result", 0);
				resultRecord.set("node_id",nextNodeId);
				resultRecord.set("node_name",nodeInfo.getNodeName());
				resultRecord.set("get_time",EasyDate.getCurrentDateString());
				this.getQuery().save(resultRecord);
				
				FlowService.getService().updateApplyInfo(businessId, resultId,nextResultId,nodeId,nextNodeId, reviewState);
			}
			
			WxMsgService.getService().sendFlowTodoCheck(msgModel);
			MessageService.getService().sendMsg(msgModel);

		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForRestartReview() {
		JSONObject params = getJSONObject();
		try {
			FlowModel flow = ApproveService.getService().getFlow("order_review");
			String businessId = params.getString("businessId");
			this.startReviewFlow(flow,businessId);
			this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ? where business_id = ?",10,businessId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForPaymentApply() {
		JSONObject object = getJSONObject();
		String id = RandomKit.uniqueStr();
		
		FlowModel flow = ApproveService.getService().getFlow("order_payment");
		String[] nodeUser = ApproveService.getService().getNodeUser("order_payment");
		String userIds = nodeUser[0];
		String userNames = nodeUser[1];
		
		String orderIds = object.getString("orderIds");
		String orderNos = object.getString("orderNos");
		String planIds = object.getString("planIds");
		try {
			
			EasyRecord record = new EasyRecord("yq_erp_order_payment","business_id");
			record.set("supplier_id", object.getString("supplierId"));
			record.set("amount", object.getString("amount"));
			record.set("pay_plan_date", object.getString("planPayDate"));
			record.set("order_ids", orderIds);
			record.set("order_nos", orderNos);
			record.set("order_json", object.getString("orderJson"));
			record.setPrimaryValues(id);
			this.getQuery().save(record);
			
			EasyRecord applyRecord = getApplyRecord("order_payment");
			applyRecord.set("apply_state",object.getIntValue("paymentState"));
			applyRecord.set("apply_no",object.getString("paymentNo"));
			applyRecord.set("apply_title",object.getString("applyName"));
			applyRecord.set("apply_id",id);
			applyRecord.set("check_ids", userIds);
			applyRecord.set("check_names", userNames);
			applyRecord.set("apply_level",object.getString("paymentLevel"));
			applyRecord.set("apply_remark",object.getString("remark"));
			this.getQuery().save(applyRecord);
			
			EasySQL updateSql = new EasySQL();
			updateSql.append(id,"update yq_erp_order_pay_plan set payment_id = ? where 1=1");
			updateSql.appendIn(planIds.split(","), "and plan_id");
			this.getQuery().executeUpdate(updateSql.getSQL(),updateSql.getParams());
			
			JSONArray orderJsonArray = object.getJSONArray("orderJsonArray");
			for(int i=0;i<orderJsonArray.size();i++) {
				JSONObject orderJson  = orderJsonArray.getJSONObject(i);
				EasyRecord orderPayment = new EasyRecord("yq_erp_order_payment_detail");
				orderPayment.setColumns(orderJson);
				orderPayment.set("payment_id", id);
				orderPayment.set("apply_time", EasyDate.getCurrentDateString());
				this.getQuery().save(orderPayment);
			}
			
			if(StringUtils.notBlank(orderIds)) {
				//更新订单已申请金额
				updateSql = new EasySQL("update yq_erp_order t1 set t1.has_apply_amount = (select sum(apply_amount) from yq_erp_order_payment_detail t2 where t2.order_id =  t1.order_id) where 1=1");
				updateSql.appendIn(orderIds.split(","), "and t1.order_id");
				this.getQuery().executeUpdate(updateSql.getSQL(),updateSql.getParams());
			}
			
			this.firstPayment(flow,id);
				
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	private void firstPayment(FlowModel flow,String businessId) throws SQLException {
		ApproveNodeModel obj = ApproveService.getService().getStartNode(new FlowApplyModel("order_payment",getUserId()));
		String nextNodeId =  obj.getNodeId();
		String userId = obj.getCheckBy();
		String userName = obj.getCheckName();
		String resultId = RandomKit.uniqueStr();
		
		EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
		resultRecord.set("result_id", resultId);
		resultRecord.set("business_id", businessId);
		resultRecord.set("check_name", userName);
		resultRecord.set("check_user_id", userId);
		resultRecord.set("check_result", 0);
		resultRecord.set("node_id",nextNodeId);
		resultRecord.set("node_name",obj.getNodeName());
		resultRecord.set("check_desc","");
		resultRecord.set("get_time",EasyDate.getCurrentDateString());
		this.getQuery().save(resultRecord);
		
		FlowService.getService().updateApplyBegin(businessId, nextNodeId,"0",resultId);
		
		FlowApplyModel applyInfo = ApproveService.getService().getApply(businessId);
		//微信推送给审批人
		MessageModel msgModel  = new MessageModel();
		msgModel.setSender(getUserId());
		msgModel.setSendName(getUserName());
		msgModel.setFkId(businessId);
		msgModel.setMsgLabel("待审批");
		msgModel.setTitle(applyInfo.getApplyTitle());
		msgModel.setData1(flow.getFlowName());
		msgModel.setData2(applyInfo.getApplyName());
		msgModel.setData3(applyInfo.getApplyTime());
		msgModel.setDesc(applyInfo.getApplyRemark());
		msgModel.setReceiver(userId);
		WxMsgService.getService().sendFlowTodoCheck(msgModel);
		MessageService.getService().sendMsg(msgModel);
	}
	
	
	public EasyResult actionForRestartPayApply() {
		JSONObject params = getJSONObject();
		try {
			FlowModel flow = ApproveService.getService().getFlow("order_payment");
			String businessId = params.getString("businessId");
			this.firstPayment(flow,businessId);
			this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ? where business_id = ?",10,businessId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForAddPaymentCheck() {
		EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
		try {
			JSONObject object = getJSONObject();
			String businessId = object.getString("businessId");
			String resultId = object.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("评审人不存在");
			}
			int checkResult = object.getIntValue("checkResult");//1 通过 2拒绝
			String checkDesc = object.getString("checkDesc");
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("business_id", businessId);
			record.set("check_user_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc",checkDesc );
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			FlowApplyModel applyInfo = ApproveService.getService().getApply(businessId);
			
			MessageModel msgModel = new MessageModel();
			msgModel.setTypeName("FLOW_ORDER_PAY_CHECK");
			msgModel.setSender(getUserId());
			msgModel.setSendName(getUserName());
			msgModel.setFkId(businessId);
			msgModel.setTitle(applyInfo.getApplyTitle());
			msgModel.setData1(applyInfo.getFlowName());
			msgModel.setData2(applyInfo.getApplyName());
			msgModel.setData3(applyInfo.getApplyTime());
			msgModel.setData4("待审批");
			msgModel.setDesc(applyInfo.getApplyRemark());
			
			if(checkResult==2) {//退回
				this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ?,last_result_time = ? where business_id = ?",21,EasyDate.getCurrentDateString(),businessId);
				msgModel.setReceiver(applyInfo.getApplyBy());
				msgModel.setData4("审批退回");
				msgModel.setDesc(checkDesc);
				WxMsgService.getService().sendFlowTodoCheck(msgModel);
				MessageService.getService().sendMsg(msgModel);
				return EasyResult.ok();
			}
			
			//获取下一级节点
			ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
			String nodeId = approveNode.getNodeId();
			String nextNodeId = approveNode.getNextNodeId();
			String nextResultId = "0";
			
			int reviewState = 0; // 10 待审批 20审批中 21 退回 30审批完成
			if("0".equals(nextNodeId)) {//完成了
				reviewState = 30;
				msgModel.setReceiver(applyInfo.getApplyBy());
				msgModel.setData4("审批完成");
				
				FlowService.getService().updateApplyEnd(businessId, nodeId, resultId);
			}else {
				reviewState = 20;
				ApproveNodeModel nodeInfo = ApproveService.getService().getNodeInfoByNodeId(nextNodeId,applyInfo,object);
				msgModel.setReceiver(nodeInfo.getCheckBy());
					
				//生成下一个节点审批记录
				EasyRecord paymentResult = new EasyRecord("yq_flow_approve_result","result_id");
				nextResultId = RandomKit.uniqueStr();
				paymentResult.set("result_id", nextResultId);
				paymentResult.set("business_id", businessId);
				paymentResult.set("check_name", nodeInfo.getCheckName());
				paymentResult.set("check_user_id", nodeInfo.getCheckBy());
				paymentResult.set("check_result", 0);
				paymentResult.set("node_id",nextNodeId);
				paymentResult.set("node_name",nodeInfo.getNodeName());
				paymentResult.set("check_desc","");
				paymentResult.set("get_time",EasyDate.getCurrentDateString());
				this.getQuery().save(paymentResult);

				FlowService.getService().updateApplyInfo(businessId, resultId,nextResultId,nodeId,nextNodeId, reviewState);
			}
			WxMsgService.getService().sendFlowTodoCheck(msgModel);
			MessageService.getService().sendMsg(msgModel);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void addOrderLog(String orderId,String followContent,String followState,String followStateLabel) {
		EasyRecord model = new EasyRecord("YQ_ERP_ORDER_FOLLOW","FOLLOW_ID");
		model.set("FOLLOW_DATE", EasyCalendar.newInstance().getDateInt());
		model.set("ADD_TIME", EasyDate.getCurrentDateString());
		model.set("ADD_BY", getUserId());
		model.set("USER_NAME", getUserName());
		model.set("FOLLOW_STATE", followState);
		model.set("FOLLOW_STATE_LABEL", followStateLabel);
		model.set("FOLLOW_CONTENT", followContent);
		try {
			this.getQuery().executeUpdate("update yq_erp_order set last_follow_content = ?,last_follow_time = ?,last_follow_label = ? where order_id = ?",followContent,EasyDate.getCurrentDateString(),followStateLabel,orderId);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}

	@Override
	public EasyResult actionForSubmitApply() {
		return null;
	}

	@Override
	public EasyResult actionForUpdateApply() {
		return null;
	}

}
