<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>${checkTitle}</title>
	<style>
		.write-item li{height: 46px;}
		.layui-input-inline{width: 70%;}
		.write-item input{display: inline-block;min-width: 120px;}
		.layui-layer-content img{max-width: 100%;max-height: 100%;}
		.fa-url{display: inline-block;width: 160px;}
		header{height: 48px;line-height: 48px;background-color: #fff;border-bottom: 1px solid #ccc;padding: 0px 15px;margin-bottom: 10px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="layui-form form-inline layui-form-pane" id="dataMgrForm">
			<input value="${periodNum}" type="hidden" name="periodNum">
			<header>
				${checkTitle} <button type="button" id="submitBtn" lay-submit lay-filter="submitData" class="layui-btn layui-btn-sm layui-btn-normal ml-30">保存提交</button>
			</header>
		    <div class="layui-row layui-col-space15" data-mars="FaDao.checkData" data-template="cardTmpl"></div>
		</form>
		<script id="cardTmpl" type="text/x-jsrender">
			{{for data}}
			   <div class="layui-col-md4" id="card_{{:CHECK_ID}}">
					<div class="layui-card">
					        <div class="layui-card-header"><span class="layui-badge">{{:#getIndex()+1}}</span> {{:FA_NO}}__{{:FA_NAME}}</div>
					        <div class="layui-card-body">
								<ul>
       							 <li>卡片编号/数量：{{:CARD_NO}} / {{:CARD_NUM}}</li>
       							 <li>开始使用日期：{{:USE_BEGIN_DATE}}</li>
       							 <li>资产原值/位置：￥{{:FA_MONEY}} / {{:FA_ADDRESS}}</li>
     						    </ul>
								<hr class="layui-border-red">
								<ul class="write-item">
       							   <li>
										<label class="layui-form-label">资产状态</label>
										<div class="layui-input-inline">
											<select name="state_{{:CHECK_ID}}" lay-verify="required">
												<option value="">请选择</option>
												<option value="在用">在用</option>
												<option value="闲置">闲置</option>
												<option value="维修中">维修中</option>
												<option value="已报废">已报废</option>
											</select>
										</div>
								   </li>
       							   <li>		
										<label class="layui-form-label">使用人</label>
										<div class="layui-input-inline">
											<select name="usePeople_{{:CHECK_ID}}" lay-verify="required">
												<option value="">请选择</option>
												<option value="本人使用">本人使用</option>
												<option value="项目借用">项目借用</option>
												<option value="部门实习生使用">部门实习生使用</option>
												<option value="其他">其他</option>
											</select>
										</div>
									</li>
       							   <li>		
										<label class="layui-form-label">资产拍照</label>
										<div class="layui-input-inline layui-text">
											<input type="hidden" name="checkIds" value="{{:CHECK_ID}}">
											<input type="hidden" name="file_{{:CHECK_ID}}">
											<button type="button" data-id="{{:CHECK_ID}}" data-pid="{{:FA_ID}}" id="uploadBtn_{{:CHECK_ID}}" class="layui-btn layui-btn-sm layui-btn-primary layui-border-blue ml-10 upload-btn">选择图片</button>
											<input type="text" name="url_{{:CHECK_ID}}" class="fa-url layui-input" lay-verify="required">
											<a class="ml-10 look_{{:CHECK_ID}}" href="javascript:;" data-look-url="{{:FA_URL}}" onclick="lookImg(this)">{{if FILE_ID}}查看{{/if}}</a>
										</div>
									</li>
       							   <li>		
										<label class="layui-form-label">备注</label>
										<div class="layui-input-inline">
											<input type="text" name="remark_{{:CHECK_ID}}" placeholder="可不填"  class="layui-input">
										</div>
									</li>
     						    </ul>
					        </div>
					  </div>
			   </div>
			{{/for}}
		</script>
		<script type="text/html" id="btnBar">
			
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">

		$(function(){
			$("#dataMgrForm").render({success:function(rs){
				
				var list = rs['FaDao.checkData'].data;
				for(var index in list){
					var row = list[index];
					var id = row['CHECK_ID'];
					var newRow = {};
					newRow['usePeople_'+id] = row['FA_USE_PEOPLE'];
					newRow['state_'+id] = row['FA_USE_STATE'];
					newRow['remark_'+id] = row['FA_REMARK'];
					newRow['url_'+id] = row['FA_URL'];
					newRow['file_'+id] = row['FILE_ID'];
					fillRecord(newRow,'',',','#card_'+id);
				}
				
				 var _form = layui.form;
				 _form.render();
				 
				 _form.on('submit(submitData)', function(data){
					    var _data = form.getJSONObject("#dataMgrForm");
					    ajax.remoteCall("${ctxPath}/servlet/fa?action=addCheck",_data,function(result) {
							if(result.state == 1){
								layer.msg('保存成功');
							}else{
								layer.alert(result.msg);
							}
						});
					    return false;
				});
				 
				layui.use('upload', function(){
				 	var upload = layui.upload;
					$('.upload-btn').each(function(){
						  var t = $(this);
						  var uploadId = t.attr('data-id');
						  var pFkId = t.attr('data-pid');
						  var uploadInst = upload.render({
						    elem: '#uploadBtn_'+uploadId
						    ,url: '/yq-work/servlet/upload?action=index'
						    ,accept: 'images'
						    ,size:1024*20
						    ,field: 'file'
						    ,data: {fkId:uploadId,pFkId:pFkId,source:'faCheck'}
						    ,done: function(res, index, upload){
								var item = this.item;
							    layer.closeAll('loading');
							    if(res&&res.state==1){
							    	layer.msg(res.msg,{icon: 1,time:800},function(){
							    		faCheckCallback(uploadId,res.data);
									}); 
								}else{
									layer.alert(res.msg,{icon: 5});
								}
						    },before:function(){
								layer.load();
						    },allDone: function(obj){ 
							
							}
						    ,error: function(res, index, upload){
								layer.closeAll('loading');
						    	layer.alert("上传文件请求异常！",{icon: 5});
						    }
				       });
			  	   });
				});
				
				
			}});
		});
		
		function faCheckCallback(uploadId,data){
			$("[name='file_"+uploadId+"']").val(data.id);
			$("[name='url_"+uploadId+"']").val(data.url);
			$('.look_'+uploadId).attr('data-look-url',data.url);
		}
		
		function lookImg(el){
			var url = $(el).attr('data-look-url');
			layer.alert('<img src="'+url+'">',{area:['50%','80%']});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>