package com.yunqu.work.dao.other;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.utils.DateUtils;
@WebObject(name = "YearDataDao")
public class YearDataDao extends AppDaoContext {
	
	private static int thisYear = 2022;

	@WebControl(name = "getCheckUserCount",type = Types.TEMPLATE)
	public JSONObject getCheckUserCount() {
		EasySQL sql = new EasySQL();
		sql.append("select count(DISTINCT(t2.check_user_id)) count from yq_flow_apply t1,yq_flow_approve_result t2 where t1.apply_id = t2.business_id");
		sql.append(getUserId(),"and t1.apply_by = ?");
		sql.appendRLike(thisYear,"and t1.apply_date like ?");
		sql.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		sql.append(0,"and t2.check_result > ?");
		
		EasySQL sql2 = new EasySQL();
		sql2.append("select t2.check_name max_check_name,count(1) max_check_count from yq_flow_apply t1,yq_flow_approve_result t2 where t1.apply_id = t2.business_id");
		sql2.append(getUserId(),"and t1.apply_by = ?");
		sql2.appendRLike(thisYear,"and t1.apply_date like ?");
		sql2.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		sql2.append(0,"and t2.check_result > ?");
		sql2.append("and t2.check_name<>''");
		sql2.append("group by t2.check_name order by max_check_count desc limit 1");
		
		JSONObject object = new JSONObject();
		try {
			int checkCount = this.getQuery().queryForInt(sql.getSQL(), sql.getParams());
			JSONObject record = this.getQuery().queryForRow(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
			object.put("checkUserCount",checkCount);
			object.put("record", record);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	@WebControl(name = "getWorkData",type = Types.TEMPLATE)
	public JSONObject getWorkData() {
		EasySQL sql = new EasySQL();
		sql.append("select count(1) from yq_weekly where `year` = 2022");
		sql.append(getUserId(),"and creator = ?");
		
		EasySQL sql2 = new EasySQL();
		sql2.append("select count(1) from yq_task where 1=1");
		sql2.append(getUserId(),"and assign_user_id = ?");
		sql2.appendRLike(thisYear,"and month_id like ?");
		
		EasySQL sql3 = new EasySQL();
		sql3.append("select count(1) from yq_task where 1=1");
		sql3.append(getUserId(),"and creator = ?");
		sql3.appendRLike(thisYear,"and month_id like ?");
		
		
		JSONObject object = new JSONObject();
		try {
			int weeklyNum = this.getQuery().queryForInt(sql.getSQL(), sql.getParams());
			int doTaskNum = this.getQuery().queryForInt(sql2.getSQL(), sql2.getParams());
			int createTaskNum = this.getQuery().queryForInt(sql3.getSQL(), sql3.getParams());
			object.put("weeklyNum", weeklyNum);
			object.put("doTaskNum", doTaskNum);
			object.put("createTaskNum", createTaskNum);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	
	@WebControl(name = "getFlowData",type = Types.TEMPLATE)
	public JSONObject getFlowData() {
		EasySQL sql = new EasySQL();
		sql.append("select sum(t1.fill_time) sum_fill_time,count(1) flow_apply_count from yq_flow_apply t1 where 1=1");
		sql.append(getUserId(),"and t1.apply_by = ?");
		sql.appendRLike(thisYear,"and t1.apply_date like ?");
		sql.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		
		EasySQL sql2 = new EasySQL();
		sql2.append("select apply_time,apply_no,apply_title,apply_end_time,timestampdiff(MINUTE,apply_time,apply_end_time) ts,timestampdiff(second,apply_time,apply_end_time) second_ts,timestampdiff(hour,apply_time,apply_end_time) hour_ts,timestampdiff(day,apply_time,apply_end_time) day_ts from yq_flow_apply where apply_end_time<>''");
		sql2.append(getUserId(),"and apply_by = ?");
		sql2.appendRLike(thisYear,"and apply_date like ?");
		sql2.append(FlowConstants.FLOW_STAT_TODO,"and apply_state >= ?");
		sql2.append("ORDER BY ts limit 1");
		
		JSONObject object = new JSONObject();
		try {
			JSONObject flowNum = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			JSONObject maxCheckInfo = this.getQuery().queryForRow(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
			object.put("flowNum",flowNum);
			object.put("maxCheckInfo", maxCheckInfo);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	@WebControl(name = "getTripInfo",type = Types.TEMPLATE)
	public JSONObject getTripInfo() {
		EasySQL sql = new EasySQL();
		sql.append("select GROUP_CONCAT(DISTINCT(dest_city)) from yq_flow_bx_item t1,yq_flow_apply t2 where t1.business_id = t2.apply_id and  t1.start_city<>''");
		sql.append(getUserId(),"and t2.apply_by = ?");
		sql.appendRLike(thisYear,"and t2.apply_date like ?");
		
		EasySQL sql2 = new EasySQL();
		sql2.append("select start_date,start_city,dest_city,cost_day,contract_name from yq_flow_bx_item t1,yq_flow_apply t2 where t1.business_id = t2.apply_id and  t1.start_city<>''");
		sql2.append(getUserId(),"and t2.apply_by = ?");
		sql2.appendRLike(thisYear,"and t2.apply_date like ?");
		sql2.append("order by t1.cost_day desc limit 1");
		
		JSONObject object = new JSONObject();
		try {
			String destCity = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			JSONObject record = this.getQuery().queryForRow(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
			object.put("allDestCity",destCity);
			if(record==null) {
				object.put("record", new JSONObject());
			}else {
				object.put("record", record);
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	@WebControl(name = "getLoginSysInfo",type = Types.TEMPLATE)
	public JSONObject getLoginSysInfo() {
		StaffModel staffModel = getStaffInfo();
		Long joinDay = DateUtils.betweenDays(staffModel.getJoinDate(), EasyDate.getCurrentDateString("yyyy-MM-dd"));
		
		EasySQL sql = new EasySQL();
		sql.append("SELECT count(1) visit_count,max(right(visit_time,8)) max_visit_date,min(RIGHT(visit_time,8)) min_visit_date from yq_user_sign where 1=1");
		sql.append(getUserId(),"and user_id = ?");
		sql.appendRLike(thisYear,"and month_id like ?");
		
		JSONObject object = new JSONObject();
		try {
			JSONObject record = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			object.put("joinDay", joinDay);
			object.put("record", record);
			object.put("staff", staffModel);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	
}
