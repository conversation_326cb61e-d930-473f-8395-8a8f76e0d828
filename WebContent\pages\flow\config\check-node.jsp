<%@ page language="java" contentType="text/html;charset=UTF-8"%> 
<%@ include file="/pages/common/global.jsp" %> 
<%@ page language="java" contentType="text/html;charset=UTF-8"%> 
<EasyTag:override name="head"> 
    <title>流程节点配置</title> 
    <style> 
        .layui-timeline-item:hover a{opacity:1;} 
        .ibox-content{min-height: calc(100vh - 100px)}; 
    </style> 
</EasyTag:override> 
<EasyTag:override name="content"> 
        <form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm"> 
        	<input value="${param.flowCode}" type="hidden" id="flowCode" name="flowCode"/>
            <div class="ibox"> 
                <div class="ibox-title clearfix"> 
                     <div class="form-group"> 
                         <h5>流程节点配置</h5> 
                         <div class="input-group input-group-sm pull-right"> 
                             <button type="button" class="btn btn-sm btn-info" onclick="addNode()"> 新增</button> 
                             <button class="btn btn-sm btn-default ml-10" type="button" onclick="checkRoleConfig();">节点角色</button>
                         </div> 
                     </div> 
                  </div>  
                    <div class="ibox-content"> 
                            <table id="nodeTable"></table> 
                    </div> 
                </div> 
        </form> 
        
        	<script type="text/html" id="bar">
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="editNode">编辑</a>
  				<a class="layui-btn layui-btn-warn layui-btn-xs" lay-event="delNode">删除</a>
		   </script>
</EasyTag:override> 
<EasyTag:override name="script"> 
    <script type="text/javascript"> 
      
            $(function(){ 
                $("#dataMgrForm").render({success:function(){ 
                    initList(); 
                }}); 
            }); 
              
            function initList(){ 
                $("#dataMgrForm").initTable({ 
                    mars:'FlowConfigDao.nodeConfig', 
                    id:'nodeTable', 
                    page:false, 
                    height:'full-130', 
                    cellMinWidth:100, 
                    cols: [[ 
                     { 
                         field:'CHECK_INDEX', 
                         width:70,
                         title:'节点次序' 
                     },{ 
                         field:'NODE_NAME', 
                         title:'节点描述' 
                     },{ 
                         field:'NODE_CODE', 
                         title:'节点编码' 
                     },{ 
                         field:'REMARK', 
                         title:'备注' 
                     },{ 
                         field:'USER_NAME', 
                         title:'审批人', 
                         minWidth:160,
                         templet:function(row){ 
                        	 var checkType = row['CHECK_TYPE'];
                        	 var userName = row['USER_NAME'];
                        	 if(checkType=='1'){
                        		 return '指定人员'+userName;
                        	 }if(checkType=='20'){
                        		 return '上级主管';
                        	 }if(checkType=='23'){
                        		 return '直接主管或副主管';
                        	 }if(checkType=='22'){
                        		 return '直接上级';
                        	 }else if(checkType=='21'){
                        		 return '中心副总';
                        	 }else if(checkType=='30'){
                        		 return '选择审批人';
                        	 }else if(checkType=='10'){
                        		 return '指定角色';
                        	 }else{
	                        	 return '';
                        	 }
                         }
                     },{ 
                         field:'P_NODE_ID', 
                         title:'节点类型', 
                         width:70,
                         templet:function(row){ 
                             var pNodeId = row['P_NODE_ID']; 
                             var cNodeId = row['C_NODE_ID']; 
                             if(pNodeId=='0'){
                            	 return '开始';
                             }
                             if(cNodeId=='0'){
                            	 return '结束';
                             }
                             return '--';
                         } 
                     },{ 
                        field: 'CREATE_TIME', 
                        title: '操作时间', 
                        align:'center' 
                    },{ 
                        field:'NODE_STATE', 
                        title:'节点状态', 
                        width:70,
                        templet:function(row){ 
                            var state = row['NODE_STATE']; 
                            return state=='0'?'启用':'暂停'; 
                        } 
                    },{ 
                        field:'', 
                        title:'操作', 
                        toolbar: '#bar'
                    } 
                ]],done:function(){ 
                      
                } 
             }); 
           } 
              
            function editNode(data){ 
                var id = data['NODE_ID']; 
                var flowCode = $("[name='flowCode']").val(); 
                popup.layerShow({id:'editNode',area:['600px','500px'],full:true,offset:'20px',url:'${ctxPath}/pages/flow/config/node-edit.jsp',title:'修改',data:{nodeId:id,flowCode:flowCode}}); 
            } 
            function addNode(){ 
                var flowCode = $("[name='flowCode']").val(); 
                popup.layerShow({id:'editNode',area:['600px','500px'],full:true,offset:'20px',url:'${ctxPath}/pages/flow/config/node-edit.jsp',title:'新增',data:{nodeId:'',flowCode:flowCode}}); 
            } 
            
            function delNode(data){
            	layer.confirm('确定是否删除?',{icon:3,icon:'50px'},function(){
            		ajax.remoteCall("${ctxPath}/servlet/flow/node?action=delNodeConf",{nodeId:data['NODE_ID']},function(result) { 
        				if(result.state == 1){
        					layer.msg(result.msg,{icon:1,time:1200},function(){
        						location.reload();
        					});
        				}else{
        					layer.alert(result.msg,{icon: 5});
        				}
        			});
            	});
            }
            
            function checkRoleConfig(){
            	popup.openTab({id:'checkRoleConfig',url:'/yq-work/pages/flow/config/check-role.jsp',title:'审批角色'});
            }
              
            function queryData(){ 
                $("#dataMgrForm").queryData({id:'nodeTable'}); 
            } 
              
    </script> 
</EasyTag:override> 
<%@ include file="/pages/common/layout-layui-auto.jsp" %> 