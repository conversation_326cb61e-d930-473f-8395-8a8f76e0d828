<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="meetForm" class="form-horizontal" data-mars="ProjectDao.record" autocomplete="off" data-mars-prefix="project.">
		   	<input type="hidden" id="projectId" value="${param.projectId}" name="project.PROJECT_ID"/>
		  	<table class="layui-table">
	  		     <tr>
   					<td style="width: 100px;">会议主持人</td>
                    <td>
                    	<input type="hidden" name="meeting.meetingHostUserId"/>
		                <input type="text" onclick="singleUser(this)" placeholder="项目经理" readonly="readonly" class="form-control input-sm select-icon" name="meeting.meetingHostUserName"/>
                    </td>
                </tr>
	  		     <tr>
   					<td style="width: 100px;">会议参与人</td>
                    <td>
                    	<input type="hidden" name="meeting.meetingUserIds"/>
		                <input type="text" onclick="multiUser(this)" placeholder="开发,工程,销售,售前,总裁室"  readonly="readonly" class="form-control input-sm select-icon" name="meeting.meetingUserNames"/>
                    </td>
                </tr>
	  		     <tr>
   					<td>开会时间</td>
                    <td>
                    	 <select name="meeting.meetingTime" style="width: 100px;display: inline-block;" class="form-control input-sm">
                    	 	<option value="1">每月1号</option>
                    	 	<option value="1">每月15号</option>
                    	 	<option value="9">每周一</option>
                    	 	<option value="0">暂时取消</option>
                    	 </select>
                    	 <small class="ml-10">当天九点线上或线下召开会议</small>
                    </td>
                </tr>
	  		     <tr>
   					<td>开会主题</td>
                    <td>
                    	 <textarea style="height: 80px;" name="meeting.meetingTheme" class="form-control input-sm">项目交付过程中进行讨论</textarea>
                    </td>
                </tr>
	  		     <tr>
   					<td>备注</td>
                    <td>
                    	<small>会议当天提前半小时会发送会议邮件和微信推送消息，会议结束后主持人应填写会议记录。</small>
                    </td>
                </tr>
		  	</table>
            <div class="layer-foot text-c">
				<button type="button" class="btn btn-primary btn-sm" onclick="ajaxSubmitForm()"> 保 存  </button>
				<button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
			</div>	
  	   </form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			$("#meetForm").render({success:function(result){
				
			}});  
		});
		
		function ajaxSubmitForm(){
			layer.msg('保存成功',{icon:1,offset:'20px',time:1200});
			layer.closeAll();
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
