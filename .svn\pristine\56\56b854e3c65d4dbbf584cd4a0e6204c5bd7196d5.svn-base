<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<EasyTag:override name="head">
    <title>项目看板</title>
    <link href="${ctxPath}/static/zui/css/zui.min.css" rel="stylesheet">
    <script src="${ctxPath}/static/zui/js/zui.min.js"></script>
    <link href="${ctxPath}/static/zui/lib/board/zui.board.min.css" rel="stylesheet">
    <script src="${ctxPath}/static/zui/lib/board/zui.board.min.js"></script>
    <style type="text/css">
        /* 保证影子元素一直可见 */
        .board-item.drag-shadow {
            z-index: 9999
        }

        .addItem {
            cursor: pointer;
        }

        .addItem:hover {
            text-decoration: underline;
            color: #999;
        }

        .panel-primary .panel-heading a {
            color: #fff;
        }

        .panel-heading a {
            opacity: 0;
        }

        .panel-heading:hover a {
            opacity: 1;
        }

        .board {
            width: 24%;
        }

        .board-item {
            cursor: pointer;
            border-radius: 6px;
            padding: 8px;
        }

        .board-item:hover {
            background-color: #f5f5f5 !important;
        }

        .task-info {
            color: #666;
            font-size: 12px;
            line-height: 18px;
        }

        .project-name {
            color: #3280fc;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 4px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="kanbanForm">
        <div class="layui-card">
            <div class="layui-card-header">
                <span id="KANBAN_NAME">我负责的任务</span>
                <span class="f-12 ml-20" id="CREATE_TIME"></span>
            </div>
            <div class="layui-card-body" style="min-height: 300px;">
                <div class="boards" id="myBoard" style="position: static;" data-mars="TaskDao.myTaskListKanban('1')" data-template="taskTpl">

                </div>
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-header">
                <span>我创建的任务</span>
                <span class="f-12 ml-20"></span>
            </div>
            <div class="layui-card-body" style="min-height: 300px;">
                <div class="boards" id="myApplyBoard" style="position: static;" data-mars="TaskDao.myTaskListKanban('2')" data-template="taskTpl">

                </div>
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-header">
                <span>抄送我的任务</span>
                <span class="f-12 ml-20"></span>
            </div>
            <div class="layui-card-body" style="min-height: 300px;">
                <div class="boards" id="myCCBoard" style="position: static;" data-mars="TaskDao.myTaskListKanban('3')" data-template="taskTpl">

                </div>
            </div>
        </div>
    </form>
    <script type="text/x-jsrender" id="taskTpl">
              <div class="board panel panel-info" data-id="10">
                <div class="panel-heading">
                   <strong> 待办中</strong>
                </div>
                <div class="panel-body">
                      <div class="board-list">
                        <div class="board-item board-item-shadow" style="height: 34px;"></div>
                        {{for data}}
                          {{if TASK_STATE== "10"}}
                            <div class="board-item" data-panel-id="10" data-item-id="{{:TASK_ID}}">
                                <p class="mb-5" style="color:#03b8cf;">截止: {{:DEADLINE_AT}}</p>
                                <div><strong>{{:TASK_NAME}}</strong></div>
                                <div class="project-name">{{:PROJECT_NAME}}</div>
                                <div class="task-info">
                                    <span>难度: {{:TASK_DIFFICULTY}}</span> | 
                                    <span>计划工时: {{:PLAN_WORK_HOUR}}h</span> |
                                </div>
                                <div class="task-info">
                                    {{if ~root.taskType!="1"}}
                                    <span>负责人: {{:ASSIGN_USER_NAME}}</span> |
                                    {{/if}}
                                    <span>进度: {{:PROGRESS}}%</span>
                                </div>
                            </div>
                          {{/if}}
                        {{/for}}
                      </div>
                    </div>
              </div>
              <div class="board panel panel-primary" data-id="20">
                <div class="panel-heading">
                   <strong>进行中</strong>
                </div>
                <div class="panel-body">
                      <div class="board-list">
                        <div class="board-item board-item-shadow" style="height: 34px;"></div>
                        {{for data}}
                          {{if TASK_STATE== "20"}}
                            <div class="board-item" data-panel-id="10" data-item-id="{{:TASK_ID}}">
                                <p class="mb-5" style="color:#3280fc;">截止: {{:DEADLINE_AT}}</p>
                                <div><strong>{{:TASK_NAME}}</strong></div>
                                <div class="project-name">{{:PROJECT_NAME}}</div>
                                <div class="task-info">
                                    <span>难度: {{:TASK_DIFFICULTY}}</span> | 
                                    <span>计划工时: {{:PLAN_WORK_HOUR}}h</span>
                                </div>
                                <div class="task-info">
                                    {{if ~root.taskType!="1"}}
                                    <span>负责人: {{:ASSIGN_USER_NAME}}</span> |
                                    {{/if}}
                                    <span>进度: {{:PROGRESS}}%</span>
                                </div>
                            </div>
                          {{/if}}
                        {{/for}}
                      </div>
                    </div>
              </div>
              <div class="board panel panel-warning" data-id="20">
                <div class="panel-heading">
                   <strong>超时未完成 ({{:data2.length}})</strong>
                </div>
                <div class="panel-body">
                      <div class="board-list">
                        <div class="board-item board-item-shadow" style="height: 34px;"></div>
                        {{for data2}}
                          {{if TASK_STATE== "20"}}
                            <div class="board-item" data-panel-id="10" data-item-id="{{:TASK_ID}}">
                                <p class="mb-5" style="color:#d9534f;">截止: {{:DEADLINE_AT}} {{call:DEADLINE_AT fn='timeBetween'}}</p>
                                <div><strong>{{:TASK_NAME}}</strong></div>
                                <div class="project-name">{{:PROJECT_NAME}}</div>
                                <div class="task-info">
                                    <span>难度: {{:TASK_DIFFICULTY}}</span> | 
                                    <span>计划工时: {{:PLAN_WORK_HOUR}}h</span>
                                </div>
                                <div class="task-info">
                                    {{if ~root.taskType!="1"}}
                                    <span>负责人: {{:ASSIGN_USER_NAME}}</span> |
                                    {{/if}}
                                    <span>进度: {{:PROGRESS}}%</span>
                                </div>
                            </div>
                          {{/if}}
                        {{/for}}
                      </div>
                    </div>
              </div>
              <div class="board panel panel-success" data-id="30">
                <div class="panel-heading">
                   <strong>本周已完成 ({{:data3.length}})</strong>
                </div>
                <div class="panel-body">
                      <div class="board-list">
                        <div class="board-item board-item-shadow" style="height: 34px;"></div>
                        {{for data3}}
                          {{if TASK_STATE== "30"}}
                            <div class="board-item" data-panel-id="10" data-item-id="{{:TASK_ID}}">
                                <p class="mb-5" style="color:#9aa72b;">截止: {{:DEADLINE_AT}}</p>
                                <div><strong>{{:TASK_NAME}}</strong></div>
                                <div class="project-name">{{:PROJECT_NAME}}</div>
                                <div class="task-info">
                                    {{if ~root.taskType!="1"}}
                                    <span>负责人: {{:ASSIGN_USER_NAME}}</span> |
                                    {{/if}}
                                    <span>实际工时: {{:WORK_HOUR}}h</span>
                                </div>
                            </div>
                          {{/if}}
                        {{/for}}
                      </div>
                    </div>
              </div>

    </script>
</EasyTag:override>


<EasyTag:override name="script">
    <script type="text/javascript">

        function isSelfShow() {
            if (true) {
                return 'display:none;';
            }
                // else {
            //     return '';
            // }
        }

        $(function () {
            $("#kanbanForm").render({
                success: function () {
                    $(".board-item").click(function () {
                        var t = $(this);
                        popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:"${ctxPath}/pages/task/task-detail.jsp",title:'任务详情',data:{taskId:t.data().itemId,taskState:t.data().panelId,status:1}});
                    });
                }
            });
        });

        var kanbanId = '${param.kanbanId}';

        function loadProgress(value,taskId){
            layui.use('slider',function(){
                var slider = layui.slider;
                slider.render({
                    elem: '#slide',min: 0,max: 100,theme: '#1E9FFF',value:value,step:5,
                    setTips: function(value){
                        return value;
                    },
                    change: function(value){
                        $('#slider-tips').html("已完成进度"+value + '%');
                        ajax.remoteCall("${ctxPath}/servlet/task?action=updateProgress",{taskId:taskId,progress:value},function(result) {
                            if(result.state != 1){
                                layer.alert(result.msg,{icon: 5});
                            }
                        },{loading:false});
                    }
                });
            });
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>