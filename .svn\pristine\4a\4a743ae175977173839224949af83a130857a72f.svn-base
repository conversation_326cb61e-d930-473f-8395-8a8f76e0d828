<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title></title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="kqPeopleEditForm" class="form-horizontal" data-mars="KqDao.kqPeople" autocomplete="off" data-mars-prefix="kqPeople.">
        <input type="hidden" value="${param.incomeStageId}" name="kqPeople.kq_user_id"/>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td width="80px" class="required">员工姓名</td>
                <td style="width: 40%">
                    <input name="kqPeople.kq_user_id" type="hidden">
                    <input name="kqPeople.kq_user_name" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm">
                </td>
                <td width="80px" class="required">部门名称</td>
                <td style="width: 40%">
                    <input type="text"  name="kqPeople.dept_name" class="form-control">
                </td>
            </tr>
            <tr>
                <td width="80px" class="required">上班时间</td>
                <td style="width: 40%">
                    <input type="text"  name="kqPeople.sb_time" onClick="WdatePicker({dateFmt:'HH:mm'})" class="form-control">
                </td>
                <td width="80px" class="required">下班时间</td>
                <td style="width: 40%">
                    <input type="text"  name="kqPeople.xb_time" onClick="WdatePicker({dateFmt:'HH:mm'})" class="form-control">
                </td>
            </tr>
            <tr>
                <td width="80px" class="required">是否考勤</td>
                <td colspan="3" style="width: 40%">
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio"  value="0" name="kqPeople.STATE"><span>是</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="1" name="kqPeople.STATE"><span>否</span>
                    </label>
                </td>
            </tr>
            </tbody>
        </table>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="kqPeopleEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("kqPeopleEdit");
        $(function(){
            $("#kqPeopleEditForm").render({success:function(result){
                }});
        });
        kqPeopleEdit.ajaxSubmitForm = function(){
            if(form.validate("#kqPeopleEditForm")){
                var data = form.getJSONObject("#kqPeopleEditForm");
                ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=addPeople",data,function(result) {
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            layer.closeAll();
                            kqPeopleList.query();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            };
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>