package com.yunqu.work.servlet.common;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.CommentService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.WxMsgService;
@WebServlet("/servlet/comment")
public class CommentServlet extends AppBaseServlet {
	private static final long serialVersionUID = -9064225788599799486L;
	
	public EasyResult actionForAdd(){
		JSONObject params = getJSONObject();
		String title = params.getString("title");
		String content = params.getString("content");
		String fkId = params.getString("fkId");
		String source = params.getString("source");
		String commentNoticeType = params.getString("commentNoticeType");
		if(StringUtils.isBlank(content)||StringUtils.isBlank(fkId)){
			return EasyResult.fail("不能为空!");
		}
		EasyResult result = CommentService.getService().addComment(getUserId(),getUserName(),fkId, content, WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"),source);
		if(result.isOk()){
			if(StringUtils.notBlank(commentNoticeType)){
				MessageModel model=new MessageModel();
				if("affair".equals(source)) {
					model.setUrl("/yq-work/affair/"+fkId);
				}else if("task".equals(source)) {
					model.setUrl("/yq-work/task/"+fkId);
				}else if("weekly".equals(source)) {
					model.setUrl("/yq-work/weekly/"+fkId);
				}else if("remind".equals(source)) {
					model.setUrl("/yq-work/notice/"+fkId);
				}
				
				String assignUserId= params.getString("ASSIGN_USER_ID");
				//任务提醒
				if(StringUtils.notBlank(assignUserId)){
					String creator = params.getString("CREATOR");
					model.setTitle(title);
					model.setFkId(fkId);
					model.setSender(getUserId());
					model.setDesc(content);
					if(getUserId().equals(creator)){
						model.setReceiver(assignUserId);
						this.sendMsg(commentNoticeType,model,true);
					}else if(getUserId().equals(assignUserId)){
						model.setReceiver(creator);
						this.sendMsg(commentNoticeType,model,true);
					}else{
						//其他人评论 非创建人和执行人 都要发
						model.setReceiver(assignUserId);
						this.sendMsg(commentNoticeType,model,true);
						model.setReceiver(creator);
						this.sendMsg(commentNoticeType,model,true);
					}
				}
				//周报提醒
				String receiver = params.getString("RECEIVER");
				if(StringUtils.notBlank(receiver)){
					if(getUserId().equals(receiver)){//周报审批人
						model.setTitle(params.getString("TITLE"));
						model.setSender(getUserId());
						model.setReceiver(params.getString("CREATOR"));
						model.setDesc(content);
						this.sendMsg(commentNoticeType,model);
					}
				}
			}
		}
		return EasyResult.ok(null,"评论成功.");
	}
	
	private void sendMsg(String commentNoticeType,MessageModel model){
		model.setCc(new String[] {model.getSender()});
		sendMsg(commentNoticeType,model,false);
	}
	
	private void sendMsg(String commentNoticeType,MessageModel model,boolean isTask){
		String[] commentNoticeTypes=commentNoticeType.split(",");
		for(int i=0;i<commentNoticeTypes.length;i++){
			String type=commentNoticeTypes[i];
			if(StringUtils.notBlank(type)){
				switch (type) {
				case "wx":
					WxMsgService.getService().sendCommentTaskMsg(model);
					break;
				case "email":
					if(isTask){
						String desc = model.getDesc();
						desc = desc.replaceAll("\n", "<br>");
						model.setDesc(desc);
						model.setTplName("taskComment.html");
						EmailService.getService().sendTaskCommentEmail(model);
					}
					break;
				default:
					break;
				}
			}
		}
	}
	public EasyResult actionForDel(){
		String commentId=getJsonPara("commentId");
		try {
			this.getQuery().executeUpdate("delete from yq_comments where comment_id = ?", commentId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail("删除失败!");
		}
		return EasyResult.ok(null,"删除成功.");
	}
	
	public EasyResult actionForSetRead() {
		String id=getJsonPara("id");
		try {
			this.getQuery().executeUpdate("update yq_message set msg_state = 2,read_time = ? where msg_id = ?",EasyDate.getCurrentDateString(),id);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
}
