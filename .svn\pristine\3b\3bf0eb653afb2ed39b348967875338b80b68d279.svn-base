package com.yunqu.work.dao.erp;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="OrderDao")
public class OrderDao extends AppDaoContext {

	@WebControl(name="followInfo",type=Types.RECORD)
	public JSONObject followInfo(){
		String followId = param.getString("fkId");
		return queryForRecord("select * from yq_erp_order_follow where follow_id = ?",followId);
	}
	
	@WebControl(name="getOrderNo",type=Types.TEXT)
	public JSONObject getOrderNo(){
		int dateId = EasyCalendar.newInstance().getDateInt();
		try {
			String val = this.getQuery().queryForString("select max(order_no) from yq_erp_order where order_no like '"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult(dateId+"1001");
			}else {
				return getJsonResult(Integer.valueOf(val)+1);
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	@WebControl(name="getReviewNo",type=Types.TEXT)
	public JSONObject getReviewNo(){
		int dateId = EasyCalendar.newInstance().getDateInt();
		try {
			String val = this.getQuery().queryForString("select max(review_no) from yq_erp_order_review where review_no like 'P-"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult("P-"+dateId+"1001");
			}else {
				val = val.replace("P-", "");
				return getJsonResult("P-"+String.valueOf(Long.valueOf(val)+1));
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String orderId = param.getString("orderId");
		return queryForRecord("select * from yq_erp_order where order_id = ?",orderId);
	}
	@WebControl(name="applyInfo",type=Types.RECORD)
	public JSONObject applyInfo(){
		String orderId = param.getString("orderId");
		return queryForRecord("select t1.*,t3.`name` SUPPLIER_NAME,t4.cust_name from yq_erp_order t1 LEFT JOIN yq_project_contract t2 on t1.contract_id=t2.CONTRACT_ID LEFT JOIN yq_erp_supplier t3 on t3.supplier_id=t1.supplier_id left join yq_crm_customer t4 on t4.cust_id = t1.cust_id where t1.order_id = ?",orderId);
	}
	
	@WebControl(name="followList",type=Types.TEMPLATE)
	public JSONObject followList(){
		EasySQL sql=getEasySQL("select * from yq_erp_order_follow where 1=1");
		sql.append(param.getString("orderId"),"and order_id = ?");
		sql.append("order by add_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="followPage",type=Types.PAGE)
	public JSONObject followPage(){
		EasySQL sql=getEasySQL("select t1.*,t2.CONTRACT_NAME,t2.ORDER_NO,t2.CUST_ID from yq_erp_order_follow t1,yq_erp_order t2 where 1=1");
		sql.append("and t1.order_id = t2.order_id");
		sql.append(param.getString("orderId"),"and t1.ORDER_ID = ?");
		sql.appendLike(param.getString("orderNo"),"and t2.ORDER_NO like ?");
		sql.append("order by t1.add_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select t1.*,t3.`name` SUPPLIER_NAME from yq_erp_order t1");
		sql.append("LEFT JOIN yq_project_contract t2 on t1.contract_id=t2.CONTRACT_ID LEFT JOIN yq_erp_supplier t3 on t3.supplier_id=t1.supplier_id");
		sql.append("where 1=1");
		sql.append(param.getString("supplierId"),"and t1.SUPPLIER_ID = ?");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="goodsList",type=Types.LIST)
	public JSONObject goodsList(){
		EasySQL sql=getEasySQL("select * from yq_erp_goods where 1=1");
		sql.append(param.getString("orderId"),"and order_id = ?");
		return queryForList(sql.getSQL(), sql.getParams());
		
	}
	
	@WebControl(name="custList",type=Types.LIST)
	public JSONObject custList(){
		EasySQL sql=getEasySQL("select t1.*,t2.CONTRACT_NAME,t3.`name` supplier_name from yq_erp_order t1,yq_project_contract t2,yq_erp_supplier t3 where t1.supplier_id = t3.supplier_id and t1.contract_id = t2.contract_id");
		sql.append(param.getString("custId"),"and t1.cust_id = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "reviewList",type=Types.PAGE)
	public JSONObject reviewList() {
		EasySQL sql = getEasySQL("select t1.*,t2.contract_name,t2.order_no,t2.total_price,t3.node_name,t3.user_name check_by from yq_erp_order_review t1,yq_erp_order t2,");
		sql.append("yq_erp_order_check_node t3");
		sql.append(" where 1=1");
		sql.append("and t3.node_id = t1.current_node_id");
		sql.append("and t1.order_id = t2.order_id");
		if(!isSuperUser()) {
			sql.append(getUserId(),"and FIND_IN_SET(?,t1.check_ids)");
		}
		sql.append(param.getString("reviewState"),"and t1.review_state = ?");
		sql.append("order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "paymentList",type=Types.PAGE)
	public JSONObject paymentList() {
		EasySQL sql = getEasySQL("select t1.*,t2.contract_name,t2.order_no,t2.total_price from yq_erp_order_payment t1,yq_erp_order t2 where 1=1");
		sql.append("and t1.order_id = t2.order_id");
		if(!isSuperUser()) {
			sql.append(getUserId(),"and FIND_IN_SET(?,t1.check_ids)");
		}
		sql.append(param.getString("paymentState"),"and t1.payment_state = ?");
		sql.append("order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "reviewDetailList",type=Types.PAGE)
	public JSONObject reviewDetailList() {
		EasySQL sql = getEasySQL("select t4.node_name,t3.get_time,t3.check_desc,t3.check_time,t3.node_id,t3.result_id,t3.check_result,t3.check_name,t1.*,t2.contract_name,t2.order_no,t2.total_price from yq_erp_order_review t1");
		sql.append(",yq_erp_order t2,yq_erp_order_review_result t3,yq_erp_order_check_node t4");
		sql.append("where 1=1");
		int isOk= param.getIntValue("todo");
		if(isOk==0) {
			sql.append("and t3.check_result > 0 ");
		}else {
			sql.append("and t1.current_node_id = t3.node_id");
		}
		sql.append("and t4.node_id = t3.node_id");
		sql.append("and t1.order_id = t2.order_id");
		sql.append("and t1.review_id = t3.review_id");
		if(!isSuperUser()) {
			sql.append(getUserId(),"and t3.check_user_id = ?");
		}
		sql.append(param.getString("checkResult"),"and t3.check_result = ?");
		sql.append("order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "paymentDetailList",type=Types.PAGE)
	public JSONObject paymentDetailList() {
		EasySQL sql = getEasySQL("select t3.check_time,t3.check_line,t3.result_id,t3.check_result,t3.check_name,t1.*,t2.contract_name,t2.order_no,t2.total_price from yq_erp_order_payment t1,yq_erp_order t2,yq_erp_order_payment_result t3 where 1=1");
		sql.append("and t1.order_id = t2.order_id");
		sql.append("and t1.payment_id = t3.payment_id");
		if(!isSuperUser()) {
			sql.append(getUserId(),"and t3.check_id = ?");
		}
		sql.append("and t3.check_line <= t1.current_check_line");
		sql.append(param.getString("checkResult"),"and t3.check_result = ?");
		sql.append("order by t3.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "reviewResult",type=Types.LIST)
	public JSONObject reviewResult() {
		EasySQL sql = getEasySQL("select t1.*,t2.node_name from yq_erp_order_review_result t1,yq_erp_order_check_node t2 where 1=1");
		sql.append("and t1.node_id = t2.node_id");
		sql.append(param.getString("orderId"),"and t1.order_id = ?",false);
		sql.append("order by t2.check_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "paymentResult",type=Types.LIST)
	public JSONObject paymentResult() {
		EasySQL sql = getEasySQL("select t1.* from yq_erp_order_payment_result t1 where 1=1");
		sql.append(param.getString("orderId"),"and t1.order_id = ?",false);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeConfig",type=Types.LIST)
	public JSONObject nodeConfig() {
		EasySQL sql = getEasySQL("select t1.* from yq_erp_order_check_node t1 where 1=1");
		sql.append(param.getString("busiType"),"and t1.busi_type = ?",false);
		sql.append("order by t1.check_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeInfo",type=Types.RECORD)
	public JSONObject nodeInfo() {
		EasySQL sql = getEasySQL("select t1.* from yq_erp_order_check_node t1 where 1=1");
		sql.append(param.getString("nodeId"),"and t1.node_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	@WebControl(name = "nodeDict",type=Types.DICT)
	public JSONObject nodeDict() {
		EasySQL sql = getEasySQL("select t1.node_id,concat(t1.node_name,'|',t1.user_name) from yq_erp_order_check_node t1 where 1=1");
		sql.append(param.getString("busiType"),"and t1.busi_type = ?",false);
		sql.append(param.getString("nodeId"),"and t1.node_id <> ?");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	
}
