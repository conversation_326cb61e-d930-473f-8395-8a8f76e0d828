function custDetail(custId) {
    popup.openTab({id: 'custDetail', title: '客户详情', url: '${ctxPath}/pages/crm/cust/cust-detail.jsp', data: {custId: custId}});
}

function contractDetail(custId, contractId) {
    popup.openTab({id: 'contractDetail', title: '合同详情', url: '${ctxPath}/project/contract', data: {contractId: contractId, custId: custId, isDiv: 0}});
}


//收款提示：合同的下次收款时间，超时未收、一周内、一月内将高亮显示
function nextReceiptTime(amount,totalReceipt,nextReceiptDate) {
    amount = parseFloat(amount);
    totalReceipt = parseFloat(totalReceipt)

    if(amount == 0){
        return "<label class='label label-info label-outline'>未计划</label>";
    }
    if(totalReceipt >= amount){
        return "<label class='label label-success'>已完成</label>";
    }
    if(nextReceiptDate == ""|| nextReceiptDate == "0" || nextReceiptDate == null){
        return "<label class='label label-info label-outline'>未计划</label>";
    }
    var nextReceiptDate2 = new Date(nextReceiptDate);
    nextReceiptDate2.setHours(23, 59, 59, 999);
    var timeNow = new Date();
    var minute = 1000 * 60;
    var hour = minute * 60;
    var day = hour * 24;
    var month = day * 30;
    var diffValue =  nextReceiptDate2 - timeNow;
    var diffDay = diffValue / day;
    var diffMonth = diffValue / month;
    var diffWeek = diffValue / (7 * day);

    if(diffDay <= 0){
        return "<label class='label label-danger'>已超时</label>"+ nextReceiptDate;
    }
    if(diffWeek <= 1){
        return "<label class='label label-warning'>一周内</label> "+ nextReceiptDate;
    }
    if(diffMonth <= 1){
        return "<label class='label label-info'>一月内</label>"+ nextReceiptDate;
    }
    if(diffMonth <= 6){
        return "<label class='label label-info label-outline'>半年内</label>"+ nextReceiptDate;
    }
    if(diffMonth <= 12){
        return "<label class='label label-info label-outline'>一年内</label>"+ nextReceiptDate;
    }

    return nextReceiptDate;
}
