<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的申请</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input type="hidden" name="flowCode" value="${flowCode}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程名称</span>
						 	<input class="form-control input-sm" name="flowName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">待办人</span>
						 	<input class="form-control input-sm" name="checkName">
						 </div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">流程状态</span>	
							<select name="applyState" onchange="queryData();" class="form-control input-sm">
							      <option value="">请选择 </option>
                    			  <option data-class="label label-warning" value="0">草稿</option>
							      <option data-class="label label-warning" value="10">待审批</option>
                    			  <option data-class="label label-success" value="20">审批中</option>
                    			  <option data-class="label label-danger" value="21">审批退回</option>
                    			  <option data-class="label label-danger" value="30">已完成</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">付款状态</span>	
							<select name="payState" onchange="queryData();" class="form-control input-sm">
							      <option value="">请选择 </option>
                    			  <option data-class="label label-default" value="0">--</option>
							      <option data-class="label label-warning" value="1">待付款</option>
                    			  <option data-class="label label-success" value="2">已付款</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>

		  </script>
		</form>
		
		 <script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default" lay-event="refreshData">刷新</button>
				<c:if test="${param.payState=='1'}">
 					<EasyTag:res resId="To_PAY_AUTH">
			 			<button class="btn btn-sm btn-info ml-10" lay-event="toPay">付款</button>
 					</EasyTag:res>
				</c:if>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				var applyState = '${param.applyState}';
				if(applyState){
					$("[name='applyState']").val(applyState);
					$("[name='applyState']").parent().hide();
				}
				var payState = '${param.payState}';
				if(payState){
					$("[name='payState']").val(payState);
					$("[name='payState']").parent().hide();
				}
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'BxDao.flowList',
					id:'flowTable',
					page:true,
					toolbar:'#btnBar',
					rowDoubleEvent:'flowDetail',
					height:'full-130',
					limit:20,
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },
					 {
	            	 	type: 'checkbox',
						title: '序号',
						align:'left'
					 },{
						 field:'APPLY_NO',
						 title:'编号',
						 width:160,
						 templet:function(row){
							 return row['APPLY_NO'];
						 }
					 },{
						 field:'APPLY_REMARK',
						 title:'报销说明',
						 width:120
					 },{
						 field:'BX_MONEY',
						 title:'报销金额',
						 width:100
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
					    field: 'CHECK_TIME',
						title: '审批时间',
						align:'center',
					    width:160
					},{
						field:'APPLY_STATE',
						title:'流程状态',
						width:80,
						templet:function(row){
							var val = row['APPLY_STATE'];
							return getText(val,'applyState');
						}
					},{
						field:'NODE_NAME',
						title:'节点名称',
						width:80
					},{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					},{
						field:'CHECK_NAME',
						title:'审批人',
						width:80,
						templet:function(row){
							return row['CHECK_NAME'];
						}
					},{
						field:'CHECK_RESULT',
						title:'审批结果',
						width:80,
						templet:function(row){
							var json  = {'0':'待审批','1':'审批通过','2':'审批拒绝'};
							var checkResult = row['CHECK_RESULT']||'';
							return json[checkResult];
						}
					},{
						field:'',
						title:'操作',
						width:50,
						fixed:'right',
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function flowDetail(data){
			var id = data['FLOW_CODE'];
			var json  = $.extend({},{businessId:data['APPLY_ID'],id:id});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/servlet/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
		
		function refreshData(){
		   location.reload();
		}
		
		function toPay(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
		   }
		   var ids = [];
		   for(var index in dataList){
			   ids.push(dataList[index]['APPLY_ID']);
		   } 
		   layer.confirm('确认操作吗,不可逆',{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/bx?action=updatePayState",{businessIds:ids.join()},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,function(){
							queryData();
						})
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>