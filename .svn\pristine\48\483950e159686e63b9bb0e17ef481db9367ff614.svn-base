package com.yunqu.work.utils;

import java.util.Properties;

import javax.mail.Authenticator;
import javax.mail.Message;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.easitline.common.utils.string.StringUtils;

public class EmailUtils {
	
	
	private Properties props;// 系统属性
	private Session mailSession;// 邮件会话对象
	private MimeMessage mimeMsg; // MIME邮件对象
	
	class Auth extends Authenticator{
		private String username = "";
		private String password = "";
	 
		public Auth(String username, String password) {
			this.username = username;
			this.password = password;
		}
		public PasswordAuthentication getPasswordAuthentication() {
			return new PasswordAuthentication(username, password);

	}
  }
 
	public EmailUtils(String smtpHost, String Port, String MailUsername, String MailPassword) {
		Auth au = new Auth(MailUsername, MailPassword);
		// 设置系统属性
		props = java.lang.System.getProperties(); // 获得系统属性对象
		props.put("mail.smtp.host", smtpHost); // 设置SMTP主机
		props.put("mail.smtp.port", Port); // 设置服务端口号
		props.put("mail.smtp.auth", "true");// 同时通过验证
		// 获得邮件会话对象 
		mailSession = Session.getInstance(props, au);
		mailSession.setDebug(true);
	}
  
	public boolean sendingMimeMail(String[] mailFrom, String MailTo,
			String mailCopyTo, String mailBCopyTo, String mailSubject,
			String mailBody) {
		try { 
			// 创建MIME邮件对象
			mimeMsg = new MimeMessage(mailSession);
			// 设置发信人
			mimeMsg.setFrom(new InternetAddress(mailFrom[0],mailFrom[1],"UTF-8"));
			// 设置收信人
			if (MailTo != null) {
				mimeMsg.setRecipients(Message.RecipientType.TO, InternetAddress.parse(MailTo));
			} 
			// 设置抄送人
			if (StringUtils.notBlank(mailCopyTo)) {
				mimeMsg.setRecipients(javax.mail.Message.RecipientType.CC,InternetAddress.parse(mailCopyTo));
			}
			// 设置暗送人
			if (StringUtils.notBlank(mailBCopyTo)) {
				mimeMsg.setRecipients(javax.mail.Message.RecipientType.BCC,InternetAddress.parse(mailBCopyTo));
			}
			// 设置邮件主题 
			mimeMsg.setSubject(mailSubject, "gb2312");
			// 设置邮件内容，将邮件body部分转化为HTML格式
			mimeMsg.setContent(mailBody, "text/html;charset=gb2312");
			//mimeMsg.setDataHandler(new javax.activation.DataHandler(
				//	new StringDataSource(MailBody, "text/html")));
			// 发送邮件
			Transport.send(mimeMsg);
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}
	
	/**
	 * 
	 * @param senderUserName 发送人姓名
	 * @param mailto 收件人
	 * @param cc 抄送人
	 * @param title 主题
	 * @param desc 内容详情
	 * @return
	 */
	public static boolean sendMsgMail(String senderUserName,String mailto,String cc,String title,String desc){
		String smtpHost ="smtp.exmail.qq.com";
		String Port="25";
		String MailUsername = "<EMAIL>";
		String MailPassword = "kk33m1t#Yq";
		String MailFrom = "<EMAIL>";
		if(mailto == null){
			System.out.println("Servlet parameter Wrongs");
			return false;
		}
		EmailUtils send=new EmailUtils(smtpHost,Port,MailUsername,MailPassword);
		if(send.sendingMimeMail(new String[]{MailFrom,senderUserName}, mailto, cc, "", title, desc)){
			return true;
		}else{
			return false;
		}
	}
	public static void main(String[] args) {
		sendMsgMail("陈小丰","<EMAIL>,<EMAIL>","<EMAIL>","title","已审核");
	}
}
