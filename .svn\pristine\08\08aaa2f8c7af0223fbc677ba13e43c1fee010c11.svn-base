<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>待我审批的</title>
	<style>
		#oaForm{display: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="oaForm" data-mars="OaDao.loadMyDoXml">
             	<div class="ibox">
	              	<div class="ibox-content">
	              		<p>总流程数：<span id="count"></span></p><br>
	              		<table id="oaTable"></table>
	              	</div> 
                </div>
        </form>
        
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
		var kqList={
			init:function(){
				
			}
		}
		
		function getNewData(data){
			var result = [];
			for(var index in data){
				var item  = data[index];
				var json = item['ItemAttributes'];
				result[index]= json;
			}
			return result;
			
		}
		
		
		 $(function(){
			 $("#oaForm").render({success:function(result){
					var json=result['OaDao.loadMyDoXml'].data;
					var items = json['Items'];
					if(items){
						var item= items['Item'];
						var count= items['TotalResults'];
						if(count==0){
							layer.msg("暂无数据",function(){
								popup.closeTab();
							});
							return;
						}
						$("#oaForm").show();
						$("#count").html(count);
						$("#oaForm").initTable({
							id:"oaTable",
							page:false,
							limit:'1000',
							limits:[1000],
							data:getNewData(item),
							cols: [[
				             {
								title: '编号',
								type:'numbers'
							 },{
							    field: 'WF_DocNumber',
								title: '单号',
								align:'left'
							},{
							    field: 'Subject',
								title: '标题',
								align:'left',
								width:200
							},{
							    field: 'WF_CurrentNodeName',
								title: '当前状态',
								align:'left'
							},{
							    field: 'WF_Author_CN',
								title: '当前处理人',
								align:'left'
							},{
							    field: 'WF_DocCreated',
								title: '申请时间',
								align:'left'
							},{
							    field: 'WF_AddName_CN',
								title: '申请人',
								align:'center'
							},{
							    field: 'WF_ProcessName',
								title: '流程名称',
								align:'left'
							},{
							    field: 'TotalTime',
								title: '耗时',
								align:'left'
							}
							]]}
						);
						
					}else{
						layer.msg("暂无数据",function(){
							popup.closeTab();
						});
						return;
					}
				}});
		})
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>