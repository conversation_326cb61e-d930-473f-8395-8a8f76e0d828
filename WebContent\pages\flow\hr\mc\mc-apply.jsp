<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>试用期月度考核</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}试用期月度考核"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">考核人</td>
					  			<td>
					  				<input name="apply.data5" type="hidden"/>
					  				<input type="text" data-rules="required" readonly="readonly" data-ref-joindate="#joindate" data-ref-dept="#deptName" onclick="singleUser(this)" class="form-control input-sm" name="apply.data1"/>
					  			</td>
					  			<td class="required">所属部门</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" id="deptName" onclick="singleDept(this)" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">入职日期</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date',max:0}" id="joindate" class="form-control input-sm Wdate" name="apply.data3"/>
					  			</td>
					  			<td class="required">考核月度</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'month',max:0}" class="form-control input-sm Wdate" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">自我总结</td>
					  			<td colspan="3">
									<textarea style="height: 150px;" placeholder="200字以上"  data-edit-node="person" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr class="hidden-print">
					  			<td>考核说明</td>
					  			<td colspan="3">
					  				当月15号之前入职的，当月开始考核；当月15号（含15号）之后的，次月开始考核。<br>
									<b>努力就有收获，拼搏吧！云趣与你共同奋进！</b>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({title:'{{:data2}}{{:data1}}{{:data4}}试用期月度考核',hideUserTitle:true,success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			Flow.initData();
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				
			}});
		}
		
		Flow.approverLayerBefore = function(){
			var type = $("[name='apply.applyRemark']").val();
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='person'){
				if(type==''){
					layer.msg('请填写总结');
					return false;
				}
				if(type.length<30){
					layer.msg('总结字数太少,至少30个字');
					return false;
				}
			}
			return true;
	  }
		
		Flow.initData = function(){
			var selectUserName = $("[name='apply.data1']").val();
			var selectUserId = $("[name='apply.data5']").val();
			var params = {selectUserId:selectUserId,selectUserName:selectUserName};
			FlowCore.approveData = params;
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>