<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>业务平台</title>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="easyform" data-mars="BusinessPlatformDao.getPlatform" method="post" autocomplete="off" data-mars-prefix="platform.">
        <input name="platformId" type="hidden" value="${param.platformId}">
        <input name="platform.PLATFORM_ID" type="hidden" value="${param.platformId}">
        <input type="hidden" name="platform.PLATFORM_TYPE_ID" value="${param.platformTypeId}">
        <table class="table table-edit table-vzebra" style="margin-top: 10px;">
            <tbody>
                <tr>
                    <td class="required">所属类型</td>
                    <td><input type="text" value="${param.platformTypeName}" data-rules="required"  readonly class="form-control input-sm"></td>
                </tr>
                <tr>
                    <td class="required">名称</td>
                    <td><input type="text" name="platform.PLATFORM_NAME" data-rules="required" onchange="getPinyinVal(this.value)" class="form-control input-sm"></td>
                </tr>
                <tr>
                    <td style="width: 80px;" class="required">编码</td>
                    <td><input type="text" name="platform.PLATFORM_CODE" data-rules="required" class="form-control input-sm"></td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td>
                        <textarea name="platform.REMARK" class="form-control input-sm" rows="3" style="resize:none;"></textarea>
                    </td>
                </tr>
                <tr>
                    <td>状态</td>
                    <td>
                        <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                            <input type="radio" checked value="0" name="platform.STATUS"><span>启用</span>
                        </label>
                        <label class="radio radio-info radio-inline">
                            <input type="radio" value="1" name="platform.STATUS"><span>停用</span>
                        </label>
                    </td>
                </tr>
                <tr>
                    <td class="required">排序</td>
                    <td><input type="number" name="platform.IDX_ORDER" data-rules="required|number" value="99" class="form-control input-sm"></td>
                </tr>
            </tbody>
        </table>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button" onclick="PlatformEdit.ajaxSubmitForm()">保存</button>
            <button class="btn btn-sm btn-default ml-20" type="button" onclick="layer.closeAll();">关闭</button>
        </div>
    </form>     
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript" src="${ctxPath}/static/js/Convert_Pinyin.js"></script>
<script type="text/javascript">
    jQuery.namespace("PlatformEdit");
    
    PlatformEdit.platformId='${param.platformId}';
    
    $(function(){
        $("#easyform").render({data:{platformTypeId:'${param.platformTypeId}',platformId:'${param.platformId}'}});
    });
    
    PlatformEdit.ajaxSubmitForm = function(){
        if(form.validate("easyform")){
            if(PlatformEdit.platformId==''){
                PlatformEdit.insertData(); 
            }else{
                PlatformEdit.updateData(); 
            }
        };
    }
    
    PlatformEdit.insertData = function() {
        var data = form.getJSONObject("easyform");
        ajax.remoteCall("${ctxPath}/servlet/business-platform?action=addPlatform",data,function(result) { 
            if(result.state == 1){
                layer.msg(result.msg,{icon: 1,offset:'100px',time:1600},function(){
                    layer.closeAll();
                    Platform.searchData();
                });
            }else{
                layer.alert(result.msg,{icon: 5});
            }
        });
    }
    
    PlatformEdit.updateData = function(){
        var data = form.getJSONObject("easyform");
        ajax.remoteCall("${ctxPath}/servlet/business-platform?action=modPlatform",data,function(result) { 
            if(result.state == 1){
                layer.msg(result.msg,{icon: 1,offset:'100px',time:1600},function(){
                    layer.closeAll();
                    Platform.searchData();
                });
            } else {
                layer.alert(result.msg,{icon: 5});
            }
        });
    }

    function getPinyinVal(val){
        if($("[name='platform.PLATFORM_CODE']").val() ==''){
            $("[name='platform.PLATFORM_CODE']").val(pinyin.getFullChars(val));
        }
    }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
