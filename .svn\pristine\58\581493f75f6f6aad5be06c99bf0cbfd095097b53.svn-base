package com.yunqu.work.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.AppModel;
import com.yunqu.work.model.VersionModel;
/**
 * 版本管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/version/*")
public class VersionServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		VersionModel model=getModel(VersionModel.class, "version");
		model.addCreateTime();
		model.setCreator(getUserPrincipal().getUserId());
		try {
			String appId=model.getString("APP_ID");
			AppModel appModel=new AppModel();
			appModel.setPrimaryValues(appId);
			appModel.set("last_version_name",model.getString("VERSION_NAME"));
			appModel.set("last_version_time", EasyDate.getCurrentDateString());
			appModel.set("last_publisher", getUserPrincipal().getUserId());
			appModel.update();
			
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		VersionModel model=getModel(VersionModel.class, "version");
		try {
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		VersionModel model=getModel(VersionModel.class, "version");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}





