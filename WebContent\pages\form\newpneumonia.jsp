<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>心肺</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm"  autocomplete="off">
						<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td class="required">日期</td>
				                    <td><input data-rules="required"  data-mars="CommonDao.date03" id="date" type="text" name="form.DATE_ID" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td style="width: 80px" class="required">体温</td>
				                    <td><input data-rules="required"  type="number" name="form.BODY_WD" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td>不适症状</td>
				                    <td>
				                    	<input type="checkbox" value="无" name="BODY_PROBLEM" checked="checked">无 &nbsp;
				                    	<input type="checkbox" value="发热" name="BODY_PROBLEM">发热 &nbsp;
				                    	<input type="checkbox" value="干咳" name="BODY_PROBLEM">干咳 &nbsp;
				                    	<input type="checkbox" value="乏力" name="BODY_PROBLEM">乏力 &nbsp;
				                    	<input type="text" name="BODY_PROBLEM" style="display: inline-block;width: 120px;"  class="form-control input-sm" placeholder="其他"/>
				                    </td>
					            </tr>
					            <tr>
					            	<td>上班地点</td>
					            	<td>
					            		<select class="form-control input-sm" name="WORK_REAMRK" style="display: inline-block;width: 120px;">
					            			<option value="在公司上班">在公司上班</option>
					            			<option value="在家办公">在家办公</option>
					            			<option value="没上班,请假">没上班,请假</option>
					            			<option value="今日是周末">今日是周末</option>
					            			<option value="今日是法定假期">今日是法定假期</option>
					            			<option value="">其他</option>
					            		</select>
				                    	<input type="text" name="WORK_REAMRK_OTHER" style="display: inline-block;width: 120px;"  class="form-control input-sm" placeholder="其他选项"/>
					            	</td>
					            
					            </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c">
						    	  <button type="button" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 提交 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("Edit");
		
		
		layui.use('laydate', function(){
		  var laydate = layui.laydate;
		  laydate.render({
		    elem: '#date',
		    type:'date',
		    format:'yyyyMMdd'
		  });
		});
		
		$(function(){
			var device = layui.device();
			if(device.ios){
				$("#editForm .layer-foot").css('position','inherit');
			}
			$("#editForm").render({success:function(){
				
			}});  
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				Edit.insertData(); 
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			var bodyProblem = data['BODY_PROBLEM'];
			var workReamrk = data['WORK_REAMRK'];
			var workReamrkOther = data['WORK_REAMRK_OTHER'];
			if(workReamrk==''&&workReamrkOther==''){
				layer.msg('请输入[其它]具体情况');
				return;
			}
			if(workReamrkOther){
				data['form.WORK_REAMRK']=workReamrkOther;
			}else{
				data['form.WORK_REAMRK']=workReamrk;
			}
			data['form.BODY_PROBLEM']=bodyProblem.join(',');
			ajax.remoteCall("${ctxPath}/servlet/form?action=addXf",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						localStorage.setItem($("#date").val(),'1');
						$(".jkCard").render();
						layer.closeAll();
					});
				}else{
					layer.alert('您选择日期的表单已填写',{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>