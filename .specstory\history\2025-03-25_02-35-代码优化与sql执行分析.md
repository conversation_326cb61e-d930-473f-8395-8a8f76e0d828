<!-- Generated by SpecStory -->

# 代码优化与SQL执行分析 (2025-03-25 10:35:47)

_**User**_

分析下代码哪里可优化的，sql执行是否有重复可以避免的，方法是否可以公共化的

---

_**Assistant**_

```thinking
让我仔细分析一下这个TaskServlet的代码...

首先看整体结构:
1. 这是一个处理任务相关操作的Servlet,包含了很多任务管理的功能点
2. 代码量较大,有1000多行,方法较多且复杂度高
3. 有很多重复的SQL执行和相似的业务逻辑

让我逐个分析可优化的点:

SQL优化方面:
1. updateTaskFinishPlanData方法中有多次重复查询plan相关数据
2. actionForUpdateState中对group_id相关的查询和更新可以合并
3. 很多地方都在查询task基本信息,可以抽取复用

方法重构方面:
1. 消息通知相关的逻辑(邮件、微信等)在多个方法中重复出现
2. 评论添加的逻辑在多处重复
3. 任务状态更新的逻辑可以抽取
4. 文件处理相关的逻辑可以独立

具体优化建议:
1. 抽取通用的消息发送方法
2. 将评论、附件等相关操作抽到单独的Service
3. 优化SQL,减少重复查询
4. 拆分大方法,提高代码可读性
5. 增加事务控制

让我详细说明下具体优化方案...
```

以下是具体的优化建议：

1. SQL优化

```java
// 优化前 - updateTaskFinishPlanData方法中的重复查询
JSONObject plan = this.getQuery().queryForRow("SELECT t1.p_plan_id,COUNT(*) AS total_tasks...");
// 后续又查询
String maxFinishDateId = this.getQuery().queryForString("select max(FINISH_DATE_ID)...");

// 优化后 - 合并查询
String sql = "SELECT t1.p_plan_id, COUNT(*) AS total_tasks, " +
            "MAX(t2.FINISH_DATE_ID) as max_finish_date_id, " +
            "SUM(CASE WHEN t2.task_state in(10,12,20) THEN 1 ELSE 0 END) AS unfinished_tasks " +
            "FROM yq_project_plan t1 INNER JOIN yq_task t2 ON t1.plan_id = t2.group_id " + 
            "WHERE t1.plan_id = ?";
```

2. 抽取消息通知公共方法

```java
// 新增 NotificationService
public class NotificationService {
    
    public void sendTaskNotification(MessageModel message) {
        try {
            // 统一处理消息发送
            MessageService.getService().sendMsg(message);
            
            if(message.getNoticeTypes().contains("3")) {
                EmailService.getService().sendTaskEmail(message); 
            }
            
            if(message.getNoticeTypes().contains("4")) {
                WxMsgService.getService().sendTaskMsg(message);
            }
            
            WebSocketUtils.sendMessage(new MsgModel(message.getReceiver(), 
                message.getTitle(), message.getContents()));
        } catch(Exception e) {
            logger.error("发送通知失败", e);
        }
    }
}
```

3. 抽取评论相关操作

```java
// 新增 CommentHelper
public class CommentHelper {
    
    public static void addTaskComment(String userId, String taskId, 
            String content, HttpServletRequest request) {
        CommentModel comment = new CommentModel();
        comment.setFkId(taskId);
        comment.setPrimaryValues(RandomKit.uuid());
        comment.setContent(content);
        comment.setCreator(userId); 
        comment.setCreateTime(EasyDate.getCurrentDateString());
        comment.setUserAgent(request.getHeader("user-agent"));
        comment.setIp(WebKit.getIP(request));
        
        CommentService.getService().addComment(comment);
    }
}
```

4. 任务状态更新逻辑优化

```java
// 新增 TaskStateManager
public class TaskStateManager {
    
    public void updateTaskState(TaskModel task, int newState) {
        // 状态变更前检查
        validateStateChange(task.getTaskState(), newState);
        
        // 更新状态
        task.setTaskState(newState);
        task.setUpdateTime(EasyDate.getCurrentDateString());
        
        // 特殊状态处理
        if(newState == 30) {
            handleTaskFinished(task);
        } else if(newState == 40) {
            handleTaskChecked(task); 
        }
        
        // 保存更新
        task.update();
        
        // 发送通知
        sendStateChangeNotification(task, newState);
    }
}
```

5. 事务控制优化

```java
// 新增事务注解
@Transactional
public EasyResult actionForUpdateState() {
    try {
        // 现有业务逻辑
        return EasyResult.ok();
    } catch(Exception e) {
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        return EasyResult.fail(e.getMessage());
    }
}
```

6. 常量定义优化

```java
// 新增TaskConstants
public interface TaskConstants {
    
    // 任务状态
    int TASK_STATE_TODO = 10;
    int TASK_STATE_RETURNED = 11; 
    int TASK_STATE_PAUSED = 12;
    int TASK_STATE_CANCELLED = 13;
    int TASK_STATE_DOING = 20;
    int TASK_STATE_FINISHED = 30;
    int TASK_STATE_CHECKED = 40;
    int TASK_STATE_CHECK_FAILED = 42;
    
    // 通知类型
    String NOTICE_TYPE_EMAIL = "3";
    String NOTICE_TYPE_WECHAT = "4";
}
```

7. 文件处理优化

```java
// 新增 FileService
public class FileService {

    public void handleTaskFiles(String taskId) {
        // 更新文件计数
        String sql = "update yq_task t1 set t1.file_count = " +
                    "(select count(1) from yq_files t2 where t2.FK_ID=t1.task_id) " +
                    "where t1.task_id = ?";
        this.getQuery().executeUpdate(sql, taskId);
    }
    
    public void deleteTaskFiles(String taskId) {
        this.getQuery().executeUpdate("delete from yq_files where fk_id = ?", taskId);
    }
}
```

主要优化建议:

1. SQL优化:
- 合并重复查询
- 使用批量更新替代循环单条更新
- 添加合适的索引
- 优化JOIN查询

2. 代码结构优化:
- 抽取公共方法
- 拆分大类为多个小类
- 使用设计模式重构
- 增加异常处理
- 添加日志记录

3. 性能优化:
- 使用缓存减少数据库访问
- 异步处理通知发送
- 批量处理替代循环处理
- 添加超时控制

4. 可维护性优化:
- 添加注释说明
- 统一错误码
- 规范命名
- 抽取配置项

这些优化可以:
1. 提高代码复用性
2. 减少数据库访问
3. 提升系统性能
4. 增强可维护性
5. 降低出错风险

建议分步实施优化,每次修改后进行充分测试。

---

_**User**_

把上述的回答调用mcp保存到印象笔记

---

_**Assistant**_

```thinking
我需要把之前的代码优化建议保存到印象笔记中。

我需要:
1. 整理内容成合适的格式
2. 设置合适的标题
3. 使用evernote工具保存

让我组织一下内容...
```

---



---

