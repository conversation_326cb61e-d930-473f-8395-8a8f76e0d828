package com.yunqu.work.servlet.tools;

import java.io.File;
import java.io.IOException;

import com.jfinal.core.Controller;
import com.jfinal.core.Path;
import com.jfinal.upload.UploadFile;
import com.yunqu.work.utils.Invoice;
import com.yunqu.work.utils.InvoiceExtractor;

@Path("/invoice")
public class InvoiceController extends Controller{
	
	public void index() {
		renderJsp("/pages/doc/invoice.jsp");
	}
	
	public void extrat() throws IOException {
		UploadFile file = getFile();
		File f = file.getFile();
		Invoice extract = InvoiceExtractor.extract(f);
		f.deleteOnExit();
		renderJson(extract);
	}

}
