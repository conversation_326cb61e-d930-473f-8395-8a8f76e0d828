package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.MessageModel;

public class TaskNoticeService extends AppBaseService implements Job{

	private static class Holder{
		private static TaskNoticeService service=new TaskNoticeService();
	}
	public static TaskNoticeService getService(){
		return Holder.service;
	}
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		this.run();
	}
	public  void run(){
		WeeklyStatService.getService().run();
		
		String current=EasyDate.getCurrentDateString();
		//待办提醒
		getLogger().info("执行待办提醒任务....");
		
		
		try {
			this.getQuery().executeUpdate("update yq_project t1 set t1.task_count = (select count(1) from yq_task t3 where t3.PROJECT_ID= t1.PROJECT_ID)");
			
			List<JSONObject> list = this.getQuery().queryForList("select * from yq_task where task_state = 10 ",null,new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("CREATOR");
					String assignUserId=jsonObject.getString("ASSIGN_USER_ID");
					String planStartedAt=jsonObject.getString("PLAN_STARTED_AT");
					String taskName=jsonObject.getString("TASK_NAME");
					MessageModel model=new MessageModel();
					model.setFkId(jsonObject.getString("TASK_ID"));
					model.setReceiver(assignUserId);
					model.setSender(creator);
					model.setTitle("【云趣协同】"+"待办任务|"+taskName);
					model.setDesc(jsonObject.getString("TASK_DESC"));
					jsonObject.put("NOTICE_NAME","您有任务未处理,请尽快处理");
					model.setData(jsonObject);
					model.setTplName("noticeTask.html");
					if(current.compareTo(planStartedAt)>=0){
						WxMsgService.getService().sendNewTaskMsg(model);
						try {
							EmailService.getService().sendTaskEmail(model);
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}
			}
		} catch (SQLException e2) {
			e2.printStackTrace();
			this.getLogger().error(e2);
		}
		
		//超时未完成提醒
		try {
			List<JSONObject> list=this.getQuery().queryForList("select * from yq_task where task_state = 20 and DEADLINE_AT <= ? ",new Object[]{current},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("CREATOR");
					String assignUserId=jsonObject.getString("ASSIGN_USER_ID");
					String taskName=jsonObject.getString("TASK_NAME");
					MessageModel model=new MessageModel();
					model.setFkId(jsonObject.getString("TASK_ID"));
					model.setReceiver(assignUserId);
					model.setSender(creator);
					model.setTitle("【云趣协同】"+"任务超时|"+taskName);
					model.setDesc(jsonObject.getString("TASK_DESC"));
					jsonObject.put("NOTICE_NAME","任务已超时,请尽快处理");
					model.setData(jsonObject);
					model.setTplName("noticeTask.html");
					WxMsgService.getService().sendUnFinshTaskMsg(model);
					try {
						EmailService.getService().sendTaskEmail(model);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		} catch (SQLException e1) {
			e1.printStackTrace();
			this.getLogger().error(e1);
		}
		//未验收提醒
		try {
			List<JSONObject> list=this.getQuery().queryForList("select * from yq_task where task_state = 30 ",new Object[]{},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("CREATOR");
					String assignUserId=jsonObject.getString("ASSIGN_USER_ID");
					String taskName=jsonObject.getString("TASK_NAME");
					MessageModel model=new MessageModel();
					model.setFkId(jsonObject.getString("TASK_ID"));
					model.setReceiver(creator);
					model.setSender(assignUserId);
					model.setTitle("【云趣协同】"+"未验收|"+taskName);
					model.setDesc(jsonObject.getString("TASK_DESC"));
					jsonObject.put("NOTICE_NAME","请及时验收任务");
					model.setData(jsonObject);
					model.setTplName("noticeTask.html");
					WxMsgService.getService().sendCheckTaskMsg(model);
					try {
						EmailService.getService().sendTaskEmail(model);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		} catch (SQLException e) {
			e.printStackTrace();
			this.getLogger().error(e);
		}
	
	}

}
