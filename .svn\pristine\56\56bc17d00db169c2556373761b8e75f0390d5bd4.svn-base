package com.yunqu.work.servlet.crm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.ContractService;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;

@WebServlet("/servlet/incomeConfirm/*")
public class IncomeConfirmServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public EasyRecord getConfirmModel(String prefix) {
        EasyRecord confirmModel = new EasyRecord("yq_contract_income_confirm", "CONFIRM_ID");
        confirmModel.setColumns(getJSONObject(prefix));
        return confirmModel;
    }

    public EasyResult actionForAdd() {
        EasyRecord confirmModel = getConfirmModel("confirm");
        confirmModel.setPrimaryValues(RandomKit.uuid().toUpperCase());
        confirmModel.set("CREATE_TIME", EasyDate.getCurrentDateString());
        confirmModel.set("CREATOR", getUserName());
        confirmModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        confirmModel.set("UPDATE_BY", getUserName());
        String contractId = confirmModel.getString("CONTRACT_ID");
        String confirmType = confirmModel.getString("CONFIRM_TYPE");
        String stageId = confirmModel.getString("INCOME_STAGE_ID");
        try {
            this.getQuery().save(confirmModel);
            if("A".equals(confirmType)){
                ContractService.getService().addCount("INCOME_CONFIRM_COUNT",contractId);
            }else if("B".equals(confirmType)){
                ContractService.getService().addCount("INCOME_CONFIRM_COUNT_B",contractId);
            }
            setConfirmInfoForIncomeStage(stageId,confirmType);
        } catch (SQLException e) {
            this.error(null, e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdate() {
        EasyRecord confirmModel = getConfirmModel("confirm");
        confirmModel.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        confirmModel.set("UPDATE_BY", getUserName());
        String confirmType = confirmModel.getString("CONFIRM_TYPE");
        String stageId = confirmModel.getString("INCOME_STAGE_ID");

        try {
            this.getQuery().update(confirmModel);
        } catch (SQLException e) {
            this.error(null, e);
            return EasyResult.fail(e.getMessage());
        }
        setConfirmInfoForIncomeStage(stageId,confirmType);
        return EasyResult.ok();
    }

    public EasyResult actionForBatchDel() {
        JSONArray confirmArray = getJSONArray();
        for (int i = 0; i < confirmArray.size(); i++) {
            JSONObject confirmObject = confirmArray.getJSONObject(i);
            String stageId = confirmObject.getString("INCOME_STAGE_ID");
            String contractId = confirmObject.getString("CONTRACT_ID");
            String confirmType = confirmObject.getString("CONFIRM_TYPE");
            try {
                this.getQuery().executeUpdate("delete from yq_contract_income_confirm where CONFIRM_ID = ?", confirmObject.getString("CONFIRM_ID"));
                if("A".equals(confirmType)){
                    ContractService.getService().subCount("INCOME_CONFIRM_COUNT",contractId);
                }else if("B".equals(confirmType)){
                    ContractService.getService().subCount("INCOME_CONFIRM_COUNT_B",contractId);
                }
                setConfirmInfoForIncomeStage(stageId,confirmType);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }

    public void setConfirmInfoForIncomeStage(String incomeStageId , String confirmType ){
        if(StringUtils.isBlank(incomeStageId)||StringUtils.isBlank(confirmType)){
            this.error("incomeStageId 或 confirmType 为空，导致收入确认阶段未更新金额！", null);
            return;
        }

        String amount = "0";
        try {
             amount = this.getQuery().queryForString("select IFNULL(sum(AMOUNT_WITH_TAX),0) from yq_contract_income_confirm where INCOME_STAGE_ID = ? and CONFIRM_TYPE = ?",new Object[]{incomeStageId,confirmType});
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
        }

        EasySQL sql = new EasySQL("UPDATE yq_contract_income_stage ");
        if("A".equals(confirmType)) {
            sql.append(" SET UNCONFIRM_AMOUNT_A = ( PLAN_AMOUNT");
        }else if("B".equals(confirmType)){
            sql.append(" SET UNCONFIRM_AMOUNT_B = ( PLAN_AMOUNT");
        }
        sql.append(amount, "- ? )");
        sql.append(incomeStageId, "WHERE INCOME_STAGE_ID = ?");
        try {
            this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
            if("A".equals(confirmType)) {
                setCompleteTimeForContractStage(incomeStageId);
            }
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
        }
    }

    /*
    * 当收入确认A的金额到达收入确认阶段的计划金额
    * 给合同阶段加上完成日期（以最晚的收入确认A日期为完成日期）
     */
    public void setCompleteTimeForContractStage(String incomeStageId){
        //首先检查unConfirm是否为0
        try {
            EasyRow row = this.getQuery().queryForRow("select * from yq_contract_income_stage where INCOME_STAGE_ID = ? ",new Object[]{incomeStageId});
            String amount = row.getColumnValue("UNCONFIRM_AMOUNT_A");
            String contractStageIds = row.getColumnValue("CONTRACT_STAGE_ID");
            if("0.00".equals(amount)){
                String confirmDate = this.getQuery().queryForString("select CONFIRM_DATE from yq_contract_income_confirm where INCOME_STAGE_ID = ? and CONFIRM_TYPE = ? ORDER BY CONFIRM_DATE DESC LIMIT 1",new Object[]{incomeStageId,"A"});
                setCompleteDateForContractStage(contractStageIds,confirmDate);
            }else {
                removeCompleteDateForContractStage(contractStageIds);
            }

        } catch (SQLException e) {
            this.error(e.getMessage(), e);
        }
    }

    public void removeCompleteDateForContractStage(String contractStageIds){
        String[] contractStages = contractStageIds.split(",");
        for (String contractStageId : contractStages) {
            String sql = "UPDATE yq_contract_stage SET ACT_COMP_DATE = '' WHERE STAGE_ID = ?";
            try {
                this.getQuery().executeUpdate(sql,contractStageId);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
            }
        }
    }

    public void setCompleteDateForContractStage(String contractStageIds,String confirmDate){
        String []contractStages = contractStageIds.split(",");
        for (String contractStageId:contractStages) {
            String sql = "UPDATE yq_contract_stage SET ACT_COMP_DATE = ? WHERE STAGE_ID = ?";
            try {
                this.getQuery().executeUpdate(sql,new Object[]{confirmDate,contractStageId});
            }catch (SQLException e){
                this.error(e.getMessage(),e);
            }
        }
    }

}
