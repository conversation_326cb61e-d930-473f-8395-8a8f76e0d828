<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>计划查询</title>
	<style>
		.layui-table-tool{background-color: #ffffff;}
		.layui-table-cell {
	        line-height: 20px !important;
	        vertical-align: middle;
	        height: auto;
	        padding: 6px 6px;
	        overflow: visible;
	        text-overflow: inherit;
	        white-space: normal;
	    }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
  <form  autocomplete="off" onsubmit="return false;" class="form-inline" id="searchForm">
    <input type="hidden" name="source" id="source" value="${param.source}"/>
    <input type="hidden" name="planState" id="planState" value=""/>
  	<div class="ibox-content">
		<div class="layui-tab layui-tab-brief task-tab" lay-filter="followTab" style="padding-top: 10px;">
			    <ul class="layui-tab-title">
			        <li data-state="" class="layui-this">全部 </li>
			        <li data-state="1">待开始</li>
			        <li data-state="5">进行中</li>
			        <li data-state="9">已完成</li>
			    </ul>
			    <div class="layui-tab-content" style="padding: 0px;">
			        <div class="layui-tab-item layui-show"></div>
			        <div class="layui-tab-item"></div>
			        <div class="layui-tab-item"></div>
			        <div class="layui-tab-item"></div>
			    </div>
			    <div class="form-group mt-10">
			    		<c:choose>
			    			<c:when test="${empty param.projectId}">
						      <div class="input-group input-group-sm"  style="width: 180px">
								 <span class="input-group-addon">项目</span>	
								 <input type="hidden" name="projectId" class="form-control input-sm">
								 <input type="text" id="projectId" readonly="readonly" onclick="singleProject(this);" class="form-control input-sm">
						      </div>
			    			</c:when>
			    			<c:otherwise>
								 <input type="hidden" name="projectId" value="${param.projectId}" class="form-control input-sm">
			    			</c:otherwise>
			    	  </c:choose>
				      <c:if test="${param.source=='all'}">
				    	  <div class="input-group input-group-sm ml-5"  style="width: 150px">
							 <span class="input-group-addon">负责人</span>	
							 <input type="text" name="personName" class="form-control input-sm">
					     </div>
				      </c:if>
			    	  <div class="input-group input-group-sm"  style="width: 150px">
						 <span class="input-group-addon">计划名称</span>	
						 <input type="text" name="planName" class="form-control input-sm">
				     </div>
				     <div class="input-group input-group-sm"  style="width: 280px">
							 <span class="input-group-addon">计划开始日期</span>	
							 <input style="width: 90px;display: inline-block" data-mars="CommonDao.monthBegin" type="text" name="planBegin1Date" data-laydate="{type:'date'}" class="form-control input-sm">
							 <input style="width: 90px;display: inline-block" type="text" name="planBegin2Date" data-laydate="{type:'date'}" class="form-control input-sm">
				      </div>
				      <div class="input-group input-group-sm" style="width: 280px">
						 <span class="input-group-addon">计划完成日期</span>	
						 <input style="width: 90px;display: inline-block" type="text" name="planEnd1Date" data-laydate="{type:'date'}" class="form-control input-sm">
						 <input style="width: 90px;display: inline-block" type="text" name="planEnd2Date" data-laydate="{type:'date'}" class="form-control input-sm">
				     </div>
				     <div class="input-group input-group-sm">
						 <button type="button" class="btn btn-sm btn-default" onclick="reloadRisk()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					</div>
			    </div>
		 </div>
		 <table class="layui-hide" id="followList"></table>
  	  </div>
	</form>
	<script  type="text/html" id="planToolBar">

	</script>
</EasyTag:override>
<EasyTag:override name="script">
 <script type="text/javascript">

 	var projectId = '${param.projectId}';
    
    function loadPlanTable(){
    	var hideProject = false;
    	if(projectId){
    		hideProject = true;
    	}
    	
        $("#searchForm").initTable({
            mars:'ProjectPlanDao.planListQuery',
            id:"followList",
            height:'full-110',
            limit:30,
            toolbar:'#planToolBar',
            data:{},
            cols: [[
                {title:'序号',type:'numbers'},
                {
                    title: '操作',
                    align:'center',
                    width:80,
                    templet:function(row){
                        return '<a class="btn btn-xs btn-link" href="javascript:;" lay-event="planDetail">详情</a>';
                    }
                },{
                	title:'项目名称',
                	field:'PROJECT_NAME',
                	event:'planDetail',
                	hide:hideProject,
                	minWidth:280
                },{
                    field: 'PLAN_STATE',
                    title: '计划状态',
                    align:'center',
                    width:80,
                    templet:function(row){
                        return planStateLabel(row.PLAN_STATE);
                    }
                },{
                    field:'PLAN_NAME',
                    title:'计划名称',
                    width:110,
                    templet:function(row){
                    	if(row.P_PLAN_NAME){
                    		return row.P_PLAN_NAME+'/'+row.PLAN_NAME;
                    	}
                    	return row.PLAN_NAME;
                    }
                },{
                    field: 'PLAN_PERSON_NAME',
                    title: '负责人',
                    width:70,
                    align:'center'
                },{
                    field: 'CREATOR',
                    title: '创建人',
                    width:70,
                    align:'center'
                },{
                    field: 'PLAN_BEGIN_DATE',
                    title: '开始日期',
                    align:'center',
                    sort:true,
                    width:140,
                    templet:function(row){
                    	if(row.BEGIN_DATE){
                    		return row.BEGIN_DATE;
                    	}
                    	return row.PLAN_BEGIN_DATE;
                    }
                },{
                    field: 'PLAN_FINISH_DATE',
                    title: '结束日期',
                    align:'center',
                    sort:true,
                    width:140,
 					templet:function(row){
 						if(row.FINISH_DATE){
 							return row.FINISH_DATE;
                    	}
                    	return row.PLAN_FINISH_DATE;
                    }
                },{
                    field: 'UPDATE_TIME',
                    title: '更新时间',
                    sort:true,
                    align:'center',
                    width:140
                },{
                    field: 'PLAN_REMARK',
                    title: '备注',
                    align:'left',
                    width:100,
                    templet:function(row){
                    	return cutText(row.PLAN_REMARK,80);
                    }
                }
            ]],done:function(result){
            	
         }});
    }

    function  filterElement(){
       layui.element.on('tab(followTab)', function(elem){
           var state = $(this).data('state');
           $("#planState").val(state);
           reloadRisk();
       });
    }

    function reloadRisk(){
        $("#searchForm").queryData({id:'followList'});
    }

    function planStateLabel(state){
        var json = {0:'<label class="label label-danger label-outline">已删除</label>',1:'<label class="label label-warning label-outline">待开始</label>',5:'<label class="label label-info label-outline">进行中</label>',9:'<label class="label label-success">已完成</label>'};
        var result=json[state]||'';
        if(result){
            return result;
        }else{
            return '';
        }
    }

    function  planDetail(data){
       window.open("/yq-work/project/"+data.PROJECT_ID+'#计划')
    }
    
    
    $(function(){
    	$("#searchForm").render({success:function(){
	    	loadPlanTable();
	    	filterElement();
    	}});
    });

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
