﻿<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>2024年度报告</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"/>
    <!-- Link Swiper's CSS -->
    <link rel="stylesheet" href="https://lib.baomitu.com/Swiper/8.4.5/swiper-bundle.min.css"/>
    <link rel="stylesheet" href="/yq-work/pages/my/2022/css/animate.min.css"/>

    <!-- Demo styles -->
    <style>
      html,
      body {
        position: relative;
        height: 100%;
      }

      body {
        background: #eee;
        font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
        font-size: 14px;
        color: #000;
        margin: 0;
        padding: 0;
      }

      .swiper {
        width: 100%;
        height: 100%;
      }

      .swiper-slide {
        text-align: left;
        font-size: 16px;
        background: #fff;
        color:#fff;
        letter-spacing:2px;
        line-height:30px;

        /* Center slide text vertically */
        display: -webkit-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
        background-image: linear-gradient(to right, #f78ca0 0%, #f9748f 19%, #fd868c 60%, #fe9a8b 100%);
      }
	  
	  .swiper-slide div{
		 margin: 40px;
	  }
      .swiper-slide img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .arrow-box {
	    position: absolute;
	    bottom: -45px;
	    left: 0;
	    right: 0;
	    margin: 0 auto;
	    width: 50%;
	    height: 90px;
	    border-radius: 100%;
	    background: rgba(255,255,255,.18);
	    z-index: 900;
	  }
	  #array {
	    z-index: 999;
	    -webkit-animation: start 1.5s infinite ease-in-out;
	    display: block;
	    margin: 15px auto 0 auto;
	    width: 20px;
	    height: 15px;
	    z-index: 999;
	}

      .swiper-slide li{list-style: none;text-align: left;}
      .slide1{
        background-image: linear-gradient(45deg, #ff9a9e 0%, #fad0c4 99%, #fad0c4 100%);
      	color:#ffffff;
      }
      .slide2{
      	color:#ffffff;
        background: -webkit-linear-gradient(top,#7ea5dc 0,#98bbd2 47%,#aecec8 68%,#dbe8ac 100%);
      }
      .slide99{
        background-image: linear-gradient(to right, #f78ca0 0%, #f9748f 19%, #fd868c 60%, #fe9a8b 100%);
      }
      
      .slide1 p{display: block;}
      .slide1{font-size: 13px;letter-spacing:2px;}
      
      #applyList,#taskTopList{margin: 10px 0px;}
      
      
    /* 样式可以根据需要进行调整 */
    #audioControl {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 9999;
      cursor: pointer;
      width:2em;
      height:2em;
      transition: transform 0.3s ease; /* 添加过渡效果 */
    }

    /* 添加旋转动画 */
    #audioControl.rotating {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
    </style>
  </head>

  <body>
  
   <audio id="backgroundMusic" autoplay loop>
    <source src="/xd.mp3" type="audio/mp3">
  </audio>
  <img id="audioControl" src="/yq-work/static/images/play-2.png" alt="音乐控制图标">
  
    <!-- Swiper -->
    <div class="swiper mySwiper">
      <div class="swiper-wrapper">
        <div class="swiper-slide">
       		<img class="ani" swiper-animate-effect="fadeIn" swiper-animate-duration="2s" swiper-animate-delay="0.01s" src="/yq-work/pages/my/2022/images/start.png"/>
        </div>
        <div class="swiper-slide slide1">
        	<div class="ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="4s" swiper-animate-delay="0.1s">
				&nbsp;&nbsp;我们抬头看到便是星光，闪烁着岁月的痕迹。
	        	星星穿越了万年，而我们在这<span id="year"></span>年中亦历经风雨。
	        	从你于<span id="joinDate"></span>加入<span id="company"></span>，成为第<span id="staffNum"></span>号员工起，已有<span id="joinDay"></span>天,
	        	这段时间充满了挑战、成长和无数个努力与奋斗的瞬间。<br>
	        	
	        	&nbsp;&nbsp;在这份平凡中，我们一同演绎着不平凡。每一天的付出与努力，都是公司成长的细微组成。因为有你的陪伴，才有了这段不平凡的旅程。感谢你在这漫长的路上一直与公司同行。<br>

				&nbsp;&nbsp;让我们携手继续追逐星辰大海，为更美好的未来而努力。愿我们的未来之路，始终充满着激情与信心！

        	</div>
        </div>
        <div class="swiper-slide">
        	<ul class="ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="3s" swiper-animate-delay="0.1s">
	       		<li>每次协作</li>
				<li>都有回忆</li>
				<li>这一年有<span id="VISIT_COUNT"></span>天访问过系统</li>
				<li>一共发起了<span id="applyFlowCount">0</span>次流程</li>
				<li>审批了<span id="approveCount">0</span>次流程</li>
				<li>填写了<span id="weeklyNum">0</span>次周报</li>
				<li>安排了<span id="createTaskNum">0</span>个任务</li>
				<li>处理了<span id="doTaskNum">0</span>个任务</li>
				<li>参与了<span id="projectNum">0</span>个项目</li>
        	</ul>
        </div>
        <div class="swiper-slide slide2">
        	<div class="ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="4s" swiper-animate-delay="0.1s">
        		这一年<br>
				你请假<span id="leave_day"></span>天<br>
				出差<span id="travel_day"></span>天<br>
				加班<span id="overtime_day"></span>天<br>
				漏打卡<span id="kq_yc_day"></span>次<br>
				加班<span id="jbCount"></span>次<br>
				迟到<span id="cdCount"></span>次<br>
				<span id="signDiv"><span id="maxSignOutDate"></span>&nbsp;<span id="maxSignOut"></span>是你最晚一次下班</span><br><br>
				
				在繁忙与挑战交织的岁月里，<br>
				工作如诗，抒写着奋斗的旋律。<br>
				每一次付出都是种子的洒落，<br>
				在勤劳中收获鲜花绚烂。<br>
			</div>
        </div>
        <div class="swiper-slide taskTop-slide">
        	<div class="ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="4s" swiper-animate-delay="0.1s">
        		这一年<br>
				协作任务<span id="allTaskNum"></span>次<br>
				<div id="taskTopList"></div>
			</div>
        </div>
        <div class="swiper-slide trip-slide">
        	<div class="ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="3s" swiper-animate-delay="0.1s">
	        	今年，你出差了<span id="cityCount"></span>个城市<br>
	        	<span id="allDestCity"></span><br>
			          最久的一次<span id="COST_DAY"></span>天出差<br>
				在<span id="START_DATE"></span>，从<span id="START_CITY"></span>到达了<span id="DEST_CITY"></span><br>
				参与<span id="CONTRACT_NAME"></span>项目
        	</div>
        </div>
        <div class="swiper-slide">
        	<div class="ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="4s" swiper-animate-delay="0.1s">
	        	今年，共有<span id="checkUserCount"></span>位同事为你审批过流程<br>
				<span id="checkUserInfo"><span id="MAX_CHECK_NAME"></span>共为你审批了<span id="MAX_CHECK_COUNT"></span>次</span><br><br>
				<span id="lastCheckTime"></span><br>
				一句谢谢，感谢你一路陪我走过。
			</div>
        </div>
        <div class="swiper-slide">
        	<div class="ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="4s" swiper-animate-delay="0.1s">
	        	今年，您总共申请了<span id="FLOW_APPLY_COUNT"></span>个流程，填写花费<span id="sumFillTime"></span>，
				<span id="applyCountInfo">『<span id="APPLY_TITLE"></span>』最快<span id="minCheckTime"></span>审批完成。</span><br><br>
				<div id="applyList"></div>
        	</div>
        </div>
        <div class="swiper-slide slide99">
        	<div class="ani" swiper-animate-effect="fadeInUp" swiper-animate-duration="4s" swiper-animate-delay="0.1s">
        		<span style="font-size: 18px;" id="userName"></span><br>
        		${word}<br>(本内容由AI大模型生成）
        	</div>
        </div>
      </div>
      <div class="arrow-box">
   			<img src="/yq-work/pages/my/2022/images/arrow.png" id="array"> 
	  </div>
      <div class="swiper-pagination"></div>
    </div>

    <script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script> 
	<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
	<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=2020515"></script>
    <!-- Swiper JS -->
    <script src="https://lib.baomitu.com/Swiper/8.4.5/swiper-bundle.min.js"></script>
    <script src="/yq-work/pages/my/2022/js/swiper.animate.min.js"></script>

    <!-- Initialize Swiper -->
    <script type="text/javascript">
      var swiper = new Swiper(".mySwiper", {
        direction: "vertical",
        loop:false,
        speed:500,
        mousewheelControl : true,
        grabCursor:true,
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        on:{
        	init:function(){
       		   swiperAnimateCache(this); //隐藏动画元素 
       	       swiperAnimate(this); //初始化完成开始动画
        	}, 
            slideChangeTransitionEnd: function(){ 
                swiperAnimate(this);
            } 
        }
      });
      
     $(function(){
    	 ajax.daoCall({loading:false,controls:['YearDataDao.getLoginSysInfo'],params:{}},function(rs){
 			var data = rs['YearDataDao.getLoginSysInfo'].data;
 			var joinDay = data['joinDay'];
 			var year = parseInt(joinDay/365);
 			if(year==0){
 				year = 1;
 			}
 			var newData = $.extend({year:year,joinDay:joinDay,company:'云趣科技',maxSignOut:data['maxSignOut'],maxSignOutDate:data['maxSignOutDate'],cdCount:data['cdCount'],jbCount:data['jbCount']},data.record,data.staff);
 			var loginAcct = newData.loginAcct;
 			if(loginAcct.startsWith('200')){
 				newData['company'] = '中融得勤';
 				var staffNum = loginAcct - 200000;
 				newData['staffNum'] = staffNum;
 			}else{
 				var staffNum = loginAcct - 100000;
 				newData['staffNum'] = staffNum;
 			}
 			var maxSignOut = newData['maxSignOut'];
 			if(maxSignOut==''){
 				$('#signDiv').html('没有考勤记录');
 				//$('#signDiv').remove();
 			}else{
	 			var maxSignOutDate = newData['maxSignOutDate'];
	 			newData['maxSignOutDate'] = dateIdFormat(maxSignOutDate);
 			}
 			
 			fillRecord(newData,'',',','.mySwiper');
 		});
    	 
    	 ajax.daoCall({loading:false,controls:['YearDataDao.getTripInfo'],params:{}},function(rs){
 			var data = rs['YearDataDao.getTripInfo'].data;
 			var newData = data.record;
 			var allDestCity = data.allDestCity;
 			if(allDestCity==''){
 				newData['cityCount'] = 0 ;
 				$('.trip-slide').find('div').html('今年您没有出差<br><br><br>愿你走出万里<br>归来仍是少年');
 			}else{
 				newData['allDestCity'] = allDestCity;
	 			newData['cityCount'] = allDestCity.split(',').length;
	 			fillRecord(newData,'',',','.mySwiper');
 			}
 		});
    	 
    	 ajax.daoCall({loading:false,controls:['YearDataDao.getCheckUserCount'],params:{}},function(rs){
 			var data = rs['YearDataDao.getCheckUserCount'].data;
 			var newData = $.extend(data.record,{checkUserCount:data.checkUserCount});
 			
 			var checkUserCount = newData['checkUserCount'];
 			if(checkUserCount==0){
 				$('#checkUserInfo').remove();
 			}
 			
 			fillRecord(newData,'',',','.mySwiper');
 		});
    	 
    	 ajax.daoCall({loading:false,controls:['YearDataDao.getFlowData'],params:{}},function(rs){
 			var data = rs['YearDataDao.getFlowData'].data;
 			var newData = $.extend(data.flowNum,data.maxCheckInfo,data.kqData);
 			
 			var applyList = data['applyList'];
 			var approveInfo = data['approveInfo'];
 			var flowApplyCount = newData['FLOW_APPLY_COUNT'];
 			
 			var sumFillTime = newData['SUM_FILL_TIME'];
 			if(sumFillTime){
	 			newData['sumFillTime'] = secondFmt(sumFillTime);
 			}else{
 				newData['sumFillTime'] = '0秒';
 			}
 			
 			var minCheckTime = '';
 			var minuteTs = newData['MINUTE_TS'];
 			var dayTs = newData['DAY_TS'];
 			var hourTs = newData['HOUR_TS'];
 			if(minuteTs==0){
 				minuteTs = 1;
 			}
 			if(hourTs==0){
 				minCheckTime = minuteTs+"分钟";
 			}else if(dayTs>0){
 				minCheckTime = dayTs+"天";
 			}else if(dayTs==0&&hourTs>0){
 				minCheckTime = hourTs+"小时";
 			}
 			newData['minCheckTime'] = minCheckTime;
 			
 			var applyListHtml = [];
 			for(var index in applyList){
 				var row = applyList[index];
 				applyListHtml.push("•【"+row.FLOW_NAME+"】申请"+row.APPLY_COUNT+"次");
 			}
 			$('#applyList').html(applyListHtml.join('<br>'));
 			
 			if(flowApplyCount==0){
 				$('#applyCountInfo').remove();
 			}else{
 				$('#applyFlowCount').text(flowApplyCount);
 				var approveCount = approveInfo.APPROVE_COUNT
 				$('#approveCount').text(approveCount);
 				var lastCheckTime = approveInfo.CHECK_TIME;
 				if(lastCheckTime&&isTimeAfterEightPM(lastCheckTime)){
 					$('#lastCheckTime').html('您审批过'+approveCount+'次流程,最晚1次'+lastCheckTime+'，辛苦了。<br>');
 				}
 			}
 			
 			
 			fillRecord(newData,'',',','.mySwiper');
 		});
    	 
    	 ajax.daoCall({loading:false,controls:['YearDataDao.getWorkData'],params:{}},function(rs){
 			var data = rs['YearDataDao.getWorkData'].data;
 			fillRecord(data,'',',','.mySwiper');
 			
 			var allTaskNum = data.allTaskNum;
 			
 			var myPorject = data.myPorject;
 			if(myPorject&&myPorject.length>0){
	 			var taskHtml = [];
	 			for(var index in myPorject){
	 				var row = myPorject[index];
	 				taskHtml.push("•【"+row.PROJ_NAME+"】协作"+row.COUNT+"次");
	 			}
	 			$('#taskTopList').html(taskHtml.join('<br>'));
 			}else{
 				$('.taskTop-slide').remove();
 			}
 			
 		});
    	 
     });
     
     function isTimeAfterEightPM(timeString) {
   	    // 将字符串时间转换为 Date 对象
   	    var parsedTime = new Date(timeString);
   	    var hours = parsedTime.getHours();
   	    // 判断时间是否晚于晚上八点（20:00）
   	    return hours >= 20;
    	}
      
     function dateIdFormat(dateString){
   		if(dateString){
   			var pattern = /(\d{4})(\d{2})(\d{2})/;
   			var formatedDate = dateString.replace(pattern, '$1-$2-$3');
   			return formatedDate;
   		}else{
   			return '';
   		}
   	}
     
     var secondFmt = function(value) {
   	    var theTime = parseInt(value);// 秒
   	    var middle= 0;// 分
   	    var hour= 0;// 小时

   	    if(theTime >= 60) {
   	        middle= parseInt(theTime/60);
   	        theTime = parseInt(theTime%60);
   	        if(middle>= 60) {
   	            hour= parseInt(middle/60);
   	            middle= parseInt(middle%60);
   	        }
   	    }
   	    var result = ""+parseInt(theTime)+"秒";
   	    if(middle > 0) {
   	        result = ""+parseInt(middle)+"分"+result;
   	    }
   	    if(hour> 0) {
   	        result = ""+parseInt(hour)+"小时"+result;
   	    }
   	    return result;
   	 }
     
     const audio = document.getElementById('backgroundMusic');
     const audioControl = document.getElementById('audioControl');

     audio.addEventListener('play', () => {
       audioControl.classList.add('rotating'); // 当音乐播放时添加旋转类名
     });

     audio.addEventListener('pause', () => {
       audioControl.classList.remove('rotating'); // 当音乐暂停时移除旋转类名
     });

     // 页面加载时自动播放音乐
     window.addEventListener('load', () => {
       audio.play();
     });

     audioControl.addEventListener('click', () => {
       if (audio.paused) {
         audio.play();
       } else {
         audio.pause();
       }
     });
     
     document.addEventListener('WeixinJSBridgeReady', function onBridgeReady() {
 	    WeixinJSBridge.call('hideOptionMenu');
 	});
     
   
    </script>
    
  </body>
</html>

