<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>事务管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		.layui-table-cell{padding: 0 6px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="taskMgrForm">
			<input name="taskState" id="taskState" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5 id="title">发件箱</h5>
	          		     <div class="input-group input-group-sm" style="width: 180px">
	          		    	<span class="input-group-addon">标题</span>	
                     		<input data-mars-reload="false" type="text" name="title" class="form-control input-sm">
                    	 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="taskMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="taskMgrTable"></table>
					</div>
				</div>
		</form>
		 <script type="text/html" id="btnBar">
			<a href="javascript:;" style="color:#337ab7;" onclick="taskMgr.edit('{{:AFFAIR_ID}}')">修改</a>
			<a href="javascript:;" style="color:#337ab7;" onclick="taskMgr.del('{{:AFFAIR_ID}}')">删除</a>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
  <script type="text/javascript">
		var taskMgr={
			init:function(){
				$("#taskMgrForm").initTable({
					mars:'AffairDao.sendAffairList',
					height:'full-120',
					id:'taskMgrTable',
					limit:20,
					even: true,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'AFFAIR_NAME',
						title: '主题',
						align:'left',
						minWidth:220,
						event:'taskMgr.detail',
						style:'color:#036fe2;cursor:pointer',
						templet:function(row){
							if(row['VIEW_COUNT']==0){
								return row['AFFAIR_NAME']+'<span class="layui-badge ml-5">新</span>';
							}else{
								return row['AFFAIR_NAME'];
							}
						}
					},{
					    field: 'CREATE_NAME',
						title: '发件人',
						align:'left',
						width:120
					},{
					    field: 'RECEIVER_USER_NAME',
						title: '收件人',
						align:'left',
						width:120
					},{
					    field: 'CREATE_TIME',
						title: '已读/收件人',
						align:'center',
						width:140,
						templet:'<div>{{d.LOOK_COUNT}}/{{d.RECEIVER_COUNT}}</div>'
						
					},{
					    field: 'CREATE_TIME',
						title: '发起时间',
						align:'center',
						width:140
					},{
						field:'LAST_REPLY_TIME',
						title:'最新回复时间',
						width:140
					},{
					    field: 'VIEW_COUNT',
						title: '浏览数',
						align:'center',
						width:100,
						templet:'<div>{{d.VIEW_COUNT}}</div>'
					},{
						field:'',
						width:90,
						title:'操作',
						templet:function(row){
							return renderTpl('btnBar',row);
						}
					}
				]],done:function(){
					 //$("tbody [data-field='VIEW_COUNT'][data-content='0']").parent().find('td').css('font-weight','700');
			   }});
			},
			query:function(){
				$("#taskMgrForm").render();
				$("#taskMgrForm").queryData({id:'taskMgrTable',jumpOne:true});
			},
			edit:function(id){
				let url = '${ctxPath}/pages/affair/affair-edit.jsp?affairId='+id;
				$.get(url,{},function(result){
  					$(".right-content").html(result);
				});
			},
			detail:function(data){
				let url = '${ctxPath}/pages/affair/affair-detail.jsp?affairId='+data.AFFAIR_ID+"&source=send";
				$.get(url,{},function(result){
					window.location.hash = "#"+data.AFFAIR_ID;
  					$(".right-content").html(result);
				});
			},del:function(id){
				layer.confirm('确认删除吗?',function(index){
					layer.close(index);
					ajax.remoteCall("${ctxPath}/servlet/affair?action=del",{id:id},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								taskMgr.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			}
		}
		$(function(){
			$("#taskMgrForm").render({success:function(result){
				taskMgr.init();
			}});
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>