package com.yunqu.work.dao.project;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;

@WebObject(name = "WorkHourDao")
public class WorkHourDao extends AppDaoContext {
	
	@WebControl(name="projectPersonWorkTimeSum",type=Types.TEMPLATE)
	public JSONObject projectPersonWorkTimeSum(){
		EasySQL sql = new EasySQL();
		sql.append(param.getString("projectId"),"SELECT create_name,sum(work_time) work_time from yq_project_work_hour where project_id = ?");
		sql.append("GROUP BY CREATE_NAME");
		return queryForList(sql.getSQL(), sql.getParams());
	}

	@WebControl(name="projectDeptWorkTimeSum",type=Types.TEMPLATE)
	public JSONObject projectDeptWorkTimeSum(){
		EasySQL sql = new EasySQL();
		sql.append(param.getString("projectId"),"SELECT dept_name,sum(work_time) work_time from yq_project_work_hour where project_id = ?");
		sql.append("GROUP BY DEPT_NAME");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="deptWorkhourStat",type=Types.TEMPLATE)
	public JSONObject deptWorkhourStat(){
		EasySQL sql=getEasySQL("select temp.*,t1.PROJECT_NAME from (select t1.project_id,sum(t1.work_time) time,count(DISTINCT(t1.creator)) PERSON_NUM from yq_project_work_hour t1,yq_project_wh t2  where 1=1 and t2.id = t1.wh_id");
		if(isSuperUser()||hasRole("WEEKLY_MANGER")) {
			sql.appendRLike(param.getString("deptName"),"and t2.dept_name like ? ");
		}else if(hasRole("DEPT_MGR")) {
			sql.append(getDeptId(),"and t2.dept_id = ? ");
		}else {
			sql.append(getUserId(),"and t1.creator = ?");
		}
		String projectId = param.getString("projectId");
		if(StringUtils.notBlank(projectId)) {
			sql.append(projectId,"and t1.project_id = ?");
		}
		this.setCondition(sql);
		
		sql.append("GROUP BY t1.project_id ) temp,yq_project t1 where 1=1");
		sql.append("and t1.PROJECT_ID=temp.project_id");
		sql.append("order by temp.time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectWorkhourDetail",type=Types.TEMPLATE)
	public JSONObject projectWorkhourDetail(){
		EasySQL sql=getEasySQL("select t1.*,t2.DEPTS,t2.USERNAME from yq_project_work_hour t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t1.worker=t2.USER_ID where 1=1");
		sql.append(param.getString("projectId"),"and t1.project_id = ?");
		sql.appendLike(param.getString("userName"),"and t1.create_name like ?");
		this.setCondition(sql);
		sql.append("order by t1.month_id desc,t1.create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	private void setCondition(EasySQL sql) {
		String startDate=param.getString("startDate");
		String endDate=param.getString("endDate");
		if(StringUtils.notBlank(startDate)){
			sql.append(startDate.replaceAll("-",""),"and t1.month_id >= ?");
		}
		if(StringUtils.notBlank(endDate)){
			sql.append(endDate.replaceAll("-",""),"and t1.month_id <= ?");
		}
		
	}
	
	@WebControl(name="queryPersonWorkHour",type=Types.TEMPLATE)
	public JSONObject queryPersonWorkHour(){
		EasySQL sql=new EasySQL("select t1.* from yq_project_wh t1 where 1=1");
		sql.append(getUserId(), "and t1.user_id = ?");
		this.setCondition(sql);
		sql.append("order by t1.month_id desc");
		return  queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="queryProjectWh",type=Types.TEMPLATE)
	public JSONObject queryProjectWh(){
		EasySQL sql=new EasySQL("select t1.*,t2.DEPTS,t2.USERNAME from yq_project_wh t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t1.user_id = t2.USER_ID where 1=1");
		sql.append("and t1.state > 0");
		this.setCondition(sql);
		sql.appendLike(param.getString("userName"),"and t1.user_name like ?");
		String projectId = param.getString("projectId");
		if(StringUtils.notBlank(projectId)) {
			sql.append(projectId,"and t1.id in (select d1.wh_id from yq_project_work_hour d1 where d1.wh_id = t1.id and d1.project_id = ?)");
		}
		if(isSuperUser()||hasRole("WEEKLY_MANGER")) {
			sql.appendRLike(param.getString("deptName"),"and t1.dept_name like ? ");
		}else if(hasRole("DEPT_MGR")) {
			String userIds = this.queryForString("select group_concat(t2.USER_ID) from "+Constants.DS_MAIN_NAME+".easi_dept_user t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID where t2.STATE = 0 and t1.DEPT_ID= ?",new Object[]{getDeptId()});
			sql.appendIn(userIds.split(","), "and t1.user_id");
		}else {
			sql.append(getUserId(),"and t1.user_id = ?");
		}
		sql.append("order by t1.month_id desc,t1.update_time desc");
		return  queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="userWorkHourList",type=Types.TEMPLATE)
	public JSONObject userWorkHourList(){
		EasySQL sql=new EasySQL("select t3.* from yq_project_work_hour t3 where 1=1");
		String userId = param.getString("userId");
		if(StringUtils.isBlank(userId)) {
			sql.append(getUserId(), "and t3.WORKER = ?");
		}else {
			sql.append(userId, "and t3.WORKER = ?");
		}
		String monthId=param.getString("monthId");
		sql.append(monthId,"and t3.month_id = ?",false);
		sql.append("order by t3.item_id");
		return  queryForList(sql.getSQL(), sql.getParams());
			
	}
	
	@WebControl(name="workHourList",type=Types.PAGE)
	public JSONObject workHourList(){
		String deptId=param.getString("deptId");
		if(StringUtils.isBlank(deptId)){
			deptId=getDeptId();
		}
		EasySQL sql=new EasySQL("select t1.*,t2.project_type,t2.PROJECT_NO,t3.USERNAME,t3.DEPTS,t3.DATA1  from yq_project_work_hour t1 INNER JOIN yq_project t2 on t1.project_id=t2.PROJECT_ID INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t3 on t3.USER_ID=t1.worker inner join yq_project_wh t4 on t4.id = t1.wh_id where 1=1");
		this.setCondition(sql);
		
		sql.append(param.getString("projectType"),"and t2.PROJECT_TYPE = ?");
		sql.append(param.getString("projectId"),"and t2.PROJECT_ID = ?");
		sql.appendLike(param.getString("userName"),"and t4.USER_NAME like ?");
		if(isSuperUser()||hasRole("WEEKLY_MANGER")) {
			sql.appendRLike(param.getString("deptName"),"and t4.dept_name like ? ");
//			sql.append(param.getString("deptId"),"and t4.dept_id = ? ");
		}else if(hasRole("DEPT_MGR")) {
			//String userIds = this.queryForString("select group_concat(t2.USER_ID) from "+Constants.DS_MAIN_NAME+".easi_dept_user t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID where t2.STATE = 0 and t1.DEPT_ID= ?",new Object[]{deptId});
			//sql.appendIn(userIds.split(","), "and t1.WORKER");
			sql.append(getDeptId(), "and t4.dept_id = ?");
		}else {
			sql.append(getUserId(),"and t1.creator = ?");
		}

		sql.append("ORDER BY t1.create_time desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
			
	}

}
