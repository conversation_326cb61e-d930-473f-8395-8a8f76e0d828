package com.yunqu.work.dao;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.TaskModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="TaskDao")
public class TaskDao extends AppDaoContext {
	
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		TaskModel model=new TaskModel();
		model.setColumns(getParam("task"));
		JSONObject jsonObject= queryForRecord(model);
		JSONObject data=jsonObject.getJSONObject("data");
		String sql="select USER_ID from  yq_cc where FK_ID = ?";
		List<EasyRow> list;
		try {
			list = this.getQuery().queryForList(sql, model.getTaskId());
			if(list!=null&&list.size()>0){
				String[] mailtos=new String[list.size()];
				for(int i=0;i<list.size();i++){
					mailtos[i]=list.get(i).getColumnValue(1);
				}
				data.put("mailtos",mailtos);
				jsonObject.put("mailtos", mailtos);
				jsonObject.put("data", data);
			}
			String projectId=data.getString("PROJECT_ID");
			if(StringUtils.notBlank(projectId)){
				String projectName=this.getQuery().queryForString("select PROJECT_NAME from YQ_PROJECT where PROJECT_ID = ?",projectId);
				jsonObject.put("projectName",projectName);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		if(StringUtils.notBlank(model.getTaskId())){
			LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getTaskId());
		}
		return jsonObject;
	}
	@WebControl(name="taskType",type=Types.DICT)
	public JSONObject taskType(){
		String projectId=param.getString("projectId");
		if(StringUtils.notBlank(projectId)){
			return getDictByQuery("select task_type_id,task_type_name from yq_task_type where project_id in (0,'"+ param.getString("projectId")+"')");
		}else{
			return getDictByQuery("select task_type_id,task_type_name from yq_task_type where project_id = ?",0);
		}
	}
	/**
	 * 我发起的任务
	 * @return
	 */
	@WebControl(name="myTaskList",type=Types.LIST)
	public JSONObject myTaskList(){
		int status=param.getIntValue("status");
		EasySQL sql=null;
		if(status==1){//我负责的任务
			sql=getEasySQL("select t1.* from yq_task t1  where 1=1");
			sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
			sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
			String state = param.getString("taskState");
			if("25".equals(state)){
				sql.append(30,"and t1.task_state < ?");
				sql.append(EasyDate.getCurrentDateString(),"and t1.deadline_at < ?");
			}else{
				sql.append(param.getString("taskState"),"and t1.task_state = ?");
			}
			sql.append(getUserPrincipal().getUserId(),"and t1.ASSIGN_USER_ID = ?",true);
			sql.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
			sql.append("order by t1.CREATE_TIME desc");
		}else if(status==2){
			sql=getEasySQL("select t1.* from yq_task t1  where 1=1");
			sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
			String state = param.getString("taskState");
			if("25".equals(state)){
				sql.append(30,"and t1.task_state < ?");
				sql.append(EasyDate.getCurrentDateString(),"and t1.deadline_at < ?");
			}else{
				sql.append(param.getString("taskState"),"and t1.task_state = ?");
			}
			sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
			sql.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
			sql.append(getUserPrincipal().getUserId(),"and t1.CREATOR = ?",true);
			sql.append("order by t1.CREATE_TIME desc");
		}else if(status==3){
			sql=getEasySQL("select t1.*,t2.create_time cc_time from yq_task t1 INNER JOIN yq_cc t2 on t2.fk_id=t1.task_id  where 1=1");
			sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
			String state = param.getString("taskState");
			if("25".equals(state)){
				sql.append(30,"and t1.task_state < ?");
				sql.append(EasyDate.getCurrentDateString(),"and t1.deadline_at < ?");
			}else{
				sql.append(param.getString("taskState"),"and t1.task_state = ?");
			}
			sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
			sql.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
			sql.append(getUserPrincipal().getUserId(),"and t2.user_id = ?",true);
			sql.append("order by t1.CREATE_TIME desc");
		}else if(status==4){
			//共享任务
			sql=getEasySQL("select t1.* from yq_task t1  where 1=1");
			sql.append(Constants.TASK_OK,"and t1.task_state >= ? and t1.task_auth = 0");
			sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
			sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
			sql.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
			sql.append("order by t1.finish_time desc");
		}else{
			return emptyPage();
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="myTaskListCountStat",type=Types.RECORD)
	public JSONObject myTaskListCountStat(){
		int status=param.getIntValue("status");
		EasySQL sql=null;
		if(status==1){//我负责的任务
			sql=getEasySQL("select");
			sql.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
			sql.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
			sql.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
			sql.append(40,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS d,");
			sql.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state < 30  THEN 1 ELSE 0 END) AS e");
			sql.append("from yq_task t1  where 1=1");
			sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
			sql.append(param.getString("taskState"),"and t1.task_state = ?");
			sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
			sql.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
			sql.append(getUserPrincipal().getUserId(),"and t1.ASSIGN_USER_ID = ?",true);
		}else if(status==2){
			sql=getEasySQL("select");
			sql.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
			sql.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
			sql.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
			sql.append(40,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS d,");
			sql.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state < 30  THEN 1 ELSE 0 END) AS e");
			sql.append("from yq_task t1  where 1=1");
			sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
			sql.append(param.getString("taskState"),"and t1.task_state = ?");
			sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
			sql.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
			sql.append(getUserPrincipal().getUserId(),"and t1.CREATOR = ?",true);
		}else if(status==3){
			sql=getEasySQL("select");
			sql.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
			sql.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
			sql.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
			sql.append(40,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS d,");
			sql.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state < 30  THEN 1 ELSE 0 END) AS e");
			sql.append("from yq_task t1 INNER JOIN yq_cc t2 on t2.fk_id=t1.task_id  where 1=1");
			sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
			sql.append(param.getString("taskState"),"and t1.task_state = ?");
			sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
			sql.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
			sql.append(getUserPrincipal().getUserId(),"and t2.user_id = ?",true);
		}else if(status==4){
			//共享任务
			sql=getEasySQL("select t1.* from yq_task t1  where 1=1");
			sql.append(Constants.TASK_OK,"and t1.task_state = ? and t1.task_auth = 0");
			sql.appendLike(param.getString("taskName"),"and t1.task_name like ?");
			sql.append(param.getString("projectId"),"and t1.PROJECT_ID = ?");
			sql.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
		}else{
			return getJsonResult(new JSONObject());
		}
		JSONObject result = queryForRecord(sql.getSQL(), sql.getParams());
		JSONObject data = result.getJSONObject("data");
		String[] keys=new String[]{"A","B","C","D","E"};
		for(int i=0;i<keys.length;i++){
			if(StringUtils.isBlank(data.getString(keys[i]))){
				data.put(keys[i],"0");
			}
		}
		result.put("data", data);
		return result;
	}
	/**
	 * 项目任务列表
	 * @return
	 */
	@WebControl(name="projectTaskList",type=Types.LIST)
	public JSONObject projectTaskList(){ 
		EasySQL sql=getEasySQL("select t2.* from yq_project t1 INNER JOIN yq_task t2 on t2.project_id=t1.PROJECT_ID where 1=1 ");
		sql.appendLike(param.getString("taskName"),"and t2.task_name like ?");
		sql.append(param.getString("taskState"),"and t2.task_state = ?");
		sql.append(param.getString("projectId"),"and t1.project_id = ?");
		sql.append(param.getString("taskTypeId"),"and t2.task_type_id = ?");
		sql.append("order by t2.CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 任务查询
	 * @return
	 */
	@WebControl(name="taskList",type=Types.LIST)
	public JSONObject taskList(){
		if(isSuperUser()||getUserPrincipal().isRole("TASK_MANGER")){
			EasySQL easySQL=getEasySQL("select * from YQ_TASK where 1=1");
			String state = param.getString("taskState");
			if("25".equals(state)){
				easySQL.append(30,"and task_state < ?");
				easySQL.append(EasyDate.getCurrentDateString(),"and deadline_at < ?");
			}else{
				easySQL.append(param.getString("taskState"),"and task_state = ?");
			}
			easySQL.appendLike(param.getString("taskName"),"and task_name like ?");
			easySQL.append(param.getString("userId"),"and assign_user_id = ?");
			easySQL.append(param.getString("projectId"),"and project_id = ?");
			easySQL.append(param.getString("taskTypeId"),"and task_type_id = ?");
			easySQL.append("order by CREATE_TIME desc");
			return queryForPageList(easySQL.getSQL(), easySQL.getParams());
		}else{
			return emptyPage();
		}
	}
	/**
	 * 任务统计
	 * @return
	 */
	@WebControl(name="projectTaskStat",type=Types.LIST)
	public JSONObject projectTaskStat(){
		EasySQL easySQL=getEasySQL("select t2.PROJECT_NAME,t1.project_id,");
		easySQL.append("SUM(1) AS E,");
		easySQL.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
		easySQL.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
		easySQL.append(40,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS d");
		easySQL.append("from yq_task t1 INNER JOIN yq_project t2 on t2.PROJECT_ID=t1.project_id where 1=1");
		easySQL.append("and t1.project_id <>''");
		easySQL.append(param.getString("taskState"),"and t1.task_state = ?");
		easySQL.appendLike(param.getString("taskName"),"and t1.task_name like ?");
		easySQL.append(param.getString("projectId"),"and t1.project_id = ?");
		easySQL.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
		easySQL.append("GROUP BY t1.project_id");
		easySQL.append("order by t2.UPDATE_TIME desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	@WebControl(name="taskCountStat",type=Types.RECORD)
	public JSONObject taskCountStat(){
		EasySQL easySQL=getEasySQL("select ");
		easySQL.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
		easySQL.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
		easySQL.append(40,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS d,");
		easySQL.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state < 30  THEN 1 ELSE 0 END) AS e");
		easySQL.append("from YQ_TASK t1 where 1=1");
		easySQL.appendLike(param.getString("taskName"),"and t1.task_name like ?");
		easySQL.append(param.getString("projectId"),"and t1.project_id = ?");
		easySQL.append(param.getString("taskTypeId"),"and t1.task_type_id = ?");
		easySQL.append(param.getString("userId"),"and t1.assign_user_id = ?");
		JSONObject result = queryForRecord(easySQL.getSQL(), easySQL.getParams());
		JSONObject data = result.getJSONObject("data");
		String[] keys=new String[]{"A","B","C","D","E"};
		for(int i=0;i<keys.length;i++){
			if(StringUtils.isBlank(data.getString(keys[i]))){
				data.put(keys[i],"0");
			}
		}
		result.put("data", data);
		return result;
	}
	
	@WebControl(name="taskAllLookLog",type=Types.LIST)
	public JSONObject taskAllLookLog(){
		EasySQL easySQL=getEasySQL("select t3.*,t2.USERNAME,t1.look_time from yq_look_log t1,"+Constants.DS_MAIN_NAME+".easi_user t2,yq_task t3 where t1.look_by=t2.USER_ID and t3.task_id=t1.fk_id ORDER BY t1.look_time desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	@WebControl(name="taskAllCommentLog",type=Types.LIST)
	public JSONObject taskAllCommentLog(){
		EasySQL easySQL=getEasySQL("select t3.*,t2.USERNAME,t1.create_time as comment_time,t1.content from yq_comments t1,"+Constants.DS_MAIN_NAME+".easi_user t2,yq_task t3 where t1.creator=t2.USER_ID and t3.task_id=t1.fk_id ORDER BY t1.create_time desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	@WebControl(name="taskAllOpLog",type=Types.LIST)
	public JSONObject taskAllOpLog(){
		EasySQL easySQL=getEasySQL("select t3.*,t2.USERNAME,t1.op_time,t1.content from yq_oplog t1,"+Constants.DS_MAIN_NAME+".easi_user t2,yq_task t3 where t1.op_by=t2.USER_ID and t3.task_id=t1.fk_id ORDER BY t1.op_time desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	
	@WebControl(name="getBugList",type=Types.LIST)
	public JSONObject getBugList(){
		String sql="select * from zt_bug where assignedTo = ? ";
		EasyQuery query=EasyQuery.getQuery(getAppName(), Constants.ZENTAO_DS_NAME);
		this.setQuery(query);
		return queryForPageList(sql, new Object[]{getUserPrincipal().getLoginAcct()});
	}
	
	@WebControl(name="taskV1",type=Types.TEMPLATE)
	public JSONObject taskV1(){
		String sql="select CREATOR,count(1) count from yq_task GROUP BY CREATOR order by count desc";
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		setQuery(query);
		this.setQuery(query);
		return queryForList(sql, new Object[]{});
	}
	@WebControl(name="taskV2",type=Types.TEMPLATE)
	public JSONObject taskV2(){
		String sql="select ASSIGN_USER_ID,count(1) count from yq_task GROUP BY ASSIGN_USER_ID order by count desc";
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		setQuery(query);
		this.setQuery(query);
		return queryForList(sql, new Object[]{});
	}
	//待办
	@WebControl(name="taskV3",type=Types.TEMPLATE)
	public JSONObject taskV3(){
		String sql="select ASSIGN_USER_ID,count(1) count from yq_task where  task_state=10 GROUP BY ASSIGN_USER_ID order by count desc";
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		setQuery(query);
		this.setQuery(query);
		return queryForList(sql, new Object[]{});
	}
	//待办
	@WebControl(name="taskV4",type=Types.TEMPLATE)
	public JSONObject taskV4(){
		String sql="select ASSIGN_USER_ID,count(1) count from yq_task where  task_state < 40 and deadline_at < ? GROUP BY ASSIGN_USER_ID order by count desc";
		EasyQuery query=getQuery();
		query.setMaxRow(10);
		setQuery(query);
		this.setQuery(query);
		return queryForList(sql, new Object[]{EasyDate.getCurrentDateString()});
	}
	
	@WebControl(name="taskDayStat",type=Types.TEMPLATE)
	public JSONObject taskDayStat(){
		String sql="select DATE_ID date,count(1) count1 from yq_task GROUP BY DATE_ID  ORDER BY DATE_ID desc";
		EasyQuery query=getQuery();
		query.setMaxRow(30);
		try {
			List<JSONObject> list = query.queryForList(sql,new Object[]{},new JSONMapperImpl());
			sql="select FINISH_DATE_ID date,count(1) count2 from yq_task GROUP BY FINISH_DATE_ID  ORDER BY FINISH_DATE_ID desc";
			List<JSONObject> list2= query.queryForList(sql,new Object[]{},new JSONMapperImpl());
			
			JSONObject result=new JSONObject();
			result.put("rs1",list);
			result.put("rs2",list2);
			return getJsonResult(result);
		} catch (SQLException e) {
			return getJsonResult(new JSONObject());
		}
	}
	

}



