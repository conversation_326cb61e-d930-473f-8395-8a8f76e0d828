<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div class="layui-table-tool">
	<div class="pull-left">
		<i class="layui-icon layui-icon-time"></i> 费用明细
	</div>
	<div class="pull-right">
		<button class="btn btn-sm btn-default" onclick="addManyTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增行</button>
		<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
		<button class="btn btn-sm btn-default ml-10" onclick="copyTr();" type="button"><i class="layui-icon layui-icon-template"></i> 复制</button>
		<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
		<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
	</div>
</div>
<div class="table-responsive" id="bxItemContainer" style="min-height: 100px;overflow-x:auto;">
 <table class="layui-table text-nowrap bxTable" style="margin-top: -1px;">
   <thead>
    <tr>
      <th></th>
      <th>费用日期<button onclick="syncDate();" class="btn btn-xs btn-default ml-10" type="button">同步</button></th>
      <th>费用类型(内部)<button onclick="syncFeeType();" class="btn btn-xs btn-default ml-10" type="button">同步</button></th>
      <th>费用类型</th>
      <th>费用说明(内部)  <EasyTag:hasRole roleId="HR_MGR"><button onclick="syncFeeDesc();" class="btn btn-xs btn-default ml-10" type="button">同步</button></EasyTag:hasRole></th>
      <th>发票类型</th>
      <th>金额</th>
      <th>部门<button onclick="syncDept();" class="btn btn-xs btn-default ml-10" type="button">同步</button></th>
      <th>项目号<button onclick="syncContract();" class="btn btn-xs btn-default ml-10" type="button">同步</button></th>
      <th>产品线<button onclick="syncContract();" class="btn btn-xs btn-default ml-10" type="button">同步</button></th>
      <th>税率(%)</th>
      <th>税金</th>
      <th>金额(不含税)</th>
    </tr> 
  </thead>
  <tbody id="itemTbody" data-mars="BxDao.bxItems" data-template="bxItems"></tbody>
</table>
</div>
<br>	
<br>	
<br>	
<br>	

<script id="trItems" type="text/x-jsrender">
<tr class="inputItem" id="item_{{:ITEM_ID}}" data-id="{{:ITEM_ID}}">
	      <td>
	      	<input type="hidden" name="orderIndex_{{:ITEM_ID}}"/>
	      	<input type="hidden" name="itemId_{{:ITEM_ID}}"/>
	      	<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
	        <label class="checkbox checkbox-inline checkbox-success">
		      	 <input tabindex="-1" type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
	        </label>
	      </td>
	      <td>
				<input type="hidden" name="budgetMonth_{{:ITEM_ID}}" data-field="budgetMonth"/>
	      	    <input type="text" tabindex="-1" data-laydate="{type:'date',max:0, min:-365}" id="bxDate_{{:ITEM_ID}}" data-rules="required" style="width: 80px;" name="bxDate_{{:ITEM_ID}}" class="form-control input-sm" data-field="bxDate"/>
	      </td>
	      <td>
	      		<input type="hidden" name="feeInCode_{{:ITEM_ID}}" data-field="feeInCode">
	      		<input data-rules="required" tabindex="-1" class="form-control input-sm" placeholder="请选择" data-field="feeInType" style="width: 100px;" data-id="{{:ITEM_ID}}" readonly="readonly" onclick="selectFeeType(this,'in')" name="feeInType_{{:ITEM_ID}}">
	      </td>
	      <td>
	      		<input type="hidden" name="subjectNo_{{:ITEM_ID}}" data-field="subjectNo"/>
	        	<input type="hidden" name="subjectName_{{:ITEM_ID}}" data-field="subjectName"/>
	      		<input type="hidden" name="feeCode_{{:ITEM_ID}}" data-field="feeCode">
	      		<input data-rules="required" tabindex="-1" class="form-control input-sm" placeholder="请选择" data-field="feeType" style="width: 100px;" data-id="{{:ITEM_ID}}" readonly="readonly" onclick="selectFeeType(this,'out')" name="feeType_{{:ITEM_ID}}">
	      </td>
	      <td>
	      		<textarea class="hidden" name="feeJson_{{:ITEM_ID}}" data-id="{{:ITEM_ID}}" data-field="feeJson"></textarea>
	      		<textarea tabindex="-1" onclick="showFeeInfo(this,'{{:ITEM_ID}}')" placeholder="请选择" style="height: 40px;resize: none;;min-width:100px" data-rules="required" data-field="feeDesc" class="form-control input-sm" readonly="readonly" name="feeDesc_{{:ITEM_ID}}"></textarea>
	      </td>
	      <td>
	      		<input type="hidden" name="invoiceNo_{{:ITEM_ID}}" data-id="{{:ITEM_ID}}" data-field="invoiceNo"/>
	      	    <input type="text" tabindex="-1" onclick="showFeeInfo(this,'{{:ITEM_ID}}')" data-rules="required" style="width: 90px;" readonly="readonly" name="invoiceType_{{:ITEM_ID}}" class="form-control input-sm" data-field="invoiceType"/>
	      </td>
	      <td>
	      		<input type="number" data-rules="required" style="width: 80px;" class="form-control input-sm" data-field="amount" name="amount_{{:ITEM_ID}}" onblur="onlyNumber(this);calcBxMoney('{{:ITEM_ID}}')"/>
	      </td>
	      <td>
			   <input type="hidden" value="" name="deptCode_{{:ITEM_ID}}" data-field="deptCode"/>
			   <input type="hidden" value="" name="deptType_{{:ITEM_ID}}" data-field="deptType"/>
	      	   <input type="hidden" name="deptId_{{:ITEM_ID}}" data-field="deptId"/>
	           <input data-rules="required" style="width: 90px;" placeholder="请选择"  onclick="selectBxDept(this)" onchange="getBxDeptInfo('{{:ITEM_ID}}')" class="form-control input-sm" data-field="deptName" data-value="${staffInfo.deptId}"  name="deptName_{{:ITEM_ID}}" data-id="{{:ITEM_ID}}" id="deptName_{{:ITEM_ID}}">
	      </td>
	      <td>
	        	<input type="hidden" name="contractState_{{:ITEM_ID}}" data-field="contractState"/>
	        	<input type="hidden" name="contractNo_{{:ITEM_ID}}" data-field="contractNo"/>
	        	<input type="hidden" name="contractId_{{:ITEM_ID}}" data-field="contractId"/>
	        	<input type="text" tabindex="-1" style="width: 150px;" placeholder="请选择" onclick="itemContract(this)" readonly="readonly" data-rules="required" id="contractName_{{:ITEM_ID}}" data-id="{{:ITEM_ID}}" name="contractName_{{:ITEM_ID}}" class="form-control input-sm" data-field="contractName"/>
	      </td>
	      <td>
	        	<input type="hidden" name="productLineNo_{{:ITEM_ID}}" data-field="productLineNo"/>
	        	<input style="width: 110px;" tabindex="-1" class="form-control input-sm" placeholder="请选择"  data-field="productLine" onclick="singleDict(this,'Y001')" readonly="readonly" data-rules="required" data-id="{{:ITEM_ID}}" name="productLine_{{:ITEM_ID}}" id="productLine_{{:ITEM_ID}}">
	      </td>
	      <td>
				<input type="hidden" name="taxRateCode_{{:ITEM_ID}}" data-field="taxRateCode"/>
	        	<select style="width: 70px;" tabindex="-1" data-rules="required" class="form-control input-sm" data-field="taxRate" name="taxRate_{{:ITEM_ID}}" onchange="calcRowRate('{{:ITEM_ID}}',0)">
	        		<option value="">--</option>
	        		<option data-code="" value="0">0</option>
	        		<option data-code="SL02" value="1">1</option>
	        		<option data-code="SL03" value="1.5">1.5</option>
	        		<option data-code="SL04" value="3">3</option>
	        		<option data-code="SL05" value="5">5</option>
	        		<option data-code="SL06" value="6">6</option>
	        		<option data-code="SL08" value="9">9</option>
	        		<option data-code="SL11" value="13">13</option>
	        	</select>
	      </td>
	      <td>
	        	<input type="number" style="width: 70px;" tabindex="-1" readonly="readonly" value="0.00" class="form-control input-sm" onblur="onlyNumber(this);calcRAmount('{{:ITEM_ID}}')" data-field="taxes" name="taxes_{{:ITEM_ID}}"/>
	      </td>
	      <td>
	        	<input type="text" readonly="readonly" tabindex="-1" style="width: 70px;" class="form-control input-sm" data-field="rAmount" name="rAmount_{{:ITEM_ID}}"/>
	      </td>
	    </tr>
</script>

	
<script id="bxItems" type="text/x-jsrender">
  	  {{for data}}
  		<tr class="inputItem"  id="item_{{:ITEM_ID}}">
  			<td>
	      		<input type="hidden" name="orderIndex_{{:ITEM_ID}}"/>
	      		<input type="hidden" name="itemId_{{:ITEM_ID}}" value="{{:ITEM_ID}}"/>
	      		<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
	        	<label class="checkbox checkbox-inline checkbox-success">
		      	 <input tabindex="-1" type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
	       	 </label>
	      </td>
	      <td>
			    <input type="hidden" value="{{:BUDGET_MONTH}}" name="budgetMonth_{{:ITEM_ID}}" data-field="budgetMonth"/>
	      	    <input type="text" tabindex="-1" data-laydate="{type:'date',max:0, min:-365}" data-rules="required" style="width: 80px;" value="{{:BX_DATE}}" name="bxDate_{{:ITEM_ID}}" class="form-control input-sm" data-field="bxDate"/>
	      </td>
		  <td>
	      		<input type="hidden" name="feeInCode_{{:ITEM_ID}}" value="{{:FEE_IN_CODE}}" data-field="feeInCode">
	      		<input data-rules="required" tabindex="-1" class="form-control input-sm" value="{{:FEE_IN_TYPE}}" placeholder="请选择" data-field="feeInType" style="width: 100px;" data-id="{{:ITEM_ID}}" readonly="readonly" onclick="selectFeeType(this,'in')" name="feeInType_{{:ITEM_ID}}">
	      </td>
	      <td>
				<input type="hidden" name="subjectNo_{{:ITEM_ID}}" value="{{:SUBJECT_NO}}" data-field="subjectNo"/>
	        	<input type="hidden" name="subjectName_{{:ITEM_ID}}" value="{{:SUBJECT_NAME}}" data-field="subjectName"/>
				<input type="hidden" value="{{:FEE_CODE}}" name="feeCode_{{:ITEM_ID}}" data-field="feeCode">
	     	   <input data-rules="required" tabindex="-1" class="form-control input-sm" data-val="{{:FEE_TYPE}}" data-field="feeType" data-unclick="1" data-id="{{:ITEM_ID}}" readonly="readonly" style="width: 100px;" value="{{:FEE_TYPE}}" onclick="selectFeeType(this,'out')" name="feeType_{{:ITEM_ID}}">
	      </td>
	      <td>
				<textarea class="hidden" name="feeJson_{{:ITEM_ID}}" data-id="{{:ITEM_ID}}" data-field="feeJson"></textarea>
	      		<textarea style="height: 40px;resize: none;min-width:100px" tabindex="-1" onclick="showFeeInfo(this,'{{:ITEM_ID}}')" readonly="readonly" data-rules="required" class="form-control input-sm" data-field="feeDesc" name="feeDesc_{{:ITEM_ID}}">{{:FEE_DESC}}</textarea>
	      </td>
	      <td>
				<input type="hidden" name="invoiceNo_{{:ITEM_ID}}" data-id="{{:ITEM_ID}}" data-field="invoiceNo" value="{{:INVOICE_NO}}"/>
	      	    <input type="text" data-rules="required" style="width: 100px;" tabindex="-1" onclick="showFeeInfo(this,'{{:ITEM_ID}}')" readonly="readonly" value="{{:INVOICE_TYPE}}" data-field="invoiceType" name="invoiceType_{{:ITEM_ID}}" class="form-control input-sm"/>
	      </td>
	      <td>
	      		<input type="number" style="width: 80px;" class="form-control input-sm" data-field="amount" value="{{:AMOUNT}}" name="amount_{{:ITEM_ID}}" onblur="onlyNumber(this);calcBxMoney('{{:ITEM_ID}}')"/>
	      </td>
	      <td>
			   <input type="hidden" value="{{:DEPT_CODE}}" name="deptCode_{{:ITEM_ID}}" data-field="deptCode"/>
			   <input type="hidden" value="{{:DEPT_TYPE}}" name="deptType_{{:ITEM_ID}}" data-field="deptType"/>
			   <input type="hidden" value="{{:DEPT_ID}}" name="deptId_{{:ITEM_ID}}" data-field="deptId"/>
	           <input type="text" data-rules="required" readonly style="width: 90px;" onclick="selectBxDept(this)" onchange="getBxDeptInfo('{{:ITEM_ID}}')" class="form-control input-sm" data-field="deptName" value="{{:DEPT_NAME}}" id="deptName_{{:ITEM_ID}}" data-id="{{:ITEM_ID}}" name="deptName_{{:ITEM_ID}}">
	      </td>
	      <td>
				<input type="hidden" name="contractState_{{:ITEM_ID}}" data-field="contractState" value="{{:CONTRACT_STATE}}"/>
				<input type="hidden" name="contractNo_{{:ITEM_ID}}" data-field="contractNo" value="{{:CONTRACT_NO}}"/>
	        	<input type="hidden" name="contractId_{{:ITEM_ID}}" data-field="contractId" value="{{:CONTRACT_ID}}"/>
	    		<input type="text" data-rules="required" tabindex="-1" readonly data-field="contractName" id="contractName_{{:ITEM_ID}}" onclick="itemContract(this)" name="contractName_{{:ITEM_ID}}" style="width: 150px;" data-id="{{:ITEM_ID}}" value="{{:CONTRACT_NAME}}" class="form-control input-sm">
	      </td>
	      <td>
				<input type="hidden" name="productLineNo_{{:ITEM_ID}}" value="{{:PRODUCT_LINE_NO}}" data-field="productLineNo"/>
				<input style="width: 110px;" value="{{:PRODUCT_LINE}}" tabindex="-1" onclick="singleDict(this,'Y001')" readonly class="form-control input-sm" data-field="productLine" data-rules="required" data-id="{{:ITEM_ID}}" name="productLine_{{:ITEM_ID}}" id="productLine_{{:ITEM_ID}}">
	      </td>
	      <td>
				<input type="hidden" name="taxRateCode_{{:ITEM_ID}}" data-field="taxRateCode" value="{{:TAX_RATE_CODE}}"/>
	        	<select style="width: 70px;" data-rules="required" tabindex="-1" class="form-control input-sm taxRate" data-field="taxRate" data-value="{{:TAX_RATE}}" name="taxRate_{{:ITEM_ID}}" onchange="calcRowRate('{{:ITEM_ID}}',0)">
	        		<option value="">--</option>
	        		<option data-code="" value="0">0</option>
	        		<option data-code="SL02" value="1">1</option>
	        		<option data-code="SL03" value="1.5">1.5</option>
	        		<option data-code="SL04" value="3">3</option>
	        		<option data-code="SL05" value="5">5</option>
	        		<option data-code="SL06" value="6">6</option>
	        		<option data-code="SL08" value="9">9</option>
	        		<option data-code="SL11" value="13">13</option>
	        	</select>
	      </td>
	      <td>
	        	<input type="number" style="width: 70px;" value="{{:TAXES}}" tabindex="-1" {{if INVOICE_TYPE!='增值税专用发票'}} readonly{{/if}} onblur="onlyNumber(this);calcRAmount('{{:ITEM_ID}}')" data-field="taxes" class="form-control input-sm" name="taxes_{{:ITEM_ID}}"/>
	      </td>
	      <td>
	        	<input type="text" style="width: 70px;" readonly="readonly" tabindex="-1" class="form-control input-sm" readonly="readonly" data-field="rAmount" value="{{:R_AMOUNT}}" name="rAmount_{{:ITEM_ID}}"/>
	      </td>
  		</tr>
 {{/for}}
	
</script>


<script id="tpl1-files" type="text/x-jsrender">
	
</script>
<script>
	
	$(function(){
		renderDate('#flowForm');
	});
	
	var hasBxBudget = 0;
	
	var subjectList = {};
	
	var bxDeptList = {};
	
	var _itemId = 2000;
	function addTr(){
		_itemId++;
		var tmp = $.templates("#trItems");
		var html = tmp.render({ITEM_ID:_itemId});
		$('#itemTbody').append(html);
		bindDate(_itemId);
	}
	
	  function addManyTr(){
		  layer.prompt({value:1,title:'请输入新增的行数',offset:'20px'},function(value, index, elem){
			  layer.close(index);
			  for(var i=1;i<=value;i++){
				  addTr();
			  }
		  });
	  }
	
	function bindDate(id){
		layui.use('laydate', function(){
		  var laydate = layui.laydate;
		  laydate.render({max:0, min:-365,elem: "#bxDate_"+id});
	   });
	}
	
	
	function delTr(){
		layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
			layer.close(index);
			$("input[name='ids']:checked").each(function(){
				var id = this.value;
				if(id.length>20){
					ajax.remoteCall("${ctxPath}/servlet/bx?action=delItem",{itemId:id},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg);
							$('#item_'+id).remove();
							calcBxMoney();
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}else{
					$('#item_'+id).remove();
					calcBxMoney();
				}
			});
		});
	}
	
	function copyTr(){
		$("input[name='ids']:checked").each(function(){
			var id = this.value;
			var newObj = $('#item_'+id);
			addTr();
			
			var lastTr = $('#itemTbody tr').last();
			newObj.find('input,select').each(function(){
				var t = $(this);
				var field = t.attr('data-field');
				if(field!=undefined){
					lastTr.find("[data-field='"+field+"']").val(t.val());
				}
			});
			calcBxMoney();
		  }
		)
	}
	
	function calcRowRate(id,type){
		var val = $("[name='amount_"+id+"']").val();
		var invoiceType = $("[name='invoiceType_"+id+"']").val();
		var el = $("[name='taxRate_"+id+"']");
		var taxRate = el.val()||0;
		if(invoiceType==''){
			layer.msg('请先选择发票类型',{icon:7});
			el.val('0');
			taxRate = 0;
		}else if(invoiceType!='增值税专用发票'){
			el.val('0');
			if(type==0&&taxRate!=0){
				layer.msg('非增值税专用发票不可选税点',{icon:7,time:2000});
			}
			taxRate = 0;
		}
		
		var v2 = numDiv(taxRate,100)+0;
		var v3 = val/(1+parseFloat(v2));
		
		var code = el.find('option:selected').data('code'); 
		$("[name='taxRateCode_"+id+"']").val(code);
		
		
		$("[name='taxes_"+id+"']").val((val-v3).toFixed(2));
		$("[name='rAmount_"+id+"']").val(v3.toFixed(2));
	}
	
	function calcRAmount(id){
		var v1 = $("[name='amount_"+id+"']").val();
		var v2 = $("[name='taxes_"+id+"']").val();
		if(v1){
			v1 = Number(v1);
			v2 = Number(v2);
			if(v2>=v1){
				v2 = v1;
				$("[name='taxes_"+id+"']").val(v1);
			}
			$("[name='rAmount_"+id+"']").val((v1-v2).toFixed(2));
		}else{
			layer.msg('请先填写费用金额',{icon:7,offset:'20px'});
		}
	}
	
	function upTr(){
		var obj = $("input[name='ids']:checked");
		if(obj.length==1){
			var id = obj.val();
			var prevTR  = $('#item_'+id).prev();
			if (prevTR.length > 0) {
				prevTR.insertAfter($('#item_'+id));
			}
		}
	}
	function downTr(){
		var obj = $("input[name='ids']:checked");
		if(obj.length==1){
			var id = obj.val();
			var nextTR  = $('#item_'+id).next();
			if (nextTR.length > 0) {
				nextTR.insertBefore($('#item_'+id));
			}
		}
	}
	
	function showFeeInfo(el,itemId){
		var feeType = $("[name='feeType_"+itemId+"']").val();
		var isTravelFee  = '0';
		var height = '400px';
		if(feeType==''){
			layer.msg('请先选择费用类型',{icon:7});
			return;
		}
		if(feeType.indexOf('差旅费')>-1){
			isTravelFee = 1;
			height = '480px';
		}
		var data = {itemId:itemId,feeType:feeType,isTravelFee:isTravelFee};
		popup.layerShow({id:'showFeeInfo',full:fullShow(),scrollbar:false,shadeClose:false,title:'费用说明',area:['600px',height],offset:'30px',url:'/yq-work/pages/flow/bx/include/fee-edit.jsp',data:data});
	}
	
	function itemContract(el){
		var itemId = $(el).data('id');
		var feeType = $("[name='feeType_"+itemId+"']").val();
		var deptName = $("[name='deptName_"+itemId+"']").val();
		if(feeType&&deptName){
			selectBxContract(el);
		}else{
			if(deptName==''){
				layer.msg('请先选择费用部门',{icon:7});
			}else{
				layer.msg('请先选择费用类型',{icon:7});
			}
		}
	}
	
	function selectBxContract(el){
		var id = new Date().getTime();
		$(el).attr('data-sid',id);
		popup.layerShow({id:'selectBxContract',full:fullShow(),scrollbar:false,area:['650px','500px'],offset:'20px',title:'选择合同',url:'/yq-work/pages/flow/select/select-bx-contract.jsp',data:{sid:id,type:'radio'}});
	}

	
	function selctCallBack(sid,type){
		var el = $("[data-sid='"+sid+"']");
		var itemId = $(el).data('id');
		getSubject(itemId);
	}
	
	function descrOfVerificationFee(itemId){
		var newEl = $("[name='feeType_"+itemId+"']");
		var newfeeType = newEl.val();
		if(newfeeType!=''){
			var travelFee = newEl.attr('data-travel-fee');
			var feeType = newEl.attr('data-val');
			if(feeType){
				if(feeType.indexOf('差旅费')>-1){
					travelFee = 1;
				}else{
					travelFee = 2;
				}
			}
			if(travelFee=='1'&&newfeeType.indexOf('差旅费')==-1){
				$("[name='feeDesc_"+itemId+"']").val('');
				layer.msg('修改费用类型需重新填写费用说明',{icon:7});
			}
			if(travelFee=='2'&&newfeeType.indexOf('差旅费')>-1){
				$("[name='feeDesc_"+itemId+"']").val('');
				layer.msg('修改费用类型需重新填写费用说明',{icon:7});
			}
		}
	}
	
	function getBxDeptInfo(itemId){
		var deptName = $("[name='deptName_"+itemId+"']").val();
		if(deptName){
			if($.isEmptyObject(bxDeptList)){
				ajax.remoteCall("${ctxPath}/webcall?action=BxDao.selectDept&pageIndex=1&pageSize=9999",{pageType:3},function(result) { 
					if(result.state == 1){
						var list = result.data;
						for(var index in list){
							var row  = list[index];
							var _deptName = row.DEPT_NAME;
							bxDeptList[_deptName] = row; 
						}
						setVal(bxDeptList);
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}else{
				setVal(bxDeptList);
			}
		}
		function setVal(bxDeptList){
			var obj = bxDeptList[deptName];
			if(obj){
				$("[name='deptId_"+itemId+"']").val(obj.DEPT_ID);
				$("[name='deptCode_"+itemId+"']").val(obj.FINANCE_DEPT_CODE);
				$("[name='deptType_"+itemId+"']").val(obj.TEMP_NAME);
			}else{
				$("[name='deptName_"+itemId+"']").val('');
				layer.alert(deptName+'>部门名称不对',{icon: 5});
			}
		}
	}
	
	function getSubject(itemId){
		var deptType = $("[name='deptType_"+itemId+"']").val();
		var feeCode = $("[name='feeCode_"+itemId+"']").val();
		var contractState = $("[name='contractState_"+itemId+"']").val();
		if(deptType&&feeCode&&contractState){
			var key1 = deptType+feeCode+contractState;
			var key2 = deptType+feeCode+"A";
			if($.isEmptyObject(subjectList)){
				ajax.remoteCall("${ctxPath}/servlet/bx/conf?action=allSubject",{},function(result) { 
					if(result.state == 1){
						var list = result.data;
						for(var index in list){
							var row  = list[index];
							var subjectName = row.subjectName;
							var subjectCode = row.subjectCode;
							var deptType = row.deptType;
							var feeCode = row.feeCode;
							var projectState = row.projectState;
							subjectList[deptType+feeCode+projectState] = {subjectName:subjectName,subjectCode:subjectCode}; 
						}
						setVal(subjectList);
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}else{
				setVal(subjectList);
			}
		}
		
		function setVal(subjectList){
			var obj = subjectList[key1];
			if(obj==undefined){
				obj = subjectList[key2];
			}
			if(obj){
				$("[name='subjectNo_"+itemId+"']").val(obj.subjectCode);
				$("[name='subjectName_"+itemId+"']").val(obj.subjectName);
				$('.layer-foot button').removeAttr('disabled');
			}else{
				$('.layer-foot button').attr('disabled',true);
				layer.alert('请联系管理员设置会计科目',{icon: 5});
			}
		}
	}
	
	function synAllSubject(){
		$("input[name='ids']").each(function(){
			var id = this.value;
			getSubject(id);
		 }
	   )
	}
	
	function syncContract(){
		var el  = $('.inputItem').first();
		var v1 = el.find("[name^='contractNo']").val();
		var v2 = el.find("[name^='contractId']").val();
		var v3 = el.find("[name^='contractName']").val();
		var v4 = el.find("[name^='contractState']").val();
		var v5 = el.find("[name^='productLine_']").val();
		var v6 = el.find("[name^='productLineNo_']").val();
		$("[name^='contractNo']").val(v1);
		$("[name^='contractId']").val(v2);
		$("[name^='contractName']").val(v3);
		$("[name^='contractState']").val(v4);
		$("[name^='productLine_']").val(v5);
		$("[name^='productLineNo_']").val(v6);
		synAllSubject();
	}
	
	function syncFeeType(){
		var el  = $('.inputItem').first();
		var v1 = el.find("[name^='feeInCode']").val();
		var v2 = el.find("[name^='feeInType']").val();
		var v3 = el.find("[name^='feeCode']").val();
		var v4 = el.find("[name^='feeType']").val();
		$("[name^='feeInCode_']").val(v1);
		$("[name^='feeInType_']").val(v2);
		$("[name^='feeCode_']").val(v3);
		$("[name^='feeType_']").val(v4);
		synAllSubject();
	}
	
	function syncFeeDesc(){
		var el  = $('.inputItem').first();
		var v1 = el.find("[name^='feeJson']").val();
		var v2 = el.find("[name^='feeDesc']").val();
		var v3 = el.find("[name^='invoiceNo']").val();
		var v4 = el.find("[name^='invoiceType']").val();
		if(v1==''){
			layer.msg('请先对一行的费用说明点击编辑确认后再同步');
			return;
		}
		$("[name^='feeJson_']").val(v1);
		$("[name^='feeDesc_']").val(v2);
		$("[name^='invoiceNo_']").val(v3);
		$("[name^='invoiceType_']").val(v4);
	}
	
	function syncDept(){
		var el  = $('.inputItem').first();
		var v1 = el.find("[name^='deptId']").val();
		var v2 = el.find("[name^='deptName']").val();
		var v3 = el.find("[name^='deptType']").val();
		var v4 = el.find("[name^='deptCode']").val();
		$("[name^='deptId_']").val(v1);
		$("[name^='deptName_']").val(v2);
		$("[name^='deptType_']").val(v3);
		$("[name^='deptCode_']").val(v4);
		synAllSubject();
	}

	function syncDate(){
		var el  = $('.inputItem').first();
		var v1 = el.find("[name^='bxDate']").val();
		$("[name^='bxDate_']").val(v1)
	}
	
     function onlyNumber(obj){
		obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"和"."以外的字符
		obj.value = obj.value.replace(/^\./g,"");//验证第一个字符是数字而不是字符
		obj.value = obj.value.replace(/\.{2,}/g,".");//只保留第一个.清除多余的
		obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
		obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
     }
	
	function selectFeeType(el,selectType){
		var id = new Date().getTime();
		$(el).attr('data-sid',id);
		var type = 'radio';
		let unclick = $(el).attr('data-unclick');
		if(unclick=='1'){
			return;
		}
		popup.layerShow({id:'selectFeeType',full:fullShow(),scrollbar:false,area:['450px','500px'],offset:'20px',title:'选择费用类型',url:'/yq-work/pages/flow/select/select-fee-type.jsp',data:{sid:id,type:type,selectType:selectType}});
	}

	function getAllFeeType(){
		var el = $("[data-field='feeInType']");
		var array = [];
		el.each(function(){
			var t = $(this);
			var val = t.val();
			if(val){
				if(val.indexOf('差旅费')>-1){
					array.push('差旅费');
				}else{
					array.push(val);
				}
			}
		});
		array = arrayUnique(array);
		var feeType = array.join();
		var date = new Date();
		var str  = date .getFullYear()+"年"+(date .getMonth()+1)+"月"+feeType;
		if(feeType==''){
			layer.msg('请先填写费用明细',{icon:7});
			return;
		}
		$("[name='apply.applyRemark']").val(str);
	}

	</script>


