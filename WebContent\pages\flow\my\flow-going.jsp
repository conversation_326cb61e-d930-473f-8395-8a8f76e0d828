<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>在途流程</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input type="hidden" name="flowCode" value="${flowCode}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程名称</span>
						 	<input class="form-control input-sm" name="flowName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">待办人</span>
						 	<input class="form-control input-sm" name="checkName">
						 </div>
						 <div class="input-group input-group-sm hidden">
						    <span class="input-group-addon">状态</span>	
							<select name="applyState" onchange="queryData();" class="form-control input-sm">
							      <option value="">请选择 </option>
                    			  <option data-class="label label-default" value="0">草稿</option>
                    			  <option data-class="label label-warning" value="1">作废</option>
                    			  <option data-class="label label-warning" value="5">已挂起</option>
							      <option data-class="label label-warning" value="10">待审批</option>
                    			  <option data-class="label label-info" value="20">审批中</option>
                    			  <option data-class="label label-danger" value="21">审批退回</option>
                    			  <option data-class="label label-success" value="30">已完成</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>
		  </script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'FlowDao.flowGoingList',
					id:'flowTable',
					page:true,
					rowDoubleEvent:'flowDetail',
					height:'full-90',
					limit:50,
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						field:'',
						title:'操作',
						width:50,
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					},{
						 field:'APPLY_NO',
						 title:'编号',
						 width:220,
						 templet:function(row){
							 var readTime = row['READ_TIME'];
							 var html = '';
							 if(readTime==''){
								 html = '<span class="layui-badge-dot"></span>';
							 }
							 return html + row['APPLY_NO']+"_"+row['FLOW_NAME'];
						 }
					 },{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:200
					 },{
						field:'NODE_NAME',
						title:'当前节点',
						width:80
					},{
						field:'CHECK_NAME',
						title:'待办人员',
						width:80,
						templet:function(row){
							return row['CHECK_NAME'];
						}
					},{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					},{
						field:'HAS_NODE_NAME',
						title:'已办节点',
						width:80
					},{
						field:'NODE_NAME',
						title:'待办节点',
						width:80
					},{
						field:'HAS_CHECK_TIME',
						title:'最近办理时间',
						width:140
					},{
						field:'',
						title:'已过去时间',
						width:90,
						templet:function(row){
							return twoTimeInterval(row.HAS_CHECK_TIME,'');
						}
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function flowDetail(data){
			var flowCode = data['FLOW_CODE'];
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,resultId:data['CURRENT_RESULT_ID']});
			var url = '${ctxPath}/web/flow';
			if(self==top){
				location.href = url+"?"+jQuery.param(json);				
			}else{
				popup.openTab({id:'flowDetail',title:'流程详情',url:url,data:json});
			}
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>