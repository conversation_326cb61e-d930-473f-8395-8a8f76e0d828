package com.yunqu.work.servlet.flow;

import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.ApproveResultRecord;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.BxService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.utils.FlowUtils;

@WebServlet("/servlet/flow/approve/*")
public class ApproveServlet extends BaseFlowServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForDoApprove() {
		EasyQuery query = this.getQuery();
		String msg = "操作成功";
		try {
		
			JSONObject params = getJSONObject();
			String businessId = params.getString("businessId");
			String resultId = params.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("审批人不存在");
			}
			
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result==null) {
				return EasyResult.fail("数据不存在-1401");
			}
			
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);
			if(apply==null) {
				return EasyResult.fail("数据不存在-1402");
			}
			//1 通过 2拒绝
			int checkResult = params.getIntValue("checkResult");
			String checkDesc = params.getString("checkDesc");
			if(checkResult==0) {
				checkResult = 1;
			}
			if(StringUtils.isBlank(checkDesc)) {
				checkDesc = "同意";
			}
			EasyRecord record = getApproveResultRecord(resultId,checkResult,checkDesc);
			
			//协办
			if(result.getApproveType()==FlowConstants.APPROVE_TYPE_ASSIST) {
				this.getQuery().update(record);
				return EasyResult.ok();
			}
			
			query.begin();
			List<String> ccIds = addCCLog(query,params.getString("ccIds"),businessId,resultId);
			int ccCount = ccIds.size();
			record.set("cc_count", ccCount);
			
			FlowModel flow = apply.getFlow();
			//获取下一级节点
			ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
			String nodeId = approveNode.getNodeId();
			int nowSignFlag = approveNode.getSignFlag();
			
			EasyResult approveNextNodeResult = getApproveNextNodeId(approveNode.getNextNodeId(),apply,params);
			if(!approveNextNodeResult.isOk()) {
				return approveNextNodeResult;
			}
			String nextNodeId =  approveNextNodeResult.getData().toString();
			
			MessageModel msgModel = null;
			
			boolean go = true;
			if(nowSignFlag==1) {
				String prevResultId = result.getPrevResultId();
				if(checkResult==1) {
					int todoCount = this.getQuery().queryForInt("select count(1) from yq_flow_approve_result where prev_result_id = ? and check_result = 0 and result_id <> ?", prevResultId,resultId);
					if(todoCount>0) {
						go = false;
					}
				}else if(checkResult==2){
					query.executeUpdate("update yq_flow_approve_result set check_result = -1 where prev_result_id = ? and check_result = 0 and result_id <> ?",prevResultId,resultId);
				}
			}
			if(go) {
				if(checkResult==2) {
					FlowService.getService().updateApplyReturn(query,businessId);
					msgModel = getMessageModel(apply,apply.getApplyBy(),"审批退回","退回原因："+checkDesc);
				}else {
					if("0".equals(nextNodeId)) {
						msg = "审批完成,流程结束";
						msgModel = getMessageModel(apply,apply.getApplyBy(),"审批完成",checkDesc);
						FlowService.getService().updateApplyEnd(query,businessId,nodeId,resultId);
					}else {
						ApproveNodeModel nextNodeInfo = ApproveService.getService().getNodeInfoByNodeId(nextNodeId,apply,params);
						if(nextNodeInfo==null) {
							return EasyResult.fail("获取不到下一级审批,请联系管理员");
						}

						boolean repeatCheckSkip =  false;
						//重复审批跳过
						if(flow.getRepeatCheckSkip()==1) {
							//repeatCheckSkip =  this.getQuery().queryForExist("select count(1) from yq_flow_approve_result where business_id = ? and check_result = 1 and check_user_id = ?", businessId,nextNodeInfo.getCheckBy());
						}
						
						boolean isFinish = false;
						//审批人是申请人，下一个审批人等于当前审批人 ||todoCheckUserId.indexOf(getUserId())>-1
						String todoCheckUserId = nextNodeInfo.getCheckBy();
						if(StringUtils.notBlank(todoCheckUserId)&&(todoCheckUserId.indexOf(apply.getApplyBy())>-1)) {
							repeatCheckSkip = true;
						}
						
						if(repeatCheckSkip) {
							nextNodeId = nextNodeInfo.getNextNodeId();
							if("0".equals(nextNodeId)) {
								isFinish = true;
								msgModel = getMessageModel(apply,apply.getApplyBy(),"审批完成",checkDesc);
								FlowService.getService().updateApplyEnd(query,businessId,nodeId,resultId);
							}else {
								ApproveNodeModel _nextNodeInfo = ApproveService.getService().getNodeInfoByNodeId(nextNodeId,apply,params);
								if(_nextNodeInfo.getCheckType()!=FlowConstants.FLOW_APPROVER_SELECT_USER) {
									nextNodeInfo = _nextNodeInfo;
								}
								
								//相邻审批跳过
							    if(flow.getXlCheckSkip()==1) {
									if(nextNodeInfo.getCheckBy().indexOf(getUserId())>-1) {
										nextNodeId = nextNodeInfo.getNextNodeId();
										if("0".equals(nextNodeId)) {
											isFinish = true;
											msgModel = getMessageModel(apply,apply.getApplyBy(),"审批完成",checkDesc);
											FlowService.getService().updateApplyEnd(query,businessId,nodeId,resultId);
										}else {
											_nextNodeInfo = ApproveService.getService().getNodeInfoByNodeId(nextNodeId,apply,params);
											if(_nextNodeInfo.getCheckType()!=FlowConstants.FLOW_APPROVER_SELECT_USER) {
												nextNodeInfo = _nextNodeInfo;
											}
										}
									}
								}
							}
						}
						
						if(!isFinish) {
							msg = "提交成功,审批下一个节点:"+nextNodeInfo.getNodeName();
							
							String nextResultId = RandomKit.uniqueStr();
							
							int signFlag = nextNodeInfo.getSignFlag();
							if(signFlag==1) {
								String checkBys = nextNodeInfo.getCheckBy();
								String[] checkIds = checkBys.split(",");
								int i= 0 ;
								for(String checkBy:checkIds) {
									ApproveResultRecord resultRecord = getApproveResultRecord(nextNodeInfo, businessId, nextResultId,resultId);
									resultRecord.setCheckUserId(checkBy);
									resultRecord.setCheckName(StaffService.getService().getUserName(checkBy));
									if(i>0) {
										resultRecord.setResultId(RandomKit.uniqueStr());
									}
									this.saveApproveNode(resultRecord,apply.getFlowCode(),query);
									i++;
								}
							}else {
								this.saveApproveResultRecord(nextNodeInfo, apply,query,nextResultId, resultId);
							}
							
							FlowService.getService().updateApplyInfo(query,businessId, resultId,nextResultId,nodeId,nextNodeId, 20);
							msgModel = getMessageModel(apply,nextNodeInfo.getCheckBy(),null,checkDesc);
						}
						
					}
				}
				
			}
			this.updateApplyConf(query,businessId,params);
			query.update(record);
			query.commit();
			this.sendAllMsg(msgModel);
			if(ccCount>0) {
				this.sendCCMsg(apply,ccIds);
			}
			this.sendToApplyMsgModel(businessId,checkResult);
		} catch (SQLException e) {
			this.error(null, e);
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(null, ex);
			}
			return EasyResult.fail("如错误继续请联系管理员："+e.getMessage());
			
		}catch (Exception e) {
			this.error(null, e);
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(null, ex);
			}
			return EasyResult.fail("如错误继续请联系管理员："+e.getMessage());
		}
		return EasyResult.ok(null,msg);
	}
	
	
	public EasyResult actionForUpdateApply() {
		return updateApply();
	}
	

	
	/**
	 *提交流程
	 */
	public EasyResult actionForSubmitApply() {
		return submitApply();
	}
	
	private void sendCCMsg(FlowApplyModel apply,List<String> userIds) {
		this.sendFeedback(apply.getApplyId(),userIds.stream().collect(Collectors.joining(",")),"新的流程抄送");
		this.updateFlowCCNum(apply.getApplyId());
	}
	
	public EasyResult actionForAddReadTime() {
		JSONObject params = getJSONObject();
		String resultId = params.getString("resultId");
		try {
			this.getQuery().executeUpdate("update yq_flow_approve_result set read_time = ? where result_id = ? and read_time = ''", EasyDate.getCurrentDateString(),resultId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForGetStartNode() throws SQLException {
		JSONObject params = getJSONObject();
		String flowCode = params.getString("flowCode");
		JSONObject result = new JSONObject();
		result.put("nowResult", new ApproveResultModel());
		
		ApproveNodeModel approveNodeModel = new ApproveNodeModel();
		approveNodeModel.setNodeName("发起人提交");
		result.put("currentNode",approveNodeModel);
		
		ApproveNodeModel nextNode = ApproveService.getService().getStartNode(new FlowApplyModel(flowCode, getUserId()), params);
		if(nextNode==null) {
			result.put("nextNode", new ApproveNodeModel());
		}else {
			result.put("nextNode",nextNode );
		}
		return EasyResult.ok(result);
	}
	
	@SuppressWarnings("unused")
	public EasyResult actionForGetNodeInfoByResultId() throws SQLException {
		JSONObject params = getJSONObject();
		String businessId = params.getString("businessId");
		String resultId = params.getString("resultId");
		
		if(StringUtils.isBlank(resultId)) {
			return EasyResult.fail("resultId is not empty.");
		}
		
		JSONObject result = new JSONObject();
		
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		String flowCode = apply.getFlowCode();
		ApproveNodeModel approveNode = null;
		
		boolean isBxFlow = FlowUtils.isBxLoanFlow(flowCode);
		if(isBxFlow) {
			approveNode = BxService.getService().getNodeByResultId(resultId);
		}else {
			approveNode = ApproveService.getService().getNodeByResultId(resultId);
		}
		
		String nextNodeId =  approveNode.getNextNodeId();
		
		if(approveNode!=null) {
			result.put("currentNode", approveNode);
			ApproveResultModel approveResult = ApproveService.getService().getResult(resultId);
			result.put("nowResult", approveResult);
			if("0".equals(approveNode.getNextNodeId())) {
				result.put("nextNode", new ApproveNodeModel());
			}else {
				ApproveNodeModel nextNodeInfo = null;
				if(isBxFlow) {
					nextNodeInfo = BxService.getService().getNodeInfo(nextNodeId, flowCode,businessId,params);
				}else {
					nextNodeInfo = ApproveService.getService().getNodeInfoByNodeId(nextNodeId,apply,params);
				}
				if(nextNodeInfo==null) {
					result.put("nextNode", new ApproveNodeModel());
				}else {
					result.put("nextNode", nextNodeInfo);
				}
			}
		}else {
			result.put("nowResult", new ApproveResultModel());
			result.put("currentNode",new ApproveNodeModel());
			result.put("nextNode", new ApproveNodeModel());
		}
		return EasyResult.ok(result);
	}
}
