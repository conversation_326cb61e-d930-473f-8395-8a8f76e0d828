package com.yunqu.work.dao.ehr;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="EhrDao")
public class EhrDao extends AppDaoContext {
	@WebControl(name="dict",type=Types.DICT)
	public  JSONObject dict(){
		return getDictById(this.getMethodParam(0).toString());
	}
	@WebControl(name="allDept",type=Types.DICT)
	public JSONObject allDept(){
		setQuery(getMainQuery());
		return getDictByQuery("select DEPT_ID,DEPT_NAME from easi_dept where LENGTH(DEPT_CODE)=6");
	}
	
	@WebControl(name="selectDept",type=Types.PAGE)
	public JSONObject selectDept(){
		setQuery(getMainQuery());
		EasySQL sql = new EasySQL();
		sql.append("select DEPT_ID,DEPT_NAME from easi_dept where LENGTH(DEPT_CODE)=6");
		sql.appendLike(param.getString("deptName"),"and DEPT_NAME like ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 全部人员
	 * @return
	 */
	@WebControl(name="userList",type=Types.LIST)
	public JSONObject userList(){
		EasySQL sql=getEasySQL("select * from easi_user where 1=1");
		sql.append("and state = 0");
		sql.appendLike(param.getString("userName"),"and USERNAME like ?");
		sql.append("order by DATA1");
		setQuery(getMainQuery());
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="addresslist",type=Types.LIST)
	public JSONObject addresslist(){
		String deptCode=param.getString("deptCode");
		EasySQL sql=getEasySQL("select t1.*,t4.LAST_LOGIN_TIME,t5.JOB_TITLE,t5.STAFF_NO from easi_user t1");
		sql.append("inner join yq_staff_info t5 on t5.staff_user_id = t1.user_id");
		sql.append("inner join easi_user_login t4 on t4.user_id = t1.user_id");
		if(StringUtils.notBlank(deptCode)){
			sql.append("inner join easi_dept_user t2 on t2.user_id = t1.user_id");
			sql.append("inner join easi_dept t3 on t3.dept_id = t2.dept_id");
		}
		sql.append(" where  t1.STATE = 0");
		if(StringUtils.notBlank(deptCode)){
			sql.appendLike(deptCode,"and t3.DEPT_CODE like ?");
		}
		setQuery(ServerContext.getAdminQuery());
		sql.appendLike(param.getString("mobile"), "and t1.MOBILE  like ?");
		sql.appendLike(param.getString("userName"), "and t1.USERNAME  like ?");
		sql.appendLike(param.getString("depts"), "and t1.DEPTS  like ?");
		sql.append("order by t1.DATA1 asc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}

}
