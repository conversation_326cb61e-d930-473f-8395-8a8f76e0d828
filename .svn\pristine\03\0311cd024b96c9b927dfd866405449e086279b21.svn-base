<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>未打卡补签申请</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <input type="hidden" name="apply.data11" id="startDate"/>
			   <input type="hidden" name="apply.data12" id="endDate"/>
			   <input type="hidden" name="apply.data13" value="1"/>
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" data-template="({{:data2}}{{:data14}})未打卡补签申请单" value="${staffInfo.userName}${staffInfo.staffNo}未打卡补签申请单"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">漏打卡日期</td>
					  			<td>
					  				<input type="hidden" value="${param.kqId}" name="apply.data3"/>
					  				<input type="text" data-rules="required" value="${param.kdDate}" data-laydate="{type:'date',max:0}" class="form-control input-sm Wdate" name="apply.data2"/>
					  			</td>
					  			<td class="required">班次类别</td>
					  			<td>
					  				<select class="form-control input-sm" data-rules="required" name="apply.data14">
					  					<option value="">--</option>
					  					<option value="上班">上班</option>
					  					<option value="下班">下班</option>
					  					<option value="整天">整天</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">补签原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark">忘打卡了</textarea>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideHistoryApply:false,success:function(data){
				Flow.initData();
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			var type = $("[name='apply.data14']").val();
			var date = $("[name='apply.data2']").val();
			if(type=='上班'){
				$('#startDate').val(date+" 09:00");
				$('#endDate').val(date+" 12:00");
			}else if(type=='下班'){
				$('#startDate').val(date+" 13:30");
				$('#endDate').val(date+" 18:00");
			}else{
				$('#startDate').val(date+" 09:00");
				$('#endDate').val(date+" 18:00");
			}
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=setFlowId",{flowId:id,kqId:'${param.kqId}'},function() { 
					
				});
			}});
		}
		
		Flow.initData = function() {
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			
			if(nodeCode=='部门主管'){
				var applyUserInfo = FlowCore.applyUserInfo;
				if(applyUserInfo.workCity=='北京'){
					params['ccNames'] = '蔡洁';
				}
			}
			FlowCore.approveData = params;
		}
		
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>