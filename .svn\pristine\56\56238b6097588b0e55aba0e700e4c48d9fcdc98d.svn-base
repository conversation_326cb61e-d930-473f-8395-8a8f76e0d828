<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>菜单</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="FoodDao.foodObj" autocomplete="off" data-mars-prefix="food.">
     		   <input type="hidden" value="${param.foodId}" name="food.ID"/>
						<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td style="width: 80px" class="required">用餐类型</td>
				                    <td>
				                    	<label class="radio radio-success radio-inline">
					                  	    <input type="radio"  checked="checked" value="2" name="dinnerType"/> <span>中餐</span>
				                    	</label>
				                    	<label class="radio radio-success radio-inline">
					                    	<input type="radio" value="3" name="dinnerType"/> <span>晚餐</span>
				                    	</label>
				                    </td>
					            </tr>
					            
					            <tr>
				                    <td style="width: 80px" class="required">日期</td>
				                    <td>
				                    	<input class="form-control input-sm Wdate"  data-mars="CommonDao.date02"  onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" data-rules="required"  name="date">
				                    </td>
					            </tr>
					            <tr id="meals">
				                    <td style="width: 80px" class="required">菜单名称</td>
				                    <td>
				                    	<select class="form-control input-sm" data-rules="required" data-mars="FoodDao.mealsDict" name="FOOD_ID">
		                    				<option value="">请选择</option>
		                    			</select>
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required">用户</td>
				                    <td>
				                    	<select class="form-control input-sm" data-rules="required" data-mars="CommonDao.userDict" name="userId">
		                    				<option value="">请选择</option>
		                    			</select>
				                    </td>
					            </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c">
						    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		
		$(function(){
			$("#editForm").render({success:function(){
				requreLib.setplugs('select2,wdate',function(){
					$("#editForm select").select2({theme: "bootstrap",placeholder:'请选择'});
				});
			}});
			$("#editForm input[type='radio']").change(function(){
				var t=$(this);
				$("#meals").render({data:{dinnerType:t.val()},loading:false});
			});
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				Edit.updateData(); 
			};
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/food?action=todayBookFood",{bookType:'2',DINNER_TYPE:$("#editForm input[name='dinnerType']:checked").val(),ID:$("#editForm select[name='FOOD_ID']").val(),userId:$("#editForm select[name='userId']").val(),dcDate:$("#editForm input[name='date']").val()},function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						$("[name='userId']").val('').select2({theme: "bootstrap",placeholder:'请选择'});
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		};

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>