package com.yunqu.work.dao.other;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="UserNavDao")
public class UserNavDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		EasyRecord model=new EasyRecord("YQ_USER_NAV","NAV_ID");
		model.setPrimaryValues(param.getString("navId"));
		return queryForRecord(model);
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		return queryForList("select * from yq_user_nav where user_id = ?", new Object[]{getUserId()});
	}

}
