package com.yunqu.work.servlet.erp;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/supplier/*")
public class SupplierServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_erp_supplier","supplier_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	public EasyResult actionForAdd(){
		EasyRecord model = getModel("supplier");
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_NAME", getUserName());
		String id = null;
		try {
			String _id = this.getQuery().queryForString("select max(supplier_id) from yq_erp_supplier");
			id = String.valueOf(Integer.valueOf(_id)+1);
			model.setPrimaryValues(id);
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(id);
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("supplier");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForAddProduct(){
		EasyRecord model = new EasyRecord("yq_erp_product","product_id");
		model.setColumns(getJSONObject("product"));
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		model.set("CREATE_USER_NAME", getUserName());
		model.setPrimaryValues(RandomKit.uniqueStr());
		try {
			this.getQuery().save(model);
			this.updateProductCount();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void updateProductCount() {
		try {
			this.getQuery().executeUpdate("update yq_erp_supplier t1 set t1.product_count = (select count(1) from yq_erp_product t2 where t2.supplier_id = t1.supplier_id)");
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}
	
	public EasyResult actionForUpdateProduct(){
		EasyRecord model = new EasyRecord("yq_erp_product","product_id");
		model.setColumns(getJSONObject("product"));
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForDel(){
		try {
			this.getQuery().executeUpdate("delete from yq_erp_supplier where supplier_id = ?", getJsonPara("invoiceId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

	public EasyResult actionForGetSupplierName(){
		String id = getJsonPara("id");
		try {
			String val = this.getQuery().queryForString("select name from yq_erp_supplier where supplier_id = '"+id+"'");
			return EasyResult.ok(val);
		} catch (SQLException e) {
			return EasyResult.fail(e.getMessage());
		}
	}

}
