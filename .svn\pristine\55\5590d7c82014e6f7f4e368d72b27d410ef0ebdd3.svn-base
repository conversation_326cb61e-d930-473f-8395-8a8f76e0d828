package com.yunqu.work.service;

import com.yunqu.work.base.AppBaseService;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;


public class IncomeConfirmService extends AppBaseService {
    private static class Holder {
        private static IncomeConfirmService service = new IncomeConfirmService();
    }

    public static IncomeConfirmService getService() {
        return Holder.service;
    }

    public void setConfirmInfoForIncomeStage(String incomeStageId, String confirmType) {
        try {
            if (StringUtils.isBlank(incomeStageId) || StringUtils.isBlank(confirmType)) {
                this.getLogger().error("incomeStageId 或 confirmType 为空，导致收入确认阶段未更新金额！", null);
                return;
            }

            EasyRow incomeStage = this.getQuery().queryForRow("select * from yq_contract_income_stage where INCOME_STAGE_ID = ?", incomeStageId);
            String stageAmount = incomeStage.getColumnValue("PLAN_AMOUNT");
            String stageAmountNoTax = incomeStage.getColumnValue("AMOUNT_NO_TAX");
            String confirmAmount = this.getQuery().queryForString("select IFNULL(sum(AMOUNT_WITH_TAX),0) from yq_contract_income_confirm where INCOME_STAGE_ID = ? and CONFIRM_TYPE = ?", new Object[]{incomeStageId, confirmType});
            String confirmAmountNoTax = this.getQuery().queryForString("select IFNULL(sum(AMOUNT_NO_TAX),0) from yq_contract_income_confirm where INCOME_STAGE_ID = ? and CONFIRM_TYPE = ?", new Object[]{incomeStageId, confirmType});
            if (StringUtils.isBlank(confirmAmount)) {
                confirmAmount = "0";
            }
            if (StringUtils.isBlank(confirmAmountNoTax)) {
                confirmAmountNoTax = "0";
            }
            Double unconfirmAmount = Double.parseDouble(stageAmount) - Double.parseDouble(confirmAmount);
            Double unconfirmAmountNoTax = Double.parseDouble(stageAmountNoTax) - Double.parseDouble(confirmAmountNoTax);

            EasySQL sql = new EasySQL("UPDATE yq_contract_income_stage ");
            if ("A".equals(confirmType)) {
                sql.append(unconfirmAmount, "SET UNCONFIRM_AMOUNT_A =  ?");
                sql.append(unconfirmAmountNoTax, ", UNCONFIRM_NO_TAX_A =  ?");
            } else if ("B".equals(confirmType)) {
                sql.append(unconfirmAmount, "SET UNCONFIRM_AMOUNT_B =  ?");
                sql.append(unconfirmAmountNoTax, ", UNCONFIRM_NO_TAX_B =  ?");
            }
            sql.append(incomeStageId, "WHERE INCOME_STAGE_ID = ?");

            this.getQuery().executeUpdate(sql.getSQL(), sql.getParams());

            if ("A".equals(confirmType)) {
                setCompleteTime(incomeStageId);
            }
        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
        }
    }

    /*
     * 当收入确认A的金额到达收入确认阶段的计划金额
     * 给收入确认阶段、合同阶段(暂时删除)加上完成日期（以最晚的收入确认A日期为完成日期）
     */
    public void setCompleteTime(String incomeStageId) {
        //首先检查unConfirm是否为0
        try {
            EasyRow row = this.getQuery().queryForRow("select * from yq_contract_income_stage where INCOME_STAGE_ID = ? ", new Object[]{incomeStageId});
            String amount = row.getColumnValue("UNCONFIRM_AMOUNT_A");
            String contractStageIds = row.getColumnValue("CONTRACT_STAGE_ID");
            String writeMode = row.getColumnValue("WRITE_MODE");

            if ("0.00".equals(amount)) {
                String confirmDate = this.getQuery().queryForString("select CONFIRM_DATE from yq_contract_income_confirm where INCOME_STAGE_ID = ? and CONFIRM_TYPE = ? ORDER BY CONFIRM_DATE DESC LIMIT 1", new Object[]{incomeStageId, "A"});
                setCompleteDateForIncomeStage(incomeStageId, confirmDate);
                //合同阶段对应模式，还需要为 contract_stage加上完成日期
//                    if ("1".equals(writeMode)) {
//                        setCompleteDateForContractStage(contractStageIds,confirmDate);
//                    }
            } else {
                removeCompleteDateForIncomeStage(incomeStageId);
                //合同阶段对应模式，还需要去掉contract_stage完成时间
//                    if ("1".equals(writeMode)) {
//                        removeCompleteDateForContractStage(contractStageIds);
//                    }
            }

        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
        }
    }

    public void setCompleteDateForIncomeStage(String incomeStageId, String confirmDate) {
        String sql = "UPDATE yq_contract_income_stage SET ACT_COMP_DATE = ? ,INCOME_CONF_FLAG = 1 WHERE INCOME_STAGE_ID = ?";
        try {
            this.getQuery().executeUpdate(sql, new Object[]{confirmDate, incomeStageId});
        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
        }
    }


    public void removeCompleteDateForIncomeStage(String incomeStageId) {
        String sql = "UPDATE yq_contract_income_stage SET ACT_COMP_DATE = '' , INCOME_CONF_FLAG = 0 WHERE INCOME_STAGE_ID = ?";
        try {
            this.getQuery().executeUpdate(sql, incomeStageId);
        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
        }
    }


    public void removeCompleteDateForContractStage(String contractStageIds) {
        String[] contractStages = contractStageIds.split(",");
        for (String contractStageId : contractStages) {
            String sql = "UPDATE yq_contract_stage SET ACT_COMP_DATE = '' WHERE STAGE_ID = ?";
            try {
                this.getQuery().executeUpdate(sql, contractStageId);
            } catch (SQLException e) {
                this.getLogger().error(e.getMessage(), e);
            }
        }
    }

    public void setCompleteDateForContractStage(String contractStageIds, String confirmDate) {
        String[] contractStages = contractStageIds.split(",");
        for (String contractStageId : contractStages) {
            String sql = "UPDATE yq_contract_stage SET ACT_COMP_DATE = ? WHERE STAGE_ID = ?";
            try {
                this.getQuery().executeUpdate(sql, new Object[]{confirmDate, contractStageId});
            } catch (SQLException e) {
                this.getLogger().error(e.getMessage(), e);
            }
        }
    }


}
