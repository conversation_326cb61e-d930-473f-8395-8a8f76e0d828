package com.yunqu.work.base;

import java.security.Principal;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.db.EasyQuery;
public abstract class AppBaseService{ 
	
	protected HttpServletRequest request;
	
	protected HttpServletRequest getRequest() {
		return this.request;
	}
	
	protected UserPrincipal  getUserPrincipal(){
		Principal principal = request.getUserPrincipal();
		return (UserPrincipal)principal; 
	}
	
	private String getAppDatasourceName() {
		return Constants.DS_NAME;
	}

	private String getAppName() {
		return Constants.APP_NAME;
	}

	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	protected EasyQuery getQuery(int convertField) {
		EasyQuery query =  this.getQuery();
		query.setConvertField(convertField);
		return query;
	}
	
	public EasyQuery getQuery(){
		return EasyQuery.getQuery(getAppName(), getAppDatasourceName());
	}
	public EasyQuery getMainQuery(){
		return EasyQuery.getQuery(getAppName(), Constants.MARS_DS_NAME);
	}
	public EasyQuery getZentaoQuery(){
		return EasyQuery.getQuery(getAppName(), Constants.ZENTAO_DS_NAME);
	}
	
	public Logger getLogger(){
		return LogEngine.getLogger(getAppName(),getLoggerName());
	}
	
}
