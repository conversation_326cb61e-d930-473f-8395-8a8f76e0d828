
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
	    .layui-table-tool {border: 1px solid #a9b7c1!important;}
		.layui-icon{font-size: 12px;}
		.updateHzAmount{display: none;}
		.ibox-content{min-height: calc(100vh - 70px)}
		.titleInfo td{
			height:24px!important;line-height: 20px!important;padding: 3px 4px!important;font-size: 14px!important;color: #444444;
		}
		
		.approveResult td,.approveResult th{
			border:none;
			border-bottom: 1px dashed #ccc!important;
			padding: 10px 6px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
				 		<div class="flow-title">${flow.flowTitle}</div>
			 			<div class="flow-approval-btn">
				 			<EasyTag:res resId="HZ_BX_MONEY">
									<button class="btn btn-warning btn-sm" onclick="$('.updateHzAmount').show();$('.hzAmountVal').hide();" type="button">核准金额</button>
				  			</EasyTag:res>
				 		</div>
				 		<table class="table table-edit table-vzebra edit-remove titleInfo">
					  		<tr>
					  			<td style="width: 100px;">当前处理人：</td>
					  			<td style="width: 38%;">
					  				<span name="apply.checkName"></span>
					  			</td>
					  			<td style="width: 100px;">申请时间：</td>
					  			<td>
					  				<span name="apply.applyTime"></span>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td style="width: 100px;">当前状态：</td>
					  			<td>
					  				<span name="apply.nodeName"></span>
					  			</td>
					  			<td style="width: 100px;">单号：</td>
					  			<td>
					  				<span name="apply.applyNo"></span>
					  			</td>
					  		</tr>
					   </table>
				    	<table class="table table-vzebra flow-table mt-10">
					  		<tr>
					  			<td style="width: 120px;" class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  				<span class="edit-remove" name="apply.applyStaffNo"></span>
					  			</td>
					  			<td style="width: 120px;" class="required">申请部门</td>
					  			<td style="width: 38%;">
					  				<input type="hidden" value="${staffInfo.deptId}" name="business.BX_DEPT_ID">
					  				<input type="text" data-rules="required" name="business.BX_DEPT_NAME" class="form-control input-sm" value="${staffInfo.deptName}" onclick="selectBxDept(this)">
					  			</td>
					  		</tr>
					  		<tr class="unedit-remove">
					  			<td style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的借款申请" name="apply.applyTitle"/>
					  			</td>
					  			<td style="width: 120px;">单号</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" data-mars="BxDao.yqBxNo" name="apply.applyNo"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>付款方式</td>
					  			<td colspan="3">
					  				<select data-rules="required" class="form-control input-sm" data-mapping="data3" id="payType" name="business.PAY_TYPE" onchange="selectPaymentType(this.value)">
					  					<option value="个人付款">个人付款</option>
					  					<option value="对公付款">对公付款</option>
					  				</select>
					  			</td>
					  			<!-- <td>借款余额</td>
					  			<td>
					  				<div class="edit-remove">
						  				账面：<span name="business.ZM_MONEY">0</span>，在途：<span name="business.ZT_MONEY">0</span>
					  				</div>
					  				<div class="unedit-remove">
						  				账面：<span>0</span>，在途：<span>0</span><input name="business.ZM_MONEY" type="hidden"/><input name="business.ZT_MONEY" type="hidden"/>
					  				</div>
					  			</td> -->
					  		</tr>
					  		<tr class="companyPaymentInfo" style="display: none;">
					  			<td>公司名</td>
					  			<td>
								 	<input type="hidden" name="business.CONTACT_CODE" data-mapping="data5">
								 	<input class="form-control input-sm" readonly="readonly" type="text" data-mapping="data4" onclick="selectContactUnit(this);" placeholder="点击选择" name="business.CONTACT_UNIT">
					  			</td>
					  			<td  colspan="2" style="text-align: left;">
								 	<button class="btn btn-xs btn-info btn-outline unedit-remove" type="button" onclick="addContactUnit(this)">+新建公司</button>
					  			</td>
					  		</tr>
					  		<tr class="companyPaymentInfo" style="display: none;">
					  			<td>银行名</td>
					  			<td>
								 	<input class="form-control input-sm" placeholder="开户行"  data-mapping="data6" type="text" name="business.CONTACT_BANK">
					  			</td>
					  			<td>银行账号</td>
					  			<td>
								 	<input class="form-control input-sm" placeholder="开户账号" data-mapping="data7" data-fn="bankCardFormat" type="text" name="business.CONTACT_BANK_NO">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>借款金额</td>
					  			<td>
					  				<input type="number" readonly="readonly" data-mapping="data1" placeholder="自动计算无需填写"  data-rules="required" class="form-control input-sm" name="business.PAY_MONEY"/>
					  			</td>
					  			<td>大写</td>
					  			<td>
					  				<input type="text" readonly="readonly" data-mapping="data2" placeholder="自动计算无需填写" class="form-control input-sm" name="business.PAY_MONEY_DX"/>
					  			</td>
					  		</tr>
				  			<tr>
					  			<td>说明</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" placeholder="如涉及投标业务有时限要求的，请特别备注" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>借款附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 unedit-remove mt-10">
				     <div class="layui-table-tool">
						<div class="pull-left">
							<i class="layui-icon layui-icon-time"></i> 借款项明细
						</div>
						<div class="pull-right">
							<button class="btn btn-sm btn-default" onclick="addTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增行</button>
							<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
							<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
							<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
						</div>
				    </div>
				  	<div class="table-responsive" style="min-height: 100px;">
							 <table class="layui-table approve-table" style="margin-top: 0px;">
							   <thead>
							    <tr>
							      <th></th>
							      <th>借款日期</th>
							      <th>借款类型</th>
							      <th class="hidden-print">借款说明</th>
							      <th>金额</th>
							      <th>部门</th>
							    </tr> 
							  </thead>
							  <tbody data-mars="LoanDao.loanItems" data-template="bxItems"></tbody>
							  <tbody>
							  	<c:forEach var="item" begin="1000" end="1020">
								    <tr id="item_${item}" data-hide-tr="1" style="display:none;">
								      <td>
								      	<input type="hidden" name="orderIndex_${item}"/>
								      	<input type="hidden" value="${item}" name="itemIndex"/>
								        <label class="checkbox checkbox-inline checkbox-success">
									      	 <input type="checkbox" name="ids" value="${item}"><span></span>
								        </label>
								      </td>
								      <td>
								      	    <input type="text" data-rules="required" data-laydate="{type:'date'}" name="bxDate_${item}" class="form-control input-sm"/>
								      </td>
								      <td>
								     	   <select data-name="feeType" data-rules="required" class="form-control input-sm feeType" name="feeType_${item}" onchange="getInvoiceType(this.value,'${item}')">
								 			    <option value="">--</option>
								 			    <option value="利息支出">利息支出</option>
												<option value="对公费用预付款">对公费用预付款</option>
												<option value="对公往来预付款">对公往来预付款</option>
												<option value="用于办理职工借款">用于办理职工借款</option>
												<option value="租赁押金借款">租赁押金借款</option>
												<option value="其他押金借款">其他押金借款</option>
												<option value="投标保证金">投标保证金</option>
												<option value="合同保证金">合同保证金</option>
												<option value="诉讼保证金">诉讼保证金</option>
								 		   </select>
								      </td>
								      <td>
								      		<textarea style="height: 40px;resize: none;" data-rules="required" maxlength="255" class="form-control input-sm" name="feeDesc_${item}"></textarea>
								      </td>
								      <td>
								      		<input type="number" class="form-control input-sm"  data-rules="required" name="amount_${item}" onchange="onlyNumber(this);calcBxMoney('${item}')"/>
								      </td>
								      <td>
								           <input type="hidden" name="deptId_${item}"/>
								           <input data-rules="required" readonly="readonly" class="form-control input-sm" name="deptName_${item}" onclick="selectBxDept(this);">
								      </td>
								    </tr>
							  	</c:forEach>
							  </tbody>	
							</table>
						</div>
				   </div>
				  <div class="layui-col-md12 edit-remove mt-10">
				 	 <table class="layui-table approve-table" style="margin-top: 0px;">
					   <thead>
					    <tr>
					      <th></th>
					      <th>借款日期</th>
					      <th>借款类型</th>
					      <th class="hidden-print">借款说明</th>
					      <th>借款金额</th>
					      <th>核准金额</th>
					      <th>部门</th>
					    </tr> 
					  </thead>
					  <tbody data-mars="LoanDao.loanItems" class="edit-remove" data-template="loanItems"></tbody>
					</table>
				  </div>
				 <div class="layui-col-md12 approve-result">
				 	   	<table class="layui-table approveResult" lay-skin="nob" style="margin-top: 10px;">
						  	<tbody data-mars="FlowDao.approveResult" data-template="approveList"></tbody>
						</table>
				</div>
			</div>
	<script id="approveList" type="text/x-jsrender">
  	   {{for data}}
  		<tr {{if CHECK_RESULT=='0'}} class="hidden-print" {{/if}}>
  			<td style="text-align:left;width:30%;">{{if NODE_ID!='0'}} {{call:CHECK_RESULT fn='checkResultLable'}}   {{if CHECK_DESC}}({{:CHECK_DESC}}) {{/if}} {{/if}}</td>
  			<td style="text-align:right;"> <span style="color:#333;">{{:NODE_NAME}}</span> / {{:CHECK_NAME}} / {{:DEPT_NAME}} / {{:CHECK_TIME}}</td>
  		</tr>
  	   {{/for}}
	   {{if data.length==0}}
		<tr><td  colspan="2">暂无数据</td></tr>
	  {{/if}}
	</script>	
	  <script id="bxItems" type="text/x-jsrender">
  		 {{for data}}
			<tr id="item_{{:ITEM_ID}}">
			  <td>
				<input type="hidden" name="orderIndex_{{:ITEM_ID}}" value="{{:ITEM_INDEX}}"/>
				<input type="hidden" name="itemId_{{:ITEM_ID}}" value="{{:ITEM_ID}}"/>
				<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
				<label class="checkbox checkbox-inline checkbox-success">
					 <input type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
				</label>
			  </td>
			  <td>
					<input type="text" data-rules="required" value="{{:BX_DATE}}" data-laydate="{type:'date'}" name="bxDate_{{:ITEM_ID}}" class="form-control input-sm"/>
			  </td>
			  <td>
				   <select data-rules="required" data-value="{{:FEE_TYPE}}" class="form-control input-sm feeType" name="feeType_{{:ITEM_ID}}" onchange="getInvoiceType(this.value,'{{:ITEM_ID}}')">
						<option value="">--</option>
						<option value="利息支出">利息支出</option>
						<option value="对公费用预付款">对公费用预付款</option>
						<option value="对公往来预付款">对公往来预付款</option>
						<option value="用于办理职工借款">用于办理职工借款</option>
						<option value="租赁押金借款">租赁押金借款</option>
						<option value="其他押金借款">其他押金借款</option>
						<option value="投标保证金">投标保证金</option>
						<option value="合同保证金">合同保证金</option>
						<option value="诉讼保证金">诉讼保证金</option>
				   </select>
			  </td>
			  <td>
					<textarea style="height: 40px;resize: none;" data-rules="required" class="form-control input-sm" name="feeDesc_{{:ITEM_ID}}">{{:FEE_DESC}}</textarea>
			  </td>
			  <td>
					<input type="number" class="form-control input-sm"  data-rules="required" value="{{:AMOUNT}}" name="amount_{{:ITEM_ID}}" onchange="onlyNumber(this);calcBxMoney('{{:ITEM_ID}}')"/>
			  </td>
			  <td>
				   <input type="hidden" name="deptId_{{:ITEM_ID}}" value="{{:DEPT_ID}}"/>
				   <input data-rules="required" readonly="readonly" value="{{:DEPT_NAME}}" class="form-control input-sm" name="deptName_{{:ITEM_ID}}" onclick="selectBxDept(this);">
			  </td>
			</tr>
 	     {{/for}}
   	  </script>
	  <script id="loanItems" type="text/x-jsrender">
  		 {{for data}}
			<tr>
			  <td>{{:ITEM_INDEX}}</td>
			  <td>{{:BX_DATE}} </td>
			  <td>{{:FEE_TYPE}}</td>
			  <td  class="hidden-print">{{:FEE_DESC}} </td>
			  <td>{{:AMOUNT}}</td>
			  <td><span class="hzAmountVal">{{:HZ_AMOUNT}}</span><input type="number" data-text="false" class="form-control input-sm updateHzAmount" value="{{:HZ_AMOUNT}}" style="width:80px;" onchange="onlyNumber(this);updateHzAmount(this,'{{:ITEM_ID}}','{{:BUSINESS_ID}}','{{:AMOUNT}}')"/></td>
			  <td>{{:DEPT_NAME}}</td>
			</tr>
 	     {{/for}}
   	  </script>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		var Flow = {};

		$(function(){
			FlowCore.initPage({hideApplyNo:true,success:function(result){
				if(FlowCore.businessId){
					var businessInfo = result['FlowDao.businessInfo'];
					var data = businessInfo['data'];
					var payType = data['PAY_TYPE'];
					if(payType=='对公付款'){
						$('.companyPaymentInfo').show();
					}
					
					initSetting(result);
					
					$(".feeType").each(function(){
						$(this).val($(this).data('value'));
					});
				}else{
					addTr();
				}
			}});
			
		});
		
		Flow.ajaxSubmitForm = function(state){
			$("[data-hide-tr]").remove();
			var index  = 1;
			$("[name^='orderIndex_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			var payType = $('#payType').val();
			if(payType=='对公付款'){
				$('.companyPaymentInfo input[type="text"]').attr('data-rules','required');
			}else{
				$('.companyPaymentInfo input[type="text"]').removeAttr('data-rules');
			}
			
			var hasFile = true;
			$('.feeType').each(function(){
				var t = $(this);
				var val = t.val();
				if(val=='投标保证金'){
					if($('.file-div').length==0){
						hasFile = false;
					}
					return false;
				}
			});
			if(!hasFile){
				layer.tips('投标保证金借款必须上传审批文件,招标文件等',$(".upload-btn"),{tips:1,time:10000,tipsMore:true});
				return;
			}
			FlowCore.ajaxSubmitForm(state);
		}
		
		
		function initSetting(result){
			var baseData  = result['FlowDao.businessInfo']['data'];
			var applyData  = result['FlowDao.baseInfo']['data'];
			var nodeName =  applyData['nodeName'];
			var payState =  baseData['PAY_STATE'];
			if(nodeName=='会计主管'&&payState=='0'){
				layer.msg('请打印单据贴票递交财务',{icon:1,offset:'rt'});
			}
			if(payState=='1'){
				$("[name='apply.nodeName']").text('准备付款');
				$("[name='apply.checkName']").text('--');
			}else if(payState=='10'){
				$("[name='apply.nodeName']").text('已付款');
				$("[name='apply.checkName']").text('--');
			}
		}
		
		function getData(){
			var data = form.getJSONObject("#flowForm");
			var payType = $('#payType').val();
			if(payType=='个人付款'){
				data['business.CONTACT_CODE']='',data['business.CONTACT_BANK']='',data['business.CONTACT_BANK_NO']='',data['business.CONTACT_UNIT']='';
			}
			return data;
		}
		
		Flow.insertData = function() {
			var data = getData();
		    FlowCore.insertData({data:data,reqUrl:'${ctxPath}/servlet/flow/loan?action=submitApply'});
		}
		
		Flow.updateData = function() {
			var data = getData();
			FlowCore.updateData({data:data,reqUrl:'${ctxPath}/servlet/flow/loan?action=updateApply'});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({reqUrl:'${ctxPath}/servlet/flow/loan?action=doApprove'});
		}
		
		
		function calcBxMoney(){
			var sum  = 0 ;
			$("[name^='amount_']").each(function(){
				var t = $(this);
				sum = numAdd(sum,t.val());	
			  }
		     );
			$("[name='business.PAY_MONEY']").val(Number(sum));
			
			var dxVal = digitUppercase(sum);
			$("[name='business.PAY_MONEY_DX']").val(dxVal);
		}
		
		function getInvoiceType(feeType,id){
			if(feeType=='投标保证金'){
				layer.tips('投标保证金借款必须填写最迟付款时间和付款备注',$("[name='apply.applyRemark']"),{tips:1,time:6000,tipsMore:true});
				layer.tips('投标保证金借款必须上传审批邮件,招标文件等',$(".upload-btn"),{tips:3,time:6000,tipsMore:true});
			}
		}
		
	   function setFeeType(feeType,itemId){
		    var el = $('#item_'+itemId).find('.feeType').first();
			el.val(feeType);
			return feeType;
	   }
	   
	   function onlyNumber(obj){
		  obj.value = obj.value.replace(/[^\d.]/g,"");//清除"数字"和"."以外的字符
		  obj.value = obj.value.replace(/^\./g,"");//验证第一个字符是数字而不是字符
		  obj.value = obj.value.replace(/\.{2,}/g,".");//只保留第一个.清除多余的
		  obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
		  obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
	   }
		
	  function addTr(){
			var obj  = $("[data-hide-tr]").first();
			if(obj.length==0){
				layer.msg('不能再新增啦',{icon:7,shade: 0.1});
				return;
			}
			obj.removeAttr("data-hide-tr");
			obj.show();
		}

		function delTr(){
			layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
				layer.close(index);
				$("input[name='ids']:checked").each(function(){
					var id = this.value;
					if(id.length>20){
						ajax.remoteCall("${ctxPath}/servlet/flow/loan?action=delItem",{itemId:id},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg);
								$('#item_'+id).remove();
								calcBxMoney();
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});					
					}else{
						$('#item_'+id).remove();
						calcBxMoney();
					}
					
				});
			});
		}
		
		function upTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var prevTR  = $('#item_'+id).prev();
				if (prevTR.length > 0) {
					prevTR.insertAfter($('#item_'+id));
				}
			}
		}
		function downTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var nextTR  = $('#item_'+id).next();
				if (nextTR.length > 0) {
					nextTR.insertBefore($('#item_'+id));
				}
			}
		}
		
		function selectPaymentType(val){
			if(val=='对公付款'){
				$('.companyPaymentInfo').show();
			}else{
				$('.companyPaymentInfo').hide();
			}
		}
		
		function selectContactUnit(el){
			var id = new Date().getTime();
			$(el).attr('data-sid',id);
			popup.layerShow({id:'selectContactUnit',scrollbar:false,area:['700px','500px'],offset:'20px',title:'往来单位',url:'/yq-work/pages/flow/select/select-contact-unit.jsp',data:{sid:id,type:'radio'}});
		}
		
		function addContactUnit(){
			popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['400px','350px'],url:'/yq-work/pages/flow/bx/config/contact-unit-edit.jsp',title:'新增',closeBtn:0});
		}
		
		function reloadContactUnit(){
			$("[name='business.CONTACT_UNIT']").click();
		}
		
		
		function updateHzAmount(el,itemId,businessId,amount){
			var hzAmount  = Number(el.value);
			if(hzAmount>amount){
				$(el).val(amount);
				layer.msg('核准金额不能大于'+amount);
				return;
			}
			var payMoneyDx = digitUppercase(hzAmount);
			ajax.remoteCall("${ctxPath}/servlet/flow/loan?action=updateHzMoney",{hzAmount:hzAmount,payMoneyDx:payMoneyDx,itemId:itemId,businessId:businessId},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		  function bankCardFormat(v){
		    	if(v){
			    	if(/\S{5}/.test(v)){
			    	  return v.replace(/\s/g, '').replace(/(.{4})/g, "$1 ");
			    	}else{
			    		return v;
			    	}
		    	}else{
		    		return '';
		    	}
		   }
		  
		  function selctCallBack(){
			  
		  }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>