<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 140px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的录用审批申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 140px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">录用部门</td>
					  			<td>
					  				<input type="text" data-rules="required" onclick="singleDept(this)" readonly="readonly" class="form-control input-sm" name="apply.data1"/>
					  			</td>
					  			<td class="required">入职岗位</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  			
					  		</tr>
					  		<tr>
					  			<td class="required">录用者姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  			<td class="required">编制</td>
					  			<td>
					  				<select data-rules="required" class="form-control input-sm" name="apply.data4">
					  					<option value="试用期员工">试用期员工</option>
					  					<option value="正式员工">正式员工</option>
					  					<option value="实习生">实习生</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">最终面试官</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" onclick="singleUser(this)" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td class="required">驻地</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">预计入职日期</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date'}" class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  			<td class="required">最高学历</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">毕业院校</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data9"/>
					  			</td>
					  			<td class="required">所学专业</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data10"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">试用期薪资/月(税前)</td>
					  			<td>
					  				<input type="number" data-rules="required" class="form-control input-sm" name="apply.data11"/>
					  			</td>
					  			<td class="required">转正薪资/月(税前)</td>
					  			<td>
					  				<input type="number" data-rules="required" class="form-control input-sm" name="apply.data12"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">绩效比例</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data13"/>
					  			</td>
					  			<td class="required">年薪(税前)</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data14"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">手机号码</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data15"/>
					  			</td>
					  			<td class="required">邮箱地址</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data16"/>
					  			</td>
					  		</tr>
					  		<tr>
				  				<td class="required">办公电脑需求</td>
					  			<td colspan="3">
					  				<select data-rules="required" class="form-control input-sm" name="apply.data17">
					  					<option value="公司提供">公司提供</option>
					  					<option value="自带电脑办公">自带电脑办公</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">基本概况</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>简历</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>请上传</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({fileCheck:true,success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var type = applyInfo.data4;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.nowNode.nodeCode;
			if(nodeCode=='中心副总'&&type=='实习生'){
				params['nextNodeCode'] = '0';
			}
			FlowCore.addCheckOrder({data:params});
		}

		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>