<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
	<style>
		.ibox-content{min-height: calc(100vh - 100px)}
		.layui-badge{left: -1px!important;}
		.layui-tab-content {
		    padding: 5px 0px;
		    margin: 0px -10px;
		}
		.layui-table-cell{padding: 0 6px;}
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<!-- <input name="deptId" type="hidden" value="0"/> -->
			<div class="ibox-content">
			         <div class="layui-tab layui-tab-brief pt-10" lay-filter="filterData" >
						  <ul class="layui-tab-title">
						    <li class="layui-this" data-flag="1">填报记录</li>
						    <li data-flag="2">填报明细</li>
						    <li data-flag="3">项目汇总</li>
						  </ul>
						  <div class="form-group mt-10 ml-5 mr-5">
		          		    <div class="input-group input-group-sm"  style="width: 150px;">
									<span class="input-group-addon">开始月份</span> 
									<input name="startDate" autocomplete="off" data-mars="CommonDao.prevMonthId" data-mars-reload="false" id="startDate" data-mars-top="true" data-laydate="{type:'month'}" class="form-control input-sm Wdate">
							 </div>
							<div class="input-group input-group-sm"  style="width: 150px;">
								<span class="input-group-addon">结束月份</span> 
								<input name="endDate" autocomplete="off" data-mars="CommonDao.nowMonthId" data-mars-reload="false" id="endDate" data-mars-top="true" data-laydate="{type:'month'}" class="form-control Wdate">
							 </div>
						      <div class="input-group input-group-sm" style="width: 120px;">
								 <span class="input-group-addon">姓名</span>	
								 <input type="text" name="userName" class="form-control input-sm">
						     </div>
						      <div class="input-group input-group-sm" style="width: 120px;">
								 <span class="input-group-addon">部门</span>	
								 <input type="hidden" name="deptId">
								 <input type="text" class="form-control input-sm" onclick="singleDept(this)">
						     </div>
						      <div class="input-group input-group-sm"  style="width: 160px">
								 <span class="input-group-addon">项目</span>	
								 <input type="hidden" name="projectId" class="form-control input-sm">
								 <input type="text" id="projectId" class="form-control input-sm" onclick="singleProject(this);">
						     </div>
							 <div class="input-group input-group-sm">
								 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 </div>
		          	     </div>
						  <div class="layui-tab-content">
						    <div class="layui-tab-item layui-show mt-5">
						    	<table class="layui-hide" class="mt-10" id="whList"></table>
						    </div>
						    <div class="layui-tab-item">
						    	<table class="layui-hide" class="mt-10" id="fillRecord"></table>
						    </div>
						    <div class="layui-tab-item">
						    		<div data-mars="WorkHourDao.deptWorkhourStat" data-template="tpl" data-container="container" class="layui-row" id="container"></div>
									<script type="text/x-jsrender" id="tpl">
									  {{if data.length>0}}
										<div class="layui-collapse" lay-accordion>
											{{for data}}
												  <div class="layui-colla-item">
													<h2 class="layui-colla-title" data-id="{{:PROJECT_ID}}">{{:#getIndex()+1}}、 {{:PROJECT_NAME}} <span class="ml-5 label label-info label-outline">{{:PERSON_NUM}}人</span> <span class="ml-5 label label-info label-outline">{{call:TIME fn='zhNum'}}/天</span></h2>
													<div class="layui-colla-content">
													</div>
												  </div>
											{{/for}}
										</div>
										{{else}}
										 <div style="text-align: center;font-size: 16px;margin-top: 15px;">暂无填报数据</div>
									  {{/if}}
									</script>
									<script type="text/x-jsrender" id="detailTpl">
										<table class="layui-table">
										  <thead>
											<tr>
											  <th>部门</th>
											  <th>姓名</th>
											  <th>工作月</th>
											  <th>工作量(人/日)</th>
											  <th>工作概括</th>
											  <th>填报者</th>
											  <th>填写时间</th>
											</tr> 
										  </thead>
										  <tbody>
										  {{for data}}
											<tr>
											  <td> {{:DEPTS}}</td>
											  <td>{{:USERNAME}}</td>
											  <td>{{:MONTH_ID}}</td>
											  <td>{{:WORK_TIME}}</td>
											  <td>{{:WORK_RESULT}}</td>
											  <td>{{call:CREATOR fn='getUserName'}}</td>
											  <td>{{:CREATE_TIME}}</td>
											</tr>
										  {{/for}}
										  {{if data.length==0}}<tr><td  colspan="6">暂无数据</td></tr>{{/if}}
										  </tbody>
										</table>
											
									</script>
						    </div>
					    </div>
			         </div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var taskTable= {
				mars:'WorkHourDao.workHourList',
				limit:50,
				toolbar:true,
				height:'full-110',
				id:'fillRecord',
				limits:[10,15,30,50,100,200,300],
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
				    field: 'DEPTS',
					title: '部门',
					align:'center',
					width:110
				},{
				    field: 'USERNAME',
					title: '姓名',
					align:'center',
					width:90
				},{
				    field: 'MONTH_ID',
					title: '月份',
					align:'left',
					width:80,
					templet:function(row){
						return monthIdFormat(row['MONTH_ID']);
					}
				 },{
					 field:'PROJECT_NO',
					 title:'项目号',
					 width:120
				 },{
				    field: 'PROJECT_NAME',
					title: '项目名称',
					align:'left',
					event:'list.detail',
					style:'cursor:pointer',
					minWidth:280
				},{
				   field: 'WORK_TIME',
				   title: '工作量/人日',
				   width:120,
				   sort:true,
				   align:'center'
				},{
				   field: 'WORK_RESULT',
				   title: '概括',
				   minWidth:120,
				   align:'left'
				},{
				   field:'CREATE_TIME',
				   title:'填报时间',
				   width:130
				}
		]]};
		
		var whTable= {
				mars:'WorkHourDao.queryProjectWh',
				limit:50,
				toolbar:true,
				height:'full-110',
				id:'whList',
				limits:[10,15,30,50,100,200,300],
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
				    field: 'USERNAME',
					title: '姓名',
					align:'left',
					width:150,
					templet:'<div>{{d.USERNAME}}_{{d.DEPTS}}</div>'
				},{
				    field: 'MONTH_ID',
					title: '月份',
					align:'left',
					width:100,
					templet:function(row){
						return monthIdFormat(row['MONTH_ID']);
					}
				 },{
				    field: 'PROJECT_NUM',
					title: '参与项目数',
					align:'left',
					width:100
				},{
				   field: 'WORK_TIME',
				   title: '月总工作量/日',
				   width:120,
				   sort:true,
				   align:'center'
				},{
				   field:'UPDATE_TIME',
				   title:'填报时间',
				   width:150
				},{
				  field:'',
				  title:'操作',
				  event:'whDetail',
				  width:100,
				  templet:'<div><a href="javascript:;">查看</a></div>'
				}
		]]};
		var list={
			init:function(){
				$("#searchForm").initTable($.extend(taskTable,{id:'fillRecord'},{done:function(result){

				}}));
				$("#searchForm").initTable($.extend(whTable,{id:'whList'},{done:function(result){

				}}));
			},
			detail:function(data){
				popup.openTab({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/project/project-detail.jsp',title:'项目总览',data:{projectId:data.PROJECT_ID}});
			},
			query:function(){
				$("#searchForm").render({success:function(){
					list.renderEvent();
					$("#searchForm").queryData({id:'fillRecord'});
					$("#searchForm").queryData({id:'whList'});
				}});
			},
			renderEvent:function(){
				layui.use(['element'], function(){
					var element = layui.element;
					element.render();
				});
				$(".layui-colla-title").click(function(){
					var t=$(this);
					if(t.attr("isload")==1){
						return;
					}
					t.attr("isload","1");
					var data = form.getJSONObject("#searchForm");
					data['projectId']=t.data("id");
					ajax.daoCall({params:data,controls:['WorkHourDao.projectWorkhourDetail']},function(result){
						var tpl = $.templates("#detailTpl");
						var html = tpl.render({data:result['WorkHourDao.projectWorkhourDetail']['data']});
						t.next().html(html);
					},{loadding:false});
				});
			}
		}
		function zhNum(val){
			if(val){
				val = Number(val);
				return val.toFixed(1);
			}
			return val;	
		}
		
		function whDetail(data){
			var userId = data['USER_ID'];
			var username = data['USERNAME'];
			var json = {id:data.ID,userId:userId,monthId:data['MONTH_ID'],state:data.STATE,opType:'detail'};
			popup.layerShow({id:'weeklyDetail',type:1,full:true,shade: 0.1,shadeClose:true,maxmin:true,anim:0,scrollbar:false,url:'${ctxPath}/pages/project/workhour/workhour-edit.jsp',title:username+"_"+monthIdFormat(json['monthId'])+'_工时明细',data:json});
		}
		
		function monthIdFormat(dateString){
			if(dateString){
				var pattern = /(\d{4})(\d{2})/;
				var formatedDate = dateString.replace(pattern, '$1年$2月');
				return formatedDate;
			}else{
				return '';
			}
		}
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.renderEvent();
				list.init();
			}});
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>