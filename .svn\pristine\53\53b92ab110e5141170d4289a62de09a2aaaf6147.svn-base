package com.yunqu.work.service;

import java.sql.SQLException;

import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.FlowFormModel;
import com.yunqu.work.model.FlowFormRowMapper;

public class FlowService extends AppBaseService{

	private static class Holder{
		private static FlowService service=new FlowService();
	}
	public static FlowService getService(){
		return Holder.service;
	}
	
	public FlowFormModel getFlowFormModel(String formId) {
		try {
			return this.getQuery().queryForRow("select * from yq_flow_form where form_id = ?",new Object[] {formId}, new FlowFormRowMapper());
		} catch (SQLException e) {
			this.getLogger().error(null,e);
			return new FlowFormModel();
		}
	}
	
	public void updateApplyInfo(String businessId,String currentResultId,String nextResultId,String nowNodeId,String nextNodeId,int applyState) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(nowNodeId,"current_node_id = ?,");
			sql.append(nextNodeId,"next_node_id = ?,");
			sql.append(currentResultId,"current_result_id = ?,");
			sql.append(nextResultId,"next_result_id = ?,");
			sql.append(applyState,"apply_state = ?,");
			sql.append(EasyDate.getCurrentDateString(),"last_result_time = ?");
			sql.append(businessId,"where apply_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	/**
	 * 流程退回
	 * @param businessId
	 * @param nextNodeId
	 * @param nextResultId
	 */
	public void updateApplyReturn(String businessId) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(EasyDate.getCurrentDateString(),"last_result_time = ?");
			sql.append(21,",apply_state = ?");
			sql.append(businessId,"where apply_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	
	public void updateApplyBegin(String businessId,String nextNodeId,String nextResultId) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(0,"current_node_id = ?");
			sql.append(0,",current_result_id = ?");
			sql.append(10,",apply_state = ?");
			sql.append(nextResultId,",next_result_id = ?");
			sql.append(nextNodeId,",next_node_id = ?");
			sql.append(businessId,"where apply_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	public void updateApplyEnd(String businessId,String nowNodeId,String nextResultId) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(nowNodeId,"current_node_id = ?,");
			sql.append(EasyDate.getCurrentDateString(),"apply_end_time = ?,");
			sql.append(nextResultId,"current_result_id = ?,");
			sql.append(30,"apply_state = ?");// 10 待审批 20审批中 21 拒绝 30审批完成
			sql.append(businessId,"where apply_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	public void updateApplyInfo(String businessId,String nextNodeId,int applyState) {
		try {
			EasySQL sql = new EasySQL("update yq_flow_apply set ");
			sql.append(nextNodeId,"current_node_id = ?,");
			sql.append(applyState,"apply_state = ?,");
			sql.append(businessId,"where apply_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null, e);
		}
	}
	
	
}
