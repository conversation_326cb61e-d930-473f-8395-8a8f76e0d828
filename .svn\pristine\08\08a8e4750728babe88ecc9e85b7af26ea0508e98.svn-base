<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>收入确认阶段看板</title>
    <style type="text/css">
        .hover-click:hover {
            color: #409eff;
            cursor: pointer;
        }

        .teams-tips {
            display: none;
        }

        .contract-url {
            display: none;
            margin-left: 10px;
            color: red;
        }

        .console-div {
            margin-bottom: 100px;
        }

        .edit-btn {
            float: right;
            margin-top: 5px;
            color: #1b9aee;
            cursor: pointer;
        }

        .no-data {
            text-align: center;
            padding: 30px;
        }

        .stat-item {
            width: 19%;
            display: inline-block;
        }

        .top-1 {
            font-size: 22px;
            font-weight: 500;
            color: #22cd0f;
            text-align: center;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
            text-align: center;
        }

        .top-3 {
            font-size: 22px;
            font-weight: 500;
            color: #f1a325;
            text-align: center;
        }

        .top-4 {
            font-size: 22px;
            font-weight: 500;
            color: #ea644a;
            text-align: center;
        }

        .top-5 {
            font-size: 22px;
            font-weight: 500;
            color: #03b8cf;
            text-align: center;
        }

        .layui-text h3 {
            font-size: 15px;
            font-weight: 500;
        }

        pre {
            background-color: #fff;
            border: none;
        }

        .layui-card {
            min-width: 100px;
            border: 1px solid #e9edf0;
            border: 1px;
            border-radius: 4px;
            -webkit-box-shadow: none;
            overflow: hidden;
        }

        .input-group-sm {
            margin-left: 5px;
        }

        .layui-icon-tips{
            margin-left: 5px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm" autocomplete="off">
        <input type="hidden" name="source" value="all">
        <input type="hidden" name="incomeStageType" value="all">

        <div class="layui-row layui-col-space10 console-div">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header">年度已收入确认排行TOP10<i class="layui-icon layui-icon-tips" lay-tips="此处统计收入确认只包括收入确认A，不包括收入确认B。"></i></div>
                    <div class="layui-card-body" style="min-height: 200px;max-height: 400px">
                        <table class="layui-table">
                            <thead>
                            <tr>
                                <th>销售</th>
                                <th>本年已收入确认</th>
                            </tr>
                            </thead>
                            <tbody data-template="rank-template" data-mars="ContractStatisticDao.confirmIncomeYearRankBySales">

                            </tbody>
                            <script id="rank-template" type="text/x-jsrender">
                             {{for data}}
                             <tr>
                             <td>{{call:SALES_BY fn='getUserName'}}</td>
                            <td>{{:TOTAL_CONFIRM_NO_TAX_A}}</td>
                            </tr>
                            {{/for}}
                              </script>
                        </table>
                    </div>
                </div>
                <div class="layui-card risk-card">
                    <div class="layui-card-header">年度未收入确认排行TOP10</div>
                    <div class="layui-card-body" style="min-height: 250px;max-height: 400px">
                        <table class="layui-table">
                            <thead>
                            <tr>
                                <th>销售</th>
                                <th>本年待收入确认</th>
                            </tr>
                            </thead>
                            <tbody data-template="yearRank-template" data-mars="ContractStatisticDao.unConfirmIncomeYearRankBySales">

                            </tbody>
                            <script id="yearRank-template" type="text/x-jsrender">
                             {{for data}}
                             <tr>
                             <td>{{call:SALES_BY fn='getUserName'}}</td>
                            <td>{{:TOTAL_UNCONFIRM_NO_TAX_A}}</td>
                            </tr>
                           {{/for}}
                              </script>
                        </table>
                    </div>
                </div>
            </div>
            <div class="layui-col-md9">
                <div class="layui-card" data-mars="IncomeStageDao.incomeConfirmStat1">
                    <div class="layui-card-header">收入确认阶段统计(税后)<i class="layui-icon layui-icon-tips" lay-tips="以下金额数据全部为税后，且收入确认只计入收入确认A。"></i></div>
                    <div class="layui-card-body" style="min-height: 150px;" data-mars="IncomeStageDao.incomeStageCountStat2">
                        <div class="stat-item">
                            <div class="top-5" id="COUNT_ALL_2">0</div>
                            <div class="top-2">全部待收入确认阶段数<i class="layui-icon layui-icon-tips" lay-tips="全部未完成收入确认A的收入确认阶段总数。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-5" id="COUNT_YEAR_2">0</div>
                            <div class="top-2">今年待确认阶段数<i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为2024年且未完成收入确认A的收入确认阶段总数。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-5" id="COUNT_MONTH_2">0</div>
                            <div class="top-2">本月待确认阶段数<i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为当月且未完成收入确认A的收入确认阶段总数。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-3" id="COUNT_DUE_2">0</div>
                            <div class="top-2">即将到期阶段数<i class="layui-icon layui-icon-tips" lay-tips="距离计划完成日期不足7天且未完成收入确认A的收入确认阶段总数。"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-4" id="COUNT_OVER_2">0</div>
                            <div class="top-2">已超时阶段数<i class="layui-icon layui-icon-tips" lay-tips="已超过计划完成日期仍未完成收入确认A的收入确认阶段总数。"></i></div>
                        </div>
                        <hr>
                        <div class="stat-item">
                            <div class="top-5" id="SUM_ALL_2">0</div>
                            <div class="top-2">全部待收入确认金额<i class="layui-icon layui-icon-tips" lay-tips="全部未完成的收入确认阶段的 待收入确认A的金额总和。部分确认收入了的收入确认阶段，其已确认收入A的金额不计入其中。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-5" id="SUM_YEAR_2">0</div>
                            <div class="top-2">今年待确认阶段金额<i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为2024年且未完成收入确认A的收入确认阶段 的 待收入确认A的金额总和。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-5" id="SUM_MONTH_2">0</div>
                            <div class="top-2">本月待确认阶段金额<i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为当月且未完成收入确认A的收入确认阶段 的 待收入确认A的金额总和。(包括即将到期和已超时)"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-3" id="SUM_DUE_2">0</div>
                            <div class="top-2">即将到期阶段金额<i class="layui-icon layui-icon-tips" lay-tips="距离计划完成日期不足7天且未完成收入确认A的收入确认阶段 的 待收入确认A的金额总和。"></i></div>
                        </div>
                        <div class="stat-item">
                            <div class="top-4" id="SUM_OVER_2">0</div>
                            <div class="top-2">已超时阶段金额<i class="layui-icon layui-icon-tips" lay-tips="已超过计划完成日期仍未完成收入确认A的收入确认阶段 的 待收入确认A的金额总和。confirmIncomeYearRankBySales。"></i></div>
                        </div>
                    </div>
                </div>
                <div class="layui-card">
                    <div class="layui-card-header">收入确认阶段列表</div>
                    <div class="layui-card-body teams" style="min-height: 150px;">
                        <div class="form-group" width="100%" style="display:flex">
                            <div class="input-group input-group-sm" style="width: 150px">
                                <span class="input-group-addon">合同号</span>
                                <input type="text" name="contractNo" class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm" style="width: 150px">
                                <span class="input-group-addon">合同名称</span>
                                <input type="text" name="contractName" class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm">
                                <label class="radio radio-info radio-inline">
                                    <input name="searchType" type="radio" value="1" checked onchange="toggleDateFields(this.value)"> <span>年</span>
                                </label>
                                <label class="radio radio-info radio-inline">
                                    <input name="searchType" type="radio" value="0" onchange="toggleDateFields(this.value)"> <span>月</span>
                                </label>
                            </div>
                            <div class="input-group input-group-sm" style="display: none">
                                <input type="text" name="dateField" class="form-control input-sm" value="year">
                            </div>
                            <div class="input-group input-group-sm" id="yearInput">
                                <span class="input-group-addon">计划完成年份</span>
                                <input type="text" name="yearId" class="form-control input-sm Wdate" onClick="WdatePicker({dateFmt:'yyyy'})" style="width: 100px;">
                            </div>
                            <div class="input-group input-group-sm" id="monthInput" style="display: none">
                                <span class="input-group-addon">计划完成月份</span>
                                <input type="text" name="monthId" class="form-control input-sm Wdate" onClick="WdatePicker({dateFmt:'yyyyMM'})" style="width: 100px;">
                            </div>
                            <div class="input-group input-group-sm">
                                <button type="button" class="btn btn-sm btn-default" onclick="IncomeConsole.query()"><span class="glyphicon glyphicon-search"></span> <span>搜索</span></button>
                                <button type="button" class="btn btn-sm btn-default ml-5" onclick="IncomeConsole.clearForm()">清空</button>
                            </div>
                        </div>
                        <div class="layui-tab layui-tab-card" lay-filter="contentFilter" style="border-top: 1px solid #ddd;">
                            <ul class="layui-tab-title">
                                <li lay-id="A" class="layui-this">全部</li>
                                <li lay-id="B">即将到期 <i class="layui-icon layui-icon-tips" lay-tips="距离计划完成日期不足7天，且收入确认A未完成。"></i></li>
                                <li lay-id="C">本月待确认 <i class="layui-icon layui-icon-tips" lay-tips="计划完成日期为本月，且收入确认A未完成。"></i></li>
                                <li lay-id="E">全部待确认 <i class="layui-icon layui-icon-tips" lay-tips="所有未完成收入确认A 的收入确认阶段(包含即将到期和已超时)。"></i></li>
                                <li lay-id="D">已超时 <i class="layui-icon layui-icon-tips" lay-tips="已过计划完成日期，且收入确认A未完成。"></i></li>
                                <li lay-id="F">已完成 <i class="layui-icon layui-icon-tips" lay-tips="所有已完成收入确认A 的收入确认阶段(包含超时完成)。"></i></li>
                                <li lay-id="G">超时完成 <i class="layui-icon layui-icon-tips" lay-tips="超时完成收入确认 的收入确认阶段。"></i></li>
                            </ul>
                            <div class="layui-tab-content" style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
                                <div class="layui-tab-item layui-show">
                                    <jsp:include page="include/income-stage-list.jsp"/>
                                </div>
                                <div class="layui-tab-item">
                                    <jsp:include page="include/income-stage-list-due.jsp"/>
                                </div>
                                <div class="layui-tab-item">
                                    <jsp:include page="include/income-stage-list-month.jsp"/>
                                </div>
                                <div class="layui-tab-item">
                                    <jsp:include page="include/income-stage-list-unconfirm.jsp"/>
                                </div>
                                <div class="layui-tab-item">
                                    <jsp:include page="include/income-stage-list-over.jsp"/>
                                </div>
                                <div class="layui-tab-item">
                                    <jsp:include page="include/income-stage-list-confirm.jsp"/>
                                </div>
                                <div class="layui-tab-item">
                                    <jsp:include page="include/income-stage-list-timeout.jsp"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </form>


</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script>
        jQuery.namespace("IncomeConsole");

        IncomeConsole.tabsId = 'A';
        var loadMap = {};
        $(function () {

            $("#searchForm").render({
                success: function () {
                    incomeStageList.init();
                }
            });
        });

        IncomeConsole.clearForm = function () {
            $("#searchForm")[0].reset();
            $('#yearInput').show();
            $('#monthInput').hide();
            $('input[name="dateField"]').val("year")


            layui.use('element', function(){
                var element = layui.element;
                element.tabChange('contentFilter', 'A'); // 'contentFilter' 是 lay-filter 对应的值，'A' 是第一个标签的 lay-id
            });

            IncomeConsole.tabsId = 'A';
           IncomeConsole.query();
        }

        IncomeConsole.query = function () {
            if (IncomeConsole.tabsId == 'A') {
                $("#searchForm").queryData({id: 'incomeStageTable', data: {}});
            } else if (IncomeConsole.tabsId == 'B') {
                $("#searchForm").queryData({id: 'incomeStageTable2', data: {}});
            } else if (IncomeConsole.tabsId == 'C') {
                $("#searchForm").queryData({id: 'incomeStageTable3', data: {}});
            } else if (IncomeConsole.tabsId == 'D') {
                $("#searchForm").queryData({id: 'incomeStageTable4', data: {}});
            } else if (IncomeConsole.tabsId == 'E') {
                $("#searchForm").queryData({id: 'incomeStageTable5', data: {}});
            } else if (IncomeConsole.tabsId == 'F') {
                $("#searchForm").queryData({id: 'incomeStageTable6', data: {}});
            } else if (IncomeConsole.tabsId == 'G') {
                $("#searchForm").queryData({id: 'incomeStageTable7', data: {}});
            }
        }

        layui.element.on('tab(contentFilter)', function (data) {
            IncomeConsole.tabsId = data.id;
           if(data.id == 'A'){
               $('input[name="incomeStageType"]').val("all");
               incomeStageList.init();
           }
            else if (data.id == 'B') {
               $('input[name="incomeStageType"]').val("due");
               incomeStageList2.init();
            } else if (data.id == 'C') {
               $('input[name="incomeStageType"]').val("month");
               incomeStageList3.init();
            } else if (data.id == 'D') {
               $('input[name="incomeStageType"]').val("timeOver");
               incomeStageList4.init();
            } else if (data.id == 'E') {
               $('input[name="incomeStageType"]').val("unConfirm");
               incomeStageList5.init();
            } else if (data.id == 'F') {
               $('input[name="incomeStageType"]').val("confirm");
               incomeStageList6.init();
            } else if (data.id == 'G') {
               $('input[name="incomeStageType"]').val("timeout");
               incomeStageList7.init();
            }
        });


        function toggleDateFields(value) {
            if (value === '0') {
                $('#yearInput').hide();
                $('#monthInput').show();
                $('input[name="dateField"]').val("month")
            } else {
                $('#yearInput').show();
                $('#monthInput').hide();
                $('input[name="dateField"]').val("year")
            }
        }


        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>