package com.yunqu.work.utils;

import java.io.IOException;

import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;

import com.alibaba.fastjson.JSONObject;

public class TTSUtiles {
	
	private static String token = null;
	
	public static void getToken() {
		String url = "https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=BAuDs4GpE9Mgbnu4lROBEDs7&client_secret=RTuQjrpxn024WY878se7LjldTW7fGgmL";
		Connection connect = Jsoup.connect(url);
		try {
			Response execute = connect.ignoreContentType(true).timeout(2000).method(Method.GET).execute();
			String result = execute.body();
			JSONObject object = JSONObject.parseObject(result);
			token = object.getString("access_token");
		} catch (IOException e) {
			e.printStackTrace();
			token = null;
		}
	}
	public static String token() {
		if(token==null) {
			getToken();
		}
		if(token==null) {
			return "";
		}
		return token;
	}
	
	public static String getUrl() {
		token();
		return "http://tsn.baidu.com/text2audio?lan=zh&ctp=1&cuid=yunqu&tok="+token+"&vol=9&per=0&spd=5&pit=5&aue=3&tex=";
	}

}
