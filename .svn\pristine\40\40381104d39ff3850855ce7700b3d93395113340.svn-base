package com.yunqu.work.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/excel/*")
public class ExcelServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		EasyRecord record=new EasyRecord("yq_excel","excel_id");
		String id = RandomKit.uuid();
		try {
			JSONObject object = getJSONObject();
			record.set("excel_content",object.getString("content"));
			record.set("title",object.getString("title"));
			record.set("excel_id", RandomKit.uuid());
			record.set("creator", getUserId());
			record.set("auth_flag","0");//0 私有 1公有 其他:密码访问
			record.set("create_time", EasyDate.getCurrentDateString());
			record.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(id);
	}
	public EasyResult actionForUpdate(){
		EasyRecord record=new EasyRecord("yq_excel","excel_id");
		try {
			JSONObject object = getJSONObject();
			record.set("excel_id", object.getString("excelId"));
			record.set("excel_content",object.getString("content"));
			record.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
}
