<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>通知公告</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		 	 <h5>公告</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">标题</span>	
							 <input type="text" name="title" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">类别</span>	
							 <select name="type" class="form-control input-sm" onchange="list.query();">
							 	<optgroup label="默认">
				            		<option value="1">日常公告</option>
				            		<option value="2">新闻动态</option>
				            		<option value="3">每周菜单</option>
				            		<option value="0">分享</option>
				            		<!-- <option value="4">人事调动</option>
				            		<option value="5">招聘信息</option> -->
			            		</optgroup>
			            		<optgroup label="fly">
			            		  <option value="10">提问</option> 
			                      <option value="20">分享</option> 
			                      <option value="30">讨论</option> 
			                      <option value="40">建议</option> 
			                      <option value="50">公告</option> 
			                      <option value="60">动态</option> 
			            		</optgroup>
							 </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" id="search" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
	    var type='${param.type}'; 
		$(function(){
			$("input:radio[name=type][value="+type+"]").attr("checked",true);  
		    list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'RemindDao.list',
					autoFill:true,
					data:{type:type},
					cols: [[
		             {
						title: '编号',
						width:80,
						type:'numbers'
					 },{
					    field: 'TITLE',
						title: '标题',
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail'
					},{
					    field: 'PUBLISH_BY',
						title: '发布人',
						width:100,
						align:'center'
					},{
					    field: 'PUBLISH_TIME',
						title: '发布时间',
						align:'center',
						width:180,
						sort:true
					},{
					    field: 'VIEW_COUNT',
						title: '阅读量',
						align:'center',
						width:100,
						sort:true
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			reload:function(){
				$("#searchForm").queryData();
			},
			detail:function(data){
				popup.openTab({id:"remindDetail",url:'${ctxPath}/notice/'+data.REMIND_ID,title:'详情',data:{remindId:data.REMIND_ID}});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>