<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-inbox"></span> 项目管理</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目名称</span>	
							 <input type="text" name="projectName" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目类型</span>	
							 <select name="projectType" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="10" data-class="label label-info">合同项目</option>
		                    		<option value="11" data-class="label label-info">提前执行</option>
		                    		<option value="20" data-class="label label-warning">自研项目</option>
		                    		<option value="30" data-class="label label-warning">维保项目</option>
	                    	</select>
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目状态</span>	
							 <select name="projectState" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="11" data-class="text-info">进度正常</option>
		                    		<option value="12" data-class="text-warning">存在风险</option>
		                    		<option value="13" data-class="text-danger">进度失控</option>
	                    	</select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <EasyTag:res resId="PROJECT_ADD">
							 <div class="input-group input-group-sm pull-right">
								 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 新增</button>
							 </div>
						 </EasyTag:res>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
  			{{if currentUserId == CREATOR || isSuperUser}}<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
		</script>
		<script type="text/x-jsrender" id="progressTpl">
  			{{:TASK_COUNT}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ProjectDao.projectList',
					height:'full-120',
					skin:'line',
					limit:20,
					cols: [[
					  {title:'序号',type:'numbers'},
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail',
						templet:function(row){
							var type = row['PROJECT_TYPE'];
							if(type==10){
								return row.PROJECT_NAME;
							}else{
								var val = getText(type,'projectType');
								return val + "&nbsp;" +row.PROJECT_NAME;
							}
						}
					},
					{
					    field: 'TASK_COUNT',
						title: '任务数',
						align:'center',
						width:90,
						templet:function(row){
							 return renderTpl('progressTpl',row);;
						}
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:120,
						templet:function(row){
							return getText(row.PROJECT_STATE,'projectState');
						}
					},{
					    field: 'PO',
						title: '项目负责人',
						align:'center',
						width:120,
						templet:function(row){
							return getUserName(row.PO);
						}
					},{
					    field: 'PERSON_COUNT',
						title: '参与人数',
						width:100,
						align:'center'
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						width:120,
						align:'center'
					},{
					    field: 'END_DATE',
						title: '截止时间',
						width:120,
						align:'left'
					},{
						title: '操作',
						align:'center',
						width:100,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							row['isSuperUser']=isSuperUser;
						    return renderTpl('bar1',row);
						}
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(data){
				popup.openTab({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/project/project-detail.jsp',title:'项目总览',data:{projectId:data.PROJECT_ID}});
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'编辑项目',data:{projectId:data.PROJECT_ID}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'新增项目'});
			},
			addTask:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务',data:{projectId:data.PROJECT_ID}});
			}
		}
		function reloadTaskList(){
			
		}
		function reloadProjectList(){
			list.query();
		}
		$(function(){
			list.init();
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>