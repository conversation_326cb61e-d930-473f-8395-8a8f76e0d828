package com.yunqu.work.model;

import com.yunqu.work.base.AppBaseModel;

public class CommentModel extends AppBaseModel {

	private static final long serialVersionUID = 1L;
	
	public CommentModel(){
		setTableInfo("YQ_COMMENTS", "COMMENT_ID");
	}
	private String commentId;
	
	private String fkId;
	
	private String creator;
	
	private String createName;
	
	private String createTime;
	
	private String ip;
	
	private String userAgent;
	
	private String content;

	public String getCommentId() {
		return commentId;
	}

	public void setCommentId(String commentId) {
		this.commentId = commentId;
		set("COMMENT_ID", commentId);
	}

	public String getFkId() {
		return fkId;
	}

	public void setFkId(String fkId) {
		this.fkId = fkId;
		set("FK_ID", fkId);
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
		set("CREATOR", creator);
	}
	
	public void setCreateName(String createName) {
		this.createName = createName;
		set("CREATE_NAME", createName);
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
		set("CREATE_TIME", createTime);
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
		set("IP", ip);
	}

	public String getUserAgent() {
		return userAgent;
	}

	public void setUserAgent(String userAgent) {
		this.userAgent = userAgent;
		set("USER_AGENT", userAgent);
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
		set("CONTENT", content);
	}
	
	

}
