package com.yunqu.work.servlet.work;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.rowmapper.ProjectDomain;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.ProjectService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;

@WebServlet("/servlet/project/conf")
public class ProjectConfServlet extends AppBaseServlet {
	private static final long serialVersionUID = -6366774540430826942L;

	public EasyResult actionForAddTeamUser(){
		JSONObject params = getJSONObject();
		String projectId = params.getString("projectId");
		String teamStr = params.getString("teamIds");
		int succcess = 0 ;
		if(StringUtils.notBlank(teamStr)){
			String[] teams = teamStr.split(",");
			for(int i=0;i<teams.length;i++){
				String userId=teams[i];
				EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM", "PROJECT_ID","USER_ID");
				record.setPrimaryValues(projectId,userId);
				record.set("JOIN_TIME", EasyDate.getCurrentDateString());
				try {
					this.getQuery().save(record);
					succcess ++;
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}else {
			return EasyResult.fail("请选择");
		}
		return EasyResult.ok(succcess,succcess+"个成员新增成功");
	}
	
	public EasyResult actionForUpdateTeamAdminFlag(){
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
		try {
			record.set("PROJECT_ID", params.getString("projectId"));
			record.set("USER_ID", params.getString("userId"));
			record.set("ADMIN_FLAG", params.getIntValue("adminFlag"));
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForRemoveProjectTeamUser(){
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
		try {
			record.set("PROJECT_ID", params.getString("projectId"));
			record.set("USER_ID", params.getString("userId"));
			this.getQuery().deleteById(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForEditNotice(){
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("YQ_PROJECT","PROJECT_ID");
		try {
			String id = params.getString("projectId");
			record.set("PROJECT_ID",id );
			record.set("PROJECT_NOTICE", params.getString("content"));
			record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			record.set("UPDATE_BY", getUserName());
			this.getQuery().update(record);
			ProjectService projectService = new ProjectService(getRequest());
			projectService.addProjectLog(getRequest(), id,"","修改了项目备忘栏");
			
			this.sendWxMsg(id,"修改项目备忘栏",params.getString("content"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddPlanItem(){
		JSONObject params = getJSONObject();
		String pPlanId = params.getString("pPlanId");
		String projectId = params.getString("projectId");
		try {
			String planNames = params.getString("planName");
			if(StringUtils.isBlank(planNames)) {
				return EasyResult.fail();
			}
			if(StringUtils.isBlank(projectId)) {
				return EasyResult.fail("请先点击保存再新增子项");
			}
			int maxOrder  = this.getQuery().queryForInt("select max(order_index) from yq_project_plan where p_plan_id = ?", pPlanId);
			if(maxOrder==0) {
				maxOrder  = this.getQuery().queryForInt("select order_index from yq_project_plan where plan_id = ?", pPlanId);
			}
			int index = maxOrder + 1;
			String[] array = planNames.split("\n");
			for(String planName:array) {
				if(StringUtils.notBlank(planName)) {
					EasyRecord planRecord = new EasyRecord("YQ_PROJECT_PLAN","PLAN_ID");
					planRecord.setPrimaryValues(FlowService.getService().getID());
					planRecord.set("p_plan_id", pPlanId);
					planRecord.set("project_id", projectId);
					planRecord.set("plan_state", 1);
					planRecord.set("p_plan_name", params.getString("pPlanName"));
					planRecord.set("plan_name",planName);
					planRecord.set("creator", getUserName());
					planRecord.set("create_time", EasyDate.getCurrentDateString());
					planRecord.set("plan_person_id", getUserId());
					planRecord.set("plan_person_name", getUserName());
					planRecord.set("update_time", EasyDate.getCurrentDateString());
					planRecord.set("update_by", getUserName());
					planRecord.set("menu_id", "0");
					planRecord.set("order_index", index);
					this.getQuery().save(planRecord);
					index++;
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelPlanItem(){
		int sum = 0;
		try {
			String planId = getJsonPara("planId");
			String projectId = getJsonPara("projectId");
			if(StringUtils.isBlank(planId)||"0".equals(planId)||StringUtils.isBlank(projectId)) {
				return EasyResult.fail();
			}
			EasySQL sql = new EasySQL();
			sql.append(EasyDate.getCurrentDateString(),"update yq_project_plan set plan_state = 0,update_time = ?");
			sql.append(getUserName(),",update_by = ?");
			sql.append("where 1=1");
			sql.append(projectId,"and project_id = ?",false);
			sql.appendIn(planId.split(","),"and plan_id");
			int result = this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			if(result==0) {
				return EasyResult.fail("本次删除需先点击保存再删除.");
			}
			sql = new EasySQL();
			sql.append(EasyDate.getCurrentDateString(),"update yq_project_plan set plan_state = 0,update_time = ?");
			sql.append(getUserName(),",update_by = ?");
			sql.append("where 1=1");
			sql.append(projectId,"and project_id = ?",false);
			sql.appendIn(planId.split(","),"and p_plan_id");
			int childCount = this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		    sum = childCount + result;
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,sum+"项删除成功");
	}
	
	public EasyResult actionForGetProjectPlan(){
		try {
			List<JSONObject> list = this.getQuery().queryForList("select * from yq_project_plan where project_id  = ?",new Object[] { getJsonPara("projectId")},new JSONMapperImpl());
			return EasyResult.ok(list);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForEditPlanItem(){
		return EasyResult.ok();
	}
	
	public EasyResult actionForEditPlan(){
		JSONArray ids = null;
		JSONObject params = getJSONObject();
		if(params.get("ids") instanceof JSONArray) {
			 ids = params.getJSONArray("ids");
		}else {
			ids = new JSONArray();
			ids.add(params.getString("ids"));
		}
		if(ids!=null&&ids.size()>0) {
			Map<String,String> newIds = new HashMap<String,String>();
			int sum  = 0;
			for(int i=0,len=ids.size();i<len;i++) {
				String configId = ids.getString(i);
				String planId = params.getString(configId+"_planId");
				EasyRecord record = new EasyRecord("YQ_PROJECT_PLAN","PLAN_ID");
				try {
					record.set("MENU_ID", configId);
					String planEndDate = params.getString(configId+"_planEndDate");
					if(StringUtils.isBlank(planEndDate)) {
						//continue;
					}
					String doUserId = params.getString(configId+"_doUserId");
					String doUserName = params.getString(configId+"_doUserName");
					if(StringUtils.isBlank(doUserId)) {
						doUserId = getUserId();
						doUserName = getUserName();
					}
					record.set("ORDER_INDEX", params.getIntValue(configId+"_order"));
					record.set("PLAN_STATE", params.getString(configId+"_state"));
					record.set("PLAN_NAME", params.getString(configId+"_planName"));
					record.set("P_PLAN_NAME", params.getString(configId+"_parentPlanName"));
					String pMenuId = params.getString(configId+"_pPlanId");
					if(newIds.get(pMenuId)!=null) {
						record.set("P_PLAN_ID", newIds.get(pMenuId));
					}
					record.set("PLAN_PERSON_ID", doUserId);
					record.set("PLAN_PERSON_NAME",doUserName);
					record.set("PLAN_BEGIN_DATE", params.getString(configId+"_planBeginDate"));
					record.set("PLAN_FINISH_DATE",planEndDate );
					record.set("BEGIN_DATE", params.getString(configId+"_beginDate"));
					record.set("FINISH_DATE", params.getString(configId+"_endDate"));
					record.set("PLAN_REMARK",params.getString(configId+"_remark"));
					record.set("PLAN_PROCESS",params.getIntValue(configId+"_process"));
					String pname = params.getString(configId+"_pname");
					if(StringUtils.notBlank(pname)) {
						record.set("PLAN_NAME",pname);
					}
					record.set("PROJECT_ID", params.getString("projectId"));
					if(StringUtils.notBlank(planId)) {
						record.set("PLAN_ID",planId);
						record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
						record.set("UPDATE_BY", getUserName());
						this.getQuery().update(record);
						sum = sum +1;
					}else {
						record.set("CREATE_TIME", EasyDate.getCurrentDateString());
						record.set("CREATOR", getUserName());
						String id = FlowService.getService().getID();
						record.set("PLAN_ID",id);
						this.getQuery().save(record);
						sum = sum +1;
						newIds.put(configId, id);
					}
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
					return EasyResult.fail();
				}
			}
			return EasyResult.ok(ids,sum+"项保存成功");
		}else {
			return EasyResult.ok();
		}
	}
	
	public EasyResult actionForQueryContractIdByReviewNo() {
		String result = Db.queryStr("SELECT CONTRACT_ID from YQ_PROJECT_CONTRACT where REVIEW_ID = ?",getJsonPara("reviewId"));
		return EasyResult.ok(result);
	}
	
	public EasyResult actionForFavoriteState() {
		int result = Db.queryInt("select count(1) from yq_favorite where favorite_by = ? and fk_id = ?",getUserId(),getPara("id"));
		return EasyResult.ok(result);
	}
	
	public EasyResult actionForUpdateFavoriteState() {
		String id = getPara("projectId");
		String favoriteFlag = getPara("favoriteFlag");
		if("1".equals(favoriteFlag)) {
			try {
				this.getQuery().executeUpdate("delete from yq_favorite where favorite_by = ? and fk_id = ?",getUserId(),id);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}else {
			EasyRecord record = new EasyRecord("yq_favorite","fk_id","favorite_by");
			record.set("favorite_id",RandomKit.uniqueStr());
			record.set("fk_id", id);
			record.set("favorite_time", EasyDate.getCurrentDateString());
			record.set("favorite_by", getUserId());
			try {
				this.getQuery().save(record);
				ProjectService projectService = new ProjectService(getRequest());
				projectService.addProjectLog(getRequest(), id,"","关注了项目");
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForStartProjectPlan() {
		JSONObject params = getJSONObject();
		String contractId = params.getString("contractId");
		try {
			EasyRecord projectRecord = new EasyRecord("YQ_PROJECT","PROJECT_ID");
			projectRecord.setPrimaryValues(contractId);
			projectRecord.set("PLAN_ID", params.getString("applyId"));
			this.getQuery().update(projectRecord);

			ProjectService projectService = new ProjectService(getRequest());
			ProjectDomain domain = projectService.getProject(contractId);
			if(domain!=null) {
				this.sendWxMsg(contractId,domain.getProjectName(),"发起项目计划");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForSetProjectPlan() {
		JSONObject params = getJSONObject();
		String contractId = params.getString("contractId");
		try {
			EasyRecord projectRecord = new EasyRecord("YQ_PROJECT","PROJECT_ID");
			projectRecord.setPrimaryValues(contractId);
			projectRecord.set("PLAN_ID", params.getString("applyId"));
			projectRecord.set("BEGIN_DATE", params.getString("beginDate"));
			projectRecord.set("CY_DATE", params.getString("cyDate"));
			projectRecord.set("ZY_DATE", params.getString("zyDate"));
			projectRecord.set("SX_DATE", params.getString("sxDate"));
			this.getQuery().update(projectRecord);
			
			String logContent = params.getString("logContent");
			if(StringUtils.isBlank(logContent)) {
				logContent = "";
			}
			ProjectService projectService = new ProjectService(getRequest());
			projectService.addProjectLog(getRequest(), contractId, params.getString("applyId"),"项目计划填报完成."+logContent);
			
			ProjectDomain domain = projectService.getProject(contractId);
			if(domain!=null) {
				this.sendWxMsg(contractId,domain.getProjectName()+"\n项目计划流程完成","初验日期:"+params.getString("cyDate")+"\n终验日期:"+params.getString("zyDate"));
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForSetProjectPlanchange() {
		JSONObject params = getJSONObject();
		String contractId = params.getString("contractId");
		try {
			EasyRecord projectRecord = new EasyRecord("YQ_PROJECT","PROJECT_ID");
			projectRecord.setPrimaryValues(contractId);
			projectRecord.set("BEGIN_DATE", params.getString("beginDate"));
			projectRecord.set("CY_DATE", params.getString("cyDate"));
			projectRecord.set("ZY_DATE", params.getString("zyDate"));
			projectRecord.set("SX_DATE", params.getString("sxDate"));
			this.getQuery().update(projectRecord);
			
			String logContent = params.getString("logContent");
			if(StringUtils.isBlank(logContent)) {
				logContent = "";
			}
			ProjectService projectService = new ProjectService(getRequest());
			projectService.addProjectLog(getRequest(), contractId, params.getString("applyId"),"项目变更计划填报完成."+logContent);
			
			ProjectDomain domain = projectService.getProject(contractId);
			if(domain!=null) {
				this.sendWxMsg(contractId,domain.getProjectName()+"\n项目计划变更流程完成","初验日期:"+params.getString("cyDate")+"\n终验日期:"+params.getString("zyDate"));
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForSetProjectPmInfo() {
		JSONObject params = getJSONObject();
		String contractId = params.getString("contractId");
		try {
			EasyRecord projectRecord = new EasyRecord("YQ_PROJECT","PROJECT_ID");
			projectRecord.setPrimaryValues(contractId);
			//项目经理
			String pmName =  params.getString("pmName");
			projectRecord.set("project_po",StaffService.getService().getUserId(pmName));
			projectRecord.set("project_po_name",pmName);
			projectRecord.set("dept_id", params.getString("deptId"));
			projectRecord.set("project_dept_name", params.getString("deptName"));
			//开发负责人
			String devName =  params.getString("devName");
			projectRecord.set("po", StaffService.getService().getUserId(devName));
			projectRecord.set("po_name", devName);
			this.getQuery().update(projectRecord);
			
			String logContent = params.getString("logContent");
			ProjectService projectService = new ProjectService(getRequest());
			projectService.addProjectLog(getRequest(), contractId, params.getString("applyId"),"项目经理任命完成."+logContent);
		
			ProjectDomain domain = projectService.getProject(contractId);
			if(domain!=null) {
				this.sendWxMsg(contractId,domain.getProjectName()+"\n项目经理任命完成","项目经理:"+pmName);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddFeedbackReply() {
		JSONObject params = getJSONObject();
		try {
			String applyId = params.getString("applyId");
			JSONObject row = this.getQuery().queryForRow("select * from yq_project_follow_record where follow_id = ?",new Object[] {applyId}, new JSONMapperImpl());
			if(row==null) {
				return EasyResult.fail(applyId+" id不存在");
			}
			String comment = params.getString("comment");
			this.getQuery().executeUpdate("update yq_project_follow_record set reply_content = ?,reply_time = ?,follow_state = 9 where follow_id = ?",comment,EasyDate.getCurrentDateString(),applyId);
			String projectId = row.getString("PROJECT_ID");
			MessageModel messageModel = new MessageModel();
			messageModel.setTitle("回复项目反馈");
			messageModel.setDesc(comment);
			
			ProjectService projectService = new ProjectService(getRequest());
			ProjectDomain domain = projectService.getProject(projectId);
			if(domain!=null) {
				messageModel.setTitle("回复项目【"+domain.getProjectName()+"】反馈");
				messageModel.setDesc(domain.getProjectName()+"："+comment);
			}
			
			messageModel.setReceiver(row.getString("UPDATE_BY"));
			messageModel.setSender(getUserId());
			messageModel.setData3(getUserName());
			messageModel.setData4(row.getString("FEEDBACK"));
			messageModel.setUrl("/yq-work/project/"+projectId+"#反馈");
			
			this.sendWxMsg(projectId,messageModel.getTitle(),messageModel.getDesc());
			
			WxMsgService.getService().sendCommentTaskMsg(messageModel);
			EmailService.getService().sendEmail(messageModel);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddRiskReply() {
		JSONObject params = getJSONObject();
		try {
			String applyId = params.getString("applyId");
			JSONObject row = this.getQuery().queryForRow("select * from yq_project_risk where risk_id = ?",new Object[] {applyId}, new JSONMapperImpl());
			if(row==null) {
				return EasyResult.fail(applyId+" id不存在");
			}
			String projectId = row.getString("PROJECT_ID");
			
			String comment = params.getString("comment");
			this.getQuery().executeUpdate("update yq_project_risk set reply_content = ?,reply_time = ?,follow_content = concat(follow_content,?,'\\n'),last_follow_time = ?,risk_state = 1 where risk_id = ?",comment,EasyDate.getCurrentDateString(),comment,EasyDate.getCurrentDateString(),applyId);
			
			MessageModel messageModel = new MessageModel();
			messageModel.setReceiver(row.getString("CREATOR"));
			messageModel.setSender(getUserId());
			messageModel.setTitle("解除项目风险");
			messageModel.setDesc(comment);
			
			
			ProjectService projectService = new ProjectService(getRequest());
			ProjectDomain domain = projectService.getProject(projectId);
			if(domain!=null) {
				messageModel.setTitle("解除项目【"+domain.getProjectName()+"】风险");
				messageModel.setDesc(domain.getProjectName()+"："+comment);
			}
			
			messageModel.setData3(getUserName());
			messageModel.setData4(row.getString("RISK_DESC"));
			messageModel.setUrl("/yq-work/project/"+projectId+"#风险");
			this.sendWxMsg(projectId,messageModel.getTitle(),messageModel.getDesc());
			WxMsgService.getService().sendCommentTaskMsg(messageModel);
			EmailService.getService().sendEmail(messageModel);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void sendWxMsg(String projectId,String title,String msg) {
		ProjectDomain domain = ProjectService.getService().getProject(projectId);
		if(domain!=null) {
			title = title +"\n"+domain.getProjectName()+"\n"+getFullUserName();
		}
		WeChatWebhookSender.sendMarkdownMessage(WebhookKey.PROJECT,title,msg);
    	if(domain!=null&&StringUtils.notBlank(domain.getWebhook())) {
    		WeChatWebhookSender.sendTextMessage(domain.getWebhook(),title+"\n"+msg);
    	}
    }
	
}
