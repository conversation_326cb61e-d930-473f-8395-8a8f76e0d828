<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.layui-tab-content{background-color: #fff;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.gray-bg{background-color: #e8edf7;}
		.items .form-control{padding: 3px 4px;font-size: 12px;}
		.items .layui-table td, .layui-table th {padding: 6px;font-size: 12px;}
		.items .select2-container--bootstrap .select2-selection{font-size: 12px;}
		.layui-icon{font-size: 12px;}
		.flow-table .form-control{min-width: 150px;}
		.extTr td{border: none;}
		.select2-results li{font-size: 12px;}
		#bxItemContainer{white-space: nowrap;}
		.ibox-content{min-height: calc(100vh - 70px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">

	<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<tr>
					  			<td style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" data-template="({{:PAY_MONEY}}元)_费用报销单"  readonly="readonly" data-mars="BxDao.bxTitle" name="apply.applyTitle"/>
					  			</td>
					  			<td style="width: 120px;">单号</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" data-mars="BxDao.yqBxNo" name="apply.applyNo"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="hidden" value="${staffInfo.deptId}" data-mapping="data2" name="business.BX_DEPT_ID">
					  				<input type="text" data-rules="required" name="business.BX_DEPT_NAME" data-mapping="data3" class="form-control input-sm" value="${staffInfo.deptName}" onclick="selectBxDept(this)">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>
					  			说明<br>
					  				<a href="javascript:;" onclick="getAllFeeType();">自动识别</a>
					  			</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" placeholder="例：2022年2月手机费、交通费、职工福利费、差旅费" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		
					  		<tr>
					  			<td>付款类型</td>
					  			<td>
					  				<select data-rules="required" class="form-control input-sm" id="payType" data-mapping="data4" name="business.PAY_TYPE" onchange="selectPaymentType(this.value)">
					  					<option value="个人付款">个人付款</option>
					  					<option value="对公付款">对公付款</option>
					  				</select>
					  			</td>
					  			<td>冲帐金额</td>
					  			<td>
					  				
					  				<input type="number" data-rules="required" onblur="onlyNumber(this);" data-mapping="data5"  onfocus="layer.msg('如无借款请填0',{icon:7,offset:'rb'})" placeholder="如无借款请填0" onchange="calcBxMoney('0000');" class="form-control input-sm" style="width: 100%;display: inline-block;" value="0" name="business.REVERSE_MONEY"/>
					  				<!-- <span>借款(0.00)</span> -->
					  			</td>
					  		</tr>
					  		<tr class="companyPaymentInfo" style="display: none;">
					  			<td>公司名</td>
					  			<td>
								 	<input type="hidden" name="business.CONTACT_CODE" data-mapping="data7">
								 	<input class="form-control input-sm" readonly="readonly" type="text" data-mapping="data6" onclick="selectContactUnit(this);" placeholder="点击选择" name="business.CONTACT_UNIT">
					  			</td>
					  			<td  colspan="2" style="text-align: left;">
								 	<button class="btn btn-xs btn-info btn-outline unedit-remove" type="button" onclick="addContactUnit(this)">+新建公司</button>
					  			</td>
					  		</tr>
					  		<tr class="companyPaymentInfo" style="display: none;">
					  			<td>银行名</td>
					  			<td>
								 	<input class="form-control input-sm" placeholder="开户行" data-mapping="data10" type="text" name="business.CONTACT_BANK">
					  			</td>
					  			<td>银行账号</td>
					  			<td>
								 	<input class="form-control input-sm" placeholder="开户账号" data-mapping="data11" type="text" name="business.CONTACT_BANK_NO">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>费用总计</td>
					  			<td>
					  				<input type="number" data-rules="required" readonly="readonly" placeholder="自动计算无需填写" data-mapping="data8" class="form-control input-sm" name="business.BX_MONEY"/>
					  			</td>
					  			<td>说明</td>
					  			<td>
					  				每月27号关闭借款以外的费用报销申请
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>应付金额</td>
					  			<td>
					  				<input type="number" data-rules="required" readonly="readonly" data-mapping="data1" placeholder="自动计算无需填写" class="form-control input-sm" name="business.PAY_MONEY"/>
					  			</td>
					  			<td>大写</td>
					  			<td>
					  				<input type="text" readonly="readonly" placeholder="自动计算无需填写" class="form-control input-sm" data-mapping="data9" name="business.PAY_MONEY_DX"/>
					  			</td>
					  		</tr>
					  		<tr class="hidden">
					  			<td>报销凭证</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-primary layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传凭证</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 mt-10 items">
				 	<jsp:include page="include/bx-item.jsp"></jsp:include>
				 </div>
			</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		var Flow = {};

		$(function(){
			if(top.$('.admin-nav-mini').length==0){
				top.$("[ew-event='flexible']").click();
			}
			
			FlowCore.initPage({success:function(result){
				if(FlowCore.businessId){
					var businessInfo = result['FlowDao.businessInfo'];
					var data = businessInfo['data'];
					var payType = data['PAY_TYPE'];
					if(payType=='对公付款'){
						$('.companyPaymentInfo').show();
					}
				}else{
					addTr();
				}
				$(".taxRate").each(function(){
					 $(this).val(Number($(this).data('value')));
				});
			}});
			
		});
		
		
		Flow.ajaxSubmitForm = function(state){
			var index  = 1;
			$("[name^='orderIndex_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			var payType = $('#payType').val();
			if(payType=='对公付款'){
				$('.companyPaymentInfo input[type="text"]').attr('data-rules','required');
			}else{
				$('.companyPaymentInfo input[type="text"]').removeAttr('data-rules');
			}
			
			var payMoney = $("[name='business.PAY_MONEY']").val();
			if(payMoney<0){
				layer.msg('应付金额不能为负数',{icon:7});
				return;
			}
			synAllSubject();
			FlowCore.ajaxSubmitForm(state);
			
		}
		
		function getData(){
			var data = form.getJSONObject("#flowForm");
			var payType = $('#payType').val();
			if(payType=='个人付款'){
				data['business.CONTACT_CODE']='',data['business.CONTACT_BANK']='',data['business.CONTACT_BANK_NO']='',data['business.CONTACT_UNIT']='';
			}
			return data;
		}
		
		Flow.insertData = function() {
			var data = getData();
			var payType = $('#payType').val();
			var doAction = $('#doAction').val();
			if(payType=='个人付款'&&doAction=='submit'){
				checkBudget(data,function(){
					FlowCore.insertData({reqUrl:'${ctxPath}/servlet/bx?action=submitApply'});
				});
			}else{
				  FlowCore.insertData({reqUrl:'${ctxPath}/servlet/bx?action=submitApply'});
			}
		}
		
		Flow.updateData = function() {
			var data = getData();
			var payType = $('#payType').val();
			var doAction = $('#doAction').val();
			if(payType=='个人付款'&&doAction=='submit'){
				checkBudget(data,function(){
					FlowCore.updateData({reqUrl:'${ctxPath}/servlet/bx?action=updateApply'});
				});
			}else{
				FlowCore.updateData({reqUrl:'${ctxPath}/servlet/bx?action=updateApply'});
			}
		}
		
		function selectPaymentType(val){
			if(val=='对公付款'){
				$('.companyPaymentInfo').show();
			}else{
				$('.companyPaymentInfo').hide();
			}
		}
		
		function calcBxMoney(itemId){
			var sum  = 0 ;
			$("[name^='amount_']").each(function(){
				var t = $(this);
				sum = numAdd(sum,t.val());	
			  }
		     );
			$("[name='business.BX_MONEY']").val(Number(sum));
			 var reverseMoney = Number($("[name='business.REVERSE_MONEY']").val());
			$("[name='business.PAY_MONEY']").val(sum - reverseMoney);
			
			var dxVal = digitUppercase(sum - reverseMoney);
			$("[name='business.PAY_MONEY_DX']").val(dxVal);
			
			if(itemId){
				calcRowRate(itemId);
			}
		}
		
	    function checkBudget(data,fn){
			var items = data.itemIndex;
			var itemIndex = [];
			if(!isArray(items)){
				itemIndex[0] = items;
			}else{
				itemIndex = items;
			}
			var json = [];
			for(var index in itemIndex){
				var itemId  = itemIndex[index];
				var feeType = data['feeInType_'+itemId];
				if(feeType.indexOf('差旅费')>-1){
					feeType = '差旅费';
				}
				var deptId = data['deptId_'+itemId];
				var amount = data['amount_'+itemId];
				var budgetMonth = data['budgetMonth_'+itemId];
				json[index] = {deptId:deptId,feeType:feeType,amount:amount,month:budgetMonth};
			}
			
			//去重 合计金额
			var newData =  getData(json);
			ajax.remoteCall("/yq-work/servlet/bx/conf?action=calcFee",{bxId:FlowCore.businessId,data:newData},function(result) {
				if(result.state == 1){
					fn();
				}else{
					$('.layer-foot button').removeAttr('disabled');
					layer.alert(result.msg,{icon: 5});
				}
			});
			function getData(data){
				var testData = {}
				for(var i =0;i<data.length;i++){
					if(!testData[data[i].deptId]){
						testData[data[i].deptId] = {}
					}
					if(!testData[data[i].deptId][data[i].month]){
						testData[data[i].deptId][data[i].month] = {}
					}
					if(typeof(testData[data[i].deptId][data[i].month][data[i].feeType]) =='undefined') testData[data[i].deptId][data[i].month][data[i].feeType] = 0;
					testData[data[i].deptId][data[i].month][data[i].feeType] += Number(data[i].amount);
				}
				return testData;
			}
		}
		
		function selectContactUnit(el){
			var id = new Date().getTime();
			$(el).attr('data-sid',id);
			popup.layerShow({id:'selectContactUnit',full:fullShow(),scrollbar:false,area:['700px','500px'],offset:'20px',title:'往来公司',url:'/yq-work/pages/flow/select/select-contact-unit.jsp',data:{sid:id,type:'radio'}});
		}
		
		function addContactUnit(){
			popup.layerShow({type:1,anim:0,scrollbar:false,full:fullShow(),shadeClose:false,offset:'20px',area:['400px','350px'],url:'/yq-work/pages/flow/bx/config/contact-unit-edit.jsp',title:'新增',closeBtn:0});
		}
		
		function reloadContactUnit(){
			$("[name='business.CONTACT_UNIT']").click();
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>