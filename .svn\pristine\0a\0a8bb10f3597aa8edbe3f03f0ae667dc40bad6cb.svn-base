<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>评审跟进</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="reviewFollowForm" autocomplete="off">
   		   		<input type="hidden" data-mars="CommonDao.randomId" name="followId"/>
   		   		<input type="hidden" value="${param.toUserId}" name="toUserId"/>
   		   		<input type="hidden" value="${param.toUserName}" name="toUserName"/>
   		   		<input type="hidden" value="${param.pFollowId}" name="pFollowId"/>
   		   		<input type="hidden" value="${param.reviewId}" name="reviewId"/>
   		   		<input type="hidden" value="${param.resultId}" name="resultId"/>
   		   		<input type="hidden" value="${param.contractName}" name="contractName"/>
   		   		<input type="hidden" value="${param.replyContent}" name="replyContent"/>
   		   		<input type="hidden" value="${param.followType}" name="followType"/>
				<table class="table table-edit">
			        <tbody>
			            <tr>
			            	<td>
			            		<textarea data-rules="required" maxlength="500" placeholder="回复描述" style="height: 110px" name="comment" class="form-control"></textarea>
			            	</td>
			            </tr>
			            <tr>
			            	<td>
			            		<button class="btn btn-xs btn-default mt-5" type="button" onclick="$('#localfile').click()">+上传</button>
			            		<span id="fileUploadMsg"></span>
			            	</td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm"  onclick="ReviewFollowEdit.ajaxSubmitForm()"> 提交 </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("ReviewFollowEdit");

		$(function(){
			$("#reviewFollowForm").render({success:function(){
				
			}});  
		});
		
		ReviewFollowEdit.ajaxSubmitForm = function(){
			if(form.validate("#reviewFollowForm")){
				ReviewFollowEdit.insertData(); 
			};
		}
		
		ReviewFollowEdit.insertData = function() {
			var data = form.getJSONObject("#reviewFollowForm");
			ajax.remoteCall("${ctxPath}/servlet/contract/review?action=addFollow",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>