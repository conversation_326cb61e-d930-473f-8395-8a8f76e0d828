<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购入库单</title>
	<style>
		.ibox-content{border: none;box-shadow:none;}
		.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th{
			 color: #0e0e0e;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="StorageOrderForm">
		<button class="btn btn-info btn-sm hidden-print" onclick="window.print();" style="position: absolute;top: 20px;right: 20px;" type="button">打印</button>
	    <input type="hidden" value="${param.orderId}" id="orderId" name="orderId"/>
	    <input type="hidden" value="${param.stockIds}" id="stockIds" name="stockIds"/>
		<div style="min-height: calc(100vh - 100px);" class="ibox-content" data-mars="OrderDao.applyInfo">
			<table style="margin: 0 auto;" class="table table-edit table-vzebra">
				<tbody>
					<tr>
						<td style="font-size: 18px;font-weight: 700;text-align: center;">广州云趣信息科技有限公司</td>
					</tr>
					<tr>
						<td style="font-size: 18px;font-weight: 700;text-align: center;">采购入库单</td>
					</tr>
				</tbody>
			</table>
			<br>
			<table style="margin: 0 auto;" class="table table-vzebra">
				<tbody>
					<tr>
						<td>供应商</td>
						<td id="SUPPLIER_NAME"></td>
						<td>公司名称</td>
						<td id="CUST_NAME"></td>
					</tr>
					<tr>
						<td>合同名称</td>
						<td id="CONTRACT_NAME"></td>
						<td>合同编号</td>
						<td id="CONTRACT_NO"></td>
					</tr>
					<tr>
						<td>采购订单号</td>
						<td id="ORDER_NO"></td>
						<td>日期</td>
						<td><span data-mars="CommonDao.today"></span></td>
					</tr>
				</tbody>
			</table>
			<br>
			<table style="margin: 0 auto;" class="layui-table" lay-size="sm">
				<thead>
					<tr>
						<td>序号</td>
						<td>入库日期</td>
						<td>申请单号</td>
						<td>物料编码</td>
						<td>物料描述</td>
						<td>单位</td>
						<td>数量</td>
						<td>单价</td>
						<td>不含税金额</td>
						<td>税率</td>
						<td>含税金额</td>
						<td>备注</td>
					</tr>
				</thead>
				<tbody data-mars="GoodsDao.stockList" data-template="template-list">
				
				</tbody>
				<tbody>
					<tr>
						<td colspan="7" style="text-align: right;">合计</td>
						<td id="total1">0</td>
						<td></td>
						<td id="total2">0</td>
						<td></td>
					</tr>
				</tbody>
			</table>
			
			<br>
			<table style="margin: 0 auto;" class="table table-edit table-vzebra">
				<tbody>
					<tr>
						<td style="width: 80px;">采购员：</td>
						<td>庞小翠</td>
						<td>仓库员：</td>
						<td>蔡洁</td>
						<td>会计：</td>
						<td>赖翠玲</td>
					</tr>
				</tbody>
			</table>
		</div>
	</form>
	
	<script id="template-list" type="text/x-jsrender">
	{{for data}}
		<tr>
		  <td>
				{{:#index+1}}
		  </td>
		  <td>
				{{:DATE_ID}}
		  </td>
		  <td>
				{{:STOCK_NO}}
		  </td>
		  <td>
				{{:GOODS_NO}}
		  </td>
		  <td>
				{{:GOODS_NAME}}
		  </td>
		  <td>
				{{:UNIT}}
		  </td>
		  <td>
				{{:NUM}}
		  </td>
		  <td>
				{{:PRICE}}
		  </td>
		  <td  class="total1">
				{{:R_PRICE*NUM}}
		  </td>
		  <td>
				{{:TAX_RATE}}%
		  </td>
		  <td class="total2">
				{{:PRICE*NUM}}
		  </td>
		  <td>
				{{:REMARK}}
		  </td>
		</tr>
   {{/for}}
</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   
		$(function(){
			$("#StorageOrderForm").render({success:function(result){
				var total1 = 0 ;
				$(".total1").each(function(){
					var t = $(this);
					total1 = numAdd(total1,t.text());
				});
				$("#total1").text(total1);
				
				var total2 = 0 ;
				$(".total2").each(function(){
					var t = $(this);
					total2 = numAdd(total2,t.text());
				});
				$("#total2").text(total2);
			}});
		});
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>