package com.yunqu.work.servlet.work;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.ProjectRiskModel;
import com.yunqu.work.model.rowmapper.ProjectDomain;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.ProjectService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;

@WebServlet("/servlet/projectRisk/*")
public class ProjectRiskServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public EasyResult actionForAdd() {
    	String id = FlowService.getService().getID();
        ProjectRiskModel model = getModel(ProjectRiskModel.class, "projectRisk");
        model.setCreator(getUserId());
        model.set("creator_name",StaffService.getService().getUserName(getUserId()));
        model.addCreateTime();
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        model.set("UPDATE_BY", getUserId());
        model.setPrimaryValues(id);
        try {
            model.save();
        } catch (Exception e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        
        try {
			String projectId = model.getString("PROJECT_ID");
			String solOwner = model.getString("SOL_OWNER");
			MessageModel messageModel = new MessageModel();
			messageModel.setReceiver(solOwner);
			messageModel.setSender(getUserId());
			messageModel.setTitle("项目风险反馈-请尽快解除风险");
			messageModel.setUrl("/yq-work/project/"+projectId+"#风险");
			messageModel.setDesc(model.getString("RISK_DESC"));
			messageModel.setData3(getUserName());
			messageModel.setData4(messageModel.getTitle());
			
			ProjectService projectService = new ProjectService(getRequest());
			ProjectDomain domain = projectService.getProject(projectId);
			if(domain!=null) {
				messageModel.setTitle("项目风险反馈【"+domain.getProjectName()+"】");
				this.sendWxMsg(getFullUserName()+"提交了项目【"+domain.getProjectName()+"】项目风险反馈",messageModel.getDesc());
			}
			WxMsgService.getService().sendCommentTaskMsg(messageModel);
			
			messageModel.setDesc(messageModel.getDesc()+"<br><br><b>请尽快登录OA回复处理风险</b>");
			EmailService.getService().sendEmail(messageModel);
		} catch (Exception e) {
			 this.error(e.getMessage(), e);
		}
		
        return EasyResult.ok(id);
    }

    public EasyResult actionForUpdate(){
        ProjectRiskModel model = getModel(ProjectRiskModel.class, "projectRisk");
        model.set("UPDATE_TIME",EasyDate.getCurrentDateString());
        model.set("UPDATE_BY",getUserId());
        try {
            model.update();
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForDelData(){
        String riskId = getJsonPara("riskId");
        try {
            this.getQuery().executeUpdate("delete from yq_project_risk where risk_id = ?",riskId);
        } catch (SQLException e) {
            e.printStackTrace();
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForBatchDel(){
        JSONArray riskArray = getJSONArray();
        for(int i=0;i<riskArray.size();i++){
            JSONObject riskObject = riskArray.getJSONObject(i);
            try {
                this.getQuery().executeUpdate("delete from yq_project_risk where risk_id = ?",riskObject.getString("RISK_ID") );
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdateObj(){
        EasyRecord record = new EasyRecord("yq_project_risk","risk_id");
        try {
            record.setColumns(getJSONObject("projectRisk"));
            if(StringUtils.notBlank(getJSONObject("projectRisk").getString("follow_content"))){
                record.set("last_follow_time", EasyDate.getCurrentDateString());
            }
            record.set("UPDATE_BY", getUserId());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getQuery().update(record);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForAddFollow(){
        String riskId = getJsonPara("riskId");
        String followContent = getJsonPara("followContent");
        try {
            this.getQuery().executeUpdate("update yq_project_risk set follow_content = concat(follow_content,?,'\n'),last_follow_time = ? where risk_id = ?",followContent,EasyDate.getCurrentDateString(),riskId);
        }catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForEndRisk(){
        String riskId = getJsonPara("riskId");
        try {
            this.getQuery().executeUpdate("update yq_project_risk set risk_state = 1 where risk_id = ?",riskId);
        } catch (SQLException e) {
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForBatchEndRisk(){
        JSONArray riskArray = getJSONArray();
        for(int i=0;i<riskArray.size();i++){
            JSONObject riskObject = riskArray.getJSONObject(i);
            try {
                this.getQuery().executeUpdate("update yq_project_risk set risk_state = 1 where risk_id = ?",riskObject.getString("RISK_ID"));
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }
    
    private void sendWxMsg(String title,String msg) {
    	WeChatWebhookSender.sendMarkdownMessage(WebhookKey.PROJECT,title,msg);
    }

}
