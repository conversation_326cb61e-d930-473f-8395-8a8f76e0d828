<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>账户管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="sqlKey" value="contactUnitList" type="hidden"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>往来单位</h5>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="dataMgr.add()">+ 新建</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="dataMgr.edit">编辑</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var dataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'BxDao.contactUnit',
					id:'dataMgrTable',
					limit:20,
					height:'full-120',
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'UNIT_NO',
						title: '编码',
						align:'left'
					},{
					    field: 'UNIT_NAME',
						title: '往来单位',
						align:'left'
					},{
					    field: 'BANK_NAME',
						title: '开户行',
						align:'left'
					},{
					    field: 'BANK_ACOUNT',
						title: '开户账号',
						align:'left'
					},{
						title: '操作',
						align:'center',
						width:80,
						templet:function(row){
						    return renderTpl('bar1',row);
						}
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable'});
			},
			edit:function(data){
				var  id = data['ID'];
				popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['400px','350px'],url:'/yq-work/pages/flow/bx/config/contact-unit-edit.jsp',title:'编辑',data:{id:id}});
			},
			add:function(){
				popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['400px','350px'],url:'/yq-work/pages/flow/bx/config/contact-unit-edit.jsp',title:'新增',closeBtn:0});
			}
		}
		
		function reloadInvoiceList(){
			dataMgr.query();
		}
		$(function(){
			$("#dataMgrForm").render({success:function(){
				dataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>