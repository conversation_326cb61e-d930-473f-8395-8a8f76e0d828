package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.MessageModel;

public class PlanNoticeService extends AppBaseService implements Job{

	private static class Holder{
		private static PlanNoticeService service=new PlanNoticeService();
	}
	
	public static PlanNoticeService getService(){
		return Holder.service;
	}
	
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		 this.run();
	}
	
	public  void run(){
		String monthId = EasyDate.getCurrentDateString("yyyy-MM");
		String monthStr = EasyDate.getCurrentDateString("yyyy年MM月");
		EasySQL sql = new EasySQL();
		sql.append("SELECT t1.*,t2.project_name,t2.project_stage from yq_project_plan t1,yq_project t2 where t1.project_id = t2.project_id and t1.plan_state in(1,9)");
		sql.appendRLike(monthId,"and t1.plan_finish_date like ?");
		sql.append("order by t1.project_id,t1.plan_finish_date");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			List<String> userList = new ArrayList<String>();
			for(JSONObject row:list) {
				String planPersonId = row.getString("PLAN_PERSON_ID");
				if(StringUtils.notBlank(planPersonId)) {
					if(!userList.contains(planPersonId)) {
						userList.add(planPersonId);
					}
				}
			}
			for(String userId:userList) {
				StringBuffer sb = new StringBuffer();
				sb.append("<p><b>"+monthStr+"要完成的项目计划</b></p>");
				sb.append("<p>请及时跟进计划，修改计划状态和完成时间（项目管理>我的计划）</p>");
				int i  = 0;
				for(JSONObject row:list) {
					String planPersonId = row.getString("PLAN_PERSON_ID");
					String planName = row.getString("PLAN_NAME");
					if(StringUtils.notBlank(planPersonId)&&StringUtils.notBlank(planName)&&planPersonId.equals(userId)) {
						i++;
						String pPlanName = row.getString("P_PLAN_NAME");
						if(StringUtils.notBlank(pPlanName)&&!planName.equals(planName)) {
							planName = pPlanName+"/"+planName;
						}
						sb.append("<p>"+i+"、"+row.getString("PROJECT_NAME")+"【"+planName+"】，截止时间："+row.getString("PLAN_FINISH_DATE")+"</p>");
					}
				}
				sb.append("<br><br><br>");
				
				MessageModel model = new MessageModel();
				model.setTitle("【项目计划】"+StaffService.getService().getUserName(userId)+"-"+monthStr+"待办计划("+i+")");
				model.setReceiver(userId);
				String superior = StaffService.getService().getStaffInfo(userId).getSuperior();
//				model.setCc(new String[] {superior});
				model.setSender(superior);
				model.setDesc(sb.toString());
				EmailService.getService().sendEmail(model);
			}
			
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
		
		
	}
	
	
}
