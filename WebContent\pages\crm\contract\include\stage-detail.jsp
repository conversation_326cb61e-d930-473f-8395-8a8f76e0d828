<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>合同阶段详情</title>
    <style>
        .form-horizontal {
            width: 100%;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="contractStageDetailForm" class="form-horizontal" data-mars="ContractStageDao.record" data-text-model="true" autocomplete="off" data-mars-prefix="contractStage.">
        <input type="hidden" value="${param.stageId}" name="contractStage.STAGE_ID"/>
        <input type="hidden" value="${param.stageId}" name="stageId"/>
        <input type="hidden" name="contractStage.CONTRACT_ID" value="${param.contractId}">
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td style="min-width: 100px">阶段名称</td>
                <td>
                    <input type="text" name="contractStage.STAGE_NAME" class="form-control input-sm">
                </td>
                <td style="min-width: 100px">所属合同</td>
                <td style="width: 55%">
                    <input type="text" name="contractStage.CONTRACT_NAME" class="form-control input-sm" style="padding-right: 10px">
                </td>
            </tr>
            <tr>
                <td>是否应收</td>
                <td>
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio" value="Y" name="contractStage.IS_RECEIVE"><span>是</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="N" name="contractStage.IS_RECEIVE"><span>否</span>
                    </label>
                </td>
                <td>应收时间</td>
                <td>
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contractStage.RCV_DATE"
                           class="form-control input-sm Wdate">
                </td>
            </tr>
            <tr>
                <td>比例(%)</td>
                <td>
                    <input type="number"
                           name="contractStage.RATIO" class="form-control input-sm"
                           placeholder="填写自动计算金额">
                </td>
                <td>计划收款金额</td>
                <td>
                    <input type="number" name="contractStage.PLAN_RCV_AMT"
                           data-rules="required" class="form-control input-sm"
                           placeholder="填写自动计算比例">
                </td>
            </tr>
            <tr>
                <td>计划完成时间</td>
                <td>
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                           name="contractStage.PLAN_COMP_DATE" class="form-control input-sm Wdate">
                </td>
                <td>实际完成时间</td>
                <td>
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                           name="contractStage.ACT_COMP_DATE" class="form-control input-sm Wdate">
                </td>
            </tr>
            <tr>
                <td style="text-align: center;" >收款列表</td>
                <td colspan="3">
                    <table id="detailReceiptList"></table>
                </td>
            </tr>
            <tr>
                <td>余款维保条件</td>
                <td>
                    <input type="text" name="contractStage.YK_WB_COND" class="form-control input-sm">
                </td>
                <td>余款条件天数</td>
                <td>
                    <input type="text" name="contractStage.YK_COND_DAYS" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>坏账金额</td>
                <td colspan="3">
                    <input type="number" name="contractStage.BAD_DEBT_AMT" class="form-control input-sm" value="0">
                </td>
            </tr>
            <tr>
                <td>最后更新时间</td>
                <td colspan="3">
                    <input type="text" name="contractStage.UPDATE_TIME" class="form-control input-sm">
                </td>
            </tr>
            </tbody>
        </table>
        <br>
        <p class="layer-foot text-c">
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll();"> 关闭</button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        $(function () {
            $("#contractStageDetailForm").render({
                success: function (result) {
                    detailReceiptList.init();

                }
            });
        });

      

        var detailReceiptList = {
            init: function () {
                $("#contractStageDetailForm").initTable({
                    mars: 'ContractReceiptDao.receiptList',
                    id: 'detailReceiptList',
                    page: false,
                    totalRow:true,
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            width: 60
                        },{
                            field:'RECEIPT_DATE',
                            title:'收款日期',
                            align:'left',
                            width:100,
                            totalRowText:"合计",
                        },{
                            field:'AMOUNT',
                            title:'收款金额',
                            totalRow:true,
                            width: 110
                        },{
                            field:'OWNER_NAME',
                            title:'收款责任人',
                            width: 90
                        },{
                            field:'KAOHE_DATA',
                            title:'收款考核数据',
                            width: 90
                        },{
                            field:'REMARK',
                            title:'备注',
                            width: 100
                        }
                    ]],
                    done: function (data) {
                    }
                })
            }
        }



    </script>

</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>