package com.yunqu.work.servlet.flow;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.service.BxService;

@WebServlet("/servlet/bx/conf")
public class BxConfServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForSavePeople() {
		JSONObject params = getJSONObject();
		String deptId = params.getString("deptId");
		EasyRecord record = new EasyRecord("yq_flow_bx_config","dept_id");
		try {
			record.set("DEPT_ID",deptId);
			record.set("COMPANY",getUserPrincipal().getRootId());
			record.set("DEPT_NAME",params.getString("deptName"));
			record.set("DEPT_CODE",params.getString("deptCode"));
			record.set("DEPT_MGR_ID", params.getString("DEPT_MGR_ID_"+deptId));
			record.set("ACCT_ID", params.getString("ACCT_ID_"+deptId));
			record.set("CENTER_ID", params.getString("CENTER_ID_"+deptId));
			record.set("CEO_ID", params.getString("CEO_ID_"+deptId));
			record.set("CFO_ID", params.getString("CFO_ID_"+deptId));
			record.set("ACCT_MGR_ID", params.getString("ACCT_MGR_ID_"+deptId));
			record.set("TEMP_NAME", params.getString("TEMP_NAME_"+deptId));
			record.set("FINANCE_DEPT_CODE", params.getString("FINANCE_DEPT_CODE_"+deptId));
			boolean bl = this.getQuery().update(record);
			if(!bl) {
				this.getQuery().save(record);
			}
			List<ApproveNodeModel> approvers = BxService.getService().getApprovers(deptId);
			JSONObject arpproverJSON = BxService.getService().toArpproverJSON(approvers);
			this.getQuery().executeUpdate("update yq_flow_bx_config set approve_flow = ? where dept_id = ?", JSONObject.toJSONString(arpproverJSON),deptId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForSaveBudget() {
		JSONObject params = getJSONObject();
		String budgetId = params.getString("budgetId");
		EasyRecord record = new EasyRecord("yq_flow_bx_budget","budget_id");
		try {
			record.set("YEAR_ID", params.getString("YEAR_ID_"+budgetId));
			record.set("QUARTER_1", params.getString("QUARTER_1_"+budgetId));
			record.set("QUARTER_2", params.getString("QUARTER_2_"+budgetId));
			record.set("QUARTER_3", params.getString("QUARTER_3_"+budgetId));
			record.set("QUARTER_4", params.getString("QUARTER_4_"+budgetId));
			
			
			record.set("USER_ID", params.getString("USER_ID_"+budgetId));
			record.set("FEE_TYPE", params.getString("FEE_TYPE_"+budgetId));
			for(int i=1;i<=12;i++) {
				record.set("MONTH_"+i, params.getFloatValue("MONTH_"+i+"_"+budgetId));
			}
			if(budgetId.length()>10) {
				record.set("budget_id",budgetId);
				record.set("update_time",EasyDate.getCurrentDateString());
				this.getQuery().update(record);
			}else {
				record.set("BUDGET_TYPE", params.getString("budgetType"));
				String id = RandomKit.uniqueStr();
				record.set("create_time",EasyDate.getCurrentDateString());
				record.set("create_name",getUserName());
				record.set("budget_id",id);
				this.getQuery().save(record);
				
				this.getQuery().executeUpdate("update yq_flow_bx_budget t1,"+Constants.DS_MAIN_NAME+".easi_user t2 set t1.user_name = t2.username,t1.dept_name = t2.depts where t1.user_id = t2.user_id and t1.budget_id = ?", id);
				this.getQuery().executeUpdate("update yq_flow_bx_budget t1,"+Constants.DS_MAIN_NAME+".easi_dept_user t2 set t1.dept_id = t2.dept_id where t1.user_id = t2.user_id and t1.budget_id = ?", id);
				
			}
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	

}
