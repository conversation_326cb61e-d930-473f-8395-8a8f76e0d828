package com.yunqu.work.servlet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.WeeklyModel;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.WeeklyNoticeService;
import com.yunqu.work.service.WeeklyStatService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.WeekUtils;
/**
 * 周报管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/weekly/*")
public class WeeklyServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		JSONObject params  = getJSONObject();
		WeeklyModel model=getModel(WeeklyModel.class, "weekly");
		int draft = params.getIntValue("draft");
		//model.setPrimaryValues(RandomKit.uniqueStr());
		model.addCreateTime();
		model.set("update_date",EasyCalendar.newInstance().getDateInt());
		model.set("update_time",EasyDate.getCurrentDateString());
		model.setCreator(getUserId());
		model.set("create_name", getUserName());
		model.set("dept_id", getDeptId());
		String ccIds = model.getString("CC_IDS");
		try {
			String receiver=model.getString("RECEIVER");
			if(StringUtils.isBlank(receiver)||draft==0){
				model.set("status","0");//未发送
			}else{
				model.set("SEND_TIME",EasyDate.getCurrentDateString());//已发送
				model.set("status","1");//已发送
				
				MessageModel messageModel=new MessageModel();
				messageModel.setReceiver(receiver);
				messageModel.setData(model);
				messageModel.setContents(model.getString("TITLE"));
				messageModel.setTypeName("weekly");
				messageModel.setFkId(model.getWeeklyId());
				messageModel.setSender(getUserId());
				messageModel.setSendName(getUserName());
				if(StringUtils.notBlank(ccIds)) {
					if(ccIds.indexOf(getUserId())==-1) {
						ccIds = ccIds+","+getUserId();
						messageModel.setCc(ccIds.split(","));
					}else {
						messageModel.setCc(ccIds.split(","));
					}
				}else {
					messageModel.setCc(new String[] {getUserId()});
				}
				messageModel.setMsgState(1);
				messageModel.setMsgType(2001);
				messageModel.setSenderType(0);
				
				messageModel.setData(model);
				
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendWeeklyEmail(messageModel);
					messageModel.setDesc(model.getString("ITEM_1"));
					WxMsgService.getService().sendNewWeeklyMsg(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			model.save();
			setItem(params,model.getWeeklyId());
			
			this.getQuery().executeUpdate("update yq_weekly t1,yq_weekly_date t2 set t1.week_begin= t2.week_begin,t1.week_end = t2.week_end where t1.year = t2.year and t1.week_no = t2.week_no and t1.weekly_id = ? ", model.getWeeklyId());
	
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void setItem(JSONObject params,String weeklyId) throws SQLException {
		Object itemIndexObj = params.get("itemIndex");
		if(itemIndexObj!=null) {
			JSONArray itemIndexs = null;
			if(itemIndexObj instanceof String) {
				itemIndexs = new JSONArray();
				itemIndexs.add(itemIndexObj.toString());
			}else {
				itemIndexs = params.getJSONArray("itemIndex");
			}
			for(int i= 0;i<itemIndexs.size();i++) {
				EasyRecord itemModel = new EasyRecord("yq_weekly_project","item_id");
				String itemId = itemIndexs.getString(i);
				String projectId = params.getString("projectId_"+itemId);
				itemModel.setPrimaryValues(itemId);
				itemModel.set("weekly_id", weeklyId);
				itemModel.set("project_id",projectId);
				itemModel.set("project_name",params.getString("projectName_"+itemId));
				itemModel.set("work_day",params.getString("workDay_"+itemId));
				itemModel.set("work_content",params.getString("workContent_"+itemId));
				itemModel.set("plan_work_content",params.getString("planWorkContent_"+itemId));
				if(StringUtils.isBlank(projectId)) {
					continue;
				}
				if(itemId.length()>20) {
					itemModel.set("item_id", itemId);
					this.getQuery().update(itemModel);
				}else {
					itemModel.setPrimaryValues(RandomKit.uniqueStr());
					this.getQuery().save(itemModel);
				}
			}
		}
		
	}
	
	
	public EasyResult actionForGetNearWeekly() {
		JSONObject params = getJSONObject();
		String weeklyId = params.getString("weeklyId");
		String type = params.getString("type");
		try {
			JSONObject row = this.getQuery().queryForRow("select creator,year,week_no from yq_weekly where weekly_id = ?", new Object[] {weeklyId}, new JSONMapperImpl());
			
			EasySQL sql = new EasySQL();
			sql.append(row.getString("CREATOR"),"select weekly_id from yq_weekly where creator = ?");
			
			if("next".equals(type)) {
				sql.append(row.getString("YEAR"),"and `year` >= ?");
				sql.append(row.getString("WEEK_NO"),"and week_no > ?");
				sql.append(" order by `year`,week_no limit 1");
			}else {
				sql.append(row.getString("YEAR"),"and `year` <= ?");
				sql.append(row.getString("WEEK_NO"),"and week_no < ?");
				sql.append(" order by `year` desc,week_no desc limit 1");
			}
			String newId = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			if(StringUtils.isBlank(newId)) {
				newId = "";
			}
			return EasyResult.ok(newId);
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForNoticeWriteWeekly(){
		JSONObject jsonObject = getJSONObject();
		String userId=jsonObject.getString("userId");
		MessageModel messageModel=new MessageModel();
		messageModel.setReceiver(userId);
		messageModel.setSender(getUserId());
		messageModel.setTitle("请及时填报"+jsonObject.getString("year")+"年第"+jsonObject.getString("weekNo")+"的周报");
		EmailService.getService().sendNoticeWriteWeeklyEmail(messageModel);
		return EasyResult.ok();
	}
	public EasyResult actionForDoGrade(){
		JSONObject jsonObject = getJSONObject();
		String weeklyId=jsonObject.getString("weeklyId");
		String grade=jsonObject.getString("grade");
		JSONObject log=new JSONObject();
		try {
			log.put("userId", getUserId());
			log.put("grade", grade);
			log.put("name", jsonObject.getString("name"));
			log.put("time",EasyDate.getCurrentDateString());
			this.getQuery().executeUpdate("update yq_weekly set grade = ?,grade_log = ? where weekly_id = ?", grade,log.toJSONString(),weeklyId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(log);
	}
	public EasyResult actionForUpdate(){
		JSONObject params  = getJSONObject();
		int draft = params.getIntValue("draft");
		WeeklyModel model=getModel(WeeklyModel.class, "weekly");
		model.set("update_date",EasyCalendar.newInstance().getDateInt());
		model.set("update_time",EasyDate.getCurrentDateString());
		String ccIds = model.getString("CC_IDS");
		try {
			String receiver=model.getString("RECEIVER");
			int status=model.getIntValue("STATUS");
			if(status<2){
				if(StringUtils.isBlank(receiver)||draft==0){
					model.set("status","0");//未发送
				}else{
					model.set("SEND_TIME",EasyDate.getCurrentDateString());//已发送
					model.set("status","1");//已发送
					
					MessageModel messageModel=new MessageModel();
					messageModel.setReceiver(receiver);
					messageModel.setContents(model.getString("TITLE"));
					messageModel.setFkId(model.getWeeklyId());
					messageModel.setSender(getUserId());
					messageModel.setSendName(getUserName());
					messageModel.setMsgState(1);
					messageModel.setData(model);
					messageModel.setMsgType(2001);
					messageModel.setTypeName("weekly");
					messageModel.setSenderType(0);
					if(StringUtils.notBlank(ccIds)) {
						if(ccIds.indexOf(getUserId())==-1) {
							ccIds = ccIds+","+getUserId();
							messageModel.setCc(ccIds.split(","));
						}else {
							messageModel.setCc(ccIds.split(","));
						}
					}else {
						messageModel.setCc(new String[] {getUserId()});
					}
					
					messageModel.setData(model);
					
					try {
						MessageService.getService().sendMsg(messageModel);
						EmailService.getService().sendWeeklyEmail(messageModel);
						messageModel.setDesc(model.getString("ITEM_1"));
						WxMsgService.getService().sendNewWeeklyMsg(messageModel);
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			model.update();
			setItem(params,model.getWeeklyId());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelItem(){
		String itemId = getJsonPara("itemId");
		try {
			this.getQuery().executeUpdate("delete from yq_weekly_project where item_id = ?", itemId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		WeeklyModel model=getModel(WeeklyModel.class, "weekly");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	public EasyResult actionForNoticeWeekly(){
		String weeklyNo=getPara("weeklyNo", ""+WeekUtils.getWeekNo());
		WeeklyNoticeService.getService().run(Integer.valueOf(weeklyNo));
		return EasyResult.ok("","提醒成功!");
	}
	public EasyResult actionForUpdateWeeklyStat(){
		WeeklyStatService.getService().run();
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForInit() {
		for(int year = 2018;year<2030;year++) {
			for(int weekNo=1;weekNo<=52;weekNo++) {
				String a=WeekUtils.getStartDayOfWeekNo(year,weekNo);
				String b=WeekUtils.getEndDayOfWeekNo(year,weekNo);
				EasyRecord record = new EasyRecord("yq_weekly_date");
				record.set("year", year);
				record.set("week_no", weekNo);
				record.set("week_begin", a);
				record.set("week_end", b);
				try {
					this.getQuery().save(record);
				} catch (SQLException e) {
					
				}
			}
		}
		return EasyResult.ok();
	}
}





