<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>云趣乐享平台</title>
	<style>
		/* .layui-fixbar{bottom: 20%;left: 15px;} */
		.fly-tag{margin-right: 10px;height: 24px;margin-bottom: 5px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
 <div class="layui-container">
  <div class="layui-row layui-col-space15">
     <div class="layui-col-md8">
      <c:if test="${!empty topList}">
       <div class="fly-panel" <c:if test="${indexPageFlag=='0'}">style="display:none;"</c:if>>
        <div class="fly-panel-title fly-filter">
          <a>置顶</a>
          <a href="#signin" class="layui-hide-sm layui-show-xs-block fly-right" id="LAY_goSignin" style="color: #FF5722;">去签到</a>
        </div>
        <ul class="fly-list">
        <c:forEach items="${topList}" var="item">
	          <li>
	            <a href="/yq-work/fly/u/${item.user_id}" class="fly-avatar">
	              <img src="${item.pic_url}" onerror="this.src='/yq-work/static/images/user-avatar-large.png'">
	            </a>
	            <h2>
	              <a class="layui-badge">${item.category_name}</a>
	              <a href="/yq-work/fly/detail/${item.remind_id}">${item.title}</a>
	            </h2>
	            <div class="fly-list-info">
	              <a href="/yq-work/fly/u/${item.creator}" link>
	                <cite>${item.username}</cite>
	                <i class="iconfont icon-renzheng" title="认证信息：${item.job_title}"></i>
	                <i class="layui-badge fly-badge-vip">VIP${item.level}</i>
	              </a>
	              <span class="publishtime-span">${item.publish_time}</span>
	              <c:if test="${item.experience>0}">
		              <span class="fly-list-kiss layui-hide-xs" title="悬赏积分"><i class="iconfont icon-kiss"></i> ${item.experience}</span>
	              </c:if>
	              <span class="layui-badge fly-badge-accept layui-hide-xs">已结</span>
	              <span class="fly-list-nums"> 
	                  <i class="iconfont icon-pinglun1" title="回答"></i> ${item.comment_count}
	              </span>
	            </div>
	            <div class="fly-list-badge">
	                <c:if test="${item.wonderful_flag=='1'}">
	              		<span class="layui-badge layui-bg-red">精帖</span>
	            	</c:if>
	            </div>
	          </li>
	        </c:forEach>
        </ul>
       </div>
	  </c:if>
      <div class="fly-panel" style="margin-bottom: 0;">
        <div class="fly-panel-title fly-filter">
          <a href="/yq-work/fly/index" class="layui-this">综合</a>
          <span class="fly-mid"></span>
          <a href="/yq-work/fly/index?state=1">未结</a>
          <span class="fly-mid"></span>
          <a href="/yq-work/fly/index?state=2">已结</a>
          <span class="fly-mid"></span>
          <a href="/yq-work/fly/index?state=3">精华</a>
          <span class="fly-filter-right layui-hide-xs">
            <a href="/yq-work/fly/index?sort=time" class="layui-this">按最新</a>
            <span class="fly-mid"></span>
            <a href="/yq-work/fly/index?sort=hot">按热议</a>
          </span>
        </div>
        <ul class="fly-list"> 
           <c:if test="${empty allList}">
        	 <div class="fly-none">没有相关数据</div> 
           </c:if>
          <c:forEach items="${allList}" var="item">
        		<li>
           		   <a href="/yq-work/fly/u/${item.user_id}" class="fly-avatar">
              		<img src="${item.pic_url}" onerror="this.src='/yq-work/static/images/user-avatar-large.png'">
           	 	   </a>
	            <h2>
	              <a class="layui-badge">${item.category_name}</a>
	              <a href="/yq-work/fly/detail/${item.remind_id}">${item.title}</a>
	            </h2>
	            <div class="fly-list-info">
	              <a href="/yq-work/fly/u/${item.user_id}" link>
	                <cite>${item.username}</cite>
	                <c:if test="${item.level>2}">
		                <i class="iconfont icon-renzheng" title="认证信息：${item.job_title}"></i>
		                <i class="layui-badge fly-badge-vip">VIP${item.level}</i>
	                </c:if>
	              </a>
	              <span class="publishtime-span">${item.publish_time}</span>
	              <c:if test="${!empty item.tag}"><span><a style="color: #999;" href="/yq-work/fly/tag/${item.tag}">#${item.tag}</a></span></c:if>
	              <c:if test="${item.experience>0}">
		              <span class="fly-list-kiss layui-hide-xs" title="悬赏积分"><i class="iconfont icon-kiss"></i> ${item.experience}</span>
	              </c:if>
	              <c:if test="${item.close_flag=='1'}">
		              <span class="layui-badge fly-badge-accept layui-hide-xs">已结</span>
	              </c:if>
	              <span class="fly-list-nums"> 
	                <i class="iconfont icon-pinglun1" title="回答"></i> ${item.comment_count}
	              </span>
	            </div>
	            <div class="fly-list-badge">
	            	<c:if test="${item.wonderful_flag=='1'}">
	              		<span class="layui-badge layui-bg-red">精帖</span>
	            	</c:if>
	            </div>
          </li>
          </c:forEach>        
        </ul>
        <div style="text-align: center">
          <div class="laypage-main">
          	<c:choose>
	          	<c:when test="${empty allList}">
		            <a href="/yq-work/fly/index?page=1" class="laypage-next">回到第一页</a>
	          	</c:when>
	          	<c:otherwise>
		            <a href="/yq-work/fly/index?page=${pageNo}" class="laypage-next">更多求解</a>
	          	</c:otherwise>
          	</c:choose>
          </div>
        </div>
      </div>
    </div>
    <div class="layui-col-md4">
      <div class="fly-panel fly-signin" <c:if test="${indexPageFlag=='0'}">style="display:none;"</c:if>>
        <div class="fly-panel-title">
          	签到
          <i class="fly-mid"></i> 
          <a href="javascript:;" class="fly-link" id="LAY_signinHelp">说明&奖励</a>
          <i class="fly-mid"></i> 
          <a href="javascript:;" class="fly-link" id="LAY_signinTop">活跃榜<span class="layui-badge-dot"></span></a>
          <span class="fly-signin-days">已连续签到<cite>0</cite>天</span>
        </div>
        <div class="fly-panel-main fly-signin-main">
          <button class="layui-btn layui-btn-danger" id="LAY_signin">今日签到</button>
          <span>可获得<cite>5</cite>积分</span>
          <!-- 已签到状态 -->
          <!--
          <button class="layui-btn layui-btn-disabled">今日已签到</button>
          <span>获得了<cite>20</cite>积分</span>
          -->
        </div>
       <!--  <div class="fly-panel-main">
         	发帖有礼
        </div> -->
      </div>

      <div class="fly-panel fly-rank fly-rank-reply" id="LAY_replyRank" <c:if test="${indexPageFlag=='0'}">style="display:none;"</c:if>>
        <h3 class="fly-panel-title">回贴月榜</h3>
        <dl>
          <!--<i class="layui-icon fly-loading">&#xe63d;</i>-->
          <c:forEach items="${replyTopList}" var="item">
	          <dd>
	            <a href="/yq-work/fly/u/${item.user_id}">
	              <img src="${item.pic_url}" onerror="this.src='/yq-work/static/images/user-avatar-large.png'"><cite>${item.username}</cite><i>${item.count}次回答</i>
	            </a>
	          </dd>
          </c:forEach>
        </dl>
      </div>
      <div class="fly-panel fly-link">
        <h3 class="fly-panel-title">热门标签</h3>
        <dl class="fly-panel-main tags"></dl>
      </div>
      <dl class="fly-panel fly-list-one">
        <dt class="fly-panel-title">最近热议</dt>
        <c:forEach items="${hostList}" var="item">
	        <dd>
	          <a href="/yq-work/fly/detail/${item.remind_id}">${item.title}</a>
	          <span><i class="iconfont icon-pinglun1"></i> ${item.comment_count}</span>
	        </dd>
        </c:forEach>
        <c:if test="${empty hostList}">
        	<div class="fly-none">没有相关数据</div>
        </c:if>
      </dl>
	  <div class="fly-panel" style="padding: 20px 0; text-align: center;">
        <img src="/yq-work/pages/fly/static/images/qrcode_lexiang.jpg" style="max-width: 90%;">
        <p style="position: relative; color: #666;margin-top: 10px">QQ扫码加交流群或<a href="https://qm.qq.com/q/wzWMW55qG6">点击加群</a></p>
      </div>
      <div class="fly-panel layui-hide">
        <div class="fly-panel-title">公告栏</div>
        <div class="fly-panel-main">
          <a href="https://work.yunqu-info.cn/" target="_blank" class="fly-zanzhu" time-limit="2017.09.25-2099.01.01" style="background-color: #5FB878;">乐享，信息共享</a>
        </div>
      </div>
      <div class="fly-panel">
        <div class="fly-panel-main">
        	<img style="max-width: 100%;" src="https://api.vvhan.com/api/ipCard">
        </div>
      </div>
      <div class="fly-panel fly-link">
        <h3 class="fly-panel-title">内部系统</h3>
        <dl class="fly-panel-main">
          <dd><a href="https://www.yunqu-info.com/" target="_blank" class="fly-link">官网</a><dd>
          <dd><a href="https://exmail.qq.com/" target="_blank">邮箱</a><dd>
          <dd><a href="http://doc.yunqu-info.com/" target="_blank">云盘</a><dd>
          <dd><a href="http://*************:1443/" target="_blank">文件共享</a><dd>
          <dd><a href="http://wj.yunqu-info.cn" target="_blank">问卷</a><dd>
          <dd><a href="http://*************/yq-work/auth/memos" target="_blank">备忘录</a><dd>
          <dd><a href="http://*************/yq-work/auth/mindoc" target="_blank">文档库</a><dd>
          <dd><a href="http://*************:8080" target="_blank">思维导图</a><dd>
          <dd><a href="http://*************:8777" target="_blank">drawio流程图</a><dd>
          <dd><a href="http://*************:3001/" target="_blank">Yapi</a><dd>
          <dd><a href="http://*************/zentao/my/" target="_blank">禅道</a><dd>
        </dl>
      </div>

    </div>
  </div>
</div>

</EasyTag:override>
 
<EasyTag:override name="script">
	<script type="text/javascript">
		layui.cache.page = '';
		layui.cache.user = {username: '${staffInfo.userName}' , uid: '${staffInfo.userId}', avatar: '${staffInfo.picUrl}', experience: 83 , sex: '男'};
		layui.config({version: "3.3",base: '/yq-work/pages/fly/static/mods/'}).extend({fly: 'index'}).use('fly',function(){
			 $.get('/yq-work/fly/queryTags',function(result){
				 for(var index in result.data){
					 var row = result.data[index];
					 $('.tags').append('<dd><a href="/yq-work/fly/tag/'+row.tag+'" class="layui-badge-rim fly-tag">'+row.tag+'('+row.count+')</a></dd>');
				 }
			 });
			 
			 $('.publishtime-span').each(function(){
				 var el = $(this);
				 var time = el.text();
				 el.text(layui.util.timeAgo(time, true));
			 });
			 
		});
   </script>
   <script async defer src="http://*************:6688/api/application/embed?protocol=http&host=*************:6688&token=ba97d0e104af28a3"></script>
</EasyTag:override>
<%@ include file="fly-layout.jsp" %>
