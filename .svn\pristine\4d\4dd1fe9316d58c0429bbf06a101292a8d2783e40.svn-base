package com.yunqu.work.servlet.crm;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.ContractStageModel;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;


import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
@WebServlet("/servlet/contractStage/*")
public class ContractStageServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;


    public EasyResult actionForAdd(){
        ContractStageModel model = getModel(ContractStageModel.class, "contractStage");
        model.setCreator(getUserId());
        model.addCreateTime();
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        //随机生成主键STAGE_ID
        //用setStageId应该有同样效果
        model.setPrimaryValues(RandomKit.uuid().toUpperCase());
       try {
           //这个save包含了getQuery().save(this)，提交到数据库
           model.save();
           String contractId=model.getContractId();
           if(StringUtils.notBlank(contractId)){
               //可能进行对project_contract表的更新

           }
       } catch (SQLException e) {
           this.error(e.getMessage(), e);
           return EasyResult.fail(e.getMessage());
       }

        return EasyResult.ok();
    }

    public EasyResult actionForUpdate(){
        ContractStageModel model=getModel(ContractStageModel.class,"contractStage");
        model.set("UPDATE_TIME",EasyDate.getCurrentDateString());
        model.set("UPDATE_BY",getUserId());
        try {
            model.update();
            String contractId=model.getContractId();
            if(StringUtils.notBlank(contractId)){
                //可能进行对project_contract表的更新
                //this.getQuery().executeUpdate("update yq_project_contract ...", contractId);

            }

        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForDelData(){
        //权限判断，参考ContractServlet
        if(hasRole("CONTRACT_MGR")) {
            String id = getJsonPara("id");
            try {
                this.getQuery().executeUpdate("delete from yq_contract_stage where stage_id = ?", id);
                //可能对project_contract表也进行更新
                //this.getQuery().executeUpdate("update yq_project_contract ...", contractId);
            } catch (SQLException e) {
                e.printStackTrace();
            }

        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdateObj(){
        EasyRecord record = new EasyRecord("YQ_CONTRACT_STAGE","STAGE_ID");
        try {
            record.setColumns(getJSONObject());
            record.set("UPDATE_BY", getUserId());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getQuery().update(record);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }
}
