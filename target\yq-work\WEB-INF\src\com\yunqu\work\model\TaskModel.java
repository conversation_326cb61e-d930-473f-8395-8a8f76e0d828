package com.yunqu.work.model;

import java.io.Serializable;

import com.yunqu.work.base.AppBaseModel;

public class TaskModel extends AppBaseModel implements Serializable{
	private static final long serialVersionUID = 1L;
	
	public TaskModel(){
		setTableInfo("YQ_TASK","TASK_ID");
	}
	public String getTaskId() {
		return getString("TASK_ID");
	}
	public void setTaskId(String taskId) {
		this.set("TASK_ID", taskId);
	}
	public String getTaskName() {
		return getString("TASK_NAME");
	}
	
	public String getProjectId() {
		return getString("PROJECT_ID");
	}
	
	public void setTaskName(String taskName) {
		this.set("TASK_NAME", taskName);
	}
	public void setTaskState(int taskState) {
		this.set("TASK_STATE", taskState);
	}
	public Integer getTaskState() {
		return getIntValue("TASK_STATE");
	}

}
