<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<script src="${ctxPath}/static/js/map.jquery.min.js"></script>
	<style>
		.form-horizontal{width: 100%;}
		.bMap{position: relative;}
		.bMap .map-warp{position: absolute;left:0;width:100%;height:200px;top:34px;display: none;}
		.bMap input{width:100%;height:30px;line-height: 30px;border:1px solid #d7d7d7;}
		.tangram-suggestion-main{z-index: 9999999999999999999!important;};
		.multiselect-container, .select2-container {z-index: inherit!important;}
		.select2-container .select2-selection--single{z-index: 9999!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="custEditForm" autocomplete="off" class="form-horizontal" data-mars="CustDao.record" autocomplete="off" data-mars-prefix="cust.">
 		   		<input type="hidden" value="${param.custId}" name="custId"/>
                <input type="hidden" value="${param.custId}" name="cust.CUST_ID">
 		   		<input type="hidden" name="cust.PROVINCE"/>
 		   		<input type="hidden" name="cust.ADDRESS"/>
 		   		<input type="hidden" name="cust.CITY"/>
 		   		<input type="hidden" name="cust.LAL"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td width="80px" class="required">客户名称</td>
		                    <td style="width: 40%">
		                   		 <input data-rules="required" id="custName" type="text" name="cust.CUST_NAME" class="form-control">
		                    </td>
		                    <td width="80px" class="required">客户编号</td>
		                    <td style="width: 40%">
		                    	<input type="text" data-rules="required" name="cust.CUST_NO" data-mars="CustDao.genCustId" class="form-control">
		                    </td>
			            </tr>
			           <tr>
			           		<td class="required">客户来源</td>
		                    <td>
		                    	<input class="form-control" onclick="singleDict(this,'Y011')" readonly="readonly" data-rules="required" name="cust.CUST_SOURCE">
		                    	
		                    </td>
			           		<td class="required">客户级别</td>
		                    <td>
		                    	<input class="form-control" onclick="singleDict(this,'Y007')" readonly="readonly" data-rules="required" name="cust.CUST_LEVEL">
		                    </td>
			           </tr>
			            <tr>
		                    <td class="required">客户性质</td>
		                    <td>
		                   		 <input class="form-control" onclick="singleDict(this,'Y006')" readonly="readonly" data-rules="required" name="cust.CUST_NATURE">
		                    </td>
		                    <td class="required">客户行业</td>
		                    <td>
		                    	<input class="form-control" onclick="singleDict(this,'Y008')" readonly="readonly" data-rules="required" name="cust.INDUSTRY">
		                    </td>
		                </tr>
		                  <tr>
		                	<td>客户状态</td>
		                    <td>
		                   		 <select name="cust.CUST_STATE" class="form-control">
		                   		 	<option value="10">初次接洽</option>
		                   		 	<option value="20">需求讨论</option>
		                   		 	<option value="30">方案/报价</option>
		                   		 	<option value="40">合同审核</option>
		                   		 	<option value="50">成交客户</option>
		                   		 	<option value="60">流失客户</option>
		                   		 </select>
		                   		 <input type="hidden" name="cust.TAX_NUM" class="form-control">
		                    </td>
		                    <td>网址</td>
		                    <td>
		                   		 <input type="text" name="cust.WEBSITE" class="form-control">
		                    </td>
		                </tr>
		                 <tr>
		                    <td>联系电话</td>
		                    <td>
		                   		 <input type="text" name="cust.MOBILE" class="form-control">
		                    </td>
		                    <td class="required">销售经理</td>
		                    <td>
		                   		<input name="cust.OWNER_ID" type="hidden">
		                   		<input data-rules="required" name="cust.OWNER_NAME" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm">
		                    </td>
			            </tr>
			             <tr>
		                   <td>客户地址</td>
		                    <td colspan="3">
		                    	<div class="bMap"></div>
		                    </td>
		                </tr>
			            <tr>
			            	<td class="required">描述</td>
			               	<td colspan="3">
	                           <textarea style="height: 80px;" class="form-control" name="cust.REMARK"></textarea>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="Cust.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/bootstrap3-typeahead.min.js"></script>
	<script type="text/javascript">
   
		jQuery.namespace("Cust");
	    
		Cust.custId='${param.custId}';
		
		var custId='${param.custId}';
		
		var nameToIdMap = {};
		var isEmptyReq = false;
		$(function(){
			$('#custName').typeahead({
				minLength:2,
				delay: 500,
				fitToElement:false,
				items:'all',
				autoSelect:true,
				source: function (query, process) {
					isEmptyReq = true;
			        return $.ajax({
			            url: '${ctxPath}/servlet/cust?action=queryCompanyName',
			            type: 'post',
			            data: {data:JSON.stringify({companyName: query})},
			            dataType: 'json',
			            async:false,
			            success: function (result) {        
			            	  if(result.state == "1"&&result.data.bsData) {
			            		   nameToIdMap = {};
			                       var json = JSON.parse(result.data.bsData); 
			                       var resultList = json.map(function (item) {
			                          nameToIdMap[item.Name]=item.CreditCode;;
			                          return item.Name;
			                        });          
			                      return process(resultList);                               
			                 } else {
			                	 if(query!=''){
				                    // alert(result.msg);
			                	 }
			                 }  
			            }
			        });
				    },
				    displayText:function(item){
				    	return item;
				    },
				    afterSelect:function(item){
				    	isEmptyReq = false;
				    	var taxNo=nameToIdMap[item];
				    	if(taxNo==''){
				    		return;
				    	}
				    	$("[name='cust.TAX_NUM']").val(taxNo);
				    	$.ajax({
				            url: '${ctxPath}/servlet/cust?action=queryCompanyTax',
				            type: 'post',
				            data: {data:JSON.stringify({taxNo: taxNo})},
				            dataType: 'json',
				            success: function (result) {  
				            	console.log(result);
				            	if(result.state==1){
				            		$("#Map_input_map").val(result.data['ADDRESS']);
				            		$("#Map_input_map").click();
				            	}
				            },error:function(){
				            	
				            }});
				    },
				    updater:function(item){
				    	return item;
				    }
			});
			
			$("#custEditForm").render({success:function(result){
				var record = result['CustDao.record'].data||{};
				$("#Map_input_map").val(record.ADDRESS);
				var param = {};
				if(record.ADDRESS){
					param['address']=record.ADDRESS;
					param['location']=record.LAL.split(",");
				}
				try{
					$(".bMap").bMap($.extend(param,{
						name:"map",
						callback:function(address,point){
							$("[name='cust.PROVINCE']").val(address.province);
							$("[name='cust.CITY']").val(address.city);
							$("[name='cust.ADDRESS']").val($("#Map_input_map").val());
							$("[name='cust.LAL']").val($("#Map_location_map").val());
						}
					}));
					$("#Map_input_map").click();
				}catch(e){
					console.error(e);
				}
			}});  
		});
		
		function selectCust(el){
			if(isEmptyReq){
				layer.confirm('请输入正确的企业名称并下拉选择,是否继续提交?',function(index){
					layer.close(index);
					Cust.insertData();
				});
			}else{
				Cust.insertData();
			}
		}
		Cust.ajaxSubmitForm = function(){
			if(form.validate("#custEditForm")){
				var address = $("#Map_input_map").val();
				var location = $("#Map_location_map").val();
				if(address==''){
					//layer.msg('请选择客户地址');
					//return;
				}
				if(location==''){
					//layer.msg('请在地图选择精确地址');
					//return;
				}
				if(Cust.custId){
					Cust.updateData(); 
				}else{
					selectCust();
				}
			};
		}
		Cust.insertData = function(flag) {
			var data = form.getJSONObject("#custEditForm");
			ajax.remoteCall("${ctxPath}/servlet/cust?action=addCust",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('custEditForm');
						reloadCustInfo();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Cust.updateData = function(flag) {
			var data = form.getJSONObject("#custEditForm");
			ajax.remoteCall("${ctxPath}/servlet/cust?action=updateCust",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('custEditForm');
						reloadCustInfo();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>