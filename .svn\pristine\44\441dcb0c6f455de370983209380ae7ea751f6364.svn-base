<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.b_content{
			margin: 10px 15px;
		}
		.d_content{
			margin-left: 4em;
			padding: 6px 0px;
		}
		.avatar{
			float: left;
			height: 3em;
			width: 3em;
			display: block;
		    margin: .2em 0 0;
		}
    	.avatar img{
		    display: inline-block;
		    height: 100%;
		    width: 100%;
		    border-radius: 100%;
		    overflow: hidden;
		    font-size: inherit;
		    vertical-align: middle;
		    -webkit-box-shadow: 0 0 1px rgba(0,0,0,.3);
		    box-shadow: 0 0 1px rgba(0,0,0,.3);
		    cursor: pointer;
		}
		#flowForm .userName{
		    font-size: 1em;
		    color: rgba(0,0,0,.87);
		    font-weight: 500;
		}
		#flowForm .createTime{
		    display: inline-block;
		    margin-left: .5em;
		    color: rgba(0,0,0,.4);
		    font-size: .875em;
		}
		
		blockquote{font-size: 12px;padding: 6px!important;}
		.form-horizontal{width: 100%;}
		.layui-timeline-item:hover a{opacity:1;}
		.layui-table-cell a{font-size: 12px;color: #3E84E9;cursor: pointer;}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 14px;}
		.layui-tab-card>.layui-tab-title{background-color: #fff;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		.layui-timeline-content p{font-size: 13px;color: #666;}
		.layui-timeline-title{margin-bottom: 2px;}
		.layui-timeline-item{margin-bottom: 2px;}
		.layui-text h3{font-size: 16px;}
		.userInfoSpan{text-decoration: underline;cursor: pointer;}
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.layui-table, .layui-table-view{margin: 0px 0px 15px;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
		.firstCheck{color: #333;}
		.lastCheck{color: #444;}
		.addReviewResult{display: none;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.gray-bg{background-color: #e8edf7;}
		.noticeBtn,.addUpdateLog,.noticeReview,.returnReview,.addFile{display: none;}
		
		.layui-table-cell {
			height: auto;
			word-break: normal;
		 	display: block;
		 	white-space: pre-wrap;
		 	word-wrap: break-word;
		 	overflow: hidden;
		}


	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="ReviewDao.info" data-mars-prefix="review." data-text-model="true">
				<input type="hidden" name="review.REVIEW_ID" value="${param.reviewId}"/>
				<input type="hidden" id="reviewId" name="reviewId" value="${param.reviewId}"/>
			    <input type="hidden" name="resultId" id="resultId" value="${param.resultId}"/>
			    <input type="hidden" id="reviewState" value="${param.reviewState}"/>
			    <input type="hidden" name="fkId" value="${param.reviewId}"/>
			    
				<div class="ibox-content" style="padding: 10px 5px;margin: -15px -15px 0px;">
					<div class="layui-row" style="padding:6px 15px;padding-bottom:0px;">
					  <div class="layui-col-md8">
							<span style="line-height: 30px;" id="contractName">合同评审</span>
					  </div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm">
									<button class="btn btn-info btn-xs addReviewResult mr-10" onclick="addReviewResult();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 填写评审意见</button>
									<button class="btn btn-default btn-xs addUpdateLog mr-10" onclick="addUpdateLog();" type="button"><i class="fa fa-plus" aria-hidden="true"></i> 添加修订</button>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
				</div>
				<div class="layui-row">
					<div class="layui-col-md12">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">相关信息</li>
						    <li>评审记录(<span id="resultCount">0</span>)</li>
						    <li>处理记录(<span id="followCount">0</span>)</li>
						  </ul>
						  <div class="layui-tab-content" style="margin:0px;background-color: #fff;min-height: calc( 100vh - 120px)">
						    <div class="layui-tab-item layui-show baseInfo">
						    	<jsp:include page="include/base-info.jsp"></jsp:include>
						    </div>
						    <div class="layui-tab-item">
						    	<table class="layui-hide" id="reviewResult"></table>
						    	<script  type="text/html" id="toolbar">
						    		<button type="button" class="btn btn-sm btn-default ml-10 noticeReview" lay-event="noticeReview">催办</button>
						    		<button type="button" class="btn btn-sm btn-warning ml-10 returnReview" lay-event="returnReview">退回</button>
								</script>
						    </div>
						    <div class="layui-tab-item">
						    	<div data-mars="ReviewDao.followList" data-template="template-comments"></div>
						    </div>
						  </div>
					</div>
					</div>
				</div>
		</form>
		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="uploadFile()"/>
		</form>
		
		 <script id="template-comments" type="text/x-jsrender">
			{{call:data fn='setFollowCount'}}
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onclick="userInfoLayer('{{:ADD_USER_ID}}')" onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:ADD_USER_ID fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{:ADD_USER_NAME}}</span> <span class="createTime">{{:ADD_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{call:FOLLOW_CONTENT fn='getContent'}}</div>
								 <a  class="btn btn-xs btn-link hover ib" onclick="reply($(this))">回复</a> <textarea data-text="false" class="hidden">{{call: fn='getJsonStr'}}</textarea>
							  	 {{if #parent.parent.data.currentUserId == ADD_USER_ID}}<a class="btn btn-xs btn-link hover ib" href="javascript:void(0)" onclick="delComment('{{:COMMENT_ID}}',$(this))">删除</a>	{{/if}}
								 {{if PREV_CONTENT}} <blockquote class="layui-elem-quote layui-quote-nm mt-5 mb-5"> {{:TO_USER_NAME}} {{:PREV_CONTENT}}</blockquote> {{/if}}
							</div>						
					</div>
				</div>
			{{/for}}
		</script>
		
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">

		layui.config({
			  base: '${ctxPath}/static/module/tableMerge/'
		}).use('tableMerge'); //加载自定义模块
	
		var Review = {reviewId:$('#reviewId').val()};
		var currentUserId = getCurrentUserId();
		var applyUserId = '${param.applyUserId}';
		var toUserId = '${param.toUserId}'||'';
		var doResult = '${param.doResult}'||'0';
		var fileLabel = '2';
		$(function(){
			if(currentUserId==applyUserId){
				fileLabel = '0';
				$('.addUpdateLog,.addFile').show();
			}
			layui.use(['element','form'],function(){
				var element = layui.element;
			});
			var resultId = $("#resultId").val();
			var reviewState = $("#reviewState").val();
			if(resultId==''||reviewState=='20'||reviewState=='30'){
				$(".addReviewResult").remove();
			}else if(doResult!='0'){
				$(".addReviewResult").hide();
			}else if(toUserId.indexOf(currentUserId)>-1){
				fileLabel = '1';
				$(".addReviewResult").show();
			}else{
				$(".addReviewResult").hide();
			}
			$("#flowForm").render({success:function(result){
				var row = result['ReviewDao.info'].data;
				$("#contractName").text(row['CONTRACT_NAME']);
				
				var jsonText = row['TEXT_JSON'];
				var json = eval('(' + jsonText + ')');
				fillRecord(json,'review.','','#flowForm');
				initNodeResult();
			}});
			
		});
		function getContent(val){
			if(val){
				return val.replace(/\n|\r\n/g,'<br/>');
			}else{
				return '--';
			}
		}
		function initNodeResult(){
			layui.use(['tableMerge'],function(){
				var tableMerge  = layui.tableMerge;
				$("#flowForm").initTable({
					mars:'ReviewDao.reviewNodeResult',
					id:'reviewResult',
					page:false,
					toolbar:'#toolbar',
					filter:true,
					height:'full-180',
					cols: [[
		              {
	            	 	 type: 'checkbox',
						 align:'left'
					  },{
	            	 	 type: 'numbers',
						 title: '序号',
						 align:'left'
					  },
					   {
						 field:'NODE_NAME',
						 title:'评审部门',
						 merge:true,
						 width:100
					 },{
						 field:'NODE_TYPE',
						 title:'审批级别',
						 width:80,
						 align:'center',
						 templet:function(row){
							 return row['NODE_TYPE']=='0'?'<span class="firstCheck">初审</span>':'<span class="lastCheck">终审</span>';
						 }
					 },{
						 field:'TO_USER_NAME',
						 title:'待办人',
						 minWidth:180
					 },{
						 field:'GET_TIME',
						 title:'送达时间',
						 width:140
					 },{
						 field:'DO_USER_NAME',
						 title:'评审人',
						 align:'center',
						 width:80,
						 templet:function(row){
							return '<span class="userInfoSpan" onclick="userInfoLayer(\''+row['DO_USER_ID']+'\')">'+row.DO_USER_NAME+'</span>';
						}
					 },{
						 field:'DO_TIME',
						 title:'评审时间',
						 width:140
					 },{
						 field:'DO_RESULT',
						 title:'评审结果',
						 align:'center',
						 width:80,
						 templet:function(row){
							 var doResult = row['DO_RESULT'];
							 if(doResult=='1'){
							 	return '<span class="label label-success">可行</span>';
							 }else if(doResult=='2'){
							 	return '<span class="label label-warning">不可行</span>';
							 }else{
								return '--';							 
							 }
						 }
					 },{
						 field:'COMMENT',
						 title:'评审意见',
						 minWidth:200,
						 event:'commentDetail',
						 style:'text-decoration:underline;cursor:pointer;'
					 }
				]],done:function(data){
					tableMerge.render(layTables['reviewResult'].config);
					$("#resultCount").text(data.total);
					if(currentUserId==applyUserId){
						$('.returnReview,.noticeReview').show();
					}
			  	}
			 });
				
			});
		}
		
		function setFollowCount(data){
			$('#followCount').text(data.length);
			return '';
		}
		
		function getJsonStr(data){
			return JSON.stringify(data);
		}
		
		function addUpdateLog(){
			var reviewId = $("[name='reviewId']").val();
	 		var json = {reviewId:reviewId};
			popup.layerShow({id:'addUpdateLog',area:['400px','300px'],url:'${ctxPath}/pages/crm/contract/review/include/revise-log.jsp',title:'新增修订日志',data:json});
		}
		
		function reply(el){
			var dataStr  = $(el).next().val();
			var data = eval('(' + dataStr + ')');
			var reviewId = $("[name='reviewId']").val();
		    var toUserId = data['ADD_USER_ID'];
		    var toUserName = data['ADD_USER_NAME'];
		    var resultId = data['RESULT_ID'];
		    var pFollowId = data['FOLLOW_ID'];
		    var replyContent = data['FOLLOW_CONTENT'];
		    var title = '回复'+toUserName+">"+replyContent;
		    var contractName = $("#contractName").text();
		    var json = {followType:'2',replyContent:replyContent,reviewId:reviewId,resultId:resultId,toUserId:toUserId,toUserName:toUserName,pFollowId:pFollowId,contractName:contractName};
			popup.layerShow({id:'addReviewFollow',area:['500px','350px'],url:'${ctxPath}/pages/crm/contract/review/include/review-follow.jsp',title:title,data:json});
		}
		
		function addReviewResult(){
			var reviewId = $("[name='reviewId']").val();
			var resultId = $("[name='resultId']").val();
			popup.layerShow({id:'addReviewResult',title:'评审意见',area:['550px','400px'],url:'${ctxPath}/pages/crm/contract/review/include/review-result.jsp',data:{resultId:resultId,reviewId:reviewId}});
		}
		
		function noticeReview(row){
			if(row.length>0){
				var ids = [];
				var userIds = [];
				for(var index in row){
					var dr = row[index]['DO_RESULT'];
					if(dr=='0'){
						ids.push(row[index]['NODE_ID']);
						userIds.push(row[index]['TO_USER_ID']);
					}
				}
				if(ids.length>0){
					var contractName = $("#contractName").text();
					var data = {};
					data['contractName'] = contractName;
					data['ids'] = ids.join();
					data['userIds'] = userIds.join();
					popup.layerShow({id:'noticeReview',area:['550px','400px'],url:'${ctxPath}/pages/crm/contract/review/include/review-notice.jsp',title:'催办通知',data:data});
				}else{
					layer.msg('已评审无需提醒');
				}
			}else{
				layer.msg('请选择');
			}
		}
		
		function returnReview(row){
			if(row.length==1){
				var data = row[0];
				var doResult = data['DO_RESULT'];
				if(doResult=='0'){
					layer.msg('未评审不允许退回操作');
				}else{
					//发送通知
					var contractName = $("#contractName").text();
					var json = {resultId:data['RESULT_ID'],contractName:contractName,doUserId:data['DO_USER_ID'],doUserName:data['DO_USER_NAME']};
					layer.prompt({formType:2,value:'请重新评审',title:'评审退回',area:['250px','130px'],offset:'30px',shade:0.1},function(value, index, elem){
						layer.close(index);
						json['remark'] = value;
						ajax.remoteCall("${ctxPath}/servlet/contract/review?action=returnReviewNode",json,function(result) { 
							if(result.state == 1){
								layer.msg(result.msg,{icon:1,time:1200},function(){
									layer.closeAll();
									$("#flowForm").queryData({id:'reviewResult'});
								});
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});
					});
					
				}
			}else if(row.length>1){
				layer.msg('只能选择一条记录');
			}else{
				layer.msg('请选择');
			}
		}
		
		function commentDetail(data){
			var json = {reviewId:data['REVIEW_ID'],resultId:data['RESULT_ID']};
			popup.layerShow({id:'commentDetail',area:['550px','450px'],title:'回复列表',url:'${ctxPath}/pages/crm/contract/review/include/reply-query.jsp',data:json});
			
		}
		
		function reloadFollow(){
			$("#flowForm").queryData({id:'replyResult',page:false,data:{resultId:''}});
			$("#flowForm").queryData({id:'reviewResult',page:false});
		}
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>