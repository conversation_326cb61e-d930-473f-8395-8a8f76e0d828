<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>版本</title>
	<style>
		img{max-width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="AppDao.record" autocomplete="off" data-mars-prefix="app.">
     		    <input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
 		   		<input type="hidden" value="${param.groupId}" name="fkId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr class="hidden">
		                    <td style="width: 80px" class="required">groupId</td>
		                    <td><input value="${param.groupId}" id="groupId" name="app.GROUP_ID" type="text" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td style="width: 80px" class="required">应用名称</td>
		                    <td><input data-rules="required"  type="text" name="app.APP_NAME" class="form-control input-sm"></td>
			            </tr>
			            <tr>
			            	 <td class="required">应用负责人</td>
		                    <td>
		                    <select class="form-control input-sm" data-rules="required" data-mars="CommonDao.userDict" name="app.APP_OWNER">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
			            </tr>
			            <tr>
		                    <td class="required">应用描述</td>
		                    <td>
		                       <div id="editor"></div>
	                          	 <textarea id="wordText" data-text="false" style="height: 600px;width:400px;display: none;" class="form-control input-sm" name="app.APP_DESC"></textarea>
		                    </td>
			            </tr>
			             <tr>
			                <td>应用相关文件</td>
			               	<td>
			               		 <div data-template="template-files" data-mars="FileDao.fileList"></div>
			               		 <div id="fileList"></div>
								 <EasyTag:res resId="VERSION_MANAGER">
									 <button class="btn btn-sm btn-default detail" type="button" onclick="$('#localfile').click()">+添加</button>
								 </EasyTag:res>
			               	</td>
	            	    </tr>
			        </tbody>
 			     </table>
				 <div class="layer-foot text-c">
				    	  <!-- <button type="button" class="btn btn-success btn-sm" style="display: none;"  onclick="Edit.ajaxSubmitForm(2)"> 审核通过 </button>
				    	  <button type="button" class="btn btn-danger btn-sm ml-10" style="display: none;" onclick="Edit.ajaxSubmitForm(3)"> 审核不通过 </button> -->
				    	  <EasyTag:res resId="VERSION_MANAGER">
					    	   <button type="button" class="btn btn-primary btn-sm m-l0 detail"  onclick="Edit.ajaxSubmitForm(2)"> 保 存 </button>
				    	  </EasyTag:res>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>
  		</form>
		 <script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" class="detail" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Edit.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		
		Edit.groupId='${param.groupId}';
		var groupId='${param.groupId}';
		var op='${param.op}';
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.config.menus = [
      		    'code',  // 代码
      		    'head',  // 标题
      		    'bold',  // 粗体
      		    'fontSize',  // 字号
      		    'fontName',  // 字体
      		    'foreColor',  // 文字颜色
      		    'link',  // 插入链接
      		    'list',  // 列表
      		    'justify',  // 对齐方式
      		    'quote',  // 引用
      		    'emoticon',  // 表情
      		    'image',  // 插入图片
      		    'table',  // 表格'
      	];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.config.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.create();
		
		$(function(){
			$("#editForm").render({success:function(result){
				 editor.txt.html($("#wordText").val());
			}});
			requreLib.setplugs('select2',function(){
				$("select[name='app.APP_OWNER']").select2({theme: "bootstrap",placeholder:'请选择'});
				$(".select2-container").css("width","100%");
			});
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.groupId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.uploadFile = function(){
			var fkId='';
			if(Edit.groupId){
				fkId=groupId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'callback',fkId:fkId,source:'app',fileMaxSize:1024*50});
			
		}
		var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
		}
		Edit.insertData = function() {
			$("#groupId").val($("#randomId").val());
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/app?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/app?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>