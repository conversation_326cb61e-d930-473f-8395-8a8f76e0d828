package com.yunqu.work.servlet.flow;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.DateUtils;

@WebServlet("/servlet/flow/fun")
public class FlowFunServlet extends BaseFlowServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAddCCReadTime() {
		JSONObject params = getJSONObject();
		String ccId = params.getString("ccId");
		try {
			this.getQuery().executeUpdate("update yq_flow_cc set read_time = ? where cc_id = ? and read_time = ''", EasyDate.getCurrentDateString(),ccId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateField() {
		JSONObject params = getJSONObject();
		JSONObject fields = params.getJSONObject("fields");
		Set<String> keys= fields.keySet();
		for(String key:keys){
			String flowCode = params.getString("flowCode");
			String businessId = params.getString("businessId");
			FlowModel flow = ApproveService.getService().getFlow(flowCode);
			String fieldName = key;//params.getString("fieldName");
			String value = fields.getString(key);//params.getString("value");
			try {
				if(fieldName.startsWith("apply")) {
					fieldName = fieldName.substring(6);
					this.getQuery().executeUpdate("update yq_flow_apply set "+fieldName+" = ? where apply_id = ?", value,businessId);
				}else if(fieldName.startsWith("business")) {
					fieldName = fieldName.substring(9);
					this.getQuery().executeUpdate("update "+flow.getTableName()+" set "+fieldName+" = ? where business_id = ?", value,businessId);
				}else if(fieldName.startsWith("extend")) {
					fieldName = fieldName.substring(7);
					String jsonStr = this.getQuery().queryForString("select data_json from yq_flow_apply where apply_id = ?", businessId);
					JSONObject extJson = new JSONObject();
					if(StringUtils.notBlank(jsonStr)) {
						extJson = JSONObject.parseObject(jsonStr);
					}
					extJson.put(fieldName, value);
					this.getQuery().executeUpdate("update yq_flow_apply set data_json = ? where apply_id = ?", extJson.toJSONString(),businessId);
				}
			} catch (SQLException e) {
				this.error(null, e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForRecover() {
		EasyQuery query = this.getQuery();
		try {
			JSONObject params = getJSONObject();
			String resultId = params.getString("resultId");
			String businessId = params.getString("businessId");
			String nextResultId = params.getString("nextResultId");
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result==null) {
				return EasyResult.fail();
			}
			int checkResult  = result.getCheckResult();
			if(checkResult==0) {
				return EasyResult.fail();
			}
			query.begin();
			query.executeUpdate("update yq_flow_apply set next_result_id = ?,current_result_id = ? where apply_id = ?",resultId,result.getPrevResultId(),businessId);
			
			query.executeUpdate("delete from yq_flow_approve_result where result_id = ?", nextResultId);
			
			EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
			record.setPrimaryValues(resultId);
			record.set("result_id", resultId);
			record.set("prev_result_id",result.getPrevResultId());
			record.set("get_time",EasyDate.getCurrentDateString());
			record.set("read_time","");
			record.set("check_time","");
			record.set("check_desc","");
			record.set("check_result",0);
			query.update(record);
			
			query.commit();
			
			this.addReviseLog(businessId, "收回审批");
			
			MessageModel msgModel = getMessageModel(businessId,null, "审批收回", "审批已收回");
			this.sendAllMsg(msgModel);
			
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(ex.getMessage(), ex);
			}
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForSubmitCC() {
		JSONObject params = getJSONObject();
		String ccIdsStr = params.getString("ccIds");
		String businessId = params.getString("businessId");
		String resultId = params.getString("resultId");
		String ccRemark = params.getString("ccRemark");
		String ccPk =  RandomKit.uniqueStr();
		int i = 0;
		ArrayList<String> userIds = new ArrayList<String>();
		ArrayList<String> userNames = new ArrayList<String>();
		if(StringUtils.notBlank(ccIdsStr)) {
			String[] ccIds = ccIdsStr.split(",");
			for(String ccId:ccIds) {
				EasyRecord record = new EasyRecord("yq_flow_cc","cc_id");
				record.set("cc_id",ccPk);
				record.set("cc_by", ccId);
				record.set("cc_by_name", StaffService.getService().getUserName(ccId));
				record.set("business_id", businessId);
				record.set("create_by", getUserId());
				record.set("create_name", getUserName());
				record.set("result_id", resultId);
				record.set("cc_remark", ccRemark);
				record.set("cc_time", EasyDate.getCurrentDateString());
				try {
					boolean existRecord = this.getQuery().queryForExist("select count(1) from yq_flow_cc where cc_by = ? and business_id = ?",ccId,businessId);
					if(existRecord) {
						continue;
					}
					this.getQuery().save(record);
					userIds.add(ccId);
					userNames.add(StaffService.getService().getUserName(ccId));
					ccPk = RandomKit.uniqueStr();
					i++;
				} catch (SQLException e) {
					this.error(null, e);
				}
			}
		}
		if(i==0) {
			return EasyResult.fail("不可重复抄送.");
		}else {
			if(userIds.size()>0) {
				this.sendFeedback(businessId,userIds.stream().collect(Collectors.joining(",")),"新的流程抄送",ccRemark);
			}
		}
		this.updateFlowCCNum(businessId);
		this.addReviseLog(businessId,ccPk,"流程抄送："+userNames.stream().collect(Collectors.joining(",")));
		return EasyResult.ok();
	}
	
	public EasyResult actionForSubmitTrust() {
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			JSONObject params = getJSONObject();
			String resultId = params.getString("resultId");
			String businessId = params.getString("businessId");
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			
			String trustUserId = params.getString("trustUserId");
			if(getUserId().equalsIgnoreCase(trustUserId)) {
				return EasyResult.fail("不可委托给自己.");
			}
			
			boolean existRecord = this.getQuery().queryForExist("select count(1) from yq_flow_trust where trust_user_id = ? and business_id = ? and result_id = ?",trustUserId,businessId,resultId);
			if(existRecord) {
				return EasyResult.fail("不可重复委托");
			}
			
			ApproveNodeModel node = ApproveService.getService().getNodeByResultId(resultId);
			
			String trustId = RandomKit.uniqueStr();
			EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
			record.setPrimaryValues(resultId);
			record.set("trust_id",trustId);
			record.set("get_time",EasyDate.getCurrentDateString());
			record.set("read_time","");
			record.set("check_desc",getUserName()+"发起的流程委托");
			record.set("check_user_id",params.getString("trustUserId"));
			record.set("check_name",params.getString("trustUserName"));
			if(node!=null&&node.getHourLimit()>0) {
				record.set("over_time",DateUtils.hourAfter(node.getHourLimit()));
			}
			
			EasyRecord trustRecord = new EasyRecord("yq_flow_trust","trust_id");
			trustRecord.setPrimaryValues(trustId);
			trustRecord.set("business_id", businessId);
			trustRecord.set("result_id", resultId);
			trustRecord.set("create_by",getUserId());
			trustRecord.set("create_by_name",getUserName());
			trustRecord.set("trust_user_id",trustUserId);
			trustRecord.set("trust_user_name",params.getString("trustUserName"));
			trustRecord.set("trust_reason",params.getString("trustReason"));
			trustRecord.set("trust_time",EasyDate.getCurrentDateString());
			
			query.update(record);
			query.save(trustRecord);
			query.commit();
			
			this.addReviseLog(businessId,trustId,"流程委托");
			
			this.sendFeedback(businessId,trustUserId,"新的流程委托");
		} catch (SQLException e) {
			this.error(null, e);
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(null, ex);
			}
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddPrintLog() {
		try {
			EasyQuery query = this.getQuery();
			JSONObject params = getJSONObject();
			String resultId = params.getString("resultId");
			String businessId = params.getString("businessId");
			EasyRecord record = new EasyRecord("yq_flow_print","print_id");
			record.setPrimaryValues(RandomKit.uniqueStr());
			record.set("business_id", businessId);
			record.set("result_id", resultId);
			record.set("print_by",getUserId());
			record.set("print_by_name",getUserName());
			record.set("print_time",EasyDate.getCurrentDateString());
			query.save(record);
			
			this.addReviseLog(businessId,record.getPrimaryValue().toString(), "打印流程");
			
			//this.sendFeedback(businessId,"流程打印");
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForAddUrge() {
		try {
			EasyQuery query = this.getQuery();
			JSONObject params = getJSONObject();
			String resultId = params.getString("resultId");
			String businessId = params.getString("businessId");
			String msgContent = params.getString("msgContent");
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result==null) {
				return EasyResult.fail();
			}
			int checkResult  = result.getCheckResult();
			if(checkResult!=0) {
				return EasyResult.fail();
			}
			boolean existRecord = this.getQuery().queryForExist("select count(1) from yq_flow_urge where receiver_id = ? and business_id = ? and result_id = ?",result.getCheckUserId(),businessId,resultId);
			if(existRecord) {
				return EasyResult.fail("不可重复催办.");
			}
			
			EasyRecord record = new EasyRecord("yq_flow_urge","urge_id");
			record.setPrimaryValues(RandomKit.uniqueStr());
			record.set("business_id", businessId);
			record.set("msg_content", msgContent);
			record.set("result_id", resultId);
			record.set("create_by",getUserId());
			record.set("create_by_name",getUserName());
			record.set("create_time",EasyDate.getCurrentDateString());
			record.set("receiver_name",result.getCheckName());
			record.set("receiver_id",result.getCheckUserId());
			query.save(record);
			
			this.addReviseLog(businessId,record.getPrimaryValue().toString(),"流程催办："+result.getCheckName());
			
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);
			MessageModel msgModel = getMessageModel(apply, result.getCheckUserId(), "流程催办", msgContent);
			this.sendAllMsg(msgModel);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	

	public EasyResult actionForAddComment() {
		EasyRecord record = new EasyRecord("yq_flow_follow","follow_id");
		try {
			JSONObject params = getJSONObject();
			String pFollowId = params.getString("pFollowId");
			String toUserId = params.getString("toUserId");
			String flowContent = params.getString("flowContent");
			String prevFollowContent = params.getString("prevFollowContent");
			String businessId = params.getString("businessId");
			String id = RandomKit.uniqueStr();
			record.setPrimaryValues(id);
			record.set("business_id",businessId);
			record.set("p_follow_id",pFollowId);
			record.set("result_id",params.getString("resultId"));
			record.set("follow_content",flowContent);
			record.set("prev_follow_content",params.getString("prevFollowContent"));
			record.set("to_user_id",toUserId);
			record.set("to_user_name",params.getString("toUserName"));
			record.set("add_time", EasyDate.getCurrentDateString());
			record.set("add_user_id", getUserId());
			record.set("add_user_name", getUserName());
			this.getQuery().save(record);
			
			boolean hasMsg = false;
			
			MessageModel model = new MessageModel();
			model.setData4(prevFollowContent);
			
			if(StringUtils.notBlank(pFollowId)) {
				this.getQuery().executeUpdate("update yq_flow_follow t1, yq_flow_follow t2 set t1.to_user_id = t2.add_user_id,t1.to_user_name = t2.add_user_name,t1.prev_follow_content = t2.follow_content where t2.follow_id = ? and t1.follow_id = ?",pFollowId,id);
				String addUserId = params.getString("addUserId");
				hasMsg = true;
				FlowApplyModel apply = ApproveService.getService().getApply(businessId);
				model.setTitle(apply.getApplyTitle()+"#新的回复");
				model.setReceiver(addUserId);
			}else if(StringUtils.notBlank(toUserId)) {
				hasMsg = true;
				FlowApplyModel apply = ApproveService.getService().getApply(businessId);
				model.setTitle(apply.getApplyTitle()+"#新的评论");
				model.setReceiver(toUserId);
			}else {
				FlowApplyModel apply = ApproveService.getService().getApply(businessId);
				if(!getUserId().equalsIgnoreCase(apply.getApplyBy())) {
					hasMsg = true;
					model.setTitle(apply.getApplyTitle()+"#新的评论");
					model.setReceiver(apply.getApplyBy());
				}
			}
			if(hasMsg) {
				model.setSender(getUserId());
				model.setDesc(flowContent);
				WxMsgService.getService().sendCommentTaskMsg(model);
				
				if(StringUtils.notBlank(prevFollowContent)) {
					model.setDesc(flowContent+"<hr>"+prevFollowContent);
				}
				model.setTplName("taskComment.html");
				EmailService.getService().sendTaskCommentEmail(model);
			}
			
			this.getQuery().executeUpdate("update yq_flow_apply set comment_count = comment_count + 1 where apply_id = ?",params.getString("businessId"));
			
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddReviseLog() {
		JSONObject params = getJSONObject();
		EasyResult result =  addReviseLog(params.getString("businessId"),params.getString("reviseContent"));
		try {
			if(result.isOk()) {
				this.getQuery().executeUpdate("update yq_flow_apply set revise_count = revise_count + 1 where apply_id = ?",params.getString("businessId"));
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return result;
	}
	
	public void actionForCannelApply() {
		String businessId = getJsonPara("businessId");
		if(StringUtils.isBlank(businessId)){
			renderJson(EasyResult.fail());
			return;
		}
		try {
			this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ? where apply_id = ?",FlowConstants.FLOW_STAT_CANNEL,businessId);
			this.addReviseLog(businessId,"作废流程");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void actionForHandupApply() {
		String businessId = getJsonPara("businessId");
		if(StringUtils.isBlank(businessId)){
			renderJson(EasyResult.fail());
			return;
		}
		try {
			this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ? where apply_id = ?",FlowConstants.FLOW_STAT_HANG_UP,businessId);
			this.addReviseLog(businessId,"挂起流程");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void actionForActiveApply() {
		String businessId = getJsonPara("businessId");
		if(StringUtils.isBlank(businessId)){
			renderJson(EasyResult.fail());
			return;
		}
		try {
			this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ? where apply_id = ?",FlowConstants.FLOW_STAT_CHECKING,businessId);
			this.addReviseLog(businessId,"激活流程");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void actionForDelApply() {
		JSONObject params = getJSONObject();
		String businessId = params.getString("businessId");
		String flowCode = params.getString("flowCode");
		if(StringUtils.isBlank(businessId)||StringUtils.isBlank(flowCode)){
			renderJson(EasyResult.fail());
			return;
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		try {
			String itemTable = flow.getItemTableName();
			if(StringUtils.notBlank(itemTable)) {
				this.getQuery().executeUpdate("delete from "+itemTable+" where business_id = ?",businessId);
			}
			String tablename = flow.getTableName();
			if(!"yq_flow_apply".equalsIgnoreCase(tablename)) {
				this.getQuery().executeUpdate("delete from "+tablename+" where business_id = ?",businessId);
			}
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id = ?",businessId);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id = ?",businessId);
			this.addReviseLog(businessId,"删除流程");
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderJson(EasyResult.ok());
		return;
	}
	
	
	public void actionForDelItems() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		try {
			this.getQuery().executeUpdate("delete from yq_flow_apply_item where item_id = ?", itemId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		renderJson(EasyResult.ok());
	}
	
   public EasyResult actionForAddFllow() {
	   return EasyResult.ok();
   }


	@Override
	public EasyResult actionForSubmitApply() {
		return null;
	}


	@Override
	public EasyResult actionForUpdateApply() {
		return null;
	}
	

}
