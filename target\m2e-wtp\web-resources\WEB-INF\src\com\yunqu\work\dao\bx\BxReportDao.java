package com.yunqu.work.dao.bx;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "BxReportDao")
public class BxReportDao extends AppDaoContext{
	
	/**
	 * 待付款
	 * @return
	 */
	@WebControl(name = "todoBxPay",type = Types.TEMPLATE)
	public JSONObject todoBxPay() {
		EasySQL sql = new EasySQL("select t1.*,t4.paper_state,t4.pay_state,t4.recorded,t4.bx_money,t4.pay_money,t4.has_pay_money,(t4.pay_money-t4.has_pay_money) do_pay_money,t4.recorded_date");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_bx_base t4 on t1.apply_id = t4.business_id");
		sql.append("where 1=1");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		String payState = param.getString("payState");
		if(StringUtils.notBlank(payState)) {
			sql.append(payState,"and t4.pay_state = ?");//待付款+部分付款
		}else {
			sql.appendIn(new int[] {1,11},"and t4.pay_state");//待付款+部分付款
		}
		sql.append(param.getString("applyName"),"and t1.apply_name = ?");
		sql.append(param.getString("payState"),"and t4.pay_state = ?");
		sql.append(param.getString("paperState"),"and t4.paper_state = ?");
		sql.append(param.getString("recorded"),"and t4.recorded = ?");
		sql.append("order by t1.apply_end_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "paperStateQuery",type = Types.TEMPLATE)
	public JSONObject paperStateQuery() {
		EasySQL sql = new EasySQL("select t1.*,t4.paper_notice_count,t4.paper_state,t4.pay_state,t4.recorded,t4.bx_money,t4.pay_money,t4.has_pay_money,(t4.pay_money-t4.has_pay_money) do_pay_money,t4.recorded_date");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_bx_base t4 on t1.apply_id = t4.business_id");
		sql.append("where 1=1");
		sql.append(param.getString("rootId"),"and t1.root_id = ?");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		
		String paperState = param.getString("paperState");
		if(StringUtils.isBlank(paperState)) {
			sql.append(1,"and t4.paper_state = ?");
		}else {
			sql.append(paperState,"and t4.paper_state = ?");
		}
		String payState = param.getString("payState");
		if(StringUtils.notBlank(payState)) {
			sql.append(payState,"and t4.pay_state = ?");
		}
		String applyBeginDate = param.getString("beginDate");
		String applyEndDate = param.getString("endDate");
		if(StringUtils.notBlank(applyBeginDate)) {
			sql.append(applyBeginDate.replaceAll("-",""),"and t1.apply_date >= ?");
		}
		if(StringUtils.notBlank(applyEndDate)) {
			sql.append(applyEndDate.replaceAll("-",""),"and t1.apply_date <= ?");
		}
		sql.append(param.getString("applyName"),"and t1.apply_name = ?");
		
		String paperNoticeCount = param.getString("paperNoticeCount");
		if("0".equals(paperNoticeCount)) {
			sql.append(paperNoticeCount,"and t4.paper_notice_count = ?");
		}else {
			sql.append(paperNoticeCount,"and t4.paper_notice_count >= ?");
		}
		
		sql.append(param.getString("recorded"),"and t4.recorded = ?");
		sql.append("order by t1.apply_time");
		if("2".equals(paperState)) {
			sql.append("desc");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 已付款
	 * @return
	 */
	@WebControl(name = "hasyPayList",type = Types.TEMPLATE)
	public JSONObject hasyPayList() {
		EasySQL sql = new EasySQL("select t1.*,t4.recorded,t4.bx_money,t4.pay_money,t4.pay_time,t4.pay_date,t4.recorded_date");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_bx_base t4 on t1.apply_id = t4.business_id");
		sql.append("where 1=1");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		sql.append(10,"and t4.pay_state = ?");//已付款
		sql.append(param.getString("recordedState"),"and t4.recorded = ?");
		sql.append(param.getString("applyName"),"and t1.apply_name = ?");
		sql.append(param.getString("payBeginDate"),"and t4.pay_date >= ?");
		sql.append(param.getString("payEndDate"),"and t4.pay_date <= ?");
		sql.append(param.getString("beginDate"),"and t4.recorded_date >= ?");
		sql.append(param.getString("endDate"),"and t4.recorded_date <= ?");
		sql.append(param.getString("recorded"),"and t4.recorded = ?");
		sql.append("order by t4.pay_time desc,t4.pay_index ");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 已入账
	 * @return
	 */
	@WebControl(name = "hasyRecorded",type = Types.TEMPLATE)
	public JSONObject hasyRecorded() {
		EasySQL sql = new EasySQL("select t1.*,t4.recorded,t4.bx_money,t4.pay_money,t4.pay_time,t4.pay_date,t4.recorded_date,t4.recorded_time");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_bx_base t4 on t1.apply_id = t4.business_id");
		sql.append("where 1=1");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		sql.append(1,"and t4.recorded = ?");//已入账
		sql.append(param.getString("applyName"),"and t1.apply_name = ?");
		sql.append(param.getString("beginDate"),"and t4.recorded_date >= ?");
		sql.append(param.getString("endDate"),"and t4.recorded_date <= ?");
		sql.append("order by t4.pay_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "personFeeStat",type = Types.TEMPLATE)
	public JSONObject personFeeStat() {
		EasySQL sql = new EasySQL("select t1.apply_name,t2.fee_in_type,t2.budget_month,sum(t2.amount) from yq_flow_apply t1,yq_flow_bx_item t2 where");
		sql.append("t1.apply_id = t2.business_id and t1.apply_state>=10");
		sql.append(param.getString("applyName"),"and t1.apply_name = ?");
		sql.append("group by t1.apply_by,t2.budget_month,t2.fee_in_type ORDER BY t2.budget_month desc,t1.apply_name");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	

}
