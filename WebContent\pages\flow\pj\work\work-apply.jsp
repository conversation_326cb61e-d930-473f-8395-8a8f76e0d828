<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>作业操作申请</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" data-template="({{:data5}}-作业申请单" value="${staffInfo.userName}${staffInfo.staffNo}未打卡补签申请单"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 100px;">审批人</td>
			                    <td>
			                    	<input type="hidden" name="apply.data2"/>
					                <input type="text" onclick="multiUser(this)" data-rules="required"  readonly="readonly" class="form-control input-sm select-icon" name="apply.data1"/>
			                    </td>
			                    <td style="width: 100px;">抄送人</td>
			                    <td>
					                <input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm select-icon" name="apply.data3"/>
			                    </td>
					  		</tr>
					  		<tr>
					  			<td class="required">所属项目</td>
					  			<td>
					  				<input name="apply.data4" type="hidden">
									<input type="text" data-rules="required" onclick="singleProject(this)" readonly="readonly" data-rules="required" name="apply.data5" class="form-control input-sm"/>
					  			</td>
					  			<td class="required">项目号</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">作业时间</td>
					  			<td>
					  				<input type="text" data-rules="required"  data-laydate="{type:'datetime',min:0}" class="form-control input-sm Wdate" name="apply.data7"/>
					  			</td>
					  			<td class="required">作业类别</td>
					  			<td>
					  				<select class="form-control input-sm" data-rules="required" name="apply.data8">
					  					<option value="">--</option>
					  					<option value="维护作业">维护作业</option>
									    <option value="BUG修复">BUG修复</option>
									    <option value="次生BUG修复">次生BUG修复</option>
									    <option value="安全整改">安全整改</option>
									    <option value="功能上线">功能上线</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td>执行结果</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.data9">
					  					<option value=""></option>
					  					<option value="成功">成功</option>
					  					<option value="失败">失败</option>
					  				</select>
					  			</td>
					  			<td>执行结果说明</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" name="apply.data10"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">作业内容</td>
					  			<td colspan="3">
									<textarea style="height: 100px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr class="fileListTr">
					  			<td>相关附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){
				Flow.initData();
			}});
		});
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='确认执行结果'){
				
				return true;
			}
			return true;
	    }
		
		Flow.ajaxSubmitForm = function(state){
			Flow.initData();
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				
			}});
		}
		
		Flow.initData = function() {
			var ccNames = '';
			var data3 = $("[name='apply.data3']").val();
			if(data3){
				ccNames = data3;
			}
			var selectUserName = $("[name='apply.data1']").val();
			var selectUserId = $("[name='apply.data2']").val();
			var params = {selectUserId:selectUserId,selectUserName:selectUserName,ccNames:ccNames};
			FlowCore.approveData = params;
		}
		
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>