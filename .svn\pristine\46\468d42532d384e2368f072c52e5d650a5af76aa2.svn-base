<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<table id="goodsStorageOutTable"></table>
<button class="btn btn-default btn-sm ml-15" onclick="goodsStockList('out')" type="button">出库单</button>
	<script type="text/javascript">
		$(function(){
			$("#OrderDetailForm").initTable({
				mars:'GoodsDao.stockList',
				id:'goodsStorageOutTable',
				page:false,
				data:{type:'20'},
				height:300,
				rowDoubleEvent:'editOutStock',
				cellMinWidth:80,
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
            	 	type: 'checkbox',
					align:'left'
				 },{
					 field:'NAME',
					 title:'物料名称'
				 },{
					 field:'NUM',
					 title:'出库数量',
					 width:70
				 },{
				    field: 'SUBJECT',
					title: '科目',
					align:'center'
				},{
				    field: 'DATE_ID',
					title: '出库日期',
					align:'center'
				},{
					field:'REMARK',
					title:'备注',
					edit:'text'
				},{
				    field: 'CREAT_NAME',
					title: '操作人',
					align:'center'
				},{
				    field: 'CREATE_TIME',
					title: '操作时间',
					align:'center'
				}
			]],done:function(data){
				$("#stockOutCount").text(data.total);
		  	}
		});
	});
	
	function editOutStock(data){
		popup.layerShow({id:'storageEdit',area:['600px','400px'],title:'修改',url:'${ctxPath}/pages/erp/goods/storage-edit.jsp',data:{title:'出库',type:'out',stockId:data['STOCK_ID']}});
	}
		
	function goodsStockList(type){
		var checkStatus = table.checkStatus('goodsStorageOutTable');
		var data = checkStatus.data;
 		var sum = data.length;
 		if(sum <= 0){
 			layer.msg('请选择物料。',{icon : 7, time : 1000});
 			return;
 		}
 		var ids = [];
 		for(var i = 0; i < sum; i ++){
			ids.push(data[i].STOCK_ID);
 		}
 		if(ids.length <= 0){
 			layer.msg('请选择。',{icon : 7, time : 1000});
 			return;
 		}
		popup.openTab({id:'out-order',title:'出库单',url:'/yq-work/pages/erp/order/include/storage-in-order.jsp',data:{orderId:orderId,stockIds:ids.join()}});
	}
	
</script>
