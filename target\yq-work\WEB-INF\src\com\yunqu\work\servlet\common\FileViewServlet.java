package com.yunqu.work.servlet.common;


import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.RandomKit.SMSAuthCodeType;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.utils.LogUtils;


/**
 * 文件预览
 */
@WebServlet(urlPatterns = { "/fileview/*" })
public class FileViewServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;
	
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)throws ServletException, IOException {
		this.doPost(req,resp);  
		
	}
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)throws ServletException, IOException {
		try{
			LogUtils.getLogger().info("fileview>"+request.getRequestURL().toString()+"?"+request.getQueryString());
			String serverName = request.getServerName();
			String url = request.getRequestURI();  
			String view = request.getParameter("view");
			String watermark = request.getParameter("watermark");
			String id = url.substring(url.indexOf("/fileview")+10);
		    EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
		    JSONObject jsonObject = query.queryForRow("select * from yq_files where file_id = ?", new Object[]{id}, new JSONMapperImpl());
			if(jsonObject==null) {
			   Render.renderHtml(request, response, "file not exist.");
			   return;
			}
		    String diskPath = jsonObject.getString("DISK_PATH");
		    String accessUrl = jsonObject.getString("ACCESS_URL");
			String filename = jsonObject.getString("FILE_NAME");
			String fileType = jsonObject.getString("FILE_TYPE");
			String fkId = jsonObject.getString("FK_ID");
			String folderId = jsonObject.getString("FOLDER_ID");
			String source = jsonObject.getString("SOURCE");
			String creator =jsonObject.getString("CREATOR");
		
			if(request.getUserPrincipal()!=null) {
				UserPrincipal principal =(UserPrincipal)request.getUserPrincipal();
				StaffModel currentUser = StaffService.getService().getStaffInfo(principal.getUserId());
				if(!principal.isSuperUser()) {
					if("yqproject".equals(source)&&(currentUser.getUserId().equals(currentUser.getDeptLeader())||principal.isRole("PROJECT_MANAGER")||principal.isRole("DEPT_MGR"))) {
						LogUtils.getLogger().info("yqproject部门主管>"+diskPath+"文件可直接下载");
					}else if(StringUtils.notBlank(folderId)&&!"0".equals(folderId)) {
						JSONObject folderInfo = query.queryForRow("select dl_auth,dept_id from yq_folder where folder_id = ?", new Object[] {folderId},new JSONMapperImpl());
						if(folderInfo!=null) {
							int dlAuth = folderInfo.getIntValue("DL_AUTH");
							if(dlAuth==1&&!creator.equals(principal.getUserId())) {
								Render.renderHtml(request, response, "权限不足，请联系管理员或文件拥有人");
								return;	
							}
							if(dlAuth==2) {
								StaffModel fileUser = StaffService.getService().getStaffInfo(creator);
								if(!fileUser.getDeptId().equalsIgnoreCase(currentUser.getDeptId())) {
									Render.renderHtml(request, response, "权限不足，您不是这个文件拥有人部门成员");
									return;	
								}
							}
						}
					}
				}
			}
			
			File file =  getFile(diskPath);
		   	if(file==null||!file.exists()){
		   		Render.renderHtml(request, response, "file not exist.");
		   		return;
		   	}
			
		   	if("office".equals(view)){
		   		request.setAttribute("fileType",fileType.substring(1));
		   		request.setAttribute("accessUrl",accessUrl+"&online=1&filename="+filename+"&fileType="+fileType);
				request.setAttribute("obj",jsonObject);
				request.setAttribute("filename",filename);
				request.setAttribute("key",RandomKit.smsAuthCode(15,SMSAuthCodeType.CharAndNumbers));
	   			request.getRequestDispatcher("/pages/doc/office-online.jsp").forward(request, response);
				return;
			}
		   	if("1".equals(watermark)&&fileType.startsWith(".pdf")){
		   		request.setAttribute("fileType",fileType.substring(1));
		   		String _url  = "/yq-work/fileview/"+id+"?online=1&view=online&filename="+filename+"&fileType="+fileType;
		   		request.setAttribute("accessUrl",_url);
		   		request.setAttribute("obj",jsonObject);
		   		request.setAttribute("filename",filename);
		   		request.setAttribute("lookTime",EasyDate.getCurrentDateString());
		   		request.setAttribute("staffInfo",StaffService.getService().getStaffInfo(request.getUserPrincipal().getName()));
		   		request.setAttribute("key",RandomKit.smsAuthCode(15,SMSAuthCodeType.CharAndNumbers));
		   		request.getRequestDispatcher("/pages/doc/pdf-watermark.jsp").forward(request, response);
		   		return;
		   	}
		   	if(request.getRemoteUser()!=null) {
		   		query.executeUpdate("update yq_files set download_count = download_count +1 where file_id = ?", id);
		   		
		   		EasyRecord record=new EasyRecord("yq_file_download_log","log_id");
		   		record.setPrimaryValues(RandomKit.uuid());
		   		record.set("file_id",id);
		   		record.set("fk_id",fkId);
		   		record.set("ip",WebKit.getIP(request));
		   		record.set("user_agent",request.getHeader("User-Agent"));
		   		record.set("download_by",request.getUserPrincipal().getName());
		   		record.set("download_by_name",StaffService.getService().getUserName(request.getUserPrincipal().getName()));
		   		record.set("download_time",EasyDate.getCurrentDateString());
		   		query.save(record);
		   	}
		   
		   	boolean bl=FileKit.imageFormat(filename);
		   	if(bl){
		   		Render.renderImages(response, file);
		   	}else if("html".equals(view)){
		   		redirectFile(response,filename,file,fileType,false);
	   			return;
		   	}else if("online".equals(view)){
		   		if(isOfficeFile(fileType)&&!"work".equals(serverName)){
		   			response.sendRedirect(url+"?view=office");
		   			return;
		   		}else {
		   			redirectFile(response,filename,file,fileType,true);
		   			return;
		   		}
		   	}else{
		   		redirectFile(response,filename,file,fileType,false);
		   		return;
		   	}
		}catch(Exception ex){
			LogUtils.getLogger().error(ex.getMessage(),ex);
			response.sendError(500,"file not exist.");
		}
	}
	
	private boolean isOfficeFile(String fileType) {
		if(fileType.startsWith(".doc")||fileType.startsWith(".xls")||fileType.startsWith(".ppt")||fileType.startsWith(".csv")) {
			return true;
		}
		return false;
	}
	
	private void redirectFile( HttpServletResponse response,String filename,File file,String fileType,boolean view) {
		String contentType = getContentType(fileType);
		if(contentType==null) {
			view = false;
		}
		if(StringUtils.notBlank(filename)){
			 if(view) {
				 response.addHeader("Content-Disposition","inline;filename=" +toUtf8String(filename));
			 }else {
	 	   	    response.setHeader("Content-disposition","attachment;filename=" +toUtf8String(filename));
			 }  
		   	 if(file!=null){
		   		 response.setHeader("Content-Length",file.length()+"");
		   	 }
		   	 if("text/plain".equals(contentType)) {
		   		 response.setHeader("Content-Type", contentType+";charset=GBK");
		   	 }else {
		   		 response.setHeader("Content-Type", contentType+";charset=UTF-8");
		   	 }
	   	}
   		try {
			FileUtils.copyFile(file, response.getOutputStream());
		} catch (IOException ex) {
			LogUtils.getLogger().error(ex.getMessage(),ex);
		}
	}

	
	public static String getContentType(String filePath){
	    Path path = Paths.get(filePath);  
	    String contentType = null;  
	    try {  
	        contentType = Files.probeContentType(path);  
	    } catch (IOException ex) {  
	    	LogUtils.getLogger().error(ex.getMessage(),ex);  
	    }
	    return contentType; 
	}
	/**
	 * 处理附件为中文名的问题
	 * @param fn
	 * @return
	 */
	private String toUtf8String(String fn) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < fn.length(); i++) {
			char c = fn.charAt(i);
			if (c >= 0 && c <= 255) {
				sb.append(c);
			} else {
				byte[] b;
				try {
					b = Character.toString(c).getBytes("utf-8");
				} catch (Exception ex) {
					// System.out.println(ex);
					b = new byte[0];
				}
				for (int j = 0; j < b.length; j++) {
					int k = b[j];
					if (k < 0)
						k += 256;
					sb.append("%" + Integer.toHexString(k).toUpperCase());
				}
			}
		}
		return sb.toString();
	}
	
	
	//如果文件存在直接返回
	private File getFile(String uri){
	  File distFile =  new File(getBaseDir()+uri);
	  if(distFile.isFile()) return distFile;
	  return null;
	}
	
	private static String getBaseDir() {
		String basePath = AppContext.getContext(Constants.APP_NAME).getProperty("basePath", "/home/<USER>/");
		return basePath+"files/";
	}
	
	
}