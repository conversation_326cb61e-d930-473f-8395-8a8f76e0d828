<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>nav</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="UserNavDao.record" autocomplete="off" data-mars-prefix="userNav.">
     		   <input type="hidden" value="${param.navId}" name="navId"/>
     		   <input type="hidden" value="${param.navId}" name="userNav.NAV_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td class="required">访问URL</td>
		                    <td><input value="" data-rules="required"  type="text" name="userNav.URL" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td style="width: 70px" class="required">网站名称</td>
		                    <td><input data-rules="required"  type="text" name="userNav.TITLE" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td>备注</td>
		                    <td>
			                   	 <textarea name="userNav.REMARK" placeholder="账号密码等信息" style="height: 150px" class="form-control input-sm"></textarea>
		                    </td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				 		  <c:if test="${!empty param.navId}">
						    	  <button type="button" class="btn btn-info btn-sm"  onclick="Edit.del()"> 删除 </button>
				 		  </c:if>
				    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("Edit");
		Edit.navId='${param.navId}';
		
		$(function(){
			$("#editForm").render({success:function(){
				
			}});  
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.navId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/userNav?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadNav();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/userNav?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						reloadNav();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		};
		Edit.del=function(data){
			layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/userNav?action=delete",{'userNav.NAV_ID':Edit.navId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							reloadNav();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>