package com.yunqu.work.service;

import java.sql.SQLException;

import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;

public class MessageService extends AppBaseService{
	private static class Holder{
		private static MessageService service=new MessageService();
	}
	public static MessageService getService(){
		return Holder.service;
	}
	
	public void sendMsg(MessageModel model){
		String id = RandomKit.uuid();
		model.setPrimaryValues(id);
		model.setCreateTime(EasyDate.getCurrentDateString());
		try {
			this.getQuery().save(model);
			this.getQuery().executeUpdate("update yq_message t1,"+Constants.DS_MAIN_NAME+".easi_user t2 set t1.RECEIVE_NAME = t2.USERNAME where t1.RECEIVER = t2.USER_ID and t1.msg_id = ?", id); 
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
	}

}
