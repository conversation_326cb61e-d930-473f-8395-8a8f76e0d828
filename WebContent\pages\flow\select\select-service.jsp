<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectServiceForm">
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 180px">
					 <span class="input-group-addon">名称</span>	
					 <input type="text" name="serviceName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-5"  data-event="enter"  onclick="SelectService.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		</div>
            	<div class="ibox">
              		<table id="serviceList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectService.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectService = {
					sid:'${param.sid}',
					query : function(){
						$("#selectServiceForm").queryData();
					},
					initTable : function(){
						$("#selectServiceForm").initTable({
							mars:'ServiceDao.list',
							id:'serviceList',
							page:true,
							limit:50,
							rowDoubleEvent:'SelectService.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'SERVICE_NAME',
								title: '产品名称'
							},{
							    field: 'SERVICE_VERSION',
								title: '当前版本号',
								width:120
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectService.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('serviceList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['SERVICE_NAME']);
									ids.push(data[index]['SERVICE_ID']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								popup.layerClose("selectServiceForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
								if(el.prev().prev().length>0){
									el.prev().prev().val('');
								}
							}
						}else{
							el.val(selectRow['SERVICE_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['SERVICE_ID']);
							}
							popup.layerClose("selectServiceForm");
						}
					}
					
			};
			$(function(){
				SelectService.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>