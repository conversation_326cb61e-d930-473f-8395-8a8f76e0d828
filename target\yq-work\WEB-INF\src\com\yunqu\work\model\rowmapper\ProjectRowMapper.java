package com.yunqu.work.model.rowmapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;


public class ProjectRowMapper implements EasyRowMapper<ProjectDomain> {

	@SuppressWarnings("unchecked")
	@Override
	public ProjectDomain mapRow(ResultSet rs, int rowNum) {
		ProjectDomain vo = new ProjectDomain();
		try {
			vo.setProjectId(rs.getString("PROJECT_ID"));
			vo.setProjectName(rs.getString("PROJECT_NAME"));
			vo.setProjectNo(rs.getString("PROJECT_NO"));
			vo.setWebhook(rs.getString("WEBHOOK"));
			vo.setPo(rs.getString("PO"));
			vo.setPoName(rs.getString("PO_NAME"));
			vo.setProjectPo(rs.getString("PROJECT_PO"));
			vo.setProjectPoName(rs.getString("PROJECT_PO_NAME"));
			vo.setSalesBy(rs.getString("SALES_BY"));
			vo.setSalesByName(rs.getString("SALES_BY_NAME"));
			vo.setPreSalesBy(rs.getString("PRE_SALES_BY"));
			vo.setPreSalesName(rs.getString("PRE_SALES_NAME"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
	}

}
