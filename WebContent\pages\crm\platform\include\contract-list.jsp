<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<table id="contractTable" class="layui-table"></table>

<script type="text/javascript">
    var contractList = {
        init: function(){
            $("#PlatformDetailForm").initTable({
                mars:'ProjectContractDao.platformContractList',
                id:'contractTable',
                page:true,
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align:'left'
                    },{
                        field: 'CONTRACT_NAME',
                        title: '合同名称',
                        align:'left',
                        minWidth:150,
                        style:'color:#1E9FFF;cursor:pointer',
                        event:'contractList.detail'
                    },{
                        field: 'CONTRACT_NO',
                        title: '合同号',
                        width:100
                    },{
                        field: 'CUSTOMER_NAME',
                        title: '客户名称',
                        minWidth:150,
                        templet:'<div><a href="javascript:;" onclick="custDetail(\'{{d.CUST_ID}}\')">{{d.CUSTOMER_NAME}}</a></div>'
                    },{
                        field: 'AMOUNT',
                        title: '合同金额',
                        width:90
                    },{
                        field: 'SALES_BY_NAME',
                        title: '销售经理',
                        width:100
                    },{
                        field: 'SIGN_DATE',
                        title: '签订时间',
                        width:120
                    },{
                        field: 'CREATE_TIME',
                        title: '创建时间',
                        width:120
                    }
                ]],
                done:function(res){
                    if(res.totalRow != undefined){
                        $("[name='extend.CONTRACT_COUNT']").text(res.totalRow);
                    }
                }
            });
        },
        detail: function(data){
            popup.openTab({
                id:'contractDetail',
                title:'合同详情',
                url:"${ctxPath}/project/contract",
                data:{contractId:data.CONTRACT_ID,custId:data.CUST_ID,isDiv:0}
            });
        },
    };
</script>
