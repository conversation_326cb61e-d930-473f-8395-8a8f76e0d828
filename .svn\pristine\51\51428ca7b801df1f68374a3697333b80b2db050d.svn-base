package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

/**
 * <AUTHOR>
 */
@WebObject(name = "ContractStageDao")
public class ContractStageDao extends AppDaoContext {

    @WebControl(name = "stageList", type = Types.LIST)
    public JSONObject stageList() {
        EasySQL sql = getEasySQL("select t1.* from yq_contract_stage t1 ");
        sql.append("where 1=1");
        sql.append(param.getString("contractId"), "and t1.CONTRACT_ID = ?");
        sql.append(param.getString("stageName"), "and t1.STAGE_NAME = ?");
        sql.append(param.getString("rcvDate"), "and t1.RCV_DATE = ?");
        sql.append(param.getString("planCompDate"), "and t1.PLAN_COMP_DATE = ?");
        sql.append(param.getString("actCompDate"), "and t1.ACT_COMP_DATE = ?");
        sql.append(param.getString("incomeStageId"), "and t1.INCOME_STAGE_ID = ?");
        sql.appendLike(param.getString("ykWbCond"),"and t1.YK_WB_COND like ?");
        setOrderBy(sql, " order by t1.CREATE_TIME desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "stageListForSelect", type = Types.LIST)
    public JSONObject stageListForSelect() {
        EasySQL sql = getEasySQL("select t1.* from yq_contract_stage t1 ");
        sql.append("where 1=1");
        if (StringUtils.isBlank(param.getString("contractId"))){
            JSONObject jsonObject =  new JSONObject();
            jsonObject.put("error","无合同Id,请选择合同");
            return getJsonResult( new JSONObject());
        }
        sql.append(param.getString("contractId"), " and t1.CONTRACT_ID = ?");
        sql.append(param.getString("stageName"), " and t1.STAGE_NAME = ?");

        if (!isSuperUser() && !hasRole("CONTRACT_MGR") && !hasRes("CONTRACT_LOOK_AUTH")) {
            sql.append("and (");
            sql.append(getUserId(), "(t1.CREATOR = ?)");
            sql.append("or");
            sql.append(getUserId(), "(t1.UPDATE_BY = ?)");
            sql.append(")");
        }

        if (StringUtils.notBlank(param.getString("incomeStageId"))){
            sql.append("and ( t1.INCOME_STAGE_FLAG = 0");
            sql.append(param.getString("incomeStageId"), " OR t1.INCOME_STAGE_ID = ? )");
        }else {
            sql.append("and t1.INCOME_STAGE_FLAG = 0");
        }
        setOrderBy(sql, " order by t1.INCOME_STAGE_FLAG desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "record", type = Types.RECORD)
    public JSONObject record() {
        String stageId = param.getString("contractStage.STAGE_ID");
        if (StringUtils.notBlank(stageId)) {
            return queryForRecord("select * from yq_contract_stage where stage_id = ?", stageId);
        } else {
            return getJsonResult(new JSONObject());
        }
    }

    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by t1.").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }

    @WebControl(name = "selectStage", type = Types.DICT)
    public JSONObject selectStage() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("select STAGE_ID, CONCAT(STAGE_NAME, '-比例:',RATIO,' % -金额: ', PLAN_RCV_AMT) AS STAGE_DESC from yq_contract_stage where 1=1");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and CONTRACT_ID = ?");
        sql.append("order by STAGE_NAME");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "selectStageSimple", type = Types.DICT)
    public JSONObject selectStageSimple() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("select STAGE_ID, CONCAT(STAGE_NAME,' -金额: ', PLAN_RCV_AMT) AS STAGE_DESC from yq_contract_stage where 1=1");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and CONTRACT_ID = ?");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }
}
