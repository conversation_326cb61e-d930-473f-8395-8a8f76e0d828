package com.yunqu.work.servlet.erp;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/fa/*")
public class FaServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_fa_data","fa_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	
	public EasyResult actionForAdd(){
		EasyRecord model = getModel("fa");
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_NAME", getUserName());
		try {
			model.remove("FA_ID");
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("fa");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDel(){
		try {
			this.getQuery().executeUpdate("delete from yq_fa_data where fa_id = ?", getJsonPara("faId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForGetFaUserId(){
		try {
			JSONObject params = getJSONObject();
			String userName = params.getString("userName");
			if(StringUtils.isBlank(userName)) {
				return EasyResult.fail();
			}
			EasySQL sql = new EasySQL();
			sql.append("select * from easi_user where 1=1");
			
			sql.appendLike(userName,"and USERNAME like ?");
			sql.appendLike(params.getString("userName"),"or DATA1 like ?");
		    JSONObject row  = this.getMainQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		    return EasyResult.ok(row);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.fail();
	}
	
	
	public EasyResult actionForAddCheck(){
		JSONObject params = getJSONObject();
		
		JSONArray checkIds = new JSONArray();
		Object _checkIds = params.get("checkIds");
		if(_checkIds instanceof String) {
			checkIds.add(_checkIds.toString());
		}else {
			checkIds = params.getJSONArray("checkIds");
		}
		for(int i=0;i<checkIds.size();i++) {
			String id = checkIds.getString(i);
			EasyRecord model = new EasyRecord("YQ_FA_CHECK","check_id");
			model.set("check_id", id);
			model.set("fa_use_state", params.getString("state_"+id));
			model.set("fa_use_people", params.getString("usePeople_"+id));
			model.set("fa_location", params.getString("location_"+id));
			model.set("fa_check_remark", params.getString("remark_"+id));
			model.set("fa_url", params.getString("url_"+id));
			model.set("file_id", params.getString("file_"+id));
			model.set("submit_ip", WebKit.getIP(getRequest()));
			model.set("submit_time", EasyDate.getCurrentDateString());
			model.set("submit_name", getUserName());
			try {
				this.getQuery().update(model);
			} catch (SQLException e) {
				this.error(null, e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}
	

}
