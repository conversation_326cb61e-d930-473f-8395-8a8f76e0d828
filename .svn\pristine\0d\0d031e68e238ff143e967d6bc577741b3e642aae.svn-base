<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<link href="${ctxPath}/static/css/sidebar.css" rel="stylesheet">
	<title>我的申请</title>
	<style type="text/css">
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		  <div class="page-box">
	  		 <div class="left-sidebar" style="display: none;">
                <div class="left-sidebar-item" data-mars="FlowDao.myFlowApplyTree" data-template="template-list"></div>
       		 </div>
        	<div onclick="leftShow(this)" style="position: absolute;display: inline-flex;align-items: center;height: 50px;background-color: #00abfc;top:40%;cursor: pointer;color: #fff;"><i class="fa fa-chevron-right" aria-hidden="true"></i></div>
  			<div class="right-content" style="width: 100%;margin-left: 0px;">
  			  <div class="ibox" >
				<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="flowMgrForm">
					<input type="hidden" name="flowCode" value="${flowCode}"/>
				   <div class="ibox-title clearfix">
          		 		<div class="pull-left layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: 0px;margin-bottom: -8px;">
          		 	 		<ul class="layui-tab-title"><li data-val="" class="layui-this">全部</li><li data-val="10,20,21">进行中</li><li data-val="30">已结束</li></ul>
          		 	 	</div>
						<div class="input-group input-group-sm pull-right">
							 <button type="button" id="conditionFiter" class="btn btn-sm btn-default"><span class="glyphicon glyphicon-filter"></span> 高级查询</button>
						</div>
	                </div> 
					<div class="ibox-content">
						 <table id="_flowTable" style="margin-top: 3px;"></table>
					</div>
				  </form>
				</div>
				<div class="flowQuery"></div>
  		</div>
 	 </div>
 	 
 	 <script id="queryCondition" type="text/x-jsrender">
		<form id="condtionForm">
			<table class="table table-edit table-vzebra">
			 <tr>
				<td>申请编号</td>
				<td>
					<input class="form-control input-sm" name="applyNo">
				</td>
				<td>流程名称</td>
				<td>
					<input class="form-control input-sm" name="flowName">
				</td>
			</tr>
			 <tr>
				<td>待办人</td>
				<td>
					<input class="form-control input-sm" name="checkName">
				</td>
				<td>流程状态</td>
				<td>
					<select name="applyState" onchange="flowList.queryData();" class="form-control input-sm">
					  <option value="">请选择 </option>
					  <option data-class="label label-default" value="0">草稿</option>
					  <option data-class="label label-warning" value="1">作废</option>
					  <option data-class="label label-warning" value="10">待审批</option>
					  <option data-class="label label-info" value="20">审批中</option>
					  <option data-class="label label-danger" value="21">审批退回</option>
					  <option data-class="label label-success" value="30">已完成</option>
				   </select>	
				</td>
			</tr>
			 <tr>
				 <td style="width:80px!important;">发起日期</td>	
				 <td style="width: 35%;">
				   <input type="text" name="applyBeginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 108px;display: inline-block">
				   <input type="text" name="applyEndDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 108px;display: inline-block">
				 </td>
				 <td>紧急程度</td>
				<td>
					<select class="form-control input-sm" name="applyLevel">
						<option value="正常">正常</option>
						<option value="重要">重要</option>
						<option value="紧急">紧急</option>
					</select>
				</td>
			 </tr>
			<tr>
				<td  colspan="4">
					<button type="button" class="btn btn-sm btn-default" onclick="flowList.conditionReset()"><span class="fa fa-eraser"></span> 清空</button>
					<button type="button" class="btn btn-sm btn-info ml-10" onclick="flowList.queryData()"><span class="glyphicon glyphicon-search"></span> 查询</button>
				</td>
			</tr>
		</table>
	  </form>
    </script>
	 <script id="template-list" type="text/x-jsrender">
			{{for flowCategory}}
 				<div class="sidebar-item">
					<a href="javascript:void(0);" class="sidebar-nav-sub-title active"><i class="sidebar-icon glyphicon glyphicon-menu-right"></i>{{:P_FLOW_NAME}}</a>
				    <ul class="sidebar-nav-sub">
						{{for #parent.parent.data.data}}
							{{if #parent.parent.data.P_FLOW_CODE==P_FLOW_CODE}}
						  		<li>
								  <a href="javascript:void(0)" data-url="{{:QUERY_URL}}" data-code="{{:FLOW_CODE}}">{{:FLOW_NAME}}({{:COUNT}})</a>
						  	  </li>
							{{/if}}
  						 {{/for}}
				    </ul>
				</div>
			{{/for}}
			{{if flowCategory.length==0}}
				<p class="text-c mt-30">无数据</p>
			{{/if}}
 		</script>
		   <script type="text/x-jsrender" id="bar">
				{{if APPLY_STATE=='21'}}
 					<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="flowList.flowEdit" href="javascript:;">重新提交</a>
				{{else}}
 					 <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="flowList.flowDetail" href="javascript:;">详情</a>
 				{{/if}}
		  </script>
		 <script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default" lay-event="flowList.refreshData"><i class="fa fa-refresh" aria-hidden="true"></i> 刷新</button>
				<div class="select-btn" style="display:none;">
			 	<button class="btn btn-sm btn-info edit-btn ml-10" lay-event="flowList.flowEdit"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> 修改</button>
			 	<button class="btn btn-sm btn-warning ml-10" lay-event="flowList.cannelApply"><i class="fa fa-recycle" aria-hidden="true"></i> 作废</button>
			 	<button class="btn btn-sm btn-danger del-btn ml-10" lay-event="flowList.delApply"><i class="fa fa-trash-o" aria-hidden="true"></i> 删除</button>
 				<EasyTag:res resId="copyFlow">
			 		<button class="btn btn-sm btn-default ml-10 hidden" lay-event="copyData"><i class="fa fa-clipboard" aria-hidden="true"></i> 复制</button>
 				</EasyTag:res>
			 	<button class="btn btn-sm btn-default ml-10" lay-event="flowList.approveList"><i class="fa fa-tasks" aria-hidden="true"></i> 过程</button>
			 	<button class="btn btn-sm btn-default ml-10" lay-event="flowList.urge"><i class="fa fa-bullhorn" aria-hidden="true"></i> 催办</button>
			 	<button class="btn btn-sm btn-default ml-10" lay-event="flowList.copyUrl"><i class="fa fa-external-link" aria-hidden="true"></i> 复制链接</button>
 				</div>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
			var flowList = {};
	
			$(function(){
				$(".page-box").render({success:function(){
					intiCondition();
					flowList.flowInitList();
					flowList.renderTree();
					loadFlowNum();
				}});
			});
			
			
			function intiCondition(){
			  var queryCondition = $.templates("#queryCondition");
			  layui.dropdown.render({
				    elem: '#conditionFiter'
				    ,content: queryCondition.render({})
				    ,style: 'width: 700px; height: 250px; padding: 15px 15px;border-radius: 6px;box-shadow: 2px 2px 30px rgb(0 0 0 / 12%);'
				    ,ready: function(){
				    	renderDate('#condtionForm');
				    }
			   });
			  layui.element.on('tab(stateFilter)', function(){
				  flowList.queryData({applyState:this.getAttribute('data-val')});
			  });
			}
			 
			flowList.renderTree = function(){
				$('body').on('click','.sidebar-nav-sub-title',function(e){
	    			$(this).toggleClass('active')
	    		})
				$(".left-sidebar-item [data-code]").click(function(){
	    			var t=$(this);
	    			var flowCode = t.data("code");
	    			var queryUrl = t.data("url");
	    			if(queryUrl){
	    				$.get(queryUrl,{flowCode:flowCode,isDiv:'1'},function(html){
		  					$(".ibox").hide();
		  					$(".flowQuery").html(html);
		  					$(".flowQuery .container-fluid").removeClass('container-fluid');
	    				});
	    				return;
	    			}
	    			if(flowCode){
	    				$(".flowQuery").empty();
	    				$(".ibox").show();
	    				$("[name='flowCode']").val(flowCode);
		    			$(".left-sidebar-item a").removeClass("activeItem");
		    			t.addClass("activeItem");
		    			flowList.queryData();
	    			}
	    		});
			}
			
			flowList.flowInitList = function(){
				$("#flowMgrForm").initTable({
					mars:'FlowDao.myFlowApply',
					id:'_flowTable',
					page:true,
					limit:50,
					toolbar:'#btnBar',
					rowDoubleEvent:'flowList.flowDetail',
					height:'full-95',
					rowEvent:'rowEvent',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'radio',
						title: '序号',
						align:'left'
					 },{
						field:'',
						title:'操作',
						align:'center',
						width:70,
						templet:function(row){
							return renderTpl('bar',row);
						 }
					},{
						field:'APPLY_STATE',
						title:'当前状态',
						width:80,
						templet:function(row){
							var val = row['APPLY_STATE'];
							return flowApplyState(val);
						}
					},{
						 field:'APPLY_NO',
						 title:'申请编号',
						 width:230,
						 templet:function(row){
							 return row['APPLY_NO']+"_"+row['FLOW_NAME'];
						 }
					 },{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:200
					 },{
						 field:'APPLY_LEVEL',
						 title:'紧急程度',
						align:'center',
						 width:70,
						 templet:function(row){
							return row['APPLY_LEVEL'];
						}
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					},{
						field:'NODE_NAME',
						title:'当前节点',
						width:80
					},{
						field:'CHECK_NAME',
						title:'当前审批人',
						width:80,
						templet:function(row){
							return row['CHECK_NAME'];
						}
					}
				]],done:function(){
					$('.select-btn').hide();
					$("tbody [data-field='APPLY_LEVEL'][data-content='紧急']").css('background-color','#f6f1c0');
					table.on('radio(_flowTable)',function(obj){
						$('.select-btn').css('display','inline-block');
						var data = obj.data;
						var applyState = data['APPLY_STATE'];
						if(applyState=='0'||applyState=='21'){
							$('.del-btn,.edit-btn').show();
						}else{
							$('.del-btn,.edit-btn').hide();
						}
					});
			  	}
			});
		 }
		
		 flowList.flowDetail = function(data){
			var flowCode = data['FLOW_CODE'];
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		flowList.flowEdit = function(dataList){
		    if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    var applyState = data['APPLY_STATE'];
			if(applyState=='0'||applyState=='10'){
				var flowCode = data['FLOW_CODE'];
				var json  = $.extend({handle:'edit',mode:'edit'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE']});
				popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
			}else{
				layer.msg('草稿或没审批过的申请才能修改',{icon:2,offset:'20px',time:1000});
			}
		}
		
		flowList.queryData = function(params){
			if (params === undefined) params = {};
			var data = form.getJSONObject("#condtionForm");
			var d = $.extend({},data,params);
			$("#flowMgrForm").queryData({id:'_flowTable',data:d});
		}
		
		flowList.conditionReset = function(){
			$("#condtionForm")[0].reset();
			flowList.queryData();
		}
			
		flowList.copyData = function(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
			var data = dataList[0];
			var flowCode = data['FLOW_CODE'];
			if(flowCode!='finance_bx'){
				layer.msg('该流程暂不支持复制功能');
				return;
			}
			var json  = $.extend({copyData:'1'},{businessId:data['APPLY_ID'],flowCode:flowCode,applyState:data['APPLY_STATE']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
	   }
	   
		flowList.copyUrl = function(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
			var data = dataList[0];
			var flowCode = data['FLOW_CODE'];
			var id = data['FLOW_CODE'];
			var url  = "http://"+location.host+'${ctxPath}/web/flow/'+data['APPLY_ID']+"#"+data['APPLY_TITLE'];
			
			var textarea = document.createElement('textarea');
		    document.body.appendChild(textarea);
		    textarea.style.position = 'fixed';
		    textarea.style.clip = 'rect(0 0 0 0)';
		    textarea.style.top = '10px';
		    textarea.value = url;
		    textarea.select();
		    document.execCommand('copy', true);
		    document.body.removeChild(textarea);
		    
		    top.layer.alert(url,{icon:1,offset:'80px',title:'复制成功'},function(index){
				top.layer.close(index);
			});
	   }
	   
		flowList.urge = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    var applyState = data['APPLY_STATE'];
			if(applyState=='10'||applyState=='20'){
			    var title = data['APPLY_TITLE'];
			    var businessId = data['APPLY_ID'];
			    var resultId = data['NEXT_RESULT_ID'];
			    var staffInfo = getStaffInfo();
			    var content = '流程：'+title+'，已达你处，请尽快办理，谢谢！--'+staffInfo.userName;
			    layer.prompt({formType: 2,value: content,offset:'20px',title: '请输入催办信息',area: ['400px', '150px']}, function(msgContent, index, elem){
		    	 	layer.close(index);
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=addUrge",{resultId:resultId,businessId:businessId,msgContent:msgContent},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg)
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
		    	});
			}else{
				layer.msg('只有审批中的流程才能催办。');
				return;
			}
		    
	   }
	   
	   flowList.approveList = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    var businessId = data['APPLY_ID'];
		   popup.layerShow({url:'/yq-work/pages/flow/comm/approve-progress.jsp',full:fullShow(),area:['80%','400px'],scrollbar:false,offset:'20px',data:{businessId:businessId},id:'approveList',title:'流程记录'});
	   }
	   
	   flowList.refreshData = function(){
		   location.reload();
	   }
	   
	   flowList.delApply = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
			var applyState = data['APPLY_STATE'];
			if(applyState!='0'){
				layer.msg('只有草稿状态才可以删除');
				return;
			}
			layer.confirm('确认删除吗,不可恢复',{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/web/flow/delApply",{businessId:data['APPLY_ID'],flowCode:data['FLOW_CODE']},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,function(){
							flowList.queryData();
						})
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
	   }
	   
	   flowList.cannelApply = function(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
			var applyState = data['APPLY_STATE'];
			if(applyState>=10&&applyState<30){
				layer.confirm('确认作废吗,不可恢复',{icon:3,offset:'20px'},function(){
					ajax.remoteCall("${ctxPath}/web/flow/cannelApply",{businessId:data['APPLY_ID']},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,function(){
								flowList.queryData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			}else{
				layer.msg('该流程状态不支持作废');
				return;
			}
	   }
	   
	   function leftShow(el){
		   var flag = $(el).data('flag');
		   if(flag=='1'){
			   $(el).prev().hide(0);
			   $(el).next().css('width','100%').css('margin-left','0px');
			   $(el).data('flag','0');
			   $(el).html('<i class="fa fa-chevron-right"></i>');
		   }else{
			   $(el).prev().show(300);
			   $(el).next().css('width','calc(100% - 160px)');
			   $(el).data('flag','1');
			   $(el).html('<i class="fa fa-chevron-left"></i>');
		   }
	   }
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>