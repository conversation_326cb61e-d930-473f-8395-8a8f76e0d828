<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">评审编号</td>
					  			<td>
					  				<input type="hidden" name="apply.relatedFlow"/>
					  				<input type="text" onclick="selectReview(this)" placeholder="点击此选择" readonly="readonly" class="form-control input-sm" name="apply.data1"/>
					  			</td>
					  			<td class="required">合同类型</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">合同名称</td>
					  			<td>
					  				<input name="apply.data16" type="hidden"/>
					  				<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  			<td class="required">产品线</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">客户名称</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td>销售经理</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">签约单位</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  			<td>签订日期</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">工程大区</td>
					  			<td>
					  				<input name="apply.data17" type="hidden"/>
					  				<input type="text" onclick="singleDept(this);" data-dept-name="工程"  data-ref-Dept-Leader-name="#dn" class="form-control input-sm" name="apply.data9"/>
					  			</td>
					  			<td class="required">工程大区主管</td>
					  			<td>
					  				<input type="text" data-rules="required" onclick="singleUser(this)" id="dn" class="form-control input-sm" name="apply.data10"/>
					  			</td>
					  		</tr>
					  		
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>相关附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  		<tr style="display: none;" class="approveInfo">
					  			<td>注意事项</td>
					  			<td colspan="3" style="color: red;">如当前项目确认包含您部门所在产品线实施内容,请务必选择对应负责人再保存提交审批，否则可以忽略直接提交通过流程</td>
					  		</tr>
					  </table>
					  <table  class="table table-vzebra flow-table mt-10 edit-remove">
				  		 <tr class="edit-remove">
				  			<td>项目经理</td>
				  			<td>
				  				<input type="text" onclick="singleUser(this);" placeholder="选择自己产品线负责人,保存再提交审批"  class="form-control input-sm select-icon" name="apply.data13"/>
				  			</td>
				  			<td>UC开发负责人</td>
				  			<td>
				  				<input type="text" onclick="multiUser(this);" placeholder="选择自己产品线负责人,保存再提交审批"  class="form-control input-sm select-icon" name="apply.data15"/>
				  			</td>
				  			
				  		  </tr>
					  	  <tr class="edit-remove">
					  			<td style="width: 120px;">CC开发负责人</td>
					  			<td style="width: 40%">
					  				<input type="text" onclick="multiUser(this);" placeholder="选择自己产品线负责人,保存再提交审批" class="form-control input-sm select-icon" name="apply.data11"/>
					  			</td>
					  			<td style="width: 120px;">AI开发负责人</td>
					  			<td>
					  				<input type="text" onclick="multiUser(this)" placeholder="选择自己产品线负责人,保存再提交审批"  class="form-control input-sm select-icon" name="apply.data12"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td>其他负责人</td>
					  			<td colspan="3">
					  				<input type="text" onclick="multiUser(this)" placeholder="售前,测试训练师等,选择自己产品线负责人,保存再提交审批"  class="form-control input-sm select-icon" name="apply.data14"/>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideUserTitle:true,title:'项目任命书-{{:data2}}',success:function(data){
				var param = FlowCore.params;
				if(param.handle=='approval'){
					$('.approveInfo').show();
				}else if(param.handle=='add'){
					var json = getQueryParams();
					fillRecord(json,"apply.",",","#flowForm");
					queryContractInfo(getUrlParam('contractId'));
				}
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			Flow.initData();
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		Flow.initData = function(){
			var pmLeader = $("[name='apply.data10']").val();
			var selectUserName = '';
			var params = {allocSelectUser:true,selectUserName:selectUserName,ccNames:'许志远,张明东,李千帆,陈莹'};
			FlowCore.approveData = params;
		}
		
		FlowCore.flowEnd = function(){
			var applyInfo = FlowCore.applyInfo;
			var devName = applyInfo['data11']+','+applyInfo['data12']+','+applyInfo['data15']+','+applyInfo['data14'];
			var data = {applyId:applyInfo['applyId'],pmName:applyInfo['data13'],devName:removeEmptyCommas(devName),contractId:applyInfo['data16'],deptId:applyInfo['data17'],deptName:applyInfo['data9']};
			ajax.remoteCall("${ctxPath}/servlet/project/conf?action=setProjectPmInfo",data,function(result) { 
				if(result.state == 1){
					
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});	
		}
		
		function removeEmptyCommas(input) {
		  return input.replace(/(,\s*,)|(,\s*$)/g, '');
		}
		
		Flow.approveLayer = function(){
			$('#checkResultTr').prepend('<tr><td style="text-align:center;color:red;font-size:16px;" colspan="2">确认是否已经选择负责人或确认无需选择再确认审批</td></tr>');
			
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='任命负责人'){
				$("[name='checkDesc']").val(getCheckDesc());
				$('[name="ccIds"]').val('');
			}
			if(nodeCode=='发布任命'){
				$('[name="ccIds"]').val('');
			}
		}
		
		function getCheckDesc(){
			let resultArray = [];
			$("[data-update]").each(function() {
			    let currentElement = $(this);
			    var text = currentElement.parent().prev().text();
			    var val = currentElement.val();
			    if(val){
				    let textAndValue =  text + "：" + val;
				    resultArray.push(textAndValue);
			    }
			});
			if(resultArray.length==0){
				resultArray.push('无需任命负责人');
			}
			return resultArray.join('\n');
		}
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='任命负责人'){
				return true;
			}
			if(nodeCode=='发布任命'){
				var array = [];
				var data11 = $("[name='apply.data11']").val();
				var data12 = $("[name='apply.data12']").val();
				var data13 = $("[name='apply.data13']").val();
				var data14 = $("[name='apply.data14']").val();
				var data15 = $("[name='apply.data15']").val();
				if(data11){
					array.push(data11.split(','));
				}
				if(data12){
					array.push(data12.split(','));
				}
				if(data13){
					array.push(data13.split(','));
				}
				if(data14){
					array.push(data14.split(','));
				}
				if(data15){
					array.push(data15.split(','));
				}
				var params = {ccNames:array.join(','),ccIds:''};
				FlowCore.approveData = params;
			}
			return true;
	    }
		
		
		function selectReview(el){
			var id = new Date().getTime();
			$(el).attr('data-sid',id);
			popup.layerShow({id:'selectCust',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择评审',url:'/yq-work/pages/flow/select/select-review.jsp',data:{sid:id,type:'radio'}});
		}
		
		function selectReviewCallBack(row){
			queryContractInfoByReview(row['APPLY_NO']);
		}
		
		function readContractData(contractInfo,reviewInfo,contractId){
			$("[name='apply.data1']").val(contractInfo['REVIEW_ID']);
			$("[name='apply.relatedFlow']").val(contractInfo['REVIEW_APPLY_ID']);
			$("[name='apply.data5']").val(contractInfo['CUSTOMER_NAME']);
			$("[name='apply.data7']").val(contractInfo['SIGN_ENT']);
			$("[name='apply.data6']").val(contractInfo['SIGN_DATE']);
			$("[name='apply.data9']").val(contractInfo['PROJECT_DEPT_NAME']);
			
			$("[name='apply.data2']").val(reviewInfo['CONTRACT_NAME']);
			$("[name='apply.data16']").val(contractInfo['CONTRACT_ID']);
			$("[name='apply.data17']").val(contractInfo['PROJECT_DEPT_ID']);
			$("[name='apply.data3']").val(reviewInfo['CONTRACT_TYPE']);
			$("[name='apply.data4']").val(reviewInfo['PROD_LINE']);
			$("[name='apply.data8']").val(reviewInfo['SALES_BY_NAME']);
		}
		
		function queryContractInfoByReview(reviewId){
			ajax.remoteCall(getCtxPath()+"/servlet/project/conf?action=queryContractIdByReviewNo",{reviewId:reviewId},function(result) { 
				var contractId = result.data;
				if(contractId){
					queryContractInfo(contractId);
				}else{
					layer.alert('未找到对应的合同设置的评审号'+reviewId,{icon:7,offset:'20px'});
				}
			});
		}
		
		function queryContractInfo(contractId){
			if(contractId){
				ajax.remoteCall(getCtxPath()+"/webcall?action=ProjectContractDao.record",{contractId:contractId},function(result) { 
					var reviewId = result.data['REVIEW_APPLY_ID'];
					ajax.remoteCall(getCtxPath()+"/webcall?action=ReviewDao.reviewInfo",{reviewId:reviewId},function(rs) { 
						readContractData(result.data,rs.data);
					});
				});
			}else{
				layer.msg('建议从合同管理入口申请此流程',{icon:7,offset:'20px',time:1200});
			}
		}
		
		function getQueryParams() {
		  const searchParams = new URLSearchParams(window.location.search);
		  const paramsJson = {};
		  for (let param of searchParams.entries()) {
		    paramsJson[param[0]] = param[1];
		  }
		  return paramsJson;
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>