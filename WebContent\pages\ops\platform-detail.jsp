<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>平台</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="platformDetail" data-mars="PlatformDao.record">
			   <input type="hidden" name="platform.PLATFORM_ID" value="${param.platformId}"/>
			   <input type="hidden" name="platformId" value="${param.platformId}"/>
	    	   <div class="ibox-card">
					<div class="ibox-card-header">
						 <ul class="nav nav-tabs" role="tablist">
						    <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">基本信息</a></li>
						    <li role="presentation"><a href="#profile" aria-controls="profile" role="tab" data-toggle="tab">主机详情</a></li>
						    <li role="presentation"><a href="#messages" aria-controls="messages" role="tab" data-toggle="tab">服务详情</a></li>
						    <li role="presentation" ><a href="#entlist" onclick="loadEntlist()" aria-controls="entlist" role="tab" data-toggle="tab">企业列表</a></li>
						  </ul>
					</div>
					<div class="ibox-card-body" style="min-height: 200px">
						<div class="tab-content">
						  <div role="tabpanel" class="tab-pane fade in active" id="home">
						  	<p>
						  		<span id="PLATFORM_NAME"></span>
						  		<span class="ml-10" id="PLATFORM_URL"></span>
						  	</p>
						  	<p class="mt-10" id="PLATFORM_DESC" style="line-height: 30px;"></p>
						  </div>
						  <div role="tabpanel" class="tab-pane fade" id="profile">
						  	<table class="layui-hide" id="host">
						  	
						  	</table>
						  </div>
						  <div role="tabpanel" class="tab-pane fade" id="messages">
						  	<table class="layui-hide" id="service">
						  	
						  	</table>
						  </div>
						  <div role="tabpanel" class="tab-pane fade"  id="entlist">
						  	<table class="layui-hide" id="entlist_table">
						  	
						  	</table>
						  </div>
						</div>
					</div>
				</div>
			</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	 	$(function(){
	 		$("#platformDetail").render();
	 		loadHost();
	 		loadService();
	 	});
		function loadHost(){
			$("#platformDetail").initTable({
				mars:'PlatformDao.host',
				page:false,
				cellMinWidth: 180,
				id:"host",
				data:{},
				cols: [[
	             {
            	 	field: 'HOST_NAME',
					title: '主机名称',
					align:'center'
				 },{
				    field: 'PRIVATE_IP',
					title: '内网IP',
					align:'center'
				},{
				    field: 'MEMORY',
					title: '内存',
					align:'center'
				},{
				    field: 'DISK_SIZE',
					title: '磁盘',
					align:'center'
				},{
				    field: 'NOW_MEMORY',
					title: '当前空闲内存',
					align:'center'
				},{
				    field: 'NOW_CUP',
					title: '当前CPU占比',
					align:'center'
				},{
				    field: 'CREATE_TIME',
					title: '创建时间',
					align:'center'
				}
				]]});
		}
		function loadService(){
			$("#platformDetail").initTable({
				mars:'PlatformDao.service',
				page:false,
				id:"service",
				data:{},
				cols: [[
	             {
            	 	field: 'SERVICE_NAME',
					title: '服务名称',
					align:'left'
				 }, {
            	 	field: 'SERVICE_VERSION',
					title: '版本号',
					align:'left'
				},
				{
				    field: 'CREATE_TIME',
					title: '创建时间',
					align:'left'
				}
				]]});
		}
		function loadEntlist(){
			$("#platformDetail").initTable({
				mars:'PlatformDao.entlist',
				id:"entlist_table",
				page:true,
				data:{platformId:'${param.platformId}'},
				cols: [[
	             {
            	 	field: 'ENT_ID',
					title: '企业ID',
					align:'left'
				 }, {
            	 	field: 'ENT_NAME',
					title: '企业名称',
					align:'left'
				},
				{
				    field: 'CREATE_TIME',
					title: '创建时间',
					align:'left'
				},
				{
				    field: 'OPEN_TIME',
					title: '开通时间',
					align:'left'
				},
				{
				    field: 'AREA_CODE',
					title: '城市',
					align:'left'
				},
				{
				    field: 'ADDR',
					title: '客户地址',
					align:'left'
				}
				]]});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>