<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.base-info tr td:nth-child(odd) {
			text-align: right;
			width: 80px;
			font-weight: 500;
			color: #000000;
		}
		.layui-link{color: #5d99cd;}
		.close-el{display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="ContractDetailForm">
		<input type="hidden" value="${param.contractId}" id="contractId" name="contractId"/>
		<input type="hidden" value="${param.contractId}" name="fkId"/>
		<input type="hidden" value="${param.custId}" name="custId"/>
		<div style="padding: 5px" class="ibox-content" data-mars="ProjectContractDao.record" data-mars-prefix="contract.">
			<div class="layui-row" style="padding:6px 15px;">
				<div class="layui-col-md10">
					<i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i>
					<span style="font-size: 18px;" name='contract.CONTRACT_NAME'></span>
					<a onclick="projectMgr();" class="layui-link" href="javascript:;">项目管理</a>
				</div>
				<div class="layui-col-md2">
					<EasyTag:hasRole roleId="CONTRACT_MGR">
						<div class="op-right pull-right mr-50">
							<div class="btn-group btn-group-sm">
								<button class="btn btn-default btn-xs" onclick="editContract();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
							</div>
							<div class="btn-group btn-group-sm">
								<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">•••</button>
								<ul class="dropdown-menu dropdown-menu-right">
									<li><a href="javascript:;" onclick="delData()">删除</a></li>
								</ul>
							</div>
						</div>
					</EasyTag:hasRole>
					<div class="close-el" onclick="closePage(this)"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
				</div>
			</div>
			<div class="layui-row" style="padding:10px 15px;">
				<div class="layui-col-md2">
					<span class="h-title">合同号</span>
					<span class="h-value" name="contract.CONTRACT_NO"></span>
				</div>
				<div class="layui-col-md2">
					<span class="h-title">签订时间</span>
					<span class="h-value" name="contract.SIGN_DATE"></span>
				</div>
				<div class="layui-col-md2">
					<span class="h-title">客户编号</span>
					<span class="h-value" name="contract.CUST_NO"></span>
				</div>
				<div class="layui-col-md3">
					<span class="h-title">客户名称</span>
					<span class="h-value" name="contract.CUSTOMER_NAME"></span>
				</div>
				<div class="layui-col-md2">
					<span class="h-title">更新时间</span>
					<span class="h-value" name="contract.UPDATE_TIME"></span>
				</div>
			</div>
		</div>
		<div class="layui-row">
			<div class="layui-col-md12">
				<div class="layui-tab layui-tab-card" lay-filter="contentFilter" style="border-top: 1px solid #ddd;">
					<ul class="layui-tab-title">
						<li lay-id="A" class="layui-this">基本信息</li>
						<li lay-id="B">合同附件<span id="fileCount"></span></li>
						<li lay-id="C">合同阶段信息(<span name="extend.STAGE_COUNT">0</span>)</li>
						<li lay-id="D">合同款项信息(<span name="extend.PAYMENT_COUNT">0</span>)</li>
						<li lay-id="E">收入确认阶段(<span name="extend.INCOME_STAGE_COUNT">0</span>)</li>
						<li lay-id="F">收入确认A(<span name="extend.INCOME_CONFIRM_COUNT">0</span>)</li>
						<li lay-id="I">收入确认B(<span name="extend.INCOME_CONFIRM_COUNT_B">0</span>)</li>
						<li lay-id="G">发票信息(<span name="extend.INVOICE_COUNT">0</span>)</li>
						<li lay-id="H">收款信息(<span name="extend.RECEIPT_COUNT">0</span>)</li>
						<li lay-id="L">采购记录</li>
						<li lay-id="J">操作记录</li>
					</ul>
					<div class="layui-tab-content" style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
						<div class="layui-tab-item layui-show">
							<jsp:include page="include/base-info.jsp"/>
						</div>
						<div class="layui-tab-item">
							<jsp:include page="../include/files.jsp"/>
						</div>
						<div class="layui-tab-item">
							<jsp:include page="include/stage-info.jsp"/>
						</div>
						<div class="layui-tab-item">
							<jsp:include page="include/payment-info.jsp"/>
						</div>
						<div class="layui-tab-item">
							<jsp:include page="include/income-stage-info.jsp"/>
						</div>
						<div class="layui-tab-item">
							<jsp:include page="include/income-confirm-info.jsp"/>
						</div>
						<div class="layui-tab-item">
							<jsp:include page="include/income-confirm-info-B.jsp"/>
						</div>
						<div class="layui-tab-item">
							<jsp:include page="include/contract-invoice-info.jsp"/>
						</div>
						<div class="layui-tab-item">
							<jsp:include page="include/receipt-info.jsp"/>
						</div>
						<div class="layui-tab-item">
							<p>采购记录</p>
						</div>
						<div class="layui-tab-item">
							<p>计划开发中</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
	<form id="custFileForm" enctype="multipart/form-data"  method="post">
		<input style="display: none;" name="file" type="file" id="custLocalfile" onchange="uploadFile();"/>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">

		var ContractDetail = {
			contractId : '${param.contractId}'
		}

		var loadMap = {};
		var contractId ='${param.contractId}';
		var contractInfo = {};

		$(function(){
			$("#ContractDetailForm").render({success:function(result){
				var record = result && result['ProjectContractDao.record'];
				var data = record && record.data;
				if(data){
					var textJson = data['TEXT_JSON'];
					if(textJson){
						var json = eval('(' + textJson + ')');
						fillRecord(json,'extend.');
					}
					contractInfo = data;
				}
				loadFileList();
			}});
			
			layui.element.on('tab(contentFilter)', function(data){
				if(loadMap[data.id]){
					return;
				}
				loadMap[data.id] = true;

				if(data.id=='B'){
					loadFileList();
				}else if(data.id=='C'){
					contractStageList.init();
				}else if(data.id=='D'){
					paymentList.init();
				}else if(data.id=='E'){
					incomeStageList.init();
				}else if(data.id=='F'){
					IncomeConfirmList.init();
				}

				else if(data.id=='G'){
					contractInvoiceList.init();
				}else if(data.id=='H'){
					contractReceiptList.init();
				}else if(data.id=='L'){

				}else if(data.id=='J'){

				}else if(data.id=='I'){
					IncomeConfirmListB.init();
				}
			});
			
			if(self==top){
				$('.op-right').css('margin-right','-10px');
				$('.close-el').hide();
			}
		});

		var ctxPath = getCtxPath();


		function reloadFile(){
			reloadCustFile();
		}

		function editContract(){
			var contractId = $("#contractId").val();
			var custId = contractInfo.CUST_ID;
			popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'r',full:true,area:['70%','100%'],url:ctxPath+'/pages/crm/contract/contract-edit.jsp',title:'编辑合同',data:{contractId:contractId,custId:custId}});
		}

		function projectMgr(){
			var projectId = $("#contractId").val();
			projectBoard(projectId);
		}

		function custDetail(el){
			var custId = contractInfo.CUST_ID;
			popup.openTab({id:'custDetail',title:'客户详情',url:ctxPath+'/cust',data:{custId:custId,isDiv:0}});
		}


		function uploadFile(){
			var fkId=$("#contractId").val();
			easyUploadFile({callback:'reloadCustFile',fkId:fkId,source:'contract',formId:'custFileForm',fileId:'custLocalfile'});
		}

		function reloadCustFile(){
			$("#ContractDetailForm").queryData({id:'fileList',page:false});
		}

		function openDownloadLog(){
			downloadLogLayer(contractId);
		}

		function delData(){
			layer.confirm("确定删除吗,不可恢复",function(index){
				layer.close(index);
				var id=$("#contractId").val();
				ajax.remoteCall(ctxPath+"/servlet/projectContract?action=delData",{id:id},function(result) {
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							popup.closeTab();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}

		function closePage(){
			if (typeof top._removePage === 'function') {
				popup.closeTab();
			} else {
				top.location.reload();
			}
		}

		function reloadContractDetail(){
            $("#ContractDetailForm").render({success:function(result){
                    var record = result && result['ProjectContractDao.record'];
                    var data = record && record.data;
                    if(data){
                        var textJson = data['TEXT_JSON'];
                        if(textJson){
                            var json = eval('(' + textJson + ')');
                            fillRecord(json,'extend.');
                        }
                        contractInfo = data;
                    }
                }});
        }


	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>