<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目看板</title>
	<link href="${ctxPath}/static/zui/css/zui.min.css" rel="stylesheet">
	<script src="${ctxPath}/static/zui/js/zui.min.js"></script>
	<link href="${ctxPath}/static/zui/lib/board/zui.board.min.css" rel="stylesheet">
	<script src="${ctxPath}/static/zui/lib/board/zui.board.min.js"></script>
	<style type="text/css">
		/* 保证影子元素一直可见 */
	    .board-item.drag-shadow {z-index: 9999}
	    .addItem{cursor: pointer;}
	    .addItem:hover{
	    	text-decoration: underline;
	    	color: #999;
	    }
	    .panel-primary .panel-heading a{color: #fff;}
	    .panel-heading a{opacity:0;}
	    .panel-heading:hover a{opacity:1;}
	    .board {width: 24%;}
	    .board-item{cursor: pointer;border-radius:6px;}
	    .board-item:hover{background-color: #f5f5f5!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form id="kanbanForm" data-mars="KanbanDao.kanbanInfo">
			<input type="hidden" name="kanbanId" value="${param.kanbanId}"/>
			<div class="layui-card">
			  <div class="layui-card-header">
			  	<span  id="KANBAN_NAME"></span>
			  	<span class="f-12 ml-20" id="CREATE_TIME"></span>
			  	
			  	<button type="button" onclick="addPanel()" class="btn  btn-primary pull-right mt-5">+新建分组</button>
			  </div>
			  <div class="layui-card-body" style="min-height: 300px;">
				  <div class="boards" id="myBoard" style="position: static;" data-mars="KanbanDao.kanbanPanel" data-template="tpl"></div>
				  <!-- <div onclick="addPanel();" style="height: 80px;width: 200px;background-color: #eee;cursor: pointer;display: inline-block;border-radius:4px;"><div  style="text-align: center;padding: 28px;">+新建看板</div></div> -->
		  	</div>
		</div>
	</form>
	 <script type="text/x-jsrender" id="tpl">
  			{{for data}}
				<div class="board panel {{:PANEL_THEME}}" data-id="{{:PANEL_ID}}">
				      <div class="panel-heading">
				        <strong> {{:PANEL_NAME}}({{:ORDER_INDEX}})</strong>
						<div class="pull-right" style="{{call:CREATOR fn='isSelfShow'}}"><a  href="javascript:;" class="mr-10" onclick="delPanel('{{:PANEL_ID}}')">删除</a><a  href="javascript:;" onclick="editPanel('{{:PANEL_ID}}')">修改</a></div>
				      </div>
				      <div class="panel-body">
				        <div class="board-list">
				          <div class="board-item board-item-shadow" style="height: 34px;"></div>
						  {{for #parent.parent.data.items}}
				            {{if #parent.parent.data.PANEL_ID==PANEL_ID}}<div class="board-item" data-panel-id="{{:PANEL_ID}}" data-item-id="{{:ITEM_ID}}"><p class="mb-5" style="color:#9aa72b;">{{call:CREATOR fn='getUserName'}} {{:CREATE_TIME}}</p>{{:ITEM_CONTENT}}</div>{{/if}}
						  {{/for}}
				        </div>
				        <div class="addItem" onclick="addPanelItem('{{:PANEL_ID}}')">+添加卡片</div>
				      </div>
				    </div>
			{{/for}}
	 </script>
</EasyTag:override>


<EasyTag:override name="script">
	<script type="text/javascript">
	
	  function isSelfShow(id){
		   var currId=getCurrentUserId();
		   if(id==currId){
			   return 'display:inline-block;';
		   }else{
			   return 'display:none;';
		   }
	   }
	  
		$(function(){
			$("#kanbanForm").render({success:function(){
				$(".board-item").click(function(){
					var t =$(this);
					var data = $.extend({},t.data());
					t.data();popup.layerShow({type:1,anim:0,scrollbar:false,offset:'30px',area:['400px','300px'],url:'${ctxPath}/pages/kanban/kanban-item-edit.jsp',title:'编辑',data:t.data()});
				});
				$('#myBoard').boards({
				    drop: function(e){
				    	var data = [];
				    	data[0]=e.element.data();
				    	data[1]=$.extend(e.target.data(),{panelId:e.target.closest('.board').data("id")});
				    	ajax.remoteCall("${ctxPath}/servlet/kanban?action=updateItemPostion",data,function(result) { 
							if(result.state == 1){
								layer.msg(result.msg,{icon:1,time:1200,offset:'rb'},function(){
									layer.closeAll();
							       // $.zui.messager.show(e.element.text() + " 拖放到 " + e.target.closest('.board').find('.panel-heading').text());
								});
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});
				    },
				    before: function(e) {
				        return true;
				    }
				});	
			}});
		});
		
		var kanbanId = '${param.kanbanId}';
		
		function addPanel(){
			 popup.layerShow({type:1,anim:0,scrollbar:false,offset:'30px',area:['400px','285px'],url:'${ctxPath}/pages/kanban/kanban-panel-edit.jsp',title:'新增分组',data:{kanbanId:kanbanId}});
		}
		function editPanel(panelId){
			 popup.layerShow({type:1,anim:0,scrollbar:false,offset:'30px',area:['400px','285px'],url:'${ctxPath}/pages/kanban/kanban-panel-edit.jsp',title:'编辑分组',data:{kanbanId:kanbanId,panelId:panelId}});
		}
		function delPanel(panelId){
			layer.confirm("是否删除,不可恢复",function(index){
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/kanban?action=delPanel",{panelId:panelId},function(result) { 
					if(result.state == 1){
						$("[data-id='"+panelId+"']").remove();
					}else{
						layer.msg("删除失败.");
					}
				});
			});
		}
		function addPanelItem(panelId){
			 popup.layerShow({type:1,anim:0,scrollbar:false,offset:'30px',area:['400px','300px'],url:'${ctxPath}/pages/kanban/kanban-item-edit.jsp',title:'新增',data:{panelId:panelId,kanbanId:kanbanId}});
			
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>