package com.yunqu.work.dao.erp;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="OrderDao")
public class OrderDao extends AppDaoContext {

	@WebControl(name="followInfo",type=Types.RECORD)
	public JSONObject followInfo(){
		String followId = param.getString("fkId");
		return queryForRecord("select * from yq_erp_order_follow where follow_id = ?",followId);
	}
	
	@WebControl(name="getOrderNo",type=Types.TEXT)
	public JSONObject getOrderNo(){
		int dateId = EasyCalendar.newInstance().getDateInt();
		try {
			String val = this.getQuery().queryForString("select max(order_no) from yq_erp_order where order_no like '"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult(dateId+"1001");
			}else {
				return getJsonResult(Integer.valueOf(val)+1);
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String orderId = param.getString("orderId");
		return queryForRecord("select * from yq_erp_order where order_id = ?",orderId);
	}
	@WebControl(name="applyInfo",type=Types.RECORD)
	public JSONObject applyInfo(){
		String orderId = param.getString("orderId");
		return queryForRecord("select t1.*,t3.`name` SUPPLIER_NAME,t4.cust_name from yq_erp_order t1 LEFT JOIN yq_project_contract t2 on t1.contract_id=t2.CONTRACT_ID LEFT JOIN yq_erp_supplier t3 on t3.supplier_id=t1.supplier_id left join yq_crm_customer t4 on t4.cust_id = t1.cust_id where t1.order_id = ?",orderId);
	}
	
	@WebControl(name="followList",type=Types.TEMPLATE)
	public JSONObject followList(){
		EasySQL sql=getEasySQL("select * from yq_erp_order_follow where 1=1");
		sql.append(param.getString("orderId"),"and order_id = ?");
		sql.append("order by add_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="followPage",type=Types.PAGE)
	public JSONObject followPage(){
		EasySQL sql=getEasySQL("select t1.*,t2.CONTRACT_NAME,t2.ORDER_NO,t2.CUST_ID from yq_erp_order_follow t1,yq_erp_order t2 where 1=1");
		sql.append("and t1.order_id = t2.order_id");
		sql.append(param.getString("orderId"),"and t1.ORDER_ID = ?");
		sql.appendLike(param.getString("orderNo"),"and t2.ORDER_NO like ?");
		sql.append("order by t1.add_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select t1.*,t2.CONTRACT_NAME,t3.`name` SUPPLIER_NAME from yq_erp_order t1");
		sql.append("LEFT JOIN yq_project_contract t2 on t1.contract_id=t2.CONTRACT_ID LEFT JOIN yq_erp_supplier t3 on t3.supplier_id=t1.supplier_id");
		sql.append("where 1=1");
		sql.append(param.getString("supplierId"),"and t1.SUPPLIER_ID = ?");
		sql.append(param.getString("custId"),"and t1.CUST_ID = ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="goodsList",type=Types.LIST)
	public JSONObject goodsList(){
		EasySQL sql=getEasySQL("select * from yq_erp_goods where 1=1");
		sql.append(param.getString("orderId"),"and order_id = ?");
		return queryForList(sql.getSQL(), sql.getParams());
		
	}
	
	@WebControl(name="custList",type=Types.LIST)
	public JSONObject custList(){
		EasySQL sql=getEasySQL("select * from yq_erp_order where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
}
