<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
		
		.w-e-text-container p{line-height: 12px!important;}
	</style>
	<link href="${ctxPath}/static/css/wangEditor-fullscreen.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" data-mars="ProductDao.info" data-mars-prefix="product." id="searchForm">
			<input id="pid" type="hidden" name="product.P_PRODUCT_ID" value="0"/>
			<input id="id" type="hidden" name="product.PRODUCT_ID"/>
			<input id="fkId" type="hidden" name="fkId"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>产品管理</h5>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.submit()">保存</button>
						 </div>
	          	     </div>
	              </div> 
						<div class="layui-row layui-col-space20">
							<div class="layui-col-md4">
							   <!--  <button type="button" class="layui-btn layui-btn-primary layui-btn-sm mr-10 refresh">刷新</button>
								<button type="button" class="layui-btn layui-btn-primary layui-btn-sm mr-10 open-all">全部展开</button>
								<button type="button" class="layui-btn layui-btn-primary layui-btn-sm mr-10 close-all">全部关闭</button> -->
							    <table class="layui-table layui-form" id="tree-table"></table>
						    </div>
						    <div class="layui-col-md8 mt-20" style="background: #fff;">
						    	<div class="layui-tab layui-tab-brief pd-10" style="margin-top: 0px;padding-top: 0px;">
								  <ul class="layui-tab-title">
								    <li class="layui-this">产品介绍</li>
								    <li>产品文档</li>
								    <li>演示环境</li>
								  </ul>
								  <div class="layui-tab-content">
								  	    <div class="layui-tab-item layui-show">
								  	    	<div class="input-group input-group-sm mb-10" style="width: 40%;">
												 <span class="input-group-addon">产品名称</span>	
												 <input type="text" name="product.PRODUCT_NAME" class="form-control input-sm">
										     </div>
								  	    	<div class="input-group input-group-sm mb-10" style="width: 40%;">
												 <span class="input-group-addon">排序</span>	
												 <input type="number" name="product.ORDER_INDEX" value="0" class="form-control input-sm">
										     </div>
								  	    	<div id="editor">
								  	    		<p>解决问题或痛点</p>
								  	    		<p>功能模块</p>
								  	    		<p>.....</p>
								  	    	</div>
				                            <textarea id="wordText" style="display: none;" name="product.INTRODUCE"></textarea>
								  	    </div>
								  	    <div class="layui-tab-item">
								  	    	<div id="fileList">
									    		<table class="layui-table">
												  <colgroup>
												    <col>
												    <col width="180">
												    <col width="120">
												  </colgroup>
												  <thead>
												    <tr>
												      <th>文件名</th>
												      <th>上传时间</th>
												      <th>上传人</th>
												    </tr> 
												  </thead>
												  <tbody data-template="template-files" data-mars="FileDao.fileListDetail">
			
												  </tbody>
												</table>
									    	</div>
									    	<button class="btn btn-sm btn-info mt-5" type="button" onclick="uploadDoc();">+上传</button>
								  	    </div>
								  	    <div class="layui-tab-item">
								  	    	<div id="demoEditor">
								  	    		<p>演示地址： http://www.baidu.com</p>
								  	    		<p>角色1：admin/admin</p>
								  	    		<p>角色2：zhangsan/123456</p>
								  	    	</div>
				                            <textarea id="demoText" style="display: none;" name="product.DEMO"></textarea>
								  	    </div>
						    	</div>
						    </div>
						  </div>
						</div>
					</div>
		</form>
		<form  id="FileForm" enctype="multipart/form-data"  method="post">
  				<input style="display: none;" name="file" type="file" id="localfile" onchange="uploadFile()"/>
 		</form>
		<script id="template-files" type="text/x-jsrender">
			{{if data.length==0}}
				<tr>
					<td colspan="3">暂无数据</td>
				</tr>
			{{/if}}
			{{for data}}
				<tr>
				  <td>{{:#index+1}}、<a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}} &nbsp;</span> <span style="color:#17a6f0">下载 | {{:FILE_SIZE}}<span></a></td>
				  <td>{{:CREATE_TIME}}</td>
				  <td>
						{{:USERNAME}}
					</td>
				</tr>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script src="${ctxPath}/static/js/emotions.js" type="text/javascript"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditorExt.js"></script>
	
<script type="text/javascript">
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.config.menus = [
			    'head',  // 标题
			    'bold',  // 粗体
			    'fontSize',  // 字号
			    'fontName',  // 字体
			    'foreColor',  // 文字颜色
			    'link',  // 插入链接
			    'list',  // 列表
			    'justify',  // 对齐方式
			    'quote',  // 引用
			    'code',  // 插入代码
			    'emoticon',  // 表情
			    'image',  // 插入图片
			    'table',  // 表格'
			 ];
		editor.config.pasteFilterStyle = false;
		editor.config.pasteIgnoreImg = false;
		editor.config.pasteTextHandle = function (content) {
		      if (content == '' && !content) return ''
		      var str = content
		      if(str.indexOf('MsoNormal')>-1){
		    	  layer.msg("复制word文档内容可能格式不正确,请对格式进行调整。",{icon:7});
		      }
		      if(str.indexOf('file://')>-1){
		    	  layer.msg("复制word文档附加的图片路径不存在,请单独复制图片。",{icon:7});
		      }
		      return str;
		} 
		 editor.config.emotions = [
              {
                  title: '默认',
                  type: 'image',
                  content:yqEmotions
              },
              {
                  title: 'emoji',
                  type: 'emoji',
                  content: ['😀', '😃', '😄', '😁', '😆','🤐','😬','🙄','😓','😂']
              }
        ];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.config.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.create();
		E.fullscreen.init(editor);
		E.viewSource.init(editor);
		$(".btn_fullscreen").click(function(){
			window.wangEditor.fullscreen.run(editor.id);
		});
		$(".btn_viewSource").click(function(){
			window.wangEditor.viewSource.run(editor.id);
		});
		
		
		var demoEditor = new E('#demoEditor');
		demoEditor.config.onchange = function (html) {
		     $("#demoText").val(html)
		}
		weUpload(demoEditor,{uploadImgMaxLength:3});
		demoEditor.create();

		var list={};

		function loadTreeTable(data){
			layui.config({
				  base: '${ctxPath}/static/module/treeTable/'
			}).use('treeTable'); //加载自定义模块

			layui.use(['treeTable','form','element'],function(){
					var form = layui.form;
					var treeTable = layui.treeTable;
				    var re = treeTable.render({
						elem: '#tree-table',
						data: data,
						height: 'full-100',
						tree:{
							iconIndex: 0,
			                isPidData: true,
			                idName: 'PRODUCT_ID',
			                pidName: 'P_PRODUCT_ID'
						},
						done: function(e){
							form.render();
							$("#tree-table tbody").append('<tr><td colspan=2><a class="layui-btn layui-btn-xs" href="javascript:void(0)" onclick="list.addProduct(0)">添加产品</a></td></tr>');
							$("#tree-table tbody a").first().click();
						},
						cols: [
							{
								field: 'PRODUCT_NAME',
								title: '名称'
							},
							{
								title: '操作',
								field:'',
								align: 'left',
								templet: function(item){
									var html = '<a  class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:void(0)" onclick="list.editProduct(\''+item.PRODUCT_ID+'\')"><i class="layui-icon layui-icon-edit"></i></a>';
									html += '<a  class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:void(0)" onclick="list.delProduct(\''+item.PRODUCT_ID+'\')"><i class="layui-icon layui-icon-delete"></i></a>';
									if(item['P_PRODUCT_ID']==0){
										html +='<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:void(0)" onclick="list.addProduct(\''+item.PRODUCT_ID+'\')"><i class="layui-icon layui-icon-addition"></i></a>';
									}
									return html;
								}
							}
						]
				});
				treeTable.openAll(re);
				$('.open-all').click(function(){
					re.expandAll();
				});
				$('.close-all').click(function(){
					re.foldAll();
				})
				$('.refresh').click(function(){
					re.reload();
				})
				    
			});
	     }
		function uploadDoc(){
			var fkId =$("#id").val();
			if(fkId){
				$('#localfile').click();
			}else{
				layer.msg("请先点击保存再上传");
			}
		}
	 	var uploadFile = function(){
	 		var fkId =$("#id").val();
			easyUploadFile({callback:'Callback',fkId:fkId,source:'product',formId:'FileForm',fileId:'localfile'});
		}
	 	var Callback = function(data){
	 		var fkId =$("#id").val();
			$("#fileList").render({data:{fkId:fkId}});
		}
	 	
 		list.loadTree = function(){
 			ajax.remoteCall("${ctxPath}/webcall?action=ProductDao.productList",form.getJSONObject("#searchForm"),function(rs) { 
				var result= rs.data;
				loadTreeTable(result);
						
			});
 		}
 		list.add =  function(){
 			var data  = form.getJSONObject("#searchForm");
 			if(data['product.PRODUCT_NAME']==''){
 				layer.msg('请输入产品名称',{icon:7});
 				return;
 			}
 			ajax.remoteCall("${ctxPath}/servlet/product?action=add", data, function(data) { 
				layer.msg(data.msg,{time:1200,icon: 1},function(){
					layer.closeAll();
					location.reload();
				});
			});
 		}
 		list.update =  function(){
 			var data  = form.getJSONObject("#searchForm");
 			ajax.remoteCall("${ctxPath}/servlet/product?action=update", data, function(data) { 
				layer.msg(data.msg,{time:1200,icon: 1},function(){
					layer.closeAll();
				});
			});
 		}
 		list.submit = function(){
 			var id =$("#id").val();
 			if(id){
 				list.update();
 			}else{
 				list.add();
 			}
 		}
 		list.addProduct =function(pid){
 			$("#searchForm")[0].reset();
 			 demoEditor.txt.html('');
 			 editor.txt.html('');
 			$("#pid").val(pid);
 			$("#id").val('');
 			
 		}
 		list.delProduct =function(id){
 			layer.confirm("确认删除吗,不可恢复",{icon:7,offset:'20px'},function(index){
 				layer.close(index);
 				ajax.remoteCall("${ctxPath}/servlet/product?action=delProduct", {productId:id}, function(data) { 
 					layer.msg(data.msg,{time:1200,icon: 1},function(){
 						location.reload();
 					});
 				});
 			});
 			
 		}
 		list.editProduct =function(id){
 			$("#id,#fkId").val(id);
 			$("#searchForm").render({success:function(result){
 				 var data = result['ProductDao.info'].data;
				 editor.txt.html(data['INTRODUCE']);
				 demoEditor.txt.html(data['DEMO']);
			}});
 		}
		$(function(){
			list.loadTree();
			var height=$(window).height();
			$(".w-e-text-container").css('height',height-250);
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>