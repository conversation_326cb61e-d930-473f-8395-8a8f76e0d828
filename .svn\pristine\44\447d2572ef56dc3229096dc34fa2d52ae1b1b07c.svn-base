<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>员工报销统计</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }
        .ibox-content .layui-table-cell {
            font-size: 13px;
        }
        .layui-icon-tips {
            display: inline-flex;
            align-items: center;
            height: 30px;
            vertical-align: middle;
            color: rgba(13, 13, 13, 0.25);
            margin-left: 1px;
        }
    </style>
</EasyTag:override>

<EasyTag:override name="content">
    <form id="searchForm" class="form-inline" autocomplete="off">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <input type="hidden" name="timeType" id="timeType" value="bx_date">
                    <div class="btn-group-sm btn-group time-type" style="width: 140px;">
                        <button class="btn btn-info btn-xs" type="button" value="bx_date">报销时间</button>
                        <button class="btn btn-default btn-xs" type="button" value="recorded_date">入账时间</button>
                    </div>
                    <div class="input-group input-group-sm" style="width: 260px">
                        <span class="input-group-addon">数据日期</span>
                        <input data-mars="CommonDao.yearBegin" name="beginDate" id="beginDate"
                               class="form-control input-sm" onchange="queryList()"
                               onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="12">
                        <span class="input-group-addon">-</span>
                        <input data-mars="CommonDao.yearDateEnd" name="endDate" id="endDate"
                               onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="12" onchange="queryList()"
                               class="form-control input-sm">
                    </div>
                    <div class="btn-group-sm btn-group select-time" style="width: 240px;">
                        <button class="btn btn-default btn-xs" type="button" value="halfYear">半年内</button>
                        <button class="btn btn-default btn-xs" type="button" value="oneYear">1年内</button>
                        <button class="btn btn-info btn-xs" type="button" value="nowYear" id="nowYearBtn">今年</button>
                        <button class="btn btn-default btn-xs" type="button" value="lastYear">去年</button>
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group input-group-sm" style="width: 140px">
                        <span class="input-group-addon">费用类型</span>
                        <input type="text" id="feeType" name="feeType" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 260px;display: inline-flex">
                        <span class="input-group-addon">合同名称</span>
                        <input type="text" id="contractName" name="contractName"
                               class="form-control input-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="btnSelectContract()"><span>选择</span></button>
                    </div>
                    <div class="input-group input-group-sm" style="width: 200px;display: inline-flex">
                        <span class="input-group-addon">员工部门</span>
                        <input type="text" name="staffDeptName" id="staffDeptName" class="form-control input-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="btnSelectStaffDept()"><span>选择</span></button>
                    </div>
                    <div class="input-group input-group-sm" style="width: 150px;">
                        <span class="input-group-addon">报销人</span>
                        <input id="bxBy" name="bxBy" type="hidden">
                        <input onclick="singleUser(this);" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 160px">
                        <span class="input-group-addon">同比变化</span>
                        <select name="compareType" class="form-control input-sm" onchange="queryList()">
                            <option value="">全部</option>
                            <option value="up">同比升高</option>
                            <option value="down">同比下降</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm pull-right">
                        <button type="button" class="btn btn-sm btn-default ml-5" onclick="queryList()"><span class="glyphicon glyphicon-search"></span> <span>搜索</span></button>
                        <button type="button" class="btn btn-sm btn-default ml-5" onclick="clearForm()">清空</button>
                        <i class="layui-icon layui-icon-tips" lay-tips="选择报销时间，以报销人填写报销单时的报销明细中每项单独的'费用日期'作为统计标准。选择入账时间，下面全部的【时段内】都将变为入账时间的数据日期筛选的结果，即今年的报销金额=入账时间为今年的报销金额，且未入账的报销单将不再统计。"></i>
                    </div>
                </div>
            </div>
            <div class="ibox-content">
                <table class="layui-hide" id="staffRankTable"></table>
            </div>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

    <script>
        $(function() {
            $('.time-type button').click(function () {
                $('[name="timeType"]').val($(this)[0].value);
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                queryList();
            });

            $('.select-time button').click(function () {
                getDate($(this)[0], 'beginDate', 'endDate');
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                queryList();
            });
            
            $("#searchForm").render({
                success: function() {
                    queryList();
                }
            });
        });

        function queryList() {
            var  beginDate = $('#beginDate').val();
            var  endDate = $('#endDate').val();

            const start = new Date(beginDate);
            const end = new Date(endDate);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if(diffDays > 366){
                layer.msg('日期范围不能超过1年,重新选择后才能查询。', {icon: 2});
            }else {
                loadStaffTable();
            }
        }

        function clearForm() {
            $('#searchForm input').val('');
            $("#searchForm .select-clear").click();
            $('#timeType').val('bx_date');
            $("#searchForm #nowYearBtn").click();
            queryList();
        }

        function loadStaffTable() {
            $("#searchForm").initTable({
                mars: 'BxStatisticDao.staffAmountList',
                cellMinWidth: 50,
                limit: 20,
                height:'full-120',
                totalRow: true,
                autoSort: false,
                id: 'staffRankTable',
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align: 'left',
                        totalRowText: "合计"
                    }, {
                        field: 'APPLY_NAME',
                        align: 'center',
                        title: '员工',
                        width: 140
                    }, {
                        field: 'DEPTS',
                        align: 'center',
                        title: '员工部门',
                        width: 200,
                    }, {
                        field: 'TOTAL_AMOUNT_THIS_YEAR',
                        title: '时段内总额',
                        sort: true,
                        align: 'center',
                        event: 'staffTableClickThisYear',
                        style: 'cursor:pointer;text-decoration: underline;',
                        minWidth:100,
                        templet: function (d) {
                            var num = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                            return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                        },
                        totalRow: true,
                    }, {
                        field: 'TOTAL_AMOUNT_LAST_YEAR',
                        title: '上一年同期',
                        sort: true,
                        align: 'center',
                        event: 'staffTableClickLastYear',
                        style: 'cursor:pointer;text-decoration: underline;',
                        minWidth:100,
                        templet: function (d) {
                            var num = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                            return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                        },
                        totalRow: true,
                    }, {
                        field: '',
                        title: '同比增长率',
                        width: 200,
                        align: 'center',
                        minWidth:100,
                        templet: function (d) {
                            var lastYearAmount = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                            if (lastYearAmount === 0) return '上一年无报销';
                            var thisYearAmount = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                            if (thisYearAmount === undefined) return '今年无报销';
                            if (thisYearAmount === lastYearAmount) return '0%';
                            var num = (thisYearAmount - lastYearAmount) * 100 / lastYearAmount;
                            //金额低于基本手机费，不对增长率加label样式
                            if(thisYearAmount < 2600 || lastYearAmount < 2400) {
                                return num.toFixed(2) + "%";
                            }
                            return ratioColor(num);
                        }
                    },
                ]],
                done: function(data) {

                }
            });
        }


        function openBxList(data) {
            data.isDiv = 1;
            data.timeType = $('#timeType').val();
            data.contractName = $('#contractName').val();
            data.feeType = $('#feeType').val();
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                maxmin: true,
                shadeClose: false,
                title: '报销明细',
                offset: 'auto',
                area: ['800px', '520px'],
                url: '${ctxPath}/pages/flow/bx/bx-list-query.jsp',
                data: data
            });
        }

        function staffTableClickThisYear(row) {
            var data = {};
            data.applyName = row.APPLY_NAME;
            data.bxBy = row.APPLY_BY;
            data.startDate = $('#beginDate').val();
            data.endDate = $('#endDate').val();
            openBxList(data);
        }

        function staffTableClickLastYear(row) {
            var data = {};
            data.applyName = row.APPLY_NAME;
            data.bxBy = row.APPLY_BY;
            data.startDate = getLastYearDate($('#beginDate').val());
            data.endDate = getLastYearDate($('#endDate').val());
            openBxList(data);
        }



        function btnSelectStaffDept() {
            var el = document.getElementById("staffDeptName");
            singleDept(el);
        }

        function btnSelectContract() {
            var el = document.getElementById("contractName");
            singleContract(el);
        }
        function selctCallBack(id, row) {
            $("[name='contractName']").val(row['CONTRACT_NAME']);
        }
        
        function ratioColor(ratio) {
            if (ratio === undefined || ratio == null || ratio == "") {
                return '';
            }
            ratio = parseFloat(ratio);
            if (ratio >= 100) {
                return "<label class='label label-danger'>" + ratio.toFixed(2) + "%</label>";
            } else if (ratio >= 70) {
                return "<label class='label label-danger label-outline'>" + ratio.toFixed(2) + "%</label>";
            } else if (ratio >= 50) {
                return "<label class='label label-warning label-outline'>" + ratio.toFixed(2) + "%</label>";
            } else if (ratio >= 30) {
                return "<label class='label label-info label-outline'>" + ratio.toFixed(2) + "%</label>";
            } else {
                return ratio.toFixed(2) + "%";
            }
        }


        function getLastYearDate(dateString) {
            const year = parseInt(dateString.substring(0, 4), 10);
            const lastYear = year - 1;
            return lastYear + dateString.substring(4);
        }

        var getDate = function (obj, startDate, endDate) {
            var val = obj.value;
            var bdate = new Date();
            var edate = new Date();
            if (val == 'halfYear') {
                bdate.setMonth(bdate.getMonth() - 6);
            } else if (val == 'oneYear') {
                bdate.setMonth(bdate.getMonth() - 12);
                bdate.setDate(bdate.getDate() + 1);
            } else if (val == 'nowYear') {
                bdate.setMonth(0);
                bdate.setDate(1);
                edate.setMonth(11);
                edate.setDate(31);
            } else if (val == 'lastYear') {
                bdate.setFullYear(bdate.getFullYear() - 1);
                bdate.setMonth(0);
                bdate.setDate(1);
                edate.setFullYear(edate.getFullYear() - 1);
                edate.setMonth(11);
                edate.setDate(31);
            }    
            if (val) {
                var bdateVal = DateUtils.dateFormat(bdate, 'yyyy-MM-dd');
                var edateVal = DateUtils.dateFormat(edate, 'yyyy-MM-dd');

                $('[name="' + startDate + '"]').val(bdateVal);
                $('[name="' + endDate + '"]').val(edateVal);
            }
        }

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>