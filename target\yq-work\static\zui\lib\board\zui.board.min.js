/*!
 * ZUI: 看板 - v1.9.1 - 2019-05-10
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2019 cnezsoft.com; Licensed MIT
 */
!function(t){"use strict";if(!t.fn.droppable)throw new Error("Droppable requires for boards");var o=function(o,a){this.$=t(o),this.options=this.getOptions(a),this.getLang(),this.init()};o.DEFAULTS={langs:{zh_cn:{append2end:"移动到末尾"},zh_tw:{append2end:"移动到末尾"},en:{append2end:"Move to the end."}}},o.prototype.getOptions=function(a){return a=t.extend({lang:t.zui.clientLang()},o.DEFAULTS,this.$.data(),a)},o.prototype.getLang=function(){var t=this.options;this.lang=t.langs[t.lang]||t.langs[o.DEFAULTS.lang]},o.prototype.init=function(){var o=1,a=this.lang;this.$.find('.board-item:not(".disable-drop"), .board:not(".disable-drop")').each(function(){var i=t(this);i.attr("id")?i.attr("data-id",i.attr("id")):i.attr("data-id")||i.attr("data-id","board"+o++),i.hasClass("board")&&i.find(".board-list").append('<div class="board-item board-item-empty"><i class="icon-plus"></i> {append2end}</div>'.format(a)).append('<div class="board-item board-item-shadow"></div>'.format(a))}),this.bind()},o.prototype.bind=function(o){var a=this.$,i=this.options;a.droppable(t.extend({before:i.before,target:'.board-item:not(".disable-drop, .board-item-shadow")',flex:!0,selector:'.board-item:not(".disable-drop, .board-item-shadow")',start:function(t){a.addClass("dragging").find(".board-item-shadow").height(t.element.outerHeight())},drag:function(t){if(a.find(".board.drop-in-empty").removeClass("drop-in-empty"),t.isIn){var o=t.target.closest(".board").addClass("drop-in"),i=o.find(".board-item-shadow"),e=t.target;a.addClass("drop-in").find(".board.drop-in").not(o).removeClass("drop-in"),i.insertBefore(e),o.toggleClass("drop-in-empty",e.hasClass("board-item-empty"))}},drop:function(o){if(o.isNew){var a;t.isFunction(i.drop)&&(a=i.drop(o)),a!==!1&&o.element.insertBefore(o.target)}},finish:function(){a.removeClass("dragging").removeClass("drop-in").find(".board.drop-in").removeClass("drop-in")}},i.droppable))},t.fn.boards=function(a){return this.each(function(){var i=t(this),e=i.data("zui.boards"),n="object"==typeof a&&a;e||i.data("zui.boards",e=new o(this,n)),"string"==typeof a&&e[a]()})},t.fn.boards.Constructor=o}(jQuery);