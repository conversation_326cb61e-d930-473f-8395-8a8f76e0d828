<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>合同管理</title>
	<style>
		.ibox-content .layui-table-cell {
		    font-size: 12px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">合同号</span>	
							 <input type="text" name="contractNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">合同名称</span>	
							 <input type="text" name="contractName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">产品线</span>	
					     	 <SELECT  data-rules="required" data-mars="YqAdmin.DictDao.select('Y001')"  name="prodLine"  class="form-control input-sm text-val">
			                    	<option value="">请选择</option>
			                 </SELECT>
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">合同类型</span>	
					     	 	<select data-rules="required" data-mars="YqAdmin.DictDao.select('Y004')" name="contractType"  class="form-control input-sm text-val">
			                    	<option value="">请选择</option>
			                    </select>
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">签约单位</span>	
					     	 	<select data-rules="required" name="signEnt" class="form-control input-sm">
			                    	<option value="">请选择</option>
			                    	<option value="广州云趣">广州云趣</option>
			                    	<option value="中融德勤">中融德勤</option>
			                    	<option value="佳都科技">佳都科技</option>
			                    </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="$('#searchForm')[0].reset()">重置</button>
						 </div>
		          	     <div class="pull-right">
		          	     	<EasyTag:hasRole roleId="CONTRACT_MGR">
					  			<button class="btn btn-sm btn-info ml-10" type="button" onclick="list.add()">+ 新增合同</button>
		          	     	</EasyTag:hasRole>
		          	     </div>
	          	     </div>
	          	     <div class="form-group">
	          	     	   <div class="input-group input-group-sm" style="width: 150px;">
							 <span class="input-group-addon">评审编号</span>	
							 <input type="text" name="reviewNo" class="form-control input-sm">
					     </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		 <script type="text/html" id="btnBar">
			<EasyTag:hasRole roleId="CONTRACT_MGR">
			  <button class="btn btn-sm btn-default" type="button" lay-event="list.edit">编辑合同</button>
			  <button class="btn btn-sm btn-default ml-5" type="button" lay-event="list.detail">合同详情</button>
			  <button class="btn btn-sm btn-default ml-5" type="button" lay-event="custDetail">客户详情</button>
			  <button class="btn btn-sm btn-default ml-5" type="button" lay-event="list.appoint">项目经理任命</button>
			  <button class="btn btn-sm btn-default ml-5" type="button" lay-event="projectDetail">项目管理</button>
			  <button class="btn btn-sm btn-default ml-5" type="button" lay-event="list.plan">项目计划</button>
			  <button class="btn btn-sm btn-default ml-5" type="button" lay-event="list.cost">成本管理</button>
			  <button class="btn btn-sm btn-default ml-5" type="button" lay-event="list.change">发生变更</button>
			</EasyTag:hasRole>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ProjectContractDao.contractList',
					height:'full-90',
					limit:20,
					toolbar:'#btnBar',
					edit:'editObj',
					autoSort:false,
					rowEvent:'rowEvent',
					cols: [[
					  {title:'',type:'radio'},
					  {title:'序号',type:'numbers'},
					  {
					    field: 'CONTRACT_NO',
						title: '合同编号',
						align:'left',
						style:'cursor:pointer',
						event:'list.detail',
						minWidth:280,
						templet:'<div>{{d.CONTRACT_NAME}}</div>'
					 },{
						field:'CONTRACT_NO',
						title:'合同号',
						width:100
					},{
						field:'ERP_CONTRACT_NO',
						title:'erp合同号',
						width:100
					},{
						field:'CUSTOMER_NAME',
						title:'客户名称',
						sort:true,
						minWidth:180
					},{
						field:'CONTRACT_STATE',
						title:'项目状态',
						width:90
					},{
						field:'PROD_LINE',
						title:'产品线',
						hide:true,
						width:120
					},{
						field:'CONTRACT_TYPE',
						title:'合同类型',
						width:120
					},{
						field:'START_DATE',
						title:'开始日期',
						hide:true,
						width:90
					},{
						field:'END_DATE',
						title:'结束日期',
						hide:true,
						width:90
					},{
						field:'SALE_DEPT_ID',
						title:'营销大区',
						width:100,
						hide:true,
						templet:function(row){
							return list.getText(row,'SALE_DEPT_NAME');
						}
					},{
						field:'AMOUNT',
						title:'合同金额',
						sort:true,
						width:90
					},{
					    field: 'SALES_BY',
						title: '销售经理',
						align:'center',
						width:100,
						templet:function(row){
							return list.getText(row,'SALES_BY_NAME');
						}
					},{
					    field: 'PAYEE',
						title: '收款人',
						align:'center',
						sort:true,
						hide:true,
						width:100,
						templet:function(row){
							return list.getText(row,'PAYEE_NAME');
						}
					},{
					    field: 'SIGN_DATE',
						title: '签订时间',
						width:120,
						sort:true,
						align:'center'
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						width:120,
						sort:true,
						align:'center'
					}
					]]}
				);
			},
			getText:function(row,field){
				var jsonText = row['TEXT_JSON'];
				if(jsonText){
					var json = eval('(' + jsonText + ')');
					return json[field]||'';
				}
				return '';
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(data){
				if(isArray(data)){
					data = data[0];
				}
				popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/pages/crm/contract/contract-detail.jsp',data:{contractId:data.CONTRACT_ID,custId:data['CUST_ID'],isDiv:0}});
			},
			edit:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的合同。',{icon : 7, time : 1000});
					return;
				}
				var data = dataList[0];
				var projectId = data['CONTRACT_ID'];
				var contractName = data['CONTRACT_NAME'];
				var custId = data['CUST_ID'];
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['60%','100%'],url:'${ctxPath}/pages/crm/contract/contract-edit.jsp',title:'编辑合同',data:{contractId:projectId,custId:custId}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['60%','100%'],url:'${ctxPath}/pages/crm/contract/contract-edit.jsp',title:'新增合同'});
			},
			appoint:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的合同。',{icon : 7, time : 1000});
					return;
				}
				var data = dataList[0];
				var contractId = data['CONTRACT_ID'];
				
			},
			plan:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的合同。',{icon : 7, time : 1000});
					return;
				}
				var data = dataList[0];
				var contractId = data['CONTRACT_ID'];
				
			},
			cost:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的合同。',{icon : 7, time : 1000});
					return;
				}
				var data = dataList[0];
				var contractId = data['CONTRACT_ID'];
				
			},
			change:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的合同。',{icon : 7, time : 1000});
					return;
				}
				var data = dataList[0];
				var contractId = data['CONTRACT_ID'];
				
			}
		}
		
		function editObj(obj){
			var data=obj.data;
		    if(data.CREATOR==getCurrentUserId() || isSuperUser){
				ajax.remoteCall("${ctxPath}/servlet/projectContract?action=updateObj",data,function(result) { 
					if(result.state != 1){
						layer.alert(result.msg,{icon: 7});
					}else{
						layer.msg(result.msg);
					}
				},{loading:false});
		    }else{
		    	layer.msg("修改无效,您无权修改!");
		    }
		}
		
		function projectDetail(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的合同。',{icon : 7, time : 1000});
				return;
			}
			var data = dataList[0];
			var projectId = data['CONTRACT_ID'];
			popup.openTab({url:'${ctxPath}/pages/project/project-detail.jsp',id:'projectDetail',title:'项目管理',data:{projectId:projectId}});
		}
		
		function custDetail(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的合同。',{icon : 7, time : 1000});
				return;
			}
			var data = dataList[0];
			var custId = data['CUST_ID'];
			popup.layerClose('contractDetail');
			popup.layerClose('custDetail');
			if(custId==''){
				return;
			}
			popup.openTab({id:'custDetail',title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:['75%','100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId,isDiv:0}});
		}
		function reloadTaskList(){
			
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>