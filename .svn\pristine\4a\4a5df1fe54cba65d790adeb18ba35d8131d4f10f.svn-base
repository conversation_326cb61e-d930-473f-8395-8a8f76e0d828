<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我参与的项目</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="status" type="hidden" value="${param.status}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-inbox"></span> <span id="title">我参与的项目</span></h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目名称</span>	
							 <input type="text" name="projectName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="mylist"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
  			{{if CREATOR == currentUserId|| isSuperUser || currentUserId == PO}}<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		var status='${param.status}';
		var list={
			init1:function(){
				$("#searchForm").initTable({
					mars:'ProjectDao.myMangerProjectList',
					cellMinWidth:90,
					title:'我负责的项目',
					skin:'line',
					height:'full-120',
					limit:20,
					id:'mylist',
					cols: [[
					  {title:'序号',type:'numbers'},
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						align:'left',
						minWidth:200,
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail'
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:100,
						templet:function(row){
							return projectState(row.PROJECT_STATE);
						}
					},{
					    field: 'PO',
						title: '项目负责人',
						align:'center',
						width:100,
						templet:function(row){
							return getUserName(row.PO);
						}
					},{
					    field: 'TASK_COUNT',
						title: '任务数',
						width:70,
						align:'center'
					},{
					    field: 'PERSON_COUNT',
						title: '参与人数',
						width:100,
						align:'center'
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						width:120,
						align:'center'
					},{
					    field: 'END_DATE',
						title: '截止时间',
						width:120,
						align:'left'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						sort:true,
						width:150,
						align:'center'
					},{
						title: '操作',
						align:'center',
						width:100,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							row['isSuperUser']=isSuperUser;
							return renderTpl('bar1',row);
						}
					}
					]]}
				);
			},
			init2:function(){
				$("#searchForm").initTable({
					mars:'ProjectDao.myJoinProjectList',
					cellMinWidth:90,
					height:'full-120',
					limit:20,
					title:'我参与的项目',
					skin:'line',
					id:'mylist',
					cols: [[
					  {title:'序号',type:'numbers'},
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						minWidth:200,
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail'
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:90,
						templet:function(row){
							return projectState(row.PROJECT_STATE);
						}
					},{
					    field: 'PO',
						title: '项目负责人',
						align:'center',
						width:90,
						templet:function(row){
							return getUserName(row.PO);
						}
					},{
					    field: 'TASK_COUNT',
						title: '任务数',
						width:70,
						align:'center'
					},{
					    field: 'PERSON_COUNT',
						title: '参与人数',
						width:100,
						align:'center'
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						width:120,
						align:'center'
					},{
					    field: 'END_DATE',
						title: '截止时间',
						width:120,
						align:'left'
					}
					]]}
				);
			},
			init3:function(){
				$("#searchForm").initTable({
					mars:'ProjectDao.myCreateProjectList',
					cellMinWidth:90,
					height:'full-120',
					title:'我创建的',
					skin:'line',
					id:'mylist',
					limit:20,
					cols: [[
					  {title:'序号',type:'numbers'},
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						align:'left',
						minWidth:200,
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail'
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:90,
						templet:function(row){
							return projectState(row.PROJECT_STATE);
						}
					},{
					    field: 'PO',
						title: '项目负责人',
						align:'center',
						width:100,
						templet:function(row){
							return getUserName(row.PO);
						}
					},{
					    field: 'TASK_COUNT',
						title: '任务数',
						width:70,
						align:'center'
					},{
					    field: 'PERSON_COUNT',
						title: '参与人数',
						width:90,
						align:'center'
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						width:110,
						align:'center',
						sort:true
					},{
					    field: 'END_DATE',
						title: '截止时间',
						width:110,
						sort:true,
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						width:160,
						sort:true,
						align:'center'
					},{
						title: '操作',
						align:'center',
						width:90,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar1',row);
						}
					}
					]]}
				);
			},
			init4:function(){
				$("#searchForm").initTable({
					mars:'ProjectDao.myFavoriteProject',
					cellMinWidth:90,
					height:'full-120',
					skin:'line',
					title:'我关注的项目',
					id:'mylist',
					limit:20,
					cols: [[
					  {title:'序号',type:'numbers'},
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						minWidth:200,
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail'
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:100,
						templet:function(row){
							return projectState(row.PROJECT_STATE);
						}
					},{
					    field: 'PO',
						title: '项目负责人',
						align:'center',
						width:100,
						templet:function(row){
							return getUserName(row.PO);
						}
					},{
					    field: 'TASK_COUNT',
						title: '任务数',
						width:70,
						align:'center'
					},{
					    field: 'PERSON_COUNT',
						title: '参与人数',
						width:90,
						align:'center'
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						align:'center',
						width:100,
						sort:true
					},{
					    field: 'END_DATE',
						title: '截止时间',
						sort:true,
						width:100,
						align:'left'
					},{
					    field: 'FAVORITE_TIME',
						title: '关注时间',
						width:160,
						sort:true,
						align:'center'
					},{
						title: '操作',
						align:'center',
						width:100,
						templet:'<div><a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.delFavorite">取消关注</div>'
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'编辑项目',data:{projectId:data.PROJECT_ID}});
			},
			detail:function(data){
				popup.openTab({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-detail.jsp',title:'项目管理',data:{projectId:data.PROJECT_ID,status:status}});
			},
			delFavorite:function(data){
				ajax.remoteCall("${ctxPath}/servlet/project?action=delFavorite",{favoriteId:data['PROJECT_ID']},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							list.query();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		function reloadProjectList(){
			list.query();
		}
		function reloadTaskList(){
			
		}
		$(function(){
			if(status==1){
				list.init1();
				$("#title").text("我负责的项目");
			}
			if(status==2){
				list.init2();
				$("#title").text("我参与的项目");
			}
			if(status==3){
				list.init3();
				$("#title").text("我创建的项目");
			}
			if(status==4){
				list.init4();
				$("#title").text("我关注的项目");
			}
	});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>