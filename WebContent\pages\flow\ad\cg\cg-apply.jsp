<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 140px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}自购笔记本电脑补助申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 140px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">入职时间</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date',max:0}"  class="form-control input-sm Wdate" name="apply.data1"/>
					  			</td>
					  			<td>劳动合同到期时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date',min:0}" placeholder="若不记得时间或无固定期限合同,可不填"  class="form-control input-sm Wdate" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">购机日期</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date',max:0}"  class="form-control input-sm Wdate" name="apply.data3"/>
					  			</td>
					  			<td class="required">购机品牌</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">购机价格</td>
					  			<td>
					  				<input type="number" data-rules="required" class="form-control input-sm" onchange="calcBtmoney(this)" name="apply.data5"/>
					  			</td>
					  			<td class="required">补贴金额</td>
					  			<td>
					  				<input type="number" data-rules="required" class="form-control input-sm" readonly="readonly" name="apply.data9"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">审查发票</td>
					  			<td>
					  				<select data-edit-node="hr" data-required="true" class="form-control input-sm" name="apply.data6">
					  					<option value=""></option>
					  					<option value="符合条件">符合条件</option>
					  					<option value="不符合条件">不符合条件</option>
					  				</select>
					  			</td>
					  			<td class="required">审查申请补助条件</td>
					  			<td>
					  				<select data-edit-node="hr" data-required="true" class="form-control input-sm" name="apply.data7">
					  					<option value=""></option>
					  					<option value="初次申请">初次申请</option>
					  					<option value="符合再次申请">符合再次申请</option>
					  					<option value="不符合再次申请">不符合再次申请</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">人力行政部核算</td>
					  			<td colspan="3">
					  				<input data-edit-node="hrhs" class="form-control input-sm" name="apply.data8">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>申请原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  		<tr class="remindInfo hidden-print">
					  			<td>注意事项</td>
					  			<td colspan="3"><small>1、申请前请先和主管沟通确认同意  &nbsp; 2、请上传电脑照片，订单截图，发票</small></td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='hr'){
				var data6 = $("[name='apply.data6']").val();
				if(data6==''){
					layer.msg('请选择');
					return false;
				}
				var data7 = $("[name='apply.data7']").val();
				if(data7==''){
					layer.msg('请选择');
					return false;
				}
			}
			return true;
	  }
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				
			}});
		}

		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		function calcBtmoney(el){
			var num = $(el).val();
			if(num>7200){
				$("[name='apply.data9']").val(7200);
			}else{
				$("[name='apply.data9']").val(num);
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>