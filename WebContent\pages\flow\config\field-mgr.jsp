<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>模板字段配置</title>
	<style>
		.fa-arrow-up,.fa-arrow-down{color: #20a0ff;cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form  class="form-inline" id="FieldForm">
      		 <c:if test="${!empty param.flowCode}">
       			<input name="flowCode" type="hidden" value="${param.flowCode}"/>
       		</c:if>
	    	<div class="layui-card">
				  <div class="layui-card-header">  字段管理
				  		<c:if test="${empty param.flowCode}">
					  		<div class="input-group input-group-sm ml-20">
							    <span class="input-group-addon">大类</span>	
								<select name="pFlowCode" onchange="queryData();" data-mars-reload="false" data-mars="FlowConfigDao.categoryDict" class="form-control input-sm">
								      <option value="">--</option>
							    </select>									  
						  	</div>
					  		<div class="input-group input-group-sm" style="width: 180px;">
							    <span class="input-group-addon">小类</span>	
								<select data-value="" name="flowCode" onchange="queryData();" data-mars-reload="false" data-mars="FlowConfigDao.flowDict" class="form-control input-sm">
								      <option value="">--</option>
							    </select>									  
						  	</div>
							 <div class="input-group input-group-sm">
							    <span class="input-group-addon">状态</span>	
								<select name="flowState" onchange="queryData();" class="form-control input-sm">
								      <option value="">-- </option>
								      <option value="0" data-class="label label-info">启用</option>
								      <option value="1" data-class="label label-warning">暂停 </option>
							    </select>									  
						  	</div>
						   <div class="input-group input-group-sm ml-5">
								 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						   </div>
					   </c:if>
				    <c:if test="${!empty param.flowCode}">
				 	   <button type="button" class="btn btn-sm btn-primary pull-right mt-10" onclick="FieldConfig.addField()">+新增字段</button>
		       		</c:if>
				  </div>
				  <div class="layui-card-body" style="min-height: 400px">
			    	<table class="table table-auto table-hover table-condensed"  data-mars="FlowConfigDao.fieldList">
                           <thead>
                        	 <tr>
						      <th class="text-l">序号</th>
						      <th class="text-l">流程</th>
						      <th class="text-l">导入字段</th>
						      <th class="text-l">释义</th>
						      <th class="text-l">数据库字段</th>
						      <th class="text-l">数据类型</th>
						      <th class="text-l">来源表</th>
						      <th class="text-l" style="width: 180px;">数据配置</th>
						      <th class="text-l">字段描述</th>
						      <th class="text-l">最近编辑人</th>
						      <th class="text-l">最近编辑时间</th>
						      <th class="text-l">操作</th>
   						 </tr>
                           </thead>
                           <tbody id="dataList">
                           </tbody>
                   </table>
                      	 <script id="list-template" type="text/x-jsrender">
								   {{for data}}
										<tr>
											<td>{{:#index+1}} # {{:IDX_ORDER}}</td>											
											<td>{{:FLOW_NAME}}/{{:FLOW_CODE}}</td>
											<td title="{{:HEADER}}">{{:HEADER}}</td>
											<td title="{{:TABLE_FIELD}}">{{:TITLE}}</td>
											<td title="{{:TABLE_FIELD}}">{{:TABLE_FIELD}}</td>
											<td>{{:DATA_TYPE}}</td>											
											<td>{{if TABLE_FLAG=='0'}} 流程表字段 {{else TABLE_FLAG=='2'}}<span class="label label-success">业务表</span>{{else TABLE_FLAG=='1'}} <span class="label label-info">扩展字段</span> {{/if}}</td>											
											<td>{{call:EXT_CONFIG fn='renderConf'}}</td>											
											<td title="{{:TABLE_FLAG}}">{{:REMARK}}</td>											
											<td>{{:UPDATE_BY}}</td>											
											<td>{{:UPDATE_TIME}}</td>											
											<td class="text-l">
												<input type="hidden" id="index_{{:#index+1}}" value="{{:FIELD_ID}}"/>
												<a  href="javascript:void(0)" onclick="FieldConfig.editField('{{:FIELD_ID}}')"><i class="layui-icon layui-icon-edit"></i></a>
												<a  href="javascript:void(0)" class="ml-10" onclick="FieldConfig.delData('{{:FIELD_ID}}',{{:STATE}})"><i class="layui-icon layui-icon-delete"></i></a>
											</td>
									    </tr>
								    {{/for}}					         
							 </script>
			  	  </div>
		  	</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
	   var FieldConfig={};
		
		//新增
		FieldConfig.addField = function() {
			popup.layerShow({type:1,title:'新增字段',offset:'20px',area:['80%','80%'],url:"${ctxPath}/pages/flow/config/field-edit.jsp",data:{flowCode:'${param.flowCode}'}});
		}
		
		//编辑
		FieldConfig.editField = function(fieldId) {
			popup.layerShow({type:1,title:'编辑字段',offset:'20px',area:['80%','80%'],url:"${ctxPath}/pages/flow/config/field-edit.jsp",data:{fieldId:fieldId}});
		}
		
		//删除
		FieldConfig.delData = function(fieldId,state){
			  layer.confirm('是否删除',{icon: 3,offset:'20px'},  function(index){
				layer.close(index);
		  		ajax.remoteCall("${ctxPath}/web/flow/config/deleteField", {fieldId:fieldId}, function(result) {
		  			if(result.state == 1){
					    layer.msg(result.msg,{icon: 1,time:1200,offset:'40px'},function(){
					    	 $("#FieldForm").render(); 
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
					}
	  			});
			});
		}
		
		FieldConfig.reload = function(){
			 $("#FieldForm").render(); 
		}
		
		
	    $(function(){
		   $("#FieldForm").render(); 
	    });
			
	    function queryData(){
	    	$("#FieldForm").render(); 
	    }
	    
	    function renderConf(jsonStr){
    		var html  = [];
	    	if(jsonStr){
	    		var data = eval('('+jsonStr+')');
	    		if(data['listshow']=='1'){
	    			html.push('列表');
	    		}
	    		if(data['importshow']=='1'){
	    			html.push('导入');
	    		}
	    		if(data['exportshow']=='1'){
	    			html.push('导出');
	    		}
	    		if(data['formshow']=='1'){
	    			html.push('表单');
	    		}
	    		if(data['query']){
	    			html.push(data['query']);
	    		}
	    	}
	    	return html.join('；');
	    }
	    
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>