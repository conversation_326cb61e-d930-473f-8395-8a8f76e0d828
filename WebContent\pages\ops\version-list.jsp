<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>版本管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input type="hidden" name="state" id="state"/>
			<input type="hidden" name="projectId" value="${param.projectId}"/>
			<input type="hidden" id="projectName" value="${param.projectName}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          			 <div class="pull-left layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: 0px;margin-bottom: 5px;">
	         		 	 	<ul class="layui-tab-title"><li data-val="" class="layui-this">全部</li><li data-val="0">我创建的</li><li data-val="1">我收到的</li></ul>
	         		 	 </div>
	          		 </div>
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm" style="width: 180px">
							 <span class="input-group-addon">主题</span>	
							 <input type="text" name="versionTitle" class="form-control input-sm">
					     </div>
					     <c:if test="${empty param.projectId}">
		          		     <div class="input-group input-group-sm" style="width: 180px">
								 <span class="input-group-addon">项目名称</span>	
								 <input type="text" name="projectName" class="form-control input-sm">
						     </div>
					     </c:if>
					     <div class="input-group input-group-sm">
							 <span class="input-group-addon">版本状态</span>	
							 <select name="versionState" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option style="display: none;" value="-1">草稿</option>
		                    		<option value="0">已提交</option>
		                    		<option value="10" data-class="label label-success">发版成功</option>
		                    		<option value="20" data-class="label label-danger">版本回退</option>
	                    	</select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						  <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
 				{{if currentUserId == CREATOR || isSuperUser}}<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
 				{{if currentUserId == CREATOR || isSuperUser}}<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.del">删除</a>{{/if}}
		</script>
		<script type="text/html" id="btnBar">
				<button type="button" class="btn btn-sm btn-info" onclick="list.add(1)">+ 合同项目发版</button>
				<button type="button" class="btn btn-sm btn-success ml-10" onclick="list.add(2)">+ 自研产品发版</button>
 		</script>
		<script type="text/html" id="projectBar">
				<button type="button" class="btn btn-sm btn-info" onclick="list.add(1)">+发版</button>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		var  projectId = '${param.projectId}';
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
			
			if(projectId){
				getProjectInfo(projectId,function(data){
					$("#projectName,[name='projectName']").val(data.PROJECT_NAME);
				});
			}
			
			 layui.element.on('tab(stateFilter)', function(){
				   $('#state').val(this.getAttribute('data-val'));
				   $("#searchForm").queryData();
			  });
			 
		});
		var list={
			init:function(){
				var hideProject = false;
				var _bar = '#btnBar';
				if(projectId){
					_bar = '#projectBar';
					hideProject = true;
				}
				$("#searchForm").initTable({
					mars:'VersionDao.list',
					limit:20,
					toolbar:_bar,
					height:'full-100',
					cols: [[
					 {type:'numbers',title:'序号'},
					 {
					    field: 'CREATE_NAME',
						title: '发版人',
						width:80,
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '发布时间',
						width:90,
						align:'center',
						templet:function(row){
							return cutText(row['CREATE_TIME'],12,'');
						}
					},{
					    field: 'VERSION_TITLE',
						title: '主题',
						align:'left',
						minWidth:200,
						event:'list.detail',
						style:'color:#1890ff;cursor: pointer;'
					},{
						field:'PROJECT_NAME',
						title:'所属项目',
						event:'projectDetail',
						minWidth:200,
						hide:hideProject
					},{
						field:'VERSION_STATE',
						title:'版本状态',
						width:80,
						templet:function(row){
							return getText(row['VERSION_STATE'],'versionState');
						}
					},
		             {
					    field: 'VERSION_NAME',
						title: '版本名称',
						align:'center',
						width:120
					},{
						field:'RECEIVER_NAME',
						title:'接收人',
						align:'center',
						width:90
					},{
						field:'RECIPIENT_NAME',
						title:'抄送人',
						width:120
					},{
						field:'FILE_DESC',
						width:130,
						title:'版本文件'
					},{
						field:'VIEW_COUNT',
						width:80,
						title:'查看次数',
						event:'lookLog',
						style:'color:#1890ff;cursor: pointer;'
					},{
						title: '操作',
						width:120,
						align:'center',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							row['isSuperUser']=isSuperUser;
							return renderTpl('bar',row);
						}
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:1,shadeClose:false,scrollbar:false,offset:'r',area:['70%','100%'],url:'${ctxPath}/pages/ops/version-edit.jsp',title:'编辑',data:{versionId:data.VERSION_ID,versionType:data['VERSION_TYPE']}});
			},
			detail:function(data){
				popup.openTab({id:'verionDetail',type:1,shadeClose:false,maxmin:true,anim:1,full:true,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/ops/version-detail.jsp',title:'版本详情',data:{versionId:data.VERSION_ID,versionState:data['VERSION_STATE']}});
			},
			add:function(type){
				var projectName = $('#projectName').val();
				popup.layerShow({type:1,maxmin:true,anim:1,shadeClose:false,scrollbar:false,offset:'r',area:['70%','100%'],url:'${ctxPath}/pages/ops/version-edit.jsp',title:'新增版本',data:{appName:'${param.appName}',versionType:type,projectId:projectId,projectName:projectName}});
			},
			del:function(data){
				layer.confirm("确认要删除吗?",{icon:3},function(){
					ajax.remoteCall("${ctxPath}/servlet/version?action=delete",{'version.VERSION_ID':data.VERSION_ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
					
				});
			}
		}
		function projectDetail(data){
			var projectId = data['PROJECT_ID'];
			var versionType = data['VERSION_TYPE'];
			if(versionType=='1'){
				projectBoard(projectId);
			}
		}
		function lookLog(data){
			lookLogLayer(data['VERSION_ID']);
		}
		function reloadVersion(){
			list.query();
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>