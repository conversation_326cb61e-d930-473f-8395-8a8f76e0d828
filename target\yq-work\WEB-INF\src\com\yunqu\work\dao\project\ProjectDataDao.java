package com.yunqu.work.dao.project;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.utils.DateUtils;

@WebObject(name="ProjectDataDao")
public class ProjectDataDao extends AppDaoContext {
	
	@WebControl(name = "consoleIndexStat",type = Types.RECORD)
	public JSONObject consoleIndexStat() {
		String projectId = param.getString("projectId");
		JSONObject object = new JSONObject();
		int weeklyCount = Db.queryInt("select count(1) from yq_weekly_project where project_id = ?",projectId);
		object.put("weeklyCount",weeklyCount);
		int fileCount = Db.queryInt("select count(1) from yq_files where fk_id = ?",projectId);
		object.put("fileCount",fileCount);
		int versionCount = Db.queryInt("select count(1) from yq_ops_version where project_id = ?",projectId);
		object.put("versionCount",versionCount);
		int riskCount = Db.queryInt("select count(1) from yq_project_risk where project_id = ?",projectId);
		object.put("riskCount",riskCount);
		return getJsonResult(object);
	}
	
	@WebControl(name = "personIndexStat",type = Types.RECORD)
	public JSONObject personIndexStat() {
		String userId = getUserId();
		JSONObject object = new JSONObject();
		int weeklyCount = Db.queryInt("select count(1) from yq_weekly where creator = ?",userId);
		object.put("weeklyCount",weeklyCount);
		int projectCount = Db.queryInt("select count(1) from yq_project_team where USER_ID = ?",userId);
		object.put("projectCount",projectCount);
		int versionCount = Db.queryInt("select count(1) from yq_ops_version where creator = ?",userId);
		object.put("versionCount",versionCount);
		int riskCount = Db.queryInt("select count(1) from yq_project_risk where creator = ?",userId);
		object.put("riskCount",riskCount);
		int bugCount = Db.queryInt("select count(1) from zentao.zt_bug where assignedTo = ?",getLoginAcct());
		object.put("bugCount",bugCount);
		return getJsonResult(object);
	}
	
	@WebControl(name="myProject",type=Types.TEMPLATE)
	public JSONObject myProject(){
		EasyQuery query = getQuery();
		query.setMaxRow(10);
		setQuery(query);
		EasySQL sql= getEasySQL("select t1.* from yq_project t1  where 1=1");
		sql.append("and t1.is_delete = 0 and");
		sql.append(getUserId(),"(find_in_set(?,t1.PO) or");
		sql.append(getUserId(),"find_in_set(?,t1.PROJECT_PO))");
		sql.append("order by t1.last_task_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="myFavoriteProject",type=Types.TEMPLATE)
	public JSONObject myFavoriteProject(){
		EasyQuery query = getQuery();
		query.setMaxRow(10);
		setQuery(query);
		EasySQL sql=getEasySQL("select t1.*,t2.favorite_time from yq_project t1 INNER JOIN yq_favorite t2 on t2.fk_id=t1.PROJECT_ID where  1=1");
		sql.append(getUserId(),"and t2.favorite_by = ?");
		sql.append("and t1.is_delete = 0");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("ORDER BY t2.favorite_time desc ");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "projectAction",type = Types.TEMPLATE)
	public JSONObject projectAction() {
		String projectId = param.getString("projectId");
		EasySQL sql = new EasySQL();
		sql.append("SELECT tt.* FROM(");
		sql.append(" SELECT CONCAT( t1.task_name, '>', t2.content) content, t2.create_name, t2.create_time,'comment' type,t1.task_id id FROM yq_task t1");
		sql.append(projectId,",yq_comments t2 WHERE t1.task_id = t2.fk_id AND t1.project_id = ? ");
		sql.append("UNION");
		sql.append(projectId," SELECT CONCAT(t1.task_name, '#',t1.assign_user_name) content, t1.create_name, t1.create_time,'task' type,t1.task_id id FROM yq_task t1 where t1.project_id = ?");
		sql.append("UNION");
		sql.append(" SELECT t1.work_content content, t2.create_name, t2.create_time,'weekly' type,t2.weekly_id id FROM yq_weekly_project t1");
		sql.append(projectId,", yq_weekly t2 WHERE t1.project_id = ? AND t1.weekly_id = t2.weekly_id");
		sql.append(" ) tt ORDER BY tt.create_time desc LIMIT 20");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "myProjectAction",type = Types.TEMPLATE)
	public JSONObject myProjectAction() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT tt.* FROM(");
		sql.append(" SELECT CONCAT(t2.create_name,'发布评论：',t1.task_name,'#',t2.content) content, t1.create_name, t2.create_time,t1.task_id id,'comment' type FROM yq_task t1");
		sql.append(getUserId(),",yq_comments t2 WHERE t1.task_id = t2.fk_id AND ( t1.creator = ? ");
		sql.append(getUserId(),"or t1.assign_user_id = ?)");
		sql.append(DateUtils.hourAfter(-24*15),"and t2.create_time > ?");
		sql.append("UNION");
		sql.append(" SELECT  CONCAT(t1.create_name,'任务：',t1.task_name) content,t1.create_name, t1.create_time,t1.task_id id,'task' type FROM yq_task t1");
		sql.append(getUserId(),"WHERE ( t1.creator = ? ");
		sql.append(getUserId(),"or t1.assign_user_id = ?)");
		sql.append(DateUtils.hourAfter(-24*15),"and t1.create_time > ?");
		sql.append("UNION");
		sql.append(" SELECT CONCAT(t1.create_name,'填写周报：',t1.title) content, t1.create_name, t1.create_time,t1.weekly_id id,'weekly' type FROM yq_weekly t1");
		sql.append(getUserId()," WHERE (t1.creator = ?");
		sql.append(getUserId(),"or t1.receiver = ?)");
		sql.append(DateUtils.hourAfter(-24*15),"and t1.create_time > ?");
		sql.append(" ) tt ORDER BY tt.create_time desc LIMIT 25");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "myProjectIndexTaskList",type = Types.PAGE)
	public JSONObject myProjectIndexTaskList() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT tt.* FROM(");
		sql.append("select t1.*,t2.PROJECT_NAME from yq_task t1 left join YQ_PROJECT t2 on t1.PROJECT_ID = t2.PROJECT_ID where 1=1");
		sql.append(getUserId(),"and (t1.creator = ?");
		sql.append(getUserId(),"or t1.assign_user_id = ?)");
		sql.append(DateUtils.hourAfter(-24*60),"and t1.create_time > ?");
		sql.append("UNION");
		sql.append("select t1.*,t2.PROJECT_NAME from yq_task t1 left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID INNER JOIN yq_cc t3 on t3.fk_id = t1.task_id where 1=1");
		sql.append(getUserId(),"and t3.user_id = ?");
		sql.append(DateUtils.hourAfter(-24*60),"and t1.create_time > ?");
		if(isDeptLeader()) {
			sql.append("UNION");
			sql.append("select t1.*,t2.PROJECT_NAME from yq_task t1 left join YQ_PROJECT t2 on t1.PROJECT_ID = t2.PROJECT_ID where 1=1 ");
			sql.append(getDeptId(),"and (t1.dept_id = ?");
			sql.append(getDeptId()," or assign_dept_id = ?)");
			sql.append(DateUtils.hourAfter(-24*60),"and t1.create_time > ?");
		}
		sql.append(" ) tt ");
		String sortName = param.getString("sortName");
		String sortType = param.getString("sortType");
		if(StringUtils.notBlank(sortName)&&StringUtils.notBlank(sortType)) {
			sql.append("order by tt."+sortName+" "+sortType);
			sql.append(",tt.create_time desc");
		}else {
			sql.append("order by tt.create_time desc");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
		
	@WebControl(name="projectStateLog",type=Types.LIST)
	public JSONObject projectStateLog(){
		EasySQL sql = getEasySQL("select * from yq_project_follow_record where 1 = 1");
		sql.append(param.getString("projectId")," and PROJECT_ID = ?",false);
		sql.append("order by update_time desc limit 5");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectFollowList",type=Types.PAGE)
	public JSONObject projectFollowList(){
		EasySQL sql = getEasySQL("select * from yq_project_follow_record where 1 = 1");
		sql.append(param.getString("projectId")," and project_id = ?");
		sql.append(param.getString("followState"),"and follow_state = ?");
		sql.append(param.getString("updateBy"),"and update_by = ?");
		sql.append(param.getString("stakeholderId"),"and stakeholder_id = ?");
		sql.append(param.getString("level"),"and level = ?");
		sql.append(param.getString("isPublic"),"and is_public = ?");
		sql.appendLike(param.getString("stakeholder"),"and stakeholder like ?");
		sql.appendLike(param.getString("createName"),"and create_name like ?");
		sql.append("order by update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="myProjectFollowList",type=Types.TEMPLATE)
	public JSONObject myProjectActionTodo(){
		EasySQL sql = getEasySQL("select t1.* from yq_project_follow_record t1 where 1=1");
		sql.append(1,"and t1.follow_state = ?");
		sql.append(getUserId()," and (t1.stakeholder_id = ? or");
		sql.append(getUserId(),"t1.update_by = ? )");
		sql.append("order by t1.update_time desc limit 8");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectPlanPage",type = Types.PAGE)
	public JSONObject projectPlanPage(){
		EasySQL sql = getEasySQL("SELECT t2.*,t1.project_name,t1.project_stage,t1.cy_date,t1.zy_date,t1.project_po_name,t1.po_name from yq_project_plan t2,yq_project t1 where ");
		sql.append("t1.project_id = t2.project_id");
		sql.append(param.getString("projectId"),"and t1.project_id = ?");
		sql.append("and t2.plan_begin_date<>''");
		sql.append("ORDER BY  t2.plan_begin_date");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="myProjectPlan",type = Types.TEMPLATE)
	public JSONObject myProjectPlan(){
		EasySQL sql = getEasySQL("select t1.*,t2.project_name from yq_project_plan t1,yq_project t2");
		sql.append("where 1=1 and t1.project_id = t2.project_id");
		sql.append(getUserId(),"and t1.plan_person_id = ?");
		sql.appendIn(new int[]{1,5},"and t1.plan_state");
		sql.append("order by t1.plan_begin_date desc limit 10");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="projectRiskList",type = Types.TEMPLATE)
    public JSONObject projectRiskList(){
    	EasySQL sql = getEasySQL("select t1.* from yq_project_risk t1");
    	sql.append("where 1=1");
    	sql.append(param.getString("projectId")," and t1.project_id = ?");
    	sql.append("order by t1.create_time desc limit 3");
    	return queryForList(sql.getSQL(),sql.getParams());
    }
	
	@WebControl(name="projectPlanList",type = Types.TEMPLATE)
	public JSONObject projectPlanList(){
		EasySQL sql = getEasySQL("select t1.* from yq_project_plan t1");
		sql.append("where 1=1");
		sql.append(param.getString("projectId")," and t1.project_id = ?");
		sql.append("and plan_begin_date !=''");
		sql.append("and plan_state > 0");
		sql.append("order by t1.plan_begin_date desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="mavenLog",type = Types.TEMPLATE)
	public JSONObject mavenLog(){
		EasySQL sql = getEasySQL("select t1.* from yq_maven_log t1");
		sql.append("where 1=1");
		sql.append(param.getString("consoleUrl"),"and CONSOLE_URL = ?");
		sql.append(param.getString("computer"),"and COMPUTER = ?");
		sql.append(param.getString("appId"),"and APP_ID = ?");
		sql.append(param.getString("pushFlag"),"and PUSH_FLAG = ?");
		sql.append("order by t1.build_date desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="myProjectRiskList",type = Types.TEMPLATE)
	public JSONObject myProjectRiskList(){
		EasySQL sql = getEasySQL("select t1.*,t2.project_name from yq_project_risk t1,yq_project t2");
		sql.append("where 1=1 and t1.project_id = t2.project_id");
		sql.append(0,"and t1.risk_state = ?");
		sql.append(getUserId()," and (t1.sol_owner = ? or");
		sql.append(getUserId(),"t1.creator = ? )");
		sql.append("order by t1.create_time desc limit 8");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name="projectFlowList",type = Types.TEMPLATE)
	public JSONObject projectFlowList(){
		JSONObject result = new JSONObject();
		String projectId = param.getString("projectId");
		String flow = param.getString("flow");
		EasySQL sql = null;
		if("plan".equals(flow)) {
			sql = getEasySQL("SELECT apply_id from yq_flow_apply where flow_code='pj_plan' and data17 = ? and apply_state in (10,20,30)");
			String planApplyId = queryForString(sql.getSQL(), new Object[] {projectId});
			result.put("planApplyId", planApplyId);
		}
		if("pm".equals(flow)) {
			sql = getEasySQL("SELECT apply_id from yq_flow_apply where flow_code='pj_pm' and data16 = ? and apply_state in (10,20,30)");
			String pmApplyId = queryForString(sql.getSQL(), new Object[] {projectId});
			result.put("pmApplyId", pmApplyId);
		}
		if("planchange".equals(flow)) {
			sql = getEasySQL("select apply_id,apply_name,apply_title,apply_date from yq_flow_apply where data19 = ? and flow_code = 'pj_planchange' and apply_state in(10,20,30)");
			try {
				List<JSONObject> planchangeList = this.getQuery().queryForList(sql.getSQL(),new Object[] {projectId},new JSONMapperImpl());
				result.put("planchangeList", planchangeList);
			} catch (SQLException e) {
				this.getLogger().error(e.getMessage(),e);
			}
		}
		return getJsonResult(result);
	}

	@WebControl(name="projectLogQuery",type=Types.PAGE) 
	public JSONObject projectLogQuery() {
		EasySQL sql = getEasySQL("select t1.*,t2.project_name from yq_project_oplog t1,yq_project t2 where t1.project_id = t2.project_id");
		// 操作人查询
		sql.appendLike(param.getString("opName"),"and t1.op_name like ?");
		sql.appendLike(param.getString("projectName"),"and t2.project_name like ?");
		// 时间范围查询
		String startTime = param.getString("startTime");
		String endTime = param.getString("endTime");
		if(StringUtils.notBlank(startTime)) {
			sql.append(startTime + " 00:00:00", "and t1.op_time >= ?");
		}
		if(StringUtils.notBlank(endTime)) {
			sql.append(endTime + " 23:59:59", "and t1.op_time <= ?");
		}
		sql.appendLike(param.getString("content"),"and t1.content like ?");
		sql.append("order by t1.op_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="lookLogQuery",type=Types.PAGE) 
	public JSONObject lookLogQuery() {
		EasySQL sql = getEasySQL("select t1.*,t2.project_name from yq_look_log t1,yq_project t2 where t1.fk_id = t2.project_id and t1.type='project'");
		// 操作人查询
		sql.appendLike(param.getString("lookName"),"and t1.look_name like ?");
		sql.appendLike(param.getString("projectName"),"and t2.project_name like ?");
		// 时间范围查询
		String startTime = param.getString("startTime");
		String endTime = param.getString("endTime");
		if(StringUtils.notBlank(startTime)) {
			sql.append(startTime + " 00:00:00", "and t1.look_time >= ?");
		}
		if(StringUtils.notBlank(endTime)) {
			sql.append(endTime + " 23:59:59", "and t1.look_time <= ?");
		}
		sql.append("order by t1.look_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}


}
