package com.yunqu.work.model;

import com.yunqu.work.base.AppBaseModel;

/**
 * <AUTHOR>
 */
public class ContractStageModel extends AppBaseModel {
    private static  final long serialVersionUID = 1L;

    public ContractStageModel() {
        setTableInfo("YQ_CONTRACT_STAGE","STAGE_ID");
    }

    public String getStageId() {
        return getString("STAGE_ID");
    }

    public void setStageId(String stageId) {
        this.set("STAGE_ID", stageId);
    }

    public String getContractId() {
        return getString("CONTRACT_ID");
    }

    public void setContractId(String contractId) {
        this.set("CONTRACT_ID", contractId);
    }

    public String getStageName() {
        return getString("STAGE_NAME");
    }

    public void setStageName(String stageName) {
        this.set("STAGE_NAME", stageName);
    }

}
