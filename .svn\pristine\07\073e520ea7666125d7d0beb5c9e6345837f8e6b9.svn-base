<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title></title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="contractReceiptEditForm" class="form-horizontal" data-mars="ContractReceiptDao.record" autocomplete="off" data-mars-prefix="receipt.">
        <input type="hidden" value="${param.receiptId}" name="receipt.RECEIPT_ID"/>
        <input type="hidden" value="${param.contractId}" name="receipt.CONTRACT_ID"/>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td width="120px" class="required">收款日期</td>
                <td width="40%">
                    <input type="text" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="receipt.RECEIPT_DATE" class="form-control input-sm Wdate">
                </td>
                <td colspan="2" style="background-color: white"></td>
            </tr>
            <tr>
                <td class="required">收款金额</td>
                <td>
                    <input type="number" data-rules="required" name="receipt.AMOUNT" class="form-control">
                </td>
                <td>收款币种</td>
                <td>
                    <input type="text" name="receipt.CURRENCY" class="form-control" value="CNY">
                </td>
            </tr>
            <tr>
                <td>收款考核数据</td>
                <td>
                    <input type="text" name="receipt.KAOHE_DATA" class="form-control">
                </td>
                <td>本笔收款责任人</td>
                <td>
                    <input type="hidden" name="receipt.OWNER_ID">
                    <input name="receipt.OWNER_NAME" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm" placeholder="点击选择责任人">
                </td>
            </tr>
            <tr>
                <td>合同阶段</td>
                <td>
                    <SELECT name="receipt.STAGE_ID" data-mars="ContractStageDao.selectStage(${param.contractId})" class="form-control">
                        <option value="">请选择</option>
                    </SELECT>
                </td>
                <td>关联客户</td>
                <td>
                    <input type="hidden" name="receipt.CUST_NO">
                    <input type="hidden" name="receipt.CUST_ID" id="custId">
                    <input type="text"  onclick="singleCust(this);" readonly="readonly" placeholder="请点击选择客户" name="receipt.CUSTOMER_NAME"  class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <textarea style="height: 60px" type="text"  name="receipt.REMARK" class="form-control">
                    </textarea>
                </td>
            </tr>
            </tbody>
        </table>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="contractReceiptEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("contractReceiptEdit");

        contractReceiptEdit.receiptId = '${param.receiptId}';
        contractReceiptEdit.isNew = '${param.isNew}';
        contractReceiptEdit.contractId = '${param.contractId}';

        $(function(){
            $("#contractReceiptEditForm").render({success:function(result){

                }});
        });

        contractReceiptEdit.ajaxSubmitForm = function(){
            if(form.validate("#contractReceiptEditForm")){
                if(contractReceiptEdit.isNew=='1'){
                    contractReceiptEdit.insertData();
                }
                else if(contractReceiptEdit.receiptId){
                    contractReceiptEdit.updateData();
                }else{
                    contractReceiptEdit.insertData();
                }
            };
        }

        contractReceiptEdit.insertData = function() {
            var data = form.getJSONObject("#contractReceiptEditForm");
            ajax.remoteCall("${ctxPath}/servlet/contractReceipt?action=add",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadContractReceiptList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        contractReceiptEdit.updateData = function() {
            var data = form.getJSONObject("#contractReceiptEditForm");
            ajax.remoteCall("${ctxPath}/servlet/contractReceipt?action=update",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadContractReceiptList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        function selectReviewCallBack(row){
            $("[name='receipt.CUSTOMER_NAME']").val(row['CUST_NAME']);
            $("[name='receipt.CUST_ID']").val(row['CUST_ID']);
            $("[name='receipt.CUST_NO']").val(row['CUST_NO']);
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>