package com.yunqu.work.ext;

import java.sql.SQLException;

import org.easitline.common.core.EasyPool;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;

import com.jfinal.config.Constants;
import com.jfinal.config.Handlers;
import com.jfinal.config.Interceptors;
import com.jfinal.config.JFinalConfig;
import com.jfinal.config.Plugins;
import com.jfinal.config.Routes;
import com.jfinal.json.FastJsonFactory;
import com.jfinal.kit.PropKit;
import com.jfinal.log.Log4jLogFactory;
import com.jfinal.plugin.activerecord.ActiveRecordPlugin;
import com.jfinal.plugin.activerecord.Config;
import com.jfinal.plugin.activerecord.DbKit;
import com.jfinal.plugin.activerecord.dialect.MysqlDialect;
import com.jfinal.render.ViewType;
import com.jfinal.template.Engine;
import com.jfinal.weixin.sdk.api.ApiConfig;
import com.jfinal.weixin.sdk.api.ApiConfigKit;
import com.yunqu.work.ext.handler.DataHandler;
import com.yunqu.work.ext.handler.UrlSkipHandler;
import com.yunqu.work.ext.interceptor.GlobalInterceptor;
import com.yunqu.work.servlet.common.FilesController;
import com.yunqu.work.servlet.wx.WeixinApiController;
import com.yunqu.work.servlet.wx.WeixinMsgController;
import com.yunqu.work.utils.LogUtils;
/**
 * 应用全局配置中心
 *
 */
public class AppWebConfig extends JFinalConfig {

	@Override
	public void configConstant(Constants me) {
		PropKit.use("config.txt");
		me.setError404View("/pages/common/404.jsp");
		me.setError500View("/pages/common/500.jsp");
		me.setDenyAccessJsp(false);
		me.setJsonFactory(new FastJsonFactory());
		me.setLogFactory(new Log4jLogFactory());
		me.setViewType(ViewType.JSP);
		me.setDevMode(ServerContext.isDebug());
		ApiConfigKit.setDevMode(me.getDevMode());
		me.setBaseUploadPath(Globals.TMP_DIR);
		me.setBaseDownloadPath(Globals.FILE_SERVER_DIR);
	}

	@Override
	public void configEngine(Engine engine) {
		engine.setDevMode(ServerContext.isDebug());
	}

	@Override
	public void configHandler(Handlers me) {
		me.add(new DataHandler());
		me.add(new UrlSkipHandler());
	}

	@Override
	public void configInterceptor(Interceptors me) {
		me.add(new GlobalInterceptor());
	}

	@Override
	public void configPlugin(Plugins me) {
		try {
			Config config = null;
			try {
				config = new Config(DbKit.MAIN_CONFIG_NAME, EasyPool.getInstance().getDatasource(AppContext.getContext(com.yunqu.work.base.Constants.APP_NAME).getDatasourceName(com.yunqu.work.base.Constants.DS_NAME)));
			} catch (SQLException e) {
				e.printStackTrace();
			}
//		    DbKit.addConfig(config);
			
			ActiveRecordPlugin arp = new ActiveRecordPlugin(config);
			arp.setDevMode(ServerContext.isDebug());
			arp.setShowSql(true);
			arp.setDialect(new MysqlDialect());
			arp.addSqlTemplate("all.sql");
			
			Engine engine = arp.getEngine();
			engine.setCompressorOn('\n');
			engine.setCompressorOn(' ');
			
			me.add(arp);
//			me.add(new EhCachePlugin());
			
		} catch (Exception e) {
			LogUtils.getLogger().error(e.getMessage(),e);
		}
	}

	@Override
	public void configRoute(Routes me) {
		me.setMappingSuperClass(true);
		me.setBaseViewPath("/pages/");
		me.scan("com.yunqu.work");
		me.add("/msg", WeixinMsgController.class);
		me.add("/api", WeixinApiController.class);
		me.add("/files", FilesController.class);
	}
	

	@Override
	public void onStart() {
		super.onStart();
	}

	@Override
	public void onStop() {
		super.onStop();
	}

	@Override
	public void afterJFinalStart() {
		ApiConfig ac = new ApiConfig();
        ac.setToken(PropKit.get("token"));
        ac.setAppId(PropKit.get("appId"));
        ac.setAppSecret(PropKit.get("appSecret"));

        /**
                     *  是否对消息进行加密，对应于微信平台的消息加解密方式：
         *  1：true进行加密且必须配置 encodingAesKey
         *  2：false采用明文模式，同时也支持混合模式
         */
        ac.setEncryptMessage(PropKit.getBoolean("encryptMessage", false));
        ac.setEncodingAesKey(PropKit.get("encodingAesKey", ""));

        /**
                  * 多个公众号时，重复调用ApiConfigKit.putApiConfig(ac)依次添加即可，第一个添加的是默认。
         */
        ApiConfigKit.putApiConfig(ac);
	
	}

	@Override
	public void beforeJFinalStop() {
		
	}
	

}
