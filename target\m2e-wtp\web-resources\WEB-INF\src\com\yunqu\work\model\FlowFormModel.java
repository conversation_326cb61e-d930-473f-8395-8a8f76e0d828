package com.yunqu.work.model;

import java.io.Serializable;

import org.easitline.common.utils.string.StringUtils;

public class FlowFormModel  implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String tableName;
	
	private String formId;
	
	private String formName;
	
	private String formCode;
	
	private String detailCode;
	

	public String getTableName() {
		if(StringUtils.isBlank(tableName)) {
			tableName = "yq_flow_apply_ext";
		}
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getFormId() {
		return formId;
	}

	public void setFormId(String formId) {
		this.formId = formId;
	}

	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}

	public String getFormCode() {
		return formCode;
	}

	public void setFormCode(String formCode) {
		this.formCode = formCode;
	}

	public String getDetailCode() {
		return detailCode;
	}

	public void setDetailCode(String detailCode) {
		this.detailCode = detailCode;
	}

	
	
}
