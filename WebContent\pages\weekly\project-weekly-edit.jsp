<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>周报</title>
	<style>
		.textare-weekly,.weeklyFile{display: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="weekEditForm" data-mars="WeeklyDao.projectRecord" autocomplete="off" data-mars-prefix="weekly.">
  		   		<input type="hidden"  name="weekly.STATUS"/>
  		   		<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
  		   		<input type="hidden" id="year" name="weekly.YEAR"/>
  		   		<input type="hidden" id="weekyNo" name="weekly.WEEK_NO"/>
  		   		<input type="hidden" value="${param.projectId}" name="weekly.PROJECT_ID"/>
  		   		<input type="hidden" id="weeklyId" value="${param.weeklyId}" name="weekly.WEEKLY_ID"/>
  		   		<input type="hidden" value="${param.weeklyId}" name="fkId"/>
  		   		<input type="hidden" value="${param.projectName}" name="projectName"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
						<tr>
							<td>主题</td>
							<td colspan="3">
		                    	<select style="width: 20%;display: inline-block;" onchange="changeWeekly($(this))" name="weekly.YEAR_WEEK" data-mars="WeeklyDao.getProjectRecentWeek" data-template="weeklys-template" name="weekly.WEEKLY_NO" class="form-control input-sm">
		                    	</select>
		                    	<script id="weeklys-template" type="text/x-jsrender">
		                    		<option value="">请选择周期</option>
								    {{for data}}
		                    			<option value="{{:YEAR_WEEK_NO}}" data-title="{{:TITLE}}">{{:YEAR}}年第{{:WEEK_NO}}周</option>
								    {{/for}}
								</script>
								<input style="width: 79%;display: inline-block;" name="weekly.TITLE" id="title" type="text" class="form-control input-sm"/>
							</td>
						</tr>
						<tr>
		                    <td class="required" style="width:110px;" >主送</td>
		                    <td style="width: 30%;">
		                    	<select id="users" data-mars="CommonDao.userDict" name="weekly.RECEIVER" class="form-control input-sm">
		                    		<option value="">暂不发送</option>
		                    	</select>
		                    </td>
		                    <td style="width: 90px;">抄送</td>
		                    <td>
		                    	<select  multiple="multiple" data-mars="CommonDao.userDict" id="mailtos" name="mailtos" class="form-control input-sm">
		                    	</select>
		                    </td>
	              		</tr>
			        </tbody>
				</table>
				<table class="table table-edit table-vzebra tplContent">
			        <tbody>
			        	<tr>
			        	 	<td style="width:110px;" class="required">项目阶段</td>
		                    <td style="width: 30%;">
			                    <select class="form-control input-sm" style="width:100%;" data-rules="required" name="weekly.STAGE">
		                    		<option value="">请选择</option>
			                    	<optgroup label="需求阶段">
			                    		<option value="项目启动/进度正常">进度正常</option>
				                  		<option value="项目启动/存在风险">存在风险</option>
				                  		<option value="项目启动/进度失控">进度失控</option>
				                  		<option value="项目启动/挂起中">挂起中</option>
				                  		<option value="项目启动/已完成">已完成</option>
			                    	</optgroup>
			                    	<optgroup label="设计阶段">
			                    		<option value="项目启动/进度正常">进度正常</option>
				                  		<option value="项目启动/存在风险">存在风险</option>
				                  		<option value="项目启动/进度失控">进度失控</option>
				                  		<option value="项目启动/挂起中">挂起中</option>
				                  		<option value="项目启动/已完成">已完成</option>
			                    	</optgroup>
			                    	<optgroup label="开发阶段">
				                  		<option value="项目启动/进度正常">进度正常</option>
				                  		<option value="项目启动/存在风险">存在风险</option>
				                  		<option value="项目启动/进度失控">进度失控</option>
				                  		<option value="项目启动/挂起中">挂起中</option>
				                  		<option value="项目启动/已完成">已完成</option>
			                    	</optgroup>
			                    	<optgroup label="系统上线">
			                    		<option value="项目启动/进度正常">进度正常</option>
				                  		<option value="项目启动/存在风险">存在风险</option>
				                  		<option value="项目启动/进度失控">进度失控</option>
				                  		<option value="项目启动/挂起中">挂起中</option>
				                  		<option value="项目启动/已完成">已完成</option>
			                    	</optgroup>
		                   	   </select>
		                    </td>
			        	 	<td class="required" style="width: 90px;">填写方式</td>
		                    <td>
			                    <label class="radio radio-success radio-inline">
			                    	<input type="radio" value="0" checked="checked" name="weekly.WEEK_TYPE" onclick="loadWeekType(0)"/> <span>文本周报</span>
			                    </label>
			                    <label class="radio radio-success radio-inline">
			                    	<input type="radio" value="1" name="weekly.WEEK_TYPE" onclick="loadWeekType(1)"/> <span>文件周报</span>
			                    </label>
		                    </td>
			        	</tr>
			        	<tr class="weeklyFile">
			        	 	<td style="vertical-align: top;">周报摘要</td>
		                    <td colspan="3">
			                    <textarea class="form-control input-sm" name="weekly.WEEK_SUMMARY" style="width: 100%;height: 60px;">详情请见附件。</textarea> 
		                    </td>
			        	</tr>
			        	<tr class="weeklyFile">
			        	 	<td class="required">周报附件</td>
		                    <td colspan="3">
			                    <input type="hidden" name="weekly.WEEK_FILE_ID"/> 
			                    <input type="hidden" name="weekly.WEEK_FILE"/> 
			                    <span id="weeklyFileSpan"></span>
			                    <button class="btn btn-xs btn-link" type="button" onclick="isOtherFile=0;$('#peojectWeeklyLocalfile').click();">+上传</button>  
			                    <a class="btn btn-sm btn-link" href="http://doc.yunqu-info.com/f/9152" target="_blank">模板</a>
		                    </td>
			        	</tr>
			            <tr  class="textare-weekly">
			           	 <td style="width:90px;vertical-align: top;" class="required">
			           	  	 本周工作总结
			           	 </td>
			             <td  colspan="3">
			             	<div id="editor"></div>
			             	<textarea id="wordText" name="weekly.THIS_WORK" maxlength="5000" style="height:150px;display: none;" class="form-control input-sm"></textarea>
			             </td>
			           </tr>
			           <tr class="textare-weekly">
			           	 <td class="required" style="vertical-align: top;">
			           		 下周工作计划
			           	 </td>
			             <td  colspan="3">
			             	<div id="editor2"></div>
			             	<textarea id="wordText2" name="weekly.NEXT_WORK" style="height:80px;display: none;" class="form-control input-sm"></textarea>
			             </td>
			           </tr>
		              <tr>
		                <td>其他附件</td>
		               	<td colspan="3">
		               		<div data-template="projectWeekly-template-files" data-mars="FileDao.fileList"></div>
		               		<div id="projectWeeklyFileList"></div>
							<button class="btn btn-sm btn-default mt-5" type="button" onclick="isOtherFile=1;$('#peojectWeeklyLocalfile').click()">+添加</button>
		               	</td>
			           </tr>
			        </tbody>
				 </table>
				 <div class="layer-foot text-c" style="z-index: 99999999999;">
				    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm()"> 提 交 </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
  		<script id="projectWeekly-template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="projectWeeklyFileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="peojectWeeklyLocalfile" onchange="Edit.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js?v=0311"></script>
	<script type="text/javascript">
		
		var isOtherFile = 0 ;
	     
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.config.menus = [
	 		    'head',  // 标题
	 		    'bold',  // 粗体
	 		    'fontSize',  // 字号
	 		    'fontName',  // 字体
	 		    'foreColor',  // 文字颜色
	 		    'link',  // 插入链接
	 		    'list',  // 列表
	 		    'justify',  // 对齐方式
	 		    'quote',  // 引用
	 		    'code',  // 插入代码
	 		    'emoticon',  // 表情
	 		    'image',  // 插入图片
	 		    'table',  // 表格'
	 	];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.config.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.create();
		
		var editor2 = new E('#editor2');
		editor2.config.menus = [
	 		    'head',  // 标题
	 		    'bold',  // 粗体
	 		    'fontSize',  // 字号
	 		    'fontName',  // 字体
	 		    'foreColor',  // 文字颜色
	 		    'link',  // 插入链接
	 		    'list',  // 列表
	 		    'justify',  // 对齐方式
	 		    'quote',  // 引用
	 		    'code',  // 插入代码
	 		    'emoticon',  // 表情
	 		    'image',  // 插入图片
	 		    'table',  // 表格'
	 	];
		weUpload(editor2,{uploadImgMaxLength:3});
		editor2.config.onchange = function (html) {
		     $("#wordText2").val(html)
		}
		editor2.create();
	
		jQuery.namespace("Edit");
		Edit.weeklyId='${param.weeklyId}';
		function autoHeight(){
			$("textarea").keydown(function(event) {
				  if(event.keyCode == "13") {
					 var height = $(this).height();
					 $(this).css('height',(height+20)+'px');
				  }
				   if(event.keyCode == 8) {
					 var height = $(this).height();
					 height=height>80?height:80;
					 $(this).css('height',(height-20)+'px');
				   }
			});
			$(".w-e-text-container").keydown(function(event) {
				  if(event.keyCode == "13") {
					 var height = $(this).height();
					 $(this).css('height',(height+20)+'px');
				  }
				   if(event.keyCode == 8) {
					 var height = $(this).height();
					 height=height>120?height:120;
					 $(this).css('height',(height-20)+'px');
				   }
			});
		}
		function loadWeeklyData(){
			$("#weekEditForm").render({success:function(result){
				 editor.txt.html($("#wordText").val());
				 editor2.txt.html($("#wordText2").val());
				 
				 
				var record=result['WeeklyDao.projectRecord'];
				var data=record.data;
				
				var weekType = data['WEEK_TYPE'];
				
				loadWeekType(weekType);
				   
				if(weekType=='1'){
					$('#weeklyFileSpan').text(data['WEEK_FILE']);
				}
				
				if(data&&data.STATUS==2){
					$("#users").prop("disabled", true);
				}
				var mailtos=record.mailtos;
				if(mailtos){
					$("[name='mailtos']").val(mailtos);
				}
				
				fillRecord(data,'weekly.','',"#weekEditForm");
				$(".w-e-text-container").first().css("height","120px");
				$(".w-e-text-container").last().css("height","120px");
				if(Edit.weeklyId==''){
					var weeklyReceiver = localStorage.getItem("weeklyReceiver")||'';
					var weeklyMailtos = localStorage.getItem("weeklyMailtos")||'';
					$("#users").val(weeklyReceiver);
					$("[name='mailtos']").val(weeklyMailtos.split(","));
				}
				requreLib.setplugs('select2',function(){
					$("#users").select2({theme: "bootstrap",placeholder:'请选择主送'});
					$("#mailtos").select2({theme: "bootstrap",placeholder:'请选择抄送'});
					$(".select2-container").css("width","100%");
				});
				autoHeight();
			}});  
		}
		$(function(){
		    loadWeekType(0);
			loadWeeklyData();
		});
		Edit.uploadFile = function(callback){
			var fkId='';
			if(Edit.weeklyId){
				fkId=Edit.weeklyId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'weeklyCallback',formId:'projectWeeklyFileForm',fileId:'peojectWeeklyLocalfile',fileMaxSize:(1024*50),fkId:fkId,source:'projectWeekly'});
		}
		var weeklyCallback = function(data){
			if(isOtherFile==0){
				$("[name='weekly.WEEK_FILE_ID']").val(data.id);
				$("[name='weekly.WEEK_FILE']").val(data.name);
				$("#weeklyFileSpan").text(data.name);
			}else{
				$("#projectWeeklyFileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
			}
		}
		
		Edit.ajaxSubmitForm = function(){
			var yearWeek = $("[name='weekly.YEAR_WEEK']").val();
			var title = $("[name='weekly.TITLE']").val();
			if(yearWeek==''){
				layer.msg('请选择周数');
				return;
			}
			if(title==''){
				layer.msg('请输入标题');
				return;
			}
			
			if(form.validate("#weekEditForm")){
				 var  users = $("#users").val();
				 if(users==''){
					 layer.msg("请选择主送。",{icon:7});
					 return;
				 }
				 var weekType = $("[name='weekly.WEEK_TYPE']:checked").val();
				 if(weekType=='0'){
					 var text=$.trim(editor.txt.text()||'');
					 var text2=$.trim(editor2.txt.text()||'');
					 var fileDiv =$(".file-div").length;
					 if(text.length==0){
						 layer.msg("本周工作总结不能为空。",{icon:7});
						 return;
					 }
					 if(text2.length==0){
						 layer.msg("下周工作计划不能为空。",{icon:7});
						 return;
					 }
				     if(text.length < 50&&fileDiv==0){
				    	 layer.confirm("当前本周工作总结描述("+text.length+")字数,<br>内容太短(低于50字),是否继续提交?",{offset:'auto',icon:7},function(index){
				    		 layer.close(index);
							if(Edit.weeklyId){
								Edit.updateData(); 
							}else{
								Edit.insertData(); 
							}
				    		 
				    	 });
				     }else{
						if(Edit.weeklyId){
							Edit.updateData(); 
						}else{
							Edit.insertData(); 
						}
					  }
				 }else{
					 var id = $("[name='weekly.WEEK_FILE_ID']").val();
					 if(id==''){
						 layer.msg('请上传周报附件');
						 return;
					 }
					 if(Edit.weeklyId){
							Edit.updateData(); 
					}else{
						Edit.insertData(); 
					}
				 }
			     
			};
		}
		Edit.insertData = function() {
			$("#weeklyId").val($("#randomId").val());
			var ids = $("[name='mailtos']").val()||[];
			ids.push(getCurrentUserId());
			$("[name='mailtos']").val(ids);
			
			var data = form.getJSONObject("#weekEditForm");
			ajax.remoteCall("${ctxPath}/servlet/project/weekly?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						localStorage.setItem("weeklyReceiver",$("#users").val());
						localStorage.setItem("weeklyMailtos",ids.join());
						reloadProjectWeekly();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#weekEditForm");
			ajax.remoteCall("${ctxPath}/servlet/project/weekly?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						reloadProjectWeekly();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		};
		Edit.del=function(data){
			layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/project/weekly?action=delete",{'weekly.WEEKLY_ID':Edit.weeklyId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							reloadProjectWeekly();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}
		
		function loadWeekType(val){
			if(val=='0'||val==undefined){
				$('.textare-weekly').show();
				$('.weeklyFile').hide();
			}else{
				$('.textare-weekly').hide();
				$('.weeklyFile').show();
			}
		}
		
		function changeWeekly(obj){
			var val = obj.val();
			$("#weekyNo").val(val.split("_")[1])
			$("#year").val(val.split("_")[0])
			$("#title").val(obj.find('option:selected').data("title"));
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>