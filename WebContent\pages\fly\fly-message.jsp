<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>云趣乐享平台 - ${staffInfo.userName}</title>
</EasyTag:override>
<EasyTag:override name="content">
 <div class="layui-container fly-marginTop fly-user-main">
   <ul class="layui-nav layui-nav-tree layui-inline" lay-filter="user">
    <li class="layui-nav-item">
      <a href="/yq-work/fly/u">
        <i class="layui-icon">&#xe609;</i>我的主页
      </a>
    </li>
    <li class="layui-nav-item">
      <a href="/yq-work/fly/my">
        <i class="layui-icon">&#xe612;</i>用户中心
      </a>
    </li>
    <li class="layui-nav-item layui-this">
      <a href="/yq-work/fly/message">
        <i class="layui-icon">&#xe611;</i> 我的消息
      </a>
    </li>
  </ul>

  <div class="site-tree-mobile layui-hide">
    <i class="layui-icon">&#xe602;</i>
  </div>
  <div class="site-mobile-shade"></div>
  
  <div class="site-tree-mobile layui-hide">
    <i class="layui-icon">&#xe602;</i>
  </div>
  <div class="site-mobile-shade"></div>
  
  <div class="fly-panel fly-panel-user" pad20>
	  <div class="layui-tab layui-tab-brief" lay-filter="user" id="LAY_msg" style="margin-top: 15px;">
	    <button class="layui-btn layui-btn-danger layui-hide" id="LAY_delallmsg">清空全部消息</button>
	    <div  id="LAY_minemsg" style="margin-top: 10px;">
	    <c:if test="${empty commentList}">
	        	<div class="fly-none">您暂时没有最新消息</div>
	    </c:if>
        <ul class="mine-msg">
          <c:forEach items="${commentList}" var="item">
           <li data-id="${item.comment_id}">
            <blockquote class="layui-elem-quote">
              <a href="/yq-work/fly/u/${item.creator}" target="_blank"><cite>${item.create_name}</cite></a>回答了您的求解<a target="_blank" href="/yq-work/fly/detail/${item.remind_id}"><cite>${item.title}</cite></a>
            </blockquote>
            <p><span>${item.create_time}</span><a href="javascript:;" class="layui-btn layui-btn-small layui-btn-danger layui-hide fly-delete">删除</a></p>
           </li>
          </c:forEach>
        </ul>
      </div>
	  </div>
	</div>

</div>

</EasyTag:override>
 
<EasyTag:override name="script">
	<script type="text/javascript">
	 	layui.cache.page = 'user';
	 	layui.cache.user = {username: '${staffInfo.userName}' , uid: '${staffInfo.userId}', avatar: '${staffInfo.picUrl}', experience: '${staffInfo.points}'};
		layui.config({version: "3",base: '/yq-work/pages/fly/static/mods/'}).extend({fly: 'index'}).use('fly');
   </script>
</EasyTag:override>
<%@ include file="fly-layout.jsp" %>
