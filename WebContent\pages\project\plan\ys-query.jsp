<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
		.layui-table-cell {
			height: auto;
		}
		.select2-search__field{min-width: 120px!important;}
		.layui-progress{margin-top: 13px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off" onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="source" type="hidden" value="${param.source}">
			<input name="acceptFlag" type="hidden" value="">
			<div class="ibox">
				<div class="ibox-title clearfix">
				 	<div class="pull-left layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: -4px 2px 5px;">
	          		 	 <ul class="layui-tab-title"><li data-val="" lay-id="0" class="layui-this">全部</li><li lay-id="1" data-val="curr">本月验收</li><li lay-id="2" data-val="next">下月验收</li><li lay-id="2" data-val="prev">上月验收</li></ul>
          		 	 </div>
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm"  style="width: 280px">
							 <span class="input-group-addon">计划验收日期</span>	
							 <input style="width: 90px;display: inline-block" type="text" name="acceptBeginDate" data-laydate="{type:'date'}" class="form-control input-sm">
							 <input style="width: 90px;display: inline-block" type="text" name="acceptEndDate" data-laydate="{type:'date'}" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 180px;">
							 <span class="input-group-addon">项目名称</span>	
							 <input type="text" name="projectName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 180px;">
							 <span class="input-group-addon">项目编号</span>	
							 <input type="text" name="projectNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px;display: none;">
							 <span class="input-group-addon">上级项目</span>	
							 <input type="text" name="pProjectName" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目类型</span>	
							 <select name="projectType" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="10" data-class="label label-info">合同项目</option>
		                    		<option value="11" data-class="label label-info">提前执行</option>
		                    		<option value="20" data-class="label label-warning">自研项目</option>
		                    		<option value="30" data-class="label label-warning">维保项目</option>
	                    	</select>
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目阶段</span>	
							 <select name="projectStage" onchange="list.query()" multiple="multiple" class="form-control input-sm" style="min-width: 160px;">
								  <option value="">--</option>
								  <option value="提前执行">提前执行</option>
								  <option value="提前执行-废弃">提前执行-废弃</option>
	                    		  <option value="提前执行-已正式签订">提前执行-已正式签订</option>
								  <option value="待初验">待初验</option>
								  <option value="待一次性验收">待一次性验收</option>
								  <option value="待终验">待终验</option>
								  <option value="已终验，维保在保">已终验，维保在保</option>
								  <option value="已终验，维保到期">已终验，维保到期</option>
								  <option value="维保到期">维保到期</option>
								  <option value="维保在保">维保在保</option>
								  <option value="在建合作运营">在建合作运营</option>
								  <option value="合作运营结项">合作运营结项</option>
								  <option value="异常终止">异常终止</option>
	                    	</select>
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目状态</span>	
							 <select name="projectState" onchange="list.query()" multiple="multiple" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="11" data-class="text-info">进度正常</option>
		                    		<option value="12" data-class="text-warning">存在风险</option>
		                    		<option value="13" data-class="text-danger">进度失控</option>
		                    		<option value="14">进度提前</option>
			            			<option value="15">进度滞后</option>
		                    		<option value="20" data-class="text-danger">挂起中</option>
		                    		<option value="30" data-class="text-success">已完成</option>
	                    	</select>
					     </div>
					     
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">当前责任方</span>	
							 <select name="problemBy" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">无问题</option>
		                    		<option value="销售">销售</option>
		                    		<option value="开发">开发</option>
		                    		<option value="工程">工程</option>
		                    		<option value="客户">客户</option>
		                    		<option value="第三方">第三方</option>
	                    	</select>
					     </div>
					     <div class="input-group input-group-sm"  style="width: 140px">
							 <span class="input-group-addon">项目经理</span>	
							 <input type="text" name="projectPoName" class="form-control input-sm">
					     </div>
					     <div class="input-group input-group-sm"  style="width: 150px">
							 <span class="input-group-addon">开发负责人</span>	
							 <input type="text" name="po" class="form-control input-sm">
					     </div>
					     <c:if test="${param.source!='dept'}">
	          	     		 <div class="input-group input-group-sm" style="width: 220px">
								 <span class="input-group-addon">工程大区</span>	
								 <input type="text" name="projectDeptName" data-dept-name="工程" onclick="multiDept(this)" readonly="readonly" class="form-control input-sm select-icon">
						     </div>
					     </c:if>
          	     		 <div class="input-group input-group-sm" style="width: 170px">
							 <span class="input-group-addon">合同类型</span>	
							 <input type="text" name="contractType" readonly="readonly" onclick="singleDict(this,'Y004')" class="form-control input-sm select-icon">
					     </div>
					     <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="list.clear()">清空</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="progressTpl">
			{{if H>0}}
			<div class="layui-progress" lay-showPercent="true">
  				<div class="layui-progress-bar {{if H==K}} layui-bg-green {{else K/H <= 0.6}} layui-bg-red {{else K/H <= 0.8}} layui-bg-orange  {{else}} layui-bg-blue {{/if}}" lay-percent="{{:K}}/{{:H}}"></div>
			</div>
			{{else}}
				0/0
			{{/if}}
		</script>
		 <script type="text/html" id="btnBar">
			 <EasyTag:res resId="PROJECT_ADD">
				<div class="input-group input-group-sm">
					<button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 新增</button>
				</div>
			</EasyTag:res>
			<button type="button" class="btn btn-sm btn-default" onclick="list.showCondition(this)">查询条件隐藏</button>
 		</script>
 		<script type="text/x-jsrender" id="planTpl">
			{{if PLAN_ID}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.plan">查看</a>
				{{else}}
				--
			{{/if}}
			<input type="hidden" class="cy-date" value="{{:CY_DATE}}">
			<input type="hidden" class="zy-date" value="{{:ZY_DATE}}">
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var list={
			init:function(){
				var data = list.getData();
				$("#searchForm").initTable({
					mars:'ProjectDao.ysProjectList',
					height:'full-180',
					autoSort:false,
					toolbar:'#btnBar',
					limit:50,
					data:data,
					rowEvent:'rowEvent',
					limits:[50,100,200,300,500],
					cols: [[
					  {title:'序号',fixed:'left',type:'radio'},
					  {title:'序号',fixed:'left',type:'numbers'},
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						align:'left',
						minWidth:300,
						fixed:'left',
						style:'cursor:pointer;color:#4298fd;',
						event:'list.detail',
						templet:function(row){
							var level = row['PROJECT_LEVEL'];													
							var html = "";
							if(level>1){
								var json  = {
									'2':'<span class="label label-danger project-level">紧急</span> ',
									'3':'<span class="label label-warning project-level">重要</span> ',
									'4':'<span class="label label-info project-level">重要且紧急</span> ',
									'9':'<span class="label label-default project-level">外包</span> '
								};
								html = json[level];
							}
							var type = row['PROJECT_TYPE'];
							var contractId = row['CONTRACT_ID'];
							var no = row['PROJECT_NO'];
							no = no==0?'':no;
							if(contractId==''){
								html  = html + " <span class='label label-danger'>无合同</span> "+ row.PROJECT_NAME;
							}
							else if(type==10){
								html =  html + no +"&nbsp;"+ row.PROJECT_NAME;
							}else{
								var val = getText(type,'projectType');
								html = html + val + "&nbsp;" + no + row.PROJECT_NAME;
							}
							return html;
						}
					},{
					    field: 'PROJECT_STAGE',
						title: '项目阶段',
						width:110,
						align:'center'
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:80,
						hide:false,
						templet:function(row){
							return getText(row.PROJECT_STATE,'projectState');
						}
					},{
					    field: 'TASK_COUNT',
						title: '任务数',
						width:90,
						sort:true,
						align:'center'
					},{
					    field: 'BUG_COUNT',
						title: 'BUG数',
						width:90,
						event:'projectBugUrl',
						sort:true,
						align:'center'
					},{
					    field: 'PLAN_ID',
						title: '项目计划',
						align:'center',
						width:80,
						templet:function(row){
							return renderTpl('planTpl',row);
						}
					},{
					    field: 'SALES_BY_NAME',
						title: '销售',
						align:'center',
						width:80
					},{
					    field: 'PROJECT_PO_NAME',
						title: '项目经理',
						sort:true,
						align:'center',
						width:80
					},{
					    field: 'PO_NAME',
						title: '开发负责人',
						align:'center',
						width:100
					},{
					    field: 'PROJECT_DEPT_NAME',
						title: '工程大区',
						sort:true,
						align:'center',
						width:90
					},{
					    field: 'KH_DATE',
						title: '考核日期',
						align:'center',
						width:90
					},{
					    field: 'PLAN_CHANGE_COUNT',
						title: '计划变更次数',
						align:'center',
						width:95
					},{
					    field: 'CY_DATE',
						title: '计划初验',
						width:90,
						align:'center'
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						width:90,
						sort:true,
						align:'center',
						hide:true
					},{
					    field: 'CY_REAL_DATE',
						title: '实际初验日期',
						width:120,
						align:'center'
					},{
					    field: 'ZY_DATE',
						title: '计划终验日期',
						width:120,
						align:'center'
					},{
					    field: 'ZY_REAL_DATE',
						title: '实际终验日期',
						width:120,
						align:'center'
					},{
					    field: 'WB_END_DATE',
						title: '维保到期时间',
						width:120,
						align:'center'
					}
					]],done:function(){
						table.on('sort(list)', function(obj){
						  var data = list.getData();
						  var _data = $.extend({},{field:obj.field,order:obj.type},data);
						  $("#searchForm").queryData({jumpOne:true,initSort:obj,data:_data});
						});
						layui.use(['element'], function(){
							var element = layui.element;
							element.render();
						});
						//$('tbody .project-level').parents('tr').css('background-color','#f9b9b9');

						$('.cy-date,.zy-date').each(function(){
							var date = $(this).val();
							try{
								if(date){
									date = new Date(date);
									var now = new Date();
									var year = now.getFullYear();
									var month = now.getMonth();
									if(date.getFullYear() == year && date.getMonth() == month){
										if($(this).hasClass('cy-date')){
											$(this).parents('tr').find("[data-field='CY_DATE']").css('background-color','#f0ad4e');
										}else{
											$(this).parents('tr').find("[data-field='ZY_DATE']").css('background-color','#f0ad4e');
										}
									}
								}
							}catch(e){
								console.log(e);
							}							
						});

					}}
				);
			},
			query:function(){
				var data = list.getData();
				$("#searchForm").queryData({jumpOne:true,data:data});
			},
			getData:function(){
				var data = form.getJSONObject("#searchForm");
				var projectStage = data.projectStage;
				if(projectStage&&isArray(projectStage)){
					data['projectStage'] = projectStage.join(',');
				}
				var projectState = data.projectState;
				if(projectState&&isArray(projectState)){
					data['projectState'] = projectState.join(',');
				}
				return data;
			},
			clear:function(){
				$('#searchForm')[0].reset();
			    $("[name='contractType']").val('');
				$("[name='projectStage']").val(null).trigger('change');
				$("[name='projectType']").val(null).trigger('change');
				$("[name='projectState']").val(null).trigger('change');
				$("[name='problemBy']").val(null).trigger('change');
				$("[name='isProjectAward']").val(null).trigger('change');
				list.query();
			},
			detail:function(data){
				var projectId = data.PROJECT_ID;
				projectBoard(projectId);
				//popup.openTab({id:projectId,url:'${ctxPath}/project/'+projectId,title:'项目总览',data:{projectId:projectId}});
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'编辑项目',data:{projectId:data.PROJECT_ID}});
			},
			taskMgr:function(data){
				popup.openTab({url:'${ctxPath}/pages/task/task-query.jsp',id:'taskMgr',title:'项目任务管理',data:{projectId:data['PROJECT_ID']}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'新增项目'});
			},
			addTask:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务',data:{projectId:data.PROJECT_ID}});
			},
			plan:function(data){
				window.open('/yq-work/web/flow/'+data.PLAN_ID);
			},
			showCondition:function(btn){
				$(btn).text($(btn).text()=='查询条件隐藏'?'显示查询条件':'查询条件隐藏');				
				if($(btn).text()=='显示查询条件'){
					$(".ibox-title").hide();
					setTimeout(function() {
						$('.layui-table-main').css('height', 'calc(100vh - 20px)!important');
						layui.table.resize('list');
					}, 100);
				}else{
					$(".ibox-title").show();
					$('.layui-table-main').css('height','calc(100vh - 180px)!important');
					layui.table.resize('list');
				}
			}
		}
		
		
		function projectBugUrl(data){
			window.open('/yq-work/project/bug/'+data['PROJECT_ID']);
		}
		
		function reloadTaskList(){
			
		}
		function reloadProjectList(){
			list.query({jumpOne:true});
		}
		
		$(function(){
			layui.element.on('tab(stateFilter)', function(){
				$("[name='acceptFlag']").val(this.getAttribute('data-val'));
				reloadProjectList();
			});
			var isDevProject = '${param.isDevProject}';
			if(isDevProject=='1'){
				$("[name='contractType']").val('技术开发合同');
				layui.element.tabChange('stateFilter', '1');
			}
			$("#searchForm").render({success:function(){
				list.init();
			}});
			
			 requreLib.setplugs('select2',function(){
				 $("#searchForm select").select2({theme: "bootstrap",placeholder:'请选择'});
				 $(".select2-container").css("width","100%");
			 });
			
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>