package com.yunqu.work.dao;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.RemindModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="RemindDao")
public class RemindDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		RemindModel model=new RemindModel();
		model.setPrimaryValues(param.getString("remindId"));
		model.setColumns(getParam("remind"));
		if(StringUtils.notBlank(param.getString("remindId"))){
			LookLogService.getService().addLog(getUserPrincipal().getUserId(),param.getString("remindId"));
		}
		return queryForRecord(model);
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select remind_id,title from YQ_REMIND");
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from YQ_REMIND where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append(param.getString("type"),"and remind_type = ?");
		sql.append(" order by publish_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="mylist",type=Types.LIST)
	public JSONObject mylist(){
		EasySQL sql=getEasySQL("select * from YQ_REMIND where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append(param.getString("type"),"and remind_type = ?");
		sql.append(0,"and status = ?");
		sql.append(" order by publish_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
