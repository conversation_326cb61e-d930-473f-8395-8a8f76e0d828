<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.layui-tab-content{background-color: #fff;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.gray-bg{background-color: #e8edf7;}
		.items .form-control{padding: 3px 4px;font-size: 12px;}
		.items .layui-table td, .layui-table th {padding: 6px;font-size: 12px;}
		.items .select2-container--bootstrap .select2-selection{font-size: 12px;}
		.layui-icon{font-size: 12px;}
		.extTr td{border: none;}
		.ibox-content{min-height: calc(100vh - 70px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
				 		<div class="flow-title">${flow.flowName}</div>
				    	<table class="table table-vzebra flow-table">
					  		<tr>
					  			<td style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" data-mars="BxDao.bxTitle" name="apply.applyTitle"/>
					  			</td>
					  			<td style="width: 120px;">单号</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" readonly="readonly" data-mars="BxDao.zrBxNo" name="apply.applyNo"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}${staffInfo.staffNo}" readonly="readonly" class="form-control input-sm" name="business.APPLY_USER_NAME"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<select class="form-control" data-rules="required">
					  					<option value="84047763625598688667544">运营部</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>说明</td>
					  			<td colspan="3">
									<textarea style="height: 50px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		
					  		<tr>
					  			<td>费用总计</td>
					  			<td>
					  				<input type="number" data-rules="required" readonly="readonly" class="form-control input-sm" name="business.BX_MONEY"/>
					  			</td>
					  			<td>冲帐金额</td>
					  			<td>
					  				<input type="number" data-rules="required" placeholder="如无借款请填0" onchange="calcBxMoney();" class="form-control input-sm" value="0" name="business.REVERSE_MONEY"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>应付金额</td>
					  			<td>
					  				<input type="number" data-rules="required" readonly="readonly" class="form-control input-sm" name="business.PAY_MONEY"/>
					  			</td>
					  			<td>大写</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="business.PAY_MONEY_DX"/>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				  <div class="layui-col-md12 items mt-10">
						   <div class="layui-table-tool">
								<div class="pull-left">
									<i class="layui-icon layui-icon-time"></i> 费用项明细
								</div>
								<div class="pull-right">
									<button class="btn btn-sm btn-default" onclick="addTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增行</button>
									<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
									<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
									<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
								</div>
						  </div>
						 <div class="table-responsive" style="min-height: 100px;overflow-x:auto;">
							 <table class="layui-table text-nowrap" style="margin-top: -1px;">
							   <thead>
							    <tr>
							      <th></th>
							      <th>费用日期</th>
							      <th>费用类型(内部)</th>
							      <th>发票类型</th>
							      <th>费用说明(内部)</th>
							      <th>金额</th>
							      <th>部门</th>
							      <th>项目</th>
							      <th>产品线</th>
							      <th>税率(%)</th>
							      <th>税金</th>
							      <th>金额</th>
							    </tr> 
							  </thead>
							  <tbody data-mars="BxDao.ztBxItems" data-template="bxItems"></tbody>
							  <tbody>
							  	<c:forEach var="item" begin="1000" end="1050">
								    <tr id="item_${item}" <c:if test="${item>1002||handle=='edit'}">data-hide-tr="1" style="display:none;"</c:if>>
								      <td>
								      	<input type="hidden" name="orderIndex_${item}"/>
								      	<input type="hidden" name="itemId_${item}" value="${item}"/>
								      	<input type="hidden" value="${item}" name="itemIndex"/>
								        <label class="checkbox checkbox-inline checkbox-success">
									      	 <input type="checkbox" name="ids" value="${item}"><span></span>
								        </label>
								      </td>
								      <td>
								      	    <input type="text" data-rules="required" data-laydate="{type:'date'}" style="width: 90px;" name="bxDate_${item}" class="form-control input-sm"/>
								      </td>
								      <td>
								     	   <select data-copy="${item}" data-name="feeType" data-rules="required" class="form-control input-sm feeType" name="feeType_${item}" onchange="getInvoiceType(this.value,'${item}')">
								 			    <option value="差旅费-机票费">差旅费-机票费</option>
												<option value="差旅费-市内交通费">差旅费-市内交通费</option>
												<option value="差旅费-车船费">差旅费-车船费</option>
												<option value="差旅费-住宿费">差旅费-住宿费</option>
												<option value="差旅费-出差补助">差旅费-出差补助</option>
												<option value="项目费用">项目费用</option>
												<option value="业务招待费">业务招待费</option>
												<option value="交通费">交通费</option>
												<option value="手机费">手机费</option>
												<option value="办公费">办公费</option>
												<option value="职工福利费">职工福利费</option>
								 		   </select>
								      </td>
								      <td>
								     	   <select data-copy="${item}" data-name="invoiceType"  data-rules="required" style="width: 90px;" class="form-control input-sm invoiceType" name="invoiceType_${item}">
								     	   		<option value="">--</option>
								 		   </select>
								      </td>
								      <td>
								      		<textarea style="height: 40px;resize: none;" data-rules="required" class="form-control input-sm" name="feeDesc_${item}"></textarea>
								      </td>
								      <td>
								      		<input type="number" style="width: 80px;" class="form-control input-sm" name="amount_${item}" onchange="calcRowRate('${item}')"/>
								      </td>
								      <td>
								           <input type="hidden" value="运营部"  name="deptName_${item}"/>
								           <select data-rules="required" style="width: 110px;" class="form-control input-sm" name="deptId_${item}">
								           		<option value="84047763625598688667544" selected="selected">运营部</option>
								           </select>
								      </td>
								      <td>
								        	<input type="hidden" name="contractNo_${item}" value="2020130100100"/>
								        	<input type="hidden" name="contractId_${item}" value="B15A1C925A84DC1C48258718002096FA"/>
								    		<select data-copy="${item}" data-name="contractName" data-rules="required" id="contractName_${item}" name="contractName_${item}" style="width: 130px;" data-id="${item}" class="form-control input-sm" onchange="selectProject(this)">
								    			<option value="">--</option>
								    			<option data-contract-id="B15A1C925A84DC1C48258718002096FA" data-contract-no="2020130100100" value="2020年广东电信天翼云呼云应用业务合作协议">2020年广东电信天翼云呼云应用业务合作协议</option>
								    			<option data-contract-id="9999999999999" data-contract-no="9999999999999" value="其他">其他</option>
								    		</select>
								      </td>
								      <td>
								      		<select data-copy="${item}" data-name="productLine" style="width: 110px;" class="form-control input-sm" name="productLine_${item}">
								        		<option value="融合通信(UC)">融合通信(UC)</option>
								        		<option value="缺省">缺省</option>
								        	</select>
								      </td>
								      <td>
								        	<select style="width: 70px;" data-rules="required" class="form-control input-sm" name="taxRate_${item}" onchange="calcRowRate('${item}')">
								        		<option value="0.00">0</option>
								        		<option value="0.01">1</option>
								        		<option value="0.03">3</option>
								        		<option value="0.06">6</option>
								        	</select>
								      </td>
								      <td>
								        	<input type="number" style="width: 70px;" value="0.00" class="form-control input-sm" name="taxes_${item}"/>
								      </td>
								      <td>
								        	<input type="text" style="width: 70px;" class="form-control input-sm" readonly="readonly" name="rAmount_${item}"/>
								      </td>
								    </tr>
							  	</c:forEach>
							  </tbody>	
							</table>
						</div>
			  </div>
		  </div>
   <script id="bxItems" type="text/x-jsrender">
  	 {{for data}}
  		<tr id="item_{{:ITEM_ID}}">
  			<td>
	      		<input type="hidden" name="orderIndex_{{:ITEM_ID}}"/>
	      		<input type="hidden" name="itemId_{{:ITEM_ID}}" value="{{:ITEM_ID}}"/>
	      		<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
	        	<label class="checkbox checkbox-inline checkbox-success">
		      	 <input type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
	       	 </label>
	      </td>
	      <td>
	      	    <input type="text" data-laydate="{type:'date'}" data-rules="required" style="width: 120px;" value="{{:BX_DATE}}" name="bxDate_{{:ITEM_ID}}" class="form-control input-sm"/>
	      </td>
	      <td>
	     	   <select data-rules="required" class="form-control input-sm feeType" data-value="{{:FEE_TYPE}}" name="feeType_{{:ITEM_ID}}" onchange="getInvoiceType(this.value,'{{:ITEM_ID}}')">
	 			    <option value="差旅费-机票费">差旅费-机票费</option>
					<option value="差旅费-市内交通费">差旅费-市内交通费</option>
					<option value="差旅费-车船费">差旅费-车船费</option>
					<option value="差旅费-住宿费">差旅费-住宿费</option>
					<option value="差旅费-出差补助">差旅费-出差补助</option>
					<option value="项目费用">项目费用</option>
					<option value="业务招待费">业务招待费</option>
					<option value="交通费">交通费</option>
					<option value="手机费">手机费</option>
					<option value="办公费">办公费</option>
					<option value="职工福利费">职工福利费</option>
	 		   </select>
	      </td>
	      <td>
	     	   <select data-rules="required" class="form-control input-sm invoiceType" name="invoiceType_{{:ITEM_ID}}">
	     	   		<option value="{{:INVOICE_TYPE}}">{{:INVOICE_TYPE}}</option>
	 		   </select>
	      </td>
	      <td>
	      		<textarea style="height: 40px;resize: none;" data-rules="required" class="form-control input-sm" name="feeDesc_{{:ITEM_ID}}">{{:FEE_DESC}}</textarea>
	      </td>
	      <td>
	      		<input type="number" style="width: 80px;" class="form-control input-sm" value="{{:AMOUNT}}" name="amount_{{:ITEM_ID}}" onchange="calcRowRate('{{:ITEM_ID}}')"/>
	      </td>
	      <td>
			   <input type="hidden" value="运营部" name="deptName_{{:ITEM_ID}}"/>
	           <select data-rules="required" style="width: 110px;" class="form-control input-sm" name="deptId_{{:ITEM_ID}}">
	           		<option value="84047763625598688667544" selected="selected">运营部</option>
	           </select>
	      </td>
	      <td>
				<input type="hidden" name="contractNo_{{:ITEM_ID}}" value="{{:CONTRACT_NO}}"/>
	        	<input type="hidden" name="contractId_{{:ITEM_ID}}" value="{{:CONTRACT_ID}}"/>
	    		<select data-rules="required" id="contractName_{{:ITEM_ID}}" name="contractName_{{:ITEM_ID}}" style="width: 130px;" data-id="{{:ITEM_ID}}" data-value="{{:CONTRACT_NAME}}" class="form-control input-sm contractName" onchange="selectProject(this)">
	    			<option value="">--</option>
	    			<option data-contract-id="B15A1C925A84DC1C48258718002096FA" data-contract-no="2020130100100" value="2020年广东电信天翼云呼云应用业务合作协议">2020年广东电信天翼云呼云应用业务合作协议</option>
	    			<option data-contract-id="9999999999999" data-contract-no="9999999999999" value="其他">其他</option>
	    		</select>
	      </td>
	      <td>
	      		<select style="width: 100px;" class="form-control input-sm productLine" data-value="{{:PRODUCT_LINE}}" name="productLine_{{:ITEM_ID}}">
	        		<option value="融合通信(UC)">融合通信(UC)</option>
	        		<option value="缺省">缺省</option>
	        	</select>
	      </td>
	      <td>
	        	<select style="width: 70px;" data-rules="required" class="form-control input-sm taxRate" data-value="{{:TAX_RATE}}" name="taxRate_{{:ITEM_ID}}" onchange="calcRowRate('{{:ITEM_ID}}')">
	        		<option value="0.00">0</option>
	        		<option value="0.01">1</option>
	        		<option value="0.03">3</option>
	        		<option value="0.06">6</option>
	        	</select>
	      </td>
	      <td>
	        	<input type="number" style="width: 70px;" value="{{:TAXES}}" onchange="calcRAmount('{{:ITEM_ID}}')" class="form-control input-sm" name="taxes_{{:ITEM_ID}}"/>
	      </td>
	      <td>
	        	<input type="text" style="width: 70px;" class="form-control input-sm" readonly="readonly" value="{{:R_AMOUNT}}" name="rAmount_{{:ITEM_ID}}"/>
	      </td>
  		</tr>
    {{/for}}
   </script>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">

		var Flow = {};
		
		$(function(){
			if(top.$('.admin-nav-mini').length==0){
				top.$("[ew-event='flexible']").click();
			}
			
			FlowCore.initPage({success:function(result){
				$(".feeType,.taxRate,.contractName,.productLine").each(function(){
					$(this).val($(this).data('value'));
				});
				
				$("input[name^='amount_']").on('change',function(){
					calcBxMoney();
				});
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			$("[data-hide-tr]").remove();
			var index  = 1;
			$("[name^='orderIndex_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({reqUrl:'${ctxPath}/servlet/zr/flow/bx?action=submitApply'});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({reqUrl:'${ctxPath}/servlet/zr/flow/bx?action=updateApply'});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		function selectProject(el){
			var obj =  $(el);
			var option = obj.find("option:selected");
			obj.prev().val(option.data('contractId'));
			obj.prev().prev().val(option.data('contractNo'));
			
		}
		
		function addTr(){
			var obj  = $("[data-hide-tr]").first();
			if(obj.length==0){
				layer.msg('不能再新增啦',{icon:7,shade: 0.1});
				return;
			}
			obj.removeAttr("data-hide-tr");
			obj.show();
			
			obj.find("[data-copy]").each(function(){
				var t = $(this);
				var name = t.data('name');
				t.val(obj.prev().find("[data-name='"+name+"']").val());
				if(name=='feeType'){
					getInvoiceType(t.val(),t.data('copy'));
				}
				if(name=='contractName'){
					selectProject(t);
				}
			});
		}
		
		function delTr(){
			layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
				layer.close(index);
				$("input[name='ids']:checked").each(function(){
					var id = this.value;
					if(id.length>20){
						ajax.remoteCall("${ctxPath}/servlet/zr/flow/bx?action=delItem",{itemId:id},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg);
								$('#item_'+id).remove();
								calcBxMoney();
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});					
					}else{
						$('#item_'+id).remove();
						calcBxMoney();
					}
					
				});
			});
		}
		
		function upTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var prevTR  = $('#item_'+id).prev();
				if (prevTR.length > 0) {
					prevTR.insertAfter($('#item_'+id));
				}
			}
		}
		function downTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var nextTR  = $('#item_'+id).next();
				if (nextTR.length > 0) {
					nextTR.insertBefore($('#item_'+id));
				}
			}
		}
		
		function calcBxMoney(){
			var sum  = 0 ;
			$("[name^='amount_']").each(function(){
				var t = $(this);
				sum = numAdd(sum,t.val());	
			  }
		     );
			$("[name='business.BX_MONEY']").val(sum);
			 var reverseMoney = $("[name='business.REVERSE_MONEY']").val();
			$("[name='business.PAY_MONEY']").val(sum - reverseMoney);
			
			var dxVal = digitUppercase(sum - reverseMoney);
			$("[name='business.PAY_MONEY_DX']").val(dxVal);
		}
		
		function calcRowRate(id){
			var val = $("[name='amount_"+id+"']").val();
			var taxRate = $("[name='taxRate_"+id+"']").val();
			var v2 = numDiv(taxRate,100)+0;
			var v3 = val/(1+parseFloat(v2));
			
			$("[name='taxes_"+id+"']").val((val-v3).toFixed(2));
			$("[name='rAmount_"+id+"']").val(v3.toFixed(2));
		}
		
		function calcRAmount(id){
			var v1 = $("[name='amount_"+id+"']").val();
			var v2 = $("[name='taxes_"+id+"']").val();
			$("[name='rAmount_"+id+"']").val(v1-v2);
		}
		
		function getInvoiceType(feeType,id){
			if(feeType=='项目费用'){
				$('#item_'+id).find('.invoiceType').html("<option value='业务招待费'>业务招待费</option>");
			}else{
				$('#item_'+id).find('.invoiceType').html("<option value='"+feeType+"'>"+feeType+"</option>");
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>