package com.yunqu.work.dao.devops;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.VersionModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="VersionDao")
public class VersionDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		VersionModel model=new VersionModel();
		model.setColumns(getParam("version"));
		if(StringUtils.notBlank(model.getVersionId())){
			try {
				if(StringUtils.notBlank(model.getVersionId())){
					Map<String, String> jsonObject = this.getQuery().findById(model,"CREATOR");
					String creator = jsonObject.get("CREATOR");
					if(!getUserId().equals(creator)){
						LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getVersionId(),"version",request);
						this.getQuery().executeUpdate("update yq_ops_version t1 set t1.view_count = (select count(1) from yq_look_log t2 where t2.fk_id = t1.version_id and t2.fk_id = ?) where t1.version_id = ?",model.getVersionId(),model.getVersionId());
					}
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
			
		}
		JSONObject result = queryForRecord(model);
		try {
			List<JSONObject> list = this.getQuery().queryForList("select t2.module_name,t1.* from yq_project_module_version t1,yq_project_module t2,yq_ops_version t3 where t1.version_id = t3.version_id and t2.module_id = t1.module_id and t1.version_id = ?",new Object[] {model.getPrimaryValue()},new JSONMapperImpl());
			result.put("modules",list);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		return result;
	}
	
	
	@WebControl(name="securityRecord",type=Types.RECORD)
	public JSONObject securityRecord(){
		EasyRecord model=new EasyRecord("YQ_OPS_SECURITY","SECURITY_ID");
		model.setColumns(getParam("security"));
		if(model.getPrimaryValue()!=null){
			try {
				if(StringUtils.notBlank(model.getPrimaryValue().toString())){
					Map<String, String> jsonObject = this.getQuery().findById(model,"CREATOR");
					String creator = jsonObject.get("CREATOR");
					if(!getUserId().equals(creator)){
						LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getPrimaryValue().toString(),"security",request);
						this.getQuery().executeUpdate("update yq_ops_security t1 set t1.view_count = (select count(1) from yq_look_log t2 where t2.fk_id = t1.version_id and t2.fk_id = ?) where t1.security_id = ?",model.getPrimaryValue().toString(),model.getPrimaryValue().toString());
					}
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
			
		}
		JSONObject result = queryForRecord(model);
		try {
			List<JSONObject> list = this.getQuery().queryForList("select t2.security_title,t1.* from yq_ops_security_do t1,yq_ops_security t2 where  t2.security_id = t1.security_id and t1.security_id = ?",new Object[] {model.getPrimaryValue()},new JSONMapperImpl());
			result.put("modules",list);
			String userNames = this.getQuery().queryForString("select group_concat(t1.user_name) from yq_ops_security_do t1 where t1.security_id = ?",new Object[] {model.getPrimaryValue()});
			result.getJSONObject("data").put("RECEIVER_NAME",userNames);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		return result;
	}
	
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select version_id,version_name from yq_ops_version");
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select t1.version_type,t1.project_name,t1.create_name,t1.update_time,t1.version_state,t1.receiver_name,t1.receiver_id,t1.version_id,t1.version_name,t1.version_title,t1.project_id,t1.creator,t1.create_time,t1.view_count,t1.file_desc,t1.recipient_id,t1.recipient_name,t1.module_count from yq_ops_version t1 where 1=1");
		sql.append(param.getString("versionState"),"and t1.version_state = ?");
		sql.appendLike(param.getString("versionName"),"and t1.version_name like ?");
		sql.appendLike(param.getString("versionTitle"),"and t1.version_title like ?");
		sql.appendLike(param.getString("projectName"),"and t1.project_name like ?");
		sql.append(param.getString("groupId"),"and t1.GROUP_ID = ?");
		sql.append(param.getString("appId"),"and t1.app_id = ?");
		String projectId = param.getString("projectId");
		sql.append(projectId,"and t1.project_id = ?");
		sql.append(param.getString("serviceId"),"and t1.service_id = ?");
		
		if(isSuperUser()||hasRole("VERSION_MANAGER")||StringUtils.isNotBlank(projectId)) {
			//
		}else {
			sql.append("and ((");
			sql.append(getUserId(),"t1.creator = ?");
			sql.append(")");
			sql.append("or (");
			sql.append(getUserId(),"FIND_IN_SET(?,t1.recipient_id)");
			sql.append("or (");
			sql.append(getUserId(),"t1.receiver_id = ?)");
			
			if(hasRole("DEPT_MGR")||hasRole("DEV_MGR")) {
				sql.append("or (");
				sql.append(getDeptId(),"t1.dept_id = ?)");
			}
			sql.append("))");
		}
		
		String state = param.getString("state");
		if("0".equals(state)) {
			sql.append(getUserId(),"and t1.creator = ?");
		}else if("1".equals(state)) {
			sql.append(getUserId(),"and t1.creator <> ?");
		}
		
		sql.append("order by t1.CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="securityList",type=Types.LIST)
	public JSONObject securityList(){
		EasySQL sql=getEasySQL("select t1.security_level,t1.project_name,t1.create_name,t1.update_time,t1.security_state,t1.receiver_name,t1.receiver_id,t1.security_id,t1.security_type,t1.security_title,t1.project_id,t1.creator,t1.create_time,t1.view_count,t1.file_desc,t1.recipient_id,t1.recipient_name,t1.do_people_count from yq_ops_security t1 where 1=1");
		sql.append(param.getString("securityState"),"and t1.security_state = ?");
		sql.appendLike(param.getString("versionName"),"and t1.version_name like ?");
		sql.appendLike(param.getString("securityTitle"),"and t1.security_title like ?");
		sql.appendLike(param.getString("projectName"),"and t1.project_name like ?");
		sql.append(param.getString("groupId"),"and t1.GROUP_ID = ?");
		sql.append(param.getString("appId"),"and t1.app_id = ?");
		String projectId = param.getString("projectId");
		sql.append(projectId,"and t1.project_id = ?");
		sql.append(param.getString("serviceId"),"and t1.service_id = ?");
		
		if(isSuperUser()||hasRole("VERSION_MANAGER")||StringUtils.isNotBlank(projectId)) {
			//
		}else {
			sql.append("and ((");
			sql.append(getUserId(),"t1.creator = ?");
			sql.append(")");
			sql.append("or (");
			sql.append(getUserId(),"FIND_IN_SET(?,t1.recipient_id)");
			sql.append("or (");
			sql.append(getUserId(),"t1.receiver_id = ?)");
			
			if(hasRole("DEPT_MGR")||hasRole("DEV_MGR")) {
				sql.append("or (");
				sql.append(getDeptId(),"t1.dept_id = ?)");
			}
			sql.append("))");
		}
		
		String state = param.getString("state");
		if("0".equals(state)) {
			sql.append(getUserId(),"and t1.creator = ?");
		}else if("1".equals(state)) {
			sql.append(getUserId(),"and t1.creator <> ?");
		}
		
		sql.append("order by t1.CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
