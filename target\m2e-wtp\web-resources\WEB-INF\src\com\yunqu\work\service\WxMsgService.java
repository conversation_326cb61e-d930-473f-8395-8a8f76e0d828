package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.HttpKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.StaffModel;

public class WxMsgService extends AppBaseService{
	private static class Holder{
		private static WxMsgService service=new WxMsgService();
	}
	
	@Override
	public Logger getLogger(){
		return LogEngine.getLogger("weixin","weixin");
	}
	
	public static WxMsgService getService(){
		return Holder.service;
	}
	public WxMsgService(){
		
	}
	public void sendWeeklyMsg(MessageModel model){
	
	}
	private void sendTemplateMsg(Map<String,String> queryParas){
		new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					String url = queryParas.get("url");
					if(StringUtils.notBlank(url)) {
						if(url.indexOf("/api/goUrl")>-1) {
							url  = url.replaceAll("=", "_A");
							url = url.replaceAll("&", "_B");
							url = url.replace("goUrl?url_A", "goUrl?url=");
						}
						queryParas.put("url", url);
					}
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
					
					getLogger().info("queryParas_url:"+url);
					getLogger().info("queryParas:"+JSONObject.toJSONString(queryParas));
					
					if(ServerContext.isLinux()&&Constants.isProd()){
						String msgSwitch = AppContext.getContext(Constants.APP_NAME).getProperty("msgSwitch","0");
						if("1".equals(msgSwitch)) {
							String result=HttpKit.post("http://172.16.68.152/yq-work/api/sendTemplateMsg", queryParas, null);
							getLogger().info("result >"+result);
						}
					}else {
						getLogger().info("本地环境不发送微信推送");
					}
					
				} catch (Exception e) {
					Thread.currentThread().interrupt();
					getLogger().error(e.getMessage(),e);
				}
			}
		}).start();
		
		
	}
	
	public void sendTextMsg(String openId,String msg){
		if(ServerContext.isLinux()){
			Map<String,String> queryParas = new HashMap<String,String>();
			queryParas.put("openId", openId);
			queryParas.put("msg", msg);
			String result=HttpKit.post("http://172.16.68.152/yq-work/api/sendTextMsg", queryParas, null);
			getLogger().info("result >"+result);
		}
	}
	
	/**
	 * 流程待办提醒
	{{first.DATA}}
	流程名称：{{keyword1.DATA}}
	申请人：{{keyword2.DATA}}
	申请时间：{{keyword3.DATA}}
	流程摘要：{{keyword4.DATA}}
	{{remark.DATA}}
	 */
	public void sendFlowTodoCheck(MessageModel model){
		String ids = model.getReceiver();
		if(StringUtils.notBlank(ids)) {
			String[] userIds = ids.split(",");
			for(String id:userIds) {
				String openId = getOpenId(id);
				getLogger().info(openId+"发送任务微信推送.....");
				if(StringUtils.isBlank(openId)){
					getLogger().info("openId is empty.....");
					return;
				}
				Map<String,String> queryParas=new HashMap<String,String>();
				queryParas.put("openId",openId);
				if(StringUtils.notBlank(model.getUrl())) {
					queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url="+model.getUrl());
				}else if(StringUtils.isBlank(model.getFkId())) {
					queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
				}else {
					queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/web/flow/"+model.getFkId());
				}
				queryParas.put("templateId","gv5U1Jb1bvAl0Dcwbv8xoxuVU-1NQ6xENcfrUdLctGY");
				queryParas.put("first",model.getTitle());
				queryParas.put("keyword1",StringUtils.defaultString(model.getData1(),"--"));
				queryParas.put("keyword2",StringUtils.defaultString(model.getData2(),"--"));
				queryParas.put("keyword3",StringUtils.defaultString(model.getData3(),"--"));
				queryParas.put("keyword4",StringUtils.defaultString(model.getData4(),"--"));
				queryParas.put("remark",StringUtils.defaultString(model.getDesc(),"--"));
				sendTemplateMsg(queryParas);
			}
		}
	}
	
	public void sendFlowFeedback(MessageModel model){
		String ids = model.getReceiver();
		if(StringUtils.notBlank(ids)) {
			String[] userIds = ids.split(",");
			for(String id:userIds) {
				String openId = getOpenId(id);
				getLogger().info(openId+"发送任务微信推送.....");
				if(StringUtils.isBlank(openId)){
					getLogger().info("openId is empty.....");
					return;
				}
				Map<String,String> queryParas=new HashMap<String,String>();
				queryParas.put("openId",openId);
				queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/web/flow/"+model.getFkId());
				queryParas.put("templateId","2XkCRaBngDbN-lyO3kyrjnTZHMjKxihY0qmTXwtTQYo");
				queryParas.put("first",model.getTitle());
				queryParas.put("keyword1",StringUtils.defaultString(model.getData1(),"--"));
				queryParas.put("keyword2",StringUtils.defaultString(model.getData2(),"--"));
				queryParas.put("keyword3",StringUtils.defaultString(model.getData3(),"--"));
				queryParas.put("remark",StringUtils.defaultString(model.getDesc(),"--"));
				sendTemplateMsg(queryParas);
			}
		}
	}
	
	/**
	 * 流程审批通知
	 {{first.DATA}}
	    审批事项：{{keyword1.DATA}}
	   审批结果：{{keyword2.DATA}}
	    处理时间：{{keyword3.DATA}}
	  {{remark.DATA}}
	 */
	public void sendFlowCheckOk(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/web/flow/"+model.getFkId());
//		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","y2JCaESaOqnsS56e1MXrhVKL2i-3CDYkcMu2x2bjNzs");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",StringUtils.defaultString(model.getData1(),"--"));
		queryParas.put("keyword2",StringUtils.defaultString(model.getData2(),"--"));
		queryParas.put("keyword3",StringUtils.defaultString(model.getData3(),"--"));
		queryParas.put("remark",StringUtils.defaultString(model.getDesc(),"--"));
		sendTemplateMsg(queryParas);
	}
	
	/**
	 * {{first.DATA}}
		姓名：{{keyword1.DATA}}
		时间：{{keyword2.DATA}}
		状态：{{keyword3.DATA}}
		{{remark.DATA}}
	 * @param model
	 */
	public void sendKqDkNotice(MessageModel model){
		String openId = getOpenId(model.getReceiver());
		getLogger().info(openId+"发送考勤打卡微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		if(StringUtils.notBlank(model.getUrl())) {
			queryParas.put("url",model.getUrl());
		}else {
			queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/kq");
		}
		queryParas.put("templateId","2PkLAU-R9lWVz2guwYzXGrrr0QflYajjalchJ3CGdGc");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",StringUtils.defaultString(model.getData1(),"--"));
		queryParas.put("keyword2",StringUtils.defaultString(model.getData2(),"--"));
		queryParas.put("keyword3",StringUtils.defaultString(model.getData3(),"--"));
		queryParas.put("remark",StringUtils.defaultString(model.getDesc(),"--"));
		sendTemplateMsg(queryParas);
	}
	
	/**
	 * 发起验收提醒
	 * @param model
	 */
	public void sendCheckTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/task/"+model.getFkId());
		queryParas.put("templateId","81uHwyil81Nkx2cTZRL8x57ZJbocYgi1O4va-_JGIPk");
		queryParas.put("first","有一项任务需要您验收");
		queryParas.put("keyword1",model.getFkId());
		queryParas.put("keyword2",model.getTitle());
		queryParas.put("keyword3",getUserName(model.getReceiver()));
		queryParas.put("keyword4",model.getData().get("DEADLINE_AT").toString());
		queryParas.put("remark","请尽快跟进验收该项任务。");
		sendTemplateMsg(queryParas);
	}
	public void sendFinishTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/task/"+model.getFkId());
		queryParas.put("templateId","U-VveUQheJZ3jn3DCXM4sfjrIqVqFf30LboSmMWtWgE");
		queryParas.put("first","提交的任务已经完成,请尽快去验收任务。");
		queryParas.put("keyword1",model.getTitle());
		queryParas.put("keyword2","已完成");
		queryParas.put("keyword3",EasyDate.getCurrentDateString());
		queryParas.put("remark","登录系统获取详情。");
		sendTemplateMsg(queryParas);
		
	}
	/**
	 * 反馈处理进度通知
	 * {{first.DATA}}
		留言时间：{{keyword1.DATA}}
		留言内容：{{keyword2.DATA}}
		处理进展：{{keyword3.DATA}}
		回复内容：{{keyword4.DATA}}
		{{remark.DATA}}
	 * @param model
	 */
	public void sendCommentTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		
		if(StringUtils.notBlank(model.getUrl())) {
			queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url="+model.getUrl());
		}else{
			queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		}
		queryParas.put("templateId","LNajqTxju_pSHohJYI45byLAintXjUO5DDmgQbJZJoE");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",EasyDate.getCurrentDateString());
		queryParas.put("keyword2",delHTMLTag(model.getDesc()));
		queryParas.put("keyword3",StringUtils.isBlank(model.getData3())?"--":model.getData3());
		queryParas.put("keyword4",StringUtils.isBlank(model.getData4())?"--":model.getData4());
		queryParas.put("remark","点击查看详情");
		sendTemplateMsg(queryParas);
		
	}
	public void sendReplyMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送.....");
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","LNajqTxju_pSHohJYI45byLAintXjUO5DDmgQbJZJoE");
		queryParas.put("first",model.getTitle()+"\n");
		queryParas.put("keyword1",model.getData1());
		queryParas.put("keyword2",model.getData2());
		queryParas.put("keyword3",model.getData3());
		queryParas.put("keyword4",model.getData4());
		queryParas.put("remark",model.getDesc());
		sendTemplateMsg(queryParas);
		
	}
	/**
	 * 到时未完成提醒
	 * @param model
	 */
	public void sendUnFinshTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/task/"+model.getFkId());
		queryParas.put("templateId","vUkl3AJ6kZGZ6WCjEepFjgHw1Gn0IYK5wa0V2YgXzi8");
		queryParas.put("first",model.getTitle()+"\n");
		queryParas.put("keyword1",model.getFkId());
		queryParas.put("keyword2",model.getData().get("PLAN_STARTED_AT").toString());
		queryParas.put("keyword3",model.getData().get("TASK_LEVEL").toString());
		queryParas.put("keyword4","--");
		queryParas.put("remark","已经超过任务截止处理时间,请尽快去处理。");
		sendTemplateMsg(queryParas);
		
	}
	public void sendNewTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务微信推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/task/"+model.getFkId());
		queryParas.put("templateId","FIJ_BXh0eOMYDkPwJAYaoI7iyHf2E4BKDFH-rsvAUkI");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",model.getFkId());
		queryParas.put("keyword2",getUserName(model.getSender()));
		queryParas.put("keyword3",model.getData().get("PLAN_STARTED_AT").toString());
		queryParas.put("remark",delHTMLTag(model.getDesc()));
		sendTemplateMsg(queryParas);
		
	}
	public void sendNewWeeklyMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送周报微信推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		String weeklyId = model.getData().getString("WEEKLY_ID");
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/pages/weekly/weekly-detail.jsp?isDiv=0&weeklyId="+weeklyId);
		queryParas.put("templateId","YFUbnl2mCoolZAP3UMnq8GxDA0PifuRxdvh5W1qStBc");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",StaffService.getService().getUserDeptName(model.getSender()));
		queryParas.put("keyword2",getUserName(model.getSender()));
		queryParas.put("keyword3",EasyDate.getCurrentDateString());
		queryParas.put("remark",delHTMLTag(model.getDesc()));
		sendTemplateMsg(queryParas);
		
	}
	public void sendWeeklyNoticeMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送周报填写推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/pages/weekly/weekly-my.jsp?isDiv=0");
		queryParas.put("templateId","wBRZ1_BsUhSutcNe1f6H_EwSY6LZJdyLIxSoEBjBJcI");
		queryParas.put("first","您的周报待填写");
		queryParas.put("keyword1",model.getTitle());
		queryParas.put("keyword2",getUserName(model.getSender())+"\n");
		queryParas.put("keyword3",EasyDate.getCurrentDateString("yyyy-MM-dd"));
		queryParas.put("remark","请您及时处理！");
		sendTemplateMsg(queryParas);
		
	}
	
	public void sendWhNoticeMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送月报填写推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/pages/project/workhour/workhour-my.jsp");
		queryParas.put("templateId","wBRZ1_BsUhSutcNe1f6H_EwSY6LZJdyLIxSoEBjBJcI");
		queryParas.put("first",model.getData1()+"_月度项目工时上报待填写");
		queryParas.put("keyword1",model.getTitle());
		queryParas.put("keyword2",getUserName(model.getSender())+"\n");
		queryParas.put("keyword3",model.getData1());
		queryParas.put("remark","根据公司项目管理要求\n请务必及时上报月度项目工时！");
		sendTemplateMsg(queryParas);
		
	}
	
	public void sendRemindNoticeMsg(MessageModel model){
		String openId = model.getOpenId();
		getLogger().info(openId+"发送公告资讯推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		String url = model.getUrl();
		if(StringUtils.isBlank(url)) {
			queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/article/"+model.getFkId());
		}else {
			queryParas.put("url",url);
		}
		queryParas.put("templateId","RUbLzhv52LRIH23RnDb9DyEh6OmwFny6IVXm85xrAWM");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",model.getData1());
		queryParas.put("keyword2",model.getData2());
		queryParas.put("remark",model.getDesc());
		sendTemplateMsg(queryParas);
	}
	
	public void sendFaCheckMsg(MessageModel model){
		String openId = model.getOpenId();
		getLogger().info(openId+"发送固定资产盘点推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		String url = model.getUrl();
		if(StringUtils.isBlank(url)) {
			queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/fa/check");
		}else {
			queryParas.put("url",url);
		}
		queryParas.put("templateId","RUbLzhv52LRIH23RnDb9DyEh6OmwFny6IVXm85xrAWM");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",model.getData1());
		queryParas.put("keyword2",model.getData2());
		queryParas.put("remark",model.getDesc());
		sendTemplateMsg(queryParas);
		
	}
	
	public void sendAffairNoticeMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送事务推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/affair/"+model.getFkId());
		queryParas.put("templateId","RUbLzhv52LRIH23RnDb9DyEh6OmwFny6IVXm85xrAWM");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",model.getData1());
		queryParas.put("keyword2",model.getData2());
		queryParas.put("remark",model.getDesc());
		sendTemplateMsg(queryParas);
	}
	
	public void sendCreateTaskMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送任务填写推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/mytask");
		queryParas.put("templateId","wBRZ1_BsUhSutcNe1f6H_EwSY6LZJdyLIxSoEBjBJcI");
		queryParas.put("first","您的任务待填写\n");
		queryParas.put("keyword1",model.getTitle());
		queryParas.put("keyword2",getUserName(model.getReceiver())+"\n");
		queryParas.put("keyword3",EasyDate.getCurrentDateString("yyyy-MM-dd"));
		queryParas.put("remark","\n请您及时填写工作任务内容！");
		sendTemplateMsg(queryParas);
		
	}
	
	/**
	 * {{first.DATA}}
		计划：{{keyword1.DATA}}
		关联联系人：{{keyword2.DATA}}
		计划时间：{{keyword3.DATA}}
		{{remark.DATA}}
	 * @param model
	 */
	public void sendScheduleNoticeMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送日程推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info("openId is empty.....");
			return;
		}
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",openId);
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","wBRZ1_BsUhSutcNe1f6H_EwSY6LZJdyLIxSoEBjBJcI");
		queryParas.put("first",model.getTitle());
		queryParas.put("keyword1",model.getData1());
		queryParas.put("keyword2",getUserName(model.getSender())+"\n");
		queryParas.put("keyword3",model.getData3());
		queryParas.put("remark",model.getDesc());
		sendTemplateMsg(queryParas);
		
	}
	public void sendFoodServedMsg(MessageModel model){
		String openId=getOpenId(model.getReceiver());
		getLogger().info(openId+"发送sendFoodServedMsg微信推送....."+model.getTitle());
		if(StringUtils.isBlank(openId)){
			getLogger().info(model.getReceiver()+" openId is empty.....");
			return;
		}
		
		this.sendTextMsg(getUserOpenIds(model.getReceiver()), "您的餐品已送达,请尽快食用");
		
		Map<String,String> queryParas=new HashMap<String,String>();
		queryParas.put("openId",getUserOpenIds(model.getReceiver()));
		queryParas.put("url","https://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		queryParas.put("templateId","rKUI8ABcnJ7q17rj_rnZo-kWnzobwQG660e4Hirk_d4");
		queryParas.put("first","您的餐品已送达,请尽快食用。"+"\n");
		queryParas.put("keyword1","--");
		queryParas.put("keyword2","--");
		queryParas.put("keyword3",model.getTitle());
		queryParas.put("remark","\n"+model.getDesc());
		sendTemplateMsg(queryParas);
		
	}

    public static String delHTMLTag(String htmlStr){ 
    	if(StringUtils.isBlank(htmlStr))return "请登录系统查看详细。";
    	htmlStr = htmlStr.replaceAll("<br>", "\\\\n");
    	return Jsoup.clean(htmlStr, Whitelist.simpleText());
    }
    

    
	public String getUserName(String userId){
		return StaffService.getService().getUserName(userId);
	}
	
	public String getOpenId(String userId){
		StaffModel model = StaffService.getService().getStaffInfo(userId);
		if(model!=null&&model.isNormal()) {
			return model.getOpenId();
		}
		return null;
	}
	private String getUserOpenIds(String... userIds){
		if(userIds==null||userIds.length==0)return "";
		JSONObject result=null;//EhcacheKit.get(CacheTime.CacheName.threeMinutes.get(), "userOpenIds");
		if(result==null){
			result=new JSONObject();
			EasyQuery query=ServerContext.getAdminQuery();
			String sql="select USER_ID,OPEN_ID from easi_user where STATE = 0";
			try {
				List<EasyRow> list=query.queryForList(sql);
				if(list!=null){
					for(EasyRow row:list){
						String userId=row.getColumnValue(1);
						String userName=row.getColumnValue(2);
						result.put(userId, userName);
					}
					//EhcacheKit.put(CacheTime.CacheName.threeMinutes.get(), "userOpenIds",result);
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		if(userIds.length==1){
			return result.getString(userIds[0]);
		}else{
			String sb=new String();
			for(String id:userIds){
				sb=sb+result.getString(id)+",";
			}
			sb = sb.substring(0,sb.length() - 1);
			return sb.toString();
		}
	}

}
