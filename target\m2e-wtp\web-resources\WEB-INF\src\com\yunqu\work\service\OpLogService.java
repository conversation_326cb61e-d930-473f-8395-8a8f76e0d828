package com.yunqu.work.service;

import java.sql.SQLException;

import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.OpLogModel;

public class OpLogService extends AppBaseService{
	private static class Holder{
		private static OpLogService service=new OpLogService();
	}
	public static OpLogService getService(){
		return Holder.service;
	}
	
	public void addLog(String opBy,String fkId,String param,String content,String url){
		OpLogModel model=new OpLogModel();
		model.setFkId(fkId);
		model.setOpBy(opBy);
		model.setPrimaryValues(RandomKit.uuid());
		model.setContent(content);
		model.setUrl(url);
		model.setParam(param);
		model.setOpTime(EasyDate.getCurrentDateString());
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
	}

}
