package com.yunqu.work.servlet.common;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.base.Constants;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.utils.DateUtils;



@Before(AuthInterceptor.class)
@Path(value = "/fly")
public class FlyController extends BaseController {

	private static Map<String,String> categoryMap = new LinkedHashMap<String,String>();
	
	static {
		categoryMap.put("0","个人动弹");
		categoryMap.put("1","日常公告");
		categoryMap.put("2","新闻动态");
		categoryMap.put("3","每周菜单");
		categoryMap.put("10", "提问");
		categoryMap.put("20", "分享");
		categoryMap.put("30", "讨论");
		categoryMap.put("40", "建议");
		categoryMap.put("50", "公告");
		categoryMap.put("60", "动态");
	}
	
	public void index() {
		setAttr("indexPageFlag", "1");
		commonData();
		render("/pages/fly/fly-index.jsp");
	}
	
	public void my() {
		navList();
		commonData();
		render("/pages/fly/user-index.jsp");
	}
	
	public void add() {
		navList();
		set("pageMode", "add");
		set("record", new Record());
		render("/pages/fly/fly-edit.jsp");
	}
	
	public void edit() {
		navList();
		String id = getPara();
		Record record = Db.findFirst("select * from yq_remind where remind_id = ?", id);
		if(record==null) {
			renderHtml("不存在记录");
			return;
		}
		set("pageMode", "edit");
		set("record", record);
		render("/pages/fly/fly-edit.jsp");
	}
	
	public void doAdd() {
		EasyResult data = new EasyResult();
		String _id = FlowService.getService().getID();
		boolean isAdd = true;
		String id = getPara("id");
		if(StringUtils.isBlank(id)) {
			id = _id;
		}else {
			isAdd = false;
		}
		Record logRecord = new Record();
		logRecord.set("remind_id",id);
		logRecord.set("title",getPara("title"));
		logRecord.set("remind_type",getPara("classType"));
		logRecord.set("category_name",categoryMap.get(getPara("classType")));
		logRecord.set("content",getPara("content"));
		logRecord.set("experience",getInt("experience"));
		if(isAdd) {
			logRecord.set("creator", getUserId());
			logRecord.set("publish_by", getNickName());
			logRecord.set("create_time",EasyDate.getCurrentDateString());
			logRecord.set("publish_time",EasyDate.getCurrentDateString());
		}
		boolean result = true;
		if(isAdd) {
			result = Db.save("yq_remind", "remind_id", logRecord);
		}else {
			result = Db.update("yq_remind", "remind_id", logRecord);
		}
		if(result) {
			data.setState(1);
			data.put("action", "/yq-work/fly/detail/"+id);
		}else {
			data.setState(0);
		}
		renderJson(data);
		
	}
	
	public void detail() {
		commonData();
		String id = getPara();
		Record record = Db.findFirst("select * from yq_remind where remind_id = ?", id);
		if(record==null) {
			renderHtml("帖子不存在~");
			return;
		}
		String userId = record.getStr("creator");
		StaffModel model = StaffService.getService().getStaffInfo(userId);
		setAttr("userInfo",model);
		setAttr("articleInfo", record);
		setAttr("isSelf", userId.equalsIgnoreCase(getUserId())||isSuperUser());
		setAttr("isSuperUser", isSuperUser());
		
		Record selfData = Db.findFirst("select * from yq_fly_record where remind_id = ? and user_id = ?", id,getUserId());
		setAttr("selfData", selfData);
		
		List<Record> list = Db.find("select t1.*,t2.username,t2.pic_url,t3.job_title,t3.level,t3.points from yq_comments t1,"+Constants.DS_MAIN_NAME+".easi_user t2,"+Constants.DS_MAIN_NAME+".yq_staff_info t3 where 1=1 and t3.staff_user_id = t2.user_id  and t1.creator = t2.user_id and t1.fk_id = ? order by t1.accept_flag desc,t1.like_count desc,t1.create_time desc", id);
		setAttr("commentList", list);
		
		if(!userId.equals(getUserId())) {
			Record logRecord = new Record();
			logRecord.set("log_id", RandomKit.uuid());
			logRecord.set("fk_id",id);
			logRecord.set("look_by", getUserId());
			logRecord.set("look_name", getNickName());
			logRecord.set("look_time",EasyDate.getCurrentDateString());
			logRecord.set("url",getRequest().getRequestURL().toString());
			logRecord.set("agent_info",getRequest().getHeader("user-agent"));
			logRecord.set("ip",WebKit.getIP(getRequest()));
			logRecord.set("type","fly");
			Db.save("yq_look_log", "log_id", logRecord);
			Db.update("update yq_remind t1 JOIN( SELECT fk_id, COUNT(1) AS view_count FROM yq_look_log GROUP BY fk_id) t2 ON t2.fk_id = t1.remind_id SET t1.view_count = t2.view_count WHERE t1.remind_id = ?", id);
			
			updateFlag(id,"look_flag",true);
		}
		
		render("/pages/fly/fly-detail.jsp");
	}
	
	public void jie() {
		setAttr("indexPageFlag", "0");
		commonData();
		render("/pages/fly/fly-index.jsp");
	}
	
	public void messageNums() {
		renderJson(EasyResult.ok(10));
	}
	
	public void jump() {
		String userName = getPara("username");
		String userId = StaffService.getService().getUserId(userName);
		if(StringUtils.notBlank(userId)) {
			redirect("/fly/u/"+userId);
		}else {
			renderHtml(userName+"不存在");
		}
	}
	
	public void search() {
		String question = getPara("q");
		set("question", question);
		jie();
	}
	
	public void reply() {
		String fkId = getPara("jid");
		String content = getPara("content");
		Record record = new Record();
		record.set("fk_id",fkId);
		record.set("content",content);
		record.set("comment_id", RandomKit.uuid());
		record.set("creator", getUserId());
		record.set("create_name", getNickName());
		record.set("create_time",EasyDate.getCurrentDateString());
		record.set("date_id",EasyCalendar.newInstance().getDateInt());
		record.set("user_agent",getRequest().getHeader("user-agent"));
		record.set("ip",WebKit.getIP(getRequest()));
		record.set("source","fly");
		Db.save("yq_comments", "comment_id", record);
		Db.update("UPDATE yq_remind t1 JOIN( SELECT fk_id, COUNT(1) AS comment_count FROM yq_comments GROUP BY fk_id) t2 ON t2.fk_id = t1.remind_id SET t1.comment_count = t2.comment_count WHERE t1.remind_id = ?", fkId);
		renderJson(EasyResult.ok(10));
	}
	
	public void getComment() {
		String id = getPara("id");
		EasyResult result = new EasyResult();
		JSONObject row = new JSONObject();
		String text = Db.queryStr("select content from  yq_comments where comment_id = ?",id);
		row.put("content",text);
		result.put("rows", row);
		renderJson(result);
	}
	
	public void zan() {
		String id = getPara("id");
		boolean ok = getBoolean("ok");
		if(ok) {
			Db.update("update yq_comments set like_count = like_count - 1 where comment_id = ?",id);
		}else {
			Db.update("update yq_comments set like_count = like_count + 1 where comment_id = ?",id);
		}
		renderJson(EasyResult.ok());
	}
	
	public void setTop() {
		String id = getPara("id");
		String field = getPara("field");
		String rank = getPara("rank");
		if("stick".equals(field)) {
			Db.update("update yq_remind set recommend_flag = ? where remind_id = ?", "0".equals(rank)?1:0,id);
		}else if("status".equals(field)) {
			Db.update("update yq_remind set wonderful_flag = ? where remind_id = ?", "0".equals(rank)?0:1,id);
			updateFlag(id,"accept_flag",true);
		}
		renderJson(EasyResult.ok(id));
	}
	
	private void updateFlag(String remindId,String fieldName,boolean isAdd) {
		this.updateFlag(remindId, null,fieldName, isAdd);
	}
	
	private void updateFlag(String remindId,String userId,String fieldName,boolean isAdd) {
		Record record = new Record();
		if(userId==null) {
			userId = getUserId();
		}
		record.set("record_id", MD5Util.getHexMD5(remindId+userId));
		record.set("remind_id",remindId);
		record.set("user_id",userId);
		record.set(fieldName, isAdd?1:0);
		boolean result = Db.update("yq_fly_record", "record_id", record);
		if(!result) {
			Db.save("yq_fly_record", "record_id", record);
		}
	}
	
	public void updateComment() {
		String id = getPara("id");
		String content = getPara("content");
		Db.update("update yq_comments set content = ? where comment_id = ?", content,id);
		EasyResult result = new EasyResult();
		renderJson(result);
	}
	
	public void rewardComment() {
		String id = getPara("id");
		Record commentRecord = Db.findFirst("select * from yq_comments where comment_id = ?",id);
		if(commentRecord!=null) {
			String creator = commentRecord.getStr("creator");
			if(creator.equals(getUserId())) {
				renderJson(EasyResult.fail("不可以采纳自己的回复"));
				return;
			}
			if(Db.update("update yq_comments set accept_flag = ? where comment_id = ?", 1,id)>0) {
				String remindId =  Db.queryStr("select fk_id from yq_comments where comment_id = ?",id);
				if(Db.update("update yq_remind set close_flag = ? where remind_id = ?", 1,remindId)>0) {
					Record record = Db.findFirst("select * from yq_remind where remind_id = ?", remindId);
					this.addArticleExperience(record.getInt("experience"),remindId,creator,0);
					this.addArticleExperience(record.getInt("experience")*-1,remindId,record.getStr("creator"),1);
				}
			}
		}
		renderJson(EasyResult.ok(id));
	}
	
	private void addArticleExperience(int num,String remindId,String creator,int experienceType) {
		if(num!=0) {
			Record record = new Record();
			record.set("experience_id",RandomKit.uniqueStr());
			record.set("experience_num",num);
			record.set("experience_type",experienceType);
			record.set("remind_id",remindId);
			record.set("user_id", creator);
			record.set("experience_source", 1);
			record.set("create_time",EasyDate.getCurrentDateString());
			boolean result = Db.save("yq_fly_experience", "experience_id", record);
			if(result) {
				this.updateUserExperience(creator);
			}
		}
	}
	
	private void updateUserExperience(String userId) {
		int allExperience  = Db.queryInt("select sum(experience_num) from yq_fly_experience where user_id = ?",userId);
		int level = allExperience/100;
		if(level>100) {
			level = 100;
		}else if(level<=0) {
			level = 1;
		}
		Db.update("update "+Constants.DS_MAIN_NAME+".yq_staff_info set points = ?,level = ?  where staff_user_id = ?",allExperience,level,userId);
		
	}
	
	public void deleteArticle() {
		String id = getPara("id");
		Db.update("update yq_remind set status = ? where remind_id = ?", 2,id);
		renderJson(EasyResult.ok(id));
	}
	
	public void deleteComment() {
		String id = getPara("id");
		String remindId =  Db.queryStr("select fk_id from yq_comments where comment_id = ?",id);
		Db.delete("delete from yq_comments where comment_id = ?", id);
		Db.update("update yq_remind t1 JOIN( SELECT fk_id, COUNT(1) AS comment_count FROM yq_comments GROUP BY fk_id) t2 ON t2.fk_id = t1.remind_id SET t1.comment_count = t2.comment_count WHERE t1.remind_id = ?", remindId);
		renderJson(EasyResult.ok(id));
	}
	
	public void collectionremove() {
		String id = getPara("cid");
		updateFlag(id,"collect_flag",false);
		renderJson(EasyResult.ok());
	}
	
	public void collectionadd() {
		String id = getPara("cid");
		updateFlag(id,"collect_flag",true);
		renderJson(EasyResult.ok());
	}
	
	public void collectionFind() {
		String cid = getPara("cid");
		JSONObject object = new JSONObject();
		Integer flag = Db.queryInt("select collect_flag from yq_fly_record where remind_id = ? and user_id = ?", cid,getUserId());
		if(flag!=null) {
			object.put("collection",flag==1);
		}else {
			object.put("collection",false);
		}
		renderJson(EasyResult.ok(object));
	}
	
	public void u() {
		String userId = getPara();
		if(StringUtils.isBlank(userId)) {
			userId =  getUserId();
		}
		StaffModel model = StaffService.getService().getStaffInfo(userId);
		if(model==null) {
			renderHtml("参数错误");
			return;
		}
		navList();
		setAttr("userInfo",model);
		
		EasySQL userArticleSql = new EasySQL();
		userArticleSql.append("from yq_remind t1 where 1=1");
		userArticleSql.append("and t1.status = 0");
		userArticleSql.append(userId,"and t1.creator = ?");
		userArticleSql.append("order by t1.publish_time desc");
		Page<Record>  userArticleList = Db.paginate(1, 12, "select t1.*",userArticleSql.getSQL(),userArticleSql.getParams());
		setAttr("userArticleList",userArticleList.getList());
		
		EasySQL userCommentSql = new EasySQL();
		userCommentSql.append("from yq_remind t1,yq_comments t2 where t1.remind_id = t2.fk_id");
		userArticleSql.append("and t1.status = 0");
		userCommentSql.append(userId,"and t2.creator = ?");
		userCommentSql.append("order by t1.publish_time desc");
		Page<Record>  userCommentList = Db.paginate(1, 5, "select t1.title,t1.remind_id,t2.comment_id,t2.content,t2.create_time comment_time",userCommentSql.getSQL(),userCommentSql.getParams());
		setAttr("userCommentList",userCommentList.getList());
		render("/pages/fly/user-home.jsp");
	}
	
	public void remind() {
		commonData();
		render("/pages/fly/fly-index.jsp");
	}
	
	public void signStatus() {
		JSONObject data = new JSONObject();
		Record record =  Db.findFirst("select * from yq_fly_sign where user_id = ? and date_id = ?",getUserId(),EasyCalendar.newInstance().getDateInt());
		if(record!=null) {
			data.put("signed",true);
			data.put("days",record.getInt("sign_days"));
			data.put("experience",record.getInt("reward_points"));
		}else {
			record =  Db.findFirst("select * from yq_fly_sign where user_id = ? and date_id = ?",getUserId(),DateUtils.getPlanMaxDay(-1));
			if(record!=null) {
				data.put("signed",false);
				data.put("days",record.getInt("sign_days"));
				data.put("experience",calcExperience(record.getInt("sign_days")+1));
			}else {
				data.put("signed",false);
				data.put("days",0);
				data.put("experience",5);
			}
		}	
		EasyResult result = EasyResult.ok();
		result.setData(data);
		renderJson(result);
	}
	
	private int calcExperience(int days) {
		if(days>=5&&days<15) {
			return 10;
		}else if(days>=15&&days<30) {
			return 15;
		}else if(days>=30) {
			return 20;
		}
		return 5;
	}
	
	public void signIn() {
		EasyResult result = EasyResult.ok();
		
		int count = Db.queryInt("select count(1) from yq_fly_sign where user_id = ? and date_id = ?",getUserId(),EasyCalendar.newInstance().getDateInt());
		if(count>0) {
			renderJson(result);
			return;
		}
		int experience = 5;
		int days  = 0;
		JSONObject data = new JSONObject();
		data.put("signed",true);
		
		result.setData(data);
		
		Record yesterdayRecord =  Db.findFirst("select * from yq_fly_sign where user_id = ? and date_id = ?",getUserId(),DateUtils.getPlanMaxDay(-1));
		if(yesterdayRecord!=null) {
			days = yesterdayRecord.getInt("sign_days")+1;
			experience = calcExperience(calcExperience(days));
		}else {
			days = 1;
		}
		data.put("experience",experience);
		data.put("days",days);
		
		Record record = new Record();
		record.set("sign_id", RandomKit.uuid());
		record.set("user_id", getUserId());
		record.set("user_name", getNickName());
		record.set("staff_no", StaffService.getService().getStaffInfo(getUserId()).getStaffNo());
		record.set("date_id",EasyCalendar.newInstance().getDateInt());
		record.set("month_id",EasyCalendar.newInstance().getFullMonth());
		record.set("sign_time",EasyDate.getCurrentDateString());
		record.set("date_id",EasyCalendar.newInstance().getDateInt());
		record.set("sign_ts",System.currentTimeMillis());
		record.set("sign_ip",WebKit.getIP(getRequest()));
		record.set("sign_days",1);
		record.set("reward_points",experience);
		if(Db.save("yq_fly_sign", "sign_id", record)) {
			addSignExperience(experience);
		}
		renderJson(result);
	}
	
	private void addSignExperience(int num) {
		Record record = new Record();
		record.set("experience_id",RandomKit.uniqueStr());
		record.set("experience_num",num);
		record.set("user_id", getUserId());
		record.set("create_time",EasyDate.getCurrentDateString());
		boolean result = Db.save("yq_fly_experience", "experience_id", record);
		if(result) {
			this.updateUserExperience(getUserId());
		}
	}
	
	public void signTop() {
		JSONArray array = new JSONArray();
		JSONArray array1 = new JSONArray();
		JSONArray array2 = new JSONArray();
		JSONArray array3 = new JSONArray();
		
		List<Record> list = Db.find("select * from yq_fly_sign where date_id = ? order by sign_ts desc limit 20",EasyCalendar.newInstance().getDateInt());
		setSignTopInfo(list,array1);
		
		List<Record> list2 = Db.find("select * from yq_fly_sign where date_id = ? order by sign_ts limit 20",EasyCalendar.newInstance().getDateInt());
		setSignTopInfo(list2,array2);
		
		List<Record> list3 = Db.find("SELECT * FROM yq_fly_sign where date_id = ? group by user_id order by sign_days DESC limit 20",DateUtils.getPlanMaxDay(0));
		setSignTopInfo(list3,array3);
		
		array.add(array1);
		array.add(array2);
		array.add(array3);
		EasyResult result = EasyResult.ok();
		result.setData(array);
		renderJson(result);
	}
	
	private void setSignTopInfo(List<Record> list,JSONArray array){
		for(Record record:list) {
			JSONObject row = new JSONObject();
			String userId = record.getStr("user_id");
			row.put("uid", userId);
			row.put("time", record.getStr("sign_time"));
			row.put("days", record.getStr("sign_days"));
			JSONObject userInfo = new JSONObject();
			userInfo.put("username", record.getStr("user_name"));
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
			userInfo.put("avatar", staffModel.getPicUrl());
			row.put("user", userInfo);
			array.add(row);
		}
	}
	
	private void navList() {
		JSONArray array = new JSONArray();
		Set<String> sets = categoryMap.keySet();
		for(String id:sets) {
			int _id = Integer.valueOf(id);
			if(_id>=10) {
				JSONObject row = new JSONObject();
				row.put("id", id);
				row.put("title", categoryMap.get(id));
				array.add(row);
			}
		}
		setAttr("itemList", array);
	}
	
	private void commonData() {
		navList();
		
		EasySQL sql = new EasySQL();
		String commonSql = "from yq_remind t1,"+Constants.DS_MAIN_NAME+".easi_user t2,"+Constants.DS_MAIN_NAME+".yq_staff_info t3 where 1=1 and t3.staff_user_id = t2.user_id and t1.creator = t2.user_id and t1.status = 0";
		sql.append(commonSql);
		String category = getPara("category","");
		if(StringUtils.notBlank(category)) {
			sql.append(category,"and t1.remind_type = ?");
		}
		String question = getPara("q");
		sql.appendLike(question,"and t1.title like ?");
		
		String state = getPara("state","");
		if("1".equals(state)) {
			sql.append(0,"and t1.close_flag = ?");
		}else if("2".equals(state)) {
			sql.append(1,"and t1.close_flag = ?");
		}else if("3".equals(state)) {
			sql.append(1,"and t1.wonderful_flag = ?");
		}
		
		String sort = getPara("sort","time");
		if("time".equals(sort)) {
			sql.append("order by t1.publish_time desc");
		}else {
			sql.append("order by t1.comment_count desc,t1.publish_time desc");
		}
		int pageNo = getParaToInt("page", 1);
		Page<Record>  list = Db.paginate(pageNo, 20, "select t1.*,t2.pic_url,t2.depts,t2.nike_name,t2.username,t2.user_id,t3.job_title,t3.level,t3.points",sql.getSQL(),sql.getParams());
		setAttr("pageNo", pageNo+1);
		setAttr("allList",list.getList());
		
		EasySQL hotSql = new EasySQL();
		hotSql.append(commonSql);
		hotSql.append(DateUtils.getBeforeDate(60, "yyyy-MM-dd HH:mm:ss"),"and t1.publish_time >= ?");
		hotSql.append("order by t1.comment_count desc");
		Page<Record>  hostList = Db.paginate(1, 10, "select t1.*,t2.pic_url,t2.depts,t2.nike_name,t2.username,t2.user_id,t3.job_title,t3.level,t3.points",hotSql.getSQL(),hotSql.getParams());
		setAttr("hostList",hostList.getList());
		
		EasySQL topSql = new EasySQL();
		topSql.append(commonSql);
		topSql.append(0,"and t1.recommend_flag = ?");
		topSql.append(DateUtils.getBeforeDate(180, "yyyy-MM-dd HH:mm:ss"),"and t1.publish_time >= ?");
		topSql.append("order by t1.publish_time desc");
		Page<Record>  topList = Db.paginate(1, 10, "select t1.*,t2.pic_url,t2.depts,t2.nike_name,t2.username,t2.user_id,t3.job_title,t3.level,t3.points",topSql.getSQL(),topSql.getParams());
		setAttr("topList",topList.getList());
		
		EasySQL replyTopSql = new EasySQL();
		replyTopSql.append("from yq_comments t1,"+Constants.DS_MAIN_NAME+".easi_user t2 where 1=1 and t1.creator = t2.user_id");
		replyTopSql.append(DateUtils.getBeforeDate(180, "yyyyMMdd"),"and t1.DATE_ID >= ?");
		replyTopSql.append("task","and t1.SOURCE = ?");
		replyTopSql.append("GROUP BY t1.CREATOR");
		replyTopSql.append("ORDER BY COUNT DESC");
		Page<Record> replyTopList = Db.paginate(1, 12, "SELECT t2.username,t2.user_id,t2.pic_url,count(1) count",replyTopSql.getSQL(),replyTopSql.getParams());
		setAttr("replyTopList",replyTopList.getList());
	    
	}
	
}
