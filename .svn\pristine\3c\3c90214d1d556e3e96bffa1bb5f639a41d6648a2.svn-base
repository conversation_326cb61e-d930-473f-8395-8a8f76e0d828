<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>合同评审</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm" style="width: 170px">
							 <span class="input-group-addon">评审编号</span>	
							 <input type="text" name="contractNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 170px">
							 <span class="input-group-addon">合同名称</span>	
							 <input type="text" name="contractName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
		          	     <div class="pull-right">
				  			<button class="btn btn-sm btn-info ml-10" type="button" onclick="list.add()">+ 发起评审</button>
		          	     </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ReviewDao.reviewDetailMgr',
					height:'full-120',
					limit:20,
					cols: [[
					  {title:'',type:'radio'},
					  {title:'序号',type:'numbers'},
					  {
					    field: 'REVIEW_NO',
						title: '评审编号',
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail',
						width:120
					 },
					 {
					    field: 'CONTRACT_NAME',
						title: '标题',
						align:'left'
					},{
						field:'NODE_NAME',
						title:'小组名称'
					},{
						field:'CHECK_STATE',
						title:'当前状态',
						templet:function(row){
							if(row['CHECK_STATE']=='10'){
								return '初审';
							}else{
								return '终审';
							}
						}
					},{
						field:'FIRST_CHECK_NAMES',
						title:'当前处理人'
					},{
					    field: 'CREATE_TIME',
						title: '申请时间',
						width:120,
						align:'center'
					},{
					    field: 'CREATE_USER_NAME',
						title: '申请人',
						width:120,
						align:'center'
					}
				  ]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(data){
				popup.openTab({id:'reviewDetail',title:'评审详情',url:'${ctxPath}/pages/bpm/review/review-apply.jsp',title:data.CONTRACT_NAME,data:{contractId:data.CONTRACT_ID,custId:data['CUST_ID'],reviewId:data['REVIEW_ID'],isDiv:0}});
			},
			add:function(){
				popup.openTab({id:'reviewDetail',url:'${ctxPath}/pages/bpm/review/review-apply.jsp',title:'合同评审'});
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>