<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<form id="flowccForm">
	<table class="table table-edit table-vzebra container-fluid mt-10">
	   	<tr>
	   		<td style="width: 120px;">被抄送人</td>
	   		<td>
	   			<input type="hidden" name="ccIds"/>
			    <input type="text" style="width: 90%;" data-rules="required" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="ccNames"/>
	   		</td>
	   	</tr>
	   	<tr>
	   		<td style="vertical-align: top;margin-top: 20px;">抄送意见</td>
	   		<td>
	   			<textarea style="height: 80px;width: 90%;" name="ccRemark" class="form-control">请尽快查阅</textarea>
	   		</td>
	   	</tr>
	   	<tr>
	   		<td>已抄送</td>
	   		<td data-mars="FlowCommon.ccList" data-template="cc-template"></td>
	   	</tr>
	 </table>
	<div class="layer-foot text-c">
	  	 <button class="btn btn-info btn-sm" type="button" onclick="FlowCore.submitCC();"> 提 交 </button>
	</div>
</form>
<script id="cc-template" type="text/x-jsrender">
	{{for data}}
		<span title="{{:CREATE_NAME}}于{{:CC_TIME}}抄送">{{:CC_BY_NAME}}</span>
	{{/for}}	
	{{if data.length==0}}--{{/if}}				         
</script>  
<script type="text/javascript">
	$(function(){
		$('#flowccForm').render({data:FlowCore.params});
	});
</script>