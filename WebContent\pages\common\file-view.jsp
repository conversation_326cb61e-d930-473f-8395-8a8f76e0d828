<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
	<form id="FileRelateLogForm">
		<div id="FileRelateLog" style="padding: 10px 20px;">
			 <table id="fileViewTable"></table>
		</div>
	</form>
	<script type="text/javascript">
	
		jQuery.namespace("FileRelateLog");
		
		FileRelateLog.fkId='${param.fkId}';
		FileRelateLog.fileId='${param.fileId}';
		
		$(function(){
			$("#FileRelateLogForm").initTable({
				mars:'FileDao.fileListDetail',
				id:'fileViewTable',
				page:false,
				data:{fkId:FileRelateLog.fkId},
				cols: [[
		         {
		    	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
				    field: 'FILE_NAME',
					title: '文件名',
					align:'left',
					minWidth:100
				},
				{
				    field: 'FILE_SIZE',
					title: '文件大小',
					width:80,
					align:'center'
				},{
		        	field: 'DOWNLOAD_COUNT',
		        	title: '下载次数',
		        	width:80
		        },
				{
				    field: 'CREATE_NAME',
					title: '上传人',
					align:'center',
					width:80
				},{
				    field: 'CREATE_TIME',
					title: '上传时间',
					width:145,
					align:'center'
				},{
					field:'',
					title:'操作',
					width:100,
					templet:'<div><a class="btn btn-xs btn-link" target="_blank" href="/yq-work/fileview/{{d.FILE_ID}}">下载</a><a  href="/yq-work/fileview/{{d.FILE_ID}}?view=online" target="_blank" class="btn btn-xs btn-link">预览</a></div>'
				}
				]]}
			);
		});
</script>
