<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>project</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="stageEditForm" class="form-horizontal" data-mars="ContractStageDao.record" autocomplete="off" data-mars-prefix="contractStage.">
        <input type="hidden" value="${param.stageId}" name="contractStage.STAGE_ID"/>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td width="100px" class="required">合同Id</td>
                <td style="width: 40%">
                    <input class="form-control" type="text" name="contractStage.CONTRACT_ID" readonly value="${param.contractId}">
                </td>
                <td width="100px" class="required">阶段名称</td>
                <td style="width: 40%">
                    <SELECT data-rules="required" name="contractStage.STAGE_NAME" class="form-control">
                        <option value="">请选择</option>
                        <option value="预验">预验</option>
                        <option value="初验">初验</option>
                        <option value="终验">终验</option>
                    </SELECT>
                </td>
            </tr>
            <tr>
                <td class="required">是否应收</td>
                <td>
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio"  value="Y" name="contractStage.IS_RECEIVE"><span>是</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="N" name="contractStage.IS_RECEIVE"><span>否</span>
                    </label>
                </td>
                <td>应收时间</td>
                <td>
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contractStage.RCV_DATE" class="form-control input-sm Wdate">
                </td>
            </tr>
            <tr>
                <td>计划完成时间</td>
                <td>
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contractStage.PLAN_COMP_DATE" class="form-control input-sm Wdate">
                </td>
                <td>实际完成时间</td>
                <td>
                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contractStage.ACT_COMP_DATE" class="form-control input-sm Wdate">
                </td>
            </tr>
            <tr>
                <td class="required">比例(%)</td>
                <td>
                    <input type="text" data-rules="required" name="contractStage.RATIO" class="form-control" oninput="StageEdit.calcPlanAmt(this.value)">
                </td>
                <td class="required">计划收款金额</td>
                <td >
                    <input type="text" name="contractStage.PLAN_RCV_AMT" class="form-control" placeholder="填写比例后自动计算">
                </td>
            </tr>
            <tr>
                <td >余款维保条件</td>
                <td >
                    <input type="text" name="contractStage.YK_WB_COND" class="form-control">
                </td>
                <td >余款条件天数</td>
                <td>
                    <input type="text" name="contractStage.YK_COND_DAYS" class="form-control" >
                </td>
            </tr>
            <tr>
                <td >坏账金额</td>
                <td colspan="3">
                    <input type="text" name="contractStage.BAD_DEBT_AMT" class="form-control" value="0">
                </td>
            </tr>
            </tbody>
        </table>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="StageEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("StageEdit");

        StageEdit.stageId = '${param.stageId}';
        StageEdit.isNew = '${param.isNew}';
        StageEdit.contractId = '${param.contractId}';
        StageEdit.amount = 0;

        $(function(){
            $("#stageEditForm").render({success:function(result){
                    StageEdit.getAmount();

                }});
        });

        StageEdit.ajaxSubmitForm = function(){
            if(form.validate("#stageEditForm")){
                if(StageEdit.isNew=='1'){
                    StageEdit.insertData();
                }
                else if(StageEdit.stageId){
                    StageEdit.updateData();
                }else{
                    StageEdit.insertData();
                }
            };
        }

        StageEdit.insertData = function() {
            var data = form.getJSONObject("#stageEditForm");
            ajax.remoteCall("${ctxPath}/servlet/contractStage?action=add",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadStageList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        StageEdit.updateData = function() {
            var data = form.getJSONObject("#stageEditForm");
            ajax.remoteCall("${ctxPath}/servlet/contractStage?action=update",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadStageList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        StageEdit.getAmount = function(){
            var data = {"params":{"contractId":StageEdit.contractId},"controls":["ProjectContractDao.record"]};
            ajax.remoteCall("${ctxPath}/webcall",data,function(result) {
                if(result){
                    StageEdit.amount= parseFloat(result['ProjectContractDao.record']['data']['AMOUNT']);
                }
            })
        }

        StageEdit.calcPlanAmt = function(ratio){
            var ratio =parseFloat(ratio);
            var  planAmt = StageEdit.amount * ratio / 100;
            $("#stageEditForm input[name='contractStage.PLAN_RCV_AMT']").val(planAmt)
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>