package com.yunqu.work.servlet.work;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.CommentModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.model.TaskModel;
import com.yunqu.work.service.CommentService;
import com.yunqu.work.service.CommonService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.OpLogService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.TaskNoticeService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebSocketUtils;
import com.yunqu.work.utils.WebhookKey;
@WebServlet("/servlet/task/*")
@MultipartConfig(maxFileSize=1024 * 1024 * 50)
public class TaskServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private static JSONObject taskNames=new JSONObject();
	static{
		taskNames.put("10", "待办");
		taskNames.put("11", "已退回");
		taskNames.put("12", "已暂停");
		taskNames.put("13", "已取消");
		taskNames.put("20", "进行中");
		taskNames.put("30", "已完成");
		taskNames.put("40", "已验收");
		taskNames.put("42", "验收不通过");
	}
	public EasyResult actionForBatchAdd(){
		JSONObject params = getJSONObject();
		JSONArray ids = params.getJSONArray("ids");
		if(ids==null||ids.size()==0){
			return EasyResult.fail();
		}
		String projectName=params.getString("projectName");
		for(int j=0;j<ids.size();j++){
			String id = ids.getString(j);
			TaskModel model=getModel(TaskModel.class, "task");
			model.set("PROJ_NAME", projectName);
			model.setPrimaryValues(RandomKit.uuid());
			String assignUserId =  params.getString("assignUserId_"+id);
			String title = params.getString("title_"+id);
			String taskDesc = params.getString("desc_"+id);
			if(StringUtils.isBlank(assignUserId)||StringUtils.isBlank(title)){
				continue;
			}
			model.set("DEPT_NAME",getDeptName());
			model.set("TASK_TYPE_ID", params.getString("typeId_"+id));
			model.set("TASK_LEVEL", params.getString("level_"+id));
			model.set("MODULE_ID", params.getString("moduleId_"+id));
			model.set("MODULE_NAME", params.getString("module_name_"+id));
			model.set("PLAN_STARTED_AT", params.getString("start_time_"+id));
			model.set("DEADLINE_AT", params.getString("end_time_"+id));
			model.set("ASSIGN_USER_ID",assignUserId);
			model.set("ASSIGN_DEPT_ID",StaffService.getService().getStaffInfo(assignUserId).getDeptId());
			model.set("ASSIGN_USER_NAME",StaffService.getService().getUserName(assignUserId));
			model.set("ASSIGN_DEPT_NAME",StaffService.getService().getUserDeptName(assignUserId));
			model.set("TASK_DESC", taskDesc);
			model.set("TASK_NAME", title);
			model.addCreateTime();
			model.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
			model.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			model.setCreator(getUserId());
			model.set("CREATE_NAME", getUserName());
			model.set("DEPT_ID", getDeptId());
			model.set("TASK_STATE", 10);
			model.set("UPDATE_BY", getUserId());
			String[] cc=null;
			try {
				JSONArray array=model.getJSONArray("NOTICE_TYPES");
				JSONObject jsonObject=getJSONObject();
				JSONArray mailtos=jsonObject.getJSONArray("mailtos");
				model.remove("NOTICE_TYPES");
				model.put("NOTICE_TYPES",StringUtils.join(array.toArray(), ","));
				
				if(mailtos!=null){
					cc=new String[mailtos.size()];
					for(int i=0;i<mailtos.size();i++){
						String userId=mailtos.getString(i);
						cc[i]=userId;
						EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
						record.setPrimaryValues(model.getTaskId(),userId);
						record.set("CREATOR", getUserId());
						record.set("CREATE_TIME", EasyDate.getCurrentDateString());
						try {
							this.getQuery().save(record);
						} catch (Exception ex) {
							this.error(ex.getMessage(), ex);
						}
						MessageModel messageModel=new MessageModel();
						messageModel.setReceiver(userId);
						messageModel.setTypeName("task");
						messageModel.setContents("【任务】抄送|"+model.getTaskName());
						messageModel.setFkId(model.getTaskId());
						messageModel.setSender(getUserId());
						messageModel.setSendName(getUserName());
						messageModel.setMsgState(1);
						messageModel.setMsgType(2001);
						messageModel.setSenderType(0);
						messageModel.setDesc(model.getString("TASK_DESC"));
						messageModel.setData(model);
						messageModel.setTplName("newTask.html");
						try {
							WebSocketUtils.sendMessage(new MsgModel(userId,"新的抄送任务", model.getTaskName()));
							MessageService.getService().sendMsg(messageModel);
							if(array.contains("3")){
								//EmailService.getService().sendEmail(messageModel);
							}
							messageModel.setDesc(model.getString("TASK_DESC"));
							if(array.contains("4")){
								WxMsgService.getService().sendNewTaskMsg(messageModel);
							}
						} catch (Exception e) {
							this.error(e.getMessage(), e);
						}
					}
				}
				MessageModel messageModel=new MessageModel();
				messageModel.setReceiver(model.getString("ASSIGN_USER_ID"));
				messageModel.setContents(model.getTaskName());
				messageModel.setFkId(model.getTaskId());
				messageModel.setSender(getUserId());
				messageModel.setTypeName("task");
				messageModel.setSendName(getUserName());
				messageModel.setMsgState(1);
				messageModel.setMsgType(2001);
				messageModel.setSenderType(0);
				messageModel.setCc(cc);
				messageModel.setData(model);
				messageModel.setTplName("newTask.html");
				
				model.save();
				
				try {
					WebSocketUtils.sendMessage(new MsgModel(assignUserId,"新的待办任务", model.getTaskName()));
					MessageService.getService().sendMsg(messageModel);
					if(array.contains("3")){
						EmailService.getService().sendTaskEmail(messageModel);
					}
					messageModel.setDesc(model.getString("TASK_DESC"));
					if(array.contains("4")){
						WxMsgService.getService().sendNewTaskMsg(messageModel);
					}
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			
				OpLogService.getService().addLog(getUserId(), model.getTaskId(),"10","新增任务",getRequestUrl());
				String projectId=model.getString("PROJECT_ID");
				if(StringUtils.notBlank(projectId)){
					this.getQuery().executeUpdate("update YQ_PROJECT set UPDATE_TIME = ? where project_id = ?", EasyDate.getCurrentDateString(),projectId);
					this.getQuery().executeUpdate("update yq_project t1 set t1.task_count = (select count(1) from yq_task t3 where t3.PROJECT_ID= t1.PROJECT_ID) where t1.PROJECT_ID = ?",projectId);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok(null,"创建成功.");
	}
	
	public EasyResult actionForAdd(){
		TaskModel taskData = getModel(TaskModel.class, "task");
		taskData.addCreateTime();
		taskData.set("DEPT_NAME",getDeptName());
		taskData.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
		taskData.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
		taskData.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		taskData.setCreator(getUserId());
		taskData.set("DEPT_ID", getDeptId());
		taskData.set("TASK_STATE", 10);
		taskData.set("CREATE_NAME", getUserName());
		taskData.set("UPDATE_BY", getUserId());
		String  assignUserId = taskData.getString("ASSIGN_USER_ID");
		if(StringUtils.isBlank(assignUserId)) {
			return EasyResult.fail("assignUserId is empty.");
		}
		taskData.set("ASSIGN_DEPT_ID",StaffService.getService().getStaffInfo(assignUserId).getDeptId());
		taskData.set("ASSIGN_USER_NAME",StaffService.getService().getUserName(assignUserId));
		taskData.set("ASSIGN_DEPT_NAME",StaffService.getService().getUserDeptName(assignUserId));
		String[] cc = null;
		try {
			JSONArray array = taskData.getJSONArray("NOTICE_TYPES");
			JSONObject jsonObject = getJSONObject();
			JSONArray mailtos = jsonObject.getJSONArray("mailtos");
			taskData.remove("NOTICE_TYPES");
			taskData.put("NOTICE_TYPES",StringUtils.join(array.toArray(), ","));//3 邮件  4 微信
			taskData.remove("PROJECT_NAME");
			this.getQuery().save(taskData);
			
			//任务抄送
			if(mailtos!=null){
				cc = new String[mailtos.size()];
				for(int i=0;i<mailtos.size();i++){
					String userId=mailtos.getString(i);
					cc[i] = userId;
					EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
					record.setPrimaryValues(taskData.getTaskId(),userId);
					record.set("CREATOR", getUserId());
					record.set("CREATE_TIME", EasyDate.getCurrentDateString());
					try {
						this.getQuery().save(record);
					} catch (Exception ex) {
						this.error(ex.getMessage(), ex);
					}
					MessageModel ccMessageModel=new MessageModel();
					ccMessageModel.setReceiver(userId);
					ccMessageModel.setTypeName("task");
					ccMessageModel.setContents("【任务抄送】"+taskData.getTaskName());
					ccMessageModel.setFkId(taskData.getTaskId());
					ccMessageModel.setSender(getUserId());
					ccMessageModel.setSendName(getUserName());
					ccMessageModel.setMsgState(1);
					ccMessageModel.setMsgType(2001);
					ccMessageModel.setSenderType(2);
					ccMessageModel.setDesc(taskData.getString("TASK_DESC"));
					ccMessageModel.setData(taskData);
					try {
						WebSocketUtils.sendMessage(new MsgModel(userId,"任务抄送", taskData.getTaskName()));
						MessageService.getService().sendMsg(ccMessageModel);
						if(array.contains("4")){
							WxMsgService.getService().sendNewTaskMsg(ccMessageModel);
						}
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			
			MessageModel messageModel = new MessageModel();
			messageModel.setReceiver(assignUserId);
			messageModel.setContents("【任务】"+taskData.getTaskName());
			messageModel.setFkId(taskData.getTaskId());
			messageModel.setSender(getUserId());
			messageModel.setTypeName("task");
			messageModel.setCc(cc);
			messageModel.setSendName(getUserName());
			messageModel.setMsgState(1);
			messageModel.setMsgType(2001);
			messageModel.setSenderType(2);
			messageModel.setFileList(CommonService.getService().getFileList(taskData.getTaskId()));
			messageModel.setData(taskData);
			messageModel.setTplName("newTask.html");
			
			this.addBaseUrl(taskData, messageModel);
			
			String groupId = taskData.getString("GROUP_ID");
			if(StringUtils.notBlank(groupId)) {
				this.updateTaskPlanData(groupId, 0);
			}
			
			try {
				WebSocketUtils.sendMessage(new MsgModel(assignUserId,"新的待办任务", taskData.getTaskName()));
				MessageService.getService().sendMsg(messageModel);
				if(array.contains("3")){
					EmailService.getService().sendTaskEmail(messageModel);
				}
				messageModel.setDesc(taskData.getString("TASK_DESC"));
				if(array.contains("4")){
					WxMsgService.getService().sendNewTaskMsg(messageModel);
				}
			} catch (Exception e) {
				this.error(e.getMessage(), e);
			}
			
			OpLogService.getService().addLog(getUserId(), taskData.getTaskId(),"10","新增任务",getRequestUrl());
			
			String projectId = taskData.getString("PROJECT_ID");
			if(StringUtils.notBlank(projectId)){
				this.getQuery().executeUpdate("update yq_project set update_time = ? where project_id = ?", EasyDate.getCurrentDateString(),projectId);
				this.getQuery().executeUpdate("update yq_project t1 set t1.task_count = (select count(1) from yq_task t3 where t3.PROJECT_ID= t1.PROJECT_ID) where t1.PROJECT_ID = ?",projectId);
			}
			this.getQuery().executeUpdate("update yq_task t1 set t1.file_count = (select count(1) from yq_files t2 where t2.FK_ID=t1.task_id) where t1.task_id= ?",taskData.getTaskId());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"创建成功.");
	}
	
	public EasyResult actionForAddFlowTask(){
		JSONArray array = getJSONArray();
		if(array==null) {
			return EasyResult.fail("无数据");
		}
		String applyId = null;
		for(int i=0,len=array.size();i<len;i++) {
			JSONObject params = array.getJSONObject(i);
			applyId = params.getString("applyId");
			String userId = params.getString("applyBy");
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
			TaskModel model = new TaskModel();
			model.addCreateTime();
			model.set("DEPT_NAME",staffModel.getDeptName());
			model.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
			model.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			model.set("CREATE_NAME", staffModel.getUserName());
			model.setCreator(userId);
			model.set("DEPT_ID", staffModel.getDeptId());
			model.set("TASK_STATE", 10);
			model.set("TASK_TYPE_ID", 2);//需求
			model.set("TASK_LEVEL", 2);
			model.set("TASK_DIFFICULTY", "中等");
			model.set("UPDATE_BY", userId);
			String  assignUserId = params.getString("assignUserId");
			model.set("ASSIGN_USER_ID", assignUserId);
			model.set("ASSIGN_DEPT_ID",StaffService.getService().getStaffInfo(assignUserId).getDeptId());
			model.set("ASSIGN_USER_NAME",StaffService.getService().getUserName(assignUserId));
			model.set("ASSIGN_DEPT_NAME",StaffService.getService().getUserDeptName(assignUserId));
			model.set("TASK_DESC", params.getString("taskDesc"));
			model.set("TASK_ID", params.getString("taskId"));
			model.set("TASK_NAME", params.getString("taskName"));
			model.set("PROJECT_ID", params.getString("projectId"));
			model.set("MODULE_ID", params.getString("projectId"));
			model.set("BUSINESS_ID", params.getString("businessId"));
			model.set("MODULE_NAME", "默认模块");
			model.set("PROJ_NAME", params.getString("projName"));
			model.set("PLAN_STARTED_AT", params.getString("planStartedAt"));
			model.set("DEADLINE_AT", params.getString("deadlineAt"));
			model.set("PLAN_WORK_HOUR", params.getString("planWorkHour"));
			try {
				this.getQuery().save(model);
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
		if(StringUtils.notBlank(applyId)) {
			try {
				this.getQuery().executeUpdate("update yq_flow_apply t1 set t1.data8 = (SELECT sum(t2.data5)  from yq_flow_apply_item t2 where t2.business_id = t1.apply_id) WHERE t1.flow_code='ts_rd' and t1.apply_id = ?",applyId);
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForAddCC(){
		JSONObject jsonObject = getJSONObject();
		String taskId = jsonObject.getString("taskId");
		String ccReason = jsonObject.getString("ccReason");
		JSONArray mailtos = jsonObject.getJSONArray("mailtos");
		if(mailtos!=null){
			for(int i=0;i<mailtos.size();i++){
				String userId=mailtos.getString(i);
				EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
				record.setPrimaryValues(taskId,userId);
				record.set("CREATOR", getUserId());
				record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				try {
					MessageModel messageModel = new MessageModel();
					WebSocketUtils.sendMessage(new MsgModel(userId,"新的抄送任务", "请点击我参与的任务查看."));
					this.getQuery().save(record);
					
					
					JSONObject task = this.getQuery().queryForRow("select * from yq_task where task_id = ?", new Object[]{taskId},new JSONMapperImpl());
					task.put("ccReason", ccReason);
					messageModel.setColumns(task);
					messageModel.setFkId(taskId);
					messageModel.setData(task);
					messageModel.setTplName("ccTask.html");
					messageModel.setTitle("【任务抄送】"+task.getString("TASK_NAME"));
					messageModel.setReceiver(userId);
					messageModel.setSender(getUserId());
					messageModel.setDesc(ccReason);
					EmailService.getService().sendTaskEmail(messageModel);
				} catch (Exception ex) {
					this.error(ex.getMessage(), ex);
				}
			}
		}else{
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForUpdateState(){
		JSONObject jsonObject=getJSONObject();
		
		TaskModel model=getModel(TaskModel.class, "task");
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		int taskState = model.getTaskState();
		Integer oldTaskState=jsonObject.getIntValue("TASK_STATE");
		String assignUserId=jsonObject.getString("ASSIGN_USER_ID");//负责人
		String creator=jsonObject.getString("CREATOR");//发起人
		String taskName=jsonObject.getString("TASK_NAME");
		if(oldTaskState==taskState&&oldTaskState!=42){
			return EasyResult.fail("状态没更改!");
		}
		try {
			JSONObject taskInfoObj =  getQuery().queryForRow("select * from yq_task where task_id = ?",new Object[] {model.getTaskId()},new JSONMapperImpl());
			if(taskInfoObj==null) {
				return EasyResult.fail("没找到对应记录，如问题持续，请联系管理员");
			}
			MessageModel messageModel=new MessageModel();
			messageModel.setFkId(model.getTaskId());
			messageModel.setTypeName("task");
			messageModel.setSender(getUserId());
			messageModel.setMsgState(1);
			messageModel.setMsgType(2001);
			messageModel.setSenderType(2);
			messageModel.setSendName(getUserName());
			messageModel.setData(taskInfoObj);
			messageModel.setTitle(jsonObject.getString("TASK_NAME"));
			messageModel.setContents("【任务】"+taskName);
			this.addBaseUrl(taskInfoObj, messageModel);
			//任务完成
			if(taskState==30){
				model.set("WORK_HOUR", jsonObject.getString("workHour"));
				model.set("FINISH_TIME", EasyDate.getCurrentDateString());
				model.set("PROGRESS", "100");
				model.set("FINISH_DATE_ID", EasyCalendar.newInstance().getDateInt());
				messageModel.setTplName("finishedTask.html");
				messageModel.setReceiver(creator);
				try {
					MessageService.getService().sendMsg(messageModel);
					WxMsgService.getService().sendFinishTaskMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			//校验完毕
			if(taskState==40){
				messageModel.setTplName("checkdTask.html");
				model.set("CHECK_TIME", EasyDate.getCurrentDateString());
				messageModel.setReceiver(assignUserId);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			//校验不通过
			if(taskState==42){
				String desc=jsonObject.getString("comment");
				messageModel.setTplName("failedcheckdTask.html");
				messageModel.setReceiver(assignUserId);
				model.set("TASK_DESC", desc);
				messageModel.setData(taskInfoObj);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			model.remove("NOTICE_TYPES");
			model.remove("TASK_DESC");
			model.update();
			
			String groupId = taskInfoObj.getString("GROUP_ID");
			if(StringUtils.notBlank(groupId)) {
				if(taskState==30){
					this.updateTaskPlanData(groupId,1);
					int delayCount = taskInfoObj.getIntValue("DELAY_COUNT");
					if(delayCount>0) {
						this.getQuery().executeUpdate(" UPDATE yq_project_plan t3 LEFT JOIN( SELECT t2.group_id, COUNT(1) AS delay_count FROM yq_task_delay t1 JOIN yq_task t2 ON t1.task_id = t2.task_id WHERE t2.task_state IN (10, 20) AND t2.group_id = ? GROUP BY t2.group_id) t ON t3.plan_id = t.group_id SET t3.task_delay_count = COALESCE(t.delay_count, 0)",groupId);
					}
				}
				else if(taskState==20){
					this.getQuery().executeUpdate("update yq_project_plan set plan_state = ? where plan_id = ?",5, groupId);
				}
			}

			String msgTitle="修改任务进度["+taskNames.getString(String.valueOf(taskState))+"]";
			OpLogService.getService().addLog(getUserId(), model.getTaskId(),taskState+"",msgTitle,getRequestUrl());
		
			MsgModel msgModel=new MsgModel(creator,messageModel.getTitle(),msgTitle); 
			WebSocketUtils.sendMessage(msgModel);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"修改成功.");
	}
	
	private void updateTaskPlanData(String planId,int isFinish) {
		if(isFinish==0) {
			try {
				this.getQuery().executeUpdate("update yq_project_plan t1 set t1.task_count = (select count(1) from yq_task t2 where t2.group_id = t1.plan_id and t2.group_id = ? and t2.task_state in(10,12,20,30,40,42)),plan_process = ROUND((task_finish_count / task_count) * 100 / 10) * 10,t1.finish_date = '' where t1.plan_id = ?",planId,planId);
				String planStartedAt = this.getQuery().queryForString("select min(PLAN_STARTED_AT) from yq_task where group_id = ?", planId);
				if(StringUtils.notBlank(planStartedAt)) {
					this.getQuery().executeUpdate("update yq_project_plan t1 set t1.begin_date = ?,t1.finish_date = '',t1.plan_state = ? where t1.plan_id = ?",DateUtils.getDate(planStartedAt,"yyyy-MM-dd HH:mm","yyyy-MM-dd"),5,planId);
					try {
						String parentId = this.getQuery().queryForString("select p_plan_id from yq_project_plan where plan_id = ?", planId);
						if(StringUtils.notBlank(parentId)&&!"0".equals(parentId)) {
							int taskCount = this.getQuery().queryForInt("select sum(task_count) from yq_project_plan where p_plan_id = ?", parentId);
							this.getQuery().executeUpdate("update yq_project_plan set begin_date = ?,finish_date = '',plan_state = 5,task_count = ? where plan_id = ?",DateUtils.getDate(planStartedAt,"yyyy-MM-dd HH:mm","yyyy-MM-dd"),taskCount,parentId);
						}
					} catch (SQLException e) {
						this.error(e.getMessage(), e);
					}
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}else {
			try {
				JSONObject plan = this.getQuery().queryForRow("SELECT t1.p_plan_id,COUNT(*) AS total_tasks, SUM(CASE WHEN t2.task_state in(10,12,20) THEN 1 ELSE 0 END) AS unfinished_tasks FROM yq_project_plan t1 INNER JOIN yq_task t2 ON t1.plan_id = t2.group_id WHERE t1.plan_id = ?", new Object[]{planId},new JSONMapperImpl());
				if(plan!=null) {
					int planState = 5;//1 待开始 5 进行中 9 已完成
					int taskCount = plan.getIntValue("TOTAL_TASKS");
					int unfinishedTasks = plan.getIntValue("UNFINISHED_TASKS");
					int finishedTasks = taskCount - unfinishedTasks;
					//进度应该是整数 比如0到100之间的整数 10个任务 完成了9个 就是90
					
					double rawProgress = (taskCount - unfinishedTasks) * 100.0 / taskCount;
					int progress = (int)(Math.round(rawProgress/10.0) * 10); // 四舍五入到最近的10的倍数
					
					if(unfinishedTasks == 0) {
						planState = 9;
						progress = 100;
						String maxFinishDateId = this.getQuery().queryForString("select max(FINISH_DATE_ID) from yq_task where group_id = ?", planId);
						this.getQuery().executeUpdate("update yq_project_plan set plan_state = ?,plan_process = ?,finish_date = ?,task_count = ?,task_finish_count = ? where plan_id = ?",planState,progress,DateUtils.getDate(maxFinishDateId,"yyyyMMdd","yyyy-MM-dd"),taskCount,finishedTasks,planId);
					}else{
						this.getQuery().executeUpdate("update yq_project_plan set plan_state = ?,plan_process = ?,task_count = ?,task_finish_count = ? where plan_id = ?",planState,progress,taskCount,finishedTasks,planId);
					}
					
					String pPlanId = plan.getString("P_PLAN_ID");
					if(StringUtils.notBlank(pPlanId)&&!"0".equals(pPlanId)) {
						JSONObject parentPlan = this.getQuery().queryForRow("select sum(task_count) task_count,sum(task_finish_count) task_finish_count from yq_project_plan where p_plan_id = ?", new Object[] {pPlanId},new JSONMapperImpl());
						if(parentPlan!=null) {
							int _taskCount = parentPlan.getIntValue("TASK_COUNT");
							int _taskFinishCount = parentPlan.getIntValue("TASK_FINISH_COUNT");
							int _unfinishedTasks = _taskCount - _taskFinishCount;
							double _rawProgress = (_taskCount - _unfinishedTasks) * 100.0 / _taskCount;
							int _progress = (int)(Math.round(_rawProgress/10.0) * 10); 
							if(_unfinishedTasks == 0) {
								String maxFinishDate = this.getQuery().queryForString("select max(finish_date) from yq_project_plan where p_plan_id = ?", pPlanId);
								this.getQuery().executeUpdate("update yq_project_plan set plan_state = ?,plan_process = ?,finish_date = ?,task_count = ?,task_finish_count = ? where plan_id = ?",9,100,maxFinishDate,_taskCount,_taskFinishCount,pPlanId);
							}else{
								this.getQuery().executeUpdate("update yq_project_plan set plan_state = ?,plan_process = ?,task_count = ?,task_finish_count = ? where plan_id = ?",5,_progress,_taskCount,_taskFinishCount,pPlanId);
							}
						}
					}
					
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
	}
	
	
	/**
	 * 转派
	 * @return
	 */
	public EasyResult actionForToOther(){
		TaskModel model=getModel(TaskModel.class, "task");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			model.set("UPDATE_BY", getUserId());
			
			String[] cc=null;
			JSONObject jsonObject=getJSONObject();
			JSONArray mailtos=jsonObject.getJSONArray("mailtos");
			if(mailtos!=null&&mailtos.size()>0){
				cc=new String[mailtos.size()];
				for(int i=0;i<mailtos.size();i++){
					String userId=mailtos.getString(i);
					try {
						EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
						record.setPrimaryValues(model.getTaskId(),userId);
						record.set("CREATOR", getUserId());
						record.set("CREATE_TIME", EasyDate.getCurrentDateString());
						this.getQuery().save(record);
						cc[i]=userId;
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			String otherUserId=getJsonPara("otherUserId");
			if(StringUtils.notBlank(otherUserId)){
				model.set("ASSIGN_USER_ID", otherUserId);
				model.set("ASSIGN_USER_NAME", StaffService.getService().getUserName(otherUserId));
				model.set("ASSIGN_DEPT_NAME",StaffService.getService().getUserDeptName(otherUserId));
				model.update();
				
				JSONObject task = this.getQuery().queryForRow("select * from yq_task where task_id = ?", new Object[]{model.getTaskId()},new JSONMapperImpl());
				model.setColumns(task);
				
				
				String forwardReason = jsonObject.getString("forwardReason");
				String comment="转派给"+getJsonPara("toOtherName");
				if(StringUtils.notBlank(forwardReason)) {
					comment = comment +"\n"+forwardReason;
				}
				String userName=getJsonPara("userName");
				if(StringUtils.notBlank(comment)){
					CommentService.getService().addComment(getUserId(), getUserName(),model.getTaskId(), comment, WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"),"task");
				}
				MessageModel messageModel=new MessageModel();
				messageModel.setTypeName("task");
				messageModel.setReceiver(otherUserId);
				messageModel.setContents(model.getTaskName());
				messageModel.setTitle("【任务】"+userName+"转派|"+model.getTaskName());
				messageModel.setFkId(model.getTaskId());
				messageModel.setSender(getUserId());
				messageModel.setSendName(getUserName());
				messageModel.setMsgState(1);
				messageModel.setMsgType(2001);
				messageModel.setSenderType(2);
				messageModel.setCc(cc);
				messageModel.setData(task);
				messageModel.setTplName("newTask.html");
				try {
					this.getQuery().executeUpdate("update yq_task t1,"+Constants.DS_MAIN_NAME+".easi_dept t2 ,"+Constants.DS_MAIN_NAME+".easi_dept_user t3 set t1.assign_dept_id=t2.DEPT_ID where t1.assign_user_id=t3.USER_ID and t2.DEPT_ID=t3.DEPT_ID and t1.assign_user_id=t3.USER_ID and t1.TASK_ID = ?",model.getTaskId());
					WxMsgService.getService().sendNewTaskMsg(messageModel);
					MessageService.getService().sendMsg(messageModel);
					this.addBaseUrl(task, messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
				
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"转派成功.");
	}
	
	public EasyResult actionForUpdate(){
		TaskModel model=getModel(TaskModel.class, "task");
		model.set("update_time", EasyDate.getCurrentDateString());
		model.set("update_by", getUserId());

		String assignUserId = model.getString("ASSIGN_USER_ID");
		if(StringUtils.notBlank(assignUserId)) {
			model.set("ASSIGN_USER_NAME",StaffService.getService().getUserName(assignUserId));
			model.set("ASSIGN_DEPT_ID",StaffService.getService().getStaffInfo(assignUserId).getDeptId());
			model.set("ASSIGN_DEPT_NAME",StaffService.getService().getUserDeptName(assignUserId));
		}
		
		try {
			JSONArray array=model.getJSONArray("NOTICE_TYPES");
			model.remove("NOTICE_TYPES");
			if(array!=null&&array.size()>0){
				model.put("NOTICE_TYPES",StringUtils.join(array.toArray(), ","));
			}
			JSONObject jsonObject=getJSONObject();
			JSONArray mailtos=jsonObject.getJSONArray("mailtos");
			if(mailtos!=null&&mailtos.size()>0){
				this.getQuery().executeUpdate("delete from yq_cc where FK_ID = ?", model.getTaskId());
				for(int i=0;i<mailtos.size();i++){
					String usertId=mailtos.getString(i);
					EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
					record.setPrimaryValues(model.getTaskId(),usertId);
					record.set("CREATOR", getUserId());
					record.set("CREATE_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
				}
			}
			model.update();
			String comment=jsonObject.getString("comment");
			if(StringUtils.notBlank(comment)){
				CommentService.getService().addComment(getUserId(),getUserName(),model.getTaskId(), comment, WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"),"task");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"修改成功.");
	}
	
	public EasyResult actionForSaveComment(){
		JSONObject jsonObject=getJSONObject();
		CommentModel model=new CommentModel();
		model.setPrimaryValues(RandomKit.uuid());
		model.setFkId(jsonObject.getString("fkId"));
		model.setContent(jsonObject.getString("content"));
		model.setCreator(getUserId());
		model.setCreateTime(EasyDate.getCurrentDateString());
		model.setUserAgent(getRequest().getHeader("user-agent"));
		model.setIp(WebKit.getIP(getRequest()));
		return CommentService.getService().addComment(model);
	}
	
	public EasyResult actionForDelayTask(){
		JSONObject jsonObject = getJSONObject();
		String date=jsonObject.getString("date");
		String bdate=jsonObject.getString("bdate");
		String taskId=jsonObject.getString("taskId");
		String reason=jsonObject.getString("reason");
		try {
			this.getQuery().executeUpdate("update yq_task set deadline_at = ?,delay_count = delay_count + 1 where task_id = ?", date,taskId);
			
			CommentModel model=new CommentModel();
			model.setFkId(taskId);
			model.setPrimaryValues(RandomKit.uuid());
			model.setContent("【任务】修改了任务截止时间,由"+bdate+"改为"+date+"\n"+reason);
			model.setCreator(getUserId());
			model.setCreateName(getUserName());
			model.setCreateTime(EasyDate.getCurrentDateString());
			model.setUserAgent(getRequest().getHeader("user-agent"));
			model.setIp(WebKit.getIP(getRequest()));
		    CommentService.getService().addComment(model);
		    
		    EasyRecord taskDelay = new EasyRecord("yq_task_delay","delay_id");
		    taskDelay.set("delay_id",model.getPrimaryValue());
		    taskDelay.set("task_id",taskId);
		    taskDelay.set("old_plan_time",bdate);
		    taskDelay.set("new_plan_time",date);
		    taskDelay.set("reason",reason);
		    taskDelay.set("user_id",getUserId());
		    taskDelay.set("dept_id",getDeptId());
		    taskDelay.set("user_name",getUserName());
		    taskDelay.set("create_time",EasyDate.getCurrentDateString());
		    this.getQuery().save(taskDelay);
		    
		    JSONObject task = this.getQuery().queryForRow("select * from yq_task where task_id = ?", new Object[]{taskId},new JSONMapperImpl());
		    
		    String groupId = task.getString("GROUP_ID");
		    if(StringUtils.notBlank(groupId)) {
		    	this.getQuery().executeUpdate("UPDATE yq_project_plan t3 LEFT JOIN( SELECT t2.group_id, COUNT(1) AS delay_count FROM yq_task_delay t1 JOIN yq_task t2 ON t1.task_id = t2.task_id WHERE t2.task_state IN (10, 20) AND t2.group_id = ? GROUP BY t2.group_id) t ON t3.plan_id = t.group_id SET t3.task_delay_count = COALESCE(t.delay_count, 0)",groupId);
		    }
		    
			MessageModel messsage=new MessageModel();
			messsage.setTitle("【任务】延期#"+task.getString("TASK_NAME"));
			String creator = task.getString("CREATOR");
			messsage.setReceiver(creator);
			messsage.setSender(getUserId());
			messsage.setDesc(model.getContent());
			messsage.setData3(getUserName());
			messsage.setData4(task.getString("TASK_NAME"));
			messsage.setFkId(taskId);
			messsage.setTplName("taskComment.html");
			messsage.setUrl("/yq-work/task/"+taskId);
			
			//发给自己领导
			StaffModel staffModel = getStaffInfo();
			if(!creator.equals(staffModel.getSuperior())) {
				messsage.setCc(new String[] {staffModel.getSuperior()});
			}
			
			this.sendWxMsg(getFullUserName()+"申请任务延期【"+task.getString("TASK_NAME")+"】",model.getContent());
			WeChatWebhookSender.sendTextMessage("dept@"+getDeptId(),getFullUserName()+"申请任务延期【"+task.getString("TASK_NAME")+"】\n"+model.getContent());
		
			WxMsgService.getService().sendCommentTaskMsg(messsage);
			EmailService.getService().sendTaskCommentEmail(messsage);
			
	    	return 	EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForReturnTask(){
		JSONObject jsonObject = getJSONObject();
		String taskId=jsonObject.getString("taskId");
		String reason=jsonObject.getString("reason");
		try {
			this.getQuery().executeUpdate("update yq_task set TASK_STATE = 11 , DEADLINE_AT = '' where task_id = ?",taskId);
			
			CommentModel model=new CommentModel();
			model.setFkId(taskId);
			model.setPrimaryValues(RandomKit.uuid());
			model.setContent("【任务】退回任务,\n"+reason);
			model.setCreator(getUserId());
			model.setCreateTime(EasyDate.getCurrentDateString());
			model.setUserAgent(getRequest().getHeader("user-agent"));
			model.setIp(WebKit.getIP(getRequest()));
			CommentService.getService().addComment(model);
			
			
			JSONObject task = this.getQuery().queryForRow("select * from yq_task where task_id = ?", new Object[]{taskId},new JSONMapperImpl());
			MessageModel messsage=new MessageModel();
			messsage.setTitle("【任务】留言#"+task.getString("TASK_NAME"));
			messsage.setReceiver(task.getString("CREATOR"));
			messsage.setSender(getUserId());
			messsage.setDesc(model.getContent());
			messsage.setFkId(taskId);
			messsage.setTplName("taskComment.html");
			EmailService.getService().sendTaskCommentEmail(messsage);
			
			
			return 	EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForDelTask(){
		String taskId=getJsonPara("taskId");
		try {
			if(StringUtils.isBlank(taskId)) {
				return EasyResult.fail();
			}
			this.getQuery().executeUpdate("delete from  yq_oplog where fk_id = ?", taskId);
			this.getQuery().executeUpdate("delete from  yq_cc where fk_id = ?", taskId);
			this.getQuery().executeUpdate("delete from  yq_look_log where fk_id = ?", taskId);
			this.getQuery().executeUpdate("delete from  yq_files where fk_id = ?", taskId);
			this.getQuery().executeUpdate("delete from  yq_task where task_id = ?", taskId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForCannelShare(){
		String taskId=getJsonPara("TASK_ID");
		try {
			this.getQuery().executeUpdate("update yq_task set task_auth = 1 where task_id = ?", taskId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"取消成功!");
	}
	
	public EasyResult actionForShare(){
		String taskId=getJsonPara("TASK_ID");
		try {
			this.getQuery().executeUpdate("update yq_task set task_auth = 0 where task_id = ?", taskId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"分享成功!");
	}
	
	public void actionForTaskDetail(){
		redirect(getRequest().getContextPath()+"/pages/task/task-detail.jsp?isDiv=0&taskId="+getPara("taskId"));
	}
	
	public EasyResult actionForNoticeTask(){
		JSONObject object=getJSONObject();
		TaskNoticeService.getService().noticeTask(object.getString("taskId"), object.getString("title"), object.getString("content"));
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateProgress(){
		JSONObject object=getJSONObject();
		try {
			this.getQuery().executeUpdate("update yq_task set progress = ? where task_id = ?",object.getString("progress"), object.getString("taskId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForNoticeMsg(){
		String type=getJsonPara("type");
		new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					if("overtimeTask".equals(type)){
						TaskNoticeService.getService().overtimeTask();
					}else if("waitTask".equals(type)){
						TaskNoticeService.getService().waitTask();
					}else if("checkTask".equals(type)){
						TaskNoticeService.getService().checkTask();
					}
				} catch (Exception e) {
					Thread.currentThread().interrupt();
					getLogger().error(e.getMessage(),e);
				}
			}
		}).start();
		return EasyResult.ok();
	}
	

	
	public JSONObject actionForAddGroup() {
		EasyRecord record = new EasyRecord("yq_task_group","group_id");
		try {
			record.setColumns(getJSONObject("group"));
			record.setPrimaryValues(RandomKit.uniqueStr(10, true));
			record.set("create_time", EasyDate.getCurrentDateString());
			record.set("creator", getUserId());
			this.getQuery().save(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public JSONObject actionForUpdateGroup() {
		EasyRecord record = new EasyRecord("yq_task_group","group_id");
		try {
			record.setColumns(getJSONObject("group"));
			this.getQuery().update(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public JSONObject actionForDelGroup() {
		String groupId = getJsonPara("groupId");
		try {
			this.getQuery().executeUpdate("update yq_task set group_id ='' where group_id = ?", groupId);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public void actionForToDeptTaskStat() {
		EasySQL sql = new EasySQL("select assign_dept_id,assign_dept_name,count(1) count,");
		sql.append("count(distinct(project_id)) as project_num");
		sql.append("from yq_task where 1=1");
		String day = getPara("day");
		if(StringUtils.notBlank(day)) {
			int _day = Integer.valueOf(day)*-1;
			sql.append(DateUtils.getPlanMaxDay(_day),"and date_id>= ?");
		}else {
			sql.append(DateUtils.getPlanMaxDay(-90),"and date_id>= ?");
		}
		sql.append("group by assign_dept_id having count>5 ORDER BY project_num desc");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			setAttr("list", list);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		renderJsp("/pages/project/project-dept-stat.jsp");
	}
	
	public EasyResult actionForSetBugIds() {
		JSONObject params = getJSONObject();
		String taskId = params.getString("taskId");
		String bugIds = params.getString("bugIds");
		try {
			String _bugIds = this.getQuery().queryForString("select bug_ids from yq_task where task_id = ?", taskId);
			if(StringUtils.notBlank(_bugIds)) {
				_bugIds = _bugIds+",";
			}
			bugIds = _bugIds + bugIds;
			
			List<String> listStr = Arrays.asList(bugIds.split(","));
			List<String> filltrList = listStr.stream().distinct().collect(Collectors.toList());
			
		    StringBuffer sb = new StringBuffer();
	        for(int i=0;i<=filltrList.size()-1;i++){
	            if(i<filltrList.size()-1){
	                sb.append(filltrList.get(i) + ",");
	            }else {
	                sb.append(filltrList.get(i));
	            }
	        }
			this.getQuery().executeUpdate("update yq_task set bug_ids = ? where task_id = ?",sb.toString(),taskId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public JSONObject actionForTags() {
		JSONObject result = new JSONObject();
		result.put("code", 200);
		result.put("msg", "");
		
		List<String> list = new ArrayList<String>();
		list.add("接口");
		list.add("报表");
		list.add("表单");
		list.add("其他");
		
		result.put("data",list.toArray());
		
		return result;
	}
	
	private void sendWxMsg(String title,String msg) {
    	WeChatWebhookSender.sendMarkdownMessage(WebhookKey.TASK,title,msg);
    }
	
	private void addBaseUrl(JSONObject taskData,MessageModel messageModel) {
		if(Constants.isProd()){
			String desc = taskData.getString("TASK_DESC");
			if(StringUtils.notBlank(desc)){
				desc = desc.replaceAll("https://work.yunqu-info.cn","");
				desc = desc.replaceAll("/yq-work/files","https://work.yunqu-info.cn/yq-work/files");
				taskData.put("TASK_DESC", desc);
				messageModel.setData(taskData);
			}
		}	
	}
}





