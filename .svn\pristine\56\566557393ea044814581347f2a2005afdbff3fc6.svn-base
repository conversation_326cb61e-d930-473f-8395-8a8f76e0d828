package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.utils.TaskUtils;
import com.yunqu.work.utils.WeekUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * <AUTHOR>
 */
@WebObject(name = "IncomeStageDao")
public class IncomeStageDao extends AppDaoContext {

    @WebControl(name = "incomeStageList", type = Types.LIST)
    public JSONObject incomeStageList() {
        EasySQL sql = getEasySQL("select t1.* from yq_contract_income_stage t1");
        sql.append("where 1=1");
        sql.append(param.getString("contractId"), " and t1.CONTRACT_ID = ?");
        sql.append(param.getString("stageName"), " and t1.STAGE_NAME like ?");
        setOrderBy(sql, " order by t1.CREATE_TIME ");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "record", type = Types.RECORD)
    public JSONObject record() {
        String incomeStageId = param.getString("incomeStage.INCOME_STAGE_ID");
        if (StringUtils.notBlank(incomeStageId)) {
            return queryForRecord("select * from yq_contract_income_stage where INCOME_STAGE_ID = ?", incomeStageId);
        } else {
            return getJsonResult(new JSONObject());
        }
    }

    @WebControl(name = "selectAllIncomeStage", type = Types.DICT)
    public JSONObject selectAllIncomeStage() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("select INCOME_STAGE_ID, CONCAT(PLAN_COMP_DATE,'-',STAGE_NAME, '-比例:',RATIO,' % -计划金额: ', PLAN_AMOUNT) AS STAGE_DESC from yq_contract_income_stage where 1=1");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and CONTRACT_ID = ?");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "selectIncomeStage", type = Types.DICT)
    public JSONObject selectIncomeStage() {
        String contractId = param.getString("contractId");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        String confirmType = param.getString("confirmType");
        EasySQL sql = new EasySQL("select INCOME_STAGE_ID, CONCAT(PLAN_COMP_DATE,'-',STAGE_NAME, '-比例:',RATIO,' % -计划金额(含税): ', PLAN_AMOUNT,'-税后:',AMOUNT_NO_TAX ");
        if ("A".equals(confirmType)) {
            sql.append(",'--剩余未确认(含税)A：',UNCONFIRM_AMOUNT_A,'-税后:',UNCONFIRM_NO_TAX_A");
        } else if ("B".equals(confirmType)) {
            sql.append(",'--剩余未确认(含税)B：',UNCONFIRM_AMOUNT_B,'-税后:',UNCONFIRM_NO_TAX_B");
        }
        sql.append(") AS STAGE_DESC from yq_contract_income_stage t1 where 1=1");
        sql.append(contractId, "and CONTRACT_ID = ?");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "allIncomeStageList", type = Types.LIST)
    public JSONObject allIncomeStageList() {
        EasySQL sql = getEasySQL("SELECT t2.CONTRACT_NAME,t2.CUST_ID,t2.CONTRACT_NO,t1.*");
        sql.append("FROM yq_contract_income_stage t1");
        sql.append("LEFT JOIN yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("where 1=1");
        if("due".equals(param.getString("stageType"))){
            sql.append(getDateAfter7Days(),"and t1.PLAN_COMP_DATE < ?");
            sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"),"and t1.PLAN_COMP_DATE >= ?");
            sql.append("and  INCOME_CONF_FLAG = 0 ");
        }
        if("month".equals(param.getString("stageType"))){
            sql.append(EasyDate.getCurrMonthBeginDay("yyyy-MM-dd"),"and t1.PLAN_COMP_DATE >= ?");
            sql.append(EasyDate.getCurrMonthEndDay("yyyy-MM-dd"),"and t1.PLAN_COMP_DATE <= ?");
            sql.append("and  INCOME_CONF_FLAG = 0 ");
        }
        if("over".equals(param.getString("stageType"))){
            sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"),"and PLAN_COMP_DATE <= ? ");
            sql.append("and  INCOME_CONF_FLAG = 0 ");
        }
        if("unConfirm".equals(param.getString("stageType"))){
            sql.append("and  INCOME_CONF_FLAG = 0 ");
        }
        if("confirm".equals(param.getString("stageType"))){
            sql.append("and  INCOME_CONF_FLAG = 1 ");
        }
        if("timeout".equals(param.getString("stageType"))){
            sql.append("and  INCOME_CONF_FLAG = 1 ");
            sql.append("and ACT_COMP_DATE > PLAN_COMP_DATE ");
        }
        sql.appendLike(param.getString("contractNo"),"and t2.CONTRACT_NO like ?");
        sql.appendLike(param.getString("contractName"), "and t2.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t2.CUSTOMER_NAME like ?");
        if("year".equals(param.getString("dateField"))){
            sql.append(param.getString("yearId"), "and t1.YEAR_ID = ?");
        }else if("month".equals(param.getString("dateField"))){
            sql.append(param.getString("monthId"), "and t1.MONTH_ID = ?");
        }

        if("user".equals(param.getString("source"))) {
            sql.append(getUserId(),"and  t2.SALES_BY = ? ");
        }

        sql.append("order by t1.PLAN_COMP_DATE DESC ");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name="incomeConfirmStat1",type=Types.RECORD)
    public JSONObject incomeConfirmStat1(){
        EasySQL sql=getEasySQL("select ");
        sql.append(" SUM(AMOUNT_NO_TAX) AS SUM_ALL_1,");
        sql.append(EasyCalendar.newInstance().getYear(),"SUM(CASE WHEN YEAR_ID = ? THEN AMOUNT_NO_TAX ELSE 0 END) AS SUM_YEAR_1,");
        sql.append(EasyCalendar.newInstance().getFullMonth()," SUM(CASE WHEN MONTH_ID = ? THEN AMOUNT_NO_TAX ELSE 0 END) AS SUM_MONTH_1,");
        sql.append(WeekUtils.getEndDayOfWeekNo(WeekUtils.getYear(), WeekUtils.getWeekNo()),"SUM(CASE WHEN CONFIRM_DATE <= ? ");
        sql.append(WeekUtils.getStartDayOfWeekNo(WeekUtils.getYear(), WeekUtils.getWeekNo())," AND CONFIRM_DATE >= ? THEN AMOUNT_NO_TAX ELSE 0 END) AS SUM_WEEK_1,");
        sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"),"SUM(CASE WHEN CONFIRM_DATE = ? THEN AMOUNT_NO_TAX ELSE 0 END) AS SUM_TODAY_1");
        sql.append("from yq_contract_income_confirm t1 ");
        if("user".equals(param.getString("source"))){
            sql.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        }
        sql.append("where 1=1 ");
        sql.append("and CONFIRM_TYPE = 'A'");
        if("user".equals(param.getString("source"))) {
            sql.append(getUserId(),"and  t2.SALES_BY = ? ");
        }
        JSONObject result =queryForRecord(sql.getSQL(), sql.getParams());
        JSONObject data = result.getJSONObject("data");
        String[] keys=new String[]{"SUM_ALL_1","SUM_YEAR_1","SUM_MONTH_1","SUM_WEEK_1","SUM_TODAY_1"};
        for(int i=0;i<keys.length;i++){
            if(StringUtils.isBlank(data.getString(keys[i]))){
                data.put(keys[i],"0");
            }
        }
        result.put("data", data);
        return result;
    }

    @WebControl(name="incomeStageCountStat2",type=Types.RECORD)
    public JSONObject incomeStageCountStat2(){
        EasySQL sql=getEasySQL("select ");
        sql.append(" COUNT(*) AS COUNT_ALL_2,");
        sql.append(" SUM(UNCONFIRM_NO_TAX_A) AS SUM_ALL_2,");
        sql.append(EasyCalendar.newInstance().getYear(),"SUM(CASE WHEN YEAR_ID = ? THEN 1 ELSE 0 END) AS COUNT_YEAR_2,");
        sql.append(EasyCalendar.newInstance().getYear(),"SUM(CASE WHEN YEAR_ID = ? THEN UNCONFIRM_NO_TAX_A ELSE 0 END) AS SUM_YEAR_2,");
        sql.append(EasyCalendar.newInstance().getFullMonth()," SUM(CASE WHEN MONTH_ID = ? THEN 1 ELSE 0 END) AS COUNT_MONTH_2,");
        sql.append(EasyCalendar.newInstance().getFullMonth()," SUM(CASE WHEN MONTH_ID = ? THEN UNCONFIRM_NO_TAX_A ELSE 0 END) AS SUM_MONTH_2,");
        sql.append(getDateAfter7Days(),"SUM(CASE WHEN PLAN_COMP_DATE <= ? ");
        sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd")," AND PLAN_COMP_DATE > ? THEN 1 ELSE 0 END) AS COUNT_DUE_2,");
        sql.append(getDateAfter7Days(),"SUM(CASE WHEN PLAN_COMP_DATE <= ? ");
        sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd")," AND PLAN_COMP_DATE > ? THEN UNCONFIRM_NO_TAX_A ELSE 0 END) AS SUM_DUE_2,");
        sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"),"SUM(CASE WHEN PLAN_COMP_DATE <= ? THEN 1 ELSE 0 END) AS COUNT_OVER_2,");
        sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"),"SUM(CASE WHEN PLAN_COMP_DATE <= ? THEN UNCONFIRM_NO_TAX_A ELSE 0 END) AS SUM_OVER_2");
        sql.append("from yq_contract_income_stage t1 ");
        if("user".equals(param.getString("source"))){
            sql.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        }
        sql.append("where 1=1");
        sql.append("and  t1.INCOME_CONF_FLAG = 0 ");
        if("user".equals(param.getString("source"))) {
            sql.append(getUserId(),"and  t2.SALES_BY = ? ");
        }

        JSONObject result =queryForRecord(sql.getSQL(), sql.getParams());
        JSONObject data = result.getJSONObject("data");
        String[] keys=new String[]{"COUNT_ALL_2","SUM_ALL_2","COUNT_YEAR_2","SUM_YEAR_2","COUNT_MONTH_2","SUM_MONTH_2","COUNT_DUE_2","SUM_DUE_2","COUNT_OVER_2","SUM_OVER_2"};
        for(int i=0;i<keys.length;i++){
            if(StringUtils.isBlank(data.getString(keys[i]))){
                data.put(keys[i],"0");
            }
        }
        result.put("data", data);
        return result;
    }


    public String getDateAfter7Days() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }



    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by t1.").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }
}
