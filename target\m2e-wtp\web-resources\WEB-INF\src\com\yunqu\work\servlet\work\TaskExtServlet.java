package com.yunqu.work.servlet.work;

import java.io.File;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.Globals;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.TaskUtils;

@WebServlet("/servlet/task/ext/*")
public class TaskExtServlet extends AppBaseServlet{
	  
	private static final long serialVersionUID = 1L;
	
	private static JSONObject taskNames=new JSONObject();
    
	static{
		taskNames.put("10", "待办");
		taskNames.put("11", "已退回");
		taskNames.put("12", "已暂停");
		taskNames.put("13", "已取消");
		taskNames.put("20", "进行中");
		taskNames.put("30", "已完成");
		taskNames.put("40", "已验收");
		taskNames.put("42", "验收不通过");
	}

		public void actionForExportTask(){
			String _data = getPara("data");
			JSONObject param = JSONObject.parseObject(_data);
			Map<String,String> userNames = getUsers();
			Map<String,String> taskTypes = getTaskType();
			List<String> headers=new ArrayList<String>();
			try {
				int status=param.getIntValue("status");
				EasySQL sql=null;
				if(status==1){//我负责的任务
					sql=new EasySQL("select t1.*,t2.PROJECT_NAME,t2.PROJECT_NO from yq_task t1  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
					TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
					sql.append(getUserId(),"and t1.ASSIGN_USER_ID = ?",true);
				}else if(status==2){
					sql=new EasySQL("select t1.*,t2.PROJECT_NAME,t2.PROJECT_NO from yq_task t1  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
					TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
					sql.append(getUserId(),"and t1.CREATOR = ?",true);
				}else if(status==3){
					sql=new EasySQL("select t1.*,t3.create_time cc_time,t2.PROJECT_NAME,t2.PROJECT_NO from yq_task t1 INNER JOIN yq_cc t3 on t3.fk_id=t1.task_id  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
					TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
					sql.append(getUserId(),"and t3.user_id = ?",true);
				}else if(status==4){
					//共享任务
					sql=new EasySQL("select t1.* from yq_task t1  where 1=1");
					sql.append(Constants.TASK_OK,"and t1.task_state >= ? and t1.task_auth = 0");
					TaskUtils.setTaskCondition(param, getDeptId(),sql);
				}else {
					sql = new EasySQL("select t1.*,t2.PROJECT_NAME,t2.PROJECT_NO from YQ_TASK t1 left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID where 1=1");
					TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
				}
				String field=param.getString("field");
				String order=param.getString("order");
				if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
					sql.append("order by").append(field).append(order);
				}else{
					sql.append("order by t1.CREATE_TIME desc");
				}
				
				File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
				/**创建头部*/
				headers.add("录单日期");
				headers.add("标题");
				headers.add("描述");
				headers.add("任务类型");
				headers.add("项目编号");
				headers.add("所属项目");
				headers.add("所属模块");
				headers.add("标签");
				headers.add("等级");
				headers.add("发起人");
				headers.add("发起部门");
				headers.add("负责人");
				headers.add("负责部门");
				headers.add("开始时间");
				headers.add("结束时间");
				headers.add("难度");
				headers.add("标签");
				headers.add("延期次数");
				headers.add("任务时长(天)");
				headers.add("计划耗时(小时)");
				headers.add("实际耗时(小时)");
				headers.add("完成时间");
				headers.add("进度(%)");
				headers.add("状态");
				
				List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
				int x=0;
				for(String header:headers){
					ExcelHeaderStyle style=new ExcelHeaderStyle();
					style.setData(header);
					if(x==1){
						style.setWidth(8000);
					}else if(x==2){
						style.setWidth(12000);
					}else {
						style.setWidth(4500);
					}
					style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
					styles.add(style);
					x++;
				}
				/**数据***/
				List<List<String>> excelData=new ArrayList<List<String>>();
				EasyQuery query=this.getQuery();
				query.setMaxRow(50000);
				
				List<JSONObject> data = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
				
				if(data!=null && data.size()>0){
					for (int i = 0; i < data.size(); i++) {
						JSONObject map = data.get(i);
						List<String> list=new ArrayList<String>();
						list.add(map.getString("CREATE_TIME"));
						list.add(map.getString("TASK_NAME"));
						list.add(Jsoup.clean(map.getString("TASK_DESC"),Whitelist.none()).replaceAll("&nbsp;"," "));
						list.add(taskTypes.get(map.getString("TASK_TYPE_ID")));
						list.add(map.getString("PROJECT_NO"));
						list.add(map.getString("PROJ_NAME"));
						list.add(map.getString("MODULE_NAME"));
						list.add(map.getString("TASK_TAGS"));
						list.add(map.getString("TASK_LEVEL"));
						list.add(userNames.get(map.getString("CREATOR")));
						list.add(map.getString("DEPT_NAME"));
						list.add(userNames.get(map.getString("ASSIGN_USER_ID")));
						list.add(map.getString("ASSIGN_DEPT_NAME"));
						list.add(map.getString("PLAN_STARTED_AT"));
						list.add(map.getString("DEADLINE_AT"));
						list.add(map.getString("TASK_DIFFCULTY"));
						list.add(map.getString("TASK_TAGS"));
						list.add(map.getString("DELAY_COUNT"));
						list.add(""+betweenDays(map.getString("PLAN_STARTED_AT"),map.getString("DEADLINE_AT")));
						list.add(map.getString("PLAN_WORK_HOUR"));
						list.add(map.getString("WORK_HOUR"));
						list.add(map.getString("FINISH_TIME"));
						list.add(map.getString("PROGRESS"));
						list.add(taskNames.getString(map.getString("TASK_STATE")));
						
						excelData.add(list);
					}
				}
				
				ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
				String fileName="任务清单列表.xlsx";
				renderFile(file,fileName,true);
			} catch (SQLException ex) {
				this.error(ex.getMessage(), ex);
			} catch (Exception e) {
				this.error(e.getMessage(), e);
			}
		}
		
		
		public void actionForUpload(){
			try {
				Part part = getFile("file");
				if(part==null||part.getSize()==0){
					 renderJson(EasyResult.fail("请选择文件"));
					 return;
				}
				String fileName = new String(getFilename(part).getBytes(), "UTF-8");
				Workbook workbook = WorkbookFactory.create(part.getInputStream());
				 //临时保存起来
				 FileKit.saveToFile(part.getInputStream(),getTempFilePath()+System.currentTimeMillis()+"_"+fileName);
				part.delete();
				
				int excelFieldCount = 5;
				List<List<String>> list = new ArrayList<>();
		        Sheet sheet = workbook.getSheetAt(0);
		        int maxLine =sheet.getLastRowNum();
		        int lastCellNum=0;
		        for (int ii = 0; ii <= maxLine; ii++) {
		            List<String> rows = new ArrayList<>();
		            Row row = sheet.getRow(ii);
		            if(ii==0){
		            	 lastCellNum=row.getLastCellNum();
		            	 if(lastCellNum!=excelFieldCount){
		            		 this.info("模板不匹配",null);
		            		 renderJson(EasyResult.fail("标准模板字段"+excelFieldCount+"个，您上传的模板字段"+lastCellNum+"个，不匹配!"));
		            		 return; 
		            	 }
		            }
		            if(row!=null&&ii>0){
		            	for(int j=0;j<lastCellNum;j++){
		            		Cell cell=row.getCell(j);
		            		if(cell!=null){
		            			String val = Utils.getCellValue(cell);
		            			if(StringUtils.isBlank(val)){
		            				rows.add("");
		            			}else{
		            				rows.add(val);
		            			}
		            		}else{
		            			rows.add("");
		            		}
		            	}
		            	list.add(rows);
		            }
		        }
		        String projectId = getPara("projectId");
		        String projectName = getPara("projectName");
		        String _assignUserId = getPara("assignUserId");
		        String taskTypeId = getPara("taskTypeId");
		        if(StringUtils.isBlank(taskTypeId)) {
		        	taskTypeId = "8";
		        }
		        
		        int successCount = 0;
		        EasyRecord record =new EasyRecord("YQ_TASK","TASK_ID");
		        for(int i =0,len=list.size();i<len;i++){
		        	List<String> row = list.get(i);
		        	String taskName = row.get(0);
		        	String assignUserName = row.get(1);
		        	String planStartedAt = row.get(2);
		        	String deadlineAt = row.get(3);
		        	String taskDesc = row.get(4);
		        	if(StringUtils.isBlank(taskName)) {
		        		continue;
		        	}
		        	record.set("TASK_NAME",taskName);
		        	if(StringUtils.isBlank(assignUserName)) {
		        		assignUserName =  StaffService.getService().getUserName(_assignUserId);
		        	}
		        	record.set("ASSIGN_USER_NAME",assignUserName);
		        	String assignUserId = StaffService.getService().getUserId(assignUserName);
		        	if(StringUtils.notBlank(assignUserId)){
		        		record.set("ASSIGN_USER_ID",assignUserId);
		        		StaffModel staffModel =  StaffService.getService().getStaffInfo(assignUserId);
		        		record.set("ASSIGN_DEPT_ID",staffModel.getDeptId());
		        		record.set("ASSIGN_DEPT_NAME",staffModel.getDeptName());
		        	}else {
		        		continue;
		        	}
		        	record.set("PLAN_STARTED_AT",DateUtils.getFormatDate(planStartedAt)+" 09:00");
		        	deadlineAt = DateUtils.getFormatDate(deadlineAt);
		        	if(deadlineAt==null) {
		        		continue;
		        	}
		        	record.set("DEADLINE_AT",deadlineAt+" 18:00");
		        	
		        	record.set("TASK_DESC",taskDesc);
		        	record.set("PROJECT_ID",projectId);
		        	record.set("PROJ_NAME",projectName);
		        	
		        	record.set("TASK_ID", RandomKit.uniqueStr());
		        	record.set("TASK_LEVEL", 2);
		        	record.set("TASK_DIFFICULTY", "中等");
		        	record.set("TASK_TYPE_ID", taskTypeId);
		        	record.set("CREATOR", getUserId());
		        	record.set("CREATE_NAME", getUserName());
		        	record.set("DEPT_ID", getDeptId());
		        	record.set("DEPT_NAME", getDeptName());
		        	record.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
		        	record.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
		        	record.set("CREATE_TIME", EasyDate.getCurrentDateString());
		        	record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		        	record.set("TASK_STATE", 10);
		        	getQuery().save(record);
		        	successCount = successCount+1;
		        }
		        renderJson(EasyResult.ok(null,"导入"+list.size()+"条，成功"+successCount+"条"));
		        return;
			 }catch (Exception e) {
				this.error(e.getMessage(), e);
				renderJson(EasyResult.fail("模板数据有误:"+e.getMessage()));
				return;
			}
		  }

          private Map<String,String> getUsers() {
            Map<String,String> map=new HashMap<String,String>();
            try {
                List<EasyRow> list=this.getMainQuery().queryForList("select USERNAME,USER_ID FROM EASI_USER");
                if(list!=null){
                    for(EasyRow row:list){
                        map.put(row.getColumnValue("USER_ID"),row.getColumnValue("USERNAME"));
                    }
                }
            } catch (SQLException e) {
                this.getLogger().error(e);
            }
            return map;
        }

        private Map<String,String> getTaskType() {
            Map<String,String> map=new HashMap<String,String>();
            try {
                List<EasyRow> list=this.getQuery().queryForList("select task_type_id,task_type_name FROM yq_task_type");
                if(list!=null){
                    for(EasyRow row:list){
                        map.put(row.getColumnValue("task_type_id"),row.getColumnValue("task_type_name"));
                    }
                }
            } catch (SQLException e) {
                this.getLogger().error(e);
            }
            return map;
        }

          public long betweenDays(String dateStr1,String dateStr2){
            if(StringUtils.isBlank(dateStr1)||StringUtils.isBlank(dateStr2)) {
                return 0;
            }
            // 获取日期
            Date date1 = parseDate(dateStr1, "yyyy-MM-dd HH:mm");
            Date date2 = parseDate(dateStr2, "yyyy-MM-dd HH:mm");
    
            // 获取相差的天数
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date1);
            long timeInMillis1 = calendar.getTimeInMillis();
            calendar.setTime(date2);
            long timeInMillis2 = calendar.getTimeInMillis();
    
            long betweenDays =  (timeInMillis2 - timeInMillis1) / (1000L*3600L*24L);
            return betweenDays;
        }

        	
        public static Date parseDate(String dateStr, String pattern){
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            Date date;
            try {
                date = sdf.parse(dateStr);
            } catch (ParseException e) {
                throw  new RuntimeException("日期转化错误");
            }

            return date;
        }
	 
	
        public static String getTempFilePath(){
            String dir = Globals.TMP_DIR +File.separator+"taskExcel"+FileKit.getFileDir();
            File file=new File(dir);
            if(!file.exists()){
                file.mkdirs();
            }
            return dir;
        }

        private String getFilename(Part part) {  
            String contentDispositionHeader = part.getHeader("content-disposition");  
            String[] elements = contentDispositionHeader.split(";");  
            for (String element : elements) {  
                if (element.trim().startsWith("filename")) {  
                    return element.substring(element.indexOf('=') + 1).trim().replace("\"", "");  
                }  
            }  
            return null;  
       }
	  
}
