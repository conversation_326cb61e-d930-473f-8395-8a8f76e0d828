<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>菜单</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="FoodDao.foodObj" autocomplete="off" data-mars-prefix="food.">
     		   <input type="hidden" value="${param.foodId}" name="food.ID"/>
						<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td style="width: 80px" class="required">用餐类型</td>
				                    <td>
				                    	<label class="radio radio-success radio-inline">
					                  	    <input type="radio" value="2" name="food.DINNER_TYPE"/> <span>中餐</span>
				                    	</label>
				                    	<label class="radio radio-success radio-inline">
					                    	<input type="radio" value="3" checked="checked" name="food.DINNER_TYPE"/> <span>晚餐</span>
				                    	</label>
				                    </td>
					            </tr>
					            
					            <tr>
				                    <td style="width: 80px" class="required">菜单名称</td>
				                    <td><input data-rules="required"  type="text" name="food.FOOD_NAME" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td style="width: 80px" class="required">供应商</td>
				                    <td><input data-rules="required"  type="text" name="food.SUPPLIER" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td class="required">价格</td>
				                    <td>
				                    	<div class="input-group input-group-sm">
					                        <input value="15" data-rules="required"  type="number" name="food.FOOD_PRICE" class="form-control input-sm">
											 <span class="input-group-addon">元</span>	
								    	 </div>
				                    </td>
					            </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c">
						    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		Edit.foodId='${param.foodId}';
		
		$(function(){
			$("#editForm").render();
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.foodId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/food?action=addFood",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/food?action=updateFood",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		};
		Edit.del=function(data){
			layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/food?action=delFood",{'id':Edit.foodId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							list.query();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>