<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
		.layui-table td, .layui-table th{padding: 9px 6px;}
		.layui-table input{width: 100%!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="editForm">
			<input type="hidden" name="projectId" value="${param.projectId}"/>
			<div class="ibox">
				<div class="ibox-title mb-15 clearfix">
	          		 <div class="form-group">
	          		       <h5><span class="glyphicon glyphicon-inbox"></span> 项目工作量统计（月）</h5>
					       <div class="input-group input-group-sm"  style="width: 150px;">
								<span class="input-group-addon">开始日期</span> 
								<input name="startDate" autocomplete="off" data-mars="CommonDao.prevMonthId" data-mars-reload="false" id="startDate" data-mars-top="true" data-laydate="{type:'month'}" class="form-control input-sm Wdate">
							</div>
							<div class="input-group input-group-sm"  style="width: 150px;">
								<span class="input-group-addon">结束日期</span> 
								<input name="endDate" autocomplete="off" data-mars="CommonDao.prevMonthId" data-mars-reload="false" id="endDate" data-mars-top="true" data-laydate="{type:'month'}" class="form-control Wdate">
							</div>
							<div class="input-group input-group-sm">
							 	<button type="button" class="btn btn-sm btn-default" onclick="loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						   </div>
	          	     </div>
	              </div> 
	             <div class="ibox-content">
				    <div data-mars="ProjectDao.projectWorkhourDetail" data-template="detailTpl" data-container="container" class="layui-row" id="container"></div>
	             </div>
				<script type="text/x-jsrender" id="detailTpl">
					<table class="layui-table">
					  <thead>
						<tr>
						  <th>部门</th>
						  <th>姓名</th>
						  <th>工作月</th>
						  <th>工作量(人月)</th>
						  <th>填报者</th>
						  <th>填写时间</th>
						</tr> 
					  </thead>
					  <tbody>
					  {{for data}}
						<tr>
						  <td> {{:DEPTS}}</td>
						  <td>{{:USERNAME}}</td>
						  <td>{{:MONTH_ID}}</td>
						  <td>{{:WORK_TIME/100}}</td>
						  <td>{{call:CREATOR fn='getUserName'}}</td>
						  <td>{{:CREATE_TIME}}</td>
						</tr>
					  {{/for}}
					   {{if data.length==0}}<tr><td  colspan="6">暂无数据</td></tr>{{/if}}
					  </tbody>
					</table>
						
				</script>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		function loadData(){
			$("#editForm").render({success:function(){
				renderDate();
			}});
		}
		$(function(){
			//设置默认月份
			loadData();
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>