<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>平台</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="PlatformDao.record" autocomplete="off" data-mars-prefix="platform.">
     		   <input type="hidden" value="${param.platformId}" name="platform.PLATFORM_ID"/>
     		   <input type="hidden" value="${param.projectId}" name="platform.PROJECT_ID"/>
						<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td style="width: 80px" class="required">平台名称</td>
				                    <td><input data-rules="required"  type="text" name="platform.PLATFORM_NAME" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td class="required">负责人</td>
				                    <td>
				                    	<select id="users" size="1" data-rules="required" data-mars="CommonDao.userDict" name="platform.OWNER" class="form-control input-sm">
				                    		<option value="">请选择</option>
				                    	</select>
				                    </td>
				                </tr>
					            <tr class="hidden">
				                    <td class="required">关联机房</td>
				                    <td>
				                    	<select size="1" data-rules="required" id="selectServerRoomId" onchange="$('#hosts').render({data:{serverRoomId:this.value}})" data-mars="ServerRoomDao.dict" name="platform.SERVER_ROOM_ID" class="form-control input-sm">
				                    	</select>
				                    </td>
				                </tr>
					            <tr  class="hidden">
				                    <td class="required">关联主机</td>
				                    <td id="hosts">
				                    	<select multiple="multiple" size="1" id="selectHosts" data-rules="required" name="hosts" class="form-control input-sm">
				                    	</select>
				                    </td>
				                </tr>
					            <tr>
				                    <td class="required">关联服务</td>
				                    <td>
				                    	<select multiple="multiple" size="1" data-rules="required" data-mars="ServiceDao.dict" name="services" class="form-control input-sm">
				                    	</select>
				                    </td>
				                </tr>
					            <tr>
				                    <td style="width: 80px" class="required">所在地点</td>
				                    <td><input data-rules="required"  type="text" name="platform.PLATFORM_ADDRESS" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td style="width: 80px" class="required">访问链接</td>
				                    <td><input  type="text" name="platform.PLATFORM_URL" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td class="required">平台状态</td>
				                    <td>
				                    	 <label class="radio radio-success radio-inline">
					                    	<input type="radio" checked="checked" value="1" name="platform.platform_state"/> <span>运行中</span>
				                    	</label>
					                    	 <label class="radio radio-success radio-inline">
				                    	<input type="radio" value="2" name="platform.platform_state"/> <span>暂停运行</span>
				                    	 </label>
				                    	 <label class="radio radio-success radio-inline">
					                    	<input type="radio" value="3" name="platform.platform_state"/> <span>已关闭</span>
				                    	 </label>
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required">备注</td>
				                    <td>
				                       <div id="editor"></div>
			                           <textarea id="wordText" data-text="false" style="height: 500px;width:400px;display: none;" class="form-control input-sm" name="platform.PLATFORM_DESC"></textarea>
				                    
				                    </td>
					            </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c">
						    	  <button type="button" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("Edit");
		Edit.platformId='${param.platformId}';
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.config.menus = [
           		    'code',  // 代码
           		    'head',  // 标题
           		    'bold',  // 粗体
           		    'fontSize',  // 字号
           		    'fontName',  // 字体
           		    'foreColor',  // 文字颜色
           		    'link',  // 插入链接
           		    'list',  // 列表
           		    'justify',  // 对齐方式
           		    'quote',  // 引用
           		    'emoticon',  // 表情
           		    'image',  // 插入图片
           		    'table',  // 表格'
        ];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.config.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.create();
		
		$(function(){
			$("#editForm").render({success:function(result){
				$("#selectHosts").attr("data-mars","HostDao.dict");
				$('#hosts').render({
					data:{serverRoomId:$("#selectServerRoomId").val()},
					success:function(){
						requreLib.setplugs('select2',function(){
							var record=result['PlatformDao.record'];
							if(typeof record.data != 'string'){
								var hosts=record.hosts;
								var services=record.services;
								$("[name='hosts']").val(hosts);
								$("[name='services']").val(services);
							}
							$("#users,#selectServerRoomId").select2({theme: "bootstrap"});
							$("[multiple='multiple']").select2({theme: "bootstrap"});
						});
					}
				});
				 editor.txt.html($("#wordText").val());
				 $(".w-e-text-container").css("height","120px");
			}});
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.platformId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/platform?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/platform?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>