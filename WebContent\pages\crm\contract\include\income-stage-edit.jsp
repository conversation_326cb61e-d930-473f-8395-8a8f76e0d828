<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title></title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="incomeStageEditForm" class="form-horizontal" data-mars="IncomeStageDao.record" autocomplete="off" data-mars-prefix="incomeStage.">
        <SELECT style="display: none" id="paymentInfo" name="paymentInfo"></SELECT>

        <input type="hidden" value="${param.incomeStageId}" name="incomeStage.INCOME_STAGE_ID"/>
        <input type="hidden" value="${param.contractId}" name="incomeStage.CONTRACT_ID"/>
<%--        <input type="hidden" name="oldContractStageIds"/>--%>
<%--        <input type="hidden" name="oldAmount"/>--%>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td class="required">阶段名称</td>
                <td>
                    <select name="incomeStage.STAGE_NAME" data-rules="required" class="form-control">
                        <option value="初验">初验</option>
                        <option value="终验">终验</option>
                        <option value="维护">维护</option>
                        <option value="分成">分成</option>
                        <option value="上线">上线</option>
                        <option value="阶段">阶段</option>
                    </select>
                </td>
                <td class="required">计划完成日期</td>
                <td>
                    <input data-rules="required" type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="incomeStage.PLAN_COMP_DATE"  class="form-control input-sm Wdate">
                </td>
            </tr>
<%--            <tr>--%>
<%--                <td>填写模式</td>--%>
<%--                <td colspan="3">--%>
<%--                    <label class="radio radio-inline radio-success">--%>
<%--                        <input type="radio" value="1" name="incomeStage.WRITE_MODE" checked="checked"  <c:if test="${!empty param.isConfirm}"> disabled </c:if> onclick="toggleType(1)"><span>合同阶段对应模式</span>--%>
<%--                    </label>--%>
<%--                    <label class="radio radio-inline radio-success">--%>
<%--                        <input type="radio" value="2" name="incomeStage.WRITE_MODE" <c:if test="${!empty param.isConfirm}"> disabled </c:if> onclick="toggleType(2)"> <span>手动填写模式</span>--%>
<%--                    </label>--%>
<%--                </td>--%>
<%--            </tr>--%>
<%--            <tr id="selectContractStage">--%>
<%--                <td>对应合同阶段</td>--%>
<%--                <td colspan="3">--%>
<%--                    <input type="hidden" name="incomeStage.CONTRACT_STAGE_ID" class="form-control input-sm">--%>
<%--                    <button type="button" name="contractStageButton" onclick="incomeStageSelectStage()" class="btn btn-primary btn-sm"> 选择合同阶段</button>--%>
<%--                    <button type="button" name="contractStageButtonDisabled" style="display: none" disabled class="btn btn-primary btn-sm"> 已收入确认不可修改</button>--%>
<%--                    <br><span name="contractStageSpan">已选合同阶段：</span>--%>
<%--                </td>--%>
<%--            </tr>--%>

            <tr id="selectContractStage">
                <td>对应合同款项</td>
                <td colspan="3">
                    <input type="hidden" name="incomeStage.PAYMENT_ID" class="form-control input-sm">
                    <button type="button" name="paymentButton" onclick="incomeStageSelectPayment()" class="btn btn-primary btn-sm"> 选择合同款项</button>
                    <br><span name="paymentSpan">已选合同款项：</span>
                </td>
            </tr>
            <tr>
                <td class="required">计划收款金额(含税)</td>
                <td>
                    <input type="text" name="incomeStage.PLAN_AMOUNT" data-rules="required" class="form-control input-sm"
<%--                           oninput="IncomeStageEdit.calcRatio(this.value)"--%>
                            readonly placeholder="选择款项自动计算"
                    >
                </td>
                <td class="required">计划收款金额(税后)</td>
                <td>
                    <input type="text" name="incomeStage.AMOUNT_NO_TAX" data-rules="required" class="form-control input-sm"
<%--                           oninput="IncomeStageEdit.calcRatio(this.value)"--%>
                            readonly placeholder="选择款项自动计算"
                    >
                </td>
            </tr>
            <tr>
                <td class="required">占合同总额比例(%)</td>
                <td>
                    <input type="text" name="incomeStage.RATIO" data-rules="required" class="form-control input-sm"
<%--                           oninput="IncomeStageEdit.calcPlanAmt(this.value)"--%>
                           readonly placeholder="选择款项自动计算"
                    >
                </td>
                <td colspan="2"></td>
            </tr>
            <tr>
                <td>信用期（天）</td>
                <td>
                    <input type="text" name="incomeStage.CREDIT_PRD" class="form-control">
                </td>
                <td>预留比例(%)</td>
                <td>
                    <input type="text" name="incomeStage.YULIU_RATIO" class="form-control">
                </td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <input type="text"  name="incomeStage.REMARK" class="form-control">
                </td>
            </tr>
            <tr>
                <td>已审核</td>
                <td colspan="3">
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio"  value="1" name="incomeStage.CHECKED_FLAG"><span>是</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="0" name="incomeStage.CHECKED_FLAG"><span>否</span>
                    </label>
                </td>
            </tr>
            <tr style="display: none">
                <td class="required">硬件已确认</td>
                <td colspan="3">
                    <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                        <input type="radio"  value="1" name="incomeStage.HARDWARE_CONF_FLAG"><span>是</span>
                    </label>
                    <label class="radio radio-info radio-inline">
                        <input type="radio" checked value="0" name="incomeStage.HARDWARE_CONF_FLAG"><span>否</span>
                    </label>
                </td>
                <td colspan="2"></td>
            </tr>
            </tbody>
        </table>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="IncomeStageEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll();"> 关闭 </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("IncomeStageEdit");

        IncomeStageEdit.incomeStageId = '${param.incomeStageId}';
        IncomeStageEdit.isNew = '${param.isNew}';
        IncomeStageEdit.contractId = '${param.contractId}';
        <%--IncomeStageEdit.isConfirm = '${param.isConfirm}';--%>
        <%--IncomeStageEdit.writeMode = '${param.writeMode}';--%>
        IncomeStageEdit.amount = '${param.amount}';
        IncomeStageEdit.paymentIds = '';
        IncomeStageEdit.extendInfo = [];
        IncomeStageEdit.isSelected = false;

        $(function(){
            $("#incomeStageEditForm").render({success:function(result){
                    $('#incomeStageEditForm td').css('min-width', '120px');
                    $('#incomeStageEditForm th').css('min-width', '120px');

                    if(IncomeStageEdit.isNew=='1'){
                        // loadType(1);
                    }else {
                        // loadType(IncomeStageEdit.writeMode);
                        // if(IncomeStageEdit.writeMode == '1')  {
                        //     loadContractStageSelected(result);
                        var incomeRecordData = result["IncomeStageDao.record"] && result["IncomeStageDao.record"].data;
                        if (incomeRecordData && Object.keys(incomeRecordData).length > 0 && incomeRecordData["PAYMENT_ID"] !== "") {
                            // $('input[name="oldContractStageIds"]').val(result["IncomeStageDao.record"]["data"]["CONTRACT_STAGE_ID"]);
                            IncomeStageEdit.paymentIds = result["IncomeStageDao.record"]["data"]["PAYMENT_ID"];
                            // $('input[name="oldAmount"]').val(result["IncomeStageDao.record"]["data"]["PLAN_AMOUNT"]);
                        } else {
                            return;
                        }
                        $('#paymentInfo').attr("data-mars","ContractPaymentDao.paymentInfoForIncomeStage")
                        $('#paymentInfo').render({
                            data: {contractId: IncomeStageEdit.contractId, incomeStageId: IncomeStageEdit.incomeStageId},
                            success: function () {
                                loadPaymentSelected()
                            }
                        })
                    }
                }});
        });

        IncomeStageEdit.ajaxSubmitForm = function(){
            if(form.validate("#incomeStageEditForm")){
                if(IncomeStageEdit.isNew=='1'){
                    IncomeStageEdit.insertData();
                }
                else if(IncomeStageEdit.incomeStageId){
                    IncomeStageEdit.updateData();
                }else{
                    IncomeStageEdit.insertData();
                }
            };
        }

        IncomeStageEdit.insertData = function() {
            var data = form.getJSONObject("#incomeStageEditForm");
            // if(data["incomeStage.WRITE_MODE"] == '2' ){
            //     data["incomeStage.CONTRACT_STAGE_ID"] ='';
            // }
            if(IncomeStageEdit.isSelected){
                data["extend"] = JSON.stringify(IncomeStageEdit.extendInfo);
            }
            ajax.remoteCall("${ctxPath}/servlet/incomeStage?action=add",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadIncomeStageList();
                        // reloadStageList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        IncomeStageEdit.updateData = function() {
            var data = form.getJSONObject("#incomeStageEditForm");
            if(IncomeStageEdit.isSelected) {
                data["extend"] = JSON.stringify(IncomeStageEdit.extendInfo);
            }
            // data['incomeStage.WRITE_MODE'] = $("input[name='incomeStage.WRITE_MODE']").val();
            ajax.remoteCall("${ctxPath}/servlet/incomeStage?action=update",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadIncomeStageList();
                        // reloadStageList();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        function incomeStageSelectPayment(){
            var id = new Date().getTime();
            var contractId = IncomeStageEdit.contractId;
            var incomeStageId = IncomeStageEdit.incomeStageId;
            popup.layerShow({scrollbar:false,area:['880px','500px'],offset:'20px',title:'选择合同款项',url:'${ctxPath}/pages/crm/contract/include/select/payment-select.jsp',data:{sid:id,type:'checkbox',contractId:contractId,incomeStageId:incomeStageId}});
        }

        function loadPaymentSelected(){
            var paymentIds = [];
            // var incomeRecordData = result["IncomeStageDao.record"] && result["IncomeStageDao.record"].data;
            // if (incomeRecordData && Object.keys(incomeRecordData).length > 0 && incomeRecordData["PAYMENT_ID"] !== "") {
            //     // $('input[name="oldContractStageIds"]').val(result["IncomeStageDao.record"]["data"]["CONTRACT_STAGE_ID"]);
            //     console.log("paymentIds");
            //     console.log(paymentIds);
            //     // $('input[name="oldAmount"]').val(result["IncomeStageDao.record"]["data"]["PLAN_AMOUNT"]);
            // } else {
            //     return;
            // }
            paymentIds = IncomeStageEdit.paymentIds.split(",");

            for(var i=0;i<paymentIds.length;i++){
                var newSpan = $('<span name="paymentNames"></span>');
                newSpan.text(getText(paymentIds[i],'paymentInfo'));
                // newSpan.text(paymentIds[i]);
                $("[name='paymentSpan']").after(newSpan);
                $("[name='paymentSpan']").after($('<br>'));
            }
        }
        /*
        function incomeStageSelectStage(){
            var id = new Date().getTime();
            var contractId = IncomeStageEdit.contractId;
            var incomeStageId = IncomeStageEdit.incomeStageId;
            popup.layerShow({full:fullShow(),scrollbar:false,area:['700px','500px'],offset:'20px',title:'选择合同阶段',url:'${ctxPath}/pages/crm/contract/include/select/stage-select.jsp',data:{sid:id,type:'checkbox',contractId:contractId,incomeStageId:incomeStageId}});
        }

        function loadContractStageSelected(result){
            var contractStageIds = [];
            var incomeRecordData = result["IncomeStageDao.record"] && result["IncomeStageDao.record"].data;
            if (incomeRecordData && Object.keys(incomeRecordData).length > 0 && incomeRecordData["CONTRACT_STAGE_ID"] !== "") {
                $('input[name="oldContractStageIds"]').val(result["IncomeStageDao.record"]["data"]["CONTRACT_STAGE_ID"]);
                contractStageIds = result["IncomeStageDao.record"]["data"]["CONTRACT_STAGE_ID"].split(",");
                $('input[name="oldAmount"]').val(result["IncomeStageDao.record"]["data"]["PLAN_AMOUNT"]);
            } else {
                return;
            }

            for(var i=0;i<contractStageIds.length;i++){
                var newSpan = $('<span name="contractStageNames"></span>');
                newSpan.text(getText(contractStageIds[i],'contractStage'));
                $("[name='contractStageSpan']").after(newSpan);
                $("[name='contractStageSpan']").after($('<br>'));
            }
        }

        function StageSelectCallBack(row){
            $("[name='incomeStage.CONTRACT_STAGE_ID']").val(row['CONTRACT_STAGE_ID']);
            $("[name='incomeStage.PLAN_AMOUNT']").val(row['PLAN_RCV_AMT']);
            $("[name='incomeStage.RATIO']").val(row['RATIO']);
                var contractStageNames = [];
                contractStageNames = row['CONTRACT_STAGE_NAME'].split(",");
               for(var i=0;i<contractStageNames.length;i++){
                   var newSpan = $('<span name="contractStageNames"></span>');
                   newSpan.text(contractStageNames[i]);
                   $("[name='contractStageSpan']").after(newSpan);
                   $("[name='contractStageSpan']").after($('<br>'));
               }
        }

        function loadType(type){
            if(type=='1'){
                $('#selectContractStage').show();
                $('input[name="incomeStage.PLAN_AMOUNT"]').attr('readonly',true).attr("placeholder","选择合同阶段后自动计算");
                $('input[name="incomeStage.RATIO"]').attr('readonly',true).attr("placeholder","选择合同阶段后自动计算");
            }else{
                $('#selectContractStage').hide();
                $('input[name="incomeStage.PLAN_AMOUNT"]').attr('readonly',false).attr("placeholder","输入比例自动计算金额").attr("on");
                $('input[name="incomeStage.RATIO"]').attr('readonly',false).attr("placeholder","输入金额自动计算比例");
            }
        }

        function toggleType(type){
            if(type=='1'){
                $('#selectContractStage').show();
                $('input[name="incomeStage.PLAN_AMOUNT"]').val('').attr('readonly',true).attr("placeholder","选择合同阶段后自动计算");
                $('input[name="incomeStage.RATIO"]').val('').attr('readonly',true).attr("placeholder","选择合同阶段后自动计算");
            }else{
                $('#selectContractStage').hide();
                $('input[name="incomeStage.CONTRACT_STAGE_ID"]').val('');
                if($("span[name='contractStageNames']")!=undefined || $("span[name='contractStageNames']") != null){
                    $("span[name='contractStageNames']").remove();
                }
                $('input[name="incomeStage.PLAN_AMOUNT"]').attr('readonly',false).attr("placeholder","输入比例自动计算金额").attr("on");
                $('input[name="incomeStage.RATIO"]').attr('readonly',false).attr("placeholder","输入金额自动计算比例");
            }
        }


        */
        function PaymentSelectCallBack(row) {
            console.log(row);
            $("[name='incomeStage.PAYMENT_ID']").val(row['PAYMENT_ID']);
            $("[name='incomeStage.PLAN_AMOUNT']").val(row['AMOUNT_WITH_TAX']);
            $("[name='incomeStage.AMOUNT_NO_TAX']").val(row['AMOUNT_NO_TAX']);
            IncomeStageEdit.isSelected = true ;
            IncomeStageEdit.extendInfo = row['extendInfo'];
            IncomeStageEdit.calcRatio(row['AMOUNT_WITH_TAX']);
            var paymentNames = [];
            paymentNames = row['PAYMENT_NAME'].split(",");
            for(var i=0;i<paymentNames.length;i++){
                var newSpan = $('<span name="paymentNames"></span>');
                newSpan.text(paymentNames[i]);
                $("[name='paymentSpan']").after(newSpan);
                $("[name='paymentSpan']").after($('<br>'));

            }
        }

        IncomeStageEdit.calcPlanAmt = function (ratio) {
            var ratio = parseFloat(ratio);
            if (isNaN(ratio) || ratio == '') {
                ratio = 0;
            }
            if (ratio > 100) {
                layer.alert("占合同总金额比例不能大于100%", {icon: 5});
                return;
            }
            var amount = parseFloat(IncomeStageEdit.amount);
            var planAmt = amount * ratio / 100;
            $("#incomeStageEditForm input[name='incomeStage.PLAN_AMOUNT']").val(planAmt.toFixed(2))
        }

        IncomeStageEdit.calcRatio = function (planAmt) {
            var planAmt = parseFloat(planAmt);
            if (isNaN(planAmt) || planAmt == '') {
                planAmt = 0;
            }
            var contractAmount = parseFloat(IncomeStageEdit.amount);
            if (planAmt > contractAmount) {
                layer.alert("收入确阶段金额不能大于合同金额", {icon: 5});
                return;
            }
            var ratio = planAmt * 100 / contractAmount;
            $("#incomeStageEditForm input[name='incomeStage.RATIO']").val(ratio.toFixed(2))
        }


    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>