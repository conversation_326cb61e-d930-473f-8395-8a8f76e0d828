package com.yunqu.work.dao.other;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ProjectWeeklyModel;
import com.yunqu.work.model.WeeklyModel;
import com.yunqu.work.service.LookLogService;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.WeekUtils;

@WebObject(name="WeeklyDao")
public class WeeklyDao extends AppDaoContext {
	
	private static final String default_tpl_id="default_tpl";
	
	@WebControl(name = "getCurrentWeekBeginDate",type = Types.TEXT)
	public JSONObject getCurrentWeekBeginDate() {
		return getJsonResult(WeekUtils.getStartDayOfWeekNo(WeekUtils.getYear(), WeekUtils.getWeekNo()));
	}
	
	@WebControl(name = "getCurrentWeekEndDate",type = Types.TEXT)
	public JSONObject getCurrentWeekEndDate() {
		return getJsonResult(WeekUtils.getEndDayOfWeekNo(WeekUtils.getYear(), WeekUtils.getWeekNo()));
	}
	
	@WebControl(name = "taskDesc",type = Types.TEXT)
	public JSONObject taskDesc() {
		StringBuffer sb = new StringBuffer();
		EasySQL easySQL=getEasySQL("select ");
		easySQL.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
		easySQL.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state < ? THEN 1 ELSE 0 END) AS g,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS h,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
		easySQL.append(40,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS d,");
		easySQL.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
		easySQL.append("from YQ_TASK t1 where 1=1");
		easySQL.append(getUserId(),"and t1.assign_user_id = ?");
		
		JSONObject result = queryForRecord(easySQL.getSQL(), easySQL.getParams());
		JSONObject data = result.getJSONObject("data");
		
		int a = data.getIntValue("A");
		int b = data.getIntValue("B");
		int c = data.getIntValue("E");
		if(a==0&&b==0&&c==0) {
			return getJsonResult("");
		}
		sb.append("本周任务汇总").append("：");
		if(a>0) {
			sb.append("待办("+a).append(")，");
		}
		if(b>0) {
			sb.append("进行中("+b).append(")，");
		}
		if(c>0) {
			sb.append("逾期数("+c).append(")。");
		}
		return getJsonResult(sb.toString());
	}
	
	
	
	@WebControl(name = "weeklyDown",type = Types.LIST)
	public JSONObject weeklyDown() {
		EasySQL sql = new EasySQL("select t2.username create_name,count(1) count from yq_weekly t1 right join "+Constants.DS_MAIN_NAME+".easi_user t2 on  t1.creator = t2.user_id where t2.state = 0");
		if(!isSuperUser()) {
			sql.appendIn(getMgrDeptArray(),"and t1.dept_id");
		}
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and t1.week_begin >= ?");
			sql.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		sql.append("GROUP BY t1.creator order by count limit 80");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name = "nowYearTop",type = Types.LIST)
	public JSONObject nowYearTop() {
		EasySQL sql = new EasySQL("select t2.username create_name,count(1) count from yq_weekly t1 right join "+Constants.DS_MAIN_NAME+".easi_user t2 on  t1.creator = t2.user_id where t2.state = 0");
		if(!isSuperUser()) {
			sql.appendIn(getMgrDeptArray(),"and t1.dept_id");
		}
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and t1.week_begin >= ?");
			sql.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		sql.append("GROUP BY t1.creator order by count desc limit 80");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "weeklyNumTop",type = Types.LIST)
	public JSONObject weeklyNumTop() {
		EasySQL sql = new EasySQL("select t1.year,t1.week_no,t2.week_begin,t2.week_end,count(1) count from yq_weekly t1,yq_weekly_date t2 where t1.year= t2.year and t1.week_no = t2.week_no");
		if(!isSuperUser()) {
			sql.appendIn(getMgrDeptArray(),"and t1.dept_id");
		}
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and t1.week_begin >= ?");
			sql.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		sql.append("GROUP BY t1.week_no order by t1.`year` desc,t1.week_no desc limit 20");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="getProjectRecentWeek",type=Types.TEMPLATE)
	public JSONObject getProjectRecentWeek(){
		JSONArray recentWeek=WeekUtils.getRecentWeek(WeekUtils.getYear(),52," 周报");
		return getJsonResult(recentWeek);
	}
	
	@WebControl(name="getProjectRecentWeekPage",type=Types.TEMPLATE)
	public JSONObject getProjectRecentWeekPage(){
		JSONArray recentWeek=WeekUtils.getRecentWeek(WeekUtils.getYear(),52," 周报");
		JSONObject emptyPage = emptyPage();
		emptyPage.put("data",recentWeek);
		emptyPage.put("totalRow",recentWeek.size());
		return emptyPage;
	}
	
	@WebControl(name="weeklyStat",type=Types.LIST)
	public JSONObject weeklyStat(){
		EasySQL sql=getEasySQL("select t2.DEPT_NAME,t1.* from yq_weekly_stat t1 INNER JOIN  "+Constants.DS_MAIN_NAME+".easi_dept t2 on t1.dept_id=t2.DEPT_ID where 1=1");
		sql.append(param.getString("deptId"),"and t2.dept_id = ?");
		sql.append(param.getString("year"),"and t1.year = ?");
		sql.append(param.getString("weeklyNo"),"and t1.weekly_no = ?");
		sql.append("order by year desc,weekly_no desc,t1.weekly_count desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="weeklyProject",type=Types.TEMPLATE)
	public JSONObject weeklyProject(){
		EasySQL sql=getEasySQL("select t1.* from yq_weekly_project t1 where 1=1");
		String weeklyId = param.getString("weeklyId");
		sql.append(weeklyId,"and t1.weekly_id = ?");
		sql.append("order by item_index");
		if(StringUtils.isBlank(weeklyId)) {
			return getJsonResult(new JSONArray());
		}
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="weeklyProjectList",type=Types.TEMPLATE)
	public JSONObject weeklyProjectList(){
		EasySQL sql=getEasySQL("select t1.*,t3.project_name,t3.project_no,t2.work_content,t2.plan_work_content,t2.work_day from yq_weekly t1,yq_weekly_project t2,yq_project t3 where t1.weekly_id = t2.weekly_id and t3.project_id = t2.project_id");
		String projectId = param.getString("projectId");
		sql.append(projectId, "and t2.project_id = ?");
		sql.appendLike(param.getString("projectName"), "and t2.project_name like ?");
		sql.appendLike(param.getString("userName"),"and t1.create_name like ?");
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and t1.week_begin >= ?");
			sql.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		
		String startDate = param.getString("startDate");
		String endDate = param.getString("endDate");
		if(StringUtils.notBlank(startDate)){
			sql.append(startDate+"-01 00:00:00","and t1.create_time >= ?");
		}
		if(StringUtils.notBlank(endDate)){
			sql.append(endDate+"-31 23:59:59","and t1.create_time <= ?");
		}
		
		boolean hasAuth = hasWeekyAuth();
		if(!hasAuth&&StringUtils.isBlank(projectId)){
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,t1.cc_ids)");
			sql.append("or");
			sql.append(getUserId(),"t1.creator = ?");
			sql.append("or");
			sql.append(getUserId(),"t1.receiver = ?");
			sql.append(")");
		}
		if(!hasAuth&&StringUtils.notBlank(projectId)) {
			sql.append("9999999999999", "and t2.project_id <> ?");
		}
		
		sql.append(0,"and t1.status <> ?");
		sql.append("order by t1.send_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="weeklyProjectListStat",type=Types.TEMPLATE)
	public JSONObject weeklyProjectListStat(){
		EasySQL sql=getEasySQL("select t3.work_people_count,t3.workhour_day,t3.BEGIN_DATE,t3.PO_NAME,t3.PROJECT_PO_NAME,t3.CY_DATE,t3.PROJECT_STATE,t3.project_no,t3.project_id,t3.project_name,count(1) count,GROUP_CONCAT(DISTINCT(t1.create_name)) user_names,count(DISTINCT(t1.create_name)) people_count,sum(t2.work_day) work_day from yq_weekly t1,yq_weekly_project t2,yq_project t3 where t1.weekly_id = t2.weekly_id and t3.project_id = t2.project_id");
		sql.append(param.getString("projectId"), "and t2.project_id = ?");
		sql.appendLike(param.getString("projectName"), "and t2.project_name like ?");
		sql.appendLike(param.getString("userName"),"and t1.create_name like ?");
		sql.appendLike(param.getString("depts"),"and t1.dept_name like ?");
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and t1.week_begin >= ?");
			sql.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		String source = param.getString("source");
		if(!hasWeekyAuth()&&!"manger".equals(source)){
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,t1.cc_ids)");
			sql.append("or");
			sql.append(getUserId(),"t1.creator = ?");
			sql.append("or");
			sql.append(getUserId(),"t1.receiver = ?");
			sql.append(")");
		}
		sql.append(0,"and t1.status <> ?");
		sql.append("GROUP BY t3.project_id");
		if("manger".equals(source)) {
			sql.append("order by work_day desc");
		}else {
			sql.append("order by t1.send_time desc");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectRecord",type=Types.RECORD)
	public JSONObject projectRecord(){
		ProjectWeeklyModel model=new ProjectWeeklyModel();
		model.setColumns(getParam("weekly"));
		JSONObject result=queryForRecord(model);
		if(StringUtils.notBlank(model.getWeeklyId())){
			String receiver=result.getJSONObject("data").getString("RECEIVER");
			String creator=result.getJSONObject("data").getString("CREATOR");
			if(!getUserId().equals(creator)){
				LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getWeeklyId(),"weekly",request);
			}
			if(getUserId().equals(receiver)){
				try {
					//设置为已阅读
					this.getQuery().executeUpdate("update yq_project_weekly set status = ? where weekly_id =? ", 2,model.getWeeklyId());
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}
		
		if(StringUtils.notBlank(model.getWeeklyId())){
			String sql="select USER_ID from  yq_cc where FK_ID = ?";
			try {
				String v1 = this.getQuery().queryForString("select GROUP_CONCAT(t1.USERNAME) from "+Constants.DS_MAIN_NAME+".easi_user t1 where t1.user_id in(select t2.USER_ID from yq_cc t2 where t2.FK_ID = ?)", model.getPrimaryValue());
				result.getJSONObject("data").put("mailtoName", v1);
				
				result.getJSONObject("data").put("projectName", this.getQuery().queryForString("select t1.project_name from yq_project t1,yq_project_weekly t2 where t1.project_id = t2.project_id and t2.weekly_id= ?", model.getPrimaryValue()));
				List<EasyRow> list = this.getQuery().queryForList(sql, model.getPrimaryValue());
				if(list!=null&&list.size()>0){
					String[] mailtos=new String[list.size()];
					for(int i=0;i<list.size();i++){
						mailtos[i]=list.get(i).getColumnValue(1);
					}
					result.put("mailtos",mailtos);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		return result;
	}
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		WeeklyModel model=new WeeklyModel();
		model.setColumns(getParam("weekly"));
		String tplId=default_tpl_id;
		JSONObject result=queryForRecord(model);
		if(StringUtils.notBlank(model.getWeeklyId())){
			String receiver=result.getJSONObject("data").getString("RECEIVER");
			String creator=result.getJSONObject("data").getString("CREATOR");
		     tplId=result.getJSONObject("data").getString("TPL_ID");
			if(!getUserId().equals(creator)){
				LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getWeeklyId(),"weekly",request);
			}
			if(getUserId().equals(receiver)){
				try {
					//设置为已阅读
					this.getQuery().executeUpdate("update yq_weekly set status = ? where weekly_id =? ", 2,model.getWeeklyId());
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}
		
		try {
		 JSONObject tplRow=this.getQuery().queryForRow("select * from yq_weekly_tpl where tpl_id = ?",new Object[]{tplId} ,new JSONMapperImpl());
		 result.put("tplRow", tplRow);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		/*try {
			String weeklyType=result.getJSONObject("data").getString("WEEKLY_TYPE");
			if("1".equals(weeklyType)){
				List<JSONObject> projectWeekly=this.getQuery().queryForList("select * from yq_project_weekly where weekly_id = ? and creator = ?",new Object[]{model.getWeeklyId(),getUserId()} ,new JSONMapperImpl());
				result.put("projectWeekly", projectWeekly);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}*/
		return result;
	}
	
	@WebControl(name="getRecentWeek15",type=Types.TEMPLATE)
	public JSONObject getRecentWeek15(){
		JSONArray getRecentWeek=WeekUtils.getRecentWeek(WeekUtils.getYear());
		return getJsonResult(getRecentWeek);
	}
	@WebControl(name="getRecentWeek",type=Types.TEMPLATE)
	public JSONObject getRecentWeek(){
		JSONArray getRecentWeek=WeekUtils.getRecentWeek(param.getIntValue("year"),6," 周报");
		return getJsonResult(getRecentWeek);
	}
	
	@WebControl(name="weeklyFeedback",type=Types.LIST)
	public JSONObject weeklyFeedback(){
		EasySQL sql = getEasySQL("select t1.* from YQ_WEEKLY t1 where 1=1");
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and week_begin >= ?");
			sql.append(updateDateArray[1],"and week_begin <= ?");
		}
		sql.append("and LENGTH(item_3)>15");
		sql.append("order by year desc,week_no desc");
		JSONObject result=queryForPageList(sql.getSQL(), sql.getParams());
		return result;
	}
	
	@WebControl(name="projectWeeklyStat",type=Types.LIST)
	public JSONObject projectWeeklyStat(){
		EasySQL sql = getEasySQL("SELECT t2.USERNAME,t2.DEPTS,count(1) count,ROUND(sum(t1.work_day)) work_day,max(t3.create_time) last_fill_time,t1.work_content from yq_weekly_project t1,yq_main.easi_user t2,yq_weekly t3 where t1.creator = t2.USER_ID and t3.weekly_id = t1.weekly_id");
		sql.append(param.getString("projectId"),"and t1.project_id = ?");
		sql.append("GROUP BY t1.creator ORDER BY work_day desc");
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		return result;
	}
	
	@WebControl(name="projectWeeklyDeptStat",type=Types.LIST)
	public JSONObject projectWeeklyDeptStat(){
		EasySQL sql = getEasySQL("SELECT t1.dept_name,ROUND(sum(t2.work_day)) work_day from yq_weekly t1,yq_weekly_project t2 where t1.weekly_id = t2.weekly_id");
		sql.append(param.getString("projectId"),"and t2.project_id = ? ");
		sql.append("GROUP BY t1.dept_name ORDER BY work_day desc");
		JSONObject result = queryForList(sql.getSQL(), sql.getParams());
		return result;
	}
	
	@WebControl(name="myProjectWeeklys",type=Types.PAGE)
	public JSONObject myProjectWeeklys(){
		EasySQL easySQL=getEasySQL("select  t.* from ");
		easySQL.append("(select t2.project_name,t2.project_no,t4.PIC_URL,t4.USERNAME,t4.DEPTS,t1.*,1 as TYPE from yq_project_weekly t1, yq_project t2,"+Constants.DS_MAIN_NAME+".easi_user t4 where 1=1");
		easySQL.append("and t2.project_id = t1.project_id");
		easySQL.append("and t4.USER_ID=t1.CREATOR");
		if(!hasWeekyAuth()){
			easySQL.append(getUserId(),"and t1.RECEIVER = ?");
			
		}
		String status = param.getString("status");
		if(StringUtils.isBlank(status)) {
			easySQL.append(0,"and t1.status <> ?");
		}else {
			easySQL.append(status,"and t1.status = ?");
		}
		
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			easySQL.append(updateDateArray[0],"and t1.week_begin >= ?");
			easySQL.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		
		easySQL.appendLike(param.getString("projectName"),"and t2.PROJECT_NAME like ?");
		easySQL.append(param.getString("weekNo"),"and t1.WEEK_NO = ?");
		easySQL.append(param.getString("year"),"and t1.YEAR = ?");
		easySQL.appendLike(param.getString("userName"),"and t4.USERNAME like ?");
		easySQL.appendLike(param.getString("depts"),"and t4.DEPTS like ?");
		
		if(!hasWeekyAuth()){
			easySQL.append("union select t4.project_name,t4.project_no,t5.PIC_URL,t5.USERNAME,t5.DEPTS,t3.*,2 as TYPE from yq_project_weekly t3,yq_cc t2, yq_project t4,"+Constants.DS_MAIN_NAME+".easi_user t5 where t3.weekly_id=t2.fk_id ");
			easySQL.append("and t5.USER_ID=t2.USER_ID");
			easySQL.append("and t3.project_id = t4.project_id");
			if(StringUtils.isBlank(status)) {
				easySQL.append(0,"and t3.status <> ?");
			}else {
				easySQL.append(status,"and t3.status = ?");
			}
			easySQL.append(getUserId(),"and t2.USER_ID = ?");
			easySQL.appendLike(param.getString("projectName"),"and t4.PROJECT_NAME like ?");
			
			if(StringUtils.notBlank(updateDate)) {
				String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
				easySQL.append(updateDateArray[0],"and t3.week_begin >= ?");
				easySQL.append(updateDateArray[1],"and t3.week_begin <= ?");
			}
		}
		easySQL.append(" ) t where 1=1 ");
		
		easySQL.append("order by t.send_time desc");
		JSONObject result=queryForPageList(easySQL.getSQL(), easySQL.getParams());
		return result;
	}
	
	
	@WebControl(name="myWeeklyList",type=Types.LIST)
	public JSONObject myWeeklyList(){
//		String maxWeeky = EasyCalendar.newInstance().getYear()+String.valueOf(WeekUtils.getWeekNo());
		String minWeeky = "0";
		try {
			minWeeky =  this.getQuery().queryForString("select min(year * 100 + week_no) from yq_weekly where creator = ?", getUserId());
			if(StringUtils.isBlank(minWeeky)) {
				minWeeky = "0";
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		EasySQL easySQL=getEasySQL("select t1.*");
		easySQL.append(",t2.year _year,t2.week_no _week_no");
		easySQL.append(",CONCAT(t2.`year`,' - ',t2.week_no,'周',' (',t2.week_begin,' ~ ',t2.week_end,') ','周报') _title");
		easySQL.append("from (select b1.*,sum(work_day) work_day,count(1) project_num from yq_weekly b1 left join yq_weekly_project b2 on b1.weekly_id = b2.weekly_id where 1=1");
		easySQL.append(getUserId(),"and b1.creator = ? group by b1.weekly_id) t1 ");
		easySQL.append("RIGHT JOIN yq_weekly_date t2 on t1.year = t2.year and t1.week_no = t2.week_no where 1=1");
//		easySQL.append(maxWeeky,"and concat(t2.year,t2.week_no) <= ?");
		easySQL.append(DateUtils.getPlanMaxDay(4),"and t2.week_end <= ?");
		
		int minWeekNumber = Integer.parseInt(minWeeky);
		easySQL.append(minWeekNumber,"and (t2.year * 100 + t2.week_no) >= ?");
		easySQL.append("order by t2.year desc,t2.week_no desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	
	
	@WebControl(name="queryDeptWeekly",type=Types.TEMPLATE)
	public JSONObject queryDeptWeekly(){
		String deptId=param.getString("deptId");
		int isSelfDept = param.getIntValue("isSelfDept");
		if(StringUtils.isBlank(deptId)){
			deptId = getMgrDeptIds();
		}
		if(StringUtils.isBlank(deptId)){
			return getJsonResult(null);
		}else{
			EasySQL sql=new EasySQL("select t1.USER_ID,t1.USERNAME,t1.DEPTS,t3.* from "+Constants.DS_MAIN_NAME+".easi_user t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_dept_user t2 on t1.USER_ID = t2.USER_ID LEFT JOIN (select b1.* from yq_weekly b1 where  b1.`status` > 0");
			sql.append(param.getString("weekNo"),"and b1.WEEK_NO = ?",false);
			sql.append(param.getString("year"),"and b1.YEAR = ?",false);
			sql.append(" ) t3 on t3.creator = t1.USER_ID where t1.STATE = 0 ");
			
			String selectPeopleType = param.getString("selectPeopleType");
			String depts = param.getString("depts");
			if(StringUtils.notBlank(depts)) {
				sql.appendLike(param.getString("depts"), "and t1.depts like ?");
			}else if(StringUtils.notBlank(selectPeopleType)){
				sql.append("and (");
				String[] array = selectPeopleType.split(",");
				int i = 0;
				for(String str:array) {
					if(i>0) {
						sql.append("or");
					}
					sql.appendLike(str, "t1.depts like ?");
					i++;
				}
				sql.append(")");
			}
			
			
			sql.appendLike(param.getString("userName"), "and t1.username like ?");
			if((!hasWeekyAuth()&&!isSuperUser())||isSelfDept==1){
				sql.appendIn(deptId.split(","),"and t2.dept_id");
			}
			sql.append("order by t3.send_time desc,t1.depts");
		    return queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		}
	}
	
	@WebControl(name="myReceiveWeeklyList",type=Types.TEMPLATE)
	public JSONObject myReceiveWeeklyList(){
		EasySQL easySQL=getEasySQL("select t4.PIC_URL,t4.USERNAME,t4.DEPTS,t1.*,1 as TYPE from YQ_WEEKLY t1,"+Constants.DS_MAIN_NAME+".easi_user t4");
		String projectId = param.getString("projectId");
		if(StringUtils.notBlank(projectId)) {
			easySQL.append(",yq_weekly_project t2");
		}
		easySQL.append("where 1=1");
		if(StringUtils.notBlank(projectId)) {
			easySQL.append("and t1.weekly_id = t2.weekly_id");
		}
		easySQL.append("and t4.USER_ID=t1.CREATOR");
		if(!hasWeekyAuth()){
			easySQL.append("and (");
			easySQL.append(getUserId(),"find_in_set(?,t1.cc_ids)");
			easySQL.append("or");
			easySQL.append(getUserId(),"t1.receiver = ?");
			if(isDeptLeader()) {
				easySQL.append("or");
				easySQL.appendIn(getMgrDeptArray(),"t1.dept_id");
			}
			easySQL.append(")");
		}
		String status = param.getString("status");
		if(StringUtils.isBlank(status)) {
			easySQL.append(0,"and t1.status <> ?");
		}else {
			easySQL.append(status,"and t1.status = ?");
		}
		
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			easySQL.append(updateDateArray[0],"and t1.week_begin >= ?");
			easySQL.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		easySQL.append(param.getString("projectId"), "and t2.project_id = ?");
		easySQL.append(param.getString("weekNo"),"and t1.WEEK_NO = ?");
		easySQL.append(param.getString("year"),"and t1.YEAR = ?");
		easySQL.appendLike(param.getString("userName"),"and t4.USERNAME like ?");
		easySQL.appendLike(param.getString("depts"),"and t4.DEPTS like ?");
		if(StringUtils.notBlank(projectId)) {
			easySQL.append("group by t1.weekly_id");
		}
		easySQL.append("order by t1.send_time desc");
		return queryForPageList(easySQL.getSQL(), easySQL.getParams());
	}
	
	@WebControl(name="deptYearProjectWeeklyStat",type=Types.TEMPLATE)
	public JSONObject deptYearProjectWeeklyStat(){
		EasySQL sql=getEasySQL("select t3.work_people_count,t3.workhour_day,t3.BEGIN_DATE,t3.PO_NAME,t3.PROJECT_PO_NAME,t3.CY_DATE,t3.PROJECT_STATE,t3.project_no,t3.project_id,t3.project_name,count(1) count,GROUP_CONCAT(DISTINCT(t1.create_name)) user_names,count(DISTINCT(t1.create_name)) people_count,sum(t2.work_day) work_day from yq_weekly t1,yq_weekly_project t2,yq_project t3 where t1.weekly_id = t2.weekly_id and t3.project_id = t2.project_id");
		sql.appendIn(getMgrDeptArray(),"and t1.dept_id");
		sql.append(EasyCalendar.newInstance().getYear(),"and t1.year = ?");
		sql.append(0,"and t1.status <> ?");
		sql.append("GROUP BY t3.project_id");
		sql.append("order by work_day desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="myYearProjectWeeklyStat",type=Types.TEMPLATE)
	public JSONObject myYearProjectWeeklyStat(){
		EasySQL sql=getEasySQL("select t3.work_people_count,t3.workhour_day,t3.BEGIN_DATE,t3.PO_NAME,t3.PROJECT_PO_NAME,t3.CY_DATE,t3.PROJECT_STATE,t3.project_no,t3.project_id,t3.project_name,count(1) count,GROUP_CONCAT(DISTINCT(t1.create_name)) user_names,count(DISTINCT(t1.create_name)) people_count,sum(t2.work_day) work_day from yq_weekly t1,yq_weekly_project t2,yq_project t3 where t1.weekly_id = t2.weekly_id and t3.project_id = t2.project_id");
		sql.append(getUserId(),"and t1.creator = ?");
		String date = param.getString("date");
		if(StringUtils.notBlank(date)) {
			sql.append(DateUtils.getBeforeDate(Integer.valueOf(date),"yyyyyMMdd"),"and t1.week_begin >= ?");
		}else {
			sql.append(EasyCalendar.newInstance().getYear(),"and t1.year = ?");
		}
		sql.append(0,"and t1.status <> ?");
		sql.append("GROUP BY t3.project_id");
		sql.append("order by work_day desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="weekyIndexStat",type=Types.RECORD)
	public JSONObject weekyIndexStat(){
		JSONObject result = new JSONObject();
		EasySQL baseSql = new EasySQL();
		baseSql.append("SELECT count(DISTINCT(t2.project_id)) from yq_weekly t1,yq_weekly_project t2 where t1.weekly_id = t2.weekly_id");
		
		try {
			EasySQL sql1 = new EasySQL();
			sql1.append(baseSql.getSQL());
			sql1.appendIn(getMgrDeptArray(),"and t1.dept_id");
			sql1.append(EasyCalendar.newInstance().getYear(),"and t1.`year` = ?");
			result.put("yearDeptCount", this.getQuery().queryForInt(sql1.getSQL(), sql1.getParams()));
		
			EasySQL sql2 = new EasySQL();
			sql2.append(baseSql.getSQL());
			sql2.appendIn(getMgrDeptArray(),"and t1.dept_id");
			sql2.append(DateUtils.getBeforeDate(30,"yyyyyMMdd"),"and t1.week_begin >= ?");
			result.put("month1DeptCount", this.getQuery().queryForInt(sql2.getSQL(), sql2.getParams()));
			
			EasySQL sql3 = new EasySQL();
			sql3.append(baseSql.getSQL());
			sql3.appendIn(getMgrDeptArray(),"and t1.dept_id");
			sql3.append(DateUtils.getBeforeDate(90,"yyyyyMMdd"),"and t1.week_begin >= ?");
			result.put("month3DeptCount", this.getQuery().queryForInt(sql3.getSQL(), sql3.getParams()));
			
			EasySQL sql4 = new EasySQL();
			sql4.append(baseSql.getSQL());
			sql4.append(getUserId(),"and t1.creator = ?");
			sql4.append(DateUtils.getBeforeDate(90,"yyyyyMMdd"),"and t1.week_begin >= ?");
			result.put("month3SelfCount", this.getQuery().queryForInt(sql4.getSQL(), sql4.getParams()));
		
			EasySQL sql5 = new EasySQL();
			sql5.append(baseSql.getSQL());
			sql5.append(getUserId(),"and t1.creator = ?");
			sql5.append(DateUtils.getBeforeDate(30,"yyyyyMMdd"),"and t1.week_begin >= ?");
			result.put("month1SelfCount", this.getQuery().queryForInt(sql5.getSQL(), sql5.getParams()));
			
			EasySQL sql6 = new EasySQL();
			sql6.append(baseSql.getSQL());
			sql6.append(getUserId(),"and t1.creator = ?");
			sql6.append(EasyCalendar.newInstance().getYear(),"and t1.`year` = ?");
			result.put("yearSelfCount", this.getQuery().queryForInt(sql6.getSQL(), sql6.getParams()));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return getJsonResult(result);
	}
	
	private boolean hasWeekyAuth() {
		return this.getUserPrincipal().isRole("WEEKLY_MANGER");
	}
}
