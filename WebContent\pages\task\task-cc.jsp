<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>抄送我的任务</title>
	<style>
		.layui-tree-txt{font-size: 13px;}
		.layui-btn+.layui-btn{margin-left: 2px;}
		
		.layui-tab-item{padding: 0px 5px;margin-top: -5px;}
		.icon{width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:1px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input name="status" id="status" type="hidden" value="3"/>
			<input name="taskState" id="taskState" type="hidden"/>
			<div class="ibox">
					<div class="ibox-title clearfix">
		          		 <div class="form-group">
						      <div class="input-group input-group-sm"  style="width: 150px">
								 <span class="input-group-addon">任务类型</span>	
								<select data-mars-reload="false" name="taskTypeId" onchange="list.query()" data-mars="TaskDao.taskType" class="form-control">
									<option value="">请选择</option>
								</select>
						     </div>
		          		     <div class="input-group input-group-sm"  style="width: 180px">
								 <span class="input-group-addon">任务名称</span>	
								 <input type="text" name="taskName" class="form-control input-sm">
						     </div>
		          		     <div class="input-group input-group-sm"  style="width: 160px">
								 <span class="input-group-addon">项目</span>	
								 <input type="hidden" name="projectId" class="form-control input-sm">
								 <input type="text" id="projectId" onclick="singleProject(this);" class="form-control input-sm">
						     </div>
		          		     <div class="input-group input-group-sm">
			             		 <span class="input-group-addon">负责人</span>
								 <input type="hidden" name="assignUserId" class="form-control input-sm">
								 <input type="text" onclick="singleUser(this)" class="form-control input-sm" style="width: 60px;">
			                 </div>
							 <div class="input-group input-group-sm">
								 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								 <button type="button" class="btn btn-sm btn-default ml-5" onclick="list.clearForm()">清空</button>
							 </div>
							 <div class="input-group input-group-sm pull-right">
								 <button type="button" class="btn btn-sm btn-info" onclick="list.add()"> + 发起任务</button>
								 <script type="text/html" id="taskBar">
						     	   <a href="javascript:;" onclick="exportTask()" class="btn btn-xs btn-default ml-10">导出任务</a>
								 </script>
							 </div>
		          	     </div>
		              </div> 
					  <div class="ibox-content">
						  <div class="layui-tab layui-tab-brief" style="margin-top: 6px;" lay-filter="task" data-mars="TaskDao.myTaskListCountStat">
							  <ul class="layui-tab-title">
							    <li data-state="" class="layui-this">全部 <span class="layui-badge layui-bg-cyan ALL">0</span></li>
							    <li data-state="10">待办中 <span class="layui-badge layui-bg-orange" id="A">0</span></li>
							    <li data-state="11">已退回 <span class="layui-badge" id="F">0</span></li>
							    <li data-state="12">暂停中 <span class="layui-badge layui-bg-cyan" id="G">0</span></li>
							    <li data-state="13">已取消 <span class="layui-badge layui-bg-orange" id="H">0</span></li>
							    <li data-state="20">进行中 <span class="layui-badge" id="B">0</span></li>
							     <li data-state="25">超时未完成 <span class="layui-badge" id="E">0</span></li>
							    <li data-state="30">已完成 <span class="layui-badge layui-bg-blue" id="C">0</span></li>
							    <li data-state="40">已验收 <span class="layui-badge layui-bg-green" id="D">0</span></li>
							  </ul>
							  <div class="layui-tab-content" style="padding: 5px 0px;margin-top: 5px;">
							    <div class="layui-tab-item layui-show">
							    	 <table class="layui-hide" id="ccTaskTable"></table>
							    </div>
							  </div>
						  </div> 
					 </div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">

		var firstLoad = true;
		var list = {
			initTable:function(){
				$("#searchForm").initTable({
					mars:'TaskDao.myTaskList',
					id:'ccTaskTable',
					limit:30,
					height:'full-140',
					data:{status:3},
					title:'我参与的任务',
					toolbar:'#taskBar',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
						    field: 'TASK_NAME',
							title: '任务名称',
							minWidth:220,
							event:'list.detail',
							align:'left',
							style:'color:#1E9FFF;cursor:pointer',
							templet:function(row){
								var groupName = row['GROUP_NAME'];
								var fileCount = row['FILE_COUNT'];
								var id = row['P_TASK_ID'];
								var html = '';
								var fileDes = fileCount>0?getFileIcon('.file'):'';
								if(groupName){
									html = html + '【'+groupName+'】';
								}
								if(id){
									html = html + fileDes+"<i class='layui-icon layui-icon-senior'></i>"+row['TASK_NAME'];
								}else{
									html = html + fileDes+row['TASK_NAME'];
								}
								return html;
							}
						},{
						    field: 'PROJ_NAME',
							title: '项目名称',
							minWidth:150,
							event:'list.projectDetail',
							align:'left'
						 },{
						    field: 'CREATOR',
							title: '发起人',
							align:'center',
							width:90,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['CREATOR']+'\')">'+row.CREATE_NAME+'</a>';
							}
						},{
						    field: 'ASSIGN_USER_ID',
							title: '负责人',
							align:'center',
							width:80,
							templet:function(row){
								return '<a href="javascript:;" onclick="userInfoLayer(\''+row['ASSIGN_USER_ID']+'\')">'+row.ASSIGN_USER_NAME+'</a>';
							}
						},{
						    field: 'TASK_STATE',
							title: '任务状态',
							align:'center',
							width:80,
							templet:function(row){
								return taskStateLabel(row.TASK_STATE);
							}
						},{
						    field: 'TASK_LEVEL',
							title: '优先级',
							align:'center',
							width:70,
							templet:function(row){
								return taskTextLevel(row.TASK_LEVEL);
							}
						},{
							field:'PROGRESS',
							title:'进度',
							width:70,
							templet:'<div>{{d.PROGRESS}}%</div>'
						},{
						    field: 'CREATE_TIME',
							title: '创建时间',
							align:'center',
							width:120,
							templet:function(row){
								var time= row['CREATE_TIME'];
								return cutText(time,12,'');
							}
							
						},{
						    field: 'UPDATE_TIME',
							title: '更新时间',
							align:'left',
							hide:true,
							width:100
						}
					]],done:function(result){
						showFun();
						if(firstLoad){
							$(".ALL").text(result.totalRow);
						}
						firstLoad = false;
					}}
				);
			},
			query:function(){
				firstLoad = true;
				$("#searchForm").render();
				$("#searchForm").queryData({id:'ccTaskTable',jumpOne:true});
			},
			clearForm:function(){
				$("[name='projectId']").val('');
				$("[name='assignUserId']").val('');
				$("#searchForm")[0].reset();
				list.query();
			},
			add:function(){
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务'});
			},
			detail:function(data){
				var state = data['TASK_STATE'];
				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
				if(state==40){
					popup.openTab({id:"task_"+data.TASK_ID,type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-detail.jsp?isDiv=0',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:3}});
				}else{
					popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:3}});
				}
			},
			myTaskDetail:function(data){
				list.detail(data);
			},
			projectDetail:function(data){
				var projectId = data['PROJECT_ID'];
				if(projectId){
					projectBoard(projectId);
				}
			}
		}
		
		function reloadTaskList(source){
			$("#searchForm").queryData({id:'ccTaskTable',jumpOne:true});
		}
		
		layui.use('element',function(){
			var element=layui.element;
			element.on('tab(task)', function(elem){
				var state = $(this).data('state');
				$("#taskState").val(state);
				$("#searchForm").queryData({id:'ccTaskTable',jumpOne:true});
			});
		 });
		
		function exportTask(){
			layer.msg('正在导出',{time:500});
			location.href = '${ctxPath}/servlet/task?action=exportTask&data='+encodeURI(JSON.stringify(form.getJSONObject('#searchForm')));
		}
		
		function showFun(){
			$('.cbTask,.ysTask,.finishTask').hide();
		}
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.initTable();
			}});
		});
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>