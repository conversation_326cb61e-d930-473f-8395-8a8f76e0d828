<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowName}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的会议室申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">会议室</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data1">
						  				<option value="">--</option>
						  				<!-- <option value="拼搏会议室">拼搏会议室</option> -->
						  				<option value="奋进会议室">奋进会议室</option>
						  				<option value="领航会议室">领航会议室</option>
						  			</select>
					  			</td>
					  			<td class="required">预计人数</td>
					  			<td>
					  				<input type="number" data-rules="required" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">使用开始时间</td>
					  			<td>
					  				<input type="text" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 09:00:00',doubleCalendar:false,alwaysUseStartDate:true,maxDate:'#F{$dp.$D(\'dateEnd\')}'})" class="form-control input-sm Wdate" id="dateStart" name="apply.data3"/>
					  			</td>
					  			<td class="required">使用结束时间</td>
					  			<td>
					  				<input type="text" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',alwaysUseStartDate:true,minDate:'#F{$dp.$D(\'dateStart\')}'})" id="dateEnd" class="form-control input-sm Wdate" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">会议类别</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data5">
						  				<option value="">--</option>
						  				<option value="部门会议">部门会议</option>
						  				<option value="项目会议">项目会议</option>
						  				<option value="高层会议">高层会议</option>
						  				<option value="招聘面试">招聘面试</option>
						  				<option value="其他会议">其他会议</option>
						  			</select>
					  			</td>
					  			<td class="required">参会人员</td>
					  			<td>
					  				<input type="hidden" name="apply.data6"/>
					  				<input type="text" data-rules="required" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" maxlength="1000" name="apply.data7"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>外部人员</td>
					  			<td colspan="3">
					  				<input type="text" class="form-control input-sm" maxlength="500" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">会议主题</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  	    <tr class="remindInfo">
					  	    	<td>注意事项</td>
					  	    	<td colspan="3">
					  	    		①会议室地址：<br>
									奋进会议室：茶水间旁边；<br>
									拼搏会议室：前台荣誉墙旁边；<br>
									领航会议室：财务室旁边<br>
									②会议室使用完毕请注意关灯、空调等，保持桌椅摆放整齐；<br>
									③若遇会议改期或取消，应及时登录OA取消原预约。<br>
									④遇特别紧急或重要会议，优先级别遵循限公司后部门的原则，请双方会议联系人共同协商解决。
					  	    	</td>
					  	    </tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 unedit-remove" data-mars="FlowCommon.roomApplyList" data-template="recentMeetingScheduleList">
				 	
				</div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>


<script id="recentMeetingScheduleList" type="text/x-jsrender">
	{{if data.length>0}}
	<div class="layui-panel mt-10 pd-30" style="padding:20px;">
  	 {{for data}}
  		<p class="mb-10">
  			{{:#index+1}}、{{:APPLY_TITLE}} / {{:DATA1}} / {{:DATA3}}~{{:DATA4}} / {{call:APPLY_STATE fn='flowApplyStateText'}}
  		</p>
  	  {{/for}}
	</div>
	{{/if}}
</script>

</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			var data6 = $("[name='apply.data6']").val();
			if(data6){
				var data7 = $("[name='apply.data7']").val();
				FlowCore.approveData = {ccNames:data7,ccIds:data6};
			}
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		function romeQuery(){
			
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>