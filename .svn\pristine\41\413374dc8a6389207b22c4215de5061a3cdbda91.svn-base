<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务监控</title>
	<style>
		.nodata{text-align: center;}
		.num td{font-size: 20px;color: #1890ff;text-align: center;}
		.num-title td{text-align: center;}
		.task-title{font-weight: 700;font-size:16px;}
		.right-state .label{
			position: absolute;
		    right: 0px;
		    top: 13px;
	        height: 24px;
	        border-radius: 3px 0 0 3px;
    		line-height: 18px;
		  /*   transform: rotate(45deg);
		    background-color: #fff;
		    color: red; */
		}
		#pageContainer{
			background-color: #fff;
		    padding: 0px 10px;
		    margin: 6px 0px;
	    }
	    .head-pic{width: 38px;height: 38px;border-radius: 50%;float: right;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off" onsubmit="return false;" class="form-inline" id="taskMgrForm">
				<div class="layui-card" style="padding:10px 15px 15px;margin-bottom:8px;">
				  	<div class="layui-row mt-10">人员工作监控 </div>
			  		<hr>
				  	<div class="layui-row mt-10">
				  	   <div class="input-group input-group-sm mr-5">
				     	     <span class="input-group-addon">工作时间</span>	
					  	     <input type="text" name="taskDateRange" data-mars="CommonDao.yearBeginToNow" class="form-control input-sm dateRange Wdate" style="width:175px">
				   		 </div>
			   			<div class="input-group input-group-sm">
			     		    <span class="input-group-addon">部门</span>	
				  		    <input class="form-control input-sm" name="deptName" style="width:90px">
		   			     </div>
		    		    <div class="input-group input-group-sm">
				     		 <span class="input-group-addon">姓名</span>	
					  		 <input type="text" name="userName" class="form-control input-sm" style="width:122px">
			   			</div>
			   			<button type="button" class="btn btn-sm btn-default ml-10" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
			  		 </div>					  
			  </div>
			  <div class="list-content">
					<div class="layui-row layui-col-space10 item-content"></div>
					<div id="pageContainer"></div>
			  </div>
		</form>
		 <script type="text/x-jsrender" id="list-templet">
			 {{if data.length==0}} <div class="layui-col-md12"><div class="layui-card"><div class="layui-card-body text-c"><img class="nodata" src="/easitline-static/images/nodata.png"/></div></div></div>{{/if}}
			 {{for data}}
 			   <div class="layui-col-md4 right-state">
				<div class="layui-card">
  					<div class="layui-card-header"><i class="layui-icon layui-icon-console"></i> <span class="task-title">{{:DEPTS}}-{{:USERNAME}}</span> 
						{{if PIC_URL}}<img class="head-pic" src="{{:PIC_URL}}">{{/if}}
					</div>
  					<div class="layui-card-body taskInfo_{{:TASK_ID}}">
    					<table class="layui-table layui-text">
							<tr class="num-title"><td>项目数</td><td>任务数</td><td>在途任务数</td><td>当前逾期任务数</td><td>在途工作量(人/天)</td></tr>
							<tr class="num"><td>{{:PROJECT_COUNT}}</td><td>{{:TASK_COUNT}}</td><td>{{:TASK_DOING}}</td><td>{{:TASK_DELAY_COUNT}}</td><td>{{:PLAN_WORK_HOUR}}</td></tr>
						</table>
    					<table class="layui-table layui-text mt-10" style="background-color:#F9FAFE;margin-top:15px;">
							<tr><td>最后任务</td><td>{{:LAST_TASK_TIME}}</td></tr>
							<tr><td>参与项目</td><td>{{:PROJECT_NAMES}}</td></tr>
						</table>
						<p class="text-r">
							<button class="btn btn-default btn-sm mr-10" type="button" onclick="list.workhour('{{:USER_ID}}','{{:USERNAME}}')">工时情况</button>
							<button class="btn btn-info btn-sm" type="button" onclick="list.taskList('{{:USER_ID}}','{{:USERNAME}}')">项目任务</button>
						</p>
  					</div>
				</div>
  			 </div>
 			 {{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript" src="${ctxPath}/pages/csc/task/common/static/task.js?v=20230324"></script>
<script type="text/javascript">
		
		$(function(){
			layui.use(['laydate'], function(){
			   layui.laydate.render({elem: '.dateRange',rangeLinked:true,shortcuts:fastSelectDateArray(),range:'到',done:function(value, date, endDate){
				    $('#dateRange').val(value);
				    list.query();
			   }});
		     });
			
			$("#taskMgrForm").render({success:function(){
				list.init();
			}});
			
		});
		
		var list={
			init:function(data){
				if (data == undefined) data = {pageType:3,pageIndex:1,pageSize:9,isFirstLoad:true};
				var _data = $.extend({},form.getJSONObject("#taskMgrForm"),data);
				ajax.remoteCall(getCtxPath()+'/webcall?action=TaskStatDao.peopleWorkInfoList', _data, function(result){
					if(result.state == 1){
						var tpl =  $.templates("#list-templet");
						var html = tpl.render(result);
						$(".item-content").html(html);
						
						if(data.isFirstLoad){
							 var laypage = layui.laypage;
							  laypage.render({
							    elem: 'pageContainer',
							    limit:9,
								limits:[6,10,20],
							    count: result.totalRow,
							    jump:function(obj, first){
							        if(!first){
										var newJson = $.extend({},{pageIndex:obj.curr,pageType:3,pageSize:obj.limit,isFirstLoad:false});
										list.init(newJson);
							        }
							    }
							});
						}
					}
				});	
			},
			query:function(){
				var dateRange = $("#dateRange").val();
				if(dateRange==''){
					layer.msg('时间范围不能为空',{icon:7});
					return;
				}
				list.init();
			},
			getDateStr:function(val){
				var AddDayCount = val;
			    var dd = new Date();
			    dd.setDate(dd.getDate()+AddDayCount);//获取AddDayCount天后的日期
			    var y = dd.getFullYear();
			    var m = dd.getMonth()+1;if(m<10)m="0"+m;
			    var d = dd.getDate();if(d<10)d="0"+d;
			    var h = dd.getHours();if(h<10)h="0"+h;
			    var m2 = dd.getMinutes();
			    return y+"-"+m+"-"+d;
			},
			workhour:function(userId,userName){
				popup.layerShow({id:"workhour",type:2,url:"${ctxPath}/pages/project/workhour/weekly-work-hour.jsp",title:"工时",area:['80%','100%'],offset:'r',data:{userId:userId,userName:userName}});
			},
			taskList:function(userId,userName){
				popup.layerShow({id:"taskList",type:2,url:"${ctxPath}/pages/task/task-query.jsp",title:"任务",area:['80%','100%'],offset:'r',data:{assignUserId:userId,assignUserName:userName}});
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>