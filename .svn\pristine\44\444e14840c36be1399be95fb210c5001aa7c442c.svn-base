<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>发票详情</title>
	<style>
		.form-horizontal{width: 100%;}
		.del-file-btn{cursor: pointer;font-size: 20px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="InvoiceEditForm" class="form-horizontal" data-text-model="false" data-mars="BxDao.bxInvoiceInfo" autocomplete="off" data-mars-prefix="invoice.">
 		   		<input type="hidden" value="${param.invoiceId}" name="invoiceId"/>
 		   		<input type="hidden" value="${param.invoiceId}" name="fkId"/>
 		   		<input type="hidden" value="${param.invoiceId}" name="invoice.INVOICE_ID"/>
 		   		<input type="hidden" id="bxStateVal"  name="invoice.BX_STATE"/>
 		   		<input type="hidden" id="invoiceFileId"  name="invoice.FILE_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td width="100px" class="required">发票号</td>
		                    <td style="width: 40%">
		                   		 <input data-rules="required" type="text" readonly="readonly" name="invoice.INVOICE_NO" class="form-control input-sm">
		                    </td>
		                    <td class="required">开票类型</td>
		                    <td style="width: 40%">
		                   		 <input type="text" data-rules="required"  name="invoice.INVOICE_TYPE" class="form-control input-sm">
		                    </td>
			            </tr>
			           <tr>
		                    <td width="100px" class="required">开票单位</td>
		                    <td>
								<input type="text" data-rules="required"  name="invoice.SALE_COMPANY" class="form-control input-sm"/>
		                    </td>
			           		<td class="required">销售方编号</td>
		                    <td>
		                   		 <input type="text" data-rules="required" name="invoice.SALE_COMPANY_NO" class="form-control input-sm">
		                    </td>
			           </tr>
			           <tr>
		                    <td class="required">购买方</td>
		                    <td>
		                   		 <input type="text" data-rules="required"  name="invoice.BUY_COMPANY" class="form-control input-sm">
		                    </td>
		                    <td class="required">购买方编号</td>
		                    <td>
		                   		 <input type="text" data-rules="required" name="invoice.BUY_COMPANY_NO" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
		                    <td class="required">开票日期</td>
		                    <td>
		                    	 <input type="text" data-rules="required"  name="invoice.INVOICE_DATE" class="form-control input-sm">
		                    </td>
		                    <td class="required">开票金额</td>
		                    <td>
		                    	 <input type="text" data-rules="required" name="invoice.TOTAL_AMOUNT" class="form-control input-sm">
		                    </td>
		                </tr>
		                  <tr>
		                	<td class="required">税额</td>
		                    <td>
		                   		 <input type="text" data-rules="required" name="invoice.TOTAL_TAX" class="form-control input-sm">
		                    </td>
		                   <td class="required">不含税</td>
		                    <td>
		                   		 <input type="text" data-rules="required" name="invoice.AMOUNT_IN_FIGUERS" class="form-control input-sm">
		                    </td>
		                </tr>
		                 <tr>
		                    <td class="required">税率</td>
		                    <td>
		                   		 <input type="text" data-rules="required" name="invoice.TAX_RATE" class="form-control input-sm">
		                    </td>
		                    <td>服务类型</td>
		                    <td>
		                   		 <input type="text" name="invoice.SERVICE_TYPE" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
			            	<td>备注</td>
			               	<td colspan="3">
	                           <textarea style="height: 60px;" class="form-control input-sm" name="invoice.KP_REMARK"></textarea>
			               	</td>
			            </tr>
			            <tr>
			            	<td>PDF文件</td>
			               	<td colspan="3">
			               		<a id="pdfFileUrl" target="_blank"><span name="invoice.FILE_NAME"></span></a>
			               	</td>
			            </tr>
			            <tr>
			            	<td>XML文件</td>
			               	<td colspan="3">
			               		<a id="xmlFileUrl" target="_blank"><span name="invoice.XML_FILE_NAME"></span></a>
			               	</td>
			            </tr>
			            <tr class="hidden">
			            	<td>其他附件</td>
			               	<td colspan="3" class="layui-text">
			               		<div id="fileList" style="display: inline-block;" data-template="template-files" data-mars="FileDao.fileList"></div>
			              		<button class="btn btn-info btn-sm" id="upload-btn" type="button">上传相关资料</button>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-default btn-sm update-btn" style="width: 80px;" onclick="InvoiceEit.ajaxSubmitForm()"> 修 改 </button>
				      <button type="button" class="btn btn-danger btn-sm ml-15 del-btn" style="width: 80px" onclick="InvoiceEit.delData();"> 删除 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
  		<script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-item"><a style="color:#01aaed;" href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#01aaed;">{{:FILE_NAME}} &nbsp;</span></a><i title="删除" class="mr-10 del-file-btn file_{{:FILE_ID}}" data-id="{{:FILE_ID}}" onclick="delFile($(this),true);">x</i></div>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("InvoiceEit");
	    
		InvoiceEit.invoiceId='${param.invoiceId}';
		
		var invoiceId = '${param.invoiceId}';
		var op = '${param.op}';
		
		$(function(){
			if(op=='detail'){
				$("#InvoiceEditForm").attr('data-text-model',true);	
			}

			$("#InvoiceEditForm").render({success:function(result){
				var bxState = $('#bxStateVal').val();
				if(bxState!=0){
					$('.del-btn,.update-btn').remove();
				}
				var invoiceFileId = $('#invoiceFileId').val();
				$('.file_'+invoiceFileId).remove();
				
				if(op=='detail'){
					$("#InvoiceEditForm #upload-btn").hide();
					$("#InvoiceEditForm .update-btn,.del-btn,.del-file-btn").hide();
				}
				
				if(invoiceId){
					var row = result['BxDao.bxInvoiceInfo'].data;
					var pdfFileId = row.FILE_ID;
					if(pdfFileId){
						$('#pdfFileUrl').attr('href','/yq-work/fileview/'+pdfFileId+'?view=online&filename='+row.FILE_NAME);
					}else{
						$('#pdfFileUrl').remove();
					}
					var xmlFileId = row.XML_FILE_ID;
					if(xmlFileId){
						$('#xmlFileUrl').attr('href','/yq-work/fileview/'+xmlFileId+'?view=online&filename='+row.XML_FILE_NAME);
					}else{
						$('#xmlFileUrl').remove();
					}
				}
				
			}});  
			
			layui.use('upload', function(){
			 	  var upload = layui.upload;
				  var uploadInst = upload.render({
				    elem: '#upload-btn'
				    ,url: '/yq-work/servlet/upload?action=index'
				    ,accept: 'file'
				    ,exts:'pdf|ofd|xml'
				    ,size:1024*20
					,multiple:true
					,number:10
				    ,field: 'file'
				    ,data:{
				    	source:function(){
							return 'bxInvoice';
						},
						fkId:function(){
							return InvoiceEit.invoiceId;
						}
				    },done: function(result, index, upload){
						var item = this.item;
					    if(result.state==1){
					    	layer.msg(result.msg+'，已自动保存',{icon: 1,time:1800},function(){
								$('#fileList').render({data:{fkId:InvoiceEit.invoiceId},success:function(){
									var invoiceFileId = $('#invoiceFileId').val();
									$('.file_'+invoiceFileId).remove();
								}});
							}); 
						}else{
							 layer.closeAll('dialog');
							 layer.closeAll('loading');
							 layer.alert(result.msg,{icon: 5});
						}
				    },choose:function(obj){
				    	
				    },before:function(obj){
				    	
				    },allDone: function(obj){
		
					}
				    ,error: function(res, index, upload){
				    	layer.close(loadIndex);
				    	layer.alert("上传文件请求异常！",{icon: 5});
				   }
		    });
		  });
			
		});
		
		InvoiceEit.ajaxSubmitForm = function(){
			layer.confirm('发票识别有误,确认是否要修改',{title:'请最终以开票电子发票文件为准',icon:3,offset:'20px'},function(){
				if(form.validate("#InvoiceEditForm")){
					if(InvoiceEit.invoiceId){
						InvoiceEit.updateData(); 
					}else{
						InvoiceEit.insertData(); 
					}
				};
			});
		}
		
		InvoiceEit.insertData = function(flag) {
			var data = form.getJSONObject("#InvoiceEditForm");
			delete data['invoice.INVOICE_ID'];
			ajax.remoteCall("${ctxPath}/servlet/bx/invoice?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadInvoiceList();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		InvoiceEit.updateData = function(flag) {
			var data = form.getJSONObject("#InvoiceEditForm");
			ajax.remoteCall("${ctxPath}/servlet/bx/invoice?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadInvoiceList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		InvoiceEit.delData = function() {
			var data = form.getJSONObject("#InvoiceEditForm");
			layer.confirm('确认删除吗',{icon:3,title:'删除提醒',offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/bx/invoice?action=del",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
							reloadInvoiceList();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>