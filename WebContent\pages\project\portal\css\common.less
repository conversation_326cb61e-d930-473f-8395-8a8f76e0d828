@colorMain:#0D92E1;
// @colorMain: #FF994C; // 主题颜色
@colorBorder: #ddd; //线跨颜色
.el-avatar {background:@colorMain;}

body {
    line-height: 1.5
}

code,kbd,pre,samp {
    font-family: <PERSON><PERSON><PERSON>,<PERSON><PERSON>,Courier,monospace
}

a:active,a:hover,a:link,a:visited {
    text-decoration: none
}
*::-webkit-scrollbar {
    width: 8px; /* Width of the scrollbar */
}

*::-webkit-scrollbar-track {
    background: none; /* Color of the track */
}

*::-webkit-scrollbar-thumb{
    background: rgba(0, 0, 0, 0.01); /* Color of the thumb */
    opacity: 0;
    border-radius: 4px;
}


*:hover::-webkit-scrollbar-thumb  {
    background: #d9d9d9; /* Color of the thumb */
    opacity:0.8;
}
.clearfix {

    &:before,
    &:after {
        content: " ";
        display: table;
    }

    &:after {
        clear: both;
    }
}

.abs-center {
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.flex {
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.flex-row {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.flex-item {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: auto;
}