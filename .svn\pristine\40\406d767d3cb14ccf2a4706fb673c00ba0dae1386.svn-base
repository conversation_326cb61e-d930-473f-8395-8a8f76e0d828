<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>任务详情</title>
	<style type="text/css">
	
		.ibox-content {
		    padding: 10px 15px!important;
		}
		.file-div{ 
		    display: inline-block;
		    background-color: #f5f5f5;
		    padding: 2px 8px;
		    line-height: 25px;
		    float: left;
		    margin-top: 3px;
		    margin-right: 5px;
		}
	   .file-div i{color: red;margin-left: 5px;cursor: pointer;}
	
		.table-vzebra tbody > tr > td:nth-child(2n+1), .table-vzebra tbody > tr > th:nth-child(2n+1){text-align: left;} 
		.radio-inline{margin-top: 0px;}
		#affairEditForm{
			margin-bottom: 100px;
			height: 100%;
		}
		#affairEditForm .userName{
		    font-size: 1em;
		    color: rgba(0,0,0,.87);
		    font-weight: 500;
		}
		#affairEditForm .createTime{
		    display: inline-block;
		    margin-left: .5em;
		    color: rgba(0,0,0,.4);
		    font-size: .875em;
		}
		#affairEditForm td{text-align: left;}
		#affairEditForm .t1 td{line-height: 16px;}
		.avatar{
			float: left;
			height: 3em;
			width: 3em;
			display: block;
		    margin: .2em 0 0;
		}
    	.avatar img{
		    display: inline-block;
		    height: 100%;
		    width: 100%;
		    border-radius: 100%;
		    overflow: hidden;
		    font-size: inherit;
		    vertical-align: middle;
		    -webkit-box-shadow: 0 0 1px rgba(0,0,0,.3);
		    box-shadow: 0 0 1px rgba(0,0,0,.3);
		    cursor: pointer;
		}
		.b_content{
			margin: 10px 15px;
		}
		.d_content{
			margin-left: 4em;
			padding: 6px 0px;
		}
		small a{margin-right: 6px;cursor: pointer;color: #999!important;}
		.color999{color: #999;margin-right: 2px;}
		.w-70{width: 70px!important;min-width:70px!important;}
		#taskDesc img{max-width: 700px;}
		#taskDesc{padding:20px 10px;background-color:#fff8f8;line-height: 22px; font-size: 13px;color: #288237;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<div class="ibox-content">
		     	<form id="affairEditForm" data-mars="AffairDao.record" autocomplete="off" data-mars-prefix="affair.">
		     		   <input type="hidden" id="affairId" value="${param.affairId}" name="affair.AFFAIR_ID"/>
		     		   <input type="hidden" value="${param.affairId}" name="fkId"/>
						<table class="table table-edit table-vzebra t1">
					            <tr>
				                    <td colspan="2">
				                   		 <span style="font-size: 18px;font-weight: bold;" name="affair.AFFAIR_NAME"></span>
				                   		 <a class="layui-hide-xs pull-right" href="javascript:pageBack('${param.source}')"><i class="layui-icon layui-icon-left"></i>返回</a>
				                    </td>
					            </tr>
					            <tr >
					            	<td>
					            		 <i class="glyphicon glyphicon-time"></i> 
					            		 <span name="affair.CREATE_NAME"></span>
					            		 <span class="color999">创建于</span>
					            		 <span class="color999" name="affair.CREATE_TIME"></span>
				                    	<i class="glyphicon glyphicon-eye-open ml-10"></i>
				                    	<span class="color999">查看次数</span>
			                    		<span onclick="viewLog();" style="text-decoration: underline;cursor: pointer;color: blue;" name="affair.VIEW_COUNT"></span>
				                    </td>
					            </tr>
				                <tr>
				                	<td>
				                    	<i class="glyphicon glyphicon-hand-right"></i>
				                    	<span class="color999">收件人</span>
			                    		<span name="affair.RECEIVER_DEPT_NAME"></span>
			                    		<span name="affair.RECEIVER_USER_NAME"></span>
				                	</td>
				                </tr>
				                <tr>
				                	<td>
				                    	<i class="glyphicon glyphicon-flag"></i>
				                		<span class="color999">抄送人</span>
				                    	<span name="affair.SHARE_NAMES" data-fn="setMailtos">--</span>
				                	</td>
				                </tr>
				                <tr>
				                	<td>
					               		<div id="affairDesc" style="min-height: 120px;overflow:auto;" name="affair.AFFAIR_DESC"></div>
				                	</td>
				                </tr>
				                <tr>
				                	<td>
					               		<div style="display: inline-block;" data-template="affair-template-files" data-mars="FileDao.fileList"></div>
					               		<div id="fileList" style="display: inline-block;"></div>
				                	</td>
				                </tr>
	  					  </table>
	  					 <fieldset class="content-title" style="display:block;">
  								<legend>回复</legend>
						  </fieldset>
	  					  <table class="table table-edit table-vzebra">
					            <tr>
					               	<td colspan="4">
			                           <textarea placeholder="在此输入回复内容" style="height: 80px;width:100%" maxlength="255" class="form-control input-sm" name="comment" id="comment"></textarea>
					               		<small class="layui-hide-xs mt-10">快速回复：<a>已处理</a><a>已查收</a><a>已解决</a></small>
					               		<div class="pull-right mt-10">
				                            <button class="btn btn-sm btn-default btn-outline mr-15" type="button" onclick="loadCommentHistory()"><i class="glyphicon glyphicon-refresh"></i> 刷新回复</button>
				                            <button class="btn btn-sm btn-info btn-outline uploadFileBtn mr-15" type="button" onclick="$('#localfile').click()">+上传附件</button>
				                            <button type="button" style="box-shadow: 0 4px 8px 0 rgba(31,93,234,.35);" class="btn btn-primary btn-sm  " onclick="addComment()"> <i class="glyphicon glyphicon-send"></i> 提交回复 </button>
					               		</div>
					               	</td>
					            </tr>
	  					  </table>
	  					  <div id="history"></div> 
		  		</form>
		</div>
	  	
	  	<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Affair.uploadFile()"/>
  		</form>
  		
  		 <script id="affair-template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="javascript:;" onclick="downloadFile(this)" data-name="{{:FILE_NAME}}" data-href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a></div>
			{{/for}}
			{{if data.length==0}}{{/if}}
		</script>
		
  		 <script id="template-comments" type="text/x-jsrender">
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{:CREATE_NAME}}</span> <span class="createTime">{{:CREATE_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{call:CONTENT fn='getContent'}}</div>
						</div>						
					</div>
				</div>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	
	<script type="text/javascript">
   
		var Affair = {};
	    
		Affair.affairId='${param.affairId}';
		
		var affairId='${param.affairId}';
			
		function getContent(val){
			if(val){
				return val.replace(/\n|\r\n/g,'<br/>');
			}else{
				return '--';
			}
		}
		
		var affairObj;
		function initFormData(){
			$("#affairEditForm").render({success:function(result){
				var record=result['AffairDao.record'];
				affairObj=record['data'];
				loadCommentHistory();
				layer.photos({photos: '#affairDesc',anim: 0,shade:0,shadeClose:true,closeBtn:true}); 
			}});  
		}
		
		$(function(){
			initFormData();
			$("small a").click(function(){
				var t = $(this);
				var text = t.text();
				$("#comment").val(text);
				addComment();
			});
		});
		
		function setMailtos(val){
			if(val==''){
				return '无';
			}else{
				return val;
			}
		}
		
		function pageBack(source){
			$("."+source).click();
		}
		
		Affair.ajaxSubmitForm = function(){
			if(form.validate("#affairEditForm")){
				if(Affair.affairId){
					Affair.updateState();
				}
			};
		}
		
		Affair.uploadFile = function(callback){
			var fkId=Affair.affairId;
			easyUploadFile({callback:'callback',fileMaxSize:(1024*20),fkId:fkId,source:'affair'});
		}
		
		var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="/yq-work/fileview/'+data.id+'?view=online&filename='+data.name+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
			var comment="上传["+data.name+"]";
			addComment(comment);
		}
		
		function loadCommentHistory(){
			 ajax.remoteCall("${ctxPath}/webcall?action=CommonDao.commentsList",{fkId:affairId},function(result) { 
				if(result.total>0){
					var html=renderTpl("template-comments",result);
					$("#history").html(html);
				}
			});
		}
		
		function downloadFile(el){
			var obj = $(el);
			addComment("下载["+obj.attr('data-name')+"]");
			window.open(obj.attr('data-href'));
		}
		
		var addComment = function(comment) {
			comment=comment||$("#comment").val();
			if(comment){
				var data =$.extend({},affairObj,{content:comment,fkId:affairId,title:affairObj['AFFAIR_NAME']});
				ajax.remoteCall("${ctxPath}/servlet/comment?action=add",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							$("#comment").val("");
							loadCommentHistory();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		
		function delComment(id,obj){
			ajax.remoteCall("${ctxPath}/servlet/comment?action=del",{commentId:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(obj).parents(".b_content").remove();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		var viewLog =function(){
			lookLogLayer(Affair.affairId);
		}
		
</script>
</EasyTag:override>
<c:choose>
	<c:when test="${param.isDiv==0}">
		<%@ include file="/pages/common/layout_form.jsp" %>
	</c:when>
	<c:otherwise>
		<%@ include file="/pages/common/layout_div.jsp" %>
	</c:otherwise>
</c:choose>