<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>合同看板</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }

        .layui-card-header, .layui-card-header select {
            color: rgb(62, 104, 254) !important;
        }

        .layui-this {
            color: rgb(62, 104, 254) !important;
        }


        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-stat .layui-col-md3 {
            text-align: center;
            padding: 10px;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .top-3 {
            font-size: 24px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .contract-stat .layui-card {
            padding: 10px;
            border-radius: 5px;
        }

        .contract-stat .layui-card-hover:hover {
            transform: scale(1.02);
            transition: transform .3s ease;
            box-shadow: 0 4px 11px #0003;
            cursor: pointer;
        }


        .layui-icon-tips {
            margin-left: 5px;
        }

        .layui-progress {
            margin-top: 26px;
            height: 8px
        }


    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm">
        <input type="hidden" name="source" value="all">
        <input type="hidden" name="stageTime" value="year">
        <input type="hidden" name="stageType" value="all">
        <input type="hidden" name="incomeStageType" value="all">
        <input type="hidden" name="receiptTime" value="year">
        <input type="hidden" name="dateField2" value="year">
        <div class="layui-row layui-col-space10 contract-stat" data-mars="ContractStatisticDao.contractConsoleStat">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1"> <span  id="COUNT_CUST">0</span> / <span id="CONTRACT_CUST_COUNT">0</span></div>
                        <div class="top-2">今年新增接洽客户/成交客户数<i class="layui-icon layui-icon-tips" lay-tips="以客户信息的创建时间年份为统计标准。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="COUNT_CONTRACT">0</div>
                        <div class="top-2">今年新增销售合同数<i class="layui-icon layui-icon-tips" lay-tips="以合同年限的年份为准，不包含提前执行、提前开发合同。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_CONTRACT">0</div>
                        <div class="top-2">今年新增销售合同金额<i class="layui-icon layui-icon-tips" lay-tips="以合同年限的年份为准。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_LAST_YEAR">0</div>
                        <div class="top-2">去年销售总额 <span id="growthDiv" style="float: right"></span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10 contract-stat" data-mars="ContractStatisticDao.contractConsoleStat2">
            <div class="layui-col-md3">
                <div class="layui-card layui-card-hover" data-category="1">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_RECEIPT">0</div>
                        <div class="top-2">今年已收款金额<i class="layui-icon layui-icon-tips" lay-tips="收款时间为今年的收款总额，与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card layui-card-hover" data-category="1">
                    <div class="layui-card-body">
                        <div class="top-3" id="UN_RECEIVE">0</div>
                        <div class="top-2">今年待收款金额<i class="layui-icon layui-icon-tips" lay-tips="今年的合同阶段总额 减去 今年的已收款总额。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card layui-card-hover" data-category="2">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_CONFIRM">0</div>
                        <div class="top-2">今年已收入确认金额(税后)<i class="layui-icon layui-icon-tips" lay-tips="收入确认时间为今年的收入确认A总额，与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card layui-card-hover">
                    <div class="layui-card-body">
                        <div class="top-3" id="SUM_UNCONFIRM" data-category="2">0</div>
                        <div class="top-2">今年待收入确认金额(税后)<i class="layui-icon layui-icon-tips" lay-tips="计划完成时间为今年的收入确认阶段总额减去收入确认A总额，与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab" lay-filter="monthFilter1">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="month1">月份销售数据
                                <i class="layui-icon layui-icon-tips" lay-tips="统计今年及去年所有销售经理所销售的合同总额，统计标准为[签约日期]。"></i>
                            </li>
                            <li lay-id="quarter1">季度销售数据</li>
                            <li lay-id="saleButton" id="saleButton1" class="layui-tab-title-right pull-right">
                                <div class="form-group">
                                    <div class="btn-group-sm btn-group select-year1" style="width: 100px;margin-left: 20px">
                                        <button class="btn btn-info btn-xs" value="0" type="button">今年</button>
                                        <button class="btn btn-default btn-xs" value="1" type="button">去年</button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="monthChart1" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="quarterChart1" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab" lay-filter="monthFilter2">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="month2">月份回款数据
                                <i class="layui-icon layui-icon-tips" lay-tips="收款数据统计每半小时更新一次。"></i>
                            </li>
                            <li lay-id="quarter2">季度回款数据</li>
                            <li lay-id="huiKuanButton" id="huiKuanButton1" class="layui-tab-title-right pull-right">
                                <div class="form-group">
                                    <div class="btn-group-sm btn-group select-year2" style="width: 100px;margin-left: 20px">
                                        <button class="btn btn-info btn-xs" value="0" type="button">今年</button>
                                        <button class="btn btn-default btn-xs" value="1" type="button">去年</button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="monthChart2" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="quarterChart2" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        合同回款排行
                        <i class="layui-icon layui-icon-tips" lay-tips="统计所有合同在今年/去年的收款金额并排行。总收款进度的分子包括往年收款。"></i>
                        <div class="form-group pull-right">
                            <div class="btn-group-sm btn-group receipt-year-1" style="width: 100px;margin-left: 20px">
                                <button class="btn btn-info btn-xs" value="0" type="button">今年</button>
                                <button class="btn btn-default btn-xs" value="1" type="button">去年</button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="contractReceiptRank"></table>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        客户回款排行
                        <i class="layui-icon layui-icon-tips" lay-tips="统计所有客户在今年/去年的收款金额并排行。总收款进度的分子分母包括往年的收款与合同金额。"></i>
                        <div class="form-group pull-right">
                            <div class="btn-group-sm btn-group receipt-year-2" style="width: 100px;margin-left: 20px">
                                <button class="btn btn-info btn-xs" value="0" type="button">今年</button>
                                <button class="btn btn-default btn-xs" value="1" type="button">去年</button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="custReceiptRank"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        销售金额排名
                        <i class="layui-icon layui-icon-tips" lay-tips="统计每个销售经理所销售的合同总额，年份以合同年限为准。"></i>
                        <div class="form-group pull-right">
                            <div class="btn-group-sm btn-group sale-year-1" style="width: 100px;margin-left: 20px">
                                <button class="btn btn-info btn-xs" value="0" type="button">今年</button>
                                <button class="btn btn-default btn-xs" value="1" type="button">去年</button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body layui-col-space10" style="height: 360px">
                        <div class="layui-col-md8">
                            <div id="saleChart1" style="height: 340px;width: 100%" data-anim="fade"></div>
                        </div>
                        <div class="layui-col-md4">
                        <table id="saleRankTable"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        销售回款排名
                        <i class="layui-icon layui-icon-tips" lay-tips="统计每个销售的今年或去年收款金额的总和，以收款日期的年份而不是合同年限为统计标准。以合同的销售经理为准，与收款负责人无关。"></i>
                        <div class="form-group pull-right">
                            <div class="btn-group-sm btn-group sale-year-2" style="width: 100px;margin-left: 20px">
                                <button class="btn btn-info btn-xs" value="0" type="button">今年</button>
                                <button class="btn btn-default btn-xs" value="1" type="button">去年</button>
                            </div>
                        </div>
                    </div>
                    <div class="layui-card-body layui-col-space10" style="height: 360px">
                        <div class="layui-col-md8">
                            <div id="saleChart2" style="height: 340px;width: 100%" data-anim="fade"></div>
                        </div>
                        <div class="layui-col-md4">
                            <table id="saleReceiptRankTable"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab" lay-filter="deptFilter1">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="deptTab11"><span name="currentYear"></span>营销大区销售份额 <i class="layui-icon layui-icon-tips"
                                                                                                                  lay-tips="统计营销大区今年销售的合同总额，年份以合同年限为准。份额仅统计top15且不为0的数据，比例只做参考。"></i></li>
                            <li lay-id="deptTab12">营销大区销售top10</li>
                            <li>排行表</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="deptChart1" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="deptChart3" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <table id="deptRankTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab" lay-filter="deptFilter2">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="deptTab21"><span name="currentYear"></span>营销大区回款份额 <i class="layui-icon layui-icon-tips"
                                                                                                                  lay-tips="统计营销大区的合同在今年里收款金额的总和。合同年限不限，收款日期为今年。份额仅统计top15且不为0的数据，比例只做参考。"></i></li>
                            <li lay-id="deptTab22">营销大区回款top10</li>
                            <li>排行表</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="deptChart2" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="deptChart4" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <table id="deptReceiptRankTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab" lay-filter="contentFilter">
                        <ul class="layui-tab-title">
                            <li class="layui-this"><span name="currentYear"></span>产品线销售排行 <i class="layui-icon layui-icon-tips" lay-tips="统计营销大区今年销售的合同总额，年份以合同年限为准。"></i></li>
                            <li lay-id="prodLinePieDiv"><span name="currentYear"></span>产品线销售份额</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="prodLineBarChart" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="prodLinePieChart" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab" lay-filter="contentFilter2">
                        <ul class="layui-tab-title">
                            <li class="layui-this"><span name="currentYear"></span>合同类型销售排行<i class="layui-icon layui-icon-tips" lay-tips="统计营销大区的合同在今年里收款金额的总和。合同年限不限，收款日期为今年。以合同的营销大区为准。"></i></li>
                            <li lay-id="typePieDiv"><span name="currentYear"></span>合同类型销售份额</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="typeBarChart" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="typePieChart" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <span name="currentYear"></span>新增合同
                        <i class="layui-icon layui-icon-tips" lay-tips="合同年限为今年的合同列表。"></i>
                    </div>
                    <div class="layui-card-body" style="height: 530px;">
                        <table id="myContractTable"></table>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <select class="form-control input-sm selectStageType" style="display: inline-block;width: 200px;float: left;margin-top: 5px;color:#000000;box-shadow:none;border: none;">
                            <option value="1" selected> 今年全部合同阶段</option>
                            <option value="2"> 今年待收款合同阶段</option>
                            <option value="3"> 本月全部合同阶段</option>
                            <option value="4"> 本月待收款合同阶段</option>
                        </select>
                        <i class="layui-icon layui-icon-tips" lay-tips="以合同阶段信息的计划完成时间为统计标准，显示所有合同阶段。"></i>
                        <button type="button" class="btn btn-sm btn-default pull-right mt-5" onclick="StageConsole()">合同阶段总览</button>
                    </div>
                    <div class="layui-card-body" style="height: 350px;overflow-y: scroll;">
                        <table id="myContractStageTable"></table>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            <select class="form-control input-sm selectIncomeStageType"
                                    style="display: inline-block;width: 200px;float: left;margin-top: 5px;color:#000000;box-shadow:none;border: none;">
                                <option value="1" selected> 今年全部收入确认阶段</option>
                                <option value="2"> 今年待完成收入确认阶段</option>
                                <option value="3"> 本月全部收入确认阶段</option>
                                <option value="4"> 本月待完成收入确认阶段</option>
                            </select>
                            <i class="layui-icon layui-icon-tips" lay-tips="以收入确认阶段信息的计划完成时间为统计标准，显示收入确认阶段(未填写计划完成时间不统计)。"></i>
                            <button type="button" class="btn btn-sm btn-default pull-right mt-5" onclick="IncomeConsole()">收入确认阶段总览</button>
                        </div>
                        <div class="layui-card-body" style="height: 350px;">
                            <table id="myIncomeStageTable"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <script type="text/x-jsrender" id="bar3">
 		 <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="contractList.contractDetail" href="javascript:;">合同详情</a>

    </script>
    <script type="text/x-jsrender" id="receiptProgress">
			{{if AMOUNT>0}}
			<div class="layui-progress" lay-showPercent="true">
  				<div class="layui-progress-bar {{if AMOUNT == TOTAL_RECEIPT}} layui-bg-green {{else TOTAL_RECEIPT/AMOUNT <= 0.6}} layui-bg-red {{else TOTAL_RECEIPT/AMOUNT <= 0.8}} layui-bg-orange  {{else}} layui-bg-blue {{/if}}" lay-percent="{{:TOTAL_RECEIPT}}/{{:AMOUNT}}"></div>
			</div>
			{{/if}}


    </script>
    <script type="text/x-jsrender" id="receiptProgress2">
			{{if AMOUNT>0}}
			<div class="layui-progress" style="margin-top: 12px;height: 8px" lay-showPercent="true">
  				<div class="layui-progress-bar {{if AMOUNT == TOTAL_RECEIPT_ALL}} layui-bg-green {{else TOTAL_RECEIPT_ALL/AMOUNT <= 0.6}} layui-bg-red {{else TOTAL_RECEIPT_ALL/AMOUNT <= 0.8}} layui-bg-orange  {{else}} layui-bg-blue {{/if}}" lay-percent="{{:TOTAL_RECEIPT_ALL}}/{{:AMOUNT}}"></div>
			</div>
			{{/if}}

    </script>
</EasyTag:override>
<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script src="${ctxPath}/static/js/console-echarts.js"></script>
    <script type="text/javascript" src="/yq-work/static/js/contract-stat.js"></script>
    <script>
        var loadMap = {};

        $(function () {

            $("#searchForm").render({
                success: function () {


                    $("[name='currentYear']").text(new Date().getFullYear());

                    var curYear = new Date().getFullYear();
                    contractList.loadMonthChart1(curYear);
                    contractList.loadMonthChart2(curYear);
                    contractList.loadQuarterChart1(curYear);
                    contractList.loadQuarterChart2(curYear);
                    contractList.initContractReceiptRank(curYear);
                    contractList.initCustReceiptRank(curYear);
                    contractList.initSaleRankList(curYear);
                    contractList.initSaleReceiptRankList(curYear);
                    contractList.initDeptRankList();
                    contractList.initDeptReceiptRankList();


                    setTimeout(function () {
                        contractList.loadProdLineChart();
                        contractList.loadTypeChart();
                    }, 300)

                    setTimeout(function () {
                        contractList.initContractList();
                        contractList.initContractStageList();
                        contractList.initIncomeStageList();
                    }, 500)

                    var sumContract = parseFloat($('#SUM_CONTRACT').text());
                    var sumLastYear = parseFloat($('#SUM_LAST_YEAR').text());
                    var growthRatio = 0;
                    if (sumLastYear !== 0) {
                        growthRatio = ((sumContract - sumLastYear) / sumLastYear * 100).toFixed(2);
                    }
                    if (growthRatio > 0) {
                        $('#growthDiv').html(
                            '同比增长' + growthRatio + '%' +
                            '<span class="layui-edge layui-edge-top" ></span>'
                        );
                    } else if (growthRatio < 0) {
                        $('#growthDiv').html(
                            '同比降低' + growthRatio + '%' +
                            '<span class="layui-edge layui-edge-bottom"></span>'
                        );
                    }
                }
            });

            layui.element.on('tab(contentFilter)', function (data) {
                if (data.id == 'prodLinePieDiv') {
                    echarts.getInstanceByDom(document.getElementById('prodLinePieChart')).resize();
                }
            });
            layui.element.on('tab(contentFilter2)', function (data) {
                if (data.id == 'typePieDiv') {
                    echarts.getInstanceByDom(document.getElementById('typePieChart')).resize();
                }
            });
            layui.element.on('tab(deptFilter1)', function (data) {
                if (data.id == 'deptTab11') {
                    echarts.getInstanceByDom(document.getElementById('deptChart1')).resize();
                } else if (data.id == 'deptTab12') {
                    echarts.getInstanceByDom(document.getElementById('deptChart3')).resize();
                }
            });
            layui.element.on('tab(deptFilter2)', function (data) {
                if (data.id == 'deptTab21') {
                    echarts.getInstanceByDom(document.getElementById('deptChart2')).resize();
                } else if (data.id == 'deptTab22') {
                    echarts.getInstanceByDom(document.getElementById('deptChart4')).resize();
                }
            });

            layui.element.on('tab(monthFilter1)', function (data) {
                if (data.id == 'quarter1') {
                    echarts.getInstanceByDom(document.getElementById('quarterChart1')).resize();
                }else if (data.id == 'month1') {
                    echarts.getInstanceByDom(document.getElementById('monthChart1')).resize();
                }else if(data.id == 'saleButton'){
                    return false;
                }
            });
            $('#saleButton1').on('click', function(event){
                event.stopPropagation();
            });
            $('.select-year1 button').click(function () {
                var val = $(this)[0].value;
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                contractList.loadMonthChart1( new Date().getFullYear()-val);
                contractList.loadQuarterChart1( new Date().getFullYear()-val);
            });

            layui.element.on('tab(monthFilter2)', function (data) {
                if (data.id == 'quarter2') {
                    echarts.getInstanceByDom(document.getElementById('quarterChart2')).resize();
                }else if (data.id == 'month2') {
                    echarts.getInstanceByDom(document.getElementById('monthChart2')).resize();
                }else if(data.id == 'huiKuanButton'){
                    return false;
                }
            });
            $('#huiKuanButton1').on('click', function(event){
                event.stopPropagation();
            });
            $('.select-year2 button').click(function () {
                var val = $(this)[0].value;
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                contractList.loadMonthChart2( new Date().getFullYear()-val);
                contractList.loadQuarterChart2( new Date().getFullYear()-val);
            });


            $('.receipt-year-1 button').click(function () {
                var val = $(this)[0].value;
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                contractList.initContractReceiptRank( new Date().getFullYear()-val);
            });
            $('.receipt-year-2 button').click(function () {
                var val = $(this)[0].value;
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                contractList.initCustReceiptRank( new Date().getFullYear()-val);
            });


            $('.sale-year-1 button').click(function () {
                var val = $(this)[0].value;
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                contractList.initSaleRankList(new Date().getFullYear()-val);
            });

            $('.sale-year-2 button').click(function () {
                var val = $(this)[0].value;
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                contractList.initSaleReceiptRankList( new Date().getFullYear()-val);
            });

            $("[data-category]").click(function () {
                var type = $(this).data('category');
                if (type == '1') {
                    StageConsole();
                } else if (type == '2') {
                    IncomeConsole();
                }
            });
        });

        var contractList = {
            initContractReceiptRank: function (dataYear) {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.contractReceiptRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        autoSort: false,
                        data: {"receiptYear": dataYear},
                        id: 'contractReceiptRank',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'CONTRACT_SIMPILE_NAME',
                                title: '合同名称',
                                minWidth: 80,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractList.contractDetail',
                            }, {
                                field: 'TOTAL_RECEIPT_YEAR',
                                title: '回款金额',
                                width: 110,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_RECEIPT_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }, {
                                field: '',
                                title: '总收款进度',
                                minWidth: 160,
                                width: 180,
                                templet: function (row) {
                                    return renderTpl('receiptProgress2', row);
                                }
                            }
                        ]], done: function () {
                            layui.use(['element'], function () {
                                var element = layui.element;
                                element.render();
                            });
                        }
                    },
                )
            },

            initCustReceiptRank: function (dataYear) {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.custReceiptRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        data: {"receiptYear": dataYear},
                        autoSort: false,
                        id: 'custReceiptRank',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'CUSTOMER_NAME',
                                title: '客户',
                                minWidth: 120,
                                align: 'left',
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractList.custDetail',
                            }, {
                                field: 'TOTAL_RECEIPT_YEAR',
                                title: '回款金额',
                                width: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_RECEIPT_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }, {
                                field: '',
                                title: '总收款进度',
                                minWidth: 160,
                                width: 190,
                                templet: function (row) {
                                    return renderTpl('receiptProgress2', row);
                                }
                            }
                        ]], done: function () {
                            layui.use(['element'], function () {
                                var element = layui.element;
                                element.render();
                            });
                        }
                    },
                )
            },

            initSaleRankList: function (dataYear) {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.saleContractAmountRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        autoSort: false,
                        data: {"year": dataYear},
                        id: 'saleRankTable',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'SALES_BY',
                                title: '销售经理',
                                width:90,
                                align: 'center',
                                templet: function (d) {
                                    return getUserName(d.SALES_BY);
                                }
                            }, {
                                field: 'TOTAL_AMOUNT_THIS_YEAR',
                                title: dataYear+'销售额',
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 0, maximumFractionDigits: 0});
                                }
                            }, {
                                field: 'TOTAL_AMOUNT_LAST_YEAR',
                                title: (dataYear-1)+'销售额',
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 0, maximumFractionDigits: 0});
                                }
                            }
                        ]],
                        done: function (data) {
                            if ((data.othis.data.sortName) || data.pageNumber != 1 || data.pageSize > 20) {
                                return;
                            }
                            var data2 = data.data;
                            var saleName = data2.map(item => getUserName(item.SALES_BY));
                            var thisYearAmounts = data2.map(item => formatAmount(item.TOTAL_AMOUNT_THIS_YEAR));
                            var lastYearAmounts = data2.map(item => formatAmount(item.TOTAL_AMOUNT_LAST_YEAR));
                            loadTwoBarWithRatioLineChart(saleName, thisYearAmounts, lastYearAmounts, 'saleChart1', [dataYear+'',(dataYear-1)+'','增长率'], null, false);
                            loadMap["saleRankTable"] = true;
                        }
                    },
                )
            },

            initSaleReceiptRankList: function (dataYear) {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.salesReceiptRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        autoSort: false,
                        data: {"year": dataYear},
                        id: 'saleReceiptRankTable',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'SALES_BY',
                                title: '销售经理',
                                width:90,
                                align: 'center',
                                templet: function (d) {
                                    return getUserName(d.SALES_BY);
                                }
                            }, {
                                field: 'TOTAL_AMOUNT_THIS_YEAR',
                                title: (dataYear)+'回款额',
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 0, maximumFractionDigits: 0});
                                }
                            }, {
                                field: 'TOTAL_AMOUNT_LAST_YEAR',
                                title: (dataYear-1)+'回款额',
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 0, maximumFractionDigits: 0});
                                }
                            }
                        ]],
                        done: function (data) {
                            if ((data.othis.data.sortName) || data.pageNumber != 1 || data.pageSize > 20) {
                                return;
                            }
                            var data2 = data.data;
                            var saleName = data2.map(item => getUserName(item.SALES_BY));
                            var thisYearAmounts = data2.map(item => formatAmount(item.TOTAL_AMOUNT_THIS_YEAR));
                            var lastYearAmounts = data2.map(item => formatAmount(item.TOTAL_AMOUNT_LAST_YEAR));
                            loadTwoBarWithRatioLineChart(saleName, thisYearAmounts, lastYearAmounts, 'saleChart2', [dataYear+'',(dataYear-1)+'','增长率'], null, false);
                        }
                    },
                )
            },

            initDeptRankList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.deptContractAmountRank',
                        cellMinWidth: 50,
                        limit: 15,
                        height: '320px',
                        autoSort: false,
                        id: 'deptRankTable',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'SALE_DEPT_NAME',
                                title: '营销大区',
                                minWidth: 90,
                                align: 'center',
                            }, {
                                field: 'TOTAL_AMOUNT_THIS_YEAR',
                                title: '今年销售总金额',
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }, {
                                field: 'TOTAL_AMOUNT_LAST_YEAR',
                                title: '去年销售总额',
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }
                        ]],
                        done: function (data) {
                            if (loadMap["deptRankTable"]) {
                                return;
                            }
                            var data2 = data.data;
                            const echartsData = data2.map(item => {
                                return {
                                    name: item.SALE_DEPT_NAME,
                                    value: formatAmount(item.TOTAL_AMOUNT_THIS_YEAR)
                                };
                            });
                            var filteredData = echartsData.filter(function (item) {
                                return item.value !== 0 && item.value !== "0.00";
                            });

                            var saleDept = data2.slice(0, 10).map(item => item.SALE_DEPT_NAME);
                            var thisYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_THIS_YEAR));
                            var lastYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_LAST_YEAR));

                            loadPieChart(filteredData,"deptChart1",true);
                            loadTwoBarWithRatioLineChart(saleDept, thisYearAmounts, lastYearAmounts, 'deptChart3', ['今年','去年','增长率'], null, false);
                            loadMap["deptRankTable"] = true;

                        }
                    },
                )
            },

            initDeptReceiptRankList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.deptReceiptRank',
                        cellMinWidth: 50,
                        limit: 15,
                        height: '320px',
                        autoSort: false,
                        id: 'deptReceiptRankTable',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'SALE_DEPT_NAME',
                                title: '营销大区',
                                minWidth: 90,
                                align: 'center',
                            }, {
                                field: 'TOTAL_AMOUNT_THIS_YEAR',
                                title: '今年回款金额',
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }, {
                                field: 'TOTAL_AMOUNT_LAST_YEAR',
                                title: '去年年回款金额',
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }
                        ]],
                        done: function (data) {
                            if (loadMap["deptReceiptRankTable"]) {
                                return;
                            }
                            var data2 = data.data;
                            const echartsData = data2.map(item => {
                                return {
                                    name: item.SALE_DEPT_NAME,
                                    value: formatAmount(item.TOTAL_AMOUNT_THIS_YEAR)
                                };
                            });

                            var filteredData = echartsData.filter(function (item) {
                                return item.value !== 0 && item.value !== "0.00";
                            });

                            var saleDept = data2.slice(0, 10).map(item => item.SALE_DEPT_NAME);
                            var thisYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_THIS_YEAR));
                            var lastYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_LAST_YEAR));
                            loadPieChart(filteredData,"deptChart2",true);
                            loadTwoBarWithRatioLineChart(saleDept, thisYearAmounts, lastYearAmounts, 'deptChart4', ['今年','去年','增长率'], null, false);
                            loadMap["deptReceiptRankTable"] = true;
                        }
                    },
                )
            },

            initContractList: function () {
                $("#searchForm").initTable({
                    mars: 'ProjectContractDao.consoleContractList',
                    cellMinWidth: 50,
                    limit: 20,
                    data: {"userType": "all"},
                    height: '500px',
                    lineStyle: 'height: 60px;overflow: hidden;',
                    autoSort: false,
                    totalRow: true,
                    id: 'myContractTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            totalRowText: "合计"
                        }, {
                            field: '',
                            title: '操作',
                            width: 80,
                            align: 'center',
                            templet: function (row) {
                                return renderTpl('bar3', row);
                            },
                        }, {
                            field: 'CONTRACT_NO',
                            title: '合同号',
                            width: 110
                        }, {
                            field: 'CONTRACT_SIMPILE_NAME',
                            title: '合同名称',
                            minWidth: 120,
                        }, {
                            field: 'AMOUNT',
                            title: '合同金额',
                            align: 'right',
                            sort: true,
                            width: 120,
                            totalRow: true
                        }, {
                            field: '',
                            title: '收款进度',
                            minWidth: 160,
                            width: 180,
                            templet: function (row) {
                                return renderTpl('receiptProgress', row);
                            }
                        }, {
                            field: 'MIN_PLAN_COMP_DATE',
                            title: '下次收款日期',
                            minWidth: 120,
                            width: 160,
                            templet: function (row) {
                                return nextReceiptTime(row.AMOUNT, row.TOTAL_RECEIPT, row.MIN_PLAN_COMP_DATE);
                            }
                        }, {
                            field: 'CUSTOMER_NAME',
                            title: '客户名称',
                            sort: true,
                            width: 160
                        }, {
                            field: 'SALES_BY',
                            title: '销售经理',
                            width: 100,
                            sort: true,
                            align: 'center',
                            templet: function (d) {
                                return getUserName(d.SALES_BY);
                            }
                        }, {
                            field: 'SIGN_DATE',
                            title: '签订时间',
                            width: 110,
                            sort: true,
                            align: 'center'
                        },
                    ]], done: function () {
                        layui.use(['element'], function () {
                            var element = layui.element;
                            element.render();
                        });
                    }
                })
            },

            initContractStageList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStageDao.myContractStageList',
                        cellMinWidth: 50,
                        limit: 20,
                        data: {homePage: 0, "stageTime": "year", "stageType": "all"},
                        height: '320px',
                        autoSort: false,
                        totalRow: true,
                        id: 'myContractStageTable',
                        cols: [[
                            {
                                type: 'numbers',
                                align: 'left',
                                totalRowText: "合计"
                            }, {
                                field: 'CONTRACT_NAME',
                                title: '合同名称',
                                minWidth: 120,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractList.contractDetail',
                            }, {
                                field: 'STAGE_NAME',
                                title: '阶段名称',
                                align: 'center',
                                minWidth: 80,
                                width: 100,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractList.contractStageDetail',
                            }, {
                                title: '收款状态',
                                width: 80,
                                align: 'center',
                                templet: function (d) {
                                    return getStageCompleteStatus(d.PLAN_COMP_DATE, d.ACT_COMP_DATE, d.RCV_DATE);
                                }
                            }, {
                                field: 'RCV_DATE',
                                title: '应收日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'PLAN_COMP_DATE',
                                title: '计划完成日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'ACT_COMP_DATE',
                                title: '实际完成日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'PLAN_RCV_AMT',
                                title: '计划收款金额',
                                width: 110,
                                totalRow: true,
                                sort: true,
                            }, {
                                field: 'RECEIVED_AMT',
                                title: '已收款金额',
                                width: 110,
                                totalRow: true,
                                sort: true,
                            }, {
                                field: 'SALES_BY',
                                title: '销售经理',
                                width: 110,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    return getUserName(d.SALES_BY);
                                }
                            },
                        ]],
                        done: function (res) {

                        },
                    },
                )
            },

            initReceiptList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractReceiptDao.myReceiptList',
                        cellMinWidth: 50,
                        limit: 20,
                        data: {homePage: 0, "receiptTime": "year"},
                        height: '320px',
                        autoSort: false,
                        totalRow: true,
                        id: 'myReceiptTable',
                        cols: [[
                            {
                                field: '',
                                title: '操作',
                                width: 80,
                                align: 'center',
                                templet: function (row) {
                                    return renderTpl('bar3', row);
                                },
                                totalRowText: "合计"
                            }, {
                                field: 'RECEIPT_DATE',
                                title: '收款日期',
                                align: 'left',
                                width: 100,
                                sort: true
                            }, {
                                field: 'AMOUNT',
                                title: '收款金额',
                                totalRow: true,
                                width: 110,
                                sort: true
                            }, {
                                field: 'CONTRACT_NAME',
                                title: '合同名称',
                                minWidth: 120,
                            }, {
                                field: 'STAGE_NAME',
                                title: '合同阶段',
                                minWidth: 80,
                                width: 100,
                            },
                        ]]
                    },
                )
            },

            initIncomeStageList: function () {
                $("#searchForm").initTable({
                    mars: 'IncomeStageDao.allIncomeStageList',
                    id: 'myIncomeStageTable',
                    page: true,
                    cellMinWidth: 60,
                    height: '320px',
                    rowDoubleEvent(row) {
                        contractList.incomeStageDetail(row);
                    },
                    data: {'incomeStageType': "all"},
                    autoSort: false,
                    totalRow: true,
                    cols: [[
                        {
                            type: 'numbers',
                            align: 'left',
                            totalRowText: "合计"
                        }, {
                            field: 'CONTRACT_NAME',
                            title: '合同名称',
                            align: 'left',
                            minWidth: 120,
                            style: 'color:#1E9FFF;cursor:pointer',
                            event: 'contractList.contractDetail',
                        }, {
                            field: 'STAGE_NAME',
                            title: '收入确认阶段',
                            align: 'center',
                            width: 100,
                            style: 'color:#1E9FFF;cursor:pointer',
                            event: 'contractList.incomeStageDetail',
                        }, {
                            field: 'INCOME_CONF_FLAG',
                            title: '状态',
                            minWidth: 100,
                            width: 100,
                            templet: function (d) {
                                return getIncomeStageStatus(d.INCOME_CONF_FLAG, d.PLAN_COMP_DATE, d.ACT_COMP_DATE);
                            }
                        }, {
                            field: 'PLAN_COMP_DATE',
                            title: '计划完成时间',
                            align: 'left',
                            sort: true,
                            width: 110
                        }, {
                            field: 'ACT_COMP_DATE',
                            title: '实际完成日期A',
                            align: 'left',
                            sort: true,
                            width: 120
                        }, {
                            field: 'PLAN_AMOUNT',
                            title: '计划收款金额(含税)',
                            minWidth: 130,
                            totalRow: true,
                            sort: true,
                        }, {
                            field: 'AMOUNT_NO_TAX',
                            title: '计划收款金额(税后)',
                            minWidth: 130,
                            totalRow: true,
                            sort: true,
                        },
                        {
                            field: 'UNCONFIRM_NO_TAX_A',
                            title: '未确认金额A(税后)',
                            width: 130,
                            totalRow: true,
                            sort: true,
                        }, {
                            field: 'REMARK',
                            title: '备注',
                            width: 120
                        }
                    ]],
                    done: function (res) {

                    }
                });

            },

            contractDetail: function (data) {
                if (isArray(data)) {
                    data = data[0];
                }
                popup.openTab({id: 'contractDetail', title: '合同详情', url: '${ctxPath}/project/contract', data: {contractId: data.CONTRACT_ID, custId: data['CUST_ID'], isDiv: 0}});
            },
            custDetail: function (data) {
                popup.openTab({id: 'custDetail', title: '客户详情', url: '${ctxPath}/pages/crm/cust/cust-detail.jsp', data: {custId: data['CUST_ID']}});
            },
            incomeStageDetail: function (data) {
                var incomeStageId = data.INCOME_STAGE_ID;
                var contractId = data.CONTRACT_ID;
                popup.layerShow({
                    type: 1,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    title: '收入确认阶段详情',
                    offset: 'r',
                    area: ['800px', '100%'],
                    url: '${ctxPath}/pages/crm/contract/include/income-stage-detail.jsp',
                    data: {contractId: contractId, incomeStageId: incomeStageId}
                });
            },
            contractStageDetail: function (data) {
                var contractId = data.CONTRACT_ID;
                var stageId = data.STAGE_ID;
                popup.layerShow({
                    type: 1,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    title: '合同阶段详情',
                    offset: 'r',
                    area: ['800px', '100%'],
                    url: '${ctxPath}/pages/crm/contract/include/stage-detail.jsp',
                    data: {contractId: contractId, stageId: stageId}
                });
            },

            loadProdLineChart: function () {
                ajax.remoteCall("/yq-work/webcall?action=ContractStatisticDao.prodLineContractAmountRank", {}, function (result) {
                    var data = result.data;

                    var prodLines = data.map(item => item.PROD_LINE);
                    var thisYearAmounts = data.map(item => formatAmount1(item.TOTAL_AMOUNT_THIS_YEAR));
                    var lastYearAmounts = data.map(item => formatAmount1(item.TOTAL_AMOUNT_LAST_YEAR));

                    const echartsData = data.map(item => {
                        return {
                            name: item.PROD_LINE,
                            value: formatAmount(item.TOTAL_AMOUNT_THIS_YEAR)
                        };
                    });
                    var filteredData = echartsData.filter(function (item) {
                        return item.value !== 0 && item.value !== "0.00";
                    });

                    loadPieChart(filteredData,"prodLinePieChart",true);
                    loadTwoBarChart2(prodLines, thisYearAmounts, lastYearAmounts, 'prodLineBarChart', ['今年','去年']);
                })
            },

            loadTypeChart: function () {
                ajax.remoteCall("/yq-work/webcall?action=ContractStatisticDao.typeContractAmountRank", {}, function (result) {
                    var data = result.data;

                    var typeNames = data.map(item => item.CONTRACT_TYPE);
                    var thisYearAmounts = data.map(item => formatAmount1(item.TOTAL_AMOUNT_THIS_YEAR));
                    var lastYearAmounts = data.map(item => formatAmount1(item.TOTAL_AMOUNT_LAST_YEAR));

                    const echartsData = data.map(item => {
                        return {
                            name: item.CONTRACT_TYPE,
                            value: formatAmount(item.TOTAL_AMOUNT_THIS_YEAR)
                        };
                    });

                    var filteredData = echartsData.filter(function (item) {
                        return item.value !== 0 && item.value !== "0.00";
                    });

                    loadPieChart(filteredData,"typePieChart",true);
                    loadTwoBarChart2(typeNames, thisYearAmounts, lastYearAmounts, 'typeBarChart', ['今年','去年']);
                })
            },


            loadMonthChart1: function (curYear) {
                var lastYear = curYear - 1;
                ajax.remoteCall("/yq-work/webcall?action=ContractStatisticDao.contractAmountStatByMonth", {"curYear": curYear}, function (result) {
                    var data2 = result.data;
                    var months = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"];
                    var thisYearAmounts = months.map(month => formatAmount(data2[curYear + "_" + month] || "0"));
                    var lastYearAmounts = months.map(month => formatAmount(data2[lastYear + "_" + month] || "0"));
                    loadTwoBarWithRatioLineChart(months, thisYearAmounts, lastYearAmounts, 'monthChart1', [curYear + '', lastYear + '','相对增长率'],null,false)
                });
            },
            loadMonthChart2: function (curYear) {
                var lastYear = curYear - 1;
                ajax.remoteCall("/yq-work/webcall?action=ContractStatisticDao.receiptAmountStatByMonth", {"curYear": curYear}, function (result) {
                    var data2 = result.data;
                    var months = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"];
                    var thisYearAmounts = months.map(month => formatAmount(data2[curYear + "_" + month] || "0"));
                    var lastYearAmounts = months.map(month => formatAmount(data2[lastYear + "_" + month] || "0"));
                    loadTwoBarWithRatioLineChart(months, thisYearAmounts, lastYearAmounts, 'monthChart2', [curYear+'',lastYear+'','相对增长率'],null,false);
                })

            },

            loadQuarterChart1: function (curYear) {
                var lastYear = curYear - 1;
                ajax.remoteCall("/yq-work/webcall?action=ContractStatisticDao.contractAmountStatByQuarter", {"curYear": curYear}, function (result) {
                    var data2 = result.data;
                    var quarters = ["Q1", "Q2", "Q3", "Q4"];
                    var thisYearAmounts = quarters.map(quarter => formatAmount(data2[curYear + "_" + quarter] || "0"));
                    var lastYearAmounts = quarters.map(quarter => formatAmount(data2[lastYear + "_" + quarter] || "0"));
                    loadTwoBarWithRatioLineChart(quarters,thisYearAmounts,lastYearAmounts,"quarterChart1",[curYear+'',lastYear+'','相对增长率'],null,false)
                });

            },


            loadQuarterChart2: function (curYear) {
                var lastYear = curYear - 1;
                ajax.remoteCall("/yq-work/webcall?action=ContractStatisticDao.receiptAmountStatByQuarter", {"curYear": curYear}, function (result) {
                    var data2 = result.data;
                    var quarters = ["Q1", "Q2", "Q3", "Q4"];
                    var thisYearAmounts = quarters.map(quarter => formatAmount(data2[curYear + "_" + quarter] || "0"));
                    var lastYearAmounts = quarters.map(quarter => formatAmount(data2[lastYear + "_" + quarter] || "0"));
                    loadTwoBarWithRatioLineChart(quarters,thisYearAmounts,lastYearAmounts,"quarterChart2",[curYear+'',lastYear+'','相对增长率'],null,false)
                });
            }

        }

        $('.selectStageType').on('change', function () {
            var value = $(this).val();

            if (value === "1") {
                $('input[name="stageTime"]').val("year");
                $('input[name="stageType"]').val("all");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            } else if (value === "2") {
                $('input[name="stageTime"]').val("year");
                $('input[name="stageType"]').val("unComp");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            } else if (value === "3") {
                $('input[name="stageTime"]').val("month");
                $('input[name="stageType"]').val("all");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            } else if (value === "4") {
                $('input[name="stageTime"]').val("month");
                $('input[name="stageType"]').val("unComp");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            }

        });

        $('.selectIncomeStageType').on('change', function () {
            var value = $(this).val();
            if (value === "1") {
                $('input[name="dateField2"]').val("year");
                $('input[name="incomeStageType"]').val("all");
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {}});
            } else if (value === "2") {
                $('input[name="dateField2"]').val("year");
                $('input[name="incomeStageType"]').val("unConfirm");
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {}});
            } else if (value === "3") {
                $('input[name="dateField2"]').val("month");
                $('input[name="incomeStageType"]').val("all");
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {}});
            } else if (value === "4") {
                $('input[name="dateField2"]').val("month");
                $('input[name="incomeStageType"]').val("unConfirm");
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {}});
            }
        });

        $('.selectReceiptType').on('change', function () {
            var value = $(this).val();
            $('input[name="receiptTime"]').val(value);
            $("#searchForm").queryData({id: 'myReceiptTable'}, {data: {}});
        });


        function IncomeConsole() {
            popup.openTab({id: 'incomeConsole', title: '全部收入确认', url: '${ctxPath}/pages/crm/contract/statistic/income-console.jsp',});
        }

        function StageConsole() {
            popup.openTab({id: 'StageConsole', title: '全部合同阶段', url: '${ctxPath}/pages/crm/contract/statistic/stage-console.jsp',});
        }

        function formatAmount(amount) {
            return (parseFloat(amount) / 10000).toFixed(2);
        }

        function formatAmount1(amount) {
            return (parseFloat(amount) / 10000).toFixed(1);
        }

        function formatAmount0(amount) {
            var num = (parseFloat(amount) / 10000).toFixed(1);
            return num.substring(0, num.length - 2);
        }


    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>