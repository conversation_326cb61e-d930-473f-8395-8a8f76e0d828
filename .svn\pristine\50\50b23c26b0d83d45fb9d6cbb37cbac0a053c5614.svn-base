package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ProjectModel;

public class OAContractSynService extends AppBaseService{
	
	private Logger logger = LogEngine.getLogger(Constants.APP_NAME);
	
	private static class Holder{
		private static OAContractSynService service=new OAContractSynService();
	}
	public static OAContractSynService getService(){
		return Holder.service;
	}
	
	public void synData(){
        try {
           OAService.getService().login("100133", "123456");
           String xml = OAService.getService().loadContractXml("100133");
           if(xml==null){
        	   return;
           }
            xml = xml.replaceAll("\n", "");
            xml = xml.replaceAll("\t", "");
       	    SAXReader reader=new SAXReader();
			 Document doc = DocumentHelper.parseText(xml);
			 Element root = doc.getRootElement();
			 List<Element> childElements  = root.elements("Item");
			 for (Element child : childElements) {
				 Element attr=child.element("ItemAttributes");
				 if(attr!=null){
					 ProjectModel model=new ProjectModel();
					 String accCode= StringUtils.trimToEmpty(attr.elementText("acc_code"));
					 try {
						if(this.getQuery().queryForExist("select count(1) from YQ_PROJECT where PROJECT_NO = ?", accCode)){
							continue; 
						 }
					} catch (SQLException e1) {
						e1.printStackTrace();
					}
					 model.set("PROJECT_NO", StringUtils.trimToEmpty(attr.elementText("acc_code")));
					 model.set("PROJECT_NAME", StringUtils.trimToEmpty(attr.elementText("acc_desc")));
					 model.set("PROJECT_ID", RandomKit.uniqueStr());
					try {
						model.save();
					} catch (SQLException e) {
						logger.error(e.getMessage(),e);
					}
				 }
			 }
		} catch (DocumentException e) {
			logger.error(e.getMessage(),e);
		}
	}
	
}







