<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
		.operation-mode{display:none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="businessEditForm" class="form-horizontal" data-mars="CustDao.busniessInfo" autocomplete="off" data-mars-prefix="business.">
 		   		<input type="hidden" value="${param.businessId}" name="businessId"/>
 		   		<input type="hidden" value="${param.businessId}" name="business.BUSINESS_ID"/>
				<input type="hidden" value="${param.businessType}" name="business.BUSINESS_TYPE"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td class="required">客户名称</td>
		                    <td colspan="3">
		                    	<input type="hidden" name="business.CUST_ID" value="${param.custId}"/>
								<input data-rules="required" value="${param.custName}" type="text" readonly="readonly" id="_custId" placeholder="请点击选择客户"  name="business.CUSTOMER_NAME" class="form-control input-sm"/>
		                    </td>
			            </tr>
			            <tr>
		                    <td width="100px" class="required">商机简称</td>
		                    <td colspan="3">
		                   		 <input data-rules="required" type="text" name="business.BUSINESS_NAME" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
		                    <td width="110px" class="required">跟进阶段</td>
		                    <td style="width: 32%;">
		                   		 <select data-rules="required" name="business.BUSINESS_STAGE" class="form-control input-sm">
		                   		 	<option value="">请选择</option>
		                   		 	<option value="10">沟通交流</option>
			              		 	<option value="20">立项阶段</option>
			              		 	<option value="25">POC阶段</option>
			              		 	<option value="30">投标阶段</option>
			              		 	<option value="40">商务阶段</option>
			              		 	<option value="50">交付阶段</option>
		                   		 </select>
		                    </td>
		                    <td width="100px"  class="required">商机结果</td>
		                    <td>
		                    	 <select data-rules="required" name="business.BUSINESS_RESULT"  class="form-control input-sm">
		                    	 	<option value="0">跟进中</option>
		                    	 	<option value="99">中止</option>
		                    	 	<option value="50">输单</option>
		                    	 	<option value="60">赢单</option>
		                    	 </select>
		                    </td>
			            </tr>
			            <tr>
		                    <td width="110px" class="required">销售区域</td>
		                    <td style="width: 32%;">
		                   		 <input data-rules="required" onclick="singleDept(this)" data-dept-name="营销"  name="business.BUSINESS_AREA" class="form-control input-sm">
		                    </td>
		                    <td width="100px"  class="required">销售经理</td>
		                    <td>
		                    	 <input name="business.SALES_BY" type="hidden">
		                   		<input data-rules="required" name="business.SALES_BY_NAME" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm">
		                    </td>
			            </tr>
						<tr class="operation-mode" style="display:none;">
							<td>业务平台</td>
							<td>
								<input type="hidden" value="${param.platformId}" name="business.PLATFORM_ID"/>
								<input type="text" value="${param.platformName}" name="business.PLATFORM_NAME" class="form-control input-sm" readonly onclick="singleBusinessPlatForm(this)" placeholder="请点击选择平台"/>
							</td>
							<td>平台参数</td>
							<td>
								<input type="text" name="business.PLATFORM_PARAMS" placeholder="根据选择的平台填写对应的参数" class="form-control input-sm">
							</td>
						</tr>
						<tr class="operation-mode">
							<td>预计赢单时间</td>
							<td>
								<input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="business.EXPECTED_WIN_DATE" class="form-control input-sm Wdate">
							</td>
							<td>实际赢单时间</td>
							<td>
								<input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="business.ACTUAL_WIN_DATE" class="form-control input-sm Wdate">
							</td>
						</tr>
						<tr class="operation-mode">
							<td>预计成本(万)</td>
							<td colspan="3">
								<input type="number" name="business.COST_BUDGET" value="0" class="form-control input-sm">
							</td>
						</tr>
			            <tr>
		                    <td class="required">预计金额(万)</td>
		                    <td>
		                   		 <input data-rules="required" type="number" name="business.AMOUNT" onchange="calcAmount(this)" class="form-control input-sm">
		                    </td>
		                    <td class="required">可能性</td>
		                    <td>
		                   		 <select data-rules="required" name="business.POSSIBILITY" class="form-control input-sm">
		                   		 	<option value="">请选择</option>
		                   		 	<option value="10">10%</option>
		                   		 	<option value="20">20%</option>
		                   		 	<option value="30">30%</option>
		                   		 	<option value="40">40%</option>
		                   		 	<option value="50">50%</option>
		                   		 	<option value="60">60%</option>
		                   		 	<option value="70">70%</option>
		                   		 	<option value="80">80%</option>
		                   		 	<option value="90">90%</option>
		                   		 	<option value="100">100%</option>
		                   		 </select>
		                    </td>
						</tr>
						<tr>
		                    <td>商机内容</td>
		                    <td colspan="3">
		                    	 <textarea style="height: 100px;" name="business.REMARK" class="form-control input-sm"></textarea>
		                    </td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="CustBusiness.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
   
		layui.config({
			  base: '${ctxPath}/static/js/'
		}).use('tableSelect');
	
		jQuery.namespace("CustBusiness");
	    
		CustBusiness.businessId='${param.businessId}';
		CustBusiness.businessType='${param.businessType}';

		$(function(){
			$("#businessEditForm").render({success:function(result){
				 requreLib.setplugs('select2',function(){
					$("#businessEditForm select").select2({theme: "bootstrap",placeholder:'请选择'});
					$(".select2-container").css("width","100%");
			 	 });

				 if(CustBusiness.businessType == '2'){
					$('.operation-mode').show();
				 }
			}}); 
			initSelectCust();
		});
		
		CustBusiness.ajaxSubmitForm = function(){
			if(form.validate("#businessEditForm")){
				var data = form.getJSONObject("#businessEditForm");
				if(CustBusiness.businessType == '2'){
					var platformId = $("[name='business.PLATFORM_ID']").val();
					if(!platformId){
						layer.msg('请选择业务平台',{icon:7});
						return;
					}
					var cost = $("[name='business.COST_BUDGET']").val();
					if(!cost){
						layer.msg('请填写预计成本！',{icon:7});
						return;
					}
				} else {
					$('.operation-mode input').val('');
					delete data['business.COST_BUDGET'];
				}
				
				if(CustBusiness.businessId){
					CustBusiness.updateData(data); 
				}else{
					CustBusiness.insertData(data); 
				}
			};
		}
		CustBusiness.insertData = function(data) {
			ajax.remoteCall("${ctxPath}/servlet/cust?action=addBusiness",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('businessEditForm');
						reloadBusinessList();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		CustBusiness.updateData = function(data) {
			ajax.remoteCall("${ctxPath}/servlet/cust?action=updateBusiness",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('businessEditForm');
						reloadBusinessList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function initSelectCust(){
			layui.use(['form','tableSelect'], function(){
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#_custId',
					searchKey: 'custName',
					checkedKey: 'CUST_ID',
					page:true,
					data:{selectTable:'1'},
					searchPlaceholder: '请输入客户名称',
					table: {
						mars: 'CustDao.selectCustList',
						cols: [[
						        { type: 'radio' },
						        { field: 'CUST_NO',title: '编号'},
						        { field: 'CUST_NAME', title: '名称', width: 320 }
						        ]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.CUST_NAME)
							ids.push(item.CUST_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
					},
					clear:function(elem){
						elem.prev().val("");
					}
				});
			});
		}
		
		function calcAmount(el){
			var val = $(el).val();
			if(val>5000){
				$(el).val('');
				layer.msg('单位是万');
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>