package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name = "IncomeConfirmDao")
public class IncomeConfirmDao extends AppDaoContext {

    @WebControl(name = "record", type = Types.RECORD)
    public JSONObject record() {
        String confirmId = param.getString("confirm.CONFIRM_ID");
        if (StringUtils.notBlank(confirmId)) {
            return queryForRecord("select * from yq_contract_income_confirm where CONFIRM_ID = ?", confirmId);
        } else {
            return getJsonResult(new JSONObject());
        }
    }

    @WebControl(name = "incomeConfirmList", type = Types.LIST)
    public JSONObject incomeConfirmList() {
        EasySQL sql = getEasySQL("select t1.* from yq_contract_income_confirm t1");
        sql.append("where 1=1");
        sql.append(param.getString("contractId"), " and t1.CONTRACT_ID = ?");
        sql.append(param.getString("confirmType")," and t1.CONFIRM_TYPE = ?");
        sql.append(param.getString("incomeStageId")," and t1.INCOME_STAGE_ID = ?");
        sql.append("order by t1.CONFIRM_DATE ASC");
        return queryForList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name  = "myIncomeConfirmList" , type = Types.LIST)
    public JSONObject myIncomeConfirmList(){
        EasySQL sql = getEasySQL("select t1.*,t2.CONTRACT_NAME,t2.CONTRACT_SIMPILE_NAME,t3.STAGE_NAME from yq_contract_income_confirm t1 ");
        sql.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("left join yq_contract_income_stage t3 ON t3.INCOME_STAGE_ID = t1.INCOME_STAGE_ID");
        sql.append("where 1=1 and CONFIRM_TYPE = 'A'");
        if ("year".equals(param.getString("confirmTime"))) {
            sql.append(EasyCalendar.newInstance().getYear(), "and t1.YEAR_ID = ?");
        } else if ("month".equals(param.getString("confirmTime"))) {
            sql.append(EasyCalendar.newInstance().getFullMonth(), "and t1.MONTH_ID = ?");
        }
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  t2.SALES_BY = ? ");
        }
        setOrderBy(sql, " order by CONFIRM_DATE desc");
        return queryForPageList(sql.getSQL(), sql.getParams());

    }

    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by t1.").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }

}
