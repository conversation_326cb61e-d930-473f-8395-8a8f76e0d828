<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>
		.layui-elem-quote{line-height: 1.7;padding: 8px;font-size: 13px;}
		.el-timeline-item:hover .editFn{opacity: 1;text-decoration: underline;}
		.el-timeline{margin:0;font-size:14px;list-style:none}.el-timeline .el-timeline-item:last-child .el-timeline-item__tail{display:none}.el-timeline-item{position:relative;padding-bottom:20px}.el-timeline-item__wrapper{position:relative;padding-left:28px;top:-3px}.el-timeline-item__tail{position:absolute;left:4px;height:100%;border-left:2px solid #e4e7ed}.el-timeline-item__icon{color:#fff;font-size:13px}.el-timeline-item__node{position:absolute;background-color:#e4e7ed;border-radius:50%;display:flex;justify-content:center;align-items:center}.el-timeline-item__node--normal{left:-1px;width:12px;height:12px}.el-timeline-item__node--large{left:-2px;width:14px;height:14px}.el-timeline-item__node--primary{background-color:#409eff}.el-timeline-item__node--success{background-color:#67c23a}.el-timeline-item__node--warning{background-color:#e6a23c}.el-timeline-item__node--danger{background-color:#f56c6c}.el-timeline-item__node--info{background-color:#909399}.el-timeline-item__dot{position:absolute;display:flex;justify-content:center;align-items:center}.el-timeline-item__content{color:#303133}.el-timeline-item__timestamp{color:#909399;line-height:1;font-size:13px}.el-timeline-item__timestamp.is-top{margin-bottom:8px;padding-top:4px}.el-timeline-item__timestamp.is-bottom{margin-top:8px}.el-card{border-radius:4px;border:1px solid #ebeef5;background-color:#fff;overflow:hidden;color:#303133;transition:.3s}.el-card.is-always-shadow,.el-card.is-hover-shadow:focus,.el-card.is-hover-shadow:hover{box-shadow:0 2px 12px 0 rgba(0,0,0,.1)}.el-card__header{padding:18px 20px;border-bottom:1px solid #ebeef5;box-sizing:border-box}.el-card__body{padding:20px}
		.layui-table-cell{padding: 0 6px;}
		.fa-filter{color: #00abfc;cursor: pointer;}
		#container{margin: 15px;}
		.itemTitle{color: #f1093f;}
		#exportData{display: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="weeklyForm" data-page-hide="true">
			<div class="ibox">
					<div class="ibox-content">
						<div class="layui-tab layui-tab-brief pt-10" lay-filter="filterData" >
						  <ul class="layui-tab-title">
						    <li class="layui-this" data-flag="1">表格视图</li>
						    <li data-flag="2">详情视图</li>
						    <li data-flag="3">项目统计</li>
						    <li data-flag="4">项目视图</li>
						  </ul>
						 <div class="mt-10">
					    	<div class="input-group input-group-sm">
							    <span class="input-group-addon">状态</span>	
								<select name="status" onchange="list.query();" class="form-control input-sm">
								      <option value="">请选择 </option>
	                    			  <option value="">全部</option>
	                    			  <option value="1">未读</option>
	                    			  <option value="2">已读</option>
							    </select>									  
						  	</div>
						  	<div class="input-group input-group-sm">
							    <span class="input-group-addon">姓名</span>	
						    	<input type="text" style="width: 80px" id="userName" name="userName" class="form-control input-sm mb-10" placeholder="姓名"/>
							 </div>
							 <div class="input-group input-group-sm">
							    <span class="input-group-addon">部门</span>	
							    	<input type="text" style="width: 80px;" name="depts" class="form-control input-sm mb-10" placeholder="部门"/>
							 </div>
							 <div class="input-group input-group-sm" id="dataContainer" style="width: 310px">
		          		    	<span class="input-group-addon">工作日期</span>	
		          		    	<input id="nowWeekly" type="hidden" data-mars-reload="false" data-mars="CommonDao.nowWeekly">
		          		    	<input id="prevWeekly" type="hidden" data-mars-reload="false" data-mars="CommonDao.prevWeekly">
	                     		<input data-mars="CommonDao.prevWeekly" data-mars-reload="false" style="border-right: 0;" type="text" data-laydate="{type:'date',range: '到'}" name="updateDate" class="form-control input-sm">
	                     		<div class="input-group-btn">
		                     		<select class="form-control input-sm" style="width:80px;" onchange="getDate(this,'updateDate')">
			                     		<option value="">全部</option>
			                     		<option value="nowWeekly">本周</option>
			                     		<option value="prevWeekly" selected="selected">上周</option>
			                     		<option value="currentMonth">本月</option>
			                     		<option value="threeDay">三天内</option>
			                     		<option value="oneWeek">一周内</option>
			                     		<option value="oneMonth">一个月内</option>
			                     		<option value="threeMonth">三个月内</option>
			                     	</select>
	                     	   </div>
	                    	 </div>
					    	<button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					    	<button type="reset" class="btn btn-sm btn-default"> 清空 </button>
					    	<button type="button" id="exportData" onclick="exportWeeklyData();" class="btn btn-sm btn-info pull-right"> 导出 </button>
						 </div>
						  <div class="layui-tab-content" style="margin: -15px -15px;min-height: calc(100vh - 100px)">
						    <div class="layui-tab-item layui-show mt-5">
						    	<table class="layui-hide mt-15" id="recentListTable"></table> 
						    </div>
						    <div class="layui-tab-item" style="padding: 10px 15px;" data-mars="WeeklyDao.myReceiveWeeklyList" data-container="container2" data-template="detail-template">
							    <ul class="el-timeline" id="container2"></ul>
							    <div id="pageContainer2" class="ml-30"></div>
						    </div>
						    <div class="layui-tab-item" style="padding: 10px 15px;">
						    	<table class="layui-hide mt-15" id="weeklyProjectStat"></table> 
						    </div>
						    <div class="layui-tab-item" data-mars="WeeklyDao.weeklyProjectList" id="weeklyProjectItem" data-container="container" data-template="follow-template">
							    <ul class="el-timeline" id="container"></ul>
							    <div id="pageContainer" class="ml-30"></div>
						    </div>
						  </div>
						</div>
					
					</div>
				</div>
		</form>
	
		 <script id="detail-template" type="text/x-jsrender">
						{{for data}}
							 <li class="el-timeline-item">
								<div class="el-timeline-item__tail"></div>
								<div class="el-timeline-item__node el-timeline-item__node--normal el-timeline-item__node--primary">
								</div>
								<div class="el-timeline-item__wrapper">
									<div class="el-timeline-item__timestamp is-top">
										{{:CREATE_TIME}} 
									</div>
									<div class="el-timeline-item__content">
										<div class="el-card is-always-shadow">
											<div class="el-card__body">
												<p onclick="list.look('{{:WEEKLY_ID}}');" style="font-size:13px;margin-top:-10px;">
													<b> {{:#index+1}}、{{:DEPTS}}.{{:CREATE_NAME}}</b> 提交于 {{:SEND_TIME}}
													<span class="pull-right">
													{{:TITLE}}
													{{if WORK_DAY}}，工时：{{:WORK_DAY}}天{{/if}}
 													  <a href="javascript:;" style="color:#4ba2ec;cursor:pointer;" class="editFn" onclick="list.look('{{:WEEKLY_ID}}')">详情</a>
													</span>
												</p>
												<p class="itemTitle mt-10">本周工作内容：</p>
												<h4 style="font-size:14px;color:#444;margin-top:5px;line-height:26px;">{{:ITEM_1}}</h4>
												<p class="itemTitle">下周计划：</p>
												<h4 style="font-size:14px;color:#444;margin-top:5px;line-height:26px;">{{:ITEM_2}}</h4>
												<p class="itemTitle">问题/帮助/建议/反映：</p>
												<h4 style="font-size:14px;color:#444;margin-top:5px;line-height:26px;">{{call:ITEM_3 fn='contentFn'}}</h4>
											</div>
										</div>
									</div>
								</div>
							</li>
						{{/for}}
						{{if data.length==0}}
							<div calss="text-c" style="height:200px;line-height:200px;text-align:center;font-size:18px;"><i class="fa fa-info-circle"></i>&nbsp;暂无数据</div>
						{{/if}}
		</script>
		
		 <script id="follow-template" type="text/x-jsrender">
						{{for data}}
							 <li class="el-timeline-item">
								<div class="el-timeline-item__tail"></div>
								<div class="el-timeline-item__node el-timeline-item__node--normal el-timeline-item__node--primary">
								</div>
								<div class="el-timeline-item__wrapper">
									<div class="el-timeline-item__timestamp is-top">
										{{:CREATE_TIME}}
									</div>
									<div class="el-timeline-item__content">
										<div class="el-card is-always-shadow">
											<div class="el-card__body">
												<p onclick="list.look('{{:WEEKLY_ID}}');" style="font-size:13px;color:#4ba2ec;margin-top:-10px;cursor:pointer;">{{:TITLE}} {{if PROJECT_NAME}}<span class="ml-5 mr-5">/</span>{{:PROJECT_NAME}}{{/if}}</p>
												<h4 style="font-size:14px;color:#444;margin-top:5px;line-height:26px;">{{call:WORK_CONTENT fn='getContent'}}</h4>
												<p style="font-size:12px;color:#999;margin-top:5px;">{{:CREATE_NAME}} 提交于{{:CREATE_TIME}} ,&nbsp; 周段 {{:WEEK_BEGIN}} ~ {{:WEEK_END}}
													{{if WORK_DAY}}，工时：{{:WORK_DAY}}天{{/if}}
 													<a href="javascript:;" class="editFn" onclick="list.look('{{:WEEKLY_ID}}')">详情</a>
												</p>
											</div>
										</div>
									</div>
								</div>
							</li>
						{{/for}}
						{{if data.length==0}}
							<div calss="text-c" style="height:200px;line-height:200px;text-align:center;font-size:18px;"><i class="fa fa-info-circle"></i>&nbsp;暂无数据</div>
						{{/if}}
		</script>
		<script type="text/x-jsrender" id="bar">
			{{if RECEIVER == currentUserId && STATUS == 1}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">审批</a>
			{{else}}
				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看</a>
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script src="/yq-work/static/js/xlsx.core.min.js" type="text/javascript"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
	<script type="text/javascript">
		
		layui.use('element', function(){
			  var element = layui.element;
			  element.render();
		});

		var list={
			recentList:function(){
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.myReceiveWeeklyList',
					id:'recentListTable',
					height:'full-120',
					cellMinWidth:100,
					limit:30,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
						 field:'WEEK_NO',
						 title:'周数',
						 width:80,
						 templet:'<div>{{d.YEAR}}/{{d.WEEK_NO}}</div>'
					 },{
					    field: 'CREATE_NAME',
						title: '填写人',
						align:'left',
						width:160,
						templet:function(row){
							return row.DEPTS+"."+row['CREATE_NAME']+'<a href="javascript:getThisUserData(\''+row['CREATE_NAME']+'\')" class="pull-right"><i class="fa fa-filter" aria-hidden="true"></i></a>';
						}
					   }
					 ,{
					    field: 'TITLE',
						title: '标题',
						minWidth:250,
						align:'left',
						event:'list.detail',
						style:'color:#1E9FFF;cursor:pointer',
						templet:function(row){
							if(row.TYPE=='1'){
								return row.TITLE;
							}else{
								return "<span class='layui-badge-rim'>抄送</span>&nbsp;"+row.TITLE;
							}
						}
					},{
						title: '工作量',
						field:'ITEM_5',
						align:'left',
						width:90,
					 }
					 ,{
					    field: 'RECEIVER_NAME',
						title: '主送人',
						align:'left',
						width:80
					 },{
					    field: 'CC_NAMES',
						title: '抄送人',
						align:'left',
						width:100
					 }
					,{
					    field: 'SEND_TIME',
						title: '收到时间',
						align:'left',
						width:200,
						templet:function(row){
							if(getCurrentUserId()==row.RECEIVER||isSuperUser){
								var status=row.STATUS;
								if(status==2){
									status='<span class="layui-badge layui-bg-green">已阅</span> ';
								}else{
									status='<span class="layui-badge">待阅</span> ';
								}
								return status+row.SEND_TIME;
							}else{
								return row.SEND_TIME;
							}
						}
					},{
						title: '操作',
						align:'left',
						width:90,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]],done:function(result){
						$("#num").text(result['totalRow']);
						
					}}
				);
			},
			weeklyProjectStat:function(){
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.weeklyProjectListStat',
					id:'weeklyProjectStat',
					height:'full-120',
					cellMinWidth:100,
					limit:30,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
						 field:'PROJECT_NAME',
						 title:'周数',
						 minWidth:250
					 },{
					    field: 'COUNT',
						title: '填写次数',
						align:'left',
						width:90
					 },{
					    field: 'USER_NAMES',
						title: '填写人',
						align:'left',
						width:160
					 },{
					    field: 'WORK_DAY',
						title: '消耗工时/天',
						align:'left',
						width:120
					 }
					]],done:function(result){
						
					}}
				);
			},
			query:function(){
				$("#weeklyForm").queryData({id:'recentListTable',jumpOne:true});
				$("#weeklyForm").queryData({id:'weeklyProjectStat',jumpOne:true});
				initProjectWeekly();
			},
			detail:function(data){
				var weeklyId = data;
				if(data.WEEKLY_ID){
					weeklyId=data.WEEKLY_ID;
				}
				list.look(weeklyId);
			},
			look:function(weeklyId){
				var width=$(window).width();
				var fullFlag=false;
				var width ='850px';
				if(width<700){
					fullFlag=true;
				}
				if(width>1900){
					width='70%';
				}
				popup.layerShow({id:'weeklyDetail',type:1,full:fullFlag,shade: 0.1,shadeClose:true,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/weekly/weekly-detail.jsp',title:'周报详情',data:{weeklyId:weeklyId}});
			}
		}
		
		var personProjectWeekly = [];
		function initProjectWeekly(){
			$("#weeklyForm").render({data:{pageIndex:1,pageType:3,pageSize:20},success:function(result){
				 layui.use('laypage', function(){
					  var data = result['WeeklyDao.weeklyProjectList'];
					  if(data){
						  personProjectWeekly = data.data;
						  var laypage = layui.laypage;
						  laypage.render({
						    elem: 'pageContainer',
						    limit:20,
						    layout:['page','prev','next','limit','count'],
						    count: data.totalRow,
						    jump:function(obj, first){
						        if(!first){
						        	$("#weeklyForm").render({data:{pageIndex:obj.curr,pageType:3,pageSize:obj.limit}});
						        }
						    }
						  });
					  }
					  
					  var data2 = result['WeeklyDao.myReceiveWeeklyList'];
					  if(data2){
						  var laypage = layui.laypage;
						  laypage.render({
						    elem: 'pageContainer2',
						    limit:20,
						    layout:['page','prev','next','limit','count'],
						    count: data2.totalRow,
						    jump:function(obj, first){
						        if(!first){
						        	$("#weeklyForm").render({data:{pageIndex:obj.curr,pageType:3,pageSize:obj.limit}});
						        }
						    }
						  });
					  }
					  
				});
			}});
		}
		
		function getThisUserData(name){
			$('#userName').val(name);
			list.query();
		}
		
		$(function(){
			$("#dataContainer").render({success:function(){
				list.recentList();
				list.weeklyProjectStat();
				initProjectWeekly();
			}});
			
			layui.element.on('tab(filterData)', function(elem){
				var flag = $(this).data('flag');
				if(flag=='4'){
					$('#exportData').show();
				}else{
					$('#exportData').hide();
				}
			});
		});
		
		var getDate = function(obj,inputName){
			var val = obj.value;
			var bdate = new Date();
			var edate = new Date();
			if('createTime'==inputName||'updateDate'==inputName){
				if(val == 'today'){
					
				}else if(val == 'yesterday'){
					bdate.setDate(bdate.getDate() - 1);
					edate = bdate;
				}else if(val == 'threeDay'){
					bdate.setDate(bdate.getDate() - 3);
				}else if(val == 'oneWeek'){
					bdate.setDate(bdate.getDate() - 7);
				}else if(val == 'oneMonth'){
					bdate.setMonth(bdate.getMonth() - 1);
				}else if(val == 'threeMonth'){
					bdate.setMonth(bdate.getMonth() - 3);
				}else if(val == 'currentMonth'){
					bdate.setDate(1);
					edate.setMonth(bdate.getMonth() + 1);
					edate.setDate(0);
				}
				
				if(val == 'nowWeekly'){
					$('[name="'+inputName+'"]').val($('#nowWeekly').val());
				}else if(val == 'prevWeekly'){
					$('[name="'+inputName+'"]').val($('#prevWeekly').val());
				}else{
					if(val){
						var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
						$('[name="'+inputName+'"]').val(v);
					}else{
						$('[name="'+inputName+'"]').val('');
					}
				}
			}else{
				if(val){
					bdate.setDate(bdate.getDate() - val);
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}
			list.query();
		}
		
		function exportWeeklyData(){
			var array = [];
			for(var index in personProjectWeekly){
				var row = personProjectWeekly[index];
				
				var v1 = row['ITEM_1'];
				var v2 = row['ITEM_2'];
				var v3 = row['ITEM_3'];
				v1 = v1.replaceAll('<br>','\n');
				v1 = v1.replaceAll(' ','');
				v1 = v1.replaceAll('&nbsp;','');
				v1 = v1.replace(/<\/?.+?>/g,"").replace(/ /g,"");
				
				v2 = v1.replaceAll('<br>','\n');
				v2 = v2.replaceAll(' ','');
				v2 = v2.replaceAll('&nbsp;','');
				v2 = v2.replace(/<\/?.+?>/g,"").replace(/ /g,"");
				
				array.push({O:row['YEAR']+"-"+row['WEEK_NO'],A:row['CREATE_NAME'],B:row['SEND_TIME'],C:row['TITLE'],D:row['PROJECT_NAME'],E:v1,F:v2,G:v3,H:row['WORK_DAY']});
			}
			
		    // 自定义下载的header，注意是数组中的数组哦
		    const Header = [['周数','姓名','填写时间','标题','项目','本周工作','下周计划','反馈','工时天数']];
		    // 将JS数据数组转换为工作表。
		    const headerWs = XLSX.utils.aoa_to_sheet(Header);
		    const ws = XLSX.utils.sheet_add_json(headerWs, array, {skipHeader: true, origin: 'A2'});
		    /* 新建空的工作表 */
		    const wb = XLSX.utils.book_new();
		    // 可以自定义下载之后的sheetname
		    XLSX.utils.book_append_sheet(wb, ws, 'sheetName');
		    /* 生成xlsx文件 */
		    XLSX.writeFile(wb, '项目周报.xlsx');
		    
		}
		
		function contentFn(val){
			if(val){
				return getContent(val);
			}else{
				return "暂无";
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>