<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>首页</title>
	<meta name="viewport" content="initial-scale=1.0, user-scalable=no"> 
	<link href="${ctxPath}/static/css/admin.css" rel="stylesheet">
	<link href="//at.alicdn.com/t/font_1002403_34h7imibmwx.css" rel="stylesheet">
	<style>
		  .todayInfo p{line-height: 30px;color: #666;}
		 .flowApply .layui-elem-field legend{font-size: 16px;}
		 .flowApply .btn{text-align: left;}
		 .flowApply .layui-icon{font-size: 12px;}
		 .flowApply .layui-field-title{margin-bottom: 10px;}
		 .layui-text p { margin: 0px 0;}
		 .layui-card .layui-tab-brief .layui-tab-content {padding: 2px 5px;}
    	 p a{text-decoration: none!important;color: #58666e!important;font-size: 13px;}
    	 .layuiadmin-card-status li h3{font-size:16px;}
    	 .tw-list{padding: 0px 0px 10px!important;}
    	 .tw-list [data-w-e]{width: 24px;}
    	 .tw-list span img{max-width: 90%!important;margin: 0 2px;}
    	 .layuiadmin-card-link a{width: auto;padding:0px 10px}
    	 .layuiadmin-card-text .layui-text-top i{font-size: 22px;}
    	 .layui-card{margin-bottom: 10px;}
    	 .table-vzebra tbody > tr > td:nth-child(2n+1), .table-vzebra tbody > tr > th:nth-child(2n+1){min-width: 30px;}
    	 
    	 .layui-tab-item{padding: 0px;}
    	 
    	 .userSign .hideUser{display: none;}
    	 .userSign img {
			height: 52px;width: 52px;border-radius:50%;
		 }
		 
		 #he-plugin-standard{
		 	min-width:300px!important;
		 	overflow: hidden;
		 }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
    <div class="layui-row layui-col-space10">
       <div class="layui-col-md12">
      		<div class="layui-card">
	             <div class="layui-card-body" style="min-height: 90px;padding: 18px 10px;">
	             		<div class="layui-col-md10 layui-col-xs12">
             				<div class="pull-left">
			                     <img style="border-radius:50%;width: 58px;height: 58px;" onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="${staffInfo.picUrl}">
             				</div>
	             			<div class="pull-left ml-15" style="margin-top: 5px;">
	             				 <span class="layui-hide-xs">
	             				 <c:choose>
			                  	 	<c:when test="${openId==''}">
			                  	 	         您没绑定微信  <a href="javascript:void(0)" onclick="bindWx();" style="padding-left: 15px;">立即绑定</a> 
			                  	 	</c:when>
			                  	 	<c:otherwise>
					                     ${staffInfo.nikeName} <a href="javascript:void(0)" onclick="bindWx();" style="padding-left: 5px;font-size: 12px;">更绑微信</a> 
			                  	 	</c:otherwise>
			                  	 </c:choose>
			                  	 </span>
			                  	 <span class="ml-20"> ${staffInfo.userName} / ${staffInfo.deptName} /${staffInfo.post} /${staffInfo.workCity}</span>  <span class="layui-hide-xs ml-20">入职日期：<span id="joinDate">${staffInfo.joinDate}</span> <span class="label label-info label-outline ml-5" id="joinDateDesc"></span></span>
		             			  <p class="layui-hide-xs"> 
				                  	 <span class="layui-hide-xs" id="todaySay"></span>
				                  	 <a class="layui-hide-xs ml-20" onclick="popup.openTab({url:'/yq-admin/pages/log/login-log.jsp',title:'登录日志',id:'loginLog'})" href="javascript:;">登录日志</a>
		                		  </p>
	             			</div>
	             		</div>
	             		<div class="layui-col-md2 layui-col-xs12">
	             			<textarea id="hitokoto" class="hidden">${hitokoto}</textarea>
             				 <a href="${ctxPath}/servlet/workbench?action=toEmail" target="_blank" class="layadmin-backlog-body text-c layui-hide-xs pull-right">
	                        	<h3>未读邮件</h3>
	                        	<p><cite id="emailNum">0</cite></p>
                      		</a>
             				 <a href="${ctxPath}/affair" target="_blank" class="layadmin-backlog-body mr-20 text-c layui-hide-xs pull-right">
	                        	<h3>未读事务</h3>
	                        	<p><cite id="affairNum">0</cite></p>
                      		</a>
	             		</div>
	             </div>
             </div>
      </div>
      <div class="layui-col-md12 layui-hide-xs render-1">
     	<div class="layui-card"  data-mars="HomeDao.waitDo">
              <div class="layui-card-body" style="min-height: 90px;padding: 18px 30px;">
             		<div class="layui-col-md2">
             		  <a class="layadmin-backlog-body" href="javascript:void(0)" onclick="moreTask()">
                       	 <h3>未办任务数</h3>
                         <p><cite id="taskNum">0</cite></p>
                      </a>
             		</div>
             		<div class="layui-col-md2">
             			<a  class="layadmin-backlog-body" href="javascript:void(0)" onclick="weekly()">
                          <h3>周报未阅数</h3>
                           <p><cite id="weeklyNum">0</cite></p>
                      </a>
             		</div>
             		<div class="layui-col-md2">
             			<a class="layadmin-backlog-body" href="javascript:void(0)" onclick="popup.openTab({url:'/yq-work/pages/flow/my/flow-todo.jsp',id:'flow_my_todo',title:'我的待办'})">
                         <h3>流程未办数</h3>
                         <p><cite id="flow_my_todo">0</cite></p>
                       </a>
             		</div>
             		<div class="layui-col-md2">
             			 <a  class="layadmin-backlog-body" href="javascript:void(0)" onclick="popup.openTab({url:'/yq-work/pages/flow/my/flow-list.jsp',id:'flow_my_apply',title:'我的申请'})">
                         <h3>流程申请数</h3>
                         <p><cite id="flow_my_apply">0</cite></p>
                       </a>
             		</div> 
             		<div class="layui-col-md2">
                       <a  class="layadmin-backlog-body" href="javascript:void(0)" onclick="popup.openTab({url:'/yq-work/pages/flow/my/flow-done.jsp',id:'flow_my_done',title:'已办流程'})">
                         <h3>流程已办数</h3>
                         <p><cite id="flow_my_done">0</cite></p>
                       </a>
             		</div>
             		<div class="layui-col-md2">
           				  <a  class="layadmin-backlog-body" href="javascript:void(0)" onclick="popup.openTab({url:'/yq-work/pages/flow/my/flow-cc.jsp',id:'flow_cc',title:'抄送流程'})">
	                        <h3>抄送流程数</h3>
	                        <p><cite id="flow_my_cc">0</cite></p>
                         </a>
             		</div>
             </div>
         </div>
      </div>
      <div class="layui-col-md12 layui-hide-xs">
     	<div class="layui-card">
             <div class="layui-card-body" style="min-height: 90px;padding: 18px 30px;">
             	<ul class="layui-row layui-col-space10">
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void()" onclick="addressList()">
                          <i class="layui-icon iconfont icon-tongxunlu"></i>
                          <cite>通讯录</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="myFa()">
                          <i class="layui-icon iconfont icon-renwu"></i>
                          <cite>固定资产</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)"  onclick="applyFlow('')">
                          <i class="layui-icon iconfont icon-OAliuchengguanli"></i>
                          <cite>发起流程</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="weekly()">
                          <i class="layui-icon iconfont icon-zhoubao"></i>
                          <cite>填写周报</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void()" onclick="moreDoc()">
                          <i class="layui-icon iconfont icon-zhishiku"></i>
                          <cite>文档</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a  href="javascript:void(0)" onclick="newMsg();">
                          <i class="layui-icon layui-icon-chat"></i>
                          <cite>未读消息</cite>
                        </a>
                      </li>
                    </ul>
             		<ul class="layui-row layui-col-space10">
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="kanban()">
                          <i class="layui-icon iconfont icon-renwu"></i>
                          <cite>看板</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void()" onclick="zendao()">
                          <i class="layui-icon iconfont icon-chandao"></i>
                          <cite>禅道</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="https://yzj.quyun.yunqu-info.cn/authserver/login" target="_blank">
                          <i class="layui-icon iconfont icon-qiyeyunzongji"></i>
                          <cite>云总机</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="moreTask()">
                          <i class="layui-icon iconfont icon-renwu"></i>
                          <cite>任务</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="/easitline-finder/sso.jsp" target="_blank">
                          <i class="layui-icon iconfont icon-xiazai"></i>
                          <cite>下载</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void()" onclick="moreDc()">
                          <i class="layui-icon iconfont icon-dingcan"></i>
                          <cite>订餐</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="moreTodo()">
                          <i class="layui-icon iconfont icon-richeng"></i>
                          <cite>日程</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="moreNote()">
                          <i class="layui-icon iconfont icon-shenhebijijishibenxiezi"></i>
                          <cite>笔记</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="moreTw()">
                          <i class="layui-icon iconfont icon-DT"></i>
                          <cite>我的动弹</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="javascript:void(0)" onclick="moreNav()">
                          <i class="layui-icon iconfont icon-wangzhidaohang"></i>
                          <cite>我的网址</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="https://getquicker.net/" target="_blank">
                          <i class="layui-icon layui-icon-windows"></i>
                          <cite>quicker</cite>
                        </a>
                      </li>
                      <li class="layui-col-md2 layui-col-sm3">
                        <a href="https://www.getxf.cn/" target="_blank">
                          <i class="layui-icon layui-icon-edit"></i>
                          <cite>网站导航</cite> <span class="layui-badge-dot"></span>
                        </a>
                      </li>
             	</ul>
             </div>
         </div>
      </div>
      <div class="layui-col-md8">
        <div class="layui-row layui-col-space10">
          <div class="layui-col-md12 render-2">
          	 <div class="layui-card">
              <div class="layui-tab layui-tab-brief layadmin-latestData flowList" lay-filter="flowFilter">
                <ul class="layui-tab-title">
                  <li data-val="todo" class="layui-this">待办流程</li>
                  <li data-val="apply">我的申请</li>
                  <li data-val="done">已办流程</li>
                  <li data-val="cc">抄送流程</li>
                </ul>
                <div class="layui-tab-content ">
                  <div data-ref-flow="todo" class="layui-tab-item layui-show">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="flow-toto-template" data-mars="FlowDao.myFlowTodo" style="width: 100%;"></ul>
         		  </div>
                  <script id="flow-toto-template" type="text/x-jsrender">
						 {{if data.length==0}}<li><p style="text-align:center;">暂无流程</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> &nbsp;<a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowtodo')">{{:APPLY_NO}}_{{:FLOW_NAME}}_{{:APPLY_TITLE}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{:APPLY_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:APPLY_TIME}}</span>
			                <span class="ml-10"><i class="fa fa-calendar-check-o"></i> {{:NODE_NAME}}</span>
			                <a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowtodo')" class="layui-btn layui-btn-xs layuiadmin-reply">办理</a>
			              </li>
			              {{/for}}
 						  {{if data.length>=8}}<li style="text-align:center;"><a href="javascript:void(0);" onclick="flowMore('myFlowtodo')">查看更多</a></li>{{/if}}
				 </script>
                  <div data-ref-flow="apply" class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="flow-my-template" data-mars="FlowDao.myFlowApply" style="width: 100%;"></ul>
         		  </div>
                  <script id="flow-my-template" type="text/x-jsrender">
						 {{if data.length==0}}<li><p style="text-align:center;">暂无流程</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> &nbsp;<a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowApply')">{{:APPLY_NO}}_{{:FLOW_NAME}}_{{:APPLY_TITLE}}</a></p>
			                <span class="ml-15">{{call:APPLY_STATE fn='flowApplyState'}}</span>
			                <span class="ml-10"><i class="fa fa-user-o"></i>  {{:APPLY_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:APPLY_TIME}}</span>
			                {{if APPLY_STATE!='0'}} <span class="ml-10"><i class="fa fa-hourglass-start"></i> {{:NODE_NAME}}/{{:CHECK_NAME}} </span>{{/if}}
			                <a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowApply')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
						  {{if data.length>=8}}<li style="text-align:center;"><a href="javascript:void(0);" onclick="flowMore('myFlowApply')">查看更多</a></li>{{/if}}
				 </script>
                  <div data-ref-flow="done"  class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="flow-done-template" data-mars="FlowDao.myFlowDone" style="width: 100%;"> </ul> 
                  </div>
                  <script id="flow-done-template" type="text/x-jsrender">
			             {{if data.length==0}}<li><p style="text-align:center;">暂无流程</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> &nbsp;<a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowDone')">{{:APPLY_NO}}_{{:FLOW_NAME}}_{{:APPLY_TITLE}}</a></p>
							<span class="ml-15">{{call:APPLY_STATE fn='flowApplyState'}}</span>			                
							<span class="ml-15"><i class="fa fa-user-o"></i> {{:APPLY_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:APPLY_TIME}}</span>
			                <span class="ml-10"><i class="fa fa-check-square-o"></i> {{:NODE_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-hourglass-3"></i> {{:CURRENT_NODE}}</span>
			                <a href="javascript:;"  onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowDone')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
 						{{if data.length>=8}}<li style="text-align:center;"><a href="javascript:void(0);" onclick="flowMore('myFlowDone')">查看更多</a></li>{{/if}}
				 </script>
                  <div data-ref-flow="cc"  class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="flow-cc-template" data-mars="FlowDao.myCCFlowList" style="width: 100%;"> </ul> 
                  </div>
                  <script id="flow-cc-template" type="text/x-jsrender">
			             {{if data.length==0}}<li><p style="text-align:center;">暂无流程</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> &nbsp;<a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowCC')">{{:APPLY_NO}}_{{:FLOW_NAME}}_{{:APPLY_TITLE}}</a></p>
							<span class="ml-15">{{call:APPLY_STATE fn='flowApplyState'}}</span>			                
							<span class="ml-15"><i class="fa fa-user-o"></i> {{:APPLY_NAME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:APPLY_TIME}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{:CREATE_NAME}} 抄送于{{:CC_TIME}}</span>
			                <a href="javascript:;" onclick="flowDetail('{{:APPLY_ID}}','{{:RESULT_ID}}','myFlowCC')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
 						{{if data.length>=8}}<li style="text-align:center;"><a href="javascript:void(0);" onclick="flowMore('myFlowCC')">查看更多</a></li>{{/if}}
				 </script>
                </div>
              </div>
            </div>
            <div class="layui-card">
              <div class="layui-tab layui-tab-brief layadmin-latestData taskList">
                <a style="position: absolute;;right: 20px;margin-top: 10px;z-index: 9999;color: rgb(1, 170, 237);" href="javascript:void(0)" onclick="moreTask()" class="layui-a-tips">更多</a>
                <ul class="layui-tab-title">
                  <li class="layui-this">我负责的任务</li>
                  <li>我发起的任务</li>
                  <li class="" id="zendaoNum">我的禅道</li>
                </ul>
                <div class="layui-tab-content ">
                  <div class="layui-tab-item layui-show">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="task-template" data-mars="HomeDao.myTaskList" style="width: 100%;"></ul>
         		  </div>
                  <script id="task-template" type="text/x-jsrender">
						 {{if data.length==0}}<li><p style="text-align:center;">暂无任务</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> {{call:TASK_STATE fn='taskStateLabel'}}&nbsp;<a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')">{{:TASK_NAME}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{call:CREATOR fn='getUserName'}}</span>
			                <span class="ml-10"><i class="fa fa-clock-o"></i> {{call:TASK_LEVEL fn='taskTextLevel'}}</span>
			                <span class="ml-10"><i class="fa fa-calendar-check-o"></i> {{:PLAN_STARTED_AT}} ~{{:DEADLINE_AT}}</span>
			                {{if TASK_STATE < 30}}<span class="ml-5">{{call:DEADLINE_AT fn='timeFn'}}</span></span>{{/if}}
							{{if FINISH_TIME}}<span title="完成时间" class="ml-10"><i class="fa fa-check-square-o"></i> {{:FINISH_TIME}}</span>{{/if}}
			                <a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">处理</a>
			              </li>
			              {{/for}}
				 </script>
                  <div class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="create-task-template" data-mars="HomeDao.myCreateTaskList" style="width: 100%;"></ul>
         		  </div>
                  <script id="create-task-template" type="text/x-jsrender">
						 {{if data.length==0}}<li><p style="text-align:center;">暂无任务</p></li>{{/if}}
						 {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> {{call:TASK_STATE fn='taskStateLabel'}}&nbsp;<a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')">{{:TASK_NAME}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{call:ASSIGN_USER_ID fn='getUserName'}}</span>
							<span class="ml-10"><i class="fa fa-clock-o"></i> {{call:TASK_LEVEL fn='taskTextLevel'}}</span>
			                <span title="发起时间" class="ml-10"><i class="fa fa-calendar-check-o"></i> {{:CREATE_TIME}}</span>
			                {{if TASK_STATE < 30}}<span class="ml-5">{{call:DEADLINE_AT fn='timeFn'}}</span></span>{{/if}}
							{{if FINISH_TIME}}<span title="完成时间" class="ml-10"><i class="fa fa-check-square-o"></i> {{:FINISH_TIME}}</span>{{/if}}
			                <a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}','{{:TASK_STATE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
				 </script>
                  <div class="layui-tab-item">
                     <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="zendao-template" data-mars="HomeDao.getBugList" style="width: 100%;"> </ul> 
                  </div>
                  <script id="zendao-template" type="text/x-jsrender">
						{{call:data fn='zendaoNum'}}
						{{if data.length==0}}<li><p style="text-align:center;">暂无数据</p></li>{{/if}}
			            {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i>&nbsp;<a href="javascript:;" onclick="zentao('{{:ID}}')">{{:TITLE}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{:OPENEDBY}}</span>
			                <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:ASSIGNEDDATE}}</span>
			                <a href="javascript:;" onclick="zentao('{{:ID}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			            {{/for}}
				 </script>
                </div>
              </div>
            </div>
           <div class="layui-card">
	          <div class="layui-tab layui-tab-brief layadmin-latestData">
                <ul class="layui-tab-title">
                  <li class="layui-this"> 通知公告  </li>
                </ul>
                <div class="layui-tab-content">
                	 <div class="layui-tab-item layui-show">
	                	 <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="notice-template" data-mars="HomeDao.notices" style="width: 100%">
		            	
		            	 </ul>
                		<script id="notice-template" type="text/x-jsrender">
	            		 {{for data}}
	              		   <li>
	               			    <p><span class="layui-badge-rim" style="color: #b4a2a2;height: 20px;line-height: 20px;">{{call:REMIND_TYPE fn='noticeLabel'}}</span> <a href="javascript:void(0)"  onclick="remindDetail('{{:REMIND_ID}}','{{:REMIND_TYPE}}')">{{:TITLE}}</a></p>
	                			<span class="ml-5"><i class="fa fa-user-o"></i>  {{:PUBLISH_BY}}</span>
	               				 <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:PUBLISH_TIME}}</span>
	                			<span class="ml-15"><i class="fa fa-eye"></i> {{:VIEW_COUNT}}</span>
	                			<a href="javascript:void(0)" onclick="remindDetail('{{:REMIND_ID}}','{{:REMIND_TYPE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
	              			</li>
	            		 {{/for}}
					   </script>
                   </div>
                </div>
              </div>
	        </div>
           <div class="layui-card">
	          <div class="layui-tab layui-tab-brief layadmin-latestData">
	             <a style="position: absolute;;right: 20px;margin-top: 10px;z-index: 9999;color: rgb(1, 170, 237);" href="javascript:void(0)" onclick="popup.openTab({url:'${ctxPath}/pages/doc/doc-list.jsp',title:'知识库'})" class="layui-a-tips">更多</a>
                <ul class="layui-tab-title">
                  <li class="layui-this"> 知识库  </li>
                </ul>
                <div class="layui-tab-content">
                  <div class="layui-tab-item layui-show">
                      <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="km-template" data-mars="HomeDao.doc" style="width: 100%;">
		            </ul>
                  </div>
                   <script id="km-template" type="text/x-jsrender">
			            {{for data}}
			              <li>
			                <p><i class="layui-icon layui-icon-right"></i> <a href="javascript:;" onclick="docDetail('{{:DOC_ID}}','{{:FOLDER_ID}}')">{{:TITLE}}</a></p>
			                <span class="ml-15"><i class="fa fa-user-o"></i>  {{:CREATE_NAME}}</span>
			                <span class="ml-15"><i class="fa fa-calendar-check-o"></i> {{:UPDATE_TIME}}</span>
			                <span class="ml-15"><i class="fa fa-eye"></i> {{:VIEW_COUNT}}</span>
			                <a href="javascript:;" onclick="docDetail('{{:DOC_ID}}','{{:FOLDER_ID}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a>
			              </li>
			              {{/for}}
					</script>
                </div>
              </div>
	        </div>
          </div>
        </div>
      </div>
      
      <div class="layui-col-md4 render-3">
      	 <div class="layui-card">
	          <div class="layui-card-header">快速发起流程
	          	<a style="position: absolute;;right: 20px;margin-top: 0px;z-index: 9999;color: rgb(1, 170, 237);" href="javascript:void(0)" onclick="applyFlow('');" class="layui-a-tips">更多</a>
	          </div>
	          <div class="layui-card-body" style="min-height: 60px;" data-mars="FlowConfigDao.myFlowFavorite" data-template="top-list">
           			
	          </div>
	          <script id="top-list" type="text/x-jsrender">
				<div class="layui-row">
				{{for data}}
					<div class="layui-col-xs12 layui-col-md6 layui-col-sm6 layui-col-lg4" style="padding:4px 0px;" data-code="{{:FLOW_CODE}}" data-name="{{:FLOW_NAME}}">
					<button class="btn btn-sm btn-default" onclick="applyFlow('{{:FLOW_CODE}}');" style="width: 85%;" type="button"> +{{:FLOW_NAME}}</button>	
				</div>
			   {{/for}}
				{{if data.length==0}} <button class="btn btn-sm btn-default" onclick="applyFlow('');" style="width: 120px;" type="button"> +发起流程 </button> {{/if}}
				</div>			
			 </script>
         </div>
         <div class="layui-card dc">
	          <div class="layui-card-header">一日三餐 <a href="javascript:void(0);" onclick="foodSetting();"  class="layui-btn layui-btn-primary layui-btn-xs mt-10 layui-a-tips">午餐预定</a></div>
	          <div class="layui-card-body layui-text">
           			<table class="layui-table" lay-skin="nob" style="padding: 10px 6px;width: 100%">
						<tbody id="booksInfo"></tbody>
					</table>
					<div class="text-c mt-10">
						<button type="button" id="doDc" class="btn btn-info btn-sm mr-10 text-r btn-outline" onclick="bookFood()"> <i class="glyphicon glyphicon-cutlery"></i> 点餐</button>
						<a href="tencent://message/?uin=1036305495&Menu=yes" id="addDoDc" class="btn btn-success btn-sm btn-outline mr-10 text-r" onclick="bookFood()">申请补点</a>
					</div>
	          </div>
         </div>
         <div class="layui-card">
         	  <div class="layui-card-header">今日日历
         	  	<a style="position: absolute;;right: 20px;margin-top: 0px;z-index: 9999;color: rgb(1, 170, 237);" href="javascript:void(0)" onclick="top.popup.layerShow({type:2,area:['80%','90%'],url:'https://www.rili.com.cn/',title:false})" class="layui-a-tips">更多</a>
         	  </div>
	          <div class="layui-card-body todayInfo" style="min-height: 170px;">
	          	<div class="layui-col-md6">
		          	 <p>日期：<span id="date"></span></p>
		          	 <p>周次：今年的第<span id="weekOfYear"></span>周</p>
		          	 <p>天次：今年的第<span id="dayOfYear"></span>天</p>
		          	 <p>农历：<span id="yearTips"></span><span id="lunarCalendar"></span></p>
	          	</div>
	          	<div class="layui-col-md6">
		          	 <p>类型：<span id="typeDes"></span></p>
		          	 <p>节气：<span id="solarTerms"></span></p>
		          	 <p>星座：<span id="constellation"></span></p>
		          	 <p>工作：本月第<span id="indexWorkDayOfMonth"></span>工作日</p>
	          	</div>
	          	<div class="layui-col-md12">
		          	 <p>宜项：<span id="avoid"></span></p>
		          	 <p class="hidden">禁忌：<span id="suit"></span></p>
	            </div>
             </div>
         </div>
         <div class="layui-card mt-10">
	          <div class="layui-card-body" style="min-height: 60px;">
           		 <div class="layui-hide-xs" id="he-plugin-standard"></div>
	          </div>
         </div>
         <div class="layui-card">
          <div class="layui-card-header">我的考勤    <a class="layui-a-tips" href="javascript:void(0);" onclick="kqMore()">更多</a> </div>
          <div class="layui-card-body">
            <div data-mars="HomeDao.queryKq" data-template="kq-template">
             <script id="kq-template" type="text/x-jsrender">
               {{if data.length==0}}<p>暂无数据</p>{{/if}}
               <table class="layui-table" lay-skin="line">
              	 {{if data.length>0}}
					<thead>
   					   <tr>
     				 	 <th>日期</th>
     				 	 <th>姓名</th>
     					 <th>上班时间</th>
      					 <th>下班时间</th>
   						</tr> 
  					</thead>
              	  {{for data}}
                    <tr>
                 	 <td>
                  		{{:DK_DATE}}
                  	</td>
                 	 <td>
                  		{{:USER_NAME}}
                  	</td>
                 	 <td>
                  		{{:SIGN_IN}}&nbsp;{{:REMARK}}
                  	</td>
                 	 <td>
                  		{{:SIGN_OUT}}&nbsp;{{:REMARK}}
                  	</td>
                	</tr>
                   {{/for}}
				 {{/if}}
			</table>
		   </script>
          </div>
          <p style="text-align: center;margin-top: 15px;margin-bottom: 15px;"><button class="btn btn-success btn-outline btn-sm" onclick="kqSign(0)" type="button"><i class="fa fa-sign-in" aria-hidden="true"></i> 上班打卡</button> <button class="btn btn-info btn-outline btn-sm ml-10" onclick="kqSign(1)" type="button"><i class="fa fa-sign-out" aria-hidden="true"></i> 下班打卡</button></p>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">网址收藏
          	  <button class="layui-btn layui-btn-primary layui-btn-xs" style="position: absolute;right: 10px;top: 10px;" onclick="addUserNav()">
             	  + 添加
              </button>
          </div>
          <div class="layui-card-body nav-card">
            <div class="layuiadmin-card-link layui-text" data-template="userNav-template" data-mars="HomeDao.queryUserNav"></div>
             <script id="userNav-template" type="text/x-jsrender">  
              	  {{if data.length==0}}<a href="javascript:void(0)" onclick="addUserNav()">+立即添加</a>{{/if}}
              	  {{for data}}
					 {{if REMARK}}
	             	 	<a onclick="userNavDetail('{{:NAV_ID}}')" href="javascript:void(0);">{{cutText:TITLE 30}}</a>
						{{else}}
	             		 <a target="_blank" href="{{:URL}}" rel="noreferrer">{{cutText:TITLE 30}}</a>
					 {{/if}}
	              {{/for}}
	              <a target="_blank" rel="noreferrer" href="https://getnote.cf/${userId}" rel="noreferrer">共享本</a>

			</script>      
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">我参与的项目<a onclick="myProject()" href="javascript:void(0)" class="layui-a-tips">更多</a></div>
          <div class="layui-card-body">
         		 <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="project-template" data-mars="HomeDao.myProject">
	             </ul>
                <script id="project-template" type="text/x-jsrender">
						    {{if data.length==0}}<li><p style="text-align:center;">暂无数据</p></li>{{/if}}
		              		{{for data}}
		               			<li>
	                				<p style="padding-bottom:0px;cursor: pointer;"><i class="layui-icon layui-icon-right"></i> <a onclick="projectDetail('{{:PROJECT_NAME}}','{{:PROJECT_ID}}')">{{call:PROJECT_NAME 40 fn='cutText'}}</a></p>
	             				 </li>
		              		{{/for}}
        		</script>
          </div>
        </div>
        <div class="layui-card">
          <div class="layui-card-header">项目在线文档<a onclick="myProject()" href="javascript:void(0)" class="layui-a-tips">更多</a></div>
          <div class="layui-card-body">
         		 <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text" data-template="project-link-template" data-mars="HomeDao.myProjectLink">
	             </ul>
                <script id="project-link-template" type="text/x-jsrender">
						    {{if data.length==0}}<li><p style="text-align:center;">暂无数据</p></li>{{/if}}
		              		{{for data}}
		               			<li>
	                				<p style="padding-bottom:0px;cursor: pointer;"><i class="layui-icon layui-icon-link"></i> <a href="{{:LINK_URL}}" target="_blank" rel="noreferrer" title="{{:PROJECT_NAME}}/{{:REMARK}}">{{call:LINK_NAME 40 fn='cutText'}}</a></p>
	             				 </li>
		              		{{/for}}
        		</script>
          </div>
        </div>
        
    
        <!-- <div class="layui-card">
          <div class="layui-card-header">产品动态</div>
          <div class="layui-card-body">
            <div class="layui-carousel layadmin-carousel layadmin-news" data-autoplay="true" data-anim="fade" lay-filter="news" lay-anim="fade" lay-indicator="inside" lay-arrow="none" style="width: 100%; height: 280px;">
              <div carousel-item="">
                <div class=""><a href="http://fly.layui.com/docs/2/" target="_blank" class="layui-bg-red">layuiAdmin 快速上手文档</a></div>
                <div class="layui-this"><a href="http://fly.layui.com/vipclub/list/layuiadmin/" target="_blank" class="layui-bg-green">layuiAdmin 会员讨论专区</a></div> 
                <div class=""><a href="http://www.layui.com/admin/#get" target="_blank" class="layui-bg-blue">获得 layui 官方后台模板系统</a></div>
              </div>
            <div class="layui-carousel-ind"><ul><li class=""></li><li class="layui-this"></li><li class=""></li></ul></div><button class="layui-icon layui-carousel-arrow" lay-type="sub"></button><button class="layui-icon layui-carousel-arrow" lay-type="add"></button></div>
          </div>
        </div> -->
  		<!-- <div class="layui-card">
          <div class="layui-card-header">在线用户</div>
          <div class="layui-card-body">
            
                  
          </div>
        </div> -->
        
         <div class="layui-card userSign">
          <div class="layui-card-header">今日访问 <span id="visitNum"></span> <span class="ml-20">当前在线数(${onlineCount})</span>
          	  <button class="layui-btn layui-btn-primary layui-btn-xs" style="position: absolute;right: 10px;top: 10px;" onclick="signShow(this)"> 展开</button>
          </div>
          <div class="layui-card-body nav-card">
             <ul data-template="userSign-template" style="text-align: center;" data-mars="HomeDao.signList"></ul>
             <script id="userSign-template" type="text/x-jsrender">  
				  {{call:data fn='visitNum'}}
              	  {{for data}}
					 <li title="第{{:#index+1}}名~{{:VISIT_TIME}}" {{if #index>=12}}class="hideUser moreSign"{{/if}} style="display:{{if #index<12}}inline-block{{else}}none{{/if}};width:15%;padding:5px;"> 
						 <div><img onclick="userInfoLayer('{{:USER_ID}}')" onerror="this.src='/yq-work/static/images/user-avatar-large.png'" src="{{:PIC_URL}}"></div>
					     <div style="font-size:12px;text-overflow: ellipsis;white-space: nowrap;text-align: center;color: rgba(0,0,0,.9);">{{:USERNAME}}</div>
					  </li>
	              {{/for}}
			</script>      
          </div>
        </div>
      </div>
    </div>
	<div style="display: none;" id="qcode">
		<div style="margin-left:40px;" id="codeContainer"></div><br><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;微信扫码关注,再次扫码绑定后关闭弹出窗<br><b class="ml-20">请尽快关注绑定，否则影响系统使用</b></p>
	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		var ctxPath='${ctxPath}';
		var pwd='${pwd}';
		var roles='${roles}';
		var deptId='${deptId}';
		var devLead='${devLead}';
		var openId='${openId}';
		var userId = '${userId}';
		var isBirthday='${isBirthday}';
		var loginAcct = ${staffInfo.loginAcct};
		if(isBirthday=='1'){
			var device = layui.device();
			var fullShow=device.mobile;
			top.layer.open({title:false,full:fullShow,type:1,offset:'60px',area:['500px','500px'],shade:0,content:'<p style="position:absolute;top:70px;left:28px;font-size:18px;color:#3fb03d;"><span style="color:#ef0909;">${staffInfo.userName }</span><br>感谢你对公司做出的贡献,在此祝你生日快乐!事事顺心!</p><img style="width:500px;height:470px;background-size: cover;" src="/yq-work/static/images/birthday.png">'});
		}
	</script>
   <script type="text/javascript" src="${ctxPath}/static/js/jquery.qrcode.min.js"></script>
   <script type="text/javascript" src="${ctxPath}/static/js/index.js?v=20221219"></script>
   <script type="text/javascript" src="${ctxPath}/static/js/dc.js?v=202106281"></script>
   <script type="text/javascript" src="${ctxPath}/static/js/notify.min.js"></script>
   <script src="${ctxPath}/static/js/ws.js"></script>
   <script src="https://widget.qweather.net/standard/static/js/he-standard-common.js?v=2.0"></script>

<script type="text/javascript">
	layui.config({
		  base: '${ctxPath}/static/module/'
	}).use('notice'); //加载自定义模块
	
	var protocol = location.protocol;
    var urlPrefix = 'ws://';
    if(protocol=='https:'){
    	urlPrefix = 'wss://';
    }
	ws.init(urlPrefix+location.host+"${ctxPath}/websocket/${staffInfo.userId}/${staffInfo.loginAcct}",{
		autoReconnect:true,
		onmessage:function(data){
			console.log(new Date()+">>>"+JSON.stringify(data));
		    layui.use(['notice'], function(){
				var notice = layui.notice;
				notice.info({
					theme:'dark',
					timeout:1000*60,
			        title: data.title||'消息通知',
			       // maxWidth:'650px',
			        message: data.msg||'你有新的消息，请注意查收!',
			        audio: (data['senderType']||"3")+""
			    });
				top.layer.alert(data.msg,{title:data.title,time:20000,icon:1,offset:'rt',shade:0});
			});
		    if(data.token){
			    try{
					var audio4= new Audio("http://tsn.baidu.com/text2audio?lan=zh&ctp=1&cuid=yunqu&tok="+data.token+"&vol=9&per=0&spd=5&pit=5&aue=3&tex="+data.msg);
					audio4.play();
				}catch(e){
					console.log(e)						
				}
		    }
		},//收到消息
		onopen:function(event){
			console.log(new Date()+' connection success');
		},//ws链接成功
		onclose:function(event){
			console.log(new Date()+' connection close');
		},//ws关闭
		onerror:function(event){
			console.log(new Date()+' connection error');
		},//ws错误
	})

	$(function(){
		WIDGET = {
		 "CONFIG": {
			"layout": "2",
		    "height": 270,
		    "background": "5",
		    "dataColor": "000000",
		    "key": "fc9925dbca104242b7726f0d9c3485e6"
		  }
		}
		setTimeout(function(){
			$('#he-plugin-standard').css('width','auto');
		},2000);
	});
</script>

</EasyTag:override>

<%@ include file="/pages/common/layout_list.jsp" %>
