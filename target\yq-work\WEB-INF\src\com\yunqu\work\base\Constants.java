package com.yunqu.work.base;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_NAME = "yq-work-ds";     //默认数据源名称
	
	public final static String MARS_DS_NAME = "yq-main-ds";     //默认数据源名称
	
	public final static String ZENTAO_DS_NAME = "yq-zentao-ds";     //禅道数据源名称
	
	public final static String MONITOR_DS_NAME = "yc-monitor-ds";     //默认数据源名称
	
	public final static String APP_NAME = "yq-work";     //应用
	
	public final static String DS_MAIN_NAME = AppContext.getContext(APP_NAME).getProperty("MAIN_DS_NAME","ycmain");     //mars库名称

	public final static String emailConfig = AppContext.getContext(APP_NAME).getProperty("emailConfig","");
	
	public final static String VERSION_MANGER="VERSION_MANGER";
	
	/**
	 * 系统环境类型
	 * prod,pre,test,dev
	 * @return
	 */
	public static String profileActive() {
		return ServerContext.getProperties("PROFILE_ACTIVE", "prod");
	}
	
	public static boolean isProd() {
		return "prod".equals(profileActive());
	}
	
	/**
	 * 
	 */
	public final static int TASK_WAIT=10;
	
	/**
	 * 
	 */
	public final static int TASK_DOING=20;
	
	/**
	 * 
	 */
	public final static int TASK_FINISH=30;
	
	/**
	 * 
	 */
	public final static int TASK_OK=40;
	
}
