package com.yunqu.work.model;

import com.yunqu.work.base.AppBaseModel;

public class FileModel extends AppBaseModel{

	private static final long serialVersionUID = 1L;
	
	public FileModel(){
		setTableInfo("YQ_FILES","FILE_ID");
	}
	public void setFileId(String fileId){
		this.set("FILE_ID",fileId);
	}
	public void setFileName(String fileName){
		this.set("FILE_NAME",fileName);
	}
	public void setFileSize(String fileSize){
		this.set("FILE_SIZE", fileSize);
	}
	public void setFileLenth(long fileLenth){
		this.set("FILE_LENTH", fileLenth);
	}
	public void setFileType(String fileType){
		this.set("FILE_TYPE",fileType);
	}
	public void setDiskPath(String diskPath){
		this.set("DISK_PATH",diskPath);
	}
	public void setAccessUrl(String accessUrl){
		this.set("ACCESS_URL",accessUrl);
	}
	public void setFolderId(String folderId){
		this.set("FOLDER_ID",folderId);
	}
	public void setFkId(String fkId){
		this.set("FK_ID",fkId);
	}
	public void setSource(String source){
		this.set("SOURCE",source);
	}
	
}
