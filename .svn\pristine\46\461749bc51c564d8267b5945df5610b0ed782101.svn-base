package com.yunqu.work.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;


public class ApproveResultRowMapper implements EasyRowMapper<ApproveResultModel> {

	@SuppressWarnings("unchecked")
	@Override
	public ApproveResultModel mapRow(ResultSet rs, int rowNum) {
		ApproveResultModel vo = new ApproveResultModel();
		try {
			vo.setResultId(rs.getString("RESULT_ID"));
			vo.setPrevResultId(rs.getString("PREV_RESULT_ID"));
			vo.setBusinessId(rs.getString("BUSINESS_ID"));
			vo.setNodeId(rs.getString("NODE_ID"));
			vo.setNodeName(rs.getString("NODE_NAME"));
			vo.setCheckUserId(rs.getString("CHECK_USER_ID"));
			vo.setCheckName(rs.getString("CHECK_NAME"));
			vo.setCheckResult(rs.getInt("CHECK_RESULT"));
			vo.setGetTime(rs.getString("GET_TIME"));
			vo.setReadTime(rs.getString("READ_TIME"));
			vo.setCheckTime(rs.getString("CHECK_TIME"));
			vo.setData1(rs.getString("DATA1"));
			vo.setData2(rs.getString("DATA2"));
			vo.setData3(rs.getString("DATA3"));
			vo.setData4(rs.getString("DATA4"));
			vo.setData5(rs.getString("DATA5"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
