<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
	<style>
		#selectUserForm{padding:5px 10px;}
		.nowUser .label{
			display: inline-block;
		    margin-bottom: 5px;
		    width: 78px;
		    margin-right: 5px;
		    height: 26px;
		    line-height: 20px;
		}
		.layui-tree-entry{height: 30px;}
		.nowUser .layui-icon-close{cursor: pointer;font-size: 12px;float: right;}
		.nowUser .layui-icon-close:hover{color: red;}
		#ztree{height: 305px;overflow-y: scroll;overflow-x: hidden;margin: 5px -5px;}
		#selectUserForm .groupList{height: 325px;overflow-y: scroll;overflow-x: hidden;}
		#selectUserForm .layui-tab-item{padding: 0px;}
		#selectUserForm .layui-tab-content{padding: 0px;}
		#selectUserForm .groupList p{height: 36px;line-height: 36px;padding-left: 5px;margin-left: 10px;}
		#selectUserForm .groupList p:hover{background-color: #eee;cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectUserForm">
       			<input name="groupId" id="groupId" type="hidden"/>
       			<input name="deptId" id="deptId" type="hidden"/>
				<input name="deptCode" id="deptCode" type="hidden"/>
            	<div class="ibox" style="margin-top: -15px;">
	            	<div class="layui-row layui-col-space5">
					  <div class="layui-col-md4">
					  	<div style="margin-top: 10px;">
					  		 <div class="input-group input-group-sm ml-5" style="width: 98%;">
								 <input type="text" name="userName" placeholder="支持拼音简写" class="form-control" style="background-color: #f2f2f2;">
						     </div>
			    			 <button type="button" class="btn btn-sm btn-default hidden ml-5"  data-event="enter"  onclick="SelectUser.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					  	</div>
					     <c:if test="${!empty param.busiType}">
						  	<div style="margin-top: 5px;">
					       	    <input name="busiType" type="hidden" id="busiType"/>
							    <label class="checkbox checkbox-inline checkbox-success ml-5"  onclick="recentContacts(this)">
				                    	<input type="checkbox" value="0" name="recentContacts"> <span>最近联系人</span>
				                </label>
						  	</div>
					     </c:if>
					    <div class="layui-tab layui-tab-brief" lay-filter="typeFilter" style="margin: 0px;margin-bottom: -8px;">
          		 	 		<ul class="layui-tab-title"><li data-val="dept" class="layui-this">部门</li><li data-val="group">分组</li></ul>
          		 	 		<div class="layui-tab-content">
          		 	 			 <div class="layui-tab-item layui-show"><div id="ztree"></div></div>
								 <div class="layui-tab-item groupList" data-mars="FlowConfigDao.groupList" data-template="select-usergroup-template"></div>
          		 	 		</div>
          		 	 	</div>
	            	  </div>
					  <div class="layui-col-md8">
	              			<table id="userList"></table>
					  </div>
					</div>
					<div class="layui-row list-user">
					    <div class="layui-col-md12"><hr class="mt-5 mb-5"></div>
					    <div class="layui-col-md12" style="height: 32px;line-height: 32px;">
					    	<div class="pull-left">已选择 <span id="selectCount">0</span> 项</div> <div class="pull-right"><button class="btn btn-xs btn-default" type="button" onclick="SelectUser.createGroup()">+创建分组</button></div>
					    </div>
					    <div class="nowUser layui-col-md12"></div>
					</div>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary" type="button" onclick="SelectUser.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
        <script id="select-usergroup-template" type="text/x-jsrender">
			      {{for data}}
			          <p onclick="SelectUser.selectGroup('{{:GROUP_ID}}')"><i class="fa fa-address-card" aria-hidden="true"></i> {{:GROUP_NAME}}</p>
			      {{/for}}
				  {{if data.length==0}} <p>无数据</p> {{/if}}
		</script>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	
			
			var SelectUser={
					sid:'${param.sid}',
					type:'radio',
					query : function(){
						$("#selectUserForm").queryData();
					},
					initData:function(){
						var el = $("[data-sid='"+SelectUser.sid+"']");
						var names = el.val();
						var ids = '';
						if(el.prev().length==0){
							ids = names;
						}else{
							ids = el.prev().val();
						}
						if(names){
							var array1 = names.split(',');
							var array2 = ids.split(',');
							for(var index in array1){
								SelectUser.addRow(array2[index],array1[index]);
							}
						}
						
					},
					initTable : function(){
						var type = '${param.type}';
						SelectUser.type = type;
						if(type=='checkbox'){
							SelectUser.initData();
						}else{
							$('#selectUserForm .layui-col-md10').css('width','100%');
							$('.nowUser,.list-user').remove();
							$('#selectUser').parents('.layui-layer').css('height','530px');
						}
						$("#selectUserForm").initTable({
							mars:'EhrDao.userList',
							id:'userList',
							page:true,
							limit:20,
							height:'400px',
							limits:[20,50,100,200,300,500],
							rowEvent:'rowEvent',
							rowDoubleEvent:'SelectUser.ok',
							cols: [[
					        {type:type},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'USERNAME',
								title: '姓名',
								width:90
							},{
							    field: 'DEPTS',
								title: '部门',
								width:120
							},{
							    field: 'JOB_TITLE',
								title: '职位',
								width:130
							}
							]],done:function(){
								table.on('checkbox(userList)', function(obj){
									if(obj.type=='all'){
										if(obj.checked){
											var checkStatus = table.checkStatus('userList');
											if(checkStatus.data.length>0){
												var data = checkStatus.data;
												for(var index in data){
													SelectUser.addRow(data[index]['USER_ID'],data[index]['USERNAME']);
												}
											}
										}else{
											$('.nowUser').empty();
										}
									}else{
									   if(obj.checked){
										   SelectUser.addRow(obj.data.USER_ID,obj.data.USERNAME);
									   }else{
										   $(".nowUser [data-id='"+obj.data.USER_ID+"']").remove();
									   }
										
									}
								});  
							}
					       }
						);
					},
					addRow:function(userId,username){
						$(".nowUser [data-id='"+userId+"']").remove();
						$('.nowUser').append('<div data-id="'+userId+'" data-name="'+username+'" class="label label-info label-outline">'+username+'<i class="layui-icon layui-icon-close" onclick="$(this).parent().remove();"></i></div>');
						$('#selectCount').text($('.nowUser div').length);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectUser.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('userList');
							
							if(SelectUser.type=='checkbox'){
								checkStatus.data = []; 
								$(".nowUser [data-id]").each(function(){
									var t = $(this);
									checkStatus.data.push({USERNAME:t.data('name'),USER_ID:t.data('id')});
								});
							}
							
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['USERNAME']);
									ids.push(data[index]['USER_ID']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								popup.layerClose("selectUserForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							if(SelectUser.type=='checkbox'){
								SelectUser.addRow(selectRow['USER_ID'],selectRow['USERNAME']);
							}else{
								el.val(selectRow['USERNAME']);
								if(el.prev().length>0){
									el.prev().val(selectRow['USER_ID']);
								}
								popup.layerClose("selectUserForm");
							}
						}
					},
					createGroup:function(){
						layer.prompt({title:'请输入组名称',offset:'20px'},function(val,index){
							layer.close(index);
							var ids = []; 
							$(".nowUser [data-id]").each(function(){
								var t = $(this);
								ids.push(t.data('id'));
							});
							var data = {groupName:val,userIds:ids.join()};
							ajax.remoteCall("/yq-work/web/flow/config/addGroup",data,function(result) { 
								if(result.state == 1){
									layer.msg(result.msg,{icon:1,time:800},function(){
										layer.closeAll();
									});
								}else{
									layer.alert(result.msg,{icon: 5});
								}
						   });
							
						});
					},
					initTree:function(){
						ajax.remoteCall("${ctxPath}/servlet/ehr?action=getFrameworkTree",{},function(result) { 
							var data=result['data']['children'];
							var str = JSON.stringify(data);
							str = str.replaceAll('name','title');
							var newData=eval('('+str+')');
							layui.use('tree', function(){
								 var tree = layui.tree;
								 tree.render({
								      elem: '#ztree',
								      data:newData,
								      showLine:false,
								      accordion:true,
								      click: function(obj){
								    	 $('#groupId').val('');
								    	 $("#deptId").val(obj.data.deptId);
								    	 $("#deptCode").val(obj.data.deptCode);
								    	 SelectUser.query();
								      }
								   }
								 );
							 });
							$(".layui-tree-main:eq(0)").click();
						});
					},
					selectGroup:function(groupId){
						$('#groupId').val(groupId);
						SelectUser.query();
					}
			};
			
			function recentContacts(el){
				var val = $(el).find('input').prop("checked");
				if(val){
					$('#selectUserForm #busiType').val('${param.busiType}');
				}else{
					$('#selectUserForm #busiType').val('');
				}
			}
			
			
			$(function(){
				$('#selectUserForm').render({success:function(){
					SelectUser.initTree();
					SelectUser.initTable();
					layui.element.on('tab(typeFilter)', function(){
						// layer.msg( this.getAttribute('data-val'));
				    });
				}});
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>