<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>
		.layui-table-cell{padding: 0 6px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
					<div class="ibox-content">
						<div class="layui-tab layui-tab-brief" lay-filter="tabBrief">
						  <ul class="layui-tab-title">
						    <li id="my" class="layui-this">我写的周报</li>
						    <li id="recive">收到的周报<span class="layui-badge" id="num">0</span></li>
						  </ul>
						  <div class="layui-tab-content">
						    <div class="layui-tab-item layui-show">
							    <table class="layui-hide" id="list"></table>
						    </div>
						    <div class="layui-tab-item">
						    	<input type="text" name="userName" class="form-control input-sm mb-10" placeholder="姓名"/>
						    	<input type="text" name="depts" class="form-control input-sm mb-10" placeholder="部门"/>
						    	<select name="weekNo"  class="form-control input-sm mb-10">
						    		<option value="">请选择周数</option>
						    		<c:forEach begin="1" step="1" end="52" var="item">
						    			<option value="${item }">第${item }周</option>
						    		</c:forEach>
						    	</select>
						    	<button type="button" class="btn btn-sm btn-default mb-10" onclick="list.query2()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							    <table class="layui-hide mt-15" id="list2"></table>
						    </div>
						  </div>
						</div>    
						
					
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
			{{if WEEKLY_ID}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>
				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看</a>
			{{else}}
				<a class="layui-btn layui-btn-info layui-btn-xs" lay-event="list.add">补填</a>
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="bar2">
			{{if RECEIVER == currentUserId && STATUS == 1}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">审批</a>
			{{else}}
				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看</a>
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			layui.use('element', function(){
				var element = layui.element;
				 element.on('tab(tabBrief)', function(elem){
	    			    var id=$(this).attr('id');
	    			    if(id=='my'){
							list.init();
	    			    }else{
							list.recentList();
	    			    }
	    		  });
				list.init();
				list.recentList();
				
			});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'WeeklyDao.myWeeklyList',
					id:'list',
					skin:'line',
					cellMinWidth:100,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'TITLE',
						title: '标题',
						align:'left',
						minWidth:250,
						templet:function(row){
							if(row.currentWeekNo==row.WEEK_NO){
								return '<span class="layui-badge layui-bg-green">本周</span> '+row.TITLE;
							}
							if(row.currentWeekNo==(parseInt(row.WEEK_NO)+1)){
								return '<span class="layui-badge layui-bg-blue">上周</span> '+row.TITLE;
							}
							return row.TITLE;
						}
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						width:170,
						align:'left'
					},{
					    field: 'STATUS',
						title: '状态',
						width:190,
						align:'left',
						templet:function(row){
							if(row.STATUS==0){
								return '<span class="layui-badge">未发出</span>';
							}if(row.STATUS==1||row.STATUS==2){
								return '已发出';
							}else{
								return '未填写';
							}
						}
					},{
						title: '操作',
						width:180,
						align:'left',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]]}
				);
			},
			recentList:function(){
				$("#searchForm").initTable({
					mars:'WeeklyDao.myReceiveWeeklyList',
					id:'list2',
					height:'full-200',
					cellMinWidth:100,
					limit:20,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'CREATOR',
						title: '填写人',
						align:'left',
						width:160,
						templet:function(row){
							return row.DEPTS+"."+getUserName(row.CREATOR);
						}
					   }
					 ,{
					    field: 'TITLE',
						title: '标题',
						minWidth:250,
						align:'left',
						event:'list.detail',
						style:'color:#1E9FFF;cursor:pointer',
						templet:function(row){
							if(row.TYPE=='1'){
								return row.TITLE;
							}else{
								return "<span class='layui-badge-rim'>抄送</span>&nbsp;"+row.TITLE;
							}
						}
					}
					 ,{
						    field: 'RECEIVER',
							title: '审批人/抄送人',
							align:'left',
							width:140,
							templet:function(row){
								if(row.TYPE=='1'){
									return getUserName(row.RECEIVER);
								}else{
									return row.DEPTS+"."+row.USERNAME;
								}
							}
						 }
					,{
					    field: 'SEND_TIME',
						title: '收到时间',
						align:'left',
						width:200,
						templet:function(row){
							if(getCurrentUserId()==row.RECEIVER||isSuperUser){
								var status=row.STATUS;
								if(status==2){
									status='<span class="layui-badge layui-bg-green">已阅</span> ';
								}else{
									status='<span class="layui-badge">待阅</span> ';
								}
								return status+row.SEND_TIME;
							}else{
								return row.SEND_TIME;
							}
						}
					},{
						title: '操作',
						align:'left',
						width:90,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar2',row);
						}
					}
					]],done:function(result){
						$("#num").text(result['totalRow']);
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'list'});
			},
			query2:function(){
				$("#searchForm").queryData({id:'list2'});
			},
			edit:function(data){
				var width=$(window).width();
				var fullFlag=false;
				if(width<700){
					fullFlag=true;
				}
				popup.layerShow({type:1,full:fullFlag,shade:false,maxmin:true,anim:0,scrollbar:false,offset:'20px',area:['850px','600px'],url:'${ctxPath}/pages/weekly/weekly-edit.jsp',title:'编辑周报',data:{weeklyId:data.WEEKLY_ID}});
			},
			add:function(data){
				var width=$(window).width();
				var fullFlag=false;
				if(width<700){
					fullFlag=true;
				}
				popup.layerShow({type:1,full:fullFlag,shade:false,maxmin:true,anim:0,scrollbar:false,offset:'20px',area:['850px','600px'],url:'${ctxPath}/pages/weekly/weekly-edit.jsp',title:'新增周报',data:data});
			},
			detail:function(data){
				var width=$(window).width();
				var fullFlag=false;
				if(width<700){
					fullFlag=true;
				}
				popup.layerShow({type:1,full:fullFlag,shade:false,maxmin:true,anim:0,scrollbar:false,offset:'20px',area:['850px','80%'],url:'${ctxPath}/pages/weekly/weekly-detail.jsp',title:'周报详情',data:{weeklyId:data.WEEKLY_ID}});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>