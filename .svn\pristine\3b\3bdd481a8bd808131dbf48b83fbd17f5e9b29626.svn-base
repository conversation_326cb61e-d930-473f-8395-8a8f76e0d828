<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>Kanban</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editForm" class="form-horizontal" data-mars="KanbanDao.kanbanInfo"  data-mars-prefix="kanban.">
	     		<input type="hidden" name="kanbanId" value="${param.kanbanId }"/>
	     		<input type="hidden" name="kanban.KANBAN_ID" value="${param.kanbanId }"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			        	<c:choose>
             				<c:when test="${param.projectId!=''}">
	                		 	 <tr style="display: none;"><td colspan="2"><input type="hidden" value="${param.projectId}" name="kanban.PROJECT_ID"/></td></tr>
             				 </c:when>
             				 <c:otherwise>
             				 	 <tr <c:if test="${param.type==0&&param.kanbanId!=''}">style="display:none;"</c:if>>
				                	<td>
				                		所属项目
				                	</td>
				                	<td>
				                		<c:choose>
				                			<c:when test="${param.type==0}">
						                		 <input id="projectId" type="hidden" value="0"  name="kanban.PROJECT_ID"/>
				                			</c:when>
				                			<c:otherwise>
							                	 <input type="hidden"  name="kanban.PROJECT_ID"/>
				              					 <input id="projectId" placeholder="点击选择项目" ts-selected="${param.projectId}" type="text" class="form-control"/>
				                			</c:otherwise>
				                		</c:choose>
				                	</td>
		                		</tr>
             				 </c:otherwise>
             			   </c:choose>
			            <tr>
		                    <td style="width: 80px;" class="required">看板名称</td>
		                    <td><input data-rules="required" maxlength="100" placeholder="xx月计划,xx工作安排" type="text" name="kanban.KANBAN_NAME" class="form-control"></td>
			            </tr>
			            <tr>
		                    <td style="width: 80px;">看板描述</td>
		                    <td><textarea style="height: 80px;" maxlength="100" name="kanban.KANBAN_DESC" class="form-control"></textarea></td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <c:if test="${!empty param.kanbanId}"> <button type="button" class="pull-right mt-15 mr-10 btn btn-sm btn-link" onclick="Kanban.delData()"> 删除  </button></c:if>
				    	  <button type="button" class="btn btn-primary ml-15" onclick="Kanban.ajaxSubmitForm()"> 保 存  </button>
					      <button type="button" title="关闭" class="btn btn-default ml-10" onclick="layer.closeAll();"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	    var Kanban = {};
	    Kanban.kanbanId='${param.kanbanId}';
		$(function(){
			$("#editForm").render();
			layui.config({
				  base: '${ctxPath}/static/js/'
			}).use('tableSelect'); //加载自定义模块
			
			layui.use(['element','tableSelect'], function(){
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#projectId',
					searchKey: 'projectName',
					checkedKey: 'PROJECT_ID',
					page:true,
					searchPlaceholder: '请输入项目名称',
					table: {
						mars: 'ProjectDao.selectProjectList',
						cols: [[
							{ type: 'radio' },
							{ field: 'PROJECT_ID',hide:true,title: 'ID'},
							{ field: 'PROJECT_NAME', title: '名称', width: 320 },
							{ field: 'PROJECT_NO', title: '编号', width: 150 }
						]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.PROJECT_NAME)
							ids.push(item.PROJECT_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
						//$("[name='kanban.KANBAN_NAME']").val(names.join(","));
					}
				});

			});
		});

			
		Kanban.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Kanban.kanbanId){
					Kanban.updateData(); 
				}else{
					Kanban.insertData(); 
				}
			}
		}
		Kanban.insertData = function(flag) {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/kanban?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadKanban();
						popup.openTab({id:'kanbanDetail',url:'${ctxPath}/pages/kanban/kanban-detail.jsp',title:'看板',data:{kanbanId:result.data}});
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Kanban.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/kanban?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadKanban();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Kanban.delData = function(flag) {
			var data = form.getJSONObject("#editForm");
			layer.confirm("确定删除吗?不可恢复",{offset:'30px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/kanban?action=delKanban",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
							reloadKanban();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>