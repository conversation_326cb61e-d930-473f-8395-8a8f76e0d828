<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>通知公告</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
				<div class="ibox">
					<div class="ibox-content">
						<div class="layui-tab layui-tab-brief">
					        <ul class="layui-tab-title">
					          <li class="layui-this">全部消息</li>
					          <li>未读<span class="layui-badge notRead">0</span></li>
					          <li>已读<span class="layui-badge hasRead">0</span></li>
					        </ul>
					        <div class="layui-tab-content">
					          <div class="layui-tab-item layui-show">
								    <table class="layui-hide" id="list"></table>
					          </div>
					           <div class="layui-tab-item">
								    <table class="layui-hide" id="list2"></table>
					           </div>
					            <div class="layui-tab-item">
								    <table class="layui-hide" id="list3"></table>
					            </div>
					         </div>
	         			 </div>
					</div>
				</div>
				 <script type="text/html" id="opBar">
						<button type="button" lay-event="list.readOk" class="btn btn-sm btn-default mr-15">标记已读</button>
						<button type="button" lay-event="list.allReadOk" class="btn btn-sm btn-default">全部已读</button>
				</script>
	</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
	    var type='${param.type}'; 
		var flowList = {
			'task':{title:'任务',url:'',flowCode:''},	
			'weekly':{title:'周报',url:'',flowCode:''},	
			'FLOW_ORDER_REVIEW_CHECK':{title:'采购评审',url:'',flowCode:''},	
			'FLOW_ORDER_PAY_CHECK':{title:'付款申请',url:'',flowCode:''}
		};
		$(function(){
			ajax.daoCall({loading:false,controls:['FlowConfigDao.categoryList'],params:{}},function(rs){
				var data = rs['FlowConfigDao.categoryList'].data;
				for(var index  in data){
					flowList[data[index]['FLOW_CODE']] = {title:data[index]['FLOW_NAME'],url:'yq-work/web/flow',flowCode:data[index]['FLOW_CODE']};
				}
			    list.init();
			});
			
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'CommonDao.myMessage',
					data:{type:type},
					rowEvent:'list.detail',
					limit:20,
					id:'list',
					skin:'line',
					even:true,
					height:'full-100',
					cols: [[
		             {
						title: '编号',
						width:50,
						type:'numbers'
					 },{
						 filed:'TYPE_NAME',
						 title:'类型',
						 width:120,
						 templet:function(row){
							 var typeName = row['TYPE_NAME'];
							 var obj = flowList[typeName];
							 if(obj){
								 return obj.title;
							 }else{
								 return '';
							 }
						 }
					 },{
					    field: 'CONTENTS',
						title: '标题内容',
						align:'left',
						minWidth:230,
						style:'cursor:pointer',
						templet:function(row){
							if(row['MSG_STATE']=='1'){
								return '<span class="layui-badge-dot"></span> '+row['CONTENTS'];
							}else{
								return row['CONTENTS'];
							}
						}
						
					},{
						field:'SEND_NAME',
						title:'发件人',
						width:80
					},{
						field:'RECEIVE_NAME',
						title:'收件人',
						width:80
					},{
					    field: 'CREATE_TIME',
						title: '时间',
						align:'center',
						width:180,
						sort:true
					}
					]],done:function(res,curr,count){
						
					}
				  }
				);
				$("#searchForm").initTable({
					mars:'CommonDao.myMessage',
					data:{msgState:'1'},
					rowEvent:'list.detail',
					limit:20,
					id:'list2',
					skin:'line',
					even:true,
					height:'full-100',
					cols: [[
		             {
						title: '编号',
						width:50,
						type:'numbers'
					 },{
						 field:'MSG_ID',
						 title:'id',
						 hide:true
					  },{
						 filed:'',
						 title:'类型',
						 width:100,
						 templet:function(row){
							 var typeName = row['TYPE_NAME'];
							 var obj = flowList[typeName];
							 if(obj){
								 return obj.title;
							 }else{
								 return '';
							 }
						 }
					 },{
					    field: 'CONTENTS',
						title: '标题内容',
						align:'left',
						minWidth:230,
						style:'cursor:pointer'
						
					},{
						field:'SEND_NAME',
						title:'发件人',
						width:80
					},{
						field:'RECEIVE_NAME',
						title:'收件人',
						width:80
					},{
					    field: 'CREATE_TIME',
						title: '发送时间',
						align:'center',
						width:180,
						sort:true
					}
					]],done:function(res,curr,count){
						$('.notRead').text(count);
					}
				  }
				);
				$("#searchForm").initTable({
					mars:'CommonDao.myMessage',
					data:{msgState:'2'},
					rowEvent:'list.detail',
					limit:20,
					id:'list3',
					skin:'line',
					even:true,
					height:'full-100',
					cols: [[
		             {
						title: '编号',
						width:50,
						type:'numbers'
					 },{
						 field:'MSG_ID',
						 title:'id',
						 hide:true
					 },{
						 filed:'',
						 title:'类型',
						 width:100,
						 templet:function(row){
							 var typeName = row['TYPE_NAME'];
							 var obj = flowList[typeName];
							 if(obj){
								 return obj.title;
							 }else{
								 return '';
							 }
						 }
					 },{
					    field: 'CONTENTS',
						title: '标题内容',
						align:'left',
						minWidth:230,
						style:'cursor:pointer'
						
					},{
						field:'SEND_NAME',
						title:'发件人',
						width:80
					},{
						field:'RECEIVE_NAME',
						title:'收件人',
						width:80
					},{
						field:'READ_TIME',
						title:'阅读时间',
						width:140
					},{
					    field: 'CREATE_TIME',
						title: '发送时间',
						align:'center',
						width:180,
						sort:true
					}
					]],done:function(res,curr,count){
						$('.hasRead').text(count);
					}
				  }
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'list'});
			},
			reload:function(){
				$("#searchForm").queryData({id:'list'});
			},
			advDetail:function(data){
			
			},
			detail:function(data){
				var msgState = data.MSG_STATE;
				if(msgState=='1'){
					  var id = data.MSG_ID;
					  ajax.remoteCall("${ctxPath}/servlet/comment?action=setRead",{id:id},function(result) { 
							if(result.state == 1){
								doExcute();
								$("#searchForm").queryData({id:'list2',data:{msgState:'1'}});
							}else{
								layer.alert(result.msg);
							}
						  }
						);
				}else{
					doExcute();
				}
				function doExcute(){
					var typeName = data['TYPE_NAME'];
					var fkId = data['FK_ID'];
					var title = data['CONTENTS']||'详情';
					if(typeName=='task'){
						popup.openTab({id:"messageDetail",url:'/yq-work/pages/task/task-detail.jsp',title:title,data:{taskId:fkId,isDiv:0}});
					}else if(typeName=='weekly'){
						popup.openTab({id:"messageDetail",url:'/yq-work/weekly',title:title,data:{weeklyId:fkId,isDiv:0}});
					}else if(typeName=='FLOW_ORDER_REVIEW_CHECK'){
						popup.openTab({id:"messageDetail",url:'/yq-work/pages/erp/order/review/review-detail-query.jsp',title:'采购评审',data:{fkId:fkId,isDiv:0}});
					}else if(typeName=='FLOW_ORDER_PAY_CHECK'){
						popup.openTab({id:"messageDetail",url:'/yq-work/pages/erp/order/payment/payment-detail-query.jsp',title:'付款申请',data:{fkId:fkId,isDiv:0}});
					}else{
						 var obj = flowList[typeName];
						 popup.openTab($.extend({},obj,{data:{businessId:fkId,isDiv:0},url:'/yq-work/web/flow/'+fkId}));
					}
				}
			},
			op:function(){
				layer.msg('时间有限,下次实现。');
			},
			readOk:function(data){
				
				
			},
			allReadOk:function(data){
				
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>