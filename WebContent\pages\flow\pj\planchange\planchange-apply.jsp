<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>关联项目计划</td>
					  			<td>
					  				<input name="apply.data19" type="hidden"/>
					  				<input name="apply.data18" type="hidden"/>
					  				<input type="hidden" name="apply.relatedFlow"/>
					  				<input type="text" onclick="selectProjectPlan(this)" data-flow-code="pj_plan" placeholder="点击此选择,若是历史无计划此处填写合同名称"  class="form-control input-sm select-icon" name="apply.data1"/>
					  			</td>
					  			<td>合同类型</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>签约单位</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  			<td>签订日期</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>销售经理</td>
					  			<td>
					  				<input type="text" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td>开发负责人</td>
					  			<td>
					  				<input type="text" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>工程大区</td>
					  			<td>
					  				<input name="apply.data17" id="deptLeaderName" type="hidden"/>
					  				<input type="text" class="form-control input-sm" readonly="readonly" onclick="singleDept(this)" data-dept-name="工程" data-ref-dept-leader-name="#deptLeaderName" name="apply.data7"/>
					  			</td>
					  			<td>项目经理</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" onclick="singleUser(this);" readonly="readonly" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">项目当前阶段</td>
					  			<td>
					  				<input type="text" class="form-control input-sm" name="apply.data9"/>
					  			</td>
					  			<td class="required">变更日期</td>
					  			<td>
					  				<input type="text" class="form-control input-sm Wdate" data-laydate="{type:'date'}" name="apply.data10"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>原计划开工时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data11"/>
					  			</td>
					  			<td>现计划开工时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data12"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>原计划初验时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" data-rules="required" class="form-control input-sm Wdate" name="apply.data13"/>
					  			</td>
					  			<td>现计划初验时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" data-rules="required" class="form-control input-sm Wdate" name="apply.data14"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>原计划终验时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" data-rules="required" class="form-control input-sm Wdate" name="apply.data15"/>
					  			</td>
					  			<td>现计划终验时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" data-rules="required" class="form-control input-sm Wdate" name="apply.data16"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>变更原因</td>
					  			<td>
									<textarea style="height: 60px;" class="form-control input-sm" data-rules="required" name="apply.applyRemark"></textarea>
					  			</td>
					  			<td>变更分类</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.data20" data-rules="required">
					  					<option value="">--</option>
								        <option value="开发">开发</option>
								        <option value="工程">工程</option>
					  					<option value="商务">商务</option>
								        <option value="客户">客户</option>
								        <option value="第三方">第三方</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr class="fileListTr">
					  			<td>相关附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  				<small class="ml-10 unedit-remove">项目启动会会议纪要附件,项目综合计划附件</small>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideUserTitle:true,title:'项目计划变更-{{:data18}}',success:function(data){
				var param = FlowCore.params;
				if(param.handle=='add'){
					var json = getQueryParams();
					var contractId = json['contractId'];
					queryContractInfo(contractId);
					fillRecord(json,"apply.",",","#flowForm");
				}
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			Flow.initData();
			var planName = $("[name='apply.data1']").val();
			if(planName==''){
				 $("[name='apply.data1']").val('无');
			}
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		Flow.initData = function(){
			var pmLeader = $("[name='apply.data8']").val();
			var params = {selectUserName:pmLeader,ccNames:'许志远,张明东,李千帆,陈莹,邓从健,邵德伟,汤湛成'};
			FlowCore.approveData = params;
		}
		
		Flow.approveLayer = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
		}
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='项目经理'){
				var data12 = $("[name='apply.data12']").val();
				if(data12==''){
					layer.msg('填写现计划开工时间',{icon:7,offset:'20px',time:1200});
					return false;
				}
				var data14 = $("[name='apply.data14']").val();
				if(data14==''){
					layer.msg('填写现计划初验时间',{icon:7,offset:'20px',time:1200});
					return false;
				}
				FlowCore.setting.uploadAuth = true;
				var selectUserName = $("[name='apply.data17']").val();
				var params = {selectUserName:selectUserName};
				FlowCore.approveData = params;
				return true;
			}
			if(nodeCode=='工程大区主管'){
				var selectUserName = $("[name='apply.data5']").val();
				var params = {selectUserName:selectUserName};
				FlowCore.approveData = params;
			}
			if(nodeCode=='归档'){
				
			}
			return true;
	    }
		
		FlowCore.flowEnd = function(){
			var applyInfo = FlowCore.applyInfo;
			var data = {applyId:applyInfo['applyId'],contractId:applyInfo['data19'],cyDate:applyInfo['data14'],zyDate:applyInfo['data16'],sxDate:applyInfo['data13'],beginDate:applyInfo['data12'],problemBy:applyInfo['data20'],logContent:''};
			ajax.remoteCall("${ctxPath}/servlet/project/conf?action=setProjectPlanchange",data,function(result) { 
				if(result.state == 1){
					
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});	
		}
		
		function selectProjectPlan(el){
			FlowCore.selectRelatedFlow(el,'radio');
		}
		
		function selctFlowCallBack(row){
			$("[name='apply.relatedFlow']").val(row['APPLY_ID']);
			
			$("[name='apply.data1']").val(row['APPLY_TITLE']);
			$("[name='apply.data2']").val(row['DATA2']);
			$("[name='apply.data3']").val(row['DATA5']);
			$("[name='apply.data4']").val(row['DATA6']);
			$("[name='apply.data5']").val(row['DATA7']);
			$("[name='apply.data6']").val(row['DATA8']);
			$("[name='apply.data7']").val(row['DATA9']);
			$("[name='apply.data8']").val(row['DATA11']);
			$("[name='apply.data18']").val(row['DATA3']);
			$("[name='apply.data17']").val(row['DATA10']);
			$("[name='apply.data11']").val(row['DATA12']);
			$("[name='apply.data13']").val(row['DATA14']);
			$("[name='apply.data15']").val(row['DATA15']);
			
			
		}
		
		function queryContractInfo(contractId){
			if(contractId){
				$("[name='apply.data19']").val(contractId);
				ajax.remoteCall(getCtxPath()+"/webcall?action=ProjectDao.record",{'project.PROJECT_ID':contractId},function(result) { 
					if(result.data){
						$("[name='apply.applyTitle']").val('项目计划变更-'+result.data['PROJECT_NAME']);
						$("[name='apply.data18']").val(result.data['PROJECT_NAME']);
						$("[name='apply.data13']").val(result.data['CY_DATE']);
						$("[name='apply.data15']").val(result.data['ZY_DATE']);
						$("[name='apply.data9']").val(result.data['PROJECT_STAGE']);
						$("[name='apply.data5']").val(result.data['SALES_BY_NAME']);
						$("[name='apply.data6']").val(result.data['PO_NAME']);
						$("[name='apply.data8']").val(result.data['PROJECT_PO_NAME']);
					}
				});
				ajax.remoteCall(getCtxPath()+"/webcall?action=ProjectContractDao.record",{contractId:contractId},function(result) { 
					if(result.data){
						var contractInfo = result.data;
						$("[name='apply.data3']").val(contractInfo['SIGN_ENT']);
						$("[name='apply.data4']").val(contractInfo['SIGN_DATE']);
						$("[name='apply.data2']").val(contractInfo['CONTRACT_TYPE']);
						var projectDeptId = result.data['PROJECT_DEPT_ID'];
						if(projectDeptId){
							getDeptInfo(projectDeptId,function(deptInfo){
								$("[name='apply.data7']").val(deptInfo['DEPT_NAME']);
								$("[name='apply.data17']").val(deptInfo['LEADER_NAME']);
							});
						}
					}
				});
			}else{
				layer.alert('建议从合同管理入口申请此流程',{icon:7,offset:'20px'});
			}
		}
		
		function getQueryParams() {
		  const searchParams = new URLSearchParams(window.location.search);
		  const paramsJson = {};
		  for (let param of searchParams.entries()) {
		    paramsJson[param[0]] = param[1];
		  }
		  return paramsJson;
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>