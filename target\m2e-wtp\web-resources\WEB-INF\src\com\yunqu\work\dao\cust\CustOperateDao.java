package com.yunqu.work.dao.cust;

import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import com.alibaba.fastjson.JSONObject;
import org.easitline.common.db.EasySQL;

//客户运营、销售
@WebObject(name="CustOperateDao")
public class CustOperateDao extends AppDaoContext {

    @WebControl(name="salePriceRecord",type=Types.RECORD)
    public JSONObject salePriceRecord(){
        String priceId = param.getString("priceId");
        return queryForRecord("select t1.*,concat(t2.BUSINESS_NAME,'-',t2.CUST_NAME) as platform_order_name from yq_crm_sales_price t1 left join yq_crm_platform_order t2 on t1.platform_order_id = t2.order_id where t1.price_id = ?",priceId);
    }

    @WebControl(name = "salePriceList",type = Types.LIST)
    public JSONObject salePriceList(){
        EasySQL sql= new EasySQL("select * from yq_crm_sales_price t1 where 1=1");
        sql.append(param.getString("custId"),"and t1.cust_id = ?");
        sql.append(param.getString("platformId"),"and t1.platform_id = ?");
        sql.append(param.getString("platformOrderId"), "and PLATFORM_ORDER_ID = ?");
        sql.append("order by t1.CREATE_TIME desc");
        return queryForList(sql.getSQL(),sql.getParams());
    }
    @WebControl(name = "salePricePageList",type = Types.LIST)
    public JSONObject salePricePageList(){
        EasySQL sql= new EasySQL("select * from yq_crm_sales_price t1 where 1=1");
        sql.append(param.getString("custId"),"and t1.cust_id = ?");
        sql.append(param.getString("platformId"),"and t1.platform_id = ?");
        sql.append("order by t1.CREATE_TIME desc");
        return queryForPageList(sql.getSQL(),sql.getParams());
    }

    @WebControl(name="costRecord",type=Types.RECORD)
    public JSONObject costRecord(){
        String costId = param.getString("costId");
        EasySQL sql= new EasySQL("select t1.*,concat(t2.BUSINESS_NAME,'-',t2.CUST_NAME) as platform_order_name,t3.apply_title from yq_crm_cost t1 left join yq_crm_platform_order t2 on t1.platform_order_id = t2.order_id left join yq_flow_apply t3 on t1.bx_apply_id = t3.apply_id where 1=1");
        sql.append(costId,"and t1.cost_id = ?",false);
        sql.append("order by t1.CREATE_TIME desc");
        return queryForRecord(sql.getSQL(),sql.getParams());
    }

    @WebControl(name = "costList",type = Types.LIST)
    public JSONObject costList(){
        EasySQL sql= new EasySQL("select * from yq_crm_cost t1 where 1=1");
        if("contractId".equals(param.getString("costListBy"))){
            sql.append(param.getString("contractId"),"and t1.contract_id = ?");
        }else if("settlementId".equals(param.getString("costListBy"))){
            sql.append(param.getString("settlementId"),"and t1.SETTLEMENT_ID = ?");
        }else{
            sql.append(param.getString("custId"),"and t1.cust_id = ?");
        }
        sql.append(param.getString("platformId"),"and t1.PLATFORM_ID = ?");
        sql.append("order by t1.CREATE_TIME desc");
        return queryForList(sql.getSQL(),sql.getParams());
    }

    @WebControl(name = "costPageList",type = Types.LIST)
    public JSONObject costPageList(){
        EasySQL sql= new EasySQL("select t1.*,t2.PLATFORM_TYPE_NAME from yq_crm_cost t1 left join yq_business_platform t3 on t1.PLATFORM_ID = t3.PLATFORM_ID left join yq_business_platform_type t2 on t3.PLATFORM_TYPE_ID = t2.PLATFORM_TYPE_ID where 1=1");
        sql.append(param.getString("custId"),"and t1.cust_id = ?");
        sql.append(param.getString("platformId"),"and t1.PLATFORM_ID = ?");
        sql.append(param.getString("platformTypeId"),"and t3.PLATFORM_TYPE_ID = ?");
        sql.append(param.getString("costType"),"and t1.RELATE_TYPE = ?");
        sql.append(param.getString("costOwner"),"and t1.COST_OWNER = ?");
        sql.append(param.getString("costBeginDate"),"and t1.DATE_ID >= ?");
        sql.append(param.getString("costEndDate"),"and t1.DATE_ID <= ?");
        sql.appendLike(param.getString("costName"),"and t1.COST_NAME like ?");
        sql.appendLike(param.getString("contractName"),"and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"),"and t1.CUST_NAME like ?");
        sql.appendLike(param.getString("supplierName"),"and t1.SUPPLIER_NAME like ?");
        sql.append("order by t1.DATE_ID desc,t1.CREATE_TIME desc");
        return queryForPageList(sql.getSQL(),sql.getParams());
    }

    @WebControl(name="settleRecord",type=Types.RECORD)
    public JSONObject settleRecord(){
        String settlementId = param.getString("settlementId");
        return queryForRecord("select * from yq_crm_settlement where SETTLEMENT_ID = ?",settlementId);
    }

    @WebControl(name = "settleList",type = Types.LIST)
    public JSONObject settleList(){
        EasySQL sql= new EasySQL("select * from yq_crm_settlement t1 where 1=1");
        sql.append(param.getString("custId"),"and t1.cust_id = ?");
        sql.append(param.getString("platformId"),"and t1.platform_id = ?");
        sql.append("order by t1.CREATE_TIME desc");
        return queryForList(sql.getSQL(),sql.getParams());
    }

    @WebControl(name = "settlePageList",type = Types.LIST)
    public JSONObject settlePageList(){
        EasySQL sql= new EasySQL("select * from yq_crm_settlement t1 where 1=1");
        sql.append(param.getString("custId"),"and t1.cust_id = ?");
        sql.append(param.getString("platformId"),"and t1.platform_id = ?");
        sql.append("order by t1.CREATE_TIME desc");
        return queryForPageList(sql.getSQL(),sql.getParams());
    }

    @WebControl(name = "settleInvoiceList",type = Types.LIST)
    public JSONObject settleInvoiceList(){
        String settlementId = param.getString("settlementId");
        EasySQL sql = new EasySQL("select * from yq_crm_invoice where 1=1");
        sql.append(settlementId, "and SETTLEMENT_ID = ?");
        sql.append("order by create_time desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "orderCostList",type = Types.LIST)
    public JSONObject orderCostList(){
        String platformOrderId = param.getString("platformOrderId");
        EasySQL sql = new EasySQL("select * from yq_crm_cost where 1=1");
        sql.append(platformOrderId, "and PLATFORM_ORDER_ID = ?");
        sql.append("order by DATE_ID desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "settleQueryPageList",type = Types.LIST)
    public JSONObject settleQueryPageList(){
        EasySQL sql = new EasySQL("select t1.*,t2.PLATFORM_TYPE_NAME,t4.CONTRACT_SIMPILE_NAME,t4.SALES_BY_NAME from yq_crm_settlement t1 left join yq_business_platform t3 on t1.PLATFORM_ID = t3.PLATFORM_ID left join yq_business_platform_type t2 on t3.PLATFORM_TYPE_ID = t2.PLATFORM_TYPE_ID left join yq_project_contract t4 on t1.CONTRACT_ID = t4.CONTRACT_ID where 1=1");
        sql.append(param.getString("platformTypeId"),"and t3.PLATFORM_TYPE_ID = ?");
        sql.append(param.getString("platformId"),"and t1.PLATFORM_ID = ?");
        sql.append(param.getString("settlementType"),"and t1.SETTLEMENT_TYPE = ?");
        sql.append(param.getString("paymentStatus"),"and t1.PAYMENT_STATUS = ?");
        sql.append(param.getString("monthBegin"),"and t1.MONTH_ID >= ?");
        sql.append(param.getString("monthEnd"),"and t1.MONTH_ID <= ?");
        sql.append(param.getString("createBeginDate"),"and DATE(t1.CREATE_TIME) >= ?");
        sql.append(param.getString("createEndDate"),"and DATE(t1.CREATE_TIME) <= ?");
        sql.append(param.getString("saleBy"),"and t4.SALES_BY = ?");
        sql.appendLike(param.getString("custName"),"and t1.CUST_NAME like ?");
        sql.appendLike(param.getString("contractName"),"and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("settlementNo"),"and t1.SETTLEMENT_NO like ?");
        sql.append("order by t1.MONTH_ID desc,t1.CREATE_TIME desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }
}
