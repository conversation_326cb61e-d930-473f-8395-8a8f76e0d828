<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>流程查询</title>
	<style>
		.ibox-content{min-height: calc(100vh - 130px)};
		.input-pading{padding: 5px 6px!important;}
	</style>
	<link href="${ctxPath}/static/css/sidebar.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
		<div class="page-box">
			 <input type="hidden" name="treeFlowCode" value="${param.flowCode}"/>
	  		 <div class="left-sidebar" style="display: none;">
                <div class="left-sidebar-item" data-mars="FlowDao.flowListTree" data-template="template-list"></div>
       		 </div>
        	<div onclick="leftShow(this)" class="leftShow" style="position: absolute;display: inline-flex;align-items: center;height: 50px;background-color: #00abfc;top:40%;cursor: pointer;color: #fff;"><i class="fa fa-chevron-right" aria-hidden="true"></i></div>
  			<div class="right-content" style="width: 100%;margin-left: 0px;">
  			  <div class="ibox" >
				<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="flowMgrForm">
				   <div class="ibox-title clearfix">
				   	 <div class="pull-left layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: -4px 2px 5px;">
          		 	 	 <ul class="layui-tab-title"><li data-val="" class="layui-this">全部</li><li data-val="10,20,21">进行中</li><li data-val="30">已结束</li></ul>
          		 	 </div>
          		 	 <button type="button" id="conditionFiter" class="btn btn-sm btn-default pull-right mt-5 layui-hide-xs"><span class="glyphicon glyphicon-filter"></span> 高级查询</button>
	          		 <div class="form-group layui-hide-xs" style="display: inline-block;">
						 <div class="input-group input-group-sm">
	          		  		 <span class="input-group-addon">发起日期</span>
	          		  		 <c:choose>
	          		  		 	<c:when test="${!empty param.flowCode}">
									 <input type="text" name="applyBeginDate" data-mars="CommonDao.appointedDay(-365)" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;display: inline-block">
	          		  		 	</c:when>
	          		  		 	<c:otherwise>
									 <input type="text" name="applyBeginDate" data-mars="CommonDao.appointedDay(-120)" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;display: inline-block">
	          		  		 	</c:otherwise>
	          		  		 </c:choose>
							 <input type="text" name="applyEndDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;display: inline-block">
	          		 	 </div>
						 <div class="input-group input-group-sm" style="width: 180px;">
					 		<span class="input-group-addon">流程标题</span>
						 	<input class="form-control input-sm input-pading" type="text" name="applyTitle">
						 </div>
	          			 <div class="input-group input-group-sm" style="width: 180px;">
					 		<span class="input-group-addon">申请编号</span>
						 	<input class="form-control input-sm input-pading" type="text" name="applyNo">
					 		<input type="hidden" name="flowCode" id="flowCode" value="${flowCode}"/>
						 </div>
						 <c:if test="${!empty param.q}">
						 	<input type="hidden" name="condition.${param.q}_exact" value="${param.v}"/>
						 </c:if>
						 <div id="morCondition" style="display: inline;"></div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="flowList.queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="flowList.reset()"><span class="fa fa-eraser"></span> 清空</button>
						 </div>
	          	      </div>
	                </div> 
					<div class="ibox-content">
						 <table id="_flowTable"></table>
					</div>
				  </form>
				</div>
				<div class="flowQuery"></div>
  		</div>
 	 </div>
 	 <script id="queryCondition" type="text/x-jsrender">
		<form id="condtionForm">
			<table class="table table-edit table-vzebra">
			 <tr>
				<td>发起人</td>
				<td>
					<input class="form-control input-sm" type="text" name="applyName">
				</td>
				<td>工号</td>
				<td>
					<input class="form-control input-sm" type="text" name="applyStaffNo">
				</td>
			</tr>
			 <tr>
				<td>发起部门</td>
				<td>
					<input class="form-control input-sm" type="text" name="applyDeptName">
				</td>
				<td>显示数据</td>
				<td>
					<select class="form-control input-sm" name="approveAll">
						<option value="0">当前节点</option>
						<option value="1">全部节点</option>
					</select>
				</td>
			</tr>
			 <tr>
				<td>待办人</td>
				<td>
					<input class="form-control input-sm" type="text" name="checkName">
				</td>
				<td>流程状态</td>
				<td>
					<select name="applyState" onchange="flowList.queryData();" class="form-control input-sm">
					  <option value="">请选择 </option>
					  <option data-class="label label-default" value="0">草稿</option>
					  <option data-class="label label-warning" value="1">作废</option>
					  <option data-class="label label-warning" value="5">已挂起</option>
					  <option data-class="label label-warning" value="10">待审批</option>
					  <option data-class="label label-info" value="20">审批中</option>
					  <option data-class="label label-danger" value="21">审批退回</option>
					  <option data-class="label label-success" value="30">已完成</option>
				   </select>	
				</td>
			</tr>
			 <tr>
				<td>紧急程度</td>
				<td colspan="4">
					<select class="form-control input-sm" name="applyLevel">
						<option value="">--</option>
						<option value="正常">正常</option>
						<option value="重要">重要</option>
						<option value="紧急">紧急</option>
					</select>
				</td>
			 </tr>
			<tr>
				<td  colspan="4">
					<button type="button" class="btn btn-sm btn-default" onclick="flowList.conditionReset()"><span class="fa fa-eraser"></span> 清空</button>
					<button type="button" class="btn btn-sm btn-info ml-10" onclick="flowList.queryData()"><span class="glyphicon glyphicon-search"></span> 查询</button>
				</td>
			</tr>
		</table>
	  </form>
    </script>
    
     <script id="condition-list" type="text/x-jsrender">
    	   {{for list}}
				<div class="input-group input-group-sm">
					<span class="input-group-addon">{{:title}}</span>
					{{if query=='daterange'}}
					  <input class="form-control input-sm" type="text" style="width: 100px;display: inline-block" data-laydate="{type:'date'}" name="condition.{{:field}}_{{:query}}_begin">
					  <input class="form-control input-sm" type="text" style="width: 100px;display: inline-block" data-laydate="{type:'date'}" name="condition.{{:field}}_{{:query}}_end">
					{{else query=='range'}}
					  <input class="form-control input-sm" type="text" style="width: 100px;display: inline-block" name="condition.{{:field}}_{{:query}}_begin">
					  <input class="form-control input-sm" type="text" style="width: 100px;display: inline-block" name="condition.{{:field}}_{{:query}}_end">
					{{else}}
					  <input class="form-control input-sm"  {{if fieldType=='number'}}type="number"{{else}}type="text"{{/if}} style="width: 100px;" {{if fieldType=='date'}}data-laydate="{type:'date'}"{{else fieldType=='datetime'}} data-laydate="{type:'datetime'}" {{/if}} name="condition.{{:field}}_{{:query}}">
					{{/if}}
				</div>
			{{/for}}
	  </script>
	 <script id="template-list" type="text/x-jsrender">
			{{for flowCategory}}
 				<div class="sidebar-item">
					<a href="javascript:void(0);" class="sidebar-nav-sub-title active"><i class="sidebar-icon glyphicon glyphicon-menu-right"></i>{{:P_FLOW_NAME}}</a>
				    <ul class="sidebar-nav-sub">
						{{for #parent.parent.data.data}}
							{{if #parent.parent.data.P_FLOW_CODE==P_FLOW_CODE}}
						  		<li>
								  <a href="javascript:void(0)" data-url="{{:QUERY_URL}}" data-code="{{:FLOW_CODE}}">{{:FLOW_NAME}}({{:COUNT}})</a>
						  	  </li>
							{{/if}}
  						 {{/for}}
				    </ul>
				</div>
			{{/for}}
			{{if flowCategory.length==0}}
				<p class="text-c mt-30">无数据</p>
			{{/if}}
		  </script>
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>
		  </script>
		  <script type="text/html" id="btnBar">
		  		<c:if test="${!empty param.flowCode}">
					<a href="javascript:;" onclick="flowApplyLink('${param.flowCode}')" class="btn btn-sm btn-info mr-10">发起流程</a>
				</c:if>
				<a href="javascript:;" onclick="exportExcel()" class="btn btn-sm btn-default">导出数据</a>
		  </script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/yq-work/static/js/flow-list.js?v=20220612"></script>
	<script type="text/javascript">

		  var flowList = {flowCode:'${param.flowCode}'};
		  
		  var condtionData = {};
	
			$(function(){
				$(".page-box").render({success:function(){
					flowList.initList();
					flowList.renderTree();
				}});
			});
			
			function intiCondition(data){
			  var conditionList = FlowMgr.getCondition(data);
			  var queryCondition = $.templates("#queryCondition");
			  var ins = layui.dropdown.render({
				    elem: '#conditionFiter'
				    ,content: queryCondition.render({list:conditionList})
				    ,style: 'width: 700px;min-height: 250px; padding: 15px 15px;border-radius: 6px;box-shadow: 2px 2px 30px rgb(0 0 0 / 12%);'
				    ,ready: function(){
				    	fillRecord(condtionData,'',',','#condtionForm');
				    	 renderDate('#condtionForm');
				    }
			   });
			  $('#conditionFiter').data('layui_dropdown_index', ins.config.id); 
			  
			  var conditionTpl = $.templates("#condition-list");
			  var conditionHtml = conditionTpl.render({list:conditionList});
			  $('#morCondition').html(conditionHtml);
	    	  renderDate('#flowMgrForm');
			  
			  layui.element.on('tab(stateFilter)', function(){
				  flowList.queryData({applyState:this.getAttribute('data-val')});
			  });
			  
			}
			
			flowList.renderTree = function(){
				$('body').on('click','.sidebar-nav-sub-title',function(e){
	    			$(this).toggleClass('active')
	    		})
				$(".left-sidebar-item [data-code]").click(function(){
	    			var t=$(this);
	    			var flowCode = t.data("code");
	    			var queryUrl = t.data("url");
	    			if(queryUrl){
	    				$.get(queryUrl,{flowCode:flowCode,isDiv:'1'},function(html){
		  					$(".ibox").hide();
		  					$(".flowQuery").html(html);
		  					$(".flowQuery .container-fluid").removeClass('container-fluid');
	    				});
	    				return;
	    			}
	    			if(flowCode){
	    				flowList.flowCode = flowCode;
	    				$(".flowQuery").empty();
	    				$(".ibox").show();
	    				$("[name='flowCode']").val(flowCode);
		    			$(".left-sidebar-item a").removeClass("activeItem");
		    			t.addClass("activeItem");
		    			flowList.initList();
	    			}
	    		});
				var _flowCode = flowList.flowCode;
				if(_flowCode&&_flowCode.indexOf('$')==-1){
					var el = $(".left-sidebar-item [data-code='"+flowList.flowCode+"']");
					if(el.length>0){
						el.click();
					}else{
						layer.alert('该流程无数据，缺省查所有。');						
					}
				}else{
					$('.leftShow').click();
				}
			}
			
			flowList.initList = function(){
				var param = {
						mars:'FlowDao.flowList',
						id:'_flowTable',
						page:true,
						toolbar:'#btnBar',
						rowDoubleEvent:'flowList.flowDetail',
						height:'full-120',
						limit:50,
						limits:[20,50,100,200,300,500],
						title:'流程数据',
						autoSort:false,
						sort:'flowList.sort',
						cellMinWidth:50,
						cols: [[
						 {
		            	 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
							field:'',
							title:'操作',
							width:50,
							event:'flowList.flowDetail',
							templet:function(row){
								return renderTpl('bar',row);
							}
						},{
							field:'APPLY_STATE',
							title:'流程状态',
							width:80,
							templet:function(row){
								var val = row['APPLY_STATE'];
								return flowApplyState(val);
							}
						},{
							 field:'APPLY_NO',
							 title:'编号',
							 width:180,
							 templet:function(row){
								 return row['APPLY_NO']+"_"+row['FLOW_NAME'];
							 }
						 },{
							 field:'APPLY_TITLE',
							 title:'流程名称',
							 minWidth:220
						 },{
							 field:'DEPT_NAME',
							 title:'发起部门',
							 sort:true,
							 width:100
						 },{
							 field:'APPLY_NAME',
							 title:'发起人',
							 sort:true,
							 width:80
						 },{
							 field:'APPLY_STAFF_NO',
							 title:'工号',
							 width:80
						 },{
							 field:'APPLY_REMARK',
							 title:'申请说明',
							 minWidth:100
						 },{
						    field: 'APPLY_TIME',
							title: '申请时间',
							 sort:true,
							align:'center',
						    width:140
						},{
							field:'NEXT_NODE_NAME',
							title:'当前节点',
							width:80,
							templet:function(row){
								if(row['NEXT_NODE_NAME']==row['NODE_NAME']){
									return '--';
								}
								return row['NEXT_NODE_NAME'];
							}
						},{
							field:'NEXT_CHECK_NAME',
							title:'当前审批人',
							width:80,
							templet:function(row){
								if(row['NEXT_CHECK_NAME']==row['CHECK_NAME']){
									return '--';
								}
								return row['NEXT_CHECK_NAME'];
							}
						},{
							field:'NODE_NAME',
							title:'上个节点',
							width:80
						},{
							field:'CHECK_NAME',
							title:'办理人',
							width:80,
							sort:true,
							templet:function(row){
								return row['CHECK_NAME'];
							}
						},{
						    field: 'CHECK_TIME',
							title: '办理时间',
							align:'center',
							sort:true,
						    width:140
						},{
							field:'CHECK_RESULT',
							title:'审批结果',
							width:80,
							sort:true,
							templet:function(row){
								var json  = {'0':'待审批','1':'审批通过','2':'审批拒绝'};
								var checkResult = row['CHECK_RESULT']||'';
								return json[checkResult]||'--';
							}
						},{
							field:'CHECK_DESC',
							title:'审批描述'
						}
					]],done:function(){
						
				  	}
				}
				FlowMgr.joinCols(param,5,function(data){
					$("#flowMgrForm").initTable(param);
					intiCondition(data);
				}); 
		 }
			
		flowList.flowDetail = function(data){
			var flowCode = data['FLOW_CODE'];
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,resultId:data['CURRENT_RESULT_ID']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		flowList.sort = function(params){
			var data = form.getJSONObject("#condtionForm");
			if($.isEmptyObject(data)){
				data = condtionData;
			}else{
				condtionData = data;
			}
			var d = $.extend({},data,params.data);
			params.data = d;
			$("#flowMgrForm").queryData(params);
		}
		
		flowList.queryData = function(params){
			if (params === undefined) params = {};
			var data = form.getJSONObject("#condtionForm");
			if($.isEmptyObject(data)){
				data = condtionData;
			}else{
				condtionData = data;
			}
			var d = $.extend({},data,params);
			$("#flowMgrForm").queryData({id:'_flowTable',data:d});
		}
		
		flowList.conditionReset = function(){
			$("#condtionForm")[0].reset();
			flowList.queryData();
		}
		
		flowList.reset = function(){
			condtionData = {};
			$("input[type='text'],select").val("");
			flowList.queryData();
		}
			
	   function leftShow(el){
		   var flag = $(el).data('flag');
		   if(flag=='1'){
			   $(el).prev().hide(0);
			   $(el).next().css('width','100%').css('margin-left','0px');
			   $(el).data('flag','0');
			   $(el).html('<i class="fa fa-chevron-right"></i>');
		   }else{
			   $(el).prev().show(300);
			   $(el).next().css('width','calc(100% - 160px)');
			   $(el).data('flag','1');
			   $(el).html('<i class="fa fa-chevron-left"></i>');
		   }
	   }
	   
	   function exportExcel(){
			$("#flowMgrForm .layui-icon-export").click();
			$("#flowMgrForm .layui-table-tool-panel li").last().click();
		}
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>