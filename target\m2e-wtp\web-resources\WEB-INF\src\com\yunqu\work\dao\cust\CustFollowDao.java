package com.yunqu.work.dao.cust;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "CustFollowDao")
public class CustFollowDao extends AppDaoContext{
	
	@WebControl(name = "personWeeklyStat",type = Types.LIST)
	public JSONObject personWeeklyStat() {
		EasySQL sql = this.getEasySQL("");
		sql.append("SELECT t4.USER_ID, t4.USERNAME, t4.DEPTS, COUNT(t1.follow_id) AS follow_count,count(distinct(t1.business_id)) sj_count, GROUP_CONCAT(t1.sj_name) sj_name, IFNULL(SUM(t1.follow_day), 0) AS total_follow_day FROM yq_main.easi_dept_user t3 JOIN yq_main.easi_dept t2 ON t3.DEPT_ID = t2.DEPT_ID JOIN yq_main.easi_user t4 ON t3.USER_ID = t4.USER_ID LEFT JOIN yq_crm_follow t1 ON t1.create_user_id = t3.USER_ID ");
		sql.append(param.getString("beginDate"),"AND t1.follow_begin >= ?");
		sql.append(param.getString("endDate"),"AND t1.follow_end <= ?");
		sql.append("WHERE t2.DEPT_PATH_NAME LIKE '%解决%' ");
		sql.appendLike(param.getString("deptName"),"and t2.dept_name like ?");
		sql.appendLike(param.getString("username"),"and t4.USERNAME like ?");
		sql.append("and t4.STATE = 0 GROUP BY t4.USER_ID, t4.USERNAME");
		sql.append("order by total_follow_day desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}

}
