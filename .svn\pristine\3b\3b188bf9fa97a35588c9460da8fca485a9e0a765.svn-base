<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购评审</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="reviewResultForm" autocomplete="off">
   		   		<input type="hidden" value="${param.reviewId}" name="reviewId"/>
   		   		<input type="hidden" value="${param.nodeId}" name="nodeId"/>
   		   		<input type="hidden" value="${param.nodeType}" name="nodeType"/>
   		   		<input type="hidden" value="${param.resultId}" name="resultId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px;" class="required">可行性</td>
	            			<td>
		                    	<label class="radio radio-success radio-inline">
			                    	<input type="radio" checked="checked" value="1" name="doResult"><span>可行</span>				                    	
		                    	</label>
		                    	<label class="radio radio-success radio-inline">
			                    	<input type="radio" value="2" name="doResult"> <span>不可行</span>
		                    	</label>
		                    </td>
			            </tr>
			            <tr>
			            	<td style="vertical-align: top;">评审意见</td>
			            	<td>
			            		<textarea style="height: 120px" name="comment" class="form-control"></textarea>
			            	</td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm"  onclick="ReviewResultEdit.ajaxSubmitForm()"> 提交 </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("ReviewResultEdit");

		$(function(){
			$("#reviewResultForm").render({success:function(){
				
			}});  
		});
		
		ReviewResultEdit.ajaxSubmitForm = function(){
			if(form.validate("#reviewResultForm")){
				ReviewResultEdit.insertData(); 
			};
		}
		
		ReviewResultEdit.insertData = function() {
			var data = form.getJSONObject("#reviewResultForm");
			ajax.remoteCall("${ctxPath}/servlet/contract/review?action=addResult",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>