<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>合同评审</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra baseInfo flow-table">
					  		<tr>
					  			<td style="width: 10%;">评审编号</td>
					  			<td style="width: 25%;"><input type="text" data-rules="required" readonly="readonly" class="form-control" name="apply.applyNo" data-mars="ReviewDao.getReviewNo"/></td>
					  			<td style="width: 10%;">经办人</td>
					  			<td style="width: 23%;"><input type="text" readonly="readonly" class="form-control" data-mars="CommonDao.userName"/></td>
					  			<td style="width: 10%;">提交时间</td>
					  			<td style="width: 22%;"><input type="text" readonly="readonly" class="form-control" data-mars="CommonDao.date02"/></td>
					  		</tr>
					  		<tr>
								<td class="required">合同名称</td>
					  			<td colspan="5">
					  				<input type="text" data-rules="required" class="form-control" name="business.CONTRACT_NAME"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">合同类型</td>
					  			<td>
					  				<select data-rules="required" class="form-control text-val" name="business.CONTRACT_TYPE">
					  					<option value="正式合同">正式合同</option>
					  					<option value="提前执行">提前执行</option>
					  				</select>
					  			</td>
					  			<td class="required">签约单位</td>
					  			<td>
					  				<input type="hidden" name="business.NODE_IDS" id="nodeIds">
					  				<select data-rules="required" class="form-control text-val" name="business.SIGN_ENT">
					  					<option value="yunqu">云趣</option>
					  					<option value="zhongrong">中融</option>
					  					<option value="pci">佳都</option>
					  				</select>
					  			</td>
					  		
					  			<td class="required">产品线</td>
					  			<td>
					  				<input class="form-control" readonly="readonly" onclick="singleDict(this,'Y001')" data-rules="required" name="business.PROD_LINE">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>售前经理</td>
					  			<td>
					  				<input name="extend.PRE_SALES_ID" type="hidden">
					  				<input name="business.PRE_SALES_BY" readonly="readonly" onclick="singleUser(this);" class="form-control">
					  			</td>
					  			<td class="required">销售经理</td>
					  			<td>
					  				<input type="hidden" name="business.SALES_BY"/>
					  				<input data-rules="required" readonly="readonly" onclick="singleUser(this);" onchange="getPreMgr(this)" name="business.SALES_BY_NAME"  class="form-control text-val">
					  			</td>
					  			<td class="required">上级主管</td>
					  			<td>
					  				<input type="hidden" class="form-control" name="business.UP_SALES_BY"/>
					  				<input data-rules="required" readonly="readonly" onclick="singleUser(this);" name="business.UP_SALES_NAME" onchange="getPreMgrName(this)" class="form-control text-val"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>合同金额</td>
					  			<td><input type="number" class="form-control" name="business.AMOUNT"/></td>
					  			<td>付款方式</td>
					  			<td><input type="text" class="form-control" name="business.PAY_TYPE"/></td>
					  			<td>维保时间</td>
					  			<td><input type="text" placeholder="维保合同必填" class="form-control" name="business.WB_TIME"/></td>
					  		</tr>
					  		<tr>
					  			<td class="required">签约部门</td>
					  			<td>
					  				<input type="hidden" name="business.SIGN_DEPT_ID"/>
					  				<input name="business.SIGN_DEPT_NAME" data-rules="required" readonly="readonly" onclick="singleDept(this);" class="form-control text-val">
					  			</td>
					  			<td class="required">所属客户</td>
					  			<td>
					  				<input type="hidden" class="form-control" name="business.CUST_ID"/>
					  				<input type="text" data-rules="required" class="form-control" name="business.CUST_NAME" id="custId" readonly="readonly"/>
					  			</td>
					  			<td>所属商机</td>
					  			<td>
					  				<input type="hidden" class="form-control" name="business.BUSINESS_NAME"/>
					  				<select data-mars="CustDao.custBusniessDict" name="business.BUSINESS_ID" class="form-control text-val">
					               		<option value="">请选择-</option>
					                </select>
					  			</td>
					  		</tr>
					  		<tr class="hidden">
					  			<td>抄送用户</td>
					  			<td colspan="5">
					  				<input name="business.READ_USER_IDS" type="hidden">
					  				<input onclick="multiUser(this)" readonly="readonly" name="business.READ_USER_NAMES"  class="form-control text-val">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="5">
					  				<textarea class="form-control" style="height: 80px;" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>评审部门</td>
					  			<td colspan="5">
					  				<div class="itemList" data-mars="ReviewDao.nodeConfList" data-template="item-template"></div>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td style="vertical-align: top;">合同方案附件</td>
					  			<td colspan="5"  class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  	</table>
				 </div>
			</div>
			
			<script id="item-template" type="text/x-jsrender">
				{{for data}}
					{{if P_NODE_ID==0}}
						 <label class="checkbox checkbox-inline checkbox-success ml-10 pnode" data-id="{{:NODE_ID}}" data-name="{{:NODE_NAME}}">
							 <input type="checkbox" value="{{:NODE_ID}}" name="nodeIds"> <span>{{:NODE_NAME}}</span>
						 </label>
					{{/if}}
				{{/for}}	
				{{for data}}
					{{if P_NODE_ID==0&&CHILD_COUNT>0}}
						<div class="child child_{{:NODE_ID}}" style="margin:10px;display:none;">
						 <div style="width:120px;display: inline-block;text-align: right;color:red;">{{:NODE_NAME}}：</div>
						 {{for #parent.parent.data}}
						  {{if #parent.parent.data.NODE_ID==P_NODE_ID}}
							  <label class="radio radio-inline radio-success">
								 <input type="radio" value="{{:NODE_ID}}" data-pid="{{:P_NODE_ID}}" name="nodeId_{{:P_NODE_ID}}"> <span>{{:NODE_NAME}}</span>
							  </label>
						   {{/if}}
						{{/for}}
					 </div>
					{{/if}}
				{{/for}}					         
			 </script>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		layui.config({
			  base: '${ctxPath}/static/js/'
		}).use('tableSelect');
	
		var Flow = {};
	
		$(function(){
			initSelectCust();
			FlowCore.initPage({hideUserTitle:true,title:'{{:CONTRACT_NAME}}',success:function(result){
				var nodeIds = $('#nodeIds').val();
				renderNodeId(nodeIds);
				
				$('.pnode').change(function(){
					var t = $(this);
					var nodeId = t.data('id');
					var el = t.find('input');
					if(el.prop("checked")){
						$('.child_'+nodeId).show();
					}else{
						$('.child_'+nodeId).hide();
					}
				});
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			var data = form.getJSONObject("#flowForm");
			var len = $('[name=nodeIds]:checked').length;
			if(len==0){
				layer.msg('请选择评审部门',{icon:7});			
				return;
			}
			var flag = initData(data);
			if(flag){
				layer.confirm('确认数据和评审部门是否无误？',{icon:3,offset:'20px'},function(index){
					layer.close(index);
					FlowCore.insertData({reqUrl:'/yq-work/servlet/contract/review?action=submitApply',data:data});
				});
			}
		}
		
		function renderNodeId(nodeIds){
			var array = nodeIds.split(',');
			for(var index in array){
				var nodeId = array[index];
				if(nodeId.length==3){
					$("[name='nodeIds'][value='"+nodeId+"']").attr('checked','true');
				}else{
					var el = $("input[value='"+nodeId+"']");
					el.attr('checked','true');
					var pid = el.data('pid');
					$("[name='nodeIds'][value='"+pid+"']").attr('checked','true');
					$('.child_'+pid).show();
				}
			}
		}
		
		function initData(data){
			var businessName = $("[name='business.BUSINESS_ID'] option:selected").text();
			data['business.BUSINESS_NAME'] = businessName||'';
			data['business.TEXT_JSON']= getTextVal();
			
			var nodeIds = data.nodeIds;
			var newNodeIds = [];
			for(var index in nodeIds){
				var nodeId = nodeIds[index];
				var nodeName = $("[data-id='"+nodeId+"']").data('name');
				var len = $('.child_'+nodeId).length;
				if(len>0){
					nodeId = $("[name='nodeId_"+nodeId+"']:checked").val();
					if(nodeId==undefined){
						layer.msg(nodeName+"请勾选");
						return false;
					}
				}
				newNodeIds.push(nodeId);
			}
			
			data['nodeIds'] = newNodeIds.join(',');
			data['business.NODE_IDS'] = data.nodeIds;
			return true;
		}
		
		Flow.updateData = function() {
			var businessId = FlowCore.businessId;
			var data = form.getJSONObject("#flowForm");
			var len = $('[name=nodeIds]:checked').length;
			if(len==0){
				layer.msg('请选择评审部门',{icon:7});			
				return;
			}
			var flag = initData(data);
			if(flag){
				var nodeIds = $('#nodeIds').val();
				var newNodeIds = data['nodeIds'];
				var applyState = $('#applyState').val();
				FlowCore.updateData({reqUrl:'/yq-work/servlet/contract/review?action=updateApply',data:data,success:function(){
					if(nodeIds!=newNodeIds&&(applyState=='10'||applyState=='20')){
						var removeNodeId = fewNode(nodeIds,newNodeIds);
						var addNodeId = newNode(nodeIds,newNodeIds);
						ajax.remoteCall("/yq-work/servlet/contract/review?action=updateNode",{businessId:businessId,removeNodeId:removeNodeId.join(),addNodeId:addNodeId.join()},function(result) {
							
						});
					}
				}});
			}
			
			
			//取两个相同
			function getArrEqual(arr1, arr2) {
			    let newArr = [];
			    for (let i = 0; i < arr2.length; i++) {
			      for (let j = 0; j < arr1.length; j++) {
			        if(arr1[j] === arr2[i]){
			          newArr.push(arr1[j]);
			        }
			    }
			   }
			   return newArr;
			}
			
			function fewNode(oldData,newData){
				oldData = oldData.split(','),newData = newData.split(',');
				var equalList = getArrEqual(oldData,newData);//取相同的
				return diffArray(oldData,equalList)
			}
			function newNode(oldData,newData){
				oldData = oldData.split(','),newData = newData.split(',');
				var equalList = getArrEqual(oldData,newData);//取相同的
				return diffArray(newData,equalList)
			}
			function diffArray(arr1,arr2){
				return arr1.concat(arr2).filter(function(v, i, arr) {
			      return arr.indexOf(v) === arr.lastIndexOf(v);
			    });
			}
		}
		
		Flow.submitCheck = function() {
			
		}
		
		function getTextVal(){
			var json = {};
			$(".baseInfo .text-val").each(function(){
				var s = $(this);
				var name = s.attr('name');
				var val = s.val();
				var field = name.split('.')[1];
				var tagName = s[0].tagName.toLowerCase();
				var tagNametype = s[0].type.toLowerCase();
		      	if(tagNametype=="checkbox"||tagNametype=="radio"){
	      			var tempVal = $('input:radio[name="'+name+'"]:checked').val();
		      		if(tempVal){
		      		  var text = s.next().text();
		      		  json[field]=text;
		      	    }
		      	}
		      	else if(tagName=="select"){
		      		var text = s.find("option:selected").text();
		      		if(val==''){
				      	json[field]='';
		      		}else{
			      		json[field]=text;
		      		}
		      	}
			});
			delete json[''];
			return JSON.stringify(json);
		}
		
		function initSelectCust(){
			layui.use(['form','tableSelect'], function(){
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#custId',
					searchKey: 'custName',
					checkedKey: 'CUST_ID',
					page:true,
					searchPlaceholder: '请输入客户名称',
					table: {
						mars: 'CustDao.custList',
						cols: [[
						        { type: 'radio' },
						        { field: 'CUST_NO',title: '编号'},
						        { field: 'CUST_NAME', title: '名称', width: 320 }
						        ]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.CUST_NAME)
							ids.push(item.CUST_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
						
						$("[name='business.BUSINESS_ID']").render({data:{custId:ids[0]}});
					},
					clear:function(elem){
						elem.prev().val("");
					}
				});
			});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>