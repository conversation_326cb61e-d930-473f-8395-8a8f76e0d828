<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="orderCheckTable"></table>
	<script type="text/javascript">
		$(function(){
			$("#OrderDetailForm").initTable({
				mars:'OrderDao.reviewResult',
				id:'orderCheckTable',
				page:false,
				cellMinWidth:80,
				cols: [[
				 {type:'numbers'},
				 {
					 field:'NODE_NAME',
					 title:'节点名称',
					 width:120
				 },
				 {
				    field: 'CHECK_NAME',
					title: '审批人',
					align:'center',
					width:90
				 },
	             {
					 field:'CHECK_RESULT',
					 title:'评审结果',
					 align:'center',
					 width:90,
					 templet:function(row){
						 var checkResult = row['CHECK_RESULT'];
						 if(checkResult=='1'){
							 return '<span class="label label-success">通过</span>';
						 }else if(checkResult=='2'){
							 return '<span class="label label-danger">拒绝</span>';
						 }else{
							 return '<span class="label label-warning">待审批</span>';
						 }
					 }
				 },{
				    field: 'GET_TIME',
					title: '接收时间',
					align:'center'
				},{
				    field: 'CHECK_TIME',
					title: '审批时间',
					align:'center'
				},{
					field:'CHECK_DESC',
					title:'评审描述',
					edit:'text'
				}
			]],done:function(data){
				$("#checkCount").text(data.total);
		  	}
		});
	});
	
</script>
