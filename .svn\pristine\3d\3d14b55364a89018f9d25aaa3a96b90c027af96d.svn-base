package com.yunqu.work.base;

import java.sql.SQLException;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;
public abstract class AppBaseServlet extends EasyBaseServlet { 
	private static final long serialVersionUID = 1L;
	
	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_NAME;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	protected boolean hasRole(String roleId) {
		return getUserPrincipal().isRole(roleId);
	}
	
	protected boolean hasRes(String resId) {
		return getUserPrincipal().isResource(resId);
	}
	
	@Override
	protected String getResId() {
		return null;
	}
	
	protected EasyQuery getMainQuery(){
		return EasyQuery.getQuery(getAppName(), Constants.MARS_DS_NAME);
	}
	
	protected EasyQuery getQuery(int convertField) {
		EasyQuery query =  super.getQuery();
		query.setConvertField(convertField);
		return query;
	}

	protected String getRequestUrl() {
		return getRequest().getRequestURI()+"?"+getRequest().getQueryString();
	}
	protected String getDeptId() {
		return getStaffInfo().getDeptId();
	}
	
	protected String getDeptName() {
		return getStaffInfo().getDeptName();
	}
	
	protected String getUserId() {
		return getUserPrincipal().getUserId();
	}
	
	protected String getUserName() {
		return getUserPrincipal().getUserName();
	}
	
	protected StaffModel getStaffInfo() {
		Object object =  getUserPrincipal().getAttribute("staffInfo");
		if(object==null) {
			StaffModel staffModel = StaffService.getService().getStaffInfo(getUserId());
			getUserPrincipal().setAttribute("staffInfo",staffModel);
			return staffModel;
		}
		String jsonStr = JSONObject.toJSONString(object);
		JSONObject jsonObject = JSONObject.parseObject(jsonStr);
		return JSONObject.toJavaObject(jsonObject, StaffModel.class);
	}
	protected String getBaseDir() {
		String basePath = AppContext.getContext(Constants.APP_NAME).getProperty("basePath", "/home/<USER>/");
		return basePath+"files/";
	}
	
	protected void delApplyData(String id) {
		try {
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id  = ?", id);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	protected void delApplyDatas(String id,Object... params) {
		try {
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id  = ?", id);
			if(params!=null) {
				for(Object obj:params) {
					this.getQuery().executeUpdate("delete from "+obj+" where business_id  = ?", id);
				}
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	protected EasyRecord getApplyRecord(String flowCode) {
		return getApplyRecord(null,flowCode);
	}
	
	protected EasyRecord getApplyRecord(String id,String flowCode) {
		EasyRecord model = new EasyRecord("yq_flow_apply","apply_id"); 
		model.set("apply_time", EasyDate.getCurrentDateString());
		model.set("apply_date", EasyCalendar.newInstance().getDateInt());
		model.set("apply_by", getUserId());
		model.set("apply_name", getUserName());
		model.set("dept_id", getDeptId());
		model.set("dept_name", getDeptName());
		model.set("flow_code", flowCode);
		if(id!=null) {
			model.setPrimaryValues(id);
		}
		return model;
	}
}
