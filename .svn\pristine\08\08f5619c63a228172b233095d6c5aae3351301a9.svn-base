package com.yunqu.work.dao.flow;

import java.sql.SQLException;
import java.util.HashSet;
import java.util.Set;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.StaffService;

@WebObject(name = "FlowDao")
public class FlowDao extends AppDaoContext{
	
	@WebControl(name = "flowNo",type=Types.TEXT)
	public JSONObject flowNo() {
		return getText(RandomKit.orderId());
	}
	
	@WebControl(name = "selectRelatedFlow",type = Types.PAGE)
	public JSONObject selectRelatedFlow() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_apply t1 where 1=1");
		sql.append(10,"and t1.apply_state >= ?");
		sql.append(getUserId(),"and t1.apply_by = ?");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		String flowCode = param.getString("flowCode");
		if(StringUtils.notBlank(flowCode)) {
			sql.appendIn(flowCode.split(","),"and t1.flow_code");
		}
		sql.append("order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "getApplyTitle",type = Types.TEXT)
	public JSONObject getApplyTitle() {
		EasySQL sql = getEasySQL("select GROUP_CONCAT(apply_title) from yq_flow_apply where 1=1");
		String id = param.getString("id");
		if(StringUtils.notBlank(id)) {
			sql.appendIn(id.split(","),"and apply_id");
		}else {
			return getText("");
		}
		try {
			String result = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			return getText(result);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getText("");
	}
	
	@WebControl(name = "applyInfo",type = Types.RECORD)
	public JSONObject applyInfo() {
		EasySQL sql = getEasySQL("select * from yq_flow_apply where 1=1");
		sql.append(param.getString("businessId"),"and apply_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "baseInfo",type = Types.RECORD)
	public JSONObject baseInfo() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			JSONObject object = getJsonResult(new JSONObject());
			object.put("applyUserInfo",StaffService.getService().getStaffInfo(getUserId()));
			return object;
		}
		EasySQL sql = new EasySQL("select t1.*,t2.result_id,t2.node_name,t2.read_time,t2.check_result,t2.check_name,t4.flow_name from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("INNER JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append(businessId,"and t1.apply_id = ?",false);
//		sql.append(param.getString("resultId"),"and t2.result_id = ?");
		EasyQuery query = getQuery();
		query.setConvertField(3);
		this.setQuery(query);
		
		JSONObject object =  queryForRecord(sql.getSQL(), sql.getParams());
		JSONObject data = object.getJSONObject("data");
		if(data!=null) {
			String applyBy = data.getString("applyBy");
			StaffModel applyUserInfo =  StaffService.getService().getStaffInfo(applyBy);
			object.put("applyUserInfo",applyUserInfo);
		}
		return object;
	}
	
	@WebControl(name = "businessInfo",type = Types.RECORD)
	public JSONObject businessInfo() {
		String flowCode = param.getString("flowCode");
		if(StringUtils.isBlank(flowCode)) {
			return getJsonResult(new JSONObject());
		}
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONObject());
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		EasySQL sql = getEasySQL("select * from "+flow.getTableName()+" where 1=1");
		if("yq_flow_apply".equalsIgnoreCase(flow.getTableName())) {
			sql.append(param.getString("businessId"),"and apply_id = ?",false);
		}else {
			sql.append(param.getString("businessId"),"and business_id = ?",false);
		}
		
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	private void setResultOrderBy(EasySQL sql,String defaultOrder) {
		String sortName =  param.getString("sortName");
		String sortType =  param.getString("sortType");
		String flowCode =  param.getString("flowCode");
		if("contract_review".equals(flowCode)) {
			sql.append("order by t1.data3,t1.check_time");
		}else if(StringUtils.notBlank(sortName)) {
			sql.append("order by t1.").append(sortName).append(sortType);
		}else {
			sql.append(defaultOrder);
		}
	}
	
	@WebControl(name = "approveResult",type=Types.TEMPLATE)
	public JSONObject approveResult() {
		String businessId = param.getString("businessId");
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_result t1 where 1=1");
		sql.append(businessId,"and t1.business_id = ?",false);
		this.setResultOrderBy(sql, "order by t1.get_time");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "ccList",type=Types.TEMPLATE)
	public JSONObject ccList() {
		String businessId = param.getString("businessId");
		EasySQL sql = getEasySQL("select t1.* from yq_flow_cc t1 where 1=1");
		sql.append(businessId,"and t1.business_id = ?",false);
		sql.append("order by t1.cc_time");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "historyFlowList",type = Types.TEMPLATE)
	public JSONObject historyFlowList() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t4.flow_name from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		sql.append(param.getString("businessId"),"and t1.apply_id <> ?");
		String applyBy = param.getString("applyBy"); 
		if(StringUtils.isBlank(applyBy)) {
			applyBy = getUserId();
		}else {
			sql.append(10,"and t1.apply_state >= ?");
		}
		sql.append(applyBy,"and t1.apply_by = ?",false);
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?",false);
		sql.append("order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "myCCFlowList",type = Types.TEMPLATE)
	public JSONObject myCCFlowList() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t4.flow_name,t3.cc_by_name,t3.cc_time,t3.cc_id,t3.read_time,t3.create_name,t3.cc_remark from yq_flow_apply t1");
		sql.append("INNER JOIN yq_flow_cc t3 on t3.business_id = t1.apply_id");
		sql.append("LEFT JOIN yq_flow_approve_result t2 on t2.result_id = t3.result_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		this.setFlowCondition(sql);
		sql.append(10,"and t1.apply_state >= ?");
		if(!isFlowMgr()) {
			Integer ccType = param.getIntValue("ccType");
			if(ccType==0) {
				sql.append(getUserId(),"and t3.cc_by = ?");
			}else {
				sql.append(getUserId(),"and t3.create_by = ?");
			}
		}
		sql.append("order by t3.cc_time desc");
	
		int homePage = param.getIntValue("homePage");
		if(homePage==1) {
			EasyQuery query = getQuery();
			query.setMaxRow(8);
			setQuery(query);
			return queryForList(sql.getSQL(), sql.getParams());
		}else {
			return queryForPageList(sql.getSQL(), sql.getParams());
		}
	}
	
	@WebControl(name = "myCCFlowTree",type = Types.TEMPLATE)
	public JSONObject myCCFlowTree() {
		EasySQL sql = new EasySQL("select t1.flow_code,t2.flow_name,t2.p_flow_code,t2.query_url,t3.flow_name p_flow_name,count(1) count from yq_flow_apply t1,yq_flow_category t2, yq_flow_category t3,yq_flow_cc t4");
		sql.append("where t1.flow_code = t2.flow_code and t2.p_flow_code = t3.flow_code");
		sql.append("and t4.business_id = t1.apply_id");
		this.setFlowCondition(sql);
		sql.append(10,"and t1.apply_state >= ?");
		if(!isFlowMgr()) {
			Integer ccType = param.getIntValue("ccType");
			if(ccType==0) {
				sql.append(getUserId(),"and t4.cc_by = ?");
			}else {
				sql.append(getUserId(),"and t4.create_by = ?");
			}
	    }
	   sql.append("GROUP BY t1.flow_code order by t2.flow_index");
	   JSONObject result  =  queryForList(sql.getSQL(), sql.getParams());
	   setTreeData(result);
	   return result;
	}

	
	@WebControl(name = "myTrustFlowList",type = Types.TEMPLATE)
	public JSONObject myTrustFlowList() {
		EasySQL sql = new EasySQL("select t1.*,t2.result_id,t2.node_name,t2.read_time,t2.check_result,t2.check_name,t4.flow_name,t3.create_by_name,t3.trust_time,t3.trust_id,t3.trust_user_name,t3.trust_reason from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on t2.business_id = t1.apply_id");
		sql.append("INNER JOIN yq_flow_trust t3 on t2.result_id = t3.result_id");
		//sql.append("INNER JOIN yq_flow_approve_result t5 on t5.result_id = t1.next_result_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		this.setFlowCondition(sql);
		sql.append(10,"and t1.apply_state >= ?");
		if(!isFlowMgr()) {
			Integer dataType = param.getIntValue("dataType");
			if(dataType==0) {
				sql.append(getUserId(),"and t3.trust_user_id = ?");
			}else {
				sql.append(getUserId(),"and t3.create_by = ?");
			}
		}
		sql.append("order by t3.trust_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/*
	 * 我的申请
	 */
	@WebControl(name = "myFlowApply",type = Types.TEMPLATE)
	public JSONObject myFlowApply() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t4.flow_name,t2.read_time,t2.result_id from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on");
		int approveAll = param.getIntValue("approveAll");
		if(approveAll==0) {
			sql.append("t2.result_id = t1.next_result_id");
		}else {
			sql.append("t2.business_id = t1.apply_id");
		}
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		
		int selectMyFlow = param.getIntValue("selectMyFlow");
		if(selectMyFlow==1) {
			sql.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >=?");
		}
		
		this.setFlowCondition(sql);
		
		sql.append(getUserId(),"and t1.apply_by = ?");
		
		this.setOrderBy(sql,"order by t1.apply_time desc,t2.get_time desc");
		
		int homePage = param.getIntValue("homePage");
		if(homePage==1) {
			EasyQuery query = getQuery();
			query.setMaxRow(8);
			setQuery(query);
			return queryForList(sql.getSQL(), sql.getParams());
		}else {
			return queryForPageList(sql.getSQL(), sql.getParams());
		}
	}
	
	@WebControl(name = "myFlowApplyTree",type = Types.TEMPLATE)
	public JSONObject myFlowApplyTree() {
		EasySQL sql = new EasySQL("select t1.flow_code,t2.flow_name,t2.p_flow_code,t2.query_url,t3.flow_name p_flow_name,count(1) count from yq_flow_apply t1,yq_flow_category t2, yq_flow_category t3 ");
		sql.append("where t1.flow_code = t2.flow_code and t2.p_flow_code = t3.flow_code");
		this.setTreeCondition(sql);
		sql.append(getUserId(),"and t1.apply_by = ?");
		sql.append("GROUP BY t1.flow_code order by t2.flow_index");
		JSONObject result = queryForList(sql.getSQL(), sql.getParams());
		this.setTreeData(result);
		return result;
	}
	
	
	private void setTreeData(JSONObject result) {
		JSONArray array = result.getJSONArray("data");
		Set<String> pFlowCodes = new HashSet<String>();
		JSONArray flowType = new JSONArray();
		for(int i =0 ;i<array.size();i++) {
			JSONObject row = array.getJSONObject(i);
			String pFlowCode = row.getString("P_FLOW_CODE");
			if(!pFlowCodes.contains(pFlowCode)) {
				flowType.add(row);
			}
			pFlowCodes.add(pFlowCode);
		}
		result.put("flowCategory", flowType);
	}
	
	
	@WebControl(name = "myOverTimeFlow",type = Types.TEMPLATE)
	public JSONObject myOverTimeFlow() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.over_time,t2.check_result,t2.check_name,t2.result_id,t2.read_time,t4.flow_name from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		sql.append(EasyDate.getCurrentDateString(),"and t2.over_time < ? and t2.over_time <> ''");
		sql.appendIn(new int[]{10,20},"and t1.apply_state");
		this.setFlowCondition(sql);
		sql.append(0,"and t2.check_result = ?");
		if(!isFlowMgr()) {
			sql.append(getUserId(),"and find_in_set(?,t2.check_user_id)");
		}
		sql.append("order by t2.get_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "myUrgeFlow",type = Types.TEMPLATE)
	public JSONObject myUrgeFlow() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.over_time,t2.check_result,t2.check_name,t2.result_id,t2.read_time,t3.msg_content,t3.create_by_name urge_by,t3.receiver_name,t3.create_time urge_time,t4.flow_name from yq_flow_apply t1");
		sql.append("INNER JOIN yq_flow_approve_result t2 on t2.business_id = t1.apply_id");
		sql.append("INNER JOIN yq_flow_urge t3 on t3.result_id = t2.result_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		sql.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		this.setFlowCondition(sql);
		if(!isFlowMgr()) {
			Integer dataType = param.getIntValue("dataType");
			if(dataType==0) {
				sql.append(getUserId(),"and find_in_set(?,t3.receiver_id)");
			}else {
				sql.append(getUserId(),"and t3.create_by = ?");
			}
		}
		sql.append("order by t3.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/*
	 * 我的申请数量
	 */
	@WebControl(name = "myFlowApplyCount",type = Types.OTHER)
	public JSONObject myFlowApplyCount() {
		EasySQL sql = new EasySQL("select count(1) from yq_flow_apply t1");
		sql.append("where 1=1");
//		sql.appendIn(new int[]{0,5,10,20,21},"and t1.apply_state");
		sql.append(getUserId(),"and t1.apply_by = ?");
		return getJsonResult(queryForString(sql.getSQL(), sql.getParams()));
	}
	
	/**
	 * 我的待办数量
	 * @return
	 */
	@WebControl(name = "myFlowTodoCount",type = Types.OTHER)
	public JSONObject myFlowTodoCount() {
		EasySQL sql = new EasySQL("select count(1) from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.business_id = t1.apply_id");
		sql.append("where 1=1");
		sql.appendIn(new int[]{10,20},"and t1.apply_state");
		sql.append(0,"and t2.check_result = ?");
		sql.append(getUserId(),"and find_in_set(?,t2.check_user_id)");
		return getJsonResult(queryForString(sql.getSQL(), sql.getParams()));
	}
	
	//我的已办数量
	@WebControl(name = "myFlowDoneCount",type = Types.OTHER)
	public JSONObject myFlowDoneCount() {
		EasySQL sql = new EasySQL("select count(1)");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.business_id = t1.apply_id");
		sql.append("where 1=1");
		sql.append("and t2.node_id<> '0'");
		sql.append(0,"and t2.check_result > ?");
		sql.append(getUserId(),"and find_in_set(?,t2.pre_check_user_id)");
//		sql.append(getUserId(),"and t2.check_user_id = ?");
		return getJsonResult(queryForString(sql.getSQL(), sql.getParams()));
	}
	
	@WebControl(name = "myFlowDoneTree",type = Types.TEMPLATE)
	public JSONObject myFlowDoneTree() {
		EasySQL sql = new EasySQL("select t1.flow_code,t2.flow_name,t2.p_flow_code,t2.query_url,t3.flow_name p_flow_name,count(1) count from yq_flow_apply t1,yq_flow_category t2, yq_flow_category t3,yq_flow_approve_result t4 ");
		sql.append("where t1.flow_code = t2.flow_code and t2.p_flow_code = t3.flow_code and t4.business_id = t1.apply_id");
		this.setTreeCondition(sql);
		sql.append("and t4.node_id<> '0'");
		sql.append(0,"and t4.check_result > ?");
		sql.append(getUserId(),"and find_in_set(?,t4.pre_check_user_id)");
		sql.append("GROUP BY t1.flow_code order by t2.flow_index");
		JSONObject result = queryForList(sql.getSQL(), sql.getParams());
		this.setTreeData(result);
		return result;
	}
	
	
	@WebControl(name = "myCCFlowCount",type = Types.OTHER)
	public JSONObject myCCFlowCount() {
		EasySQL sql = new EasySQL("select count(1) from yq_flow_apply t1 ");
		sql.append("INNER JOIN yq_flow_cc t2 on t2.business_id = t1.apply_id");
		sql.append("where 1=1");
		sql.append("and t2.read_time = ''");
		sql.append(10,"and t1.apply_state >= ?");
		sql.append(getUserId(),"and t2.cc_by = ?");
		return getJsonResult(queryForString(sql.getSQL(), sql.getParams()));
	}
	
	/**
	 * 我的待办
	 * @return
	 */
	@WebControl(name = "myFlowTodo",type = Types.TEMPLATE)
	public JSONObject myFlowTodo() {
		EasySQL sql = new EasySQL("select t1.*,t2.approve_type,t2.over_time,t2.node_name,t2.check_result,t2.check_name,t2.result_id,t2.read_time,t4.flow_name from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.business_id = t1.apply_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		sql.appendIn(new int[]{10,20},"and t1.apply_state");
		this.setFlowCondition(sql);
		sql.append(0,"and t2.check_result = ?");
		sql.append(getUserId(),"and find_in_set(?,t2.check_user_id)");
		this.setOrderBy(sql,"order by t2.get_time desc");
		int homePage = param.getIntValue("homePage");
		if(homePage==1) {
			EasyQuery query = getQuery();
			query.setMaxRow(8);
			setQuery(query);
			return queryForList(sql.getSQL(), sql.getParams());
		}else {
			return queryForPageList(sql.getSQL(), sql.getParams());
		}
	}
	
	@WebControl(name = "myFlowTodoTree",type = Types.TEMPLATE)
	public JSONObject myFlowTodoTree() {
		EasySQL sql = new EasySQL("select t1.flow_code,t2.flow_name,t2.p_flow_code,t2.query_url,t3.flow_name p_flow_name,count(1) count from yq_flow_apply t1,yq_flow_category t2, yq_flow_category t3,yq_flow_approve_result t4 ");
		sql.append("where t1.flow_code = t2.flow_code and t2.p_flow_code = t3.flow_code and t4.business_id = t1.apply_id");
		this.setTreeCondition(sql);
		sql.appendIn(new int[]{10,20},"and t1.apply_state");
		sql.append(0,"and t4.check_result = ?");
		sql.append(getUserId(),"and find_in_set(?,t4.check_user_id)");
		sql.append("GROUP BY t1.flow_code order by t2.flow_index");
		JSONObject result = queryForList(sql.getSQL(), sql.getParams());
		this.setTreeData(result);
		return result;
	}
	
	private void setTreeCondition(EasySQL sql) {
		String flowCode = param.getString("flowCode");
		if(StringUtils.isBlank(flowCode)) {
			flowCode = param.getString("treeFlowCode");
		}
		if(StringUtils.notBlank(flowCode)) {
			int index = flowCode.indexOf("$");
			if(index>-1) {
				if(index==0) {
					sql.appendLLike(flowCode.substring(1),"and t1.flow_code like ?");
				}else {
					sql.appendRLike(flowCode.substring(0,flowCode.length()-1),"and t1.flow_code like ?");
				}
			}else {
				sql.appendIn(flowCode.split(","),"and t1.flow_code");
			}
		}
	}
	
	
	/**
	 * 已办流程
	 * @return
	 */
	@WebControl(name = "myFlowDone",type = Types.TEMPLATE)
	public JSONObject myFlowDone() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.result_id,t2.check_result,t2.check_name,t2.check_time,t4.flow_name,");
		sql.append("( SELECT CONCAT(t3.node_name,',',t3.check_name)  FROM yq_flow_approve_result t3 where t3.result_id = t1.next_result_id ) current_node ");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.business_id = t1.apply_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		this.setFlowCondition(sql);
		sql.append("and t2.node_id<> '0'");
		sql.append(0,"and t2.check_result > ?");
		
		Integer dataType = param.getIntValue("dataType");
		if(dataType==0) {
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,t2.pre_check_user_id)");
			sql.append("or");
			sql.append(getUserId(),"t2.check_user_id = ?");
			sql.append(")");
		}else {
			sql.append(getUserId(),"and t2.check_user_id = ?");
		}
		
		this.setOrderBy(sql,"order by t2.check_time desc");
		int homePage = param.getIntValue("homePage");
		if(homePage==1) {
			EasyQuery query = getQuery();
			query.setMaxRow(8);
			setQuery(query);
			return queryForList(sql.getSQL(), sql.getParams());
		}else {
			return queryForPageList(sql.getSQL(), sql.getParams());
		}
	}
	
	/**
	 *    流程查询
	 * @return
	 */
	@WebControl(name = "flowList",type = Types.TEMPLATE)
	public JSONObject flowList() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t2.check_time,t2.check_desc,t3.node_name next_node_name,t3.check_name next_check_name,t4.flow_name");
		sql.append("from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on");
		int approveAll = param.getIntValue("approveAll");
		if(approveAll==0) {
			sql.append("t2.result_id = t1.current_result_id");
			sql.append("left join yq_flow_approve_result t3 on t3.result_id = t1.next_result_id");
		}else {
			sql.append("t2.business_id = t1.apply_id");
			sql.append("left join yq_flow_approve_result t3 on t3.result_id = t1.next_result_id");
		}
		sql.append("INNER JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		
		this.setFlowCondition(sql,true);
		
		String applyState = param.getString("applyState");
		if(StringUtils.notBlank(applyState)) {
			sql.appendIn(applyState.split(","),"and t1.apply_state");
		}else {
			sql.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		}
		
		sql.append(param.getString("checkResult"),"and t2.check_result = ?");
		if(!isFlowMgr()) {
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,t4.flow_mgr_ids)");
			sql.append("or");
			sql.append(getUserId(),"t1.apply_by = ?");
			sql.append(")");
		}
		this.setOrderBy(sql,"order by t1.apply_time desc,t1.apply_id,t2.get_time");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "flowListTree",type = Types.TEMPLATE)
	public JSONObject flowListTree() {
		EasySQL sql = new EasySQL("select t1.flow_code,t2.flow_name,t2.p_flow_code,t2.query_url,t3.flow_name p_flow_name,count(1) count from yq_flow_apply t1,yq_flow_category t2, yq_flow_category t3");
		sql.append("where t1.flow_code = t2.flow_code and t2.p_flow_code = t3.flow_code");
		this.setTreeCondition(sql);
		String applyState = param.getString("applyState");
		if(StringUtils.isBlank(applyState)) {
			sql.append(10,"and t1.apply_state >= ?");
		}else {
			sql.append(applyState,"and t1.apply_state = ?");
		}
		
		if(!isFlowMgr()) {
			sql.append("and (");
			sql.append(getUserId(),"find_in_set(?,t2.flow_mgr_ids)");
			sql.append("or");
			sql.append(getUserId(),"t1.apply_by = ?");
			sql.append(")");
		}
		sql.append("GROUP BY t1.flow_code order by t2.flow_index");
		JSONObject result = queryForList(sql.getSQL(), sql.getParams());
		this.setTreeData(result);
		return result;
	}
	
	
	@WebControl(name = "flowGoingList",type = Types.TEMPLATE)
	public JSONObject flowGoingList() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t2.check_time,t3.node_name has_node_name,t3.check_time has_check_time,t4.flow_name");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("INNER JOIN yq_flow_approve_result t3 on t3.result_id = t1.current_result_id");
		sql.append("INNER JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		sql.appendIn(new int[] {10,20},"and t1.apply_state");
		this.setFlowCondition(sql);
		sql.append(param.getString("checkResult"),"and t2.check_result = ?");
		if(!isFlowMgr()) {
			sql.append(getUserId(),"and t1.apply_by = ?");
		}
//		sql.append(param.getString("checkUserId"),"and t2.check_user_id = ?");
		this.setOrderBy(sql,"order by t1.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	private void setFlowCondition(EasySQL sql) {
		setFlowCondition(sql,false);
	}
	
	private void setFlowCondition(EasySQL sql,boolean removeState) {
		String flowCode = param.getString("flowCode");
		if(StringUtils.notBlank(flowCode)) {
			int index = flowCode.indexOf("$");
			if(index>-1) {
				if(index==0) {
					sql.appendLLike(flowCode.substring(1),"and t1.flow_code like ?");
				}else {
					sql.appendRLike(flowCode.substring(0,flowCode.length()-1),"and t1.flow_code like ?");
				}
			}else {
				sql.appendIn(flowCode.split(","),"and t1.flow_code");
			}
		}
		sql.appendLike(param.getString("applyDeptName"),"and t1.dept_name like ?");
		sql.appendLike(param.getString("applyStaffNo"),"and t1.apply_staff_no like ?");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		sql.appendRLike(param.getString("_nodeName"),"and t2.node_name like ?");
		
		if(!removeState) {
			String applyState = param.getString("applyState");
			if(StringUtils.notBlank(applyState)) {
				sql.appendIn(applyState.split(","),"and t1.apply_state");
			}
		}
		sql.append(param.getString("fkId"),"and t1.fk_id = ?");
		sql.append(param.getString("applyLevel"),"and t1.apply_level = ?");
		sql.append(param.getString("checkName"),"and t2.check_name = ?");
		sql.appendLike(param.getString("applyName"),"and t1.apply_name like ?");
		sql.appendLike(param.getString("deptName"),"and t1.dept_name like ?");
		sql.appendLike(param.getString("flowName"),"and t4.flow_name like ?");
		
		String applyBeginDate = param.getString("applyBeginDate");
		String applyEndDate = param.getString("applyEndDate");
		if(StringUtils.notBlank(applyBeginDate)) {
			sql.append(applyBeginDate.replaceAll("-",""),"and t1.apply_date >= ?");
		}
		if(StringUtils.notBlank(applyEndDate)) {
			sql.append(applyEndDate.replaceAll("-",""),"and t1.apply_date <= ?");
		}
		
		JSONObject condition = JsonKit.getJSONObject(request, "condition");
		if(condition!=null) {
			Set<String> keys = condition.keySet();
			for(String key:keys) {
				String value = condition.getString(key);
				if(StringUtils.isBlank(value)) {
					continue;
				}
				String[] array = key.split("_");
				int len = array.length;
				String field = array[0];
				String query = array[1];
				if(len==2) {
					if("like".equals(query)) {
						sql.appendLike(value,"and t1."+field+" like ?");
					}else if("gt".equals(query)){
						sql.append(value,"and t1."+field+" > ?");
					}else if("lt".equals(query)){
						sql.append(value,"and t1."+field+" < ?");
					}else if("ge".equals(query)){
						sql.append(value,"and t1."+field+" >= ?");
					}else if("le".equals(query)){
						sql.append(value,"and t1."+field+" <= ?");
					}else {
						sql.append(value,"and t1."+field+" = ?");
					}
				}else if(len==3){
					String type = array[2];
					if("begin".equals(type)) {
						sql.append(value,"and t1."+field+" >= ?");
					}else {
						sql.append(value,"and t1."+field+" <= ?");
					}
				}
			}
		}
	}
	

	private void setOrderBy(EasySQL sql,String defaultOrder) {
		String sortName =  param.getString("sortName");
		String sortType =  param.getString("sortType");
		String t2Fields = "CHECK_TIME,CHECK_RESULT,CHECK_NAME";
		if(StringUtils.notBlank(sortName)) {
			if(t2Fields.contains(sortName)) {
				sql.append("order by t2.").append(sortName).append(sortType);
			}else {
				sql.append("order by t1.").append(sortName).append(sortType);
			}
		}else {
			sql.append(defaultOrder);
		}
	}
	
	
	@WebControl(name="flowFileList",type=Types.LIST)
	public JSONObject flowFileList(){
		EasySQL sql=new EasySQL("select t1.*,t2.apply_name,t2.apply_id,t2.apply_no,t2.apply_title,t2.apply_state from yq_files t1,yq_flow_apply t2 where t1.fk_id = t2.apply_id");
		if(!hasRes("FLOW_AUTH_LOOK_FILE")&&!isFlowMgr()&&!isSuperUser()){
			sql.append(getUserId(),"and t1.creator = ?");
		}
		sql.append(10,"and t2.apply_state >= ?");
		sql.appendLike(param.getString("fileName"),"and t1.file_name like ?");
		sql.appendLike(param.getString("applyNo"),"and t2.apply_no like ?");
		sql.appendLike(param.getString("applyTitle"),"and t2.apply_title like ?");
		String source = param.getString("source");
		if(StringUtils.notBlank(source)) {
			sql.append("flow/"+source,"and t1.source = ?");
		}
		String sources = param.getString("sources");
		if(StringUtils.notBlank(sources)) {
			String[] array = sources.split(",");
			String[] newArray = new String[array.length];
			for(int i=0;i<array.length;i++) {
				newArray[i] = "flow/"+array[i];
			}
			sql.appendIn(newArray,"and t1.source");
		}
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
}
