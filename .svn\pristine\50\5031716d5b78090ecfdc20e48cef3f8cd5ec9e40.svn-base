<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">评审编号</td>
					  			<td>
					  				<input type="hidden" name="apply.relatedFlow"/>
					  				<input type="text" onclick="selectReview(this)" placeholder="点击此选择" readonly="readonly" class="form-control input-sm select-icon" name="apply.data1"/>
					  			</td>
					  			<td class="required">合同类型</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">合同名称</td>
					  			<td>
					  				<input type="text" readonly="readonly" placeholder="点击此选择" onclick="singleContract(this);" class="form-control input-sm select-icon" name="apply.data3"/>
					  			</td>
					  			<td class="required">合同号</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">签约单位</td>
					  			<td>
					  				<input type="text" readonly="readonly" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td class="required">签订日期</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" data-rules="required" class="form-control input-sm Wdate" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">销售经理</td>
					  			<td>
					  				<input type="text" readonly="readonly" onclick="singleUser(this)" class="form-control input-sm select-icon" name="apply.data7"/>
					  			</td>
					  			<td class="required">开发负责人</td>
					  			<td>
					  				<input type="text" onclick="multiUser(this)" data-rules="required" class="form-control input-sm select-icon" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">工程大区</td>
					  			<td>
					  				<input name="apply.data10" id="dn" type="hidden"/>
					  				<input type="text" onclick="singleDept(this);" data-dept-name="工程" data-ref-dept-leader-name="#dn" class="form-control input-sm select-icon" name="apply.data9"/>
					  			</td>
					  			<td class="required">项目经理</td>
					  			<td>
					  				<input type="text" data-rules="required" onclick="singleUser(this)" class="form-control input-sm select-icon" name="apply.data11"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td>计划开工时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data12"/>
					  			</td>
					  			<td>计划上线时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data13"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td>计划初验时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data14"/>
					  			</td>
					  			<td>计划终验时间</td>
					  			<td>
					  				<input type="text" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data15"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr class="fileListTr">
					  			<td>相关附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  				<small class="ml-10">项目启动会会议纪要附件,项目综合计划附件</small>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideUserTitle:true,title:'项目计划-{{:data3}}',success:function(data){
				
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			Flow.initData();
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		Flow.initData = function(){
			var pmLeader = $("[name='apply.data11']").val();
			var params = {selectUserName:pmLeader,ccNames:'许志远,张明东,李千帆'};
			FlowCore.approveData = params;
		}
		
		Flow.approveLayer = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='填写项目计划'){
				$('[name="ccIds"]').val('');
			}
			if(nodeCode=='发布计划'){
				$('[name="ccIds"]').val('');
			}
		}
		
		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='部门主管'){
				var selectUserName = $("[name='apply.data7']").val();
				var params = {selectUserName:selectUserName};
				FlowCore.approveData = params;
				return true;
			}
			if(nodeCode=='填写项目计划'){
				var data12 = $("[name='apply.data12']").val();
				if(data12==''){
					layer.msg('填写计划开工时间',{icon:7,offset:'20px',time:1200});
					return false;
				}
				var data13 = $("[name='apply.data13']").val();
				if(data13==''){
					layer.msg('填写计划上线时间',{icon:7,offset:'20px',time:1200});
					return false;
				}
				FlowCore.setting.uploadAuth = true;
				var selectUserName = $("[name='apply.data10']").val();
				var params = {selectUserName:selectUserName};
				FlowCore.approveData = params;
				return true;
			}
			if(nodeCode=='发布计划'){
				var array = [];
				var data7 = $("[name='apply.data7']").val();
				var data8 = $("[name='apply.data8']").val();
				var data10 = $("[name='apply.data10']").val();
				var data11 = $("[name='apply.data11']").val();
				if(data7){
					array.push(data7.split(','));
				}
				if(data8){
					array.push(data8.split(','));
				}
				if(data10){
					array.push(data10.split(','));
				}
				if(data11){
					array.push(data11.split(','));
				}
				var params = {ccNames:array.join(','),ccIds:''};
				FlowCore.approveData = params;
			}
			return true;
	    }
		
		FlowCore.flowEnd =  function(){
			
		}
		
		function selectReview(el){
			var id = new Date().getTime();
			$(el).attr('data-sid',id);
			popup.layerShow({id:'selectCust',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择评审',url:'/yq-work/pages/flow/select/select-review.jsp',data:{sid:id,type:'radio'}});
		}
		
		function selectReviewCallBack(row){
			$("[name='apply.data1']").val(row['APPLY_NO']);
			$("[name='apply.relatedFlow']").val(row['APPLY_ID']);
			
			$("[name='apply.data2']").val(row['CONTRACT_TYPE']);
			
			$("[name='apply.data5']").val(row['SIGN_ENT']);
			
			$("[name='apply.data7']").val(row['SALES_BY_NAME']);
			
		}
		
		function selctCallBack(id,row){
			$("[name='apply.data3']").val(row['CONTRACT_NAME']);
			$("[name='apply.data4']").val(row['CONTRACT_NO']);
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>