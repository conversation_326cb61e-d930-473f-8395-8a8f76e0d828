package com.yunqu.work.dao.ehr;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "OrgDao")
public class OrgDao extends AppDaoContext {

	@WebControl(name="userList",type=Types.LIST)
	public JSONObject userList(){
		EasySQL sql=getEasySQL("select * from easi_user where 1=1");
		sql.append("and state = 0");
		sql.appendLike(param.getString("userName"),"and USERNAME like ?");
		setQuery(getMainQuery());
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="getUpUser",type=Types.RECORD)
	public JSONObject getUpUser(){
		EasySQL sql=getEasySQL(" select USER_ID,USERNAME from easi_user t3,");
		sql.append(param.getString("userId"),"(select superior from yq_staff_info t1,easi_user t2 where t1.staff_user_id = t2.USER_ID and t2.USER_ID= ?) t4 ");
		sql.append("where t3.USER_ID = t4.superior");
		setQuery(getMainQuery());
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	
}
