package com.yunqu.work.dao.other;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.service.LookLogService;

@WebObject(name="ProductDao")
public class ProductDao extends AppDaoContext {
	
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select PRODUCT_ID,PRODUCT_NAME from YQ_PRODUCT where p_product_id<>'0' order by order_index");
	}
	@WebControl(name="info",type=Types.RECORD)
	public JSONObject info(){
		String  productId = param.getString("productId");
		if(StringUtils.notBlank(productId)){
			LookLogService.getService().addLog(getUserId(),productId);
		}
		EasySQL sql = getEasySQL("select * from yq_product where 1=1");
		sql.append(param.getString("product.PRODUCT_ID"),"and product_id  = ?");
		sql.append(productId,"and product_id  = ?");
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="productList",type=Types.TEMPLATE)
	public JSONObject productList(){
		EasySQL sql = getEasySQL("select product_id,p_product_id,product_name,order_index from yq_product where 1=1");
		sql.append("order by order_index asc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	

}
