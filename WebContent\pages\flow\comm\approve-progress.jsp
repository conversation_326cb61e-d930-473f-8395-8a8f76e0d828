<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<div class="container-fluid">
<form id="flowProgressForm" >
	 <input type="hidden" name="businessId" value="${param.businessId}"/>
	 <table class="layui-table" style="margin-top: 10px;padding: 10px;">
	   <thead>
	      <tr>
		      <th>序号</th>
		      <th>节点名称</th>
		      <th>经办人</th>
		      <th>审批结果</th>
		      <th class="hidden-print">接收时间</th>
		      <th class="hidden-print">查看时间</th>
		      <th>审批时间</th>
		      <th>审批描述</th>
		      <th class="hidden-print">用时</th>
	      </tr>
	  </thead>
	  <tbody data-mars="FlowDao.approveResult" data-template="approveResultList"></tbody>
	</table>
						
	<script id="approveResultList" type="text/x-jsrender">
  	 {{for data}}
  		<tr {{if CHECK_RESULT=='0'}} class="hidden-print" {{/if}}>
  			<td>{{:#index+1}}</td>
  			<td>{{:NODE_NAME}}</td>
  			<td>{{:CHECK_NAME}}  {{if TRUST_ID}} <a class="hidden-print pull-right" href="javascript:;" onclick="FlowCore.lookTrust('{{:TRUST_ID}}')">查看委托</a> {{/if}}</td>
  			<td>{{if NODE_ID!='0'}} {{call:CHECK_RESULT fn='checkResultLable'}} {{/if}} {{if CC_COUNT>0}} <a class="hidden-print pull-right" href="javascript:;" onclick="FlowCore.lookCC('{{:RESULT_ID}}')">查看抄送</a> {{/if}}</td>
  			<td class="hidden-print">{{:GET_TIME}}</td>
  			<td class="hidden-print">{{:READ_TIME}}</td>
  			<td>{{:CHECK_TIME}}</td>
  			<td {{if CHECK_DESC}}onmouseover="layer.tips('{{:CHECK_DESC}}',this)"{{/if}}>{{cutText:CHECK_DESC 40}}</td>
  			<td class="hidden-print">{{call:GET_TIME CHECK_TIME fn='timeDiff'}}</td>
  		</tr>
  	  {{/for}}
	</script>
</form>
</div>
<script type="text/javascript" src="/yq-work/static/js/flow.js"></script>
<script type="text/javascript">

	$(function(){
		$('#flowProgressForm').render();
	});

	
</script>