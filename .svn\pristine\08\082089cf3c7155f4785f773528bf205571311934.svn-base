<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="weeklyForm" data-page-hide="true">
				<input name="source" value="manger" type="hidden">
				<div class="layui-card">
			        <div class="layui-card-header">
			        	<div class="input-group input-group-sm" id="dataContainer" style="width: 310px;">
		          		    	<span class="input-group-addon">工作日期</span>	
		          		    	<input id="nowWeekly" type="hidden" data-mars-reload="false" data-mars="CommonDao.nowWeekly">
		          		    	<input id="prevWeekly" type="hidden" data-mars-reload="false" data-mars="CommonDao.prevWeekly">
	                     		<input data-mars="CommonDao.prevWeekly" data-mars-reload="false" style="border-right: 0;" type="text" data-laydate="{type:'date',range: '到'}" name="updateDate" class="form-control input-sm">
	                     		<div class="input-group-btn">
		                     		<select class="form-control input-sm" id="fastSelectDate" style="width:80px;" onchange="getDate(this,'updateDate')">
			                     		<option value="">全部</option>
			                     		<option value="nowWeekly">本周</option>
			                     		<option value="prevWeekly" selected="selected">上周</option>
			                     		<option value="currentMonth">本月</option>
			                     		<option value="threeDay">三天内</option>
			                     		<option value="oneWeek">一周内</option>
			                     		<option value="oneMonth">一个月内</option>
			                     		<option value="threeMonth">三个月内</option>
			                     		<option value="nowYear">今年</option>
			                     		<option value="oneYear">1年内</option>
			                     	</select>
	                     	   </div>
	                      </div>
						 <div class="input-group input-group-sm">
							    <span class="input-group-addon">项目</span>	
							    <input name="projectId" type="hidden">
							    <input type="text" style="width: 140px;" onclick="singleProject(this);" readonly="readonly" class="form-control input-sm mb-10" placeholder="点击选择"/>
						  </div>
						  <div class="input-group input-group-sm">
							    <span class="input-group-addon">姓名</span>	
						    	<input type="text" style="width: 80px" id="userName" name="userName" class="form-control input-sm mb-10" placeholder="姓名"/>
						</div>
						<div class="input-group input-group-sm">
							   <span class="input-group-addon">部门</span>	
							   <input type="text" style="width: 80px;" name="depts" class="form-control input-sm mb-10" placeholder="部门"/>
						</div>
					    <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					    <button type="reset" class="btn btn-sm btn-default"> 清空 </button>
			        </div>
			        <div class="layui-card-body">
						 <table class="layui-hide" id="weeklyProjectStat"></table> 
			        </div>
		      </div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script src="/yq-work/static/js/xlsx.core.min.js" type="text/javascript"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
	<script type="text/javascript">
		

		var list={
			weeklyProjectStat:function(){
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.weeklyProjectListStat',
					id:'weeklyProjectStat',
					height:'full-120',
					cellMinWidth:100,
					limits:[30,50,100,200],
					title:'项目工时汇总',
					toolbar:true,
					limit:30,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'PROJECT_NO',
						title: '项目编号',
						align:'left',
						width:110
					 },{
						 field:'PROJECT_NAME',
						 title:'项目名称',
						 minWidth:250,
						 event:'projectDetail'
					 },{
					    field: 'BEGIN_DATE',
						title: '开工日期',
						align:'left',
						width:90
					 },{
					    field: 'PROJECT_PO_NAME',
						title: '项目经理',
						align:'left',
						width:90
					 },{
					    field: 'PO_NAME',
						title: '开发负责人',
						align:'left',
						width:90
					 },{
						 
					    field: 'WORKHOUR_DAY',
						title: '投入总人天',
						align:'left',
						width:90
					 },{
						 
					    field: 'WORK_PEOPLE_COUNT',
						title: '总参与人',
						align:'left',
						width:90
					 },{
					    field: 'COUNT',
						title: '周报次数',
						align:'left',
						width:90
					 },{
					    field: 'USER_NAMES',
						title: '参与人',
						align:'left',
						width:160
					 },{
					    field: 'PEOPLE_COUNT',
						title: '参与人数',
						align:'left',
						width:90
					 },{
					    field: 'WORK_DAY',
						title: '消耗工时/天',
						align:'left',
						width:120
					 }
					]],done:function(result){
						
					}}
				);
			},
			query:function(){
				$("#weeklyForm").queryData({id:'weeklyProjectStat',jumpOne:true});
			},
			detail:function(data){
				var weeklyId = data;
				if(data.WEEKLY_ID){
					weeklyId=data.WEEKLY_ID;
				}
				list.look(weeklyId);
			}
		}
		
		
		function getThisUserData(name){
			$('#userName').val(name);
			list.query();
		}
		
		$(function(){
			$("#dataContainer").render({success:function(){
				var weekNo = new Date().getDay();
				if(weekNo==5||weekNo==6||weekNo==0){
					var nowWeekly =  $('#nowWeekly').val();
					$('#fastSelectDate').val('nowWeekly');
					$("[name='updateDate']").val(nowWeekly);
				}
				list.weeklyProjectStat();
			}});
		});
		
		var getDate = function(obj,inputName){
			var val = obj.value;
			var bdate = new Date();
			var edate = new Date();
			if('createTime'==inputName||'updateDate'==inputName){
				if(val == 'today'){
					
				}else if(val == 'yesterday'){
					bdate.setDate(bdate.getDate() - 1);
					edate = bdate;
				}else if(val == 'threeDay'){
					bdate.setDate(bdate.getDate() - 3);
				}else if(val == 'oneWeek'){
					bdate.setDate(bdate.getDate() - 7);
				}else if(val == 'oneMonth'){
					bdate.setMonth(bdate.getMonth() - 1);
				}else if(val=='nowYear'){
					 var currentYear = new Date().getFullYear();
				     bdate = new Date(currentYear, 0, 1);
				}else if(val == 'threeMonth'){
					bdate.setMonth(bdate.getMonth() - 3);
				}else if(val == 'oneYear'){
					bdate.setMonth(bdate.getMonth() - 12);
				}else if(val == 'currentMonth'){
					bdate.setDate(1);
					edate.setMonth(bdate.getMonth() + 1);
					edate.setDate(0);
				}
				
				if(val == 'nowWeekly'){
					$('[name="'+inputName+'"]').val($('#nowWeekly').val());
				}else if(val == 'prevWeekly'){
					$('[name="'+inputName+'"]').val($('#prevWeekly').val());
				}else{
					if(val){
						var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
						$('[name="'+inputName+'"]').val(v);
					}else{
						$('[name="'+inputName+'"]').val('');
					}
				}
			}else{
				if(val){
					bdate.setDate(bdate.getDate() - val);
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}
			list.query();
		}
		
		
		function projectDetail(data){
			var projectId = data['PROJECT_ID'];
			projectBoard(projectId);
		}
		
		function contentFn(val){
			if(val){
				return getContent(val);
			}else{
				return "暂无";
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>