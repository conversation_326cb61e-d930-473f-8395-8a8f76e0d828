package com.yunqu.work.dao.cust;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="ContactsDao")
public class ContactsDao extends AppDaoContext{

	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String contactsId = param.getString("contactsId");
		return queryForRecord("select * from yq_crm_contacts where contacts_id = ?",contactsId);
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from yq_crm_contacts where 1=1");
		sql.append(param.getString("custId"), "and cust_id = ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="custContacts",type=Types.DICT)
	public JSONObject custContacts(){
		EasySQL sql=getEasySQL("select name,name from yq_crm_contacts where 1=1");
		sql.append(param.getString("custId"), "and cust_id = ?",false);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
}
