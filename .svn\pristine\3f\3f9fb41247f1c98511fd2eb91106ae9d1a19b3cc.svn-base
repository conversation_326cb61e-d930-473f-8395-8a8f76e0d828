<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>周报</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="WeeklyDao.record" autocomplete="off" data-mars-prefix="weekly.">
  		   		<input type="hidden"  name="weekly.WEEKLY_TYPE" value="0"/>
  		   		<input type="hidden"  name="weekly.STATUS"/>
  		   		<input type="hidden" id="workTimeLevel" name="weekly.WORK_TIME_LEVEL"/>
  		   		<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
  		   		<input type="hidden" id="weeklyId" value="${param.weeklyId}" name="weekly.WEEKLY_ID"/>
  		   		<input type="hidden" value="${param.weeklyId}" name="fkId"/>
  		   		<input type="hidden" value="${param.YEAR}" name="weekly.YEAR"/>
  		   		<input type="hidden" value="${param.WEEK_NO}" name="weekly.WEEK_NO"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
						<tr style="display: none;">
							<td class="required">周报模板</td>
							<td>
								<select name="weekly.TPL_ID" class="form-control input-sm" onchange="loadWeeklyData()">
									<option value="default_tpl">默认模板</option>
								</select>
							</td>
						</tr>
						<tr>
							<td class="required"  style="width:120px;">标题</td>
							<td>
								<input name="weekly.TITLE" value="${param.TITLE }" id="title" type="text" class="form-control input-sm"/>
							</td>
						</tr>
						<tr>
		                    <td>主送</td>
		                    <td>
		                    	<select id="users" data-mars="CommonDao.userDict" name="weekly.RECEIVER" class="form-control input-sm">
		                    		<option value="">暂不发送</option>
		                    	</select>
		                    </td>
		                </tr>
		                 <tr>
		                    <td>抄送</td>
		                    <td>
		                    	<select  multiple="multiple" data-mars="CommonDao.userDict" id="mailtos" name="mailtos" class="form-control input-sm">
		                    	</select>
		                    </td>
	              		</tr>
			        </tbody>
				</table>
				<table class="table table-edit table-vzebra tplContent">
			        <tbody>
			        	<tr>
			           	 <td class="required" style="vertical-align: top;">
			             	  工作饱和度
			           	 </td>
			             <td>
			             	<input id="workDesc" placeholder="右侧按钮选择" style="width: 120px;display: inline-block;" type="text" name="weekly.ITEM_5" class="form-control input-sm" readonly="readonly" value=""/>
			             	<div class="btn-group btn-group-sm">
							  <button onclick="$('#workDesc').val('很不饱和');$('#workTimeLevel').val(10);" type="button" class="btn btn-default btn-xs">很不饱和(60%↓)</button>
							  <button onclick="$('#workDesc').val('不饱和');$('#workTimeLevel').val(20);" type="button" class="btn btn-default btn-xs">不饱和(60~70%)</button>
							  <button onclick="$('#workDesc').val('低饱和');$('#workTimeLevel').val(30);" type="button" class="btn btn-default btn-xs">低饱和(70~85%)</button>
							  <button onclick="$('#workDesc').val('饱和');$('#workTimeLevel').val(40);" type="button" class="btn btn-default btn-xs">饱和(85~100%)</button>
							  <button onclick="$('#workDesc').val('超饱和');$('#workTimeLevel').val(50);" type="button" class="btn btn-default btn-xs">超饱和(100%↑)</button>
							</div>
							<p style="color: #6c8b0d;">
								工作量百分比法：工作量饱满度=岗位有效工作时间/正常工作时间。
							</p>
			             </td>
			           </tr>
			           <tr>
			           	 <td style="width:120px;vertical-align: top;" class="required">
			           	  	 本周工作总结
			           	 </td>
			             <td>
			             	<div id="editor"></div>
			             	<textarea id="wordText" name="weekly.ITEM_1" maxlength="5000" style="height:150px;display: none;" class="form-control input-sm"></textarea>
			             </td>
			           </tr>
			           <tr>
			           	 <td class="required" style="vertical-align: top;">
			           		 下周工作计划
			           	 </td>
			             <td>
			             	<div id="editor2"></div>
			             	<textarea id="wordText2" name="weekly.ITEM_2" style="height:80px;display: none;" class="form-control input-sm"></textarea>
			             </td>
			           </tr>
		              <tr>
		                <td>附件</td>
		               	<td>
		               		<div data-template="template-files" data-mars="FileDao.fileList"></div>
		               		<div id="fileList"></div>
							<button class="btn btn-sm btn-default mt-5" type="button" onclick="$('#localfile').click()">+添加</button>
		               	</td>
			           </tr>
			           <tr>
			           	 <td style="vertical-align: top;">
			             	   问题/帮助/建议
			           	 </td>
			             <td>
			             	<textarea name="weekly.ITEM_3" style="height:80px" class="form-control input-sm"></textarea>
			             </td>
			           </tr>
			           <tr style="display: none;">
			           	 <td style="vertical-align: top;">
			             	   备注
			           	 </td>
			             <td>
			             	<textarea name="weekly.ITEM_4" style="height:80px" class="form-control input-sm"></textarea>
			             </td>
			           </tr>
			        </tbody>
				 </table>
				 <div class="layer-foot text-c" style="z-index: 99999999999;">
				    	  <button type="button" class="btn btn-primary btn-sm ml-15"  onclick="Edit.ajaxSubmitForm()"> 提 交 </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
  		<script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Edit.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.customConfig.menus = [
	 		    'head',  // 标题
	 		    'bold',  // 粗体
	 		    'fontSize',  // 字号
	 		    'fontName',  // 字体
	 		    'foreColor',  // 文字颜色
	 		    'link',  // 插入链接
	 		    'list',  // 列表
	 		    'justify',  // 对齐方式
	 		    'quote',  // 引用
	 		    'code',  // 插入代码
	 		    'emoticon',  // 表情
	 		    'image',  // 插入图片
	 		    'table',  // 表格'
	 	];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.customConfig.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.create();
		
		var editor2 = new E('#editor2');
		editor2.customConfig.menus = [
	 		    'head',  // 标题
	 		    'bold',  // 粗体
	 		    'fontSize',  // 字号
	 		    'fontName',  // 字体
	 		    'foreColor',  // 文字颜色
	 		    'link',  // 插入链接
	 		    'list',  // 列表
	 		    'justify',  // 对齐方式
	 		    'quote',  // 引用
	 		    'code',  // 插入代码
	 		    'emoticon',  // 表情
	 		    'image',  // 插入图片
	 		    'table',  // 表格'
	 	];
		weUpload(editor2,{uploadImgMaxLength:3});
		editor2.customConfig.onchange = function (html) {
		     $("#wordText2").val(html)
		}
		editor2.create();
	
		jQuery.namespace("Edit");
		Edit.weeklyId='${param.weeklyId}';
		function autoHeight(){
			$("textarea").keydown(function(event) {
				  if(event.keyCode == "13") {
					 var height = $(this).height();
					 $(this).css('height',(height+20)+'px');
				  }
				   if(event.keyCode == 8) {
					 var height = $(this).height();
					 height=height>80?height:80;
					 $(this).css('height',(height-20)+'px');
				   }
			});
			$(".w-e-text-container").keydown(function(event) {
				  if(event.keyCode == "13") {
					 var height = $(this).height();
					 $(this).css('height',(height+20)+'px');
				  }
				   if(event.keyCode == 8) {
					 var height = $(this).height();
					 height=height>120?height:120;
					 $(this).css('height',(height-20)+'px');
				   }
			});
		}
		function loadWeeklyData(){
			$("#editForm").render({success:function(result){
				 editor.txt.html($("#wordText").val());
				 editor2.txt.html($("#wordText2").val());
				 
				 
				var record=result['WeeklyDao.record'];
				var data=record.data;
				
				if(data&&data.STATUS==2){
					$("#users").prop("disabled", true);
				}
				var mailtos=record.mailtos;
				if(mailtos){
					$("[name='mailtos']").val(mailtos);
				}
				
				fillRecord(data,'weekly.','',"#editForm");
				$(".w-e-text-container").first().css("height","120px");
				$(".w-e-text-container").last().css("height","120px");
				if(Edit.weeklyId==''){
					var weeklyReceiver = localStorage.getItem("weeklyReceiver")||'';
					var weeklyMailtos = localStorage.getItem("weeklyMailtos")||'';
					$("#users").val(weeklyReceiver);
					$("[name='mailtos']").val(weeklyMailtos.split(","));
				}
				requreLib.setplugs('select2',function(){
					$("#users").select2({theme: "bootstrap",placeholder:'请选择主送'});
					$("#mailtos").select2({theme: "bootstrap",placeholder:'请选择抄送'});
					$(".select2-container").css("width","100%");
				});
				autoHeight();
			}});  
		}
		$(function(){
			loadWeeklyData();
		});
		Edit.uploadFile = function(callback){
			var fkId='';
			if(Edit.weeklyId){
				fkId=Edit.weeklyId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'callback',fileMaxSize:(1024*50),fkId:fkId,source:'weekly'});
		}
		var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
		}
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				 var  users = $("#users").val();
				 if(users==''){
					 layer.msg("请选择主送。",{icon:7});
					 return;
				 }
				 var text=$.trim(editor.txt.text()||'');
				 var text2=$.trim(editor2.txt.text()||'');
				 var fileDiv =$(".file-div").length;
				 if(text.length==0){
					 layer.msg("本周工作总结不能为空。",{icon:7});
					 return;
				 }
				 if(text2.length==0){
					 layer.msg("下周工作计划不能为空。",{icon:7});
					 return;
				 }
			     if(text.length < 50&&fileDiv==0){
			    	 layer.confirm("当前本周工作总结描述("+text.length+")字数,<br>内容太短(低于50字),是否继续提交?",{offset:'auto',icon:7},function(index){
			    		 layer.close(index);
						if(Edit.weeklyId){
							Edit.updateData(); 
						}else{
							Edit.insertData(); 
						}
			    		 
			    	 });
			     }else{
					if(Edit.weeklyId){
						Edit.updateData(); 
					}else{
						Edit.insertData(); 
					}
				  }
			     
			};
		}
		Edit.insertData = function() {
			$("#weeklyId").val($("#randomId").val());
			var ids = $("[name='mailtos']").val()||[];
			ids.push(getCurrentUserId());
			$("[name='mailtos']").val(ids);
			
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/weekly?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						localStorage.setItem("weeklyReceiver",$("#users").val());
						localStorage.setItem("weeklyMailtos",ids.join());
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/weekly?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		};
		Edit.del=function(data){
			layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/weekly?action=delete",{'weekly.WEEKLY_ID':Edit.weeklyId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							list.query();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>