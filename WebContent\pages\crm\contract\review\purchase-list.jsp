<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
</EasyTag:override>
<EasyTag:override name="content">
    <form id="purchaseEditForm" autocomplete="off">
    		<table class="layui-table approve-table">
		  		 <thead>
		  		 	<tr>
		  		 		<th style="width: 100px;">采购类型</th>
		  		 		<th style="width: 100px;">登记类型</th>
		  		 		<th>品名</th>
		  		 		<th>规格</th>
		  		 		<th>数量</th>
		  		 		<th>供应商</th>
		  		 		<th>单价</th>
		  		 		<th>数量</th>
		  		 		<th>总价</th>
		  		 		<th style="width: 100px;">是否含税</th>
		  		 		<th>操作</th>
		  		 	</tr>
		  		 </thead>
		  		 <tbody id="purchaseTbody">
		  		 	
		  		 </tbody>
		  		 <tbody class="hidden">
		  		 	<tr id="templateTr">
		  		 		<td>
		  		 			<select data-text="false" class="form-control" name="f1">
		  		 				<option value="外购">外购</option>
		  		 				<option value="外包">外包</option>
		  		 			</select>
		  		 		</td>
		  		 		<td>
							<select data-text="false" class="form-control" name="f10">
								<option value="新增">新增</option>
								<option value="变更">变更</option>
								<option value="取消">取消</option>
							</select>
						</td>
		  		 		<td><input type="text" data-text="false" data-rules="required" class="form-control" name="f2"></td>
		  		 		<td><input type="text" data-text="false" class="form-control" name="f3"></td>
		  		 		<td><input type="text" data-text="false" class="form-control" name="f4"></td>
		  		 		<td><input type="text" data-text="false" class="form-control" name="f5"></td>
		  		 		<td><input type="text" data-text="false" class="form-control" name="f6"></td>
		  		 		<td><input type="text" data-text="false" class="form-control" name="f7"></td>
		  		 		<td><input type="text" data-text="false" class="form-control" name="f8"></td>
		  		 		<td>
							<select data-text="false" class="form-control" name="f9">
								<option value="含税">含税</option>
								<option value="不含税">不含税</option>
							</select>
						</td>
		  		 		<td><button class="btn btn-xs btn-default" type="button" onclick="$(this).parent().parent().remove();">删除</button></td>
		  		 	</tr>
		  		 </tbody>
		 </table>
		 <p> <button class="pull-right btn btn-sm btn-info mr-10" type="button" onclick="addTr()">新增一行</button> </p>
		 <div class="layer-foot text-c">
	    	  <button type="button" class="btn btn-primary btn-sm" onclick="PurchaseEdit.ajaxSubmitForm()"> 确 定  </button>
		      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
		</div>			
 	</form>
	  		
	<script id="purchase-template" type="text/x-jsrender">
		{{for data}}
			<tr>
				<td>
					<select data-text="false" class="form-control" data-value="{{:f1}}" name="f1">
						<option value="外购">外购</option>
						<option value="外包">外包</option>
					</select>
				</td>
				<td>
					<select data-text="false" class="form-control" data-value="{{:f10}}" name="f10">
						<option value="新增">新增</option>
						<option value="变更">变更</option>
						<option value="取消">取消</option>
					</select>
				</td>
				<td><input type="text" data-text="false"  data-rules="required" class="form-control" value="{{:f2}}" name="f2"></td>
				<td><input type="text" data-text="false" class="form-control" value="{{:f3}}" name="f3"></td>
				<td><input type="text" data-text="false" class="form-control" value="{{:f4}}" name="f4"></td>
				<td><input type="text" data-text="false" class="form-control" value="{{:f5}}" name="f5"></td>
				<td><input type="text" data-text="false" class="form-control" value="{{:f6}}" name="f6"></td>
				<td><input type="text" data-text="false" class="form-control" value="{{:f7}}" name="f7"></td>
				<td><input type="text" data-text="false" class="form-control" value="{{:f8}}" name="f8"></td>
				<td>
					<select data-text="false" class="form-control" data-value="{{:f9}}" name="f9">
						<option value="含税">含税</option>
						<option value="不含税">不含税</option>
					</select>
				</td>
				<td></td>
			</tr>		
		{{/for}}
	</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		var PurchaseEdit = {
			ajaxSubmitForm:function(){
				if(form.validate("#purchaseTbody")){
					var array = [];
					$('#purchaseTbody tr').each(function(){
						var data = form.getJSONObject($(this));
						array.push(data);
					});
					var postData = $.extend({fields:{'business.BUY_LIST':JSON.stringify(array)}},FlowCore.params);
					ajax.remoteCall("/yq-work/servlet/flow/fun?action=updateField",postData,function(result) { 
						if(result.state == 1){
							ajax.remoteCall("/yq-work/servlet/contract/review?action=updatePurchase",{array:array,businessId:postData.businessId},function(result) { 
								layer.msg(result.msg,{icon:1,time:800},function(){
									var tmp = $.templates('#review-purchase-template');
									var html = tmp.render({data:array});
									$('#reviewPurchase').html(html);
									$('.buy-list').show();
									
									layer.closeAll();
								});
							});
							
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}
			}
		};
		
		$(function(){
			ajax.daoCall({loading:false,controls:['FlowDao.businessInfo'],params:FlowCore.params},function(rs){
				var obj = rs['FlowDao.businessInfo'].data;
				var buyList = obj['BUY_LIST']
				var data = eval('(' + buyList + ')'); 
				var tmp = $.templates('#purchase-template');
				var html = tmp.render({data:data});
				$('#purchaseTbody').html(html);
				
				$("#purchaseEditForm [data-value]").each(function(){
					 $(this).val($(this).data('value'));
				});
			});
		});
		
		function addTr(){
			var newTr = $('#templateTr').clone();
			newTr.find('input').val('');
			$('#purchaseTbody').append(newTr);
		}
		
	</script>
	
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>