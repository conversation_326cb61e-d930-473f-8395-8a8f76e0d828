package com.yunqu.work.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

import org.easitline.common.core.log.impl.CoreLogger;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

public class DateUtils {

	public static int getPlanMaxDay(int days){
		 Calendar cal = Calendar.getInstance();
		 cal.setTime(new Date());
		 cal.add(Calendar.DATE, days);
         SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");  
         return Integer.valueOf(yyyyMMdd.format(cal.getTime()));
	}
	public static String getTodayHm(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		SimpleDateFormat hhmm = new SimpleDateFormat("HH:mm");  
		return hhmm.format(cal.getTime());
	}
	public static int getPlanToday(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");  
		return Integer.valueOf(yyyyMMdd.format(cal.getTime()));
	}
	public static int getHour(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		SimpleDateFormat hh = new SimpleDateFormat("HH");  
		return Integer.valueOf(hh.format(cal.getTime()));
	}
	public static void main(String[] args) {
//		System.out.println(hourAfter(23));
//		System.out.println(getPrevMonth());
//		System.out.println(getFormatDate("2024-01-26"));

//		System.out.println(getPrevMonth());
		System.out.println(DateUtils.hourAfter(-24*60));

	}
	public static int getTodayYm(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMM");  
		return Integer.valueOf(yyyyMMdd.format(cal.getTime()));
	}
	public static String getDate(String date,String patten){
		SimpleDateFormat sdf = new SimpleDateFormat(patten);  
		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");  
		try {
			Date d=sdf2.parse(date);
			Calendar cal = Calendar.getInstance();
			cal.setTime(d);
			return sdf.format(cal.getTime());
		} catch (ParseException e) {
			LogUtils.getLogger().error(e.getMessage(),e);
		}
		return null;
	}
	
	public static String getDate(String date,String beforePatten,String patten){
		SimpleDateFormat sdf = new SimpleDateFormat(patten);  
		SimpleDateFormat sdf2 = new SimpleDateFormat(beforePatten);  
		try {
			Date d=sdf2.parse(date);
			Calendar cal = Calendar.getInstance();
			cal.setTime(d);
			return sdf.format(cal.getTime());
		} catch (ParseException e) {
			LogUtils.getLogger().error(e.getMessage(),e);
		}
		return null;
	}
	
	 public static String convertErrorDate(String inputDateString) {
		if(StringUtils.isBlank(inputDateString)) {
	 		return EasyDate.getCurrentDateString("yyyy年MM月dd日");
	 	}
		if(inputDateString.startsWith("年")) {
			inputDateString = EasyCalendar.newInstance().getYear()+inputDateString;
			return inputDateString;
		}
		return inputDateString;
	 }
	
	 public static String convertDate(String inputDateString) {
		 	if(StringUtils.notBlank(inputDateString)&&inputDateString.startsWith("年")) {
		 		inputDateString = EasyCalendar.newInstance().getYear()+inputDateString;
		 	}
	        // 定义输入日期字符串的格式
	        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
	        try {
	            // 将输入字符串解析为LocalDate对象
	            LocalDate date = LocalDate.parse(inputDateString, inputFormatter);
	            // 定义目标日期字符串的格式
	            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
	            // 将LocalDate对象格式化为目标日期字符串
	            String outputDateString = date.format(outputFormatter);
	            return outputDateString;
	        } catch (Exception e) {
	        	LogUtils.getLogger().error(e.getMessage(),e);
	            // 处理解析失败等异常情况
	            e.printStackTrace();
	            return EasyDate.getCurrentDateString("yyyyMMdd");
	        }
	   }
	

	public static String getPrevMonth() {
		SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
		Date date = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH,-1);
		date = calendar.getTime();
		return format.format(date);
	}
	
	public static String getBeforeDate(int day){
		if(day==0){
			return null;
		}
		return getBeforeDate(day,"yyyy-MM-dd HH:mm");
	}
	
	public static String nowAddOneSecond() {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = new Date();
		date.setTime(date.getTime() + 1000);
		return df.format(date);
	}
	
	public static String getDate(int second) {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = new Date();
		date.setTime(date.getTime() + 1000*60*second);
		return df.format(date);
	}
	
	public static Long betweenDays(String a, String b) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");// 自定义时间格式

        Calendar calendar_a = Calendar.getInstance();// 获取日历对象
        Calendar calendar_b = Calendar.getInstance();

        Date date_a = null;
        Date date_b = null;

        try {
            date_a = simpleDateFormat.parse(a);//字符串转Date
            date_b = simpleDateFormat.parse(b);
            calendar_a.setTime(date_a);// 设置日历
            calendar_b.setTime(date_b);
        } catch (ParseException e) {
            e.printStackTrace();//格式化异常
        }

        long time_a = calendar_a.getTimeInMillis();
        long time_b = calendar_b.getTimeInMillis();

        long between_days = (time_b - time_a) / (1000 * 3600 * 24);//计算相差天数

        return between_days;
    }
	
	
	
	public static String getBeforeDate(int day,String patten){
	    SimpleDateFormat sdf = new SimpleDateFormat(patten);                
	    Calendar c = Calendar.getInstance();           
	    c.add(Calendar.DATE, - day);           
	    Date time = c.getTime();         
	    String preDay = sdf.format(time); 
	    return preDay;
	}
	public static int getWeek(String dateStr){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");  
		try {
			Date date=sdf.parse(dateStr);
			Calendar cal = Calendar.getInstance();
			cal.setTime(date);
			return cal.get(Calendar.DAY_OF_WEEK);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return 0;
	}
	public static int getTodayWeek(){
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		return cal.get(Calendar.DAY_OF_WEEK);
	}
	
	public static JSONArray getBookFoodPlatData(int days){  
		 JSONArray array=new JSONArray();
		 SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");  
		 SimpleDateFormat yyyyMM = new SimpleDateFormat("yyyyMM");  
         Calendar dBegin=Calendar.getInstance();
         dBegin.setTime(new Date());
         
         Calendar endDate = Calendar.getInstance();
         endDate.setTime(new Date());
         endDate.add(Calendar.DATE,days);
         
	     while (endDate.getTime().after(dBegin.getTime())){  
	    	 dBegin.add(Calendar.DAY_OF_MONTH, 1); 
	    	 JSONObject result=new JSONObject();
	    	 Date date=dBegin.getTime();
	    	 result.put("YMD",yyyyMMdd.format(date));
        	 result.put("YM",yyyyMM.format(date));
        	 result.put("WEEK", dBegin.get(Calendar.DAY_OF_WEEK));
        	 array.add(result);
	     }  
	     return array;  
	 }
	
	public static String hourAfter(int hour) {
		 Calendar date = Calendar.getInstance();
		 date.setTime(new Date());
		 date.add(Calendar.HOUR,hour);
		 SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  // 设置日期格式
		 String time = simpleDateFormat.format(date.getTime());
		 return time;
	}
	
	public static String getFormatDate(String inputDate) {
		 try {
            // 根据输入的日期格式创建SimpleDateFormat对象
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMdd");
            if (inputDate.contains("/")) {
                inputFormat = new SimpleDateFormat("yyyy/M/d");
            } else if (inputDate.contains("年") && inputDate.contains("月")) {
                inputFormat = new SimpleDateFormat("yyyy年M月d");
            }else {
            	inputFormat = new SimpleDateFormat("yyyy-MM-dd");
            }
            // 将输入日期字符串解析为Date对象
            Date date = inputFormat.parse(inputDate);
            // 创建目标日期格式的SimpleDateFormat对象
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
            // 格式化日期为目标格式
            return outputFormat.format(date);
        } catch (ParseException e) {
        	CoreLogger.getPlatform().error(e.getMessage(),e);
            return null;
        }
	}

	public static String getMonthIdFormat(String dateStr) {
		if(StringUtils.isEmpty(dateStr) || dateStr.length()< 10 ){
			return "";
		}
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
			Date date = sdf.parse(dateStr);
			String date2 = sdf2.format(date);
			String mothId = date2.substring(0, 6);
			return mothId;
		} catch (Exception e) {
			LogUtils.getLogger().error("格式化monthId时出错:"+e.getMessage());
		}
		return "";
	}

	public static String getMonthEndDay(int year,int month){
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.YEAR , year);
		calendar.set(Calendar.MONTH , month - 1);
		calendar.set(Calendar.DATE , 1);
		calendar.add(Calendar.MONTH, 1);
		calendar.add(Calendar.DAY_OF_YEAR , -1);
		String fullMonth=(calendar.get(Calendar.MONTH) + 1) > 9 ? String.valueOf((calendar.get(Calendar.MONTH) + 1)) : "0" + (calendar.get(Calendar.MONTH) + 1);
		return calendar.get(Calendar.YEAR) + "-" + fullMonth + "-" +
				calendar.get(Calendar.DAY_OF_MONTH);
		}

	public static String getFullMonthStr() {
		StringBuffer buf = new StringBuffer("");
		Calendar calendar = Calendar.getInstance();
		buf.append(calendar.get(1));
		buf.append("-");
		buf.append(calendar.get(2) + 1 > 9 ? String.valueOf(calendar.get(2) + 1) : "0" + (calendar.get(2) + 1));
		return buf.toString();
	}
	public static String getLastMonthStr() {
		StringBuffer buf = new StringBuffer("");
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.MONTH, -1);
		buf.append(calendar.get(Calendar.YEAR));
		buf.append("-");
		buf.append(calendar.get(Calendar.MONTH) + 1 > 9 ? String.valueOf(calendar.get(Calendar.MONTH) + 1) : "0" + (calendar.get(Calendar.MONTH) + 1));
		return buf.toString();
	}

	public static String getQuarterStart() {
		Calendar calendar = Calendar.getInstance();
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		int quarter = (month - 1) / 3 + 1;

		int startMonth = (quarter - 1) * 3 + 1;
		calendar.set(year, startMonth - 1, 1);

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(calendar.getTime());
	}

	public static String getQuarterEnd() {
		Calendar calendar = Calendar.getInstance();
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		int quarter = (month - 1) / 3 + 1;
		int endMonth = (quarter - 1) * 3 + 3;
		calendar.set(year, endMonth - 1, 1);
		calendar.add(Calendar.MONTH, 1);
		calendar.add(Calendar.DAY_OF_MONTH, -1);

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(calendar.getTime());
	}

	public static String getQuarterStart(int year, int quarter) {
		Calendar calendar = Calendar.getInstance();
		int startMonth = (quarter - 1) * 3 + 1;
		calendar.set(year, startMonth - 1, 1);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(calendar.getTime());
	}

	public static String getQuarterEnd(int year, int quarter) {
		Calendar calendar = Calendar.getInstance();
		int endMonth = (quarter - 1) * 3 + 3;
		calendar.set(year, endMonth - 1, 1);
		calendar.add(Calendar.MONTH, 1);
		calendar.add(Calendar.DAY_OF_MONTH, -1);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(calendar.getTime());
	}

	public static String getLastYearTodayDate(){
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.YEAR, -1);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		return sdf.format(calendar.getTime());
	}

}
