<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%
  if(request.getRemoteUser()==null){
	  response.sendRedirect("/workbench/sso");
  }else{
	  String userAcct=request.getRemoteUser();
	  String host=request.getHeader("host");
	  out.print(host);
	  if(host.contains("yunqu")){
	 	 response.sendRedirect("http://zentao.yunqu-info.com/zentao/user-login-L3plbnRhby8=.html?get&account="+userAcct+"&password=yq123456");
	  }else{
	  	 response.sendRedirect("http://*************/zentao/user-login-L3plbnRhby8=.html?get&account="+userAcct+"&password=yq123456");
	  }
  }
%>