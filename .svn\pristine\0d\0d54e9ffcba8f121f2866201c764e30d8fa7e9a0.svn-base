<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
		.selfSummary p{height: 32px;line-height: 32px;}
		.leaderSummary span{color: #046c9e;}
		.selfSummary span{color: #046c9e;text-decoration: underline;}
		.selfSummary textarea{text-align: left;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 150px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" value="转正申请单"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 150px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="hidden" name="apply.data1">
					  				<input type="text" data-rules="required" data-ref-dept="#deptName" data-ref-no="#staffNo" data-ref-job="#jobTitle" onclick="singleUser(this)" readonly="readonly" class="form-control input-sm" name="extend.userName"/>
					  			</td>
					  			<td class="required">工号</td>
					  			<td>
					  				<input type="text" data-rules="required" id="staffNo" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" id="deptName" onclick="singleDept(this)" readonly="readonly" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  			<td class="required">职位</td>
					  			<td>
					  				<input type="text" data-rules="required" id="jobTitle" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">试用起止日期</td>
					  			<td style="width: 40%;">
					  				<input type="text" data-rules="required" data-laydate="{type:'date'}" style="width: 40%;display: inline-block;" class="form-control input-sm Wdate" name="apply.data5"/>
					  				<input type="text" data-rules="required" data-laydate="{type:'date'}" style="width: 40%;display: inline-block;" class="form-control input-sm Wdate" name="apply.data6"/>
					  			</td>
					  			<td class="required">预计转正日期</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date'}" class="form-control input-sm Wdate" name="apply.data7"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">试用期月薪(税前)</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data8"/>
					  			</td>
					  			<td class="required">预计转正后月薪(税前)</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data9"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">自我总结</td>
					  			<td colspan="3" class="selfSummary">
					  				<p>1、根据重要性排列出您在试用期间所承担的主要职责或所完成的主要工作任务及其输出结果。（以数据或事实来说明）</p>
					  				<textarea style="height: 80px;" data-rules="required" data-edit-node="person" class="form-control input-sm" name="apply.data10" placeholder="序号 工作任务\关键职责 输出结果"></textarea>
					  				<p>2. 在试用期所取得的工作成绩中，您最自豪的是：</p>
									<textarea style="height: 80px;" data-rules="required" data-edit-node="person"  class="form-control input-sm" name="apply.data11"></textarea>
					  				<p>3. 您在工作中克服了哪些困难或障碍？您认为还需要哪些方面的配合和支持？</p>
									<textarea style="height: 80px;" data-rules="required" data-edit-node="person"  class="form-control input-sm" name="apply.data12"></textarea>
					  				<p>4. 您对今后工作的设想或目标是什么？您会采取哪些措施来改善工作？</p>
									<textarea style="height: 80px;" data-rules="required" data-edit-node="person"  class="form-control input-sm" name="apply.data13"></textarea>
					  				<p>5. 为了更好地开展工作，您希望在哪些方面得到培训或提高？</p>
									<textarea style="height: 80px;" data-rules="required" data-edit-node="person" class="form-control input-sm" name="apply.data14"></textarea>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove">
					  			<td class="required">直接主管评价</td>
					  			<td colspan="3" class="leaderSummary">
					  				<p>1、综合评价（描述该员工的优势、不足、应关注方面、应培养方向等）</p>
					  				<textarea style="height: 50px;" data-edit-node="deptLeader" data-rules="required" class="form-control input-sm" name="apply.data15"></textarea>
					  				<p>2、转正意见：
					  				   <label class="radio radio-inline radio-success" data-edit-node="deptLeader" style="line-height: normal;">
					                      <input type="radio" value="如期转正" checked="checked" data-edit-node="deptLeader" name="apply.data16"><span>如期转正</span>
					              	   </label>
					  				   <label class="radio radio-inline radio-success" data-edit-node="deptLeader" style="line-height: normal;">
					                      <input type="radio" value="延期转正" data-edit-node="deptLeader" name="apply.data16"><span>延期转正</span>
					              	   </label>
					  				   <label class="radio radio-inline radio-success" data-edit-node="deptLeader" style="line-height: normal;">
					                      <input type="radio" value="不予转正" data-edit-node="deptLeader" name="apply.data16"><span>不予转正</span>
					              	   </label>
              						</p>
					  				<p>3、转正薪资： 
					  				   <label class="radio radio-inline radio-success" data-edit-node="deptLeader" style="line-height: normal;">
					                      <input type="radio" value="建议按入职时确认薪资"  data-edit-node="deptLeader" name="apply.data17"><span>建议按入职时确认薪资</span>
					              	   </label>
					  				   <label class="radio radio-inline radio-success" data-edit-node="deptLeader" style="line-height: normal;">
					                      <input type="radio" value="月薪调整" data-edit-node="deptLeader" name="apply.data17"><span>转正月薪调整为</span>
					              	   </label>
					              	   <input data-edit-node="deptLeader" name="apply.data18" type="number" style="width: 80px;display: inline-block;" class="form-control input-sm">元(税前）</p>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideUserTitle:true,title:'{{:data3}}{{:userName}}转正申请单',success:function(data){
				
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			Flow.initData();
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		

		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='person'){
				var data10 = $("[name='apply.data10']").val();
				var data11 = $("[name='apply.data11']").val();
				var data12 = $("[name='apply.data12']").val();
				var data13 = $("[name='apply.data13']").val();
				var data14 = $("[name='apply.data14']").val();
				if(data10==''||data11==''||data12==''|data13==''||data14==''){
					layer.msg('请选择');
					return false;
				}
			}
			if(nodeCode=='deptLeader'){
				var data15 = $("[name='apply.data15']").val();
				if(data15==''){
					layer.msg('必填项必填');
					return false;
				}
			}
			return true;
	     }
		
		Flow.initData = function(){
			var selectUserName = $("[name='extend.userName']").val();
			var params = {selectUserName:selectUserName};
			FlowCore.approveData = params;
		}
		
		Flow.leaveHistory = function(){
			
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>