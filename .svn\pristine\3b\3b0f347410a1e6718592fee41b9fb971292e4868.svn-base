<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="supplierId" type="hidden" value="${param.supplierId}">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>付款方式查询</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		 <span class="input-group-addon">订单号</span>
						 	 <input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		 <span class="input-group-addon">合同名称</span>
						 	 <input class="form-control input-sm" name="contractName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" data-event="enter" class="btn btn-sm btn-default" onclick="DataMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
				<script  type="text/html" id="toolbar">
				    <button type="button" class="btn btn-sm btn-info" lay-event="DataMgr.add">+ 新增付款计划</button>
					<button type="button" class="btn btn-sm btn-success ml-10" lay-event="DataMgr.applyPay">+ 发起付款申请</button>
				</script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var DataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.payTypeMgr',
					id:'dataMgrTable',
					height:'full-140',
					cols: [[
		             {
		           	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 field:'ORDER_NO',
						 title:'订单号',
						 width:80,
						 event:'DataMgr.detail',
						 style:'text-decoration:underline;',
						 templet:'<div><a href="javascript:;">{{d.ORDER_NO}}</a></div>'
					 },{
						 field:'ORDER_TITLE',
						 title:'订单名称',
						 minWidth:220
					 },{
					    field: 'PAY_PERIOD',
						title: '付款条件',
						align:'center',
						width:80
					},{
					    field: 'PAY_DAY',
						title: '工作日付款',
						align:'center',
						width:90
					},{
					    field: 'PAY_RATE',
						title: '付款比例',
						align:'center',
						width:110,
						templet:'<div>{{d.PAY_RATE}}%</div>'
					},{
					    field: 'PAY_AMOUNT',
						title: '付款金额',
						align:'center',
						totalRow:true,
						width:110
					},{
					    field: 'PAY_TYPE',
						title: '付款方式',
						totalRow:true,
						align:'center',
						width:80
					},{
					    field: 'REMARK',
						title: '说明',
						align:'center',
						width:110
					},
					{
						field:'CREATE_TIME',
						width:150,
						title:'录入时间'
					},{
						field:'PLAN_ID',
						title:'付款计划',
						width:70,
						fixed:'right',
						align:'center',
						event:'editPayPlan',
						templet:function(row){
							var planId = row['PLAN_ID'];
							if(planId==''){
								return '<button type="button" class="layui-btn layui-btn-xs" href="#">发起</button>';
							}else{
								return '<a class="layui-btn layui-btn-xs layui-btn-primary">查看</a>';
							}
						}
					}
				]],done:function(data){
					
					
			  }});
			},
			detail:function(data){
				popup.openTab({id:'orderDetail',title:'采购详情',url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{orderId:data.ORDER_ID,custId:data.CUST_ID}});
			},
			supplierDetail:function(data){
				var id = data['SUPPLIER_ID'];
				popup.openTab({id:'supplierDetail',url:'${ctxPath}/pages/erp/supplier/supplier-detail.jsp',title:'供应商详情',data:{supplierId:id}});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable',jumpOne:true});
			},
			edit:function(orderId){
				popup.openTab({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:'${ctxPath}/pages/erp/order/apply-edit.jsp',title:'修改信息',data:{orderId:orderId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:'${ctxPath}/pages/erp/order/order-add.jsp',title:'新建采购申请'});
			},
			contractDetail:function(data){
				popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/pages/crm/contract/contract-detail.jsp',data:{contractId:data.CONTRACT_ID,custId:data['CUST_ID']}});
			},
			applyPay:function(data){
				var len = data.length;
				if(len==0){
					return;
				}
				var supplierId = data[0]['SUPPLIER_ID'];
				var orderId = data[0]['ORDER_ID'];
				var planId = data[0]['PLAN_ID'];
				popup.layerShow({id:'applyPayment',full:true,scrollbar:false,area:['750px','600px'],offset:'20px',url:'${ctxPath}/pages/erp/order/payment/apply-payment.jsp',title:'发起付款申请',data:{planId:planId,supplierId:supplierId,orderIds:orderId}});
			}
		}
		
		function reloadPayTypeList(){
			$("#OrderDetailForm").queryData({id:'payTypeList',page:false});
		}
		
		function addPayType(){
			popup.layerShow({id:'addPayType',scrollbar:false,shadeClose:false,area:['550px','480px'],title:'新增',url:'${ctxPath}/pages/erp/order/pay/type/type-edit.jsp',data:{orderId:orderId}});
		}	
		
		function editPayType(data){
			var typeId = data['TYPE_ID'];
			popup.layerShow({id:'editPayType',scrollbar:false,shadeClose:false,area:['550px','480px'],title:'修改',url:'${ctxPath}/pages/erp/order/pay/type/type-edit.jsp',data:{orderId:orderId,typeId:typeId}});
		}
		
		function editPayPlan(data){
			var orderId = data['ORDER_ID'];
			var typeId = data['TYPE_ID'];
			var planId = data['PLAN_ID'];
			var payAmount = data['PAY_AMOUNT'];
			popup.layerShow({id:'editPayPlan',scrollbar:false,shadeClose:false,area:['550px','480px'],title:'付款计划',url:'${ctxPath}/pages/erp/order/pay/plan/plan-edit.jsp',data:{orderId:orderId,typeId:typeId,planId:planId,payAmount:payAmount}});
		}
		
		
		function reloadPayPlanList(){
			DataMgr.query();
		}
		
		function reloadDataList(){
			DataMgr.query();
		}
		$(function(){
			$("#dataMgrForm").render({success:function(){
				DataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>