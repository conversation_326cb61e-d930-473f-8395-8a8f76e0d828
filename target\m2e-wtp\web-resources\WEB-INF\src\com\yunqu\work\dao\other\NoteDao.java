package com.yunqu.work.dao.other;

import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.NoteModel;

@WebObject(name="NoteDao")
public class NoteDao extends AppDaoContext {

	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		NoteModel model=new NoteModel();
		model.setColumns(getParam("note"));
		JSONObject result = queryForRecord(model);
		JSONObject data = result.getJSONObject("data");
		String noteAuth = data.getString("NOTE_AUTH");
		String creator = data.getString("CREATOR");
		if("1".equals(noteAuth)&&!getUserId().equalsIgnoreCase(creator)) {
			return getJsonResult(new JSONObject());
		}
		return result;
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select note_id,title from yq_note");
	}
	
	@WebControl(name="tagsDict",type=Types.DICT)
	public JSONObject tagsDict(){
		try {
			List<JSONObject> list = this.getQuery().queryForList("select DISTINCT(tags) from yq_note where creator = ?", new Object[] {getUserId()}, new JSONMapperImpl());
			Set<String> set = new HashSet<String>();
			for(JSONObject row:list) {
				String tags = row.getString("TAGS");
				String[] array  = tags.split(",");
				for(String str:array) {
					if(StringUtils.notBlank(str)) {
						set.add(str);
					}
				}
			}
			String[] array = new String[set.size()*2];
			int i=0;
			for(String str:set) {
				array[i] = str;
				array[i+1] = str;
				i = i+2;
			}
			return getDictByArray(array);
		} catch (SQLException e) {
			return getDictByArray("默认","默认");
		}
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from yq_note where 1=1");
		sql.appendLike(param.getString("tags"),"and tags like ?");
		sql.appendLike(param.getString("title"),"and title like ?");
		String noteAuth = param.getString("noteAuth");
		sql.append(noteAuth," and note_auth = ?");
		sql.append(getUserPrincipal().getUserId()," and creator = ?");
		sql.append("order by update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
}
