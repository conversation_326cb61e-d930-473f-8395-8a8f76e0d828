<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>

<table id="goodsTable"></table>
<button class="btn btn-info btn-sm" onclick="addGoods()" type="button">新增</button>
<button class="btn btn-default btn-sm ml-15" onclick="updateGoods()" type="button">修改</button>
<button class="btn btn-default btn-sm ml-15" onclick="storageQuery()" type="button">入出库查询</button>
<button class="btn btn-default btn-sm ml-15" onclick="putStorage()" type="button">入库</button>
<button class="btn btn-default btn-sm ml-15" onclick="outStorage()" type="button">出库</button>
	<script type="text/javascript">
		$(function(){
			$("#OrderDetailForm").initTable({
				mars:'OrderDao.goodsList',
				id:'goodsTable',
				page:false,
				height:300,
				cellMinWidth:100,
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
            	 	type: 'radio',
					align:'left'
				 },{
				    field: 'GOODS_NO',
					title: '编号',
					align:'left'
				},{
				    field: 'NAME',
					title: '供应商名称',
					align:'center'
				},{
				    field: 'NUMBER',
					title: '总数/入库/出库',
					align:'center',
					templet:'<div>{{d.NUMBER}}/{{d.IN_NUMBER}}/{{d.OUT_NUMBER}}</div>'
				},{
				    field: 'TAX_RATE',
					title: '税率',
					align:'center'
				},{
				    field: 'PRICE',
					title: '单价',
					align:'center'
				},{
				    field: 'TOTAL_PRICE',
					title: '总价',
					align:'center'
				},{
					field:'REMARK',
					title:'备注',
					edit:'text'
				},{
				    field: 'CREATE_USER_NAME',
					title: '创建人',
					align:'center'
				},{
				    field: 'UPDATE_TIME',
					title: '更新时间',
					align:'center'
				}
			]],done:function(){
				
		  	}
		});
		});
		
		function reloadDataList(){
			$("#OrderDetailForm").queryData({id:'goodsTable',page:false});
		}
		
		function editGoods(id){
			popup.layerShow({id:'goods',title:'编辑',area:['600px','400px'],shadeClose:false,data:{goodsId:id},url:'${ctxPath}/pages/erp/goods/goods-edit.jsp'});
		}
		
		function addGoods(){
			popup.layerShow({id:'goods',title:'新增',area:['600px','400px'],shadeClose:false,data:{orderId:'${param.orderId}'},url:'${ctxPath}/pages/erp/goods/goods-edit.jsp'});
		}
		
		function updateGoods(){
			var checkStatus = table.checkStatus('goodsTable');
	 		var data = checkStatus.data;
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择物料。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		editGoods(data[0]['GOODS_ID']);
		}
		
		function storageQuery(){
			
		}
		
		function putStorage(){
			doStorage('put');
		}
		
		function doStorage(type){
			var checkStatus = table.checkStatus('goodsTable');
			if(type=='cancel'){
				 checkStatus = table.checkStatus('goodsStorageTable');
			}
	 		var data = checkStatus.data;
	 		var sum = data.length;
	 		if(sum <= 0){
	 			layer.msg('请选择物料。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		var ids = [];
	 		for(var i = 0; i < sum; i ++){
 				ids.push(data[i].GOODS_ID);
	 		}
	 		if(ids.length <= 0){
	 			layer.msg('请选择。',{icon : 7, time : 1000});
	 			return;
	 		}
	 		var data= {goodsId:ids.join(','),type:type,orderId:orderId};
	 		
	 		popup.layerShow({id:'storageEdit',area:['550px','400px'],shadeClose:false,title:'物料入出库',url:'${ctxPath}/pages/erp/goods/storage-edit.jsp',data:data});
			
		}
		
		function outStorage(){
			doStorage('out');
		}
		
</script>
