package com.yunqu.work.model;

import com.yunqu.work.base.AppBaseModel;

public class LookLogModel extends AppBaseModel {
   private static final long serialVersionUID = 1L;
	
   private String logId;
	
   private String lookBy;
   
   private String fkId;
   
   private String lookTime;
   
	
   public LookLogModel(){
	   setTableInfo("YQ_LOOK_LOG", "LOG_ID");
   }


	public String getLogId() {
		return logId;
	}
	
	
	public void setLogId(String logId) {
		this.logId = logId;
		this.set("log_id", logId);
	}
	
	
	public String getLookBy() {
		return lookBy;
	}
	
	
	public void setLookBy(String lookBy) {
		this.lookBy = lookBy;
		this.set("look_by", lookBy);
	}
	
	
	public String getFkId() {
		return fkId;
	}
	
	
	public void setFkId(String fkId) {
		this.fkId = fkId;
		this.set("fk_id", fkId);
	}
	
	
	public String getLookTime() {
		return lookTime;
	}
	
	
	public void setLookTime(String lookTime) {
		this.lookTime = lookTime;
		this.set("look_time", lookTime);
	}
	   
   

}
