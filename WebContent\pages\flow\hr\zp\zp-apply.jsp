<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td style="width: 38%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">招聘岗位</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data1"/>
					  			</td>
					  			<td class="required">招聘人数</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">薪资范围</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data3"/>
					  			</td>
					  			<td class="required">到岗日期</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">招聘原因</td>
					  			<td>
					  				<select  data-rules="required" class="form-control input-sm" name="apply.data7">
					  					<option value="">--</option>
					  					<option value="新增人员">新增人员</option>
										<option value="替换离职人员">替换离职人员</option>
										<option value="调动补充">调动补充</option>
										<option value="其他">其他</option>
					  				</select>
					  			</td>
					  			<td class="required">工作地点(驻地)</td>
					  			<td>
					  				<input type="text" data-rules="required"  class="form-control input-sm" maxlength="500" name="apply.data8"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">招聘要求</td>
					  			<td colspan="3">
									<textarea style="height: 70px;" placeholder="性别，年龄，学历，专业要求，经验要求等" class="form-control input-sm" onchange="limitLenth(this,500);" name="apply.data6"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">岗位职责</td>
					  			<td colspan="3">
									<textarea style="height: 70px;" placeholder="请按照重要程度由强到弱填写" class="form-control input-sm" onchange="limitLenth(this,500);" name="apply.data5"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>其他备注</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" onchange="limitLenth(this,500);" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  				 <a class="ml-5 unedit-remove" target="_blank" href="/zp_template.doc">下载模板</a>
					  			</td>
					  		</tr>
					  		<tr class="remindInfo hidden-print">
					  			<td>注意事项</td>
					  			<td colspan="3">
					  				<small>1.单个岗位招聘，可直接填写本表单；<br>2.多岗位招聘可下载模板填写并上传。</small>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){

			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>