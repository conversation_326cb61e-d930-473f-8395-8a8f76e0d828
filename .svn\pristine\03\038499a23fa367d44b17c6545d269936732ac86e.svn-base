package com.yunqu.work.servlet.common;


import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.easitline.common.core.Globals;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.Constants;


/**
 * 文件预览
 */
@WebServlet(urlPatterns = { "/fileview/*" })
public class FileViewServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;
	
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)throws ServletException, IOException {
		this.doPost(req,resp);  
		
	}
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response)throws ServletException, IOException {
		try{
			String url = request.getRequestURI();  
			String id = url.substring(url.indexOf("/fileview")+10);
		    EasyQuery query=EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
		    JSONObject jsonObject= query.queryForRow("select * from yq_files where file_id = ?", new Object[]{id}, new JSONMapperImpl());
			String accessUrl=jsonObject.getString("ACCESS_URL");
			String filename =jsonObject.getString("FILE_NAME");
			String fileType =jsonObject.getString("FILE_TYPE");
			String fkId =jsonObject.getString("FK_ID");
		    File file =  getFile(accessUrl);
		   	if(file==null||!file.exists()){
		   		response.sendError(404,"file not exist.");
		   		return;
		   	}
		   
		   	EasyRecord record=new EasyRecord("yq_file_download_log","log_id");
		   	record.setPrimaryValues(RandomKit.uuid());
		   	record.set("file_id",id);
		   	record.set("fk_id",fkId);
		   	record.set("download_by",request.getUserPrincipal().getName());
		   	record.set("download_time",EasyDate.getCurrentDateString() );
		   	query.save(record);
		   	boolean bl=FileKit.imageFormat(filename);
		   	if(bl){
		   		Render.renderImages(response, file);
		   	}else{
	   			if(StringUtils.notBlank(filename)){
			   	   response.setHeader("Content-disposition","attachment;filename="+toUtf8String(filename));
			   	   if(file!=null){
			   		 response.setHeader("Content-Length",file.length()+"");
			   	   }
			   	}
		   		response.setContentType(getContentType(fileType)+";charset=UTF-8");
		   		FileUtils.copyFile(file, response.getOutputStream());
		   	}
		}catch(Exception ex){
			response.sendError(500,"file not exist.");
		}
	}
	public static String getContentType(String filePath){
	    Path path = Paths.get(filePath);  
	    String contentType = null;  
	    try {  
	        contentType = Files.probeContentType(path);  
	    } catch (IOException e) {  
	        e.printStackTrace();  
	    }
	    return contentType; 
	}
	/**
	 * 处理附件为中文名的问题
	 * @param fn
	 * @return
	 */
	private String toUtf8String(String fn) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < fn.length(); i++) {
			char c = fn.charAt(i);
			if (c >= 0 && c <= 255) {
				sb.append(c);
			} else {
				byte[] b;
				try {
					b = Character.toString(c).getBytes("utf-8");
				} catch (Exception ex) {
					// System.out.println(ex);
					b = new byte[0];
				}
				for (int j = 0; j < b.length; j++) {
					int k = b[j];
					if (k < 0)
						k += 256;
					sb.append("%" + Integer.toHexString(k).toUpperCase());
				}
			}
		}
		return sb.toString();
	}
	
	
	//如果文件存在直接返回
	private File getFile(String uri){
	  File distFile =  new File(getBaseDir()+uri);
	  if(distFile.isFile()) return distFile;
	  return null;
	}
	
	private static String getBaseDir() {
		//return Globals.WEBAPPS_DIR+File.separator+"ROOT";
		return Globals.WEBAPPS_DIR;
	}
	
	
}