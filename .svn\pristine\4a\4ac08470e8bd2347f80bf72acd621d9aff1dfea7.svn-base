<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>云趣乐享平台 - ${staffInfo.userName}</title>
</EasyTag:override>
<EasyTag:override name="content">
	 <div class="layui-container fly-marginTop fly-user-main">
  <ul class="layui-nav layui-nav-tree layui-inline" lay-filter="user">
    <li class="layui-nav-item">
      <a href="/yq-work/fly/u">
        <i class="layui-icon">&#xe609;</i>我的主页
      </a>
    </li>
    <li class="layui-nav-item layui-this">
      <a href="/yq-work/fly/my">
        <i class="layui-icon">&#xe612;</i>用户中心
      </a>
    </li>
    <li class="layui-nav-item">
      <a href="/yq-work/fly/message">
        <i class="layui-icon">&#xe611;</i> 我的消息
      </a>
    </li>
  </ul>

  <div class="site-tree-mobile layui-hide">
    <i class="layui-icon">&#xe602;</i>
  </div>
  <div class="site-mobile-shade"></div>
  
  <div class="site-tree-mobile layui-hide">
    <i class="layui-icon">&#xe602;</i>
  </div>
  <div class="site-mobile-shade"></div>
  
  
  <div class="fly-panel fly-panel-user" pad20>
    <div class="layui-tab layui-tab-brief" lay-filter="user">
      <ul class="layui-tab-title" id="LAY_mine">
        <li data-type="mine-jie" lay-id="index" class="layui-this">我发的帖（<span>${myCreateArticleCount}</span>）</li>
        <li data-type="collection" data-url="/collection/find/" lay-id="collection">我收藏的帖（<span>${collectCount}</span>）</li>
      </ul>
      <div class="layui-tab-content" style="padding: 20px 0;">
        <div class="layui-tab-item layui-show">
          <ul class="mine-view jie-row">
          	<c:forEach items="${myCreateArticleList}" var="item">
            <li>
              <a class="jie-title" href="/yq-work/fly/detail/${item.remind_id}" target="_blank">${item.title}</a>
              <i>${item.publish_time}</i>
              <a class="mine-edit" href="/yq-work/fly/edit/${item.remind_id}">编辑</a>
              <em>${item.view_count}阅/${item.comment_count}答</em>
            </li>
          	</c:forEach>
          </ul>
          <div id="LAY_page"></div>
        </div>
        <div class="layui-tab-item">
          <ul class="mine-view jie-row">
          	<c:forEach items="${collectList}" var="item">
              <li>
              	<a class="jie-title" href="/yq-work/fly/detail/${item.remind_id}" target="_blank">${item.title}</a>
              	<i>收藏于${item.collect_time}</i>  
              </li>
          	</c:forEach>
          </ul>
          <div id="LAY_page1"></div>
        </div>
      </div>
    </div>
  </div>
 </div>

</EasyTag:override>
 
<EasyTag:override name="script">
	<script type="text/javascript">
	 	layui.cache.page = 'user';
	 	layui.cache.user = {username: '${staffInfo.userName}' , uid: '${staffInfo.userId}', avatar: '${staffInfo.picUrl}', experience: '${staffInfo.points}'};
		layui.config({version: "3",base: '/yq-work/pages/fly/static/mods/'}).extend({fly: 'index'}).use('fly');
   </script>
</EasyTag:override>
<%@ include file="fly-layout.jsp" %>
