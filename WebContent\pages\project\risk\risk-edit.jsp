<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>project</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="riskEditForm" class="form-horizontal" data-mars="ProjectRiskDao.record" autocomplete="off" data-mars-prefix="projectRisk.">
        <input type="hidden" value="${param.riskId}" name="projectRisk.RISK_ID"/>
        <input type="hidden" value="${param.projectId}" name="projectRisk.PROJECT_ID">
        <input type="hidden" value="${param.projectName}" name="projectRisk.PROJECT_NAME">
        <table class="table table-edit table-vzebra">
            <tbody>
                <tr>
                    <td style="width: 120px" class="required">风险分类</td>
                    <td>
                        <select name="projectRisk.RISK_BELONG" data-rules="required" class="form-control input-sm">
                            <option value="">请选择</option>
                            <option value="我方">我方</option>
                            <option value="客户">客户</option>
                            <option value="三方(外购)">三方（外购）</option>
                            <option value="三方(外包)">三方（外包）</option>
                        </select>
                    </td>
                  </tr>
                  <tr>
                    <td style="width: 100px">风险等级</td>
                    <td>
                        <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                            <input type="radio" checked value="0" name="projectRisk.RISK_LEVEL"><span>低</span>
                        </label>
                        <label class="radio radio-info radio-inline">
                            <input type="radio" value="1" name="projectRisk.RISK_LEVEL"><span>中</span>
                        </label>
                        <label class="radio radio-info radio-inline">
                            <input type="radio" value="2" name="projectRisk.RISK_LEVEL"><span>高</span>
                        </label>
                    </td>
                  </tr>
                 <tr>
                    <td>风险触发时间</td>
                    <td>
                        <input type="text" data-rules="required" data-mars="CommonDao.today" name="projectRisk.TRIGGER_TIME" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="form-control Wdate">
                    </td>
                  </tr>
                 <tr>
                    <td>最晚解除时间</td>
                    <td>
                        <input type="text" data-rules="required" name="projectRisk.RELEASE_TIME" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="form-control Wdate">
                    </td>
                  </tr>
                  <tr>
                    <td>负责处理人</td>
                    <td>
	                    <input name="projectRisk.SOL_OWNER" type="hidden">
	                    <input name="projectRisk.OWNER_NAME" onclick="singleUser(this);" id="ownerName" readonly="readonly" class="form-control input-sm select-icon">
                    </td>
                </tr>
                <c:if test="${empty param.riskId}">
                  <tr>
                    <td>风险抄送人</td>
                    <td>
	                    <input name="ccIds" type="hidden">
						<input name="ccNames" type="text" placeholder="可选"  onclick="multiUser(this);" readonly="readonly" class="form-control input-sm select-icon">
                    </td>
                  </tr>
                </c:if>
                <tr>
                    <td class="required">风险说明</td>
                    <td>
                        <textarea style="height: 110px;" placeholder="例如：因客户变动导致验收时间不可靠" data-rules="required" class="form-control input-sm" name="projectRisk.RISK_DESC"></textarea>
                    </td>
                </tr>
                <tr>
                    <td class="required">风险应对措施</td>
                    <td>
                        <textarea style="height: 80px;" placeholder="例如：销售应该及时介入沟通推送验收" data-rules="required" class="form-control input-sm" name="projectRisk.SOLUTION"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="RiskEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
        </div>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">

    	var RiskEdit = {
    		riskId:'${param.riskId}',
    		projectId:'${param.projectId}'
    	}

        $(function(){
            $("#riskEditForm").render({success:function(result){

            }});
        });

        RiskEdit.ajaxSubmitForm = function(){
        	var ownerName = $("#ownerName").val();
        	if(ownerName==''){
        		layer.msg('请选择负责人',{icon:7,offset:'20px',time:1200});
        		return;
        	}
            if(form.validate("#riskEditForm")){
                if(RiskEdit.riskId){
                    RiskEdit.updateData();
                }else{
                    RiskEdit.insertData();
                }
            };
        }

        RiskEdit.insertData = function() {
            var data = form.getJSONObject("#riskEditForm");
            ajax.remoteCall("${ctxPath}/servlet/projectRisk?action=add",data,function(result) {
                if(result.state == 1){
                	addFlow(data,result.data);
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadRisk();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        RiskEdit.updateData = function() {
            var data = form.getJSONObject("#riskEditForm");
            ajax.remoteCall("${ctxPath}/servlet/projectRisk?action=update",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadRisk();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }
        
        function addFlow(formData,id){
    		var data = {"applyState":"0","flowCode":"pj_risk","doAction":"submit","checkResult":"1","noticeType":["1","2"],"urgent":"3","apply.fill_time":"20"};
    		data['businessId'] = id;
    		data['apply.apply_level'] = '紧急';
    		data['checkDesc'] = '尽快处理';
    		data['apply.apply_title'] = '项目风险反馈-'+formData['projectRisk.PROJECT_NAME'];
    		data['apply.apply_remark'] = formData['projectRisk.RISK_DESC'];
    		data['selectUserId'] = formData['projectRisk.SOL_OWNER'];
    		data['selectUserName'] = formData['projectRisk.OWNER_NAME'];
    		

    		var staffInfo = getStaffInfo();
    		data['apply.dept_name'] = staffInfo.deptName;
    		data['apply.apply_name'] = staffInfo.userName;
    		
    		data['ccIds'] = formData.ccIds;
    		data['ccNames'] = formData.ccNames;
    		
    		data['apply.data1'] = formData['projectRisk.OWNER_NAME'];
    		data['apply.data2'] = formData['projectRisk.SOL_OWNER'];
    		
    		data['apply.data4'] = formData.ccIds;
    		data['apply.data3'] = formData.ccNames;
    		
    		data['apply.data5'] = formData['projectRisk.PROJECT_ID'];
    		data['apply.data6'] = formData['projectRisk.PROJECT_NAME'];
    		
    		
    		console.log(data);
    		ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=submitApply",data,function(result) { 
    			if(result.state != 1){
    				layer.alert(result.msg,{icon: 5});
    			}
    		});
    	}
        
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>