<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的申请</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input type="hidden" name="flowCode" value="${flowCode}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm ml-5">
							 <span class="input-group-addon">入账日期</span>	
							 <input type="text" name="beginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
							 <input type="text" name="endDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
				    	 </div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">付款状态</span>	
							<select name="payState" onchange="queryData();" class="form-control input-sm">
							      <option value="">请选择 </option>
							      <option data-class="label label-warning" value="1">待付款</option>
                    			  <option data-class="label label-success" value="11">部分付款</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>

		  </script>
		</form>
		
		<div id="selectDate" style="display: none;">
			<div style="padding: 10px 30px;">
		 		<input class="form-control input-sm Wdate" type="text" id="date" style="width:160px;" placeholder="请选择日期" >
		 		<textarea class="form-control input-sm mt-5 hidden" id="remark" style="width:100%;" placeholder="备注" ></textarea>
			</div>
		</div>
		
		 <script type="text/html" id="btnBar">
 				<EasyTag:res resId="To_PAY_AUTH">
			 		<button class="btn btn-sm btn-info" lay-event="toPay"><span class="glyphicon glyphicon-wrench"></span> 付款</button>
			 		<button class="btn btn-sm btn-warning ml-10" lay-event="entryPay"><span class="glyphicon glyphicon-ok"></span> 入账</button>
 				</EasyTag:res>
			 	<button class="btn btn-sm btn-default ml-10" lay-event="refreshData"><span class="glyphicon glyphicon-refresh"></span> 刷新</button>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'BxReportDao.todoBxPay',
					id:'flowTable',
					page:true,
					toolbar:'#btnBar',
					rowDoubleEvent:'flowDetail',
					height:'full-130',
					limit:20,
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },
					 {
	            	 	type: 'checkbox',
						title: '序号',
						align:'left'
					 },{
						 field:'APPLY_NO',
						 title:'编号',
						 width:160,
						 templet:function(row){
							 return row['APPLY_NO'];
						 }
					 },{
						 field:'PAY_MONEY',
						 title:'应付金额',
						 width:100
					 },{
						 field:'DO_PAY_MONEY',
						 title:'本次付款',
						 edit:'text',
						 width:100
					 },{
						 field:'RECORDED',
						 title:'入账状态',
						 templet:function(row){
							 if(row.RECORDED=='0'){
								 return '未入账';
							 }else{
								 return '已入账';
							 }
						 }
					 },{
						 field:'APPLY_REMARK',
						 title:'报销说明',
						 width:120
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					},{
					    field: 'APPLY_END_TIME',
						title: '审批完成时间',
						align:'center',
					    width:160
					},{
						field:'',
						title:'操作',
						width:50,
						fixed:'right',
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				]],done:function(){
					layui.use('laydate', function(){
						 var laydate = layui.laydate;
						 laydate.render({max:0, min:-365,elem: "#date"});
					});
			  	}
			});
		 }
			
		function flowDetail(data){
			var id = data['FLOW_CODE'];
			var json  = $.extend({},{businessId:data['APPLY_ID'],id:id});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
		
		function refreshData(){
		   location.reload();
		}
		
		function toPay(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
		   }
		  
		   var array = [];
		   for(var index in dataList){
			   var row  = dataList[index];
			   array.push({id:dataList[index]['APPLY_ID'],payMoney:dataList[index]['PAY_MONEY'],thisMoney:dataList[index]['DO_PAY_MONEY']});
		   } 
		   
		   var index = layer.open({type:1,content:$('#selectDate'),shade:0.1,offset:'20px',area:['300px','220px'],btn:['提交','取消'],yes:function(){
			   layer.confirm('确认操作吗,不可逆',{icon:3,offset:'20px'},function(){
				   var date = $('#date').val();
				   if(date==''){
						layer.msg('请选择付款日期。',{icon : 7, time : 1000});
					    return;
				   }
				   layer.close(index);
					ajax.remoteCall("${ctxPath}/servlet/bx/fun?action=updatePayState",{array:array,date:date},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,function(){
								queryData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
		   }},function(){
			  
		   });
		   
		}
		
		function entryPay(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
		   }
		   var array = [];
		   for(var index in dataList){
			   var row  = dataList[index];
			   var recorded = row['RECORDED'];
			   if(recorded=='0'){
				   array.push({id:row['APPLY_ID']});
			   }
		   } 
		   if(array.length==0){
				layer.msg('没有符合条件的记录。',{icon : 7, time : 1000});
			   return;
		   }
		   var index = layer.open({type:1,content:$('#selectDate'),shade:0.1,offset:'20px',area:['300px','220px'],btn:['提交','取消'],yes:function(){
			   layer.confirm('确认操作吗,不可逆',{icon:3,offset:'20px'},function(){
				   var date = $('#date').val();
				   if(date==''){
						layer.msg('请选择入账日期。',{icon : 7, time : 1000});
					    return;
				   }
				   layer.close(index);
					ajax.remoteCall("${ctxPath}/servlet/bx/fun?action=updateBxRecorded",{array:array,date:date},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,function(){
								queryData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
		   }},function(){
			  
		   });
		}
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>