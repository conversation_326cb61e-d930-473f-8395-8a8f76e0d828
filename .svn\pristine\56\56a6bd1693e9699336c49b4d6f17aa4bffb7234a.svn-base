<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>${filename}</title>
	<style>
		html,body,#placeholder{
            height: 100%;
        }
	</style>
	 <script type="text/javascript" src="http://*************:8889/web-apps/apps/api/documents/api.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
		 <div id="placeholder" class = "nav"></div>
</EasyTag:override>
<EasyTag:override name="script">

 <script type="text/javascript">
		
	var documentType = 'text';
 	var fileType = '${fileType}';
 	if(fileType.startsWith('ppt')){
 		documentType = 'presentation';
 	}else if(fileType.startsWith('xls')){
 		documentType = 'spreadsheet';
 	}else{
 		documentType = 'text';
 	}
     new DocsAPI.DocEditor("placeholder", {
		"document": {
		  "fileType": "${fileType}",
		  "key": "${key}",
		  "title": "${filename}",
		  "url": "http://"+location.host+"${accessUrl}"
		},
		"documentType": documentType,
		"width": "100%", 
		"height": (window.innerHeight-30)+"px",
		"lang": "zh",
	    "location": "zh",
		"editorConfig":{
			 "mode": "view",
             "customization": {
                 "chat": false,
                 "comments": false,
                 "compactHeader": true,
                 "compactToolbar": true,
                 "help": true
		    	//"callbackUrl": "/yq-work/office"
		 	}
		},
		 "permissions": {
	    	 	"changeHistory": true,
	            "comment": false,
	            "copy": false,
	            "download": false,
	            "edit": false,
	            "fillForms": false,
	            "modifyContentControl": false,
	            "modifyFilter": false,
	            "print": false,
	            "rename": false,
	            "review": false
			}
	  });
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>