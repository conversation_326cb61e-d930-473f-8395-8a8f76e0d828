package com.yunqu.work.servlet.common;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.MemosService;
import com.yunqu.work.service.NextCloudService;
import com.yunqu.work.service.OAService;
import com.yunqu.work.service.WeeklyNoticeService;
import com.yunqu.work.service.YApiService;
import com.yunqu.work.service.ZendaoService;

@WebServlet("/servlet/excuteJob/*")
public class ExcuteJobServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	public void actionForZendao(){
		ZendaoService.getService().run();
	    renderText("ok");
	}
	
	public void actionForNewZendao(){
		ZendaoService.getService().newZendao();
		renderText("ok");
	}
	
	public void actionForMemos(){
		MemosService.getService().run();
		renderText("ok");
	}
	
	public void actionForRegYApi() {
		YApiService.getService().regUser();
		renderText("ok");
	}
	
	public void actionForOA(){
		OAService.getService().scanOaAcount();
		renderText("ok");
	}
	
	public void actionForNextCloudSyn(){
		 NextCloudService.getService().run();
		renderText("同步数据");
	}
	
	public void actionForCreateProjectWorkHour(){
		String monthId = getPara("monthId");
		String deptName = getPara("deptName");
		if(StringUtils.isBlank(monthId)) {
			monthId = EasyCalendar.newInstance().getFullMonth();
		}
		WeeklyNoticeService.getService().createProjectWorkHour(monthId,deptName);
		renderText("同步数据ok");
	}
	
	public void actionForWhNotice(){
		String monthId = getPara("monthId");
		if(StringUtils.isBlank(monthId)) {
			monthId = EasyCalendar.newInstance().getFullMonth();
		}
		WeeklyNoticeService.getService().workhourNotice(monthId, getUserId());
		renderText("同步数据ok");
	}

	
}
