package com.yunqu.work.dao;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="CommonDao")
public class CommonDao extends AppDaoContext {
	
	@WebControl(name="monthRange",type=Types.TEXT)
	public JSONObject monthRange(){
		Calendar calendar=Calendar.getInstance();
		calendar.add(Calendar.MONTH, -1);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM-dd")+" 到 "+EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	@WebControl(name="currentMonthRange",type=Types.TEXT)
	public JSONObject currentMonthRange(){
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Calendar ca = Calendar.getInstance();   
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH)); 
        String last = format.format(ca.getTime());
	        
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM")+"-01"+" 到 "+last);
	}
	
	@WebControl(name="date01",type=Types.TEXT)
	public JSONObject date01(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd")+" 00:00");
	}
	@WebControl(name="date02",type=Types.TEXT)
	public JSONObject date02(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm"));
	}
	@WebControl(name="date03",type=Types.TEXT)
	public JSONObject data03(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyyMMdd"));
	}
	
	@WebControl(name="randomId",type=Types.TEXT)
	public JSONObject randomId(){
		return getJsonResult(RandomKit.uniqueStr());
	}
	@WebControl(name="userName",type=Types.TEXT)
	public JSONObject userName(){
		return getJsonResult(getUserPrincipal().getUserName());
	}
	@WebControl(name="userDict",type=Types.DICT)
	public JSONObject userDict(){
		//JSONObject result=EhcacheKit.get(CacheTime.CacheName.hour.get(), "userList");
		//if(result==null){
			EasyQuery query=ServerContext.getAdminQuery();
			this.setQuery(query);
			JSONObject	result=getDictByQuery("select USER_ID,USERNAME from easi_user where STATE = 0");
			//EhcacheKit.put(CacheTime.CacheName.hour.get(), "userList",result);
		//}
		return result;
		
	}
	@WebControl(name="userList",type=Types.LIST)
	public JSONObject userList(){
		EasyQuery query=ServerContext.getAdminQuery();
		this.setQuery(query);
		JSONObject jsonObject=queryForList("select USER_ID,USERNAME,PIC_URL from easi_user ",null);//where STATE = 0
		jsonObject.put("isSuperUser", isSuperUser());
		return jsonObject;
	}
	@WebControl(name="userInfo",type=Types.OTHER)
	public JSONObject userInfo(){
		String id=param.getString("userId");
		JSONObject jsonObject=queryForRecord("select * from yq_user_info where user_id = ? ",id);
		return jsonObject;
	}
	
	/**
	 * 获取今天时间
	 * @return
	 */
	@WebControl(name="today",type=Types.TEXT)
	public JSONObject today(){
		return getText(EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	
	/**
	 * 获取三天后
	 * @return
	 */
	@WebControl(name="date04", type=Types.TEXT)
	public JSONObject date04() {
		Date date = EasyDate.getCurrentDate();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, 3);
		date = calendar.getTime();
		return getText(EasyDate.dateToString(date, "yyyy-MM-dd HH:mm:ss"));
	}
	/**
	 * 获取两周后
	 * @return
	 */
	@WebControl(name="twoWeeks", type=Types.TEXT)
	public JSONObject twoWeeks() {
		Date date = EasyDate.getCurrentDate();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.WEEK_OF_YEAR, -2);
		date = calendar.getTime();
		return getText(EasyDate.dateToString(date, "yyyy-MM-dd"));
	}
	@WebControl(name="commentsList",type=Types.TEMPLATE)
	public JSONObject fileList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_comments where fk_id = ? order by create_time desc";
			return queryForList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 查看日志
	 * @return
	 */
	@WebControl(name="lookLogList",type=Types.LIST)
	public JSONObject lookLogList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_look_log where fk_id = ? order by look_time desc";
			return queryForPageList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 操作日志
	 * @return
	 */
	@WebControl(name="lookOpList",type=Types.LIST)
	public JSONObject lookOpList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_oplog where fk_id = ? order by op_time desc";
			return queryForList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 下载日志
	 * @return
	 */
	@WebControl(name="downloadLog",type=Types.LIST)
	public JSONObject downloadLog(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select t2.file_name,t2.file_size,t1.download_time,t1.download_by from yq_file_download_log t1,yq_files t2 where t1.file_id=t2.file_id and t1.fk_id = ? ORDER BY t1.download_time desc";
			return queryForPageList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 消息
	 * @return
	 */
	@WebControl(name="myMessage",type=Types.LIST)
	public JSONObject myMessage(){
		String msgType=param.getString("msgType");
		if(StringUtils.notBlank(msgType)){
			String sql="select * from YQ_MESSAGE   where RECEIVER = ?  and MSG_TYPE = ? ORDER BY CREATE_TIME desc";
			return queryForPageList(sql, new Object[]{getUserPrincipal().getUserId(),msgType});
		}else{
			String sql="select * from YQ_MESSAGE  where RECEIVER = ? ORDER BY CREATE_TIME desc";
			return queryForPageList(sql, new Object[]{getUserPrincipal().getUserId()});
		}
	}
}
