<!DOCTYPE HTML>
<html>
	<head>
		<meta charset="utf-8">
		<title>周报管理</title>
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/buijs@latest/lib/latest/bui.css">
		<script src="https://cdn.jsdelivr.net/npm/buijs@latest/lib/zepto.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/buijs@latest/lib/latest/bui.js"></script>
		<style>
			.page-comment>main {
				background:#fff
			}
			.page-comment .user-wrap {
				position: relative;
				min-height: 3rem;
			}
			.page-comment .userinfo {
				position: absolute;
				bottom: -.4rem;
				right: .2rem;
			}
			.page-comment .userface {
				width: 1.2rem;
				height: 1.2rem;
				overflow: hidden;
				border-radius: .1rem;
			}
			.page-comment .nickname {
				color: #fff;
				padding: .1rem .2rem;
				font-size: .36rem;
			}
			.page-comment .comment-box {
				padding-top: .5rem;
			}
			.page-comment .item-title {
				margin-bottom:0;
				color:#333
			}
			.page-comment .bui-list .span1>.bui-box {
				margin-bottom:.2rem
			}
			.page-comment footer {
				background:#fff;
				height:1.3rem
			}
			.page-comment footer .bui-input input {
				background:#F3F5F8;
				border-radius:.1rem;
				padding-left:.2rem;
				height:.8rem
			}
			.page-comment footer .bui-btn {
				color:#39a4ff;
				border:0
			}
			.page-comment footer .bui-btn:active {
				background:none
			}
			.comment-line {
				padding:.2rem
			}
			.comment-line .thumbnail {
				width:.7rem;
				height:.7rem;
				border-radius: .1rem;
				overflow: hidden;
				margin-right:.2rem
			}
			.comment-line .item-title {
				margin-bottom:.2rem;
				color:#6685B0;
				font-size:.28rem
			}
			.comment-line .bui-rating .bui-rating-cell {
				margin-left:0;
				margin-right:0
			}
			.comment-line .bui-rating .bui-rating-cell:before {
				font-size:.4rem
			}
			.comment-line .time {
				display:inline-block;
				height:.7rem;
				line-height:.7rem
			}
			.comment-content {
				-webkit-line-clamp:3;
				color:#333;
				word-break: break-all;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				overflow: hidden;
			}
			.comment-content.active,.comment-content.autoheight {
				-webkit-line-clamp:inherit
			}
			.question .comment-content {
				margin-bottom:0
			}
			.bui-btn-toggle {
				text-align:left;
				display:inline-block;
				border:0;
				padding-left:0;
				max-height:1rem;
				color:#39a4ff
			}
			.bui-btn-toggle:active {
				background:none;
				color:#39a4ff
			}
			.comment-reply {
				margin-left:.9rem;
				background:#f4f6f9;
				padding:.15rem;
				font-size:.22rem;
				color:#333
			}
			.comment-reply li {
				margin-bottom:.1rem
			}
			.comment-reply em {
				font-style:normal;
				color:#61749B
			}
		
		  .dropdown-comment  >.bui-btn {
		    padding: 0;
		    padding-left: .2rem;
		    padding-right: .2rem;
		    border: 0;
			}
		  .dropdown-comment  >.bui-btn:active,
		  .dropdown-comment  >.bui-btn.active {
		      background: none;
		  }
		  .dropdown-comment >.bui-list {
		    width: 2.1rem;
		    text-align: right;
		    top: -.1rem;
			}
		  .dropdown-comment >.bui-list .bui-btn {
		      display: inline-block;
		      background: #4c5154;
		      color: #fff;
		      margin: 0;
		      font-size: .22rem;
		      border: 0;
				}
		  .dropdown-comment >.bui-list .bui-btn:first-child {
		      border-radius: .1rem 0 0 .1rem;
		  }
		  .dropdown-comment >.bui-list .bui-btn:last-child {
		      border-radius: 0 .1rem .1rem 0;
		  }
		  .dropdown-comment >.bui-list .bui-btn:only-child {
		      border-radius: .1rem;
		  }
		  .dropdown-comment >.bui-list .bui-btn:active,
		  .dropdown-comment >.bui-list .bui-btn.active {
		      background: #4c5154;
		      color: #fff;
		    }
		  .dropdown-comment >.bui-list .bui-btn  img {
		        height: .4rem;
		    }
		  .dropdown-comment .icon-comment {
		        font-size: .4rem;
						color: #889dc4;
		    }
		
			.dialog-slide .bui-dialog-main,
			.bui-dialog-fullscreen {
				background: none;
			}
			.bui-slide-fullscreen {
				background: rgba(0,0,0,0.7);
			}
		
			.dialog-slide img {
		
			}
		
			.slide-img {
				width: 100%;
				height: 100%;
				background-position: center;
				background-size: contain;
				background-repeat: no-repeat;
			}
		
			.bui-bar .bui-bar-text {
				width: 2rem;
			}
		
		</style>
	</head>
<body>
<div class="bui-page">
 <header class="bui-bar">
        <div class="bui-bar-left">
            <a class="bui-btn" onclick="bui.back();"><i class="icon-back"></i></a>
        </div>
        <div class="bui-bar-main">
            <!-- 不修改文本值 -->
            <div id="barMain" class="bui-dropdown bui-arrow-center">
                <div class="bui-btn">
                    <span class="span1">动态</span>
                    <i class="icon-dropdown"></i>
                </div>
                <ul class="bui-list round">
                    <li class="bui-btn" value="22">我的周报</li>
                    <li class="bui-btn" value="11">成员周报</li>
                </ul>
            </div>
        </div>
       <div class="bui-bar-right bui-bar-text">
        	<a class="bui-btn"><i class="icon-camera"></i></a>
        </div>
    </header>
	<main>
		<!-- 搜索条控件结构 -->
		<div id="searchbar" class="bui-searchbar bui-box">
        <div class="span1">
   		  <div class="bui-input">
   				<i class="icon-search"></i>
   				<input type="search" value="" placeholder="请输入姓名" />
          </div>
        </div>
        <div class="btn-search">搜索</div>
		</div>
		<!-- 列表控件结构 -->
		<div id="scrollSearch" class="bui-scroll">
			<div class="bui-scroll-head"></div>
			<div class="bui-scroll-main comment-box">
				<ul class="bui-list">

				</ul>
			</div>
			<div class="bui-scroll-foot"></div>
		</div>
	</main>
	 <footer class="container-xy comment-post" style="display:none;">
        <div class="input-wrap">
            <div class="bui-input comment-input">
                <input type="text" value="" placeholder="说说你的看法">
                <div class="bui-btn">发表</div>
            </div>
        </div>
    </footer>
</div>

<script>

 bui.ready(function(){
   var pageview = {};

     // 模块初始化定义
     pageview.init = function () {
       var uiList = bui.list({
             id: "#scrollSearch",
             url: "/yq-work/webcall?action=WeeklyDao.myReceiveWeeklyList",
             data: { "pageType":'3'},
             field:{page:'pageIndex',data:'data',size:'pageSize'},
             method:'POST',
             page:1,
             pageSize:10,
             template: function (data) {
                 var html = "";
                 data.map(function(el, index) {
                     html +=`<li class="comment-line">
		                    	 <div class="bui-box">
		                         <div class="thumbnail"><img onerror="this.src='/yq-work/static/images/user-avatar-large.png'" src="${el.PIC_URL}" alt=""></div>
		                         <div class="span1">
		                             <div class="bui-box">
		                                 <div class="span1">
		                                     <h3 class="item-title">${el.DEPTS} / ${el.USERNAME}</h3>
		                                 </div>
		                             </div>
		                             <div class="item-text bui-box-text-hide comment-content">[${el.TITLE}] ${el.ITEM_1}</div>
		                             <div class="bui-btn-toggle">展开</div>
		                             <div class="bui-box">
		                                 <div class="span1">
		                                     <span class="time">${el.SEND_TIME}</span>
		                                 </div>
		                                 <div class="bui-dropdown dropdown-comment"  data-id="${el.WEEKLY_ID}">
		                                     <div class="bui-btn bui-box">
		                                         <div class="span1"><i class="icon-comment"></i></div>
		                                     </div>
		                                 </div>
		                             </div>
		                         </div>
		                     </div>
		                     <!-- 回复 -->
		                     <!--<ul class="comment-reply">
		                         <li>
		                             <em>王小o</em>回复<em>岑博</em>：好，一是对接，二是管理，
		                         </li>
		                         <li>
		                             <em>岑博</em>回复<em>邹龙明</em>：可以，这块是作为PAAS
		                         </li>
		                     </ul>-->
                     </li>`
                 });
                 return html;
             },
             onBeforeRefresh: function() {

             },
             onBeforeLoad: function() {

             },
             onRefresh: function() {

             },
             onLoad: function() {
            	 
               pageview.showMore();
               
		       var uiDropdown = bui.dropdown({
		           id: ".dropdown-comment",
		           data: [{
		               name: "点赞",
		               value: "点赞"
		           }, {
		               name: "评论",
		               value: "评论"
		           }],
		           position: "left",
		           change: false,
		           relative: false,
		           callback: function(e) {
		               var index = $(e.target).index();
		               switch (index ) {
		                 case 0:
		                	 bui.hint("点赞成功")
		                 break;
		                 case 1:
		                 	 pageview.showPost();
		                	 bui.init();
		                 	// 评论
		                 break;
		               }
		           }
		       });
		       
             }
         });

       var uiSearchbar = bui.searchbar({
           id:"#searchbar",
           onInput: function(e,keyword) {

           },
           onRemove: function(e,keyword) {
               $('.btn-search').click();
           },
           callback: function (e,keyword) {
               if(uiList){
                   uiList.empty();
                   uiList.init({
                       page: 1,
                       data: {
                           "userName":keyword
                       }
                   });
               }
           }
       });
       
       // 下拉菜单有遮罩的情况
       var uiMask = bui.mask({
           zIndex:9,
           appendTo: "main",
           callback: function (argument) {
               barMain.hide();
           }
       });

       //下拉菜单 在顶部中间
       var barMain = bui.dropdown({
           id: "#barMain",
           showArrow: true,
           relative: false,
           callback: function (argument) {
               uiMask.hide();
           }
       })
       // 通过监听事件绑定
       barMain.on("show",function () {
           uiMask.show();
       }).on("hide",function () {
           uiMask.hide();
       })
       
     }
     
     
 	 // 展开更多
     pageview.showMore = function() {
         $(".bui-btn-toggle").on("click", function() {
             var $target = $(this).prev(".comment-content");
             if ($target.hasClass("active")) {
                 $(this).text("展开")
             } else {
                 $(this).text("收起")
             }
             $target.toggleClass("active")
         })
     }

     pageview.showPost = function () {
       $(".comment-post").show();
     }

     pageview.hidePost = function () {
       $(".comment-post").hide();
     }
     
     pageview.init();

 })
</script>
</body>
</html>