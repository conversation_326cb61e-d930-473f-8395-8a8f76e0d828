<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>机房管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="fa fa-server"></span> 机房管理</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">机房名称</span>	
							 <input type="text" name="serverRoomName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 添加</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
  			{{if currentUserId == CREATOR}}<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
 			{{if currentUserId == CREATOR}}<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.del">删除</a>{{/if}}
			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.host">主机管理</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ServerRoomDao.list',
					autoFill:true,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'center'
					 },{
					    field: 'SERVICE_ROOM_NAME',
						title: '机房名称',
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.host'
					},{
					    field: 'ADDRESS',
						title: '所在地',
						align:'left'
					},{
					    field: 'OWNER',
						title: '负责人',
						align:'center',
						templet:function(row){
							return getUserName(row.OWNER);
						}
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center'
					},{
						title: '操作',
						align:'center',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/ops/serverroom-edit.jsp',title:'编辑',data:{serverRoomId:data.SERVER_ROOM_ID}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,area:['550px','400px'],url:'${ctxPath}/pages/ops/serverroom-edit.jsp',title:'新增主机'});
			},
			host:function(data){
				popup.openTab({url:'${ctxPath}/pages/ops/host-list.jsp',title:'主机',data:{serverRoomId:data.SERVER_ROOM_ID}});
			},
			del:function(data){
				layer.confirm("确认要删除吗?",{icon:3},function(){
					ajax.remoteCall("${ctxPath}/servlet/serverRoom?action=delete",{'serverRoom.SERVER_ROOM_ID':data.SERVER_ROOM_ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
					
				});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>