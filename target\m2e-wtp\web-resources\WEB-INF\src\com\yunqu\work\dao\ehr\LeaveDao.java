package com.yunqu.work.dao.ehr;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name="LeaveDao")
public class LeaveDao extends AppDaoContext {

@WebControl(name="annualLeaveRecord",type= Types.RECORD)
public JSONObject annualLeaveRecord(){
	String userId = param.getString("applyBy");
	if(StringUtils.isBlank(userId)) {
		userId = getUserId();
	}
    EasySQL sql = getEasySQL("select t2.*,t1.USERNAME from easi_user t1,yq_staff_info t2 where t1.user_id = t2.staff_user_id");
    sql.append(userId," and t2.staff_user_id = ?");
    setQuery(getMainQuery());
    return queryForRecord(sql.getSQL(), sql.getParams());
}

@WebControl(name="annualLeaveApplyList",type= Types.LIST)
public JSONObject annualLeaveApplyList(){
	String applyBy = param.getString("applyBy");
	if(StringUtils.isBlank(applyBy)) {
		applyBy = getUserId();
	}
    String staffNo = StaffService.getService().getStaffInfo(applyBy).getStaffNo();
    String data14 = "年休假";
    String startDate = EasyCalendar.newInstance().getYear()+"-01-01 00:00";
    String endDate = EasyCalendar.newInstance().getYear()+"-12-31 23:59";
    EasySQL sql = getEasySQL("select * from yq_flow_apply t1 where 1=1 ");
    sql.append(staffNo," and t1.apply_staff_no = ?");
    sql.append(data14,"and data14 = ?");
    sql.append(startDate, " and data11 >= ?");
    sql.append(endDate, " and data12 <= ?");
    sql.append("and apply_state in (10,20,30)");
    sql.append("ORDER BY DATA11");
    return queryForList(sql.getSQL(), sql.getParams());
}

@WebControl(name="annualLeaveApplySum",type= Types.LIST)
public JSONObject annualLeaveApplySum(){
	String applyBy = param.getString("applyBy");
	if(StringUtils.isBlank(applyBy)) {
		applyBy = getUserId();
	}
    String staffNo = StaffService.getService().getStaffInfo(applyBy).getStaffNo();
    String startDate = EasyCalendar.newInstance().getYear()+"-01-01 00:00";
    String endDate = EasyCalendar.newInstance().getYear()+"-12-31 23:59";
    EasySQL sql = getEasySQL("SELECT t1.*, t4.USERNAME, COALESCE(t3.totalDays,0) as TOTAL_DAYS");
    sql.append("from yq_staff_info t1 ");
    sql.append("LEFT JOIN (");
    sql.append("SELECT apply_staff_no,sum(data13) as totalDays  from  yq_work.yq_flow_apply t2  WHERE t2.data14 = '年休假' and t2.apply_state in (10,20,30) ");
    sql.append(startDate, " and t2.data11 >= ?");
    sql.append(endDate, " and t2.data12 <= ?");
    sql.append(" GROUP BY t2.apply_staff_no ) t3 on t1.staff_no = t3.apply_staff_no ");
    sql.append("left JOIN easi_user t4 on t1.staff_user_id = t4.USER_ID");
    sql.append(" WHERE t1.account_state = 0");
    sql.appendLike(param.getString("staffName")," and t4.USERNAME like ?");
    sql.appendLike(staffNo," and t1.staff_no like ?");
    sql.append("ORDER BY t1.staff_no desc");
    setQuery(getMainQuery());
    return queryForList(sql.getSQL(), sql.getParams());
}

@WebControl(name="annualLeaveList",type= Types.LIST)
public JSONObject annualLeaveList(){
    String startDate = EasyCalendar.newInstance().getYear()+"-01-01 00:00";
    String endDate = EasyCalendar.newInstance().getYear()+"-12-31 23:59";
    EasySQL sql = getEasySQL("SELECT t1.*, t4.USERNAME, COALESCE(t3.totalDays,0) as TOTAL_DAYS");
    sql.append("from yq_staff_info t1 ");
    sql.append("LEFT JOIN (");
    sql.append("SELECT apply_staff_no,sum(data13) as totalDays  from  yq_work.yq_flow_apply t2  WHERE t2.data14 = '年休假' and t2.apply_state in (10,20,30) ");
    sql.append(startDate, " and t2.data11 >= ?");
    sql.append(endDate, " and t2.data12 <= ?");
    sql.append(" GROUP BY t2.apply_staff_no ) t3 on t1.staff_no = t3.apply_staff_no ");
    sql.append("left JOIN easi_user t4 on t1.staff_user_id = t4.USER_ID");
    sql.append(" WHERE t1.account_state = 0");
    sql.appendLike(param.getString("staffName")," and t4.USERNAME like ?");
    sql.appendLike(param.getString("staffNo")," and t1.staff_no like ?");
    sql.append("ORDER BY t1.staff_no desc");
    setQuery(getMainQuery());
    return queryForList(sql.getSQL(), sql.getParams());
}



}
