<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>客户管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="CustContactsMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		 	<div class="input-group input-group-sm ml-15">
							 <span class="input-group-addon">跟进时间</span>	
							 <input type="text" name="beginDate" data-mars="CommonDao.monthBegin" data-mars-top="true" data-mars-reload="false" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
							 <input type="text" name="endDate"  data-mars="WeeklyDao.getCurrentWeekEndDate" data-mars-top="true" data-mars-reload="false" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
				    	</div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">姓名</span>
						 	<input class="form-control input-sm" name="username">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">部门</span>
						 	<input class="form-control input-sm" name="deptName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="CustContactsMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="CustContactsMgrTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var CustContactsMgr={
			init:function(){
				$("#CustContactsMgrForm").initTable({
					mars:'CustFollowDao.personWeeklyStat',
					id:'CustContactsMgrTable',
					autoSort:false,
					page:false,
					height:'full-120',
					limit:15,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'DEPTS',
						title: '所属部门',
						align:'left',
						width:140,
					},{
					    field: 'USERNAME',
						title: '填写人',
						align:'left',
						width:90,
					},{
					    field: 'FOLLOW_COUNT',
						title: '跟进次数',
						align:'left',
						width:90
					},{
					    field: 'TOTAL_FOLLOW_DAY',
						title: '消耗总天数',
						align:'center',
						width:100
					},{
						field:'SJ_COUNT',
						title:'商机数量',
						width:100
					},{
					    field: 'SJ_NAME',
						title: '跟进商机'
					},{
						field:'',
						title:'操作',
						width:100,
						templet:'<div><a href="javasript:;" onclick="contactsQuery(\'{{d.USER_ID}}\')">填写记录</a></div>'
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#CustContactsMgrForm").queryData({id:'CustContactsMgrTable',jumpOne:true,page:false});
			}
		}
		
		function contactsQuery(id){
			popup.openTab({id:'custContactQuery',title:'联系记录',url:'${ctxPath}/pages/crm/contacts/contact-query.jsp',data:{userId:id}});	
		}
		
		$(function(){
			$("#CustContactsMgrForm").render({success:function(){
				CustContactsMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>