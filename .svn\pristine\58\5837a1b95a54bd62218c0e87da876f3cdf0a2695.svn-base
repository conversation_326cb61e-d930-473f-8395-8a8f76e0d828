package com.yunqu.work.servlet.flow;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.work.base.BaseFlowServlet;

@WebServlet("/servlet/flow/node")
public class NodeServlet extends BaseFlowServlet {

	public EasyResult actionForUpdateNodeConf(){
		EasyRecord model = new EasyRecord("yq_flow_approve_node","node_id");
		try {
			model.setColumns(getJSONObject("conf"));
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForAddNodeConf(){
		EasyRecord model = new EasyRecord("yq_flow_approve_node","node_id");
		try {
			model.setColumns(getJSONObject("conf"));
			model.set("create_time", EasyDate.getCurrentDateString());
			model.remove("NODE_ID");
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelNodeConf() {
		String nodeId  = getJsonPara("nodeId");
		try {
			this.getQuery().executeUpdate("update yq_flow_approve_node p_node_id = null where p_node_id = ?", nodeId);
			this.getQuery().executeUpdate("update yq_flow_approve_node c_node_id = null where c_node_id = ?", nodeId);
			this.getQuery().executeUpdate("delete from yq_flow_approve_node where NODE_ID = ?", nodeId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
}
