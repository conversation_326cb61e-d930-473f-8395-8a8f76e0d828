package com.yunqu.work.servlet.crm;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/contacts/*")
public class ContactsServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_crm_contacts","contacts_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	public EasyResult actionForAdd(){
		EasyRecord model = getModel("contacts");
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("contacts");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDel(){
		try {
			String contactsId = getJsonPara("contactsId") ;
			String sql = "select count(1) from yq_crm_follow where contacts_id = ?";
			boolean bl = this.getQuery().queryForExist(sql, contactsId);
			if(bl) {
				return EasyResult.fail("联系人已被联系记录管理,不能删除");
			}
			this.getQuery().executeUpdate("delete from yq_crm_contacts where contacts_id = ?", contactsId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	

}
