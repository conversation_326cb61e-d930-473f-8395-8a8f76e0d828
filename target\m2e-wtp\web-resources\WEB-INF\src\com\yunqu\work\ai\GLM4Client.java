package com.yunqu.work.ai;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.safety.Whitelist;
import org.jsoup.Jsoup;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.utils.LogUtils;

public class GLM4Client {
	
    public static String request(String systemSay,String userSay) {
        String glm4ApiUrl = "https://open.bigmodel.cn/api/paas/v4/chat/completions";
        String apiKey = "f9a0321adeb5f0834f490d6834e7e08d.MQNtlfTIjAcOezSj";

        userSay = Jsoup.clean(userSay, Whitelist.none());
        
        JSONObject params = new JSONObject();
        params.put("type", "function");
        params.put("model", "glm-4-plus");
        params.put("stream", false);
        
        JSONArray array = new JSONArray();
        JSONObject say = new JSONObject();
        say.put("role", "user");
        say.put("content", userSay);
        array.add(say);
        
        JSONObject systemSayJson = new JSONObject();
        systemSayJson.put("role", "system");
        systemSayJson.put("content",systemSay);
        array.add(systemSayJson);
        
        params.put("messages", array);
        
        String result = null;
        
        try {
        	String requestBody = params.toJSONString();
        	Connection connection  = Jsoup.connect(glm4ApiUrl)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + apiKey)
                .requestBody(requestBody);

        	LogUtils.getLLM().info(requestBody);
        	Response response = connection.ignoreContentType(true).timeout(600000).method(Method.POST).execute();
			if(response.statusCode()==200) {
				String resultStr = response.body();
				LogUtils.getLLM().info(resultStr);
				JSONObject resultJson = JSONObject.parseObject(resultStr);
				JSONArray choices = resultJson.getJSONArray("choices");
				JSONObject choicesOne = choices.getJSONObject(0);
				JSONObject message = choicesOne.getJSONObject("message");
				String  markdown = message.getString("content");
				Parser parser = Parser.builder().build();
		        HtmlRenderer renderer = HtmlRenderer.builder().build();
		        Node document = parser.parse(markdown);
		        result =  renderer.render(document);
			}else {
				return null;
			}
        } catch (Exception e) {
        	LogUtils.getLLM().error(e.getMessage(),e);
        }
		return result;
    }
    
    public static void main(String[] args) {
		System.out.println(request("写代码", "用户管理"));
	}
	
}
