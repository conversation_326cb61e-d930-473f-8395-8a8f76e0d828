package com.yunqu.work.dao.devops;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.ServerRoomModel;

@WebObject(name="ServerRoomDao")
public class ServerRoomDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		ServerRoomModel model=new ServerRoomModel();
		model.setColumns(getParam("serverRoom"));
		return queryForRecord(model);
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		EasySQL sql=getEasySQL("select server_room_id,SERVICE_ROOM_NAME from yq_ops_serverroom where 1=1");
		sql.append(param.getString("projectId"),"and project_id = ?");
		return getDictByQuery(sql.getSQL(),sql.getParams());
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from yq_ops_serverroom where 1=1");
		sql.append(param.getString("projectId"),"and project_id = ?");
		sql.appendLike(param.getString("serverRoomName"),"and service_room_name like ?");
		if(!isSuperUser()&&!getUserPrincipal().isResource("SERVER_ROOM_MANAGER")){
			sql.append(getUserPrincipal().getUserId(),"and creator = ?");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
