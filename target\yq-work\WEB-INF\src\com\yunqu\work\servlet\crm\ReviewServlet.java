package com.yunqu.work.servlet.crm;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.ApproveResultRecord;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.FlowService;

@WebServlet("/servlet/contract/review")
public class ReviewServlet extends BaseFlowServlet {

	private static final long serialVersionUID = 1L;

	public EasyResult actionForUpdatePurchase() {
		JSONObject params = getJSONObject();
		String businessId = params.getString("businessId");
		try {
			this.getQuery().executeUpdate("delete from yq_contract_purchase where business_id = ?", businessId);
			JSONArray array = params.getJSONArray("array");
			for(int i=0,len=array.size();i<len;i++) {
				EasyRecord record = new EasyRecord("yq_contract_purchase");
				record.setColumns(array.getJSONObject(i));
				record.set("update_time",EasyDate.getCurrentDateString());
				record.set("create_name",getUserName());
				record.set("business_id",businessId);
				this.getQuery().save(record);
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForDelNode() {
		JSONObject params = getJSONObject();
		String nodeId = params.getString("nodeId");
		try {
			this.getQuery().executeUpdate("delete from yq_contract_review_node_conf where node_id = ? ", nodeId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForCheckReturn() {
		try {
			EasyQuery query = this.getQuery();
			JSONObject params = getJSONObject();
			String resultId = params.getString("resultId");
			String msgContent = params.getString("flowContent");
			EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
			record.set("result_id", resultId);
			record.set("check_result", 0);
			record.set("check_desc",getUserName()+"："+ msgContent);
			query.update(record);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateApproveDesc() {
		JSONObject params = getJSONObject();
		String resultId = params.getString("resultId");
		String flowContent = getUserName()+"："+params.getString("flowContent");
		try {
			this.getQuery().executeUpdate("update yq_flow_approve_result set check_desc = ? where result_id = ? ", flowContent,resultId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddNodeConf(){
		EasyRecord model = new EasyRecord("yq_contract_review_node_conf","NODE_ID");
		model.setColumns(getJSONObject("conf"));
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		String pNodeId = model.getString("P_NODE_ID");
		try {
			if("0".equals(pNodeId)) {
				model.setPrimaryValues(this.getQuery().queryForInt("select max(node_id) from yq_contract_review_node_conf where length(node_id) = 3")+1);
			}else {
				model.setPrimaryValues(this.getQuery().queryForInt("select max(node_id) from yq_contract_review_node_conf where length(node_id) = 4")+1);
				model.set("child_count",this.getQuery().queryForInt("select count(1) from yq_contract_review_node_conf where  p_node_id = ?",pNodeId)+1);
			}
			this.getQuery().save(model);
			return EasyResult.ok(model.getPrimaryValue());
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	public EasyResult actionForUpdateNodeConf(){
		EasyRecord model = new EasyRecord("yq_contract_review_node_conf","node_id");
		model.setColumns(getJSONObject("conf"));
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		try {
			model.set("child_count",this.getQuery().queryForInt("select count(1) from yq_contract_review_node_conf where  p_node_id = ?",model.getString("NODE_ID")));
			this.getQuery().update(model);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForUpdateNode() {
		JSONObject params = getJSONObject();
		String businessId = params.getString("businessId");
		String removeNodeId = params.getString("removeNodeId");
		String addNodeId = params.getString("addNodeId");
	    try {
	    	if(StringUtils.notBlank(removeNodeId)) {
	    		EasySQL sql = new EasySQL("delete from yq_flow_approve_result where 1=1 and check_result = 0");
	    		sql.append(businessId, "and business_id = ?");
	    		sql.appendIn(removeNodeId.split(","), "and data3");
	    		this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
	    	}
			if(StringUtils.notBlank(addNodeId)) {
				String[] nodeIds = addNodeId.split(",");
				
				FlowApplyModel apply = ApproveService.getService().getApply(businessId);
				ApproveNodeModel nodeInfo = ApproveService.getService().getStartNode(apply,params);
				
				for(String nodeId:nodeIds) {
					JSONObject row = this.getQuery().queryForRow("select * from yq_contract_review_node_conf where node_id = ?", new Object[] {nodeId},new JSONMapperImpl());
					String firstCheckIds= row.getString("FIRST_CHECK_IDS");
					String lastCheckIds= row.getString("LAST_CHECK_IDS");
					String firstCheckNames= row.getString("FIRST_CHECK_NAMES");
					String lastCheckNames= row.getString("LAST_CHECK_NAMES");
					
					int level = 1;
					if(StringUtils.isBlank(firstCheckIds)) {
						firstCheckIds = lastCheckIds;
						firstCheckNames = lastCheckNames;
						level = 2;
					}
					
					String nextResultId = RandomKit.uniqueStr();
					
					ApproveNodeModel _nodeInfo = new ApproveNodeModel();
					_nodeInfo.setCheckBy(firstCheckIds);
					_nodeInfo.setCheckName(firstCheckNames);
					_nodeInfo.setNodeId(nodeInfo.getNodeId());
//					_nodeInfo.setNodeName(nodeInfo.getNodeName());
					
					_nodeInfo.setNodeName(row.getString("NODE_NAME")+(level==1?"初审":"终审"));
					
					ApproveResultRecord nextResult = getApproveResultRecord(_nodeInfo,businessId,nextResultId,"");
					nextResult.set("data1", 1);
					nextResult.set("data2", level);
					nextResult.set("data3", row.getString("NODE_ID"));
					nextResult.set("data4", row.getString("NODE_NAME"));
					this.saveApproveNode(nextResult,apply.getFlowCode());
//					this.getQuery().save(nextResult);
				}
			}
		} catch (Exception e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
		
	}


	public EasyResult actionForDoApprove() {
		EasyQuery query = this.getQuery();
		String msg = "操作成功";
		try {
			query.begin();
			JSONObject params = getJSONObject();
			String flowCode = params.getString("flowCode");
			String businessId = params.getString("businessId");
			String resultId = params.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("审批人不存在");
			}
			
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);
			
			MessageModel msgModel = getMessageModel(apply, null, "待审批", apply.getApplyRemark());
			
			//1 通过 2拒绝
			int checkResult = params.getIntValue("checkResult");
			String checkDesc = params.getString("checkDesc");
			
			String type = result.getData1();
			String level = result.getData2();
			//审批小组 生成终审
			if("1".equals(type)) {
				EasyRecord record = getApproveResultRecord(resultId,checkResult,checkDesc);
				query.update(record);
				
				if(checkResult==1) {
					//没通过不生成终审
					if("1".equals(level)) {
						String nextResultId = RandomKit.uniqueStr();
						ApproveNodeModel _nodeInfo = new ApproveNodeModel();
						JSONObject row = this.getQuery().queryForRow("select * from yq_contract_review_node_conf where node_id = ?", new Object[] {result.getData3()},new JSONMapperImpl());
						_nodeInfo.setCheckBy(row.getString("LAST_CHECK_IDS"));
						_nodeInfo.setCheckName(row.getString("LAST_CHECK_NAMES"));
						_nodeInfo.setNodeId(result.getNodeId());
//						_nodeInfo.setNodeName(result.getNodeName());
						_nodeInfo.setNodeName(row.getString("NODE_NAME")+"终审");
						
						ApproveResultRecord nextResult = getApproveResultRecord(_nodeInfo,businessId,nextResultId,resultId);
						nextResult.set("data1", 1);
						nextResult.set("data2", 2);
						nextResult.set("data3", row.getString("NODE_ID"));
						nextResult.set("data4", row.getString("NODE_NAME"));
						boolean hasTrsut = this.saveApproveNode(nextResult,apply.getFlowCode(),query);
//						query.save(nextResult);
						if(hasTrsut) {
							msgModel.setReceiver(nextResult.getCheckUserId());
						}else {
							msgModel.setReceiver(_nodeInfo.getCheckBy());
						}
						
						
						EasySQL sql = new EasySQL("update yq_flow_apply set ");
						sql.append(resultId,"current_result_id = ?");
						sql.append(FlowConstants.FLOW_STAT_CHECKING,",apply_state = ?");
						sql.append(nextResultId,",next_result_id = ?");
						sql.append(businessId,"where apply_id = ?");
						query.executeUpdate(sql.getSQL(),sql.getParams());
					}else {
						EasySQL sql = new EasySQL("update yq_flow_apply set ");
						sql.append(resultId,"current_result_id = ?");
						sql.append(FlowConstants.FLOW_STAT_CHECKING,",apply_state = ?");
						sql.append(businessId,"where apply_id = ?");
						query.executeUpdate(sql.getSQL(),sql.getParams());
						
						int todoCount  = query.queryForInt("select count(1) from yq_flow_approve_result where business_id = ? and data1 = 1 and check_result = 0 and result_id <> ?", businessId,resultId);
						if(todoCount==0&&checkResult==1) {
							int failCount  = query.queryForInt("select count(1) from yq_flow_approve_result where business_id = ? and data1 = 1 and check_result = 2 and result_id <> ?", businessId,resultId);
							if(failCount==0) {
								ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
								String nodeId = approveNode.getNodeId();
								String nextNodeId = approveNode.getNextNodeId();
								
								ApproveNodeModel nextNodeInfo = ApproveService.getService().getNodeInfoByNodeId(nextNodeId,apply,params);
								if("0".equals(nextNodeInfo.getNextNodeId())) {
									nextNodeInfo.setCheckBy(apply.getData(1));
									nextNodeInfo.setCheckName(apply.getData(2));
								}
								
								String nextResultId = RandomKit.uniqueStr();
								ApproveResultRecord resultRecord = getApproveResultRecord(nextNodeInfo, businessId, nextResultId,resultId);
								resultRecord.set("data3",9999);
//								query.save(resultRecord);
								boolean hasTrust = this.saveApproveNode(resultRecord, apply.getFlowCode(),query);
								
								
								msg = "提交成功,审批下一个节点:"+nextNodeInfo.getNodeName();
								FlowService.getService().updateApplyInfo(query,businessId, resultId,nextResultId,nodeId,nextNodeId, 20);
								
								if(hasTrust) {
									msgModel.setReceiver(resultRecord.getCheckUserId());
								}else {
									msgModel.setReceiver(nextNodeInfo.getCheckBy());
								}
							}
						}
					}
				}
			}else {
				
				//如果评审小组都审批完成 > 获取下一级节点
				ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
				String nodeId = approveNode.getNodeId();
				String nextNodeId = approveNode.getNextNodeId();
			
				EasyRecord record = getApproveResultRecord(resultId,checkResult,checkDesc);
				query.update(record);
				
				if(checkResult==2) {
					FlowService.getService().updateApplyReturn(businessId);
					msgModel.setData4("审批退回");
					msgModel.setMsgLabel("审批退回");
					msgModel.setDesc("审批退回："+checkDesc);
					msgModel.setReceiver(apply.getApplyBy());
				}else {
					if("0".equals(nextNodeId)) {//完成了
						msg = "审批完成,流程结束";
						msgModel.setMsgLabel("审批完成");
						msgModel.setReceiver(apply.getApplyBy());
						FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
					}else {
						msgModel.setMsgLabel("请尽快审批");
						ApproveNodeModel nextNodeInfo = ApproveService.getService().getNodeInfoByNodeId(nextNodeId,apply,params);
						if(nextNodeInfo==null) {
							return EasyResult.fail("获取不到下一级审批,请联系管理员");
						}
						if("0".equals(nextNodeInfo.getNextNodeId())) {
							nextNodeInfo.setCheckBy(apply.getData(1));
							nextNodeInfo.setCheckName(apply.getData(2));
						}
						
						msgModel.setReceiver(nextNodeInfo.getCheckBy());
						
						String nextResultId = RandomKit.uniqueStr();
						EasyRecord resultRecord = getApproveResultRecord(nextNodeInfo, businessId, nextResultId,resultId);
						resultRecord.set("data3",9999);
						query.save(resultRecord);
						msg = "提交成功,审批下一个节点:"+nextNodeInfo.getNodeName();
						
						FlowService.getService().updateApplyInfo(query,businessId, resultId,nextResultId,nodeId,nextNodeId, 20);
					}
				}
			}
			this.sendAllMsg(msgModel);
			query.commit();
		} catch (SQLException e) {
			this.error(null, e);
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(null, ex);
			}
			return EasyResult.fail(e.getMessage());
			
		}
		return EasyResult.ok(null,msg);
	}
	
	
	
	@Override
	protected String startFlow(String businessId, int applyState, JSONObject params)throws SQLException {
		String nodeIds = params.getString("nodeIds");
		
		String nextResultId = RandomKit.uniqueStr();
		
		EasyRecord resultRecord = getApproveResultRecord(businessId);
		if(applyState==21) {
			resultRecord.set("node_name","修改申请");
		}else {
			resultRecord.set("node_name","申请填写");
		}
		this.getQuery().save(resultRecord);
		String nowResultId = resultRecord.getPrimaryValue().toString();
		
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		ApproveNodeModel nodeInfo = ApproveService.getService().getStartNode(apply,params);
		if(nodeInfo==null) {
			return null;
		}
		
		EasySQL sql = new EasySQL("select * from yq_contract_review_node_conf where 1=1");
		sql.appendIn(nodeIds.split(","),"and node_id");
		List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		
		int i = 0;
		for(JSONObject row:list) {
			String firstCheckIds= row.getString("FIRST_CHECK_IDS");
			String lastCheckIds= row.getString("LAST_CHECK_IDS");
			String firstCheckNames= row.getString("FIRST_CHECK_NAMES");
			String lastCheckNames= row.getString("LAST_CHECK_NAMES");
			
			int level = 1;
			if(StringUtils.isBlank(firstCheckIds)) {
				firstCheckIds = lastCheckIds;
				firstCheckNames = lastCheckNames;
				level = 2;
			}
			ApproveNodeModel _nodeInfo = new ApproveNodeModel();
			_nodeInfo.setCheckBy(firstCheckIds);
			_nodeInfo.setCheckName(firstCheckNames);
			_nodeInfo.setNodeId(nodeInfo.getNodeId());
			
			_nodeInfo.setNodeName(row.getString("NODE_NAME")+(level==1?"初审":"终审"));
			
			ApproveResultRecord nextResult = getApproveResultRecord(_nodeInfo,businessId,nextResultId,nowResultId);
			//指定是审批小组
			nextResult.set("data1", 1);
			//指定是初审还是终审
			nextResult.set("data2", level);
			//设置NodeId
			nextResult.set("data3", row.getString("NODE_ID"));
			//设置NodeName
			nextResult.set("data4", row.getString("NODE_NAME"));
			
			if(i>0) {
				nextResult.setPrimaryValues(nextResultId+i);
			}
			this.saveApproveNode(nextResult,apply.getFlowCode());
//			this.getQuery().save(nextResult);
			i++;
		}
		
		FlowService.getService().updateApplyBegin(businessId, nodeInfo.getNodeId(),nowResultId,nextResultId);
		
		return nodeInfo.getNodeName();
	}

	@Override
	protected boolean setFlowApplyData(String businessId,EasyRecord applyRecord, JSONObject params) {
		return true;
	}

	@Override
	protected boolean setFlowBusiData(String businessId, EasyRecord busiRecord, JSONObject params) {
		return true;
	}
	
	public EasyResult actionForSubmitApply() {
		return submitApply();
	}


	public EasyResult actionForUpdateApply() {
		return updateApply();
	}
	
	
}
