<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
	<table class="layui-hide" id="paymentTable"></table>

	<script type="text/javascript">
	
		function loadPayment(){
			$("#SupplierDetailForm").initTable({
				mars:'SupplierDao.paymentList',
				id:'paymentTable',
				page:true,
				height:'full-250',
				cols: [[
				 {
					 field:'PAYMENT_NO',
					 title:'单据编号',
					 event:'paymentDetail',
					 width:120,
					 style:'text-decoration:underline;',
					 templet:'<div><a href="javascript:;">{{d.PAYMENT_NO}}</a></div>'
				 },{
					field:'TOTAL_PRICE',
					title:'含税总价'
				},{
				    field: 'APPLY_TIME',
					title: '操作时间',
					align:'center',
					width:130,
					templet:function(row){
						var time= row['APPLY_TIME'];
						return cutText(time,19,'');
					}
				},{
				    field: 'CREATE_NAME',
					title: '创建人',
					align:'center',
					width:70
				}
			]],done:function(data){
				 $("#paymentCount").text(data['totalRow']);
		  }});
		}
		
		$(function(){
			loadPayment();
		});
		
		function paymentDetail(data){
			popup.openTab({id:'paymentDetail',title:'付款申请详情',url:'${ctxPath}/pages/erp/payment/payment-detail.jsp',data:{paymentId:data.PAYMENT_ID}});
		}
</script>
