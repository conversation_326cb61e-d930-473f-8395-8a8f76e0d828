<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>合同审批</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}的合同评审"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">合同类型</td>
					  			<td>
					  				<select data-rules="required" class="form-control input-sm" name="apply.data1">
					  					<option value="">请选择</option>
					  					<option value="人力行政相关">人力行政相关</option>
					  					<option value="资质申报">资质申报</option>
					  					<option value="咨询服务">咨询服务</option>
					  					<option value="知识产权">知识产权</option>
					  					<option value="品牌宣传">品牌宣传</option>
					  					<option value="其他">其他</option>
					  				</select>
					  			</td>
					  			<td class="required">合同额(含税)</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">用章类型</td>
					  			<td>
					  				<input data-rules="required" class="form-control input-sm" readonly="readonly" data-text="广州云趣公章,北京云趣公章,合同专用章,中融合同章,法人章,中融公章"  onclick="selectText(this)" name="apply.data3">
					  			</td>
					  			<td class="required">合同份数</td>
					  			<td>
					  				<input type="number" data-rules="required" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">合同概况</td>
					  			<td colspan="3">
									<textarea style="height: 80px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>请上传合同</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({fileCheck:true,success:function(data){
				Flow.initData();
				
				
				var val = $("input[name='apply.data2']").val();
				if(val){
					$("span[name='apply.data2']").text(formatMoney(val));
				}
				
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.initData = function(){
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var type = applyInfo.data3;;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='总裁'){
				var checkBys = {'法人章':'杨曼丽','人力资源章':'庞小翠','广州云趣公章':'庞小翠','北京云趣公章':'朱栩','财务专用章':'杨曼丽','合同专用章':'杨曼丽','中融合同章':'杨曼丽','工程专用章':'李千帆','中融公章':'庞小翠'};
				var types = type.split(',');
				var selectUserNames = [];
				for(var index in types){
					selectUserNames.push(checkBys[types[index]]);
				}
				selectUserNames = arrayUnique(selectUserNames);
				params['selectUserName'] = selectUserNames.join();
			}
			FlowCore.approveData = params;
			
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({data:FlowCore.approveData});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>