package com.yunqu.work.servlet.wx;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.ServletException;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.StrKit;
import com.jfinal.weixin.sdk.api.ApiConfigKit;
import com.jfinal.weixin.sdk.api.ApiResult;
import com.jfinal.weixin.sdk.api.CustomServiceApi;
import com.jfinal.weixin.sdk.api.MenuApi;
import com.jfinal.weixin.sdk.api.SnsAccessToken;
import com.jfinal.weixin.sdk.api.SnsAccessTokenApi;
import com.jfinal.weixin.sdk.api.SubscribeMsgApi;
import com.jfinal.weixin.sdk.api.TagApi;
import com.jfinal.weixin.sdk.api.TemplateData;
import com.jfinal.weixin.sdk.api.TemplateMsgApi;
import com.jfinal.weixin.sdk.api.UserApi;
import com.jfinal.weixin.sdk.jfinal.ApiController;

public class WeixinApiController extends ApiController {

	public void index() {
		renderHtml("index");
	}
	public void sendSubscribeMsg(){
		String openId=getPara("openId");
		String text=getPara("text");
		String title=getPara("title");
		ApiResult apiResult = SubscribeMsgApi.subscribe(openId, "sH3YSOruJNXGHpxVCCZVQXHCLO-F31tTi7hfE4AFh0Y","http://work.yunqu-info.cn", 100, title, text, "#fe7300");
		renderJson(apiResult.getJson());
	}
	public void sendTemplateMsg(){
		String first=getPara("first");
		String url=getPara("url");
		String remark=getPara("remark");
		String templateId=getPara("templateId");
		String openId=getPara("openId");
		String keyword1=getPara("keyword1");
		String keyword2=getPara("keyword2");
		String keyword3=getPara("keyword3");
		String keyword4=getPara("keyword4");
		
		TemplateData templateData=TemplateData.New();
		templateData.setTouser(openId);
		templateData.setTemplate_id(templateId);
		templateData.setUrl(url);
		templateData.add("first",first, "#4183c4");
		templateData.add("keyword1", keyword1, "#444");
		templateData.add("keyword2", keyword2, "#999");
		if(StrKit.notBlank(keyword3)){
			templateData.add("keyword3", keyword3, "#999");
		}
		if(StrKit.notBlank(keyword4)){
			templateData.add("keyword4", keyword4, "#999");
		}
		templateData.add("remark", remark, "#fe7300");
		ApiResult apiResult = TemplateMsgApi.send(templateData.build());
		renderText(apiResult.getJson());
	}
	/**
	 * 获取公众号菜单
	 */
	public void getMenu() {
		ApiResult apiResult = MenuApi.getMenu();
		if (apiResult.isSucceed())
			renderText(apiResult.getJson());
		else
			renderText(apiResult.getErrorMsg());
	}
	public void getDelMenu() {
		ApiResult apiResult = MenuApi.deleteMenu();;
		if (apiResult.isSucceed())
			renderText(apiResult.getJson());
		else
			renderText(apiResult.getErrorMsg());
	}
	public void addMenu() {
		String jsonStr = "{\n" +
				"    \"button\": [\n" +
				"        {\n" +
				"\t    \"name\": \"云趣工作协作平台\",\n" +
				"\t    \"url\": \"http://work.yunqu-info.cn/yq-work/api/goUrl?url=http://work.yunqu-info.cn/workbench/index\",\n" +
				"\t    \"type\": \"view\"\n" +
				"        }\n" +
				"    ]\n" +
				"}";
		jsonStr = "{\"button\":[{\"type\":\"view\",\"name\":\"通讯录\",\"url\":\"http://work.yunqu-info.cn/yq-work/api/goUrl?url=http://work.yunqu-info.cn/yq-work/pages/ehr/address-list.html\"},{\"name\":\"协同\",\"sub_button\":[{\"type\":\"view\",\"name\":\"周报\",\"url\":\"http://work.yunqu-info.cn/yq-work/api/goUrl?url=http://work.yunqu-info.cn/yq-work/pages/weekly/weekly-received.html\"},{\"type\":\"view\",\"name\":\"任务\",\"url\":\"http://work.yunqu-info.cn/yq-work/api/goUrl?url=http://work.yunqu-info.cn/yq-work/pages/task/my-task-list.jsp?status=1\"},{\"type\":\"view\",\"name\":\"知识库\",\"url\":\"http://work.yunqu-info.cn/yq-work/api/goUrl?url=http://work.yunqu-info.cn/yq-work/pages/doc/folder-ext.jsp?source=rules\"}]},{\"type\":\"view\",\"name\":\"首页\",\"url\":\"http://work.yunqu-info.cn/yq-work/api/goUrl?url=http://work.yunqu-info.cn/workbench/index\"}],\"matchrule\":{\"tag_id\":\"2\",\"sex\":\"1\",\"country\":\"中国\",\"province\":\"广东\",\"city\":\"广州\",\"client_platform_type\":\"2\",\"language\":\"zh_CN\"}}";
		String menuStr = getPara("menuStr");
		if(StringUtils.notBlank(menuStr)) {
			jsonStr =  menuStr;
		}
		ApiResult apiResult = MenuApi.createMenu(jsonStr);;
		if (apiResult.isSucceed())
			renderText(apiResult.getJson());
		else
			renderText(apiResult.getErrorMsg());
	}
	
	public void authCallback(){
		String code = getPara("code");
		String userId = getPara("userId");
		SnsAccessToken ansAccessToken= SnsAccessTokenApi.getSnsAccessToken(ApiConfigKit.getAppId(), ApiConfigKit.getApiConfig().getAppSecret(), code);
		ApiResult apiResult = UserApi.getUserInfo(ansAccessToken.getOpenid());
		info(ansAccessToken.getJson());
		Object subscribe=apiResult.get("subscribe");
		if(subscribe!=null&&apiResult.getInt("subscribe")==0){
			renderHtml("<p style='font-size:20px;text-align:center'>请先关注服务号。</p><div style='text-align:center'><img style='width:250px;height:250px;' src='http://work.yunqu-info.cn/qrcode_for_gh_562078549f9f_258.jpg'/></div>");
			return;
		}
		
		String openId=apiResult.getStr("openid");
		String nickname=apiResult.getStr("nickname");
		String headimgUrl=apiResult.getStr("headimgurl");
		headimgUrl=headimgUrl.replaceAll("\\/", "/");
		
		if(StrKit.isBlank(userId)){
			 renderText("userId not empty.");
			 return;
		}
		
		try {
			ServerContext.getAdminQuery().executeUpdate("update easi_user set OPEN_ID = ?,PIC_URL = ?,NIKE_NAME = ? where user_id = ?",openId,headimgUrl,nickname,userId);
			UserApi.updateRemark(openId, ServerContext.getAdminQuery().queryForString("select concat(depts,'/',username) from easi_user where user_id = ?", userId));
		} catch (SQLException e) {
			error(e.getMessage(),e);
			try {
				ServerContext.getAdminQuery().executeUpdate("update easi_user set OPEN_ID = ?,PIC_URL = ? where user_id = ?",openId,headimgUrl,userId);
			} catch (SQLException e1) {
				error(e1.getMessage(),e1);
			}
		}
		renderHtml("<h2 style='text-align:center'>绑定成功!</h2>");
	}
	
	public void updateAllRemark() {
		try {
			List<JSONObject> list = ServerContext.getAdminQuery().queryForList("select OPEN_ID,USERNAME,DEPTS from easi_user where OPEN_ID<>''", new Object[] {}, new JSONMapperImpl());
			for(JSONObject object:list) {
				UserApi.updateRemark(object.getString("OPEN_ID"),object.getString("DEPTS")+"/"+object.getString("USERNAME"));
			}
		} catch (SQLException e) {
			error(e.getMessage(),e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void createTag() {
		try {
			List<JSONObject> list = ServerContext.getAdminQuery().queryForList("select DEPT_NAME from easi_dept", new Object[] {}, new JSONMapperImpl());
			for(JSONObject object:list) {
				TagApi.create(object.getString("DEPT_NAME"));
			}
		} catch (SQLException e) {
			error(e.getMessage(),e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void  sendTextMsg() {
		String msg = getPara("msg");
		String openId = getPara("openId");
		if(StringUtils.isBlank(msg)) {
			msg = "test";
		}
		if(StringUtils.isBlank(openId)) {
			openId =  "oBgmx0Vrpyuxb1Px80YFEAwT0iFM";
			msg = "open id is null;"+msg;
		}
		ApiResult sendText = CustomServiceApi.sendText(openId, msg);
		info(sendText.getJson());
		renderJson(sendText);
	}
	
	
	public void getAuthorize(){
		String userId=getPara("userId");
		if(StrKit.isBlank(userId)){
			 renderText("userId not empty.");
			 return;
		}
		String url = SnsAccessTokenApi.getAuthorizeURL(ApiConfigKit.getAppId(), "http://work.yunqu-info.cn/yq-work/api/authCallback?userId="+userId,true);
		redirect(url);
	}
	
	
	public void callback(){
		String code = getPara("code");
		String url = getPara("state");
		info("code:"+code);
		if(StringUtils.notBlank(url)) {
			url = url.replaceAll("_A", "=");
			url = url.replaceAll("_B", "&");
			if(url.startsWith("/yq-work")||url.startsWith("/workbench")) {
				url = "http://work.yunqu-info.cn"+url;
			}else if(url.startsWith("/pages")) {
				url = "http://work.yunqu-info.cn/yq-work";
			}
		}
		info("redirect_url:"+url);
		SnsAccessToken ansAccessToken= SnsAccessTokenApi.getSnsAccessToken(ApiConfigKit.getAppId(), ApiConfigKit.getApiConfig().getAppSecret(), code);
		ApiResult apiResult = UserApi.getUserInfo(ansAccessToken.getOpenid());
		info("openId:"+ansAccessToken.getOpenid());
		info(apiResult.getJson());
		try {
			String userId = ServerContext.getAdminQuery().queryForString("select USER_ID from easi_user where open_id  = ?",ansAccessToken.getOpenid());
			if(StringUtils.isBlank(userId)) {
				renderHtml("<p style='font-size:50px;text-align:center'>请先做好账号和公众号绑定.</p>");
				return;
			}
			if(getRequest().getRemoteUser()==null) {
				JSONObject object = ServerContext.getAdminQuery().queryForRow("select * from easi_user_login where ACCT_STATE = 0 and USER_ID = ?", new Object[] {userId}, new JSONMapperImpl());
				try {
					if(object!=null) {
						getRequest().login(object.getString("USER_ACCT"),object.getString("USER_PWD"));
					}else {
						renderHtml("账号已暂停使用.");
						return;
					}
				} catch (ServletException e) {
					this.error(e.getMessage(), e);
					renderHtml("登录失败,"+e.getMessage());
					return;
				}
			}
			redirect(url);
			return;
		} catch (SQLException e) {
			this.error(null, e);
			renderHtml("跳转失败,"+e.getMessage());
		}
	}
	
	
	
	public void goUrl(){
		String toUrl = getPara("url");
		info("url1>>>>>>>>>"+toUrl);
		String url = SnsAccessTokenApi.getAuthorizeURL(ApiConfigKit.getAppId(), "http://work.yunqu-info.cn/yq-work/api/callback",toUrl,true);
		info("ur2>>>>>>>>>"+url);
		redirect(url);
	}
	
	/**
	 * 获取公众号关注用户
	 */
	public void getFollowers() {
		ApiResult apiResult = UserApi.getFollows();
		renderText(apiResult.getJson());
	}
	
	protected void info(String msg) {
		Logger logger = LogEngine.getLogger("weixin","weixin");
		logger.info(msg);
	}
	
	protected void error(String msg,Throwable throwable) {
		Logger logger = LogEngine.getLogger("weixin","weixin");
		logger.error(msg,throwable);
	}
}
