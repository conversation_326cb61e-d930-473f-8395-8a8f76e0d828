<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>通讯录</title>
	<style>
		.layui-tree-icon{height: 16px;width: 16px;line-height: 13px;}
		.left{
			width: 220px;float: left;margin: -15px;padding: 15px;background-color: #fff;height: 100%;height: 100%;
			min-height: calc(100vh - 30px);
		}
		.right{
			float: left;width:calc(100% - 220px);
			margin-left: 30px;
			height: 100px;
			
		}
		.layui-tree-set{padding: 2px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form name="addressForm" autocomplete="off"  onsubmit="return false;" class="form-inline" id="addressForm">
			<input name="deptId" id="deptId" type="hidden"/>
			<input name="deptCode" id="deptCode" type="hidden"/>
			<div class="ibox">
				<div class="ibox-content left">
					<div style="line-height:40px;height: 40px;border-bottom: 1px solid #eee;">
						组织机构
						<div class="box-tools pull-right">
							<button type="button" class="btn btn-xs" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
						</div>
					</div>
					<div id="ztree" style="padding-bottom: 60px"></div>
				</div>
				<div class="right">
					<div class="ibox-content clearfix mt-10">
						<div class="form-group">
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">姓名</span>	
							 <input type="text" name="userName" class="form-control input-sm" style="width: 90px;">
					     </div>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">部门</span>	
							 <input type="text" name="depts" class="form-control input-sm" style="width: 90px;">
					     </div>
	        		     <div class="input-group input-group-sm hidden">
							 <span class="input-group-addon">性别</span>	
							 <select id="sex" data-mars="EhrDao.dict('S001')"></select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="addresslist.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
					</div>
					<div class="ibox-content clearfix mt-10" >
						 <table class="layui-hide" id="address"></table>
					</div>
					
				</div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
 <script src="https://cdn.bootcss.com/jquery.qrcode/1.0/jquery.qrcode.min.js"></script>
 <script type="text/javascript">
		
		
		$(function(){
			$("#addressForm").render({success:function(){
				addresslist.init();
				addresslist.initTree();
				setTimeout(function(){
					$(".layui-icon-addition:eq(0)").click();
					$(".layui-icon-addition:eq(1)").click();
				},1000);
			}});
		});
		function utf16to8(str) {
		    var out, i, len, c;  
		    out = "";  
		    len = str.length;  
		    for(i = 0; i < len; i++) {  
		    c = str.charCodeAt(i);  
		    if ((c >= 0x0001) && (c <= 0x007F)) {  
		        out += str.charAt(i);  
		    } else if (c > 0x07FF) {  
		        out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));  
		        out += String.fromCharCode(0x80 | ((c >>  6) & 0x3F));  
		        out += String.fromCharCode(0x80 | ((c >>  0) & 0x3F));  
		    } else {  
		        out += String.fromCharCode(0xC0 | ((c >>  6) & 0x1F));  
		        out += String.fromCharCode(0x80 | ((c >>  0) & 0x3F));  
		    }  
		    }  
		    return out; 
		} 
		var addresslist={
			initTree:function(){
				ajax.remoteCall("${ctxPath}/servlet/ehr?action=getFrameworkTree",{},function(result) { 
					var data=result['data']['children'];
					var str = JSON.stringify(data);
					str = str.replaceAll('name','title');
					var newData=eval('('+str+')');
					 layui.use('tree', function(){
						 var tree = layui.tree;
						 tree.render({
						      elem: '#ztree',
						      data:newData,
						      click: function(obj){
						    	 $("#deptId").val(obj.data.deptId);
						    	 $("#deptCode").val(obj.data.deptCode);
						    	 addresslist.query();
						      }
						   }
						 );
					 });
					
				});
				
			},
			saveQcode:function(data){
				var qcode=[];
				qcode.push("BEGIN:VCARD");
				qcode.push("VERSION:3.0");
				qcode.push("FN:"+data['USERNAME']);
				qcode.push("ORG:云趣科技-"+data['DEPTS']);
				qcode.push("TEL:"+data['MOBILE']);
				qcode.push("EMAIL:"+data['EMAIL']);
				qcode.push("TITLE:"+data['DATA2']);
				qcode.push("URL:www.yunqu-info.com");
				qcode.push("END:VCARD");
				
				var html="<div style='padding:20px' id='qrcode'></div>";
				
				layer.open({shadeClose:true,offset:'30px',title:'请使用微信扫一扫',type:1,area:['300px','350px'],content:html,success:function(){
					 jQuery('#qrcode').qrcode(utf16to8(qcode.join("\n")));
				}});
			},
			call:function(tel){
				var html="<div style='padding:20px' id='qrcode2'></div>";
				layer.open({shadeClose:true,offset:'30px',title:'请使用微信扫一扫',type:1,area:['300px','350px'],content:html,success:function(){
					 jQuery('#qrcode2').qrcode(utf16to8('https://h5.bqu.cn/tel_qrcode/?tel='+tel));
				}});
			},
			init:function(){
				$("#addressForm").initTable({
					mars:'EhrDao.addresslist',
					limit:15,
					id:'address',
					cols: [[
					  {title:'序号',type:'numbers',width:50},
					  {
					    field: 'DATA1',
						title: '工号',
						width:80,
						align:'center',
						hide:true
					  },
		              {
					    field: 'USERNAME',
						title: '姓名',
						width:80,
						align:'left'
					},{
					    field: 'MOBILE',
						title: '手机号码',
						align:'center',
						templet:'<div>{{d.MOBILE}}<i class="glyphicon glyphicon-earphone ml-5" style="cursor:pointer;color:#2d8cf0;" onclick="addresslist.call(\'{{d.MOBILE}}\')"></i></div>',
					},{
					    field: 'EMAIL',
						title: '邮箱',
						align:'left',
						width:200,
						style:'color:red;cursor: pointer;',
						templet:function(row){
							return "<a href='mailto:"+row.EMAIL+"'>"+row.EMAIL+"</a>";
						}
					},{
					    field: 'DEPTS',
						title: '部门',
						align:'left',
					},{
					    field: 'DATA1',
						title: '工号',
						align:'left',
					},{
					    field: 'DATA2',
						title: '职位',
						align:'left',
					},{
						field:'LAST_LOGIN_TIME',
						title:'最后登录时间'
					},{
					    field: '',
						title: '保存到手机',
						align:'center',
						event:'addresslist.saveQcode',
						style:'text-decoration: underline;color: #4980b8;',
						templet:function(){
							return '保存';
						}
					}
				 ]]}
				);
			},
			query:function(){
				$("#addressForm").queryData({jumpOne:true});
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>