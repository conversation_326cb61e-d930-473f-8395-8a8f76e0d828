package com.yunqu.work.servlet.crm;

import java.sql.SQLException;
import javax.servlet.annotation.WebServlet;
import com.yunqu.work.base.AppBaseServlet;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

@WebServlet("/servlet/custOperate/*")
public class CustOperateServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;
    
    private EasyRecord getPriceModel(String prefix){
        EasyRecord model = new EasyRecord("yq_crm_sales_price","PRICE_ID");
        model.setColumns(getJSONObject(prefix));
        return model;
    }

    public EasyResult actionForAddPrice(){
        EasyRecord model = getPriceModel("price");
        model.setPrimaryValues(RandomKit.uuid().toUpperCase());
        model.set("CREATE_TIME", EasyDate.getCurrentDateString());
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        model.set("CREATOR", getUserId());
        model.set("CREATE_NAME", getUserName());
        
        try {
            this.getQuery().save(model);
            return EasyResult.ok("保存成功");
        } catch (SQLException e) {
            this.error("保存失败", e);
            return EasyResult.fail("保存失败:" + e.getMessage());
        }
    }
    
    public EasyResult actionForUpdatePrice(){
        EasyRecord model = getPriceModel("price");
        try {
            model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getQuery().update(model);
            return EasyResult.ok("更新成功");
        } catch (SQLException e) {
            this.error("更新失败", e);
            return EasyResult.fail("更新失败:" + e.getMessage());
        }
    }
    
    public EasyResult actionForDeletePrice(){
        String priceId = getJsonPara("priceId");
        try {
            this.getQuery().executeUpdate("delete from yq_crm_sales_price where PRICE_ID = ?", priceId);
            return EasyResult.ok("删除成功");
        } catch (SQLException e) {
            this.error("删除失败", e);
            return EasyResult.fail("删除失败:" + e.getMessage());
        }
    }
}
