package com.yunqu.work.servlet.flow;

import java.math.BigDecimal;
import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.utils.NumberToCN;
@WebServlet("/servlet/bx/fun/*")
public class YqBxFunServlet extends BaseFlowServlet {
	
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForUpdateHzMoney() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		BigDecimal hzAmount = params.getBigDecimal("hzAmount");
		String businessId = params.getString("businessId");
		try {
			
			this.getQuery().executeUpdate("update yq_flow_bx_item set hz_amount = ? where item_id = ?",hzAmount,itemId);
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.hz_money = (select sum(t2.hz_amount) from yq_flow_bx_item t2 where t1.business_id = t2.business_id) where t1.business_id = ?",businessId);
			String num = this.getQuery().queryForString("select t1.hz_money - t1.reverse_money from yq_flow_bx_base t1 where t1.business_id = ?", businessId);
			
			String payMoneyDx = NumberToCN.number2CNMontrayUnit(new BigDecimal(num));
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.pay_money = ?,t1.pay_money_dx = ? where t1.business_id = ?",num,payMoneyDx,businessId);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateReverseMoney() {
		JSONObject params = getJSONObject();
		BigDecimal reverseMoney = params.getBigDecimal("reverseMoney");
		String businessId = params.getString("businessId");
		try {
			
			this.getQuery().executeUpdate("update yq_flow_bx_base set reverse_money = ? where business_id = ?",reverseMoney,businessId);
			String num = this.getQuery().queryForString("select t1.hz_money - t1.reverse_money from yq_flow_bx_base t1 where t1.business_id = ?", businessId);
			String payMoneyDx = NumberToCN.number2CNMontrayUnit(new BigDecimal(num));
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.pay_money = ?,t1.pay_money_dx = ? where t1.business_id = ?",num,payMoneyDx,businessId);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdatePayState() {
		JSONObject params = getJSONObject();
		String date = params.getString("date");
		JSONArray array = params.getJSONArray("array");
		try {
			for(int i=0;i<array.size();i++) {
				JSONObject row  = array.getJSONObject(i);
				EasySQL sql = new EasySQL();
				
				double payMoney = row.getDoubleValue("payMoney");
				double thisMoney = row.getDoubleValue("thisMoney");
				
				sql.append(payMoney==thisMoney?10:11,"update yq_flow_bx_base set pay_state = ?");// 0 流程中 1 待付款 10已付款 11部分付款
				sql.append(EasyDate.getCurrentTimeStampString(), ",pay_time = ?");
				sql.append(thisMoney,",has_pay_money = has_pay_money + ?");
				sql.append(date,",pay_date = ?");
				sql.append("where 1=1");
				sql.append(row.getString("id"),"and business_id = ?");
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
}
