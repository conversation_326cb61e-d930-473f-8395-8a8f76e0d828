package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveNodeRowMapper;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowApplyRowMapper;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.FlowRowMapper;

public class ApproveService extends AppBaseService {

	private static class Holder{
		private static ApproveService service=new ApproveService();
	}
	public static ApproveService getService(){
		return Holder.service;
	}
	
	public ApproveNodeModel getStartNode(String flowCode) {
		try {
			ApproveNodeModel obj = this.getQuery().queryForRow("select * from yq_flow_approve_node where p_node_id = 0 and flow_code = ? and node_state = 0 order by check_index", new Object[] {flowCode}, new ApproveNodeRowMapper());
			return obj;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ApproveNodeModel();
		}
	}
	public ApproveNodeModel getNode(String nodeId) {
		try {
			ApproveNodeModel obj = this.getQuery().queryForRow("select * from yq_flow_approve_node where node_id = ?", new Object[] {nodeId}, new ApproveNodeRowMapper());
			return obj;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ApproveNodeModel();
		}
	}
	
	public ApproveNodeModel getNodeByResultId(String resultId) {
		try {
			ApproveNodeModel obj = this.getQuery().queryForRow("select t2.* from yq_flow_approve_result t1,yq_flow_approve_node t2 where t1.node_id = t2.node_id and t1.result_id = ?", new Object[] {resultId}, new ApproveNodeRowMapper());
			return obj;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ApproveNodeModel();
		}
	}
	
	public List<ApproveNodeModel> getNodes(String flowCode) {
		try {
			return this.getQuery().queryForList("select * from yq_flow_approve_node where  flow_code = ? and node_state = 0 order by check_index", new Object[] {flowCode}, new ApproveNodeRowMapper());
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ArrayList<ApproveNodeModel>();
		}
	}
	
	public FlowModel getFlow(String flowCode) {
		try {
			String sql = Db.getSql("findFlowInfo");
			FlowModel model  = this.getQuery().queryForRow(sql,new Object[] {flowCode}, new FlowRowMapper());
			return model;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new FlowModel();
		}
	}
	
	public FlowApplyModel getApply(String applyId) {
		try {
			FlowApplyModel obj = this.getQuery().queryForRow("select t1.*,t2.flow_name from yq_flow_apply t1,yq_flow_category t2 where t1.flow_code = t2.flow_code and t1.apply_id = ?", new Object[] {applyId},new FlowApplyRowMapper());
			return obj;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new FlowApplyModel();
		}
	}
	
}
