function getUserName(userId){
	if(userId=='')return '';
	var cacheStr=sessionStorage.getItem("userList");
	if(cacheStr){
		var json=$.extend({},eval('(' + cacheStr + ')'));
		var userInfo=json[userId];
		if(userInfo){
			return userInfo.userName||'';
		}else{
			return '';
		}
	}else{
		initUserList();
		return '';
	}
}
function fullShow(){
	var windowWidth = $(window).width();
    if(windowWidth < 640){
       return true;
    }else{
    	return false;
    }
}
function getUserPic(userId){
	if(userId=='')return '';
	var cacheStr=sessionStorage.getItem("userList");
	if(cacheStr){
		var json=$.extend({},eval('(' + cacheStr + ')'));
		var userInfo=json[userId];
		if(userInfo){
			return userInfo.userPic||'';
		}else{
			return '';
		}
	}else{
		initUserList();
		return '';
	}
}
var isSuperUser=isSuperUser();
function isSuperUser(){
	var cacheStr=sessionStorage.getItem("isSuperUser");
	if(cacheStr){
		return cacheStr;
	}else{
		initUserList();
		return false;
	}
}
function getCurrentUserId(){
	var cacheStr=sessionStorage.getItem("yq_userId");
	return cacheStr;
}
function initUserList(){
	var cache=sessionStorage.getItem("userList");
	if(cache==null||cache==''){
		ajax.remoteCall("/yq-work/webcall?action=CommonDao.userList",{},function(result){
			var userList={};
			var userPic={};
			var data=result.data;
			var isSuperUser=result.isSuperUser
			for(var index in data){
				userList[data[index].USER_ID]={userName:data[index].USERNAME,userPic:data[index].PIC_URL};
			}
			sessionStorage.setItem("userList",JSON.stringify(userList));
			sessionStorage.setItem("isSuperUser",isSuperUser);
		});
	}
}
function projectState(state){
	var json={0:'未启动',1:'进行中',2:'已完成',3:'已延期',4:'已撤销'};
	var result=json[state]||'';
	if(result){
		return result;
	}else{
		return '';
	}
}
function taskState(state){
	var json={10:'待办中',20:'进行中 ',30:'已完成',40:'已验收',42:'验收不通过'};
	var result=json[state]||'';
	if(result){
		return result;
	}else{
		return '';
	}
}
function todoLevel(val){
	if(val==1)return '<label class="label label-default">一般</label>';
	if(val==2)return '<label class="label label-warning">最高</label>';
	if(val==3)return '<label class="label label-danger">较高</label>';
	if(val==4)return '<label class="label label-info">较低</label>';
	return "--";
}
function taskLevel(val){
	if(val==1)return '<label class="label label-default">不指定</label>';
	if(val==2)return '<label class="label label-warning">严重</label>';
	if(val==3)return '<label class="label label-danger">主要</label>';
	if(val==4)return '<label class="label label-info">次要</label>';
	if(val==5)return '<label class="label label-info">不重要</label>';
	return "--";
}
function renderSelect(){
	var select=$("body").find("select");
	if(select.leng>0&&requreLib){
		requreLib.setplugs('select2',function(){
			select.select2({theme: "bootstrap"});
		});
	}
}
function loadDownloadLog(option){
	var formId=option.formId;
	var tableId=option.tableId;
	$("#"+formId).initTable({
		mars:'CommonDao.downloadLog',
		id:tableId,
		data:option,
		cols: [[
         {
    	 	type: 'numbers',
			title: '序号',
			align:'left'
		 },{
		    field: 'FILE_NAME',
			title: '文件名',
			align:'center'
		},
		{
		    field: 'FILE_SIZE',
			title: '文件大小',
			align:'center'
		},
		{
		    field: 'DOWNLOAD_BY',
			title: '下载人',
			align:'center',
			templet:function(row){
				return getUserName(row.DOWNLOAD_BY);
		 }
		},{
		    field: 'DOWNLOAD_TIME',
			title: '下载时间',
			align:'center'
		}
		]]}
	);
}
function loadLookLog(option){
	var formId=option.formId;
	var tableId=option.tableId;
	$("#"+formId).initTable({
		mars:'CommonDao.lookLogList',
		id:tableId,
		data:option,
		cols: [[
         {
    	 	type: 'numbers',
			title: '序号',
			align:'left'
		 },{
		    field: 'LOOK_BY',
			title: '查看人',
			align:'center',
			templet:function(row){
				return getUserName(row.LOOK_BY);
			}
		},{
		    field: 'LOOK_TIME',
			title: '查看时间',
			align:'center'
		}
		]]}
	);
}
function loadOpLog(option){
	var formId=option.formId;
	var tableId=option.tableId;
	$("#"+formId).initTable({
		mars:'CommonDao.lookOpList',
		id:tableId,
		data:option,
		page:false,
		cols: [[
		        {
		        	type: 'numbers',
		        	title: '序号',
		        	align:'center'
		        },{
		        	field: 'CONTENT',
		        	title: '操作内容',
		        	align:'center'
		        },{
		        	field: 'OP_BY',
		        	title: '操作人',
		        	align:'center',
		        	templet:function(row){
		        		return getUserName(row.OP_BY);
		        	}
		        },{
		        	field: 'OP_TIME',
		        	title: '操作时间',
		        	align:'center'
		        }
		   ]]}
	);
}
function delFile(obj){
	var id=$(obj).data("id");
	ajax.remoteCall("/yq-work/servlet/doc?action=del",{id:id},function(result){
		$(obj).parent().remove();
	});
}
function easyUploadFile(options){
	var callback=options.callback;
	var formId=options.formId||'fileForm';
	var fileId=options.fileId||'localfile';
	if(callback&&$.isFunction(window[callback])){
		
	}else{
		layer.alert("必须填写正确的回调方法！",{icon:7},function(index){
			layer.close(index);
			return;
		});
	}
	var fileList = document.getElementById(fileId).files;
	if(fileList.length<=0){
    	alert("请选择上传的文件!")
    	return;
	}
	for(var i=0;i<fileList.length;i++){
		var fileObj=fileList[i];
		if(!checkfile(fileObj,options)){
			return;
		}
	}
	var formData = new FormData($("#"+formId)[0]); 
	var params=$.extend({},options);
	var paramStr=jQuery.param(params);
	$.ajax({  
          url: '/yq-work/servlet/upload?'+paramStr,  
          type: 'POST',  
          data: formData,async: false,cache: false,contentType: false,processData: false,  
          success: function (result) {
		    	 layer.msg(result.msg,{time:1500,offset:'rb',icon:1},function(){
		    		 layer.closeAll('dialog');
			    	 if(result.state  == 1){
			    		 window[callback](result.data,params);
			    	 }
		    	 });
          },error: function (returndata) {  
	             layer.msg('上传失败!'); 
	             layer.closeAll('dialog');
          },beforeSend:function(){
        	    layer.msg('正在上传', {icon: 16,time:1000*100,offset:'30px'});
          },complete:function(){
        	    layer.closeAll('loading');
          }  
     }); 
}
function checkfile(fileObj,options){
	//校验文件类型
	var filename=fileObj.name.toLowerCase(); 
	var fileType=options.fileType;
	if(fileType!=null&&fileType!=undefined&&fileType!=''){
		var fileExtension = filename.substring(filename.lastIndexOf('.') + 1);
		fileExtension=fileExtension.toLowerCase();
		if(fileType.indexOf(fileExtension)==-1){
			alert("上传仅支持格式："+fileType);
			return false;
		}
	}
	//校验文件大小
	var fileMaxSize=options.fileMaxSize;
	if(fileMaxSize!=null&&fileMaxSize!=undefined&&fileMaxSize!=''){
		return checkFileSize(fileObj,fileMaxSize);
	}
	return true;
}
	
var isIE = /msie/i.test(navigator.userAgent) && !window.opera; 
function checkFileSize(target,fileMaxSize) { 
	var fileSize = 0; 
	if (isIE && !target.files) { 
		target.select(); target.blur();
		var filePath = document.selection.createRange().text;
		var fileSystem = new ActiveXObject("Scripting.FileSystemObject"); 
		if(!fileSystem.FileExists(filePath)){ 
			alert("附件不存在，请重新输入！"); 
			return false; 
		} 
		var file = fileSystem.GetFile(filePath); 
		fileSize = file.Size; 
	} else { 
		fileSize = target.size; 
	}  
	var size = fileSize / 1024; 
	if(size>fileMaxSize){ 
		if(fileMaxSize>=1024){
			alert("文件大小限制"+(fileMaxSize/1024).toFixed(2)+"M内！"); 
		}else{
			alert("文件大小限制"+fileMaxSize+"k内！"); 
		}
		target.value =""; 
		return false; 
	} 
	return true;
} 
$(function(){
	//renderSelect();
	initUserList();
});