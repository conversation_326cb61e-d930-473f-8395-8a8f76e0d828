<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择供应商</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selecSupplierForm">
	         <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 150px">
					 <span class="input-group-addon">编号</span>	
					 <input type="text" name="supplierNo" class="form-control input-sm">
			     </div>
	   			 <div class="input-group input-group-sm ml-5" style="width: 150px">
					 <span class="input-group-addon">名称</span>	
					 <input type="text" name="supplierName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectSupplier.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		   </div>
           	   <div class="ibox">
              		<table id="SelectSupplierList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectSupplier.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectSupplier = {
					sid:'${param.sid}',
					query : function(){
						$("#selecSupplierForm").queryData();
					},
					initTable : function(){
						$("#selecSupplierForm").initTable({
							mars:'SupplierDao.list',
							id:'SelectSupplierList',
							page:true,
							limit:20,
							height:'350px',
							rowDoubleEvent:'SelectSupplier.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'SUPPLIER_NO',
								title: '编号',
								width:100
							},{
							    field: 'NAME',
								title: '名称'
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectSupplier.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('SelectSupplierList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['NAME']);
									ids.push(data[index]['SUPPLIER_ID']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								
								var refPayBank = el.data('refPayBank');
								if(refPayBank){
									var _refPayBank = $($g(refPayBank));
									_refPayBank.val(data[0]['PAY_BANK_NAME']);
								}
								var refPayBankAccount = el.data('refPayBankAccount');
								if(refPayBankAccount){
									var _refPayBankAccount = $($g(refPayBankAccount));
									_refPayBankAccount.val(data[0]['PAY_BANK_ACCOUNT']);
								}
								
								popup.layerClose("selecSupplierForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							el.val(selectRow['NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['SUPPLIER_ID']);
							}
							
							var refPayBank = el.data('refPayBank');
							if(refPayBank){
								var _refPayBank = $($g(refPayBank));
								_refPayBank.val(selectRow['PAY_BANK_NAME']);
							}
							var refPayBankAccount = el.data('refPayBankAccount');
							if(refPayBankAccount){
								var _refPayBankAccount = $($g(refPayBankAccount));
								_refPayBankAccount.val(selectRow['PAY_BANK_ACCOUNT']);
							}
							
							popup.layerClose("selecSupplierForm");
						}
					}
					
			};
			$(function(){
				SelectSupplier.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>