package com.yunqu.work.ai;

public class Bigmodel {
	
	public static String MODEL_GLM = "GLM";
	public static String MODEL_QWEN = "Qwen";
	
	public static String request(String model,String systemSay,String userSay) {
		if(model.equals(MODEL_GLM)) {
			return GLM4Client.request(systemSay, userSay);
		}
		if(model.equals(MODEL_QWEN)) {
			SiliconCloudAI.qwenCoder(systemSay, userSay);
		}
		return SiliconCloudAI.qwenCoder(systemSay, userSay);
	}
	
	public static String request(String systemSay,String userSay) {
		return SiliconCloudAI.deepseek32B(systemSay, userSay);
	}
}
