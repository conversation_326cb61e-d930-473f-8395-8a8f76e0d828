<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title></title>
    <style>
        .form-horizontal {
            width: 100%;
        }

        .select2 {
            z-index: 90;
        }

        .select2-container {
            width: 100% !important;
        }

        .select2-container--open .select2-dropdown {
            left: 0;
            z-index: 999999999;
        }

        .select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
            background-color: #fff;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="incomeConfirmEditForm" class="form-horizontal" data-mars="IncomeConfirmDao.record" autocomplete="off"
          data-mars-prefix="confirm.">
        <input type="hidden" value="${param.confirmId}" name="confirm.CONFIRM_ID"/>
        <input type="hidden" value="${param.oldStageId}" name="oldStageId"/>
        <input type="hidden" value="${param.contractId}" name="confirm.CONTRACT_ID"/>
        <input type="hidden" value="${param.confirmType}" name="confirm.CONFIRM_TYPE"/>
        <table class="table table-vzebra">
            <tbody>
            <tr style="display: none">
                <td>开票编号</td>
                <td colspan="3">
                    <input type="text" name="confirm.INVOICE_ID" class="form-control">
                </td>
            </tr>
            <tr>
                <td style="min-width: 120px" class="required">日期</td>
                <td>
                    <input type="text" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                           name="confirm.CONFIRM_DATE" class="form-control input-sm Wdate">
                </td>
                <td width="120px">来源</td>
                <td>
                    <select name="confirm.SOURCE" class="form-control">
                        <option value="">请选择</option>
                        <option value="来源1">来源1</option>
                        <option value="来源2">来源2</option>
                        <option value="来源3">来源3</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>收入确认阶段</td>
                <td colspan="3">
                    <SELECT name="confirm.INCOME_STAGE_ID" onchange="incomeConfirmEdit.getStageAmount(this.value)"
                        <%--                            <c:if test="${!empty param.confirmId}"> disabled </c:if>--%>
                            class="form-control">
                        <option value="">请选择</option>
                    </SELECT>
                </td>
            </tr>
            <tr>
                <td class="required">确认金额(税前)</td>
                <td>
                    <input type="number" data-rules="required" name="confirm.AMOUNT_WITH_TAX" class="form-control"
                           placeholder="选择收入确认阶段后自动获取" oninput="incomeConfirmEdit.syncAmount(1)">
                </td>
                <td class="required">确认金额(税后)</td>
                <td>
                    <input type="number" data-rules="required" name="confirm.AMOUNT_NO_TAX"
                           class="form-control" placeholder="选择收入确认阶段后自动获取" oninput="incomeConfirmEdit.syncAmount(2)">
                </td>
            </tr>
            <tr>
                <td>摘要</td>
                <td colspan="3">
                    <textarea name="confirm.CONFIRM_DESC" class="form-control" rows="3"></textarea>
                </td>
            </tr>
            <tr>
                <td>发票来源</td>
                <td>
                    <input type="text" name="confirm.INVOICE_SOURCE" class="form-control input-sm">
                </td>
                <td>开票客户</td>
                <td>
                    <input type="hidden" name="confirm.CUST_NO">
                    <input type="hidden" name="confirm.CUST_ID" id="custId">
                    <input type="text" onclick="singleCust(this);" readonly="readonly" placeholder="请点击选择客户"
                           name="confirm.CUSTOMER_NAME" class="form-control input-sm">
                </td>
            </tr>
            </tbody>
        </table>
        <br>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px"
                    onclick="incomeConfirmEdit.ajaxSubmitForm()"> 保 存
            </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px"
                    onclick="popup.layerClose(this);"> 关闭
            </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("incomeConfirmEdit");

        incomeConfirmEdit.confirmId = '${param.confirmId}';
        incomeConfirmEdit.isNew = '${param.isNew}';
        incomeConfirmEdit.contractId = '${param.contractId}';
        incomeConfirmEdit.confirmType = '${param.confirmType}';
        incomeConfirmEdit.unConfirmAmount = 0;
        incomeConfirmEdit.unConfirmAmountNoTax = 0;
        incomeConfirmEdit.stageAmount = 0;
        incomeConfirmEdit.stageAmountNoTax = 0;
        incomeConfirmEdit.isStageAmountLoad = false;
        incomeConfirmEdit.oldIncomeStageId = '';
        incomeConfirmEdit.oldConfirmAmount = 0;
        incomeConfirmEdit.oldConfirmAmountNoTax = 0;

        incomeConfirmEdit.custName = '${param.custName}';
        incomeConfirmEdit.custId = '${param.custId}';
        incomeConfirmEdit.custNo = '${param.custNo}';
        $(function () {
            $("#incomeConfirmEditForm").render({
                success: function (result) {

                    $('select[name="confirm.INCOME_STAGE_ID"]').attr("data-mars", "IncomeStageDao.selectIncomeStage");
                    $('select[name="confirm.INCOME_STAGE_ID"]').render({
                        data: {contractId: incomeConfirmEdit.contractId, confirmType: incomeConfirmEdit.confirmType},
                        success: function () {
                            if (incomeConfirmEdit.confirmId && incomeConfirmEdit.isNew != '1') {
                                var recordData = result["IncomeConfirmDao.record"] && result["IncomeConfirmDao.record"].data;
                                $('select[name="confirm.INCOME_STAGE_ID"]').val(recordData.INCOME_STAGE_ID);
                                incomeConfirmEdit.oldIncomeStageId = recordData.INCOME_STAGE_ID;
                                incomeConfirmEdit.oldConfirmAmount = Number(recordData.AMOUNT_WITH_TAX);
                                incomeConfirmEdit.oldConfirmAmountNoTax = Number(recordData.AMOUNT_NO_TAX);
                               //获得stageAmount,不然修改金额不会比例同步
                                incomeConfirmEdit.getStageAmount(recordData.INCOME_STAGE_ID);
                            }
                            if (incomeConfirmEdit.isNew == '1') {
                                $('[name="confirm.CUST_NO"]').val(incomeConfirmEdit.custNo);
                                $('[name="confirm.CUST_ID"]').val(incomeConfirmEdit.custId);
                                $('[name="confirm.CUSTOMER_NAME"]').val(incomeConfirmEdit.custName);
                            }
                            requreLib.setplugs('select2', function () {
                                $('select[name="confirm.INCOME_STAGE_ID"]').select2({theme: "bootstrap", placeholder: '请选择'});
                            });
                        }
                    });
                }
            });
        });

        incomeConfirmEdit.ajaxSubmitForm = function () {
            if (form.validate("#incomeConfirmEditForm")) {
                if (incomeConfirmEdit.isNew == '1') {
                    incomeConfirmEdit.insertData();
                } else if (incomeConfirmEdit.confirmId) {
                    incomeConfirmEdit.updateData();
                } else {
                    incomeConfirmEdit.insertData();
                }
            }
        }

        incomeConfirmEdit.insertData = function () {
            var data = form.getJSONObject("#incomeConfirmEditForm");
            ajax.remoteCall("${ctxPath}/servlet/incomeConfirm?action=add", data, function (result) {
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        if (incomeConfirmEdit.confirmType == 'A') {
                            reloadIncomeConfirmList();
                            reloadStageList();
                        } else if (incomeConfirmEdit.confirmType == 'B') {
                            reloadIncomeConfirmListB();
                        }
                        reloadIncomeStageList();
                    });
                } else {
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        incomeConfirmEdit.updateData = function () {
            var data = form.getJSONObject("#incomeConfirmEditForm");
            data["confirm.INCOME_STAGE_ID"] = $("SELECT[name='confirm.INCOME_STAGE_ID']").val();
            ajax.remoteCall("${ctxPath}/servlet/incomeConfirm?action=update", data, function (result) {
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        if (incomeConfirmEdit.confirmType == 'A') {
                            reloadStageList();
                            reloadIncomeConfirmList();
                        } else if (incomeConfirmEdit.confirmType == 'B') {
                            reloadIncomeConfirmListB();
                        }
                    });
                    reloadIncomeStageList();
                } else {
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        incomeConfirmEdit.getTaxRate = function (paymentType) {
            $("#incomeConfirmTaxRate").val(paymentType);
            incomeConfirmEdit.calcTax();
        }

        incomeConfirmEdit.calcTax = function () {
            var taxRate = parseFloat($("#incomeConfirmTaxRate option:selected").text());
            if (isNaN(taxRate)) {
                taxRate = 0;
            }
            var amount = $("#incomeConfirmEditForm input[name='confirm.AMOUNT_WITH_TAX']").val();
            var amountNoTax = amount * 100 / (100 + taxRate);
            $("input[name='confirm.AMOUNT_NO_TAX']").val(amountNoTax.toFixed(2));
            var tax = taxRate * amountNoTax / 100;
            $("input[name='confirm.TAX']").val(tax.toFixed(2));
        }

        incomeConfirmEdit.getStageAmount = function (stageId) {
            if (stageId) {
                var data = {"params": {"incomeStage.INCOME_STAGE_ID": stageId}, "controls": ["IncomeStageDao.record"]};
                ajax.remoteCall("${ctxPath}/webcall", data, function (result) {
                    var stage = result["IncomeStageDao.record"]["data"];
                    if (stage && Object.keys(stage).length > 0) {
                        incomeConfirmEdit.stageAmount = Number(stage["PLAN_AMOUNT"]);
                        incomeConfirmEdit.stageAmountNoTax = Number(stage["AMOUNT_NO_TAX"]);
                        if (incomeConfirmEdit.confirmType == 'A') {
                            incomeConfirmEdit.unConfirmAmount = Number(stage["UNCONFIRM_AMOUNT_A"]);
                            incomeConfirmEdit.unConfirmAmountNoTax = Number(stage["UNCONFIRM_NO_TAX_A"]);
                        } else if (incomeConfirmEdit.confirmType == 'B') {
                            incomeConfirmEdit.unConfirmAmount = Number(stage["UNCONFIRM_AMOUNT_B"]);
                            incomeConfirmEdit.unConfirmAmountNoTax = Number(stage["UNCONFIRM_NO_TAX_B"]);
                        }
                        if (incomeConfirmEdit.isNew == '1' || incomeConfirmEdit.isStageAmountLoad) {
                            $('input[name="confirm.AMOUNT_WITH_TAX"]').val(incomeConfirmEdit.unConfirmAmount);
                            $('input[name="confirm.AMOUNT_NO_TAX"]').val(incomeConfirmEdit.unConfirmAmountNoTax);
                        }
                    } else {
                        layer.alert("该收入确认阶段信息有误", {icon: 5});
                    }
                    incomeConfirmEdit.isStageAmountLoad = true;
                })
            }
        }


        incomeConfirmEdit.syncAmount = function (type) {
            var newIncomeStageId = $("SELECT[name='confirm.INCOME_STAGE_ID']").val();
            if (type == 1) { //修改含税金额
                var confirmAmount = Number($('input[name="confirm.AMOUNT_WITH_TAX"]').val());
                var max = incomeConfirmEdit.unConfirmAmount;
                var flag = false;
                if (incomeConfirmEdit.oldIncomeStageId == newIncomeStageId) {
                    max = incomeConfirmEdit.unConfirmAmount + incomeConfirmEdit.oldConfirmAmount;
                    flag = true; //原选择IncomeStageId和现在的IncomeStageId相同，所以金额可以和原金额叠加
                }
                if (confirmAmount > max) {
                    if (incomeConfirmEdit.isNew == '1') {
                        layer.alert("确认金额(税前)不能大于 收入确认阶段 剩余未确认的金额(税前)：" + incomeConfirmEdit.unConfirmAmount, {icon: 5});
                    } else {
                        if (flag) {//原选择IncomeStageId和现在的IncomeStageId相同，所以金额可以和原金额叠加
                            layer.alert("修改后的确认金额(税前)不能大于<br> 原确认金额(税前)[" + incomeConfirmEdit.oldConfirmAmount + "] +收入确认阶段剩余未确认金额(税前)[" + incomeConfirmEdit.unConfirmAmount + "]<br> 的总和:" + max, {icon: 5});
                        } else {
                            layer.alert("确认金额(税前)不能大于 收入确认阶段 剩余未确认的金额(税前)：" + incomeConfirmEdit.unConfirmAmount, {icon: 5});
                        }
                    }
                    $('input[name="confirm.AMOUNT_WITH_TAX"]').val(max.toFixed(2))
                    if (flag) {//叠加
                        $('input[name="confirm.AMOUNT_NO_TAX"]').val((incomeConfirmEdit.unConfirmAmountNoTax + incomeConfirmEdit.oldConfirmAmountNoTax).toFixed(2))
                    } else { //不叠加
                        $('input[name="confirm.AMOUNT_NO_TAX"]').val((incomeConfirmEdit.unConfirmAmountNoTax).toFixed(2))
                    }
                } else if (confirmAmount < 0) {
                    layer.alert("确认金额不能小于0", {icon: 5});
                } else {
                    var ratio = confirmAmount / incomeConfirmEdit.stageAmount;
                    var confirmAmountNoTax = ratio * incomeConfirmEdit.stageAmountNoTax;
                    $('input[name="confirm.AMOUNT_NO_TAX"]').val(confirmAmountNoTax.toFixed(2));
                }
            } else { //修改税后金额
                var confirmAmountNoTax = Number($('input[name="confirm.AMOUNT_NO_TAX"]').val());
                var flag = false;
                var max = incomeConfirmEdit.unConfirmAmountNoTax;
                if (incomeConfirmEdit.oldIncomeStageId == newIncomeStageId) {
                    max = incomeConfirmEdit.unConfirmAmountNoTax + incomeConfirmEdit.oldConfirmAmountNoTax;
                    flag = true; //原选择IncomeStageId和现在的IncomeStageId相同，所以金额可以和原金额叠加
                }
                if (confirmAmountNoTax > max) {
                    if (incomeConfirmEdit.isNew == '1') {
                        layer.alert("确认金额(税后)不能大于 收入确认阶段 剩余未确认的金额(税后)：" + incomeConfirmEdit.unConfirmAmountNoTax, {icon: 5});
                    } else {
                        if (flag) {//原选择IncomeStageId和现在的IncomeStageId相同，所以金额可以和原金额叠加
                            layer.alert("修改后的确认金额(税后)不能大于<br> 原确认金额(税后)[" + incomeConfirmEdit.oldConfirmAmountNoTax + "] +收入确认阶段剩余未确认金额(税后)[" + incomeConfirmEdit.unConfirmAmountNoTax + "]<br> 的总和:" + max, {icon: 5});
                        } else { //不叠加
                            layer.alert("确认金额(税后)不能大于 收入确认阶段 剩余未确认的金额(税后)：" + incomeConfirmEdit.unConfirmAmountNoTax, {icon: 5});
                        }
                    }
                    $('input[name="confirm.AMOUNT_NO_TAX"]').val(max.toFixed(2))
                    if (flag) {//叠加
                        $('input[name="confirm.AMOUNT_WITH_TAX"]').val((incomeConfirmEdit.unConfirmAmount + incomeConfirmEdit.oldConfirmAmount).toFixed(2))
                    } else { //不叠加
                        $('input[name="confirm.AMOUNT_WITH_TAX"]').val((incomeConfirmEdit.unConfirmAmount).toFixed(2))
                    }
                } else if (confirmAmountNoTax < 0) {
                    layer.alert("确认金额不能小于0", {icon: 5});
                } else {
                    var ratio = confirmAmountNoTax / incomeConfirmEdit.stageAmountNoTax;
                    var confirmAmount = ratio * incomeConfirmEdit.stageAmount;
                    $('input[name="confirm.AMOUNT_WITH_TAX"]').val(confirmAmount.toFixed(2));
                }
            }

        }

        function selectReviewCallBack(row) {
            $("[name='confirm.CUSTOMER_NAME']").val(row['CUST_NAME']);
            $("[name='confirm.CUST_ID']").val(row['CUST_ID']);
            $("[name='confirm.CUST_NO']").val(row['CUST_NO']);
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>