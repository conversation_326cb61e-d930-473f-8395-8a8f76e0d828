<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="supplierId" type="hidden" value="${param.supplierId}">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 150px;">
					 		 <span class="input-group-addon">计划号</span>
						 	 <input class="form-control input-sm" placeholder="多个逗号隔开"  name="planIds">
						 </div>
						 <div class="input-group input-group-sm" style="width: 150px;">
					 		 <span class="input-group-addon">订单号</span>
						 	 <input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 150px;">
					 		 <span class="input-group-addon">合同名称</span>
						 	 <input class="form-control input-sm" name="contractName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 150px;">
					 		 <span class="input-group-addon">供应商</span>
						 	 <input class="form-control input-sm" name="supplierName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 150px;">
					 		<span class="input-group-addon">付款状态</span>
						 	 <select name="payState" onchange="DataMgr.query()" class="form-control">
	                   		 	<option value="">请选择</option>
	                   		 	<option value="0">已申请</option>
	                   		 	<option value="1">待申请</option>
	                   		 </select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px">
	          		    	<span class="input-group-addon">付款计划月份</span>	
                     		<input type="text" data-laydate="{type:'month'}" name="monthId" class="form-control input-sm">
                    	 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" data-event="enter" class="btn btn-sm btn-default" onclick="DataMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
				<script  type="text/html" id="toolbar">
					<EasyTag:res resId="ERP_ORDER_APPLY_PAYMENT">
						<button type="button" class="btn btn-sm btn-success ml-10" lay-event="DataMgr.applyPay">+ 发起付款申请</button>
					</EasyTag:res>
				</script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var DataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.payPlanList',
					id:'dataMgrTable',
					autoSort:false,
					cellMinWidth:90,
					limit:30,
					limits:[30,50,100,200],
					height:'full-90',
					toolbar:'#toolbar',
					cols: [[
		             {
	            	 	type: 'checkbox',
						title: '序号',
						align:'left'
					 },{
						 field:'PLAN_ID',
						 title:'计划号',
						 width:80
					 },{
						field:'PAYMENT_ID',
						title:'付款流程',
						width:70,
						templet:function(row){
							var id = row['PAYMENT_ID'];
							if(id==''){
								return '--';
							}else{
								return '<span class="label label-info label-outline">已发起</span>';
							}
						}
					},{
						 field:'CONTRACT_NO',
						 title:'合同号'
					 },{
						 field:'ORDER_ORG',
						 title:'采购组织',
						 width:80
					 },{
					    field: 'PLAN_PAY_AMOUNT',
						title: '计划付款金额',
						width:100,
						align:'center'
					},{
						 field:'ORDER_NO',
						 title:'采购订单号',
						 event:'DataMgr.detail',
						 width:90,
						 style:'text-decoration:underline;',
						 templet:'<div><a href="javascript:;">{{d.ORDER_NO}}</a></div>'
					 },{
					    field: 'SUPPLIER_NAME',
						title: '供应商',
						align:'center',
					    style:'text-decoration:underline;',
						minWidth:180,
						event:'DataMgr.supplierDetail'
					},{
						field:'TOTAL_PRICE',
						width:90,
						title:'订单总额'
					},{
						field:'HAS_PAY_AMOUNT',
						width:90,
						hide:true,
						title:'已付款金额'
					},{
						field:'PAY_PERIOD',
						width:70,
						title:'付款阶段'
					},{
					    field: 'PAY_REASON',
						title: '付款依据',
						align:'left'
					},{
					    field: 'PLAN_PAY_MONTH',
						title: '计划付款月份',
						width:90,
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '操作时间',
						align:'center',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'CREATE_USER_ID',
						title: '创建人',
						align:'center',
						width:70,
						templet:function(row){
							return getUserName(row.CREATE_USER_ID);
						}
					}
				]],done:function(){
					
			  }});
			},
			detail:function(data){
				popup.openTab({id:'orderDetail',title:'采购详情',url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{orderId:data.ORDER_ID,custId:data.CUST_ID}});
			},
			supplierDetail:function(data){
				var id = data['SUPPLIER_ID'];
				popup.openTab({id:'supplierDetail',url:'${ctxPath}/pages/erp/supplier/supplier-detail.jsp',title:'供应商详情',data:{supplierId:id}});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable',jumpOne:true});
			},
			edit:function(orderId){
				popup.openTab({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:'${ctxPath}/pages/erp/order/apply-edit.jsp',title:'修改信息',data:{orderId:orderId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['700px','500px'],url:'${ctxPath}/pages/erp/order/order-add.jsp',title:'新建采购申请'});
			},
			contractDetail:function(data){
				popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/project/contract',data:{contractId:data.CONTRACT_ID,custId:data['CUST_ID']}});
			},
			applyPay:function(data){
				var len = data.length;
				if(len==0){
					layer.msg('请选择付款计划',{icon:7});
					return;
				}
				var supplerIds = [];
				var orderIds = [];
				var planIds = [];
				var orderNos = [];
				var orderOrg = [];
				for(var index in data){
					var paymentId = data[index]['PAYMENT_ID'];
					if(paymentId==''||paymentId==undefined){
						supplerIds.push(data[index]['SUPPLIER_ID']);
						orderIds.push(data[index]['ORDER_ID']);
						planIds.push(data[index]['PLAN_ID']);
						orderNos.push(data[index]['ORDER_NO']);
						orderOrg.push(data[index]['ORDER_ORG']);
					}
				}
				var dd = arrayUnique(supplerIds);
				var ss = arrayUnique(orderIds);
				var zz = arrayUnique(orderOrg);
				if(dd.length>1){
					layer.msg('只能选一个供应商发起付款');
					return;
				}
				if(zz.length>1){
					layer.msg('只能选一个采购组织发起付款');
					return;
				}
				if(planIds.length==0){
					layer.msg('请选择未申请付款的计划',{icon:7});
					return;
				}
				var supplierId = data[0]['SUPPLIER_ID'];
				var orderOrgName = orderOrg.join();
				var flowCode = 'order_payment';
				if(orderOrgName=='中融'){
					flowCode = 'order_payment_zr';
				}
				var data = {supplierId:supplierId,orderIds:orderIds.join(),planIds:planIds.join(),orderNos:orderNos.join(),flowCode:flowCode,orderOrgName:orderOrgName,handle:'add',goUrl:''};
				popup.openTab({id:'applyPayment',full:true,scrollbar:false,area:['750px','600px'],offset:'20px',url:'/yq-work/web/flow',title:'发起付款申请',data:data});
			},
			typeList:function(){
				popup.openTab({id:'payTypeQuery',title:'付款方式查询',url:'${ctxPath}/pages/erp/order/pay/type/type-query.jsp',data:{}});
			}
		}
		
		function reloadDataList(){
			DataMgr.query();
		}
		
		$(function(){
			$("#dataMgrForm").render({success:function(){
				DataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>