package com.yunqu.work.dao.other;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="ExcelDao")
public class ExcelDao extends AppDaoContext {

	@WebControl(name="excelInfo",type=Types.RECORD)
	public JSONObject excelInfo(){
		String excelId =param.getString("excelId");
		if(StringUtils.isBlank(excelId)){
			return getJsonResult(new JSONObject());
		}
		return queryForRecord("select * from yq_excel where excel_id = ?",excelId);
	}
	@WebControl(name="excelMgr",type=Types.LIST)
	public JSONObject excelMgr(){
		EasySQL sql=getEasySQL("select * from yq_excel");
		sql.append(param.getString("fkId"), "and fk_id = ?");
		String authFlag=param.getString("authFlag");
		if(authFlag.length()==1){
			sql.append(authFlag,"and auth_flag = ?");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	
}
