<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<!DOCTYPE html>
<html lang="zh-CN" class="no-js">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="renderer" content="webkit"> 
        <link rel="icon" type="image/x-icon" href="${ctxPath}/favicon.ico">
         <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
        <meta content="IE=EmulateIE8" http-equiv="X-UA-Compatible">
        <meta content="yes" name="apple-mobile-web-app-capable">
        <meta content="black" name="apple-mobile-web-app-status-bar-style">
        <meta name="robots" content="index,follow">
        <meta http-equiv="pragma" content="no-cache">
 		<meta http-equiv="cache-control" content="no-cache">
	 	<meta http-equiv="expires" content="0"> 
		<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
		<link rel="stylesheet" href="/yq-work/pages/fly/static/global.css">
        <script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script> 
        <EasyTag:block name="head"></EasyTag:block>
    </head>
<body>
		 <div class="fly-header layui-bg-black">
		    <div class="layui-container">
		      <a class="fly-logo" href="/yq-work/fly/index">
		        <img src="/yq-work/pages/fly/static/logo.png" style="height: 37px;" alt="layui">
		      </a>
		      <ul class="layui-nav fly-nav layui-hide-xs">
		        <li class="layui-nav-item layui-this">
		          <a href="/yq-work/fly/index"><i class="iconfont icon-jiaoliu"></i>知识库</a>
		        </li>
		        <li class="layui-nav-item">
		          <a href="/yq-work/fly/index"><i class="iconfont icon-iconmingxinganli"></i>培训</a>
		        </li>
		        <li class="layui-nav-item">
		          <a href="/yq-work/fly/index"><i class="iconfont icon-ui"></i>产品</a>
		        </li>
		      </ul>
		      <ul class="layui-nav fly-nav-user">
		       <li class="layui-nav-item">
		         <a class="fly-nav-avatar" href="javascript:;">
		          <cite class="layui-hide-xs">${staffInfo.userName}</cite>
		          <i class="iconfont icon-renzheng layui-hide-xs"></i>
		          <i class="layui-badge fly-badge-vip layui-hide-xs">VIP${staffInfo.level}</i>
		          <img src="${staffInfo.picUrl}" onerror="this.src='/yq-work/static/images/user-avatar-large.png'">
		        </a>
		        <dl class="layui-nav-child">
		          <dd><a href="/yq-work/fly/my"><i class="layui-icon">&#xe620;</i>用户中心</a></dd>
		          <dd><a href="/yq-work/fly/message"><i class="iconfont icon-tongzhi" style="top: 4px;"></i>我的消息</a></dd>
		          <dd><a href="/yq-work/fly/u"><i class="layui-icon" style="margin-left: 2px; font-size: 22px;">&#xe68e;</i>我的主页</a></dd>
		          <hr style="margin: 5px 0;">
		          <dd><a href="/workbench/sso?action=logout" style="text-align: center;">退出</a></dd>
		        </dl>
		      </li>
		      </ul>
		    </div>
		  </div>
	     <div class="fly-panel fly-column">
			    <div class="layui-container">
			      <ul class="layui-clear">
			        <li class="layui-hide-xs"><a href="/yq-work/fly/index">首页</a></li>
			        <c:forEach items="${itemList}" var="item">
				        <li><a href="/yq-work/fly/jie?category=${item.id}">${item.title}<c:if test="${item.title=='分享' }"><span class="layui-badge-dot"></span></c:if></a></li>
			        </c:forEach>
			        <li class="layui-hide-xs layui-hide-sm layui-show-md-inline-block"><span class="fly-mid"></span></li>
			        <li class="layui-hide-xs layui-hide-sm layui-show-md-inline-block"><a href="/yq-work/fly/my">我发表的贴</a></li>
			        <li class="layui-hide-xs layui-hide-sm layui-show-md-inline-block"><a href="/yq-work/fly/my#collection">我收藏的贴</a></li>
			      </ul>
			      <div class="fly-column-right layui-hide-xs">
			        <span class="fly-search"><i class="layui-icon"></i></span>
			        <a href="/yq-work/fly/add" class="layui-btn">发表新帖</a>
			      </div>
			      <div class="layui-hide-sm layui-show-xs-block" style="margin-top: -10px; padding-bottom: 10px; text-align: center;">
			        <a href="/yq-work/fly/add" class="layui-btn">发表新帖</a>
			      </div>
			    </div>
		 </div>
	     
	     <EasyTag:block name="content"></EasyTag:block>
	     
	    <div class="fly-footer">
		    <p>
		    	<a href="/yq-work/fly/index" target="_blank">云趣乐享</a> 2024 &copy; 
		    	<a href="https://www.getxf.cn/" target="_blank">getxf.cn 出品</a>
		    </p>
		    <p>
		      <a href="http://*************/" target="_blank">协同办公</a>
		      <a href="/yq-work/fly/index" target="_blank">企业云盘</a>
		      <a href="/yq-work/fly/index" target="_blank">微信公众号</a>
		    </p>
		  </div>
  
		<script type="text/javascript" src="//at.alicdn.com/t/font_1002403_o92go0iokf.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"></script>
		<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
		<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
		<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=2020515"></script>
		<script type="text/javascript" src="/easitline-static/lib/layui/layui.js"></script>
  	    <EasyTag:block name="script"></EasyTag:block>
  	    
  	    <%--  <script>
			var _hmt = _hmt || [];
			_hmt.push(['_setCustomVar', 1, 'login', '<%=request.getRemoteUser()%>', 3]);
			
			(function() {
			  var hm = document.createElement("script");
			  hm.src = "https://hm.baidu.com/hm.js?3074806290f6205dfc5b78dcaf6349d9";
			  var s = document.getElementsByTagName("script")[0]; 
			  s.parentNode.insertBefore(hm, s);
			})();
			
			document.addEventListener('WeixinJSBridgeReady', function onBridgeReady() {
			    WeixinJSBridge.call('hideOptionMenu');
			});
			
		</script>
		
		<script src="https://sdk.51.la/perf/js-sdk-perf.min.js" crossorigin="anonymous"></script>
		<script>
		  new LingQue.Monitor().init({id:"Jqro5mZGAFXIu5mH"});
		</script> --%>
  	    
</body>
</html>