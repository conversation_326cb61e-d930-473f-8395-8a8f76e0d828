$(function(){
	
	ajax.remoteCall("/yq-work/webcall?action=CommonDao.myUserInfo",{},function(result){
		var data = result.data;
		localStorage.setItem("yq_userId",data.userId);
		localStorage.setItem("deptId",data.deptId);
		
		layui.data('yqwork',{key:'staffInfo',value:data});
		layui.data('yqwork',{key:'userId',value:data.userId});
		layui.data('yqwork',{key:'deptId',value:data.deptId});
		
		return data.userId;
	},{async:false});
		
	localStorage.setItem("devLead",devLead);
	localStorage.setItem("deptId",deptId);
	localStorage.setItem("roles",roles);
	pwd=pwd.toUpperCase();
	if('E10ADC3949BA59ABBE56E057F20F883E'==pwd||'E91AD915BA65B8DE9FD53CAFEB9FDD95'==pwd){
		layer.alert('密码较弱,请立即修改密码!',{icon:7,time:30000,offset:'auto',shade:0,shadeClose:true},function(index){
			layer.close(index);
			popup.openTab({url:'/yq-admin/servlet/user?action=userModPwd',title:'修改密码',id:'pwd'});
		});
	}
    $(".render-1").render({data:{homePage:1}});
    $(".render-2").render({data:{homePage:1}});
    $(".render-3").render({data:{homePage:1},success:function(){
		 var len = $('#carousel-nav a').length;
		 if(len>0){
		 	 var carousel = layui.carousel;
			 var carInst = carousel.render({
				width:'100%',height:'70px',arrow: 'none',anim: 'fade',
			    elem: '#carousel-nav'
			 });
		 }else{
			$('#carousel-nav').parent().parent().remove();
		}
	}});
	loadSchedule();
	//if(locationCompany()){
		 initBookFoodData();
	//}else{
		// $(".dc").remove();
	//}
	
	loadFlowNum();
	
	//10分钟刷新一次
	setTimeout(loadFlowNum, 1000*60*10);
	
	openWx();
	
	var pushMsgFlag=sessionStorage.getItem("pushMsgFlag");
	if(pushMsgFlag!='ok'){
		pushMsg();
	}
	//layer.alert('云趣文档协作平台发布了doc.yunqu-info.com',{shade:0,time:20000,title:'提醒',icon:6,offset:'rb',btn:['立即前往'],yes:function(){
	//	layer.msg("账号:"+loginAcct+"默认密码：yunqu168",{shade:0,offset:'20px',time:2000,icon:1},function(){
	//		window.open('http://doc.yunqu-info.com');
	//	});
	//}});
	
	layui.element.on('tab(flowFilter)', function(){
		var val =  this.getAttribute('data-val');
		$("[data-ref-flow='"+val+"']").render({data:{homePage:'1'}});
    });

	
	$('#joinDateDesc').html(diffDate($('#joinDate').text()));
	
	/*try{
		$.get('https://v1.hitokoto.cn/?c=k',function(json){
			var html = getTimeState() + "，"+ json['hitokoto']+" -- "+json['from'];
			html = cutText(html,50);
			console.log(html);
			$('#todaySay').html('<a href="https://api.vvhan.com/api/zhichang" target="_blank">'+html+'</a>');
		});
	}catch(e){
		console.error(e);
	}*/
	
	ajax.remoteCall("/yq-work/servlet/workbench?action=emailNewCount",{},function(result) {
		$('#emailNum').text(result.data);
	});
	
	ajax.remoteCall("/yq-work/webcall?action=AffairDao.receivedCount",{},function(result) {
		$('#affairNum').text(result.data);
		if(result.data>0){
			//window.open('/yq-work/affair','affair');
		}
	});
	
	ajax.remoteCall("/yq-work/servlet/workbench?action=todayInfo",{},function(result) {
		if(result.state==1){
			var data = result.data.data;
			fillRecord(data,'',',','.todayInfo');
		}
	});
	
});

function getTimeState(){
    // 获取当前时间
    var timeNow = new Date();
    // 获取当前小时
    var hours = timeNow.getHours();
    // 设置默认文字
    var text = '';
    // 判断当前时间段
    if (hours >= 0 && hours <= 10) {
        text = '早上好';
    } else if (hours > 10 && hours <= 14) {
        text = '中午好';
    } else if (hours > 14 && hours <= 18) {
        text = '下午好';
    } else if (hours > 18 && hours <= 24) {
        text = '晚上好';
    }
    return "<span class='text-danger'>"+text+"</span>";
};

function kqSign(type){
	top.layui.admin.chooseLocation({
    needCity: true,
	defaultZoom:16,
	keywords:'写字楼,小区',
	title:'选择考勤地点',
    onSelect: function (res) {
       		console.log(JSON.stringify(res));
			res['signTimeType'] = type;
			ajax.remoteCall("/yq-work/servlet/kaoqin?action=manualSign",res,function(result) { 
				if(result.state == 1){
					layer.msg('操作成功', {icon: 1});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
	    }
  });
}


function kqTop(){
	popup.openTab({id:'deptKq',url:'/yq-work/pages/ehr/kq-dept-stat.jsp',title:'考勤数据榜',data:{}});
}

function flowDetail(id,resultId,type){
	ajax.remoteCall("/yq-work/webcall?action=FlowDao.baseInfo",{businessId:id,resultId:resultId},function(result){
		var data = result.data;
		var flowCode = data['flowCode'];
		if(type=='myFlowtodo'){
			var readTime = data['readTime'];
			if(readTime==''){
				ajax.remoteCall("/yq-work/servlet/flow/approve?action=addReadTime",{resultId:resultId},function(result) {});
			}
			var json  = $.extend({},{businessId:data['applyId'],flowCode:flowCode,isApprovalFlag:'1',handle:'approval',resultId:resultId});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:json});
			
		}else if(type=='myFlowCC'){
			var json  = $.extend({resultId:resultId},{businessId:data['applyId'],flowCode:flowCode});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:json});
			
		}else if(type=='myFlowDone'){
			var json  = $.extend({resultId:resultId},{businessId:data['applyId'],flowCode:flowCode});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:json});
			
		}else if(type=='myFlowApply'){
			var applyState = data['applyState'];
			var json  = $.extend({resultId:resultId},{businessId:data['applyId'],flowCode:flowCode,applyState:applyState});
			if(applyState=='0'){
				json['handle'] ='edit';
			}
			popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:json});
			
		}
	});
}

function flowMore(type){
	if(type=='myFlowtodo'){
		popup.openTab({id:'flow_my_todo',title:'我的待办',url:'/yq-work/pages/flow/my/flow-todo.jsp',reload:true});
	}else if(type=='myFlowDone'){
		popup.openTab({id:'flow_my_done',title:'已办流程',url:'/yq-work/pages/flow/my/flow-done.jsp',reload:true});
	}else if(type=='myFlowApply'){
		popup.openTab({id:'flow_my_apply',title:'我的申请',url:'/yq-work/pages/flow/my/flow-list.jsp',reload:true});
	}else if(type=='myFlowCC'){
		popup.openTab({id:'flow_cc',title:'抄送流程',url:'/yq-work/pages/flow/my/flow-cc.jsp',reload:true});
	}
}

function applyFlow(flowCode){
	if(flowCode==''){
		popup.openTab({url:'/yq-work/pages/flow/flow-apply.jsp',title:'发起流程',id:'flow_apply'})
		return;
	}
	ajax.remoteCall("/yq-work/web/flow/getFlow",{flowCode:flowCode},function(result) { 
		if(result.state == 1){
			var data = result.data;
			var title = data.flowName;
			popup.openTab({id:flowCode,url:'/yq-work/web/flow?flowCode='+flowCode,title:title,data:{handle:'add'}});
		}else{
			layer.alert(result.msg,{icon: 5});
		}
	});
 }		
		
function diffDate(date1) {
    let begin = new Date(date1);
    let end = new Date();

    // 如果开始时间晚于结束时间，交换日期，并记录交换状态
    let swap = false;
    if (begin > end) {
        let tmp = begin;
        begin = end;
        end = tmp;
        swap = true;
    }

    // 分别取二个日期的年、月、日值
    let d1 = begin.getDate();
    let m1 = begin.getMonth() + 1;
    let y1 = begin.getFullYear();
    let d2 = end.getDate();
    let m2 = end.getMonth() + 1;
    let y2 = end.getFullYear();

    // 获取每个月的天数，这里要注意一下闰年的2月
    var getMonthDays = function (y, m) {
        var aMonthDays = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
        if (m == 2 && ((y % 400 == 0) || (y % 4 == 0 && y % 100 != 0))) {
            return 29;
        }
        return aMonthDays[m];
    };

    let y, m, d
    let tempD = 0
    let tmpM = 0
    let tmpY = 0

    // 计算日，不足时向月份借
    if (d2 >= d1) {
        d = d2 - d1
    } else {
        tmpM = -1
        d = getMonthDays(y1, m1) + d2 - d1
    }

    // 计算月，不足时向年份借
    if (m2 + tmpM >= m1) {
        m = m2 + tmpM - m1
    } else {
        tmpY = -1
        m = 12 + m2 + tmpM - m1
    }

    // 计算年
    y = y2 + tmpY - y1

    // 拼接距离字符串 输出格式如："5天"， "5月 05天"， "5年 05月 05日"
    let str = "";
    if (y > 0) {
        str = y + '年 ' + ("0" + m).substr(-2) + "月 " + ("0" + d).substr(-2) + '天 '
    } else if (m > 0) {
        str = m + "月 " + ("0" + d).substr(-2) + '天 '
    } else if (d > 0) {
        str = d + '天 '
    }

    // 完整输出
    return str;
}


function loadSchedule(){
	layui.use('laydate', function(){
  		var laydate = layui.laydate;
		laydate.render({
	   		 elem: '#schedule',
	    	 position: 'static',
			 calendar: true,
			 mark:{
				
			},done:function(value, date){
				
			}
	 	 });
	});
}

function pushMsg(){
	ajax.remoteCall(ctxPath+"/servlet/workbench?action=pushMsg",{},function(result) { 
		sessionStorage.setItem("pushMsgFlag",'ok');
	},{loading:false,error:function(e){
		
	}});
}


var signHide = true;
function signShow(el){
	if(signHide){
		$(el).text('收起');
		$('.moreSign').css('display','inline-block');
		signHide = false;
	}else{
		$(el).text('展开');
		$('.moreSign').css('display','none');
		signHide = true;
	}
	
}
function openWx(){
	if(''==openId){
		bindWx();
	}
}
function bindWx(){
	$('#codeContainer').html("");
	$('#codeContainer').qrcode({width:180,height:180,text:"http://work.yunqu-info.cn/yq-work/api/getAuthorize?userId="+userId});
	layer.open({type:1,title:'绑定微信服务号',offset:'30px',shade:0,area:['300px','350px'],shadeClose:false,closeBtn:0,btn:['我已扫码','稍后再试'],content:$("#qcode")});
}
function bindOA(type){
	popup.layerShow({type:1,anim:0,scrollbar:false,offset:'30px',area:['300px','250px'],url:ctxPath+'/pages/oa/oa-acount.jsp',data:{type:type},title:'OA账号绑定'});
}
function openMyAllWf(){
	popup.openTab({url:ctxPath+'/pages/oa/my-all-wf.jsp',title:'我发起的流程'});
}
function openMydoWf(){
	popup.openTab({url:ctxPath+'/pages/oa/my-do-wf.jsp',title:'待我审批的流程'});
}

function fullShow(){
	var device = layui.device();
	var height = $(window).height();
    if(device.mobile||height<600){
       return true;
    }else{
    	return false;
    }
}
function zendao(){
	if(locationCompany()){
		window.open(ctxPath+'/sso.jsp');
	}else{
		window.open('http://work.yunqu-info.cn/yq-work/sso.jsp');
	}
}
function zendaoNum(data){
	var num=data.length;
	if(num > 0){
		$("#zendaoNum").append('<span class="layui-badge-dot layui-bg-blue"></span>');
	}
	return '';
}
function visitNum(data){
	var num=data.length;
	if(num > 0){
		$("#visitNum").html('('+num+')');
	}
	return '';
}
function addressList(){
	popup.openTab({id:'addressList',type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:ctxPath+'/pages/ehr/address-list.jsp',title:'通讯录',data:{}});
}
function kanban(){
	popup.openTab({id:'kanban',url:ctxPath+'/pages/kanban/kanban-index.jsp',title:'我的看板'});
}

function myFa(){
	popup.openTab({id:'myFa',url:ctxPath+'/pages/ehr/fa/fa-query.jsp',title:'我的固定资产'});
}

function kqMore(type){
	if(type=='1'){
		popup.openTab({id:'kqDataQuery',url:ctxPath+'/pages/ehr/kq-query-wx.jsp',title:'考勤查询'});
	}else{
		popup.openTab({id:'kqDataQuery',url:ctxPath+'/pages/ehr/kq-query.jsp',title:'考勤查询'});
	}
}
function moreNote(){
	popup.openTab({id:'note',url:ctxPath+'/pages/note/note-list.jsp',title:'我的笔记'});
}
function moreDoc(){
	popup.openTab({url:ctxPath+'/pages/doc/doc-list.jsp',title:'文档'});
}
function newMsg(){
	popup.openTab({url:ctxPath+'/pages/my/message-list.jsp',title:'待办事项'});
}
function moreNav(){
	popup.openTab({url:ctxPath+'/pages/nav/user-nav-mgr.jsp',title:'我的网址'});
}
function moreTw(){
	popup.openTab({url:ctxPath+'/pages/remind/remind-mgr.jsp',data:{type:0},title:'我的动弹'});
}
function moreTodo(){
	popup.openTab({type:1,full:fullShow(),maxmin:true,area:['900px','650px'],url:ctxPath+'/pages/todo/my-todo-list.jsp',title:'我的日程'});
}
function moreTask(){
	popup.openTab({id:'task',url:ctxPath+'/pages/task/my-task-list.jsp?status=1',title:'我负责的任务'});
}
function reloadNav(){
	$(".nav-card").render();;
}
function reloadTaskList(){
	$(".taskList").render();;
}
function weekly(){
	popup.openTab({title:'我的周报',url:ctxPath+'/pages/weekly/weekly-index.jsp'})
}

function addWeekly(){
	popup.openTab({title:'周报填写',url:ctxPath+'/pages/weekly/weekly-my.jsp?isDiv=0'})
}

function remindDetail(remindId,remindType){
	//popup.openTab({id:"remindDetail",url:ctxPath+'/fly/detail/'+remindId,title:'详情',data:{remindId:remindId,remindType:remindType}});
	window.open(ctxPath+'/fly/detail/'+remindId+"?source=index");
}
function docDetail(docId,folderId){
	window.open(ctxPath+'/fly/docs/'+docId);
	//popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:ctxPath+'/pages/doc/doc-detail.jsp',title:'详情',data:{op:'detail',docId:docId,folderId:folderId}});
}
function openNotices(){
	popup.openTab({id:'reminds',url:ctxPath+'/pages/remind/remind-list.jsp?type=1',title:'通知公告'});
}
function taskDetail(taskId,taskState){
	popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:ctxPath+'/pages/task/task-detail.jsp',title:'处理',data:{taskId:taskId,taskState:taskState,status:1}});
}
function zentao(id){
	window.open('http://172.16.68.152:6688/index.php?m=bug&f=view&bugID='+id);
}
function addTwitter(){
	var width=$(window).width();
	var fullFlag=false;
	if(width<700){
		fullFlag=true;
	}
	popup.layerShow({type:1,full:fullFlag,closeBtn:0,anim:0,scrollbar:false,offset:'20px',area:['450px','440px'],url:ctxPath+'/pages/remind/twitter.jsp',title:false});
}
function reloadTwitter(){
	$(".tw-card").render();
}
function addUserNav(){
	popup.layerShow({type:1,anim:0,scrollbar:false,offset:'20px',area:['450px','400px'],url:ctxPath+'/pages/nav/user-nav-edit.jsp',title:'网址导航'});
}
function userNavDetail(id){
	popup.layerShow({type:1,anim:0,scrollbar:false,offset:'20px',area:['400px','350px'],url:ctxPath+'/pages/nav/user-nav-detail.jsp',data:{navId:id},title:'访问链接'});
}
function projectDetail(title,id){
	title = cutText(title,10,'');
	projectBoard(id);
}
function myProject(){
	popup.openTab({id:'myProject',url:ctxPath+'/pages/project/my-project-list.jsp?status=2',title:'我参与的项目',data:{}});
}
function getTwContent(val){
	//val = val.replaceAll("img src","img data-src")
	return val;
}

function getDateDes(dateStr){
	return getDateDiff(getDateTimeStamp(dateStr));
}
function getDateTimeStamp(dateStr){
	 return Date.parse(dateStr.replace(/-/gi,"/"));
}
function getDateDiff(dateTimeStamp){
	var minute = 1000 * 60;
	var hour = minute * 60;
	var day = hour * 24;
	var halfamonth = day * 15;
	var month = day * 30;
	var now = new Date().getTime();
	var diffValue = now - dateTimeStamp;
	if(diffValue < 0){return;}
	var monthC =diffValue/month;
	var weekC =diffValue/(7*day);
	var dayC =diffValue/day;
	var hourC =diffValue/hour;
	var minC =diffValue/minute;
	if(monthC>=1){
		result="" + parseInt(monthC) + "月前";
	}
	else if(weekC>=1){
		result="" + parseInt(weekC) + "周前";
	}
	else if(dayC>=1){
		result=""+ parseInt(dayC) +"天前";
	}
	else if(hourC>=1){
		result=""+ parseInt(hourC) +"小时前";
	}
	else if(minC>=1){
		result=""+ parseInt(minC) +"分钟前";
	}else
	result="刚刚";
	return result;
}