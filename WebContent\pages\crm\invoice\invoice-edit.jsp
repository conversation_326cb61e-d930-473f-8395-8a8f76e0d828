<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="InvoiceEditForm" class="form-horizontal" data-mars="InvoiceDao.record" autocomplete="off" data-mars-prefix="invoice.">
		<input type="hidden" value="${param.invoiceId}" name="invoiceId"/>
		<input type="hidden" value="${param.invoiceId}" name="invoice.INVOICE_ID"/>
		<fieldset class="content-title">
			<legend><span class="layui-badge-rim">1</span> 基本信息</legend>
		</fieldset>
		<table class="table table-edit table-vzebra">
			<tbody>
			<tr>
				<td width="100px" class="required">发票申请编号</td>
				<td style="width: 40%">
					<input data-rules="required"  type="text" name="invoice.INVOICE_NO" class="form-control input-sm">
				</td>
				<td width="100px" class="required">客户名称</td>
				<td style="width: 40%">
					<input type="hidden" value="${param.custNo}" name="invoice.CUST_NO">
					<input type="hidden" value="${param.custId}" name="invoice.CUST_ID" id="custId">
					<input type="text" value="${param.custName}" onclick="singleCust(this);" readonly="readonly" placeholder="请点击选择客户" name="invoice.CUST_NAME" class="form-control input-sm">
				</td>
			</tr>
			<tr>
				<td class="required">合同编号</td>
				<td>
					<input value="${param.contractNo}" name="invoice.CONTRACT_NO" type="hidden"/>
					<input value="${param.contractId}" name="invoice.CONTRACT_ID" type="hidden"/>
					<input value="${param.contractName}" type="text" onclick="singleContract(this);" readonly="readonly" name="invoice.CONTRACT_NAME" class="form-control input-sm">
				</td>
				<td>合同金额</td>
				<td>
					<input value="${param.contractAmount}" type="text" readonly="readonly" name="contractAmount" class="form-control input-sm">
				</td>
			</tr>
			<tr>
				<td>业务平台</td>
				<td>
					<input type="hidden" name="invoice.PLATFORM_ID" value="${param.platformId}"/>
					<input type="text" name="invoice.PLATFORM_NAME" value="${param.platformName}" class="form-control input-sm" readonly onclick="singleBusinessPlatForm(this)" placeholder="（仅运营类合同需填写）"/>
				</td>
				<td>开票流程 <i class="layui-icon layui-icon-tips" lay-tips="当一个流程开多张票时，每张发票信息都可以关联这个流程。"></i></td>
				<td>
					<input type="hidden" id="bxApplyId" name="invoice.KP_APPLY_ID"/>
					<input type="text" name="invoice.APPLY_TITLE" data-flow-code="finance_kp" readonly="readonly" placeholder="关联流程" onclick="FlowCore.selectRelatedFlow(this,'radio');" class="form-control input-sm"/>
				</td>
				</td>
			</tr>
			<tr>
				<td>对应合同阶段</td>
				<td>
					<SELECT name="invoice.STAGE_ID" class="form-control">
						<option value="">请先选择合同</option>
					</SELECT>
				</td>
				<td class="required">开票日期</td>
				<td>
					<input type="text" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="invoice.MARK_DATE" class="form-control input-sm Wdate">
				</td>
			</tr>
			<tr>
				<td class="required">开票类型</td>
				<td>
					<select data-rules="required" data-mars="YqAdmin.DictDao.select('Y009')" name="invoice.INVOICE_TYPE"  class="form-control input-sm text-val">
						<option value="">请选择</option>
					</select>
				</td>
				<td class="required">开票金额（元）</td>
				<td>
					<input type="number" data-rules="required" name="invoice.MARK_MONEY" class="form-control input-sm" value="0" oninput="InvoiceEit.calcTax()">
				</td>
			</tr>
			<tr>
				<td class="required">收入类型</td>
				<td>
					<SELECT name="invoice.PAYMENT_TYPE" data-mars="ContractPaymentDao.getPaymentType()" class="form-control" onchange="InvoiceEit.getTaxRate(this.value)">
						<option value="">请选择</option>
					</SELECT>
					<select style="display: none" id="invoiceEditTaxRate" data-mars="ContractPaymentDao.getPaymentRate()">
						<option value="0" selected></option>
					</select>
				</td>
				<td class="required">税率</td>
				<td>
					<input data-rules="required" type="text" name="invoice.TAX_RATE" class="form-control input-sm" oninput="InvoiceEit.calcTax()" placeholder="选择类型后自动填写">
				</td>
			</tr>
			<tr>
				<td>税额</td>
				<td>
					<input type="text" name="invoice.TOTAL_TAX" class="form-control input-sm" placeholder="选择收入类型并输入开票金额后自动计算">
				</td>
				<td>不含税总额</td>
				<td>
					<input type="text" name="invoice.AMOUNT_IN_FIGUERS" class="form-control input-sm" placeholder="选择收入类型并输入开票金额后自动计算">
				</td>
			</tr>
			<tr>
				<td>备注</td>
				<td colspan="3">
					<textarea style="height: 80px;" class="form-control input-sm" name="invoice.REMARK"></textarea>
				</td>
			</tr>
			</tbody>
		</table>
		<fieldset class="content-title">
			<legend><span class="layui-badge-rim">2</span>抬头信息</legend>
		</fieldset>
		<table class="table table-edit table-vzebra">
			<tbody>
			<tr style="display: none">
				<td>
					<textarea id="textJson" class="form-control input-sm" name="invoice.TEXT_JSON"></textarea>
				</td>
			</tr>
			<tr>
				<td width="100px" class="required">开票抬头</td>
				<td style="width: 40%">
					<input name="title.TITLE_ID" type="hidden">
					<input name="invoice.ENT_TITLE_ID" type="hidden">
					<input type="text" name="title.TITLE_NAME" onclick="invoiceSelectTitle()" class="form-control input-sm" placeholder="点击选择抬头">
				</td>
				<td width="100px" class="required">纳税人识别号</td>
				<td style="width: 40%">
					<input type="text" name="title.TAX_NO" class="form-control input-sm"/>
				</td>
			</tr>
			<tr>
				<td class="required">开户行</td>
				<td>
					<input type="text" name="title.BANK_NAME" class="form-control input-sm">
				</td>
				<td>开户账号</td>
				<td>
					<input type="text" readonly="readonly" name="title.BANK_NO" class="form-control input-sm">
				</td>
			</tr>
			<tr>
				<td>开票地址</td>
				<td>
					<input type="text" name="title.ADDRESS" class="form-control input-sm">
				</td>
				<td>电话</td>
				<td>
					<input type="text" name="title.TEL" class="form-control input-sm">
				</td>
			</tr>
			</tbody>
		</table>
		<fieldset class="content-title">
			<legend><span class="layui-badge-rim">3</span> 邮寄信息</legend>
		</fieldset>
		<table class="table table-edit table-vzebra">
			<tbody>
			<tr style="display: none">
				<td>
					<textarea id="textJson2" class="form-control input-sm" name="invoice.MAIL_JSON"></textarea>
				</td>
			</tr>
			<tr>
				<td width="100px">联系人</td>
				<td style="width: 40%">
					<input type="text" name="mail.CONTACTS" class="form-control input-sm">
				</td>
				<td width="100px">联系方式</td>
				<td style="width: 40%">
					<input type="text" name="mail.CONTACTS_TEL" class="form-control input-sm"/>
				</td>
			</tr>
			<tr>
				<td>邮寄地址</td>
				<td colspan="3">
					<input type="text" name="mail.MAIL_ADDRESS" class="form-control input-sm">
				</td>
			</tr>
			</tbody>
		</table>
		<br>
		<p class="layer-foot text-c">
			<button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="InvoiceEit.ajaxSubmitForm()"> 保 存 </button>
			<button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
		</p>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/yq-work/static/js/flow.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">

		jQuery.namespace("InvoiceEit");

		InvoiceEit.invoiceId = '${param.invoiceId}';
		InvoiceEit.contractId = '${param.contractId}';
		InvoiceEit.stageId = '';
		InvoiceEit.editSource = '${param.editSource}';
		var invoiceId='${param.invoiceId}';

		$(function(){
			$("#InvoiceEditForm").render({success:function(result){
					var dataJsonStr = $('#textJson').val();
					if(dataJsonStr){
						var textJson = eval("("+dataJsonStr+")");
						fillRecord(textJson,"title.",",","#InvoiceEditForm");
					}
					var dataJsonStr2 = $('#textJson2').val();
					if(dataJsonStr2){
						var textJson = eval("("+dataJsonStr2+")");
						fillRecord(textJson,"mail.",",","#InvoiceEditForm");
					}
					if(!$.isEmptyObject(result['InvoiceDao.record'])) {
						if(!$.isEmptyObject(result['InvoiceDao.record']['data'])){
							InvoiceEit.stageId = result['InvoiceDao.record']['data']['STAGE_ID'];
						}
					}
					if($('[name="invoice.CONTRACT_ID"]').val().trim()!="" || $('[name="invoice.CONTRACT_ID"]').val().length != 0){
						InvoiceEit.selectStageId($('[name="invoice.CONTRACT_ID"]').val());
					}

				}});
		});


		InvoiceEit.ajaxSubmitForm = function(){
			if(form.validate("#InvoiceEditForm")){
				if(InvoiceEit.invoiceId){
					InvoiceEit.updateData();
				}else{
					InvoiceEit.insertData();
				}
			};
		}
		InvoiceEit.insertData = function(flag) {
			var data = form.getJSONObject("#InvoiceEditForm");
			// delete data['invoice.INVOICE_ID'];
			data['invoice.TEXT_JSON']= getTitleVal(data);
			data['invoice.MAIL_JSON']= getMailVal(data);
			ajax.remoteCall("${ctxPath}/servlet/invoice?action=add",data,function(result) {
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						if(InvoiceEit.editSource == 'contract'){
							reloadContractInvoiceList();
						}else {
							reloadInvoiceList();
						}
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		InvoiceEit.updateData = function(flag) {
			var data = form.getJSONObject("#InvoiceEditForm");
			data['invoice.TEXT_JSON']= getTitleVal(data);
			data['invoice.MAIL_JSON']= getMailVal(data);
			ajax.remoteCall("${ctxPath}/servlet/invoice?action=update",data,function(result) {
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						if(InvoiceEit.editSource == 'contract'){
							reloadContractInvoiceList();
						}else {
							reloadInvoiceList();
						}
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

		InvoiceEit.getTaxRate = function(paymentType){
			$("#invoiceEditTaxRate").val(paymentType);
			var taxRate = $("#invoiceEditTaxRate option:selected").text();
			$("input[name='invoice.TAX_RATE']").val(taxRate);
			InvoiceEit.calcTax();
		}

		InvoiceEit.calcTax = function (){
			var taxRate = parseFloat($("#invoiceEditTaxRate option:selected").text());
			if(isNaN(taxRate)){
				taxRate = 0;
			}

			var amount = $("#InvoiceEditForm input[name='invoice.MARK_MONEY']").val();
			var amountNoTax = amount * 100 / (100 + taxRate);
			$("input[name='invoice.AMOUNT_IN_FIGUERS']").val(amountNoTax.toFixed(2));

			var tax = amountNoTax * taxRate / 100 ;
			$("input[name='invoice.TOTAL_TAX']").val(tax.toFixed(2));
		}

		InvoiceEit.selectStageId = function(contractId){
		var stageId=InvoiceEit.stageId;
			$('select[name="invoice.STAGE_ID"]').attr('data-mars','ContractStageDao.selectStage');
			$('select[name="invoice.STAGE_ID"]').render({data:{contractId:contractId},success:function(result){
					$('select[name="invoice.STAGE_ID"] option:first').text("请选择");
					if(stageId != null && stageId != ""){
						$('select[name="invoice.STAGE_ID"]').val(stageId);
					}
				}});
		}

		function invoiceSelectTitle(){
			var id = new Date().getTime();
			var custName = $("[name='invoice.CUST_NAME']").val();
			popup.layerShow({full:fullShow(),scrollbar:false,area:['700px','500px'],offset:'20px',title:'选择抬头',url:'${ctxPath}/pages/crm/invoice/title/title-select.jsp',data:{sid:id,type:'radio',custName:custName}});
		}

		function SelectTitleCallBack(row){
			$("[name='title.TITLE_ID']").val(row['TITLE_ID']);
			$("[name='invoice.ENT_TITLE_ID']").val(row['TITLE_ID']);
			$("[name='title.TITLE_NAME']").val(row['TITLE_NAME']);
			$("[name='title.TAX_NO']").val(row['TAX_NO']);
			$("[name='title.BANK_NO']").val(row['BANK_NO']);
			$("[name='title.BANK_NAME']").val(row['BANK_NAME']);
			$("[name='title.ADDRESS']").val(row['ADDRESS']);
			$("[name='title.TEL']").val(row['TEL']);
		}

		function getTitleVal(formData){
			var data = $.extend({},formData);
			var json = {};
			for(var key in data){
				if(key.indexOf("title.")>-1){
					var newKey = key.replace('title.','');
					json[newKey] = data[key];
				}
			}
			return JSON.stringify(json);
		}

		function getMailVal(formData){
			var data = $.extend({},formData);
			var json = {};
			for(var key in data){
				if(key.indexOf("mail.")>-1){
					var newKey = key.replace('mail.','');
					json[newKey] = data[key];
				}
			}
			return JSON.stringify(json);
		}


		function selctCallBack(id,row){
			$("[name='invoice.CONTRACT_NAME']").val(row['CONTRACT_NAME']);
			$("[name='invoice.CONTRACT_NO']").val(row['CONTRACT_NO']);
			$("[name='invoice.CONTRACT_ID']").val(row['CONTRACT_ID']);
			$("[name='invoice.PLATFORM_NAME']").val(row['PLATFORM_NAME']);
			$("[name='invoice.PLATFORM_ID']").val(row['PLATFORM_ID']);
			$("[name='contractAmount']").val(row['AMOUNT']);
			InvoiceEit.selectStageId(row['CONTRACT_ID'])
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>