package com.yunqu.work.servlet.common;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.string.StringUtils;

import com.jfinal.core.Controller;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.Constants;

public class FilesController extends Controller {

	public void index() {
		HttpServletResponse response = getResponse();
		String url = getPara("url");
		
		String online = getPara("online");
		String fileType = getPara("fileType");
		String fileName = getPara("fileName");
		if(StringUtils.isBlank(fileName)) {
			 fileName = url.substring(url.lastIndexOf("/")+1);  
		}
		if(StringUtils.isBlank(fileType)) {
			fileType = FileKit.getHouzui(fileName);
		}
		String basePath = AppContext.getContext(Constants.APP_NAME).getProperty("basePath", "/home/<USER>/");
		
		String path = basePath+"files/"+url;
		
		File file =new File(path);
		
		if(file.exists()) {
			try {
				if(StringUtils.notBlank(fileType)){
					if(StringUtils.notBlank(fileName)&&"1".equals(online)){
						response.setHeader("Content-disposition","attachment;filename="+toUtf8String(fileName));
					}else {
						response.addHeader("Content-Disposition","inline;filename=" +toUtf8String(fileName));
					}
					if(file!=null){
						response.setHeader("Content-Length",file.length()+"");
					}
					response.setContentType(getContentType(fileType)+";charset=UTF-8");
				}
				FileUtils.copyFile(file, response.getOutputStream());
				renderNull();
			} catch (IOException e) {
				e.printStackTrace();
				renderHtml(e.getMessage());
				renderNull();
			}
		}else {
			try {
				response.sendError(404,"file not exist.");
				renderNull();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		
	}
	
	public void view() {
		String id = getPara(0);
		Record record = Db.findFirst("select * from yq_files where file_id = ?", id);
		if(record==null) {
			renderHtml("文件不存在。");
			return;
		}
		String source = getPara("source", "task");
		
		String diskPath = record.getStr("disk_path");
		diskPath = diskPath.replace("/"+source, "");
		String action = getPara("action");
		String url = null;
		if("download".equals(action)) {
			url = "/yq-work/fileview/"+id;
		}else {
			url = "/fileweb/finder?action=finder."+action+"&host=yq&workspace="+source+"&sub=/&path="+diskPath;
		}
		setAttr("url",url);
		renderJsp("/pages/doc/view.jsp");
	}
	
	
	
	private String toUtf8String(String fn) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < fn.length(); i++) {
			char c = fn.charAt(i);
			if (c >= 0 && c <= 255) {
				sb.append(c);
			} else {
				byte[] b;
				try {
					b = Character.toString(c).getBytes("utf-8");
				} catch (Exception ex) {
					// System.out.println(ex);
					b = new byte[0];
				}
				for (int j = 0; j < b.length; j++) {
					int k = b[j];
					if (k < 0)
						k += 256;
					sb.append("%" + Integer.toHexString(k).toUpperCase());
				}
			}
		}
		return sb.toString();
	}
	public static String getContentType(String filePath){
	    java.nio.file.Path path = Paths.get(filePath);  
	    String contentType = null;  
	    try {  
	        contentType = Files.probeContentType(path);  
	    } catch (IOException e) {  
	        e.printStackTrace();  
	    }
	    return contentType; 
	}
	
}
