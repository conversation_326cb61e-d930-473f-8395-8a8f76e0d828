<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的票夹</title>
	<style>
		.layui-badge{left: -1px!important;}
		
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataForm">
			<input name="businessId" type="hidden" value="${param.businessId}">
			<div class="ibox">
					<div class="ibox-content">
						    <table class="layui-hide" id="InvoiceMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
			<a class="layui-btn layui-btn-primary layui-border-blue layui-btn-xs" lay-event="InvoiceMgr.edit">查看</a>
		</script>
		<script type="text/x-jsrender" id="btnBar">
			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="InvoiceMgr.downloadInvoice">批量下载</a>
			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="InvoiceMgr.printInvoice">合并预览/打印</a>
			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="InvoiceMgr.printBx">打印底单</a>
			<a class="layui-btn layui-btn-normal layui-btn-xs layui-hide" href="https://lvfapiao.com/" target="_blank">电子发票PDF合并打印</a>
			<a class="layui-btn layui-btn-normal layui-btn-xs layui-hide" href="https://fapiaohezi.com/webapp/pdf_tool" target="_blank">发票打印合并器</a>
			<a class="layui-btn layui-btn-normal layui-btn-xs layui-hide" href="https://tools.pdf24.org/zh/merge-pdf" target="_blank">发票在线合并打印</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var InvoiceMgr={
			init:function(){
				$("#dataForm").initTable({
					mars:'BxInvoiceDao.bxApplyInvoiceList',
					id:'InvoiceMgrTable',
					page:false,
					toolbar:'#btnBar',
					totalRow:true,
					cols: [[
		             {
	            	 	type: 'checkbox',
						title: '序号',
						align:'left'
					 },{
						title: '操作',
						align:'center',
						width:80,
						templet:function(row){
						    return renderTpl('bar',row);
						}
					},{
					    field: 'INVOICE_NO',
						title: '发票号',
						align:'left',
						width:160,
						totalRowText:'统计'
					},{
					    field: 'TOTAL_AMOUNT',
						title: '含税金额',
						align:'left',
						width:90,
						totalRow:true
					},{
					    field: 'BX_AMOUNT',
						title: '报销金额',
						align:'left',
						width:90,
						totalRow:true
					},{
						field:'AMOUNT_IN_FIGUERS',
						title:'不含税',
						width:90,
						totalRow:true
					},{
						field:'TOTAL_TAX',
						title:'税额',
						width:90,
						totalRow:true
					},{
						field:'TAX_RATE',
						title:'税率',
						width:90,
						templet:function(row){
							if(row.TAX_RATE<1){
								return row.TAX_RATE*100+"%";
							}
							return row.TAX_RATE;
						}
					},{
					    field: 'INVOICE_DATE',
						title: '开票日期',
						align:'center',
						width:110
					},{
					    field: 'INVOICE_TYPE',
						title: '开票类型',
						width:110
					},{
					    field: 'SALE_COMPANY',
						title: '开票方',
						width:120
					},{
					    field: 'BUY_COMPANY',
						title: '购买方',
						width:120
					},{
					    field: 'KP_REMARK',
						title: '开票备注',
						minWidth:120
					},{
					    field: 'FILE_NAME',
						title: '发票文件',
						width:160
					},{
					    field: 'CREATE_TIME',
						title: '导入时间',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'CREATE_NAME',
						title: '导入人',
						align:'center',
						width:70
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataForm").queryData({id:'InvoiceMgrTable',jumpOne:true,page:false});
			},
			edit:function(data){
				var  invoiceId = data['INVOICE_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/flow/bx/invoice/invoice-edit.jsp',title:'查看发票',data:{op:'detail',invoiceId:invoiceId}});
			},
			printInvoice:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择要打印的发票。',{icon : 7, time : 1000});
					return;
				}
				var arrayId = [];
				for(var index in dataList){
					arrayId.push(dataList[index]['INVOICE_ID']);
				}
				window.open('/yq-work/invoice/merge?invoiceIds='+arrayId.join(','));
			},
			printBx:function(dataList){
				var businessId = $("#dataForm [name='businessId']").val();
				window.open('/yq-work/servlet/bx/invoice?action=printBx&businessId='+businessId);
			},
			downloadInvoice:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择要下载的发票。',{icon : 7, time : 1000});
					return;
				}
				var arrayId = [];
				for(var index in dataList){
					arrayId.push(dataList[index]['INVOICE_ID']);
				}
				layer.msg('正在导出',{time:500});
				var applyNo = '${param.applyNo}';
				window.open('${ctxPath}/servlet/bx/fun?action=exportPdfInvoice&invoiceIds='+arrayId.join(',')+"&applyNo="+applyNo);
			}
		}
		
		function reloadInvoiceList(){
			InvoiceMgr.query();
		}
		
		$(function(){
			$("#dataForm").render({success:function(){
				InvoiceMgr.init();
			}});
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>