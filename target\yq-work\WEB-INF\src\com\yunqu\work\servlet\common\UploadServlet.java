package com.yunqu.work.servlet.common;

import java.io.File;
import java.sql.SQLException;
import java.util.Collection;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.FileModel;
import com.yunqu.work.utils.YqFileUtils;
/**
 *文件上传辅助类
 *
 */
@WebServlet(urlPatterns = { "/servlet/upload/*" })
@MultipartConfig(maxFileSize=1024*1024*1024)
public class UploadServlet extends AppBaseServlet{
	private static final long serialVersionUID = 1L;
	
	public JSONObject actionForIndex(){
		HttpServletRequest request=getRequest();
		try {
			Collection<Part> fileList=request.getParts();
			String source=getPara("source");
			String folderId=getPara("folderId");
			if(fileList==null||fileList.size()<=0){return EasyResult.fail("未选择文件.");}
			JSONArray list=new JSONArray();
			JSONObject jsonObject=null;
			for (Part part:fileList) {
				if(part.getSubmittedFileName()==null){continue;}
				String filename = new String(getFilename(part).getBytes(), "UTF-8"); 
				if(StringUtils.isBlank(filename)){
					part.delete();
					continue;
				}
				jsonObject = new JSONObject();
				String fileId=RandomKit.uuid();
				String fileDir=FileKit.getFileDir();
				File file = new File(getBaseDir()+source+fileDir);  
				if (!file.exists()) {  
					file.mkdirs();
				}  
				String newFileName=getNewFileName(fileId,filename);
				String urlPath="/yq-work/files?url=/"+source+fileDir+newFileName;
				File newFile=new File(file.getAbsoluteFile() +File.separator + newFileName);
				
				jsonObject.put("id",fileId);
				jsonObject.put("url",urlPath);
				jsonObject.put("name",filename);
				jsonObject.put("fsType",FileKit.getHouzui(filename));
				jsonObject.put("path",newFile.getAbsolutePath());
				jsonObject.put("relativePath","/"+source+fileDir+newFileName);
				
				FileModel model=new FileModel();
				model.setFileId(fileId);
				model.setIp(WebKit.getIP(request));
				model.addCreateTime();
				model.set("CREATE_NAME",getUserName());
				model.setFolderId(folderId);
				model.setCreator(getRemoteUser());
				model.setFileName(filename);
				model.setAccessUrl(urlPath);
				model.setDiskPath("/"+source+fileDir+newFileName);
				model.setFileType(FileKit.getHouzui(filename));
				model.setFkId(getPara("fkId"));
				
				String fkFlag = getPara("fkFlag");
				if(StringUtils.notBlank(fkFlag)&&"1".equals(fkFlag)) {
					model.setFkId(fileId);
				}
				
				model.setPFkId(getPara("pFkId"));
				model.setSource(getPara("source"));
				String fileLabel = getPara("fileLabel");
				if(StringUtils.notBlank(fileLabel)) {
					model.setFileLabel(fileLabel);
				}
				
				FileKit.saveToFile(part.getInputStream(), newFile.getAbsolutePath());
				
				part.delete();// 删除临时文件
				
				model.setFileLenth(newFile.length());
				model.setFileSize(getPrintSize(newFile.length()));
				model.save();
				
				jsonObject.put("size",getPrintSize(newFile.length()));
				
				list.add(jsonObject);
			}
			if(list.size()>1) {
				return EasyResult.ok(list, "上传成功!");
			}else {
				return EasyResult.ok(jsonObject, "上传成功!");
			}
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
	    }
	}
	
	public void actionForDel() {
		String _id = getJsonPara("id");
		String ids = getJsonPara("ids");
		String[] array = null;
		if(StringUtils.notBlank(ids)) {
			array = ids.split(",");
		}else {
			array = new String[] {_id};
		}
		for(String id:array) {
			try {
				JSONObject jsonObject = getQuery().queryForRow("select * from yq_files where file_id = ?", new Object[]{id}, new JSONMapperImpl());
				if(jsonObject==null) {
					renderJson(EasyResult.fail("数据不存在"));
					return;
				}
				String diskPath=jsonObject.getString("DISK_PATH");
				String basePath = getBaseDir();
				File file =  new File(basePath+diskPath);
				boolean fileNotExist = false;
				if(file==null||!file.exists()){
					fileNotExist = true;
					this.error("文件不存在",null);
				}
				boolean bl = file.delete();
				if(bl||fileNotExist) {
					if(StringUtils.isNotBlank(id)) {
						if(isSuperUser()) {
							this.getQuery().executeUpdate("delete from yq_file_download_log where file_id =  ?",id);
							this.getQuery().executeUpdate("delete from yq_look_log where fk_id =  ?",id);
							this.getQuery().executeUpdate("delete from yq_files where file_id =  ?",id);
						}else {
							this.getQuery().executeUpdate("update yq_files set del_flag = 1 where file_id =  ?",id);
						}
					}
				}
				
			} catch (SQLException e) {
				this.error(null, e);
				renderJson(EasyResult.fail(e.getMessage()));
				return;
			}
		}
		renderJson(EasyResult.ok());
		return;
	}
	
	private String getPrintSize(long size) {
		return YqFileUtils.getPrintSize(size);
	}
	
	private String getNewFileName(String id,String oldFileName) {
		String filename = id+FileKit.getHouzui(oldFileName);
		return filename;
	}
	 private String getFilename(Part part) {  
	        String contentDispositionHeader = part.getHeader("content-disposition");  
	        String[] elements = contentDispositionHeader.split(";");  
	        for (String element : elements) {  
	            if (element.trim().startsWith("filename")) {  
	                return element.substring(element.indexOf('=') + 1).trim().replace("\"", "");  
	            }  
	        }  
	        return null;  
	}
		
}
