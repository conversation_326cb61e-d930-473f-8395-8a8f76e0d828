package com.yunqu.work.servlet;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.WeeklyModel;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.WeeklyNoticeService;
import com.yunqu.work.service.WeeklyStatService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.WeekUtils;
/**
 * 周报管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/weekly/*")
public class WeeklyServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		WeeklyModel model=getModel(WeeklyModel.class, "weekly");
		//model.setPrimaryValues(RandomKit.uniqueStr());
		model.addCreateTime();
		model.set("update_time",EasyDate.getCurrentDateString());
		model.setCreator(getUserId());
		model.set("create_name", getUserName());
		model.set("dept_id", getDeptId());
		JSONObject jsonObject=getJSONObject();
		JSONArray mailtos=jsonObject.getJSONArray("mailtos");
		String[] cc=null;
		if(mailtos!=null){
			cc=new String[mailtos.size()];
			for(int i=0;i<mailtos.size();i++){
				String usertId=mailtos.getString(i);
				cc[i]=usertId;
				EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
				record.setPrimaryValues(model.getWeeklyId(),usertId);
				record.set("CREATOR", getUserPrincipal().getUserId());
				record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				try {
					this.getQuery().save(record);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}
		try {
			String receiver=model.getString("RECEIVER");
			if(StringUtils.isBlank(receiver)){
				model.set("status","0");//未发送
			}else{
				model.set("SEND_TIME",EasyDate.getCurrentDateString());//已发送
				model.set("status","1");//已发送
				
				MessageModel messageModel=new MessageModel();
				messageModel.setReceiver(receiver);
				messageModel.setData(model);
				messageModel.setContents(model.getString("TITLE"));
				messageModel.setTypeName("weekly");
				messageModel.setFkId(model.getWeeklyId());
				messageModel.setSender(getUserId());
				messageModel.setSendName(getUserName());
				messageModel.setCc(cc);
				messageModel.setMsgState(1);
				messageModel.setMsgType(2001);
				messageModel.setSenderType(0);
				
				messageModel.setData(model);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendWeeklyEmail(messageModel);
					messageModel.setDesc(model.getString("ITEM_1"));
					WxMsgService.getService().sendNewWeeklyMsg(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	private List<String> ccList(String weeklyId){
		String sql="select USER_ID from  yq_cc where FK_ID = ?";
		List<String> mailtos=new ArrayList<String>();
		List<EasyRow> list;
		try {
			list = this.getQuery().queryForList(sql,weeklyId);
			if(list!=null&&list.size()>0){
				for(int i=0;i<list.size();i++){
					mailtos.add(list.get(i).getColumnValue(1));
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return mailtos;
	}
	
	public EasyResult actionForGetNearWeekly() {
		JSONObject params = getJSONObject();
		String weeklyId = params.getString("weeklyId");
		String type = params.getString("type");
		try {
			JSONObject row = this.getQuery().queryForRow("select creator,year,week_no from yq_weekly where weekly_id = ?", new Object[] {weeklyId}, new JSONMapperImpl());
			
			EasySQL sql = new EasySQL();
			sql.append(row.getString("CREATOR"),"select weekly_id from yq_weekly where creator = ?");
			
			if("next".equals(type)) {
				sql.append(row.getString("YEAR"),"and `year` >= ?");
				sql.append(row.getString("WEEK_NO"),"and week_no > ?");
				sql.append(" order by `year`,week_no limit 1");
			}else {
				sql.append(row.getString("YEAR"),"and `year` <= ?");
				sql.append(row.getString("WEEK_NO"),"and week_no < ?");
				sql.append(" order by `year` desc,week_no desc limit 1");
			}
			String newId = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			if(StringUtils.isBlank(newId)) {
				newId = "";
			}
			return EasyResult.ok(newId);
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForNoticeWriteWeekly(){
		JSONObject jsonObject = getJSONObject();
		String userId=jsonObject.getString("userId");
		MessageModel messageModel=new MessageModel();
		messageModel.setReceiver(userId);
		messageModel.setSender(getUserId());
		messageModel.setTitle("请及时填报"+jsonObject.getString("year")+"年第"+jsonObject.getString("weekNo")+"的周报");
		EmailService.getService().sendNoticeWriteWeeklyEmail(messageModel);
		return EasyResult.ok();
	}
	public EasyResult actionForDoGrade(){
		JSONObject jsonObject = getJSONObject();
		String weeklyId=jsonObject.getString("weeklyId");
		String grade=jsonObject.getString("grade");
		JSONObject log=new JSONObject();
		try {
			log.put("userId", getUserId());
			log.put("grade", grade);
			log.put("name", jsonObject.getString("name"));
			log.put("time",EasyDate.getCurrentDateString());
			this.getQuery().executeUpdate("update yq_weekly set grade = ?,grade_log = ? where weekly_id = ?", grade,log.toJSONString(),weeklyId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(log);
	}
	public EasyResult actionForUpdate(){
		WeeklyModel model=getModel(WeeklyModel.class, "weekly");
		JSONObject jsonObject=getJSONObject();
		JSONArray mailtos=jsonObject.getJSONArray("mailtos");
		List<String> cc=new ArrayList<String>();
		List<String> ccList=ccList(model.getWeeklyId());
		if(mailtos!=null){
			for(int i=0;i<mailtos.size();i++){
				String usertId=mailtos.getString(i);
				cc.add(usertId);
				EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
				record.setPrimaryValues(model.getWeeklyId(),usertId);
				record.set("CREATOR", getUserPrincipal().getUserId());
				record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				try {
					if(ccList.contains(usertId))continue;
					this.getQuery().save(record);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
			for(String userId:ccList){
				if(!cc.contains(userId)){
					try {
						this.getQuery().executeUpdate("delete from YQ_CC where fk_id = ? and user_id = ?",model.getWeeklyId(),userId);
					} catch (SQLException e) {
						e.printStackTrace();
					}
				}
			}
		}
		
		model.set("update_time",EasyDate.getCurrentDateString());
		try {
			String receiver=model.getString("RECEIVER");
			int status=model.getIntValue("STATUS");
			if(status<2){
				if(StringUtils.isBlank(receiver)){
					model.set("status","0");//未发送
				}else{
					model.set("SEND_TIME",EasyDate.getCurrentDateString());//已发送
					model.set("status","1");//已发送
					
					MessageModel messageModel=new MessageModel();
					messageModel.setReceiver(receiver);
					messageModel.setContents(model.getString("TITLE"));
					messageModel.setFkId(model.getWeeklyId());
					messageModel.setSender(getUserId());
					messageModel.setSendName(getUserName());
					messageModel.setMsgState(1);
					messageModel.setData(model);
					messageModel.setMsgType(2001);
					messageModel.setTypeName("weekly");
					messageModel.setSenderType(0);
					
					messageModel.setData(model);
					
					try {
						MessageService.getService().sendMsg(messageModel);
						EmailService.getService().sendWeeklyEmail(messageModel);
						messageModel.setDesc(model.getString("ITEM_1"));
						WxMsgService.getService().sendNewWeeklyMsg(messageModel);
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		WeeklyModel model=getModel(WeeklyModel.class, "weekly");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	public EasyResult actionForNoticeWeekly(){
		String weeklyNo=getPara("weeklyNo", ""+WeekUtils.getWeekNo());
		WeeklyNoticeService.getService().run(Integer.valueOf(weeklyNo));
		return EasyResult.ok("","提醒成功!");
	}
	public EasyResult actionForUpdateWeeklyStat(){
		WeeklyStatService.getService().run();
		return EasyResult.ok();
	}
}





