package com.yunqu.work.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;


public class FlowFormRowMapper implements EasyRowMapper<FlowFormModel> {

	@SuppressWarnings("unchecked")
	@Override
	public FlowFormModel mapRow(ResultSet rs, int rowNum) {
		FlowFormModel vo = new FlowFormModel();
		try {
			vo.setFormId(rs.getString("FORM_ID"));
			vo.setTableName(rs.getString("TABLE_NAME"));
			vo.setFormName(rs.getString("FORM_NAME"));
			vo.setFormCode(rs.getString("FORM_CODE"));
			vo.setDetailCode(rs.getString("DETAIL_CODE"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
	}

}
