<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>任务详情</title>
	
	<style type="text/css">
		<c:if test="${param.isDiv==0}">
			@media (min-width: 850px) {
				.task-container-fluid{margin: 15px;padding:26px 50px;}
			}
			@media (min-width: 1400px) {
				.task-container-fluid{margin: 20px 200px;padding:30px 50px;}
			}
			@media (min-width: 1900px) {
				.task-container-fluid{margin: 20px 300px;;padding:30px 50px;}
			}
			@media (max-width: 768px) {
				.pull-right .checkbox,.a-qq,.color999,.a-link{display: none;}
			}
			.task-container-fluid{background-color: #fff!important;border-radius:8px;}
			body{background-image: url("${ctxPath}/static/images/task-bg.gif");}
			
			.dealTaskDiv{
				left:0px!important;
				text-align: center;
				background-color: #fff!important;
				margin-left: 0px!important;
			}
			.dealTaskDiv .dealTask{display: none;}
		</c:if>
		.radio-inline{margin-top: 0px;}
		#taskEditForm{
			margin-bottom: 100px;
			height: 100%;
		}
		.radio-inline{margin-left: 2px;}
		#taskEditForm .userName{font-size:1em;color:rgba(0,0,0,.87);font-weight:500;}
		#taskEditForm .createTime{display:inline-block;margin-left:.5em;color:rgba(0,0,0,.4);font-size:.875em;}
		#taskEditForm td{text-align: left;}
		#taskEditForm .t1 td{line-height: 16px;}
		.avatar{float:left;height:3em;width:3em;display:block;margin:.2em00;}
    	.avatar img{display:inline-block;height:100%;width:100%;border-radius:100%;overflow:hidden;font-size:inherit;vertical-align:middle;-webkit-box-shadow:001pxrgba(0,0,0,.3);box-shadow:001pxrgba(0,0,0,.3);cursor:pointer;}
		.b_content{
			margin: 10px 15px;
		}
		.d_content{margin-left:4em;padding:6px0px;}
		.fullShow{position: relative;left: 20px;top:30px;font-size: 16px;color:#17a6f0;margin-right:10px;cursor: pointer;}
		.fullShow .layui-icon{font-size: 18px;}
		.color999{color: #999;margin-right: 7px;}
		#groupName{display: inline-block;}
		.w-70{width: 70px!important;min-width:70px!important;}
		#taskDesc img{max-width: 700px;}
		#taskDesc{padding:20px 10px;background-color:#fff8f8;line-height: 22px; font-size: 13px;color: #288237;}
		.ys,.returnTask,.toOther,.updateTask,.uploadFileBtn,#pTaskTr,.applyDelay,.stopTask{display: none;}
		.dealTask{display: none;}
		.dealTaskDiv{position: fixed;bottom: 8px;z-index: 9999;width: 100%;background-color: #f6f0f0;padding: 10px 30px;margin-left: -10px;margin-bottom: -8px;}
		.icon {width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:4px;}
		#slide{display: inline-block;width: 200px;margin-left: 20px;}
		#slider-tips{margin-left: 20px;}
		.file-div a{color:#17a6f0;}
		blockquote{font-size: inherit!important;}
		.ai-result p{padding: 10px;}
		.ai-result h4,.ai-result h3{font-size: inherit;padding: 10px 0px;}
		pre{margin-bottom: 10px;}
		.ai-result{display: none;padding: 10px 0px;}
		.stop-ai-btn{display: none;}
		#taskDesc blockquote{font-size:13px;padding:10px;color: #dd3705;}
		pre code{display: block;padding: 10px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		  <div class="task-container-fluid">
	     	<form id="taskEditForm" data-mars="TaskDao.record" autocomplete="off" data-mars-prefix="task.">
	     		   <input type="hidden" id="taskId" value="${param.taskId}" name="task.TASK_ID"/>
	     		   <input type="hidden" value="${param.taskId}" name="fkId"/>
							<table class="table table-edit table-vzebra t1">
						            <tr>
					                    <td colspan="2">
					                    	 <span id="projectName"> </span>
					                    	 <span name="task.MODULE_NAME" style="font-size: 14px;color: #090909;"></span>
					                    	 <a class="projectUrl ml-10 a-link" href="${ctxPath}/project" target="_blank"><i class="layui-icon layui-icon-link"></i> 详情</a>
					                    </td>
						            </tr>
						            <tr>
					                    <td colspan="2">
					                    	 <div id="groupName"></div>
					                    	 <span style="font-size: 16px;font-weight: bold;" name="task.TASK_NAME"></span>
					                   		 <a class="ml-10 a-link" onclick="layer.closeAll();" href="${ctxPath}/task/${param.taskId}" target="_blank"><i class="layui-icon layui-icon-link"></i>分享任务</a>
					                    </td>
						            </tr>
						            <tr id="pTaskTr">
						            	<td colspan="2">
						            		<i class="fa fa-bookmark-o"></i>
						            		<span class="f-14 color999 mr-5">父任务</span>
						            		<span class="f-14" id="pTaskName"></span>
						            		<a class="ml-20" id="pTaskUrl" href="${ctxPath}/task/" target="_blank">点击查看</a>
						            	</td>
						            </tr>
						            <tr >
						            	<td style="width: 50%">
						            		 <i class="glyphicon glyphicon-time"></i> 
						            		 <span data-fn="getUserName" name="task.CREATOR"></span>
						            		 <span class="color999">创建于</span>
						            		 <span class="color999" name="task.CREATE_TIME"></span>
						            		 
						            		<i class="glyphicon glyphicon-flash ml-10"></i>
				                    		<span class="color999">优先级</span>
				                    		<span data-fn="taskTextLevel" name="task.TASK_LEVEL"></span>
						            	</td>
						            	<td>
						            		<i class="fa fa-edit"></i> 
						            		<span data-fn="getUserName" name="task.UPDATE_BY"></span>
				                    		<span class="color999">更新于</span>
				                    		<span name="task.UPDATE_TIME"></span>
					                    </td>
						            </tr>
						            <tr>
					                    <td>
					                    	<i class="fa fa-hourglass-end"></i> 
					                    	<span class="color999">任务起止时间</span>
					                    	<span name="task.PLAN_STARTED_AT"></span> ~
					                    	<span data-fn="deadlineFn" name="task.DEADLINE_AT"></span>
					                    </td>
					                    <td>
					                    	<i class="glyphicon glyphicon-hand-right"></i>
					                    	<span class="color999">指派给</span>
				                    		<span data-fn="getUserName" name="task.ASSIGN_USER_ID"></span>
				                    		<span class="ml-10" id="taskStateSpan"></span>
					                    </td>
					                </tr>
					                <tr>
					                	<td>
					                    	<i class="glyphicon glyphicon-calendar"></i>
					                		<span class="color999 ml-10">预估工时/小时</span>
					                		<span name="task.PLAN_WORK_HOUR"></span>
					                		<span id="deadlineDesc"></span>
					                    	<button type="button" onclick="applyDelay();" class="btn btn-warning btn-xs btn-link applyDelay">申请延期</button>
					                	</td>
					                	<td>
					                    	<i class="glyphicon glyphicon-fire"></i>
					                		<span class="color999 ml-10">难度</span>
					                		<span name="task.TASK_DIFFICULTY"></span>
					                	</td>
					                </tr>
					                <tr>
					                	<td>
					                    	<i class="glyphicon glyphicon-flag"></i>
					                		<span class="color999 ml-10">抄送人</span>
					                    	<span id="mailtos"></span>
					                    	<span class="layui-btn layui-btn-primary layui-btn-xs ml-10" onclick="Task.addCC()"><i class="layui-icon layui-icon-addition"></i></span>
					                	</td>
					                	<td>
					                    	<i class="glyphicon glyphicon-tag"></i>
					                		<span class="color999 ml-10">标签</span>
					                		<span name="task.TASK_TAGS"></span>
					                	</td>
					                </tr>
					                <tr>
					                	<td colspan="2">
					                    	<i class="fa fa-random"></i>
					                		<span class="color999 ml-10">完成进度</span>
											<div id="slide" class="demo-slider"></div>
											<span id="slider-tips"></span>
											<span class="pull-right" data-fn="taskWh" name="task.WORK_HOUR"></span>
					                	</td>
					                </tr>
		  					  </table>
			               	 
			               	  <p>
				               	  <button class="btn btn-sm btn-info mr-10 start-ai-btn" onclick="aiModelGen(this);" type="button"><i class="fa fa-send"></i> DeepSeek解答</button>
				               	  <button class="btn btn-sm btn-warning stop-ai-btn" onclick="stopAi(this);" type="button">AI停止</button>
			               	  </p>
			               	  
		  					  <!-- 内容 -->
		  					  <span onclick="fullScreeShow()" class="fullShow"><i class="layui-icon layui-icon-screen-full"></i></span>
			               	  <div id="taskDesc" ondblclick="fullScreeShow();" style="max-height: 800px;overflow:auto;" name="task.TASK_DESC"></div>
			               	  <div class="ai-result">
			               	  	<blockquote class="layui-elem-quote layui-quote-nm"></blockquote>
			               	  </div>
			               	   
		  					  <!-- 文件 -->
			               	  <div style="display: inline-block;" data-template="task-template-files" data-mars="FileDao.fileList"></div>
			               	  <div id="fileList" style="display: inline-block;"></div>
			               	
		  					  <!-- 禅道 -->
			               	  <div id="bugsList" data-template="bug-template" data-mars="TaskDao.zentaoBugs"></div>
			               		
			               	  <!-- 评论 -->
		  					  <fieldset class="content-title" style="display:block;">
	  								<legend>意见</legend>
							  </fieldset>
		  					  <table class="table table-edit table-vzebra">
						            <tr>
						               	<td colspan="4">
				                           <textarea placeholder="在此输入评论内容" style="height: 80px;width:100%" class="form-control input-sm" name="comment" id="comment"></textarea>
						               	</td>
						            </tr>
						            <tr>
						               	<td>
				                          	<div class="btn-group pull-left">
												<a type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown">
													动态 <span class="caret"></span>
												</a>
												<ul class="dropdown-menu" role="menu">
													<li><a href="javascript:void(0)" onclick="allLog();">全部动态</a></li>
													<li><a href="javascript:void(0)" onclick="loadCommentHistory();">评论动态</a></li>
													<li><a href="javascript:void(0)" onclick="lookLog();">浏览动态</a></li>
													<li><a href="javascript:void(0)" onclick="opLog();">操作动态</a></li>
												</ul>
											</div>
						               	</td>
						               	<td colspan="3">
						               		<div class="pull-right">
								               	<label class="checkbox checkbox-default radio-inline" style="margin-top: 0px;">
						                            <input type="checkbox" checked="checked" name="commentNoticeType" value="wx"/> <span>微信通知</span> 
								               	</label>
								               	<label class="checkbox checkbox-default radio-inline mr-15">
						                            <input type="checkbox" name="commentNoticeType" checked="checked" value="email"/> <span>邮件通知</span>  
								               	</label>
							               		<button class="btn btn-link btn-sm mr-15" onclick="Task.linkZentaoBug();" type="button">关联禅道BUG</button>
					                            <button class="btn btn-sm btn-info btn-outline uploadFileBtn mr-15" type="button" onclick="$('#localfile').click()">+附件</button>
					                            <button type="button" style="box-shadow: 0 4px 8px 0 rgba(31,93,234,.35);" class="btn btn-primary btn-sm  " onclick="addComment()"> <i class="glyphicon glyphicon-send"></i> 发布评论 </button>
						               		</div>
						               	</td>
						            </tr>
		  					  </table>
		  					 <div id="opLog" style="border-bottom:1px solid #eee;">
		  					  	<table id="opLogTable"></table>
		  					 </div>
		  					 <div id="history"></div>
		  					 <div id="lookLog">
		  					  	<table id="viewTable"></table>
		  					 </div>
		  					 <div class="dealTaskDiv">
	  					 		<div class="dealTask">
				               	     <label class="radio radio-info radio-inline state">
		                    			<input type="radio" value="10" name="task.TASK_STATE"/> <span>待办中</span>
			                    	</label>
				            		<label class="radio radio-info radio-inline state">
		                    			<input type="radio" value="12"  name="task.TASK_STATE"/> <span>暂停</span>
			                    	</label>
				            		<label class="radio radio-info radio-inline state">
		                    			<input type="radio" value="13"  name="task.TASK_STATE"/> <span>取消</span>
			                    	</label>
				            		<label class="radio radio-info radio-inline state doing-state">
		                    			<input type="radio" value="20"  name="task.TASK_STATE"/> <span>进行中</span>
			                    	</label>
			                    	<label class="radio radio-info radio-inline state finsh-state">
				                    	<input type="radio" value="30"  name="task.TASK_STATE"/> <span>已完成</span>
			                    	</label>
			                    	<label class="radio radio-info radio-inline ys">
				                    	<input type="radio" value="40" name="task.TASK_STATE"/> <span>验收通过</span>
			                    	</label>
			                    	<label class="radio radio-warning radio-inline ys">
				                    	<input type="radio" value="42" name="task.TASK_STATE"/> <span>验收不通过</span>
			                    	</label>
			                    	<button class="btn btn-sm btn-info btn-outline ml-15" type="button" onclick="Task.updateState()">确认修改</button>
	  					 		</div>
	  					 		<div class="btn-group ml-20">
			                    	 <button class="btn btn-sm btn-info ml-30 toOther" type="button" onclick="Task.toOther()">转派</button>
			                    	 <button class="btn btn-sm btn-warning ml-10 returnTask" type="button" onclick="Task.returnTask()">退回</button>
			                    	 <button class="btn btn-sm btn-danger  ml-10 updateTask" type="button" onclick="updateTaskLayer()">修改</button>
	  					 		</div>
			              </div>
	  		</form>
	  	 </div>
	  	 
	  	 
	  	<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Task.uploadFile()"/>
  		</form>
  		 <script id="task-template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div">
					{{call:FILE_TYPE fn='getFileIcon'}}<input name="fileIds" value='{{:FILE_ID}}' type="hidden"/>
					<a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span></a>
					<a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#17a6f0">下载<span></a>
					{{if FILE_TYPE=='.xml'}}
						<a href="/yq-work/files/view/{{:FILE_ID}}?action=display" target="_blank">编辑器打开</a>&nbsp;
						{{else FILE_TYPE=='.zip'||FILE_TYPE=='.war'}}
							<a href="/yq-work/files/view/{{:FILE_ID}}?action=zip.view" target="_blank">预览</a>&nbsp;
						{{else FILE_TYPE=='.pdf'||FILE_TYPE=='.txt'}}
							<a href="/yq-work/files/view/{{:FILE_ID}}?action=get" target="_blank">本地打开</a>&nbsp;
						{{else FILE_TYPE=='.xlsx'||FILE_TYPE=='.xls'||FILE_TYPE=='.docx'||FILE_TYPE=='.doc'||FILE_TYPE=='.ppt'||FILE_TYPE=='.pptx'}}
							<a href="/yq-work/files/view/{{:FILE_ID}}?action=office" target="_blank">预览</a>&nbsp;
						{{else FILE_TYPE=='.log'||FILE_TYPE=='.txt'||FILE_TYPE=='.out'}}
							<a href="/yq-work/files/view/{{:FILE_ID}}?action=tail" target="_blank">tail</a>&nbsp;
							<a href="/yq-work/files/view/{{:FILE_ID}}?action=less" target="_blank">less</a>&nbsp;
							<a href="/yq-work/files/view/{{:FILE_ID}}?action=grep" target="_blank">grep</a>&nbsp;
					{{/if}}
					<a class="hidden" href="/yq-work/files/view/{{:FILE_ID}}?action=download" target="_blank">download</a>
				</div>
			{{/for}}
			{{if data.length==0}}{{/if}}
		</script>
  		 <script id="childTaskTpl" type="text/x-jsrender">
			{{for data}}
				<p class="f-14" style="line-height:30px;">子任务：{{call:TASK_STATE fn='taskStateLabel'}}<span class="f-13 ml-5 mr-5">{{call:ASSIGN_USER_ID fn='getUserName'}}&nbsp;{{:CREATE_TIME}}</span><a href="${ctxPath}/task/{{:TASK_ID}}" target="_blank">{{call:TASK_NAME 80 fn='cutText'}}</a></p>
			{{/for}}
		</script>
  		 <script id="bug-template" type="text/x-jsrender">
			{{if data.length>0}}
			 <blockquote class="layui-elem-quote">
				{{for data}}
					<p style="font-size: 13px;"> <label class="label label-info">{{:STATUS}}</label> 禅道BUG({{:ID}})&nbsp;<a  style="text-decoration: underline;" target="_blank" href="http://*************/zentao/bug-view-{{:ID}}.html">{{:TITLE}}</a></p>
				{{/for}}
   			 </blockquote>
			{{/if}}
		</script>
  		 <script id="template-comments" type="text/x-jsrender">
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onclick="userInfoLayer('{{:OP_BY}}')" onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{call:CREATOR fn='getUserName'}}</span> <span class="createTime">{{:CREATE_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{call:CONTENT fn='getContent'}}</div>{{if #parent.parent.data.currentUserId == CREATOR}}<a style class="btn btn-xs btn-link hover ib" href="javascript:void(0)" onclick="delComment('{{:COMMENT_ID}}',$(this))">删除</a>	{{/if}}
						</div>						
					</div>
				</div>
			{{/for}}
		</script>
  		 <script id="template-ops" type="text/x-jsrender">
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onclick="userInfoLayer('{{:OP_BY}}')" onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:OP_BY fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{call:OP_BY fn='getUserName'}}</span> <span class="createTime">{{:OP_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{call:CONTENT fn='getContent'}}</div>
						</div>						
					</div>
				</div>
			{{/for}}
		</script>
	   <script id="selectBugs" type="text/x-jsrender">
			<div style="padding: 15px;">
				<input id="bugIds" type="hidden"/>
				<input id="bugNames" placeholder="请选择"  onclick="multiZentaoBug(this);" class="form-control input-sm"  type="text"/>
			</div>
		</script>
		
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
   
		var Task = {
			taskId:	'${param.taskId}'
		};
		
		var taskObj;
		
		var taskId ='${param.taskId}';
			
		function scrollTo(obj,pobj){
			var b = $(obj);
			var p = b.parents(pobj);
			p.scrollTop(p.scrollTop()+b.position().top);
		}
		
		function getContent(val){
			if(val){
				return val.replace(/\n|\r\n/g,'<br/>');
			}else{
				return '--';
			}
		}
		
		function deadlineFn(val,row){
			var state = row["TASK_STATE"];
			$("#taskStateSpan").html(taskStateLabel(state));
			if(state==10||state==20){
				$("#deadlineDesc").html(timeBetween(val));
			}
			return val;
		}
		
		function applyDelay(){
			popup.layerShow({type:1,area:['500px','380px'],full:fullShow(),url:'${ctxPath}/pages/task/task-delay.jsp',title:'申请任务延期',data:{taskId:taskId,deadlineAt:taskObj['DEADLINE_AT']}});
		}
		
		function doShow(taskObj){
			var staffInfo = getStaffInfo();
			var currentId = getCurrentUserId();
			var deptId = getDeptId();
			var isLead = isDevLead();
			
			$("title").text(taskObj['TASK_NAME']);
			var assignUserId = taskObj['ASSIGN_USER_ID'];
			var assignDeptId = taskObj['ASSIGN_DEPT_ID'];
			var creator = taskObj['CREATOR'];
			var taskState = taskObj['TASK_STATE'];
			
		
			if(taskState==10||taskState==20){
				$(".toOther").css('display','inline-block');
			}
			
			if(taskState<30&&taskState!=13){
				$(".applyDelay").css('display','inline-block');
			}
			
			if(assignUserId==currentId){
				$(".returnTask,.dealTask,.uploadFileBtn").css('display','inline-block');
			}
			
			if(creator==currentId){
				$(".updateTask,.dealTask,.uploadFileBtn").css('display','inline-block');
				if(taskState==12){
					$('.finsh-state').hide();
				}
			}
			
			if(assignUserId!=currentId&&(taskState==20||taskState==30)){
				$('.doing-state,.finsh-state').hide();
			}
			
			
			if(taskState==30||taskState==42){
				if(creator==currentId){
					$(".ys").css('display','inline-block');;
				}else{
					$('.dealTaskDiv').remove();
				}
				$(".dealTask").css('display','inline-block');
				$("#taskEditForm .state").hide();
				scrollTo('#comment','.container-fluid');
			}
		   
			if(assignUserId==currentId){//负责的人
				if(taskState==30){
					$("input:radio[name='task.TASK_STATE'][value='40']").prop("checked",true);
				}else if(taskState==10){
					$("input:radio[name='task.TASK_STATE'][value='20']").prop("checked",true);
				}
			}
			
			if(taskState==40||taskState==13){
				$('.dealTaskDiv').remove();
				scrollTo('#comment','.container-fluid');
			}
			
			if(assignUserId!=currentId){
				$('#slider-tips').html("已完成进度"+taskObj['PROGRESS'] + '%');
			}else{
				$('#slider-tips').html("已完成进度"+taskObj['PROGRESS'] + '% <span class="text-danger">请点击进度条修改进度</span>');
			}
			
			layui.use('slider',function(){
	    		var slider = layui.slider;
				 slider.render({
					  elem: '#slide',min: 0,max: 100,theme: '#1E9FFF',value:taskObj['PROGRESS'],step:5,disabled:assignUserId!=currentId,
					  setTips: function(value){
						  return value;
					  },
					  change: function(value){
						  $('#slider-tips').html("已完成进度"+value + '%');
						  ajax.remoteCall("${ctxPath}/servlet/task?action=updateProgress",{taskId:taskId,progress:value},function(result) { 
								if(result.state != 1){
									layer.alert(result.msg,{icon: 5});
								}
							},{loading:false});
					  }
				 });
			});
			
		}
		
		function initFormData(){
			$("#taskEditForm").render({success:function(result){
					var record = result['TaskDao.record'];
					taskObj = record['data'];
					if(taskObj['TASK_ID']==undefined){
						layer.msg("任务已被删除,不存在了.",{time:800,icon:7},function(){
							$(".task-container-fluid").remove();
							layer.closeAll();
						});
						return;
					}
					loadCommentHistory();
					opLog(true);
					doShow(taskObj);
					
					if(typeof record.data != 'string'){
						var mailtos=record.mailtos;
						$("#mailtos").html("");
						for(var m in mailtos){
							$("#mailtos").append(getUserName(mailtos[m])+" ");
						}
						if(mailtos==null||mailtos.length==0){
							$("#mailtos").append("无 ");
						}
						
						if(record.data['GROUP_NAME']){
							$("#groupName").html("<span class='label label-info mr-5'>计划："+record.data['GROUP_NAME']+"</span>");
						}
						
						if(record.data['PROJECT_ID']){
							$("#projectName").text(cutText(record.projectName,80));
							$(".projectUrl").attr("href",'/yq-work/project/'+taskObj['PROJECT_ID']);
						}
						
						if(record.data['BUSINESS_ID']){
							$("#projectName").text(cutText(record.sjName,80));
							$(".projectUrl").attr("href",'/yq-work/sj/'+taskObj['BUSINESS_ID']);
						}
						
						if(record['taskName']){
							$("#pTaskTr").show();
							$("#pTaskName").text(record['taskName']);
							$("#pTaskUrl").attr("href",$("#pTaskUrl").attr("href")+taskObj['P_TASK_ID']);
						}
						if(record['childTaskList']&&record['childTaskList'].length>0){
							var tpl = $.templates("#childTaskTpl");
							var html = tpl.render({data:record['childTaskList']});
							$("#pTaskTr td").html(html);
							$("#pTaskTr").show();
						}
					}
					var taskDescHeiht = $("#taskDesc").height();
					if(taskDescHeiht<200){
						$(".fullShow").hide();
					}
					var taskDesc = taskObj['TASK_DESC'];
					if(taskDesc.length<=7){
						$("#taskDesc").html(taskObj['TASK_NAME']);
					}
					layer.photos({photos: '#taskDesc',anim: 0,shade:0,shadeClose:true,closeBtn:true});
					
			}});  
		}
		
		function updateTaskLayer(){
			layer.closeAll();
			var businessId = taskObj['BUSINESS_ID'];
			var params = {taskId:taskId,businessId:businessId,taskState:taskObj['TASK_STATE'],projectName:taskObj['PROJ_NAME']};
			popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'编辑任务',data:params});
		}
		
		Task.ajaxSubmitForm = function(){
			if(form.validate("#taskEditForm")){
				if(Task.taskId){
					Task.updateState();
				}
			};
		}
		
		Task.uploadFile = function(callback){
			var fkId=Task.taskId;
			easyUploadFile({callback:'callback',fileMaxSize:(1024*100),fkId:fkId,source:'task'});
		}
		
		var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
			var comment = getUserName(getCurrentUserId())+"上传附件["+data.name+"]";
			addComment(comment,1);
		}
		
		function fullScreeShow(){
			layer.open({type:1,offset:'20px',content:"<div style='padding:15px 30px;overflow:auto;height:calc(100vh - 100px);'>"+$("#taskDesc").html()+"</div>",area:['80%','80%'],maxmin:true,shadeClose:true});
		}
		
		function allLog(){
			loadCommentHistory();
			opLog(true);
		}
		
		function loadCommentHistory(){
			$("#history").show();
			$("#lookLog").hide();
			$("#opLog").hide();
			ajax.remoteCall("${ctxPath}/webcall?action=CommonDao.commentsList",{fkId:taskId},function(result) { 
				if(result.data==null||result.data.length<=0){
					$("#taskEditForm").find('#history').hide();
					$("#opLog").css("border-bottom","none");
				}else{
					result['currentUserId']=getCurrentUserId();
					var html=renderTpl("template-comments",result);
					$("#history").html(html);
				}
			});
		}
		
		function lookLog(){
			$("#lookLog").show();
			$("#history").hide();
			$("#opLog").hide();
			loadLookLog({tableId:'viewTable',formId:'taskEditForm',fkId:Task.taskId})
		}
		
		function opLog(isAll){
			$("#opLog").show();
			$("#lookLog").hide();
			if(!isAll)$("#history").hide();
			
			ajax.remoteCall("${ctxPath}/webcall?action=CommonDao.lookOpList",{fkId:taskId},function(result) { 
				if(result.data==null||result.data.length<=0){
			
				}else{
					var tpl = $.templates("#template-ops");
					var html = tpl.render(result);
					$("#opLog").html(html);
				}
			});
			//loadOpLog({tableId:'opLogTable',formId:'taskEditForm',fkId:Task.taskId})
		}
		
		Task.toOther = function() {
			popup.layerShow({type:1,full:fullShow(),area:['500px','350px'],url:'${ctxPath}/pages/task/task-forward.jsp',title:'任务转派',data:{taskId:taskId}});
		}
		
		Task.returnTask = function(){
			popup.layerShow({type:1,full:fullShow(),area:['400px','300px'],url:'${ctxPath}/pages/task/task-return.jsp',title:'退回任务',data:{taskId:taskId}});
		}
		
		Task.addCC = function(){
			popup.layerShow({type:1,full:fullShow(),area:['450px','300px'],url:'${ctxPath}/pages/task/task-forward-cc.jsp',title:'新增抄送人',data:{taskId:taskId}});
		}
		
		Task.updateState = function() {
			var data = form.getJSONObject("#taskEditForm");
			var taskState = data['task.TASK_STATE'];
			if(taskState==42){
				if($("#comment").val()==''){
					layer.msg("请在评论区填写不通过的理由!");
					return;
				}								
			}
			if(taskState==30){
				layer.prompt({id:'workHourLayer',title:'请输入任务消耗工时/小时',offset:'20px',success:function(){
					$('.layui-layer-input').attr('type','number').attr('placeholder','人/小时');;
				}},function(value, index, elem){
					if(value>40){
						layer.confirm('确认工时要'+value+"/小时?请确认无误",{offset:'20px',shade:0.1,icon:3},function(){
							layer.close(index);
							data['workHour'] = value;
							doSubmit();
						});
					}else{
						layer.close(index);
						data['workHour'] = value;
						doSubmit();
					}
				});
			}else{
				doSubmit();
			}
			function doSubmit(){
				ajax.remoteCall("${ctxPath}/servlet/task?action=updateState",$.extend({},taskObj,data),function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							initFormData();
							addComment();
							reloadTaskList&&reloadTaskList();
							//layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		
		Task.linkZentaoBug = function(){
			var tpl = $.templates("#selectBugs");
			var html = tpl.render({});
			popup.layerShow({title:'引用关联禅道BUG',content:html,area:['400px','200px'],type:1,btn:['提交','取消'],yes:function(index){
				var bugIds = $("#bugIds").val();
				if(bugIds==''){
					layer.msg('请选择.');
					return;
				}
				var array = bugIds.split(',');
				if(array.length>20){
					layer.msg('不能超过20个bug.');
					return;
				}
				ajax.remoteCall("${ctxPath}/servlet/task?action=setBugIds",{bugIds:bugIds,taskId:taskId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{time:800},function(){
							addComment('关联禅道BUG：'+$('#bugNames').val(),1);
							layer.close(index);
						});
					}else{
						layer.alert(result.msg,{icon: 7});
					}
				});
			}});
		}
		
		function taskWh(val){
			if(val){
				return '实际工时：'+val+'/小时';
			}
			return '';
		}
		
		var addComment = function(comment,isInputContent) {
			comment = comment||$("#comment").val();
			isInputContent= isInputContent||0;
			if(comment){
			    var commentNoticeType ='';
			 	$.each($("input[name='commentNoticeType']:checked"),function(){
	                commentNoticeType += $(this).val()+",";
	            });
				var data =$.extend({},taskObj,{inputContent:isInputContent,content:comment,fkId:taskId,title:taskObj['TASK_NAME'],commentNoticeType:commentNoticeType,source:'task'});
				ajax.remoteCall("${ctxPath}/servlet/comment?action=add",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							$("#comment").val("");
							loadCommentHistory();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		function delComment(id,obj){
			ajax.remoteCall("${ctxPath}/servlet/comment?action=del",{commentId:id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(obj).parents(".b_content").remove();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		var stopAiFlag = false;
		function aiModelGen(){
			stopAiFlag = false;
			$('.ai-result,.stop-ai-btn').show();
			$('.ai-result blockquote').html('AI请求中,稍等几秒...<br>');
			$('.start-ai-btn').hide();
			var source = new EventSource("${ctxPath}/llm/taskAnswerStreamAI/"+Task.taskId);
			source.onmessage = function(event) {
				$('.ai-result,.stop-ai-btn').show();
			    if (event.data == "DONE"||stopAiFlag) {
					source.close();
			    	stopAi();
			    	finishAi();
			    }else{
				    $('.ai-result blockquote').append(event.data);
			    }
			};

			source.addEventListener("CustomEvent", function(event) {
			    console.log("Received a CustomEvent with data:", event.data);
			}, false);

			source.onerror = function(error) {
				$('.ai-result').hide();
				$('.start-ai-btn').show();
			    console.error("EventSource error:", error);
			};
		}
		
		function stopAi(){
			stopAiFlag = true;
			$('.stop-ai-btn').hide();
			$('.start-ai-btn').show();
		}
		
		function finishAi(){
			 $.ajax({
	            url: "${ctxPath}/llm/markdownToHtml",
	            type: "POST", 
	            dataType: "text",
	            data:{id:Task.taskId},
	            success: function(response){
	                $('.ai-result blockquote').html(response);
	                layer.msg('AI回答完成',{icon:1,offset:'20px',time:1200});
	            },
	            error: function(xhr, status, error){
	                console.error("An error occurred: " + error);
	            }
	        });
		}
	    
	    $(function(){
			initFormData();
		});
		
</script>
</EasyTag:override>
<c:choose>
	<c:when test="${param.isDiv==0}">
		<%@ include file="/pages/common/layout_form.jsp" %>
	</c:when>
	<c:otherwise>
		<%@ include file="/pages/common/layout_div.jsp" %>
	</c:otherwise>
</c:choose>