<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>物料大小类</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>物料分类表</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">大类值</span>
						 	<input class="form-control input-sm" name="pCode">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">小类值</span>
						 	<input class="form-control input-sm" name="code">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">说明</span>
						 	<input class="form-control input-sm" name="name">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" data-event='enter' class="btn btn-sm btn-default" onclick="DataMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var DataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'GoodsDao.categoryList',
					id:'dataMgrTable',
					autoSort:false,
					page:false,
					height:'full-120',
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'P_CODE',
						title: '大类值',
						align:'center'
					},{
						 field:'NAME',
						 title:'说明'
					 },{
					    field: 'CODE',
						title: '小类值',
						align:'center'
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable',jumpOne:true,page:false});
			}
		}
		
		$(function(){
			$("#dataMgrForm").render({success:function(){
				DataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>