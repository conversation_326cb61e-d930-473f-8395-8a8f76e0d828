package com.yunqu.work.model;

import java.io.Serializable;

import org.easitline.common.utils.string.StringUtils;

public class FlowModel  implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private String tableName;
	
	private String itemTableName;
	
	private String flowTitle;
	
	private String formId;
	
	private int sourceType;
	
	private String flowCode;
	
	private String flowName;
	
	private String applyUrl;
	
	private String detailUrl;
	
	private String flowRemark;
	
	private String startNodeName;
	

	public String getTableName() {
		if(StringUtils.isBlank(tableName)) {
			tableName = "yq_flow_apply_ext";
		}
		return tableName;
	}

	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public String getFormId() {
		return formId;
	}

	public void setFormId(String formId) {
		this.formId = formId;
	}

	public String getFlowCode() {
		return flowCode;
	}

	public void setFlowCode(String flowCode) {
		this.flowCode = flowCode;
	}

	public String getFlowName() {
		return flowName;
	}

	public void setFlowName(String flowName) {
		this.flowName = flowName;
	}

	public String getApplyUrl() {
		return applyUrl;
	}

	public void setApplyUrl(String applyUrl) {
		this.applyUrl = applyUrl;
	}

	public String getDetailUrl() {
		return detailUrl;
	}

	public void setDetailUrl(String detailUrl) {
		this.detailUrl = detailUrl;
	}

	public int getSourceType() {
		return sourceType;
	}

	public void setSourceType(int sourceType) {
		this.sourceType = sourceType;
	}

	public String getItemTableName() {
		return itemTableName;
	}

	public void setItemTableName(String itemTableName) {
		this.itemTableName = itemTableName;
	}

	public String getFlowTitle() {
		return flowTitle;
	}

	public void setFlowTitle(String flowTitle) {
		this.flowTitle = flowTitle;
	}

	public String getFlowRemark() {
		return flowRemark;
	}

	public void setFlowRemark(String flowRemark) {
		this.flowRemark = flowRemark;
	}

	public String getStartNodeName() {
		if(startNodeName==null) {
			return "申请人";
		}
		return startNodeName;
	}

	public void setStartNodeName(String startNodeName) {
		this.startNodeName = startNodeName;
	}
	
	
	
	
}
