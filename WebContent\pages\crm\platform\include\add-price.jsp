<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>销售价格</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>

<EasyTag:override name="content">
    <div class="container-fluid">
        <form id="addPriceForm" class="form-horizontal" data-mars="CustOperateDao.salePriceRecord" data-mars-prefix="price.">
            <table class="table table-edit table-vzebra">
                <tbody>
                <tr class="hidden">
                    <td colspan="4">
                        <input type="hidden" name="price.PRICE_ID" value="${param.priceId}"/>
                        <input type="hidden" name="priceId" value="${param.priceId}"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">客户</td>
                    <td colspan="3">
                        <input type="hidden" name="price.CUST_ID" value="${param.custId}"/>
                        <input type="text" data-rules="required" name="price.CUSTOMER_NAME" value="${param.custName}" class="form-control input-sm" readonly onclick="singleCust(this)" placeholder="请点击选择订购业务的客户。"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">业务平台</td>
                    <td colspan="3">
                        <input type="hidden" name="price.PLATFORM_ID" value="${param.platformId}"/>
                        <input type="text" data-rules="required"  name="price.PLATFORM_NAME" value="${param.platformName}" class="form-control input-sm" readonly onclick="singleBusinessPlatForm(this)" placeholder="请点击选择平台"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">业务订购</td>
                    <td colspan="3">
                        <input type="hidden" name="price.PLATFORM_ORDER_ID"/>
                        <input type="text" name="price.PLATFORM_ORDER_NAME" class="form-control input-sm" readonly onclick="selectPlatformOrder(this)" placeholder="请点击选择订购信息"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">产品名称</td>
                    <td colspan="3">
                        <input type="hidden" name="price.PRODUCT_ID" id="productId"/>
                        <input type="text" id="selectGoods" name="price.PRODUCT_NAME"
                               class="form-control input-sm" data-rules="required" readonly placeholder="请点击选择产品"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">供应商</td>
                    <td colspan="3">
                        <input type="hidden" name="price.SUPPLIER_ID" id="supplierId"/>
                        <input type="text" name="price.SUPPLIER_NAME" class="form-control input-sm"
                             readonly placeholder="选择产品后自动关联供应商" data-rules="required" />
                    </td>
                </tr>
                <tr>
                    <td class="required">供应商价格</td>
                    <td>
                        <input type="number" data-rules="required" name="price.SUPPLIER_PRICE" class="form-control input-sm"/>
                    </td>
                    <td class="required">采购税率(%)</td>
                    <td>
                        <input type="number" data-rules="required" name="price.BUY_TAX_RATE" class="form-control input-sm"/>
                    </td>
                </tr>
                <tr>
                    <td class="required">销售价格</td>
                    <td>
                        <input type="number" data-rules="required" name="price.SALES_PRICE" class="form-control input-sm"/>
                    </td>
                    <td class="required">销售税率(%)</td>
                    <td>
                        <input type="number" data-rules="required" name="price.SALE_TAX_RATE" class="form-control input-sm"/>
                    </td>
                </tr>
                <tr>
                    <td>价格类型</td>
                    <td>
                        <input type="text" name="price.PRICE_TYPE" class="form-control input-sm"/>
                    </td>
                    <td>价格单位</td>
                    <td>
                        <input type="text" name="price.PRICE_UNIT" placeholder="月/分钟/线/条/坐席..." class="form-control input-sm"/>
                    </td>
                </tr>
                <tr>
                    <td>付款方式</td>
                    <td>
                        <input type="text" name="price.PAYMENT_METHOD" class="form-control input-sm"/>
                    </td>
                    <td>状态</td>
                    <td>
                        <select name="price.STATUS" class="form-control input-sm">
                            <option value="0">正常</option>
                            <option value="1">停用</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>备注</td>
                    <td colspan="3">
                        <textarea name="price.REMARK" class="form-control input-sm" rows="4"></textarea>
                    </td>
                </tr>
                </tbody>
            </table>
            <p class="layer-foot text-c">
                <button type="button" class="btn btn-primary btn-sm" style="width: 80px"
                        onclick="PriceEdit.ajaxSubmitForm()">保 存
                </button>
                <button type="button" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll()">关 闭
                </button>
            </p>
        </form>
    </div>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript">
        jQuery.namespace("PriceEdit");
        
        PriceEdit.priceId = '${param.priceId}';
        PriceEdit.isNew = '${param.isNew}';
        PriceEdit.custId = '${param.custId}';
        PriceEdit.custName = '${param.custName}';

        $(function(){
            $("#addPriceForm").render({
                success:function(result){

                }
            });
        });

        layui.config({
            base: '${ctxPath}/static/js/'
        }).use('tableSelect'); //加载自定义模块

        layui.use(['tableSelect'], function(){
            var tableSelect = layui.tableSelect;
            tableSelect.render({
                elem: '#selectGoods',
                searchKey: 'productName',
                checkedKey: 'PRODUCT_ID',
                page:true,
                searchPlaceholder: '请输入产品名称',
                table: {
                    mars: 'SupplierDao.productSelect',
                    cols: [[
                        { type: 'radio' },
                        { field: 'PRODUCT_NAME',title: '产品名称'},
                        { field: 'TYPE_CODE', title: '物料类别'},
                        { field: 'PRICE', title: '含税单价'},
                        { field: 'UNIT', title: '单位'}
                    ]]
                },
                done: function (elem, data) {
                    var row = data.data;
                    var names = [];
                    var ids = [];
                    layui.each(row, function (index, item) {
                        names.push(item.PRODUCT_NAME)
                        ids.push(item.PRODUCT_ID)
                    });
                    elem.attr("ts-selected",ids.join(","));
                    elem.val(names.join(","));
                    elem.prev().val(ids.join(","));
                    $("[name='price.PRODUCT_NAME']").val(names.join(","));
                    getSupplierName(row[0]['SUPPLIER_ID']);
                    $("[name='price.SUPPLIER_ID']").val(row[0]['SUPPLIER_ID']);
                    $("[name='price.BUY_TAX_RATE']").val(row[0]['TAX_RATE']);
                    $("[name='price.SUPPLIER_PRICE']").val(row[0]['PRICE']);
                },
                clear:function(elem){
                    elem.prev().val("");
                }
            });
        });

        function getSupplierName(id){
            ajax.remoteCall("${ctxPath}/servlet/supplier?action=getSupplierName",{id:id},function(result) {
                if(result.state == 1){
                    $("[name='price.SUPPLIER_NAME']").val(result.data);
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        PriceEdit.ajaxSubmitForm = function(){
            if(form.validate("#addPriceForm")){
                var data = form.getJSONObject("#addPriceForm");
                delete data['price.PLATFORM_ORDER_NAME'];
                if(this.priceId|| this.isNew === "0"){
                    this.updateData(data);
                }else{
                    this.insertData(data);
                }
            }
        }

        PriceEdit.insertData = function(data){
            ajax.remoteCall("${ctxPath}/servlet/custOperate?action=addPrice",data,function(result){
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadPriceList();
                    });
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        PriceEdit.updateData = function(data){
            ajax.remoteCall("${ctxPath}/servlet/custOperate?action=updatePrice",data,function(result){
                if (result.state == 1) {
                    layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                        layer.closeAll();
                        reloadPriceList();
                    });
                }else{
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        function selectPlatformOrder(el){
            var id = new Date().getTime();
            $(el).attr('data-sid',id);
            popup.layerShow({
                id:'selectOrder',
                full:fullShow(),
                scrollbar:false,
                area:['700px','500px'],
                offset:'20px',
                title:'选择订购',
                url:'/yq-work/pages/crm/platform/select/select-platform-order.jsp',
                data:{
                    sid:id,
                    type:'radio',
                    platformId:$("[name='price.PLATFORM_ID']").val(),
                    custName:$("[name='price.CUSTOMER_NAME']").val()
                }
            });
        }

        function PlatformOrderSelectCallBack(data) {
            $("[name='price.PLATFORM_ORDER_ID']").val(data.orderIds);
            $("[name='price.PLATFORM_ORDER_NAME']").val(data.orderNames);
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>