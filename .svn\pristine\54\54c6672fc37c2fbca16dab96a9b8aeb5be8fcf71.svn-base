<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="GoodsEditForm" class="form-horizontal" data-mars="GoodsDao.record" autocomplete="off" data-mars-prefix="goods.">
 		   		<input type="hidden" value="${param.goodsId}" name="goodsId"/>
 		   		<input type="hidden" value="${param.goodsId}" name="goods.GOODS_ID"/>
 		   		<input type="hidden" value="${param.orderId}" name="goods.ORDER_ID"/>
 		   		<input type="hidden" value="${param.supplierId}" name="supplierId"/>
				<table class="table table-vzebra">
			        <tbody>
		                
		                <tr>
		                	<td class="required">所属产品</td>
		                    <td colspan="3">
		                    	<input name="goods.PRODUCT_ID" type="hidden">
								<input type="text" id="selectGoods" data-rules="required" readonly="readonly" name="goods.CATEGORY_NAME" class="form-control"/>
		                    </td>
		                </tr>
		                <tr>
		                	<td class="required">描述</td>
		                    <td colspan="3">
								<input data-rules="required" name="goods.NAME" class="form-control">
		                    </td>
		                </tr>
			            <tr>
		                    <td class="required">物料编号</td>
		                    <td>
		                   		 <input type="text" data-rules="required" name="goods.GOODS_NO" class="form-control">
		                    </td>
		                    <td>数量</td>
		                    <td>
		                   		 <input type="number" data-rules="required" onchange="calcPrice()" value="1" name="goods.NUMBER" class="form-control">
		                    </td>
			            </tr>
			            <tr>
		                	<td>发票类型</td>
		                    <td>
		                   		 <select name="goods.INVOICE_TYPE" data-rules="required" class="form-control">
		                   		 	<option value="1">增值税专用发票</option>
		                   		 	<option value="2">普通发票</option>
		                   		 </select>
		                    </td>
		                    <td>税率(%)</td>
		                    <td>
		                   		 <input type="number" name="goods.TAX_RATE" onchange="calcPrice()" data-rules="required" class="form-control">
		                    </td>
		                </tr>
		                  <tr>
		                	<td>含税单价</td>
		                    <td>
		                   		 <input type="number" name="goods.PRICE" onchange="calcPrice()" data-rules="required" class="form-control">
		                    </td>
		                    <td>含税总价</td>
		                    <td>
		                   		 <input type="number" name="goods.TOTAL_PRICE" readonly="readonly" data-rules="required" class="form-control">
		                    </td>
		                </tr>
		                <tr>
		                	<td>单价</td>
		                    <td>
		                   		 <input type="number" name="goods.R_PRICE" readonly="readonly" data-rules="required" class="form-control">
		                    </td>
		                    <td>总价</td>
		                    <td>
		                   		 <input type="number" name="goods.R_TOTAL_PRICE" readonly="readonly" data-rules="required" class="form-control">
		                    </td>
		                </tr>
		                <tr>
			            	<td>到货日期</td>
			               	<td colspan="3">
	                           <input class="form-control" name="goods.PLAN_ARRIVAL_TIME"/>
			               	</td>
			            </tr>
		                <tr>
			            	<td>备注说明</td>
			               	<td colspan="3">
	                           <textarea style="height: 80px;" class="form-control" name="goods.REMARK"></textarea>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="GoodsEdit.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("GoodsEdit");
	    
		GoodsEdit.goodsId='${param.goodsId}';
		GoodsEdit.isCopy='${param.isCopy}';
		
		var goodsId='${param.goodsId}';
		
		$(function(){
			$("#GoodsEditForm").render({success:function(result){

			}});  
		});
		
		layui.config({
			  base: '${ctxPath}/static/js/'
		}).use('tableSelect'); //加载自定义模块
		
		layui.use(['tableSelect'], function(){
			var tableSelect = layui.tableSelect;
			tableSelect.render({
				elem: '#selectGoods',
				searchKey: 'productName',
				checkedKey: 'PRODUCT_ID',
				page:true,
				searchPlaceholder: '请输入产品名称',
				table: {
					mars: 'SupplierDao.productSelect',
					cols: [[
						{ type: 'radio' },
						{ field: 'PRODUCT_NAME',title: '产品名称'},
						{ field: 'TYPE_CODE', title: '物料类别'},
						{ field: 'PRICE', title: '含税单价'},
						{ field: 'UNIT', title: '单位'}
					]]
				},
				done: function (elem, data) {
					var row = data.data;
					var names = [];
					var ids = [];
					layui.each(row, function (index, item) {
						names.push(item.PRODUCT_NAME)
						ids.push(item.PRODUCT_ID)
					});
					elem.attr("ts-selected",ids.join(","));
					elem.val(names.join(","));
					elem.prev().val(ids.join(","));
					$("[name='goods.NAME']").val(names.join(","));
					getGoodsNo(row[0]['TYPE_CODE']);
					
					$("[name='goods.INVOICE_TYPE']").val(row[0]['INVOICE_TYPE']);
					$("[name='goods.TAX_RATE']").val(row[0]['TAX_RATE']);
					$("[name='goods.PRICE']").val(row[0]['PRICE']);
					calcPrice();
				},
				clear:function(elem){
					 elem.prev().val("");
				}
			});
		});
		
		function calcPrice(){
			var val = $("[name='goods.PRICE']").val();
			var rate = $("[name='goods.TAX_RATE']").val();
			var num = $("[name='goods.NUMBER']").val();
			$("[name='goods.TOTAL_PRICE']").val(num*val);
			if(rate){
				var v1 = numDiv(100 - rate,100)*val;
				$("[name='goods.R_PRICE']").val(v1);
				$("[name='goods.R_TOTAL_PRICE']").val(v1*num);
			}else{
				layer.msg('请填写税率');
			}
		}
		
		GoodsEdit.ajaxSubmitForm = function(){
			if(form.validate("#GoodsEditForm")){
				if(GoodsEdit.isCopy=='1'){
					GoodsEdit.insertData(); 
				}
				else if(GoodsEdit.goodsId){
					GoodsEdit.updateData(); 
				}else{
					GoodsEdit.insertData(); 
				}
			};
		}
		GoodsEdit.insertData = function(flag) {
			var data = form.getJSONObject("#GoodsEditForm");
			ajax.remoteCall("${ctxPath}/servlet/goods?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadDataList();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		GoodsEdit.updateData = function(flag) {
			var data = form.getJSONObject("#GoodsEditForm");
			ajax.remoteCall("${ctxPath}/servlet/goods?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadDataList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function getGoodsNo(id){
			ajax.remoteCall("${ctxPath}/servlet/goods?action=getGoodsNo",{id:id},function(result) { 
				if(result.state == 1){
					$("[name='goods.GOODS_NO']").val(result.data);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>