package com.yunqu.work.dao.flow;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "FlowConfigDao")
public class FlowConfigDao extends AppDaoContext{

	@WebControl(name = "myFlowCategoryList",type = Types.TEMPLATE)
	public JSONObject myFlowCategoryList() {
		EasySQL sql = getEasySQL("select * from (");
		sql.append("select * from yq_flow_category where 1=1");
		sql.append(param.getString("pFlowCode"),"and p_flow_code = ?");
		sql.appendLike(param.getString("flowName"),"and flow_name like ?");
		sql.append(0,"and flow_state = ?");
		if(!isSuperUser()) {
			sql.append(getDeptId(),"and (flow_auth_depts = 0  or find_in_set(?,flow_auth_depts))");
		}
		sql.append("union");
		
		sql.append("select * from yq_flow_category where 1=1");
		sql.append("and p_flow_code = '0'");
		sql.append(0,"and flow_state = ?");
		
		sql.append(") t");
		sql.append("order by t.flow_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "categoryList",type = Types.TEMPLATE)
	public JSONObject categoryList() {
		EasySQL sql = getEasySQL("select * from yq_flow_category where 1=1");
		sql.append(param.getString("flowState"),"and flow_state = ?");
		sql.append(param.getString("pFlowCode"),"and p_flow_code = ?");
		sql.append("order by flow_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "fieldList",type = Types.PAGE)
	public JSONObject fieldList() {
		EasySQL sql = getEasySQL("select * from yq_flow_field where 1=1");
		sql.append(param.getString("flowCode"),"and flow_code = ?");
		sql.append("order by idx_order");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "flowFields",type = Types.TEMPLATE)
	public JSONObject flowFields() {
		EasySQL sql = getEasySQL("select * from yq_flow_field where 1=1");
		sql.append(param.getString("flowCode"),"and flow_code = ?",false);
		sql.append("order by idx_order");
		JSONObject result = queryForList(sql.getSQL(),sql.getParams());
		JSONArray array = result.getJSONArray("data");
		JSONArray list = new JSONArray();
		for(int i= 0;i<array.size();i++) {
		   JSONObject row =	array.getJSONObject(i);
		   JSONObject extJson = JSONObject.parseObject(row.getString("EXT_CONFIG"));
		   if(extJson!=null) {
			   int listShow = extJson.getIntValue("listshow");
			   if(listShow==1) {
				   JSONObject record = new JSONObject();
				   record.putAll(extJson);
				   record.put("field", row.getString("TABLE_FIELD").toUpperCase());
				   record.put("title", row.getString("TITLE"));
				   list.add(record);
			   }
		   }
		}
		return getJsonResult(list);
	}
	
	@WebControl(name="fieldInfo",type=Types.RECORD)
	public JSONObject fieldInfo(){
		String fieldId=param.getString("fieldId");
		if(StringUtils.isBlank(fieldId)){
			return getJsonResult(new JSONObject());
		}
		return queryForRecord("select * from yq_flow_field where FIELD_ID = ?", fieldId);
	}
	
	
	@WebControl(name = "groupList",type = Types.TEMPLATE)
	public JSONObject groupList() {
		EasySQL sql = getEasySQL("select * from yq_group_user where 1=1");
		sql.append(getUserId(),"and creator = ?");
		sql.append("order by create_time");
		return queryForList(sql.getSQL(),sql.getParams());
	}

	@WebControl(name = "nodeConfig",type=Types.LIST)
	public JSONObject nodeConfig() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_node t1 where 1=1");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?",false);
		sql.append("order by t1.check_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "roleConfig",type=Types.LIST)
	public JSONObject roleConfig() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_role t1 where 1=1");
		sql.append(param.getString("roleName"),"and t1.role_name = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeDict",type=Types.DICT)
	public JSONObject nodeDict() {
		EasySQL sql = getEasySQL("select t1.node_id,t1.node_name from yq_flow_approve_node t1 where 1=1");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?",false);
		sql.append(param.getString("nodeId"),"and t1.node_id <> ?");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeInfo",type=Types.RECORD)
	public JSONObject nodeInfo() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_node t1 where 1=1");
		String nodeId = param.getString("nodeId");
		if(StringUtils.isBlank(nodeId)) {
			return getJsonResult(null);
		}
		sql.append(nodeId,"and t1.node_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "roleInfo",type=Types.RECORD)
	public JSONObject roleInfo() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_role t1 where 1=1");
		String roleId = param.getString("roleId");
		if(StringUtils.isBlank(roleId)) {
			return getJsonResult(null);
		}
		sql.append(roleId,"and t1.role_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name = "roleDict",type = Types.DICT)
	public JSONObject roleDict() {
		return getDictByQuery("select role_id,role_name from yq_flow_approve_role");
	}
	
	@WebControl(name = "categoryDict",type = Types.DICT)
	public JSONObject categoryDict() {
		return getDictByQuery("select flow_code,flow_name from yq_flow_category where  p_flow_code = ?","0");
	}
	
	@WebControl(name = "categoryInfo",type = Types.RECORD)
	public JSONObject categoryInfo() {
		String flowCode = param.getString("conf.FLOW_CODE");
		return queryForRecord("select * from yq_flow_category where flow_code = ?", flowCode);
	}

	

	
}
