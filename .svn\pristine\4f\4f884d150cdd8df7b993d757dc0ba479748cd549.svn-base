<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
		.layui-table td, .layui-table th{padding: 9px 6px;}
		.layui-table input{width: 100%!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="editForm">
			<div class="ibox">
				<div class="ibox-title mb-15 clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-inbox"></span> 月工作量统计</h5>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">年份</span>	
							 <select name="year" onchange="loadData()" class="form-control input-sm">
		                    		<option value="2020">2020</option>
	                    	</select>
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">月份</span>	
							 <select name="month" onchange="loadData()" class="form-control input-sm">
		                    		<option value="01">1</option>
		                    		<option value="02">2</option>
		                    		<option value="03">3</option>
		                    		<option value="04">4</option>
		                    		<option value="05">5</option>
		                    		<option value="06">6</option>
		                    		<option value="07">7</option>
		                    		<option value="08">8</option>
		                    		<option value="09">9</option>
		                    		<option value="10">10</option>
		                    		<option value="11">11</option>
		                    		<option value="12">12</option>
	                    	</select>
					     </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="req.ajaxSubmitForm()">保存</button>
						 </div>
	          	     </div>
	              </div> 
			    <div data-mars="ProjectDao.queryDeptUser" data-template="tpl" data-container="container" class="layui-row mt-15 layui-col-space10" id="container"></div>
				<div style="height: 120px;"></div>
				<script type="text/x-jsrender" id="tpl">
					{{for data}}
						 <div class="layui-col-md4">
							<div class="layui-card">
							  <div class="layui-card-header">
								{{:#index+1}}.{{:USERNAME}}
								<input type="hidden"  name="userId" value="{{:USER_ID}}"/>
							  </div>
							  <div class="layui-card-body">
							  <table class="layui-table">
							  <thead>
								<tr>
								  <th>项目名称</th>
								  <th>工作量占比%</th>
								  <th><a class="btn btn-xs btn-link" onclick="addTr('{{:USER_ID}}');" href="javascript:;">+添加</a></th>
								</tr> 
							  </thead>
							  <tbody id="tbody_{{:USER_ID}}">
								{{for #parent.parent.data.workHours}}
									{{if #parent.parent.data.USER_ID==WORKER}}
										<tr class="{{:WORKER}}_{{:#getIndex()}}">
											<td>
											  <input type="hidden"  name="{{:WORKER}}_id" value="{{:#getIndex()}}"/>
											  <input type="hidden" name="{{:WORKER}}_project_{{:#getIndex()}}" value="{{:PROJECT_ID}}"/>
											  <input value="{{:PROJECT_NAME}}" ts-selected="{{:PROJECT_ID}}" name="{{:WORKER}}_name_{{:#getIndex()}}" id="{{:WORKER}}_{{:PROJECT_ID}}_{{:#getIndex()}}" type="text" class="form-control projectId"/>
											</td>
											<td><input style="width:50px;" value="{{:WORK_TIME}}" name="{{:WORKER}}_time_{{:#getIndex()}}" type="text" class="form-control"></td>
											<td style="width:30px;"><a class="btn btn-xs btn-link" href="javascript:;" onclick="$('.{{:WORKER}}_{{:#getIndex()}}').remove();">删除</a></td>
										</tr>
									{{/if}}
								{{/for}}
								<c:forEach var="item" begin="100" end="120">
									<tr class="{{:USER_ID}}_${item}" <c:if test="${item>=101}">style="display:none;" data-hide-tr="1"</c:if>>
									   <td>
											<input type="hidden"  name="{{:USER_ID}}_id" value="${item}"/>
											<input type="hidden" name="{{:USER_ID}}_project_${item}" value=""/>
											<input name="{{:USER_ID}}_name_${item}" id="{{:USER_ID}}_${item}" type="text" class="form-control projectId"/>
										</td>
									   <td style="width:100px">
											<input style="width:50px;" name="{{:USER_ID}}_time_${item}" type="text" class="form-control">
									   </td>
									   <td  style="width:30px;">
										<a class="btn btn-xs btn-link" href="javascript:;" onclick="$('.{{:USER_ID}}_${item}').remove();">删除</a>
									   </td>
									</tr>
								</c:forEach>
							  </tbody>
							</table>
							  </div>
							</div>
						  </div>
					{{/for}}
				</script>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		
		layui.config({
			  base: '${ctxPath}/static/js/'
		}).use('tableSelect'); //加载自定义模块

		function loadData(){
			$("#editForm").render({success:function(){
				layui.use(['element','tableSelect'], function(){
					var tableSelect = layui.tableSelect;
					$(".projectId").each(function(){
						var id =$(this).attr("id");
						tableSelect.render({
							elem: "#"+id,
							searchKey: 'projectName',
							checkedKey: 'PROJECT_ID',
							page:true,
							limit:30,
							searchPlaceholder: '请输入项目名称',
							table: {
								mars: 'ProjectContractDao.projectList',
								cols: [[
									{ type: 'radio' },
									{ field: 'PROJECT_ID',hide:true,title: 'ID'},
									{ field: 'PROJECT_NAME', title: '名称', width: 320 },
									{ field: 'PROJECT_NO', title: '编号', width: 150 }
								]]
							},
							done: function (elem, data) {
								var names = [];
								var ids = [];
								layui.each(data.data, function (index, item) {
									names.push(item.PROJECT_NAME)
									ids.push(item.PROJECT_ID)
								});
								elem.attr("ts-selected",ids.join(","));
								elem.val(names.join(","));
								elem.prev().val(ids.join(","));
							},
							clear:function(elem){
								  elem.prev().val("");
							}
						});
					});
			
				});
			}});
		}
		$(function(){
			//设置默认月份
			loadData();
		});
		
		var req={};
		req.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				req.insertData(); 
			}
		}
		req.insertData = function() {
			$("[data-hide-tr]").remove();
			
			var userIds = $("[name='userId']");
			if(userIds.length>0){
				userIds.each(function(){
					var userObj=$(this);
					var userId=$(this).val();
					var times=$("[name^='"+userId+"_time']");
					var sumTime=0;
					if(times.length>0){
						times.each(function(){
							var time=$(this).val();
							if(time){
							  sumTime+=parseInt(time);
							}
						});
					}
					if(sumTime>0&&sumTime!=100){
						layer.msg($(userObj).parent().text()+"填报的总工作量是"+sumTime+",不足100.");
						return false;
					}
				});				
				
			}		
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/project?action=addProjectWorkHour",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();

					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function addTr(userId){
			var obj  =$("#tbody_"+userId).find("[data-hide-tr]").first();
			obj.removeAttr("data-hide-tr");
			obj.show()
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>