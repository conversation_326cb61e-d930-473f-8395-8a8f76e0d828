<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 150px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" value="续签劳动合同申请单"   name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 150px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="hidden" name="apply.data1">
					  				<input type="text" data-rules="required" data-ref-dept="#deptName" data-ref-no="#staffNo" data-ref-job="#jobTitle" onclick="singleUser(this)" readonly="readonly" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  			<td class="required">工号</td>
					  			<td>
					  				<input type="text" data-rules="required" id="staffNo" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" id="deptName" onclick="singleDept(this)" readonly="readonly" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  			<td class="required">职位</td>
					  			<td>
					  				<input type="text" data-rules="required" id="jobTitle" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">劳动合同到期时间</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'date'}" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  			<td class="required">现月薪(税前)</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">绩效比例</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data8"/>
					  			</td>
					  			<td class="required">现年薪(税前)</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data9"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">续签次数</td>
					  			<td>
					  				<select class="form-control input-sm" data-rules="required" name="apply.data10">
					  					<option value="">--</option>
					  					<option value="首次续签">首次续签</option>
					  					<option value="第二次续签">第二次续签</option>
					  				</select>
					  			</td>
					  			<td class="required">续签类型</td>
					  			<td>
					  				<select class="form-control input-sm" data-rules="required" name="apply.data11">
					  					<option value="">--</option>
					  					<option value="3年固定期">3年固定期</option>
					  					<option value="无固定期">无固定期</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove leaderSay">
					  			<td>部门主管意见</td>
					  			<td>
					  				<select class="form-control input-sm" onchange="changeType(this.value)" data-rules="required" name="apply.data12">
					  					<option value="">--</option>
					  					<option value="不同意续签">不同意续签</option>
					  					<option value="同意续签,薪资不变">同意续签,薪资不变</option>
					  					<option value="同意续签,薪资调整">同意续签,薪资调整</option>
					  				</select>
					  			</td>
					  			<td></td>
					  			<td></td>
					  		</tr>
					  		<tr class="edit-remove leaderSay refuse-reason">
					  			<td>不同意续签原因</td>
					  			<td colspan="3">
					  				<input type="text" data-rules="required" value="" class="form-control input-sm" name="apply.data13"/>
					  			</td>
					  		</tr>
					  		<tr class="edit-remove leaderSay change-salary">
					  			<td>调整薪资</td>
					  			<td colspan="3">
					  				<input type="text" data-rules="required" value="月薪___元（税前），年薪___元（税前），调薪生效日期___年__月___日。" class="form-control input-sm" name="apply.data14"/>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideUserTitle:true,title:'{{:data2}}的续签劳动合同申请单',success:function(data){
				Flow.initData();
				var applyInfo = FlowCore.applyInfo;
				var data12 = applyInfo.data12;
				changeType(data12);
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		Flow.initData = function(){
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var data11 = applyInfo.data11;
			var data12 = applyInfo.data12;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			
			if(nodeCode=='中心副总'){
				if(data11=='无固定期'||data12.indexOf('薪资调整')>-1){
					params['nextNodeCode'] = '职能总监';
				}else{
					params['nextNodeCode'] = '0';
				}
			}
			FlowCore.approveData = params;
		}

		Flow.approverLayerBefore = function(){
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			if(nodeCode=='deptLeader'||nodeCode=='中心副总'){
				var data12 = $("[name='apply.data12']").val();
				if(data12==''){
					layer.msg('请选择续签意见');
					return false;
				}
			}
			return true;
	     }
		
		
		function changeType(val){
			$('.change-salary,.refuse-reason').hide();
			if(val.indexOf('薪资调整')>-1){
				$('.change-salary').show();
				var data14 = $('[name="apply.data14"]');
				if(data14.val()==''){
					data14.val('月薪___元（税前），年薪___元（税前），调薪生效日期___年__月___日。');
				}
			}else if(val.indexOf('不同意续签')>-1){
				$('.refuse-reason').show();
			}
		}
	
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>