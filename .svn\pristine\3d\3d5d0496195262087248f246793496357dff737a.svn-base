package com.yunqu.work.dao.contract;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="ProjectContractDao")
public class ContractDao extends AppDaoContext {
	
	@WebControl(name="getContractNo",type=Types.TEXT)
	public JSONObject getOrderNo(){
		long dateId = Long.valueOf(EasyCalendar.newInstance().getDateInt());
		try {
			String val = this.getQuery().queryForString("select max(CONTRACT_NO) from yq_project_contract where CONTRACT_NO like '"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult(dateId+"001");
			}else {
				return getJsonResult(Long.valueOf(val)+1);
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String contractId = param.getString("contractId");
		if(StringUtils.notBlank(contractId)){
			return queryForRecord("select * from yq_project_contract where contract_id = ?", contractId);
		}else{
			return getJsonResult(new JSONObject());
		}
	}
	
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		EasySQL sql = new EasySQL("select CONTRACT_ID,CONTRACT_NAME from YQ_PROJECT_CONTRACT where 1=1");
		sql.append(param.getString("custId"),"and CUST_ID = ?",false);
		return getDictByQuery(sql.getSQL(),sql.getParams());
	}
	
	/**
	 * 全部合同项目
	 * @return
	 */
	@WebControl(name="contractList",type=Types.LIST)
	public JSONObject contractList(){
		EasySQL sql = getEasySQL("select t1.* from yq_project_contract t1 ");
		sql.append(getUserId(),"left join (select b1.* from yq_crm_cust_team b1 where b1.ht_auth = 1 and b1.user_id = ?) t2 on t1.cust_id = t2.cust_id");
		sql.append("where 1=1");
		sql.append(param.getString("signEnt"),"and t1.SIGN_ENT = ?");
		sql.append(param.getString("prodLine"),"and t1.PROD_LINE = ?");
		sql.append(param.getString("projectDeptId"),"and t1.PROJECT_DEPT_ID = ?");
		sql.appendLike(param.getString("contractType"),"and t1.CONTRACT_TYPE like ?");
		sql.appendLike(param.getString("signOffice"),"and t1.SIGN_OFFICE like ?");
		sql.appendLike(param.getString("contractName"),"and t1.CONTRACT_NAME like ?");
		sql.appendLike(param.getString("contractNo"),"and t1.CONTRACT_NO like ?");
		sql.appendLike(param.getString("erpContractNo"),"and t1.ERP_CONTRACT_NO like ?");
		sql.appendLike(param.getString("reviewNo"),"and t1.REVIEW_ID like ?");
		if(!isSuperUser()&&!hasRole("CONTRACT_MGR")&&!hasRes("CONTRACT_LOOK_AUTH")) { 
			sql.append("and (");
			sql.append(getUserId(),"(t1.creator = ?)");
			sql.append("or");
			sql.append(getUserId(),"(t1.sales_by = ?)");
			if(isDeptLeader()) {
				sql.append("or");
				sql.append(getDeptId(),"(t1.sale_dept_id = ?)");
			}
			sql.append(")");
		}
		sql.append("group by t1.contract_id");
		
		setOrderBy(sql,"order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	private void setOrderBy(EasySQL sql,String defaultOrder) {
		String sortName =  param.getString("sortName");
		String sortType =  param.getString("sortType");
		if(StringUtils.notBlank(sortName)) {
			sql.append("order by t1.").append(sortName).append(sortType);
		}else {
			sql.append(defaultOrder);
		}
	}
	
	
	/**
	 * 全部合同项目
	 * @return
	 */
	@WebControl(name="contractSelect",type=Types.LIST)
	public JSONObject contractSelect(){
		EasySQL sql=getEasySQL("select CONTRACT_ID,CONTRACT_NAME,CONTRACT_NO,ERP_CONTRACT_NO,CONTRACT_STATE,CUSTOMER_NAME,CUST_ID,CUST_NO,AMOUNT from YQ_PROJECT_CONTRACT where 1=1");
		String contractName = param.getString("contractName");
		if(StringUtils.notBlank(contractName)){
			sql.append("and (");
			sql.appendLike(contractName,"CONTRACT_NAME like ?");
			sql.append("or");
			sql.appendLike(contractName,"CONTRACT_NO like ?");
			sql.append("or");
			sql.appendLike(contractName,"ERP_CONTRACT_NO like ?");
			sql.append(")");
		}
		sql.append("order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="bxContractSelect",type=Types.LIST)
	public JSONObject bxContractSelect(){
		String dataType = param.getString("dataType");
		EasySQL sql=getEasySQL("select t1.CONTRACT_ID,t1.CONTRACT_NAME,t1.CONTRACT_NO,t1.ERP_CONTRACT_NO,t1.CONTRACT_STATE from YQ_PROJECT_CONTRACT t1");
		if("1".equals(dataType)) {
			sql.append("inner join yq_flow_bx_item t2 on t2.contract_id = t1.contract_id");
			sql.append("inner join yq_flow_apply t3 on t3.apply_id = t2.business_id");
			sql.append(getUserId(),"where t3.apply_by = ?");
		}else {
			sql.append("where 1=1");
		}
		String contractName = param.getString("contractName");
		if(StringUtils.notBlank(contractName)){
			sql.append("and (");
			sql.appendLike(contractName,"t1.CONTRACT_NAME like ?");
			sql.append("or");
			sql.appendLike(contractName,"t1.CONTRACT_NO like ?");
			sql.append("or");
			sql.appendLike(contractName,"t1.ERP_CONTRACT_NO like ?");
			sql.append(")");
		}
		String rootId = getStaffInfo().getRootId();
		if("zr".equals(rootId)) {
			sql.append("中融德勤","and (t1.SIGN_ENT = ? or t1.CONTRACT_ID in('0','9999999999999'))");
		}else {
			sql.append("中融德勤","and (t1.SIGN_ENT <> ? or t1.CONTRACT_ID in('0','9999999999999'))");
		}
		
		sql.append("and t1.END_DATE = ''");
		if("1".equals(dataType)) {
			sql.append("group by t1.CONTRACT_ID");
			sql.append("order by t3.APPLY_TIME desc");
		}else {
			sql.append("order by t1.CREATE_TIME desc");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="custContractList",type=Types.LIST)
	public JSONObject custContractList(){
		EasySQL sql=getEasySQL("select * from YQ_PROJECT_CONTRACT where 1=1");
		sql.append(param.getString("custId"),"and CUST_ID = ?");
		sql.append("order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 全部项目
	 * @return
	 */
	@WebControl(name="projectModuleList",type=Types.LIST)
	public JSONObject projectModuleList(){
		EasySQL sql=getEasySQL("select t1.* from yq_project t1 where 1=1");
		sql.append("and t1.is_delete = 0");
		sql.append(20,"and t1.project_state < ?");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.append("order by t1.UPDATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}



	@WebControl(name="myContractList",type=Types.LIST)
	public JSONObject myContractList(){
		EasySQL sql = getEasySQL("select t1.* from yq_project_contract t1 ");
		sql.append("where 1=1");
		sql.append(param.getString("signEnt"),"and t1.SIGN_ENT = ?");
		sql.append(param.getString("prodLine"),"and t1.PROD_LINE = ?");
		sql.append(param.getString("projectDeptId"),"and t1.PROJECT_DEPT_ID = ?");
		sql.appendLike(param.getString("contractType"),"and t1.CONTRACT_TYPE like ?");
		sql.appendLike(param.getString("signOffice"),"and t1.SIGN_OFFICE like ?");
		sql.appendLike(param.getString("contractName"),"and t1.CONTRACT_NAME like ?");
		sql.appendLike(param.getString("contractNo"),"and t1.CONTRACT_NO like ?");
		sql.appendLike(param.getString("erpContractNo"),"and t1.ERP_CONTRACT_NO like ?");
		sql.appendLike(param.getString("reviewNo"),"and t1.REVIEW_ID like ?");

		if("sale".equals(param.getString("userType"))){
			sql.append(getUserId(),"and t1.sales_by = ?");
		}
		else if("create".equals(param.getString("userType"))){
			sql.append(getUserId(),"and t1.CREATOR = ?");
		}
		sql.append(EasyCalendar.newInstance().getYear(), "and t1.YEAR = ?");

		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}


}
