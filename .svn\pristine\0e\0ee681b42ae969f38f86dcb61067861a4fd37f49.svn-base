<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">加班类别</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data14">
						  				<option value="">--</option>
						  				<option value="工作日加班">工作日加班</option>
						  				<option value="周末加班">周末加班 </option>
						  				<option value="法定节假日加班">法定节假日加班</option>
						  			</select>
					  			</td>
					  			<td class="required">申请加班时间</td>
					  			<td style="width: 40%;">
					  				<input type="text" style="width: 30%;display: inline-block;" data-rules="required" onchange="calcDay()" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 09:00:00',doubleCalendar:true,alwaysUseStartDate:true,maxDate:'#F{$dp.$D(\'dateEnd\')}'})" id="dateStart" class="form-control input-sm Wdate" name="apply.data11"/>
					  				<input type="text" style="width: 30%;display: inline-block;" data-rules="required" onchange="calcDay()" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 18:00:00',doubleCalendar:true,alwaysUseStartDate:true,minDate:'#F{$dp.$D(\'dateStart\')}'})" id="dateEnd" class="form-control input-sm Wdate" name="apply.data12"/>
					  				<input type="number" style="width: 35%;display: inline-block;" data-edit-node="考勤备案" data-rules="required" placeholder="手动计算"  class="form-control input-sm" name="apply.data13"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>所属项目</td>
					  			<td>
					  				<input name="apply.data5" type="hidden">
									<input type="text" onclick="singleContract(this)" readonly="readonly" data-rules="required" name="apply.data4" class="form-control input-sm"/>
					  			</td>
					  			<td>证明人</td>
					  			<td>
					  				 <input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">加班事由</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>相关附件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  		<tr class="remindInfo hidden-print">
					  		   <td>注意事项</td>
					  		   <td colspan="3">
								
								1. 员工如需加班，应在当天下班前向直接上级提出书面申请，并说明加班原因、时间和工作内容。<br>
								
								2. 直接上级审核后，如认为加班有必要，应将申请转交给人力资源部门进行审批。<br>
								
								3. 人力资源部门在收到申请后，应及时处理并作出批准或拒绝的决定。如果需要进一步调查核实，应及时通知申请人。<br>
								
								4. 如加班被批准，员工应按照公司规定填写加班记录表，并在次日上班时提交给直接上级确认。<br>
								
								5. 如员工因紧急任务需要加班且无法提前向直接上级提出申请，可直接向人力资源部门提出申请。人力资源部门将根据实际情况进行审批。<br>
								
								6. 如员工因公出差、参加培训等原因需要加班，应事先向直接上级提出申请并征得同意。人力资源部门将根据实际情况进行审批。
					  			<a onclick="FlowCore.historyApply();" href="javascript:;">申请记录</a> 
					  		  </td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/yq-work/static/js/datediff.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){
				Flow.initData();
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.initData = function() {
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			
			if(nodeCode=='人力行政部'){
				var applyUserInfo = FlowCore.applyUserInfo;
				if(applyUserInfo.workCity=='北京'){
					params['ccNames'] = '蔡洁';
				}
			}
			FlowCore.approveData = params;
		}
		
		Flow.submitCheck = function() {
			var params = {};
			FlowCore.addCheckOrder({data:params});
		}
		
		function selctCallBack(){
			
		}

		function calcDay(){
			var start = $("[name='apply.data11']").val();
			var end = $("[name='apply.data12']").val();
			if(start&&end){
				if(end<=start){
					layer.msg('结束日期应该小于开始日期',{icon:2});
					$("[name='apply.data12']").val('');
					return;
				}
				var val = dateDiff.diffAllDay(start,end);
				$("[name='apply.data13']").val(val);
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>