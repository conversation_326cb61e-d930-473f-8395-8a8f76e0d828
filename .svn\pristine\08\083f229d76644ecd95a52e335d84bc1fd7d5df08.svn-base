<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>版本</title>
	<style>
		#versionEditForm table,tr,td,p{max-width: 100%!important;}
		#versionEditForm img{max-width: 98%!important;height: auto;}
		#versionEditForm .w-e-text{max-width: 100%!important;}
		.file-div{display: block;float: inherit;}
		.w-e-toolbar p, .w-e-text-container p, .w-e-menu-panel p {
		    font-size: 14px !important;
		}
	</style>
	<link href="/yq-work/static/css/monokai_sublime.min.css" rel="stylesheet">
	<script src="/yq-work/static/js/highlight.min.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="versionEditForm" data-mars="VersionDao.record" autocomplete="off" data-mars-prefix="version.">
     		    <input type="hidden" value="${param.versionId}" id="versionId" name="version.VERSION_ID"/>
     		    <input type="hidden" value="${param.groupId}"  name="version.GROUP_ID"/>
     		    <input type="hidden" value="${param.versionType}"  name="version.VERSION_TYPE"/>
     		    <input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
     		    <input type="hidden" id="dateId" data-mars="CommonDao.date03"/>
 		   		<input type="hidden" value="${param.versionId}" name="fkId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			           <tr>
		                    <td style="width: 80px" class="required">主题</td>
		                    <td><input data-rules="required" type="text" name="version.VERSION_TITLE" id="versionTitle" value="" class="form-control input-sm"></td>
			            </tr>
			           <c:choose>
			           		<c:when test="${param.versionType=='2'}">
					           <tr>
				                    <td style="width: 80px" class="required">产品</td>
				                    <td>
						     		    <input type="hidden" value="${param.projectId}"  name="version.PROJECT_ID"/>
				                    	<input data-rules="required" type="text" onclick="singleService(this)" readonly="readonly" name="version.PROJECT_NAME"  value="${param.projectName}" class="form-control input-sm">
				                    </td>
					            </tr>
			           		</c:when>
			           		<c:otherwise>
					           <tr>
				                    <td style="width: 80px" class="required">项目</td>
				                    <td>
						     		    <input type="hidden" value="${param.projectId}"  name="version.PROJECT_ID"/>
				                    	<input data-rules="required" type="text" onclick="singleProject(this)" readonly="readonly" name="version.PROJECT_NAME"  value="${param.projectName}" class="form-control input-sm">
						     		    <input type="hidden" value="${param.projectId}"  name="projectId"/>
				                    </td>
					            </tr>
			           		</c:otherwise>
			           </c:choose>
            	        <tr>
		                    <td>收件人</td>
		                    <td>
		                    	<input type="hidden" name="version.RECEIVER_ID"/>
		                    	<input type="text" data-rules="required" onclick="singleUser(this)" readonly="readonly" class="form-control input-sm" name="version.RECEIVER_NAME"/>
		                    </td>
		               </tr>
            	        <tr>
		                    <td>抄送人</td>
		                    <td>
		                    	<input type="hidden" name="version.RECIPIENT_ID"/>
		                    	<input type="text" data-rules="required" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="version.RECIPIENT_NAME"/>
		                    </td>
		               </tr>
			           <tr>
		                    <td style="width: 80px" class="required">版本名称</td>
		                    <td><input data-rules="required" id="versionName" data-mars="CommonDao.date03" type="text" name="version.VERSION_NAME" class="form-control input-sm"></td>
			            </tr>
			            <tr class="hidden">
		                    <td class="required">版本日期</td>
		                    <td><input type="text" data-rules="required" data-rules="required" data-mars="CommonDao.date02" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" name="version.VERION_DATE"  class="form-control input-sm Wdate"></td>
		                </tr>
		                <tr>
		                	<td style="vertical-align: top;">模板</td>
		                	<td>
		                	
		                		<div  id="moduleTd" class="layui-row layui-col-space10">
		                		
		                		</div>
		                	
		                	</td>
		                </tr>
		                <tr>
			                <td style="vertical-align: top;">相关文件</td>
			               	<td>
			               		 <div data-template="v-template-files" data-mars="FileDao.fileList"></div>
			               		 <div id="vfileList"></div>
								 <button class="btn btn-xs btn-info mt-5" type="button" onclick="$('#vlocalfile').click()">+批量添加</button>
			               	</td>
	            	    </tr>
			            <tr>
		                    <td class="required" style="vertical-align: top;">版本描述<br><a href="javascript:;" onclick="$('.w-e-icon-fullscreen').click();">全屏</a></td>
		                    <td>
		                          <div id="editor"></div>
	                          	 <textarea id="wordText" data-text="false" style="height: 500px;width:400px;display: none;" class="form-control input-sm" name="version.VERSION_DESC">
	                          	 </textarea>
		                    </td>
			            </tr>
			        </tbody>
 			     </table>
				 <div class="layer-foot text-c" style="z-index: 99999;">
			    	  <button type="button" class="btn btn-primary btn-sm m-l0 detail" style="box-shadow: 0 -10px 20px 0 rgba(45,67,118,.1);" onclick="VersionEdit.ajaxSubmitForm(2)"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>
  		</form>
  		
  		<div id="contentTpl" style="display: none;">
  			<p><b><font color="#c24f4a">【1】升级模块:</font></b></p>
  			<p>描述本次发布的模块清单</p>
  			<p><br/></p>
  			
  			<p><b><font color="#c24f4a">【2】影响范围：</font></b></p>
  			<p>描述本次升级影响的功能</p>
  			<p><br/></p>
  			
  			<p><b><font color="#c24f4a">【3】升级脚本：</font></b></p>
  			<p>粘贴本次升级涉及到的脚本，如果是使用其它文档管理脚本的，写明需要获取脚本的路径、需要执行脚本的范围</p>
  			<p><br/></p>
  			
  			<p><b><font color="#c24f4a">【4】升级说明：</font></b></p>
  			<p>描述升级现场升级war包，还需要额外处理的操作</p>
  			<p><br/></p>

  			<p><b><font color="#c24f4a">【5】升级回退：</font></b></p>
  			<p> 回退所有升级文件到上一个版本</p>
			<p>	对于修改的配置项全部还原；修改配置项后重载模块</p>
			<p>	验证话务等核心功能是否正常</p>
			
  			<p><br/></p>
  			<p><br/></p>
	                          	 	
  		
  		</div>
  		
		 <script id="module-template" type="text/x-jsrender">
			{{for data}}
				<div class="layui-col-md6">
					<div class="layui-panel">
						<div style="padding: 10px;">
							<input type="hidden" name="prevVersion_{{:MODULE_ID}}" value="{{:LAST_VERSION}}"/>
							<input type="hidden" name="moduleName_{{:MODULE_ID}}" value="{{:MODULE_NAME}}"/>
							<p>模块：{{:MODULE_NAME}}</p>
							<p>
								上次版本号：{{:LAST_VERSION}}
								，&nbsp;&nbsp;升级版本号：<input type="text" name="module.{{:MODULE_ID}}" style="display:inline-block;width:140px;" class="form-control input-sm">
							</p>
							<p class="mt-5"><textarea placeholder="升级内容描述" style="height:80px;" class="form-control input-sm" name="versionDesc_{{:MODULE_ID}}"></textarea></p>
						</div>
					</div>
				</div>
		 {{/for}}
		</script>
		 <script id="now-module-template" type="text/x-jsrender">
			{{for data}}
				<div class="layui-col-md6">
					<div class="layui-panel">
						<div style="padding: 10px;">
							<input type="hidden" name="id_{{:MODULE_ID}}" value="{{:ID}}"/>
							<input type="hidden" name="prevVersion_{{:MODULE_ID}}" value="{{:PREV_VERSION}}"/>
							<input type="hidden" name="moduleName_{{:MODULE_ID}}" value="{{:MODULE_NAME}}"/>
							<p>模块：{{:MODULE_NAME}}</p>
							<p>
								上次版本号：{{:PREV_VERSION}}
								，&nbsp;&nbsp;升级版本号：<input type="text" name="module.{{:MODULE_ID}}" style="display:inline-block;width:140px;" value="{{:NOW_VERSION}}" class="form-control input-sm">
							</p>
							<p class="mt-5"><textarea placeholder="升级内容描述" style="height:80px;" class="form-control input-sm" name="versionDesc_{{:MODULE_ID}}">{{:VERSION_DESC}}</textarea></p>
						</div>
					</div>
				</div>
		 {{/for}}
		</script>
		
		 <script id="v-template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/> {{:CREATE_NAME}} 于 {{:CREATE_TIME}} 上传文件 【{{:FILE_NAME}}】 {{:FILE_SIZE}}<a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank">下载</a>({{:DOWNLOAD_COUNT}})<i title="删除" class="detail" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="vfileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file"  multiple="multiple" type="file" id="vlocalfile" onchange="VersionEdit.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/pinyin.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
		
		jQuery.namespace("VersionEdit");
		
		VersionEdit.versionId='${param.versionId}';
		var versionId='${param.versionId}';
		var op='${param.op}';
		
		$(function(){
			var E = window.wangEditor;
			var editor = new E('#editor');
			editor.highlight = hljs;
			editor.config.menus = [
				'head',
			    'bold',
			    'fontSize',
			    'fontName',
			    'italic',
			    'underline',
			    'strikeThrough',
			    'indent',
			    'lineHeight',
			    'foreColor',
			    'backColor',
			    'link',
			    'list',
			    'todo',
			    'justify',
			    'quote',
			    'emoticon',
			    'image',
			    'video',
			    'table',
			    'code',
			    'splitLine',
			    'undo',
			    'redo'
	      	];
			weUpload(editor,{uploadImgMaxLength:3});
			editor.config.onchange = function (html) {
			     $("#wordText").val(html)
			}
			editor.create();
			
			$("#versionEditForm").render({success:function(result){
				if(versionId==''){
					editor.txt.append($('#contentTpl').html());
					 $("#versionName").val("1.0#"+$("#versionName").val()+"-01");
					 var dateId = $('#dateId').val();
					 $('#versionTitle').val('项目升级版本发布-'+dateId);
					 
					 var moduleIds = '${param.moduleIds}';
					 if(moduleIds){
						 ajax.remoteCall("${ctxPath}/servlet/version?action=modules",{moduleIds:moduleIds},function(rs) { 
							var data = rs.data;
							var tpl = $.templates('#module-template');
							var html = tpl.render({data:data});
							$('#moduleTd').html(html);;
						 });
					 }else{
						 $('#moduleTd').parent().parent().remove();
					 }
				}else{
					editor.txt.html($("#wordText").val());
					
					var moduleCount = result['VersionDao.record']['data']['MODULE_COUNT'];
					if(moduleCount==0){
						 $('#moduleTd').parent().parent().remove();
					}else{
						var modules = result['VersionDao.record']['modules'];
						var tpl = $.templates('#now-module-template');
						var html = tpl.render({data:modules});
						$('#moduleTd').html(html);;
					}
					
				}
				 
				 if(op=='detail'){
					$("#editor").html($("#wordText").val());
					$("#editor").css("color","#555").css("min-height","200px").css("border","1px solid #ddd").css("padding","10px");
					$("#versionEditForm input").attr("readonly","readonly").removeAttr("onClick");
					$(".detail").remove();
					$('#editor').css('border','1px solid #ddd').css('padding','10px');
				 }
				 
				 $(".w-e-text-container").css("height","160px");
				 $("#versionEditForm .w-e-text-container").css("height",$(window).height()-450);
				 
				 $("#versionEditForm .w-e-text-container").keydown(function(event) {
					  if(event.keyCode == "13") {
						 var height = $(this).height();
						 $(this).css('height',(height+20)+'px');
					  }
					   if(event.keyCode == 8) {
						 var height = $(this).height();
						 height=height>300?height:300;
						 $(this).css('height',(height-20)+'px');
					   }
				});
				 
			}});
		});
		VersionEdit.ajaxSubmitForm = function(){
			if(form.validate("#versionEditForm")){
				if(VersionEdit.versionId){
					VersionEdit.updateData(); 
				}else{
					VersionEdit.insertData(); 
				}
			};
		}
		VersionEdit.uploadFile = function(){
			var fkId='';
			if(VersionEdit.versionId){
				fkId=versionId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'vcallback',fileId:'vlocalfile',formId:'vfileForm',fkId:fkId,source:'version',fileMaxSize:1024*50});
			
		}
	
		var vcallback = function(result,params){
			var array = [];
			if(isArray(result)){
				array = result;
			}else{
				array[0] = result;
			}
			for(var index in array){
				var data = array[index];
				$("#vfileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span>'+data.name+'-('+data.size+')<a href="'+data.url+'" target="_blank">下载</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
			}
			ajax.remoteCall("${ctxPath}/servlet/version?action=updateFile",{id:params['fkId']},function(result) {
				
			});
			
		}
		
		
		VersionEdit.insertData = function() {
			$("#versionId").val($("#randomId").val());
			var data = form.getJSONObject("#versionEditForm");
			ajax.remoteCall("${ctxPath}/servlet/version?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadVersion();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		VersionEdit.updateData = function() {
			var data = form.getJSONObject("#versionEditForm");
			ajax.remoteCall("${ctxPath}/servlet/version?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						reloadVersion();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>