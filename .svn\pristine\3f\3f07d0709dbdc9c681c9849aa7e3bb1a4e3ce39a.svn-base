package com.yunqu.work.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;

public class FileRowMapper implements EasyRowMapper<FileModel> {

	@SuppressWarnings("unchecked")
	@Override
	public FileModel mapRow(ResultSet rs, int convertField) {
		FileModel model = new FileModel();
		try {
			model.setFileId(rs.getString("FILE_ID"));
			model.setFkId(rs.getString("FK_ID"));
			model.setFileName(rs.getString("FILE_NAME"));
			model.setDiskPath(rs.getString("DISK_PATH"));
			model.setFileSize(rs.getString("FILE_SIZE"));
			model.setFileLenth(rs.getLong("FILE_LENTH"));
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return model;
	}

}
