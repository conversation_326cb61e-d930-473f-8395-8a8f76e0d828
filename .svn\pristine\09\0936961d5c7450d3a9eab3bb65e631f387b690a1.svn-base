<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>动弹分享</title>
	<link href="${ctxPath}/static/css/admin.css" rel="stylesheet">
	<link href="//at.alicdn.com/t/font_1002403_34h7imibmwx.css" rel="stylesheet">
	<style type="text/css">
		 .tw-list{padding: 0px 0px 10px!important;}
    	 .tw-list [data-w-e]{width: 24px;}
    	 .tw-list span img{max-width: 50%!important;margin: 0 2px;max-height: 300px;}
    	 #searchForm img{max-width: 50%!important;max-height: 300px!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
	   <div class="layui-card tw-card">
          	 <div class="layui-card-header">动弹分享
          	 	<button class="layui-btn layui-btn-xs layui-btn-primary ml-15" style="margin-top: -4px;" type="button" onclick="addTwitter()">+发布</button>
         	 </div>
          	 <div class="layui-card-body">
              <ul class="layuiadmin-card-status layuiadmin-home2-usernote layui-text tw-list" data-template="tw-template" data-mars="HomeDao.tweet"></ul>
              <div id="pageContainer" class="ml-30"></div>
       		  <script id="tw-template" type="text/x-jsrender">
				 {{if data.length==0}}<li><p style="text-align:center;">暂无数据</p></li>{{/if}}
				 {{for data}}
					<li>
						{{if PUBLISH_BY=='匿名'}}<i style="width: 38px;height: 38px;float:left;font-size:30px;color:#ce67b8;text-align:center;" class="iconfont icon-niming1 mt-10"></i>{{else}} <img style="width: 38px;height: 38px;border-radius:50%;float:left"  onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}"> {{/if}}
						 <div style="margin-top:8px;margin-left:50px;"> 
						  <div style="color:red;vertical-align: top;">{{:PUBLISH_BY}}</div>
						  <div style="color:#111;"  title="{{:PUBLISH_TIME}}">{{call:CONTENT fn='getTwContent'}}</div>
						 </div>
						 <div style="margin-left:48px;color:#777;font-size:12px;">{{call:PUBLISH_TIME fn='getDateDes'}}<a href="javascript:void(0)" onclick="remindDetail('{{:REMIND_ID}}','{{:REMIND_TYPE}}')" class="layui-btn layui-btn-xs layuiadmin-reply">查看</a></div>
					</li>
				 {{/for}}
        	   </script>
          </div>
      </div>	
	</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
	
		$(function(){
			initPage();
		});
		
		function initPage(){
			$('#searchForm').render({data:{pageIndex:1,pageType:3,pageSize:20},success:function(result){
				layer.photos({photos: '#searchForm',anim: 0,shade:0,shadeClose:true,closeBtn:true});
				 layui.use('laypage', function(){
					  var data = result['HomeDao.tweet'];
					  if(data){
						  var laypage = layui.laypage;
						  laypage.render({
						    elem: 'pageContainer',
						    limit:20,
						    layout:['page','prev','next','limit','count'],
						    count: data.totalRow,
						    jump:function(obj, first){
						        if(!first){
						        	$("#searchForm").render({data:{pageIndex:obj.curr,pageType:3,pageSize:obj.limit}});
						        }
						    }
						  });
					  }
					});
			}});
		}
		
		function addTwitter(){
			var width=$(window).width();
			var fullFlag=false;
			if(width<700){
				fullFlag=true;
			}
			popup.layerShow({type:1,full:fullFlag,closeBtn:0,anim:0,scrollbar:false,offset:'20px',area:['550px','440px'],url:'${ctxPath}/pages/remind/twitter.jsp',title:false});
		}
		
		function reloadTwitter(){
			initPage();
		}
		
		function remindDetail(remindId,remindType){
			popup.openTab({id:"remindDetail",url:'${ctxPath}/pages/remind/remind-detail.jsp',title:'详情',data:{remindId:remindId,remindType:remindType}});
		}
		
		function getTwContent(val){
			//val = val.replaceAll("img src","img data-src")
			return val;
		}
		
		function getDateDes(dateStr){
			return getDateDiff(getDateTimeStamp(dateStr));
		}
		
		function getDateTimeStamp(dateStr){
			 return Date.parse(dateStr.replace(/-/gi,"/"));
		}
		
		function getDateDiff(dateTimeStamp){
			var minute = 1000 * 60;
			var hour = minute * 60;
			var day = hour * 24;
			var halfamonth = day * 15;
			var month = day * 30;
			var now = new Date().getTime();
			var diffValue = now - dateTimeStamp;
			if(diffValue < 0){return;}
			var monthC =diffValue/month;
			var weekC =diffValue/(7*day);
			var dayC =diffValue/day;
			var hourC =diffValue/hour;
			var minC =diffValue/minute;
			if(monthC>=1){
				result="" + parseInt(monthC) + "月前";
			}
			else if(weekC>=1){
				result="" + parseInt(weekC) + "周前";
			}
			else if(dayC>=1){
				result=""+ parseInt(dayC) +"天前";
			}
			else if(hourC>=1){
				result=""+ parseInt(hourC) +"小时前";
			}
			else if(minC>=1){
				result=""+ parseInt(minC) +"分钟前";
			}else
			result="刚刚";
			return result;
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>