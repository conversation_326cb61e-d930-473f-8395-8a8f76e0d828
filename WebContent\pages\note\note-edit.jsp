<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>笔记</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="NoteDao.record" autocomplete="off" data-mars-prefix="note.">
   		  	   <input type="hidden" value="${param.noteId}" name="note.NOTE_ID"/>
   		  	   <input type="hidden" id="creator" name="note.CREATOR"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px" class="required">标题</td>
		                    <td><input data-rules="required"  maxlength="100" type="text" name="note.TITLE" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td style="width: 80px">标签</td>
		                    <td><input maxlength="100" type="text" name="note.TAGS" class="form-control input-sm"></td>
			            </tr>
			            <tr>
		                    <td class="required">
		                 		   正文
		                    </td>
		                    <td>
	                           <textarea class="form-control input-sm" style="height:calc(100vh - 280px);" name="note.CONTENT"></textarea>
		                    </td>
			            </tr>
			            <tr id="shareSet">
		                    <td class="required">共享设置</td>
		                    <td>
			                     <label class="radio radio-success radio-inline">
			   						<input type="radio" checked="checked" name="note.NOTE_AUTH" value="1"> <span>私有</span>
				  				 </label>
								 <label class="radio radio-success radio-inline">
				   					<input type="radio" name="note.NOTE_AUTH" value="2"> <span>共享</span>
				  				 </label>
		                    </td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c" style="z-index: 99999999">
				 		  <c:if test="${!empty param.noteId }">
					    	  <button type="button" class="btn btn-warning btn-sm mr-15" id="delBtn"  onclick="Edit.del()"> 删除 </button>
			 			  </c:if>
				    	  <button type="button" id="saveBtn" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
	
		Edit.noteId='${param.noteId}';
		
		$(function(){
			$("#editForm").render({success:function(result){
				 var creator=$("#creator").val();
				 if(creator){
					 var currentUserId=getCurrentUserId();
					 if(creator!=currentUserId){
						 $("#saveBtn").remove();
						 $("#shareSet").remove();
						 $("#delBtn").remove();
					 }
				 }
			}});
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.noteId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/note?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadNote();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/note?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						reloadNote();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.del=function(){
			layer.confirm("确认要删除吗?",{icon:3},function(){
				ajax.remoteCall("${ctxPath}/servlet/note?action=delete",{'note.NOTE_ID':Edit.noteId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							list.query();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>