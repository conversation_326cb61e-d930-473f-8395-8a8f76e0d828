<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的已办</title>
	<link href="${ctxPath}/static/css/sidebar.css" rel="stylesheet">
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<div class="page-box">
			<input type="hidden" name="treeFlowCode" value="${param.flowCode}"/>
	  		 <div class="left-sidebar" style="display: none;">
                <div class="left-sidebar-item" data-mars="FlowDao.myFlowDoneTree" data-template="template-list"></div>
       		 </div>
        	<div onclick="leftShow(this)" class="leftShow" style="position: absolute;display: inline-flex;align-items: center;height: 50px;background-color: #00abfc;top:40%;cursor: pointer;color: #fff;"><i class="fa fa-chevron-right" aria-hidden="true"></i></div>
  			<div class="right-content" style="width: 100%;margin-left: 0px;">
  			  <div class="ibox" >
				<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="flowMgrForm">
				   <div class="ibox-title clearfix">
          		 		<div class="pull-left layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: -4px 2px 5px;">
          		 			 <ul class="layui-tab-title"><li class="layui-this" data-val="0">全部</li><li data-val="1">我经办的</li></ul>
          		 	 	</div>
          		 	 	<button type="button" id="conditionFiter" class="btn btn-sm btn-default pull-right mt-5 layui-hide-xs"><span class="glyphicon glyphicon-filter"></span> 高级查询</button>
          		 	 	<div class="form-group layui-hide-xs" style="display: inline-block;">
						 <div class="input-group input-group-sm" style="width: 180px;">
					 		<span class="input-group-addon">流程标题</span>
						 	<input class="form-control input-sm input-pading" type="text" name="applyTitle">
						 </div>
	          			 <div class="input-group input-group-sm" style="width: 180px;">
					 		<span class="input-group-addon">申请编号</span>
						 	<input class="form-control input-sm input-pading" type="text" name="applyNo">
					 		<input type="hidden" name="flowCode" id="flowCode" value="${flowCode}"/>
					 		<input type="hidden" name="dataType" id="dataType" value="0"/>
						 </div>
						  <div class="input-group input-group-sm">
	          		  		 <span class="input-group-addon">办理日期</span>
							 <input type="text" name="checkBeginDate" data-mars="CommonDao.appointedDay(-60)" data-laydate="{type:'date'}" class="form-control input-sm" data-mars-reload="false" style="width: 90px;display: inline-block">
							 <input type="text" name="checkEndDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;display: inline-block">
	          		 	 </div>
						 <div id="morCondition" style="display: inline-block"></div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="flowList.queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="flowList.reset()"><span class="fa fa-eraser"></span> 清空</button>
						 </div>
	          	      </div>
	                </div> 
					<div class="ibox-content">
						 <table id="_flowTable" style="margin-top: 3px;"></table>
					</div>
				  </form>
				</div>
				<div class="flowQuery"></div>
  		  </div>
 	   </div>
 	   
 	   <script id="queryCondition" type="text/x-jsrender">
		<form id="condtionForm">
			<table class="table table-edit table-vzebra">
			 <tr>
				<td>发起人</td>
				<td>
					<input class="form-control input-sm" type="text" name="applyName">
				</td>
				<td>显示数据</td>
				<td>
					<select class="form-control input-sm" name="approveAll">
						<option value="0">当前节点</option>
						<option value="1">全部节点</option>
					</select>
				</td>
			</tr>
			 <tr>
				<td>待办人</td>
				<td>
					<input class="form-control input-sm" type="text" name="checkName">
				</td>
				<td>流程状态</td>
				<td>
					<select name="applyState" onchange="flowList.queryData();" class="form-control input-sm">
					  <option value="">请选择 </option>
					  <option data-class="label label-default" value="0">草稿</option>
					  <option data-class="label label-warning" value="1">作废</option>
					  <option data-class="label label-warning" value="5">已挂起</option>
					  <option data-class="label label-warning" value="10">待审批</option>
					  <option data-class="label label-info" value="20">审批中</option>
					  <option data-class="label label-danger" value="21">审批退回</option>
					  <option data-class="label label-success" value="30">已完成</option>
				   </select>	
				</td>
			</tr>
			 <tr>
				 <td style="width:80px!important;">发起日期</td>	
				 <td style="width: 35%;">
				   <input type="text" name="applyBeginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 108px;display: inline-block">
				   <input type="text" name="applyEndDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 108px;display: inline-block">
				 </td>
				 <td>紧急程度</td>
				<td>
					<select class="form-control input-sm" name="applyLevel">
						<option value="">--</option>
						<option value="正常">正常</option>
						<option value="重要">重要</option>
						<option value="紧急">紧急</option>
					</select>
				</td>
			 </tr>
			<tr>
				<td  colspan="4">
					<button type="button" class="btn btn-sm btn-default" onclick="flowList.conditionReset()"><span class="fa fa-eraser"></span> 清空</button>
					<button type="button" class="btn btn-sm btn-info ml-10" onclick="flowList.queryData()"><span class="glyphicon glyphicon-search"></span> 查询</button>
				</td>
			</tr>
		</table>
	  </form>
    </script>
       <script id="condition-list" type="text/x-jsrender">
    	   {{for list}}
				<div class="input-group input-group-sm">
					<span class="input-group-addon">{{:title}}</span>
					{{if query=='daterange'}}
					  <input class="form-control input-sm" type="text" style="width: 100px;display: inline-block" data-laydate="{type:'date'}" name="condition.{{:field}}_{{:query}}_begin">
					  <input class="form-control input-sm" type="text" style="width: 100px;display: inline-block" data-laydate="{type:'date'}" name="condition.{{:field}}_{{:query}}_end">
					{{else query=='range'}}
					  <input class="form-control input-sm" type="text" style="width: 100px;display: inline-block" name="condition.{{:field}}_{{:query}}_begin">
					  <input class="form-control input-sm" type="text" style="width: 100px;display: inline-block" name="condition.{{:field}}_{{:query}}_end">
					{{else}}
					  <input class="form-control input-sm" type="text" style="width: 100px;" {{if query=='date'}}data-laydate="{type:'date'}"{{/if}} name="condition.{{:field}}_{{:query}}">
					{{/if}}
				</div>
			{{/for}}
	    </script>
	   <script type="text/x-jsrender" id="bar">
 		  <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="flowDetail" href="javascript:;">详情</a>
	   </script>
	   <script id="template-list" type="text/x-jsrender">
			{{for flowCategory}}
 				<div class="sidebar-item">
					<a href="javascript:void(0);" class="sidebar-nav-sub-title active"><i class="sidebar-icon glyphicon glyphicon-menu-right"></i>{{:P_FLOW_NAME}}</a>
				    <ul class="sidebar-nav-sub">
						{{for #parent.parent.data.data}}
							{{if #parent.parent.data.P_FLOW_CODE==P_FLOW_CODE}}
						  		<li>
								  <a href="javascript:void(0)" data-url="{{:QUERY_URL}}" data-code="{{:FLOW_CODE}}">{{:FLOW_NAME}}({{:COUNT}})</a>
						  	  </li>
							{{/if}}
  						 {{/for}}
				    </ul>
				</div>
			{{/for}}
			{{if flowCategory.length==0}}
				<p class="text-c mt-30">无数据</p>
			{{/if}}
 		</script>
 		<script type="text/html" id="btnBar">
			 <button class="btn btn-sm btn-default" type="button" lay-event="refreshData"><i class="fa fa-refresh"></i> 刷新</button>
			 <button class="btn btn-sm btn-default ml-10" type="button" lay-event="recover"><i class="fa fa-bitbucket" aria-hidden="true"></i> 收回</button>
			 <button class="btn btn-sm btn-default ml-10" type="button" lay-event="urge"><i class="fa fa-comment-o"></i> 催办</button>
			 <button class="btn btn-sm btn-default ml-10" type="button" lay-event="cannel"><i class="fa fa-recycle"></i> 作废</button>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/yq-work/static/js/flow-list.js?v=20220507"></script>
	<script type="text/javascript">
	
	 		var flowList = {flowCode:'${param.flowCode}'};
	 
	 		var condtionData = {};
	 		
			$(function(){
				$(".page-box").render({success:function(){
					flowList.initList();
					flowList.renderTree();
					
					var device = layui.device();
				    if(!device.mobile){
				    	$('.leftShow').click();
				    }
				}});
			});
			
			flowList.renderTree = function(){
				$('body').on('click','.sidebar-nav-sub-title',function(e){
	    			$(this).toggleClass('active')
	    		})
				$(".left-sidebar-item [data-code]").click(function(){
	    			var t=$(this);
	    			var flowCode = t.data("code");
	    			var queryUrl = t.data("url");
	    			if(queryUrl){
	    				$.get(queryUrl,{flowCode:flowCode,isDiv:'1'},function(html){
		  					$(".ibox").hide();
		  					$(".flowQuery").html(html);
		  					$(".flowQuery .container-fluid").removeClass('container-fluid');
	    				});
	    				return;
	    			}
	    			if(flowCode){
	    				flowList.flowCode = flowCode;
	    				$(".flowQuery").empty();
	    				$(".ibox").show();
	    				$("[name='flowCode']").val(flowCode);
		    			$(".left-sidebar-item a").removeClass("activeItem");
		    			t.addClass("activeItem");
		    			flowList.initList();
	    			}
	    		});
				if(flowList.flowCode){
					var el = $(".left-sidebar-item [data-code='"+flowList.flowCode+"']");
					if(el.length>0){
						el.click();
					}else{
						layer.alert('该流程无数据，缺省查所有。');						
					}
				}
			}
			
			function reloadTree(checkBeginDate){
				console.log('reloadTree',checkBeginDate);
				$(".page-box").render({data:{checkBeginDate:checkBeginDate},success:function(){
					flowList.renderTree();
				}});
			}
			
			function intiCondition(data){
				  var conditionList = FlowMgr.getCondition(data);
				  var queryCondition = $.templates("#queryCondition");
				  var ins = layui.dropdown.render({
					    elem: '#conditionFiter'
					    ,content: queryCondition.render({list:conditionList})
					    ,style: 'width: 700px;min-height: 250px; padding: 15px 15px;border-radius: 6px;box-shadow: 2px 2px 30px rgb(0 0 0 / 12%);'
					    ,ready: function(){
					    	fillRecord(condtionData,'',',','#condtionForm');
					    }
				   });
				  $('#conditionFiter').data('layui_dropdown_index', ins.config.id); 
				  
				  var conditionTpl = $.templates("#condition-list");
				  var conditionHtml = conditionTpl.render({list:conditionList});
				  $('#morCondition').html(conditionHtml);
		    	  renderDate('#flowMgrForm');
				  
				  layui.element.on('tab(stateFilter)', function(){
					  $('.left-sidebar').render({success:function(){
						  flowList.renderTree();
					  }});
					  $('#dataType').val(this.getAttribute('data-val'));
					  flowList.queryData();
				  });
			  }
			
			 flowList.initList = function(){
				var param = {
						mars:'FlowDao.myFlowDone',
						id:'_flowTable',
						page:true,
						limit:50,
						sort:'flowList.sort',
						rowEvent:'rowEvent',
						rowDoubleEvent:'flowDetail',
						toolbar:'#btnBar',
						height:'full-110',
						cellMinWidth:100,
						cols: [[
							{
		            	 	type: 'radio',
							title: '序号',
							align:'left'
						 },{
							field:'',
							title:'操作',
							width:70,
							align:'center',
							event:'flowDetail',
							templet:function(row){
								return renderTpl('bar',row);
							}
						},{
							 field:'APPLY_NO',
							 title:'编号',
							 width:220,
							 templet:function(row){
								 return row['APPLY_NO']+"_"+row['FLOW_NAME'];
							 }
						 },{
							 field:'APPLY_TITLE',
							 title:'流程名称',
							 minWidth:200
						 },{
							 field:'APPLY_STATE',
							 title:'当前状态',
							 width:80,
							 templet:function(row){
								var val = row['APPLY_STATE'];
								return flowApplyState(val);
							}
						 },{
							 field:'DEPT_NAME',
							 title:'发起部门',
							 width:80
						 },{
							 field:'APPLY_NAME',
							 title:'发起人',
							 width:70
						 },{
						    field: 'APPLY_TIME',
							title: '发起时间',
							align:'center',
						    width:140
						},{
							 field:'NODE_NAME',
							 title:'已办节点',
							 width:80
						 },{
							 field:'CHECK_NAME',
							 title:'办理人',
							 width:80
						 },{
							 field:'CHECK_TIME',
							 title:'办理时间',
							 width:120
						 },{
							 field:'CURRENT_NODE',
							 title:'当前节点',
							 width:120
						 },{
							 field:'CHECK_RESULT',
							 title:'流程状态',
							 width:80,
							 hide:true,
							 templet:function(row){
								 return row['CHECK_RESULT']=='1'?'通过':'退回';
							 }
						 }
					]],done:function(){
						
				  	}
			  };
				
			FlowMgr.joinCols(param,5,function(data){
				$("#flowMgrForm").initTable(param);
				intiCondition(data);
			}); 
		 }
			 
	    function refreshData(){
		   location.reload();
	    }
				
		function flowDetail(data){
			var flowCode = data['FLOW_CODE'];
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,resultId:data['CURRENT_RESULT_ID']});
			var url = '${ctxPath}/web/flow';
			if(self==top){
				location.href = url+"?"+jQuery.param(json);				
			}else{
				popup.openTab({id:'flowDetail',title:'流程详情',url:url,data:json});
			}
		}
		
		flowList.sort = function(params){
			var data = form.getJSONObject("#condtionForm");
			if($.isEmptyObject(data)){
				data = condtionData;
			}else{
				condtionData = data;
			}
			var d = $.extend({},data,params.data);
			params.data = d;
			$("#flowMgrForm").queryData(params);
		}
		
		var prevData = '';
		flowList.queryData = function(params){
			if (params === undefined) params = {};
			var data = form.getJSONObject("#condtionForm");
			if($.isEmptyObject(data)){
				data = condtionData;
			}else{
				condtionData = data;
			}
			var d = $.extend({},data,params);
			var checkBeginDate = $("[name='checkBeginDate']").val();
			if(checkBeginDate==''){
				layer.msg('请选择办理开始时间',{icon:7,offset:'20px',time:1200});
				return;
			}
			$("#flowMgrForm").queryData({id:'_flowTable',data:d});
			if(checkBeginDate != prevData){
				reloadTree(checkBeginDate);
			}
			prevData = checkBeginDate;
		}
		
		flowList.conditionReset = function(){
			$("#condtionForm")[0].reset();
			flowList.queryData();
		}
		
		flowList.reset = function(){
			condtionData = {};
			$("input[type='text'],select").val("");
			flowList.queryData();
		}
			
	   function leftShow(el){
		   var flag = $(el).data('flag');
		   if(flag=='1'){
			   $(el).prev().hide(0);
			   $(el).next().css('width','100%').css('margin-left','0px');
			   $(el).data('flag','0');
			   $(el).html('<i class="fa fa-chevron-right"></i>');
		   }else{
			   $(el).prev().show(300);
			   $(el).next().css('width','calc(100% - 160px)');
			   $(el).data('flag','1');
			   $(el).html('<i class="fa fa-chevron-left"></i>');
		   }
	   }
	   
	   
		function recover(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		   
		    var applyState = data['APPLY_STATE'];
			if(applyState=='10'||applyState=='20'){
			   if(data['CURRENT_RESULT_ID']!= data['RESULT_ID']){
				    layer.msg('下节点已经办理,不可收回',{icon:2});
					return;				   
			   }
			    var title = data['APPLY_TITLE'];
			    var businessId = data['APPLY_ID'];
			    var resultId = data['RESULT_ID'];
			    var nextResultId = data['NEXT_RESULT_ID'];
			    layer.confirm('下一步骤尚未接收办理，确认要收回至本步骤重新办理么？',{icon:3,offset:'20px'},function(){
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=recover",{resultId:resultId,businessId:businessId,nextResultId:nextResultId},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,function(){
								refreshData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
			    });
			}else{
				layer.msg('只有审批中的流程才能收回。');
				return;
			}
			
		}
		
		 function urge(dataList){
			   if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    var data = dataList[0];
			    var applyState = data['APPLY_STATE'];
				if(applyState=='10'||applyState=='20'){
				    var title = data['APPLY_TITLE'];
				    var businessId = data['APPLY_ID'];
				    var resultId = data['NEXT_RESULT_ID'];
				    var staffInfo = getStaffInfo();
				    var content = '流程：'+title+'，已达你处，请尽快办理，谢谢！--'+staffInfo.userName;
				    layer.prompt({formType: 2,value: content,offset:'20px',title: '请输入催办信息',area: ['400px', '150px']}, function(msgContent, index, elem){
			    	 	layer.close(index);
			    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addUrge",{resultId:resultId,businessId:businessId,msgContent:msgContent},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg)
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});	    	  	
			    	});
				}else{
					layer.msg('只有审批中的流程才能催办。');
					return;
				}
			    
		   }
		 
		 function cannel(dataList){
			 if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
			  }
			  var data = dataList[0];
			  var applyState = data['APPLY_STATE'];
			  var flowCode = data['FLOW_CODE'];
			  if((flowCode.indexOf('finance')>-1&&applyState==21)||(flowCode.indexOf('finance')==-1&&applyState>=10&&applyState<=30)){
				 layer.prompt({formType: 2,value: '',offset:'20px',title: '请输入作废原因',area: ['400px', '150px']}, function(remark, index, elem){
				    layer.close(index);
					ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=cannelApply",{businessId:data['APPLY_ID'],flowCode:flowCode,remark:remark,applyState:applyState},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800},function(){
								flowList.queryData();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			 }else{
				layer.msg('该流程状态不支持作废');
				return;
			}
		 }
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>