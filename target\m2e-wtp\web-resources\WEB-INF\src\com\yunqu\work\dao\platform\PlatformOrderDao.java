package com.yunqu.work.dao.platform;

import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import com.alibaba.fastjson.JSONObject;

@WebObject(name="PlatformOrderDao")
public class PlatformOrderDao extends AppDaoContext {

    @WebControl(name="getOrder",type=Types.RECORD)
    public JSONObject getOrder() {
        String orderId = param.getString("orderId");
        if(orderId == null) {
            JSONObject resultJson = new JSONObject();
            resultJson.put("data", new JSONObject());
            return resultJson;
        }
        EasySQL sql = this.getEasySQL("SELECT t.* FROM yq_crm_platform_order t WHERE 1=1");
        sql.append(orderId,"and t.ORDER_ID =  ?",false);
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name="orderPageList",type=Types.LIST)
    public JSONObject orderPageList() {
        EasySQL sql = this.getEasySQL("SELECT t.* FROM yq_crm_platform_order t WHERE 1=1");
        sql.append(param.getString("platformId"), "AND t.PLATFORM_ID = ?");
        sql.append(param.getString("custId"), "AND t.CUST_ID = ?");
        sql.append(param.getString("orderStatus"), "AND t.ORDER_STATUS = ?");
        sql.appendLike(param.getString("custName"), "AND t.CUST_NAME LIKE ?");
        sql.appendLike(param.getString("sjName"), "AND t.BUSINESS_NAME LIKE ?");
        sql.appendLike(param.getString("contractName"), "AND t.CONTRACT_NAME LIKE ?");
        sql.append("ORDER BY t.CREATE_TIME DESC");
        return this.queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name="orderRecord",type=Types.RECORD) 
    public JSONObject orderRecord() {
        String orderId = param.getString("orderId");
        return queryForRecord("select * from yq_crm_platform_order where ORDER_ID = ?", orderId);
    }
}