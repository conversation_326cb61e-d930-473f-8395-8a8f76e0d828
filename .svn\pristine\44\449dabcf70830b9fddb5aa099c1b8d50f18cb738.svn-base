package com.yunqu.work.service;

import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.utils.WeekUtils;

public class WeeklyNoticeService extends AppBaseService implements Job{

	private static class Holder{
		private static WeeklyNoticeService service=new WeeklyNoticeService();
	}
	public static WeeklyNoticeService getService(){
		return Holder.service;
	}
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		 this.run(WeekUtils.getWeekNo());
		 
	}
	public  void run(int weekNo){
		//待办提醒
		getLogger().info("执行周报提醒任务....");
		try {
			int year=WeekUtils.getYear();
			List<JSONObject> list = this.getQuery().queryForList("SELECT USER_ID,DEPTS FROM "+Constants.DS_MAIN_NAME+".easi_user WHERE USER_ID NOT IN ( SELECT t1.USER_ID FROM "+Constants.DS_MAIN_NAME+".easi_user t1, yq_weekly t2 WHERE t1.USER_ID = t2.creator AND t2.week_no = ? and t2.year = ? ) AND STATE = 0 ",new Object[]{weekNo,year},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("USER_ID");
					String depts = jsonObject.getString("DEPTS");
					if(StringUtils.notBlank(depts)) {
						if(depts.contains("开发")||depts.contains("创新")||depts.contains("分部")||depts.contains("工程")||depts.contains("交互")||depts.contains("研究")||depts.contains("UI")) {
							MessageModel model=new MessageModel();
							model.setReceiver(creator);
							model.setSender(creator);
							model.setTitle(WeekUtils.getWeekTitle(year,weekNo)+" 周报"+"\n");
							WxMsgService.getService().sendWeeklyNoticeMsg(model);
						}
					}
				}
			}
		} catch (SQLException e2) {
			this.getLogger().error(e2);
		}
	}
	
	//月报提醒
	public  void workhourNotice(String monthId,String userId){
		getLogger().info("执行项目工时上报待填写任务....");
		try {
			if(StringUtils.isBlank(monthId)) {
				monthId = EasyCalendar.newInstance().getFullMonth();
			}
			EasySQL sql = new EasySQL("select t1.user_id from yq_project_wh t1 where t1.state=0 and t1.user_id in(select t2.USER_ID from "+Constants.DS_MAIN_NAME+".easi_user t2 where t2.STATE = 0)");
			sql.append(monthId,"and t1.month_id = ?");
			if(StringUtils.isNotBlank(userId)) {
				sql.append(userId,"and t1.user_id = ?");
			}
			
			try {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");  
				Date date = sdf.parse(monthId);
				monthId = new SimpleDateFormat("yyyy年MM月").format(date);
			} catch (ParseException e) {
				this.getLogger().error(e);
			}
			
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator = jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(creator);
					model.setSender(creator);
					model.setData1(monthId);
					model.setTitle("项目工时上报");
					WxMsgService.getService().sendWhNoticeMsg(model);
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
	}
	
	public void noticeTodo(String sql){
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql,new Object[] {},new JSONMapperImpl());
			for(JSONObject object :list) {
				String creator=object.getString("CREATOR");
				MessageModel model=new MessageModel();
				model.setReceiver(creator);
				model.setSender(creator);
				model.setTitle("您有个日程安排即将开始");
				model.setData1(object.getString("TITLE"));
				model.setData3(object.getString("BEGIN_TIME")+" ~ "+object.getString("END_TIME"));
				model.setDesc(WxMsgService.getService().delHTMLTag(object.getString("TODO_DESC")));
				WxMsgService.getService().sendScheduleNoticeMsg(model);
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
	}

	
	public void createProjectWorkHour() {
		createProjectWorkHour(EasyCalendar.newInstance().getFullMonth(),null);
	}
	
	public void createProjectWorkHour(String monthId,String deptName) {
		try {
			EasySQL sql = new EasySQL("SELECT t2.USER_ID,t1.DEPT_ID,t2.USERNAME from easi_user t2,easi_dept_user t1 where  t2.state = 0 and t1.USER_ID = t2.USER_ID");
			if(StringUtils.isBlank(deptName)) {
				sql.append("and (t2.depts like '开发%'  or t2.depts like '创新%'  or t2.depts like '%工程%'  or t2.depts like '%分部' or t2.depts like '%智能交互部')");
			}else {
				sql.appendLike(deptName,"and t2.depts like ?");
			}
			List<JSONObject> list = this.getMainQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				for(JSONObject obj:list) {
					String userId = obj.getString("USER_ID");
					String username = obj.getString("USERNAME");
					String deptId = obj.getString("DEPT_ID");
					
					EasyRecord record = new EasyRecord("yq_project_wh","id");
					record.set("id",monthId+"_"+userId);
					record.set("month_id",monthId );
					record.set("user_id", userId);
					record.set("user_name", username);
					record.set("dept_id", deptId);
					record.set("state", 0);
					boolean bl = this.getQuery().update(record);
					if(!bl) {
						record.set("create_time", EasyDate.getCurrentDateString());
						this.getQuery().save(record);
					}
				}
			}
		} catch (SQLException e) {
			getLogger().error(e.getMessage(),e);
		}
	}
}
