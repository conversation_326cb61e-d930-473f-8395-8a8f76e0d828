<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>里程碑</title>
	<style>
		.base-info tr td:nth-child(odd){
			background-color: #f8f8f8;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input type="hidden" name="project.PROJECT_ID" value="${param.projectId}"/>
			<input type="hidden" name="projectId" value="${param.projectId}"/>
			<input type="hidden" name="projectName" id="projectName" value="${param.projectName}"/>
			<div class="ibox">
				<div class="ibox-content">
					<table class="layui-table base-info" data-mars="ProjectDao.record" data-mars-prefix="project.">
						<tr>
							<td style="width: 100px;">开工日期</td>
							<td><span name="project.BEGIN_DATE"></span></td>
							<td style="width: 100px;">上线日期</td>
							<td><span name="project.SX_DATE"></span></td>
							<td>初验日期</td>
							<td><span name="project.CY_DATE"></span></td>
							<td>终验日期</td>
							<td><span name="project.ZY_DATE"></span></td>
						</tr>
						<tr>
						  <td>项目经理</td>
						  <td>
						  	<span name="project.PROJECT_PO_NAME"></span>
						  </td>
						  <td>开发负责人</td>
						  <td>
						  	<span name="project.PO_NAME"></span>
						  </td>
						  <td>项目计划</td>
						  <td>
						  	<button class="btn btn-xs btn-link" type="button" onclick="lookFlow('plan');">查看流程</button>
						  </td>
						  <td>项目任命</td>
						  <td>
						  	<button class="btn btn-xs btn-link" type="button" onclick="lookFlow('pm');">查看流程</button>
						  </td>
						</tr>
					</table>
				    <table id="tree" lay-filter="tree"></table>
				</div>
			</div>
		</form>
		<script type="text/x-jsrender" id="btnBar">
			 <button onclick="list.editMode();" type="button" class="btn btn-info btn-sm change-mode mr-10">切换模式</button>
			 <button onclick="list.saveData();" type="button" class="btn btn-info btn-sm save-btn mr-10">保存</button>

			 <button lay-event="list.delItemData" type="button" class="btn btn-warning btn-sm del-btn ml-20">删除项</button>
			 <button lay-event="list.addItemData" type="button" class="btn btn-default btn-sm add-btn ml-5">新增项</button>
			 <button lay-event="list.addPlanFlow" type="button" class="btn btn-success btn-sm add-btn ml-30">发布项目计划</button>
		</script>
		<script type="text/x-jsrender" id="planNameTpl">
			{{if hasAuth}}
			  <input type="text" name="{{:ID}}_pname" style="width:150px" value="{{:PLAN_NAME}}" class="form-control input-sm">
			{{else}}
			  {{:PLAN_NAME}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="poUserTpl">
			{{if hasAuth}}
			  <input type="hidden" name="{{:ID}}_doUserId" value="{{:PLAN_PERSON_ID}}">
			  <input type="text" name="{{:ID}}_doUserName" style="width:80px" placeholder="若为空是本人" onclick="singleUser(this);" value="{{:PLAN_PERSON_NAME}}" class="form-control input-sm">
			{{else}}
			  {{:PLAN_PERSON_NAME}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="beginDateTpl">
			{{if hasAuth}}
			  <input type="text" data-laydate="{type:'date'}" style="width:120px" name="{{:ID}}_planBeginDate" value="{{:PLAN_BEGIN_DATE}}" class="form-control input-sm">
			{{else}}
			  {{:PLAN_BEGIN_DATE}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="endDateTpl">
			{{if hasAuth}}
			  <input type="text" data-laydate="{type:'date'}" style="width:120px" name="{{:ID}}_planEndDate" value="{{:PLAN_FINISH_DATE}}" class="form-control input-sm">
			{{else}}
			  {{:PLAN_FINISH_DATE}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="rBeginDateTpl">
			{{if hasAuth}}
			  <input type="text" data-laydate="{type:'date'}" style="width:120px" name="{{:ID}}_beginDate" value="{{:BEGIN_DATE}}" class="form-control input-sm">
			{{else}}
			  {{:PLAN_BEGIN_DATE}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="rEndDateTpl">
			{{if hasAuth}}
			  <input type="text" data-laydate="{type:'date'}" style="width:120px" name="{{:ID}}_endDate" value="{{:FINISH_DATE}}" class="form-control input-sm">
			{{else}}
			  {{:PLAN_FINISH_DATE}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="taskTpl">
			{{if hasAuth&&TASK_ID==''}}
				<button class="btn btn-xs btn-default" onclick="addTask('{{:PLAN_ID}}','{{:PLAN_NAME}}','{{:PROJECT_ID}}')" type="button">+发起任务</button>
			{{else TASK_ID}}
			  <a href="javascript:;" onclick="taskDetail('{{:TASK_ID}}')">查看任务</a>
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="processTpl">
			{{if hasAuth}}
			  <select type="number" style="width:90px" name="{{:ID}}_process" data-value="{{:PLAN_PROCESS}}" class="form-control input-sm">
					<option value="0">0%</option>
					<option value="10">10%</option>
					<option value="20">20%</option>
					<option value="30">30%</option>
					<option value="40">40%</option>
					<option value="50">50%</option>
					<option value="60">60%</option>
					<option value="70">70%</option>
					<option value="80">80%</option>
					<option value="90">90%</option>
					<option value="100">100%</option>
			  </select>
			{{else}}
			  {{:PLAN_PROCESS}}%
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="remarkTpl">
			 <input type="hidden" name="{{:ID}}_order" value="{{:SORT}}">
			 <input type="hidden" name="{{:ID}}_planName" value="{{:MENU_NAME}}">
			 <input type="hidden" name="{{:ID}}_parentPlanName" value="{{:PARENT_NAME}}">
			 <input type="hidden" name="planIds" value="{{:PLAN_ID}}">
			 <input type="hidden" name="{{:ID}}_planId" value="{{:PLAN_ID}}">
			 <input type="hidden" name="{{:ID}}_pPlanId" value="{{:PARENT_ID}}">
			 <input type="hidden" name="ids" value="{{:ID}}">
			{{if hasAuth}}
			   <input type="text" name="{{:ID}}_remark" value="{{:PLAN_REMARK}}" class="form-control input-sm" style="width:99%;">
			{{else}}
			   {{:PLAN_REMARK}}
			{{/if}}
		</script>
		<script type="text/x-jsrender" id="stateTpl">
			{{if hasAuth}}
			  {{if PLAN_STATE}}
			   <select class="form-control input-sm" name="{{:ID}}_state" data-value="{{:PLAN_STATE}}">
					<option value="1">待开始</option>
					<option value="5">进行中</option>
					<option value="9">已完成</option>
				</select>
				{{else}}
				   <input type="hidden" name="{{:ID}}_state" value="1"> 待填写
				{{/if}}
			{{else}}
			    {{call:PLAN_STATE fn='stateDesc'}}
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
	    var projectId = '${param.projectId}';
	   
	    var hasAuth = false;
	    var editModeFlag = false;
		
	    $(function(){
			if(top.isAdmin=='1'||isSuperUser){
				hasAuth = true;
				editModeFlag = true;
			}
			$("#searchForm").render({success:function(){
				loadData();
			}});
		});
	    
	    function loadData(){
	    	list.init();
			$("select[data-value]").each(function(){
				$(this).val($(this).attr('data-value'));
			});
	    }
		
	    
		var list = {
			init:function(){
				var data = list.getData();
				var treeTable = layui.treeTable;
				 var inst = treeTable.render({
					 elem: '#tree',
					 id:'tree',
					 toolbar:'#btnBar',
					 data:data,
					 height:'full-100',
					 event:'rowEvent',
					 tree:{view:{expandAllDefault:true,showIcon:false},customName:{pid:'PARENT_ID',id:'ID',name:'MENU_NAME'}},
					 cols:[[
						 {type:'radio',hide:false},
						 {field:'MENU_NAME',width:220,align:'left',title:'名称',templet:function(row){
							 row.hasAuth = hasAuth;
							 if(row.PLAN_NAME==''){
								 row['PLAN_NAME'] = row.MENU_NAME;
							 }
							 return renderTpl('planNameTpl',row);
						   }
						 },
						 {field:'PLAN_PERSON_NAME',width:100,align:'center',title:'负责人',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('poUserTpl',row);
						   }
						 },
						 {field:'TASK_ID',width:100,align:'center',title:'任务',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('taskTpl',row);
						   }
						 },
						 {field:'PLAN_BEGIN_DATE',width:150,align:'center',title:'计划开始时间',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('beginDateTpl',row);
						   }
						 },
						 {field:'PLAN_FINISH_DATE',width:150,align:'center',title:'计划结束时间',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('endDateTpl',row);
						   }
						 },
						 {field:'BEGIN_DATE',width:150,align:'center',title:'实际开始时间',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('rBeginDateTpl',row);
						   }
						 },
						 {field:'FINISH_DATE',width:150,align:'center',title:'实际结束时间',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('rEndDateTpl',row);
						   }
						 },
						 {field:'PLAN_PROCESS',width:110,align:'center',title:'进度',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('processTpl',row);
						   }
						 },
						 {field:'PLAN_STATE',title:'状态',align:'center',width:100,templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('stateTpl',row);
						   }
						 },
						 {field:'PLAN_REMARK',title:'备注',templet:function(row){
							 row.hasAuth = hasAuth;
							 return renderTpl('remarkTpl',row);
						   }
						 }
					]],
					done:function(){
						renderDate('#searchForm');
						$("[data-value]").each(function(){
							var val = $(this).data('value');
							if(val){
								$(this).val(val);
							}
						});
						
						if(!editModeFlag){
							$('.change-mode').hide();
						}
						if(hasAuth){
							$('.save-btn,.add-btn,.del-btn').show();
						}else{
							$('.save-btn,.add-btn,.del-btn').hide();
						}
					}
				 });
				 
				 treeTable.on('row(tree)', function(obj) {
					 rowEvent(obj.data,obj);
				 });
				 
				 treeTable.on('toolbar(tree)', function(obj){
					  var checkStatus = treeTable.checkStatus('tree'); 
					  var data = checkStatus.data;
		    		  var event = obj.event;
		    		  if(event){
		    			  excuteFn(event,[data,obj]);
		    		  }
				 });
			},
			getData:function(){
				var _data = form.getJSONObject("#searchForm");
				var data = [];
				ajax.remoteCall("${ctxPath}/webcall?action=ProjectPlanDao.projectPlanList",_data,function(rs) { 
					data = rs.data;
				},{async:false});
				var _data = dataToTree(data,{idFiled: 'ID', textFiled: 'MENU_NAME', parentField: 'PARENT_ID', childField: '',def:{spread:true}, map: {ID: 'id', MENU_NAME: 'title' } });
				return _data;
			},
			query:function(){
				$("#searchForm").queryData();
			},
			saveData:function(data){
				var data = form.getJSONObject("#searchForm");
				ajax.remoteCall("${ctxPath}/servlet/project/conf?action=editPlan",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							location.reload();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			},
			editMode:function(){
				if(hasAuth){
					hasAuth = false;
					$('.save-btn,.add-btn,.del-btn').hide();
				}else{
					hasAuth = true;
					$('.save-btn').show();
				}
				list.init();
			},
			delItemData:function(list){
				if(list.length==0){
					layer.msg('请先选择',{icon:7,offset:'20px',time:1200});
					return;
				}
				var data = list[0];
				layer.confirm("确认要删除["+data.PLAN_NAME+"]吗?",{offset:'20px',icon:3},function(){
					ajax.remoteCall("${ctxPath}/servlet/project/conf?action=delPlanItem",{'planId':data.PLAN_ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								loadData();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			addItemData:function(list){
				if(list.length==0){
					layer.msg('请先选择',{icon:7,offset:'20px',time:1200});
					return;
				}
				var data = list[0];
				var pPlanId = data['P_PLAN_ID'];
				if(pPlanId!='0'){
					layer.msg('请选择一级类别',{icon:7,offset:'20px',time:1200});
					return;
				}
				layer.prompt({title:"["+data.PLAN_NAME+"]新增子类别",offset:'20px',icon:3},function(value, index, elem){
					if(value === '') return elem.focus();
					ajax.remoteCall("${ctxPath}/servlet/project/conf?action=addPlanItem",{'pPlanId':data.PLAN_ID,planName:value,pPlanName:data.PLAN_NAME,projectId:data.PROJECT_ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								loadData();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				});
			},
			addPlanFlow:function(list){
				var contractId = projectId
				flowApply('pj_plan',{contractId:contractId});
			}
		}
		
		function lookFlow(flow){
			ajax.remoteCall("${ctxPath}/webcall?action=ProjectDataDao.projectFlowList",{'projectId':'${param.projectId}'},function(result) {
				var data = result.data;
				if(flow=='pm'){
					var json = {businessId:data['pmApplyId']};
					if(json.businessId){
						popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:json});
					}else{
						layer.msg('流程不存在',{icon:7,offset:'20px',time:1200});
					}
				}else{
					var json = {businessId:data['planApplyId']};
					if(json.businessId){
						popup.openTab({id:'flowDetail',title:'流程详情',url:'/yq-work/web/flow',data:json});
					}else{
						layer.msg('流程不存在',{icon:7,offset:'20px',time:1200});
					}
				}
			});
		}
		
		function addTask(planId,planName,projectId){
			var projectName = $('#projectName').val();
			popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务',data:{projectId:projectId,projectName:projectName,groupId:planId,groupName:planName}});
		}
		
		function taskDetail(taskId){
			popup.openTab({id:"task_"+taskId,type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',url:'${ctxPath}/pages/task/task-detail.jsp?isDiv=0',title:'任务详情',data:{taskId:taskId}});
		}
		
		function stateDesc(val){
			if(val){
				var json = {'0':'待填写','1':'<span class="label label-warning">待开始</span>','5':'<span class="label label-info">进行中</span>','9':'<span class="label label-success">已完成</span>'};
				return json[val]||'';
			}
			return '--';
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>