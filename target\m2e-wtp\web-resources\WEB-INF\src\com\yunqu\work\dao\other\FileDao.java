package com.yunqu.work.dao.other;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="FileDao")
public class FileDao extends AppDaoContext {
	
	@WebControl(name="myFileLists",type=Types.LIST)
	public JSONObject myFileLists(){
		EasySQL sql=new EasySQL("select * from yq_files where 1=1");
		if(!isSuperUser()){
			sql.append(getUserId(),"and creator = ?");
		}
		sql.append("and del_flag = 0");
		sql.appendLike(param.getString("fileName"),"and FILE_NAME like ?");
		sql.append(param.getString("source"),"and source = ?");
		
		String beginDate = param.getString("beginDate");
		if(StringUtils.notBlank(beginDate)) {
			sql.append(beginDate+" 00:00:00","and create_time >= ?");
		}
		String endDate = param.getString("endDate");
		if(StringUtils.notBlank(endDate)) {
			sql.append(endDate+" 23:59:59","and create_time <= ?");
		}
		
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	@WebControl(name="fileList",type=Types.TEMPLATE)
	public JSONObject fileList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_files where fk_id = ? and del_flag = 0 order by create_time desc";
			return queryForList(sql, new Object[]{fkId});
		}
		return getJsonResult(new JSONArray());
	}
	
	@WebControl(name="fileListDetail",type=Types.TEMPLATE)
	public JSONObject fileListDetail(){
		String fkId=param.getString("fkId");
		String folderId=param.getString("folderId");
		String pFkId = param.getString("pFkId");
		EasySQL sql = new EasySQL("select  t1.* from yq_files t1 where 1=1");
		sql.append(0,"and t1.del_flag = ? ");
		sql.append(fkId,"and t1.fk_id = ? ");
		sql.append(folderId,"and t1.folder_id = ? ");
		String source = param.getString("source");
		if(StringUtils.notBlank(source)) {
			sql.appendIn(source.split(","),"and t1.source");
		}
		sql.append(pFkId,"and t1.p_fk_id = ? ");
		sql.append(" order by t1.file_label,t1.create_time desc");
		if(StringUtils.isBlank(fkId)&&StringUtils.isBlank(pFkId)) {
			return getJsonResult(new JSONArray());
		}
		return queryForList(sql.getSQL(), sql.getParams());
	}

}
