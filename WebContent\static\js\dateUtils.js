var DateUtils = {
	/**
	 * 将本身的日期对象按格式化的方式输出成字符串
	 * @param formatter 格式化类型： 默认yyyy-MM-dd
	 * @returns {String}
	 */
	dateFormat : function(date,formatter){
	    if(!formatter || formatter == "")
	    {
	        formatter = "yyyy-MM-dd";
	    }
	    var year = date.getFullYear().toString();
	    var month = (date.getMonth() + 1).toString();
	    var day = date.getDate().toString();
	    var hour=date.getHours().toString();     
	    var minute=date.getMinutes().toString();     
	    var second=date.getSeconds().toString();   
	    var returnDate = formatter;
	    
	    var yearMarker = formatter.replace(/[^y|Y]/g,'');
	    if(yearMarker.length > 1){
		    if(yearMarker.length == 2)
		    {
		        year = year.substring(2,4);
		    }    
		    returnDate = returnDate.replace(yearMarker,year);
	    }
	    var monthMarker = formatter.replace(/[^M]/g,'');
	    if(monthMarker.length > 1)
	    {
	        if(month.length == 1) 
	        {
	            month = "0" + month;
	        }
	        returnDate = returnDate.replace(monthMarker,month);
	    }    
	    var dayMarker = formatter.replace(/[^d]/g,'');
	    if(dayMarker.length > 1)
	    {
	        if(day.length == 1) 
	        {
	            day = "0" + day;
	        }
	        returnDate = returnDate.replace(dayMarker,day);
	    }
	    var hourMarker = formatter.replace(/[^h|H]/g,'');
	    if(hourMarker.length > 1)
	    {
	    	if(hour.length == 1) 
	    	{
	    		hour = "0" + hour;
	    	}
	    	returnDate = returnDate.replace(hourMarker,hour);
	    }
	    var minuteMarker = formatter.replace(/[^m]/g,'');
	    if(minuteMarker.length > 1)
	    {
	    	if(minute.length == 1) 
	    	{
	    		minute = "0" + minute;
	    	}
	    	returnDate = returnDate.replace(minuteMarker,minute);
	    }
	    var secondMarker = formatter.replace(/[^s|S]/g,'');
	    if(secondMarker.length > 1)
	    {
	    	if(second.length == 1) 
	    	{
	    		second = "0" + second;
	    	}
	    	returnDate = returnDate.replace(secondMarker,second);
	    }
	    return returnDate;    
	}
}