<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<style>
 	#followFileList a{background-color: #f8f8f8;border: 1px solid #ccc;padding: 3px 5px;margin: 3px;display: inline-block;}
</style>
<div class="container-fluid">
   <table class="table table-edit table-vzebra"  id="addFollow" data-mars="OrderDao.followInfo" data-mars-prefix="follow.">
       <tbody>
           <tr>
           		<td class="required" width="100px">跟进阶段</td>
                <td>
                	<input name="follow.ORDER_ID" value="${param.orderId}" type="hidden"/>
                	<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
                	<input type="hidden" name="fkId" value="${param.followId}"/>
                	<input type="hidden" name="follow.FOLLOW_ID" value="${param.followId}"/>
                	<input type="hidden" name="follow.FOLLOW_STAT_LABEL" id="followStatLabel"/>
                	
                	<select name="follow.FOLLOW_STATE" class="form-control">
                    	<option value="">请选择</option>
              		 	<option value="10">创建订单</option>
              		 	<option value="20">合同评审</option>
              		 	<option value="30">合同盖章</option>
              		 	<option value="40">合同签订</option>
              		 	<option value="50">到货跟进</option>
              		 	<option value="60">入出库</option>
              		 	<option value="70">付款申请</option>
              		 	<option value="80">付款完成</option>
                    </select>
                 </td>
           </tr>
            <tr>
            	<td class="required">跟进内容</td>
            	<td>
            		<textarea name="follow.FOLLOW_CONTENT" data-rules="required" style="height: 150px"  class="form-control"></textarea>
            	</td>
            </tr>
            <tr>
            	<td class="required">附件</td>
            	<td>
            		<a onclick="$('#followLocalfile').click();" href="javascript:;">+上传</a>
            		<div id="followFileList" style="display: inline-block;" data-template="follow-template-files" data-mars="FileDao.fileList"></div>
            	</td>
            </tr>
       </tbody>
	</table>
    <p class="layer-foot text-c">
   	   <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="FollowEit.ajaxSubmitForm()"> 保 存 </button>
       <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
	</p>
</div>

<form  id="followFileForm" enctype="multipart/form-data"  method="post">
  	<input style="display: none;" name="file" type="file" id="followLocalfile" onchange="uploadFollowFile();"/>
</form>

<script id="follow-template-files" type="text/x-jsrender">
	{{for data}}
		<a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}} &nbsp;</span></a>
	{{/for}}
</script>
<script>
	layui.config({
		  base: '${ctxPath}/static/js/'
	}).use('tableSelect');

	$(function(){
		$("#addFollow textarea").keydown(function(event) {
			  if(event.keyCode == "13") {
				 var height = $(this).height();
				 $(this).css('height',(height+20)+'px');
			  }
			   if(event.keyCode == 8) {
				 var height = $(this).height();
				 height=height>120?height:120;
				 $(this).css('height',(height-20)+'px');
			   }
		});
		$("#addFollow").render({success:function(result){

		}});
	});
	
	var FollowEit ={
		followId:'${param.followId}',
		ajaxSubmitForm:function(){
			if(form.validate("#addFollow")){
				$('#followStatLabel').val($("[name='follow.FOLLOW_STATE']").find("option:selected").text());
				if(FollowEit.followId){
					FollowEit.updateData(); 
				}else{
					FollowEit.insertData(); 
				}
			};
			
		},
		insertData:function(){
			var data = form.getJSONObject("#addFollow");
			var randomId = $("#randomId").val();
			data['follow.FOLLOW_ID'] = randomId;
			
			ajax.remoteCall("${ctxPath}/servlet/order?action=addFollow",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('addFollow');
						reloadFollowInfo();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		},
		updateData:function(){
			var data = form.getJSONObject("#addFollow");
			
			ajax.remoteCall("${ctxPath}/servlet/order?action=updateFollow",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('addFollow');
						reloadFollowInfo();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	
	}

	var uploadFollowFile = function(){
		var orderId = $("[name='follow.ORDER_ID']").val();
		if(orderId==''){
			layer.msg('请选择采购订单');
			return;
		}
		var randomId = $("#randomId").val();
		var fkId = FollowEit.followId||randomId;
		easyUploadFile({callback:'callback',pFkId:orderId,fkId:fkId,source:'order',formId:'followFileForm',fileId:'followLocalfile'});
		
	}
 	 
	var callback = function(data){
		var randomId = $("#randomId").val();
		var fkId = FollowEit.followId||randomId;
		$("#followFileList").render({data:{fkId:fkId}});
	}
</script>
