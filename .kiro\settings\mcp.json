{"mcpServers": {"Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequentialthinking"]}, "interactive-feedback-mcp": {"command": "uv", "args": ["--directory", "D:\\soft\\develop\\nodejs-config\\node_global\\node_modules\\interactive-feedback-mcp-main", "run", "server.py"], "disabled": false, "autoApprove": []}}}