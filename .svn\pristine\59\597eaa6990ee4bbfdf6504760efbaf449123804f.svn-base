package com.yunqu.work.servlet.ehr;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import com.alibaba.fastjson.JSONArray;
import com.yunqu.work.service.StaffService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.Globals;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.KqNoticeService;
import com.yunqu.work.service.WxMsgService;
/**
 * <AUTHOR>
 * 非工作日 https://blog.csdn.net/u011456337/article/details/86180383
 *
 */
@WebServlet("/servlet/kaoqin/*")
@MultipartConfig
public class KaoqinServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAddLeaveOnKq() {
		String dateId = getJsonPara("dateId");
		KqNoticeService.getService().addKqRecord(dateId);
		KqNoticeService.getService().addLeaveOnKq(dateId);
		return EasyResult.ok();
	}


	public EasyResult actionForInitKqObj() {
		String dateId = getPara("dateId");
		KqNoticeService.getService().addKqRecord(dateId);
		return EasyResult.ok();
	}
	
	public EasyResult actionForNotKqCount() {
		String date = getJsonPara("date");
		try {
			int count = this.getQuery().queryForInt("select count(1) from yq_flow_apply where flow_code='hr_kq_yc' and apply_by = ? and LEFT(data2,7) = ?",getUserId(),date);
			return EasyResult.ok(count);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(0);
	}
	public EasyResult actionForUpdatePeople() {
		String userId = getJsonPara("userId");
		try {
			this.getQuery().executeUpdate("update yq_kq_people set state = 1 where kq_user_id = ?",userId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdatePeople2() {
		String userId = getJsonPara("userId");
		try {
			this.getQuery().executeUpdate("update yq_kq_people set state = 0 where kq_user_id = ?",userId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelPeople() {
		String userId = getJsonPara("userId");
		try {
			this.getQuery().executeUpdate("delete from yq_kq_people where kq_user_id = ?",userId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}

	private EasyRecord getPeopleModel(String prefix){
		EasyRecord kqPeopleModel = new EasyRecord("yq_kq_people","kq_user_id");
		kqPeopleModel.setColumns(getJSONObject(prefix));
		return kqPeopleModel;
	}
	public EasyResult actionForAddPeople(){
		EasyRecord kqPeopleModel = getPeopleModel("kqPeople");
		try {
			this.getQuery().save(kqPeopleModel);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}


	public EasyResult actionForBatchDel(){
		JSONArray kqPeopleArray = getJSONArray();
		for(int i = 0;i < kqPeopleArray.size();i++){
			JSONObject kqPeopleObject = kqPeopleArray.getJSONObject(i);
			try {
				this.getQuery().executeUpdate("delete from yq_kq_people where kq_user_id = ?",kqPeopleObject.getString("KQ_USER_ID") );
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}

	public EasyResult actionForBatchRemoveKq(){
		JSONArray kqPeopleArray = getJSONArray();
		for(int i = 0;i < kqPeopleArray.size();i++){
			JSONObject kqPeopleObject = kqPeopleArray.getJSONObject(i);
			try {
				this.getQuery().executeUpdate("update yq_kq_people set state = 1 where kq_user_id = ?",kqPeopleObject.getString("KQ_USER_ID") );
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}

	public EasyResult actionForBatchAddKq(){
		JSONArray kqPeopleArray = getJSONArray();
		for(int i = 0;i<kqPeopleArray.size();i++){
			JSONObject kqPeopleObject = kqPeopleArray.getJSONObject(i);
			try {
				this.getQuery().executeUpdate("update yq_kq_people set state = 0 where kq_user_id = ?",kqPeopleObject.getString("KQ_USER_ID") );
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}

	public EasyResult actionForGetTodayWxDk() {
		try {
		  JSONObject row = this.getQuery().queryForRow("select * from YQ_KQ_OBJ where USER_ID = ? and DK_DATE = ?", new Object[] {getUserId(), EasyCalendar.newInstance().getDateInt()}, new JSONMapperImpl());
		  return EasyResult.ok(row);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.fail();
	}
	
	public EasyResult actionForManualSign() {
		JSONObject params = getJSONObject();
		try {
			int signTimeType = params.getIntValue("signTimeType");
			EasyRecord record = new EasyRecord("YQ_KQ_SIGN","DK_DATE","USER_ID");
			record.set("IP", WebKit.getIP(getRequest()));
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("USER_ID", getUserId());
			record.set("DK_DATE", EasyCalendar.newInstance().getDateInt());
			
			boolean existData  = this.getQuery().update(record);
			if(!existData) {
				record.set("KQ_ID",RandomKit.uuid());
				record.set("REMARK", "网页打卡");
				record.set("DEPT_ID", getDeptId());
				record.set("USER_NAME", getRemoteUser());
				record.set("STAFF_ID", getStaffInfo().getStaffNo());
				record.set("DEPT", getStaffInfo().getDeptName());
				this.getQuery().save(record);
			}
			JSONObject cityInfo = params.getJSONObject("city");
			String address  = cityInfo.getString("province")+cityInfo.getString("city")+cityInfo.getString("district")+params.getString("address")+"("+params.getString("name")+")";
			
			if(signTimeType==0) {
				boolean bl = this.getQuery().queryForExist("select count(1) from YQ_KQ_SIGN where DK_DATE = ? and USER_ID = ? and SIGN_IN <>''",EasyCalendar.newInstance().getDateInt(),getUserId());
				if(bl) {
					return EasyResult.fail("今日已签到过，请勿重复");
				}
				record.set("ADDRESS_IN", address);
				record.set("SIGN_IN", EasyDate.getCurrentDateString("HH:mm"));
				this.getQuery().update(record);
				//微信推送
			}else {
				record.set("ADDRESS_OUT", address);
				record.set("SIGN_OUT", EasyDate.getCurrentDateString("HH:mm"));
				this.getQuery().update(record);
				//微信推送
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForWxDk() {
		JSONObject params = getJSONObject();
		String dkTime = EasyDate.getCurrentDateString();
		try {
			int signTimeType = params.getIntValue("signTimeType");
			EasyRecord record = new EasyRecord("YQ_KQ_OBJ","DK_DATE","USER_ID");
			record.set("IP", WebKit.getIP(getRequest()));
			record.set("device",getRequest().getHeader("User-Agent"));
			record.set("CREATE_TIME", dkTime);
			record.set("USER_ID", getUserId());
			record.set("DK_DATE", EasyCalendar.newInstance().getDateInt());
			record.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
			record.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
			record.set("DK_CITY", params.getString("city"));
			if(KqNoticeService.getService().kqFlag()) {
				record.set("DK_RANK", "正常班次");
			}else {
				record.set("DK_RANK", "加班");
			}
			boolean existData  = this.getQuery().update(record);
			if(!existData) {
				record.set("KQ_ID",RandomKit.uuid());
				record.set("DEPT_ID", getDeptId());
				record.set("USER_NAME", getUserName());
				record.set("STAFF_ID", getStaffInfo().getStaffNo());
				record.set("DEPT", getStaffInfo().getDeptName());
				this.getQuery().save(record);
			}
			
			String address = params.getString("address");
			if(signTimeType==0) {
				boolean bl = this.getQuery().queryForExist("select count(1) from YQ_KQ_OBJ where DK_DATE = ? and USER_ID = ? and SIGN_IN <>''",EasyCalendar.newInstance().getDateInt(),getUserId());
				if(bl) {
					return EasyResult.fail("已有今日上班卡，请勿重复(若提前申请外勤/出差流程，已自动填充数据！)");
				}
				record.set("ADDRESS_IN", address);
				record.set("SIGN_IN", EasyDate.getCurrentDateString("HH:mm"));
				record.set("LATE_TIME", calcLateMin());
				record.set("SIGN_INT_TIME", EasyDate.getCurrentDateString());
				record.set("LOCATION_IN", params.getString("location"));
				record.set("DISTANCE_IN", params.getString("distance"));
				record.set("ADDRESS_COMPONENT_IN", params.getString("addressComponent"));
				String remark = params.getString("remark");
				if(StringUtils.notBlank(remark)) {
					record.set("REASON_IN", remark);
				}
				this.getQuery().update(record);
				
				MessageModel model = new MessageModel();
				model.setReceiver(getUserId());
				model.setSender(getUserId());
				model.setTitle("打卡地点："+address);
				model.setData1(getUserName());
				model.setData2(EasyDate.getCurrentDateString());
				model.setData3("上班打卡成功");
				model.setDesc(KqNoticeService.getService().getZaoanWord());
				model.setUrl("https://wenxiaobai.com");
				WxMsgService.getService().sendKqDkNotice(model);
				
			}else {
				record.set("ADDRESS_OUT", address);
				record.set("SIGN_OUT", EasyDate.getCurrentDateString("HH:mm"));
				record.set("OVER_TIME", calcOverTime());
				record.set("SIGN_OUT_TIME", EasyDate.getCurrentDateString());
				record.set("LOCATION_OUT", params.getString("location"));
				record.set("DISTANCE_OUT", params.getString("distance"));
				record.set("ADDRESS_COMPONENT_OUT", params.getString("addressComponent"));
				String remark = params.getString("remark");
				if(StringUtils.notBlank(remark)) {
					record.set("REASON_OUT", remark);
				}
				this.getQuery().update(record);
				
				MessageModel model = new MessageModel();
				model.setReceiver(getUserId());
				model.setSender(getUserId());
				model.setTitle("打卡地点："+address);
				model.setData1(getUserName());
				model.setData2(EasyDate.getCurrentDateString());
				model.setData3("下班打卡成功");
				model.setDesc(KqNoticeService.getService().getTq(params.getString("city")));
				model.setUrl("https://wenxiaobai.com");
				WxMsgService.getService().sendKqDkNotice(model);
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(dkTime);
	}

	public EasyResult actionForSetBK() {
		JSONObject params = getJSONObject();
		String bkDate = params.getString("bkDate");
		String bkType = params.getString("bkType");
		String applyBy = params.getString("applyBy");
		String staffNo = StaffService.getService().getStaffNo(applyBy);
		if(StringUtils.isAnyBlank(bkDate,bkType)){
			return EasyResult.fail("补卡时间/补卡类型为空，未能更新考勤记录！");
		}
		String dateId = bkDate.replaceAll("-", "");
		try {
			// 先查询是否存在记录及其REMARK
			String existingRemark = "";
			JSONObject existingRecord = this.getQuery().queryForRow(
					"select REMARK from yq_kq_obj where STAFF_ID = ? and DK_DATE = ?",
					new Object[]{staffNo, dateId},
					new JSONMapperImpl()
			);
			if (existingRecord != null) {
				existingRemark = StringUtils.trimToEmpty(existingRecord.getString("REMARK"));
			}

			StringBuilder newRemark = new StringBuilder(existingRemark);
			if (StringUtils.notBlank(existingRemark)) {
				newRemark.append("/");
			}

			EasyRecord kqRecord = new EasyRecord("yq_kq_obj", "STAFF_ID", "DK_DATE");
			kqRecord.set("STAFF_ID", staffNo);
			kqRecord.set("DK_DATE", dateId);
			if ("上班".equals(bkType)) {
				kqRecord.set("SIGN_IN", "09:00");
				kqRecord.set("LATE_TIME", "");
				kqRecord.set("SIGN_INT_TIME", bkDate + " " + "09:00:00");
				newRemark.append("上班补签");
			} else if ("下班".equals(bkType)) {
				kqRecord.set("SIGN_OUT", "18:00");
				kqRecord.set("SIGN_OUT_TIME", bkDate + " " + "18:00:00");
				newRemark.append("下班补签");
			} else if ("整天".equals(bkType)) {
				kqRecord.set("SIGN_IN", "09:00");
				kqRecord.set("LATE_TIME", "");
				kqRecord.set("SIGN_INT_TIME", bkDate + " " + "09:00:00");
				kqRecord.set("SIGN_OUT", "18:00");
				kqRecord.set("SIGN_OUT_TIME", bkDate + " " + "18:00:00");
				newRemark.append("上班补签/下班补签");
			}
			kqRecord.set("REMARK", newRemark.toString());

			boolean bl = this.getQuery().update(kqRecord);
			if (!bl) {
				kqRecord.set("KQ_ID", RandomKit.uuid());
				kqRecord.set("USER_ID", applyBy);
				kqRecord.set("USER_NAME", StaffService.getService().getUserName(applyBy));
				if(KqNoticeService.getService().kqFlagByDateId(dateId)) {
					kqRecord.set("DK_RANK", "正常班次");
				}else {
					kqRecord.set("DK_RANK", "加班");
				}
				kqRecord.set("DEPT_ID",StaffService.getService().getUserMgrDeptIds(applyBy));
				kqRecord.set("DATE_ID",dateId);
				kqRecord.set("MONTH_ID", StringUtils.isNotBlank(dateId)?dateId.substring(0,6):"");
				this.getQuery().save(kqRecord);
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.fail("考勤记录更新失败");
		}
		return EasyResult.ok();
	}

	public EasyResult actionForSetLeaveOrTravel() {
		JSONObject params = getJSONObject();
		String startTime = params.getString("startTime");
		String endTime = params.getString("endTime");
		String applyBy = params.getString("applyBy");
		String staffNo = StaffService.getService().getStaffNo(applyBy);
		String reason = params.getString("reason");
		String dayNums = params.getString("dayNums");
		String flowCode = params.getString("flowCode");

		if (StringUtils.isAnyBlank(startTime, endTime)) {
			return EasyResult.fail("请假/外勤起止时间为空，未能更新考勤记录！");
		}
		// 将开始时间和结束时间转换为Date对象
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		try {
			int state = this.getQuery().queryForInt("select t1.state from yq_kq_people t1 where t1.kq_user_id = ?", applyBy);
			if(state == 1){
				//不考虑无需考勤的人
				return EasyResult.ok("申请人无需考勤");
			}
			Date startDate = sdf.parse(startTime);
			Date endDate = sdf.parse(endTime);

			// 设置今天20:00的时间点
			Calendar todayEnd = Calendar.getInstance();
			todayEnd.set(Calendar.HOUR_OF_DAY, 20);
			todayEnd.set(Calendar.MINUTE, 0);
			todayEnd.set(Calendar.SECOND, 0);
			todayEnd.set(Calendar.MILLISECOND, 0);

			// 用于遍历的日历对象
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startDate);

			// 遍历每一天
			while (!calendar.getTime().after(endDate)) {
				// 获取当前遍历日期的yyyyMMdd格式
				String currentDateId = new SimpleDateFormat("yyyyMMdd").format(calendar.getTime());
				String currentDate = new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime());

				// 设置当前遍历日期的00:00时间点
				Calendar currentDayEnd = Calendar.getInstance();
				currentDayEnd.setTime(calendar.getTime());
				currentDayEnd.set(Calendar.HOUR_OF_DAY, 0);
				currentDayEnd.set(Calendar.MINUTE, 0);
				currentDayEnd.set(Calendar.SECOND, 0);
				currentDayEnd.set(Calendar.MILLISECOND, 0);

				// 如果当前遍历日期已经大于今天20:00，退出
				if (currentDayEnd.getTime().after(todayEnd.getTime())) {
					break;
				}


				// 使用kqFlagByDateId判断是否为工作日
				if (KqNoticeService.getService().kqFlagByDateId(currentDateId)) {
					// 是工作日，更新考勤记录
					EasyRecord record = new EasyRecord("YQ_KQ_OBJ", "DK_DATE", "STAFF_ID");
					record.set("DK_DATE", currentDateId);
					record.set("STAFF_ID",staffNo);
					record.set("DK_RANK", "正常班次");
					// 添加原因到备注中
					record.set("REMARK", reason);
					//更新记录，如果不存在则不处理
					boolean bl = this.getQuery().update(record);
					//有update成功并且是出差/外勤的
					if(bl&&"ha_travel".equals(flowCode)){
						JSONObject oldRecord = this.getQuery().queryForRow("select * from YQ_KQ_OBJ where DK_DATE = ? and STAFF_ID = ?", 
							new Object[] {currentDateId,staffNo}, new JSONMapperImpl());

						// 获取当天的时间边界
						String morning = currentDate + " 09:00";
						String evening = currentDate + " 18:00";
						// 判断外勤时间是否覆盖上午或下午
						boolean containsMorning = startTime.compareTo(morning) <= 0;  // 开始时间早于等于9点
						boolean containsEvening = endTime.compareTo(evening) >= 0;  // 结束时间晚于等于18点
								
						// 处理签到时间
						String signInTime = "09:00";
						if (containsMorning) {
							// 如果外勤覆盖上午，检查是否有更早的打卡记录
							String existingSignIn = oldRecord.getString("SIGN_IN");
							if(StringUtils.notBlank(existingSignIn)) {
								String hour = existingSignIn.substring(0,2);
								if(Integer.parseInt(hour) < 9) {
									signInTime = existingSignIn;
								}
							}
							record.set("SIGN_IN", signInTime);
							record.set("SIGN_INT_TIME", currentDate + " " + signInTime + ":00");
							record.set("LATE_TIME", "");
						}

						// 处理签退时间
						String signOutTime = "18:00";
						if (containsEvening) {
							// 如果外勤覆盖下午，检查是否有更晚的打卡记录
							String existingSignOut = oldRecord.getString("SIGN_OUT");
							if(StringUtils.notBlank(existingSignOut)) {
								String hour = existingSignOut.substring(0,2);
								if(Integer.parseInt(hour) >= 18) {
									signOutTime = existingSignOut;
								}
							}
							record.set("SIGN_OUT", signOutTime);
							record.set("SIGN_OUT_TIME", currentDate + " " + signOutTime + ":00");
						}
								
						this.getQuery().update(record); //更新记录
					}
				}
				// 移动到下一天
				calendar.add(Calendar.DATE, 1);
			}
		} catch (ParseException e) {
			this.getLogger().info("请假/出差流程同步到考勤记录失败，员工号："+staffNo +"-申请:"+reason+"-起止时间:"+startTime+" "+endTime);
			this.getLogger().error("日期格式解析失败:"+e.getMessage());
			return EasyResult.fail("日期格式解析失败");
		} catch (SQLException e) {
			this.getLogger().info("请假/出差流程同步到考勤记录失败，员工号："+staffNo +"-申请:"+reason+"-起止时间:"+startTime+" "+endTime);
			this.getLogger().error("考勤记录更新失败:"+e.getMessage());
			return EasyResult.fail("考勤记录更新失败");
		}

		return EasyResult.ok() ;
	}

	public EasyResult actionForUploadFile()  throws SQLException, IOException, ServletException {
		EasyResult result = new EasyResult();
		try {
			Part part = this.getFile("kqData");
			String filename = new String(getFilename(part).getBytes(), "UTF-8"); 
			String separator = File.separator;
			String path=Globals.SERVER_DIR+File.separator+"kaoqin";
			if (!new File(path).exists()) {
				new File(path).mkdirs();
			}
			File tmpWarFile = new java.io.File(path + separator + EasyDate.getCurrentDateString("yyyyMMddHHmmss")+ FileKit.getHouzui(filename));
			if (tmpWarFile.exists()) {
				tmpWarFile.delete();
			}
			
			FileKit.saveToFile(part.getInputStream(), tmpWarFile.getAbsolutePath());
			
		    Workbook workbook = WorkbookFactory.create(part.getInputStream());
		    List<List<String>> list = new ArrayList<>();
		    int num = workbook.getNumberOfSheets();
		    
		    int succCount = 0;
		    int lastCellNum=0;
		    for(int index =0 ;index < num ;index++){
		    	Sheet sheet = workbook.getSheetAt(index);
		    	int maxLine =sheet.getLastRowNum();
		    	for (int ii = 0; ii <= maxLine; ii++) {
		    		List<String> rows = new ArrayList<>();
		    		Row row = sheet.getRow(ii);
		    		lastCellNum=row.getLastCellNum();
		    		if(ii==0){
		    			if(lastCellNum<5){
		    				return EasyResult.fail("excel不匹配!");
		    			}
		    		}
		    		if(row!=null){
		    			for(int j=0;j<lastCellNum;j++){
		    				Cell cell=row.getCell(j);
		    				if(cell!=null){
		    					String val = Utils.getCellValue(cell);
		    					if(StringUtils.isBlank(val)){
		    						rows.add("");
		    					}else{
		    						rows.add(val);
		    					}
		    				}else{
		    					rows.add("");
		    				}
		    			}
		    			list.add(rows);
		    		}
		    	}
		    }
		 
		     Map<Integer,String> fieldNameIndex = new LinkedHashMap<Integer,String>();
		     List<String> headerRow = list.get(0);
		     for(int i=0;i<headerRow.size();i++) {
		    	String name =  headerRow.get(i);
		    	fieldNameIndex.put(i, name);
		     }
		     list.remove(0);
		     
		     
		      Map<String,String> fields=new LinkedHashMap<String,String>();
		      fields.put("考勤号码","STAFF_ID");
		      fields.put("姓名", "USER_NAME");
		      fields.put("日期", "DK_DATE");
		      fields.put("对应时段","DK_RANK");
		      fields.put("签到时间","SIGN_IN");
		      fields.put("签退时间","SIGN_OUT");
		      fields.put("例外情况","REMARK");
		      fields.put("加班时间","OVER_TIME");
		      fields.put("实到","TRUE_TO");
		      fields.put("是否旷工","NOT_WORK");
		      fields.put("迟到时间","LATE_TIME");
		      fields.put("部门", "DEPT");
		      fields.put("出勤时间","SUM_TIME");
		      
		      String batchId = RandomKit.uuid();
		      
		      boolean isNotNull = true;
			  for(int j=0;j<list.size();j++){
				List<String> strs=list.get(j);
				if(strs.size()>0){
					EasyRecord record=new EasyRecord("YQ_KQ_DATA","STAFF_ID","DK_DATE");
					for(int x=0;x<lastCellNum;x++){
						String title = fieldNameIndex.get(x);
						String field = fields.get(title);
						if(StringUtils.isNotBlank(title)&&StringUtils.notBlank(field)) {
							String value=StringUtils.trimToEmpty(strs.get(x));
							if("DK_DATE".equals(field)) {
								value = dkDateFormat(value);	
								Integer intDate = Integer.valueOf(value);
								if(intDate==null) {
									  System.out.println(intDate);
								 }
								 if(intDate>EasyCalendar.newInstance().getDateInt()) {
									 isNotNull = false;
								  }
								  
								  record.set("DATE_ID", value);
								  record.set("MONTH_ID", value.substring(0, 6));
							}
							if("STAFF_ID".equals(field)&&"4".equals(value)) {
								value = "100294";
							}
							record.set(field, value);
						}
					}
					if(!isNotNull) {
						isNotNull = true;
						continue;
					}
					boolean b=this.getQuery().update(record);
					if(!b){
						record.set("CREATE_TIME", EasyDate.getCurrentDateString());
						record.put("BATCH_ID",batchId);
						record.set("KQ_ID", RandomKit.uniqueStr());
						getQuery().save(record);
						succCount++;
						this.getLogger().info(succCount+" obj upload succ.");
					}
			   }
		    }
			  
			EasyRecord record=new EasyRecord("YQ_KQ_BATCH","BATCH_ID");
			record.setPrimaryValues(batchId);
			record.set("upload_time", EasyDate.getCurrentDateString());
			record.set("file_name",filename);
			record.set("excel_count",succCount);
			record.set("file_path",tmpWarFile.getAbsolutePath());
			record.set("upload_by",getRemoteUser());
			this.getQuery().save(record);
			
			this.getQuery().executeUpdate("update yq_kq_batch t1 set t1.kq_scope = CONCAT((select min(DK_DATE) from yq_kq_data t2 where t2.batch_id = t1.batch_id),'~',(select max(DK_DATE) from yq_kq_data t2 where t2.batch_id = t1.batch_id)) where t1. batch_id = ?", batchId);
			
			this.getQuery().executeUpdate("update yq_kq_data t1,"+Constants.DS_MAIN_NAME+".yq_staff_info t2 set t1.user_id = t2.staff_user_id where t1.STAFF_ID = t2.staff_no and t1.batch_id = ?",batchId);
			
			this.getQuery().executeUpdate("UPDATE yq_kq_data t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_dept_user t2 ON t1.user_id = t2.user_id SET t1.dept_id = t2.dept_id WHERE t1.batch_id = ?",batchId);
			
			this.getQuery().executeUpdate("update yq_kq_batch t1 set t1.excel_count = (select count(1) from yq_kq_data t2 where t2.batch_id = t1.batch_id)");
			result.put("state", 1);
			result.put("msg", succCount+"条数据上传成功!");
			part.delete();
			return result;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.addFail(e.getMessage());
			return result;
		}finally {
			
		}
	}
	
    private String dkDateFormat(String dateStr) {
    	try {
    		dateStr = dateStr.replaceAll(" ", "");
    		SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");  
    		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");  
    		Date date = sdf.parse(dateStr);
    	    return sdf2.format(date);
    	} catch (Exception e) {  
    		e.printStackTrace();  
    	}
    	return "";  
    }
    
    private String calcLateMin() {
    	try {
			SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			long from = simpleFormat.parse(EasyDate.getCurrentDateString()).getTime();
			long to = simpleFormat.parse(EasyDate.getCurrentDateString("yyyy-MM-dd")+" 09:00").getTime();
			int minutes = (int) ((from-to)/(1000 * 60));
			if(minutes<=0) {
				return "";
			}
			return String.valueOf(minutes);
		} catch (ParseException e) {
			this.error(null, e);
			return "";
		}
    }
    
    private String calcOverTime() {
    	try {
    		SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    		long from = simpleFormat.parse(EasyDate.getCurrentDateString()).getTime();
    		long to = simpleFormat.parse(EasyDate.getCurrentDateString("yyyy-MM-dd")+" 19:00").getTime();
    		int minutes = (int) ((from-to)/(1000 * 60));
    		if(minutes<=0) {
    			return "";
    		}
    		return String.valueOf(minutes);
    	} catch (ParseException e) {
    		this.error(null, e);
    		return "";
    	}
    }
	
	 private String getFilename(Part part) {  
        String contentDispositionHeader = part.getHeader("content-disposition");  
        String[] elements = contentDispositionHeader.split(";");  
        for (String element : elements) {  
            if (element.trim().startsWith("filename")) {  
                return element.substring(element.indexOf('=') + 1).trim().replace("\"", "");  
            }  
        }  
        return null;  
	}

	 public EasyResult actionForDelBatch(){
		 String batchId = getJsonPara("batchId");
		 try {
			this.getQuery().executeUpdate("delete from yq_kq_data where batch_id = ?", batchId);
			this.getQuery().executeUpdate("delete from yq_kq_batch where batch_id = ?", batchId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		 return EasyResult.ok();
	 }
	 public EasyResult actionForSetFlowId(){
		 JSONObject params = getJSONObject();
		 String flowId=params.getString("flowId");
		 String kqId=params.getString("kqId");
		 try {
			 this.getQuery().executeUpdate("update yq_kq_data set flow_id = ? where kq_id = ?", flowId,kqId);
		 } catch (SQLException e) {
			 e.printStackTrace();
		 }
		 return EasyResult.ok();
	 }
	 
	 public void actionForExportWxKqRecord(){
			String _data = getPara("data");
			JSONObject param = JSONObject.parseObject(_data);
			List<String> headers=new ArrayList<String>();
			
			EasySQL sql = new EasySQL("select * from yq_kq_obj where 1=1");
			
			String monthId = param.getString("monthId");
			if(StringUtils.notBlank(monthId)) {
				sql.append(monthId.replaceAll("-", ""),"and month_id = ?");
			}
			sql.appendLike(param.getString("userName"),"and user_name like ?");
			
			String hrFlag = param.getString("hrFlag");
			if("1".equals(hrFlag)) {
				
			}else if(hasRole("DEPT_MGR")) {
				sql.append(getDeptId(),"and dept_Id = ?");
			}else{
				sql.append(getUserId(),"and user_Id = ?");
			}

			String beginDate = param.getString("beginDate");
			String endDate = param.getString("endDate");
			
			if(StringUtils.notBlank(beginDate)) {
				sql.append(beginDate.replaceAll("-",""),"and DK_DATE >= ?");
			}
			if(StringUtils.notBlank(endDate)) {
				sql.append(endDate.replaceAll("-",""),"and DK_DATE <= ?");
			}
			sql.append("order by DK_DATE desc");
			
			File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
			/**创建头部*/
			headers.add("工号");
			headers.add("部门");
			headers.add("姓名");
			headers.add("考勤日期");
			headers.add("上班打卡地址");
			headers.add("下班打卡地址");
			headers.add("打卡城市");
			headers.add("班次");
			headers.add("上班时间");
			headers.add("下班时间");
			headers.add("上班偏差距离");
			headers.add("下班偏差距离");
			headers.add("上班异常定位");
			headers.add("下班异常定位");
			headers.add("迟到时间");
			headers.add("加班时间");
			headers.add("例外情况");
			
			List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
			int x=0;
			for(String header:headers){
				ExcelHeaderStyle style=new ExcelHeaderStyle();
				style.setData(header);
				if(x==1){
					style.setWidth(12000);
				}else {
					style.setWidth(4500);
				}
				style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
				styles.add(style);
				x++;
			}
			
			List<List<String>> excelData=new ArrayList<List<String>>();
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> data = null;
			try {
				data = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			} catch (SQLException e) {
				this.error(null, e);
				renderHtml(e.getMessage());
				return;
			}
			
			if(data!=null && data.size()>0){
				for (int i = 0; i < data.size(); i++) {
					JSONObject map = data.get(i);
					List<String> list=new ArrayList<String>();
					list.add(map.getString("STAFF_ID"));
					list.add(map.getString("DEPT"));
					list.add(map.getString("USER_NAME"));
					list.add(map.getString("DK_DATE"));
					list.add(map.getString("ADDRESS_IN"));
					list.add(map.getString("ADDRESS_OUT"));
					list.add(map.getString("DK_CITY"));
					list.add(map.getString("DK_RANK"));
					list.add(map.getString("SIGN_IN"));
					list.add(map.getString("SIGN_OUT"));
					list.add(map.getString("DISTANCE_IN"));
					list.add(map.getString("DISTANCE_OUT"));
					list.add(map.getString("REASON_IN"));
					list.add(map.getString("REASON_OUT"));
					list.add(map.getString("LATE_TIME"));
					list.add(map.getString("OVER_TIME"));
					list.add(map.getString("REMARK"));
					
					excelData.add(list);
				}
			}
			try {
				ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			} catch (IOException e) {
				e.printStackTrace();
			}
			String fileName="考勤列表.xlsx";
			renderFile(file,fileName,true);
		}
}





