<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>发票详情</title>
	<style>
		.form-horizontal{width: 100%;}
		.del-file-btn{cursor: pointer;font-size: 20px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="InvoiceEditForm" class="form-horizontal" data-text-model="false" data-mars="BxInvoiceDao.bxInvoiceInfo" autocomplete="off" data-mars-prefix="invoice.">
 		   		<input type="hidden" value="${param.invoiceId}" name="invoiceId"/>
 		   		<input type="hidden" value="${param.invoiceId}" name="fkId"/>
 		   		<input type="hidden" value="${param.invoiceId}" name="invoice.INVOICE_ID"/>
 		   		<input type="hidden" id="bxStateVal"  name="invoice.BX_STATE"/>
 		   		<input type="hidden" id="invoiceFileId"  name="invoice.FILE_ID"/>
 		   		<input data-mars="CommonDao.randomId" id="randomId" type="hidden">
				<table class="table table-edit table-vzebra">
			        <tbody>
				        <tr>
		                    <td width="100px">所属人</td>
		                    <td colspan="3">
		                    	 <input name="invoice.CREATOR" type="hidden">
		                   		 <input placeholder="如果不选择代表本人" type="text" readonly="readonly" onclick="singleUser(this);" name="invoice.CREATE_NAME" class="form-control input-sm">
		                    </td>
				        </tr>
			            <tr>
		                    <td width="100px" class="required">发票号</td>
		                    <td style="width: 40%">
		                   		 <input data-rules="required" type="text" name="invoice.INVOICE_NO" <c:if test="${param.op=='edit'}">readonly="readonly"</c:if> class="form-control input-sm">
		                    </td>
		                    <td class="required">开票类型</td>
		                    <td style="width: 40%">
		                   		 <select data-rules="required" name="invoice.INVOICE_TYPE" onchange="setRate(this.value)" class="form-control input-sm">
		                   		 	    <option value="">--</option>
		                   		 	    <option value="专用发票">专用发票</option>
								        <option value="区块链发票">区块链发票</option>
								        <option value="普通发票">普通发票</option>
								        <option value="电子专用发票">电子专用发票</option>
								        <option value="电子发票(专用发票)">电子发票(专用发票)</option>
								        <option value="电子发票(普通发票)">电子发票(普通发票)</option>
								        <option value="电子发票(铁路电子客票)">电子发票(铁路电子客票)</option>
								        <option value="电子发票(航空运输电子客票行程单)">电子发票(航空运输电子客票行程单)</option>
								        <option value="电子发票普通发票">电子发票普通发票</option>
								        <option value="电子普通发票">电子普通发票</option>
								        <option value="通用机打电子发票">通用机打电子发票</option>
								        <option value="通行费电子普票">通行费电子普票</option>
		                   		 </select>
		                    </td>
			            </tr>
			           <tr>
		                    <td width="100px" >开票单位</td>
		                    <td>
								<input type="text"  name="invoice.SALE_COMPANY" class="form-control input-sm"/>
		                    </td>
			           		<td>销售方编号</td>
		                    <td>
		                   		 <input type="text" name="invoice.SALE_COMPANY_NO" class="form-control input-sm">
		                    </td>
			           </tr>
			           <tr>
		                    <td>购买方</td>
		                    <td>
		                   		 <input type="text" name="invoice.BUY_COMPANY" class="form-control input-sm">
		                    </td>
		                    <td>购买方编号</td>
		                    <td>
		                   		 <input type="text" name="invoice.BUY_COMPANY_NO" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
		                    <td class="required">发票金额</td>
		                    <td>
		                    	 <input type="text" data-rules="required" name="invoice.TOTAL_AMOUNT" onchange="calcMoney(this.value)" class="form-control input-sm">
		                    </td>
		                    <td class="required">开票日期</td>
		                    <td>
		                    	 <input type="text" data-rules="required" data-laydate="{type:'date',format: 'yyyy年MM月dd日'}" name="invoice.INVOICE_DATE" class="form-control input-sm">
		                    </td>
		                </tr>
		                  <tr>
		                	<td class="required">税额</td>
		                    <td>
		                   		 <input type="text" data-rules="required" name="invoice.TOTAL_TAX" class="form-control input-sm">
		                    </td>
		                   <td class="required">不含税</td>
		                    <td>
		                   		 <input type="text" data-rules="required" name="invoice.AMOUNT_IN_FIGUERS" class="form-control input-sm">
		                    </td>
		                </tr>
		                 <tr>
		                    <td class="required">税率</td>
		                    <td>
		                   		 <input type="text" data-rules="required" name="invoice.TAX_RATE" class="form-control input-sm">
		                    </td>
		                    <td>服务类型</td>
		                    <td>
		                   		 <input type="text" name="invoice.SERVICE_TYPE" class="form-control input-sm">
		                    </td>
			            </tr>
		                 <tr>
		                    <td>商品名称</td>
		                    <td>
		                   		 <input type="text" name="invoice.COMMODITY_NAME" class="form-control input-sm">
		                    </td>
		                    <td>发票名称</td>
		                    <td>
		                   		 <input type="text" name="invoice.INVOICE_TYPE_ORG" readonly="readonly" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
			            	<td>备注</td>
			               	<td colspan="3">
	                           <textarea style="height: 60px;" class="form-control input-sm" name="invoice.KP_REMARK"></textarea>
			               	</td>
			            </tr>
			            <tr>
			            	<td>PDF文件</td>
			               	<td colspan="3">
			               		<c:choose>
				               		<c:when test="${param.op=='add'}">
				               			<input name="invoice.FILE_NAME" id="fileName" style="display: inline-block;width: 100px;" type="text" readonly="readonly" class="form-control input-sm">
				               			<button class="btn btn-info btn-xs" data-type="pdf" id="upload-btn" type="button">上传相关资料</button>
				               		</c:when>
			               			<c:otherwise>
					               		<a id="pdfFileUrl" target="_blank"><span name="invoice.FILE_NAME"></span></a>
			               			</c:otherwise>
			               		</c:choose>
			               	</td>
			            </tr>
			            <tr>
			            	<td>XML文件</td>
			               	<td colspan="3">
			               		<a id="xmlFileUrl" target="_blank"><span name="invoice.XML_FILE_NAME"></span></a>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <c:choose>
			    	  	<c:when test="${param.op=='add'}">
			    	 		 <button type="button" class="btn btn-info btn-sm ml-15 update-btn" style="width: 80px;" onclick="InvoiceEit.ajaxSubmitForm()"> 新增 </button>
			    	  	</c:when>
			    	  	<c:when test="${param.op=='edit'}">
				      		<button type="button" class="btn btn-danger btn-sm  del-btn" style="width: 80px" onclick="InvoiceEit.delData();"> 删除 </button>
			    	  		<button type="button" class="btn btn-default btn-sm ml-15 update-btn" style="width: 80px;" onclick="InvoiceEit.ajaxSubmitForm()"> 修 改 </button>
			    	  	</c:when>
			    	  </c:choose>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("InvoiceEit");
	    
		InvoiceEit.invoiceId='${param.invoiceId}';
		
		var invoiceId = '${param.invoiceId}';
		var op = '${param.op}';
		
		$(function(){
			if(op=='detail'){
				$("#InvoiceEditForm").attr('data-text-model',true);	
			}

			$("#InvoiceEditForm").render({success:function(result){
				var bxState = $('#bxStateVal').val();
				if(bxState!=0){
					$('.del-btn,.update-btn').remove();
				}
				var invoiceFileId = $('#invoiceFileId').val();
				
				if(op=='detail'){
					$("#InvoiceEditForm #upload-btn").hide();
					$("#InvoiceEditForm .update-btn,.del-btn,.del-file-btn").hide();
				}
				
				renderDate('#InvoiceEditForm');
				
				if(invoiceId){
					var row = result['BxInvoiceDao.bxInvoiceInfo'].data;
					var pdfFileId = row.FILE_ID;
					if(pdfFileId){
						$('#pdfFileUrl').attr('href','/yq-work/fileview/'+pdfFileId+'?view=online&filename='+row.FILE_NAME);
					}else{
						$('#pdfFileUrl').remove();
					}
					var xmlFileId = row.XML_FILE_ID;
					if(xmlFileId){
						$('#xmlFileUrl').attr('href','/yq-work/fileview/'+xmlFileId+'?view=online&filename='+row.XML_FILE_NAME);
					}else{
						$('#xmlFileUrl').remove();
					}
				}
				
			}});  
			
			layui.use('upload', function(){
			 	  var upload = layui.upload;
				  var uploadInst = upload.render({
				    elem: '#upload-btn'
				    ,url: '/yq-work/servlet/upload?action=index'
				    ,accept: 'file'
				    ,exts:'pdf|ofd|xml'
				    ,size:1024*20
					,multiple:true
					,number:10
				    ,field: 'file'
				    ,data:{
				    	source:function(){
							return 'bxInvoice';
						},
						fkId:function(){
							if(InvoiceEit.invoiceId){
								return InvoiceEit.invoiceId;
							}else{
								return $("#randomId").val();								
							}
						}
				    },done: function(result, index, upload){
					    if(result.state==1){
					    	layer.msg(result.msg+'，已自动保存',{icon: 1,time:1800},function(){
					    		$('#invoiceFileId').val(result.data.id);
					    		$('#fileName').val(result.data.name);
							}); 
						}else{
							 layer.closeAll('dialog');
							 layer.closeAll('loading');
							 layer.alert(result.msg,{icon: 5});
						}
				    },choose:function(obj){
				    	
				    },before:function(obj){
				    	
				    },allDone: function(obj){
		
					}
				    ,error: function(res, index, upload){
				    	layer.close(loadIndex);
				    	layer.alert("上传文件请求异常！",{icon: 5});
				   }
		    });
		  });
			
		});
		
		InvoiceEit.ajaxSubmitForm = function(){
				if(form.validate("#InvoiceEditForm")){
					if(InvoiceEit.invoiceId){
						layer.confirm('发票识别有误,确认是否要修改',{title:'请最终以开票电子发票文件为准',icon:3,offset:'20px'},function(index){
							layer.close(index);
							InvoiceEit.updateData(); 
						});
					}else{
						InvoiceEit.insertData(); 
					}
				};
		}
		
		InvoiceEit.insertData = function(flag) {
			var data = form.getJSONObject("#InvoiceEditForm");
			if(data['invoice.FILE_ID']==''){
				layer.msg('请上传发票',{icon:7,time:800});
				return;
			}
			delete data['invoice.INVOICE_ID'];
			data['invoice.BX_STATE'] = '0';
			data['invoice.INVOICE_ID'] = $("#randomId").val();
			ajax.remoteCall("${ctxPath}/servlet/bx/invoice?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadInvoiceList();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		InvoiceEit.updateData = function(flag) {
			var data = form.getJSONObject("#InvoiceEditForm");
			ajax.remoteCall("${ctxPath}/servlet/bx/invoice?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadInvoiceList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		InvoiceEit.delData = function() {
			var data = form.getJSONObject("#InvoiceEditForm");
			layer.confirm('确认删除吗',{icon:3,title:'删除提醒',offset:'20px'},function(){
				ajax.remoteCall("${ctxPath}/servlet/bx/invoice?action=del",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
							reloadInvoiceList();
							ajax.remoteCall("/yq-work/servlet/upload?action=del",{id:data.invoiceId},function() {});
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		function calcMoney(kpMoney){
			var invoiceType = $("[name='invoice.INVOICE_TYPE']").val();
			if(invoiceType==''){
				layer.msg('请先选择开票类型',{icon:7,offset:'20px',time:1200});
				return;
			}
			if(invoiceType&&invoiceType.indexOf('铁路')>-1){
				var tax  = calculateTax (kpMoney,9);
				$("[name='invoice.TOTAL_TAX']").val(tax);
				$("[name='invoice.AMOUNT_IN_FIGUERS']").val(kpMoney - tax);
			}
			if(invoiceType&&invoiceType.indexOf('航空')>-1){
				var tax  = calculateTax (kpMoney,9);
				$("[name='invoice.TOTAL_TAX']").val(tax);
				$("[name='invoice.AMOUNT_IN_FIGUERS']").val(kpMoney - tax);
			}
		}
		
		function calculateTax(amount, taxRate) {
			  // 将税率从百分比转换为小数
			  var rateAsDecimal = taxRate / 100;
			  
			  // 计算税额
			  var taxAmount = amount * rateAsDecimal;
			  
			  // 四舍五入到最接近的整数
			  var roundedTaxAmount = Math.round(taxAmount);
			  
			  // 返回四舍五入后的税额
			  return roundedTaxAmount;
		}
		
		function setRate(val){
			if(val&&val.indexOf('铁路')>-1){
				$("[name='invoice.TAX_RATE']").val('9%');
				layer.msg('电子发票(铁路电子客票)固定税率是9%',{icon:1,offset:'20px',time:1200});
			}
			if(val&&val.indexOf('航空')>-1){
				$("[name='invoice.TAX_RATE']").val('9%');
				layer.msg('电子发票(航空运输电子客票行程单)固定税率是9%',{icon:1,offset:'20px',time:1200});
			}
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>