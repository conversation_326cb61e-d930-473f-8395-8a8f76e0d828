<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>
		
		.layui-card-body{height: calc(100vh - 150px);overflow-y: scroll;};
		.layui-card-body table{padding: 10px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="weeklyForm" data-page-hide="true">
			<div class="ibox">
					<div class="ibox-content weeklyTitle">
				    	<div class="input-group input-group-sm" style="width: 310px">
	          		    	<span class="input-group-addon">工作日期</span>	
                     		<input data-mars="CommonDao.threeMonthRange" data-mars-reload="false" style="border-right: 0;" type="text" data-laydate="{type:'date',range: '到'}" name="updateDate" class="form-control input-sm">
                     		<div class="input-group-btn">
	                     		<select class="form-control input-sm" style="width:80px;" onchange="getDate(this,'updateDate')">
		                     		<option value="">全部</option>
		                     		<option value="currentMonth">本月</option>
		                     		<option value="oneWeek">一周内</option>
		                     		<option value="oneMonth">一个月内</option>
		                     		<option selected="selected" value="threeMonth">三个月内</option>
		                     		<option value="sixMonth">半年内</option>
		                     		<option value="oneYear">一年内</option>
		                     	</select>
                     	   </div>
                    	 </div>
				    	<button type="button" class="btn btn-sm btn-default ml-10" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					</div>
					
					<div class="layui-row layui-col-space10 mt-10">
					  <div class="layui-col-md4">
					    <div class="layui-card mt-10">
						  <div class="layui-card-header">周报填写数排行榜</div>
						  <div class="layui-card-body">
								<table id="yearTable"></table>
						  </div>
						</div>
					  </div>
					  <div class="layui-col-md4">
						    <div class="layui-card mt-10">
							  <div class="layui-card-header">周报填写倒数排行榜</div>
							  <div class="layui-card-body">
									<table id="weeklyDown"></table>
							  </div>
							</div>
					  </div>
					  <div class="layui-col-md4">
						    <div class="layui-card mt-10">
							  <div class="layui-card-header">部门周报数</div>
							  <div class="layui-card-body">
									<table id="weeklyNumTop"></table>
							  </div>
							</div>
					  </div>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
	
	<script type="text/javascript">
		$(function(){
			$('#weeklyForm').render({success:function(){
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.nowYearTop',
					id:'yearTable',
					page:false,
					cellMinWidth:100,
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							width:50,
							align:'left'
						 },{
						    field: 'CREATE_NAME',
							title: '姓名',
							align:'center'
						},{
						    field: 'COUNT',
							title: '周报数量',
							align:'center'
						}
					]]}
				);
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.weeklyDown',
					id:'weeklyDown',
					page:false,
					cellMinWidth:100,
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							width:50,
							align:'left'
						 },{
						    field: 'CREATE_NAME',
							title: '姓名',
							align:'center'
						},{
						    field: 'COUNT',
							title: '周报数量',
							align:'center'
						}
					]]}
				);
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.weeklyNumTop',
					id:'weeklyNumTop',
					page:false,
					cellMinWidth:100,
					cols: [[
						{
							field: 'WEEK_NO',
							title: '周数',
							width:50,
							align:'center'
						 },{
						    field: 'CREATE_NAME',
							title: '姓名',
							align:'center',
						    width:180,
							templet:'<div>{{d.WEEK_BEGIN}}~{{d.WEEK_END}}</div>'
						},{
						    field: 'COUNT',
						    width:90,
							title: '周报数量',
							align:'center'
						}
					]]}
				);
				
			}});
			
		});
	
		function queryData(){
			$("#weeklyForm").queryData({id:'weeklyNumTop',page:false});
			$("#weeklyForm").queryData({id:'weeklyDown',page:false});
			$("#weeklyForm").queryData({id:'yearTable',page:false});
		}
		var getDate = function(obj,inputName){
			var val = obj.value;
			var bdate = new Date();
			var edate = new Date();
			if('createTime'==inputName||'updateDate'==inputName){
				if(val == 'today'){
					
				}else if(val == 'yesterday'){
					bdate.setDate(bdate.getDate() - 1);
					edate = bdate;
				}else if(val == 'threeDay'){
					bdate.setDate(bdate.getDate() - 3);
				}else if(val == 'oneWeek'){
					bdate.setDate(bdate.getDate() - 7);
				}else if(val == 'oneMonth'){
					bdate.setMonth(bdate.getMonth() - 1);
				}else if(val == 'threeMonth'){
					bdate.setMonth(bdate.getMonth() - 3);
				}else if(val == 'sixMonth'){
					bdate.setMonth(bdate.getMonth() - 6);
				}else if(val == 'oneYear'){
					bdate.setMonth(bdate.getMonth() - 12);
				}else if(val == 'currentMonth'){
					bdate.setDate(1);
					edate.setMonth(bdate.getMonth() + 1);
					edate.setDate(0);
				}
				if(val){
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}else{
				if(val){
					bdate.setDate(bdate.getDate() - val);
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}
			queryData();
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>