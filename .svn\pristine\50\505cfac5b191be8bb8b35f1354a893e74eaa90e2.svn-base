<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="supplierId" type="hidden" value="${param.supplierId}">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 150px;">
					 		<span class="input-group-addon">采购组织</span>
						 	 <select name="orderOrg" onchange="DataMgr.query()" class="form-control">
	                   		 	<option value="">请选择</option>
	                   		 	<option value="云趣">云趣</option>
	                   		 	<option value="中融">中融</option>
	                   		 </select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		 <span class="input-group-addon">订单号</span>
						 	 <input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		 <span class="input-group-addon">合同名称</span>
						 	 <input class="form-control input-sm" name="contractName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		 <span class="input-group-addon">合同号</span>
						 	 <input class="form-control input-sm" name="contractNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">采购类型</span>
						 	 <select name="orderType" onchange="DataMgr.query()" class="form-control">
	                   		 	<option value="">请选择</option>
	                   		 	<option value="0">销售采购</option>
	                   		 	<option value="1">固定资产</option>
	                   		 </select>
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" data-event="enter" class="btn btn-sm btn-default" onclick="DataMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
		</form>
		 <script type="text/html" id="btnBar">
				<button type="button" class="btn btn-sm btn-info" onclick="DataMgr.add()">+ 创建采购订单</button>
				<div class="btn-group btn-group-sm ml-10">
					<button type="button" class="btn btn-xs btn-success dropdown-toggle" data-toggle="dropdown">发起流程 <span class="caret"></span></button>
					<ul class="dropdown-menu dropdown-menu-right">
						<li><a href="javascript:void(0);" lay-event="DataMgr.reviewApply">+采购评审</a></li>
						<li><a href="javascript:void(0);" lay-event="DataMgr.payApply">+采购付款</a></li>
						<li><a href="javascript:void(0);" lay-event="DataMgr.getGoods">+到货确认</a></li>
						<li><a href="javascript:void(0);" lay-event="DataMgr.getwl">+零星物料领用</a></li>
				   </ul>
				</div>
				<div class="btn-group btn-group-sm ml-10">
					<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">详情  <span class="caret"></span> </button>
					<ul class="dropdown-menu dropdown-menu-right">
						<li><a href="javascript:;" lay-event="DataMgr.detail">订单详情</a></li>
						<li><a href="javascript:;" lay-event="DataMgr.contractDetail">合同详情</a></li>
						<li><a href="javascript:;" lay-event="DataMgr.supplierDetail">供应商详情</a></li>
				   </ul>
				</div>
				<div class="btn-group btn-group-sm ml-10">
					<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">更多 <span class="caret"></span></button>
					<ul class="dropdown-menu dropdown-menu-right">
						<li><a href="javascript:;" lay-event="cannelOrder">取消采购</a></li>
						<li><a href="javascript:;" lay-event="stopOrder">终止采购</a></li>
				   </ul>
				</div>
			 	<button class="btn btn-sm btn-default layui-hide-xs ml-10" type="button" lay-event="DataMgr.refreshData"><i class="fa fa-refresh" aria-hidden="true"></i> 刷新</button>
			 	<button class="btn btn-sm btn-default edit-btn ml-10" type="button" lay-event="DataMgr.orderEdit"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> 修改</button>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var DataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.list',
					id:'dataMgrTable',
					autoSort:false,
					limit:30,
					rowEvent:'rowEvent',
					rowDoubleEvent:'DataMgr.detail',
					toolbar:'#btnBar',
					height:'full-90',
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 type:'radio'
					 },{
					     field: 'SUPPLIER_NAME',
						 title: '供应商',
						 align:'left',
						 minWidth:180
					},{
						 field:'ORDER_NO',
						 title:'订单编号',
						 width:90
					 },{
					    field: 'ORDER_ORG',
						title: '采购组织',
						align:'left',
						width:70
					},{
					    field: 'CONTRACT_NAME',
						title: '合同名称',
						align:'left',
						minWidth:200
					},{
					    field: 'ORDER_TYPE',
						title: '采购类型',
						width:90,
						hide:true,
						align:'center',
						templet:function(row){
							var json = {'0':'销售采购','1':'固定资产'};
							return json[row['ORDER_TYPE']];
						}
					},{
						field:'TOTAL_PRICE',
						width:90,
						title:'订单金额'
					},{
					    field: 'LAST_FOLLOW_LABEL',
						title: '最新状态',
						width:90,
						hide:true,
						align:'center'
					},{
					    field: 'LAST_FOLLOW_CONTENT',
						title: '跟进内容',
						hide:true,
						align:'center'
					},{
					    field: 'LAST_FOLLOW_TIME',
						title: '跟进时间',
						hide:true,
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '操作时间',
						align:'center',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'CREATE_USER_NAME',
						title: '制单人',
						align:'center',
						width:70
					},{
					    field: 'REVIEW_ID',
						title: '关联流程',
						minWidth:90,
						align:'center',
						hide:true,
						templet:function(row){
							var v1 = row['REVIEW_ID'];
							var v2 = row['PAYMENT_ID'];
							var array = [];
							if(v1){
								array.push('<a href="javascript:;" lay-event="reviewDetail">合同评审</a>');
							}
							if(v2){
								array.push('<a href="javascript:;" lay-event="paymentDetail">付款申请</a>');
							}
							return array.join(' ');
						}
					}
				]],done:function(){
					table.on('radio(dataMgrTable)',function(obj){
						
					});
					$('tbody tr').first().click();
					
			  }});
			},
			detail:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
				var data = {};
				if(isArray(dataList)){
					data = dataList[0];
				}else{
					data = dataList;
				}
				var orderId = data['ORDER_ID'];
				popup.openTab({id:'orderDetail',title:'采购详情',url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{orderId:orderId,reviewId:data['REVIEW_ID'],custId:data.CUST_ID,applyBy:data['CREATE_USER_ID']}});
			},
			supplierDetail:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    var data = dataList[0];
				var id = data['SUPPLIER_ID'];
				popup.openTab({id:'supplierDetail',url:'${ctxPath}/pages/erp/supplier/supplier-detail.jsp',title:'供应商详情',data:{supplierId:id}});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable',jumpOne:true});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['80%','100%'],data:{orderId:''},url:'${ctxPath}/pages/erp/order/order-add.jsp',title:'新建采购申请'});
			},
			contractDetail:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    var data = dataList[0];
				popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/pages/crm/contract/contract-detail.jsp',data:{contractId:data.CONTRACT_ID,custId:data['CUST_ID']}});
			},
			reviewApply:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    var data = dataList[0];
			    var orderId = data['ORDER_ID'];
			    var orderNo = data['ORDER_NO'];
			    popup.openTab({id:'orderReviewApply',url:'/yq-work/web/flow',title:'采购合同评审申请',data:{flowCode:getFlowCode(data,'order_review'),handle:'add',orderId:orderId,orderNo:orderNo}});
			},
			payApply:function(dataList){
				 if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    var data = dataList[0];
			    var orderId = data['ORDER_ID'];
			    var supplierId = data['SUPPLIER_ID'];
			    var supplierName = data['SUPPLIER_NAME'];
			    popup.openTab({id:'order_payment',url:'/yq-work/web/flow',title:'采购付款申请',data:{flowCode:getFlowCode(data,'order_payment'),handle:'add',orderId:orderId,supplierId:supplierId,supplierName:supplierName}});
			},
			getGoods:function(dataList){
				 if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    var data = dataList[0];
			    var orderId = data['ORDER_ID'];
			    popup.openTab({id:'order_payment',url:'/yq-work/web/flow',title:'采购到货确认申请',data:{flowCode:getFlowCode(data,'order_getgoods'),handle:'add',orderId:orderId}});
			},
			getwl:function(dataList){
				 if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    var data = dataList[0];
			    var orderId = data['ORDER_ID'];
			    popup.openTab({id:'order_payment',url:'/yq-work/web/flow',title:'零星物料领用申请',data:{flowCode:getFlowCode(data,'order_getwl'),handle:'add',orderId:orderId}});
			},
			refreshData:function(){
				 location.reload();
			},
			orderEdit:function(dataList){
				 if(dataList.length == 0){
					 layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					 return;
				 }
			     var data = dataList[0];
			     var orderId = data['ORDER_ID'];
				 popup.layerShow({id:'editOrder',scrollbar:false,title:'修改',offset:'r',area:['70%','100%'],url:'${ctxPath}/pages/erp/order/order-add.jsp',data:{orderId:orderId}});
			}
		}
		
		function getFlowCode(data,flowCode){
			var orderOrg = data['ORDER_ORG'];
			if(orderOrg=='中融'){
		    	flowCode = flowCode+"_zr";
		    }
			return flowCode;
		}

		function stopOrder(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    var orderId = data['ORDER_ID'];
			popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'终止采购',data:{orderId:orderId,followState:90}});
		}
		
		function cannelOrder(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    var orderId = data['ORDER_ID'];
			layer.confirm('确认操作吗,数据将会被重置',{icon:3,offset:'30px'},function(index){
				layer.close(index);
				popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'取消采购',data:{orderId:orderId,followState:99}});
			});
		}
		
		function reviewDetail(data){
			  var orderId = data['ORDER_ID'];
			  var reviewId = data['REVIEW_ID'];
			  popup.openTab({id:'order_review',url:'/yq-work/web/flow?flowCode=order_review',title:'采购评审',data:{handle:'detail',orderId:orderId,reviewId:reviewId}});
		}
		
		function paymentDetail(data){
			var orderId = data['ORDER_ID'];
			var paymentId = data['PAYMENT_ID'];
			popup.openTab({id:'order_payment',url:'/yq-work/web/flow?flowCode=order_payment',title:'采购付款申请',data:{handle:'detail',orderId:orderId,paymentId:paymentId}});
		}
		
		
		function reloadDataList(){
			DataMgr.query();
		}
		$(function(){
			$("#dataMgrForm").render({success:function(){
				DataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>