package com.yunqu.work.servlet.crm;

import java.sql.SQLException;
import java.util.List;
import java.util.Set;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/contract/review")
public class ReviewServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	public EasyResult actionForDelNode() {
		JSONObject params = getJSONObject();
		String nodeId = params.getString("nodeId");
		try {
			this.getQuery().executeUpdate("delete from yq_contract_review_node_conf where node_id = ? ", nodeId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForFinishReview() {
		JSONObject params = getJSONObject();
		String reviewId = params.getString("reviewId");
		try {
			this.getQuery().executeUpdate("update yq_contract_review set review_state = 20,finish_time = ? where review_id = ? ",EasyDate.getCurrentDateString(),reviewId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddFollow() {
		EasyRecord record = new EasyRecord("yq_contract_review_follow","follow_id");
		JSONObject object =  getJSONObject();
		try {
			record.setPrimaryValues(RandomKit.uniqueStr());
			record.set("review_id", object.getString("reviewId"));
			record.set("result_id", object.getString("resultId"));
			record.set("follow_content", object.getString("comment"));
			record.set("add_time", EasyDate.getCurrentDateString());
			record.set("add_user_id", getUserId());
			record.set("add_user_name", getUserName());
			this.getQuery().save(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForAddNodeConf(){
		String id = RandomKit.uniqueStr();
		EasyRecord model = new EasyRecord("yq_contract_review_node_conf","NODE_ID");
		model.setColumns(getJSONObject("conf"));
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		model.remove("NODE_ID");
//		model.setPrimaryValues(RandomKit.uniqueStr());
		try {
			this.getQuery().save(model);
			return EasyResult.ok(id);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	public EasyResult actionForUpdateNodeConf(){
		String id = RandomKit.uniqueStr();
		EasyRecord model = new EasyRecord("yq_contract_review_node_conf","node_id");
		model.setColumns(getJSONObject("conf"));
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		try {
			this.getQuery().update(model);
			return EasyResult.ok(id);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_contract_review","review_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	
	public EasyResult actionForAdd(){
		JSONObject params = getJSONObject();
		String id = params.getString("id");
		EasyRecord model = getModel("review");
		model.set("create_time", EasyDate.getCurrentDateString());
		model.set("update_time", EasyDate.getCurrentDateString());
		model.set("create_user_id", getUserId());
		model.set("create_user_name", getUserName());
		model.set("review_id",id );
		
		int reviewState = model.getIntValue("REVIEW_STATE");
		StringBuffer nodeIdBuff = new StringBuffer();
		try {
			this.getQuery().save(model);
			Set<String> set = params.keySet();
			for(String name:set) {
				if(name.startsWith("node_")) {
					Object object = params.get(name);
					String nodeId = null;
					if(object instanceof JSONArray) {
						 nodeId = params.getJSONArray(name).getString(0);
					}else {
						 nodeId = params.getString(name);
					}
					nodeIdBuff.append(nodeId).append(",");
				}
			}
			String _nodeIds = nodeIdBuff.substring(0, nodeIdBuff.length()-1);
			
			EasySQL _sql = new EasySQL("select * from yq_contract_review_node_conf where 1=1 and (first_check_ids<>'' or last_check_ids<>'')");
			_sql.appendIn(_nodeIds.split(","),"and node_id");
			List<JSONObject> list = this.getQuery().queryForList(_sql.getSQL(),_sql.getParams(), new JSONMapperImpl());
			for(JSONObject object:list) {
				String nodeId = object.getString("NODE_ID");
				EasyRecord record = new EasyRecord("yq_contract_review_result");
				String resultId = "0";
				String lastCheckIds = object.getString("LAST_CHECK_IDS");
				String firstCheckIds = object.getString("FIRST_CHECK_IDS");
				if(StringUtils.notBlank(lastCheckIds)) {
					resultId = RandomKit.uniqueStr();
					record.set("result_id",resultId);
					record.set("review_id",id);
					record.set("node_type",1);
					record.set("node_id",nodeId);
					if(StringUtils.isBlank(firstCheckIds)) {
						record.set("get_time",EasyDate.getCurrentDateString());
					}
					record.set("to_user_id",lastCheckIds);
					record.set("to_user_name",object.getString("LAST_CHECK_NAMES"));
					this.getQuery().save(record);
				}
				if(StringUtils.notBlank(firstCheckIds)) {
					record.set("result_id",RandomKit.uniqueStr());
					record.set("next_result_id",resultId);
					record.set("node_type",0);
					record.set("get_time",EasyDate.getCurrentDateString());
					record.set("to_user_id",firstCheckIds);
					record.set("to_user_name",object.getString("FIRST_CHECK_NAMES"));
					this.getQuery().save(record);
				}
				
			}
			
			if(reviewState>0) {
				//通知所有初审人去评审
				String[] nodeIdsArray = _nodeIds.split(",");
				EasySQL sql = new EasySQL("select group_concat(first_check_ids) ids from yq_contract_review_node_conf where first_check_ids<>''");
				sql.appendIn(nodeIdsArray,"and node_id");
				String firstCheckUserIds = this.getQuery().queryForString(sql.getSQL(),sql.getParams());
				
				sql = new EasySQL("select group_concat(last_check_ids) ids from yq_contract_review_node_conf where first_check_ids = '' and last_check_ids<>''");
				sql.appendIn(nodeIdsArray,"and node_id");
				String lastCheckUserIds = this.getQuery().queryForString(sql.getSQL(),sql.getParams());
			}
			//发邮件和微信推送
			
			return EasyResult.ok(id);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("review");
		JSONObject params = getJSONObject();
		try {
			String id = model.getString("REVIEW_ID");
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
			
			this.getQuery().executeUpdate("update yq_contract_review_result set node_state = 1  where review_id = ?",id);//先禁用
			
			StringBuffer nodeIdBuff = new StringBuffer();
			StringBuffer newNodeIdBuff = new StringBuffer();
			Set<String> set = params.keySet();
			for(String name:set) {
				if(name.startsWith("node_")) {
					Object object = params.get(name);
					String nodeId = null;
					if(object instanceof JSONArray) {
						 nodeId = params.getJSONArray(name).getString(0);
					}else {
						 nodeId = params.getString(name);
					}
					nodeIdBuff.append(nodeId).append(",");
					
					boolean  bl = this.getQuery().queryForExist("select count(1) from yq_contract_review_result where review_id = ? and node_id = ?",id,nodeId);
					if(!bl) {
						newNodeIdBuff.append(nodeId).append(",");
					}
				}
			}
			String _nodeIds = nodeIdBuff.substring(0, nodeIdBuff.length()-1);
			if(newNodeIdBuff.length()>0) {
				String _newNodeIds = newNodeIdBuff.substring(0, newNodeIdBuff.length()-1);
				EasySQL _sql = new EasySQL("select * from yq_contract_review_node_conf where 1=1 and (first_check_ids<>'' or last_check_ids<>'')");
				_sql.appendIn(_newNodeIds.split(","),"and node_id");
				List<JSONObject> list = this.getQuery().queryForList(_sql.getSQL(),_sql.getParams(), new JSONMapperImpl());
				for(JSONObject object:list) {
					String nodeId = object.getString("NODE_ID");
					EasyRecord record = new EasyRecord("yq_contract_review_result");
					String resultId = "0";
					String lastCheckIds = object.getString("LAST_CHECK_IDS");
					String firstCheckIds = object.getString("FIRST_CHECK_IDS");
					if(StringUtils.notBlank(lastCheckIds)) {
						resultId = RandomKit.uniqueStr();
						record.set("result_id",resultId);
						record.set("review_id",id);
						record.set("node_type",1);
						record.set("node_id",nodeId);
						if(StringUtils.isBlank(firstCheckIds)) {
							record.set("get_time",EasyDate.getCurrentDateString());
						}
						record.set("to_user_id",lastCheckIds);
						record.set("to_user_name",object.getString("LAST_CHECK_NAMES"));
						this.getQuery().save(record);
					}
					if(StringUtils.notBlank(firstCheckIds)) {
						record.set("result_id",RandomKit.uniqueStr());
						record.set("next_result_id",resultId);
						record.set("node_type",0);
						record.set("get_time",EasyDate.getCurrentDateString());
						record.set("to_user_id",firstCheckIds);
						record.set("to_user_name",object.getString("FIRST_CHECK_NAMES"));
						this.getQuery().save(record);
					}
					
				}
			}
			
			EasySQL sql = new EasySQL("update yq_contract_review_result set node_state = 0 where 1=1");
			sql.appendIn(_nodeIds.split(","),"and node_id");
			
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddResult(){
		EasyRecord record = new EasyRecord("yq_contract_review_result","result_id");
		try {
			JSONObject params = getJSONObject();
			String resultId = params.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("评审失败,请联系管理员");
			}
			record.setPrimaryValues(resultId);
			record.set("do_result", params.getString("doResult"));
			record.set("comment", params.getString("comment"));
			record.set("do_time", EasyDate.getCurrentDateString());
			record.set("do_user_id", getUserId());
			record.set("do_user_name", getUserName());
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
		
	}
	
	
	public EasyResult actionForDel(){
		try {
			this.getQuery().executeUpdate("delete from yq_contract_review where review_id = ?", getJsonPara("reviewId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	
	
}
