package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;

public class OAUpdateContractIdService extends AppBaseService{
	
	private Logger logger = LogEngine.getLogger(Constants.APP_NAME);
	
	private static class Holder{
		private static OAUpdateContractIdService service=new OAUpdateContractIdService();
	}
	public static OAUpdateContractIdService getService(){
		return Holder.service;
	}
	public String synData(){
        try {
          StringBuffer msg = new StringBuffer();
          // OAService.getService().login("100133", "123456");
           OAService.getService().login("100078","10304603");
           String xml = OAService.getService().loadContractXml("100078");
           if(xml==null){
        	   return null;
           }
            xml = xml.replaceAll("\n", "");
            xml = xml.replaceAll("\t", "");
			Document doc = DocumentHelper.parseText(xml);
			Element root = doc.getRootElement();
			@SuppressWarnings("unchecked")
			List<Element> childElements  = root.elements("Item");
			for (Element child : childElements) {
				 Element attr=child.element("ItemAttributes");
				 if(attr!=null){
					 String id=child.elementText("DocUNID");
					 String subject = attr.elementText("Subject");
					 if(subject.indexOf(" ")==-1){
						 return null;
					 }
					 String[] array = subject.split(" ");
					 String accCode = StringUtils.trimToEmpty(array[0]);
					try {
						JSONObject contractObj=this.getQuery().queryForRow("select * from YQ_PROJECT_CONTRACT where CONTRACT_ID = ?",new String[]{id}, new JSONMapperImpl());
						if(contractObj==null||contractObj.size()==0){
							try {
								this.getQuery().executeUpdate("update YQ_PROJECT_CONTRACT set CONTRACT_ID = ? where CONTRACT_NO = ?", id,accCode);
							} catch (SQLException e) {
								logger.error(e.getMessage(),e);
							}
						}
					} catch (SQLException e) {
						logger.error(null,e);
					}
				 }
			 }
			return msg.toString();
		} catch (DocumentException e) {
			logger.error(e.getMessage(),e);
		}
		return null;
	}
	
}







