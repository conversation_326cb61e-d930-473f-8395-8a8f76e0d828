package com.yunqu.work.servlet.work;

import java.io.File;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;

import org.apache.poi.ss.usermodel.IndexedColors;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.CommentModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.model.TaskModel;
import com.yunqu.work.service.CommentService;
import com.yunqu.work.service.CommonService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.OpLogService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.TaskNoticeService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.TaskUtils;
import com.yunqu.work.utils.WebSocketUtils;
@WebServlet("/servlet/task/*")
public class TaskServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private static JSONObject taskNames=new JSONObject();
	static{
		taskNames.put("10", "待办");
		taskNames.put("11", "已驳回");
		taskNames.put("20", "进行中");
		taskNames.put("30", "已完成");
		taskNames.put("40", "已验收");
		taskNames.put("42", "验收不通过");
	}
	public EasyResult actionForBatchAdd(){
		JSONObject params = getJSONObject();
		JSONArray ids = params.getJSONArray("ids");
		if(ids==null||ids.size()==0){
			return EasyResult.fail();
		}
		String projectName=params.getString("projectName");
		for(int j=0;j<ids.size();j++){
			String id = ids.getString(j);
			TaskModel model=getModel(TaskModel.class, "task");
			model.set("PROJ_NAME", projectName);
			model.setPrimaryValues(RandomKit.uuid());
			String assignUserId =  params.getString("assignUserId_"+id);
			String title = params.getString("title_"+id);
			String taskDesc = params.getString("desc_"+id);
			if(StringUtils.isBlank(assignUserId)||StringUtils.isBlank(title)){
				continue;
			}
			model.set("DEPT_NAME",getDeptName());
			model.set("TASK_TYPE_ID", params.getString("typeId_"+id));
			model.set("TASK_LEVEL", params.getString("level_"+id));
			model.set("MODULE_ID", params.getString("moduleId_"+id));
			model.set("MODULE_NAME", params.getString("module_name_"+id));
			model.set("PLAN_STARTED_AT", params.getString("start_time_"+id));
			model.set("DEADLINE_AT", params.getString("end_time_"+id));
			model.set("ASSIGN_USER_ID",assignUserId);
			model.set("ASSIGN_DEPT_ID",StaffService.getService().getStaffInfo(assignUserId).getDeptId());
			model.set("ASSIGN_USER_NAME",StaffService.getService().getUserName(assignUserId));
			model.set("ASSIGN_DEPT_NAME",StaffService.getService().getUserDeptName(assignUserId));
			model.set("TASK_DESC", taskDesc);
			model.set("TASK_NAME", title);
			model.addCreateTime();
			model.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
			model.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			model.setCreator(getUserId());
			model.set("CREATE_NAME", getUserName());
			model.set("DEPT_ID", getDeptId());
			model.set("TASK_STATE", 10);
			model.set("UPDATE_BY", getUserId());
			String[] cc=null;
			try {
				JSONArray array=model.getJSONArray("NOTICE_TYPES");
				JSONObject jsonObject=getJSONObject();
				JSONArray mailtos=jsonObject.getJSONArray("mailtos");
				model.remove("NOTICE_TYPES");
				model.put("NOTICE_TYPES",StringUtils.join(array.toArray(), ","));
				
				if(mailtos!=null){
					cc=new String[mailtos.size()];
					for(int i=0;i<mailtos.size();i++){
						String userId=mailtos.getString(i);
						cc[i]=userId;
						EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
						record.setPrimaryValues(model.getTaskId(),userId);
						record.set("CREATOR", getUserId());
						record.set("CREATE_TIME", EasyDate.getCurrentDateString());
						try {
							this.getQuery().save(record);
						} catch (Exception ex) {
							this.error(ex.getMessage(), ex);
						}
						MessageModel messageModel=new MessageModel();
						messageModel.setReceiver(userId);
						messageModel.setTypeName("task");
						messageModel.setContents("抄送|"+model.getTaskName());
						messageModel.setFkId(model.getTaskId());
						messageModel.setSender(getUserId());
						messageModel.setSendName(getUserName());
						messageModel.setMsgState(1);
						messageModel.setMsgType(2001);
						messageModel.setSenderType(0);
						messageModel.setDesc(model.getString("TASK_DESC"));
						messageModel.setData(model);
						messageModel.setTplName("newTask.html");
						try {
							WebSocketUtils.sendMessage(new MsgModel(userId,"新的抄送任务", model.getTaskName()));
							MessageService.getService().sendMsg(messageModel);
							if(array.contains("3")){
								//EmailService.getService().sendEmail(messageModel);
							}
							messageModel.setDesc(model.getString("TASK_DESC"));
							if(array.contains("4")){
								WxMsgService.getService().sendNewTaskMsg(messageModel);
							}
						} catch (Exception e) {
							this.error(e.getMessage(), e);
						}
					}
				}
				MessageModel messageModel=new MessageModel();
				messageModel.setReceiver(model.getString("ASSIGN_USER_ID"));
				messageModel.setContents(model.getTaskName());
				messageModel.setFkId(model.getTaskId());
				messageModel.setSender(getUserId());
				messageModel.setTypeName("task");
				messageModel.setSendName(getUserName());
				messageModel.setMsgState(1);
				messageModel.setMsgType(2001);
				messageModel.setSenderType(0);
				messageModel.setCc(cc);
				messageModel.setData(model);
				messageModel.setTplName("newTask.html");
				
				model.save();
				
				try {
					WebSocketUtils.sendMessage(new MsgModel(assignUserId,"新的待办任务", model.getTaskName()));
					MessageService.getService().sendMsg(messageModel);
					if(array.contains("3")){
						EmailService.getService().sendTaskEmail(messageModel);
					}
					messageModel.setDesc(model.getString("TASK_DESC"));
					if(array.contains("4")){
						WxMsgService.getService().sendNewTaskMsg(messageModel);
					}
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			
				OpLogService.getService().addLog(getUserId(), model.getTaskId(),"10","新增任务",getRequestUrl());
				String projectId=model.getString("PROJECT_ID");
				if(StringUtils.notBlank(projectId)){
					this.getQuery().executeUpdate("update YQ_PROJECT set UPDATE_TIME = ? where project_id = ?", EasyDate.getCurrentDateString(),projectId);
					this.getQuery().executeUpdate("update yq_project t1 set t1.task_count = (select count(1) from yq_task t3 where t3.PROJECT_ID= t1.PROJECT_ID) where t1.PROJECT_ID = ?",projectId);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok(null,"创建成功.");
	}
	
	public EasyResult actionForAdd(){
		TaskModel model=getModel(TaskModel.class, "task");
		model.addCreateTime();
		model.set("DEPT_NAME",getDeptName());
		model.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
		model.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.setCreator(getUserId());
		model.set("DEPT_ID", getDeptId());
		model.set("TASK_STATE", 10);
		model.set("CREATE_NAME", getUserName());
		model.set("UPDATE_BY", getUserId());
		String  assignUserId = model.getString("ASSIGN_USER_ID");
		model.set("ASSIGN_DEPT_ID",StaffService.getService().getStaffInfo(assignUserId).getDeptId());
		model.set("ASSIGN_USER_NAME",StaffService.getService().getUserName(assignUserId));
		model.set("ASSIGN_DEPT_NAME",StaffService.getService().getUserDeptName(assignUserId));
		String[] cc=null;
		try {
			JSONArray array=model.getJSONArray("NOTICE_TYPES");
			JSONObject jsonObject=getJSONObject();
			JSONArray mailtos=jsonObject.getJSONArray("mailtos");
			model.remove("NOTICE_TYPES");
			model.put("NOTICE_TYPES",StringUtils.join(array.toArray(), ","));//3 邮件  4 微信
			
			if(mailtos!=null){
				cc=new String[mailtos.size()];
				for(int i=0;i<mailtos.size();i++){
					String userId=mailtos.getString(i);
					cc[i]=userId;
					EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
					record.setPrimaryValues(model.getTaskId(),userId);
					record.set("CREATOR", getUserId());
					record.set("CREATE_TIME", EasyDate.getCurrentDateString());
					try {
						this.getQuery().save(record);
					} catch (Exception ex) {
						this.error(ex.getMessage(), ex);
					}
					if(ServerContext.isLoginAuth()){
						String desc=model.getString("TASK_DESC");
						if(StringUtils.notBlank(desc)){
							desc=desc.replaceAll("http://work.yunqu-info.cn","");
							desc=desc.replaceAll("/files", "http://work.yunqu-info.cn/files");
							model.set("TASK_DESC", desc);
						}
					}
					MessageModel messageModel=new MessageModel();
					messageModel.setReceiver(userId);
					messageModel.setTypeName("task");
					messageModel.setContents("抄送|"+model.getTaskName());
					messageModel.setFkId(model.getTaskId());
					messageModel.setSender(getUserId());
					messageModel.setSendName(getUserName());
					messageModel.setMsgState(1);
					messageModel.setMsgType(2001);
					messageModel.setSenderType(2);
					messageModel.setDesc(model.getString("TASK_DESC"));
					messageModel.setData(model);
					messageModel.setTplName("newTask.html");
					try {
						WebSocketUtils.sendMessage(new MsgModel(userId,"新的抄送任务", model.getTaskName()));
						MessageService.getService().sendMsg(messageModel);
						if(array.contains("3")){
							//EmailService.getService().sendEmail(messageModel);
						}
						messageModel.setDesc(model.getString("TASK_DESC"));
						if(array.contains("4")){
							WxMsgService.getService().sendNewTaskMsg(messageModel);
						}
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			MessageModel messageModel=new MessageModel();
			messageModel.setReceiver(assignUserId);
			messageModel.setContents(model.getTaskName());
			messageModel.setFkId(model.getTaskId());
			messageModel.setSender(getUserId());
			messageModel.setTypeName("task");
			messageModel.setSendName(getUserName());
			messageModel.setMsgState(1);
			messageModel.setMsgType(2001);
			messageModel.setSenderType(2);
			messageModel.setFileList(CommonService.getService().getFileList(model.getTaskId()));
			if(ServerContext.isLoginAuth()){
				String desc=model.getString("TASK_DESC");
				if(StringUtils.notBlank(desc)){
					desc=desc.replaceAll("http://work.yunqu-info.cn","");
					desc=desc.replaceAll("/files", "http://work.yunqu-info.cn/files");
					model.set("TASK_DESC", desc);
				}
			}
			messageModel.setCc(cc);
			messageModel.setData(model);
			messageModel.setTplName("newTask.html");
			
			String desc=model.getString("TASK_DESC")+"";
			desc=desc.replaceAll("http://work.yunqu-info.cn/files", "/files");
			model.set("TASK_DESC", desc);
			model.remove("PROJECT_NAME");
			model.save();
			try {
				WebSocketUtils.sendMessage(new MsgModel(assignUserId,"新的待办任务", model.getTaskName()));
				MessageService.getService().sendMsg(messageModel);
				if(array.contains("3")){
					EmailService.getService().sendTaskEmail(messageModel);
				}
				messageModel.setDesc(model.getString("TASK_DESC"));
				if(array.contains("4")){
					WxMsgService.getService().sendNewTaskMsg(messageModel);
				}
			} catch (Exception e) {
				this.error(e.getMessage(), e);
			}
			
			OpLogService.getService().addLog(getUserId(), model.getTaskId(),"10","新增任务",getRequestUrl());
			String projectId=model.getString("PROJECT_ID");
			if(StringUtils.notBlank(projectId)){
				this.getQuery().executeUpdate("update yq_project set update_time = ? where project_id = ?", EasyDate.getCurrentDateString(),projectId);
				this.getQuery().executeUpdate("update yq_project t1 set t1.task_count = (select count(1) from yq_task t3 where t3.PROJECT_ID= t1.PROJECT_ID) where t1.PROJECT_ID = ?",projectId);
			}
			
			this.getQuery().executeUpdate("update yq_task t1 set t1.file_count = (select count(1) from yq_files t2 where t2.FK_ID=t1.task_id) where t1.task_id= ?",model.getTaskId());
			
//			this.getQuery().executeUpdate("update yq_task t1,"+Constants.DS_MAIN_NAME+".easi_dept t2 ,"+Constants.DS_MAIN_NAME+".easi_dept_user t3 set t1.assign_dept_id=t2.DEPT_ID where t1.assign_user_id=t3.USER_ID and t2.DEPT_ID=t3.DEPT_ID and t1.assign_user_id=t3.USER_ID and t1.TASK_ID = ?",model.getTaskId());
//			this.getQuery().executeUpdate("update yq_task t1,"+Constants.DS_MAIN_NAME+".easi_user t2 set t1.assign_user_name = t2.USERNAME where t1.assign_user_id = t2.USER_ID and t1.task_id = ?",model.getTaskId());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"创建成功.");
	}
	public EasyResult actionForAddCC(){
		JSONObject jsonObject=getJSONObject();
		String taskId=jsonObject.getString("taskId");
		JSONArray mailtos=jsonObject.getJSONArray("mailtos");
		if(mailtos!=null){
			for(int i=0;i<mailtos.size();i++){
				String userId=mailtos.getString(i);
				EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
				record.setPrimaryValues(taskId,userId);
				record.set("CREATOR", getUserId());
				record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				try {
					WebSocketUtils.sendMessage(new MsgModel(userId,"新的抄送任务", "请点击我参与的任务查看."));
					this.getQuery().save(record);
				} catch (Exception ex) {
					this.error(ex.getMessage(), ex);
				}
			}
		}else{
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateState(){
		JSONObject jsonObject=getJSONObject();
		TaskModel model=getModel(TaskModel.class, "task");
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		int taskState=model.getTaskState();
		Integer oldTaskState=jsonObject.getIntValue("TASK_STATE");
		String assignUserId=jsonObject.getString("ASSIGN_USER_ID");//负责人
		String creator=jsonObject.getString("CREATOR");//发起人
		String taskName=jsonObject.getString("TASK_NAME");
		if(oldTaskState==taskState&&oldTaskState!=42){
			return EasyResult.fail("状态没更改!");
		}
		try {
			MessageModel messageModel=new MessageModel();
			messageModel.setFkId(model.getTaskId());
			messageModel.setTypeName("task");
			messageModel.setSender(getUserId());
			messageModel.setMsgState(1);
			messageModel.setMsgType(2001);
			messageModel.setSenderType(2);
			messageModel.setSendName(getUserName());
			messageModel.setData(model);
			messageModel.setTitle(jsonObject.getString("TASK_NAME"));
			//任务完成
			if(taskState==30){
				model.set("WORK_HOUR", jsonObject.getString("workHour"));
				model.set("FINISH_TIME", EasyDate.getCurrentDateString());
				model.set("PROGRESS", "100");
				model.set("FINISH_DATE_ID", EasyCalendar.newInstance().getDateInt());
				messageModel.setTplName("finishedTask.html");
				messageModel.setContents(taskName);
				messageModel.setReceiver(creator);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
					WxMsgService.getService().sendFinishTaskMsg(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			//校验完毕
			if(taskState==40){
				messageModel.setTplName("checkdTask.html");
				model.set("CHECK_TIME", EasyDate.getCurrentDateString());
				messageModel.setReceiver(assignUserId);
				messageModel.setContents(taskName);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			//校验不通过
			if(taskState==42){
				String desc=jsonObject.getString("comment");
				messageModel.setTplName("failedcheckdTask.html");
				messageModel.setReceiver(assignUserId);
				messageModel.setContents(taskName);
				model.set("TASK_DESC", desc);
				messageModel.setData(model);
				try {
					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
			}
			model.remove("NOTICE_TYPES");
			model.remove("TASK_DESC");
			model.update();
			String msgTitle="修改任务进度["+taskNames.getString(String.valueOf(taskState))+"]";
			OpLogService.getService().addLog(getUserId(), model.getTaskId(),taskState+"",msgTitle,getRequestUrl());
		
			MsgModel msgModel=new MsgModel(creator,messageModel.getTitle(),msgTitle); 
			WebSocketUtils.sendMessage(msgModel);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"修改成功.");
	}
	/**
	 * 转派
	 * @return
	 */
	public EasyResult actionForToOther(){
		TaskModel model=getModel(TaskModel.class, "task");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			model.set("UPDATE_BY", getUserId());
			
			String[] cc=null;
			JSONObject jsonObject=getJSONObject();
			JSONArray mailtos=jsonObject.getJSONArray("mailtos");
			if(mailtos!=null&&mailtos.size()>0){
				cc=new String[mailtos.size()];
				for(int i=0;i<mailtos.size();i++){
					String userId=mailtos.getString(i);
					try {
						EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
						record.setPrimaryValues(model.getTaskId(),userId);
						record.set("CREATOR", getUserId());
						record.set("CREATE_TIME", EasyDate.getCurrentDateString());
						this.getQuery().save(record);
						cc[i]=userId;
					} catch (Exception e) {
						this.error(e.getMessage(), e);
					}
				}
			}
			String otherUserId=getJsonPara("otherUserId");
			if(StringUtils.notBlank(otherUserId)){
				model.set("ASSIGN_USER_ID", otherUserId);
				model.set("ASSIGN_USER_NAME", StaffService.getService().getUserName(otherUserId));
				model.set("ASSIGN_DEPT_NAME",StaffService.getService().getUserDeptName(otherUserId));
				model.update();
				
				JSONObject task = this.getQuery().queryForRow("select * from yq_task where task_id = ?", new Object[]{model.getTaskId()},new JSONMapperImpl());
				model.setColumns(task);
				
				String comment="转派给"+getJsonPara("toOtherName");
				String userName=getJsonPara("userName");
				if(StringUtils.notBlank(comment)){
					CommentService.getService().addComment(getUserId(), getUserName(),model.getTaskId(), comment, WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"));
				}
				MessageModel messageModel=new MessageModel();
				messageModel.setTypeName("task");
				messageModel.setReceiver(otherUserId);
				messageModel.setTitle(userName+"转派|"+model.getTaskName());
				messageModel.setContents(model.getTaskName());
				messageModel.setFkId(model.getTaskId());
				messageModel.setSender(getUserId());
				messageModel.setSendName(getUserName());
				messageModel.setMsgState(1);
				messageModel.setMsgType(2001);
				messageModel.setSenderType(2);
				if(ServerContext.isLoginAuth()){
					String desc=model.getString("TASK_DESC");
					if(StringUtils.notBlank(desc)){
						desc=desc.replaceAll("/files", "http://work.yunqu-info.cn/files");
						model.set("TASK_DESC", desc);
					}
				}
				messageModel.setCc(cc);
				messageModel.setData(model);
				messageModel.setTplName("newTask.html");
				try {
					this.getQuery().executeUpdate("update yq_task t1,"+Constants.DS_MAIN_NAME+".easi_dept t2 ,"+Constants.DS_MAIN_NAME+".easi_dept_user t3 set t1.assign_dept_id=t2.DEPT_ID where t1.assign_user_id=t3.USER_ID and t2.DEPT_ID=t3.DEPT_ID and t1.assign_user_id=t3.USER_ID and t1.TASK_ID = ?",model.getTaskId());

					MessageService.getService().sendMsg(messageModel);
					EmailService.getService().sendTaskEmail(messageModel);
					messageModel.setDesc(model.getString("TASK_DESC"));
					WxMsgService.getService().sendNewTaskMsg(messageModel);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
				}
				
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"转派成功.");
	}
	public EasyResult actionForUpdate(){
		TaskModel model=getModel(TaskModel.class, "task");
		model.set("update_time", EasyDate.getCurrentDateString());
		model.set("update_by", getUserId());

		String assignUserId = model.getString("ASSIGN_USER_ID");
		if(StringUtils.notBlank(assignUserId)) {
			model.set("ASSIGN_USER_NAME",StaffService.getService().getUserName(assignUserId));
			model.set("ASSIGN_DEPT_ID",StaffService.getService().getStaffInfo(assignUserId).getDeptId());
			model.set("ASSIGN_DEPT_NAME",StaffService.getService().getUserDeptName(assignUserId));
		}
		
		try {
			JSONArray array=model.getJSONArray("NOTICE_TYPES");
			model.remove("NOTICE_TYPES");
			if(array!=null&&array.size()>0){
				model.put("NOTICE_TYPES",StringUtils.join(array.toArray(), ","));
			}
			JSONObject jsonObject=getJSONObject();
			JSONArray mailtos=jsonObject.getJSONArray("mailtos");
			if(mailtos!=null&&mailtos.size()>0){
				this.getQuery().executeUpdate("delete from yq_cc where FK_ID = ?", model.getTaskId());
				for(int i=0;i<mailtos.size();i++){
					String usertId=mailtos.getString(i);
					EasyRecord record=new EasyRecord("YQ_CC", "FK_ID","USER_ID");
					record.setPrimaryValues(model.getTaskId(),usertId);
					record.set("CREATOR", getUserId());
					record.set("CREATE_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
				}
			}
			model.update();
			String comment=jsonObject.getString("comment");
			if(StringUtils.notBlank(comment)){
				CommentService.getService().addComment(getUserId(),getUserName(),model.getTaskId(), comment, WebKit.getIP(getRequest()),getRequest().getHeader("user-agent"));
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"修改成功.");
	}
	public EasyResult actionForSaveComment(){
		JSONObject jsonObject=getJSONObject();
		CommentModel model=new CommentModel();
		model.setPrimaryValues(RandomKit.uuid());
		model.setFkId(jsonObject.getString("fkId"));
		model.setContent(jsonObject.getString("content"));
		model.setCreator(getUserId());
		model.setCreateTime(EasyDate.getCurrentDateString());
		model.setUserAgent(getRequest().getHeader("user-agent"));
		model.setIp(WebKit.getIP(getRequest()));
		return CommentService.getService().addComment(model);
	}
	public EasyResult actionForDelayTask(){
		JSONObject jsonObject = getJSONObject();
		String date=jsonObject.getString("date");
		String bdate=jsonObject.getString("bdate");
		String taskId=jsonObject.getString("taskId");
		String reason=jsonObject.getString("reason");
		try {
			this.getQuery().executeUpdate("update yq_task set DEADLINE_AT = ? where task_id = ?", date,taskId);
			
			CommentModel model=new CommentModel();
			model.setFkId(taskId);
			model.setPrimaryValues(RandomKit.uuid());
			model.setContent("修改了任务截止时间,由"+bdate+"改为"+date+"\n"+reason);
			model.setCreator(getUserId());
			model.setCreateTime(EasyDate.getCurrentDateString());
			model.setUserAgent(getRequest().getHeader("user-agent"));
			model.setIp(WebKit.getIP(getRequest()));
		    CommentService.getService().addComment(model);
		    
		    JSONObject task = this.getQuery().queryForRow("select * from yq_task where task_id = ?", new Object[]{taskId},new JSONMapperImpl());
			MessageModel messsage=new MessageModel();
			messsage.setTitle(task.getString("TASK_NAME"));
			messsage.setReceiver(task.getString("CREATOR"));
			messsage.setSender(getUserId());
			messsage.setDesc(model.getContent());
			messsage.setFkId(taskId);
			messsage.setTplName("taskComment.html");
			EmailService.getService().sendTaskCommentEmail(messsage);
			
	    	return 	EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	public EasyResult actionForReturnTask(){
		JSONObject jsonObject = getJSONObject();
		String taskId=jsonObject.getString("taskId");
		String reason=jsonObject.getString("reason");
		try {
			this.getQuery().executeUpdate("update yq_task set TASK_STATE = 11 , DEADLINE_AT = '' where task_id = ?",taskId);
			
			CommentModel model=new CommentModel();
			model.setFkId(taskId);
			model.setPrimaryValues(RandomKit.uuid());
			model.setContent("驳回任务,\n"+reason);
			model.setCreator(getUserId());
			model.setCreateTime(EasyDate.getCurrentDateString());
			model.setUserAgent(getRequest().getHeader("user-agent"));
			model.setIp(WebKit.getIP(getRequest()));
			CommentService.getService().addComment(model);
			
			
			JSONObject task = this.getQuery().queryForRow("select * from yq_task where task_id = ?", new Object[]{taskId},new JSONMapperImpl());
			MessageModel messsage=new MessageModel();
			messsage.setTitle(task.getString("TASK_NAME"));
			messsage.setReceiver(task.getString("CREATOR"));
			messsage.setSender(getUserId());
			messsage.setDesc(model.getContent());
			messsage.setFkId(taskId);
			messsage.setTplName("taskComment.html");
			EmailService.getService().sendTaskCommentEmail(messsage);
			
			
			return 	EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public EasyResult actionForDelTask(){
		String taskId=getJsonPara("taskId");
		try {
			this.getQuery().executeUpdate("delete from  yq_oplog where fk_id = ?", taskId);
			this.getQuery().executeUpdate("delete from  yq_cc where fk_id = ?", taskId);
			this.getQuery().executeUpdate("delete from  yq_look_log where fk_id = ?", taskId);
			this.getQuery().executeUpdate("delete from  yq_files where fk_id = ?", taskId);
			this.getQuery().executeUpdate("delete from  yq_task where task_id = ?", taskId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForCannelShare(){
		String taskId=getJsonPara("TASK_ID");
		try {
			this.getQuery().executeUpdate("update yq_task set task_auth = 1 where task_id = ?", taskId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"取消成功!");
	}
	public EasyResult actionForShare(){
		String taskId=getJsonPara("TASK_ID");
		try {
			this.getQuery().executeUpdate("update yq_task set task_auth = 0 where task_id = ?", taskId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(null,"分享成功!");
	}
	public void actionForTaskDetail(){
		redirect(getRequest().getContextPath()+"/pages/task/task-detail.jsp?isDiv=0&taskId="+getPara("taskId"));
	}
	public EasyResult actionForNoticeTask(){
		JSONObject object=getJSONObject();
		TaskNoticeService.getService().noticeTask(object.getString("taskId"), object.getString("title"), object.getString("content"));
		return EasyResult.ok();
	}
	public EasyResult actionForUpdateProgress(){
		JSONObject object=getJSONObject();
		try {
			this.getQuery().executeUpdate("update yq_task set progress = ? where task_id = ?",object.getString("progress"), object.getString("taskId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForNoticeMsg(){
		String type=getJsonPara("type");
		new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					if("overtimeTask".equals(type)){
						TaskNoticeService.getService().overtimeTask();
					}else if("waitTask".equals(type)){
						TaskNoticeService.getService().waitTask();
					}else if("checkTask".equals(type)){
						TaskNoticeService.getService().checkTask();
					}
				} catch (Exception e) {
					Thread.currentThread().interrupt();
					getLogger().error(e.getMessage(),e);
				}
			}
		}).start();
		return EasyResult.ok();
	}
	private Map<String,String> getUsers() {
		Map<String,String> map=new HashMap<String,String>();
		try {
			List<EasyRow> list=this.getMainQuery().queryForList("select USERNAME,USER_ID FROM EASI_USER");
			if(list!=null){
				for(EasyRow row:list){
					map.put(row.getColumnValue("USER_ID"),row.getColumnValue("USERNAME"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return map;
	}
	private Map<String,String> getTaskType() {
		Map<String,String> map=new HashMap<String,String>();
		try {
			List<EasyRow> list=this.getQuery().queryForList("select task_type_id,task_type_name FROM yq_task_type");
			if(list!=null){
				for(EasyRow row:list){
					map.put(row.getColumnValue("task_type_id"),row.getColumnValue("task_type_name"));
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return map;
	}
	
	 public static Date parseDate(String dateStr, String pattern){
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date date;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            throw  new RuntimeException("日期转化错误");
        }

        return date;
    }
	 
    public long betweenDays(String dateStr1,String dateStr2){
    	if(StringUtils.isBlank(dateStr1)||StringUtils.isBlank(dateStr2)) {
    		return 0;
    	}
        // 获取日期
        Date date1 = parseDate(dateStr1, "yyyy-MM-dd HH:mm");
        Date date2 = parseDate(dateStr2, "yyyy-MM-dd HH:mm");

        // 获取相差的天数
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date1);
        long timeInMillis1 = calendar.getTimeInMillis();
        calendar.setTime(date2);
        long timeInMillis2 = calendar.getTimeInMillis();

        long betweenDays =  (timeInMillis2 - timeInMillis1) / (1000L*3600L*24L);
        return betweenDays;
    }
	  
	  
	public void actionForExportTask(){
		String _data = getPara("data");
		JSONObject param = JSONObject.parseObject(_data);
		Map<String,String> userNames = getUsers();
		Map<String,String> taskTypes = getTaskType();
		List<String> headers=new ArrayList<String>();
		try {
			int status=param.getIntValue("status");
			EasySQL sql=null;
			if(status==1){//我负责的任务
				sql=new EasySQL("select t1.*,t2.PROJECT_NAME from yq_task t1  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
				TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
				sql.append(getUserId(),"and t1.ASSIGN_USER_ID = ?",true);
			}else if(status==2){
				sql=new EasySQL("select t1.*,t2.PROJECT_NAME from yq_task t1  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
				TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
				sql.append(getUserId(),"and t1.CREATOR = ?",true);
			}else if(status==3){
				sql=new EasySQL("select t1.*,t3.create_time cc_time,t2.PROJECT_NAME from yq_task t1 INNER JOIN yq_cc t3 on t3.fk_id=t1.task_id  left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID  where 1=1");
				TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
				sql.append(getUserId(),"and t3.user_id = ?",true);
			}else if(status==4){
				//共享任务
				sql=new EasySQL("select t1.* from yq_task t1  where 1=1");
				sql.append(Constants.TASK_OK,"and t1.task_state >= ? and t1.task_auth = 0");
				TaskUtils.setTaskCondition(param, getDeptId(),sql);
			}else {
				sql = new EasySQL("select t1.*,t2.PROJECT_NAME from YQ_TASK t1 left join YQ_PROJECT t2 on t1.PROJECT_ID =t2.PROJECT_ID where 1=1");
				TaskUtils.setTaskConditionWithState(param, getDeptId(),sql);
			}
			String field=param.getString("field");
			String order=param.getString("order");
			if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
				sql.append("order by").append(field).append(order);
			}else{
				sql.append("order by t1.CREATE_TIME desc");
			}
			
			File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
			/**创建头部*/
			headers.add("录单日期");
			headers.add("标题");
			headers.add("描述");
			headers.add("任务类型");
			headers.add("所属项目");
			headers.add("所属模块");
			headers.add("标签");
			headers.add("等级");
			headers.add("发起人");
			headers.add("负责人");
			headers.add("开始时间");
			headers.add("结束时间");
			headers.add("难度");
			headers.add("标签");
			headers.add("任务时长(天)");
			headers.add("计划耗时(小时)");
			headers.add("实际耗时(小时)");
			headers.add("完成时间");
			headers.add("进度(%)");
			headers.add("状态");
			
			List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
			int x=0;
			for(String header:headers){
				ExcelHeaderStyle style=new ExcelHeaderStyle();
				style.setData(header);
				if(x==1){
					style.setWidth(8000);
				}else if(x==2){
					style.setWidth(12000);
				}else {
					style.setWidth(4500);
				}
				style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
				styles.add(style);
				x++;
			}
			/**数据***/
			List<List<String>> excelData=new ArrayList<List<String>>();
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> data = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			
			if(data!=null && data.size()>0){
				for (int i = 0; i < data.size(); i++) {
					JSONObject map = data.get(i);
					List<String> list=new ArrayList<String>();
					list.add(map.getString("CREATE_TIME"));
					list.add(map.getString("TASK_NAME"));
					list.add(Jsoup.clean(map.getString("TASK_DESC"),Whitelist.none()).replaceAll("&nbsp;"," "));
					list.add(taskTypes.get(map.getString("TASK_TYPE_ID")));
					list.add(map.getString("PROJ_NAME"));
					list.add(map.getString("MODULE_NAME"));
					list.add(map.getString("TASK_TAGS"));
					list.add(map.getString("TASK_LEVEL"));
					list.add(userNames.get(map.getString("CREATOR")));
					list.add(userNames.get(map.getString("ASSIGN_USER_ID")));
					list.add(map.getString("PLAN_STARTED_AT"));
					list.add(map.getString("DEADLINE_AT"));
					list.add(map.getString("TASK_DIFFCULTY"));
					list.add(map.getString("TASK_TAGS"));
					list.add(""+betweenDays(map.getString("PLAN_STARTED_AT"),map.getString("DEADLINE_AT")));
					list.add(map.getString("PLAN_WORK_HOUR"));
					list.add(map.getString("WORK_HOUR"));
					list.add(map.getString("FINISH_TIME"));
					list.add(map.getString("PROGRESS"));
					list.add(taskNames.getString(map.getString("TASK_STATE")));
					
					excelData.add(list);
				}
			}
			
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			String fileName="任务清单列表.xlsx";
			renderFile(file,fileName,true);
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
	}
	
	public JSONObject actionForAddGroup() {
		EasyRecord record = new EasyRecord("yq_task_group","group_id");
		try {
			record.setColumns(getJSONObject("group"));
			record.setPrimaryValues(RandomKit.uniqueStr(10, true));
			record.set("create_time", EasyDate.getCurrentDateString());
			record.set("creator", getUserId());
			this.getQuery().save(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public JSONObject actionForUpdateGroup() {
		EasyRecord record = new EasyRecord("yq_task_group","group_id");
		try {
			record.setColumns(getJSONObject("group"));
			this.getQuery().update(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public JSONObject actionForDelGroup() {
		String groupId = getJsonPara("groupId");
		try {
			this.getQuery().executeUpdate("update yq_task set group_id ='' where group_id = ?", groupId);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public void actionForToDeptTaskStat() {
		EasySQL sql = new EasySQL("select assign_dept_id,assign_dept_name,count(1) count,");
		sql.append("count(distinct(project_id)) as project_num");
		sql.append("from yq_task where 1=1");
		String day = getPara("day");
		if(StringUtils.notBlank(day)) {
			int _day = Integer.valueOf(day)*-1;
			sql.append(DateUtils.getPlanMaxDay(_day),"and date_id>= ?");
		}else {
			sql.append(DateUtils.getPlanMaxDay(-90),"and date_id>= ?");
		}
		sql.append("group by assign_dept_id having count>5 ORDER BY project_num desc");
		try {
			List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			setAttr("list", list);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		renderJsp("/pages/project/project-dept-stat.jsp");
	}
	
	public JSONObject actionForTags() {
		JSONObject result = new JSONObject();
		result.put("code", 200);
		result.put("msg", "");
		
		List<String> list = new ArrayList<String>();
		list.add("接口");
		list.add("报表");
		list.add("表单");
		list.add("其他");
		
		result.put("data",list.toArray());
		
		return result;
	}
}





