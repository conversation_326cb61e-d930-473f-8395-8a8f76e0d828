package com.yunqu.work.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.utils.string.StringUtils;


public class FlowRowMapper implements EasyRowMapper<FlowModel> {

	@SuppressWarnings("unchecked")
	@Override
	public FlowModel mapRow(ResultSet rs, int rowNum) {
		FlowModel vo = new FlowModel();
		try {
			vo.setFlowName(rs.getString("FLOW_NAME"));
			vo.setFlowTitle(rs.getString("FLOW_TITLE"));
			vo.setApplyUrl(rs.getString("APPLY_URL"));
			vo.setDetailUrl(rs.getString("DETAIL_URL"));
			vo.setFlowCode(rs.getString("FLOW_CODE"));
			vo.setFormId(rs.getString("FORM_ID"));
			vo.setItemTableName(rs.getString("ITEM_TABLE"));
			vo.setSourceType(rs.getInt("SOURCE_TYPE"));
			vo.setFlowRemark(rs.getString("FLOW_REMARK"));
			String busiTable = rs.getString("BUSI_TABLE");
			if(StringUtils.isBlank(busiTable)) {
				vo.setTableName(rs.getString("TABLE_NAME"));
			}else {
				vo.setTableName(busiTable);
			}
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
