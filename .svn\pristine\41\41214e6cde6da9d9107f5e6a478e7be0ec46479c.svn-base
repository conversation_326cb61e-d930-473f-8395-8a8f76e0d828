<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>固定资产管理</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">部门</span>
						 	<input class="form-control input-sm" name="deptName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">姓名</span>
						 	<input class="form-control input-sm" name="faUserName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">状态</span>
						 	<select class="form-control input-sm" name="flag">
						 		<option value="">--</option>
						 		<option value="1">未提交</option>
						 		<option value="2">已提交</option>
						 	</select>
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" data-event='enter' class="btn btn-sm btn-default" onclick="DataMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="dataMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default layui-hide-xs" type="button" lay-event="DataMgr.edit"><i class="fa fa-edit" aria-hidden="true"></i> 一键提醒</button>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var DataMgr={
			init:function(){
				$("#dataMgrForm").initTable({
					mars:'FaDao.checkDataStat',
					id:'dataMgrTable',
					autoSort:false,
					toolbar:'#btnBar',
					limit:30,
					limits:[30,100,300],
					cellMinWidth:100,
					rowEvent:'rowEvent',
					height:'full-90',
					cols: [[
					 {type:'checkbox'},
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'DEPTS',
						title: '部门',
						align:'left'
					},{
						 field:'FA_USER_NAME',
						 title:'姓名'
					 },{
						 field:'DATA1',
						 title:'工号'
					 },{
						 field:'COUNT',
						 title:'资产数量',
						 width:80
					 },{
					    field: 'SUBMIT_TIME',
						title: '最后提交时间'
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataMgrForm").queryData({id:'dataMgrTable',jumpOne:true});
			},
			edit:function(dataList){
			    if(dataList.length == 0){
					layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
					return;
				}
			    
				var ids = [];
				for(var index in dataList){
					ids.push(dataList[index]['FA_USER_ID']);
				}
				ajax.remoteCall("${ctxPath}/servlet/remind?action=faCheckMsg",{userIds:ids.join(',')},function(result) {
					if(result.state == 1){
						layer.msg('提醒成功');
					}else{
						layer.alert(result.msg);
					}
				});
			},
			add:function(){
				
			}
		}
		
		function reloadDataList(){
			DataMgr.query();
		}
		$(function(){
			$("#dataMgrForm").render({success:function(){
				DataMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>