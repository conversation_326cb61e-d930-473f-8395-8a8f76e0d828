<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>事务详情</title>
	<style type="text/css">
		.icon{
		    width: 1.6em;
		    height: 1.6em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 2px;
		}
		.ibox-content {
		    padding: 10px 15px!important;
		    min-height: calc(100vh - 20px);
		    border-radius: 8px;
		}
		
		#affairEditForm .file-div{ 
		    display: block;
		    float: inherit;
		    background-color: #ffffff;
		    margin-left: -10px;
		}
	
		.table-vzebra tbody > tr > td:nth-child(2n+1), .table-vzebra tbody > tr > th:nth-child(2n+1){text-align: left;} 
		.radio-inline{margin-top: 0px;}
		#affairEditForm{
			margin-bottom: 100px;
			height: 100%;
		}
		#affairEditForm .userName{
		    font-size: 1em;
		    color: rgba(0,0,0,.87);
		    font-weight: 500;
		}
		#affairEditForm .createTime{
		    display: inline-block;
		    margin-left: .5em;
		    color: rgba(0,0,0,.4);
		    font-size: .875em;
		}
		#affairEditForm td{text-align: left;}
		#affairEditForm .t1 td{line-height: 16px;}
		.avatar{
			float: left;
			height: 3em;
			width: 3em;
			display: block;
		    margin: .2em 0 0;
		}
    	.avatar img{
		    display: inline-block;
		    height: 100%;
		    width: 100%;
		    border-radius: 100%;
		    overflow: hidden;
		    font-size: inherit;
		    vertical-align: middle;
		    -webkit-box-shadow: 0 0 1px rgba(0,0,0,.3);
		    box-shadow: 0 0 1px rgba(0,0,0,.3);
		    cursor: pointer;
		}
		.b_content{
			margin: 10px 15px;
		}
		.d_content{
			margin-left: 4em;
			padding: 6px 0px;
		}
		small a{margin-right: 6px;cursor: pointer;color: #999!important;}
		.color999{color: #999;margin-right: 2px;}
		#affairDesc img{max-width: 700px;}
		#affairDesc{
		    padding: 15px 15px;
		    line-height: 24px;
		    font-size: 13px;
		    color: #0c100c;
		    border-radius: 10px;
		    border: 1px solid #eee;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<div class="ibox-content">
		     	<form id="affairEditForm" data-mars="AffairDao.record" autocomplete="off" data-mars-prefix="affair.">
		     		   <input type="hidden" id="affairId" value="${param.affairId}" name="affair.AFFAIR_ID"/>
		     		   <input type="hidden" value="${param.affairId}" name="fkId"/>
						<table class="table table-edit table-vzebra t1">
					            <tr>
				                    <td colspan="2">
				                   		 <span style="font-size: 18px;font-weight: bold;line-height: 22px;" name="affair.AFFAIR_NAME"></span>
				                   		 <div class="layui-hide-xs pull-right">
				                   		     <button class="btn btn-default btn-xs" type="button" id="pageBack" onclick="pageGoback('${param.source}')">返回</button>
				                   		     <button class="btn btn-success btn-xs ml-10" type="button" onclick="addData()">+我要发起</button>
				                   		     <c:if test="${param.source=='send'}">
					                   		     <button class="btn btn-warning btn-xs ml-10" type="button" onclick="delData()">删除</button>
					                   		     <button class="btn btn-info btn-xs ml-10" type="button" onclick="editData()">修改</button>
				                   		     </c:if>
				                   		 </div>
				                    </td>
					            </tr>
					            <tr >
					            	<td>
					            		 <i class="glyphicon glyphicon-time"></i> 
					            		 <span name="affair.CREATE_NAME"></span>
					            		 <span class="color999">发送于</span>
					            		 <span class="color999" name="affair.EXCUTE_SEND_TIME" data-fn="affairTimeFn"></span>
				                    	<i class="layui-hide-xs glyphicon glyphicon-eye-open ml-10"></i>
				                    	<span class="layui-hide-xs color999">查看次数</span>
			                    		<span class="layui-hide-xs" onclick="viewLog();" style="text-decoration: underline;cursor: pointer;color: blue;" name="affair.VIEW_COUNT"></span>
				                    </td>
					            </tr>
				                <tr>
				                	<td>
				                    	<i class="glyphicon glyphicon-hand-right"></i>
				                    	<span class="color999">收件人</span>
			                    		<span name="affair.RECEIVER_DEPT_NAME"></span>
			                    		<span name="affair.RECEIVER_USER_NAME"></span>
			                    		<span onclick="viewLookRecord();" style="text-decoration: underline;cursor: pointer;color: blue;"><span name="affair.LOOK_COUNT"></span>/<span name="affair.RECEIVER_COUNT"></span></span>
				                	</td>
				                </tr>
				                <tr>
				                	<td>
				                    	<i class="glyphicon glyphicon-flag"></i>
				                		<span class="color999">抄送人</span>
				                    	<span name="affair.SHARE_NAMES" data-fn="setMailtos">--</span>
				                	</td>
				                </tr>
				                <tr>
				                	<td>
					               		<div id="affairDesc" style="min-height: 120px;overflow:auto;">
					               			<div style="min-height: 200px;" name="affair.AFFAIR_DESC"></div>
					               			<div data-template="affair-template-files" data-mars="FileDao.fileList"></div>
					               			<div id="fileList"></div>
					               		
					               			<div style="color: #666;font-size: 14px;">
					               				<hr style="border: none;border-bottom:1px solid #eee!important;border-color:#1e9fff!important;">
					               				<p>姓名：${userData.userName}</p>
					               				<p>部门：${userData.deptName }</p>
					               				<p>岗位：${userData.post }</p>
					               				<p>电话：${userData.mobile }</p>
					               				<p>邮箱：${userData.email}</p>
					               			</div>
					               		</div>
				                	</td>
				                </tr>
	  					  </table>
	  					 <fieldset class="content-title" style="display:block;">
  								<legend>回复</legend>
						  </fieldset>
	  					  <table class="table table-edit table-vzebra">
					            <tr>
					               	<td colspan="4">
			                           <textarea placeholder="在此输入回复内容" style="height: 80px;width:100%" maxlength="255" class="form-control input-sm" name="comment" id="comment"></textarea>
					               		<small class="layui-hide-xs mt-10">快速回复：<a>已处理</a><a>已查收</a><a>已解决</a></small>
					               		<div class="pull-right mt-10">
				                            <button class="btn btn-sm btn-default btn-outline mr-15" type="button" onclick="loadCommentHistory()"><i class="glyphicon glyphicon-refresh"></i> 刷新回复</button>
				                            <button class="btn btn-sm btn-info btn-outline uploadFileBtn mr-15" type="button" onclick="$('#localfile').click()">+上传附件</button>
				                            <div class="btn-group btn-group-sm">
				                           		<button type="button" style="box-shadow: 0 4px 8px 0 rgba(31,93,234,.35);" class="btn btn-primary btn-sm dropdown-toggle"  data-toggle="dropdown"> <i class="glyphicon glyphicon-send"></i> 提交回复 </button>
												<ul class="dropdown-menu dropdown-menu-right">
												    <li><a href="javascript:;"  onclick="addComment(0)">回复</a></li>
												    <li><a href="javascript:;"  onclick="addComment(1)">回复全部</a></li>
												</ul>
										  </div>
					               		</div>
					               	</td>
					            </tr>
	  					  </table>
	  					  <div id="history"></div> 
		  		</form>
		</div>
	  	
	  	<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Affair.uploadFile()"/>
  		</form>
  		
  		 <script id="affair-template-files" type="text/x-jsrender">
			{{if data.length>0}}<br>{{/if}}
			{{for data}}
				<div class="file-div">
					<input name="fileIds" value='{{:FILE_ID}}' type="hidden"/> {{call:FILE_TYPE fn='getFileIcon'}}<a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" target="_blank" style="color:#000000;">{{:FILE_NAME}}</a>
					<span class="layui-hide-xs">（{{:FILE_SIZE}}）{{:CREATE_NAME}}于{{:CREATE_TIME}}上传 </span>
 					<a class="layui-hide-xs" href="javascript:void(0);" onclick="downloadFile(this)" data-name="{{:FILE_NAME}}" data-href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看"> <i class="fa fa-download"></i>下载</a> 
 					<a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" target="_blank"><i class="fa fa-eye"></i> 预览</a> 
				</div>
			{{/for}}
		</script>
		
  		 <script id="template-comments" type="text/x-jsrender">
			{{for data}}
				<div class="b_content">
					<div class="avatar">
						<img onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:CREATOR fn='getUserPic'}}">
					</div>
					<div class="d_content">
						<div><span class="userName">{{:CREATE_NAME}}</span> <span class="createTime">{{:CREATE_TIME}}</span></div>	
						<div class="p_hover">
							<div class="ib">{{call:CONTENT fn='getContent'}}</div>
						</div>						
					</div>
				</div>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	
	<script type="text/javascript">
   
		var Affair = {};
	    
		Affair.affairId='${param.affairId}';
		
		var affairId='${param.affairId}';
			
		function getContent(val){
			if(val){
				return val.replace(/\n|\r\n/g,'<br/>');
			}else{
				return '--';
			}
		}
		
		var affairObj;
		function initFormData(){
			$("#affairEditForm").render({success:function(result){
				var record=result['AffairDao.record'];
				affairObj=record['data'];
				
				$('title').text(affairObj.AFFAIR_NAME);
				
				loadCommentHistory();
				
				initLookLog();
				
				layer.photos({photos: '#affairDesc',anim: 0,shade:0,shadeClose:true,closeBtn:true}); 
			}});  
		}
		
		$(function(){
			initFormData();
			$("small a").click(function(){
				var t = $(this);
				var text = t.text();
				saveComment(text,0);
			});
		});
		
		function setMailtos(val){
			if(val==''){
				return '无';
			}else{
				return val;
			}
		}
	
		Affair.ajaxSubmitForm = function(){
			if(form.validate("#affairEditForm")){
				if(Affair.affairId){
					Affair.updateState();
				}
			};
		}
		
		Affair.uploadFile = function(callback){
			var fkId=Affair.affairId;
			easyUploadFile({callback:'callback',fileMaxSize:(1024*20),fkId:fkId,source:'affair'});
		}
		
		var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="/yq-work/fileview/'+data.id+'?view=online&filename='+data.name+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
			var comment="上传["+data.name+"]";
			saveComment(comment,0);
		}
		
		function loadCommentHistory(){
			 ajax.remoteCall("${ctxPath}/webcall?action=CommonDao.commentsList",{fkId:affairId},function(result) { 
				if(result.total>0){
					var html=renderTpl("template-comments",result);
					$("#history").html(html);
				}
			});
		}
		
		function downloadFile(el){
			var obj = $(el);
			saveComment("下载["+obj.attr('data-name')+"]",0);
			window.open(obj.attr('data-href'));
		}
		
		var addComment = function(noticeFlag) {
			var comment = $("#comment").val();
			saveComment(comment,noticeFlag);
		}
		
		var saveComment = function(comment,noticeFlag){
			if(comment){
				var data = {noticeFlag:noticeFlag,content:comment,fkId:affairId};
				ajax.remoteCall("${ctxPath}/servlet/affair?action=addComment",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							$("#comment").val("");
							loadCommentHistory();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}
		}
		
		function delComment(id,obj){
			ajax.remoteCall("${ctxPath}/servlet/comment?action=del",{commentId:affairId},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(obj).parents(".b_content").remove();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function initLookLog(){
			ajax.remoteCall("${ctxPath}/servlet/affair?action=updateLookRecord",{id:affairId},function(result) {});
		}
		
		var viewLog =function(){
			lookLogLayer(Affair.affairId);
		}
		
		var viewLookRecord = function(){
			popup.layerShow({url:'/yq-work/pages/affair/affair-obj.jsp',id:'viewLookRecord',full:fullShow(),title:'查收详情',data:{affairId:affairId},area:['500px','500px']});
		}
		
		var affairTimeFn = function(val,row,el){
			if(val){
				return val;
			}else{
				$(el).prev().text('创建于');
				return row['CREATE_TIME'];
			}
		}
		var editData = function(){
			var json  = {affairId:affairObj['AFFAIR_ID'],affairState:affairObj['AFFAIR_STATE'],sendType:affairObj['SEND_TYPE']};
			let url = '${ctxPath}/pages/affair/affair-edit.jsp';
			$.get(url,json,function(result){
				$(".right-content").html(result);
			});
		}
		
		var delData = function(id){
			layer.confirm('确认删除吗?',function(index){
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/affair?action=del",{id:Affair.affairId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							$('.send').click();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		var pauseData = function(id){
			layer.confirm('确认暂停吗?',function(index){
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/affair?action=pause",{id:Affair.affairId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							$('.send').click();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
	    function pageGoback(source){
	    	var len = $('.'+source).length;
	    	if(len==0){
	    		 location.href = '/yq-work/affair';
	    	}else{
	    		location.href = '/yq-work/affair/'+source;
	    	}
		}
		
	  var addData = function(){
		  location.href = '/yq-work/affair/write';
	  }
		
</script>
</EasyTag:override>
<c:choose>
	<c:when test="${param.isDiv==0}">
		<%@ include file="/pages/common/layout_form.jsp" %>
	</c:when>
	<c:otherwise>
		<%@ include file="/pages/common/layout_div.jsp" %>
	</c:otherwise>
</c:choose>