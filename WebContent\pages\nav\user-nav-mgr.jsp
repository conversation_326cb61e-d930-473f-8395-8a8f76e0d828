<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>nav</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
				<button class="btn btn-sm btn-info" type="button" onclick="list.add();" style="position: fixed;bottom: 100px;right: 100px;border-radius:50%;width: 60px;height: 60px;">+新增</button>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
				list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'UserNavDao.list',
					page:false,
					cols: [[
		             {
						title: '编号',
						type:'numbers'
					 },{
					    field: 'TITLE',
						title: '标题',
						align:'left'
					},{
					    field: 'URL',
						title: 'url',
						align:'left',
						templet:'<div><a target="_blank" href="{{d.URL}}" class="layui-table-link">{{d.URL}}</a></div>'
					},{
					    field: 'REMARK',
						title: '备注',
						align:'left'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'left'
					},{
					    field: '',
						title: '操作',
						align:'left',
						event:'list.edit',
						templet:function(){
							return '<a class="layui-table-link" href="javascript:void(0)">编辑</a>';
						}
					}
					]]}
				);
			},
			add:function(){
				popup.layerShow({type:1,anim:0,scrollbar:false,offset:'20px',area:['400px','350px'],url:'${ctxPath}/pages/nav/user-nav-edit.jsp',title:'网址导航'});
			},
			edit:function(data){
				popup.layerShow({type:1,anim:0,scrollbar:false,offset:'20px',area:['400px','350px'],url:'${ctxPath}/pages/nav/user-nav-edit.jsp',data:{navId:data['NAV_ID']},title:'网址导航'});
			},
			query:function(){
				$("#searchForm").queryData();
			}
		}
		function reloadNav(){
			list.query();
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>