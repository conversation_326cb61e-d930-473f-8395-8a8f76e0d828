<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
	<style>
		.layui-badge{left: -1px!important;}
	</style>
	<style>
		
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>项目任务数统计</h5>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目类型</span>	
							 <select name="projectType" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="10" data-class="label label-info">合同项目</option>
		                    		<option value="11" data-class="label label-info">提前执行</option>
		                    		<option value="20" data-class="label label-warning">自研项目</option>
		                    		<option value="30" data-class="label label-warning">维保项目</option>
	                    	</select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var taskTable= {
				mars:'TaskDao.projectTaskStat',
				skin:'line',
				limit:15,
				limits:[10,15,30,50,100,200,300],
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
				    field: 'PROJECT_NAME',
					title: '项目名称',
					align:'left',
					event:'list.detail',
					style:'color:#1E9FFF;cursor:pointer'
				},{
				    field: 'E',
					title: '总任务数',
					align:'center',
					width:90,
					sort:true
				},{
				    field: 'A',
					title: '待办数',
					align:'center',
					width:90,
					sort:true
				}
				 ,{
				    field: 'B',
					title: '进行中',
					width:90,
					sort:true,
					align:'center'
				}
			  ,{
				   field: 'C',
				   title: '已完成',
				   sort:true,
					width:90,
				   align:'center'
			},{
				   field: 'D',
				   title: '已验收',
					width:90,
				   sort:true,
				   align:'center'
				}
			]]};
		var list={
			init:function(){
				$("#searchForm").initTable($.extend(taskTable,{id:'list'},{done:function(result){

				}}));
			},
			detail:function(data){
				projectDetailByRow(data);
			},
			query:function(){
				$("#searchForm").queryData({id:'list'});
			},
		}
		$(function(){
			list.init();
			$("#searchForm").render();
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>