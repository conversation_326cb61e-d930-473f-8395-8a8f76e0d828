package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveNodeRowMapper;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.ApproveResultRowMapper;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowApplyRowMapper;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.FlowRowMapper;
import com.yunqu.work.model.StaffModel;

public class ApproveService extends AppBaseService {

	private static class Holder{
		private static ApproveService service=new ApproveService();
	}
	public static ApproveService getService(){
		return Holder.service;
	}
	
	public static Map<String,String> roles = new HashMap<String, String>();
	
	public ApproveNodeModel getStartNode(FlowApplyModel applyModel) {
		return getStartNode(applyModel,null);
	}
	
	public ApproveNodeModel getStartNode(FlowApplyModel applyModel,JSONObject params) {
		try {
			ApproveNodeModel obj = this.getQuery().queryForRow("select * from yq_flow_approve_node where p_node_id = 0 and flow_code = ? and node_state = 0 order by check_index", new Object[] {applyModel.getFlowCode()}, new ApproveNodeRowMapper());
			return getNodeInfo(obj,applyModel,params);
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ApproveNodeModel();
		}
	}
	public ApproveNodeModel getNodeInfoByNodeCode(String nodeCode,FlowApplyModel applyModel,JSONObject params) {
		try {
			String flowCode = applyModel.getFlowCode();
			ApproveNodeModel obj = this.getQuery().queryForRow("select * from yq_flow_approve_node where node_code = ? and flow_code = ? and node_state = 0", new Object[] {nodeCode,flowCode}, new ApproveNodeRowMapper());
			if(obj!=null) {
				return getNodeInfo(obj,applyModel,params);
			}
			return null;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return null;
		}
	}
	public ApproveNodeModel getNode(String nodeId) {
		try {
			ApproveNodeModel obj = this.getQuery().queryForRow("select * from yq_flow_approve_node where node_id = ?", new Object[] {nodeId}, new ApproveNodeRowMapper());
			return obj;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ApproveNodeModel();
		}
	}
	
	public ApproveNodeModel getNodeByResultId(String resultId) {
		try {
			ApproveNodeModel obj = this.getQuery().queryForRow("select t2.* from yq_flow_approve_result t1,yq_flow_approve_node t2 where t1.node_id = t2.node_id and t1.result_id = ?", new Object[] {resultId}, new ApproveNodeRowMapper());
			return obj;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ApproveNodeModel();
		}
	}
	
	public ApproveResultModel getResult(String resultId) {
		try {
			ApproveResultModel obj = this.getQuery().queryForRow("select t1.* from yq_flow_approve_result t1 where  t1.result_id = ?", new Object[] {resultId}, new ApproveResultRowMapper());
			return obj;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ApproveResultModel();
		}
	}
	
	public List<ApproveResultModel> getTodoResult(String businessId) {
		try {
			List<ApproveResultModel> obj = this.getQuery().queryForList("select t1.* from yq_flow_approve_result t1 where  t1.business_id = ? and t1.check_result = ?", new Object[] {businessId,0}, new ApproveResultRowMapper());
			return obj;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ArrayList<ApproveResultModel>();
		}
	}
	
	public List<ApproveNodeModel> getNodes(String flowCode) {
		try {
			return this.getQuery().queryForList("select * from yq_flow_approve_node where  flow_code = ? and node_state = 0 order by check_index", new Object[] {flowCode}, new ApproveNodeRowMapper());
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new ArrayList<ApproveNodeModel>();
		}
	}
	
	public FlowModel getFlow(String flowCode) {
		try {
			FlowModel model  = this.getQuery().queryForRow("select t1.* from yq_flow_category t1 where t1.flow_code = ?",new Object[] {flowCode}, new FlowRowMapper());
			return model;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new FlowModel();
		}
	}
	
	public FlowApplyModel getApply(String applyId) {
		try {
			FlowApplyModel obj = this.getQuery().queryForRow("select t1.*,t2.flow_name,t2.approver_field,t2.start_node_name,t2.xl_check_skip,t2.repeat_check_skip from yq_flow_apply t1,yq_flow_category t2 where t1.flow_code = t2.flow_code and t1.apply_id = ?", new Object[] {applyId},new FlowApplyRowMapper());
			return obj;
		} catch (SQLException e) {
			getLogger().error(null,e);
			return new FlowApplyModel();
		}
	}
	public String getNextNodeId(String nodeId) throws SQLException {
		String nextNodeId = this.getQuery().queryForString("select c_node_id from yq_flow_approve_node where node_id = ?", nodeId);
		return nextNodeId;
	}
	
	public ApproveNodeModel getNodeInfo(ApproveNodeModel nodeInfo,FlowApplyModel applyModel,JSONObject params) throws SQLException {
		String userId = applyModel.getApplyUserId();
		if(nodeInfo==null) {
			return null;
		}
		if(StringUtils.isBlank(userId)) {
			userId = applyModel.getApplyBy();
		}
		if(params == null) {
			params = new JSONObject();
		}
		String _userId = params.getString("applyUserId");
		String _deptId = params.getString("applyDeptId");
		if(StringUtils.notBlank(_userId)) {
			userId = _userId;
		}else if(StringUtils.notBlank(_deptId)) {
			userId = StaffService.getService().getLeader(_deptId);
		}
		int checkType = nodeInfo.getCheckType();
		if(checkType==22) {//直接上级
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
			//申请人是主管
			if(userId.equalsIgnoreCase(staffModel.getSuperior())) {
				nodeInfo.setCheckBy(staffModel.getpSuperior());
				nodeInfo.setCheckName(StaffService.getService().getUserName(staffModel.getpSuperior()));
				
//				String nodeId = nodeInfo.getNextNodeId();
//				if(!"0".equals(nodeId)) {
//					nodeInfo = getNodeInfo(nodeId,userId,params);
//				}
				
			}else {
				nodeInfo.setCheckBy(staffModel.getSuperior());
				nodeInfo.setCheckName(StaffService.getService().getUserName(staffModel.getSuperior()));
			}
		}else if(checkType==20) {//直接主管
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
			if(userId.equalsIgnoreCase(staffModel.getDeptLeader())) {
				String nodeId = nodeInfo.getNextNodeId();
				if(!"0".equals(nodeId)&&nodeInfo.getCheckType()!=FlowConstants.FLOW_APPROVER_SELECT_USER) {
					return getNodeInfoByNodeId(nodeId,applyModel,params);
				}else {
					nodeInfo.setCheckBy(staffModel.getDeptLeader());
					nodeInfo.setCheckName(StaffService.getService().getUserName(staffModel.getDeptLeader()));
				}
			}else {
				nodeInfo.setCheckBy(staffModel.getDeptLeader());
				nodeInfo.setCheckName(StaffService.getService().getUserName(staffModel.getDeptLeader()));
			}
			
		}else if(checkType==23) {//直接主管或副主管
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
			if(userId.equalsIgnoreCase(staffModel.getDeptLeader())) {
				String nodeId = nodeInfo.getNextNodeId();
				if(!"0".equals(nodeId)&&nodeInfo.getCheckType()!=FlowConstants.FLOW_APPROVER_SELECT_USER) {
					return getNodeInfoByNodeId(nodeId,applyModel,params);
				}else {
					nodeInfo.setCheckBy(staffModel.getDeptLeader());
					nodeInfo.setCheckName(StaffService.getService().getUserName(staffModel.getDeptLeader()));
				}
			}else {
				String deptSecondLeader = StaffService.getService().getDeptSecondLeader(staffModel.getDeptId());
				if(StringUtils.notBlank(deptSecondLeader)) {
					nodeInfo.setCheckBy(staffModel.getDeptLeader()+","+deptSecondLeader);
					nodeInfo.setCheckName(StaffService.getService().getUserName(staffModel.getDeptLeader())+","+StaffService.getService().getUserName(deptSecondLeader));
				}else {
					nodeInfo.setCheckBy(staffModel.getDeptLeader());
					nodeInfo.setCheckName(StaffService.getService().getUserName(staffModel.getDeptLeader()));
				}
			}
			
		}if(checkType==21) {//中心副总
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
//			if(userId.equalsIgnoreCase(staffModel.getSuperior())) {
//				nodeInfo.setCheckBy(staffModel.getUserId());
//				nodeInfo.setCheckName(staffModel.getUserName());
//			}else {
				nodeInfo.setCheckBy(staffModel.getpSuperior());
				nodeInfo.setCheckName(StaffService.getService().getUserName(staffModel.getpSuperior()));
//			}
			if(userId.equalsIgnoreCase(staffModel.getpSuperior())) {
//				String nodeId = nodeInfo.getNextNodeId();
//				if(!"0".equals(nodeId)&&nodeInfo.getCheckType()!=FlowConstants.FLOW_APPROVER_SELECT_USER) {
//					return getNodeInfoByNodeId(nodeId,applyModel,params);
//				}
			}
		}else if(checkType==30) {//自定义选择人
			if(params.isEmpty()) {
				this.getLogger().warn("没指定选择审批人。");
				return null;
			}
			String selectUserId = params.getString("selectUserId");
			String selectUserName = params.getString("selectUserName");
			if(StringUtils.isBlank(selectUserId)&&StringUtils.isBlank(selectUserName)) {
				selectUserId = "";selectUserName = "";
				//this.getLogger().warn("没指定选择审批人");
				//return null;
			}
			if(StringUtils.isBlank(selectUserId)&&StringUtils.notBlank(selectUserName)) {
				selectUserId = StaffService.getService().getUserId(selectUserName);
			}
			if(StringUtils.isBlank(selectUserName)&&StringUtils.notBlank(selectUserId)) {
				selectUserName = StaffService.getService().getUserName(selectUserId);
			}
			nodeInfo.setCheckBy(selectUserId);
			nodeInfo.setCheckName(selectUserName);
		}else if(checkType==40) {
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
			nodeInfo.setCheckBy(userId);
			nodeInfo.setCheckName(staffModel.getUserName());
		}else if(checkType==10) {
			String selectUserId = params.getString("assignUserId");
			String selectUserName = params.getString("assignUserName");
			if(StringUtils.isBlank(selectUserId)&&StringUtils.notBlank(selectUserName)) {
				selectUserId = StaffService.getService().getUserId(selectUserName);
			}
			if(StringUtils.isBlank(selectUserName)&&StringUtils.notBlank(selectUserId)) {
				selectUserName = StaffService.getService().getUserName(selectUserId);
			}
			if(StringUtils.notBlank(selectUserId)) {
				nodeInfo.setCheckBy(selectUserId);
				nodeInfo.setCheckName(selectUserName);
			}else {
				String roleId = nodeInfo.getCheckRoleId();
				if(roles.get(roleId)==null) {
					JSONObject row = this.getQuery().queryForRow("select * from yq_flow_approve_role where role_id = ?", new Object[] {roleId},new JSONMapperImpl());
					nodeInfo.setCheckBy(row.getString("ROLE_USER_ID"));
					nodeInfo.setCheckName(row.getString("ROLE_USER_NAME"));
					roles.put(roleId, nodeInfo.getCheckBy()+"#"+nodeInfo.getCheckName());
				}else {
					String[] array = roles.get(roleId).split("#");
					nodeInfo.setCheckBy(array[0]);
					nodeInfo.setCheckName(array[1]);
				}
			}
		}
		return nodeInfo;
	}
	
	public ApproveNodeModel getNodeInfoByNodeId(String nodeId,FlowApplyModel applyModel,JSONObject params) throws SQLException {
		ApproveNodeModel nodeInfo= this.getQuery().queryForRow("select * from yq_flow_approve_node where node_id = ?", new Object[]{nodeId}, new ApproveNodeRowMapper());
		return getNodeInfo(nodeInfo,applyModel,params);
	}
	
	public String[] getNodeUser(String flowCode) {
		String sql = "select GROUP_CONCAT(user_id) user_ids,GROUP_CONCAT(user_name) user_names from yq_flow_approve_node where flow_code = ? and node_state = 0 ORDER BY check_index";
		JSONObject checkObj = null;
		try {
			checkObj = this.getQuery().queryForRow(sql,new Object[] {flowCode},new JSONMapperImpl());
			String userIds = checkObj.getString("USER_IDS");
			String userNames = checkObj.getString("USER_NAMES");
			return new String[] {userIds,userNames};
		} catch (SQLException e) {
			getLogger().error(null, e);
			return new String[]{"",""};
		}
	}
	
	public String getUserNames(String userIds) {
		EasySQL sql = new EasySQL("select GROUP_CONCAT(USERNAME) user_names from easi_user where 1=1");
		sql.appendIn(userIds.split(","),"and user_id");
		try {
			return getMainQuery().queryForString(sql.getSQL(), sql.getParams());
		} catch (SQLException e) {
			getLogger().error(null,e);
			return null;
		}
	}
	
	public void clearRoleCache() {
		roles.clear();
	}
	
}
