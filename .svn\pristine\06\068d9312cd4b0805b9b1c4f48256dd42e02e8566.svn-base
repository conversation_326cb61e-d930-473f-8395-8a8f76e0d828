<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>考勤名单</title>
</EasyTag:override>
<EasyTag:override name="content">
    <form autocomplete="off"  onsubmit="return false;" class="form-inline" id="kqPeopleForm">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5>考勤名单</h5>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">员工名</span>
                        <input type="text" name="userName" class="form-control input-sm" style="width: 90px;">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">部门名</span>
                        <input type="text" name="deptName" class="form-control input-sm" style="width: 90px;">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">角色</span>
                        <input type="text" name="jobName" class="form-control input-sm" style="width: 100px;">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">驻地</span>
                        <input type="text" name="workArea" class="form-control input-sm" style="width: 100px;">
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">是否考勤</span>
                        <select name="state" class="form-control input-sm" >
                            <option value="">全部</option>
                            <option value="1">否</option>
                            <option value="0">是</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm">
                        <button type="button" class="btn btn-sm btn-default" onclick="kqPeopleList.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
                    </div>
                </div>
            </div>
            <div class="ibox-content">
                <table class="layui-hide" id="kqPeopleList"></table>
            </div>
        </div>

    </form>

    <script  type="text/html" id="kqToolbar">
        <button class="btn btn-info btn-sm" onclick="kqPeopleList.batchDel()" type="button">批量删除</button>
        <button class="btn btn-default btn-sm ml-15" lay-event="kqPeopleList.add" type="button">添加员工</button>
        <button class="btn btn-default btn-sm ml-15" lay-event="kqPeopleList.batchRemoveKq" type="button">批量免除考勤</button>
        <button class="btn btn-default btn-sm ml-15" lay-event="kqPeopleList.batchAddKq" type="button">批量加入考勤</button>
    </script>


    <script type="text/x-jsrender" id="bar">
		{{if STATE==1}}
  			<a style="background-color:#f0ad4e" class="layui-btn layui-btn-normal layui-btn-xs" lay-event="kqPeopleList.addKq">加入考勤</a>
  			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="kqPeopleList.del">删除</a>
		{{else}}
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="kqPeopleList.removeKq">免除考勤</a>
  			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="kqPeopleList.del">删除</a>
		{{/if}}
	</script>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        $(function(){
            $('#kqPeopleForm').render({success:function(){
                    kqPeopleList.init();
                }});
        });

        var kqPeopleList={
            init:function(){
                $('#kqPeopleForm').initTable({
                    mars:'KqDao.kqPeople',
                    id:"kqPeopleList",
                    toolbar:"#kqToolbar",
                    limits:[20,50,100,300,500,1000,1500,2000],
                    autoSort:false,
                    rowEvent:'rowEvent',
                    cols: [[
                        {title:'',type:'checkbox'},
                        {
                            field: 'KQ_USER_NAME',
                            title: '姓名',
                            align:'center',
                            width:120
                        },{
                            field: 'DEPT_NAME',
                            title: '部门名称',
                            align:'center',
                        },{
                            field: 'JOB_TITLE',
                            title: '角色',
                            align:'center',
                        },{
                            field: 'WORK_AREA',
                            title: '驻地',
                            align:'center',
                        },
                        {
                            field: 'STATE',
                            title: '是否考勤',
                            align:'center',
                            width: 100,
                            templet:function(d){
                                if(d.STATE == '1'){
                                    return '×';
                                }else{
                                    return '√';
                                }
                            }
                        },
                        {
                            field: 'SB_TIME',
                            title: '上班时间',
                            align:'center',
                            width: 100,
                        },{
                            field: 'XB_TIME',
                            title: '下班时间',
                            align:'center',
                            width: 100,
                        },
                        {
                            field: 'REMARK',
                            title: '备注',
                            align: 'left'
                        },{
                            title: '操作',
                            align:'center',
                            width:180,
                            cellMinWidth:180,
                            templet:function(row){
                                return renderTpl('bar',row);
                            }
                        }
                    ]]}
                );
            },
            query:function (){
                $("#kqPeopleForm").queryData();
            },
            add:function(){
                popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,offset:'auto',area:['50%','40%'],url:'${ctxPath}/pages/ehr/kq-people-edit.jsp',title:'添加员工考勤名单'});
            },
            removeKq:function(data){
                ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=UpdatePeople",{'userId':data.KQ_USER_ID},function(result) {
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            kqPeopleList.query();
                            layer.closeAll();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            },
            addKq:function(data){
                ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=UpdatePeople2",{'userId':data.KQ_USER_ID},function(result) {
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            kqPeopleList.query();
                            layer.closeAll();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            },
            del:function(data){
                layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
                    ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=delPeople",{'userId':data.KQ_USER_ID},function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                kqPeopleList.query();
                                layer.closeAll();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                });
            },
            batchDel:function(){
                var checkStatus = table.checkStatus('kqPeopleList');
                if(checkStatus.data.length>0){
                    ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=batchDel",checkStatus.data,function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                kqPeopleList.query();
                                layer.closeAll();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                }else{
                    layer.msg("请选择!");
                }
            },
            batchAddKq:function(){
                var checkStatus = table.checkStatus('kqPeopleList');
                if(checkStatus.data.length>0){
                    ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=batchAddKq",checkStatus.data,function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                kqPeopleList.query();
                                layer.closeAll();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                }else{
                    layer.msg("请选择!");
                }
            },
            batchRemoveKq:function(){
                var checkStatus = table.checkStatus('kqPeopleList');
                if(checkStatus.data.length>0){
                    ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=batchRemoveKq",checkStatus.data,function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                kqPeopleList.query();
                                layer.closeAll();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                }else{
                    layer.msg("请选择!");
                }
            },
        }

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>