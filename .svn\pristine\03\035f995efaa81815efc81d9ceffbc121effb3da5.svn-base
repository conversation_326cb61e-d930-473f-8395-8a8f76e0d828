<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>未打卡补签申请</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <input type="hidden" name="apply.data11" id="startDate"/>
			   <input type="hidden" name="apply.data12" id="endDate"/>
			   <input type="hidden" name="apply.data13" value="1"/>
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" data-template="({{:data2}}{{:data14}})未打卡补签申请单" value="${staffInfo.userName}${staffInfo.staffNo}未打卡补签申请单"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">漏打卡日期</td>
					  			<td>
					  				<input type="hidden" value="${param.kqId}" name="apply.data3"/>
					  				<input type="text" data-rules="required" value="${param.kdDate}" data-laydate="{type:'date',max:0}" class="form-control input-sm Wdate" name="apply.data2"/>
					  			</td>
					  			<td class="required">班次类别</td>
					  			<td>
					  				<select class="form-control input-sm" data-rules="required" name="apply.data14">
					  					<option value="">--</option>
					  					<option value="上班">上班</option>
					  					<option value="下班">下班</option>
					  					<option value="整天">整天</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">补签原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark">忘打卡了</textarea>
					  			</td>
					  		</tr>
					  		<tr class="remindInfo hidden-print">
					  			<td>注意事项</td>
					  			<td colspan="3">
					  			    1个月内， 因员工个人原因（不包括考勤系统故障） 未指纹打卡的， 有 <b>2 次补签</b>的机会。 上午上班未指纹打卡的员工须当天填写《未打卡补签申请单》， 下午下班未指纹打卡的员工须第二天早上填写《未打卡补签申请单》。
					  			    <a onclick="FlowCore.historyApply();" href="javascript:;">申请记录</a> 
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideHistoryApply:false,success:function(data){
				Flow.initData();
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			var date = $("[name='apply.data2']").val();
			var monthDate = date.substring(0,7);
			ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=notKqCount",{date:monthDate},function(result) { 
				var monthApplyCount = result.data;
				if(monthApplyCount>=2){
					layer.confirm("在"+monthDate+"，您已经提交了"+monthApplyCount+"次关于漏打卡的申请。然而，根据规定，您不能再进行此类申请。请仔细阅读相关注意事项，或者准备不少于20字的补签原因并提交审批.",{icon:3,title:'操作提醒',offset:'20px',btn:['继续提交','取消']},function(){
						excuteSubmit(1);
					});
				}else{
					excuteSubmit(0);
				}
			});
			
			function excuteSubmit(v){
				if(v=='1'){
					var applyRemark = $("[name='apply.applyRemark']").val();
					if(applyRemark.length<20){
						layer.msg('补签原因不能少于20字');
						return;
					}
				}
				
				var type = $("[name='apply.data14']").val();
				if(type=='上班'){
					$('#startDate').val(date+" 09:00");
					$('#endDate').val(date+" 12:00");
				}else if(type=='下班'){
					$('#startDate').val(date+" 13:30");
					$('#endDate').val(date+" 18:00");
				}else{
					$('#startDate').val(date+" 09:00");
					$('#endDate').val(date+" 18:00");
				}
				FlowCore.ajaxSubmitForm(state);
			}
			
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				var id = result.data;
				ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=setFlowId",{flowId:id,kqId:'${param.kqId}'},function() { 
					
				});
			}});
		}
		
		Flow.initData = function() {
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;
			
			if(nodeCode=='部门主管'){
				var applyUserInfo = FlowCore.applyUserInfo;
				if(applyUserInfo.workCity=='北京'){
					params['ccNames'] = '蔡洁';
				}
			}
			FlowCore.approveData = params;
		}
		
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>