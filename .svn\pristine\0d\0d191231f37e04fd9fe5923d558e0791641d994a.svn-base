<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>twitter</title>
	<style>
		#editor img{max-width: 90%;height: auto;}
		#editor{line-height: 26px;}
		.w-e-text-container{text-align: left;}
	</style>
	<script src="${ctxPath}/static/js/emotions.js" type="text/javascript"></script>
</EasyTag:override>
<EasyTag:override name="content">
     			<form id="twitterEditForm" style="margin-bottom: -20px;" data-mars="RemindDao.record" autocomplete="off" data-mars-prefix="remind.">
     		   			<input type="hidden" value="0" name="remind.REMIND_TYPE"/>
     		  			<input type="hidden" value="${param.remindId}" name="remind.REMIND_ID"/>
						<table class="table table-edit table-vzebra">
					        <tbody>
					        	<tr style="display: none;">
				                    <td style="width: 80px" class="required">标题</td>
				                    <td><input data-rules="required"  id="title" value="无" type="text" name="remind.TITLE" class="form-control input-sm"></td>
					            </tr>
					            <tr style="display: none;">
				                    <td style="width: 80px" class="required">发布时间</td>
				                    <td><input data-mars="CommonDao.date02" type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" name="remind.PUBLISH_TIME" class="form-control input-sm Wdate"></td>
					            </tr>
					            <tr style="display: none;">
				                    <td class="required">状态</td>
				                    <td>
				                    	<input type="radio" checked="checked" value="0" name="remind.STATUS"/> 正常
				                    	<input type="radio" value="1" name="remind.STATUS"/> 草稿
				                    	<input type="radio" value="2" name="remind.STATUS"/> 删除
				                    </td>
					            </tr>
					            <tr>
				                    <td colspan="2">
				                       <div style="margin-top: 15px;" id="editor"></div>
			                           <textarea id="wordText" data-text="false" style="height: 200px;width:300px;display: none;" class="form-control input-sm" name="remind.CONTENT"></textarea>
				                    </td>
					            </tr>
					            <tr>
				                    <td colspan="2" style="text-align: left;">
				                   		<input type="text" style="display: none;" data-mars="CommonDao.userName"  id="userName">
				                   		<input type="hidden" name="remind.PUBLISH_BY" data-mars="CommonDao.userName" id="realName" value="">
				                   		 <label class="checkbox checkbox-inline checkbox-info">
				                    		<input type="radio" value="1" checked="checked" onclick="$('#realName').val($('#userName').val())" name="pubType"/><span>实名</span> 
				                    	</label>
				                   		 <label class="checkbox checkbox-inline checkbox-info">
							    			<input type="radio" value="0" onclick="$('#realName').val('匿名')" name="pubType"/><span>匿名</span> 
										</label>
				                    </td>
					            </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c">
						    	  <button type="button" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 提 交 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/wangEditor/wangEditor.min.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		Edit.remindId='${param.remindId}';
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		weUpload(editor,{uploadImgMaxLength:3});
		editor.customConfig.onchange = function (html) {
			 if(html){
				// html = html.substring(3,html.length);
				// html = html.substring(0,html.length-8);
			     var text=editor.txt.text();
			     if(text.length>500){
			    	 layer.msg("内容不建议超过500长度",{icon:7});
			    	 return;
			     }
			     if(text.length>50){
			    	 text=text.substring(0,50);
			     }
			     $("#title").val(text||'无');
			     html = html.replaceAll("<p>","");
			     html = html.replaceAll("</p>","");
			     html = html.replaceAll("<div>","");
			     html = html.replaceAll("</div>","");
			     
			     $("#wordText").val(html);
			 }else{
			     $("#wordText").val(html)
			 }
		} 
		editor.customConfig.menus = [
      		    'emoticon',  // 表情
      		    'foreColor',  // 文字颜色
      		    'link',  // 插入链接
      		    'image'  // 插入图片
        ];
		 editor.customConfig.emotions = [
              {
                  title: '默认',
                  type: 'image',
                  content:yqEmotions
              },
              {
                  title: 'emoji',
                  type: 'emoji',
                  content: ['😀', '😃', '😄', '😁', '😆','🤐','😬','🙄','😓','😂']
              }
        ];

		editor.create();
		
		$(function(){
			if(Edit.remindId){
				
			}else{
				
			}
			$("#twitterEditForm").render({success:function(result){
				 editor.txt.html($("#wordText").val());
				// var height=$(window).height();
				 $(".w-e-text-container").css("height",250);
			}});
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#twitterEditForm")){
				if(Edit.remindId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#twitterEditForm");
			ajax.remoteCall("${ctxPath}/servlet/remind?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadTwitter();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#twitterEditForm");
			ajax.remoteCall("${ctxPath}/servlet/remind?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						reloadTwitter();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>