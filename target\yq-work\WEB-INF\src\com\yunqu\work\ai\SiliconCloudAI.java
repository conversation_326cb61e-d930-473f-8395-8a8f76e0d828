package com.yunqu.work.ai;

import java.util.ArrayList;
import java.util.List;

import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.utils.LogUtils;

/**
 * sk-kvefbhygihiwowgdydjkrfcaelwlsuzuolhuitmcaropjlfl
 *
 */
public class SiliconCloudAI {

	public static String qwenCoder(String systemSay,String userSay) {
		return request("deepseek-chat",systemSay,userSay);
	}
	
	public static String qwen7B(String systemSay,String userSay) {
		return request("deepseek-chat",systemSay,userSay);
	}
	
	public static String qwen14B(String systemSay,String userSay) {
		return request("deepseek-chat",systemSay,userSay);
	}
	
	public static String deepseek32B(String systemSay,String userSay) {
		return request("deepseek-chat",systemSay,userSay);
	}
	
	public static List<String> requestSSE(String model,String systemSay,String userSay) {
		List<String> result = null;
		try {
			userSay = Jsoup.clean(userSay, Whitelist.none());
			Connection connection = Jsoup.connect("https://api.deepseek.com/v1/chat/completions");
			connection.header("accept", "application/json");
			connection.header("content-type", "application/json");
			connection.header("authorization", "Bearer sk-a70891a410244db1a2aa351ba93bdcb7");
			connection.header("Content-Type","application/json");
			String requestBody = "{\"model\":\""+model+"\",\"messages\":[{\"role\":\"user\",\"content\":\""+userSay+"\"},{\"role\":\"system\",\"content\":\""+systemSay+"\"}],\"stream\":true,\"max_tokens\":4096,\"temperature\":0.7,\"top_p\":0.7,\"frequency_penalty\":0.5,\"n\":1}";
			connection.requestBody(requestBody);
			LogUtils.getLLM().info("model:"+model+">"+requestBody);
			Response response = connection.ignoreContentType(true).timeout(60000).method(Method.POST).execute();
			if(response.statusCode()==200) {
				String resultStr = response.body();
				result =  getMsgs(resultStr);
			}else {
				return empty();
			}
		} catch (Exception e) {
			e.printStackTrace();
			LogUtils.getLLM().error(e.getMessage(),e);
			return empty();
		}
		return result;
	}
	
	public static String request(String model,String systemSay,String userSay) {
		String result = null;
		try {
			userSay = Jsoup.clean(userSay, Whitelist.none());
			Connection connection = Jsoup.connect("https://api.deepseek.com/v1/chat/completions");
			connection.header("accept", "application/json");
			connection.header("Content-Type", "application/json");
			connection.header("authorization", "Bearer sk-a70891a410244db1a2aa351ba93bdcb7");
		
			JSONObject requestBodyJson = new JSONObject();
			requestBodyJson.put("model", model);

			JSONArray messages = new JSONArray();
			
			JSONObject userObj = new JSONObject();
			userObj.put("role", "user");
			userObj.put("content", userSay);
			
			JSONObject systemObj = new JSONObject();
			systemObj.put("role", "system");
			systemObj.put("content", systemSay);
			
			messages.add(systemObj);
			messages.add(userObj);

			requestBodyJson.put("messages", messages);
			requestBodyJson.put("stream", false);
			requestBodyJson.put("max_tokens", 4096);
			requestBodyJson.put("temperature", 0.6);
			requestBodyJson.put("top_p", 1);
			requestBodyJson.put("frequency_penalty", 0.5);
			requestBodyJson.put("n", 1);

			String requestBody = requestBodyJson.toString();
			
			connection.requestBody(requestBody);
			LogUtils.getLLM().info(requestBody);
			Response response = connection.ignoreContentType(true).timeout(60000).method(Method.POST).execute();
			if(response.statusCode()==200) {
				String resultStr = response.body();
				LogUtils.getLLM().info(resultStr);
				JSONObject resultJson = JSONObject.parseObject(resultStr);
				JSONArray choices = resultJson.getJSONArray("choices");
				JSONObject choicesOne = choices.getJSONObject(0);
				JSONObject message = choicesOne.getJSONObject("message");
				String  markdown = message.getString("content");
				Parser parser = Parser.builder().build();
		        HtmlRenderer renderer = HtmlRenderer.builder().build();
		        Node document = parser.parse(markdown);
		        result =  renderer.render(document);
			}else {
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
			LogUtils.getLLM().error(e.getMessage(),e);
		}
		return result;
	}
	
	private static List<String> empty(){
		List<String> list = new ArrayList<String>();
		list.add("DONE");
		return list;
	}
	
	private static List<String> getMsgs(String content) {
		String[] array = content.split("\n");
		List<String> list = new ArrayList<String>();
		for(String str:array) {
			if(str.startsWith("data")) {
				String rs = str.substring(5);
				if(rs.contains("DONE")){
					list.add("DONE");
				}else {
					JSONObject resultJson = JSONObject.parseObject(rs);
					JSONArray choices = resultJson.getJSONArray("choices");
					JSONObject choicesOne = choices.getJSONObject(0);
					JSONObject message = choicesOne.getJSONObject("delta");
					String c = message.getString("content");
					if(c!=null) {
						list.add(c);
					}
				}
			}else {
				
			}
		}
		return list;
	}	
	
	
	public static void main(String[] args) {
		System.out.println(deepseek32B("写一篇作文","主题i love you"));
	}
}
