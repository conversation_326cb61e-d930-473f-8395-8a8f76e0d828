<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="feeEditForm" autocomplete="off" data-text-model="true">
	     	    <input type="hidden" id="itemId" value="${param.itemId}"/>
				<table class="table table-vzebra">
			        <tbody>
		                <tr class="travelType">
		                	<td style="width: 70px;">出发日期</td>
		                	<td style="width: 30%">
								<input type="text" class="form-control input-sm" name="START_DATE"/>
		                	</td>
		                	<td style="width: 70px;">出发城市</td>
		                	<td>
								<input type="text" class="form-control input-sm" name="START_CITY"/>
		                	</td>
		                </tr>
		                <tr class="travelType">
		                	<td>到达日期</td>
		                	<td>
								<input type="text" class="form-control input-sm" name="DEST_DATE"/>
		                	</td>
		                	<td>到达城市</td>
		                	<td>
								<input type="text" class="form-control input-sm" name="DEST_CITY"/>
		                	</td>
		                </tr>
		                <tr class="travelType">
		                	<td>费用地点</td>
		                	<td colspan="3">
								<input type="text" class="form-control input-sm" name="COST_PLACE"/>
		                	</td>
		                </tr>
		                <tr>
		                	<td>发票类型</td>
		                	<td>
								<input type="text" class="form-control input-sm" name="INVOICE_TYPE"/>
		                	</td>
		                	<td>费用月份</td>
		                	<td>
								<input type="text" class="form-control input-sm" name="BUDGET_MONTH"/>
		                	</td>
		                </tr>
		                <tr>
		                	<td>电子发票号</td>
		                	<td colspan="3">
								<input type="text" class="form-control input-sm" name="INVOICE_NO"/>
		                	</td>
		                </tr>
		                <tr>
		                	<td>内部费用描述</td>
		                	<td colspan="3">
								<textarea class="form-control input-sm" name="FEE_DESC"></textarea>
		                	</td>
		                </tr>
			        </tbody>
 				 </table>
				 <div class="layer-foot text-c">
				      <button type="button" title="关闭" class="btn btn-default btn-sm" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
       
	    var FeeEdit = {};
	    
		$(function(){
			var itemId = $('#itemId').val();
			if(itemId.length>20){
				ajax.daoCall({loading:false,controls:['BxDao.bxItemInfo'],params:{itemId:itemId}},function(rs){
					var result = rs['BxDao.bxItemInfo'].data;
					fillRecord(result,'','','#feeEditForm');
					
					var feeType = result['FEE_TYPE'];
					if(feeType.indexOf('差旅费')==-1){
						$('.travelType').hide();
					}
				});
			}
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>