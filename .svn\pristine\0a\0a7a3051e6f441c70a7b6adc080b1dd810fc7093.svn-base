package com.yunqu.work.servlet.erp;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;

@WebServlet("/servlet/order/flow")
public class OrderFlowServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	public EasyResult actionForAddCheck() {
		EasyRecord record = new EasyRecord("yq_erp_order_review_result","result_id");
		try {
			JSONObject object = getJSONObject();
			String reviewId = object.getString("reviewId");
			String orderId = object.getString("orderId");
			String resultId = object.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("评审人不存在");
			}
			int checkResult = object.getIntValue("checkResult");
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("review_id", reviewId);
			record.set("order_id", orderId);
			record.set("check_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc", object.getString("checkDesc"));
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			addOrderLog(orderId,"合同评审审批","20","合同评审");
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddPaymentCheck() {
		EasyRecord record = new EasyRecord("yq_erp_order_payment_result","result_id");
		try {
			JSONObject object = getJSONObject();
			String paymentId = object.getString("paymentId");
			String orderId = object.getString("orderId");
			String resultId = object.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("评审人不存在");
			}
			int checkResult = object.getIntValue("checkResult");
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("payment_id", paymentId);
			record.set("order_id", orderId);
			record.set("check_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc", object.getString("checkDesc"));
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			addOrderLog(orderId,"合同评审审批","20","合同评审");
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForReviewApply() {
		JSONObject object = getJSONObject();
		String id = RandomKit.uniqueStr();
		String orderId = object.getString("orderId");
		String userIds = object.getString("userIds");
		String reviewLevel = object.getString("reviewLevel");
		String userNames = object.getString("userNames");
		EasyRecord record = new EasyRecord("yq_erp_order_review","review_id");
		record.set("apply_time", EasyDate.getCurrentDateString());
		record.set("apply_by", getUserId());
		record.set("last_review_date", object.getString("lastReviewDate"));
		record.set("review_level", reviewLevel);
		record.set("review_no", object.getString("reviewNo"));
		record.set("remark", object.getString("remark"));
		record.set("order_id", orderId);
		record.set("check_ids", userIds);
		record.set("check_names", userNames);
		record.set("create_name", getUserName());
		record.setPrimaryValues(id);
		try {
			this.getQuery().save(record);
			this.getQuery().executeUpdate("update yq_erp_order set review_id = ?,last_follow_label = '采购评审',last_follow_time = ?  where order_id = ?",id,EasyDate.getCurrentDateString(),orderId);
			
			String[] userIdsArray = userIds.split(",");
			for(int i=0;i<userIdsArray.length;i++) {
				EasyRecord resultRecord = new EasyRecord("yq_erp_order_review_result","result_id");
				String userId =userIdsArray[i];
				resultRecord.set("result_id", RandomKit.uniqueStr());
				resultRecord.set("review_id", id);
				resultRecord.set("order_id", orderId);
				resultRecord.set("check_id", userId);
				resultRecord.set("check_result", 0);
				resultRecord.set("check_line", 0);
				resultRecord.set("check_desc","");
				resultRecord.set("create_time",EasyDate.getCurrentDateString());
				this.getQuery().save(resultRecord);
			}
			this.getQuery().executeUpdate("update yq_erp_order_review_result t1,"+Constants.DS_MAIN_NAME+".easi_user t2 set t1.check_name = t2.username where t1.check_id = t2.user_id and t1.review_id = ?", id);
			this.getQuery().executeUpdate("update yq_erp_order_review_result t1,yq_erp_order_check_staff t2 set t1.check_line = t2.check_line where t1.check_id = t2.user_id and t1.review_id = ?", id);
		
			addOrderLog(orderId,"发起合同评审","20","合同评审");
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForPaymentApply() {
		JSONObject object = getJSONObject();
		String id = RandomKit.uniqueStr();
		String orderId = object.getString("orderId");
		String userIds = object.getString("userIds");
		String paymentLevel = object.getString("paymentLevel");
		String userNames = object.getString("userNames");
		EasyRecord record = new EasyRecord("yq_erp_order_payment","payment_id");
		record.set("apply_time", EasyDate.getCurrentDateString());
		record.set("apply_by", getUserId());
		record.set("last_do_date", object.getString("lastDoDate"));
		record.set("payment_level", paymentLevel);
		record.set("payment_no", object.getString("paymentNo"));
		record.set("remark", object.getString("remark"));
		record.set("order_id", orderId);
		record.set("check_ids", userIds);
		record.set("check_names", userNames);
		record.set("create_name", getUserName());
		record.setPrimaryValues(id);
		try {
			this.getQuery().save(record);
			this.getQuery().executeUpdate("update yq_erp_order set payment_id = ?,last_follow_label = '付款申请',last_follow_time = ?  where order_id = ?",id,EasyDate.getCurrentDateString(),orderId);
			
			String[] userIdsArray = userIds.split(",");
			for(int i=0;i<userIdsArray.length;i++) {
				EasyRecord resultRecord = new EasyRecord("yq_erp_order_payment_result","result_id");
				String userId =userIdsArray[i];
				resultRecord.set("result_id", RandomKit.uniqueStr());
				resultRecord.set("payment_id", id);
				resultRecord.set("order_id", orderId);
				resultRecord.set("check_id", userId);
				resultRecord.set("check_result", 0);
				resultRecord.set("check_line", 0);
				resultRecord.set("check_desc","");
				resultRecord.set("create_time",EasyDate.getCurrentDateString());
				this.getQuery().save(resultRecord);
			}
			this.getQuery().executeUpdate("update yq_erp_order_payment_result t1,"+Constants.DS_MAIN_NAME+".easi_user t2 set t1.check_name = t2.username where t1.check_id = t2.user_id and t1.payment_id = ?", id);
			this.getQuery().executeUpdate("update yq_erp_order_payment_result t1,yq_erp_order_check_staff t2 set t1.check_line = t2.check_line where t1.check_id = t2.user_id and t1.payment_id = ?", id);
			
			addOrderLog(orderId,"发起付款申请","30","付款申请");
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	private void addOrderLog(String orderId,String followContent,String followState,String followStateLabel) {
		EasyRecord model = new EasyRecord("YQ_ERP_ORDER_FOLLOW","FOLLOW_ID");
		model.set("FOLLOW_DATE", EasyCalendar.newInstance().getDateInt());
		model.set("ADD_TIME", EasyDate.getCurrentDateString());
		model.set("ADD_BY", getUserId());
		model.set("USER_NAME", getUserName());
		model.set("FOLLOW_STATE", followState);
		model.set("FOLLOW_STATE_LABEL", followStateLabel);
		model.set("FOLLOW_CONTENT", followContent);
		try {
			this.getQuery().executeUpdate("update yq_erp_order set last_follow_content = ?,last_follow_time = ?,last_follow_label = ? where order_id = ?",followContent,EasyDate.getCurrentDateString(),followStateLabel,orderId);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
}
