package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.kit.PathKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.template.Engine;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.utils.EmailUtils;

public class EmailService extends AppBaseService{
	private static class Holder{
		private static EmailService service=new EmailService();
	}
	public static EmailService getService(){
		return Holder.service;
	}
	public EmailService(){
		
	}
	public void sendWeeklyEmail(MessageModel model){
		model.setTplName("weekly.html");
		JSONObject data = model.getData();
		JSONObject rs=new JSONObject();
		Set<String> set = data.keySet();
		for(String key:set){
			String val = data.getString(key);
			if(StringUtils.notBlank(val)){
				rs.put(key, val.replaceAll("\r\n", "<br>"));
			}
		}
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(rs);
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	public void sendProjectWeeklyEmail(MessageModel model){
		model.setTplName("projectWeekly.html");
		JSONObject data = model.getData();
		JSONObject rs=new JSONObject();
		Set<String> set = data.keySet();
		for(String key:set){
			String val = data.getString(key);
			if(StringUtils.notBlank(val)){
				rs.put(key, val.replaceAll("\r\n", "<br>"));
			}
		}
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(rs);
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	public void sendTaskEmail(MessageModel model){
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(model.getData());
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	public void sendNoticeWriteWeeklyEmail(MessageModel model){
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate("weeklyNotice.html").renderToString(model.getData());
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	public void sendTaskCommentEmail(MessageModel model){
		Map<String,String> map=new HashMap<String, String>();
		map.put("title", model.getTitle());
		map.put("desc", model.getDesc());
		model.setTitle("【云趣协同】新的评论|"+model.getTitle());
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(map);
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	
	public void sendEmail(MessageModel model){
		String userId=model.getReceiver();
		String email=getUserEmails(userId);
		if(StringUtils.notBlank(email)){
			String userName=getUserName(model.getSender());
			this.asynMail(userName,email,getUserEmails(model.getCc()),model.getTitle(),model.getDesc());
			System.out.println("senderName:"+userName);
		}
	}
	private void asynMail(String userName,String mailto,String cc,String title,String desc) {
		if(!ServerContext.isLoginAuth())return;
		new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					EmailUtils.sendMsgMail(userName,mailto,cc,title, desc);
					getLogger().info(userName+" send email:"+mailto+"----"+title+"----"+desc);
					Thread.sleep(1000*15);
				} catch (Exception e) {
					Thread.currentThread().interrupt();
					getLogger().error(e.getMessage(),e);
				}
			}
		}).start();
	}
	public String getUserName(String userId){
		try {
			String userName=this.getQuery().queryForString("select USERNAME from "+Constants.DS_MAIN_NAME+".EASI_USER WHERE USER_ID = ?",userId);
			return userName;
		} catch (SQLException e) {
			return "";
		}
	}
	private String getUserName(String... userIds){
		if(userIds==null||userIds.length==0)return "";
		JSONObject result=null;//EhcacheKit.get(CacheTime.CacheName.hour.get(), "userList");
		if(result==null){
			EasyQuery query=ServerContext.getAdminQuery();
			String sql="select USER_ID,USERNAME from easi_user where STATE = 0";
			try {
				List<EasyRow> list=query.queryForList(sql);
				if(list!=null){
					Map<String, String> dict = new LinkedHashMap<String, String>();
					for(EasyRow row:list){
						String userId=row.getColumnValue(1);
						String userName=row.getColumnValue(2);
						dict.put(userId, userName);
					}
					result=new JSONObject();
					result.put("data",dict);
					//EhcacheKit.put(CacheTime.CacheName.hour.get(), "userList",result);
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		JSONObject data=result.getJSONObject("data");
		if(userIds.length==1){
			return data.getString(userIds[0]);
		}else{
			String sb=new String();
			for(String id:userIds){
				sb=sb+data.getString(id)+",";
			}
			sb = sb.substring(0,sb.length() - 1);
			return sb.toString();
		}
	}
	public String getUserEmail(String userId){
		try {
			String email=this.getQuery().queryForString("select EMAIL from "+Constants.DS_MAIN_NAME+".EASI_USER WHERE USER_ID = ?",userId);
			return email;
		} catch (SQLException e) {
			return "";
		}
	}
	private String getUserEmails(String... userIds){
		if(userIds==null||userIds.length==0)return "";
		JSONObject result=null;//EhcacheKit.get(CacheTime.CacheName.hour.get(), "userEmail");
		if(result==null){
			result=new JSONObject();
			EasyQuery query=ServerContext.getAdminQuery();
			String sql="select USER_ID,EMAIL from easi_user where STATE = 0";
			try {
				List<EasyRow> list=query.queryForList(sql);
				if(list!=null){
					for(EasyRow row:list){
						String userId=row.getColumnValue(1);
						String userName=row.getColumnValue(2);
						result.put(userId, userName);
					}
					//EhcacheKit.put(CacheTime.CacheName.hour.get(), "userEmail",result);
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		if(userIds.length==1){
			return result.getString(userIds[0]);
		}else{
			String sb=new String();
			for(String id:userIds){
				sb=sb+result.getString(id)+",";
			}
			sb = sb.substring(0,sb.length() - 1);
			return sb.toString();
		}
	}

}
