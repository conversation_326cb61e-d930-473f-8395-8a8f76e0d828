package com.yunqu.work.dao.erp;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="SupplierDao")
public class SupplierDao extends AppDaoContext {

	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String supplierId = param.getString("supplierId");
		return queryForRecord("select * from yq_erp_supplier where supplier_id = ?",supplierId);
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from yq_erp_supplier where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		EasySQL sql=getEasySQL("select supplier_id,name from yq_erp_supplier where 1=1");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
}
