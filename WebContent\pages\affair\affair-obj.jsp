<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">

</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="affairObjForm">
			<input name="affairId" id="affairId" value="${param.affairId}" type="hidden"/>
		    <table class="layui-hide" id="objTable"></table>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
  <script type="text/javascript">
  
		var list = {
			init:function(){
				$("#affairObjForm").initTable({
					mars:'AffairDao.affairObjList',
					id:'objTable',
					limit:20,
					even: true,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'USER_NAME',
						title: '发件人',
						align:'left',
						width:90
					},{
					    field: 'SEND_TIME',
						title: '发送时间',
						align:'center',
						width:140
					},{
					    field: 'LOOK_TIME',
						title: '查看时间',
						align:'left',
						width:140
					}
				]],done:function(){
					
			   }});
			}
	    }
		
		$(function(){
			list.init();
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>