<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>项目详情</title>
	<style>
		.layui-table-cell{padding: 0 6px;}
		.layui-table td, .layui-table th{padding: 9px 8px;}
		.n1,.n2,.n3,.n4,.n5,.n6{left: -2px!important;}
		.layui-col-md8 .layui-card-header{border-bottom: 1px solid #eee;}
		#projectUpdateDesc:focus{
			box-shadow:none;
			-webkit-box-shadow:none;
		}
		#weekly .layui-col-md4{
			margin-bottom: 10px;
		}
		#weekly .layui-col-md4 div{
			min-height:110px;
			line-height:24px;
			background-color: #f8f8f8;
			padding: 10px;
		}
		.task-tab li{font-size: 13px;}
		.task-tab li span{left: -2px!important;top: 0px!important;}
		.task-tab li .layui-badge{height: 16px;line-height: 16px;padding: 0px 5px;}
		
		.publishProjectStateBody .layui-table-cell {
			height: auto!important;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form class="ibox-index" style="padding: 0px;width: 100%;margin: 0 auto;" id="projectDetail"  data-mars="ProjectDao.record"  data-mars-prefix="project.">
			   <input type="hidden" id="projectId" value="${param.projectId}"  name="project.PROJECT_ID"/>
 		   	   <input type="hidden" value="${param.projectId}" name="projectId"/>
 		   	   <input type="hidden" value="${param.projectId}" name="fkId"/>
 		   	   <input name="taskState" id="taskState" type="hidden"/>
				<div class="layui-row layui-col-space10">
				  <div class="layui-col-md9">
				  	<div class="layui-card">
				  	<div class="layui-card-body">
						<div class="layui-tab layui-tab-brief">
						  <ul class="layui-tab-title">
						    <li class="layui-this">任务事件 <span class="layui-badge layui-bg-blue n2">0</span></li>
						    <li>项目动态  <span class="layui-badge layui-bg-blue n6">0</span></li>
						    <li>项目周报 <span class="layui-badge layui-bg-blue n1">0</span></li>
						    <li>参与人员 <span class="layui-badge layui-bg-blue n3">0</span></li>
						   <!--  <li>项目模块 <span class="layui-badge layui-bg-blue n4">0</span></li> -->
						    <li>项目文件 <span class="layui-badge layui-bg-blue n5">0</span></li>
						    <li>项目描述</li>
						  </ul>
						  <div class="layui-tab-content" style="min-height: 300px;">
						     <div class="layui-tab-item layui-show">
						     	<div class="layui-tab task-tab" lay-filter="task" data-mars="TaskDao.taskCountStat">
								  <ul class="layui-tab-title">
								    <li data-state="" class="layui-this">全部 <span class="layui-badge layui-bg-cyan ALL">0</span></li>
								    <li data-state="10">待办中 <span class="layui-badge layui-bg-orange" id="A">0</span></li>
								    <li data-state="20">进行中 <span class="layui-badge" id="B">0</span></li>
								    <li data-state="30">已完成 <span class="layui-badge layui-bg-blue" id="C">0</span></li>
								    <li data-state="40">已验收 <span class="layui-badge layui-bg-green" id="D">10</span></li>
								  </ul>
								  <div class="layui-tab-content" style="padding: 0px;">
								  	<div class="layui-tab-item layui-show"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  	<div class="layui-tab-item"></div>
								  </div>
								</div> 
							    <table class="layui-hide" id="task"></table>
						    </div>
						    <div class="layui-tab-item">
						    	<div class="layui-row layui-col-space15">
						          <div class="layui-card publishProjectState" style="margin-bottom: 0px;">
									  <div class="layui-card-body">
									  	<div style="border: 1px solid #e5e5e5;padding: 10px;">
										  	<p>
								  				<select class="form-control input-sm" name="projectState" style="width: 120px;margin-bottom: 10px;display: inline-block;margin-right: 20px;">
						                    		<option value="11">进度正常</option>
						                    		<option value="12">存在风险</option>
						                    		<option value="13">进度失控</option>
						                    		<option value="20">挂起中</option>
						                    		<option value="30">已完成</option>
							                    </select>
							                    <label class="radio radio-success radio-inline">
								   					<input type="radio" checked="checked" name="color" value="success"><span></span>
								  				 </label>
												<label class="radio radio-warning radio-inline">
								   					<input type="radio" name="color" value="warning"><span></span>
								  				 </label>
												<label class="radio radio-danger radio-inline">
								   					<input type="radio" name="color" value="danger"><span></span>
								  				 </label>
										  	</p>
										  	<textarea name="projectUpdateDesc" id="projectUpdateDesc" maxlength="300" placeholder="请输入项目状态变更理由" class="form-control input-sm" style="height: 70px;width:100%;resize:none;border: none;"></textarea>
										  	<p>
										  		&nbsp; <button type="button" onclick="publishProjectState()" class="btn btn-sm pull-right btn-info">发布</button>
										  	</p>
									  	</div>
									  </div>
									</div>
									<div class="layui-card">
									  <div class="layui-card-body publishProjectStateBody" style="padding-top: 0px;">
									  		<table class="layui-table" id="prjectStateTable"></table>
									  </div>
									</div>
          						</div>
						    </div>
						     <div class="layui-tab-item">
							    <table class="layui-hide" id="weekly"></table>
						  		<!-- <div id="weekly" class="mt-15">
						  			<div class="layui-row layui-col-space30">
									    <div class="layui-col-md4">
									      	<div>
									      		<p>2020 - 5周 (2020-1-27 ~ 2020-2-2) 周报</p>
									      		<p class="f-13">这周还未填写周报哦--赶紧把工作记录下来吧!</p>
									      		<p class="mt-10"><button type="button" onclick="addWeekly();" class="btn btn-sm btn-info btn-outline">写周报</button></p>
									      	</div>
									    </div>
									    <div class="layui-col-md4">
									      	<div>
									      		<p>2020 - 5周 (2020-1-27 ~ 2020-2-2) 周报</p>
									      		<p class="f-13">这周还未填写周报哦--赶紧把工作记录下来吧!</p>
									      		<p class="mt-10"><button type="button" class="btn btn-sm btn-info btn-outline">写周报</button></p>
									      	</div>
									    </div>
									    <div class="layui-col-md4">
									      	<div>
									      		<p>2020 - 5周 (2020-1-27 ~ 2020-2-2) 周报</p>
									      		<p class="f-13">这周还未填写周报哦--赶紧把工作记录下来吧!</p>
									      		<p class="mt-10"><button type="button" class="btn btn-sm btn-info btn-outline">写周报</button></p>
									      	</div>
									    </div>
									    <div class="layui-col-md4">
									      	<div>
									      		<p>2020 - 5周 (2020-1-27 ~ 2020-2-2) 周报</p>
									      		<p class="f-13">审批状态:未阅 &nbsp;<span class="pull-right">发布时间：2020-02-02</span></p>
									      		<p class="mt-15"><button type="button" class="btn btn-sm btn-warning btn-outline">查看周报</button></p>
									      	</div>
									    </div>
									    <div class="layui-col-md4">
									      	<div>
									      		<p>2020 - 5周 (2020-1-27 ~ 2020-2-2) 周报</p>
									      		<p class="f-13">这周还未填写周报哦--赶紧把工作记录下来吧!</p>
									      		<p class="mt-15"><button type="button" class="btn btn-sm btn-info btn-outline">写周报</button></p>
									      	</div>
									    </div>
									    <div class="layui-col-md4">
									      	<div>
									      		<p style="font-size: 16px;color: #1E9FFF;">查看全部</p>
									      		<p>&nbsp;</p>
									      		<p>&nbsp;</p>
									      	</div>
									    </div>
									  </div>
						  		</div> -->
						     </div>
						    <div class="layui-tab-item">
						    	<table class="layui-hide" id="teams"></table>
						    </div>
						    <!-- <div class="layui-tab-item">
						    	<table class="layui-hide" id="modules"></table>
						    </div> -->
						    <div class="layui-tab-item">
						    	<div id="fileList">
						    		<table class="layui-table">
									  <colgroup>
									    <col>
									    <col width="180">
									    <col width="120">
									  </colgroup>
									  <thead>
									    <tr>
									      <th>文件名</th>
									      <th>上传时间</th>
									      <th>上传人</th>
									    </tr> 
									  </thead>
									  <tbody data-template="project-template-files" data-mars="FileDao.fileListDetail">

									  </tbody>
									</table>
						    	</div>
						    </div>
						    <div class="layui-tab-item">
						    	<div name="project.PROJECT_DESC" data-fn="contentFn" style="padding: 10px;border: 1px solid #eee;"></div>
						    </div>
						  </div>
						</div>
					 </div>
				  </div>
				  </div>
				  <div class="layui-col-md3">
				   <div class="layui-card">
			  			<div class="layui-card-header">常用操作</div>
					  	<div class="layui-card-body">
					  	    <div class="layui-row layui-col-space15">
							    <div class="layui-col-md4">
							  		 <button type="button" onclick="addWeekly()" class="btn btn-default  btn-link">+项目周报</button>
							    </div>
							    <EasyTag:hasRole roleId="PROJECT_MANAGER">
								    <div class="layui-col-md4">
								  		 <button type="button" onclick="editContract()" class="btn btn-warning btn-link">项目合同</button>
								    </div>
							    </EasyTag:hasRole>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="addTask()" class="btn btn-default btn-link">发起任务</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="addBatchTask()" class="btn btn-default btn-link">+批量任务</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="projectKanban()" class="btn btn-default btn-link">项目看板</button>
							    </div>
							    <div class="layui-col-md4">
								  	 <button type="button" onclick="prjectWorkhour()" class="btn btn-default btn-link">项目工时</button>
							    </div>
						     </div>
					  	</div>
					</div>
			  		<div class="layui-card">
			  			<div class="layui-card-header">项目信息  <button type="button" onclick="favoriteProject()" class="btn btn-xs btn-info btn-outline pull-right mt-10 mr-15">关注项目</button></div>
					  	<div class="layui-card-body">
					  		<table class="layui-table projectInfo" lay-skin="nob">
						        <tbody>
						           <tr>
					                    <td colspan="2">
					                        <span style="color: #5cb85c;" name="project.PROJECT_NAME"></span>
					                    </td>
						           </tr>
						           <tr>
					                    <td class="text-r" width="90px">项目编号：</td>
					                    <td><span name="project.PROJECT_NO"></span></td>
						           </tr>
						           <tr>
					                   <td class="text-r">负责人：</td>
					                   <td data-fn="getUserName" name="project.PO"></td>
						           </tr>
						            <tr>
					                    <td class="text-r">项目级别：</td>
					                    <td data-fn="projectLevel" name="project.PROJECT_LEVEL"></td>
					                </tr>
						            <tr>
					                    <td class="text-r">项目状态：</td>
					                    <td><label class="label label-info">{{call:PROJECT_STATE fn='projectState'}}</label></td>
					                </tr>
						            <tr>
					                    <td class="text-r">项目周期：</td>
					                    <td>
						                    <span name="project.BEGIN_DATE"></span> - 
						                    <span name="project.END_DATE"></span>
					                    </td>
					                </tr>
						            <tr>
						            	<td class="text-r">操作：</td>
						            	<td>
						            		<button class="btn btn-default btn-link" onclick="editProject()" type="button">修改</button>
						            		<EasyTag:hasRole roleId="PROJECT_MANAGER">
							            		<button class="btn btn-default btn-link ml-10" onclick="delProject()" type="button">删除</button>
						            		</EasyTag:hasRole>
						            	</td>
						            </tr>
						        </tbody>
			 				</table>
					  	</div>
				  	</div>
				  </div>
				</div>
				
			</form>
		<script id="project-template-files" type="text/x-jsrender">
			{{if data.length==0}}
				<tr>
					<td colspan="3">暂无数据</td>
				</tr>
			{{/if}}
			{{for data}}
				<tr>
				  <td>{{:#index+1}}、<a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}} &nbsp;</span> <span style="color:#17a6f0">下载 | {{:FILE_SIZE}}<span></a></td>
				  <td>{{:CREATE_TIME}}</td>
				  <td>
						{{:USERNAME}}
					</td>
				</tr>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
	
		var record={};
		var projectId='${param.projectId}';
		
	 	$(function(){
	 		$("#projectDetail").render({success:function(result){
 				record=result['ProjectDao.record'].data;
 				if(record['PROJECT_ID']){
 					loadTaskTable();
			 		loadWeekly();
			 		projectTeams();
			 		projectStateLog();
			 		var fileListDetail = result['FileDao.fileListDetail'];
			 		$(".n5").text(fileListDetail.data.length);
			 		
			 		var taskCountStat = result['TaskDao.taskCountStat'];
			 		fillRecord(taskCountStat['data']);
			 		
 				}else{
 					popup.closeTab();
 				}
	 		},then:function(){
	 			$(window).resize();
	 		}});
	 	});
	 	layui.use('element',function(){
			var element=layui.element;
			element.on('tab(task)', function(elem){
				var  state=$(this).data('state');
				$("#taskState").val(state);
				reloadTaskList();
			});
		  });
	 	function reloadTaskList(){
	 		$("#projectDetail").queryData({id:'task'});
	 	}
	 	function favoriteProject(){
	 		ajax.remoteCall("${ctxPath}/servlet/project?action=addFavorite",{fkId:'${param.projectId}'},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg);
				}else{
					layer.msg(result.msg,{icon: 7,time:1200});
				}
			},{loading:false});
	 	}
	 	function addWeekly(){
	 		var projectName=$("[name='project.PROJECT_NAME']").text();
		    popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/weekly/project-weekly-edit.jsp',title:'新增项目周报',data:{projectId:'${param.projectId}',projectName:projectName}});
	 	}
	 	function editWeekly(data){
	 		var weeklyId = data['WEEKLY_ID'];
	 		if(!weeklyId){
	 			weeklyId=data;
	 		}
		    popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/weekly/project-weekly-edit.jsp',title:'修改项目周报',data:{projectId:'${param.projectId}',weeklyId:weeklyId}});
	 	}
	 	function detailWeekly(data){
	 		
	 	}
	 	function prjectWorkhour(){
		    popup.layerShow({type:2,anim:0,scrollbar:false,offset:'r',area:['768px','100%'],url:'${ctxPath}/pages/project/workhour/project-workhour.jsp',title:'项目工时',data:{projectId:projectId}});
	 	}
	 	function reloadProjectWeekly(){
	 		$("#projectDetail").queryData({id:'weekly'});
	 	}
		function loadWeekly(){
			$("#projectDetail").initTable({
				mars:'ProjectDao.projectWeeklys',
				id:"weekly",
				data:{},
				cols: [[
				 {type:'checkbox'},
	             {
					title: '序号',
					type:'numbers'
				 },{
				    field: 'USERNAME',
					title: '填写人',
					width:90,
					align:'left'
				},{
				    field: 'DEPTS',
					title: '所属部门',
					width:90,
					align:'left'
				},{
				    field: 'CREATE_TIME',
					title: '填写时间',
					width:160,
					align:'left'
				},{
				    field: 'TITLE',
					title: '周报名称',
					align:'left'//,
				//	event:'detailWeekly',
				//	style:'color:#1E9FFF;cursor:pointer'
				},{
				    field: '',
					title: '操作',
					align:'left',
					width:80,
					templet:function(row){
						var creator = row['CREATOR'];
						if(getCurrentUserId()==creator){
							return '<a href="javascript:;" onclick="editWeekly(\''+row['WEEKLY_ID']+'\')" class="layui-text">修改</a>';
						}else{
							return '';
						}
					}
				}
				]],done:function(result){
					table.resize('weekly');
					$(".n1").text(result.totalRow);
				}});
			
		}
		function projectStateLog(){
			$("#projectDetail").initTable({
				mars:'ProjectDao.projectStateLog',
				id:"prjectStateTable",
				cellMinWidth:100,
				data:{},
				cols: [[
				 {type:'checkbox'},
	             {
					title: '序号',
					type:'numbers'
				 },{
				    field: 'UPDATE_REASON',
					title: '更新内容',
					minWidth:400,
					templet:'<div><span class="text-{{d.FONT_COLOR}}">{{d.UPDATE_REASON}}</span></div>'
				},{
				    field: 'UPDATE_STATE',
					title: '更新状态',
					width:80,
					align:'center',
					templet:function(row){
						return projectState(row.UPDATE_STATE);
					}
				},{
				    field: 'UPDATE_BY',
					title: '更新人',
					align:'center',
					width:90,
					templet:function(row){
						return getUserName(row.UPDATE_BY);
					}
				},{
				    field: 'UPDATE_TIME',
					title: '提交时间',
					align:'center',
					width:150,
					align:'center'
				}
				]],done:function(result){
					$(".n6").text(result.totalRow);
				}});
			
		}
		var isLoadTask=false;
		function loadTaskTable(){
			$("#projectDetail").initTable({
				mars:'TaskDao.projectTaskList',
				id:"task",
				data:{},
				cols: [[
				 {type:'checkbox'},
	             {
					title: '序号',
					type:'numbers'
				 },{
				    field: 'TASK_NAME',
					title: '任务名称',
					align:'left',
					event:'taskDetail',
					style:'color:#1E9FFF;cursor:pointer'
				},{
				    field: 'TASK_STATE',
					title: '任务状态',
					align:'left',
					width:90,
					templet:function(row){
						return taskStateLabel(row.TASK_STATE);
					}
				},{
				    field: 'CREATOR',
					title: '发起人',
					width:90,
					align:'left',
					templet:function(row){
						return getUserName(row.CREATOR);
					}
				},{
				    field: 'ASSIGN_USER_ID',
					title: '指派给',
					width:90,
					align:'left',
					templet:function(row){
						return getUserName(row.ASSIGN_USER_ID);
					}
				},{
				    field: 'CREATE_TIME',
					title: '创建时间',
					width:180,
					align:'left'
				}
				]],done:function(result){
					table.resize('task');
					if(isLoadTask==false){
						$(".ALL,.n2").text(result.totalRow);
					}
					isLoadTask=true;
				}});
			
		}
		function  taskDetail(data){
			var status=0;
			if(data['CREATOR']==getCurrentUserId()){
				status=2;
			}
			if(data['ASSIGN_USER_ID']==getCurrentUserId()){
				status=1;
			}
			popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:status}});
		}
		function projectModule(){
			$("#projectDetail").initTable({
				mars:'ProjectDao.projectModule',
				height:400,
				id:"modules",
				page:false,
				data:{},
				edit:'editModule',
				cols: [[
				 {type:'numbers'},
	             {
            	 	field: 'MODULE_NAME',
					title: '模块名称',
					edit:'text',
					align:'center'
				 },
				 {
            	 	field: 'REMARK',
					title: '备注',
					edit:'text',
					align:'center'
				},
				{
				    field: 'UPDATE_BY',
					title: '更新人',
					align:'left',
					templet:function(row){
						return getUserName(row.UPDATE_BY);
					}
				},{
				    field: 'UPDATE_TIME',
					title: '更新时间',
					align:'left'
				}
				]],done:function(result){
					$(".n4").text(result.total);
				}});
		}
		function projectTeams(){
			$("#projectDetail").initTable({
				mars:'ProjectDao.projectTeams',
				id:"teams",
				data:{},
				edit:'editTeam',
				limit:15,
				cols: [[
				 {type:'numbers'},
	             {
            	 	field: 'USERNAME',
					title: '姓名',
					align:'left',
					templet:'<div>{{d.DEPTS}}.{{d.USERNAME}}</div>'
				 },{
				    field: 'MOBILE',
					title: '手机号码',
					align:'left'
				},{
				    field: 'EMAIL',
					title: '邮箱',
					align:'left'
				},{
				    field: 'ROLE_NAME',
					title: '角色',
					edit:'text',
					align:'left'
				},{
				    field: 'REMARK',
					title: '备注',
					edit:'text',
					align:'left'
				},{
				    field: 'JOIN_TIME',
					title: '加入时间',
					align:'left',
					hide:true
				}
				]],done:function(result){
					$(".n3").text(result.totalRow);
					table.resize("teams");
				}});
		}
		function editTeam(obj){
			var data=obj.data;
		    if(data.USER_ID==getCurrentUserId()||record.PO==getCurrentUserId() || isSuperUser){
				var params={PROJECT_ID:data.PROJECT_ID,USER_ID:data.USER_ID,ROLE_NAME:data.ROLE_NAME,REMARK:data.REMARK};
				ajax.remoteCall("${ctxPath}/servlet/project?action=updateTeam",params,function(result) { 
					if(result.state != 1){
						layer.alert(result.msg,{icon: 7});
					}
				},{loading:false});
		    }else{
		    	layer.msg("修改无效,您无权修改!");
		    }
		}
		function contentFn(val){
			if(val){
				return val;
			}else{
				return '暂无';
			}
		}
		function editModule(obj){
			var data=obj.data;
		    if(record.PO==getCurrentUserId() || isSuperUser){
				var params={PROJECT_ID:'${param.projectId}',MODULE_ID:data.MODULE_ID,MODULE_NAME:data.MODULE_NAME,REMARK:data.REMARK};
				ajax.remoteCall("${ctxPath}/servlet/project?action=updateModule",params,function(result) { 
					$("#projectDetail").queryData({id:'modules'});
					if(result.state != 1){
						layer.alert(result.msg,{icon: 7});
					}
				},{loading:false});
		    }else{
		    	layer.msg("修改无效,您无权修改!");
		    }
		}
		function delProject(){
				layer.confirm("确定删除吗?",{offset:'30px',icon:7},function(){
					ajax.remoteCall("${ctxPath}/servlet/project?action=delProject",{projectId:projectId},function(result) { 
						popup.closeTab();
						if(result.state != 1){
							layer.alert(result.msg,{icon: 7});
						}else{
							layer.msg(result.msg);
						}
					});
				});
		}
		function reloadProjectList(){
			$("#projectDetail").render();
		}
		function editProject(data){
			popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'编辑项目',data:{projectId:projectId}});
		}
		function projectKanban(){
			popup.openTab({id:'projectKanban',url:'${ctxPath}/pages/kanban/kanban-index.jsp',title:'项目看板',data:{projectId:'${param.projectId}'}});
		}
		function addBatchTask(){
			var projectName=$("[name='project.PROJECT_NAME']").text();
			popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-batch-add.jsp',title:'安排任务',closeBtn:0,data:{projectId:'${param.projectId}',projectName:projectName}});
		}
		function editContract(){
			popup.layerShow({type:2,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/contract/project-contract-edit.jsp',title:'编辑合同',data:{contractId:'${param.projectId}'}});
		}
		var addTask=function(){
			popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务',data:{projectId:projectId}});
		}
		function publishProjectState(){
			var data = form.getJSONObject(".publishProjectState");
			data['projectId']=projectId;
			var projectUpdateDesc=$("#projectUpdateDesc").val();
			if(projectUpdateDesc==''){
				layer.msg("描述理由不能为空.");
				return;
			}
			if(projectUpdateDesc.length<10){
				layer.msg("描述理由不能少于十个字");
				return;
			}
			ajax.remoteCall("${ctxPath}/servlet/project?action=updateProjectState",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg);
					$("#projectDetail").queryData({id:'prjectStateTable'});
				}else{
					layer.alert(result.msg,{icon: 7});
				}
			});
			
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>