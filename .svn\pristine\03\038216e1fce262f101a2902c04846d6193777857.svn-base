package com.yunqu.work.model;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.db.EasyRowMapper;

import com.yunqu.work.utils.LogUtils;


public class StaffRowMapper implements EasyRowMapper<StaffModel> {

	@SuppressWarnings("unchecked")
	@Override
	public StaffModel mapRow(ResultSet rs, int rowNum) {
		StaffModel vo = new StaffModel();
		try {
			vo.setRootId(rs.getString("ROOT_ID"));
			vo.setUserId(rs.getString("USER_ID"));
			vo.setLoginAcct(rs.getString("STAFF_NO"));
			vo.setSuperior(rs.getString("SUPERIOR"));
			vo.setpSuperior(rs.getString("P_SUPERIOR"));
			vo.setSex(rs.getString("SEX"));
			vo.setPoints(rs.getString("POINTS"));
			vo.setLevel(rs.getString("LEVEL"));
			vo.setUserName(rs.getString("USERNAME"));
			vo.setNikeName(rs.getString("NIKE_NAME"));
			vo.setDeptId(rs.getString("DEPT_ID"));
			vo.setDeptName(rs.getString("DEPTS"));
			vo.setStaffNo(rs.getString("STAFF_NO"));
			vo.setOpenId(rs.getString("OPEN_ID"));
			vo.setMobile(rs.getString("MOBILE"));
			vo.setEmail(rs.getString("EMAIL"));
			vo.setPost(rs.getString("JOB_TITLE"));
			vo.setPicUrl(rs.getString("PIC_URL"));
			vo.setJoinDate(rs.getString("JOIN_DATE"));
			vo.setWorkCity(rs.getString("OFTEN_WORK_CITY"));
			vo.setNormal(rs.getInt("STATE")==0);
		} catch (SQLException ex) {
			LogUtils.getLogger().error(ex.getMessage(),ex);
		}
		return vo;
		
	}

}
