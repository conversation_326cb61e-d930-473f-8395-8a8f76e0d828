<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>订单结果导入</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  class="form-inline"  method="post" enctype="multipart/form-data" id="importTaskForm">
         	    	<table class="layui-table layui-text">
         	    		<tr>
         	    			<td style="width:100px;">下载模板  </td>
         	    			<td>
         	    				<a href="/template/任务导入模板.xlsx" target="_blank">任务导入模板</a>
         	    			 </td>
         	    		</tr>
         	    		<tr>
         	    			<td>选择项目 </td>
         	    			<td>
         	    				 <input type="hidden" name="projectId" class="form-control input-sm">
								 <input type="text" name="projectName" onclick="singleProject(this);" readonly="readonly"  style="width: 100%;" class="form-control input-sm">
         	    			 </td>
         	    		</tr>
         	    		<tr>
         	    			<td>任务类型</td>
         	    			<td>
								<select  name="taskTypeId" data-rules="required"  data-mars="TaskDao.taskType" class="form-control" style="width: 100%;">
					                <option value="">请选择</option>
					             </select>
         	    			 </td>
         	    		</tr>
         	    		<tr>
         	    			<td>负责人(当Excel列为空取这个)</td>
         	    			<td>
         	    				 <input type="hidden" name="assignUserId" class="form-control input-sm">
								 <input type="text" onclick="singleUser(this)" placeholder="可不填" class="form-control input-sm" style="width: 100%;">
         	    			 </td>
         	    		</tr>
         	    		<tr>
         	    			<td style="width: 200px;">选择Excel文件  </td>
         	    			<td>
         	    				<input class="form-control" type="file" id="file" name="file" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"/>
         	    			 </td>
         	    		</tr>
         	    	</table>
         	    	<div class="layer-foot text-c">
	          	    	<button class="btn btn-sm btn-info" type="button" onclick="upload()"> 确认上传  </button>
         	    	</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">

		function upload(){
			var projectId = $("#importTaskForm [name='projectId']").val();
			if(projectId==''){
				layer.msg('请选择项目',{icon:7,offset:'20px',time:1200});
				return;
			}
			var taskTypeId = $("#importTaskForm [name='taskTypeId']").val();
			if(taskTypeId==''){
				layer.msg('请选择类型',{icon:7,offset:'20px',time:1200});
				return;
			}
			var formData = new FormData($("#importTaskForm")[0]); 
			$.ajax({  
		          url: '${ctxPath}/servlet/task/ext?action=upload&'+$("#importTaskForm").serialize(),  
		          type: 'POST',
		          data: formData,async: true,cache: false,contentType: false,processData: false,  
		          success: function (result) {
		        	  layer.closeAll('dialog');
		        	  if(result.state==1){
			        	  var result=result.data;
			        	  layer.msg('操作成功.',function(){
			        		  layer.closeAll();
			        		  reloadTaskList('2');
			        	  });
		        	  }else{
		        		  layer.alert(result.msg);
		        	  }
		        	  $('#file').val('');
		          },error:function(){
		        	  layer.closeAll('dialog');
		          },beforeSend:function(){
	        	  	  layer.msg('数据解析中...', {icon: 16 ,shade: 0.01,time:0,offset:'180px'});
		          } 
		     }); 
		}
		
		$(function(){
			$('#importTaskForm').render();
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>