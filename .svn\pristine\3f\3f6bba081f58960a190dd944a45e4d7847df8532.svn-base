<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>文档库管理</title>
	<style>
		li::marker {
		  content: '▸';
		  color: red;
		}
	    header{
	    	height: 54px;
	    	line-height:38px;
	    	width: 100%;
	    	border-bottom: 1px solid #ccc;
	    	background-color: #fff;
	    	position: fixed;
	    	padding:0.8rem;
	    	top: 0;
	    	left: 0;
	    	right: 0;
	    	z-index: 900;
	    }
	    .left{
	    	position:absolute;
	    	top:54px;
	    	left:0px;
	    	bottom:0;
	    	z-index:301;
	    	border-right:1px solid #ddd;
			width: 280px;
			background-color: #fff;
			overflow: scroll;
		}
		.left #treebox{
			overflow-y: scroll;
			overflow-x: auto;
			min-height: calc(100vh - 60px)
		}
		.right{
			position:absolute;
			left:280px;
			background-color:#fff;
			top:55px;
			bottom:0;
			overflow-y:auto;
			right:0;
			z-index:308;
		}
		
		 .catalog-ul {
		    margin: 0;
		    padding-left: 10px;
		    font-size: 13px;
		    font-weight: 400;
		    line-height: 28px;
		}
		
		.catalog-li {
		    list-style: initial;
		    list-style-position: inside;
		    white-space: nowrap;
		    text-overflow: ellipsis;
		}
		
		.catalog-li a {
		    padding: 6px 0;
		    outline: none;
		    color: #40485b;
		}
		
		.catalog-li.active a {
		    color: #fe7300;
		}
		.catalog-li a:hover {
		    color: #797e8c;
		} 

		.right .ibox-content{
			box-shadow: none;
			border: none;
		}
		.right .layer-foot{
			display:none;
			position: inherit;
		}
		.right textarea{
			width: 100%;
		}
		
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input type="hidden" name="folderCode" id="folderCode" value="${param.folderCode}"/>
			<header>
				<span class="ml-15">知识库</span>
			</header>
			<div id="treebox" class="left folder-tree">
				<mytree :tree="tree" :files="files"></mytree>
			</div>
			<div class="right">
				<div style="padding: 30px;font-size: 20px;">
					 请点击左侧文档标题
				</div>
			</div>
		</form>
		
		<!-- 树模板 -->
	<script type="text/x-template" id="treeTmpl">
		<div>
		<ul  v-for="item,index in tree" class="catalog-ul">
		  <li v-if="(item.children && item.children.length>0) || (item.FOLDER_ID && files[item.FOLDER_ID])" class="catalog-li">
			 <a style="font-weight:700;" href="javascript:;">{{item.title}}<span v-if="item.FOLDER_ID && files[item.FOLDER_ID]">({{files[item.FOLDER_ID].length}})</span> </a>
			 <ul class="catalog-ul">
		      <li v-if="item.FOLDER_ID && files[item.FOLDER_ID]" v-for="file,fileIdx in files[item.FOLDER_ID]">
		      	<a href="javascript:;" @click.stop="openFile(file,item)" :title="file.TITLE">{{file.TITLE}}</a>
		      </li>
		      <mytree :tree="item.children" :files="files"></mytree>
		    </ul>
		  </li>
		</ul>
		</div>
	</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script src="https://cdn.bootcdn.net/ajax/libs/vue/2.6.10/vue.min.js"></script>
	<script type="text/javascript">
		
		Vue.component('mytree',{
			name:'mytree',
		    template:'#treeTmpl',
		    props:['tree','files'],
		    methods:{
		    	openFile:function(fileItem,parentItem){
		    		var docId = fileItem.DOC_ID;
		    		$.get('/yq-work/pages/doc/doc-detail.jsp',{docId:docId,isDiv:1},function(result){
	  					$(".right").html(result);
    				});

		    	}
		    }
		});
	
		function folderJsonTransfer(treeJson,folderJson){
			//文件对象
			var docObj = {}
			for(let i = 0;i<folderJson.length;i++){
				if(typeof(docObj[FOLDER_ID])=='undefined') docObj[FOLDER_ID] = []
				docObj[FOLDER_ID].push(folderJson[i])
			}
			//深度遍历文件夹树
			for(let i = 0;i<treeJson.length;i++){
				
			}
		}
	
		$(function(){
			var folderCode = $('#folderCode').val();
			ajax.remoteCall("${ctxPath}/webcall?action=FolderDao.docsFolder",{folderCode:folderCode},function(rs) { 
				var result= rs.data;
				var treeData = dataToTree(result,{idFiled: 'FOLDER_ID', textFiled: 'FOLDER_NAME', parentField: 'P_FOLDER_ID', childField: '',def:{spread:false}, map: {FOLDER_ID: 'id', FOLDER_NAME: 'title' } });
				var docs = rs.docs;
				 var vueTree = new Vue({
					el:'#treebox',
					data:function(){
						return{
							tree:[],
							files:{}
						}
					},
					mounted:function(){
						this.tree = treeData;

						this.files = this.folderJsonTransfer(docs);
						this.$nextTick(function(){
							layui.use(['element'], function() {

							})
						})
					},
					methods:{
						folderJsonTransfer(folderJson){
							//文件对象
							var docObj = {}
							for(let i = 0;i<folderJson.length;i++){
								let FOLDER_ID = folderJson[i].FOLDER_ID
								if(typeof(docObj[FOLDER_ID])=='undefined') docObj[FOLDER_ID] = []
								docObj[FOLDER_ID].push(folderJson[i])
							}
							return docObj;

						}
					}
				});
			});
		});
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>