package com.yunqu.work.servlet;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.Globals;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
/**
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/kaoqin/*")
@MultipartConfig
public class KaoqinServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private String setDateFormat(String str,boolean split){
		String[] array=str.split("-");
		StringBuffer buffer=new StringBuffer(array[0]);
		if(split){
			buffer.append("-");
		}
		if(array[1].length()==1){
			buffer.append("0"+array[1]);
		}else{
			buffer.append(array[1]);
		}
		if(split){
			buffer.append("-");
		}
		if(array[2].length()==1){
			buffer.append("0"+array[2]);
		}else{
			buffer.append(array[2]);
		}
		return buffer.toString();
	}
	
	public EasyResult actionForUploadFile()  throws SQLException, IOException, ServletException {
		EasyResult result = new EasyResult();
		try {
			Part part=this.getFile("kqData");
			String filename =part.getSubmittedFileName();
			String separator = File.separator;
			if(StringUtils.isNotBlank(filename)&&filename.startsWith(separator)){
				filename = filename.substring(filename.lastIndexOf(separator),filename.length());
			}
			String path=Globals.SERVER_DIR+File.separator+"kaoqin";
			if (!new File(path).exists()) {
				new File(path).mkdirs();
			}
			File tmpWarFile = new java.io.File(path + separator + System.currentTimeMillis()+"_" + filename);
			if (tmpWarFile.exists()) {
				tmpWarFile.delete();
			}
			
			FileKit.saveToFile(part.getInputStream(), tmpWarFile.getAbsolutePath());
			
		    Workbook workbook = WorkbookFactory.create(part.getInputStream());
		    List<List<String>> list = new ArrayList<>();
	        Sheet sheet = workbook.getSheetAt(0);
	        int maxLine =sheet.getLastRowNum();
	        int lastCellNum=0;
	        for (int ii = 1; ii <= maxLine; ii++) {
	            List<String> rows = new ArrayList<>();
	            Row row = sheet.getRow(ii);
	            if(ii==1){
	            	 lastCellNum=row.getLastCellNum();
	            	 if(lastCellNum!=29){
	            		 return EasyResult.fail("标准模板字段29个，您上传的模板字段"+lastCellNum+"个，不匹配!");
	            	 }
	            }
	            if(row!=null){
	            	for(int j=0;j<lastCellNum;j++){
	            		Cell cell=row.getCell(j);
	            		if(cell!=null){
	            			String val = Utils.getCellValue(cell);
	            			if(StringUtils.isBlank(val)){
	            				rows.add("");
	            			}else{
	            				rows.add(val);
	            			}
	            		}else{
	            			rows.add("");
	            		}
	            	}
	            	list.add(rows);
	            }
	      }
	      list.remove(0);
	     
	      Map<Integer,String> fields=new LinkedHashMap<Integer,String>();
	      fields.put(1, "STAFF_ID");
	      fields.put(3, "USER_NAME");
	      fields.put(5, "DK_DATE");
	      fields.put(6, "DK_RANK");
	      fields.put(9, "SIGN_IN");
	      fields.put(10, "SIGN_OUT");
	      //fields.put(12, "TRUE_TO");
	      
	      String batchId = RandomKit.uuid();
	      
          JSONArray array=new JSONArray();
		  for(int j=0;j<list.size();j++){
			List<String> strs=list.get(j);
			JSONObject jsonObject=new JSONObject(true);
			String objId=RandomKit.uuid();
			jsonObject.put("KQ_ID",objId);
			jsonObject.put("BATCH_ID",batchId);
			if(strs.size()>0){
				for(int x=0;x<20;x++){
					Set<Integer> keys=fields.keySet();
					for(Integer key : keys){
						if(key==x){
							String field=fields.get(key);
							String value=StringUtils.trimToEmpty(strs.get(x));
							jsonObject.put(field, value);
						}
						
					}
					
				}
				array.add(jsonObject);
		   }
	    }
			if(array!=null&&array.size()>0){
				EasyRecord record=new EasyRecord("YQ_KQ_DATA","KQ_ID");
				for(int i=0;i<array.size();i++){
					JSONObject cols=array.getJSONObject(i);
					//cols.put("TRUE_TO", cols.getIntValue("TRUE_TO"));
					record.setColumns(cols);
					String dateStr=setDateFormat(cols.getString("DK_DATE"),true);
					record.set("DK_DATE",dateStr );
					record.set("DATE_ID", setDateFormat(cols.getString("DK_DATE"),false));
					boolean b=this.getQuery().save(record);
					if(!b){
						getQuery().update(record);
					}
				}
			}    
			EasyRecord record=new EasyRecord("YQ_KQ_BATCH","BATCH_ID");
			record.setPrimaryValues(batchId);
			record.set("upload_time", EasyDate.getCurrentDateString());
			record.set("file_name",filename);
			record.set("EXCEL_COUNT",list.size());
			record.set("file_path",tmpWarFile.getAbsolutePath());
			record.set("upload_by",getRemoteUser());
			this.getQuery().save(record);
			
			
			result.put("state", 1);
			result.put("msg", "上传成功!");
			part.delete();
			return result;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.addFail(e.getMessage());
			return result;
		}finally {
			
		}
	}
	

}





