<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>项目反馈</title>
	<style>
		.radio.radio-inline{margin-top: 12px;}
		.publishProjectStateBody .layui-table-cell {
			padding:6px 6px;
			height: auto;
			word-break: normal;
		 	display: block;
		 	white-space: pre-wrap;
		 	word-wrap: break-word;
		 	overflow: hidden;
		}
		.select2-search__field,.select2-container{min-width: 100px!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="projectAction" class="form-inline publishProjectState">
		 <input name="projectId" type="hidden" value="${param.projectId}">
		 <input name="projectName" type="hidden" value="${param.projectName}">
		  <div class="layui-card">
			  	<div class="layui-card-body">
		  		    <div class="input-group input-group-sm" style="width: 160px;margin-bottom: 10px;">
						<span class="input-group-addon">反馈类别*</span>	
		  				<select class="form-control input-sm" name="followState" style="width: 120px;display: inline-block;">
	                  		<option value="">--请选择--</option>
	                  		<option value="进度更新">进度更新</option>
							<option value="问题报告">问题报告</option>
							<option value="风险管理">风险管理</option>
							<option value="需求变更">需求变更</option>
							<option value="技术讨论">技术讨论</option>
							<option value="质量保证">质量保证</option>
							<option value="资源分配">资源分配</option>
							<option value="决策支持">决策支持</option>
							<option value="用户反馈">用户反馈</option>
							<option value="会议纪要">会议纪要</option>
							<option value="文档更新">文档更新</option>
							<option value="其他">其他</option>
	                   </select>
				     </div>
		  		    <div class="input-group input-group-sm ml-5" style="width: 160px;margin-bottom: 10px;">
						<span class="input-group-addon">反馈日期*</span>	
						<input name="followDate" data-mars="CommonDao.today" type="date" class="form-control input-sm">
				    </div>
		  		    <div class="input-group input-group-sm ml-5" style="width:160px;margin-bottom: 10px;">
						<span class="input-group-addon">紧急程度</span>	
						<select name="level" class="form-control input-sm">
							<option value="正常">正常</option>
		  					<option value="重要">重要</option>
		  					<option value="紧急">紧急</option>
						</select>
				    </div>
		  		    <div class="input-group input-group-sm ml-5" style="width:200px;margin-bottom: 10px;">
						<span class="input-group-addon">当前是否有开发工作量</span>	
						<select name="hasDevWork" class="form-control input-sm">
							<option value="">--</option>
		  					<option value="0">是</option>
		  					<option value="1">否</option>
						</select>
				    </div>
				    <br>
		  		     <div class="input-group input-group-sm" style="margin-bottom: 10px;">
						<span class="input-group-addon">当前责任方</span>
						<!-- 多选 -->
						<select name="problemBy" multiple="multiple" class="form-control input-sm">
							<option value="">--</option>
							<option value="开发">开发</option>
							<option value="工程">工程</option>
							<option value="商务">商务</option>
							<option value="客户">客户</option>
							<option value="第三方">第三方</option>
						</select>
				     </div>
				     <div class="input-group input-group-sm ml-5" style="width:180px;margin-bottom: 10px;">
						<span class="input-group-addon">是否影响当前进度</span>	
						<select name="effectProgress" class="form-control input-sm">
							<option value="">--</option>
							<option value="0">是</option>
		  					<option value="1">否</option>
						</select>
				    </div>
				     <div class="input-group input-group-sm" style="width: 160px;margin-bottom: 10px;">
						<span class="input-group-addon">回复负责人</span>	
						<input name="stakeholderId" type="hidden">
						<input name="stakeholder" type="text" placeholder="可选" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm select-icon">
				    </div>
		  		    <div class="input-group input-group-sm ml-5" style="width:200px;margin-bottom: 10px;">
						<span class="input-group-addon">抄送人</span>	
						<input name="ccIds" type="hidden">
						<input name="ccNames" type="text" placeholder="可选"  onclick="multiUser(this);" readonly="readonly" class="form-control input-sm select-icon">
				    </div>
				    <div class="input-group input-group-sm ml-5" style="width:130px;margin-bottom: 10px;">
						<span class="input-group-addon">是否公开</span>	
						<select name="isPublic" class="form-control input-sm">
							<option value="0">是</option>
		  					<option value="1">否</option>
						</select>
				    </div>
				  	<textarea name="feedbackContent" id="feedbackContent" placeholder="请输入项目跟进内容" class="form-control input-sm" style="height: 70px;width:100%;resize:none;"></textarea>
				  	<div style="height: 30px;line-height: 30px;">
					  	<button type="button" onclick="publishProjectState()" class="btn btn-sm btn-info mt-5">发布</button>
					  	<a style="color:#0ec3fc;margin-left: 10px;" href="javascript:;" onclick="uploadFile()">上传附件</a>
					  	<small class="ml-5">
					  		如需对方必须回复，请选择回复负责人，需实时告知可选择抄送人，
					  	</small>
					  	<small style="color: #99a421;">
					  		紧急程度与处理时效对应关系如下：一般事项：3天内完成，重要事项：48小时内响应， 紧急事项：24小时内优先处理
					  	</small>
				  	</div>
			  	</div>
		 </div>
		<div class="layui-card">
			<div class="layui-card-header">交流反馈</div>
		    <div class="layui-card-body publishProjectStateBody" style="padding-top: 0px;min-height: calc(100vh - 300px)">
		  		<table class="layui-table" id="prjectStateTable"></table>
		    </div>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">

	var projectId = '${param.projectId}';
	function projectFollowList(){
		$("#projectAction").initTable({
			mars:'ProjectDao.projectFollowList',
			id:"prjectStateTable",
			cellMinWidth:100,
			data:{pageType:3},
			page:true,
			cols: [[
	      	{
				title: '序号',
				type:'numbers'
			 },{
			    field: 'FEEDBACK',
				title: '反馈内容',
				minWidth:400,
				event:'flowDetail',
				templet:function(row){
					var operateId = row['FOLLOW_ID'];
					var stakeholderId = row['STAKEHOLDER_ID'];
					var stakeholder = row['STAKEHOLDER'];
					var updateBy = row['UPDATE_BY'];
					var array = [];
					if(stakeholder){
						array.push('<a href="javascript:;">@'+stakeholder+'</a>：');
					}
					array.push(row['FEEDBACK']);
					
					var replyContent = row['REPLY_CONTENT'];
					if(replyContent){
						array.push('<hr>'+row['REPLY_TIME']+'回复：<b>'+replyContent+'</b>')
					}
					return array.join('');
				}
			},{
			    field: 'FEEDBACK_TYPE',
				title: '反馈类别',
				width:80,
				align:'center'
			},{
			    field: 'PROBLEM_BY',
				title: '问题责任方',
				width:80,
				align:'center'
			},{
			    field: 'HAS_DEV_WORK',
				title: '开发工作量',
				width:80,
				align:'center',
				templet:function(row){
					if(row['HAS_DEV_WORK']=='0'){
						return '是';
					}
					if(row['HAS_DEV_WORK']=='1'){
						return '否';
					}
					return row['HAS_DEV_WORK'];
				}
			},{
			    field: 'EFFECT_PROGRESS',
				title: '影响进度',
				width:80,
				align:'center',
				templet:function(row){
					if(row['EFFECT_PROGRESS']=='0'){
						return '是';
					}
					if(row['EFFECT_PROGRESS']=='1'){
						return '否';
					}
					return row['EFFECT_PROGRESS'];
				}
			},{
			    field: 'CREATE_NAME',
				title: '提出人',
				align:'center',
				width:90
			},{
			    field: 'UPDATE_TIME',
				title: '提交时间',
				align:'center',
				width:150,
				align:'center'
			},{
				field:'REPLY_TIME',
				title:'回复状态',
				width:100,
				templet:function(row){
					var replyTime = row.REPLY_TIME;
					var stakeholder = row.STAKEHOLDER;
					if(stakeholder&&replyTime){
						return stakeholder+'已回复';
					}
					if(stakeholder==''){
						return '无设置';
					}else{
						return stakeholder+'待回复'; 
					}
				}
			}
			]],done:function(result){
				
			}});
		
	}
	
	function publishProjectState(){
		var data = form.getJSONObject(".publishProjectState");
		
		// 处理problemBy多选值
		if(Array.isArray(data.problemBy)) {
			data.problemBy = data.problemBy.join(',');
		}
		
		data['projectId']=projectId;
		var followState = data.followState;
		var followDate = data.followDate;
		var stakeholder = data.stakeholder;
		if(followState==''){
			layer.msg("请选择反馈类别.");
			return;
		}
		if(followDate==''){
			layer.msg("请选择反馈日期.");
			return;
		}
		var feedbackContent=$("#feedbackContent").val();
		if(feedbackContent==''){
			layer.msg("描述理由不能为空.");
			return;
		}
		if(feedbackContent.length<5){
			layer.msg("描述理由不能少于5个字");
			return;
		}
		if(feedbackContent.length>2000){
			layer.msg("描述理由不能大于2000个字");
			return;
		}
		ajax.remoteCall("${ctxPath}/servlet/project?action=updateProjectState",data,function(result) { 
			if(result.state == 1){
				addFlow(data,result.data);
				layer.msg(result.msg);
				$("#feedbackContent").val("");
				$("#projectAction").queryData({id:'prjectStateTable',page:false});
			}else{
				layer.alert(result.msg,{icon: 7});
			}
		});
	}
	
	function addFlow(formData,id){
		if(formData.stakeholderId==''){
			return;
		}
		var data = {"applyState":"0","flowCode":"pj_feedback","doAction":"submit","checkResult":"1","noticeType":["1","2"],"urgent":"3","apply.fill_time":"20"};
		data['businessId'] = id;
		data['apply.apply_level'] = formData.level;
		data['checkDesc'] = getTimeLimit(formData.level);
		data['apply.apply_title'] = '【'+formData.followState+'】'+formData.projectName;
		data['apply.apply_remark'] =  formData.feedbackContent;
		data['selectUserId'] = formData.stakeholderId;
		data['selectUserName'] = formData.stakeholder;
		

		var staffInfo = getStaffInfo();
		data['apply.dept_name'] = staffInfo.deptName;
		data['apply.apply_name'] = staffInfo.userName;
		
		
		
		data['ccIds'] = formData.ccIds;
		data['ccNames'] = formData.ccNames;
		
		data['apply.data1'] = formData.stakeholder;
		data['apply.data2'] = formData.stakeholderId;
		
		data['apply.data4'] = formData.ccIds;
		data['apply.data3'] = formData.ccNames;
		
		data['apply.data5'] = formData.projectId;
		data['apply.data6'] = formData.projectName;
		
		
		
		ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=submitApply",data,function(result) { 
			if(result.state != 1){
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	function getTimeLimit(level){
		if(level=='正常'){
			return '请72小时内回复';
		}
		if(level=='重要'){
			return '请48小时内回复';
		}
		if(level=='紧急'){
			return '请24小时内回复';
		}
		return '尽快回复';
	}
	
	function flowDetail(row){
		if(row.STAKEHOLDER_ID){
			var data = {businessId:row['FOLLOW_ID']};
			popup.openTab({id:'flowDetail',title:'回复详情',url:'${ctxPath}/web/flow',data:data});
		}
	}
	
	function uploadFile(){
		layer.msg('暂未开放');
	}	


	$(function(){
		requreLib.setplugs('select2',function(){
			$("select[name='problemBy']").select2({
				theme: "bootstrap"
			});
		});
		
		$('#projectAction').render({success:function(){
			projectFollowList();
		}});
	});
 	
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>