<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
  <head>
    <title>考勤打卡</title>
    <meta charset="utf-8">
    <meta http-equiv="pragma" content="no-cache">
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no"/>
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <link rel="stylesheet" href="https://cdn.bootcss.com/weui/1.1.3/style/weui.min.css">
	<link rel="stylesheet" href="https://cdn.bootcss.com/jquery-weui/1.2.1/css/jquery-weui.min.css">
	<!-- <script src="https://map.qq.com/api/gljs?v=1.exp&key=7QTBZ-JKQEX-QMI4U-T3QCV-CBTXJ-HHBRI"></script> -->
    <style type="text/css">
	    #container{
	    	margin: -10px ​-15px 10px;
	    }
	    .weui-cells{font-size: 14px;}
    </style>
    
</head>
<body ontouchstart onload="init()">
  <div class="weui-msg">
  	 <div class="weui-msg__icon-area"><i class="weui-icon-success weui-icon_msg"></i></div>
  </div>
  <div class="weui-cells" style="margin-top: -8px;">
	  <div class="weui-cell" style="padding: 0px 0px 5px;display: none;">
	  	<div id="container"></div>
	  </div>
	  <div class="weui-cell">
	    <div class="weui-cell__bd">
	      <p>员工信息</p>
	    </div>
	    <div class="weui-cell__ft">${staffInfo.deptName}/${staffInfo.userName}</div>
	  </div>
	  <div class="weui-cell">
	    <div class="weui-cell__bd">
	      <p>考勤时间</p>
	    </div>
	    <div class="weui-cell__ft"><span class="kqInfo"></span><span class="kqDjs"></span></div>
	  </div>
	  <div class="weui-cell">
	    <div class="weui-cell__bd">
	      <p>当前时间</p>
	    </div>
	    <div class="weui-cell__ft nowTime">${nowTime}</div>
	  </div>
	  <div class="weui-cell">
	    <div class="weui-cell__bd">
	      <p>位置</p>
	    </div>
	    <div class="address weui-cell__ft"></div>
	  </div>
</div>
<a class="weui-cell weui-cell_access" href="javascript:;" id="openLocation">
    <div class="weui-cell__bd">
      <p>打开地图</p>
    </div>
    <div class="weui-cell__ft">
    </div>
  </a>
  <a class="weui-cell weui-cell_access" href="javascript:;"  id="getLocation">
    <div class="weui-cell__bd">
      <p>刷新定位</p>
    </div>
    <div class="weui-cell__ft">
    </div>
  </a>
  
   <div class="weui-msg__opr-area">
    <p class="weui-btn-area">
      <a href="javascript:;" id="signIn" class="weui-btn weui-btn_primary">上班打卡</a>
      <a href="javascript:;" id="signOut" class="weui-btn weui-btn_default">下班打卡</a>
    </p>
  </div>
    
 <br>

 <div class="weui-msg">
  <div class="weui-msg__extra-area">
    <div class="weui-footer">
      <p class="weui-footer__links">
        <a href="javascript:void(0);" class="weui-footer__link">智能考勤，轻松管理</a>
      </p>
      <p class="weui-footer__text">Copyright © 2017-2023</p>
    </div>
  </div>
</div>
  
<script src="https://code.jquery.com/jquery-1.12.4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-weui/1.2.1/js/jquery-weui.min.js"></script>
<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
<script type="text/javascript" src="/yq-work/wx/js_sdk.jsp?test=false"></script>
<script type="text/javascript">

function init(){
	 wx.ready(function () {
		 wx.hideOptionMenu();
		 wx.checkJsApi({
	      jsApiList: ['hideOptionMenu','getNetworkType','openLocation','openLocation'],
	      success: function (res) {
	         //alert(JSON.stringify(res));
	      }
	    });
		  
	  var postData = {address:'',distance:'0',latitude:'',longitude:'',city:''};
	  
	  document.querySelector('#openLocation').onclick = function () {
	    wx.openLocation({
	      latitude:  postData['latitude'],
	      longitude: postData['longitude'],
	      scale: 28,
	      name:'当前位置',
	      address:postData['address']
	    });
	  };
	  
	  
	  //加载定位
	  function loadNowLocation(){
		  wx.getLocation({
			  type: 'gcj02',
		      success: function (res) {
		    	  postData['latitude'] = res.latitude;
		    	  postData['longitude'] = res.longitude;
		    	  
		    	  //显示微信内置地图
		    	  showLocation(res.latitude+","+res.longitude);
		    	   
		    	  //加载地图
		    	  //initMap(res.latitude,res.longitude);
		            
		      },
		      cancel: function (res) {
		    	  $.toptip('您拒绝授权获取地理位置', 'warning');
		    	  $('#signIn,#signOut').hide();
		      }
		   });
	  }
	  
	  loadNowLocation();
	  
	  showTime();
	  
	  document.querySelector('#getLocation').onclick = function () {
		  loadNowLocation();
	  };
	  
	  debugger;
	  
	  var timer = {};
	  
	  var myDate = new Date();
	  if(myDate.getHours()>=13){
			$("#signIn").hide();
	  }else{
			$("#signOut").hide();
	  }
	  
	  var overtime = '${overtime}';
	  var currentTime = '${currentTime}';
	  var currentDate = '${currentDate}';
	  var _currentTime = new Date(currentTime);
	  
	  if(overtime=='1'){
		  var dkTimes = [{type:0,beginTime:'07:00',endTime:'09:00',lastTime:'13:00'},{type:1,beginTime:'13:00',endTime:'18:00',lastTime:'23:59'}];
		  for(var index in dkTimes){
			  var obj = dkTimes[index];
			  clearTimeout(timer[obj.type]);
		      var beginTime = new Date(currentDate+" "+obj.beginTime);
		      var endTime = new Date(currentDate+" "+obj.endTime);
		      var lastTime = new Date(currentDate+" "+obj.lastTime);
		      
		      if(myDate.getHours()>=13&&obj.type==0){
		    	  continue;
		      }
		      if(myDate.getHours()<13&&obj.type==1){
		    	  continue;
		      }
			 countTime(obj.type,_currentTime.getTime(),beginTime.getTime(),endTime.getTime(),lastTime.getTime())
		  }
	  }else{
		  $('.kqInfo').text('今日非工作日');
		  $.toptip("今日非工作日",10000,'success');
	  }

	  
	  function countTime(kqType,nowDate,beginTime,endTime,lastTime){  
	         var now = nowDate; 
	         var begin = beginTime;
	         var end = endTime;  
	         var last = lastTime;  
	         var flag = true;
	         var leftTime = end - now;
	         
	         if(kqType==0){
	        	 if(now < end){
	        		 $('.kqInfo').text('上班倒计时：');
	        		 flag = true;
	        	 }else{
	        		 leftTime = now - end;
	        		 $('.kqInfo').text('已上班：');
	        	 }
	         }else if(kqType==1){
	        	 if(now < end){
	        		 $('.kqInfo').text('下班倒计时：');
	        		 flag = true;
	        	 }else{
	        		 leftTime = now - end;
	        		 $('.kqInfo').text('已下班：');
	        		 flag = true;
	        	 }
	         }
	         
	         //定义变量 d,h,m,s保存倒计时的时间  
	         var d,h,m,s; 
	         
	         if(flag){
	             d = Math.floor(leftTime/1000/60/60/24);  
	             h = Math.floor(leftTime/1000/60/60%24);  
	             m = Math.floor(leftTime/1000/60%60);  
	             s = Math.floor(leftTime/1000%60);                     
		         if(h>0&&m>0){
		        	 $('.kqDjs').text(h+"时"+ m+"分"+ s+"秒");
		         }
		         if(h==0&&m>0){
		        	 $('.kqDjs').text( m+"分"+s+"秒");
		         }
		         if(m==0&&s>=0){
		        	 $('.kqDjs').text( s+"秒");
		         }
		         //递归每秒调用countTime方法，显示动态时间效果
	         }
	         if(flag){
	        	 timer[kqType] = setTimeout(function(){
			 		nowDate = nowDate + 1000;
			 		countTime(kqType,nowDate,beginTime,endTime,lastTime)
			 	},1000);  
	         	
	         }
	    }
	  
	  
	  function showKqData(){
		  $.ajax({
			  	url:'/yq-work/servlet/kaoqin?action=getTodayWxDk',
				data: {},
				dataType: 'json',
				type:'post',
				success: function (res) {
					if(res.state==1){
						var data = res.data;
						if(data){
							 var signIn = data.SIGN_IN;
							 var signOut = data.SIGN_OUT;
							 if(signIn&&signOut){
								 $.toptip('上班已打卡'+signIn+";下班已打卡"+signOut,10000,'success');
							 }else if(signIn){
								 $.toptip('上班已打卡：'+signIn,10000, 'success');
							 }else if(signOut){
								 $.toptip("下班已打卡："+signOut,10000,'success');
							 }
						}
					}else{
						$.toast(res.msg, "forbidden");
					}
				},error:function(){
					$.toast('网络出现故障', "forbidden");
				}
			});
	  }
	  
	  showKqData();
	  
	  document.querySelector('#signIn').onclick = function () {
		  var json =  $.extend(postData,{signTimeType:0});
		  var distance = json['distance'];
		  if(json['address']==''){
			  $.toast('没有获取到您的定位,请刷新再试。', "forbidden");
			  return;
		  }
		  if(distance&&distance>300){
			  $.actions({
				  title:'异常定位打卡原因',
				  actions: [{
				    text: "外勤",
				    onClick: function() {
				    	json['remark'] = '外勤';
						doExcute();
				    }
				  },{
				    text: "请假",
				    onClick: function() {
				    	json['remark'] = '请假';
						doExcute();
				    }
				  },{
				    text: "出差",
				    onClick: function() {
				    	json['remark'] = '出差';
						doExcute();
				    }
				  },{
				    text: "居家办公",
				    onClick: function() {
				    	json['remark'] = '居家办公';
						doExcute();
				    }
				  }
				  ]
				});
		  }else{
			  doExcute();
		  }
		  function doExcute(){
			  $.ajax({
				  	url:'/yq-work/servlet/kaoqin?action=wxDk',
					data: {data:JSON.stringify(json)},
					dataType: 'json',
					type:'post',
					success: function (res) {
						if(res.state==1){
							$.toast("上班打卡成功");
						}else{
							$.toast(res.msg, "forbidden");
						}
					},error:function(){
						$.toast('网络出现故障', "forbidden");
					}
				});
		  }
	  };
	  
	  document.querySelector('#signOut').onclick = function () {
		  var json =  $.extend(postData,{signTimeType:1});
		  var distance = json['distance'];
		  if(json['address']==''){
			  $.toast('没有获取到您的定位,请刷新再试。', "forbidden");
			  return;
		  }
		  if(distance&&distance>300){
			  $.actions({
				  title:'异常定位打卡原因',
				  actions: [{
				    text: "外勤",
				    onClick: function() {
				    	json['remark'] = '外勤';
						doExcute();
				    }
				  },{
				    text: "请假",
				    onClick: function() {
				    	json['remark'] = '请假';
						doExcute();
				    }
				  },{
				    text: "出差",
				    onClick: function() {
				    	json['remark'] = '出差';
						doExcute();
				    }
				  },{
				    text: "居家办公",
				    onClick: function() {
				    	json['remark'] = '居家办公';
						doExcute();
				    }
				  }
				  ]
				});
		  }else{
			  doExcute();
		  }
		  function doExcute(){
			  $.ajax({
				  	url:'/yq-work/servlet/kaoqin?action=wxDk',
					data: {data:JSON.stringify(json)},
					dataType: 'json',
					type:'post',
					success: function (res) {
						if(res.state==1){
							$.toast("下班打卡成功");
						}else{
							$.toast(res.msg, "forbidden");
						}
					},error:function(){
						$.toast('网络出现故障', "forbidden");
					}
				});
		  }
		  
	  };
	  
	  function showTime(){
		  setInterval(function() {
		      var now = (new Date()).toLocaleString();
		      $('.nowTime').text(now);
		  }, 1000);
	  }
	  
	  var isLoadMap = false;
	  function initMap(x,y) {
		  if(isLoadMap){
			  return;
		  }
		  isLoadMap = true;
	      //定义地图中心点坐标
	      var center = new TMap.LatLng(x, y)
	      //定义map变量，调用 TMap.Map() 构造函数创建地图
	      var map = new TMap.Map(document.getElementById('container'), {
	          center: center,
	          zoom: 17, pitch: 43.5,rotation: 45
	      });
	  }
	  
	  function showLocation(info){
		  $.ajax({
			  	url:'/yq-work/map/req',
				data: {
					"url": 'https://apis.map.qq.com/ws/geocoder/v1/',
					"get_poi": "0",
					"location": info
				},
				dataType: 'json',
				type:'get',
				success: function (res) {
			    	console.log(res);
					if(res.state==1){
						var mapData = res.data;
						if(mapData.status=='0'){
							var mapResult = mapData.result;
							var address = mapResult.address;
							
							postData['address'] = address;
							postData['addressComponent'] = mapResult.address_component;
							postData['location'] = mapResult.location;
							postData['city'] = mapResult.address_component.city;
							
					    	$('.address').text(address);
					    	$.toast("定位成功");
					    	
					    	try{
						    	calcLong(info,mapResult.address_component.city);
					    	}catch(e){
					    		
					    	}
						}else{
							$.alert(mapData.message);
						}
					}else{
						$.toast('获取位置信息失败', 'text');
					}
			    },
			    fail: function () {
			    	$.toast("请求失败", "cancel");
			    },
			    complete: function () {
			    	//$.toast("请求失败", "cancel");
			    }
			});
	    }
	  
	  
	  //https://lbs.qq.com/getPoint/
	  var companyDkLocation = {'广州市':'23.124375,113.372344','北京市':'39.960691,116.323633','武汉市':'30.582314,114.365448'};
	  
	  function calcLong(from,city){
		  var to = '';
		  var zuobiao = companyDkLocation[city];
		  if(zuobiao){
			  to = zuobiao;
		  }
		  if(to){
			  $.ajax({
				  	url:'/yq-work/map/req',
					data: {
						"url": 'https://apis.map.qq.com/ws/distance/v1/matrix',
						"from": from,
						"to": to,
						"mode":'walking'
					},
					dataType: 'json',
					type:'get',
					success: function (res) {
						if(res.state==1){
							var mapData = res.data;
							if(mapData.status=='0'){
								var mapResult = mapData.result;
								var row = mapResult['rows'][0]['elements'][0];
								var distance = row['distance'];
								postData['distance'] = distance;
								
								if(distance>300){
									$.alert("当前距离打卡位置超"+distance+"米,不在有效300米内");
								}else{
									$.toptip('当前位置距离打卡地点：'+distance+"米", 'success');
								}
							}
						}else{
							$.toast('获取距离计算信息失败', 'text');
						}
				    },
				    fail: function () {
				    	$.toast("请求失败", "cancel");
				    },
				    complete: function () {
				    	
				    }
				});
		   }
	    }
	  
	  
	});
	
	
}
</script>
</body>

</html>
