<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>笔记</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <label class="radio radio-success radio-inline">
		   					<input type="radio" name="noteAuth" checked="checked" value="1" onchange="reloadNote()"> <span>私有</span>
		  				 </label>
						 <label class="radio radio-success radio-inline">
		   					<input type="radio" name="noteAuth" value="2" onchange="reloadNote()"> <span>共享</span>
		  				 </label>
	          		     <div class="input-group input-group-sm ml-30">
							 <span class="input-group-addon">标题</span>	
							 <input type="text" name="title" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm ml-5">
							 <span class="input-group-addon">标签</span>	
							 <select data-mars="NoteDao.tagsDict" name="tags" onchange="list.query();" class="form-control input-sm">
							 	<option value="">请选择</option>
							 </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 新增</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			 $('#searchForm').render({success:function(){
				list.init();
			 }});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'NoteDao.list',
					height:'full-90',
					limit:30,
					cols: [[
		             {
						title: '编号',
						type:'numbers'
					 },{
					    field: 'TITLE',
						title: '标题',
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail'
					},{
					    field: 'TAGS',
						title: '标签',
						width:100,
						align:'left'
					},{
					    field: 'CREATOR',
						title: '创建人',
						align:'left',
						width:90,
						templet:function(row){
							return getUserName(row.CREATOR);
						}
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						width:140,
						align:'left'
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/note/note-edit.jsp',title:'新增'});
			},
			detail:function(data){
				var noteAuth=$("#searchForm input[name='noteAuth']:checked").val();
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/note/note-edit.jsp',title:'详情',data:{noteId:data.NOTE_ID,noteAuth:noteAuth}});
			}
		}
		function reloadNote(){
			list.query();
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>