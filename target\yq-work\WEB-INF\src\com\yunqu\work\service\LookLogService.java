package com.yunqu.work.service;

import java.sql.SQLException;

import javax.servlet.http.HttpServletRequest;

import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;

import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.LookLogModel;

import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;

public class LookLogService extends AppBaseService{
	private static class Holder{
		private static LookLogService service=new LookLogService();
	}
	public static LookLogService getService(){
		return Holder.service;
	}
	
	public void addLog(String lookBy,String fkId){
		addLog(lookBy,fkId,null,null,null,null,null);
	}
	
	public void addLog(String lookBy,String fkId,String type){
		addLog(lookBy,fkId,type,null,null,null,null);
	}

    private String getDevice(String userAgent) {
     	//解析agent字符串
        UserAgent ua = UserAgent.parseUserAgentString(userAgent);
        //获取浏览器对象
        Browser browser = ua.getBrowser();
        //获取操作系统对象
        OperatingSystem os = ua.getOperatingSystem();
        return String.format("设备类型:%s,操作系统:%s,浏览器:%s,浏览器版本:%s,浏览器引擎:%s",os.getDeviceType(),os.getName(),browser.getName(), browser.getVersion(userAgent),browser.getRenderingEngine());
    }
    
    private UserPrincipal getUserPrincipal(HttpServletRequest request){ 
		return (UserPrincipal)request.getUserPrincipal();
	}
    
    private String getNickName(HttpServletRequest request){
  		UserPrincipal  principal  = getUserPrincipal(request);
  		return principal.getUserName();
  	} 
	    
	public void addLog(String lookBy,String fkId,String type,HttpServletRequest request){
		String ip = WebKit.getIP(request);
		String url = request.getRequestURL().toString();
		String device = getDevice(request.getHeader("User-Agent"));
		addLog(lookBy,fkId,type,ip,url,device,getNickName(request));
	}
	
	public void addLog(String lookBy,String fkId,String type,String ip){
		addLog(lookBy,fkId,type,ip,null,null,null);
	}
	
	public void addLog(String lookBy,String fkId,String type,String ip,String url,String device,String userName){
		LookLogModel model=new LookLogModel();
		model.setFkId(fkId);
		model.setLookBy(lookBy);
		model.setPrimaryValues(RandomKit.uuid());
		model.set("look_time", EasyDate.getCurrentDateString());
		if(type!=null) {
			model.set("type", type);
		}
		if(ip!=null) {
			model.set("ip", ip);
		}
		if(url!=null) {
			model.set("url", url);
		}
		if(device!=null) {
			model.set("device", device);
		}
		if(userName!=null) {
			model.set("look_name", userName);
		}
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
	}

}
