package com.yunqu.work.dao.cust;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name="ContactsDao")
public class ContactsDao extends AppDaoContext{

	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		String contactsId = param.getString("contactsId");
		return queryForRecord("select * from yq_crm_contacts where contacts_id = ?",contactsId);
	}
	
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select t1.*,t2.cust_name from yq_crm_contacts t1 left join yq_crm_customer t2 on t1.cust_id=t2.cust_id where 1=1");
		sql.append(param.getString("custId"), "and t1.cust_id = ?");
		sql.append("order by t1.update_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="custContacts",type=Types.DICT)
	public JSONObject custContacts(){
		EasySQL sql=getEasySQL("select name,name from yq_crm_contacts where 1=1");
		sql.append(param.getString("custId"), "and cust_id = ?",false);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
}
