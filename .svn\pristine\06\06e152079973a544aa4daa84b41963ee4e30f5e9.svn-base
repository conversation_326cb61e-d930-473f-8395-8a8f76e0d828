package com.yunqu.work.dao.other;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.SqlPara;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.StaffModel;

@WebObject(name="CommonDao")
public class CommonDao extends AppDaoContext {
	
	@WebControl(name="monthRange",type=Types.TEXT)
	public JSONObject monthRange(){
		Calendar calendar=Calendar.getInstance();
		calendar.add(Calendar.MONTH, -1);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM-dd")+" 到 "+EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	@WebControl(name="currentMonthRange",type=Types.TEXT)
	public JSONObject currentMonthRange(){
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		Calendar ca = Calendar.getInstance();   
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH)); 
        String last = format.format(ca.getTime());
	        
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM")+"-01"+" 到 "+last);
	}
	
	@WebControl(name="date01",type=Types.TEXT)
	public JSONObject date01(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd")+" 00:00");
	}
	@WebControl(name="date02",type=Types.TEXT)
	public JSONObject date02(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyy-MM-dd HH:mm"));
	}
	@WebControl(name="date03",type=Types.TEXT)
	public JSONObject data03(){
		return getJsonResult(EasyDate.getCurrentDateString("yyyyMMdd"));
	}
	
	@WebControl(name="prevMonthId",type=Types.TEXT)
	public JSONObject prevMonthId(){
		Calendar calendar=Calendar.getInstance();
		calendar.add(Calendar.MONTH, -1);
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM"));
	}
	@WebControl(name="nowMonthId",type=Types.TEXT)
	public JSONObject nowMonthId(){
		Calendar calendar=Calendar.getInstance();
		return getJsonResult(EasyDate.dateToString(calendar.getTime(), "yyyy-MM"));
	}
	
	@WebControl(name="randomId",type=Types.TEXT)
	public JSONObject randomId(){
		return getJsonResult(RandomKit.uniqueStr());
	}
	@WebControl(name="userName",type=Types.TEXT)
	public JSONObject userName(){
		return getJsonResult(getUserPrincipal().getUserName());
	}
	@WebControl(name="userDict",type=Types.DICT)
	public JSONObject userDict(){
		String projectId=param.getString("projectId");
		if(StringUtils.notBlank(projectId)){
			return	getDictByQuery("select t2.USER_ID,t2.USERNAME from yq_project_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t1.USER_ID=t2.USER_ID where t1.PROJECT_ID = ?",projectId);
		}else{
			JSONObject	result=getDictByQuery("select USER_ID,USERNAME from "+Constants.DS_MAIN_NAME+".easi_user where STATE = 0");
			return result;
		}
		
	}
	@WebControl(name="userList",type=Types.LIST)
	public JSONObject userList(){
		EasyQuery query=ServerContext.getAdminQuery();
		this.setQuery(query);
		JSONObject jsonObject=queryForList("select USER_ID,USERNAME,PIC_URL from easi_user ",null);//where STATE = 0
		jsonObject.put("isSuperUser", isSuperUser());
		return jsonObject;
	}
	@WebControl(name="userInfo",type=Types.OTHER)
	public JSONObject userInfo(){
		String id=param.getString("userId");
		JSONObject jsonObject=queryForRecord("select * from yq_user_info where user_id = ? ",id);
		return jsonObject;
	}
	
	@WebControl(name="myUserInfo",type=Types.OTHER)
	public JSONObject myUserInfo(){
		StaffModel staffInfo = getStaffInfo();
		staffInfo.setResIds(getUserPrincipal().getResources());
		staffInfo.setRoleIds(getUserPrincipal().getRoles());
		return getJsonResult(staffInfo);
	}
	
	@WebControl(name="deptDict",type=Types.DICT)
	public JSONObject deptDict(){
		return getDictByQuery("select DEPT_ID,DEPT_NAME from "+Constants.DS_MAIN_NAME+".easi_dept");
	}
	
	/**
	 * 获取今天时间
	 * @return
	 */
	@WebControl(name="today",type=Types.TEXT)
	public JSONObject today(){
		return getText(EasyDate.getCurrentDateString("yyyy-MM-dd"));
	}
	
	/**
	 * 获取三天后
	 * @return
	 */
	@WebControl(name="date04", type=Types.TEXT)
	public JSONObject date04() {
		Date date = EasyDate.getCurrentDate();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, 3);
		date = calendar.getTime();
		return getText(EasyDate.dateToString(date, "yyyy-MM-dd HH:mm:ss"));
	}
	/**
	 * 获取两周后
	 * @return
	 */
	@WebControl(name="twoWeeks", type=Types.TEXT)
	public JSONObject twoWeeks() {
		Date date = EasyDate.getCurrentDate();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.WEEK_OF_YEAR, -2);
		date = calendar.getTime();
		return getText(EasyDate.dateToString(date, "yyyy-MM-dd"));
	}
	@WebControl(name="commentsList",type=Types.TEMPLATE)
	public JSONObject fileList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_comments where fk_id = ? order by create_time desc";
			return queryForList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 查看日志
	 * @return
	 */
	@WebControl(name="lookLogList",type=Types.LIST)
	public JSONObject lookLogList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_look_log where fk_id = ? order by look_time desc";
			return queryForPageList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 操作日志
	 * @return
	 */
	@WebControl(name="lookOpList",type=Types.LIST)
	public JSONObject lookOpList(){
		String fkId=param.getString("fkId");
		if(StringUtils.notBlank(fkId)){
			String sql="select * from yq_oplog where fk_id = ? order by op_time desc";
			return queryForList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 下载日志
	 * @return
	 */
	@WebControl(name="downloadLog",type=Types.LIST)
	public JSONObject downloadLog(){
		String fkId=param.getString("fkId");
		String fileId=param.getString("fileId");
		if(StringUtils.notBlank(fileId)){
			String sql="select t2.file_name,t2.file_size,t1.download_time,t1.download_by from yq_file_download_log t1,yq_files t2 where t1.file_id=t2.file_id and t1.file_id = ? ORDER BY t1.download_time desc";
			return queryForPageList(sql, new Object[]{fileId});
		}
		if(StringUtils.notBlank(fkId)){
			String sql="select t2.file_name,t2.file_size,t1.download_time,t1.download_by from yq_file_download_log t1,yq_files t2 where t1.file_id=t2.file_id and t1.fk_id = ? ORDER BY t1.download_time desc";
			return queryForPageList(sql, new Object[]{fkId});
		}
		return getJsonResult(null);
	}
	/**
	 * 消息
	 * @return
	 */
	@WebControl(name="myMessage",type=Types.LIST)
	public JSONObject myMessage(){
		String msgType=param.getString("msgType");
		if(StringUtils.notBlank(msgType)){
			String sql="select * from YQ_MESSAGE   where RECEIVER = ?  and MSG_TYPE = ? ORDER BY CREATE_TIME desc";
			return queryForPageList(sql, new Object[]{getUserPrincipal().getUserId(),msgType});
		}else{
			EasySQL sql=getEasySQL("select * from YQ_MESSAGE  where 1=1");
			if(!isSuperUser()) {
				sql.append(getUserId(),"and RECEIVER = ?");
			}
			String msgState = param.getString("msgState");
			sql.append(msgState,"and msg_state = ?");
			if("1".equals(msgState)) {
				sql.append("ORDER BY CREATE_TIME desc");
			}else if("2".equals(msgState)) {
				sql.append("ORDER BY READ_TIME desc");
			}else {
				sql.append("ORDER BY CREATE_TIME desc");
			}
			return queryForPageList(sql.getSQL(),sql.getParams());
		}
	}
	
	@WebControl(name = "dictVal",type = Types.OTHER)
	public JSONObject dictVal() {
		setQuery(getMainQuery());
		EasySQL sql =new EasySQL("select DICT_TYPE_ID,DICT_ID,DICT_NAME from easi_pc_dict where 1=1");
		sql.appendIn(param.getString("dicts").split(","), "and DICT_TYPE_ID ");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "findListBySqlkey",type = Types.TEMPLATE)
	public JSONObject findListBySqlkey() {
		String sqlKey = param.getString("sqlKey");
		if(StringUtils.isNotBlank(sqlKey)) {
			SqlPara sqlPara = Db.getSqlPara(sqlKey, param);
			return queryForList(sqlPara.getSql(), sqlPara.getPara());
		}
		return getJsonResult(new JSONArray());
	}
	
	@WebControl(name = "findRecordBySqlkey",type = Types.RECORD)
	public JSONObject findRecordBySqlkey() {
		String sqlKey = param.getString("sqlKey");
		if(StringUtils.isNotBlank(sqlKey)) {
			SqlPara sqlPara = Db.getSqlPara(sqlKey, param);
			return queryForRecord(sqlPara.getSql(), sqlPara.getPara());
		}
		return getJsonResult(new JSONArray());
	}
	
	@WebControl(name = "findPageBySqlkey",type = Types.TEMPLATE)
	public JSONObject findPageBySqlkey() {
		String sqlKey = param.getString("sqlKey");
		if(StringUtils.isNotBlank(sqlKey)) {
			SqlPara sqlPara = Db.getSqlPara(sqlKey, param);
			return queryForPageList(sqlPara.getSql(), sqlPara.getPara());
		}
		return emptyPage();
	}
}





