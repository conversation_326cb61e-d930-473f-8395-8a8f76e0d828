<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="contactsEditForm" class="form-horizontal" data-text-model="${param.isEdit=='0'}" data-mars="ContactsDao.record" autocomplete="off" data-mars-prefix="contacts.">
 		   		<input type="hidden" value="${param.contactsId}" name="contactsId"/>
 		   		<input type="hidden" value="${param.contactsId}" name="contacts.CONTACTS_ID"/>
				<table class="table table-vzebra">
			        <tbody>
			            <tr>
		                    <td width="80px" class="required">姓名</td>
		                    <td style="width: 40%">
		                   		 <input data-rules="required" autocomplete="off" type="text" name="contacts.NAME" class="form-control input-sm">
		                    </td>
		                    <td width="80px" class="required">客户名称</td>
		                    <td style="width: 40%">
								<input type="hidden" value="${param.custId}" name="contacts.CUST_ID" class="form-control input-sm"/>
								<input type="text" data-rules="required" readonly="readonly" id="custName" value="${param.custName}" class="form-control input-sm"/>
		                    </td>
			            </tr>
			           
			            <tr>
		                    <td class="required">手机</td>
		                    <td>
		                    	 <input type="text" data-rules="required" name="contacts.MOBILE" class="form-control input-sm">
		                    </td>
		                    <td>微信</td>
		                    <td>
		                    	 <input type="text" name="contacts.WEIXIN" class="form-control input-sm">
		                    </td>
		                </tr>
		                  <tr>
		                	<td class="required">性别</td>
		                    <td>
		                   		 <select data-rules="required" name="contacts.SEX" class="form-control input-sm">
		                   		 	<option value="">请选择</option>
		                   		 	<option value="男">男</option>
		                   		 	<option value="女">女</option>
		                   		 </select>
		                    </td>
		                    <td>部门</td>
		                    <td>
		                   		 <input type="text" name="contacts.DEPT" class="form-control input-sm">
		                    </td>
		                </tr>
		                  <tr>
		                	<td>邮箱</td>
		                    <td>
		                   		 <input type="text" autocomplete="off" name="contacts.EMAIL" class="form-control input-sm">
		                    </td>
		                    <td>职务</td>
		                    <td>
		                   		 <input type="text" name="contacts.POST" class="form-control input-sm">
		                    </td>
		                </tr>
		                 <tr>
		                    <td>角色</td>
		                    <td>
		                    	 <select data-rules="required" data-mars="YqAdmin.DictDao.select('Y010')" name="contacts.ROLE"  class="form-control input-sm">
			                    	<option value="">请选择</option>
			                    </select>
		                    </td>
		                    <td>地址</td>
		                    <td>
		                   		 <input type="text" name="contacts.ADDRESS" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
			            	<td>备注</td>
			               	<td colspan="3">
	                           <textarea style="height: 80px;" class="form-control input-sm" name="contacts.REMARK"></textarea>
			               	</td>
			            </tr>
			            <c:if test="${!empty param.contactsId && param.isEdit==1}">
				            <tr>
				            	<td></td>
				               	<td colspan="3">
		                          	<a class="btn btn-xs btn-link" onclick="delContacts()" href="javascript:;">删除</a>
				               	</td>
				            </tr>
			            </c:if>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	   <c:if test="${param.isEdit=='1'}">
				    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="Cust.ajaxSubmitForm()"> 保 存 </button>
			    	   </c:if>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("Cust");
	    
		Cust.contactsId='${param.contactsId}';
		
		var contactsId='${param.contactsId}';
		
		
		layui.config({
			  base: '${ctxPath}/static/js/'
		}).use('tableSelect'); //加载自定义模块
		
		layui.use(['tableSelect'], function(){
			var tableSelect = layui.tableSelect;
			tableSelect.render({
				elem: '#custName',
				searchKey: 'custName',
				checkedKey: 'CUST_ID',
				page:true,
				searchPlaceholder: '请输入客户名称',
				table: {
					mars: 'CustDao.selectCustList',
					cols: [[
						{ type: 'radio' },
						{ field: 'CUST_ID',hide:true,title: 'ID'},
						{ field: 'CUST_NAME', title: '名称', width: 320 },
						{ field: 'CUST_NO', title: '编号', width: 150 }
					]]
				},
				done: function (elem, data) {
					var names = [];
					var ids = [];
					layui.each(data.data, function (index, item) {
						names.push(item.CUST_NAME)
						ids.push(item.CUST_ID)
					});
					elem.attr("ts-selected",ids.join(","));
					elem.val(names.join(","));
					elem.prev().val(ids.join(","));
				},
				clear:function(elem){
					 elem.prev().val("");
				}
			});
		});
		function delContacts(){
			layer.confirm('是否确认删除，不可恢复',function(index){
				layer.close(index);
				var data = {contactsId:contactsId};
				ajax.remoteCall("${ctxPath}/servlet/contacts?action=del",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							reloadContactsList();
							popup.layerClose('contactsEditForm');
	 					});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
		$(function(){
			$("#contactsEditForm").render({success:function(result){

			}});  
		});
		
		Cust.ajaxSubmitForm = function(){
			if(form.validate("#contactsEditForm")){
				if(Cust.contactsId){
					Cust.updateData(); 
				}else{
					Cust.insertData(); 
				}
			};
		}
		Cust.insertData = function(flag) {
			var data = form.getJSONObject("#contactsEditForm");
			delete data['contacts.CONTACTS_ID'];
			ajax.remoteCall("${ctxPath}/servlet/contacts?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadContactsList();
						popup.layerClose('contactsEditForm');
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Cust.updateData = function(flag) {
			var data = form.getJSONObject("#contactsEditForm");
			ajax.remoteCall("${ctxPath}/servlet/contacts?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadContactsList();
						popup.layerClose('contactsEditForm');
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>