<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购出库单</title>
	<style>
		.ibox-content{border: none;box-shadow:none;}
		.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th{
			 color: #0e0e0e;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="StorageOrderForm">
		<button class="btn btn-info btn-sm hidden-print" onclick="window.print();" style="position: absolute;top: 20px;right: 20px;" type="button">打印</button>
	    <input type="hidden" value="${param.orderId}" id="orderId" name="orderId"/>
	    <input type="hidden" value="${param.stockIds}" id="stockIds" name="stockIds"/>
		<div style="min-height: calc(100vh - 100px);" class="ibox-content" data-mars="OrderDao.applyInfo">
			<table style="margin: 0 auto;" class="table table-edit table-vzebra">
				<tbody>
					<tr>
						<td style="font-size: 18px;font-weight: 700;text-align: center;">广州云趣信息科技有限公司</td>
					</tr>
					<tr>
						<td style="font-size: 18px;font-weight: 700;text-align: center;">采购出库单</td>
					</tr>
				</tbody>
			</table>
			<br>
			<table style="margin: 0 auto;" class="table table-vzebra">
				<tbody>
					<tr>
						<td>供应商</td>
						<td id="SUPPLIER_NAME"></td>
						<td>公司名称</td>
						<td>广州云趣信息科技有限公司</td>
					</tr>
					<tr>
						<td>合同名称</td>
						<td id="CONTRACT_NAME"></td>
						<td>合同编号</td>
						<td id="CONTRACT_NO"></td>
					</tr>
					<tr>
						<td>采购订单号</td>
						<td id="ORDER_NO"></td>
						<td>下单日期</td>
						<td id="CREATE_TIME"></td>
					</tr>
				</tbody>
			</table>
			<br>
			<table style="margin: 0 auto;" class="layui-table" lay-size="sm">
				<thead>
					<tr>
						<td>序号</td>
						<td>出库日期</td>
						<td>申请单号</td>
						<td>物料编码</td>
						<td>物料描述</td>
						<td>出库类型</td>
						<td>单位</td>
						<td>不含税单价</td>
						<td>数量</td>
						<td>出库成本(含税)</td>
						<td>领用部门</td>
						<td>备注</td>
					</tr>
				</thead>
				<tbody data-mars="GoodsDao.stockList" data-template="template-list">
				
				</tbody>
			</table>
			
			<br>
			<table style="margin: 0 auto;" class="table table-edit table-vzebra">
				<tbody>
					<tr>
						<td style="width: 80px;">仓库员：</td>
						<td>蔡洁</td>
						<td>申请人：</td>
						<td>庞小翠</td>
					</tr>
				</tbody>
			</table>
		</div>
	</form>
	
	<script id="template-list" type="text/x-jsrender">
	{{for data}}
		<tr>
		  <td>
				{{:#index+1}}
		  </td>
		  <td>
				{{:DATE_ID}}
		  </td>
		  <td>
				{{:STOCK_NO}}
		  </td>
		  <td>
				{{:GOODS_NO}}
		  </td>
		  <td>
				{{:GOODS_NAME}}
		  </td>
		  <td>
				{{call:OUT_TYPE fn='getOutType'}}
		  </td>
		  <td>
				{{:UNIT}}
		  </td>
 		  <td>
				{{:R_PRICE}}
		  </td>
 		  <td>
				{{:NUM}}
		  </td>
 		  <td>
				{{:PRICE*NUM}}
		  </td>
		  <td>
				{{:OUT_DEPT_NAME}}
		  </td>
		  <td>
				{{:REMARK}}
		  </td>
		</tr>
   {{/for}}
</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   
		$(function(){
			$("#StorageOrderForm").render({success:function(result){

			}});
		});
		
		function getOutType(type){
			var json = {1:'正常出库',2:'零星物料出库',3:'项目协作费出库',4:'固定资产'};
			return json[type]||'';
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>