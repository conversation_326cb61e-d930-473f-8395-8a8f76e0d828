<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectDeptForm">
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 160px">
					 <span class="input-group-addon">名称</span>	
					 <input type="text" name="deptName" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectDept.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		</div>
            	<div class="ibox">
              		<table id="deptList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectDept.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectDept={
					sid:'${param.sid}',
					query : function(){
						$("#selectDeptForm").queryData();
					},
					initTable : function(){
						$("#selectDeptForm").initTable({
							mars:'BxDao.selectDept',
							id:'deptList',
							page:true,
							limit:50,
							rowDoubleEvent:'SelectDept.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'DEPT_NAME',
								title: '部门'
							},{
							    field: 'FINANCE_DEPT_CODE',
								title: '编号'
							},{
								field:'TEMP_NAME',
								title:'类型'
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectDept.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('deptList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['DEPT_NAME']);
									ids.push(data[index]['DEPT_ID']);
									el.attr('data-tpl',data[index]['TEMP_NAME']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}
								popup.layerClose("selectDeptForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							el.val(selectRow['DEPT_NAME']);
							el.attr('data-tpl',selectRow['TEMP_NAME']);
							if(el.prev().length>0){
								el.prev().val(selectRow['DEPT_ID']);
							}
							popup.layerClose("selectDeptForm");
						}
					}
					
			};
			$(function(){
				SelectDept.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>