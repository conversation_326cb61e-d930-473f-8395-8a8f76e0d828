<!-- Generated by SpecStory -->

# Convert Attendance Selection to Tab Format (2025-04-02 09:32:22)

_**User**_

参考flow-query ，把是否考勤改为选项卡条件

---

_**Assistant**_

```thinking
让我分析一下这个需求:

1. 参考flow-query.jsp中的选项卡实现:
- 使用了layui的tab组件
- 通过tab切换来过滤不同状态的数据
- tab切换时触发查询方法

2. 当前kq-people.jsp中的是否考勤条件:
- 使用select下拉框
- 值为空/1/0分别对应全部/否/是
- 在查询时作为state参数传递

3. 需要改造的内容:
- 将select改为layui tab
- 保持原有的值映射关系
- 点击tab时触发查询
- 样式要与flow-query保持一致

4. 具体实现思路:
- 复制flow-query中的tab HTML结构和样式
- 修改tab的文案和data-val值
- 添加tab切换事件监听
- 移除原select控件

5. 检查相关代码:
- 查询方法kqPeopleList.query()不需要修改
- KqDao.kqPeople()方法的state参数处理逻辑不需要修改

好的,我来编写具体的修改方案。
```

我将帮你修改kq-people.jsp文件,将是否考勤的select改为选项卡形式。

---

```diff
                          <input type="text" name="deptName" class="form-control input-sm" style="width: 90px;">
                      </div>
-                     <div class="input-group input-group-sm">
-                         <span class="input-group-addon">是否考勤</span>
-                         <select name="state" class="form-control input-sm" >
-                             <option value="">全部</option>
-                             <option value="1">否</option>
-                             <option value="0">是</option>
-                         </select>
+                     <div class="pull-left layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: -4px 2px 5px;">
+                         <ul class="layui-tab-title">
+                             <li data-val="" class="layui-this">全部</li>
+                             <li data-val="0">考勤</li>
+                             <li data-val="1">免考勤</li>
+                         </ul>
                      </div>
                      <div class="input-group input-group-sm">
          }
  
+         layui.element.on('tab(stateFilter)', function(){
+             kqPeopleList.query({state:this.getAttribute('data-val')});
+         });
+ 
      </script>
  </EasyTag:override>
```

---

```thinking
修改已经完成,让我检查一下修改的内容:

1. 移除了原有的select控件
2. 添加了layui tab组件:
- 使用layui-tab-brief样式,与flow-query保持一致
- 设置了lay-filter="stateFilter"用于事件绑定
- 添加了3个选项:全部/考勤/免考勤,对应值为""/0/1
- 设置默认选中"全部"选项(layui-this)

3. 添加了tab切换事件监听:
- 使用layui.element.on绑定stateFilter的tab事件
- 切换时调用kqPeopleList.query并传入state参数
- state值从tab的data-val属性获取

4. 样式设置:
- margin设置与flow-query一致
- 使用pull-left确保布局正确

修改看起来符合要求:
- 功能上保持了原有的查询逻辑
- 界面上改为了更现代的tab形式
- 与flow-query的实现保持了一致
- 用户体验更好

不需要其他修改了。
```

我已经完成了将是否考勤条件从select下拉框改为选项卡形式的修改。主要变化包括:

1. 使用了layui的Tab组件替换原select控件
2. 保持了原有的值映射:
   - 空值 = 全部
   - 0 = 考勤 
   - 1 = 免考勤
3. 添加了tab切换事件,切换时自动触发查询
4. 样式与flow-query保持一致

修改后的界面会更加直观友好,用户可以直接点击选项卡来筛选数据,而不需要打开下拉框选择。

您可以测试一下修改后的功能,看看是否符合预期。如果需要调整,请告诉我。

---



---

