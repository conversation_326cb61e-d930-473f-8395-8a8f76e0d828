<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="orderId" type="hidden" value="${param.orderId}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>采购评审</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">订单号</span>
						 	<input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">销售合同号</span>
						 	<input class="form-control input-sm" name="contractNo">
						 </div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">类别</span>	
							<select name="type" onchange="queryData();" class="form-control input-sm">
							      <option value=""> 请选择 </option>
							      <option value="10">入库</option>
                    			  <option value="20">出库</option>
                    			  <option value="30">退货</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="followTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
			  <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="BusinessMgr.edit">评审</a>
			  <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="BusinessMgr.detail">查看</a>
		  </script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.reviewList',
					id:'followTable',
					page:true,
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
	            	 	type: 'checkbox',
						align:'left'
					 },{
						 field:'ORDER_NO',
						 title:'订单号',
						 event:'orderDetail',
						 style:'text-decoration:underline;'
					 },{
						 field:'CONTRACT_NAME',
						 title:'所属合同'
					 },{
						field:'TOTAL_PRICE',
						title:'含税金额'
					},{
					    field: 'CHECK_NAMES',
						title: '评审人',
						align:'left'
					},{
						field:'REMARK',
						title:'备注',
						edit:'text'
					},{
					    field: 'APPLY_TIME',
						title: '操作时间',
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '操作人',
						align:'center'
					},{
						field:'',
						title:'操作',
						templet:'#bar'
					}
				]],done:function(){
					
			  	}
			});
		 }
		 function orderCheckLog(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.reviewList',
					id:'followTable',
					page:true,
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
	            	 	type: 'checkbox',
						align:'left'
					 },{
						 field:'ORDER_NO',
						 title:'订单号',
						 event:'orderDetail',
						 style:'text-decoration:underline;'
					 },{
						 field:'CONTRACT_NAME',
						 title:'所属合同'
					 },{
						field:'TOTAL_PRICE',
						title:'含税金额'
					},{
					    field: 'CHECK_NAME',
						title: '评审人',
						align:'left'
					},{
						field:'REMARK',
						title:'备注',
						edit:'text'
					},{
					    field: 'APPLY_TIME',
						title: '操作时间',
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '操作人',
						align:'center'
					},{
						field:'',
						title:'操作',
						templet:'#bar'
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function orderDetail(data){
			popup.openTab({id:'orderDetail',title:'采购详情',url:'${ctxPath}/pages/erp/order/order-detail.jsp',data:{review:1,orderId:data.ORDER_ID,custId:data.CUST_ID,resultId:data['RESULT_ID']}});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'followTable'});
		}
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>