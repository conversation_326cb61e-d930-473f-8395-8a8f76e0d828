<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="titleEditForm" class="form-horizontal" data-mars="InvoiceDao.titleRecord" autocomplete="off" data-mars-prefix="title.">
 		   		<input type="hidden" value="${param.titleId}" name="titleId"/>
 		   		<input type="hidden" value="${param.titleId}" name="title.TITLE_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
					<tr id="titleCust">
						<td class="required">客户名称</td>
						<td>
							<input type="hidden" name="title.CUST_ID" value="${param.custId}" id="custId">
							<input type="text" onclick="singleCust(this);" readonly="readonly" placeholder="请点击选择客户" name="custName" class="form-control input-sm">
						</td>
					</tr>
						<tr>
		                    <td class="required">纳税人识别号</td>
		                    <td>
		                   		 <div class="input-group input-group-sm">
									<input data-rules="required" type="text" name="title.TAX_NO" class="form-control input-sm"/>
									<span class="input-group-addon" onclick="CustTitle.loadTax2()">点击根据客户获取</span>
					    		 </div>
		                    </td>
			            </tr>
			            <tr>
		                    <td width="120px" class="required">开票抬头</td>
		                    <td>
		                   		 <input data-rules="required"  type="text" name="title.TITLE_NAME" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
		                    <td class="required">开户行</td>
		                    <td>
		                    	 <input type="text" name="title.BANK_NAME" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
		                    <td>开户账号</td>
		                    <td>
		                    	 <input type="text" name="title.BANK_NO" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
		                	<td>开票地址</td>
		                    <td>
		                   		 <input type="text" name="title.ADDRESS" class="form-control input-sm">
		                    </td>
			            </tr>
	                    <tr>
	                    <td>电话</td>
		                    <td>
		                   		 <input type="text" name="title.TEL" class="form-control input-sm">
		                    </td>
	                    </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="CustTitle.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("CustTitle");
	    
		CustTitle.titleId='${param.titleId}';
		CustTitle.editSource = '${param.editSource}';


		var titleId='${param.titleId}';
		var custId='${param.custId}';

		$(function(){
			$("#titleEditForm").render({success:function(result){
					var custId = $("input[name='title.CUST_ID']").val();
					if (custId !== null && custId !== "") {
						$("#titleCust").hide();
					}
	}});
		});
		
		CustTitle.ajaxSubmitForm = function(){
			if(form.validate("#titleEditForm")){
				if(CustTitle.titleId){
					CustTitle.updateData(); 
				}else{
					CustTitle.insertData(); 
				}
			};
		}
		function loadTax(el){
			var val = el.prev().val();

			if(val!=''){
				$.ajax({
		            url: '${ctxPath}/servlet/cust?action=queryCompanyTax',
		            type: 'post',
		            data: {data:JSON.stringify({taxNo: val})},
		            dataType: 'json',
		            success: function (result) {
		            	console.log("result");
		            	console.log(result);
		            	if(result.state==1){
							fillRecord(result.data,'title.','');
		            	}
		            },error:function(){
		            	
		            }});
			}
		}

		CustTitle.loadTax2 = function(){
			var custId = $("input[name='title.CUST_ID']").val();
			if(custId!=''){
				$.ajax({
					url: '${ctxPath}/servlet/cust?action=queryCompanyTaxNum',
					type: 'post',
					data: {data:JSON.stringify({custId: custId})},
					dataType: 'json',
					success: function (result) {
						if(result.state==1){
							$("#titleEditForm input[name='title.TAX_NO']").val(result.data);
						}else{
							layer.msg(result.msg);
							$("#titleEditForm input[name='title.TAX_NO']").val("");
						}
					},error:function(){

					}});
			}else{
				layer.msg("请先选择客户");
			}

		}


		CustTitle.insertData = function(flag) {
			var data = form.getJSONObject("#titleEditForm");
			delete data['title.TITLE_ID'];
			ajax.remoteCall("${ctxPath}/servlet/invoice?action=addTitle",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('titleEditForm');
						if(CustTitle.editSource == 'selectTitle'){
							reloadSelectTitleList();
						}else {
							reloadInvoiceTitleList();
						}
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		CustTitle.updateData = function(flag) {
			var data = form.getJSONObject("#titleEditForm");
			ajax.remoteCall("${ctxPath}/servlet/invoice?action=updateTitle",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('titleEditForm');
						if(CustTitle.editSource == 'selectTitle'){
							reloadSelectTitleList();
						}else {
							reloadInvoiceTitleList();
						}
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>