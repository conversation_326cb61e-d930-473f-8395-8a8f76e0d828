package com.yunqu.work.dao.cust;

import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import com.alibaba.fastjson.JSONObject;
import org.easitline.common.db.EasySQL;

//客户运营、销售
@WebObject(name="CustOperateDao")
public class CustOperateDao extends AppDaoContext {

    @WebControl(name="salePriceRecord",type=Types.RECORD)
    public JSONObject salePriceRecord(){
        String priceId = param.getString("priceId");
        return queryForRecord("select * from yq_crm_sales_price where PRICE_ID = ?",priceId);
    }

    @WebControl(name = "salePriceList",type = Types.LIST)
    public JSONObject salePriceList(){
        EasySQL sql= new EasySQL("select * from yq_crm_sales_price t1 where 1=1");
        sql.append(param.getString("custId"),"and t1.cust_id = ?");
        sql.append("order by t1.CREATE_TIME desc");
        return queryForList(sql.getSQL(),sql.getParams());
    }
    @WebControl(name = "salePricePageList",type = Types.LIST)
    public JSONObject salePricePageList(){
        EasySQL sql= new EasySQL("select * from yq_crm_sales_price t1 where 1=1");
        sql.append(param.getString("custId"),"and t1.cust_id = ?");
        sql.append("order by t1.CREATE_TIME desc");
        return queryForPageList(sql.getSQL(),sql.getParams());
    }


}
