<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div class="mb-10"></div>
<div class="layui-row layui-col-space10" data-mars="KanbanDao.projectKanban" data-template="kanbanTpl">
  
</div>

		<script type="text/x-jsrender" id="kanbanTpl">
  			{{for data}}
				<div class="layui-col-md4">
					<div class="layui-card">
					  <div class="layui-card-header">{{cutText:KANBAN_NAME 30}}<span class="pull-right">{{call:CREATOR  fn='getUserName'}}</span></div>
					  <div class="layui-card-body">
						{{cutText:PROJECT_NAME 76}}
						<div class="mt-10">
							<button style="{{call:CREATOR fn='isSelfShow'}}" type="button" onclick="projectKanban('{{:KANBAN_ID}}',1)" class="btn btn-sm btn-default">修改</button>
							<div class="pull-right text-danger"><button onclick="kanbanDetail('{{:KANBAN_ID}}')" type="button" class="btn btn-sm btn-info"> 查看</button></div>
						</div>
					  </div>
					</div>
				  </div>
			{{/for}}
			  <div class="layui-col-md4">
				<div class="layui-card">
				  <div class="layui-card-header">新建面板</div>
				  <div class="layui-card-body" style='height:80px;text-align:center;'>
						<a class="btn btn-default btn-link mt-10" href="javascript:;" onclick="projectKanban();">点击新建</a>
				  </div>
				</div>
			  </div>
		   </script>