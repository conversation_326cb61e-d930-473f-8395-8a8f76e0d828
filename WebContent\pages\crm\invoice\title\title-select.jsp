<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>选择抬头</title>
</EasyTag:override>
<EasyTag:override name="content">
    <form method="post" class="form-inline" onsubmit="return false;" id="selectTitleForm">
        <div class="row">
            <div class="input-group input-group-sm ml-15" style="width: 200px">
                <span class="input-group-addon">客户名称</span>
                <input type="text" name="custName" value="${param.custName}" class="form-control input-sm">
            </div>
            <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectTitle.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
            <button type="button" class="btn btn-sm btn-info pull-right mr-20" onclick="SelectTitle.add()">+新增抬头</button>
        </div>
        <div class="ibox">
            <table id="SelectTitleList"></table>
        </div>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary"  type="button" onclick="SelectTitle.ok()">确定</button>
            <button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">

    <script type="text/javascript">
        var SelectTitle={
            sid:'${param.sid}',
            query : function(){
                $("#selectTitleForm").queryData({id:'SelectTitleList'});
            },
            initTable : function(){
                $("#selectTitleForm").initTable({
                        mars:'InvoiceDao.titleList',
                        id:'SelectTitleList',
                        page:true,
                        limit:20,
                        height:'400px',
                        rowDoubleEvent:'SelectTitle.ok',
                        cols: [[
                            {type:'${param.type}'},
                            {
                                type: 'numbers',
                                title: '序号',
                                align:'left'
                            },{
                                field: 'TITLE_NAME',
                                title: '开票抬头',
                                width: 100,
                            },{
                                field: 'BANK_NAME',
                                title: '开户行',
                                width: 200,
                            },{
                                field: 'BANK_NO',
                                title: '开户账号',
                                width: 150,
                            },{
                                field: 'ADDRESS',
                                title: '开票地址',
                                width:120
                            }
                        ]]
                    }
                );
            },
            ok : function(selectRow){
                if(selectRow==undefined){
                    var checkStatus = table.checkStatus('SelectTitleList');
                    if(checkStatus.data.length>0){
                        var data = checkStatus.data;
                        SelectTitleCallBack(data[0]);
                        popup.layerClose("selectTitleForm");
                    }else{
                        layer.msg("请选择!");
                    }
                }else{
                    SelectTitleCallBack(selectRow);
                    popup.layerClose("selectTitleForm");
                }
            },
            add:function(){
                popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/crm/invoice/title/title-edit.jsp',title:'新增抬头',data:{editSource:'selectTitle'},closeBtn:1});
             }
        };
        $(function(){
           SelectTitle.initTable();
        });

        function reloadSelectTitleList(){
            SelectTitle.query();
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>