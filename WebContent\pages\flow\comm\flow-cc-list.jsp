<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<div class="container-fluid">
  <form id="flowCCListForm" >
	 <input type="hidden" name="businessId" value="${param.businessId}"/>
	 <table class="table table-auto table-bordered table-hover table-condensed" style="margin-top: 10px;padding: 10px;">
	   <thead>
	      <tr>
		      <th style="width: 80px;">序号</th>
		      <th style="width: 110px;">抄送人</th>
		      <th style="width: 140px;">创建人</th>
		      <th style="width: 140px;">创建时间</th>
		      <th style="min-width: 100px;">抄送意见</th>
		      <th style="width: 140px;">查阅时间</th>
	      </tr>
	  </thead>
	   <tbody data-mars="FlowDao.ccList"  class="hidden-print" data-template="ccList"></tbody>
	</table>
  </form>
	 
</div>
					
<script id="ccList" type="text/x-jsrender">
  {{if data.length==0}}
		<tr><td  colspan="6" class="text-c">暂无数据</td></tr>
  {{/if}}
  {{for data}}
  	 <tr>
  			<td>{{:#index+1}}</td>
  			<td>{{:CC_BY_NAME}}</td>
  			<td>{{:CREATE_NAME}}</td>
  			<td>{{:CC_TIME}}</td>
  			<td>{{:CC_REMARK}}</td>
  			<td>{{:READ_TIME}}</td>
  	</tr>
  {{/for}}
</script>

<script>
	$(function(){
		$('#flowCCListForm').render();
	});
</script>