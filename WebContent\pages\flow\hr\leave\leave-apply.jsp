<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 120px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" data-template='{{:data14}}{{:data13}}天请假申请' readonly="readonly" value="${staffInfo.userName}${staffInfo.staffNo}的请假申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 120px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">岗位</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.post}"  class="form-control input-sm" name="apply.data1"/>
					  			</td>
					  			<td class="required">申请类别</td>
					  			<td>
						  			<select data-rules="required" class="form-control input-sm" name="apply.data14" onchange="calcDay();">
						  				<option value="">--</option>
						  				<option value="年休假">年休假</option>
						  				<option value="调休假">调休假 </option>
						  				<option value="事假">事假</option>
						  				<option value="病假">病假</option>
						  				<option value="婚假">婚假</option>
						  				<option value="丧假">丧假</option>
						  				<option value="产假或陪产假">产假或陪产假</option>
						  				<option value="产检假">产检假</option>
						  				<option value="哺乳假">哺乳假</option>
						  			</select>
					  			</td>
					  		</tr>
					  		<tr class="unedit-remove">
					  			<td>关联流程</td>
					  			<td colspan="3">
					  				<input type="hidden" name="apply.relatedFlow"/>
					  				<input type="text" data-flow-code="kq_overtime" placeholder="关联加班流程" onclick="Flow.selectRelatedFlow(this,'checkbox');" class="form-control input-sm"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">起止时间</td>
					  			<td style="width: 40%;">
					  				<input type="text" style="width: 40%;display: inline-block;" onchange="calcDay();" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 09:00:00',minTime:'9:00',maxTime:'18:00',doubleCalendar:false,alwaysUseStartDate:true})" class="form-control input-sm Wdate" name="apply.data11"/>
					  				<input type="text" style="width: 40%;display: inline-block;" onchange="calcDay();" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 18:00:00',minTime:'9:00',maxTime:'18:00',doubleCalendar:false,alwaysUseStartDate:true})" class="form-control input-sm Wdate" name="apply.data12"/>
					  			</td>
					  			<td class="required">请假天数</td>
					  			<td>
					  				<input type="number" data-edit-node="考勤备案" readonly="readonly" data-rules="required" placeholder="自动计算"  class="form-control input-sm" name="apply.data13"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">请假原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>证明文件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  		<tr class="hidden-print">
					  			<td>本年数据</td>
					  			<td colspan="3">
					  				<div data-mars="FlowCommon.kqTypeStat" data-template="kqStatTpl"></div>
					  				<script id="kqStatTpl" type="text/x-jsrender">
  	 									{{for data}}
											<span class="mr-10">{{:TYPE}}：{{:NUM}}天</span>
 										 {{/for}}
										{{if data.length==0}}无请假记录{{/if}}
									</script>
									<%--<a onclick="Flow.annualLeave();" href="javascript:;">剩余年假查询</a> --%>
								</td>
					  		</tr>
					  		<tr class="remindInfo hidden-print">
					  		   <td>注意事项</td>
					  		   <td colspan="3">
					  			1、员工请假，请提前到 OA 填报《请假申请》，做好工作安排，得到部门领导审批后方可离岗，若请假未获得审批而擅自离岗， 视为旷工处理。<br>
								2、员工请假，如有不可控制因素不能提前填报《请假申请》， 应用其他方式获得直属上司或相应管理层的许可，并于假期结束后 1 个工作日内补填《请假申请》；<br>
								3、员工申请调休假时，需在请假原因处说明用哪天的加班来调休，且OA系统有对应加班单；<br>
								4、员工申请病假、产假、陪产假、计划生育假、 婚假、 丧假等需提供相应有效证明的假别时， 如因暂缺有效证明而无法及时申请，可先向部门领导及人力行政部报备，但必须在假期结束后 2 个工作日内补填《请假申请》， 否则视为事假处理。<br>
								5、对于上述填报情况，若未及时补填请假申请单的， 最迟不能超过 5 个工作日，否则，将视为旷工处理。<a onclick="FlowCore.historyApply();" href="javascript:;">申请记录</a>
					  		  </td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/yq-work/static/js/datediff.js?v=20250418"></script>
	<script type="text/javascript">

		var Flow = {};

		$(function(){
			FlowCore.initPage({hideHistoryApply:false,success:function(data){
				Flow.initData();
			}});
		});

		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}

		Flow.insertData = function() {
			FlowCore.insertData({data:Flow.getStartNode(),success:function(result){
				var id = result.data;
				//更新加班表字段
			}});
		}
		
		Flow.selectRelatedFlow = function(el,type){
			var id = new Date().getTime();
			$(el).attr('data-sid',id);
			var data = $.extend($(el).data(),{sid:id,type:type});
			popup.layerShow({id:'selectRelatedFlow',full:fullShow(),scrollbar:false,area:['80%','80%'],offset:'r',title:'选择加班流程',url:'/yq-work/pages/flow/hr/overtime/select-overtime-flow.jsp',data:data});
		}

		Flow.updateData = function() {
			FlowCore.updateData({data:Flow.getStartNode()});
		}

		Flow.getStartNode = function(){
			var type = $("[name='apply.data14']").val();
			if(type=='事假'||type=='调休假'){
				return {nextNodeCode:'部门主管'}
			}
			return {};
		}

		Flow.initData = function() {
			var params = {};
			var applyInfo = FlowCore.applyInfo;
			var days = applyInfo.data13;
			var type = applyInfo.data14;
			var approveInfo = FlowCore.approveInfo;
			var nodeCode = approveInfo.currentNode.nodeCode;

			if(nodeCode=='部门主管'){
				if(days>=4){
					params['nextNodeCode'] = '中心副总';
				}else{
					params['nextNodeCode'] = '考勤备案';
				}
				var applyUserInfo = FlowCore.applyUserInfo;
				if(applyUserInfo.workCity=='北京'){
					params['ccNames'] = '蔡洁';
				}
			}else if(nodeCode=='中心副总'){
				if(days>7){
					params['nextNodeCode'] = '总裁';
				}else{
					params['nextNodeCode'] = '考勤备案';
				}
			}
			FlowCore.approveData = params;
		}

		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}

		FlowCore.flowStart = function(result){
			FlowCore.flowEnd(result);
		}
		
		FlowCore.flowEnd = function (result) {
			var applyInfo = FlowCore.applyInfo;
			var data = {applyId: applyInfo['applyId']};
			ajax.remoteCall("${ctxPath}/servlet/kaoqin?action=setLeaveOrTravel", data, function (result) {
				if (result.state == 1) {
					console.log("请假请求已添加到考勤记录！");
				} else {
					layer.alert(result.msg, {icon: 5});
				}
			});
		}


		Flow.leaveHistory = function(){

		}

		Flow.annualLeave = function(){
			var applyInfo = FlowCore.applyInfo;
			var data = FlowCore.params;
			data['applyBy'] = applyInfo.applyBy;
			data.isDiv = '1';
			popup.layerShow({type:1,url:'/yq-work/pages/ehr/annualLeave/my-annual-leave.jsp',full:fullShow(),area:['68%','100%'],scrollbar:false,offset:'r',data:data,id:'historyApply',title:'年假查询'});
		}
		
		function calcDay(){
			var start = $("[name='apply.data11']").val();
			var end = $("[name='apply.data12']").val();
			if(start&&end){
				if(end<=start){
					layer.msg('结束日期应该小于开始日期',{icon:2});
					$("[name='apply.data12']").val('');
					return;
				}
				var type = $("[name='apply.data14']").val();
				if(type!='产假或陪产假'&&type!='哺乳假'){
					if(start.substring(0,7)!=end.substring(0,7)){
						layer.msg('请不要跨月份请假或分多次请假',{icon:2});
						$("[name='apply.data12']").val('');
						return;
					}
				}
				var val = dateDiff.diff(start,end);
				$("[name='apply.data13']").val(val);
			}
		}


</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>