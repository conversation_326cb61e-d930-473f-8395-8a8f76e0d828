<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
		.layui-table-cell {
			height: auto;
		}
		.layui-table-cell{padding: 0 6px;}
		.layui-progress{margin-top: 13px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>项目管理</h5>
	          		     <div class="input-group input-group-sm" style="width: 160px;">
							 <span class="input-group-addon">项目名称</span>	
							 <input type="text" name="projectName" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm hidden" style="width:130px;">
							 <span class="input-group-addon">部门</span>	
							 <select name="deptId" data-mars="EhrDao.allDept" data-mars-reload="false" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                   	</select>
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 160px;">
							 <span class="input-group-addon">项目编号</span>	
							 <input type="text" name="projectNo" class="form-control input-sm">
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目类型</span>	
							 <select name="projectType" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="10" data-class="label label-info">合同项目</option>
		                    		<option value="11" data-class="label label-info">提前执行</option>
		                    		<option value="20" data-class="label label-warning">自研项目</option>
		                    		<option value="30" data-class="label label-warning">维保项目</option>
	                    	</select>
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">项目状态</span>	
							 <select name="projectState" onchange="list.query()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="11" data-class="text-info">进度正常</option>
		                    		<option value="12" data-class="text-warning">存在风险</option>
		                    		<option value="13" data-class="text-danger">进度失控</option>
		                    		<option value="20" data-class="text-danger">挂起中</option>
		                    		<option value="30" data-class="text-success">已完成</option>
	                    	</select>
					     </div>
					       <div class="input-group input-group-sm"  style="width: 140px">
							 <span class="input-group-addon">负责人</span>	
							 <input type="hidden" name="userId" class="form-control input-sm">
							 <input type="text" id="userId" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
  			{{if currentUserId == CREATOR || isSuperUser}}<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
		</script>
		<script type="text/x-jsrender" id="progressTpl">
			{{if H>0}}
			<div class="layui-progress" lay-showPercent="true">
  				<div class="layui-progress-bar {{if H==K}} layui-bg-green {{else K/H <= 0.6}} layui-bg-red {{else K/H <= 0.8}} layui-bg-orange  {{else}} layui-bg-blue {{/if}}" lay-percent="{{:K}}/{{:H}}"></div>
			</div>
			{{else}}
				0/0
			{{/if}}
		</script>
		 <script type="text/html" id="btnBar">
			 <EasyTag:res resId="PROJECT_ADD">
				<div class="input-group input-group-sm">
					<button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 新增</button>
				</div>
			</EasyTag:res>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ProjectDao.projectList',
					height:'full-120',
					skin:'line',
					autoSort:false,
					toolbar:'#btnBar',
					limit:20,
					cols: [[
					  {title:'序号',type:'numbers'},
		              {
					    field: 'PROJECT_NAME',
						title: '项目名称',
						align:'left',
						minWidth:200,
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail',
						templet:function(row){
							var type = row['PROJECT_TYPE'];
							var contractId = row['CONTRACT_ID'];
							var no = row['PROJECT_NO'];
							no = no==0?'':no;
							if(contractId==''){
								no  = " <span class='label label-danger'>无合同</span> "+ no;
							}
							if(type==10){
								return no +"&nbsp;"+ row.PROJECT_NAME;
							}else{
								var val = getText(type,'projectType');
								return val + "&nbsp;" + no + row.PROJECT_NAME;
							}
						}
					},
					{
					    field: 'H',
						title: '完成数/总数',
						sort:true,
						align:'center',
						minWidth:140,
						event:'list.taskMgr',
						templet:function(row){
							 return renderTpl('progressTpl',row);;
						}
					},
					{
					    field: 'D',
						title: '未完成数',
						align:'left',
						width:120,
						sort:true,
						event:'list.taskMgr',
						templet:function(row){
							return row['D']+"（<span title='待办/进行中/超时'>"+row['A']+"&nbsp;/&nbsp;"+row['B']+"&nbsp;/&nbsp;"+row['G']+"</span>）";
						}
					},{
					    field: 'PROJECT_STATE',
						title: '项目状态',
						align:'center',
						width:80,
						templet:function(row){
							return getText(row.PROJECT_STATE,'projectState');
						}
					},{
					    field: 'PO',
						title: '负责人',
						align:'center',
						width:80,
						templet:function(row){
							return getUserName(row.PO);
						}
					},{
					    field: 'BEGIN_DATE',
						title: '开始时间',
						width:90,
						sort:true,
						align:'center'
					},{
					    field: 'END_DATE',
						sort:true,
						title: '截止时间',
						width:90,
						align:'left'
					},{
						title: '操作',
						align:'center',
						width:80,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							row['isSuperUser']=isSuperUser;
						    return renderTpl('bar1',row);
						}
					}
					]],done:function(){
						table.on('sort(list)', function(obj){
						  $("#searchForm").queryData({jumpOne:true,initSort:obj,data:{field:obj.field,order:obj.type}});
						});
						layui.use(['element'], function(){
							var element = layui.element;
							element.render();
						});
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData({jumpOne:true});
			},
			detail:function(data){
				var projectId = data.PROJECT_ID;
				popup.openTab({id:projectId,url:'${ctxPath}/pages/project/project-detail.jsp',title:'项目总览',data:{projectId:projectId}});
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'编辑项目',data:{projectId:data.PROJECT_ID}});
			},
			taskMgr:function(data){
				popup.openTab({url:'${ctxPath}/pages/task/task-list.jsp',id:'taskMgr',title:'项目任务管理',data:{projectId:data['PROJECT_ID']}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/project/project-edit.jsp',title:'新增项目'});
			},
			addTask:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/task/task-edit.jsp',title:'新增任务',data:{projectId:data.PROJECT_ID}});
			}
		}
		function reloadTaskList(){
			
		}
		function reloadProjectList(){
			list.query({jumpOne:true});
		}
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
			
			layui.config({
				  base: '${ctxPath}/static/js/'
			}).use('tableSelect'); //加载自定义模块
			
			layui.use(['element','tableSelect'], function(){
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#userId',
					searchKey: 'userName',
					checkedKey: 'USER_ID',
					page:true,
					searchPlaceholder: '请输入姓名',
					table: {
						mars: 'EhrDao.userList',
						cols: [[
							{ type: 'radio' },
							{ field: 'USER_ID',hide:true,title: 'ID'},
							{ field: 'USERNAME', title: '姓名' },
							{ field: 'DEPTS', title: '部门' },
							{ field: 'DATA1', title: '工号'}
						]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.USERNAME)
							ids.push(item.USER_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
					},
					clear:function(){
						elem.prev().val('');
					}
				});
			});
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>