package com.yunqu.work.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/product")
public class ProductServlet extends AppBaseServlet{
	private static final long serialVersionUID = -7024355585094693968L;
	
	public EasyResult actionForAdd(){
		EasyRecord record=new EasyRecord("yq_product","product_id");
		try {
			record.setColumns(getJSONObject("product"));
			record.setPrimaryValues(RandomKit.uuid());
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		EasyRecord record=new EasyRecord("yq_product","product_id");
		try {
			record.setColumns(getJSONObject("product"));
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelProduct(){
		try {
			String id=getJsonPara("productId");
			if(StringUtils.isNotBlank(id)) {
				this.getQuery().executeUpdate("delete from yq_product where product_id  = ?",id);
				this.getQuery().executeUpdate("delete from yq_product where p_product_id  = ?",id);
				this.getQuery().executeUpdate("delete from yq_files where fk_id  = ?",id);
			}
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}

}
