<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<style type="text/css">
	.avatar.size-L, .avatar.size-L img {
	    width: 48px;
	    height: 48px;
        border-radius: 50%;
	}
	.commentList,.comment-write{margin:5px 15px;}
	.commentList .item.comment-flip {padding-left: 64px;padding-right: 0;}
	.commentList .item {list-style: none outside none;margin: 1rem 0 0;padding-right: 64px;}
	.commentList .item{list-style: none outside none;margin: 1rem 0 0}
	.commentList .avatar{border: 1px solid transparent;float: left}
	.comment-main{position:relative;margin-left:64px;border:1px solid #dedede;border-radius:2px}
	.comment-main:before,.comment-main:after{position:absolute;top:11px;left:-16px;right:100%;width:0;height:0;display:block;content:" ";border-color:transparent;border-style:solid solid outset;pointer-events:none}
	.comment-main:before{border-right-color:#dedede;border-width:8px}
	.comment-main:after{border-width:7px;border-right-color:#f8f8f8;margin-top:1px;margin-left:2px}
   	.comment-header{padding:10px 15px;background:#f8f8f8;border-bottom:1px solid #eee}
    .comment-title{margin:0 0 8px 0;font-size:1rem;line-height:1.2}
    .comment-meta{font-size:13px;color:#999;line-height:1.2}
    .comment-meta a{color:#999}
    .comment-author{font-weight:700;color:#999}
    .comment-body{padding:15px 15px 5px;overflow:hidden;line-height: 24px;}
    .comment-body>:last-child{margin-bottom:0}
	.commentList .comment-flip .avatar {float: right}
	.comment-flip .comment-main{margin-left: 0; margin-right: 64px}
	.comment-flip .comment-main:before {border-left-color: #dedede;border-right-color: transparent}
	.comment-flip .comment-main:before, .comment-flip .comment-main:after {left: 100%;position: absolute;right: -16px}
	.comment-flip .comment-main:after {border-left-color: #f8f8f8;border-right-color: transparent;margin-left: auto;margin-right: 2px}
	.comment-edit{display: none;}
	.comment-write{margin-top: 20px;height: 130px;line-height: 130px;}
</style>
<form id="updateLogForm">
		<div class="comment-write">
			<div class="pull-left" style="width: 66px;margin-top: -15px;">
				<i class="avatar size-L radius"><img src="/yq-work/static/images/user-avatar-large.png"></i>
			</div>
			<div class="input-group input-group-sm" style="width: 250px;margin-bottom: 5px;">
				 <span class="input-group-addon">@人员</span>
				 <input type="hidden" id="toSayPersonId">
				 <input type="text" id="toSayPersonName" placeholder="指定人员留言" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm select-icon">
				 <span class="input-group-addon">
			    	<a href="javascript:;" class="ml-5 clearPerson" onclick="clearPerson(this);">清空</a>
				 </span>
		    </div>
			<textarea class="form-control" id="flowContent" style="display: inline-block;width:calc(100% - 130px);height: 90px;"></textarea>
			<button class="btn btn-danger pb-10" onclick="submitRevise()" type="button" style="width: 56px;height: 64px;line-height: 16px;margin-bottom: 65px">发表<br>评论</button>
		</div>
		<c:if test="${!empty param.replyContent}"><blockquote style="margin: -5px 82px 10px;font-size: 12px;color: #999;" class="layui-elem-quote layui-text"><font color="red">回复内容：</font><span id="replyContent">${param.replyContent}</span> <a class="btn btn-link btn-xs" href="javascript:void(0);" onclick="$(this).parent().remove()">清除</a></blockquote></c:if>
		<ul class="commentList" data-template="revise-template" data-mars="FlowCommon.followList"></ul><br><br>
        <script id="revise-template" type="text/x-jsrender">
			{{for data}}
		      <li data-userid="{{:ADD_USER_ID}}" class="item cl"> <a href="javascript:;"><i class="avatar size-L radius"><img  onclick="userInfoLayer('{{:ADD_USER_ID}}')" onerror="this.src='${ctxPath}/static/images/user-avatar-large.png'" src="{{call:ADD_USER_ID fn='getUserPic'}}"></i></a>
              <div class="comment-main">
                <header class="comment-header">
                  <div class="comment-meta"><a class="comment-author" onclick="userInfoLayer('{{:ADD_USER_ID}}')" href="javascript:;">{{:ADD_USER_NAME}}</a> 评论于
                    <time>{{:ADD_TIME}}</time>
                  </div>
                </header>
                <div class="comment-body">
                  <p> {{if PREV_FOLLOW_CONTENT}} <blockquote class="layui-elem-quote layui-text"><a href="javascript:;" style="color:#4298fd;" onclick="userInfoLayer('{{:TO_USER_ID}}')">{{:TO_USER_NAME}}</a>： <br>{{call:PREV_FOLLOW_CONTENT fn='getContent'}} </blockquote> {{else TO_USER_ID}} <a href="javascript:;" style="color:#4298fd;" onclick="userInfoLayer('{{:TO_USER_ID}}')">@{{:TO_USER_NAME}}</a> <br>{{/if}} {{call:FOLLOW_CONTENT fn='getContent'}}</p>
                  <p class="hidden">{{:FOLLOW_CONTENT}}</p> 
				 <p class="pull-right"><a class="f-12 ml-10" onclick="toTaFollow('{{:ADD_USER_ID}}','{{:ADD_USER_NAME}}')" href="javascript:;">@TA</a><a class="f-12 ml-10 comment-edit" onclick="editFollow('{{:FOLLOW_ID}}',this)" href="javascript:;">修改</a><a class="f-12 ml-10" onclick="replyFollow('{{:FOLLOW_ID}}','{{:ADD_USER_ID}}',this)" href="javascript:;">回复</a></p>
                </div>
              </div>
             </li>
			{{/for}}
			{{if data.length==0}}
				<div style="padding:20px 35%;" ><img src="/easitline-static/images/nodata.png"></div>
			{{/if}}					         
	   </script>  
       <script type="text/javascript">
       
       		var FlowComment = {toUserName:'${param.toUserName}',toUserId:'${param.toUserId}',resultId:'${param.resultId}'};
       		
       		$(function(){
       			loadFollow();
       			if(FlowComment.toUserName){
	       			//$('#flowContent').val('@'+FlowComment.toUserName+"：");
	       			$('#toSayPersonName').val(FlowComment.toUserName);
	       			$('#toSayPersonId').val(FlowComment.toUserId);
       			}
       		});
       		
       		function loadFollow(){
       			$('#updateLogForm').render({data:FlowCore.params,success:function(){
       				var staffInfo = getStaffInfo();
       				var userId = staffInfo.userId;
       				$("[data-userid]").each(function(){
       					var t = $(this);
       					var _userId = t.data('userid');
       					if(userId==_userId){
       						t.addClass('comment-flip');
       						t.find('.comment-edit').show();
       					}
       				});
       			}});
       		}
       		
       		function submitRevise(){
       			var val = $('#flowContent').val();
       			if(val==''){
       			    layer.msg('不能为空',{icon:2});
       				return;
       			}
       			var toSayPersonName = $('#toSayPersonName').val();
       			var toSayPersonId = $('#toSayPersonId').val();
       			if(toSayPersonName){
     				var prevFollowContent = $('#replyContent').text()||'';
      				var data = $.extend({flowContent:val,toUserId:toSayPersonId,toUserName:toSayPersonName,prevFollowContent:prevFollowContent},FlowCore.params);
   		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addComment",setFollowData(data),function(result) { 
   						if(result.state == 1){
   							clearPerson();
   							$('#replyContent').parent().remove();
   							$('#flowContent').val('');
   							layer.msg(result.msg,{icon:1,time:800},function(){
   	    						loadFollow();
   							})
   						}else{
   							layer.alert(result.msg,{icon: 5});
   						}
   					});
       				return;
       			}
       			var data = $.extend({flowContent:val},FlowCore.params);
    			ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addComment",setFollowData(data),function(result) { 
    				if(result.state == 1){
    					layer.msg(result.msg,{icon:1,time:1200},function(){
    						$('#flowContent').val('');
    						loadFollow();
    					});
    				}else{
    					layer.alert(result.msg,{icon: 5});
    				}
    			});
       		}
       		
       		function replyFollow(followId,addUserId,el){
       		   layer.prompt({formType: 2,value: '',offset:'20px',maxlength:8000,placeholder:'最大输入2000字',title: '请输入回复信息',area: ['500px', '220px']}, function(flowContent, index, elem){
		    	 	layer.close(index);
		    	 	var prevFollowContent = $(el).parent().prev().text();
		    		var data = $.extend({flowContent:flowContent,pFollowId:followId,addUserId:addUserId,prevFollowContent:prevFollowContent},FlowCore.params);
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addComment",setFollowData(data),function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800},function(){
	    						loadFollow();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
		    	});
       		}
       		
       		function toTaFollow(toUserId,toUserName){
       			layer.prompt({formType: 2,value: '',offset:'20px',maxlength:8000,title: '请输入内容',placeholder:'最大输入2000字',area: ['500px', '220px']}, function(flowContent, index, elem){
		    	 	layer.close(index);
		    		var data = $.extend({flowContent:flowContent,toUserId:toUserId,toUserName:toUserName},FlowCore.params);
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addComment",setFollowData(data),function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800},function(){
	    						loadFollow();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
		    	});
       		}
       		
       		function editFollow(followId,el){
       		   var content = $(el).parent().prev().text();
       		   layer.prompt({formType: 2,value: content,offset:'20px',maxlength:8000,title: '修改回复',placeholder:'最大输入2000字',area: ['500px', '220px']}, function(flowContent, index, elem){
		    	 	layer.close(index);
		    		var data = $.extend({flowContent:flowContent,followId:followId},FlowCore.params);
		    	  	ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=updateComment",setFollowData(data),function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:800},function(){
	    						loadFollow();
							})
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});	    	  	
		    	});
       		}
       		
       		function setFollowData(data){
       			if(FlowComment.resultId){
       				data['resultId'] = FlowComment.resultId;
       			}
       			return data;
       		}
       		
       		function clearPerson(){
       			$('#toSayPersonName').val('');
       			$('#toSayPersonId').val('');
       		}
       		
       </script>
</form>