<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>通知公告</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <span class="mr-30" id="title"></span>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">标题</span>	
							 <input type="text" name="title" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right" id="pubBtn">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 发布</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/html" id="bar">
  			<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.edit">编辑</a>
 			<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		var type="${param.type}";
		var typeNames={0:'我的动弹',"1":"通知公告","2":'最新动态',3:'交流分享'}
		$(function(){
			if(type==0){
				$("#pubBtn").hide();
			}
			$("#title").text(typeNames[type]);
			//$("#searchForm").render({success:function(){
				list.init();
			//}});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'RemindDao.list',
					data:{type:type},
					autoFill:true,
					cols: [[
		             {
						type:'checkbox'
					 },{
					    field: 'TITLE',
						title: '标题',
						align:'left'
					},{
					    field: 'PUBLISH_BY',
						title: '发布人',
						width:120,
						align:'left'
					},{
					    field: 'STATUS',
						title: '状态',
						width:120,
						align:'center',
						templet:function(row){
							var json={0:'已发布',1:'草稿',2:'<font color="red">已删除</font>'};
							return json[row['STATUS']]||'';
						}
					},{
					    field: 'CREATOR',
						title: '创建人',
						width:120,
						align:'left',
						templet:function(row){
							return getUserName(row.CREATOR);
						}
					},{
					    field: 'PUBLISH_TIME',
						title: '发布时间',
						width:180,
						align:'left'
					},{
					    field: 'VIEW_COUNT',
						title: '阅读量',
						width:120,
						align:'left'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						width:180,
						align:'left'
					},{
						title: '操作',
						width:120,
						align:'left',
						toolbar: '#bar'
					}
					]],done:function(){
						table.resize({id:'list'});
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/remind/remind-edit.jsp',title:'编辑',data:{remindId:data.REMIND_ID,type:type}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/remind/remind-edit.jsp',title:'新增'+typeNames[type],data:{type:type}});
			},
			detail:function(data){
				popup.openTab({id:'remindDetail',url:'${ctxPath}/pages/remind/remind-detail.jsp',title:'详情',data:{remindId:data.REMIND_ID,type:type}});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>