<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购详情</title>
	<style>
		.form-horizontal{width: 100%;}
		.layui-table-cell a{font-size: 12px;color: #3E84E9;cursor: pointer;}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 14px;}
		.layui-timeline-item:hover a{opacity:1;}
		.layui-tab-card>.layui-tab-title{background-color: #fff;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		.layui-timeline-content p{font-size: 13px;color: #666;}
		.layui-timeline-title{margin-bottom: 2px;}
		.layui-timeline-item{margin-bottom: 2px;}
		.layui-text h3{font-size: 16px;}
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.gray-bg{background-color: #e8edf7;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="OrderDetailForm">
			    <input type="hidden" value="${param.orderId}" id="orderId" name="orderId"/>
 	  	        <input type="hidden" value="${param.orderId}" name="pFkId"/>
 	  	        <input type="hidden" value="${param.custId}" name="custId"/>
				<div style="padding: 5px" class="ibox-content" data-mars="OrderDao.applyInfo" data-mars-prefix="apply.">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md8">
					     <i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 18px;" name='apply.CONTRACT_NAME'></span>
					  </div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm">
									<button class="btn btn-default btn-xs" onclick="editOrder();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
								</div>
								<div class="btn-group btn-group-sm">
									<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">•••</button>
									<ul class="dropdown-menu dropdown-menu-right">
									    <li><a href="javascript:;" onclick="applyReview()">发起采购评审</a></li>
									    <li><a href="javascript:;" onclick="addFollow()">发起付款申请</a></li>
									    <li><a href="javascript:;" onclick="addFollow()">新增跟进记录</a></li>
									    <li><a href="javascript:;" onclick="addFollow()">删除采购</a></li>
									</ul>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding:10px 15px;">
					  <div class="layui-col-md2">
					  	<span class="h-title">单据编号</span>
					  	<span class="h-value" name="apply.ORDER_NO"></span>
					  	<input type="hidden" name="apply.SUPPLIER_ID"/>
					  	<input type="hidden" name="apply.CONTRACT_ID"/>
					  </div>
					  <div class="layui-col-md3">
					  	<span class="h-title">供应商</span>
					  	<span class="h-value" name="apply.SUPPLIER_NAME">--</span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">状态</span>
					  	<span class="h-value" name="apply.LAST_FOLLOW_LABEL">--</span>
					  </div>
					  <div class="layui-col-md3">
					  	<span class="h-title">客户名称</span>
					  	<span class="h-value" name="apply.CUST_NAME"></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">更新时间</span>
					  	<span class="h-value" name="apply.UPDATE_TIME"></span>
					  </div>
					</div>
				</div>
				<div class="layui-card mt-10 hidden" style="margin-bottom: 0px;">
				  <div class="layui-card-header">审批流信息</div>
				  <div class="layui-card-body">
					    
				  </div>
				</div>
				<div class="layui-row">
					<div class="layui-col-md9">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">基本信息</li>
						    <li>物料信息(<span id="goodsCount">0</span>)</li>
						    <li>入库记录</li>
						    <li>出库记录</li>
						    <li>附件列表(<span id="fileCount">0</span>)</li>
						    <li>采购评审</li>
						    <li>付款申请</li>
						  </ul>
						  <div class="layui-tab-content" style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
						    <div class="layui-tab-item layui-show">
								<jsp:include page="include/base-info.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/goods-mgr.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/storage-in.jsp">
						    		<jsp:param value="2" name="type"/>
						    	</jsp:include>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/storage-out.jsp">
						    		<jsp:param value="2" name="type"/>
						    	</jsp:include>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/file-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						  </div>
					</div>
					</div>
					<div class="layui-col-md3">
						<div class="layui-card" style="margin: 10px 0px 15px 5px;border: 1px solid #eee;">
						  <div class="layui-card-header" style="background-color: #fff;border-bottom: 1px solid #eee;">动态
							 <button type="button" class="btn btn-xs btn-default pull-right mt-10" onclick="addFollow()">+新建</button>
						  </div>
						  <div class="layui-card-body followList" style="height: calc( 100vh - 180px);overflow-y: scroll;">
								<jsp:include page="include/follow-list.jsp"></jsp:include>
						  </div>
						</div>
					</div>
				</div>
		</form>
		<form  id="orderDetailFileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="orderDetailLocalfile" onchange="orderUploadFile()"/>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   
		jQuery.namespace("OrderDetail");
	    
		OrderDetail.orderId='${param.orderId}';
		
		var orderId = '${param.orderId}';
		
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
				var form = layui.form;
				form.render();
			});
			$("#OrderDetailForm").render({success:function(result){
				
			}});  
		});
		
		
		function addFollow(){
			popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'新增跟进',data:{orderId:orderId}});
			
		}
		
		function applyReview(){
			popup.layerShow({id:'applyReview',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/include/apply-review.jsp',title:'发起评审',data:{orderId:orderId}});
		}
		
		function editFollow(followId){
			popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'修改跟进',data:{orderId:orderId,followId:followId}});
			
		}
		function moreFollow(){
			popup.openTab({id:'orderFollowList',url:'${ctxPath}/pages/erp/order/follow-query.jsp',title:'采购跟进记录',data:{orderId:orderId}});
		}
		
		function editOrder(){
			popup.layerShow({id:'editOrder',scrollbar:false,title:'修改',area:['600px','500px'],url:'${ctxPath}/pages/erp/order/order-add.jsp',data:{orderId:orderId}});
		}
		
		function contractDetail(){
			var contractId = $("[name='apply.CONTRACT_ID']").val();
			var custId = $("[name='custId']").val();
			popup.openTab({id:'contractDetail',title:'合同详情',url:'${ctxPath}/pages/crm/contract/contract-detail.jsp',data:{contractId:contractId,custId:custId}});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>