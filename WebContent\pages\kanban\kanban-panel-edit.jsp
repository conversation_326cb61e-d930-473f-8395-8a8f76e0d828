<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>Kanban</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editForm" class="form-horizontal" data-mars="KanbanDao.panelInfo" data-mars-prefix="panel.">
	     		<input type="hidden" name="panelId" value="${param.panelId}"/>
	     		<input type="hidden" name="panel.PANEL_ID" value="${param.panelId}"/>
	     		<input type="hidden" name="panel.KANBAN_ID" value="${param.kanbanId}"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
			            	<td style="width: 60px;">名称</td>
		                    <td><input data-rules="required"  name="panel.PANEL_NAME" class="form-control"/></td>
			            </tr>
			            <tr>
			            	<td style="width: 60px;">排序</td>
		                    <td><input type="number" data-rules="required" placeholder="面板从小到大排序" value="1" name="panel.ORDER_INDEX" class="form-control"/></td>
			            </tr>
			            <tr>
			            	<td>主题</td>
			            	<td>
			            		<select name="panel.PANEL_THEME" class="form-control">
			            			<option value="">默认</option>
			            			<option value="panel-primary">panel-primary</option>
			            			<option value="panel-success">panel-success</option>
			            			<option value="panel-warning">panel-warning</option>
			            			<option value="panel-danger">panel-danger</option>
			            			<option value="panel-info">panel-info</option>
			            		</select>
			            	</td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary" onclick="KanbanPanel.ajaxSubmitForm()"> 保 存  </button>
					      <button type="button" title="关闭" class="btn btn-default ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	   
		var KanbanPanel = {};
		KanbanPanel.panelId = '${param.panelId}';
		$(function(){
			if(KanbanPanel.panelId){
				$("#editForm").render();
			}
		});

			
		KanbanPanel.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(KanbanPanel.panelId){
					KanbanPanel.updateData(); 
				}else{
					KanbanPanel.insertData(); 
				}
			}
		}
		KanbanPanel.insertData = function(flag) {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/kanban?action=addPanel",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						window.location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		KanbanPanel.updateData = function(flag) {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/kanban?action=updatePanel",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						window.location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>