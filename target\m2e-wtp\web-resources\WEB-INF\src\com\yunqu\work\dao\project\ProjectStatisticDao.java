package com.yunqu.work.dao.project;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.utils.DateUtils;

@WebObject(name = "ProjectStatisticDao")
public class ProjectStatisticDao extends AppDaoContext {

    @WebControl(name = "projectDashboardStat", type = Types.RECORD)
    public JSONObject projectDashboardStat() {
        JSONObject result = new JSONObject();
        
        EasySQL sql = new EasySQL("select count(1) from yq_project t1 where 1=1");
        setCondition(sql);
        int projectCount = Db.queryInt(sql.getSQL(),sql.getParams());
        result.put("projectCount", projectCount);
        
        EasySQL sql1 = new EasySQL("select count(1) from yq_project t1 where 1=1");
        setCondition(sql1);
        sql1.appendIn(new String[] {"待初验","待一次性验收","提前执行","待终验","维保在保"}, "and PROJECT_STAGE");
        
        int projectIngCount = Db.queryInt(sql1.getSQL(),sql1.getParams());
        result.put("projectIngCount", projectIngCount);
        
        EasySQL sql2 = new EasySQL("select count(1) from yq_task t2,yq_project t1 where t1.project_id = t2.project_id");
        setCondition(sql2);
        sql2.appendIn(new int[] {10,20},"and t2.task_state");
        int taskCount = Db.queryInt(sql2.getSQL(),sql2.getParams());
        result.put("taskCount", taskCount);
        
        EasySQL sql3 = new EasySQL("select count(1) from yq_project_risk t2,yq_project t1 where t1.project_id = t2.project_id");
        setCondition(sql3);
        sql3.append("and t2.risk_state = 0");
        int riskCount = Db.queryInt(sql3.getSQL(),sql3.getParams());
        result.put("riskCount", riskCount);
        return getJsonResult(result);
    }


    @WebControl(name = "projectCountByTime", type = Types.LIST)
    public JSONObject projectCountByTime() {
        String beginDate = param.getString("beginDate");
        String endDate = param.getString("endDate");
        String beginMonth = StringUtils.isBlank(beginDate) ? EasyCalendar.newInstance().getYear() + "-01" : beginDate.substring(0, 7);
        String endMonth = StringUtils.isBlank(endDate) ? EasyCalendar.newInstance().getYear() + "-12" : endDate.substring(0, 7);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar beginCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();
        try {
            beginCal.setTime(sdf.parse(beginMonth));
            endCal.setTime(sdf.parse(endMonth));
        } catch (ParseException e) {
            return EasyResult.fail("日期格式错误");
        }
        Calendar calendar = (Calendar) beginCal.clone();

        int monthPeriod = (endCal.get(Calendar.YEAR) - beginCal.get(Calendar.YEAR)) * 12 + endCal.get(Calendar.MONTH) - beginCal.get(Calendar.MONTH) + 1;

        String[] timeTypes = {"sign_date", "zy_real_date"};
        JSONObject result = new JSONObject();
        for (String timeType : timeTypes) {
            EasySQL sql = new EasySQL("select ");
            for (int j = 1; j <= monthPeriod; j++) {
                String month = sdf.format(calendar.getTime());
                String monthName = month.replace("-", "_");
                sql.appendLike(month, "SUM(CASE WHEN t1." + timeType + " like ? THEN 1 ELSE 0 END)");
                if (j == monthPeriod) {
                    sql.append("AS " + monthName);
                } else {
                    sql.append("AS " + monthName + ",");
                }
                calendar.add(Calendar.MONTH, 1);
            }
            sql.append("from yq_project t1");
            sql.append("where 1=1");
            setCondition(sql);
//          sql.append(param.getString("projectType")," and project_type = ?");

            JSONObject resultTemp = queryForRecord(sql.getSQL(), sql.getParams());
            JSONObject data = resultTemp.getJSONObject("data");
            result.put(timeType, data);
            calendar.setTimeInMillis(beginCal.getTimeInMillis());
        }
        return result;
    }

    @WebControl(name = "projectFieldRatio", type = Types.LIST)
    public JSONObject projectFieldRatio() {
        String fieldName = param.getString("fieldName");
        EasySQL sql = getEasySQL("SELECT count(project_id) AS NUMS, COALESCE(NULLIF(" + fieldName + ", ''), '未填') AS FIELD_NAME");
        sql.append("from yq_project t1 where 1=1");
        this.setCondition(sql);
        sql.append("GROUP BY COALESCE(NULLIF(" + fieldName + ", ''), '未填')");
        setOrderBy(sql, " order by NUMS desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectStage", type = Types.LIST)
    public JSONObject projectStage() {
        EasySQL sql = getEasySQL("SELECT count(project_id) AS NUMS, COALESCE(NULLIF(PROJECT_STAGE, ''), '未填') AS PROJECT_STAGE");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("GROUP BY COALESCE(NULLIF(PROJECT_STAGE, ''), '未填')");
        setOrderBy(sql, " order by NUMS desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectState", type = Types.LIST)
    public JSONObject projectState() {
        EasySQL sql = getEasySQL("SELECT count(project_id) AS NUMS, PROJECT_STATE");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("GROUP BY COALESCE(NULLIF(PROJECT_STATE, ''), '未填')");
        setOrderBy(sql, " order by NUMS desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectPersonCountStat", type = Types.LIST)
    public JSONObject projectPersonCountStat() {
        EasySQL sql = getEasySQL("SELECT");
        sql.append("SUM(CASE WHEN PERSON_COUNT < 5 THEN 1 ELSE 0 END) AS '低于5人',");
        sql.append("SUM(CASE WHEN PERSON_COUNT >= 5 AND PERSON_COUNT < 10 THEN 1 ELSE 0 END) AS '5-10人',");
        sql.append("SUM(CASE WHEN PERSON_COUNT >= 10 AND PERSON_COUNT < 15 THEN 1 ELSE 0 END) AS '10-15人',");
        sql.append("SUM(CASE WHEN PERSON_COUNT >= 15 AND PERSON_COUNT < 20 THEN 1 ELSE 0 END) AS '15-20人',");
        sql.append("SUM(CASE WHEN PERSON_COUNT >= 20 THEN 1 ELSE 0 END) AS '20人及以上'");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectWorkPeopleCountStat", type = Types.LIST)
    public JSONObject projectWorkPeopleCountStat() {
        EasySQL sql = getEasySQL("SELECT");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT < 5 THEN 1 ELSE 0 END) AS '低于5人',");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT >= 5 AND WORK_PEOPLE_COUNT < 10 THEN 1 ELSE 0 END) AS '5-10人',");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT >= 10 AND WORK_PEOPLE_COUNT < 15 THEN 1 ELSE 0 END) AS '10-15人',");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT >= 15 AND WORK_PEOPLE_COUNT < 20 THEN 1 ELSE 0 END) AS '15-20人',");
        sql.append("SUM(CASE WHEN WORK_PEOPLE_COUNT >= 20 THEN 1 ELSE 0 END) AS '20人及以上'");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectPersonCountList", type = Types.LIST)
    public JSONObject projectPersonCountList() {
        EasySQL sql = getEasySQL("SELECT PROJECT_ID,PROJECT_NAME,PROJECT_NO,PERSON_COUNT,WORK_PEOPLE_COUNT");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("and t1.project_id !='9999999999999' ");
        setOrderBy(sql, "ORDER BY PERSON_COUNT desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "workHourRank", type = Types.LIST)
    public JSONObject workHourRank() {
        EasySQL sql = getEasySQL("SELECT PROJECT_ID,PROJECT_NAME,PROJECT_NO,CAST(TRUNCATE(WORKHOUR_DAY,0) AS SIGNED) AS WORKHOUR_DAY,ROUND((WORKHOUR_DAY/WORK_PEOPLE_COUNT),1) as PERSON_AVERAGE");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        sql.append("and work_people_count is not null and work_people_count != '' ");
        this.setCondition(sql);
        sql.append("and t1.project_id !='9999999999999' ");
        setOrderBy(sql,"ORDER BY CAST(WORKHOUR_DAY AS DECIMAL(10,2)) DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

  


    @WebControl(name = "workHourTypeRank", type = Types.LIST)
        public JSONObject workHourTypeRank() {
           EasySQL sql = getEasySQL("SELECT t1.project_id, t1.project_name, t1.project_no,");
           sql.append("SUM(CASE WHEN (d.DEPT_PATH_NAME LIKE '%工程%' OR d.DEPT_PATH_NAME LIKE '%合同%' OR d.DEPT_PATH_NAME LIKE '%北京%') THEN t2.work_day ELSE 0 END) as eng_hours,");
           sql.append("SUM(CASE WHEN (d.DEPT_PATH_NAME LIKE '%创新%' OR d.DEPT_PATH_NAME LIKE '%开发%' OR d.DEPT_PATH_NAME LIKE '%研究%') THEN t2.work_day ELSE 0 END) as dev_hours,");
           sql.append("SUM(t2.work_day) as total_hours");
           sql.append("FROM yq_project t1");
           sql.append("INNER JOIN yq_weekly_project t2 ON t1.project_id = t2.project_id");
           sql.append("INNER JOIN yq_weekly t3 ON t2.weekly_id = t3.weekly_id");
           sql.append("INNER JOIN "+ Constants.DS_MAIN_NAME +".easi_dept d ON t3.dept_id = d.DEPT_ID");
           sql.append("WHERE 1=1");
           sql.append("and t1.project_id !='9999999999999' ");
           this.setCondition(sql);
           sql.append("GROUP BY t1.project_id, t1.project_name, t1.project_no");
           setOrderBy(sql, "ORDER BY total_hours DESC");
           return queryForPageList(sql.getSQL(), sql.getParams());
        
    }

    @WebControl(name = "businessTripRank", type = Types.LIST)
        public JSONObject businessTripRank() {
           EasySQL sql = getEasySQL("SELECT t2.project_name,t1.data9 AS PROJECT_ID,ROUND(SUM(data13),1) as trip_days");
           sql.append("from yq_flow_apply t1 INNER JOIN yq_project t2 on t1.data9 = t2.project_id");
           sql.append("WHERE data14 ='出差' and data9 != '0' and data9 != '9999999999999'");
           sql.append(30,"and apply_state = ?");
           String beginTime = StringUtils.isNotBlank(param.getString("beginDate"))?param.getString("beginDate")+" 00:00":"";
           String endTime = StringUtils.isNotBlank(param.getString("endDate"))?param.getString("endDate")+" 23:59":"";
           sql.append(beginTime,"and data11 >= ?");
           sql.append(endTime,"and data12 <= ?");
           sql.append("GROUP BY t1.data9");
           setOrderBy(sql, "ORDER BY trip_days desc");
           return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "delayTaskRankByProject", type = Types.LIST)
    public JSONObject delayTaskRankByProject() {
        EasySQL sql = getEasySQL("select count(DISTINCT t2.task_id) AS delay_task,sum(t2.delay_count) AS sum_delay, t1.project_name,t1.PROJECT_ID");
        sql.append("from yq_project t1 INNER JOIN yq_task t2 on t2.PROJECT_ID=t1.PROJECT_ID ");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("and t2.delay_count != 0");
        sql.append("and t1.project_id !='9999999999999' ");
        sql.append("GROUP BY t1.project_name");
        setOrderBy(sql,"ORDER BY delay_task DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "delayTimeRankByProject", type = Types.LIST)
    public JSONObject delayTimeRankByProject() {
        EasySQL sql = getEasySQL("SELECT t1.PROJECT_ID,t1.project_name,t1.project_no,");
        sql.append("MAX(dc.total_delay_days) AS max_delay_days,");
        sql.append("ROUND(SUM(dc.total_delay_days) / COUNT(DISTINCT dc.task_id), 1) AS average_delay_days");
        sql.append("from yq_project t1");
        sql.append("INNER JOIN ( SELECT t2.PROJECT_ID,t2.task_id,SUM(DATEDIFF(STR_TO_DATE(t3.new_plan_time, '%Y-%m-%d %H:%i'),STR_TO_DATE(t3.old_plan_time, '%Y-%m-%d %H:%i'))) AS total_delay_days");
        sql.append("FROM yq_task t2 INNER JOIN yq_task_delay t3 ON t2.task_id = t3.task_id");
        sql.append("where 1=1 and t2.delay_count != 0");
        sql.append("GROUP BY t2.PROJECT_ID, t2.task_id");
        sql.append(") dc ON t1.PROJECT_ID = dc.PROJECT_ID");
        sql.append("where 1=1");
        sql.append("and t1.project_id !='9999999999999' ");
        this.setCondition(sql);
        sql.append("GROUP BY t1.PROJECT_ID, t1.project_name, t1.project_no");
        setOrderBy(sql,"ORDER BY max_delay_days DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "weeklyProjectNumsRankByDept", type = Types.LIST)
    public JSONObject weeklyProjectNumsRankByDept() {
        EasySQL sql = getEasySQL("select t3.dept_name,count(DISTINCT t1.project_id) AS PROJECT_COUNTS,COUNT(DISTINCT t2.creator) AS PERSON_COUNT,ROUND(COUNT(DISTINCT t1.project_id) / COUNT(DISTINCT t2.creator),1) AS AVG_PROJECT_COUNTS");
        sql.append("from yq_project t1 INNER JOIN yq_weekly_project t2 ON t1.project_id = t2.project_id INNER JOIN yq_weekly t3 ON t2.weekly_id = t3.weekly_id");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("GROUP BY t3.dept_name");
        setOrderBy(sql,"ORDER BY PROJECT_COUNTS desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "taskProjectNumsRankByDept", type = Types.LIST)
    public JSONObject taskProjectNumsRankByDept() {
        EasySQL sql = getEasySQL("select count(DISTINCT t1.project_id) AS PROJECT_COUNTS,t2.assign_dept_name as DEPT_NAME,COUNT(DISTINCT t2.assign_user_id) AS PERSON_COUNT,ROUND(COUNT(DISTINCT t1.project_id) / COUNT(DISTINCT t2.assign_user_id),1) AS AVG_PROJECT_COUNTS");
        sql.append("from yq_project t1 INNER JOIN yq_task t2 on t2.PROJECT_ID=t1.PROJECT_ID ");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("GROUP BY t2.assign_dept_name ORDER BY PROJECT_COUNTS desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "delayTaskRankByDept", type = Types.LIST)
    public JSONObject delayTaskRankByDept() {
        EasySQL sql = getEasySQL("select count(DISTINCT t2.task_id) AS delay_task,sum(t2.delay_count) AS sum_delay,t2.assign_dept_name as DEPT_NAME");
        sql.append("from yq_task t2");
        sql.append("where 1=1");
        setTaskCondition(sql);
        sql.append("and t2.delay_count != 0");
        sql.append("GROUP BY t2.assign_dept_id");
        setOrderBy(sql,"ORDER BY delay_task DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "delayTimeRankByDept", type = Types.LIST)
    public JSONObject delayTimeRankByDept() {
        EasySQL sql = getEasySQL("SELECT t1.assign_dept_name as DEPT_NAME,");
        sql.append("MAX(dc.task_delay_days) AS max_delay_days,");
        sql.append("ROUND(SUM(dc.task_delay_days) / COUNT(DISTINCT dc.task_id), 1) AS average_delay_days");
        sql.append("from yq_task t1");
        sql.append("INNER JOIN ( SELECT t2.task_id,SUM(DATEDIFF(STR_TO_DATE(t3.new_plan_time, '%Y-%m-%d %H:%i'),STR_TO_DATE(t3.old_plan_time, '%Y-%m-%d %H:%i'))) AS task_delay_days");
        sql.append("FROM yq_task t2 INNER JOIN yq_task_delay t3 ON t2.task_id = t3.task_id");
        sql.append("where 1=1 and t2.delay_count != 0");
        setTaskCondition(sql);
        sql.append("GROUP BY t2.task_id");
        sql.append(") dc ON t1.task_id = dc.task_id");
        sql.append("where 1=1");
        sql.append("and dc.task_delay_days > 0");
        sql.append("GROUP BY t1.assign_dept_id");
        setOrderBy(sql,"ORDER BY average_delay_days DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "finishTaskRankByDept", type = Types.LIST)
    public JSONObject finishTaskRankByDept() {
        EasySQL sql = getEasySQL("select t2.assign_dept_name as DEPT_NAME,");
        sql.append("SUM(CASE WHEN t2.task_state >= 30 THEN 1 ELSE 0 END) AS task_nums,");
        sql.append("ROUND(SUM(CASE WHEN t2.task_state >= 30 THEN 1 ELSE 0 END) / COUNT(DISTINCT t2.assign_user_id), 1) AS avg_task_nums");
        sql.append("from yq_task t2");
        sql.append("where 1=1");
        setTaskCondition(sql);
        sql.append("and t2.task_state >= 30");
        sql.append("GROUP BY t2.ASSIGN_DEPT_ID");
        setOrderBy(sql,"ORDER BY task_nums DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "undoTaskRankByDept", type = Types.LIST)
    public JSONObject undoTaskRankByDept() {
        EasySQL sql = getEasySQL("select t2.assign_dept_name as DEPT_NAME,");
        sql.append("SUM(CASE WHEN t2.task_state IN (10, 20) THEN 1 ELSE 0 END) AS task_nums,");
        sql.append("ROUND(SUM(CASE WHEN t2.task_state IN (10, 20) THEN 1 ELSE 0 END) / COUNT(DISTINCT t2.assign_user_id), 1) AS avg_task_nums");
        sql.append("from yq_task t2");
        sql.append("where 1=1");
        sql.append("and t2.task_state IN (10, 20)");
        sql.append("GROUP BY t2.ASSIGN_DEPT_ID");
        setOrderBy(sql,"ORDER BY task_nums DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "taskRankByUser", type = Types.LIST)
    public JSONObject taskRankByUser() {
        EasySQL sql = getEasySQL("select t2.assign_user_id,t2.assign_user_name,t2.assign_dept_name,");
        sql.append(30,"SUM(CASE WHEN t2.task_state >= ? THEN 1 ELSE 0 END) AS task_nums,");
        sql.append("count(DISTINCT t2.project_id) AS PROJECT_COUNTS");
        sql.append("from yq_task t2");
        sql.append("where 1=1");
        setTaskCondition(sql);
        sql.append("GROUP BY t2.ASSIGN_USER_ID");
        setOrderBy(sql,"ORDER BY task_nums DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }
    @WebControl(name = "delayTaskRankByUser", type = Types.LIST)
    public JSONObject delayTaskRankByUser() {
        EasySQL sql = getEasySQL("select count(DISTINCT t2.task_id) AS delay_task,sum(t2.delay_count) AS sum_delay,t2.assign_user_name,t2.assign_dept_name");
        sql.append("from yq_task t2");
        sql.append("where 1=1");
        setTaskCondition(sql);
        sql.append("and t2.delay_count != 0");
        sql.append("GROUP BY t2.ASSIGN_USER_ID");
        setOrderBy(sql,"ORDER BY sum_delay DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "projectStatList", type = Types.LIST)
    public JSONObject projectStatList() {
        EasySQL sql = getEasySQL("select t1.*");
        sql.append("from yq_project t1 ");
        sql.append("where 1=1");
        this.setCondition(sql);
        setOrderBy(sql,"ORDER BY last_task_time DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "projectProgressList", type = Types.LIST)
    public JSONObject projectProgressList() {
        EasySQL sql = getEasySQL("SELECT t1.PROJECT_ID,t1.project_name,t1.project_no,t1.PROJECT_STAGE,t1.PROJECT_STATE,CY_DATE,ZY_DATE,CY_REAL_DATE,ZY_REAL_DATE,t1.BEGIN_DATE,LAST_TASK_TIME,");
        sql.append("sum(dc.delay_count) as sum_delay,");
        sql.append("count(DISTINCT dc.task_id) AS delay_task,");
        sql.append("MAX(dc.total_delay_days) AS max_delay_days,");
        sql.append("ROUND(SUM(dc.total_delay_days) / COUNT(DISTINCT dc.task_id), 1) AS average_delay_days");
        sql.append("from yq_project t1");
        sql.append("LEFT JOIN ( SELECT t2.PROJECT_ID,t2.delay_count,t2.task_id,SUM(DATEDIFF(STR_TO_DATE(t3.new_plan_time, '%Y-%m-%d %H:%i'),STR_TO_DATE(t3.old_plan_time, '%Y-%m-%d %H:%i'))) AS total_delay_days");
        sql.append("FROM yq_task t2 INNER JOIN yq_task_delay t3 ON t2.task_id = t3.task_id");
        sql.append("where 1=1 and t2.delay_count != 0");
        sql.append("GROUP BY t2.PROJECT_ID, t2.task_id");
        sql.append(") dc ON t1.PROJECT_ID = dc.PROJECT_ID");
        sql.append("where 1=1");
        this.setCondition(sql);
        sql.append("group by t1.PROJECT_ID,t1.project_name,t1.project_no,t1.PROJECT_STAGE,t1.PROJECT_STATE,CY_DATE,ZY_DATE,CY_REAL_DATE,ZY_REAL_DATE,t1.BEGIN_DATE,LAST_TASK_TIME");
        setOrderBy(sql,"ORDER BY last_task_time DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectList1", type = Types.LIST)
    public JSONObject projectList1() {
        String timeType2 = param.getString("timeType2");
        EasySQL sql = getEasySQL("select t1.project_id,project_name,project_no,project_stage,CY_REAL_DATE,ZY_REAL_DATE,"+timeType2);
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        sql.appendLike(DateUtils.getFullMonthStr(), "and " + timeType2 + " like ?");
        setOrderBy(sql,"ORDER BY "+timeType2+" ASC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "projectListByCondition", type = Types.LIST)
    public JSONObject projectListByCondition() {
        EasySQL sql = getEasySQL("select t1.project_id,project_name,project_no,project_stage,last_task_time,cy_date,zy_date");
        sql.append("from yq_project t1");
        sql.append("where 1=1");
        sql.append(param.getString("projectStage"), "and project_stage = ?");
        setOrderBy(sql,"ORDER BY last_task_time DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    private void setTaskCondition(EasySQL sql){
        String beginDate = StringUtils.isNotBlank(param.getString("beginDate"))?param.getString("beginDate")+" 00:00:00":"";
        String endDate = StringUtils.isNotBlank(param.getString("endDate"))?param.getString("endDate")+" 23:59:59":"";
        sql.append(beginDate, "and t2.create_time >= ?");
        sql.append(endDate, "and t2.create_time <= ?");
    }

    private void setCondition(EasySQL sql) {
    	sql.append(param.getString("projectType")," and t1.project_type = ?");
        sql.append("and t1.is_delete = 0");
        String timeType = param.getString("timeType");
        if (StringUtils.isBlank(timeType)|| StringUtils.isAllBlank(param.getString("beginDate"),param.getString("endDate"))) {
            return;
        }
        if ("create_time".equals(timeType)) {
            sql.append(param.getString("beginDate") + " 00:00:00", "and t1." + timeType + " >= ?");
            sql.append(param.getString("endDate") + " 23:59:59", "and t1." + timeType + " <= ?");
        } else {
            sql.append(param.getString("beginDate"), "and " + timeType + " >= ?");
            sql.append(param.getString("endDate"), "and " + timeType + " <= ?");
        }
    }


    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");

        if (StringUtils.notBlank(sortName)) {
            if ("WORKHOUR_DAY".equals(sortName)) {
                sortName = "CAST(WORKHOUR_DAY AS DECIMAL(10,2))";
            }
            sql.append("order by ").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }
}
