<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>合同评审</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
		.comment:hover{text-decoration: underline;cursor: pointer;color: blue;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo" data-mars="FlowDao.businessInfo" data-mars-prefix="business.">
				 		<div class="flow-title">${flow.flowTitle}</div>
				 		<div class="flow-btn">
				 			<button class="btn btn-outline btn-danger btn-sm mr-5" onclick="Flow.buyList();" type="button"><i class="fa fa-shopping-cart"></i> 采购清单 </button>
				 		</div>
				    	<table class="table table-vzebra baseInfo flow-table">
					  		<tr>
					  			<td style="width: 10%;">评审编号</td>
					  			<td style="width: 25%;" class="copy"><input type="text" data-rules="required" class="form-control" name="apply.applyNo" data-mars="ReviewDao.getReviewNo"/></td>
					  			<td style="width: 10%;">经办人</td>
					  			<td style="width: 23%;"><input type="text" readonly="readonly" class="form-control" name="apply.applyName"/></td>
					  			<td style="width: 10%;">提交时间</td>
					  			<td style="width: 22%;"><input type="text" readonly="readonly" class="form-control" name="apply.applyTime"/></td>
					  		</tr>
					  		<tr>
								<td>合同名称</td>
					  			<td colspan="5" class="copy">
					  				<input type="text" data-rules="required" class="form-control" name="business.CONTRACT_NAME"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>合同类型</td>
					  			<td>
					  				<select data-rules="required" class="form-control text-val" name="business.CONTRACT_TYPE">
					  					<option value="正式合同">正式合同</option>
					  					<option value="提前执行">提前执行</option>
					  				</select>
					  			</td>
					  			<td>签约单位</td>
					  			<td>
					  				<select data-rules="required" class="form-control" name="business.SIGN_ENT">
					  					<option value="yunqu">云趣</option>
					  					<option value="zhongrong">中融</option>
					  					<option value="pci">佳都</option>
					  				</select>
					  			</td>
					  			<td>产品线</td>
					  			<td>
					  				<input class="form-control" data-rules="required" name="business.PROD_LINE">
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>售前经理</td>
					  			<td class="copy">
					  				<input name="business.PRE_SALES_NAME" readonly="readonly" class="form-control">
					  			</td>
					  			<td>销售经理</td>
					  			<td class="copy">
					  				<input data-rules="required" readonly="readonly" name="business.SALES_BY_NAME"  class="form-control">
					  			</td>
					  			<td>上级主管</td>
					  			<td class="copy">
					  				<input data-rules="required" name="business.UP_SALES_NAME" class="form-control"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>合同金额</td>
					  			<td><input type="text" class="form-control" name="business.AMOUNT"/></td>
					  			<td>付款方式</td>
					  			<td><input type="text" class="form-control" name="business.PAY_TYPE"/></td>
					  			<td>维保时间</td>
					  			<td><input type="text" placeholder="维保合同必填" class="form-control" name="business.WB_TIME"/></td>
					  		</tr>
					  		<tr>
					  			<td>签约部门</td>
					  			<td>
					  				<input name="business.SIGN_DEPT_NAME" data-rules="required" class="form-control">
					  			</td>
					  			<td>所属客户</td>
					  			<td>
					  				<input type="hidden" class="form-control" name="business.CUST_ID"/>
					  				<input type="text" data-rules="required" class="form-control" name="business.CUST_NAME" id="custId" readonly="readonly"/>
					  			</td>
					  			<td>所属商机</td>
					  			<td>
					  				<input type="text" class="form-control" name="business.SJ_NAME"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="5">
					  				<textarea style="display: none;" data-text="false" id="buyList" name="business.BUY_LIST"></textarea>
					  				<textarea class="form-control" style="height: 100px;" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td style="vertical-align: top;">合同方案附件</td>
					  			<td colspan="5" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  	</table>
				 </div>
				 <div class="layui-col-md12 buy-list">
				  	<table class="layui-table approve-table">
				  		 <thead>
				  		 	<tr>
				  		 		<th>登记类型</th>
				  		 		<th>采购类型</th>
				  		 		<th>品名</th>
				  		 		<th>规格</th>
				  		 		<th>数量</th>
				  		 		<th>供应商</th>
				  		 		<th>单价</th>
				  		 		<th>数量</th>
				  		 		<th>总价</th>
				  		 		<th>是否含税</th>
				  		 	</tr>
				  		 </thead>
				  		 <tbody id="reviewPurchase"></tbody>
				  	</table>
				 </div>
				 <script id="review-purchase-template" type="text/x-jsrender">
					{{for data}}
						<tr>
							<td>{{:f10}}</td>
							<td>{{:f1}}</td>
							<td>{{:f2}}</td>
							<td>{{:f3}}</td>
							<td>{{:f4}}</td>
							<td>{{:f5}}</td>
							<td>{{:f6}}</td>
							<td>{{:f7}}</td>
							<td>{{:f8}}</td>
							<td>{{:f9}}</td>
						</tr>		
					{{/for}}
				</script>
				 <div class="layui-col-md12 approve-result">
				 	<table class="layui-table approve-table" style="margin-top: 10px;padding: 10px;">
					   <thead>
					      <tr>
						      <th class="hidden-print"></th>
						      <th>序号</th>
						      <th>节点名称</th>
						      <th>经办人</th>
						      <th>评审结果</th>
						      <th class="hidden-print hide">接收时间</th>
						      <th class="hidden-print hide">查看时间</th>
						      <th>审批时间</th>
						      <th>审批描述</th>
						      <th class="hidden-print">用时</th>
					      </tr>
					  </thead>
					  <tbody data-mars="FlowDao.approveResult" id="approveResult" data-template="approveList"></tbody>
					</table>
					<script id="approveList" type="text/x-jsrender">
					 {{for data}}
						<tr>
							<td style="width:50px;" class="hidden-print text-c"> 
								<label class="radio radio-inline radio-success">
			                        <input type="radio" value="{{:RESULT_ID}}" data-text="false" name="selectResultId"><span></span>
			                	</label>
							</td>
							<td style="width:50px;text-align:center;">{{:#index+1}}</td>
							<td data-nd="{{:DATA3}}" style="width:160px;">{{:NODE_NAME}}{{if DATA1=='1'}}-{{:DATA4}}{{/if}}</td>
							<td style="width:120px;" onmouseover="layer.tips('{{:CHECK_NAME}}',this)">{{cutText:CHECK_NAME 12}}  {{if TRUST_ID}} <a class="hidden-print pull-right" href="javascript:;" onclick="FlowCore.lookTrust('{{:TRUST_ID}}')">查看委托</a> {{/if}}</td>
							<td style="width:100px;">{{if NODE_ID!='0'}} {{if DATA2=='1'}}初审:{{else DATA2=='2'}}终审:{{/if}} {{call:CHECK_RESULT fn='reviewResultLable'}} {{/if}} {{if CC_COUNT>0}} <a class="hidden-print pull-right" href="javascript:;" onclick="FlowCore.lookCC('{{:RESULT_ID}}')">查看抄送</a> {{/if}}</td>
							<td class="hidden-print hide">{{:GET_TIME}}</td>
							<td class="hidden-print hide">{{:READ_TIME}}</td>
							<td style="width:150px;" onmouseover="layer.tips('接收时间：{{:GET_TIME}}',this)">{{:CHECK_TIME}}</td>
							<td class="comment" {{if CHECK_DESC}}onmouseover="FlowCore.tips(this)" onclick="commentLayer('{{:CHECK_USER_ID}}','{{:CHECK_NAME}}','{{:RESULT_ID}}')"{{/if}}><textarea class="hidden">{{:CHECK_DESC}}</textarea>{{cutText:CHECK_DESC 70}}</td>
							<td style="width:90px;" class="hidden-print">{{call:GET_TIME CHECK_TIME fn='dateDiff'}}</td>
						</tr>
					  {{/for}}
					{{if data.length==0}}
						<tr><td  colspan="10" class="text-c">暂无数据</td></tr>
					{{/if}}
					</script>
				</div>
			</div>
			
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">

		var Flow = {};
	
		$(function(){
			FlowCore.initPage({approvePageUrl:'/yq-work/pages/crm/contract/review/review-approve.jsp',success:function(result){
				var buyList = $('#buyList').val();
				if(buyList&&buyList!='--'){
					var data = eval('(' + buyList + ')'); 
					var tmp = $.templates('#review-purchase-template');
					var html = tmp.render({data:data});
					$('#reviewPurchase').html(html);
					$('.buy-list').show();
				}else{
					$('.buy-list').hide();
				}
				
				$('#approveResult [data-nd]').each(function(){
					var t = $(this);
					var nodeId = t.data('nd');
					if(nodeId){
						var nextEl = t.parent().next().find('[data-nd]');
						var nextNodeId = nextEl.data('nd');
						if(nodeId==nextNodeId){
							t.attr('rowspan','2');
							nextEl.remove();
						}
					}
				});
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
		}
		
		Flow.updateData = function() {
		}
		
		Flow.submitCheck = function() {
			var finalResult = $("[name='finalResult']:checked").val();
			if(finalResult==undefined){
				layer.msg('请选择结论性意见',{icon:7,offset:'50px'});
				return;
			}
			var checkResult = $("[name='checkResult']:checked").val();
			if(checkResult==undefined){
				layer.msg('请选择审批结果',{icon:7,offset:'50px'});
				return;
			}
			if(finalResult=='2'){
				var checkDesc = $("[name='checkDesc']").val();
				if(checkDesc==''){
					layer.msg('请填写描述',{icon:2});
					return;
				}
				var resultDesc = checkResult=='1'?'\n评审结果：可行':'\n评审结果：不可行';
				var data = $.extend({flowContent:checkDesc+resultDesc},FlowCore.params);
				ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=addComment",data,function(result) { 
					if(result.state == 1){
						ajax.remoteCall("${ctxPath}/servlet/contract/review?action=updateApproveDesc",data,function(rs) { 
							layer.msg(rs.msg,{icon:1,time:800},function(){
								 location.reload();
							})
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			}else{
				FlowCore.addCheckOrder({reqUrl:'${ctxPath}/servlet/contract/review?action=doApprove'});
			}
			
		}
		
		Flow.buyList = function(){
			popup.layerShow({url:'/yq-work/pages/crm/contract/review/purchase-list.jsp',full:fullShow(),area:['95%','80%'],shadeClose:true,scrollbar:false,offset:'20px',data:{},id:'commentLayer',title:'采购清单'});
		}
		
		function reviewResultLable(val){
			var json = {'0':'待评审','1':'可行','2':'<font color="red">不可行</font>'};
			 return json[val]||'待评审';
		}
		
		function commentLayer(toUserId,toUserName,resultId){
			var data = {toUserId:toUserId,toUserName:toUserName,resultId:resultId,businessId:FlowCore.businessId};
			popup.layerShow({url:'/yq-work/pages/flow/comm/flow-comment.jsp',full:fullShow(),area:['700px','100%'],shadeClose:true,scrollbar:false,offset:'r',data:data,id:'commentLayer',title:'意见列表'});
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>