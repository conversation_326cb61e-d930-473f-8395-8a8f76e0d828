<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>Kanban</title>
	<style>

	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editForm" class="form-horizontal" data-mars="KanbanDao.panelItemInfo"  data-mars-prefix="item.">
	     		<input type="hidden" name="itemId" value="${param.itemId}"/>
	     		<input type="hidden" name="item.KANBAN_ID" value="${param.kanbanId}"/>
	     		<input type="hidden" name="item.ITEM_ID" value="${param.itemId}"/>
	     		<input type="hidden" name="item.PANEL_ID" value="${param.panelId}"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td><textarea maxlength="255" data-rules="required" style="height: 130px;" name="item.ITEM_CONTENT" class="form-control"></textarea></td>
			            </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary" onclick="KanbanItem.ajaxSubmitForm()"> 保 存  </button>
					      <button type="button" title="关闭" class="btn btn-default ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	   
		var KanbanItem = {};
		KanbanItem.itemId = '${param.itemId}';
		$(function(){
			if(KanbanItem.itemId){
				$("#editForm").render();
			}
		});

			
		KanbanItem.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(KanbanItem.itemId){
					KanbanItem.updateData(); 
				}else{
					KanbanItem.insertData(); 
				}
			}
		}
		KanbanItem.insertData = function(flag) {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/kanban?action=addPanelItem",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:800},function(){
						layer.closeAll();
						window.location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		KanbanItem.updateData = function(flag) {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/kanban?action=updatePanelItem",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:800},function(){
						layer.closeAll();
						window.location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>