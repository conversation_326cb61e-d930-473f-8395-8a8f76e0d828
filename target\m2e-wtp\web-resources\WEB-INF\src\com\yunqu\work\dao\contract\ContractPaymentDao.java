package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name = "ContractPaymentDao")
public class ContractPaymentDao extends AppDaoContext {

    @WebControl(name = "getPaymentType", type = Types.DICT)
    public JSONObject getPaymentType() {
        EasySQL sql = new EasySQL("select DICT_ID, CONCAT(DICT_NAME, ' -税率: ', DICT_VALUE , '%') AS DICT_DESC from yq_contract_payment_dict where 1=1");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "getPaymentRate", type = Types.DICT)
    public JSONObject getPaymentRate() {
        String dictId = param.getString("dictId");
        EasySQL sql = new EasySQL("select DICT_ID,DICT_VALUE from yq_contract_payment_dict where 1=1");
        sql.append(dictId, "and DICT_ID = ?");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "selectPayment", type = Types.DICT)
    public JSONObject selectPayment() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("select PAYMENT_ID,CONCAT(DICT_NAME,'-税率:',DICT_VALUE,'%-占比:',RATIO,'%-金额: ', AMOUNT_WITH_TAX) AS DICT_DESC from yq_contract_payment t1,yq_contract_payment_dict t2 where t1.PAYMENT_TYPE = t2.DICT_ID");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and t1.CONTRACT_ID = ? ");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }
    @WebControl(name = "selectPayment2", type = Types.DICT)
    public JSONObject selectPayment2() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("select PAYMENT_ID,CONCAT(DICT_NAME,'-税率:',DICT_VALUE,'%-金额:', AMOUNT_WITH_TAX) AS DICT_DESC from yq_contract_payment t1,yq_contract_payment_dict t2 where t1.PAYMENT_TYPE = t2.DICT_ID");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and t1.CONTRACT_ID = ? ");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "paymentStageInfo", type = Types.DICT)
    public JSONObject paymentStageInfo() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("SELECT t1.PAYMENT_ID , CONCAT(t3.DICT_NAME,'-收入确认比例:',t2.RATIO,'%-该款项计划金额(含税):',t2.AMOUNT_WITH_TAX,'-税后:',t2.AMOUNT_NO_TAX ) AS INFO from yq_contract_payment t1 LEFT JOIN yq_contract_payment_stage t2 ON t1.PAYMENT_ID =  t2.PAYMENT_ID LEFT JOIN yq_contract_payment_dict t3 ON t1.PAYMENT_TYPE = t3.DICT_ID where 1=1");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and t1.CONTRACT_ID = ? ");
        return getDictByQuery(sql.getSQL(),sql.getParams());
    }


    @WebControl(name = "record", type = Types.RECORD)
    public JSONObject record() {
        String paymentId = param.getString("payment.PAYMENT_ID");
        if (StringUtils.notBlank(paymentId)) {
            return queryForRecord("select * from yq_contract_payment where PAYMENT_ID = ?", paymentId);
        } else {
            return getJsonResult(new JSONObject());
        }
    }

    @WebControl(name = "paymentList", type = Types.LIST)
    public JSONObject paymentList() {
        EasySQL sql = getEasySQL("select t1.* from yq_contract_payment t1");
        sql.append("where 1=1");
        sql.append(param.getString("contractId"), " and t1.CONTRACT_ID = ?");
        setOrderBy(sql, " order by t1.CREATE_TIME");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    /*
     * 编辑收入确认阶段时
     * 查询该合同所有款项，
     * 要显示基本信息+款项已选比例、该阶段已选比例
     */
    @WebControl(name = "paymentListForSelect", type = Types.LIST)
    public JSONObject paymentListForSelect() {
        if (StringUtils.isBlank(param.getString("contractId"))) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("error", "无合同Id,请选择合同");
            return getJsonResult(new JSONObject());
        }
        EasySQL sql = getEasySQL("SELECT t1.PAYMENT_ID,t3.DICT_NAME,t3.DICT_VALUE,t1.AMOUNT_NO_TAX,t1.AMOUNT_WITH_TAX,t1.RATIO ,COALESCE(SUM(t2.RATIO), 0) AS TOTAL_RATIO,COALESCE(SUM(t2.AMOUNT_WITH_TAX), 0) AS TOTAL_AMOUNT,");
        if (StringUtils.isBlank(param.getString("incomeStageId"))) {
            sql.append("COALESCE(SUM(CASE WHEN t2.INCOME_STAGE_ID = '' THEN t2.RATIO ELSE 0 END), 0) AS STAGE_RATIO ,");
            sql.append("COALESCE(SUM(CASE WHEN t2.INCOME_STAGE_ID = '' THEN t2.RATIO ELSE 0 END), 0) AS CHOSEN_RATIO ,");
            sql.append("COALESCE(SUM(CASE WHEN t2.INCOME_STAGE_ID = '' THEN t2.AMOUNT_WITH_TAX ELSE 0 END), 0) AS STAGE_AMOUNT");
        } else {
            sql.append(param.getString("incomeStageId"), "COALESCE(SUM(CASE WHEN t2.INCOME_STAGE_ID = ? THEN t2.RATIO ELSE 0 END), 0) AS STAGE_RATIO ,");
            sql.append(param.getString("incomeStageId"), "COALESCE(SUM(CASE WHEN t2.INCOME_STAGE_ID = ? THEN t2.RATIO ELSE 0 END), 0) AS CHOSEN_RATIO ,");
            sql.append(param.getString("incomeStageId"), "COALESCE(SUM(CASE WHEN t2.INCOME_STAGE_ID = ? THEN t2.AMOUNT_WITH_TAX ELSE 0 END), 0) AS STAGE_AMOUNT ,");
            sql.append(param.getString("incomeStageId"), "COALESCE(SUM(CASE WHEN t2.INCOME_STAGE_ID = ? THEN t2.AMOUNT_WITH_TAX ELSE 0 END), 0) AS OLD_STAGE_AMOUNT");
        }
        sql.append("FROM yq_contract_payment t1 LEFT JOIN yq_contract_payment_stage t2 ON t1.PAYMENT_ID = t2.PAYMENT_ID");
        sql.append("LEFT JOIN yq_contract_payment_dict t3 ON t1.PAYMENT_TYPE = t3.DICT_ID");
        sql.append(param.getString("contractId"), "WHERE t1.CONTRACT_ID = ? ");
        sql.append(param.getString("paymentType1"), "and t1.PAYMENT_TYPE = ? ");
        sql.append("GROUP BY t1.PAYMENT_ID,t1.PAYMENT_TYPE,t1.AMOUNT_NO_TAX,t1.AMOUNT_WITH_TAX,t1.RATIO");
        sql.append("ORDER BY STAGE_RATIO DESC , TOTAL_RATIO ASC");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "paymentInfoForIncomeStage", type = Types.DICT)
    public JSONObject paymentInfoForIncomeStage() {
        String incomeStageId = param.getString("incomeStageId");
        String contractId = param.getString("contractId");
        if (StringUtils.isBlank(incomeStageId)) {
            incomeStageId = this.getMethodParam(0).toString();
        }

        EasySQL sql = getEasySQL("SELECT t1.PAYMENT_ID, CONCAT (t3.DICT_NAME,'-税率:',t1.TAX_RATE,'%--款项金额(税前):', t1.AMOUNT_WITH_TAX,'-收入确认比例:',t2.RATIO,'%-计划收款金额(含税):' ,t2.AMOUNT_WITH_TAX,'-税后:',t2.AMOUNT_NO_TAX)");
        sql.append("FROM yq_contract_payment t1 LEFT JOIN yq_contract_payment_stage t2 ON t1.PAYMENT_ID = t2.PAYMENT_ID");
        sql.append("LEFT JOIN yq_contract_payment_dict t3 ON t1.PAYMENT_TYPE = t3.DICT_ID ");
        sql.append(contractId, "WHERE t1.CONTRACT_ID = ? ");
        sql.append(incomeStageId, "AND t2.INCOME_STAGE_ID = ? ");
//        sql.append("GROUP BY t1.PAYMENT_ID, t1.PAYMENT_TYPE, t1.AMOUNT_WITH_TAX, t2.RATIO, t3.DICT_NAME");
        sql.append("ORDER BY t2.AMOUNT_WITH_TAX DESC");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "paymentIncomeStageList", type = Types.LIST)
    public JSONObject paymentIncomeStageList() {
        String incomeStageId = param.getString("incomeStageId");
        EasySQL sql = getEasySQL("SELECT * FROM yq_contract_payment_stage");
        sql.append(incomeStageId, "WHERE INCOME_STAGE_ID = ? ");
        sql.append("ORDER BY AMOUNT_WITH_TAX DESC");
        return queryForList(sql.getSQL(), sql.getParams());
    }


    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by t1.").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }

}
