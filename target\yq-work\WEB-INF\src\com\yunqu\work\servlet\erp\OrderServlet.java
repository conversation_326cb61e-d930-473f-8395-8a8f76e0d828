package com.yunqu.work.servlet.erp;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/order/*")
public class OrderServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("YQ_ERP_ORDER","ORDER_ID");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	
	public EasyResult actionForSubmitApply() {
		return EasyResult.ok();
	}
	
	public EasyResult actionForDoPay() {
		JSONObject params =getJSONObject();
		String paymentId = params.getString("paymentId");
		String orderIds = params.getString("orderIds");
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			query.executeUpdate("update yq_erp_order_payment set  pay_state = ? where payment_id = ?",10,paymentId);
			
			EasySQL sql = new EasySQL("update yq_erp_order t1 set t1.last_follow_label = '已付款'  where 1=1");
			sql.appendIn(orderIds.split(",")," and t1.order_id");
			query.executeUpdate(sql.getSQL(), sql.getParams());
			
			EasySQL statSql = new EasySQL();
			statSql.append("update yq_erp_order t1 ");
			statSql.append(paymentId,"set t1.has_pay_amount = (select sum(amount) from yq_erp_order_payment t2 where t2.order_id =  t1.order_id and t2.payment_id = ?) ");
			sql.append(orderIds,"where FIND_IN_SET(?,t1.order_id)");
			query.executeUpdate(statSql.getSQL(), statSql.getParams());
			
			query.commit();
		} catch (SQLException e) {
			this.error(null, e);
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(null, ex);
			}
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAdd(){
		String id= RandomKit.uniqueStr();
		EasyRecord model = getModel("order");
		model.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		model.set("CREATE_USER_NAME", getUserName());
		model.setPrimaryValues(id);
		try {
			this.getQuery().save(model);
			this.getQuery().executeUpdate("update yq_erp_supplier t1 set t1.order_count = (select count(1) from yq_erp_order t2 where t1.supplier_id = t2.supplier_id) where t1.supplier_id = ?", id);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		JSONObject object = new JSONObject();
		object.put("id", id);
		object.put("userId", getUserId());
		
		this.addOrderLog(id,"", "10","创建订单");
		return EasyResult.ok(object);
	}
	public EasyResult actionForAddFollow(){
		EasyRecord model = new EasyRecord("YQ_ERP_ORDER_FOLLOW","FOLLOW_ID");
		model.setColumns(getJSONObject("follow"));
		model.set("ADD_TIME", EasyDate.getCurrentDateString());
		model.set("ADD_BY", getUserId());
		model.set("USER_NAME", getUserName());
		String orderId = model.getString("ORDER_ID");
		try {
			String followId = model.getString("FOLLOW_ID");
			if(StringUtils.isBlank(followId)) {
				model.setPrimaryValues(RandomKit.uniqueStr());
			}
			this.getQuery().save(model);
			
			EasyRecord orderModel = new EasyRecord("yq_erp_order","order_id");
			Integer followState = model.getIntValue("FOLLOW_STATE");
			orderModel.set("order_id", orderId);
			orderModel.set("last_follow_time", EasyDate.getCurrentDateString());
			orderModel.set("last_follow_label", model.getString("FOLLOW_STATE_LABEL"));
			orderModel.set("last_follow_content", model.getString("FOLLOW_CONTENT"));
			orderModel.set("order_state", followState);
			
			if(followState==90) {
				
			}else if(followState==99) {
				orderModel.set("total_price", 0);
				orderModel.set("rk_amount", 0);
				this.getQuery().executeUpdate("update yq_erp_goods set number = 0,out_number = 0 ,in_number = 0,return_number = 0,return_supplier_num = 0,return_store_num = 0  where order_id = ?", orderId);
				this.getQuery().executeUpdate("update yq_erp_goods_stock set num = 0  where order_id = ?", orderId);
				this.getQuery().executeUpdate("delete from yq_erp_order_invoice where order_id = ?", orderId);
			}
			this.getQuery().update(orderModel);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateFollow(){
		EasyRecord model = new EasyRecord("YQ_ERP_ORDER_FOLLOW","FOLLOW_ID");
		model.setColumns(getJSONObject("follow"));
		try {
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("order");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}

	public EasyResult actionForDel(){
		try {
			this.getQuery().executeUpdate("delete from yq_erp_order where order_id = ?", getJsonPara("orderId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddInvoice(){
		EasyRecord model = new EasyRecord("yq_erp_order_invoice","invoice_id");
		try {
			model.setColumns(getJSONObject("invoice"));
			model.set("invoice_id", RandomKit.uniqueStr());
			model.set("create_user_id", getUserId());
			model.set("create_time", EasyDate.getCurrentDateString());
			model.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().save(model);
			this.orderMakeAmountStat(model.getString("ORDER_ID"));
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddPayPlan(){
		EasyRecord model = new EasyRecord("yq_erp_order_pay_plan","plan_id");
		try {
			model.setColumns(getJSONObject("plan"));
			model.set("create_user_id", getUserId());
			model.set("create_time", EasyDate.getCurrentDateString());
			model.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().save(model);
			
			int id = this.getQuery().queryForInt("select max(plan_id) from yq_erp_order_pay_plan");
			String typeId = model.getString("TYPE_ID");
			this.getQuery().executeUpdate("update yq_erp_order_pay_type set plan_id =  ? where type_id = ?",id,typeId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddPayType(){
		EasyRecord model = new EasyRecord("yq_erp_order_pay_type","type_id");
		try {
			model.setColumns(getJSONObject("plan"));
			model.set("type_id", RandomKit.uniqueStr());
			model.set("create_user_id", getUserId());
			model.set("create_time", EasyDate.getCurrentDateString());
			model.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdatePayPlan(){
		EasyRecord model = new EasyRecord("yq_erp_order_pay_plan","plan_id");
		try {
			model.setColumns(getJSONObject("plan"));
			model.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
			
			String id = model.getString("PLAN_ID");
			String typeId = model.getString("TYPE_ID");
			this.getQuery().executeUpdate("update yq_erp_order_pay_type set plan_id =  ? where type_id = ?",id,typeId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdatePayType(){
		EasyRecord model = new EasyRecord("yq_erp_order_pay_type","type_id");
		try {
			model.setColumns(getJSONObject("plan"));
			model.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateInvoice(){
		EasyRecord model = new EasyRecord("yq_erp_order_invoice","invoice_id");
		try {
			model.setColumns(getJSONObject("invoice"));
			model.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
			this.orderMakeAmountStat(model.getString("ORDER_ID"));
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelInvoice(){
		try {
		    JSONObject params = getJSONObject();
			this.getQuery().executeUpdate("delete from yq_erp_order_invoice where invoice_id = ?", params.getString("invoiceId"));
			this.orderMakeAmountStat(params.getString("orderId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelPayPlan(){
		try {
			JSONObject params = getJSONObject();
			this.getQuery().executeUpdate("delete from yq_erp_order_pay_plan where plan_id = ?", params.getString("planId"));
			
			this.getQuery().executeUpdate("update yq_erp_order_pay_type set plan_id = '' where plan_id = ?",params.getString("planId"));
			
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelPayType(){
		try {
			JSONObject params = getJSONObject();
			this.getQuery().executeUpdate("delete from yq_erp_order_pay_type where type_id = ?", params.getString("typeId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	private void orderMakeAmountStat(String orderId) {
		try {
			this.getQuery().executeUpdate("update yq_erp_order t1 set t1.make_amount = (select sum(t2.amount_tax) from yq_erp_order_invoice t2 where t1.order_id = t2.order_id) where t1.order_id = ?", orderId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		
	}
	
	public EasyResult actionForUpdateGoodsFileCount() {
		try {
			String orderId = getJsonPara("orderId");
			this.getQuery().executeUpdate("update yq_erp_goods t1 set t1.file_count = (select count(1) from yq_files t2 where t2.fk_id = t1.goods_id and t2.p_fk_id = ?)", orderId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	private void addOrderLog(String orderId,String followContent,String followState,String followStateLabel) {
		EasyRecord model = new EasyRecord("YQ_ERP_ORDER_FOLLOW","FOLLOW_ID");
		model.set("FOLLOW_DATE", EasyCalendar.newInstance().getDateInt());
		model.set("ADD_TIME", EasyDate.getCurrentDateString());
		model.set("ADD_BY", getUserId());
		model.set("USER_NAME", getUserName());
		model.set("FOLLOW_STATE", followState);
		model.set("FOLLOW_STATE_LABEL", followStateLabel);
		model.set("FOLLOW_CONTENT", followContent);
		model.set("ORDER_ID", orderId);
		model.setPrimaryValues(RandomKit.uniqueStr());
		try {
			this.getQuery().save(model);
			this.getQuery().executeUpdate("update yq_erp_order set last_follow_content = ?,last_follow_time = ?,last_follow_label = ? where order_id = ?",followContent,EasyDate.getCurrentDateString(),followStateLabel,orderId);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}

}
