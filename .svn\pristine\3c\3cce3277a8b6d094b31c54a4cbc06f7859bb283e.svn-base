package com.yunqu.work.utils;

import java.io.IOException;

import org.easitline.common.core.web.EasyResult;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import com.alibaba.fastjson.JSONObject;

public class EntInfoUtils {
	
	public static EasyResult queryEntList(String companyName){
		SSLHelper.trustEveryone();
		Connection connect = Jsoup.connect("https://www.testin.cn/account/query/matchData.htm?keyword="+companyName+"&mobile=");
		connect.header("Accept","application/json");
		connect.header("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.116 Safari/537.36");
		connect.header("Host","www.testin.cn");
		connect.header("referer","https://www.testin.cn");
		Document document;
		try {
			document = connect.timeout(3000).ignoreContentType(true).get();
			String result  = document.text();
			return EasyResult.ok(JSONObject.parseObject(result));
		} catch (IOException e) {
			return EasyResult.fail();
		}
		
	}
	
	public static EasyResult queryTax(String taxNo){
		return EasyResult.ok();
	}
	
	

}
