package com.yunqu.work.dao.flow;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "FlowDao")
public class FlowDao extends AppDaoContext{

	@WebControl(name = "categoryList",type = Types.TEMPLATE)
	public JSONObject categoryList() {
		EasySQL sql = getEasySQL("select * from yq_flow_category where 1=1");
		sql.append(param.getString("pFlowId"),"and p_flow_id = ?");
		sql.append("order by flow_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "applyList",type = Types.PAGE)
	public JSONObject applyList() {
		EasySQL sql = getEasySQL("select * from yq_flow_apply where 1=1");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "categoryDict",type = Types.DICT)
	public JSONObject categoryDict() {
		return getDictByQuery("select flow_id,flow_name from yq_flow_category where  p_flow_id = 0");
	}
	
	@WebControl(name = "categoryInfo",type = Types.RECORD)
	public JSONObject categoryInfo() {
		return queryForRecord("select * from yq_flow_category where flow_id = ?", param.getString("flowId"));
	}
}
