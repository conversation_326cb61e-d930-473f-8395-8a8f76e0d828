package com.yunqu.work.servlet.crm;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.ContractStatService;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.annotation.WebServlet;
import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

@WebServlet("/servlet/contractStat/*")
public class ContractStatServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public EasyResult actionForUpdateReceiptStat() {
        String result = ContractStatService.getService().updateContractReceiptStat();
        if ("ok".equals(result)) {
            return EasyResult.ok();
        } else {
            return EasyResult.fail();
        }
    }

    public EasyResult actionForUpdateIncomeStat() {
        String result = ContractStatService.getService().updateContractIncomeStat();
        if ("ok".equals(result)) {
            return EasyResult.ok();
        } else {
            return EasyResult.fail();
        }
    }

    public void actionForExportReceiptStat() {
        String _data = getPara("data");
        JSONObject param = JSONObject.parseObject(_data);

        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        if (period == 0 || startYear == 0) {
            renderHtml("查找时长或开始年份为空，默认输出一年统计报表。");
            period = 1;
            startYear = EasyCalendar.newInstance().getYear();
        }


        File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
        /**创建头部*/
        List<String> headers = new ArrayList<String>();
        headers.add("合同名");
        headers.add("合同号");
        headers.add("客户名");
        headers.add("合同金额");
        /**遍历月份*/
        EasySQL sql = new EasySQL("select t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                headers.add(monthId);
                sql.append(monthId, ",SUM(CASE WHEN yq_contract_receipt_stat.MONTH_ID = ? THEN yq_contract_receipt_stat.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_" + monthId);
            }
        }
        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN yq_contract_receipt_stat ON t1.CONTRACT_ID = yq_contract_receipt_stat.CONTRACT_ID");

        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");

        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        sql.append("order by t1.create_time desc");

        List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
        int x = 0;
        for (String header : headers) {
            ExcelHeaderStyle style = new ExcelHeaderStyle();
            style.setData(header);
            if (x == 0) {
                style.setWidth(12000);
            } else if (x <= 3) {
                style.setWidth(4500);
            } else {
                style.setWidth(2500);
            }
            style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
            styles.add(style);
            x++;
        }
        List<List<String>> excelData = new ArrayList<List<String>>();
        EasyQuery query = this.getQuery();
        query.setMaxRow(50000);

        List<JSONObject> data = null;
        try {
            data = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        } catch (SQLException e) {
            this.error(null, e);
            renderHtml(e.getMessage());
            return;
        }

        if (data != null && data.size() > 0) {
            for (int i = 0; i < data.size(); i++) {
                JSONObject map = data.get(i);
                List<String> list = new ArrayList<String>();
                list.add(map.getString("CONTRACT_SIMPILE_NAME"));
                list.add(map.getString("CONTRACT_NO"));
                list.add(map.getString("CUSTOMER_NAME"));
                list.add(map.getString("AMOUNT"));
                for (int k = 0; k < period; k++) {
                    int curYear = startYear + k;
                    for (int j = 1; j <= 12; j++) {
                        String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                        list.add(map.getString("TOTAL_" + monthId));
                    }
                }
                excelData.add(list);
            }
        }
        try {
            ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
        }

        String fileName = startYear + "收款统计报表.xlsx";
        int endYear = startYear + period - 1;
        if (period != 1) {
            fileName = startYear + "-" + endYear + "收款统计报表.xlsx";
        }
        renderFile(file, fileName, true);

    }


    public void actionForExportIncomeStat() {
        String _data = getPara("data");
        JSONObject param = JSONObject.parseObject(_data);

        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        if (period == 0 || startYear == 0) {
            renderHtml("查找时长或开始年份为空，默认输出今年统计报表。");
            period = 1;
            startYear = EasyCalendar.newInstance().getYear();
        }


        File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
        /**创建头部*/
        List<String> headers = new ArrayList<String>();
        headers.add("合同名");
        headers.add("合同号");
        headers.add("客户名");
        headers.add("合同金额");

        /**查询*/
        EasySQL sql = new EasySQL("select t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? AND t2.INCOME_TYPE = 'S' THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "STAGE_" + monthId);
                headers.add("收入阶段_" + monthId);
            }
        }

        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? AND t2.INCOME_TYPE = 'A' THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_A_" + monthId);
                headers.add("收入确认A_" + monthId);
            }
        }
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? AND t2.INCOME_TYPE = 'B' THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_B_" + monthId);
                headers.add("收入确认B_" + monthId);
            }
        }

        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN yq_contract_income_stat t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");

        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");

        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        sql.append("order by t1.create_time desc");


        List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
        int x = 0;
        for (String header : headers) {
            ExcelHeaderStyle style = new ExcelHeaderStyle();
            style.setData(header);
            if (x == 0) {
                style.setWidth(12000);
            } else {
                style.setWidth(4500);
            }
            style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
            styles.add(style);
            x++;
        }
        List<List<String>> excelData = new ArrayList<List<String>>();
        EasyQuery query = this.getQuery();
        query.setMaxRow(50000);

        List<JSONObject> data = null;
        try {
            data = query.queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
        } catch (SQLException e) {
            this.error(null, e);
            renderHtml(e.getMessage());
            return;
        }

        if (data != null && data.size() > 0) {
            for (int i = 0; i < data.size(); i++) {
                JSONObject map = data.get(i);
                List<String> list = new ArrayList<String>();
                list.add(map.getString("CONTRACT_SIMPILE_NAME"));
                list.add(map.getString("CONTRACT_NO"));
                list.add(map.getString("CUSTOMER_NAME"));
                list.add(map.getString("AMOUNT"));
                for (int ab = 0; ab < 3; ab++) {
                    for (int k = 0; k < period; k++) {
                        int curYear = startYear + k;
                        for (int j = 1; j <= 12; j++) {
                            String monthId = "";
                            if (j < 10) {
                                monthId = curYear + "0" + j;
                            } else {
                                monthId = curYear + "" + j;
                            }
                            if (ab == 0) {
                                list.add(map.getString("STAGE_" + monthId));
                            } else if (ab == 1) {
                                list.add(map.getString("TOTAL_A_" + monthId));
                            } else if (ab == 2) {
                                list.add(map.getString("TOTAL_B_" + monthId));
                            }
                        }
                    }
                }
                excelData.add(list);
            }
        }
        try {
            ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
        }

        String fileName = startYear + "收入确认统计报表.xlsx";
        int endYear = startYear + period - 1;
        if (period != 1) {
            fileName = startYear + "-" + endYear + "收入确认统计报表.xlsx";
        }
        renderFile(file, fileName, true);

    }

}
