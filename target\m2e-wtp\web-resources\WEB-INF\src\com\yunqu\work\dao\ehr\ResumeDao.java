package com.yunqu.work.dao.ehr;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "ResumeDao")
public class ResumeDao extends AppDaoContext {
	
	@WebControl(name = "followInfo",type = Types.RECORD)
	public JSONObject followInfo() {
		return queryForRecord("select * from yq_resume_follow where follow_id = ?", new Object[]{param.getString("followId")});
	}
	
	@WebControl(name = "zpneedInfo",type = Types.RECORD)
	public JSONObject zpneedInfo() {
		return queryForRecord("select * from yq_zp_need where zp_id = ?", new Object[]{param.getString("zpId")});
	}
	
	@WebControl(name = "zpneedList",type = Types.LIST)
	public JSONObject zpneedList() {
		EasySQL sql = getEasySQL("select * from yq_zp_need where 1=1");
		sql.append(param.getString("zpState"),"and zp_state = ?");
		sql.append("order by update_time desc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "followList",type = Types.LIST)
	public JSONObject followList() {
		EasySQL sql = getEasySQL("select t1.*,t2.people_name,t2.email,t2.tel from yq_resume_follow t1,yq_resume t2 where 1=1 and t1.resume_id = t2.resume_id");
		sql.append(param.getString("resumeId"),"and t1.resume_id = ?");
		sql.appendLike(param.getString("fileName"),"and t1.file_name like ?");
		sql.append(param.getString("followType"),"and t1.follow_type = ?");
		String followType = param.getString("followType");
		sql.append(param.getString("followLabel"),"and t1.follow_label = ?");
		sql.append(param.getString("followByName"),"and t1.follow_by_name = ?");
		if(!hasRole("HR_MGR")) {
			sql.append(getUserId(),"and t1.follow_by = ?");
		}
		if("1".equals(followType)) {
			sql.append("order by t1.follow_date desc,t1.follow_time desc");
		}else {
			sql.append("order by t1.create_time desc");
		}
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "list",type = Types.LIST)
	public JSONObject list() {
		EasySQL sql = getEasySQL("select * from yq_resume where 1=1");
		sql.appendLike(param.getString("fileName"),"and file_name like ?");
		sql.appendLike(param.getString("getByName"),"and get_by_name like ?");
		sql.appendLike(param.getString("peopleName"),"and people_name like ?");
		sql.append(param.getString("postType"),"and post_type = ?");
		sql.append(param.getString("followLabel"),"and last_follow_label = ?");
		String resumeType = param.getString("resumeType");
		if("mgr".equals(resumeType)) {
			
		}else if("get".equals(resumeType)) {
			sql.append("and get_by_id = ''");
			String deptName = getStaffInfo().getDeptName();
			if(StringUtils.notBlank(deptName)){
				sql.appendLike(deptName.substring(0,2),"and ((dept_label like ?) or");
				sql.appendLike(getUserPrincipal().getUserName(),"(get_by_name like ?))");
			}
		}else if("my".equals(resumeType)) {
			sql.append(getDeptId(),"and ((get_dept_id = ?) or");
			sql.append(getUserId(),"(get_by_id = ?))");
		}
		
		sql.append(param.getString("resumeState"),"and resume_state = ?","0");
		if("get".equals(resumeType)) {
			sql.append("order by get_time desc");
		}else {
			sql.append("order by upload_time desc");
			
		}
		return queryForPageList(sql.getSQL(),sql.getParams());
	}
}
