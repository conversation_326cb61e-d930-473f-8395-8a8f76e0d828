package com.yunqu.work.utils;

import org.easitline.common.utils.string.StringUtils;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;

import java.util.Properties;

import static org.quartz.CronScheduleBuilder.cronSchedule;
import static org.quartz.JobBuilder.newJob;
import static org.quartz.TriggerBuilder.newTrigger;

/**
 * 定是作业任务类
 * <AUTHOR>
 */
public class SchedulerTaskUtils {

	private static Scheduler scheduler = null;

	private static final String SCHEDULER_GROUP = "SCHEDULER_GROUP";

	/**
	 * 创建调度任务
	 * @param prop
	 * @param keyName
	 * @throws SchedulerException
	 * @throws ClassNotFoundException
	 */
	public static void addTask(Properties prop, String keyName) throws SchedulerException,ClassNotFoundException{
		if(prop==null) {
			throw new IllegalArgumentException("The properties object can not be blank.");
		}
		String keys = prop.getProperty(keyName);
		if(StringUtils.isBlank(keys)) {
			throw new IllegalArgumentException("The value of configName: " + keyName + " can not be blank.");
		}
		String[] key = keys.split(",");
		for(String k : key) {
			if(StringUtils.isBlank(k)) {
				throw new IllegalArgumentException("taskName can not be blank.");
			}
			k = k.trim();
			String cronKey = k+"_cron";
			if(StringUtils.isBlank(cronKey)) {
				throw new IllegalArgumentException(cronKey + " not found.");
			}
			cronKey = cronKey.trim();

			String taskClass = prop.getProperty(k);
			String cronStr = prop.getProperty(cronKey);
			if(StringUtils.isBlank(taskClass)) {
				throw new IllegalArgumentException(taskClass + " not found.");
			}
			if(StringUtils.isBlank(cronStr)) {
				throw new IllegalArgumentException("cron expression not found.");
			}
			try {
				addTask(Class.forName(taskClass), cronStr, taskClass);
			} catch (ClassNotFoundException e) {
				throw new ClassNotFoundException(taskClass +" not found.",e);
			} catch (SchedulerException e) {
				throw new SchedulerException("Create Scheduler Faile!",e);
			}
		}
	}


	/**
	 * 创建调度任务
	 * @param clazz
	 * @param cron
	 * @throws ClassNotFoundException
	 * @throws SchedulerException
	 */
	public static void addTask(final String clazz, final String cron) throws ClassNotFoundException, SchedulerException{
		addTask(clazz, cron, clazz);
	}

	/**
	 * 创建调度任务
	 * @param clazz
	 * @param cron
	 * @param triggerName
	 * @throws SchedulerException
	 * @throws ClassNotFoundException
	 */
	public static void addTask(final String clazz, final String cron, final String triggerName) throws SchedulerException,ClassNotFoundException{
		try {
			addTask(Class.forName(clazz),cron,triggerName);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
			throw new ClassNotFoundException(clazz +" not found.",e);
		}

	}

	/**
	 * 创建调度任务
	 * @param clazz
	 * @param cron
	 * @param triggerName
	 * @throws SchedulerException
	 */
	@SuppressWarnings("rawtypes")
	public static void addTask(final Class clazz, final String cron, final String triggerName) throws SchedulerException{
		if(clazz==null) {
			throw new NullPointerException("作业任务类名称不能为空！");
		}
		if(StringUtils.isBlank(cron) || !CronExpression.isValidExpression(cron)) {
			throw new RuntimeException("cron表达式不符合要求！");
		}
		if(StringUtils.isBlank(triggerName)) {
			throw  new NullPointerException("触发器名称triggerName不能为空！");
		}
		if(scheduler==null) {
			try {
				scheduler = StdSchedulerFactory.getDefaultScheduler();
			} catch (Exception e) {
				e.printStackTrace();
				throw new SchedulerException("创建调度器scheduler异常！",e);
			}
		}
		Trigger trigger = newTrigger().withIdentity(triggerName,SCHEDULER_GROUP).startNow().withSchedule(cronSchedule(cron).withMisfireHandlingInstructionFireAndProceed()).build();
		scheduler.scheduleJob(getJob(clazz,triggerName,SCHEDULER_GROUP),trigger);
	}

	/**
	 * 实例调度任务
	 * @param clazz
	 * @param triggerName
	 * @param group
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private static JobDetail getJob(Class clazz, String triggerName, String group) {
		JobDetail job = newJob(clazz).withIdentity(triggerName,group).usingJobData("name","quartz").build();
		return job;
	}

	/**
	 * 开始作业
	 * @throws SchedulerException
	 */
	public static void start() throws SchedulerException{
		try {
			scheduler.start();
		} catch (SchedulerException e) {
			e.printStackTrace();
			throw new SchedulerException("启动调度器scheduler异常！",e);
		}
	}

	/**
	 * 注销调度器
	 * @throws SchedulerException
	 */
	public static void shutDown() throws SchedulerException {
		if(scheduler!=null) {
			try {
				scheduler.shutdown(true);
			} catch (SchedulerException e) {
				e.printStackTrace();
				throw new SchedulerException("关闭调度器scheduler异常！",e);
			}
		}
	}
}
