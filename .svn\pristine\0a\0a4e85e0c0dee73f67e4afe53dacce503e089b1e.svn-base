<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<div class="layui-card mt-10" style="margin-bottom: 0px;">
  <div class="layui-card-header">审批流信息</div>
  <div class="layui-card-body">
	    <table class="table table-edit table-vzebra">
	    	<tr>
	    		<td style="width: 80px;">评审结果</td>
	    		<td>
	    			<label class="radio radio-inline radio-success">
                       	<input type="radio" value="1" name="checkResult"> <span>通过</span>
                       </label>
                       <label class="radio radio-inline radio-success">
                       	<input type="radio" value="2" name="checkResult"> <span>拒绝</span>
                       </label>
	    		</td>
	    	</tr>
	    	<tr>
	    		<td>评审说明</td>
	    		<td>
	    			<textarea  style="height: 60px" name="checkDesc" class="form-control"></textarea>
	    		</td>
	    	</tr>
	    	<tr>
	    		<td></td>
	    		<td>
	    			<button class="btn btn-info btn-sm" type="button" onclick="addCheckOrder();">提交</button>
	    		</td>
	    	</tr>
	    </table>
  </div>
</div>

<script type="text/javascript">
	function addCheckOrder(){
		var checkResult = $("[name='checkResult']:checked").val();
		var checkDesc = $("[name='checkDesc']").val();
		var reviewId = $("#reviewId").val();
		var resultId = $("#resultId").val();
		if(reviewId==''){
			layer.msg('请从审批页面入口进来',{icon:7,offset:'50px'});
			return;
		}
		if(checkResult==undefined){
			layer.msg('请选择评审结果',{icon:7,offset:'50px'});
			return;
		}
		if(checkResult=='2'&&checkDesc==''){
			layer.msg('请输入评审拒绝理由',{icon:7,offset:'50px'});
			return;
		}
		var data = {checkResult:checkResult,checkDesc:checkDesc,orderId:orderId,reviewId:reviewId,resultId:resultId};
		ajax.remoteCall("${ctxPath}/servlet/order/flow?action=addCheck",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon:1,time:1200},function(){
					popup.closeTab();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
</script>