package com.yunqu.work.servlet.ehr;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;

@WebServlet("/servlet/resume")
public class ResumeServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	public EasyResult actionForDelData() {
		try {
			String resumeIds = getJsonPara("resumeIds");
			EasySQL sql = new EasySQL("update yq_resume set resume_state = 1  where 1=1");
			sql.appendIn(resumeIds.split(","),"and resume_id");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
	}
	public EasyResult actionForDelFollow() {
		try {
			String followIds = getJsonPara("followIds");
			EasySQL sql = new EasySQL("delete from yq_resume_follow where 1=1");
			sql.appendIn(followIds.split(",")," and follow_id");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForAddFollow() {
		EasyRecord record = new EasyRecord("yq_resume_follow","follow_id");
		record.setColumns(getJSONObject("follow"));
		try {
			record.set("create_time", EasyDate.getCurrentDateString());
			record.set("follow_by_name", getUserName());
			record.set("follow_by", getUserId());
			record.setPrimaryValues(RandomKit.uniqueStr());
			
			this.getQuery().save(record);
			String resumeId = record.getString("RESUME_ID");
			String followLabel = record.getString("FOLLOW_LABEL");
			String followContent = record.getString("FOLLOW_CONTENT");
			this.getQuery().executeUpdate("update yq_resume set last_follow_label = ?,last_follow_time = ?,last_follow_content = ? where resume_id = ?",followLabel,EasyDate.getCurrentDateString(),followContent,resumeId);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForUpdateFollow() {
		EasyRecord record = new EasyRecord("yq_resume_follow","follow_id");
		record.setColumns(getJSONObject("follow"));
		try {
			this.getQuery().update(record);
			String resumeId = record.getString("RESUME_ID");
			String followLabel = record.getString("FOLLOW_LABEL");
			String followContent = record.getString("FOLLOW_CONTENT");
			this.getQuery().executeUpdate("update yq_resume set last_follow_label = ?,last_follow_time = ?,last_follow_content = ? where resume_id = ?",followLabel,EasyDate.getCurrentDateString(),followContent,resumeId);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForAdd() {
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("yq_resume","resume_id");
		record.set("post_type", params.getString("postType"));
		record.set("resume_id", params.getString("fkId"));
		record.set("file_id", params.getString("fileId"));
		record.set("file_name", params.getString("fileName"));
		try {
			record.set("upload_time", EasyDate.getCurrentDateString());
			record.set("upload_by", getUserId());
			this.getQuery().save(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
	}
	public EasyResult actionForGet() {
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("yq_resume","resume_id");
		record.set("resume_id", params.getString("resumeId"));
		try {
			record.set("get_time", EasyDate.getCurrentDateString());
			record.set("get_dept_id", getDeptId());
			record.set("get_by_id", getUserId());
			record.set("get_by_name", getUserName());
			this.getQuery().update(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
	}
	public EasyResult actionForUpdateField() {
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("yq_resume","resume_id");
		try {
			record.setColumns(params);
			this.getQuery().update(record);
			
			this.getQuery().executeUpdate("update yq_resume t1,"+Constants.DS_MAIN_NAME+".easi_user t2 set t1.get_by_id = t2.user_id  where t1.get_by_name =  t2.username and t1.resume_id = ?", record.getString("RESUME_ID"));
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForAddZpNeed() {
		EasyRecord record = new EasyRecord("yq_zp_need","zp_id");
		try {
			record.setColumns(getJSONObject("zpneed"));
			record.set("create_time", EasyDate.getCurrentDateString());
			record.set("update_time", EasyDate.getCurrentDateString());
			record.set("create_name", getUserName());
			record.remove("ZP_ID");
			this.getQuery().save(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
	}
	
	public EasyResult actionForUpdateZpNeed() {
		EasyRecord record = new EasyRecord("yq_zp_need","zp_id");
		try {
			record.setColumns(getJSONObject("zpneed"));
			record.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
	}
	
}
