<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>项目仪表盘</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }


        .layui-this {
            font-weight: bold;
        }


        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .top-3 {
            font-size: 24px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .pj-stat .layui-card {
            padding: 10px;
            border-radius: 5px;
        }

        .pj-stat .layui-card-hover:hover {
            transform: scale(1.02);
            transition: transform .3s ease;
            box-shadow: 0 4px 11px #0003;
            cursor: pointer;
        }


        .layui-icon-tips {
            margin-left: 3px;
            font-size: 14px;
        }


        #searchForm {
            overflow-y: auto;
            overflow-x: hidden;
        }

        .layui-tab-content .layui-card {
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }


    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm" autocomplete="off">
        <div class="layui-row layui-col-space10 pj-stat" data-mars="ProjectStatisticDao.projectDashboardStat">
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="projectCount">0</div>
                        <div class="top-2">当前进行中项目</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-3" id="taskCount">0</div>
                        <div class="top-2">当前进行中任务</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md4">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="riskCount">0</div>
                        <div class="top-2">当前待处理风险</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10 layui-tab">
            <ul class="layui-tab-title" style="background-color: #fff;">
                <li class="layui-this" lay-id="deptTab11">项目</li>
                <li lay-id="deptTab12">大区</li>
                <li lay-id="deptTab13">部门</li>
                <li>人力</li>
            </ul>
            <div class="layui-tab-content" style="margin: 0;padding-top:10px;background-color: #fff;">
                <div class="layui-tab-item layui-show">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md8">
                            <div class="layui-card">
                                <div class="layui-card-header">项目状态统计</div>
                                <div class="layui-card-body">
                                    <div style="height: 380px;width: 100%;" data-anim="fade"></div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-card">
                                <div style="height: 380px;width: 100%;" data-anim="fade"></div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md5">
                            <div class="layui-card">
                                <div style="height: 380px;width: 100%;" data-anim="fade"></div>
                            </div>
                        </div>
                        <div class="layui-col-md7">
                            <div class="layui-card">
                                <div style="height: 380px;width: 100%;" data-anim="fade"></div>
                            </div>
                        </div>
                    </div>


                </div>
                <div class="layui-tab-item">

                </div>
                <div class="layui-tab-item">

                </div>
                <div class="layui-tab-item">
                </div>
            </div>
        </div>
        <br>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

    <script>
        var loadMap = {};

        $(function () {

            $("#searchForm").render({
                success: function () {
                }
            });


        });

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
