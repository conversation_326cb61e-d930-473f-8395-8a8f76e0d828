package com.yunqu.work.servlet.flow;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.service.ApproveService;

@WebServlet("/servlet/flow/*")
public class FlowServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public void actionForIndex() {
		String flowCode = getPara("id");
		String businessId = getPara("businessId");
		String mode = getPara("mode");
		try {
			if(StringUtils.isBlank(flowCode)) {
				renderHtml("id not empty.");
				return;
			}
			keepPara();
			FlowModel flowModel =  ApproveService.getService().getFlow(flowCode);
			setAttr("flow", flowModel);
			setAttr("fdata", new JSONObject());
			String url = flowModel.getApplyUrl();
			if(StringUtils.isNotBlank(businessId)) {
				if(!"edit".equals(mode)) {
					url = flowModel.getDetailUrl();
				}
				FlowApplyModel applyInfo = ApproveService.getService().getApply(businessId);
				setAttr("applyInfo", applyInfo);
				String resultId = getPara("resultId");
				if(StringUtils.notBlank(resultId)) {
					ApproveResultModel approveResultModel = ApproveService.getService().getResult(resultId);
					setAttr("resultInfo", approveResultModel);
				}
			}else {
				setAttr("applyInfo",new FlowApplyModel());
			}
			
			String tableName = flowModel.getTableName();
			if(StringUtils.notBlank(tableName)&&StringUtils.notBlank(businessId)) {
				JSONObject  data = this.getQuery(3).queryForRow("select * from "+tableName+" where business_id = ?", new Object[] {businessId}, new JSONMapperImpl());
				setAttr("bdata", data);
			}
			if(StringUtils.isBlank(url)) {
				renderHtml("url not empty.");
				return;
			}
			url = url.replace("/yq-work", "");
			forward(url);
		} catch (SQLException e) {
			this.error(null, e);
			renderHtml(e.getMessage());
		}
	}
	
	public EasyResult actionForGetFlow() {
		String flowCode = getJsonPara("flowCode");
		if(StringUtils.isBlank(flowCode)){
			return EasyResult.fail();
		}
		return EasyResult.ok(ApproveService.getService().getFlow(flowCode));
	}
	public EasyResult actionForUpdateCategory(){
		EasyRecord model = new EasyRecord("yq_flow_category","flow_code");
		try {
			model.setColumns(getJSONObject("conf"));
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddCategory(){
		EasyRecord model = new EasyRecord("yq_flow_category","flow_code");
		try {
			model.setColumns(getJSONObject("conf"));
			model.set("create_time", EasyDate.getCurrentDateString());
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
}
