<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="payPlanEditForm" class="form-horizontal" data-mars="OrderDao.payPlanInfo" autocomplete="off" data-mars-prefix="plan.">
 		   		<input type="hidden" value="${param.planId}" name="plan.PLAN_ID"/>
 		   		<input type="hidden" value="${param.planId}" name="planId"/>
 		   		<input type="hidden" value="${param.orderId}" name="orderId"/>
 		   		<input type="hidden" value="${param.orderId}" name="plan.ORDER_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
	                     <tr style="display: none;">
		                     <td width="120px" class="required">付款方式</td>
		                     <td>
		                   		  <select class="form-control" data-value="${param.typeId}" data-rules="required" data-mars="OrderDao.payTypeDict" name="plan.TYPE_ID">
	                           		<option value="">请选择</option>
	                           	 </select>
		                    </td>
		                </tr>
			            <tr>
		                	<td  width="120px" class="required">计划付款月份</td>
		                    <td>
		                   		 <input data-rules="required" type="month" name="plan.PLAN_PAY_MONTH" class="form-control input-sm">
		                    </td>
	                     </tr>
			            <tr>
		                    <td class="required">计划付款金额</td>
		                    <td>
		                    	 <input type="text"  data-rules="required" value="${param.payAmount}" name="plan.PLAN_PAY_AMOUNT" class="form-control input-sm">
		                    </td>
	                    </tr>
	                    <c:if test="${!empty param.planId}">
	                        <tr>
			                    <td class="required">实际付款时间</td>
			                    <td>
									<input type="text" name="plan.REAL_PAY_DATE" readonly="readonly" class="form-control input-sm"/>
			                    </td>
				            </tr>
			            </c:if>
	                     <tr>
		                    <td class="required">付款依据</td>
		                    <td>
		                   		 <textarea type="text" data-rules="required" style="height: 80px;" name="plan.PAY_REASON" class="form-control input-sm"></textarea>
		                    </td>
		                </tr>
	                     <tr>
		                    <td>物料描述</td>
		                    <td>
		                   		 <textarea type="text" style="height: 60px;" name="plan.GOODS_DESC" class="form-control input-sm"></textarea>
		                    </td>
		                </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="PayPlanEit.ajaxSubmitForm()"> 保 存 </button>
					  <c:if test="${!empty param.planId }">
					      <button type="button" class="btn btn-default btn-sm ml-15" onclick="PayPlanEit.del();"> 删除 </button>
					  </c:if>
				</p>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
   
		jQuery.namespace("PayPlanEit");
	    
		PayPlanEit.planId='${param.planId}';
		
		var planId='${param.planId}';
		
		$(function(){
			$("#payPlanEditForm").render({success:function(result){

			}});  
		});
		
		PayPlanEit.ajaxSubmitForm = function(){
			if(form.validate("#payPlanEditForm")){
				if(PayPlanEit.planId){
					PayPlanEit.updateData(); 
				}else{
					PayPlanEit.insertData(); 
				}
			};
		}
		PayPlanEit.insertData = function() {
			var data = form.getJSONObject("#payPlanEditForm");
			ajax.remoteCall("${ctxPath}/servlet/order?action=addPayPlan",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadPayPlanList();
 					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		PayPlanEit.updateData = function() {
			var data = form.getJSONObject("#payPlanEditForm");
			ajax.remoteCall("${ctxPath}/servlet/order?action=updatePayPlan",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadPayPlanList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		PayPlanEit.del = function() {
			layer.confirm('是否确认删除?',function(index){
				layer.close(index);
				var data = form.getJSONObject("#payPlanEditForm");
				ajax.remoteCall("${ctxPath}/servlet/order?action=delPayPlan",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							layer.closeAll();
							reloadPayPlanList();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>