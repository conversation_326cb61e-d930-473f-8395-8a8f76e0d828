package com.yunqu.work.dao.other;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.FolderModel;

@WebObject(name="FolderDao")
public class FolderDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		FolderModel model=new FolderModel();
		model.setColumns(getParam("folder"));
		return queryForRecord(model);
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select folder_id,folder_name from YQ_FOLDER");
	}
	
	@WebControl(name="docFolder",type=Types.LIST)
	public JSONObject docFolder(){
		int folderAuth = param.getIntValue("folderAuth");
		if(folderAuth==0||folderAuth==1) {
			EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
			sql.append(1,"and folder_auth = ?");
			sql.append(0,"and folder_state = ?");
			sql.append(param.getString("source"),"and source = ?","0");
			return queryForList(sql.getSQL(), sql.getParams());
		}else if(folderAuth==2) {
			EasySQL sql =  new EasySQL();
			sql.append(getUserId(),"select * from yq_folder where folder_auth = 2 and creator= ?");
			sql.append(0,"and folder_state = ?");
			sql.append(param.getString("source"),"and source = ?","0");
			return queryForList(sql.getSQL(), sql.getParams());
		}else if(folderAuth==3) {
			EasySQL sql =  new EasySQL();
			sql.append(getDeptId(),"select * from yq_folder where folder_auth = 3 and dept_id = ?");
			sql.append(0,"and folder_state = ?");
			sql.append(param.getString("source"),"and source = ?","0");
			return queryForList(sql.getSQL(), sql.getParams());
		}else if(folderAuth==4) {
			EasySQL sql =  new EasySQL();
			sql.append("select * from yq_folder where folder_auth = 4");
			sql.append(0,"and folder_state = ?");
			sql.append(param.getString("source"),"and source = ?","0");
			if(!isSuperUser()){
				sql.append("and (");
				sql.append(getUserId(),"find_in_set(?,share_user_ids)");
				sql.append("or");
				sql.append(getUserId(),"creator = ?");
				sql.append(")");
			}
			return queryForList(sql.getSQL(), sql.getParams());
		}
		return emptyPage();
	}
	
	/**
	 * 文档库
	 * @return
	 */
	@WebControl(name="list",type=Types.TEMPLATE)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
		sql.append(0,"and folder_state = ?");
		sql.append(param.getString("folderId"),"and p_folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and folder_name like ?");
		sql.appendLike(param.getString("folderCode"),"and folder_code like ?");
		sql.append(param.getString("fkId"),"and fk_id = ?");
		sql.append(1,"and folder_auth = ?");
		sql.append(param.getString("source"),"and source = ?","0");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 文件夹下面的文件
	 * @return
	 */
	@WebControl(name="files",type=Types.LIST)
	public JSONObject files(){
		EasySQL sql=getEasySQL("select doc_id,folder_id,manager,title,doc_auth,view_count,efficient,CREATE_TIME,update_time,creator,create_name,update_by from yq_doc where 1=1");
		String folderIds = param.getString("folderIds");
		String folderId = param.getString("folderId");
		if(StringUtils.notBlank(folderIds)) {
			sql.appendIn(folderIds.split(","),"and folder_id");
		}else {
			sql.append(folderId,"and folder_id = ?");
		}
		sql.appendLike(param.getString("folderName"),"and title like ?");
		sql.append(1,"and doc_auth = ?");
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectLink",type=Types.TEMPLATE)
	public JSONObject projectLink(){
		EasySQL sql=getEasySQL("select * from yq_project_link where 1=1");
		sql.append(param.getString("folderId"),"and p_id = ?");
		sql.append(param.getString("projectId"),"and project_id = ?",false);
		sql.append(0,"and state = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="linkInfo",type=Types.RECORD)
	public JSONObject linkInfo(){
		EasySQL sql=getEasySQL("select * from yq_project_link where 1=1");
		sql.append(param.getString("linkId"),"and link_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 自己创建的文档目录
	 * @return
	 */
	@WebControl(name="mylist",type=Types.LIST)
	public JSONObject mylist(){
		EasySQL sql=getEasySQL("select * from YQ_FOLDER where 1=1");
		sql.append(0,"and folder_state = ?");
		sql.append(param.getString("folderId"),"and p_folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and folder_name like ?");
		sql.append(getUserPrincipal().getUserId(),"and creator = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 文件库
	 * @return
	 */
	@WebControl(name="myfiles",type=Types.LIST)
	public JSONObject myfiles(){
		EasySQL sql=getEasySQL("select t1.*,t2.folder_name from yq_files t1 left join yq_folder t2 on t1.folder_id = t2.folder_id where 1=1");
		String folderIds = param.getString("folderIds");
		String folderId = param.getString("folderId");
		if(StringUtils.notBlank(folderIds)) {
			sql.appendIn(folderIds.split(","),"and t1.folder_id");
		}else {
			sql.append(folderId,"and t1.folder_id = ?");
		}
		sql.appendRLike(param.getString("folderCode"),"and t1.folder_code like  ?");
		sql.appendLike(param.getString("fileName"),"and t1.file_name like ?");
		sql.appendLike(param.getString("userName"),"and t1.create_name like ?");
		sql.appendLike(param.getString("folderName"),"and t2.folder_name like ?");
		sql.append(param.getString("source"),"and t1.source = ?");
		
		this.setOrderBy(sql);
		
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	private void setOrderBy(EasySQL sql) {
		String sortName =  param.getString("sortName");
		String sortType =  param.getString("sortType");
		if(StringUtils.notBlank(sortType)) {
			sql.append("order by ").append("t1."+sortName).append(sortType);
		}else {
			sql.append("order by t1.create_time desc");
		}
	}
	
	
	/**
	 * 自己创建的文档文件
	 * @return
	 */
	@WebControl(name="mydoc",type=Types.LIST)
	public JSONObject mydoc(){
		EasySQL sql=getEasySQL("select * from yq_doc where 1=1");
		sql.append(param.getString("folderId"),"and folder_id = ?","0");
		sql.appendLike(param.getString("folderName"),"and title like ?");
		sql.append(getUserPrincipal().getUserId(),"and creator = ?");
		sql.append("order by create_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}

}
