<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
	<style>
		.select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
   			 background-color: #fff;
		}
		#editForm img{max-width: 98%!important;height: auto;}
		#editForm .w-e-text{max-width: 100%!important;}
		#editForm table,tr,td,p{max-width: 100%!important;}
		.layui-table-tips{z-index: 999999999999999999!important;};
		.btn-group-sm > .btn, .btn-sm{padding: 5px!important;}
		.select2 {z-index: 90;}
	</style>
	<link href="${ctxPath}/static/css/wangEditor-fullscreen.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editForm" style="margin-bottom: 100px;" data-mars="TaskDao.record" autocomplete="off" data-mars-prefix="task.">
	     		   <input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
	     		   <input type="hidden" id="taskId" value="${param.taskId}" name="task.TASK_ID"/>
	     		   <input type="hidden" value="${param.taskId}" name="fkId"/>
							<table class="table table-edit table-vzebra">
						        <tbody>
						            <tr>
					                    <td class="required">标题</td>
					                    <td>
					                    	<input data-rules="required" maxlength="200" type="text" name="task.TASK_NAME" class="form-control input-sm">
					                    </td>
						            </tr>
						            <tr id="condition" class="hidden">
						            	<td>
						            		类别
						            	</td>
						            	<td>
						            		<label style="vertical-align:text-bottom;" class="radio radio-info radio-inline">
				                    			<input type="radio" checked="checked" value="1" name="task.TASK_TYPE"/> <span>项目合同任务</span>
					                    	</label>
					                    </td>
						            </tr>
						          <tr>
						            	<td>任务描述</td>
						               	<td>
						               	     <div id="editor">
										     </div>
				                           <textarea id="wordText" data-text="false" style="height: 500px;width:400px;display: none;" class="form-control input-sm" name="task.TASK_DESC"></textarea>
						               	</td>
						            </tr>
						            <tr>
						                <td style="width: 80px">附件</td>
						               	<td>
						               		<div data-template="template-files" data-mars="FileDao.fileList"></div>
						               		<div id="fileList"></div>
											<button class="btn btn-sm btn-default mt-5" type="button" onclick="$('#localfile').click()">+添加</button>
						               	</td>
						            </tr>
						          <tr>
						            	<td colspan="2">
							            		<fieldset class="content-title text-l">
											  		<legend>任务属性</legend>
												</fieldset>
						            	</td>
						            </tr>
						           <!-- <tr>
					                     <td>状态</td>
					                    <td>
					                    	<select name="task.TASK_STATE" class="form-control input-sm">
					                    		<option value="1">未开始</option>
					                    		<option value="2">进行中</option>
					                    		<option value="3">已完成</option>
					                    		<option value="4">已暂停</option>
					                    		<option value="5">已取消</option>
					                    		<option value="6">已关闭</option>
					                    	</select>
					                    </td> 
					                    
					                </tr>-->
					                 <tr>
						            	<td>
						            		任务类型
						            	</td>
						            	<td>
						                    	<select  name="task.TASK_TYPE_ID" data-rules="required" data-mars="TaskDao.taskType" class="form-control"></select>
					                    </td>
						            </tr>
					                <tr <c:if test="${!empty param.projectId}">style="display:none"</c:if>>
					                	<td>
					                		所属项目
					                	</td>
					                	<td>
						                	<c:choose>
							                	<c:when test="${!empty param.projectId}">
							                		 <input type="hidden" name="task.PROJECT_ID" value="${param.projectId}"/>
							                		 <input type="hidden" name="projectId" value="${param.projectId}"/>
							                	</c:when>
							                	<c:otherwise>
				                					<div id="project">
						                    	  	   <input type="hidden" name="task.PROJECT_ID"/>
			                    					   <input id="taskProjectId" ts-selected="${param.projectId}" type="text" class="form-control input-sm"/>
							           			   </div>
							                	</c:otherwise>
						               		 </c:choose>
					                	</td>
					                </tr>
						            <tr>
					                    <td class="required">指派给</td>
					                    <td>
					                    	<select data-rules="required" id="users" data-mars="CommonDao.userDict" name="task.ASSIGN_USER_ID" class="form-control input-sm">
					                    		<option value="">请选择</option>
					                    	</select>
					                    </td>
					                </tr>
						            <tr>
					                    <td>抄送人</td>
					                    <td>
					                    	<select  multiple="multiple" data-mars="CommonDao.userDict" id="mailtos" name="mailtos" class="form-control input-sm">
					                    	</select>
					                    </td>
					                </tr>
						            <tr>
					                    <td class="required">任务起始时间</td>
					                    <td>
					                    	<input id="beginDate" type="text" style="display: inline-block;width: 140px" data-rules="required" data-mars="CommonDao.date02" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" name="task.PLAN_STARTED_AT"  class="form-control input-sm Wdate">
					                    	<input id="endDate" type="text" style="display: inline-block;width: 140px" data-rules="required" placeholder="截至时间" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" name="task.DEADLINE_AT" class="form-control input-sm Wdate">
					                   		<div class="btn-group btn-group-sm">
					                   			<button onclick="fillDate(1)" class="btn btn-sm btn-default" type="button">1天</button>
					                   			<button onclick="fillDate(2)" class="btn btn-sm btn-default" type="button">2天</button>
					                   			<button onclick="fillDate(3)" class="btn btn-sm btn-default" type="button">3天</button>
					                   			<button onclick="fillDate(7)" class="btn btn-sm btn-default" type="button">7天</button>
					                   			<button onclick="fillDate(15)" class="btn btn-sm btn-default" type="button">15天</button>
					                   			<button onclick="fillDate(30)" class="btn btn-sm btn-default" type="button">30天</button>
					                   		</div>
					                    </td>
					                </tr>
						            <tr>
					                    <td class="required">优先级</td>
					                    <td>
												<label class="radio radio-success radio-inline">
								   					<input type="radio" name="task.TASK_LEVEL" value="1"> <span>较低</span>
								  				 </label>
												<label class="radio radio-success radio-inline">
								   					<input type="radio" checked="checked" name="task.TASK_LEVEL" value="2"> <span>普通</span>
								  				 </label>
												<label class="radio radio-success radio-inline">
								   					<input type="radio" name="task.TASK_LEVEL" value="3"> <span>紧急</span>
								  				 </label>
												<label class="radio radio-success radio-inline">
								   					<input type="radio" name="task.TASK_LEVEL" value="4"> <span>非常紧急</span>
								  				 </label>
					                    </td>
					                </tr>
						            <tr>
						              	<td>
						            		提醒方式
						            	</td>
						            	<td>
						            		<label class="checkbox checkbox-default checkbox-inline">
				                    			<input type="checkbox" value="1" disabled="disabled" name="task.NOTICE_TYPES"/> <span>即时通讯</span>
					                    	</label>
						            		<label class="checkbox checkbox-default checkbox-inline">
				                    			<input type="checkbox" checked="checked" value="2" onclick="$(this).prop('checked', true);" name="task.NOTICE_TYPES"/> <span>系统私信</span>
					                    	</label>
					                    	<label class="checkbox checkbox-default checkbox-inline">
						                    	<input type="checkbox" value="3" checked="checked"  name="task.NOTICE_TYPES"/> <span>邮件提醒</span>
					                    	</label>
					                    	<label class="checkbox checkbox-default checkbox-inline">
						                    	<input type="checkbox" value="4" name="task.NOTICE_TYPES" checked="checked"/> <span>微信推送</span>
					                    	</label>
						            	</td>
						            </tr>
						            
						        </tbody>
		  					  </table>
							 <div class="layer-foot text-c" style="z-index: 99999999999;box-shadow: 0 -10px 20px 0 rgba(45,67,118,.1);">
							    	  <button type="button" class="btn btn-primary btn-sm" onclick="Task.ajaxSubmitForm()"> 保 存  </button>
								      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
							</div>			
	  		</form>
  		 <script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Task.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditorExt.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/tableSelect.js"></script>
	<script type="text/javascript">
		
	    String.prototype.replaceAll = function(s1, s2) {
		    return this.replace(new RegExp(s1, "gm"), s2);
		}
	
		jQuery.namespace("Task");
	    
		Task.taskId='${param.taskId}';
		var taskId='${param.taskId}';
		var projectId='${param.projectId}';
		var taskStateVal='${param.taskState}';
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.customConfig.menus = [
   		    'head',  // 标题
   		    'bold',  // 粗体
   		    'fontSize',  // 字号
   		    'fontName',  // 字体
   		    'foreColor',  // 文字颜色
   		    'link',  // 插入链接
   		    'list',  // 列表
   		    'justify',  // 对齐方式
   		    'quote',  // 引用
   		    'code',  // 插入代码
   		    'emoticon',  // 表情
   		    'image',  // 插入图片
   		    'table',  // 表格'
   		 ];
		editor.customConfig.pasteFilterStyle = false;
		editor.customConfig.pasteIgnoreImg = false;
		editor.customConfig.pasteTextHandle = function (content) {
		      if (content == '' && !content) return ''
		      var str = content
		      if(str.indexOf('MsoNormal')>-1){
		    	  layer.msg("复制word文档内容可能格式不正确,请对格式进行调整。",{icon:7});
		      }
		      if(str.indexOf('file://')>-1){
		    	  layer.msg("复制word文档附加的图片路径不存在,请单独复制图片。",{icon:7});
		      }
		      return str;
		} 
		weUpload(editor,{uploadImgMaxLength:3});
		editor.customConfig.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.create();
		E.fullscreen.init(editor);
		E.viewSource.init(editor);
		$(".btn_fullscreen").click(function(){
			window.wangEditor.fullscreen.run(editor.id);
		});
		$(".btn_viewSource").click(function(){
			window.wangEditor.viewSource.run(editor.id);
		});
		
		$(function(){
			if(taskId==''){
				var taskProjectId = localStorage.getItem("taskProjectId");
				var taskProjectName = localStorage.getItem("taskProjectName");
				if(taskProjectId){
					$("#editForm [name='task.PROJECT_ID']").val(taskProjectId);
					$("#taskProjectId").val(taskProjectName);
					$("#taskProjectId").attr("ts-selected",taskProjectId);
				}
			}
			layui.config({
				  base: '${ctxPath}/static/js/'
			}).use('tableSelect'); //加载自定义模块
			
			layui.use(['form','tableSelect'], function(){
				var form = layui.form;
				form.render();
				var tableSelect = layui.tableSelect;
				tableSelect.render({
					elem: '#taskProjectId',
					searchKey: 'projectName',
					checkedKey: 'PROJECT_ID',
					page:true,
					searchPlaceholder: '请输入项目名称',
					table: {
						mars: 'ProjectContractDao.projectList',
						cols: [[
							{ type: 'radio' },
							{ field: 'PROJECT_ID',hide:true,title: 'ID'},
							{ field: 'PROJECT_NAME', title: '名称', width: 320 },
							{ field: 'PROJECT_NO', title: '编号', width: 150 }
						]]
					},
					done: function (elem, data) {
						var names = [];
						var ids = [];
						layui.each(data.data, function (index, item) {
							names.push(item.PROJECT_NAME)
							ids.push(item.PROJECT_ID)
						});
						elem.attr("ts-selected",ids.join(","));
						elem.val(names.join(","));
						elem.prev().val(ids.join(","));
					}
				});

			});

			
		    $('[data-toggle="tooltip"]').tooltip();
			$("#editForm").render({success:function(result){
				 var c = $("#wordText").val();
				 c = c.replaceAll("http://work.yunqu-info.cn","");
				 editor.txt.html(c);
				 var record=result['TaskDao.record'];
				 requreLib.setplugs('select2',function(){
					if(typeof record.data != 'string'){
						var mailtos=record.mailtos;
						$("[name='mailtos']").val(mailtos);
						$("#taskProjectId").val(record.projectName);
						$("#taskProjectId").attr("ts-selected",record.data['PROJECT_ID']);
					}
					if(taskId==''){
						var taskAssignUserId = localStorage.getItem("taskAssignUserId")||'';
						var taskMailtos = localStorage.getItem("taskMailtos")||'';
						$("#users").val(taskAssignUserId);
						$("[name='mailtos']").val(taskMailtos.split(","));
					}
					
					$("#editForm #users,#editForm #mailtos").select2({theme: "bootstrap",placeholder:'请选择'});
					if(taskId){
						if(taskStateVal!=0){
							$("#mailtos,#users").prop("disabled", true);
						}
						$("input[name='task.NOTICE_TYPES']").prop("disabled", true);
					}
				 });
				 $("#editForm .w-e-text-container").css("height","150px");
				 layer.photos({photos:'#editor',anim: 0,shade:0,shadeClose:true,closeBtn:true}); 
				 $("#editForm .w-e-text-container").keydown(function(event) {
					  if(event.keyCode == "13") {
						 var height = $(this).height();
						 $(this).css('height',(height+20)+'px');
					  }
					   if(event.keyCode == 8) {
						 var height = $(this).height();
						 height=height>120?height:120;
						 $(this).css('height',(height-20)+'px');
					   }
				});
			}});  
			if(projectId){
				$("[name='task.PROJECT_ID']").val(projectId);
			}
		});
		function fillDate(val){
			var v = addDate(val);
			if(v.indexOf("NaN")>-1){
				layer.msg("时间格式不正确,请检查浏览器兼容性或者换chome");
			}else{
				$("#endDate").val(v);
			}
		}
		function addDate(days) {
            if (days == undefined || days == '') {
                days = 1;
            }
            var d=$("#beginDate").val();
            if(d){
	            var date = new Date(d.replace(/-/g,'/'));
	            date.setDate(date.getDate() + days);
	            var month = date.getMonth() + 1;
	            var day = date.getDate();
	            var h = date.getHours();
	            var m = date.getMinutes();
	            return date.getFullYear() + '-' + getFormatDate(month) + '-' + getFormatDate(day)+" "+ getFormatDate(h)+":"+ getFormatDate(m);
            }else{
            	return "";
            }
        }
		function getFormatDate(arg) {
            if (arg=='0'||arg) {
	            var re = arg + '';
	            if (re.length < 2) {
	                re = '0' + re;
	            }
	            return re;
            }else{
                return '';
            }
        }

		Task.uploadFile = function(callback){
			var fkId='';
			if(Task.taskId){
				fkId=taskId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'callback',fileMaxSize:(1024*50),fkId:fkId,source:'task'});
		}
		var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
		}
		
		Task.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				var endDate=$("#endDate").val();
				if(endDate==''){
					layer.msg('任务截至时间不能为空!');
					return;
				}
				 var html=$("#wordText").val();
				 html = html.replaceAll("http://work.yunqu-info.cn","");
				 $("#wordText").val(html);
				if(Task.taskId){
					Task.updateData(); 
				}else{
					Task.insertData(); 
				}
			};
		}
		Task.insertData = function(flag) {
			$("#taskId").val($("#randomId").val());
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/task?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						localStorage.setItem("taskAssignUserId",$("#users").val());
						var ids = $("[name='mailtos']").val()||[];
						localStorage.setItem("taskMailtos",ids.join());
						
						var projectId = $("#editForm [name='task.PROJECT_ID']").val();
						if(projectId){
							var projectName = $("#taskProjectId").val();
							localStorage.setItem("taskProjectId",projectId);
							localStorage.setItem("taskProjectName",projectName);
						}
						reloadTaskList();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Task.updateData = function(flag) {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/task?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadTaskList();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>