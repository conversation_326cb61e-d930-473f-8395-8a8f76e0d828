<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
		.layui-table td, .layui-table th{padding: 9px 6px;}
		.layui-table input{width: 100%!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="editForm">
			<div class="ibox">
				<div class="ibox-title mb-15 clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-inbox"></span> 项目工作量统计（月）</h5>
						   	<EasyTag:hasRole roleId="PROJECT_MANAGER">
						      <div class="input-group input-group-sm">
								 <span class="input-group-addon">部门</span>	
								 <select name="deptId" data-mars="EhrDao.allDept" data-mars-reload="false" onchange="loadData()" class="form-control input-sm">
			                    		<option value="0">请选择</option>
		                    	</select>
						     </div>
						   	</EasyTag:hasRole>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">年份</span>	
							 <select name="year" onchange="loadData()" class="form-control input-sm">
		                    		<option value="2020">2020</option>
	                    	</select>
					     </div>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">月份</span>	
							 <select name="month" onchange="loadData()" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="01" selected="selected">1</option>
		                    		<option value="02">2</option>
		                    		<option value="03">3</option>
		                    		<option value="04">4</option>
		                    		<option value="05">5</option>
		                    		<option value="06">6</option>
		                    		<option value="07">7</option>
		                    		<option value="08">8</option>
		                    		<option value="09">9</option>
		                    		<option value="10">10</option>
		                    		<option value="11">11</option>
		                    		<option value="12">12</option>
	                    	</select>
					     </div>
	          	     </div>
	              </div> 
	             <div class="ibox-content">
				    <div data-mars="ProjectDao.deptWorkhourStat" data-template="tpl" data-container="container" class="layui-row" id="container"></div>
	             </div>
				<div style="height: 120px;"></div>
				<script type="text/x-jsrender" id="tpl">
				  {{if data.length>0}}
					<div class="layui-collapse" lay-accordion>
						{{for data}}
							  <div class="layui-colla-item">
								<h2 class="layui-colla-title" data-id="{{:PROJECT_ID}}">{{:PROJECT_NAME}}<span class="ml-5 label label-info label-outline">{{:TIME/100}}人/月</span></h2>
								<div class="layui-colla-content">
								</div>
							  </div>
						{{/for}}
					</div>
					{{else}}
					 暂无填报数据
				  {{/if}}
				</script>
				<script type="text/x-jsrender" id="detailTpl">
					<table class="layui-table">
					  <thead>
						<tr>
						  <th>部门</th>
						  <th>姓名</th>
						  <th>工作月</th>
						  <th>工作量(人月)</th>
						  <th>填报者</th>
						  <th>填写时间</th>
						</tr> 
					  </thead>
					  <tbody>
					  {{for data}}
						<tr>
						  <td> {{:DEPTS}}</td>
						  <td>{{:USERNAME}}</td>
						  <td>{{:MONTH_ID}}</td>
						  <td>{{:WORK_TIME/100}}</td>
						  <td>{{call:CREATOR fn='getUserName'}}</td>
						  <td>{{:CREATE_TIME}}</td>
						</tr>
					  {{/for}}
 					  {{if data.length==0}}<tr><td  colspan="6">暂无数据</td></tr>{{/if}}
					  </tbody>
					</table>
						
				</script>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		function loadData(){
			$("#editForm").render({success:function(){
				layui.use(['element'], function(){
					var element = layui.element;
					element.render();
				});
				$(".layui-colla-title").click(function(){
					var t=$(this);
					if(t.attr("isload")==1){
						return;
					}
					t.attr("isload","1");
					var data = form.getJSONObject("#editForm");
					data['projectId']=t.data("id");
					ajax.daoCall({params:data,controls:['ProjectDao.projectWorkhourDetail']},function(result){
						var tpl = $.templates("#detailTpl");
						var html = tpl.render({data:result['ProjectDao.projectWorkhourDetail']['data']});
						t.next().html(html);
					});
					
				});
				//$(".layui-colla-title").first().click();
			}});
		}
		$(function(){
			//设置默认月份
			loadData();
		});
		function projectWorkhourDetail(projectId){
			//
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>