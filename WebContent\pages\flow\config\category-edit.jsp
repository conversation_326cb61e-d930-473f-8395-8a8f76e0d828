<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>配置</title>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editConfForm" autocomplete="off" data-mars="FlowConfigDao.categoryInfo" data-mars-prefix="conf.">
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px;">流程编码</td>
		                    <td style="width: 40%;">
		                    	<input name="conf.FLOW_CODE" data-rules="required" value="${flowCode}" <c:if test="${!empty flowCode}">readonly="readonly"</c:if> class="form-control input-sm" type="text">
		                    </td>
		                    <td style="width: 80px;">流程名称</td>
		                    <td style="width: 40%;">
		                    	<input name="conf.FLOW_NAME" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
		                <tr>
		                    <td style="width: 80px;">流程申请名称</td>
		                    <td style="width: 40%;">
		                    	<input name="conf.FLOW_TITLE" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                    <td style="width: 80px;">审批字段</td>
		                    <td style="width: 40%;">
		                    	<select name="conf.APPROVER_FIELD" data-rules="required" class="form-control input-sm">
		                    		<option value="apply_by">apply_by</option>
		                    		<c:forEach begin="1" end="20" var="item">
		                    			<option value="data${item}">data${item}</option>
		                    		</c:forEach>
		                    	</select>
		                    </td>
		                </tr>
		                 <tr>
		                  	<td>组织</td>
		                    <td>
		                    	<select name="conf.ROOT_ID" class="form-control input-sm">
		                    		<option value="0">公用</option>
		                    		<option value="yq">云趣</option>
		                    		<option value="zr">中融</option>
		                    	</select>
		                    </td>
		                  	<td>编号生成规则</td>
		                    <td>
		                    	<input name="conf.APPLY_NO_RULE" class="form-control input-sm">
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">分类</td>
		                    <td>
		                    	<select name="conf.P_FLOW_CODE" class="form-control input-sm" data-mars="FlowConfigDao.categoryDict">
		                    		<option value="0">--</option>
		                    	</select>
		                    </td>
		                    <td style="width: 80px;">关联文档</td>
		                    <td>
		                    	<input name="conf.FAQ_URL" class="form-control input-sm" type="text">
		                    </td>
		                 <!--    <td style="width: 80px;">展示类型</td>
		                    <td>
		                    	  <label class="radio radio-info radio-inline" style="margin-top: 2px;">
		                          	 <input type="radio" checked value="0" name="conf.SOURCE_TYPE" onclick="$('.sourceType1').show();$('.sourceType2').hide();"><span>固定页面</span>
		                          </label>
		                          <label class="radio radio-info radio-inline">
		                          	 <input type="radio" value="1" name="conf.SOURCE_TYPE" onclick="$('.sourceType2').show();$('.sourceType1').hide();"><span>自定义表单</span>
		                          </label>
		                    </td>
		                </tr>
			            <tr class="sourceType2">
		                    <td style="width: 80px;">表单</td>
		                    <td colspan="3">
		                    	<select name="conf.FORM_ID" class="form-control input-sm" data-mars="FormDao.formDict">
		                    		<option value="">--</option>
		                    	</select>
		                    </td>
		                </tr> -->
			            <tr class="sourceType1">
		                    <td style="width: 80px;">业务主表</td>
		                    <td>
		                    	<input name="conf.BUSI_TABLE" value="yq_flow_apply_ext" class="form-control input-sm" type="text">
		                    </td>
		                    <td style="width: 80px;">业务明细表</td>
		                    <td>
		                    	<input name="conf.ITEM_TABLE" value="yq_flow_apply_item" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
			            <tr class="sourceType1">
		                    <td style="width: 80px;">发起流程url</td>
		                    <td>
		                    	<input name="conf.APPLY_URL" class="form-control input-sm" type="text">
		                    </td>
		                    <td style="width: 80px;">查看流程url</td>
		                    <td>
		                    	<input name="conf.DETAIL_URL" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
			            <tr>
			          	  <td style="width: 80px;">查询列表url</td>
		                    <td>
		                    	<input name="conf.QUERY_URL" class="form-control input-sm" type="text">
		                    </td>
		                    <td style="width: 80px;">流程管理控制</td>
		                    <td>
		                    	<select name="conf.FLOW_MGR_IDS" data-mars="CommonDao.userDict" class="form-control input-sm select2" multiple="multiple">
		                    		<option value="0">--</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">部门可见权限</td>
		                    <td>
		                    	<select name="conf.FLOW_AUTH_DEPTS" class="form-control input-sm select2" data-mars="EhrDao.allDept" multiple="multiple">
		                    		<option value="0">全员可见</option>
		                    	</select>
		                    </td>
		                    <td style="width: 80px;">排除可见部门</td>
		                    <td>
		                    	<select name="conf.NOT_AUTH_DEPTS" class="form-control input-sm select2" data-mars="EhrDao.allDept" multiple="multiple">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">申请人节点名称</td>
		                    <td>
		                    	<input name="conf.START_NODE_NAME" value="申请人" class="form-control input-sm">
		                    </td>
		                    <td style="width: 80px;">状态</td>
		                    <td>
		                    	<select name="conf.FLOW_STATE" class="form-control input-sm">
		                    		<option value="0">启用</option>
		                    		<option value="1">暂停</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">相邻审批自动跳过</td>
		                    <td>
		                    	<select name="conf.XL_CHECK_SKIP" class="form-control input-sm">
		                    		<option value="0">否</option>
		                    		<option value="1">是</option>
		                    	</select>
		                    </td>
		                    <td style="width: 80px;">重复审批自动跳过</td>
		                    <td>
		                    	<select name="conf.REPEAT_CHECK_SKIP" class="form-control input-sm">
		                    		<option value="0">否</option>
		                    		<option value="1">是</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">申请人审批自动跳过</td>
		                    <td>
		                    	<select name="conf.APPLY_CHECK_SKIP" class="form-control input-sm">
		                    		<option value="1">是</option>
		                    		<option value="0">否</option>
		                    	</select>
		                    </td>
		                    <td style="width: 80px;">申请入口</td>
		                    <td>
		                    	<select name="conf.ENTRY_SHOW" class="form-control input-sm">
		                    		<option value="1">显示</option>
		                    		<option value="0">隐藏</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">描述</td>
		                    <td>
		                    	<input name="conf.FLOW_DESC" class="form-control input-sm" type="text">
		                    </td>
		                    <td style="width: 80px;">排序</td>
		                    <td>
		                    	<input name="conf.FLOW_INDEX" data-rules="required" class="form-control input-sm" type="number">
		                    </td>
		                </tr>
		                <tr>
		                  	<td>可执行代码</td>
		                    <td colspan="3">
		                    	<textarea name="conf.JS_CODE" class="form-control input-sm" style="height: 80px;"></textarea>
		                    </td>
		                </tr>
			            <tr>
		                    <td style="width: 80px;">流程说明</td>
		                    <td colspan="3">
		                    	<textarea name="conf.FLOW_REMARK" class="form-control input-sm" style="height: 100px;"></textarea>
		                    </td>
		                </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="FlowEditConf.ajaxSubmitForm()"> 确 定  </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
        var FlowEditConf={flowCode:'${flowCode}'};
        
        FlowEditConf.ajaxSubmitForm = function(){
			if(form.validate("#editConfForm")){
				var data = form.getJSONObject("#editConfForm");
				
				var flowMgrIds = $("[name='conf.FLOW_MGR_IDS']").val()||[];
				data['conf.FLOW_MGR_IDS'] = flowMgrIds.join(',');
				
				var flowAuthDepts = $("[name='conf.FLOW_AUTH_DEPTS']").val()||[];
				data['conf.FLOW_AUTH_DEPTS'] = flowAuthDepts.join(',');
				
				var notAuthDepts = $("[name='conf.NOT_AUTH_DEPTS']").val()||[];
				data['conf.NOT_AUTH_DEPTS'] = notAuthDepts.join(',');
				
				if(FlowEditConf.flowCode==''){
					FlowEditConf.updateData('add',data); 
				}else{
					FlowEditConf.updateData('update',data); 
				}
			};
		}
        FlowEditConf.updateData = function(flag,data) {
			ajax.remoteCall("${ctxPath}/web/flow/config/"+flag+"Category",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose("#editConfForm");
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		$(function(){
			$("#editConfForm").render({success:function(result){
				 var record = result['FlowConfigDao.categoryInfo'].data;
				 var flowMgrIds = record['FLOW_MGR_IDS'];
				 if(flowMgrIds){
					 $("[name='conf.FLOW_MGR_IDS']").val(flowMgrIds.split(','));
				 }
				 var flowAuthDepts = record['FLOW_AUTH_DEPTS'];
				 if(flowAuthDepts){
					 $("[name='conf.FLOW_AUTH_DEPTS']").val(flowAuthDepts.split(','));
				 }
				 
				 var notAuthDepts = record['NOT_AUTH_DEPTS'];
				 if(notAuthDepts){
					 $("[name='conf.NOT_AUTH_DEPTS']").val(notAuthDepts.split(','));
				 }
				 
				 var sourceType = record['SOURCE_TYPE'];
				 if(sourceType=='1'){
					 $('.sourceType2').show();$('.sourceType1').hide();
				 }else{
					 $('.sourceType1').show();$('.sourceType2').hide();
				 }
				 
				 requreLib.setplugs('select2',function(){
					$("#editConfForm .select2").select2({theme: "bootstrap",placeholder:'请选择'});
					$(".select2-container").css("width","100%");
				 });
			}});
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>