 <%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>设置</title>
	<style>
		.layui-input-block{margin-left: 160px;}
		.layui-form-label{width: 120px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="settingform" method="post" class="layui-form">
		      <div class="ibox-content" style="min-height: 500px">
				<div class="ibox-content-header">
					设置<span style="color: #666;margin-left: 20px;font-size: 12px;">若切换电脑或浏览器请重新设置参数</span>
				</div>
				<div class="ibox-content-body">
						<div class="layui-form-item">
						    <label class="layui-form-label">文档</label>
						    <div class="layui-input-block">
						      <input type="checkbox" name="close" lay-skin="switch" lay-text="ON|OFF">
						    </div>
						  </div>
						  <div class="layui-form-item">
						    <label class="layui-form-label">日常安排</label>
						    <div class="layui-input-block">
						      <input type="checkbox" checked="" name="open" lay-skin="switch" lay-filter="switchTest" lay-text="ON|OFF">
						    </div>
						  </div>
						  <div class="layui-form-item">
						    <label class="layui-form-label">点餐</label>
						    <div class="layui-input-block">
						      <input type="checkbox" checked="" name="open" lay-skin="switch" lay-filter="switchTest" lay-text="ON|OFF">
						    </div>
						  </div>
						  <div class="layui-form-item">
						    <label class="layui-form-label">通知公告</label>
						    <div class="layui-input-block">
						      <input type="checkbox" checked="" name="open" lay-skin="switch" lay-filter="switchTest" lay-text="ON|OFF">
						    </div>
						  </div>
						  <div class="layui-form-item">
						    <label class="layui-form-label">快捷入口</label>
						    <div class="layui-input-block">
						      <input type="checkbox" checked="" name="open" lay-skin="switch" lay-filter="switchTest" lay-text="ON|OFF">
						    </div>
						  </div>
						<div class="layui-form-item" pane="">
						    <label class="layui-form-label">弹层显示区域</label>
						    <div class="layui-input-block">
						      <input type="radio" name="showArea" value="center" title="居中" checked="">
						      <input type="radio" name="showArea" value="right" title="居右">
						      <input type="radio" name="showArea" value="left" title="居左">
						    </div>
					    </div>
						
						<div class="form-group text-center mt-30">
							<div class="col-sm-2"></div>
							 <div class="col-sm-4 text-l">
						   		<button class="btn btn-sm btn-success"  type="button" id="submit-form" style="width: 80px;" onclick="_user_setting.set()"> 保存 </button>
							 </div>
					   </div>
				</div>
			</div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("_user_setting");

	_user_setting.init= function() {
        if (localStorage) {
            var data = JSON.parse(localStorage.getItem('_user_setting'));
            if (data) {
            	for (var i in data) {
            		$setVal(i,data[i],'settingform')
            	}
            }
        }
    };
    _user_setting.set= function() {
        if (localStorage) {
            var dataList = $('#settingform').serializeArray();
            var data = {}
            for (var i = 0; i < dataList.length; i++) {
                data[dataList[i].name] = dataList[i].value;
            }
            localStorage.setItem('_user_setting', JSON.stringify(data));
            layer.msg('修改成功。',{icon:1},function(){
            	popup.closeTab();
            })
        }
    }
    _user_setting.init();
    
    $(function(){
    	layui.use(['form'], function(){
    		  var form = layui.form;
        });
    });
    
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>