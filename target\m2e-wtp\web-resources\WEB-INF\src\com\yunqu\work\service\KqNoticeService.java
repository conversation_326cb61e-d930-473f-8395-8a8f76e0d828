package com.yunqu.work.service;

import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;

public class KqNoticeService extends AppBaseService implements Job{

	private static Set<String> kqPeoples = new HashSet<String>();

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME+"-kq";
	}
	
	private static class Holder{
		private static KqNoticeService service=new KqNoticeService();
	}
	
	public static KqNoticeService getService(){
		return Holder.service;
	}
	
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		 //this.sbDkNotice();
	}
	
	
	private static int today = 0;
	private static int todayDate = 0;
	
	/**
	 * true： 正常班  false：加班
	 * @return
	 */
	public boolean kqFlag() {
	    int currentDate = EasyCalendar.newInstance().getDateInt();
	    if (todayDate != currentDate) {
	        todayDate = currentDate;
	        boolean isNonWorkingDay = !Db.find("select 1 from yq_no_work_day where day = ? limit 1", currentDate).isEmpty();
	        today = isNonWorkingDay ? 1 : 2;
	    }
	    return today != 1;
	}

	
	//上班打卡提醒
	public  void sbDkNotice(){
		getLogger().info("执行早上打卡提醒....");
		if(!kqFlag()) {
			return;
		}
		try {
			int dateId = EasyCalendar.newInstance().getDateInt();
			List<JSONObject> list = this.getQuery().queryForList("select USER_ID,USER_NAME from yq_kq_obj where DK_DATE = ? and SIGN_IN = ''",new Object[]{dateId},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(creator);
					model.setSender(creator);
					model.setTitle("您好，您有一条考勤打卡提醒");
					model.setData1(jsonObject.getString("USER_NAME"));
					model.setData2(EasyDate.getCurrentDateString());
					model.setData3("记得打卡,外勤也要,请假提前走流程。");
					model.setDesc("上班时间为09:00，请别忘记打卡！");
					WxMsgService.getService().sendKqDkNotice(model);
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
	}

	//超过上班时间没打卡提前申请流程或补卡
	public  void sbDkTimeoutNotice(){
		getLogger().info("执行早上超时未打卡提醒....");
		if(!kqFlag()) {
			return;
		}
		try {
			int dateId = EasyCalendar.newInstance().getDateInt();
			List<JSONObject> list = this.getQuery().queryForList("select USER_ID,USER_NAME from yq_kq_obj where DK_DATE = ? and SIGN_IN = '' and REMARK = ''",new Object[]{dateId},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(creator);
					model.setSender(creator);
					model.setTitle("您好，您有一条考勤打卡提醒");
					model.setData1(jsonObject.getString("USER_NAME"));
					model.setData2(EasyDate.getCurrentDateString());
					model.setData3("记得打卡,外勤也要,请假提前走流程。");
					model.setDesc("上班时间为09:00，请别忘记打卡！");
					WxMsgService.getService().sendKqDkNotice(model);
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
	}
	
	//不打卡又不走流程提醒
	public  void notDkAndFlow(){
		getLogger().info("notDkAndFlow： 没有打卡又没走流程....");
		if(!kqFlag()) {
			return;
		}
		try {
			int dateId = DateUtils.getPlanMaxDay(-1);
			List<JSONObject> list = this.getQuery().queryForList("select USER_ID,USER_NAME,SIGN_IN,SIGN_OUT from yq_kq_obj where DK_DATE = ? and REMARK = ''",new Object[]{dateId},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(creator);
					model.setSender(creator);
					model.setTitle("昨日考勤异常提醒");
					model.setData1(jsonObject.getString("USER_NAME"));
					model.setData2(EasyDate.getCurrentDateString());
					String signIn = jsonObject.getString("SIGN_IN");
					String signOut = jsonObject.getString("SIGN_OUT");
					String msg = null;
					if(StringUtils.isBlank(signIn)&&StringUtils.isBlank(signOut)) {
						msg = "上下班都没打卡";
					}else if(StringUtils.isBlank(signIn)&&StringUtils.isNotBlank(signOut)) {
						msg = "上班未打卡";
					}else if(StringUtils.isNotBlank(signIn)&&StringUtils.isBlank(signOut)) {
						msg = "下班未打卡";	
					}
					if(msg!=null) {
						model.setData3("昨日考勤异常提醒："+msg);
						model.setUrl("https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/myKqRecord");
						if(hasKqPeople(creator)) {
							WxMsgService.getService().sendKqDkNotice(model);						
						}
					}
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
	}
	
	/**
	 * 合并用户的打卡异常的记录，然后拼接后发邮件
	 * @param sender 发送者
	 * @param monthId 月份ID,格式:YYYYMM
	 */
	public  void monthKqNotice(String sender,String monthId){
		getLogger().info("monthKqNotice： 合并用户的打卡异常的记录，然后拼接后发邮件....");
		try {
			if(StringUtils.isBlank(monthId)) {
				monthId = EasyCalendar.newInstance().getFullMonth();
			}
			
			if(kqPeoples.isEmpty()||kqPeoples.size()==0){
				initKqPeopleDataCache();
			}
			
			// 获取今天日期,用于过滤
			String today = String.valueOf(EasyCalendar.newInstance().getDateInt());
			
			// 查询考勤异常数据,排除今天的记录
			List<JSONObject> list = this.getQuery().queryForList(
				"select DK_DATE,USER_ID,USER_NAME,SIGN_IN,SIGN_OUT " +
				"from yq_kq_obj where MONTH_ID = ? and REMARK = '' " +
				"and (SIGN_IN ='' OR SIGN_OUT ='') " +
				"and DK_DATE < ? " +
				"ORDER BY USER_ID,DK_DATE",
				new Object[]{monthId, today},
				new JSONMapperImpl()
			);

			if(list!=null && !list.isEmpty()){
				getLogger().info("找到符合条件数据:"+list.size()+"条");
				
				StringBuffer sb = new StringBuffer();
				String currentUserId = null;
				String currentUserName = null;
				int index = 1;

				// 处理每条记录
				for(int i = 0; i < list.size(); i++) {
					JSONObject jsonObject = list.get(i);
					String userId = jsonObject.getString("USER_ID");
					String userName = jsonObject.getString("USER_NAME");
					String date = jsonObject.getString("DK_DATE");
					
					// 如果是新用户或最后一条记录
					boolean isNewUser = !userId.equals(currentUserId);
					boolean isLastRecord = (i == list.size()-1);
					
					// 如果是新用户,先处理上一个用户的数据
					if(isNewUser && currentUserId != null) {
						sendUserKqNotice(currentUserId, currentUserName, sb.toString(), sender, monthId);
						sb.setLength(0);
						index = 1;
					}
					
					// 更新当前用户信息
					currentUserId = userId;
					currentUserName = userName;
					
					// 处理异常记录
					String signIn = jsonObject.getString("SIGN_IN");
					String signOut = jsonObject.getString("SIGN_OUT");
					String msg = null;
					
					if(StringUtils.isBlank(signIn) && StringUtils.isBlank(signOut)) {
						msg = "上下班都没打卡";
					} else if(StringUtils.isBlank(signIn) && StringUtils.isNotBlank(signOut)) {
						msg = "上班未打卡";
					} else if(StringUtils.isNotBlank(signIn) && StringUtils.isBlank(signOut)) {
						msg = "下班未打卡";
					}
					
					if(msg != null) {
						sb.append(index).append(" / ").append(userName)
						  .append(" / ").append(date).append(" / ")
						  .append(msg).append("<br>");
						index++;
					}
					
					// 如果是最后一条记录,处理最后一个用户的数据
					if(isLastRecord && sb.length() > 0) {
						sendUserKqNotice(currentUserId, currentUserName, sb.toString(), sender, monthId);
					}
				}
			}
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
	}
	
	/**
	 * 发送用户考勤异常通知邮件
	 */
	private void sendUserKqNotice(String userId, String userName, String content, String sender, String monthId) {
		if(content.length() > 0 && hasKqPeople(userId)) {
			MessageModel model = new MessageModel();
			model.setReceiver(userId);
			model.setSender(sender);
			model.setTitle(monthId + "-考勤异常待处理");
			model.setDesc("  <p>如考勤异常(漏打卡，外勤，出差，请假)，请尽快提交对应流程。</p><br>" + content);
			EmailService.getService().sendEmail(model);
			getLogger().info("发送邮件给:" + userName + ",内容:" + content);
		}else{
			getLogger().error("发送邮件给:" + userName + ",内容:" + content);
		}
	}
	
	public  void xbDkNotice(){
		getLogger().info("执行下班打卡提醒....");
		if(!kqFlag()) {
			return;
		}
		try {
			int dateId = EasyCalendar.newInstance().getDateInt();
			List<JSONObject> list = this.getQuery().queryForList("select b1.USER_ID,b1.USER_NAME from YQ_KQ_OBJ b1 where b1.DK_DATE = ? and b1.SIGN_OUT = ''",new Object[]{dateId},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator = jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(creator);
					model.setSender(creator);
					model.setTitle("您好，您有一条考勤打卡提醒");
					model.setData1(jsonObject.getString("USER_NAME"));
					model.setData2(EasyDate.getCurrentDateString());
					model.setData3("下班未打卡");
					model.setDesc("已经下班很久了，请别忘记打卡！");
					WxMsgService.getService().sendKqDkNotice(model);
				}
			}
		} catch (SQLException ex) {
			this.getLogger().error(ex);
		}
	}
	
	public String getZaoanWord() {
		String sql = "select * from yq_ai_sites order by RAND() limit 1";
		try {
			JSONObject row = this.getQuery().queryForRow(sql, new Object[]{},new JSONMapperImpl());
			String desc = row.getString("DESC");
			if(StringUtils.notBlank(desc)&&desc.length()>80) {
				desc = desc.substring(0,80)+"...";
			}
			return "<a target=\"_blank\" href=\""+row.getString("URL")+"\">"+row.getString("TITLE")+"-"+desc+"</a>";
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		return "豆包大模型 - 字节跳动推出的AI大模型家族，包括视频生成、语音视觉、通用语言模型等";
	}
	
	public static Map<String,String> cityMap = new HashMap<String, String>();
	private static int tqTodayDate = 0;
	
	public String getTq(String city) {
		if(StringUtils.isBlank(city)) {
			return "无";
		}
		int _todayDate = EasyCalendar.newInstance().getDateInt();
		if(tqTodayDate!=_todayDate){
			tqTodayDate = _todayDate;
			cityMap.clear();
		}
		String todayTq = cityMap.get(city);
		if(StringUtils.notBlank(todayTq)) {
			return todayTq;
		}
		//https://www.seniverse.com/products?iid=fc96f6fd-2da2-4fde-ae0d-5fb9b37d3cf0
		Connection connection =  Jsoup.connect("https://api.seniverse.com/v3/weather/daily.json");
		connection.data("key","SXN5v39ZWpXeNR6bX");
		connection.data("location",city);
		connection.data("start","1");
		connection.data("days","1");
		try {
			Response response = connection.ignoreContentType(true).timeout(5000).method(Method.GET).execute();
			String str = response.body();
			JSONObject json = JSONObject.parseObject(str);
			if(!json.containsKey("results")) {
				cityMap.put(city, "无");
				return "无";
			}
			JSONArray results = json.getJSONArray("results");
			JSONObject row = results.getJSONObject(0);
			JSONArray daily = row.getJSONArray("daily");
			JSONObject detail = daily.getJSONObject(0);
			String tq = city+"明天天气："+detail.getString("text_day")+" "+detail.getString("low")+"°~"+detail.getString("high")+"°";
			cityMap.put(city, tq);
			return tq;
		} catch (IOException e) {
			this.getLogger().error(e);
		}
		return "无";
		
	}
	
	
	public  void addKqRecord(String dateId){
		getLogger().info("执行新增考勤记录....");
		String _dateId = String.valueOf(EasyCalendar.newInstance().getDateInt());
		String monthId = String.valueOf(EasyCalendar.newInstance().getFullMonth());
		if(StringUtils.notBlank(dateId)) {
			_dateId  = dateId;
			monthId = dateId.substring(0,6);
		}
		
		List<JSONObject> list = null;
		try {
			list = this.getQuery().queryForList("select t1.kq_user_id,t1.kq_user_name,t1.kq_city from yq_kq_people t1 where t1.state = ?",new Object[]{0},new JSONMapperImpl());
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		if(list!=null&&list.size()>0){
			for(JSONObject row:list){
				String kqUserId = row.getString("KQ_USER_ID");
				StaffModel model = StaffService.getService().getStaffInfo(kqUserId);
				if(model!=null&&model.isNormal()) {
					EasyRecord record = new EasyRecord("YQ_KQ_OBJ","DK_DATE","USER_ID");
					record.set("KQ_ID",RandomKit.uuid());
					record.set("USER_ID",kqUserId);
					record.set("STAFF_ID",model.getStaffNo());
					record.set("CREATE_TIME",EasyDate.getCurrentDateString() );
					record.set("USER_NAME",model.getUserName());
					record.set("DEPT_NAME",model.getDeptName());
					record.set("DEPT_ID",model.getDeptId());
					record.set("KQ_CITY",row.getString("KQ_CITY"));
					record.set("NOT_WORK","Y");
					record.set("DK_DATE", _dateId);
					record.set("DATE_ID", _dateId);
					record.set("MONTH_ID", monthId);
					try {
						this.getQuery().save(record);
					} catch (SQLException ex) {
						this.getLogger().error(ex);
					}
				}
			}
		}
		
	}

	public void addLeaveOnKq(String userId,String dateId){
		 String date = dateId != null ? dateId.substring(0,4) + "-" + dateId.substring(4,6) + "-" + dateId.substring(6,8) : EasyDate.getCurrentDateString("yyyy-MM-dd");
		 String dateBegin = date + " 09:00";
		 String dateEnd = date + " 18:00";
		 addLeaveOnKqByDate(userId,null,dateBegin, dateEnd);
	}
   
   public void addLeaveOnKq(String dateId){
	   addLeaveOnKq(null,dateId);
   }
   

   private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

   public static void iterateCurrentMonth(String beginDateStr, String endDateStr, Consumer<String> consumer) {
       LocalDate beginDate = LocalDateTime.parse(beginDateStr, FORMATTER).toLocalDate();
       LocalDate endDate = LocalDateTime.parse(endDateStr, FORMATTER).toLocalDate();

       LocalDate now = LocalDate.now();
       LocalDate lastDayOfMonth = now.withDayOfMonth(now.lengthOfMonth());

       // beginDate 保持不变，endDate 最大遍历到本月最后一天
       if (endDate.isAfter(lastDayOfMonth)) {
           endDate = lastDayOfMonth;
       }
       if (beginDate.isAfter(endDate)) return;

       LocalDate current = beginDate;
       DateTimeFormatter dayOnlyFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
       while (!current.isAfter(endDate)) {
           consumer.accept(current.format(dayOnlyFormatter));
           current = current.plusDays(1);
       }
   }

   
   public void addLeaveOnKqByDate(String userId,String applyId,String beginDateId,String endDateId){
	   this.getLogger().info("执行添加请假/外勤信息到考勤记录....userId:"+userId+",applyId:"+applyId+",beginDateId:"+beginDateId+",endDateId:"+endDateId);
	   List<JSONObject> list = null;
	   try {
			//包含今日的请假,请假开始时间<今天18:00；请假结束时间>今天09:00
			EasySQL sql = new EasySQL();
			sql.append("SELECT t1.APPLY_ID,t1.FLOW_CODE,t1.APPLY_STATE,t1.APPLY_NO,t1.DATA11,t1.DATA12,t1.DATA13,t1.DATA14,t1.APPLY_BY,APPLY_REMARK FROM YQ_FLOW_APPLY t1 WHERE FLOW_CODE IN ('ha_leave','ha_travel')");
			if(StringUtils.isBlank(applyId)) {
				if(StringUtils.isBlank(userId)&&StringUtils.isBlank(beginDateId)) {
					return;
				}
				sql.append(beginDateId,"AND t1.DATA12 >= ? ");
				sql.append(endDateId,"AND t1.DATA11 <= ?");
				sql.append(userId,"AND t1.APPLY_BY = ?");
			}else {
				sql.append(applyId,"AND t1.APPLY_ID = ?");
			}
			list = this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		
		if(kqPeoples.isEmpty()||kqPeoples.size()==0){
			initKqPeopleDataCache();
		}
		
		if(list!=null&& !list.isEmpty()){
			for(JSONObject row:list){
				String kqUserId = row.getString("APPLY_BY");
				String flowCode = row.getString("FLOW_CODE");
				String beiginDate = row.getString("DATA11");
				String applyNo = row.getString("APPLY_NO");	
				String endDate = row.getString("DATA12");
				int applyState = row.getIntValue("APPLY_STATE");
				this.getLogger().info(applyNo+">beiginDate:"+beiginDate+","+endDate);
				if(kqPeoples.contains(kqUserId)){			
					if(StringUtils.isBlank(beiginDate) || StringUtils.isBlank(endDate)) {
						continue;
					}
				    iterateCurrentMonth(beiginDate, endDate, dateStr -> {
						try {
							this.getLogger().info("遍历日期>dateStr:"+dateStr+">applyNo:"+applyNo);
							EasyRecord record = new EasyRecord("YQ_KQ_OBJ","DK_DATE","USER_ID");
							record.set("DK_DATE", dateStr.replaceAll("-",""));
							record.set("USER_ID",kqUserId);
							record.set("APPLY_ID",row.getString("APPLY_ID"));
							record.set("APPLY_NO",applyNo);
							record.set("APPLY_STATE",applyState);
							record.set("KQ_TYPE",flowCode);
							record.set("NOT_WORK","N");
							record.set("APPLY_REMARK",row.getString("APPLY_REMARK"));
							record.set("REMARK",row.getString("DATA14"));
							if(applyState==10||applyState==20||applyState==30) {
								this.getQuery().update(record);
							}else {
								record.set("REMARK","");
								record.set("APPLY_NO","");
								this.getQuery().update(record);
							}
						} catch (SQLException ex) {
							this.getLogger().error("添加请假/外勤信息到考勤记录失败",ex);
					  }
				   });
				}
			}
		}
   }

	public void deptKqData() {
		deptKqData(EasyCalendar.newInstance().getFullMonth());
		deptKqDataNew(EasyCalendar.newInstance().getFullMonth());
	}
	
	
	/**
	 * 本月部门考勤数据
	 */
	public void deptKqData(String monthId) {
	   if(!kqFlag()) {
			return;
	   }
	   if(StringUtils.isBlank(monthId)) {
		   monthId = EasyCalendar.newInstance().getFullMonth();
	   }
	   EasySQL sql = new EasySQL();
	   sql.append(monthId,"SELECT DEPT_ID,DEPT from yq_kq_data where MONTH_ID = ? GROUP BY DEPT_ID");
	   try {
			List<JSONObject> list =  this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(list!=null) {
				for(JSONObject row:list) {
			    	String deptId = row.getString("DEPT_ID");
			    	if(StringUtils.isBlank(deptId)) {
			    		continue;
			    	}
			    	EasySQL deptSql = new EasySQL();
			    	deptSql.append("SELECT USER_NAME,FLOOR(SUM(TIME_TO_SEC(OVER_TIME)) / 60) AS total_minutes,count(1) count from yq_kq_data where 1=1");
			    	deptSql.append(deptId,"and DEPT_ID = ?");
			    	deptSql.append(monthId,"and MONTH_ID = ?");
			    	deptSql.append("and OVER_TIME <>'' GROUP BY USER_ID HAVING count > 2 ORDER BY total_minutes desc");
			    	List<JSONObject> deptUserList =  this.getQuery().queryForList(deptSql.getSQL(),deptSql.getParams(), new JSONMapperImpl());
			    	StringBuffer sb = new StringBuffer();
			    	int i = 0;
			    	for(JSONObject user:deptUserList) {
			    		i++;
			    		sb.append(i+"."+user.getString("USER_NAME")+",加班分钟:"+user.getString("TOTAL_MINUTES")+",次数:"+user.getIntValue("COUNT")+"\n");
			    		if(i>=10) {
//			    			break;
			    		}
			    	}
			    	if(i>=2) {
			    		WeChatWebhookSender.sendMarkdownMessage("dept@"+deptId, monthId+"-"+row.getString("DEPT")+"加班时间数据", sb.toString(),true);
			    		if(ServerContext.isLinux()) {
			    			WeChatWebhookSender.sendMarkdownMessage(WebhookKey.TEST, monthId+"-"+row.getString("DEPT")+"加班时间数据", sb.toString());
			    		}
			    	}
				}
			}
	   } catch (SQLException e) {
		   this.getLogger().error(e);
	  }
	}

	public void deptKqDataNew(String monthId) {
		if(!kqFlag()) {
			 return;
		}
		if(StringUtils.isBlank(monthId)) {
			monthId = EasyCalendar.newInstance().getFullMonth();
		}
		EasySQL sql = new EasySQL();
		sql.append(monthId,"SELECT DEPT_ID,DEPT_NAME DEPT from yq_kq_obj where MONTH_ID = ? GROUP BY DEPT_ID");
		try {
			 List<JSONObject> list =  this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			 if(list!=null) {
				 for(JSONObject row:list) {
					 String deptId = row.getString("DEPT_ID");
					 if(StringUtils.isBlank(deptId)) {
						 continue;
					 }
					 EasySQL deptSql = new EasySQL();
					 deptSql.append("SELECT USER_NAME,sum(OVER_TIME) AS total_minutes,count(1) count from yq_kq_obj where 1=1");
					 deptSql.append(deptId,"and DEPT_ID = ?");
					 deptSql.append(monthId,"and MONTH_ID = ?");
					 deptSql.append("and OVER_TIME <>'' and REMARK = '' GROUP BY USER_ID HAVING count > 2 ORDER BY total_minutes desc");
					 List<JSONObject> deptUserList =  this.getQuery().queryForList(deptSql.getSQL(),deptSql.getParams(), new JSONMapperImpl());
					 StringBuffer sb = new StringBuffer();
					 int i = 0;
					 for(JSONObject user:deptUserList) {
						 i++;
						 sb.append(i+"."+user.getString("USER_NAME")+",加班分钟:"+user.getString("TOTAL_MINUTES")+",次数:"+user.getIntValue("COUNT")+"\n");
						 if(i>=10) {
 //			    			break;
						 }
					 }
					 if(i>=2) {
						 WeChatWebhookSender.sendMarkdownMessage("dept@"+deptId, monthId+"-"+row.getString("DEPT")+"加班时间数据", sb.toString(),true);
						 if(ServerContext.isLinux()) {
							 WeChatWebhookSender.sendMarkdownMessage(WebhookKey.TEST, monthId+"-"+row.getString("DEPT")+"加班时间数据", sb.toString());
						 }
					 }
				 }
			 }
		} catch (SQLException e) {
			this.getLogger().error(e);
	   }
	 }
	

	public void updateKqPeopleData() {
		try {
			this.getQuery().executeUpdate("DELETE FROM yq_kq_people WHERE kq_user_id IN( SELECT user_id FROM yq_main.easi_user WHERE state != 0)",new Object[] {});
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		try {
			this.getQuery().executeUpdate("INSERT INTO yq_kq_people( kq_user_id, kq_user_name, dept_name, sb_time, xb_time, kq_address, kq_location, state, join_date, leave_date, remark,kq_city) SELECT t1.USER_ID, t1.USERNAME, t1.depts dept_name, '09:00', '18:00', '', '', 1, '', '', '',t2.often_work_city FROM yq_main.easi_user t1 LEFT JOIN yq_kq_people t3 ON t1.USER_ID = t3.kq_user_id LEFT JOIN yq_main.yq_staff_info t2 ON t1.USER_ID = t2.staff_user_id WHERE t3.kq_user_id IS NULL and t1.STATE = 0 ",new Object[] {});
		} catch (SQLException e) {
			this.getLogger().error(e);
		}

		try {
			this.getQuery().executeUpdate("UPDATE yq_kq_obj set NOT_WORK = 'N' where DK_DATE >= ?",new Object[] {DateUtils.getPlanMaxDay(-31)});
			this.getQuery().executeUpdate("UPDATE yq_kq_obj set NOT_WORK = 'Y' where DK_DATE >= ? and SIGN_IN='' and SIGN_OUT='' and REMARK=''",new Object[] {DateUtils.getPlanMaxDay(-31)});
		} catch (SQLException e) {
			this.getLogger().error(e);
		}


		try {
			this.getQuery().executeUpdate("UPDATE yq_kq_obj t1,yq_main.easi_dept_user t2 set t1.DEPT_ID = t2.DEPT_ID where t1.USER_ID = t2.USER_ID and t1.MONTH_ID = ?",new Object[] {EasyCalendar.newInstance().getFullMonth()});
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		try {
			this.getQuery().executeUpdate("UPDATE yq_kq_obj t1,yq_flow_apply t2 set t1.APPLY_STATE = t2.APPLY_STATE where t1.APPLY_ID = t2.APPLY_ID and t1.DK_DATE>= ?",new Object[] {EasyCalendar.newInstance().getFullMonth()+"01"});
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
		removeKqPeopleDataCache();
		initKqPeopleDataCache();

	}

	public void removeKqPeopleDataCache() {
		kqPeoples.clear();
	}
	
	public void initKqPeopleDataCache() {
		try {
			List<JSONObject> kqPeopleList = this.getQuery().queryForList("select kq_user_id from yq_kq_people t1 where  t1.state = 0",new Object[] {},new JSONMapperImpl());
			if(kqPeopleList!=null) {
			for(JSONObject row:kqPeopleList) {
				kqPeoples.add(row.getString("KQ_USER_ID"));
			}
		  }
		} catch (SQLException e) {
			this.getLogger().error(e);
		}
	}

	public boolean hasKqPeople(String userId) {
		return kqPeoples.contains(userId);
	}
}
