function loadInvoiceTitleList(){
		$("#custDetailForm").initTable({
			mars:'InvoiceDao.titleList',
			id:'invoiceTitleList',
			page:false,
			cols: [[
             {
           	 	type: 'numbers',
				title: '序号',
				align:'left'
			 },{
			    field: 'TITLE_NAME',
				title: '发票抬头',
				align:'left'
			},{
			    field: 'TAX_NO',
				title: '税号',
				align:'center'
			},{
			    field: 'BANK_NAME',
				title: '开户银行',
				align:'left'
			},{
			    field: 'BANK_NO',
				title: '银行账户',
				align:'center'
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="editInvoiceTitle(\'{{d.TITLE_ID}}\')">编辑</a></div>'
			}
		]],done:function(){
			
	  }});
	}
function loadBusinessList(){
		$("#custDetailForm").initTable({
			mars:'CustDao.businessList',
			id:'businessList',
			page:false,
			cols: [[
             {
           	 	type: 'numbers',
				title: '序号',
				align:'left'
			 },{
			    field: 'BUSINESS_NAME',
				title: '商机名称',
				align:'left'
			},{
			    field: 'AMOUNT',
				title: '预计金额',
				align:'left'
			},{
				field:'POSSIBILITY',
				title:'可行性',
				templet:'<div>{{d.POSSIBILITY}}%</div>'
			},{
			    field: 'REMARK',
				title: '描述',
				align:'left'
			},{
			    field: 'CREATE_TIME',
				title: '创建时间',
				align:'left'
			},{
			    field: 'SALES_BY',
				title: '销售',
				align:'center',
				templet:function(row){
					return getUserName(row.SALES_BY);
				}
			},{
	        	field:'',
	        	title:'操作',
	        	width:60,
	        	templet:'<div><a href="javascript:;" onclick="businessDetail(\'{{d.BUSINESS_ID}}\',\'{{d.CUST_ID}}\',\'{{d.CUSTOMER_NAME}}\')">查看</a></div>'
	        }
		]],done:function(res){
			$("#businessCount").text("("+res.total+")");
	  }});
	}
	function loadInvoiceList(){
		$("#custDetailForm").initTable({
			mars:'InvoiceDao.list',
			id:'invoiceList',
			cols: [[
             {
           	 	type: 'numbers',
				title: '序号',
				align:'left'
			 },{
			    field: 'INVOICE_NO',
				title: '编号',
				align:'left'
			},{
			    field: 'MARK_MONEY',
				title: '开票金额'
			},{
			    field: 'MARK_DATE',
				title: '开票日期'
			},{
			    field: 'INVOICE_TYPE',
				title: '发票类型'
			},{
			    field: 'REMARK',
				title: '备注',
				align:'center'
			}
		]],done:function(){
			
	  }});
	}
	
	function loadContactsList(){
		$("#custDetailForm").initTable({
			mars:'ContactsDao.list',
			id:'contactsList',
			page:false,
			cols: [[
           {
         	 	type: 'numbers',
				title: '序号',
				align:'left'
			 },{
			    field: 'NAME',
				title: '姓名',
				align:'left',
				templet:'<div><a href="javascript:;" onclick="editContact(\'{{d.CONTACTS_ID}}\',0)">{{d.NAME}}</a></div>'
				
			},{
			    field: 'MOBILE',
				title: '手机号',
				align:'left'
			},{
			    field: 'EMAIL',
				title: '邮箱',
				align:'left'
			},{
			    field: 'POST',
				title: '职务',
				align:'left'
			},{
			    field: 'ROLE',
				title: '角色',
				align:'left'
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="editContact(\'{{d.CONTACTS_ID}}\',1)">编辑</a></div>'
			}
		]],done:function(res){
			$("#contactsCount").text("("+res.total+")");
	  }});
	}
	function loadBuyGoodsList(){
		$("#custDetailForm").initTable({
			mars:'ApplyDao.custList',
			id:'buyGoodsList',
			page:false,
			cols: [[
           {
         	 	type: 'numbers',
				title: '序号',
				align:'left'
			 },{
			    field: 'APPLY_NO',
				title: '单据号',
				align:'left'
			},{
			    field: 'CREATE_TIME',
				title: '发起时间',
				align:'left'
			},{
			    field: 'APPLY_STATE',
				title: '状态',
				align:'left',
				templet:function(row){
					return '采购评审';
				}
			},{
				field:'',
				title:'操作',
				width:60,
				templet:'<div><a href="javascript:;" onclick="purchaseDetail(\'{{d.APPLY_ID}}\',1)">详情</a></div>'
			}
		]],done:function(res){
			$("#contractCount").text("("+res.total+")");
	  }});
	}
	
	
	function loadTeamList(){
		$("#custDetailForm").initTable({
			mars:'CustDao.teamList',
			id:'teamList',
			page:false,
			cols: [[
		      {
		        	type: 'numbers',
		        	title: '序号',
		        	align:'left'
		        },{
		        	field: 'USERNAME',
		        	title: '姓名'
		        		
		        },{
		        	field: 'RW_AUTH',
		        	title: '读写权限',
		        	align:'left',
		        	templet:function(row){
		        		var auth = row['RW_AUTH'];
		        		if(auth=='0'){
		        			return '负责人';
		        		}else if(auth=='1'){
		        			return '只读';
		        		}else{
		        			return '读写';
		        		}
		        	}
	        },{
	        	field: 'HT_AUTH',
	        	title: '合同权限',
	        	align:'left',
	        	templet:function(row){
	        		var auth = row['HT_AUTH'];
	        		if(auth=='1'){
	        			return '√';
	        		}else{
	        			return '×';
	        		}
	        	}
	        },{
	        	field: 'SJ_AUTH',
	        	title: '商机权限',
	        	align:'left',
	        	templet:function(row){
	        		var auth = row['SJ_AUTH'];
	        		if(auth=='1'){
	        			return '√';
	        		}else{
	        			return '×';
	        		}
	        	}
	        },{
	        	field:'',
	        	title:'操作',
	        	width:60,
	        	templet:'<div><a href="javascript:;" onclick="removeTeamUser(\'{{d.CUST_ID}}\',\'{{d.USER_ID}}\')">移除</a></div>'
	        }
	        ]],done:function(res){
	        	$("#teamCount").text("("+res.total+")");
	        }});
	}
	function loadContractList(){
		$("#custDetailForm").initTable({
			mars:'ProjectContractDao.custContractList',
			id:'contractList',
			page:true,
			cols: [[
		    {
	        	type: 'numbers',
	        	title: '序号',
	        	align:'left'
	        },{
	        	field: 'CONTRACT_NAME',
	        	title: '合同名称'
	        },{
	        	field: 'CONTRACT_NO',
	        	title: '合同号',
        		width:100
	        },{
	        	field: 'CREATE_TIME',
	        	title: '创建时间',
	        	width:120
	        },{
	        	field:'',
	        	title:'操作',
	        	width:60,
	        	templet:'<div><a href="javascript:;" onclick="contractDetail(\'{{d.CONTRACT_ID}}\')">查看</a></div>'
	        }
	        ]],done:function(res){
				 $("#contractCount").text("("+res.totalRow+")");
	        }});
	}
	function loadReviewList(){
		$("#custDetailForm").initTable({
			mars:'ReviewDao.custReview',
			id:'reviewList',
			page:false,
			cols: [[
		    {
	        	type: 'numbers',
	        	title: '序号',
	        	align:'left'
	        },{
	        	field: 'CONTRACT_NAME',
	        	title: '合同名称'
	        },{
	        	field: 'REVIEW_NO',
	        	title: '评审编号',
        		width:130
	        },{
	        	field: 'CREATE_TIME',
	        	title: '发起时间',
	        	width:140
	        },{
	        	field:'',
	        	title:'操作',
	        	width:60,
	        	templet:'<div><a href="javascript:;" onclick="reviewDetail(\'{{d.REVIEW_ID}}\')">查看</a></div>'
	        }
	        ]],done:function(res){
				 $("#reviewCount").text("("+res.total+")");
	        }});
	}
	function loadFileList(){
		$("#custDetailForm").initTable({
			mars:'FileDao.fileListDetail',
			id:'fileList',
			page:false,
			cols: [[
	        {
	        	type: 'numbers',
	        	title: '序号',
	        	align:'left'
	        },{
	        	field: 'FILE_NAME',
	        	title: '文件名',
				templet:'<div><a target="_blank" href="'+ctxPath+'/fileview/{{d.FILE_ID}}?filename={{d.FILE_NAME}}&view=online">{{d.FILE_NAME}}</a></div>'
	        },{
	        	field: 'FILE_SIZE',
	        	title: '文件大小 ',
	        	width:80
	        },{
	        	field: 'USERNAME',
	        	title: '上传人',
	        	width:80
	        },{
	        	field: 'CREATE_TIME',
	        	title: '上传时间',
	        	width:130
	        },{
	        	field:'',
	        	title:'操作',
	        	width:60,
	        	templet:'<div><a href="'+ctxPath+'/fileview/{{d.FILE_ID}}?filename={{d.FILE_NAME}}" title="点击查看" target="_blank">查看</a></div>'
	        }
	        ]],done:function(res){
	        	$("#fileCount").text("("+res.total+")");
	        }});
	}
  