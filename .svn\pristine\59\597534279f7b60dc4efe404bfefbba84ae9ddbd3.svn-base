<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>采购合同评审管理</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">评审编号</span>
						 	<input class="form-control input-sm" name="reviewNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">订单号</span>
						 	<input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">销售合同号</span>
						 	<input class="form-control input-sm" name="contractNo">
						 </div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">评审状态</span>	
							<select name="reviewState" onchange="queryData();" class="form-control input-sm">
							      <option value="">请选择 </option>
                    			  <option data-class="label label-warning" value="0">草稿</option>
							      <option data-class="label label-warning" value="10">待审批</option>
                    			  <option data-class="label label-success" value="20">审批中</option>
                    			  <option data-class="label label-danger" value="21">审批退回</option>
                    			  <option data-class="label label-danger" value="30">已完成</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="followTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>
		  </script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'OrderFlowDao.reviewList',
					id:'followTable',
					page:true,
					rowDoubleEvent:'orderDetail',
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 field:'REVIEW_NO',
						 title:'评审编号',
						 width:100
					 },{
						 field:'ORDER_NO',
						 title:'订单编号',
						 width:100
					 },{
						 field:'CONTRACT_NAME',
						 title:'标题',
						 minWidth:180
					 },{
						 field:'SUPPLIER_NAME',
						 title:'供应商',
						 minWidth:180
					 },{
						field:'REVIEW_STATE',
						title:'当前状态',
						width:80,
						templet:function(row){
							var val = row['REVIEW_STATE'];
							return getText(val,'reviewState');
						}
					},{
						field:'NODE_NAME',
						title:'当前节点'
					},{
						field:'CHECK_BY',
						title:'当前审批人'
					},{
						field:'REMARK',
						title:'备注',
						edit:'text'
					},{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center'
					},{
					    field: 'CREATE_NAME',
						title: '申请者',
						width:100,
						align:'center'
					},{
					    field: 'TOTAL_PRICE',
						title: '总金额',
						width:100,
						align:'center'
					},{
						field:'',
						title:'操作',
						width:50,
						fixed:'right',
						event:'orderDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function orderDetail(data){
			var nodeId = data['NODE_ID'];
			var resultId = data['RESULT_ID'];
			var reviewId = data['REVIEW_ID'];
			var orderId = data['ORDER_ID'];
			var reviewState = data['REVIEW_STATE'];
			var data = {reviewId:reviewId,nodeId:nodeId,orderId:orderId,custId:data.CUST_ID,resultId:resultId,reviewState:reviewState,applyBy:data['APPLY_BY']};
			popup.openTab({id:'orderDetail',title:'评审详情',url:'${ctxPath}/pages/erp/order/order-check.jsp',data:data});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'followTable'});
		}
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>