<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>上传发票</title>
	<style>
		.form-horizontal{width: 100%;}
		.uploadMsg{line-height: 30px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="InvoiceEditForm" class="form-horizontal" autocomplete="off">
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
			            	<td style="text-align: center;font-size: 14px;">
			            		请选择 PDF发票文件<br>如<b>发票号</b>识别异常请联系管理员，其他字段识别有误不影响。
			            		<div class="uploadMsg"></div>
			            	</td>
			            </tr>
			            <tr>
			            	<td>
			            			<div class="layui-upload-drag" style="display: block;height: 170px;" id="upload-drag">
									  	<i class="layui-icon layui-icon-upload"></i> 
									  	<div>点击上传，或将文件拖拽到此处(支持文件多选)</div>
									</div>
			            	</td>
			            </tr>
			        </tbody>
 				</table>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
	    var msgArray = [];
		$(function(){
				$('#InvoiceEditForm').render({success:function(){}});
				layui.use('upload', function(){
				 	  var upload = layui.upload;
					  var uploadInst = upload.render({
					    elem: '#upload-drag'
					    ,url: '/yq-work/servlet/upload?action=index'
					    ,accept: 'file'
					    ,exts:'pdf'
					    ,size:1024*20
						,multiple:true
						,number:50
					    ,field: 'file'
					    ,data:{
					    	source:function(){
								return 'bxInvoice';
							},
							fkFlag:1
					    },done: function(result, index, upload){
							var item = this.item;
						    if(result.state==1){
						    	layer.msg(result.msg,{icon: 1,time:800},function(){
									callback(result.data,item);
								}); 
							}else{
								 layer.closeAll('dialog');
								 layer.closeAll('loading');
								 layer.alert(result.msg,{icon: 5});
							}
					    },choose:function(obj){
					    	
					    },before:function(obj){
					    	$('.uploadMsg').html('');
					    	msgArray = [];
					    },allDone: function(obj){
					    	$('.uploadMsg').html(msgArray.join('<br>'));
					    	layer.msg("如识别有误，请自行修改信息。",{icon: 7});
						}
					    ,error: function(res, index, upload){
					    	layer.close(loadIndex);
					    	layer.alert("上传文件请求异常！",{icon: 5});
					   }
			    });
			  });
			
		});
	
	 function callback(data){
		 data['invoiceId'] = data.id;
		 ajax.remoteCall('${ctxPath}/servlet/bx/invoice?action=index',data,function(result){
				if(result.state == 1){
					msgArray.push("上传成功>"+result.data.name);
					layer.msg('上传成功，可继续再点击上传。',{icon : 1, time : 1500},function(){
						reloadInvoiceList();
					});
				}else{
					layer.msg(result.msg,{icon : 7, time : 800},function(){
						ajax.remoteCall("/yq-work/servlet/upload?action=del",{id:data.id},function(result) { 
							msgArray.push("上传失败>"+result.data.name);
							layer.msg('已自动删除重复上传发票');
						});
					});
				}
		}); 
	 }
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>