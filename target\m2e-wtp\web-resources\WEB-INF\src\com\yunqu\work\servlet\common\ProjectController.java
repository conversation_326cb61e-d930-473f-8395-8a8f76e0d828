package com.yunqu.work.servlet.common;

import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.CookieKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.core.ActionKey;
import com.jfinal.core.Path;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.service.LookLogService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.utils.LogUtils;
import com.yunqu.work.utils.ZendaoApi;


@Before(AuthInterceptor.class)
@Path(value = "/project")
public class ProjectController extends BaseController {

	private static Map<String,Long> projectAccess = new ConcurrentHashMap<String,Long>();
	public void index() {
		String projectId = getPara();
		if(StringUtils.isBlank(projectId)) {
			projectId = getPara("projectId");
		}
		if(StringUtils.isBlank(projectId)) {
			renderHtml("projectId is null");
			return;
		}
		boolean isProjectNo = false;
		Record record = null;
		if(projectId.length()==13&&!"0".equals(projectId)) {
			record = Db.findFirst("select * from yq_project where project_no = ?", projectId);
			if(record!=null) {
				projectId = record.getStr("project_id");
			}else {
				isProjectNo = true;
			}
		}
		if(!isProjectNo) {
			record = Db.findFirst("select * from yq_project where project_id = ?", projectId);
		}
		if(record==null) {
			renderHtml("url不正确");
			return;
		}
		int isAdmin = 0;
		
		Record team = Db.findFirst("select * from YQ_PROJECT_TEAM where USER_ID = ? and PROJECT_ID = ?",getUserId(),projectId);
		if(team!=null) {
			isAdmin = team.getInt("ADMIN_FLAG");
			try {
				this.getQuery().executeUpdate("update YQ_PROJECT_TEAM set ACCESS_TIME = ? where USER_ID = ? and PROJECT_ID = ?", EasyDate.getCurrentDateString(),getUserId(),projectId);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		
		String projectPo = record.get("project_po");
		String po = record.get("po");
		if(StringUtils.notBlank(projectPo)&&projectPo.contains(getUserId())) {
			isAdmin = 1;
		}
		if(StringUtils.notBlank(po)&&po.contains(getUserId())) {
			isAdmin = 1;
		}
		
		int projectPublic = record.getInt("project_public");
		if(projectPublic==1&&isAdmin==0) {
			if(team==null&&!hasRole("PROJECT_MANAGER")) {
				renderHtml("您无权访问、请联系项目管理员");
				return;
			}
		}
		if(hasRole("PROJECT_MANAGER")||isSuperUser()) {
			isAdmin = 1;
		}
		if(("9999999999999".equals(projectId)||"0".equals(projectId))&&isAdmin!=1) {
			renderHtml("您无权访问、请联系项目管理员");
			return;
		}
		String projectNo = record.getStr("project_no");
		setAttr("isAdmin", isAdmin);
		setAttr("isProjectMgr",hasRole("PROJECT_MANAGER")?1:0);
		setAttr("projectInfo", record);
		setAttr("projectId", projectId);
		setAttr("projectNo", projectNo);
		if("0".equals(projectNo)) {
			setAttr("projectName", record.getStr("project_name"));
		}else {
			setAttr("projectName", projectNo+""+record.getStr("project_name"));
		}
		String accessKey = getUserId() + projectId;
		long currentTime = System.currentTimeMillis();
		// 检查项目访问记录是否存在，或者访问时间是否超过5分钟
		if (!projectAccess.containsKey(accessKey) || currentTime - projectAccess.get(accessKey) > 5 * 60 * 1000) {
		    projectAccess.put(accessKey, currentTime);
		    LookLogService.getService().addLog(getUserId(), projectId, "project", getRequest());
		}
		render("/pages/project/portal/index.jsp?isDiv=0&projectId="+projectId);
	}
	
	
	public void follow() {
		String id = getPara();
		renderJsp("/pages/project/module/action.jsp?followId="+id);
	}
	
	public void projectExcel() {
		renderJsp("/pages/project/project-excel.jsp");
	}
	
	public void projectIndex() {
		renderJsp("/pages/project/project-index.jsp");
	}
	
	public void projectList() {
		setAttr("isProjectMgr",hasRole("PROJECT_MANAGER")?1:0);
		renderJsp("/pages/project/project-list.jsp");
	}
	
	public void myProjectList() {
		renderJsp("/pages/project/my/my-manger.jsp");
	}
	
	public void contract() {
		String id = getPara();
		if(StringUtils.isBlank(id)) {
			id = getPara("contractId");
		}
		if(StringUtils.isBlank(id)) {
			renderHtml("链接不存在，请联系管理员");
			return;
		}
		int isAdmin = 0;
		String projectId = null;
		try {
			JSONObject result = this.getQuery().queryForRow("select * from yq_project_contract where contract_id  = ?",new Object[] {id}, new JSONMapperImpl());
			if(result!=null) {
				projectId = result.getString("PROJECT_ID");
			}
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
		if(projectId!=null) {
			try {
				JSONObject result = this.getQuery().queryForRow("select PROJECT_PO,PO,DEPT_ID from yq_project where project_id  = ?",new Object[] {projectId}, new JSONMapperImpl());
				if(result!=null) {
					String projectPo = result.getString("PROJECT_PO");
					String po = result.getString("PO");
					String deptId = result.getString("DEPT_ID");
					if(StringUtils.notBlank(projectPo)&&projectPo.contains(getUserId())) {
						isAdmin = 1;
					}
					if(StringUtils.notBlank(po)&&po.contains(getUserId())) {
						isAdmin = 1;
					}
					if(StringUtils.notBlank(deptId)) {
						String leader = StaffService.getService().getLeader(deptId);
						String secondLeader = StaffService.getService().getDeptSecondLeader(deptId);
						if(StringUtils.notBlank(leader)&&leader.equalsIgnoreCase(getUserId())) {
							isAdmin = 1;
						}else if(StringUtils.notBlank(secondLeader)&&secondLeader.equalsIgnoreCase(getUserId())) {
							isAdmin = 1;
						}
					}
				}
			} catch (SQLException e) {
				this.getLogger().error(e.getMessage(),e);
			}
		}
		
		if(hasRole("PROJECT_MANAGER")||hasRole("CONTRACT_MGR")||hasRole("FLOW_SUPER_USER")||isSuperUser()) {
			isAdmin = 1;
		}
		if(isAdmin==0) {
			renderHtml("您没权限，请联系管理员");
			return;
		}
		LookLogService.getService().addLog(getUserPrincipal().getUserId(),id,"contract",getRequest());
		setAttr("projectId",id);
		renderJsp("/pages/crm/contract/contract-detail.jsp?contractId="+id);
	}
	
	@ActionKey("/cust")
	public void cust() {
		String id = getPara();
		if(StringUtils.isBlank(id)) {
			id = getPara("custId");
		}
		setAttr("custId",id);
		renderJsp("/pages/crm/cust/cust-detail.jsp?custId="+id);
	}
	
	public void goZt() {
		set("ztHasLogin","0");
		CookieKit cookieKit = new CookieKit(getRequest(), getResponse());
		String zentaosid = cookieKit.getCookie("zentaosid");
		String queryStr = getRequest().getQueryString();
		String url = null;
		if(StringUtils.isBlank(queryStr)) {
			url = "http://*************:6688/index.php?m=my&f=work&mode=bug";
		}else {
			url = queryStr.replaceAll("url=", "");
		}
		if(StringUtils.notBlank(zentaosid)) {
			set("ztHasLogin","1");
//			url = url.replaceAll("/yq-work", "");
//			redirect(url);
//			return;
		}
		set("url", url);
		renderJsp("/pages/project/include/zt-go.jsp");
	}
	
	public void projectTest() {
		String projectId = getPara();
		if(StringUtils.isBlank(projectId)) {
			renderHtml("参数不能为空");
			return;
		}
		String id = Db.queryStr("select id from zentao.zt_project where code = ?",projectId);
		if(StringUtils.isBlank(id)) {
			renderHtml("项目暂没同步到禅道");
			return;
		}
		Record projectproduct = Db.findFirst("select product from zentao.zt_projectproduct where project = ?",id);
		if(projectproduct!=null) {
			String productId = projectproduct.get("product");
			redirect("http://*************:6688/index.php?m=bug&f=browse&productID="+productId);
		}else {
			renderHtml("项目暂无设置产品");
		}
	}
	
	public void bug() {
		String projectId = getPara();
		String id = Db.queryStr("select id from zentao.zt_project where code = ?",projectId);
		if(StringUtils.isBlank(id)) {
			try {
			    JSONObject row = this.getQuery().queryForRow("select * from yq_project where project_id= ?", new Object[] {projectId}, new JSONMapperImpl());
			    JSONObject result = ZendaoApi.addProject(row.getString("PROJECT_NAME"),projectId);
			    if(result==null||result.isEmpty()) {
			    	this.getLogger().error("bug>projectId>"+projectId);
			    	renderHtml("新增项目失败，请联系管理员");
			    	return;
			    }
			    this.getLogger().info("update zentao project>"+result.toJSONString());
			    id = result.getString("id");
			    EasyRecord record = new EasyRecord("zentao.zt_project","id");
			    record.set("id",id);
			    record.set("code", projectId);
			    record.set("type", "project");
			    record.set("model", "scrum");
			    record.set("acl", "open");
			    record.set("auth", "extend");
			    record.set("status","doing");
			    record.set("firstEnd","2059-12-31");
				record.set("begin", EasyDate.getCurrentDateString("yyyy-MM-dd"));
				record.set("realBegan", EasyDate.getCurrentDateString("yyyy-MM-dd"));
			    this.getQuery().update(record);
			    
			    this.getQuery().executeUpdate("update yq_project set zentao_id = ? where project_id = ?", id,projectId);
			    
			} catch (Exception e) {
				this.getLogger().error(e.getMessage());
				renderHtml(e.getMessage());
				return;
			}
		}
//		redirect("http://*************:6688/index.php?m=project&f=index&project="+id);
		redirect("http://*************:6688/index.php?m=project&f=bug&projectID="+id);
	}
	
	/**
	 * https://www.zentao.net/book/zentaopms/344.html
	 */
	public void zentao() {
		long timestamp = System.currentTimeMillis() / 1000;		 
		String token = MD5Util.getHexMD5("OAe36fae6627a6d97a77ce3c9b1b5868f2"+timestamp);
		token = token.toLowerCase();
		String account = getUserPrincipal().getLoginAcct();
		redirect("http://*************:6688/api.php?m=user&f=apilogin&id=2&code=OA&time="+timestamp+"&token="+token+"&account="+account);
	}
	
	
	public void projectConsole() {
		keepPara();
		String projectId = getPara();
		if(StringUtils.isBlank(projectId)) {
			projectId = getPara("projectId");
		}
		if(StringUtils.isBlank(projectId)) {
			renderHtml("页面链接错误");
			return;
		}
		JSONObject result = null;
		try {
			result = this.getQuery().queryForRow("select * from yq_project where project_id  = ?",new Object[] {projectId}, new JSONMapperImpl());
			if(result==null) {
				renderHtml("页面链接错误");
				return;
			}
			setAttr("projectInfo", result);
		} catch (SQLException e) {
			LogUtils.getLogger().error(e.getMessage(),e);
		}
		int isAdmin = 0;
		if(hasRole("PROJECT_MANAGER")||hasRole("CONTRACT_MGR")||hasRole("FLOW_SUPER_USER")||isSuperUser()) {
			isAdmin = 1;
		}
		
		if(result!=null) {
			String projectPo = result.getString("PROJECT_PO");
			String po = result.getString("PO");
			String deptId = result.getString("DEPT_ID");
			if(StringUtils.notBlank(projectPo)&&projectPo.contains(getUserId())) {
				isAdmin = 1;
			}
			if(StringUtils.notBlank(po)&&po.contains(getUserId())) {
				isAdmin = 1;
			}
			if(StringUtils.notBlank(deptId)) {
				String leader = StaffService.getService().getLeader(deptId);
				String secondLeader = StaffService.getService().getDeptSecondLeader(deptId);
				if(StringUtils.notBlank(leader)&&leader.equalsIgnoreCase(getUserId())) {
					isAdmin = 1;
				}else if(StringUtils.notBlank(secondLeader)&&secondLeader.equalsIgnoreCase(getUserId())) {
					isAdmin = 1;
				}
			}
		}
		setAttr("isAdmin", isAdmin);
		renderJsp("/pages/project/project-console.jsp");
	}
	
	public void projectApprove() {
		String projectId = getPara();
		String contractId = Db.queryStr("select contract_id from yq_project where project_id = ?", projectId);
		String reviewId = Db.queryStr("select REVIEW_ID from YQ_PROJECT_CONTRACT where CONTRACT_ID = ?", contractId);
		if(StringUtils.isBlank(reviewId)) {
			renderHtml("没找到对应的评审流程");
			return;
		}
		String applyId = Db.queryStr("select apply_id from yq_flow_apply where apply_no = ?", reviewId);
		redirect("/web/flow/"+applyId);
	}
	
}
