package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.utils.WeekUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * <AUTHOR>
 */
@WebObject(name = "ContractStatisticDao")
public class ContractStatisticDao extends AppDaoContext {


    @WebControl(name = "statisticUpdateTime", type = Types.TEXT)
    public JSONObject receiptUpdateTime() {

        String tableName = param.getString("tableName");
        if (StringUtils.isBlank(tableName)) {
            tableName = this.getMethodParam(0).toString();
        }
        EasySQL sql = getEasySQL("select UPDATE_TIME FROM yq_contract_stat_table_info");
        sql.append(tableName, "where TABLE_NAME = ? ");
        try {
            String updateTime = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
            return getJsonResult(updateTime);
        } catch (SQLException e) {
            this.getLogger().error(e.getMessage(), e);
            return getJsonResult("更新时间缺失");
        }
    }

    //240815直接从收款表里进行统计
    @WebControl(name = "receiptStatList", type = Types.LIST)
    public JSONObject receiptStatListFromReceipt() {
        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        EasySQL sql = getEasySQL("select t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN yq_contract_receipt.MONTH_ID = ? THEN yq_contract_receipt.AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_" + monthId);
            }
        }
        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN  yq_contract_receipt ON t1.CONTRACT_ID = yq_contract_receipt.CONTRACT_ID");

        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");

        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        sql.append("order by t1.create_time desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    //240906从统计表里进行查询
    @WebControl(name = "receiptStatList2", type = Types.LIST)
    public JSONObject receiptStatListFromStatTable() {
        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        EasySQL sql = getEasySQL("select t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {

                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_" + monthId);
            }
        }
        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN yq_contract_receipt_stat t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");

        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");

        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        sql.append("order by t1.create_time desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    //240815从incomeStage、incomeConfirm两个表直接统计
    @WebControl(name = "incomeStatList", type = Types.LIST)
    public JSONObject incomeStatList() {
        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        EasySQL sql = getEasySQL("select t3.CONTRACT_SIMPILE_NAME,t3.CONTRACT_NO,t3.CUSTOMER_NAME,t3.AMOUNT,");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append("t3.STAGE_" + monthId + ",");
            }
        }

        for (int ab = 0; ab < 2; ab++) {
            for (int i = 0; i < period; i++) {
                int curYear = startYear + i;
                for (int j = 1; j <= 12; j++) {
                    String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                    if (ab == 0) {
                        sql.append(monthId, "SUM(CASE WHEN yq_contract_income_confirm.MONTH_ID = ? AND yq_contract_income_confirm.CONFIRM_TYPE = 'A' THEN yq_contract_income_confirm.AMOUNT_NO_TAX ELSE 0 END)");
                        sql.append("AS " + "TOTAL_A_" + monthId + ",");
                    } else {
                        if (j == 12 && i == period - 1) {
                            sql.append(monthId, "SUM(CASE WHEN yq_contract_income_confirm.MONTH_ID = ? AND yq_contract_income_confirm.CONFIRM_TYPE = 'B' THEN yq_contract_income_confirm.AMOUNT_NO_TAX ELSE 0 END)");
                            sql.append("AS " + "TOTAL_B_" + monthId);
                        } else {
                            sql.append(monthId, "SUM(CASE WHEN yq_contract_income_confirm.MONTH_ID = ? AND yq_contract_income_confirm.CONFIRM_TYPE = 'B' THEN yq_contract_income_confirm.AMOUNT_NO_TAX ELSE 0 END)");
                            sql.append("AS " + "TOTAL_B_" + monthId + ",");
                        }
                    }
                }
            }
        }

        sql.append("from (");
        sql.append("select t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT,t1.create_time");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN yq_contract_income_stage.MONTH_ID = ? THEN yq_contract_income_stage.AMOUNT_NO_TAX ELSE 0 END)");
                sql.append("AS " + "STAGE_" + monthId);
            }
        }

        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN  yq_contract_income_stage ON t1.CONTRACT_ID = yq_contract_income_stage.CONTRACT_ID");
        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");
        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT,t1.CREATE_TIME ");
        sql.append("order by t1.create_time desc");

        sql.append(" ) t3");

        sql.append("LEFT JOIN yq_contract_income_confirm ON t3.CONTRACT_ID = yq_contract_income_confirm.CONTRACT_ID");
        sql.append("GROUP BY t3.CONTRACT_SIMPILE_NAME,t3.CONTRACT_NO,t3.CUSTOMER_NAME,t3.AMOUNT");
        sql.append("order by t3.create_time desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    //240906 从yq_contract_income_stat表查询
    @WebControl(name = "incomeStatList2", type = Types.LIST)
    public JSONObject incomeStatListFromStatTable() {

        Integer period = param.getIntValue("period");
        Integer startYear = param.getIntValue("startYear");

        EasySQL sql = getEasySQL("select t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? AND t2.INCOME_TYPE = 'S' THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "STAGE_" + monthId);
            }
        }
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? AND t2.INCOME_TYPE = 'A' THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_A_" + monthId);
            }
        }
        for (int i = 0; i < period; i++) {
            int curYear = startYear + i;
            for (int j = 1; j <= 12; j++) {
                String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
                sql.append(monthId, ",SUM(CASE WHEN t2.MONTH_ID = ? AND t2.INCOME_TYPE = 'B' THEN t2.TOTAL_AMOUNT ELSE 0 END)");
                sql.append("AS " + "TOTAL_B_" + monthId);
            }
        }
        sql.append("FROM yq_project_contract t1");
        sql.append("LEFT JOIN yq_contract_income_stat t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");

        sql.append("WHERE 1=1");
        sql.appendLike(param.getString("contractName"), "and t1.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t1.CUSTOMER_NAME like ?");
        sql.appendLike(param.getString("contractNo"), "and t1.CONTRACT_NO like ?");

        sql.append("GROUP BY t1.CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_NO,t1.CUSTOMER_NAME,t1.AMOUNT");
        sql.append("order by t1.create_time desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    //按月统计销售金额
    @WebControl(name = "contractAmountStatByMonth", type = Types.LIST)
    public JSONObject contractAmountStatByMonth() {
        Integer curYear = param.getIntValue("curYear");
        if (curYear == 0) {
            curYear = EasyCalendar.newInstance().getYear();
        }
        Integer lastYear = curYear -1;
        EasySQL sql = getEasySQL("select ");
        for (int j = 1; j <= 12; j++) {
            String month = (j < 10) ? curYear + "-0" + j : curYear + "-" + j;
            sql.appendLike(month, "ROUND(SUM(CASE WHEN SIGN_DATE like ? THEN t1.AMOUNT ELSE 0 END),2)");
            String monthName = (j < 10) ? curYear + "_0" + j : curYear + "_" + j;
            sql.append("AS " + monthName + ",");
        }
        for (int j = 1; j <= 12; j++) {
            String month = (j < 10) ? lastYear + "-0" + j : lastYear + "-" + j;
            sql.appendLike(month, "ROUND(SUM(CASE WHEN SIGN_DATE like ? THEN t1.AMOUNT ELSE 0 END),2)");
            String monthName = (j < 10) ? lastYear + "_0" + j : lastYear + "_" + j;
            if (j == 12){
                sql.append("AS " + monthName );
            }else {
                sql.append("AS " + monthName + ",");
            }
        }
        sql.append("FROM yq_project_contract t1");
        sql.append("WHERE 1=1");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and SALES_BY = ?");
        }
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "receiptAmountStatByMonth", type = Types.LIST)
    public JSONObject receiptAmountStatByMonth() {
        Integer curYear = param.getIntValue("curYear");
        if (curYear == 0) {
            curYear = EasyCalendar.newInstance().getYear();
        }
        Integer lastYear = curYear -1;

        EasySQL sql = getEasySQL("select ");
        for (int j = 1; j <= 12; j++) {
            String monthId = (j < 10) ? curYear + "0" + j : curYear + "" + j;
            sql.append(monthId, "SUM(CASE WHEN t1.MONTH_ID = ? THEN t1.TOTAL_AMOUNT ELSE 0 END)");
            String monthName = (j < 10) ? curYear + "_0" + j : curYear + "_" + j;
            sql.append("AS "  + monthName +" ,");
        }
        for (int j = 1; j <= 12; j++) {
            String monthId = (j < 10) ? lastYear + "0" + j : lastYear + "" + j;
            sql.append(monthId, "SUM(CASE WHEN t1.MONTH_ID = ? THEN t1.TOTAL_AMOUNT ELSE 0 END)");
            String monthName = (j < 10) ? lastYear + "_0" + j : lastYear + "_" + j;
            if (j == 12){
                sql.append("AS " + monthName );
            }else {
                sql.append("AS " + monthName + ",");
            }
        }
        sql.append("FROM yq_contract_receipt_stat t1 ");
        if ("user".equals(param.getString("source"))) {
            sql.append("inner join yq_project_contract t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
            sql.append("WHERE 1=1");
            sql.append(getUserId(), " and t2.SALES_BY = ?");
        }
        return queryForRecord(sql.getSQL(), sql.getParams());

    }



    @WebControl(name = "contractAmountStatByQuarter", type = Types.LIST)
    public JSONObject contractAmountStatByQuarter() {
        Integer curYear = param.getIntValue("curYear");
        if (curYear == 0) {
            curYear = EasyCalendar.newInstance().getYear();
        }
        Integer lastYear = curYear - 1;
        EasySQL sql = getEasySQL("SELECT ");

        for (int j = 1; j <= 4; j++) {
            String startMonth =  ((j - 1) * 3 + 1) <= 9 ?   "0" + ((j - 1) * 3 + 1): ((j - 1) * 3 + 1)+"" ;
            String endMonth = (j * 3) <= 9 ? "0" + (j * 3) : "" + (j * 3);
            String quarterStart = curYear + "-" + startMonth + "-01";
            String quarterEnd = curYear + "-" + endMonth  + "-31";
            sql.append(quarterStart, "ROUND(SUM(CASE WHEN SIGN_DATE >= ?");
            sql.append(quarterEnd,"AND SIGN_DATE <= ? THEN t1.AMOUNT ELSE 0 END), 2)");
            String quarterName = curYear + "_Q" + j;
            sql.append("AS " + quarterName + ",");

            String quarterStart2 = lastYear + "-" + startMonth + "-01";
            String quarterEnd2 = lastYear + "-" + endMonth + "-31";
            sql.append(quarterStart2, "ROUND(SUM(CASE WHEN SIGN_DATE >= ? ");
            sql.append(quarterEnd2,"AND SIGN_DATE <= ? THEN t1.AMOUNT ELSE 0 END), 2)");
            String quarterName2 = lastYear + "_Q" + j;
            if (j == 4) {
                sql.append("AS " + quarterName2);
            } else {
                sql.append("AS " + quarterName2 + ",");
            }
        }

        sql.append("FROM yq_project_contract t1 ");
        sql.append("WHERE 1=1 ");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "AND SALES_BY = ?");
        }

        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "receiptAmountStatByQuarter", type = Types.LIST)
    public JSONObject receiptAmountStatByQuarter() {
        Integer curYear = param.getIntValue("curYear");
        if (curYear == 0) {
            curYear = EasyCalendar.newInstance().getYear();
        }
        Integer lastYear = curYear - 1;
        EasySQL sql = getEasySQL("SELECT ");

        // 当前年份的季度统计
        for (int j = 1; j <= 4; j++) {
            String startMonth = ((j - 1) * 3 + 1) <= 9 ? "0" + ((j - 1) * 3 + 1) : ((j - 1) * 3 + 1) + "";
            String endMonth = (j * 3) <= 9 ? "0" + (j * 3) : "" + (j * 3);

            sql.append(curYear + startMonth, "ROUND(SUM(CASE WHEN MONTH_ID >= ? ");
            sql.append(curYear + endMonth,"AND MONTH_ID <= ? THEN t1.TOTAL_AMOUNT ELSE 0 END), 2)");
            String quarterName = curYear + "_Q" + j;
            sql.append("AS " + quarterName + ",");

            sql.append(lastYear + startMonth, "ROUND(SUM(CASE WHEN MONTH_ID >= ? ");
            sql.append(lastYear + endMonth,"AND MONTH_ID <= ? THEN t1.TOTAL_AMOUNT ELSE 0 END), 2)");
            String quarterName2 = lastYear + "_Q" + j;
            if (j == 4) {
                sql.append("AS " + quarterName2);
            } else {
                sql.append("AS " + quarterName2 + ",");
            }
        }

        sql.append("FROM yq_contract_receipt_stat t1 ");
        if ("user".equals(param.getString("source"))) {
            sql.append("inner join yq_project_contract t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
            sql.append("WHERE 1=1");
            sql.append(getUserId(), " and t2.SALES_BY = ?");
        }

        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    /**
     * 合同 今年 回款排行
     * 合同年限不限，以收款日期统计今年
     *
     * @return
     */
    @WebControl(name = "contractReceiptRank", type = Types.LIST)
    public JSONObject contractReceiptRank() {
        String year = StringUtils.isBlank(param.getString("receiptYear"))? EasyCalendar.newInstance().getYear() + "" : param.getString("receiptYear");
        EasySQL sql = getEasySQL("SELECT t1.CONTRACT_SIMPILE_NAME,t1.AMOUNT,t1.CONTRACT_ID, t1.CUST_ID,SUM(t2.AMOUNT) AS TOTAL_RECEIPT_ALL,");
        sql.append(year, "SUM(CASE WHEN t2.YEAR_ID = ? THEN t2.AMOUNT ELSE 0 END) AS TOTAL_RECEIPT_YEAR");
        sql.append("FROM yq_project_contract t1 inner join yq_contract_receipt t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("GROUP BY t1.CONTRACT_ID,t1.AMOUNT,t1.CONTRACT_SIMPILE_NAME,t1.CUST_ID");
        setOrderBy(sql, " order by TOTAL_RECEIPT_YEAR desc, TOTAL_RECEIPT_ALL desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    /**
     * 客户 今年 回款排行
     *
     * @return
     */
    @WebControl(name = "custReceiptRank", type = Types.LIST)
    public JSONObject custReceiptRank() {
        String year = StringUtils.isBlank(param.getString("receiptYear"))? EasyCalendar.newInstance().getYear() + "" : param.getString("receiptYear");
        EasySQL sql = getEasySQL("SELECT t1.CUSTOMER_NAME,t1.CUST_ID, sub.AMOUNT,ROUND(SUM(t2.AMOUNT),2) AS TOTAL_RECEIPT_ALL,");
        sql.append(year, "ROUND(SUM(CASE WHEN t2.YEAR_ID = ? THEN t2.AMOUNT ELSE 0 END),2) AS TOTAL_RECEIPT_YEAR");
        sql.append("FROM (SELECT CONTRACT_ID, ROUND(SUM(AMOUNT), 2) AS AMOUNT FROM yq_project_contract GROUP BY CONTRACT_ID) sub ");
        sql.append("INNER JOIN yq_project_contract t1 ON sub.CONTRACT_ID = t1.CONTRACT_ID inner join yq_contract_receipt t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("GROUP BY t1.CUST_ID,t1.CUSTOMER_NAME,t1.CONTRACT_ID,sub.AMOUNT");
        setOrderBy(sql, " order by TOTAL_RECEIPT_YEAR desc, TOTAL_RECEIPT_ALL desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 今年 销售合同金额 销售排行
     * 要排除掉离职员工
     * @return
     */
    @WebControl(name = "saleContractAmountRank", type = Types.LIST)
    public JSONObject saleContractAmountRank() {
        Integer year = param.getIntValue("year");
        year = year == 0 ?EasyCalendar.newInstance().getYear():year;
        EasySQL sql = getEasySQL("SELECT t1.SALES_BY,");
        sql.append(year, " ROUND(SUM(CASE WHEN t1.YEAR = ? THEN t1.AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(year-1, " ROUND(SUM(CASE WHEN t1.YEAR = ? THEN t1.AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append(" FROM yq_project_contract t1 ");
        sql.append("INNER JOIN " + Constants.DS_MAIN_NAME + ".easi_user t2 on t2.USER_ID = t1.SALES_BY where t2.STATE = 0");
        sql.append(" GROUP BY t1.SALES_BY");
        sql.append("HAVING TOTAL_AMOUNT_THIS_YEAR > 0 OR TOTAL_AMOUNT_LAST_YEAR > 0");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc, TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 今年 营销区域 销售合同金额 销售排行
     *
     * @return
     */
    @WebControl(name = "deptContractAmountRank", type = Types.LIST)
    public JSONObject deptContractAmountRank() {
        EasySQL sql = getEasySQL("SELECT JSON_UNQUOTE(JSON_EXTRACT(TEXT_JSON, '$.SALE_DEPT_NAME')) AS SALE_DEPT_NAME,");
        sql.append(EasyCalendar.newInstance().getYear(), "ROUND(SUM(CASE WHEN YEAR = ? THEN AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(EasyCalendar.newInstance().getYear() - 1, "ROUND(SUM(CASE WHEN YEAR = ? THEN AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append("FROM yq_project_contract");
        sql.append("GROUP BY JSON_UNQUOTE(JSON_EXTRACT(TEXT_JSON, '$.SALE_DEPT_NAME'))");
        sql.append("HAVING TOTAL_AMOUNT_THIS_YEAR > 0 OR TOTAL_AMOUNT_LAST_YEAR > 0");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc, TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 产品线 合同金额 排行 + 去年对比
     *
     * @return
     */
    @WebControl(name = "prodLineContractAmountRank", type = Types.TEMPLATE)
    public JSONObject prodLineContractAmountRank() {
        EasySQL sql = getEasySQL("SELECT COALESCE(NULLIF(PROD_LINE, ''), '其他') AS PROD_LINE, ");
        sql.append(EasyCalendar.newInstance().getYear(), "ROUND(SUM(CASE WHEN YEAR = ? THEN AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(EasyCalendar.newInstance().getYear() - 1, "ROUND(SUM(CASE WHEN YEAR = ? THEN AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append("FROM yq_project_contract");
        sql.append("GROUP BY COALESCE(NULLIF(PROD_LINE, ''), '其他') ");
        sql.append("HAVING TOTAL_AMOUNT_THIS_YEAR > 0 OR TOTAL_AMOUNT_LAST_YEAR > 0");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc, TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    /**
     * 合同类型 合同金额 排行 + 去年对比
     *
     * @return
     */
    @WebControl(name = "typeContractAmountRank", type = Types.TEMPLATE)
    public JSONObject typeContractAmountRank() {
        EasySQL sql = getEasySQL("SELECT COALESCE(NULLIF(CONTRACT_TYPE, ''), '其他') AS CONTRACT_TYPE,  ");
        sql.append(EasyCalendar.newInstance().getYear(), "ROUND(SUM(CASE WHEN YEAR = ? THEN AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(EasyCalendar.newInstance().getYear() - 1, "ROUND(SUM(CASE WHEN YEAR = ? THEN AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append("FROM yq_project_contract");
        sql.append("GROUP BY COALESCE(NULLIF(CONTRACT_TYPE, ''), '其他') ");
        sql.append("HAVING TOTAL_AMOUNT_THIS_YEAR > 0 OR TOTAL_AMOUNT_LAST_YEAR > 0");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc, TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    /**
     * 销售 今年 回款排行
     * 用合同的 销售 而不是 收款的负责人计算
     *
     * @return
     */
    @WebControl(name = "salesReceiptRank", type = Types.LIST)
    public JSONObject salesReceiptRank() {
        Integer year = param.getIntValue("year");
        year = year == 0 ?EasyCalendar.newInstance().getYear():year;
        EasySQL sql = getEasySQL("SELECT t1.SALES_BY,");
        sql.append(year, " ROUND(SUM(CASE WHEN t2.YEAR_ID = ? THEN t2.AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(year - 1, " ROUND(SUM(CASE WHEN t2.YEAR_ID = ? THEN t2.AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append("FROM yq_project_contract t1 inner join yq_contract_receipt t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("INNER JOIN " + Constants.DS_MAIN_NAME + ".easi_user t3 on t3.USER_ID = t1.SALES_BY where t3.STATE = 0");
        sql.append("GROUP BY t1.SALES_BY");
        sql.append("HAVING TOTAL_AMOUNT_THIS_YEAR > 0 OR TOTAL_AMOUNT_LAST_YEAR > 0");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc, TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());

    }

    @WebControl(name = "salesReceiptRankTop10", type = Types.TEMPLATE)
    public JSONObject salesReceiptRankTop10() {
        EasySQL sql = getEasySQL("SELECT t1.SALES_BY,  ROUND(SUM(t2.AMOUNT), 2)  AS TOTAL_AMOUNT FROM yq_project_contract t1");
        sql.append("inner join yq_contract_receipt t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("INNER JOIN " + Constants.DS_MAIN_NAME + ".easi_user t3 on t3.USER_ID = t1.SALES_BY");
        sql.append(EasyCalendar.newInstance().getYear(), "where t2.YEAR_ID = ?");
        sql.append("and t3.STATE = 0");
        sql.append("GROUP BY t1.SALES_BY");
        setOrderBy(sql, " order by TOTAL_AMOUNT desc");
        sql.append("LIMIT 10");
        return queryForList(sql.getSQL(), sql.getParams());
    }


    /**
     * 营销大区 今年 回款排行
     * 用合同的 营销大区 计算
     *
     * @return
     */
    @WebControl(name = "deptReceiptRank", type = Types.LIST)
    public JSONObject deptReceiptRank() {
        EasySQL sql = getEasySQL("SELECT JSON_UNQUOTE(JSON_EXTRACT(TEXT_JSON, '$.SALE_DEPT_NAME')) AS SALE_DEPT_NAME,");
        sql.append(EasyCalendar.newInstance().getYear(), "ROUND(SUM(CASE WHEN t2.YEAR_ID = ? THEN t2.AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(EasyCalendar.newInstance().getYear() - 1, "ROUND(SUM(CASE WHEN t2.YEAR_ID = ? THEN t2.AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append(" FROM yq_project_contract t1 inner join yq_contract_receipt t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("GROUP BY JSON_UNQUOTE(JSON_EXTRACT(TEXT_JSON, '$.SALE_DEPT_NAME'))");
        sql.append("HAVING TOTAL_AMOUNT_THIS_YEAR > 0 OR TOTAL_AMOUNT_LAST_YEAR > 0");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc, TOTAL_AMOUNT_LAST_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "deptReceiptRankTop10", type = Types.TEMPLATE)
    public JSONObject deptReceiptRankTop10() {
        EasySQL sql = getEasySQL("SELECT JSON_UNQUOTE(JSON_EXTRACT(TEXT_JSON, '$.SALE_DEPT_NAME')) AS SALE_DEPT_NAME,  ROUND(SUM(t2.AMOUNT), 2)  AS TOTAL_AMOUNT FROM yq_project_contract t1");
        sql.append("inner join yq_contract_receipt t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append(EasyCalendar.newInstance().getYear(), "where t2.YEAR_ID = ?");
        sql.append("GROUP BY JSON_UNQUOTE(JSON_EXTRACT(TEXT_JSON, '$.SALE_DEPT_NAME'))");
        setOrderBy(sql, " order by TOTAL_AMOUNT desc");
        sql.append("LIMIT 10");
        return queryForList(sql.getSQL(), sql.getParams());
    }


    /**
     * 本月 未确认金额 销售排行
     *
     * @return
     */
    @WebControl(name = "unConfirmIncomeMonthRankBySales", type = Types.TEMPLATE)
    public JSONObject unConfirmIncomeMonthRankBySales() {
        EasySQL sql = getEasySQL("SELECT t1.SALES_BY, SUM(t2.UNCONFIRM_NO_TAX_A) AS TOTAL_UNCONFIRM_NO_TAX_A FROM yq_project_contract t1 JOIN yq_contract_income_stage t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("INNER JOIN " + Constants.DS_MAIN_NAME + ".easi_user t3 on t3.USER_ID = t1.SALES_BY where t3.STATE = 0");
        sql.append(EasyCalendar.newInstance().getFullMonth(), "and t2.MONTH_ID = ?");
        sql.append("GROUP BY t1.SALES_BY");
        sql.append("ORDER BY TOTAL_UNCONFIRM_NO_TAX_A DESC");
        sql.append("LIMIT 10");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    /**
     * 本年 未确认金额 销售排行
     *
     * @return
     */
    @WebControl(name = "unConfirmIncomeYearRankBySales", type = Types.TEMPLATE)
    public JSONObject unConfirmIncomeYearRankBySales() {
        EasySQL sql = getEasySQL("SELECT t1.SALES_BY, SUM(t2.UNCONFIRM_NO_TAX_A) AS TOTAL_UNCONFIRM_NO_TAX_A FROM yq_project_contract t1 JOIN yq_contract_income_stage t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("INNER JOIN " + Constants.DS_MAIN_NAME + ".easi_user t3 on t3.USER_ID = t1.SALES_BY where t3.STATE = 0");
        sql.append(EasyCalendar.newInstance().getYear(), "and t2.YEAR_ID = ?");
        sql.append("GROUP BY t1.SALES_BY");
        sql.append("ORDER BY TOTAL_UNCONFIRM_NO_TAX_A DESC");
        sql.append("LIMIT 10");
        return queryForList(sql.getSQL(), sql.getParams());
    }


    /**
     * 本年 已确认金额 销售排行
     *
     * @return
     */
    @WebControl(name = "confirmIncomeYearRankBySales", type = Types.TEMPLATE)
    public JSONObject confirmIncomeYearRankBySales() {
        EasySQL sql = getEasySQL("SELECT t1.SALES_BY, SUM(t2.AMOUNT_NO_TAX) AS TOTAL_CONFIRM_NO_TAX_A FROM yq_project_contract t1 JOIN yq_contract_income_confirm t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("INNER JOIN " + Constants.DS_MAIN_NAME + ".easi_user t3 on t3.USER_ID = t1.SALES_BY where t3.STATE = 0");
        sql.append(EasyCalendar.newInstance().getYear(), "and t2.YEAR_ID = ?");
        sql.append("and t2.CONFIRM_TYPE = 'A'");
        sql.append("GROUP BY t1.SALES_BY");
        sql.append("ORDER BY TOTAL_CONFIRM_NO_TAX_A DESC");
        sql.append("LIMIT 10");
        return queryForList(sql.getSQL(), sql.getParams());
    }


    /**
     * 未确认金额 合同排行
     *
     * @return
     */
    @WebControl(name = "unConfirmContractRankYear", type = Types.TEMPLATE)
    public JSONObject unConfirmContractRankYear() {
        EasySQL sql = getEasySQL("SELECT pc.CUST_ID,pc.CONTRACT_ID,pc.CONTRACT_SIMPILE_NAME, SUM(cis.UNCONFIRM_NO_TAX_A) AS TOTAL_UNCONFIRM_NO_TAX_A FROM yq_project_contract pc JOIN yq_contract_income_stage cis ON pc.CONTRACT_ID = cis.CONTRACT_ID");
        sql.append("where 1=1");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  pc.SALES_BY = ? ");
        }
        sql.append("GROUP BY pc.CUST_ID,pc.CONTRACT_ID,pc.CONTRACT_SIMPILE_NAME");
        sql.append("ORDER BY TOTAL_UNCONFIRM_NO_TAX_A DESC");
        sql.append("LIMIT 10");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    /**
     * 未确认金额 客户排行
     *
     * @return
     */
    @WebControl(name = "unConfirmCustomerRankYear", type = Types.TEMPLATE)
    public JSONObject unConfirmCustomerRankYear() {
        EasySQL sql = getEasySQL("SELECT pc.CUST_ID,pc.CUSTOMER_NAME, SUM(cis.UNCONFIRM_NO_TAX_A) AS TOTAL_UNCONFIRM_NO_TAX_A FROM yq_project_contract pc JOIN yq_contract_income_stage cis ON pc.CONTRACT_ID = cis.CONTRACT_ID");
        sql.append("where 1=1");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  pc.SALES_BY = ? ");
        }
        sql.append("GROUP BY pc.CUST_ID,pc.CUSTOMER_NAME");
        sql.append("ORDER BY TOTAL_UNCONFIRM_NO_TAX_A DESC");
        sql.append("LIMIT 10");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    /**
     * 待收款金额 合同排行
     *
     * @return
     */
    @WebControl(name = "unReceiptContractTop10", type = Types.TEMPLATE)
    public JSONObject unReceiptContractTop10() {
        EasySQL sql = getEasySQL("SELECT t1.CUST_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_ID,  ROUND(SUM(t2.PLAN_RCV_AMT - t2.RECEIVED_AMT ), 2)  AS TOTAL_AMOUNT FROM yq_project_contract t1");
        sql.append("inner join yq_contract_stage t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("where 1=1");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  t1.SALES_BY = ? ");
        }
        sql.append("GROUP BY t1.CUST_ID,t1.CONTRACT_SIMPILE_NAME,t1.CONTRACT_ID");
        sql.append(" order by TOTAL_AMOUNT desc");
        sql.append("LIMIT 10");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    /**
     * 待收款 客户排行
     *
     * @return
     */
    @WebControl(name = "unReceiptCustomerTop10", type = Types.TEMPLATE)
    public JSONObject unReceiptCustomerTop10() {
        EasySQL sql = getEasySQL("SELECT t1.CUST_ID, t1.CUSTOMER_NAME,  ROUND(SUM(t2.PLAN_RCV_AMT - t2.RECEIVED_AMT ), 2)  AS TOTAL_AMOUNT FROM yq_project_contract t1");
        sql.append("inner join yq_contract_stage t2 on t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("where 1=1");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  t1.SALES_BY = ? ");
        }
        sql.append("GROUP BY t1.CUST_ID, t1.CUSTOMER_NAME");
        sql.append("order by TOTAL_AMOUNT desc");
        sql.append("LIMIT 10");
        return queryForList(sql.getSQL(), sql.getParams());
    }


    /**
     * 合同看板统计数据
     *
     * @return
     */
    @WebControl(name = "contractConsoleStat", type = Types.RECORD)
    public JSONObject contractConsoleStat() {
        EasySQL sql = getEasySQL("select ");
        sql.append(EasyCalendar.newInstance().getYear(), " COUNT(CASE WHEN t1.YEAR = ? THEN 1 ELSE NULL END) AS COUNT_CONTRACT,");
        sql.append(EasyCalendar.newInstance().getYear(), " CONCAT(ROUND(SUM(CASE WHEN t1.YEAR = ? THEN AMOUNT ELSE 0 END) / 10000, 2), '万') AS SUM_CONTRACT,");
        sql.append(EasyCalendar.newInstance().getYear() - 1, " CONCAT(ROUND(SUM(CASE WHEN t1.YEAR = ? THEN AMOUNT ELSE 0 END) / 10000, 2), '万') AS SUM_LAST_YEAR");
        sql.append("from yq_project_contract t1 ");
        sql.append("where 1=1");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  t1.SALES_BY = ? ");
        } else if ("all".equals(param.getString("source"))) {
            sql.append(0,"and t1.SIGN_FLAG = ?");
        }
        
        JSONObject result = queryForRecord(sql.getSQL(), sql.getParams());

        JSONObject data = result.getJSONObject("data");
        String[] keys = new String[]{"COUNT_CONTRACT", "SUM_CONTRACT", "SUM_LAST_YEAR"};
        for (int i = 0; i < keys.length; i++) {
            if (StringUtils.isBlank(data.getString(keys[i]))) {
                data.put(keys[i], "0");
            }
        }

//        double sumContract = Double.parseDouble(data.getString("SUM_CONTRACT"));
//        String roundedSumContract = String.format("%.2f", sumContract);
//        data.put("SUM_CONTRACT", roundedSumContract);

        EasySQL sql2 = getEasySQL("select IFNULL(COUNT(*), 0) as COUNT_CUST from yq_crm_customer WHERE 1=1");
        if ("user".equals(param.getString("source"))) {
            sql2.append(getUserId(), "and owner_id = ?");
        }
        sql2.appendLike(EasyCalendar.newInstance().getYear(), "and create_time like ?");
        String countCust = queryForString(sql2.getSQL(), sql2.getParams());
        data.put("COUNT_CUST", StringUtils.isBlank(countCust) ? "0" : countCust);
        
        EasySQL sql3 = getEasySQL("select count(DISTINCT(CUST_ID)) from yq_project_contract WHERE 1=1");
        sql3.append(EasyCalendar.newInstance().getYear(),"and YEAR = ?");
        if ("user".equals(param.getString("source"))) {
            sql3.append(getUserId(), "and  SALES_BY = ? ");
        } else if ("all".equals(param.getString("source"))) {
            sql3.append(0,"and SIGN_FLAG = ?");
        }
        String contractCustCount = queryForString(sql3.getSQL(), sql3.getParams());
        data.put("CONTRACT_CUST_COUNT", StringUtils.isBlank(contractCustCount) ? "0" : contractCustCount);

        result.put("data", data);
        return result;
    }

    @WebControl(name = "contractConsoleStat2", type = Types.RECORD)
    public JSONObject contractConsoleStat2() {

        EasySQL sql = getEasySQL("select ");
        sql.append("ROUND(SUM(t1.PLAN_RCV_AMT)/10000,2) AS SUM_STAGE");
        sql.append("from yq_contract_stage t1 ");
        sql.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("where 1=1");
        sql.append(EasyCalendar.newInstance().getYear(), "and t1.YEAR_ID = ?");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and t2.SALES_BY = ? ");
        }

        JSONObject result = queryForRecord(sql.getSQL(), sql.getParams());
        JSONObject data = result.getJSONObject("data");
        if (StringUtils.isBlank(data.getString("SUM_STAGE"))) {
            data.put("SUM_STAGE", "0");
        }

        EasySQL sql2 = getEasySQL("select ROUND(SUM(t1.AMOUNT)/10000,2) as SUM_RECEIPT from yq_contract_receipt t1 left join yq_project_contract t2 on t1.CONTRACT_ID = t2.CONTRACT_ID WHERE 1=1");
        if ("user".equals(param.getString("source"))) {
            sql2.append(getUserId(), "and t2.SALES_BY = ?");
        }
        sql2.append(EasyCalendar.newInstance().getYear(), "and t1.YEAR_ID = ?");
        String receiptSum = queryForString(sql2.getSQL(), sql2.getParams());
        data.put("SUM_RECEIPT", StringUtils.isBlank(receiptSum) ? "0" : receiptSum);

        double sumStage = Double.parseDouble(data.getString("SUM_STAGE"));
        double receiptSumDouble = Double.parseDouble(receiptSum);
        double unReceive = sumStage - receiptSumDouble;
        data.put("UN_RECEIVE", String.format("%.2f", unReceive));


        EasySQL sql3 = getEasySQL("select ");
        sql3.append(EasyCalendar.newInstance().getYear(), " ROUND(SUM(CASE WHEN YEAR_ID = ? THEN UNCONFIRM_NO_TAX_A ELSE 0 END)/10000,2) AS SUM_UNCONFIRM");
        sql3.append("from yq_contract_income_stage t1 ");
        if ("user".equals(param.getString("source"))) {
            sql3.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        }
        sql3.append("where 1=1");
        sql3.appendRLike(EasyCalendar.newInstance().getYear(),"and t1.PLAN_COMP_DATE like ?");
        sql3.append(EasyCalendar.newInstance().getYear(), "and  t1.INCOME_CONF_FLAG = 0 and t1.YEAR_ID = ?");
        if ("user".equals(param.getString("source"))) {
            sql3.append(getUserId(), "and  t2.SALES_BY = ? ");
        }
        String unconfirm = queryForString(sql3.getSQL(), sql3.getParams());
        data.put("SUM_UNCONFIRM", StringUtils.isBlank(unconfirm) ? "0" : unconfirm);


        EasySQL sql4 = getEasySQL("select ROUND(SUM(AMOUNT_NO_TAX)/10000,2) AS SUM_CONFIRM from yq_contract_income_confirm t1 ");
        if ("user".equals(param.getString("source"))) {
            sql4.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        }
        sql4.append("where 1=1 ");
        sql4.appendRLike(EasyCalendar.newInstance().getYear(),"and t1.CONFIRM_DATE like ?");
        sql4.append("and CONFIRM_TYPE = 'A'");
        if ("user".equals(param.getString("source"))) {
            sql4.append(getUserId(), "and  t2.SALES_BY = ? ");
        }
        String confirm = queryForString(sql4.getSQL(), sql4.getParams());
        data.put("SUM_CONFIRM", StringUtils.isBlank(confirm) ? "0" : confirm);

        for (String key : data.keySet()) {
            String value = data.getString(key);
            data.put(key, value + "万");
        }

        result.put("data", data);
        return result;
    }


    @WebControl(name = "contractStageConsoleStat", type = Types.RECORD)
    public JSONObject contractStageConsoleStat() {
        EasySQL sql = getEasySQL("select ");
        sql.append(" COUNT(*) AS COUNT_ALL,");
        sql.append(" CONCAT( ROUND ( SUM(PLAN_RCV_AMT - RECEIVED_AMT)/10000,2),'万') AS SUM_ALL,");
        sql.append(EasyCalendar.newInstance().getYear(), "SUM(CASE WHEN YEAR_ID = ? THEN 1 ELSE 0 END) AS COUNT_YEAR,");
        sql.append(EasyCalendar.newInstance().getYear(), "CONCAT( ROUND ( SUM(CASE WHEN YEAR_ID = ? THEN (PLAN_RCV_AMT - RECEIVED_AMT) ELSE 0 END)/10000,2),'万') AS SUM_YEAR,");
        sql.append(EasyCalendar.newInstance().getFullMonth(), " SUM(CASE WHEN MONTH_ID = ? THEN 1 ELSE 0 END) AS COUNT_MONTH,");
        sql.append(EasyCalendar.newInstance().getFullMonth(), "CONCAT( ROUND (  SUM(CASE WHEN MONTH_ID = ? THEN (PLAN_RCV_AMT - RECEIVED_AMT) ELSE 0 END)/10000,2),'万') AS SUM_MONTH,");
        sql.append(getDateAfter7Days(), "SUM(CASE WHEN PLAN_COMP_DATE <= ? ");
        sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"), " AND PLAN_COMP_DATE > ? THEN 1 ELSE 0 END) AS COUNT_DUE,");
        sql.append(getDateAfter7Days(), "CONCAT( ROUND ( SUM(CASE WHEN PLAN_COMP_DATE <= ? ");
        sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"), " AND PLAN_COMP_DATE > ? THEN (PLAN_RCV_AMT - RECEIVED_AMT) ELSE 0 END)/10000,2),'万') AS SUM_DUE,");
        sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"), "SUM(CASE WHEN PLAN_COMP_DATE <= ? THEN 1 ELSE 0 END) AS COUNT_OVER,");
        sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"), "CONCAT( ROUND ( SUM(CASE WHEN PLAN_COMP_DATE <= ? and PLAN_COMP_DATE != '' and PLAN_COMP_DATE is not null THEN (PLAN_RCV_AMT - RECEIVED_AMT) ELSE 0 END)/10000,2),'万') AS SUM_OVER");
        sql.append("from yq_contract_stage t1 ");
        if ("user".equals(param.getString("source"))) {
            sql.append("left join yq_project_contract t2 ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        }
        sql.append("where 1=1");
        sql.append("and  t1.ACT_COMP_DATE = '' ");
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  t2.SALES_BY = ? ");
        }

        JSONObject result = queryForRecord(sql.getSQL(), sql.getParams());
        JSONObject data = result.getJSONObject("data");
        for (String key : data.keySet()) {
            if (StringUtils.isBlank(data.getString(key))) {
                data.put(key, "0");
            }
        }
        result.put("data", data);
        return result;
    }


    public String getDateAfter7Days() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }


    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by ").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }




}

