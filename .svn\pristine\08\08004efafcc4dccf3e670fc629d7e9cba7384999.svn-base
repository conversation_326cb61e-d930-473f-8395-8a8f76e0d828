<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>项目仪表盘</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }


        .layui-this {
            font-weight: bold;
        }


        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .top-3 {
            font-size: 24px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .divInline * {
            display: inline;
        }

        .divInline {
            height: 24px;
        }


        .kq-stat .layui-card {
            padding: 10px;
            border-radius: 5px;
        }


        .layui-icon-tips {
            margin-left: 3px;
            font-size: 14px;
        }

        #fixedDiv {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 999;
            background: #fff;
            padding: 0px 10px;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        #searchForm {
            padding-top: 60px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .layui-card {
            border: 1px solid rgba(0, 0, 0, 0.1);
        }


    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm" autocomplete="off">
        <div class="layui-row" id="fixedDiv">
            <div class="form-group" style="flex-grow: 1;display: flex;margin-top: 10px;">
                <div class="input-group input-group-sm ml-5">
                    <span class="input-group-addon">考勤日期</span>
                    <input type="text" id="beginDate" name="beginDate" data-mars="CommonDao.yearBegin" data-laydate="{type:'date'}"
                           class="form-control input-sm" style="width: 90px;">
                    <input type="text" id="endDate" name="endDate" data-laydate="{type:'date'}" class="form-control input-sm"
                           style="width: 90px;">
                </div>
                <div class="input-group input-group-sm">
                    <button type="button" class="btn btn-sm btn-default ml-10" onclick="onTimeChange()"><span
                            class="glyphicon glyphicon-search"></span> 搜索
                    </button>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space10 kq-stat" data-mars="ProjectStatisticDao.projectDashboardStat">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="divInline">
                            <div class="top-1">0</div>
                        </div>
                        <div class="top-2">
                            本人出勤天数
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1">0</div>
                        <div class="top-2">出勤率</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1">0</div>
                        <div class="top-2">加班次数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-3">0</div>
                        <div class="top-2">迟早次数</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">月份趋势</div>
                    <div style="height: 360px">
                        <div id="monthOverChart" style="height: 340px;width: 100%;" data-anim="fade"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md7">
                <div class="layui-card">
                    <div class="layui-card-header">部门加班TOP20</div>
                    <div style="height: 360px">
                        <div id="deptOverChart1" style="height: 340px;width: 100%;" data-anim="fade"></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md5">
                <div class="layui-card">
                    <div class="layui-card-header">部门迟到TOP10</div>
                    <div style="height: 360px">
                        <div id="deptLateChart" style="height: 340px;width: 100%;" data-anim="fade"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">个人加班Top30</div>
                    <div style="height: 380px">
                        <div id="overChart1" style="height: 360px;width: 100%;" data-anim="fade"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">迟到时长TOP30</div>
                    <div style="height: 380px">
                        <div class="layui-col-md12">
                            <div id="lateChart1" style="height: 360px;width: 100%;" data-anim="fade"></div>
                        </div>
                        <div style="display: none" class="layui-card-body">
                            <table id="lateTable1"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">出勤天数排行</div>
                    <div style="height: 380px">
                        <div class="layui-col-md7">
                            <div id="workChart1" style="height: 360px;width: 100%;" data-anim="fade"></div>
                        </div>
                        <div class="layui-col-md5 layui-card-body">
                            <table id="workTable1"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

    <script>
        $(function () {

            $("#searchForm").render({
                success: function () {
                    onTimeChange();
                }
            });

        });

        function onTimeChange(){
            loadMonthOverChart();
            loadOverChart1();
            loadDeptOverChart1();
            loadLateTable1();
            loadDeptLateChart();
            loadWorkTable1();
        }
        function loadMonthOverChart(){
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            ajax.remoteCall("/yq-work/webcall?action=KqStatDao.overTimeStatByMonth", {"beginDate": beginDate, "endDate": endDate}, function (result) {
                var data2 = result.data;
                var year = data2['YEAR'];
                var months = ["01","02","03","04","05","06","07","08","09","10","11","12"];
                var monthNames = months.map(month => year+'_'+month);
                var nums1 = monthNames.map(monthName => data2["OVER_TIME_"+monthName]);
                var nums2 = monthNames.map(monthName => data2["OVER_TIME_COUNT_"+monthName]);
                var legend = ['加班总时长/小时','加班次数']
                loadBarAndLineChart(monthNames,nums1,nums2, 'monthOverChart', legend)
            })
            }

        function loadOverChart1(){
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();

            ajax.remoteCall("/yq-work/webcall?action=KqStatDao.overTimeRank", {"beginDate": beginDate, "endDate": endDate,"pageSize": 30}, function (result) {
                var data2 = result.data;
                data2 = data2.slice(0, 30);
                var userName = data2.map(item => item.USER_NAME);
                var nums1 = data2.map(item => Number(item.OVER_TIME).toFixed(1));
                var nums2 = data2.map(item => item.OVER_TIME_COUNT);
                var legend = ['加班总时长/小时','加班次数']
                loadBarAndLineChart(userName,nums1,nums2, 'overChart1', legend)

            })

        }

        function loadDeptOverChart1(){
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();

            ajax.remoteCall("/yq-work/webcall?action=KqStatDao.overTimeRankByDept", {"beginDate": beginDate, "endDate": endDate,"pageSize":"20"}, function (result) {
                var data2 = result.data;
                data2 = data2.slice(0, 30);
                var deptName = data2.map(item => item.DEPT);
                var nums1 = data2.map(item => Number(item.AVG_OVER_TIME).toFixed(1));
                var nums2 = data2.map(item => Number(item.AVG_OVER_TIME_COUNT).toFixed(0));
                var legend = ['人均总加班/小时','人均加班次数']
                loadBarAndLineChart(deptName,nums1,nums2, 'deptOverChart1', legend)

            })

        }


        function loadLateTable1(){
            $("#searchForm").initTable({
                mars:'KqStatDao.lateTimeRank',
                id:"lateTable1",
                cellMinWidth:30,
                limit:30,
                totalRow:false,
                toolbar:true,
                height: '340px',
                cols: [[
                    {
                        title: '编号',
                        type:'numbers'
                    },{
                        field: 'STAFF_ID',
                        title: '工号',
                        align:'left',
                        minWidth: 60,
                    },{
                        field: 'USER_NAME',
                        title: '姓名',
                        align:'left',
                        minWidth: 80,
                    },{
                        field: 'LATE_TIME_COUNT',
                        title: '迟到次数',
                        align:'left',
                        minWidth: 80,
                        sort: true,
                    },{
                        field: 'LATE_TIME',
                        title: '迟到时间/分',
                        align:'left',
                        minWidth: 110,
                        sort: true,
                        templet:function(row){
                            return Number(row['LATE_TIME']);
                        }
                    },{
                        field: 'WORK_DAY',
                        title: '工作天数',
                        align:'left',
                        minWidth: 80,
                    },
                ]],
                done: function (data) {
                    if ( data.pageNumber != 1 || data.pageSize > 30) {
                        return;
                    }
                    var data2 = data.data;
                    data2 = data2.slice(0, 30);
                    var userName = data2.map(item => item.USER_NAME);
                    var nums1 = data2.map(item => Number( item.LATE_TIME));
                    var nums2 = data2.map(item => item.LATE_TIME_COUNT);
                    var legend = ['迟到时间/分','迟到次数']
                    loadBarAndLine2yAxisChart(userName,nums1,nums2, 'lateChart1', legend)
                }
            })
        }

        function loadDeptLateChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            ajax.remoteCall("/yq-work/webcall?action=KqStatDao.lateTimeRankByDept", {"beginDate": beginDate, "endDate": endDate,"pageSize":"10"}, function (result) {
                var data2 = result.data;
                data2 = data2.slice(0, 10);
                var deptName = data2.map(item => item.DEPT);
                var nums1 = data2.map(item => Number(item.AVG_LATE_TIME));
                var nums2 = data2.map(item => Number(item.AVG_LATE_TIME_COUNT).toFixed(0));
                var legend = ['人均迟到时间/分','人均迟到次数']
                loadBarAndLine2yAxisChart(deptName,nums1,nums2, 'deptLateChart', legend)
            })
        }

        function loadWorkTable1(){
            $("#searchForm").initTable({
                mars:'KqStatDao.workDayRank',
                id:"workTable1",
                cellMinWidth:30,
                limit:20,
                totalRow:false,
                toolbar:true,
                height: '340px',
                cols: [[
                    {
                        title: '编号',
                        type:'numbers'
                    },{
                        field: 'STAFF_ID',
                        title: '工号',
                        align:'left',
                        minWidth: 60
                    },{
                        field: 'USER_NAME',
                        title: '姓名',
                        align:'left',
                        minWidth: 80,
                    },{
                        field: 'WORK_DAY',
                        title: '工作天数',
                        align:'left',
                        minWidth: 80,
                        sort:true,
                    },{
                        field: 'NOT_WORK',
                        title: '缺席天数',
                        align:'left',
                        minWidth: 80,
                        sort:true,
                    },{
                        field: '',
                        title: '出勤率(%)',
                        align:'left',
                        minWidth: 80,
                        templet:function(row){
                            return numDiv(row['WORK_DAY'],(Number(row['WORK_DAY'])+Number(row['NOT_WORK'])))*100;
                        }
                    },
                ]],
                done: function (data) {
                    if ( data.pageNumber != 1 || data.pageSize > 20) {
                        return;
                    }
                    var data2 = data.data;
                    data2 = data2.slice(0, 20);
                    var userName = data2.map(item => item.USER_NAME);
                    var nums1 = data2.map(item => item.WORK_DAY);
                    var nums2 = data2.map(row => numDiv(row['WORK_DAY'],(Number(row['WORK_DAY'])+Number(row['NOT_WORK'])))*100);
                    var legend = ['工作天数','出勤率(%)']
                    loadBarAndLine2yAxisChart(userName,nums1,nums2, 'workChart1', legend)
                }
            })
        }


        function loadBarChart(fieldName, amounts, tableName, legend) {
            var options = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '4%',
                    right: '4%',
                    bottom: '1%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '50%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: amounts,
                        label: {
                            show: true,
                            position: 'top'
                        },
                    }
                ],
            };

            var chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);

            return chart;
        }

        function loadBarAndLineChart(fieldName, amounts1, amounts2, tableName, legend) {
            var options = {
                color: ["#5470c6"],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '0%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '50%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: amounts1,
                        label: {
                            show: true,
                            position: 'top'
                        },
                    },
                    {
                        name: legend[1],
                        type: 'line',
                        data: amounts2,
                        smooth: false,
                        color:'#000',
                        label: {
                            color:'#fff',
                            textBorderColor:'#000',
                            textBorderWidth: 2,
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                if (Number(amounts1[params.dataIndex]) - Number(amounts2[params.dataIndex])< 3) {
                                    return '';
                                }
                                return amounts2[params.dataIndex];
                            },
                        },
                    }

                ],
            };

            var chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);

            return chart;
        }

        function loadBarAndLine2yAxisChart(fieldName, amounts1, amounts2, tableName, legend) {
            var sortedAmounts2 = [...amounts2].sort((a, b) => b - a);
            var ratio3 = sortedAmounts2[2];
            var options = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '0%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '50%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    } ,{
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: amounts1,
                        label: {
                            show: true,
                            position: 'top'
                        },
                    },
                    {
                        name: legend[1],
                        type: 'line',
                        data: amounts2,
                        smooth: false,
                        yAxisIndex: 1,
                        itemStyle: {
                            color:  "#000"
                        },
                        label: {
                            show: true,
                            position: 'top',
                            color:  "#FFF",
                            textBorderColor:'#000',
                            textBorderWidth: 1,
                            formatter: function (params) {
                                return amounts2[params.dataIndex] >= ratio3 ? amounts2[params.dataIndex] : '';
                            },
                        },
                    }

                ],
            };

            var chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);

            return chart;
        }

        function loadPieChart(echartsData, chartName) {
            var options1 = {
                color: ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"],
                tooltip: {
                    trigger: "item",
                    formatter: "{b}: {c}<br> ({d}%)"
                },
                grid: {
                    left: '35%',
                    right: '35%',
                    top: '1%',
                    bottom: '0%',
                    containLabel: false
                },
                series: [
                    {
                        name: "",
                        type: "pie",
                        radius: ['20%', '55%'],
                        data: echartsData,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{b}: {c} ({d}%)'
                        },
                        labelLine: {
                            show: true
                        }
                    },
                ]
            };
            var myCharts = echarts.init(document.getElementById(chartName));
            myCharts.setOption(options1);
        }

        var getDate = function (obj, startDate, endDate) {
            var val = obj.value;
            var bdate = new Date();
            var edate = new Date();
            var monthAdjustments = {
                'oneMonth': -1,
                'threeMonth': -3,
                'halfYear': -6,
                'oneYear': -12,
                'twoYear': -24
            };
            if (val in monthAdjustments) {
                bdate.setMonth(bdate.getMonth() + monthAdjustments[val]);
            } else if (val === 'nowYear') {
                bdate.setMonth(0, 1);
                edate.setMonth(11, 31);
            } else if (val === 'lastYear') {
                bdate.setFullYear(bdate.getFullYear() - 1, 0, 1);
                edate.setFullYear(edate.getFullYear() - 1, 11, 31);
            } else if (val === 'nextYear') {
                bdate.setFullYear(bdate.getFullYear() + 1, 0, 1);
                edate.setFullYear(edate.getFullYear() + 1, 11, 31);
            }
            var bdateVal = val && val !== 'all' ? DateUtils.dateFormat(bdate, 'yyyy-MM-dd') : '';
            var edateVal = val && val !== 'all' ? DateUtils.dateFormat(edate, 'yyyy-MM-dd') : '';
            $('[name="' + startDate + '"]').val(bdateVal);
            $('[name="' + endDate + '"]').val(edateVal);
        }

        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
