package com.yunqu.work.servlet.wx;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.util.List;

import javax.servlet.ServletException;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.StrKit;
import com.jfinal.weixin.sdk.api.ApiConfigKit;
import com.jfinal.weixin.sdk.api.ApiResult;
import com.jfinal.weixin.sdk.api.CustomServiceApi;
import com.jfinal.weixin.sdk.api.MenuApi;
import com.jfinal.weixin.sdk.api.SnsAccessToken;
import com.jfinal.weixin.sdk.api.SnsAccessTokenApi;
import com.jfinal.weixin.sdk.api.SnsApi;
import com.jfinal.weixin.sdk.api.SubscribeMsgApi;
import com.jfinal.weixin.sdk.api.TagApi;
import com.jfinal.weixin.sdk.api.TemplateData;
import com.jfinal.weixin.sdk.api.TemplateMsgApi;
import com.jfinal.weixin.sdk.api.UserApi;
import com.jfinal.weixin.sdk.jfinal.ApiController;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.AesUtils;

public class WeixinApiController extends ApiController {

	public void index() {
		renderHtml("index");
	}
	public void sendSubscribeMsg(){
		String openId=getPara("openId");
		String text=getPara("text");
		String title=getPara("title");
		ApiResult apiResult = SubscribeMsgApi.subscribe(openId, "sH3YSOruJNXGHpxVCCZVQXHCLO-F31tTi7hfE4AFh0Y","https://work.yunqu-info.cn", 100, title, text, "#fe7300");
		renderJson(apiResult.getJson());
	}
	public void sendTemplateMsg(){
		String first=getPara("first");
		String url=getPara("url");
		String remark=getPara("remark");
		String templateId=getPara("templateId");
		String openId=getPara("openId");
		String keyword1=getPara("keyword1");
		String keyword2=getPara("keyword2");
		String keyword3=getPara("keyword3");
		String keyword4=getPara("keyword4");
		
		TemplateData templateData=TemplateData.New();
		templateData.setTouser(openId);
		templateData.setTemplate_id(templateId);
		templateData.setUrl(url);
		templateData.add("first",first, "#4183c4");
		templateData.add("keyword1", keyword1, "#444");
		templateData.add("keyword2", keyword2, "#999");
		if(StrKit.notBlank(keyword3)){
			templateData.add("keyword3", keyword3, "#999");
		}
		if(StrKit.notBlank(keyword4)){
			templateData.add("keyword4", keyword4, "#999");
		}
		templateData.add("remark", remark, "#fe7300");
		ApiResult apiResult = TemplateMsgApi.send(templateData.build());
		String resultStr = "apiResult>openId:"+openId+">first>"+first+">templateId>"+templateId+">"+apiResult.getJson();
		if(apiResult.isSucceed()) {
			this.info(resultStr);
		}else {
			this.info(resultStr);
			this.error(resultStr, null);
			String resultText = apiResult.getJson();
			if(resultText.contains("43004")) {
				try {
					ServerContext.getAdminQuery().executeUpdate("update easi_user set OPEN_ID = '',NIKE_NAME = '--' where OPEN_ID = ?",openId);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}
		renderText(apiResult.getJson());
	}
	
	
	/**
	 * 获取公众号菜单
	 */
	public void getMenu() {
		ApiResult apiResult = MenuApi.getMenu();
		if (apiResult.isSucceed())
			renderText(apiResult.getJson());
		else
			renderText(apiResult.getErrorMsg());
	}
	public void getDelMenu() {
		ApiResult apiResult = MenuApi.deleteMenu();;
		if (apiResult.isSucceed())
			renderText(apiResult.getJson());
		else
			renderText(apiResult.getErrorMsg());
	}
	public void addMenu() {
		String jsonStr = "{\"button\":[{\"name\":\"常用功能\",\"sub_button\":[{\"type\":\"view\",\"name\":\"验证码\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=https://work.yunqu-info.cn/yq-work/security/code\"},{\"type\":\"view\",\"name\":\"考勤打卡\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=https://work.yunqu-info.cn/yq-work/kq\"}]},{\"name\":\"办公协同\",\"sub_button\":[{\"type\":\"view\",\"name\":\"首页\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=https://work.yunqu-info.cn/workbench/index\"},{\"type\":\"view\",\"name\":\"通讯录\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=https://work.yunqu-info.cn/yq-work/pages/ehr/address-list.html\"},{\"type\":\"view\",\"name\":\"新闻资讯\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=https://work.yunqu-info.cn/yq-work/news\"},{\"type\":\"view\",\"name\":\"任务\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=https://work.yunqu-info.cn/yq-work/mytask\"},{\"type\":\"view\",\"name\":\"周报\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=https://work.yunqu-info.cn/yq-work/receivedWeekly\"}]},{\"name\":\"流程审批\",\"sub_button\":[{\"type\":\"view\",\"name\":\"我的待办\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/flow/todo\"},{\"type\":\"view\",\"name\":\"我的申请\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/flow/my\"},{\"type\":\"view\",\"name\":\"已办流程\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/flow/done\"},{\"type\":\"view\",\"name\":\"抄送流程\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/flow/cc\"},{\"type\":\"view\",\"name\":\"发起流程\",\"url\":\"https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/flow/apply\"}]}],\"matchrule\":{\"tag_id\":\"2\",\"sex\":\"1\",\"country\":\"中国\",\"province\":\"广东\",\"city\":\"广州\",\"client_platform_type\":\"2\",\"language\":\"zh_CN\"}}";
		String menuStr = getPara("menuStr");
		if(StringUtils.notBlank(menuStr)) {
			jsonStr =  menuStr;
		}
		ApiResult apiResult = MenuApi.createMenu(jsonStr);;
		if (apiResult.isSucceed())
			renderText(apiResult.getJson());
		else
			renderText(apiResult.getErrorMsg());
	}
	
	public void authCallback(){
		String code = getPara("code");
		String userId = getPara("userId");
		info("code:"+code+",userId:"+userId);
		SnsAccessToken ansAccessToken= SnsAccessTokenApi.getSnsAccessToken(ApiConfigKit.getAppId(), ApiConfigKit.getApiConfig().getAppSecret(), code);
		//UserApi.getUserInfo(ansAccessToken.getOpenid());
		ApiResult apiResult = SnsApi.getUserInfo(ansAccessToken.getAccessToken(), ansAccessToken.getOpenid());
		info("authCallback>ansAccessToken:"+ansAccessToken.getJson());
		info("authCallback>apiResult:"+apiResult.getJson());
		
		Object subscribe=apiResult.get("subscribe");
		if(subscribe!=null&&apiResult.getInt("subscribe")==0){
			renderHtml("<p style='font-size:50px;text-align:center'>请先关注服务号。</p><div style='text-align:center'><img style='width:250px;height:250px;' src='https://work.yunqu-info.cn/qrcode_for_gh_562078549f9f_258.jpg'/></div>");
			return;
		}
		
		String openId=apiResult.getStr("openid");
		String nickname=apiResult.getStr("nickname");
		String headimgUrl=apiResult.getStr("headimgurl");
		if(StringUtils.notBlank(headimgUrl)) {
			headimgUrl=headimgUrl.replaceAll("\\/", "/");
		}
		
		if(StrKit.isBlank(userId)){
			 renderText("userId not empty.");
			 return;
		}
		if(StrKit.isBlank(openId)){
			error("userId:"+userId+">扫码报错："+apiResult.toString(), null);
			renderText("openId没获取到,请联系管理员.");
			return;
		}
		
		try {
			ServerContext.getAdminQuery().executeUpdate("update easi_user set OPEN_ID = ?,PIC_URL = ?,NIKE_NAME = ? where user_id = ?",openId,headimgUrl,nickname,userId);
			UserApi.updateRemark(openId, ServerContext.getAdminQuery().queryForString("select concat(depts,'/',username) from easi_user where user_id = ?", userId));
		} catch (SQLException e) {
			error(e.getMessage(),e);
			try {
				ServerContext.getAdminQuery().executeUpdate("update easi_user set OPEN_ID = ?,PIC_URL = ? where user_id = ?",openId,headimgUrl,userId);
			} catch (SQLException e1) {
				error(e1.getMessage(),e1);
			}
		}
		renderHtml("<h1 style='text-align:center;font-size:50px;'>绑定/更新信息成功,请关闭重新打开。</h1>");
	}
	
	public void updateAllRemark() {
		try {
			List<JSONObject> list = ServerContext.getAdminQuery().queryForList("select OPEN_ID,USERNAME,DEPTS from easi_user where OPEN_ID<>''", new Object[] {}, new JSONMapperImpl());
			for(JSONObject object:list) {
				UserApi.updateRemark(object.getString("OPEN_ID"),object.getString("DEPTS")+"/"+object.getString("USERNAME"));
			}
		} catch (SQLException e) {
			error(e.getMessage(),e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void createTag() {
		try {
			List<JSONObject> list = ServerContext.getAdminQuery().queryForList("select DEPT_NAME from easi_dept", new Object[] {}, new JSONMapperImpl());
			for(JSONObject object:list) {
				TagApi.create(object.getString("DEPT_NAME"));
			}
		} catch (SQLException e) {
			error(e.getMessage(),e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void  sendTextMsg() {
		String msg = getPara("msg");
		String openId = getPara("openId");
		if(StringUtils.isBlank(msg)) {
			msg = "test";
		}
		if(StringUtils.isBlank(openId)) {
			openId =  "oBgmx0Vrpyuxb1Px80YFEAwT0iFM";
			msg = "open id is null;"+msg;
		}
		ApiResult sendText = CustomServiceApi.sendText(openId, msg);
		info(sendText.getJson());
		renderJson(sendText);
	}
	
	
	public void go(){
		String toUrl = getPara("url");
		String callbackUrl = "https://work.yunqu-info.cn/yq-work/api/loginCallback";
		String url = SnsAccessTokenApi.getAuthorizeURL(ApiConfigKit.getAppId(), callbackUrl,toUrl,true);
		info("go>"+url);
		redirect(url);
	}
	
	public void getAuthorize(){
		String userId=getPara("userId");
		if(StrKit.isBlank(userId)){
			 renderText("userId not empty.");
			 return;
		}
		String goUrl = "https://work.yunqu-info.cn/yq-work/api/authCallback?userId="+userId;
		String url = SnsAccessTokenApi.getAuthorizeURL(ApiConfigKit.getAppId(),goUrl ,false);
		redirect(url);
//		String url = SnsAccessTokenApi.getAuthorizeURL(ApiConfigKit.getAppId(),goUrl ,false);
//		String oauth2LoginUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=APPID&redirect_uri=REDIRECT_URI&response_type=code&scope=SCOPE&state=STATE#wechat_redirect";
//		oauth2LoginUrl = oauth2LoginUrl.replace("APPID", ApiConfigKit.getAppId()).replace("REDIRECT_URI", goUrl).replace("SCOPE", "snsapi_userinfo");
//		redirect(oauth2LoginUrl);
	}
	
	public void loginRemind() {
		String receiver = getPara("id");
		String ip = getPara("ip");
		String openId= WxMsgService.getService().getOpenId(receiver);
		StaffModel model = StaffService.getService().getStaffInfo(receiver);
		TemplateData templateData=TemplateData.New();
		templateData.setTouser(openId);
		templateData.setTemplate_id("pD_HzgACGJdpFG0I7QRw-bNwlqYmlp8JA58tX07Oo4s");
		templateData.setUrl("https://work.yunqu-info.cn/yq-work/api/goUrl?url=/workbench/index");
		templateData.add("first","您在 "+ip+" 登录成功", "#4183c4");
		templateData.add("keyword1", model.getStaffNo(), "#444");
		templateData.add("keyword2", "您在 "+ip+" 登录成功", "#999");
		templateData.add("keyword3", EasyDate.getCurrentDateString(), "#999");
		templateData.add("remark", "如非本人操作,可能账号已经被盗，请立即修改密码或联系管理员。", "#fe7300");
		ApiResult apiResult = TemplateMsgApi.send(templateData.build());
		renderJson(apiResult);
	}
	
	public void loginCallback(){
		String code = getPara("code");
		String url = getPara("state");
		SnsAccessToken ansAccessToken= SnsAccessTokenApi.getSnsAccessToken(ApiConfigKit.getAppId(), ApiConfigKit.getApiConfig().getAppSecret(), code);
		ApiResult apiResult = UserApi.getUserInfo(ansAccessToken.getOpenid());
		info("openId:"+ansAccessToken.getOpenid());
		info(apiResult.getJson());
		if(!url.contains("?")) {
			url = url+"?";
		}
		this.info("loginCallback>url>"+url);
		url = url.replaceAll("@", "&");
		CacheManager.getDefaultCache().put(ansAccessToken.getOpenid(), System.currentTimeMillis());
		JSONObject data = new JSONObject();
		data.put("id",ansAccessToken.getOpenid());
		data.put("ts",System.currentTimeMillis());
		String resultStr = data.toJSONString();
		String token = AesUtils.encrypt(resultStr);
		try {
			token = URLEncoder.encode(token,"UTF-8");
		} catch (UnsupportedEncodingException e) {
			this.error(e.getMessage(), e);
		}
		url = url +"&token="+token;
		this.info("loginCallback>"+url);
		redirect(url);
	}
	
	
	public void callback(){
		String code = getPara("code");
		String url = getPara("state");
		info("code:"+code+">state:"+url);
		if(StringUtils.notBlank(url)) {
			url = url.replaceAll("_A", "=");
			url = url.replaceAll("_B", "&");
			if(url.startsWith("/yq-work")||url.startsWith("/workbench")) {
				url = "https://work.yunqu-info.cn"+url;
			}else if(url.startsWith("/pages")) {
				url = "https://work.yunqu-info.cn/yq-work";
			}
		}
		info("redirect_url:"+url);
		SnsAccessToken ansAccessToken= SnsAccessTokenApi.getSnsAccessToken(ApiConfigKit.getAppId(), ApiConfigKit.getApiConfig().getAppSecret(), code);
		
		ApiResult apiResult = UserApi.getUserInfo(ansAccessToken.getOpenid());
		info("openId:"+ansAccessToken.getOpenid());
		info(apiResult.getJson());
		try {
			String userId = ServerContext.getAdminQuery().queryForString("select USER_ID from easi_user where open_id  = ?",ansAccessToken.getOpenid());
			if(StringUtils.isBlank(userId)) {
				renderHtml("<p style='font-size:50px;text-align:center'>请先做好账号和公众号绑定.</p>");
				return;
			}
			if(getRequest().getRemoteUser()==null) {
				JSONObject object = ServerContext.getAdminQuery().queryForRow("select t1.*,t2.PIC_URL from easi_user_login t1,easi_user t2 where t1.user_id = t2.user_id and  t1.ACCT_STATE = 0 and t1.USER_ID = ?", new Object[] {userId}, new JSONMapperImpl());
				try {
					if(object!=null) {
						String picUrl = object.getString("PIC_URL");
						if(StringUtils.isBlank(picUrl)) {
							info(userId+">重新更新微信数据.");
							String goUrl = "https://work.yunqu-info.cn/yq-work/api/authCallback?userId="+userId;
							String _url = SnsAccessTokenApi.getAuthorizeURL(ApiConfigKit.getAppId(),goUrl ,false);
							redirect(_url);
							return;
						}
						LogEngine.getLogger("mars-sso").info(object.getString("USER_ACCT")+">微信登录");
						getRequest().login(object.getString("USER_ACCT"),object.getString("USER_PWD"));
					}else {
						renderHtml("账号已暂停使用.");
						return;
					}
				} catch (ServletException e) {
					this.error(e.getMessage(), e);
					renderHtml("登录失败,"+e.getMessage());
					return;
				}
			}
			redirect(url);
			return;
		} catch (SQLException e) {
			this.error(null, e);
			renderHtml("跳转失败,"+e.getMessage());
		}
	}
	
	
	
	public void goUrl(){
		String toUrl = getPara("url");
		info("url1>>>>>>>>>"+toUrl);
		if(getRequest().getRemoteUser()==null) {
			String url = SnsAccessTokenApi.getAuthorizeURL(ApiConfigKit.getAppId(), "https://work.yunqu-info.cn/yq-work/api/callback",toUrl,true);
			info("ur2>>>>>>>>>"+url);
			redirect(url);
		}else {
			String url = toUrl;
			if(StringUtils.notBlank(url)) {
				url = url.replaceAll("_A", "=");
				url = url.replaceAll("_B", "&");
				if(url.startsWith("/yq-work")||url.startsWith("/workbench")||url.startsWith("/yq-lucky")) {
					url = "https://work.yunqu-info.cn"+url;
				}else if(url.startsWith("/pages")) {
					url = "https://work.yunqu-info.cn/yq-work";
				}
			}
			redirect(url);
		}
	}
	
	/**
	 * 获取公众号关注用户
	 */
	public void getFollowers() {
		ApiResult apiResult = UserApi.getFollows();
		renderText(apiResult.getJson());
	}
	
	protected void info(String msg) {
		Logger logger = LogEngine.getLogger("weixin","weixin");
		logger.info(msg);
	}
	
	protected void error(String msg,Throwable throwable) {
		Logger logger = LogEngine.getLogger("weixin","weixin");
		logger.error(msg,throwable);
	}
}
