package com.yunqu.work.servlet.crm;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;

import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/invoice/*")
public class InvoiceServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_crm_invoice","invoice_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	private EasyRecord getTitleModel(String prefix){
		EasyRecord model = new EasyRecord("yq_crm_invoice_title","title_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	public EasyResult actionForAdd(){
		EasyRecord model = getModel("invoice");
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("invoice");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDel(){
		try {
			this.getQuery().executeUpdate("delete from yq_crm_invoice where invoice_id = ?", getJsonPara("invoiceId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForAddTitle(){
		EasyRecord model = getTitleModel("title");
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateTitle(){
		EasyRecord model = getTitleModel("title");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelTitle(){
		try {
			this.getQuery().executeUpdate("delete from yq_crm_invoice_title where title_id = ?", getJsonPara("titleId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	

}
