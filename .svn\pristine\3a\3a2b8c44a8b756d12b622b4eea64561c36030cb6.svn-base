<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>报销人员设置</title>
	<style>
	
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form id = "searchForm"  name = "searchForm" onsubmit="return false;" class="form-inline" autocomplete="off">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>费用部门</h5>
	          		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">部门</span>	
							 <input type="text" name="deptName" class="form-control input-sm" style="width: 90px;">
					     </div>
	        		     <div class="input-group input-group-sm">
							 <span class="input-group-addon">状态</span>	
							 <select name="deptState" class="form-control input-sm" onchange="queryData();">
							 	<option value="0" selected="selected">使用中</option>
							 	<option value="1">已删除</option>
							 </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						  <div class="input-group input-group-sm pull-right">
						  	  <input type="hidden" id="deptId">
						  	  <input type="text" id="deptName" class="form-control input-sm mr-5" onclick="singleDept(this)" style="width: 90px;">
							 <button type="button" class="btn btn-sm btn-info" onclick="addDept()">新增部门</button>
						 </div>
	          	     </div>
	              </div> 
				<div class="ibox-content">
				    <table class="table table-auto table-bordered table-hover table-condensed" id="tableHead">
                           <thead>
                        	  <tr>
                        	      <th>序号</th>
                        	      <th>部门</th>
                        	      <th>部门主管</th>
						          <th>费用会计</th>
						          <th>中心副总</th>
						          <th>财务总监</th>
						          <th>总裁</th>
						          <th>会计主管</th>
						          <th>模板</th>
						          <th>部门编码(财务)</th>
						          <th>操作</th>
   						      </tr>
                           </thead>
                           <tbody id="dataList"  data-mars="BxDao.allDept" data-mars-reload="true" data-template="list-template">
                           </tbody>
                    </table>
                    <script id="list-template" type="text/x-jsrender">
								   {{for  data}}
										<tr>
											<td>{{:#index+1}}</td>
											<td>{{:D_NAME}}</td>
											<td>
												<input type="hidden" value="{{:DEPT_MGR_ID}}" name="DEPT_MGR_ID_{{:D_ID}}">
												<input style="width:80px;" class="form-control input-sm" onclick="singleUser(this)" value="{{:DEPT_MGR_NAME}}" name="DEPT_MGR_NAME_{{:D_ID}}">
											</td>
											<td>
												<input type="hidden" value="{{:ACCT_ID}}" name="ACCT_ID_{{:D_ID}}">
												<input style="width:80px;" class="form-control input-sm" onclick="singleUser(this)" value="{{:ACCT_NAME}}" name="ACCT_NAME_{{:D_ID}}">
											</td>
											<td>
												<input type="hidden" value="{{:CENTER_ID}}" name="CENTER_ID_{{:D_ID}}">
												<input style="width:80px;" class="form-control input-sm" onclick="singleUser(this)" value="{{:CENTER_NAME}}" name="CENTER_NAME_{{:D_ID}}">
											</td>
											<td>
												<input type="hidden" value="{{:CFO_ID}}" name="CFO_ID_{{:D_ID}}">
												<input style="width:80px;" class="form-control input-sm" onclick="singleUser(this)" value="{{:CFO_NAME}}" name="CFO_NAME_{{:D_ID}}">
											</td>
											<td>
												<input type="hidden" value="{{:CEO_ID}}" name="CEO_ID_{{:D_ID}}">
												<input style="width:80px;" class="form-control input-sm" onclick="singleUser(this)" value="{{:CEO_NAME}}" name="CEO_NAME_{{:D_ID}}">
											</td>
											<td>
												<input type="hidden" value="{{:ACCT_MGR_ID}}" name="ACCT_MGR_ID_{{:D_ID}}">
												<input style="width:80px;" class="form-control input-sm" onclick="singleUser(this)" value="{{:ACCT_MGR_NAME}}" name="ACCT_MGR_NAME_{{:D_ID}}">
											</td>
											<td>
												<select class="form-control input-sm tempName" data-value="{{:TEMP_NAME}}" name="TEMP_NAME_{{:D_ID}}" style="width:80px;">
													<option value="管理部门">管理部门</option>
													<option value="销售部门">销售部门</option>
													<option value="工程研发部门">工程研发部门</option>
												</select>
											</td>
											<td>
												<input class="form-control input-sm" value="{{:FINANCE_DEPT_CODE}}" name="FINANCE_DEPT_CODE_{{:D_ID}}" style="width:80px;"/>
											</td>
											<td>
                                                <a href="javascript:void(0)" onclick="Config.saveData('{{:D_ID}}','{{:DD_NAME}}','{{:D_CODE}}','{{:ROOT_ID}}')" > 提交</a>
                                                <a href="javascript:void(0)" onclick="Config.delData('{{:D_ID}}')" > 删除 </a>
                                            </td>                                       
									    </tr>
								    {{/for}}					         
				    </script>
				</div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		
		$(function(){
			queryData();
		});
		
		jQuery.namespace("Config");
		
		function queryData(){
			$("#searchForm").render({success:function(){
				$(".tempName").each(function(){
					 $(this).val($(this).data('value'));
				});
			}});
		}
		
		Config.saveData = function(deptId,deptName,deptCode,rootId){
			var data = form.getJSONObject("#searchForm");
			data['deptId'] = deptId;
			data['deptName'] = deptName;
			data['deptCode'] = deptCode;
			data['rootId'] = rootId;
			ajax.remoteCall("/yq-work/servlet/bx/conf?action=savePeople",data,function(result) {
				if(result.state == 1){
					top.layer.msg(result.msg);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Config.delData = function(deptId){
			ajax.remoteCall("/yq-work/servlet/bx/conf?action=delPeople",{deptId:deptId},function(result) {
				if(result.state == 1){
					top.layer.msg(result.msg);
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function addDept(){
			var deptName = $('#deptName').val();
			if(deptName==''){
				layer.msg('请选择部门');
				return;
			}
			var deptId = $('#deptId').val();
			ajax.remoteCall("/yq-work/servlet/bx/conf?action=addBxDept",{deptId:deptId},function(result) {
				if(result.state == 1){
					queryData();
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>