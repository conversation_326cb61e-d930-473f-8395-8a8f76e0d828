<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>todo</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="TodoDao.record" autocomplete="off" data-mars-prefix="todo.">
     		   <input type="hidden" value="${param.todoId}" name="todo.TODO_ID"/>
						<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td style="width: 80px" class="required">日程名称</td>
				                    <td><input data-rules="required"  type="text" name="todo.TITLE" class="form-control input-sm"></td>
					            </tr>
					             <tr>
					                    <td class="required">起始时间</td>
					                    <td>
					                    	<input id="beginDate" type="text" style="display: inline-block;width: 140px" data-rules="required" data-mars="CommonDao.date02" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" name="todo.BEGIN_TIME"  class="form-control input-sm Wdate">
					                    	<input id="endDate" type="text" style="display: inline-block;width: 140px" data-rules="required" placeholder="截至时间" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',startDate:'%y-%M-%d 18:00:00',doubleCalendar:true,alwaysUseStartDate:true})" name="todo.END_TIME" class="form-control input-sm Wdate">
					                   		<div class="btn-group btn-group-sm">
					                   			<button onclick="fillDate(1)" class="btn btn-sm btn-default" type="button">1天</button>
					                   			<button onclick="fillDate(2)" class="btn btn-sm btn-default" type="button">2天</button>
					                   			<button onclick="fillDate(3)" class="btn btn-sm btn-default" type="button">3天</button>
					                   			<button onclick="fillDate(7)" class="btn btn-sm btn-default" type="button">7天</button>
					                   			<button onclick="fillDate(15)" class="btn btn-sm btn-default" type="button">15天</button>
					                   			<button onclick="fillDate(30)" class="btn btn-sm btn-default" type="button">30天</button>
					                   		</div>
					                    </td>
					            </tr>
					            <tr>
				                    <td>优先级</td>
				                    <td>
				                    	<label class="radio radio-success radio-inline">
					                    	<input type="radio" checked="checked" value="1" name="todo.LEVEL"/><span>一般</span>				                    	
				                    	</label>
				                    	<label class="radio radio-success radio-inline">
					                    	<input type="radio" value="2" name="todo.LEVEL"/> <span>最高</span>
				                    	</label>
				                    	<label class="radio radio-success radio-inline">
					                    	<input type="radio" value="3" name="todo.LEVEL"/> <span>较高</span>
				                    	</label>
				                    	<label class="radio radio-success radio-inline">
					                    	<input type="radio" value="4" name="todo.LEVEL"/> <span>较低</span>
				                    	</label>
				                    </td>
					            </tr>
					            <tr>
				                    <td>状态</td>
				                    <td>
				                    	<label class="radio radio-info radio-inline">
					                    	<input type="radio" checked="checked" value="1" name="todo.STATUS"/><span>未开始</span>				                    	
				                    	</label>
				                    	<label class="radio radio-info radio-inline">
					                    	<input type="radio" value="2" name="todo.STATUS"/> <span>进行中</span>
				                    	</label>
				                    	<label class="radio radio-info radio-inline">
					                    	<input type="radio" value="3" name="todo.STATUS"/> <span>已完成</span>
				                    	</label>
				                    	<label class="radio radio-info radio-inline">
					                    	<input type="radio" value="4" name="todo.STATUS"/> <span>已暂停</span>
				                    	</label>
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required" style="vertical-align: top;">描述</td>
				                    <td>
				                       <div id="editor"></div>
			                           <textarea id="wordText" data-text="false" style="height: 500px;width:70%;display: none;" class="form-control input-sm" name="todo.TODO_DESC"></textarea>
				                    
				                    </td>
					            </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c" style="z-index: 99999;">
						 			<c:if test="${!empty param.todoId }">
							    	  <button type="button" class="btn btn-warning btn-sm mr-15"  onclick="Edit.del()"> 删除 </button>
						 			</c:if>
						    	  <button type="button" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("Edit");
		Edit.todoId='${param.todoId}';
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.config.menus = [
   		    'code',  // 代码
   		    'head',  // 标题
   		    'bold',  // 粗体
   		    'fontSize',  // 字号
   		    'fontName',  // 字体
   		    'foreColor',  // 文字颜色
   		    'link',  // 插入链接
   		    'list',  // 列表
   		    'justify',  // 对齐方式
   		    'quote',  // 引用
   		    'emoticon',  // 表情
   		    'image',  // 插入图片
   		    'table',  // 表格'
   		 ];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.config.onchange = function (html) {
		     $("#wordText").val(html);
		}
		editor.create();
		
		$(function(){
			$("#editForm").render({success:function(result){
				 editor.txt.html($("#wordText").val());
				 var height=$(window).height();
				 $(".w-e-text-container").css("height",160);
			}});
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.todoId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/todo?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadTodo();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/todo?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						reloadTodo();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.del=function(){
			layer.confirm("确认要删除吗?",{icon:3},function(){
				ajax.remoteCall("${ctxPath}/servlet/todo?action=delete",{'todo.TODO_ID':Edit.todoId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							reloadTodo();
							layer.closeAll();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
				
			});
		}

		function fillDate(val){
			var v = addDate(val);
			if(v.indexOf("NaN")>-1){
				layer.msg("时间格式不正确,请检查浏览器兼容性或者换chome");
			}else{
				$("#endDate").val(v);
			}
		}
		function addDate(days) {
            if (days == undefined || days == '') {
                days = 1;
            }
            var d=$("#beginDate").val();
            if(d){
	            var date = new Date(d.replace(/-/g,'/'));
	            date.setDate(date.getDate() + days);
	            var month = date.getMonth() + 1;
	            var day = date.getDate();
	            var h = date.getHours();
	            var m = date.getMinutes();
	            return date.getFullYear() + '-' + getFormatDate(month) + '-' + getFormatDate(day)+" 18:00";
            }else{
            	return "";
            }
        }
		
		function getFormatDate(arg) {
            if (arg=='0'||arg) {
	            var re = arg + '';
	            if (re.length < 2) {
	                re = '0' + re;
	            }
	            return re;
            }else{
                return '';
            }
        }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>