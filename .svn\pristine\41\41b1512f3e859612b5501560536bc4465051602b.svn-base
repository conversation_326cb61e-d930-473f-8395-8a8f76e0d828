<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>选择款项</title>
</EasyTag:override>
<EasyTag:override name="content">
    <form method="post" class="form-inline" onsubmit="return false;" id="PaymentSelectForm">
        <input type="hidden" name="contractId" value="${param.contractId}"/>
        <input type="hidden" name="incomeStageId" value="${param.incomeStageId}"/>
        <div class="row">
            <div class="input-group input-group-sm ml-15" style="width: 200px">
                <span class="input-group-addon">款项类型</span>
                <SELECT name="paymentType1" data-mars="ContractPaymentDao.getPaymentType" class="form-control">
                    <option value="">全部</option>
                </SELECT>
            </div>
            <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter" onclick="PaymentSelect.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
        </div>
        <div class="ibox">
            <table id="PaymentSelectList"></table>
        </div>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button" onclick="PaymentSelect.ok()">确定</button>
            <button class="btn btn-sm btn-default ml-20" type="button" onclick="popup.layerClose(this)">关闭</button>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">

    <script type="text/javascript">
        var PaymentSelect = {
            sid: '${param.sid}',
            contractId: '${param.contractId}',

            query: function () {
                $("#PaymentSelectForm").queryData({id: 'PaymentSelectList'});
            },


            initTable: function () {

                $("#PaymentSelectForm").initTable({
                        mars: 'ContractPaymentDao.paymentListForSelect',
                        id: 'PaymentSelectList',
                        page: false,
                        limit: 10,
                        height: '400px',
                        cellMinWidth: 60,
                        cols: [[
                            {type: '${param.type}'},
                            {
                                field: 'DICT_NAME',
                                title: '款项类型',
                                width: 125,
                                // templet: function (d) {
                                //     return getText(d.PAYMENT_TYPE, 'paymentType1');
                                // }
                            }, {
                                field: 'DICT_VALUE',
                                title: '税率%',
                                width: 50
                            }, {
                                field: 'RATIO',
                                title: '款项占比%',
                                width: 80
                            }, {
                                field: 'AMOUNT_WITH_TAX',
                                title: '含税金额',
                                width: 90
                            }, {
                                field: 'AMOUNT_NO_TAX',
                                title: '税后金额',
                                width: 90
                            }, {
                                field: 'TOTAL_RATIO',
                                title: '剩余未选比例%',
                                width: 110,
                                templet: function (d) {
                                    var remain = 100 - Number(d.TOTAL_RATIO);
                                    return remain.toFixed(2);
                                }
                            },{
                                field: 'STAGE_RATIO',
                                title: '原收入确认比例%',
                                width: 120,
                                hide:true
                            },{
                                field: 'OLD_STAGE_AMOUNT',
                                title: '原收入确认金额(计算最大可选)',
                                width: 120,
                                hide:true
                            },{
                                field: 'TOTAL_AMOUNT',
                                title: '总已被选金额(用于计算最大可选)',
                                width: 120,
                                hide:true
                            },{
                                field: 'CHOSEN_RATIO',
                                title: '收入确认比例%',
                                edit: 'text',
                                width: 110
                            },{
                                field: 'STAGE_AMOUNT',
                                title: '收入确认金额(含税)',
                                edit: 'text',
                                width: 130,
                            }
                        ]], done: function () {


                            table.on('edit(PaymentSelectList)', function (obj) {
                                var field = obj.field;
                                var value = obj.value // 得到修改后的值
                                var data = obj.data // 得到所在行所有键值
                                var num = Number(value);

                                var maxAmount = Number(data.AMOUNT_WITH_TAX) - Number( data.TOTAL_AMOUNT) + Number(data.OLD_STAGE_AMOUNT) ;
                                var maxRatio = 100 - Number(data.TOTAL_RATIO) + Number(data.STAGE_RATIO);
                                if(field == "CHOSEN_RATIO"){
                                    if (!(num >= 0.01 && num <= maxRatio)) {
                                        layer.tips('比例必须为0.01-' + maxRatio.toFixed(2) + '之间的数字', this, {tips: 1});
                                        return obj.reedit(); // 重新编辑
                                    }
                                    var update = {};
                                    update[field] = num;
                                    var chosenAmount =  num * Number(data.AMOUNT_WITH_TAX) / 100;
                                    update["STAGE_AMOUNT"] = chosenAmount.toFixed(2);
                                    obj.update(update, true);
                                }else if (field == "STAGE_AMOUNT"){
                                    if (maxAmount!='NaN' && num > maxAmount){
                                        layer.tips('金额必须为小于' + maxAmount.toFixed(2) + '的数字', this, {tips: 1});
                                        return obj.reedit(); // 重新编辑
                                    }
                                    var update = {};
                                    update[field] = num;
                                    var chosenRatio =  num * 100 /  Number( data.AMOUNT_WITH_TAX);
                                    update["CHOSEN_RATIO"] = chosenRatio.toFixed(2);
                                    obj.update(update, true);
                                }
                            });

                        }
                    }
                );
            },
            ok: function (selectRow, obj) {

                var el = $("[data-sid='" + PaymentSelect.sid + "']");

                //用勾选后确定
                var checkStatus = table.checkStatus('PaymentSelectList');
                if (checkStatus.data.length > 0) {
                    if ($("span[name='paymentNames']") != undefined || $("span[name='paymentNames']") != null) {
                        $("span[name='paymentNames']").remove();
                    }
                    var data = checkStatus.data;
                    var amount = 0;
                    var amountNoTax = 0;
                    var paymentIds = [];
                    var paymentName = [];
                    for (var index in data) {

                        if (data[index]['CHOSEN_RATIO'] == undefined || data[index]['CHOSEN_RATIO'] == null || data[index]['CHOSEN_RATIO'] == '') {
                            layer.msg("请正确填写确认比例");
                            return;
                        }
                        var choseRatio = Number(data[index]['CHOSEN_RATIO']);
                        paymentIds.push(data[index]['PAYMENT_ID']);
                        var choseMoney = Number(data[index]['STAGE_AMOUNT']);
                        var choseMoneyNoTax =  Number(data[index]['STAGE_AMOUNT']) * 100 / (100 + Number(data[index]['DICT_VALUE']));
                        amount = amount + choseMoney;
                        amountNoTax = amountNoTax + choseMoneyNoTax;
                        paymentName.push(data[index]['DICT_NAME'] + "--款项金额(税前):" + data[index]['AMOUNT_WITH_TAX']+ "--收入确认比例:" + choseRatio + "%--计划收款金额(含税):" + choseMoney.toFixed(2)+"-税后:"+choseMoneyNoTax.toFixed(2));
                        data[index]['AMOUNT_WITH_TAX'] = data[index]['STAGE_AMOUNT'];
                        data[index]['AMOUNT_NO_TAX'] = choseMoneyNoTax.toFixed(2);
                    }
                    var total = {
                        AMOUNT_WITH_TAX: amount.toFixed(2),
                        AMOUNT_NO_TAX: amountNoTax.toFixed(2),
                        PAYMENT_ID: paymentIds.join(","),
                        PAYMENT_NAME: paymentName.join(","),
                        paymentNums: data.length,
                        extendInfo: data
                    };
                    PaymentSelectCallBack(total);
                    popup.layerClose("PaymentSelectForm");
                } else {
                    layer.msg("请选择!");
                }

            }
        };
        $(function () {
            $('[name="paymentType1"]').render({
                success: function (result) {
                    PaymentSelect.initTable();
                }
            });
        });

        function reloadPaymentSelectList() {
            PaymentSelect.query();
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>