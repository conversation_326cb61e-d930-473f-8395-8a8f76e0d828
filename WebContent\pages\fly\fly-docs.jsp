<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>云趣乐享平台</title>
</EasyTag:override>
<EasyTag:override name="content">
 <div class="layui-container">
  <div class="layui-row layui-col-space15">
     <div class="layui-col-md8">
      <div class="fly-panel" style="margin-bottom: 0;">
        <div class="fly-panel-title fly-filter">
          <a href="/yq-work/fly/docs" class="layui-this">综合</a>
          <span class="fly-mid"></span>
          <a href="/yq-work/fly/docs?state=1">未结</a>
          <span class="fly-mid"></span>
          <a href="/yq-work/fly/docs?state=2">已结</a>
          <span class="fly-mid"></span>
          <a href="/yq-work/fly/docs?state=3">精华</a>
          <span class="fly-filter-right layui-hide-xs">
            <a href="/yq-work/fly/docs?sort=time" class="layui-this">按最新</a>
            <span class="fly-mid"></span>
            <a href="/yq-work/fly/docs?sort=hot">按热议</a>
          </span>
        </div>
        <ul class="fly-list"> 
           <c:if test="${empty allList}">
        	 <div class="fly-none">没有相关数据</div> 
           </c:if>
          <c:forEach items="${allList}" var="item">
        		<li>
           		   <a href="/yq-work/fly/u/${item.user_id}" class="fly-avatar">
              		<img src="${item.pic_url}" onerror="this.src='/yq-work/static/images/user-avatar-large.png'">
           	 	   </a>
	            <h2>
	              <a class="layui-badge">${item.category_name}</a>
	              <a href="/yq-work/fly/docs/${item.doc_id}">${item.title}</a>
	            </h2>
	            <div class="fly-list-info">
	              <a href="/yq-work/fly/u/${item.user_id}" link>
	                <cite>${item.username}</cite>
	                <c:if test="${item.level>2}">
		                <i class="iconfont icon-renzheng" title="认证信息：${item.job_title}"></i>
		                <i class="layui-badge fly-badge-vip">VIP${item.level}</i>
	                </c:if>
	              </a>
	              <span>${item.create_time}</span>
	              <c:if test="${item.experience>0}">
		              <span class="fly-list-kiss layui-hide-xs" title="悬赏积分"><i class="iconfont icon-kiss"></i> ${item.experience}</span>
	              </c:if>
	              <c:if test="${item.close_flag=='1'}">
		              <span class="layui-badge fly-badge-accept layui-hide-xs">已结</span>
	              </c:if>
	              <span class="fly-list-nums"> 
	                <i class="iconfont icon-pinglun1" title="回答"></i> ${item.comment_count}
	              </span>
	            </div>
	            <div class="fly-list-badge">
	            	<c:if test="${item.wonderful_flag=='1'}">
	              		<span class="layui-badge layui-bg-red">精帖</span>
	            	</c:if>
	            </div>
          </li>
          </c:forEach>        
        </ul>
        <div style="text-align: center">
          <div class="laypage-main">
          	<c:choose>
	          	<c:when test="${empty allList}">
		            <a href="/yq-work/fly/docs?page=1" class="laypage-next">回到第一页</a>
	          	</c:when>
	          	<c:otherwise>
		            <a href="/yq-work/fly/docs?page=${pageNo}" class="laypage-next">获取更多</a>
	          	</c:otherwise>
          	</c:choose>
          </div>
        </div>
      </div>
    </div>
    <div class="layui-col-md4">
      <dl class="fly-panel fly-list-one">
        <dt class="fly-panel-title">最近热议</dt>
        <c:forEach items="${hostList}" var="item">
	        <dd>
	          <a href="/yq-work/fly/detail/${item.remind_id}">${item.title}</a>
	          <span><i class="iconfont icon-pinglun1"></i> ${item.comment_count}</span>
	        </dd>
        </c:forEach>
        <c:if test="${empty hostList}">
        	<div class="fly-none">没有相关数据</div>
        </c:if>
      </dl>

      <div class="fly-panel">
        <div class="fly-panel-title">公告栏</div>
        <div class="fly-panel-main">
          <a href="https://work.yunqu-info.cn/" target="_blank" class="fly-zanzhu" time-limit="2017.09.25-2099.01.01" style="background-color: #5FB878;">乐享，信息共享</a>
        </div>
      </div>
      
      <div class="fly-panel fly-link">
        <h3 class="fly-panel-title">内部系统</h3>
        <dl class="fly-panel-main">
          <dd><a href="https://www.yunqu-info.com/" target="_blank" class="fly-link">官网</a><dd>
          <dd><a href="https://exmail.qq.com/" target="_blank">邮箱</a><dd>
          <dd><a href="http://doc.yunqu-info.com/" target="_blank">云盘</a><dd>
          <dd><a href="http://*************:1443/" target="_blank">文件共享</a><dd>
          <dd><a href="http://wj.yunqu-info.cn" target="_blank">问卷</a><dd>
          <dd><a href="http://*************/yq-work/auth/memos" target="_blank">备忘录</a><dd>
          <dd><a href="http://*************/yq-work/auth/mindoc" target="_blank">文档库</a><dd>
          <dd><a href="http://*************:8080" target="_blank">思维导图</a><dd>
          <dd><a href="http://*************:3001/" target="_blank">Yapi</a><dd>
          <dd><a href="http://*************/zentao/my/" target="_blank">禅道</a><dd>
        </dl>
      </div>

    </div>
  </div>
</div>

</EasyTag:override>
 
<EasyTag:override name="script">
	<script type="text/javascript">
		layui.cache.page = '';
		layui.cache.user = {username: '${staffInfo.userName}' , uid: '${staffInfo.userId}', avatar: '${staffInfo.picUrl}', experience: 83 , sex: '男'};
		layui.config({version: "3.0.0",base: '/yq-work/pages/fly/static/mods/'}).extend({fly: 'index'}).use('fly');
   </script>
</EasyTag:override>
<%@ include file="fly-layout.jsp" %>
