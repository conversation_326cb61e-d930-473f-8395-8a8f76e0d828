
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<style>
		.layui-input, .layui-select, .layui-textarea{border: 1px solid rgb(217, 217, 217);height: 30px;padding: 5px 10px;line-height: 1.5;}
		.ew-cascader-hide{position: fixed;}
		 @media screen and (max-width: 768px){
		    #feeEditForm .form-control{
		    	min-width: 150px!important;
		    }
		    #feeEditForm .layui-input{
		    	min-width: 150px!important;
		    }
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="feeEditForm" autocomplete="off">
				<table class="table table-vzebra">
					<tr>
						<td>注意事项</td>
						<td colspan="3" style="color: #ff5722;">
							每行报销项目仅使用一种发票类型;如果涉及专用发票,请确保其税率保持一致,若税率不同,请分行填写。
						</td>
					</tr>
			        <tbody>
			        	<c:if test="${param.isTravelFee=='1'}">
		                <tr>
		                	<td style="width: 70px;">往返日期</td>
		                	<td colspan="3">
								<input type="text" data-rules="required" style="display: inline-block;width: 150px;" data-laydate="{type:'date',max:30, min:-365}" class="form-control input-sm  Wdate" name="START_DATE"/>
								<input type="text" data-rules="required" style="display: inline-block;width: 150px;" data-laydate="{type:'date',max:30, min:-365}" class="form-control input-sm  Wdate" name="DEST_DATE"/>
		                		<label class="checkbox checkbox-inline checkbox-success ml-10">
							      	 <input type="checkbox" name="saveTravelFee" checked="checked" value="1"><span style="padding-left: 0px;">复制</span>
						        </label>
		                	</td>
		                </tr>
		                <tr>
		                	<td  class="required" style="width: 70px;">出发城市</td>
		                	<td>
								<input type="text" data-rules="required" class="form-control input-sm selectCity" name="START_CITY"/>
		                	</td>
		                	<td class="required">到达城市</td>
		                	<td>
								<input type="text" data-rules="required" class="form-control input-sm selectCity" name="DEST_CITY"/>
		                	</td>
		                </tr>
		                <tr>
		                	<td class="required">费用地点</td>
		                	<td>
								<input type="text" data-rules="required" class="form-control input-sm selectCity" name="COST_PLACE"/>
		                	</td>
		                	<td class="required">费用天数</td>
		                	<td>
								<input type="number" data-rules="required" class="form-control input-sm" onblur="autoGenDesc();" name="COST_DAY"/>
		                	</td>
		                </tr>
			        	</c:if>
		                <tr>
		                	<td style="width: 70px;" class="required">发票类型<a onclick="layer.msg('一条明细只允许一个发票类型,若多个请新增明细');"><i class="layui-icon layui-icon-help"></i></a></td>
		                	<td colspan="3">
		                		<label class="radio radio-inline radio-success">
		                        	<input type="radio" value="增值税电子普通发票" checked="checked" name="INVOICE_TYPE"> <span style="color: #1e9fff;">增值税电子普通发票</span>
		                        </label>
		                		<label class="radio radio-inline radio-success zp-invoice">
		                        	<input type="radio" value="增值税电子专用发票" name="INVOICE_TYPE"> <span style="color: #1e9fff;">增值税电子专用发票</span>
		                        </label>
		                        <small class="pull-right">电子发票号8位</small>
		                        <br>
		                        <label class="radio radio-inline radio-success">
		                        	<input type="radio" value="数电票(普通发票)" name="INVOICE_TYPE"> <span style="color: #efa704;">数电票(普通发票)</span>
		                        </label>
		                		<label class="radio radio-inline radio-success zp-invoice" style="margin-left: 26px;">
		                        	<input type="radio" value="数电票(增值税专用发票)" name="INVOICE_TYPE"> <span style="color: #efa704;">数电票(增值税专用发票)</span>
		                        </label>
		                        <small class="pull-right">电子发票号20位</small>
		                        <br>
		                        
		                        <label class="radio radio-inline radio-success">
		                        	<input type="radio" value="电子发票(铁路电子客票)" name="INVOICE_TYPE"> <span style="color:#2ad549;">电子发票(铁路电子客票)</span>
		                        </label>
		                		<label class="radio radio-inline radio-success zp-invoice" style="margin-left: 26px;">
		                        	<input type="radio" value="电子发票(航空运输电子客票行程单)" name="INVOICE_TYPE"> <span style="color: #2ad549;">电子发票(航空运输电子客票行程单)</span>
		                        </label>
		                        <small class="pull-right">电子发票号20位</small>
		                        <br>
		                        
		                		<label class="radio radio-inline radio-success common-invoice">
		                        	<input type="radio" value="增值税普通发票等"  name="INVOICE_TYPE"> <span>增值税普通发票等</span>
		                        </label>
		                		<label class="radio radio-inline radio-success zp-invoice" style="margin-left: 22px;">
		                        	<input type="radio" value="增值税专用发票" name="INVOICE_TYPE"> <span>增值税专用发票</span>
		                        </label>
		                        <small class="pull-right">纸质发票或其他</small>
		                	</td>
		                </tr>
		                <tr id="budgetMonthTr">
		                	<td class="required">费用预算月份</td>
		                	<td colspan="3">
								<input class="form-control input-sm Wdate" data-laydate="{type:'month',max:0, min:-360}" name="BUDGET_MONTH" id="budgetMonth"/>
		                	</td>
		                </tr>
		                <tr class="eInvoiceTr">
		                	<td rowspan="2">
		                		电子发票号
		                		<input type="hidden" name="INVOICE_NO"/>
		                		<div style="display: none;width:90%;" class="mt-5 invoiceTpl">
									<input type="text" class="form-control input-sm inputInvoiceNo" onchange="repeatJudge(this)" placeholder="发票号：8或20位数字" style="width: 180px;display: inline-block;"/>
									<button class="btn btn-sm btn-default" type="button" onclick="$(this).parent().remove();">×</button>
		                		</div>
		                	</td>
		                	<td colspan="3">
		                		<div style="display: block;margin-top: 3px;">
									<!-- <button class="btn btn-sm btn-success mt-5" type="button" onclick="addInvoice(this)">+录入电子发票号</button> -->
									<button class="btn btn-sm btn-success mt-5" type="button" onclick="selectInvoice(this)"><i class="fa fa-filter"></i> 选择发票</button>
									<a href="/yq-work/invoice/list" class="btn btn-info btn-outline mt-5 ml-15 btn-sm" target="_blank"> <i class="fa fa-file-pdf-o"></i> 管理发票</a>
									<small style="color: red;margin-left: 10px;">电子发票必填发票号,否则无需填写</small>
		                		</div>
		                	</td>
		                </tr>
		                <tr class="eInvoiceTr">
		                	<td style="text-align: left;" colspan="3">
		                		<div id="invoiceContainer"></div>
		                		<table class="layui-table invoice-list"></table>
		                	</td>
		                </tr>
		                <tr>
		                	<td class="required" style="vertical-align: top;">费用说明<br>(公司内部)
		                		<c:if test="${param.isTravelFee=='1'}">
		                			<br><a onclick="autoGenDesc()" href="javascript:;">自动生成</a>
		                		</c:if>
		                	</td>
		                	<td colspan="3">
								<textarea class="form-control input-sm" style="height: 80px;" data-rules="required" name="FEE_DESC"></textarea>
		                	</td>
		                </tr>
		               <!--  <tr>
		                	<td class="required">费用描述</td>
		                	<td colspan="3">
								<textarea class="form-control input-sm" data-rules="required" name="FEE_OUT_DESC"></textarea>
		                	</td>
		                </tr> -->
			        </tbody>
 				 </table>
				 <div class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm" onclick="FeeEdit.ajaxSubmitForm()"> 确 定  </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
	  		
	  		<script id="invoices" type="text/x-jsrender">
				{{for data}}
					<div style="display: inline-block;width:90%;" class="mt-5 invoiceTpl">
						<input type="text" value="{{:no}}" class="form-control input-sm inputInvoiceNo" onchange="repeatJudge(this)" style="width: 180px;display: inline-block;"/>
						<button class="btn btn-sm btn-default" type="button" onclick="$(this).parent().remove();">×</button>
		             </div>
				{{/for}}
			</script>
	  		<script id="invoices-table" type="text/x-jsrender">
				{{for data}}
					<tr class="item-{{:INVOICE_ID}}" data-itype="{{:INVOICE_TYPE}}" data-rate="{{:TAX_RATE_NUM}}">
						<td>
							<input name="invoiceIds" type="hidden" value="{{:INVOICE_ID}}">
							<input class="inputInvoiceNo" type="hidden" value="{{:INVOICE_NO}}">
							<input class="invoiceAmount" type="hidden" value="{{:TOTAL_AMOUNT}}">
							{{:INVOICE_NO}}_{{:SALE_COMPANY}}_{{:INVOICE_DATE}}_￥{{:TOTAL_AMOUNT}}
						   <button class="btn btn-xs btn-danger" type="button" onclick="deleteInvoice(this,'{{:INVOICE_ID}}','{{:INVOICE_NO}}','{{:BUSINESS_ID}}')"><i class="fa fa-close"></i></button>
					  </td>
					</tr>
				{{/for}}
			</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript" src="/yq-work/static/module/cascader/citys-data.js"></script>
	<script type="text/javascript">
	
	    var FeeEdit = {itemId:'${param.itemId}',isTravelFee:'${param.isTravelFee}'};
	    
	   function deepClone(obj) {
            var result;
            var oClass = isClass(obj);
            if (oClass === 'Object') result = {};
            else if (oClass === 'Array') result = [];
            else return obj;
            for (var key in obj) {
                if (!obj.hasOwnProperty(key)) continue;
                var copy = obj[key], cClass = isClass(copy);
                if (cClass === 'Object') result[key] = arguments.callee(copy); // 递归调用
                else if (cClass === 'Array') result[key] = arguments.callee(copy);
                else result[key] = obj[key];
            }
            
            function isClass(o) {
                if (o === null) return 'Null';
                if (o === undefined) return 'Undefined';
                return Object.prototype.toString.call(o).slice(8, -1);
            }
            
            return result;
        }
	    
	    FeeEdit.ajaxSubmitForm = function() {
	    	if(form.validate("#feeEditForm")){
				var data = form.getJSONObject("#feeEditForm");
	    		var invoiceType = data['INVOICE_TYPE']; 
	    		if(isEInvoice(invoiceType)){
		    		var array = [];
		    		$('.inputInvoiceNo').each(function(){
		    			var val = $(this).val();
		    			if(val){
							if(val.length!=8&&val.length!=20){
								layer.msg('电子发票号为8或20位数字',{offset:'20px',icon:7});
								return false;
							}
			    			array.push(val);
		    			}
		    		});
		    		array = arrayUnique(array);
		    		var invoiceNos =  array.join(',');
		    		if(invoiceNos==''){
		    			layer.msg('请填写发票号');
		    			return;
		    		}
		    		data['INVOICE_NO'] = invoiceNos; 
	    		}else{
		    		data['INVOICE_NO'] = ''; 
	    		}

	    		var invoiceIdArray = [];
	    		var invoiceIds = data['invoiceIds'];
	    		delete data['invoiceIds'];
	    		if(invoiceIds){
	    			if(!isArray(invoiceIds)){
	    				invoiceIdArray.push(invoiceIds);
	    			}else{
	    				invoiceIdArray = invoiceIds;
	    			}
	    		}
	    		data['invoiceIdArray'] = invoiceIdArray.join(',');
	    		
				var _invoiceNos = data['INVOICE_NO']
				if(_invoiceNos){
					ajax.remoteCall("${ctxPath}/servlet/bx/conf?action=judgeInvoice",{invoiceNo:_invoiceNos,itemId:FeeEdit.itemId},function(result) { 
						if(result.state == 0){
							layer.alert('您填写的【'+_invoiceNos+'】有允许重复使用的发票号>'+result.msg,{offset:'20px',icon: 5});
						}else{
							setData();
							popup.layerClose("#feeEditForm");
						}
					});
				}else{
					setData();
					popup.layerClose("#feeEditForm");
				}
				
				function setData(){
					delete data['saveTravelFee'];
			    	var itemId = FeeEdit.itemId;
			    	
					$("[name='feeJson_"+itemId+"']").val(JSON.stringify(data));
					$("[name='feeDesc_"+itemId+"']").val(data['FEE_DESC']);
					$("[name='invoiceNo_"+itemId+"']").val(data['INVOICE_NO']);
					$("[name='budgetMonth_"+itemId+"']").val(data['BUDGET_MONTH']);
					$("[name='invoiceType_"+itemId+"']").val(invoiceType);
					if(isSpecialInvoice(invoiceType)){
						//$("[name='taxRate_"+itemId+"']").val('');
						$("[name='taxes_"+itemId+"']").removeAttr('readonly');
						layer.msg('请确定专票税率',{icon:7});
					}else{
						$("[name='taxRate_"+itemId+"']").val('0');
						$("[name='taxes_"+itemId+"']").val('0').attr('readonly','true');
						calcRowRate(itemId,1);
					}
					if(FeeEdit.isTravelFee=='1'){
						$("[name='feeType_"+itemId+"']").attr('data-travel-fee','1');
					}else{
						$("[name='feeType_"+itemId+"']").attr('data-travel-fee','2');
					}
					var saveTravelFee = $("input[name='saveTravelFee']:checked").val();
					if(saveTravelFee=='1'){
						saveFeeTempInfo(data);
					}else{
						layui.sessionData('bx',{key:'feeInfo',value:{saveTravelFee:''}});
					}
					calcAllAmount();
				}
	    	}
		}
	    
	    
	  
	    
	    
	    function saveFeeTempInfo(data){
	    	layui.sessionData('bx',{key:'feeInfo',value:{'START_DATE':data['START_DATE'],'DEST_DATE':data['DEST_DATE'],'START_CITY':data['START_CITY'],'DEST_CITY':data['DEST_CITY'],'COST_PLACE':data['COST_PLACE'],'COST_DAY':data['COST_DAY'],'saveTravelFee':'1'}});
	    }
	    
	    function queryBxBudget(){
	    	if(hasBxBudget==1){
	    		$('#budgetMonthTr').show();
				$('#budgetMonth').attr('data-rules','required');
	    	}else if(hasBxBudget==2){
	    		$('#budgetMonthTr').hide();
				$('#budgetMonth').removeAttr('data-rules');
	    	}else{
		    	ajax.remoteCall("${ctxPath}/servlet/bx/conf?action=queryUserBudget",{},function(result) {
					if(result.state == 1){
						if(result.data.length>0){
							hasBxBudget = 1;
							$('#budgetMonthTr').show();
							$('#budgetMonth').attr('data-rules','required');
						}else{
							hasBxBudget = 2;
							$('#budgetMonthTr').hide();
							$('#budgetMonth').removeAttr('data-rules');
						}
					}else{
						layer.alert(result.msg);
					}
				});
	    	}
	    }
	    
	    function autoGenDesc(){
	    	var data = form.getJSONObject("#feeEditForm");
	    	var itemId = FeeEdit.itemId;
			var feeType = $("[name='feeType_"+itemId+"']").val();
			var _feeType = feeType.split('-')[1];
			var desc = '';
	    	var array = [];
			if(_feeType.indexOf('机票')>-1){
				array.push(data['START_DATE']+"从"+data['START_CITY']);
		    	array.push( '乘坐飞机');
		    	array.push('到达'+data['DEST_CITY']+",");
		    	array.push("总共出差了"+data['COST_DAY']+"天");
			}else if(_feeType.indexOf('车船')>-1){
				array.push(data['START_DATE']+"从"+data['START_CITY']);
		    	array.push('坐高铁');
		    	array.push('到达'+data['DEST_CITY']+",");
		    	array.push("总共出差了"+data['COST_DAY']+"天");
			}else if(_feeType.indexOf('住宿')>-1){
				array.push("出差"+data['COST_DAY']+"天的住宿费");
			}else if(_feeType.indexOf('补助')>-1){
				array.push(data['START_DATE']+"至"+data['DEST_DATE']);
		    	array.push('出差'+data['DEST_CITY']+"");
		    	array.push(data['COST_DAY']+"天补助");
			}else if(_feeType.indexOf('交通')>-1){
				array.push(getMonth(data['START_DATE'])+"出差"+data['DEST_CITY']);
		    	array.push( '市内交通费');
			}else{
				layer.msg('请手工输入费用说明');
			}
	    	$("[name='FEE_DESC']").val(array.join(''));
	    	
	    	function getMonth(val){
	    		if(val){
	    			return val.substring(0,7).replace('-','');
	    		}else{
	    			return '';
	    		}
	    	}
	    }
	    
	    function  calcCostDay(){
	    	var dateString1 = $("#feeEditForm [name='START_DATE']").val();
	    	var dateString2 = $("#feeEditForm [name='DEST_DATE']").val();
	    	if(dateString1&&dateString2){
		        var  startDate = Date.parse(dateString1);
		        var  endDate = Date.parse(dateString2);
		        if (startDate>endDate){
		            return 0;
		        }
		        if (startDate==endDate){
		            return 1;
		        }
		        var days=(endDate - startDate)/(1*24*60*60*1000);
		        $("#feeEditForm [name='COST_DAY']").val(days+1);
	    	}
	    }
	    
	    
		$(function(){
			renderDate('#feeEditForm');
			
			var payType = $('#payType').val();
			if(payType=='对公付款'){
				$('#budgetMonthTr').hide();
			}else{
				queryBxBudget();
			}
			
			
			$("[name='INVOICE_TYPE']").change(function(){
				var t = $(this);
				var val = t.val();
				if(isEInvoice(val)){
					$('.eInvoiceTr').show();
				}else{
					$('.eInvoiceTr').hide();
				}
				$('.invoice-list').html('');
			});
			
			var itemId = FeeEdit.itemId;
			var feeJson = $("[name='feeJson_"+itemId+"']").val();
			
			var feeType = $("[name='feeType_"+itemId+"']").val();
			if(feeType=='职工福利费'||feeType=='业务招待费'){
			   $('.zp-invoice').remove();
			   //$("[name='INVOICE_TYPE'][value='增值税专用发票']").parent().remove();
			   //layer.msg(feeType+"不允许有专票",{time:800,icon:7});
			}
			
			if(feeType=='差旅费-出差补助'){
				$('.common-invoice input').prop("checked", true);
				$('.common-invoice').siblings().remove();
				$('.eInvoiceTr').hide();
				//$("[name='INVOICE_TYPE'][value='增值税专用发票']").parent().remove();
				//$("[name='INVOICE_TYPE'][value='增值税电子普通发票']").parent().remove();
				//$("[name='INVOICE_TYPE'][value='增值税电子专用发票']").parent().remove();
			}
			
			
			if(feeJson){
				var json = eval('(' + feeJson + ')'); 
				fillData(json);
				//redenrInvoiceNO(json);
				redenrInvoiceList(json);
			}else{
				if(itemId.length>20){
					ajax.daoCall({loading:false,controls:['BxDao.bxItemInfo'],params:{itemId:itemId}},function(rs){
						var result = rs['BxDao.bxItemInfo'].data;
						fillRecord(result,'','','#feeEditForm');
						if(feeJson==''){
							redenrInvoiceList(result);
						}
					});
				}else{
					var data = layui.sessionData('bx');
					if(data&&data.feeInfo&&data.feeInfo.saveTravelFee=='1'){
						fillData(data.feeInfo);
						if(FeeEdit.isTravelFee=='1'){
							autoGenDesc();
						}
					}else{
						$("input[name='saveTravelFee']").click();
						initSelectCity({});
					}
					
					var bxDate = $("[name='bxDate_"+itemId+"']").val();
					if(bxDate){
						$("[name='START_DATE']").val(bxDate);
					   //$("[name='BUDGET_MONTH']").val(bxDate.substring(0,7));
					}
				}
			}
			
			function fillData(row){
				 fillRecord(row,'','','#feeEditForm');
				 initSelectCity(row);
			}
			
			function initSelectCity(row){
				if(FeeEdit.isTravelFee=='1'){
			    	 layui.config({
						  base: '/yq-work/static/module/'
					 }).use('cascader');
			    	 layui.use(['form','cascader'], function () {
			    		 var cascader = layui.cascader;
			    		 $('.selectCity').each(function(){
			    			 var name = $(this).attr('name');
			    			 var data = cascader.getCity2(deepClone(citysData));
				    		 var ins = cascader.render({
			    	            elem: $(this),
			    	            data: data,
			    	            itemHeight: '250px',
			    	            filterable: true,
			    	            renderFormat: function (labels, values) {
			    	                return labels.join('');
			    	            },
			    	            onChange: function (values, data) {
			    	            	autoGenDesc();
			    	            	calcCostDay();
			    	            }
			    	       	  });
				    		if(!$.isEmptyObject(row)){
					    		ins.setValue(row[name]); 
				    		 }
			    		 });
			    	 });
			    }
			}
			
			function redenrInvoiceNO(json){
				var val = json['INVOICE_NO'];
				var invoiceType = json['INVOICE_TYPE'];
				if(val){
					var array  = val.split(',');
					var data = [];
					for(var index in array){
						data.push({no:array[index]});
					}
					var tmp = $.templates('#invoices');
					var html  = tmp.render({data:data});
					$('#invoiceContainer').prepend(html);
				}
				if(isEInvoice(invoiceType)){
					$('.eInvoiceTr').show();
				}
			}
			
			function redenrInvoiceList(json){
				var invoiceIdArray = json['invoiceIdArray'];
				var invoiceType = json['INVOICE_TYPE'];
				if(invoiceIdArray){
					ajax.remoteCall("${ctxPath}/webcall?action=BxInvoiceDao.bxInvoiceListById",{invoiceIdArray:invoiceIdArray},function(result) { 
						var tmp = $.templates('#invoices-table');
						var html  = tmp.render(result);
						$('.invoice-list').prepend(html);
					});
				}
				if(isEInvoice(invoiceType)){
					$('.eInvoiceTr').show();
				}
			}
			
		});

		function addInvoice(el){
			var newObj = $('.invoiceTpl').first().clone();
			newObj.css('display','inline-block');
			newObj.removeClass('invoiceTpl');
			$('#invoiceContainer').prepend(newObj);
		}
		
		var _hasSelectInvoiceNos = '';
		
		function selectInvoice(el){
			_hasSelectInvoiceNos = '';
			
			var invoiceType = $('[name="INVOICE_TYPE"]:checked').val();
			top.layer.msg('您选择的发票类型是：'+invoiceType,{offset:'10px',icon:1,time:5000});
			
			var sid = new Date().getTime();
			$(el).attr('data-sid',sid);
			
			var data = {itemId:FeeEdit.itemId,sid:sid,type:'checkbox',invoiceType:invoiceType};
			
			var hasSelectInvoices = [];
			$(".inputInvoiceNo").each(function(){
				var val = $(this).val();
				if(val){
					hasSelectInvoices.push(val);
				}
			});
			
			try{
			    var staffInfo = getStaffInfo();
				if(staffInfo.deptName!='人力行政部'){
					$("[data-field='invoiceNo']").each(function(){
						var _invoiceNos = $(this).val();
						if(_invoiceNos){
							hasSelectInvoices.push(_invoiceNos.split(','));
						}
					});
				}
			}catch(e){}
			
		
			_hasSelectInvoiceNos = hasSelectInvoices.join();
			
		    popup.layerShow({id:'addInvoiceInfo',scrollbar:false,title:'选择【未报销】【'+invoiceType+'】发票',area:['70%','80%'],offset:'20px',url:'/yq-work/pages/flow/select/select-bx-invoice.jsp',data:data,success:function(){
		    	
		    }});
		}
		
		function repeatJudge(el){
			var val = $(el).val();
			if(val){
			   if(val.length!=8&&val.length!=20){
					layer.msg('电子发票号为8或20位数字',{offset:'20px',icon:7});
			   }
			   $("[data-field='invoiceNo']").each(function(){
				   var value = $(this).val();
				   var itemId = $(this).data('id');
				   if(value){
					   if(itemId!=FeeEdit.itemId){
						   if(value.indexOf(val)>-1){
							   $(el).val('');
							   layer.msg('发票号'+val+'已经存在');
							   return false;
						   }
					   }
				   }
			   });
			}
		}
		
		function deleteInvoice(el,invoiceId,invoiceNo,businessId){
			if(businessId){
				ajax.remoteCall("${ctxPath}/servlet/bx/invoice?action=delBxInvoice",{invoiceId:invoiceId,invoiceNo:invoiceNo,businessId:businessId},function(result) { 
					$(el).parent().parent().remove();
					var invoiceNoElement = $("[name='invoiceNo_"+FeeEdit.itemId+"']");
					var _invoiceNo = invoiceNoElement.val();
					if(_invoiceNo){
						var arr = _invoiceNo.split(','); 
						arr = arr.filter(function(item) {
						  return item !== invoiceNo;
						});
						var str = arr.join(',');
						invoiceNoElement.val(str);
					}
					layer.msg('移除成功',{icon:1,time:800});
				});
			}else{
				$(el).parent().parent().remove();
				layer.msg('移除成功',{icon:1,time:800});
			}
		}
		
		function calcAllAmount(){
			var sum = 0;
			$("#feeEditForm .invoiceAmount").each(function(){
				  var value = $(this).val();
				  sum = sum + Number(value);
			  }
			);
			$("[name='amount_"+FeeEdit.itemId+"']").val(sum.toFixed(2));
			$("[name='amount_"+FeeEdit.itemId+"']").attr('data-amount',sum.toFixed(2));
			calcBxMoney(FeeEdit.itemId);
		}
		
		function isEInvoice(type){
			if(type.indexOf('电')>-1){
				return true;
			}
			return false;
		}
		function isSpecialInvoice(type){
			if(type.indexOf('专')>-1){
				return true;
			}
			return false;
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>