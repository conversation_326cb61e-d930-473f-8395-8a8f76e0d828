package com.yunqu.work.service;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;

public class KqNoticeService extends AppBaseService implements Job{

	private static class Holder{
		private static KqNoticeService service=new KqNoticeService();
	}
	
	public static KqNoticeService getService(){
		return Holder.service;
	}
	
	@Override
	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		 //this.sbDkNotice();
	}
	
	
	private static int today = 0;
	private static int todayDate = 0;
	
	/**
	 * true： 正常班  false：加班
	 * @return
	 */
	public boolean kqFlag() {
	    int _todayDate = EasyCalendar.newInstance().getDateInt();
		if(todayDate!=_todayDate){
		    todayDate = _todayDate;
			List<Record> list = Db.find("select * from yq_no_work_day where day = ?",_todayDate);
			if(list!=null&&list.size()>0){
				today = 1;
			}else{
				today = 2;
			}
		}
		if(today==1){
			return false;
		}
		return true;
	}
	
	//上班打卡提醒
	public  void sbDkNotice(){
		getLogger().info("执行早上打卡提醒....");
		if(!kqFlag()) {
			return;
		}
		try {
			int dateId = EasyCalendar.newInstance().getDateInt();
//			List<JSONObject> list = this.getQuery().queryForList("select t1.USER_ID,t1.USERNAME,t2.often_work_city from "+Constants.DS_MAIN_NAME+".easi_user t1,"+Constants.DS_MAIN_NAME+".yq_staff_info t2 where t1.USER_ID = t2.STAFF_USER_ID and t1.STATE = 0 and t1.USER_ID not in(select b1.USER_ID from yq_kq_obj b1 where b1.DK_DATE = ?)",new Object[]{dateId},new JSONMapperImpl());
			List<JSONObject> list = this.getQuery().queryForList("select USER_ID,USER_NAME from yq_kq_obj where DK_DATE = ? and SIGN_IN = ''",new Object[]{dateId},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator=jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(creator);
					model.setSender(creator);
					model.setTitle("您好，您有一条考勤打卡提醒");
					model.setData1(jsonObject.getString("USER_NAME"));
					model.setData2(EasyDate.getCurrentDateString());
					model.setData3("上班未打卡");
					model.setDesc("上班时间为09:00:00，请别忘记打卡！");
					WxMsgService.getService().sendKqDkNotice(model);
				}
			}
		} catch (SQLException e2) {
			this.getLogger().error(e2);
		}
	}
	
	
	public  void xbDkNotice(){
		getLogger().info("执行下班打卡提醒....");
		if(!kqFlag()) {
			return;
		}
		try {
			int dateId = EasyCalendar.newInstance().getDateInt();
			List<JSONObject> list = this.getQuery().queryForList("select b1.USER_ID,b1.USER_NAME from YQ_KQ_OBJ b1 where b1.DK_DATE = ? and b1.SIGN_OUT = ''",new Object[]{dateId},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				for(JSONObject jsonObject:list){
					String creator = jsonObject.getString("USER_ID");
					MessageModel model=new MessageModel();
					model.setReceiver(creator);
					model.setSender(creator);
					model.setTitle("您好，您有一条考勤打卡提醒");
					model.setData1(jsonObject.getString("USER_NAME"));
					model.setData2(EasyDate.getCurrentDateString());
					model.setData3("下班未打卡");
					model.setDesc("已经下班很久了，请别忘记打卡！");
					WxMsgService.getService().sendKqDkNotice(model);
				}
			}
		} catch (SQLException ex) {
			this.getLogger().error(ex);
		}
	}
	
	private static String zaoanContent = null;
	private static int zaoanTodayDate = 0;
	
	public String getZaoanWord() {
		 int _todayDate = EasyCalendar.newInstance().getDateInt();
		if(zaoanTodayDate!=_todayDate){
			zaoanTodayDate = _todayDate;
			//https://www.tianapi.com/console/
			Connection connection =  Jsoup.connect("https://apis.tianapi.com/zaoan/index");
			connection.data("key","5b4c86b8325e339da075520e19d357b0");
			try {
				Response response = connection.ignoreContentType(true).timeout(5000).method(Method.GET).execute();
				String str = response.body();
				JSONObject json = JSONObject.parseObject(str);
				if(json.getIntValue("code")==200) {
					zaoanContent =  json.getJSONObject("result").getString("content");
					this.getLogger().info("req zaoanContent:"+zaoanContent);
					return zaoanContent;
				}
			} catch (IOException e) {
				this.getLogger().error(e);
			}
			return "早安";
	    }
		return zaoanContent;
		
	}
	
	public static Map<String,String> cityMap = new HashMap<String, String>();
	private static int tqTodayDate = 0;
	
	public String getTq(String city) {
		if(StringUtils.isBlank(city)) {
			return "无";
		}
		int _todayDate = EasyCalendar.newInstance().getDateInt();
		if(tqTodayDate!=_todayDate){
			tqTodayDate = _todayDate;
			cityMap.clear();
		}
		String todayTq = cityMap.get(city);
		if(StringUtils.notBlank(todayTq)) {
			return todayTq;
		}
		//https://www.seniverse.com/products?iid=fc96f6fd-2da2-4fde-ae0d-5fb9b37d3cf0
		Connection connection =  Jsoup.connect("https://api.seniverse.com/v3/weather/daily.json");
		connection.data("key","SXN5v39ZWpXeNR6bX");
		connection.data("location",city);
		connection.data("start","1");
		connection.data("days","1");
		try {
			Response response = connection.ignoreContentType(true).timeout(5000).method(Method.GET).execute();
			String str = response.body();
			JSONObject json = JSONObject.parseObject(str);
			if(!json.containsKey("results")) {
				cityMap.put(city, "无");
				return "无";
			}
			JSONArray results = json.getJSONArray("results");
			JSONObject row = results.getJSONObject(0);
			JSONArray daily = row.getJSONArray("daily");
			JSONObject detail = daily.getJSONObject(0);
			String tq = city+"明天天气："+detail.getString("text_day")+" "+detail.getString("low")+"°~"+detail.getString("high")+"°";
			cityMap.put(city, tq);
			return tq;
		} catch (IOException e) {
			this.getLogger().error(e);
		}
		return "无";
		
	}
	
	
	public  void addKqRecord(String dateId){
		getLogger().info("执行新增考勤记录....");
			String _dateId = String.valueOf(EasyCalendar.newInstance().getDateInt());
			String monthId = String.valueOf(EasyCalendar.newInstance().getFullMonth());
			if(StringUtils.notBlank(dateId)) {
				_dateId  = dateId;
				monthId = dateId.substring(0,6);
			}
			
			List<JSONObject> list = null;
			try {
				list = this.getQuery().queryForList("select t1.kq_user_id,t1.kq_user_name from yq_kq_people t1 where t1.state = ?",new Object[]{0},new JSONMapperImpl());
			} catch (SQLException e) {
				this.getLogger().error(e);
			}
			if(list!=null&&list.size()>0){
				for(JSONObject row:list){
					String kqUserId = row.getString("KQ_USER_ID");
					StaffModel model = StaffService.getService().getStaffInfo(kqUserId);
					if(model!=null) {
						EasyRecord record = new EasyRecord("YQ_KQ_OBJ","DK_DATE","USER_ID");
						record.set("KQ_ID",RandomKit.uuid());
						record.set("USER_ID",kqUserId );
						record.set("STAFF_ID",model.getStaffNo());
						record.set("CREATE_TIME",EasyDate.getCurrentDateString() );
						record.set("USER_NAME",model.getUserName());
						record.set("DEPT",model.getDeptName());
						record.set("DK_DATE", _dateId);
						record.set("DATE_ID", _dateId);
						record.set("MONTH_ID", monthId);
						try {
							this.getQuery().save(record);
						} catch (SQLException ex) {
							this.getLogger().error(ex);
						}
					}
				}
			}
		
	}
	
	public void deptKqData() {
		deptKqData(EasyCalendar.newInstance().getFullMonth());
	}
	
	/**
	 * 本月部门考勤数据
	 */
	public void deptKqData(String monthId) {
	   if(!kqFlag()) {
			return;
	   }
	   if(StringUtils.isBlank(monthId)) {
		   monthId = EasyCalendar.newInstance().getFullMonth();
	   }
	   EasySQL sql = new EasySQL();
	   sql.append(monthId,"SELECT DEPT_ID,DEPT from yq_kq_data where MONTH_ID = ? GROUP BY DEPT_ID");
	   try {
			List<JSONObject> list =  this.getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
			if(list!=null) {
				for(JSONObject row:list) {
			    	String deptId = row.getString("DEPT_ID");
			    	EasySQL deptSql = new EasySQL();
			    	deptSql.append("SELECT USER_NAME,FLOOR(SUM(TIME_TO_SEC(OVER_TIME)) / 60) AS total_minutes,count(1) count from yq_kq_data where ");
			    	deptSql.append(deptId,"DEPT_ID = ?");
			    	deptSql.append(monthId,"and MONTH_ID = ?");
			    	deptSql.append("and OVER_TIME <>'' GROUP BY USER_ID HAVING count > 2 ORDER BY total_minutes desc");
			    	List<JSONObject> deptUserList =  this.getQuery().queryForList(deptSql.getSQL(),deptSql.getParams(), new JSONMapperImpl());
			    	StringBuffer sb = new StringBuffer();
			    	int i = 0;
			    	for(JSONObject user:deptUserList) {
			    		i++;
			    		sb.append(i+"."+user.getString("USER_NAME")+",加班分钟:"+user.getString("TOTAL_MINUTES")+",次数:"+user.getIntValue("COUNT")+"\n");
			    		if(i>=10) {
			    			break;
			    		}
			    	}
			    	if(i>=5) {
			    		WeChatWebhookSender.sendMarkdownMessage("dept@"+deptId, monthId+"-"+row.getString("DEPT")+"加班时间数据", sb.toString());
			    		if(ServerContext.isLinux()) {
			    			WeChatWebhookSender.sendMarkdownMessage(WebhookKey.TEST, monthId+"-"+row.getString("DEPT")+"加班时间数据", sb.toString());
			    		}
			    	}
				}
			}
	   } catch (SQLException e) {
		   this.getLogger().error(e);
	  }
	}
	
	public static void main(String[] args) {
		System.out.println(KqNoticeService.getService().getZaoanWord());
	}
	
	
}
