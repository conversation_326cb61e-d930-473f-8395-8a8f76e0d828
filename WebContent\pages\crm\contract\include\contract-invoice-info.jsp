<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="invoiceTable"></table>
<script  type="text/html" id="invoiceToolbar">
    <button class="btn btn-info btn-sm" onclick="contractInvoiceList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-15" onclick="reloadContractInvoiceList()" type="button">刷新</button>
</script>
<script type="text/javascript">
    var contractInvoiceList={
        init:function(){
            $("#ContractDetailForm").initTable({
                mars:'InvoiceDao.list',
                id:'invoiceTable',
                toolbar:'#invoiceToolbar',
                cellMinWidth:50,
                page:false,
                totalRow:true,
                autoSort:true,
                rowDoubleEvent(row){
                    contractInvoiceList.detail2(row);
                },
                cols: [[
                    {
                        type: 'numbers',
                        title: '序号',
                        align:'left',
                        fixed:'left',
                        totalRowText:"合计"
                    },{
                        field: 'INVOICE_NO',
                        title: '发票编号',
                        align:'left',
                        minWidth:100,
                        style:'color:#1E9FFF;cursor:pointer',
                        event:'contractInvoiceList.detail'
                    },{
                        field: 'INVOICE_TYPE',
                        title: '开票类型',
                        align:'center',
                        width:110
                    },{
                        field: 'MARK_DATE',
                        title: '开票日期',
                        align:'center',
                        sort:true,
                    },{
                        field: 'MARK_MONEY',
                        title: '开票金额',
                        align:'center',
                        totalRow:true,
                        sort:true,
                    },{
                        field: 'TAX_RATE',
                        title: '税率(%)',
                        align:'center',
                        width: 60,
                    },{
                        field: 'TOTAL_TAX',
                        title: '税额',
                        align:'center',
                        totalRow:true,
                        width: 80
                    },{
                        field: 'AMOUNT_IN_FIGUERS',
                        title: '不含税金额',
                        align:'center',
                        width: 100,
                        totalRow:true,
                    },{
                        field:'PAYMENT_TYPE',
                        title:'收入类型',
                        templet:function(row){
                            return getText(row.PAYMENT_TYPE,'paymentType');
                        }
                    },{
                        field: 'STAGE_ID',
                        title: '合同阶段',
                        align:'left',
                        templet:function(row){
                            return getText(row.STAGE_ID,'contractStage');
                        }
                    },{
                        field: 'PLATFORM_NAME',
                        title: '业务平台',
                        align:'left',
                    },{
                        field: 'APPLY_TITLE',
                        title: '开票流程',
                        align:'left',
                    },{
                        field: 'CUST_NAME',
                        title: '客户名称',
                        align:'center'
                    },{
                        field: 'BANK_NAME',
                        title: '客户银行',
                        align:'center',
                        hide:true,
                        templet:function(row){
                            return contractInvoiceList.getText(row,'BANK_NAME');
                        }
                    },{
                        field: 'BANK_NO',
                        title: '客户账号',
                        align:'center',
                        hide:true,
                        templet:function(row){
                            return contractInvoiceList.getText(row,'BANK_NO');
                        }
                    },{
                        field: 'TAX_NO',
                        title: '客户税号',
                        align:'center',
                        hide:true,
                        templet:function(row){
                            return contractInvoiceList.getText(row,'TAX_NO');
                        }
                    },{
                        field: 'MAIL_ADDRESS',
                        title: '邮寄地址',
                        hide:true,
                        templet:function(row){
                            return contractInvoiceList.getText2(row,'MAIL_ADDRESS');
                        }
                    },{
                        field: 'CONTACTS_TEL',
                        title: '邮寄电话',
                        hide:true,
                        templet:function(row){
                            return contractInvoiceList.getText2(row,'CONTACTS_TEL');
                        }
                    },{
                        field: 'CONTACTS',
                        title: '邮寄联系人',
                        hide:true,
                        templet:function(row){
                            return contractInvoiceList.getText2(row,'CONTACTS');
                        }
                    },{
                        title: '操作',
                        align:'center',
                        minWidth:100,
                        fixed:'right',
                        templet:function(row){
                            var html = '<a lay-event="contractInvoiceList.detail">查看</a>';

                            if(row.SETTLEMENT_ID && row.SETTLEMENT_ID.trim() !== '') {
                                if (getCurrentUserId()  == row.CREATE_USER_ID || isSuperUser) {
                                    html += ' <span style="color:#ccc;" title="该发票已被结算，不允许修改">编辑</span>';
                                    html += ' <span style="color:#ccc;" title="该发票已被结算，不允许删除">删除</span>';
                                }
                            } else {
                                if (getCurrentUserId()  == row.CREATE_USER_ID || isSuperUser) {
                                    html += ' <a lay-event="contractInvoiceList.edit">编辑</a>';
                                    html += ' <a lay-event="contractInvoiceList.del">删除</a>';
                                }
                            }
                            
                            return html;
                        }
                    },
                    {
                        field: 'SETTLEMENT_ID',
                        title: '结算状态',
                        align: 'center',
                        width: 100,
                        templet: function(d){
                            if(d.SETTLEMENT_ID && d.SETTLEMENT_ID.trim() !== '') {
                                return '<span style="color:#ff5722;">已结算</span>';
                            } else {
                                return '<span style="color:#009688;">未结算</span>';
                            }
                        }
                    }
                ]],
                done:function(res){
                }
            });

        },
        getText:function(row,field){
            var jsonText = row['TEXT_JSON'];
            if(jsonText){
                var json = eval('(' + jsonText + ')');
                return json[field]||'';
            }
            return '';
        },
        getText2:function(row,field){
            var jsonText = row['MAIL_JSON'];
            if(jsonText){
                var json = eval('(' + jsonText + ')');
                return json[field]||'';
            }
            return '';
        },
        query:function(){
            $("#ContractDetailForm").queryData({id:'invoiceTable'});
        },
        edit:function(data){
            if(data.SETTLEMENT_ID && data.SETTLEMENT_ID.trim() !== '') {
                layer.alert('该发票记录已被结算（结算编号：' + (data.SETTLEMENT_NO || data.SETTLEMENT_ID) + '），不允许修改', {icon: 0});
                return;
            }
            
            var  invoiceId = data['INVOICE_ID'];
            popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['60%','80%'],url:'${ctxPath}/pages/crm/invoice/invoice-edit.jsp',title:'编辑发票',data:{editSource:'contract',invoiceId:invoiceId,contractAmount:contractInfo.AMOUNT}});
        },
        add:function(){
            var contractId = $("[name='contractId']").val();
            popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['800px','500px'],url:'${ctxPath}/pages/crm/invoice/invoice-edit.jsp',title:'新增发票',data:{editSource:'contract',contractId:contractId,contractNo:contractInfo.CONTRACT_NO,contractName:contractInfo.CONTRACT_NAME,contractAmount:contractInfo.AMOUNT,custName:contractInfo.CUSTOMER_NAME,custId:contractInfo.CUST_ID,custNo:contractInfo.CUST_NO,platformId: contractInfo.PLATFORM_ID,platformName:contractInfo.PLATFORM_NAME}});
        },
        detail:function(data){
            var  invoiceId = data['INVOICE_ID'];
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'发票详情',offset:'r',area:['60%','100%'],url:'${ctxPath}/pages/crm/invoice/invoice-detail.jsp',data:{invoiceId:invoiceId}});
        },
        detail2:function(data){
            var invoiceId=data.INVOICE_ID;
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'发票详情',offset:'r',area:['60%','100%'],url:'${ctxPath}/pages/crm/invoice/invoice-detail.jsp',data:{invoiceId:invoiceId}});
        },
        del:function (data){
            if(data.SETTLEMENT_ID && data.SETTLEMENT_ID.trim() !== '') {
                layer.alert('该发票记录已被结算（结算编号：' + (data.SETTLEMENT_NO || data.SETTLEMENT_ID) + '），不允许删除', {icon: 0});
                return;
            }
            
            layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function () {
                ajax.remoteCall("${ctxPath}/servlet/invoice?action=del", data, function (result) {
                    if (result.state == 1) {
                        layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                            layer.closeAll();
                            reloadContractInvoiceList();
                        });
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                });
            });
        }
    }

    function reloadContractInvoiceList(){
        $("#ContractDetailForm").queryData({id:'invoiceTable',page:false});
        $("#ContractDetailForm").queryData({id:'stageTable',page:false});
        $("[name='contractStage']").render();
        reloadContractDetail();
    }
</script>
