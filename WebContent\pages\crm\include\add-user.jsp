<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
	<style>
		.select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
   			 background-color: #fff;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="CustTeamForm" autocomplete="off">
	     	   <input type="hidden" id="custId" value="${param.custId}" name="custId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
			            	<td style="width: 70px">团队成员</td>
		                    <td>
		                    	<select  multiple="multiple" data-mars="CommonDao.userDict" name="userIds" class="form-control input-sm">
		                    	</select>
		                    </td>
		                </tr>
		                <tr>
		                	<td>读写权限</td>
		                	<td>
	                		  <label class="radio radio-info radio-inline" style="margin-top: 2px;">
	                          	<input type="radio" checked value="1" name="auth"><span>只读</span>
	                          </label>
	                          <label class="radio radio-info radio-inline">
	                          	<input type="radio" value="2" name="auth"><span>读写</span>
	                          </label>
		                	</td>
		                </tr>
		                <tr>
		                	<td>合同权限</td>
		                	<td>
	                		  <label class="radio radio-info radio-inline" style="margin-top: 2px;">
	                          	<input type="radio" value="1" name="htAuth"><span>有</span>
	                          </label>
	                          <label class="radio radio-info radio-inline">
	                          	<input type="radio" checked value="2" name="htAuth"><span>无</span>
	                          </label>
		                	</td>
		                </tr>
		                <tr>
		                	<td>商机权限</td>
		                	<td>
	                		  <label class="radio radio-info radio-inline" style="margin-top: 2px;">
	                          	<input type="radio"  value="1" name="sjAuth"><span>有</span>
	                          </label>
	                          <label class="radio radio-info radio-inline">
	                          	<input type="radio" checked value="2" name="sjAuth"><span>无</span>
	                          </label>
		                	</td>
		                </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="CustTeam.ajaxSubmitForm()"> 确 定  </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
       
	    var CustTeam={};
	    CustTeam.ajaxSubmitForm = function(flag) {
			var data = form.getJSONObject("#CustTeamForm");
			ajax.remoteCall("${ctxPath}/servlet/cust?action=addTeamUser",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						reloadTeamList();
						popup.layerClose("#CustTeamForm");
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		$(function(){
			 requreLib.setplugs('select2',function(){
				$("#CustTeamForm").render({success:function(){
					$("#CustTeamForm select").select2({theme: "bootstrap",placeholder:'请选择'});
				}});
			 });
		});

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>