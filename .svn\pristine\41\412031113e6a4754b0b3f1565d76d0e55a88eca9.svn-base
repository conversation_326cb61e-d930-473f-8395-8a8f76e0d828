<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>周报</title>
	<style>
		.textare-weekly,.weeklyFile{display: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="weekEditForm" data-mars="WeeklyDao.projectRecord" data-text-model="true" autocomplete="off" data-mars-prefix="weekly.">
  		   		<input type="hidden" value="${param.projectId}" name="weekly.PROJECT_ID"/>
  		   		<input type="hidden" id="weeklyId" value="${param.weeklyId}" name="weekly.WEEKLY_ID"/>
  		   		<input type="hidden" value="${param.weeklyId}" name="fkId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
						<tr>
							<td>项目名称：</td>
							<td>
								<input name="weekly.projectName" type="text" class="form-control input-sm"/>
							</td>
						</tr>
						<tr>
							<td>主题：</td>
							<td>
								<input name="weekly.TITLE" type="text" class="form-control input-sm"/>
							</td>
						</tr>
						<tr>
							<td>发出时间：</td>
							<td>
								<input name="weekly.SEND_TIME" type="text" class="form-control input-sm"/>
							</td>
						</tr>
						<tr>
							<td>收件人：</td>
							<td>
								<input name="weekly.mailtoName" type="text" class="form-control input-sm"/>
							</td>
						</tr>
			        	<tr>
			        	 	<td style="width:110px;">项目阶段：</td>
		                    <td>
			                    <input class="form-control input-sm" style="width:100%;" name="weekly.STAGE">
		                    </td>
			        	</tr>
			        	<tr class="weeklyFile">
			        	 	<td style="vertical-align: top;">周报摘要：</td>
		                    <td>
			                    <input class="form-control input-sm" name="weekly.WEEK_SUMMARY"> 
		                    </td>
			        	</tr>
			            <tr  class="textare-weekly">
			           	   <td style="width:110px;vertical-align: top;">
			           	  	 本周工作总结：
			           	   </td>
			               <td >
			             	<span id="wordText"></span>
			              </td>
			           </tr>
			           <tr class="textare-weekly">
			           	 <td style="vertical-align: top;">
			           		 下周工作计划：
			           	 </td>
			             <td >
			             	<span id="wordText2"></span>
			             </td>
			           </tr>
		              <tr>
		                <td>附件：</td>
		               	<td>
		               		<div data-template="projectWeekly-template-files" data-mars="FileDao.fileList"></div>
		               	</td>
			           </tr>
			        </tbody>
				 </table>
				 <div class="layer-foot text-c">
					  <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
				</div>
  		</form>
  		<script id="projectWeekly-template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">预览<span></a>  <a class="ml-5" title="下载"   target="_blank" href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}">下载</a></div>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		
		Edit.weeklyId='${param.weeklyId}';

		function loadWeeklyData(){
			$("#weekEditForm").render({success:function(result){
				 
				var record=result['WeeklyDao.projectRecord'];
				var data=record.data;

			    loadWeekType(data['WEEK_TYPE']);
				  
				fillRecord(data,'weekly.','',"#weekEditForm");
				
				$('#wordText').html(data['THIS_WORK']);
				$('#wordText2').html(data['NEXT_WORK']);
			
			}});  
		}
		$(function(){
			loadWeeklyData();
		});
		
		function loadWeekType(val){
			if(val=='0'){
				$('.textare-weekly').show();
				$('.weeklyFile').hide();
			}else{
				$('.textare-weekly').hide();
				$('.weeklyFile').show();
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>