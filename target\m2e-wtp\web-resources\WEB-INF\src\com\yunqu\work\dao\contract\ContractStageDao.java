package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * <AUTHOR>
 */
@WebObject(name = "ContractStageDao")
public class ContractStageDao extends AppDaoContext {

    @WebControl(name = "stageList", type = Types.LIST)
    public JSONObject stageList() {
        EasySQL sql = getEasySQL("select t1.* , ");
        sql.append("IFNULL(SUM(t2.mark_money), 0) AS KAIPIAO_AMT");
        sql.append("from yq_contract_stage t1 ");
        sql.append("left join yq_crm_invoice t2 on t1.STAGE_ID = t2.STAGE_ID");
        sql.append("where 1=1");
        sql.append(param.getString("contractId"), "and t1.CONTRACT_ID = ?");
        sql.append(param.getString("stageName"), "and t1.STAGE_NAME = ?");
        sql.append("group by t1.STAGE_ID");
        setOrderBy(sql, " order by t1.CREATE_TIME desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    /**
     * 合同看板：合同阶段列表
     *
     * @return
     */
    @WebControl(name = "myContractStageList", type = Types.LIST)
    public JSONObject myContractStageList() {
        EasySQL sql = getEasySQL("select t1.*,t2.CONTRACT_NAME,t2.CONTRACT_SIMPILE_NAME,t2.CUST_ID,t2.SALES_BY from yq_contract_stage t1 ");
        sql.append("left join yq_project_contract t2  ON t1.CONTRACT_ID = t2.CONTRACT_ID");
        sql.append("where 1=1");
        // 查询时间，今年或本月
        if ("year".equals(param.getString("stageTime"))) {
            sql.appendLike(EasyCalendar.newInstance().getYear(), "and PLAN_COMP_DATE like ?");
        } else if ("month".equals(param.getString("stageTime"))) {
            String fullMonth = EasyCalendar.newInstance().getFullMonth();
            String formattedMonth = fullMonth.substring(0, 4) + "-" + fullMonth.substring(4);
            sql.appendLike(formattedMonth, "and PLAN_COMP_DATE like ?");
        }

        //状态：待收款、全部、即将到期、本月待收款、已超时、已完成、超时完成
        if ("unComp".equals(param.getString("stageType"))) {
            sql.append("and ACT_COMP_DATE = '' ");
        } else if ("all".equals(param.getString("stageType"))) {

        } else if ("due".equals(param.getString("stageType"))) {
            sql.append(getDateAfter7Days(), "and t1.PLAN_COMP_DATE <= ?");
            sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"), "and t1.PLAN_COMP_DATE >= ?");
            sql.append("and ACT_COMP_DATE = ''");
        }
        if ("month".equals(param.getString("stageType"))) {
            sql.append(EasyDate.getCurrMonthBeginDay("yyyy-MM-dd"), "and t1.PLAN_COMP_DATE >= ?");
            sql.append(EasyDate.getCurrMonthEndDay("yyyy-MM-dd"), "and t1.PLAN_COMP_DATE <= ?");
            sql.append("and ACT_COMP_DATE = ''");
        }
        if ("timeover".equals(param.getString("stageType"))) {
            sql.append(EasyDate.getCurrentDateString("yyyy-MM-dd"), "and PLAN_COMP_DATE <= ? ");
            sql.append("and ACT_COMP_DATE = ''");
            //排除没填计划完成日期的
            sql.append("and PLAN_COMP_DATE IS NOT NULL AND PLAN_COMP_DATE != ''");
        }
        if ("complete".equals(param.getString("stageType"))) {
            sql.append("and ACT_COMP_DATE IS NOT NULL AND ACT_COMP_DATE != ''");
        }
        if ("complete2".equals(param.getString("stageType"))) {
            sql.append("and ACT_COMP_DATE IS NOT NULL AND ACT_COMP_DATE != ''");
            sql.append("and ACT_COMP_DATE > PLAN_COMP_DATE ");
            //排除没填计划完成日期的
            sql.append("and PLAN_COMP_DATE IS NOT NULL AND PLAN_COMP_DATE != ''");
        }

        //我的或全部
        if ("user".equals(param.getString("source"))) {
            sql.append(getUserId(), "and  t2.SALES_BY = ? ");
        }

        //特定年月
        if ("year".equals(param.getString("dateField"))) {
            sql.append(param.getString("yearId"), "and t1.YEAR_ID = ?");
        } else if ("month".equals(param.getString("dateField"))) {
            sql.append(param.getString("monthId"), "and t1.MONTH_ID = ?");
        }

        sql.appendLike(param.getString("contractNo"), "and t2.CONTRACT_NO like ?");
        sql.appendLike(param.getString("contractName"), "and t2.CONTRACT_NAME like ?");
        sql.appendLike(param.getString("custName"), "and t2.CUSTOMER_NAME like ?");

        setOrderBy(sql, " order by PLAN_COMP_DATE desc");
        return queryForPageList(sql.getSQL(), sql.getParams());

    }

    @WebControl(name = "stageListForSelect", type = Types.LIST)
    public JSONObject stageListForSelect() {
        EasySQL sql = getEasySQL("select t1.* from yq_contract_stage t1 ");
        sql.append("where 1=1");
        if (StringUtils.isBlank(param.getString("contractId"))) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("error", "无合同Id,请选择合同");
            return getJsonResult(new JSONObject());
        }
        sql.append(param.getString("contractId"), " and t1.CONTRACT_ID = ?");
        sql.append(param.getString("stageName"), " and t1.STAGE_NAME = ?");

        if (!isSuperUser() && !hasRole("CONTRACT_MGR") && !hasRes("CONTRACT_LOOK_AUTH")) {
            sql.append("and (");
            sql.append(getUserId(), "(t1.CREATOR = ?)");
            sql.append("or");
            sql.append(getUserId(), "(t1.UPDATE_BY = ?)");
            sql.append(")");
        }

        if (StringUtils.notBlank(param.getString("incomeStageId"))) {
            sql.append("and ( t1.INCOME_STAGE_FLAG = 0");
            sql.append(param.getString("incomeStageId"), " OR t1.INCOME_STAGE_ID = ? )");
        } else {
            sql.append("and t1.INCOME_STAGE_FLAG = 0");
        }
        setOrderBy(sql, " order by t1.INCOME_STAGE_FLAG desc");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "record", type = Types.RECORD)
    public JSONObject record() {
        String stageId = param.getString("stageId");
        if (StringUtils.notBlank(stageId)) {
            return queryForRecord("select t1.*,t2.CONTRACT_NAME from yq_contract_stage t1 left join yq_project_contract t2 on t1.CONTRACT_ID = t2.CONTRACT_ID where t1.stage_id = ?", stageId);
        } else {
            return getJsonResult(new JSONObject());
        }
    }

    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by ").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }

    @WebControl(name = "selectStage", type = Types.DICT)
    public JSONObject selectStage() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("select STAGE_ID, CONCAT(STAGE_NAME, '-比例:',RATIO,' % -金额: ', PLAN_RCV_AMT) AS STAGE_DESC from yq_contract_stage where 1=1");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and CONTRACT_ID = ?");
        sql.append("order by STAGE_NAME");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }
    @WebControl(name = "selectStageDetail", type = Types.DICT)
    public JSONObject selectStageDetail() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("select STAGE_ID, CONCAT(STAGE_NAME, '-比例:',RATIO,' % -金额: ', PLAN_RCV_AMT,'-计划完成日期: ',PLAN_COMP_DATE,'-已收金额:',RECEIVED_AMT) AS STAGE_DESC from yq_contract_stage where 1=1");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and CONTRACT_ID = ?");
        sql.append("order by STAGE_NAME");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "selectStageSimple", type = Types.DICT)
    public JSONObject selectStageSimple() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("select STAGE_ID, CONCAT(STAGE_NAME,' -金额: ', PLAN_RCV_AMT) AS STAGE_DESC from yq_contract_stage where 1=1");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and CONTRACT_ID = ?");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }


    public String getDateAfter7Days() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }

}
