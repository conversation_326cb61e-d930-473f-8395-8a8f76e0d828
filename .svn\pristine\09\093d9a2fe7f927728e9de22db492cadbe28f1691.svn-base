package com.yunqu.work.servlet;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.framework.vo.Department;
import org.easitline.common.core.framework.vo.User;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.DESUtil;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MsgModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.model.StaffRowMapper;
import com.yunqu.work.service.OAService;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.ExmailUtils;
import com.yunqu.work.utils.WebSocketUtils;

@WebServlet("/servlet/workbench/*")
public class WorkbenchServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;
	
	public void actionForIndex(){
		getUserPrincipal().getDepartment();
		User user=getUserPrincipal().getUserInfo();
		Department department=user.getDepartment();
		String pwd="";
		Object obj=getUserPrincipal().getAttribute("pwd");
		if(obj!=null){
			pwd=obj.toString();
		}
		try {
			if(StringUtils.isBlank(pwd)){
			    pwd=this.getQuery().queryForString("select USER_PWD from "+Constants.DS_MAIN_NAME+".EASI_USER_LOGIN WHERE USER_ID = ?", getUserId());
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		try {
			String open_id = this.getQuery().queryForString("select OPEN_ID from "+Constants.DS_MAIN_NAME+".EASI_USER WHERE USER_ID = ?",getUserId());
			setAttr("openId",open_id);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
	
		try {
			StaffModel staffModel = this.getMainQuery().queryForRow("select t1.ROOT_ID,t1.USER_ID,t1.DEPTS,t1.USERNAME,t1.NIKE_NAME,t1.OPEN_ID,t1.EMAIL,t1.MOBILE,t1.PIC_URL,t2.SUPERIOR,t2.STAFF_NO,t2.JOB_TITLE,t2.JOIN_DATE,t3.DEPT_ID from easi_user t1 LEFT JOIN yq_staff_info t2 on t1.USER_ID = t2.staff_user_id LEFT JOIN easi_dept_user t3 on t3.USER_ID = t1.USER_ID where t1.USER_ID = ?",new Object[]{getUserId()},new StaffRowMapper());
			getUserPrincipal().setAttribute("staffInfo",staffModel);
			setAttr("staffInfo",staffModel);
			
			String joinDate = staffModel.getJoinDate();
			if(StringUtils.notBlank(joinDate)) {
				Long joinDay = DateUtils.betweenDays(joinDate, EasyDate.getCurrentDateString("yyyy-MM-dd"));
				setAttr("joinDay", joinDay);
				
				String x = this.getMainQuery().queryForString("select  SUM(CASE WHEN join_date >=? THEN 1 ELSE 0 END)/count(1) from yq_staff_info where account_state=0 ",joinDate);
				Double d = Double.valueOf(x);
				if(d==1) {
					d = 0.90;
				}
				Double d2= d*100;
				setAttr("joinDayPpercentage", Math.round(d2));
				
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		//判断是否生日
		try {
			String born = this.getQuery().queryForString("select BORN from "+Constants.DS_MAIN_NAME+".EASI_USER where user_id = ?",getUserId());
			if(StringUtils.notBlank(born)){
				String _born = born.replaceAll("-", "").substring(4);
				String today = EasyDate.getCurrentDateString("MMdd");
				if(today.equals(_born)) {
					setAttr("isBirthday", "1");
				}
				//todo
			}else{
				
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		
		try {
			boolean bl = this.getQuery().queryForExist("select count(1) from yq_user_sign where date_id = ? and user_id = ?", EasyCalendar.newInstance().getDateInt(),getUserId());
			if(!bl) {
				EasyRecord visitReocord = new EasyRecord("yq_user_sign","user_id","date_id");
				visitReocord.set("user_id", getUserId());
				visitReocord.set("date_id", EasyCalendar.newInstance().getDateInt());
				visitReocord.set("month_id", EasyCalendar.newInstance().getFullMonth());
				visitReocord.set("visit_time", EasyDate.getCurrentDateString());
				visitReocord.set("visit_ts", System.currentTimeMillis());
				visitReocord.set("ip", WebKit.getIP(getRequest()));
				this.getQuery().save(visitReocord);
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		
		setAttr("userId",user.getUserId());
		setAttr("pwd",pwd);
		getUserPrincipal().removeAttribute("pwd");
		setAttr("department",department);
		setAttr("devLead",getUserPrincipal().isRole("DEV_MGR")?1:0);
		setAttr("deptId", getDeptId());
		
//		if(!hasRole("TOP_MGR")){
//			renderJsp("/pages/portal/top-mgr.jsp");
//		}else {
			renderJsp("/pages/index.jsp");
//		}
	}
	public EasyResult actionForPushMsg(){
		String sql="select count(1) from yq_task where assign_user_id = ? and task_state<30";
		try {
			int count = this.getQuery().queryForInt(sql, getUserId());
			if(count>0){
				WebSocketUtils.sendMessage(new MsgModel(getUserId(), "任务待办结提醒","您目前还有"+count+"任务未完成,请及时处理哦."));
			}
			count = this.getQuery().queryForInt("select count(1) from YQ_MESSAGE where RECEIVER = ? and MSG_STATE = 1", getUserId());
			if(count>0) {
				WebSocketUtils.sendMessage(new MsgModel(getUserId(), "未读消息提醒","您目前还有"+count+"条消息未读,请及时查看处理."));
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForRefreshOaCookie(){
		String account=getUserPrincipal().getLoginAcct();
		List<JSONObject> list;
		try {
			list = this.getQuery().queryForList("select * from YQ_OA_USER WHERE OA_ACCOUNT = ?",new Object[]{account},new JSONMapperImpl());
			if(list!=null&&list.size()>0){
				JSONObject jsonObject=list.get(0);
				String oaPwd=jsonObject.getString("OA_PWD");
				oaPwd =	DESUtil.getInstance().decryptStr(oaPwd);
				boolean flag=OAService.getService().login(jsonObject.getString("OA_ACCOUNT"),oaPwd);
				if(flag){
					getUserPrincipal().setAttribute("cookie",OAService.getService().getUserCookie(account));
				}
			}
			return EasyResult.ok();
		} catch (SQLException e) {
			return EasyResult.fail();
		}
	}
	public void actionForToEmail(){
		String email = getUserPrincipal().getEmail();
		if(StringUtils.isBlank(email)){
			renderHtml("您没有绑定邮箱地址,请联系行政帮忙绑定.");
		}else{
			String url = ExmailUtils.getLoginUrl(email);
			if(url!=null){
				redirect(url);
			}else{
				renderHtml("授权不通过,请联系人事确认"+email+"是否在授权内.");
			}
		}
	}
	
	public void actionForToMessage() {
		forward("/pages/my/message-list.jsp");
	}
	
	public EasyResult actionForEmailNewCount(){
		String email = getUserPrincipal().getEmail();
		if(StringUtils.isBlank(email)){
			return EasyResult.fail();
		}else{
			String count = ExmailUtils.getNewcount(email);
			if(count!=null){
				return EasyResult.ok(count);
			}else{
				return EasyResult.fail();
			}
		}
	}
	public EasyResult actionForNewMessage(){
		try {
			return EasyResult.ok(this.getQuery().queryForInt("select count(1) from YQ_MESSAGE where RECEIVER = ? and MSG_STATE = 1", getUserId()));
		} catch (SQLException e) {
			return EasyResult.ok(0);
		}
	}
	public void actionForWxAuth(){
		
	}

}
