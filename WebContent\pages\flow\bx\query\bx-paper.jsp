<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的申请</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" id="applyName" name="applyName">
						 </div>
						 <div class="input-group input-group-sm ml-5">
							 <span class="input-group-addon">报销日期</span>	
							 <input type="text" name="beginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
							 <input type="text" name="endDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
				    	 </div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">签收状态</span>	
							<select name="paperState" onchange="queryData();" class="form-control input-sm">
							      <option data-class="label label-warning" value="1">待上交</option>
                    			  <option data-class="label label-success" value="2">已收到</option>
                    			  <option data-class="label label-success" value="9">已废弃</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">所属公司</span>	
							<select name="flowCode" onchange="queryData();" class="form-control input-sm">
							      <option data-class="label label-warning" value="">--</option>
                    			  <option data-class="label label-success" value="finance_bx">云趣</option>
                    			  <option data-class="label label-success" value="finance_bx_zr">中融</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">是否提醒</span>	
							<select name="paperNoticeCount" onchange="queryData();" class="form-control input-sm">
							      <option value="">--</option>
                    			  <option value="0">未提醒</option>
                    			  <option value="1">已提醒</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>

		  </script>
		</form>
		
		 <script type="text/html" id="btnBar">
			 <button class="btn btn-sm btn-info" lay-event="toNotice">批量提醒</button>
			 <button class="btn btn-sm btn-success ml-10" lay-event="updatePaperState">单据签收</button>
			 <button class="btn btn-sm btn-danger ml-10" lay-event="removePaper">批量废弃</button>
			 <button class="btn btn-sm btn-default ml-10" lay-event="refreshData"><span class="glyphicon glyphicon-refresh"></span> 刷新</button>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'BxReportDao.paperStateQuery',
					id:'flowTable',
					page:true,
					toolbar:'#btnBar',
					title:'纸质单据状态',
					rowDoubleEvent:'flowDetail',
					height:'full-90',
					limit:50,
					limits:[30,50,100,300,500],
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },
					 {
	            	 	type: 'checkbox',
						title: '序号',
						align:'left'
					 },{
						field:'PAY_STATE',
						title:'',
						hide:true,
						templet:'<div>{{d.PAY_STATE}}</div>'
					 },{
						 field:'APPLY_NO',
						 title:'编号',
						 width:160,
						 templet:function(row){
							 return row['APPLY_NO'];
						 }
					 },{
						field:'APPLY_STATE',
						title:'流程状态',
						width:80,
						templet:function(row){
							var val = row['APPLY_STATE'];
							return flowApplyState(val);
						}
					},{
						 field:'PAY_MONEY',
						 title:'应付金额',
						 width:100
					 },{
						 field:'DO_PAY_MONEY',
						 title:'本次付款',
						 width:100
					 },{
						 field:'APPLY_REMARK',
						 title:'报销说明',
						 minWidth:120
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:140
					},{
					    field: 'APPLY_END_TIME',
						title: '审批完成时间',
						align:'center',
					    width:140
					},{
						field:'',
						title:'操作',
						width:50,
						fixed:'right',
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				]],done:function(){
					$("tbody [data-field='PAY_STATE'][data-content='11']").parent().css('background-color','#f6f1c0');
					 layui.laydate.render({max:0, min:-365,elem: "#date"});
			  	}
			});
		 }
			
		function flowDetail(data){
			var id = data['FLOW_CODE'];
			var json  = $.extend({},{businessId:data['APPLY_ID'],flowCode:id});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
		
		function refreshData(){
		   location.reload();
		}
		
		function toNotice(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
		   }
		   var array = [];
		   for(var index in dataList){
			   var row  = dataList[index];
			   var paperState = row['PAPER_STATE'];
			   if(paperState=='1'){
				   array.push(row['APPLY_ID']);
			   }
		   } 
		   ajax.remoteCall("${ctxPath}/servlet/bx/fun?action=noticeSendPaper",{ids:array.join()},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{time:800,icon:1},function(){
						queryData();
					})
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function removePaper(dataList){
		   if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
		   }
		   var array = [];
		   for(var index in dataList){
			   var row  = dataList[index];
			   var paperState = row['PAPER_STATE'];
			   if(paperState=='1'){
				   array.push(row['APPLY_ID']);
			   }
		   } 
		   layer.confirm('确认废弃吗',function(index){
			   layer.close(index);
			   ajax.remoteCall("${ctxPath}/servlet/bx/fun?action=removePaper",{ids:array.join()},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{time:800,icon:1},function(){
							queryData();
						})
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
		   });
		}
		
		
		function updatePaperState(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
			if(dataList.length > 1 && $('#applyName').val()==''){
				layer.msg('只能选一条记录。',{icon : 7, time : 1000});
				return;
			}
			layer.confirm('确认已签收报销的纸质单据吗',function(index){
				layer.close(index);
				for(var index in dataList){
					var businessId = dataList[index]['APPLY_ID'];
					ajax.remoteCall("${ctxPath}/servlet/bx/fun?action=getPaperState",{businessId:businessId},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								queryData();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
				}
			});
		}
		
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>