<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>流程查询</title>
	<style>
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
	<link href="${ctxPath}/static/css/sidebar.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
		<div class="page-box">
  			<div class="right-content" style="width: 100%;margin-left: 0px;">
  			  <div class="ibox" >
				<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="flowMgrForm">
				   <div class="ibox-title clearfix">
	          		 <div class="form-group">
	          			 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">考勤流程</span>
						 	<select class="form-control input-sm" onchange="flowList.initList();" name="flowCode" id="flowCode">
						 		<option value="ha_leave,ha_travel,kq_overtime,hr_kq_yc">全部</option>
						 		<option value="ha_leave">请假</option>
						 		<option value="ha_travel">出差</option>
						 		<option value="kq_overtime">加班</option>
						 		<option value="hr_kq_yc">漏打卡</option>
						 	</select>
						 </div>
	          			 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">申请编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">申请部门</span>
						 	<input class="form-control input-sm" name="deptName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 180px;">
					 		<span class="input-group-addon">考勤开始时间</span>
						 	 <input type="text" name="condition.DATA11_daterange_begin" data-laydate="{type:'date'}" class="form-control input-sm">
						 </div>
						 <div class="input-group input-group-sm" style="width: 180px;">
					 		<span class="input-group-addon">考勤结束时间</span>
				  			 <input type="text" name="condition.DATA12_daterange_end" data-laydate="{type:'date'}" class="form-control input-sm">
						 </div>
					  	<div class="input-group input-group-sm pull-right layui-hide-xs">
							 <button type="button" id="conditionFiter" class="btn btn-sm btn-default"><span class="glyphicon glyphicon-filter"></span> 高级查询</button>
						</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="flowList.queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	      </div>
	                </div> 
					<div class="ibox-content">
						 <table id="_flowTable"></table>
					</div>
				  </form>
				</div>
				<div class="flowQuery"></div>
  		</div>
 	 </div>
 	 <script id="queryCondition" type="text/x-jsrender">
		<form id="condtionForm">
			<table class="table table-edit table-vzebra">
			 <tr>
				<td>发起人</td>
				<td>
					<input class="form-control input-sm" name="applyName">
				</td>
				<td>显示数据</td>
				<td>
					<select class="form-control input-sm" name="approveAll">
						<option value="0">当前节点</option>
						<option value="1">全部节点</option>
					</select>
				</td>
			</tr>
			 <tr>
				<td>待办人</td>
				<td>
					<input class="form-control input-sm" name="checkName">
				</td>
				<td>流程状态</td>
				<td>
					<select name="applyState" onchange="flowList.queryData();" class="form-control input-sm">
					  <option value="">请选择 </option>
					  <option data-class="label label-default" value="0">草稿</option>
					  <option data-class="label label-warning" value="1">作废</option>
					  <option data-class="label label-warning" value="5">已挂起</option>
					  <option data-class="label label-warning" value="10">待审批</option>
					  <option data-class="label label-info" value="20">审批中</option>
					  <option data-class="label label-danger" value="21">审批退回</option>
					  <option data-class="label label-success" value="30">已完成</option>
				   </select>	
				</td>
			</tr>
			 <tr>
				 <td style="width:80px!important;">发起日期</td>	
				 <td style="width: 35%;">
				   <input type="text" name="applyBeginDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 108px;display: inline-block">
				   <input type="text" name="applyEndDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 108px;display: inline-block">
				 </td>
				 <td>紧急程度</td>
				<td>
					<select class="form-control input-sm" name="applyLevel">
						<option value="正常">正常</option>
						<option value="重要">重要</option>
						<option value="紧急">紧急</option>
					</select>
				</td>
			 </tr>
			<tr>
				<td  colspan="4">
					<button type="button" class="btn btn-sm btn-default" onclick="flowList.conditionReset()"><span class="fa fa-eraser"></span> 清空</button>
					<button type="button" class="btn btn-sm btn-info ml-10" onclick="flowList.queryData()"><span class="glyphicon glyphicon-search"></span> 查询</button>
				</td>
			</tr>
		</table>
	  </form>
    </script>
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>
		  </script>
		  <script type="text/html" id="btnBar">
				<a href="javascript:;" onclick="exportExcel()" class="btn btn-sm btn-default">导出数据</a>
		  </script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/yq-work/static/js/flow-list.js?v=20220612"></script>
	<script type="text/javascript">

		  var flowList = {flowCode:'${param.flowCode}'};
		  
		  var condtionData = {};
	
			$(function(){
				$(".page-box").render({success:function(){
					flowList.initList();
				}});
			});
			
			function intiCondition(data){
			  var queryCondition = $.templates("#queryCondition");
			  layui.dropdown.render({
				    elem: '#conditionFiter'
				    ,content: queryCondition.render({})
				    ,style: 'width: 700px;min-height: 250px; padding: 15px 15px;border-radius: 6px;box-shadow: 2px 2px 30px rgb(0 0 0 / 12%);'
				    ,ready: function(){
				    	fillRecord(condtionData,'',',','#condtionForm');
				    	renderDate('#condtionForm');
				    }
			   });
			}
			
			flowList.initList = function(){
				var param = {
						mars:'FlowDao.flowList',
						id:'_flowTable',
						page:true,
						toolbar:'#btnBar',
						rowDoubleEvent:'flowList.flowDetail',
						height:'full-95',
						limit:50,
						limits:[20,50,100,200,300,500],
						title:'流程数据',
						cellMinWidth:100,
						cols: [[
						 {
		            	 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
							field:'',
							title:'操作',
							width:50,
							event:'flowList.flowDetail',
							templet:function(row){
								return renderTpl('bar',row);
							}
						},{
							 field:'APPLY_NO',
							 title:'编号',
							 width:220,
							 templet:function(row){
								 return row['APPLY_NO']+"_"+row['FLOW_NAME'];
							 }
						 },{
							 field:'APPLY_TITLE',
							 title:'流程名称',
							 minWidth:200
						 },{
							 field:'DEPT_NAME',
							 title:'发起部门',
							 sort:true,
							 width:100
						 },{
							 field:'APPLY_NAME',
							 title:'发起人',
							 width:80
						 },{
							 field:'APPLY_REMARK',
							 title:'申请说明',
							 width:100
						 },{
						    field: 'APPLY_TIME',
							title: '申请时间',
							align:'center',
						    width:160
						},{
						    field: 'DATA11',
							title: '考勤开始时间',
							align:'center',
							sort:true,
						    width:160
						},{
						    field: 'DATA12',
							title: '考勤结束时间',
							align:'center',
							sort:true,
						    width:160
						},{
						    field: 'DATA13',
							title: '天数',
							sort:true,
							align:'center',
						    width:90
						},{
						    field: 'CHECK_TIME',
							title: '审批时间',
							align:'center',
						    width:160
						},{
							field:'APPLY_STATE',
							title:'流程状态',
							width:80,
							templet:function(row){
								var val = row['APPLY_STATE'];
								return flowApplyState(val);
							}
						},{
							field:'NODE_NAME',
							title:'节点名称',
							width:80
						},{
							field:'CHECK_NAME',
							title:'审批人',
							width:80,
							templet:function(row){
								return row['CHECK_NAME'];
							}
						},{
							field:'CHECK_RESULT',
							title:'审批结果',
							width:80,
							templet:function(row){
								var json  = {'0':'待审批','1':'审批通过','2':'审批拒绝'};
								var checkResult = row['CHECK_RESULT']||'';
								return json[checkResult]||'--';
							}
						},{
							field:'CHECK_DESC',
							title:'审批描述'
						}
					]],done:function(){
						
				  	}
				}
				FlowMgr.joinCols(param,5,function(data){
					$("#flowMgrForm").initTable(param);
					intiCondition(data);
				}); 
		 }
			
		flowList.flowDetail = function(data){
			var flowCode = data['FLOW_CODE'];
			var json  = $.extend({handle:'detail'},{businessId:data['APPLY_ID'],flowCode:flowCode,resultId:data['CURRENT_RESULT_ID']});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		flowList.queryData = function(params){
			if (params === undefined) params = {};
			var data = form.getJSONObject("#condtionForm");
			condtionData = data;
			var d = $.extend({},data,params);
			$("#flowMgrForm").queryData({id:'_flowTable',data:d});
		}
		
		flowList.conditionReset = function(){
			$("#condtionForm")[0].reset();
			flowList.queryData();
		}
	   
	   function exportExcel(){
			$("#flowMgrForm .layui-icon-export").click();
			$("#flowMgrForm .layui-table-tool-panel li").last().click();
		}
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>