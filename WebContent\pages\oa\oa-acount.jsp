<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>OA</title>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" autocomplete="off">
     		<input type="hidden" name="type" value="${param.type }"/>
			<table class="table table-edit table-vzebra">
		        <tbody>
		            <tr>
	                    <td style="width: 50px" class="required">OA账号</td>
	                    <td><input data-rules="required" readonly="readonly" value="<%=request.getRemoteUser()%>" type="text" name="account" class="form-control input-sm"></td>
		            </tr>
		            <tr>
	                    <td class="required">OA密码</td>
	                    <td><input data-rules="required" type="password" name="pwd" class="form-control input-sm"></td>
		            </tr>
		        </tbody>
					  </table>
			 <div class="layer-foot text-c">
			    	  <button type="button" class="btn btn-primary btn-sm"  onclick="subOA()"> 提 交 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
			</div>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		var subOA = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/ehr?action=addOA",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>