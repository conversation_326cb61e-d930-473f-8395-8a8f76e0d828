package com.yunqu.work.dao.other;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.utils.DateUtils;
@WebObject(name = "YearDataDao")
public class YearDataDao extends AppDaoContext {
	
	private static int thisYear = 2024;

	@WebControl(name = "getCheckUserCount",type = Types.TEMPLATE)
	public JSONObject getCheckUserCount() {
		EasySQL sql = new EasySQL();
		sql.append("select count(DISTINCT(t2.check_user_id)) count from yq_flow_apply t1,yq_flow_approve_result t2 where t1.apply_id = t2.business_id");
		sql.append(getUserId(),"and t1.apply_by = ?");
		sql.append(getUserId(),"and t2.check_user_id <> ?");
		sql.appendRLike(thisYear,"and t1.apply_date like ?");
		sql.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		sql.append(0,"and t2.check_result > ?");
		
		EasySQL sql2 = new EasySQL();
		sql2.append("select t2.check_name max_check_name,count(1) max_check_count from yq_flow_apply t1,yq_flow_approve_result t2 where t1.apply_id = t2.business_id");
		sql2.append(getUserId(),"and t1.apply_by = ?");
		sql2.appendRLike(thisYear,"and t1.apply_date like ?");
		sql2.append(getUserId(),"and t2.check_user_id <> ?");
		sql2.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		sql2.append(0,"and t2.check_result > ?");
		sql2.append("and t2.check_name<>''");
		sql2.append("group by t2.check_name order by max_check_count desc limit 1");
		
		JSONObject object = new JSONObject();
		try {
			int checkCount = this.getQuery().queryForInt(sql.getSQL(), sql.getParams());
			JSONObject record = this.getQuery().queryForRow(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
			object.put("checkUserCount",checkCount);
			object.put("record", record);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	@WebControl(name = "getWorkData",type = Types.TEMPLATE)
	public JSONObject getWorkData() {
		EasySQL sql = new EasySQL();
		sql.append("select count(1) from yq_weekly where `year` = 2024");
		sql.append(getUserId(),"and creator = ?");
		
		EasySQL sql2 = new EasySQL();
		sql2.append("select count(1) from yq_task where 1=1");
		sql2.append(getUserId(),"and assign_user_id = ?");
		sql2.appendRLike(thisYear,"and month_id like ?");
		
		EasySQL sql3 = new EasySQL();
		sql3.append("select count(1) from yq_task where 1=1");
		sql3.append(getUserId(),"and creator = ?");
		sql3.appendRLike(thisYear,"and month_id like ?");
		
		EasySQL sql4 = new EasySQL();
		sql4.append("select count(DISTINCT(project_id)) from yq_task where project_id<>''");
		sql4.append(getUserId(),"and (creator = ? or");
		sql4.append(getUserId(),"assign_user_id = ?)");
		sql4.appendRLike(thisYear,"and month_id like ?");
		
		EasySQL sql5 = new EasySQL();
		sql5.append("select count(1) count,proj_name from yq_task where project_id<>''");
		sql5.append(getUserId(),"and (creator = ? or");
		sql5.append(getUserId(),"assign_user_id = ?)");
		sql5.appendRLike(thisYear,"and month_id like ?");
		sql5.append("group by project_id");
		sql5.append("order by count desc limit 6");
		
		EasySQL sql6 = new EasySQL();
		sql6.append("select count(1) from yq_task where 1=1");
		sql6.append(getUserId(),"and (creator = ? or");
		sql6.append(getUserId(),"assign_user_id = ?)");
		sql6.appendRLike(thisYear,"and month_id like ?");
		
		JSONObject object = new JSONObject();
		try {
			int weeklyNum = this.getQuery().queryForInt(sql.getSQL(), sql.getParams());
			int doTaskNum = this.getQuery().queryForInt(sql2.getSQL(), sql2.getParams());
			int createTaskNum = this.getQuery().queryForInt(sql3.getSQL(), sql3.getParams());
			int allTaskNum = this.getQuery().queryForInt(sql6.getSQL(), sql6.getParams());
			int projectNum = this.getQuery().queryForInt(sql4.getSQL(), sql4.getParams());
			List<JSONObject> myPorject = this.getQuery().queryForList(sql5.getSQL(), sql5.getParams(), new JSONMapperImpl());
			object.put("weeklyNum", weeklyNum);
			object.put("doTaskNum", doTaskNum);
			object.put("allTaskNum", allTaskNum);
			object.put("createTaskNum", createTaskNum);
			object.put("projectNum", projectNum);
			object.put("myPorject", myPorject);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	
	@WebControl(name = "getFlowData",type = Types.TEMPLATE)
	public JSONObject getFlowData() {
		EasySQL sql = new EasySQL();
		sql.append("select sum(t1.fill_time) sum_fill_time,count(1) flow_apply_count from yq_flow_apply t1 where 1=1");
		sql.append(getUserId(),"and t1.apply_by = ?");
		sql.appendRLike(thisYear,"and t1.apply_date like ?");
		sql.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		
		EasySQL sql5 = new EasySQL();
		sql5.append("select max(t1.check_time) check_time,count(1) approve_count from yq_flow_approve_result t1 where 1=1");
		sql5.append(getUserId(),"and t1.check_user_id = ?");
		sql5.appendRLike(thisYear,"and t1.get_date like ?");
		sql5.append("0","and t1.node_id != ?");
		
		EasySQL sql2 = new EasySQL();
		sql2.append("select apply_time,apply_no,apply_title,apply_end_time,timestampdiff(MINUTE,apply_time,apply_end_time) minute_ts,timestampdiff(second,apply_time,apply_end_time) second_ts,timestampdiff(hour,apply_time,apply_end_time) hour_ts,timestampdiff(day,apply_time,apply_end_time) day_ts from yq_flow_apply where apply_end_time<>''");
		sql2.append(getUserId(),"and apply_by = ?");
		sql2.appendRLike(thisYear,"and apply_date like ?");
		sql2.append(FlowConstants.FLOW_STAT_TODO,"and apply_state >= ?");
		sql2.append("ORDER BY minute_ts limit 1");
		
		EasySQL sql4 = new EasySQL();
		sql4.append("select count(1) apply_count,t1.flow_code,t2.flow_name from yq_flow_apply t1,yq_flow_category t2 where 1=1 and t1.flow_code = t2.flow_code");
		sql4.append(getUserId(),"and t1.apply_by = ?");
		sql4.appendRLike(thisYear,"and t1.apply_date like ?");
		sql4.append(FlowConstants.FLOW_STAT_TODO,"and t1.apply_state >= ?");
		sql4.append("group by t1.flow_code");
		sql4.append("ORDER BY apply_count desc limit 6");
		
		EasySQL sql3 = new EasySQL();
		sql3.append("select flow_code,ROUND(ifnull(sum(data13),0)) day,count(1) num from yq_flow_apply where 1=1 and flow_code in('ha_leave','ha_travel','kq_overtime','hr_kq_yc')");
		sql3.append(getUserId(),"and apply_by = ?");
		sql3.appendRLike(thisYear,"and apply_date like ?");
		sql3.append(FlowConstants.FLOW_STAT_TODO,"and apply_state >= ?");
		sql3.append("GROUP BY flow_code");
		
		JSONObject object = new JSONObject();
		try {
			JSONObject flowNum = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			JSONObject maxCheckInfo = this.getQuery().queryForRow(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
			JSONObject approveInfo = this.getQuery().queryForRow(sql5.getSQL(), sql5.getParams(), new JSONMapperImpl());
			object.put("flowNum",flowNum);
			object.put("maxCheckInfo", maxCheckInfo);
			object.put("approveInfo", approveInfo);
			
			JSONObject kqData = new JSONObject();
			kqData.put("leave_day", "0");kqData.put("travel_day", "0");kqData.put("overtime_day", "0");kqData.put("kq_yc_day", "0");
			
			List<JSONObject> applyList = this.getQuery().queryForList(sql4.getSQL(), sql4.getParams(), new JSONMapperImpl());
			object.put("applyList",applyList);
			
			List<JSONObject> list = this.getQuery().queryForList(sql3.getSQL(), sql3.getParams(), new JSONMapperImpl());
			if(list!=null) {
				for(JSONObject row:list) {
					String flowCode = row.getString("FLOW_CODE");
					if("ha_leave".equals(flowCode)) {
						kqData.put("leave_day",removeLastZero(row.getString("DAY")));
					}else if("ha_travel".equals(flowCode)) {
						kqData.put("travel_day",removeLastZero(row.getString("DAY")));
					}else if("kq_overtime".equals(flowCode)) {
						kqData.put("overtime_day",removeLastZero(row.getString("DAY")));
					}else if("hr_kq_yc".equals(flowCode)) {
						kqData.put("kq_yc_day",removeLastZero(row.getString("DAY")));
					}
				}
			}
			object.put("kqData", kqData);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	private String removeLastZero(String str) {
		if(str.endsWith(".0")) {
			str = str.replace(".0", "");
		}
		return str;
	}
	
	@WebControl(name = "getTripInfo",type = Types.TEMPLATE)
	public JSONObject getTripInfo() {
		EasySQL sql = new EasySQL();
		sql.append("select GROUP_CONCAT(DISTINCT(dest_city)) from yq_flow_bx_item t1,yq_flow_apply t2 where t1.business_id = t2.apply_id and  t1.start_city<>''");
		sql.append(getUserId(),"and t2.apply_by = ?");
		sql.appendRLike(thisYear,"and t2.apply_date like ?");
		
		EasySQL sql2 = new EasySQL();
		sql2.append("select start_date,start_city,dest_city,cost_day,contract_name from yq_flow_bx_item t1,yq_flow_apply t2 where t1.business_id = t2.apply_id and  t1.start_city<>''");
		sql2.append(getUserId(),"and t2.apply_by = ?");
		sql2.appendRLike(thisYear,"and t2.apply_date like ?");
		sql2.append("order by t1.cost_day desc limit 1");
		
		JSONObject object = new JSONObject();
		try {
			String destCity = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			JSONObject record = this.getQuery().queryForRow(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
			object.put("allDestCity",destCity);
			if(record==null) {
				object.put("record", new JSONObject());
			}else {
				object.put("record", record);
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	@WebControl(name = "getLoginSysInfo",type = Types.TEMPLATE)
	public JSONObject getLoginSysInfo() {
		StaffModel staffModel = getStaffInfo();
		Long joinDay = DateUtils.betweenDays(staffModel.getJoinDate(), EasyDate.getCurrentDateString("yyyy-MM-dd"));
		
		EasySQL sql = new EasySQL();
		sql.append("SELECT count(1) visit_count,max(right(visit_time,8)) max_visit_date,min(RIGHT(visit_time,8)) min_visit_date from yq_user_sign where 1=1");
		sql.append(getUserId(),"and user_id = ?");
		sql.appendRLike(thisYear,"and month_id like ?");
		
		EasySQL sql2 = new EasySQL();
		sql2.append("select dk_date,sign_out max_sign_out from yq_kq_data where 1=1");
		sql2.append(getUserId(),"and user_id = ?");
		sql2.appendRLike(thisYear,"and dk_date like ?");
		sql2.append("order by sign_out desc limit 1");
		
		EasySQL sql3 = new EasySQL();
		sql3.append("select count(1) from yq_kq_data where 1=1");
		sql3.append(getUserId(),"and user_id = ?");
		sql3.appendRLike(thisYear,"and dk_date like ?");
		sql3.append("and late_time<>''");
		
		EasySQL sql4 = new EasySQL();
		sql4.append("select count(1) from yq_kq_data where 1=1");
		sql4.append(getUserId(),"and user_id = ?");
		sql4.appendRLike(thisYear,"and dk_date like ?");
		sql4.append("and over_time<>''");
		
		
		JSONObject object = new JSONObject();
		try {
			JSONObject record = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			object.put("joinDay", joinDay);
			object.put("record", record);
			object.put("staff", staffModel);
			
			JSONObject row = this.getQuery().queryForRow(sql2.getSQL(), sql2.getParams(),new JSONMapperImpl());
			if(row!=null) {
				object.put("maxSignOut", row.getString("MAX_SIGN_OUT"));
				object.put("maxSignOutDate",row.getString("DK_DATE"));
			}else {
				object.put("maxSignOut", "");
				object.put("maxSignOutDate","");
			}
			
			int cdCount = this.getQuery().queryForInt(sql3.getSQL(), sql3.getParams());
			object.put("cdCount",cdCount);
			
			int jbCount = this.getQuery().queryForInt(sql4.getSQL(), sql4.getParams());
			object.put("jbCount",jbCount);
			
		} catch (SQLException e) {
			this.error(null, e);
		}
		return getJsonResult(object);
	}
	
	
}
