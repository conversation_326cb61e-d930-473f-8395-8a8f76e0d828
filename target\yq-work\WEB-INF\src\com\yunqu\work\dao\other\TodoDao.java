package com.yunqu.work.dao.other;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.TodoModel;

@WebObject(name="TodoDao")
public class TodoDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		TodoModel model=new TodoModel();
		model.setColumns(getParam("todo"));
		return queryForRecord(model);
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select todo_id,title from YQ_TODO");
	}
	@WebControl(name="list",type=Types.LIST)
	public JSONObject list(){
		EasySQL sql=getEasySQL("select * from YQ_TODO where 1=1");
		sql.appendLike(param.getString("title"),"and title like ?");
		sql.append(param.getString("status")," and status = ?");
		sql.append(getUserPrincipal().getUserId()," and creator = ?");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

}
