<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table class="layui-table">
    <tr>
        <td>合同总额</td>
        <td>已收入确金额(含税)</td>
        <td>已收入确金额(税后)</td>
        <td>已收入确认占比</td>
        <td>
            <div style="float:left;">未收入确认金额(含税)
                <i class="layui-icon layui-icon-tips" lay-tips="由合同总金额 减去 收入确认金额(含税)的总和 得出。"></i></div>
        </td>
        <td>
            <div style="float:left;">未收入确认金额(税后)
            <i class="layui-icon layui-icon-tips" lay-tips="由款项金额(税后)的总和 减去 收入确认金额(税后)的总和 得出。若款项金额未全部录入，影响未收入确认(税后)数据！"></i></div>
        </td>
    </tr>
    <tr>
        <td style="width: 15%;"><b id="confirmContractAmount"></b></td>
        <td style="width: 15%;"><b id="confirmAmount"></b></td>
        <td style="width: 15%;"><b id="confirmAmountNoTax"></b></td>
        <td style="width: 15%;"><b id="confirmRatio"></b></td>
        <td style="width: 15%;"><b id="unConfirmAmount"></b></td>
        <td style="width: 15%;"><b id="unConfirmAmountNoTax"></b></td>
    </tr>
</table>


<table id="confirmTable"></table>
<script  type="text/html" id="confirmToolbar">
    <button class="btn btn-info btn-sm" onclick="IncomeConfirmList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-10" onclick="IncomeConfirmList.edit()" type="button">修改</button>
    <button class="btn btn-warning btn-sm ml-10" onclick="IncomeConfirmList.del()" type="button">删除</button>
    <button class="btn btn-default btn-sm ml-10" onclick="reloadIncomeConfirmList()" type="button">刷新</button>
</script>
<script type="text/javascript">
    var IncomeConfirmList={
        init:function(){
            $("#ContractDetailForm").initTable({
                mars:'IncomeConfirmDao.incomeConfirmList',
                id:'confirmTable',
                page:false,
                toolbar:'#confirmToolbar',
                cellMinWidth:60,
                rowEvent:'rowEvent',
                totalRow:true,
                autoSort:true,
                data:{'confirmType':'A'},
                cols: [[
                    {
                        type: 'radio',
                        align:'left',
                        totalRowText:"合计"
                    },{
                        field:'CONFIRM_DATE',
                        title:'日期',
                        width:100
                    },{
                        field:'INCOME_STAGE_ID',
                        title:'收入确认阶段',
                        minWidth:280,
                        templet:function(d){
                            return getText(d.INCOME_STAGE_ID,"incomeStageName");
                        },
                    },{
                        field:'SOURCE',
                        title:'来源',
                        align:'left',
                        width:90
                    },{
                        field:'AMOUNT_WITH_TAX',
                        title:'金额(含税)',
                        width:120,
                        totalRow:true,
                    },{
                        field:'AMOUNT_NO_TAX',
                        title:'金额(税后)',
                        width:120,
                        totalRow:true,
                    },{
                        field:'CONFIRM_DESC',
                        title:'摘要',
                        minWidth: 100
                    },{
                        field:'INVOICE_SOURCE',
                        title:'发票来源',
                        width: 100
                    },{
                        field:'CUSTOMER_NAME',
                        title:'开票客户',
                        width:140,
                        templet:'<div><a href="javascript:;" onclick="IncomeConfirmList.custDetail(\'{{d.CUST_ID}}\')">{{d.CUSTOMER_NAME}}</a></div>'
                    },{
                        field:'UPDATE_BY',
                        title:'操作人',
                        width:90
                    },{
                        field:'UPDATE_TIME',
                        title:'更新时间',
                        width:140
                    }
                ]],
                done:function(data){
                    var totalAmount = 0;
                    var totalAmountNoTax = 0;
                    $('#confirmContractAmount').text(contractInfo.AMOUNT);
                    $.each(data.data, function(index, item) {
                        var amountWithTax = parseFloat(item.AMOUNT_WITH_TAX);
                        var amountNoTax = parseFloat(item.AMOUNT_NO_TAX);
                        totalAmount += amountWithTax;
                        totalAmountNoTax += amountNoTax;
                    });

                    $('#confirmAmount').text(totalAmount.toFixed(2));
                    $('#confirmAmountNoTax').text(totalAmountNoTax.toFixed(2));

                    var totalContractAmount =  parseFloat(contractInfo.AMOUNT);
                    var unConfirm = totalContractAmount-totalAmount;
                    $('#unConfirmAmount').text(unConfirm.toFixed(2));

                    var totalPaymentNoTax = $('#contractNoTaxAmount').text();
                    totalPaymentNoTax = parseFloat(totalPaymentNoTax);
                    var unConfirmNoTax = totalPaymentNoTax - totalAmountNoTax ;
                    $('#unConfirmAmountNoTax').text(unConfirmNoTax.toFixed(2));

                    if(totalContractAmount == 0 ){
                        $('#confirmRatio').text("0%");
                    }else {
                        var ratio = totalAmount * 100 /totalContractAmount;
                        ratio = ratio.toFixed(2);
                        ratio =ratio+"%";
                        $('#confirmRatio').text(ratio);
                    }
                }
            });

        },
        add:function (){
            var custNo = contractInfo.CUST_NO;
            var custId = contractInfo.CUST_ID;
            var custName = contractInfo.CUSTOMER_NAME;
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增收入确认',offset:'auto',area:['800px','400px'],url:ctxPath+'/pages/crm/contract/include/income-confirm-edit.jsp',data:{custName:custName,custNo:custNo,custId:custId,contractId:contractId,isNew:'1',confirmType:'A'}});
        },
        edit:function(){
            var checkStatus = table.checkStatus('confirmTable');
            if(checkStatus.data.length > 1){
                layer.msg('一次只能更新一个收入确认',{icon : 7, time : 1000});
            }
            else if(checkStatus.data.length == 1){
                var confirmId = checkStatus.data[0]['CONFIRM_ID'];
                var oldStageId = checkStatus.data[0]['INCOME_STAGE_ID'];
                popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新收入确认',offset:'auto',area:['800px','400px'],url:ctxPath+'/pages/crm/contract/include/income-confirm-edit.jsp',data:{oldStageId:oldStageId,contractId:contractId,confirmId:confirmId,isNew:'0',confirmType:'A'}});
            }else{
                layer.msg("请选择!");
            }
        },
        del:function(){
            var checkStatus = table.checkStatus('confirmTable');
            if(checkStatus.data.length>0){
                layer.confirm("确认要删除吗?",{icon:3,offset:'120px'},function(){
                    ajax.remoteCall("${ctxPath}/servlet/incomeConfirm?action=BatchDel",checkStatus.data,function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                layer.closeAll();
                                reloadIncomeConfirmList();
                                reloadIncomeStageList();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                });
            }else{
                layer.msg("请选择!");
            }
        },
        custDetail:function(custId){
            var width = $(window).width();
            var w = '80%';
            if(width>1500){
                w = '800px';
            }else if(width<1000){
                w = '100%';
            }else{

            }
            popup.openTab({id:'custDetail',title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:[w,'100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId}});
        }
    }

    // 悬浮提示
    $('*[lay-tips]').on('mouseenter', function(){
        var content = $(this).attr('lay-tips');
        this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">'+ content + '</div>', this, {
            time: -1
            ,maxWidth: 280
            ,tips: [3, '#3A3D49']
        });
    }).on('mouseleave', function(){
        layer.close(this.index);
    });

    function reloadIncomeConfirmList(){
        $("#ContractDetailForm").queryData({id:'confirmTable',page:false,data:{'confirmType':'A'}});
        $("[name='incomeStageName']").render();
        reloadContractDetail();
    }
</script>
