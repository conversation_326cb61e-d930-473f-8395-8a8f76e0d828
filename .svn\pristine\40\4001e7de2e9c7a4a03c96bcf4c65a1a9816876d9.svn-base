<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>task</title>
	<style>
		.select2-container--bootstrap.select2-container--disabled .select2-selection, .select2-container--bootstrap.select2-container--disabled .select2-selection--multiple .select2-selection__choice {
   			 background-color: #fff;
   			 width: 100%!important;
		}
		.multiselect-container, .select2-container{
			width: 100%!important;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	     	<form id="editConfForm" autocomplete="off" data-mars="FlowConfigDao.nodeInfo" data-mars-prefix="conf.">
	     	    <input type="hidden" id="nodeId" value="${param.nodeId}" name="conf.NODE_ID"/>
	     	    <input type="hidden" value="${param.nodeId}" name="nodeId"/>
	     	    <input type="hidden" value="${param.flowCode}" name="conf.FLOW_CODE"/>
	     	    <input type="hidden" value="${param.flowCode}" name="flowCode"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px;">节点名称</td>
		                    <td>
		                    	<input name="conf.NODE_NAME" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                    <td>节点编码</td>
		                    <td>
		                    	<input name="conf.NODE_CODE" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                    <td>节点排序</td>
		                    <td>
		                    	<input name="conf.CHECK_INDEX" data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                      <td>状态</td>
		                    <td style="width: 15%;">
		                    	<select name="conf.NODE_STATE" class="form-control input-sm" data-rules="required">
		                    		<option value="0">启用</option>
		                    		<option value="1">暂停</option>
		                    	</select>
		                    </td>
		                </tr>
		                <tr>
		                    <td>上一级节点</td>
		                    <td>
		                    	<select name="conf.P_NODE_ID" class="form-control input-sm" data-rules="required" data-mars="FlowConfigDao.nodeDict">
		                    		<option value="0">流程开始</option>
		                    	</select>
		                    </td>
		                    <td>下一级节点</td>
		                    <td>
		                    	<select name="conf.C_NODE_ID" class="form-control input-sm" data-rules="required" data-mars="FlowConfigDao.nodeDict">
		                    		<option value="0">流程结束</option>
		                    	</select>
		                    </td>
		                    <td>起始节点</td>
		                    <td>
		                    	<select name="conf.NODE_TYPE" data-rules="required" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="1">是</option>
		                    		<option value="0" selected="selected">否</option>
		                    	</select>
		                    </td>
		                	
		                    <td>手写签字</td>
		                    <td>
		                    	<select name="conf.SIGNATURE" data-rules="required" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    		<option value="0" selected="selected">不用</option>
		                    		<option value="1">需要</option>
		                    	</select>
		                    </td>
		                </tr>
		                <tr>
		                	<td>超时时间/小时</td>
		                    <td>
		                    	<input name="conf.HOUR_LIMIT" value="0" placeholder="0代表不限制"  data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                	<td>委托</td>
		                    <td>
		                    	<select name="conf.TRUST_FLAG" class="form-control input-sm" data-rules="required">
		                    		<option value="0" selected="selected">不可以</option>
		                    		<option value="1">可以</option>
		                    	</select>
		                    </td>
		                	<td>审批方式</td>
		                    <td>
		                    	<select name="conf.SIGN_FLAG" class="form-control input-sm" data-rules="required">
		                    		<option value="0" selected="selected">或签</option>
		                    		<option value="1">会签</option>
		                    	</select>
		                    </td>
		                    <td>审批人类型</td>
		                	<td>
		                		<select name="conf.CHECK_TYPE" class="form-control input-sm" onchange="selectCheckType(this.value)" data-rules="required">
		                			<option value="1">指定人员</option>
		                			<option value="10">指定角色</option>
		                			<option value="11">指定部门</option>
		                			<option value="20">直接主管</option>
		                			<option value="22">上级主管</option>
		                			<option value="21">中心副总</option>
		                			<option value="30" selected="selected">发起人自选</option>
		                			<option value="40">发起人自己</option>
		                		</select>
		                	</td>
		                </tr>
			            <tr id="specifyUser">
		                    <td>指定成员</td>
		                    <td colspan="3">
		                    	<input type="hidden" name="conf.USER_ID"/>
		                    	<input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="conf.USER_NAME"/>
		                    </td>
		                </tr>
			            <tr id="specifyRole">
		                    <td>指定角色</td>
		                    <td colspan="3">
		                    	<select data-mars="FlowConfigDao.roleDict" name="conf.ROLE_ID" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr id="specifyDept">
		                    <td>指定部门</td>
		                    <td colspan="3">
		                    	<select data-mars="EhrDao.allDept" name="conf.CHECK_DEPT_ID" class="form-control input-sm">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
		                </tr>
		                <tr>
		                	<td>退回规则</td>
		                    <td>
		                    	<select name="conf.RETURN_RULE" class="form-control input-sm" data-rules="required">
		                			<option selected="selected" value="0">退回申请人</option>
		                			<option value="1">不能退回</option>
		                			<option value="2">只能退回上一个节点</option>
		                			<option value="3">可退回以前任意节点</option>
		                			<option value="4">可退回指定的节点</option>
		                		</select>
		                    </td>
		                	<td>流程抄送</td>
		                    <td>
		                    	<select name="conf.CC_FLAG" class="form-control input-sm" data-rules="required">
		                    		<option value="0">不可以抄送</option>
		                    		<option value="1">所有办理人都可以抄送</option>
		                    	</select>
		                    </td>
		                  	<td>默认抄送人</td>
		                    <td>
		                    	<input type="hidden" name="conf.CC_IDS"/>
		                    	<input type="text" onclick="multiUser(this)" readonly="readonly" class="form-control input-sm" name="conf.CC_NAMES"/>
		                    </td>
		                    <td>审批备注标题</td>
		                    <td>
		                    	<input name="conf.OPINION_TITLE" value="审批意见"  data-rules="required" class="form-control input-sm" type="text">
		                    </td>
		                </tr>
		                <tr>
		                  	<td>可编辑字段</td>
		                    <td colspan="7">
		                    	<textarea name="conf.EDIT_FIELD" onclick="selectText(this)" readonly="readonly" data-text="applyRemark,data1,data2,data3,data4,data5,data6,data7,data8,data9,data10,data11,data12,data13,data14,data15,data16,data17,data18,data19,data20" class="form-control input-sm" style="height: 40px;"></textarea>
		                    </td>
		                </tr>
		                <tr>
		                  	<td>条件配置<br><a href="javascript:;" onclick="editConditionConf(this)">配置</a></td>
		                    <td colspan="7">
		                    	<textarea name="conf.CONDITION_CONF" class="form-control input-sm" style="height: 40px;"></textarea>
		                    </td>
		                </tr>
		                <tr class="hidden">
		                  	<td>表单字段</td>
		                    <td colspan="7">
		                    	<textarea name="conf.FORM_FIELD" class="form-control input-sm" style="height: 40px;"></textarea>
		                    </td>
		                </tr>
		                <tr>
		                  	<td>扩展配置</td>
		                    <td colspan="7">
		                    	<textarea name="conf.EXT_CONF" class="form-control input-sm" style="height: 40px;"></textarea>
		                    </td>
		                </tr>
		                <tr>
		                  	<td>提交可执行代码</td>
		                    <td colspan="7">
		                    	<textarea name="conf.SUBMIT_CODE" class="form-control input-sm" style="height: 80px;"></textarea>
		                    </td>
		                </tr>
		                <tr>
		                  	<td>审批可执行代码</td>
		                    <td colspan="7">
		                    	<textarea name="conf.APPROVE_CODE" class="form-control input-sm" style="height: 80px;"></textarea>
		                    </td>
		                </tr>
		                <tr>
		                  	<td>节点说明</td>
		                    <td colspan="7">
		                    	<textarea name="conf.NODE_DESC" class="form-control input-sm" style="height: 40px;"></textarea>
		                    </td>
		                </tr>
		                <tr>
		                  	<td>备注</td>
		                    <td colspan="7">
		                    	<textarea name="conf.REMARK" class="form-control input-sm" style="height: 40px;"></textarea>
		                    </td>
		                </tr>
			        </tbody>
 					  </table>
				 <div class="layer-foot text-c" style="z-index: 999999999999;">
				    	  <button type="button" class="btn btn-primary btn-sm" onclick="NodeConf.ajaxSubmitForm()"> 确 定  </button>
					      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="popup.layerClose(this);"> 关闭 </button>
				</div>			
	  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
        var NodeConf={nodeId:$("#nodeId").val()};
        NodeConf.ajaxSubmitForm = function(){
			if(form.validate("#editConfForm")){
				if(NodeConf.nodeId==''){
					NodeConf.updateData('add'); 
				}else{
					NodeConf.updateData('update'); 
				}
			};
		}
		NodeConf.updateData = function(flag) {
			var data = form.getJSONObject("#editConfForm");
			ajax.remoteCall("${ctxPath}/servlet/flow/node?action="+flag+"NodeConf",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose("#editConfForm");
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		$(function(){
			 requreLib.setplugs('select2',function(){
				$("#editConfForm").render({success:function(result){
					 $("#editConfForm select").select2({theme: "bootstrap",placeholder:'请选择'});
					 if(NodeConf.nodeId){
						var nodeInfo = result['FlowConfigDao.nodeInfo'].data;
						var checkType = nodeInfo['CHECK_TYPE'];
						selectCheckType(checkType);
					 }
				}});
			 });
		});
		
		function selectCheckType(val){
			if(val=='1'){
				$('#specifyUser').show();
				$('#specifyRole,#specifyDept').hide();
			}else if(val=='10'){
				$('#specifyRole').show();
				$('#specifyUser,#specifyDept').hide();
			}else if(val=='11'){
				$('#specifyDept').show();
				$('#specifyUser,#specifyRole').hide();
			}else{
				$('#specifyUser,#specifyRole,#specifyDept').hide();
			}
		}

		function editConditionConf(el){
			
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>