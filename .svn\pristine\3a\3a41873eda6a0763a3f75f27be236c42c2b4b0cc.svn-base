<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>我的任务</title>
	<style type="text/css">
		.layui-table-cell{font-size: 13px;}
		.right-content .container-fluid{
			
		}
		.right-content .container-fluid,pt{
			padding: 0px;
		}
	</style>
	<link href="${ctxPath}/static/css/sidebar.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
  <div class="page-box">
	  <div class="left-sidebar">
           <div class="left-sidebar-item">
           		 <div class="sidebar-item">
                   <a href="javascript:void(0);" class="sidebar-nav-sub-title active"><i class="sidebar-icon glyphicon glyphicon-menu-right"></i>项目中心</a>
                   <ul class="sidebar-nav-sub">
                      <li>
                        <EasyTag:hasRole roleId="PROJECT_MANGER">
	                        <a class="activeItem" href="javascript:void(0)" data-url="${ctxPath}/pages/project/project-list.jsp?isDiv=1">所有项目</a>
                        </EasyTag:hasRole> 
               			 <a href="javascript:void(0)" data-url="${ctxPath}/pages/project/my-project-list.jsp?status=1&isDiv=1">我负责的</a>
               			 <a href="javascript:void(0)" data-url="${ctxPath}/pages/project/my-project-list.jsp?status=4&isDiv=1">我关注的</a>
                         <a href="javascript:void(0)" data-url="${ctxPath}/pages/project/my-project-list.jsp?status=2&isDiv=1">我参与的</a>
               			 <a href="javascript:void(0)" data-url="${ctxPath}/pages/project/my-project-list.jsp?status=3&isDiv=1">我创建的</a>
                      </li>
                   </ul>
                 </div>
           </div>
       </div>
  		<div class="right-content">
  		</div>
  </div>
        
        
</EasyTag:override>
<EasyTag:override name="script">
	<script>
    	$(function(){
    		$('body').on('click','.sidebar-nav-sub-title',function(e){
    			$(this).toggleClass('active')
    		})
    		$(".left-sidebar-item a").click(function(){
    			var t=$(this);
    			var url=t.data("url");
    			if(url){
  					$(".right-content").load(url);
	    			$(".left-sidebar-item a").removeClass("activeItem");
	    			t.addClass("activeItem");
    			}
    		});
    		$(".sidebar-nav-sub a").first().click();
    	});
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>