package com.yunqu.work.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.kit.PathKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.template.Engine;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.FileModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.utils.BeanMapUtilByReflect;
import com.yunqu.work.utils.exmail.EmailUtils;

public class EmailService extends AppBaseService{
	
	@Override
	public Logger getLogger() {
		return LogEngine.getLogger(Constants.APP_NAME,"email");
	}
	
	private static class Holder{
		private static EmailService service=new EmailService();
	}
	
	public static EmailService getService(){
		return Holder.service;
	}
	
	public EmailService(){
		
	}
	
	public void sendWeeklyEmail(MessageModel model){
		model.setTplName("weekly.html");
		JSONObject data = model.getData();
		JSONObject rs=new JSONObject();
		Set<String> set = data.keySet();
		for(String key:set){
			String val = data.getString(key);
			if(StringUtils.notBlank(val)){
				rs.put(key, val.replaceAll("\r\n", "<br>"));
			}
		}
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(rs);
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	public void sendProjectWeeklyEmail(MessageModel model){
		model.setTplName("projectWeekly.html");
		JSONObject data = model.getData();
		JSONObject rs=new JSONObject();
		Set<String> set = data.keySet();
		for(String key:set){
			String val = data.getString(key);
			if(StringUtils.notBlank(val)){
				rs.put(key, val.replaceAll("\r\n", "<br>"));
			}
		}
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(rs);
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	public void sendTaskEmail(MessageModel model){
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(model.getData());
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	public void sendNoticeWriteWeeklyEmail(MessageModel model){
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate("weeklyNotice.html").renderToString(model.getData());
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	public void sendTaskCommentEmail(MessageModel model){
		Map<String,String> map=new HashMap<String, String>();
		map.put("title", model.getTitle());
		map.put("desc", model.getDesc());
		map.put("fkId",model.getFkId());
		String url = model.getUrl();
		if(StringUtils.notBlank(url)) {
			if(!url.startsWith("http")) {
				map.put("url","http://172.16.68.152"+model.getUrl());
			}else {
				map.put("url",model.getUrl());
			}
		}
		model.setTitle(model.getTitle());
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate(model.getTplName()).renderToString(map);
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	
	public void sendFlowCommentEmail(MessageModel model){
		Map<String,String> map=new HashMap<String, String>();
		map.put("title", model.getTitle());
		map.put("desc", model.getDesc());
		map.put("fkId",model.getFkId());
		map.put("sendName",model.getSendName());
		model.setTitle(model.getTitle());
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate("flowComment.html").renderToString(map);
		model.setDesc(bodyHtml);
		sendEmail(model);
	}
	
	public void sendFlowNoticeEmail(MessageModel model){
		Map<String,Object> map=new HashMap<String, Object>();
		map.putAll(BeanMapUtilByReflect.beanToMap(model));
		if(model.getData()!=null) {
			map.putAll(BeanMapUtilByReflect.beanToMap(model.getData()));
		}
		
		String desc = model.getDesc();
		if(StringUtils.notBlank(desc)) {
			desc = desc.replaceAll("\n", "<br>");
		}
		
		map.put("desc",desc );
		map.put("msgLabel", model.getMsgLabel());
		String title = model.getTitle();
		if(StringUtils.notBlank(title)&&!title.contains("流程审批")) {
			if(StringUtils.notBlank(model.getMsgLabel())) {
				map.put("title", "【流程审批】"+model.getMsgLabel()+"_"+title);
			}else {
				map.put("title","【流程审批】"+model.getTitle());
			}
		}else {
			map.put("title",title);
		}
		
		String bodyHtml=Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/email").getTemplate("flowNotice.html").renderToString(map);
		model.setTitle(map.get("title").toString());
		model.setDesc(bodyHtml);
		this.sendEmail(model);
	}
	
	public void sendEmail(MessageModel model){
		String[] receivers = model.getReceivers();
		String email = null;
		if(receivers==null) {
			String userId = model.getReceiver();
			if(StringUtils.isAnyBlank(userId,model.getSender())) {
				getLogger().error("sendEmail>"+userId+">"+model.getSender()+">"+model.getTitle());
				return;
			}
			if(userId.equalsIgnoreCase(model.getSender())) {
				getLogger().warn(userId+":收发人不能是同一个人.");
				//return;
			}
			email = getUserEmails(userId);
		}else {
			email = getUserEmails(receivers);
		}
		if(StringUtils.notBlank(email)){
			String userName = null;
			if(StringUtils.notBlank(model.getEmailSender())) {
				userName = model.getEmailSender();
			}else {
				userName =  getUserName(model.getSender());
			}
			if(StringUtils.notBlank(userName)) {
				this.getLogger().info(userName+">"+email+">"+model.getTitle()+">"+model.getDesc());
				this.asynMail(userName,email,getUserEmails(model.getCc()),model.getTitle(),model.getDesc(),model.getFileList());
			}
		}
	}
	
	private void asynMail(String userName,String mailto,String cc,String title,String desc,List<FileModel> fileList) {
		getLogger().info("开始发邮件："+mailto+","+title);
		if(!ServerContext.isLinux()&&!Constants.isProd()) {
			return;
		}
		String msgSwitch = AppContext.getContext(Constants.APP_NAME).getProperty("msgSwitch","0");
		if("1".equals(msgSwitch)) {
			new Thread(new Runnable() {
				@Override
				public void run() {
					try {
						EmailUtils.sendMsgMail(userName,mailto,cc,title, desc,fileList);
						getLogger().info(userName+" send email:"+mailto+"----"+title+"----"+desc);
						Thread.sleep(1000*15);
					} catch (Exception e) {
						Thread.currentThread().interrupt();
						getLogger().error(e.getMessage(),e);
					}
				}
			}).start();
		}
	}
	
	public String getUserName(String userId){
		if(StringUtils.isBlank(userId)) {
			return "system";
		}
		return StaffService.getService().getUserDeptName(userId)+StaffService.getService().getUserName(userId);
	}
	
	private String getEmailInfo(String userId) {
		if(StringUtils.notBlank(userId)&&!userId.contains("null")) {
			String email = StaffService.getService().getUserEmail(userId);
			if(StringUtils.notBlank(email)) {
				return email+"_"+getUserName(userId);
			}
		}
		this.getLogger().error("getEmailInfo>"+userId);
		return "";
	}
	
	
	private String getUserEmails(String... userIds){
		if(userIds==null||userIds.length==0)return "";
		if(userIds.length==1){
			return getEmailInfo(userIds[0]);
		}else{
			String sb=new String();
			for(String id:userIds){
				String email = getEmailInfo(id);
				if(StringUtils.notBlank(email)) {
					sb = sb + email +",";
				}
			}
			if(sb.length()>0) {
				sb = sb.substring(0,sb.length() - 1);
			}
			return sb.toString();
		}
	}

}
