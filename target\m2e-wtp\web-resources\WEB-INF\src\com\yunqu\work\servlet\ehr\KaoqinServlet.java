package com.yunqu.work.servlet.ehr;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.Globals;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.KqNoticeService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.DateUtils;
/**
 * <AUTHOR>
 * 非工作日 https://blog.csdn.net/u011456337/article/details/86180383
 *
 */
@WebServlet("/servlet/kaoqin/*")
@MultipartConfig
public class KaoqinServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	
	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME+"-kq";
	}

	public EasyResult actionForAddLeaveOnKq() {
		String dateId = getPara("dateId");
		KqNoticeService.getService().addKqRecord(dateId);
		KqNoticeService.getService().addLeaveOnKq(dateId);
		return EasyResult.ok();
	}
	
	public EasyResult actionForRefreshKqFlow() {
		String dateId = getPara("dateId");
		String monthId = getPara("monthId");
		String flag = getJsonPara("flag");
		if(StringUtils.isBlank(flag)) {
			flag = getPara("flag");
		}
		if(StringUtils.notBlank(flag)) {
			monthId = EasyCalendar.newInstance().getFullMonth();
		}
		if(StringUtils.notBlank(monthId)) {
			for(int i=1;i<=31;i++) {
				String fullDay = String.valueOf(monthId+""+i);
				if(i<10) {
					fullDay = String.valueOf(monthId+"0"+i);
				}
				KqNoticeService.getService().addLeaveOnKq(fullDay);
			}
		}else {
			KqNoticeService.getService().addLeaveOnKq(dateId);
		}
		return EasyResult.ok();
	}


	public EasyResult actionForInitKqObj() {
		String dateId = getPara("dateId");
		KqNoticeService.getService().addKqRecord(dateId);
		return EasyResult.ok();
	}
	
	public EasyResult actionForNotKqCount() {
		String date = getJsonPara("date");
		try {
			int count = this.getQuery().queryForInt("select count(1) from yq_flow_apply where flow_code='hr_kq_yc' and apply_by = ? and LEFT(data2,7) = ?",getUserId(),date);
			return EasyResult.ok(count);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(0);
	}
	
	public EasyResult actionForUpdatePeople() {
		String userId = getJsonPara("userId");
		try {
			this.getQuery().executeUpdate("update yq_kq_people set state = 1 where kq_user_id = ?",userId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdatePeople2() {
		String userId = getJsonPara("userId");
		try {
			this.getQuery().executeUpdate("update yq_kq_people set state = 0 where kq_user_id = ?",userId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelPeople() {
		String userId = getJsonPara("userId");
		try {
			this.getQuery().executeUpdate("delete from yq_kq_people where kq_user_id = ?",userId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForMonthKqNotice() {
		String monthId = getPara("monthId");
		String flag = getJsonPara("flag");
		if("2".equals(flag)) {
			monthId = DateUtils.getPrevMonth();
		}
		KqNoticeService.getService().monthKqNotice(getUserId(),monthId);
		return EasyResult.ok();
	}

	private EasyRecord getPeopleModel(String prefix){
		EasyRecord kqPeopleModel = new EasyRecord("yq_kq_people","kq_user_id");
		kqPeopleModel.setColumns(getJSONObject(prefix));
		return kqPeopleModel;
	}
	
	public EasyResult actionForAddPeople(){
		EasyRecord kqPeopleModel = getPeopleModel("kqPeople");
		try {
			boolean bl = this.getQuery().update(kqPeopleModel);
			if(!bl) {
				 this.getQuery().save(kqPeopleModel);
			}
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}


	public EasyResult actionForBatchDel(){
		JSONArray kqPeopleArray = getJSONArray();
		for(int i = 0;i < kqPeopleArray.size();i++){
			JSONObject kqPeopleObject = kqPeopleArray.getJSONObject(i);
			try {
				this.getQuery().executeUpdate("delete from yq_kq_people where kq_user_id = ?",kqPeopleObject.getString("KQ_USER_ID") );
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}

	public EasyResult actionForBatchRemoveKq(){
		JSONArray kqPeopleArray = getJSONArray();
		for(int i = 0;i < kqPeopleArray.size();i++){
			JSONObject kqPeopleObject = kqPeopleArray.getJSONObject(i);
			try {
				this.getQuery().executeUpdate("update yq_kq_people set state = 1 where kq_user_id = ?",kqPeopleObject.getString("KQ_USER_ID") );
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}

	public EasyResult actionForBatchAddKq(){
		JSONArray kqPeopleArray = getJSONArray();
		for(int i = 0;i<kqPeopleArray.size();i++){
			JSONObject kqPeopleObject = kqPeopleArray.getJSONObject(i);
			try {
				this.getQuery().executeUpdate("update yq_kq_people set state = 0 where kq_user_id = ?",kqPeopleObject.getString("KQ_USER_ID") );
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
		return EasyResult.ok();
	}

	public EasyResult actionForGetTodayWxDk() {
		try {
		  JSONObject row = this.getQuery().queryForRow("select * from YQ_KQ_OBJ where USER_ID = ? and DK_DATE = ?", new Object[] {getUserId(), EasyCalendar.newInstance().getDateInt()}, new JSONMapperImpl());
		  return EasyResult.ok(row);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.fail();
	}
	
	@Deprecated
	public EasyResult actionForManualSign() {
		JSONObject params = getJSONObject();
		try {
			int signTimeType = params.getIntValue("signTimeType");
			EasyRecord record = new EasyRecord("YQ_KQ_SIGN","DK_DATE","USER_ID");
			record.set("IP", WebKit.getIP(getRequest()));
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("USER_ID", getUserId());
			record.set("DK_DATE", EasyCalendar.newInstance().getDateInt());
			
			boolean existData  = this.getQuery().update(record);
			if(!existData) {
				record.set("KQ_ID",RandomKit.uuid());
				record.set("REMARK", "网页打卡");
				record.set("DEPT_ID", getDeptId());
				record.set("USER_NAME", getRemoteUser());
				record.set("STAFF_ID", getStaffInfo().getStaffNo());
				record.set("DEPT", getStaffInfo().getDeptName());
				this.getQuery().save(record);
			}
			JSONObject cityInfo = params.getJSONObject("city");
			String address  = cityInfo.getString("province")+cityInfo.getString("city")+cityInfo.getString("district")+params.getString("address")+"("+params.getString("name")+")";
			
			if(signTimeType==0) {
				boolean bl = this.getQuery().queryForExist("select count(1) from YQ_KQ_SIGN where DK_DATE = ? and USER_ID = ? and SIGN_IN <>''",EasyCalendar.newInstance().getDateInt(),getUserId());
				if(bl) {
					return EasyResult.fail("今日已签到过，请勿重复");
				}
				record.set("ADDRESS_IN", address);
				record.set("SIGN_IN", EasyDate.getCurrentDateString("HH:mm"));
				this.getQuery().update(record);
				//微信推送
			}else {
				record.set("ADDRESS_OUT", address);
				record.set("SIGN_OUT", EasyDate.getCurrentDateString("HH:mm"));
				this.getQuery().update(record);
				//微信推送
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForWxDk() {
		JSONObject params = getJSONObject();
		this.getLogger().info(getRemoteUser()+">>"+params.toJSONString());
		
		String dkTime = EasyDate.getCurrentDateString();
		try {
			int signTimeType = params.getIntValue("signTimeType");
			int dateId = EasyCalendar.newInstance().getDateInt();
			
			// 检查是否已打卡
			if(signTimeType == 0) {
				JSONObject kqRow = this.getQuery().queryForRow(
					"select SIGN_IN from YQ_KQ_OBJ where DK_DATE = ? and USER_ID = ?",
					new Object[] {dateId, getUserId()},
					new JSONMapperImpl()
				);
				if(kqRow != null && StringUtils.notBlank(kqRow.getString("SIGN_IN"))) {
					String signIn = kqRow.getString("SIGN_IN");
					String time2 = "09:00";
					LocalTime lt1 = LocalTime.parse(signIn);
					LocalTime lt2 = LocalTime.parse(time2);
					if (!lt1.isBefore(lt2)) {
						return EasyResult.fail("已有今日上班卡,请勿重复打卡");
					}
				}
			}
			
			EasyRecord record = new EasyRecord("YQ_KQ_OBJ","DK_DATE","USER_ID");
			record.set("IP", WebKit.getIP(getRequest()));
			record.set("device",getRequest().getHeader("User-Agent"));
			record.set("CREATE_TIME", dkTime);
			record.set("USER_ID", getUserId());
			record.set("DK_DATE", dateId);
			record.set("DATE_ID", dateId);
			record.set("NOT_WORK","N");
			record.set("MONTH_ID", EasyCalendar.newInstance().getFullMonth());
			record.set("DK_CITY", params.getString("city"));
			if(KqNoticeService.getService().kqFlag()) {
				record.set("DK_RANK", "正常班次");
			}else {
				record.set("DK_RANK", "加班");
			}
			
			try {
				boolean existData = this.getQuery().update(record);
				if(!existData) {
					record.set("KQ_ID",RandomKit.uuid());
					record.set("DEPT_ID", getDeptId());
					record.set("USER_NAME", getUserName());
					record.set("STAFF_ID", getStaffInfo().getStaffNo());
					record.set("DEPT_NAME", getStaffInfo().getDeptName());
					record.set("KQ_CITY", getStaffInfo().getWorkCity());
					this.getQuery().save(record);
				}
			} catch(SQLException e) {
				// 如果是唯一键冲突,说明并发打卡,返回友好提示
				if(e.getMessage().contains("Duplicate entry")) {
					return EasyResult.fail("您的打卡请求正在处理中,请稍后再试");
				}
				throw e;
			}
			
			String address = params.getString("address");
			if(signTimeType==0) {
				record.set("ADDRESS_IN", address);
				record.set("SIGN_IN", EasyDate.getCurrentDateString("HH:mm"));
				record.set("LATE_TIME", calcLateMin());
				record.set("SIGN_INT_TIME", EasyDate.getCurrentDateString());
				record.set("LOCATION_IN", params.getString("location"));
				record.set("DISTANCE_IN", params.getString("distance"));
				record.set("ADDRESS_COMPONENT_IN", params.getString("addressComponent"));
				String remark = params.getString("remark");
				if(StringUtils.notBlank(remark)) {
					record.set("REASON_IN", remark);
					record.set("KQ_TYPE", "sbDwYc");
					record.set("TRUE_TO","N");
				}else {
					record.set("REASON_IN","");
					record.set("KQ_TYPE", "normal");
					record.set("TRUE_TO","Y");
				}
				this.getQuery().update(record);
				
				MessageModel model = new MessageModel();
				model.setReceiver(getUserId());
				model.setSender(getUserId());
				model.setTitle("打卡地点："+address);
				model.setData1(getUserName());
				model.setData2(EasyDate.getCurrentDateString());
				model.setData3("上班打卡成功");
//				model.setDesc(KqNoticeService.getService().getZaoanWord());
				model.setUrl("https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/myKqRecord");
				WxMsgService.getService().sendKqDkNotice(model);
				
			}else {
				record.set("ADDRESS_OUT", address);
				record.set("SIGN_OUT", EasyDate.getCurrentDateString("HH:mm"));
				record.set("OVER_TIME", calcOverTime());
				record.set("SIGN_OUT_TIME", EasyDate.getCurrentDateString());
				record.set("LOCATION_OUT", params.getString("location"));
				record.set("DISTANCE_OUT", params.getString("distance"));
				record.set("ADDRESS_COMPONENT_OUT", params.getString("addressComponent"));
				String remark = params.getString("remark");
				if(StringUtils.notBlank(remark)) {
					record.set("REASON_OUT", remark);
					record.set("KQ_TYPE", "xbDwYc");
					record.set("TRUE_TO","N");
				}else {
					record.set("REASON_OUT","");
					record.set("KQ_TYPE", "normal");
					record.set("TRUE_TO","Y");
				}
				this.getQuery().update(record);
				
				MessageModel model = new MessageModel();
				model.setReceiver(getUserId());
				model.setSender(getUserId());
				model.setTitle("打卡地点："+address);
				model.setData1(getUserName());
				model.setData2(EasyDate.getCurrentDateString());
				model.setData3("下班打卡成功");
				model.setDesc(KqNoticeService.getService().getTq(params.getString("city")));
				model.setUrl("https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/myKqRecord");
				WxMsgService.getService().sendKqDkNotice(model);
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(dkTime);
	}
	

	/**
	  * 漏打卡补卡
	 * @return
	 */
	public EasyResult actionForSetBK() {
		JSONObject params = getJSONObject();
		String applyId = params.getString("applyId");
		String applyNo = params.getString("applyNo");
		String applyState = params.getString("applyState");
		String bkDate = params.getString("bkDate");
		String bkType = params.getString("bkType");
		String applyBy = params.getString("applyBy");
		String applyRemark = params.getString("applyRemark");
		if(StringUtils.isAnyBlank(bkDate,bkType,applyId,applyBy)){
			return EasyResult.fail("补卡时间/补卡类型为空，未能更新考勤记录！");
		}
		String dateId = bkDate.replaceAll("-", "");
		try {
			EasyRecord kqRecord = new EasyRecord("yq_kq_obj", "USER_ID", "DK_DATE");
			kqRecord.set("USER_ID", applyBy);
			kqRecord.set("DK_DATE", dateId);
			kqRecord.set("APPLY_ID", applyId);
			kqRecord.set("APPLY_REMARK", applyRemark);
			kqRecord.set("KQ_TYPE", "hr_kq_yc");
			kqRecord.set("APPLY_NO", applyNo);
			kqRecord.set("APPLY_STATE", applyState);
			kqRecord.set("NOT_WORK","N");
			kqRecord.set("TRUE_TO","Y");
			if ("上班".equals(bkType)) {
				kqRecord.set("SIGN_IN", "09:00");
				kqRecord.set("LATE_TIME", "");
				kqRecord.set("SIGN_INT_TIME", bkDate + " " + "09:00:00");
				kqRecord.set("REMARK","上班补签");
			} else if ("下班".equals(bkType)) {
				kqRecord.set("SIGN_OUT", "18:00");
				kqRecord.set("SIGN_OUT_TIME", bkDate + " " + "18:00:00");
				kqRecord.set("REMARK","下班补签");
			} else if ("整天".equals(bkType)) {
				kqRecord.set("SIGN_IN", "09:00");
				kqRecord.set("LATE_TIME", "");
				kqRecord.set("SIGN_INT_TIME", bkDate + " " + "09:00:00");
				kqRecord.set("SIGN_OUT", "18:00");
				kqRecord.set("SIGN_OUT_TIME", bkDate + " " + "18:00:00");
				kqRecord.set("REMARK", "整天补签");
			}
			this.getQuery().update(kqRecord);
		} catch (SQLException e) {
			this.getLogger().error("考勤记录自动补卡失败:"+e.getMessage(),e);
			return EasyResult.fail("考勤记录更新失败");
		}
		return EasyResult.ok();
	}

	/**
	 * 请假或出差流程更新考勤
	 * @return
	 */
	public EasyResult actionForSetLeaveOrTravel() {
		JSONObject params = getJSONObject();
		String applyId = params.getString("applyId");
		KqNoticeService.getService().addLeaveOnKqByDate(getUserId(), applyId, null,null);
		return EasyResult.ok();
	}
	

	public EasyResult actionForUploadFile()  throws SQLException, IOException, ServletException {
		EasyResult result = new EasyResult();
		try {
			Part part = this.getFile("kqData");
			String filename = new String(getFilename(part).getBytes(), "UTF-8"); 
			String separator = File.separator;
			String path=Globals.SERVER_DIR+File.separator+"kaoqin";
			if (!new File(path).exists()) {
				new File(path).mkdirs();
			}
			File tmpWarFile = new java.io.File(path + separator + EasyDate.getCurrentDateString("yyyyMMddHHmmss")+ FileKit.getHouzui(filename));
			if (tmpWarFile.exists()) {
				tmpWarFile.delete();
			}
			
			FileKit.saveToFile(part.getInputStream(), tmpWarFile.getAbsolutePath());
			
		    Workbook workbook = WorkbookFactory.create(part.getInputStream());
		    List<List<String>> list = new ArrayList<>();
		    int num = workbook.getNumberOfSheets();
		    
		    int succCount = 0;
		    int lastCellNum=0;
		    for(int index =0 ;index < num ;index++){
		    	Sheet sheet = workbook.getSheetAt(index);
		    	int maxLine =sheet.getLastRowNum();
		    	for (int ii = 0; ii <= maxLine; ii++) {
		    		List<String> rows = new ArrayList<>();
		    		Row row = sheet.getRow(ii);
		    		lastCellNum=row.getLastCellNum();
		    		if(ii==0){
		    			if(lastCellNum<5){
		    				return EasyResult.fail("excel不匹配!");
		    			}
		    		}
		    		if(row!=null){
		    			for(int j=0;j<lastCellNum;j++){
		    				Cell cell=row.getCell(j);
		    				if(cell!=null){
		    					String val = Utils.getCellValue(cell);
		    					if(StringUtils.isBlank(val)){
		    						rows.add("");
		    					}else{
		    						rows.add(val);
		    					}
		    				}else{
		    					rows.add("");
		    				}
		    			}
		    			list.add(rows);
		    		}
		    	}
		    }
		 
		     Map<Integer,String> fieldNameIndex = new LinkedHashMap<Integer,String>();
		     List<String> headerRow = list.get(0);
		     for(int i=0;i<headerRow.size();i++) {
		    	String name =  headerRow.get(i);
		    	fieldNameIndex.put(i, name);
		     }
		     list.remove(0);
		     
		     
		      Map<String,String> fields=new LinkedHashMap<String,String>();
		      fields.put("考勤号码","STAFF_ID");
		      fields.put("姓名", "USER_NAME");
		      fields.put("日期", "DK_DATE");
		      fields.put("对应时段","DK_RANK");
		      fields.put("签到时间","SIGN_IN");
		      fields.put("签退时间","SIGN_OUT");
		      fields.put("例外情况","REMARK");
		      fields.put("加班时间","OVER_TIME");
		      fields.put("实到","TRUE_TO");
		      fields.put("是否旷工","NOT_WORK");
		      fields.put("迟到时间","LATE_TIME");
		      fields.put("部门", "DEPT");
		      fields.put("出勤时间","SUM_TIME");
		      
		      String batchId = RandomKit.uuid();
		      
		      boolean isNotNull = true;
			  for(int j=0;j<list.size();j++){
				List<String> strs=list.get(j);
				if(strs.size()>0){
					EasyRecord record=new EasyRecord("YQ_KQ_DATA","STAFF_ID","DK_DATE");
					for(int x=0;x<lastCellNum;x++){
						String title = fieldNameIndex.get(x);
						String field = fields.get(title);
						if(StringUtils.isNotBlank(title)&&StringUtils.notBlank(field)) {
							String value=StringUtils.trimToEmpty(strs.get(x));
							if("DK_DATE".equals(field)) {
								value = dkDateFormat(value);	
								Integer intDate = Integer.valueOf(value);
								if(intDate==null) {
									  System.out.println(intDate);
								 }
								 if(intDate>EasyCalendar.newInstance().getDateInt()) {
									 isNotNull = false;
								  }
								  
								  record.set("DATE_ID", value);
								  record.set("MONTH_ID", value.substring(0, 6));
							}
							if("STAFF_ID".equals(field)&&"4".equals(value)) {
								value = "100294";
							}
							record.set(field, value);
						}
					}
					if(!isNotNull) {
						isNotNull = true;
						continue;
					}
					boolean b=this.getQuery().update(record);
					if(!b){
						record.set("CREATE_TIME", EasyDate.getCurrentDateString());
						record.put("BATCH_ID",batchId);
						record.set("KQ_ID", RandomKit.uniqueStr());
						getQuery().save(record);
						succCount++;
						this.getLogger().info(succCount+" obj upload succ.");
					}
			   }
		    }
			  
			EasyRecord record=new EasyRecord("YQ_KQ_BATCH","BATCH_ID");
			record.setPrimaryValues(batchId);
			record.set("upload_time", EasyDate.getCurrentDateString());
			record.set("file_name",filename);
			record.set("excel_count",succCount);
			record.set("file_path",tmpWarFile.getAbsolutePath());
			record.set("upload_by",getRemoteUser());
			this.getQuery().save(record);
			
			this.getQuery().executeUpdate("update yq_kq_batch t1 set t1.kq_scope = CONCAT((select min(DK_DATE) from yq_kq_data t2 where t2.batch_id = t1.batch_id),'~',(select max(DK_DATE) from yq_kq_data t2 where t2.batch_id = t1.batch_id)) where t1. batch_id = ?", batchId);
			
			this.getQuery().executeUpdate("update yq_kq_data t1,"+Constants.DS_MAIN_NAME+".yq_staff_info t2 set t1.user_id = t2.staff_user_id where t1.STAFF_ID = t2.staff_no and t1.batch_id = ?",batchId);
			
			this.getQuery().executeUpdate("UPDATE yq_kq_data t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_dept_user t2 ON t1.user_id = t2.user_id SET t1.dept_id = t2.dept_id WHERE t1.batch_id = ?",batchId);
			
			this.getQuery().executeUpdate("update yq_kq_batch t1 set t1.excel_count = (select count(1) from yq_kq_data t2 where t2.batch_id = t1.batch_id)");
			result.put("state", 1);
			result.put("msg", succCount+"条数据上传成功!");
			part.delete();
			return result;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.addFail(e.getMessage());
			return result;
		}finally {
			
		}
	}


    private String dkDateFormat(String dateStr) {
    	try {
    		dateStr = dateStr.replaceAll(" ", "");
    		SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");  
    		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");  
    		Date date = sdf.parse(dateStr);
    	    return sdf2.format(date);
    	} catch (Exception e) {  
    		e.printStackTrace();  
    	}
    	return "";  
    }
    
    private String calcLateMin() {
    	try {
			SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
			long from = simpleFormat.parse(EasyDate.getCurrentDateString()).getTime();
			long to = simpleFormat.parse(EasyDate.getCurrentDateString("yyyy-MM-dd")+" 09:00").getTime();
			int minutes = (int) ((from-to)/(1000 * 60));
			if(minutes<=0) {
				return "";
			}
			return String.valueOf(minutes);
		} catch (ParseException e) {
			this.error(null, e);
			return "";
		}
    }
    
    private String calcOverTime() {
    	try {
    		SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    		long from = simpleFormat.parse(EasyDate.getCurrentDateString()).getTime();
    		long to = simpleFormat.parse(EasyDate.getCurrentDateString("yyyy-MM-dd")+" 18:00").getTime();
    		int minutes = (int) ((from-to)/(1000 * 60));
    		if(minutes<=0) {
    			return "";
    		}
    		return String.valueOf(minutes);
    	} catch (ParseException e) {
    		this.error(null, e);
    		return "";
    	}
    }
	
	 private String getFilename(Part part) {  
        String contentDispositionHeader = part.getHeader("content-disposition");  
        String[] elements = contentDispositionHeader.split(";");  
        for (String element : elements) {  
            if (element.trim().startsWith("filename")) {  
                return element.substring(element.indexOf('=') + 1).trim().replace("\"", "");  
            }  
        }  
        return null;  
	}

	 public EasyResult actionForDelBatch(){
		 String batchId = getJsonPara("batchId");
		 try {
			this.getQuery().executeUpdate("delete from yq_kq_data where batch_id = ?", batchId);
			this.getQuery().executeUpdate("delete from yq_kq_batch where batch_id = ?", batchId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		 return EasyResult.ok();
	 }
	 public EasyResult actionForSetFlowId(){
		 JSONObject params = getJSONObject();
		 String flowId=params.getString("flowId");
		 String kqId=params.getString("kqId");
		 try {
			 this.getQuery().executeUpdate("update yq_kq_data set flow_id = ? where kq_id = ?", flowId,kqId);
		 } catch (SQLException e) {
			 e.printStackTrace();
		 }
		 return EasyResult.ok();
	 }
	 
	 public void actionForExportWxKqRecord(){
			String _data = getPara("data");
			JSONObject param = JSONObject.parseObject(_data);
			List<String> headers=new ArrayList<String>();
			
			EasySQL sql = new EasySQL("select * from yq_kq_obj where 1=1");
			
			String lateFlag = param.getString("lateFlag");
			if(StringUtils.notBlank(lateFlag)) {
				sql.append("and LATE_TIME <>''");
			}
			String missedPunchFlag = param.getString("missedPunchFlag");
			if(StringUtils.notBlank(missedPunchFlag)) {
				sql.append("and length(concat(SIGN_IN,SIGN_OUT)) = 5 and REMARK = ''");
			}
			String absenteeismFlag = param.getString("absenteeismFlag");
			if(StringUtils.notBlank(absenteeismFlag)) {
				sql.append("Y","and NOT_WORK = ? and REMARK = ''");
			}
			sql.appendRLike(param.getString("kqCity"),"and kq_city like ?");
			String monthId = param.getString("monthId");
			if(StringUtils.notBlank(monthId)) {
				sql.append(monthId.replaceAll("-", ""),"and month_id = ?");
			}
			sql.appendRLike(param.getString("kqCity"),"and kq_city like ?");
			sql.appendRLike(param.getString("userName"),"and user_name like ?");
			sql.appendRLike(param.getString("deptName"),"and dept_name like ?");
			
			String hrFlag = param.getString("hrFlag");
			if("1".equals(hrFlag)&&(hasRole("HR_MGR")||isSuperUser()||hasRole("DEPT_MGR"))) {
				
			}else if(hasRole("DEPT_MGR")) {
				sql.append(getDeptId(),"and dept_Id = ?");
			}else{
				sql.append(getUserId(),"and user_Id = ?");
			}

			String beginDate = param.getString("beginDate");
			String endDate = param.getString("endDate");
			
			if(StringUtils.notBlank(beginDate)) {
				sql.append(beginDate.replaceAll("-",""),"and DK_DATE >= ?");
			}else{
				renderHtml("考勤开始日期不能为空");
				return;
			}
			if(StringUtils.notBlank(endDate)) {
				sql.append(endDate.replaceAll("-",""),"and DK_DATE <= ?");
			}
			sql.append("order by DK_DATE desc");
			
			File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
			/**创建头部*/
			headers.add("工号");
			headers.add("部门");
			headers.add("姓名");
			headers.add("考勤日期");
			headers.add("班次");
			headers.add("上班时间");
			headers.add("下班时间");
			headers.add("迟到时间");
			headers.add("加班时间");
			headers.add("例外情况");
			headers.add("流程备注");
			headers.add("流程编号");
			headers.add("流程状态");
			headers.add("上班打卡地址");
			headers.add("下班打卡地址");
			headers.add("打卡城市");
			headers.add("上班偏差距离");
			headers.add("下班偏差距离");
			headers.add("上班异常定位");
			headers.add("下班异常定位");
			
			List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
			int x=0;
			for(String header:headers){
				ExcelHeaderStyle style=new ExcelHeaderStyle();
				style.setData(header);
				if(x==1){
					style.setWidth(4000);
				}else {
					style.setWidth(3500);
				}
				style.setBackgroundColor(IndexedColors.GREY_40_PERCENT.index);
				styles.add(style);
				x++;
			}
			
			List<List<String>> excelData=new ArrayList<List<String>>();
			EasyQuery query=this.getQuery();
			query.setMaxRow(50000);
			
			List<JSONObject> data = null;
			try {
				data = query.queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			} catch (SQLException e) {
				this.error(null, e);
				renderHtml(e.getMessage());
				return;
			}
			
			if(data!=null && data.size()>0){
				for (int i = 0; i < data.size(); i++) {
					JSONObject map = data.get(i);
					List<String> list=new ArrayList<String>();
					list.add(map.getString("STAFF_ID"));
					list.add(map.getString("DEPT_NAME"));
					list.add(map.getString("USER_NAME"));
					list.add(map.getString("DK_DATE"));
					list.add(map.getString("DK_RANK"));
					list.add(map.getString("SIGN_IN"));
					list.add(map.getString("SIGN_OUT"));
					list.add(map.getString("LATE_TIME"));
					list.add(map.getString("OVER_TIME"));
					list.add(map.getString("REMARK"));
					list.add(map.getString("APPLY_REMARK"));
					list.add(map.getString("APPLY_NO"));
					list.add(getApplyState(map.getString("APPLY_STATE")));
					list.add(map.getString("ADDRESS_IN"));
					list.add(map.getString("ADDRESS_OUT"));
					list.add(map.getString("DK_CITY"));
					list.add(map.getString("DISTANCE_IN"));
					list.add(map.getString("DISTANCE_OUT"));
					list.add(map.getString("REASON_IN"));
					list.add(map.getString("REASON_OUT"));
					excelData.add(list);
				}
			}
			try {
				ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			} catch (IOException e) {
				this.error(null, e);
				renderHtml(e.getMessage());
				return;
			}
			if(StringUtils.isBlank(endDate)){
				endDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
			}
			String fileName="微信考勤数据-"+beginDate+"-"+endDate+".xlsx";
			renderFile(file,fileName,true);
		}

		private String getApplyState(String applyNo){
			Map<String,String> map = new HashMap<String,String>();
			map.put("0","草稿");
			map.put("1","作废");
			map.put("5","已挂起");
			map.put("10","待审批");
			map.put("20","审批中");
			map.put("21","审批退回");
			map.put("30","已完成");
			return map.get(applyNo);
		}	
  }
