<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<div id="fileList">
    <table class="layui-table" lay-size="sm">
	  <thead>
	    <tr>
	      <th>序号</th>
	      <th>文件名</th>
	      <th>文件大小</th>
	      <th>上传时间</th>
	      <th>上传人</th>
	      <th>下载次数</th>
	      <th>操作</th>
	    </tr> 
	  </thead>
	  <tbody data-template="template-files" data-mars="FileDao.fileListDetail">

	  </tbody>
	</table>
</div>
<button class="btn btn-sm btn-info addFile mt-5" type="button" onclick="$('#localfile').click()">+上传</button>
						    	
<script id="template-files" type="text/x-jsrender">
	{{call:data fn = 'setCount'}}
	{{if data.length==0}}
		<tr>
			<td colspan="7">暂无数据</td>
		</tr>
	{{/if}}
	{{for data}}
		<tr id="tr_{{:FILE_ID}}" data-id="{{:FILE_ID}}">
		  <td>{{:#index+1}}</td>
		  <td>{{call:FILE_TYPE fn='getFileIcon'}}<a href="/yq-work/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}} &nbsp;</span></a></td>
		  <td>{{:FILE_SIZE}}</td>
		  <td>{{:CREATE_TIME}}</td>
		  <td>
			  {{:CREATE_NAME}}
		  </td>
		  <td>
			  {{:DOWNLOAD_COUNT}}
		  </td>
		  <td>
			  <a href="/yq-work/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" target="_blank"><span style="color:#17a6f0">下载<span></a>
			  {{call:CREATOR FILE_ID fn='getDelFileElement'}}
		  </td>
		</tr>
   {{/for}}
</script>
<script>
	var uploadFile = function(){
		var fkId = Review.reviewId;
		easyUploadFile({callback:'callback',fkId:fkId,pFkId:fkId,fileLabel:fileLabel||'0',source:'contract',formId:'fileForm',fileId:'localfile'});
		
	}
	function setCount(data){
		$("#fileCount").text(data.length);
	}
	
	var callback = function(data){
		$("#fileList").render({data:{fkId:Review.reviewId}});
	}

</script>