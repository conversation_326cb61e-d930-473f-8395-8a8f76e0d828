package com.yunqu.work.servlet.erp;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.WxMsgService;

@WebServlet("/servlet/order/flow")
public class OrderFlowServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	/**
	 * 执行审批
	 * @return
	 */
	public EasyResult actionForAddCheck() {
		EasyRecord record = new EasyRecord("yq_erp_order_review_result","result_id");
		try {
			JSONObject object = getJSONObject();
			String reviewId = object.getString("reviewId");
			String orderId = object.getString("orderId");
			String resultId = object.getString("resultId");
			String nodeId = object.getString("nodeId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("评审人不存在");
			}
			int checkResult = object.getIntValue("checkResult");//1 通过 2拒绝
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("review_id", reviewId);
			record.set("order_id", orderId);
			record.set("check_user_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc", object.getString("checkDesc"));
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			String applyBy = this.getQuery().queryForString("select apply_by from yq_erp_order_review where review_id = ?", reviewId);
			
			MessageModel msgModel = new MessageModel();
			msgModel.setSender(getUserId());
			msgModel.setFkId(reviewId);
			msgModel.setData1("采购评审");
			msgModel.setData2(getUserName());
			msgModel.setData3(EasyDate.getCurrentDateString());
			msgModel.setTitle("采购评审有新的进展");
			msgModel.setTypeName("FLOW_ORDER_REVIEW_CHECK");
			msgModel.setSendName(getUserName());
			
			if(checkResult==2) {
				this.getQuery().executeUpdate("update yq_erp_order_review set review_state = ?,last_result_time = ? where review_id = ?",21,EasyDate.getCurrentDateString(),reviewId);
				msgModel.setData4("评审退回");
				msgModel.setDesc(object.getString("checkDesc"));
				msgModel.setReceiver(applyBy);
				WxMsgService.getService().sendFlowTodoCheck(msgModel);
				MessageService.getService().sendMsg(msgModel);
				return EasyResult.ok();
			}
			
			//获取下一级节点
			String nextNodeId = this.getQuery().queryForString("select c_node_id from yq_erp_order_check_node where node_id = ?", nodeId);
			
			int reviewState = 10; // 10 待审批 20审批中 21 拒绝 30审批完成
			
			if("0".equals(nextNodeId)) {//完成了
				reviewState = 30;
				msgModel.setReceiver(applyBy);
			}else {
				reviewState = 20;
				JSONObject nodeInfo= this.getQuery().queryForRow("select * from yq_erp_order_check_node where node_id = ?", new Object[]{nextNodeId}, new JSONMapperImpl());

				msgModel.setReceiver(nodeInfo.getString("USER_ID"));
				EasyRecord resultRecord = new EasyRecord("yq_erp_order_review_result","result_id");
				resultRecord.set("result_id", RandomKit.uniqueStr());
				resultRecord.set("review_id", reviewId);
				resultRecord.set("order_id", orderId);
				resultRecord.set("check_name", nodeInfo.getString("USER_NAME"));
				resultRecord.set("check_user_id", nodeInfo.getString("USER_ID"));
				resultRecord.set("check_result", 0);
				resultRecord.set("node_id",nextNodeId);
				resultRecord.set("check_desc","");
				resultRecord.set("get_time",EasyDate.getCurrentDateString());
				this.getQuery().save(resultRecord);
				
				this.getQuery().executeUpdate("update yq_erp_order_review set current_node_id = ? where review_id = ?",nextNodeId,reviewId);
			}
			
			this.getQuery().executeUpdate("update yq_erp_order_review set current_result_id = ?,review_state = ?,last_result_time = ? where review_id = ?", resultId,reviewState,EasyDate.getCurrentDateString(),reviewId);
			
			WxMsgService.getService().sendFlowTodoCheck(msgModel);
			MessageService.getService().sendMsg(msgModel);

		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForRestartReview() {
		JSONObject params = getJSONObject();
		try {
			String reviewId = params.getString("reviewId");
			this.firstReview(reviewId,params.getString("orderId"));
			this.getQuery().executeUpdate("update yq_erp_order_review set review_state = ? where review_id = ?",10,reviewId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForRestartPayApply() {
		JSONObject params = getJSONObject();
		try {
			String paymentId = params.getString("paymentId");
			this.firstPayment(paymentId);
			this.getQuery().executeUpdate("update yq_erp_order_payment set payment_state = ? where payment_id = ?",10,paymentId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddPaymentCheck() {
		EasyRecord record = new EasyRecord("yq_erp_order_payment_result","result_id");
		try {
			JSONObject object = getJSONObject();
			String paymentId = object.getString("paymentId");
			String resultId = object.getString("resultId");
			String nodeId = object.getString("nodeId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("评审人不存在");
			}
			int checkResult = object.getIntValue("checkResult");//1 通过 2拒绝
			String checkDesc = object.getString("checkDesc");
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("payment_id", paymentId);
			record.set("check_user_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc",checkDesc );
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			String applyBy = this.getQuery().queryForString("select apply_by from yq_erp_order_payment where payment_id = ?", paymentId);
			
			MessageModel msgModel = new MessageModel();
			msgModel.setSender(getUserId());
			msgModel.setFkId(paymentId);
			msgModel.setTitle("采购付款申请有新的进展");
			msgModel.setData1("采购付款申请");
			msgModel.setData2(getUserName());
			msgModel.setData3(EasyDate.getCurrentDateString());
			msgModel.setTypeName("FLOW_ORDER_PAY_CHECK");
			msgModel.setSendName(getUserName());
			
			if(checkResult==2) {//退回
				this.getQuery().executeUpdate("update yq_erp_order_payment set payment_state = ?,last_result_time = ? where payment_id = ?",21,EasyDate.getCurrentDateString(),paymentId);
				msgModel.setReceiver(applyBy);
				msgModel.setData4("审批退回");
				msgModel.setDesc(checkDesc);
				WxMsgService.getService().sendFlowTodoCheck(msgModel);
				MessageService.getService().sendMsg(msgModel);
				return EasyResult.ok();
			}
			
			//获取下一级节点
			String nextNodeId = this.getQuery().queryForString("select c_node_id from yq_erp_order_check_node where node_id = ?", nodeId);
			
			int reviewState = 0; // 10 待审批 20审批中 21 退回 30审批完成
			if("0".equals(nextNodeId)) {//完成了
				reviewState = 30;
				msgModel.setReceiver(applyBy);
				msgModel.setData4("审批完成");
			}else {
				reviewState = 20;
				
				JSONObject nodeInfo= this.getQuery().queryForRow("select * from yq_erp_order_check_node where node_id = ?", new Object[]{nextNodeId}, new JSONMapperImpl());
				
				msgModel.setReceiver(nodeInfo.getString("USER_ID"));
					
				//生成下一个节点审批记录
				EasyRecord paymentResult = new EasyRecord("yq_erp_order_payment_result","result_id");
				paymentResult.set("result_id", RandomKit.uniqueStr());
				paymentResult.set("payment_id", paymentId);
				paymentResult.set("check_name", nodeInfo.getString("USER_NAME"));
				paymentResult.set("check_user_id", nodeInfo.getString("USER_ID"));
				paymentResult.set("check_result", 0);
				paymentResult.set("node_id",nextNodeId);
				paymentResult.set("check_desc","");
				paymentResult.set("get_time",EasyDate.getCurrentDateString());
				this.getQuery().save(paymentResult);
				msgModel.setData4("请尽快审批");
			
				this.getQuery().executeUpdate("update yq_erp_order_payment set current_node_id = ? where payment_id = ?",nextNodeId,paymentId);
			}
			this.getQuery().executeUpdate("update yq_erp_order_payment set current_result_id = ?,payment_state = ?,last_result_time = ? where payment_id = ?", resultId,reviewState,EasyDate.getCurrentDateString(),paymentId);
			WxMsgService.getService().sendFlowTodoCheck(msgModel);
			MessageService.getService().sendMsg(msgModel);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	/**
	 * 发起评审
	 * @return
	 */
	public EasyResult actionForReviewApply() {
		JSONObject object = getJSONObject();
		String id = RandomKit.uniqueStr();
		
		String sql = "select GROUP_CONCAT(user_id) user_ids,GROUP_CONCAT(user_name) user_names from yq_erp_order_check_node where busi_type='review' and node_state = 0 ORDER BY check_index";
		JSONObject checkObj = null;
		try {
			checkObj = this.getQuery().queryForRow(sql,new Object[] {},new JSONMapperImpl());
		} catch (SQLException e) {
			this.error(null, e);
		}
		
		String userIds = checkObj.getString("USER_IDS");
		String userNames = checkObj.getString("USER_NAMES");
		String orderId = object.getString("orderId");
		String reviewLevel = object.getString("reviewLevel");
		EasyRecord record = new EasyRecord("yq_erp_order_review","review_id");
		record.set("apply_time", EasyDate.getCurrentDateString());
		record.set("apply_by", getUserId());
		record.set("review_level", reviewLevel);
		record.set("review_state", object.getString("reviewState"));
		record.set("review_no", object.getString("reviewNo"));
		record.set("remark", object.getString("remark"));
		record.set("order_id", orderId);
		record.set("check_ids", userIds);
		record.set("check_names", userNames);
		record.set("create_name", getUserName());
		record.setPrimaryValues(id);
		try {
			this.getQuery().save(record);
			this.getQuery().executeUpdate("update yq_erp_order set review_id = ?,last_follow_label = '采购评审',last_follow_time = ?  where order_id = ?",id,EasyDate.getCurrentDateString(),orderId);
			
			this.firstReview(id,orderId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void firstReview(String reviewId,String orderId) throws SQLException {
		JSONObject obj = this.getQuery().queryForRow("select * from yq_erp_order_check_node where p_node_id = 0 and busi_type = ? and node_state = 0 order by check_index", new Object[] {"review"}, new JSONMapperImpl());
		
		EasyRecord resultRecord = new EasyRecord("yq_erp_order_review_result","result_id");
		String nodeId = obj.getString("NODE_ID");
		String userId =obj.getString("USER_ID");
		String userName =obj.getString("USER_NAME");
		resultRecord.set("result_id", RandomKit.uniqueStr());
		resultRecord.set("review_id", reviewId);
		resultRecord.set("order_id", orderId);
		resultRecord.set("check_name", userName);
		resultRecord.set("check_user_id", userId);
		resultRecord.set("check_result", 0);
		resultRecord.set("node_id",nodeId);
		resultRecord.set("check_desc","");
		resultRecord.set("get_time",EasyDate.getCurrentDateString());
		this.getQuery().save(resultRecord);
		this.getQuery().executeUpdate("update yq_erp_order_review set current_node_id = ?  where review_id = ?",nodeId,reviewId);
		
		//微信推送给审批人
		MessageModel msgModel  = new MessageModel();
		msgModel.setSender(getUserId());
		msgModel.setFkId(reviewId);
		msgModel.setTitle("您有一个新的采购评审");
		msgModel.setTypeName("FLOW_ORDER_REVIEW_CHECK");
		msgModel.setReceiver(userId);
		msgModel.setData1("采购评审");
		msgModel.setData2(getUserName());
		msgModel.setData3(EasyDate.getCurrentDateString());
		WxMsgService.getService().sendFlowTodoCheck(msgModel);
		MessageService.getService().sendMsg(msgModel);
	}
	
	
	
	public EasyResult actionForPaymentApply() {
		JSONObject object = getJSONObject();
		String id = RandomKit.uniqueStr();
		
		String sql = "select GROUP_CONCAT(user_id) user_ids,GROUP_CONCAT(user_name) user_names from yq_erp_order_check_node where busi_type='payment' and node_state = 0 ORDER BY check_index";
		JSONObject checkObj = null;
		try {
			checkObj = this.getQuery().queryForRow(sql,new Object[] {},new JSONMapperImpl());
		} catch (SQLException e) {
			this.error(null, e);
		}
		
		String userIds = checkObj.getString("USER_IDS");
		String userNames = checkObj.getString("USER_NAMES");
		String orderIds = object.getString("orderIds");
		String planId = object.getString("planId");
		String paymentLevel = object.getString("paymentLevel");
		EasyRecord record = new EasyRecord("yq_erp_order_payment","payment_id");
		record.set("apply_time", EasyDate.getCurrentDateString());
		record.set("supplier_id", object.getString("supplierId"));
		record.set("apply_by", getUserId());
		record.set("apply_name", object.getString("applyName"));
		record.set("amount", object.getString("amount"));
		record.set("plan_pay_date", object.getString("planPayDate"));
		record.set("payment_level", paymentLevel);
		record.set("payment_state", object.getString("paymentState"));
		record.set("payment_no", object.getString("paymentNo"));
		record.set("remark", object.getString("remark"));
		record.set("order_id", orderIds);
		record.set("check_ids", userIds);
		record.set("order_json", object.getString("orderJson"));
		record.set("check_names", userNames);
		record.set("create_name", getUserName());
		record.setPrimaryValues(id);
		try {
			this.getQuery().save(record);
			this.getQuery().executeUpdate("update yq_erp_order_pay_plan set payment_id = ? where plan_id = ?", id,planId);
			
			JSONArray orderJsonArray = object.getJSONArray("orderJsonArray");
			for(int i=0;i<orderJsonArray.size();i++) {
				JSONObject orderJson  = orderJsonArray.getJSONObject(i);
				String orderId = orderJson.getString("ORDER_ID");
				this.getQuery().executeUpdate("update yq_erp_order t1 set t1.has_apply_amount = (select sum(apply_amount) from yq_erp_order_payment_detail t2 where t2.order_id =  t1.order_id) where t1.order_id = ?", orderId);
				
				EasyRecord orderPayment = new EasyRecord("yq_erp_order_payment_detail");
				orderPayment.setColumns(orderJson);
				orderPayment.set("payment_id", id);
				orderPayment.set("apply_time", EasyDate.getCurrentDateString());
				this.getQuery().save(orderPayment);
			}
			this.firstPayment(id);
				
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	private void firstPayment(String paymentId) throws SQLException {
		JSONObject obj = this.getQuery().queryForRow("select * from yq_erp_order_check_node where p_node_id = 0 and busi_type = ? and node_state = 0 order by check_index", new Object[] {"payment"}, new JSONMapperImpl());
		
		EasyRecord resultRecord = new EasyRecord("yq_erp_order_payment_result","result_id");
		String nodeId = obj.getString("NODE_ID");
		String userId =obj.getString("USER_ID");
		String userName =obj.getString("USER_NAME");
		resultRecord.set("result_id", RandomKit.uniqueStr());
		resultRecord.set("payment_id", paymentId);
		resultRecord.set("check_name", userName);
		resultRecord.set("check_user_id", userId);
		resultRecord.set("check_result", 0);
		resultRecord.set("node_id",nodeId);
		resultRecord.set("check_desc","");
		resultRecord.set("get_time",EasyDate.getCurrentDateString());
		this.getQuery().save(resultRecord);
		this.getQuery().executeUpdate("update yq_erp_order_payment set current_node_id = ?  where payment_id = ?",nodeId,paymentId);
		
		//微信推送给审批人
		MessageModel msgModel  = new MessageModel();
		msgModel.setSender(getUserId());
		msgModel.setData1("采购付款申请");
		msgModel.setFkId(paymentId);
		msgModel.setData2(getUserName());
		msgModel.setTitle("采购付款申请有新的进展");
		msgModel.setData3(EasyDate.getCurrentDateString());
		msgModel.setReceiver(userId);
		WxMsgService.getService().sendFlowTodoCheck(msgModel);
	}
	
	
	private void addOrderLog(String orderId,String followContent,String followState,String followStateLabel) {
		EasyRecord model = new EasyRecord("YQ_ERP_ORDER_FOLLOW","FOLLOW_ID");
		model.set("FOLLOW_DATE", EasyCalendar.newInstance().getDateInt());
		model.set("ADD_TIME", EasyDate.getCurrentDateString());
		model.set("ADD_BY", getUserId());
		model.set("USER_NAME", getUserName());
		model.set("FOLLOW_STATE", followState);
		model.set("FOLLOW_STATE_LABEL", followStateLabel);
		model.set("FOLLOW_CONTENT", followContent);
		try {
			this.getQuery().executeUpdate("update yq_erp_order set last_follow_content = ?,last_follow_time = ?,last_follow_label = ? where order_id = ?",followContent,EasyDate.getCurrentDateString(),followStateLabel,orderId);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
}
