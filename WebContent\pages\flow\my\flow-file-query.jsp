<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>流程文件管理</title>
	<style>
		.layui-btn{display: none;}
		.layui-table-hover .layui-btn{
			display: inline-block;
		}
		#folderName a,span{color: #999;margin: 0px 3px;}
		.hover{background-color: #daddb5;padding: 5px 8px;font-size: 12px;border-radius: 40%;margin-left: 8px;}
		.layui-table-hover .hover{opacity:1;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm" data-page-hide="true">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		    <div class="input-group input-group-sm" <c:if test="${!empty param.source}">style="display:none;"</c:if>>
					 		<span class="input-group-addon">来源</span>
						 	<select class="form-control input-sm" data-mars="FlowConfigDao.flowDict" onchange="list.query()" id="source" name="source">
	                    		<option value="">请选择</option>
	                    	</select>
						 </div>
						 <c:if test="${!empty param.source}">
						 	<input name="sources" type="hidden" value="${param.source}"/>
						 </c:if>
	          		     <div class="input-group input-group-sm" style="width: 200px;">
							 <span class="input-group-addon">文件名称</span>	
							 <input type="text" name="fileName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px;">
							 <span class="input-group-addon">流程编号</span>	
							 <input type="text" name="applyNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px;">
							 <span class="input-group-addon">流程标题</span>	
							 <input type="text" name="applyTitle" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						<table class="layui-hide" id="files"></table>					    
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			$('#searchForm').render({success:function(){
				list.init();
			}});
		});
		var currentUserId=getCurrentUserId();
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'FlowDao.flowFileList',
					id:'files',
					page:true,
					limit:30,
					height:'full-90',
					rowDoubleEvent:'list.detail',
					cols: [[
					 {type:'numbers',width:50,align:'center',title:'序号'},
		             {
					    field: 'FILE_NAME',
						title: '文件名称',
						minWidth:150,
						align:'left',
						templet:function(row){
							return getFileIcon(row['FILE_TYPE'])+'<a href="javascript:;" onclick="list.detail(\''+row['FILE_ID']+'\')">'+row.FILE_NAME+'</a>'+'<a href="javascript:;" onclick="list.previw(\''+row['FILE_ID']+'\')" class="hidden">预览</a><a href="javascript:;" onclick="list.downloadFile(\''+row['FILE_ID']+'\')" class="hover">下载</a>';
						}
					},{
						field:'APPLY_TITLE',
						minWidth:250,
						align:'left',
						title:'流程标题',
						event:'flowDetail',
						templet:'<div>{{d.APPLY_NO}}-{{d.APPLY_TITLE}}</div>'
					},{
						field:'APPLY_STATE',
						width:80,
						templet:function(row){
							return flowApplyState(row['APPLY_STATE']);
						}
					},{
						field:'CREATE_TIME',
						width:160,
						align:'center',
						title:'上传时间'
					},{
						field:'DOWNLOAD_COUNT',
						title:'下载次数',
						align:'center',
						style:'text-decoration: underline;',
						event:'dlLayer',
						width:80
					},{
					    field: 'FILE_SIZE',
						title: '文件大小',
						align:'center',
						sort:true,
						width:85,
						templet:function(row){
							return row.FILE_SIZE;
						}
					},{
					    field: 'SOURCE',
						title: '来源',
						align:'left',
						sort:true,
						width:95,
						templet:function(row){
							var _source = row['SOURCE'];
							_source = _source.substring(5);
							return getText(_source,'#source');
						}
					},{
					    field: 'CREATOR',
						title: '上传人',
						align:'center',
						width:100,
						templet:function(row){
							return getUserName(row.CREATOR);
						}
					}
					]],done:function(res){
						
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(obj){
				var id = '';
				if($.isPlainObject(obj)){
					id = obj['FILE_ID']
				}else{
					id = obj;
				}
				window.open('${ctxPath}/fileview/'+id+'?view=online');
			},
			previw:function(id){
				window.open('${ctxPath}/fileview/'+id+'?view=online');
			},
			downloadFile:function(id){
				window.open('${ctxPath}/fileview/'+id);
			}
		}
		
		function reloadFile(){
			list.query();
		}
		
		function flowDetail(data){
			console.log(data);
			window.open("${ctxPath}/web/flow/"+data['APPLY_ID']);
		}
		
		function dlLayer(data){
			var fkId = data['FK_ID'];
			var fileId = data['FILE_ID'];
			popup.layerShow({url:'/yq-work/pages/common/download-log.jsp',full:fullShow(),area:['650px','400px'],scrollbar:false,offset:'20px',data:{fkId:fkId,fileId:fileId},id:'downloadLogLayer',title:'下载日志'});
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>