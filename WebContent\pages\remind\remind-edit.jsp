<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>todo</title>
	<style>
		#editor img{max-width: 100%;height: auto;}
		#editor{line-height: 26px;}
	</style>
	<script src="${ctxPath}/static/js/emotions.js" type="text/javascript"></script>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="RemindDao.record" autocomplete="off" data-mars-prefix="remind.">
     		  			<input type="hidden" value="${param.remindId}" id="remindId" name="remind.REMIND_ID"/>
     		  			<input type="hidden" id="categoryName" name="remind.CATEGORY_NAME"/>
     		  			<input type="hidden" value="${param.remindId}" name="fkId"/>
     		  			<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
						<table class="table table-edit table-vzebra">
					        <tbody>
					            <tr>
				                    <td style="width: 80px" class="required">标题</td>
				                    <td><input data-rules="required"  type="text" name="remind.TITLE" class="form-control input-sm"></td>
					            </tr>
					            <tr>
					            	<td class="required">类别</td>
					            	<td>
						            	<select name="remind.REMIND_TYPE" id="remindType" data-rules="required" class="form-control input-sm">
						            		<option value="">请选择</option>
						            		<optgroup label="默认">
							            		<option value="1">日常公告</option>
							            		<option value="2">新闻动态</option>
							            		<option value="3">每周菜单</option>
							            		<option value="0">分享</option>
							            		<!-- <option value="4">人事调动</option>
							            		<option value="5">招聘信息</option> -->
						            		</optgroup>
						            		<!-- <optgroup label="fly">
						            		  <option value="10">提问</option> 
						                      <option value="20">分享</option> 
						                      <option value="30">讨论</option> 
						                      <option value="40">建议</option> 
						                      <option value="50">公告</option> 
						                      <option value="60">动态</option> 
						            		</optgroup> -->
						            	</select>
					            	</td>
					            </tr>
					            <tr>
				                    <td style="width: 80px" class="required">发布人</td>
				                    <td><input  type="text" data-mars="CommonDao.userName" name="remind.PUBLISH_BY" class="form-control input-sm"></td>
					            </tr>
					            <tr>
				                    <td style="width: 80px" class="required">发布时间</td>
				                    <td><input data-mars="CommonDao.date02" type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" name="remind.PUBLISH_TIME" class="form-control input-sm Wdate"></td>
					            </tr>
					            <tr>
				                    <td class="required">状态</td>
				                    <td>
				                     	<label class="radio radio-success radio-inline">
					                    	<input type="radio"value="0" checked="checked" name="remind.STATUS"/><span>正常</span>
					                    </label>
					                     <label class="radio radio-success radio-inline">
					                    	<input type="radio"value="1" name="remind.STATUS"/><span>草稿</span>
					                    </label>
					                     <label class="radio radio-success radio-inline">
					                    	<input type="radio"value="2" name="remind.STATUS"/><span>删除</span>
					                    </label>
				                    </td>
					            </tr>
  								<tr>
				                    <td>置顶</td>
				                    <td>
				                    	 <label class="radio radio-success radio-inline">
					                    	<input type="radio"value="0" name="remind.RECOMMEND_FLAG"/><span>是</span>
					                    </label>
					                    <label class="radio radio-success radio-inline">
					                    	<input type="radio" checked="checked"  value="1" name="remind.RECOMMEND_FLAG"/><span>否</span>
					                    </label>
				                    </td>
					            </tr>
					            <tr>
				                    <td class="required">内容</td>
				                    <td>
				                       <div id="editor"></div>
			                           <textarea id="wordText" data-text="false" style="height: 200px;width:300px;display: none;" class="form-control input-sm" name="remind.CONTENT"></textarea>
				                    
				                    </td>
					            </tr>
					            <tr>
				                    <td>摘要</td>
				                    <td>
			                           <textarea style="height: 80px;" class="form-control input-sm" name="remind.SUMMARY"></textarea>
				                    </td>
					            </tr>
					            <tr>
				                    <td>封面</td>
				                    <td>
			                           	<input type="text" id="coverUrl" name="remind.COVER_URL" style="width: 200px;display: inline-block;" class="form-control input-sm"/>
			                           	<button class="btn btn-xs btn-info upload-btn" type="button">上传</button>
			                           	<a href="#" id="coverUrlHref" target="_blank">访问</a>
				                    </td>
					            </tr>
					            <tr>
					            	<td>相关附件</td>
					            	<td>
										<div data-template="template-files" data-mars="FileDao.fileList"></div>
					               		<div id="fileList"></div>
										<button class="btn btn-sm btn-default mt-5" type="button" onclick="$('#localfile').click()">+添加</button>
					            	</td>
					            </tr>
					        </tbody>
	  					  </table>
						 <div class="layer-foot text-c">
						    	  <button type="button" class="btn btn-primary btn-sm"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
		<script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Edit.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>	
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
		Edit.remindId='${param.remindId}';
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.config.showLinkImg = false;
		weUpload(editor,{uploadImgMaxLength:20});
		editor.config.onchange=function (html) {
		     $("#wordText").val(html)
		} 
		editor.config.emotions = [
             {
                 title: '默认',
                 type: 'image',
                 content:yqEmotions
             },
             {
                 title: 'emoji',
                 type: 'emoji',
                 content: ['😀', '😃', '😄', '😁', '😆','🤐','😬','🙄','😓','😂']
             }
       ];
		editor.create();
		
		$(function(){
			$("#editForm").render({success:function(result){
				 editor.txt.html($("#wordText").val());
				 var height=$(window).height();
				 $(".w-e-text-container").css("height",height-400);
				 
				 $('#coverUrlHref').attr('href',$("[name='remind.COVER_URL']").val());
			}});
			
			layui.use('upload', function(){
			 	  var upload = layui.upload;
				  var uploadInst = upload.render({
				    elem: '.upload-btn'
				    ,url: '/yq-work/servlet/upload?action=index'
				    ,accept: 'file'
				    ,exts:'xls|xlsx|txt|jpg|zip|ppt|pptx|doc|docx|tar|png|pdf|csv|rar|tar|7z|mp4|mp3|eml'
				    ,size:1024*200
					,multiple:false
					,number:20
				    ,field: 'remindFile'
				    ,data: {
						fkId:function(){
							var fkId='';
							if(Edit.remindId){
								fkId=Edit.remindId;
							}else{
								fkId=$("#randomId").val();
							}
							return fkId;
						},
						source:'remind'
					}
				    ,done: function(res, index, upload){
						var item = this.item;
					    layer.closeAll('loading');
					    if(res&&res.state==1){
					    	layer.msg(res.msg,{icon: 1,time:800},function(){
					    		$('#coverUrl').val(res.data.url);
					    		$('#coverUrlHref').attr('href',res.data.url);
							}); 
						}else{
							layer.alert(res.msg,{icon: 5});
						}
				    },before:function(){
						layer.load();
				    },allDone: function(obj){ 
					
					}
				    ,error: function(res, index, upload){
						layer.closeAll('loading');
				    	layer.alert("上传文件请求异常！",{icon: 5});
				    }
		      });
	  	  });
		});
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				var categoryName = $("#remindType").find("option:selected").text();;
				$("#categoryName").val(categoryName);
				
				if(Edit.remindId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			$("#remindId").val($("#randomId").val());
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/remind?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/remind?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.uploadFile = function(callback){
			var fkId='';
			if(Edit.remindId){
				fkId=Edit.remindId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'callback',fileMaxSize:(1024*10),fkId:fkId,source:'remind'});
		}
		 var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
		}
		

		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>