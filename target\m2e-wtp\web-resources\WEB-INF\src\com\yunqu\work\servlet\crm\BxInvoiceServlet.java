package com.yunqu.work.servlet.crm;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.math.NumberUtils;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.PathKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.jfinal.template.Engine;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.FileModel;
import com.yunqu.work.utils.BdOcrUtiles;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.XmlToJsonUtils;
import com.yunqu.work.utils.YqFileUtils;

@WebServlet("/servlet/bx/invoice/*")
@MultipartConfig(maxFileSize=20*1024*1024)
public class BxInvoiceServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public void actionForPdfMerge() {
		 try {
			 HttpServletResponse response = this.getResponse();
			 // 创建PDF合并工具
			 PDFMergerUtility merger = new PDFMergerUtility();
			 
			 String invoiceIds = getPara("invoiceIds");
			 EasySQL sql = new EasySQL("select t2.disk_path,t2.file_name from yq_finance_bx_invoice t1,yq_files t2 where t1.file_id = t2.file_id");
			 sql.appendIn(invoiceIds.split(","),"and t1.invoice_id");
				
			 List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
		     for(JSONObject row:list) {
				String diskPath = row.getString("DISK_PATH");
				String fileName = row.getString("FILE_NAME");
				String filePath = getBaseDir()+File.separator+diskPath;
				
				if(!new File(filePath).exists()) {
					continue;
				}
				if(fileName.indexOf(".pdf")==-1) {
					continue;
				}
				merger.addSource(filePath);
			 }

            // 设置合并后的输出流
            response.setContentType("application/pdf");
            OutputStream outputStream = response.getOutputStream();

            // 合并PDF并将结果写入输出流
            merger.setDestinationStream(outputStream);
            merger.mergeDocuments();
            
            // 关闭输出流
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            this.error(e.getMessage(), e);
        }
    }
	
	public EasyResult actionForIndex(){
		String userId = getUserId();
		synchronized (userId.intern()){
			JSONObject params = getJSONObject();
			String invoiceCategory = params.getString("invoiceCategory");
			String invoiceId = params.getString("invoiceId");
			String path = params.getString("path");
			String fileId = params.getString("id");
			String fsType = params.getString("fsType");
			String fileName = params.getString("name");
			
			EasyRecord model = new EasyRecord("yq_finance_bx_invoice","invoice_id");
			model.setPrimaryValues(invoiceId);
			model.set("file_id", fileId);
			model.set("file_name", fileName);
			model.set("creator", getUserId());
			model.set("create_name", getUserName());
			model.set("create_time", EasyDate.getCurrentDateString());
			
			if(".xml".equals(fsType)) {
				try {
					this.parseXml(path, model);
					String invoiceNo = model.getString("INVOICE_NO");
					if(invoiceNo==null) {
						return EasyResult.fail("文件格式内容不正确");
					}
					JSONObject row = this.getQuery().queryForRow("select * from yq_finance_bx_invoice where invoice_no = ?", new Object[] {invoiceNo},new JSONMapperImpl());
					if(row!=null) {
						return EasyResult.fail(String.format("发票号%s已存在%s",row.getString("INVOICE_NO"),row.getString("CREATE_NAME")));
					}
					
					try {
						this.getQuery().save(model);
						this.getQuery().executeUpdate("update yq_finance_bx_invoice t1,yq_finance_xml_invoice t2 set t1.xml_file_id = t2.xml_file_id,t1.xml_file_name = t2.xml_file_name where t1.invoice_no = t2.invoice_no and t1.invoice_no = ?",invoiceNo);
					} catch (SQLException e) {
						this.error(e.getMessage(), e);
					}
					return EasyResult.ok(params);
				} catch (Exception e) {
					this.error(e.getMessage(), e);
					return EasyResult.fail(e.getMessage());
				}
			}else{
				String ocrReqId = invoiceId;
				EasyResult reqResult =  BdOcrUtiles.getService().ocrApi(getStaffInfo(),invoiceCategory,ocrReqId,fileName,path,fsType);
				if(!reqResult.isOk()) {
					return reqResult;
				}
				String result = reqResult.getData().toString();
				
				LogEngine.getLogger("bx-invoice").info("userName:"+getUserName()+"fileId:"+fileId+">fileName:"+fileName+">invoiceId:"+invoiceId+">>>>>>>>>>"+result);
				JSONObject row = JSONObject.parseObject(result);
				JSONObject wordsResult = null;
				Object wordsResultObj = row.get("words_result");
				if(wordsResultObj instanceof JSONArray) {
					wordsResult = row.getJSONArray("words_result").getJSONObject(0);
				}else {
					wordsResult = row.getJSONObject("words_result");
				}
				if(wordsResult==null) {
					return EasyResult.fail("识别失败");
				}
				
				String invoiceNo = wordsResult.getString("InvoiceNumConfirm");
				if (StringUtils.isBlank(invoiceNo) || invoiceNo.length() < 8) {
				    invoiceNo = wordsResult.getString("CheckCode");
				}

				if(StringUtils.isBlank(invoiceNo)) {
					invoiceNo = wordsResult.getString("invoice_num");
				}
				if(StringUtils.isBlank(invoiceNo)) {
					return EasyResult.fail("未识别到发票，请联系管理员");
				}
				try {
					JSONObject info = this.getQuery().queryForRow("select * from yq_finance_bx_invoice where invoice_no = ?", new Object[] {invoiceNo},new JSONMapperImpl());
					if(info!=null) {
						return EasyResult.fail(String.format("发票号%s已存在%s",info.getString("INVOICE_NO"),info.getString("CREATE_NAME")));
					}
					
				} catch (SQLException ex) {
					this.error(ex.getMessage(), ex);
				}
				model.set("ocr_req_id",ocrReqId);
				model.set("invoice_no",invoiceNo);
				
				//发票类型
				String invoiceType = wordsResult.getString("InvoiceType");
				String invoiceTypeOrg = wordsResult.getString("InvoiceTypeOrg");
				model.set("invoice_type_org", invoiceTypeOrg);
				model.set("invoice_type", invoiceType);
				model.set("invoice_label_name", invoiceType);
				model.set("invoice_type_code", getPdfInvoiceTypeCode(invoiceNo,invoiceType,invoiceTypeOrg));
				
				
				//税额
				String totalTax = wordsResult.getString("TotalTax");
				if(StringUtils.isBlank(totalTax)) {
					totalTax = "0";
				}
				if(StringUtils.notBlank(totalTax)&&totalTax.indexOf("*")>-1) {
					model.set("total_tax", 0);
				}else if("免税".equals(totalTax)){
					model.set("total_tax", 0);
				}else{
					model.set("total_tax", totalTax.replace("¥",""));
				}
				
				String totalAmount =  wordsResult.getString("AmountInFiguers");
				String amountInFiguers =  wordsResult.getString("TotalAmount");
				if(StringUtils.isBlank(amountInFiguers)) {
					amountInFiguers = totalAmount;
				}
				if(StringUtils.isBlank(amountInFiguers)) {
					amountInFiguers = extractAmountAsDouble(wordsResult.getString("ticket_rates"));
				}
				
				if(StringUtils.isBlank(totalAmount)) {
					totalAmount = "0";
				}
				//价税合计
				model.set("total_amount",totalAmount);
				model.set("bx_amount",totalAmount);
				model.set("hz_amount",totalAmount);
				
				//去税金额
				model.set("amount_in_figuers", amountInFiguers);
				
				try {
					float a1 = Float.valueOf(totalAmount);
					float a2 = Float.valueOf(amountInFiguers);
					if(a2>a1) {
						model.set("total_amount",amountInFiguers);
						model.set("amount_in_figuers", amountInFiguers);
					}
					if(a1==0) {
						model.set("total_amount",model.getFloatValue("TOTAL_TAX")+model.getFloatValue("AMOUNT_IN_FIGUERS"));
					}
				}catch (Exception e) {
					this.error(e.getMessage(), e);
				}
				
				boolean isTrain = StringUtils.notBlank(wordsResult.getString("starting_station"));
				boolean isAir = StringUtils.notBlank(invoiceTypeOrg)&&invoiceTypeOrg.contains("航空运输电子客票行程单");
				//税率
				if(!isTrain) {
					JSONArray commodityTaxRate = wordsResult.getJSONArray("CommodityTaxRate");
					if(commodityTaxRate!=null&&!commodityTaxRate.isEmpty()) {
						Object taxRate = JSONPath.eval(wordsResult, "CommodityTaxRate[0].word");
						if(taxRate!=null&&!isValidTaxRate(taxRate.toString())) {
							model.set("tax_rate",0);
						}else {
							model.set("tax_rate",taxRate);
						}
					}else {
						model.set("tax_rate",0);
					}
				}
				
				
				model.set("tax_rate_num",getTaxRateNum(model.getString("TAX_RATE")));
				
				//价税合计大写
				model.set("amount_in_words", wordsResult.getString("AmountInWords"));
				
				
				JSONArray commodityNameArray = wordsResult.getJSONArray("CommodityName");
				if(commodityNameArray!=null&&!commodityNameArray.isEmpty()) {
					model.set("commodity_name", JSONPath.eval(wordsResult, "CommodityName[0].word"));
				}
				
				model.set("province", wordsResult.getString("Province"));
				String invoiceDate = DateUtils.convertErrorDate(wordsResult.getString("InvoiceDate"));
				model.set("invoice_date", invoiceDate);

				model.set("invoice_code", wordsResult.getString("InvoiceCode"));
				
				if(StringUtils.notBlank(invoiceDate)) {
					String _invoiceDate = DateUtils.convertDate(invoiceDate);
					if(_invoiceDate!=null) {
						model.set("kp_month", _invoiceDate.substring(0, 6));
						model.set("kp_date_id", _invoiceDate);
					}
				}
				
				model.set("sale_company", removeStr(wordsResult.getString("SellerName")));
				model.set("sale_company_no", wordsResult.getString("SellerRegisterNum"));
				model.set("buy_company", removeStr(wordsResult.getString("PurchaserName")));
				model.set("buy_company_no", wordsResult.getString("PurchaserRegisterNum"));
				model.set("service_type", wordsResult.getString("ServiceType"));
				model.set("kp_remark", wordsResult.getString("Remarks"));
				if(isTrain) {
					model.set("tax_rate","9%");
					double _amountInFiguers = model.getDoubleValue("AMOUNT_IN_FIGUERS");
					model.set("total_tax", calculateTax(_amountInFiguers,9));
					model.set("amount_in_figuers",_amountInFiguers - calculateTax(_amountInFiguers,9));
				}
				if(isAir) {
					model.set("tax_rate","9%");
					double _amountInFiguers = model.getDoubleValue("AMOUNT_IN_FIGUERS");
					model.set("total_tax", calculateTax(_amountInFiguers,9));
					model.set("amount_in_figuers",_amountInFiguers - calculateTax(_amountInFiguers,9));
					model.set("kp_remark", wordsResult.getString("AmountInWords"));
					model.set("invoice_type", "电子发票(航空运输电子客票行程单)");
					model.set("sale_company", "航空公司");
					model.set("buy_company", "广州云趣信息科技有限公司");
					model.set("invoice_type_code", "3002");
				}
				if(isTrain) {
					model.set("invoice_date",  wordsResult.getString("invoice_date"));
					model.set("buy_company", removeStr(wordsResult.getString("name")));
					model.set("sale_company", "铁路12306科创中心");
					model.set("INVOICE_TYPE", "电子发票(铁路电子客票)");
					model.set("invoice_type_org", "电子发票(铁路电子客票)");
					model.set("commodity_name", wordsResult.getString("seat_category"));
					model.set("kp_remark", wordsResult.getString("date")+"从"+wordsResult.getString("starting_station")+"到"+wordsResult.getString("destination_station"));
					model.set("invoice_type_code", "3001");
				}
				
				try {
					this.getQuery().save(model);
					
					int hasXml = this.getQuery().executeUpdate("update yq_finance_bx_invoice t1,yq_finance_xml_invoice t2 set t1.xml_file_id = t2.xml_file_id,t1.xml_file_name = t2.xml_file_name where t1.invoice_no = t2.invoice_no and t1.invoice_no = ?",invoiceNo);
					if(hasXml>0) {
						this.updatePdfInvoice(invoiceNo);
					}
					return EasyResult.ok(params);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
					return EasyResult.fail(e.getMessage());
				}
			}
		}
	}
	
	private void updatePdfInvoice(String invoiceNo) {
		 try {
			this.getQuery().executeUpdate("update yq_finance_bx_invoice t1,yq_finance_xml_invoice t2 set t1.total_amount = t2.total_amount,t1.amount_in_figuers = t2.amount_in_figuers,t1.total_tax = t2.total_tax,t1.tax_rate = t2.tax_rate,t1.tax_rate_num = t2.tax_rate_num,t1.kp_remark = t2.kp_remark,t1.sale_company= t2.sale_company,t1.buy_company = t2.buy_company,t1.invoice_date = t2.invoice_date,t1.buy_company_no = t2.buy_company_no where t1.invoice_no = t2.invoice_no and t1.invoice_no = ?",invoiceNo);
		 } catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
	}
	
	private static String extractAmountAsDouble(String input) {
		if(StringUtils.isBlank(input)) {
			return "0";
		}
	    String amountStr = input.replace("￥", "").replace("元", "");
	    return amountStr;
	}
	
	 public static double calculateTax(double amount, int taxRate) {
        // 将税率转换为小数形式 (例如: 10 -> 0.10)
        double rate = taxRate / 100.0;
        // 计算税额并保留两位小数
        return Math.round(amount * rate * 100) / 100.0;
    }
	
	private String removeStr(String str) {
		if(StringUtils.isBlank(str)) {
			str = "";
		}
		str = str.replaceAll("名称:", "");
		str = str.replaceAll("称:", "");
		str = str.replaceAll(":", "");
		return str;
	}
	
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_finance_bx_invoice","invoice_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	
	public EasyResult actionForCheckFileName(){
		JSONObject params = getJSONObject();
		String uploadFileName = params.getString("name");
		try {
			boolean result = this.getQuery().queryForExist("select count(1) from yq_files where file_name = ?", uploadFileName);
			if(result) {
				return EasyResult.fail(uploadFileName+"已上传过，请勿重复上传。");
			}
		} catch (SQLException ex) {
			this.error(ex.getMessage(), ex);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForImportXml(){
		JSONObject params = getJSONObject();
		String path = params.getString("path");
		String fsType = params.getString("fsType");
		String uploadFileId = params.getString("id");
		String source = "xmlInvoice";
		
		int xmlFileCount  = 0;
		int xmlRepeatFileCount  = 0;
		int xmlFileSuccCount  = 0;
		
		
		List<FileModel> list = new ArrayList<FileModel>();
		if(".zip".equals(fsType)) {
			try {
				ZipInputStream zipIn = new ZipInputStream(new FileInputStream(path));
				ZipEntry entry = null;
				while ((entry = zipIn.getNextEntry()) != null) {
					this.info(entry.getName(), null);
					if (!entry.isDirectory()) {
						String filename = entry.getName();
						String fileId = RandomKit.uuid();
						String fileDir = File.separator+EasyCalendar.newInstance().getFullMonth()+File.separator;
						String houzui = FileKit.getHouzui(filename);
						File file = new File(getBaseDir()+source+fileDir);  
						if (!file.exists()) {  
							file.mkdirs();
						} 
						
						if(!".xml".equals(houzui)) {
							continue;
						}
						xmlFileCount = xmlFileCount +1;
						
						String invoiceNo = getInvoiceNo(filename);
						try {
						   boolean result = this.getQuery().queryForExist("select count(*) from yq_finance_xml_invoice where invoice_no = ?", invoiceNo);
						   if(result) {
							   xmlRepeatFileCount = xmlRepeatFileCount +1;
							   continue;
						   }
						} catch (SQLException e) {
							this.error(e.getMessage(), e);
						}
						
						String toPathDir = getBaseDir()+source+File.separator+"document"+fileDir;
						String toPath = toPathDir +File.separator + filename;
						if(!new File(toPathDir).exists()) {
							new File(toPathDir).mkdirs();
						}
						if(new File(toPath).exists()) {
							toPath = toPathDir +File.separator + RandomKit.orderId()+filename;
						}
						
						
						FileOutputStream fos = new FileOutputStream(toPath);
						byte[] buffer = new byte[1024];
						int len;
						while ((len = zipIn.read(buffer)) > 0) {
							fos.write(buffer, 0, len);
						}
						fos.close();
						
						//写入本地文件
						String newFileName = getNewFileName(fileId,filename);
						String urlPath="/yq-work/files?url=/"+source+fileDir+newFileName;
						File newFile = new File(file.getAbsoluteFile() +File.separator + newFileName);
						FileKit.copyFile(toPath,newFile.getAbsolutePath());
						this.info("toPath>"+toPath,null);
						
						//保存到数据库
						FileModel fileModel = new FileModel();
						fileModel.setFileId(fileId); 
						fileModel.setAccessUrl(urlPath);
						fileModel.setFileName(filename);
						fileModel.setDiskPath("/"+source+fileDir+newFileName);
						fileModel.setFileType(houzui);
						fileModel.setFileLenth(newFile.length());
						fileModel.setFileSize(YqFileUtils.getPrintSize(newFile.length()));
						
						list.add(fileModel);
						
				}
					zipIn.closeEntry();
				}
				zipIn.close();
			} catch (IOException e) {
				this.error(e.getMessage(), e);
			}
		}else if(".xml".equals(fsType)){
			File newFile = new File(params.getString("path"));
			FileModel fileModel = new FileModel();
			fileModel.setFileId(RandomKit.uuid());
			fileModel.setFkId(params.getString("id"));
			fileModel.setFileName(params.getString("name"));
			fileModel.setDiskPath(params.getString("relativePath"));
			fileModel.setAccessUrl(params.getString("url"));
			String houzui = FileKit.getHouzui(fileModel.getFileName());
			fileModel.setFileType(houzui);
			fileModel.setFileLenth(newFile.length());
			fileModel.setFileSize(YqFileUtils.getPrintSize(newFile.length()));
			list.add(fileModel);
		}
		for(FileModel model:list) {
			model.setIp(WebKit.getIP(getRequest()));
			model.addCreateTime();
			model.setSource(source);
			model.set("CREATE_NAME",getUserName());
			model.setCreator(getRemoteUser());
			model.setFkId(uploadFileId);
			try {
				this.getQuery().save(model);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
			
			String invoiceNo  = getInvoiceNo(model.getFileName());
			if(invoiceNo!=null) {
				try {
					this.getQuery().executeUpdate("update yq_finance_bx_invoice set xml_file_id = ?,xml_file_name = ?,xml_upload_time = ? where invoice_no = ?", model.getFileId(),model.getFileName(),EasyDate.getCurrentDateString(),invoiceNo);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
			
			try {
				EasyRecord bxXmlInvoice = new EasyRecord("yq_finance_xml_invoice","invoice_no");
				bxXmlInvoice.set("invoice_no", invoiceNo);
				bxXmlInvoice.set("xml_file_id", model.getFileId());
				bxXmlInvoice.set("xml_file_name", model.getFileName());
				bxXmlInvoice.set("upload_file_id", uploadFileId);
				bxXmlInvoice.set("xml_upload_time", EasyDate.getCurrentDateString());
				bxXmlInvoice.set("create_time", EasyDate.getCurrentDateString());
				bxXmlInvoice.set("creator", getUserId());
				bxXmlInvoice.set("create_name", getUserName());
				this.parseXml(model.getDiskPath(), bxXmlInvoice);
				
				String _invoiceNo = bxXmlInvoice.getString("INVOICE_NO");
				
				if(StringUtils.isBlank(_invoiceNo)) {
					this.error(model.getDiskPath()+"不是财务发票xml，解析错误。", null);
					this.getQuery().deleteById(model);
					continue;
				}
				
				if(invoiceNo==null&&StringUtils.notBlank(_invoiceNo)) {
					try {
						this.getQuery().executeUpdate("update yq_finance_bx_invoice set xml_file_id = ?,xml_file_name = ?,xml_upload_time = ? where invoice_no = ?", model.getFileId(),model.getFileName(),EasyDate.getCurrentDateString(),_invoiceNo);
					} catch (SQLException e) {
						this.error(e.getMessage(), e);
					}
				}
				
				this.getQuery().save(bxXmlInvoice);
				xmlFileSuccCount = xmlFileSuccCount +1;
				
				this.updatePdfInvoice(_invoiceNo);
				
			} catch (Exception e) {
				this.error(e.getMessage(), e);
				if(e.getMessage().contains("Duplicate")) {
					xmlRepeatFileCount = xmlRepeatFileCount +1;
				}
			}
		}
		try {
			this.getQuery().executeUpdate("update yq_finance_xml_invoice t1,yq_finance_bx_invoice t2 set t1.bx_invoice_id = t2.invoice_id,t1.belong_user_id = t2.creator,t1.belong_user_name = t2.create_name where t1.upload_file_id = ? and t1.invoice_no = t2.invoice_no", uploadFileId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}	
		return EasyResult.ok(xmlFileSuccCount,String.format("读取到文件数：%s,重复导入%s,导入成功%s", xmlFileCount,xmlRepeatFileCount,xmlFileSuccCount));
	}
	
	
	private String getInvoiceNo(String fileName) {
		String[] array = fileName.split("_");
		if(array.length==4||array.length==3) {
			String invoiceNo = array[1];
			return invoiceNo;
		}
		return null;
	}
	
	private EasyRecord parseXml(String path,EasyRecord model) throws Exception {
		String str = FileKit.readToString(getBaseDir()+File.separator+path);
		JSONObject json = XmlToJsonUtils.xmltoJson(str);
//		LogEngine.getLogger("bx-invoice").info(">>>json.toJSONString>>>>>>>"+json.toJSONString());
		
		JSONObject eInvoice = json.getJSONObject("EInvoice");
		JSONObject eInvoiceData = eInvoice.getJSONObject("EInvoiceData");
		JSONObject basicInformation = eInvoiceData.getJSONObject("BasicInformation");
		
		Object issuItemInformationObj = eInvoiceData.get("IssuItemInformation");
		JSONObject issuItemInformation = null;
		if(issuItemInformationObj instanceof JSONArray) {
			issuItemInformation = eInvoiceData.getJSONArray("IssuItemInformation").getJSONObject(0);
		}else {
			issuItemInformation = eInvoiceData.getJSONObject("IssuItemInformation");
		}
		
//		LogEngine.getLogger("bx-invoice").info(">>>>>>>>>>"+str);
		
		Object invoiceNo = JSONPath.eval(eInvoice, "TaxSupervisionInfo.InvoiceNumber");
		if(invoiceNo==null) {
			this.warn(str, null);
		}
		
		model.set("invoice_no", invoiceNo);
		model.set("invoice_date", JSONPath.eval(eInvoice, "TaxSupervisionInfo.IssueTime"));
		model.set("province", JSONPath.eval(eInvoice, "TaxSupervisionInfo.TaxBureauName"));
		
		model.set("amount_in_figuers", basicInformation.getString("TotalAmWithoutTax"));
		model.set("total_amount", basicInformation.getString("TotalTax-includedAmount"));
		model.set("total_tax", basicInformation.getString("TotalTaxAm"));
		model.set("tax_rate", issuItemInformation.getString("TaxRate"));
		model.set("tax_rate_num", getTaxRateNum(issuItemInformation.getString("TaxRate")));
		
		model.set("amount_in_words", basicInformation.getString("TotalTax-includedAmountInChinese"));
		
		
		Object LabelName = JSONPath.eval(eInvoice, "Header.InherentLabel.GeneralOrSpecialVAT.LabelName");
		model.set("invoice_type", LabelName);
		if("普通发票".equals(LabelName)) {
			model.set("invoice_type_code", "1001");
		}else if("增值税专用发票".equals(LabelName)) {
			model.set("invoice_type_code", "2001");
		}else if("航空运输客票电子行程单".equals(LabelName)) {
			model.set("invoice_type_code", "3002");
		}else if("铁路电子客票".equals(LabelName)) {
			model.set("invoice_type_code", "3001");
		}
		
		model.set("invoice_label_name", JSONPath.eval(eInvoice, "Header.InherentLabel.EInvoiceType.LabelName")+"("+JSONPath.eval(eInvoice, "Header.InherentLabel.GeneralOrSpecialVAT.LabelName")+")");
		
		model.set("commodity_name", issuItemInformation.getString("ItemName"));
		
		JSONObject additionalInformation = eInvoiceData.getJSONObject("AdditionalInformation");
		if(additionalInformation!=null) {
			model.set("kp_remark", additionalInformation.getString("Remark"));
		}
		
		model.set("sale_company", JSONPath.eval(eInvoiceData, "SellerInformation.SellerName"));
		model.set("sale_company_no", JSONPath.eval(eInvoiceData,"SellerInformation.SellerIdNum"));
		model.set("buy_company", JSONPath.eval(eInvoiceData, "BuyerInformation.BuyerName"));
		model.set("buy_company_no", JSONPath.eval(eInvoiceData, "BuyerInformation.BuyerIdNum"));
		return model;
	}
	
	
	private String getNewFileName(String id,String oldFileName) {
		String filename = id+FileKit.getHouzui(oldFileName);
		return filename;
	}
	
	public EasyResult actionForAdd(){
		EasyRecord model = getModel("invoice");
		String invoiceNo = model.getString("INVOICE_NO");
		try {
			boolean flag = this.getQuery().queryForExist("select count(1) from yq_finance_bx_invoice where invoice_no = ?",invoiceNo);
			if(flag) {
				return EasyResult.fail("发票号："+invoiceNo+"已存在");
			}
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		String invoiceType = model.getString("INVOICE_TYPE");
		model.set("INVOICE_TYPE_CODE", getPdfInvoiceTypeCode(invoiceNo,invoiceType,invoiceType));
		
		String creator = model.getString("CREATOR");
		if(StringUtils.isBlank(creator)) {
			model.set("CREATOR", getUserId());
			model.set("CREATE_NAME",getUserName());
		}
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("invoice");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDel(){
		try {
			String[] array = null;
			String invoiceIds = getJsonPara("invoiceIds");
			if(StringUtils.notBlank(invoiceIds)) {
				array = invoiceIds.split(",");
			}else {
				array = new String[]{getJsonPara("invoiceId")};
			}
			for(String str:array) {
				if(StringUtils.isNotBlank(str)) {
					this.getQuery().executeUpdate("delete from yq_finance_bx_invoice where invoice_id = ?", str);
					this.getQuery().executeUpdate("delete from yq_files where fk_id = ?",str);
				}
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelBxInvoice(){
		try {
			String invoiceId = getJsonPara("invoiceId");
			String businessId = getJsonPara("businessId");
			this.getQuery().executeUpdate("update yq_finance_bx_invoice set bx_state = ?,business_id = '',bx_item_id = '',use_time = '',use_by ='' where invoice_id = ? and business_id = ?",0,invoiceId,businessId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	/**
	 * 1001 数电普通2001 数电专票
	 * 1002 电子普通 2002 电子专票
	 * 3001 铁路数电 3002 航空数电
	 * @param name
	 * @return
	 */
	private String getPdfInvoiceTypeCode(String invoiceNo,String name,String invoiceTypeOrg) {
		if(StringUtils.notBlank(name)) {
			if(StringUtils.notBlank(invoiceTypeOrg)) {
				if(invoiceTypeOrg.contains("铁路电子客票")) {
					return "3001";
				}
				if(invoiceTypeOrg.contains("航空运输")) {
					return "3002";
				}
			}
			if(invoiceNo.length()==20) {
				if(name.indexOf("专用")>-1) {
					return "2001";
				}else {
					return "1001";
				}
			}else if(name.indexOf("通用机打")>-1) {
				return "1002";
			}else{
				if(name.indexOf("专用")>-1) {
					return "2002";
				}else {
					return "1002";
				}
			}
		}
		return "";
	}
	
	public static boolean isValidTaxRate(String rateStr) {
		rateStr = rateStr.replaceAll("%", "");
        // 使用正则表达式匹配税率
        if (rateStr.matches("\\d+(\\.\\d+)?")) { // 匹配数字，可能包含小数点
            try {
                double rate = Double.parseDouble(rateStr);
                // 检查是否是小数形式的税率
                if (rate >= 0.0 && rate < 1.0) {
                    return true;
                }
                // 检查是否是百分比形式的税率
                if (rate >= 0.0 && rate < 100.0) {
                    int percentageRate = (int) rate;
                    return rate == percentageRate; // 确保rate没有小数部分
                }
            } catch (NumberFormatException e) {
                // 捕获转换异常，直接返回false
                return false;
            }
        }
        return false;
    }
	
	private int getTaxRateNum(String taxRate) {
		if(StringUtils.notBlank(taxRate)) {
			taxRate = taxRate.replaceAll("%","");
			if(NumberUtils.isCreatable(taxRate)) {
				double val =  Double.parseDouble(taxRate);
				if(val<1) {
					val = val*100;
				}
				return (int)val;
			}
			return 0;
		}
		return 0;
	}
	
	public void actionForPrintBx() {
		String applyId = getPara("businessId");
	    try {
	    	String tplName = "bx-1.xml";
	    	
	    	String baseSql = "SELECT t1.apply_no,t1.apply_name,t1.dept_name,t1.apply_staff_no,t2.pay_money from yq_flow_apply t1,yq_flow_bx_base t2 where t1.apply_id = t2.business_id and t1.apply_id = ?";
	    	JSONObject record = this.getQuery().queryForRow(baseSql, new Object[] {applyId},new JSONMapperImpl());
	    	
	    	Map<String,String> data = new HashMap<String,String>();
	    	data.put("deptName", record.getString("DEPT_NAME"));
	    	data.put("userName", record.getString("APPLY_NAME"));
	    	data.put("staffNo", record.getString("APPLY_STAFF_NO"));
	    	data.put("money", record.getString("PAY_MONEY"));
	    	data.put("applyNo", record.getString("APPLY_NO"));
			
	    	String sql = "select fee_type,SUM(hz_amount) hz_amount from yq_flow_bx_item where business_id = ?  GROUP BY fee_type ORDER BY hz_amount desc limit 6";
	    	List<JSONObject> list = this.getQuery().queryForList(sql, new Object[]{applyId},new JSONMapperImpl());
			if(list!=null&&list.size()>0) {
				int i = 1;
				for(JSONObject row:list) {
					data.put("feeType"+i,row.getString("FEE_TYPE"));
					data.put("feeNum"+i,row.getString("HZ_AMOUNT"));
					i++;
				}
				for(int y=1;y<=6;y++) {
					if(StringUtils.isBlank(data.get("feeType"+y))) {
						data.put("feeType"+y,"");
						data.put("feeNum"+y,"");
					}
				}
				
			}else {
//				tplName = "bx-2.xml";
			}
			
//			String travelFeeSql = "select CONCAT(start_date,'-',end_date) date,CONCAT(start_city,'-',dest_city) city,cost_day day from yq_flow_bx_item where business_id = ? and fee_type like '差旅费%'   ORDER BY hz_amount desc limit 6";
//	    	List<JSONObject> travelFeeList = this.getQuery().queryForList(travelFeeSql, new Object[]{applyId},new JSONMapperImpl());
//	    	if(travelFeeList!=null&&travelFeeList.size()>0) {
//	    		for(JSONObject row:travelFeeList) {
//	    			
//	    		}
//	    		if(list!=null&&list.size()>0) {
//	    			tplName = "bx-3.xml";
//	    		}
//	    	}
			File file = FileKit.createTempFile(RandomKit.uuid()+".docx");
			Engine.use().setBaseTemplatePath(PathKit.getWebRootPath()+"/tpl/bx").getTemplate(tplName).render(data,file.getAbsolutePath());
			renderFile(file, record.getString("APPLY_NO")+"_单据粘贴底单.docx", true);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			renderHtml(e.getMessage());
		}
	}
	
}
