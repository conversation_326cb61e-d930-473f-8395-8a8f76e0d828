<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>project</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="riskDetailForm" class="form-horizontal" data-text-model="true" data-mars="ProjectRiskDao.record" autocomplete="off" data-mars-prefix="projectRisk.">
        	<input type="hidden" value="${param.riskId}" name="projectRisk.RISK_ID"/>
        	<input type="hidden" name="projectRisk.PROJECT_ID"/>
            <table class="table table-vzebra">
              <tbody>
              	<c:if test="${param.isAdmin=='1'}">
                  <tr>
                	<td>操作</td>
                	<td>
                		<button type="button" class="btn btn-primary btn-xs" onclick="RiskDetail.editRisk()"> 编 辑 </button>
			            <button type="button" class="btn btn-info btn-xs ml-10" onclick="RiskDetail.addRiskFollow()"> + 跟 进 </button>
			            <c:if test="${param.riskState=='0'}">
				            <button type="button" class="btn btn-primary layui-hide btn-xs ml-10" onclick="RiskDetail.endRisk()"> 解除风险 </button>
			            </c:if>
                	</td>
                  </tr>
              	</c:if>
                <tr>
                    <td width="120px">风险分类</td>
                    <td>
                        <select name="projectRisk.RISK_BELONG">
                            <option value="">--</option>
                            <option value="我方">我方</option>
                            <option value="客户">客户</option>
                            <option value="三方(外购)">三方（外购）</option>
                            <option value="三方(外包)">三方（外包）</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>风险说明</td>
                    <td>
                        <textarea name="projectRisk.RISK_DESC"></textarea>
                    </td>
                </tr>
                <tr>
                    <td >风险应对措施</td>
                    <td>
                        <textarea  name="projectRisk.SOLUTION"></textarea>
                    </td>
                </tr>
                <tr>
                    <td>风险等级</td>
                    <td>
                         <select name="projectRisk.RISK_LEVEL">
						    <option value="0" selected>警告</option>
						    <option value="1">严重</option>
						    <option value="2">灾难</option>
						 </select>
                    </td>
                </tr>
                <tr>
                    <td>风险状态</td>
                    <td>
                       <select name="projectRisk.RISK_STATE">
						    <option value="0" selected>进行中</option>
						    <option value="1">已解除</option>
						</select>
                    </td>
                </tr>
                <tr>
                    <td >风险触发时间</td>
                    <td>
                        <input type="text"  name="projectRisk.TRIGGER_TIME">
                    </td>
                </tr>
                <tr>
                    <td >负责处理人</td>
                    <td>
                        <input name="projectRisk.OWNER_NAME">
                    </td>
                </tr>
                <tr>
                    <td >风险录入时间</td>
                    <td>
                        <input type="text" name="projectRisk.CREATE_TIME">
                    </td>
                </tr>
                <tr>
                    <td >提出人</td>
                    <td>
                        <input name="projectRisk.CREATOR_NAME" readonly="readonly">
                    </td>
                </tr>
                <tr>
                    <td >最后跟进时间</td>
                    <td>
                        <input type="text" name="projectRisk.LAST_FOLLOW_TIME">
                    </td>
                </tr>
                <tr>
                    <td>跟进内容</td>
                    <td>
                        <textarea data-fn="getContent" name="projectRisk.FOLLOW_CONTENT"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>
    </form>
</EasyTag:override>
<EasyTag:override name="script">

    <script type="text/javascript">
    
    	var RiskDetail = {
   			riskId:'${param.riskId}'
    	};

        $(function(){
            $("#riskDetailForm").render({success:function(result){
            
            }});
        });

        RiskDetail.editRisk = function(){
        	layer.closeAll();
        	var projectId = $("input[name='projectRisk.PROJECT_ID']").val();
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新风险信息',offset:'r',area:['50%','100%'],url:'${ctxPath}/pages/project/risk/risk-edit.jsp',title:'编辑风险信息',data:{riskId:RiskDetail.riskId,projectId:projectId,isNew:'0'}});
        }

        RiskDetail.endRisk = function (){
            layer.confirm("确认将风险更改为已完成吗?",{icon:3,offset:'20px'},function(){
                ajax.remoteCall("${ctxPath}/servlet/projectRisk?action=endRisk",{'riskId':RiskDetail.riskId},function(result) {
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            reloadRisk();
                            layer.closeAll();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            });
        }

        RiskDetail.addRiskFollow = function (){
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增跟进信息',offset:'20px',area:['450px','300px'],url:'${ctxPath}/pages/project/risk/risk-follow-edit.jsp',title:'新增跟进信息',data:{riskId:RiskDetail.riskId}});
        }


        function delRisk(riskId){
            layer.confirm("确认要删除吗?",{icon:3,offset:'20px'},function(){
                ajax.remoteCall("${ctxPath}/servlet/projectRisk?action=delData",{'riskId':riskId},function(result) {
                    if(result.state == 1){
                        layer.msg(result.msg,{icon:1,time:1200},function(){
                            reloadRisk();
                            layer.closeAll();
                        });
                    }else{
                        layer.alert(result.msg,{icon: 5});
                    }
                });
            });
        }

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>