<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>客户管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
		.layui-tab-item{padding: 0px;}
	  	.editFn{opacity: 0;color: #03a9f4;}
	  	.el-timeline-item:hover .editFn{opacity: 1;text-decoration: underline;}
		.el-timeline{margin:0;font-size:14px;list-style:none}.el-timeline .el-timeline-item:last-child .el-timeline-item__tail{display:none}.el-timeline-item{position:relative;padding-bottom:20px}.el-timeline-item__wrapper{position:relative;padding-left:28px;top:-3px}.el-timeline-item__tail{position:absolute;left:4px;height:100%;border-left:2px solid #e4e7ed}.el-timeline-item__icon{color:#fff;font-size:13px}.el-timeline-item__node{position:absolute;background-color:#e4e7ed;border-radius:50%;display:flex;justify-content:center;align-items:center}.el-timeline-item__node--normal{left:-1px;width:12px;height:12px}.el-timeline-item__node--large{left:-2px;width:14px;height:14px}.el-timeline-item__node--primary{background-color:#409eff}.el-timeline-item__node--success{background-color:#67c23a}.el-timeline-item__node--warning{background-color:#e6a23c}.el-timeline-item__node--danger{background-color:#f56c6c}.el-timeline-item__node--info{background-color:#909399}.el-timeline-item__dot{position:absolute;display:flex;justify-content:center;align-items:center}.el-timeline-item__content{color:#303133}.el-timeline-item__timestamp{color:#909399;line-height:1;font-size:13px}.el-timeline-item__timestamp.is-top{margin-bottom:8px;padding-top:4px}.el-timeline-item__timestamp.is-bottom{margin-top:8px}.el-card{border-radius:4px;border:1px solid #ebeef5;background-color:#fff;overflow:hidden;color:#303133;transition:.3s}.el-card.is-always-shadow,.el-card.is-hover-shadow:focus,.el-card.is-hover-shadow:hover{box-shadow:0 2px 12px 0 rgba(0,0,0,.1)}.el-card__header{padding:18px 20px;border-bottom:1px solid #ebeef5;box-sizing:border-box}.el-card__body{padding:20px}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="CustFollowForm">
			<input name="custId" type="hidden" value="${param.custId}">
			<input name="contactsId" type="hidden" value="${param.contactsId}">
			<input name="businessId" type="hidden" value="${param.businessId}">
			<div class="ibox">
				<div class="ibox-title clearfix mb-10">
	          		 <div class="form-group">
	          		     <h5>跟进周报统计</h5>
				    	<div class="input-group input-group-sm ml-15">
							 <span class="input-group-addon">跟进时间</span>	
							 <input type="text" name="beginDate" data-mars="CommonDao.prevMonthDay" data-mars-top="true" data-mars-reload="false" onclick="WdatePicker({})" class="form-control input-sm" style="width: 90px;">
							 <input type="text" name="endDate"  data-mars="WeeklyDao.getCurrentWeekEndDate" data-mars-top="true" data-mars-reload="false" onclick="WdatePicker({})" class="form-control input-sm" style="width: 90px;">
				    	</div>
						 <div class="input-group input-group-sm" style="width: 150px;">
					 		<span class="input-group-addon">跟进人</span>
						 	<input class="form-control input-sm" name="userName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 180px;">
					 		<span class="input-group-addon">客户名称</span>
						 	<input class="form-control input-sm" name="customerName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-info" onclick="CustContactsMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-default" onclick="$('.layui-card-body').hide();">折叠</button>
						 </div>
	          	     </div>
	              </div> 
					 <script id="follow-template" type="text/x-jsrender">
						<ul class="el-timeline">
						{{for data}}
							{{if #parent.parent.data.userId == CREATE_USER_ID && #parent.parent.data.groupType == 'user'}}
							 <li class="el-timeline-item">
								<div class="el-timeline-item__tail"></div>
								<div class="el-timeline-item__node el-timeline-item__node--normal el-timeline-item__node--primary">
								</div>
								<div class="el-timeline-item__wrapper">
									<div class="el-timeline-item__timestamp is-top">
									 {{:CREATE_TIME}}
									</div>
									<div class="el-timeline-item__content">
										<div class="el-card is-always-shadow">
											<div class="el-card__body">
												<p onclick="CustContactsMgr.custDetail('{{:CUST_ID}}');" style="font-size:13px;color:#4ba2ec;margin-top:-10px;cursor:pointer;">{{:CUSTOMER_NAME}} {{if BUSINESS_NAME}}<span class="ml-5 mr-5">/</span>{{:BUSINESS_NAME}}{{/if}}  <span class="label label-info">{{:FOLLOW_TYPE}}</span></p>
												<h4 style="font-size:14px;color:#444;margin-top:5px;line-height:26px;">{{call:CONTENT fn='getContent'}}</h4>
												{{if FILE_COUNT>0}}<p onclick="fileDetail('{{:FOLLOW_ID}}')" style="font-size:13px;color:#5190de;line-height:20px;cursor:pointer;" title="点击查看"> <i class="layui-icon layui-icon-file"></i> 附件({{:FILE_COUNT}}个) </p> {{/if}}
												<p style="font-size:12px;color:#999;margin-top:5px;">{{:CREATE_USER_NAME}} 提交于{{:CREATE_TIME}} ,&nbsp; 跟进时间 {{:FOLLOW_BEGIN}} ~ {{:FOLLOW_END}} , 工时: {{:FOLLOW_DAY}}
  													{{if FOLLOW_LEVEL}}<span class="pull-right">{{call:FOLLOW_LEVEL fn='followLevelLabel'}}</span>{{/if}}
													<a href="javascript:;" class="editFn" onclick="CustContactsMgr.edit('{{:FOLLOW_ID}}','{{:CUST_ID}}')">编辑</a></p>
											</div>
										</div>
									</div>
								</div>
							</li>
							{{/if}}
							{{if #parent.parent.data.custId == CUST_ID && #parent.parent.data.groupType == 'cust'}}
							 <li class="el-timeline-item">
								<div class="el-timeline-item__tail"></div>
								<div class="el-timeline-item__node el-timeline-item__node--normal el-timeline-item__node--primary">
								</div>
								<div class="el-timeline-item__wrapper">
									<div class="el-timeline-item__timestamp is-top">
									 {{:CREATE_TIME}}
									</div>
									<div class="el-timeline-item__content">
										<div class="el-card is-always-shadow">
											<div class="el-card__body">
												<p onclick="CustContactsMgr.custDetail('{{:CUST_ID}}');" style="font-size:13px;color:#4ba2ec;margin-top:-10px;cursor:pointer;">{{if BUSINESS_NAME}}{{:BUSINESS_NAME}}{{/if}}  <span class="label label-info">{{:FOLLOW_TYPE}}</span></p>
												<h4 style="font-size:14px;color:#444;margin-top:5px;line-height:26px;">{{call:CONTENT fn='getContent'}}</h4>
												{{if FILE_COUNT>0}}<p onclick="fileDetail('{{:FOLLOW_ID}}')" style="font-size:13px;color:#5190de;line-height:20px;cursor:pointer;" title="点击查看"> <i class="layui-icon layui-icon-file"></i> 附件({{:FILE_COUNT}}个) </p> {{/if}}
												<p style="font-size:12px;color:#999;margin-top:5px;">{{:CREATE_USER_NAME}} 提交于{{:CREATE_TIME}} ,&nbsp; 跟进时间 {{:FOLLOW_BEGIN}} ~ {{:FOLLOW_END}},工时: {{:FOLLOW_DAY}}
													{{if FOLLOW_LEVEL}}<span class="pull-right">{{call:FOLLOW_LEVEL fn='followLevelLabel'}}</span>{{/if}}
 													<a href="javascript:;" class="editFn" onclick="CustContactsMgr.edit('{{:FOLLOW_ID}}','{{:CUST_ID}}')">编辑</a></p>
											</div>
										</div>
									</div>
								</div>
							</li>
							{{/if}}
							{{if #parent.parent.data.sjId == BUSINESS_ID && #parent.parent.data.groupType == 'sj'}}
							 <li class="el-timeline-item">
								<div class="el-timeline-item__tail"></div>
								<div class="el-timeline-item__node el-timeline-item__node--normal el-timeline-item__node--primary">
								</div>
								<div class="el-timeline-item__wrapper">
									<div class="el-timeline-item__timestamp is-top">
									 {{:CREATE_TIME}}
									</div>
									<div class="el-timeline-item__content">
										<div class="el-card is-always-shadow">
											<div class="el-card__body">
												<p onclick="CustContactsMgr.custDetail('{{:CUST_ID}}');" style="font-size:13px;color:#4ba2ec;margin-top:-10px;cursor:pointer;">{{:CUSTOMER_NAME}} <span class="label label-info">{{:FOLLOW_TYPE}}</span></p>
												<h4 style="font-size:14px;color:#444;margin-top:5px;line-height:26px;">{{call:CONTENT fn='getContent'}}</h4>
												{{if FILE_COUNT>0}}<p onclick="fileDetail('{{:FOLLOW_ID}}')" style="font-size:13px;color:#5190de;line-height:20px;cursor:pointer;" title="点击查看"> <i class="layui-icon layui-icon-file"></i> 附件({{:FILE_COUNT}}个) </p> {{/if}}
												<p style="font-size:12px;color:#999;margin-top:5px;">{{:CREATE_USER_NAME}} 提交于{{:CREATE_TIME}} ,&nbsp; 跟进时间 {{:FOLLOW_BEGIN}} ~ {{:FOLLOW_END}}, 工时: {{:FOLLOW_DAY}}
													{{if FOLLOW_LEVEL}}<span class="pull-right">{{call:FOLLOW_LEVEL fn='followLevelLabel'}}</span>{{/if}}
 													<a href="javascript:;" class="editFn" onclick="CustContactsMgr.edit('{{:FOLLOW_ID}}','{{:CUST_ID}}')">编辑</a></p>
											</div>
										</div>
									</div>
								</div>
							</li>
							{{/if}}
						{{/for}}
						{{if data.length==0}}
							<li calss="text-c" style="height:200px;line-height:200px;text-align:center;font-size:18px;"><i class="fa fa-info-circle"></i>&nbsp;暂无数据</li>
						{{/if}}
						</ul>
					</script>
					 <script id="follow-user-template" type="text/x-jsrender">
						  {{for data}}
							  <div class="layui-col-md6">
					    	   <div class="layui-card">
						  		<div class="layui-card-header">{{:#index+1}}.{{:CREATE_USER_NAME}}({{:COUNT}}) <span class="label label-info label-outline ml-5">{{:FOLLOW_DAY}}天</span> <a class="pull-right btn btn-xs btn-link mt-10" href="javascript:;" >折叠</a></div>
						  		<div class="layui-card-body" data-container="container3{{:#index}}" id="container3{{:#index}}" data-group-type="user" data-user-id="{{:CREATE_USER_ID}}" data-mars="CustDao.userFollowList" data-template="follow-template">

						 		 </div>
								</div>
					          </div>
						    {{/for}}
						{{if data.length==0}}
							<div calss="text-c" style="height:200px;line-height:200px;text-align:center;font-size:18px;"><i class="fa fa-info-circle"></i>&nbsp;暂无数据</div>
						{{/if}}
					</script>
					 <script id="follow-cust-template" type="text/x-jsrender">
						  {{for data}}
							  <div class="layui-col-md6">
					    	   <div class="layui-card">
						  		<div class="layui-card-header">{{:#index+1}}.{{:CUST_NAME}}({{:COUNT}})   <span class="label label-info label-outline ml-5">{{:FOLLOW_DAY}}天</span> <a class="pull-right btn btn-xs btn-link mt-10" href="javascript:;">折叠</a></div>
						  		<div class="layui-card-body" data-container="container1{{:#index}}" id="container1{{:#index}}" data-group-type="cust" data-cust-id="{{:CUST_ID}}" data-mars="CustDao.userFollowList" data-template="follow-template">

						 		 </div>
								</div>
					          </div>
						    {{/for}}
						{{if data.length==0}}
							<div calss="text-c" style="height:200px;line-height:200px;text-align:center;font-size:18px;"><i class="fa fa-info-circle"></i>&nbsp;暂无数据</div>
						{{/if}}
					</script>
					 <script id="follow-shangji-template" type="text/x-jsrender">
						  {{for data}}
							  <div class="layui-col-md6">
					    	   <div class="layui-card">
						  		<div class="layui-card-header">{{:#index+1}}.{{cutText:BUSINESS_NAME 50}}({{:COUNT}})  <span class="label label-info label-outline ml-5">{{:FOLLOW_DAY}}天</span> <a class="pull-right btn btn-xs btn-link mt-10" href="javascript:;" >折叠</a></div>
						  		<div class="layui-card-body" data-container="container2{{:#index}}" id="container2{{:#index}}" data-group-type="sj" data-sj-id="{{:BUSINESS_ID}}" data-mars="CustDao.userFollowList" data-template="follow-template">

						 		 </div>
								</div>
					          </div>
						    {{/for}}
						{{if data.length==0}}
							<div calss="text-c" style="height:200px;line-height:200px;text-align:center;font-size:18px;"><i class="fa fa-info-circle"></i>&nbsp;暂无数据</div>
						{{/if}}
					</script>
	              <div class="layui-tab layui-tab-brief">
					  <ul class="layui-tab-title" style="background-color: #fff;">
					    <li class="layui-this">填报人分组</li>
					    <li>客户分组</li>
					    <li>商机分组</li>
					  </ul>
					  <div class="layui-tab-content" style="min-height: 100px;padding: 10px 0px;">
					    <div class="layui-tab-item layui-show">
					    	 <div class="layui-row layui-col-space10 mt-10" data-template="follow-user-template" data-container="uc" id="uc" data-mars="CustDao.folowByWeek" data-mars-reload="true">
					
				  			</div>
				  			 <div id="pageContainer" class="layui-row mt-10"></div>
					    </div>
					    <div class="layui-tab-item">
					    	 <div class="layui-row layui-col-space10 mt-10" data-template="follow-cust-template" data-container="cust" id="cust" data-mars="CustDao.folowCustByWeek" data-mars-reload="true">
					
				  			</div>
					    
					    </div>
					    <div class="layui-tab-item">
					    	 <div class="layui-row layui-col-space10 mt-10" data-template="follow-shangji-template" data-container="shangji" id="shangji" data-mars="CustDao.folowSjByWeek" data-mars-reload="true">
					
				  			</div>
					    
					    </div>
					  </div>
					</div>
      
      
      
	             
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var CustContactsMgr={
			query:function(){
				initPage();
			},
			edit:function(followId,custId){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['60%','80%'],url:'${ctxPath}/pages/crm/include/add-follow.jsp',title:'编辑',data:{followId:followId,custId:custId}});
			},
			add:function(){
				var custId = '';
				popup.layerShow({type:1,anim:0,scrollbar:false,maxmin:true,shadeClose:false,offset:'20px',area:['60%','80%'],url:'${ctxPath}/pages/crm/include/add-follow.jsp',title:'新增跟进记录',data:{custId:custId}});
			},
			custDetail:function(custId){
				var width = $(window).width();
				var w = '80%';
				if(width>1500){
					w = '800px';
				}else if(width<1000){
					w = '100%';
				}else{
					
				}
				popup.openTab({id:'custDetail',title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:[w,'100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId,isDiv:0}});
			}
		}
		function reloadFollowInfo(){
			$("[name='contactsId']").val('');
			CustContactsMgr.query();
		}
		$(function(){
			layui.use('element', function(){
				  var element = layui.element;
				  
			});
			requreLib.setplugs('wdate');
		    initPage();
			
		});
		
		function fileDetail(id){
			popup.layerShow({url:'${ctxPath}/pages/common/file-view.jsp',area:['680px','400px'],scrollbar:false,offset:'20px',data:{fkId:id},id:'fileViewLayer',title:'查看文件'});
		}
		
		function initPage(){
			$("#CustFollowForm").render({data:{pageIndex:1,pageType:3,pageSize:10},success:function(result){
				$("#CustFollowForm").render({success:function(rs){
					 $('.layui-card-body').hide();
					 $('.layui-card-header').click(function(){
						 $(this).next().toggle();
					 });
				}});
			}});
		}
		
		function getContent(val){
			if(val){
				return val.replace(/\n|\r\n/g,'<br/>');
			}else{
				return '--';
			}
		}
		
		function changeWeekly(el){
			queryData();
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>