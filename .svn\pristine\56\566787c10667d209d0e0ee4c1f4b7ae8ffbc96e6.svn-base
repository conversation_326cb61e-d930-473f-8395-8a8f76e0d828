<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>考勤数据</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }


        .layui-this {
            font-weight: bold;
        }


        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .top-3 {
            font-size: 24px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .divInline * {
            display: inline;
        }

        .divInline {
            height: 24px;
        }


        .kq-stat .layui-card {
            padding: 10px;
            border-radius: 5px;
        }


        .layui-icon-tips {
            margin-left: 3px;
            font-size: 14px;
        }

        #fixedDiv {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 999;
            background: #fff;
            padding: 0px 10px;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        #searchForm {
            padding-top: 60px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .layui-card {
            border: 1px solid rgba(0, 0, 0, 0.1);
        }


    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm" autocomplete="off">
        <div class="layui-row" id="fixedDiv">
            <div class="form-group" style="flex-grow: 1;display: flex;margin-top: 10px;">
                <div class="input-group input-group-sm ml-5">
                    <span class="input-group-addon">考勤日期</span>
                    <input type="text" id="beginDate" name="beginDate" data-mars="CommonDao.yearBegin" data-laydate="{type:'date'}"
                           class="form-control input-sm" style="width: 90px;">
                    <input type="text" id="endDate" name="endDate" data-laydate="{type:'date'}" class="form-control input-sm"
                           style="width: 90px;">
                </div>
                <c:if test="${ismgr=='1'}">
                <div class="input-group input-group-sm">
                    <button type="button" class="btn btn-sm btn-default ml-10" onclick="onTimeChange()"><span
                            class="glyphicon glyphicon-search"></span> 搜索
                    </button>
                </div>
                </c:if>
            </div>
        </div>

        <div class="layui-row layui-col-space10 kq-stat" data-mars="KqStatDao.userStatRecord">
            <div class="layui-col-md2">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="divInline">
                            <div class="top-1" id="WORK_DAY">0</div>
                        </div>
                        <div class="top-2">
                            本人本年出勤天数
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="NOT_WORK">0</div>
                        <div class="top-2">本年缺勤天数<i class="layui-icon layui-icon-tips" lay-tips="缺勤天数包括旷工、请假(病假年假事假等所有请假情况)的天数，不包括公出。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="OVER_TIME_COUNT">0</div>
                        <div class="top-2">本年加班次数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="OVER_TIME">0</div>
                        <div class="top-2">本年加班总时长/小时</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-3" id="LATE_TIME_COUNT">0</div>
                        <div class="top-2">本年迟到次数</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md2">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-3" id="LATE_TIME">0</div>
                        <div class="top-2">本年迟到总时长/分</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">月份趋势</div>
                    <div style="height: 360px">
                        <div id="monthOverChart" style="height: 340px;width: 100%;" data-anim="fade"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md7">
                <div class="layui-card">
                    <div class="layui-card-header">部门加班TOP20</div>
                    <div style="height: 360px">
                        <div id="deptOverChart1" style="height: 340px;width: 100%;" data-anim="fade"></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md5">
                <div class="layui-card">
                    <div class="layui-card-header">部门迟到TOP10</div>
                    <div style="height: 360px">
                        <div id="deptLateChart" style="height: 340px;width: 100%;" data-anim="fade"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">个人加班Top30 <i class="layui-icon layui-icon-tips" lay-tips="加班率由员工加班天数/该员工个人的打卡天数(除去公出和请假天数)计算。"></i></div>
                    <div style="height: 380px">
                        <div id="overChart3" style="height: 360px;width: 100%;" data-anim="fade"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">个人迟到TOP30<i class="layui-icon layui-icon-tips" lay-tips="迟到率由员工迟到天数/该员工个人的打卡天数(除去公出和请假天数)计算。"></i></div>
                    <div style="height: 380px">
                        <div class="layui-col-md12">
                            <div id="lateChart3" style="height: 360px;width: 100%;" data-anim="fade"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

    <script>
        $(function () {

            $("#searchForm").render({
                success: function () {
                    onTimeChange();
                }
            });

        });

        function onTimeChange(){
            loadMonthOverChart();
            loadOverChartMulti();
            loadDeptOverChart1();
            loadLateChartMulti();
            loadDeptLateChart();
        }
        function loadMonthOverChart(){
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            ajax.remoteCall("/yq-work/webcall?action=KqStatDao.overTimeStatByMonth", {"beginDate": beginDate, "endDate": endDate}, function (result) {
                var data2 = result.data;
                var year = data2['YEAR'];
                var months = ["01","02","03","04","05","06","07","08","09","10","11","12"];
                var monthNames = months.map(month => year+'_'+month);
                var nums1 = monthNames.map(monthName => data2["OVER_TIME_"+monthName]);
                var nums2 = monthNames.map(monthName => data2["OVER_TIME_COUNT_"+monthName]);
                var legend = ['加班总时长/小时','加班次数']
                loadBarAndLineChart(monthNames,nums1,nums2, 'monthOverChart', legend,"#91cc75")
            })
            }

        function loadOverChartMulti(){
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            ajax.remoteCall("/yq-work/webcall?action=KqStatDao.multiOverTimeRank", {"beginDate": beginDate, "endDate": endDate,"pageSize": 30}, function (result) {
                var data2 = result.data;
                data2 = data2.slice(0, 30);
                var userName = data2.map(item => item.USER_NAME);
                var nums1 = data2.map(item => Number(item.OVER_TIME).toFixed(1));
                var nums2 = data2.map(item => item.OVER_TIME_COUNT);
                var nums3= data2.map(item => parseInt(item.OVER_RATIO * 100));
                var nums4 = data2.map(item => parseInt(item.AVG_OVER_TIME));
                var legend = ['加班总时长/小时','加班次数',"加班率"]
                loadBarAndLineAndBubbleChart2(userName, nums1, nums2, nums3,nums4,'次均加班时间/分：', 'overChart3', legend,"#84ceed","#5470c6")
            })
        }


        function loadDeptOverChart1(){
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();

            ajax.remoteCall("/yq-work/webcall?action=KqStatDao.overTimeRankByDept", {"beginDate": beginDate, "endDate": endDate,"pageSize":"20"}, function (result) {
                var data2 = result.data;
                data2 = data2.slice(0, 30);
                var deptName = data2.map(item => item.DEPT);
                var nums1 = data2.map(item => Number(item.AVG_OVER_TIME).toFixed(1));
                var nums2 = data2.map(item => Number(item.AVG_OVER_TIME_COUNT).toFixed(0));
                var legend = ['人均总加班/小时','人均加班次数']
                loadBarAndLineChart(deptName,nums1,nums2, 'deptOverChart1', legend)

            })

        }


        function loadDeptLateChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            ajax.remoteCall("/yq-work/webcall?action=KqStatDao.lateTimeRankByDept", {"beginDate": beginDate, "endDate": endDate,"pageSize":"10"}, function (result) {
                var data2 = result.data;
                data2 = data2.slice(0, 10);
                var deptName = data2.map(item => item.DEPT);
                var nums1 = data2.map(item => parseInt(item.AVG_LATE_TIME));
                var nums2 = data2.map(item => parseInt(item.AVG_LATE_TIME_COUNT,10));
                var legend = ['人均迟到时间/分','人均迟到次数']
                loadBarAndLine2yAxisChart(deptName,nums1,nums2, 'deptLateChart', legend)
            })
        }

        function loadLateChartMulti(){
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            ajax.remoteCall("/yq-work/webcall?action=KqStatDao.multiLateTimeRank", {"beginDate": beginDate, "endDate": endDate,"pageSize": 30}, function (result) {
                var data2 = result.data;
                data2 = data2.slice(0, 30);
                var userName = data2.map(item => item.USER_NAME);
                var nums1 = data2.map(item => item.LATE_TIME_COUNT);
                var nums2 = data2.map(item => Number(item.LATE_TIME));
                var nums3 = data2.map(item => parseInt(item.LATE_RATIO*100));
                var nums4 = data2.map(item => parseInt(item.AVG_LATE_TIME));
                var legend = ['迟到次数','迟到时间/分', '迟到率']
                loadBarAndLineAndBubbleChart(userName, nums1, nums2, nums3,nums4,'次均迟到时间/分：', 'lateChart3', legend)
            })
        }

        // 定义一套统一的主题配色方案
        const chartColors = {
            primary: ['#5B8FF9', '#61DDAA', '#65789B', '#F6BD16', '#7262fd', '#78D3F8', '#9661BC', '#F6903D', '#008685'],
            secondary: ['#6F7DFF', '#78D3F8', '#FFB1AC', '#FFE08C', '#B4E6E2', '#95D3F9', '#D3B6E0', '#FF9F7F', '#37A2DA'],
            background: '#fff',
            textColor: '#333',
            axisLineColor: '#E0E6F1'
        };

        // 统一的图表基础配置
        const baseChartOptions = {
            backgroundColor: chartColors.background,
            textStyle: {
                color: chartColors.textColor
            },
            tooltip: {
                backgroundColor: 'rgba(255,255,255,0.9)',
                borderColor: '#e6e6e6',
                textStyle: {
                    color: '#666'
                },
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: 'rgba(200,200,200,0.1)'
                    }
                }
            },
            grid: {
                left: '3%',
                right: '4%', 
                bottom: '3%',
                containLabel: true
            }
        };

        // 修改 loadBarAndLineChart 函数（月份趋势）的 tooltip
        function loadBarAndLineChart(fieldName, amounts1, amounts2, tableName, legend, color) {
            const options = {
                ...baseChartOptions,
                color: color || chartColors.primary,
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(function(item) {
                            result += item.marker + item.seriesName + ': ' + item.value + '<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: legend,
                    textStyle: {
                        color: chartColors.textColor
                    }
                },
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0,
                        color: chartColors.textColor
                    },
                    axisLine: {
                        lineStyle: {
                            color: chartColors.axisLineColor
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        color: chartColors.textColor
                    },
                    splitLine: {
                        lineStyle: {
                            color: chartColors.axisLineColor,
                            type: 'dashed'
                        }
                    }
                },
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: amounts1,
                        barWidth: '40%',
                        itemStyle: {
                            borderRadius: [4, 4, 0, 0]
                        },
                        label: {
                            show: true,
                            position: 'top',
                            distance: 5,
                            color: chartColors.textColor
                        }
                    },
                    {
                        name: legend[1],
                        type: 'line',
                        data: amounts2,
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 8,
                        lineStyle: {
                            width: 3
                        },
                        itemStyle: {
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            position: 'bottom',
                            distance: 15,
                            color: '#666',
                            fontSize: 12,
                            fontWeight: 500,
                            backgroundColor: 'rgba(255,255,255,0.7)',
                            borderRadius: 4,
                            padding: [4, 8],
                            formatter: '{c}'
                        }
                    }
                ]
            };

            const chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);
            return chart;
        }

        // 修改 loadBarAndLineAndBubbleChart 函数中的数据系列类型
        function loadBarAndLineAndBubbleChart(fieldName, amounts1, amounts2, amounts3, amounts4, names4, tableName, legend) {
            var maxAmount1 = Math.max(...amounts1);
            var fixedIndex = maxAmount1 * 0.2;

            var options = {
                color: '#f17c67',
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function (params) {
                        var result = params[0].name + '<br/>';
                        params.forEach(function (item) {
                            if (item.seriesType === 'bar' || item.seriesType === 'line') {
                                result += item.marker + item.seriesName + ': ' + (item.value) + '<br/>';
                            } else {
                                result += item.marker + item.seriesName + ': ' + (amounts3[params[0].dataIndex]) + '% <br/>';
                            }
                        });
                        result += names4 + amounts4[params[0].dataIndex] + '<br/>';
                        return result;
                    }
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '0%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '50%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '迟到次数',
                        axisLabel: {
                            formatter: '{value}'
                        },
                    },
                    {
                        type: 'value',
                        name: '迟到时间(分)',
                        position: 'right',
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#333'
                            }
                        },
                        splitLine: {
                            show: false
                        },
                        max: function(value) {
                            return Math.ceil(value.max * 1.2);
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],  // 迟到次数
                        type: 'line',
                        data: amounts1,
                        smooth: false,
                        yAxisIndex: 0,
                        itemStyle: {
                            color: "#333"
                        },
                        label: {
                            show: true,
                            position: 'inside',
                            color: "#FFF",
                            textBorderColor: '#333',
                            textBorderWidth: 2,
                            formatter: '{c}',
                            fontSize: 12,
                            fontWeight: 500
                        }
                    },
                    {
                        name: legend[1],  // 迟到时长
                        type: 'bar',
                        yAxisIndex: 1,
                        data: amounts2,
                        label: {
                            show: true,
                            position: 'top',
                            color: '#fff',
                            textBorderColor: '#e86b56',
                            textBorderWidth: 2,
                        },
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#ff9b8f'},
                                {offset: 1, color: '#f17c67'}
                            ])
                        }


                    },
                    {
                        name: legend[2],  // 迟到率
                        type: 'scatter',
                        data: amounts3.map((value, index) => [index, fixedIndex]),
                        symbolSize: function (data) {
                            return amounts3[data[0]] / 3;
                        },
                        yAxisIndex: 0,
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: function (params) {
                                return amounts3[params.data[0]] + '%';
                            },
                        },
                        itemStyle: {
                            color: "#ffc069"
                        },
                        z: 10
                    }
                ]
            };

            var chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);
            return chart;
        }

        function loadBarAndLineAndBubbleChart2(fieldName, amounts1, amounts2, amounts3, amounts4, names4, tableName, legend, color1, color2) {
            var maxAmount1 = Math.max(...amounts1);
            var fixedIndex = Number(maxAmount1) / 10;

            var options = {
                color: '#5b8ff9',
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function (params) {
                        var result = params[0].name + '<br/>';
                        params.forEach(function (item) {
                            if (item.seriesType === 'bar' || item.seriesType === 'line') {
                                result += item.marker + item.seriesName + ': ' + (item.value) + '<br/>';
                            } else {
                                result += item.marker + item.seriesName + ': ' + (amounts3[params[0].dataIndex]) + '% <br/>';
                            }
                        });
                        result += names4 + amounts4[params[0].dataIndex]  + '<br/>';
                        return result;
                    }
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '3%',
                    right: '3%',
                    bottom: '0%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '50%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        },
                        max:maxAmount1
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: amounts1,
                        yAxisIndex: 0,
                        label: {
                            show: true,
                            position: 'top',
                            color:'#fff',
                            textBorderColor:color2,
                            textBorderWidth: 2,
                        },
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#7ca3fa'},
                                {offset: 1, color: '#5b8ff9'}
                            ])
                        }
                    },
                    {
                        name: legend[1],
                        type: 'line',
                        data: amounts2,
                        smooth: false,
                        itemStyle: {
                            color:  "#333"
                        },
                        label: {
                            show: true,
                            position: 'inside',
                            color:  "#FFF",
                            textBorderColor:'#333',
                            textBorderWidth: 2,
                        },
                    },
                    {
                        name: legend[2],
                        type: 'scatter',
                        data: amounts3.map((value, index) => [index, fixedIndex]),
                        symbolSize: function (data) {
                            return amounts3[data[0]]/3;
                        },
                        yAxisIndex: 0,
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: function (params) {
                                return amounts3[params.data[0]]+'%';
                            },
                        },
                        itemStyle: {
                            color: "#91cc75"
                        },
                    }

                ],
            };

            var chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);

            return chart;
        }

        function loadPieChart(echartsData, chartName) {
            var options1 = {
                color: ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"],
                tooltip: {
                    trigger: "item",
                    formatter: "{b}: {c}<br> ({d}%)"
                },
                grid: {
                    left: '35%',
                    right: '35%',
                    top: '1%',
                    bottom: '0%',
                    containLabel: false
                },
                series: [
                    {
                        name: "",
                        type: "pie",
                        radius: ['20%', '55%'],
                        data: echartsData,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{b}: {c} ({d}%)'
                        },
                        labelLine: {
                            show: true
                        }
                    },
                ]
            };
            var myCharts = echarts.init(document.getElementById(chartName));
            myCharts.setOption(options1);
        }

        var getDate = function (obj, startDate, endDate) {
            var val = obj.value;
            var bdate = new Date();
            var edate = new Date();
            var monthAdjustments = {
                'oneMonth': -1,
                'threeMonth': -3,
                'halfYear': -6,
                'oneYear': -12,
                'twoYear': -24
            };
            if (val in monthAdjustments) {
                bdate.setMonth(bdate.getMonth() + monthAdjustments[val]);
            } else if (val === 'nowYear') {
                bdate.setMonth(0, 1);
                edate.setMonth(11, 31);
            } else if (val === 'lastYear') {
                bdate.setFullYear(bdate.getFullYear() - 1, 0, 1);
                edate.setFullYear(edate.getFullYear() - 1, 11, 31);
            } else if (val === 'nextYear') {
                bdate.setFullYear(bdate.getFullYear() + 1, 0, 1);
                edate.setFullYear(edate.getFullYear() + 1, 11, 31);
            }
            var bdateVal = val && val !== 'all' ? DateUtils.dateFormat(bdate, 'yyyy-MM-dd') : '';
            var edateVal = val && val !== 'all' ? DateUtils.dateFormat(edate, 'yyyy-MM-dd') : '';
            $('[name="' + startDate + '"]').val(bdateVal);
            $('[name="' + endDate + '"]').val(edateVal);
        }

        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });

        // 修改 loadBarAndLine2yAxisChart 函数（部门迟到）的 tooltip
        function loadBarAndLine2yAxisChart(fieldName, amounts1, amounts2, tableName, legend) {
            const options = {
                ...baseChartOptions,
                color: ['#FF9F7F', '#5470c6'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        // 构建tooltip内容
                        let result = params[0].name + '<br/>'; // 先显示x轴的值（部门名称）
                        params.forEach(function(item) {
                            result += item.marker + item.seriesName + ': ' + item.value + '<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: legend[0],
                        position: 'left',
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#FF9F7F'
                            }
                        },
                        splitLine: {
                            show: false  // 去掉网格线
                        },
                        axisLabel: {
                            formatter: '{value}'
                        }
                    },
                    {
                        type: 'value',
                        name: legend[1],
                        position: 'right',
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#5470c6'
                            }
                        },
                        splitLine: {
                            show: false  // 去掉网格线
                        },
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: amounts1,
                        barWidth: '40%',
                        itemStyle: {
                            borderRadius: [4, 4, 0, 0],
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {offset: 0, color: '#ff7c6e'},
                                {offset: 1, color: '#FF9F7F'}
                            ])
                        },
                        label: {
                            show: true,
                            position: 'top',
                            distance: 5,
                            color: '#666',
                            fontSize: 12,
                            fontWeight: 500,
                            backgroundColor: 'rgba(255,255,255,0.7)',
                            borderRadius: 3,
                            padding: [2, 4],
                            formatter: '{c}'
                        }
                    },
                    {
                        name: legend[1],
                        type: 'line',
                        yAxisIndex: 1,
                        data: amounts2,
                        smooth: true,
                        symbolSize: 8,
                        lineStyle: {
                            width: 3
                        },
                        label: {
                            show: true,
                            position: 'bottom',  // 改为底部显示，避免重叠
                            distance: 15,        // 增加距离
                            color: '#5470c6',
                            fontSize: 12,
                            fontWeight: 500,
                            formatter: '{c}',
                            backgroundColor: 'rgba(255,255,255,0.7)',
                            borderRadius: 3,
                            padding: [2, 4]
                        }
                    }
                ]
            };

            const chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);
            return chart;
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
