<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
		.h-title{
			font-size: 12px;
    		color: #777;
		}
		.h-value{
			min-height: 14px;
		    margin-top: 3px;
		    font-size: 13px;
		    color: #333;
		    text-overflow: ellipsis;
		    display: -webkit-box;
		    -webkit-line-clamp: 2;
		    -webkit-box-orient: vertical;
		    overflow: hidden;
		}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 13px;}
		.layui-tab-card>.layui-tab-title{background-color: #f5f6f9;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<div id="custDetailForm">
				<div style="border-bottom: 1px solid #ddd;margin: 0px -15px;" data-mars="CustDao.record" data-mars-prefix="cust.">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md4">
					     <i class="layui-icon layui-icon-about" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 16px;" name='cust.CUST_NAME'></span>
					  </div>
					  <div class="layui-col-md4">
					  	&nbsp;<input type="hidden" value="${param.custId}" id="custId" name="custId"/>
					  </div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm">
									<button class="btn btn-default btn-xs" onclick="editCust();" type="button">编辑</button>
									<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">新建 <span class="caret"></span></button>
									<ul class="dropdown-menu dropdown-menu-right">
									    <li><a href="#">联系人</a></li>
									    <li><a href="#">商机</a></li>
									    <li><a href="#">合同</a></li>
									    <li role="separator" class="divider"></li>
									    <li><a href="#">回款</a></li>
									    <li><a href="#">发票</a></li>
									</ul>
								</div>
								<div class="btn-group btn-group-sm">
									<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">•••</button>
									<ul class="dropdown-menu dropdown-menu-right">
									    <li><a href="#">删除</a></li>
									    <li><a href="#">转移给他人</a></li>
									</ul>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.layerClose('custDetailForm')"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding:10px 15px;">
					  <div class="layui-col-md2">
					  	<span class="h-title">客户级别</span>
					  	<span class="h-value" name="cust.CUST_LEVEL" data-fn='getCustLevel'></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">成交状态</span>
					  	<span class="h-value">未成交</span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">负责人</span>
					  	<span class="h-value" name="cust.OWNER_ID" data-fn="getUserName">重要</span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">税号</span>
					  	<span class="h-value" name="cust.TAX_NUM"></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">更新时间</span>
					  	<span class="h-value" name="cust.UPDATE_TIME"></span>
					  </div>
					</div>
				</div>
				<div class="layui-row" style="background-color: #f8f8f8;">
					<div class="layui-col-md8">
						<div class="layui-tab layui-tab-card" style="margin: 10px -15px;border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">基本信息</li>
						    <li>联系人</li>
						    <li>团队成员</li>
						    <li>商机</li>
						    <li>合同</li>
						    <li>回款</li>
						    <li>发票</li>
						    <li>附件</li>
						    <li>操作记录</li>
						  </ul>
						  <div class="layui-tab-content" style="background-color: #fff;min-height: calc( 100vh - 200px)">
						    <div class="layui-tab-item layui-show">
						    	<jsp:include page="include/base-info.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/contacts.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/invoice-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						  </div>
					</div>
					</div>
					<div class="layui-col-md4">
						<div class="layui-card" style="margin: 10px 10px 15px 25px;border: 1px solid #eee;">
						  <div class="layui-card-header" style="background-color: #f5f6f9;">动态 <button type="button" class="btn btn-xs pull-right mt-10 btn-info" onclick="addFollow()">+ 新建</button></div>
						  <div class="layui-card-body" style="min-height: calc( 100vh - 200px);">
							    <jsp:include page="include/follow-list.jsp"/>
						  </div>
						</div>
					</div>
				</div>
		</div>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/pages/crm/staitc/table.js"></script>
	<script type="text/javascript" src="${ctxPath}/pages/crm/staitc/mgr.js"></script>
	<script type="text/javascript">
   
		jQuery.namespace("Cust");
	    
		Cust.custId='${param.custId}';
		
		var custId='${param.custId}';
		
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
				var form = layui.form;
				form.render();
			});
			$("#custDetailForm").render({success:function(result){
				
			}});  
		});
		
		Cust.ajaxSubmitForm = function(){
			if(form.validate("#custDetailForm")){
				if(Cust.custId){
					Cust.updateData(); 
				}else{
					Cust.insertData(); 
				}
			};
		}
		Cust.insertData = function(flag) {
			var data = form.getJSONObject("#custDetailForm");
			ajax.remoteCall("${ctxPath}/servlet/cust?action=addCust",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadProjectList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Cust.updateData = function(flag) {
			var data = form.getJSONObject("#custDetailForm");
			ajax.remoteCall("${ctxPath}/servlet/cust?action=updateCust",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadProjectList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>