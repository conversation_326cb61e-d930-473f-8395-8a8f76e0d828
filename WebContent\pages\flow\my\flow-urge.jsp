<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>催办查询</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		 	<div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">类型</span>
						 	<select class="form-control input-sm" id="dataType" name="dataType" onchange="queryData()">
						 		<option value="0">我接收的催办</option>
						 		<option value="1">我发起的催办</option>
						 	</select>
						 </div>
	          		 	 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程名称</span>
						 	<input class="form-control input-sm" name="flowName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
				{{if CHECK_RESULT=='0'&&dataType=='0'}}
 					<a class="btn btn-xs btn-info" lay-event="flowCheck" href="javascript:;"><i class="fa fa-paper-plane-o"></i> 审批</a>
					{{else}}
 					<a class="btn btn-xs btn-default" lay-event="flowDetail" href="javascript:;">详情</a>
				{{/if}}
		  </script>
		  	 <script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default" lay-event="refreshData">刷新</button>
 		</script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'FlowDao.myUrgeFlow',
					id:'flowTable',
					page:true,
					limit:20,
					toolbar:'#btnBar',
					rowDoubleEvent:'flowDetail',
					height:'full-90',
					cellMinWidth:80,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						field:'',
						title:'操作',
						width:80,
						align:'center',
						templet:function(row){
							row['dataType'] = $('#dataType').val();
							return renderTpl('bar',row);
						}
					},{
						 field:'APPLY_NO',
						 title:'编号',
						 width:200,
						 templet:function(row){
							 var readTime = row['READ_TIME'];
							 var html = '';
							 if(readTime==''){
								 html = '<span class="layui-badge-dot mr-5"></span>';
							 }
							 return html+row['APPLY_NO']+"_"+row['APPLY_TITLE'];
						 }
					 },{
						 field:'URGE_BY',
						 title:'发起催办人',
						 width:80
					 },{
						 field:'RECEIVER_NAME',
						 title:'被催人',
						 width:80
					 },{
						 field:'URGE_TIME',
						 title:'催办时间',
						 width:130
					 },{
						 field:'MSG_CONTENT',
						 title:'催办内容',
						 minWidth:220
					 },{
						 field:'NODE_NAME',
						 title:'催办节点',
						 width:110
					 },{
						 field:'APPLY_NAME',
						 title:'流程发起人',
						 width:110,
						 templet:'<div>{{d.DEPT_NAME}}.{{d.APPLY_NAME}}</div>'
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					}
				]],done:function(){
					
			  	}
			});
		 }
			
		function flowDetail(data){
			var flowCode = data['FLOW_CODE'];
			var readTime = data['READ_TIME'];
			var resultId = data['RESULT_ID'];
			var dataType = $('#dataType').val();
			if(readTime==''&&dataType=='0'){
				ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=addReadTime",{resultId:resultId},function(result) {});
			}
			var json  = $.extend({},{businessId:data['APPLY_ID'],flowCode:flowCode,'handle':'detail',resultId:resultId});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		function flowCheck(data){
			var flowCode = data['FLOW_CODE'];
			var readTime = data['READ_TIME'];
			var resultId = data['RESULT_ID'];
			if(readTime==''){
				ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=addReadTime",{resultId:resultId},function(result) {});
			}
			var json  = $.extend({},{businessId:data['APPLY_ID'],flowCode:flowCode,isApprovalFlag:'1','handle':'approval',resultId:resultId});
			popup.openTab({id:'flowDetail',title:'审批-'+data['FLOW_NAME'],url:'${ctxPath}/web/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
			
	    function refreshData(){
		   location.reload();
	    }
		  
		function entrust(dataList){
			
		}
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>