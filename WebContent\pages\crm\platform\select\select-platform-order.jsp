<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择订购</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectOrderForm">
       	   <input name="platformId" value="${param.platformId}" type="hidden"/>
	       <div class="row">
	   			 <div class="input-group input-group-sm ml-10" style="width: 160px">
					 <span class="input-group-addon">客户名称</span>	
					 <input type="text" name="custName" class="form-control input-sm" value="${param.custName}">
			     </div>
	   			 <div class="input-group input-group-sm ml-5" style="width: 160px">
					 <span class="input-group-addon">商机名称</span>
					 <input type="text" name="sjName" class="form-control input-sm" value="${param.sjName}">
			     </div>
	   			 <div class="input-group input-group-sm ml-55" style="width: 160px">
					 <span class="input-group-addon">合同名称</span>
					 <input type="text" name="contractName" class="form-control input-sm" value="${param.contractName}">
			     </div>

    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter" onclick="SelectOrder.query()">
                    <span class="glyphicon glyphicon-search"></span> 搜索
                 </button>
	   		</div>
           	<div class="ibox">
              	<table id="selectOrderList"></table>
            </div>
            <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary" type="button" onclick="SelectOrder.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20" type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
			var SelectOrder = {
					sid:'${param.sid}',
					query : function(){
						$("#selectOrderForm").queryData();
					},
					initTable : function(){
						$("#selectOrderForm").initTable({
							mars:'PlatformOrderDao.orderPageList',
							id:'selectOrderList',
							page:true,
							limit:20,
							height:'350px',
							rowDoubleEvent:'SelectOrder.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'CUST_NAME',
								title: '客户名称',
								width:120
							},{
							    field: 'BUSINESS_NAME',
								title: '商机名称',
								width:120
							},{
							    field: 'CONTRACT_NAME',
								title: '合同名称',
								width:120
							},{
                                field: 'ORDER_STATUS',
                                title: '订购状态',
                                width:80,
                                align: 'center',
                                templet: function(d){
                                    var status = ['正常','暂停','终止'];
                                    return status[d.ORDER_STATUS] || '未知';
                                }
                            },{
                                field: 'ORDER_AMOUNT',
                                title: '订购金额',
                                width:100,
                                align:'right'
                            },{
                                field: 'START_DATE',
                                title: '开始日期',
                                width:100,
                                align:'center'
                            },{
                                field: 'END_DATE',
                                title: '结束日期',
                                width:100,
                                align:'center'
                            },{
                                field: 'SALES_BY_NAME',
                                title: '销售',
                                width:80,
                                align:'center'
                            }
							]]
					    });
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectOrder.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('selectOrderList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['BUSINESS_NAME'] + ' - ' + data[index]['CUST_NAME'] + ' - ' + data[index]['PLATFORM_NAME']);
									ids.push(data[index]['ORDER_ID']);
								}
								el.val(names.join(','));
								if(el.prev().length>0){
									el.prev().val(ids.join(','));
								}

								if(typeof PlatformOrderSelectCallBack === 'function') {
									PlatformOrderSelectCallBack({
										orderIds: ids.join(','),
										orderNames: names.join(','),
                                        contractId: data[0]['CONTRACT_ID'],
                                        contractName: data[0]['CONTRACT_NAME']
									});
								}
								
								popup.layerClose("selectOrderForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							var name = selectRow['BUSINESS_NAME'] + ' - ' + selectRow['CUST_NAME'] + ' - ' + selectRow['PLATFORM_NAME'];
							el.val(name);
							if(el.prev().length>0){
								el.prev().val(selectRow['ORDER_ID']);
							}

							if(typeof PlatformOrderSelectCallBack === 'function') {
								PlatformOrderSelectCallBack({
									orderIds: selectRow['ORDER_ID'],
									orderNames: name,
                                    contractId: selectRow['CONTRACT_ID'],
                                    contractName: selectRow['CONTRACT_NAME']
								});
							}
							
							popup.layerClose("selectOrderForm");
						}
					}
			};
			$(function(){
				SelectOrder.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
