package com.yunqu.work.dao.other;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;

@WebObject(name = "TaskStatDao")
public class TaskStatDao extends AppDaoContext {

	@WebControl(name = "thisYearCountStat",type = Types.RECORD)
	public JSONObject thisYearCountStat() {
		JSONObject row = new JSONObject();
		EasySQL sql = getEasySQL("SELECT count(DISTINCT(project_id)) from yq_task where 1=1");
		sql.appendRLike(EasyCalendar.newInstance().getYear(),"and DATE_ID like ?");
		sql.append(getUserId(),"and assign_user_id = ?");
		try {
			row.put("DATA1", this.getQuery().queryForInt(sql.getSQL(), sql.getParams()));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		sql = getEasySQL("SELECT count(1) from yq_task where 1=1");
		sql.appendRLike(EasyCalendar.newInstance().getYear(),"and DATE_ID like ?");
		sql.append(getUserId(),"and assign_user_id = ?");
		try {
			row.put("DATA2", this.getQuery().queryForInt(sql.getSQL(), sql.getParams()));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		sql = getEasySQL("SELECT count(1) from yq_task where 1=1");
		sql.appendRLike(EasyCalendar.newInstance().getYear(),"and DATE_ID like ?");
		sql.append(getUserId(),"and CREATOR = ?");
		try {
			row.put("DATA3", this.getQuery().queryForInt(sql.getSQL(), sql.getParams()));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		sql = getEasySQL("SELECT count(1) from yq_project_risk where 1=1");
		sql.appendRLike(EasyCalendar.newInstance().getYear(),"and CREATE_TIME like ?");
		sql.append(getUserId(),"and SOL_OWNER = ?");
		sql.append(1,"and RISK_STATE = ?");
		try {
			row.put("DATA4", this.getQuery().queryForInt(sql.getSQL(), sql.getParams()));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		
		return getJsonResult(row);
	}
	
	
	@WebControl(name = "nowYearTaskData",type = Types.PAGE)
	public JSONObject nowYearTaskData() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT ");
		sql.append("IFNULL(project_count, 0) project_count, ");
		sql.append("IFNULL(taskcount, 0) task_count, ");
		sql.append("IFNULL(task_doing, 0) task_doing, ");
		sql.append("ROUND(IFNULL(plan_work_hour, 0)) PLAN_WORK_HOUR, ");
		sql.append("IFNULL(task_delay_count, 0) task_delay_count, ");
		sql.append("IFNULL(year_delay_task_count, 0) year_delay_task_count, ");
		sql.append("ROUND(IFNULL(use_work_hour, 0)) use_work_hour, ");
		sql.append("t1.USER_ID, ");
		sql.append("t1.USERNAME, ");
		sql.append("t1.DEPTS ");
		sql.append("FROM yq_main.easi_user t1 ");
		sql.append("LEFT JOIN ( ");
		sql.append("SELECT assign_user_id, ");
		sql.append("count(DISTINCT(d1.project_id)) project_count, ");
		sql.append("count(1) taskcount, ");
		sql.append("SUM(CASE WHEN d1.task_state IN (10, 20) THEN 1 ELSE 0 END) AS task_doing, ");
		sql.append("SUM(CASE WHEN d1.task_state IN (10, 20) THEN d1.PLAN_WORK_HOUR ELSE 0 END) AS plan_work_hour, ");
		sql.append("SUM(CASE WHEN d1.task_state IN (10, 20) AND d1.deadline_at < NOW() THEN 1 ELSE 0 END) AS task_delay_count, ");
		sql.append("SUM(d1.work_hour) use_work_hour, ");
		sql.append("SUM(CASE WHEN d1.delay_count > 0 THEN 1 ELSE 0 END) AS year_delay_task_count ");
		sql.append("FROM yq_task d1 ");
		String yearId = param.getString("yearId");
		if(StringUtils.isBlank(yearId)) {
			yearId = String.valueOf(EasyCalendar.newInstance().getYear());
		}
		sql.appendRLike(yearId,"WHERE d1.date_id like ?");
		sql.append("GROUP BY d1.assign_user_id ");
		sql.append(") t2 ON t1.USER_ID = t2.assign_user_id ");
		sql.append("WHERE t1.STATE = 0 ");
		sql.appendLike(param.getString("deptName"),"and t1.DEPTS like ?");
		sql.appendLike(param.getString("userName"),"and t1.username like ?");
	    int source = param.getIntValue("source");
	    String deptId = param.getString("deptId");
	    if(source==1) {
	    	deptId = getDeptId();
	    }
	    sql.append(deptId,"and t1.user_id in (select USER_ID from yq_main.easi_dept_user where DEPT_ID = ?)");
		sql.append("ORDER BY task_count desc");
		JSONObject result =  queryForPageList(sql.getSQL(),sql.getParams());
		JSONArray array = result.getJSONArray("data");
		for(int i=0,len = array.size();i<len;i++) {
			JSONObject row = array.getJSONObject(i);
			String userId = row.getString("USER_ID");
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
			row.put("JOIN_DATE", staffModel.getJoinDate());
			row.put("JOB_POST", staffModel.getPost());
		}
		return result;
	}
	@WebControl(name = "peopleWorkInfoList",type = Types.PAGE)
	public JSONObject peopleWorkInfoList() {
		EasySQL sql = new EasySQL();
		sql.append("SELECT ");
		sql.append("t2.create_time last_task_time,");
		sql.append("t2.project_names,");
		sql.append("IFNULL(project_count, 0) project_count, ");
		sql.append("IFNULL(taskcount, 0) task_count, ");
		sql.append("IFNULL(task_doing, 0) task_doing, ");
		sql.append("ROUND(IFNULL(plan_work_hour, 0)) PLAN_WORK_HOUR, ");
		sql.append("IFNULL(task_delay_count, 0) task_delay_count, ");
		sql.append("IFNULL(year_delay_task_count, 0) year_delay_task_count, ");
		sql.append("ROUND(IFNULL(use_work_hour, 0)) use_work_hour, ");
		sql.append("t1.USER_ID, ");
		sql.append("t1.USERNAME, ");
		sql.append("t1.DEPTS ");
		sql.append("FROM yq_main.easi_user t1 ");
		sql.append("LEFT JOIN ( ");
		sql.append("SELECT assign_user_id, ");
		sql.append("count(DISTINCT(d1.project_id)) project_count, ");
		sql.append("count(1) taskcount, ");
		sql.append("SUM(CASE WHEN d1.task_state IN (10, 20) THEN 1 ELSE 0 END) AS task_doing, ");
		sql.append("SUM(CASE WHEN d1.task_state IN (10, 20) THEN d1.PLAN_WORK_HOUR ELSE 0 END) AS plan_work_hour, ");
		sql.append("SUM(CASE WHEN d1.task_state IN (10, 20) AND d1.deadline_at < NOW() THEN 1 ELSE 0 END) AS task_delay_count, ");
		sql.append("SUM(d1.work_hour) use_work_hour, ");
		sql.append("max(d1.create_time) create_time,");
		sql.append("GROUP_CONCAT(distinct(d1.proj_name)) AS project_names,");
		sql.append("SUM(CASE WHEN d1.delay_count > 0 THEN 1 ELSE 0 END) AS year_delay_task_count ");
		sql.append("FROM yq_task d1 ");
		sql.append("WHERE 1=1");
		String dateRange = param.getString("taskDateRange");
		if(StringUtils.notBlank(dateRange)) {
			String[] array  = dateRange.replaceAll("-","").split(" 到 ");
			sql.append(array[0],"and d1.date_id >= ?");
			sql.append(array[1],"and d1.date_id <= ?");
		}
		sql.append("GROUP BY d1.assign_user_id ");
		sql.append(") t2 ON t1.USER_ID = t2.assign_user_id ");
		sql.append("WHERE t1.STATE = 0 ");
		sql.appendLike(param.getString("deptName"),"and t1.DEPTS like ?");
		sql.appendLike(param.getString("userName"),"and t1.username like ?");
		int source = param.getIntValue("source");
		String deptId = param.getString("deptId");
		if(source==1) {
			deptId = getMgrDeptIds();
		}
		if(StringUtils.isBlank(deptId)) {
			deptId = getDeptId();	
		}		
		sql.append("and t1.user_id in (select USER_ID from yq_main.easi_dept_user where 1=1");
		sql.appendIn(deptId.split(","),"and DEPT_ID");
		sql.append(")");
		sql.append("ORDER BY task_count desc");
		JSONObject result =  queryForPageList(sql.getSQL(),sql.getParams());
		JSONArray array = result.getJSONArray("data");
		for(int i=0,len = array.size();i<len;i++) {
			JSONObject row = array.getJSONObject(i);
			String userId = row.getString("USER_ID");
			StaffModel staffModel = StaffService.getService().getStaffInfo(userId);
			row.put("JOIN_DATE", staffModel.getJoinDate());
			row.put("JOB_POST", staffModel.getPost());
			row.put("PIC_URL", staffModel.getPicUrl());
		}
		return result;
	}
	
	
}
