<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的申请</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input type="hidden" name="flowCode" value="${param.flowCode}"/>
			<input type="hidden" name="recorded" value="1"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm ml-5">
							 <span class="input-group-addon">入账日期</span>	
							 <input type="text" name="beginDate" data-mars="CommonDao.prevMonthDay" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
							 <input type="text" name="endDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
				    	</div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="flowTable"></table>
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>

		  </script>
		</form>
		 <script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-info" lay-event="exportData"><span class="glyphicon glyphicon-export"></span> 导出金蝶EXCEL </button>
			 	<button class="btn btn-sm btn-success ml-10" lay-event="exportXmlData"><span class="glyphicon glyphicon-export"></span> 导出XML文件</button>
			 	<button class="btn btn-sm btn-default ml-10" lay-event="refreshData"><span class="glyphicon glyphicon-refresh"></span> 刷新</button>
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				renderDate('#dataMgrForm');
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'BxReportDao.hasyRecorded',
					id:'flowTable',
					page:true,
					toolbar:'#btnBar',
					rowDoubleEvent:'flowDetail',
					height:'full-100',
					limit:20,
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 type:'checkbox'
					 },{
						 field:'APPLY_NO',
						 title:'编号',
						 width:160,
						 templet:function(row){
							 return row['APPLY_NO'];
						 }
					 },{
						 field:'RECORDED_DATE',
						 title:'入账日期',
						 width:100
					 },{
						 field:'RECORDED_TIME',
						 title:'操作日期'
					 },{
						 field:'APPLY_NAME',
						 title:'报销人',
						 width:80
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
						 field:'PAY_MONEY',
						 title:'付款金额',
						 width:100
					 },{
						 field:'APPLY_REMARK',
						 title:'报销说明',
						 width:120
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					},{
					    field: 'APPLY_END_TIME',
						title: '审批完成时间',
						align:'center',
					    width:160
					},{
						field:'',
						title:'操作',
						width:50,
						fixed:'right',
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				]],done:function(){
					layui.use('laydate', function(){
						 var laydate = layui.laydate;
						 laydate.render({max:0, min:-365,elem: "#date"});
					});
			  	}
			});
		 }
			
		function flowDetail(data){
			var id = data['FLOW_CODE'];
			var json  = $.extend({},{businessId:data['APPLY_ID'],flowCode:id});
			popup.openTab({id:'flowDetail',title:'流程详情',url:'${ctxPath}/web/flow',data:json});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'flowTable'});
		}
		
		function refreshData(){
		   location.reload();
		}
		
		
		function exportData(dataList){
			layer.msg('正在导出',{time:500});
			window.open('${ctxPath}/servlet/bx/fun?action=exportBx&data='+encodeURI(JSON.stringify(form.getJSONObject('#dataMgrForm'))));
		}
		
		function exportXmlData(dataList){
			layer.msg('正在导出',{time:500});
			window.open('${ctxPath}/servlet/bx/fun?action=exportBxXml&data='+encodeURI(JSON.stringify(form.getJSONObject('#dataMgrForm'))));
		}
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>