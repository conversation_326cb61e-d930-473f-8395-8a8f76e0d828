package com.yunqu.work.service;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

import org.easitline.common.db.impl.JSONMapperImpl;
import org.jsoup.Connection;
import org.jsoup.Jsoup;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;

public class YApiService extends AppBaseService {
	
	private static class Holder{
		private static YApiService service=new YApiService();
	}
	public static YApiService getService(){
		return Holder.service;
	}
	
	
	public void regUser() {
		try {
			List<JSONObject> list = this.getMainQuery().queryForList("SELECT USERNAME,NIKE_NAME,SEX,MOBILE,EMAIL,BORN,USER_PWD,DATA1,USER_ACCT,DEPTS,ACCT_STATE from easi_user t1 INNER JOIN easi_user_login t2 on t1.USER_ID=t2.USER_ID where t1.state = 0",null,new JSONMapperImpl());
			getLogger().info("list>>"+list.size());
			if(list!=null&&list.size()>0){
				for(JSONObject object:list){
					String username = object.getString("USERNAME");
					String email = object.getString("EMAIL");
					String password = "yq123456";
					Connection jsoup = Jsoup.connect("http://172.16.68.152:3001/api/user/reg");
					jsoup.data("email",email);
					jsoup.data("password",password);
					jsoup.data("username",username);
					try {
						Response response = jsoup.method(Method.POST).ignoreContentType(true).timeout(5000).execute();
						getLogger().info(response.body());
					} catch (IOException e) {
						getLogger().error(null,e);
					}
				}
				
			}
		} catch (SQLException e) {
			getLogger().error(null,e);
		}
		
		
	}

	
}
