<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购详情</title>
	<style>
		.form-horizontal{width: 100%;}
		.layui-table-cell a{font-size: 12px;color: #3E84E9;cursor: pointer;}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 14px;}
		.layui-timeline-item:hover a{opacity:1;}
		.layui-tab-card>.layui-tab-title{background-color: #fff;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		.layui-timeline-content p{font-size: 13px;color: #666;}
		.layui-timeline-title{margin-bottom: 2px;}
		.layui-timeline-item{margin-bottom: 2px;}
		.layui-text h3{font-size: 16px;}
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.gray-bg{background-color: #e8edf7;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
		.editField{border: none;box-shadow:none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="OrderDetailForm" class="layui-card" data-mars="OrderFlowDao.getPaymentInfo" data-mars-prefix="payment.">
			    <input type="hidden" value="${param.businessId}" id="businessId" name="businessId"/>
			    <c:choose>
			    	<c:when test="${!empty bdata.supplierId}">
					    <input type="hidden" value="${bdata.supplierId}" id="supplierId" name="supplierId"/>
			    	</c:when>
			    	<c:otherwise>
					    <input type="hidden" value="${param.supplierId}" id="supplierId" name="supplierId"/>
			    	</c:otherwise>
			    </c:choose>
 	  	        <input type="hidden" value="${param.resultId}" id="resultId" name="resultId"/>
				<div style="padding: 5px;min-height:calc(100vh - 30px);" class="layui-card-body" data-mars="FlowDao.applyInfo" data-mars-prefix="apply.">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md8">
					     <i class="layui-icon layui-icon-app hidden-print" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 18px;" name='apply.APPLY_TITLE'></span>
					  </div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm hidden-print">
									<button class="btn btn-default btn-xs" onclick="window.print();" type="button"><i class="fa fa-print" aria-hidden="true"></i> 打印 </button>
						  			<c:if test="${param.isApprovalFlag=='1'}">
										<button class="btn btn-info btn-xs" onclick="addReviewResult();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 填写审批意见</button>
						  			</c:if>
						  			<c:if test="${applyInfo.applyState=='21'}">
										<button class="btn btn-warning btn-xs" onclick="restartPayApply();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 重新发起</button>
						  			</c:if>
						  			<yq:user userId="${applyInfo.applyBy}">
										<button class="btn btn-warning btn-xs" onclick="editPayApply();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 修改 </button>
						  			</yq:user>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close hidden-print"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding: 12px 20px;">
						<div class="layui-col-md12">
							  <jsp:include page="include/base-info.jsp"/>
						</div>
						<div class="layui-col-md12 table-responsive" data-mars="OrderFlowDao.paymentOrderList">
							<table class="layui-table text-nowrap">
								<thead>
									<tr>
										<td>订单号</td>
										<td>合同名称</td>
										<td>合同号</td>
										<td>订单金额</td>
										<td>入库金额</td>
										<td>已开票金额</td>
										<td>本次结算金额</td>
										<td>已申请金额</td>
										<td>已付款金额</td>
										<td>创建时间</td>
										<td>申请人</td>
										<td>备注</td>
									</tr>
								</thead>
								<tbody id="orderList" data-container="orderList" data-template="order-template"></tbody>
							</table>
							<script id="order-template" type="text/x-jsrender">
								{{if data.length==0}}
									<tr>
										<td colspan="11">暂无数据</td>
									</tr>
								{{/if}}
								{{for data}}
									<tr>
									  <td>{{:ORDER_NO}}</td>
									  <td>{{:CONTRACT_NAME}}</td>
									  <td>{{:CONTRACT_NO}}</td>
									  <td>
										  {{:TOTAL_PRICE}}
									  </td>
									  <td>
										  {{:RK_AMOUNT}}
									  </td>
									  <td>
										  {{:MAKE_AMOUNT}}
									  </td>
									  <td>
										  <input type="number" style="width:80px;" class="form-control input-sm editField" value="{{:APPLY_AMOUNT}}"/>
									  </td>
									  <td>
										  {{:HAS_APPLY_AMOUNT}}
									  </td>
									  <td>
										  {{:HAS_PAY_AMOUNT}}
									  </td>
									  <td>{{:CREATE_TIME}}</td>
									  <td>
										  {{:CREATE_USER_NAME}}
									  </td>
									  <td>
										  {{:APPLY_REMARK}}
									  </td>
									</tr>
							   {{/for}}
							</script>

						</div>
					</div>
					<div class="layui-row" style="padding: 0px 20px;">
						<div class="layui-col-md12">
							<jsp:include page="include/payment-check-list.jsp"/>
						</div>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   
		jQuery.namespace("OrderDetail");
	    
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
			});
			$("#OrderDetailForm").render({success:function(result){
				var orderList = result['OrderFlowDao.paymentOrderList'].data;
				render.readerTemplate($('#orderList'),{data:orderList});
			}});  
		});
		
		function addReviewResult(){
			var resultId = $('#resultId').val();
			var businessId = $('#businessId').val();
			popup.layerShow({id:'addResult',full:fullShow(),area:['500px','350px'],title:'填写审批意见',url:'${ctxPath}/pages/erp/order/payment/flow-approval.jsp',data:{resultId:resultId,businessId:businessId}});
		}
		
		function editPayApply(){
			
		}
		
		function restartPayApply(){
			layer.confirm('确认重新发起付款申请吗',{icon:3,offset:'50px'},function(index){
				layer.close(index);
				var businessId = $('#businessId').val();
				ajax.remoteCall("${ctxPath}/servlet/order/flow?action=restartPayApply",{businessId:businessId},function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							popup.closeTab();
						});
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				});
			});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>