<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择发票</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectInvoiceForm">
       	   <input name="hasSelectInvoices" id="hasSelectInvoices" type="hidden"/>
       	   <input name="invoiceType" value="${param.invoiceType}" type="hidden"/>
       	   <input name="bxItemId" value="${param.itemId}" type="hidden"/>
	       <div class="layui-row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 140px">
					 <span class="input-group-addon">开票月份</span>	
					 <input type="text" name="kpMonth" data-laydate="{type:'month',format: 'yyyyMM'}" class="form-control input-sm">
			     </div>
	   			 <div class="input-group input-group-sm ml-5" style="width: 160px">
					 <span class="input-group-addon">发票号</span>	
					 <input type="text" name="invoiceNo" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectInvocie.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		</div>
	   		<div class="layui-row mt-10">
	   			 <small class="ml-10 mr-30">请注意选择发票类型。当前显示的是待报销状态的发票，您可以双击单行来选中这些发票。</small>
	   		</div>
           	<div class="ibox" style="padding: 10px;">
             		<table id="SelectInvocieList"></table>
            </div>
            <div class="layer-foot text-c" style="z-index: 999999;">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectInvocie.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
        
        
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectInvocie={
					sid:'${param.sid}',
					bxItemId:'${param.itemId}',
					invoiceType:'${param.invoiceType}',
					okSubmit:true,
					query : function(){
						$("#selectInvoiceForm").queryData();
					},
					initTable : function(){
						if(_hasSelectInvoiceNos){
							$('#hasSelectInvoices').val(_hasSelectInvoiceNos);
						}
						
						if(SelectInvocie.invoiceType=='增值税电子普通发票'){
							$("[name='invoiceType']").val('1002');
						}
						if(SelectInvocie.invoiceType=='增值税电子专用发票'){
							$("[name='invoiceType']").val('2002');
						}
						if(SelectInvocie.invoiceType=='数电票(普通发票)'){
							$("[name='invoiceType']").val('1001');
						}
						if(SelectInvocie.invoiceType=='数电票(增值税专用发票)'){
							$("[name='invoiceType']").val('2001');
						}
						if(SelectInvocie.invoiceType=='电子发票(铁路电子客票)'){
							$("[name='invoiceType']").val('3001');
						}
						if(SelectInvocie.invoiceType=='电子发票(航空运输电子客票行程单)'){
							$("[name='invoiceType']").val('3002');
						}
						$("#selectInvoiceForm").initTable({
							mars:'BxInvoiceDao.selectMyBxInvoice',
							id:'SelectInvocieList',
							page:true,
							limit:50,
							limits:[5,10,20,50,100],
							rowDoubleEvent:'SelectInvocie.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'INVOICE_NO',
								title: '发票号',
								align:'left',
								width:160
							 },{
								field: 'SALE_COMPANY',
								title: '开票方',
								width:180
							 },{
							    field: 'TOTAL_AMOUNT',
								title: '含税金额',
								align:'left',
								width:90
							 },{
							    field: 'FILE_NAME',
								title: '文件名',
								align:'left',
							 },{
							    field: 'INVOICE_DATE',
								title: '开票日期',
								align:'center',
								width:110
							  },{
								 field:'INVOICE_TYPE',
								 title:'发票类型',
								 width:130
							  },
							  {
								 field:'TAX_RATE',
								 title:'税点',
								 width:80
							  }
						    ]],
						    done:function(res){
					    		var rate = $('.invoice-list tr').first().data('rate');
						    	table.on('checkbox(SelectInvocieList)', function(obj){
									var tableStatus = table.checkStatus('SelectInvocieList');
									var list = tableStatus.data;
									var array = [];
									for(var index in list){
										var taxRateNum = list[index]['TAX_RATE_NUM'];
										array.push(taxRateNum);
									}
									if(isSpecialInvoice(SelectInvocie.invoiceType)){
										var _array = arrayUnique(array);
										if(_array.length>1){
											layer.alert("存在多种税点【"+_array.join(',')+"】请分多行填写。",{icon:7,time:1800});
											SelectInvocie.okSubmit = false;
										}else if(rate!=undefined&&_array[0]!=rate){
											layer.alert("当前选择的税点和已选择的税点"+rate+"%不一致，请调整。",{icon:7});
											SelectInvocie.okSubmit = false;
										}else{
											SelectInvocie.okSubmit = true;
										}
									}
								});
						    }
					      }
						);
					},
					ok : function(selectRow){
						if(!SelectInvocie.okSubmit){
							return;
						}
						
						var el = $("[data-sid='"+SelectInvocie.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('SelectInvocieList');
							if(checkStatus.data.length>0){
								var data = checkStatus.data;
								var money  = 0;
								var taxMoney  = 0;
								var taxRateNum  = 0;
								for(var index in data){
									var invoiceId = data[index]['INVOICE_ID'];
									taxRateNum = data[index]['TAX_RATE_NUM'];
									money = money + Number(data[index]['TOTAL_AMOUNT']);
									taxMoney = taxMoney + Number(data[index]['TOTAL_TAX']);
									$(".item-"+invoiceId+"").remove();
								}
								
								var tmp = $.templates('#invoices-table');
								var html  = tmp.render({data:data});
								$('.invoice-list').prepend(html);
								
								if(isSpecialInvoice(SelectInvocie.invoiceType)){
									if(taxRateNum){
										$("[name='taxRate_"+SelectInvocie.bxItemId+"']").val(taxRateNum);
									}
									if(taxMoney>0){
										$("[name='taxes_"+SelectInvocie.bxItemId+"']").val(taxMoney);
									}
								}
								
								popup.layerClose("selectInvoiceForm");
							}else{
								
							}
						}else{
							var invoiceId = selectRow['INVOICE_ID'];
							$(".item-"+invoiceId+"").remove();
							
							var _array = new Array();
							_array.push(selectRow);
							
							var tmp = $.templates('#invoices-table');
							var html  = tmp.render({data:_array});
							$('.invoice-list').prepend(html);
							
							if(isSpecialInvoice(SelectInvocie.invoiceType)){
								var taxMoney  = Number(selectRow['TOTAL_TAX']);;
								var taxRateNum  = selectRow['TAX_RATE_NUM'];
								if(taxRateNum){
									$("[name='taxRate_"+SelectInvocie.bxItemId+"']").val(taxRateNum);
								}
								if(taxMoney>0){
									$("[name='taxes_"+SelectInvocie.bxItemId+"']").val(taxMoney);
								}
							}
							
							popup.layerClose("selectInvoiceForm");
						}
					}
					
			};
			
			function isSpecialInvoice(type){
				if(type.indexOf('专')>-1){
					return true;
				}
				return false;
			}
			
			$(function(){
				SelectInvocie.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>