package com.yunqu.work.service;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.utils.DateUtils;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;

public class DcNoticeService extends AppBaseService{

	private static class Holder{
		private static DcNoticeService service=new DcNoticeService();
	}
	
	public static DcNoticeService getService(){
		return Holder.service;
	}
	
	public void sendDcNotice(int type) {
		this.getLogger().info("DcNoticeService>type>"+type);
		EasySQL baseSql = new EasySQL();
		baseSql.append("FROM dc_reserve_list a1 LEFT JOIN dc_reserve_list a2 ON a1.user_id = a2.user_id");
		baseSql.append(DateUtils.getPlanMaxDay(0),"AND a2.ymd = ?");
		baseSql.append(DateUtils.getPlanMaxDay(-7),"AND a2.state = 0 WHERE a1.ymd > ?");
		baseSql.append("AND a1.state = 0 AND a2.user_id IS NULL");
		String baseSQL = baseSql.toFullSql();
		if(type==0) {
			EasySQL sql = new EasySQL();
			sql.append("SELECT DISTINCT a1.user_id");
			sql.append(baseSQL);
			try {
				List<JSONObject> list = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
				if(list!=null) {
					for(JSONObject row:list) {
						MessageModel model = new MessageModel();
						String userId = row.getString("USER_ID");
						String openId = StaffService.getService().getStaffInfo(userId).getOpenId();
						model.setReceiver(userId);
						model.setOpenId(openId);
						model.setTitle("您今天还没点午餐");
						model.setData1("您今天还没点午餐");
						model.setData2(EasyDate.getCurrentDateString());
						model.setUrl("https://work.yunqu-info.cn");
					    WxMsgService.getService().sendRemindNoticeMsg(model);
					}
				 }
				}catch (SQLException e) {
				   this.getLogger().error(e.getMessage(),e);
			 }
		}
		
		EasySQL sql2 = new EasySQL();
		sql2.append("SELECT GROUP_CONCAT(DISTINCT a1.user_id)");
		sql2.append(baseSQL);
		try {
			String userIds = this.getQuery().queryForString(sql2.getSQL(), sql2.getParams());
			String userNames = StaffService.getService().getUserName(userIds);
			WeChatWebhookSender.sendMarkdownMessage(WebhookKey.HR,"在过去的7天内有过点餐记录，但今天未进行点餐的人员,"+processString(userNames));
		} catch (SQLException e) {
			this.getLogger().error(e.getMessage(),e);
		}
	}
	
	private static String processString(String input) {
		StringBuilder result = new StringBuilder();
		if(StringUtils.notBlank(input)) {
			String[] parts = input.split(",");
			for (int i = 0; i < parts.length; i++) {
				result.append("@").append(parts[i].trim());
				if (i < parts.length - 1) {
					result.append(",");
				}
			}
		}
	    return result.toString();
	}
}
