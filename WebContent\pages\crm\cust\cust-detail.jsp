<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
	
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="custDetailForm">
			    <input type="hidden" value="${param.custId}" id="custId" name="custId"/>
		  	    <input type="hidden" value="${param.custId}" name="fkId"/>
				<div class="ibox-content" style="padding: 5px;" data-mars="CustDao.record" data-mars-prefix="cust.">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md6">
					     <i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 18px;" name='cust.CUST_NAME'></span>
					  </div>
					  <div class="layui-col-md2">&nbsp;</div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm">
									<button class="btn btn-default btn-xs" onclick="editCust();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding:10px 15px;">
					  <div class="layui-col-md2">
					  	<span class="h-title">客户级别</span>
					  	<span class="h-value" name="cust.CUST_LEVEL"></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">客户状态</span>
					  	<span class="h-value" name="cust.CUST_STATE" data-match="{10:'初步接洽',20:'需求讨论',30:'方案/报价',40:'合同审核',50:'成交客户',60:'流失客户'}"></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">负责人</span>
					  	<span class="h-value" name="cust.OWNER_ID" data-fn="getUserName">重要</span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">所属行业</span>
					  	<span class="h-value" name="cust.INDUSTRY"></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">更新时间</span>
					  	<span class="h-value" name="cust.UPDATE_TIME"></span>
					  </div>
					</div>
				</div>
				<div class="layui-row">
					<div class="layui-col-md9">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">基本信息</li>
						    <li>联系人<span id="contactsCount"></span></li>
						    <li>团队成员<span id="teamCount"></span></li>
						    <li>商机<span id="businessCount"></span></li>
						    <li>评审<span id="reviewCount"></span></li>
						    <li>合同列表<span id="contractCount"></span></li>
						    <li>合同采购<span id="orderCount"></span></li>
						    <li>发票<span id="invoiceCount"></span></li>
						    <li>附件<span id="fileCount"></span></li>
						    <li>运营 <span id="operateCount"></span></li>
							<li>操作记录</li>
						  </ul>
						  <div class="layui-tab-content" style="margin:0px;background-color: #fff;min-height: calc( 100vh - 180px)">
						    <div class="layui-tab-item layui-show">
						    	<jsp:include page="../include/base-info.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="../include/contacts.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="../include/team.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="../include/business.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	 <table id="reviewList" class="layui-table"></table>
						    </div>
						    <div class="layui-tab-item">
						    	 <table id="contractList" class="layui-table"></table>
						    </div>
						    <div class="layui-tab-item">
						    	 <table id="buyGoodsList" class="layui-table"></table>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="../include/invoice-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="../include/cust-files.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="../include/operate-info.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						  </div>
					</div>
					</div>
					<div class="layui-col-md3">
						<div class="layui-card" style="margin: 10px 0px 15px 5px;border: 1px solid #eee;">
						  <div class="layui-card-header" style="background-color: #fff;border-bottom: 1px solid #eee;">动态
								<div class="btn-group btn-group-xs pull-right mt-10">
									 <button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">+新建</button>
									 <ul class="dropdown-menu dropdown-menu-right">
									    <li><a href="javascript:;" onclick="addFollow('1')">售前跟进</a></li>
									    <li><a href="javascript:;" onclick="addFollow('2')">销售跟进</a></li>
									</ul>
								</div>
						</div>
						  <div class="layui-card-body followList" style="height: calc( 100vh - 180px);overflow-y: scroll;">
							    <jsp:include page="../include/follow-list.jsp"/>
						  </div>
						</div>
					</div>
				</div>
		</form>
		<form id="custFileForm" enctype="multipart/form-data"  method="post">
  			 <input style="display: none;" name="file" type="file" id="custLocalfile" onchange="uploadFile();"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script src="http://api.map.baidu.com/api?v=2.0&ak=EZPCgQ6zGu6hZSmXlRrUMTpr"></script>
	<script type="text/javascript" src="${ctxPath}/pages/crm/staitc/table.js"></script>
	<script type="text/javascript" src="${ctxPath}/pages/crm/staitc/mgr.js"></script>
	<script type="text/javascript">
   
		jQuery.namespace("Cust");
	    
		Cust.custId='${param.custId}';
		
		var custId='${param.custId}';
		
		$(function(){
			$("#custDetailForm").render({success:function(result){
				
			}});
			
		});
		
		function openDownloadLog(){
			downloadLogLayer(custId);
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>