<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">入职日期</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.joinDate}" readonly="readonly" class="form-control input-sm" name="extend.f1"/>
					  			</td>
					  			<td class="required">岗位</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.post}" readonly="readonly" class="form-control input-sm" name="extend.f2"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 140px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}误餐补助申请"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 140px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  		<!-- 	<td class="required">员工类别</td>
					  			<td>
					  				<select data-rules="required" class="form-control input-sm" name="apply.data1">
					  					<option value="">请选择</option>
					  					<option value="销售人员">销售人员</option>
					  					<option value="北京分部">北京分部</option>
					  					<option value="北京人力行政">北京人力行政</option>
					  				</select>
					  			</td> -->
					  			<td class="required">申请月份</td>
					  			<td>
					  				<input type="text" data-rules="required" data-mars="CommonDao.nowMonthId" readonly="readonly" class="form-control input-sm Wdate" name="apply.data2"/>
					  			</td>
					  			<td class="required">当月应发餐补天数</td>
					  			<td>
					  				<input type="hidden" data-mars="FlowCommon.monthWorkDay2" id="otherDay" class="form-control input-sm"/>
					  				<input type="number" data-rules="required" data-mars="FlowCommon.monthWorkDay" readonly="readonly" id="monthCbDay" onchange="calcDay();" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">请假天数</td>
					  			<td>
					  				<input type="number" data-rules="required"  onchange="calcDay();" class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  			<td class="required">当月出差天数</td>
					  			<td>
					  				<input type="number" data-rules="required"  onchange="calcDay();" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">当月实发餐补天数</td>
					  			<td>
					  				<input type="number" data-rules="required" placeholder="应发餐补天数减去出差天数减去请假天数"  class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  			<td>出差说明</td>
					  			<td>
					  				<input type="text" placeholder="往返日期/出发城市/出发城市"  class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  		</tr>
					  		<tr class="unedit-remove">
					  			<td>关联流程</td>
					  			<td colspan="3">
					  				<input type="hidden" name="apply.relatedFlow"/>
					  				<input type="text" data-flow-code="ha_leave,ha_travel" readonly="readonly" placeholder="关联请假或出差流程" onclick="FlowCore.selectRelatedFlow(this,'checkbox');" class="form-control input-sm"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>备注</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr class="remindInfo hidden-print">
					  			<td>注意事项</td>
					  			<td colspan="3">
					  				<small>
					  				1.外区销售、外区开发人员（开发中心、新产品创新中心）、北京人力行政，工程中心、解决方案中心、北京技术中心，请按实际情况按月填写本表。<br>
									2.当月驻地餐补，须于次月1号前填写。
					  				</small>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(data){
				try{
					var param = FlowCore.params;
					if(param.handle=='edit'||param.handle=='add'){
						var staffInfo = getStaffInfo();
						var deptName = staffInfo.deptName;
						if(deptName.indexOf('工程')>-1||deptName.indexOf('合同')>-1){
							var otherDay = $('#otherDay').val();
							$('#monthCbDay').val(otherDay);
						}
					}
				}catch(e){
					console.error(e);
				}
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				
			}});
		}
		
		function calcDay(){
			var v1 = $("[name='apply.data3']").val();
			var v2 = $("[name='apply.data5']").val();
			var v3 = $("[name='apply.data7']").val();
			$("[name='apply.data4']").val(v1-v2-v3);;
		}

		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>