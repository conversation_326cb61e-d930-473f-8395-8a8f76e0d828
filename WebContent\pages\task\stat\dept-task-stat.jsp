<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>任务管理</title>
	<style>
		.layui-badge{left: -1px!important;}
	</style>
	<style>
		
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		      <h5>部门负责的任务统计</h5>
	          		      <EasyTag:hasRole roleId="TASK_MANAGER">
		          		      <div class="input-group input-group-sm" style="width:150px;">
								 <span class="input-group-addon">部门</span>	
								 <select name="deptId" data-mars="EhrDao.allDept" data-mars-reload="false" onchange="list.query()" class="form-control input-sm">
			                    		<option value="">请选择</option>
			                   	</select>
						     </div>
	          		      </EasyTag:hasRole>
					      <div class="input-group input-group-sm" style="width: 300px">
							 <span class="input-group-addon">执行时间</span>	
	                     	 <input style="border-right: 0;" type="text" data-laydate="{type:'date',range: '到'}" data-mars-reload="false" name="planDate" data-mars="CommonDao.currentMonthRange" class="form-control input-sm">
	                     	 <div class="input-group-btn">
	                     		<select class="form-control input-sm" style="width:70px;" onchange="getDate(this,'planDate')">
		                     		<option value="">本月</option>
		                     		<option value="today">今日</option>
		                     		<option value="yesterday">昨天</option>
		                     		<option value="threeDay">三天内</option>
		                     		<option value="oneWeek">一周内</option>
		                     		<option value="oneMonth">一个月内</option>
		                     		<option value="threeMonth">三个月内</option>
		                     	</select>
	                     	 </div>
                     	  </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
<script type="text/javascript">
		var taskTable= {
				mars:'TaskDao.deptTaskStat',
				limit:100,
				page:false,
				limits:[10,15,30,50,100,200,300],
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'center'
				 },{
				    field: 'USERNAME',
					title: '姓名',
					align:'center'
				},{
				    field: 'F',
					title: '总任务数',
					align:'center',
					sort:true
				},{
				    field: 'A',
					title: '待办数',
					align:'center',
					sort:true
				}
				 ,{
				    field: 'B',
					title: '进行中',
					sort:true,
					align:'center'
				},{
				    field: 'E',
					title: '超时未完成',
					sort:true,
					align:'center'
				}
			  ,{
				   field: 'C',
				   title: '已完成',
				   sort:true,
				   align:'center'
			},{
				   field: 'D',
				   title: '已验收',
				   sort:true,
				   align:'center'
				}
			]]};
			var list={
			 init:function(){
				$("#searchForm").initTable($.extend(taskTable,{id:'list'},{done:function(result){

				}}));
			},
			detail:function(data){
				projectDetailByRow(data);
			},
			query:function(){
				$("#searchForm").queryData({id:'list'});
			},
		}
		var getDate = function(obj,inputName){
			var val = obj.value;
			var bdate = new Date();
			var edate = new Date();
			if('createTime'==inputName||'planDate'==inputName){
				if(val == 'today'){
					
				}else if(val == 'yesterday'){
					bdate.setDate(bdate.getDate() - 1);
					edate = bdate;
				}else if(val == 'threeDay'){
					bdate.setDate(bdate.getDate() - 3);
				}else if(val == 'oneWeek'){
					bdate.setDate(bdate.getDate() - 7);
				}else if(val == 'oneMonth'){
					bdate.setMonth(bdate.getMonth() - 1);
				}else if(val == 'threeMonth'){
					bdate.setMonth(bdate.getMonth() - 3);
				}else{
					bdate.setDate(1);
				}
				var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
				$('[name="'+inputName+'"]').val(v);
			}else{
				if(val){
					bdate.setDate(bdate.getDate() - val);
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}
			list.query();
		}
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>