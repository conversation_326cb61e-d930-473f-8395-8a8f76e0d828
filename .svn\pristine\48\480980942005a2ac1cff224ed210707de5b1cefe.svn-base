<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<style>
 	#followFileList a{background-color: #f8f8f8;border: 1px solid #ccc;padding: 3px 5px;margin: 3px;display: inline-block;}
</style>
<div class="container-fluid">
   <table class="table table-edit table-vzebra"  id="addFollow" data-mars="ResumeDao.followInfo" data-mars-prefix="follow.">
       <tbody>
	       	<c:choose>
	       	  <c:when test="${param.followType=='1'}">
	       	  		   <tr>
			            	<td  width="100px" class="required">安排面试日期</td>
			            	<td>
			            		<input name="follow.FOLLOW_DATE" data-mars="CommonDao.today" type="date" style="display: inline-block;width: 40%;" data-rules="required" class="form-control">
			            		<input name="follow.FOLLOW_TIME" type="time" style="display: inline-block;width: 40%;" data-rules="required" class="form-control">
			            		<input name="follow.FOLLOW_LABEL" type="hidden" value="面试安排"  class="form-control">
			            	</td>
			           </tr>
	       	  </c:when>
	       		<c:otherwise>
	       			<tr>
		            	<td class="required">跟进日期</td>
		            	<td>
		            		<input name="follow.FOLLOW_DATE" data-mars="CommonDao.today" type="date" data-rules="required" class="form-control">
		            	</td>
		           </tr>
	       		  <tr>
		           		<td class="required" width="100px">跟进结果</td>
		                <td>
		                	<select name="follow.FOLLOW_LABEL" class="form-control">
		                    	<option value="">请选择</option>
		              		 	<option value="邀约成功">邀约成功</option>
		              		 	<option value="邀约失败">邀约失败</option>
		              		 	<option value="面试安排">面试安排</option>
		              		 	<option value="一面成功">一面成功</option>
		              		 	<option value="一面失败">一面失败 </option>
		              		 	<option value="面试不合格">面试不合格</option>
		              		 	<option value="面试通过">面试通过</option>
		              		 	<option value="发放offer">发放offer</option>
		              		 	<option value="接受offer">接受offer</option>
		              		 	<option value="拒绝offer">拒绝offer</option>
		              		 	<option value="入职成功">入职成功</option>
		                    </select>
		                 </td>
          			 </tr>
	       		</c:otherwise>
	       	</c:choose>
            <tr>
            	<td>备注内容</td>
            	<td>
            		<input name="follow.FOLLOW_TYPE" value="${param.followType}" type="hidden"/>
            		<input name="follow.RESUME_ID" value="${param.resumeId}" type="hidden"/>
                	<input name="follow.FILE_ID" value="${param.fileId}" type="hidden"/>
                	<input name="follow.FILE_NAME" value="${param.fileName}" type="hidden"/>
                	<input name="followId" value="${param.followId}" type="hidden"/>
                	<input name="follow.FOLLOW_ID" value="${param.followId}" type="hidden"/>
            		<textarea name="follow.FOLLOW_CONTENT" style="height: 150px"  class="form-control"></textarea>
            	</td>
            </tr>
       </tbody>
	</table>
    <p class="layer-foot text-c">
   	   <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="FollowEit.ajaxSubmitForm()"> 保 存 </button>
       <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
	</p>
</div>
<script>

	$(function(){
		$("#addFollow textarea").keydown(function(event) {
			  if(event.keyCode == "13") {
				 var height = $(this).height();
				 $(this).css('height',(height+20)+'px');
			  }
			   if(event.keyCode == 8) {
				 var height = $(this).height();
				 height=height>120?height:120;
				 $(this).css('height',(height-20)+'px');
			   }
		});
		$("#addFollow").render({success:function(result){

		}});
	});
	
	var FollowEit ={
		followId:'${param.followId}',
		ajaxSubmitForm:function(){
			if(form.validate("#addFollow")){
				if(FollowEit.followId){
					FollowEit.updateData(); 
				}else{
					FollowEit.insertData(); 
				}
			};
			
		},
		insertData:function(){
			var data = form.getJSONObject("#addFollow");
			ajax.remoteCall("${ctxPath}/servlet/resume?action=addFollow",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('addFollow');
						reloadFollow();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		},
		updateData:function(){
			var data = form.getJSONObject("#addFollow");
			ajax.remoteCall("${ctxPath}/servlet/resume?action=updateFollow",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						popup.layerClose('addFollow');
						reloadFollow();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	
	}

</script>
