package com.yunqu.work.base;

public class FlowConstants {
	
	/**
	 * 默认审批 0
	 */
	public static int APPROVE_TYPE_DEFAULT = 0;
	
	/**
	 * 协办 1
	 */
	public static int APPROVE_TYPE_ASSIST = 1;
	
	/**
	 * 转办(委托) 2
	 */
	public static int APPROVE_TYPE_TRUST = 2;
	
	
	
	/**
	 * 草稿
	 */
	public static int FLOW_STAT_DRAFT = 0;
	
	/**
	 * 作废
	 */
	public static int FLOW_STAT_CANNEL = 1;
	
	/**
	 * 挂起
	 */
	public static int FLOW_STAT_HANG_UP = 5;
	
	/**
	 * 待审批
	 */
	public static int FLOW_STAT_TODO = 10;
	
	/**
	 * 审批中
	 */
	public static int FLOW_STAT_CHECKING = 20;
	
	/**
	 * 审批退回
	 */
	public static int FLOW_STAT_CHECK_RETURN = 21;
	
	/**
	 * 审批完成
	 */
	public static int FLOW_STAT_CHECK_FINISH = 30;
	
	/**
	 * 审批人：自己选择
	 */
	public static int FLOW_APPROVER_SELECT_USER = 30;
	
	
	

}
