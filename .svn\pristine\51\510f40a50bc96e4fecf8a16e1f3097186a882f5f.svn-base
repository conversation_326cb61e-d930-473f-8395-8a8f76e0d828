<%@ include file="/pages/common/global.jsp" %> 
<%@ page language="java" contentType="text/html;charset=UTF-8"%> 
<EasyTag:override name="head"> 
    <title>项目工时填报</title> 
    <style> 
        tr .layui-btn{opacity:0;} 
        tr:hover .layui-btn{opacity:1;} 
        .layui-table td, .layui-table th{padding: 9px 6px;} 
        .layui-table input{width: 100%!important;} 
    </style> 
</EasyTag:override> 
<EasyTag:override name="content"> 
        <form  autocomplete="off"  onsubmit="return false;" class="form-inline" <c:if test="${param.opType=='detail'}">data-text-model="true"</c:if> id="editForm"> 
               <input type="hidden" name="userId" value="${param.userId}"/> 
               <input type="hidden" name="monthId" value="${param.monthId}"/> 
                <div class="ibox"> 
                      <table class="layui-table"> 
                          <thead> 
                            <tr> 
                              <th>项目名称(每个项目一行)</th> 
                              <th>人/天</th> 
                              <th>是否驻场</th> 
                              <th>工作完成概括</th> 
                              <th><a class="btn btn-sm btn-link add-btn" onclick="addTr();" href="javascript:;">+添加</a></th> 
                            </tr>  
                          </thead> 
                            
                          <tbody data-mars="WorkHourDao.userWorkHourList" data-template="whListTpl" data-container="whContainer"  id="whContainer"></tbody> 
                            
                          <tbody  id="tbodyUserId"> 
                            <c:forEach var="item" begin="500000" end="500030"> 
                                <tr class="tr_${item}" <c:if test="${item>=500001||param.state!='0'}">style="display:none;" data-hide-tr="1"</c:if>> 
                                   <td> 
                                        <input type="hidden" name="projectId_${item}" value=""/> 
                                        <input name="projectName_${item}"  data-rules="required" placeholder="点击此选择" readonly="readonly" id="projectName_${item}" onclick="singleProject(this);" style="min-width: 150px;" type="text" class="form-control projectId"/> 
                                        <input type="hidden" name="itemId" value="${item}"/> 
                                    </td> 
                                   <td style="width:110px"> 
                                        <input style="width:80px;min-width: 80px;" type="number"  data-rules="required" name="time_${item}"  placeholder="耗时天数"  onblur="if(this.value>100)this.value=100;if(this.value<=0)this.value=1;" type="text" class="form-control"> 
                                   </td> 
                                   <td style="width:90px"> 
                                        <select  name="resident_${item}"  class="form-control"> 
                                        	<option value="否">否</option>
                                        	<option value="是">是</option>
                                        </select>
                                   </td> 
                                   <td> 
                                        <textarea name="result_${item}" placeholder="建议200字以内"  onclick="textareaEditLayer(this);" maxlength="500" style="width: 100%;min-width: 160px;" data-rules="required" class="form-control"></textarea>   
                                   </td> 
                                   <td  style="width:30px;"> 
                                    <a class="btn btn-xs btn-link" href="javascript:;" onclick="$('.tr_${item}').remove();">删除</a> 
                                   </td> 
                                </tr> 
                            </c:forEach> 
                          </tbody> 
                      </table> 
                  </div> 
                 <div class="layer-foot text-c"> 
                    <button type="button" class="btn btn-sm btn-info" onclick="req.ajaxSubmitForm()">保存</button> 
                 </div> 
        </form> 
        <script type="text/x-jsrender" id="whListTpl"> 
             {{for data}} 
                <tr class="ntr_{{:ITEM_ID}}"> 
                    <td style="width:450px"> 
                      <input type="hidden" name="projectId_{{:ITEM_ID}}" value="{{:PROJECT_ID}}"/> 
                      <input value="{{:PROJECT_NAME}}" ts-selected="{{:PROJECT_ID}}" readonly="readonly" data-rules="required" name="projectName_{{:ITEM_ID}}" id="projectName_{{:ITEM_ID}}" onclick="singleProject(this);" type="text" class="form-control projectId"/> 
                      <input type="hidden" name="itemId" value="{{:ITEM_ID}}"/> 
                      <input type="hidden" name="whId_{{:ITEM_ID}}" value="{{:WH_ID}}"/> 
                    </td> 
                    <td style="width:110px"> 
                        <input style="width:80px;" type="number"  data-rules="required" value="{{:WORK_TIME}}" placeholder="耗时天数"  onblur="if(this.value>100)this.value=100;if(this.value<=0)this.value=1;" name="time_{{:ITEM_ID}}" type="text" class="form-control"> 
                    </td> 
					<td style="width:90px"> 
                         <select  name="resident_{{:ITEM_ID}}" data-value="{{:RESIDENT}}" class="form-control"> 
                            <option value="否">否</option>
                            <option value="是">是</option>
                         </select>
                    </td> 
                    <td> 
                        <textarea name="result_{{:ITEM_ID}}" data-rules="required"  onclick="textareaEditLayer(this);" style="width: 100%;" class="form-control">{{:WORK_RESULT}}</textarea>        
                    </td> 
                    <td style="width:30px;"><a class="btn btn-xs btn-link del-btn" href="javascript:;" onclick="delData('{{:ITEM_ID}}');">删除</a></td> 
                </tr> 
            {{/for}} 
        </script> 
</EasyTag:override> 
<EasyTag:override name="script"> 
<script type="text/javascript"> 
          
  
        function loadData(){ 
            $("#editForm").render({success:function(){ 
                renderDate(); 
              
                $("[data-value]").each(function(){
                	var val = $(this).data('value');
                	$(this).val(val);
                });
                  
                var opType = '${param.opType}'; 
                if(opType=='detail'){ 
                    $("#editForm").editform(); 
                    $('.add-btn,.del-btn,.layer-foot').remove(); 
                      
                } 
                
                var device = layui.device();
    			if(device.mobile){
    				$('#editForm .layui-table').addClass('table-responsive');
    				$('#editForm .layui-table').parent().addClass('table-responsive');
    				$('#editForm .layui-table').addClass('text-nowrap');
    			}
    			
            }}); 
        } 
          
        $(function(){ 
            loadData(); 
        }); 
          
        var req={}; 
        req.ajaxSubmitForm = function(){ 
            $("[data-hide-tr]").remove(); 
            if(form.validate("#editForm")){ 
                req.insertData();  
            } 
        } 
        req.insertData = function() { 
            var times=$("[name^='time_']"); 
            var sumTime=0; 
            if(times.length>0){ 
                times.each(function(){ 
                    var time=$(this).val(); 
                    if(time){ 
                      sumTime+=parseFloat(time); 
                    } 
                }); 
            } 
            if(sumTime>0&&sumTime>31){ 
                layer.msg("填报的总工作量是"+sumTime+",不能大于31天"); 
                return false; 
            } 
            layer.confirm("月总工时为"+sumTime+"天，确认提交吗<br>请认真核对工时和内容【非常重要】<br>如若填错,请及时修改调整",{icon:3,offset:'50px'},function(){ 
                var data = form.getJSONObject("#editForm"); 
                ajax.remoteCall("${ctxPath}/servlet/project?action=addProjectWorkHour",data,function(result) {  
                    if(result.state == 1){ 
                        layer.msg(result.msg,{icon:1,time:1200},function(){ 
                            layer.closeAll(); 
                            list.query(); 
                        }); 
                    }else{ 
                        layer.alert(result.msg,{icon: 5}); 
                    } 
                }); 
            }); 
              
        } 
        function addTr(){ 
            var obj  =$("#tbodyUserId").find("[data-hide-tr]").first(); 
            obj.removeAttr("data-hide-tr"); 
            obj.show() 
        } 
          
        function delData(itemId){ 
            ajax.remoteCall("${ctxPath}/servlet/project?action=delProjectWorkHour",{itemId:itemId},function(result) {  
                if(result.state == 1){ 
                    layer.msg(result.msg,{icon:1,time:1200},function(){ 
                        $('.ntr_'+itemId).remove(); 
                    }); 
                }else{ 
                    layer.alert(result.msg,{icon: 5}); 
                } 
            }); 
        } 
          
</script> 
</EasyTag:override> 
<%@ include file="/pages/common/layout_div.jsp" %>