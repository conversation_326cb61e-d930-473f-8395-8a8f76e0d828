package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

/**
 * <AUTHOR>
 */
@WebObject(name = "IncomeStageDao")
public class IncomeStageDao extends AppDaoContext {

    @WebControl(name = "incomeStageList", type = Types.LIST)
    public JSONObject incomeStageList() {
        EasySQL sql = getEasySQL("select t1.* from yq_contract_income_stage t1");
        sql.append("where 1=1");
        sql.append(param.getString("contractId"), " and t1.CONTRACT_ID = ?");
        sql.append(param.getString("stageName"), " and t1.STAGE_NAME like ?");

        if (!isSuperUser() && !hasRole("CONTRACT_MGR") && !hasRes("CONTRACT_LOOK_AUTH")) {
            sql.append("and (");
            sql.append(getUserId(), "(t1.CREATOR = ?)");
            sql.append("or");
            sql.append(getUserId(), "(t1.UPDATE_BY = ?)");
            sql.append(")");
        }
        setOrderBy(sql, " order by t1.CREATE_TIME ");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "record", type = Types.RECORD)
    public JSONObject record() {
        String incomeStageId = param.getString("incomeStage.INCOME_STAGE_ID");
        if (StringUtils.notBlank(incomeStageId)) {
            return queryForRecord("select * from yq_contract_income_stage where INCOME_STAGE_ID = ?", incomeStageId);
        } else {
            return getJsonResult(new JSONObject());
        }
    }

    @WebControl(name = "selectAllIncomeStage", type = Types.DICT)
    public JSONObject selectAllIncomeStage() {
        String contractId = param.getString("contractId");
        EasySQL sql = new EasySQL("select INCOME_STAGE_ID, CONCAT(PLAN_COMP_DATE,'-',STAGE_NAME, '-比例:',RATIO,' % -计划金额: ', PLAN_AMOUNT) AS STAGE_DESC from yq_contract_income_stage where 1=1");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        sql.append(contractId, "and CONTRACT_ID = ?");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "selectIncomeStage", type = Types.DICT)
    public JSONObject selectIncomeStage() {
        String contractId = param.getString("contractId");
        if (StringUtils.isBlank(contractId)) {
            contractId = this.getMethodParam(0).toString();
        }
        String confirmType = param.getString("confirmType");
        EasySQL sql = new EasySQL("select INCOME_STAGE_ID, CONCAT(PLAN_COMP_DATE,'-',STAGE_NAME, '-比例:',RATIO,' % -计划金额: ', PLAN_AMOUNT ");
        if ("A".equals(confirmType)) {
            sql.append(",'-未确认金额A：',UNCONFIRM_AMOUNT_A");
        } else if ("B".equals(confirmType)) {
            sql.append(",'-未确认金额B：',UNCONFIRM_AMOUNT_B");
        }
        sql.append(") AS STAGE_DESC from yq_contract_income_stage t1 where 1=1");
        sql.append(contractId, "and CONTRACT_ID = ?");
        return getDictByQuery(sql.getSQL(), sql.getParams());
    }


    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by t1.").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }
}
