<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>
		.layui-table-cell{padding: 0 6px;}
		.fa-filter{color: #00abfc;cursor: pointer;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="weeklyForm" data-page-hide="true">
			<div class="ibox">
					<div class="ibox-content">
				    	<div class="input-group input-group-sm">
						    <span class="input-group-addon">状态</span>	
							<select name="status" onchange="list.query();" class="form-control input-sm">
							      <option value="">请选择 </option>
                    			  <option value="">全部</option>
                    			  <option value="1">未读</option>
                    			  <option value="2">已读</option>
						    </select>									  
					  	</div>
					  	<div class="input-group input-group-sm">
						    <span class="input-group-addon">姓名</span>	
					    	<input type="text" style="width: 80px" id="userName" name="userName" class="form-control input-sm mb-10" placeholder="姓名"/>
						 </div>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">部门</span>	
						    	<input type="text" style="width: 80px;" name="depts" class="form-control input-sm mb-10" placeholder="部门"/>
						 </div>
						 <div class="input-group input-group-sm" style="width: 310px">
	          		    	<span class="input-group-addon">工作日期</span>	
                     		<input data-mars="CommonDao.monthRange" data-mars-reload="false" style="border-right: 0;" type="text" data-laydate="{type:'date',range: '到'}" name="updateDate" class="form-control input-sm">
                     		<div class="input-group-btn">
	                     		<select class="form-control input-sm" style="width:80px;" onchange="getDate(this,'updateDate')">
		                     		<option value="">全部</option>
		                     		<option value="today">今日</option>
		                     		<option value="yesterday">昨天</option>
		                     		<option selected="selected" value="currentMonth">本月</option>
		                     		<option value="threeDay">三天内</option>
		                     		<option value="oneWeek">一周内</option>
		                     		<option value="oneMonth">一个月内</option>
		                     		<option value="threeMonth">三个月内</option>
		                     	</select>
                     	   </div>
                    	 </div>
				    	<button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
				    	<button type="reset" class="btn btn-sm btn-default"> 清空 </button>
					</div>
					<div class="ibox-content mt-10">
					  	<table class="layui-hide mt-15" id="recentListTable"></table> 
					</div>
				</div>
		</form>
	
		<script type="text/x-jsrender" id="bar">
			{{if RECEIVER == currentUserId && STATUS == 1}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">审批</a>
			{{else}}
				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看</a>
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
	<script type="text/javascript">
		
		layui.use('element', function(){
			  var element = layui.element;
			  element.render();
		});

		var list={
			recentList:function(){
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.myReceiveWeeklyList',
					id:'recentListTable',
					height:'full-180',
					cellMinWidth:100,
					limit:30,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
						 field:'WEEK_NO',
						 title:'周数',
						 width:80,
						 templet:'<div>{{d.YEAR}}/{{d.WEEK_NO}}</div>'
					 },{
					    field: 'CREATE_NAME',
						title: '填写人',
						align:'left',
						width:160,
						templet:function(row){
							return row.DEPTS+"."+row['CREATE_NAME']+'<a href="javascript:getThisUserData(\''+row['CREATE_NAME']+'\')" class="pull-right"><i class="fa fa-filter" aria-hidden="true"></i></a>';
						}
					   }
					 ,{
					    field: 'TITLE',
						title: '标题',
						minWidth:250,
						align:'left',
						event:'list.detail',
						style:'color:#1E9FFF;cursor:pointer',
						templet:function(row){
							if(row.TYPE=='1'){
								return row.TITLE;
							}else{
								return "<span class='layui-badge-rim'>抄送</span>&nbsp;"+row.TITLE;
							}
						}
					},{
						title: '工作量',
						field:'ITEM_5',
						align:'left',
						width:90,
					 }
					 ,{
						    field: 'RECEIVER',
							title: '审批人/抄送人',
							align:'left',
							width:140,
							templet:function(row){
								if(row.TYPE=='1'){
									return getUserName(row.RECEIVER);
								}else{
									return row.DEPTS+"."+row.USERNAME;
								}
							}
						 }
					,{
					    field: 'SEND_TIME',
						title: '收到时间',
						align:'left',
						width:200,
						templet:function(row){
							if(getCurrentUserId()==row.RECEIVER||isSuperUser){
								var status=row.STATUS;
								if(status==2){
									status='<span class="layui-badge layui-bg-green">已阅</span> ';
								}else{
									status='<span class="layui-badge">待阅</span> ';
								}
								return status+row.SEND_TIME;
							}else{
								return row.SEND_TIME;
							}
						}
					},{
						title: '操作',
						align:'left',
						width:90,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]],done:function(result){
						$("#num").text(result['totalRow']);
						
					}}
				);
			},
			query:function(){
				$("#weeklyForm").queryData({id:'recentListTable',jumpOne:true});
			},
			detail:function(data){
				var width=$(window).width();
				var fullFlag=false;
				var width ='850px';
				if(width<700){
					fullFlag=true;
				}
				if(width>1900){
					width='70%';
				}
				var weeklyId = data;
				if(data.WEEKLY_ID){
					weeklyId=data.WEEKLY_ID;
				}
				popup.layerShow({id:'weeklyDetail',type:1,full:fullFlag,shade: 0.1,shadeClose:true,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/weekly/weekly-detail.jsp',title:'周报详情',data:{weeklyId:weeklyId}});
			}
		}
		
		function getThisUserData(name){
			$('#userName').val(name);
			list.query();
		}
		
		$(function(){
			$("#weeklyForm").render({success:function(){
				list.recentList();
			}});
		});
		
		var getDate = function(obj,inputName){
			var val = obj.value;
			var bdate = new Date();
			var edate = new Date();
			if('createTime'==inputName||'updateDate'==inputName){
				if(val == 'today'){
					
				}else if(val == 'yesterday'){
					bdate.setDate(bdate.getDate() - 1);
					edate = bdate;
				}else if(val == 'threeDay'){
					bdate.setDate(bdate.getDate() - 3);
				}else if(val == 'oneWeek'){
					bdate.setDate(bdate.getDate() - 7);
				}else if(val == 'oneMonth'){
					bdate.setMonth(bdate.getMonth() - 1);
				}else if(val == 'threeMonth'){
					bdate.setMonth(bdate.getMonth() - 3);
				}else if(val == 'currentMonth'){
					bdate.setDate(1);
					edate.setMonth(bdate.getMonth() + 1);
					edate.setDate(0);
				}
				if(val){
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}else{
				if(val){
					bdate.setDate(bdate.getDate() - val);
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}
			list.query();
		}
		
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>