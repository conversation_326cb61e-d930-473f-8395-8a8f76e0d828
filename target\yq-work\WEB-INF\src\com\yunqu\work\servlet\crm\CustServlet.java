package com.yunqu.work.servlet.crm;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.BusinessModel;
import com.yunqu.work.model.CustomerModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.OAContractSynDetailService;
import com.yunqu.work.service.OACustSynService;
import com.yunqu.work.service.OAUpdateContractIdService;
import com.yunqu.work.service.StaffService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.EntInfoUtils;
import com.yunqu.work.utils.WeChatWebhookSender;
import com.yunqu.work.utils.WebhookKey;

@WebServlet("/servlet/cust/*")
public class CustServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForSynCust(){
		OACustSynService.getService().synData();
		return EasyResult.ok();
	}
	public EasyResult actionForSynCustDetail(){
		OAContractSynDetailService.getService().synData();
		return EasyResult.ok();
	}
	
	public EasyResult actionForSynCustId(){
		OAUpdateContractIdService.getService().synData();
		return EasyResult.ok();
	}
	
	public EasyResult actionForQueryCompanyName(){
		String companyName = getJsonPara("companyName");
		if(StringUtils.notBlank(companyName)&&companyName.length()>1){
			return EntInfoUtils.queryEntList(companyName);
		}else{
			return EasyResult.fail();
		}
	}
	public EasyResult actionForQueryCompanyTax(){
		String taxNo = getJsonPara("taxNo");
		if(StringUtils.notBlank(taxNo)&&taxNo.length()>10){
			return EntInfoUtils.queryTax(taxNo);
		}else{
			return EasyResult.fail();
		}
	}

	public EasyResult actionForQueryCompanyTaxNum(){
		String custId = getJsonPara("custId");
		if(StringUtils.notBlank(custId)){
			String taxNum = "";
			try {
				taxNum = this.getQuery().queryForString("select tax_num from YQ_CRM_CUSTOMER where CUST_ID = ?", custId);
			} catch (SQLException e) {
				e.printStackTrace();
			}
			if(StringUtils.notBlank(taxNum)){
				return EasyResult.ok(taxNum);
			}
			return  EasyResult.fail("该客户没有记录纳税人识别号");
		}
		return EasyResult.fail("custId为空");
	}


	public EasyResult actionForAddCust(){
		CustomerModel model = getModel(CustomerModel.class, "cust");
		String custName = model.getString("CUST_NAME");
		boolean hasCustName;
		try {
			hasCustName = this.getQuery().queryForExist("select count(1) from YQ_CRM_CUSTOMER where CUST_NAME = ?", custName);
			if(hasCustName) {
				return EasyResult.fail(custName+"已存在。");
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		model.set("CREATOR", getUserId());
		model.set("OWNER_ID", getUserId());
		model.set("DEPT_ID", getDeptId());
		model.set("CUST_ID", RandomKit.uuid());
		try {
			model.save();
			
			WeChatWebhookSender.sendMarkdownMessage(WebhookKey.PRESLAES,getFullUserName()+"创建客户",custName);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateCust(){
		CustomerModel model = getModel(CustomerModel.class, "cust");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			model.set("UPDATE_BY", getUserId());
			model.update();
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForAddBusiness(){
		String id = RandomKit.uuid();
		BusinessModel model = getModel(BusinessModel.class, "business");
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_BY", getUserId());
		model.set("CREATOR", getUserId());
		model.set("CREATE_NAME", getUserName());
		model.set("DEPT_ID", getDeptId());
		model.setPrimaryValues(id);
		String sjName = model.getString("BUSINESS_NAME");
		try {
			boolean hasSjName = this.getQuery().queryForExist("select count(1) from YQ_CRM_BUSINESS where BUSINESS_NAME = ?", sjName);
			if(hasSjName) {
				return EasyResult.fail(sjName+"已存在。");
			}
			model.save();
			WeChatWebhookSender.sendMarkdownMessage(WebhookKey.PRESLAES,getFullUserName()+"创建商机",sjName);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(id);
	}
	
	public EasyResult actionForUpdateBusiness(){
		BusinessModel model = getModel(BusinessModel.class, "business");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			model.set("UPDATE_BY", getUserId());
			model.update();
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelCust(){
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddContacts(){
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateContacts(){
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelContacts(){
		return EasyResult.ok();
	}
	public EasyResult actionForAddTeamUser(){
		JSONObject params = getJSONObject();
		String custId = params.getString("custId");
		String auth = params.getString("auth");
		String htAuth = params.getString("htAuth");
		String sjAuth = params.getString("sjAuth");
		JSONArray userIds = params.getJSONArray("userIds");
		EasyRecord record = new EasyRecord("YQ_CRM_CUST_TEAM");
		for(int i=0;i<userIds.size();i++){
			record.set("user_id",userIds.getString(i));
			record.set("cust_id",custId);
			record.set("rw_auth",auth);
			record.set("sj_auth",sjAuth);
			record.set("ht_auth",htAuth);
			try {
				this.getQuery().save(record);
			} catch (SQLException e) {
				this.error(null, e);
				return EasyResult.fail("成员不能重复添加.");
			}
		}
		return EasyResult.ok(userIds);
	}
	public EasyResult actionForRemoveTeamUser(){
		JSONObject params = getJSONObject();
		String custId = params.getString("custId");
		String userId = params.getString("userId");
		try {
			this.getQuery().executeUpdate("delete from yq_crm_cust_team where cust_Id = ? and user_id = ?", custId,userId);
		} catch (SQLException e) {
			return EasyResult.fail();
		}
		return EasyResult.ok();
		
		
	}
	public EasyResult actionForAddFollow(){
		CustomerModel customerModel = new CustomerModel();
		EasyRecord record = new EasyRecord("YQ_CRM_FOLLOW");
		record.setColumns(getJSONObject("follow"));
		try {
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("CREATE_USER_NAME",getUserPrincipal().getUserName());
			record.set("DEPT_ID",getDeptId());
			record.set("CREATE_USER_ID",getUserId());
			record.set("FOLLOW_BY", getUserPrincipal().getNikeName());
			this.getQuery().save(record);
			
			String content = record.getString("CONTENT");
			
			customerModel.setPrimaryValues(record.getString("CUST_ID"));
			customerModel.set("LAST_FOLLOW_TIME", EasyDate.getCurrentDateString());
			customerModel.set("LAST_FOLLOW_CONTENT", content);
			customerModel.set("UPDATE_BY", getUserId());
			this.getQuery().update(customerModel);
			
			String contactsId = record.getString("CONTACTS_ID");
			if(StringUtils.notBlank(contactsId)) {
				this.getQuery().executeUpdate("update yq_crm_contacts set last_follow_time = ? where contacts_id = ?",record.getString("FOLLOW_END"),contactsId);
			}
			this.getQuery().executeUpdate("update yq_crm_follow t1 set t1.file_count = (select count(1) from yq_files t2 where t2.FK_ID=t1.follow_id) where t1.follow_id= ?",record.getString("FOLLOW_ID"));
			
			String businessId = record.getString("BUSINESS_ID");
			if(StringUtils.notBlank(businessId)) {
				this.getQuery().executeUpdate("update yq_crm_business t1 set t1.follow_day = ifnull((select sum(follow_day) from yq_crm_follow t2 where t2.business_id = t1.BUSINESS_ID),0) where t1.business_id = ?",businessId);
				this.getQuery().executeUpdate("update yq_crm_business t1 set t1.follow_count = ifnull((select count(1) from yq_crm_follow t2 where t2.business_id = t1.BUSINESS_ID),0) where t1.business_id = ?",businessId);
				
				String businessState = record.getString("BUSINESS_STATE");
				String businessResult = record.getString("BUSINESS_RESULT");
				
				if(StringUtils.notBlank(businessState)) {
					this.getQuery().executeUpdate("update yq_crm_business set business_stage  = ? where BUSINESS_ID = ?", businessState,businessId);
				}
				if(StringUtils.notBlank(businessResult)) {
					this.getQuery().executeUpdate("update yq_crm_business set BUSINESS_RESULT  = ?,business_result_date = ?,last_follow_time = ? where BUSINESS_ID = ?", businessResult,record.getString("FOLLOW_END"),EasyDate.getCurrentDateString(),businessId);
				}
			}
			
			//发送推送给销售并且抄送给主管
			String sjId = record.getString("BUSINESS_ID");
			if(StringUtils.notBlank(sjId)) {
				JSONObject sjInfo = this.getQuery().queryForRow("select * from yq_crm_business where business_id = ?",new Object[] {sjId},new JSONMapperImpl());	
				String sjName = record.getString("SJ_NAME");
				MessageModel model = new MessageModel();
				model.setTitle("【商机跟进】"+getFullUserName()+"跟进["+sjName+"]");
				model.setSender(getUserId());
				model.setCc(new String[] {StaffService.getService().getStaffInfo(getUserId()).getDeptLeader(),getUserId()});
				String salesBy = sjInfo.getString("SALES_BY");
				model.setReceiver(salesBy);
				if(StringUtils.notBlank(salesBy)) {
					StaffModel staffModel = StaffService.getService().getStaffInfo(salesBy);
					if(staffModel!=null&&staffModel.isNormal()) {
						model.setOpenId(staffModel.getOpenId());
						model.setDesc(content);
						model.setData1(model.getTitle());
						model.setData2(EasyDate.getCurrentDateString());
						model.setFkId(sjId);
						model.setUrl("https://work.yunqu-info.cn/yq-work/api/goUrl?url=/yq-work/sj/"+sjId);
						WeChatWebhookSender.sendMarkdownMessage(WebhookKey.PRESLAES,model.getTitle(),content);
						WxMsgService.getService().sendRemindNoticeMsg(model);
						content = content.replaceAll("\n", "<br>");
						content = content + "<br><br><a href=\"https://work.yunqu-info.cn/yq-work/sj/"+sjId+"\">查看详情<a>";
						model.setDesc(content);
						EmailService.getService().sendEmail(model);
					}
				}
			}
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateFollow(){
		EasyRecord record = new EasyRecord("YQ_CRM_FOLLOW","FOLLOW_ID");
		record.setColumns(getJSONObject("follow"));
		try {
			this.getQuery().update(record);
			this.getQuery().executeUpdate("update yq_crm_follow t1 set t1.file_count = (select count(1) from yq_files t2 where t2.FK_ID=t1.follow_id) where t1.follow_id= ?",record.getString("FOLLOW_ID"));
			
			String businessId = record.getString("BUSINESS_ID");
			if(StringUtils.notBlank(businessId)) {
				this.getQuery().executeUpdate("update yq_crm_business t1 set t1.follow_day = (select sum(follow_day) from yq_crm_follow t2 where t2.business_id = t1.BUSINESS_ID) where t1.business_id = ?",businessId);
				this.getQuery().executeUpdate("update yq_crm_business t1 set t1.follow_count = (select count(1) from yq_crm_follow t2 where t2.business_id = t1.BUSINESS_ID) where t1.business_id = ?",businessId);
				
				String businessState = record.getString("BUSINESS_STATE");
				String businessResult = record.getString("BUSINESS_RESULT");
				
				if(StringUtils.notBlank(businessState)) {
					this.getQuery().executeUpdate("update yq_crm_business set business_stage  = ? where BUSINESS_ID = ?", businessState,businessId);
				}
				if(StringUtils.notBlank(businessState)) {
					this.getQuery().executeUpdate("update yq_crm_business set BUSINESS_RESULT  = ? where BUSINESS_ID = ?", businessResult,businessId);
				}
			}
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	

}
