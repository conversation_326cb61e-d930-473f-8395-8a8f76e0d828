<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="orderPaymentTable"></table>
	<script type="text/javascript">
		$(function(){
			$("#OrderDetailForm").initTable({
				mars:'OrderFlowDao.paymentResult',
				id:'orderPaymentTable',
				page:false,
				height:300,
				cellMinWidth:80,
				cols: [[
				 {type:'numbers'},
				 {
				    field: 'CHECK_NAME',
					title: '评审人',
					align:'center',
					width:90
				 },
	             {
					 field:'CHECK_RESULT',
					 title:'评审结果',
					 align:'center',
					 width:90,
					 templet:function(row){
						 var checkResult = row['CHECK_RESULT'];
						 if(checkResult=='1'){
							 return '通过';
						 }else if(checkResult=='2'){
							 return '拒绝';
						 }else{
							 return '<span class="label label-warning">待审批</span>';
						 }
					 }
				 },{
				    field: 'CREATE_TIME',
					title: '发起时间',
					align:'center'
				},{
				    field: 'CHECK_TIME',
					title: '审批时间',
					align:'center'
				},{
					field:'CHECK_DESC',
					title:'评审描述',
					edit:'text'
				}
			]],done:function(data){
				$("#paymentCount").text(data.total);
		  	}
		});
	});
	
</script>
