<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>采购申请管理</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>采购评审明细</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">订单号</span>
						 	<input class="form-control input-sm" name="orderNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">销售合同号</span>
						 	<input class="form-control input-sm" name="contractNo">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					
					<div class="layui-tab layui-tab-brief">
						  <ul class="layui-tab-title">
						    <li class="layui-this">待评审列表(<span id="todoCount"></span>)</li>
						    <li>已评审列表(<span id="okCount"></span>)</li>
						  </ul>
						  <div class="layui-tab-content">
						  	 <div class="layui-tab-item layui-show">
								<table id="todoReview"></table>
						    </div>
						    <div class="layui-tab-item">
								<table id="finishReview"></table>
						    </div>
						  </div>
						</div>   
					</div>
				</div>
				
		   <script type="text/x-jsrender" id="bar">
			  {{if CHECK_RESULT=='0'}}
 				<a class="layui-btn layui-bg-orange layui-btn-xs" href="javascript:;">评审</a> {{else}} 
 				<a class="layui-btn layui-btn-primary layui-btn-xs" href="javascript:;">详情</a>
			  {{/if}}
		  </script>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			layui.use(['element','form'],function(){
				var element = layui.element;
			});
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
					initFinishList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.reviewDetailList',
					id:'todoReview',
					data:{todo:'1'},
					page:true,
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 field:'REVIEW_NO',
						 title:'评审编号',
						 width:100
					 },{
						 field:'ORDER_NO',
						 title:'订单号',
						 width:100
					 },{
						 field:'CONTRACT_NAME',
						 title:'标题',
						 minWidth:160
					 },{
						 field:'SUPPLIER_NAME',
						 title:'供应商',
						 minWidth:160
					 },{
						 field:'NODE_NAME',
						 width:90,
						 title:'当前节点'
					 },{
					    field: 'CHECK_NAME',
						title: '当前评审人',
						width:80,
						align:'left'
					},{
					    field: 'GET_TIME',
						title: '接收时间',
						align:'center'
					},{
					    field: 'CREATE_NAME',
						title: '发起人',
						width:100,
						align:'center'
					},{
					    field: 'TOTAL_PRICE',
						title: '总金额',
						width:100,
						align:'center'
					},{
						field:'',
						title:'操作',
						width:70,
						fixed:'right',
						event:'orderDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				]],done:function(data){
					$("#todoCount").text(data.totalRow);
			  	}
			});
		 }
			function initFinishList(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.reviewDetailList',
					id:'finishReview',
					data:{todo:'0'},
					page:true,
					height:'full-130',
					rowDoubleEvent:'orderDetail',
					cellMinWidth:100,
					cols: [[
					 {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 field:'REVIEW_NO',
						 title:'评审编号',
						 width:100
					 },{
						 field:'ORDER_NO',
						 title:'订单号',
						 width:100
					 },{
						 field:'CONTRACT_NAME',
						 title:'标题',
						 minWidth:160
					 },{
						 field:'SUPPLIER_NAME',
						 title:'供应商',
						 minWidth:160
					 },{
						 field:'NODE_NAME',
						 title:'当前节点'
					 },{
					    field: 'CHECK_NAME',
						title: '当前评审人',
						width:80,
						align:'left'
					},{
						field:'CHECK_RESULT',
						title:'评审状态',
						width:80,
						templet:function(row){
							var val = row['CHECK_RESULT'];
							if(val=='0'){
								return '<span class="label label-warning">待审批</span>';
							}else if(val=='1'){
								return '<span class="label label-success">同意</span>';
							}else if(val=='2'){
								return '<span class="label label-danger">拒绝</span>';
							}else{
								return '';
							}
						}
					},{
						field:'CHECK_DESC',
						title:'评审内容',
						edit:'text'
					},{
					    field: 'GET_TIME',
						title: '接收时间',
						align:'center'
					},{
					    field: 'CHECK_TIME',
						title: '评审时间',
						align:'center'
					},{
					    field: 'CREATE_NAME',
						title: '申请人',
						width:100,
						align:'center'
					},{
					    field: 'TOTAL_PRICE',
						title: '总金额',
						width:100,
						align:'center'
					},{
						field:'',
						title:'操作',
						width:70,
						fixed:'right',
						event:'orderDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					}
				]],done:function(data){
					$("#okCount").text(data.totalRow);
			  	}
			});
		 }
			
		function orderDetail(data){
			var nodeId = data['NODE_ID'];
			var resultId = data['RESULT_ID'];
			var orderId = data['ORDER_ID'];
			var checkResult = data['CHECK_RESULT'];
			var data = {nodeId:nodeId,checkResult:checkResult,orderId:orderId,custId:data.CUST_ID,resultId:resultId};
			popup.openTab({id:'orderDetail',title:'采购评审',url:'${ctxPath}/pages/erp/order/order-check.jsp',data:data});
		}
		
		function queryData(){
			$("#dataMgrForm").queryData({id:'todoReview'});
		}
			
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>