<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>合同看板</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }

        .layui-card-header, .layui-card-header select {
            color: rgb(62, 104, 254) !important;
        }

        .layui-this {
            color: rgb(62, 104, 254) !important;
        }


        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-stat .layui-col-md3 {
            text-align: center;
            padding: 10px;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .top-3 {
            font-size: 24px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .contract-stat .layui-card {
            padding: 10px;
            border-radius: 5px;
        }
        .contract-stat .layui-card-hover:hover {
            transform: scale(1.02);
            transition: transform .3s ease;
            box-shadow: 0 4px 11px #0003;
            cursor: pointer;
        }


        .layui-icon-tips {
            margin-left: 5px;
        }
        .layui-progress{margin-top: 12px;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm">
        <input type="hidden" name="source" value="all">
        <input type="hidden" name="stageTime" value="year">
        <input type="hidden" name="stageType" value="all">
        <input type="hidden" name="incomeStageType" value="all">
        <input type="hidden" name="receiptTime" value="year">
        <input type="hidden" name="dateField2" value="year">
        <div class="layui-row layui-col-space10 contract-stat" data-mars="ContractStatisticDao.contractConsoleStat">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="COUNT_CUST">0</div>
                        <div class="top-2">今年新增客户<i class="layui-icon layui-icon-tips" lay-tips="以客户信息的创建时间年份为统计标准。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="COUNT_CONTRACT">0</div>
                        <div class="top-2">今年新增销售合同数<i class="layui-icon layui-icon-tips" lay-tips="以合同年限的年份为准。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_CONTRACT">0</div>
                        <div class="top-2">今年新增销售合同金额<i class="layui-icon layui-icon-tips" lay-tips="以合同年限的年份为准。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card layui-card-hover"  data-category="1">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_STAGE">0</div>
                        <div class="top-2">今年预计收款金额<i class="layui-icon layui-icon-tips" lay-tips="计划完成时间为今年的合同阶段总额，合同阶段的计划完成时间与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10 contract-stat" data-mars="ContractStatisticDao.contractConsoleStat2">
            <div class="layui-col-md3">
                <div class="layui-card layui-card-hover"  data-category="1">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_RECEIPT">0</div>
                        <div class="top-2">今年已收款金额<i class="layui-icon layui-icon-tips" lay-tips="收款时间为今年的收款总额，与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card layui-card-hover" data-category="1">
                    <div class="layui-card-body">
                        <div class="top-3" id="UN_RECEIVE">0</div>
                        <div class="top-2">今年待收款金额<i class="layui-icon layui-icon-tips" lay-tips="今年的合同阶段总额 减去 今年的已收款总额。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card layui-card-hover" data-category="2">
                    <div class="layui-card-body">
                        <div class="top-1" id="SUM_CONFIRM">0</div>
                        <div class="top-2">今年已收入确认金额(税后)<i class="layui-icon layui-icon-tips" lay-tips="收入确认时间为今年的收入确认A总额，与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card layui-card-hover">
                    <div class="layui-card-body">
                        <div class="top-3" id="SUM_UNCONFIRM" data-category="2">0</div>
                        <div class="top-2">今年待收入确认金额(税后)<i class="layui-icon layui-icon-tips" lay-tips="计划完成时间为今年的收入确认阶段总额减去收入确认A总额，与合同年限无关。"></i></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <span name="currentYear"></span>合同回款排行
                        <i class="layui-icon layui-icon-tips" lay-tips="统计所有合同在今年的收款金额并排行。合同年限不限，收款日期为今年。"></i>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="contractReceiptRank"></table>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <span name="currentYear"></span>客户回款排行
                        <i class="layui-icon layui-icon-tips" lay-tips="统计所有客户在今年的收款金额并排行。合同年限不限，收款日期为今年。"></i>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="custReceiptRank"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <span name="currentYear"></span>销售排行
                        <i class="layui-icon layui-icon-tips" lay-tips="统计每个销售今年销售的合同总额，年份以合同年限为准。"></i>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="saleRankTable"></table>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <span name="currentYear"></span>销售回款排行
                        <i class="layui-icon layui-icon-tips" lay-tips="统计每个销售的合同在今年里收款金额的总和。合同年限不限，收款日期为今年。以合同的销售经理为准，与收款负责人无关。"></i>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="saleReceiptRankTable"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab">
                        <ul class="layui-tab-title">
                            <li class="layui-this"><span name="currentYear"></span>营销大区销售份额 <i class="layui-icon layui-icon-tips" lay-tips="统计营销大区今年销售的合同总额，年份以合同年限为准。"></i></li>
                            <li><span name="currentYear"></span>营销大区销售排行</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="deptChart1" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <table id="deptRankTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab">
                        <ul class="layui-tab-title">
                            <li class="layui-this"><span name="currentYear"></span>营销大区回款份额 <i class="layui-icon layui-icon-tips" lay-tips="统计营销大区的合同在今年里收款金额的总和。合同年限不限，收款日期为今年。以合同的营销大区为准。"></i></li>
                            <li><span name="currentYear"></span>营销大区回款排行</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="deptChart2" style="height: 340px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <table id="deptReceiptRankTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <span name="currentYear"></span>新增合同
                        <i class="layui-icon layui-icon-tips" lay-tips="合同年限为今年的合同列表。"></i>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="myContractTable"></table>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <select class="form-control input-sm selectStageType" style="display: inline-block;width: 200px;float: left;margin-top: 5px;color:#000000;box-shadow:none;border: none;">
                            <option value="1" selected> 今年全部合同阶段</option>
                            <option value="2"> 今年待收款合同阶段</option>
                            <option value="3"> 本月全部合同阶段</option>
                            <option value="4"> 本月待收款合同阶段</option>
                        </select>
                        <i class="layui-icon layui-icon-tips" lay-tips="以合同阶段信息的计划完成时间为统计标准，显示所有合同阶段。"></i>
                        <button type="button" class="btn btn-sm btn-default" onclick="StageConsole()">合同阶段总览</button>
                    </div>
                    <div class="layui-card-body" style="height: 350px;">
                        <table id="myContractStageTable"></table>
                    </div>
                </div>
            </div>
            <div class="layui-row layui-col-space10">
                <div class="layui-col-md12">
                    <div class="layui-card">
                        <div class="layui-card-header">
                            <select class="form-control input-sm selectIncomeStageType"
                                    style="display: inline-block;width: 200px;float: left;margin-top: 5px;color:#000000;box-shadow:none;border: none;">
                                <option value="1" selected> 今年全部收入确认阶段</option>
                                <option value="2"> 今年待完成收入确认阶段</option>
                                <option value="3"> 本月全部收入确认阶段</option>
                                <option value="4"> 本月待完成收入确认阶段</option>
                            </select>
                            <i class="layui-icon layui-icon-tips" lay-tips="以收入确认阶段信息的计划完成时间为统计标准，显示收入确认阶段(未填写计划完成时间不统计)。"></i>
                            <button type="button" class="btn btn-sm btn-default" onclick="IncomeConsole()">收入确认阶段总览</button>
                        </div>
                        <div class="layui-card-body" style="height: 350px;">
                            <table id="myIncomeStageTable"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <script type="text/x-jsrender" id="bar3">
 		 <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="contractList.contractDetail" href="javascript:;">合同详情</a>
    </script>
    <script type="text/x-jsrender" id="receiptProgress">
			{{if AMOUNT>0}}
			<div class="layui-progress" lay-showPercent="true">
  				<div class="layui-progress-bar {{if AMOUNT == TOTAL_RECEIPT}} layui-bg-green {{else TOTAL_RECEIPT/AMOUNT <= 0.6}} layui-bg-red {{else TOTAL_RECEIPT/AMOUNT <= 0.8}} layui-bg-orange  {{else}} layui-bg-blue {{/if}}" lay-percent="{{:TOTAL_RECEIPT}}/{{:AMOUNT}}"></div>
			</div>
			{{/if}}
	</script>
</EasyTag:override>
<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script>
        $(function () {

            $("#searchForm").render({
                success: function () {
                    $("[name='currentYear']").text(new Date().getFullYear());
                    contractList.initContractList();

                    contractList.initContractReceiptRank();
                    contractList.initCustReceiptRank();
                    contractList.initSaleRankList();
                    contractList.initSaleReceiptRankList();
                    contractList.initDeptRankList();
                    contractList.initDeptReceiptRankList();

                    contractList.initContractStageList();
                    // contractList.initReceiptList();
                    contractList.initIncomeStageList();
                }
            });

            $("[data-category]").click(function(){
                var type = $(this).data('category');
                if(type=='1'){
                    StageConsole();
                }else if(type=='2'){
                    IncomeConsole();
                }
            });
        });

        var contractList = {
            initContractReceiptRank: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.contractReceiptRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        autoSort: false,
                        id: 'contractReceiptRank',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'CONTRACT_SIMPILE_NAME',
                                title: '合同名称',
                                minWidth: 120,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractList.contractDetail',
                            }, {
                                field: 'TOTAL_AMOUNT',
                                title: '回款金额',
                                width: 130,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }
                        ]]
                    },
                )
            },

            initCustReceiptRank: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.custReceiptRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        autoSort: false,
                        id: 'custReceiptRank',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'CUSTOMER_NAME',
                                title: '客户',
                                minWidth: 120,
                                align: 'left',
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractList.custDetail',
                            }, {
                                field: 'TOTAL_AMOUNT',
                                title: '回款金额',
                                width: 130,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }
                        ]]
                    },
                )
            },

            initSaleRankList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.saleContractAmountRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        autoSort: false,
                        id: 'saleRankTable',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'SALES_BY',
                                title: '销售经理',
                                // width: 90,
                                minWidth: 90,
                                align: 'center',
                                templet: function (d) {
                                    return getUserName(d.SALES_BY);
                                }
                            }, {
                                field: 'TOTAL_AMOUNT',
                                title: '销售合同总金额',
                                // width: 120,
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }
                        ]]
                    },
                )
            },

            initSaleReceiptRankList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.salesReceiptRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        autoSort: false,
                        id: 'saleReceiptRankTable',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'SALES_BY',
                                title: '销售经理',
                                // width: 90,
                                minWidth: 90,
                                align: 'center',
                                templet: function (d) {
                                    return getUserName(d.SALES_BY);
                                }
                            }, {
                                field: 'TOTAL_AMOUNT',
                                title: '回款金额',
                                // width: 120,
                                minWidth: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }
                        ]]
                    },
                )
            },

            initDeptRankList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.deptContractAmountRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        autoSort: false,
                        id: 'deptRankTable',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'SALE_DEPT_NAME',
                                title: '营销大区',
                                // width: 90,
                                align: 'center',
                            }, {
                                field: 'TOTAL_AMOUNT',
                                title: '销售合同总金额',
                                // width: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }
                        ]],
                        done: function (data) {
                            var data2 = data.data;
                            const echartsData = data2.map(item => {
                                return {
                                    name: item.SALE_DEPT_NAME,
                                    value: formatAmount(item.TOTAL_AMOUNT)
                                };
                            });
                            contractList.loadDeptChart1(echartsData);
                        }
                    },
                )
            },

            initDeptReceiptRankList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStatisticDao.deptReceiptRank',
                        cellMinWidth: 50,
                        limit: 10,
                        height: '320px',
                        autoSort: false,
                        id: 'deptReceiptRankTable',
                        cols: [[
                            {
                                type: 'numbers',
                                title: '序号',
                                align: 'left'
                            }, {
                                field: 'SALE_DEPT_NAME',
                                title: '营销大区',
                                // width: 90,
                                align: 'center',
                            }, {
                                field: 'TOTAL_AMOUNT',
                                title: '回款金额',
                                // width: 120,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    var num = parseFloat(d.TOTAL_AMOUNT);
                                    return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                                }
                            }
                        ]],
                        done: function (data) {
                            var data2 = data.data;
                            const echartsData = data2.map(item => {
                                return {
                                    name: item.SALE_DEPT_NAME,
                                    value: formatAmount(item.TOTAL_AMOUNT)
                                };
                            });
                            contractList.loadDeptChart2(echartsData);
                        }
                    },
                )
            },

            initContractList: function () {
                $("#searchForm").initTable({
                    mars: 'ProjectContractDao.myContractList',
                    cellMinWidth: 50,
                    limit: 20,
                    data: {"userType": "all"},
                    height: '320px',
                    autoSort: false,
                    totalRow: true,
                    id: 'myContractTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            totalRowText: "合计"
                        }, {
                            field: '',
                            title: '操作',
                            width: 80,
                            align: 'center',
                            templet: function (row) {
                                return renderTpl('bar3', row);
                            },
                        }, {
                            field: 'CONTRACT_NO',
                            title: '合同号',
                            width: 110
                        }, {
                            field: 'CONTRACT_SIMPILE_NAME',
                            title: '合同名称',
                            minWidth: 120,
                        }, {
                            field: 'AMOUNT',
                            title: '合同金额',
                            align: 'right',
                            sort: true,
                            width: 120,
                            totalRow: true
                        },{
                            field:'',
                            title: '收款进度',
                            minWidth:160,
                            width: 180,
                            templet:function(row){
                                return renderTpl('receiptProgress',row);
                            }
                        }, {
                            field: 'CUSTOMER_NAME',
                            title: '客户名称',
                            sort: true,
                            width: 160
                        }, {
                            field: 'SALES_BY',
                            title: '销售经理',
                            width: 100,
                            sort: true,
                            align: 'center',
                            templet: function (d) {
                                return getUserName(d.SALES_BY);
                            }
                        }, {
                            field: 'SIGN_DATE',
                            title: '签订时间',
                            width: 110,
                            sort: true,
                            align: 'center'
                        },
                    ]],done:function (){
                        layui.use(['element'], function(){
                            var element = layui.element;
                            element.render();
                        });
                    }
                })
            },

            initContractStageList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractStageDao.myContractStageList',
                        cellMinWidth: 50,
                        limit: 20,
                        data: {homePage: 0, "stageTime": "year", "stageType": "all"},
                        height: '320px',
                        autoSort: false,
                        totalRow: true,
                        id: 'myContractStageTable',
                        cols: [[
                            {
                                type: 'numbers',
                                align: 'left',
                                totalRowText: "合计"
                            }, {
                                field: 'CONTRACT_NAME',
                                title: '合同名称',
                                minWidth: 120,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractList.contractDetail',
                            }, {
                                field: 'STAGE_NAME',
                                title: '阶段名称',
                                align: 'center',
                                minWidth: 80,
                                width: 100,
                                style: 'color:#1E9FFF;cursor:pointer',
                                event: 'contractList.contractStageDetail',
                            }, {
                                title: '收款状态',
                                width: 80,
                                align: 'center',
                                templet: function (d) {
                                    return getStageCompleteStatus(d.PLAN_COMP_DATE, d.ACT_COMP_DATE, d.RCV_DATE);
                                }
                            }, {
                                field: 'RCV_DATE',
                                title: '应收日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'PLAN_COMP_DATE',
                                title: '计划完成日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'ACT_COMP_DATE',
                                title: '实际完成日期',
                                width: 110,
                                sort: true,
                            }, {
                                field: 'PLAN_RCV_AMT',
                                title: '计划收款金额',
                                width: 110,
                                totalRow: true,
                                sort: true,
                            }, {
                                field: 'RECEIVED_AMT',
                                title: '已收款金额',
                                width: 110,
                                totalRow: true,
                                sort: true,
                            }, {
                                field: 'SALES_BY',
                                title: '销售经理',
                                width: 110,
                                sort: true,
                                align: 'center',
                                templet: function (d) {
                                    return getUserName(d.SALES_BY);
                                }
                            },
                        ]],
                    done: function (res) {

                    },
                    },
                )
            },

            initReceiptList: function () {
                $("#searchForm").initTable({
                        mars: 'ContractReceiptDao.myReceiptList',
                        cellMinWidth: 50,
                        limit: 20,
                        data: {homePage: 0, "receiptTime": "year"},
                        height: '320px',
                        autoSort: false,
                        totalRow: true,
                        id: 'myReceiptTable',
                        cols: [[
                            {
                                field: '',
                                title: '操作',
                                width: 80,
                                align: 'center',
                                templet: function (row) {
                                    return renderTpl('bar3', row);
                                },
                                totalRowText: "合计"
                            }, {
                                field: 'RECEIPT_DATE',
                                title: '收款日期',
                                align: 'left',
                                width: 100,
                                sort: true
                            }, {
                                field: 'AMOUNT',
                                title: '收款金额',
                                totalRow: true,
                                width: 110,
                                sort: true
                            }, {
                                field: 'CONTRACT_NAME',
                                title: '合同名称',
                                minWidth: 120,
                            }, {
                                field: 'STAGE_NAME',
                                title: '合同阶段',
                                minWidth: 80,
                                width: 100,
                            },
                        ]]
                    },
                )
            },

            initIncomeStageList: function () {
                $("#searchForm").initTable({
                    mars: 'IncomeStageDao.allIncomeStageList',
                    id: 'myIncomeStageTable',
                    page: true,
                    cellMinWidth: 60,
                    height: '320px',
                    rowDoubleEvent(row) {
                        contractList.incomeStageDetail(row);
                    },
                    data: {'incomeStageType': "all"},
                    autoSort: false,
                    totalRow: true,
                    cols: [[
                        {
                            type: 'numbers',
                            align: 'left',
                            totalRowText: "合计"
                        }, {
                            field: 'CONTRACT_NAME',
                            title: '合同名称',
                            align: 'left',
                            minWidth: 120,
                            style: 'color:#1E9FFF;cursor:pointer',
                            event: 'contractList.contractDetail',
                        }, {
                            field: 'STAGE_NAME',
                            title: '收入确认阶段',
                            align: 'center',
                            width: 100,
                            style: 'color:#1E9FFF;cursor:pointer',
                            event: 'contractList.incomeStageDetail',
                        }, {
                            field: 'INCOME_CONF_FLAG',
                            title: '状态',
                            minWidth: 100,
                            width: 100,
                            templet: function (d) {
                                return getIncomeStageStatus(d.INCOME_CONF_FLAG, d.PLAN_COMP_DATE, d.ACT_COMP_DATE);
                            }
                        }, {
                            field: 'PLAN_COMP_DATE',
                            title: '计划完成时间',
                            align: 'left',
                            sort: true,
                            width: 110
                        }, {
                            field: 'ACT_COMP_DATE',
                            title: '实际完成日期A',
                            align: 'left',
                            sort: true,
                            width: 120
                        }, {
                            field: 'PLAN_AMOUNT',
                            title: '计划收款金额(含税)',
                            minWidth: 130,
                            totalRow: true,
                            sort: true,
                        }, {
                            field: 'AMOUNT_NO_TAX',
                            title: '计划收款金额(税后)',
                            minWidth: 130,
                            totalRow: true,
                            sort: true,
                        },
                        {
                            field: 'UNCONFIRM_NO_TAX_A',
                            title: '未确认金额A(税后)',
                            width: 130,
                            totalRow: true,
                            sort: true,
                        }, {
                            field: 'REMARK',
                            title: '备注',
                            width: 120
                        }
                    ]],
                    done: function (res) {

                    }
                });

            },

            contractDetail: function (data) {
                if (isArray(data)) {
                    data = data[0];
                }
                popup.openTab({id: 'contractDetail', title: '合同详情', url: '${ctxPath}/project/contract', data: {contractId: data.CONTRACT_ID, custId: data['CUST_ID'], isDiv: 0}});
            },
            custDetail: function (data) {
                popup.openTab({id: 'custDetail', title: '客户详情', url: '${ctxPath}/pages/crm/cust/cust-detail.jsp', data: {custId: data['CUST_ID']}});
            },
            incomeStageDetail: function (data) {
                var incomeStageId = data.INCOME_STAGE_ID;
                var contractId = data.CONTRACT_ID;
                popup.layerShow({
                    type: 1,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    title: '收入确认阶段详情',
                    offset: 'r',
                    area: ['800px', '100%'],
                    url: '${ctxPath}/pages/crm/contract/include/income-stage-detail.jsp',
                    data: {contractId: contractId, incomeStageId: incomeStageId}
                });
            },
            contractStageDetail: function (data) {
                var contractId = data.CONTRACT_ID;
                var stageId = data.STAGE_ID;
                popup.layerShow({
                    type: 1,
                    anim: 0,
                    scrollbar: false,
                    shadeClose: false,
                    title: '合同阶段详情',
                    offset: 'r',
                    area: ['800px', '100%'],
                    url: '${ctxPath}/pages/crm/contract/include/stage-detail.jsp',
                    data: {contractId: contractId, stageId: stageId}
                });
            },


            loadDeptChart1: function (echartsData) {
                var options1 = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b}: {c}万 ({d}%)"
                    },
                    series: [
                        {
                            name: "合同销售数据",
                            type: "pie",
                            radius: '55%',
                            data: echartsData,
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{b}: {c} 万({d}%)'
                            },
                            labelLine: {
                                show: true
                            }
                        },

                    ]
                };
                var myCharts = echarts.init(document.getElementById('deptChart1'), myEchartsTheme);
                myCharts.setOption(options1);
            },
            loadDeptChart2: function (echartsData) {
                var options1 = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b}: {c}万 ({d}%)"
                    },
                    series: [
                        {
                            name: "合同销售数据",
                            type: "pie",
                            radius: '55%',
                            data: echartsData,
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{b}: {c} 万({d}%)'
                            },
                            labelLine: {
                                show: true
                            }
                        },

                    ]
                };
                var myCharts = echarts.init(document.getElementById('deptChart2'), myEchartsTheme);
                myCharts.setOption(options1);
            },

        }


        $('.selectStageType').on('change', function () {
            var value = $(this).val();

            if (value === "1") {
                $('input[name="stageTime"]').val("year");
                $('input[name="stageType"]').val("all");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            } else if (value === "2") {
                $('input[name="stageTime"]').val("year");
                $('input[name="stageType"]').val("unComp");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            } else if (value === "3") {
                $('input[name="stageTime"]').val("month");
                $('input[name="stageType"]').val("all");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            } else if (value === "4") {
                $('input[name="stageTime"]').val("month");
                $('input[name="stageType"]').val("unComp");
                $("#searchForm").queryData({id: 'myContractStageTable', data: {}});
            }

        });

        $('.selectIncomeStageType').on('change', function () {
            var value = $(this).val();
            if (value === "1") {
                $('input[name="dateField2"]').val("year");
                $('input[name="incomeStageType"]').val("all");
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {}});
            } else if (value === "2") {
                $('input[name="dateField2"]').val("year");
                $('input[name="incomeStageType"]').val("unConfirm");
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {}});
            } else if (value === "3") {
                $('input[name="dateField2"]').val("month");
                $('input[name="incomeStageType"]').val("all");
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {}});
            } else if (value === "4") {
                $('input[name="dateField2"]').val("month");
                $('input[name="incomeStageType"]').val("unConfirm");
                $("#searchForm").queryData({id: 'myIncomeStageTable', data: {}});
            }
        });

        $('.selectReceiptType').on('change', function () {
            var value = $(this).val();
            $('input[name="receiptTime"]').val(value);
            $("#searchForm").queryData({id: 'myReceiptTable'}, {data: {}});
        });


        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });

        function IncomeConsole() {
            popup.openTab({id: 'incomeConsole', title: '全部收入确认', url: '${ctxPath}/pages/crm/contract/statistic/income-console.jsp',});
        }

        function StageConsole() {
            popup.openTab({id: 'StageConsole', title: '全部合同阶段', url: '${ctxPath}/pages/crm/contract/statistic/stage-console.jsp',});
        }

        function formatAmount(amount) {
            return (parseFloat(amount) / 10000).toFixed(2);
        }

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>