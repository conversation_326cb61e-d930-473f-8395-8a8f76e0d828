<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table id="goodsStorageOutTable"></table>

<script  type="text/html" id="outToolbar">
	<button class="btn btn-default btn-sm" onclick="stockOutOrder()" type="button">出库单</button>
	<button class="btn btn-default btn-sm ml-15" onclick="returnStorage()" type="button">退货</button>
</script>
	<script type="text/javascript">
		$(function(){
			$("#OrderDetailForm").initTable({
				mars:'GoodsDao.stockList',
				id:'goodsStorageOutTable',
				page:false,
				data:{type:'20'},
				height:300,
				toolbar:'#outToolbar',
				rowDoubleEvent:'editOutStock',
				cellMinWidth:80,
				cols: [[
	             {
            	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
            	 	type: 'checkbox',
					align:'left'
				 },{
					 field:'GOODS_NAME',
					 title:'物料名称'
				 },{
					 field:'NUMBER',
					 title:'采购数量'
				 },{
					 field:'NUM',
					 title:'出库数量',
					 width:70
				 },{
					 field:'PRICE',
					 title:'出库成本',
					 width:70,
					 templet:function(row){
						 return row['PRICE']*row['NUM'];
					 }
				 },{
				    field: 'OUT_TYPE',
					title: '出库方式',
					align:'center',
					templet:function(row){
						var json = {1:'整体出库',2:'零件出库'};
						 return json[row['OUT_TYPE']];
					 }
				},{
				    field: 'DATE_ID',
					title: '出库日期',
					align:'center'
				},{
					field:'REMARK',
					title:'备注',
					edit:'text'
				},{
				    field: 'CREAT_NAME',
					title: '操作人',
					align:'center'
				},{
				    field: 'CREATE_TIME',
					title: '操作时间',
					align:'center'
				}
			]],done:function(data){
				$("#stockOutCount").text(data.total);
		  	}
		});
	});
	
	function storageOutReload(){
		$("#OrderDetailForm").queryData({page:false,id:'goodsStorageOutTable',data:{type:'20'}});
	}
		
	function editOutStock(data){
		popup.layerShow({id:'storageEdit',area:['600px','400px'],title:'修改',url:'${ctxPath}/pages/erp/goods/storage-edit.jsp',data:{title:'出库',type:'out',stockId:data['STOCK_ID'],goodsId:data['GOODS_ID']}});
	}
		
	function stockOutOrder(type){
		var checkStatus = table.checkStatus('goodsStorageOutTable');
		var data = checkStatus.data;
 		var sum = data.length;
 		if(sum <= 0){
 			layer.msg('请选择物料。',{icon : 7, time : 1000});
 			return;
 		}
 		var ids = [];
 		for(var i = 0; i < sum; i ++){
			ids.push(data[i].STOCK_ID);
 		}
 		if(ids.length <= 0){
 			layer.msg('请选择。',{icon : 7, time : 1000});
 			return;
 		}
		popup.openTab({id:'out-order',title:'出库单',url:'/yq-work/pages/erp/order/include/storage-out-order.jsp',data:{orderId:orderId,stockIds:ids.join()}});
	}
	
</script>
