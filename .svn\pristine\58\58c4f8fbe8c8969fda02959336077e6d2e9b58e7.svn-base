package com.yunqu.work.dao.contract;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name = "IncomeConfirmDao")
public class IncomeConfirmDao extends AppDaoContext {

    @WebControl(name = "record", type = Types.RECORD)
    public JSONObject record() {
        String confirmId = param.getString("confirm.CONFIRM_ID");
        if (StringUtils.notBlank(confirmId)) {
            return queryForRecord("select * from yq_contract_income_confirm where CONFIRM_ID = ?", confirmId);
        } else {
            return getJsonResult(new JSONObject());
        }
    }

    @WebControl(name = "incomeConfirmList", type = Types.LIST)
    public JSONObject incomeConfirmList() {
        EasySQL sql = getEasySQL("select t1.* from yq_contract_income_confirm t1");
        sql.append("where 1=1");
        sql.append(param.getString("contractId"), " and t1.CONTRACT_ID = ?");
        sql.append(param.getString("confirmType")," and t1.CONFIRM_TYPE = ?");
        sql.append(param.getString("incomeStageId")," and t1.INCOME_STAGE_ID = ?");
        sql.append("order by t1.CONFIRM_DATE ASC");
        return queryForList(sql.getSQL(), sql.getParams());
    }

}
