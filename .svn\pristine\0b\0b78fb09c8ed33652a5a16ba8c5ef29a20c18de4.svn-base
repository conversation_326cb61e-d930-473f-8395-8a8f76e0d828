package com.yunqu.work.servlet.crm;

import java.io.File;
import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.easitline.common.core.Globals;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.ProjecContractModel;
import com.yunqu.work.model.ProjectModel;
import com.yunqu.work.service.OAContractSynDetailService;
import com.yunqu.work.service.OAService;
@WebServlet("/servlet/projectContract/*")
public class ContractServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public void actionForSynData(){
		try {
			List<JSONObject> list=this.getQuery().queryForList("select * from YQ_PROJECT_CONTRACT",null,new JSONMapperImpl());
			ProjectModel model=new ProjectModel();
			for(JSONObject json:list){
				model.set("PROJECT_ID", json.getString("CONTRACT_ID"));
				model.set("PROJECT_NAME", json.getString("CONTRACT_NAME"));
				model.set("PROJECT_NO", json.getString("CONTRACT_NO"));
				model.set("CONTRACT_ID", json.getString("CONTRACT_ID"));
				model.save();
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		renderText("OK");
	}
	public void actionForSyn(){
         try {
        	 File file = new File(Globals.TMP_DIR+File.separator+"LinkeyGetXmlDataForAdmin.xml");
        	 SAXReader reader=new SAXReader();
			 Document doc=reader.read(file);
			 Element root = doc.getRootElement();
			 List<Element> childElements = root.elements();
			 for (Element child : childElements) {
				 Element attr=child.element("ItemAttributes");
				 if(attr!=null){
					 ProjectModel model=new ProjectModel();
					 String accCode= StringUtils.trimToEmpty(attr.elementText("acc_code"));
					 try {
						if(this.getQuery().queryForExist("select count(1) from YQ_PROJECT where PROJECT_NO = ?", accCode)){
							continue; 
						 }
					} catch (SQLException e1) {
						e1.printStackTrace();
					}
					 model.set("PROJECT_NO", StringUtils.trimToEmpty(attr.elementText("acc_code")));
					 model.set("PROJECT_NAME", StringUtils.trimToEmpty(attr.elementText("acc_desc")));
					 model.set("PROJECT_ID", RandomKit.uniqueStr());
					try {
						model.save();
					} catch (SQLException e) {
						e.printStackTrace();
					}
				 }
			 }
            renderText("ok");
		} catch (DocumentException e) {
			e.printStackTrace();
		}
	}
	public EasyResult actionForAdd(){
		ProjecContractModel model=getModel(ProjecContractModel.class, "contract");
		model.setCreator(getUserId());
		model.addCreateTime();
		model.setPrimaryValues(RandomKit.uuid().toUpperCase());
		try {
			model.save();
			String projectId=model.getString("PROJECT_ID");
			if(StringUtils.notBlank(projectId)){
				this.getQuery().executeUpdate("update yq_project set contract_id= ? where project_id = ?",model.getPrimaryValue(),projectId);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		ProjecContractModel model=getModel(ProjecContractModel.class, "contract");
		model.set("UPDATE_TIME",EasyDate.getCurrentDateString());
		try {
			model.update();
			String projectId=model.getString("PROJECT_ID");
			if(StringUtils.notBlank(projectId)){
				this.getQuery().executeUpdate("update yq_project set contract_id= ? where project_id = ?",model.getPrimaryValue(),projectId);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForSynDetail(){
		OAService.getService().login("100042","pcitech");
		String id = getJsonPara("id");
		OAContractSynDetailService.getService().synData(id);
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForDelete(){
		ProjecContractModel model=getModel(ProjecContractModel.class, "contract");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
	
}





