<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>文档库管理</title>
	<style>
		.layui-btn{display: none;}
		.layui-table-hover .layui-btn{
			display: inline-block;
		}
		#folderName a,#folderName span{color: #999;margin: 0px 3px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
		    <input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
			<input type="hidden" name="folderId" id="folderId" value="0"/>
			<input type="hidden" id="_folderName"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="fa fa-file"></span> 我创建的文档库</h5>
	          		     <div class="input-group input-group-sm" style="width: 180px">
							 <span class="input-group-addon">文档库名称</span>	
							 <input type="text" name="folderName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <div class="btn-group">
								<button type="button" class="btn btn-sm btn-success" onclick="list.addDoc()">+ 新增文档</button>
								<button type="button" class="btn btn-sm btn-success dropdown-toggle" data-toggle="dropdown">
									<span class="caret"></span>
								</button>
								<ul class="dropdown-menu" role="menu" style="left: -54px">
									<li><a href="javascript:$('#localfile').click()">上传文件</a></li>
									<li><a href="javascript:list.addFolder()">新建文件夹</a></li>
								</ul>
							</div>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content" style="min-height: 400px">
		              	<p id="folderName" style="height: 40px;line-height: 40px;margin-top: -16px"><a href='javascript:void(0)' onclick='list.goFolder(0,this)'>所有文件夹</a> </p>
					    <table class="layui-hide" id="list"></table>
					    <table class="layui-hide" id="files"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
  			<a class="layui-btn layui-btn-xs  layui-btn-normal" lay-event="list.open">打开</a>
  			{{if currentUserId==MANAGER || currentUserId == CREATOR}}<a class="layui-btn layui-btn-xs" lay-event="list.editFolder">编辑</a>{{/if}}
		</script>
		<script type="text/x-jsrender" id="bar2">
  			<a class="layui-btn layui-btn-xs  layui-btn-normal" lay-event="list.detail">详情</a>
  			{{if currentUserId==MANAGER || currentUserId == CREATOR}}<a class="layui-btn layui-btn-xs" lay-event="list.editDoc">编辑</a>{{/if}}
  			{{if currentUserId==MANAGER || currentUserId == CREATOR}}<a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="list.deleteDoc">删除</a>{{/if}}
		</script>
		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
				list.loadFolder();
		});
		var uploadFile = function(callback){
			$("#randomId").render({success:function(){
				var docId=$("#randomId").val();
				easyUploadFile({callback:'callback',fileMaxSize:(1024*50),fkId:docId,source:'doc'});
			}});
		}
		
		var callback = function(data,params){
			var id=data.id;
			var url=data.url;
			var name=data.name;
			var docId=params.fkId;
			var data={'doc.DOC_ID':docId,'doc.TITLE':name,'doc.DOC_AUTH':1,'doc.FOLDER_ID':$("#folderId").val(),'doc.DOC_DESC':'见附件'};
			ajax.remoteCall("${ctxPath}/servlet/doc?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		var list={
			loadFolder:function(){
				$("#searchForm").initTable({
					mars:'FolderDao.myFolderlist',
					skin:'line',
					page:false,
					id:'list',
					rowDoubleEvent:'list.open',
					cols: [[
		             {
					    field: 'FOLDER_NAME',
						title: '文档库名称',
						align:'left',
						templet:function(row){
							return '<i class="fa fa-folder-open-o"></i>&nbsp;'+row.FOLDER_NAME;
						}
					},
					{
						title: '操作',
						align:'center',
						width:180,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar1',row);
						}
					},{
					    field: 'MANAGER',
						title: '负责人',
						align:'right',
						width:130,
						templet:function(row){
							return getUserName(row.MANAGER);
						}
					}
					]],done:function(res){
						var flag=false;
						if(res.data.length==0){
							$("[lay-id='list']").remove();
							flag=true;
						}
						$(".layui-table-header").css("display","none");
						$(".layui-table-view").first().css("margin-bottom","0px");
						list.loadFile(flag);
					}}
				);
			},
			loadFile:function(flag){
				$("#searchForm").initTable({
					mars:'FolderDao.mydoc',
					id:'files',
					skin:'line',
					page:false,
					rowDoubleEvent:'list.detail',
					cols: [[
					 {type:'numbers',width:30,align:'right'},
		             {
					    field: 'TITLE',
						title: '文档库名称',
						align:'left',
						templet:function(row){
							return '<i class="fa fa-file-archive-o"></i>&nbsp;'+row.TITLE;
						}
					},{
						title: '操作',
						align:'center',
						width:180,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar2',row);
						}
					},{
					    field: 'MANAGER',
						title: '负责人',
						align:'right',
						width:120,
						templet:function(row){
							return getUserName(row.MANAGER);
						}
					}
					]],done:function(res){
						$(".layui-table-header").css("display","none");
						$("[data-field='TITLE'] .layui-table-cell").css("padding-left",'0px');
						if(res.data.length==0){
							$("[lay-id='files']").remove();
							if(flag){
								$("#folderName").html('<div class="text-center mt-30">暂无我创建的文档</div><div class="text-center mt-10"><a href="javascript:void(0)" class="btn btn-link" onclick="list.addDoc()">+ 新增文档</a></div>');
							}
						}
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'list',page:false});
				$("#searchForm").queryData({id:'files',page:false});
			},
			open:function(data,obj){
				$("#folderId").val(data.FOLDER_ID);//设置当前文件夹目录
				$("#_folderName").val(data.FOLDER_NAME);
				$("#folderName").append("<span>/</span> <a href='javascript:void(0)' onclick=\"list.goFolder('"+data.FOLDER_ID+"',this)\">"+data.FOLDER_NAME+"</a>  ");
				list.query();
			},
			editDoc:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/doc/doc-edit.jsp',title:'编辑',data:{docId:data.DOC_ID,folderId:data.FOLDER_ID}});
			},
			addDoc:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/doc/doc-edit.jsp',data:{folderId:$("#folderId").val()},title:'新增文档'});
			},
			addFolder:function(){
				var folderName = $("#_folderName").val()||'根目录';
				popup.layerShow({type:1,maxmin:true,area:['450px','300px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'新增文件夹',data:{id:$("#folderId").val(),source:'0',folderName:folderName,folderAuth:1}});
			},
			editFolder:function(data){
				popup.layerShow({type:1,maxmin:true,area:['450px','300px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'编辑文件夹',data:{folderId:data.FOLDER_ID}});
			},
			detail:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['700px','100%'],url:'${ctxPath}/pages/doc/doc-detail.jsp',title:'详情',data:{op:'detail',docId:data.DOC_ID,folderId:data.FOLDER_ID}});
			},
			goFolder:function(id,obj){
				$(obj).nextAll().remove();
				$(obj).nextAll().find("span").remove();
				$("#folderId").val(id);
				list.query();
			},
			deleteDoc:function(data){
				layer.confirm("确认要删除吗?",{icon:3},function(){
					ajax.remoteCall("${ctxPath}/servlet/doc?action=delete",{'doc.DOC_ID':data.DOC_ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
					
				});
			}
		}
		function loadTree (){
			location.reload();
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>