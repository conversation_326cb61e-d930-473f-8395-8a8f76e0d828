package com.yunqu.work.dao.ehr;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;

@WebObject(name="EhrDao")
public class EhrDao extends AppDaoContext {
	
	
	@WebControl(name="dict",type=Types.DICT)
	public  JSONObject dict(){
		return getDictById(this.getMethodParam(0).toString());
	}
	@WebControl(name="dictList",type=Types.LIST)
	public  JSONObject dictList(){
		this.setQuery(getMainQuery());
		return queryForList("select * from EASI_PC_DICT  where DICT_TYPE_ID = ? order by IDX_ORDER",new Object[] {this.getMethodParam(0).toString()});
	}
	
	
	@WebControl(name="allDept",type=Types.DICT)
	public JSONObject allDept(){
		setQuery(getMainQuery());
		return getDictByQuery("select t1.DEPT_ID,t1.DEPT_NAME from "+Constants.DS_MAIN_NAME+".easi_dept t1,"+Constants.DS_MAIN_NAME+".easi_dept_user t2 where t1.DEPT_ID = t2.DEPT_ID GROUP BY t1.DEPT_ID ORDER BY t1.IDX_ORDER");
	}
	
	@WebControl(name="selectDept",type=Types.PAGE)
	public JSONObject selectDept(){
		setQuery(getMainQuery());
		EasySQL sql = new EasySQL();
		sql.append("select t1.*,count(1) count from "+Constants.DS_MAIN_NAME+".easi_dept t1 inner join "+Constants.DS_MAIN_NAME+".easi_dept_user t2 on t1.DEPT_ID = t2.DEPT_ID where 1=1");
		sql.appendLike(param.getString("deptName"),"and t1.DEPT_PATH_NAME like ?");
		if(!isSuperUser()&&!hasRole("HR_MGR")&&!hasRole("FINANCE_ROLE")) {
			sql.append(getStaffInfo().getRootId(),"and t1.root_id = ?");
		}
		sql.append("GROUP BY t1.DEPT_ID ORDER BY t1.IDX_ORDER");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 全部人员
	 * @return
	 */
	@WebControl(name="userList",type=Types.LIST)
	public JSONObject userList(){
		String groupId=param.getString("groupId");
		String deptCode=param.getString("deptCode");
		EasySQL sql=getEasySQL("select t1.username,t1.depts,t1.user_id,t2.job_title,t2.staff_no,t2.dept_id,t2.join_date from easi_user t1 inner join yq_staff_info t2  on t1.USER_ID = t2.staff_user_id inner join easi_user_login t3 on t1.user_id = t3.user_id");
		if(StringUtils.notBlank(deptCode)){
			sql.append("inner join easi_dept_user t4 on t4.user_id = t1.user_id");
			sql.append("inner join easi_dept t5 on t5.dept_id = t4.dept_id");
		}
		sql.append(" where  t1.STATE = 0");
		if(StringUtils.notBlank(deptCode)){
			sql.appendLike(deptCode,"and t5.DEPT_CODE like ?");
		}
		
		sql.appendLike(param.getString("deptName"),"and t1.depts like ?");
		String userName = param.getString("userName");
		if(StringUtils.notBlank(userName)) {
			sql.append("and ((");
			sql.appendLike(userName," t1.USERNAME like ?");
			sql.append(")");
			sql.append("or (");
			sql.appendLike(userName," t2.py_name like ?");
			sql.append("))");
		}
		
		if(StringUtils.notBlank(groupId)) {
			try {
				String ids = this.getQuery().queryForString("select user_ids from yq_group_user where group_id = ?",groupId);
				if(StringUtils.notBlank(ids)) {
					sql.appendIn(ids.split(","),"and t1.user_id");
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
		
		String busiType = param.getString("busiType");
		if("version".equals(busiType)) {
			try {
				String ids = this.getQuery().queryForString("select CONCAT(GROUP_CONCAT(receiver_id),',',GROUP_CONCAT(recipient_id)) from yq_ops_version where creator = ? order by create_time desc limit 20",getUserId());
				if(StringUtils.notBlank(ids)) {
					sql.appendIn(ids.split(","),"and t1.user_id");
				}
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
		sql.append("ORDER BY t2.staff_no");
		setQuery(getMainQuery());
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name="addresslist",type=Types.LIST)
	public JSONObject addresslist(){
		String deptCode=param.getString("deptCode");
		EasySQL sql=getEasySQL("select t1.MOBILE,t1.DEPTS,t1.USERNAME,t1.EMAIL,t1.NIKE_NAME,t1.BORN,t4.LAST_LOGIN_TIME,t5.JOB_TITLE,t5.STAFF_NO from easi_user t1");
		sql.append("inner join yq_staff_info t5 on t5.staff_user_id = t1.user_id");
		sql.append("inner join easi_user_login t4 on t4.user_id = t1.user_id");
		if(StringUtils.notBlank(deptCode)){
			sql.append("inner join easi_dept_user t2 on t2.user_id = t1.user_id");
			sql.append("inner join easi_dept t3 on t3.dept_id = t2.dept_id");
		}
		sql.append(" where  t1.STATE = 0");
		if(StringUtils.notBlank(deptCode)){
			sql.appendLike(deptCode,"and t3.DEPT_CODE like ?");
		}
		setQuery(ServerContext.getAdminQuery());
		sql.appendLike(param.getString("mobile"), "and t1.MOBILE  like ?");
		sql.appendLike(param.getString("userName"), "and t1.USERNAME  like ?");
		sql.appendLike(param.getString("depts"), "and t1.DEPTS  like ?");
		sql.append("order by t1.DATA1 asc");
		return queryForPageList(sql.getSQL(),sql.getParams());
	}

}
