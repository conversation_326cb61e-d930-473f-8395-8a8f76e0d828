<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
		.w-e-text-container{z-index: 0!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="projectEditForm" class="form-horizontal" data-mars="ProjectDao.record" autocomplete="off" data-mars-prefix="project.">
 		   		<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
 		   		<input type="hidden" id="projectId" value="${param.projectId}" name="project.PROJECT_ID"/>
 		   		<input type="hidden" value="${param.projectId}" name="fkId"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td width="80px" class="required">项目名称</td>
		                    <td style="width: 40%"><input data-rules="required"  type="text" name="project.PROJECT_NAME" class="form-control input-sm"></td>
		                    <td width="80px" class="required">合同编号</td>
		                    <td style="width: 40%"><input placeholder="若无填写0"  data-rules="required"  type="text" name="project.PROJECT_NO" id="projectNo" class="form-control input-sm"></td>
			            </tr>
			           
			            <tr>
		                    <td class="required">项目负责人</td>
		                    <td>
		                    <select class="form-control input-sm" data-rules="required" data-mars="CommonDao.userDict" name="project.PO">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
		                    <td class="required">项目级别</td>
		                    <td>
		                    	<select class="form-control input-sm"" name="project.PROJECT_LEVEL">
		                    		<option value="1">正常</option>
		                    		<option value="2">紧急</option>
		                    		<option value="3">重要</option>
		                    		<option value="4">重要且紧急</option>
		                    	</select>
		                    </td>
		                </tr>
			            <tr>
		                    <td>参与团队人员</td>
		                    <td colspan="3">
			                    <select class="form-control input-sm" multiple="multiple" data-mars="CommonDao.userDict" name="teams">
			                    </select>
		                    </td>
		                </tr>
			            <tr>
		                    <td class="required">项目起止时间</td>
		                    <td colspan="3">
			                    <input id="beginDate" data-mars="CommonDao.today" type="text" style="display: inline-block;width: 120px" placeholder="开始时间" data-rules="required" data-rules="required" data-mars="CommonDao.today" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="project.BEGIN_DATE"  class="form-control input-sm Wdate">
			                    <input id="endDate" type="text" style="display: inline-block;width: 120px" placeholder="计划完成时间" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" id="orderFinishTime" name="project.END_DATE" class="form-control input-sm Wdate">
		                    	<div class="btn-group">
		                   			<button onclick="fillDate(30)" class="btn btn-sm btn-default" type="button">1个月</button>
		                   			<button onclick="fillDate(60)" class="btn btn-sm btn-default" type="button">2个月</button>
		                   			<button onclick="fillDate(90)" class="btn btn-sm btn-default" type="button">3个月</button>
		                   			<button onclick="fillDate(180)" class="btn btn-sm btn-default" type="button">半年</button>
		                   			<button onclick="fillDate(365)" class="btn btn-sm btn-default" type="button">一年</button>
		                   		</div>
		                    </td>
		                </tr>
		                <tr>
			              	<td>
			            		项目类型
			            	</td>
			            	<td colspan="3">
			            		<label class="radio radio-info radio-inline">
	                    			<input type="radio" value="10" checked="checked" name="project.PROJECT_TYPE"/> <span>合同项目</span>
		                    	</label>
			            		<label class="radio radio-info radio-inline">
	                    			<input type="radio" value="11" name="project.PROJECT_TYPE"/> <span>提前执行</span>
		                    	</label>
			            		<label class="radio radio-info radio-inline">
	                    			<input type="radio" value="20" name="project.PROJECT_TYPE"/> <span>自研项目</span>
		                    	</label>
			            		<label class="radio radio-info radio-inline">
	                    			<input type="radio" value="30" name="project.PROJECT_TYPE"/> <span>维保项目</span>
		                    	</label>
			            	</td>
			            </tr>
		                <tr>
			              	<td>
			            		项目进度
			            	</td>
			            	<td colspan="3">
			            		<label class="radio radio-info radio-inline">
	                    			<input type="radio" value="11" onclick="return false;" checked="checked" name="project.PROJECT_STATE"/> <span>进度正常</span>
		                    	</label>
			            		<label class="radio radio-info radio-inline">
	                    			<input type="radio" value="12" onclick="return false;" name="project.PROJECT_STATE"/> <span>存在风险</span>
		                    	</label>
			            		<label class="radio radio-info radio-inline">
	                    			<input type="radio" value="13" onclick="return false;" name="project.PROJECT_STATE"/> <span>进度失控</span>
		                    	</label>
			            		<label class="radio radio-info radio-inline">
	                    			<input type="radio" value="20" onclick="return false;" name="project.PROJECT_STATE"/> <span>挂起中</span>
		                    	</label>
			            		<label class="radio radio-success radio-inline">
	                    			<input type="radio" value="30" onclick="return false;" name="project.PROJECT_STATE"/> <span>已完成</span>
		                    	</label>
			            	</td>
			            </tr>
		                <tr <c:if test="${empty param.projectId}">style='display:none'</c:if>>
			                <td>项目合同</td>
			               	<td colspan="3">
								<button class="btn btn-sm btn-default" id="editContract" type="button" onclick="Project.editContract();">编辑合同</button>
			               	</td>
			            </tr>
		                <tr>
			                <td>附件</td>
			               	<td colspan="3">
			               		<div data-template="template-files" data-mars="FileDao.fileList"></div>
			               		<div id="fileList"></div>
								<button class="btn btn-sm btn-default mt-5" type="button" onclick="$('#localfile').click()">+上传</button>
			               	</td>
			            </tr>
			            <tr>
			            	<td class="required">描述</td>
			               	<td colspan="3">
			               	     <div id="editor">
							     </div>
	                           <textarea id="wordText" style="height: 500px;display: none;" class="form-control input-sm" name="project.PROJECT_DESC"></textarea>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c" style="z-index: 999999999">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="Project.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  		</form>
	    <script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="Project.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
   
		jQuery.namespace("Project");
	    
		Project.projectId='${param.projectId}';
		var projectId='${param.projectId}';
		
		var E = window.wangEditor;
		var editor = new E('#editor');
		 editor.customConfig.menus = [
		    'head',  // 标题
		    'bold',  // 粗体
		    'fontSize',  // 字号
		    'fontName',  // 字体
		    'foreColor',  // 文字颜色
		    'link',  // 插入链接
		    'list',  // 列表
		    'justify',  // 对齐方式
		    'quote',  // 引用
		    'emoticon',  // 表情
		    'image',  // 插入图片
		    'table',  // 表格'
		 ];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.customConfig.onchange = function (html) {
		     $("#wordText").val(html);
		};
		var projectObj={};
		$(function(){
		    $('[data-toggle="tooltip"]').tooltip();
			$("#projectEditForm").render({success:function(result){
				editor.create();
				editor.txt.html($("#wordText").val());
			 	 requreLib.setplugs('select2',function(){
					var record=result['ProjectDao.record'];
					var today=result['CommonDao.today']['data'];
					if(typeof record.data != 'string'){
						projectObj=record.data;
						if(!projectObj['CONTRACT_ID']){
							$("#editContract").text("新增合同");
						}
						var teams=record.teams;
						$("[name='teams']").val(teams);
					}
					if(Project.projectId==''){
						$("#beginDate").val(today);
					}
					$("#projectEditForm select").select2({theme: "bootstrap",placeholder:'请选择'});
					$("#projectEditForm [multiple='multiple']").select2({width:'100%',theme: "bootstrap",placeholder:'请选择'});
					$(".select2-container").css("width","100%");
			 	 });
			 	$("#projectEditForm").on("select2:select",function(e){
					console.log(e.params.data);
				});
				$("#projectEditForm").on("select2:unselecting",function(e){
					console.log(e.params.data);
				
				});
			}});  
		});
		Project.uploadFile = function(){
			var fkId='';
			if(Project.projectId){
				fkId=projectId;
			}else{
				fkId=$("#randomId").val();
			}
			easyUploadFile({callback:'callback',fkId:fkId,source:'project'});
			
		}
		var callback = function(data){
			$("#fileList").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
		}
		
		Project.ajaxSubmitForm = function(){
			if($("#projectEditForm [name='teams']").val()==null){
				layer.msg("请选择项目团队成员.",{icon:7});
				return;
			}
			if(form.validate("#projectEditForm")){
				if(Project.projectId){
					Project.updateData(); 
				}else{
					Project.insertData(); 
				}
			};
		}
		Project.insertData = function(flag) {
			var ids = $("#projectEditForm [name='teams']").val()||[];
			ids.push($("#projectEditForm [name='project.PO']").val());
			$("#projectEditForm [name='teams']").val(ids);
			
			$("#projectEditForm #projectId").val($("#randomId").val());
			var data = form.getJSONObject("#projectEditForm");
			ajax.remoteCall("${ctxPath}/servlet/project?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadProjectList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Project.updateData = function(flag) {
			var ids = $("#projectEditForm [name='teams']").val()||[];
			ids.push($("#projectEditForm [name='project.PO']").val());
			$("#projectEditForm [name='teams']").val(ids);
			
			var data = form.getJSONObject("#projectEditForm");
			ajax.remoteCall("${ctxPath}/servlet/project?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
						reloadProjectList();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Project.editContract = function(){
			popup.layerShow({type:2,maxmin:true,anim:0,scrollbar:false,offset:'l',area:['750px','100%'],url:'${ctxPath}/pages/project/contract/project-contract-edit.jsp',title:'编辑合同',data:{projectId:projectId,contractId:projectObj['CONTRACT_ID']}});
		}
		
		function fillDate(val){
			var v = addDate(val);
			if(v.indexOf("NaN")>-1){
				layer.msg("时间格式不正确,请检查浏览器兼容性或者换chome");
			}else{
				$("#endDate").val(v);
			}
		}
		function addDate(days) {
            if (days == undefined || days == '') {
                days = 1;
            }
            var d=$("#beginDate").val();
            if(d){
	            var date = new Date(d.replace(/-/g,'/'));
	            date.setDate(date.getDate() + days);
	            var month = date.getMonth() + 1;
	            var day = date.getDate();
	            var h = date.getHours();
	            var m = date.getMinutes();
	            return date.getFullYear() + '-' + getFormatDate(month) + '-' + getFormatDate(day)+" "+ getFormatDate(h)+":"+ getFormatDate(m);
            }else{
            	return "";
            }
        }
		function getFormatDate(arg) {
            if (arg == undefined || arg == '') {
                return '';
            }
            var re = arg + '';
            if (re.length < 2) {
                re = '0' + re;
            }
            return re;
        }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>