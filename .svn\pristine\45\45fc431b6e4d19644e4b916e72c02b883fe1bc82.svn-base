<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>考勤</title>
	<style>
		.ibox-content{padding: 15px 0px;}
		.deptDateStat {
		    position: fixed;
		    top: 40%;
		    right: 0px;
		    text-align: center;
		    width: 30px;
		    background: #52a4ef;
		    color: rgb(255, 255, 255);
		    padding: 10px 8px;
		    outline: none;
		    border: 1px solid rgb(221, 221, 221);
		    border-radius: 3px;
		    z-index: 3333;
		    cursor: pointer;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form class="form-inline" id="kqSearchForm">
        		<button class="deptDateStat" type="button" onclick="deptKq();">部门统计</button>
       	    	<input name="hrFlag" type="hidden" value="${param.hrFlag}"/>
       	    	<input name="batchId" type="hidden" value="${param.batchId}"/>
       	    	<input name="userId" type="hidden" value="${param.userId}"/>
             	<div class="ibox">
	             	<div class="ibox-content">
	             	<div class="layui-tab layui-tab-brief">
					  	<ul class="layui-tab-title">
					   		 <li class="layui-this">考勤查询</li>
					   	 	 <li>迟到记录</li>
					   	 	 <li>漏打卡记录</li>
					   	 	 <li>旷工记录</li>
					   	 	 <li>汇总报表</li>
					     </ul>
						 <div class="form-group mb-5 mt-10 ml-20">
						     <div class="input-group input-group-sm" style="width: 140px;">
						 		<span class="input-group-addon">姓名</span>
							 	<input class="form-control input-sm" name="userName">
							 </div>
						     <div class="input-group input-group-sm" style="width: 140px;">
						 		<span class="input-group-addon">部门</span>
							 	<input class="form-control input-sm" name="deptName">
							 </div>
							 <div class="input-group input-group-sm ml-5">
								 <span class="input-group-addon">考勤月份</span>	
								 <input type="text" name="monthId" data-laydate="{type:'month'}" class="form-control input-sm" style="width: 90px;">
					    	</div>
							 <div class="input-group input-group-sm ml-5">
								 <span class="input-group-addon">考勤日期</span>	
								 <input type="text" name="beginDate" data-mars="CommonDao.monthBegin" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
								 <input type="text" name="endDate" data-laydate="{type:'date'}" class="form-control input-sm" style="width: 90px;">
					    	</div>
					    	 <div class="input-group input-group-sm">
							 	<button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 </div>
						  </div>
					     <div class="layui-tab-content">
					       <div class="layui-tab-item layui-show">
		              		  <table id="kqDataDetail"></table>
					       </div>
					       <div class="layui-tab-item">
					       		<table id="kqLateList"></table>
					       </div>
					       <div class="layui-tab-item">
					       		<table id="loseDkList"></table>
					       </div>
					       <div class="layui-tab-item">
					       		<table id="notWorkDkList"></table>
					       </div>
					       <div class="layui-tab-item">
					       		<table id="reportList"></table>
					       </div>
				        </div>
					</div>
                </div>
            </div> 
        </form>
        
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		
		var kqList={
			init:function(){
				$("#kqSearchForm").initTable({
					mars:'KqDao.list',
					id:"kqDataDetail",
					cellMinWidth:30,
					limit:20,
					height:'full-120',
					cols: [[
		             {
						title: '编号',
						type:'numbers'
					 },{
					    field: 'STAFF_ID',
						title: '工号',
						align:'left'
					},{
					    field: 'USER_NAME',
						title: '姓名',
						align:'left'
					},{
					    field: 'DK_DATE',
						title: '打卡日期',
						align:'center'
					},{
					    field: 'DK_RANK',
						title: '班次',
						align:'left'
					},{
					    field: 'SIGN_IN',
						title: '签入时间',
						align:'left'
					},{
					    field: 'SIGN_OUT',
						title: '签出时间',
						align:'left'
					},{
					    field: 'LATE_TIME',
						title: '迟到时间',
						align:'left'
					},{
					    field: 'OVER_TIME',
						title: '加班时间',
						align:'left'
					},{
					    field: 'NOT_WORK',
						title: '是否旷工',
						align:'left'
					},{
					    field: 'TRUE_TO',
						title: '实到',
						align:'left'
					},{
					    field: 'SUM_TIME',
						title: '上班总时间',
						align:'left'
					},{
					    field: 'REMARK',
						title: '例外情况',
						align:'left'
					},{
					    field: 'DEPT',
						title: '部门',
						align:'left'
					}
					]]}
				);
				$("#kqSearchForm").initTable({
					mars:'KqDao.list',
					id:"kqLateList",
					cellMinWidth:30,
					data:{'lateFlag':'1'},
					limit:20,
					totalRow:false,
					height:'full-120',
					cols: [[
		             {
						title: '编号',
						type:'numbers'
					 },{
					    field: 'STAFF_ID',
						title: '工号',
						align:'left'
					},{
					    field: 'USER_NAME',
						title: '姓名',
						align:'left'
					},{
					    field: 'DK_DATE',
						title: '打卡日期',
						align:'center'
					},{
					    field: 'DK_RANK',
						title: '班次',
						hide:true,
						align:'left'
					},{
					    field: 'SIGN_IN',
						title: '签入时间',
						align:'left'
					},{
					    field: 'SIGN_OUT',
						title: '签出时间',
						align:'left'
					},{
					    field: 'LATE_TIME',
						title: '迟到时间',
						align:'left',
						totalRow:true
					},{
					    field: 'OVER_TIME',
						title: '加班时间',
						align:'left',
						totalRow:true
					},{
					    field: 'NOT_WORK',
						title: '是否旷工',
						align:'left'
					},{
					    field: 'TRUE_TO',
						title: '实到',
						align:'left'
					},{
					    field: 'SUM_TIME',
						title: '上班总时间',
						align:'left'
					},{
					    field: 'REMARK',
						title: '例外情况',
						align:'left'
					},{
					    field: 'DEPT',
						title: '部门',
						align:'left'
					}
					]]}
				);
				$("#kqSearchForm").initTable({
					mars:'KqDao.list',
					id:"loseDkList",
					cellMinWidth:30,
					data:{'missedPunchFlag':'1'},
					limit:20,
					totalRow:false,
					height:'full-120',
					cols: [[
					{
						field:'KQ_ID',
						title:'操作',
						hide:false,
						templet:function(row){
							if(row.FLOW_ID){
								return '已申请';								
							}else{
								return '<div><span lay-event="applyBdk" class="btn btn-xs btn-default">+补签流程</span></div>';
							}
						}
						
					},
		             {
						title: '编号',
						type:'numbers'
					 },{
					    field: 'STAFF_ID',
						title: '工号',
						align:'left'
					},{
					    field: 'DEPT',
						title: '部门',
						align:'left'
					},{
					    field: 'USER_NAME',
						title: '姓名',
						align:'left'
					},{
					    field: 'DK_DATE',
						title: '打卡日期',
						align:'center'
					},{
					    field: 'DK_RANK',
						title: '班次',
						hide:true,
						align:'left'
					},{
					    field: 'SIGN_IN',
						title: '签入时间',
						align:'left'
					},{
					    field: 'SIGN_OUT',
						title: '签出时间',
						align:'left'
					},{
					    field: 'LATE_TIME',
						title: '迟到时间',
						align:'left',
						totalRow:true
					},{
					    field: 'OVER_TIME',
						title: '加班时间',
						align:'left',
						totalRow:true
					},{
					    field: 'NOT_WORK',
						title: '是否旷工',
						align:'left'
					},{
					    field: 'TRUE_TO',
						title: '实到',
						align:'left'
					},{
					    field: 'SUM_TIME',
						title: '上班总时间',
						align:'left'
					},{
					    field: 'REMARK',
						title: '例外情况',
						align:'left'
					}
					]]}
				);
				$("#kqSearchForm").initTable({
					mars:'KqDao.list',
					id:"notWorkDkList",
					cellMinWidth:30,
					data:{'absenteeismFlag':'1'},
					limit:20,
					totalRow:false,
					height:'full-120',
					cols: [[
		             {
						title: '编号',
						type:'numbers'
					 },{
					    field: 'STAFF_ID',
						title: '工号',
						align:'left'
					},{
					    field: 'USER_NAME',
						title: '姓名',
						align:'left'
					},{
					    field: 'DK_DATE',
						title: '打卡日期',
						align:'center'
					},{
					    field: 'DK_RANK',
						title: '班次',
						hide:true,
						align:'left'
					},{
					    field: 'SIGN_IN',
						title: '签入时间',
						align:'left'
					},{
					    field: 'SIGN_OUT',
						title: '签出时间',
						align:'left'
					},{
					    field: 'LATE_TIME',
						title: '迟到时间',
						align:'left',
						totalRow:true
					},{
					    field: 'OVER_TIME',
						title: '加班时间',
						align:'left',
						totalRow:true
					},{
					    field: 'NOT_WORK',
						title: '是否旷工',
						align:'left'
					},{
					    field: 'TRUE_TO',
						title: '实到',
						align:'left'
					},{
					    field: 'SUM_TIME',
						title: '上班总时间',
						align:'left'
					},{
					    field: 'REMARK',
						title: '例外情况',
						align:'left'
					},{
					    field: 'DEPT',
						title: '部门',
						align:'left'
					}
					]]}
				);
				
				$("#kqSearchForm").initTable({
					mars:'KqDao.report',
					id:"reportList",
					cellMinWidth:30,
					limit:20,
					totalRow:false,
					toolbar:true,
					height:'full-120',
					cols: [[
		             {
						title: '编号',
						type:'numbers'
					 },{
					    field: 'STAFF_ID',
						title: '工号',
						align:'left'
					},{
					    field: 'USER_NAME',
						title: '姓名',
						align:'left'
					},{
					    field: 'WORK_DAY',
						title: '工作天数',
						align:'center'
					},{
					    field: 'NOT_WORK',
						title: '缺席天数',
						align:'left'
					},{
					    field: 'LOSE_DK',
						title: '漏打卡次数',
						align:'left'
					},{
					    field: 'LATE_TIME_COUNT',
						title: '迟到次数',
						align:'left'
					},{
					    field: 'LATE_TIME',
						title: '迟到时间/分钟',
						align:'left',
						totalRow:true,
						templet:function(row){
							return Number(row['LATE_TIME']);
						}
					},{
					    field: 'OVER_TIME_COUNT',
						title: '加班次数',
						align:'left'
					},{
					    field: 'OVER_TIME',
						title: '总加班时间',
						align:'left',
						templet:function(row){
							return Number(row['OVER_TIME']);
						}
					},{
					    field: 'OVER_TIME',
						title: '平均加班/分钟',
						align:'left',
						totalRow:true,
						templet:function(row){
							if(Number(row.OVER_TIME_COUNT)==0){
								return 0;
							}else{
								return (row.OVER_TIME/row.WORK_DAY).toFixed(0);
							}
						}
					}
					]]}
				);
			}
		}
		 $(function(){
			 $('#kqSearchForm').render({success:function(){
				 kqList.init();
			 }});
		});
		
		function queryData(){
			$("#kqSearchForm").queryData({id:'kqDataDetail'});
			$("#kqSearchForm").queryData({id:'kqLateList',data:{'lateFlag':'1'}});
			$("#kqSearchForm").queryData({id:'loseDkList',data:{'missedPunchFlag':'1'}});
			$("#kqSearchForm").queryData({id:'notWorkDkList',data:{'absenteeismFlag':'1'}});
			$("#kqSearchForm").queryData({id:'reportList',data:{}});
		}
		
		function applyBdk(data){
			popup.closeTab({callback:function(){
				popup.openTab({id:'applyBdk',url:'/yq-work/web/flow?flowCode=hr_kq_yc&handle=edit',title:'流程申请',data:{kdDate:dateIdFormat(data['DK_DATE']),kqId:data['KQ_ID']}});
			}});
		}
		
		function deptKq(){
			popup.openTab({id:'deptKq',url:'/yq-work/pages/ehr/kq/kq-dept-stat.jsp',title:'部门考勤',data:{}});
			
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>