package com.yunqu.work.dao.project;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name="ProjectRiskDao")
public class ProjectRiskDao extends AppDaoContext {

    @WebControl(name="record",type=Types.RECORD)
    public JSONObject record(){
        String riskId = param.getString("projectRisk.RISK_ID");
        if(StringUtils.notBlank(riskId)){
            return queryForRecord("select * from yq_project_risk where risk_id = ?",riskId);
        }else {
            return getJsonResult(new JSONObject());
        }
    }

    @WebControl(name="projectRiskList",type = Types.LIST)
    public JSONObject projectRiskList(){
    	EasySQL sql = getEasySQL("select t1.* from yq_project_risk t1");
    	sql.append("where 1=1");
    	sql.append(param.getString("projectId")," and t1.project_id = ?");
    	setOrderBy(sql,"order by t1.create_time desc");
    	return queryForList(sql.getSQL(),sql.getParams());
    }
    
    
    @WebControl(name="riskList",type = Types.LIST)
    public JSONObject riskList(){
        EasySQL sql = getEasySQL("select t1.* from yq_project_risk t1,yq_project t2 where t1.project_id = t2.project_id");
        sql.append(param.getString("projectId")," and t1.project_id = ?");
        sql.append(param.getInteger("riskClass")," and t1.risk_class = ?");
        sql.append(param.getString("riskBelong")," and t1.risk_belong like ?");
        sql.append(param.getInteger("riskLevel")," and t1.risk_level = ?");
        sql.append(param.getInteger("riskState")," and t1.risk_state = ?");
        sql.appendLike(param.getString("ownerName"),"and t1.owner_name like ?");
        sql.appendLike(param.getString("creatorName"),"and t1.creator_name like ?");
        sql.append(param.getString("solOwner")," and t1.sol_owner = ?");
        sql.append(param.getString("creator")," and t1.creator = ?");
        setOrderBy(sql,"order by t1.create_time desc");
        return queryForPageList(sql.getSQL(),sql.getParams());
    }

    private void setOrderBy(EasySQL sql,String defaultOrder) {
        String sortName =  param.getString("sortName");
        String sortType =  param.getString("sortType");
        if(StringUtils.notBlank(sortName)) {
            sql.append("order by t1.").append(sortName).append(sortType);
        }else {
            sql.append(defaultOrder);
        }
    }

    @WebControl(name="riskCountStat",type=Types.RECORD)
    public JSONObject riskCountStat(){
        EasySQL easySQL = getEasySQL("select ");
        easySQL.append(0,"SUM(CASE WHEN t1.risk_state = ? THEN 1 ELSE 0 END) AS a,");
        easySQL.append(1,"SUM(CASE WHEN t1.risk_state = ? THEN 1 ELSE 0 END) AS b");
        easySQL.append("from yq_project_risk t1 where 1=1");
        easySQL.append(param.getString("projectId")," and t1.project_id = ?");
        JSONObject result = queryForRecord(easySQL.getSQL(), easySQL.getParams());
        JSONObject data = result.getJSONObject("data");
        String[] keys=new String[]{"A","B"};
        for(int i=0;i<keys.length;i++){
            if(StringUtils.isBlank(data.getString(keys[i]))){
                data.put(keys[i],"0");
            }
        }
        result.put("data", data);
        return result;
    }

}
