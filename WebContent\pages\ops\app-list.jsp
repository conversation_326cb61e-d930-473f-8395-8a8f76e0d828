<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>版本管理</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5> 应用列表  </h5>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">应用名称</span>	
							 <input type="text" name="appName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="list.add()">+ 添加</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
  			    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.addVersion">发版</a>
  			    <a class="layui-btn layui-btn-info layui-btn-xs" lay-event="list.detail">下载</a>
 				{{if currentUserId == CREATOR || currentUserId == APP_OWNER || isSuperUser}}<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.edit">编辑</a>{{/if}}
 				{{if currentUserId == CREATOR || currentUserId == APP_OWNER || isSuperUser}}<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="list.del">删除</a>{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			list.init();
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'AppDao.list',
					cols: [[
					 {type:'numbers',title:'序号'},
		             {
					    field: 'APP_NAME',
						title: '应用名称',
						align:'center',
						event:'list.detail',
						style:'color:#4b89dc;cursor: pointer;'
					},
		             {
					    field: 'LAST_VERSION_TIME',
						title: '最新版本日期',
						align:'center'
					},{
					    field: 'LAST_PUBLISHER',
						title: '最后发版人',
						align:'center',
						templet:function(row){
							return getUserName(row.LAST_PUBLISHER);
						}
					},{
					    field: 'APP_OWNER',
						title: '应用负责人',
						align:'center',
						templet:function(row){
							return getUserName(row.APP_OWNER);
						}
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center'
					},{
						title: '操作',
						align:'center',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							row['isSuperUser']=isSuperUser;
							return renderTpl('bar',row);
						}
					}
					]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			edit:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/ops/app-edit.jsp',title:'编辑',data:{groupId:data.GROUP_ID}});
			},
			addVersion:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/ops/version-edit.jsp',title:'发版本',data:{appName:data.APP_NAME,groupId:data.GROUP_ID}});
			},
			detail:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['900px','100%'],url:'${ctxPath}/pages/ops/app-detail.jsp',title:data.APP_NAME,data:{groupId:data.GROUP_ID,op:'detail'}});
			},
			add:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/ops/app-edit.jsp',title:'新增版本'});
			},
			del:function(data){
				layer.confirm("确认要删除吗?",{icon:3},function(){
					ajax.remoteCall("${ctxPath}/servlet/app?action=delete",{'app.APP_ID':data.APP_ID},function(result) { 
						if(result.state == 1){
							layer.msg(result.msg,{icon:1,time:1200},function(){
								list.query();
								layer.closeAll();
							});
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					});
					
				});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>