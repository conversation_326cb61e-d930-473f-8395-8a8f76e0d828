package com.yunqu.work.dao.project;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ProjectModel;
import com.yunqu.work.service.LookLogService;

@WebObject(name="ProjectDao")
public class ProjectDao extends AppDaoContext {
	
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		ProjectModel model=new ProjectModel();
		model.setColumns(getParam("project"));
		if(StringUtils.notBlank(model.getProjectId())){
			JSONObject jsonObject=queryForRecord(model);
			String sql="select USER_ID from  yq_project_team where PROJECT_ID = ?";
			List<EasyRow> list;
			try {
				list = this.getQuery().queryForList(sql, model.getProjectId());
				if(list!=null&&list.size()>0){
					String[] teams=new String[list.size()];
					for(int i=0;i<list.size();i++){
						teams[i]=list.get(i).getColumnValue(1);
					}
					JSONObject data=jsonObject.getJSONObject("data");
					data.put("teams",teams);
					jsonObject.put("teams", teams);
					jsonObject.put("data", data);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
			if(StringUtils.notBlank(model.getString("PROJECT_TYPE"))){
				LookLogService.getService().addLog(getUserId(),model.getProjectId());
			}
			return jsonObject;
		}else{
			return getJsonResult(new JSONObject());
		}
	}
	@WebControl(name="dict",type=Types.DICT)
	public JSONObject dict(){
		return getDictByQuery("select PROJECT_ID,PROJECT_NAME from yq_project where is_delete = 0");
	}
	
	@WebControl(name="modules",type=Types.DICT)
	public JSONObject modules(){
		String projectId = param.getString("projectId");
		return getDictByQuery("select module_id,module_name from yq_project_module where project_id = ?",projectId);
	}
	
	@WebControl(name="myProjectDic",type=Types.DICT)
	public JSONObject myProjectDic(){
		return getDictByQuery("select t1.PROJECT_ID,t1.PROJECT_NAME from yq_project t1 INNER JOIN yq_project_team t2 on t2.PROJECT_ID=t1.PROJECT_ID where t2.USER_ID= ?",getUserId());
	}
	/**
	 * 全部项目
	 * @return
	 */
	@WebControl(name="projectList",type=Types.LIST)
	public JSONObject projectList(){
		if(isSuperUser()||getUserPrincipal().isRole("PROJECT_MANAGER")){
			EasySQL sql=getEasySQL("select t1.*,");
			sql.append(10,"SUM(CASE WHEN t2.task_state = ? THEN 1 ELSE 0 END) AS a,");
			sql.append(20,"SUM(CASE WHEN t2.task_state = ? THEN 1 ELSE 0 END) AS b,");
			sql.append(30,"SUM(CASE WHEN t2.task_state = ? THEN 1 ELSE 0 END) AS c,");
			sql.append(30,"SUM(CASE WHEN t2.task_state < ? THEN 1 ELSE 0 END) AS d,");
			sql.append(30,"SUM(CASE WHEN t2.task_state >= ? THEN 1 ELSE 0 END) AS k,");
			sql.append(40,"SUM(CASE WHEN t2.task_state >= ? THEN 1 ELSE 0 END) AS f,");
			sql.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t2.deadline_at < ? and t2.task_state < 30  THEN 1 ELSE 0 END) AS g,");
			sql.append("SUM(CASE WHEN t2.task_state > 0 THEN 1 ELSE 0 END) AS h");
			
			sql.append("from yq_project t1 LEFT JOIN yq_task t2 ON t1.PROJECT_ID = t2.project_id  where 1=1");
			sql.append("and t1.is_delete = 0");
			sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
			sql.append(param.getString("projectState"),"and t1.project_state = ?");
			sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
			sql.append(param.getString("userId"),"and t1.PO = ?");
			sql.append(param.getString("projectType"),"and t1.project_type = ?");
			sql.append(param.getString("deptId"),"and t1.dept_id = ?");
			sql.append("GROUP BY t1.PROJECT_ID");
			String field=param.getString("field");
			String order=param.getString("order");
			if(StringUtils.notBlank(field)&&StringUtils.isNotEmpty(order)){
				sql.append("order by").append(field).append(order);
				
			}else{
				sql.append("order by d desc,h desc");
			}
			return queryForPageList(sql.getSQL(), sql.getParams());
		}else{
			return emptyPage();
		}
	}
	/**
	 * 我创建的项目
	 * @return
	 */
	@WebControl(name="myCreateProjectList",type=Types.LIST)
	public JSONObject myCreateProjectList(){
		EasySQL sql=getEasySQL("select count(1) count,t1.* from yq_project t1 INNER JOIN yq_task t2 on t1.project_id=t2.project_id  where 1=1");
		sql.append("and t1.is_delete = 0");
		sql.append(getUserId(),"and t2.CREATOR=  ? ");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("GROUP BY t1.project_id");
		sql.append("order by count desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 我负责的项目
	 * @return
	 */
	@WebControl(name="myMangerProjectList",type=Types.LIST)
	public JSONObject myMangerProjectList(){
		EasySQL sql=getEasySQL("select t1.* from yq_project t1  where 1=1");
		sql.append("and t1.is_delete = 0 and");
		sql.append(getUserId(),"(t1.PO=  ? or");
		sql.append(getUserId()," t1.PROJECT_PO=  ? )");
		
		sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
		sql.appendLike(param.getString("projectName"),"and PROJECT_NAME like ?");
		sql.append(param.getString("projectState"),"and project_state = ?");
		sql.append("order by CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 我关注的项目
	 * @return
	 */
	@WebControl(name="myFavoriteProject",type=Types.LIST)
	public JSONObject myFavoriteProject(){
		EasySQL sql=getEasySQL("select t1.*,t2.favorite_time from yq_project t1 INNER JOIN yq_favorite t2 on t2.fk_id=t1.PROJECT_ID where  1=1");
		sql.append(getUserId(),"and t2.favorite_by= ?");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.append("and t1.is_delete = 0");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("ORDER BY t2.favorite_time desc ");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 我参与的项目
	 * @return
	 */
	@WebControl(name="myJoinProjectList",type=Types.LIST)
	public JSONObject myProjectList(){
		EasySQL sql=getEasySQL("select t1.* from yq_project t1 INNER JOIN yq_project_team t2 ON t2.PROJECT_ID=t1.PROJECT_ID where 1=1");
		sql.append(getUserId(),"and t2.USER_ID=  ? ");
		sql.appendLike(param.getString("projectName"),"and t1.PROJECT_NAME like ?");
		sql.appendLike(param.getString("projectNo"),"and t1.project_no like ?");
		sql.append(param.getString("projectState"),"and t1.project_state = ?");
		sql.append("and t1.is_delete = 0");
		sql.append("order by t1.CREATE_TIME desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目团队
	 * @return
	 */
	@WebControl(name="projectTeams",type=Types.LIST)
	public JSONObject projectTeams(){
		EasySQL sql=getEasySQL("select t2.USERNAME,t2.MOBILE,t2.EMAIL,t2.DEPTS,t3.LAST_LOGIN_TIME,t1.* from yq_project_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user_login t3 on t3.USER_ID=t2.USER_ID where 1 = 1");
		sql.append("and t2.STATE = 0");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",false);
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目团队
	 * @return
	 */
	@WebControl(name="projectTeamsDict",type=Types.DICT)
	public JSONObject projectTeamsDict(){
		EasySQL sql=getEasySQL("select t2.USER_ID,t2.USERNAME from yq_project_team t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID where 1 = 1");
		sql.append("and t2.STATE = 0");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",false);
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目周报
	 * @return
	 */
	@WebControl(name="projectWeeklys",type=Types.LIST)
	public JSONObject projectWeeklys(){
		EasySQL sql=getEasySQL("select t2.USERNAME,t2.MOBILE,t2.EMAIL,t2.DEPTS,t1.* from YQ_PROJECT_WEEKLY t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.CREATOR where 1 = 1");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",false);
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目状态更新日志
	 * @return
	 */
	@WebControl(name="projectStateLog",type=Types.LIST)
	public JSONObject projectStateLog(){
		EasySQL sql=getEasySQL("select * from yq_project_state_log where 1 = 1");
		sql.append(param.getString("projectId")," and PROJECT_ID = ?",false);
		return queryForList(sql.getSQL(), sql.getParams());
	}
	/**
	 * 项目模块
	 * @return
	 */
	@WebControl(name="projectModule",type=Types.LIST)
	public JSONObject projectModule(){
		EasySQL sql=getEasySQL("select t1.* from yq_project_module t1  where 1 = 1");
		sql.append(param.getString("projectId")," and t1.PROJECT_ID = ?",true);
		JSONObject result=queryForList(sql.getSQL(), sql.getParams());
		JSONArray array=result.getJSONArray("data");
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("MODULE_NAME", "点击这添加模块");
		array.add(jsonObject);
		result.put("data",array);
		return result;
	}
	
	@WebControl(name="deptWorkhourStat",type=Types.TEMPLATE)
	public JSONObject deptWorkhourStat(){
		EasySQL sql=getEasySQL("select temp.*,t1.PROJECT_NAME from (select project_id,sum(work_time) time from yq_project_work_hour t1   where 1=1");
		String deptId=param.getString("deptId");
		if(!"0".equals(deptId)){
			if(StringUtils.isBlank(deptId)){
				sql.append(getDeptId(),"and dept_id = ? ");
			}else{
				sql.append(deptId,"and dept_id = ? ");
			}
		}
		
		String startDate=param.getString("startDate");
		String endDate=param.getString("endDate");
		
		if(StringUtils.notBlank(startDate)){
			sql.append(startDate.replaceAll("-",""),"and t1.month_id >= ?");
		}
		if(StringUtils.notBlank(endDate)){
			sql.append(endDate.replaceAll("-",""),"and t1.month_id <= ?");
		}
		
		sql.append("GROUP BY project_id ) temp,yq_project t1 where 1=1");
		sql.append("and t1.PROJECT_ID=temp.project_id");
		sql.append("order by temp.time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectWorkhourDetail",type=Types.TEMPLATE)
	public JSONObject projectWorkhourDetail(){
		EasySQL sql=getEasySQL("select t1.*,t2.DEPTS,t2.USERNAME from yq_project_work_hour t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t1.worker=t2.USER_ID where 1=1");
		sql.append(param.getString("projectId"),"and t1.project_id = ?");

		String startDate=param.getString("startDate");
		String endDate=param.getString("endDate");
		
		if(StringUtils.notBlank(startDate)){
			sql.append(startDate.replaceAll("-",""),"and t1.month_id >= ?");
		}
		if(StringUtils.notBlank(endDate)){
			sql.append(endDate.replaceAll("-",""),"and t1.month_id <= ?");
		}

//		String deptId=param.getString("deptId");
//		if(!"0".equals(deptId)){
//			if(StringUtils.isBlank(deptId)){
//				sql.append(getDeptId(),"and t1.dept_id = ? ");
//			}else{
//				sql.append(deptId,"and t1.dept_id = ? ");
//			}
//		}
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="queryDeptUser",type=Types.TEMPLATE)
	public JSONObject queryDeptUser(){
		String deptId=getDeptId();
		if(StringUtils.isBlank(deptId)){
			return getJsonResult(new JSONArray());
		}else{
			JSONObject result = null ;
			if(hasRole("DEPT_MGR")) {
				result = queryForList("select t2.USER_ID,t2.USERNAME from "+Constants.DS_MAIN_NAME+".easi_dept_user t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID where t2.STATE = 0 and t1.DEPT_ID= ?",new Object[]{deptId});
			}else {
				result = queryForList("select t2.USER_ID,t2.USERNAME from "+Constants.DS_MAIN_NAME+".easi_user t2  where t2.user_id = ?",new Object[]{getUserId()});
			}
			JSONArray data = result.getJSONArray("data");
			String[] userIds=new String[data.size()];
			for(int i=0;i<data.size();i++){
				JSONObject jsonObject=data.getJSONObject(i);
				userIds[i]=jsonObject.getString("USER_ID");
			}
			
			EasySQL sql=new EasySQL("select t3.* from yq_project_work_hour t3 where 1=1");
			sql.appendIn(userIds, "and t3.WORKER");
			String startDate=param.getString("startDate");
			String endDate=param.getString("endDate");
			if(StringUtils.notBlank(startDate)){
				sql.append(startDate.replaceAll("-",""),"and t3.month_id >= ?");
			}
			if(StringUtils.notBlank(endDate)){
				sql.append(endDate.replaceAll("-",""),"and t3.month_id <= ?");
			}
			try {
				result.put("workHours", this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl()));
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
			return  result;
			
		}
	  }
		@WebControl(name="workHourList",type=Types.PAGE)
		public JSONObject workHourList(){
			String deptId=param.getString("deptId");
			if(StringUtils.isBlank(deptId)){
				deptId=getDeptId();
			}
			EasySQL sql=new EasySQL("select t1.*,t2.project_type,t2.PROJECT_NO,t3.USERNAME,t3.DEPTS,t3.DATA1  from yq_project_work_hour t1 INNER JOIN yq_project t2 on t1.project_id=t2.PROJECT_ID INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t3 on t3.USER_ID=t1.worker  where 1=1");
			
			String startDate=param.getString("startDate");
			String endDate=param.getString("endDate");
			
			if(StringUtils.notBlank(startDate)){
				sql.append(startDate.replaceAll("-",""),"and t1.month_id >= ?");
			}
			if(StringUtils.notBlank(endDate)){
				sql.append(endDate.replaceAll("-",""),"and t1.month_id <= ?");
			}
			
			
			sql.append(param.getString("projectType"),"and t2.PROJECT_TYPE = ?");
			sql.append(param.getString("projectId"),"and t2.PROJECT_ID = ?");
			String[] userIds = null;
			if(!"0".equals(deptId)){
				JSONObject result=queryForList("select t2.USER_ID,t2.USERNAME from "+Constants.DS_MAIN_NAME+".easi_dept_user t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID where t2.STATE = 0 and t1.DEPT_ID= ?",new Object[]{deptId});
				JSONArray data = result.getJSONArray("data");
				userIds=new String[data.size()];
				for(int i=0;i<data.size();i++){
					JSONObject jsonObject=data.getJSONObject(i);
					userIds[i]=jsonObject.getString("USER_ID");
				}
				sql.appendIn(userIds, "and t1.WORKER");
			}
			sql.append("ORDER BY t1.creator");
			return queryForPageList(sql.getSQL(),sql.getParams());
			
				
	}
		
	@WebControl(name="fileListDetail",type=Types.TEMPLATE)
	public JSONObject fileListDetail(){
		EasySQL sql = new EasySQL("select  t2.project_name,t2.project_no,t1.* from yq_files t1,yq_project t2 where 1=1");
		sql.append("and t1.fk_id = t2.project_id");
		if(!hasRole("PROJECT_MANAGER")&&!isSuperUser()) {
			sql.append(getUserId(),"and t2.project_id in (select t3.PROJECT_ID from yq_project_team t3 where t3.PROJECT_ID = t2.PROJECT_ID and t3.USER_ID = ?)");
		}
		sql.append("project","and t1.source = ?");
		sql.appendLike(param.getString("projectNo"),"and t2.project_no like ? ");
		sql.appendLike(param.getString("projectName"),"and t2.project_name like ? ");
		sql.append(" order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectLink",type=Types.TEMPLATE)
	public JSONObject projectLink(){
		EasySQL sql=getEasySQL("select  t2.project_name,t2.project_no,t1.* from yq_project_link  t1,yq_project t2 where 1=1");
		sql.append("and t1.project_id = t2.project_id");
		if(!hasRole("PROJECT_MANAGER")&&!isSuperUser()) {
			sql.append(getUserId(),"and t2.project_id in (select t3.PROJECT_ID from yq_project_team t3 where t3.PROJECT_ID = t2.PROJECT_ID and t3.USER_ID = ?)");
		}
		sql.appendLike(param.getString("projectNo"),"and t2.project_no like ? ");
		sql.appendLike(param.getString("projectName"),"and t2.project_name like ? ");
		sql.append(0,"and state = ?");
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}	
		
		

}
