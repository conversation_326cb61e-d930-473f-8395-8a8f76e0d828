<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowTitle}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 140px;">标题</td>
					  			<td style="width: 38%;">
					  				<input type="text" data-rules="required" class="form-control input-sm" value="${staffInfo.userName}${staffInfo.staffNo}${flow.flowTitle}"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 140px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">所属项目</td>
					  			<td>
					  				<input name="apply.data1" type="hidden">
									<input type="text" data-rules="required" onclick="singleProject(this)" readonly="readonly" data-rules="required" name="apply.data2" class="form-control input-sm"/>
					  			</td>
					  			<td class="required">需求预算</td>
					  			<td>
					  				<input type="text" data-rules="required" placeholder="若无填写0" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  		</tr>
				  			<tr>
					  			<td class="required">开发负责人</td>
					  			<td>
					  				<input type="hidden" name="apply.data4"/>
					  				<input type="text" data-rules="required" onclick="singleUser(this);" data-ref-update="prev" readonly="readonly" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  			<td class="required">销售经理</td>
					  			<td>
					  				<input type="hidden" name="apply.data6"/>
					  				<input type="text" data-rules="required" onclick="singleUser(this);" data-ref-update="prev" readonly="readonly" class="form-control input-sm" name="apply.data7"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">申请原因</td>
					  			<td colspan="3">
									<textarea style="height: 60px;" data-rules="required" class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td>文件</td>
					  			<td colspan="3" class="fileList">
					  				<button type="button" class="layui-btn layui-btn-normal layui-btn-sm upload-btn"><i class="layui-icon">&#xe67c;</i>上传附件</button>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12">
		  				<div class="layui-col-md12 mt-10">
					     <div class="layui-table-tool">
							<div class="pull-left">需求列表</div>
							<div class="pull-right unedit-remove">
								<button class="btn btn-sm btn-default" onclick="addTr();" type="button"><i class="layui-icon layui-icon-addition"></i> 新增行</button>
								<button class="btn btn-sm btn-default ml-10" onclick="delTr();" type="button"><i class="layui-icon layui-icon-delete"></i> 删除</button>
								<button class="btn btn-sm btn-default ml-10" onclick="upTr();" type="button"><i class="layui-icon layui-icon-up"></i> 上移</button>
								<button class="btn btn-sm btn-default ml-10" onclick="downTr();" type="button"><i class="layui-icon layui-icon-down"></i> 下移</button>
							</div>
					    </div>
					  	<div class="table-responsive" style="min-height: 100px;">
								 <table class="layui-table approve-table" style="margin-top: 0px;">
								   <thead>
								    <tr>
								      <th style="width: 50px;">序号</th>
								      <th>需求描述</th>
								      <th style="width: 120px;">客户期望时间点</th>
								      <th style="width: 100px;">负责人</th>
								      <th style="width: 100px;">工作量</th>
								      <th style="width: 120px;">预计开发开始时间</th>
								      <th style="width: 120px;">预计开发结束时间</th>
								    </tr> 
								  </thead>
								  <tbody data-mars="FlowCommon.items" data-template="flow-items"></tbody>
								  <tbody>
								  	<c:forEach var="item" begin="1000" end="1020">
									    <tr id="item_${item}" data-hide-tr="1" style="display:none;">
									      <td style="width: 50px!important;">
									      	<input type="hidden" name="order_index_${item}"/>
									      	<input type="hidden" value="${item}" name="itemIndex"/>
									        <label class="checkbox checkbox-inline checkbox-success">
										      	 <input tabindex="-1" type="checkbox" name="ids" value="${item}"><span></span>
									        </label>
									      </td>
									      <td>
									      	    <textarea name="data1_${item}" data-rules="required" class="form-control input-sm"></textarea>
									      </td>
									      <td>
									     	   <input name="data2_${item}" data-laydate="{type:'date'}" readonly data-rules="required" class="form-control input-sm">
									      </td>
									      <td>
									      		<input type="hidden" name="data3_${item}">
									      	   <input type="text" class="form-control input-sm" onclick="singleUser(this)" placeholder="开发填写" name="data4_${item}"/>
									      </td>
									      <td>
									     	   <input name="data5_${item}" placeholder="开发填写" class="form-control input-sm">
									      </td>
									      <td>
									      	   <input type="text" data-laydate="{type:'date'}" class="form-control input-sm" placeholder="开发填写" name="data6_${item}"/>
									      </td>
									      <td>
									      	   <input type="text" data-laydate="{type:'date'}" class="form-control input-sm" placeholder="开发填写" name="data7_${item}"/>
									      </td>
									    </tr>
								  	</c:forEach>
								  </tbody>	
								</table>
							</div>
					   </div>
				 
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
			
			<script id="flow-items" type="text/x-jsrender">
  		 {{for data}}
			<tr id="item_{{:ITEM_ID}}">
			  <td>
				<input type="hidden" name="order_index_{{:ITEM_ID}}" value="{{:ITEM_INDEX}}"/>
				<input type="hidden" value="{{:ITEM_ID}}" name="itemIndex"/>
				<span class="edit-remove">{{:ORDER_INDEX}}</span>
				<label class="checkbox checkbox-inline checkbox-success">
					 <input tabindex="-1" type="checkbox" name="ids" value="{{:ITEM_ID}}"><span></span>
				</label>
			  </td>
			  <td>
					<textarea style="height: 40px;resize: none;" maxlength="255" class="form-control input-sm" name="data1_{{:ITEM_ID}}">{{:DATA1}}</textarea>
			  </td>
			  <td>
					<input type="text" class="form-control input-sm" readonly data-rules="required" name="data2_{{:ITEM_ID}}" value="{{:DATA2}}"/>
			  </td>
			  <td>
				  	<input name="data3_{{:ITEM_ID}}" type="hidden" value="{{:DATA3}}">
					<input type="text" class="form-control input-sm" onclick="singleUser(this)" data-text="false" data-rules="required" name="data4_{{:ITEM_ID}}" value="{{:DATA4}}"/>
			  </td>
			  <td>
				  <input name="data5_{{:ITEM_ID}}" data-rules="required" data-text="false" class="form-control input-sm" value="{{:DATA5}}">
			  </td>
			  <td>
					<input type="text" class="form-control input-sm" data-text="false" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',startDate:'%y-%M-%d',doubleCalendar:true,alwaysUseStartDate:true})" name="data6_{{:ITEM_ID}}" value="{{:DATA6}}"/>
			  </td>
			  <td>
					<input type="text" class="form-control input-sm" data-text="false" data-rules="required" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',startDate:'%y-%M-%d',doubleCalendar:true,alwaysUseStartDate:true})" name="data7_{{:ITEM_ID}}" value="{{:DATA7}}"/>
			  </td>
			</tr>
 	     {{/for}}
   	  </script>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({success:function(result){
				if(FlowCore.businessId){
					
				}else{
					addTr();
				}
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			$("[data-hide-tr]").remove();
			var index  = 1;
			$("[name^='order_index_']").each(function(){
				var t = $(this);
				t.val(index);
				index++;
			});
			
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		FlowCore.flowEnd =  function(){
			
		}
		
	   function addTr(){
			var obj  = $("[data-hide-tr]").first();
			if(obj.length==0){
				layer.msg('不能再新增啦',{icon:7,shade: 0.1});
				return;
			}
			obj.removeAttr("data-hide-tr");
			obj.show();
		}

		function delTr(){
			layer.confirm('确定要删除吗',{icon:3,offset:'30px',shade: 0.1,},function(index){
				layer.close(index);
				$("input[name='ids']:checked").each(function(){
					var id = this.value;
					if(id.length>20){
						ajax.remoteCall("${ctxPath}/servlet/flow/fun?action=delItems",{itemId:id},function(result) { 
							if(result.state == 1){
								layer.msg(result.msg);
								$('#item_'+id).remove();
							}else{
								layer.alert(result.msg,{icon: 5});
							}
						});					
					}else{
						$('#item_'+id).remove();
					}
					
				});
			});
		}
			
		function upTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var prevTR  = $('#item_'+id).prev();
				if (prevTR.length > 0) {
					prevTR.insertAfter($('#item_'+id));
				}
			}
		}
		
		function downTr(){
			var obj = $("input[name='ids']:checked");
			if(obj.length==1){
				var id = obj.val();
				var nextTR  = $('#item_'+id).next();
				if (nextTR.length > 0) {
					nextTR.insertBefore($('#item_'+id));
				}
			}
		}
			
		function selctCallBack(){
			
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>