<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>文档库管理</title>
	<style>
		.layui-btn{display: none;}
		.layui-table-hover .layui-btn{
			display: inline-block;
		}
		#searchForm #folderName a,span{color: #999;margin: 0px 3px;}
		#searchForm .btn .caret{color: #fff;}
		/* #searchForm .layui-table-page{display: none;} */
		.layui-tree-icon{height: 16px;width: 16px;line-height: 13px;}
	    .left{
			width: 280px;float: left;margin: -15px;padding: 15px;background-color: #fff;min-height: calc(100vh - 30px);
		}
		.right{
			float: left;width:calc(100% - 280px);
			margin-left: 30px;
			height: 100%;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<input type="hidden" id="randomId" data-mars="CommonDao.randomId"/>
			<input type="hidden" name="folderId" id="folderId" value=""/>
			<div class="ibox-content left">
				<div style="line-height:40px;height: 40px;border-bottom: 1px solid #eee;">
					文件夹列表
				</div>
				<div id="tree"></div>
			
			
			</div>
			<div class="right">
				<div class="ibox">
					<div class="ibox-title clearfix">
		          		 <div class="form-group">
		          		     <h5><span class="fa fa-file"></span> 共享文档库</h5>
		          		     <div class="input-group input-group-sm" style="width: 220px">
								 <span class="input-group-addon">文档库名称</span>	
								 <input type="text" name="folderName" class="form-control input-sm">
						     </div>
							 <div class="input-group input-group-sm">
								 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 </div>
							 <div class="input-group input-group-sm pull-right">
								 <div class="btn-group">
									<button type="button" class="btn btn-sm btn-info" onclick="list.addDoc()">+ 新增文档</button>
									<button type="button" class="btn btn-sm btn-info dropdown-toggle" data-toggle="dropdown">
										<span class="caret"></span>
									</button>
									<ul class="dropdown-menu" role="menu" style="left: -54px">
										<li><a href="javascript:$('#localfile').click()">上传文件</a></li>
										<li><a href="javascript:list.addFolder()">新建文件夹</a></li>
									</ul>
								</div>
							 </div>
		          	     </div>
		              </div> 
						<div class="ibox-content" style="min-height: 300px">
			              	<!-- <p id="folderName" style="height: 40px;line-height: 40px;margin-top: -16px"><a href='javascript:void(0)' onclick='list.goFolder(0,this)'>所有文件夹</a> </p> -->
						   <!--  <table class="layui-hide" id="list"></table> -->
						    <table class="layui-hide" id="files"></table>
						</div>
					</div>
			</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
  			<a class="layui-btn layui-btn-xs  layui-btn-normal" lay-event="list.open">打开</a>
  			{{if currentUserId==MANAGER || currentUserId == CREATOR}}<a class="layui-btn layui-btn-xs" lay-event="list.editFolder">编辑</a>{{/if}}
		</script>
		<script type="text/x-jsrender" id="bar2">
  			<a class="layui-btn layui-btn-xs  layui-btn-normal" lay-event="list.detail">详情</a>
  			{{if currentUserId==MANAGER || currentUserId == CREATOR}}<a class="layui-btn layui-btn-xs" lay-event="list.editDoc">编辑</a>{{/if}}
  			<a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="list.addFavorite">收藏</a>
		</script>
		<form  id="fileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfile" onchange="uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			loadTree();
			//list.loadFolder();
			list.loadFile();
		});
		
		var uploadFile = function(callback){
			$("#randomId").render({success:function(){
				var docId=$("#randomId").val();
				easyUploadFile({callback:'callback',fileMaxSize:(1024*50),fkId:docId,source:'doc'});
			}});
		}
		
		var callback = function(data,params){
			var id=data.id;
			var url=data.url;
			var name=data.name;
			var docId=params.fkId;
			var data={'doc.DOC_ID':docId,'doc.TITLE':name,'doc.DOC_AUTH':1,'doc.FOLDER_ID':$("#folderId").val(),'doc.DOC_DESC':'见附件'};
			ajax.remoteCall("${ctxPath}/servlet/doc?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function loadTree(){
			ajax.remoteCall("${ctxPath}/webcall?action=FolderDao.docFolder",{},function(rs) { 
				var result= rs.data;
				var data = dataToTree(result,{idFiled: 'FOLDER_ID', textFiled: 'FOLDER_NAME', parentField: 'P_FOLDER_ID', childField: '',def:{spread:false}, map: {FOLDER_ID: 'id', FOLDER_NAME: 'title' } });
				data = [{title:'文档目录',spread:true,FOLDER_ID:'0',children:data}];
				layui.use('tree', function(){
				    var tree = layui.tree;
				    tree.render({
				        elem: '#tree',
				        data: data,
				        click: function(obj){
				           	var id = obj.data['FOLDER_ID'];
				            if(id=='0'){
					            $("#folderId").val('');
				           		list.query();
				           		return;
			           		 }else{
					            $("#folderId").val(id);
					           	list.query();
			           		 }
				        }
				   });
				});
				
			});
		}
		var list={
			loadFolder:function(){
				$("#searchForm").initTable({
					mars:'FolderDao.list',
					skin:'line',
					page:false,
					id:'list',
					rowDoubleEvent:'list.open',
					cols: [[
		             {
					    field: 'FOLDER_NAME',
						title: '文档库名称',
						align:'left',
						templet:function(row){
							return '<i class="fa fa-folder-open-o"></i>&nbsp;'+row.FOLDER_NAME;
						}
					},
					{
						title: '操作',
						align:'center',
						width:180,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar1',row);
						}
					},{
					    field: 'MANAGER',
						title: '负责人',
						align:'right',
						width:130,
						templet:function(row){
							return getUserName(row.MANAGER);
						}
					}
					]],done:function(res){
						if(res.data.length==0){
							$("[lay-id='list']").remove();
						}
						$(".layui-table-header").css("display","none");
						$(".layui-table-view").first().css("margin-bottom","0px");
						
					}}
				);
			},
			loadFile:function(){
				$("#searchForm").initTable({
					mars:'FolderDao.files',
					id:'files',
					skin:'line',
					rowDoubleEvent:'list.detail',
					page:true,
					cols: [[
					 {type:'numbers',width:40,align:'center',title:'序号'},
		             {
					    field: 'TITLE',
						title: '文档库名称',
						align:'left',
						style:'padding-left:0px',
						templet:function(row){
							return '<i class="fa fa-file-archive-o"></i>&nbsp;'+row.TITLE;
						}
					},{
						title: '操作',
						align:'center',
						width:180,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar2',row);
						}
					},{
					    field: 'MANAGER',
						title: '发布人',
						align:'right',
						width:120,
						templet:function(row){
							return getUserName(row.MANAGER);
						}
					},{
						field:'CREATE_TIME',
						title:'发布时间',
						width:145
					}
					]],done:function(res){
						//$(".layui-table-header").css("display","none");
						//$("[data-field='TITLE'] .layui-table-cell").css("padding-left",'0px');
						//if(res.data.length==0){
						//	$("[lay-id='files']").remove();
						//}
					}}
				);
			},
			query:function(){
				//$("#searchForm").queryData({id:'list'});
				$("#searchForm").queryData({id:'files'});
			},
			open:function(data,obj){
				$("#folderId").val(data.FOLDER_ID);//设置当前文件夹目录
				$("#folderName").append("<span>/</span> <a href='javascript:void(0)' onclick=\"list.goFolder('"+data.FOLDER_ID+"',this)\">"+data.FOLDER_NAME+"</a>  ");
				list.query();
			},
			editDoc:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/doc/doc-edit.jsp',title:'编辑',data:{'doc.DOC_ID':data.DOC_ID,docId:data.DOC_ID,folderId:data.FOLDER_ID}});
			},
			addDoc:function(){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['750px','100%'],url:'${ctxPath}/pages/doc/doc-edit.jsp',data:{folderId:$("#folderId").val()},title:'新增文档'});
			},
			addFolder:function(){
				popup.layerShow({type:1,maxmin:true,area:['450px','300px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'新增文件夹',data:{id:$("#folderId").val(),source:'0'}});
			},
			editFolder:function(data){
				popup.layerShow({type:1,maxmin:true,area:['450px','300px'],url:'${ctxPath}/pages/doc/folder-edit.jsp',title:'编辑文件夹',data:{folderId:data.FOLDER_ID}});
			},
			detail:function(data){
				popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['800px','100%'],url:'${ctxPath}/pages/doc/doc-detail.jsp',title:'详情',data:{op:'detail',docId:data.DOC_ID,folderId:data.FOLDER_ID}});
			},
			goFolder:function(id,obj){
				$(obj).nextAll().remove();
				$(obj).nextAll().find("span").remove();
				$("#folderId").val(id);
				list.query();
			},
			addFavorite:function(data) {
				ajax.remoteCall("${ctxPath}/servlet/doc?action=addFavorite",{fkId:data.DOC_ID},function(result) { 
					if(result.state == 1){
						layer.msg("收藏成功！",{icon:1,time:1200});
					}else{
						layer.msg(result.msg,{icon: 7,time:1200});
					}
				});
			}
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>