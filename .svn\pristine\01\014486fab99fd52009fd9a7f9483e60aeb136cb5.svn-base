<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>我的文件管理</title>
	<style>
		.layui-btn{display: none;}
		.layui-table-hover .layui-btn{
			display: inline-block;
		}
		#folderName a,span{color: #999;margin: 0px 3px;}
		.hover{background-color: #daddb5;padding: 5px 8px;font-size: 12px;border-radius: 40%;margin-left: 8px;}
		.layui-table-hover .hover{opacity:1;}
		.icon {
		    width: 1.5em;
		    height: 1.5em;
		    vertical-align: -0.55em;
		    fill: currentColor;
		    overflow: hidden;
		    margin-right: 10px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm" data-page-hide="true">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="fa fa-file"></span> 我上传的文件</h5>
	          		    <div class="input-group input-group-sm">
					 		<span class="input-group-addon">来源</span>
						 	<select class="form-control input-sm" onchange="list.query()" name="source">
	                    		<option value="">请选择</option>
	                    		<option value="task">任务</option>
	                    		<option value="weekly">周报</option>
	                    		<option value="doc">文档</option>
	                    		<option value="project">项目</option>
	                    		<option value="version">版本</option>
	                    	</select>
						 </div>
	          		     <div class="input-group input-group-sm" style="width: 200px;">
							 <span class="input-group-addon">文件名称</span>	
							 <input type="text" name="fileName" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						<table class="layui-hide" id="files"></table>					    
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		$(function(){
			list.init();
		});
		var currentUserId=getCurrentUserId();
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'FileDao.myFileLists',
					id:'files',
					page:true,
					limit:15,
					rowDoubleEvent:'list.detail',
					cols: [[
					 {type:'numbers',width:50,align:'center',title:'序号'},
		             {
					    field: 'FILE_NAME',
						title: '文件名称',
						align:'left',
						templet:function(row){
							return getFileIcon(row['FILE_TYPE'])+'<a href="javascript:;" onclick="list.detail(\''+row['FILE_ID']+'\')">'+row.FILE_NAME+'</a>'+'<a href="javascript:;" onclick="list.previw(\''+row['FILE_ID']+'\')" class="hover">预览</a>';
						}
					},{
						field:'CREATE_TIME',
						width:160,
						align:'center',
						title:'上传时间'
					},{
						field:'DOWNLOAD_COUNT',
						title:'下载次数',
						align:'center',
						width:80
					},{
					    field: 'FILE_SIZE',
						title: '文件大小',
						align:'center',
						sort:true,
						width:85,
						templet:function(row){
							return row.FILE_SIZE;
						}
					},{
					    field: 'SOURCE',
						title: '来源',
						align:'center',
						sort:true,
						width:85,
						templet:function(row){
							return row.SOURCE;
						}
					},{
					    field: 'CREATOR',
						title: '上传人',
						align:'center',
						width:100,
						templet:function(row){
							return getUserName(row.CREATOR);
						}
					},{
						field:'',
						title:'下载日志',
						align:'center',
						width:80,
						templet:'<div><a href="javascript:;" onclick="downloadLogLayer(\'{{d.FK_ID}}\',\'{{d.FILE_ID}}\')">查看</a><a class="ml-5 hidden" href="javascript:;" onclick="delFile(\'{{d.FILE_ID}}\')">删除</a></div>'
					}
					]],done:function(res){
						//$(".layui-table-header").css("display","none");
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(id){
				window.open('${ctxPath}/fileview/'+id);
			},
			previw:function(id){
				window.open('${ctxPath}/fileview/'+id+'?view=online');
			}
		}
		
		function reloadFile(){
			list.query();
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>