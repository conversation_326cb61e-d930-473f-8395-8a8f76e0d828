<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>报销看板</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }


        .layui-this {
            font-weight: bold;
        }


        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-stat .layui-col-md2 {
            text-align: center;
            padding: 10px;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .top-3 {
            font-size: 24px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .top-4 {
            font-size: 36px;
            line-height: 45px;
            font-weight: 500;
            color: #496797;
            margin-bottom: 5px;
        }

        .top-5 {
            font-size: 18px;
            color: #666;
        }

        .bx-stat .layui-card {
            border-radius: 5px;
        }

        .bx-stat .layui-card-hover:hover {
            transform: scale(1.02);
            transition: transform .3s ease;
            box-shadow: 0 4px 11px #0003;
            cursor: pointer;
        }


        .layui-icon-tips {
            margin-left: 3px;
            font-size: 14px;
        }

        .layui-progress {
            margin-top: 26px;
            height: 8px
        }

        #fixdDiv {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 999;
            background: #fff;
            padding: 0px 10px;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        #searchForm {
            padding-top: 60px;
            overflow-y: auto;
        }

    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm" autocomplete="off">
        <div class="layui-row" id="fixdDiv">
            <div class="form-group" style="flex-grow: 1;display: flex;margin-top: 10px;">
                <div class="input-group input-group-sm">
                    <span class="h-title ml-10"> 时间条件 </span>
                    <i class="layui-icon layui-icon-tips" lay-tips="选择报销时间，以报销人填写报销单时的报销明细中每项单独的'费用日期'作为统计标准。选择入账时间，下面全部的【时段内】都将变为入账时间的+数据月份筛选的结果，即今年的报销金额=入账时间为今年的报销金额，且未入账的报销单将不再统计。"></i>
                </div>
                <input type="hidden" name="timeType" id="timeType" value="bx_date">
                <div class="btn-group-sm btn-group time-type" style="width: 140px;margin-left: 20px">
                    <button class="btn btn-info btn-xs" type="button" value="bx_date">报销时间</button>
                    <button class="btn btn-default btn-xs" type="button" value="recorded_date">入账时间</button>
                </div>

                <div class="input-group input-group-sm ml-10" style="width: 240px">
                    <span class="input-group-addon">数据月份</span>
                    <input data-mars="CommonDao.yearMonthBegin" name="beginMonth" id="beginMonth"
                           class="form-control input-sm" onchange="monthChange()"
                           onClick="WdatePicker({dateFmt:'yyyy-MM'})" size="12">
                    <span class="input-group-addon">-</span>
                    <input data-mars="CommonDao.yearMonthEnd" name="endMonth" id="endMonth"
                           onClick="WdatePicker({dateFmt:'yyyy-MM'})" size="12" onchange="monthChange()"
                           class="form-control input-sm">
                </div>
                <div class="btn-group-sm btn-group select-time" style="width: 300px;margin-left: 20px">
                    <button class="btn btn-default btn-xs" type="button" value="halfYear">半年内</button>
                    <button class="btn btn-default btn-xs" type="button" value="oneYear">1年内</button>
                    <button class="btn btn-info btn-xs" type="button" value="nowYear">今年</button>
                    <button class="btn btn-default btn-xs" type="button" value="lastYear">去年</button>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10 bx-stat" id="statDiv1" data-mars="BxStatisticDao.bxConsoleStat">
            <div style="display: none">
                <div class="top-1" id="LAST_YEAR_THIS_MONTH">0</div>
                <div class="top-1" id="LAST_YEAR_LAST_MONTH">0</div>
                <div class="top-1" id="LAST_YEAR_THIS_QUARTER">0</div>
                <div class="top-1" id="LAST_YEAR_LAST_QUARTER">0</div>
                <div class="top-1" id="TOTAL_AMOUNT_LAST_YEAR">0.0</div>
                <div class="top-1" id="COUNT_LAST_YEAR">0</div>
                <div class="top-1" id="TOTAL_AMOUNT_BEFORE_YEAR">0</div>
                <div class="top-1" id="COUNT_BEFORE_YEAR">0</div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="TOTAL_AMOUNT_THIS_MONTH">0</div>
                        <div class="top-2">当月报销金额<i class="layui-icon layui-icon-tips" lay-tips="只统计审批完成的金额，在途流程不计入。"></i><span id="growthDiv1" style="float: right"></span></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-3" id="TOTAL_AMOUNT_LAST_MONTH">0</div>
                        <div class="top-2">上月报销总额<span id="growthDiv2" style="float: right"></span></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="TOTAL_AMOUNT_THIS_QUARTER">0</div>
                        <div class="top-2">当季报销金额<span id="growthDiv3" style="float: right"></span></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-3" id="TOTAL_AMOUNT_LAST_QUARTER">0</div>
                        <div class="top-2">上季报销总额<span id="growthDiv4" style="float: right"></span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10 bx-stat" data-mars="BxStatisticDao.bxConsoleStat">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="TOTAL_AMOUNT_THIS_YEAR">0.0</div>
                        <div class="top-2">今年报销总额<i class="layui-icon layui-icon-tips" lay-tips="环比指与去年全年的报销金额进行比较。"></i> <span id="growthDiv5" style="float: right"></span></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card ">
                    <div class="layui-card-body">
                        <div class="top-3" id="TOTAL_AMOUNT_IN_A_YEAR">0</div>
                        <div class="top-2">近一年报销总额<i class="layui-icon layui-icon-tips" lay-tips="近一年指从去年今天到此时的报销总额，同比的去年同期指前年今天到去年今天。"></i> <span id="growthDiv7" style="float: right"></span></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card ">
                    <div class="layui-card-body">
                        <div class="top-1" id="COUNT_THIS_YEAR">0</div>
                        <div class="top-2">今年报销总次<i class="layui-icon layui-icon-tips" lay-tips="统计发起报销流程并审批完成的次数，多个审批项目一次发起被计数为一次。"></i> <span id="growthDiv6" style="float: right"></span></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card ">
                    <div class="layui-card-body">
                        <div class="top-3" id="COUNT_IN_A_YEAR">0</div>
                        <div class="top-2">近一年报销总次<span id="growthDiv8" style="float: right"></span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md9">
                <div class="layui-card">
                    <div class="layui-card-header" style="display: flex;">
                        <span style="flex-shrink: 0;">总报销金额-时间趋势</span>
                    </div>
                    <div class="layui-card-body" style="height: 350px;width: 100%">
                        <div id="monthChart1" style="height: 350px;width: 100%" data-anim="fade"></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3" id="statDiv2" data-mars="BxStatisticDao.bxConsoleStat2">
                <div class="layui-row bx-stat">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="top-4" id="AMOUNT_IN_PERIOD">0</div>
                            <div class="top-5">时段内报销总额<i class="layui-icon layui-icon-tips" lay-tips="时段与置顶的时间条件保持一致。"></i></div>
                        </div>
                    </div>
                </div>
                <div class="layui-row bx-stat mt-5">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="top-4" id="COUNT_IN_PERIOD">0</div>
                            <div class="top-5">时段内报销总次</div>
                        </div>
                    </div>
                </div>
                <div class="layui-row bx-stat mt-5">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="top-4" id="TRAVEL_FEE_IN_PERIOD">0</div>
                            <div class="top-5">时段内差旅费总额</div>
                        </div>
                    </div>
                </div>
                <div class="layui-row bx-stat mt-5">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="top-4" id="BUSINESS_FEE_IN_PERIOD">0</div>
                            <div class="top-5">时段内业务招待费</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-card">
                <div class="layui-card-body layui-tab" lay-filter="deptFilter1">
                    <ul class="layui-tab-title">
                        <li class="layui-this" lay-id="deptTab11">部门占比图</li>
                        <li lay-id="deptTab12">部门总额排名</li>
                        <li lay-id="deptTab13">部门人均排名</li>
                        <li>部门完整排名表</li>
                    </ul>
                    <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                        <div class="layui-tab-item layui-show">
                            <div id="deptChart2" style="height: 380px;width: 100%" data-anim="fade"></div>
                        </div>
                        <div class="layui-tab-item">
                            <div id="deptChart1" style="height: 380px;width: 100%;" data-anim="fade"></div>
                        </div>
                        <div class="layui-tab-item">
                            <div id="deptChart3" style="height: 380px;width: 100%;" data-anim="fade"></div>
                        </div>
                        <div class="layui-tab-item">
                            <table id="deptRankTable"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10" style="margin-top: 10px">
            <div class="layui-col-md12">
                <div class="layui-card layui-col-md7">
                    <div class="layui-card-body layui-tab" lay-filter="contractFilter1">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="contractTab11">合同花销top10</li>
                            <li lay-id="contractTab12">合同花销占比</li>
                            <li>完整排行表</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="contractChart1" style="height: 400px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="contractChart2" style="height: 400px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <table id="contractRankTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-card layui-col-md5">
                    <div class="layui-card-header" style="line-height: 21px;height: 44px;margin-top: 8px;font-size: 13px">
                        合同：
                        <button type="button" class="btn btn-default btn-xs ml-5" onclick="openContractBxList()"> 报销明细</button>
                        <span id="contractName"></span><span>--</span><span id="contractAmount">0</span>
                    </div>
                    <div class="layui-card-body">
                        <table id="contractRankTable2"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card layui-col-md8">
                    <div class="layui-card-body layui-tab" lay-filter="travelFeeFilter">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="travelFeeTab1">差旅花销top15</li>
                            <li lay-id="travelFeeTab2">差旅花销占比</li>
                            <li>完整排行表</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="travelFeeChart1" style="height: 400px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="travelFeeChart2" style="height: 400px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <table id="travelFeeRankTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-card layui-col-md4">
                    <div class="layui-card-header" style="line-height: 21px;height: 44px;margin-top: 8px;font-size: 13px">
                        合同：
                        <button type="button" class="btn btn-default btn-xs ml-5" onclick="openContractBxList2()"> 报销明细</button>
                        <span id="contractName2"></span><span>--</span><span id="contractAmount2">0</span>
                    </div>
                    <div class="layui-card-body">
                        <table id="contractRankTable3"></table>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab" lay-filter="feeFilter1">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="feeTab11">费用类型top10</li>
                            <li lay-id="feeTab12">费用类型占比</li>
                            <li>完整排行表</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="feeChart1" style="height: 360px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="feeChart2" style="height: 360px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <table id="feeRankTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-body layui-tab" lay-filter="staffFilter1">
                        <ul class="layui-tab-title">
                            <li class="layui-this" lay-id="staffTab11">员工总报销top10</li>
                            <li lay-id="staffTab12">员工报销占比</li>
                            <li>完整排行表</li>
                        </ul>
                        <div class="layui-tab-content" style="margin: 0px;padding:0px;background-color: #fff;">
                            <div class="layui-tab-item layui-show">
                                <div id="staffChart1" style="height: 360px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <div id="staffChart2" style="height: 360px;width: 100%" data-anim="fade"></div>
                            </div>
                            <div class="layui-tab-item">
                                <table id="staffRankTable"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">自定义统计</div>
                    <div class="layui-card-body">
                        <div class="form-group" width="100%" style="display: flex; margin-bottom: 5px;">
                            <input type="hidden" name="timeType2" id="timeType2" value="bx_date">
                            <div class="btn-group-sm btn-group time-type2" style="width: 140px;">
                                <button class="btn btn-info btn-xs" type="button" value="bx_date">报销时间</button>
                                <button class="btn btn-default btn-xs" type="button" value="recorded_date">入账时间</button>
                            </div>
                            <div class="input-group input-group-sm ml-10" style="width: 300px;">
                                <span class="input-group-addon">数据时间</span>
                                <input data-mars="CommonDao.yearBegin" name="startDate2" id="startDate2" size="12"
                                       class="form-control input-sm"
                                       onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})">
                                <span class="input-group-addon">-</span>
                                <input data-mars="CommonDao.today" name="endDate2" id="endDate2"
                                       onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="12"
                                       class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm  ml-10" style="width: 200px">
                                <span class="input-group-addon">统计指标</span>
                                <select class="form-control input-sm" id="searchField" name="searchField">
                                    <option value="FEE_TYPE" selected>费用类型</option>
                                    <option value="TRAVEL_FEE">差旅费详情</option>
                                    <option value="APPLY_NAME">报销员工</option>
                                    <option value="INVOICE_TYPE">发票类型</option>
                                    <option value="DEPT_TYPE">部门类型</option>
                                    <option value="DEPT_NAME">部门名称</option>
                                    <option value="PRODUCT_LINE">产品线</option>
                                    <option value="COST_PLACE">费用地点</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group" width="100%" style="display: flex">
                            <div class="input-group input-group-sm" style="width: 140px;">
                                <span class="input-group-addon">报销人</span>
                                <input id="bxBy" name="bxBy" type="hidden">
                                <input name="bxName" onclick="singleUser(this);" class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm ml-10" style="width:300px;">
                                <span class="input-group-addon">报销合同</span>
                                <input name="contractNo" type="hidden"/>
                                <input name="contractId" type="hidden"/>
                                <input name="contractName" type="text" onclick="singleContract(this);" readonly="readonly" class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm ml-10" style="width: 200px">
                                <span class="input-group-addon">报销部门</span>
                                <input type="text" name="deptName" id="deptName" class="form-control input-sm"
                                       onclick="singleDept(this)"/>
                            </div>
                            <div class="input-group input-group-sm ml-10" style="width: 180px">
                                <span class="input-group-addon">费用类型</span>
                                <input type="text" name="feeType" class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm ml-10">
                                <button type="button" class="btn btn-sm btn-default" onclick="loadSearchTable()"><span class="glyphicon glyphicon-search"></span> <span>搜索</span></button>
                                <button type="button" class="btn btn-sm btn-default ml-5" onclick="clearForm()">清空</button>
                            </div>
                        </div>
                        <div style="width: 35%;display: inline-block">
                            <table id="searchTable"></table>
                        </div>
                        <div style="width: 60%;margin-left:2%;display: inline-block">
                            <div id="searchChart1" style="height: 400px;width: 100%" data-anim="fade"></div>
                        </div>
                        <div style="width: 100%;">
                            <table id="searchTable2"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

    <script>
        var loadMap = {};

        $(function () {

            $("#searchForm").render({
                success: function () {

                    loadStatRatio();

                    monthChange();

                    setTimeout(function () {
                        loadSearchTable();
                    }, 2000)
                }
            });

            layui.element.on('tab(staffFilter1)', function (data) {
                if (data.id == 'staffTab11') {
                    echarts.getInstanceByDom(document.getElementById('staffChart1')).resize();
                } else if (data.id == 'staffTab12') {
                    echarts.getInstanceByDom(document.getElementById('staffChart2')).resize();
                }
            });
            layui.element.on('tab(feeFilter1)', function (data) {
                if (data.id == 'feeTab11') {
                    echarts.getInstanceByDom(document.getElementById('feeChart1')).resize();
                } else if (data.id == 'feeTab12') {
                    echarts.getInstanceByDom(document.getElementById('feeChart2')).resize();
                }
            });

            layui.element.on('tab(contractFilter1)', function (data) {
                if (data.id == 'contractTab11') {
                    echarts.getInstanceByDom(document.getElementById('contractChart1')).resize();
                } else if (data.id == 'contractTab12') {
                    echarts.getInstanceByDom(document.getElementById('contractChart2')).resize();
                }
            });

            layui.element.on('tab(travelFeeFilter)', function (data) {
                if (data.id == 'travelFeeTab1') {
                    echarts.getInstanceByDom(document.getElementById('travelFeeChart1')).resize();
                } else if (data.id == 'travelFeeTab2') {
                    echarts.getInstanceByDom(document.getElementById('travelFeeChart2')).resize();
                }
            });

            $('.time-type button').click(function () {
                $('[name="timeType"]').val($(this)[0].value);
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                timeTypeChange();
            });

            $('.select-time button').click(function () {
                getStartEndMonth($(this)[0], 'beginMonth', 'endMonth');
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
            });

            $('.time-type2 button').click(function () {
                $('[name="timeType2"]').val($(this)[0].value);
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                loadSearchTable();
            });

            layui.element.on('tab(deptFilter1)', function (data) {
                if (data.id == 'deptTab11') {
                    echarts.getInstanceByDom(document.getElementById("deptChart2")).resize();
                } else if (data.id == 'deptTab12') {
                    echarts.getInstanceByDom(document.getElementById("deptChart1")).resize();
                } else if (data.id == 'deptTab13') {
                    echarts.getInstanceByDom(document.getElementById("deptChart3")).resize();
                }
            });


        });

        function openBxList(data) {
            data.isDiv = 1;
            data.timeType = $('#timeType').val();
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                maxmin: true,
                shadeClose: false,
                title: '报销明细',
                offset: 'auto',
                area: ['800px', '520px'],
                url: '${ctxPath}/pages/flow/bx/bx-list-query.jsp',
                data: data
            });
        }

        function openContractBxList() {
            var beginMonth = $('#beginMonth').val();
            var endMonth = $('#endMonth').val();
            var data = {};
            data.contractName = $('#contractName').text();
            data.startDate = beginMonth + '-01';
            data.endDate = getMonthEndDate(endMonth);
            openBxList(data);
        }

        function openContractBxList2() {
            var beginMonth = $('#beginMonth').val();
            var endMonth = $('#endMonth').val();
            var data = {};
            data.contractName = $('#contractName2').text();
            data.startDate = beginMonth + '-01';
            data.feeType = '差旅费';
            data.endDate = getMonthEndDate(endMonth);
            openBxList(data);
        }

        function timeTypeChange() {
            $("#searchForm").render({
                success: function () {
                    loadStatRatio();
                    var beginMonth = $('#beginMonth').val();
                    var endMonth = $('#endMonth').val();
                    var timeType = $('#timeType').val();
                    reloadStat2(beginMonth, endMonth, timeType);
                }
            });
            monthChange();
        }

        function monthChange() {
            var beginMonth = $('#beginMonth').val();
            var endMonth = $('#endMonth').val();
            var timeType = $('#timeType').val();

            reloadStat2(beginMonth, endMonth, timeType);
            loadMonthChart1(beginMonth, endMonth, timeType);
            loadDeptNameTable();
            loadDeptPerAverageChart(beginMonth, endMonth, timeType);

            setTimeout(function () {
                loadContractNameTable();
                loadTravelFeeTable();
            }, 300)

            setTimeout(function () {
                loadFeeTypeTable();
                loadStaffTable();
            }, 500)
        }


        function formatStat2(id) {
            var amount = parseFloat($('#' + id).text());
            if (Number(amount) < 10000) return;
            $('#' + id).text(formatAmount(amount) + '万');
        }

        function reloadStat2(beginMonth, endMonth, timeType) {
            $('#statDiv2').render({
                data: {"beginMonth": beginMonth, "endMonth": endMonth, "timeType": timeType}, success: function () {
                    formatStat2("AMOUNT_IN_PERIOD");
                    formatStat2("TRAVEL_FEE_IN_PERIOD");
                    formatStat2("BUSINESS_FEE_IN_PERIOD");
                }
            });
        }

        function getStartEndMonth(obj, beginMonthName, endMonthName) {
            var val = obj.value;
            var endDate = new Date();
            var beginDate = new Date();
            var beginMonth = '';
            var endMonth = '';
            if (val == 'halfYear') {
                beginDate.setMonth(endDate.getMonth() - 5);
            } else if (val == 'oneYear') {
                beginDate.setMonth(endDate.getMonth() - 11);
            } else if (val == 'nowYear') {
                endDate.setMonth(11);
                beginDate.setMonth(0);
            } else if (val == 'lastYear') {
                var lastYear = endDate.getFullYear() - 1;
                endDate.setMonth(11);
                endDate.setFullYear(lastYear);
                beginDate.setMonth(0);
                beginDate.setFullYear(lastYear);
            }
            endMonth = DateUtils.dateFormat(endDate, 'yyyy-MM');
            beginMonth = DateUtils.dateFormat(beginDate, 'yyyy-MM');

            if (val) {
                $('[name="' + beginMonthName + '"]').val(beginMonth);
                $('[name="' + endMonthName + '"]').val(endMonth);
            } else {
                return;
            }
            //接下来加载表格
            monthChange();
        }


        var travelFeeContractNames = [];
        var travelFeeContractIds = [];
        var travelFeeContractAmounts = [];

        function loadTravelFeeTable() {
            $("#searchForm").initTable({
                    mars: 'BxStatisticDao.travelFeeRankByContract',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '380px',
                    autoSort: false,
                    id: 'travelFeeRankTable',
                    rowDoubleEvent: 'travelFeeTableClick',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'CONTRACT_SIMPILE_NAME',
                            title: '合同名',
                            width: 120,
                            event: 'travelFeeTableClick',
                            style: 'cursor:pointer;text-decoration: underline;'
                        }, {
                            field: 'SUM_FEE',
                            title: '时段内差旅费',
                            sort: true,
                            width: 110,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.SUM_FEE);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        },
                        {
                            field: 'RATIO',
                            sort: true,
                            title: '累积差旅/合同额',
                            width: 150,
                            align: 'center',
                            templet: function (d) {
                                return ratioColor(d.RATIO);
                            }
                        }, {
                            field: 'SUM_FEE_ALL_TIME',
                            title: '全时段差旅费',
                            sort: true,
                            width: 110,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.SUM_FEE_ALL_TIME);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'FEE1',
                            title: '时段内机票费',
                            sort: true,
                            width: 110,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.FEE1);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'FEE2',
                            title: '车船费',
                            sort: true,
                            width: 80,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.FEE2);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'FEE3',
                            title: '市内交通费',
                            sort: true,
                            width: 95,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.FEE3);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'FEE4',
                            title: '住宿费',
                            sort: true,
                            width: 80,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.FEE4);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'FEE5',
                            title: '出差补助',
                            sort: true,
                            width: 80,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.FEE5);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        },

                    ]],
                    done: function (data) {
                        if ((data.othis.data)||data.pageNumber != 1||data.pageSize!=20) {
                            return;
                        }
                        var data2 = data.data;
                        travelFeeContractNames = data2.map(item => item.CONTRACT_SIMPILE_NAME);
                        travelFeeContractIds = data2.map(item => item.CONTRACT_ID);
                        travelFeeContractAmounts = data2.map(item => item.AMOUNT);

                        loadContractTable3(travelFeeContractIds[0], travelFeeContractNames[0],travelFeeContractAmounts[0])

                        loadTravelFeeBarChart(data2);

                        loadTravelFeePieChart(data2);
                    }
                },
            )
        }

        function loadTravelFeeBarChart(data2) {
            var contractNames2 = data2.slice(0, 15).map(item => subContractName(item.CONTRACT_SIMPILE_NAME));
            var fee1 = data2.slice(0, 15).map(item => formatAmount(item.FEE1));
            var fee2 = data2.slice(0, 15).map(item => formatAmount(item.FEE2));
            var fee3 = data2.slice(0, 15).map(item => formatAmount(item.FEE3));
            var fee4 = data2.slice(0, 15).map(item => formatAmount(item.FEE4));
            var fee5 = data2.slice(0, 15).map(item => formatAmount(item.FEE5));
            var sum = data2.slice(0, 15).map(item => formatAmount(item.SUM_FEE));
            var ratio = data2.slice(0, 15).map(item => item.RATIO);
            var sortedRatios = [...ratio].sort((a, b) => b - a);
            var ratio3 = parseFloat(sortedRatios[2]);
            ratio3 = ratio3 > 10 ? ratio3 : 10;

            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function (params) {
                        var result = params[0].name + '<br/>';
                        params.forEach(function (item) {
                            if (item.seriesType === 'bar') {
                                result += item.marker + item.seriesName + ': ' + (item.value + '万') + '<br/>';
                            } else if (item.seriesType === 'line') {
                                result += '时段内合计：' + sum[params[0].dataIndex] + '万' + '<br/>';
                                result += item.marker + item.seriesName + ': ' + item.value + '%';
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['机票费', '车船费', '市内交通费', '住宿费', '出差补助', '总累积差旅费占合同金额比']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '1%',
                    containLabel: true
                },
                barGap: '0%',
                xAxis: {
                    type: 'category',
                    data: contractNames2,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}万'
                        }
                    },
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    }
                ],
                series: [
                    {
                        name: '机票费',
                        type: 'bar',
                        stack: '总量',
                        data: fee1,
                    },
                    {
                        name: '车船费',
                        type: 'bar',
                        stack: '总量',
                        data: fee2,
                    },
                    {
                        name: '市内交通费',
                        type: 'bar',
                        stack: '总量',
                        data: fee3,
                    },
                    {
                        name: '住宿费',
                        type: 'bar',
                        stack: '总量',
                        data: fee4,
                    },
                    {
                        name: '出差补助',
                        type: 'bar',
                        stack: '总量',
                        data: fee5,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                return sum[params.dataIndex] + '万';
                            }
                        },

                    },
                    {
                        name: '总累积差旅费占合同金额比',
                        type: 'line',
                        yAxisIndex: 1,
                        data: ratio.map(Number),
                        smooth: false,
                        itemStyle: {
                            color: function (params) {
                                return ratio[params.dataIndex] >= ratio3 ? '#ec4c63' : '#5FB878';
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                return ratio[params.dataIndex] >= ratio3 ? ratio[params.dataIndex] + '%' : '';
                            },
                            textStyle: {
                                color: '#b82843',
                            },
                        }
                    }
                ],
            };
            var travelFeeChart = echarts.init(document.getElementById('travelFeeChart1'), myEchartsTheme);
            travelFeeChart.setOption(option);

            if (loadMap["travelFeeChartOnClick"]) {
                return;
            }
            travelFeeChart.on('click', function (params) {
                if (params.componentType === 'series') {
                    var index = params.dataIndex;
                    var contractId = travelFeeContractIds[index];
                    var contractName = travelFeeContractNames[index];
                    var contractAmount = travelFeeContractAmounts[index];
                    loadContractTable3(contractId, contractName,contractAmount);
                }
            });
            loadMap["travelFeeChartOnClick"] = true;

        }

        function loadTravelFeePieChart(data2) {
            const echartsData = data2.map(item => {
                return {
                    name: subContractName(item.CONTRACT_SIMPILE_NAME),
                    value: formatAmount(item.SUM_FEE)
                };
            });
            var top10Data = echartsData.slice(0, 10);
            var otherAmount = echartsData.slice(10).reduce((sum, item) => sum + parseFloat(item.value), 0);
            if (otherAmount > 0) {
                top10Data.push({
                    name: "top20剩余部分",
                    value: otherAmount.toFixed(2)
                });
            }
            var filteredData = top10Data.filter(function (item) {
                return item.value !== 0 && item.value !== "0.00";
            });
            var options1 = {
                tooltip: {
                    trigger: "item",
                    formatter: "{b}: {c}万 ({d}%)"
                },
                series: [
                    {
                        name: "时段内差旅费",
                        type: "pie",
                        radius: ['20%', '55%'],
                        data: filteredData,
                        itemStyle: {
                            color: function (params) {
                                var colorList = ["#63b2ee", "#76da91", "#f8cb7f", "#f89588", "#7cd6cf", "#9192ab", "#7898e1", "#efa666", "#eddd86", "#9987ce","#91cc75"];
                                return colorList[params.dataIndex % colorList.length];
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{b}: {c} 万({d}%)'
                        },
                        labelLine: {
                            show: true
                        }
                    },

                ]
            };
            var myCharts = echarts.init(document.getElementById("travelFeeChart2"), myEchartsTheme);
            myCharts.setOption(options1);
            if (loadMap["travelFeePieChartOnClick"]) {
                return;
            }
            myCharts.on('click', function (params) {
                if (params.componentType === 'series') {
                    var index = params.dataIndex;
                    if (index === 10) return;
                    var index = params.dataIndex;
                    var contractId = travelFeeContractIds[index];
                    var contractName = travelFeeContractNames[index];
                    var contractAmount = travelFeeContractAmounts[index];
                    loadContractTable3(contractId, contractName,contractAmount);
                }
            });
            loadMap["travelFeePieChartOnClick"] = true;
        }

        function travelFeeTableClick(row) {
            loadContractTable3(row.CONTRACT_ID, row.CONTRACT_SIMPILE_NAME,row.AMOUNT);
        }


        function loadStatRatio() {
            var currentMonth = new Date().getMonth() + 1;
            var lastMonth = currentMonth - 1;
            var curQuarter = getQuarterFromMonth(currentMonth);
            var lastQuarter = curQuarter - 1;
            calGrowthRatio("TOTAL_AMOUNT_THIS_MONTH", "LAST_YEAR_THIS_MONTH", "growthDiv1", '同比去年' + currentMonth + '月')
            if (currentMonth == 1) {
                lastMonth = 12;
                calGrowthRatio("TOTAL_AMOUNT_LAST_MONTH", "LAST_YEAR_LAST_MONTH", "growthDiv2", '同比前年' + lastMonth + '月')
            } else {
                calGrowthRatio("TOTAL_AMOUNT_LAST_MONTH", "LAST_YEAR_LAST_MONTH", "growthDiv2", '同比去年' + lastMonth + '月')
            }
            calGrowthRatio("TOTAL_AMOUNT_THIS_QUARTER", "LAST_YEAR_THIS_QUARTER", "growthDiv3", '同比去年' + curQuarter + '季度')
            if (curQuarter == 1) {
                lastQuarter = 4;
                calGrowthRatio("TOTAL_AMOUNT_LAST_QUARTER", "LAST_YEAR_LAST_QUARTER", "growthDiv4", '同比前年' + lastQuarter + '季度')
            } else {
                calGrowthRatio("TOTAL_AMOUNT_LAST_QUARTER", "LAST_YEAR_LAST_QUARTER", "growthDiv4", '同比去年' + lastQuarter + '季度')
            }
            var lastYearAmount = formatAmount0($("#TOTAL_AMOUNT_LAST_YEAR").text()) + '万';
            var count = ($("#COUNT_LAST_YEAR").text());
            calGrowthRatio("TOTAL_AMOUNT_THIS_YEAR", "TOTAL_AMOUNT_LAST_YEAR", "growthDiv5", '环比' + lastYearAmount)
            calGrowthRatio("COUNT_THIS_YEAR", "COUNT_LAST_YEAR", "growthDiv6", '环比' + count + '次');

            var beforeYearAmount = formatAmount0($("#TOTAL_AMOUNT_BEFORE_YEAR").text()) + '万';
            var beforeYearCount = ($("#COUNT_BEFORE_YEAR").text());
            calGrowthRatio("TOTAL_AMOUNT_IN_A_YEAR", "TOTAL_AMOUNT_BEFORE_YEAR", "growthDiv7", '同比' + beforeYearAmount);
            calGrowthRatio("COUNT_IN_A_YEAR", "COUNT_BEFORE_YEAR", "growthDiv8", '同比' + beforeYearCount + '次');
        }

        function loadDeptNameTable() {
            $("#searchForm").initTable({
                    mars: 'BxStatisticDao.deptNameAmountRank',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '360px',
                    autoSort: false,
                    id: 'deptRankTable',
                    rowDoubleEvent: 'deptTableClick',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'DEPT_NAME',
                            title: '部门',
                            event: 'deptTableClick',
                            minWidth:120,
                            style: 'cursor:pointer;text-decoration: underline;'
                        }, {
                            field: 'TOTAL_AMOUNT_THIS_YEAR',
                            title: '时段内报销总额',
                            sort: true,
                            align: 'center',
                            minWidth:120,
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'TOTAL_AMOUNT_LAST_YEAR',
                            title: '上一年同期总额',
                            sort: true,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'AVERAGE_THIS_YEAR',
                            title: '时段内人均报销',
                            sort: true,
                            align: 'center',
                            minWidth:120,
                            templet: function (d) {
                                var num = parseFloat(d.AVERAGE_THIS_YEAR);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }
                    ]],
                    done: function (data) {
                        if ((data.othis.data)||data.pageNumber != 1) {
                            return;
                        }
                        var data2 = data.data;

                        var deptNames = data2.slice(0, 20).map(item => item.DEPT_NAME);
                        var thisYearAmounts = data2.slice(0, 20).map(item => formatAmount(item.TOTAL_AMOUNT_THIS_YEAR));
                        var lastYearAmounts = data2.slice(0, 20).map(item => formatAmount(item.TOTAL_AMOUNT_LAST_YEAR));

                        var chart = loadBarChart(deptNames, thisYearAmounts, lastYearAmounts, "deptChart1");
                        if (!loadMap["deptChart1OnClick"]) {
                            chart.on('click', function (params) {
                                var index = params.dataIndex;
                                var deptName = deptNames[index];
                                var beginMonth = $('#beginMonth').val();
                                var endMonth = $('#endMonth').val();
                                var startDate = beginMonth + '-01';
                                var endDate = getMonthEndDate(endMonth);
                                openBxList(
                                    {
                                        deptName: deptName,
                                        startDate: startDate,
                                        endDate: endDate
                                    }
                                )
                            });
                        }
                        loadMap["deptChart1OnClick"] = true;

                        const echartsData = data2.map(item => {
                            return {
                                name: item.DEPT_NAME,
                                value: formatAmount(item.TOTAL_AMOUNT_THIS_YEAR)
                            };
                        });
                        var top10Data = echartsData.slice(0, 20);
                        var otherAmount = echartsData.slice(20).reduce((sum, item) => sum + parseFloat(item.value), 0);
                        if (otherAmount > 0) {
                            top10Data.push({
                                name: "剩余部分",
                                value: otherAmount.toFixed(2)
                            });
                        }
                        var filteredData = top10Data.filter(function (item) {
                            return item.value !== 0 && item.value !== "0.00";
                        });
                        var pieChart = loadRosePieChat(filteredData, "deptChart2");
                        if (!loadMap["deptChart2OnClick"]) {
                            pieChart.on('click', function (params) {
                                var index = params.dataIndex;
                                var deptName = echartsData[index].name;
                                var beginMonth = $('#beginMonth').val();
                                var endMonth = $('#endMonth').val();
                                var startDate = beginMonth + '-01';
                                var endDate = getMonthEndDate(endMonth);
                                openBxList(
                                    {
                                        deptName: deptName,
                                        startDate: startDate,
                                        endDate: endDate
                                    }
                                )
                            });
                        }
                        loadMap["deptChart2OnClick"] = true;
                    }
                },
            )
        }

        function deptTableClick(data) {
            var deptName = data.DEPT_NAME;
            var beginMonth = $('#beginMonth').val();
            var endMonth = $('#endMonth').val();
            var startDate = beginMonth + '-01';
            var endDate = getMonthEndDate(endMonth)
            openBxList(
                {
                    deptName: deptName,
                    startDate: startDate,
                    endDate: endDate
                }
            )
        }


        function loadDeptPerAverageChart(beginMonth, endMonth, timeType) {
            ajax.remoteCall("/yq-work/webcall?action=BxStatisticDao.deptNameAverageRank", {"pageSize": 25, "beginMonth": beginMonth, "endMonth": endMonth, "timeType": timeType}, function (result) {
                var data2 = result.data;
                var deptName = data2.map(item => item.DEPT_NAME);
                var averageAmounts = data2.map(item => item.PERSON_AVERAGE);

                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function (params) {
                            var result = params[0].name + '<br/>';
                            params.forEach(function (item) {
                                if (item.seriesType === 'bar') {
                                    result += item.marker + item.seriesName + ': ' + (item.value) + '<br/>';
                                }
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: ['人均报销']
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '1%',
                        containLabel: true
                    },
                    barGap: '0%',
                    barWidth: '50%',
                    xAxis: {
                        type: 'category',
                        data: deptName,
                        axisLabel: {
                            rotate: 45,
                            interval: 0
                        }
                    },
                    yAxis: [
                        {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        }
                    ],
                    series: [
                        {
                            name: '人均报销',
                            type: 'bar',
                            data: averageAmounts,
                            label: {
                                show: true,
                                position: 'top',
                                formatter: function (params) {
                                    if (params.dataIndex <= 2 || params.dataIndex % 2 == 0) {
                                        var averageAmount = parseFloat(averageAmounts[params.dataIndex]);
                                        return averageAmount.toFixed(1);
                                    } else {
                                        return '';
                                    }
                                }
                            },
                        }
                    ],
                };

                var chart = echarts.init(document.getElementById('deptChart3'), myEchartsTheme);
                chart.setOption(option);
            })
        }

        var staffNames = [];
        var staffIds = [];

        function loadStaffTable() {
            $("#searchForm").initTable({
                    mars: 'BxStatisticDao.staffAmountRank',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '340px',
                    autoSort: false,
                    id: 'staffRankTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'APPLY_NAME',
                            align: 'center',
                            title: '员工',
                            width: 80
                        }, {
                            field: 'TOTAL_AMOUNT_THIS_YEAR',
                            title: '时段内总额',
                            sort: true,
                            align: 'center',
                            event: 'staffTableClickThisYear',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth:100,
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'TOTAL_AMOUNT_LAST_YEAR',
                            title: '上一年同期',
                            sort: true,
                            align: 'center',
                            event: 'staffTableClickLastYear',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth:100,
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: '',
                            title: '同比增长率',
                            width: 120,
                            align: 'center',
                            minWidth:100,
                            templet: function (d) {
                                var lastYearAmount = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                if (lastYearAmount === 0) return '上一年无报销';
                                var thisYearAmount = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                if (thisYearAmount === undefined) return '今年无报销';
                                var num = (thisYearAmount - lastYearAmount) * 100 / lastYearAmount;
                                return ratioColor(num);
                            }
                        },
                    ]],
                    done: function (data) {
                        if ((data.othis.data)||data.pageNumber != 1||data.pageSize!=20) {
                            return;
                        }
                        var data2 = data.data;


                        staffNames = data2.slice(0, 10).map(item => item.APPLY_NAME);
                        staffIds = data2.slice(0, 10).map(item => item.APPLY_BY);

                        var thisYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_THIS_YEAR));
                        var lastYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_LAST_YEAR));

                        var staffBarChart = loadBarChart(staffNames, thisYearAmounts, lastYearAmounts, "staffChart1");

                        if (!loadMap["staffBarChartOnclick"]) {
                            staffBarChart.on('click', function (params) {
                                var index = params.dataIndex;
                                openBxList({
                                    applyName: staffNames[index],
                                    bxBy: staffIds[index],
                                    startDate: $('#beginMonth').val() + '-01',
                                    endDate: getMonthEndDate($('#endMonth').val()),
                                })
                            });
                        }
                        loadMap["staffBarChartOnclick"] = true;

                        const echartsData = data2.map(item => {
                            return {
                                name: item.APPLY_NAME,
                                value: formatAmount(item.TOTAL_AMOUNT_THIS_YEAR)
                            };
                        });
                        var top10Data = echartsData.slice(0, 10);
                        var otherAmount = echartsData.slice(10).reduce((sum, item) => sum + parseFloat(item.value), 0);
                        if (otherAmount > 0) {
                            top10Data.push({
                                name: "top20剩余部分",
                                value: otherAmount.toFixed(2)
                            });
                        }
                        var filteredData = top10Data.filter(function (item) {
                            return item.value !== 0 && item.value !== "0.00";
                        });
                        var staffChart2 = loadPieChart(filteredData, "staffChart2");
                        if (!loadMap["staffPieChartOnclick"]) {
                            staffChart2.on('click', function (params) {
                                var index = params.dataIndex;
                                openBxList({
                                    applyName: staffNames[index],
                                    bxBy: staffIds[index],
                                    startDate: $('#beginMonth').val() + '-01',
                                    endDate: getMonthEndDate($('#endMonth').val()),
                                })
                            });
                        }
                        loadMap["staffPieChartOnclick"] = true;
                    }
                },
            )

        }

        function staffTableClickThisYear(row) {
            var data = {};
            data.applyName = row.APPLY_NAME;
            data.bxBy = row.APPLY_BY;
            ;
            data.startDate = $('#beginMonth').val() + '-01';
            data.endDate = getMonthEndDate($('#endMonth').val());
            openBxList(data);
        }

        function staffTableClickLastYear(row) {
            var data = {};
            data.applyName = row.APPLY_NAME;
            data.bxBy = row.APPLY_BY;
            data.startDate = getLastYearDate($('#beginMonth').val() + '-01');
            data.endDate = getLastYearDate(getMonthEndDate($('#endMonth').val()));
            openBxList(data);
        }

        var feeTypes = [];

        function loadFeeTypeTable() {
            $("#searchForm").initTable({
                    mars: 'BxStatisticDao.feeTypeAmountRank',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '340px',
                    autoSort: false,
                    id: 'feeRankTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'FEE_TYPE',
                            title: '费用类型',
                            minWidth:100,
                        }, {
                            field: 'TOTAL_AMOUNT_THIS_YEAR',
                            title: '时段内总额',
                            sort: true,
                            align: 'center',
                            event: 'feeTypeTableClickThisYear',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth:100,
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'TOTAL_AMOUNT_LAST_YEAR',
                            title: '上一年同期',
                            sort: true,
                            align: 'center',
                            event: 'feeTypeTableClickLastYear',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth:100,
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 1, maximumFractionDigits: 1});
                            }
                        }, {
                            field: '',
                            title: '同比增长率',
                            width: 120,
                            align: 'center',
                            templet: function (d) {
                                var lastYearAmount = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                if (lastYearAmount === 0) return '上一年无报销';
                                var thisYearAmount = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                if (thisYearAmount === undefined) return '今年无报销';
                                var num = (thisYearAmount - lastYearAmount) * 100 / lastYearAmount;
                                return ratioColor(num);
                            }
                        },
                    ]],
                    done: function (data) {
                        if ((data.othis.data)||data.pageNumber != 1||data.pageSize!=20) {
                            return;
                        }
                        var data2 = data.data;

                        feeTypes = data2.slice(0, 10).map(item => item.FEE_TYPE);
                        var thisYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_THIS_YEAR));
                        var lastYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_LAST_YEAR));

                        var feeBarChart = loadBarChart(feeTypes, thisYearAmounts, lastYearAmounts, "feeChart1");

                        if (!loadMap["feeTypeChart1"]) {
                            feeBarChart.on('click', function (params) {
                                var index = params.dataIndex;
                                var data = {};
                                data.feeType = feeTypes[index];
                                data.startDate = $('#beginMonth').val() + '-01';
                                data.endDate = getMonthEndDate($('#endMonth').val());
                                openBxList(data)
                            });
                        }
                        loadMap["feeTypeChart1"] = true;

                        const echartsData = data2.map(item => {
                            return {
                                name: item.FEE_TYPE,
                                value: formatAmount(item.TOTAL_AMOUNT_THIS_YEAR)
                            };
                        });
                        var top10Data = echartsData.slice(0, 10);
                        var otherAmount = echartsData.slice(10).reduce((sum, item) => sum + parseFloat(item.value), 0);
                        if (otherAmount > 0) {
                            top10Data.push({
                                name: "top20剩余部分",
                                value: otherAmount.toFixed(2)
                            });
                        }
                        var filteredData = top10Data.filter(function (item) {
                            return item.value !== 0 && item.value !== "0.00";
                        });
                        var feeChart2 = loadPieChart(filteredData, "feeChart2");
                        if (!loadMap["feeTypeChart2"]) {
                            feeChart2.on('click', function (params) {
                                var index = params.dataIndex;
                                if (index === 10) return;
                                var data = {};
                                data.feeType = feeTypes[index];
                                data.startDate = $('#beginMonth').val() + '-01';
                                data.endDate = getMonthEndDate($('#endMonth').val());
                                openBxList(data)
                            });
                        }
                        loadMap["feeTypeChart2"] = true;
                    }
                },
            )
        }

        function feeTypeTableClickThisYear(row) {
            var data = {};
            data.feeType = row.FEE_TYPE;
            data.startDate = $('#beginMonth').val() + '-01';
            data.endDate = getMonthEndDate($('#endMonth').val());
            openBxList(data);
        }

        function feeTypeTableClickLastYear(row) {
            var data = {};
            data.feeType = row.FEE_TYPE;
            data.startDate = getLastYearDate($('#beginMonth').val() + '-01');
            data.endDate = getLastYearDate(getMonthEndDate($('#endMonth').val()));
            openBxList(data);
        }


        var contractNamesRankList = [];
        var contractIdsRankList = [];
        var contractAmountRankList = [];


        function loadContractNameTable() {
            $("#searchForm").initTable({
                    mars: 'BxStatisticDao.contractNameAmountRank',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '380px',
                    autoSort: false,
                    id: 'contractRankTable',
                    rowDoubleEvent: 'contractTableClick',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'CONTRACT_SIMPILE_NAME',
                            title: '合同名',
                            event: 'contractTableClick',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth:100,
                        }, {
                            field: 'TOTAL_AMOUNT_THIS_YEAR',
                            title: '时段内报销',
                            sort: true,
                            width: 110,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT_THIS_YEAR);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'TOTAL_AMOUNT_LAST_YEAR',
                            title: '上一年同期',
                            sort: true,
                            width: 100,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT_LAST_YEAR);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'SUM_BX',
                            title: '累积报销',
                            sort: true,
                            width: 100,
                            align: 'center',
                            templet: function (d) {
                                var num = parseFloat(d.SUM_BX);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'RATIO',
                            sort: true,
                            title: '累积报销/合同金额比',
                            width: 150,
                            align: 'center',
                            templet: function (d) {
                                return ratioColor(d.RATIO);
                            }
                        }
                    ]],
                    done: function (data) {
                        if ((data.othis.data)||data.pageNumber != 1||data.pageSize!=20) {
                            return;
                        }
                        var data2 = data.data;
                        contractNamesRankList = data2.map(item => item.CONTRACT_SIMPILE_NAME);
                        contractIdsRankList = data2.map(item => item.CONTRACT_ID);
                        contractAmountRankList = data2.map(item => item.AMOUNT);

                        loadContractTable2(contractIdsRankList[0], contractNamesRankList[0],contractAmountRankList[0]);
                        var contractName2 = data2.slice(0, 10).map(item => subContractName(item.CONTRACT_SIMPILE_NAME));
                        var thisYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_THIS_YEAR));
                        var lastYearAmounts = data2.slice(0, 10).map(item => formatAmount(item.TOTAL_AMOUNT_LAST_YEAR));
                        var ratio = data2.slice(0, 10).map(item => (item.RATIO));

                        loadContractBarChart(contractName2, thisYearAmounts, lastYearAmounts, ratio, "contractChart1");

                        const echartsData = data2.map(item => {
                            return {
                                name: subContractName(item.CONTRACT_SIMPILE_NAME),
                                value: formatAmount(item.TOTAL_AMOUNT_THIS_YEAR)
                            };
                        });
                        var top10Data = echartsData.slice(0, 10);
                        var otherAmount = echartsData.slice(10).reduce((sum, item) => sum + parseFloat(item.value), 0);
                        if (otherAmount > 0) {
                            top10Data.push({
                                name: "top20剩余部分",
                                value: otherAmount.toFixed(2)
                            });
                        }
                        var filteredData = top10Data.filter(function (item) {
                            return item.value !== 0 && item.value !== "0.00";
                        });
                        loadContractPieChart(filteredData, "contractChart2");
                    }
                },
            )
        }

        function loadContractTable2(contractId, contractName,contractAmount) {
            $('#contractName').text(contractName)
            contractAmount = parseFloat(contractAmount) > 10000 ? formatAmount(contractAmount)+"万":contractAmount;
            $('#contractAmount').text(contractAmount)

            var timeType = $('#timeType').val();
            $("#searchForm").initTable({
                    mars: 'BxStatisticDao.feeTypeRankByContract',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '368px',
                    autoSort: false,
                    id: 'contractRankTable2',
                    data: {"contractId": contractId, "timeType": timeType},
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'FEE_TYPE',
                            title: '费用类型',
                            minWidth: 100,
                        }, {
                            field: 'YEAR_AMOUNT',
                            title: '时段内报销',
                            sort: true,
                            width: 105,
                            align: 'center',
                            event: 'contractFeeTypeTableClickThisYear',
                            style: 'cursor:pointer;text-decoration: underline;',
                            templet: function (d) {
                                var num = parseFloat(d.YEAR_AMOUNT);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'TOTAL_AMOUNT',
                            title: '累计报销',
                            sort: true,
                            width: 105,
                            align: 'center',
                            event: 'contractFeeTypeTableClickAll',
                            style: 'cursor:pointer;text-decoration: underline;',
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: '',
                            title: '总报销/合同金额',
                            width: 110,
                            align: 'center',
                            templet: function (d) {
                                var contractAmount = parseFloat(d.AMOUNT);
                                if (contractAmount === 0) return '';
                                var num = parseFloat(d.TOTAL_AMOUNT) * 100 / contractAmount;
                                return ratioColor(num);
                            }
                        },
                    ]],
                    done: function (data) {

                    }
                },
            )
        }

        function contractFeeTypeTableClickThisYear(row) {
            var data = {};
            data.contractName = $('#contractName').text();
            data.feeType = row.FEE_TYPE;
            data.startDate = $('#beginMonth').val() + '-01';
            data.endDate = getMonthEndDate($('#endMonth').val());
            openBxList(data);
        }

        function contractFeeTypeTableClickAll(row) {
            var data = {};
            data.contractName = $('#contractName').text();
            data.feeType = row.FEE_TYPE;
            openBxList(data);
        }

        function loadContractTable3(contractId,contractName,contractAmount) {
            $('#contractName2').text(contractName)
            contractAmount = parseFloat(contractAmount) > 10000 ? formatAmount(contractAmount)+"万":contractAmount;
            $('#contractAmount2').text(contractAmount)
            var timeType = $('#timeType').val();
            $("#searchForm").initTable({
                    mars: 'BxStatisticDao.staffRankByContract',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '368px',
                    autoSort: false,
                    id: 'contractRankTable3',
                    data: {"contractId": contractId, "timeType": timeType},
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'APPLY_NAME',
                            title: '报销员工',
                            align: 'center',
                            width: 80,
                        }, {
                            field: 'TIME_AMOUNT',
                            title: '时段内差旅费',
                            sort: true,
                            width: 120,
                            align: 'center',
                            event: 'contractStaffTableClickTime',
                            style: 'cursor:pointer;text-decoration: underline;',
                            templet: function (d) {
                                var num = parseFloat(d.TIME_AMOUNT);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }, {
                            field: 'TOTAL_AMOUNT',
                            title: '累计差旅费',
                            sort: true,
                            width: 120,
                            align: 'center',
                            event: 'contractStaffTableClickAll',
                            style: 'cursor:pointer;text-decoration: underline;',
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }
                    ]],
                    done: function (data) {

                    }
                },
            )
        }

        function contractStaffTableClickTime(row) {
            var data = {};
            data.contractName = $('#contractName2').text();
            data.bxBy = row.APPLY_BY;
            data.applyName = row.APPLY_NAME;
            data.feeType = '差旅费';
            data.startDate = $('#beginMonth').val() + '-01';
            data.endDate = getMonthEndDate($('#endMonth').val());
            openBxList(data);
        }

        function contractStaffTableClickAll(row) {
            var data = {};
            data.contractName = $('#contractName2').text();
            data.bxBy = row.APPLY_BY;
            data.applyName = row.APPLY_NAME;
            data.feeType = '差旅费';
            openBxList(data);
        }


        function loadMonthChart1(beginMonth, endMonth, timeType) {
            ajax.remoteCall("/yq-work/webcall?action=BxStatisticDao.bxDeptTypeAmountMonthly", {"beginMonth": beginMonth, "endMonth": endMonth, "timeType": timeType}, function (result) {
                var data1 = result["工程研发部门"];
                var data2 = result["管理部门"];
                var data3 = result["销售部门"];
                var data4 = result["sum"];

                var deptType = ['工程研发部门', '管理部门', '销售部门', '全部'];
                var months = Object.keys(data4);
                var dept1 = months.map(month => formatAmount(data1[month] || "0"));
                var dept2 = months.map(month => formatAmount(data2[month] || "0"));
                var dept3 = months.map(month => formatAmount(data3[month] || "0"));
                var sum = months.map(month => formatAmount(data4[month] || "0"));

                var options2 = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function (params) {
                            var result = '';
                            result += params[0].name + "月" + '<br/>';
                            params.forEach(function (item) {
                                result += item.marker + item.seriesName + ': ' + (item.value + '万') + '<br/>';
                            });
                            return result;
                        },
                    },
                    legend: {
                        data: deptType
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    barGap: '0%',
                    barCategoryGap: '50%',
                    xAxis: {
                        type: 'category',
                        data: months,
                        axisLabel: {
                            rotate: 45,
                            interval: 0
                        }
                    },
                    yAxis: [
                        {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}万'
                            }
                        },
                        {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}万'
                            }
                        }
                    ],
                    series: [
                        {
                            name: deptType[0],
                            type: 'bar',
                            data: dept1,
                            stack: '总量',
                        },
                        {
                            name: deptType[1],
                            type: 'bar',
                            data: dept2,
                            stack: '总量',
                        },
                        {
                            name: deptType[2],
                            type: 'bar',
                            data: dept3,
                            stack: '总量',
                            label: {
                                show: true,
                                position: 'top',
                                formatter: function (params) {
                                    return sum[params.dataIndex] + '万';
                                }
                            },
                        }, {
                            name: '全部',
                            type: 'line',
                            yAxisIndex: 1,
                            data: sum,
                            smooth: false,
                        }
                    ]
                };

                var monthChart = echarts.init(document.getElementById('monthChart1'), myEchartsTheme);
                monthChart.setOption(options2);
                if (loadMap["monthChartOnClick"]) {
                    return;
                }
                monthChart.on('click', function (params) {
                    var index = params.dataIndex;
                    var month = months[index];
                    month = month.replace('_', '-');
                    var monthStart = month + "-01";
                    var monthEnd = getMonthEndDate(month);
                    var data = {
                        "startDate": monthStart,
                        "endDate": monthEnd
                    };
                    openBxList(data);
                });
                loadMap["monthChartOnClick"] = true;

            })
        }

        function loadSearchTable() {
            var field = $("#searchField").val();
            var fieldName = $("#searchField option:selected").text();
            $("#searchForm").initTable({
                    mars: 'BxStatisticDao.searchAmountRank',
                    cellMinWidth: 50,
                    limit: 15,
                    height: '380px',
                    autoSort: false,
                    id: 'searchTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: field,
                            align: 'center',
                            title: fieldName,
                            minWidth:100,
                        }, {
                            field: 'TOTAL_AMOUNT',
                            title: '时段内报销总额',
                            sort: true,
                            align: 'center',
                            minWidth:100,
                            templet: function (d) {
                                var num = parseFloat(d.TOTAL_AMOUNT);
                                return num.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2});
                            }
                        }
                    ]],
                    done: function (data) {
                        var data2 = data.data;

                        var fields = data2.map(item => item[field]);
                        var bxAmount = data2.map(item => formatAmount(item.TOTAL_AMOUNT));

                        var options1 = {
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                },
                                formatter: function (params) {
                                    var result = params[0].name + '<br/>';
                                    params.forEach(function (item) {
                                        result += item.marker + item.seriesName + ': ' + (item.value + '万') + '<br/>';
                                    });
                                    return result;
                                }
                            },
                            legend: {
                                data: ['报销总额']
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            barGap: '0%',
                            barCategoryGap: '50%',
                            xAxis: {
                                type: 'category',
                                data: fields,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: {
                                type: 'value',
                                axisLabel: {
                                    formatter: '{value}万'
                                }
                            },
                            series: [
                                {
                                    name: '报销总额',
                                    type: 'bar',
                                    data: bxAmount,
                                    barWidth: '40%',
                                    label: {
                                        show: true,
                                        position: 'top',
                                        formatter: function (params) {
                                            return bxAmount[params.dataIndex] + '万';
                                        }
                                    },
                                }
                            ]
                        };

                        var saleChart1 = echarts.init(document.getElementById('searchChart1'), myEchartsTheme);
                        saleChart1.setOption(options1);
                        loadMap["searchTable"] = true;

                    }
                },
            )

            var data= {};
            data.timeType = $("input[name='timeType2']").val();
            data.contractName = $("input[name='contractName']").val();
            data.deptName = $("input[name='deptName']").val();
            data.startDate = $("input[name='startDate2']").val();
            data.endDate = $("input[name='endDate2']").val();
            data.bxBy = $("input[name='bxBy']").val();
            data.feeType = $("input[name='feeType']").val();

            $("#searchForm").initTable({
                mars: 'BxDao.bxItemList',
                id: 'searchTable2',
                cellMinWidth: 50,
                limit: 20,
                height: '420px',
                autoSort: false,
                totalRow: true,
                toolbar: true,
                data:data,
                cols: [[
                    {
                        type: 'numbers',
                        align: 'left',
                        totalRowText: "合计"
                    }, {
                        field: 'BX_DATE',
                        title: '费用日期',
                        sort: true,
                        width: 100
                    }, {
                        field: 'RECORDED_DATE',
                        title: '入账日期',
                        sort: true,
                        width: 100
                    }, {
                        field: 'APPLY_BY',
                        title: '报销人',
                        width: 80,
                        templet: function (d) {
                            return getUserName(d.APPLY_BY);
                        }
                    }, {
                        field: 'DEPT_NAME',
                        title: '部门',
                        width: 100
                    }, {
                        field: 'FEE_TYPE',
                        title: '费用类型',
                        width: 100
                    }, {
                        field: 'R_AMOUNT',
                        title: '含税金额',
                        sort: true,
                        width: 100,
                        totalRow: true,
                    }, {
                        field: 'AMOUNT',
                        title: '税后金额',
                        sort: true,
                        width: 100,
                        hide: true,
                        totalRow: true,
                    }, {
                        field: 'TAXES',
                        title: '税金',
                        sort: true,
                        width: 80,
                        hide: true,
                        totalRow: true,
                    }, {
                        field: 'CONTRACT_NAME',
                        title: '合同名称',
                        minWidth: 80,
                    }, {
                        field: 'FEE_DESC',
                        title: '费用说明',
                        minWidth: 80,
                    }
                ]],
                done: function (res) {

                }
            });
        }

        function clearForm() {
            $("#searchField").val("FEE_TYPE");
            $("#startDate").val(new Date().getFullYear() + '-01-01');
            $("#endDate").val(new Date().getFullYear() + '-12-31');
            $("input[name='contractId']").val('');
            $("input[name='contractName']").val('');
            $("input[name='contractNo']").val('');
            $("input[name='bxBy']").val('');
            $("input[name='bxName']").val('');
            $("input[name='deptName']").val('');
            $("input[name='feeType']").val('');
            loadSearchTable();
        }

        function loadBarChart(fieldName, thisYearAmounts, lastYearAmounts, tableName, legend) {
            if (legend == null) {
                legend = ['时段内', '上一年同期', '增长率']
            }
            var growthRates = thisYearAmounts.map((thisYear, index) => {
                var lastYear = lastYearAmounts[index];
                if (lastYear === 0 || lastYear == "0.00") {
                    return 0;
                }
                return ((thisYear - lastYear) / lastYear * 100).toFixed(2);
            });
            var sortedRatios = [...growthRates].sort((a, b) => b - a);
            var ratio3 = parseFloat(sortedRatios[3]);
            if (ratio3 > 30) {
                ratio3 = 30;
            } else if (ratio3 < 0) {
                ratio3 = 0;
            }

            var options3 = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function (params) {
                        var result = params[0].name + '<br/>';
                        params.forEach(function (item) {
                            if (item.seriesType === 'bar') {
                                result += item.marker + item.seriesName + ': ' + (item.value + '万') + '<br/>';
                            } else if (item.seriesType === 'line') {
                                result += item.marker + item.seriesName + ': ' + item.value + '%<br/>';
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                barGap: '0%',
                barCategoryGap: '50%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}万'
                        }
                    },
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: thisYearAmounts,
                        barWidth: '40%',
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                if (params.dataIndex > 2 && params.dataIndex % 2 == 1) {
                                    return '';
                                } else {
                                    return thisYearAmounts[params.dataIndex] + '万';
                                }
                            },
                            textStyle: {
                                fontWeight: 'bold',
                                fontSize: 13,
                                color: '#02374e'
                            }
                        },
                    },
                    {
                        name: legend[1],
                        type: 'bar',
                        data: lastYearAmounts,
                        barWidth: '40%',
                    },
                    {
                        name: legend[2],
                        type: 'line',
                        yAxisIndex: 1,
                        data: growthRates.map(Number),
                        smooth: false,
                        itemStyle: {
                            color: function (params) {
                                return growthRates[params.dataIndex] > ratio3 ? '#ec4c63' : '#5FB878';
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                return growthRates[params.dataIndex] > ratio3 ? growthRates[params.dataIndex] + '%' : '';
                            },
                            textStyle: {
                                color: '#b82843',
                            },
                        }
                    }
                ]
            };

            var chart = echarts.init(document.getElementById(tableName), myEchartsTheme);
            chart.setOption(options3);

            return chart;
        }

        function loadContractBarChart(fieldName, thisYearAmounts, lastYearAmounts, ratio, tableName) {
            var sortedRatios = [...ratio].sort((a, b) => b - a);
            var ratio3 = parseFloat(sortedRatios[2]);
            var legend = ['时段内', '上一年同期', '报销占合同金额比'];
            var options3 = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function (params) {
                        var result = params[0].name + '<br/>';
                        params.forEach(function (item) {
                            if (item.seriesType === 'bar') {
                                result += item.marker + item.seriesName + ': ' + (item.value + '万') + '<br/>';
                            } else if (item.seriesType === 'line') {
                                result += item.marker + item.seriesName + ': ' + item.value + '%<br/>';
                            }
                        });
                        return result;
                    }
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                barGap: '0%',
                barCategoryGap: '50%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}万'
                        }
                    },
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: thisYearAmounts,
                        barWidth: '40%',
                        itemStyle: {
                            opacity: 0.6
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                return thisYearAmounts[params.dataIndex] + '万';
                            },
                            textStyle: {
                                fontWeight: 'bold',
                                fontSize: 14,
                                color: '#02374e'
                            }
                        },
                    },
                    {
                        name: legend[1],
                        type: 'bar',
                        data: lastYearAmounts,
                        barWidth: '40%',
                    },
                    {
                        name: legend[2],
                        type: 'line',
                        yAxisIndex: 1,
                        data: ratio.map(Number),
                        smooth: false,
                        itemStyle: {
                            color: function (params) {
                                return ratio[params.dataIndex] >= ratio3 ? '#ec4c63' : '#5FB878';
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                return ratio[params.dataIndex] >= ratio3 ? ratio[params.dataIndex] + '%' : '';
                            },
                            textStyle: {
                                color: '#b82843',
                            },
                        }
                    }
                ]
            };

            var chart = echarts.init(document.getElementById(tableName), myEchartsTheme);
            chart.setOption(options3);
            if (loadMap["contractBarChartOnClick"]) {
                return;
            }
            chart.on('click', function (params) {
                if (params.componentType === 'series') {
                    var index = params.dataIndex;
                    var contractId = contractIdsRankList[index];
                    var contractName = contractNamesRankList[index];
                    var contractAmount = contractAmountRankList[index];
                    loadContractTable2(contractId,contractName,contractAmount);
                }
            });
            loadMap["contractBarChartOnClick"] = true;
        }

        function contractTableClick(row) {
            loadContractTable2(row.CONTRACT_ID, row.CONTRACT_SIMPILE_NAME,row.AMOUNT);
        }

        function loadRosePieChat(echartsData,tableName){
            var options1 = {
                tooltip: {
                    trigger: "item",
                    formatter: "{b}: {c}万 ({d}%)"
                },
                series: [
                    {
                        name: "花销数据",
                        type: "pie",
                        radius: ['20%', '55%'],
                        center: ['52%', '50%'],
                        roseType: "radius",
                        data: echartsData,
                        minAngle: 2,
                        itemStyle: {
                            color: function (params) {
                                var colorList = ["#63b2ee", "#76da91", "#f8cb7f", "#f89588", "#7cd6cf", "#9192ab", "#7898e1", "#efa666", "#eddd86", "#9987ce","#91cc75"];
                                return colorList[params.dataIndex % colorList.length];
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{b}: {c} 万({d}%)'
                        },
                        labelLine: {
                            show: true
                        }
                    },

                ]
            };
            var myCharts = echarts.init(document.getElementById(tableName), myEchartsTheme);
            myCharts.setOption(options1);

            return myCharts;
        }

        function loadPieChart(echartsData, tableName) {
            var options1 = {
                tooltip: {
                    trigger: "item",
                    formatter: "{b}: {c}万 ({d}%)"
                },
                series: [
                    {
                        name: "花销数据",
                        type: "pie",
                        radius: ['20%', '55%'],
                        data: echartsData,
                        itemStyle: {
                            color: function (params) {
                                var colorList = ["#63b2ee", "#76da91", "#f8cb7f", "#f89588", "#7cd6cf", "#9192ab", "#7898e1", "#efa666", "#eddd86", "#9987ce","#91cc75"];
                                return colorList[params.dataIndex % colorList.length];
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{b}: {c} 万({d}%)'
                        },
                        labelLine: {
                            show: true
                        }
                    },

                ]
            };
            var myCharts = echarts.init(document.getElementById(tableName), myEchartsTheme);
            myCharts.setOption(options1);

            return myCharts;
        }

        function loadContractPieChart(echartsData, tableName) {
            var options1 = {
                tooltip: {
                    trigger: "item",
                    formatter: "{b}: {c}万 ({d}%)"
                },
                series: [
                    {
                        name: "花销数据",
                        type: "pie",
                        radius: ['20%', '55%'],
                        data: echartsData,
                        itemStyle: {
                            color: function (params) {
                                var colorList = ["#63b2ee", "#76da91", "#f8cb7f", "#f89588", "#7cd6cf", "#9192ab", "#7898e1", "#efa666", "#eddd86", "#9987ce","#91cc75"];
                                return colorList[params.dataIndex % colorList.length];
                            }
                        },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{b}: {c} 万({d}%)'
                        },
                        labelLine: {
                            show: true
                        }
                    },

                ]
            };
            var myCharts = echarts.init(document.getElementById(tableName), myEchartsTheme);
            myCharts.setOption(options1);
            if (loadMap["contractPieChartOnClick"]) {
                return;
            }
            myCharts.on('click', function (params) {
                if (params.componentType === 'series') {
                    var index = params.dataIndex;
                    if (index === 10) return;
                    var contractId = contractIdsRankList[index];
                    var contractName = contractNamesRankList[index];
                    var contractAmount = contractAmountRankList[index];
                    loadContractTable2(contractId, contractName,contractAmount);
                }
            });
            loadMap["contractPieChartOnClick"] = true;
        }


        function calGrowthRatio(nameThis, nameLast, nameDiv, word) {
            var sumThis = parseFloat($('#' + nameThis).text());
            var sumLast = parseFloat($('#' + nameLast).text());
            var growthRatio = 0;
            if (sumLast !== 0) {
                growthRatio = ((sumThis - sumLast) / sumLast * 100).toFixed(2);
            }
            if (sumThis >= 10000 && sumLast >= 10000) {
                $('#' + nameThis).text(formatAmount(sumThis) + '万');
                $('#' + nameLast).text(formatAmount(sumLast) + '万');
            }
            if (growthRatio > 0) {
                $('#' + nameDiv).html(
                    word + '<span class="layui-edge layui-edge-top" ></span>' + growthRatio + '%'
                );
            } else if (growthRatio < 0) {
                $('#' + nameDiv).html(
                    word + '<span class="layui-edge layui-edge-bottom"></span>' + Math.abs(growthRatio) + '%'
                );
            }
        }

        function getQuarterFromMonth(month) {
            return Math.ceil(month / 3);
        }

        function subContractName(contractName) {
            if (contractName.length > 10) {
                if (/^\d{4}-\d{4}/.test(contractName.substring(0, 9))) {
                    return contractName.substring(0, 15) + "...";
                } else if (/^\d{4}/.test(contractName.substring(0, 4))) {
                    return contractName.substring(0, 13) + "...";
                }
                return contractName.substring(0, 10) + "...";
            } else {
                return contractName;
            }
        }

        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });


        function ratioColor(ratio) {
            if (ratio === undefined || ratio == null || ratio == "") {
                return '';
            }
            ratio = parseFloat(ratio);
            if (ratio >= 70) {
                return "<label class='label label-danger'>" + ratio.toFixed(2) + "%</label>";
            } else if (ratio >= 50) {
                return "<label class='label label-danger label-outline'>" + ratio.toFixed(2) + "%</label>";
            } else if (ratio >= 30) {
                return "<label class='label label-warning label-outline'>" + ratio.toFixed(2) + "%</label>";
            } else if (ratio >= 10) {
                return "<label class='label label-info label-outline'>" + ratio.toFixed(2) + "%</label>";
            } else {
                return ratio.toFixed(2) + "%";
            }
        }


        function getLastYearDate(dateString) {
            const year = parseInt(dateString.substring(0, 4), 10);
            const lastYear = year - 1;
            return lastYear + dateString.substring(4);
        }

        function formatAmount(amount) {
            return (parseFloat(amount) / 10000).toFixed(2);
        }

        function formatAmount0(amount) {
            return (parseFloat(amount) / 10000).toFixed(0);
        }

        function selctCallBack(id, row) {
            $("[name='contractName']").val(row['CONTRACT_NAME']);
            $("[name='contractNo']").val(row['CONTRACT_NO']);
            $("[name='contractId']").val(row['CONTRACT_ID']);
        }

        function getMonthEndDate(month) {
            var monthEnd = "";
            var year = parseInt(month.split('-')[0], 10);
            var monthNum = parseInt(month.split('-')[1], 10);

            var isLeapYear = (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
            if (monthNum === 2) {
                monthEnd = month + (isLeapYear ? "-29" : "-28");
            } else if ([4, 6, 9, 11].indexOf(monthNum) !== -1) {
                monthEnd = month + "-30";
            } else {
                monthEnd = month + "-31";
            }
            return monthEnd;
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>