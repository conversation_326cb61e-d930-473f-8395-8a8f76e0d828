<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择组件</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectDictForm">
       		  <input name="selectType" id="selectType" type="hidden" value="${param.selectType}">
       		  <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 150px">
					 <span class="input-group-addon">费用名称</span>	
					 <input type="text" name="feeName" class="form-control input-sm">
			     </div>
	   			 <div class="input-group input-group-sm ml-5" style="width: 140px">
					 <span class="input-group-addon">费用编码</span>	
					 <input type="text" name="feeCode" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10"  data-event="enter"  onclick="SelectDict.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		</div>
              <div class="ibox">
              		<table id="dictList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectDict.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectDict={
					sid:'${param.sid}',
					selectType:'${param.selectType}',
					query : function(){
						$("#selectDictForm").queryData({page:false});
					},
					initTable : function(){
						$("#selectDictForm").initTable({
							mars:'BxDao.selectFeeType',
							id:'dictList',
							page:false,
							limit:20,
							rowDoubleEvent:'SelectDict.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'FEE_NAME',
								title: '名称'
							},{
							    field: 'FEE_CODE',
								title: '编号'
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectDict.sid+"']");
						var id = el.data('id');
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('dictList');
							if(checkStatus.data.length>0){
								var selectRow = checkStatus.data[0];
								var names = selectRow['FEE_NAME'];
								var ids = selectRow['FEE_CODE'];
								el.val(names);
								if(el.prev().length>0){
									el.prev().val(ids);
								}
								if(SelectDict.selectType=='out'){
									selctCallBack && selctCallBack(SelectDict.sid,'feeType');
								}else if(names!='项目费用'){
									$("[name='feeType_"+id+"']").attr('data-unclick','1');
									$("[name='feeType_"+id+"']").val(names);
									$("[name='feeCode_"+id+"']").val(ids);
									selctCallBack && selctCallBack(SelectDict.sid,'feeType');
								}else{
									$("[name='feeType_"+id+"']").attr('data-unclick','0');
								}
								descrOfVerificationFee(id);
								popup.layerClose("selectDictForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							var names = selectRow['FEE_NAME'];
							var ids = selectRow['FEE_CODE'];
							el.val(names);
							if(el.prev().length>0){
								el.prev().val(ids);
							}
							if(SelectDict.selectType=='out'){
								selctCallBack && selctCallBack(SelectDict.sid,'feeType');
							}else if(names!='项目费用'){
								$("[name='feeType_"+id+"']").attr('data-unclick','1');
								$("[name='feeType_"+id+"']").val(names);
								$("[name='feeCode_"+id+"']").val(ids);
								selctCallBack && selctCallBack(SelectDict.sid,'feeType');
							}else{
								$("[name='feeType_"+id+"']").attr('data-unclick','0');
							}
							descrOfVerificationFee(id);
							popup.layerClose("selectDictForm");
						}
					}
					
			};
			$(function(){
				SelectDict.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>