package com.yunqu.work.servlet.common;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.Collection;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.FileModel;
/**
 *文件上传辅助类
 *
 */
@WebServlet(urlPatterns = { "/servlet/upload/*" })
@MultipartConfig(maxFileSize=500*1024*1024)
public class UploadServlet extends AppBaseServlet{
	private static final long serialVersionUID = 1L;
	
	public JSONObject actionForIndex(){
		HttpServletRequest request=getRequest();
		try {
			Collection<Part> fileList=request.getParts();
			String source=getPara("source");
			String folderId=getPara("folderId");
			if(fileList==null||fileList.size()<=0){return EasyResult.fail("未选择文件.");}
			JSONArray list=new JSONArray();
			JSONObject jsonObject=null;
			for (Part part:fileList) {
				jsonObject = new JSONObject();
				if(part.getSubmittedFileName()==null){continue;}
				InputStream is = part.getInputStream();  
				String filename = new String(getFilename(part).getBytes(), "UTF-8"); 
				if(StringUtils.isBlank(filename)){
					part.delete();
					continue;
				}
				String fileId=RandomKit.uuid();
				String fileDir=FileKit.getFileDir();
				File file = new File(getBaseDir()+source+fileDir);  
				if (!file.exists()) {  
					file.mkdirs();
				}  
				String newFileName=getNewFileName(fileId,filename);
				String urlPath="/yq-work/files?url=/"+source+fileDir+newFileName;
				File newFile=new File(file +File.separator + newFileName);
				
				jsonObject.put("id",fileId);
				jsonObject.put("url",urlPath);
				jsonObject.put("name",filename);
				jsonObject.put("fsType",FileKit.getHouzui(filename));
				jsonObject.put("path",newFile.getAbsolutePath());
				
				FileModel model=new FileModel();
				model.setFileId(fileId);
				model.setIp(WebKit.getIP(request));
				model.addCreateTime();
				model.set("CREATE_NAME",getUserName());
				model.setFolderId(folderId);
				model.setCreator(getRemoteUser());
				model.setFileName(filename);
				model.setAccessUrl(urlPath);
				model.setDiskPath("/"+source+fileDir+newFileName);
				model.setFileType(FileKit.getHouzui(filename));
				model.setFkId(getPara("fkId"));
				model.setPFkId(getPara("pFkId"));
				model.setSource(getPara("source"));
				String fileLabel = getPara("fileLabel");
				if(StringUtils.notBlank(fileLabel)) {
					model.setFileLabel(fileLabel);
				}
				FileOutputStream fos = new FileOutputStream(newFile);
				byte[] buf = new byte[1024];  
				while (is.read(buf) != -1) {  
					fos.write(buf);  
				}  
				fos.flush();  
				fos.close();  
				is.close();  
				
				part.delete();// 删除临时文件
				
				model.setFileLenth(newFile.length());
				model.setFileSize(getPrintSize(newFile.length()));
				model.save();
				
				jsonObject.put("size",getPrintSize(newFile.length()));
				
				list.add(jsonObject);
			}
			if(list.size()>1) {
				return EasyResult.ok(list, "上传成功!");
			}else {
				return EasyResult.ok(jsonObject, "上传成功!");
			}
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
	    }
	}
	
	public void actionForDel() {
		String id = getJsonPara("id");
		try {
			JSONObject jsonObject = getQuery().queryForRow("select * from yq_files where file_id = ?", new Object[]{id}, new JSONMapperImpl());
			if(jsonObject==null) {
				renderJson(EasyResult.fail("数据不存在"));
				return;
			}
			String diskPath=jsonObject.getString("DISK_PATH");
			String basePath = getBaseDir();
			File file =  new File(basePath+diskPath);
			boolean fileNotExist = false;
		   	if(file==null||!file.exists()){
		   		fileNotExist = true;
		   		this.error("文件不存在",null);
		    }
		   	boolean bl = file.delete();
		   	if(bl||fileNotExist) {
		   		this.getQuery().executeUpdate("delete from yq_file_download_log where file_id =  ?",id);
		   		this.getQuery().executeUpdate("delete from yq_look_log where fk_id =  ?",id);
		   		this.getQuery().executeUpdate("delete from yq_files where file_id =  ?",id);
		   	}
		   	renderJson(EasyResult.ok());
		   	return;
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
	}
	
	private String getPrintSize(long size) {
		// 如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
		double value = (double) size;
		if (value < 1024) {
			return String.valueOf(value) + "B";
		} else {
			value = new BigDecimal(value / 1024).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
		}
		// 如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
		// 因为还没有到达要使用另一个单位的时候
		// 接下去以此类推
		if (value < 1024) {
			return String.valueOf(value) + "KB";
		} else {
			value = new BigDecimal(value / 1024).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
		}
		if (value < 1024) {
			return String.valueOf(value) + "MB";
		} else {
			// 否则如果要以GB为单位的，先除于1024再作同样的处理
			value = new BigDecimal(value / 1024).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
			return String.valueOf(value) + "GB";
		}
	}
	private String getNewFileName(String id,String oldFileName) {
		String filename = id+FileKit.getHouzui(oldFileName);
		return filename;
	}
	 private String getFilename(Part part) {  
	        String contentDispositionHeader = part.getHeader("content-disposition");  
	        String[] elements = contentDispositionHeader.split(";");  
	        for (String element : elements) {  
	            if (element.trim().startsWith("filename")) {  
	                return element.substring(element.indexOf('=') + 1).trim().replace("\"", "");  
	            }  
	        }  
	        return null;  
	}
		
}
