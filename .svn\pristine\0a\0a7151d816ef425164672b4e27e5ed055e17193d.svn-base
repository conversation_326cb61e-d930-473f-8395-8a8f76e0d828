<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>project</title>
	<style>
		.form-horizontal{width: 100%;}
		.h-title{
			font-size: 12px;
    		color: #777;
		}
		.h-value{
			min-height: 14px;
		    margin-top: 3px;
		    font-size: 13px;
		    color: #333;
		    text-overflow: ellipsis;
		    display: -webkit-box;
		    -webkit-line-clamp: 2;
		    -webkit-box-orient: vertical;
		    overflow: hidden;
		}
		.layui-table-cell a{font-size: 12px;color: #3E84E9;cursor: pointer;}
		.layui-tab-card{box-shadow:none;border-style:none;}
		.layui-tab-title li{font-size: 14px;}
		.layui-tab-card>.layui-tab-title{background-color: #fff;border-right: 1px solid #eee;}
		.layui-tab-card>.layui-tab-title .layui-this{color: #3E84E9;border-bottom: 1px solid #3E84E9;}
		.layui-timeline-content p{font-size: 13px;color: #666;}
		.layui-timeline-title{margin-bottom: 2px;}
		.layui-timeline-item{margin-bottom: 2px;}
		.layui-text h3{font-size: 16px;}
		.layui-form-pane .layui-form-label{
			height: 34px;
			line-height: 18px;
			min-width: 100px;
			padding: 8px;
			font-size: 13px;
		}
		.gray-bg{background-color: #e8edf7;}
		.layui-input, .layui-select, .layui-textarea{height: 34px;font-size: 13px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="ContractDetailForm"  data-mars="CustDao.record" data-mars-prefix="cust.">
			    <input type="hidden" value="${param.contractId}" id="contractId" name="contractId"/>
 	  	        <input type="hidden" value="${param.contractId}" name="fkId"/>
 	  	        <input type="hidden" value="${param.custId}" name="custId"/>
				<div style="padding: 5px" class="ibox-content" data-mars="ProjectContractDao.record" data-mars-prefix="contract.">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md8">
					     <i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 18px;" name='contract.CONTRACT_NAME'></span>
					  </div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm">
									<button class="btn btn-default btn-xs" onclick="editContract();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
								</div>
								<div class="btn-group btn-group-sm">
									<button type="button" class="btn btn-xs btn-default dropdown-toggle" data-toggle="dropdown">•••</button>
									<ul class="dropdown-menu dropdown-menu-right">
									    <li><a href="#">删除</a></li>
									    <li><a href="#">转移给他人</a></li>
									    <li><a href="#">更改状态</a></li>
									    <li><a href="#">研发管理</a></li>
									</ul>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding:10px 15px;">
					  <div class="layui-col-md2">
					  	<span class="h-title">合同号</span>
					  	<span class="h-value" name="contract.CONTRACT_NO"></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">签订时间</span>
					  	<span class="h-value" name="contract.SIGN_DATE">--</span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">客户编号</span>
					  	<span class="h-value" name="cust.CUST_NO">--</span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">客户名称</span>
					  	<span class="h-value" name="cust.CUST_NAME"></span>
					  </div>
					  <div class="layui-col-md2">
					  	<span class="h-title">更新时间</span>
					  	<span class="h-value" name="contract.UPDATE_TIME"></span>
					  </div>
					</div>
				</div>
				<div class="layui-row">
					<div class="layui-col-md12">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">基本信息</li>
						    <li>合同阶段信息</li>
						    <li>收入确认阶段</li>
						    <li>合同款项信息</li>
						    <li>收入确认</li>
						    <li>收款信息</li>
						    <li>发票信息</li>
						    <li>合同附件</li>
						    <li>操作记录</li>
						  </ul>
						  <div class="layui-tab-content" style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
						    <div class="layui-tab-item layui-show">
								<jsp:include page="include/base-info.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						    <div class="layui-tab-item">
						    	<p>计划开发中</p>
						    </div>
						  </div>
					</div>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="${ctxPath}/pages/crm/contract/static/table.js"></script>
	<script type="text/javascript" src="${ctxPath}/pages/crm/contract/static/mgr.js"></script>
	<script type="text/javascript">
   
		jQuery.namespace("ContractDetail");
	    
		ContractDetail.contractId='${param.contractId}';
		
		var contractId='${param.contractId}';
		
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
				var form = layui.form;
				form.render();
			});
			$("#ContractDetailForm").render({success:function(result){
				
			}});  
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>