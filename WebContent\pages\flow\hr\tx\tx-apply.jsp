<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>${flow.flowName}</title>
	<style>
		.ibox-content{min-height: calc(100vh - 80px)}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="flowForm" data-mars="FlowDao.baseInfo" data-mars-prefix="apply.">
			   <div class="layui-row ibox-content">
			   	   <div class="layui-col-md12 applyInfo">
				 		<div class="flow-title">${flow.flowTitle}</div>
				    	<table class="table table-vzebra flow-table">
					  		<jsp:include page="/pages/flow/comm/flow-info.jsp"/>
					  		<tr>
					  			<td class="required">申请部门</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.deptName}" readonly="readonly" class="form-control input-sm" name="apply.deptName"/>
					  			</td>
					  			<td class="required">申请人</td>
					  			<td>
					  				<input type="text" data-rules="required" value="${staffInfo.userName}" readonly="readonly" class="form-control input-sm" name="apply.applyName"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required" style="width: 150px;">标题</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" class="form-control input-sm" value="调薪/调岗申请单"  name="apply.applyTitle"/>
					  			</td>
					  			<td class="required" style="width: 150px;">紧急程度</td>
					  			<td>
					  				<select class="form-control input-sm" name="apply.applyLevel">
					  					<option value="正常">正常</option>
					  					<option value="重要">重要</option>
					  					<option value="紧急">紧急</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">姓名</td>
					  			<td>
					  				<input type="hidden" name="apply.data1">
					  				<input type="text" data-rules="required" data-ref-dept="#deptName" data-ref-no="#staffNo" data-ref-job="#jobTitle" data-ref-joindate="#joindate" onclick="singleUser(this)" readonly="readonly" class="form-control input-sm" name="apply.data2"/>
					  			</td>
					  			<td class="required">工号</td>
					  			<td>
					  				<input type="text" data-rules="required" readonly="readonly" id="staffNo" class="form-control input-sm" name="apply.data3"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">部门</td>
					  			<td>
					  				<input type="text" data-rules="required" id="deptName" onclick="singleDept(this)" readonly="readonly" class="form-control input-sm" name="apply.data4"/>
					  			</td>
					  			<td class="required">职位</td>
					  			<td>
					  				<input type="text" data-rules="required" id="jobTitle" class="form-control input-sm" name="apply.data5"/>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">入职日期</td>
					  			<td>
					  				<input type="text" readonly="readonly" id="joindate" class="form-control input-sm" name="apply.data6"/>
					  			</td>
					  			<td class="required">员工类型</td>
					  			<td>
					  				<select class="form-control input-sm" data-rules="required" name="apply.data7">
					  					<option value="">请选择</option>
					  					<option value="正式员工">正式员工</option>
					  					<option value="试用期员工">试用期员工</option>
					  					<option value="实习生">实习生</option>
					  				</select>
					  			</td>
					  		</tr>
					  		<tr>
					  			<td class="required">调整类型</td>
					  			<td>
					  				<select class="form-control input-sm" onchange="changeType(this.value)" data-rules="required" name="apply.data8">
					  					<option value="">--</option>
					  					<option value="调岗">调岗</option>
					  					<option value="调薪">调薪</option>
					  					<option value="调岗调薪">调岗调薪</option>
					  				</select>
					  			</td>
					  			<td></td>
					  			<td></td>
					  		</tr>
					  		<tr class="A2">
					  			<td class="required">调整后部门</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data9"/>
					  			</td>
					  			<td class="required">调整后岗位</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data10"/>
					  			</td>
					  		</tr>
					  		<tr class="A1">
					  			<td class="required">现有薪资</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data11"/>
					  			</td>
					  			<td class="required">调整后薪资</td>
					  			<td>
					  				<input type="text" data-rules="required" class="form-control input-sm" name="apply.data12"/>
					  			</td>
					  		</tr>
					  		<tr class="A1">
					  			<!-- <td class="required">调薪类型</td>
					  			<td>
					  				<select class="form-control input-sm" data-rules="required" name="apply.data10">
					  					<option value="">--</option>
					  					<option value="转正调薪">转正调薪</option>
					  					<option value="年度调薪">年度调薪</option>
					  					<option value="晋升调薪">晋升调薪</option>
					  					<option value="转岗调薪">转岗调薪</option>
					  					<option value="特别调薪">特别调薪</option>
					  				</select>
					  			</td> -->
					  			<td class="required">上次调薪日期</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'month'}" class="form-control input-sm" name="apply.data13"/>
					  			</td>
					  			<td class="required">调薪生效日期</td>
					  			<td>
					  				<input type="text" data-rules="required" data-laydate="{type:'month'}" class="form-control input-sm" name="apply.data14"/>
					  			</td>
					  		</tr>
				  			<tr>
					  			<td class="required">调整理由</td>
					  			<td colspan="3">
									<textarea style="height: 80px;" data-rules="required"  class="form-control input-sm" name="apply.applyRemark"></textarea>
					  			</td>
					  		</tr>
					  </table>
				 </div>
				 <div class="layui-col-md12 approve-result">
				 	 <jsp:include page="/pages/flow/comm/approve-result.jsp"/>
				</div>
			</div>
</form>
</EasyTag:override>

<EasyTag:override name="script">
	
	<script type="text/javascript">
		
		var Flow = {};
	
		$(function(){
			FlowCore.initPage({hideUserTitle:true,title:'{{:data2}}的{{:data8}}申请单',success:function(data){
				var applyInfo = FlowCore.applyInfo;
				var data8 = applyInfo.data8;
				changeType(data8);
			}});
		});
		
		Flow.ajaxSubmitForm = function(state){
			FlowCore.ajaxSubmitForm(state);
		}
		
		Flow.insertData = function() {
			FlowCore.insertData({success:function(result){
				
			}});
		}
		
		Flow.updateData = function() {
			FlowCore.updateData({});
		}
		
		Flow.submitCheck = function() {
			FlowCore.addCheckOrder({});
		}
		
		function changeType(val){
			$('.A1,.A2').hide();
			$('.A1,.A2').find('input').removeAttr('data-rules');
			if(val=='调岗'){
				$('.A2').show();
				$('.A2').find('input').attr('data-rules','required');
			}else if(val=='调薪'){
				$('.A1').show();
				$('.A1').find('input').attr('data-rules','required');
			}else if(val=='调岗调薪'){
				$('.A1,.A2').show();
				$('.A1,.A2').find('input').attr('data-rules','required');
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_flow.jsp" %>