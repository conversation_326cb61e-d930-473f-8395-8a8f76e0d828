<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>projectContract</title>
	<style>
		.form-horizontal{width: 100%;}
		.w-e-text-container{z-index: 0!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		  <form id="contractEditForm" class="form-horizontal" data-mars="ProjectContractDao.record" autocomplete="off" data-mars-prefix="contract.">
 		   		<input type="hidden" value="${param.projectId}" name="contract.PROJECT_ID"/>
 		   		<input type="hidden" value="${param.contractId}" name="contractId"/>
 		   		<input type="hidden" id="contractId" value="${param.contractId}" name="contract.CONTRACT_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td width="80px" class="required">合同简称</td>
		                    <td style="width: 40%"><input data-rules="required" onchange="getContractName();" placeholder="不带合同号" type="text" name="contract.CONTRACT_SIMPILE_NAME" class="form-control input-sm"></td>
		                    <td width="80px" class="required">合同编号</td>
		                    <td style="width: 40%">
		                    	<input data-mars="ProjectContractDao.getContractNo" onchange="getContractName();" data-rules="required"  type="text" name="contract.CONTRACT_NO" class="form-control input-sm">
		                    </td>
			            </tr>
			            <tr>
		                    <td width="80px" class="required">合同名称</td>
		                    <td colspan="3"><input data-rules="required" type="text" name="contract.CONTRACT_NAME" class="form-control input-sm"></td>
			            </tr>
			           <tr>
			           		<td>合同评审号</td>
		                    <td>
			                    <input type="text" placeholder="请点击选择" readonly="readonly" onclick="selectReview(this)" name="contract.REVIEW_ID"  class="form-control input-sm">
		                    </td>
		                    <td class="required">合同年限</td>
		                    <td>
		                    
			                    <input data-rules="required" readonly="readonly" onclick="singleDict(this,'Y005')" name="contract.YEAR"  class="form-control input-sm">
		                    </td>
			           </tr>
			           <tr>
		                    <td class="required">ERP合同号</td>
		                    <td>
			                    <input type="text" name="contract.ERP_CONTRACT_NO" data-rules="required" class="form-control input-sm">
		                    </td>
		                    <td>客户方合同号</td>
		                    <td>
			                    <input type="text" name="contract.CUST_CONTRACT_NO" class="form-control input-sm">
		                    </td>
			           </tr>
			           <tr>
		                    <td>合同签订</td>
		                    <td>
			                     <label class="radio radio-info radio-inline" style="margin-top: 2px;">
		                          	<input type="radio" checked value="0" name="contract.SIGN_FLAG"><span>正式签订</span>
		                          </label>
		                          <label class="radio radio-info radio-inline">
		                          	<input type="radio" value="1" name="contract.SIGN_FLAG"><span>未正式签订</span>
		                          </label>
		                    </td>
		                    <td class="required">状态</td>
			            	<td>
			                     <label class="radio radio-info radio-inline" style="margin-top: 2px;">
		                          	<input type="radio" checked value="Y" name="contract.CONTRACT_STATE"><span>正常</span>
		                          </label>
		                          <label class="radio radio-info radio-inline">
		                          	<input type="radio" value="N" name="contract.CONTRACT_STATE"><span>结束</span>
		                          </label>
		                          <label class="radio radio-info radio-inline">
		                          	<input type="radio" value="Z" name="contract.CONTRACT_STATE"><span>资本化</span>
		                          </label>
		                    </td>
			           </tr>
			           <tr>
		                    <td class="required">客户名称</td>
		                    <td>
			                    <input type="hidden" name="contract.CUST_NO">
			                    <input type="hidden" name="contract.CUST_ID" id="custId">
			                    <input type="text" data-rules="required" onclick="singleCust(this);" readonly="readonly" placeholder="请点击选择客户" name="contract.CUSTOMER_NAME"  class="form-control input-sm">
		                    </td>
		                     <td>关联商机</td>
		                    <td>
			                    <input type="hidden" name="contract.SJ_ID">
			                    <input type="text" onclick="singleSj(this,$('#custId').val());" readonly="readonly" name="contract.SJ_NAME"  class="form-control input-sm">
		                    </td>
			           </tr>
			           <tr>
		                    <td>项目开始</td>
		                    <td>
			                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contract.START_DATE" class="form-control input-sm Wdate">
		                    </td>
		                    <td>项目结束</td>
		                    <td>
			                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" placeholder="警告:若项目未结束请勿填写此时间"  name="contract.END_DATE" class="form-control input-sm Wdate">
		                    </td>
			           </tr>
			            <tr>
		                    <td>签订日期</td>
		                    <td>
			                    <input type="text" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="contract.SIGN_DATE" class="form-control input-sm Wdate">
		                    </td>
		                    <td class="required">合同金额</td>
		                    <td>
			                    <input type="number" data-rules="required" value="" name="contract.AMOUNT" class="form-control input-sm">
		                    </td>
		                </tr>
		                <tr>
		                    <td class="required">签约人</td>
		                    <td>
		                    	<input name="contract.SALES_BY" type="hidden"/>
		                    	<input class="form-control input-sm" data-rules="required" readonly="readonly" onclick="multiUser(this)" name="extend.SALES_BY_NAME">
		                    </td>
		                    <td class="required">收款负责人</td>
		                    <td>
		                    	<input name="contract.PAYEE" type="hidden"/>
		                    	<input class="form-control input-sm" data-rules="required" readonly="readonly" onclick="multiUser(this)" name="extend.PAYEE_NAME">
		                    </td>
			        	</tr>
			        	  <tr>
			        		<td>售前经理</td>
		                    <td>
		                    	<input name="contract.PM_BY" type="hidden"/>
		                    	<input class="form-control input-sm" readonly="readonly" onclick="multiUser(this)" name="extend.PM_BY_NAME">
		                    </td>
		                    <td class="required">营销大区</td>
		                    <td>
		                    	<input type="hidden" name="contract.SALE_DEPT_ID"/>
					  			<input name="extend.SALE_DEPT_NAME" data-rules="required" readonly="readonly" onclick="singleDept(this);" class="form-control">
		                    </td>
		                </tr>
			            <tr>
		                    <td class="required">产品线</td>
		                    <td>
			                    <input data-rules="required" readonly="readonly" onclick="singleDict(this,'Y001')" name="contract.PROD_LINE"  class="form-control input-sm">
		                    </td>
		                    <td class="required">合同类型</td>
		                    <td>
		                    	<input data-rules="required" readonly="readonly" onclick="singleDict(this,'Y004')" name="contract.CONTRACT_TYPE"  class="form-control input-sm">
		                    </td>
		                </tr>
			            <tr>
			            	<td>合同描述</td>
			               	<td colspan="3">
	                           <textarea style="display: none;" id="textJson" class="form-control input-sm" name="contract.TEXT_JSON"></textarea>
	                           <textarea id="wordText" style="height: 120px;" class="form-control input-sm" name="contract.CONTRAC_DESC"></textarea>
			               	</td>
			            </tr>
			        </tbody>
 				</table>
			    <p class="layer-foot text-c" style="z-index: 999999999">
			    	  <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="Contract.ajaxSubmitForm()"> 保 存 </button>
				      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
				</p>
  	    </form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
   
		var Contract = {contractId:'${param.contractId}'}
	    
		var contractId = Contract.contractId;
		
		$(function(){
		    $('[data-toggle="tooltip"]').tooltip();
			$("#contractEditForm").render({success:function(result){
				var dataJsonStr = $('#textJson').val();
				if(dataJsonStr){
					var textJson = eval("("+dataJsonStr+")");
					fillRecord(textJson,"extend.",",","#contractEditForm");
				}
			}});  
		});
		Contract.ajaxSubmitForm = function(){
			if(form.validate("#contractEditForm")){
				if(Contract.contractId){
					Contract.updateData(); 
				}else{
					Contract.insertData(); 
				}
			};
		}
		Contract.insertData = function(flag) {
			var data = form.getJSONObject("#contractEditForm");
			data['contract.TEXT_JSON']= getTextVal(data);
			ajax.remoteCall("${ctxPath}/servlet/projectContract?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Contract.updateData = function(flag) {
			var data = form.getJSONObject("#contractEditForm");
			data['contract.TEXT_JSON']= getTextVal(data);
			ajax.remoteCall("${ctxPath}/servlet/projectContract?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						location.reload();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function selectReview(el){
			var id = new Date().getTime();
			$(el).attr('data-sid',id);
			popup.layerShow({id:'selectCust',full:fullShow(),scrollbar:false,area:['600px','550px'],offset:'20px',title:'选择评审',url:'/yq-work/pages/flow/select/select-review.jsp',data:{sid:id,type:'radio'}});
		}
		
		function selectReviewCallBack(row){
			$("[name='contract.REVIEW_ID']").val(row['APPLY_NO']);
			
			$("[name='contract.CONTRACT_SIMPILE_NAME']").val(row['CONTRACT_NAME']);
			$("[name='contract.CONTRACT_NAME']").val(row['CONTRACT_NAME']);
			
			$("[name='contract.CUSTOMER_NAME']").val(row['CUST_NAME']);
			$("[name='contract.CUST_ID']").val(row['CUST_ID']);
			$("[name='contract.CUST_NO']").val(row['CUST_NO']);
			
			$("[name='extend.PM_BY_NAME']").val(row['PRE_SALES_NAME']);
			$("[name='contract.PM_BY']").val(row['PRE_SALES_BY']);
			
			$("[name='extend.SALES_BY_NAME']").val(row['SALES_BY_NAME']);
			$("[name='contract.SALES_BY']").val(row['SALES_BY']);
			
			$("[name='extend.PAYEE_NAME']").val(row['SALES_BY_NAME']);
			$("[name='contract.PAYEE']").val(row['SALES_BY']);
			
			$("[name='extend.SALE_DEPT_NAME']").val(row['SIGN_DEPT_NAME']);
			$("[name='contract.SALE_DEPT_ID']").val(row['SIGN_DEPT_ID']);
			
			$("[name='contract.PROD_LINE']").val(row['PROD_LINE']);
			
			getContractName();
		}
		
		function getContractName(){
			var v1 = $("[name='contract.CONTRACT_NO']").val();
			var v2 = $("[name='contract.CONTRACT_SIMPILE_NAME']").val();
			$("[name='contract.CONTRACT_NAME']").val(v1+" "+v2);
		}
		
		function getTextVal(formData){
			var data = $.extend({},formData);
			var json = {};
			for(var key in data){
				if(key.indexOf("extend.")>-1){
					var newKey = key.replace('extend.','');
					json[newKey] = data[key];
				}
			}
			return JSON.stringify(json);
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>