<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>预览日志</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="flowViewForm">
		<div class="layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: 0px;margin-bottom: -8px;">
        	<ul class="layui-tab-title"><li data-val="1" class="layui-this">浏览记录</li><li data-val="2">下载日志</li><li data-val="3">操作日志</li><li data-val="4">打印日志</li></ul>
        	<div class="layui-tab-content" style="margin:-10px;">
			    <div class="layui-tab-item layui-show">
					 <table id="logViewTable"></table>
			    </div>
			    <div class="layui-tab-item">
					 <table id="downloadLogTable"></table>
			    </div>
			    <div class="layui-tab-item">
			    	<table id="reviseLogTable"></table>
			    </div>
			    <div class="layui-tab-item">
			    	<table id="printLogTable"></table>
			    </div>
		  </div>
        </div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
		var FlowView = {fkId:'${param.fkId}'};		
		
		$(function(){
			
			$("#flowViewForm").initTable({
				mars:'CommonDao.lookLogList',
				id:'logViewTable',
				height:'400px',
				data:{fkId:FlowView.fkId},
				cols: [[
		         {
		    	 	type: 'numbers',
					title: '序号',
					align:'left'
				 },{
				    field: 'LOOK_NAME',
					title: '查看人',
					align:'center',
					width:80,
					templet:'<div><a href="javascript:;" onclick="userInfoLayer(\'{{d.LOOK_BY}}\')">{{d.LOOK_NAME}}</a></div>'
				},{
				    field: 'LOOK_TIME',
					title: '查看时间',
					align:'center'
				},{
				    field: 'IP',
					title: '访问ip',
					align:'left'
				},{
				    field: 'DEVICE',
					title: '访问设备',
					align:'left'
				}
			 ]]
		   });
			
		$("#flowViewForm").initTable({
			mars:'CommonDao.downloadLog',
			id:'downloadLogTable',
			height:'400px',
			data:{fkId:FlowView.fkId},
			cols: [[
	         {
	    	 	type: 'numbers',
				title: '序号',
				align:'left'
			 },{
			    field: 'FILE_NAME',
				title: '文件名',
				width:160,
				align:'left'
			},
			{
			    field: 'FILE_SIZE',
				title: '文件大小',
				width:80,
				align:'center'
			},
			{
			    field: 'DOWNLOAD_BY',
				title: '下载人',
				align:'center',
				width:80,
				templet:function(row){
					return getUserName(row.DOWNLOAD_BY);
			 }
			},{
			    field: 'DOWNLOAD_TIME',
				title: '下载时间',
				width:145,
				align:'center'
			}
			]]}
		);
		
		$("#flowViewForm").initTable({
			mars:'FlowCommon.reviseList',
			id:'reviseLogTable',
			height:'400px',
			page:false,
			data:{businessId:FlowView.fkId},
			cols: [[
	         {
	    	 	type: 'numbers',
				title: '序号',
				align:'left'
			 },{
			    field: 'REVISE_CONTENT',
				title: '操作内容',
				width:260,
				align:'left'
			},
			{
			    field: 'ADD_USER_NAME',
				title: '操作人',
				align:'center',
				width:80
			},{
			    field: 'ADD_TIME',
				title: '操作时间',
				width:145,
				align:'center'
			}
			]]}
		);
		
		$("#flowViewForm").initTable({
			mars:'FlowCommon.printList',
			id:'printLogTable',
			height:'400px',
			page:false,
			data:{businessId:FlowView.fkId},
			cols: [[
	         {
	    	 	type: 'numbers',
				title: '序号',
				align:'left'
			 },
			 {
			    field: 'PRINT_BY_NAME',
				title: '打印人',
				align:'center',
				width:100
			},{
			    field: 'PRINT_TIME',
				title: '打印时间',
				width:145,
				align:'center'
			}
			]]}
		);
			
	});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>