<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>人员项目任务统计</title>
	<style>
		.layui-badge{left: -1px!important;}
		.layui-tab-item {
		    padding: 5px 5px;
		}
		.layui-tab-content {
		    padding: 5px 0px;
		}
		[rowspan]{vertical-align: top;}
		
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
		.icon {width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:1px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="taskMgrForm">
			<c:choose>
				<c:when test="${param.isAll=='1'}">
					<input type="hidden" name="isSelfDept" value="0">
				</c:when>
				<c:otherwise>
					<input type="hidden" name="isSelfDept" value="1">
				</c:otherwise>
			</c:choose>
			<input name="taskBusinessId" id="taskBusinessId" value="${param.businessId}" type="hidden"/>
			<div class="layui-card">
		        <div class="layui-card-header">
		         		<div class="input-group input-group-sm" style="width: 260px">
	          		    	<span class="input-group-addon">执行时间</span>	
                     		<input data-mars="CommonDao.threeMonthRange" data-mars-top="true" data-mars-reload="false" type="text" data-laydate="{type:'date',range: '到'}" name="planDate" class="form-control input-sm">
                    	 </div>
                    	<div class="btn-group-sm btn-group select-date ml-10">
							<button class="btn btn-default btn-xs" type="button" value="today">今日</button>
							<button class="btn btn-default btn-xs" type="button" value="yesterday">昨天</button>
							<button class="btn btn-default btn-xs" type="button" value="currentMonth">本月</button>
							<button class="btn btn-default btn-xs" type="button" value="threeDay">3天内</button>
							<button class="btn btn-default btn-xs" type="button" value="oneWeek">1周内</button>
							<button class="btn btn-default btn-xs" type="button" value="oneMonth">1个月内</button>
							<button class="btn btn-info  btn-xs" type="button" value="threeMonth">3个月内</button>
							<button class="btn btn-default btn-xs" type="button" value="halfYear">半年内</button>
							<button class="btn btn-default btn-xs" type="button" value="oneYear">1年内</button>
							<button class="btn btn-default btn-xs" type="button" value="nowYear">今年</button>
							<button class="btn btn-default btn-xs" type="button" value="">全部</button>
                     </div>
		        </div>
		        <div class="layui-card-body">
						<c:choose>
			             	<c:when test="${!empty param.projectId}">
								 <input type="hidden" name="projectId" value="${param.projectId}" class="form-control input-sm">
			             	</c:when>
			             	<c:otherwise>
				             	<div class="input-group input-group-sm" style="width: 160px">
		          		    		<span class="input-group-addon">项目</span>	
								    <input type="hidden" name="projectId" class="form-control input-sm">
								    <input type="text" id="projectId" onclick="singleProject(this);" class="form-control input-sm">
		          		    	</div>
			             	</c:otherwise>
			             </c:choose>
                    	  <div class="input-group input-group-sm">
			             		 <span class="input-group-addon">创建人</span>
								 <input type="hidden" name="creator" class="form-control input-sm">
								 <input type="text" onclick="singleUser(this)" class="form-control input-sm" style="width: 60px">
			             </div>
			              <div class="input-group input-group-sm">
			             		 <span class="input-group-addon">负责人</span>
								 <input type="hidden" name="assignUserId" class="form-control input-sm">
								 <input type="text" onclick="singleUser(this)" class="form-control input-sm" style="width: 60px;">
			             </div>
		             	<div class="input-group input-group-sm">
		            		 <span class="input-group-addon">发起部门</span>
			             	 <input name="deptId" type="hidden">
							 <input style="width: 80px" onclick="singleDept(this);" class="form-control input-sm">
		                 </div>
		             	<div class="input-group input-group-sm">
		            		 <span class="input-group-addon">任务类型</span>
			             	 <input name="deptId" type="hidden">
							 <select style="width: 80px" data-mars-reload="false" name="taskTypeId" data-mars="TaskDao.taskType" class="form-control input-sm">
								<option value="">请选择</option>
							 </select>
		                 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-info ml-5" onclick="taskMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="taskMgr.clearForm()">清空</button>
						 </div>
						 <div  class="input-group input-group-sm" id="headerEnd"></div>
		        </div>
		    </div>
			<div class="ibox" style="margin-top: -10px;">
				  <div class="ibox-content">
					   <table id="taskProjectStat" class="layui-hide mb-30"></table>
				  </div>
			</div>
		</form>
	</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
<script type="text/javascript">
		
		var firstLoad = true;
		var taskMgr = {
			query:function(qType){
				taskProjectStatTable('query');
			},
			clearForm:function(){
				var planDate = $("[name='planDate']").val();
				$("[name='projectId']").val('');
				$("[name='creator']").val('');
				$("[name='assignUserId']").val('');
				$("[name='deptId']").val('');
				$("#taskMgrForm")[0].reset();
				$("[name='planDate']").val(planDate);
				taskMgr.query();
			},
			projectDetail:function(data){
				var projectId = data['PROJECT_ID'];
				if(projectId){
					projectBoard(projectId);
					return;
				}
				var businessId = data['BUSINESS_ID'];
				if(businessId){
					popup.openTab({id:'businessFollowList',url:'${ctxPath}/pages/crm/contacts/contact-query.jsp',title:'商机跟进记录',data:{businessId:businessId}});
				}
			}
		}
		
		$(function(){
			$("#taskMgrForm").render({success:function(result){
				taskProjectStatTable();
			}});
			
			$('.select-date button').click(function(){
				getDate($(this)[0],'planDate');
				$(this).siblings().removeClass('btn-info');
				$(this).siblings().addClass('btn-default');
				$(this).addClass('btn-info');
			});
		});
		
		var getDate = function(obj,inputName){
			var val = obj.value;
			var bdate = new Date();
			var edate = new Date();
			if('createTime'==inputName||'planDate'==inputName){
				if(val == 'today'){
					
				}else if(val == 'yesterday'){
					bdate.setDate(bdate.getDate() - 1);
					edate = bdate;
				}else if(val == 'threeDay'){
					bdate.setDate(bdate.getDate() - 3);
				}else if(val == 'oneWeek'){
					bdate.setDate(bdate.getDate() - 7);
				}else if(val == 'oneMonth'){
					bdate.setMonth(bdate.getMonth() - 1);
				}else if(val == 'threeMonth'){
					bdate.setMonth(bdate.getMonth() - 3);
				}else if(val == 'halfYear'){
					bdate.setMonth(bdate.getMonth() - 6);
				}else if(val == 'oneYear'){
					bdate.setMonth(bdate.getMonth() - 12);
				}else if(val == 'currentMonth'){
					bdate.setDate(1);
					edate.setMonth(bdate.getMonth() + 1);
					edate.setDate(0);
				}
				if(val){
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}else{
				if(val){
					bdate.setDate(bdate.getDate() - val);
					var v = DateUtils.dateFormat(bdate,'yyyy-MM-dd')+' 到 '+DateUtils.dateFormat(edate,'yyyy-MM-dd');
					$('[name="'+inputName+'"]').val(v);
				}else{
					$('[name="'+inputName+'"]').val('');
				}
			}
			taskMgr.query();
		}
		
		var taskProjectStat = true;
		function taskProjectStatTable(type){
			if(taskProjectStat){
				taskProjectStat = false;
				var projectId = '${param.projectId}'; 
				var hideProject = projectId!='';
				$("#taskMgrForm").initTable({
					id:'taskProjectStat',
					mars:'TaskDao.projectUserTaskStat',
					limit:30,
					page:true,
					data:{'selectUser':'1'},
					cellMinWidth:100,
					totalRow:true,
					toolbar:true,
					defaultToolbar:['filter', 'print', 'exports'],
					limits:[10,15,30,50,100,200,300],
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'center'
					 },{
						 field:'ASSIGN_DEPT_NAME',
						 title:'部门',
						 sort:true
					 },{
					    field: 'ASSIGN_USER_NAME',
						title: '姓名',
						align:'center',
						width:100,
						sort:true,
						event:'_selectUser',
						totalRowText:'汇总'
					},{
						 field:'PROJECT_NAME',
						 title:'项目名称',
						 minWidth:300,
						 hide:hideProject,
						 event:'taskMgr.projectDetail'
					 },{
						field:'',
						title: '已完成/总数',
						event:'_selectUser',
						align:'center',
						width:90,
						templet:function(row){
							return row['H']+"/"+row['F'];
						}
					},{
					    field: 'F',
						title: '任务总数',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'G',
						title: '未完成数',
						event:'_selectUser',
						align:'center',
						sort:true,
						totalRow:true
					},{
					    field: 'A',
						title: '待办数',
						event:'_selectUser',
						align:'center',
						sort:true,
						totalRow:true
					}
					 ,{
					    field: 'B',
						title: '进行中',
						sort:true,
						align:'center',
						totalRow:true
					},{
					    field: 'E',
						title: '超时未完成',
						sort:true,
						align:'center',
						totalRow:true
					}
				  ,{
					   field: 'C',
					   title: '已完未验收',
					   sort:true,
					   align:'center',
						totalRow:true
				},{
					   field: 'D',
					   title: '已验收',
					   sort:true,
					   align:'center',
					   totalRow:true
					}
				]],done:function(res,curr,count){
					table.on('radio(taskProjectStat)', function(obj){
						_selectUser(obj.data);
					});
				}}
			  );
			}else if(type=='query'){
				$("#taskMgrForm").queryData({id:'taskProjectStat',data:{'selectUser':'1'},jumpOne:true,page:true});
			}
			
		}
		
		
		function _selectUser(data){
			var userId=data['ASSIGN_USER_ID'];
			var userName = data['ASSIGN_USER_NAME'];
			$("[name='assignUserId']").val(userId);
			$("[name='assignUserId']").next().val(userName);
			taskMgr.query();
			$("#headerEnd").html("<div style='cursor:pointer' class='label label-success label-outline ml-10' onclick='delUser(this)'><span class='selectUserSpan'>"+userName+"</span> x</div>");
			$("[data-flag='1']").click();
		}
		
		function delUser(el){
			$("[name='assignUserId']").val('');
			$("[name='assignUserId']").next().val('');
			$(el).remove();
			taskMgr.query();
		}
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>