package com.yunqu.work.servlet.crm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.IncomeStageModel;
import com.yunqu.work.service.ContractService;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;


import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
@WebServlet("/servlet/incomeStage/*")
public class IncomeStageServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public EasyResult actionForAdd() {
        IncomeStageModel model = getModel(IncomeStageModel.class, "incomeStage");
        model.setCreator(getUserName());
        model.addCreateTime();
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        model.set("UPDATE_BY", getUserName());
        model.setPrimaryValues(RandomKit.uuid().toUpperCase());
        String contractStageId = model.getString("CONTRACT_STAGE_ID");
        String incomeStageId = model.getIncomeStageId();
        String planDate = model.getString("PLAN_COMP_DATE");
        String contractId = model.getString("CONTRACT_ID");

        int writeMode = model.getIntValue("WRITE_MODE");
        //合同阶段对应模式
        if (writeMode == 1) {
            String setStageResult = addStageInfoForContractStage(contractStageId, incomeStageId, planDate);
            if (!"success".equals(setStageResult)) {
                return EasyResult.fail(setStageResult);
            }
        }

        model.set("MONTH_ID", DateUtils.getMonthIdFormat(planDate));

        String amount = model.getString("PLAN_AMOUNT");
        model.set("UNCONFIRM_AMOUNT_A", amount);
        model.set("UNCONFIRM_AMOUNT_B", amount);

        try {
            model.save();
            ContractService.getService().reloadCount("INCOME_STAGE_COUNT", "yq_contract_income_stage", contractId);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }

    public EasyResult actionForUpdate() {
        IncomeStageModel model = getModel(IncomeStageModel.class, "incomeStage");
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        model.set("UPDATE_BY", getUserName());
        String oldContractStageIds = getJsonPara("oldContractStageIds");
        String incomeStageId = model.getIncomeStageId();
        String contractStageIds = model.getString("CONTRACT_STAGE_ID");
        String planDate = model.getString("PLAN_COMP_DATE");
        int writeMode = model.getIntValue("WRITE_MODE");

        //不管改前是什么模式，都删除旧的
        if (StringUtils.isNotBlank(oldContractStageIds)) {
            String setContractStageResult2 = removeStageInfoForContractStage(oldContractStageIds);
            if (!"success".equals(setContractStageResult2)) {
                return EasyResult.fail(setContractStageResult2);
            }
        }

        //合同阶段对应模式，加新的
        if (writeMode == 1) {
            String setContractStageResult = addStageInfoForContractStage(contractStageIds, incomeStageId, planDate);
            if (!"success".equals(setContractStageResult)) {
                return EasyResult.fail(setContractStageResult);
            }
        }
        String oldAmount = getJsonPara("oldAmount");
        String newAmount = model.getString("PLAN_AMOUNT");
        if (!newAmount.equals(oldAmount)) {
            model.set("UNCONFIRM_AMOUNT_A", newAmount);
            model.set("UNCONFIRM_AMOUNT_B", newAmount);
        }
        model.set("MONTH_ID", DateUtils.getMonthIdFormat(planDate));

        try {
            model.update();
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        }
        return EasyResult.ok();
    }


    public EasyResult actionForBatchDel() {
        JSONArray incomeStageArray = getJSONArray();
        for (int i = 0; i < incomeStageArray.size(); i++) {
            JSONObject incomeStageObject = incomeStageArray.getJSONObject(i);
            String contractStageId = incomeStageObject.getString("CONTRACT_STAGE_ID");
            String incomeStageId = incomeStageObject.getString("INCOME_STAGE_ID");
            int writeMode = incomeStageObject.getIntValue("WRITE_MODE");
            //合同阶段对应模式
            if (writeMode == 1) {
                if (StringUtils.notBlank(contractStageId)) {
                    String setContractStageResult = removeStageInfoForContractStage(contractStageId);
                    if (!"success".equals(setContractStageResult)) {
                        return EasyResult.fail(setContractStageResult);
                    }
                }
            }
            try {
                this.getQuery().executeUpdate("delete from yq_contract_income_stage where INCOME_STAGE_ID = ?", incomeStageId);
                String contractId = incomeStageObject.getString("CONTRACT_ID");
                ContractService.getService().reloadCount("INCOME_STAGE_COUNT", "yq_contract_income_stage", contractId);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return EasyResult.fail(e.getMessage());
            }
        }
        return EasyResult.ok();
    }

    public String addStageInfoForContractStage(String contractStageIds, String incomeStageId, String planDate) {
        String[] contractStages = contractStageIds.split(",");
        for (String contractStageId : contractStages) {
            String sql = "UPDATE yq_contract_stage SET INCOME_STAGE_FLAG = 1 ,INCOME_STAGE_ID = ? ,PLAN_COMP_DATE = ? WHERE STAGE_ID = ?";
            try {
                this.getQuery().executeUpdate(sql, new Object[]{incomeStageId, planDate, contractStageId,});
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return "修改合同阶段状态失败：" + e.getMessage();
            }
        }
        return "success";
    }

    public String removeStageInfoForContractStage(String contractStageIds) {
        String[] contractStages = contractStageIds.split(",");
        for (String contractStageId : contractStages) {
            String sql = "UPDATE yq_contract_stage SET INCOME_STAGE_FLAG = 0 ,INCOME_STAGE_ID = '' ,PLAN_COMP_DATE = '' WHERE STAGE_ID = ?";
            try {
                this.getQuery().executeUpdate(sql, contractStageId);
            } catch (SQLException e) {
                this.error(e.getMessage(), e);
                return "删除合同阶段状态失败：" + e.getMessage();
            }
        }
        return "success";
    }


}
