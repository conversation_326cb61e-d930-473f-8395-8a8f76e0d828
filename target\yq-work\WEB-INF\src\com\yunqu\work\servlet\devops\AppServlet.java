package com.yunqu.work.servlet.devops;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.AppModel;
/**
 * 应用管理
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/app/*")
public class AppServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		AppModel model=getModel(AppModel.class, "app");
		model.addCreateTime();
		model.setCreator(getUserPrincipal().getUserId());
		try {
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		AppModel model=getModel(AppModel.class, "app");
		try {
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		AppModel model=getModel(AppModel.class, "app");
		try {
			model.delete();
			String sql="delete from yq_ops_version where group_id = ?";
			this.getQuery().executeUpdate(sql, model.getGroupId());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}





