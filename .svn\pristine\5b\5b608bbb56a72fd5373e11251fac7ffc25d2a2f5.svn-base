var FlowMgr = {
	joinCols:function(param,index,fn){
		var flowCode = $("[name='flowCode']").val();
		if(flowCode){
			var cacheData  = layui.sessionData('yqwork')['flowCode_'+flowCode];
			if(cacheData){
				doThis(cacheData);
				fn && fn(cacheData);
			}else{
				ajax.daoCall({async:false,loading:false,controls:['FlowConfigDao.flowFields'],params:{flowCode:flowCode}},function(rs){
					var data = rs['FlowConfigDao.flowFields'].data;
					var listdata = [];
					for(var index in data){
				  		var newObj = data[index];
				  		let tableField = newObj.field;
				  		let tableFlag = newObj.tableFlag;
						if(newObj['listshow']=='1'){
				  			if(tableFlag==1&&newObj.templet==undefined){
				  				newObj.templet=function(row){
				  					let extendStr = row['DATA_JSON']||'{}';
				  					let json = eval('('+extendStr+')')||{};
				  					return getVal(json[tableField]);
				  				}
				  			}else if(newObj.templet!=undefined){
				  				newObj.templet = eval(newObj.templet);
				  			}else{
				  				newObj.templet = function(row){
				  				  return getVal(row[tableField]);
				  				}
				  			}
							listdata.push(newObj);
						}
				}
				doThis(listdata);
				layui.sessionData('yqwork', {key:'flowCode_'+flowCode,value:data});
				fn && fn(data);
			  });
			}
			function getVal(val){
				if(val==undefined){
					return '--';
				}
				return val;
			}
			function doThis(data){
				var colsConf  = data||[];
				var cols = param.cols[0]; 
				var result = cols.slice(0,index).concat(colsConf).concat(cols.slice(index,cols.length));
				param.cols[0] = result;
			}
		}else{
			fn && fn({});
		}
	},
	getCondition:function(data){
		var condition = [];
		for(var index in data){
			var newObj = data[index];
			if(newObj['query']){
				condition.push(newObj);
			}
		}
		return condition;
	}
}

 function leftShow(el){
	   var flag = $(el).data('flag');
	   if(flag=='1'){
		   $(el).prev().hide(0);
		   $(el).next().css('width','100%').css('margin-left','0px');
		   $(el).data('flag','0');
		   $(el).html('<i class="fa fa-chevron-right"></i>');
	   }else{
		   $(el).prev().show(300);
		   $(el).next().css('width','calc(100% - 160px)');
		   $(el).data('flag','1');
		   $(el).html('<i class="fa fa-chevron-left"></i>');
	   }
}


