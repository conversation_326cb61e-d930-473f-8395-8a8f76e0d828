<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>文档</title>
	<style>
		.isSuper{display: none;}
		#editForm img{max-width: 100%;height: auto;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     	<form id="editForm" data-mars="DocDao.record" autocomplete="off" data-mars-prefix="doc.">
     		   <input type="hidden" id="_randomId" data-mars="CommonDao.randomId"/>
   		  	   <input type="hidden" id="_docId" value="${param.docId}" name="doc.DOC_ID"/>
   		  	   <input type="hidden" name="fkId" value="${param.docId}"/>
   		  	   <input type="hidden" value="${param.folderId}" name="doc.FOLDER_ID"/>
				<table class="table table-edit table-vzebra">
			        <tbody>
			            <tr>
		                    <td style="width: 80px" class="required">文档名称</td>
		                    <td><input maxlength="100" data-rules="required"  type="text" name="doc.TITLE" class="form-control input-sm"></td>
			            </tr>
			           <!--  <tr class="detail">
			            	 <td class="required">负责人</td>
		                    <td>
		                   	   <select class="form-control input-sm" data-rules="required" data-mars="CommonDao.userDict" name="doc.MANAGER">
		                    		<option value="">请选择</option>
		                    	</select>
		                    </td>
			            </tr> -->
			            <tr>
		                    <td class="required">文档正文</td>
		                    <td>
		                       <div id="editor"></div>
	                           <textarea id="wordText" data-text="false" style="height: 500px;width:400px;display: none;" class="form-control input-sm" name="doc.DOC_DESC"></textarea>
		                    
		                    </td>
			            </tr>
			            <tr>
		                    <td class="required">附件</td>
		                    <td>
                   				<div data-template="template-files" data-mars="FileDao.fileList"></div>
			               		<div id="fileList_edit"></div>
								<button class="btn btn-sm btn-default mt-5 detail" type="button" onclick="$('#localfileEdit').click()">+添加</button>
		                    </td>
			            </tr>
			            <tr class="detail">
		                    <td class="required">共享设置</td>
		                    <td>
			                    <label class="radio radio-success radio-inline">
			                    	<input type="radio" checked="checked" value="1" name="doc.DOC_AUTH"/> <span>所有人</span>
			                    </label>
			                    <label class="radio radio-success radio-inline">
			                    	<input type="radio" value="2" name="doc.DOC_AUTH"/> <span>私有</span>
			                    </label>
			                    <span class="ml-30 isSuper">排序</span>
			                    <span class="ml-15 isSuper" style="display: inline-block;">
			                   		<input type="number" name="doc.ORDER_INDEX" onfocus="layer.tips('默认99,数值越大越排后面',this)" style="width: 70px" value="99" class="form-control input-sm"/>
			                    </span>
		                    </td>
			            </tr>
			        </tbody>
 					  </table>
						 <div class="layer-foot text-c">
						    	  <button type="button" class="btn btn-primary btn-sm detail"  onclick="Edit.ajaxSubmitForm()"> 保 存 </button>
							      <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" onclick="layer.closeAll();"> 关闭 </button>
						</div>
  		</form>
  		<script id="template-files" type="text/x-jsrender">
			{{for data}}
				<div class="file-div"><input name="fileIds" value='{{:FILE_ID}}' type="hidden"/><a href="${ctxPath}/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{:FILE_NAME}}</span> <span style="color:#17a6f0">下载<span></a><i title="删除" class="detail" data-id="{{:FILE_ID}}" onclick="delFile($(this))">x</i></div>
			{{/for}}
		</script>
  		<form  id="fileFormEdit" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="localfileEdit" onchange="Edit.uploadFile()"/>
  		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/wangEditor.min.js"></script>
	<script type="text/javascript">
	
		jQuery.namespace("Edit");
	
		Edit.docId='${param.docId}';
		var E = window.wangEditor;
		var editor = new E('#editor');
		editor.customConfig.menus = [
     		    'head',  // 标题
     		    'bold',  // 粗体
     		    'fontSize',  // 字号
     		    'fontName',  // 字体
     		    'foreColor',  // 文字颜色
     		    'link',  // 插入链接
     		    'list',  // 列表
     		    'justify',  // 对齐方式
     		    'quote',  // 引用
     		    'code',  // 插入代码
     		    'emoticon',  // 表情
     		    'image',  // 插入图片
     		    'table',  // 表格'
     	];
		weUpload(editor,{uploadImgMaxLength:3});
		editor.customConfig.onchange = function (html) {
		     $("#wordText").val(html)
		}
		editor.create();
		
		$(function(){
			var op='${param.op}';
			$("#editForm").render({success:function(result){
				 editor.txt.html($("#wordText").val());
				 var height=$(window).height();
				 $(".w-e-text-container").css("height",height-400);
				 if(op=='detail'){
					$(".detail").hide();
					editor.$textElem.attr('contenteditable', false);
				}
				if(isSuperUser){
					$(".isSuper").show();
				}
				layer.photos({photos:'#editor',anim: 0,shade:0,shadeClose:true,closeBtn:true}); 
				/* requreLib.setplugs('select2',function(){
				   $("#editForm select[name='doc.MANAGER']").select2({theme: "bootstrap",placeholder:'请选择'});
				}); */
			}});
		});
		Edit.uploadFile = function(){
			var fkId='';
			if(Edit.docId){
				fkId=Edit.docId;
			}else{
				fkId=$("#_randomId").val();
			}
			easyUploadFile({formId:'fileFormEdit',fileId:'localfileEdit',callback:'callbackEdit',fkId:fkId,source:'doc'});
			
		}
		var callbackEdit = function(data){
			$("#fileList_edit").append('<div class="file-div"><input name="fileIds" value='+data.id+' type="hidden"/><span><a href="'+data.url+'" target="_blank">'+data.name+'</a></span><i title="删除" data-id="'+data.id+'" onclick="delFile($(this))">x</i></div>');
		}
		Edit.ajaxSubmitForm = function(){
			if(form.validate("#editForm")){
				if(Edit.docId){
					Edit.updateData(); 
				}else{
					Edit.insertData(); 
				}
			};
		}
		Edit.insertData = function() {
			$("#_docId").val($("#_randomId").val());
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/doc?action=add",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		Edit.updateData = function() {
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/doc?action=update",data,function(result) { 
				if(result.state == 1){
					layer.msg("操作成功！",{icon:1,time:1200},function(){
						list.query();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>