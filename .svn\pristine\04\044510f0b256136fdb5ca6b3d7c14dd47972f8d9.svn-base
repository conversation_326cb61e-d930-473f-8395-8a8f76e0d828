package com.yunqu.work.servlet.flow;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.WxMsgService;

@WebServlet("/servlet/flow/fun")
public class FlowFunServlet extends BaseFlowServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAddCCReadTime() {
		JSONObject params = getJSONObject();
		String ccId = params.getString("ccId");
		try {
			this.getQuery().executeUpdate("update yq_flow_cc set read_time = ? where cc_id = ? and read_time = ''", EasyDate.getCurrentDateString(),ccId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateField() {
		JSONObject params = getJSONObject();
		String flowCode = params.getString("flowCode");
		String businessId = params.getString("businessId");
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		String fieldName = params.getString("fieldName");
		String value = params.getString("value");
		try {
			if(fieldName.startsWith("apply")) {
				fieldName = fieldName.substring(6);
				this.getQuery().executeUpdate("update yq_flow_apply set "+fieldName+" = ? where apply_id = ?", value,businessId);
			}else if(fieldName.startsWith("business")) {
				fieldName = fieldName.substring(9);
				this.getQuery().executeUpdate("update "+flow.getTableName()+" set "+fieldName+" = ? where business_id = ?", value,businessId);
			}else if(fieldName.startsWith("extend")) {
				fieldName = fieldName.substring(7);
				String jsonStr = this.getQuery().queryForString("select data_json from yq_flow_apply where apply_id = ?", businessId);
				JSONObject extJson = new JSONObject();
				if(StringUtils.notBlank(jsonStr)) {
					extJson = JSONObject.parseObject(jsonStr);
				}
				extJson.put(fieldName, value);
				this.getQuery().executeUpdate("update yq_flow_apply set data_json = ? where apply_id = ?", extJson.toJSONString(),businessId);
			}
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForRecover() {
		EasyQuery query = this.getQuery();
		try {
			JSONObject params = getJSONObject();
			String resultId = params.getString("resultId");
			String businessId = params.getString("businessId");
			String nextResultId = params.getString("nextResultId");
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result==null) {
				return EasyResult.fail();
			}
			int checkResult  = result.getCheckResult();
			if(checkResult==0) {
				return EasyResult.fail();
			}
			query.begin();
			query.executeUpdate("update yq_flow_apply set next_result_id = ?,current_result_id = ? where apply_id = ?",resultId,result.getPrevResultId(),businessId);
			
			query.executeUpdate("delete from yq_flow_approve_result where result_id = ?", nextResultId);
			
			EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
			record.setPrimaryValues(resultId);
			record.set("result_id", resultId);
			record.set("prev_result_id",result.getPrevResultId());
			record.set("get_time",EasyDate.getCurrentDateString());
			record.set("read_time","");
			record.set("check_time","");
			record.set("check_desc","");
			record.set("check_result",0);
			query.update(record);
			
			query.commit();
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);
			FlowModel flow = ApproveService.getService().getFlow(apply.getFlowCode());
			MessageModel msgModel = new MessageModel();
			msgModel.setTypeName(apply.getFlowCode());
			msgModel.setSender(getUserId());
			msgModel.setReceiver(apply.getApplyBy());
			msgModel.setSendName(getUserName());
			msgModel.setFkId(businessId);
			msgModel.setMsgLabel("审批收回");
			msgModel.setTitle(apply.getApplyTitle());
			msgModel.setData1(flow.getFlowName());
			msgModel.setData2(apply.getApplyName());
			msgModel.setData3(apply.getApplyTime());
			msgModel.setData4(apply.getApplyRemark());
			msgModel.setDesc("审批已收回");
			
			this.sendAllMsg(msgModel);
			
		} catch (SQLException e) {
			try {
				query.roolback();
			} catch (SQLException ex) {
				this.error(ex.getMessage(), ex);
			}
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	

	public EasyResult actionForAddComment() {
		EasyRecord record = new EasyRecord("yq_flow_follow","follow_id");
		try {
			JSONObject params = getJSONObject();
			String pFollowId = params.getString("pFollowId");
			String toUserId = params.getString("toUserId");
			String flowContent = params.getString("flowContent");
			String businessId = params.getString("businessId");
			String id = RandomKit.uniqueStr();
			record.setPrimaryValues(id);
			record.set("business_id",businessId);
			record.set("p_follow_id",pFollowId);
			record.set("result_id",params.getString("resultId"));
			record.set("follow_content",flowContent);
			record.set("prev_follow_content",params.getString("prevFollowContent"));
			record.set("to_user_id",toUserId);
			record.set("to_user_name",params.getString("toUserName"));
			record.set("add_time", EasyDate.getCurrentDateString());
			record.set("add_user_id", getUserId());
			record.set("add_user_name", getUserName());
			this.getQuery().save(record);
			
			boolean hasMsg = false;
			MessageModel model = new MessageModel();
			
			if(StringUtils.notBlank(pFollowId)) {
				this.getQuery().executeUpdate("update yq_flow_follow t1, yq_flow_follow t2 set t1.to_user_id = t2.add_user_id,t1.to_user_name = t2.add_user_name,t1.prev_follow_content = t2.follow_content where t2.follow_id = ? and t1.follow_id = ?",pFollowId,id);
				String addUserId = params.getString("addUserId");
				hasMsg = true;
				FlowApplyModel apply = ApproveService.getService().getApply(businessId);
				model.setTitle(apply.getApplyTitle()+"#新的回复");
				model.setReceiver(addUserId);
			}else if(StringUtils.notBlank(toUserId)) {
				hasMsg = true;
				FlowApplyModel apply = ApproveService.getService().getApply(businessId);
				model.setTitle(apply.getApplyTitle()+"#新的评论");
				model.setReceiver(toUserId);
			}else {
				FlowApplyModel apply = ApproveService.getService().getApply(businessId);
				if(!getUserId().equalsIgnoreCase(apply.getApplyBy())) {
					hasMsg = true;
					model.setTitle(apply.getApplyTitle()+"#新的评论");
					model.setReceiver(apply.getApplyBy());
				}
			}
			if(hasMsg) {
				model.setSender(getUserId());
				model.setDesc(flowContent);
				WxMsgService.getService().sendCommentTaskMsg(model);
				model.setTplName("taskComment.html");
				EmailService.getService().sendTaskCommentEmail(model);
			}
			
			this.getQuery().executeUpdate("update yq_flow_apply set comment_count = comment_count + 1 where apply_id = ?",params.getString("businessId"));
			
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddReviseLog() {
		JSONObject params = getJSONObject();
		EasyResult result =  addReviseLog(params.getString("businessId"),params.getString("reviseContent"));
		try {
			if(result.isOk()) {
				this.getQuery().executeUpdate("update yq_flow_apply set revise_count = revise_count + 1 where apply_id = ?",params.getString("businessId"));
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return result;
	}
	
	
   public EasyResult actionForAddFllow() {
	   return EasyResult.ok();
   }


	@Override
	public EasyResult actionForSubmitApply() {
		return null;
	}


	@Override
	public EasyResult actionForUpdateApply() {
		return null;
	}
	

}
