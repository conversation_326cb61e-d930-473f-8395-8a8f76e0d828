<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
<title>员工年假总览</title>
</EasyTag:override>
<EasyTag:override name="content">
<form autocomplete="off"  onsubmit="return false;" class="form-inline" id="annualLeaveListForm">
  <div class="ibox">
    <div class="ibox-title clearfix">
      <div class="form-group">
        <h5>员工年假总览</h5>
        <div class="input-group input-group-sm">
          <span class="input-group-addon">员工号</span>
          <input type="text" name="staffNo" class="form-control input-sm" style="width: 90px;">
        </div>
        <div class="input-group input-group-sm">
          <span class="input-group-addon">员工名</span>
          <input type="text" name="staffName" class="form-control input-sm" style="width: 90px;">
        </div>
        <div class="input-group input-group-sm">
          <button type="button" class="btn btn-sm btn-default" onclick="annualLeaveList.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
        </div>
      </div>
    </div>
    <div class="ibox-content">
      <table class="layui-hide" id="annualLeaveList"></table>
    </div>
  </div>

</form>

<script  type="text/html" id="annualLeaveListBar">
  <button class="btn btn-info btn-sm" onclick="annualLeaveList.query()" type="button">刷新</button>
</script>



</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
  var springFestival = 3;
  $(function(){
    $('#annualLeaveListForm').render({success:function(){
        annualLeaveList.init();
      }});
  });

  var annualLeaveList={
    init:function(){
      $('#annualLeaveListForm').initTable({
                mars:'LeaveDao.annualLeaveList',
                id:"annualLeaveList",
                toolbar:"#annualLeaveListBar",
                pages:true,
                limits:[20,50,100,300,500,1000,1500,2000],
                cols: [[
                  {
                    type: 'numbers',
                    title: '序号',
                    align:'left'
                  },{
                    field: 'STAFF_NO',
                    title: '员工号',
                    align:'left',
                    sort: true
                  },{
                    field: 'USERNAME',
                    title: '姓名',
                    align:'left',
                    sort: true
                  },{
                    field: 'JOIN_DATE',
                    title: '入职日期',
                    align:'left',
                    sort:true
                  },{
                    title: '截止日期',
                    align:'left',
                    templet:function (){
                      return getLastYearEndDateString();
                    }
                  },{
                    title: '入职天数',
                    align:'left',
                    templet:function (d){
                      var start = new Date(d.JOIN_DATE);
                      var end = getLastYearEndDate();
                      if(start > end){
                        return '2023未入职';
                      }
                      var days =  Math.abs(end - start) / (1000 * 60 * 60 * 24) + 1;
                      return days;
                    }
                  },{
                    title: '2024年休假(天)',
                    align:'left',
                    templet:function (d){
                      if(!d.JOIN_DATE) return "入职时间为空！";
                      var start = new Date(d.JOIN_DATE);
                      return calculate(start,-1);
                    }
                  },{
                    title: '2024年春节安排（天)',
                    align:'left',
                    templet:function (){
                      return springFestival;
                    }
                  }
                  ,{
                    title:'除春节年假',
                    templet:function (d){
                      if(!d.JOIN_DATE) return "入职时间为空！";
                      var start = new Date(d.JOIN_DATE);
                      return calculate(start,0);
                    }
                  },{
                    field: 'TOTAL_DAYS',
                    title: '个人已申请年假(天)',
                    align:'left',
                    sort: true
                  },{
                    title:'最终剩余年假',
                    templet:function (d){
                      if(!d.JOIN_DATE) return "入职时间为空！";
                      var start = new Date(d.JOIN_DATE);
                      return calculate(start,d.TOTAL_DAYS);
                    }
                  }
                ]],
                done:function(){
                }
              }
      );
    },
    query:function (){
      $("#annualLeaveListForm").queryData();
    }
  }

  function getLastYearEndDateString(){
    var currentYear = new Date().getFullYear();
    var lastDayOfLastYear = new Date(currentYear - 1, 11, 31,8,0,0);
    var formattedDate = lastDayOfLastYear.toISOString().slice(0, 10);
    return  formattedDate;
  }
  function getLastYearEndDate(){
    var currentYear = new Date().getFullYear();
    var lastDayOfLastYear = new Date(currentYear - 1, 11, 31, 8, 0, 0);
    return  lastDayOfLastYear;
  }

  function calculate(start,springFlag){
    var end = getLastYearEndDate();
    var usedDays = 0;
    if(springFlag == 0){
      usedDays = springFestival;
    }
    if(start > end){
      return '无年假天数';
    }
    if(springFlag != -1 && springFlag != 0){
      var num = parseFloat(springFlag);
      usedDays = springFestival + num;
    }

    var days =  Math.abs(end - start) / (1000 * 60 * 60 * 24) + 1;
    var years = days/365;

    if(years < 1){
      return ( Math.floor(years * 5) - usedDays);
    } else if(years <= 9){
      return (7 - usedDays);
    } else if(years <= 19){
      return (10 - usedDays);
    } else if(years >=20){
      return (15 - usedDays);
    }
  }

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>