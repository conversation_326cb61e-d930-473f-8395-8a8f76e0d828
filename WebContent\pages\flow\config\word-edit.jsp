<%@ page language="java" contentType="text/html;charset=UTF-8"%> 
<%@ include file="/pages/common/global.jsp" %> 
<%@ page language="java" contentType="text/html;charset=UTF-8"%> 
<EasyTag:override name="head"> 
    <title>配置</title> 
</EasyTag:override> 
<EasyTag:override name="content"> 
        <form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm"> 
        	<input value="${param.flowCode}" type="hidden" id="flowCode" name="flowCode"/>
            <div class="ibox"> 
                 <div class="ibox-content"> 
                    <table id="wordTable"></table> 
                    <button type="button" class="btn btn-sm btn-info" onclick="addWord()"> 新增</button> 
                 </div> 
            </div> 
        </form> 
        <script type="text/html" id="bar">
  			<a class="layui-btn layui-btn-warn layui-btn-xs" lay-event="delWord">删除</a>
		</script>
</EasyTag:override> 
<EasyTag:override name="script"> 
    <script type="text/javascript"> 
      
            $(function(){ 
                $("#dataMgrForm").render({success:function(){ 
                    initList(); 
                }}); 
            }); 
              
            function initList(){ 
                $("#dataMgrForm").initTable({ 
                    mars:'FlowCommon.approveWord', 
                    id:'wordTable', 
                    page:false, 
                    edit:'editObj',
                    cellMinWidth:100, 
                    cols: [[ 
                     { 
                         field:'WORD_CONTENT', 
                         title:'备注',
                         edit:true
                     },{ 
                        field: 'CREATE_TIME', 
                        title: '操作时间', 
                        width:140,
                        align:'center' 
                    },{ 
                        field:'ORDER_INDEX', 
                        width:70,
                        title:'节点次序',
                        edit:true
                    },{ 
                        field:'', 
                        title:'操作', 
                        width:80,
                        toolbar: '#bar'
                    } 
                ]],done:function(){ 
                      
                } 
             }); 
           } 
            
            function reloadWord(){
            	 $("#dataMgrForm").queryData({page:false});
            }
            
        	function editObj(obj){
    			var data=obj.data;
   				ajax.remoteCall("${ctxPath}/web/flow/config/updateWord",data,function(result) { 
   					if(result.state != 1){
   						layer.alert(result.msg,{icon: 7});
   					}else{
   						layer.msg(result.msg);
   					}
   				});
    		}
        	
            function addWord(){ 
            	 layer.prompt({formType: 2,value: '',offset:'20px',title: '请输入',area: ['400px', '150px']}, function(wordContent, index, elem){
 		    	 	layer.close(index);
 		    	  	ajax.remoteCall("${ctxPath}/web/flow/config/addWord",{wordContent:wordContent},function(result) { 
 						if(result.state == 1){
 							layer.msg(result.msg,{icon:1,time:800},function(){
 								reloadWord();
 							})
 						}else{
 							layer.alert(result.msg,{icon: 5});
 						}
 					});	    	  	
 		    	});
            } 
            
            function delWord(data){
            	layer.confirm('确定是否删除?',{icon:3,icon:'50px'},function(){
            		ajax.remoteCall("${ctxPath}/web/flow/config/delWord",{id:data['ID']},function(result) { 
        				if(result.state == 1){
        					layer.msg(result.msg,{icon:1,time:1200},function(){
        						reloadWord();
        					});
        				}else{
        					layer.alert(result.msg,{icon: 5});
        				}
        			});
            	});
            }
              
              
    </script> 
</EasyTag:override> 
<%@ include file="/pages/common/layout_div.jsp" %> 