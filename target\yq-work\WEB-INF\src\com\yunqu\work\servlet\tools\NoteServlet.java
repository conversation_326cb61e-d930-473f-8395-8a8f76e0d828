package com.yunqu.work.servlet.tools;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.NoteModel;

@WebServlet("/servlet/note")
public class NoteServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForAdd(){
		NoteModel model=getModel(NoteModel.class, "note");
		model.setPrimaryValues(RandomKit.uniqueStr());
		model.set("update_time", EasyDate.getCurrentDateString());
		model.setCreator(getUserPrincipal().getUserId());
		try {
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		NoteModel model=getModel(NoteModel.class, "note");
		try {
			model.set("update_time", EasyDate.getCurrentDateString());
			model.update();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	public EasyResult actionForDelete(){
		NoteModel model=getModel(NoteModel.class, "note");
		try {
			model.delete();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail(e.getMessage());
		}
	   return EasyResult.ok();
	}
}
