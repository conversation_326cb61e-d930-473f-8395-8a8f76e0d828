package com.yunqu.work.ext.handler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.jfinal.handler.Handler;
import com.yunqu.work.utils.LogUtils;
/**
 * 过滤请求
 *
 */
public class UrlSkipHandler extends Handler {
	
	
	public void handle(String target, HttpServletRequest request, HttpServletResponse response, boolean[] isHandled) {
		if(StringUtils.notBlank(target)&&!target.startsWith("/static")) {
			LogUtils.getAccess().info(WebKit.getIP(request)+">>"+request.getRemoteUser()+">>"+target+","+request.getQueryString());
		}
		if (target.startsWith("/static")||target.endsWith(".css")||target.endsWith(".js")||target.startsWith("/websocket")||target.startsWith("/servlet")||target.startsWith("/webcall")||target.startsWith("/fileview")||target.startsWith("websocket")) {
			return;
		} else {
			next.handle(target, request, response, isHandled);
		}
	}
}
 