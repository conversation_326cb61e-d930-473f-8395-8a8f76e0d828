package com.yunqu.work.dao.contract;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;

@WebObject(name = "ReviewDao")
public class ReviewDao extends AppDaoContext {

	@WebControl(name="getReviewNo",type=Types.TEXT)
	public JSONObject getReviewNo(){
		int dateId = EasyCalendar.newInstance().getDateInt();
		try {
			String val = this.getQuery().queryForString("select max(review_no) from yq_contract_review where review_no like 'P-"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return getJsonResult("P-"+dateId+"1001");
			}else {
				val = val.replace("P-", "");
				return getJsonResult("P-"+String.valueOf(Long.valueOf(val)+1));
			}
		} catch (SQLException e) {
			return getJsonResult("");
		}
	}
	
	@WebControl(name = "info",type = Types.RECORD)
	public JSONObject info() {
		String reviewId = param.getString("reviewId");
		if(StringUtils.isBlank(reviewId)) {
			return getJsonResult(new JSONObject());
		}
		return queryForRecord("select * from yq_contract_review where review_id = ?",reviewId);
	}
	@WebControl(name = "nodeInfo",type = Types.RECORD)
	public JSONObject nodeInfo() {
		String nodeId = param.getString("conf.NODE_ID");
		if(StringUtils.isBlank(nodeId)) {
			return getJsonResult(new JSONObject());
		}
		return queryForRecord("select * from yq_contract_review_node_conf where node_id = ?",nodeId);
	}
	
	@WebControl(name = "followList",type = Types.PAGE)
	public JSONObject followList() {
		EasySQL sql = getEasySQL("select t1.* from yq_contract_review_follow t1  where 1=1");
		sql.append(param.getString("reviewId"),"and t1.review_id = ?",false);
		sql.append("order by t1.add_time desc");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "list",type = Types.PAGE)
	public JSONObject list() {
		EasySQL sql = getEasySQL("select * from yq_contract_review where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?");
		sql.append(param.getString("signDeptId"),"and sign_dept_id = ?");
		sql.append(param.getString("prodLine"),"and prod_line = ?");
		sql.append(param.getString("signEnt"),"and sign_ent = ?");
		sql.appendLike(param.getString("contractName"),"and CONTRACT_NAME like ?");
		sql.append(param.getString("reviewNo"),"and REVIEW_NO = ?");
		sql.append(param.getString("reviewState"),"and review_state = ?");
		if(!isSuperUser()) {
			sql.append(getUserId(),"and create_user_id = ?");
		}
		sql.append("order by create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "custReview",type = Types.LIST)
	public JSONObject custReview() {
		EasySQL sql = getEasySQL("select * from yq_contract_review where 1=1");
		sql.append(param.getString("custId"),"and cust_id = ?",false);	
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "reviewNodeResult",type = Types.LIST)
	public JSONObject reviewNodeResult() {
		EasySQL sql = getEasySQL("select t1.*,t2.node_name from yq_contract_review_result t1,yq_contract_review_node_conf t2 where t1.node_id = t2.node_id ");
		sql.append(param.getString("reviewId"),"and t1.review_id = ?",false);
		sql.append("ORDER BY t1.node_id,t1.node_type,t1.do_time");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "reviewDetailMgr",type = Types.PAGE)
	public JSONObject reviewDetailMgr() {
		EasySQL sql = getEasySQL("select t1.*,t3.node_name,t2.result_id,t2.node_id,t2.node_type,t2.to_user_name,t2.to_user_id,t2.do_user_name,t2.get_time,t2.do_time,t2.comment,t2.do_result from yq_contract_review t1");
		sql.append("INNER JOIN yq_contract_review_result t2 on t1.review_id = t2.review_id");
		sql.append("LEFT JOIN yq_contract_review_node_conf t3 on t3.node_id = t2.node_id");
		sql.append("where 1=1 and t2.node_state = 0 and t1.review_state > 0");
		sql.append(param.getString("reviewState"),"and t1.review_state = ?");	
		sql.appendLike(param.getString("contractName"),"and t1.contract_name like ?");	
		sql.appendLike(param.getString("reviewNo"),"and t1.review_no like ?");	
		if(!isSuperUser()) {
			sql.append("and ((");
			sql.append(getUserId(),  "FIND_IN_SET(?,t2.to_user_id)");
			sql.append(")");
			sql.append("or (");
			sql.append(getUserId(),  "FIND_IN_SET(?,t1.read_user_ids)");
			sql.append("))");
		}
		sql.append("ORDER BY t1.create_time desc,t2.node_id,t2.node_type");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeConfList",type = Types.TEMPLATE)
	public JSONObject nodeConfList() {
		EasySQL sql = getEasySQL("select * from yq_contract_review_node_conf");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name = "reviewNodeList",type = Types.OTHER)
	public JSONObject reviewNodeList() {
		EasySQL sql = getEasySQL("select GROUP_CONCAT(node_id) nodeIds from yq_contract_review_result where 1=1 and node_state = 0");
		sql.append(param.getString("reviewId"),"and review_id = ?",false);
		try {
			String ids = this.getQuery().queryForString(sql.getSQL(), sql.getParams());
			return getText(ids);
		} catch (SQLException e) {
			return getText("");
		}
		
	}
}
