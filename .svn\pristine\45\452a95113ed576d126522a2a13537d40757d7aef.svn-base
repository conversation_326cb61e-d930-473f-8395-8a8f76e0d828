<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<div id="fileList">
    <table class="layui-table" lay-size="sm">
	  <thead>
	    <tr>
	      <th>文件名</th>
	      <th>关联物料</th>
	      <th>文件大小</th>
	      <th>上传时间</th>
	      <th>上传人</th>
	      <th>下载次数</th>
	      <th>操作</th>
	    </tr> 
	  </thead>
	  <tbody data-template="order-detail-template-files" data-mars="GoodsDao.fileListDetail">

	  </tbody>
	</table>
</div>
<button class="btn btn-sm btn-info mt-5 uploadBtn" type="button" onclick="$('#orderDetailLocalfile').click()">+上传</button>
						    	
<script id="order-detail-template-files" type="text/x-jsrender">
	{{call:data fn = 'setCount'}}
	{{if data.length==0}}
		<tr>
			<td colspan="7">暂无数据</td>
		</tr>
	{{/if}}
	{{for data}}
		<tr id="tr_{{:FILE_ID}}" data-id="{{:FILE_ID}}">
		  <td>{{call:FILE_TYPE fn='getFileIcon'}}<a href="/yq-work/fileview/{{:FILE_ID}}?view=online&filename={{:FILE_NAME}}" title="点击查看" target="_blank"><span style="color:#444">{{cutText:FILE_NAME 40}} &nbsp;</span></a></td>
		  <td>{{:GOODS_NAME}}</td>
		  <td>{{:FILE_SIZE}}</td>
		  <td>{{:CREATE_TIME}}</td>
		  <td>
			  {{:CREATE_NAME}}
		  </td>
		  <td>
			  {{:DOWNLOAD_COUNT}}
		  </td>
		  <td>
			  <a href="/yq-work/fileview/{{:FILE_ID}}?filename={{:FILE_NAME}}" target="_blank"><span style="color:#17a6f0">下载<span></a>
			  {{call:CREATOR FILE_ID fn='getDelFileElement'}}		  
		  </td>
		</tr>
   {{/for}}
</script>
<script>
	var orderUploadFile = function(){
		var pFkId = orderId;
		var fkId =  $("#goodsId").val()||pFkId;
		easyUploadFile({callback:'orderDetailCallback',fkId:fkId,pFkId:pFkId,source:'order',formId:'orderDetailFileForm',fileId:'orderDetailLocalfile'});
		
	}
	function setCount(data){
		$("#fileCount").text(data.length);
	}
	
	var orderDetailCallback = function(data){
		$("#fileList").render({data:{pFkId:orderId}});
		ajax.remoteCall("/yq-work/servlet/order?action=updateGoodsFileCount",{orderId:orderId},function(result) { 
			if(result.state == 1){
				layer.closeAll();
				reloadDataList();
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		},{loading:false});
	}

</script>