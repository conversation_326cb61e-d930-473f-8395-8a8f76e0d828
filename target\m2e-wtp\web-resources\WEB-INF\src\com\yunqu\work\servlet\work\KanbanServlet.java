package com.yunqu.work.servlet.work;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
@WebServlet("/servlet/kanban")
public class KanbanServlet extends AppBaseServlet {
	private static final long serialVersionUID = -9064225788599799486L;
	
	public EasyResult actionForDelKanban(){
		String kanbanId=getJsonPara("kanbanId");
		String sql = "delete from yq_kanban_panel where kanban_id = ?";
		try {
			this.getQuery().executeUpdate(sql, kanbanId);
			sql = "delete from yq_kanban_panel_item where kanban_id = ?";
			this.getQuery().executeUpdate(sql, kanbanId);
			sql = "delete from yq_kanban where kanban_id = ?";
			this.getQuery().executeUpdate(sql, kanbanId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForUpdate(){
		EasyRecord record=new EasyRecord("yq_kanban","kanban_id");
		record.setColumns(getJSONObject("kanban"));
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public EasyResult actionForAdd(){
		String id = RandomKit.uuid();
		EasyRecord record=new EasyRecord("yq_kanban","kanban_id");
		record.setColumns(getJSONObject("kanban"));
		record.setPrimaryValues(id);
		record.set("creator",getUserId());
		record.set("create_time", EasyDate.getCurrentDateString());
		try {
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		try {
			EasyRecord panel=new EasyRecord("yq_kanban_panel","panel_id");
			panel.set("KANBAN_ID", id);
			panel.set("CREATOR", getUserId());
			panel.set("CREATE_TIME",EasyDate.getCurrentDateString());

			String projectId=record.getString("PROJECT_ID");
			if("0".equals(projectId)){
				panel.set("PANEL_NAME","今天要做的");
				panel.set("panel_theme","panel-primary");
				panel.set("PANEL_ID",RandomKit.uuid());
				panel.set("ORDER_INDEX", 1);
				this.getQuery().save(panel);
				
				panel.set("PANEL_NAME","明天要做的");
				panel.set("panel_theme","panel-info");
				panel.set("PANEL_ID",RandomKit.uuid());
				panel.set("ORDER_INDEX", 2);
				this.getQuery().save(panel);
				
				panel.set("PANEL_NAME","本周要做的");
				panel.set("panel_theme","panel-success");
				panel.set("PANEL_ID",RandomKit.uuid());
				panel.set("ORDER_INDEX", 3);
				this.getQuery().save(panel);
			}else{
				panel.set("PANEL_NAME","未开始");
				panel.set("panel_theme","");
				panel.set("PANEL_ID",RandomKit.uuid());
				panel.set("ORDER_INDEX", 1);
				this.getQuery().save(panel);
				
				panel.set("PANEL_NAME","进行中");
				panel.set("panel_theme","panel-info");
				panel.set("PANEL_ID",RandomKit.uuid());
				panel.set("ORDER_INDEX", 2);
				this.getQuery().save(panel);
				
				panel.set("PANEL_NAME","已暂停");
				panel.set("panel_theme","panel-warning");
				panel.set("PANEL_ID",RandomKit.uuid());
				panel.set("ORDER_INDEX", 3);
				this.getQuery().save(panel);
				
				panel.set("PANEL_NAME","已完成");
				panel.set("panel_theme","panel-success");
				panel.set("PANEL_ID",RandomKit.uuid());
				panel.set("ORDER_INDEX", 4);
				this.getQuery().save(panel);
				
				/*panel.set("PANEL_NAME","已取消");
				panel.set("panel_theme","panel-danger");
				panel.set("PANEL_ID",RandomKit.uuid());
				panel.set("ORDER_INDEX", 5);
				this.getQuery().save(panel);
				
				panel.set("PANEL_NAME","已关闭");
				panel.set("panel_theme","panel-success");
				panel.set("PANEL_ID",RandomKit.uuid());
				panel.set("ORDER_INDEX", 6);
				this.getQuery().save(panel);*/
				
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok(id,"新增成功.");
	}
	
	public JSONObject actionForAddPanel(){
		EasyRecord record=new EasyRecord("yq_kanban_panel","panel_id");
		record.setColumns(getJSONObject("panel"));
		record.setPrimaryValues(RandomKit.uniqueStr());
		record.set("creator",getUserId());
		record.set("create_time", EasyDate.getCurrentDateString());
		try {
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public JSONObject actionForDelPanel(){
		String panelId = getJsonPara("panelId");
		String sql="delete from yq_kanban_panel_item where panel_id  = ?";
		try {
			this.getQuery().executeUpdate(sql,panelId);
			sql="delete from yq_kanban_panel where panel_id = ?";
			this.getQuery().executeUpdate(sql,panelId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public JSONObject actionForDelPanelItem(){
		String itemId = getJsonPara("itemId");
		String sql="delete from yq_kanban_panel_item where item_id  = ?";
		try {
			this.getQuery().executeUpdate(sql,itemId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public JSONObject actionForUpdatePanel(){
		EasyRecord record=new EasyRecord("yq_kanban_panel","panel_id");
		record.setColumns(getJSONObject("panel"));
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public JSONObject actionForAddPanelItem(){
		EasyRecord record=new EasyRecord("yq_kanban_panel_item","item_id");
		record.setColumns(getJSONObject("item"));
		record.setPrimaryValues(RandomKit.uuid());
		record.set("creator",getUserId());
		record.set("create_time", EasyDate.getCurrentDateString());
		try {
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public JSONObject actionForUpdatePanelItem(){
		EasyRecord record=new EasyRecord("yq_kanban_panel_item","item_id");
		record.setColumns(getJSONObject("item"));
		try {
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	public JSONObject actionForUpdateItemPostion(){
		JSONArray array = getJSONArray();
		JSONObject source = array.getJSONObject(0);
		JSONObject target = array.getJSONObject(1);
		try {
			this.getQuery().executeUpdate("update yq_kanban_panel_item set panel_id = ? where item_id = ?",target.getString("panelId"),source.getString("itemId"));
			if(StringUtils.isNotBlank(target.getString("itemId"))){
				this.getQuery().executeUpdate("update yq_kanban_panel_item set panel_id = ? where item_id = ?",source.getString("panelId"),target.getString("itemId"));
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
}





