package com.yunqu.work.dao.other;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.model.ProjectWeeklyModel;
import com.yunqu.work.model.WeeklyModel;
import com.yunqu.work.service.LookLogService;
import com.yunqu.work.utils.WeekUtils;

@WebObject(name="WeeklyDao")
public class WeeklyDao extends AppDaoContext {
	
	private static final String default_tpl_id="default_tpl";
	
	@WebControl(name = "getCurrentWeekBeginDate",type = Types.TEXT)
	public JSONObject getCurrentWeekBeginDate() {
		return getJsonResult(WeekUtils.getStartDayOfWeekNo(WeekUtils.getYear(), WeekUtils.getWeekNo()));
	}
	
	@WebControl(name = "getCurrentWeekEndDate",type = Types.TEXT)
	public JSONObject getCurrentWeekEndDate() {
		return getJsonResult(WeekUtils.getEndDayOfWeekNo(WeekUtils.getYear(), WeekUtils.getWeekNo()));
	}
	
	@WebControl(name = "taskDesc",type = Types.TEXT)
	public JSONObject taskDesc() {
		StringBuffer sb = new StringBuffer();
		EasySQL easySQL=getEasySQL("select ");
		easySQL.append(10,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS a,");
		easySQL.append(20,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS b,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state < ? THEN 1 ELSE 0 END) AS g,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS h,");
		easySQL.append(30,"SUM(CASE WHEN t1.task_state = ? THEN 1 ELSE 0 END) AS c,");
		easySQL.append(40,"SUM(CASE WHEN t1.task_state >= ? THEN 1 ELSE 0 END) AS d,");
		easySQL.append(EasyDate.getCurrentDateString(),"SUM(CASE WHEN t1.deadline_at < ? and t1.task_state in(10,20)  THEN 1 ELSE 0 END) AS e");
		easySQL.append("from YQ_TASK t1 where 1=1");
		easySQL.append(getUserId(),"and t1.assign_user_id = ?");
		
		JSONObject result = queryForRecord(easySQL.getSQL(), easySQL.getParams());
		JSONObject data = result.getJSONObject("data");
		
		int a = data.getIntValue("A");
		int b = data.getIntValue("B");
		int c = data.getIntValue("E");
		if(a==0&&b==0&&c==9) {
			return getJsonResult("");
		}
		sb.append("本周任务汇总").append("：");
		if(a>0) {
			sb.append("待办("+a).append(")，");
		}
		if(b>0) {
			sb.append("进行中("+b).append(")，");
		}
		if(c>0) {
			sb.append("逾期数("+c).append(")。");
		}
		return getJsonResult(sb.toString());
	}
	
	
	
	@WebControl(name = "weeklyDown",type = Types.LIST)
	public JSONObject weeklyDown() {
		EasySQL sql = new EasySQL("select t2.username create_name,count(1) count from yq_weekly t1 right join "+Constants.DS_MAIN_NAME+".easi_user t2 on  t1.creator = t2.user_id where t2.state = 0");
		if(!isSuperUser()) {
			sql.append(getDeptId(),"and t1.dept_id = ?");
		}
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and t1.week_begin >= ?");
			sql.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		sql.append("GROUP BY t1.creator order by count limit 80");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name = "nowYearTop",type = Types.LIST)
	public JSONObject nowYearTop() {
		EasySQL sql = new EasySQL("select t2.username create_name,count(1) count from yq_weekly t1 right join "+Constants.DS_MAIN_NAME+".easi_user t2 on  t1.creator = t2.user_id where t2.state = 0");
		if(!isSuperUser()) {
			sql.append(getDeptId(),"and t1.dept_id = ?");
		}
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and t1.week_begin >= ?");
			sql.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		sql.append("GROUP BY t1.creator order by count desc limit 80");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "weeklyNumTop",type = Types.LIST)
	public JSONObject weeklyNumTop() {
		EasySQL sql = new EasySQL("select t1.year,t1.week_no,t2.week_begin,t2.week_end,count(1) count from yq_weekly t1,yq_weekly_date t2 where t1.year= t2.year and t1.week_no = t2.week_no");
		if(!isSuperUser()) {
			sql.append(getDeptId(),"and t1.dept_id = ?");
		}
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			sql.append(updateDateArray[0],"and t1.week_begin >= ?");
			sql.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		sql.append("GROUP BY t1.week_no order by t1.`year` desc,t1.week_no desc limit 20");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name="getProjectRecentWeek",type=Types.TEMPLATE)
	public JSONObject getProjectRecentWeek(){
		JSONArray recentWeek=WeekUtils.getRecentWeek(WeekUtils.getYear(),52," 周报");
		return getJsonResult(recentWeek);
	}
	
	@WebControl(name="getProjectRecentWeekPage",type=Types.TEMPLATE)
	public JSONObject getProjectRecentWeekPage(){
		JSONArray recentWeek=WeekUtils.getRecentWeek(WeekUtils.getYear(),52," 周报");
		JSONObject emptyPage = emptyPage();
		emptyPage.put("data",recentWeek);
		emptyPage.put("totalRow",recentWeek.size());
		return emptyPage;
	}
	
	@WebControl(name="weeklyStat",type=Types.LIST)
	public JSONObject weeklyStat(){
		EasySQL sql=getEasySQL("select t2.DEPT_NAME,t1.* from yq_weekly_stat t1 INNER JOIN  "+Constants.DS_MAIN_NAME+".easi_dept t2 on t1.dept_id=t2.DEPT_ID where 1=1");
		sql.append(param.getString("deptId"),"and t2.dept_id = ?");
		sql.append(param.getString("year"),"and t1.year = ?");
		sql.append(param.getString("weeklyNo"),"and t1.weekly_no = ?");
		sql.append("order by year desc,weekly_no desc,t1.weekly_count desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="projectRecord",type=Types.RECORD)
	public JSONObject projectRecord(){
		ProjectWeeklyModel model=new ProjectWeeklyModel();
		model.setColumns(getParam("weekly"));
		JSONObject result=queryForRecord(model);
		if(StringUtils.notBlank(model.getWeeklyId())){
			String receiver=result.getJSONObject("data").getString("RECEIVER");
			String creator=result.getJSONObject("data").getString("CREATOR");
			if(!getUserId().equals(creator)){
				LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getWeeklyId());
			}
			if(getUserId().equals(receiver)){
				try {
					//设置为已阅读
					this.getQuery().executeUpdate("update yq_project_weekly set status = ? where weekly_id =? ", 2,model.getWeeklyId());
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}
		
		if(StringUtils.notBlank(model.getWeeklyId())){
			String sql="select USER_ID from  yq_cc where FK_ID = ?";
			try {
				String v1 = this.getQuery().queryForString("select GROUP_CONCAT(t1.USERNAME) from "+Constants.DS_MAIN_NAME+".easi_user t1 where t1.user_id in(select t2.USER_ID from yq_cc t2 where t2.FK_ID = ?)", model.getPrimaryValue());
				result.getJSONObject("data").put("mailtoName", v1);
				
				result.getJSONObject("data").put("projectName", this.getQuery().queryForString("select t1.project_name from yq_project t1,yq_project_weekly t2 where t1.project_id = t2.project_id and t2.weekly_id= ?", model.getPrimaryValue()));
				List<EasyRow> list = this.getQuery().queryForList(sql, model.getPrimaryValue());
				if(list!=null&&list.size()>0){
					String[] mailtos=new String[list.size()];
					for(int i=0;i<list.size();i++){
						mailtos[i]=list.get(i).getColumnValue(1);
					}
					result.put("mailtos",mailtos);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
		return result;
	}
	@WebControl(name="record",type=Types.RECORD)
	public JSONObject record(){
		WeeklyModel model=new WeeklyModel();
		model.setColumns(getParam("weekly"));
		String tplId=default_tpl_id;
		JSONObject result=queryForRecord(model);
		if(StringUtils.notBlank(model.getWeeklyId())){
			String receiver=result.getJSONObject("data").getString("RECEIVER");
			String creator=result.getJSONObject("data").getString("CREATOR");
		     tplId=result.getJSONObject("data").getString("TPL_ID");
			if(!getUserId().equals(creator)){
				LookLogService.getService().addLog(getUserPrincipal().getUserId(),model.getWeeklyId());
			}
			if(getUserId().equals(receiver)){
				try {
					//设置为已阅读
					this.getQuery().executeUpdate("update yq_weekly set status = ? where weekly_id =? ", 2,model.getWeeklyId());
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}
		
		try {
		 JSONObject tplRow=this.getQuery().queryForRow("select * from yq_weekly_tpl where tpl_id = ?",new Object[]{tplId} ,new JSONMapperImpl());
		 result.put("tplRow", tplRow);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		/*try {
			String weeklyType=result.getJSONObject("data").getString("WEEKLY_TYPE");
			if("1".equals(weeklyType)){
				List<JSONObject> projectWeekly=this.getQuery().queryForList("select * from yq_project_weekly where weekly_id = ? and creator = ?",new Object[]{model.getWeeklyId(),getUserId()} ,new JSONMapperImpl());
				result.put("projectWeekly", projectWeekly);
			}
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}*/
		if(StringUtils.notBlank(model.getWeeklyId())){
			String sql="select USER_ID from  yq_cc where FK_ID = ?";
			List<EasyRow> list;
			try {
				list = this.getQuery().queryForList(sql, model.getPrimaryValue());
				if(list!=null&&list.size()>0){
					String[] mailtos=new String[list.size()];
					for(int i=0;i<list.size();i++){
						mailtos[i]=list.get(i).getColumnValue(1);
					}
					result.put("mailtos",mailtos);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
			
		}
		return result;
	}
	
	@WebControl(name="getRecentWeek15",type=Types.TEMPLATE)
	public JSONObject getRecentWeek15(){
		JSONArray getRecentWeek=WeekUtils.getRecentWeek(WeekUtils.getYear());
		return getJsonResult(getRecentWeek);
	}
	@WebControl(name="getRecentWeek",type=Types.TEMPLATE)
	public JSONObject getRecentWeek(){
		JSONArray getRecentWeek=WeekUtils.getRecentWeek(param.getIntValue("year"),6," 周报");
		return getJsonResult(getRecentWeek);
	}
	
	@WebControl(name="weeklyFeedback",type=Types.LIST)
	public JSONObject weeklyFeedback(){
		EasySQL easySQL=getEasySQL("select t1.* from YQ_WEEKLY t1 where 1=1");
		easySQL.append("and LENGTH(item_3)>15");
		easySQL.append("order by year desc,week_no desc");
		JSONObject result=queryForPageList(easySQL.getSQL(), easySQL.getParams());
		return result;
	}
	
	@WebControl(name="myProjectWeeklys",type=Types.PAGE)
	public JSONObject myProjectWeeklys(){
		EasySQL easySQL=getEasySQL("select  t.* from ");
		easySQL.append("(select t2.project_name,t2.project_no,t4.PIC_URL,t4.USERNAME,t4.DEPTS,t1.*,1 as TYPE from yq_project_weekly t1, yq_project t2,"+Constants.DS_MAIN_NAME+".easi_user t4 where 1=1");
		easySQL.append("and t2.project_id = t1.project_id");
		easySQL.append("and t4.USER_ID=t1.CREATOR");
		if(!this.getUserPrincipal().isRole("WEEKLY_MANGER")){
			easySQL.append(getUserId(),"and t1.RECEIVER = ?");
		}
		String status = param.getString("status");
		if(StringUtils.isBlank(status)) {
			easySQL.append(0,"and t1.status <> ?");
		}else {
			easySQL.append(status,"and t1.status = ?");
		}
		
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			easySQL.append(updateDateArray[0],"and t1.week_begin >= ?");
			easySQL.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		
		easySQL.appendLike(param.getString("projectName"),"and t2.PROJECT_NAME like ?");
		easySQL.append(param.getString("weekNo"),"and t1.WEEK_NO = ?");
		easySQL.append(param.getString("year"),"and t1.YEAR = ?");
		easySQL.appendLike(param.getString("userName"),"and t4.USERNAME like ?");
		easySQL.appendLike(param.getString("depts"),"and t4.DEPTS like ?");
		
		if(!this.getUserPrincipal().isRole("WEEKLY_MANGER")){
			easySQL.append("union select t4.project_name,t4.project_no,t5.PIC_URL,t5.USERNAME,t5.DEPTS,t3.*,2 as TYPE from yq_project_weekly t3,yq_cc t2, yq_project t4,"+Constants.DS_MAIN_NAME+".easi_user t5 where t3.weekly_id=t2.fk_id ");
			easySQL.append("and t5.USER_ID=t2.USER_ID");
			easySQL.append("and t3.project_id = t4.project_id");
			if(StringUtils.isBlank(status)) {
				easySQL.append(0,"and t3.status <> ?");
			}else {
				easySQL.append(status,"and t3.status = ?");
			}
			easySQL.append(getUserId(),"and t2.USER_ID = ?");
			easySQL.appendLike(param.getString("projectName"),"and t4.PROJECT_NAME like ?");
			
			if(StringUtils.notBlank(updateDate)) {
				String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
				easySQL.append(updateDateArray[0],"and t3.week_begin >= ?");
				easySQL.append(updateDateArray[1],"and t3.week_begin <= ?");
			}
		}
		easySQL.append(" ) t where 1=1 ");
		
		easySQL.append("order by t.send_time desc");
		JSONObject result=queryForPageList(easySQL.getSQL(), easySQL.getParams());
		return result;
	}
	
	
	@WebControl(name="myWeeklyList",type=Types.LIST)
	public JSONObject myWeeklyList(){
		EasySQL easySQL=getEasySQL("select t1.* from YQ_WEEKLY t1 where 1=1");
		easySQL.append(getUserPrincipal().getUserId(),"and t1.CREATOR = ?");
		easySQL.append("order by t1.year desc,t1.week_no desc");
		
		JSONObject result=queryForPageList(easySQL.getSQL(), easySQL.getParams());
		JSONArray array=result.getJSONArray("data");
		
		String pageIndex = request.getParameter("pageIndex");
		if("1".equals(pageIndex)) {
			JSONArray getRecentWeek=WeekUtils.getRecentWeek();
			int currentWeekNo=WeekUtils.getWeekNo();
			if(array!=null&&array.size()>0){
				for(int i=0;i<array.size();i++){
					JSONObject object=array.getJSONObject(i);
					object.put("currentWeekNo", currentWeekNo);
					int year=object.getIntValue("YEAR");
					int weekNo=object.getIntValue("WEEK_NO");
					String str=year+"_"+weekNo;
					removeItem(getRecentWeek,str,currentWeekNo);
				}
			}
			array.addAll(getRecentWeek);
			result.put("data", array);
		}
		return result;
	}
	private void removeItem(JSONArray array,String key,int currentWeekNo){
		for(int i=0;i<array.size();i++){
			JSONObject jsonObject=array.getJSONObject(i);
			String val=jsonObject.getString("YEAR_WEEK_NO");
			if(key.equals(val)){
				array.remove(i);
			}
		}
	}
	@WebControl(name="queryDeptWeekly",type=Types.TEMPLATE)
	public JSONObject queryDeptWeekly(){
		String deptId=param.getString("deptId");
		if(StringUtils.isBlank(deptId)){
			deptId=getDeptId();
		}
		if(StringUtils.isBlank(deptId)){
			return getJsonResult(null);
		}else{
			JSONObject result=queryForList("select t2.USER_ID,t2.USERNAME from "+Constants.DS_MAIN_NAME+".easi_dept_user t1 INNER JOIN "+Constants.DS_MAIN_NAME+".easi_user t2 on t2.USER_ID=t1.USER_ID where t1.DEPT_ID= ? and t2.STATE = 0",new Object[]{deptId});
			JSONArray data = result.getJSONArray("data");
			String[] userIds=new String[data.size()];
			for(int i=0;i<data.size();i++){
				JSONObject jsonObject=data.getJSONObject(i);
				userIds[i]=jsonObject.getString("USER_ID");
			}
			
			EasySQL sql=new EasySQL("select t1.* from yq_weekly t1 where 1=1");
			sql.append(param.getString("weekNo"),"and t1.WEEK_NO = ?",false);
			sql.append(param.getString("year"),"and t1.YEAR = ?",false);
			sql.appendIn(userIds, "and t1.CREATOR");
			try {
				result.put("weeklys", this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl()));
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
			return  result;
			
		}
	}
	@WebControl(name="myReceiveWeeklyList",type=Types.PAGE)
	public JSONObject myReceiveWeeklyList(){
		EasySQL easySQL=getEasySQL("select  t.* from ");
		easySQL.append("(select t4.PIC_URL,t4.USERNAME,t4.DEPTS,t1.*,1 as TYPE from YQ_WEEKLY t1,"+Constants.DS_MAIN_NAME+".easi_user t4 where 1=1");
		easySQL.append("and t4.USER_ID=t1.CREATOR");
		if(!this.getUserPrincipal().isRole("WEEKLY_MANGER")){
			easySQL.append(getUserId(),"and t1.RECEIVER = ?");
		}
		String status = param.getString("status");
		if(StringUtils.isBlank(status)) {
			easySQL.append(0,"and t1.status <> ?");
		}else {
			easySQL.append(status,"and t1.status = ?");
		}
		
		String updateDate = param.getString("updateDate");
		if(StringUtils.notBlank(updateDate)) {
			String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
			easySQL.append(updateDateArray[0],"and t1.week_begin >= ?");
			easySQL.append(updateDateArray[1],"and t1.week_begin <= ?");
		}
		
		easySQL.append(param.getString("weekNo"),"and t1.WEEK_NO = ?");
		easySQL.append(param.getString("year"),"and t1.YEAR = ?");
		easySQL.appendLike(param.getString("userName"),"and t4.USERNAME like ?");
		easySQL.appendLike(param.getString("depts"),"and t4.DEPTS like ?");
		if(!this.getUserPrincipal().isRole("WEEKLY_MANGER")){
			easySQL.append("union select t5.PIC_URL,t5.USERNAME,t5.DEPTS,t3.*,2 as TYPE from yq_weekly t3,yq_cc t2,"+Constants.DS_MAIN_NAME+".easi_user t5 where t3.weekly_id=t2.fk_id ");
			easySQL.append("and t5.USER_ID=t2.USER_ID");
			if(StringUtils.isBlank(status)) {
				easySQL.append(0,"and t3.status <> ?");
			}else {
				easySQL.append(status,"and t3.status = ?");
			}
			easySQL.append(getUserId(),"and t2.USER_ID = ?");
			
			if(StringUtils.notBlank(updateDate)) {
				String[] updateDateArray = updateDate.replaceAll("-", "").split(" 到 ");
				easySQL.append(updateDateArray[0],"and t3.week_begin >= ?");
				easySQL.append(updateDateArray[1],"and t3.week_begin <= ?");
			}
		}
		easySQL.append(" ) t where 1=1 ");
		easySQL.append("order by t.send_time desc");
		JSONObject result=queryForPageList(easySQL.getSQL(), easySQL.getParams());
		return result;
	}
}
