package com.yunqu.work.servlet.zr;

import java.math.BigDecimal;
import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.FlowService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.WxMsgService;
import com.yunqu.work.utils.NumberToCN;

@WebServlet("/servlet/zr/flow/bx/*")
public class BxServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public void actionForIndex() {
		
	}
	
	/**
	 * 执行审批
	 * @return
	 */
	public EasyResult actionForDoApprove() {
		try {
			JSONObject params = getJSONObject();
			String flowCode = params.getString("flowCode");
			String businessId = params.getString("businessId");
			String resultId = params.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("审批人不存在");
			}
			
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			
			
			//获取下一级节点
			ApproveNodeModel approveNode = ApproveService.getService().getNodeByResultId(resultId);
			String nodeId = approveNode.getNodeId();
			String nextNodeId =approveNode.getNextNodeId();
			
			
			FlowModel flow = ApproveService.getService().getFlow(flowCode);
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);

			int checkResult = params.getIntValue("checkResult");//1 通过 2拒绝
			String checkDesc = params.getString("checkDesc");
			EasyRecord record = new EasyRecord("yq_flow_approve_result","result_id");
			record.set("result_id", resultId);
			record.set("check_name", getUserName());
			record.set("business_id", businessId);
			record.set("check_user_id", getUserId());
			record.set("check_result",checkResult);
			record.set("check_desc", checkDesc);
			record.set("check_time", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
			
			
			MessageModel msgModel = new MessageModel();
			msgModel.setTypeName(flow.getFlowCode());
			msgModel.setSender(getUserId());
			msgModel.setSendName(getUserName());
			msgModel.setFkId(businessId);
			msgModel.setMsgLabel("待审批");
			msgModel.setTitle(apply.getApplyTitle());
			msgModel.setData1(flow.getFlowName());
			msgModel.setData2(apply.getApplyName());
			msgModel.setData3(apply.getApplyTime());
			msgModel.setData4("待审批");
			msgModel.setDesc("--");
			
			if(checkResult==2) {
				this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ?,last_result_time = ? where apply_id = ?",21,EasyDate.getCurrentDateString(),businessId);
				msgModel.setData4("审批退回");
				msgModel.setMsgLabel("审批退回");
				msgModel.setDesc(checkDesc);
				msgModel.setReceiver(apply.getApplyBy());
				WxMsgService.getService().sendFlowTodoCheck(msgModel);
				MessageService.getService().sendMsg(msgModel);
				EmailService.getService().sendEmail(msgModel);
				return EasyResult.ok();
			}
			
			
			String nextResultId  = "0";
			int reviewState = 10; // 10 待审批 20审批中 21 拒绝 30审批完成
			if("0".equals(nextNodeId)) {//完成了
				reviewState = 30;
				msgModel.setReceiver(apply.getApplyBy());
				FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
			}else {
				reviewState = 20;
				JSONObject nodeInfo = ApproveService.getService().getNodeInfo(nextNodeId,getUserId(),params);
				
				String todoCheckUserId = nodeInfo.getString("USER_ID");
				if(todoCheckUserId.indexOf(getUserId())>-1) {
					 nextNodeId = nodeInfo.getString("C_NODE_ID");
					 nodeInfo = ApproveService.getService().getNodeInfo(nextNodeId,getUserId(),params);
				}
				msgModel.setMsgLabel(nodeInfo.getString("NODE_NAME"));
				msgModel.setReceiver(nodeInfo.getString("USER_ID"));
				

				EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
				nextResultId = RandomKit.uniqueStr();
				resultRecord.set("result_id", nextResultId);
				resultRecord.set("business_id", businessId);
				resultRecord.set("check_name", nodeInfo.getString("USER_NAME"));
				resultRecord.set("check_user_id", nodeInfo.getString("USER_ID"));
				resultRecord.set("check_result", 0);
				resultRecord.set("node_id",nextNodeId);
				resultRecord.set("node_name",nodeInfo.getString("NODE_NAME"));
				resultRecord.set("check_desc","");
				resultRecord.set("get_time",EasyDate.getCurrentDateString());
				this.getQuery().save(resultRecord);
				
				FlowService.getService().updateApplyInfo(businessId, resultId,nextResultId,nodeId,nextNodeId, reviewState);
			}
			
			WxMsgService.getService().sendFlowTodoCheck(msgModel);
			MessageService.getService().sendMsg(msgModel);

		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdateApply() {
		JSONObject params = getJSONObject();
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		EasyRecord record = new EasyRecord(flow.getTableName(),"business_id");
		record.setColumns(JsonKit.getJSONObject(params, "business"));
		String businessId = params.getString("businessId");
		record.setPrimaryValues(businessId);
		
		String doAction = params.getString("doAction");
		EasyRecord applyRecord = getApplyRecord(flowCode);
		applyRecord.setPrimaryValues(businessId);
		applyRecord.set("apply_title",params.getString("applyTitle"));
		applyRecord.set("apply_remark",params.getString("applyRemark"));
		if("submit".equals(doAction)) {
			applyRecord.set("apply_state",10);
		}
		try {
			updateItem(params,businessId,true);
			this.getQuery().update(record);
			this.getQuery().update(applyRecord);
			
			if("submit".equals(doAction)) {
				this.startFlow(flow,businessId,applyState);
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
		
	}
	/**
	 *提交流程
	 */
	public EasyResult actionForSubmitApply() {
		JSONObject params = getJSONObject();
		String id = RandomKit.uniqueStr();
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String businessId = params.getString("businessId");
		if(StringUtils.notBlank(businessId)) {
			id = businessId;
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		String[] nodeUser = ApproveService.getService().getNodeUser(flowCode);
		String userIds = nodeUser[0];
		String userNames = nodeUser[1];
		
		String doAction = params.getString("doAction");
		EasyRecord applyRecord = getApplyRecord(flowCode);
		applyRecord.set("apply_state","submit".equals(doAction)?10:0);
		applyRecord.set("apply_no",params.getString("applyNo"));
		applyRecord.set("apply_title",params.getString("applyTitle"));
		applyRecord.set("apply_remark",params.getString("applyRemark"));
		applyRecord.set("apply_id",id);
		applyRecord.set("check_ids", userIds);
		applyRecord.set("check_names", userNames);
		
		EasyRecord record = new EasyRecord(flow.getTableName(),"business_id");
		record.setColumns(JsonKit.getJSONObject(params, "business"));
		record.set("HZ_MONEY", record.getString("BX_MONEY"));
		record.setPrimaryValues(id);
		
		try {
			updateItem(params,id,false);
			boolean bl = this.getQuery().save(record);
			if(bl) {
				this.getQuery().save(applyRecord);
			}
			if("submit".equals(doAction)) {
				this.startFlow(flow,id,applyState);
			}
		} catch (SQLException e) {
			this.error(null, e);
			this.delApplyDatas(id, flow.getTableName(),"zr_flow_bx_item");
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	private void updateItem(JSONObject params,String bussinessId,boolean isUpdate) throws SQLException {
		Object itemIndexObj = params.get("itemIndex");
		JSONArray itemIndexs = null;
		if(itemIndexObj instanceof String) {
			itemIndexs = new JSONArray();
			itemIndexs.add(itemIndexObj.toString());
		}else {
			itemIndexs = params.getJSONArray("itemIndex");
		}
			for(int i= 0;i<itemIndexs.size();i++) {
				EasyRecord itemModel = new EasyRecord("zr_flow_bx_item","ITEM_ID");
				String item = itemIndexs.getString(i);
				itemModel.set("BUSINESS_ID", bussinessId);
				itemModel.set("BX_DATE", params.getString("bxDate_"+item));
				itemModel.set("CONTRACT_ID", params.getString("contractId_"+item));
				itemModel.set("INVOICE_TYPE", params.getString("invoiceType_"+item));
				itemModel.set("CONTRACT_NAME", params.getString("contractName_"+item));
				itemModel.set("CONTRACT_NO", params.getString("contractNo_"+item));
				itemModel.set("FEE_TYPE", params.getString("feeType_"+item));
				itemModel.set("FEE_DESC", params.getString("feeDesc_"+item));
				itemModel.set("AMOUNT", params.getString("amount_"+item));//含税金额
				itemModel.set("HZ_AMOUNT", params.getString("amount_"+item));//核准金额
				itemModel.set("ITEM_INDEX", params.getString("orderIndex_"+item));
				itemModel.set("DEPT_ID", params.getString("deptId_"+item));
				itemModel.set("DEPT_NAME", params.getString("deptName_"+item));
				itemModel.set("TAX_RATE", params.getString("taxRate_"+item));//税率
				itemModel.set("TAXES", params.getString("taxes_"+item));//税金
				itemModel.set("R_AMOUNT", params.getString("rAmount_"+item));//去税金额
				itemModel.set("PRODUCT_LINE", params.getString("productLine_"+item));
				
				String itemId = params.getString("itemId_"+item);
				if(itemId.length()>20) {
					itemModel.set("ITEM_ID", itemId);
					this.getQuery().update(itemModel);
				}else {
					itemModel.setPrimaryValues(RandomKit.uniqueStr());
					this.getQuery().save(itemModel);
				}
		}
	}
	
	private void startFlow(FlowModel flow,String businessId,int applyState) throws SQLException {
		ApproveNodeModel nodeInfo = ApproveService.getService().getStartNode(flow.getFlowCode());
		
		String todoCheckUserId = nodeInfo.getCheckBy();
		if(todoCheckUserId.indexOf(getUserId())>-1) {
			 nodeInfo = ApproveService.getService().getNode(nodeInfo.getNextNodeId());
		}
		
		
		String resultId = RandomKit.uniqueStr();
		EasyRecord resultRecord = new EasyRecord("yq_flow_approve_result","result_id");
		String nextNodeId =  nodeInfo.getNodeId();
		String userId = nodeInfo.getCheckBy();
		String userName = nodeInfo.getCheckName();
		
		resultRecord.set("result_id", RandomKit.uniqueStr());
		resultRecord.set("business_id", businessId);
		resultRecord.set("check_name", getUserName());
		resultRecord.set("check_user_id", getUserId());
		resultRecord.set("check_result", 1);
		resultRecord.set("node_id",0);
		if(applyState==21) {
			resultRecord.set("node_name","修改申请");
		}else {
			resultRecord.set("node_name","申请填写");
		}
		resultRecord.set("check_desc","");
		resultRecord.set("check_time",EasyDate.getCurrentDateString());
		resultRecord.set("get_time",EasyDate.getCurrentDateString());
		this.getQuery().save(resultRecord);
		
		resultRecord.set("result_id", resultId);
		resultRecord.set("business_id", businessId);
		resultRecord.set("check_name", userName);
		resultRecord.set("check_user_id", userId);
		resultRecord.set("check_result", 0);
		resultRecord.set("node_id",nextNodeId);
		resultRecord.set("node_name",nodeInfo.getNodeName());
		resultRecord.set("check_desc","");
		resultRecord.set("get_time",EasyDate.getCurrentDateString());
		this.getQuery().save(resultRecord);
		
		
		FlowService.getService().updateApplyBegin(businessId, nextNodeId,resultId);
		
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		//微信推送给审批人
		MessageModel msgModel  = new MessageModel();
		msgModel.setSender(getUserId());
		msgModel.setSendName(getUserName());
		msgModel.setFkId(businessId);
		msgModel.setTitle(apply.getApplyTitle());
		msgModel.setTypeName(flow.getFlowCode());
		msgModel.setMsgLabel(nodeInfo.getNodeName());
		msgModel.setReceiver(userId);
		msgModel.setData1(flow.getFlowName());
		msgModel.setData2(apply.getApplyName());
		msgModel.setData3(apply.getApplyTime());
		msgModel.setDesc(apply.getApplyRemark());
		WxMsgService.getService().sendFlowTodoCheck(msgModel);
		MessageService.getService().sendMsg(msgModel);
	}
	
	public EasyResult actionForUpdateHzMoney() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		BigDecimal hzAmount = params.getBigDecimal("hzAmount");
		String businessId = params.getString("businessId");
		try {
			
			this.getQuery().executeUpdate("update zr_flow_bx_item set hz_amount = ? where item_id = ?",hzAmount,itemId);
			this.getQuery().executeUpdate("update zr_flow_bx_base t1 set t1.hz_money = (select sum(t2.hz_amount) from zr_flow_bx_item t2 where t1.business_id = t2.business_id) where t1.business_id = ?",businessId);
			String num = this.getQuery().queryForString("select t1.hz_money - t1.reverse_money from zr_flow_bx_base t1 where t1.business_id = ?", businessId);
			
			String payMoneyDx = NumberToCN.number2CNMontrayUnit(new BigDecimal(num));
			this.getQuery().executeUpdate("update zr_flow_bx_base t1 set t1.pay_money_dx = ? where t1.business_id = ?",payMoneyDx,businessId);
			
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForAddReadTime() {
		JSONObject params = getJSONObject();
		String resultId = params.getString("resultId");
		try {
			this.getQuery().executeUpdate("update yq_flow_approve_result set read_time = ? where result_id = ?", EasyDate.getCurrentDateString(),resultId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
}
