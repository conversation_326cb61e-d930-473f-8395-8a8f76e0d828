package com.yunqu.work.dao.flow;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.service.BxService;
import com.yunqu.work.utils.FlowUtils;

@WebObject(name = "FlowConfigDao")
public class FlowConfigDao extends AppDaoContext{

	@WebControl(name = "myFlowCategoryList",type = Types.TEMPLATE)
	public JSONObject myFlowCategoryList() {
		EasySQL sql = getEasySQL("select * from (");
		sql.append("select * from yq_flow_category where 1=1");
		sql.append(param.getString("pFlowCode"),"and p_flow_code = ?");
		sql.appendLike(param.getString("flowName"),"and flow_name like ?");
		sql.append(0,"and flow_state = ?");
		if(!isFlowMgr()) {
			sql.append(getDeptId(),"and (flow_auth_depts = 0  or find_in_set(?,flow_auth_depts))");
			sql.append(getDeptId(),"and  not find_in_set(?,not_auth_depts)");
		}
		sql.append("union");
		
		sql.append("select * from yq_flow_category where 1=1");
		sql.append("and p_flow_code = '0'");
		sql.append(0,"and flow_state = ?");
		if(!isFlowMgr()) {
			sql.append(getDeptId(),"and (flow_auth_depts = 0  or find_in_set(?,flow_auth_depts))");
			sql.append(getDeptId(),"and  not find_in_set(?,not_auth_depts)");
		}
		
		sql.append(") t");
		sql.append("order by t.flow_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "myFlowFavorite",type = Types.TEMPLATE)
	public JSONObject myFlowFavorite() {
		EasySQL sql = new EasySQL("select t1.*,t2.id from yq_flow_category t1,yq_flow_favorite t2 where 1=1");
		sql.append(getUserId(),"and t1.flow_code = t2.flow_code and t2.user_id = ?");
		sql.append(param.getString("pFlowCode"),"and  t1.p_flow_code = ?");
		sql.appendLike(param.getString("flowName"),"and  t1.flow_name like ?");
		sql.append(0,"and  t1.flow_state = ?");
		if(!isFlowMgr()) {
			sql.append(getDeptId(),"and ( t1.flow_auth_depts = 0  or find_in_set(?, t1.flow_auth_depts))");
			sql.append(getDeptId(),"and  !find_in_set(?, t1.not_auth_depts)");
		}
		sql.append("order by t2.add_time");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	
	
	@WebControl(name = "categoryList",type = Types.TEMPLATE)
	public JSONObject categoryList() {
		EasySQL sql = getEasySQL("select * from yq_flow_category where 1=1");
		sql.append(param.getString("flowState"),"and flow_state = ?");
		sql.append(param.getString("pFlowCode"),"and p_flow_code = ?");
		sql.append(param.getString("flowCode"),"and flow_code = ?");
		sql.append("order by flow_index");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "fieldList",type = Types.PAGE)
	public JSONObject fieldList() {
		EasySQL sql = getEasySQL("select t1.*,t2.flow_name,t2.flow_code from yq_flow_field t1,yq_flow_category t2 where 1=1 and t1.flow_code = t2.flow_code");
		sql.append(param.getString("flowCode"),"and t2.flow_code = ?");
		sql.append(param.getString("pFlowCode"),"and t2.p_flow_code = ?");
		sql.append(param.getString("flowState"),"and t2.flow_state = ?");
		sql.append("order by t2.flow_code,t1.idx_order");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "flowFields",type = Types.TEMPLATE)
	public JSONObject flowFields() {
		EasySQL sql = getEasySQL("select * from yq_flow_field where 1=1");
		sql.append(0,"and state = ?");
		sql.append(param.getString("flowCode"),"and flow_code = ?",false);
		sql.append("order by idx_order");
		JSONObject result = queryForList(sql.getSQL(),sql.getParams());
		JSONArray array = result.getJSONArray("data");
		JSONArray list = new JSONArray();
		for(int i= 0;i<array.size();i++) {
		   JSONObject row =	array.getJSONObject(i);
		   JSONObject extJson = JSONObject.parseObject(row.getString("EXT_CONFIG"));
		   if(extJson!=null&&!extJson.isEmpty()) {
			   JSONObject record = new JSONObject();
			   record.putAll(extJson);
			   int tableFlag = row.getIntValue("TABLE_FLAG");
			   if(tableFlag==1) {
				   record.put("field", row.getString("TABLE_FIELD"));
			   }else {
				   record.put("field", row.getString("TABLE_FIELD").toUpperCase());
			   }
			   record.put("tableFlag", tableFlag);
			   record.put("title", row.getString("TITLE"));
			   record.put("sort", extJson.getBooleanValue("sort"));
			   list.add(record);
		   }
		}
		return getJsonResult(list);
	}
	
	@WebControl(name="fieldInfo",type=Types.RECORD)
	public JSONObject fieldInfo(){
		String fieldId=param.getString("fieldId");
		if(StringUtils.isBlank(fieldId)){
			return getJsonResult(new JSONObject());
		}
		return queryForRecord("select * from yq_flow_field where FIELD_ID = ?", fieldId);
	}
	
	
	@WebControl(name = "groupList",type = Types.TEMPLATE)
	public JSONObject groupList() {
		EasySQL sql = getEasySQL("select * from yq_group_user where 1=1");
		sql.append(getUserId(),"and creator = ?");
		sql.append("order by create_time");
		return queryForList(sql.getSQL(),sql.getParams());
	}

	private JSONArray bxNodeList(List<ApproveNodeModel> list){
		JSONArray jsonList = new JSONArray();
		for(ApproveNodeModel model :list) {
			JSONObject row = new JSONObject();
			row.put("NODE_ID",model.getNodeId());
			row.put("NODE_NAME",model.getNodeName());
			row.put("C_NODE_ID",model.getNextNodeId());
			row.put("P_NODE_ID",model.getPrevNodeId());
			jsonList.add(row);
			
		}
		return jsonList;
	}
	@WebControl(name = "nodeConfig",type=Types.LIST)
	public JSONObject nodeConfig() {
		String flowCode = param.getString("flowCode");
		boolean isBxFlow = FlowUtils.isBxLoanFlow(flowCode);
		if(isBxFlow) {
			return getJsonResult(bxNodeList(BxService.getService().getApprovers(getDeptId())));
		}
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_node t1 where 1=1");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?",false);
		sql.append("order by t1.check_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "roleConfig",type=Types.LIST)
	public JSONObject roleConfig() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_role t1 where 1=1");
		sql.append(param.getString("roleName"),"and t1.role_name = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeDict",type=Types.DICT)
	public JSONObject nodeDict() {
		EasySQL sql = getEasySQL("select t1.node_id,t1.node_name from yq_flow_approve_node t1 where 1=1");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?",false);
		sql.append(param.getString("nodeId"),"and t1.node_id <> ?");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeInfo",type=Types.RECORD)
	public JSONObject nodeInfo() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_node t1 where 1=1");
		String nodeId = param.getString("nodeId");
		if(StringUtils.isBlank(nodeId)) {
			return getJsonResult(null);
		}
		sql.append(nodeId,"and t1.node_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "roleInfo",type=Types.RECORD)
	public JSONObject roleInfo() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_role t1 where 1=1");
		String roleId = param.getString("roleId");
		if(StringUtils.isBlank(roleId)) {
			return getJsonResult(null);
		}
		sql.append(roleId,"and t1.role_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	
	@WebControl(name = "roleDict",type = Types.DICT)
	public JSONObject roleDict() {
		return getDictByQuery("select role_id,role_name from yq_flow_approve_role");
	}
	
	@WebControl(name = "getFaqUrl",type = Types.DICT)
	public JSONObject getFaqUrl() throws SQLException {
		String text  = this.getQuery().queryForString("select faq_url from yq_flow_category where flow_code = ?", param.getString("flowCode"));
		return getJsonResult(text);
	}
	
	@WebControl(name = "categoryDict",type = Types.DICT)
	public JSONObject categoryDict() {
		return getDictByQuery("select flow_code,flow_name from yq_flow_category where  p_flow_code = ?","0");
	}
	
	@WebControl(name = "flowDict",type = Types.DICT)
	public JSONObject flowDict() {
		return getDictByQuery("select flow_code,flow_name from yq_flow_category where  p_flow_code <> ?","0");
	}
	
	@WebControl(name = "categoryInfo",type = Types.RECORD)
	public JSONObject categoryInfo() {
		String flowCode = param.getString("conf.FLOW_CODE");
		return queryForRecord("select * from yq_flow_category where flow_code = ?", flowCode);
	}

	

	
}
