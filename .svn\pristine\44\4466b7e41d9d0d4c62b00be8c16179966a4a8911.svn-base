<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择用户</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selectTextForm">
              <div class="ibox">
              		<table id="textList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectText.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectText = {
					sid:'${param.sid}',
					query : function(){
						$("#selectTextForm").queryData();
					},
					initTable : function(){
						var el = $("[data-sid='"+SelectText.sid+"']");
					  	var text = $(el).data('text');
						var array = text.split(',');
						var list = [];
						for(var index in array){
							list.push({'text':array[index]});
						}
						$("#selectTextForm").initTable({
							data:list,
							id:'textList',
							page:false,
							limit:20,
							rowEvent:'rowEvent2',
							rowDoubleEvent:'SelectText.ok',
							cols: [[
					        {type:'checkbox'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'text',
								title: '名称'
							}
							]],done:function(){
								SelectText.initSelect($(el).val());
							}
					       }
						);
					},
					initSelect:function(val){

					},
					ok : function(selectRow){
						var el = $("[data-sid='"+SelectText.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('textList');
							if(checkStatus.data.length>0){
								var names = [];
								var data = checkStatus.data;
								for(var index in data){
									names.push(data[index]['text']);
								}
								el.val(names.join(','));
								popup.layerClose("selectTextForm");
							}else{
								el.val('');
								popup.layerClose("selectTextForm");
							}
						}else{
							el.val(selectRow['text']);
							popup.layerClose("selectTextForm");
						}
					}
					
			};
			$(function(){
				SelectText.initTable();
			});
			
			function rowEvent2(data,obj){
				obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
				obj.tr.find('div[class="layui-unselect layui-form-checkbox"]').trigger("click");
			}
			
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>