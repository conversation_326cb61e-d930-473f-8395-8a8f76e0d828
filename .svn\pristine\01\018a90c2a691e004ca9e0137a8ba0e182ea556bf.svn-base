<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>待办流程</title>
	<link href="${ctxPath}/static/css/sidebar.css" rel="stylesheet">
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<div class="page-box">
	  		<div class="left-sidebar" style="display: none;">
                <div class="left-sidebar-item" data-mars="FlowDao.myFlowTodoTree" data-template="template-list"></div>
       		</div>
        	<div onclick="leftShow(this)" style="position: absolute;display: inline-flex;align-items: center;height: 50px;background-color: #00abfc;top:40%;cursor: pointer;color: #fff;"><i class="fa fa-chevron-right" aria-hidden="true"></i></div>
  			<div class="right-content" style="width: 100%;margin-left: 0px;">
			  <div class="ibox">
				<form  autocomplete="off" method="post" onsubmit="return false;" class="form-inline" id="flowMgrForm">
				  <div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		 	 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程编号</span>
						 	<input class="form-control input-sm" name="applyNo">
						 	<input type="hidden" name="flowCode" id="flowCode"/>
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">流程名称</span>
						 	<input class="form-control input-sm" name="flowName">
						 </div>
	          		 	 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">节点名称</span>
						 	<input class="form-control input-sm" type="text" name="_nodeName">
						 </div>
						 <div class="input-group input-group-sm" style="width: 130px;">
					 		<span class="input-group-addon">发起人</span>
						 	<input class="form-control input-sm" name="applyName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	                </div> 
					<div class="ibox-content">
						 <table id="_flowTable"></table>
					</div>
				  </form>
				</div>
			  </div>
			</div>	
		   <script type="text/x-jsrender" id="bar">
 				<a class="btn btn-xs btn-info" href="javascript:;"><i class="fa fa-paper-plane-o"></i> 审批</a>
		  </script>
		  	 <script type="text/html" id="btnBar">
			 	<button class="btn btn-sm btn-default" type="button" lay-event="refreshData"><i class="fa fa-refresh"></i> 刷新</button>
			 	<button class="btn btn-sm btn-default ml-10" type="button" lay-event="entrust"><i class="fa fa-random"></i> 委托</button>
			 	<button class="btn btn-sm btn-default ml-10" type="button" lay-event="ccFlow"><i class="fa fa-group"></i> 抄送</button>
 		</script>
 		 <script id="template-list" type="text/x-jsrender">
			{{for flowCategory}}
 				<div class="sidebar-item">
					<a href="javascript:void(0);" class="sidebar-nav-sub-title active"><i class="sidebar-icon glyphicon glyphicon-menu-right"></i>{{:P_FLOW_NAME}}</a>
				    <ul class="sidebar-nav-sub">
						{{for #parent.parent.data.data}}
							{{if #parent.parent.data.P_FLOW_CODE==P_FLOW_CODE}}
						  		<li>
								  <a href="javascript:void(0)" data-url="{{:QUERY_URL}}" data-code="{{:FLOW_CODE}}">{{:FLOW_NAME}}({{:COUNT}})</a>
						  	  </li>
							{{/if}}
  						 {{/for}}
				    </ul>
				</div>
			{{/for}}
			{{if flowCategory.length==0}}
				<p class="text-c mt-30">无数据</p>
			{{/if}}
 		</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/yq-work/static/js/flow.js"></script>
	<script type="text/javascript">
	
		   var flowList = {flowCode:'${param.flowCode}'};

		   $(function(){
				$(".page-box").render({success:function(){
					flowList.renderTree();
					initList();
					loadFlowNum();
				}});
			});
			flowList.renderTree = function(){
				$('body').on('click','.sidebar-nav-sub-title',function(e){
	    			$(this).toggleClass('active')
	    		})
				$(".left-sidebar-item [data-code]").click(function(){
	    			var t=$(this);
	    			var flowCode = t.data("code");
	    			var queryUrl = t.data("url");
	    			if(queryUrl){
	    				$.get(queryUrl,{flowCode:flowCode,isDiv:'1'},function(html){
		  					$(".ibox").hide();
		  					$(".flowQuery").html(html);
		  					$(".flowQuery .container-fluid").removeClass('container-fluid');
	    				});
	    				return;
	    			}
	    			if(flowCode){
	    				$(".flowQuery").empty();
	    				$(".ibox").show();
	    				$("[name='flowCode']").val(flowCode);
		    			$(".left-sidebar-item a").removeClass("activeItem");
		    			t.addClass("activeItem");
		    			flowList.queryData();
	    			}
	    		});
				if(flowList.flowCode){
					$(".left-sidebar-item [data-code='"+flowList.flowCode+"']").click();
				}
				
			}
			function initList(){
				$("#flowMgrForm").initTable({
					mars:'FlowDao.myFlowTodo',
					id:'_flowTable',
					page:true,
					limit:50,
					title:'我的待办',
					toolbar:'#btnBar',
					rowDoubleEvent:'flowDetail',
					height:'full-90',
					cellMinWidth:100,
					rowEvent:'rowEvent',
					cols: [[
					 {
	            	 	type: 'radio',
						title: '序号',
						align:'left'
					 },{
						field:'',
						title:'操作',
						width:80,
						align:'center',
						event:'flowDetail',
						templet:function(row){
							return renderTpl('bar',row);
						}
					},{
						 field:'APPLY_NO',
						 title:'编号',
						 width:220,
						 templet:function(row){
							 var readTime = row['READ_TIME'];
							 var html = '';
							 if(readTime==''){
								 html = '<span class="layui-badge-dot mr-5"></span>';
							 }
							// var after = twoTimeInterval(row.OVER_TIME,'');
							// if(after!=''){
							//	 after = '<span class="fa fa-hourglass-end ml-5" title="'+after+'"></span>';
							// }
							 return html+row['APPLY_NO']+"_"+row['FLOW_NAME'];
						 }
					 },{
						 field:'APPLY_TITLE',
						 title:'流程名称',
						 minWidth:220
					 },{
						 field:'APPLY_LEVEL',
						 title:'紧急程度',
						 width:70,
						 templet:function(row){
							return row['APPLY_LEVEL'];
						}
					 },{
						 field:'NODE_NAME',
						 title:'当前节点',
						 width:110
					 },{
						 field:'DEPT_NAME',
						 title:'发起部门',
						 width:100
					 },{
						 field:'APPLY_NAME',
						 title:'发起人',
						 width:80
					 },{
					    field: 'APPLY_TIME',
						title: '申请时间',
						align:'center',
					    width:160
					}
				]],done:function(){
					$("tbody [data-field='APPLY_LEVEL'][data-content='紧急']").parent().css('background-color','#f6f1c0');
				  //$(".fa-hourglass-end").parent().parent().parent().css('background-color','#efd9ee');
			  	}
			});
		 }
			
		function flowDetail(data){
			var flowCode = data['FLOW_CODE'];
			var readTime = data['READ_TIME'];
			var resultId = data['RESULT_ID'];
			if(readTime==''){
				ajax.remoteCall("${ctxPath}/servlet/flow/approve?action=addReadTime",{resultId:resultId},function(result) {});
			}
			var json  = $.extend({},{businessId:data['APPLY_ID'],flowCode:flowCode,isApprovalFlag:'1','handle':'approval',resultId:resultId});
			popup.openTab({id:'flowDetail',title:'审批-'+data['FLOW_NAME'],url:'${ctxPath}/web/flow',data:json});
		}
		
		function queryData(){
			$("#flowMgrForm").queryData({id:'_flowTable'});
		}
			
	    function refreshData(){
		   location.reload();
	    }
		  
		function entrust(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    FlowCore.params = {resultId:data['RESULT_ID'],businessId:data['APPLY_ID']};
		    FlowCore.trustLayer();
		}
		
		function ccFlow(dataList){
			if(dataList.length == 0){
				layer.msg('请选择操作的记录。',{icon : 7, time : 1000});
				return;
			}
		    var data = dataList[0];
		    FlowCore.params = {resultId:data['RESULT_ID'],businessId:data['APPLY_ID']};
		    FlowCore.ccLayer();
		}

		flowList.queryData = function(){
			$("#flowMgrForm").queryData({id:'_flowTable'});
		}
		
		 function leftShow(el){
			   var flag = $(el).data('flag');
			   if(flag=='1'){
				   $(el).prev().hide(0);
				   $(el).next().css('width','100%').css('margin-left','0px');
				   $(el).data('flag','0');
				   $(el).html('<i class="fa fa-chevron-right"></i>');
			   }else{
				   $(el).prev().show(300);
				   $(el).next().css('width','calc(100% - 160px)');
				   $(el).data('flag','1');
				   $(el).html('<i class="fa fa-chevron-left"></i>');
			   }
		   }
	</script>
</EasyTag:override>

<%@ include file="/pages/common/layout-layui-auto.jsp" %>