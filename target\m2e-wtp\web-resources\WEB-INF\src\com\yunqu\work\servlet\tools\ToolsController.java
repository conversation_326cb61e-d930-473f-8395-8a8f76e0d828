package com.yunqu.work.servlet.tools;

import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Clear;
import com.jfinal.core.ActionKey;
import com.jfinal.core.Path;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.model.NoteModel;

@Clear
@Path(value = "/tools")
public class ToolsController extends BaseController {

	@ActionKey(value = "/addNote")
	public void addNote() {
		String key = getPara();
		String content = getPara("content");
		String tags = getPara("tags");
		this.info("key="+key+",content="+content+",tags="+tags, null);
		if(StringUtils.isBlank(key)) {
			renderText("key不能为空");
			return;
		}else {
			try {
				boolean bl= this.getMainQuery().queryForExist("select count(1) from easi_user where state =0 and user_id = ?",key);
				if(!bl) {
					renderText("key不存在");
					return;
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				renderText(e.getMessage());
				return;
			}
		}
		if(StringUtils.isBlank(content)) {
			renderText("内容不能为空");
			return;
		}
		
		NoteModel model= new NoteModel();
		model.setPrimaryValues(RandomKit.uniqueStr());
		String title = Jsoup.clean(content, Whitelist.simpleText());
		if(title.length()>50) {
			model.set("title",title.substring(0, 50));
		}else {
			model.set("title",title);
		}
		model.set("content",content);
		model.set("tags",tags.replaceAll("\n",","));
		model.set("update_time", EasyDate.getCurrentDateString());
		model.setCreator(key);
		try {
			model.save();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			renderText(e.getMessage());
			return;
		}
		renderText("0");
	}
	
	@ActionKey(value = "/noteTags")
	public void noteTags() {
		String key = getPara();
		if(StringUtils.isBlank(key)) {
			renderText("key不能为空");
			return;
		}else {
			try {
				boolean bl= this.getMainQuery().queryForExist("select count(1) from easi_user where state = 0 and user_id = ?",key);
				if(!bl) {
					renderText("key不存在");
					return;
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				renderText(e.getMessage());
				return;
			}
		}
		try {
			List<JSONObject> list = this.getQuery().queryForList("select DISTINCT(tags) from yq_note where creator = ?", new Object[] {key}, new JSONMapperImpl());
			Set<String> set = new HashSet<String>();
			set.add("账号信息");
			set.add("网上摘录");
			set.add("网址收藏");
			set.add("sql语句");
			set.add("代码");
			for(JSONObject row:list) {
				String tags = row.getString("TAGS");
				String[] array  = tags.split(",");
				for(String str:array) {
					set.add(str);
				}
			}
			StringBuffer  sb = new StringBuffer();
			for(String str:set) {
				sb.append(str+"\n");
			}
			renderText(sb.toString());
		} catch (SQLException e) {
			this.error(null, e);
			renderText("");
		}
	}
	
	@ActionKey(value = "/note")
	public void note() {
		renderJsp("/pages/note/note-list.jsp");
		
	}
	
	
}
