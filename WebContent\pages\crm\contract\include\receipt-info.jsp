<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table class="layui-table">
    <tr>
        <td>合同总额</td>
        <td>已收款金额</td>
        <td>已收款占比</td>
        <td>未收款金额</td>
    </tr>
    <tr>
        <td style="width: 25%;"><b id="receiptContractAmount"></b></td>
        <td style="width: 25%;"><b id="receiptAmount"></b></td>
        <td style="width: 25%;"><b id="receiptRatio"></b></td>
        <td style="width: 25%;"><b id="unReceiptAmount"></b></td>
    </tr>
</table>
<table id="receiptTable"></table>
<script  type="text/html" id="receiptToolbar">
    <button class="btn btn-info btn-sm" onclick="contractReceiptList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-10" onclick="contractReceiptList.edit()" type="button">修改</button>
    <button class="btn btn-warning btn-sm ml-10" onclick="contractReceiptList.del()" type="button">删除</button>
    <button class="btn btn-default btn-sm ml-10" onclick="reloadContractReceiptList()" type="button">刷新</button>
</script>
<script type="text/javascript">
    var contractReceiptList={
        init:function(){
            $("#ContractDetailForm").initTable({
                mars:'ContractReceiptDao.receiptList',
                id:'receiptTable',
                page:false,
                toolbar:'#receiptToolbar',
                cellMinWidth:50,
                totalRow:true,
                rowEvent:'rowEvent',
                autoSort:true,
                cols: [[
                    {
                        type: 'radio',
                        align:'left',
                    },{
                        type: 'numbers',
                        title: '序号',
                        align:'left',
                        width:80,
                        totalRowText:"合计",
                    },{
                        field:'RECEIPT_DATE',
                        title:'收款日期',
                        align:'left',
                        width:100,
                        sort:true
                    },{
                        field:'CUST_NO',
                        title:'客户编号',
                        width:100,
                        templet:'<div><a href="javascript:;" onclick="contractReceiptList.custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NO}}</a></div>'
                    },{
                        field:'CUSTOMER_NAME',
                        title:'客户名称',
                        minWidth: 120,
                        templet:'<div><a href="javascript:;" onclick="contractReceiptList.custDetail(\'{{d.CUST_ID}}\')">{{d.CUSTOMER_NAME}}</a></div>'
                    },{
                        field:'STAGE_ID',
                        title:'合同阶段',
                        minWidth: 240,
                        templet:function(row){
                            return getText(row.STAGE_ID,'contractStage');
                        }
                    },{
                        field:'CURRENCY',
                        title:'币种',
                        width: 50
                    },{
                        field:'AMOUNT',
                        title:'收款金额',
                        totalRow:true,
                        width: 100
                    },{
                        field:'OWNER_NAME',
                        title:'本笔收款责任人',
                        width: 90
                    },{
                        field:'KAOHE_DATA',
                        title:'收款考核数据',
                    },{
                        field:'REMARK',
                        title:'备注',
                    },{
                        field:'UPDATE_BY',
                        title:'操作人',
                        width:90
                    },{
                        field:'UPDATE_TIME',
                        title:'更新时间',
                        width:140
                    }
                ]],
                done:function(data){
                    if(data.total!=undefined){
                        $("#contractReceiptCount").text("("+data.total+")");
                    }

                    var totalAmount = 0;
                    $('#receiptContractAmount').text(contractInfo.AMOUNT);
                    $.each(data.data, function(index, item) {
                        var amountWithTax = parseFloat(item.AMOUNT);
                        totalAmount += amountWithTax;
                    });
                    totalAmount = totalAmount.toFixed(2);
                    $('#receiptAmount').text(totalAmount);

                    var totalContractAmount =  parseFloat(contractInfo.AMOUNT);
                    var umReceipt = totalContractAmount-totalAmount;
                    umReceipt = umReceipt.toFixed(2);
                    $('#unReceiptAmount').text(umReceipt);

                    if(totalAmount == 0){
                        $('#receiptRatio').text("0%");
                    }else {
                        var ratio = totalAmount * 100 /totalContractAmount;
                        ratio = ratio.toFixed(2);
                        ratio =ratio+"%";
                        $('#receiptRatio').text(ratio);
                    }
                }
            });

        },
        add:function (){
            var custNo = contractInfo.CUST_NO;
            var custId = contractInfo.CUST_ID;
            var custName = contractInfo.CUSTOMER_NAME;
            var payeeId = "";
            var payeeName = "";
            if(contractInfo.PAYEE!= undefined && contractInfo.PAYEE != "" ){
                payeeId = contractInfo.PAYEE;
                var textJson = contractInfo['TEXT_JSON'];
                if(textJson){
                    var jsonObj = JSON.parse(textJson);
                    payeeName = jsonObj.PAYEE_NAME;
                }
            }else {
                payeeId = contractInfo.SALES_BY;
                var textJson = contractInfo['TEXT_JSON'];
                if(textJson){
                    var jsonObj = JSON.parse(textJson);
                    payeeName = jsonObj.SALES_BY_NAME;
                }
            }

            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增收款信息',offset:'auto',area:['800px','400px'],url:ctxPath+'/pages/crm/contract/include/receipt-edit.jsp',data:{custName:custName,custNo:custNo,custId:custId,payeeId:payeeId,payeeName:payeeName,contractId:contractId,isNew:'1'}});
        },
        edit:function(){
            var checkStatus = table.checkStatus('receiptTable');
            if(checkStatus.data.length > 1){
                layer.msg('一次只能更新一个收款信息',{icon : 7, time : 1000});
            }
            else if(checkStatus.data.length == 1){
                var receiptId = checkStatus.data[0]['RECEIPT_ID'];
                var payeeId = "";
                var payeeName = "";
                if(contractInfo.PAYEE!= undefined && contractInfo.PAYEE != "" ){
                    payeeId = contractInfo.PAYEE;
                    var textJson = contractInfo['TEXT_JSON'];
                    if(textJson){
                        var jsonObj = JSON.parse(textJson);
                        payeeName = jsonObj.PAYEE_NAME;
                    }
                }else {
                    payeeId = contractInfo.SALES_BY;
                    var textJson = contractInfo['TEXT_JSON'];
                    if(textJson){
                        var jsonObj = JSON.parse(textJson);
                        payeeName = jsonObj.SALES_BY_NAME;
                    }
                }
                popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'更新收款信息',offset:'auto',area:['800px','400px'],url:ctxPath+'/pages/crm/contract/include/receipt-edit.jsp',data:{contractId:contractId,receiptId:receiptId,payeeId:payeeId,payeeName:payeeName,isNew:'0'}});
            }else{
                layer.msg("请选择!");
            }
        },
        del:function(){
            var checkStatus = table.checkStatus('receiptTable');
            if(checkStatus.data.length>0){
                layer.confirm("确认要删除吗?",{icon:3,offset:'120px'},function(){
                    ajax.remoteCall("${ctxPath}/servlet/contractReceipt?action=BatchDel",checkStatus.data,function(result) {
                        if(result.state == 1){
                            layer.msg(result.msg,{icon:1,time:1200},function(){
                                layer.closeAll();
                                reloadContractReceiptList();
                            });
                        }else{
                            layer.alert(result.msg,{icon: 5});
                        }
                    });
                });
            }else{
                layer.msg("请选择!");
            }
        },
        custDetail:function(custId){
            var width = $(window).width();
            var w = '80%';
            if(width>1500){
                w = '800px';
            }else if(width<1000){
                w = '100%';
            }else{

            }
            popup.openTab({id:'custDetail',title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:[w,'100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId}});
        }
    }

    function getCustInfo(){

    }


    function reloadContractReceiptList(){
        $("#ContractDetailForm").queryData({id:'receiptTable',page:false});
        $("[name='contractStage']").render();
        reloadContractDetail();
        $("#ContractDetailForm").queryData({id:'stageTable',page:false});
    }
</script>
