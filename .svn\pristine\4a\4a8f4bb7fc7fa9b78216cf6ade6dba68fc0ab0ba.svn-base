package com.yunqu.work.servlet.common;

import java.util.List;

import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.string.StringUtils;

import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.jfinal.kit.SseEmitter;
import com.jfinal.plugin.activerecord.Db;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.utils.SiliconCloudAI;

@Before(AuthInterceptor.class)
@Path(value = "/llm")
public class LLMController extends BaseController {

	public void taskAnswerStreamAI() {
		SseEmitter sseEmitter = new SseEmitter(getResponse());
		String taskId = getPara();
		String content = Db.queryStr("select task_desc from yq_task where task_id = ?",taskId);
		if(StringUtils.notBlank(content)) {
			List<String> result = SiliconCloudAI.requestSSE("Qwen/Qwen2.5-Coder-7B-Instruct","请解答的这个任务,如果遇到开发问题,请使用Java,如果遇到数据,请使用mysql", content);
			if(result!=null) {
				for(String msg:result) {
					sseEmitter.sendMessage(msg);
					try {
						Thread.sleep(20);
					} catch (InterruptedException e) {
						this.error(e.getMessage(), e);
					}
				}
			}
		}
		sseEmitter.complete();
		renderNull();
	}
	
	public void taskAnswerAI() {
		String taskId = getJsonPara("taskId");
		String content = Db.queryStr("select task_desc from yq_task where task_id = ?",taskId);
		if(StringUtils.notBlank(content)) {
			String result = SiliconCloudAI.qwenCoder("请解答的这个任务,如果遇到开发问题,请使用Java,如果遇到数据,请使用mysql", content);
			if(StringUtils.notBlank(result)) {
				renderJson(EasyResult.ok(result));
				return;
			}
		}
		renderJson(EasyResult.fail());
	}
	
	public void weeklyAnswerAI() {
		String weeklyId = getJsonPara("weeklyId");
		String content = Db.queryStr("select item_1 from yq_weekly where weekly_id = ?",weeklyId);
		if(StringUtils.notBlank(content)) {
			String result = SiliconCloudAI.qwenCoder("请总结的这个周报", content);
			if(StringUtils.notBlank(result)) {
				renderJson(EasyResult.ok(result));
			}
		}
		renderJson(EasyResult.fail());
	}
	
	public void markdownToHtml() {
		String markdown = getPara("markdown");
		Parser parser = Parser.builder().build();
        HtmlRenderer renderer = HtmlRenderer.builder().build();
        Node document = parser.parse(markdown);
        String result =  renderer.render(document);
        renderText(result);
	}
}
