package com.yunqu.work.servlet.flow;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.aop.Before;
import com.jfinal.core.Path;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.ext.interceptor.GlobalInterceptor;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.FlowService;

@Path("/web/flow/config")
@Before(value = {GlobalInterceptor.class,AuthInterceptor.class})
public class FlowConfServlet extends BaseController {
	
	public void index() {
	
	}

	public void updateCategory(){
		EasyRecord model = new EasyRecord("yq_flow_category","flow_code");
		try {
			model.setColumns(getJSONObject("conf"));
			model.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	public void updateRow(){
		JSONObject params = getJSONObject();
		try {
			EasyRecord model = new EasyRecord("yq_flow_category","flow_code");
			model.setPrimaryValues(params.getString("FLOW_CODE"));
			model.set("FLOW_INDEX",params.getString("FLOW_INDEX"));
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void addCategory(){
		EasyRecord model = new EasyRecord("yq_flow_category","flow_code");
		try {
			model.setColumns(getJSONObject("conf"));
			model.set("create_time", EasyDate.getCurrentDateString());
			model.set("update_time", EasyDate.getCurrentDateString());
			this.getQuery().save(model);
			FlowService.getService().initFlowField(model.getString("FLOW_CODE"));
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void initField(){
		String flowCode = getPara("flowCode");
		if(StringUtils.notBlank(flowCode)) {
			FlowService.getService().initFlowField(flowCode);
		}
		renderJson(EasyResult.ok());
	}
	
	
	public void addFavorite(){
		EasyRecord model = new EasyRecord("yq_flow_favorite","flow_code","user_id");
		try {
			int count = this.getQuery().queryForInt("select count(1) from yq_flow_favorite where user_id = ?", getUserId());
			if(count>=12) {
				renderJson(EasyResult.fail("不能超过12个"));
				return;
			}
			model.set("flow_code",getJsonPara("flowCode"));
			model.set("add_time", EasyDate.getCurrentDateString());
			model.set("user_id", getUserId());
			model.set("user_name", getUserPrincipal().getUserName());
			boolean bl = this.getQuery().update(model);
			if(!bl) {
				this.getQuery().save(model);
			}
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void delFavorite(){
		EasyRecord model = new EasyRecord("yq_flow_favorite","id");
		try {
			model.setPrimaryValues(getJsonPara("id"));
			this.getQuery().deleteById(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void updateRole(){
		EasyRecord model = new EasyRecord("yq_flow_approve_role","role_id");
		try {
			model.setColumns(getJSONObject("conf"));
			this.getQuery().update(model);
			ApproveService.getService().clearRoleCache();
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void addRole(){
		EasyRecord model = new EasyRecord("yq_flow_approve_role","role_id");
		try {
			model.setColumns(getJSONObject("conf"));
			model.set("create_time", EasyDate.getCurrentDateString());
			model.remove("ROLE_ID");
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	public void updateTrust(){
		EasyRecord model = new EasyRecord("yq_flow_trust_conf","id");
		try {
			model.setColumns(getJSONObject("conf"));
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void addTrust(){
		EasyRecord model = new EasyRecord("yq_flow_trust_conf","id");
		try {
			model.setColumns(getJSONObject("conf"));
			model.set("create_name", getNickName());
			model.set("create_time", EasyDate.getCurrentDateString());
			model.remove("ID");
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void delTrust(){
		JSONObject params  = getJSONObject();
		EasyRecord model = new EasyRecord("yq_flow_trust_conf","id");
		model.setPrimaryValues(params.getString("id"));
		try {
			this.getQuery().deleteById(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void delGroup(){
		JSONObject params  = getJSONObject();
		EasyRecord model = new EasyRecord("yq_group_user","group_id");
		model.setPrimaryValues(params.getString("groupId"));
		try {
			this.getQuery().deleteById(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void addGroup(){
		JSONObject params  = getJSONObject();
		EasyRecord model = new EasyRecord("yq_group_user","group_id");
		try {
			model.setPrimaryValues(RandomKit.uniqueStr());
			model.set("user_ids", params.getString("userIds"));
			model.set("group_name", params.getString("groupName"));
			model.set("create_time", EasyDate.getCurrentDateString());
			model.set("creator", getUserId());
			model.set("group_type", 0);
			model.set("create_name", getUserPrincipal().getUserName());
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void addWord(){
		JSONObject params  = getJSONObject();
		EasyRecord model = new EasyRecord("yq_flow_approve_word");
		try {
			model.set("word_content", params.getString("wordContent"));
			model.set("create_name", getUserPrincipal().getUserName());
			model.set("create_time", EasyDate.getCurrentDateString());
			model.set("create_by", getUserId());
			model.set("order_index", 0);
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	public void updateWord(){
		JSONObject params  = getJSONObject();
		EasyRecord model = new EasyRecord("yq_flow_approve_word","id");
		try {
			model.setColumns(params);
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	public void delWord(){
		JSONObject params  = getJSONObject();
		EasyRecord model = new EasyRecord("yq_flow_approve_word","id");
		try {
			model.setColumns(params);
			this.getQuery().deleteById(model);
		} catch (SQLException e) {
			this.error(null, e);
			renderJson(EasyResult.fail(e.getMessage()));
			return;
		}
		renderJson(EasyResult.ok());
	}
	
	public void updateField(){
		EasyRecord record=new EasyRecord("yq_flow_field","FIELD_ID");
		record.setColumns(getJSONObject("field"));
		try {
			record.set("UPDATE_BY", getUserPrincipal().getUserName());
			record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderJson(EasyResult.ok());
	}
	public void addField(){
		EasyRecord record=new EasyRecord("yq_flow_field","FIELD_ID");
		record.setColumns(getJSONObject("field"));
		try {
			record.remove("FIELD_ID");
			record.set("UPDATE_BY", getUserPrincipal().getUserName());
			record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().save(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		renderJson(EasyResult.ok());
	}
	
	public void initFlowField() {
		try {
			List<JSONObject>  list = this.getQuery().queryForList("select flow_code from yq_flow_category",new Object[] {},new JSONMapperImpl());
			for(JSONObject row:list) {
				String flowCode = row.getString("FLOW_CODE");
				FlowService.getService().initFlowField(flowCode);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		renderText("ok");
	}
	
	public void delRole(){
		renderJson(EasyResult.ok());
	}
}
