<!-- Generated by SpecStory -->

# Untitled (2025-03-30 22:31:51)

_**User**_

Caused by: java.lang.NoClassDefFoundError: kotlin/jvm/internal/Intrinsics
	at okhttp3.ConnectionPool.<init>(ConnectionPool.kt) ~[okhttp-4.12.0.jar:?]
	at okhttp3.ConnectionPool.<init>(ConnectionPool.kt:47) ~[okhttp-4.12.0.jar:?]
	at okhttp3.OkHttpClient$Builder.<init>(OkHttpClient.kt:471) ~[okhttp-4.12.0.jar:?]
	at com.yunqu.work.ai.SiliconCloudAI.requestSSE(SiliconCloudAI.java:54) ~[classes/:?]
	at com.yunqu.work.servlet.common.LLMController.taskAnswerStreamAI(LLMController.java:36) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_65]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_65]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_65]
	at java.lang.reflect.Method.invoke(Method.java:497) ~[?:1.8.0_65]
	at com.jfinal.aop.Invocation.invoke(Invocation.java:97) ~[jfinal-5.2.3.jar:?]
	... 26 more
Caused by: java.lang.ClassNotFoundException: kotlin.jvm.internal.Intrinsics

能不能换个简单写法

---



---

