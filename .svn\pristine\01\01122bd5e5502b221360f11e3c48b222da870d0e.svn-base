<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>我的任务</title>
	<style type="text/css">
		.layui-table-cell{font-size: 13px;}
		.right-content .container-fluid{
			
		}
		.right-content .container-fluid,pt{
			padding: 0px;
		}
		.page-box .left-sidebar{width: 140px!important;}
		.page-box .right-content{margin-left: 140px!important;}
	</style>
	<link href="${ctxPath}/static/css/sidebar.css" rel="stylesheet">
</EasyTag:override>
<EasyTag:override name="content">
  <div class="page-box">
	  <div class="left-sidebar">
           <div class="left-sidebar-item" data-mars="ProductDao.productList" data-template="template-list"></div>
       </div>
        <script id="template-list" type="text/x-jsrender">
			{{for data}}
				{{if P_PRODUCT_ID==0}}
				 <div class="sidebar-item">
                   <a href="javascript:void(0);" data-url="${ctxPath}/pages/product/product-detail.jsp?isDiv=1&productId={{:PRODUCT_ID}}" class="sidebar-nav-sub-title active"><i class="sidebar-icon glyphicon glyphicon-menu-right"></i>{{:PRODUCT_NAME}}</a>
                   <ul class="sidebar-nav-sub">
					{{for #parent.parent.data}}
					   {{if #parent.parent.data.PRODUCT_ID==P_PRODUCT_ID}}
                         <li>
               				 <a href="javascript:void(0)" id="{{:PRODUCT_ID}}" data-url="${ctxPath}/pages/product/product-detail.jsp?isDiv=1&productId={{:PRODUCT_ID}}">{{:PRODUCT_NAME}}</a>
                         </li>
					   {{/if}}
					{{/for}}
                   </ul>
                 </div>
				{{/if}}
			{{/for}}
		</script>
  		<div class="right-content">
  			
  		
  		</div>
  </div>
        
        
</EasyTag:override>
<EasyTag:override name="script">
	<script>
    	$(function(){
    		$(".left-sidebar").render({success:function(){
	    		$('body').on('click','.sidebar-nav-sub-title',function(e){
	    			$(this).toggleClass('active')
	    		})
	    		$(".left-sidebar-item a").click(function(){
	    			var t=$(this);
	    			var url=t.data("url");
	    			if(url){
	  					$(".right-content").load(url);
		    			$(".left-sidebar-item a").removeClass("activeItem");
		    			t.addClass("activeItem");
		    			sessionStorage.setItem("lastProductId",t.attr("id"));
	    			}
	    		});
	    		var val = sessionStorage.getItem("lastProductId");
	    		if(val){
					var len = $("[id='"+val+"']").length; 
					if(len>0){
						$("[id='"+val+"']").click();
					}else{
			    		$(".sidebar-nav-sub a").first().click();
					}
	    		}else{
		    		$(".sidebar-nav-sub a").first().click();
	    		}
    		}});
    		
    		
    	});
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>