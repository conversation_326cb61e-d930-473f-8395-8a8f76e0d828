<%@ taglib prefix="stage" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>project</title>
    <style>
        .form-horizontal{width: 100%;}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="riskEditForm" class="form-horizontal" data-mars="ProjectRiskDao.record" autocomplete="off" data-mars-prefix="projectRisk.">
        <input type="hidden" value="${param.riskId}" name="projectRisk.RISK_ID"/>
        <input type="hidden" value="${param.projectId}" name="projectRisk.PROJECT_ID">
        <table class="table table-vzebra table-edit">
            <tbody>
                <tr>
                    <td style="width: 100px" class="required">风险分类</td>
                    <td style="width: 38%">
                        <select name="projectRisk.RISK_BELONG" data-rules="required" class="form-control input-sm">
                            <option value="">请选择</option>
                            <option value="我方">我方</option>
                            <option value="客户">客户</option>
                            <option value="三方(外购)">三方（外购）</option>
                            <option value="三方(外包)">三方（外包）</option>
                        </select>
                    </td>
                    <td style="width: 100px">风险等级</td>
                    <td>
                        <label class="radio radio-info radio-inline" style="margin-top: 2px;">
                            <input type="radio" checked value="0" name="projectRisk.RISK_LEVEL"><span>警告</span>
                        </label>
                        <label class="radio radio-info radio-inline">
                            <input type="radio" value="1" name="projectRisk.RISK_LEVEL"><span>严重</span>
                        </label>
                        <label class="radio radio-info radio-inline">
                            <input type="radio" value="2" name="projectRisk.RISK_LEVEL"><span>灾难</span>
                        </label>
                    </td>
                </tr>
                 <tr>
                    <td>风险触发时间</td>
                    <td>
                        <input type="text" data-rules="required" name="projectRisk.TRIGGER_TIME" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" class="form-control Wdate">
                    </td>
                    <td>负责处理人</td>
                    <td>
	                    <input name="projectRisk.SOL_OWNER" type="hidden">
	                    <input name="projectRisk.OWNER_NAME" onclick="singleUser(this);" readonly="readonly" class="form-control input-sm">
                    </td>
                </tr>
                <tr>
                    <td>风险说明</td>
                    <td colspan="3" >
                        <textarea style="height: 80px;" data-rules="required" class="form-control input-sm" name="projectRisk.RISK_DESC"></textarea>
                    </td>
                </tr>
                <tr>
                    <td class="required">风险应对措施</td>
                    <td colspan="3" >
                        <textarea style="height: 80px;" data-rules="required" class="form-control input-sm" name="projectRisk.SOLUTION"></textarea>
                    </td>
                </tr>
                <tr>
                    <td >跟进说明</td>
                    <td colspan="3">
                        <textarea style="height: 80px;" class="form-control input-sm" name="projectRisk.FOLLOW_CONTENT"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>
        <p class="layer-foot text-c">
            <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="RiskEdit.ajaxSubmitForm()"> 保 存 </button>
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="popup.layerClose(this);"> 关闭 </button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript">
        jQuery.namespace("RiskEdit");

        RiskEdit.riskId = '${param.riskId}';
        RiskEdit.isNew = '${param.isNew}';
        RiskEdit.projectId = '${param.projectId}';

        $(function(){
            $("#riskEditForm").render({success:function(result){

                }});
        });

        RiskEdit.ajaxSubmitForm = function(){
            if(form.validate("#riskEditForm")){
                if(RiskEdit.isNew=='1'){
                    RiskEdit.insertData();
                }
                else if(RiskEdit.riskId){
                    RiskEdit.updateData();
                }else{
                    RiskEdit.insertData();
                }
            };
        }

        RiskEdit.insertData = function() {
            var data = form.getJSONObject("#riskEditForm");
            ajax.remoteCall("${ctxPath}/servlet/projectRisk?action=add",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadRisk();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }

        RiskEdit.updateData = function() {
            var data = form.getJSONObject("#riskEditForm");
            ajax.remoteCall("${ctxPath}/servlet/projectRisk?action=update",data,function(result) {
                if(result.state == 1){
                    layer.msg(result.msg,{icon:1,time:1200},function(){
                        layer.closeAll();
                        reloadRisk();
                    });
                }else{
                    layer.alert(result.msg,{icon: 5});
                }
            });
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>