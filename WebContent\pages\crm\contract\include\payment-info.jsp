<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<table class="layui-table">
    <tr>
        <td>合同总额(含税)</td>
        <td>已输入款项金额(含税)</td>
        <td>未输入款项金额(含税)</td>
        <td>已输入款项金额(税后)</td>
    </tr>
    <tr>
        <td style="width: 25%;"><b id="paymentContractAmount"></b></td>
        <td style="width: 25%;"><b id="inputContractAmount"></b></td>
        <td style="width: 25%;"><b id="unInputContractAmount"></b></td>
        <td style="width: 25%;"><b id="contractNoTaxAmount"></b></td>
    </tr>
</table>

<table id="paymentTable"></table>
<script type="text/html" id="PaymentToolbar">
    <button class="btn btn-info btn-sm" onclick="paymentList.add()" type="button">新增</button>
    <button class="btn btn-default btn-sm ml-10" onclick="paymentList.edit()" type="button">修改</button>
    <button class="btn btn-warning btn-sm ml-10" onclick="paymentList.del()" type="button">删除</button>
    <button class="btn btn-default btn-sm ml-10" onclick="reloadPaymentList()" type="button">刷新</button>
</script>
<script type="text/javascript">
    var paymentList = {
        init: function () {
            $("#ContractDetailForm").initTable({
                mars: 'ContractPaymentDao.paymentList',
                id: 'paymentTable',
                page: false,
                toolbar: '#PaymentToolbar',
                rowEvent: 'rowEvent',
                cellMinWidth: 50,
                totalRow: true,
                autoSort: true,
                cols: [[
                    {
                        type: 'radio',
                        align: 'left',
                        totalRowText: "合计"
                    }, {
                        field: 'PAYMENT_TYPE',
                        title: '款项类型',
                        align: 'left',
                        minWidth: 150,
                        templet: function (d) {
                            return getText(d.PAYMENT_TYPE, 'paymentType');
                        }
                    }, {
                        field: 'AMOUNT_WITH_TAX',
                        title: '合同金额(含税)',
                        align: 'left',
                        sort: true,
                        minWidth: 100,
                        totalRow: true,
                    }, {
                        field: 'RATIO',
                        title: '占比(%)',
                        align: 'left',
                        sort: true,
                        width: 100,
                        minWidth: 100,
                        totalRow: true
                    }, {
                        field: 'AMOUNT_NO_TAX',
                        title: '合同金额(税后)',
                        minWidth: 100,
                        totalRow: true,
                    }, {
                        field: 'TAX_RATE',
                        title: '税率(%)',
                        minWidth: 80
                    }, {
                        field: 'TAX',
                        title: '税金',
                        totalRow: true,
                    }, {
                        field: 'JICHENG_FEE',
                        title: '集成费',
                        totalRow: true,
                    }, {
                        field: 'FINANCIAL_FEE',
                        title: '财务费用',
                        totalRow: true,
                    }, {
                        field: 'INVOICED_AMOUNT',
                        title: '已开票金额',
                        hide: true
                    }, {
                        field: 'CHECKED_FLAG',
                        title: '已审核',
                        width: 80,
                        templet: function (d) {
                            if (d.CHECKED_FLAG == '1') {
                                return '√';
                            } else {
                                return '×';
                            }
                        }
                    }, {
                        field: 'UPDATE_BY',
                        title: '操作人',
                        width: 90
                    }, {
                        field: 'UPDATE_TIME',
                        title: '更新时间',
                        width: 140
                    }
                ]],
                done: function (data) {
                    if (data.total != undefined) {
                        $("#contractPaymentCount").text("(" + data.total + ")");
                    }

                    var totalAmount = 0;
                    var totalAmountNoTax = 0;
                    $('#paymentContractAmount').text(contractInfo.AMOUNT);
                    $.each(data.data, function (index, item) {
                        var amountWithTax = parseFloat(item.AMOUNT_WITH_TAX);
                        var amountNoTax1 = parseFloat(item.AMOUNT_NO_TAX);
                        totalAmount += amountWithTax;
                        totalAmountNoTax += amountNoTax1;
                    });

                    $('#inputContractAmount').text(totalAmount.toFixed(2));
                    $('#contractNoTaxAmount').text(totalAmountNoTax.toFixed(2));
                    var unInput = parseFloat(contractInfo.AMOUNT) - totalAmount;
                    $('#unInputContractAmount').text(unInput.toFixed(2));

                    var paymentDivArr = $('#paymentDiv .layui-table-total .layui-table-cell');
                    var totalRatio = totalAmount * 100 / Number(contractInfo.AMOUNT);
                    var ratioDiv = $(paymentDivArr[3]);
                    ratioDiv.html(totalRatio.toFixed(2));
                }
            });
        },
        del: function () {
            var checkStatus = table.checkStatus('paymentTable');
            if (checkStatus.data.length > 0) {
                var flag = false;
                for (var i = 0; i < checkStatus.data.length; i++) {
                    if (checkStatus.data[i].SELECT_FLAG === '1') {
                        flag = true;
                    }
                }
                if (flag) {
                    layer.confirm('该收款已被收入确认阶段选择，<br>请先删除对应的收入确认阶段!', {icon: 3, offset: '120px'}, function () {
                        layer.closeAll();
                    })
                } else {
                    layer.confirm("确认要删除吗?", {icon: 3, offset: '120px'}, function () {
                        ajax.remoteCall("${ctxPath}/servlet/contractPayment?action=BatchDel", checkStatus.data, function (result) {
                            if (result.state == 1) {
                                layer.msg(result.msg, {icon: 1, time: 1200}, function () {
                                    layer.closeAll();
                                    reloadPaymentList();
                                });
                            } else {
                                layer.alert(result.msg, {icon: 5});
                            }
                        });
                    });
                }
            } else {
                layer.msg("请选择!");
            }
        },
        edit: function () {
            var checkStatus = table.checkStatus('paymentTable');

            if (checkStatus.data.length > 1) {
                layer.msg('一次只能更新一个款项', {icon: 7, time: 1000});
            }
            else if(checkStatus.data.length == 1){
                var paymentId = checkStatus.data[0]['PAYMENT_ID'];
                if (checkStatus.data[0].SELECT_FLAG === '1') {
                    layer.confirm("此收款信息已被收入确认阶段选择，<br>无法修改金额。", {icon: 3, offset: '20px'}, function () {
                        popup.layerShow({
                            type: 1,
                            anim: 0,
                            scrollbar: false,
                            shadeClose: false,
                            title: '更新款项',
                            offset: '20px',
                            area: ['500px', '560px'],
                            url: ctxPath + '/pages/crm/contract/include/payment-edit.jsp',
                            data: {amount: contractInfo.AMOUNT, contractId: contractId, paymentId: paymentId, isNew: '0', isSelected: '1'}
                        });
                    });
                } else {
                    popup.layerShow({
                        type: 1,
                        anim: 0,
                        scrollbar: false,
                        shadeClose: false,
                        title: '更新款项',
                        offset: '20px',
                        area: ['500px', '560px'],
                        url: ctxPath + '/pages/crm/contract/include/payment-edit.jsp',
                        data: {amount: contractInfo.AMOUNT, contractId: contractId, paymentId: paymentId, isNew: '0'}
                    });
                }
            } else {
                layer.msg("请选择!");
            }
        },
        add: function () {
            popup.layerShow({type:1,anim:0,scrollbar:false,shadeClose:false,title:'新增款项信息',offset:'20px',area:['500px','560px'],url:ctxPath+'/pages/crm/contract/include/payment-edit.jsp',data:{amount:contractInfo.AMOUNT,contractId:contractId,isNew:'1'}});
        }
    }

    function reloadPaymentList() {
        $("#ContractDetailForm").queryData({id: 'paymentTable', page: false});
        reloadContractDetail();
        IncomeConfirmList.init();
        IncomeConfirmListB.init();
    }
</script>
