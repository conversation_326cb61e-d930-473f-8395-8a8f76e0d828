package com.yunqu.work.servlet.platform;

import java.sql.SQLException;
import javax.servlet.annotation.WebServlet;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

@WebServlet("/servlet/custOperate/*")
public class CustOperateServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;
    
    private EasyRecord getPriceModel(String prefix){
        EasyRecord model = new EasyRecord("yq_crm_sales_price","PRICE_ID");
        model.setColumns(getJSONObject(prefix));
        return model;
    }

    public EasyResult actionForAddPrice(){
        EasyRecord model = getPriceModel("price");
        model.setPrimaryValues(RandomKit.uuid().toUpperCase());
        model.set("CREATE_TIME", EasyDate.getCurrentDateString());
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        model.set("CREATOR", getUserId());
        model.set("CREATE_NAME", getUserName());
        
        try {
            this.getQuery().save(model);
            return EasyResult.ok("保存成功");
        } catch (SQLException e) {
            this.error("保存失败", e);
            return EasyResult.fail("保存失败:" + e.getMessage());
        }
    }
    
    public EasyResult actionForUpdatePrice(){
        EasyRecord model = getPriceModel("price");
        try {
            model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getQuery().update(model);
            return EasyResult.ok("更新成功");
        } catch (SQLException e) {
            this.error("更新失败", e);
            return EasyResult.fail("更新失败:" + e.getMessage());
        }
    }
    
    public EasyResult actionForDeletePrice(){
        String priceId = getJsonPara("priceId");
        try {
            this.getQuery().executeUpdate("delete from yq_crm_sales_price where PRICE_ID = ?", priceId);
            return EasyResult.ok("删除成功");
        } catch (SQLException e) {
            this.error("删除失败", e);
            return EasyResult.fail("删除失败:" + e.getMessage());
        }
    }

    private EasyRecord getCostModel(String prefix){
        EasyRecord model = new EasyRecord("yq_crm_cost","COST_ID");
        model.setColumns(getJSONObject(prefix));
        return model;
    }

    public EasyResult actionForAddCost(){
        EasyRecord model = getCostModel("cost");
        model.setPrimaryValues(RandomKit.uuid().toUpperCase());
        model.set("MONTH_ID", DateUtils.getMonthIdFormat(model.getString("DATE_ID")));
        model.set("CREATE_TIME", EasyDate.getCurrentDateString());
        model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        model.set("CREATOR", getUserId());
        model.set("CREATE_NAME", getUserName());
        
        try {
            this.getQuery().save(model);
            return EasyResult.ok("保存成功");
        } catch (SQLException e) {
            this.error("保存失败", e);
            return EasyResult.fail("保存失败:" + e.getMessage());
        }
    }
    
    public EasyResult actionForUpdateCost(){
        String costId = getJsonPara("costId");
        if(StringUtils.notBlank(costId)) {
            try {
                boolean isSettled = this.getQuery().queryForExist("SELECT COUNT(1) FROM yq_crm_cost WHERE COST_ID = ? AND SETTLEMENT_ID IS NOT NULL AND SETTLEMENT_ID != ''", costId);
                if(isSettled) {
                    return EasyResult.fail("该成本记录已被结算，不允许修改");
                }
            } catch (SQLException e) {
                this.error("检查结算状态失败", e);
                return EasyResult.fail("检查结算状态失败:" + e.getMessage());
            }
        }
        
        EasyRecord model = getCostModel("cost");
        model.set("MONTH_ID", DateUtils.getMonthIdFormat(model.getString("DATE_ID")));
        try {
            model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            this.getQuery().update(model);
            return EasyResult.ok("更新成功");
        } catch (SQLException e) {
            this.error("更新失败", e);
            return EasyResult.fail("更新失败:" + e.getMessage());
        }
    }
    
    public EasyResult actionForDeleteCost(){
        String costId = getJsonPara("costId");
        try {
            boolean isSettled = this.getQuery().queryForExist("SELECT COUNT(1) FROM yq_crm_cost WHERE COST_ID = ? AND SETTLEMENT_ID IS NOT NULL AND SETTLEMENT_ID != ''", costId);
            if(isSettled) {
                return EasyResult.fail("该成本记录已被结算，不允许删除");
            }
            
            this.getQuery().executeUpdate("DELETE FROM yq_crm_cost WHERE COST_ID = ?", costId);
            return EasyResult.ok("删除成功");
        } catch (SQLException e) {
            this.error("删除失败", e);
            return EasyResult.fail("删除失败:" + e.getMessage());
        }
    }

    private EasyRecord getSettleModel(String prefix){
        EasyRecord model = new EasyRecord("yq_crm_settlement","SETTLEMENT_ID");
        model.setColumns(getJSONObject(prefix));
        return model;
    }

    public EasyResult actionForAddSettle(){
        EasyQuery query = this.getQuery();
        try {
            query.begin();
            
            EasyRecord model = getSettleModel("settle");
            String settlementId = RandomKit.uuid().toUpperCase();
            String settlementNo = "JS" + EasyDate.getCurrentDateString("yyyyMMddHHmmss");
            
            model.setPrimaryValues(settlementId);
            model.set("SETTLEMENT_NO", settlementNo);
            model.set("CREATE_TIME", EasyDate.getCurrentDateString());
            model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            model.set("CREATOR", getUserId());
            model.set("CREATE_NAME", getUserName());
            
            query.save(model);
            
            String costIds = model.getString("COST_IDS");
            String invoiceIds = model.getString("INVOICE_IDS");
            
            if(StringUtils.notBlank(costIds)) {
                String[] costIdArray = costIds.split(",");
                for(String costId : costIdArray) {
                    if(StringUtils.notBlank(costId)) {
                        query.executeUpdate("UPDATE yq_crm_cost SET SETTLEMENT_ID = ?, SETTLEMENT_NO = ? WHERE COST_ID = ?", 
                                          settlementId, settlementNo, costId.trim());
                    }
                }
            }
            
            if(StringUtils.notBlank(invoiceIds)) {
                String[] invoiceIdArray = invoiceIds.split(",");
                for(String invoiceId : invoiceIdArray) {
                    if(StringUtils.notBlank(invoiceId)) {
                        query.executeUpdate("UPDATE yq_crm_invoice SET SETTLEMENT_ID = ?, SETTLEMENT_NO = ? WHERE invoice_id = ?", 
                                          settlementId, settlementNo, invoiceId.trim());
                    }
                }
            }
            
            query.commit();
            return EasyResult.ok("保存成功");
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                this.error("回滚失败", ex);
            }
            this.error("保存失败", e);
            return EasyResult.fail("保存失败:" + e.getMessage());
        }
    }
    
    public EasyResult actionForUpdateSettle(){
        EasyQuery query = this.getQuery();
        try {
            query.begin();
            
            EasyRecord model = getSettleModel("settle");
            String settlementId = model.getString("SETTLEMENT_ID");
            String settlementNo = model.getString("SETTLEMENT_NO");
            
            query.executeUpdate("UPDATE yq_crm_cost SET SETTLEMENT_ID = NULL, SETTLEMENT_NO = NULL WHERE SETTLEMENT_ID = ?", settlementId);
            query.executeUpdate("UPDATE yq_crm_invoice SET SETTLEMENT_ID = NULL, SETTLEMENT_NO = NULL WHERE SETTLEMENT_ID = ?", settlementId);
            
            model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
            query.update(model);
            
            String costIds = model.getString("COST_IDS");
            String invoiceIds = model.getString("INVOICE_IDS");
            
            if(StringUtils.notBlank(costIds)) {
                String[] costIdArray = costIds.split(",");
                for(String costId : costIdArray) {
                    if(StringUtils.notBlank(costId)) {
                        query.executeUpdate("UPDATE yq_crm_cost SET SETTLEMENT_ID = ?, SETTLEMENT_NO = ? WHERE COST_ID = ?", 
                                          settlementId, settlementNo, costId.trim());
                    }
                }
            }
            
            if(StringUtils.notBlank(invoiceIds)) {
                String[] invoiceIdArray = invoiceIds.split(",");
                for(String invoiceId : invoiceIdArray) {
                    if(StringUtils.notBlank(invoiceId)) {
                        query.executeUpdate("UPDATE yq_crm_invoice SET SETTLEMENT_ID = ?, SETTLEMENT_NO = ? WHERE invoice_id = ?", 
                                          settlementId, settlementNo, invoiceId.trim());
                    }
                }
            }
            
            query.commit();
            return EasyResult.ok("更新成功");
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                this.error("回滚失败", ex);
            }
            this.error("更新失败", e);
            return EasyResult.fail("更新失败:" + e.getMessage());
        }
    }
    
    public EasyResult actionForDeleteSettle(){
        String settlementId = getJsonPara("settlementId");
        EasyQuery query = this.getQuery();
        try {
            query.begin();
            
            query.executeUpdate("UPDATE yq_crm_cost SET SETTLEMENT_ID = NULL, SETTLEMENT_NO = NULL WHERE SETTLEMENT_ID = ?", settlementId);
            query.executeUpdate("UPDATE yq_crm_invoice SET SETTLEMENT_ID = NULL, SETTLEMENT_NO = NULL WHERE SETTLEMENT_ID = ?", settlementId);
            
            query.executeUpdate("DELETE FROM yq_crm_settlement WHERE SETTLEMENT_ID = ?", settlementId);
            
            query.commit();
            return EasyResult.ok("删除成功");
        } catch (SQLException e) {
            try {
                query.roolback();
            } catch (SQLException ex) {
                this.error("回滚失败", ex);
            }
            this.error("删除失败", e);
            return EasyResult.fail("删除失败:" + e.getMessage());
        }
    }
}
