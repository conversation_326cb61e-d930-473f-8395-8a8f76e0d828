<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>数电发票查询</title>
	<style>
		.layui-badge{left: -1px!important;}
		.layui-table-cell{padding: 0 6px;}
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataForm">
			<input name="type" type="hidden" value="9">
			<div class="ibox">
				<div class="ibox-title clearfix">
					 <div class="layui-tab layui-tab-brief" lay-filter="stateFilter" style="margin: 0px;margin-bottom: -8px;">
          		 	 		<ul class="layui-tab-title"><li data-val="1" class="layui-this">已报销</li><li data-val="0">待报销</li></ul>
          		 	 </div>
	          		 <div class="form-group mt-15">
	          		 	<div class="input-group input-group-sm">
							 <span class="input-group-addon">上传日期</span>	
							 <input type="text" name="beginDate" data-mars="CommonDao.threeMonthBefore" style="width: 90px;" data-laydate="{type:'date'}" class="form-control input-sm">
							 <input type="text" name="endDate" style="width: 90px;" data-laydate="{type:'date'}" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm" style="width: 170px;">
					 		<span class="input-group-addon">发票类型</span>
						 	<select class="form-control input-sm" name="invoiceType" onchange="InvoiceMgr.query();">
						 		<option value="">--</option>
						 		<option value="1001" data-class="label label-info">数电_普通发票</option>
						 		<option value="2001" data-class="label label-success">数电_专用发票</option>
						 		<option value="1002" data-class="label label-success">电子_普通发票</option>
						 		<option value="2002" data-class="label label-success">电子_专用发票</option>
						 	</select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">同步XML</span>
						 	<select class="form-control input-sm" name="xmlSynState" onchange="InvoiceMgr.query();">
						 		<option value="">--</option>
						 		<option value="0">待同步</option>
						 		<option value="1">已同步</option>
						 	</select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 150px;">
					 		<span class="input-group-addon">发票号</span>
						 	<input class="form-control input-sm" name="invoiceNo">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">开票单位</span>
						 	<input class="form-control input-sm" name="saleCompany">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">报销人</span>
						 	<input class="form-control input-sm" type="hidden" name="bxUserId">
						 	<input class="form-control input-sm" name="bxUserName" onclick="singleUser(this);" data-fn="InvoiceMgr.query">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;display: none;">
					 		<span class="input-group-addon">报销状态</span>
						 	<select class="form-control input-sm" name="bxState" onchange="InvoiceMgr.query();">
						 		<option value="">--</option>
						 		<option value="1" data-class="label label-success" selected="selected">已报销</option>
						 		<option value="0" data-class="label label-info">待报销</option>
						 	</select>
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="InvoiceMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						<table class="layui-hide" id="InvoiceMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar">
				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="InvoiceMgr.edit">查看</a>
		</script>
		<script type="text/x-jsrender" id="btnBar">
			<a class="btn btn-info btn-sm" lay-event="InvoiceMgr.exportExcelData">导出EXCEL</a>
			<a class="btn btn-warning btn-sm ml-10" lay-event="InvoiceMgr.exportFileData">批量下载发票</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var InvoiceMgr={
			init:function(){
				$("#dataForm").initTable({
					mars:'BxInvoiceDao.bxInvoiceList',
					id:'InvoiceMgrTable',
					height:'full-100',
					limit:30,
					toolbar:'#btnBar',
					totalRow:true,
					autoSort:false,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
						 type:'checkbox'
					 },{
						title: '操作',
						align:'center',
						width:80,
						templet:function(row){
						    return renderTpl('bar',row);
						}
					},{
						 field:'BX_STATE',
						 title:'报销状态',
						 width:80,
						 templet:function(row){
							 if(row.BX_STATE=='0'){
								 return getText(row.BX_STATE,'bxState');
							 }else{
								var businessId = row.BUSINESS_ID;
								return "<a href='${ctxPath}/web/flow?handle=detail&businessId="+businessId+"' target='_blank'>查看报销单</a>";								 
							 }
						 }
					 },{
					     field: 'CREATE_NAME',
						 title: '导入人',
						 align:'center',
						 width:70
					},{
						 field:'XML_FILE_ID',
						 title:'数电XML',
						 width:80,
						 templet:function(row){
							 var invoiceNo = row.INVOICE_NO;
							 if(invoiceNo.length<20){
								 return '--';
							 }
							 if(row.XML_FILE_ID==''){
								 return '待同步';
							 }else{
								var xmlFileId = row.XML_FILE_ID;
								return "<a style='color:#1e9fff;' href='/yq-work/fileview/"+xmlFileId+"' target='_blank'>下载</a>";								 
							 }
						 }
					 },{
					    field: 'USE_TIME',
						title: '报销时间',
						align:'left',
						width:140
					},{
					    field: 'INVOICE_NO',
						title: '发票号',
						align:'left',
						width:160,
						totalRowText:'统计'
					},{
					    field: 'TOTAL_AMOUNT',
						title: '含税金额',
						align:'left',
						width:90,
						totalRow:true
					},{
						field:'AMOUNT_IN_FIGUERS',
						title:'不含税',
						width:90,
						totalRow:true
					},{
						field:'TOTAL_TAX',
						title:'税额',
						width:90,
						totalRow:true
					},{
						field:'TAX_RATE',
						title:'税率',
						width:90,
						templet:function(row){
							if(row.TAX_RATE<1){
								return row.TAX_RATE*100+"%";
							}
							return row.TAX_RATE;
						}
					},{
					    field: 'INVOICE_DATE',
						title: '开票日期',
						align:'center',
						width:110
					},{
					    field: 'INVOICE_TYPE',
						title: '开票类型',
						width:110
					},{
					    field: 'SALE_COMPANY',
						title: '开票方',
						width:120
					},{
					    field: 'BUY_COMPANY',
						title: '购买方',
						width:120
					},{
					    field: 'KP_REMARK',
						title: '开票备注',
						minWidth:120
					},{
					    field: 'FILE_NAME',
						title: '发票文件',
						width:160
					},{
					    field: 'CREATE_TIME',
						title: '导入时间',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#dataForm").queryData({id:'InvoiceMgrTable',jumpOne:true});
			},
			edit:function(data){
				var  invoiceId = data['INVOICE_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/flow/bx/invoice/invoice-edit.jsp',title:'查看发票',data:{invoiceId:invoiceId,op:'detail'}});
			},
			exportExcelData:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择要下载的记录。',{icon : 7, time : 1000});
					return;
				}
				var arrayId = [];
				for(var index in dataList){
					arrayId.push(dataList[index]['INVOICE_ID']);
				}
				var data = form.getJSONObject('#dataForm');
				data['invoiceIds'] = arrayId.join(',');
				layer.msg('正在导出',{time:500});
				window.open('${ctxPath}/servlet/bx/fun?action=exportBxInvoiceExcel&data='+encodeURI(JSON.stringify(data)));
			},
			exportFileData:function(dataList){
				if(dataList.length == 0){
					layer.msg('请选择要下载的发票。',{icon : 7, time : 1000});
					return;
				}
				var arrayId = [];
				for(var index in dataList){
					arrayId.push(dataList[index]['INVOICE_ID']);
				}
				var data = form.getJSONObject('#dataForm');
				data['invoiceIds'] = arrayId.join(',');
				layer.msg('正在导出',{time:500});
				window.open('${ctxPath}/servlet/bx/fun?action=exportBxInvoiceFile&data='+encodeURI(JSON.stringify(data)));
			}
		}
		
		function reloadInvoiceList(){
			InvoiceMgr.query();
		}
		
		$(function(){
			layui.element.on('tab(stateFilter)', function(){
				$("[name='bxState']").val(this.getAttribute('data-val'));
				reloadInvoiceList();
			});
			
			$("#dataForm").render({success:function(){
				InvoiceMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>