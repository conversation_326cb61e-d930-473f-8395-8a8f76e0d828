<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>
		.layui-table-cell{padding: 0 6px;}
		[data-week-no]{color: #999;cursor: pointer;}
		.weekActive{font-size: 16px;color: #e32424;}
		.layui-timeline-item{cursor: pointer;}
		.layui-timeline-item:hover .layui-timeline-title{color: #1E9FFF;}
		.layui-timeline-title{font-size: 16px!important;color: #916f6f;}
		.layui-timeline-content p{font-size: 13px;line-height: 24px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="weeklyForm" data-page-hide="true">
			<div class="ibox">
					<div class="ibox-content weeklyTitle">
				    	<input type="text" style="width: 130px" name="userName" class="form-control input-sm mb-10" placeholder="姓名"/>
				    	<input type="text" style="width: 130px;" name="depts" class="form-control input-sm mb-10" placeholder="部门"/>
				    	<button type="button" class="btn btn-sm btn-default mb-10" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					  	<div style="margin-bottom: 0px;">
				    		<div class="pl-10" data-mars="WeeklyDao.getRecentWeek" data-template="getRecentWeek" style="float:left;vertical-align:super;display:inline;height: 40px;line-height: 40px;"></div>
					  		<div class="mt-5" style="display: inline-block;">
						  		<select name="year" onchange="yearChange(this.value)" class="form-control input-sm">
				    				<option value="2021">2021年</option>
				    				<option value="2020">2020年</option>
				    				<option value="2019">2019年</option>
						    	</select>
						    	<select name="weekNo" onchange="getWeeklyTitle(this.value)" class="form-control input-sm">
						    		<option value="">所有周数</option>
						    		<c:forEach begin="1" step="1" end="52" var="item">
						    			<option value="${item }">第${item }周</option>
						    		</c:forEach>
						    	</select>
					  		</div>
							<div  style="float: right;margin-top: 10px;" id="weeklyTitle"></div>
					  	</div>
					</div>
					<div class="ibox-content mt-10">
					  	<div class="layui-tab layui-tab-brief">
						  <ul class="layui-tab-title">
						    <li  class="layui-this">表格视图</li>
						    <!-- <li>列表视图</li> -->
						    <EasyTag:hasRole roleId="DEV_MGR">
							    <li>部门提交情况</li>
						    </EasyTag:hasRole>
						  </ul>
						  <div class="layui-tab-content">
						  	 <div class="layui-tab-item layui-show">
								<table class="layui-hide mt-15" id="recentListTable"></table>
						  	 </div>
   							<%--  <div class="layui-tab-item">
								<div data-mars="WeeklyDao.myReceiveWeeklyList" data-template="tpl" id="tplMars" data-container="tplContainer"></div>
								<div id="tplContainer"></div>
								<div class="row paginate mt-10">
	               			 		<jsp:include page="/pages/common/pagination.jsp"/>
	           			 		</div> 
   							 </div> --%>
						    <EasyTag:hasRole roleId="DEV_MGR">
   							  <div class="layui-tab-item">
								<div id="cardList" data-mars="WeeklyDao.queryDeptWeekly" data-template="queryDeptWeeklyTpl" data-container="deptWeeklyTbody">
									<table class="layui-table">
									  <thead>
									    <tr>
									      <th>序号</th>
									      <th>姓名</th>
									      <th>周报名称</th>
									      <th>饱和度</th>
									      <th>本周评分</th>
									      <th>提交状态</th>
									    </tr> 
									  </thead>
									   <tbody id="deptWeeklyTbody">
									   
									   </tbody>
									</table>
								</div>
						  	 </div>
						  	 </EasyTag:hasRole>
						  </div>
						</div>  
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="queryDeptWeeklyTpl">
			{{for data}}
				<tr>
    			  <td>{{:#index+1}}</td>
      			 <td>{{:USERNAME}}</td>
     			 {{call:#parent.parent.data.weeklys USER_ID fn='isFill'}}
   				 </tr>
			{{/for}}
		</script>
		<script type="text/x-jsrender" id="getRecentWeek">
			<span data-title="" data-week-no="" class="mr-20">全部</span>
			{{for data}}
				{{if #index==0}}<input type="hidden" id="currNo" value="{{:currentWeekNo}}"/>{{/if}}
				<span data-title="{{:TITLE}}" data-week-no="{{:WEEK_NO}}" class="mr-20 {{if currentWeekNo==WEEK_NO}}weekActive{{/if}}">第{{:WEEK_NO}}周</span>
			{{/for}}
		</script>
		<script type="text/x-jsrender" id="tpl">
			{{if list.length==0}}暂无数据{{/if}}
			<ul class="layui-timeline mt-15">
			{{for list}}
			  <li class="layui-timeline-item" onclick="list.detail('{{:WEEKLY_ID}}')">
				<i class="layui-icon layui-timeline-axis">&#xe63f;</i>
				<div class="layui-timeline-content layui-text">
				  <h3 class="layui-timeline-title">{{if TYPE!=1}}<span class="label label-success label-outline mr-10">抄送</span>{{/if}}<span class="layui-icon layui-icon-username"></span> {{:DEPTS}}.{{call: CREATOR fn='getUserName'}}<i class="layui-icon layui-icon-form ml-15"></i> {{:TITLE}}<i class="layui-icon layui-icon-time ml-20"> </i>{{:SEND_TIME}}</h3>
				  <p><span class="label label-success label-outline">本周工作</span><br>{{:ITEM_1}}</p>
				  <p><span class="label label-info label-outline">下周计划</span><br>{{:ITEM_2}}</p>
				  {{if ITEM_3}}<p><span class="label label-warning label-outline">反馈</span><br>{{:ITEM_3}}</p>{{/if}}
				  {{if ITEM_4}}<p><span class="label label-danger label-outline">备注</span><br>{{:ITEM_4}}</p>{{/if}}
				</div>
			  </li>
			  {{/for}}
			  
			</ul>
		
			
		</script>
		<script type="text/x-jsrender" id="bar">
			{{if RECEIVER == currentUserId && STATUS == 1}}
  				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="list.detail">审批</a>
			{{else}}
				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="list.detail">查看</a>
			{{/if}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		layui.use('element', function(){
			  var element = layui.element;
			  element.render();
		});
		function isFill(data,userId){
			var  td ="";
			var html="<td>";
			var tempHtml="<td>"
			var weekNo=$("[name='weekNo']").val();
			var thisData={};
			if(weekNo){
				for(var index in data){
					if(data[index]['CREATOR']==userId){
						thisData=data[index];
						td = data[index]['SEND_TIME']+' <label class="label label-success label-outline">已提交</label>';
					}
				}
				if(td){
					
				}else{
					td = '<label class="label label-danger">未提交</label><a href="javascript:;" onclick="noticeWriteWeekly(\''+userId+'\')" class="btn btn-xs btn-link">提醒</a>';
				}
				
			}else{
				td = '请选择周';
			}
			html+=td;
			html+="</td>";
			if(thisData['TITLE']){
				tempHtml+="<a href='javascript:;' class='btn btn-xs f-13 btn-link' onclick=\"list.detail('"+thisData['WEEKLY_ID']+"')\">"+thisData['TITLE']+"</a>";
			}else{
				tempHtml+="&nbsp;";
			}
			tempHtml+="</td>";
			
			tempHtml+="<td>";
			if(thisData['TITLE']){
				tempHtml+=thisData['ITEM_5'];
			}
			tempHtml+="</td>";
			tempHtml+="<td>";
			if(thisData['GRADE']){
				var gradeJson = {0:'D(不合格)',1:'C(及格)',2:'B(较好)',3:'A(优秀)'};
				tempHtml+=gradeJson[thisData['GRADE']];
			}
			tempHtml+="</td>";
			
			return tempHtml+html;
			
		}
		function noticeWriteWeekly(userId){
			var data = $.extend({userId:userId},form.getJSONObject("#weeklyForm"));
			ajax.remoteCall("${ctxPath}/servlet/weekly?action=noticeWriteWeekly",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		function loadRecentWeekList(isInit){
			$(".weeklyTitle").render({success:function(){
				$("[name='weekNo']").val($("#currNo").val());
				$("[data-week-no]").click(function(){
					var t =$(this);
					t.siblings().removeClass("weekActive");
					t.addClass("weekActive");
					$("#weeklyTitle").html(t.data("title"));
					$("[name='weekNo']").val(t.data("weekNo"));
					list.query();
				});
				$("#weeklyTitle").html($(".weekActive").data("title"));
				$(".layui-tab-content").render({data:form.getJSONObject("#weeklyForm")});
				if(isInit){
					list.recentList();
				}
			}});
		}
		var list={
			recentList:function(){
				$("#weeklyForm").initTable({
					mars:'WeeklyDao.myReceiveWeeklyList',
					id:'recentListTable',
					height:'full-200',
					cellMinWidth:100,
					limit:30,
					cols: [[
		             {
						title: '序号',
						type:'numbers',
						align:'center'
					 },{
					    field: 'CREATOR',
						title: '填写人',
						align:'left',
						width:160,
						templet:function(row){
							return row.DEPTS+"."+getUserName(row.CREATOR);
						}
					   }
					 ,{
					    field: 'TITLE',
						title: '标题',
						minWidth:250,
						align:'left',
						event:'list.detail',
						style:'color:#1E9FFF;cursor:pointer',
						templet:function(row){
							if(row.TYPE=='1'){
								return row.TITLE;
							}else{
								return "<span class='layui-badge-rim'>抄送</span>&nbsp;"+row.TITLE;
							}
						}
					},{
						title: '工作量',
						field:'ITEM_5',
						align:'left',
						width:90,
					 }
					 ,{
						    field: 'RECEIVER',
							title: '审批人/抄送人',
							align:'left',
							width:140,
							templet:function(row){
								if(row.TYPE=='1'){
									return getUserName(row.RECEIVER);
								}else{
									return row.DEPTS+"."+row.USERNAME;
								}
							}
						 }
					,{
					    field: 'SEND_TIME',
						title: '收到时间',
						align:'left',
						width:200,
						templet:function(row){
							if(getCurrentUserId()==row.RECEIVER||isSuperUser){
								var status=row.STATUS;
								if(status==2){
									status='<span class="layui-badge layui-bg-green">已阅</span> ';
								}else{
									status='<span class="layui-badge">待阅</span> ';
								}
								return status+row.SEND_TIME;
							}else{
								return row.SEND_TIME;
							}
						}
					},{
						title: '操作',
						align:'left',
						width:90,
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							return renderTpl('bar',row);
						}
					}
					]],done:function(result){
						$("#num").text(result['totalRow']);
						
					}}
				);
			},
			query:function(){
				$(".layui-tab-content").render({data:form.getJSONObject("#weeklyForm")});
				$("#weeklyForm").queryData({id:'recentListTable',jumpOne:true});
				/* $("#weeklyForm").searchData({el:$("#tplMars"),success:function(){
					layui.use('element', function(){
						  var element = layui.element;
						  element.render();
					});
				}}); */
			},
			detail:function(data){
				var width=$(window).width();
				var fullFlag=false;
				var width ='850px';
				if(width<700){
					fullFlag=true;
				}
				if(width>1900){
					width='70%';
				}
				var weeklyId = data;
				if(data.WEEKLY_ID){
					weeklyId=data.WEEKLY_ID;
				}
				popup.layerShow({type:1,full:fullFlag,shade: 0.1,shadeClose:true,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/weekly/weekly-detail.jsp',title:'周报详情',data:{weeklyId:weeklyId}});
			}
		}
		
		function yearChange(val){
			loadRecentWeekList();
			list.query();
		}
		function getWeeklyTitle(val){
			var len = $("[data-week-no='"+val+"']").length;
			if(len==0){
				$(".weekActive").removeClass("weekActive");
			}
			if(val){
				$("#weeklyTitle").html("第"+val+"周");
			}else{
				$("#weeklyTitle").html("全部");
			}
			list.query();
		}
		$(function(){
			loadRecentWeekList(true);
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>