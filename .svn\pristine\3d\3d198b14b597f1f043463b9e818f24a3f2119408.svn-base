package com.yunqu.work.servlet;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.service.XuanService;
import com.yunqu.work.service.ZendaoService;

@WebServlet("/servlet/ehr/*")
public class EhrServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public void actionForSyn(){
		String sql="select t1.id,t2.USER_ID from tbl_people t1 INNER JOIN yq_main.easi_user t2 on t2.USERNAME= t1.realname";
		try {
			List<JSONObject> list=this.getQuery().queryForList(sql, null, new JSONMapperImpl());
			for(JSONObject jsonObject:list){
				sql="update dc_reserve_list set user_id = ?,create_by = ? where user_id = ?";
				this.getQuery().executeUpdate(sql, jsonObject.getString("USER_ID"),jsonObject.getString("USER_ID"),jsonObject.getString("ID"));
				System.out.println("id:"+jsonObject.getString("USER_ID")+"更新成功");
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		renderText("OK");
	}
	public void actionForZendao(){
		ZendaoService.getService().run();
	    renderText("ok");
	}
	public void actionForXuan(){
		XuanService.getService().run();
		renderText("ok");
	}
	
	
}
