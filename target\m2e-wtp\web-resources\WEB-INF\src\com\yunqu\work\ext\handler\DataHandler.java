package com.yunqu.work.ext.handler;

import java.util.Iterator;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.sso.UserPrincipal;

import com.jfinal.handler.Handler;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;
/**
 * 过滤请求
 *
 */
public class DataHandler extends Handler {
	
	public void handle(String target, HttpServletRequest request, HttpServletResponse response, boolean[] isHandled) {
		if (target.indexOf(".jsp")>-1||target.indexOf("web/flow")>-1) {
			Map<?, ?> pmap = request.getParameterMap();
			Iterator<?> it = pmap.keySet().iterator();
			while (it.hasNext()){
			    String key = it.next().toString();
			    String[] values = (String[])pmap.get(key);
			    if(values!=null&&values.length>0){
			    	request.setAttribute(key,values[0]);
			    }
			}
			UserPrincipal principal = (UserPrincipal)request.getUserPrincipal();
			request.setAttribute("isLogin", principal!=null);
			request.setAttribute("principal", principal);
			if(principal!=null) {
				String userId = principal.getUserId();
				Object obj = principal.getAttribute("staffInfo");
				StaffModel staffInfo =  null;
				if(obj!=null&&obj instanceof StaffModel) {
					staffInfo = (StaffModel)obj;
				}else {
					staffInfo = StaffService.getService().getStaffInfo(userId);
					principal.setAttribute("staffInfo", staffInfo);
				}
				request.setAttribute("staffInfo", staffInfo);
			}
		}
		next.handle(target, request, response, isHandled);
	}
}
 