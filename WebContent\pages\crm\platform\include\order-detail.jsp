
<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>订购详情</title>
    <style>
        .form-horizontal {
            width: 100%;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="orderDetailForm" class="form-horizontal" data-mars="PlatformOrderDao.orderRecord" data-text-model="true" autocomplete="off" data-mars-prefix="order.">
        <input type="hidden" value="${param.orderId}" name="order.ORDER_ID"/>
        <input type="hidden" value="${param.orderId}" name="platformOrderId"/>
        <input type="hidden" value="${param.orderId}" name="orderId"/>
        <table class="table table-vzebra">
            <tbody>
            <tr>
                <td style="width: 160px">客户名称</td>
                <td>
                    <input type="text" name="order.CUST_NAME" class="form-control input-sm" readonly>
                </td>
                <td style="width: 160px">销售</td>
                <td>
                    <input type="text" name="order.SALES_BY_NAME" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>合同名称</td>
                <td>
                    <input type="text" name="order.CONTRACT_NAME" class="form-control input-sm" readonly>
                </td>
                <td>商机名称</td>
                <td>
                    <input type="text" name="order.BUSINESS_NAME" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>订购状态</td>
                <td>
                    <span id="orderStatusText"></span>
                </td>
                <td>订购金额</td>
                <td>
                    <input type="text" name="order.ORDER_AMOUNT" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>开始日期</td>
                <td>
                    <input type="text" name="order.START_DATE" class="form-control input-sm" readonly>
                </td>
                <td>结束日期</td>
                <td>
                    <input type="text" name="order.END_DATE" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>创建时间</td>
                <td>
                    <input type="text" name="order.CREATE_TIME" class="form-control input-sm" readonly>
                </td>
                <td>更新时间</td>
                <td>
                    <input type="text" name="order.UPDATE_TIME" class="form-control input-sm" readonly>
                </td>
            </tr>
            <tr>
                <td>销售价格</td>
                <td colspan="3">
                    <table id="orderPriceTable"></table>
                </td>
            </tr>
            <tr>
                <td>相关成本</td>
                <td colspan="3">
                    <table id="orderCostTable"></table>
                </td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <textarea name="order.REMARK" class="form-control input-sm" rows="3" readonly></textarea>
                </td>
            </tr>
            </tbody>
        </table>
        <br>
        <p class="layer-foot text-c">
            <button type="button" title="关闭" class="btn btn-default btn-sm ml-15" style="width: 80px" onclick="layer.closeAll();"> 关闭</button>
        </p>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        $(function () {
            $("#orderDetailForm").render({
                success: function (result) {
                    var record = result && result['PlatformOrderDao.orderRecord'];
                    var data = record && record.data;
                    if(data){
                        var orderStatus = data['ORDER_STATUS'];
                        var statusText = ['正常','暂停','终止'][orderStatus] || '未知';
                        $('#orderStatusText').text(statusText);
                    }

                    orderPriceList.init();
                    orderCostList.init();
                }
            });
        });

        var orderPriceList = {
            init: function () {
                $("#orderDetailForm").initTable({
                    mars: 'CustOperateDao.salePriceList',
                    id: 'orderPriceTable',
                    page: false,
                    cols: [[
                        {
                            field: 'PRODUCT_NAME',
                            title: '产品名称',
                            width: 150,
                            align: 'left'
                        },{
                            field: 'PRICE_TYPE',
                            title: '价格类型',
                            width: 100,
                            align: 'center'
                        }, {
                            field: 'PRICE_UNIT',
                            title: '价格单位',
                            width: 100,
                            align: 'center'
                        }, {
                            field: 'SALES_PRICE',
                            title: '销售价格',
                            width: 110,
                            align: 'right'
                        }, {
                            field: 'SALE_TAX_RATE',
                            title: '销售税率',
                            width: 110,
                            align: 'right',
                            templet: function(d){
                                return d.SALE_TAX_RATE + '%';
                            }
                        }, {
                            field: 'REMARK',
                            title: '备注',
                            minWidth: 80,
                            align: 'left'
                        }
                    ]]
                });
            }
        }

        var orderCostList = {
            init: function () {
                $("#orderDetailForm").initTable({
                    mars: 'CustOperateDao.orderCostList',
                    id: 'orderCostTable',
                    page: false,
                    cols: [[
                        {
                            field: 'COST_NAME',
                            title: '成本名称',
                            width: 150,
                            align: 'left'
                        },{
                            field: 'DATE_ID',
                            title: '日期',
                            width: 100,
                            align: 'center'
                        }, {
                            field: 'COST_TYPE',
                            title: '成本类型',
                            width: 100,
                            align: 'center'
                        }, {
                            field: 'TOTAL_AMOUNT',
                            title: '成本金额(含税)',
                            width: 110,
                            align: 'right'
                        }, {
                            field: 'NO_TAX_AMOUNT',
                            title: '成本金额(去税)',
                            width: 110,
                            align: 'right'
                        }, {
                            field: 'REMARK',
                            title: '备注',
                            minWidth: 80,
                            align: 'left'
                        }
                    ]]
                });
            }
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
