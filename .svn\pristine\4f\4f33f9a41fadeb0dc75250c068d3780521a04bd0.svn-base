<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<table class="layui-hide" id="weekly"></table>
<script type="text/javascript">
	function reloadProjectWeekly(){
		$("#projectDetail").queryData({id:'weekly'});
	}
	function loadWeekly(){
		$("#projectDetail").initTable({
			mars:'ProjectDao.projectWeeklys',
			id:"weekly",
			data:{},
			cols: [[
			 {type:'checkbox'},
	         {
				title: '序号',
				type:'numbers'
			 },{
			    field: 'USERNAME',
				title: '填写人',
				width:90,
				align:'left'
			},{
			    field: 'DEPTS',
				title: '所属部门',
				width:90,
				align:'left'
			},{
			    field: 'CREATE_TIME',
				title: '填写时间',
				width:160,
				align:'left'
			},{
			    field: 'TITLE',
				title: '周报名称',
				align:'left'//,
			//	event:'detailWeekly',
			//	style:'color:#1E9FFF;cursor:pointer'
			},{
			    field: '',
				title: '操作',
				align:'left',
				width:80,
				templet:function(row){
					var creator = row['CREATOR'];
					if(getCurrentUserId()==creator){
						return '<a href="javascript:;" onclick="editWeekly(\''+row['WEEKLY_ID']+'\')" class="layui-text">修改</a>';
					}else{
						return '';
					}
				}
			}
			]],done:function(result){
				table.resize('weekly');
				$(".n1").text(result.totalRow);
			}});
		
	}
	
	function addWeekly(){
 		var projectName=$("[name='project.PROJECT_NAME']").text();
	    popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/weekly/project-weekly-edit.jsp',title:'新增项目周报',data:{projectId:'${param.projectId}',projectName:projectName}});
 	}
 	function editWeekly(data){
 		var weeklyId = data['WEEKLY_ID'];
 		if(!weeklyId){
 			weeklyId=data;
 		}
	    popup.layerShow({type:1,maxmin:true,anim:0,scrollbar:false,offset:'r',area:['850px','100%'],url:'${ctxPath}/pages/weekly/project-weekly-edit.jsp',title:'修改项目周报',data:{projectId:'${param.projectId}',weeklyId:weeklyId}});
 	}
 	function detailWeekly(data){
 		
 	}
</script>