package com.yunqu.work.dao.ehr;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.Constants;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;

@WebObject(name = "HrStatisticDao")
public class HrStatisticDao extends AppDaoContext {
    @WebControl(name = "hrConsoleStat", type = Types.RECORD)
    public JSONObject hrConsoleStat() {
        EasySQL sql = new EasySQL("select COUNT(DISTINCT CASE WHEN t1.STATE = 0 THEN USER_ID END) as TOTAL_AMOUNT_NOW,");
        sql.append("COUNT(DISTINCT CASE WHEN t1.STATE = 0 AND EMPLOYMENT_STATE = 1 THEN USER_ID END) as TOTAL_AMOUNT_ZS,");
        sql.appendLike(DateUtils.getFullMonthStr(), "COUNT(DISTINCT CASE WHEN join_date like ? THEN USER_ID END) as JOIN_AMOUNT_THIS_MONTH,");
        sql.appendLike(DateUtils.getFullMonthStr(), "COUNT(DISTINCT CASE WHEN leave_date like ? THEN USER_ID END) as LEAVE_AMOUNT_THIS_MONTH,");
        sql.appendLike(EasyCalendar.newInstance().getYear(), "COUNT(DISTINCT CASE WHEN join_date like ? THEN USER_ID END) as JOIN_AMOUNT_THIS_YEAR,");
        sql.appendLike(EasyCalendar.newInstance().getYear(), "COUNT(DISTINCT CASE WHEN leave_date like ? THEN USER_ID END) as LEAVE_AMOUNT_THIS_YEAR,");
        sql.append("ROUND(AVG(CASE WHEN t1.STATE = 0 THEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) ELSE NULL END),1) AS AVERAGE_AGE_TOP,");
        sql.append("ROUND(AVG(CASE WHEN t1.STATE = 0 THEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) ELSE NULL END),1) AS AVERAGE_JOIN_TOP,");
        //环比去年直至今日的数据
        String lastYearBegin = DateUtils.getQuarterStart(EasyCalendar.newInstance().getYear() - 1, 1);
        String lastYearToday = DateUtils.getLastYearTodayDate();
        sql.append(lastYearBegin, "COUNT(DISTINCT CASE WHEN leave_date >= ?");
        sql.append(lastYearToday, "and leave_date <= ? THEN USER_ID END) as LEAVE_AMOUNT_LAST_YEAR_TODAY,");
        sql.append(lastYearBegin, "COUNT(DISTINCT CASE WHEN join_date >= ?");
        sql.append(lastYearToday, "and join_date <= ? THEN USER_ID END) as JOIN_AMOUNT_LAST_YEAR_TODAY");
        sql.append("from " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id ");
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    /**
     * 离职、入职、在职人数按月统计
     */
    @WebControl(name = "staffAmountMonthlyStat", type = Types.LIST)
    public JSONObject staffAmountMonthlyStat() {
        String startMonth = param.getString("startMonth");
        Integer monthPeriod = param.getIntValue("monthPeriod");
        monthPeriod = monthPeriod == 0 ? 12 : monthPeriod;
        if (StringUtils.isBlank(startMonth)) {
            startMonth = DateUtils.getFullMonthStr();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();

        String[] fields = {"leave_date", "join_date"};
        //全部员工、正式员工、其他员工
        String[] employmentName = {"_SUM", "_1", "_2"};
        String[] employmentState = {"", "and employment_state = '1'", "and employment_state != '1'"};
        JSONObject result = new JSONObject();
        for (String field : fields) {
            for (int i = 0; i < employmentName.length; i++) {
                try {
                    calendar.setTime(sdf.parse(startMonth));
                } catch (ParseException e) {
                    return EasyResult.fail("日期格式错误");
                }
                EasySQL sql = new EasySQL("select ");
                for (int j = 1; j <= monthPeriod; j++) {
                    String month = sdf.format(calendar.getTime());
                    String monthName = month.replace("-", "_");
                    sql.appendLike(month, "COUNT(DISTINCT CASE WHEN " + field + " like ? " + employmentState[i] + " THEN USER_ID END)");
                    if (j == monthPeriod) {
                        sql.append("AS " + monthName);
                    } else {
                        sql.append("AS " + monthName + ",");
                    }
                    calendar.add(Calendar.MONTH, 1);
                }
                sql.append("from " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id ");
                sql.append("where 1=1");
                JSONObject resultTemp = queryForRecord(sql.getSQL(), sql.getParams());
                JSONObject data = resultTemp.getJSONObject("data");
                result.put(field + employmentName[i], data);
            }
        }

        try {
            calendar.setTime(sdf.parse(startMonth));
        } catch (ParseException e) {
            return EasyResult.fail("日期格式错误");
        }
        EasySQL sql = new EasySQL("select ");
        for (int j = 1; j <= monthPeriod; j++) {
            String month = sdf.format(calendar.getTime());
            String monthFirstDay = month + "-01";
            String monthLastDay = month + "-31";
            String monthName = month.replace("-", "_");
            sql.append(monthLastDay,"count(DISTINCT CASE WHEN  (join_date <= ? ");
            sql.append(monthLastDay,"and leave_date >= ? ) ");
            sql.append("or");
            //排除掉leave_date is null但state=1已经离职的数据，
            sql.append(monthLastDay,"(join_date <= ? and (leave_date = '' or leave_date is null) and t1.state = 0)");
            //加入已经离职过，但是又被返聘的员工
            sql.append("or");
            sql.append(monthLastDay,"(t1.state = 0 and join_date <= ? ) ");
            sql.append("THEN USER_ID END)");

            if (j == monthPeriod) {
                sql.append("AS " + monthName);
            } else {
                sql.append("AS " + monthName + ",");
            }
            calendar.add(Calendar.MONTH, 1);
        }
        sql.append("from " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id ");
        sql.append("where 1=1");
        JSONObject resultTemp3 = queryForRecord(sql.getSQL(), sql.getParams());
        JSONObject data3 = resultTemp3.getJSONObject("data");
        result.put("在职", data3);

        return result;
    }


    @WebControl(name = "deptNameAmountRank", type = Types.LIST)
    public JSONObject deptNameAmountRank() {
        String startMonth = param.getString("startMonth2");
        Integer monthPeriod = param.getIntValue("monthPeriod2");
        monthPeriod = monthPeriod == 0 ? 12 : monthPeriod;
        if (StringUtils.isBlank(startMonth)) {
            startMonth = DateUtils.getFullMonthStr();
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(sdf.parse(startMonth));
        } catch (ParseException e) {
            return EasyResult.fail("日期格式错误");
        }
        String startDate = sdf.format(calendar.getTime()) + "-01";
        calendar.add(Calendar.MONTH, monthPeriod - 1);
        String endDate = sdf.format(calendar.getTime()) + "-31";

        String deptType = param.getString("deptType");

        EasySQL sql = getEasySQL("SELECT");
        if ("center".equals(deptType)) {
            sql.append("t3.DEPT_NAME as DEPT_CENTER,");
        } else {
            sql.append("t1.DEPTS as DEPT_CENTER,");
        }
        //全部员工
        sql.append(endDate, "COUNT(DISTINCT CASE WHEN  (join_date <= ? ");
        sql.append(endDate, "and leave_date >= ? ) ");
        sql.append("or");
        //排除掉leave_date is null但state=1已经离职的数据，
        sql.append(endDate,"(join_date <= ? and (leave_date = '' or leave_date is null) and t1.state = 0)");
        //加入已经离职过，但是又被返聘的员工
        sql.append("or");
        sql.append(endDate,"(t1.state = 0 and join_date <= ? ) ");
        sql.append("THEN USER_ID END) as STAFF_AMOUNT_SUM,");
       //全部离职、入职
        sql.append(startDate, "COUNT(DISTINCT CASE WHEN leave_date >= ? ");
        sql.append(endDate, "and leave_date <= ?  THEN USER_ID END) AS LEAVE_AMOUNT,");
        sql.append(startDate, "COUNT(DISTINCT CASE WHEN  join_date >= ? ");
        sql.append(endDate, "and join_date <= ? THEN USER_ID END)  AS JOIN_AMOUNT");
        sql.append("from " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id");
        if ("center".equals(deptType)) {
            sql.append("JOIN " + Constants.DS_MAIN_NAME + ".easi_dept t3 ON t2.p_dept_id = t3.DEPT_ID");
            sql.append("GROUP BY t3.DEPT_NAME");
        } else {
            sql.append("GROUP BY t1.DEPTS");
        }
        setOrderBy(sql, " order by STAFF_AMOUNT_SUM desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "eduRatioStat", type = Types.LIST)
    public JSONObject eduRatioStat() {
        EasySQL sql = getEasySQL("SELECT EDU, COUNT(USER_ID) as AMOUNT FROM " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id ");
        sql.append("WHERE t1.STATE = 0");
        sql.append("GROUP BY EDU");
        return queryForList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "allEduNames", type = Types.LIST)
    public  JSONObject allEduNames(){
        EasySQL sql1 = new EasySQL("SELECT DISTINCT EDU FROM " + Constants.DS_MAIN_NAME + ".easi_user ");
        sql1.append("WHERE EDU IS NOT NULL AND EDU <> ''");
        return queryForList(sql1.getSQL(), sql1.getParams());
    }

    @WebControl(name = "eduAmountRankByDept", type = Types.LIST)
    public JSONObject eduAmountRankByDept() {
        EasySQL sql1 = new EasySQL("select DISTINCT EDU FROM " + Constants.DS_MAIN_NAME + ".easi_user ");
        sql1.append("WHERE EDU IS NOT NULL AND EDU <> ''");
        JSONObject resultTemp = queryForList(sql1.getSQL(), sql1.getParams());
        JSONArray eduNames = resultTemp.getJSONArray("data");

        String[] eduStrings = new String[eduNames.size()];
        for (int i = 0; i < eduNames.size(); i++) {
            JSONObject obj = eduNames.getJSONObject(i);
            eduStrings[i] = obj.getString("EDU");
        }

        String deptType = param.getString("deptType");
        EasySQL sql = new EasySQL("SELECT");
        if ("center".equals(deptType)) {
            sql.append("t3.DEPT_NAME as DEPT_CENTER,");
        } else {
            sql.append("t1.DEPTS as DEPT_CENTER,");
        }
        sql.append("COUNT(USER_ID) AS STAFF_AMOUNT");
        for (String edu : eduStrings) {
            sql.append(", COUNT(DISTINCT CASE");
            sql.append(edu, "WHEN EDU = ? THEN USER_ID END) as 'EDU_" + edu + "'");
        }
        sql.append(", COUNT(DISTINCT CASE");
        sql.append("WHEN EDU = '' THEN USER_ID END) as 'EDU_其它'");
        sql.append(", ROUND(COUNT(DISTINCT CASE");
        sql.appendLike("全日制本科","WHEN EDU like ? or ");
        sql.append("EDU = '硕士' or EDU = '研究生' THEN USER_ID END)*100/COUNT(USER_ID),2) as 'BK_RATIO'");
        sql.append("FROM " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id ");
        if ("center".equals(deptType)) {
            sql.append("JOIN " + Constants.DS_MAIN_NAME + ".easi_dept t3 ON t2.p_dept_id = t3.DEPT_ID");
            sql.append("WHERE t1.STATE = 0");
            sql.append("GROUP BY t3.DEPT_NAME");
        } else {
            sql.append("WHERE t1.STATE = 0");
            sql.append("GROUP BY t1.DEPTS");
        }
        sql.append("ORDER BY BK_RATIO DESC");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "ageRatioStat", type = Types.RECORD)
    public JSONObject ageRatioStat() {
        EasySQL sql = new EasySQL("SELECT");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) < 25 THEN 1 ELSE 0 END) AS '25岁以下',");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) BETWEEN 25 AND 30 THEN 1 ELSE 0 END) AS '25-30岁',");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) BETWEEN 31 AND 40 THEN 1 ELSE 0 END) AS '31-40岁',");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) BETWEEN 41 AND 50 THEN 1 ELSE 0 END) AS '41-50岁',");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) > 50 THEN 1 ELSE 0 END) AS '50岁以上'");
        sql.append("FROM " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id ");
        sql.append("WHERE t1.STATE = 0");
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "ageRankByDept", type = Types.LIST)
    public JSONObject ageRankByDept() {
        String deptType = param.getString("deptType");
        EasySQL sql = new EasySQL("SELECT");
        if ("center".equals(deptType)) {
            sql.append("t3.DEPT_NAME as DEPT_CENTER,");
        } else {
            sql.append("t1.DEPTS as DEPT_CENTER,");
        }
        sql.append("ROUND(AVG(TIMESTAMPDIFF(YEAR, BORN, CURDATE())),1) AS AVERAGE_AGE,");
        sql.append("COUNT(USER_ID) AS STAFF_AMOUNT,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) < 25 THEN 1 ELSE 0 END) AS AGE_25,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) BETWEEN 25 AND 30 THEN 1 ELSE 0 END) AS AGE_25_30,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) BETWEEN 31 AND 40 THEN 1 ELSE 0 END) AS AGE_31_40,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) BETWEEN 41 AND 50 THEN 1 ELSE 0 END) AS AGE_41_50,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, BORN, CURDATE()) > 50 THEN 1 ELSE 0 END) AS AGE_50");
        sql.append("from " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id");
        if ("center".equals(deptType)) {
            sql.append("JOIN " + Constants.DS_MAIN_NAME + ".easi_dept t3 ON t2.p_dept_id = t3.DEPT_ID");
            sql.append("WHERE t1.STATE = 0");
            sql.append("GROUP BY t3.DEPT_NAME");
        } else {
            sql.append("WHERE t1.STATE = 0");
            sql.append("GROUP BY t1.DEPTS");
        }
        setOrderBy(sql, "order by AVERAGE_AGE asc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    @WebControl(name = "joinYearStat", type = Types.RECORD)
    public JSONObject joinYearStat() {
        EasySQL sql = new EasySQL("SELECT");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) < 1 THEN 1 ELSE 0 END) AS '1年以下',");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) BETWEEN 1 AND 2 THEN 1 ELSE 0 END) AS '1-3年',");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) BETWEEN 3 AND 4 THEN 1 ELSE 0 END) AS '3-5年',");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) BETWEEN 5 AND 6 THEN 1 ELSE 0 END) AS '5-7年',");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) >= 7 THEN 1 ELSE 0 END) AS '7年以上'");
        sql.append("FROM " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id ");
        sql.append("WHERE t1.STATE = 0");
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "joinYearRankByDept", type = Types.LIST)
    public JSONObject joinYearRankByDept() {
        String deptType = param.getString("deptType");
        EasySQL sql = new EasySQL("SELECT");
        if ("center".equals(deptType)) {
            sql.append("t3.DEPT_NAME as DEPT_CENTER,");
        } else {
            sql.append("t1.DEPTS as DEPT_CENTER,");
        }
        sql.append("ROUND(AVG(TIMESTAMPDIFF(YEAR, join_date, CURDATE())),1) AS AVERAGE_JOIN,");
        sql.append("COUNT(USER_ID) AS STAFF_AMOUNT,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) < 1 THEN 1 ELSE 0 END) AS YEAR_1,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) BETWEEN 1 AND 2 THEN 1 ELSE 0 END) AS YEAR_1_3,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) BETWEEN 3 AND 4 THEN 1 ELSE 0 END) AS YEAR_3_5,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) BETWEEN 5 AND 6 THEN 1 ELSE 0 END) AS YEAR_5_7,");
        sql.append("SUM(CASE WHEN TIMESTAMPDIFF(YEAR, join_date, CURDATE()) >= 7 THEN 1 ELSE 0 END) AS YEAR_7");
        sql.append("from " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id");
        if ("center".equals(deptType)) {
            sql.append("JOIN " + Constants.DS_MAIN_NAME + ".easi_dept t3 ON t2.p_dept_id = t3.DEPT_ID");
            sql.append("WHERE t1.STATE = 0");
            sql.append("GROUP BY t3.DEPT_NAME");
        } else {
            sql.append("WHERE t1.STATE = 0");
            sql.append("GROUP BY t1.DEPTS");
        }
        setOrderBy(sql, "order by AVERAGE_JOIN desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "ageAverageStat", type = Types.RECORD)
    public JSONObject ageAverageStat() {
        EasySQL sql = new EasySQL("SELECT");
        sql.append("ROUND(AVG(TIMESTAMPDIFF(YEAR, BORN, CURDATE())),1) AS AVERAGE_AGE,");
        sql.append("ROUND(AVG(TIMESTAMPDIFF(YEAR, join_date, CURDATE())),1) AS AVERAGE_JOIN,");
        sql.append("COUNT(USER_ID) AS TOTAL_STAFF");
        sql.append("FROM " + Constants.DS_MAIN_NAME + ".easi_user t1 join " + Constants.DS_MAIN_NAME + ".yq_staff_info t2 on t1.USER_ID = t2.staff_user_id ");
        sql.append("WHERE t1.STATE = 0");
        sql.append(param.getString("centerId"), "and t2.p_dept_id = ?");
        sql.append(param.getString("deptId"), "and t2.dept_id = ?");
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by ").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }

}
