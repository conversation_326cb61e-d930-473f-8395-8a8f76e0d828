package com.yunqu.work.servlet.crm;

import java.sql.SQLException;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.FileKit;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.utils.BdOcrUtiles;
import com.yunqu.work.utils.XmlToJsonUtils;

@WebServlet("/servlet/bx/invoice/*")
@MultipartConfig(maxFileSize=20*1024*1024)
public class BxInvoiceServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	
	public EasyResult actionForIndex(){
		JSONObject params = getJSONObject();
		String invoiceId = params.getString("invoiceId");
		String path = params.getString("path");
		String fileId = params.getString("id");
		String fsType = params.getString("fsType");
		String fileName = params.getString("name");
		
		EasyRecord model = new EasyRecord("yq_bx_invoice","invoice_id");
		model.setPrimaryValues(invoiceId);
		model.set("file_id", fileId);
		model.set("file_name", fileName);
		model.set("creator", getUserId());
		model.set("create_time", EasyDate.getCurrentDateString());
		
		if(".xml".equals(fsType)) {
			try {
				String str = FileKit.readToString(path);
				JSONObject json = XmlToJsonUtils.xmltoJson(str);
				this.info(json.toJSONString(), null);
				
				Object invoiceNo = JSONPath.eval(json, "EInvoice.TaxSupervisionInfo.InvoiceNumber");
				if(invoiceNo==null) {
					this.warn(str, null);
					return EasyResult.fail("文件格式内容不正确");
				}
				model.set("invoice_no", invoiceNo);
				model.set("invoice_date", JSONPath.eval(json, "EInvoice.TaxSupervisionInfo.IssueTime"));
				
				model.set("amount_in_figuers", JSONPath.eval(json, "EInvoice.EInvoiceData.IssuItemInformation.TotaltaxIncludedAmount"));
				model.set("total_amount", JSONPath.eval(json, "EInvoice.EInvoiceData.IssuItemInformation.Amount"));
				model.set("total_tax", JSONPath.eval(json, "EInvoice.EInvoiceData.IssuItemInformation.TaxRate"));
				
				model.set("service_type", JSONPath.eval(json, "EInvoice.EInvoiceData.IssuItemInformation.ItemName"));
				model.set("kp_remark", JSONPath.eval(json, "EInvoice.EInvoiceData.AdditionalInformation.Remark"));
				model.set("sale_company", JSONPath.eval(json, "EInvoice.EInvoiceData.SellerInformation.SellerName"));
				model.set("sale_company_no", JSONPath.eval(json, "EInvoice.EInvoiceData.SellerInformation.SellerIdNum"));
				model.set("buy_company", JSONPath.eval(json, "EInvoice.EInvoiceData.BuyerInformation.BuyerName"));
				model.set("buy_company_no", JSONPath.eval(json, "EInvoice.EInvoiceData.BuyerInformation.BuyerIdNum"));
				try {
					this.getQuery().save(model);
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
				return EasyResult.ok();
			} catch (Exception e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}else{
			String result = BdOcrUtiles.fpOcr(path,fsType);
			JSONObject row = JSONObject.parseObject(result);
			JSONObject wordsResult = row.getJSONObject("words_result");
			if(wordsResult==null) {
				return EasyResult.fail("识别失败");
			}
			model.set("invoice_no", wordsResult.getString("InvoiceNumConfirm"));
			model.set("invoice_type", wordsResult.getString("InvoiceType"));
			model.set("total_amount", wordsResult.getString("TotalAmount"));
			model.set("amount_in_figuers", wordsResult.getString("AmountInFiguers"));
			model.set("invoice_date", wordsResult.getString("InvoiceDate"));
			model.set("sale_company", wordsResult.getString("SellerName"));
			model.set("sale_company_no", wordsResult.getString("SellerRegisterNum"));
			model.set("buy_company", wordsResult.getString("PurchaserName"));
			model.set("buy_company_no", wordsResult.getString("PurchaserRegisterNum"));
			model.set("service_type", wordsResult.getString("ServiceType"));
			model.set("kp_remark", wordsResult.getString("Remarks"));
			model.set("total_tax", wordsResult.getString("TotalTax"));
			model.set("amount_in_words", wordsResult.getString("AmountInWords"));
			try {
				this.getQuery().save(model);
				return EasyResult.ok(result);
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
				return EasyResult.fail(e.getMessage());
			}
		}
	}
	
	 
	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_bx_invoice","invoice_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	
	public EasyResult actionForAdd(){
		EasyRecord model = getModel("invoice");
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		try {
			this.getQuery().save(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("invoice");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
		} catch (SQLException e) {
			this.error(null, e);
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDel(){
		try {
			this.getQuery().executeUpdate("delete from yq_bx_invoice where invoice_id = ?", getJsonPara("invoiceId"));
			this.getQuery().executeUpdate("delete from yq_files where fk_id = ?", getJsonPara("invoiceId"));
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return EasyResult.ok();
	}
	
	

}
