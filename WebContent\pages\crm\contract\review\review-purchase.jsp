<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>合同评审</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">评审编号</span>	
							 <input type="text" name="applyNo" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 180px">
							 <span class="input-group-addon">合同名称</span>	
							 <input type="text" name="contractName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm" style="width: 150px">
							 <span class="input-group-addon">签约单位</span>	
							 <select class="form-control input-sm" onchange="list.query();" name="signEnt">
		                    	<option value="">请选择</option>
		                    	<option value="yunqu">云趣</option>
		                    	<option value="zhongrong">中融</option>
		                    	<option value="pci">佳都</option>
		                     </select>
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" data-event="enter" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
					    <table class="layui-hide" id="list"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.init();
			}});
		});
		var list={
			init:function(){
				$("#searchForm").initTable({
					mars:'ReviewDao.purchaseList',
					height:'full-90',
					limit:20,
					cellMinWidth:80,
					rowEvent:'rowEvent',
					id:'list',
					cols: [[
					  {title:'',type:'radio'},
					  {title:'序号',type:'numbers'},
					  {
					    field: 'APPLY_NO',
						title: '评审编号',
						align:'left',
						style:'color:#1E9FFF;cursor:pointer',
						event:'list.detail',
						width:120
					 },
					 {
					    field: 'CONTRACT_NAME',
						title: '合同名称',
						align:'left',
						minWidth:220
					},{
					    field: 'F10',
						title: '登记类型',
						align:'left'
					},{
					    field: 'F1',
						title: '采购类型',
						align:'left'
					},{
					    field: 'F2',
						title: '品名',
						align:'left'
					},{
					    field: 'F3',
						title: '规格',
						align:'left'
					},{
					    field: 'F4',
						title: '数量',
						align:'left'
					},{
					    field: 'F5',
						title: '供应商',
						align:'left'
					},{
					    field: 'F6',
						title: '单价',
						align:'left'
					},{
					    field: 'F7',
						title: '数量',
						align:'left'
					},{
					    field: 'F8',
						title: '总价',
						align:'left'
					},{
					    field: 'F9',
						title: '是否含税',
						align:'left'
					},{
					    field: 'CREATE_NAME',
						title: '填写人',
						align:'left'
					},{
					    field: 'UPDATE_TIME',
						title: '填写时间',
						align:'left'
					}
				   ]]}
				);
			},
			query:function(){
				$("#searchForm").queryData();
			},
			detail:function(row){
				var data = {businessId:row['APPLY_ID']};
				popup.openTab({id:'reviewDetail',title:'评审详情',url:'${ctxPath}/web/flow',data:data});
			}
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>