package com.yunqu.work.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.utils.WeekUtils;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.sql.SQLException;
import java.util.List;

public class ContractStatService extends AppBaseService {
    private static class Holder{
        private static ContractStatService service=new ContractStatService();
    }
    public static ContractStatService getService(){
        return Holder.service;
    }

    public  void updateContractReceiptStat(){
        getLogger().info("-----updateContractReceiptStat执行合同收入统计 ....");
        try {
            this.getQuery().executeUpdate("TRUNCATE TABLE contract_receipt_stat");
            EasySQL sql = new EasySQL("INSERT INTO yq_contract_receipt_stat (CONTRACT_ID,CONTRACT_SIMPILE_NAME, CONTRACT_NO, CUSTOMER_NAME,MONTH_ID,AMOUNT)");
            sql.append("SELECT t1,CONTRACT_ID,t1.CONTRACT_SIMPILE_NAME, t1.CONTRACT_NO, t1.CUSTOMER_NAME,");
            sql.append("yq_contract_receipt.MONTH_ID,SUM(yq_contract_receipt.AMOUNT) AS AMOUNT");
            sql.append("from yq_project_contract t1 RIGHT JOIN yq_contract_receipt ON t1.CONTRACT_ID = yq_contract_receipt.CONTRACT_ID WHERE 1=1 GROUP BY t1.CONTRACT_ID, t1.CONTRACT_SIMPILE_NAME, t1.CONTRACT_NO, t1.CUSTOMER_NAME, yq_contract_receipt.MONTH_ID");
            this.getQuery().executeUpdate(sql.getSQL());
            updateStatTime("contract_receipt_stat");
        } catch (SQLException e) {
            getLogger().error("updateContractReceiptStat合同收入统计出错",e);
        }
    }


    private void updateStatTime(String tableName){
        EasySQL sql = new EasySQL("UPDATE yq_stat_table_info ");
        sql.append(EasyDate.getCurrentDateString(),"SET UPDATE_TIME = ?");
        sql.append(tableName,"WHERE TABLE_NAME = ?");
    }
}
