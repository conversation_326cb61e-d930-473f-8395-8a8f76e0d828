/*!
 * ZUI: 排序 - v1.9.1 - 2019-05-10
 * http://zui.sexy
 * GitHub: https://github.com/easysoft/zui.git 
 * Copyright (c) 2019 cnezsoft.com; Licensed MIT
 */
+function(t,e,r){"use strict";if(!t.fn.droppable)return void console.error("Sortable requires droppable.js");var o="zui.sortable",n={selector:"li,div",dragCssClass:"invisible",sortingClass:"sortable-sorting"},s="order",a=function(e,r){var o=this;o.$=t(e),o.options=t.extend({},n,o.$.data(),r),o.init()};a.DEFAULTS=n,a.NAME=o,a.prototype.init=function(){var e,r=this,o=r.$,n=r.options,a=n.selector,i=n.containerSelector,l=n.sortingClass,d=n.dragCssClass,f=n.targetSelector,c=n.reverse,g=function(e){e=e||r.getItems(1);var o=e.length;o&&e.each(function(e){var r=c?o-e:e;t(this).attr("data-"+s,r).data(s,r)})};g(),o.droppable({handle:n.trigger,target:f?f:i?a+","+i:a,selector:a,container:o,always:n.always,flex:!0,lazy:n.lazy,canMoveHere:n.canMoveHere,dropToClass:n.dropToClass,before:n.before,nested:!!i,mouseButton:n.mouseButton,stopPropagation:n.stopPropagation,start:function(t){d&&t.element.addClass(d),e=!1,r.trigger("start",t)},drag:function(t){if(o.addClass(l),t.isIn){var n=t.element,d=t.target,f=i&&d.is(i);if(f){if(!d.children(a).filter(".dragging").length){d.append(n);var p=r.getItems(1);g(p),r.trigger(s,{list:p,element:n})}return}var u=n.data(s),h=d.data(s);if(u===h)return g(p);u>h?d[c?"after":"before"](n):d[c?"before":"after"](n),e=!0;var p=r.getItems(1);g(p),r.trigger(s,{list:p,element:n})}},finish:function(t){d&&t.element&&t.element.removeClass(d),o.removeClass(l),r.trigger("finish",{list:r.getItems(),element:t.element,changed:e})}})},a.prototype.destroy=function(){this.$.droppable("destroy"),this.$.data(o,null)},a.prototype.reset=function(){this.destroy(),this.init()},a.prototype.getItems=function(e){var r=this.$.find(this.options.selector).not(".drag-shadow");return e?r:r.map(function(){var e=t(this);return{item:e,order:e.data("order")}})},a.prototype.trigger=function(e,r){return t.zui.callEvent(this.options[e],r,this)},t.fn.sortable=function(e){return this.each(function(){var r=t(this),n=r.data(o),s="object"==typeof e&&e;n?"object"==typeof e&&n.reset():r.data(o,n=new a(this,s)),"string"==typeof e&&n[e]()})},t.fn.sortable.Constructor=a}(jQuery,window,document);