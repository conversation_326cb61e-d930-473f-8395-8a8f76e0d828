<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>项目仪表盘</title>
    <style type="text/css">
        .layui-card {
            margin-bottom: 5px;
        }


        .layui-this {
            font-weight: bold;
        }


        .fast-url a {
            display: inline-block;
            font-size: 14px;
            color: #292929;
            padding-right: 0;
            padding-left: 0;
            padding-top: 10px;
            width: 29%;
        }

        .top-1 {
            font-size: 24px;
            font-weight: 500;
            color: #22cd0f;
        }

        .top-2 {
            font-size: 14px;
            color: #666;
        }

        .top-3 {
            font-size: 24px;
            font-weight: 500;
            color: #f0ad4e;
        }

        .divInline * {
            display: inline;
        }

        .divInline {
            height: 24px;
        }


        .pj-stat .layui-card {
            padding: 10px;
            border-radius: 5px;
        }

        .pj-stat .layui-card-hover:hover {
            transform: scale(1.02);
            transition: transform .3s ease;
            box-shadow: 0 4px 11px #0003;
            cursor: pointer;
        }


        .layui-icon-tips {
            margin-left: 3px;
            font-size: 14px;
        }

        #fixedDiv {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 999;
            background: #fff;
            padding: 0px 10px;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        #searchForm {
            padding-top: 60px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .layui-tab-content .layui-card {
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }


    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="searchForm" autocomplete="off">
        <div style="display: none">
            <select name="projectState" class="form-control input-sm">
                <option value=""></option>
                <option value="11" data-class="text-info">进度正常</option>
                <option value="12" data-class="text-warning">存在风险</option>
                <option value="13" data-class="text-danger">进度失控</option>
                <option value="20" data-class="text-danger">挂起中</option>
                <option value="30" data-class="text-success">已完成</option>
            </select>
        </div>
        <div class="layui-row" id="fixedDiv">
            <div class="form-group" style="flex-grow: 1;display: flex;margin-top: 10px;">
                <input type="hidden" name="timeType" id="timeType" value="begin_date">
                <div class="btn-group-sm btn-group time-type" style="width: 310px;margin-left: 20px">
                    <button class="btn btn-info btn-xs" type="button" value="begin_date">项目开始时间</button>
                    <button class="btn btn-default btn-xs" type="button" value="create_time">创建时间</button>
                    <button class="btn btn-default btn-xs" type="button" value="cy_date">计划初验</button>
                    <button class="btn btn-default btn-xs" type="button" value="zy_date">计划终验</button>
                </div>
                <div class="input-group input-group-sm ml-10" style="width: 280px">
                    <span class="input-group-addon">时间</span>
                    <input data-mars="CommonDao.yearBegin" name="beginDate" id="beginDate"
                           class="form-control input-sm" onchange="timeChange()"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="12">
                    <span class="input-group-addon">-</span>
                    <input data-mars="CommonDao.yearDateEnd" name="endDate" id="endDate"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" size="12" onchange="timeChange()"
                           class="form-control input-sm">
                </div>
                <div class="btn-group-sm btn-group select-begin-time" style="width: 400px;margin-left: 20px">
                    <button class="btn btn-default btn-xs" type="button" value="threeMonth">3月内</button>
                    <button class="btn btn-default btn-xs" type="button" value="halfYear">半年内</button>
                    <button class="btn btn-default btn-xs" type="button" value="oneYear">1年内</button>
                    <button class="btn btn-default btn-xs" type="button" value="twoYear">2年内</button>
                    <button class="btn btn-info btn-xs" type="button" value="nowYear">今年</button>
                    <button class="btn btn-default btn-xs" type="button" value="lastYear">去年</button>
                    <button class="btn btn-default btn-xs" type="button" value="nextYear">明年</button>
                    <button class="btn btn-default btn-xs" type="button" value="all">全部</button>
                </div>
                <i class="layui-icon layui-icon-tips" lay-tips="除特殊说明外，本页面下所有统计和列表均根据此置顶区的时间条件对项目进行了筛选。"></i>
            </div>
        </div>

        <div class="layui-row layui-col-space10 pj-stat" data-mars="ProjectStatisticDao.projectDashboardStat">
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="divInline">
                            <div class="top-1" id="newProjectCount">0</div>
                            <div class="top-3">(<span id="newProjectCount2">0</span>)</div>
                        </div>
                        <div class="top-2">今年开工项目
                            <i class="layui-icon layui-icon-tips" lay-tips="括号内只统计技术开发合同和技术服务合同的项目。"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="projectCount">0</div>
                        <div class="top-2">当前进行中项目</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-1" id="taskCount">0</div>
                        <div class="top-2">当前进行中任务</div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="top-3" id="riskCount">0</div>
                        <div class="top-2">当前待处理风险</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-row layui-col-space10 layui-tab" lay-filter="filter1">
            <ul class="layui-tab-title" style="background-color: #fff;">
                <li class="layui-this" lay-id="tab1">项目</li>
                <li lay-id="tab2">进度</li>
                <li lay-id="tab3">部门</li>
                <li lay-id="tab4">人力</li>
            </ul>
            <div class="layui-tab-content" style="margin: 0;padding-top:10px;background-color: #fff;">
                <div class="layui-tab-item layui-show">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md7">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    项目趋势(月)
                                </div>
                                <div style="height: 360px">
                                    <div id="timeChart">
                                        <div id="timeChart1" style="height: 340px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md5">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    <input type="hidden" id="fieldName" name="fieldName" value="PROJECT_TYPE"/>
                                    <select class="select-ratio-field" style="width: 150px;float: left;color:#000000;height:42px;font-size:14px;box-shadow:none;border: none;">
                                        <option value="PROJECT_TYPE" selected> 项目类型占比</option>
                                        <option value="CONTRACT_TYPE">合同类型占比</option>
                                        <option value="PROJECT_DEPT_NAME">工程大区占比</option>
                                        <option value="PRODUCT_LINE">产品线占比</option>
                                    </select>
                                    <i class="layui-icon layui-icon-tips" lay-tips="项目的统计范围跟随置顶栏的时间条件。"></i>
                                </div>
                                <div style="height: 360px">
                                    <div>
                                        <div id="ratioChart1" style="height: 340px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md8">
                            <div class="layui-card layui-col-md12">
                                <div class="layui-card-header">项目阶段统计</div>
                                <div>
                                    <div class="layui-col-md5">
                                        <div id="stageChart1" style="height: 380px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div class="layui-col-md7">
                                        <div id="stageChart2" style="height: 380px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-card">
                                <div class="layui-card-header">项目状态统计</div>
                                <div>
                                    <div id="stateChart1" style="height: 380px;width: 100%;" data-anim="fade"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md5">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    <select class="selectPersonCount" style="width: 150px;float: left;color:#000000;height:42px;font-size:14px;box-shadow:none;border: none;">
                                        <option value="personCountStat" selected> 项目规模分布</option>
                                        <option value="workPersonCountStat"> 实际参与分布</option>
                                        <option value="personCountList">总排名表</option>
                                    </select>
                                    <i class="layui-icon layui-icon-tips" lay-tips="项目规模（参与人数）指项目中[成员]的数量，实际参与(周报人数)指周报中填写过该项目的员工的总数。"></i>
                                </div>
                                <div style="height: 380px">
                                    <div id="personCountStat">
                                        <div id="personChart1" style="height: 360px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div id="workPersonCountStat" style="display: none">
                                        <div id="personChart2" style="height: 360px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div id="personCountList" class="layui-card-body" style="display: none">
                                        <table id="personCountListTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md7">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    <select class="select-work-hour" style="width: 150px;float: left;color:#000000;height:42px;font-size:14px;box-shadow:none;border: none;">
                                        <option value="workHourRank" selected> 投入工时排名</option>
                                        <option value="workHourAverageRank"> 人均投入排名</option>
                                        <option value="workHourList">总排名表</option>
                                    </select>
                                    <i class="layui-icon layui-icon-tips" lay-tips="人均工时指该项目[总投入工时]/[填写过项目周报的实际人数]，项目的统计范围跟随置顶栏的时间条件。"></i>
                                </div>
                                <div style="height: 380px">
                                    <div id="workHourRank">
                                        <div id="workHourChart1" style="height: 360px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div id="workHourAverageRank" style="display: none">
                                        <div id="workHourChart2" style="height: 360px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div id="workHourList" class="layui-card-body" style="display: none">
                                        <table id="workHourListTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    总项目统计表 <i class="layui-icon layui-icon-tips" lay-tips="列表搜索结果由置顶的时间条件得出。"></i>
                                </div>
                                <div style="height: 450px">
                                    <div class="layui-card-body">
                                        <table id="projectListTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <div class="layui-card">
                                <div class="layui-card-header">本月上线<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件。"></i></div>
                                <div class="layui-card-body">
                                    <table id="sxTable"></table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-card">
                                <div class="layui-card-header">本月计划初验<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件。"></i></div>
                                <div class="layui-card-body">
                                    <table id="cyTable"></table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-card">
                                <div class="layui-card-header">本月计划终验<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件。"></i></div>
                                <div class="layui-card-body">
                                    <table id="zyTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <div class="layui-card">
                                <div class="layui-card-header">全部待初验<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件。"></i></div>
                                <div class="layui-card-body">
                                    <table id="allCyTable"></table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-card">
                                <div class="layui-card-header">全部待终验<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件。"></i></div>
                                <div class="layui-card-body">
                                    <table id="allZyTable"></table>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-card">
                                <div class="layui-card-header">全部待一次性验收<i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件。"></i></div>
                                <div class="layui-card-body">
                                    <table id="allOnceTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    <select class="riskStateSelect" style="width: 150px;float: left;color:#000000;height:42px;font-size:14px;box-shadow:none;border: none;">
                                        <option value="0" selected>待处理风险</option>
                                        <option value="1">已完成风险</option>
                                        <option value="">全部风险</option>
                                    </select>
                                    <i class="layui-icon layui-icon-tips" lay-tips="本表搜索结果不跟随置顶时间条件。"></i></div>
                                <div class="layui-card-body">
                                    <table id="riskTable"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10" style="margin-top: 5px">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    延期任务数量<i class="layui-icon layui-icon-tips" lay-tips="统计表中不显示过往无延期任务的项目。"></i>
                                </div>
                                <div style="height: 380px">
                                    <div class="layui-col-md7">
                                        <div id="delayChart1" style="height: 360px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div class="layui-col-md5 layui-card-body">
                                        <table id="delayTable1"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    <select class="delay2Select" style="width: 150px;float: left;color:#000000;height:42px;font-size:14px;box-shadow:none;border: none;">
                                        <option value="delayChart2Div" selected>延期任务时长</option>
                                        <option value="delayTable2Div">总排名表</option>
                                    </select>
                                    <i class="layui-icon layui-icon-tips" lay-tips="平均延期天数是指该项目下所有延期的任务的平均延期天数，最大延期天数是每个任务下的多次延期时长相加后最大的数值。统计表中不显示过往无延期任务的项目。"></i>
                                </div>
                                <div style="height: 380px">
                                    <div id="delayChart2Div">
                                        <div id="delayChart2" style="height: 360px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                    <div id="delayTable2Div" class="layui-card-body" style="display: none">
                                        <table id="delayTable2"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md12">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    总项目进度列表 <i class="layui-icon layui-icon-tips" lay-tips="列表搜索结果由置顶的时间条件得出。"></i>
                                </div>
                                <div style="height: 450px">
                                    <div class="layui-card-body">
                                        <table id="projectProgressListTable"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    参与项目数量
                                    <i class="layui-icon layui-icon-tips" lay-tips="通过周报中的项目数进行统计。"></i>
                                </div>
                                <div style="height: 360px">
                                    <div>
                                        <div id="deptChart1" style="height: 340px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    参与项目数量
                                    <i class="layui-icon layui-icon-tips" lay-tips="通过被指派的任务进行统计。"></i>
                                </div>
                                <div style="height: 360px">
                                    <div>
                                        <div id="deptChart2" style="height: 340px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="layui-tab-item">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md7">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    人力投入(月)
                                </div>
                                <div style="height: 360px">
                                    <div id="workTimeChart">
                                        <div id="workTimeChart1" style="height: 340px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md5">
                            <div class="layui-card">
                                <div class="layui-card-header">
                                    工时类型
                                </div>
                                <div style="height: 360px">
                                    <div>
                                        <div style="height: 340px;width: 100%;" data-anim="fade"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br>
    </form>
</EasyTag:override>
<EasyTag:override name="script">
    <script src="/easitline-static/lib/echarts/echarts.min.js"></script>
    <script src="${ctxPath}/static/js/echartsTheme.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/dateUtils.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

    <script>
        var loadMap = {};

        $(function () {

            $("#searchForm").render({
                success: function () {
                    timeChange();
                }
            });

            layui.element.on('tab(filter1)', function (data) {
                if (data.id == 'tab2') {
                    echarts.getInstanceByDom(document.getElementById('delayChart1')).resize();
                    echarts.getInstanceByDom(document.getElementById('delayChart2')).resize();
                } else if (data.id == 'tab3') {
                    echarts.getInstanceByDom(document.getElementById('deptChart1')).resize();
                    echarts.getInstanceByDom(document.getElementById('deptChart2')).resize();
                }
            });


            $('.select-begin-time button').click(function () {
                getDate($(this)[0], 'beginDate', 'endDate');
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                timeChange();
            });

            $('.select-ratio-field').on('change', function () {
                var value = $(this).val();
                $('#fieldName').val(value);
                loadRatioChart();
            });

            $('.selectPersonCount').on('change', function () {
                var value = $(this).val();
                $('#' + value).show();
                $('#' + value).siblings().hide();
                if (value === "workPersonCountStat") {
                    echarts.getInstanceByDom(document.getElementById("personChart2")).resize();
                }
            });

            $('.riskStateSelect').on('change', function () {
                var value = $(this).val();
                loadRiskTable(value);
            });

            $('.delay2Select').on('change', function () {
                var value = $(this).val();
                $('#' + value).show();
                $('#' + value).siblings().hide();
            });

            $('.select-work-hour').on('change', function () {
                var value = $(this).val();
                $('#' + value).show();
                $('#' + value).siblings().hide();
                if (value === "workHourAverageRank") {
                    echarts.getInstanceByDom(document.getElementById("workHourChart2")).resize();
                }
            });


            $('.time-type button').click(function () {
                $('[name="timeType"]').val($(this)[0].value);
                $(this).siblings().removeClass('btn-info');
                $(this).siblings().addClass('btn-default');
                $(this).addClass('btn-info');
                timeChange();
            });
        });


        function timeChange() {
            loadTimeChart();
            loadRatioChart();
            loadStageChart();
            loadStateChart();
            loadPersonChart();
            loadProjectListTable();

            loadSxTable();
            loadCyTable();
            loadZyTable();

            loadAllStageTable("待初验", "allCyTable");
            loadAllStageTable("待终验", "allZyTable");
            loadAllStageTable("待一次性验收", "allOnceTable");

            loadRiskTable(0);

            loadDelayTable1();
            loadDelayTable2();
            setTimeout(function () {
                loadWorkPersonChart();
                loadPersonTable();
                loadWorkHourTable();
                loadWorkHourAverageChart();

                loadProjectProgressListTable();
            }, 1000)

            loadDeptChart1();
            loadDeptChart2();
        }

        function loadTimeChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectCountByTime", {"beginDate": beginDate, "endDate": endDate}, function (result) {
                var data1 = result["begin_date"];
                var data2 = result["end_date"];

                var timeTypes = ['开始项目', '结束项目'];
                var months = Object.keys(data1);
                var amount1 = months.map(month => data1[month] || "0");
                var amount2 = months.map(month => data2[month] || "0");

                var options = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function (params) {
                            var result = '';
                            result += params[0].name + "月" + '<br/>';
                            params.forEach(function (item) {
                                result += item.marker + item.seriesName + ': ' + item.value + '<br/>';
                            });
                            return result;
                        },
                    },
                    legend: {
                        data: timeTypes
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    barGap: '0%',
                    barCategoryGap: '50%',
                    xAxis: {
                        type: 'category',
                        data: months,
                        axisLabel: {
                            rotate: 45,
                            interval: 0
                        }
                    },
                    yAxis: [
                        {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        },
                    ],
                    series: [
                        {
                            name: timeTypes[0],
                            type: 'line',
                            yAxisIndex: 0,
                            data: amount1,
                            smooth: false,
                            label: {
                                show: true,
                                position: 'top',
                                formatter: function (params) {
                                    return amount1[params.dataIndex] > 0 ? amount1[params.dataIndex] : '';
                                },
                                textStyle: {
                                    fontSize: 13,
                                    fontWeight: 'bold',
                                },
                            },
                            itemStyle: {
                                color: '#91cc75'
                            }
                        }, {
                            name: timeTypes[1],
                            type: 'line',
                            yAxisIndex: 0,
                            data: amount2,
                            smooth: false,
                            label: {
                                show: true,
                                position: 'top',
                                formatter: function (params) {
                                    return amount2[params.dataIndex] > 0 ? amount2[params.dataIndex] : '';
                                },
                                textStyle: {
                                    fontSize: 13,
                                    fontWeight: 'bold',
                                },
                            },
                            itemStyle: {
                                color: '#fac858'
                            },
                        },
                    ]
                };

                var timeChart1 = echarts.init(document.getElementById('timeChart1'));
                timeChart1.setOption(options);

            })

        }

        function loadRatioChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            var fieldName = $('#fieldName').val();

            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectFieldRatio", {
                "beginDate": beginDate,
                "endDate": endDate,
                "timeType": timeType,
                "fieldName": fieldName
            }, function (result) {
                var data2 = result.data;
                var projectTypeMapping = {"10": "合同项目", "11": "提前执行", "20": "自研项目", "30": "维保项目"};

                const echartsData = data2.map(item => {
                    if (fieldName == "PROJECT_TYPE") {
                        return {
                            name: projectTypeMapping[item.FIELD_NAME],
                            value: item.NUMS
                        };
                    } else {
                        return {
                            name: item.FIELD_NAME,
                            value: item.NUMS
                        };
                    }

                });
                loadPieChart(echartsData, "ratioChart1");
            })
        }

        function loadDelayTable1() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.delayTaskRankByProject',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '340px',
                    autoSort: false,
                    id: 'delayTable1',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 100,
                        }, {
                            field: 'DELAY_TASK',
                            title: '延期任务数',
                            width: 100,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'SUM_DELAY',
                            title: '总延期次数',
                            width: 100,
                            sort: true,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {
                        if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                            return;
                        }
                        var data2 = data.data;
                        data2 = data2.slice(0, 20);
                        var projectName = data2.map(item => subContractName(item.PROJECT_NAME));
                        var nums1 = data2.map(item => item.DELAY_TASK);
                        var nums2 = data2.map(item => item.SUM_DELAY);
                        var legend = ['延迟任务数量', '总延迟次数']
                        loadBarAndLineChart(projectName, nums1, nums2, "delayChart1", legend)
                    }
                },
            )
        }

        function loadDelayTable2() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.delayTimeRankByProject',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '340px',
                    autoSort: false,
                    id: 'delayTable2',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 100,
                        }, {
                            field: 'TOTAL_DELAY_DAYS',
                            title: '总延时天数',
                            width: 150,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'MAX_DELAY_DAYS',
                            title: '任务最大延时/天',
                            width: 150,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'AVERAGE_DELAY_DAYS',
                            title: '任务平均延时/天',
                            width: 150,
                            sort: true,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {
                        if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                            return;
                        }
                        var data2 = data.data;
                        data2 = data2.slice(0, 20);
                        var projectName = data2.map(item => subContractName(item.PROJECT_NAME));
                        var nums1 = data2.map(item => item.MAX_DELAY_DAYS);
                        var nums2 = data2.map(item => item.AVERAGE_DELAY_DAYS);
                        var nums3 = data2.map(item => item.TOTAL_DELAY_DAYS);
                        var legend = ['任务最大延时/天', '任务平均延时/天', '总延时天数']
                        loadTwoBarAndLineChart(projectName, nums1, nums2, nums3, "delayChart2", legend)
                    }
                },
            )
        }

        function loadSxTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectList1',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '320px',
                    autoSort: false,
                    data: {"timeType2": "sx_date"},
                    id: 'sxTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 110,
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            width: 110
                        }, {
                            field: 'SX_DATE',
                            title: '上线时间',
                            width: 90,
                            sort: true,
                            align: 'center',
                        },
                    ]],
                    done: function (data) {
                        table.on('sort(sxTable)', function (obj) {
                            $("#searchForm").queryData({
                                id: 'sxTable', jumpOne: true, initSort: obj,
                                data: {sortName: obj.field, sortType: obj.type, "timeType2": "sx_date"}
                            })
                        })
                    }
                },
            )
        }

        function loadCyTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectList1',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '320px',
                    autoSort: false,
                    data: {"timeType2": "cy_date"},
                    id: 'cyTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 110,
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            width: 110
                        }, {
                            field: 'CY_DATE',
                            title: '计划初验',
                            width: 90,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'CY_REAL_DATE',
                            title: '实际初验',
                            width: 80,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {
                        table.on('sort(cyTable)', function (obj) {
                            $("#searchForm").queryData({
                                id: 'cyTable', jumpOne: true, initSort: obj,
                                data: {sortName: obj.field, sortType: obj.type, "timeType2": "cy_date"}
                            })
                        })
                    }
                },
            )
        }

        function loadZyTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectList1',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '320px',
                    autoSort: false,
                    data: {"timeType2": "zy_date"},
                    id: 'zyTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 110,
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            width: 110
                        }, {
                            field: 'ZY_DATE',
                            title: '计划终验',
                            width: 90,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'ZY_REAL_DATE',
                            title: '实际终验',
                            width: 80,
                            align: 'center',
                        },
                    ]],
                    done: function (data) {
                        table.on('sort(zyTable)', function (obj) {
                            $("#searchForm").queryData({
                                id: 'zyTable', jumpOne: true, initSort: obj,
                                data: {sortName: obj.field, sortType: obj.type, "timeType2": "zy_date"}
                            })
                        })
                    }
                },
            )
        }

        function loadAllStageTable(stage, tableId) {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectListByCondition',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '320px',
                    autoSort: false,
                    data: {"projectStage": stage},
                    id: tableId,
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 110,
                        }, {
                            field: 'LAST_TASK_TIME',
                            title: '最后任务时间',
                            width: 140,
                            sort: true,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {
                        table.on('sort('+tableId+')', function (obj) {
                            $("#searchForm").queryData({
                                id: tableId, jumpOne: true, initSort: obj,
                                data: {sortName: obj.field, sortType: obj.type, "projectStage": stage}
                            })
                        })
                    }
                },
            )
        }

        function loadRiskTable(state) {
            $("#searchForm").initTable({
                mars: 'ProjectRiskDao.riskList',
                id: "riskTable",
                height: '320px',
                limit: 30,
                data: {riskState: state},
                cols: [[
                    {title: '序号', type: 'numbers'},
                    {
                        title: '操作',
                        align: 'center',
                        width: 120,
                        templet: function (row) {
                            return '<a class="btn btn-xs btn-link" href="javascript:;" lay-event="riskDetail">详情</a><a class="btn btn-xs btn-link ml-5" href="javascript:;" lay-event="riskFlow">流程</a>';
                        }
                    }, {
                        field: 'OWNER_NAME',
                        title: '负责人',
                        width: 70,
                        templet: function (row) {
                            return getUserName(row.SOL_OWNER);
                        }
                    }, {
                        field: 'CREATOR',
                        title: '发起人',
                        width: 70,
                        align: 'center',
                        templet: function (row) {
                            return getUserName(row.CREATOR);
                        }
                    },
                    {
                        field: 'RISK_BELONG',
                        title: '风险分类',
                        align: 'center',
                        width: 90
                    }, {
                        field: 'RISK_DESC',
                        title: '风险说明',
                        align: 'left',
                        minWidth: 200,
                        templet: function (row) {
                            return cutText(row.RISK_DESC, 120);
                        }
                    }, {
                        field: 'SOLUTION',
                        title: '风险应对措施',
                        align: 'left',
                        minWidth: 150,
                        templet: function (row) {
                            return cutText(row.SOLUTION, 120);
                        }
                    }, {
                        field: 'CREATE_TIME',
                        title: '录入时间',
                        align: 'center',
                        width: 140,
                    }, {
                        field: 'RISK_LEVEL',
                        title: '风险等级',
                        align: 'center',
                        width: 80,
                        templet: function (row) {
                            return riskLevelLabel(row.RISK_LEVEL);
                        }
                    }, {
                        field: 'RISK_STATE',
                        title: '风险状态',
                        align: 'center',
                        width: 80,
                        templet: function (row) {
                            return riskStateLabel(row.RISK_STATE);
                        }
                    }
                ]], done: function (result) {

                }
            });
        }

        function riskFlow(data) {
            var data = {businessId: data['RISK_ID']};
            popup.openTab({id: 'flowDetail', title: '流程详情', url: '${ctxPath}/web/flow', data: data});
        }

        function riskDetail(data) {
            var isAdmin = 0;
            if (top.isAdmin == '1' || isSuperUser) {
                isAdmin = 1;
            }
            var riskId = data['RISK_ID'];
            var riskState = data['RISK_STATE'];
            popup.layerShow({
                type: 1,
                anim: 0,
                scrollbar: false,
                shadeClose: false,
                title: '风险详情',
                offset: 'r',
                area: ['700px', '100%'],
                url: '${ctxPath}/pages/project/risk/risk-detail.jsp',
                title: '风险详情',
                data: {riskId: riskId, riskState: riskState, isAdmin: isAdmin}
            });
        }

        function riskStateLabel(state) {
            var json = {0: '<label class="label label-danger label-outline">进行中</label>', 1: '<label class="label label-success">已完成</label>'};
            var result = json[state] || '';
            if (result) {
                return result;
            } else {
                return '';
            }
        }

        function riskLevelLabel(state) {
            var json = {0: '<label class="label  label-outline label-warning">低</label>', 1: '<label class="label  label-warning">中</label>', 2: '<label class="label label-danger">高</label>'};
            var result = json[state] || '';
            if (result) {
                return result;
            } else {
                return '';
            }
        }

        function loadProjectListTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectStatList',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '400px',
                    autoSort: false,
                    toolbar:true,
                    id: 'projectListTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 100,
                        },{
                            field: 'PROJECT_PO_NAME',
                            title: '项目经理',
                            width:80
                        },{
                            field: 'PO_NAME',
                            title: '开发负责人',
                            width:100
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            width: 110
                        },{
                            field: 'PERSON_COUNT',
                            title: '参与人数',
                            width: 110,
                            sort: true,
                        },{
                            field: 'WORK_PEOPLE_COUNT',
                            title: '周报人数',
                            width: 110,
                            sort: true,
                        },{
                            field: 'WORKHOUR_DAY',
                            title: '投入人力/天',
                            width: 110,
                            sort: true,
                        },{
                            field: '',
                            title: '人均投入/天',
                            width: 110,
                            templet: function (d) {
                                if(d.WORK_PEOPLE_COUNT == ""|| d.WORK_PEOPLE_COUNT == "0" || d.WORK_PEOPLE_COUNT === 0)return '';
                                return (Number(d.WORKHOUR_DAY)/Number(d.WORK_PEOPLE_COUNT)).toFixed(1);
                            }
                        }, {
                            field: 'CY_DATE',
                            title: '计划初验',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'ZY_DATE',
                            title: '计划终验',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'CY_REAL_DATE',
                            title: '实际初验',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'ZY_REAL_DATE',
                            title: '实际终验',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'BEGIN_DATE',
                            title: '开始时间',
                            width: 90,
                            sort: true,
                            hide:true
                        }, {
                            field: 'LAST_TASK_TIME',
                            title: '最近任务',
                            width: 140,
                            align: 'left',
                            sort: true,
                        },
                    ]],
                    done: function (data) {
                    }
                },
            )
        }

        function loadProjectProgressListTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectProgressList',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '400px',
                    autoSort: false,
                    toolbar: true,
                    id: 'projectProgressListTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left',
                            fixed:'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 200,
                            fixed:'left'
                        }, {
                            field: 'PROJECT_STAGE',
                            title: '项目阶段',
                            width: 110
                        }, {
                            field: 'PROJECT_STATE',
                            title: '项目状态',
                            align: 'center',
                            width: 80,
                            templet: function (row) {
                                return getText(row.PROJECT_STATE, 'projectState');
                            }
                        },{
                            field: 'DELAY_TASK',
                            title: '延期任务数',
                            width: 100,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'SUM_DELAY',
                            title: '总延期次数',
                            width: 100,
                            sort: true,
                            align: 'center',
                        },{
                            field: 'MAX_DELAY_DAYS',
                            title: '任务最大延时/天',
                            width: 150,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'AVERAGE_DELAY_DAYS',
                            title: '任务平均延时/天',
                            width: 150,
                            sort: true,
                            align: 'center',
                        },{
                            field: 'CY_DATE',
                            title: '计划初验',
                            width: 90,
                            sort: true,
                        }, {
                            field: 'ZY_DATE',
                            title: '计划终验',
                            width: 90,
                            sort: true,
                        }, {
                            field: 'CY_REAL_DATE',
                            title: '实际初验',
                            width: 90,
                            sort: true,
                        }, {
                            field: 'ZY_REAL_DATE',
                            title: '实际终验',
                            width: 90,
                            sort: true,
                        }, {
                            field: 'BEGIN_DATE',
                            title: '开始时间',
                            width: 90,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'LAST_TASK_TIME',
                            title: '最近任务',
                            width: 140,
                            align: 'left',
                            sort: true,
                        },
                    ]],
                    done: function (data) {
                    }
                },
            )
        }


        function loadStageChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectStage", {"beginDate": beginDate, "endDate": endDate, "timeType": timeType}, function (result) {
                var data2 = result.data;
                var stageName = data2.map(item => item.PROJECT_STAGE);
                var amounts = data2.map(item => item.NUMS);
                loadHorizontalBarChart(stageName, amounts, "stageChart2")

                const echartsData = data2.map(item => {
                    return {
                        name: item.PROJECT_STAGE,
                        value: item.NUMS
                    };
                });
                loadRosePieChart(echartsData, "stageChart1");

            })
        }

        function loadStateChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectState", {"beginDate": beginDate, "endDate": endDate, "timeType": timeType}, function (result) {
                var data2 = result.data;

                const PROJECT_STATE_MAP = {
                    '11': '进度正常',
                    '12': '存在风险',
                    '13': '进度失控',
                    '20': '挂起中',
                    '30': '已完成'
                };
                const echartsData = data2.map(item => {
                    return {
                        name: PROJECT_STATE_MAP[item.PROJECT_STATE] || item.PROJECT_STATE,
                        value: item.NUMS
                    };
                });
                loadPieChart(echartsData, "stateChart1");
            })
        }

        function loadPersonChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectPersonCountStat", {"beginDate": beginDate, "endDate": endDate, "timeType": timeType}, function (result) {
                var data2 = result.data;
                var personCountCategory = Object.keys(data2);
                var amounts = personCountCategory.map(key => data2[key]);
                loadHorizontalBarChart(personCountCategory, amounts, "personChart1")
            })
        }

        function loadWorkPersonChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectWorkPeopleCountStat", {"beginDate": beginDate, "endDate": endDate, "timeType": timeType}, function (result) {
                var data2 = result.data;
                var personCountCategory = Object.keys(data2);
                var amounts = personCountCategory.map(key => data2[key]);
                loadHorizontalBarChart(personCountCategory, amounts, "personChart2")
            })
        }

        function loadPersonTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.projectPersonCountList',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '340px',
                    autoSort: false,
                    id: 'personCountListTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 100,
                        }, {
                            field: 'PERSON_COUNT',
                            title: '参与人数',
                            width: 80,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'WORK_PEOPLE_COUNT',
                            title: '周报人数',
                            width: 80,
                            sort: true,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {

                    }
                },
            )
        }

        function loadWorkHourTable() {
            $("#searchForm").initTable({
                    mars: 'ProjectStatisticDao.workHourRank',
                    cellMinWidth: 50,
                    limit: 20,
                    height: '340px',
                    autoSort: false,
                    id: 'workHourListTable',
                    cols: [[
                        {
                            type: 'numbers',
                            title: '序号',
                            align: 'left'
                        }, {
                            field: 'PROJECT_NAME',
                            title: '项目',
                            event: 'projectDetailByRow',
                            style: 'cursor:pointer;text-decoration: underline;',
                            minWidth: 100,
                        }, {
                            field: 'WORKHOUR_DAY',
                            title: '投入工时(天)',
                            width: 110,
                            sort: true,
                            align: 'center',
                        }, {
                            field: 'PERSON_AVERAGE',
                            title: '人均投入(天)',
                            width: 110,
                            sort: true,
                            align: 'center',
                        }
                    ]],
                    done: function (data) {
                        if ((data.othis.data) || data.pageNumber != 1 || data.pageSize > 20) {
                            return;
                        }
                        var data2 = data.data.slice(0, 20);
                        var projectName = data2.map(item => subContractName(item.PROJECT_NAME));
                        var workHour = data2.map(item => item.WORKHOUR_DAY);
                        loadBarChart(projectName, workHour, "workHourChart1")
                    }
                },
            )
        }

        function loadWorkHourAverageChart() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.workHourRank", {
                "beginDate": beginDate, "endDate": endDate, "timeType": timeType, "pageSize": 20, "sortName": "PERSON_AVERAGE",
                "sortType": "desc"
            }, function (result) {
                var data2 = result.data;
                var projectName = data2.map(item => subContractName(item.PROJECT_NAME));
                var workHour = data2.map(item => item.PERSON_AVERAGE);
                var legend = ['平均每人投入工时/天']
                loadBarChart(projectName, workHour, "workHourChart2", legend)
            })
        }

        function loadDeptChart1() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectNumsRankByDept", {
                "beginDate": beginDate, "endDate": endDate, "timeType": timeType, "pageSize": 20
            }, function (result) {
                var data2 = result.data;
                var deptName = data2.map(item => item.DEPT_NAME);
                var nums = data2.map(item => item.NUMS);
                var legend = ['参与项目']
                loadBarChart(deptName, nums, "deptChart1", legend)
            })
        }

        function loadDeptChart2() {
            var beginDate = $('#beginDate').val();
            var endDate = $('#endDate').val();
            var timeType = $('#timeType').val();
            ajax.remoteCall("/yq-work/webcall?action=ProjectStatisticDao.projectNumsRankByDept2", {
                "beginDate": beginDate, "endDate": endDate, "timeType": timeType, "pageSize": 20
            }, function (result) {
                var data2 = result.data;
                var deptName = data2.map(item => item.DEPT_NAME);
                var nums = data2.map(item => item.NUMS);
                var legend = ['参与项目']
                loadBarChart(deptName, nums, "deptChart2", legend)
            })
        }

        function loadBarChart(fieldName, amounts, tableName, legend) {
            if (legend == null) {
                legend = ['投入人力/天']
            }
            var options = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '6%',
                    right: '4%',
                    bottom: '1%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '50%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: amounts,
                        label: {
                            show: true,
                            position: 'top'
                        },
                    }
                ],
            };

            var chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);

            return chart;
        }

        function loadBarAndLineChart(fieldName, amounts1, amounts2, tableName, legend) {
            var options = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '6%',
                    right: '4%',
                    bottom: '1%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '50%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: amounts1,
                        label: {
                            show: true,
                            position: 'inside'
                        },
                    },
                    {
                        name: legend[1],
                        type: 'line',
                        data: amounts2,
                        smooth: false,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function (params) {
                                if (amounts1[params.dataIndex] == amounts2[params.dataIndex]) {
                                    return '';
                                }
                                return amounts2[params.dataIndex];
                            },
                        },
                    }

                ],
            };

            var chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);

            return chart;
        }

        function loadTwoBarAndLineChart(fieldName, amounts1, amounts2, amounts3, tableName, legend) {
            var options = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                },
                legend: {
                    data: legend
                },
                grid: {
                    left: '7%',
                    right: '4%',
                    bottom: '1%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '30%',
                xAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        rotate: 45,
                        interval: 0
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    },
                    {
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    },
                ],
                series: [
                    {
                        name: legend[0],
                        type: 'bar',
                        data: amounts1,
                        label: {
                            show: true,
                            position: 'inside'
                        },
                    },
                    {
                        name: legend[1],
                        type: 'bar',
                        data: amounts2,
                        label: {
                            show: true,
                            position: 'inside'
                        },
                    },
                    {
                        name: legend[2],
                        type: 'line',
                        data: amounts3,
                        smooth: false,
                        yAxisIndex: 1,
                        label: {
                            show: true,
                            position: 'top',
                        },
                    }

                ],
            };

            var chart = echarts.init(document.getElementById(tableName));
            chart.setOption(options);

            return chart;
        }

        function loadHorizontalBarChart(fieldName, nums, chartName) {
            var totalAmount = nums.reduce((sum, value) => sum + Number(value), 0);
            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function (params) {
                        if (totalAmount === 0) {
                            return params[0].name + ':' + params[0].value;
                        }
                        var percentage = ((Number(params[0].value) / totalAmount) * 100).toFixed(2);
                        return params[0].name + ': ' + params[0].value + '\n占比: ' + percentage + '%';
                    },
                },
                grid: {
                    left: '3%',
                    right: '17%',
                    bottom: '3%',
                    top: '1%',
                    containLabel: true
                },
                barGap: '0%',
                barWidth: '80%',
                xAxis: {
                    type: 'value',
                },
                yAxis: {
                    type: 'category',
                    data: fieldName,
                    axisLabel: {
                        show: true,
                        interval: 0,
                    }
                },
                series: [
                    {
                        name: '项目数',
                        type: 'bar',
                        data: nums,
                        itemStyle: {
                            color: function (params) {
                                var colorList = ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"];
                                return colorList[params.dataIndex % colorList.length];
                            }
                        },
                        label: {
                            show: true,
                            position: 'right',
                            lineHeight: 12,
                            textStyle: {
                                fontWeight: 'bold',
                            },
                            formatter: function (params) {
                                if (totalAmount === 0) {
                                    return params.name + ':' + params.value;
                                }
                                var percentage = ((Number(params.value) / totalAmount) * 100).toFixed(2);
                                return params.name + ': ' + params.value + '\n占比: ' + percentage + '%';
                            }
                        },
                    },
                ]
            };
            var myChart = echarts.init(document.getElementById(chartName));
            myChart.setOption(option)
        }

        function loadPieChart(echartsData, chartName) {
            var options1 = {
                color: ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"],
                tooltip: {
                    trigger: "item",
                    formatter: "{b}: {c}<br> ({d}%)"
                },
                grid: {
                    left: '35%',
                    right: '35%',
                    top: '1%',
                    bottom: '0%',
                    containLabel: false
                },
                series: [
                    {
                        name: "",
                        type: "pie",
                        radius: ['20%', '55%'],
                        data: echartsData,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{b}: {c} ({d}%)'
                        },
                        labelLine: {
                            show: true
                        }
                    },
                ]
            };
            var myCharts = echarts.init(document.getElementById(chartName));
            myCharts.setOption(options1);
        }

        function loadRosePieChart(echartsData, chartName) {
            var options1 = {
                color: ["#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de", "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc"],
                tooltip: {
                    trigger: "item",
                    formatter: "{b}: {c}<br> ({d}%)"
                },
                grid: {
                    left: '40%',
                    right: '40%',
                    bottom: '0%',
                    top: '1%',
                    containLabel: false
                },
                series: [
                    {
                        name: "",
                        type: "pie",
                        radius: ['20%', '55%'],
                        center: ['55%', '50%'],
                        roseType: "radius",
                        data: echartsData,
                        minAngle: 17,
                        label: {
                            show: true,
                            position: 'outside',
                            lineHeight: 12,
                            rich: {
                                bValue: {
                                    fontSize: 12,
                                },
                                cValue: {
                                    fontSize: 12,
                                },
                                dValue: {
                                    fontSize: 12,
                                }
                            },
                            formatter: function (params) {
                                if (params.dataIndex >= 7) {
                                    return '{bValue|' + params.data.name + '}:{cValue|' + params.data.value + '}({dValue|' + params.percent + '%})';
                                }
                                return '{bValue|' + params.data.name + '}:{cValue|' + params.data.value + '}\n({dValue|' + params.percent + '%})';
                            },
                        },
                        labelLine: {
                            normal: {
                                length: 10,
                                length2: 3
                            }
                        },
                    },

                ]
            };
            var myCharts = echarts.init(document.getElementById(chartName));
            myCharts.setOption(options1);
        }

        function subContractName(contractName) {
            if (contractName.length > 10) {
                if (/^\d{4}-\d{4}/.test(contractName.substring(0, 9))) {
                    return contractName.substring(0, 15) + "...";
                } else if (/^\d{4}/.test(contractName.substring(0, 4))) {
                    return contractName.substring(0, 13) + "...";
                }
                return contractName.substring(0, 10) + "...";
            } else {
                return contractName;
            }
        }

        var getDate = function (obj, startDate, endDate) {
            var val = obj.value;
            var bdate = new Date();
            var edate = new Date();
            var monthAdjustments = {
                'oneMonth': -1,
                'threeMonth': -3,
                'halfYear': -6,
                'oneYear': -12,
                'twoYear': -24
            };
            if (val in monthAdjustments) {
                bdate.setMonth(bdate.getMonth() + monthAdjustments[val]);
            } else if (val === 'nowYear') {
                bdate.setMonth(0, 1);
                edate.setMonth(11, 31);
            } else if (val === 'lastYear') {
                bdate.setFullYear(bdate.getFullYear() - 1, 0, 1);
                edate.setFullYear(edate.getFullYear() - 1, 11, 31);
            } else if (val === 'nextYear') {
                bdate.setFullYear(bdate.getFullYear() + 1, 0, 1);
                edate.setFullYear(edate.getFullYear() + 1, 11, 31);
            }
            var bdateVal = val && val !== 'all' ? DateUtils.dateFormat(bdate, 'yyyy-MM-dd') : '';
            var edateVal = val && val !== 'all' ? DateUtils.dateFormat(edate, 'yyyy-MM-dd') : '';
            $('[name="' + startDate + '"]').val(bdateVal);
            $('[name="' + endDate + '"]').val(edateVal);
        }

        $('*[lay-tips]').on('mouseenter', function () {
            var content = $(this).attr('lay-tips');
            this.index = layer.tips('<div style="padding: 0px; font-size: 14px; color: #eee;">' + content + '</div>', this, {
                time: -1
                , maxWidth: 280
                , tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function () {
            layer.close(this.index);
        });
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
