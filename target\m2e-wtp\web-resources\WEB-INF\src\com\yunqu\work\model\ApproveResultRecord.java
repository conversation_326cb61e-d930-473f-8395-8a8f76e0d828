package com.yunqu.work.model;

import java.io.Serializable;

import org.easitline.common.db.EasyRecord;

public class ApproveResultRecord extends EasyRecord implements Serializable{
	private static final long serialVersionUID = 1L;

	public ApproveResultRecord() {
		this.setTableInfo("YQ_FLOW_APPROVE_RESULT","RESULT_ID");
	}
	
	private String nodeId;
	
	private String resultId;
	
	private String prevResultId;
	
	private String preCheckUserId;
	
	private String checkUserId;
	
	private String checkName;
	
	private String getTime;
	
	private String readTime;
	
	private Integer checkResult;
	
	private Integer approveType;
	
	private String checkTime;
	
	private String overTime;
	
	private String businessId;
	
	private String nodeName;
	
	private String data1;
	private String data2;
	private String data3;
	private String data4;
	private String data5;
	
	private ApproveNodeModel approveNode;

	public String getNodeId() {
		return nodeId;
	}

	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
		this.set("node_id", nodeId);
	}

	public String getResultId() {
		return resultId;
	}

	public void setResultId(String resultId) {
		this.resultId = resultId;
		this.set("result_id", resultId);
	}
	
	

	public String getPreCheckUserId() {
		return preCheckUserId;
	}

	public void setPreCheckUserId(String preCheckUserId) {
		this.preCheckUserId = preCheckUserId;
		this.set("pre_check_user_id", preCheckUserId);
	}

	public String getCheckUserId() {
		return checkUserId;
	}

	public void setCheckUserId(String checkUserId) {
		this.checkUserId = checkUserId;
		this.set("check_user_id", checkUserId);
	}

	public String getCheckName() {
		return checkName;
	}

	public void setCheckName(String checkName) {
		this.checkName = checkName;
		this.set("check_name", checkName);
	}

	public String getGetTime() {
		return getTime;
	}

	public void setGetTime(String getTime) {
		this.getTime = getTime;
		this.set("get_time", getTime);
	}

	public String getReadTime() {
		return readTime;
	}

	public void setReadTime(String readTime) {
		this.readTime = readTime;
		this.set("read_time", readTime);
	}

	public Integer getCheckResult() {
		return checkResult;
	}

	public void setCheckResult(Integer checkResult) {
		this.checkResult = checkResult;
		this.set("check_result", checkResult);
	}

	public String getBusinessId() {
		return businessId;
	}

	public void setBusinessId(String businessId) {
		this.businessId = businessId;
		this.set("business_id", businessId);
	}

	public String getNodeName() {
		return nodeName;
	}

	public void setNodeName(String nodeName) {
		this.nodeName = nodeName;
		this.set("node_name", nodeName);
	}

	
	
	public String getOverTime() {
		return overTime;
	}

	public void setOverTime(String overTime) {
		this.overTime = overTime;
		this.set("over_time", overTime);
	}

	public String getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(String checkTime) {
		this.checkTime = checkTime;
		this.set("check_time", checkTime);
	}

	public String getPrevResultId() {
		return prevResultId;
	}

	public void setPrevResultId(String prevResultId) {
		this.prevResultId = prevResultId;
		this.set("prev_result_id", prevResultId);
	}

	public String getData1() {
		return data1;
	}

	public void setData1(String data1) {
		this.data1 = data1;
		this.set("data1", data1);
	}

	public String getData2() {
		return data2;
	}

	public void setData2(String data2) {
		this.data2 = data2;
		this.set("data2", data2);
	}

	public String getData3() {
		return data3;
	}

	public void setData3(String data3) {
		this.data3 = data3;
		this.set("data3", data3);
	}

	public String getData4() {
		return data4;
	}

	public void setData4(String data4) {
		this.data4 = data4;
		this.set("data4", data4);
	}

	public String getData5() {
		return data5;
	}

	public void setData5(String data5) {
		this.data5 = data5;
		this.set("data5", data5);
	}

	public Integer getApproveType() {
		return approveType;
	}

	public void setApproveType(Integer approveType) {
		this.approveType = approveType;
		this.set("approve_type", approveType);
	}

	public ApproveNodeModel getApproveNode() {
		return approveNode;
	}

	public void setApproveNode(ApproveNodeModel approveNode) {
		this.approveNode = approveNode;
	}
	
	

}
