package com.yunqu.work.service;

import java.io.IOException;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.crypt.DESUtil;
import org.easitline.common.utils.string.StringUtils;
import org.jsoup.Connection;
import org.jsoup.Connection.Method;
import org.jsoup.Connection.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseService;
import com.yunqu.work.base.Constants;

public class OAService extends AppBaseService{
	
	private Logger logger = LogEngine.getLogger(Constants.APP_NAME);
	@SuppressWarnings("unused")
	private HttpServletRequest request;
	
	private static class Holder{
		private static OAService service=new OAService();
	}
	public static OAService getService(){
		return Holder.service;
	}
	public static OAService getService(HttpServletRequest request){
		OAService service=Holder.service;
		service.request=request;
		return service;
	}
	
	private static Map<String,Map<String,String>> cookies=new HashMap<String,Map<String,String>>();
	
	public Map<String,String> getUserCookie(String userName){
		return cookies.get(userName);
	}
	
	public  boolean login(String userName,String pwd){
		try {
			Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/names.nsf?Login");
			connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
			connection.data("Username",userName);
			connection.data("Password",pwd);
			Response resp=connection.method(Method.POST).ignoreContentType(true).timeout(2000).execute();
			Map<String, String> loginCookies = resp.cookies();
			if(loginCookies==null||loginCookies.size()==0){
				cookies.remove(userName);
				return false;
			}else{
				cookies.put(userName,loginCookies);
				return true;
			}
		} catch (IOException e) {
			logger.error(null,e);
			return false;
		}
				
	}
	
	public static void main(String[] args) throws IOException {
		//OAService.getService().login("100263","624708");
		OAService.getService().login("100042","pcitech");
//		OAService.getService().login("100133","123456");
//		System.out.println(OAService.getService().loadContractXml("100042"));
//		System.out.println(OAService.getService().loadCustXml("100042"));
//		OAService.getService().loadContractDetail();
		//OAService.getService().loadHome();
//		OAService.getService().getNotReadEmailCount("100263");
//		OAService.getService().loadTreeList();
//		OAService.getService().loadWaitXml();
//		OAService.getService().loadCompletedXml();
		//OAService.getService().loadKaoqinXml();
		//OAService.getService().loadKaoqinDetail();
	}

	public  void loadContractDetail() throws IOException{
		//Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/app.nsf/frmOpenForm?readform&WF_FormNumber=F_App006_003&WF_DocUNID=2EC1641AB7F4FE4F4825850D000BCB7B&WF_Action=Edit");
		//Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/app.nsf/frmOpenForm?readform&WF_FormNumber=F_App006_003&WF_DocUNID=ADAD5A40DE63FB8A482584D5000D98B9");
		Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/app.nsf/LinkeyGetXmlData?openagent&Num=G_App006_003&rdc=0.013029555556045624&start=0&limit=100&Num=G_App006_003&QViewName=&UserName=%E6%9D%8E%E5%8D%83%E5%B8%86100042");
		connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
		connection.cookies(cookies.get("100042"));
		//connection.cookie("LtpaToken", "AAECAzVFNEE2MEVBNUU0QjMzREFDTj0TwO4Tx6cTt6sxMDAwNDIvTz1wY2lNrjVeD8i8L0ksrQHfq/utR7Jx9A==");
		connection.header("Accept", "text/html, application/xhtml+xml, image/jxr, */*");
		connection.header("Referer","http://yqoa.yunqu-info.com/bpm/app.nsf/frmOpenForm?readform&WF_FormNumber=F_App006_003&WF_DocUNID=2EC1641AB7F4FE4F4825850D000BCB7B");
		connection.header("Connection","Keep-Alive");
		connection.header("Host","yqoa.yunqu-info.com");
		connection.header("Accept-Language", "zh-CN");
		connection.header("Content-Type", "text/html; charset=UTF-8");
		Response resp=connection.method(Method.GET).timeout(8000).execute();
		System.out.println(resp.body());
		
	}
	public  void loadHome() throws IOException{
		Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/frm?readform");
		connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
		connection.cookies(cookies.get("100263"));
		Response resp=connection.method(Method.GET).ignoreContentType(true).timeout(2000).execute();
		System.out.println(resp.body());
		
	}
	public  String getNotReadEmailCount(String account){
		Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/interface.nsf/ui_exmail_online");
		connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
		connection.cookies(cookies.get(account));
		Response resp;
		try {
			resp = connection.method(Method.POST).ignoreContentType(true).timeout(2000).execute();
			if(resp.statusCode()==200){
				JSONObject object = JSONObject.parseObject(resp.body());
				return StringUtils.defaultString(object.getString("NewCount"),"0");
			}else{
				return "0";
			}
		} catch (IOException e) {
			logger.error(null,e);
			return "0";
		}
	}
	//json
	public  String loadTreeJson(String account){
		Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/Tree_List?openagent&sync=1");
		connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
		connection.cookies(cookies.get(account));
		connection.data("node","T_001_002");///bpm/bpm.nsf/LinkeyGrid?readform&Num=G_001_006
		try {
			Response resp = connection.method(Method.POST).ignoreContentType(true).timeout(2000).execute();
			if(resp.statusCode()==200){
				return resp.body();
			}else{
				return null;
			}
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
			return null;
		}
		
	}
	
	//客户xml
	public  String loadCustXml(String account){
		try {
			//http://yqoa.yunqu-info.com/bpm/app.nsf/frmOpenForm?readform&WF_FormNumber=F_App004_CustomerRecord&WF_DocUNID=1753615A1FA6667B4825852E0032C2E9&WF_Action=Edit
			Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/app.nsf/LinkeyGetXmlData?openagent&Num=G_App004_CustomerRecord&rdc=0.*****************");
			connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
			connection.cookies(cookies.get(account));
			connection.data("start","0");
			connection.data("limit","20");
			connection.data("Num","G_App004_CustomerRecord");
			Response resp  = connection.method(Method.POST).ignoreContentType(true).timeout(8000).execute();
			return resp.body();
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
			return null;
		}
	}
	//合同xml
	public  String loadContractXml(String account){
		try {
			//Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/app.nsf/LinkeyGetXmlDataForAdmin?openagent&ObjName=ProjectNumber_1&SEGMENT=12_PRJ&rdc=0.*****************");
			Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/app.nsf/LinkeyGetXmlData?openagent&rdc=0.013029555556045624");
			connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
			connection.cookies(cookies.get(account));
			connection.data("start","0");
			connection.data("limit","20");
			connection.data("Num","G_App006_003");
			Response resp  = connection.method(Method.POST).ignoreContentType(true).timeout(8000).execute();
			return resp.body();
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
			return null;
		}
	}
	public  Document loadContractDetail(String account,String id){
		try {
			Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/app.nsf/frmOpenForm?readform&WF_FormNumber=F_App006_003&WF_DocUNID="+id+"&WF_Action=Edit");
			connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
			connection.cookies(cookies.get(account));
			connection.timeout(8000);
			connection.ignoreContentType(true);
			Document doc  = connection.get();
			return doc;
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
			return null;
		}
	}
	//待我批xml
	public  String loadMyDoXml(String account){
		try {
			Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/LinkeyGetXmlData?openagent&Num=G_001_006");
			connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
			connection.cookies(cookies.get(account));
			connection.data("start","0");
			connection.data("limit","150");
			connection.data("Num","G_001_006");
			Response resp  = connection.method(Method.POST).ignoreContentType(true).timeout(8000).execute();
			return resp.body();
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
			return null;
		}
	}
	//我提交的所有流程xml
	public  String loadMyWfXml(String account){
		try {
			Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/LinkeyGetXmlData?openagent&Num=G_001_003");
			connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
			connection.cookies(cookies.get(account));
			connection.data("start", "0");
			connection.data("limit", "150");
			Response resp  = connection.method(Method.POST).ignoreContentType(true).timeout(8000).execute();
			return resp.body();
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
			return null;
		}
	}
	
	//我提交流程已办结的xml
	public  String loadCompletedXml(String account){
		try {
			Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/LinkeyGetXmlData?openagent&Num=G_011_001.1");
			connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
			connection.cookies(cookies.get("100263"));
			Response resp=connection.method(Method.POST).ignoreContentType(true).timeout(2000).execute();
			return resp.body();
		} catch (IOException e) {
			this.logger.error(e.getMessage(),e);
			return null;
		}
	}
	
	//考勤出差等申请单xml
	public  void loadKaoqinXml() throws IOException{
		Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/LinkeyGetXmlData?openagent&Num=G_011_C01&rdc=0.****************");
		connection.header("Content-Type","application/xml;charset=utf-8");
		connection.data("start","0");
		connection.data("limit","30");
		connection.data("Num","G_011_C01");
		connection.data("start=0&limit=30&Num=G_011_C01&QViewName=&UserName=叶子彤100263");
		connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
		connection.cookies(cookies.get("100263"));
		Response resp=connection.method(Method.POST).ignoreContentType(true).timeout(2000).execute();
		System.out.println(resp.body());
	}
	public  void loadKaoqinDetail() throws IOException{
		//Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/frmopenform?readform&WF_FormNumber=F_011_001.1&WF_DocUNID=62D9C360A937D006482584A300079036");
		Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/frmopenform?readform&WF_FormNumber=F_011_001.1&WF_DocUNID=1913CFA187475CB7482584B0000A1240");
		connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
		connection.cookies(cookies.get("100263"));
		Response resp=connection.method(Method.POST).ignoreContentType(true).timeout(2000).execute();
		System.out.println(resp.body());
		Document document=Jsoup.parse(resp.body());
		System.out.println(document.getElementById("ANo").text());
		System.out.println(document.getElementById("BDate").text());
		System.out.println(document.getElementById("BTime").text());
		System.out.println(document.getElementById("EDate").text());
		System.out.println(document.getElementById("ETime").text());
		System.out.println(document.getElementById("Expression").text());//请假
		//System.out.println(document.getElementById("Reason").text());//出差 、、补签
		
		//补签
		System.out.println(document.getElementById("BDate_1").text());
		System.out.println(document.getElementById("BTime_1").text());
		System.out.println(document.getElementById("BDate_2").text());
		System.out.println(document.getElementById("BTime_2").text());
		
		
		Elements inputs=document.select("#sqztx input");
		for(Element element:inputs){
			if(element.hasAttr("checked")){
				System.out.println(element.val());
			}
		}
		Elements inputs2=document.select("#buqiantr04 input");
		for(Element element:inputs2){
			if(element.hasAttr("checked")){
				System.out.println(element.val());
			}
		}
	}
		
	public void emailLoginJump(HttpServletRequest request,HttpServletResponse response) throws IOException{
		Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/interface.nsf/ui_exmail_login?openagent");
		connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
		connection.cookies(cookies.get(request.getRemoteUser()));
		Response resp=connection.method(Method.POST).ignoreContentType(true).timeout(2000).execute();
		String str=resp.body();
		Render.renderHtml(request, response,str);
	}
	
	public void myWfDetail(HttpServletRequest request,HttpServletResponse response,String no) throws IOException{
		Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/frmopenform?readform&WF_FormNumber=F_011_001&WF_DocUNID="+no);
		connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
		connection.cookies(cookies.get(request.getRemoteUser()));
		connection.header("Connection", "keep-alive");
		Response resp=connection.method(Method.GET).ignoreContentType(true).timeout(6000).execute();
		String str=resp.body();
		if(resp.statusCode()==200&&StringUtils.notBlank(str)){
			str=str.replaceAll("alert", "console.log");
			str=str.replaceAll("查看流转记录", "");
			str=str.replaceAll("查看流程图", "");
			str=str.replaceAll("<body", " <body style=\"background-color: #fff;\"");
			str=str.replaceAll("/linkey/bpm", "http://yqoa.yunqu-info.com/linkey/bpm");
			str=str.replaceAll("Form_GetJs", "http://yqoa.yunqu-info.com/bpm/bpm.nsf/Form_GetJs");
			str=str.replaceAll("/bpm/bpm.nsf/WF_RunRule", "http://yqoa.yunqu-info.com/bpm/bpm.nsf/WF_RunRule");
			Render.renderHtml(request, response,str);
		}else{
			Render.renderHtml(request, response,"<h2>请刷新页面重试。</h2>");
		}
	}
	public void wfDetail(HttpServletRequest request,HttpServletResponse response) throws IOException{
		Connection connection=Jsoup.connect("http://yqoa.yunqu-info.com/bpm/bpm.nsf/frmopenform?readform&WF_FormNumber=F_011_001&WF_DocUNID=9D2E643C41B39555482584620005AF26");
		connection.userAgent("Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
		connection.cookies(cookies.get(request.getRemoteUser()));
		Response resp=connection.method(Method.POST).ignoreContentType(true).timeout(2000).execute();
		String str=resp.body();
		str=str.replaceAll("/linkey", "http://yqoa.yunqu-info.com/linkey");
		Render.renderHtml(request, response,str);
	}
	
	public  void scanOaAcount(){
	/*	List<String> accounts=new ArrayList<String>();
		try {
			List<JSONObject> list = getMainQuery().queryForList("select t2.USER_ID,t2.USER_ACCT from easi_user t1 INNER JOIN easi_user_login t2 on t2.USER_ID=t1.USER_ID where t1.STATE=0", new Object[]{},new JSONMapperImpl());
			for(JSONObject object:list){
				String userAcct=object.getString("USER_ACCT");
				accounts.add(userAcct);
				Boolean flag=login(userAcct, "123456");
				if(flag){
					EasyRecord record = new EasyRecord("yq_oa_user","OA_ACCOUNT");
					record.set("OA_ACCOUNT",userAcct );
					record.set("USER_ID",object.getString("USER_ID"));
					record.set("OA_PWD",DESUtil.getInstance().encryptStr("123456"));
					record.set("STATE", "ok");
					record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
					this.getQuery().save(record);
				}
			}
		} catch (SQLException e) {
			logger.error(null,e);
		}*/
		try {
			List<JSONObject> list = getQuery().queryForList("select * from YQ_OA_USER", new Object[]{},new JSONMapperImpl());
			for(JSONObject object:list){
				String userAcct=object.getString("OA_ACCOUNT");
				/*if(!accounts.contains(userAcct)){
					EasyRecord record = new EasyRecord("YQ_OA_USER","OA_ACCOUNT");
					record.set("OA_ACCOUNT",userAcct );
					this.getQuery().deleteById(record);
				}*/
				String pwd=object.getString("OA_PWD");
				Boolean flag=login(userAcct, DESUtil.getInstance().decryptStr(pwd));
				if(!flag){
					EasyRecord record = new EasyRecord("YQ_OA_USER","OA_ACCOUNT");
					record.set("OA_ACCOUNT",userAcct );
					record.set("STATE","fail");
					this.getQuery().update(record);
				}else{
					EasyRecord record = new EasyRecord("YQ_OA_USER","OA_ACCOUNT");
					record.set("OA_ACCOUNT",userAcct );
					record.set("STATE","ok");
					this.getQuery().update(record);
				}
			}
		} catch (SQLException e) {
			logger.error(null,e);
		}
		
	}
}







