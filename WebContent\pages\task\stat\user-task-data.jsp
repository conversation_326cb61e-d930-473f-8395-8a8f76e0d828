<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>年度任务汇总</title>
	<style>
		.layui-tree-txt{font-size: 13px;}
		.layui-btn+.layui-btn{margin-left: 2px;}
		
		.layui-tab-item{padding: 0px 5px;margin-top: -5px;}
		.icon{width:1.5em;height:1.5em;vertical-align:-0.55em;fill:currentColor;overflow:hidden;margin-right:1px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="searchForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <div class="input-group input-group-sm"  style="width: 120px">
							 <span class="input-group-addon">部门</span>	
							 <input type="text" name="deptName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm"  style="width: 120px">
							 <span class="input-group-addon">姓名</span>	
							 <input type="text" name="userName" class="form-control input-sm">
					     </div>
	          		     <div class="input-group input-group-sm"  style="width: 120px">
							 <span class="input-group-addon">年份</span>	
							 <input type="number" name="yearId" class="form-control input-sm">
					     </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							 <button type="button" class="btn btn-sm btn-default ml-5" onclick="list.clearForm()">清空</button>
						 </div>
	          	     </div>
	              </div> 
				  <div class="ibox-content">
					  <table class="layui-hide" id="yearTaskTable"></table>
				 </div>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">

		var list = {
			initTable:function(){
				$("#searchForm").initTable({
					mars:'TaskStatDao.nowYearTaskData',
					id:'yearTaskTable',
					limit:30,
					height:'full-80',
					title:'年度任务统计',
					cols: [[
						{
						 	type: 'numbers',
							title: '序号',
							align:'left'
						 },{
						    field: 'DEPTS',
							title: '部门',
							width:120
						 },{
						    field: 'USERNAME',
							title: '姓名',
							width:90,
							event:'list.detail',
							align:'left',
							style:'color:#1E9FFF;cursor:pointer'
						},{
						    field: 'JOIN_DATE',
							title: '入职日期',
							width:100
					    },{
						    field: 'JOB_POST',
							title: '岗位',
							width:120
						 },{
						    field: 'TASK_COUNT',
							title: '本年总任务数',
							width:100
						 },{
						    field: 'USE_WORK_HOUR',
							title: '本年已投入工作量/人/天',
							width:150,
							templet:function(row){
								return Math.round(row.USE_WORK_HOUR/8);
							}
						 },{
						    field: 'PLAN_WORK_HOUR',
							title: '计划投入/人/天',
							width:150,
							templet:function(row){
								return Math.round(row.PLAN_WORK_HOUR/8);
							}
						 },{
						    field: 'TASK_DOING',
							title: '在途任务数',
							width:100
						 },{
						    field: 'PLAN_WORK_HOUR',
							title: '在途工作量(人/天）',
							width:150
						 },{
						    field: 'TASK_DELAY_COUNT',
							title: '当前逾期任务数',
							width:120
						 },{
						    field: 'YEAR_DELAY_TASK_COUNT',
							title: '本年累计逾期任务数',
							width:130
						 }
					]],done:function(result){
						
					}}
				);
			},
			query:function(){
				$("#searchForm").queryData({id:'yearTaskTable',jumpOne:true});
			},
			clearForm:function(){
				$("#searchForm")[0].reset();
				list.query();
			},
			detail:function(data){
				layer.msg('todo',{icon:7,offset:'20px',time:1200});
			}
		}
		
		$(function(){
			$("#searchForm").render({success:function(){
				list.initTable();
			}});
		});
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>