<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>我的任务</title>
	<style type="text/css">
		.project-list p{font-size: 13px;margin: 10px 0px;}
		.add-btn .top-2,.add-btn a{cursor: pointer;color: #1890ff;}
		.layui-timeline-title{font-size: 16px;font-weight: 400;color: #0cc7ff;}
		.timeline-a{color: #666666!important;}
		.layui-card{margin-bottom: 5px;}
		.fast-url a{display: inline-block;font-size: 13px;color: #292929;padding:0px 10px;width: 29.9%;overflow: hidden;}
		.top-stat .layui-col-md3{text-align: center;padding: 10px;}
		.top-1{font-size: 18px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="searchForm">
	  <div class="layui-row layui-col-space5">
	    <div class="layui-col-md8">
	      <div class="layui-card">
	        <div class="layui-card-header">项目任务</div>
	        <div class="layui-card-body" style="min-height: 150px;">
			   <table class="layui-hide" id="myTaskTable"></table>
	        </div>
	      </div>
	      <div class="layui-card layui-hide">
	        <div class="layui-card-header">我关注的项目/里程碑</div>
	        <div class="layui-card-body">
			        
	        </div>
	      </div>
	      <div class="layui-card">
	        <div class="layui-card-header">我的动态</div>
	        <div class="layui-card-body" style="min-height: 150px;">
	        	<div style="padding-bottom: 10px;min-height: 350px;max-height: 800px;overflow-y: scroll;">
    		   		<div class="layui-timeline action"></div>
   		   		</div>
	        </div>
	      </div>
	    </div>
	    <div class="layui-col-md4">
	      <div class="layui-card">
	        <div class="layui-card-header">数据看板</div>
	        <div class="layui-card-body" data-mars="TaskDao.myTaskListCountStat">
	         	<div class="layui-row layui-col-space1 top-stat" data-mars="ProjectDataDao.personIndexStat">
				  <div class="layui-col-md3">
				    <div class="top-1" id="A">14</div>
				    <div class="top-2">待办任务</div>
				  </div>
				  <div class="layui-col-md3">
				    <div class="top-1" id="B">0</div>
				    <div class="top-2">进行中任务</div>
				  </div>
				  <div class="layui-col-md3">
				    <div class="top-1" id="E">0</div>
				    <div class="top-2">逾期任务</div>
				  </div>
				  <div class="layui-col-md3">
				    <div class="top-1" id="bugCount">0</div>
				    <div class="top-2">禅道BUG</div>
				  </div>
				  <div class="layui-col-md3">
				    <div class="top-1" id="weeklyCount">0</div>
				    <div class="top-2">周报数</div>
				  </div>
				  <div class="layui-col-md3">
				    <div class="top-1" id="versionCount">0</div>
				    <div class="top-2">版本</div>
				  </div>
				  <div class="layui-col-md3">
				    <div class="top-1" id="riskCount">0</div>
				    <div class="top-2">风险</div>
				  </div>
				  <div class="layui-col-md3">
				    <div class="top-1" id="projectCount">0</div>
				    <div class="top-2">项目</div>
				  </div>
  				</div>
  				<hr>
  				<div class="layui-row top-stat layui-col-space1">
  					<div class="layui-col-md3 add-btn">
					    <div class="top-1"><i class="layui-icon layui-icon-form"></i></div>
					    <div class="top-2" onclick="createTask();">创建任务</div>
				    </div>
  					<div class="layui-col-md3 add-btn">
					    <div class="top-1"><i class="layui-icon layui-icon-flag"></i></div>
					    <div class="top-2" onclick="addWeekly();">填写周报</div>
				    </div>
  					<div class="layui-col-md3 add-btn">
					    <div class="top-1"><i class="layui-icon layui-icon-release"></i></div>
					    <div class="top-2" onclick="addVersion();">发布版本</div>
				    </div>
  					<div class="layui-col-md3 add-btn">
					    <div class="top-1"><i class="layui-icon layui-icon-dialogue"></i></div>
					    <div class="top-2" onclick="flowApplyLink('ts_rd');">需求评审</div>
				    </div>
  					<div class="layui-col-md3 add-btn">
					    <div class="top-1"><i class="layui-icon layui-icon-date"></i></div>
					    <div class="top-2" onclick="editWorkhour();">工时上报</div>
				    </div>
  					<div class="layui-col-md3 add-btn">
					    <div class="top-1"><i class="layui-icon layui-icon-link"></i></div>
					    <div class="top-2"><a target="_blank" href="/yq-work/project/zentao">禅道</a></div>
				    </div>
  					<div class="layui-col-md3 add-btn">
					    <div class="top-1"><i class="layui-icon layui-icon-util"></i></div>
					    <div class="top-2"><a target="_blank" href="http://172.16.68.152:3001/">在线接口</a></div>
				    </div>
  					<div class="layui-col-md3 add-btn">
					    <div class="top-1"><i class="layui-icon layui-icon-edit"></i></div>
					    <div class="top-2"><a target="_blank" href="/yq-work/auth/memos">备忘录</a></div>
				    </div>
  				</div>
	        </div>
	      </div>
	      <div class="layui-card">
	        <div class="layui-card-header">我的项目</div>
	        <div class="layui-card-body project-list" data-template="project-template" data-mars="HomeDao.myProject"></div>
	        <script id="project-template" type="text/x-jsrender">
				{{if data.length==0}}<p style="text-align:center;">暂无数据</p>{{/if}}
		        {{for data}}
	                <p><i class="layui-icon layui-icon-circle"></i> <a href="javascript:;" onclick="projectBoard('{{:PROJECT_ID}}')">{{call:PROJECT_NAME 60 fn='cutText'}}</a></p>
		       {{/for}}
        	</script>
	      </div>
	      <div class="layui-card">
	        <div class="layui-card-header">工具</div>
	        <div class="layui-card-body fast-url"></div>
	      </div>
	    </div>
	  </div>
   </form>    
    <script id="action-list" type="text/x-jsrender">
		{{for data}}
			<div class="layui-timeline-item">
   			 	 <i class="layui-icon layui-timeline-axis"></i>
   			 	<div class="layui-timeline-content layui-text">
      				<h3 class="layui-timeline-title">{{:#index+1}} # {{call:CREATE_TIME fn='dateShow'}}</h3>
					{{if TYPE=='task'}}<span class="layui-icon layui-icon-senior"></span>{{else TYPE=='weekly'}}<span class="layui-icon layui-icon-date"></span>{{else TYPE=='project'}}<span class="layui-icon layui-icon-app"></span>{{/if}}
					<a class="timeline-a" href="javascript:;" onclick="list.openContent('{{:TYPE}}','{{:ID}}')">{{cutText:CONTENT 200}}</a>
			  	</div>
 			 </div>			
	    {{/for}}
		{{if data.length==0}} <tr><td colspan="3">暂无记录</td></tr> {{/if}}
	</script>   
</EasyTag:override>
<EasyTag:override name="script">
	<script>
	  	$(function(){
	  		$("#searchForm").render({data:{status:1},success:function(){
		  		loadTask();
		  		loadAction();
		  		loadUrl();
		  		prjectStage();
	  		}});
	  	});
	  	
	  	var list = {
  			myTaskDetail:function(data){
  				var width = $(window).width();
				if(width < 2000){
					width = '850px';
				}else{
					width = '50%';
				}
  				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/task/task-detail.jsp',title:'任务详情',data:{taskId:data.TASK_ID,taskState:data.TASK_STATE,status:1}});
  			},
  			openContent:function(type,id){
  				if(type=='task'){
  					window.open('/yq-work/task/'+id);
  				}else if(type=='weekly'){
  					window.open('/yq-work/weekly/'+id);
  				}else if(type=='project'){
  					projectBoard(id);
  				}
  			}
	  	};
	  	
	  	function loadTask(){
	  		$("#searchForm").initTable({
				mars:'ProjectDataDao.myProjectIndexTaskList',
				title:'我负责的任务',
				cellMinWidth:50,
				limit:20,
				data:{status:1,beforeDay:100},
				height:'300px',
				maxHeight:500,
				id:'myTaskTable',
				cols: [[
					{type:'numbers',title:'序号'},
					{
					    field: 'CREATE_NAME',
						title: '发起人',
						align:'left',
						width:70,
						sort:true
					 },{
					    field: 'ASSIGN_USER_NAME',
						title: '负责人',
						align:'left',
						width:70,
						sort:true
					 },{
					    field: 'TASK_STATE',
						title: '任务状态',
						align:'center',
						sort:true,
						width:80,
						templet:function(row){
							return taskStateLabel(row.TASK_STATE);
						}
					},{
					    field: 'TASK_NAME',
						title: '任务名称',
						event:'list.myTaskDetail',
						minWidth:220,
						align:'left',
						style:'color:#1E9FFF;cursor:pointer'
					},{
					    field: 'PROJ_NAME',
						title: '项目名称',
						event:'list.projectDetail',
						align:'left',
						minWidth:150
					},{
					    field: 'CREATE_TIME',
						title: '发起时间',
						align:'center',
						width:90,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,12,'');
						}
					},{
					    field: 'DEADLINE_AT',
						title: '截止时间',
						align:'left',
						width:100,
						templet:function(row){
							var time= row['DEADLINE_AT'];
							var state= row['TASK_STATE'];
							if(state<30&&state!=12){
								return timeBetween(time);
							}
							return cutText(time,12,'');
						}
					}
				]],done:function(result){
				
				}}
			);
		 }
	  	
	  	function prjectStage(){
	  		
	  	}
	  	
	  	function loadUrl(){
	  		var list = [{'title':'博思白板','url':'https://boardmix.cn/'},{'title':'ioDraw','url':'https://www.iodraw.com/'},{'title':'知犀','url':'https://www.zhixi.com/'},{'title':'AI大学堂','url':'https://www.aidaxue.com/'},{'title':'开发热榜','url':'https://tophub.today/c/developer'},{'title':'产品+','url':'https://www.woshipm.com/'},{'title':'开发者搜索','url':'https://kaifa.baidu.com/'},{'title':'开搜AI','url':'https://kaisouai.com/'},{'title':'开源搜索','url':'https://so.gitee.com/'},{'title':'AtomHub','url':'https://atomhub.openatom.cn/'},{'title':'OPSX镜像','url':'https://developer.aliyun.com/mirror/'},{'title':'程序员盒子','url':'https://www.coderutil.com/'},{'title':'程序员宝盒','url':'https://www.baoboxs.com/'},{'title':'菜鸟工具','url':'https://www.jyshare.com/'},{'title':'AI导航','url':'https://www.aihub.cn/'},{'title':'InfoQ','url':'https://www.infoq.cn/'},{'title':'掘金酱','url':'https://e.juejin.cn/'},{'title':'墨天轮','url':'https://www.modb.pro/dbMarket'},{'title':'有趣新产品','url':'https://ywyj.cn/'},{'title':'即刻产品','url':'https://h5.ruguoapp.com/jk-product-launch-event/today'},{'title':'topbook','url':'https://topbook.cc/'},{'title':'diagrams','url':'https://app.diagrams.net/'},{'title':'CodeWithGPU','url':'https://www.codewithgpu.com/'},{'title':'量子位','url':'https://www.qbitai.com/'}];
	  		for(var index in list){
	  			$('.fast-url').append('<a target="_blank" href="'+list[index]['url']+'" class="mr-10"><i class="layui-icon layui-icon-right"></i>'+list[index]['title']+'</a>');
	  		}
	  	}
	  	
	  	function loadAction(){
	 		ajax.remoteCall("/yq-work/webcall?action=ProjectDataDao.myProjectAction",{},function(result){
	 			var list = result.data;
	 			var tmp = $.templates('#action-list');
	 			var html = tmp.render({data:list});
	 			$('.action').html(html);
	 		});
	 	}
	  	
	  	function dateShow(time){
	  		var result = layui.util.timeAgo(time, true);
	  		return result;
	  	}
	  	
	  	function editWorkhour(){
	  		popup.openTab({id:'yq_project_add_work_hour',title:'工时上报',url:'/yq-work/pages/project/workhour/workhour-my.jsp?source=home'});
	  	}
	  	
	  	function addWeekly(){
	  		popup.openTab({id:'WoDeZhouBao',title:'填写周报',url:'/yq-work/pages/weekly/weekly-my.jsp'});
	  	}
	  	
	  	function addVersion(){
	  		popup.openTab({id:'BanBenFaBu',title:'版本发布',url:'/yq-work/pages/ops/version-list.jsp'});
	  	}
	  	
	  	function createTask(){
	  		popup.openTab({id:'yq_task_apply',title:'创建任务',url:'/yq-work/pages/task/task-apply.jsp',data:{source:'projectIndex'}});
	  	}
	  	
	  	
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>