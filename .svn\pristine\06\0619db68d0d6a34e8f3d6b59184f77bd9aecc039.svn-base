package com.yunqu.work.servlet.ehr;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.Globals;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;
import com.yunqu.work.base.Constants;
/**
 * <AUTHOR>
 *
 */
@WebServlet("/servlet/kaoqin/*")
@MultipartConfig
public class KaoqinServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	public EasyResult actionForManualSign() {
		JSONObject params = getJSONObject();
		try {
			int signTimeType = params.getIntValue("signTimeType");
			EasyRecord record = new EasyRecord("YQ_KQ_SIGN","DK_DATE","USER_ID");
			record.set("IP", WebKit.getIP(getRequest()));
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("USER_ID", getUserId());
			record.set("DK_DATE", EasyCalendar.newInstance().getDateInt());
			
			boolean existData  = this.getQuery().update(record);
			if(!existData) {
				record.set("KQ_ID",RandomKit.uuid());
				record.set("REMARK", "网页打卡");
				record.set("DEPT_ID", getDeptId());
				record.set("USER_NAME", getRemoteUser());
				record.set("STAFF_ID", getStaffInfo().getStaffNo());
				record.set("DEPT", getStaffInfo().getDeptName());
				this.getQuery().save(record);
			}
			JSONObject cityInfo = params.getJSONObject("city");
			String address  = cityInfo.getString("province")+cityInfo.getString("city")+cityInfo.getString("district")+params.getString("address")+"("+params.getString("name")+")";
			
			if(signTimeType==0) {
				boolean bl = this.getQuery().queryForExist("select count(1) from YQ_KQ_SIGN where DK_DATE = ? and USER_ID = ? and SIGN_IN <>''",EasyCalendar.newInstance().getDateInt(),getUserId());
				if(bl) {
					return EasyResult.fail("今日已签到过，请勿重复");
				}
				record.set("ADDRESS_IN", address);
				record.set("SIGN_IN", EasyDate.getCurrentDateString("HH:mm"));
				this.getQuery().update(record);
			}else {
				record.set("ADDRESS_OUT", address);
				record.set("SIGN_OUT", EasyDate.getCurrentDateString("HH:mm"));
				this.getQuery().update(record);
			}
		} catch (SQLException e) {
			this.getLogger().error(null,e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForUploadFile()  throws SQLException, IOException, ServletException {
		EasyResult result = new EasyResult();
		try {
			Part part=this.getFile("kqData");
			String filename = new String(getFilename(part).getBytes(), "UTF-8"); 
			String separator = File.separator;
			String path=Globals.SERVER_DIR+File.separator+"kaoqin";
			if (!new File(path).exists()) {
				new File(path).mkdirs();
			}
			File tmpWarFile = new java.io.File(path + separator + EasyDate.getCurrentDateString("yyyyMMddHHmmss")+ FileKit.getHouzui(filename));
			if (tmpWarFile.exists()) {
				tmpWarFile.delete();
			}
			
			FileKit.saveToFile(part.getInputStream(), tmpWarFile.getAbsolutePath());
			
		    Workbook workbook = WorkbookFactory.create(part.getInputStream());
		    List<List<String>> list = new ArrayList<>();
		    int num = workbook.getNumberOfSheets();
		    
		    int succCount = 0;
		    int lastCellNum=0;
		    for(int index =0 ;index < num ;index++){
		    	Sheet sheet = workbook.getSheetAt(index);
		    	int maxLine =sheet.getLastRowNum();
		    	for (int ii = 0; ii <= maxLine; ii++) {
		    		List<String> rows = new ArrayList<>();
		    		Row row = sheet.getRow(ii);
		    		lastCellNum=row.getLastCellNum();
		    		if(ii==0){
		    			if(lastCellNum<5){
		    				return EasyResult.fail("excel不匹配!");
		    			}
		    		}
		    		if(row!=null){
		    			for(int j=0;j<lastCellNum;j++){
		    				Cell cell=row.getCell(j);
		    				if(cell!=null){
		    					String val = Utils.getCellValue(cell);
		    					if(StringUtils.isBlank(val)){
		    						rows.add("");
		    					}else{
		    						rows.add(val);
		    					}
		    				}else{
		    					rows.add("");
		    				}
		    			}
		    			list.add(rows);
		    		}
		    	}
		    }
		 
		     Map<Integer,String> fieldNameIndex = new LinkedHashMap<Integer,String>();
		     List<String> headerRow = list.get(0);
		     for(int i=0;i<headerRow.size();i++) {
		    	String name =  headerRow.get(i);
		    	fieldNameIndex.put(i, name);
		     }
		     list.remove(0);
		     
		     
		      Map<String,String> fields=new LinkedHashMap<String,String>();
		      fields.put("考勤号码","STAFF_ID");
		      fields.put("姓名", "USER_NAME");
		      fields.put("日期", "DK_DATE");
		      fields.put("对应时段","DK_RANK");
		      fields.put("签到时间","SIGN_IN");
		      fields.put("签退时间","SIGN_OUT");
		      fields.put("例外情况","REMARK");
		      fields.put("加班时间","OVER_TIME");
		      fields.put("实到","TRUE_TO");
		      fields.put("是否旷工","NOT_WORK");
		      fields.put("迟到时间","LATE_TIME");
		      fields.put("部门", "DEPT");
		      fields.put("出勤时间","SUM_TIME");
		      
		      String batchId = RandomKit.uuid();
		      
			  for(int j=0;j<list.size();j++){
				List<String> strs=list.get(j);
				if(strs.size()>0){
					EasyRecord record=new EasyRecord("YQ_KQ_DATA","STAFF_ID","DK_DATE");
					record.put("BATCH_ID",batchId);
					for(int x=0;x<lastCellNum;x++){
						String title = fieldNameIndex.get(x);
						String field = fields.get(title);
						if(StringUtils.isNotBlank(title)&&StringUtils.notBlank(field)) {
							String value=StringUtils.trimToEmpty(strs.get(x));
							if("DK_DATE".equals(field)) {
							  value = dkDateFormat(value);	
							  record.set("DATE_ID", value);
							  record.set("MONTH_ID", value.substring(0, 6));
							}
							if("STAFF_ID".equals(field)&&"4".equals(value)) {
								value = "100294";
							}
							record.set(field, value);
						}
					}
					boolean b=this.getQuery().update(record);
					if(!b){
						record.set("KQ_ID", RandomKit.uuid());
						getQuery().save(record);
						succCount++;
					}
			   }
		    }
			  
			EasyRecord record=new EasyRecord("YQ_KQ_BATCH","BATCH_ID");
			record.setPrimaryValues(batchId);
			record.set("upload_time", EasyDate.getCurrentDateString());
			record.set("file_name",filename);
			record.set("excel_count",succCount);
			record.set("file_path",tmpWarFile.getAbsolutePath());
			record.set("upload_by",getRemoteUser());
			this.getQuery().save(record);
			
			this.getQuery().executeUpdate("update yq_kq_batch t1 set t1.kq_scope = CONCAT((select min(DK_DATE) from yq_kq_data t2 where t2.batch_id = t1.batch_id),'~',(select max(DK_DATE) from yq_kq_data t2 where t2.batch_id = t1.batch_id)) where t1. batch_id = ?", batchId);
			
			this.getQuery().executeUpdate("update yq_kq_data t1,"+Constants.DS_MAIN_NAME+".yq_staff_info t2 set t1.user_id = t2.staff_user_id where t1.STAFF_ID = t2.staff_no and t1.batch_id = ?",batchId);
			
			this.getQuery().executeUpdate("update yq_kq_data t1,"+Constants.DS_MAIN_NAME+".easi_dept_user t2 set t1.dept_id = t2.dept_id where t1.user_id = t2.user_id and t1.batch_id = ?",batchId);
			
			result.put("state", 1);
			result.put("msg", "上传成功!");
			part.delete();
			return result;
		} catch (Exception e) {
			this.error(e.getMessage(), e);
			result.addFail(e.getMessage());
			return result;
		}finally {
			
		}
	}
	
    private String dkDateFormat(String dateStr) {
    	try {
    		SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");  
    		SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");  
    		Date date = sdf.parse(dateStr);
    	    return sdf2.format(date);
    	} catch (Exception e) {  
    		e.printStackTrace();  
    	}
    	return "";  
    }
	
	 private String getFilename(Part part) {  
        String contentDispositionHeader = part.getHeader("content-disposition");  
        String[] elements = contentDispositionHeader.split(";");  
        for (String element : elements) {  
            if (element.trim().startsWith("filename")) {  
                return element.substring(element.indexOf('=') + 1).trim().replace("\"", "");  
            }  
        }  
        return null;  
	}

	 public EasyResult actionForDelBatch(){
		 String batchId=getJsonPara("batchId");
		 try {
			this.getQuery().executeUpdate("delete from yq_kq_data where batch_id = ?", batchId);
			this.getQuery().executeUpdate("delete from yq_kq_batch where batch_id = ?", batchId);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		 return EasyResult.ok();
	 }
}





