.remindInfo td:nth-child(1){
	vertical-align: top;
}
.remindInfo td:nth-child(2){
	line-height: 26px;	
}

.layui-menu-body-title{
	font-size: 13px;
    color: #666;
}
.flow-title{
	font-size: 20px;
	height: 70px;
	line-height: 60px;
	text-align: center;
}
.flow-header{
    margin:-10px -10px 8px;
    height: 50px;
    line-height: 50px;
    background-color: #fff;
    padding: 0px 15px;
    border-bottom: 1px solid #ccc;
}
@media screen and (max-width: 768px){
	.flow-header {
        height: auto;
	    margin: 0px;
	    line-height: 40px;
	    margin-bottom: 5px;
	}
	.flow-title{
		font-size: 14px;
	}
	.input-group{display: inline-table;}
	.ml-10 {
   	   margin-left: 5px;
	}
	
}
#flowForm .file-div{
	float: inherit;
}
#flowForm .copy-btn{
	display: none;
}
#flowForm td:hover .copy-btn{
	display: block;
}
#flowForm .comment:hover{text-decoration: underline;cursor: pointer;color:#0f92cd;}

.flow-approve{
    height: 60px;
    width: 60px;
    line-height: 60px;
    position: absolute;
    text-align: center;
    right: 10px;
    top: 0px;
    border: 1px solid #444;
    border-radius: 50%;
}

.flow-state{
	position: absolute;
    right: 10px;
    top: 10px;
}
.flow-state img{
    width: 150px;
    height: 150px;
    aspect-ratio: auto 150 / 150;
}

.flow-table{
	/* page-break-after:always; */
	border-bottom: 1px solid #a9b7c1;
	border-right: 1px solid #a9b7c1;
}
.flow-table td{
	border-top: 1px solid #a9b7c1!important;
	border-left: 1px solid #a9b7c1!important;
}

.approve-table td,.approve-table th{
    border: 1px solid #a9b7c1!important;
}

@media print{
	body{
		font-family: '宋体';
		/* font-size:18px;
		transform: scale(1);
		-webkit-transform: scale(1); */
	}
	.ibox-content{
		border:none;
	}  
	.required:before{
		content:'';
	}
	.layui-layer-tips{
		display: none;
	}
	.label{
		color: #444;
		background-color: #fff;
		padding:0px;
		font-size: 100%;
		border: none;
	} 
	.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control{
		background-color: #ffffff;
		border-color: #fff;
	}
	select{
		-webkit-appearance:none;
		-moz-appearance:none;
		appearance:none;
	}
	.layui-fixbar .layui-icon{
		display: none;
	}
	.flow-title{
		font-weight: 700;
		font-size: 22px;
	}
}
 #flowForm .layui-table td,#flowForm .layui-table th{
	padding: 10px 6px!important;
}

.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th{
	color: #050505;
}
.container-fluid {
    padding: 5px 10px;
}
.ibox-content{
	padding: 5px;
} 


.ibox-card-body,
.ibox-card-header,
.ibox-form-label,
.ibox-form-mid,
.ibox-form-select,
.ibox-input-block,
.ibox-input-inline,
.ibox-textarea {
    position: relative
}


.w-e-full-screen-editor{
    z-index: 9999999999!important;
}

.Wdate {
    background: #fff url(/easitline-static/lib/My97DatePicker/skin/date.png) no-repeat right;
}
		
.Select {
    background: #fff url(/yq-work/static/images/select.png) no-repeat right;
}
		
.layui-table[lay-size="sm"] td, .layui-table[lay-size="sm"] th {
    font-size: 13px;
    padding: 8px 10px;
}
.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th{
	color: #363839;
}

.layui-table td, .layui-table th {
    position: relative;
    padding: 8px 12px;
    min-height: 18px;
    line-height: 18px;
    font-size: 13px;
    color:#222;
}
.layui-table-click{
	 border-color: #79b7e7;
     background: #f1f1f1;
     color: #e17009
}
.layui-table-click td{
     color: #e17009
}

.select2-container--bootstrap .select2-selection{
	font-size: 12px;
}
.select2-container--bootstrap .select2-results__option{padding: 6px;}

/** 控制表格单元格换行 **/
.tableSelect .layui-table-cell {
	height: auto;
	word-break: normal;
 	display: block;
 	white-space: pre-wrap;
 	word-wrap: break-word;
 	overflow: hidden;
}
.tableSelect{
   z-index: 9999999999!important;
}	
	
/*定义滚动条宽高及背景，宽高分别对应横竖滚动条的尺寸*/
*::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: rgba(255, 255, 255, 0);
}

/*定义滚动条的轨道，内阴影及圆角*/
*::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: rgba(230, 230, 230, 0.05);
}

*:hover::-webkit-scrollbar-track {
  background-color: rgba(230, 230, 230, 0.5);
}

/*定义滑块，内阴影及圆角*/

*::-webkit-scrollbar-thumb {
  height: 20px;
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  background-color: rgba(216, 216, 216, 0.4);
  transition: background-color 1s;
}

*:hover::-webkit-scrollbar-thumb {
  background-color: rgba(216, 216, 216, 1);
}

*::-webkit-scrollbar-thumb:hover {
  background-color: rgba(190, 190, 190, 1);
}

*::-webkit-scrollbar-thumb:active {
  background-color: rgba(160, 160, 160, 1);
}

.layui-table-cell{padding: 0px 6px;font-size: 13px;}

.fly-panel-title {
    position: relative;
    height: 42px;
    line-height: 42px;
    padding: 0 15px;
    border-bottom: 1px dotted #E9E9E9;
    color: #333;
    border-radius: 2px 2px 0 0;
    font-size: 14px;
}

.fly-filter a{padding: 0 8px; color: #666;}
.fly-filter a.layui-this{color: #5FB878;}
.fly-filter .fly-mid{margin: 0 8px;}
.fly-filter-right{position: absolute; right: 10px; top: 0;}
.tooltip.top{z-index: 9999999999999999999999;}
ul{list-style: none;margin: 0;padding: 0;}
.ibox-card {
    margin-bottom: 15px;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0,.05)
}
.layui-table-tips-c{box-sizing:content-box;}

.ibox-card:last-child {
    /*margin-bottom: 0*/
}

.ibox-card-header {
    height: 42px;
    line-height: 42px;
    padding: 0 15px;
    border-bottom: 1px solid #f6f6f6;
    color: #333;
    border-radius: 2px 2px 0 0;
    font-size: 14px
}

.ibox-bg-black,
.ibox-bg-blue,
.ibox-bg-cyan,
.ibox-bg-green,
.ibox-bg-orange,
.ibox-bg-red {
    color: #fff !important
}

.ibox-card-body {
    padding: 0px 15px;
    line-height: 24px;
    overflow-y: auto;
}

.ibox-card-body .ibox-table {
    margin: 5px 0
}

.ibox-card .ibox-tab {
    margin: 0
}

.ibox-card .swiper-container{
    /*overflow-y: inherit;*/
}
.ibox-card .swiper-slide{overflow: auto;}
.ibox-card .swiper-pagination{
    position: absolute;
    right: 10px;
    bottom: 13px;
}

.ibox-card .ibox-card-header .swiper-pagination-bullets .swiper-pagination-bullet{
    margin: 0 4px;
    width: 10px;height: 10px;
}


.ibox-card-badge,.ibox-index .ibox-card-btn-group,.ibox-index .ibox-card-span-color,.ibox-index .ibox-card-span-icon {
    position: absolute;
    right: 15px;
    top:11px;
}
.ibox-card-span-icon{
    line-height: initial;
    position: absolute;
    right: 15px;
    top: 50%;
    margin-top: 1px;
}
/* tab */
.ibox-card-header .nav-tabs{
    border-bottom: 0;
}
.ibox-card-header .nav-tabs>li {
    float: left;
    margin-bottom: 0;
}

.ibox-card-header .nav-tabs>li>a:hover {
    border-color: #eee #eee #ddd;
    background-color: transparent;
}

.ibox-card-header .nav-tabs>li.active>a,.ibox-index .ibox-card-header .nav-tabs>li.active>a:focus,.ibox-index .ibox-card-header .nav-tabs>li.active>a:hover {
    color: #555;
    cursor: default;
    background-color: #fff;
    border: 0; 
    border-bottom: 2px solid transparent;
    border-bottom-color: #3cf;
}

.ibox-card-header .nav-tabs>li>a{
    border: 0; 
    border-bottom: 2px solid transparent;
}

.ibox-card-backlog{

}

.ibox-card-backlog-body:hover {
    background-color: #f2f2f2;
    color: #888;
}

.ibox-card-backlog .ibox-card-backlog-body {
    display: block;
    padding: 10px 15px;
    background-color: #f8f8f8;
    color: #999;
    border-radius: 2px;
    transition: all .3s;
    -webkit-transition: all .3s;
    text-decoration: none;
    cursor: pointer;
}

.ibox-card-backlog-body h3 {
    padding-bottom: 10px;
    font-size: 12px;
    margin: 0;
}

.ibox-card-backlog-body p cite {
    font-style: normal;
    font-size: 30px;
    font-weight: 300;
    color: #337ab7;
}

.col-space10>* {
    padding: 5px;
}
.ibox-card-shortcut{text-align: center;}
.ibox-card-shortcut>.row,.ibox-card-backlog>.row{
    margin: 0 -7px;
}
.ibox-card-shortcut li:hover .ibox-card-icon {
    background-color: #f2f2f2;
}
.ibox-card-shortcut li .ibox-card-icon {
    display: inline-block;
    width: 100%;
    height: 55px;
    line-height: 55px;
    text-align: center;
    border-radius: 2px;
    font-size: 30px;
    background-color: #F8F8F8;
    color: #333;
    transition: all .3s;
    -webkit-transition: all .3s;
}
.ibox-card-shortcut li a{text-decoration: none;cursor: pointer;}
.ibox-card-shortcut li cite {
    position: relative;
    top: 2px;
    display: block;
    color: #666;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 14px;
    list-style: none;
    font-style: normal;
}

.ibox-index .col-lg-1,.ibox-index .col-lg-10,.ibox-index .col-lg-11,.ibox-index .col-lg-12,.ibox-index .col-lg-2,.ibox-index .col-lg-3,.ibox-index .col-lg-4,.ibox-index .col-lg-5,.ibox-index .col-lg-6,.ibox-index .col-lg-7,.ibox-index .col-lg-8,.ibox-index .col-lg-9,.ibox-index .col-md-1,.ibox-index .col-md-10,.ibox-index .col-md-11,.ibox-index .col-md-12,.ibox-index .col-md-2,.ibox-index .col-md-3,.ibox-index .col-md-4,.ibox-index .col-md-5,.ibox-index .col-md-6,.ibox-index .col-md-7,.ibox-index .col-md-8,.ibox-index .col-md-9,.ibox-index .col-sm-1,.ibox-index .col-sm-10,.ibox-index .col-sm-11,.ibox-index .col-sm-12,.ibox-index .col-sm-2,.ibox-index .col-sm-3,.ibox-index .col-sm-4,.ibox-index .col-sm-5,.ibox-index .col-sm-6,.ibox-index .col-sm-7,.ibox-index .col-sm-8,.ibox-index .col-sm-9,.ibox-index .col-xs-1,.ibox-index .col-xs-10,.ibox-index .col-xs-11,.ibox-index .col-xs-12,.ibox-index .col-xs-2,.ibox-index .col-xs-3,.ibox-index .col-xs-4,.ibox-index .col-xs-5,.ibox-index .col-xs-6,.ibox-index .col-xs-7,.ibox-index .col-xs-8,.ibox-index .col-xs-9 {
    position: relative;
    min-height: 1px;
    padding-right: 7px;
    padding-left: 7px;
}

.badge-red{background-color: red!important;  color: #fff!important;}

/* 信息 */
.ibox-card-userinfo{
    position: relative;
    min-height: 100px;
    padding: 10px;
    padding-left: 100px;
}

.ibox-card-userinfo .user-avator{
    position: absolute;
    left: 10px;
    top:10px;
    width: 80px;
    height: 80px;
}

.ibox-card-userinfo .user-avator img{
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.ibox-card-userinfo .user-info{
    padding-left: 10px;
}
.ibox-card-userinfo .user-info h4{
    margin-top: 7px;
    margin-bottom: 7px;
}
.ibox-card-userinfo .user-info p{
    font-size: 14px;
    margin:0;
    line-height: 25px;
}

.ibox-card-list-group .list-group-item{
    border: 0;
    padding: 15px;
}
.ibox-card-list-group .list-group-item:before{
    position: absolute;
    content: '';
    background-color: #f2f2f2;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
}
.ibox-card-list-group a.list-group-item:focus, .ibox-card-list-group a.list-group-item:hover,.ibox-card-list-group button.list-group-item:focus,.ibox-card-list-group button.list-group-item:hover{
    background-color: #f8f8f8;
}

.ibox-card-list-group .list-group-item.active:after,.ibox-card-list-group a.list-group-item:focus:after, .ibox-card-list-group a.list-group-item:hover:after,.ibox-card-list-group button.list-group-item:focus:after,.ibox-card-list-group button.list-group-item:hover:after{
    background-color:  #337ab7;
    position: absolute;
    content: '';
    left: 0;
    bottom: 0;
    top:0;
    width: 3px;
}

.ibox-card-list-group .list-group-item.active,.ibox-card-list-group .list-group-item.active:focus,.ibox-card-list-group .list-group-item.active:hover{
    color: #337ab7;
    background-color: #f8f8f8;
}
.list-group-item:first-child,.list-group-item:last-child{border-radius: 0}


.file-div{ 
    display: inline-block;
    background-color: #f5f5f5;
    padding: 2px 8px;
    line-height: 25px;
    float: left;
    font-size:13px;
    margin-top: 3px;
    margin-right: 5px;
}
.file-div i{color: red;margin-left: 5px;cursor: pointer;}
.ibox-index{padding: 5px 10px;}
.multiselect-container,.select2-container{z-index: 999999999999;};
.select2-container{width: 100%!important;};
.tooltip{z-index: 99999999999999};
.layui-table-tool a{
    padding-right: 6px;
    padding-left: 6px;
    font-weight: 400;
    color: #3c4353;
    text-shadow: none;
    cursor: pointer;
    background: 0;
}
.ib{display: inline-block;}
.hover{
	opacity:0;
}
.p_hover:hover .hover{
	opacity:1;
}
.create_new .create_new-content .publish .publishBtn {
    display: block;
    float: right;
    width: 100px;
    height: 32px;
    background: rgba(31,93,234,1);
    box-shadow: 0 4px 8px 0 rgba(31,93,234,.35);
    border-radius: 100px;
    text-align: center;
    line-height: 32px;
    color: #fff;
    cursor: pointer;
    margin-top: 10px;
    font-size: 13px;
}

.h-title{
	font-size: 12px;
  		color: #777;
}
.h-value{
	min-height: 14px;
    margin-top: 3px;
    font-size: 13px;
    color: #333;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
.layui-tab-item {
	padding: 0px 15px;
}

@media screen and (max-width:768px){
 	.container-fluid{
 		padding: 5px;
 	}
 	.ibox-title h5{display: none;}
 	.ibox-content{padding: 15px 5px;}
 	.layui-table-cell{padding: 0px 2px;font-size: 12px;}
}

.layui-fixbar .layui-icon{
    border-radius: 50%;
    font-size: 24px;
    opacity: .55;
}

