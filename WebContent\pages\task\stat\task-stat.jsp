<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>项目管理</title>
	<style>
	    tr .layui-btn{opacity:0;}
		tr:hover .layui-btn{opacity:1;}
		.layui-table td, .layui-table th{padding: 9px 6px;}
		.layui-table input{width: 100%!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="editForm">
			<div class="ibox">
				<div class="ibox-title mb-15 clearfix">
	          		 <div class="form-group">
	          		     <h5><span class="glyphicon glyphicon-inbox"></span> 任务排行榜</h5>
					      <div class="input-group input-group-sm">
							 <span class="input-group-addon">时间区间</span>	
							 <select name="day" onchange="loadData()" class="form-control input-sm">
		                    		<option value="7">7天内</option>
		                    		<option value="30">一月内</option>
		                    		<option value="60">2个月内</option>
		                    		<option value="90" selected="selected">3个月内</option>
		                    		<option value="180">近半年</option>
		                    		<option value="365">近1年</option>
		                    		<option value="730">近2年</option>
	                    	</select>
					     </div>
	          	     </div>
	              </div> 
	             <div class="layui-row layui-col-space10">
	            	 <div class="layui-col-md3" data-mars="TaskDao.taskV1" data-template="v1Tpl"></div>
					  <div class="layui-col-md3" data-mars="TaskDao.taskV2" data-template="v2Tpl"> </div>
					  <div class="layui-col-md3" data-mars="TaskDao.taskV3" data-template="v3Tpl"></div>
					  <div class="layui-col-md3" data-mars="TaskDao.taskV4" data-template="v4Tpl"></div>
					  <div class="layui-col-md3" data-mars="TaskDao.taskD1" data-template="d1Tpl"></div>
					  <div class="layui-col-md3" data-mars="TaskDao.taskD2" data-template="d2Tpl"></div>
					  <div class="layui-col-md3" data-mars="TaskDao.taskD3" data-template="d3Tpl"></div>
					  <div class="layui-col-md3" data-mars="TaskDao.taskD4" data-template="d4Tpl"></div>
	             </div>
				<script type="text/x-jsrender" id="v1Tpl">
					<div class="layui-card">
					  <div class="layui-card-header">发起数排行榜</div>
					  <div class="layui-card-body">
						<table class="layui-table">
						  <thead>
							<tr>
							  <th>序号</th>
							  <th>发起人</th>
							  <th>任务数</th>
							</tr> 
						  </thead>
						  <tbody>
						  {{for data}}
							<tr>
							  <td>{{:#index+1}}</td>
							  <td>{{call:CREATOR fn='getUserName'}}</td>
							  <td>{{:COUNT}}</td>
							</tr>
						  {{/for}}
						  </tbody>
						</table>
						
					  </div>
					</div>
				</script>
				<script type="text/x-jsrender" id="v2Tpl">
					<div class="layui-card">
					  <div class="layui-card-header">处理数排行榜</div>
					  <div class="layui-card-body">
						<table class="layui-table">
						  <thead>
							<tr>
							  <th>序号</th>
							  <th>处理人</th>
							  <th>任务数</th>
							</tr> 
						  </thead>
						  <tbody>
						  {{for data}}
							<tr>
							  <td>{{:#index+1}}</td>
							  <td>{{call:ASSIGN_USER_ID fn='getUserName'}}</td>
							  <td>{{:COUNT}}</td>
							</tr>
						  {{/for}}
						  </tbody>
						</table>
						
					  </div>
					</div>
				</script>
				<script type="text/x-jsrender" id="v3Tpl">
					<div class="layui-card">
					  <div class="layui-card-header">待办数排行榜</div>
					  <div class="layui-card-body">
						<table class="layui-table">
						  <thead>
							<tr>
							  <th>序号</th>
							  <th>处理人</th>
							  <th>任务数</th>
							</tr> 
						  </thead>
						  <tbody>
						  {{for data}}
							<tr>
							  <td>{{:#index+1}}</td>
							  <td>{{call:ASSIGN_USER_ID fn='getUserName'}}</td>
							  <td>{{:COUNT}}</td>
							</tr>
						  {{/for}}
						  </tbody>
						</table>
						
					  </div>
					</div>
				</script>
				<script type="text/x-jsrender" id="v4Tpl">
					<div class="layui-card">
					  <div class="layui-card-header">超时未办结排行榜</div>
					  <div class="layui-card-body">
						<table class="layui-table">
						  <thead>
							<tr>
							  <th>序号</th>
							  <th>处理人</th>
							  <th>任务数</th>
							</tr> 
						  </thead>
						  <tbody>
						  {{for data}}
							<tr>
							  <td>{{:#index+1}}</td>
							  <td>{{call:ASSIGN_USER_ID fn='getUserName'}}</td>
							  <td>{{:COUNT}}</td>
							</tr>
						  {{/for}}
						  </tbody>
						</table>
						
					  </div>
					</div>
				</script>
				<script type="text/x-jsrender" id="d1Tpl">
					<div class="layui-card">
					  <div class="layui-card-header">部门创建的任务数</div>
					  <div class="layui-card-body">
						<table class="layui-table">
						  <thead>
							<tr>
							  <th>序号</th>
							  <th>部门</th>
							  <th>任务数</th>
							</tr> 
						  </thead>
						  <tbody>
						  {{for data}}
							<tr>
							  <td>{{:#index+1}}</td>
							  <td>{{:DEPT_NAME}}</td>
							  <td>{{:COUNT}}</td>
							</tr>
						  {{/for}}
						  </tbody>
						</table>
						
					  </div>
					</div>
				</script>
				<script type="text/x-jsrender" id="d2Tpl">
					<div class="layui-card">
					  <div class="layui-card-header">部门负责的任务数</div>
					  <div class="layui-card-body">
						<table class="layui-table">
						  <thead>
							<tr>
							  <th>序号</th>
							  <th>部门</th>
							  <th>任务数</th>
							</tr> 
						  </thead>
						  <tbody>
						  {{for data}}
							<tr>
							  <td>{{:#index+1}}</td>
							  <td>{{:DEPT_NAME}}</td>
							  <td>{{:COUNT}}</td>
							</tr>
						  {{/for}}
						  </tbody>
						</table>
						
					  </div>
					</div>
				</script>
				<script type="text/x-jsrender" id="d3Tpl">
					<div class="layui-card">
					  <div class="layui-card-header">部门待办任务数</div>
					  <div class="layui-card-body">
						<table class="layui-table">
						  <thead>
							<tr>
							  <th>序号</th>
							  <th>部门</th>
							  <th>任务数</th>
							</tr> 
						  </thead>
						  <tbody>
						  {{for data}}
							<tr>
							  <td>{{:#index+1}}</td>
							  <td>{{:DEPT_NAME}}</td>
							  <td>{{:COUNT}}</td>
							</tr>
						  {{/for}}
						  </tbody>
						</table>
						
					  </div>
					</div>
				</script>
				<script type="text/x-jsrender" id="d4Tpl">
					<div class="layui-card">
					  <div class="layui-card-header">部门超时任务数</div>
					  <div class="layui-card-body">
						<table class="layui-table">
						  <thead>
							<tr>
							  <th>序号</th>
							  <th>部门</th>
							  <th>任务数</th>
							</tr> 
						  </thead>
						  <tbody>
						  {{for data}}
							<tr>
							  <td>{{:#index+1}}</td>
							  <td>{{:DEPT_NAME}}</td>
							  <td>{{:COUNT}}</td>
							</tr>
						  {{/for}}
						  </tbody>
						</table>
						
					  </div>
					</div>
				</script>
			</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		function loadData(){
			$("#editForm").render({success:function(){
				
			}});
		}
		$(function(){
			loadData();
		});
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>