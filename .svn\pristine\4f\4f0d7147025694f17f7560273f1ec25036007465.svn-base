package com.yunqu.work.model;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseModel;

public class MessageModel extends AppBaseModel {
   private static final long serialVersionUID = 1L;
	
   private String msgId;
   private int msgType;
   private String receiver;
   private String contents;
   private int msgState;
   private String createTime;
   private String fkId;
   private String readTime;
   private String url;
   private int senderType;
   private String sender;
   
   private String title;
   private String[] cc;
   private String desc;
   private String tplName;
   private JSONObject data;
   
   
   
   private String data1;
   private String data2;
   private String data3;
   private String data4;
   
   
    public MessageModel(){
	   setTableInfo("YQ_MESSAGE", "MSG_ID");
   }

	public String getMsgId() {
		return msgId;
	}
	
	public void setMsgId(String msgId) {
		this.msgId = msgId;
		set("MSG_ID", msgId);
	}
	
	public int getMsgType() {
		return msgType;
	}
	/**
	 * 如: 2000:系统消息 ,2001:消息通知, 2002：私信
	 * @param msgType
	 */
	public void setMsgType(int msgType) {
		this.msgType = msgType;
		set("MSG_TYPE", msgType);
	}
	
	public String getReceiver() {
		return receiver;
	}
	
	public void setReceiver(String receiver) {
		this.receiver = receiver;
		set("RECEIVER", receiver);
	}
	
	public String getContents() {
		return contents;
	}
	
	public void setContents(String contents) {
		this.contents = contents;
	    this.setTitle(contents);
		set("CONTENTS", contents);
	}
	
	public int getMsgState() {
		return msgState;
	}
	/**
	 * 发送状态： 0 未发送 1 已发送 2 未读 3 已读
	 * @param msgState
	 */
	public void setMsgState(int msgState) {
		this.msgState = msgState;
		set("MSG_STATE", msgState);
	}
	
	public String getCreateTime() {
		return createTime;
	}
	
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
		set("CREATE_TIME", createTime);
	}
	
	public String getFkId() {
		return fkId;
	}
	
	public void setFkId(String fkId) {
		this.fkId = fkId;
		set("FK_ID", fkId);
	}
	
	public String getReadTime() {
		return readTime;
	}
	
	public void setReadTime(String readTime) {
		this.readTime = readTime;
		set("READ_TIME", readTime);
	}
	
	public String getUrl() {
		return url;
	}
	
	public void setUrl(String url) {
		this.url = url;
		set("URL", url);
	}
	
	public int getSenderType() {
		return senderType;
	}
	
	public void setSenderType(int senderType) {
		this.senderType = senderType;
		set("SENDER_TYPE", senderType);
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
		this.set("SENDER", sender);
	}

	
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public JSONObject getData() {
		return data;
	}

	public void setData(JSONObject data) {
		this.data = data;
	}

	public String getTplName() {
		return tplName;
	}

	public void setTplName(String tplName) {
		this.tplName = tplName;
	}

	public String[] getCc() {
		return cc;
	}

	public void setCc(String[] cc) {
		this.cc = cc;
	}

	public String getData1() {
		return data1;
	}

	public void setData1(String data1) {
		this.data1 = data1;
	}

	public String getData2() {
		return data2;
	}

	public void setData2(String data2) {
		this.data2 = data2;
	}

	public String getData3() {
		return data3;
	}

	public void setData3(String data3) {
		this.data3 = data3;
	}

	public String getData4() {
		return data4;
	}

	public void setData4(String data4) {
		this.data4 = data4;
	}
	
   

}
