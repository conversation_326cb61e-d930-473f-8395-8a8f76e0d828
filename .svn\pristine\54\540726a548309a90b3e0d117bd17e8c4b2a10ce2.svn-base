package com.yunqu.work.servlet.work;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/project/conf")
public class ProjectConfServlet extends AppBaseServlet {
	private static final long serialVersionUID = -6366774540430826942L;

	public EasyResult actionForAddTeamUser(){
		JSONObject params = getJSONObject();
		String projectId = params.getString("projectId");
		String teamStr = params.getString("teamIds");
		int succcess = 0 ;
		if(StringUtils.notBlank(teamStr)){
			String[] teams = teamStr.split(",");
			for(int i=0;i<teams.length;i++){
				String userId=teams[i];
				EasyRecord record=new EasyRecord("YQ_PROJECT_TEAM", "PROJECT_ID","USER_ID");
				record.setPrimaryValues(projectId,userId);
				record.set("JOIN_TIME", EasyDate.getCurrentDateString());
				try {
					this.getQuery().save(record);
					succcess ++;
				} catch (SQLException e) {
					this.error(e.getMessage(), e);
				}
			}
		}else {
			return EasyResult.fail("请选择");
		}
		return EasyResult.ok(succcess,succcess+"个成员新增成功");
	}
	
	public EasyResult actionForUpdateTeamAdminFlag(){
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("YQ_PROJECT_TEAM","PROJECT_ID","USER_ID");
		try {
			record.set("PROJECT_ID", params.getString("projectId"));
			record.set("USER_ID", params.getString("userId"));
			record.set("ADMIN_FLAG", params.getIntValue("adminFlag"));
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForEditNotice(){
		JSONObject params = getJSONObject();
		EasyRecord record = new EasyRecord("YQ_PROJECT","PROJECT_ID");
		try {
			record.set("PROJECT_ID", params.getString("projectId"));
			record.set("PROJECT_NOTICE", params.getString("content"));
			record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			record.set("UPDATE_BY", getUserName());
			this.getQuery().update(record);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
}
