<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>客户管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		.layui-table-cell{padding: 0 6px;}
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="custMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>发票管理</h5>
						 <div class="input-group input-group-sm" style="width: 140px;">
					 		<span class="input-group-addon">客户id</span>
						 	<input class="form-control input-sm" name="custId">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" class="btn btn-sm btn-default" onclick="CustMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-default" style="margin-right: 5px" onclick="CustMgr.query()"> 刷新</button>
							 <button type="button" class="btn btn-sm btn-info" onclick="CustMgr.add()">+ 新建发票抬头</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="custMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
	  			{{if currentUserId == CREATOR || isSuperUser }}
					<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="CustMgr.edit">编辑</a>
				{{/if}}
				<a class="layui-btn layui-btn-normal layui-btn-xs layui-hide" lay-event="CustMgr.detail">查看</a>
		</script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var CustMgr={
			init:function(){
				$("#custMgrForm").initTable({
					mars:'InvoiceDao.titleList',
					id:'custMgrTable',
					autoSort:false,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left',
						fixed:'left'
					 },{
					    field: 'CUST_NAME',
						title: '客户名称',
						align:'left',
						minWidth:180,
						templet:'<div><a href="javascript:;" onclick="CustMgr.custDetail(\'{{d.CUST_ID}}\')">{{d.CUST_NAME}}</a></div>'
					},{
					    field: 'TITLE_NAME',
						title: '开票抬头',
						align:'left',
						minWidth:180
					},{
					    field: 'TAX_NO',
						title: '纳税人识别号',
						align:'center',
						width:110
					},{
					    field: 'BANK_NAME',
						title: '开户行',
						align:'center'
					},{
					    field: 'BANK_NO',
						title: '开户账号',
						align:'center'
					},{
					    field: 'TEL',
						title: '电话',
						align:'center'
					},{
					    field: 'ADDRESS',
						title: '开票地址',
						align:'center'
					},{
						title: '操作',
						align:'center',
						minWidth:80,
						fixed:'right',
						templet:function(row){
							row['currentUserId']=getCurrentUserId();
							row['isSuperUser']=isSuperUser;
						    return renderTpl('bar1',row);
						}
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#custMgrForm").queryData({id:'custMgrTable',jumpOne:true});
			},
			edit:function(data){
				var  titleId = data['TITLE_ID'];
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/crm/invoice/title/title-edit.jsp',title:'编辑抬头',data:{titleId:titleId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),maxmin:true,anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','500px'],url:'${ctxPath}/pages/crm/invoice/title/title-edit.jsp',title:'新增抬头',closeBtn:1});
			},
			custDetail:function(custId){
				var width = $(window).width();
				var w = '80%';
				if(width>1500){
					w = '800px';
				}else if(width<1000){
					w = '100%';
				}else{

				}
				popup.openTab({id:'custDetail',title:'客户详情',type:1,closeBtn:0,shade:false,maxmin:false,anim:0,scrollbar:false,shadeClose:true,offset:'r',area:[w,'100%'],url:'${ctxPath}/pages/crm/cust/cust-detail.jsp',data:{custId:custId}});
			}
		}
		function reloadInvoiceTitleList(){
			CustMgr.query();
		}
		$(function(){
			$("#custMgrForm").render({success:function(){
				CustMgr.init();
			}});
		});
		
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>