package com.yunqu.work.dao.bx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;
import java.text.ParseException;
import com.yunqu.work.utils.DateUtils;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;

@WebObject(name = "BxStatisticDao")
public class BxStatisticDao extends AppDaoContext {

    @WebControl(name = "bxConsoleStat", type = Types.RECORD)
    public JSONObject bxConsoleStat() {
        EasySQL sql = new EasySQL("select ");
        int curYear = EasyCalendar.newInstance().getYear();
        int lastYear = curYear - 1;
        String todayDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
        String lastYearToday = DateUtils.getLastYearTodayDate();
        String yearBeforeLastYearToday = yearBeforeLastYearTodayDate();

        sql.appendLike(curYear, "ROUND(SUM(CASE WHEN bx_date like ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.appendLike(lastYear, "ROUND(SUM(CASE WHEN bx_date like ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR,");
        sql.appendLike(DateUtils.getFullMonthStr(), "ROUND(SUM(CASE WHEN bx_date like ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_MONTH,");
        sql.appendLike(DateUtils.getLastMonthStr(), "ROUND(SUM(CASE WHEN bx_date like ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_MONTH,");
        sql.appendLike(getLastYearLastMonthStr(), "ROUND(SUM(CASE WHEN bx_date like ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_THIS_MONTH,");
        sql.appendLike(getLastYearThisMonthStr(), "ROUND(SUM(CASE WHEN bx_date like ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_LAST_MONTH,");

        sql.append(todayDate, "ROUND(SUM(CASE WHEN bx_date <= ?");
        sql.append(lastYearToday, "AND bx_date >= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_IN_A_YEAR,");
        sql.append(lastYearToday, "ROUND(SUM(CASE WHEN bx_date <= ?");
        sql.append(yearBeforeLastYearToday, "AND bx_date >= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_BEFORE_YEAR,");

        int curQuarter = getQuarterFromMonth(EasyCalendar.newInstance().getMonth());
        int lastQuarter = curQuarter == 1 ? 4 : curQuarter - 1;

        sql.append(DateUtils.getQuarterStart(), "ROUND(SUM(CASE WHEN bx_date >= ?");
        sql.append(DateUtils.getQuarterEnd(), "AND bx_date <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_QUARTER,");
        if (lastQuarter == 4) {
            sql.append(DateUtils.getQuarterStart(lastYear, lastQuarter), "ROUND(SUM(CASE WHEN bx_date >= ?");
            sql.append(DateUtils.getQuarterEnd(lastYear, lastQuarter), "AND bx_date <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_QUARTER,");
        } else {
            sql.append(DateUtils.getQuarterStart(curYear, lastQuarter), "ROUND(SUM(CASE WHEN bx_date >= ?");
            sql.append(DateUtils.getQuarterEnd(curYear, lastQuarter), "AND bx_date <=  ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_QUARTER,");
        }

        sql.append(DateUtils.getQuarterStart(lastYear, curQuarter), "ROUND(SUM(CASE WHEN bx_date >= ?");
        sql.append(DateUtils.getQuarterEnd(lastYear, curQuarter), "AND bx_date <= ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_THIS_QUARTER,");

        if (lastQuarter == 4) {
            sql.append(DateUtils.getQuarterStart(lastYear - 1, lastQuarter), "ROUND(SUM(CASE WHEN bx_date >= ?");
            sql.append(DateUtils.getQuarterEnd(lastYear - 1, lastQuarter), "AND bx_date <=  ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_LAST_QUARTER,");
        } else {
            sql.append(DateUtils.getQuarterStart(lastYear, lastQuarter), "ROUND(SUM(CASE WHEN bx_date >= ?");
            sql.append(DateUtils.getQuarterEnd(lastYear, lastQuarter), "AND bx_date <= ? THEN R_AMOUNT ELSE 0 END), 2) AS LAST_YEAR_LAST_QUARTER,");
        }

        sql.appendLike(EasyCalendar.newInstance().getYear(), " COUNT(DISTINCT CASE WHEN bx_date LIKE ? THEN business_id END) AS COUNT_THIS_YEAR,");
        sql.appendLike(EasyCalendar.newInstance().getYear() - 1, "COUNT(DISTINCT CASE WHEN bx_date LIKE ? THEN business_id END) AS COUNT_LAST_YEAR,");

        sql.append(todayDate, "COUNT(DISTINCT CASE WHEN bx_date <= ?   ");
        sql.append(lastYearToday, "AND bx_date >= ? THEN business_id END) AS COUNT_IN_A_YEAR,");
        sql.append(lastYearToday, "COUNT(DISTINCT CASE WHEN bx_date <= ?   ");
        sql.append(yearBeforeLastYearToday, "AND bx_date >= ? THEN business_id END) AS COUNT_BEFORE_YEAR");

        sql.append("from yq_flow_apply t1 inner join yq_flow_bx_item t2 on t1.apply_id = t2.business_id");
        sql.append("where 1=1");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t1.apply_state = ?");
        return queryForRecord(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "bxMonthlyByAmountDeptType", type = Types.LIST)
    public JSONObject bxMonthlyByAmountDeptType() {
        String startMonth = param.getString("startMonth");
        Integer monthPeriod = param.getIntValue("monthPeriod");
        monthPeriod = monthPeriod == 0 ? 12 : monthPeriod;
        if(StringUtils.isBlank(startMonth)){
            startMonth = DateUtils.getFullMonthStr();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();

        String[] deptType = {"工程研发部门", "销售部门", "管理部门", "sum"};
        JSONObject result = new JSONObject();
        for (String dept : deptType) {
            try {
                calendar.setTime(sdf.parse(startMonth));
            } catch (ParseException e) {
                return EasyResult.fail("日期格式错误");
            }
            EasySQL sql = new EasySQL("select ");
            for (int j = 1; j <= monthPeriod; j++) {
                String month = sdf.format(calendar.getTime());
                String monthName = month.replace("-", "_");
                sql.appendLike(month, "ROUND(SUM(CASE WHEN bx_date like ? THEN R_AMOUNT ELSE 0 END),2)");
                if (j == monthPeriod) {
                    sql.append("AS " + monthName);
                } else {
                    sql.append("AS " + monthName + ",");
                }
                calendar.add(Calendar.MONTH, 1);
            }
            sql.append("from yq_flow_apply t1 inner join yq_flow_bx_item t2 on t1.apply_id = t2.business_id");
            sql.append("where 1=1");
            sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t1.apply_state = ?");
            if (!dept.equals("sum")) {
                sql.append(dept, "and t2.dept_type = ?");
            }
            JSONObject resultTemp = queryForRecord(sql.getSQL(), sql.getParams());
            JSONObject data = resultTemp.getJSONObject("data");
            result.put(dept, data);
        }
        return result;
    }

    /*
     * 搜索可自选统计的维度和报销日期
     */
    @WebControl(name = "searchAmountRank", type = Types.LIST)
    public JSONObject searchAmountRank() {
        String field = param.getString("searchField");
        if (StringUtils.isBlank(field)) {
            return EasyResult.ok();
        }
        String startDate = param.getString("startDate");
        String endDate = param.getString("endDate");
        if (StringUtils.isBlank(startDate)) {
            startDate = DateUtils.getQuarterStart();
        }
        if (StringUtils.isBlank(endDate)) {
            endDate = DateUtils.getQuarterEnd();
        }

        EasySQL sql = getEasySQL("SELECT");
        if (field.equalsIgnoreCase("APPLY_NAME")) {
            sql.append(field + ",");
        } else if (field.equalsIgnoreCase("FEE_TYPE")) {
            sql.append("CASE WHEN FEE_TYPE like '差旅费%' THEN '差旅费' ELSE FEE_TYPE END as FEE_TYPE,");
        } else if (field.equalsIgnoreCase("TRAVEL_FEE")) {
            sql.append("FEE_TYPE as TRAVEL_FEE,");
        } else {
            sql.append(" t1." + field + ",");
        }
        sql.append(startDate, "ROUND(SUM(CASE WHEN bx_date >= ?");
        sql.append(endDate, "AND bx_date <= ? THEN R_AMOUNT ELSE 0 END), 2) AS TOTAL_AMOUNT");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id where 1=1");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        sql.append(param.getString("contractId"), "and t1.contract_id = ?");


        if (field.equalsIgnoreCase("APPLY_NAME")) {
            sql.append("GROUP BY " + field);
        } else if (field.equalsIgnoreCase("FEE_TYPE")) {
            sql.append("GROUP BY CASE WHEN FEE_TYPE LIKE '差旅费%' THEN '差旅费' ELSE FEE_TYPE END");
        } else if (field.equalsIgnoreCase("TRAVEL_FEE")) {
            sql.append("and FEE_TYPE LIKE '差旅费%' ");
            sql.append("GROUP BY FEE_TYPE");
        } else {
            sql.append("GROUP BY t1." + field);
        }
        setOrderBy(sql, " order by TOTAL_AMOUNT desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "deptNameAmountRank", type = Types.LIST)
    public JSONObject deptNameAmountRank() {
        String startMonth = param.getString("deptStartMonth");
        Integer monthPeriod = param.getIntValue("deptMonthPeriod");
        monthPeriod = monthPeriod == 0 ? 12 : monthPeriod;
        if(StringUtils.isBlank(startMonth)){
            startMonth = DateUtils.getFullMonthStr();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(sdf.parse(startMonth));
        } catch (ParseException e) {
            return EasyResult.fail("日期格式错误");
        }
        String startMonth1 = sdf.format(calendar.getTime())+"-01";
        calendar.add(Calendar.MONTH, monthPeriod-1);
        String endMonth1 = sdf.format(calendar.getTime())+"-31";
        calendar.add(Calendar.YEAR, -1);
        String endMonth2 = sdf.format(calendar.getTime())+"-31";
        calendar.add(Calendar.MONTH, -monthPeriod+1);
        String startMonth2 = sdf.format(calendar.getTime())+"-01";

        EasySQL sql = getEasySQL("SELECT t1.DEPT_NAME,");
        sql.append(startMonth1," COUNT(DISTINCT CASE WHEN bx_date >= ? ");
        sql.append(endMonth1, "and bx_date <= ? THEN t2.apply_by END) AS PEOPLE_COUNT,");
        sql.append(startMonth1, "ROUND(SUM(CASE WHEN bx_date >= ? ");
        sql.append(endMonth1, "and bx_date <= ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.append(startMonth2, "ROUND(SUM(CASE WHEN bx_date >= ? ");
        sql.append(endMonth2, "and bx_date <= ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR,");

        sql.append(startMonth1," ROUND( CASE WHEN COUNT(DISTINCT CASE WHEN bx_date >= ? ");
        sql.append(endMonth1, "and bx_date <= ? THEN t2.apply_by END) > 0");
        sql.append(startMonth1,"THEN SUM(CASE WHEN bx_date >= ? ");
        sql.append(endMonth1, "and bx_date <= ? THEN r_amount ELSE 0 END) / ");
        sql.append(startMonth1,"COUNT(DISTINCT CASE WHEN bx_date >= ? ");
        sql.append(endMonth1, "and bx_date <= ? THEN t2.apply_by END) ELSE 0 END ,2 ) AS AVERAGE_THIS_YEAR");

        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id where 1=1");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        sql.append("GROUP BY t1.DEPT_NAME");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "deptNameAverageRank", type = Types.LIST)
    public JSONObject deptNameAverageRank() {
        String startMonth = param.getString("deptStartMonth");
        Integer monthPeriod = param.getIntValue("deptMonthPeriod");
        monthPeriod = monthPeriod == 0 ? 12 : monthPeriod;
        if(StringUtils.isBlank(startMonth)){
            startMonth = DateUtils.getFullMonthStr();
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(sdf.parse(startMonth));
        } catch (ParseException e) {
            return EasyResult.fail("日期格式错误");
        }
        String startMonth1 = sdf.format(calendar.getTime())+"-01";
        calendar.add(Calendar.MONTH, monthPeriod-1);
        String endMonth1 = sdf.format(calendar.getTime())+"-31";

        EasySQL sql = getEasySQL("SELECT t1.DEPT_NAME,");
        sql.append(startMonth1," ROUND( CASE WHEN COUNT(DISTINCT CASE WHEN bx_date >= ? ");
        sql.append(endMonth1, "and bx_date <= ? THEN t2.apply_by END) > 0");
        sql.append(startMonth1,"THEN SUM(CASE WHEN bx_date >= ? ");
        sql.append(endMonth1, "and bx_date <= ? THEN r_amount ELSE 0 END) / ");
        sql.append(startMonth1,"COUNT(DISTINCT CASE WHEN bx_date >= ? ");
        sql.append(endMonth1, "and bx_date <= ? THEN t2.apply_by END) ELSE 0 END ,2 ) AS PERSON_AVERAGE");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id where 1=1");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        sql.append("GROUP BY t1.DEPT_NAME");
        sql.append(" order by PERSON_AVERAGE desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }



    @WebControl(name = "staffAmountRank", type = Types.LIST)
    public JSONObject staffAmountRank() {
        EasySQL sql = getEasySQL("SELECT apply_name,");
        sql.appendLike(EasyCalendar.newInstance().getYear(), "ROUND(SUM(CASE WHEN bx_date like ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.appendLike(EasyCalendar.newInstance().getYear() - 1, "ROUND(SUM(CASE WHEN bx_date like ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append("from yq_flow_apply t1 inner join yq_flow_bx_item t2 on t1.apply_id = t2.business_id where 1=1");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t1.apply_state = ?");
        sql.append("GROUP BY apply_name");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "feeTypeAmountRank", type = Types.LIST)
    public JSONObject feeTypeAmountRank() {
        EasySQL sql = getEasySQL("SELECT CASE WHEN FEE_TYPE like '差旅费%' THEN '差旅费' ELSE FEE_TYPE END as FEE_TYPE,");
        sql.appendLike(EasyCalendar.newInstance().getYear(), "ROUND(SUM(CASE WHEN bx_date like ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.appendLike(EasyCalendar.newInstance().getYear() - 1, "ROUND(SUM(CASE WHEN bx_date like ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id where 1=1");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        sql.append("GROUP BY CASE WHEN FEE_TYPE LIKE '差旅费%' THEN '差旅费' ELSE FEE_TYPE END");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "contractNameAmountRank", type = Types.LIST)
    public JSONObject contractNameAmountRank() {
        EasySQL sql = getEasySQL("SELECT CONTRACT_SIMPILE_NAME,t1.CONTRACT_ID,ROUND(SUM(r_amount)/t2.AMOUNT * 100 ,2) as ratio,");
        sql.appendLike(EasyCalendar.newInstance().getYear(), "ROUND(SUM(CASE WHEN bx_date like ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_THIS_YEAR,");
        sql.appendLike(EasyCalendar.newInstance().getYear() - 1, "ROUND(SUM(CASE WHEN bx_date like ? THEN r_amount ELSE 0 END), 2) AS TOTAL_AMOUNT_LAST_YEAR,");
        sql.append("ROUND(SUM(r_amount), 2) AS SUM_BX");
        sql.append("FROM yq_flow_bx_item t1 left join yq_project_contract t2 on t1.contract_id = t2.CONTRACT_ID ");
        sql.append(" inner join yq_flow_apply t3 on t1.business_id = t3.apply_id");
        sql.append("WHERE t1.CONTRACT_ID!='9999999999999' and t1.CONTRACT_ID != '0' ");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t3.apply_state = ?");
        sql.append("GROUP BY CONTRACT_SIMPILE_NAME,t1.CONTRACT_ID");
        setOrderBy(sql, " order by TOTAL_AMOUNT_THIS_YEAR desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "feeTypeRankByContract", type = Types.LIST)
    public JSONObject feeTypeRankByContract() {
        EasySQL sql = getEasySQL("SELECT FEE_TYPE,t3.AMOUNT,");
        sql.appendLike(EasyCalendar.newInstance().getYear(), "ROUND(SUM(CASE WHEN bx_date like ? THEN r_amount ELSE 0 END), 2) AS YEAR_AMOUNT,");
        sql.append("ROUND(SUM( r_amount), 2) AS TOTAL_AMOUNT");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id");
        sql.append("left join yq_project_contract t3 on t1.contract_id = t3.CONTRACT_ID where 1=1");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        sql.append(param.getString("contractId"), "and t1.CONTRACT_ID = ?");
        sql.append("GROUP BY FEE_TYPE ");
        setOrderBy(sql, "order by TOTAL_AMOUNT desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "staffRankByContract", type = Types.LIST)
    public JSONObject staffRankByContract() {
        String startDate = param.getString("travelStartDate");
        String endDate = param.getString("travelEndDate");
        startDate = StringUtils.isBlank(startDate)?"0000-00-00":startDate;
        endDate = StringUtils.isBlank(endDate)?EasyDate.getCurrentDateString():endDate;
        EasySQL sql = getEasySQL("SELECT APPLY_NAME,");
        sql.append("ROUND(SUM( r_amount), 2) AS TOTAL_AMOUNT,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN bx_date >= ?");
        sql.append(endDate,"AND bx_date <= ? THEN r_amount ELSE 0 END), 2) AS TIME_AMOUNT");
        sql.append("FROM yq_flow_bx_item t1 inner join yq_flow_apply t2 on t1.business_id = t2.apply_id");
        sql.append("left join yq_project_contract t3 on t1.contract_id = t3.CONTRACT_ID where 1=1");
        sql.append("and FEE_TYPE LIKE '差旅费%'");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t2.apply_state = ?");
        sql.append(param.getString("contractId"), "and t1.CONTRACT_ID = ?");
        sql.append("GROUP BY APPLY_NAME ");
        setOrderBy(sql, "order by TOTAL_AMOUNT desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    @WebControl(name = "travelFeeRankByContract", type = Types.LIST)
    public JSONObject travelFeeRankByContract() {
        String startDate = param.getString("startDate");
        String endDate = param.getString("endDate");
        startDate = StringUtils.isBlank(startDate)?"0000-00-00":startDate;
        endDate = StringUtils.isBlank(endDate)?EasyDate.getCurrentDateString():endDate;

        EasySQL sql = getEasySQL("SELECT CONTRACT_SIMPILE_NAME,t1.CONTRACT_ID,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-机票费' AND bx_date >= ?");
        sql.append(endDate,"AND bx_date <= ? THEN r_amount ELSE 0 END), 2) AS FEE1,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-车船费' AND bx_date >= ?");
        sql.append(endDate,"AND bx_date <= ? THEN r_amount ELSE 0 END), 2) AS FEE2,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-市内交通费' AND bx_date >= ?");
        sql.append(endDate,"AND bx_date <= ? THEN r_amount ELSE 0 END), 2) AS FEE3,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-住宿费' AND bx_date >= ?");
        sql.append(endDate,"AND bx_date <= ? THEN r_amount ELSE 0 END), 2) AS FEE4,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type = '差旅费-出差补助' AND bx_date >= ?");
        sql.append(endDate,"AND bx_date <= ? THEN r_amount ELSE 0 END), 2) AS FEE5,");
        sql.append(startDate,"ROUND(SUM(CASE WHEN fee_type like '差旅费%' AND bx_date >= ?");
        sql.append(endDate,"AND bx_date <= ? THEN r_amount ELSE 0 END), 2) AS SUM_FEE");

        sql.append("FROM yq_flow_bx_item t1 left join yq_project_contract t2 on t1.contract_id = t2.CONTRACT_ID ");
        sql.append("inner join yq_flow_apply t3 on t1.business_id = t3.apply_id");
        sql.append("WHERE t1.CONTRACT_ID!='9999999999999' and t1.CONTRACT_ID != '0' ");
        sql.append(FlowConstants.FLOW_STAT_CHECK_FINISH, "and t3.apply_state = ?");
        sql.append("GROUP BY CONTRACT_SIMPILE_NAME,t1.CONTRACT_ID");
        setOrderBy(sql, " order by SUM_FEE desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }


    private String getLastYearLastMonthStr() {
        StringBuffer buf = new StringBuffer("");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.add(Calendar.YEAR, -1);
        buf.append(calendar.get(Calendar.YEAR));
        buf.append("-");
        buf.append(calendar.get(Calendar.MONTH) + 1 > 9 ? String.valueOf(calendar.get(Calendar.MONTH) + 1) : "0" + (calendar.get(Calendar.MONTH) + 1));
        return buf.toString();
    }

    private String getLastYearThisMonthStr() {
        StringBuffer buf = new StringBuffer("");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        buf.append(calendar.get(Calendar.YEAR));
        buf.append("-");
        buf.append(calendar.get(Calendar.MONTH) + 1 > 9 ? String.valueOf(calendar.get(Calendar.MONTH) + 1) : "0" + (calendar.get(Calendar.MONTH) + 1));
        return buf.toString();
    }

    private int getQuarterFromMonth(int month) {
        return (month - 1) / 3 + 1;
    }

    private String yearBeforeLastYearTodayDate(){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -2);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar.getTime());
    }

    private void setOrderBy(EasySQL sql, String defaultOrder) {
        String sortName = param.getString("sortName");
        String sortType = param.getString("sortType");
        if (StringUtils.notBlank(sortName)) {
            sql.append("order by ").append(sortName).append(sortType);
        } else {
            sql.append(defaultOrder);
        }
    }

}
