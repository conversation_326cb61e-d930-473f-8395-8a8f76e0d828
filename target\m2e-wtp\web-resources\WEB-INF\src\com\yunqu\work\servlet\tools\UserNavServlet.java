package com.yunqu.work.servlet.tools;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/userNav")
public class UserNavServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	public EasyResult actionForAdd(){
		EasyRecord record=new EasyRecord("YQ_USER_NAV","NAV_ID");
		try {
			record.setColumns(getJSONObject("userNav"));
			record.set("CREATE_TIME", EasyDate.getCurrentDateString());
			record.set("NAV_ID", RandomKit.uuid());
			record.set("USER_ID", getUserId());
			this.getQuery().save(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}
	public EasyResult actionForUpdate(){
		EasyRecord record=new EasyRecord("YQ_USER_NAV","NAV_ID");
		try {
			record.setColumns(getJSONObject("userNav"));
			this.getQuery().update(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}
	public EasyResult actionForDelete(){
		EasyRecord record=new EasyRecord("YQ_USER_NAV","NAV_ID");
		try {
			record.setColumns(getJSONObject("userNav"));
			this.getQuery().deleteById(record);
			return EasyResult.ok();
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
			return EasyResult.fail();
		}
	}

}
