package com.yunqu.work.servlet.flow;

import java.util.Calendar;
import java.util.List;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.jfinal.aop.Before;
import com.jfinal.core.ActionKey;
import com.jfinal.core.Path;
import com.yunqu.work.base.BaseController;
import com.yunqu.work.ext.interceptor.AuthInterceptor;
import com.yunqu.work.ext.interceptor.GlobalInterceptor;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.LookLogService;
import com.yunqu.work.utils.FlowUtils;

@Path("/web/flow")
@Before(value = {GlobalInterceptor.class,AuthInterceptor.class})
public class FlowController extends BaseController {
	
	public void index() {
		String flowCode = getPara("flowCode");
		String businessId = getPara("businessId");
		String resultId = getPara("resultId");
		String mode = getPara("mode");
		if(StringUtils.isBlank(mode)) {
			mode = getPara("handle");
		}
		String queryString = getRequest().getQueryString();
		try {
			if(StringUtils.isBlank(flowCode)) {
				flowCode = getPara("id");
			}
			FlowApplyModel applyInfo = null;
			if(StringUtils.isBlank(businessId)) {
				businessId = getPara();
			}
			if(StringUtils.isBlank(flowCode)&&StringUtils.notBlank(businessId)) {
				flowCode = this.getQuery().queryForString("select flow_code from yq_flow_apply where apply_id = ?", businessId);
				if(StringUtils.isBlank(flowCode)) {
					renderHtml("您访问的流程不存在,请检查链接是否正确[1405].");
					return;
				}
				applyInfo = ApproveService.getService().getApply(businessId);
				if(applyInfo==null) {
					renderHtml("流程不存在或被删除.");
					return;
				}
				if(applyInfo.getApplyState()==10||applyInfo.getApplyState()==20) {
					List<ApproveResultModel> list = ApproveService.getService().getTodoResult(businessId);
					if(list!=null&&list.size()>0) {
						for(ApproveResultModel result:list) {
							if(result!=null&&result.getCheckResult()==0) {
								String _resultId = result.getResultId();
								if(StringUtils.notBlank(result.getCheckUserId())&&result.getCheckUserId().indexOf(getUserId())>-1) {
									if(StringUtils.isBlank(result.getReadTime())) {
										this.getQuery().executeUpdate("update yq_flow_approve_result set read_time = ? where result_id = ? and read_time = ''", EasyDate.getCurrentDateString(),_resultId);
									}
									redirect("/web/flow?businessId="+businessId+"&flowCode="+flowCode+"&resultId="+_resultId+"&handle=approval");
									return;
								}
							}
						}
					}
				}
				redirect("/web/flow?businessId="+businessId+"&flowCode="+flowCode+"&resultId="+applyInfo.getNextResultId());
				return;
			}
			if(StringUtils.isBlank(flowCode)) {
				renderHtml("您访问的流程不存在,请检查链接是否正确[1404].");
				return;
			}
			keepPara();
			FlowModel flowModel =  ApproveService.getService().getFlow(flowCode);
			if(flowModel==null) {
				renderHtml("流程编码【"+flowCode+"】不存在,请与管理员联系。");
				return;
			}
			setAttr("title", flowModel.getFlowName());
			setAttr("businessId", businessId);
			
			setAttr("flow", flowModel);
			
			String url = flowModel.getApplyUrl();
			
			
			if(StringUtils.notBlank(businessId)) {
				if(applyInfo==null) {
					 applyInfo = ApproveService.getService().getApply(businessId);
				}
				if(applyInfo==null) {
					renderHtml("流程不存在或被删除.");
					return;
				}
				
				if(!"edit".equals(mode)) {
					url = flowModel.getDetailUrl();
				}else if("edit".equals(mode)){
					if(!isFlowMgr()) {
						if(!getUserId().equals(applyInfo.getApplyBy())) {
							renderHtml("您不是申请人没权限操作.");
							return;
						}
					}
				}
				
				if(StringUtils.isBlank(resultId)) {
					resultId = applyInfo.getNextResultId();
				}
				setAttr("resultId", resultId);
				
				//新增预览日志
				if(!getUserId().equals(applyInfo.getApplyBy())&&!isFlowMgr()) {
					LookLogService.getService().addLog(getUserPrincipal().getUserId(),businessId,"flow",getRequest());
				}
			}else {
				if(FlowUtils.isBxFlow(flowCode)) {
					Calendar calendar=Calendar.getInstance();
					int day = calendar.get(Calendar.DAY_OF_MONTH);
					if(day>=27) {
						renderHtml("每月27号费用报销系统自动关闭，次月1号开启。");
						return;
					}
				}
			}
			
			if(StringUtils.isBlank(url)) {
				renderHtml("url is empty,流程链接配置不正确.");
				return;
			}
			
			url = url.replace("/yq-work", "");
			if(url.indexOf("?")==-1&&StringUtils.notBlank(queryString)) {
				url = url +"?"+queryString;
			}else if(url.indexOf("?")==-1&&StringUtils.notBlank(businessId)){
				url = url +"?businessId="+businessId;
			}
			renderJsp(url);
		} catch (Exception e) {
			this.error(null, e);
			renderHtml(e.getMessage());
		}
	}
	
	@ActionKey(value = "/flow/apply")
	public void apply() {
		renderJsp("/pages/flow/flow-apply.jsp");
	}
	
	@ActionKey(value = "/flow/cc")
	public void cc() {
		renderJsp("/pages/flow/my/flow-cc.jsp");
	}
	
	@ActionKey(value = "/flow/todo")
	public void todo() {
		renderJsp("/pages/flow/my/flow-todo.jsp");
	}
	
	@ActionKey(value = "/flow/done")
	public void done() {
		renderJsp("/pages/flow/my/flow-done.jsp");
	}
	
	@ActionKey(value = "/flow/my")
	public void my() {
		renderJsp("/pages/flow/my/flow-list.jsp");
	}
	
	@ActionKey(value = "/invoice/identify")
	public void invoice() {
		renderJsp("/pages/doc/invoice.jsp");
	}
	
	@ActionKey(value = "/invoice/list")
	public void myInvoice() {
		renderJsp("/pages/flow/bx/invoice/invoice-list.jsp");
	}
	
	@ActionKey(value = "/invoice/merge")
	public void invoiceMerge() {
		set("invoiceIds", getPara("invoiceIds"));
		renderJsp("/pages/flow/bx/invoice/invoice-merge.jsp");
	}
	
	public void getFlow() {
		String flowCode = getJsonPara("flowCode");
		if(StringUtils.isBlank(flowCode)){
			renderJson(EasyResult.fail());
			return;
		}
		renderJson(EasyResult.ok(ApproveService.getService().getFlow(flowCode)));
	}

	
}
