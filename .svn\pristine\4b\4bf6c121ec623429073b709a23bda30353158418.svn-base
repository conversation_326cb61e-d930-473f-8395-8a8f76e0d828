<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>客户管理</title>
	<style>
		.layui-badge{left: -1px!important;}
		.layui-table-cell{padding: 0 6px;}
		.filterCondition{display: none;padding-bottom: 20px;overflow: auto;height: 100%;}
		.layui-progress{margin-top: 12px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="businessMgrForm">
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>商机管理</h5>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		 <span class="input-group-addon">跟进阶段</span>
						 	 <select data-rules="required" name="businessStage" class="form-control input-sm">
					 	 		<option value="">请选择</option>
	                   		 	<option value="10">沟通交流</option>
		              		 	<option value="20">立项阶段</option>
		              		 	<option value="25">POC阶段</option>
		              		 	<option value="30">投标阶段</option>
		              		 	<option value="40">商务阶段</option>
		                   	  </select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		 <span class="input-group-addon">商机结果</span>
						 	 <select data-rules="required" name="businessResult" class="form-control input-sm">
					 	 		<option value="">请选择</option>
	                   		 	<option value="0">跟进中</option>
	                   		 	<option value="50" data-class="label label-success">赢单</option>
	                   		 	<option value="60" data-class="label label-warning">输单</option>
	                   		 	<option value="99"  data-class="label label-danger">取消</option>
		                   	  </select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">创建人</span>
					 		<select data-rules="required" data-mars="CommonDao.userDict" name="creator"  class="form-control input-sm">
	                    	 	<option value="">请选择</option>
	                    	</select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">所属销售</span>
					 			<select data-rules="required" data-mars="CommonDao.userDict" name="sales"  class="form-control input-sm">
	                    	 	<option value="">请选择</option>
	                    	</select>
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">商机名称</span>
						 	<input class="form-control input-sm" name="name">
						 </div>
						 <div class="input-group input-group-sm" style="width: 160px;">
					 		<span class="input-group-addon">客户名称</span>
						 	<input class="form-control input-sm" name="custName">
						 </div>
						 <div class="input-group input-group-sm">
							 <button type="button" data-event="enter" class="btn btn-sm btn-default" onclick="BusinessMgr.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="BusinessMgr.add()">+ 新建</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
						    <table class="layui-hide" id="sjMgrTable"></table>
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="bar1">
				<a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="BusinessMgr.edit">编辑</a>
				<a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="BusinessMgr.detail">查看</a>
		</script>
		 <script type="text/html" id="opBar">
				<a href="javascript:;" onclick="exportExcel()" class="btn btn-sm btn-default">导出数据</a>
		 </script>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		
		var BusinessMgr={
			init:function(){
				$("#businessMgrForm").initTable({
					mars:'CustDao.businessPage',
					id:'sjMgrTable',
					height:'full-120',
					toolbar:'#opBar',
					limit:50,
					page:true,
					limits:[10,15,20,30,50,100,200,300],
					totalRow:true,
					cols: [[
		             {
	            	 	type: 'numbers',
						title: '序号',
						align:'left'
					 },{
					    field: 'CUST_NAME',
						title: '所属客户',
						minWidth:180,
						totalRowText:'统计',
						align:'left'
					},{
					    field: 'BUSINESS_NAME',
						title: '商机名称',
						align:'left',
						minWidth:200,
						event:'BusinessMgr.edit',
						style:'color:#1E9FFF;cursor:pointer'
					},{
						field:'BUSINESS_STAGE',
						title:'跟进 阶段',
						width:80,
						templet:function(row){
							return getText(row['BUSINESS_STAGE'],'businessStage');
						}
					},{
						field:'BUSINESS_RESULT',
						title:'商机结果',
						width:80,
						templet:function(row){
							return getText(row['BUSINESS_RESULT'],'businessResult');
						}
					},{
						field:'FOLLOW_COUNT',
						width:80,
						title:'跟进次数',
						totalRow:true
					},{
						field:'FOLLOW_DAY',
						width:80,
						title:'消耗工时',
						totalRow:true
					},{
					    field: 'POSSIBILITY',
						title: '可能性',
						align:'left',
						width:90,
						templet:'<div>{{d.POSSIBILITY}}%</div>'
					},{
					    field: 'AMOUNT',
						title: '预计金额',
						align:'left',
						width:100,
						templet:'<div>¥{{d.AMOUNT}}</div>'
					},{
					    field: 'UPDATE_TIME',
						title: '更新时间',
						width:140,
						align:'center'
					},{
					    field: 'CREATE_TIME',
						title: '创建时间',
						align:'center',
						width:130,
						templet:function(row){
							var time= row['CREATE_TIME'];
							return cutText(time,19,'');
						}
					},{
					    field: 'SALES_BY',
						title: '销售经理',
						align:'center',
						width:90,
						templet:function(row){
							return getUserName(row.SALES_BY);
						}
					},{
					    field: 'CREATOR',
						title: '创建人',
						align:'center',
						width:90,
						templet:function(row){
							return getUserName(row.CREATOR);
						}
					},{
						title: '操作',
						align:'center',
						width:110,
						fixed:'right',
						templet:function(row){
						    return renderTpl('bar1',row);
						}
					}
				]],done:function(){
					
			  }});
			},
			query:function(){
				$("#businessMgrForm").queryData({id:'sjMgrTable',jumpOne:true});
			},
			edit:function(data){
				var  businessId = data['BUSINESS_ID'];
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','430px'],url:'${ctxPath}/pages/crm/shangji/sj-edit.jsp',title:'编辑',data:{businessId:businessId}});
			},
			add:function(){
				popup.layerShow({type:1,full:fullShow(),anim:0,scrollbar:false,shadeClose:false,offset:'20px',area:['600px','430px'],url:'${ctxPath}/pages/crm/shangji/sj-edit.jsp',title:'新增',closeBtn:0});
			},
			detail:function(data){
				var businessId = data['BUSINESS_ID'];
				var custId = data['CUST_ID'];
				var custName = data['CUST_NAME'];
				popup.openTab({id:'bussinessContactQuery',title:'跟进记录',url:'${ctxPath}/pages/crm/contacts/contact-query.jsp',data:{businessId:businessId,custId:custId,custName:custName}});
			}
		}
		function reloadBusinessList(){
			BusinessMgr.query();
		}
		$(function(){
			$("#businessMgrForm").render({success:function(){
				 requreLib.setplugs('select2',function(){
						$("#businessMgrForm select").select2({theme: "bootstrap",placeholder:'请选择'});
						$(".select2-container").css("width","100%");
						BusinessMgr.init();
			 	 });
			}});
		});
		function exportExcel(){
			$(".layui-icon-export").click();
			$(".layui-table-tool-panel li").last().click();
		}
		
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>