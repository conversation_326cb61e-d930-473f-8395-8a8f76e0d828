<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>采购详情</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="OrderDetailForm" data-mars="OrderDao.applyInfo" data-mars-prefix="apply.">
			    <input type="hidden" value="${param.orderId}" id="orderId" name="orderId"/>
 	  	        <input type="hidden" value="${param.orderState}" id="orderState" name="apply.ORDER_STATE"/>
 	  	        <input type="hidden" id="supplierId" name="apply.SUPPLIER_ID"/>
				<div style="padding: 5px" class="ibox-content">
					<div class="layui-row" style="padding:6px 15px;">
					  <div class="layui-col-md8">
					     <i class="layui-icon layui-icon-app" style="font-size: 22px;" aria-hidden="true"></i> <span style="font-size: 18px;" id="orderTitle">采购订单详情</span>
					  </div>
					  <div class="layui-col-md4">
					  		<div class="pull-right mr-50">
						  		<div class="btn-group btn-group-sm">
									<button class="btn btn-default btn-xs" onclick="editOrder();" type="button"><i class="fa fa-edit" aria-hidden="true"></i> 编辑</button>
								    <button class="btn btn-default btn-xs ml-10" type="button" onclick="addFollow()">+新增跟进记录</button>
								</div>
					  		</div>
							<div style="display: inline-block;position: absolute;right: 0px;top: -2px;cursor: pointer;" onclick="popup.closeTab()"><i style="font-size: 26px;" class="layui-icon layui-icon-close"></i></div>
					  </div>
					</div>
					<div class="layui-row" style="padding:10px 15px;">
						  <div class="layui-col-md12">
						  		<jsp:include page="include/base-info.jsp"/>
						  </div>
					</div>
				</div>
				<div class="layui-row">
					<div class="layui-col-md12">
						<div class="layui-tab layui-tab-card" style="border-top: 1px solid #ddd;">
						  <ul class="layui-tab-title">
						    <li class="layui-this">物料信息(<span id="goodsCount">0</span>)</li>
						    <li>附件列表(<span id="fileCount">0</span>)</li>
						    <li>发票管理(<span id="invoiceCount">0</span>)</li>
						    <li>付款方式(<span id="payTypeCount">0</span>)</li>
						    <li>动态(<span id="followCount">0</span>)</li>
						    <li>流程(<span id="flowCount">0</span>)</li>
						  </ul>
						  <div class="layui-tab-content" style="margin: 0px;background-color: #fff;min-height: calc( 100vh - 180px)">
						    <div class="layui-tab-item layui-show">
						    	<jsp:include page="include/goods-mgr.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="include/file-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="invoice/invoice-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
						    	<jsp:include page="pay/type/type-list.jsp"/>
						    </div>
						    <div class="layui-tab-item">
								<jsp:include page="include/follow-list.jsp"></jsp:include>
								<button type="button" class="btn btn-sm btn-info pull-left mt-10 ml-20" style="" onclick="addFollow()">+新建</button>
						    </div>
						    <div class="layui-tab-item">
						    	todo
						    </div>
						  </div>
					</div>
					</div>
				</div>
		</form>
		<form  id="orderDetailFileForm" enctype="multipart/form-data"  method="post">
  			<input style="display: none;" name="file" type="file" id="orderDetailLocalfile" onchange="orderUploadFile()"/>
		</form>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
   
		jQuery.namespace("OrderDetail");
	    
		OrderDetail.orderId='${param.orderId}';
		
		var orderId = '${param.orderId}';
		
		$(function(){
			layui.use(['element','form'],function(){
				var element = layui.element;
				var form = layui.form;
				form.render();
			});
			
			$("#OrderDetailForm").render({success:function(result){
				var orderState = $("#orderState").val();
				if(orderState=='90'||orderState=='99'){
					var style=document.createElement('style');
					style.type='text/css';
					if(style.styleSheet){
					    style.styleSheet.cssText='button{display:none!important;}';
					}else{
					    style.appendChild(document.createTextNode('button{display:none!important;}'));
					}
					document.getElementsByTagName('head')[0].appendChild(style);
				}
				
				var newOrder = '${param.newOrder}';
				var ts = '${param.ts}';
				var _ts = Date.parse(new Date());
				var calcTs = _ts-ts;
				if(newOrder=='1'&&calcTs<3000){
					$('.layui-tab-title li').first().click();
					addGoods();
				}
			}});  
		});
		
		
		function addFollow(){
			popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'新增跟进',data:{orderId:orderId}});
			
		}
		
		function editFollow(followId){
			popup.layerShow({id:'addFollow',scrollbar:false,area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/add-follow.jsp',title:'修改跟进',data:{orderId:orderId,followId:followId}});
			
		}
		function moreFollow(){
			popup.openTab({id:'orderFollowList',url:'${ctxPath}/pages/erp/order/follow-query.jsp',title:'采购跟进记录',data:{orderId:orderId}});
		}
		
		function editOrder(){
			popup.layerShow({id:'editOrder',scrollbar:false,title:'修改',offset:'r',area:['700px','100%'],url:'${ctxPath}/pages/erp/order/order-add.jsp',data:{orderId:orderId}});
		}
		
		function setFollowCount(data){
			$('#followCount').text(data.total);
			return '';
		}
		
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>