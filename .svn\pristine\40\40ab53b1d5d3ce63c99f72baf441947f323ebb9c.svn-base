<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>周报管理</title>
	<style>
		.ibox-content {
		    padding: 15px;
		}
		.layui-table-cell{padding: 0 6px;}
		[data-week-no]{color: #999;cursor: pointer;}
		.weekActive{font-size: 16px;color: #e32424;}
		.layui-timeline-item{cursor: pointer;}
		.layui-timeline-item:hover .layui-timeline-title{color: #1E9FFF;}
		.layui-timeline-title{font-size: 16px!important;color: #916f6f;}
		.layui-timeline-content p{font-size: 13px;line-height: 24px;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="weeklyForm" data-page-hide="true">
			<div class="ibox">
					<div class="ibox-content weeklyTitle">
						<select name="selectPeopleType" onchange="list.query()" class="form-control input-sm mb-10">
		    				<option value="">选择人员角色</option>
		    				<option value="开发,创新,分部">开发</option>
		    				<option value="工程">工程</option>
		    				<option value="开发,工程">开发和工程</option>
				    	</select>
				    	<input type="text" style="width: 130px" name="userName" class="form-control input-sm mb-10" placeholder="姓名"/>
				    	<input type="text" style="width: 130px;" name="depts" class="form-control input-sm mb-10" placeholder="部门"/>
				    	<button type="button" class="btn btn-sm btn-default mb-10" onclick="list.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
					  	<div style="margin-bottom: 0px;">
				    		<div class="pl-10" data-mars="WeeklyDao.getRecentWeek" data-template="getRecentWeek" style="float:left;vertical-align:super;display:inline;height: 40px;line-height: 40px;"></div>
					  		<div class="mt-5" style="display: inline-block;">
						  		<select name="year" onchange="yearChange(this.value)" class="form-control input-sm">
				    				<option value="2023">2023年</option>
				    				<option value="2022">2022年</option>
				    				<option value="2021">2021年</option>
				    				<option value="2020">2020年</option>
						    	</select>
						    	<select name="weekNo" onchange="getWeeklyTitle(this.value)" class="form-control input-sm">
						    		<option value="">所有周数</option>
						    		<c:forEach begin="1" step="1" end="52" var="item">
						    			<option value="${item }">第${item }周</option>
						    		</c:forEach>
						    	</select>
					  		</div>
							<div  style="float: right;margin-top: 10px;" id="weeklyTitle"></div>
					  	</div>
					</div>
					<div class="ibox-content mt-10">
					  	<div class="layui-tab layui-tab-brief">
						  <ul class="layui-tab-title">
						     <li class="layui-this">提交统计</li>
						  </ul>
						  <div class="layui-tab-content">
   							  <div class="layui-tab-item layui-show">
								<div id="cardList" data-mars="WeeklyDao.queryDeptWeekly" data-template="queryDeptWeeklyTpl" data-container="deptWeeklyTbody">
									<table class="layui-table">
									  <thead>
									    <tr>
									      <th>序号</th>
									      <th>姓名</th>
									      <th>周报名称</th>
									      <th>饱和度</th>
									      <th>提交时间</th>
									      <th>提交状态</th>
									    </tr> 
									  </thead>
									   <tbody id="deptWeeklyTbody">
									   
									   </tbody>
									</table>
								</div>
						  	 </div>
						  </div>
						</div>  
					</div>
				</div>
		</form>
		<script type="text/x-jsrender" id="queryDeptWeeklyTpl">
			{{for data}}
				<tr>
    			  <td>{{:#index+1}}</td>
      			 <td>{{:DEPTS}}.{{:USERNAME}}</td>
      			 <td onclick="list.detail('{{:WEEKLY_ID}}')">{{:TITLE}}</td>
      			 <td>{{:ITEM_5}}</td>
      			 <td>{{:SEND_TIME}}</td>
      			 <td>{{if SEND_TIME}} {{else}} <a href="javascript:;" onclick="noticeWriteWeekly('{{:USER_ID}}')" class="btn btn-xs btn-link">提醒</a> {{/if}}</td>
   				 </tr>
			{{/for}}
		</script>
		<script type="text/x-jsrender" id="getRecentWeek">
			{{for data}}
				{{if #index==0}}<input type="hidden" id="currNo" value="{{:currentWeekNo}}"/>{{/if}}
				<span data-title="{{:TITLE}}" data-week-no="{{:WEEK_NO}}" class="mr-20 {{if currentWeekNo==WEEK_NO}}weekActive{{/if}}">第{{:WEEK_NO}}周</span>
			{{/for}}
		</script>
</EasyTag:override>
<EasyTag:override name="script">

	<script type="text/javascript">
		
		
		function noticeWriteWeekly(userId){
			var data = $.extend({userId:userId},form.getJSONObject("#weeklyForm"));
			ajax.remoteCall("${ctxPath}/servlet/weekly?action=noticeWriteWeekly",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
		
		function loadRecentWeekList(isInit){
			$(".weeklyTitle").render({success:function(){
				$("[name='weekNo']").val($("#currNo").val());
				$("[data-week-no]").click(function(){
					var t =$(this);
					t.siblings().removeClass("weekActive");
					t.addClass("weekActive");
					$("#weeklyTitle").html(t.data("title"));
					$("[name='weekNo']").val(t.data("weekNo"));
					list.query();
				});
				$("#weeklyTitle").html($(".weekActive").data("title"));
				$(".layui-tab-content").render({data:form.getJSONObject("#weeklyForm")});
			}});
		}
		
		var list={
			query:function(){
				$(".layui-tab-content").render({data:form.getJSONObject("#weeklyForm")});
				$("#weeklyForm").queryData({id:'recentListTable',jumpOne:true});
			},
			detail:function(data){
				var width=$(window).width();
				var fullFlag=false;
				var width ='850px';
				if(width<700){
					fullFlag=true;
				}
				if(width>1900){
					width='70%';
				}
				var weeklyId = data;
				if(data.WEEKLY_ID){
					weeklyId=data.WEEKLY_ID;
				}
				popup.layerShow({id:'weeklyDetail',type:1,full:fullFlag,shade: 0.1,shadeClose:true,maxmin:true,anim:0,scrollbar:false,offset:'r',area:[width,'100%'],url:'${ctxPath}/pages/weekly/weekly-detail.jsp',title:'周报详情',data:{weeklyId:weeklyId}});
			}
		}
		
		function yearChange(val){
			loadRecentWeekList();
			list.query();
		}
		
		function getWeeklyTitle(val){
			var len = $("[data-week-no='"+val+"']").length;
			if(len==0){
				$(".weekActive").removeClass("weekActive");
			}
			if(val){
				$("#weeklyTitle").html("第"+val+"周");
			}else{
				$("#weeklyTitle").html("全部");
			}
			list.query();
		}
		$(function(){
			loadRecentWeekList(true);
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>