package com.yunqu.work.servlet.erp;

import java.sql.SQLException;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppBaseServlet;

@WebServlet("/servlet/goods")
public class GoodsServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;

	private EasyRecord getModel(String prefix){
		EasyRecord model = new EasyRecord("yq_erp_goods","goods_id");
		model.setColumns(getJSONObject(prefix));
		return model;
	}
	public EasyResult actionForAdd(){
		String id= RandomKit.uniqueStr();
		EasyRecord model = getModel("goods");
		model.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
		model.set("CREATE_TIME", EasyDate.getCurrentDateString());
		model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
		model.set("CREATE_USER_ID", getUserId());
		model.set("CREATE_USER_NAME", getUserName());
		model.setPrimaryValues(id);
		try {
			this.getQuery().save(model);
			
			String orderId = model.getString("ORDER_ID");
			this.orderPriceStat(orderId);
			this.getQuery().executeUpdate("update yq_erp_product t1 set t1.goods_count = (select sum(t2.number) from yq_erp_goods t2 where t2.product_id = t1.product_id)");
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok(id);
	}
	
	private void orderPriceStat(String orderId) {
		try {
			this.getQuery().executeUpdate("update yq_erp_order t1 set t1.total_price = (select sum(t2.total_price) from yq_erp_goods t2 where t2.order_id = t1.order_id ) where t1.order_id = ?", orderId);
		} catch (SQLException e) {
			this.error(null, e);
		}
		
	}
	
	public EasyResult actionForUpdate(){
		EasyRecord model = getModel("goods");
		try {
			model.set("UPDATE_TIME", EasyDate.getCurrentDateString());
			this.getQuery().update(model);
			
			String orderId = model.getString("ORDER_ID");
			this.orderPriceStat(orderId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForGetGoodsNo(){
		String id = getJsonPara("id");
		try {
			String val = this.getQuery().queryForString("select max(goods_no) from yq_erp_goods where goods_no like '"+id+"%'");
			if(StringUtils.isBlank(val)) {
				return EasyResult.ok(id+"1001");
			}else {
				return EasyResult.ok(Integer.valueOf(val)+1);
			}
		} catch (SQLException e) {
			return EasyResult.fail(e.getMessage());
		}
	}

	public EasyResult actionForUpdateStorage(){
		JSONObject columns = getJSONObject("stock");
		EasyRecord record = new EasyRecord("YQ_ERP_GOODS_STOCK","STOCK_ID");
		record.setColumns(columns);
		String dateId = columns.getString("DATE_ID").replaceAll("-", "");
		record.set("DATE_ID", dateId);
		int returnType = columns.getIntValue("RETURN_TYPE");
		try {
			boolean bl = this.getQuery().update(record);
			if(bl) {
				int type = columns.getIntValue("TYPE");
				String goodsId = columns.getString("GOODS_ID");
				this.updateStorageNum(type,returnType,dateId,goodsId,null);
			}
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelStorage(){
		return EasyResult.ok();
	}
	
	private String getStockNo() {
		int dateId = EasyCalendar.newInstance().getDateInt();
		try {
			String val = this.getQuery().queryForString("select max(stock_no) from yq_erp_goods_stock where stock_no like '"+dateId+"%'");
			if(StringUtils.isBlank(val)) {
				return dateId+"1001";
			}else {
				return String.valueOf(Long.valueOf(val)+1);
			}
		} catch (SQLException e) {
			this.error(null, e);
			return "";
		}
	}
	
	public EasyResult actionForBatchAddStorage(){
		JSONObject params = getJSONObject();
		EasyQuery query = getQuery();
		try {
			query.begin();
			String goodsIds = params.getString("goodsIds");
			JSONObject columns = getJSONObject("stock");
			
			String[] array = goodsIds.split(",");
			int returnType = 0;
			String dateId = null;
			for(int i=0,len=array.length;i<len;i++) {
				String goodsId = array[i];
				EasyRecord record = new EasyRecord("YQ_ERP_GOODS_STOCK","STOCK_ID");
				record.setColumns(columns);
				record.remove("STOCK_ID");
				record.set("STOCK_NO", getStockNo());
				record.set("CREAT_NAME", getUserName());
				record.set("NUM",params.getString(goodsId));
				record.set("GOODS_ID", goodsId);
				record.set("CREATOR", getUserId());
				record.set("CREATE_TIME", EasyDate.getCurrentDateString());
				dateId = columns.getString("DATE_ID").replaceAll("-", "");
				record.set("DATE_ID", dateId);
				record.set("YEAR_ID", dateId.substring(0, 4));
				record.set("MONTH_ID", dateId.substring(0, 6));
				returnType = columns.getIntValue("RETURN_TYPE");
				try {
					query.save(record);
				} catch (SQLException e) {
					this.error(null, e);
					return EasyResult.fail(e.getMessage());
				}
			}
			EasySQL sql =  new EasySQL("update YQ_ERP_GOODS_STOCK t1,yq_erp_goods t2 set t1.order_id = t2.order_id where t1.goods_id = t2.goods_id");
			sql.appendIn(array," and t1.goods_id");
			query.executeUpdate(sql.getSQL(), sql.getParams());
			
			query.commit();
			int type = columns.getIntValue("TYPE");
			this.updateStorageNum(type,returnType,dateId,goodsIds,null);
		} catch (SQLException ex) {
			this.error(null, ex);
			try {
				query.roolback();
			} catch (SQLException e) {
				this.error(null, e);
			}
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForAddStorage(){
		JSONObject columns = getJSONObject("stock");
		EasyRecord record = new EasyRecord("YQ_ERP_GOODS_STOCK","STOCK_ID");
		record.setColumns(columns);
		record.remove("STOCK_ID");
		record.set("CREAT_NAME", getUserName());
		record.set("CREATOR", getUserId());
		record.set("CREATE_TIME", EasyDate.getCurrentDateString());
		String dateId = columns.getString("DATE_ID").replaceAll("-", "");
		record.set("DATE_ID", dateId);
		record.set("YEAR_ID", dateId.substring(0, 4));
		record.set("MONTH_ID", dateId.substring(0, 6));
		int returnType = columns.getIntValue("RETURN_TYPE");
		
		try {
			boolean bl = this.getQuery().save(record);
			if(bl){
				String goodsId = columns.getString("GOODS_ID");
				String orderId = columns.getString("ORDER_ID");
				int type = columns.getIntValue("TYPE");
				updateStorageNum(type,returnType,dateId,goodsId,orderId);
			}
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	public EasyResult actionForReloadStorageNum() {
		JSONObject params = getJSONObject();
		String goodsId = params.getString("goodsId");
		EasySQL sql = new EasySQL("update yq_erp_goods t1 set ");
		sql.append("t1.in_number = IFNULL((select sum(num) from yq_erp_goods_stock t2 where t2.goods_id= t1.goods_id and t2.type = 10),0),");
		sql.append("t1.out_number = IFNULL((select sum(num) from yq_erp_goods_stock t3 where t3.goods_id= t1.goods_id and t3.type = 20),0),");
		sql.append("t1.return_number =IFNULL((select sum(num) from yq_erp_goods_stock t4 where t4.goods_id= t1.goods_id and t4.type = 30),0),");
		sql.append("t1.return_supplier_num = IFNULL(( SELECT sum( num ) FROM yq_erp_goods_stock t5 WHERE t5.goods_id = t1.goods_id AND t5.type = 30 and t5.return_type=1), 0 ),");
		sql.append("t1.return_store_num = IFNULL(( SELECT sum( num ) FROM yq_erp_goods_stock t6 WHERE t6.goods_id = t1.goods_id AND t6.type = 30 and t6.return_type=2), 0)");
		sql.append("where 1=1");
		sql.appendIn(goodsId.split(",")," and t1.goods_id");
		try {
			this.getQuery().executeUpdate(sql.getSQL(), sql.getParams());
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
		return EasyResult.ok();
	}
	
	
	
	private void updateStorageNum(int type,int returnType,String dateId,String goodsId,String orderId) {
		try {
			EasySQL sql = new EasySQL("update yq_erp_goods t1 set in_number = IFNULL((select sum(num) from yq_erp_goods_stock t2 where t2.goods_id= t1.goods_id and t2.type = 10),0),out_number = IFNULL((select sum(num) from yq_erp_goods_stock t3 where t3.goods_id= t1.goods_id and t3.type = 20),0),return_number =IFNULL((select sum(num) from yq_erp_goods_stock t4 where t4.goods_id= t1.goods_id and t4.type = 30),0) where 1=1");
			sql.appendIn(goodsId.split(",")," and t1.goods_id");
			this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			if(type==10) {
				sql = new EasySQL("update yq_erp_goods set ");
				sql.append(dateId,"in_date = ? where 1=1");
				sql.appendIn(goodsId.split(",")," and goods_id");
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
				
				this.getQuery().executeUpdate("update yq_erp_order t1 set t1.rk_amount=(select sum(t2.in_number*t2.price) from yq_erp_goods t2 where t1.order_id = t2.order_id) where t1.order_id <> ?","");
			}else if(type==20){
				sql = new EasySQL("update yq_erp_goods t1 set ");
				sql.append("t1.out_type = (select t3.out_type from yq_erp_goods_stock t3 where t3.out_type is not null and t1.goods_id = t3.goods_id limit 1)");
				sql.append(",t1.out_dept_name = (select GROUP_CONCAT(t2.out_dept_name) from yq_erp_goods_stock t2 where t2.out_dept_name<>'' and t1.goods_id = t2.goods_id)");
				sql.append(dateId,",t1.out_date = ? where 1=1");
				sql.appendIn(goodsId.split(",")," and t1.goods_id");
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			}else if(type==30) {
				
			}
			if(returnType>0) {
				sql = new EasySQL("UPDATE yq_erp_goods t1  SET return_supplier_num = IFNULL(( SELECT sum( num ) FROM yq_erp_goods_stock t2 WHERE t2.goods_id = t1.goods_id AND t2.type = 30 and t2.return_type=1), 0 ),return_store_num = IFNULL(( SELECT sum( num ) FROM yq_erp_goods_stock t3 WHERE t3.goods_id = t1.goods_id AND t3.type = 30 and t3.return_type=2), 0 ) where 1=1");
				sql.appendIn(goodsId.split(","),"and t1.goods_id");
				this.getQuery().executeUpdate(sql.getSQL(),sql.getParams());
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	
}


