package com.yunqu.work.base;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.kit.JsonKit;

import com.alibaba.fastjson.JSONObject;
import com.jfinal.core.Controller;
import com.yunqu.work.model.StaffModel;
import com.yunqu.work.service.StaffService;

public class BaseController extends Controller {

	protected EasyQuery getQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	}
	
	protected EasyQuery getMainQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.MARS_DS_NAME);
	}
	
	protected boolean isFlowMgr(){
		return hasRole("FLOW_SUPER_USER");
	}
	
	protected boolean hasRole(String roleId) {
		return getUserPrincipal().isRole(roleId);
	}
	
	protected boolean isSuperUser() {
		return getUserPrincipal().isSuperUser();
	}
	
	protected Logger getLogger(){
		return LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME);
	}
    
	public void renderHtml(String html) {
		super.renderHtml("<div style='padding:30px;background-color: #fff;height: calc(100vh - 100px);font-size:22px'>"+html+"</div>");
	}
	
	protected void error(Object message,Throwable e){
		this.getLogger().error(message, e);
	}
	
	protected void info(Object message,Throwable e){
		this.getLogger().info(message, e);
	}

	protected void debug(Object message,Throwable e){
		this.getLogger().debug(message,e);
	}
	
	protected JSONObject getJSONObject(){
		return  JsonKit.getJSONObject(this.getRequest(), null);
	}
	
	protected JSONObject getJSONObject(String modelName){
		return  JsonKit.getJSONObject(this.getRequest(), modelName);
	}
	
   protected UserPrincipal getUserPrincipal(){ 
		return (UserPrincipal)this.getRequest().getUserPrincipal();
	}
    
  	protected String getNickName(){
  		UserPrincipal  principal  = getUserPrincipal();
  		return principal.getUserName();
  	}

  	protected String getUserId() {
  		return getUserPrincipal().getUserId();
	}
  	
  	protected String getJsonPara(String name) {
		return  getJSONObject().getString(name);
	}
  	
	protected StaffModel getStaffInfo() {
		Object object =  getUserPrincipal().getAttribute("staffInfo");
		if(object==null) {
			StaffModel staffModel = StaffService.getService().getStaffInfo(getUserId());
			getUserPrincipal().setAttribute("staffInfo",staffModel);
			return staffModel;
		}
		String jsonStr = JSONObject.toJSONString(object);
		JSONObject jsonObject = JSONObject.parseObject(jsonStr);
		return JSONObject.toJavaObject(jsonObject, StaffModel.class);
	}
	
	protected String getDeptId() {
		return getStaffInfo().getDeptId();
	}
	
	protected String getDeptName() {
		return getStaffInfo().getDeptName();
	}
	
  	
  	protected EasyQuery getQuery(int convertField) {
		EasyQuery query =  this.getQuery();
		query.setConvertField(convertField);
		return query;
	}

}
