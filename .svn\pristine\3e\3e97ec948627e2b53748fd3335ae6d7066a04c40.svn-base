package com.yunqu.work.dao.flow;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.service.ApproveService;

@WebObject(name = "FlowDao")
public class FlowDao extends AppDaoContext{

	@WebControl(name = "myFlowCategoryList",type = Types.TEMPLATE)
	public JSONObject myFlowCategoryList() {
		EasySQL sql = getEasySQL("select * from yq_flow_category where 1=1");
		sql.append(param.getString("pFlowCode"),"and p_flow_code = ?");
		sql.append(0,"and flow_state = ?");
		sql.append(getDeptId(),"and (flow_auth_depts = 0  or find_in_set(?,flow_auth_depts))");
		sql.append("order by flow_code");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	
	@WebControl(name = "categoryList",type = Types.TEMPLATE)
	public JSONObject categoryList() {
		EasySQL sql = getEasySQL("select * from yq_flow_category where 1=1");
		sql.append(param.getString("flowState"),"and flow_state = ?","0");
		sql.append(param.getString("pFlowCode"),"and p_flow_code = ?");
		sql.append("order by flow_code");
		return queryForList(sql.getSQL(),sql.getParams());
	}
	

	@WebControl(name = "nodeConfig",type=Types.LIST)
	public JSONObject nodeConfig() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_node t1 where 1=1");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?",false);
		sql.append("order by t1.check_index");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeDict",type=Types.DICT)
	public JSONObject nodeDict() {
		EasySQL sql = getEasySQL("select t1.node_id,concat(t1.node_name,'|',t1.user_name) from yq_flow_approve_node t1 where 1=1");
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?",false);
		sql.append(param.getString("nodeId"),"and t1.node_id <> ?");
		return getDictByQuery(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "nodeInfo",type=Types.RECORD)
	public JSONObject nodeInfo() {
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_node t1 where 1=1");
		sql.append(param.getString("nodeId"),"and t1.node_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "applyList",type = Types.PAGE)
	public JSONObject applyList() {
		EasySQL sql = getEasySQL("select * from yq_flow_apply where 1=1");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	@WebControl(name = "applyInfo",type = Types.RECORD)
	public JSONObject applyInfo() {
		EasySQL sql = getEasySQL("select * from yq_flow_apply where 1=1");
		sql.append(param.getString("businessId"),"and apply_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "baseInfo",type = Types.RECORD)
	public JSONObject baseInfo() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return getJsonResult(new JSONObject());
		}
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t4.flow_name from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("INNER JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append(businessId,"and t1.apply_id = ?",false);
		EasyQuery query = getQuery();
		query.setConvertField(3);
		this.setQuery(query);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "businessInfo",type = Types.RECORD)
	public JSONObject businessInfo() {
		String flowCode = param.getString("flowCode");
		if(StringUtils.isBlank(flowCode)) {
			return getJsonResult(new JSONObject());
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		EasySQL sql = getEasySQL("select * from "+flow.getTableName()+" where 1=1");
		sql.append(param.getString("businessId"),"and business_id = ?",false);
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
	
	
	
	@WebControl(name = "categoryDict",type = Types.DICT)
	public JSONObject categoryDict() {
		return getDictByQuery("select flow_code,flow_name from yq_flow_category where  p_flow_code = ?","0");
	}
	
	@WebControl(name = "categoryInfo",type = Types.RECORD)
	public JSONObject categoryInfo() {
		String flowCode = param.getString("conf.FLOW_CODE");
		return queryForRecord("select * from yq_flow_category where flow_code = ?", flowCode);
	}

	@WebControl(name = "approveResult",type=Types.TEMPLATE)
	public JSONObject approveResult() {
		String businessId = param.getString("businessId");
		EasySQL sql = getEasySQL("select t1.* from yq_flow_approve_result t1 where 1=1");
		sql.append(businessId,"and t1.business_id = ?",false);
		sql.append("order by t1.get_time");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	/*
	 * 我的申请
	 */
	@WebControl(name = "myFlowApply",type = Types.TEMPLATE)
	public JSONObject myFlowApply() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t4.flow_name from yq_flow_apply t1 LEFT JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		this.setFlowCondition(sql);
		sql.append(getUserId(),"and t1.apply_by = ?");
		sql.append("order by t1.apply_time desc");
		
		int homePage = param.getIntValue("homePage");
		if(homePage==1) {
			EasyQuery query = getQuery();
			query.setMaxRow(8);
			setQuery(query);
			return queryForList(sql.getSQL(), sql.getParams());
		}else {
			return queryForPageList(sql.getSQL(), sql.getParams());
		}
	}
	
	/*
	 * 我的申请数量
	 */
	@WebControl(name = "myFlowApplyCount",type = Types.OTHER)
	public JSONObject myFlowApplyCount() {
		EasySQL sql = new EasySQL("select count(1) from yq_flow_apply t1");
		sql.append("where 1=1");
		sql.append(30,"and t1.apply_state <> ?");
		sql.append(getUserId(),"and t1.apply_by = ?");
		return getJsonResult(queryForString(sql.getSQL(), sql.getParams()));
	}
	
	/**
	 * 我的待办数量
	 * @return
	 */
	@WebControl(name = "myFlowTodoCount",type = Types.OTHER)
	public JSONObject myFlowTodoCount() {
		EasySQL sql = new EasySQL("select count(1) from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("where 1=1");
		sql.append(0,"and t1.apply_state > ?");
		sql.append(0,"and t2.check_result = ?");
		sql.append(getUserId(),"and find_in_set(?,t2.check_user_id)");
		return getJsonResult(queryForString(sql.getSQL(), sql.getParams()));
	}
	
	//我的已办数量
	@WebControl(name = "myFlowDoneCount",type = Types.OTHER)
	public JSONObject myFlowDoneCount() {
		EasySQL sql = new EasySQL("select count(1)");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.business_id = t1.apply_id");
		sql.append("where 1=1");
		sql.append(0,"and t2.check_result > ?");
		sql.append(getUserId(),"and t2.check_user_id = ?");
		return getJsonResult(queryForString(sql.getSQL(), sql.getParams()));
	}
	
	/**
	 * 我的待办
	 * @return
	 */
	@WebControl(name = "myFlowTodo",type = Types.TEMPLATE)
	public JSONObject myFlowTodo() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t2.result_id,t2.read_time,t4.flow_name from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		sql.append(0,"and t1.apply_state > ?");
		this.setFlowCondition(sql);
		sql.append(0,"and t2.check_result = ?");
		sql.append(getUserId(),"and find_in_set(?,t2.check_user_id)");
		sql.append("order by t2.get_time desc");
		int homePage = param.getIntValue("homePage");
		if(homePage==1) {
			EasyQuery query = getQuery();
			query.setMaxRow(8);
			setQuery(query);
			return queryForList(sql.getSQL(), sql.getParams());
		}else {
			return queryForPageList(sql.getSQL(), sql.getParams());
		}
	}
	
	/**
	 * 已办流程
	 * @return
	 */
	@WebControl(name = "myFlowDone",type = Types.TEMPLATE)
	public JSONObject myFlowDone() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t2.check_time,t4.flow_name,");
		sql.append("( SELECT CONCAT(t3.node_name,',',t3.check_name)  FROM yq_flow_approve_result t3 where t3.result_id = t1.next_result_id ) current_node ");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.business_id = t1.apply_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		this.setFlowCondition(sql);
		sql.append(0,"and t2.check_result > ?");
		sql.append(getUserId(),"and t2.check_user_id = ?");
		sql.append("order by t2.check_time desc");
		int homePage = param.getIntValue("homePage");
		if(homePage==1) {
			EasyQuery query = getQuery();
			query.setMaxRow(8);
			setQuery(query);
			return queryForList(sql.getSQL(), sql.getParams());
		}else {
			return queryForPageList(sql.getSQL(), sql.getParams());
		}
	}
	
	/**
	 *    流程查询
	 * @return
	 */
	@WebControl(name = "flowList",type = Types.TEMPLATE)
	public JSONObject flowList() {
		EasySQL sql = new EasySQL("select t1.*,t2.node_name,t2.check_result,t2.check_name,t2.check_time,t4.flow_name");
		sql.append("from yq_flow_apply t1 INNER JOIN yq_flow_approve_result t2 on t2.result_id = t1.next_result_id");
		sql.append("LEFT JOIN yq_flow_category t4 on t1.flow_code = t4.flow_code");
		sql.append("where 1=1");
		this.setFlowCondition(sql);
		sql.append(param.getString("checkResult"),"and t2.check_result = ?");
		sql.append(getUserId(),"and find_in_set(?,t4.flow_mgr_ids)");
		sql.append(param.getString("checkUserId"),"and t2.check_user_id = ?");
		sql.append("order by t1.apply_time desc,t1.apply_id,t2.get_time");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	private void setFlowCondition(EasySQL sql) {
		sql.append(param.getString("flowCode"),"and t1.flow_code = ?");
		sql.appendLike(param.getString("applyTitle"),"and t1.apply_title like ?");
		sql.appendLike(param.getString("applyNo"),"and t1.apply_no like ?");
		sql.append(param.getString("applyState"),"and t1.apply_state = ?");
		sql.append(param.getString("checkName"),"and t1.check_name = ?");
		sql.append(param.getString("applyName"),"and t1.apply_name = ?");
		sql.appendLike(param.getString("flowName"),"and t4.flow_name like ?");
		
	}
	

	
}
