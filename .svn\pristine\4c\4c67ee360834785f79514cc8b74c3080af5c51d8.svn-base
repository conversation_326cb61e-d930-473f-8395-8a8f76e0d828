package com.yunqu.work.servlet.flow;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.List;

import javax.servlet.annotation.WebServlet;

import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.BaseFlowServlet;
import com.yunqu.work.base.FlowConstants;
import com.yunqu.work.model.ApproveNodeModel;
import com.yunqu.work.model.ApproveResultModel;
import com.yunqu.work.model.ApproveResultRecord;
import com.yunqu.work.model.FlowApplyModel;
import com.yunqu.work.model.FlowModel;
import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.ApproveService;
import com.yunqu.work.service.BxService;
import com.yunqu.work.service.FlowService;

@WebServlet("/servlet/bx/*")
public class YqBxServlet extends BaseFlowServlet {
	private static final long serialVersionUID = 1L;
	
	public void actionForIndex() {
		renderHtml("404");
	}
	
	
	public EasyResult actionForSubmitApply() {
		JSONObject params = getJSONObject();
		this.info("actionForSubmitApply:"+params.toJSONString(), null);
		JSONObject business = JsonKit.getJSONObject(params, "business");
		
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String businessId = params.getString("businessId");
		String deptId = business.getString("BX_DEPT_ID");
		if(StringUtils.isBlank(businessId)) {
			businessId = FlowService.getService().getID();
		}
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		String[] userInfo =  BxService.getService().getApproverUser(deptId);
		if(userInfo==null) {
			return EasyResult.fail("请配置审批人");
		}
		
		String doAction = params.getString("doAction");
		EasyRecord applyRecord = getApplyRecord(flowCode);
		applyRecord.set("apply_state","submit".equals(doAction)?10:0);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.set("apply_id",businessId);
		applyRecord.setColumns(applyInfo);
		applyRecord.set("apply_no","submit".equals(doAction)?getBxLoanNo(flowCode):"");
		
		boolean bl = true;
		EasyRecord record = null;
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
			String bxMoney = record.getString("BX_MONEY");
			if(StringUtils.notBlank(bxMoney)) {
				record.set("HZ_MONEY",bxMoney );
//				applyRecord.set("data1",bxMoney);
			}
		}
		try {
			updateItem(params,businessId,false);
			if(record!=null) {
				bl = this.getQuery().save(record);
			}
			if(bl) {
				this.getQuery().save(applyRecord);
			}
			this.saveFile(businessId,params);
			this.updateBxRecord(businessId);
			this.saveContactUnit(record);
			this.saveFile(businessId,params);
			if("submit".equals(doAction)) {
				String result = this.startFlow(flow,businessId,deptId,applyState);
				if(result==null) {
					this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
					return EasyResult.fail("流程节点未配置,请联系管理员");
				}
				return EasyResult.ok(businessId,"提交到审批节点:"+result);
			}else {
				addReviseLog(businessId,"保存草稿");
			}
			return EasyResult.ok(businessId);
		} catch (Exception e) {
			this.error(null, e);
			this.delApplyDatas(businessId, flow.getTableName(),flow.getItemTableName());
			return EasyResult.fail("流程异常,请联系管理员"+e.getMessage());
		}
	}
	
	public EasyResult actionForUpdateApply() {
		JSONObject params = getJSONObject();
		this.info("actionForUpdateApply:"+params.toJSONString(), null);
		
		JSONObject business = JsonKit.getJSONObject(params, "business");
		
		String businessId = params.getString("businessId");
		String flowCode = params.getString("flowCode");
		int applyState = params.getIntValue("applyState");
		String doAction = params.getString("doAction");
		String deptId = business.getString("BX_DEPT_ID");
		
		Calendar calendar=Calendar.getInstance();
		int day = calendar.get(Calendar.DAY_OF_MONTH);
		if(day>=27&&applyState!=FlowConstants.FLOW_STAT_CHECK_RETURN) {
			return EasyResult.fail("每月27号费用报销系统自动关闭，次月1号开启。");
		}
		
		FlowModel flow = ApproveService.getService().getFlow(flowCode);
		String[] userInfo =  BxService.getService().getApproverUser(deptId);
		if(userInfo==null) {
			return EasyResult.fail("请配置审批人");
		}
		
		boolean bl = true;
		
		EasyRecord applyRecord = null;
		if("submit".equals(doAction)) {
			applyRecord =  getApplyRecord(businessId,flowCode,applyState);
		}else {
			applyRecord = getApplyRecord();
		}
		
		EasyRecord record = null;
		if(!business.isEmpty()) {
			record = new EasyRecord(flow.getTableName(),"business_id");
			record.setColumns(business);
			record.setPrimaryValues(businessId);
			String bxMoney = record.getString("BX_MONEY");
			if(StringUtils.notBlank(bxMoney)) {
				record.set("HZ_MONEY",bxMoney );
				applyRecord.set("data1",bxMoney);
			}
		}
		
		applyRecord.setPrimaryValues(businessId);
		JSONObject applyInfo  = JsonKit.getJSONObject(params, "apply");
		applyRecord.setColumns(applyInfo);
		applyRecord.set("apply_id",businessId);
		if("submit".equals(doAction)) {
			applyRecord.set("apply_state",10);
			if(applyState!=FlowConstants.FLOW_STAT_CHECK_RETURN) {
				applyRecord.set("apply_no",getBxLoanNo(flowCode));
			}
		}

		try {
			updateItem(params,businessId,true);
			if(record!=null) {
				bl = this.getQuery().update(record);
			}
			if(bl) {
				this.getQuery().update(applyRecord);
			}
			this.updateBxRecord(businessId);
			this.saveContactUnit(record);
			if("submit".equals(doAction)) {
				this.startFlow(flow,businessId,deptId,applyState);
			}else {
				addReviseLog(businessId,"保存草稿");
			}
		} catch (Exception e) {
			this.error(null, e);
			return EasyResult.fail("流程异常,请联系管理员"+e.getMessage());
		}
		return EasyResult.ok();
		
	}
	
	private String startFlow(FlowModel flow,String businessId,String deptId,int applyState) throws SQLException {
		List<ApproveNodeModel> approvers = BxService.getService().getApproversByUser(getUserId(),deptId);
		ApproveNodeModel nodeInfo = approvers.get(0);
		
		String todoCheckUserId = nodeInfo.getCheckBy();
		if(todoCheckUserId.indexOf(getUserId())>-1) {
			 nodeInfo = ApproveService.getService().getNode(nodeInfo.getNextNodeId());
		}
		
		String nextResultId = RandomKit.uniqueStr();
		
		EasyRecord resultRecord = getApproveResultRecord(businessId);
		if(applyState==21) {
			resultRecord.set("node_name","修改申请");
		}else {
			resultRecord.set("node_name","申请填写");
		}
		this.getQuery().save(resultRecord);
		String nowResultId = resultRecord.getPrimaryValue().toString();
		
		ApproveResultRecord nextResult = getApproveResultRecord(nodeInfo,businessId,nextResultId,nowResultId);
		boolean hasTrust = this.saveApproveNode(nextResult,flow.getFlowCode());
//		this.getQuery().save(nextResult);
		
		FlowService.getService().updateApplyBegin(businessId, nodeInfo.getNodeId(),nowResultId,nextResultId);
		
		FlowApplyModel apply = ApproveService.getService().getApply(businessId);
		
		if(hasTrust) {
			nodeInfo.setCheckBy(nextResult.getCheckUserId());
		}
		
		//微信推送给审批人
		MessageModel msgModel  = getMessageModel(apply, nodeInfo.getCheckBy(), null, null);
		
		this.sendMsg(msgModel);
		
		return nodeInfo.getNodeName();
	}
	
	/**
	 * 执行审批
	 * @return
	 */
	public EasyResult actionForDoApprove() {
		try {
			String msg = "操作成功";
			JSONObject params = getJSONObject();
			this.info("actionForDoApprove:"+params.toJSONString(),null);
			
			String businessId = params.getString("businessId");
			String resultId = params.getString("resultId");
			if(StringUtils.isBlank(resultId)) {
				return EasyResult.fail("审批人不存在");
			}
			
			ApproveResultModel result = ApproveService.getService().getResult(resultId);
			if(result.getCheckResult()!=0) {
				return EasyResult.fail("您已经审批过了.");
			}
			if(!result.getCheckUserId().contains(getUserId())) {
				return EasyResult.fail("您没权限审批.");
			}
			
			
			//获取下一级节点
			ApproveNodeModel approveNode = BxService.getService().getNodeByResultId(resultId);
			if(approveNode==null) {
				return EasyResult.fail("请联系管理员配置审批人员");
			}
			String nodeId = approveNode.getNodeId();
			String nextNodeId =approveNode.getNextNodeId();
			
			
			FlowApplyModel apply = ApproveService.getService().getApply(businessId);
			String deptId = this.getQuery().queryForString("select bx_dept_id from yq_flow_bx_base where business_id = ?", businessId);
			

			int checkResult = params.getIntValue("checkResult");//1 通过 2拒绝
			String checkDesc = params.getString("checkDesc");
			EasyRecord record = getApproveResultRecord(resultId,checkResult,checkDesc);
			this.getQuery().update(record);
			
			
			MessageModel msgModel = getMessageModel(apply, null, null, null);
			
			if(checkResult==2) {
				this.getQuery().executeUpdate("update yq_flow_apply set apply_state = ?,last_result_time = ? where apply_id = ?",21,EasyDate.getCurrentDateString(),businessId);
				msgModel.setData4("审批退回");
				msgModel.setMsgLabel("审批退回");
				msgModel.setDesc("审批退回："+checkDesc);
				msgModel.setReceiver(apply.getApplyBy());
			}else {
				if("0".equals(nextNodeId)) {
					msg = "审批完成,流程结束";
					msgModel.setMsgLabel("审批完成");
					msgModel.setReceiver(apply.getApplyBy());
					FlowService.getService().updateApplyEnd(businessId,nodeId,resultId);
					this.checkEnd(businessId);
				}else {
					msgModel.setMsgLabel("请尽快审批");
					
					ApproveNodeModel nextNodeInfo = BxService.getService().getNodeInfo(nextNodeId,deptId,params);
					
					String todoCheckUserId = nextNodeInfo.getCheckBy();
					if(todoCheckUserId.indexOf(apply.getApplyBy())>-1) {
						nextNodeId = nextNodeInfo.getNextNodeId();
						if(!"0".equals(nextNodeId)) {
							nextNodeInfo = BxService.getService().getNodeInfo(nextNodeId,deptId,params);
						}
					}
					msgModel.setMsgLabel(nextNodeInfo.getNodeName());
					msgModel.setReceiver(nextNodeInfo.getCheckBy());
					
					String nextResultId = RandomKit.uniqueStr();
					ApproveResultRecord resultRecord = getApproveResultRecord(nextNodeInfo, businessId, nextResultId,resultId);
					boolean hasTrust = this.saveApproveNode(resultRecord,apply.getFlowCode());
					if(hasTrust) {
						msgModel.setReceiver(resultRecord.getCheckUserId());
					}
					
					FlowService.getService().updateApplyInfo(businessId, resultId,nextResultId,nodeId,nextNodeId, 20);
					msg = "提交成功,审批下一个节点:"+nextNodeInfo.getNodeName();
				}
			}
			this.sendMsg(msgModel);
			return EasyResult.ok(businessId,msg);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail(e.getMessage());
		}
	}
	
	public void sendMsg(MessageModel msgModel) {
		String str = "林单婷,陈琛,杨曼丽";
		String recevier = msgModel.getReceiveName();
		if(StringUtils.notBlank(recevier)&&str.indexOf(recevier)>-1) {
			this.sendAllMsg(msgModel,false);
		}else {
			this.sendAllMsg(msgModel);
		}
	}
	
	public void updateBxRecord(String businessId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("update yq_flow_bx_base t1 set ");
			sql.append("t1.bx_money = ( select sum(amount) from yq_flow_bx_item t2 where t2.business_id = t1.business_id ),");
			sql.append("t1.tax_money = ( select sum(taxes) from yq_flow_bx_item t2 where t2.business_id = t1.business_id ),");
			sql.append("t1.no_tax_money = ( select sum(r_amount) from yq_flow_bx_item t2 where t2.business_id = t1.business_id),");
			sql.append("t1.hz_money = ( select sum(hz_amount) from yq_flow_bx_item t2 where t2.business_id = t1.business_id)");
			sql.append(businessId,"where t1.business_id = ?");
			this.getQuery().executeUpdate(sql.getSQL(), sql.getParams());
			
			this.getQuery().executeUpdate("update yq_flow_bx_base t1 set t1.pay_money = t1.hz_money - t1.reverse_money where t1.business_id = ?", businessId);
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
	}
	
	//保存往来单位
	public void saveContactUnit(EasyRecord bxBase) {
		String payType = bxBase.getString("PAY_TYPE");
		if("对公付款".equals(payType)) {
			String unitNo = bxBase.getString("CONTACT_CODE");
			String bankAcount = bxBase.getString("CONTACT_BANK_NO");
			try {
				boolean result = this.getQuery().queryForExist("select count(1) from yq_finance_contact_unit where unit_no = ? and bank_acount = ?",unitNo,bankAcount);
				if(!result) {
					EasyRecord record = new EasyRecord("yq_finance_contact_unit","unit_no","bank_acount");
					record.set("unit_no",unitNo);
					record.set("unit_name",bxBase.getString("CONTACT_UNIT"));
					record.set("bank_acount",bankAcount);
					record.set("bank_name",bxBase.getString("CONTACT_BANK"));
					record.set("create_time",EasyDate.getCurrentDateString());
					record.set("creator",getUserId());
					record.set("create_name",getUserName());
					this.getQuery().save(record);
				}
			} catch (SQLException e) {
				this.error(e.getMessage(), e);
			}
		}
	}
	
	
	private void updateItem(JSONObject params,String bussinessId,boolean isUpdate) throws SQLException {
		Object itemIndexObj = params.get("itemIndex");
		JSONArray itemIndexs = null;
		if(itemIndexObj instanceof String) {
			itemIndexs = new JSONArray();
			itemIndexs.add(itemIndexObj.toString());
		}else {
			itemIndexs = params.getJSONArray("itemIndex");
		}
		for(int i= 0;i<itemIndexs.size();i++) {
				EasyRecord itemModel = new EasyRecord("yq_flow_bx_item","ITEM_ID");
				String item = itemIndexs.getString(i);
				String newItemId = getNewItemId(item);
				
				itemModel.set("BUSINESS_ID", bussinessId);
				String feeJson = params.getString("feeJson_"+item);
				if(StringUtils.notBlank(feeJson)) {
					JSONObject feejsonRow = JSONObject.parseObject(feeJson);
					String invoiceIdArray = feejsonRow.getString("invoiceIdArray");	
					feejsonRow.remove("invoiceIdArray");
					itemModel.putAll(feejsonRow);
					itemModel.set("INVOICE_NO", params.getString("invoiceNo_"+item));
					
					
					this.getQuery().executeUpdate("update yq_finance_bx_invoice set bx_state = ?,business_id = '',bx_item_id = '',use_time = '',use_by ='' where bx_item_id = ?",0,newItemId);
					if(StringUtils.notBlank(invoiceIdArray)) {
						String[] array = invoiceIdArray.split(",");
						for(String invoiceId:array) {
							EasyRecord invoiceRecord = new EasyRecord("yq_finance_bx_invoice","invoice_id");
							invoiceRecord.set("INVOICE_ID", invoiceId);
							invoiceRecord.set("BX_STATE", 1);
							invoiceRecord.set("BUSINESS_ID", bussinessId);
							invoiceRecord.set("BX_ITEM_ID", newItemId);
							invoiceRecord.set("USE_TIME", EasyDate.getCurrentDateString());
							invoiceRecord.set("USE_BY_NAME", getUserName());
							invoiceRecord.set("USE_BY", getUserId());
							this.getQuery().update(invoiceRecord);
						}
					}
				}
				String budgetMonth = params.getString("budgetMonth_"+item);
				String bxDate = params.getString("bxDate_"+item);
				itemModel.set("BX_DATE", bxDate);
				if(StringUtils.isBlank(budgetMonth)) {
					itemModel.set("BUDGET_MONTH", bxDate.substring(0,7));
				}else {
					itemModel.set("BUDGET_MONTH", budgetMonth);
				}
				itemModel.set("CONTRACT_ID", params.getString("contractId_"+item));
				itemModel.set("INVOICE_TYPE", params.getString("invoiceType_"+item));
				itemModel.set("CONTRACT_NAME", params.getString("contractName_"+item));
				itemModel.set("CONTRACT_NO", params.getString("contractNo_"+item));
				itemModel.set("CONTRACT_STATE", params.getString("contractState_"+item));
				itemModel.set("FEE_IN_TYPE", params.getString("feeInType_"+item));
				itemModel.set("FEE_IN_CODE", params.getString("feeInCode_"+item));
				itemModel.set("FEE_TYPE", params.getString("feeType_"+item));
				itemModel.set("FEE_CODE", params.getString("feeCode_"+item));
				itemModel.set("SUBJECT_NO", params.getString("subjectNo_"+item));
				itemModel.set("SUBJECT_NAME", params.getString("subjectName_"+item));
				itemModel.set("FEE_DESC", params.getString("feeDesc_"+item));
				itemModel.set("AMOUNT", params.getString("amount_"+item));//含税金额
				itemModel.set("HZ_AMOUNT", params.getString("amount_"+item));//核准金额
				itemModel.set("ITEM_INDEX", params.getString("orderIndex_"+item));
				itemModel.set("DEPT_ID", params.getString("deptId_"+item));
				itemModel.set("DEPT_NAME", params.getString("deptName_"+item));
				itemModel.set("DEPT_TYPE", params.getString("deptType_"+item));
				itemModel.set("DEPT_CODE", params.getString("deptCode_"+item));
				itemModel.set("TAX_RATE", params.getString("taxRate_"+item));//税率
				itemModel.set("TAX_RATE_CODE", params.getString("taxRateCode_"+item));//税率编码
				itemModel.set("TAXES", params.getString("taxes_"+item));//税金
				itemModel.set("R_AMOUNT", params.getString("rAmount_"+item));//去税金额
				itemModel.set("PRODUCT_LINE", params.getString("productLine_"+item));
				itemModel.set("PRODUCT_LINE_NO", params.getString("productLineNo_"+item));
				String itemId = params.getString("itemId_"+item);
				if(itemId.length()>20) {
					itemModel.set("ITEM_ID", itemId);
					this.getQuery().update(itemModel);
				}else {
					itemModel.setPrimaryValues(newItemId);
					this.getQuery().save(itemModel);
				}
		}
	}
	
	private String getNewItemId(String itemId) {
		if(itemId.length()>20) {
			return itemId;
		}else {
			return RandomKit.uniqueStr();
		}
	}
	
	private void checkEnd(String businessId) throws SQLException {
		this.getQuery().executeUpdate("update yq_flow_bx_base set pay_state = ? where business_id  = ?", 1,businessId);
	}
	
	public EasyResult actionForDelItem() {
		JSONObject params = getJSONObject();
		String itemId = params.getString("itemId");
		try {
			this.getQuery().executeUpdate("delete from yq_flow_bx_item where item_id = ?",itemId);
			this.getQuery().executeUpdate("update yq_finance_bx_invoice set business_id = '',bx_item_id = '',use_time = '',bx_state = 0,use_by = '' where bx_item_id = ?",itemId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	
	public EasyResult actionForDelApply() {
		JSONObject params = getJSONObject();
		this.info("actionForDelApply:"+params.toJSONString(),null);
		String businessId = params.getString("businessId");
		try {
			this.getQuery().executeUpdate("delete from yq_flow_bx_item where business_id = ?",businessId);
			this.getQuery().executeUpdate("delete from yq_flow_bx_base where business_id = ?",businessId);
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id = ?",businessId);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id = ?",businessId);
			this.getQuery().executeUpdate("update yq_finance_bx_invoice set business_id = '',bx_item_id = '',use_time = '',bx_state = 0,use_by = '' where business_id = ?",businessId);
		} catch (SQLException e) {
			this.error(null, e);
			return EasyResult.fail();
		}
		return EasyResult.ok();
	}
	

}
