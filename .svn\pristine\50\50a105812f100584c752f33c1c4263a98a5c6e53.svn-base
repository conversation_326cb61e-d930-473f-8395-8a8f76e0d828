<%@ page language="java" contentType="text/html;charset=UTF-8"%>
     <table class="table table-edit table-vzebra">
         <tbody>
         	<!-- <tr>
             	<td>任务创建时间</td>
                 <td>
                     <div class="input-group input-group-sm" style="width: 235px">
                     	<input style="border-right: 0;" type="text" data-laydate="{type:'date',range: '到'}" name="createTime" class="form-control input-sm">
                     	<div class="input-group-btn">
                     		<select class="form-control input-sm" style="width:70px;" onchange="getDate(this,'createTime')">
	                     		<option value="">本月</option>
	                     		<option value="today">今日</option>
	                     		<option value="yesterday">昨天</option>
	                     		<option value="threeDay">三天内</option>
	                     		<option value="oneWeek">一周内</option>
	                     		<option value="oneMonth">一个月内</option>
	                     		<option value="threeMonth">三个月内</option>
	                     	</select>
                     	</div>
                     </div>
                 </td>
             </tr> -->
             <tr>
             	<td>任务名称</td>
             	<td>
					 <input type="text" style="width: 235px" name="taskName" class="form-control input-sm">
             	</td>
             </tr>
             <tr>
                 <td style="width: 120px;">任务完成时间</td>
                 <td>
                 	<div class="input-group input-group-sm" style="width: 235px">
                     	<input style="border-right: 0;" type="text" data-laydate="{type:'date',range: '到'}" name="finishTime" class="form-control input-sm">
                     	<div class="input-group-btn">
                     		<select class="form-control input-sm" style="width:70px;" onchange="getDate(this,'finishTime')">
	                     		<option value="">请选择</option>
	                     		<option value="7">7天内</option>
	                     		<option value="10">10天内</option>
	                     		<option value="15">15天内</option>
	                     		<option value="30">30天内</option>
	                     		<option value="60">60天内</option>
	                     		<option value="90">90天内</option>
	                     	</select>
                     	</div>
                     </div>
                 </td>
             </tr>
             <tr>
                 <td style="width: 120px;">任务更新时间</td>
                 <td>
                 	<div class="input-group input-group-sm" style="width: 235px">
                     	<input style="border-right: 0;" type="text" data-laydate="{type:'date',range: '到'}" name="updateTime" class="form-control input-sm">
                     	<div class="input-group-btn">
                     		<select class="form-control input-sm" style="width:70px;" onchange="getDate(this,'updateTime')">
	                     		<option value="">请选择</option>
	                     		<option value="7">7天内</option>
	                     		<option value="10">10天内</option>
	                     		<option value="15">15天内</option>
	                     		<option value="30">30天内</option>
	                     		<option value="60">60天内</option>
	                     		<option value="90">90天内</option>
	                     	</select>
                     	</div>
                     </div>
                 </td>
             </tr>
               <tr>
             	<td>负责部门</td>
             	<td>
					 <select style="width: 235px" name="assignDeptId" data-mars="EhrDao.allDept" data-mars-reload="false" class="form-control input-sm">
                    		<option value="">请选择</option>
                   	</select>
		     	</td>
             </tr>
             <tr>
             	<td>发起部门</td>
             	<td>
					 <select style="width: 235px" name="deptId" data-mars="EhrDao.allDept" data-mars-reload="false" class="form-control input-sm">
                    		<option value="">请选择</option>
                   	</select>
			     </td>
             </tr>
             <tr style="display: none;">
             	<td>任务状态</td>
             	<td>
				  <select id="taskStateLabel" class="form-control input-sm" style="width: 235px"		>
                   		<option value="">全部</option>
                   		<option value="10" data-class="label label-warning">待办中</option>
                   		<option value="20" data-class="label label-info">进行中</option>
                   		<option value="30" data-class="label label-success">已完成</option>
                   		<option value="40" data-class="label label-default">已验收</option>
                   		<option value="42" data-class="label label-danger">验收不通过</option>
                  	</select>
				</td>
             </tr>
             <tr>
             	<td>项目</td>
             	<td>
				  <input type="hidden" name="projectId" class="form-control input-sm">
				  <input type="text" id="projectId" class="form-control input-sm" style="width: 235px">
			    </td>
             </tr>
             <tr>
             	<td>创建人</td>
             	<td>
					 <input type="hidden" name="creator" class="form-control input-sm">
					 <input type="text" id="creator" class="form-control input-sm" style="width: 235px">
	    		</td>
             </tr>
             <tr>
             	<td>负责人</td>
             	<td>
					 <input type="hidden" name="assignUserId" class="form-control input-sm">
					 <input type="text" id="assignUserId" class="form-control input-sm" style="width: 235px">
	    		</td>
             </tr>
             <tr>
             	<td>任务类型</td>
             	<td>	
					<select style="width: 235px" data-mars-reload="false" name="taskTypeId" data-mars="TaskDao.taskType" class="form-control">
						<option value="">请选择</option>
					</select>
			   </td>
             </tr>
           
             <tr>
             	<td></td>
             	<td></td>
             </tr>
         </tbody>
     </table>
     <div style="height: 100px;"></div>
     <div class="layer-foot text-c">
         <button class="btn btn-sm btn-primary" type="button" onclick="popup.layerClose(this);taskMgr.query();" style="width: 98%;">开始筛选</button>
     </div>
