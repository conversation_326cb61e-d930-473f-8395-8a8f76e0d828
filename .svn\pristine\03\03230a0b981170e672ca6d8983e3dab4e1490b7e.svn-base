<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<EasyTag:override name="head">
	<title>流程节点配置</title>
	<style>
		.layui-timeline-item:hover a{opacity:1;}
		.ibox-content{min-height: calc(100vh - 100px)};
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		<form  autocomplete="off"  onsubmit="return false;" class="form-inline" id="dataMgrForm">
			<input name="orderId" type="hidden" value="${param.orderId}"/>
			<div class="ibox">
				<div class="ibox-title clearfix">
	          		 <div class="form-group">
	          		     <h5>流程节点配置</h5>
						 <div class="input-group input-group-sm">
						    <span class="input-group-addon">类别</span>	
							<select name="busiType" onchange="queryData();" class="form-control input-sm">
							      <option value="review">采购评审 </option>
							      <option value="payment">采购付款</option>
						    </select>									  
					  	</div>
						 <div class="input-group input-group-sm ml-15">
							 <button type="button" class="btn btn-sm btn-default" onclick="queryData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
						 </div>
						 <div class="input-group input-group-sm pull-right">
							 <button type="button" class="btn btn-sm btn-info" onclick="addNode()"> 新增</button>
						 </div>
	          	     </div>
	              </div> 
					<div class="ibox-content">
							<table id="nodeTable"></table>
					</div>
				</div>
		</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
	
			$(function(){
				$("#dataMgrForm").render({success:function(){
					initList();
				}});
			});
			
			function initList(){
				$("#dataMgrForm").initTable({
					mars:'OrderDao.nodeConfig',
					id:'nodeTable',
					page:true,
					height:'full-130',
					cellMinWidth:100,
					cols: [[
					 {
						 field:'CHECK_LINE',
						 title:'节点次序'
					 },{
						 field:'NODE_NAME',
						 title:'节点描述'
					 },{
						 field:'USER_NAME',
						 title:'审批人',
						 minWidth:160
					 },{
					    field: 'CREATE_TIME',
						title: '操作时间',
						align:'center'
					},{
						field:'',
						title:'节点描述',
						event:'editNode',
						style:'text-decoration:underline;',
						templet:'<div>修改</div>'
					}
				]],done:function(){
					
			  	}
			 });
		   }
			
			function editNode(data){
				var id = data['NODE_ID'];
				var busiType = $("[name='busiType']").val();
				popup.layerShow({id:'editNode',area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/config/node-edit.jsp',title:'修改',data:{nodeId:id,busiType:busiType}});
			}
			function addNode(){
				var busiType = $("[name='busiType']").val();
				popup.layerShow({id:'editNode',area:['550px','400px'],offset:'20px',url:'${ctxPath}/pages/erp/order/config/node-edit.jsp',title:'新增',data:{nodeId:'',busiType:busiType}});
			}
			
			function queryData(){
				$("#dataMgrForm").queryData({id:'nodeTable'});
			}
			
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout-layui-auto.jsp" %>

