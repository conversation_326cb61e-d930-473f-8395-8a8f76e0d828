package com.yunqu.work.base;

import java.sql.SQLException;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.work.model.MessageModel;
import com.yunqu.work.service.EmailService;
import com.yunqu.work.service.MessageService;
import com.yunqu.work.service.WxMsgService;
public abstract class BaseFlowServlet extends AppBaseServlet{ 
	private static final long serialVersionUID = 1L;

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME+"-flow";
	}
	
	protected void sendAllMsg(MessageModel msgModel) {
		WxMsgService.getService().sendFlowTodoCheck(msgModel);
		MessageService.getService().sendMsg(msgModel);
		EmailService.getService().sendFlowNoticeEmail(msgModel);
	}
	
	protected void delApplyData(String id) {
		try {
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id  = ?", id);
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	protected void delApplyDatas(String id,String... params) {
		try {
			this.getQuery().executeUpdate("delete from yq_flow_apply where apply_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_files where fk_id  = ?", id);
			this.getQuery().executeUpdate("delete from yq_flow_approve_result where business_id  = ?", id);
			if(params!=null) {
				for(String obj:params) {
					if(StringUtils.isBlank(obj)) {
						continue;
					}
					if(!"yq_flow_apply".equalsIgnoreCase(obj)) {
						this.getQuery().executeUpdate("delete from "+obj+" where business_id  = ?", id);
					}
				}
			}
		} catch (SQLException e) {
			this.error(null, e);
		}
	}
	
	protected EasyRecord getApplyRecord(String flowCode) {
		return getApplyRecord(null,flowCode);
	}
	
	protected EasyRecord getApplyRecord(String flowCode,String applyId,String applyState) {
		EasyRecord record =  getApplyRecord(null,flowCode);
		record.set(applyState, applyState);
		record.setPrimaryValues(applyId);
		return record;
	}
	
	protected EasyRecord getApplyRecord() {
		EasyRecord model = new EasyRecord("yq_flow_apply","apply_id"); 
		model.set("update_by",getUserId());
		model.set("update_time",EasyDate.getCurrentDateString());
		return model;
	}
	
	protected EasyRecord getApplyRecord(String id,String flowCode) {
		EasyRecord model = new EasyRecord("yq_flow_apply","apply_id"); 
		model.set("apply_time", EasyDate.getCurrentDateString());
		model.set("apply_date", EasyCalendar.newInstance().getDateInt());
		model.set("apply_by", getUserId());
		model.set("apply_name", getUserName());
		model.set("dept_id", getDeptId());
		model.set("dept_name", getDeptName());
		model.set("flow_code", flowCode);
		if(id!=null) {
			model.setPrimaryValues(id);
		}
		return model;
	}
}
