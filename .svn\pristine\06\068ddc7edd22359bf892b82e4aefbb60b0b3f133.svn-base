	function moreDc(){
		popup.openTab({id:'task',url:ctxPath+'/pages/food/my-book-stat.jsp',title:'我的点餐'});
	}
	function bookFood(){
		popup.layerShow({type:1,anim:0,scrollbar:false,offset:'20px',area:['450px','450px'],url:ctxPath+'/pages/food/book-food.jsp',title:'点餐'});
	}
	function  delBook(id,obj){
		layer.confirm("如您已经用餐,请不要取消!继续操作?",{icon:3,offset:'20px'},function(){
			ajax.remoteCall(ctxPath+"/servlet/food?action=cannelBook",{'id':id},function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon:1,time:1200},function(){
						$(obj).parent().remove();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		});
	}
	var dinnerTypes={2:'中餐',3:'加班餐'};
	var timer = {}
	function initBookFoodData(){
		
		$("#booksInfo").html("");
		ajax.remoteCall(ctxPath+"/webcall?action=FoodDao.myTodayBook",{},function(result) { 
			var dcSwitch=result.dcSwitch;
			if(dcSwitch=='1'){
				$(".dc").remove();
				return;
			}
			var currentTime=result.currentTime;
			var currentDate=result.currentDate;
			var _currentTime=new Date(result.currentTime);
			var eatTimes=result.eatTimes;
			for(var index in eatTimes){

				var obj=eatTimes[index];
				var dinnerType=obj.DINNER_TYPE;
				clearTimeout(timer[dinnerType]);
				var beginTime=new Date(currentDate+" "+obj.BEGIN_TIME);
				var endTime=new Date(currentDate+" "+obj.END_TIME);
				$("#booksInfo").append("<tr class='detail_"+dinnerType+"'><td><label class='label label-info label-outline mr-5'>"+dinnerTypes[obj.DINNER_TYPE]+"</label><span>"+obj.BEGIN_TIME+"-"+obj.END_TIME+"</span><span data-msg class='text-info ml-5 info_"+dinnerType+"'></span></td></tr>");
				countTime(dinnerType,_currentTime.getTime(),beginTime.getTime(),endTime.getTime());
			}
			
			var data=result.data;
			for(var index in data){
				var obj=data[index];
				var ct=obj.CREATE_TIME;
				$("#booksInfo .detail_"+obj.DINNER_TYPE+"").after('<tr><td><label class="label label-info label-outline mr-5">'+dinnerTypes[obj.DINNER_TYPE]+'</label><span>'+obj.FOOD_NAME+' '+ct.substring(10,16)+'</span><a onclick=\'delBook("'+obj.ID+'",$(this))\' class="btn btn-xs btn-link cannelBook layui-hide">取消</a></td></tr>');
			}
		});
	}
	
	function countTime(dinnerType,nowDate,beginTime,endTime){  
         var now = nowDate; 
         var begin=beginTime;
         var end = endTime;  
         var flag=true;
         var leftTime=end - now;
         if(now<begin){
        	 //未到点餐时间
        	 $(".info_"+dinnerType).html("未到点餐时间");
        	 flag=false;
         }
         if(now>end){
        	//超过点餐时间
        	 $(".info_"+dinnerType).html("点餐已经结束");
        	 $("#addDoDc").show();
        	 flag=false;
         }
         if(flag){
        	 $(".info_"+dinnerType).html("结束倒计时：");
        	 $("#doDc").show();
         }else{
        	 //$("#doDc").hide();
         }
         
         //定义变量 d,h,m,s保存倒计时的时间  
         var d,h,m,s; 
         
         if(flag){
             d = Math.floor(leftTime/1000/60/60/24);  
             h = Math.floor(leftTime/1000/60/60%24);  
             m = Math.floor(leftTime/1000/60%60);  
             s = Math.floor(leftTime/1000%60);                     
	         if(h>0&&m>0){
	        	 $(".info_"+dinnerType).append( h+"时"+ m+"分"+ s+"秒");
	         }
	         if(h==0&&m>0){
	        	 $(".info_"+dinnerType).append( m+"分"+s+"秒");
	         }
	         if(m==0&&s>=0){
	        	 $(".info_"+dinnerType).append( s+"秒");
	         }
	         //递归每秒调用countTime方法，显示动态时间效果
         }
         if(nowDate<=endTime){
        	 timer[dinnerType] = setTimeout(function(){
		 		nowDate += 1000;
		 		countTime(dinnerType,nowDate,beginTime,endTime)
		 	},1000);  
         	
         }
    }
	function getHeight(){
		var height=$(window).height();
		var h="90%";
		if(height>650)h='600px';
		return h;
	}
	function foodSetting(){
		popup.layerShow({type:1,maxmin:true,scrollbar:false,area:['500px',getHeight()],url:ctxPath+'/pages/food/book-food-setting.jsp',title:'设置'});
	}
	function foodStat(){
		popup.layerShow({type:1,maxmin:true,scrollbar:false,area:['600px',getHeight()],url:ctxPath+'/pages/food/book-food-stat.jsp',title:'订餐数据'});
	}
	function foodTop(){
		popup.layerShow({type:1,maxmin:true,scrollbar:false,area:['600px',getHeight()],url:ctxPath+'/pages/food/book-food-top.jsp',title:'排行榜'});
	}