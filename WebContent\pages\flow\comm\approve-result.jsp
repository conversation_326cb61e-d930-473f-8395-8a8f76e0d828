<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html;charset=UTF-8"%>
 <table class="layui-table approve-table" style="margin-top: 10px;padding: 10px;">
   <thead>
      <tr>
	      <th style="width: 40px;">序号</th>
	      <th style="width: 120px;">节点名称</th>
	      <th style="width: 110px;">经办人</th>
	      <th style="width: 100px;">审批结果</th>
	      <th style="width: 140px;" class="hidden-print">接收时间</th>
	      <th style="width: 140px;" class="hidden-print">查看时间</th>
	      <th style="width: 140px;">审批时间</th>
	      <th style="min-width: 100px;">审批描述</th>
	      <th style="width: 150px;" class="hidden-print">用时</th>
      </tr>
  </thead>
  <tbody data-mars="FlowDao.approveResult" data-template="approveList"></tbody>
</table>
					
<script id="approveList" type="text/x-jsrender">
  	 {{for data}}
  		<tr {{if CHECK_RESULT=='0'}} class="hidden-print" {{/if}}>
  			<td>{{:#index+1}}</td>
  			<td>{{:NODE_NAME}}</td>
  			<td>
			 {{:CHECK_NAME}} {{if APPROVE_TYPE=='1'}} <span class="label label-info label-outline">协办</span> {{/if}} {{if TRUST_ID}} <a class="hidden-print pull-right" href="javascript:;" onclick="FlowCore.lookTrust('{{:TRUST_ID}}')">查看委托</a> {{/if}} 
			 {{if CHECK_USER_ID}}
			 	<button class="btn btn-xs btn-default hidden-print hover-show pull-right" onclick="autoSelectReplyUser('{{:CHECK_USER_ID}}')" type="button">+留言</button>
			 {{/if}}
			</td>
  			<td>{{if NODE_ID!='0'}} {{call:CHECK_RESULT fn='checkResultLable'}} {{/if}} {{if CC_COUNT>0}} <a class="hidden-print pull-right" href="javascript:;" onclick="FlowCore.lookCC('{{:RESULT_ID}}')">查看抄送</a> {{/if}}</td>
  			<td class="hidden-print">{{:GET_TIME}}</td>
  			<td class="hidden-print">{{:READ_TIME}}</td>
  			<td>{{:CHECK_TIME}}</td>
  			<td class="comment" {{if CHECK_DESC}}onmouseover="FlowCore.tips(this)" onclick="FlowCore.approveCommentLayer('{{:CHECK_USER_ID}}','{{:CHECK_NAME}}','{{:RESULT_ID}}',this)"{{/if}}><textarea data-text="false" class="hidden">{{:CHECK_DESC}}</textarea><span class="hidden-print">{{cutText:CHECK_DESC 70}}</span><span class="visible-print">{{:CHECK_DESC}}</span></td>
  			<td class="hidden-print">{{call:GET_TIME CHECK_TIME fn='timeDiff'}}</td>
  		</tr>
  	  {{/for}}
	{{if data.length==0}}
		<tr><td  colspan="9" class="text-c">暂无数据</td></tr>
	{{/if}}
</script>