<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>选择固定资产</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form method="post" class="form-inline" onsubmit="return false;" id="selecFaForm">
       	     <input id="itemId" value="${param.itemId}" type="hidden"/>
	         <div class="row">
	   			 <div class="input-group input-group-sm ml-15" style="width: 170px">
					 <span class="input-group-addon">资产编号</span>	
					 <input type="text" name="faNo" class="form-control input-sm">
			     </div>
    			 <button type="button" class="btn btn-sm btn-default ml-10" data-event="enter"  onclick="SelectFa.query()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
	   		   </div>
           	   <div class="ibox">
              		<table id="SelectFaList"></table>
               </div>
               <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" onclick="SelectFa.ok()">确定</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="popup.layerClose(this)">关闭</button>
			</div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
			var SelectFa = {
					sid:'${param.sid}',
					query : function(){
						$("#selecFaForm").queryData();
					},
					initTable : function(){
						$("#selecFaForm").initTable({
							mars:'FaDao.myData',
							id:'SelectFaList',
							page:true,
							limit:20,
							height:'350px',
							rowDoubleEvent:'SelectFa.ok',
							cols: [[
					        {type:'${param.type}'},
				             {
			            	 	type: 'numbers',
								title: '序号',
								align:'left'
							 },{
							    field: 'FA_NAME',
								title: '资产名称',
							},{
							    field: 'FA_NO',
								title: '资产编号'
							},{
								field:'CARD_NO',
								title:'卡片编号'
							}
							]]
					       }
						);
					},
					ok : function(selectRow){
						debugger;
						var el = $("[data-sid='"+SelectFa.sid+"']");
						if(selectRow==undefined){
							var checkStatus = table.checkStatus('SelectFaList');
							if(checkStatus.data.length>0){
								var names = [];
								var ids = [];
								var data = checkStatus.data;
								var itemId = $("#itemId").val();
								if(itemId){
									var rowData = data[0];
									$("#item_"+itemId+" [name='data1_"+itemId+"']").val(rowData['CARD_NO']);
									$("#item_"+itemId+" [name='data2_"+itemId+"']").val(rowData['FA_NO']);
									$("#item_"+itemId+" [name='data3_"+itemId+"']").val(rowData['FA_NAME']);
									$("#item_"+itemId+" [name='data4_"+itemId+"']").val(rowData['MODE']);
									$("#item_"+itemId+" [name='data5_"+itemId+"']").val(rowData['UNIT_NAME']);
									$("#item_"+itemId+" [name='data6_"+itemId+"']").val(rowData['CARD_NUM']);
									
									if(FlowCore.params.flowCode.indexOf('fa_receive')>-1){
										$("#item_"+itemId+" [name='data7_"+itemId+"']").val(rowData['FA_MONEY']);
										calcBxMoney();
									}
								}
								popup.layerClose("selecFaForm");
							}else{
								el.val('');
								if(el.prev().length>0){
									el.prev().val('');
								}
							}
						}else{
							var itemId = $("#itemId").val();
							if(itemId){
								var rowData = selectRow;
								$("#item_"+itemId+" [name='data1_"+itemId+"']").val(rowData['CARD_NO']);
								$("#item_"+itemId+" [name='data2_"+itemId+"']").val(rowData['FA_NO']);
								$("#item_"+itemId+" [name='data3_"+itemId+"']").val(rowData['FA_NAME']);
								$("#item_"+itemId+" [name='data4_"+itemId+"']").val(rowData['MODE']);
								$("#item_"+itemId+" [name='data5_"+itemId+"']").val(rowData['UNIT_NAME']);
								$("#item_"+itemId+" [name='data6_"+itemId+"']").val(rowData['CARD_NUM']);
								
								if(FlowCore.params.flowCode.indexOf('fa_receive')>-1){
									$("#item_"+itemId+" [name='data7_"+itemId+"']").val(rowData['FA_MONEY']);
									calcBxMoney();
								}
							}
							popup.layerClose("selecFaForm");
						}
					}
					
			};
			$(function(){
				SelectFa.initTable();
			});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>