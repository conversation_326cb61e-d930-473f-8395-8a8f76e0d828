package com.yunqu.work.dao.bx;

import java.sql.SQLException;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.work.base.AppDaoContext;
import com.yunqu.work.base.FlowConstants;

@WebObject(name = "BxInvoiceDao")
public class BxInvoiceDao extends AppDaoContext{

	@WebControl(name = "invoiceNoQuery",type = Types.TEMPLATE)
	public JSONObject invoiceNoQuery() {
		EasySQL sql = new EasySQL("select t1.invoice_no,t1.invoice_type,t1.amount,t1.dept_name,t2.apply_name,t2.apply_time,t2.apply_no,t2.apply_id,t2.flow_code from yq_flow_bx_item t1,yq_flow_apply t2 where t1.business_id = t2.apply_id");
		sql.appendLike(param.getString("invoiceNo"),"and t1.invoice_no like ?");
		sql.append(FlowConstants.FLOW_STAT_TODO,"and t2.apply_state >=?");
		sql.append("and t1.invoice_no<>''");
		sql.append("ORDER BY t2.apply_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "xmlInvoiceQuery",type = Types.TEMPLATE)
	public JSONObject xmlInvoiceQuery() {
		EasySQL sql = new EasySQL("select t1.*,t2.file_name from yq_finance_xml_invoice t1,yq_files t2 where t1.xml_file_id = t2.file_id");
		sql.appendLike(param.getString("invoiceNo"),"and t1.invoice_no like ?");
		sql.appendLike(param.getString("saleCompany"),"and t1.sale_company like ?");
		sql.append(param.getString("belongUserId"),"and t1.belong_user_id = ?");
		
		String relateBxPerson = param.getString("relateBxPerson");
		if("1".equals(relateBxPerson)) {
			sql.append("and t1.belong_user_id <>''");
		}
		if("0".equals(relateBxPerson)) {
			sql.append("and t1.belong_user_id =''");
		}
		
		String invoiceType = param.getString("invoiceType");
		if(StringUtils.notBlank(invoiceType)) {
			sql.appendRLike(invoiceType,"and t1.invoice_type_code like ?");
		}
		String beginDate = param.getString("beginDate");
		if(StringUtils.notBlank(beginDate)) {
			sql.append(beginDate+" 00:00:00","and t1.xml_upload_time >= ?");
		}
		String endDate = param.getString("endDate");
		if(StringUtils.notBlank(endDate)) {
			sql.append(endDate+" 23:59:59","and t1.xml_upload_time <= ?");
		}
		
		String kpBeginDate = param.getString("kpBeginDate");
		if(StringUtils.notBlank(kpBeginDate)) {
			sql.append(kpBeginDate+" 00:00:00","and t1.invoice_date >= ?");
		}
		String kpEndDate = param.getString("kpEndDate");
		if(StringUtils.notBlank(kpEndDate)) {
			sql.append(kpEndDate+" 23:59:59","and t1.invoice_date <= ?");
		}
		
		sql.append("order by t1.xml_upload_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	private void setBxInvoiceCondition(EasySQL sql) {
		sql.appendLike(param.getString("invoiceNo"),"and t1.invoice_no like ?");
		sql.appendLike(param.getString("saleCompany"),"and t1.sale_company like ?");
		sql.appendRLike(param.getString("invoiceType"),"and t1.invoice_type_code like ?");
		
		String xmlSynState = param.getString("xmlSynState");
		if("0".equals(xmlSynState)) {
			sql.append("and t1.xml_file_id = ''");
		}else if("1".equals(xmlSynState)) {
			sql.append("and t1.xml_file_id <> ''");
		}
		
		String kpBeginDate = param.getString("kpBeginDate");
		if(StringUtils.notBlank(kpBeginDate)) {
			sql.append(kpBeginDate.replaceAll("-",""),"and t1.kp_date_id >= ?");
		}
		String kpEndDate = param.getString("kpEndDate");
		if(StringUtils.notBlank(kpEndDate)) {
			sql.append(kpEndDate.replaceAll("-",""),"and t1.kp_date_id <= ?");
		}
		sql.append(param.getString("kpMonth"),"and t1.kp_month = ?");
	}
	
	@WebControl(name = "selectMyBxInvoice",type = Types.TEMPLATE)
	public JSONObject selectMyBxInvoice() {
		EasySQL sql = new EasySQL("select t1.* from yq_finance_bx_invoice t1 where 1=1");
		sql.append(getUserId(),"and t1.creator = ?");
		sql.append(0,"and t1.bx_state = ?");
		sql.append(param.getString("kpMonth"),"and t1.kp_month = ?");
		
		String hasSelectInvoices = param.getString("hasSelectInvoices");
		if(StringUtils.notBlank(hasSelectInvoices)) {
			String[] array = hasSelectInvoices.split(",");
			if(array.length>1) {
				sql.appendIn(array,"and t1.invoice_no not");
			}else {
				sql.append(array[0],"and t1.invoice_no <> ?");
			}
		}
		
		this.setBxInvoiceCondition(sql);
		sql.append("order by t1.kp_date_id");
		
//		sql.append("union all");
//		sql.append("select t1.* from yq_finance_bx_invoice t2,yq_flow_apply t3 where 1=1 and t2.business_id = t3.apply_id");
//		sql.appendIn(new int[]{1,2},"t3.apply_state");
		
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "myBxInvoiceList",type = Types.TEMPLATE)
	public JSONObject myBxInvoiceList() {
		EasySQL sql = new EasySQL("select t1.* from yq_finance_bx_invoice t1 where 1=1");
		sql.append(getUserId(),"and t1.creator = ?");
		sql.append(param.getString("bxState"),"and t1.bx_state = ?");
		this.setBxInvoiceCondition(sql);
		sql.append("order by t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "bxInvoiceList",type = Types.TEMPLATE)
	public JSONObject bxInvoiceList() {
		EasySQL sql = new EasySQL("select t1.* from yq_finance_bx_invoice t1 where 1=1");
		sql.append(param.getString("bxUserId"),"and t1.creator = ?");
		sql.append(param.getString("bxState"),"and t1.bx_state = ?");
		this.setBxInvoiceCondition(sql);
		sql.append("ORDER BY t1.create_time desc");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "bxInvoiceListById",type = Types.TEMPLATE)
	public JSONObject bxInvoiceListById() {
		String invoiceIdArray = param.getString("invoiceIdArray");
		if(StringUtils.isBlank(invoiceIdArray)) {
			return emptyPage();
		}
		EasySQL sql = new EasySQL("select t1.* from yq_finance_bx_invoice t1 where 1=1");
		sql.appendIn(invoiceIdArray.split(","), "and t1.invoice_id");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "bxApplyInvoiceList",type = Types.TEMPLATE)
	public JSONObject bxApplyInvoiceList() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return emptyPage();
		}
		EasySQL sql = new EasySQL("select t1.*,'true' LAY_CHECKED from yq_finance_bx_invoice t1 where 1=1");
		sql.append(businessId, "and t1.business_id = ?");
		return queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name = "bxApplyInvoiceCount",type = Types.TEMPLATE)
	public JSONObject bxApplyInvoiceCount() {
		String businessId = param.getString("businessId");
		if(StringUtils.isBlank(businessId)) {
			return emptyPage();
		}
		EasySQL sql = new EasySQL("select count(*) from yq_finance_bx_invoice t1 where 1=1");
		sql.append(businessId, "and t1.business_id = ?");
		int num = 0;
		try {
			num = this.getQuery().queryForInt(sql.getSQL(), sql.getParams());
		} catch (SQLException e) {
			this.error(e.getMessage(), e);
		}
		return getJsonResult(num);
	}
	
	@WebControl(name = "bxInvoiceInfo",type = Types.RECORD)
	public JSONObject bxInvoiceInfo() {
		String invoiceId = param.getString("invoiceId");
		if(StringUtils.isBlank(invoiceId)) {
			return getJsonResult(new JSONObject());
		}
		EasySQL sql = new EasySQL("select t1.* from yq_finance_bx_invoice t1 where 1=1");
		sql.append(invoiceId,"and t1.invoice_id = ?");
		return queryForRecord(sql.getSQL(), sql.getParams());
	}
}
