package com.yunqu.work.model;

import java.io.Serializable;

public class ApproveNodeModel  implements Serializable{
	private static final long serialVersionUID = 1L;

	public ApproveNodeModel() {
		
	}
	
	public ApproveNodeModel(String nodeId,String nodeName,String checkBy,String checkName) {
		setCheckBy(checkBy);
		setNodeId(nodeId);
		setNodeName(nodeName);
		setNodeCode(nodeId);
		setCheckName(checkName);
	}

	private int checkIndex = 0;
	
	private String nodeId;
	
	private String prevNodeId;
	
	private String nextNodeId;
	
	private String nodeName;
	
	private String checkBy;
	
	private Integer checkType;
	
	private String checkName;
	
	private String checkRoleId;
	
	private String flowCode;
	
	private String nodeCode;
	
	private String ccIds;
	
	private String ccNames;
	
	private int hourLimit = 0;
	
	private int ccFlag;
	
	private int returnRule;
	
	private int signFlag;
	
	private String opinionTitle;
	
	private String editField;
	
	private String approveCode;
	
	private String submitCode;
	

	public String getNodeId() {
		return nodeId;
	}

	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}

	public String getPrevNodeId() {
		return prevNodeId;
	}

	public void setPrevNodeId(String prevNodeId) {
		this.prevNodeId = prevNodeId;
	}

	public String getNextNodeId() {
		return nextNodeId;
	}

	public void setNextNodeId(String nextNodeId) {
		this.nextNodeId = nextNodeId;
	}

	public String getNodeName() {
		return nodeName;
	}

	public void setNodeName(String nodeName) {
		this.nodeName = nodeName;
	}

	public String getCheckBy() {
		return checkBy;
	}

	public void setCheckBy(String checkBy) {
		this.checkBy = checkBy;
	}

	public String getCheckName() {
		return checkName;
	}

	public void setCheckName(String checkName) {
		this.checkName = checkName;
	}

	public String getFlowCode() {
		return flowCode;
	}

	public void setFlowCode(String flowCode) {
		this.flowCode = flowCode;
	}

	public Integer getCheckType() {
		return checkType;
	}

	public void setCheckType(Integer checkType) {
		this.checkType = checkType;
	}

	public String getCheckRoleId() {
		return checkRoleId;
	}

	public void setCheckRoleId(String checkRoleId) {
		this.checkRoleId = checkRoleId;
	}

	public String getNodeCode() {
		return nodeCode;
	}

	public void setNodeCode(String nodeCode) {
		this.nodeCode = nodeCode;
	}

	public String getCcIds() {
		return ccIds;
	}

	public void setCcIds(String ccIds) {
		this.ccIds = ccIds;
	}

	public String getCcNames() {
		return ccNames;
	}

	public void setCcNames(String ccNames) {
		this.ccNames = ccNames;
	}

	public int getHourLimit() {
		return hourLimit;
	}

	public void setHourLimit(int hourLimit) {
		this.hourLimit = hourLimit;
	}

	public int getCcFlag() {
		return ccFlag;
	}

	public void setCcFlag(int ccFlag) {
		this.ccFlag = ccFlag;
	}

	public int getReturnRule() {
		return returnRule;
	}

	public void setReturnRule(int returnRule) {
		this.returnRule = returnRule;
	}

	/**
	 * 会签 1开始 0 关闭
	 * @return
	 */
	public int getSignFlag() {
		return signFlag;
	}

	public void setSignFlag(int signFlag) {
		this.signFlag = signFlag;
	}

	public String getOpinionTitle() {
		return opinionTitle;
	}

	public void setOpinionTitle(String opinionTitle) {
		this.opinionTitle = opinionTitle;
	}

	public String getEditField() {
		return editField;
	}

	public void setEditField(String editField) {
		this.editField = editField;
	}

	public String getApproveCode() {
		return approveCode;
	}

	public void setApproveCode(String approveCode) {
		this.approveCode = approveCode;
	}

	public String getSubmitCode() {
		return submitCode;
	}

	public void setSubmitCode(String submitCode) {
		this.submitCode = submitCode;
	}


	public void setCheckIndex(int checkIndex){
		this.checkIndex = checkIndex;
	}
	public int getCheckIndex(){
		return checkIndex;
	}
	

}
